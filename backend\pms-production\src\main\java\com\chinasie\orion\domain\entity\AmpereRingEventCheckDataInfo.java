package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 22 日
 * 安质环检查事件数据表
 **/
@Data
@TableName(value = "pms_amperering_event_check_data_info")
@ApiModel(value = "AmpereRingEventCheckDataInfo 对象",description = "安质环检查事件数据表")
public class AmpereRingEventCheckDataInfo extends ObjectEntity implements Serializable {
    /**
     * 检查问题编号
     */
    @ApiModelProperty(value = "检查问题编号")
    @TableField(value = "check_number")
    private String checkNumber;

    /**
     * 检查主题
     */
    @ApiModelProperty(value = "检查主题")
    @TableField(value = "check_subject")
    private String checkSubject;

    /**
     * 事件等级
     */
    @ApiModelProperty(value = "事件等级")
    @TableField(value = "event_level")
    private String eventLevel;

    /**
     * 事件类型
     */
    @ApiModelProperty(value = "事件类型")
    @TableField(value = "event_type")
    private String eventType;

    /**
     * 事件地点
     */
    @ApiModelProperty(value = "事件地点")
    @TableField(value = "event_address")
    private String eventAddress;

    /**
     * 事件地点code
     */
    @ApiModelProperty(value = "事件地点code")
    @TableField(value = "event_address_code")
    private String eventAddressCode;

    /**
     * 事件发生时间
     */
    @ApiModelProperty(value = "事件发生的时间")
    @TableField(value = "event_date")
    private Date eventDate;

    /**
     * 监察人
     */
    @ApiModelProperty(value = "监察人")
    @TableField(value = "check_person")
    private String checkPerson;

    /**
     * 监察人所在部门
     */
    @ApiModelProperty(value = "监察人所在部门")
    @TableField(value = "check_person_dept")
    private String checkPersonDept;

    /**
     * 是否监督发现
     */
    @ApiModelProperty(value = "是否监督发现")
    @TableField(value = "is_find")
    private Boolean isFind;

    /**
     * 时间描述
     */
    @ApiModelProperty(value = "时间描述")
    @TableField(value = "event_desc")
    private String eventDesc;

    /**
     * 直接负责人
     */
    @TableField(value = "person_in_charge")
    @ApiModelProperty(value = "直接负责人")
    private String personInCharge;

    /**
     * 直接部门
     */
    @TableField(value = "zr_dept_code")
    @ApiModelProperty(value = "直接部门")
    private String zrDeptCode;

    /**
     * 直接归口部门
     */
    @ApiModelProperty(value = "直接归口部门")
    @TableField(value = "gk_dept_code")
    private String gkDeptCode;

    /**
     * 直接责任部门
     */
    @ApiModelProperty(value = "直接责任部门")
    @TableField(value = "zrdept")
    private String zrdept;

    /**
     * 直接归口部门
     */
    @ApiModelProperty(value = "直接归口部门")
    @TableField(value = "gkdept")
    private String gkdept;

    /**
     * 流程状态
     */
    @ApiModelProperty(value = "流程状态")
    @TableField(value = "current_process")
    private String currentProcess;
}
