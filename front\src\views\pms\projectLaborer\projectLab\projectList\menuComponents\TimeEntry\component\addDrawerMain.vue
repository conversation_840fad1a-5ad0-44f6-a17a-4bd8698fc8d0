<template>
  <div class="viewDrawerMain">
    <DetailsLayout
      title="工时填报"
      show-table-header
    >
      <div style="height:200px;overflow:hidden;">
        <OrionTable
          ref="tableRefWeekEndRef"
          :options="weekEndTableOption"
        >
          <template #toolbarLeft>
            <div class="flex flex-ac">
              <BasicButton
                type="primary"
                @click="goWeek(-1)"
              >
                上周
              </BasicButton>
              <BasicButton
                @click="goWeek(1)"
              >
                下周
              </BasicButton>
            </div>
          </template>
          <template #type>
            <div>项目工时</div>
          </template>
          <template #name>
            <div>{{ state.basicInfo.name }}</div>
          </template>
        </OrionTable>
      </div>
    </DetailsLayout>
    <DetailsLayout
      :title="state.selectDay+'填报'"
      show-table-header
    >
      <OrionTable
        ref="tableRefCurrentDay"
        :options="currentDayTableOption"
      >
        <template #toolbarLeft>
          <BasicButton
            type="primary"
            icon="sie-icon-tianjiaxinzeng"
            @click="handleAdd"
          >
            添加一行
          </BasicButton>
          <BasicButton
            icon="sie-icon-del"
            @click="multiDelete"
          >
            移除
          </BasicButton>
        </template>
        <template #bodyCell="{text,index,record,column}">
          <div
            v-if="column.type==='form'"
          >
            <FormItemRest>
              <Input
                v-if="column.component==='Input'"
                v-model:value="record[column.dataIndex]"
                :disabled="column.disabled"
                :placeholder="column.placeholder||''"
                @blur="onInputBlur(column.dataIndex)"
              />
              <InputNumber
                v-if="column.component==='InputNumber'"
                v-model:value="record[column.dataIndex]"
                :disabled="column.disabled"
                :placeholder="column.placeholder||''"
                @blur="onInputBlur(column.dataIndex)"
              />
              <InputSearch
                v-if="column.component==='TreeModal'"
                v-model:value="record[column.dataIndex]"
                readonly="readonly"
                @search="showTreeSelectModal(column.dataIndex,index)"
              />
            </FormItemRest>
          </div>
          <div v-else-if="column.flag">
            {{ index + 1 }}
          </div>
          <div v-else>
            {{ text }}
          </div>
        </template>
      </OrionTable>
    </DetailsLayout>
  </div>
</template>

<script lang="ts" setup>
import {
  h, inject, onMounted, reactive, Ref, ref,
} from 'vue';
import { BasicButton, openTreeSelectModal, OrionTable } from 'lyra-component-vue3';
import {
  FormItemRest, Input, InputNumber, message, Modal, InputSearch,
} from 'ant-design-vue';
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import dayjs from 'dayjs';

const currenDay = ref('Monday');
const state = reactive({

  currentWeekIndex: 0,
  showWeekTable: false,
  currenDayIndex: null,
  dataindex: null,
  basicInfo: inject('basicInfo'),
  dayDetailList: [
    {
      detaiList: [],
      workDate: '',
      workHour: 0,
      type: 'Monday',
    },
    {
      detaiList: [],
      workDate: '',
      workHour: 0,
      type: 'Tuesday',
    },
    {
      detaiList: [],
      workDate: '',
      workHour: 0,
      type: 'Wednesday',
    },
    {
      detaiList: [],
      workDate: '',
      workHour: 0,
      type: 'Thursday',
    },
    {
      detaiList: [],
      workDate: '',
      workHour: 0,
      type: 'Friday',
    },
    {
      detaiList: [],
      workDate: '',
      workHour: 0,
      type: 'Sunday',
    },
    {
      detaiList: [],
      workDate: '',
      workHour: 0,
      type: 'weekday',
    },
  ],
  currentMonday: '',
  currentWeekday: '',
  selectDay: '',
});

const weekEndColumns = ref([
  {
    title: '工时类型',
    dataIndex: 'type',
    align: 'left',
    width: 90,
    slots: { customRender: 'type' },
  },
  {
    title: '条目',
    dataIndex: 'name',
    align: 'left',
    width: 100,
    slots: { customRender: 'name' },
  },
  {
    title: `${state.dayDetailList[0].workDate}周一`,
    align: 'left',
    dataIndex: 'Monday',
    customRender({
      record, index, column, text,
    }) {
      return h('span', {
        title: text,
        // class: 'flex flex-te',
      }, [
        h('div', {
          class: 'inputCircle',
          placeholder: '点击录入',
          onClick(e:Event) {
            handleOprate(column);
          },
        }, text),
      ]);
    },
  },
  {
    title: `${state.dayDetailList[1].workDate}周二`,
    align: 'left',
    dataIndex: 'Tuesday',
    customRender({
      record, index, column, text,
    }) {
      return h('span', {
        title: text,
        // class: 'flex flex-te',
      }, [
        h('div', {
          class: 'inputCircle',
          placeholder: '点击录入',
          onClick(e:Event) {
            handleOprate(column);
          },
        }, text),
      ]);
    },
  },

  {
    title: `${state.dayDetailList[2].workDate}周三`,
    align: 'left',
    dataIndex: 'Wednesday',
    customRender({
      record, index, column, text,
    }) {
      return h('span', {
        title: text,
        // class: 'flex flex-te',
      }, [
        h('div', {
          class: 'inputCircle',
          placeholder: '点击录入',
          onClick(e:Event) {
            handleOprate(column);
          },
        }, text),
      ]);
    },
  },
  {
    title: `${state.dayDetailList[3].workDate}周四`,
    align: 'left',
    dataIndex: 'Thursday',
    customRender({
      record, index, column, text,
    }) {
      return h('span', {
        title: text,
        // class: 'flex flex-te',
      }, [
        h('div', {
          class: 'inputCircle',
          placeholder: '点击录入',
          onClick(e:Event) {
            handleOprate(column);
          },
        }, text),
      ]);
    },
  },
  {
    title: `${state.dayDetailList[4].workDate}周五`,
    dataIndex: 'Friday',
    align: 'left',
    customRender({
      record, index, column, text,
    }) {
      return h('span', {
        title: text,
        // class: 'flex flex-te',
      }, [
        h('div', {
          class: 'inputCircle',
          placeholder: '点击录入',
          onClick(e:Event) {
            handleOprate(column);
          },
        }, text),
      ]);
    },
  },
  {
    title: `${state.dayDetailList[5].workDate}周六`,
    align: 'left',
    dataIndex: 'Sunday',
    customRender({
      record, index, column, text,
    }) {
      return h('span', {
        title: text,
        // class: 'flex flex-te',
      }, [
        h('div', {
          class: 'inputCircle',
          placeholder: '点击录入',
          onClick(e:Event) {
            handleOprate(column);
          },
        }, text),
      ]);
    },
  },
  {
    title: `${state.dayDetailList[6].workDate}周天`,
    dataIndex: 'weekday',
    align: 'left',
    customRender({
      record, index, column, text,
    }) {
      return h('span', {
        title: text,
        // class: 'flex flex-te',
      }, [
        h('div', {
          class: 'inputCircle',
          placeholder: '点击录入',
          onClick(e:Event) {
            handleOprate(column);
          },
        }, text),
      ]);
    },
  },
]);
onMounted(() => {
  for (let i = 0; i < 7; i++) {
    for (let j in state.dayDetailList) {
      if (i === Number(j)) {
        state.dayDetailList[j].workDate = dayjs().startOf('week').add(i, 'day').format('YYYY-MM-DD');
      }
    }
  }
  state.currentMonday = dayjs().startOf('week').add(0, 'day').format('YYYY-MM-DD');
  state.currentWeekday = dayjs().startOf('week').add(6, 'day').format('YYYY-MM-DD');
  state.selectDay = `${state.dayDetailList[0].workDate}周一`;
  setWeekendColumns();
});

function setWeekendColumns() {
  weekEndColumns.value[2].title = `${state.dayDetailList[0].workDate}周一`;
  weekEndColumns.value[3].title = `${state.dayDetailList[1].workDate}周二`;
  weekEndColumns.value[4].title = `${state.dayDetailList[2].workDate}周三`;
  weekEndColumns.value[5].title = `${state.dayDetailList[3].workDate}周四`;
  weekEndColumns.value[6].title = `${state.dayDetailList[4].workDate}周五`;
  weekEndColumns.value[7].title = `${state.dayDetailList[5].workDate}周六`;
  weekEndColumns.value[8].title = `${state.dayDetailList[6].workDate}周天`;
  tableRefWeekEndRef.value.setColumns(weekEndColumns.value);
}

const weekEndTableOption = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: false,
  smallSearchField: false,
  showSmallSearch: false,
  pagination: false,
  dataSource: [
    {
      Monday: null,
      Tuesday: null,
      Wednesday: null,
      Thursday: null,
      Friday: null,
      Sunday: null,
      weekday: null,
    },
  ],
  showIndexColumn: false,
  columns: weekEndColumns.value,
});
const obj = () => ({
  dates: new Date().getTime().toString(),
  workHour: '',
  projectPlace: '',
  relateObject: '',
  relateName: '',
  taskContent: '',
  workHourAmt: null,
});
const dataSource: Ref<any[]> = ref([obj()]);
const dataSourceAll = ref({
  Monday: [obj()],
  Tuesday: [obj()],
  Wednesday: [obj()],
  Thursday: [obj()],
  Friday: [obj()],
  Sunday: [obj()],
  weekday: [obj()],
});

const handleOprate = (column) => {
  state.selectDay = column.title;
  currenDay.value = column.dataIndex;
  tableRefCurrentDay.value.setColumns(columns);
  tableRefCurrentDay.value.setTableData(dataSourceAll.value[currenDay.value]);
};

const columns: Ref<any[]> = ref([
  {
    title: '任务工时',
    dataIndex: 'workHour',
    type: 'form',
    component: 'InputNumber',
  },
  {
    title: '项目地点',
    dataIndex: 'projectPlace',
    width: 200,
    required: true,
    type: 'form',
    component: 'Input',
    suffix: true,
  },
  {
    title: '关联对象',
    dataIndex: 'relateName',
    type: 'form',
    component: 'TreeModal',
    // required: true,
  },
  {
    title: '任务内容',
    dataIndex: 'taskContent',
    type: 'form',
    component: 'Input',
    placeholder: '',
  },
]);

const tableRefCurrentDay = ref(null);
const tableRefWeekEndRef = ref(null);
const showTable = ref(false);

const tabsIndex = ref(0);
const tabsArr = ref([
  {
    name: '上周',
    id: 1,
  },
  {
    name: '下周',
    id: 2,
  },
]);

const onInputBlur = (dataIndex: string) => {
  const keys = [
    'workHour',
    'projectPlace',
    'relateName',
    'taskContent',
  ];
  if (dataIndex === 'workHour') {
    if (keys.includes('workHour')) {
      autoCalculation(dataIndex);
    }
  }
};

const currentDayTableOption = reactive({
  rowSelection: {},
  showToolButton: false,
  showTableSetting: false,
  showSmallSearch: false,
  pagination: false,
  deleteToolButton: 'add|enable|disable|delete',
  dataSource: dataSourceAll.value[currenDay.value],
  rowKey: 'dates',
  columns,
});
function autoCalculation(dataIndex) {
  let datas = dataSourceAll.value[currenDay.value].map((item) => item);
  const arr = datas.map((item) => Number(item.workHour)).reduce((prev, next) => prev + next, 0);
  weekEndTableOption.value.dataSource[0][currenDay.value] = arr;
  tableRefWeekEndRef.value.setTableData(weekEndTableOption.value.dataSource);
  tableRefCurrentDay.value.setTableData(dataSourceAll.value[currenDay.value]);
}
// 添加一行
const handleAdd = () => {
  dataSourceAll.value[currenDay.value].push(obj());
};

function showTreeSelectModal(dataindex, index) {
  state.currenDayIndex = index;
  state.dataindex = dataindex;
  openTreeSelectModal({
    onOk: selectOk,
  });
}
// 删除
const selectKeys: Ref<string[]> = ref([]);
const multiDelete = () => {
  if (tableRefCurrentDay.value.getSelectRowKeys().length === dataSourceAll.value[currenDay.value].length) {
    return message.error('至少保留一条明细');
  }
  Modal.confirm({
    title: '移除提示',
    content: '确认移除所选择的数据项？',
    onOk() {
      dataSourceAll.value[currenDay.value] = dataSourceAll.value[currenDay.value].filter((item) =>
        !tableRefCurrentDay.value.getSelectRowKeys().includes(item.dates));
      autoCalculation('workHour');
    },
  });
};
const selectOk = (e) => {
  dataSourceAll.value[currenDay.value][state.currenDayIndex][state.dataindex] = e.associatedData.innerName;
  dataSourceAll.value[currenDay.value][state.currenDayIndex].relateObject = e.associatedData.id;
  tableRefCurrentDay.value.reload();
};

const goWeek = (e) => {
  if (e === 1) {
    state.currentWeekIndex++;
  } else {
    state.currentWeekIndex--;
  }
  state.currentWeekIndex = e === 1 ? state.currentWeekIndex++ : state.currentWeekIndex--;
  let currentDate = '';
  currentDate = dayjs(dayjs().add(7 * state.currentWeekIndex, 'day').valueOf()).format('YYYY-MM-DD');
  for (let i = 0; i < 7; i++) {
    for (let j in state.dayDetailList) {
      if (i === Number(j)) {
        state.dayDetailList[j].workDate = dayjs(currentDate).startOf('week').add(i, 'day').format('YYYY-MM-DD');
      }
    }
  }
  state.currentMonday = dayjs(currentDate).startOf('week').add(0, 'day').format('YYYY-MM-DD');
  state.currentWeekday = dayjs(currentDate).startOf('week').add(6, 'day').format('YYYY-MM-DD');
  dataSourceAll.value = {
    Monday: [obj()],
    Tuesday: [obj()],
    Wednesday: [obj()],
    Thursday: [obj()],
    Friday: [obj()],
    Sunday: [obj()],
    weekday: [obj()],
  };
  tableRefWeekEndRef.value.setTableData([
    {
      Monday: null,
      Tuesday: null,
      Wednesday: null,
      Thursday: null,
      Friday: null,
      Sunday: null,
      weekday: null,
    },
  ]);
  tableRefCurrentDay.value.setTableData(dataSourceAll.value[currenDay.value]);
  state.selectDay = `${state.dayDetailList[0].workDate}周一`;
  setWeekendColumns();
};

defineExpose({
  dataSourceAll: () => dataSourceAll.value,
  getTableData: () => state.dayDetailList,
});

</script>
<style lang="less">
.viewDrawerMain {
  height:100%;
}
.inputCircle {
  width:80px;
  height:26px;
  text-align: left;
  font-size:14px;
  cursor: pointer;
  border: 1px solid #d9d9d9;
  color: rgba(0,0,0,.25);
}
.inputCircle:empty::before {
  content:attr(placeholder)
}
</style>
