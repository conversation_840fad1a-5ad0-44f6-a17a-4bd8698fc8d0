package org.jeecg.modules.demo.dkm.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import java.io.File;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;

/**
 * 文件路径工具类
 * 
 * <AUTHOR>
 * @date 2025-5-19
 */
@Component
@Slf4j
public class FilePathUtil {
    
    // 读取配置文件中的下载服务器IP、端口和路径
    private static String downloadServerIp;
    private static String downloadServerPort;
    private static String downloadServerPath;
    private static String serverStoragePath;
    
    @Value("${mpf.download-server-ip}")
    public void setDownloadServerIp(String ip) {
        FilePathUtil.downloadServerIp = ip;
    }
    @Value("${mpf.download-server-port}")
    public void setDownloadServerPort(String port) {
        FilePathUtil.downloadServerPort = port;
    }
    @Value("${mpf.download-server-path}")
    public void setDownloadServerPath(String path) {
        FilePathUtil.downloadServerPath = path;
    }
    @Value("${mpf.server-storage-path:/home/<USER>")
    public void setServerStoragePath(String path) {
        FilePathUtil.serverStoragePath = path;
    }

    /**
     * 获取服务器上文件的存储路径
     * Windows: D:\home\download
     * Linux: /home/<USER>
     * 
     * @return 服务器存储路径
     */
    public static String getServerStoragePath() {
        String storagePath;
        String osName = System.getProperty("os.name").toLowerCase();
        if (osName.contains("win")) {
            // Windows系统路径
            storagePath = serverStoragePath;
        } else {
            // Linux系统路径
            storagePath = serverStoragePath;
        }
        // log打印存储路径
        log.info("服务器存储路径: {}", storagePath);
        
        // 创建存储目录
        File folder = new File(storagePath);
        if (!folder.exists()) {
            folder.mkdirs();
        }

        // 遍历目录中的文件，如果在今天日期之前，则删除
        deleteExpiredFiles(folder);

        return storagePath;
    }
    
    /**
     * 删除指定目录中的过期文件（修改时间在今天之前的文件）
     * 
     * @param folder 要清理的目录
     */
    private static void deleteExpiredFiles(File folder) {
        File[] files = folder.listFiles();
        if (files != null) {
            LocalDate today = LocalDate.now();
            for (File file : files) {
                if (file.isFile()) {
                    long lastModified = file.lastModified();
                    LocalDate fileDate = Instant.ofEpochMilli(lastModified)
                            .atZone(ZoneId.systemDefault())
                            .toLocalDate();
                    if (fileDate.isBefore(today)) {
                        if (file.delete()) {
                            log.info("删除过期文件: {}", file.getName());
                        } else {
                            log.warn("删除文件失败: {}", file.getName());
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 获取客户端下载文件的URL路径
     * 
     * @return 下载URL路径
     */
    public static String getDownloadUrl() {
        String downloadUrl;
        String osName = System.getProperty("os.name").toLowerCase();
        if (osName.contains("win")) {
            // Windows系统为本地文件路径
            downloadUrl = downloadServerPath;
        } else {
            // Linux系统拼接HTTP URL
            downloadUrl = "http://" + downloadServerIp + ":" + downloadServerPort + downloadServerPath;
        }
        // log打印下载URL
        log.info("下载URL路径: {}", downloadUrl);
        
        return downloadUrl;
    }
    
    /**
     * 获取下载文件夹路径（兼容旧代码，建议使用新方法）
     * @deprecated 建议使用 getServerStoragePath() 和 getDownloadUrl() 方法
     * @return 下载文件夹路径
     */
    public static String getDownloadFolder() {
        return getServerStoragePath();
    }
} 