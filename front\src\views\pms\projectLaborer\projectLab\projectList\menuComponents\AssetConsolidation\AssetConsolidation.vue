<script setup lang="ts">
import {
  computed,
  h, inject, onMounted, ref, watchEffect,
} from 'vue';
import dayjs from 'dayjs';
import { get, map, random } from 'lodash-es';
import Api from '/@/api';
import {
  Layout, OrionTable, randomString, BasicButton, DataStatusTag, openDrawer, isPower,
} from 'lyra-component-vue3';
import { useRoute, useRouter } from 'vue-router';
import { Space, Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { useBusinessWorkflow } from 'lyra-workflow-component-vue3';
import { useCreatedOrEditableForm } from './hooks/useCreatedOrEditableForm';

const router = useRouter();
const route = useRoute();
const tableRef = ref();

const flowRef = useBusinessWorkflow({
  Api,
  businessData: {},
  openDrawer,
  afterEvent() {
    updateTable();
  },
});
const flowStartLoading = ref(false);

const parentData = inject('formData');
const powerData = ref([]);
const selectKeys = ref([]);
const dataSource = ref([]);
const tableOptions = {
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  ...parseRowSelection({
    rowSelection: {
      onChange(val) {
        selectKeys.value = val;
      },
    },
  }),
  smallSearchField: ['number', 'name'],
  filterConfig: {
    fields: [
      {
        field: 't.contract_name',
        fieldName: '目标年度',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 't.contract_number',
        fieldName: '目标类型',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
    ],
  },
  columns: [
    {
      title: '申请单号',
      dataIndex: 'number',
      width: 160,
      fixed: 'left',
    },
    {
      title: '申请名称',
      dataIndex: 'name',
      width: 400,
      minWidth: 400,
      customCell({ text, record }) {
        return {
          style: {
            color: '#5172dc',
            cursor: 'pointer',
          },
          onClick: () => {
            router.push({
              name: 'AssetsFixedDetail',
              params: {
                id: record.id,
              },
              query: {
                query: randomString(),
              },
            });
          },
        };
      },
    },
    {
      title: '申请人',
      dataIndex: 'resPersonName',
      width: 100,
    },
    {
      title: '申请时间',
      dataIndex: 'resTime',
      width: 100,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '状态',
      dataIndex: 'dataStatus',
      width: 90,
      customRender: ({ record, text }) => h(DataStatusTag, { statusData: record.dataStatus }),
    },
    {
      title: '申请说明',
      dataIndex: 'resDescribe',
      width: 200,
    },
    {
      title: '转固时间',
      dataIndex: 'finishTime',
      width: 100,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    ...parseColAction({
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 200,
      fixed: 'right',
    }),
  ],
  api: (params: Record<string, any>) => new Api('/pms/projectAssetApply/page').fetch({
    ...params,
    query: {
      projectId: get(parentData.value, 'id'),
    },
    power: {
      pageCode: 'PMS0004',
      containerCode: 'PMS_XMXQ_container_08_03',
    },
  }, '', 'POST'),
  actions: [
    {
      text: '编辑',
      isShow: (record) => [
        get(parentData.value, 'status') !== 130,
        isPower(
          'PMS_XMXQ_container_08_03_button_03',
          get(record, 'rdAuthList'),
        ),
        [101, 130].includes(record.status),
      ].every(Boolean),
      onClick(record) {
        useCreatedOrEditableForm({
          title: '编辑申请单',
          ...record,
          operateType: 'editable',
        }, updateTable);
      },
    },
    {
      text: '删除',
      isShow: (record) => [
        get(parentData.value, 'status') !== 130,
        isPower(
          'PMS_XMXQ_container_08_03_button_04',
          get(record, 'rdAuthList'),
        ),
        [101].includes(record.status),
      ].every(Boolean),
      onClick(record) {
        Modal.confirm({
          title: '删除警告',
          icon: h(ExclamationCircleOutlined),
          content: '确定删除这条数据',
          async onOk() {
            try {
              const targetKeys = [record.id];
              return await deleteRequest(targetKeys);
            } catch {
            }
          },
          onCancel() {},
        });
      },
    },
    {
      text: '提交申请',
      isShow: (record) => [
        get(parentData.value, 'status') !== 130,
        isPower(
          'PMS_XMXQ_container_08_03_button_05',
          get(record, 'rdAuthList'),
        ),
        [101].includes(record.status),
      ].every(Boolean),
      onClick(record) {
        flowRef.setProps({
          businessData: record,
        });
        flowRef?.onAddTemplate({
          messageUrl: `/pms/assetsFixedDetail/${record.id}?query=${randomString(6)}`,
        }, 'start', flowStartLoading);
      },
    },
  ],
};

const showCreateBtn = computed(() => [get(parentData.value, 'status') !== 130, isPower('PMS_XMXQ_container_08_03_button_01', powerData.value)].every(Boolean));
const showRemoveBtn = computed(() => [get(parentData.value, 'status') !== 130, isPower('PMS_XMXQ_container_08_03_button_02', powerData.value)].every(Boolean));

function updateTable() {
  tableRef.value?.reload?.();
}

function parseColAction(cfg) {
  if (get(parentData.value, 'status') !== 130) {
    return [cfg];
  }
  return [];
}
function parseRowSelection(cfg) {
  if (get(parentData.value, 'status') !== 130) {
    return cfg;
  }
  return {};
}
function getPowerDataHandle(data) {
  powerData.value = data;
}
function handleCreateApplyForm() {
  useCreatedOrEditableForm({
    title: '创建申请单',
    projectId: get(parentData.value, 'id'),
    projectName: get(parentData.value, 'name'),
    operateType: 'create',
  }, updateTable);
}
function handleDeleteRows() {
  Modal.confirm({
    title: '删除警告',
    icon: h(ExclamationCircleOutlined),
    content: '确定删除选中的数据',
    async onOk() {
      try {
        const targetKeys = tableRef.value?.getSelectRowKeys();
        return await deleteRequest(targetKeys);
      } catch {
      }
    },
    onCancel() {},
  });
}
async function deleteRequest(idxs) {
  return new Promise((resolve) => {
    new Api('/pms/projectAssetApply/remove')
      .fetch(idxs, '', 'DELETE')
      .then((res) => {
        updateTable();
        resolve({});
      });
  });
}
</script>

<template>
  <Layout
    v-get-power="{pageCode: 'PMS0004',getPowerDataHandle}"
    v-loading="flowStartLoading"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      class="scroll-table"
      :dataSource="dataSource"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <Space
          :size="12"
          align="center"
        >
          <BasicButton
            v-if="showCreateBtn"
            type="primary"
            icon="sie-icon-tianjiaxinzeng"
            @click="handleCreateApplyForm"
          >
            创建申请单
          </BasicButton>
          <BasicButton
            v-if="showRemoveBtn"
            :disabled="!selectKeys.length"
            icon="sie-icon-shanchu"
            @click="handleDeleteRows"
          >
            删除
          </BasicButton>
        </Space>
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">

</style>