package com.chinasie.orion.management.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * NcfFormPurchOrderCollect Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-21 14:55:52
 */
@TableName(value = "pms_ncf_form_purch_order_collect")
@ApiModel(value = "NcfFormPurchOrderCollectEntity对象", description = "采购-商城集采订单（总表）")
@Data

public class NcfFormPurchOrderCollect extends ObjectEntity implements Serializable {

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    @TableField(value = "order_number")
    private String orderNumber;

    /**
     * PO订单号
     */
    @ApiModelProperty(value = "PO订单号")
    @TableField(value = "po_order_number")
    private String poOrderNumber;

    /**
     * 电商渠道订单号
     */
    @ApiModelProperty(value = "电商渠道订单号")
    @TableField(value = "commerce_channel_order_number")
    private String commerceChannelOrderNumber;

    /**
     * 企业名称
     */
    @ApiModelProperty(value = "企业名称")
    @TableField(value = "enterprise_name")
    private String enterpriseName;

    /**
     * PR公司名称
     */
    @ApiModelProperty(value = "PR公司名称")
    @TableField(value = "pr_company_name")
    private String prCompanyName;

    /**
     * 部门
     */
    @ApiModelProperty(value = "部门")
    @TableField(value = "department")
    private String department;

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @TableField(value = "contract_id")
    private String contractId;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @TableField(value = "contract_name")
    private String contractName;

    /**
     * 下单人
     */
    @ApiModelProperty(value = "下单人")
    @TableField(value = "order_placer")
    private String orderPlacer;

    /**
     * 下单人电话
     */
    @ApiModelProperty(value = "下单人电话")
    @TableField(value = "order_phone_number")
    private String orderPhoneNumber;

    /**
     * 下单时间
     */
    @ApiModelProperty(value = "下单时间")
    @TableField(value = "order_time")
    private Date orderTime;

    /**
     * 对账人
     */
    @ApiModelProperty(value = "对账人")
    @TableField(value = "reconciler")
    private String reconciler;

    /**
     * 收货负责人
     */
    @ApiModelProperty(value = "收货负责人")
    @TableField(value = "consignee")
    private String consignee;

    /**
     * 收货审核人
     */
    @ApiModelProperty(value = "收货审核人")
    @TableField(value = "receipt_reviewer")
    private String receiptReviewer;

    /**
     * 支付负责人
     */
    @ApiModelProperty(value = "支付负责人")
    @TableField(value = "payment_manager")
    private String paymentManager;

    /**
     * 验收方式
     */
    @ApiModelProperty(value = "验收方式")
    @TableField(value = "acceptance_method")
    private String acceptanceMethod;

    /**
     * 结算方式
     */
    @ApiModelProperty(value = "结算方式")
    @TableField(value = "settlement_method")
    private String settlementMethod;

    /**
     * 要求到货日期
     */
    @ApiModelProperty(value = "要求到货日期")
    @TableField(value = "request_delivery_date")
    private Date requestDeliveryDate;

    /**
     * 订单总金额
     */
    @ApiModelProperty(value = "订单总金额")
    @TableField(value = "total_order_amount")
    private BigDecimal totalOrderAmount;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 订单待支付
     */
    @ApiModelProperty(value = "订单待支付")
    @TableField(value = "order_pay_day")
    private String orderPayDay;

    /**
     * 订单确认时间
     */
    @ApiModelProperty(value = "订单确认时间")
    @TableField(value = "order_confirmation_time")
    private Date orderConfirmationTime;

    /**
     * 订单审批时间
     */
    @ApiModelProperty(value = "订单审批时间")
    @TableField(value = "order_approval_time")
    private Date orderApprovalTime;

    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间")
    @TableField(value = "paid_time")
    private Date paidTime;

    /**
     * 开票时间
     */
    @ApiModelProperty(value = "开票时间")
    @TableField(value = "invoicing_time")
    private Date invoicingTime;

    /**
     * 申请开票时间
     */
    @ApiModelProperty(value = "申请开票时间")
    @TableField(value = "application_for_invoicing_time")
    private Date applicationForInvoicingTime;

    /**
     * 对账确认时间
     */
    @ApiModelProperty(value = "对账确认时间")
    @TableField(value = "reconciliation_confirmation_time")
    private Date reconciliationConfirmationTime;

    /**
     * 对账申请时间
     */
    @ApiModelProperty(value = "对账申请时间")
    @TableField(value = "reconciliation_application_time")
    private Date reconciliationApplicationTime;

    /**
     * 发货耗时
     */
    @ApiModelProperty(value = " 发货耗时")
    @TableField(value = "used_time")
    private Integer usedTime;

    /**
     * 订单最后一次交货时间
     */
    @ApiModelProperty(value = "订单最后一次交货时间")
    @TableField(value = "time_of_delivery")
    private Date timeOfDelivery;

    /**
     * 订单最后一次确认收货时间
     */
    @ApiModelProperty(value = "订单最后一次确认收货时间")
    @TableField(value = "time_of_last_receipt")
    private Date timeOfLastReceipt;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态")
    @TableField(value = "order_state")
    private String orderState;

    /**
     * 退货金额
     */
    @ApiModelProperty(value = "退货金额")
    @TableField(value = "return_amount")
    private BigDecimal returnAmount;

}
