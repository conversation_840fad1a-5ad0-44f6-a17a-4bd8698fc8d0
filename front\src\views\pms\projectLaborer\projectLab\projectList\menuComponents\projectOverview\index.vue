<template>
  <div
    class="information"
  >
    <a-row
      class="top"
      :gutter="20"
    >
      <a-col
        :span="8"
        class="left"
      >
        <ItemInformation />
      </a-col>
      <a-col
        :span="8"
        class="center"
      >
        <ManHour />
      </a-col>
      <a-col
        :span="8"
        class="right"
      >
        <Deliverable />
      </a-col>
    </a-row>
    <div class="content">
      <Milestone />
    </div>
    <a-row class="bottom">
      <a-col
        :span="10"
        class="left"
      >
        <Complete />
      </a-col>
      <a-col :span="14">
        <WorkItem />
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { Row, Col } from 'ant-design-vue';
import ItemInformation from './components/ItemInformation.vue';
import ManHour from './components/ManHour.vue';
import Deliverable from './components/Deliverable.vue';
import Milestone from './components/Milestone.vue';
import Complete from './components/Complete.vue';
import WorkItem from './components/WorkItem.vue';
export default {
  name: 'Index',
  components: {
    WorkItem,
    Complete,
    Milestone,
    Deliverable,
    ManHour,
    ItemInformation,
    ARow: Row,
    ACol: Col,
  },
};
</script>

<style scoped lang="less">
  @borderColor: #dcdfe6;
  .top {
    .center {
      border-right: 1px solid @borderColor;
      border-left: 1px solid @borderColor;
    }
  }
  .bottom {
    .left {
      border-right: 1px solid @borderColor;
    }
  }
  .content {
    border-top: 1px solid @borderColor;
    border-bottom: 1px solid @borderColor;
  }

  .information {
    margin: 0 ~`getPrefixVar('content-margin-left')`;
  }
</style>
