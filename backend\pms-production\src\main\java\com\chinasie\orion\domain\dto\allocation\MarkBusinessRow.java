package com.chinasie.orion.domain.dto.allocation;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

@Data
public class MarkBusinessRow implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "员工号不可为空")
    @ApiModelProperty(value = "员工号",example = "P639846")
    private String staffNo;

    @ApiModelProperty(value = "业务ID(人员管理ID或物资管理ID ； 修改的时候必填，新增的时候不传)")
    private String rowId;

    @ApiModelProperty(value = "关联表ID(人员组织关联ID或物资组织关联ID ； 修改的时候必填，新增的时候不传)")
    private String relationId;

    @ApiModelProperty(value = "组织ID")
    private String orgId;

    @NotBlank(message = "大修轮次编码不可为空")
    @ApiModelProperty(value = "大修轮次编码")
    private String repairRoundCode;

    @ApiModelProperty(value = "大修轮次名称")
    private String repairRoundName;

    @NotBlank(message = "计划入场时间不可为空")
    @ApiModelProperty(value = "计划入场时间")
    private String realStartDate;

    @NotBlank(message = "计划离场时间不可为空")
    @ApiModelProperty(value = "计划离场时间")

    private String realEndDate;
    @ApiModelProperty(value = "大修基地编码")
    private String basePlaceCode;
    @ApiModelProperty(value = "大修基地名称")
    private String basePlaceName;

    @ApiModelProperty(value = "专业编码")
    private String specialtyCode;
    @ApiModelProperty(value = "专业名称")
    private String specialtyName;

    @ApiModelProperty(value = "班组编码")
    private String teamCode;
    @ApiModelProperty(value = "班组名称")
    private String teamName;

    @ApiModelProperty(value = "物资编码")
    private String number;
    @ApiModelProperty(value = "物资名称")
    private String name;

    @ApiModelProperty(value = "中心编码")
    private String costCenterCode;

}
