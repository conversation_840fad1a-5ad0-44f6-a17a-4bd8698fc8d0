INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01877311212019974144', '里程碑金额确认分配标题', 'CONFIRM_ALLOCATION_TITLE', '合同{contractName}的里程碑{milestoneName}存在收入确认异常，请处理', '1', 1, '', '314j1000000000000000000', '2025-01-09 19:07:26', '314j1000000000000000000', '2025-01-09 19:07:57', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01877311324674785280', '里程碑金额确认分配内容', 'CONFIRM_ALLOCATION_CONTENT', '<p>合同{contractName}的里程碑{milestoneName}存在收入确认异常，请处理</p>', '2', 1, '', '314j1000000000000000000', '2025-01-09 19:07:53', '314j1000000000000000000', '2025-01-09 19:07:56', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');


INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1877312368251174912', '合同里程碑收入异常', 'CONTRACT_MILESTONE_INCOME', 'vub01877311212019974144', 'vub01877311324674785280', 1, 'SYS', '1,2', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, '314j1000000000000000000', '2025-01-09 19:12:02', '314j1000000000000000000', '2025-01-09 19:12:51', NULL, NULL, NULL, 1, b'1', b'1');
