package com.chinasie.orion.domain.vo.tree;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;

@Data
public class JobPrepareEditVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "工单id")
    @NotEmpty(message = "工单ID不能为空")
    private String  jobId;
    @ApiModelProperty(value = "工单号")
    @NotEmpty(message = "工单编号不能为空")
    private String  jobNumber;
    @ApiModelProperty(value = "责任人id")
    private String  rspUserId;
    @ApiModelProperty(value = "责任人名称")
    private String  rspUserName;
    @ApiModelProperty(value = "责任人工号")
    private String  rspUserCode;
    @ApiModelProperty(value = "是否重大项目")
    private Boolean  isMajorProject;
    @ApiModelProperty(value = "计划结束时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date beginTime;
    @ApiModelProperty(value = "计划结束时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date endTime;
    @ApiModelProperty(value = "阶段")
    private String  phase;
    @ApiModelProperty(value = "实际开始时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date actualBeginTime;
    @ApiModelProperty(value = "工期")
    private Integer workDuration;
    @ApiModelProperty(value = "首次执行")
    private String  firstExecute;
    private String firstExecuteName;
    @ApiModelProperty(value = "是否高风险")
    private Boolean isHighRisk;
    @ApiModelProperty(value = "高风险等级")
    private String heightRiskLevelName;
    @ApiModelProperty(value = "防异物等级名称")
    private String antiForfeignLevel;
    private String antiForfeignLevelName;
    @ApiModelProperty(value = "大修轮次")
    @NotEmpty(message = "大修轮次不能为空")
    private String repairRound;
    @ApiModelProperty(value = "监管人员Id")
    private String supervisoryStaffId;
    @ApiModelProperty(value = "监管人员工号")
    private String supervisoryStaffCode;
    @ApiModelProperty(value = "监管人员名称")
    private String supervisoryStaffName;
    @ApiModelProperty(value = "管理人员Id")
    private String managePersonId;
    @ApiModelProperty(value = "管理人员工号")
    private String managePersonCode;
    @ApiModelProperty(value = "管理人员名称")
    private String managePersonName;
    @ApiModelProperty(value = "工作包审查状态")
    private Integer workPackageStatus;
    @ApiModelProperty(value = "高风险描述拼接")
    private String highRiskDescribe;

    @ApiModelProperty(value = "协助")
    private Boolean isCollaboration;
    @ApiModelProperty(value = "协同专业名称拼接")
    private String collaborationNames;
    @ApiModelProperty(value = "协同专业ID拼接")
    private String collaborationIds;
}
