package com.chinasie.orion.service.impl;


import com.chinasie.orion.domain.entity.JobPackage;
import com.chinasie.orion.domain.dto.JobPackageDTO;
import com.chinasie.orion.domain.vo.JobPackageVO;
import com.chinasie.orion.service.JobPackageService;
import com.chinasie.orion.repository.JobPackageMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;

import javax.servlet.http.HttpServletResponse;

import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;

import java.io.InputStream;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;


/**
 * <p>
 * JobPackage 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-09 15:47:59
 */
@Service
@Slf4j
public class JobPackageServiceImpl extends OrionBaseServiceImpl<JobPackageMapper, JobPackage> implements JobPackageService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public JobPackageVO detail(String id, String pageCode) throws Exception {
        JobPackage jobPackage = this.getById(id);
        JobPackageVO result = BeanCopyUtils.convertTo(jobPackage, JobPackageVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param jobPackageDTO
     */
    @Override
    public String create(JobPackageDTO jobPackageDTO) throws Exception {
        JobPackage jobPackage = BeanCopyUtils.convertTo(jobPackageDTO, JobPackage::new);
        this.save(jobPackage);

        String rsp = jobPackage.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param jobPackageDTO
     */
    @Override
    public Boolean edit(JobPackageDTO jobPackageDTO) throws Exception {
        JobPackage jobPackage = BeanCopyUtils.convertTo(jobPackageDTO, JobPackage::new);

        this.updateById(jobPackage);

        String rsp = jobPackage.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<JobPackageVO> pages(Page<JobPackageDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<JobPackage> condition = new LambdaQueryWrapperX<>(JobPackage.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(JobPackage::getCreateTime);


        Page<JobPackage> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), JobPackage::new));

        PageResult<JobPackage> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<JobPackageVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<JobPackageVO> vos = BeanCopyUtils.convertListTo(page.getContent(), JobPackageVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "作业工作包信息导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", JobPackageDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        JobPackageExcelListener excelReadListener = new JobPackageExcelListener();
        EasyExcel.read(inputStream, JobPackageDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<JobPackageDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("作业工作包信息导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<JobPackage> jobPackagees = BeanCopyUtils.convertListTo(dtoS, JobPackage::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::JobPackage-import::id", importId, jobPackagees, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<JobPackage> jobPackagees = (List<JobPackage>) orionJ2CacheService.get("pmsx::JobPackage-import::id", importId);
        log.info("作业工作包信息导入的入库数据={}", JSONUtil.toJsonStr(jobPackagees));

        this.saveBatch(jobPackagees);
        orionJ2CacheService.delete("pmsx::JobPackage-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::JobPackage-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<JobPackage> condition = new LambdaQueryWrapperX<>(JobPackage.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(JobPackage::getCreateTime);
        List<JobPackage> jobPackagees = this.list(condition);

        List<JobPackageDTO> dtos = BeanCopyUtils.convertListTo(jobPackagees, JobPackageDTO::new);

        String fileName = "作业工作包信息数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", JobPackageDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<JobPackageVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }

    @Override
    public JobPackage getInfoByJobId(String id) {
        LambdaQueryWrapperX<JobPackage> condition = new LambdaQueryWrapperX<>(JobPackage.class);
        condition.eq(JobPackage::getJobId,id);
        condition.select(JobPackage::getClassName,JobPackage::getJobId,JobPackage::getStatus,JobPackage::getId,JobPackage::getEquipmentSystem,JobPackage::getFunctionalLocation);
        List<JobPackage> list = this.list(condition);
        if(CollectionUtils.isEmpty(list)){
            return  null;
        }
        return list.get(0);
    }


    public static class JobPackageExcelListener extends AnalysisEventListener<JobPackageDTO> {

        private final List<JobPackageDTO> data = new ArrayList<>();

        @Override
        public void invoke(JobPackageDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<JobPackageDTO> getData() {
            return data;
        }
    }


}
