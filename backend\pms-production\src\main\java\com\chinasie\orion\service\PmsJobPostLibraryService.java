package com.chinasie.orion.service;

import com.chinasie.orion.domain.entity.PmsJobPostLibrary;
import com.chinasie.orion.domain.dto.PmsJobPostLibraryDTO;
import com.chinasie.orion.domain.vo.PmsJobPostLibraryVO;

import java.lang.String;
import java.util.List;


import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;
import java.util.Map;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * PmsJobPostLibrary 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 10:19:25
 */
public interface PmsJobPostLibraryService extends OrionBaseService<PmsJobPostLibrary> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    PmsJobPostLibraryVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param pmsJobPostLibraryDTO
     */
    String create(PmsJobPostLibraryDTO pmsJobPostLibraryDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param pmsJobPostLibraryDTO
     */
    Boolean edit(PmsJobPostLibraryDTO pmsJobPostLibraryDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<PmsJobPostLibraryVO> pages(Page<PmsJobPostLibraryDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<PmsJobPostLibraryVO> vos) throws Exception;

    /**
     *  通过岗位编号获取岗位信息
     * @param number
     * @return
     */
    PmsJobPostLibraryVO detailByNumber(String number);

    /**
     *  通过岗位ID列表获取岗位简化信息
     * @param jobIdList
     * @return
     */
    Map<String, String> getSimpleMapList(List<String> jobIdList);

    Map<String, String> listByNumberList(List<String> jobPostList);


    List<PmsJobPostLibraryVO> listEntity(PmsJobPostLibraryDTO postLibraryDTO);
}
