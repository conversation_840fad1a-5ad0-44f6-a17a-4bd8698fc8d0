package com.chinasie.orion.management.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

import java.util.List;
/**
 * ProcurePkgGroup VO对象
 *
 * <AUTHOR>
 * @since 2024-06-21 14:49:40
 */
@ApiModel(value = "ProcurePkgGroupVO对象", description = "采购项目包组")
@Data
public class ProcurePkgGroupVO extends  ObjectVO   implements Serializable{

    /**
     * 包组编号
     */
    @ApiModelProperty(value = "包组编号")
    private String groupId;


    /**
     * 包组名称
     */
    @ApiModelProperty(value = "包组名称")
    private String groupName;


    /**
     * 报价轮次
     */
    @ApiModelProperty(value = "报价轮次")
    private Integer biddingRounds;


    /**
     * 报价开始时间
     */
    @ApiModelProperty(value = "报价开始时间")
    private Date BiddingBeginTime;


    /**
     * 报价截止时间
     */
    @ApiModelProperty(value = "报价截止时间")
    private Date BiddingEndTime;


    /**
     * 报价状态
     */
    @ApiModelProperty(value = "报价状态")
    private String BiddingStatus;

    /**
     * 需求编号
     */
    @ApiModelProperty(value = "需求编号")
    private String requirementId;


}
