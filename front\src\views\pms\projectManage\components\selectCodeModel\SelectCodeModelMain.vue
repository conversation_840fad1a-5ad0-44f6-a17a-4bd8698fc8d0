<template>
  <div class="table-wrap">
    <OrionTable
      ref="tableRef"
      :options="tableOption"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { OrionTable } from 'lyra-component-vue3';

const tableRef = ref(null);

const columns = [
  {
    title: 'WBS名称',
    dataIndex: 'name',
  },
  {
    title: 'WBS编码',
    dataIndex: 'name',
  },
  {
    title: '预算金额',
    dataIndex: 'name',
    width: 120,
  },
  {
    title: '剩余金额',
    dataIndex: 'name',
    width: 120,
  },
  {
    title: '预算年度',
    dataIndex: 'name',
    width: 120,
  },
];
const tableOption = {
  rowSelection: {},
  columns,
  showToolButton: false,
  showTableSetting: false,
};
</script>

<style scoped lang="less">
.table-wrap{
  height: 400px;
  overflow: hidden;
}
</style>
