<template>
  <Layout2
    v-model:tabsIndex="tabsIndex"
    left-title="项目角色"
    class="people-manage-left"
  >
    <template #left>
      <div>
        <a-menu
          v-model:selectedKeys="selectedKeys"
          style="width: 99.5%"
          mode="inline"
          :open-keys="openKeys"
          @select="select"
        >
          <a-sub-menu key="sub1">
            <template #icon>
              <ApartmentOutlined />
            </template>
            <template
              #title
            >
              所有角色
              <!-- 增删功能 -->
              <!-- <div style="float: right; margin-right: 15px">
                <PlusSquareOutlined @click="addicon" />
                <MinusSquareOutlined @click="downicon" style="margin-left: 30px" />
              </div> -->
            </template>
          </a-sub-menu>
          <a-menu-item
            v-for="item in menuOptions"
            :key="item.id"
          >
            {{ item.name }}
          </a-menu-item>
        </a-menu>
      </div>
    </template>
    <PeopleTable
      v-if="tabsIndex === 0"
      :role-id="roleId"
      :project-id="id"
      :pageType="pageType"
    />
  </Layout2>
</template>

<script lang="ts">
import { deletRoleApi, peopleManegeApi } from '/@/views/pms/projectLaborer/api/projectList';

import {
  defineComponent, onMounted, reactive, toRefs, watch,
} from 'vue';
import { Layout2 } from 'lyra-component-vue3';
import { ApartmentOutlined } from '@ant-design/icons-vue';
import { Menu } from 'ant-design-vue';
import PeopleTable from './ManegeComponents/peopleTable.vue';

export default defineComponent({
  name: 'PeopleManege',
  components: {
    Layout2,
    AMenu: Menu,
    ASubMenu: Menu.SubMenu,
    AMenuItem: Menu.Item,
    ApartmentOutlined,
    PeopleTable,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
    keynumber: {
      type: String,
      default: '',
    },
    pageType: {
      type: String,
      default: 'page',
    },
  },

  emits: ['business'],
  setup(props, { emit }) {
    const state = reactive({
      tabsIndex: 0,
      rootSubmenuKeys: ['sub1'],
      openKeys: ['sub1'],
      selectedKeys: [],
      /* 菜单下拉配置 */
      menuOptions: [],
      /* 查询用户分页id */
      roleId: '',
    });
    watch(
      () => props.keynumber,
      () => getManege(),
    );

    onMounted(async () => {
      getManege();
    });

    const getManege = async () => {
      const res = await peopleManegeApi(props.id);
      state.selectedKeys = [res[0].id];
      state.roleId = res[0].id;
      state.menuOptions = res;
      // businessIdEmit(res[0].businessId);
    };
    function businessIdEmit(businessId) {
      emit('business', businessId);
    }
    const onOpenChange = (openKeys: string[]) => {
      const latestOpenKey = openKeys.find((key) => state.openKeys.indexOf(key) === -1);
      if (state.rootSubmenuKeys.indexOf(latestOpenKey!) === -1) {
        state.openKeys = openKeys;
      } else {
        state.openKeys = latestOpenKey ? [latestOpenKey] : [];
      }
    };

    const select = ({ key }) => {
      state.roleId = key;
      // const rows = state.menuOptions.filter((s) => s.id === key);
      // businessIdEmit(rows[0].businessId);
    };
      /* 添加+图标 */
    const addicon = () => {
    };
      /* --删除图标 */
    const downicon = () => {
      const params = [state.roleId];
      deletHandle(params);
    };
    const deletHandle = (params) => {
      deletRoleApi(params).then(() => {
        getManege();
      });
    };
    return {
      ...toRefs(state),
      onOpenChange,
      select,
      addicon,
      downicon,
    };
  },
});
</script>

<style lang="less" scoped>
  :deep(.ant-menu) {
    box-sizing: border-box;
    margin: 0;
    border: 0;
    .ant-menu-item,
    .ant-menu-item-active {
      &::before,
      &::after {
        content: '';
        width: 0;
        height: 0;
      }
    }
    .ant-menu-submenu {
      .ant-menu-submenu-title {
        background: #f5f5f5;
        padding: 0 0 0 10px !important;

        .ant-menu-submenu-arrow {
          &::before,
          &::after {
            content: '';
            width: 0;
            height: 0;
          }
        }
      }
    }
  }
  .wrap,
  .flex {
    box-sizing: border-box;
  }

</style>
