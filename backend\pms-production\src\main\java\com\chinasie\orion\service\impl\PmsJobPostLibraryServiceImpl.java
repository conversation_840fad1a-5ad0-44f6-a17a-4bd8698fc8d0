package com.chinasie.orion.service.impl;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.domain.entity.PmsJobPostLibrary;
import com.chinasie.orion.domain.dto.PmsJobPostLibraryDTO;
import com.chinasie.orion.domain.vo.PmsJobPostLibraryVO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.mybatis.domain.entity.LyraEntity;
import com.chinasie.orion.service.BasePlaceService;
import com.chinasie.orion.service.PmsJobPostLibraryService;
import com.chinasie.orion.repository.PmsJobPostLibraryMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;

import java.io.InputStream;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;


/**
 * <p>
 * PmsJobPostLibrary 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 10:19:25
 */
@Service
@Slf4j
public class PmsJobPostLibraryServiceImpl extends OrionBaseServiceImpl<PmsJobPostLibraryMapper, PmsJobPostLibrary> implements PmsJobPostLibraryService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Resource
    private BasePlaceService basePlaceService;



    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public PmsJobPostLibraryVO detail(String id, String pageCode) throws Exception {
        PmsJobPostLibrary pmsJobPostLibrary = this.getById(id);
        PmsJobPostLibraryVO result = BeanCopyUtils.convertTo(pmsJobPostLibrary, PmsJobPostLibraryVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param pmsJobPostLibraryDTO
     */
    @Override
    public String create(PmsJobPostLibraryDTO pmsJobPostLibraryDTO) throws Exception {
        PmsJobPostLibrary pmsJobPostLibrary = BeanCopyUtils.convertTo(pmsJobPostLibraryDTO, PmsJobPostLibrary::new);

        String num = "GW-"+RandomUtil.randomNumbers(6);
        pmsJobPostLibrary.setNumber(num);
        this.save(pmsJobPostLibrary);

        String rsp = pmsJobPostLibrary.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param pmsJobPostLibraryDTO
     */
    @Override
    public Boolean edit(PmsJobPostLibraryDTO pmsJobPostLibraryDTO) throws Exception {
        PmsJobPostLibrary pmsJobPostLibrary = BeanCopyUtils.convertTo(pmsJobPostLibraryDTO, PmsJobPostLibrary::new);

        this.updateById(pmsJobPostLibrary);

        String rsp = pmsJobPostLibrary.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<PmsJobPostLibraryVO> pages(Page<PmsJobPostLibraryDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<PmsJobPostLibrary> condition = new LambdaQueryWrapperX<>(PmsJobPostLibrary.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(PmsJobPostLibrary::getCreateTime);
        Page<PmsJobPostLibrary> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        PmsJobPostLibraryDTO libraryDTO=  pageRequest.getQuery();
        if(Objects.nonNull(libraryDTO)){
            if(StringUtils.hasText(libraryDTO.getBaseCode())){
                condition.eq(PmsJobPostLibrary::getBaseCode,libraryDTO.getBaseCode());
            }
        }
//        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), PmsJobPostLibrary::new));
        PageResult<PmsJobPostLibrary> page = this.getBaseMapper().selectPage(realPageRequest, condition);
        Page<PmsJobPostLibraryVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<PmsJobPostLibraryVO> vos = BeanCopyUtils.convertListTo(page.getContent(), PmsJobPostLibraryVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "作业岗位库导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", PmsJobPostLibraryDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        PmsJobPostLibraryExcelListener excelReadListener = new PmsJobPostLibraryExcelListener();
        EasyExcel.read(inputStream, PmsJobPostLibraryDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<PmsJobPostLibraryDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("作业岗位库导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<PmsJobPostLibrary> pmsJobPostLibraryes = BeanCopyUtils.convertListTo(dtoS, PmsJobPostLibrary::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("ncf::PmsJobPostLibrary-import::id", importId, pmsJobPostLibraryes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<PmsJobPostLibrary> pmsJobPostLibraryes = (List<PmsJobPostLibrary>) orionJ2CacheService.get("ncf::PmsJobPostLibrary-import::id", importId);
        log.info("作业岗位库导入的入库数据={}", JSONUtil.toJsonStr(pmsJobPostLibraryes));

        this.saveBatch(pmsJobPostLibraryes);
        orionJ2CacheService.delete("ncf::PmsJobPostLibrary-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("ncf::PmsJobPostLibrary-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<PmsJobPostLibrary> condition = new LambdaQueryWrapperX<>(PmsJobPostLibrary.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(PmsJobPostLibrary::getCreateTime);
        List<PmsJobPostLibrary> pmsJobPostLibraryes = this.list(condition);

        List<PmsJobPostLibraryDTO> dtos = BeanCopyUtils.convertListTo(pmsJobPostLibraryes, PmsJobPostLibraryDTO::new);

        String fileName = "作业岗位库数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", PmsJobPostLibraryDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<PmsJobPostLibraryVO> vos) throws Exception {
        Map<String,String> nuberToName = basePlaceService.allMapSimpleList();
        vos.forEach(vo -> {
            vo.setBaseName(nuberToName.getOrDefault(vo.getBaseCode(),""));
        });
    }

    @Override
    public PmsJobPostLibraryVO detailByNumber(String number) {
        if(StrUtil.isEmpty(number)){
            return null;
        }

        LambdaQueryWrapperX<PmsJobPostLibrary> wrapperX = new LambdaQueryWrapperX<>(PmsJobPostLibrary.class);
        wrapperX.eq(PmsJobPostLibrary::getNumber,number);
        wrapperX.select(PmsJobPostLibrary::getId,PmsJobPostLibrary::getAuthorizationGuide,PmsJobPostLibrary::getNumber
                ,PmsJobPostLibrary::getName,PmsJobPostLibrary::getAuthorizationTime,PmsJobPostLibrary::getBaseCode);
        List<PmsJobPostLibrary> list = this.list(wrapperX);
        if(CollectionUtils.isEmpty(list)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(),String.format( "未查到编号为【%s】的岗位信息",number));
        }
        Map<String,String> nuberToName = basePlaceService.allMapSimpleList();
        PmsJobPostLibraryVO postLibraryVO =  BeanCopyUtils.convertTo(list.get(0),PmsJobPostLibraryVO::new);
        postLibraryVO.setBaseName(nuberToName.getOrDefault(postLibraryVO.getBaseCode(),""));
        return postLibraryVO;
    }

    @Override
    public Map<String, String> getSimpleMapList(List<String> jobIdList) {
        if(CollectionUtils.isEmpty(jobIdList)){
            return  new HashMap<>();
        }
        LambdaQueryWrapperX<PmsJobPostLibrary> wrapperX = new LambdaQueryWrapperX<>(PmsJobPostLibrary.class);
        wrapperX.in(PmsJobPostLibrary::getId,jobIdList);
        wrapperX.select(PmsJobPostLibrary::getId,PmsJobPostLibrary::getAuthorizationGuide,PmsJobPostLibrary::getNumber
                ,PmsJobPostLibrary::getName,PmsJobPostLibrary::getAuthorizationTime,PmsJobPostLibrary::getBaseCode);
        List<PmsJobPostLibrary> list = this.list(wrapperX);
        if(CollectionUtils.isEmpty(list)){
            return  new HashMap<>();
        }
        return list.stream().collect(Collectors.toMap(LyraEntity::getId,PmsJobPostLibrary::getName));
    }

    @Override
    public Map<String, String> listByNumberList(List<String> jobPostList) {
        if(CollectionUtils.isEmpty(jobPostList)){
            return  new HashMap<>();
        }
        LambdaQueryWrapperX<PmsJobPostLibrary> wrapperX = new LambdaQueryWrapperX<>(PmsJobPostLibrary.class);
        wrapperX.in(PmsJobPostLibrary::getNumber,jobPostList);
        wrapperX.select(PmsJobPostLibrary::getId,PmsJobPostLibrary::getAuthorizationGuide,PmsJobPostLibrary::getNumber
                ,PmsJobPostLibrary::getName,PmsJobPostLibrary::getAuthorizationTime,PmsJobPostLibrary::getBaseCode);
        List<PmsJobPostLibrary> list = this.list(wrapperX);
        if(CollectionUtils.isEmpty(list)){
            return  new HashMap<>();
        }
        return list.stream().collect(Collectors.toMap(PmsJobPostLibrary::getNumber,PmsJobPostLibrary::getName));
    }

    @Override
    public List<PmsJobPostLibraryVO> listEntity(PmsJobPostLibraryDTO postLibraryDTO) {
        LambdaQueryWrapperX<PmsJobPostLibrary> wrapperX = new LambdaQueryWrapperX<>(PmsJobPostLibrary.class);
        // 为了兼容 获取 苏州院的 岗位
        if(Objects.nonNull(postLibraryDTO)){
//            wrapperX.setEntity(BeanCopyUtils.convertTo(postLibraryDTO,PmsJobPostLibrary::new));
            if(Objects.equals(postLibraryDTO.getBaseCode(),"SNPI") || !StringUtils.hasText(postLibraryDTO.getBaseCode())){
                wrapperX.eq(PmsJobPostLibrary::getName,"SNPI");
            }else{
                wrapperX.and(item->{
                    item.eq(PmsJobPostLibrary::getBaseCode, postLibraryDTO.getBaseCode()).or().eq(PmsJobPostLibrary::getBaseCode,"SNPI");
                });
            }
        }
        List<PmsJobPostLibrary> postLibraries =this.list(wrapperX);
        if(CollectionUtils.isEmpty(postLibraries)){
            return  new ArrayList<>();
        }
        return BeanCopyUtils.convertListTo(postLibraries,PmsJobPostLibraryVO::new);

    }


    public static class PmsJobPostLibraryExcelListener extends AnalysisEventListener<PmsJobPostLibraryDTO> {

        private final List<PmsJobPostLibraryDTO> data = new ArrayList<>();

        @Override
        public void invoke(PmsJobPostLibraryDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<PmsJobPostLibraryDTO> getData() {
            return data;
        }
    }


}
