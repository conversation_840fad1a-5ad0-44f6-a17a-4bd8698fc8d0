<script setup lang="ts">
import { Empty, Step, Steps } from 'ant-design-vue';
import {
  inject, onMounted, ref, Ref,
} from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import SpinView from '/@/views/pms/components/SpinView.vue';
import EmptyView from '/@/views/pms/components/EmptyView.vue';

const projectId = inject('projectId');
const loading:Ref<boolean> = ref(false);
const milestoneList:Ref<any[]> = ref([]);

onMounted(() => {
  getMilestoneList();
});

// 获取里程碑列表
async function getMilestoneList() {
  loading.value = true;
  try {
    const result = await new Api('/pms/projectCollectionStatistics/getProjectMilestoneViewList').fetch({
      projectId,
    }, '', 'GET');
    milestoneList.value = result || [];
  } finally {
    loading.value = false;
  }
}
</script>

<template>
  <div class="container">
    <spin-view v-if="loading" />
    <Steps
      v-else-if="milestoneList.length"
      class="steps"
      progress-dot
      :current="0"
    >
      <Step
        v-for="(item,index) in milestoneList"
        :key="index"
        :subTitle="`状态：${item.typeName||'--'}`"
        :description="`当前：${item.beginTime?dayjs(item.beginTime).format('YYYY年MM月DD日') :'--'}`"
      >
        <template #title>
          <div class="custom-title success">
            {{ item.name||'--' }}
          </div>
        </template>
      </Step>
    </Steps>
    <empty-view v-else />
  </div>
</template>

<style scoped lang="less">
.container{
  min-height: 145px;
}
.steps{
  padding-top: 40px;
}
:deep(.ant-steps-item-content){
  width: 150px;
}
.custom-title {
  position: absolute;
  top: -50px;
  left: 50%;
  transform: translateX(-50%);
  padding: 0 12px;
  max-width: 100%;
  line-height: 24px;
  border-radius: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  border: 1px solid #f1f1f1;
  font-size: 12px;

  &.success {
    color: ~`getPrefixVar('success-color')`;
    border-color: ~`getPrefixVar('success-color')`;
  }

  &.warning {
    color: ~`getPrefixVar('warning-color')`;
    border-color: ~`getPrefixVar('warning-color')`;
  }
}

:deep(.ant-steps-item-title) {
  width: 100%;
}
</style>
