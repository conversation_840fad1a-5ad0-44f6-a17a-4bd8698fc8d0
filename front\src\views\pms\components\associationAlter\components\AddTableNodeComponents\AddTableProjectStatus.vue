<script setup lang="ts">
import { BasicForm, getDictByNumber, useForm } from 'lyra-component-vue3';
import {
  computed, reactive, Ref, ref,
} from 'vue';
import dayjs from 'dayjs';

import { Input as AInput } from 'ant-design-vue';
import { simpleProjectTableColumns } from '../../tableColumns.js';

import Api from '/@/api';

import { AddSelectTableModal } from '../AddSelectTableModal/index';

const props = defineProps<{
  type: string | undefined
}>();

const loading: Ref<boolean> = ref(false);

const commonProjectInfo = reactive({
  projectName: '',
  projectId: '',
  oldStatus: '',
  oldStatusName: '',
});
const projectManagerPrams = reactive({
  projectName: computed(() => commonProjectInfo.projectName),
  projectId: computed(() => commonProjectInfo.projectId),
  oldManager: '',
  oldManagerName: '',
  newManager: '',
  newManagerName: '',
});

const projectStatusPrams = reactive({
  projectName: computed(() => commonProjectInfo.projectName),
  projectId: computed(() => commonProjectInfo.projectId),
  oldStatus: computed(() => commonProjectInfo.oldStatus),
  oldStatusName: computed(() => commonProjectInfo.oldStatusName),
  newStatus: '',
  stopStartTime: '',
  stopEndTime: '',
});

function SelectSimpleProjectClick() {
  AddSelectTableModal({
    title: '项目列表',
    width: '80%',
    selectType: 'radio',
    selectedData: [
      {
        id: commonProjectInfo.projectId,
        name: commonProjectInfo.projectName,
      },
    ],
    columns: simpleProjectTableColumns,
    tableApi(option) {
      const params: Record<string, any> = {
        ...option,
        query: {

        },
      };
      delete params.node;
      delete params.tableMethods;
      delete params.orders;
      return new Api('/pms/project/getSimplePage').fetch(params, '', 'POST');
    },
    async onOk({ tableData }) {
      const obj = tableData[0];
      commonProjectInfo.projectName = obj.name;
      commonProjectInfo.projectId = obj.id;
      commonProjectInfo.oldStatusName = obj.dataStatus.name;
      commonProjectInfo.oldStatus = obj.dataStatus.statusValue;
      projectManagerPrams.oldManagerName = obj.pm;
      projectManagerPrams.oldManager = obj.pmId;

      setFieldsValue({
        projectName: obj.name,
        projectId: obj.id,
        oldManagerName: obj.pm,
        oldManager: obj.pmId,
        oldStatusName: obj.dataStatus.name,
        oldStatus: obj.dataStatus.statusValue,
      });
    },
  });
}

const [
  register,
  {
    validate, updateSchema, setFieldsValue, getFieldsValue,
  },
] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas: [
    {
      field: 'projectName',
      component: 'Input',
      slot: 'SelectSimpleProject',
      label: '选择项目',
      rules: [{ required: true }],
    },
    {
      field: 'oldStatusName',
      component: 'Input',
      label: '原项目状态',
      rules: [{ required: true }],
      componentProps: {
        disabled: true,
      },
    },
    {
      field: 'newStatus',
      label: '变更项目状态',
      rules: [{ required: true }],
      componentProps: {
        placeholder: '请选择',
        api: () => getDictByNumber('ecr_status_status_type'),
        onChange: (val) => {
          // props.type = val;
          projectStatusPrams.newStatus = val;
          setFieldsValue({
            newStatus: val,
          });
        },
        labelField: 'name',
        valueField: 'number',
      },
      component: 'ApiSelect',
    },
    {
      field: 'stopStartTime',
      component: 'DatePicker',
      label: '暂停开始日期:',
      colProps: {
        span: 12,
      },
      componentProps: {
        placeholder: '请选择时间',
        valueFormat: 'YYYY-MM-DD',
        disabledDate: (current) => {
          let endTime = getFieldsValue().stopEndTime || '';
          if (endTime) {
            let startTime = dayjs(current).format('YYYY-MM-DD');
            let limitEndTime = dayjs(endTime).format('YYYY-MM-DD');
            return !(Date.parse(limitEndTime) >= Date.parse(startTime));
          }
          return false;
        },
        style: {
          width: '100%',
        },
      },
      ifShow: ({ values }) => values.newStatus === 'status_stop',
    },
    {
      field: 'stopEndTime',
      component: 'DatePicker',
      label: '暂停结束日期:',
      colProps: {
        span: 12,
      },
      componentProps: {
        placeholder: '请选择时间',
        valueFormat: 'YYYY-MM-DD',
        disabledDate: (current) => {
          let beginTime = getFieldsValue().stopStartTime || '';
          if (beginTime) {
            let startTime = dayjs(beginTime).format('YYYY-MM-DD');
            let endTime = dayjs(current).format('YYYY-MM-DD');
            return !(Date.parse(endTime) >= Date.parse(startTime));
          }
          return false;
        },
        style: {
          width: '100%',
        },
      },
      ifShow: ({ values }) => values.newStatus === 'status_stop',
    },
    // {
    //   field: 'applyTime',
    //   component: 'DatePicker',
    //   label: '项目启动日期:',
    //   colProps: {
    //     span: 12,
    //   },
    //   componentProps: {
    //     placeholder: '请选择时间',
    //     valueFormat: 'YYYY-MM-DD',
    //     style: {
    //       width: '100%',
    //     },
    //   },
    //   ifShow: ({ values }) => values.newStatus === 'status_start',
    // },
  ],
});

defineExpose({
  async getFormData() {
    const formValues = await validate();

    if (formValues && props.type === 'project_status') {
      projectStatusPrams.stopEndTime = formValues.stopEndTime || '';
      projectStatusPrams.stopStartTime = formValues.stopStartTime || '';
      return projectStatusPrams;
    }

    return null;
  },
  async setFormData(record, detailData = null) {
    await setFieldsValue({ ...record });

    commonProjectInfo.projectName = record.projectName;
    commonProjectInfo.projectId = record.projectId;

    if (props.type === 'project_status') {
      commonProjectInfo.oldStatus = detailData.oldStatus;
      commonProjectInfo.oldStatusName = detailData.oldStatusName;

      projectStatusPrams.newStatus = detailData.newStatus;
      projectStatusPrams.stopEndTime = detailData.stopEndTime || '';
      projectStatusPrams.stopStartTime = detailData.stopStartTime || '';

      setFieldsValue({ ...projectStatusPrams });
    }
  },
});
const tableRef = ref();

</script>

<template>
  <div>
    <BasicForm
      v-if="type!==''"
      :key="type"
      @register="register"
    >
      <template #SelectSimpleProject="{ model, field }">
        <AInput
          v-model:value="model[field]"
          style="width: 100%"
          @click="SelectSimpleProjectClick"
        />
      </template>
    </BasicForm>
  </div>
</template>

<style scoped lang="less">
.edit-btn{
  color: red;
}
.min-table{
  min-height: 300px;
}
</style>
