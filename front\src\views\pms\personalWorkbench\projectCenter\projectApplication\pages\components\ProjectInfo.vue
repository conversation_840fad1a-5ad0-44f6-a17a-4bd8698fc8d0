<script setup lang="ts">
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import GridFileList from '/@/views/pms/components/GridFileList.vue';

import {
  computed, inject, ref, Ref,
} from 'vue';
import { declarationData } from '../keys';

// 申报详情数据
const data = inject(declarationData);
// 项目基本信息
const projectBasicInfo: Ref<any[]> = ref([
  {
    label: '项目编号',
    field: 'projectNumber',
  },
  {
    label: '项目名称',
    field: 'projectName',
  },
  {
    label: '项目预估金额',
    field: 'estimateAmt',
    isMoney: true,
    unit: '元',
  },
  {
    label: '项目来源',
    field: '',
  },
  {
    label: '项目开始日期',
    field: 'projectStartTime',
    formatTime: 'YYYY-MM-DD',
  },
  {
    label: '项目结束日期',
    field: 'projectEndTime',
    formatTime: 'YYYY-MM-DD',
  },
  {
    label: '责任部门',
    field: 'rspDeptName',
  },
  {
    label: '项目负责人',
    field: 'resUserName',
  },
  {
    label: '项目类型',
    field: 'projectTypeName',
  },
  {
    label: '子分类',
    field: 'projectSubTypeName',
    gridColumn: '2/5',
    hidden: computed(() => !data.value?.projectSubTypeName),
  },
  {
    label: '项目申请理由',
    field: 'applyReason',
    gridColumn: '1/5',
  },
]);

// 项目详细信息
const projectDetailInfo: Ref<any[]> = ref([
  {
    label: '项目申报支持性材料',
    field: '',
    class: 'flex-ver',
    labelBold: true,
    slotName: 'fileTable',
  },
  {
    label: '项目背景摘要',
    field: 'projectBackground',
  },
  {
    label: '项目目标摘要',
    field: 'projectTarget',
  },
  {
    label: '技术方案摘要',
    field: 'technologyPlan',
  },
]);

const fileList: Ref<Array<{
  id: string,
  name: string,
  createUserName: string,
  createTime: string,
}>> = computed(() => data.value?.materialList && data.value.materialList.map((item) => ({
  ...item,
  fileName: `${item.name}.${item.filePostfix}`,
  filePostfix: `.${item.filePostfix}`,
})));
</script>

<template>
  <DetailsLayout
    border-bottom
    title="项目基本信息"
    :data-source="data"
    :list="projectBasicInfo"
  />
  <DetailsLayout
    border-bottom
    title="项目详细信息"
    :column="1"
    :data-source="data"
    :list="projectDetailInfo"
  >
    <template #fileTable>
      <GridFileList
        :column="1"
        :list="fileList"
      />
    </template>
  </DetailsLayout>
</template>

<style scoped lang="less">

</style>
