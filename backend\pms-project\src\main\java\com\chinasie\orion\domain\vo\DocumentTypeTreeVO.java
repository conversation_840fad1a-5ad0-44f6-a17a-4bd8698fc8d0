package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/2/22 14:55
 * @description:
 */
@Data
public class DocumentTypeTreeVO implements Serializable {

    @ApiModelProperty(value = "父级ID")
    private String parentId;

    @ApiModelProperty(value = "项目ID")
    private String projectId;

    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "描述/备注")
    private String remark;

    @ApiModelProperty(value = "文档数")
    private long count = 0L;

    @ApiModelProperty(value = "类名称")
    private String className;

    private List<DocumentTypeTreeVO> child;

}
