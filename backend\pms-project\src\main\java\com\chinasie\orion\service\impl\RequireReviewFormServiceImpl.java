package com.chinasie.orion.service.impl;

import com.chinasie.orion.domain.dto.RequireReviewFormDTO;
import com.chinasie.orion.domain.entity.RequireReviewForm;
import com.chinasie.orion.domain.vo.RequireReviewFormVO;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.RequireReviewFormMapper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.RequireReviewFormService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;

import javax.servlet.http.HttpServletResponse;

import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;

import java.io.InputStream;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;


/**
 * <p>
 * RequireReviewForm 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-03 20:42:14
 */
@Service
@Slf4j
public class RequireReviewFormServiceImpl extends OrionBaseServiceImpl<RequireReviewFormMapper, RequireReviewForm> implements RequireReviewFormService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public RequireReviewFormVO detail(String id, String pageCode) throws Exception {
        RequireReviewForm requireReviewForm = this.getById(id);
        RequireReviewFormVO result = BeanCopyUtils.convertTo(requireReviewForm, RequireReviewFormVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param requireReviewFormDTO
     */
    @Override
    public String create(RequireReviewFormDTO requireReviewFormDTO) throws Exception {
        RequireReviewForm requireReviewForm = BeanCopyUtils.convertTo(requireReviewFormDTO, RequireReviewForm::new);
        this.save(requireReviewForm);

        String rsp = requireReviewForm.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param requireReviewFormDTO
     */
    @Override
    public Boolean edit(RequireReviewFormDTO requireReviewFormDTO) throws Exception {
        RequireReviewForm requireReviewForm = BeanCopyUtils.convertTo(requireReviewFormDTO, RequireReviewForm::new);

        this.updateById(requireReviewForm);

        String rsp = requireReviewForm.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<RequireReviewFormVO> pages(Page<RequireReviewFormDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<RequireReviewForm> condition = new LambdaQueryWrapperX<>(RequireReviewForm.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(RequireReviewForm::getCreateTime);


        Page<RequireReviewForm> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), RequireReviewForm::new));

        PageResult<RequireReviewForm> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<RequireReviewFormVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<RequireReviewFormVO> vos = BeanCopyUtils.convertListTo(page.getContent(), RequireReviewFormVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "需求评审单导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", RequireReviewFormDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        RequireReviewFormExcelListener excelReadListener = new RequireReviewFormExcelListener();
        EasyExcel.read(inputStream, RequireReviewFormDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<RequireReviewFormDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("需求评审单导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<RequireReviewForm> requireReviewFormes = BeanCopyUtils.convertListTo(dtoS, RequireReviewForm::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::RequireReviewForm-import::id", importId, requireReviewFormes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<RequireReviewForm> requireReviewFormes = (List<RequireReviewForm>) orionJ2CacheService.get("pmsx::RequireReviewForm-import::id", importId);
        log.info("需求评审单导入的入库数据={}", JSONUtil.toJsonStr(requireReviewFormes));

        this.saveBatch(requireReviewFormes);
        orionJ2CacheService.delete("pmsx::RequireReviewForm-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::RequireReviewForm-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<RequireReviewForm> condition = new LambdaQueryWrapperX<>(RequireReviewForm.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(RequireReviewForm::getCreateTime);
        List<RequireReviewForm> requireReviewFormes = this.list(condition);

        List<RequireReviewFormDTO> dtos = BeanCopyUtils.convertListTo(requireReviewFormes, RequireReviewFormDTO::new);

        String fileName = "需求评审单数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", RequireReviewFormDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<RequireReviewFormVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }


    public static class RequireReviewFormExcelListener extends AnalysisEventListener<RequireReviewFormDTO> {

        private final List<RequireReviewFormDTO> data = new ArrayList<>();

        @Override
        public void invoke(RequireReviewFormDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<RequireReviewFormDTO> getData() {
            return data;
        }
    }


}
