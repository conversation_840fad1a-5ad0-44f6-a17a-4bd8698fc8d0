package com.chinasie.orion.util;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.chinasie.orion.api.authority.AuthorityServiceApi;
import com.chinasie.orion.api.authority.AuthorityServiceApiV2;
import com.chinasie.orion.api.authority.domain.dto.DataDetailAuthorityParamsDTO;
import com.chinasie.orion.api.authority.domain.dto.HeadAuthorityParamsDTO;
import com.chinasie.orion.api.authority.domain.dto.RowDataAuthorityBizParamsDTO;
import com.chinasie.orion.api.authority.domain.dto.RowDataAuthorityParamsDTO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.domain.dto.PowerParams;
import com.chinasie.orion.sdk.domain.vo.PageButtonAuthorityVO;
import com.chinasie.orion.sdk.domain.vo.PageContainerAuthorityVO;
import com.chinasie.orion.sdk.domain.vo.RowDataOperationAuthorityVO;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.service.ProjectRoleUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class PmsAuthUtil {

    @Autowired
    private AuthorityServiceApiV2 authorityServiceApi;

    @Autowired
    private ProjectRoleUserService projectRoleUserService;


    /**
     * 获取某项目下某用户下的角色code列表
     *
     * @param projectId
     * @param userId
     * @return
     * @throws Exception
     */
    public List<String> getRoleCodeList(String projectId, String userId) throws Exception {
        if (StringUtils.hasText(projectId)) {
            return projectRoleUserService.getRoleInfoByProjectAndUserId(projectId, userId);
        }
        return new ArrayList<>();
    }


    //设置列表权限
    public <T> void setRowAuths(String userId, PowerParams power, List<T> entities,
                                Function<T, String> getId,
                                Function<T, DataStatusVO> getDataStatusVO,
                                BiConsumer<T, List<RowDataOperationAuthorityVO>> setRowAuth,
                                Function<T, String> getCreatorId,
                                Function<T, String> getModifyId,
                                Function<T, String> getOwnerId,
                                Map<String, List<String>> dataRoleMap) throws Exception {
        if (ObjectUtil.isEmpty(power)) {
            log.error("【权限设置-列表】，前端传入参数为空");
            return;
        }
        if (StrUtil.isBlank(power.getPageCode()) || StrUtil.isBlank(power.getContainerCode())) {
            log.error("【权限设置-列表】，前端传入参数为空");
            return;
        }
        RowDataAuthorityParamsDTO rowDataAuthorityParamsDTO = setRowAuths(userId, power.getPageCode(), power.getContainerCode(), entities, getId, getDataStatusVO, getCreatorId, getModifyId, getOwnerId, dataRoleMap);
        ResponseDTO<Map<String, List<RowDataOperationAuthorityVO>>> rowDataAuthority = authorityServiceApi.getRowDataAuthority(rowDataAuthorityParamsDTO);
        Map<String, List<RowDataOperationAuthorityVO>> authMap = rowDataAuthority.getResult();
        if (ObjectUtil.isEmpty(authMap)) {
            log.error("【权限设置-列表】，pmi获取权限为空");
            return;
        }
        entities.forEach(vo -> {
            List<RowDataOperationAuthorityVO> rowDataOperationAuthorityVOS = authMap.get(getId.apply(vo));
            setRowAuth.accept(vo, rowDataOperationAuthorityVOS);
        });
    }


    private <T> RowDataAuthorityParamsDTO setRowAuths(String userId, String pageCode, String containerCode, List<T> datas,
                                                      Function<T, String> getId, Function<T, DataStatusVO> getDataStatusVO,
                                                      Function<T, String> getCreatorId, Function<T, String> getModifyId, Function<T, String> getOwnerId, Map<String, List<String>> dataRoleMap) {
        RowDataAuthorityParamsDTO rowDataAuthorityParamsDTO = new RowDataAuthorityParamsDTO();
        rowDataAuthorityParamsDTO.setUserId(userId);
        rowDataAuthorityParamsDTO.setPageCode(pageCode);
        rowDataAuthorityParamsDTO.setContainerCode(containerCode);
        rowDataAuthorityParamsDTO.setMenuId(ServletUtils.getRequest().getHeader("menuid"));
        List<RowDataAuthorityBizParamsDTO> rowAuths = datas.stream().map(data -> {
            RowDataAuthorityBizParamsDTO entity = new RowDataAuthorityBizParamsDTO();
            entity.setDataId(getId.apply(data));
            entity.setDataStatus(getDataStatusVO.apply(data));

            List<String> projectRoleCode = dataRoleMap.getOrDefault(getId.apply(data), new ArrayList<>());
            if (null != getCreatorId) {
                String creatorId = getCreatorId.apply(data);
                if (Objects.equals(userId, creatorId)) {
                    projectRoleCode.add("plan_sjcjz");
                }
            }
            if (null != getModifyId) {
                String modifyId = getModifyId.apply(data);
                if (Objects.equals(userId, modifyId)) {
                    projectRoleCode.add("plan_sjscyz");
                }
            }
            if (null != getOwnerId) {
                String ownerId = getOwnerId.apply(data);
                if (Objects.equals(userId, ownerId)) {
                    projectRoleCode.add("plan_sjsyz");
                }
            }
            entity.setRoleCodeList(projectRoleCode);
            return entity;
        }).collect(Collectors.toList());
        rowDataAuthorityParamsDTO.setBizDataList(rowAuths);

        return rowDataAuthorityParamsDTO;
    }


    /**
     * 设置详情权限
     *
     * @param entity
     * @param userId
     * @param pageCode
     * @param dataStatusVO
     * @param setRowAuth
     * @param <T>
     * @throws Exception
     */
    public <T> void setDetailAuths(T entity, String userId, String pageCode, DataStatusVO dataStatusVO, BiConsumer<T, List<PageContainerAuthorityVO>> setRowAuth, String creatorId, String modifyId, String ownerId, List<String> roleCodeList) throws Exception {
        if (StrUtil.isBlank(pageCode)) {
            log.error("【权限设置-详情】，前端传入参数为空");
            return;
        }
        DataDetailAuthorityParamsDTO dataDetailAuthorityParamsDTO = setDetailAuths(userId, pageCode, dataStatusVO, creatorId, modifyId, ownerId, roleCodeList);
        log.info("【权限设置-详情】，请求参数={}", JSONUtil.toJsonStr(dataDetailAuthorityParamsDTO));
        ResponseDTO<List<PageContainerAuthorityVO>> dataDetailAuthority = authorityServiceApi.getDataDetailAuthority(dataDetailAuthorityParamsDTO);
        log.info("【权限设置-详情】，响应结果={}", JSONUtil.toJsonStr(dataDetailAuthority));

        setRowAuth.accept(entity, dataDetailAuthority.getResult());
    }


    private DataDetailAuthorityParamsDTO setDetailAuths(String userId, String pageCode, DataStatusVO dataStatusVO, String creatorId, String modifyId, String ownerId, List<String> roleCodeList) {
        DataDetailAuthorityParamsDTO dataDetailAuthorityParamsDTO = new DataDetailAuthorityParamsDTO();
        dataDetailAuthorityParamsDTO.setUserId(userId);
        dataDetailAuthorityParamsDTO.setPageCode(pageCode);
        dataDetailAuthorityParamsDTO.setDataStatus(dataStatusVO);
        dataDetailAuthorityParamsDTO.setMenuId(ServletUtils.getRequest().getHeader("menuid"));
        List<String> projectRoleCode = new ArrayList<>(roleCodeList);
        if (Objects.equals(userId, creatorId)) {
            projectRoleCode.add("plan_sjcjz");
        }
        if (Objects.equals(userId, modifyId)) {
            projectRoleCode.add("plan_sjscyz");
        }
        if (Objects.equals(userId, ownerId)) {
            projectRoleCode.add("plan_sjsyz");
        }
        dataDetailAuthorityParamsDTO.setRoleCodeList(projectRoleCode);
        return dataDetailAuthorityParamsDTO;
    }


    /**
     * 设置分页列表头部权限
     *
     * @param entity
     * @param userId
     * @param power
     * @param setHeaderAuth
     * @param <T>
     * @throws Exception
     */
    public <T> void setHeaderAuths(T entity, String userId, PowerParams power, BiConsumer<T, List<PageButtonAuthorityVO>> setHeaderAuth, List<String> roleCodeList) throws Exception {

        if (Objects.isNull(power)) {
            return;
        }
        if (StrUtil.isBlank(power.getHeadContainerCode())) {
            return;
        }

        if (StrUtil.isBlank(power.getPageCode())) {
            return;
        }

        HeadAuthorityParamsDTO headAuthorityParamsDTO = new HeadAuthorityParamsDTO();

        headAuthorityParamsDTO.setHeadContainerCode(power.getHeadContainerCode());
        headAuthorityParamsDTO.setPageCode(power.getPageCode());
        headAuthorityParamsDTO.setRoleCodeList(roleCodeList);
        headAuthorityParamsDTO.setUserId(userId);
        headAuthorityParamsDTO.setMenuId(ServletUtils.getRequest().getHeader("menuid"));

        ResponseDTO<List<PageButtonAuthorityVO>> headAuthority = authorityServiceApi.getHeadAuthority(headAuthorityParamsDTO);

        log.info("详情权限集成，用户={}，入参={}，出参={}", userId, JSONUtil.toJsonStr(headAuthorityParamsDTO), JSONUtil.toJsonStr(headAuthority));
        setHeaderAuth.accept(entity, headAuthority.getResult());
    }


}
