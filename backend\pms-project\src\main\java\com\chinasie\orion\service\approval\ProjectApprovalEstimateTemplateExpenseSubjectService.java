package com.chinasie.orion.service.approval;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.approval.FormulaDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.domain.dto.approval.ProjectApprovalEstimateTemplateExpenseSubjectDTO;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalEstimateTemplateExpenseSubject;
import com.chinasie.orion.domain.vo.approval.FunctionVO;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalEstimateTemplateExpenseSubjectVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;

/**
 * <p>
 * ProjectApprovalEstimateTemplateExpenseSubject 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28 17:21:24
 */
public interface ProjectApprovalEstimateTemplateExpenseSubjectService  extends OrionBaseService<ProjectApprovalEstimateTemplateExpenseSubject> {

    /**
     *  详情
     *
     * * @param id
     */
    ProjectApprovalEstimateTemplateExpenseSubjectVO detail(String id, String pageCode)throws Exception;

    /**
     *  新增
     *
     * * @param projectApprovalEstimateTemplateExpenseSubjectDTO
     */
    String create(ProjectApprovalEstimateTemplateExpenseSubjectDTO projectApprovalEstimateTemplateExpenseSubjectDTO)throws Exception;

    /**
     *  批量新增
     *
     * * @param projectApprovalEstimateTemplateExpenseSubjectDTO
     */
    boolean createBatch(List<ProjectApprovalEstimateTemplateExpenseSubjectDTO> SubjectList)throws Exception;

    /**
     *  编辑
     *
     * * @param projectApprovalEstimateTemplateExpenseSubjectDTO
     */
    Boolean edit(ProjectApprovalEstimateTemplateExpenseSubjectDTO projectApprovalEstimateTemplateExpenseSubjectDTO)throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<ProjectApprovalEstimateTemplateExpenseSubjectVO> pages(Page<ProjectApprovalEstimateTemplateExpenseSubjectDTO> pageRequest)throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void  setEveryName(List<ProjectApprovalEstimateTemplateExpenseSubjectVO> vos)throws Exception;

    /**
     * 设置公式
     * @param formulaDTO
     * @return
     * @throws Exception
     */
    Boolean setFormula(FormulaDTO formulaDTO) throws Exception;

    /**
     * 取消公式
     * @param id
     * @return
     * @throws Exception
     */
    Boolean cancelFormula(String id) throws Exception;

    /**
     * 获取公式函数列表
     * @return
     */
    List<FunctionVO> getFunctionList();

    /**
     * 获取公式变量
     * @param estimateTemplateId
     * @return
     */
    List<ProjectApprovalEstimateTemplateExpenseSubjectVO> getFormulaVariable(String estimateTemplateId);

}
