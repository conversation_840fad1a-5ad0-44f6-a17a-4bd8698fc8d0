package com.chinasie.orion.service.impl;





import com.chinasie.orion.domain.entity.ProdActionItem;
import com.chinasie.orion.domain.entity.ProdActionRichText;
import com.chinasie.orion.domain.dto.ProdActionRichTextDTO;
import com.chinasie.orion.domain.vo.ProdActionRichTextVO;



import com.chinasie.orion.service.ProdActionRichTextService;
import com.chinasie.orion.repository.ProdActionRichTextMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import java.util.stream.Collectors;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * ProdActionRichText 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 10:33:48
 */
@Service
@Slf4j
public class ProdActionRichTextServiceImpl extends  OrionBaseServiceImpl<ProdActionRichTextMapper, ProdActionRichText>   implements ProdActionRichTextService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  ProdActionRichTextVO detail(String id,String pageCode) throws Exception {
        ProdActionRichText prodActionRishText =this.getById(id);
        ProdActionRichTextVO result = BeanCopyUtils.convertTo(prodActionRishText,ProdActionRichTextVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param prodActionRishTextDTO
     */
    @Override
    public  String create(ProdActionRichTextDTO prodActionRishTextDTO) throws Exception {
        ProdActionRichText prodActionRishText =BeanCopyUtils.convertTo(prodActionRishTextDTO,ProdActionRichText::new);
        this.save(prodActionRishText);

        String rsp=prodActionRishText.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param prodActionRishTextDTO
     */
    @Override
    public Boolean edit(ProdActionRichTextDTO prodActionRishTextDTO) throws Exception {
        ProdActionRichText prodActionRishText =BeanCopyUtils.convertTo(prodActionRishTextDTO,ProdActionRichText::new);

        this.updateById(prodActionRishText);

        String rsp=prodActionRishText.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProdActionRichTextVO> pages( Page<ProdActionRichTextDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProdActionRichText> condition = new LambdaQueryWrapperX<>( ProdActionRichText. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ProdActionRichText::getCreateTime);


        Page<ProdActionRichText> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProdActionRichText::new));

        PageResult<ProdActionRichText> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProdActionRichTextVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProdActionRichTextVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProdActionRichTextVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }




    @Override
    public void  setEveryName(List<ProdActionRichTextVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }

    @Override
    public void saveOrUpdateEntity(String id, String richText, String type) {
        ProdActionRichText prodActionRichText =new ProdActionRichText();
        if(!Objects.isNull(prodActionRichText)){
            prodActionRichText.setId(prodActionRichText.getId());
        }
        LambdaQueryWrapperX<ProdActionRichText> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(ProdActionRichText.class);
        lambdaQueryWrapperX.eq(ProdActionRichText::getActionId,id);
        lambdaQueryWrapperX.eq(ProdActionRichText::getDataType,type);
        List<ProdActionRichText> prodActionRichTexts= this.list(lambdaQueryWrapperX);
        if(CollectionUtils.isEmpty(prodActionRichTexts)){
            prodActionRichText.setDataType(type);
            prodActionRichText.setActionId(id);
            prodActionRichText.setRichText(richText);
            this.saveOrUpdate(prodActionRichText);
            return;
        }
        prodActionRichText= prodActionRichTexts.get(0);
        prodActionRichText.setDataType(type);
        prodActionRichText.setActionId(id);
        prodActionRichText.setRichText(richText);
        this.saveOrUpdate(prodActionRichText);
    }

    @Override
    public Map<String, List<ProdActionRichText>> getRichMapList(List<String> idList) {
        List<ProdActionRichText> list = this.list(new LambdaQueryWrapperX<ProdActionRichText>().in(ProdActionRichText::getActionId, idList));
        if(CollectionUtils.isEmpty(list)){
            return  new HashMap<>();
        }

        return list.stream().collect(Collectors.groupingBy(ProdActionRichText::getActionId));
    }

    @Override
    public ProdActionRichText getByActionId(String id, String type) {
        LambdaQueryWrapperX<ProdActionRichText> condition = new LambdaQueryWrapperX<>( ProdActionRichText. class);
        condition.eq(ProdActionRichText::getActionId,id);
        condition.eq(ProdActionRichText::getDataType,type);
        condition.last(" limit 1");
        return this.getOne(condition);

    }


}
