package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.DeliverableDTO;
import com.chinasie.orion.domain.dto.DeliverableQueryDTO;
import com.chinasie.orion.domain.dto.SearchDTO;
import com.chinasie.orion.domain.dto.pas.EcrDTO;
import com.chinasie.orion.domain.entity.Deliverable;
import com.chinasie.orion.domain.vo.AnalysisVO;
import com.chinasie.orion.domain.vo.DeliverPage;
import com.chinasie.orion.domain.vo.DeliverableVo;
import com.chinasie.orion.domain.vo.performance.DeliverablePerformanceVO;
import com.chinasie.orion.domain.vo.projectOverview.ProjectDeliverCount;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/23/18:11
 * @description:
 */
public interface DeliverableService extends OrionBaseService<Deliverable> {

    /**
     * 获取交付物列表  某计划下的
     *
     * @param planId
     * @return
     */
    List<DeliverableVo> listByPlanId(String planId, DeliverableQueryDTO deliverableQueryDTO) throws Exception;

    /**
     * 获取交付物详情信息
     *
     * @param id
     * @return
     */
    DeliverableVo getDetailById(String id) throws Exception;

    /**
     * 分页查询交付物列表
     *
     * @param pageRequest
     * @return
     */
    DeliverPage pageList(Page<DeliverableDTO> pageRequest) throws Exception;

    String saveDeliver(DeliverableDTO deliverableDTO) throws Exception;

    Boolean updateDeliver(DeliverableDTO deliverableDTO) throws Exception;

    /**
     * 批量删除交付物
     *
     * @param devIdList
     * @return
     */
    boolean delBatch(List<String> devIdList) throws Exception;

    /**
     * 交付物统计
     *
     * @param projectId
     * @return
     */
    ProjectDeliverCount getCount(String projectId) throws Exception;

    Boolean upgrade(String id) throws Exception;

    List<DeliverableVo> getDeliverableRevision(String revKey) throws Exception;

    /**
     * 变更
     *
     * @param id
     * @param ecrDTO
     * @return
     */
    String change(String id, EcrDTO ecrDTO) throws Exception;

    /**
     * 搜索
     *
     * @param searchDTO
     * @return
     */
    List<DeliverableVo> search(SearchDTO searchDTO) throws Exception;

    /**
     * 变更影响分析
     *
     * @param id
     * @return
     */
    List<AnalysisVO> analysis(String id) throws Exception;

    /**
     * 通过交付目标获取交付物列表
     * @param deliverGoalsId
     * @return
     * @throws Exception
     */
    List<DeliverableVo> listByDeliverGoals(String deliverGoalsId) throws Exception;

    /**
     * 通过用户id获取用户负责的项目计划的交付物分页
     * @param userId
     * @param pageRequest
     * @return
     * @throws Exception
     */
    com.chinasie.orion.sdk.metadata.page.Page<DeliverablePerformanceVO> pageDeliverableByUserId(String userId, com.chinasie.orion.sdk.metadata.page.Page<DeliverableDTO> pageRequest) throws Exception;

    /**
     * 导出通过用户id获取用户负责的项目计划的交付物分页
     * @param userId
     * @param pageRequest
     * @param response
     * @throws Exception
     */
    void exportExcelByUserId(String userId, com.chinasie.orion.sdk.metadata.page.Page<DeliverableDTO> pageRequest, HttpServletResponse response) throws Exception;

    List<DeliverableVo> getListByIds(List<String> ids);
}
