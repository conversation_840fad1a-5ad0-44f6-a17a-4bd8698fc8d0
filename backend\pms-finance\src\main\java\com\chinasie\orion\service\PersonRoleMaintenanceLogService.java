package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.PersonRoleMaintenanceLogDTO;
import com.chinasie.orion.domain.entity.PersonRoleMaintenanceLog;
import com.chinasie.orion.domain.vo.PersonRoleMaintenanceLogVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * PersonRoleMaintenanceLog 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-09 19:49:38
 */
public interface PersonRoleMaintenanceLogService  extends  OrionBaseService<PersonRoleMaintenanceLog>  {


    /**
     *  详情
     *
     * * @param id
     */
    PersonRoleMaintenanceLogVO detail(String id, String pageCode)throws Exception;

    /**
     *  新增
     *
     * * @param personRoleMaintenanceLogDTO
     */
    String create(PersonRoleMaintenanceLogDTO personRoleMaintenanceLogDTO)throws Exception;

    /**
     *  编辑
     *
     * * @param personRoleMaintenanceLogDTO
     */
    Boolean edit(PersonRoleMaintenanceLogDTO personRoleMaintenanceLogDTO)throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<PersonRoleMaintenanceLogVO> pages( Page<PersonRoleMaintenanceLogDTO> pageRequest)throws Exception;


    /**
     *  审核人员变更记录分页
     *
     * * @param pageRequest
     *
     */
    Page<PersonRoleMaintenanceLogVO> personRoleDetailLogPages(String roleId, Page<PersonRoleMaintenanceLogDTO> pageRequest)throws Exception;

    /**
     *  下载模板
     *
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response)throws Exception;

    /**
     *  导入校验
     *
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


    /**
     *  确认导入
     *
     * * @param importId
     */
    Boolean importByExcel(String importId)throws Exception;

    /**
     *  取消导入
     *
     * * @param importId
     */
    Boolean importCancelByExcel(String importId)throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

    /**
     *  设置各种名称
     *
     * * @param searchConditions
     * * @param response
     */
    void  setEveryName(List<PersonRoleMaintenanceLogVO> vos)throws Exception;
}
