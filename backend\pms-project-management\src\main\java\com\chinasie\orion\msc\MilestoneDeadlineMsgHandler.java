package com.chinasie.orion.msc;

import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.constant.ContractMilestoneNode;
import com.chinasie.orion.domain.entity.ContractMilestone;
import com.chinasie.orion.domain.entity.MarketContract;
import com.chinasie.orion.msc.api.MscBuildHandler;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class MilestoneDeadlineMsgHandler implements MscBuildHandler<ContractMilestone> {
    @Override
    public SendMessageDTO buildMsc(ContractMilestone contractMilestone, Object... objects) {
        Map<String,Object> messageMap = new HashMap<>();
        MarketContract marketContract =(MarketContract) objects[0];
        messageMap.put("contractName",marketContract.getName());
        messageMap.put("milestoneName",contractMilestone.getMilestoneName());
        messageMap.put("deadline",contractMilestone.getPlanAcceptDate());

        Set<String> distinct = new HashSet<>();
        distinct.add(contractMilestone.getTechRspUser());
        distinct.add(contractMilestone.getBusRspUser());
        distinct.add(marketContract.getTechRspUser());
        distinct.add(marketContract.getCommerceRspUser());
        List<String> toUser = new ArrayList<>(distinct);

        SendMessageDTO sendMessageDTO = SendMessageDTO.builder()
                .messageMap(messageMap)
                .titleMap(messageMap)
                .businessId(contractMilestone.getId())
                .messageUrl("/pas/milestones-details?id=" + contractMilestone.getId())
                .messageUrlName("里程碑详情")
                .senderTime(new Date())
                .recipientIdList(toUser)
                .platformId(contractMilestone.getPlatformId())
                .orgId(contractMilestone.getOrgId())
                .build();
        return sendMessageDTO;
    }

    @Override
    public String support() {
        return ContractMilestoneNode.NODE_MILESTONE_NOTIFY;
    }
}
