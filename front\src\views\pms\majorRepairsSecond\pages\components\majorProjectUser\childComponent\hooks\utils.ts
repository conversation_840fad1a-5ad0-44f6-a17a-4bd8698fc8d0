import {
  treeMap,
  openModal,
} from 'lyra-component-vue3';
import { Ref, ref, h } from 'vue';
import Api from '/@/api';

// 修改接口
export function updateEdit(record: Record<string, any>, url: string = 'relationOrgToMaterial/edit') {
  return new Api('/pms').fetch(record, url, 'PUT');
}

// 处理更新
export const handleUpdate = (dataList:any, record: any, params: any, field: string, value: string, resolve: (value: string) => void, url?: string, dicObject?: any, detailsData?: any, tableMethods?: any) => {
  updateEdit({
    ...params,
    [field]: value,
    repairOrgName: record?.name,
    name: record?.data?.userName,
    repairRound: detailsData?.repairRound,
    baseCode: detailsData?.baseCode,
    relationId: record?.id,
  }, url).then((res) => {
    treeMap(dataList.value, {
      conversion(item) {
        item.businessDataList = item.businessDataList.map((b) =>
          (b?.data?.personId === res.personId
            ? {
              ...b,
              data: {
                ...b.data,
                status: field === 'actInDate' && value ? 1 : (field === 'actOutDate' && value ? 2 : b?.data?.status),
                ...res,
                leaveReasonName: field === 'leaveReason' ? dicObject?.label : b?.data?.leaveReasonName,
              },
            }
            : b));
      },
    });
    // if (res) {
    //   tableMethods.updateTable();
    // }
    resolve('');
  }).catch((error) => {
    resolve('');
  });
};

// 修改接口
export function updateEditPersonal(record: Record<string, any>, url: string = 'relationOrgToMaterial/edit') {
  return new Api('/pms').fetch(record, url, 'PUT');
}

// 人员下砖处理更新
export const handleUpdatePersonal = (record: any, params: any, field: string, value: string, resolve: (value: string) => void, url?: string, dicObject?: any, detailsData?: any, updateTable?: any) => {
  updateEditPersonal({
    ...params,
    [field]: value,
    repairOrgName: record?.name,
    name: record?.data?.userName,
    repairRound: detailsData?.repairRound,
    baseCode: detailsData?.baseCode,
    relationId: record?.id,
  }, url).then((res) => {
    if (res) {
      updateTable();
    }
    resolve('');
  }).catch((error) => {
    resolve('');
  });
};

// 人员下转
export function openDataPersonalForm(component: any, object: {
  record?: Record<string, any>,
  detailsData?: any,
  type?: any,
}): void {
  const drawerRef: Ref = ref();
  openModal({
    title: '人员信息',
    width: '80%',
    height: 650,
    content() {
      return h(component, {
        ref: drawerRef,
        record: object?.record,
        detailsData: object?.detailsData,
        personDeepEnum: object?.type,
      });
    },
    footer: null,
  });
}
