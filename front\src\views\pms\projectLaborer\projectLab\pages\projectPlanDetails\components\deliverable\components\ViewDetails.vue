<template>
  <BasicDrawer
    v-model:visible="father.visible"
    class="ui-2-0"
    :width="350"
    :title="father.title"
    :body-style="bodyStyle"
    :mask-closable="false"
  >
    <a-tabs style="margin-top: -20px">
      <a-tab-pane tab="概述" />
    </a-tabs>
    <BasicTitle title="预览图">
      <div class="img-box">
        <img
          :src="father.form.planImage"
          alt=""
        >
      </div>
    </BasicTitle>
    <BasicTitle title="基本信息">
      <a-form
        :label-col="{ span: 7 }"
        :wrapper-col="{ span: 17 }"
      >
        <a-form-item label="编号">
          {{ father.form.number }}
        </a-form-item>
        <a-form-item label="名称">
          {{ father.form.name }}
        </a-form-item>
        <a-form-item label="所属项目">
          {{ father.form.projectName }}
        </a-form-item>
        <a-form-item label="所属任务">
          {{ father.form.planName }}
        </a-form-item>
        <a-form-item label="状态">
          {{ father.form.statusName }}
        </a-form-item>
        <a-form-item label="负责人">
          {{ father.form.principalName }}
        </a-form-item>
        <a-form-item label="计划交付时间">
          {{ father.form.predictDeliverTime }}
        </a-form-item>
        <a-form-item label="实际交付时间">
          {{ father.form.deliveryTime }}
        </a-form-item>
        <a-form-item label="描述">
          {{ father.form.remark }}
        </a-form-item>
        <a-form-item label="修改人">
          {{ father.form.modifyName }}
        </a-form-item>
        <a-form-item label="修改时间">
          {{ father.form.modifyTime }}
        </a-form-item>
        <a-form-item label="创建人">
          {{ father.form.creatorName }}
        </a-form-item>
        <a-form-item label="创建时间">
          {{ father.form.createTime }}
        </a-form-item>
      </a-form>
    </BasicTitle>
  </BasicDrawer>
</template>

<script>
import { computed, reactive, toRefs } from 'vue';
import { Drawer, Tabs, Form } from 'ant-design-vue';
import BasicTitle from '/@/views/pms/projectLaborer/componentsList/basicTitle/index.vue';

import dayjs from 'dayjs';
import {
  BasicDrawer,
} from 'lyra-component-vue3';
export default {
  name: 'ViewDetails',
  components: {
    BasicDrawer,
    BasicTitle,
    ATabs: Tabs,
    AForm: Form,
    AFormItem: Form.Item,
    ATabPane: Tabs.TabPane,
  },
  props: {
    data: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  setup(props) {
    const state = reactive({
      bodyStyle: {
        overflow: 'auto',
        height: 'calc(100vh - 55px)',
      },
      father: computed({
        get() {
          return props.data;
        },
      }),
    });
    function formatDate(t) {
      return t ? dayjs(t).format('YYYY-MM-DD HH:mm:ss') : '';
    }
    return {
      ...toRefs(state),
      formatDate,
    };
  },
};
</script>

<style scoped lang="less">
  :deep(.ant-form-item) {
    margin-bottom: 10px;
    .ant-form-item-label {
      text-align: left;
      > label::after {
        content: '';
      }
    }
  }

  .img-box {
    height: 200px;
    margin-bottom: 10px;

    > img {
      height: 100%;
      width: 100%;
    }
  }
</style>
