package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.PageContainerAuthorityVO;
import com.chinasie.orion.sdk.domain.vo.RowDataOperationAuthorityVO;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/2/22 17:55
 * @description:
 */
@Data
public class DocumentVO implements Serializable {
    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private String id;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "名称+文件后缀")
    private String fullName;

    /**
     * 数据Id
     */
    @ApiModelProperty(value = "数据Id")
    private String dataId;

    /**
     * 来源名称
     */
    @ApiModelProperty(value = "来源名称")
    private String sourceName;

    /**
     * 文档类型id
     */
    @ApiModelProperty(value = "文档类型id")
    private String documentTypeId;

    /**
     * 文档类型名称
     */
    @ApiModelProperty(value = "文档类型名称")
    private String documentTypeName;

    /**
     * 文件后缀
     */
    @ApiModelProperty(value = "文件后缀")
    private String filePostfix;

    @ApiModelProperty(value = "拥有者")
    private String ownerId;

    @ApiModelProperty(value = "拥有者名字")
    private String ownerName;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    @ApiModelProperty(value = "修改者ID")
    private String modifyId;

    @ApiModelProperty(value = "修改者名称")
    private String modifyName;

    @ApiModelProperty(value = "版本号")
    private String revId;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "状态名称")
    private String statusName;


    @ApiModelProperty(value = "类名称")
    private String className = "FileInfo";

    @ApiModelProperty(value = "签入签出")
    private String checkIn;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建者ID
     */
    @ApiModelProperty(value = "创建者ID")
    private String creatorId;

    @ApiModelProperty(value = "创建者")
    private String creatorName;

    @ApiModelProperty(value = "文件大小")
    private Long fileSize;

    /**
     * 状态对象
     */
    @ApiModelProperty(value = "状态对象")
    private DataStatusVO dataStatus;

    @ApiModelProperty("行数据权限")
    private List<RowDataOperationAuthorityVO> rdAuthList;

    @ApiModelProperty("数据详情权限（页面权限）")
    private List<PageContainerAuthorityVO> detailAuthList;
}

