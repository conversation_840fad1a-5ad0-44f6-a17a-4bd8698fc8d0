<script setup lang="ts">
import {
  Layout, OrionTable, BasicButton, BasicUpload, downLoadById, openFile, isPower,
} from 'lyra-component-vue3';
import {
  inject, onMounted, ref, Ref, unref,
} from 'vue';
import Api from '/@/api';
import { Modal } from 'ant-design-vue';
import dayjs from 'dayjs';
import { declarationData, declarationDataId } from '../keys';

const dataId: Ref = inject(declarationDataId);
const data = inject(declarationData);
const powerData:Ref = inject('powerData', ref());
const tableRef: Ref = ref();
const uploadRef: Ref = ref();
const selectRows: Ref<any[]> = ref([]);
const tableOptions = {
  showToolButton: false,
  rowSelection: {},
  api: (params:Record<string, any>) => new Api('/pms/projectDeclareFileInfo/meeting').fetch({
    ...params,
    query: {
      dataId: unref(dataId),
    },
  }, 'getPage', 'POST'),
  columns: [
    {
      title: '文件名称',
      dataIndex: 'fullName',
    },
    {
      title: '文件大小',
      dataIndex: 'fileSize',
      customRender({ text }) {
        return text ? `${text}kb` : '';
      },
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 140,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '查看',
      isShow: () => isPower('PMS_XMSBXQ_container_03_button_03', powerData.value),
      onClick(record:any) {
        openFile(record);
      },
    },
    {
      text: '下载',
      isShow: () => isPower('PMS_XMSBXQ_container_03_button_04', powerData.value),
      onClick(record:any) {
        downLoadById(record.id);
      },
    },
    {
      text: '删除',
      isShow: () => isPower('PMS_XMSBXQ_container_03_button_05', powerData.value),
      modal: (record:any) => batchDelete([record.id]),
    },
  ],
};

onMounted(() => {
  tableRef.value.redoHeight();
});

function updateTable() {
  tableRef.value.reload();
}

// 打开文件上传弹窗
function onUploadFile() {
  uploadRef.value.openModal(true);
}

// 保存回调
async function saveChange(successAll, cb) {
  const params = successAll.map((item) => ({
    ...item.result,
    dataId: unref(dataId),
    projectId: data.value?.projectId,
  }));
  await cb(new Api('/pms/projectDeclareFileInfo/meeting').fetch(params, 'saveBatch', 'POST'));
  updateTable();
}

// 表格多选
function selectionChange({ rows }) {
  selectRows.value = rows;
}

// 批量删除
function batchDelete(ids) {
  return new Promise((resolve, reject) => {
    new Api('/pms/projectDeclareFileInfo/meeting').fetch(ids, 'removeBatch', 'DELETE')
      .then(() => {
        updateTable();
        resolve(true);
      })
      .catch(() => {
        reject();
      });
  });
}

// 批量删除
function handleBatchDel() {
  Modal.confirm({
    title: '删除提示',
    content: '确认要删除已选择的记录？',
    onOk: () => batchDelete(selectRows.value.map((item) => item.id)),
  });
}
</script>

<template>
  <Layout
    :options="{ body: { scroll: true } }"
    contentTitle="会议纪要"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      :onSelectionChange="selectionChange"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="isPower('PMS_XMSBXQ_container_03_button_01',powerData)"
          type="primary"
          icon="orion-icon-upload"
          @click="onUploadFile"
        >
          上传附件
        </BasicButton>
        <BasicButton
          v-if="isPower('PMS_XMSBXQ_container_03_button_02',powerData)"
          icon="sie-icon-del"
          :disabled="selectRows.length===0"
          @click="handleBatchDel"
        >
          删除
        </BasicButton>
      </template>
    </OrionTable>
    <BasicUpload
      ref="uploadRef"
      :max-number="100"
      :isClassification="false"
      :isToolRequired="false"
      :isButton="false"
      :onSaveChange="saveChange"
    />
  </Layout>
</template>

<style scoped lang="less">

</style>
