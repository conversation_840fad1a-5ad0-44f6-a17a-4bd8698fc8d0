<template>
  <div class="messageComponent">
    <a-modal
      v-model:visible="visible"
      :title="title"
      ok-text="确认"
      cancel-text="取消"
      class="messageModal"
      width="380px"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <slot v-if="showSlot" />

      <div
        v-else
        class="messageVal"
      >
        <InfoCircleOutlined />
        <span>{{ messageVal }}</span>
      </div>
    </a-modal>
  </div>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, watch,
} from 'vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
import { Modal } from 'ant-design-vue';
export default defineComponent({
  name: 'MessageModal',
  components: {
    aModal: Modal,
    InfoCircleOutlined,
  },
  props: {
    title: {
      type: String,
      default: '',
    },
    showVisible: {
      type: Boolean,
      default: false,
    },
    showSlot: {
      type: Boolean,
      default: true,
    },
    messageVal: {
      type: String,
      default: '',
    },
  },
  setup(props, { emit }) {
    const state = reactive({
      visible: props.showVisible,
    });
    watch(
      () => props.showVisible,
      (newVal, odlVal) => {
        state.visible = newVal;
      },
    );
    return {
      ...toRefs(state),
      handleOk() {
        emit('confirm');
      },
      handleCancel() {
        emit('cancel');
      },
    };
  },
});
</script>
<style lang="less">
  .messageModal {
    .ant-modal-header {
      background: #5172dc;
      color: #ffffff;
      .ant-modal-title {
        color: #ffffff;
      }
    }
    .ant-modal-close-x {
      color: #ffffff;
    }
    .ant-modal-footer {
      text-align: center;
      .ant-btn {
        color: #5172dc;
        background: #5172dc19;
        border-radius: 4px;
      }
    }
  }
</style>
