package com.chinasie.orion.service.review;


import com.chinasie.orion.domain.dto.review.ReviewEssentialsDTO;
import com.chinasie.orion.domain.entity.review.ReviewEssentials;
import com.chinasie.orion.domain.vo.review.ReviewEssentialsVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

/**
 * <p>
 * ReviewEssentials 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:04
 */
public interface ReviewEssentialsService extends OrionBaseService<ReviewEssentials> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    ReviewEssentialsVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param reviewEssentialsDTO
     */
    String create(ReviewEssentialsDTO reviewEssentialsDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param reviewEssentialsDTO
     */
    Boolean edit(ReviewEssentialsDTO reviewEssentialsDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     *
     * @param mainTableId
     */
    Page<ReviewEssentialsVO> pages(String mainTableId, Page<ReviewEssentialsDTO> pageRequest) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<ReviewEssentialsVO> vos) throws Exception;

    /**
     * 启用
     * @param ids
     * @return
     */
    Boolean start(List<String> ids);

    /**
     * 禁用
     * @param ids
     * @return
     */
    Boolean stop(List<String> ids);
}
