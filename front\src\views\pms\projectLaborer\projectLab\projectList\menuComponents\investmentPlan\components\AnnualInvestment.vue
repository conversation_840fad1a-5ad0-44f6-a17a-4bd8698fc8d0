<template>
  <BasicDrawer
    v-bind="$attrs"
    title=""
    width="1000"
    :min-height="600"
    @register="registerModal"
    @visible-change="visibleChange"
  >
    <div class="add-annual-investment-content">
      <BasicForm
        v-if="showForm"
        class="content-form"
        @register="registerForm"
      >
        <template #monthInvestmentSchemes="{ model, field }">
          <div class="table-list">
            <div class="item-tr">
              <template
                v-for="(item,index) in model[field]"
                :key="index"
              >
                <div
                  v-if="index<6"
                  class="item-th"
                >
                  {{ item.name }}
                </div>
              </template>
            </div>
            <div class="item-tr">
              <template
                v-for="(item,index) in model[field]"
                :key="index"
              >
                <div
                  v-if="index<6"
                  class="item-td"
                >
                  <AInputNumber
                    v-model:value="item.predicate"
                    :disabled="closeFlagChange"
                    :min="initMin(model[field],index)"
                  />
                </div>
              </template>
            </div>
          </div>
          <div class="table-list">
            <div class="item-tr">
              <template
                v-for="(item,index) in model[field]"
                :key="index"
              >
                <div
                  v-if="index>=6"
                  class="item-th"
                >
                  {{ item.name }}
                </div>
              </template>
            </div>
            <div class="item-tr">
              <template
                v-for="(item,index) in model[field]"
                :key="index"
              >
                <div
                  v-if="index>=6"
                  class="item-td"
                >
                  <AInputNumber
                    v-model:value="item.predicate"
                    :disabled="closeFlagChange"
                    :min="initMin(model[field],index)"
                  />
                </div>
              </template>
            </div>
          </div>
        </template>
      </BasicForm>
    </div>

    <template #footer>
      <div class="add-plan-footer">
        <div class="btn-style">
          <BasicButton
            class="canncel"
            @click="cancel"
          >
            取消
          </BasicButton>
          <BasicButton
            type="primary"
            :loading="loadingBtn"
            @click="confirm('save')"
          >
            保存
          </BasicButton>
          <BasicButton
            v-if="formType==='add'"
            type="primary"
            :loading="loadingBtn"
            @click="confirm('startProcess')"
          >
            启动流程
          </BasicButton>
        </div>
      </div>
    </template>
  </BasicDrawer>
</template>
<script lang="ts">
import {
  defineComponent, onMounted, reactive, toRefs, computed, ref, nextTick,
} from 'vue';
import {
  useDrawerInner, BasicForm, useForm, BasicDrawer, BasicButton,
} from 'lyra-component-vue3';
import { InputNumber, message } from 'ant-design-vue';
import {
  annualPlanYear, getYearPlanDetails, initAnnuallForm, editYearPlan,
} from '../index';

export default defineComponent({
  name: 'AnnualInvestment',
  components: {
    AInputNumber: InputNumber,
    BasicForm,
    BasicDrawer,
    BasicButton,
  },
  emits: ['update'],
  setup(props, { emit }) {
    const state = reactive({
      formType: '',
      formId: '',
      showForm: false,
      showTable: false,
      formMethod: null as any,
      yearName: 0,
      loadingBtn: false,
      investmentId: '',
      formStatus: 100,
    });
    const [registerModal, { closeDrawer, setDrawerProps }] = useDrawerInner(async (modalData) => {
      state.showForm = true;
      setDrawerProps({ title: '调整年度投资计划申请表' });
      state.formId = modalData.id;
      state.formType = modalData.type;
      getYearPlanDetails(state.formId).then((res) => {
        state.investmentId = res.investmentId;
        let fieldList = [
          'architecture',
          'device',
          'other',
          'installation',
        ];
        let allValue = 0;
        for (let name in res) {
          if (fieldList.indexOf(name) >= 0 && res[name]) {
            allValue += Number(res[name]);
          }
        }
        res.yearComplete = allValue;
        res.thisYearValue = allValue;
        state.yearName = Number(res.yearName);
        state.formStatus = res.status;
        state.formMethod.setFieldsValue(res);
      });
    });
    // appendix 附件
    const [registerForm, formMethod] = useForm(initAnnuallForm(state));
    function cancel() {
      closeDrawer();
    }
    const confirm = async (type = 'save') => {
      let formData: any = await state.formMethod.validateFields();
      state.loadingBtn = true;
      formData.monthInvestmentSchemes.forEach((item) => {
        delete item.id;
      });
      formData.investmentId = state.investmentId;
      if (state.formType === 'add') {
        annualPlanYear(formData, state.formId).then((res) => {
          message.success('调整成功');
          closeDrawer();
          if (type === 'save') {
            emit('update', { type: 'save' });
          } else {
            emit('update', {
              type,
              record: res,
            });
          }
          state.loadingBtn = false;
        }).catch((err) => {
          state.loadingBtn = false;
        });
      } else {
        formData.id = state.formId;
        editYearPlan(formData).then((res) => {
          message.success('编辑成功');
          closeDrawer();
          emit('update', { type: 'save' });
          state.loadingBtn = false;
        }).catch((err) => {
          state.loadingBtn = false;
        });
      }
    };
    function visibleChange(val) {
      if (!val) {
        state.showForm = false;
      }
    }

    onMounted(() => {
      state.formMethod = formMethod;
    });
    function initMin(data, index) {
      if (index === 0) return 0;
      if (data[index - 1]?.predicate) {
        return data[index - 1]?.predicate;
      }
      return 0;
    }
    return {
      ...toRefs(state),
      registerModal,
      registerForm,
      cancel,
      confirm,
      visibleChange, // drawerColumns(),
      initMin,
    };
  },

});
</script>
<style lang="less" scoped>
.add-annual-investment-content{
  display: flex;
  height: 100%;
  flex-direction: column;
 :deep(.ant-form-item){
   display: block;
 }
  .table-list{
    height: 90px;
    overflow: auto;
    .item-tr{
      display: flex;
      width: 100%;
      border:1px solid #cccccc;
      border-right: 0;
      .item-th{
        width: calc(~'100%/6');
        background: #f2f2f2;
        text-align: center;
        padding: 5px;
      }
      .item-td{
        padding: 5px;
        width: calc(~'100%/6');
      }
      .item-th,.item-td{
        border-right: 1px solid #cccccc;
      }
    }
    .item-tr+.item-tr{
      border-top:0
    }
  }
}
.add-plan-footer{
  display: flex;
  justify-content: space-between;
  .next-check-box{
    line-height: 32px;
  }
  .btn-style{
    flex: 1;
    text-align: right;
    .canncel{
      margin-right: 10px;
    }
  }
}
</style>
<style lang="less">
</style>
