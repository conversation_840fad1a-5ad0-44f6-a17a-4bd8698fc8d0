<?xml version="1.0"?>
<!--
  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. See accompanying LICENSE file.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
                      https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.apache.hadoop</groupId>
    <artifactId>hadoop-project</artifactId>
    <version>3.3.3</version>
    <relativePath>../../hadoop-project</relativePath>
  </parent>
  <artifactId>hadoop-mapreduce-client</artifactId>
  <version>3.3.3</version>
  <name>Apache Hadoop MapReduce Client</name>
  <packaging>pom</packaging>

  <properties>
    <hadoop.common.build.dir>${basedir}/../../../hadoop-common-project/hadoop-common/target</hadoop.common.build.dir>
    <!-- Used by jdiff -->
    <!-- Antrun cannot resolve yarn.basedir, so we need to setup something else -->
    <dev-support.relative.dir>../dev-support</dev-support.relative.dir>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.avro</groupId>
      <artifactId>avro</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.eclipse.jetty</groupId>
          <artifactId>jetty-server</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.ant</groupId>
          <artifactId>ant</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.jboss.netty</groupId>
          <artifactId>netty</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.velocity</groupId>
          <artifactId>velocity</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-api</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>paranamer-ant</artifactId>
          <groupId>com.thoughtworks.paranamer</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>javax.ws.rs</groupId>
      <artifactId>javax.ws.rs-api</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.hadoop</groupId>
      <artifactId>hadoop-common</artifactId>
      <scope>provided</scope>
    </dependency>

    <dependency>
     <groupId>org.slf4j</groupId>
       <artifactId>slf4j-api</artifactId>
    </dependency>
    <dependency>
     <groupId>org.slf4j</groupId>
       <artifactId>slf4j-reload4j</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.hadoop</groupId>
      <artifactId>hadoop-annotations</artifactId>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.hadoop</groupId>
      <artifactId>hadoop-common</artifactId>
      <type>test-jar</type>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.hadoop</groupId>
      <artifactId>hadoop-hdfs</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.hadoop</groupId>
      <artifactId>hadoop-hdfs-client</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.google.inject.extensions</groupId>
      <artifactId>guice-servlet</artifactId>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty</artifactId>
    </dependency>
    <dependency>
      <groupId>commons-logging</groupId>
      <artifactId>commons-logging</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.hadoop.thirdparty</groupId>
      <artifactId>hadoop-shaded-guava</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>commons-codec</groupId>
      <artifactId>commons-codec</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>commons-cli</groupId>
      <artifactId>commons-cli</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>commons-collections</groupId>
      <artifactId>commons-collections</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.sun.jersey.jersey-test-framework</groupId>
      <artifactId>jersey-test-framework-grizzly2</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>
 
  <build>
    <plugins>
       <plugin>
        <groupId>com.github.spotbugs</groupId>
        <artifactId>spotbugs-maven-plugin</artifactId>
         <configuration>
          <xmlOutput>true</xmlOutput>
          <excludeFilterFile>${mr.basedir}/dev-support/findbugs-exclude.xml</excludeFilterFile>
          <effort>Max</effort>
       </configuration>
     </plugin>
     <plugin>
       <groupId>org.apache.maven.plugins</groupId>
       <artifactId>maven-surefire-plugin</artifactId>
       <configuration>
         <properties>
           <property>
             <name>listener</name>
             <value>org.apache.hadoop.test.TimedOutTestsListener</value>
           </property>
         </properties>
       </configuration>
     </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>docs</id>
      <activation>
        <activeByDefault>false</activeByDefault>
      </activation>
      <properties>
        <jdiff.stable.api>2.7.2</jdiff.stable.api>
        <jdiff.stability>-unstable</jdiff.stability>
        <jdiff.javadoc.maxmemory>512m</jdiff.javadoc.maxmemory>
      </properties>
      <dependencies>
        <dependency>
          <groupId>xerces</groupId>
          <artifactId>xercesImpl</artifactId>
          <version>${xerces.jdiff.version}</version>
        </dependency>
      </dependencies>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <executions>
              <execution>
                <goals>
                  <goal>javadoc-no-fork</goal>
                </goals>
                <phase>prepare-package</phase>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-dependency-plugin</artifactId>
            <executions>
              <execution>
                <id>site</id>
                <phase>prepare-package</phase>
                <goals>
                  <goal>copy</goal>
                </goals>
                <configuration>
                  <artifactItems>
                    <artifactItem>
                      <groupId>jdiff</groupId>
                      <artifactId>jdiff</artifactId>
                      <version>${jdiff.version}</version>
                      <overWrite>false</overWrite>
                      <outputDirectory>${project.build.directory}</outputDirectory>
                      <destFileName>jdiff.jar</destFileName>
                    </artifactItem>
                    <artifactItem>
                      <groupId>org.apache.hadoop</groupId>
                      <artifactId>hadoop-annotations</artifactId>
                      <version>${project.version}</version>
                      <overWrite>false</overWrite>
                      <outputDirectory>${project.build.directory}</outputDirectory>
                      <destFileName>hadoop-annotations.jar</destFileName>
                    </artifactItem>
                    <artifactItem>
                      <groupId>xerces</groupId>
                      <artifactId>xercesImpl</artifactId>
                      <version>${xerces.version.jdiff}</version>
                      <overWrite>false</overWrite>
                      <outputDirectory>${project.build.directory}</outputDirectory>
                      <destFileName>xerces.jar</destFileName>
                    </artifactItem>
                  </artifactItems>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-antrun-plugin</artifactId>
            <executions>
              <execution>
                <id>site</id>
                <phase>prepare-package</phase>
                <goals>
                  <goal>run</goal>
                </goals>
                <configuration>
                  <target if="should.run.jdiff">

                  <!-- Jdiff -->
                  <mkdir dir="${project.build.directory}/site/jdiff/xml"/>
                  <javadoc maxmemory="${jdiff.javadoc.maxmemory}" verbose="yes">
                    <doclet name="org.apache.hadoop.classification.tools.IncludePublicAnnotationsJDiffDoclet"
                            path="${project.build.directory}/hadoop-annotations.jar:${project.build.directory}/jdiff.jar">
                      <param name="-apidir" value="${project.build.directory}/site/jdiff/xml"/>
                      <param name="-apiname" value="${project.name} ${project.version}"/>
                      <param name="${jdiff.stability}"/>
                    </doclet>
                    <packageset dir="${basedir}/src/main/java"/>
                    <classpath>
                      <path refid="maven.compile.classpath"/>
                    </classpath>
                  </javadoc>
                  <javadoc sourcepath="${basedir}/src/main/java"
                           destdir="${project.build.directory}/site/jdiff/xml"
                           sourceFiles="${dev-support.relative.dir}/jdiff/Null.java"
                           maxmemory="${jdiff.javadoc.maxmemory}">
                    <doclet name="org.apache.hadoop.classification.tools.IncludePublicAnnotationsJDiffDoclet"
                            path="${project.build.directory}/hadoop-annotations.jar:${project.build.directory}/jdiff.jar:${project.build.directory}/xerces.jar">
                      <param name="-oldapi" value="${project.name} ${jdiff.stable.api}"/>
                      <param name="-newapi" value="${project.name} ${project.version}"/>
                      <param name="-oldapidir" value="${basedir}/${dev-support.relative.dir}/jdiff"/>
                      <param name="-newapidir" value="${project.build.directory}/site/jdiff/xml"/>
                      <param name="-javadocold"
                             value="https://hadoop.apache.org/docs/r${jdiff.stable.api}/api/"/>
                      <param name="-javadocnew" value="${project.build.directory}/site/api/"/>
                      <param name="-stats"/>
                      <param name="${jdiff.stability}"/>
                    </doclet>
                    <classpath>
                      <path refid="maven.compile.classpath"/>
                    </classpath>
                  </javadoc>
                  <property name="compile_classpath" refid="maven.compile.classpath"/>

                 </target>
               </configuration>
             </execution>
           </executions>
         </plugin>
       </plugins>
     </build>
   </profile>
  </profiles>
 
  <modules>
    <module>hadoop-mapreduce-client-core</module>
    <module>hadoop-mapreduce-client-common</module>
    <module>hadoop-mapreduce-client-shuffle</module>
    <module>hadoop-mapreduce-client-app</module>
    <module>hadoop-mapreduce-client-jobclient</module>
    <module>hadoop-mapreduce-client-hs</module>
    <module>hadoop-mapreduce-client-hs-plugins</module>
    <module>hadoop-mapreduce-client-nativetask</module>
    <module>hadoop-mapreduce-client-uploader</module>
  </modules>
</project>
