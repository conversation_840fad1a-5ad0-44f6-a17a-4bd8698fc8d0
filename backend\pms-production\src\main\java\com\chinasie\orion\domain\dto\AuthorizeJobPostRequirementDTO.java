package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.Boolean;
import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * AuthorizeJobPostRequirement DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-08 20:25:47
 */
@ApiModel(value = "AuthorizeJobPostRequirementDTO对象", description = "岗位授权要求检验表")
@Data
@ExcelIgnoreUnannotated
public class AuthorizeJobPostRequirementDTO extends  ObjectDTO   implements Serializable{

    /**
     * 岗位授权管理Id
     */
    @ApiModelProperty(value = "岗位授权管理Id")
    @ExcelProperty(value = "岗位授权管理Id ", index = 0)
    private String authorizeManageId;

    /**
     * 岗位id
     */
    @ApiModelProperty(value = "岗位id")
    @ExcelProperty(value = "岗位id ", index = 1)
    private String jobPostId;

    /**
     * 要求类型(取得证书、通过培训)（冗余）
     */
    @ApiModelProperty(value = "要求类型(取得证书、通过培训)（冗余）")
    @ExcelProperty(value = "要求类型(取得证书、通过培训)（冗余） ", index = 2)
    private String type;

    /**
     * 要求名称（冗余）
     */
    @ApiModelProperty(value = "要求名称（冗余）")
    @ExcelProperty(value = "要求名称（冗余） ", index = 3)
    private String name;

    /**
     * 应取得证书number（冗余）
     */
    @ApiModelProperty(value = "应取得证书number（冗余）")
    @ExcelProperty(value = "应取得证书number（冗余） ", index = 4)
    private String certificateNumber;

    /**
     * 应通过培训number
     */
    @ApiModelProperty(value = "应通过培训number")
    @ExcelProperty(value = "应通过培训number ", index = 5)
    private String trainNumber;

    /**
     * 是否满足要求
     */
    @ApiModelProperty(value = "是否满足要求")
    @ExcelProperty(value = "是否满足要求 ", index = 6)
    private Boolean isSatisfy;




    /**
     * 证书编号
     */
    @ApiModelProperty(value = "证书库ID")
    private String certificateId;

}
