<template>
  <div class="form-report">
    <BasicForm
      @register="registerForm"
    />
  </div>
</template>
<script lang="ts" setup>
import {
  BasicForm, DataStatusTag, openModal, useForm, useModal, getDict,
} from 'lyra-component-vue3';
import {
  ref, reactive, onMounted, computed, h, Ref, nextTick,
} from 'vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import Api from '/@/api';

const props = withDefaults(defineProps<{
    drawerData:object,
    treeData:any[],
    selectTreeId:string
}>(), {
  drawerData: () => ({
    type: 'add',
  }),
  treeData: () => [],
  selectTreeId: '',
});
const state = reactive({
  formId: '',
  formType: 'add',
});
const [
  registerForm,
  {
    setFieldsValue, clearValidate, resetFields, validateFields, getFieldsValue,
  },
] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  showActionButtonGroup: false,
  schemas: [
    {
      field: 'name',
      component: 'Input',
      label: '模版名称',
      colProps: {
        span: 12,
      },
      required: true,
      componentProps: {
        maxlength: 200,
        showCount: true,
      },
    },
    {
      field: 'templateClassifyId',
      component: 'TreeSelect',
      label: '所属分类',
      colProps: {
        span: 12,
      },
      componentProps: {
        treeData: props.treeData,
        fieldNames: {
          children: 'children',
          key: 'id',
          value: 'id',
          label: 'name',
        },
      },
    },
    {
      field: 'remark',
      component: 'InputTextArea',
      label: '模板描述',
      colProps: {
        span: 24,
      },
      componentProps: {
        placeholder: '请输入模板描述',
        maxlength: 500,
        style: { height: '130px' },
        showCount: true,
      },
    },

  ],
});
onMounted(async () => {
  if (props.drawerData.type === 'add') {
    await setFieldsValue({ templateClassifyId: props.selectTreeId });
  } else {
    await setFieldsValue(props.drawerData.record);
    // getFormPageData(props.drawerData.record.id);
  }
});

async function getModalData() {
  return await validateFields();
  // console.log(data);
}

defineExpose({
  getModalData,
});
</script>
<style lang="less" scoped>
:deep(.ant-input-affix-wrapper){
  height: 100%;
}
</style>
