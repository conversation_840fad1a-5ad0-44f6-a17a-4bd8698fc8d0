package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;


/**
 * AssessmentLog VO对象
 *
 * <AUTHOR>
 * @since 2024-10-17 09:28:12
 */
@ApiModel(value = "AssessmentLogVO对象", description = "审核记录表")
@Data
public class AssessmentLogVO extends  ObjectVO   implements Serializable{

            /**
         * 合同编号
         */
        @ApiModelProperty(value = "合同编号")
        private String contractNumber;


        /**
         * 中心名称
         */
        @ApiModelProperty(value = "中心名称")
        private String centerName;


        /**
         * 审批意见
         */
        @ApiModelProperty(value = "审批意见")
        private String assessmentAdvice;


        /**
         * 审批人id
         */
        @ApiModelProperty(value = "审批人id")
        private String personId;


        /**
         * 审批人姓名
         */
        @ApiModelProperty(value = "审批人姓名")
        private String personName;


        /**
         * 提交时间
         */
        @ApiModelProperty(value = "提交时间")
        private Date submitTime;


        /**
         * 审批时间
         */
        @ApiModelProperty(value = "审批时间")
        private Date assessmentTime;

        /**
         * 状态名称
         */
        @ApiModelProperty(value = "状态名称")
        private String statusName;


        @ApiModelProperty(value = "审批类型")
        private String type;

        @ApiModelProperty(value = "年份")
        private Integer year;
}
