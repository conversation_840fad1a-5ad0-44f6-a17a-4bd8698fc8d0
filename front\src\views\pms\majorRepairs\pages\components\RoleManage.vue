<script setup lang="ts">
import {
  BasicButton, IOpenBasicSelectModalProps, Layout, openBasicSelectModal, OrionTable,
} from 'lyra-component-vue3';
import {
  computed, inject, ref, Ref, unref, h,
} from 'vue';
import dayjs from 'dayjs';
import Api from '/@/api';
import { message, Modal } from 'ant-design-vue';

const detailsData: Record<string, any> = inject('detailsData', {});

const tableRef = ref();
const selectedKeys: Ref<string[]> = ref([]);
const keyword: Ref<string> = ref('');

const tableOptions = {
  rowSelection: {
    onChange(keys: string[]) {
      selectedKeys.value = keys;
    },
  },
  api: (params: any) => {
    delete params.searchConditions;
    return new Api('/pms/majorRepairPlanRole/page').fetch({
      ...params,
      query: {
        majorRepairTurn: detailsData.repairRound,
        roleName: unref(keyword),
      },
    }, '', 'POST');
  },
  showToolButton: false,
  columns: [
    {
      title: '编号',
      dataIndex: 'roleCode',
    },
    {
      title: '名称',
      dataIndex: 'roleName',
    },
    {
      title: '修改人',
      dataIndex: 'modifyName',
    },
    {
      title: '修改时间',
      dataIndex: 'modifyTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '';
      },
    },
  ],
};

function updateTable() {
  tableRef.value.reload();
}

const toolButton = computed(() => [
  {
    text: '系统添加',
    icon: 'sie-icon-fenpei',
    key: 'add',
  },
  {
    text: '删除',
    icon: 'delete',
    key: 'delete',
    disabled: selectedKeys.value.length === 0,
  },
]);

function handleToolButton(event: string) {
  switch (event) {
    case 'add':
      openBasicSelectModal(roleModalOptions);
      break;
    case 'delete':
      Modal.confirm({
        title: '删除提示！',
        content: '确认删除已选择的角色？',
        onOk: () => deleteApi(selectedKeys.value),
      });
      break;
  }
}

function deleteApi(ids: string[]) {
  return new Promise((resolve) => {
    new Api('/pms/majorRepairPlanRole/remove').fetch(ids, '', 'DELETE').then(() => {
      message.success('操作成功');
      updateTable();
      resolve(true);
    }).catch(() => {
      resolve(true);
    });
  });
}

const roleModalOptions: IOpenBasicSelectModalProps = {
  title: '添加角色',
  tableColumns: [
    {
      title: '角色编号',
      dataIndex: 'code',
    },
    {
      title: '角色名称',
      dataIndex: 'name',
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
      width: 100,
    },
    {
      title: '修改人',
      dataIndex: 'modifyName',
      width: 100,
    },
    {
      title: '备注',
      dataIndex: 'description',
      width: 100,
    },
  ],
  tableApi: async (params: any) => {
    const obj: Record<string, any> = {
      sysModuleTypeId: 'PMS',
      status: 1,
    };
    let keyword: string = '';
    params.searchConditions && (keyword = params.searchConditions[0][0].values.join(','));
    const result = await new Api('/pmi/role/list').fetch(obj, '', 'POST');
    return result?.filter((item) => item.name.includes(keyword));
  },
  onOk(selectedRows: Record<string, any>[]) {
    if (selectedRows.length === 0) {
      message.info('请选择角色');
      return Promise.reject();
    }
    return new Promise((resolve) => {
      new Api('/pms/majorRepairPlanRole/batch').fetch({
        majorRepairTurn: detailsData.repairRound,
        roleCodeList: selectedRows.map((item) => item.code),
      }, '', 'POST').then(() => {
        message.success('操作成功');
        updateTable();
        resolve();
      }).catch(() => {
        resolve();
      });
    });
  },
};
</script>

<template>
  <OrionTable
    ref="tableRef"
    v-model:keyword="keyword"
    :options="tableOptions"
  >
    <template #toolbarLeft>
      <BasicButton
        v-for="item in toolButton"
        :key="item.key"
        v-bind="item"
        @click="handleToolButton(item.key)"
      >
        {{ item.text }}
      </BasicButton>
    </template>
  </OrionTable>
</template>

<style scoped lang="less">
.role-manage-modal{
  height: 400px;
  overflow: hidden;
}
</style>
