package com.chinasie.orion.service.Impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.base.api.domain.entity.RoleDO;
import com.chinasie.orion.base.api.domain.entity.RoleUserDO;
import com.chinasie.orion.base.api.domain.vo.UserDeptVO;
import com.chinasie.orion.base.api.repository.RoleDOMapper;
import com.chinasie.orion.base.api.repository.RoleUserDOMapper;
import com.chinasie.orion.base.api.service.DeptBaseApiService;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.*;
import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.export.ImportExcelErrorNoteVO;
import com.chinasie.orion.excel.handler.MyExcelUtils;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.BaseErrorCode;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.MyExceptionCode;
import com.chinasie.orion.repository.ContractInfoMapper;
import com.chinasie.orion.msc.api.MscBuildHandlerManager;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.ContractCenterPlanMapper;
import com.chinasie.orion.repository.ContractMainMapper;
import com.chinasie.orion.repository.LaborCostAcceptanceMapper;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.RoleVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.domain.vo.org.DeptBaseInfoVO;
import com.chinasie.orion.sdk.domain.vo.org.SimpleDeptVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.Year;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;




/**
 * <p>
 * ContractMain 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-17 09:42:35
 */
@Service
@Slf4j
public class ContractMainServiceImpl extends  OrionBaseServiceImpl<ContractMainMapper, ContractMain>   implements ContractMainService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private ContractCenterService contractCenterService;

    @Autowired
    private ContractCostTypeService contractCostTypeService;

    @Autowired
    private ContractAssessmentStandardService standardService;

    @Autowired
    private ContractInfoMapper contractInfoMapper;

    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Autowired
    private ContractCenterPlanService centerPlanService;

    @Autowired
    private ContractCenterPlanMapper centerPlanMapper;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private DictRedisHelper dictRedisHelper;

    @Autowired
    private DeptBaseApiService deptBaseApiService;

    @Autowired
    private RoleUserDOMapper roleUserDOMapper;

    @Autowired
    private RoleDOMapper roleDOMapper;
    @Resource
    MscBuildHandlerManager mscBuildHandlerManager;


    @Autowired
    private LaborCostAcceptanceMapper laborCostAcceptanceMapper;





    /**
     * //表格读取监听器
     */
    private final ContractMainExcelListener sheetOneListener = new ContractMainExcelListener();
    private final ContractCenterExcelListener sheetTwoListener = new ContractCenterExcelListener();
    private final AssessmentStandardExcelListener sheetThreeListener = new AssessmentStandardExcelListener();

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ContractMainVO detail(String id, String pageCode) throws Exception {
        ContractMain contractMain = this.getById(id);
        ContractMainVO result = BeanCopyUtils.convertTo(contractMain, ContractMainVO::new);
        setEveryName(Collections.singletonList(result));

        return result;
    }

    /**
     * 新增
     * <p>
     * * @param contractMainDTO
     */
    @Override
    public String create(ContractMainDTO contractMainDTO) throws Exception {
        ContractMain contractMain = BeanCopyUtils.convertTo(contractMainDTO, ContractMain::new);
        this.save(contractMain);

        String rsp = contractMain.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param contractMainDTO
     */
    @Override
    public Boolean edit(ContractMainDTO contractMainDTO) throws Exception {
        ContractMain contractMain = BeanCopyUtils.convertTo(contractMainDTO, ContractMain::new);

        this.updateById(contractMain);

        String rsp = contractMain.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<IssuedDTO> params) throws Exception {
        List<String> numbers = params.stream().map(IssuedDTO::getContractNumber).collect(Collectors.toList());
        Integer year = Year.now().getValue();
        List<String> ids = contractInfoMapper.getContractMainIds(numbers, year);
        checkStatus(ids);
        // 删除相关中心
        LambdaQueryWrapperX<ContractCenter> centerWrapper = new LambdaQueryWrapperX<>(ContractCenter.class);
        centerWrapper.in(ContractCenter::getContractNumber, numbers);
        contractCenterService.remove(centerWrapper);

        //删除相关成本类型
        LambdaQueryWrapperX<ContractCostType> costTypeWrapper = new LambdaQueryWrapperX<>(ContractCostType.class);
        costTypeWrapper.in(ContractCenter::getContractNumber, numbers);
        contractCostTypeService.remove(costTypeWrapper);

        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ContractMainVO> pages(Page<ContractMainDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ContractMain> condition = new LambdaQueryWrapperX<>(ContractMain.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ContractMain::getCreateTime);


        Page<ContractMain> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ContractMain::new));

        PageResult<ContractMain> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ContractMainVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ContractMainVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ContractMainVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {


        String fileName = "技术配置导入模板.xlsx";
        response.reset();
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        try {
            MyExcelUtils.createTemplateCustomer(response,fileName,dictRedisHelper);

        } catch (Exception e) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERR.getErrorCode(), e.getMessage());
        }

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ContractMainExcelListener excelReadListener = new ContractMainExcelListener();

        ExcelReader read = EasyExcel.read(inputStream).build();
        ReadSheet sheet1 = EasyExcel.readSheet("sheet1").head(ContractMainImportDTO.class).headRowNumber(2).registerReadListener(sheetOneListener).build();
        ReadSheet sheet2 = EasyExcel.readSheet("sheet2").head(ContractCenterImportDTO.class).headRowNumber(2).registerReadListener(sheetTwoListener).build();
        ReadSheet sheet3 = EasyExcel.readSheet("sheet3").head(AssessmentStandardImportDTO.class).headRowNumber(2).registerReadListener(sheetThreeListener).build();

        read.read(sheet1, sheet2, sheet3);
        read.finish();

        //通过监听器获取数据
        ThreadLocal<List<ContractMainImportDTO>> dataOneList = sheetOneListener.getDataList();
        List<ContractMainImportDTO> contractMainImportList = dataOneList.get();

        ThreadLocal<List<ContractCenterImportDTO>> dataTwoList = sheetTwoListener.getDataList();
        List<ContractCenterImportDTO> centerImportList = dataTwoList.get();

        ThreadLocal<List<AssessmentStandardImportDTO>> dataThreeList = sheetThreeListener.getDataList();
        List<AssessmentStandardImportDTO> standardImportList = dataThreeList.get();

        checkData(result, contractMainImportList);
        checkData(result,centerImportList);
        checkData(result,standardImportList);

        handleContractMainImportDTO(contractMainImportList);

        if (!CollectionUtils.isEmpty(result.getErr())){
            return result;
        }
        //检查数据是否存在
        checkDataExist(centerImportList,contractMainImportList,result);
        if (!CollectionUtils.isEmpty(result.getErr())){
            return result;
        }

        List<ContractMain> contractMains = BeanCopyUtils.convertListTo(contractMainImportList, ContractMain::new);
        List<ContractCostType> contractCostTypes = BeanCopyUtils.convertListTo(contractMainImportList, ContractCostType::new);
        List<ContractCenter> contractCenters = BeanCopyUtils.convertListTo(centerImportList, ContractCenter::new);
        List<ContractAssessmentStandard> contractAssessmentStandards = BeanCopyUtils.convertListTo(standardImportList, ContractAssessmentStandard::new);

        contractCostTypes = setCostNumber(contractCostTypes);

        //封装合同状态
        if (!Objects.isNull(contractMains)){
            LambdaQueryWrapperX<ContractInfo> wrapperX = new LambdaQueryWrapperX<>(ContractInfo.class);
            List<String> numberLists = contractMains.stream().map(ContractMain::getContractNumber).collect(Collectors.toList());
            wrapperX.in(ContractInfo::getContractNumber,numberLists);
            wrapperX.select(ContractInfo::getStatus,ContractInfo::getContractNumber);
            List<ContractInfo> contractInfos = contractInfoMapper.selectList(wrapperX);
            Map<String, Integer> numberToStatus = contractInfos.stream().collect(Collectors.toMap(ContractInfo::getContractNumber, ContractInfo::getStatus));
            //合同去重
            contractMains = contractMains.stream().distinct().collect(Collectors.toList());
            contractCostTypes = contractCostTypes.stream().distinct().collect(Collectors.toList());
            contractMains.forEach(item->{
                item.setContractSatus(numberToStatus.getOrDefault(item.getContractNumber(),1));
            });
        }

        HashMap<String,List<?>> map = new HashMap<>();
        map.put("contractMains",contractMains);
        map.put("costTypes",contractCostTypes);
        map.put("contractCenters",contractCenters);
        map.put("contractAssessmentStandards",contractAssessmentStandards);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ContractMain-import::id", importId, map, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }

    public List<ContractCostType> setCostNumber(List<ContractCostType> contractCostTypes){
        List<DictValueVO> byDictNumber = dictRedisHelper.getByDictNumber(CostDictNumberConstant.COST_DICT_NUMBER, CurrentUserHelper.getOrgId());
        Map<String, String> nameToNumber = byDictNumber.stream().collect(Collectors.toMap(DictValueVO::getDescription, DictValueVO::getValue));

        for (ContractCostType contractCostType : contractCostTypes) {
            contractCostType.setCostTypeNumber(nameToNumber.getOrDefault(contractCostType.getCostTypeName(), ""));
        }
        return contractCostTypes;
    }


    private void handleContractMainImportDTO(List<ContractMainImportDTO> contractMainImportList) {
        for (ContractMainImportDTO contractMainImportDTO : contractMainImportList) {
            String costType = contractMainImportDTO.getCostTypeName();
            if (StrUtil.isNotBlank(costType)){
                char[] chars = costType.toCharArray();
                int begin = 0;
                int end = 0;
                for (int i = chars.length-1; i > -1; i--) {
                    if (chars[i]==')'){
                        end = i;
                        continue;
                    }
                    if (chars[i]=='('){
                        begin = i;
                        break;
                    }
                }
                contractMainImportDTO.setUnit(costType.substring(begin+1, end));
                contractMainImportDTO.setCostTypeName(costType.substring(0, begin));
            }
        }
    }

    private ImportExcelCheckResultVO checkData(ImportExcelCheckResultVO result,List<?> data){
        if (CollectionUtils.isEmpty(data)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (data.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("合同计划主表导入的原始数据={}", JSONUtil.toJsonStr(data));
        return result;
    }

    private ImportExcelCheckResultVO checkDataExist(List<ContractCenterImportDTO> centerImportList,
                                                    List<ContractMainImportDTO> contractMainImportList,
                                                    ImportExcelCheckResultVO result) throws Exception {
        LambdaQueryWrapperX<ContractInfo> wrapperX = new LambdaQueryWrapperX<>(ContractInfo.class);
        wrapperX.select(ContractInfo::getContractNumber);
        List<ContractInfo> contractInfos = contractInfoMapper.selectList(wrapperX);
        if (CollectionUtils.isEmpty(contractInfos)){
            throw new Exception("没有合同信息");
        }
        //检查导入合同数据是否存在于系统中
        List<String> contractNumbers = contractInfos.stream().map(ContractInfo::getContractNumber).collect(Collectors.toList());
        List<ImportExcelErrorNoteVO> errList = new ArrayList<>();
        for (ContractMainImportDTO contractMain : contractMainImportList) {
            if (!contractNumbers.contains(contractMain.getContractNumber())){
                ImportExcelErrorNoteVO importExcelErrorNoteVO = new ImportExcelErrorNoteVO();
                importExcelErrorNoteVO.setOrder(contractMain.getNumber());
                importExcelErrorNoteVO.setErrorNotes(Collections.singletonList("合同编号不存在"));
                errList.add(importExcelErrorNoteVO);
            }
        }

        //检查部门是否存在
        List<SimpleDeptVO> allSimpleDept = deptRedisHelper.getAllSimpleDept();
        List<String> codeIds = allSimpleDept.stream().map(SimpleDeptVO::getDeptCode).collect(Collectors.toList());
//        List<DeptVO> deptVOS = deptRedisHelper.listAllDept();
//        List<String> codeNames = deptVOS.stream().map(DeptVO::getCodeName).filter(StringUtils::hasText).collect(Collectors.toList());
        for (ContractCenterImportDTO item : centerImportList) {
            if (!codeIds.contains(item.getCenterCode())){
                ImportExcelErrorNoteVO importExcelErrorNoteVO = new ImportExcelErrorNoteVO();
                importExcelErrorNoteVO.setOrder(item.getNumber());
                importExcelErrorNoteVO.setErrorNotes(Collections.singletonList("部门不存在"));
                errList.add(importExcelErrorNoteVO);
            }
        }
        result.setErr(errList);
        result.setCode(200);
        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        Map<String, List<?>> dataMap = (Map<String, List<?>>) orionJ2CacheService.get("pmsx::ContractMain-import::id", importId);
        if (Objects.isNull(dataMap)){
            throw new Exception("数据不存在或已过期");
        }
        log.info("合同计划主表导入的入库数据={}", JSONUtil.toJsonStr(dataMap.toString()));

//        List<ContractMain> contractMains = (List<ContractMain>) dataMap.get("contractMains");
//        List<ContractCostType> contractCostTypes = (List<ContractCostType>) dataMap.get("costTypes");
//        List<ContractCenter> contractCenters = (List<ContractCenter>) dataMap.get("contractCenters");
//        List<ContractAssessmentStandard> assessmentStandards = (List<ContractAssessmentStandard>) dataMap.get("contractAssessmentStandards");

        List<ContractMain> contractMains = getTypedList(dataMap, "contractMains");
        contractMains.forEach(vo->{
            vo.setYear(new Date());
            vo.setStatus(ContractPlanStatusConstant.ING.getKey());
        });
        List<ContractCostType> contractCostTypes = getTypedList(dataMap, "costTypes");
        List<ContractCenter> contractCenters = getTypedList(dataMap, "contractCenters");
        List<ContractAssessmentStandard> assessmentStandards = getTypedList(dataMap, "contractAssessmentStandards");

        LambdaQueryWrapperX<ContractMain> wrapperX = new LambdaQueryWrapperX<>(ContractMain.class);
        wrapperX.in(ContractMain::getContractNumber,contractMains.stream().map(ContractMain::getContractNumber).collect(Collectors.toList()));
        this.remove(wrapperX);

        this.saveBatch(contractMains);
        contractCostTypeService.saveBatch(contractCostTypes);
        contractCenterService.saveBatch(contractCenters);
        standardService.saveBatch(assessmentStandards);

        orionJ2CacheService.delete("pmsx::ContractMain-import::id", importId);
        return true;
    }

    private <T> List<T> getTypedList(Map<String, List<?>> dataMap, String key) {
        List<T> list = (List<T>) dataMap.get(key);
        if (list == null) {
            list = new ArrayList<>();
        }
        return list;
    }

    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ContractMain-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ContractMain> condition = new LambdaQueryWrapperX<>(ContractMain.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ContractMain::getCreateTime);
        List<ContractMain> contractMaines = this.list(condition);

        List<ContractMainDTO> dtos = BeanCopyUtils.convertListTo(contractMaines, ContractMainDTO::new);

        String fileName = "合同计划主表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractMainDTO.class, dtos);

    }

    @Override
    public void issuedByIds(List<IssuedDTO> params) {
        ContractMainMapper baseMapper = this.getBaseMapper();
        List<String> unIssuedIds = new ArrayList<>();
        List<ContractMain> contractMains = new ArrayList<>();
        if (CollectionUtils.isEmpty(params)){
            //查询需要下发的合同
            LambdaQueryWrapper<ContractMain> wrapperSelect = new LambdaQueryWrapper<>(ContractMain.class);
            wrapperSelect.eq(ContractMain::getStatus,121);
            wrapperSelect.apply("EXTRACT(YEAR FROM year) = {0}",Year.now());
            wrapperSelect.select(ContractMain::getId,ContractMain::getYear,ContractMain::getContractName,ContractMain::getContractNumber,ContractMain::getOrgId,ContractMain::getPlatformId);
            contractMains = this.list(wrapperSelect);
            unIssuedIds = contractMains.stream().map(ContractMain::getId).collect(Collectors.toList());
//            unIssuedIds = baseMapper.getUnIssuedIds();
            setData(unIssuedIds);

            //更新
            LambdaUpdateWrapper<ContractMain> wrapper = new LambdaUpdateWrapper<>(ContractMain.class);
            wrapper.set(ContractMain::getStatus, ContractPlanStatusConstant.START.getKey());
            wrapper.in(ContractMain::getId, unIssuedIds);
            if (CollectionUtils.isEmpty(unIssuedIds)){
                throw new BaseException(MyExceptionCode.ERROR_NOT_FOUNT_JOB.getErrorCode(),"不存在未下发合同！");
            }
            this.update(wrapper);
        }else {
            LambdaQueryWrapperX<ContractMain> wrapperX = new LambdaQueryWrapperX<>(ContractMain.class);
            wrapperX.apply("EXTRACT(YEAR FROM year) = {0}", Year.now());
            wrapperX.eq(ContractMain::getContractNumber, params.get(0).getContractNumber());
            wrapperX.select(ContractMain::getId,ContractMain::getContractName,ContractMain::getContractNumber);
            contractMains = baseMapper.selectList(wrapperX);
            if (!CollectionUtils.isEmpty(contractMains)){
                unIssuedIds = contractMains.stream().map(ContractMain::getId).collect(Collectors.toList());
                contractMains.forEach(item->item.setStatus(ContractPlanStatusConstant.START.getKey()));
                setData(unIssuedIds);
                this.updateBatchById(contractMains);
            }
        }
        List<String> contractNumbers = contractMains.stream().map(ContractMain::getContractNumber).collect(Collectors.toList());
        Map<String,ContractMain> contractMainMap = contractMains.stream().collect(Collectors.toMap(ContractMain::getContractNumber,Function.identity()));
        List<ContractCenter> contractCenters = contractCenterService.list(new LambdaQueryWrapper<ContractCenter>().in(ContractCenter::getContractNumber,contractNumbers));
        LambdaQueryWrapperX<RoleDO> roleDOLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        roleDOLambdaQueryWrapperX.in(RoleDO::getCode, "Orgadmin001");
        roleDOLambdaQueryWrapperX.select(RoleDO::getId);
        RoleDO roleDO = roleDOMapper.selectOne(roleDOLambdaQueryWrapperX);
        LambdaQueryWrapper<RoleUserDO> roleUserDOQueryWrapper = new LambdaQueryWrapper<>();
        roleUserDOQueryWrapper.eq(RoleUserDO::getRoleId, roleDO.getId());
        List<RoleUserDO> roleUserDOS = roleUserDOMapper.selectList(roleUserDOQueryWrapper);
        List<String> orgAdminIds = roleUserDOS.stream().map(RoleUserDO::getUserId).collect(Collectors.toList());
//        List<UserVO> userVOS =  userRedisHelper.getUserDetail(CurrentUserHelper.getOrgId(),orgAdminIds);
//        Map<String,List<String>> deptMap = new HashMap<>();
        Map<String, List<UserDeptVO>> userDeptVOs = deptBaseApiService.getDeptByByUserId(CurrentUserHelper.getOrgId(),orgAdminIds);
        List<UserDeptVO> allUsers = userDeptVOs.values()
                .stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());
        Map<String,List<String>> deptMap = allUsers.stream().collect(Collectors.groupingBy(UserDeptVO::getDeptCode,Collectors.mapping(               // 映射提取用户名称
                UserDeptVO::getUserId,
                Collectors.toList()           // 收集为 List<String>
        )));
//        for(UserVO userVO:userVOS){
//            List<DeptVO> deptVOS = userVO.getOrganizations();
//            for(DeptVO deptVO:deptVOS){
//                //if(deptVO.getType().equals("20")){
//                    if(CollectionUtils.isEmpty(deptMap.get(deptVO.getDeptCode()))){
//                        List<String> userIds = new ArrayList<>();
//                        userIds.add(userVO.getId());
//                        deptMap.put(deptVO.getDeptCode(),userIds);
//                    }else{
//                        deptMap.get(deptVO.getDeptCode()).add(userVO.getId());
//                    }
//                //}
//            }
//        }
        for(ContractCenter contractCenter:contractCenters){
            if(CollUtil.isNotEmpty(deptMap.get(contractCenter.getCenterCode()))){
                List<String> ids = deptMap.get(contractCenter.getCenterCode());
                    ContractMain contractMain = contractMainMap.get(contractCenter.getContractNumber());
                    contractMain.setDeptId(contractCenter.getCenterCode());
                    mscBuildHandlerManager.send(contractMain, MsgHandlerConstant.CONTRACT_MAIN, ids);
            }
        }

    }

    public void setData(List<String> unIssuedIds){
        //初始化中心用人计划赋值并且持久化
        if (!CollectionUtils.isEmpty(unIssuedIds)){

            checkStatus(unIssuedIds);

            List<ContractCenterPlan> centerPlans = baseMapper.copyCenterPlan(unIssuedIds);
            if (CollectionUtils.isEmpty(centerPlans)){
                throw new BaseException(MyExceptionCode.ERROR_NOT_FOUNT_JOB.getErrorCode(),"合同用人计划没有部门或者成本类型，无法下发");
            }
            centerPlans.forEach(item->{
                item.setNum(0);
                item.setYear(new Date());
                item.setStatus(CenterPlanEnum.UNSUBMIT.getKey());
            });
            centerPlanService.saveBatch(centerPlans);
        }
    }

    @Override
    public String judge(String id) {
        DeptBaseInfoVO deptBaseInfoByDeptCode = deptRedisHelper.getDeptBaseInfoByDeptCode(CurrentUserHelper.getOrgId(),id);
        if (Objects.isNull(deptBaseInfoByDeptCode)){
            throw new BaseException(MyExceptionCode.ERROR_EXIST_DEPT);
        }
        if ("21".equals(deptBaseInfoByDeptCode.getTypeCode())){
            return "中心";
        }else if ("20".equals(deptBaseInfoByDeptCode.getType())){
            return "部门";
        }else {
            return "项目部";
        }
    }


    @Override
    public void establishment(EstablishmentContractPlanDTO establishmentContractPlanDTO) {

        List<ContractCenterDTO> contractCenters = establishmentContractPlanDTO.getContractCenters();
        List<ContractCostTypeDTO> contractCostTypes = establishmentContractPlanDTO.getContractCostTypes();
        List<ContractAssessmentStandardDTO> contractAssessmentStandards = establishmentContractPlanDTO.getContractAssessmentStandards();

        List<ContractCenter> centers = BeanCopyUtils.convertListTo(contractCenters, ContractCenter::new);
        List<ContractCostType> costTypes = BeanCopyUtils.convertListTo(contractCostTypes, ContractCostType::new);
        costTypes = setCostNumber(costTypes);
        List<ContractAssessmentStandard> assessmentStandards = BeanCopyUtils.convertListTo(contractAssessmentStandards, ContractAssessmentStandard::new);
        //更新中心信息
        LambdaQueryWrapperX<ContractCenter> centerWrapper = new LambdaQueryWrapperX<>(ContractCenter.class);
        centerWrapper.eq(ContractCenter::getContractNumber,establishmentContractPlanDTO.getContractNumber());
        contractCenterService.remove(centerWrapper);
        contractCenterService.saveBatch(centers);

        //更新成本类型
        LambdaQueryWrapperX<ContractCostType> costTypeWrapper = new LambdaQueryWrapperX<>(ContractCostType.class);
        costTypeWrapper.eq(ContractCostType::getContractNumber,establishmentContractPlanDTO.getContractNumber());
        contractCostTypeService.remove(costTypeWrapper);
        contractCostTypeService.saveBatch(costTypes);

        //更新考核标准
        LambdaQueryWrapperX<ContractAssessmentStandard> assessmentStandardWrapper = new LambdaQueryWrapperX<>(ContractAssessmentStandard.class);
        assessmentStandardWrapper.eq(ContractAssessmentStandard::getContractNumber,establishmentContractPlanDTO.getContractNumber());
        standardService.remove(assessmentStandardWrapper);
        standardService.saveBatch(assessmentStandards);
    }


    @Override
    public ContractInfoVO contractInfoDetail(Integer year, String contractNumber) {
        ContractInfoVO contractInfo = this.getBaseMapper().getContractInfo(year, contractNumber);
        Map<Integer, String> statusToName = ContractPlanStatusConstant.getMap();
        contractInfo.setStatusName(statusToName.getOrDefault(contractInfo.getStatus(),""));
        return contractInfo;
    }

    private void checkStatus(List<String> contractPlanIds){
     //   List<ContractMain> contractMains = baseMapper.selectBatchIds(contractPlanIds);

        List<ContractMain> contractMains = this.listByIds(contractPlanIds);
        //判断是否有其他的状态合同计划
        log.info("当前合同计划状态：{}",contractMains.get(0).getStatus());
        List<Integer> statusList = contractMains.stream()
                .map(ContractMain::getStatus)
                .filter(status -> !status.equals(ContractPlanStatusConstant.ING.getKey()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(statusList)){
            throw new BaseException(MyExceptionCode.ERROR_NOT_PERMISSION.getErrorCode(),"存在已下发合同计划，请重新选择！");
        }
    }


    @Override
    public Map<String, List<ContractCenterPlanVO>> groupCenterPlan(ContractCenterPlanListDTO contractCenterPlanListDTO) {
        Map<String, List<ContractCenterPlanVO>> nameToVo = new HashMap<>();
        if (centerPlanService.roleJudge()){
            List<ContractCenterPlanVO> res = centerPlanMapper.planListNoSumAndCenter(contractCenterPlanListDTO.getContractNumber(), contractCenterPlanListDTO.getYear());
            if (CollectionUtils.isEmpty(res)){
                return nameToVo;
            }
            res = getOriginalAmount(res);
            nameToVo = res.stream().collect(Collectors.groupingBy(ContractCenterPlanVO::getCenterName));
            return nameToVo;
        }

        SimpleUser simplerUser = userRedisHelper.getSimplerUser(CurrentUserHelper.getOrgId(), CurrentUserHelper.getCurrentUserId());
        String deptId = simplerUser.getDeptId();
        SimpleDeptVO simpleDeptById = deptRedisHelper.getSimpleDeptById(deptId);
        String deptCode = simpleDeptById.getDeptCode();
        List<ContractCenterPlanVO> res = centerPlanMapper.planList(contractCenterPlanListDTO.getContractNumber(), deptCode, contractCenterPlanListDTO.getYear());
        res = getOriginalAmount(res);
        nameToVo = res.stream().collect(Collectors.groupingBy(ContractCenterPlanVO::getCenterName));
        return nameToVo;
    }

    public List<ContractCenterPlanVO> getOriginalAmount(List<ContractCenterPlanVO> param){

        param.forEach(vo->{
            vo.setOriginalTotalAmount(vo.getUnitPrice().multiply(BigDecimal.valueOf(vo.getBeforeNum())));
        });

        return param;
    }

    @Override
    public void beginNextYear(List<String> contractMainIds) {
        List<ContractMain> contractMainList = getBeginIds(contractMainIds);
        List<ContractMain> noNextYear = contractMainList.stream().filter(vo -> vo.getHasNext() == 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(noNextYear)){
            throw new BaseException(MyExceptionCode.ERROR_NOT_FOUNT_JOB.getErrorCode(),"没有未开启下年录入的合同！");
        }

        List<String> numbers = new ArrayList<>();
        Date nextYear = Date.from(LocalDate.now().plusYears(1).atStartOfDay(ZoneId.systemDefault()).toInstant());

        //给新的合同计划状态和id设置为空
        // 更新需要开启下年录入的合同状态
        noNextYear.forEach(vo->{
            vo.setHasNext(1);
        });
        this.updateBatchById(noNextYear);

        noNextYear.forEach(vo->{
            numbers.add(vo.getContractNumber());
            vo.setId(null);

            vo.setYear(nextYear);
            vo.setStatus(ContractPlanStatusConstant.START.getKey());
            vo.setHasNext(0);
        });
        this.saveBatch(noNextYear);

        //复制单位用人计划
        LambdaQueryWrapperX<ContractCenterPlan> centerPlanWrapper = new LambdaQueryWrapperX<>(ContractCenterPlan.class);
        centerPlanWrapper.in(ContractCenterPlan::getContractNumber,numbers);
        centerPlanWrapper.like(ContractCenterPlan::getYear,Year.now());
        List<ContractCenterPlan> centerPlans = centerPlanService.list(centerPlanWrapper);
        centerPlans.forEach(vo->{
            vo.setYear(nextYear);
            vo.setId(null);
            vo.setStatus(CenterPlanEnum.UNSUBMIT.getKey());
        });
        centerPlanService.saveBatch(centerPlans);
    }

    @Override
    public void beginEdit(List<String> contractMainIds) {
        List<ContractMain> contractMains = getBeginIds(contractMainIds);
        if (CollectionUtils.isEmpty(contractMains)){
            throw new BaseException(MyExceptionCode.ERROR_NOT_FOUNT_JOB.getErrorCode(),"合同计划不存在！");
        }
        //更新用人计划的状态为可调整
        List<String> numbers = contractMains.stream().map(ContractMain::getContractNumber).collect(Collectors.toList());
        LambdaUpdateWrapper<ContractCenterPlan> wrapperX = new LambdaUpdateWrapper<>(ContractCenterPlan.class);
        wrapperX.in(ContractCenterPlan::getContractNumber,numbers);
        wrapperX.apply("EXTRACT(YEAR FROM year) = {0}", Year.now());
        wrapperX.set(ContractCenterPlan::getStatus,CenterPlanEnum.EDITABLE.getKey());
        centerPlanService.update(wrapperX);

    }


    @Override
    public List<Map<String,Object>> getSheetHeads() {
        List<Map<String,Object>> res = new ArrayList<>();

        List o = (List)orionJ2CacheService.get("pms::costType::sheetHeads", CostDictNumberConstant.HEAD_SHEETS_CACHE_KEY);
        log.info("111缓存获取的数据为:{}",o);
        if (CollectionUtils.isEmpty(o)||o.size()==0){
            log.info("数据为空");
            List<DictValueVO> costType = dictRedisHelper.getByDictNumber(CostDictNumberConstant.COST_DICT_NUMBER, CurrentUserHelper.getOrgId());
            //封装表头到map中
            for (DictValueVO dictValueVO : costType) {
                Map<String,Object> sheetHeads = new HashMap<>();
                String description = dictValueVO.getDescription();
                String number = dictValueVO.getValue();
                SheetHeadHandler.wrapperParam(sheetHeads, description, number);
                res.add(sheetHeads);
            }
            //将表头数据放到缓存中
            orionJ2CacheService.set("pms::costType::sheetHeads",CostDictNumberConstant.HEAD_SHEETS_CACHE_KEY,res);
            return res;
        }
        res = (List<Map<String,Object>>) o;
        return res;
    }


    @Override
    public List<ContractCenterPlanVO> listByCode(String code, String contractNumber, Integer year) {
        List<ContractCenterPlanVO> res;
        if (!StringUtils.isEmpty(code)){
            res = this.getBaseMapper().listByCode(code, contractNumber, year);
        }else {
            res = this.getBaseMapper().listSum( contractNumber, year);
        }
        //计算总金额
        for (ContractCenterPlanVO re : res) {
            re.setTotalAmount(re.getUnitPrice().multiply(BigDecimal.valueOf(re.getNum())));
        }
        return res;
    }

    @Override
    public LaborCostStatisticsVO laborCostStatistics(String contractNumber, Integer year) {
        LocalDate now = LocalDate.now();
        int currentYear = now.getYear();
        int currentMonth = now.getMonthValue();
        if(year > currentYear){
            return null;
        }
        int totalQuarter = 4;

        if(year == currentYear){
            if(currentMonth < 4){
                totalQuarter = 1;
            }else if(currentMonth < 7){
                totalQuarter = 2;
            }else if(currentMonth < 10){
                totalQuarter = 3;
            }
        }

        LaborCostStatisticsVO laborCostStatisticsVO = new LaborCostStatisticsVO();


        List<ContractInfo>  contractInfos = contractInfoMapper.selectList(ContractInfo :: getContractNumber,contractNumber);
        if(CollectionUtils.isEmpty(contractInfos)){
            return null;
        }



        ContractInfo contractInfo = contractInfos.get(0);
        laborCostStatisticsVO.setId(contractInfo.getContractNumber());
        laborCostStatisticsVO.setName(contractInfo.getContractName());
        laborCostStatisticsVO.setType("contract");

        String userId = CurrentUserHelper.getCurrentUserId();
        UserVO userById = userRedisHelper.getUserById(userId);
        if (Objects.isNull(userById)){
            throw new BaseException(MyExceptionCode.ERROR_USER_NOT_FOUNT);
        }


        log.error("角色-----------------------"+JSONObject.toJSONString(userById));
        List<RoleVO> roles = userById.getRoles();
        List<String> codeList = roles.stream().map(RoleVO::getCode).collect(Collectors.toList());
        List<LaborCostStatisticsVO> deptResultList = new ArrayList<>();
        laborCostStatisticsVO.setChildren(deptResultList);
       if(codeList.contains(RoleCode.technologyConfig)){
           LambdaQueryWrapper<ContractCenter> wrapper = new LambdaQueryWrapper<>(ContractCenter.class);
           wrapper.eq(ContractCenter::getContractNumber, contractNumber);
           List<ContractCenter> list = contractCenterService.list(wrapper);
           if (CollectionUtils.isEmpty(list)){
               return laborCostStatisticsVO;
           }
           list.forEach(item ->{
               LaborCostStatisticsVO deptResult = new LaborCostStatisticsVO();
               deptResult.setId(item.getId());
               deptResult.setOrgCode(item.getCenterCode());
               deptResult.setName(item.getCenterName());
               deptResult.setParentId(laborCostStatisticsVO.getId());
               deptResult.setType("dept");
               deptResultList.add(deptResult);
           });
        }
       else{
           SimpleUser simpleUser = userRedisHelper.getSimplerUser(CurrentUserHelper.getOrgId(), CurrentUserHelper.getCurrentUserId());
           if(simpleUser == null){
               return laborCostStatisticsVO;
           }

           String orgCode = simpleUser.getOrgCode();
           String orgName = simpleUser.getOrgName();
           LaborCostStatisticsVO deptResult = new LaborCostStatisticsVO();
           deptResult.setOrgCode(orgCode);
           deptResult.setId(orgCode);
           deptResult.setParentId(laborCostStatisticsVO.getId());
           deptResult.setName(orgName);
           deptResult.setType("dept");
           deptResultList.add(deptResult);
       }

        if(CollectionUtils.isEmpty(deptResultList)){
            return laborCostStatisticsVO;
        }



        List<String> deptCodes =  deptResultList.stream().map(LaborCostStatisticsVO :: getOrgCode).collect(Collectors.toList());

        List<AttendanceSignStatistics> attendanceSignStatistics = this.baseMapper.selectAttendanceSign(deptCodes,year,contractNumber,null);

        List<TravelCostStatistics> travelCostStatistics = this.baseMapper.selectTravelCost(deptCodes,year,contractNumber,null);

        List<PlaneCostStatistics> planeCostStatistics = this.baseMapper.selectPlaneCost(deptCodes,year,contractNumber,null);

        List<OpenCostStatistics> openCostStatistics = this.baseMapper.selectOpenCost(deptCodes,year,contractNumber,null);

//        if(CollectionUtils.isEmpty(attendanceSignStatistics) && CollectionUtils.isEmpty(travelCostStatistics)
//                && CollectionUtils.isEmpty(planeCostStatistics) && CollectionUtils.isEmpty(openCostStatistics)){
//            return laborCostStatisticsVO;
//        }


        //获取验收审批状态
        LambdaQueryWrapperX<LaborCostAcceptance> acceptanceLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        acceptanceLambdaQueryWrapperX.eq(LaborCostAcceptance :: getContractNo,contractNumber);
        acceptanceLambdaQueryWrapperX.eq(LaborCostAcceptance :: getAcceptanceYear,year);
        List<LaborCostAcceptance> laborCostAcceptances =  laborCostAcceptanceMapper.selectList(acceptanceLambdaQueryWrapperX);
        Map<String,List<LaborCostAcceptance>>  laborCostAcceptanceMap = new HashMap();
        if(!CollectionUtils.isEmpty(laborCostAcceptances)){
            laborCostAcceptanceMap = laborCostAcceptances.stream().collect(Collectors.groupingBy(LaborCostAcceptance :: getAcceptanceDeptCode));
        }


        Map<String,List<AttendanceSignStatistics>> attendanceSignStatisticsMap = new HashMap();
        if(!CollectionUtils.isEmpty(attendanceSignStatistics)){
            attendanceSignStatisticsMap = attendanceSignStatistics.stream().collect(Collectors.groupingBy(AttendanceSignStatistics :: getOrgCode));
        }

        Map<String,List<TravelCostStatistics>> travelCostStatisticsMap = new HashMap();
        if(!CollectionUtils.isEmpty(travelCostStatistics)){
            travelCostStatisticsMap = travelCostStatistics.stream().collect(Collectors.groupingBy(TravelCostStatistics :: getOrgCode));
        }

        Map<String,List<PlaneCostStatistics>> planeCostStatisticsMap = new HashMap();
        if(!CollectionUtils.isEmpty(planeCostStatistics)){
            planeCostStatisticsMap = planeCostStatistics.stream().collect(Collectors.groupingBy(PlaneCostStatistics :: getOrgCode));
        }

        Map<String,List<OpenCostStatistics>> openCostStatisticsMap = new HashMap();
        if(!CollectionUtils.isEmpty(openCostStatistics)){
            openCostStatisticsMap = openCostStatistics.stream().collect(Collectors.groupingBy(OpenCostStatistics :: getOrgCode));
        }

        BigDecimal contractActualTotalAmount = new BigDecimal(0);
        BigDecimal contractWorkload = new BigDecimal(0);
        BigDecimal contractJobGradeAmt = new BigDecimal(0);
        BigDecimal contractHotelAmount = new BigDecimal(0);
        BigDecimal contractTransferAmount = new BigDecimal(0);
        BigDecimal contractLogisticsAmt = new BigDecimal(0);
        BigDecimal contractMedicalExaminationAmt = new BigDecimal(0);
        BigDecimal contractArticlesAmt = new BigDecimal(0);
        BigDecimal contractRestaurantAmt = new BigDecimal(0);
        BigDecimal contractOtherAmt = new BigDecimal(0);

        for(LaborCostStatisticsVO item : deptResultList){

            String deptCode = item.getOrgCode();
            List<AttendanceSignStatistics> attendanceSignStatistics1 = attendanceSignStatisticsMap.get(deptCode);
            Map<Integer,AttendanceSignStatistics> attendanceSignStatisticsMap1 = new HashMap<>();
            if(!CollectionUtils.isEmpty(attendanceSignStatistics1)){
               attendanceSignStatisticsMap1 = attendanceSignStatistics1.stream().collect(Collectors.toMap(AttendanceSignStatistics :: getDataQuarter, o -> o));
            }

            List<TravelCostStatistics> travelCostStatistics1 = travelCostStatisticsMap.get(deptCode);
            Map<Integer,TravelCostStatistics> travelCostStatisticsMap1 = new HashMap<>();
            if(!CollectionUtils.isEmpty(travelCostStatistics1)){
                travelCostStatisticsMap1 = travelCostStatistics1.stream().collect(Collectors.toMap(TravelCostStatistics :: getDataQuarter, o -> o));
            }



            List<PlaneCostStatistics> planeCostStatistics1 = planeCostStatisticsMap.get(deptCode);
            Map<Integer,PlaneCostStatistics> planeCostStatisticsMap1 = new HashMap<>();
            if(!CollectionUtils.isEmpty(planeCostStatistics1)){
                planeCostStatisticsMap1 = planeCostStatistics1.stream().collect(Collectors.toMap(PlaneCostStatistics :: getDataQuarter, o -> o));
            }

            List<OpenCostStatistics> openCostStatistics1 = openCostStatisticsMap.get(deptCode);
            Map<Integer,OpenCostStatistics> openCostStatisticsMap1 = new HashMap<>();
            if(!CollectionUtils.isEmpty(planeCostStatistics1)){
                openCostStatisticsMap1 = openCostStatistics1.stream().collect(Collectors.toMap(OpenCostStatistics :: getDataQuarter, o -> o));
            }

            List<LaborCostAcceptance> laborCostAcceptances1 = laborCostAcceptanceMap.get(deptCode);
            Map<Integer,LaborCostAcceptance> laborCostAcceptanceMap1 = new HashMap<>();
            if(!CollectionUtils.isEmpty(laborCostAcceptances1)){
                laborCostAcceptanceMap1 = laborCostAcceptances1.stream().collect(Collectors.toMap(LaborCostAcceptance :: getAcceptanceQuarter, o -> o));
            }


            List<LaborCostStatisticsVO> quarterResultList = new ArrayList<>();
            item.setChildren(quarterResultList);
            BigDecimal deptActualTotalAmount = new BigDecimal(0);
            BigDecimal deptWorkload = new BigDecimal(0);
            BigDecimal deptJobGradeAmt = new BigDecimal(0);
            BigDecimal deptHotelAmount = new BigDecimal(0);
            BigDecimal deptTransferAmount = new BigDecimal(0);
            BigDecimal deptLogisticsAmt = new BigDecimal(0);
            BigDecimal deptMedicalExaminationAmt = new BigDecimal(0);
            BigDecimal deptArticlesAmt = new BigDecimal(0);
            BigDecimal deptRestaurantAmt = new BigDecimal(0);
            BigDecimal deptOtherAmt = new BigDecimal(0);

            for(int i = 1; i <= totalQuarter; i++){
                LaborCostStatisticsVO laborCostStatisticsVOTemp = new LaborCostStatisticsVO();
                laborCostStatisticsVOTemp.setType("quarter");
                laborCostStatisticsVOTemp.setYear(year);
                laborCostStatisticsVOTemp.setOrgCode(item.getOrgCode());
                laborCostStatisticsVOTemp.setQuarter(i);
                quarterResultList.add(laborCostStatisticsVOTemp);
                laborCostStatisticsVOTemp.setParentId(item.getId());
                laborCostStatisticsVOTemp.setId(item.getId()+i);
                String name = "";
                if(i == 1){
                    name = year+"年第一季度";
                }
                else if(i == 2){
                    name = year+"年第二季度";
                }
                else if(i == 3){
                    name = year+"年第三季度";
                }
                else if(i == 4){
                    name = year+"年第四季度";
                }
                laborCostStatisticsVOTemp.setName(name);

                LaborCostAcceptance laborCostAcceptance = laborCostAcceptanceMap1.get(i);
                if(laborCostAcceptance != null){
                    laborCostStatisticsVOTemp.setStatus(laborCostAcceptance.getStatus());
                    laborCostStatisticsVOTemp.setDataStatus(laborCostAcceptance.getDataStatus());
                    laborCostStatisticsVOTemp.setAccepttanceId(laborCostAcceptance.getId());
                }
                else{
                    DataStatusVO dataStatusVO = new DataStatusVO();
                    dataStatusVO.setName(LaborCostAcceptanceStatusEnum.UNSUBMIT.getDesc());
                    dataStatusVO.setStatusValue(LaborCostAcceptanceStatusEnum.UNSUBMIT.getKey());
                    dataStatusVO.setColor("1");
                    laborCostStatisticsVOTemp.setDataStatus(dataStatusVO);
                    laborCostStatisticsVOTemp.setStatus(LaborCostAcceptanceStatusEnum.UNSUBMIT.getKey());
                }


                AttendanceSignStatistics attendanceSignStatistics2 = attendanceSignStatisticsMap1.get(i);
                if(attendanceSignStatistics2 != null){
                    laborCostStatisticsVOTemp.setWorkload(attendanceSignStatistics2.getAttandanceRate() == null?new BigDecimal(0):attendanceSignStatistics2.getAttandanceRate());
                    laborCostStatisticsVOTemp.setJobGradeAmt(attendanceSignStatistics2.getUnitPrice() == null?new BigDecimal(0):attendanceSignStatistics2.getUnitPrice());
                }
                BigDecimal otherAmt = new BigDecimal(0);
                TravelCostStatistics travelCostStatistics2 = travelCostStatisticsMap1.get(i);
                if(travelCostStatistics2 != null){
                    laborCostStatisticsVOTemp.setHotelAmount(travelCostStatistics2.getHotelAmount()== null?new BigDecimal(0):travelCostStatistics2.getHotelAmount());
                    laborCostStatisticsVOTemp.setTransferAmount(travelCostStatistics2.getTransferAmount()== null?new BigDecimal(0):travelCostStatistics2.getTransferAmount());
                    otherAmt.add(travelCostStatistics2.getTrafficAmount()== null?new BigDecimal(0):travelCostStatistics2.getTrafficAmount());
                }

                OpenCostStatistics openCostStatistics2 = openCostStatisticsMap1.get(i);
                if(openCostStatistics2 != null){
                    laborCostStatisticsVOTemp.setLogisticsAmt(openCostStatistics2.getLogisticsAmt()== null?new BigDecimal(0):openCostStatistics2.getLogisticsAmt());
                    laborCostStatisticsVOTemp.setMedicalExaminationAmt(openCostStatistics2.getPhysicalExaminationAmt()== null?new BigDecimal(0):openCostStatistics2.getPhysicalExaminationAmt());
                    laborCostStatisticsVOTemp.setArticlesAmt(openCostStatistics2.getLaborAmt()== null?new BigDecimal(0):openCostStatistics2.getLaborAmt());
                    laborCostStatisticsVOTemp.setRestaurantAmt(openCostStatistics2.getRestaurantManagementAmt()== null?new BigDecimal(0):openCostStatistics2.getRestaurantManagementAmt());
                    otherAmt.add(openCostStatistics2.getOtherAmt()== null?new BigDecimal(0):openCostStatistics2.getOtherAmt());
                }

                PlaneCostStatistics planeCostStatistics2 = planeCostStatisticsMap1.get(i);
                if(planeCostStatistics2 != null){
                    otherAmt.add(planeCostStatistics2.getDiscountPrice()== null?new BigDecimal(0):planeCostStatistics2.getDiscountPrice());
                }
                BigDecimal actualTotalAmount = laborCostStatisticsVOTemp.getJobGradeAmt().add(laborCostStatisticsVOTemp.getHotelAmount()).add(laborCostStatisticsVOTemp.getTransferAmount())
                        .add(laborCostStatisticsVOTemp.getLogisticsAmt()).add(laborCostStatisticsVOTemp.getMedicalExaminationAmt()).add(laborCostStatisticsVOTemp.getArticlesAmt())
                        .add(laborCostStatisticsVOTemp.getRestaurantAmt()).add(otherAmt);
                laborCostStatisticsVOTemp.setActualTotalAmount(actualTotalAmount);
                deptActualTotalAmount = deptActualTotalAmount.add(actualTotalAmount);
                deptWorkload = deptWorkload.add(laborCostStatisticsVOTemp.getWorkload());
                deptJobGradeAmt = deptJobGradeAmt.add(laborCostStatisticsVOTemp.getJobGradeAmt());
                deptHotelAmount = deptHotelAmount.add(laborCostStatisticsVOTemp.getHotelAmount());
                deptTransferAmount.add(laborCostStatisticsVOTemp.getTransferAmount());
                deptTransferAmount = deptLogisticsAmt.add(laborCostStatisticsVOTemp.getLogisticsAmt());
                deptMedicalExaminationAmt = deptMedicalExaminationAmt.add(laborCostStatisticsVOTemp.getMedicalExaminationAmt());
                deptArticlesAmt = deptArticlesAmt.add(laborCostStatisticsVOTemp.getArticlesAmt());
                deptRestaurantAmt = deptRestaurantAmt.add(laborCostStatisticsVOTemp.getRestaurantAmt());
                deptOtherAmt = deptOtherAmt.add(laborCostStatisticsVOTemp.getOtherAmt());
            }
            item.setActualTotalAmount(deptActualTotalAmount);
            item.setWorkload(deptWorkload);
            item.setArticlesAmt(deptArticlesAmt);
            item.setJobGradeAmt(deptJobGradeAmt);
            item.setHotelAmount(deptHotelAmount);
            item.setTransferAmount(deptTransferAmount);
            item.setLogisticsAmt(deptLogisticsAmt);
            item.setMedicalExaminationAmt(deptMedicalExaminationAmt);
            item.setRestaurantAmt(deptRestaurantAmt);
            item.setOtherAmt(deptOtherAmt);

            contractActualTotalAmount = contractActualTotalAmount.add(deptActualTotalAmount);
            contractWorkload = contractWorkload.add(deptWorkload);
            contractJobGradeAmt = contractJobGradeAmt.add(deptJobGradeAmt);
            contractHotelAmount = contractHotelAmount.add(deptHotelAmount);
            contractTransferAmount = contractTransferAmount.add(deptTransferAmount);
            contractLogisticsAmt = contractLogisticsAmt.add(deptLogisticsAmt);
            contractMedicalExaminationAmt = contractMedicalExaminationAmt.add(deptMedicalExaminationAmt);
            contractArticlesAmt = contractArticlesAmt.add(deptArticlesAmt);
            contractRestaurantAmt = contractRestaurantAmt.add(deptRestaurantAmt);
            contractOtherAmt = contractOtherAmt.add(deptOtherAmt);
        }
        laborCostStatisticsVO.setActualTotalAmount(contractActualTotalAmount);
        laborCostStatisticsVO.setWorkload(contractWorkload);
        laborCostStatisticsVO.setArticlesAmt(contractArticlesAmt);
        laborCostStatisticsVO.setJobGradeAmt(contractJobGradeAmt);
        laborCostStatisticsVO.setHotelAmount(contractHotelAmount);
        laborCostStatisticsVO.setTransferAmount(contractTransferAmount);
        laborCostStatisticsVO.setLogisticsAmt(contractLogisticsAmt);
        laborCostStatisticsVO.setMedicalExaminationAmt(contractMedicalExaminationAmt);
        laborCostStatisticsVO.setRestaurantAmt(contractRestaurantAmt);
        laborCostStatisticsVO.setOtherAmt(contractOtherAmt);

        return laborCostStatisticsVO;
    }

    /**
     * 通过合同计划id排除合同计划状态是编制中和已失效的
     * @param contractMainIds ids
     * @return 结果
     */
    private List<ContractMain> getBeginIds(List<String> contractMainIds){

        //如果为空，则开启所有不为失效或者编制中的合同计划
        if (CollectionUtils.isEmpty(contractMainIds)){
            LambdaQueryWrapperX<ContractMain> wrapperX = new LambdaQueryWrapperX<>(ContractMain.class);
            List<Integer> statusList = new ArrayList<>();
            statusList.add(ContractPlanStatusConstant.ING.getKey());
            statusList.add(ContractPlanStatusConstant.CLOSED.getKey());

            wrapperX.apply("EXTRACT(YEAR FROM year) = {0}", Year.now());
            wrapperX.notIn(ContractMain::getStatus,statusList);

            List<ContractMain> contractMains = this.getBaseMapper().selectList(wrapperX);
            if (CollectionUtils.isEmpty(contractMains)){
                throw new BaseException(MyExceptionCode.ERROR_NOT_FOUNT_JOB.getErrorCode(),"全部合同用人计划都为正在编制或者已失效");
            }
            return contractMains;
        }else {
            LambdaQueryWrapperX<ContractMain> wrapperX = new LambdaQueryWrapperX<>(ContractMain.class);
            wrapperX.in(ContractMain::getId,contractMainIds);
            wrapperX.like(ContractMain::getYear,Year.now());
            wrapperX.select(ContractMain::getId,ContractMain::getStatus);
            List<ContractMain> preRes = list(wrapperX);
            //过滤状态
            List<ContractMain> collect = preRes.stream().filter(vo -> (!vo.getStatus().equals(ContractPlanStatusConstant.CLOSED.getKey())
                    && !vo.getStatus().equals(ContractPlanStatusConstant.ING.getKey())))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect)){
                throw new BaseException(MyExceptionCode.ERROR_NOT_FOUNT_JOB.getErrorCode(),"全部合同用人计划都为正在编制或者已失效");
            }
            return collect;
        }
    }



    @Override
    public void setEveryName(List<ContractMainVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }

    private static class TemplateExcelListener<T> extends AnalysisEventListener<T> {

        List<T> saveList = new ArrayList<>();

        ThreadLocal<List<T>> dataList = new ThreadLocal<>();

        //每读取一行内容，都会调用一次该对象的invoke，在invoke可以操作使用读取到的数据
        @Override
        public void invoke(T data, AnalysisContext context) {
            saveList.add(data);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            List<T> newList = new ArrayList<>(saveList);
            saveList.clear();
            dataList.set(newList);
        }

        public ThreadLocal<List<T>> getDataList(){
            return dataList;
        }
    }

    public static class ContractMainExcelListener extends TemplateExcelListener<ContractMainImportDTO> {
    }

    public static class ContractCenterExcelListener extends TemplateExcelListener<ContractCenterImportDTO> {
    }

    public static class AssessmentStandardExcelListener extends TemplateExcelListener<AssessmentStandardImportDTO> {
    }

    public static class SheetHeadHandler{

        private static final String TOTAL_AFTER = "-total";
        private static final String ACTUAL_AFTER = "-actual";
        private static final String NAME_TOTAL_AFTER = "预算金额";
        private static final String NAME_ACTUAL_AFTER = "实际金额";

        public static Map<String,Object> wrapperParam(Map<String,Object> param, String key, String value){
            param.put(key+NAME_TOTAL_AFTER,value+TOTAL_AFTER);
            param.put(key+NAME_ACTUAL_AFTER,value+ACTUAL_AFTER);
            return param;
        }


        public static Map<String,BigDecimal> wrapperParamDecimal(Map<String,BigDecimal> param, String key){
            param.put(key+TOTAL_AFTER,BigDecimal.valueOf(0));
            param.put(key+ACTUAL_AFTER,BigDecimal.valueOf(0));
            return param;
        }

        public static Map<String,BigDecimal> setAmount(CostVO costVO, Map<String,BigDecimal> param){
            String total = costVO.getCostType() + TOTAL_AFTER;
            String actual = costVO.getCostType() + ACTUAL_AFTER;
            BigDecimal orDefault = param.getOrDefault(total, BigDecimal.valueOf(0));
            //给预算金额赋值
            param.put(total,costVO.getTotalBudget().add(orDefault));
            //todo 给实际执行金额赋值

            return param;
        }

        public static Map<String,BigDecimal> getSheetHeads(DictRedisHelper dictRedisHelper){
            //获取表头map
            Map<String, BigDecimal> sheetHeads = new HashMap<>();
            List<DictValueVO> costType = dictRedisHelper.getByDictNumber(CostDictNumberConstant.COST_DICT_NUMBER, CurrentUserHelper.getOrgId());
            //封装表头到map中
            for (DictValueVO dictValueVO : costType) {
                String number = dictValueVO.getValue();
                wrapperParamDecimal(sheetHeads, number);
            }

            return sheetHeads;
        }
    }
}
