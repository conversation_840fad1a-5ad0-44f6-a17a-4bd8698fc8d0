package com.chinasie.orion.repository;

import com.chinasie.orion.domain.entity.CenterJobManage;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <p>
 * CenterJobManage Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-14 10:35:27
 */
@Mapper
public interface CenterJobManageMapper extends OrionBaseMapper<CenterJobManage> {

    void updateByJobNumberListMatchUp(@Param("jobNumberList") List<String> jobNumberList, @Param("status") Integer status);


    void removeNotMatchUpData(@Param("jobNumberList") List<String> jobNumberList);
}

