package com.chinasie.orion.management.xxljob;

import com.chinasie.orion.management.constant.MsgHandlerConstant;
import com.chinasie.orion.management.domain.entity.ActualPayMilestone;
import com.chinasie.orion.management.domain.entity.ContractExtendInfo;
import com.chinasie.orion.management.repository.ContractInfoMapper;
import com.chinasie.orion.management.service.ActualPayMilestoneService;
import com.chinasie.orion.msc.api.MscBuildHandlerManager;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class ContractAcceptanceXxljob {

    @Resource
    ContractInfoMapper contractInfoMapper;

    @Resource
    MscBuildHandlerManager mscBuildHandlerManager;

    @XxlJob(value = "contractAcceptanceJobHandler")
    public void acceptance(){
        List<ContractExtendInfo> contractInfo = contractInfoMapper.getContractInfo();
        contractInfo.forEach(item->{
            mscBuildHandlerManager.send(item, MsgHandlerConstant.NODE_CONTRACT_ACCEPTANCE);
        });

    }


}
