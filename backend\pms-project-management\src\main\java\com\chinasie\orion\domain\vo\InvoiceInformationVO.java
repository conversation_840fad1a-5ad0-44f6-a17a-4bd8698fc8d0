package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;

import java.util.List;

/**
 * InvoiceInformation VO对象
 *
 * <AUTHOR>
 * @since 2025-01-07 02:41:49
 */
@ApiModel(value = "InvoiceInformationVO对象", description = "发票信息")
@Data
public class InvoiceInformationVO extends ObjectVO implements Serializable {

    /**
     * 收入计划编号
     */
    @ApiModelProperty(value = "收入计划编号")
    private String incomePlanNum;


    /**
     * 关联合同id
     */
    @ApiModelProperty(value = "关联合同id")
    private String contractId;


    /**
     * 里程碑id
     */
    @ApiModelProperty(value = "里程碑id")
    private String milestoneId;


    /**
     * 里程碑名称
     */
    @ApiModelProperty(value = "里程碑名称")
    private String milestoneName;


    /**
     * 发票类型
     */
    @ApiModelProperty(value = "发票类型")
    private String invoiceType;


    /**
     * 发票号码
     */
    @ApiModelProperty(value = "发票号码")
    private String InvoiceNum;


    /**
     * 开票公司
     */
    @ApiModelProperty(value = "开票公司")
    private String billIssueCompany;


    /**
     * 发票购买方
     */
    @ApiModelProperty(value = "发票购买方")
    private String invoicePurchaser;


    /**
     * 开票日期
     */
    @ApiModelProperty(value = "开票日期")
    private Date invoiceDate;


    /**
     * 不含税金额
     */
    @ApiModelProperty(value = "不含税金额")
    private BigDecimal amtNoTax;


    /**
     * 税额
     */
    @ApiModelProperty(value = "税额")
    private BigDecimal tax;


    /**
     * 含税金额
     */
    @ApiModelProperty(value = "含税金额")
    private BigDecimal amtTax;


}
