package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * IncomeProvisionInformation VO对象
 *
 * <AUTHOR>
 * @since 2024-09-29 11:32:18
 */
@ApiModel(value = "IncomeProvisionInformationVO对象", description = "收入计提信息表")
@Data
public class IncomeProvisionInformationVO extends  ObjectVO   implements Serializable{

            /**
         * 年份
         */
        @ApiModelProperty(value = "年份")
        private String year;


        /**
         * 凭证号
         */
        @ApiModelProperty(value = "凭证号")
        private String voucherNumber;


        /**
         * 挂账金额
         */
        @ApiModelProperty(value = "挂账金额")
        private BigDecimal accruedAmt;


        /**
         * 已冲销金额
         */
        @ApiModelProperty(value = "已冲销金额")
        private BigDecimal amortizedAmt;


        /**
         * 剩余未冲销金额
         */
        @ApiModelProperty(value = "剩余未冲销金额")
        private BigDecimal remainUnamortizedAmt;


        /**
         * 本次冲销金额
         */
        @ApiModelProperty(value = "本次冲销金额")
        private BigDecimal amortizationAmount;


        /**
         * 本次冲销金额（不含税）
         */
        @ApiModelProperty(value = "本次冲销金额（不含税）")
        private BigDecimal amortizationAmtExTax;


        /**
         * 不冲销原因
         */
        @ApiModelProperty(value = "不冲销原因")
        private String noAmortizationReason;


        /**
         * 收入填报Id
         */
        @ApiModelProperty(value = "收入填报Id")
        private String incomePlanId;


        /**
         * 收入填报数据ID
         */
        @ApiModelProperty(value = "收入填报数据ID")
        private String incomePlanDataId;


    

}
