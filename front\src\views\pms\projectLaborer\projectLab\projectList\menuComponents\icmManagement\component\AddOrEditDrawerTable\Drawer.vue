<template>
  <BasicDrawer
    destroyOnClose
    showFooter
    :width="1000"
    :title="state.title"
    @register="modalRegister"
    @visible-change="visibleChange"
    @ok="confirm"
  >
    <ModalForm ref="formModalRef" />
  </BasicDrawer>
</template>

<script setup lang="ts">
import {
  defineProps, reactive, defineExpose, defineEmits, ref,
} from 'vue';
import { BasicDrawer, useDrawer } from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import { useRouter } from 'vue-router';
import ModalForm from './ModalForm.vue';
import Api from '/@/api';
import { useUserStore } from '/@/store/modules/user';

const userStore = useUserStore();

const [modalRegister, modalMethods] = useDrawer();
const emit = defineEmits(['update']);

function initData() {
  return {
    action: 'add',
    title: '',
    originData: {},
  };
}

const state = reactive(initData());
const formModalRef = ref(null);

function visibleChange(show) {
  !show && Object.assign(state, initData());
}

interface openModalTypes {
    action: String;// add  edit 等
    info?: any
}

function openDrawer(data: openModalTypes) {
  modalMethods.openDrawer(true);
  data && usualHandle(data);
  if (data.action === 'add') {
    setTimeout(() => {
      if (userStore.getUserInfo?.simpleUser?.orgId) {
        formModalRef.value.FormMethods.setFieldsValue({ publishDeptId: userStore.getUserInfo?.simpleUser?.orgId });
      }
    });
  }
  if (data.action === 'edit') {
    state.originData.record.reviewDeptIds = state.originData.record.reviewDeptIds.split(',');
    state.originData.record.reviewDeptIdList = state.originData.record.reviewDeptIds;
    let length = state.originData.record.cooperationUserIdList?.length ?? 0;
    let arr: any = [];
    if (length) {
      let data = state.originData.record.cooperationUserNames.split(',');
      arr = state.originData.record.cooperationUserIdList.map((item, index) => ({
        label: data[index],
        value: item,
      }));
    }

    setTimeout(() => {
      formModalRef.value.setOptions([
        {
          label: state.originData.record.manUserName,
          value: state.originData.record.manUser,
        },
      ], arr);
      formModalRef.value.FormMethods.setFieldsValue(state.originData?.record);
    });
  }
}

function usualHandle(data) {
  data?.action && (state.action = data?.action);
  data?.action === 'add' && (state.title = '新增');
  data?.action === 'edit' && (state.title = '编辑');
  if (data?.info) {
    state.originData = JSON.parse(JSON.stringify(data.info));
  }
}

const router = useRouter();

async function confirm() {
  await formModalRef.value && await formModalRef.value.FormMethods.validate();
  modalMethods.setDrawerProps({ confirmLoading: true });
  try {
    const res = await goFetch();
    message.success('操作成功');
    emit('update');
    formModalRef.value && formModalRef.value.FormMethods.resetFields();
    //   跳转详情
    if (state.action === 'add') {
      router.push({
        name: 'IcmManagementDetailsIndex',
        params: {
          id: res?.id,
        },
      });
    }
    modalMethods.openDrawer(false);
  } catch (_) {
  } finally {
    modalMethods.setDrawerProps({ confirmLoading: false });
  }
}

async function goFetch() {
  const formData = formModalRef.value && formModalRef.value.FormMethods.getFieldsValue();
  const params = JSON.parse(JSON.stringify(formData));
  if (params?.replyTime) {
    params.replyTime = dayjs(params?.replyTime).format('YYYY-MM-DD');
  }
  // params.cooperationUsers = 'as4f0cab09530b914568ac05c185f2627d57';
  params.projectId = state.originData?.projectId;
  if (state.action === 'add') {
    return await new Api('/pms/interface-management').fetch(params, '', 'POST');
  }
  if (state.action === 'edit') {
    params.id = state.originData.record.id;
    return await new Api('/pms/interface-management').fetch(params, '', 'PUT');
  }
}

defineExpose({
  modalMethods,
  openDrawer,
});
</script>

<style scoped lang="less">

</style>
