<script setup lang="ts">
import { BasicCard, OrionTable } from 'lyra-component-vue3';
import { reactive, ref, Ref } from 'vue';
import ContractBasicInfo
  from '../common/ContractBasicInfo.vue';
import { useContractPlanDetail } from '../../hooks/useContractPlanDetail';
import { get, map } from 'lodash-es';
import ContractEmploymentPlan from '../common/ContractEmploymentPlan.vue';
import Api from '/@/api';

const props = defineProps({
  // eslint-disable-next-line vue/require-default-prop,vue/require-prop-types
  record: {},
});
const tableRef = ref();
const { basicContractEmployerPlan } = useContractPlanDetail(get(props, 'record.contractNumber'));

defineExpose({
  onSubmit() {
    const contractEmploymentPlanData = tableRef.value?.exportTableData();
    const bodyParams = map(contractEmploymentPlanData, (item) => ({
      status: item.status,
      year: new Date(`${get(basicContractEmployerPlan, 'year')}-01-01`),
      id: get(item, 'id'),
      num: item.num,
      beforeNum: item.beforeNum,
      contractNumber: item.contractNumber,
      centerName: get(props, 'record.centerName'),
    }));
    return new Promise((resolve, reject) => {
      new Api('/spm/contractCenterPlan/edit').fetch(bodyParams, '', 'PUT').then(() => {
        resolve('');
      }).catch((err) => {
        reject(err);
      });
    });
  },
});

</script>

<template>
  <ContractBasicInfo />
  <BasicCard
    title="合同用人计划"
    :isBorder="false"
  >
    <ContractEmploymentPlan
      ref="tableRef"
      :is-editable-num="true"
    />
  </BasicCard>
</template>

<style scoped lang="less">
.contract-employment-plan{
  overflow: auto;
  min-height: 120px;
  max-height: 500px;
  :deep(.ant-basic-table){
    &.default-spacing{
      padding: 0;
    }
  }
}
</style>