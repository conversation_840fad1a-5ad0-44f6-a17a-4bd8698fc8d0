<script setup lang="ts">
import { BasicDrawer, useDrawerInner } from 'lyra-component-vue3';
import {
  inject,
  onUnmounted, provide, readonly, ref, Ref, unref,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { message, Spin } from 'ant-design-vue';
import FormMain from './FormMain.vue';
import Api from '/@/api';
import { declarationDataId } from '/@/views/pms/personalWorkbench/projectCenter/projectInitiation/pages/keys';

const emits = defineEmits<{
  (e: 'confirmCallback', result: any): void
}>();

/**
 * @param openProps
 * @param openProps.operationType   值为:fixed 代表此次打开抽屉操作携带部分固定参数，不可更改
 */
const [register, { setDrawerProps, changeOkLoading, closeDrawer }] = useDrawerInner(async (openProps) => {
  detail.value = openProps;
  if (openProps.id) {
    setDrawerProps({
      title: '编辑节点',
    });
    await getDetail();
  } else {
    setDrawerProps({
      title: '新建节点',
    });
  }
  setDrawerProps({
    showFooter: true,
  });
  visibleDrawer.value = true;
});
const route = useRoute();
const router = useRouter();
const formRef: Ref = ref();
const detail: Ref = ref();
provide('detail', readonly(detail));
const loading: Ref<boolean> = ref(false);
const visibleDrawer: Ref<boolean> = ref(false);
const dataId: Ref = inject(declarationDataId);
async function getDetail() {
  loading.value = true;
  try {
    const result = await new Api('/pms/projectApprovalMilestone').fetch('', detail.value.id, 'GET');
    const materialList = await new Api('/res/manage/file/listByIds').fetch([detail.value.id], '', 'POST');
    result.materialList = materialList;
    detail.value = result || {};
  } finally {
    loading.value = false;
  }
}

function visibleChange(visible: boolean) {
  if (!visible) {
    visibleDrawer.value = visible;
    setDrawerProps({
      showFooter: visible,
    });
  }
}

async function onOk() {
  const formValues = await formRef.value.validate();
  const params = {
    ...formValues,
    approvalId: unref(dataId), // 立项id
    projectId: route.query.projectId, // 项目id
    id: detail.value.id, // 弹框中选中项id
    attachments: formRef.value.getTableData(), // 附件数组
    resPerson: formRef.value.selectUser?.id, // 责任人
    beginTime: formValues.beginTime ? dayjs(formValues.beginTime).format('YYYY-MM-DD') : '',
    endTime: formValues.endTime ? dayjs(formValues.endTime).format('YYYY-MM-DD') : '',
  };
  delete params.resPersonName;
  changeOkLoading(true);
  try {
    const result = await new Api('/pms/projectApprovalMilestone').fetch(params, '', detail.value.id ? 'PUT' : 'POST');
    closeDrawer();
    emits('confirmCallback', result);
    // 如果是项目内新增则成功新增后跳转到详情
    if (detail.value?.type === 'goDetails') {
      router.push({
        name: 'ProjectInitiationDetail',
        params: {
          id: result.id,
        },
      });
    }
  } finally {
    changeOkLoading(false);
  }
}
</script>

<template>
  <BasicDrawer
    v-bind="$attrs"
    width="800px"
    :onVisibleChange="visibleChange"
    @register="register"
    @ok="onOk"
  >
    <div
      v-if="loading"
      class="w-full h-full flex flex-ac flex-pc"
    >
      <Spin />
    </div>

    <FormMain
      v-else-if="visibleDrawer"
      ref="formRef"
      :detail="detail"
    />
  </BasicDrawer>
</template>

<style scoped lang="less">

</style>
