package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * RankingVO
 *
 * <AUTHOR>
 * @since 2024-06-18 17:49:18
 */
@ApiModel(value = "OverhaulVO", description = "多基地大修准备及实施状态")
@Data
public class OverhaulVO extends ObjectVO implements Serializable {

    /**
     * 实施状态
     */
    @ApiModelProperty(value = "实施状态")
    private String overhaulStatus;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private String figure;

    /**
     * 占比
     */
    @ApiModelProperty(value = "占比")
    private String ratio;
}
