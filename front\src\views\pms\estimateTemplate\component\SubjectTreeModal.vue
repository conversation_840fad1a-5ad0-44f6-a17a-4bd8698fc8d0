<template>
  <div class="subject-table">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      @initData="initData"
    />
  </div>
</template>
<script lang="ts" setup>
import {
  computed, h, Ref, ref,
} from 'vue';
import dayjs from 'dayjs';
import { OrionTable } from 'lyra-component-vue3';
import Api from '/@/api';

const tableRef = ref();
const selectRowKeys:Ref<string[]> = ref([]);
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: {
    selectedRowKeys: computed(() => selectRowKeys.value),
    onSelect: (record, selected: boolean, selectedRows) => {
      if (!selected) {
        let unselectedId = [record.id];
        if (Array.isArray(record.children)) {
          unselectedId = unselectedId.concat(findSelect(record.children));
        }
        selectRowKeys.value = selectRowKeys.value.filter((item) => !unselectedId.includes(item));
      } else {
        let ids = [];
        if (Array.isArray(record.children)) {
          ids = ids.concat(findSelect(record.children));
        }
        if (record.parentId !== '0') {
          let tableData = tableRef.value.getDataSource();
          let parentData = findParent(tableData, record.id);
          let parentIds = parentData.map((item) => item.id);
          ids = parentIds.concat(ids);
        } else {
          ids = [record.id].concat(ids);
        }
        ids.forEach((item) => {
          if (!selectRowKeys.value.includes(item)) {
            selectRowKeys.value.push(item);
          }
        });
      }
    },
    onSelectAll: (selected: boolean, selectedRows, changeRows) => {
      if (selected) {
        selectRowKeys.value = selectedRows.map((item) => item.id);
      } else {
        selectRowKeys.value = [];
      }
    },
  },
  showIndexColumn: false,
  expandIconColumnIndex: 4,
  showTableSetting: false,
  api: (params) => new Api('/pms').fetch(params, 'expenseSubject/tree/page', 'POST'),
  columns: [

    {
      title: '科目序号',
      dataIndex: 'index',
      align: 'left',
      width: 100,
    },
    {
      title: '科目编码',
      dataIndex: 'number',
      width: 150,
    },
    {
      title: '科目名称',
      dataIndex: 'name',
      minWidth: 150,
    },
  ],
  //  beforeFetch,
});
function findSelect(data) {
  let ids = [];
  data.forEach((item) => {
    ids.push(item.id);
    if (Array.isArray(item.children)) {
      ids = ids.concat(findSelect(item.children));
    }
  });
  return ids;
}
function findParent(data, id) {
  let parentData = [];
  for (let i = 0; i < data.length; i++) {
    let item = data[i];
    if (item.id === id) {
      return [item];
    }
    if (Array.isArray(item.children)) {
      let newParentData = findParent(item.children, id);
      if (newParentData.length > 0) {
        parentData = [item].concat(newParentData);
      }
    }
  }
  return parentData;
}
function initData(data:any[], indexColumns = '') {
  data.forEach((item, index) => {
    if (indexColumns) {
      item.index = `${indexColumns}.${index + 1}`;
    } else {
      item.index = index + 1;
    }
    if (Array.isArray(item.children)) {
      initData(item.children, item.index);
    }
  });
}
defineExpose({
  getSelectData,
});
function getSelectData() {
  if (selectRowKeys.value.length === 0) {
    return [];
  }
  let selectData = [];
  let tableData = tableRef.value.getDataSource();
  selectRowKeys.value.forEach((item) => {
    let record = findNodeById(item, tableData);
    if (record) {
      selectData.push(record);
    }
  });
  return selectData;
}
function findNodeById(nodeId, nodes) {
  for (const node of nodes) {
    if (node.id === nodeId) {
      return node; // 找到匹配的节点
    }
    if (node.children && node.children.length > 0) {
      const foundNode = findNodeById(nodeId, node.children);
      if (foundNode) {
        return foundNode; // 在子节点中找到匹配的节点
      }
    }
  }
  return null; // 没有找到匹配的节点
}
</script>
<style lang="less" scoped>
.subject-table{
  height: 100%;
  overflow: hidden;
}
</style>