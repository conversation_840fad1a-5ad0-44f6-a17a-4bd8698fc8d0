package com.chinasie.orion.schedule;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.constant.ProjectContractPayNodeStatusEnum;
import com.chinasie.orion.domain.entity.ContractPayNode;
import com.chinasie.orion.service.ContractPayNodeService;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;

/**
 * @author: yk
 * @date: 2023/10/28 14:42
 * @description: 更改项目合同支付节点状态为待支付
 */
@Component
public class ProjectContractPayNodeChangeStatusXxlJob {

    @Autowired
    private ContractPayNodeService ContractPayNodeService;


    @XxlJob("projectContractPayNodeChangeStatus")
    public void changeStatus() throws Exception {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        LambdaUpdateWrapper<ContractPayNode> lambdaQueryWrapper = new LambdaUpdateWrapper<>();
        lambdaQueryWrapper.eq(ContractPayNode::getStatus, ProjectContractPayNodeStatusEnum.CREATED.getStatus());
        lambdaQueryWrapper.lt(ContractPayNode::getInitPlanPayDate, calendar.getTime());
        lambdaQueryWrapper.set(ContractPayNode::getStatus, ProjectContractPayNodeStatusEnum.PAYING.getStatus());
        ContractPayNodeService.update(lambdaQueryWrapper);
    }
}
