package com.chinasie.orion.domain.vo.projectOverviewZgh;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 项目毛利
 */
@Data
@ApiModel(value = "ProjectGrossProfitVO", description = "项目毛利")
public class ProjectGrossProfitVO {

    @ApiModelProperty("计划")
    private BigDecimal plan= BigDecimal.ZERO;

    @ApiModelProperty("计划")
    private BigDecimal planRate= BigDecimal.ZERO;

    @ApiModelProperty("计划较近10天")
    private BigDecimal planTenPercent= BigDecimal.ZERO;

    @ApiModelProperty("实际")
    private BigDecimal practice= BigDecimal.ZERO;

    @ApiModelProperty("计划")
    private BigDecimal practiceRate= BigDecimal.ZERO;

    @ApiModelProperty("实际较近10天")
    private BigDecimal practiceTenPercent= BigDecimal.ZERO;
}
