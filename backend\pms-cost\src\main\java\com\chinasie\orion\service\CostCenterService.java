package com.chinasie.orion.service;

import java.lang.String;
import java.util.List;
import java.util.Map;

import com.chinasie.orion.domain.dto.CostCenterDTO;
import com.chinasie.orion.domain.entity.CostCenter;
import com.chinasie.orion.domain.vo.CostCenterVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

/**
 * <p>
 * CostCenter 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16 13:29:31
 */
public interface CostCenterService extends OrionBaseService<CostCenter> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    CostCenterVO detail(String id) throws Exception;

    /**
     * 新增
     * <p>
     * * @param costCenterDTO
     */
    CostCenterVO create(CostCenterDTO costCenterDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param costCenterDTO
     */
    Boolean edit(CostCenterDTO costCenterDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean removeById(String id) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<CostCenterVO> pages(Page<CostCenterDTO> pageRequest) throws Exception;

    /**
     * 禁用
     *
     * @param id
     * @return
     */
    Boolean ban(String id) throws Exception;

    /**
     * 启用
     *
     * @param id
     * @return
     */
    Boolean use(String id) throws Exception;

    /**
     * 列表
     * <p>
     * * @param id
     */
    List<CostCenterVO> getCostCenterList(CostCenterDTO costCenterDTO) throws Exception;

    /**
     * 通过IDs查询成本中心信息
     *
     * @param costCenterIds 成本中心IDS
     * @return map
     * @throws Exception e
     */
    Map<String, CostCenter> getCosCenterMap(List<String> costCenterIds) throws Exception;

}

