<script setup lang="ts">
import {
  computed, ComputedRef, inject, onMounted, ref, Ref, watch,
} from 'vue';
import { Icon } from 'lyra-component-vue3';
import { useChart } from './useChart';
import Api from '/@/api';
import EmptyView from '/@/views/pms/components/EmptyView.vue';
import SpinView from '/@/views/pms/components/SpinView.vue';

const loading: Ref<boolean> = ref(false);
const projectId: string = inject('projectId');
const evolveList: Ref<any[]> = ref([]);
const evolveListObj = ref({});

const dataOptions: ComputedRef<any[]> = computed(() => [
  {
    color: '#61A5E8',
    name: '进行中计划',
    type: 'bar',
    stack: 'Ad',
    emphasis: {
      focus: 'series',
    },
    data: evolveList.value[0]?.doing,
  },
  {
    color: '#FF0000',
    name: '已逾期计划',
    type: 'bar',
    stack: 'Ad',
    emphasis: {
      focus: 'series',
    },
    data: evolveList.value[0]?.overdue,
  },
  {
    color: '#7ECF51',
    name: '已完成计划',
    type: 'bar',
    stack: 'Ad',
    emphasis: {
      focus: 'series',
    },
    data: evolveList.value[0]?.done,
  },
]);

const chartOption = computed(() => ({
  color: [
    '#61A5E8',
    '#FF0000',
    '#7ECF51',
  ],
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
  },
  grid: {
    top: 10,
    left: 20,
    right: 20,
    bottom: 0,
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: evolveList.value[0]?.items?.map((item) => item.memberName),
  },
  yAxis: [
    {
      type: 'value',
    },
  ],
  series: [
    {
      name: '进行中计划',
      type: 'bar',
      stack: 'Ad',
      emphasis: {
        focus: 'series',
      },
      data: evolveListObj.value?.items?.map((item) => item.doing),
    },
    {
      name: '已逾期计划',
      type: 'bar',
      stack: 'Ad',
      emphasis: {
        focus: 'series',
      },
      data: evolveListObj.value?.items?.map((item) => item.overdue),
    },
    {
      name: '已完成计划',
      type: 'bar',
      stack: 'Ad',
      emphasis: {
        focus: 'series',
      },
      data: evolveListObj.value?.items?.map((item) => item.done),
    },
  ],
}));

const chartRef: Ref = ref();

const chartInstance = useChart(chartRef);

watch(() => chartOption.value, (value) => {
  if (evolveList.value.length) {
    chartInstance.value.setOption(value);
  }
});

onMounted(() => {
  getEvolveList();
  chartInstance.value.setOption(chartOption.value);
});

// 获取计划进展趋势列表
async function getEvolveList() {
  loading.value = true;
  try {
    const result = await new Api(`/pms/projectOverview/zgh/projectPlanMemberCount/${projectId}`).fetch({
    }, '', 'GET');
    evolveList.value.push(result);
    evolveListObj.value = result;
  } finally {
    setTimeout(() => {
      loading.value = false;
    }, 2000);
  }
}
</script>

<template>
  <div class="flex flex-ac mr10">
    <spin-view
      v-if="loading"
      class="project-scheme-chart"
    />
    <empty-view
      v-show="!loading && evolveList.length===0"
      class="project-scheme-chart"
    />
    <div
      v-show="true"
      ref="chartRef"
      class="project-scheme-chart"
    />

    <div class="container-question">
      <div class="container-flex-wrap">
        <div class="flex-wrap">
          <div
            class="circle-flex-item"
            title="进行中计划"
          >
            <div class="circle info">
              <Icon icon="orion-icon-filesync" />
            </div>
            <div>
              <div class="value">
                {{ dataOptions[0]?.data || 0 }}
              </div>
              <span>进行中计划</span>
            </div>
          </div>
          <div
            class="circle-flex-item"
            title="已逾期计划"
          >
            <div class="circle error-self">
              <Icon icon="orion-icon-file-exception delay-color" />
            </div>
            <div>
              <div class="value">
                {{ dataOptions[1].data || 0 }}
              </div>
              <span>已逾期计划</span>
            </div>
          </div>
          <div
            class="circle-flex-item"
            title="已完成计划"
          >
            <div class="circle circle_define">
              <Icon icon="sie-icon-zhishiwendaguanli" />
            </div>
            <div>
              <div class="value">
                {{ dataOptions[2].data || 0 }}
              </div>
              <span>已完成计划</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.container-question{
  position: absolute;
  right: 0;
  top: -16px;
  width: 380px;
  .container-flex-wrap {
    .flex-wrap {
      display: flex;
      align-items: center;
      justify-content: space-between ;
      .circle-flex-item{
        width: 100%;
      }
      > div {
        flex: 1;
      }
      .circle_define{
        background-color:#34D77B ;
        color: #fff;
      }
    }
  }
}
.error-self{
  background-color: #FF0000;
}
:deep(.delay-color){
  color: #fff;
}
.project-scheme-chart{
  position: relative;
}
</style>
