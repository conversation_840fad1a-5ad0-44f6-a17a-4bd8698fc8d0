package com.chinasie.orion.domain.dto.pas;


import com.chinasie.orion.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * DocTypeAttrValueDTO 文档类型属性值DTO对象
 *
 * <AUTHOR> sie
 * @since 2022-08-22
 */
@ApiModel(value = "EcrAttrValueDTO对象", description = "文档类型属性值")
public class EcrAttrValueDTO extends ObjectDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 属性主键
     */
    @ApiModelProperty(value = "属性主键")
    private String attrId;

    /**
     * 分类主键
     */
    @ApiModelProperty(value = "分类主键")
    private String typeId;

    /**
     * 变更申请主键
     */
    @ApiModelProperty(value = "变更申请主键")
    private String ecrId;


    /**
     * 属性值
     */
    @ApiModelProperty(value = "属性值")
    private String value;

    @Override
    public String getId(){
        return id;
    }

    @Override
    public void setId(String id) {
        this.id = id;
    }

    public String getAttrId(){
        return attrId;
    }

    public void setAttrId(String attrId) {
        this.attrId = attrId;
    }

    public String getTypeId(){
        return typeId;
    }

    public void setTypeId(String typeId) {
        this.typeId = typeId;
    }

    public String getValue(){
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getEcrId() {
        return ecrId;
    }

    public void setEcrId(String ecrId) {
        this.ecrId = ecrId;
    }
}
