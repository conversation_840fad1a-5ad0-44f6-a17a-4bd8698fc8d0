package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.DeliverGoalsDTO;
import com.chinasie.orion.domain.vo.DeliverGoalsVO;
import com.chinasie.orion.service.DeliverGoalsService;
import com.chinasie.orion.service.DeliverGoalsToDeliverableService;
import com.mzt.logapi.starter.annotation.LogRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
/**
 * <p>
 * DeliverGoals 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29 13:50:13
 */
@RestController
@RequestMapping("/deliverGoals")
@Api(tags = "交付目标（ied）")
public class DeliverGoalsController {

    @Autowired
    private DeliverGoalsService deliverGoalsService;

    @Autowired
    private DeliverGoalsToDeliverableService deliverGoalsToDeliverableService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查询【交付目标详情】", type = "交付目标", bizNo = "{{#id}}")
    public ResponseDTO<DeliverGoalsVO> detail(@PathVariable(value = "id") String id) throws Exception {
        DeliverGoalsVO rsp = deliverGoalsService.detail(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param deliverGoalsDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【交付目标】", type = "交付目标", bizNo = "{{#deliverGoalsDTO.name}}")
    public ResponseDTO<String> create(@RequestBody DeliverGoalsDTO deliverGoalsDTO) throws Exception {
        String rsp =  deliverGoalsService.create(deliverGoalsDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param deliverGoalsDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【交付目标】", type = "交付目标", bizNo = "{{#deliverGoalsDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  DeliverGoalsDTO deliverGoalsDTO) throws Exception {
        Boolean rsp = deliverGoalsService.edit(deliverGoalsDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【交付目标】", type = "交付目标", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = deliverGoalsService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【交付目标分页】", type = "交付目标", bizNo = "")
    public ResponseDTO<Page<DeliverGoalsVO>> pages(@RequestBody Page<DeliverGoalsDTO> pageRequest) throws Exception {
        Page<DeliverGoalsVO> rsp =  deliverGoalsService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 关联交付物
     * @param deliverGoalsId
     * @param deliverIdList
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "关联交付物")
    @RequestMapping(value = "/relation/deliverable", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】新增【交付目标关联交付物】", type = "交付目标", bizNo = "{{#deliverGoalsId}}{{#deliverIdList.toString()}}")
    public ResponseDTO<Boolean> saveRelation(@RequestParam("deliverGoalsId") String deliverGoalsId, @RequestBody List<String> deliverIdList) throws Exception {
        Boolean rsp =  deliverGoalsToDeliverableService.saveRelation(deliverGoalsId, deliverIdList);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 删除交付物关联关系
     * @param deliverGoalsId
     * @param deliverIdList
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除交付物关联关系")
    @RequestMapping(value = "/relation/deliverable", method = RequestMethod.DELETE)
    @LogRecord(success = "【{USER{#logUserId}}】删除【交付目标关联交付物】", type = "交付目标", bizNo = "{{#deliverGoalsId}}{{#deliverIdList.toString()}}")
    public ResponseDTO<Boolean> deleteRelation(@RequestParam("deliverGoalsId") String deliverGoalsId, @RequestBody List<String> deliverIdList) throws Exception {
        Boolean rsp =  deliverGoalsToDeliverableService.deleteRelation(deliverGoalsId, deliverIdList);
        return new ResponseDTO<>(rsp);
    }
}
