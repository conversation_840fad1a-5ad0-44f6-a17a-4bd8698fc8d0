package com.chinasie.orion.service;

import com.chinasie.orion.domain.entity.ObjectEntity;
import com.chinasie.orion.mybatis.service.OrionBaseService;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/06/10:39
 * @description:
 */
public interface ObjectEntityService extends OrionBaseService<ObjectEntity> {

    /**
     *  获取数据名称
     * @param id
     * @return
     */
    String getName(String id) throws Exception;
}
