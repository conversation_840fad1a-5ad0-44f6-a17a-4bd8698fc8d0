<template>
  <Icon
    :icon="icon"
    :color="color"
    :size="size"
  />
</template>

<script>
// import { Icon } from '/@/components/IconSelect';
import { reactive, toRefs, computed } from 'vue';
import {
  Icon,
} from 'lyra-component-vue3';
export default {
  name: 'FileIcon',
  components: { Icon },
  props: {
    filePostfix: {
      type: String,
      default: '.txt',
    },
    size: {
      type: [Number, String],
      default: 14,
    },
  },
  setup(props) {
    const state = reactive({
      icon: computed(() => (props.filePostfix
        ? state.fileType[props.filePostfix].icon
        : state.fileType['.txt'].icon)),
      color: computed(() => (props.filePostfix
        ? state.fileType[props.filePostfix].color
        : state.fileType['.txt'].color)),
      fileType: {
        '.docx': {
          color: '#0e6cb7',
          icon: 'fa-file-word-o',
        },
        '.doc': {
          color: '#0e6cb7',
          icon: 'fa-file-word-o',
        },
        '.pdf': {
          color: '#ff5722',
          icon: 'fa-file-pdf-o',
        },
        '.zip': {
          color: '#673ab7',
          icon: 'fa-file-zip-o',
        },
        '.xlsx': {
          color: '#009688',
          icon: 'fa-file-excel-o',
        },
        '.xls': {
          color: '#009688',
          icon: 'fa-file-excel-o',
        },
        '.video': {
          color: '#00bcd4',
          icon: 'fa-file-video-o',
        },
        '.txt': {
          color: '#607d8b',
          icon: 'fa-file-text-o',
        },
        '.jpg': {
          color: '#607d8b',
          icon: 'fa-file-text-o',
        },
        '.png': {
          color: '#607d8b',
          icon: 'fa-file-text-o',
        },
      },
    });
    return {
      ...toRefs(state),
    };
  },
};
</script>

<style scoped></style>
