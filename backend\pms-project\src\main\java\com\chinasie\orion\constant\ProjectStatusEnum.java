package com.chinasie.orion.constant;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/1/7 9:23
 * @description:
 */
public enum ProjectStatusEnum {
    CHANGE(0, "变更"),
    CREATE(101,"已创建"),
    PENDING(110, "待立项"),
    APPROVED(120, "已立项"),
    EXECUTION(121, "执行中"),
    ACCEPTED(130, "已验收"),
    CLOSE(140, "已关闭"),
    PAUSED(170, "已暂停"),
    TERMINATED(180, "已终止"),
    END(160, "项目结项");
    private final Integer status;
    private final String desc;

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    ProjectStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static String getStringByStatus(Integer status) {
        if (APPROVED.getStatus().equals(status)) {
            return APPROVED.getDesc();
        }
        if (ACCEPTED.getStatus().equals(status)) {
            return ACCEPTED.getDesc();
        }
        if (CLOSE.getStatus().equals(status)) {
            return CLOSE.getDesc();
        }
        return "";
    }
}
