package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

/**
 * ProjectPurchaseReceiveInfo Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-06 09:16:22
 */
@ApiModel(value = "ProjectPurchaseReceiveInfoVO对象", description = "项目采购收货方信息")
@Data
public class ProjectPurchaseReceiveInfoVO extends ObjectVO implements Serializable{

    /**
     * 收货人姓名
     */
    @ApiModelProperty(value = "收货人姓名")
    private String receivePerson;

    /**
     * 收货人电话
     */
    @ApiModelProperty(value = "收货人电话")
    private String receivePhone;

    /**
     * 收货地址
     */
    @ApiModelProperty(value = "收货地址")
    private String receiveAddress;

    /**
     * 收货人邮箱
     */
    @ApiModelProperty(value = "收货人邮箱")
    private String receiveEmail;

    /**
     * 采购单id
     */
    @ApiModelProperty(value = "采购单id")
    private String purchaseId;

}
