package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSONObject;
import com.chinasie.orion.constant.AmpereRingBoardConfigConstant;
import com.chinasie.orion.domain.dto.AmpereRingBoardConfigDeptDTO;
import com.chinasie.orion.domain.dto.AmpereRingBoardConfigKpiDTO;
import com.chinasie.orion.domain.dto.JobHeightRiskDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.repository.*;
import com.chinasie.orion.service.AmpereRingBoardStatisticsService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 22 日
 **/
@Service
@Slf4j
public class AmpereRingBoardStatisticsServiceImpl implements AmpereRingBoardStatisticsService {

    @Autowired
    private AmpereRingEventCheckDataInfoMapper ampereRingEventCheckDataInfoMapper;

    @Autowired
    private AmpereRingBoardConfigKpiMapper ampereRingBoardConfigKpiMapper;

    @Autowired
    private AmpereRingBoardConfigDeptMapper configDeptMapper;

    @Autowired
    private AmpereRingKpiScoreMapper ampereRingKpiScoreMapper;

    @Autowired
    private JobHeightRiskMapper jobHeightRiskMapper;

    @Autowired
    private AmpereRingBoardConfigJobMapper ampereRingBoardConfigJobMapper;

    @Autowired
    private AmpereRingBoardConfigDeptMapper ampereRingBoardConfigDeptMapper;

    /**
     * 统计考核kpi
     *
     *统计每年发生的各类事件等级的数量
     * 集团考核总数：计算集团考核的指标的数值之和（只计算显示在看板中的指标）
     * 公司管控：计算公司管控的指标的数值之和（只计算显示在看板中的指标）
     * 公司监控：计算公司监控的指标的数值之和（只计算显示在看板中的指标）
     * @return
     */
    @Override
    public Map<String,List<AmpereRingBoardConfigKpiDTO>> queryKpi(AmpereRingBoardConfigKpiDTO kpiDto) {
        //获取 看板的统计的指标
        LambdaQueryWrapperX<AmpereRingBoardConfigKpi> kpiLambdaQueryWrapperX=new LambdaQueryWrapperX<>();
        kpiLambdaQueryWrapperX.isNotNull(AmpereRingBoardConfigKpi::getKpiCode);
        //kpiLambdaQueryWrapperX.eq(AmpereRingBoardConfigKpi::getLogicStatus,1);

        List<AmpereRingBoardConfigKpi> configKpiList = ampereRingBoardConfigKpiMapper.selectList(kpiLambdaQueryWrapperX);
        if(CollectionUtils.isEmpty(configKpiList)){
            return Maps.newHashMap();
        }
        List<AmpereRingBoardConfigKpiDTO> kpiDTOS = BeanCopyUtils.convertListTo(configKpiList, AmpereRingBoardConfigKpiDTO::new);
        Map<String, List<AmpereRingBoardConfigKpiDTO>> kpiMaps = kpiDTOS.stream().collect(Collectors.groupingBy(AmpereRingBoardConfigKpiDTO::getKpiCode));

        //统计的考核指标。
        List<String> eventLevels = configKpiList.stream().map(vo -> vo.getEventLevel()).collect(Collectors.toList());
        LambdaQueryWrapperX<AmpereRingEventCheckDataInfo> dataQueryWrapper=new LambdaQueryWrapperX<>();
        //排查 环境加分,安全加分,质量加分
        dataQueryWrapper.notIn(AmpereRingEventCheckDataInfo::getEventType,Arrays.asList("质量加分","安全加分","环境加分"));
        dataQueryWrapper.like(AmpereRingEventCheckDataInfo::getEventDate,kpiDto.getYear());
        //模糊查询
//        if(CollectionUtil.isNotEmpty(eventLevels)){
//            dataQueryWrapper.and(w->{
//              //  w.in(AmpereRingEventCheckDataInfo::getEventLevel,eventLevels);
//                w.or(q->{
//                    for(int i=0;i<eventLevels.size();i++){
//                        if(i == eventLevels.size()-1){
//                            w.like(AmpereRingEventCheckDataInfo::getEventLevel, eventLevels.get(i));
//                        }
//                        else{
//                            w.or();
//                            w.like(AmpereRingEventCheckDataInfo::getEventLevel, eventLevels.get(i));
//                        }
//                    }
//                });
//            });
//        }
        dataQueryWrapper.like(AmpereRingEventCheckDataInfo::getEventDate,kpiDto.getYear());
        if(CollectionUtil.isNotEmpty(eventLevels)){
            dataQueryWrapper.and(w->{
              //  w.in(AmpereRingEventCheckDataInfo::getEventLevel,eventLevels);
                    for(int i=0;i<eventLevels.size();i++){
                        w.or().like(AmpereRingEventCheckDataInfo::getEventLevel, eventLevels.get(i));
                    }
            });
        }

        List<AmpereRingEventCheckDataInfo> dataInfos = ampereRingEventCheckDataInfoMapper.selectList(dataQueryWrapper);
        Map<String, List<AmpereRingEventCheckDataInfo>> dataInfoMaps = dataInfos.stream().collect(Collectors.groupingBy(AmpereRingEventCheckDataInfo::getEventLevel));
        Map<String,List<AmpereRingBoardConfigKpiDTO>> temp=Maps.newHashMap();
        Set<String> keys =  dataInfoMaps.keySet();
        //组件统计数据
        kpiMaps.forEach((kpi,eventVos)->{
            //针对排序
            List<AmpereRingBoardConfigKpiDTO> dtos = eventVos.stream().sorted((o1, o2) -> {
                if (o1.getSort() > o2.getSort()) {
                    return 1;
                } else {
                    return -1;
                }
            }).map(o -> {
                for(String key : keys){
                    log.error("----------"+o.getEventLevel()+"-------"+key);
                    if(key.contains(o.getEventLevel())){
                        o.setEventLevelCount((o.getEventLevelCount() == null ? 0 : o.getEventLevelCount()) + (dataInfoMaps.get(key).size()));
                    }
                }
                o.setEventLevelCount(o.getEventLevelCount() == null ? 0 : o.getEventLevelCount());
                return o;
            }).collect(Collectors.toList());
            temp.put(kpi,dtos);
        });

        return temp;
    }

    /**
     * 绩效考核得分
     *
     * @param boardConfigKpiDTO
     * @return
     */
    @Override
    public List<AmpereRingBoardConfigDeptDTO> queryScore(AmpereRingBoardConfigKpiDTO boardConfigKpiDTO) {
        LambdaQueryWrapperX<AmpereRingBoardConfigDept> configDeptWrapperX=new LambdaQueryWrapperX<>();
        //获取需要统计的部门/项目部
        configDeptWrapperX.eq(AmpereRingBoardConfigDept::getIsDeptScoreShow,true);
        configDeptWrapperX.eq(AmpereRingBoardConfigDept::getLogicStatus,1);
        if(AmpereRingBoardConfigConstant.AMPERE_RING_DEPT_SCORE_TOTAL_TYPE.equals(boardConfigKpiDTO.getDeptScoreType())){
            //部门绩效
            configDeptWrapperX.notLike(AmpereRingBoardConfigDept::getDeptName,"项目部");
        }
        if(AmpereRingBoardConfigConstant.AMPERE_RING_PROJECT_SCORE_TOTAL_TYPE.equals(boardConfigKpiDTO.getDeptScoreType())){
            //项目部绩效
            configDeptWrapperX.like(AmpereRingBoardConfigDept::getDeptName,"项目部");
        }

        List<AmpereRingBoardConfigDept> depts = configDeptMapper.selectList(configDeptWrapperX);
        if(CollectionUtil.isEmpty(depts)){
            return new ArrayList<AmpereRingBoardConfigDeptDTO>();
        }
        Map<String, AmpereRingBoardConfigDeptDTO> deptMap = depts.stream()
                .map(o -> {
                    if(Objects.isNull(o.getStandardScore())||o.getStandardScore()==0){
                        o.setStandardScore(20.0);
                    }
                    return BeanCopyUtils.convertTo(o, AmpereRingBoardConfigDeptDTO::new);
                }).collect(Collectors.toMap(AmpereRingBoardConfigDeptDTO::getDeptName, Function.identity(), (k1, k2) -> k1));

        List<String> deptCodes = depts.stream().map(o -> o.getDeptCode()).collect(Collectors.toList());
        //获取绩效评分
        LambdaQueryWrapperX<AmpereRingKpiScore> kpiScoreLambdaWrapperX=new LambdaQueryWrapperX<>();
        kpiScoreLambdaWrapperX.eq(AmpereRingKpiScore::getLogicStatus,1);
        if(!CollectionUtils.isEmpty(deptCodes)){
            kpiScoreLambdaWrapperX.in(AmpereRingKpiScore::getDutyPersonDept,deptCodes);
        }
        if(Objects.nonNull(boardConfigKpiDTO.getYear())){
            kpiScoreLambdaWrapperX.like(AmpereRingKpiScore::getEventDate,boardConfigKpiDTO.getYear());
        }
        kpiScoreLambdaWrapperX.eq(AmpereRingKpiScore::getStatus,2);//已考核
        //kpiScoreLambdaWrapperX.eq(AmpereRingKpiScore::getType,2);//部门
        List<AmpereRingKpiScore> kpiScoreS = ampereRingKpiScoreMapper.selectList(kpiScoreLambdaWrapperX);
        Map<String, List<AmpereRingKpiScore>> deptScoreMap = kpiScoreS.stream().filter(o->Objects.nonNull(o.getDutyPersonDept())).collect(Collectors.groupingBy(AmpereRingKpiScore::getDutyPersonDept));
        
        //计算
        List<AmpereRingBoardConfigDeptDTO> configDeptDTOS = deptMap.values().stream().map(dept -> {
            if (deptScoreMap.containsKey(dept.getDeptCode())) {
                List<AmpereRingKpiScore> ampereRingKpiScores = deptScoreMap.get(dept.getDeptCode());
                double asDouble = ampereRingKpiScores.stream().filter(o->Objects.nonNull(o.getScore())).mapToDouble(AmpereRingKpiScore::getScore).reduce((x, y) -> x + y).getAsDouble();
                double v = ((Objects.isNull(dept.getStandardScore())?20:dept.getStandardScore()) + asDouble) / (Objects.isNull(dept.getStandardScore())?20:dept.getStandardScore()) * 100;
                dept.setKpiScore(String.format("%.2f",v));
            }else{
                dept.setKpiScore("100.00");
            }
            return dept;
        }).sorted((o1, o2) -> {
            if (Double.parseDouble(o1.getKpiScore()) > Double.parseDouble(StringUtils.hasText(o2.getKpiScore()) ? o2.getKpiScore() : "0")) {
                return -1;
            } else {
                return 1;
            }
        }).collect(Collectors.toList());


        return configDeptDTOS;
    }

    /**
     * 作业未完工统计
     * 分级统计
     * 当前环节:070_现场作业
     * 作业状态：不是99，98
     * 按“当前环节”字段进行统计，统计“作业过程”状态的所有工单且开工日期在当天及之前的所有工单的数量。
     *
     * @return
     */
    @Override
    public JobHeightRiskDTO  jobTotalUndone() {
        //查询需要展示的作业
        LambdaQueryWrapperX<AmpereRingBoardConfigJob> jobLambdaQueryWrapperX=new LambdaQueryWrapperX<>();
        jobLambdaQueryWrapperX.eq(AmpereRingBoardConfigJob::getIsShow,true);
        List<AmpereRingBoardConfigJob> ringBoardConfigJobs = ampereRingBoardConfigJobMapper.selectList(jobLambdaQueryWrapperX);
        List<String> jobNames = ringBoardConfigJobs.stream().map(o -> o.getJobName()).distinct().collect(Collectors.toList());

        LambdaQueryWrapperX<JobHeightRisk> lambdaQueryWrapperX=new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.le(JobHeightRisk::getPlanCommencementDate, DateTime.now());
//        lambdaQueryWrapperX.eq(JobHeightRisk::getCurrentPhase,"070_现场作业");
//        lambdaQueryWrapperX.notIn(JobHeightRisk::getProcessStatus, Arrays.asList(99,98));

//        List<String> currentPhases = Arrays.asList("010_发起流程","021_管控一/二级作业部门安全员审查","022_管控三级作业所安全员审查","030_所或模块经理批准","040_部门负责人或授权人批准","050_公司安质环部审查","060_总经理部批准","070_现场作业");
//        lambdaQueryWrapperX.in(JobHeightRisk ::getCurrentPhase,currentPhases);
        lambdaQueryWrapperX.eq(JobHeightRisk ::getCurrentPhase,"070_现场作业");
        lambdaQueryWrapperX.notIn(JobHeightRisk :: getProcessStatus,Arrays.asList(10,98,99));
        JobHeightRiskDTO riskDTO=new JobHeightRiskDTO();
        riskDTO.setRiskTypeNameSub(Lists.newArrayList());
        riskDTO.setAddressSub(Lists.newArrayList());
        if(!CollectionUtils.isEmpty(jobNames)){
            lambdaQueryWrapperX.in(JobHeightRisk::getJudgmentStandards,jobNames);
        }else{
            return riskDTO;
        }
        List<JobHeightRisk> jobHeightRisks = jobHeightRiskMapper.selectList(lambdaQueryWrapperX);
        if(CollectionUtils.isEmpty(jobHeightRisks)){
            return riskDTO;
        }

        /**
         * 过滤规则  除掉 一个job number 下的多条数据
         * 1.取最高级别的信息。
         * 2.如果是同级别,就取集合里面的集合的第一个
         */
        List<JobHeightRisk> dealJobHeightLists = filterMuliterData(jobHeightRisks);
        List<JobHeightRiskDTO> jobHeightRiskDTOS = BeanCopyUtils.convertListTo(dealJobHeightLists, JobHeightRiskDTO::new);

        if(!CollectionUtils.isEmpty(jobHeightRiskDTOS)){
            riskDTO.setRiskLevelTotal(jobHeightRiskDTOS.size());
            Map<String, List<JobHeightRiskDTO>> riskLevelMap = jobHeightRiskDTOS.stream().collect(Collectors.groupingBy(JobHeightRiskDTO::getRiskLevel));
            Map<String,JobHeightRiskDTO> jobHeightRiskDTOMap=Maps.newHashMap();
            //汇总风险等级
            riskLevelMap.forEach((eventLevel,vos)->{
                JobHeightRiskDTO temp=new JobHeightRiskDTO();
                temp.setRiskLevel(eventLevel);
                temp.setRiskLevelTotal(vos.size());
                jobHeightRiskDTOMap.put(eventLevel,temp);
            });
            riskDTO.setChildHeightRisks(jobHeightRiskDTOMap);
            //地点风险等级信息
            Map<String, List<JobHeightRiskDTO>> jobAddressMap = jobHeightRiskDTOS.stream().filter(o->Objects.nonNull(o.getJobAddressName())).collect(Collectors.groupingBy(JobHeightRiskDTO::getJobAddressName));
            addSubInfo(jobAddressMap, riskDTO,"address");
            Map<String, List<JobHeightRiskDTO>> judMap = jobHeightRiskDTOS.stream().filter(o->Objects.nonNull(o.getJudgmentStandards())).collect(Collectors.groupingBy(JobHeightRiskDTO::getJudgmentStandards));
           // addSubInfo(judMap,riskDTO,"judgment_standards");

        }
        //针对没有数据的情况处理
        Map<String, JobHeightRiskDTO> childHeightRisks = Map.of();
        if(Objects.isNull(childHeightRisks)){
            childHeightRisks=Maps.newHashMap();
        } else {
            childHeightRisks = riskDTO.getChildHeightRisks();
        }
        Map<String, JobHeightRiskDTO> finalChildHeightRisks = childHeightRisks;
        Arrays.asList("一级","二级","三级").stream().forEach(k->{
            if(!finalChildHeightRisks.containsKey(k)){
                JobHeightRiskDTO tmep=new JobHeightRiskDTO();
                tmep.setRiskLevelTotal(0);
                finalChildHeightRisks.put(k,tmep);
            }
        });
        jobTypeStatistics(riskDTO,jobHeightRisks);

        riskDTO.setChildHeightRisks(finalChildHeightRisks);
        return riskDTO;
    }

    private void jobTypeStatistics(JobHeightRiskDTO riskDTO, List<JobHeightRisk> jobHeightRisks) {
        List<JobHeightRiskDTO> riskTypeNameSub = new ArrayList<>();

        // 高风险数据 先通过作业区分 获取每个作业对应的高风险等级（获取每个作业风险等级最高的数据 以及对应的风险类型）
        Map<String,List<JobHeightRisk>> copyIdMap = jobHeightRisks.stream().collect(Collectors.groupingBy(JobHeightRisk :: getCopyId));

        List<JobHeightRisk> list = new ArrayList<>();

        copyIdMap.forEach((key,value) ->{
            Map<String,List<JobHeightRisk>> riskLevelMap = value.stream().collect(Collectors.groupingBy(JobHeightRisk :: getRiskLevel));
            if(riskLevelMap.size()==1){
                //如果是同级别,就取集合里面的集合的第一个
                list.add(value.get(0));
            }else{
                //说明是多个级别 按照级别取 一级》二级》三级 取级别的第一
                if(riskLevelMap.containsKey("一级")){
                    List<JobHeightRisk> heightRisks = riskLevelMap.get("一级");
                    list.add(heightRisks.get(0));
                }else if(riskLevelMap.containsKey("二级")){
                    List<JobHeightRisk> heightRisks = riskLevelMap.get("二级");
                    list.add(heightRisks.get(0));
                }else if(riskLevelMap.containsKey("三级")){
                    List<JobHeightRisk> heightRisks = riskLevelMap.get("三级");
                    list.add(heightRisks.get(0));
                }
            }

        });
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        Map<String,List<JobHeightRisk>> judgmentStandardsMap = list.stream().collect(Collectors.groupingBy(JobHeightRisk :: getJudgmentStandards));
        judgmentStandardsMap.forEach((key,value) ->{
            JobHeightRiskDTO jobHeightRiskDTO = new JobHeightRiskDTO();
            jobHeightRiskDTO.setJudgmentStandards(key);
            jobHeightRiskDTO.setRiskLevelTotal(value.size());
            Map<String,JobHeightRiskDTO> childHeightRisks = new HashMap<>();
            Map<String,List<JobHeightRisk>> riskLevelMap = value.stream().collect(Collectors.groupingBy(JobHeightRisk :: getRiskLevel));
            riskLevelMap.forEach((riskLevel,vos)->{
                JobHeightRiskDTO riskDTO1 = new JobHeightRiskDTO();
                riskDTO1.setRiskLevelTotal(vos.size());
                childHeightRisks.put(riskLevel,riskDTO1);
            });
            jobHeightRiskDTO.setChildHeightRisks(childHeightRisks);
            riskTypeNameSub.add(jobHeightRiskDTO);
        });
        riskDTO.setRiskTypeNameSub(riskTypeNameSub);
    }

    /**
     * 作业计划开工统计
     *
     * @param jobHeightRiskDTO
     * @return
     */
    @Override
    public JobHeightRiskDTO queryPlanStartWorkTotal(JobHeightRiskDTO jobHeightRiskDTO) {
        //查询需要展示的作业
        LambdaQueryWrapperX<AmpereRingBoardConfigJob> jobLambdaQueryWrapperX=new LambdaQueryWrapperX<>();
        jobLambdaQueryWrapperX.eq(AmpereRingBoardConfigJob::getIsShow,true);
        List<AmpereRingBoardConfigJob> ringBoardConfigJobs = ampereRingBoardConfigJobMapper.selectList(jobLambdaQueryWrapperX);
        List<String> jobNames = ringBoardConfigJobs.stream().map(o -> o.getJobName()).distinct().collect(Collectors.toList());

        LambdaQueryWrapperX<JobHeightRisk> lambdaQueryWrapperX=new LambdaQueryWrapperX<>();

        JobHeightRiskDTO riskDTO=new JobHeightRiskDTO();
        riskDTO.setRiskTypeNameSub(Lists.newArrayList());
        riskDTO.setAddressSub(Lists.newArrayList());
        if(Objects.nonNull(jobHeightRiskDTO.getPlanStartTime())){
            lambdaQueryWrapperX.ge(JobHeightRisk::getPlanCommencementDate,jobHeightRiskDTO.getPlanStartTime());
        }
        if(Objects.nonNull(jobHeightRiskDTO.getPlanEndTime())){
            lambdaQueryWrapperX.le(JobHeightRisk::getPlanCommencementDate,jobHeightRiskDTO.getPlanEndTime());
        }
        if(!CollectionUtils.isEmpty(jobNames)){
            lambdaQueryWrapperX.in(JobHeightRisk::getJudgmentStandards,jobNames);
        }else{
            return riskDTO;
        }
//        List<String> currentPhases = Arrays.asList("010_发起流程","021_管控一/二级作业部门安全员审查","022_管控三级作业所安全员审查","030_所或模块经理批准","040_部门负责人或授权人批准","050_公司安质环部审查","060_总经理部批准","070_现场作业");
        lambdaQueryWrapperX.eq(JobHeightRisk ::getCurrentPhase,"070_现场作业");
        lambdaQueryWrapperX.notIn(JobHeightRisk :: getProcessStatus,Arrays.asList(98,99));
        List<JobHeightRisk> jobHeightRisks = jobHeightRiskMapper.selectList(lambdaQueryWrapperX);
        if(CollectionUtils.isEmpty(jobHeightRisks)){
            return riskDTO;
        }
        /**
         * 过滤规则  除掉 一个job number 下的多条数据
         * 1.取最高级别的信息。
         * 2.如果是同级别,就取集合里面的集合的第一个
         */
        List<JobHeightRisk> heightRisks = filterMuliterData(jobHeightRisks);
        List<JobHeightRiskDTO> jobHeightRiskDTOS = BeanCopyUtils.convertListTo(heightRisks, JobHeightRiskDTO::new);
        //if(!CollectionUtils.isEmpty(jobHeightRiskDTOS)){
        riskDTO.setRiskLevelTotal(jobHeightRiskDTOS.size());
        Map<String, List<JobHeightRiskDTO>> riskLevelMap = jobHeightRiskDTOS.stream().filter(o->Objects.nonNull(o.getRiskLevel())).collect(Collectors.groupingBy(JobHeightRiskDTO::getRiskLevel));
        Map<String, JobHeightRiskDTO> jobHeightRiskDTOMap = Maps.newHashMap();
        jobHeightRiskDTOMap.put("一级",makeNullRiskDto("一级",0));
        jobHeightRiskDTOMap.put("二级",makeNullRiskDto("二级",0));
        jobHeightRiskDTOMap.put("三级",makeNullRiskDto("三级",0));
        //汇总风险等级
        if(!CollectionUtils.isEmpty(riskLevelMap)){
            riskLevelMap.forEach((eventLevel, vos) -> {
                JobHeightRiskDTO temp = new JobHeightRiskDTO();
                temp.setRiskLevel(eventLevel);
                temp.setRiskLevelTotal(vos.size());
                jobHeightRiskDTOMap.put(eventLevel, temp);
            });
        }
        riskDTO.setChildHeightRisks(jobHeightRiskDTOMap);
        //地点风险等级信息
        Map<String, List<JobHeightRiskDTO>> jobAddressMap = jobHeightRiskDTOS.stream().filter(o->Objects.nonNull(o.getJobAddressName())).collect(Collectors.groupingBy(JobHeightRiskDTO::getJobAddressName));
        addSubInfo(jobAddressMap, riskDTO, "address");
        Map<String, List<JobHeightRiskDTO>> judMap = jobHeightRiskDTOS.stream().filter(o->Objects.nonNull(o.getJudgmentStandards())).collect(Collectors.groupingBy(JobHeightRiskDTO::getJudgmentStandards));
        //addSubInfo(judMap, riskDTO, "judgment_standards");
        //}

        jobTypeStatistics(riskDTO,jobHeightRisks);
        return riskDTO;
    }

    private  List<JobHeightRisk> filterMuliterData(List<JobHeightRisk> jobHeightRisks) {
        Map<String, List<JobHeightRisk>> jobGroupMap = jobHeightRisks.stream().collect(Collectors.groupingBy(JobHeightRisk::getCopyId));
        List<JobHeightRisk> dealJobHeightLists=Lists.newArrayList();
        jobGroupMap.forEach((k,list)->{
            //分等级处理
            Map<String, List<JobHeightRisk>> tmepMap = list.stream().collect(Collectors.groupingBy(JobHeightRisk::getRiskLevel));

            if(tmepMap.size()==1){
                //如果是同级别,就取集合里面的集合的第一个
                dealJobHeightLists.add(list.get(0));

            }else{
                //说明是多个级别 按照级别取 一级》二级》三级 取级别的第一
                if(tmepMap.containsKey("一级")){
                    List<JobHeightRisk> heightRisks = tmepMap.get("一级");
                    dealJobHeightLists.add(heightRisks.get(0));
                }else if(tmepMap.containsKey("二级")){
                    List<JobHeightRisk> heightRisks = tmepMap.get("二级");
                    dealJobHeightLists.add(heightRisks.get(0));
                }else if(tmepMap.containsKey("三级")){
                    List<JobHeightRisk> heightRisks = tmepMap.get("三级");
                    dealJobHeightLists.add(heightRisks.get(0));
                }
            }
        });
        return dealJobHeightLists;
    }

    /**
     * 空对象
     * @param event
     * @param total
     * @return
     */
    private JobHeightRiskDTO makeNullRiskDto(String event, int total) {
        JobHeightRiskDTO jobHeightRiskDTO=new JobHeightRiskDTO();
        jobHeightRiskDTO.setRiskLevel(event);
        jobHeightRiskDTO.setRiskLevelTotal(total);
        return jobHeightRiskDTO;
    }

    /**
     * 隐患排查统计
     *
     * 时间范围：
     * queryCheckProblemsDateType
     * 年：year
     * 月：month
     * 季度：quarter
     *queryCheckProblemsDate 参数：
     * 年：2024
     * 月: 2024-06
     * 季度 2024-Q1
     *集团发现：统计各部门事件等级为“集团发现”的事件数
     * 公司监督发现：
     * A：外部监督发现选择为“是”的事件
     * B：外部监督发现选择否，检查人部门包含安全质量环保部和总经理部的事件
     * C：检查人部门是客户成功事业中心且归口责任部门不是客户成功事业中心、 宁德项目部、台山项目部、防城港项目部、大亚湾项目部、红沿河项目部、阳江项目部、惠州项目部的事件数
     * D：【隐患/事件领域】为【安质环绩效加分】的数据
     * 公司监督发现=A+B+C-D
     * 自纠自查=全部-集团发现数-公司监督数
     * @param ampereRingBoardConfigKpiDTO
     * @return
     */
    @Override
    public Map checkProblems(AmpereRingBoardConfigKpiDTO ampereRingBoardConfigKpiDTO) {
        //查询的隐患排查需要展示的组织
        LambdaQueryWrapperX<AmpereRingBoardConfigDept> deptLambdaQueryWrapperX=new LambdaQueryWrapperX<>();
        deptLambdaQueryWrapperX.eq(AmpereRingBoardConfigDept::getIsCheckProblemsShow,true);
        List<AmpereRingBoardConfigDept> configDepts = ampereRingBoardConfigDeptMapper.selectList(deptLambdaQueryWrapperX);
        Map<String, String> deptInfoMap = configDepts.stream().collect(Collectors.toMap(AmpereRingBoardConfigDept::getDeptCode, AmpereRingBoardConfigDept::getDeptName));

        LambdaQueryWrapperX<AmpereRingEventCheckDataInfo> eventCheckWrapperX=new LambdaQueryWrapperX<>();
        eventCheckWrapperX.eq(AmpereRingEventCheckDataInfo::getLogicStatus,1);
        // 010_录入 过滤
        eventCheckWrapperX.ne(AmpereRingEventCheckDataInfo::getCurrentProcess,"010_录入");
        if(!CollectionUtils.isEmpty(configDepts)){
            List<String> deptCodes = configDepts.stream().map(AmpereRingBoardConfigDept::getDeptCode).collect(Collectors.toList());
            eventCheckWrapperX.in(AmpereRingEventCheckDataInfo::getGkDeptCode,deptCodes);
        }else{
            return Maps.newHashMap();
        }
        if(StringUtils.hasText(ampereRingBoardConfigKpiDTO.getQueryCheckProblemsDateType())
        && StringUtils.hasText(ampereRingBoardConfigKpiDTO.getQueryCheckProblemsDate())){
            //查询时间的组织
            if("year".equals(ampereRingBoardConfigKpiDTO.getQueryCheckProblemsDateType())||
                    "month".equals(ampereRingBoardConfigKpiDTO.getQueryCheckProblemsDateType())){
                //年 ，月
                eventCheckWrapperX.like(AmpereRingEventCheckDataInfo::getEventDate,ampereRingBoardConfigKpiDTO.getQueryCheckProblemsDate());
            }
            if("quarter".equals(ampereRingBoardConfigKpiDTO.getQueryCheckProblemsDateType())){
                //Q1  01,02,03  Q2 04，05，06  Q3：07，08，09  Q4：10，11，12
                String[] dateTypes = ampereRingBoardConfigKpiDTO.getQueryCheckProblemsDate().split("-");
                if(dateTypes.length>1){
                    makerQuarterWrapperX(ampereRingBoardConfigKpiDTO, eventCheckWrapperX, dateTypes[0],dateTypes[1]);
                }
            }

        }
        List<AmpereRingEventCheckDataInfo> dataInfos = ampereRingEventCheckDataInfoMapper.selectList(eventCheckWrapperX);
        //统计规则
        /**
         * 集团发现：统计各部门事件等级为“集团发现”的事件数
         * 公司监督发现：
         * A：外部监督发现选择为“是”的事件
         * B：外部监督发现选择否，检查人部门包含安全质量环保部和总经理部的事件
         * C：检查人部门是客户成功事业中心且归口责任部门不是客户成功事业中心、
         * 宁德项目部、台山项目部、防城港项目部、大亚湾项目部、红沿河项目部、阳江项目部、惠州项目部的事件数
         * D：【隐患/事件领域】为【安质环绩效加分】的数据
         * 公司监督发现=A+B+C-D
         * 自纠自查=全部-集团发现数-公司监督数
         */
        List<String> deptProjectNames = Arrays.asList("客户成功事业中心", "宁德项目部", "台山项目部", "防城港项目部", "大亚湾项目部",
                "红沿河项目部", "阳江项目部", "惠州项目部");

        List<String> deptProjectCodes = configDepts.stream().filter(o -> deptProjectNames.contains(o.getDeptName())).map(AmpereRingBoardConfigDept::getDeptCode).collect(Collectors.toList());

        //统计部门
        Map resultMap = Maps.newHashMap();
        AtomicLong groupSum= new AtomicLong(0);
        AtomicLong companySum= new AtomicLong(0);
        AtomicLong deptSum= new AtomicLong(0);
        if(!CollectionUtils.isEmpty(dataInfos)){
            Map<String, List<AmpereRingEventCheckDataInfo>> deptDataInfoMap = dataInfos.stream().filter(o->Objects.nonNull(o.getGkDeptCode())).collect(Collectors.groupingBy(AmpereRingEventCheckDataInfo::getGkDeptCode));
            //部门
            List<Map> deptMaps=Lists.newArrayList();
            resultMap.put("部门",deptMaps);
            //项目部
            List<Map> projectMaps=Lists.newArrayList();
            resultMap.put("项目部",projectMaps);
            deptDataInfoMap.forEach((deptCode,infos)->{
                Map temp=Maps.newHashMap();
                //集团 统计各部门事件等级为“上级发现”的事件数
                long groupCount = infos.stream().filter(o -> "上级发现".equals(o.getEventLevel())).count();
                temp.put("groupTotal",groupCount);
                groupSum.addAndGet(groupCount);
                //公司监督：外部监督是的  对象的数据 0 表示是  1 表示 否 。这边因为我们当时这边用的bolean 值 所以反取
                long companyFindCount = infos.stream().filter(o -> Objects.nonNull(o.getIsFind())&&!o.getIsFind()).count();
                //公司：外部监督发现选择否，检查人部门包含安全质量环保部和总经理部的事件
                long companyCheckCount = infos.stream().filter(o -> Objects.nonNull(o.getIsFind())&&o.getIsFind() &&
                        Objects.nonNull(o.getCheckPersonDept())&&
                        (o.getCheckPersonDept().contains("安全质量环保部")
                        || o.getCheckPersonDept().contains("总经理部"))).count();
                //外部监督发现是否,检查人部门是客户成功事业中心且归口责任部门不是客户成功事业中心、 宁德项目部、台山项目部、防城港项目部、大亚湾项目部、红沿河项目部、阳江项目部、惠州项目部的事件数
                long companyCheckDeptCount = infos.stream().filter(o -> "客户成功事业中心".equals(o.getCheckPersonDept()) &&
                        Objects.nonNull(o.getIsFind())&& o.getIsFind()&&
                        !deptProjectCodes.contains(o.getGkDeptCode())).count();
                long companyCount=companyCheckCount+companyCheckDeptCount+companyFindCount;
                companySum.addAndGet(companyCount);
                temp.put("companyTotal",companyCount);

                //自查
                long deptCount=infos.size()-groupCount-companyCount;
                temp.put("deptCount",deptCount);
                deptSum.addAndGet(deptCount);
                if(deptInfoMap.containsKey(deptCode)){
                    String deptName = deptInfoMap.get(deptCode);
                    temp.put("name",deptName);
                    if(deptName.contains("项目部")){
                        projectMaps.add(temp);
                    }else{
                        deptMaps.add(temp);
                    }


                }
                temp.put("isStandard",checkIsStandard(groupCount,companyCount,deptCount));
            });
        }
        resultMap.put("groupSum",groupSum.get());
        resultMap.put("companySum",companySum.get());
        resultMap.put("deptSum",deptSum.get());
        resultMap.put("isStandard",checkIsStandard(groupSum.get(),companySum.get(),deptSum.get()));
        return resultMap;
    }

    /**
     * 是否达标
     * 展示各部门的隐患发现的情况，集团发现:公司发现:部门自查自纠是否满足1：7：49的要求
     * @param group
     * @param company
     * @param dept
     * @return
     */
    private Object checkIsStandard(long group, long company, long dept) {
        if(company==0&&group==0&&dept==0){
            //如果都没有发现问题
            return true;
        }

        //必须 部门发现的> 公司发现的 > 集团发现的
        if(company<group||dept<company){
            return false;
        }
        //公司 必须发现事件数量是集团的7陪以上
        if(group!=0&&company/group<7){
            return false;
        }
        //部门 必须 发现的是公司的7陪以上
        if(company!=0&& dept/company<7){
            return false;
        }
        return true;
    }

    /**
     * 季度查询语句拼接
     * @param ampereRingBoardConfigKpiDTO
     * @param eventCheckWrapperX
     * @param year
     */
    private static void makerQuarterWrapperX(AmpereRingBoardConfigKpiDTO ampereRingBoardConfigKpiDTO, LambdaQueryWrapperX<AmpereRingEventCheckDataInfo> eventCheckWrapperX, String year,String quarter) {
        if(quarter.contains("Q1")){
            eventCheckWrapperX.and(wrapperx->{
               wrapperx.like(AmpereRingEventCheckDataInfo::getEventDate, year +"-01");
               wrapperx.or();
               wrapperx.like(AmpereRingEventCheckDataInfo::getEventDate, year +"-02");
               wrapperx.or();
               wrapperx.like(AmpereRingEventCheckDataInfo::getEventDate, year +"-03");
            });
        }
        if(quarter.contains("Q2")){
            eventCheckWrapperX.and(wrapperx->{
                wrapperx.like(AmpereRingEventCheckDataInfo::getEventDate, year +"-04");
                wrapperx.or();
                wrapperx.like(AmpereRingEventCheckDataInfo::getEventDate, year +"-05");
                wrapperx.or();
                wrapperx.like(AmpereRingEventCheckDataInfo::getEventDate, year +"-06");
            });
        }
        if(quarter.contains("Q3")){
            eventCheckWrapperX.and(wrapperx->{
                wrapperx.like(AmpereRingEventCheckDataInfo::getEventDate, year +"-07");
                wrapperx.or();
                wrapperx.like(AmpereRingEventCheckDataInfo::getEventDate, year +"-08");
                wrapperx.or();
                wrapperx.like(AmpereRingEventCheckDataInfo::getEventDate, year +"-09");
            });
        }
        if(quarter.contains("Q4")){
            eventCheckWrapperX.and(wrapperx->{
                wrapperx.like(AmpereRingEventCheckDataInfo::getEventDate, year +"-10");
                wrapperx.or();
                wrapperx.like(AmpereRingEventCheckDataInfo::getEventDate, year +"-11");
                wrapperx.or();
                wrapperx.like(AmpereRingEventCheckDataInfo::getEventDate, year +"-12");
            });
        }
    }

    /**
     * 组织风险信息
     * @param jobMap
     * @param addDto
     * @param type
     */
    private static void addSubInfo(Map<String, List<JobHeightRiskDTO>> jobMap, JobHeightRiskDTO addDto,String type) {
        List<JobHeightRiskDTO> addressList= Lists.newArrayList();
        jobMap.forEach((key, vos)->{
            JobHeightRiskDTO temp=new JobHeightRiskDTO();
            temp.setRiskLevelTotal(vos.size());
            Map<String, List<JobHeightRiskDTO>> addressRiskMap = vos.stream().collect(Collectors.groupingBy(JobHeightRiskDTO::getRiskLevel));
            Map<String,JobHeightRiskDTO> tempMap=Maps.newHashMap();
            addressRiskMap.forEach((k,list)->{
                //风险等级信息
                JobHeightRiskDTO t=new JobHeightRiskDTO();
                t.setRiskLevelTotal(list.size());
                tempMap.put(k,t);
            });
            temp.setChildHeightRisks(tempMap);
            if(!CollectionUtils.isEmpty(vos)){
                temp.setJobAddressName(vos.get(0).getJobAddressName());
            }
            addressList.add(temp);
            if(type.equals("address")){
                temp.setJobAddress(key);
                addDto.setAddressSub(addressList);
            }
            if(type.equals("judgment_standards")){
                temp.setJudgmentStandards(key);
                // 为空
                addressList.forEach(ite->{
                    ite.setJobAddressName("");
                });
                addDto.setRiskTypeNameSub(addressList);
            }
        });
    }
}
