package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.ProjectSchemeContentBatchDTO;
import com.chinasie.orion.domain.dto.ProjectSchemeContentDTO;
import com.chinasie.orion.domain.entity.ProjectSchemeContent;
import com.chinasie.orion.domain.vo.ProjectSchemeContentVO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.mybatis.service.OrionBaseService;

import java.util.List;
import java.util.Map;

/**
 * ProjectSchemeContentService
 *
 * @author: yangFy
 * @date: 2023/4/19 16:08
 * @description:
 * <p>
 *项目计划记录内容
 *
 * </p>
 */
public interface ProjectSchemeContentService extends OrionBaseService<ProjectSchemeContent> {
    /**
     * 添加计划记录（批量）
     * @param contentDTOS
     * @return
     * @throws Exception
     */
    List<String> createBatch(ProjectSchemeContentBatchDTO projectSchemeContentBatchDTO) throws Exception;

    /**
     * 删除（批量）
     * @param ids
     * @return
     * @throws Exception
     */
    Boolean deleteByIds(List<String> ids) throws Exception;

    List<ProjectSchemeContentVO> getList(String id) throws Exception;

    Map<String, String> getMapBySchemeIds(List<String> schemeIds);
}
