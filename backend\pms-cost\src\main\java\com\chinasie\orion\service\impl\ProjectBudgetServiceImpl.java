package com.chinasie.orion.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.bo.CodeBo;
import com.chinasie.orion.constant.ClassNameConstant;
import com.chinasie.orion.domain.dto.ProjectBudgetAnnualDTO;
import com.chinasie.orion.domain.dto.ProjectBudgetDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageRequest;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.BudgetMonthMapper;
import com.chinasie.orion.repository.ExponseDetailMapper;
import com.chinasie.orion.repository.ProjectBudgetMapper;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.CostCenterService;
import com.chinasie.orion.service.ExpenseSubjectService;
import com.chinasie.orion.service.ProjectBudgetService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.PmsAuthUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * ProjectBudget 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16 14:48:31
 */
@Service
public class ProjectBudgetServiceImpl extends OrionBaseServiceImpl<ProjectBudgetMapper, ProjectBudget> implements ProjectBudgetService {

    @Resource
    private ProjectBudgetMapper projectBudgetMapper;

    @Resource
    private BudgetMonthMapper budgetMonthMapper;

    @Resource
    private CostCenterService costCenterService;

    @Resource
    private ExpenseSubjectService expenseSubjectService;
    
    @Resource
    private ExponseDetailMapper exponseDetailMapper;

    @Resource
    private CodeBo codeBo;

    @Autowired
    private PmsAuthUtil pmsAuthUtil;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    public ProjectBudgetVO detail(String id,String pageCode) throws Exception {
        ProjectBudget projectBudget =projectBudgetMapper.selectById(id);
        ProjectBudgetVO result = BeanCopyUtils.convertTo(projectBudget,ProjectBudgetVO::new);

        if (org.springframework.util.StringUtils.hasText(pageCode)) {
            String currentUserId = CurrentUserHelper.getCurrentUserId();
            List<String> roleCodeList = pmsAuthUtil.getRoleCodeList(result.getProjectId(), currentUserId);
            pmsAuthUtil.setDetailAuths(result, currentUserId, pageCode, result.getDataStatus(), ProjectBudgetVO::setDetailAuthList, result.getCreatorId(), result.getModifyId(), result.getOwnerId(), roleCodeList);
        }
        return result;
    }



    /**
     *  新增
     *
     * * @param projectBudgetDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public  ProjectBudgetVO create(ProjectBudgetDTO projectBudgetDTO) throws Exception {
        ProjectBudget projectBudget =BeanCopyUtils.convertTo(projectBudgetDTO,ProjectBudget::new);
        BudgetMonth budgetMonth = BeanCopyUtils.convertTo(projectBudgetDTO, BudgetMonth::new);
        // 获取编码
//        List<SysCodeSegmentVO> codeRuleList = codeBo.getCodeRuleList(ClassNameConstant.PROJECT_BUDGET,ClassNameConstant.NUMBER);
//        if (!CollectionUtils.isEmpty(codeRuleList)) {
//            String code = codeBo.getCode(codeRuleList);
//            projectBudget.setNumber(code);
//        }
        String code = codeBo.createCode(ClassNameConstant.PROJECT_BUDGET, ClassNameConstant.NUMBER, false, null);
        if (BeanUtil.isNotEmpty(code)){
            projectBudget.setNumber(code);
        }
        projectBudget.setPriceDifference(projectBudget.getYearExpense());
        int projectBudgetInsert = projectBudgetMapper.insert(projectBudget);
        budgetMonth.setBudgetProjectId(projectBudget.getId());
        budgetMonth.setBudgetProjectName(projectBudget.getName());
        int budgetMonthInsert = budgetMonthMapper.insert(budgetMonth);
        projectBudget.setMonthBudgetId(budgetMonth.getId());
        projectBudgetMapper.updateById(projectBudget);
        ProjectBudgetVO rsp = BeanCopyUtils.convertTo(projectBudget,ProjectBudgetVO::new);
        return rsp;
    }

    /**
     *  编辑
     *
     * * @param projectBudgetDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean edit(ProjectBudgetDTO projectBudgetDTO) throws Exception {
        boolean exists = exponseDetailMapper
                .exists(new LambdaQueryWrapperX<ExponseDetail>()
                        .eq(ExponseDetail::getBudgetProjectId, projectBudgetDTO.getId()));
        if (exists){
            throw  new PMSException(PMSErrorCode.PMS_ERR,"当前预算下存在成本，无法编辑");
        }
        ProjectBudget projectBudget =BeanCopyUtils.convertTo(projectBudgetDTO,ProjectBudget::new);
        BudgetMonth budgetMonth =BeanCopyUtils.convertTo(projectBudgetDTO,BudgetMonth::new);
        int update =  projectBudgetMapper.updateById(projectBudget);
        ProjectBudget projectBudgetUpdate = projectBudgetMapper.selectById(projectBudgetDTO.getId());
        budgetMonth.setId(projectBudgetUpdate.getMonthBudgetId());
        int update1 = budgetMonthMapper.updateById(budgetMonth);
        return SqlHelper.retBool(update);
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(List<String> ids) throws Exception {
        boolean exists = exponseDetailMapper
                .exists(new LambdaQueryWrapperX<ExponseDetail>()
                        .in(ExponseDetail::getBudgetProjectId, ids));
        if (exists){
           throw  new PMSException(PMSErrorCode.PMS_ERR,"当前预算下存在成本，请联系管理员");
        }

        List<ProjectBudget> projectBudgets = projectBudgetMapper.selectBatchIds(ids);
        List<String> monthBudgetIds = projectBudgets.stream()
                .map(ProjectBudget::getMonthBudgetId)
                .collect(Collectors.toList());
        int delete = projectBudgetMapper.deleteBatchIds(ids);

        budgetMonthMapper.deleteBatchIds(monthBudgetIds);
        return SqlHelper.retBool(delete);
    }

    @Override
    public Page<ProjectBudgetVO> getProjectBudgetVOPage(Page<ProjectBudgetDTO> pageRequest) throws Exception{
       Page<ProjectBudgetVO> resultPage = new com.chinasie.orion.sdk.metadata.page.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), 0L, new ArrayList<>());
        pmsAuthUtil.setHeaderAuths(resultPage, CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), com.chinasie.orion.sdk.metadata.page.Page::setHeadAuthList, new ArrayList<>());

        ProjectBudgetDTO query = pageRequest.getQuery();
        LambdaQueryWrapperX<ProjectBudget> pageCondition = new LambdaQueryWrapperX<>();
        String projectId = query.getProjectId();
        if (StringUtils.hasText(projectId)) {
            pageCondition.eq(ProjectBudget::getProjectId,projectId);
        }
//        List<ConditionItem> conditionItems = pageRequest.getQueryCondition();
//        if (CollectionUtils.isEmpty(conditionItems)) {
//            conditionItems = new ArrayList<>();
//        }
//        Map<String, ConditionItem> conditionItemMap = QueryConditionUtil.conditionListTurnToMap(conditionItems);
//        ConditionItem nameOfConditionItem = conditionItemMap.getOrDefault("name", new ConditionItem());
//        Object value = nameOfConditionItem.getValue();
//        if (Objects.nonNull(value)) {
//            pageCondition.and(sub->sub.like(ProjectBudget::getName, value).or().like(ProjectBudget::getNumber, value));
//        }
        List<List<SearchCondition>> searchConditions = pageRequest.getSearchConditions();
        if (CollectionUtil.isNotEmpty(searchConditions)){
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions,pageCondition);
        }
        Page<ProjectBudget> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        PageResult<ProjectBudget> pageResult = projectBudgetMapper.selectPage(realPageRequest, pageCondition);
        List<ProjectBudget> records = pageResult.getContent();

        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(records)) {
            return resultPage;
        }

        Map<String, BudgetMonth> monthMap = budgetMonthMapper.selectList().stream()
                .collect(Collectors.toMap(BudgetMonth::getBudgetProjectId, e -> e));
        List<ProjectBudgetVO> projectBudgetVOS = BeanCopyUtils.convertListTo(records, ProjectBudgetVO::new);
        List<String> costCenterIds = projectBudgetVOS.stream().map(ProjectBudgetVO::getCostCenterId).distinct().collect(Collectors.toList());
        List<String> expenseSubjectIds = projectBudgetVOS.stream().map(ProjectBudgetVO::getExpenseAccountId).distinct().collect(Collectors.toList());
        Map<String, CostCenter> cosCenterMap = costCenterService.getCosCenterMap(costCenterIds);
        Map<String, ExpenseSubject> expenseSubjectMap = expenseSubjectService.getExpenseSubjectMap(expenseSubjectIds);
        projectBudgetVOS.forEach(e->{
            BudgetMonth budgetMonth = monthMap.getOrDefault(e.getId(),new BudgetMonth());
            CostCenter costCenter = cosCenterMap.getOrDefault(e.getCostCenterId(),new CostCenter());
            ExpenseSubject expenseSubject = expenseSubjectMap.getOrDefault(e.getExpenseAccountId(),new ExpenseSubject());
            BudgetMonthVO monthVO = BeanCopyUtils.convertTo(budgetMonth, BudgetMonthVO::new);
            e.setBudgetMonthVO(monthVO);
            e.setCostCenterName(costCenter.getName());
            e.setExpenseAccountName(expenseSubject.getName());
        });

        //权限设置
        Map<String, List<String>> dataRoleMap = getDataRoleMap(projectBudgetVOS);

        pmsAuthUtil.setRowAuths(CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), projectBudgetVOS, ProjectBudgetVO::getId, ProjectBudgetVO::getDataStatus, ProjectBudgetVO::setRdAuthList,
                ProjectBudgetVO::getCreatorId,
                ProjectBudgetVO::getModifyId,
                ProjectBudgetVO::getOwnerId,
                dataRoleMap);
        resultPage.setTotalSize(pageResult.getTotalSize());
        resultPage.setContent(projectBudgetVOS);
        return resultPage;
    }

    @Override
    public List<ProjectBudgetVO> listProjectBudgetVO() throws Exception{
        List<ProjectBudget> list = list(new LambdaQueryWrapperX<ProjectBudget>().orderByDesc(ProjectBudget::getYear));
        if (!CollectionUtils.isEmpty(list)){
            List<ProjectBudgetVO> projectBudgetVOS = BeanCopyUtils.convertListTo(list, ProjectBudgetVO::new);
            return projectBudgetVOS;
        }
        return null;
    }

    @Override
    public com.chinasie.orion.sdk.metadata.page.Page<ProjectBudgetVO> getProjectBudgetVOPage1(com.chinasie.orion.sdk.metadata.page.Page<ProjectBudgetDTO> pageRequest)  throws Exception{
        com.chinasie.orion.sdk.metadata.page.Page<ProjectBudgetVO> resultPage = new com.chinasie.orion.sdk.metadata.page.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), 0L, new ArrayList<>());
        pmsAuthUtil.setHeaderAuths(resultPage, CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), com.chinasie.orion.sdk.metadata.page.Page::setHeadAuthList, new ArrayList<>());
        LambdaQueryWrapperX<ProjectBudget> pageCondition = new LambdaQueryWrapperX<>();
        pageCondition.orderByDesc(ProjectBudget::getYear);
        ProjectBudgetDTO query = pageRequest.getQuery();
        String projectId = query.getProjectId();
        if (StringUtils.hasText(projectId)) {
            pageCondition.eq(ProjectBudget::getProjectId,projectId);
        }
        Page<ProjectBudget> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        PageResult<ProjectBudget> pageResult = projectBudgetMapper.selectPage(realPageRequest, pageCondition);
        List<ProjectBudget> records = pageResult.getContent();

        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(records)) {
            return resultPage;
        }
        List<String> costCenterIds = records.stream().map(ProjectBudget::getCostCenterId).distinct().collect(Collectors.toList());
        List<String> expenseSubjectIds = records.stream().map(ProjectBudget::getExpenseAccountId).distinct().collect(Collectors.toList());
        Map<String, CostCenter> cosCenterMap = costCenterService.getCosCenterMap(costCenterIds);
        Map<String, ExpenseSubject> expenseSubjectMap = expenseSubjectService.getExpenseSubjectMap(expenseSubjectIds);
        records.forEach(e->{
            CostCenter costCenter = cosCenterMap.getOrDefault(e.getCostCenterId(),new CostCenter());
            ExpenseSubject expenseSubject = expenseSubjectMap.getOrDefault(e.getExpenseAccountId(),new ExpenseSubject());
            e.setCostCenterName(costCenter.getName());
            e.setExpenseAccountName(expenseSubject.getName());
        });
        List<ProjectBudgetVO> projectBudgetVOS = BeanCopyUtils.convertListTo(records, ProjectBudgetVO::new);


        //权限设置
        Map<String, List<String>> dataRoleMap = getDataRoleMap(projectBudgetVOS);

        pmsAuthUtil.setRowAuths(CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), projectBudgetVOS, ProjectBudgetVO::getId, ProjectBudgetVO::getDataStatus, ProjectBudgetVO::setRdAuthList,
                ProjectBudgetVO::getCreatorId,
                ProjectBudgetVO::getModifyId,
                ProjectBudgetVO::getOwnerId,
                dataRoleMap);
        resultPage.setTotalSize(pageResult.getTotalSize());
        resultPage.setContent(projectBudgetVOS);
        return resultPage;
       // return new PageResult<>(projectBudgetVOS, pageResult.getPageNum(), pageResult.getPageSize(), pageResult.getTotalSize());
    }

    public Map<String, List<String>> getDataRoleMap(List<ProjectBudgetVO> vos) throws Exception {
        Map<String, List<String>> dataRoleCodeMap = new HashMap<>();
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        for (ProjectBudgetVO v : vos) {
            List<String> roles = pmsAuthUtil.getRoleCodeList(v.getProjectId(), currentUserId);
            dataRoleCodeMap.put(v.getId(), roles);
        }
        return dataRoleCodeMap;
    }

    @Override
    public TotalCostVO getTotal(String projectId) throws Exception {
        List<ProjectBudget> projectBudgets = projectBudgetMapper.selectList(new LambdaQueryWrapperX<ProjectBudget>().eq(ProjectBudget::getProjectId,projectId));
        BigDecimal yearExpense = projectBudgets.stream().map(ProjectBudget::getYearExpense).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalCost = projectBudgets.stream().map(ProjectBudget::getTotalCost).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalDifference = yearExpense.subtract(totalCost);
        TotalCostVO totalCostVO = new TotalCostVO();
        totalCostVO.setTotalBudget(yearExpense);
        totalCostVO.setTotalCost(totalCost);
        totalCostVO.setTotalDifference(totalDifference);
        BigDecimal subtract = yearExpense.subtract(totalCost);
        int status = subtract.compareTo(BigDecimal.ZERO);
        if (status > 0) {
            totalCostVO.setIsOut(0);
        } else {
            totalCostVO.setIsOut(1);
        }
        return totalCostVO;
    }

    @Override
    public Map<String, ProjectBudget> getProjectBudgetMap(List<String> projectBudgetIds) throws Exception {
        if (!org.springframework.util.CollectionUtils.isEmpty(projectBudgetIds)){
            List<ProjectBudget> projectBudgets = list(new LambdaQueryWrapperX<ProjectBudget>().in(ProjectBudget::getId, projectBudgetIds));
            if (org.springframework.util.CollectionUtils.isEmpty(projectBudgets)){
                return Collections.EMPTY_MAP;
            }
            return projectBudgets.stream().collect(Collectors.toMap(ProjectBudget::getId, e -> e));
        }
        return Collections.EMPTY_MAP;
    }

    @Override
    public List<ProjectBudgetVO> getBudgetListByProjectIdListAndAnnual(ProjectBudgetAnnualDTO projectBudgetAnnualDTO) throws Exception {
        LambdaQueryWrapperX<ProjectBudget> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(ProjectBudget.class);
        lambdaQueryWrapperX.in(ProjectBudget :: getProjectId,projectBudgetAnnualDTO.getProjectIdList());
        lambdaQueryWrapperX.eq(ProjectBudget :: getYear,projectBudgetAnnualDTO.getAnnual());
        List<ProjectBudget> projectBudgets = projectBudgetMapper.selectList(lambdaQueryWrapperX);
        return BeanCopyUtils.convertListTo(projectBudgets,ProjectBudgetVO :: new);
    }
}