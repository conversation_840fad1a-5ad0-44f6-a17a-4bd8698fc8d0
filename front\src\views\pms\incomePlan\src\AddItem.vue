<template>
  <BasicForm @register="registerForm" />
</template>

<script setup lang="ts">
import {
  h, nextTick, onMounted, reactive, ref, Ref,
} from 'vue';
import {
  BasicForm, useForm,
} from 'lyra-component-vue3';
import Api from '/@/api';
import dayjs from 'dayjs';

const emits = defineEmits([]);
const props = defineProps({
  detail: {
    type: Object,
    default: () => {
    },
  },
  type: {
    type: String,
    default: '',
  },
});
const [registerForm, formFunction] = useForm({
  layout: 'vertical',
  baseColProps: {
    span: 24,
  },
  schemas: [
    {
      field: 'productId',
      component: 'SelectModal',
      label: '选择产品',
      required: true,
      componentProps: ({ formModel }) => ({
        labelString: props.detail?.productName ?? '',
        valueString: props.detail?.productId ?? '',
        onChange: (params) => {
          // 如果没有值,清空productName,productNumber
          if (!params) {
            formModel.productName = '';
            formModel.productNumber = '';
          }
        },
        modalConfig: {
          title: '新增产品',
          tableConfig: {
            tableApi: (params) => new Api('/pms/projectIncome/getProductEstimatePage').fetch(params, '', 'GET'),
            beforeOk: (params) => {
              if (params.allSelect.length > 0) {
                // 新增的时候加入productName,productNumber
                formModel.productName = params.allSelect[0]?.name;
                formModel.productNumber = params.allSelect[0]?.number;
              }
              return params;
            },
          },

        },
      }),
    },
    {
      field: 'productName',
      component: 'Input',
      label: '产品名称',
      show: false,
    },
    {
      field: 'productNumber',
      component: 'Input',
      label: '产品名称',
      show: false,
    },
    {
      field: 'expectedContractYear',
      component: 'DatePicker',
      label: '预期合同年份',
      required: true,
      componentProps() {
        return {
          picker: 'year',
          valueFormat: 'YYYY',
        };
      },
    },
    {
      field: 'expectedSaleNumber',
      component: 'InputNumber',
      label: '预期销售数量',
      required: true,
      componentProps: {
        min: 1,
        step: 1,
        style: { width: '100%' },
      },
    },
    {
      field: 'fabricateHour',
      component: 'InputNumber',
      label: '制造工时',
      required: true,
      componentProps: {
        min: 1,
        step: 1,
        addonAfter: '小时',
        style: { width: '100%' },
      },
    },
    {
      field: 'expectedIncome',
      component: 'InputNumber',
      label: '预期收益',
      required: true,
      componentProps: {
        min: 0,
        addonAfter: '元',
        style: { width: '100%' },
      },
    },
    {
      field: 'remark',
      component: 'InputTextArea',
      label: '备注',
      componentProps: {
        rows: 4,
        showCount: true,
        maxlength: 500,
      },
    },
  ],
});

onMounted(() => {
  if (props.type === 'edit') {
    initForm();
  }
});

async function initForm() {
  let newData = {
    ...props.detail,
    expectedContractYear: dayjs(props.detail.expectedContractYear).format('YYYY'),
  };
  await formFunction.setFieldsValue(newData);
  await nextTick();
  await formFunction.clearValidate();
}

defineExpose({
  async getData() {
    const formData = await formFunction.validate();
    return {
      ...formData,
    };
  },
});
</script>

<style scoped lang="less"></style>
