package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * ContractCenterPlan Entity对象
 *
 * <AUTHOR>
 * @since 2024-10-17 09:32:48
 */
@TableName(value = "pmsx_contract_center_plan")
@ApiModel(value = "ContractCenterPlanEntity对象", description = "中心用人计划")
@Data

public class ContractCenterPlan extends  ObjectEntity  implements Serializable{

    @TableField(value = "cost_type_id")
    @ApiModelProperty(value = "成本类型id")
    private String costTypeId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_number")
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @TableField(value = "contract_name")
    private String contractName;

    /**
     * 用人单位代号
     */
    @ApiModelProperty(value = "用人单位代号")
    @TableField(value = "center_code")
    private String centerCode;

    /**
     * 用人单位名称
     */
    @ApiModelProperty(value = "用人单位名称")
    @TableField(value = "center_name")
    private String centerName;

    /**
     * 成本类型编号
     */
    @ApiModelProperty(value = "成本类型编号")
    @TableField(value = "cost_type_number")
    private String costTypeNumber;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    @TableField(value = "num")
    private Integer num;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @TableField(value = "year")
    private Date year;

}
