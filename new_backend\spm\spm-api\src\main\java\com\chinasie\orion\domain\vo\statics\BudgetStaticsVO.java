package com.chinasie.orion.domain.vo.statics;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(value = "BudgetStaticsVO对象", description = "预算总的统计")
public class BudgetStaticsVO {
    @ApiModelProperty(value = "预算")
    private BigDecimal budgetTotal;
    @ApiModelProperty(value = "预算列表")
    private List<BudgetViewStaticsVO> viewStaticsVOList ;
}
