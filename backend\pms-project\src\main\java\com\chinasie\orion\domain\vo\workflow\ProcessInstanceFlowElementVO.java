package com.chinasie.orion.domain.vo.workflow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 * @author: yk
 * @date: 2023/9/24 17:18
 * @description:
 */
@ApiModel("流程实例信息")
@Data
public class ProcessInstanceFlowElementVO {
    /**
     * 已完成节点
     */
    @ApiModelProperty(value = "已完成节点")
    Set<String> finishedFlowElementIds;

    /**
     * 执行中节点
     */
    @ApiModelProperty(value = "执行中节点")
    Set<String> executeFlowElementIds;
}
