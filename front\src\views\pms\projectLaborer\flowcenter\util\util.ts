export function _joinStr(list, key = '') {
  let res = '';
  if (list) {
    if (key && list.length > 0) {
      list.forEach((item) => {
        res += `${item[key]},`;
      });
    } else {
      list.forEach((item) => {
        res += `${item},`;
      });
    }
  }

  res = res.substring(0, res.length - 1);
  return res;
}

export function onHandleTransferUpdate(add, currentItem, userKey, idKey) {
  const noChange = add.removeItems.length === 0 && add.addItems.length === 0;
  if (noChange) {
    return false;
  }
  let users: string[] = currentItem[userKey] ? (currentItem[userKey].split(',') as string[]) : [];
  let ids: string[] = currentItem[idKey] ? (currentItem[idKey].split(',') as string[]) : [];
  if (add.addItems.length > 0) {
    add.addItems.forEach((item) => {
      users.push(item.name);
      ids.push(item.id);
    });
  }
  if (add.removeItems.length > 0) {
    add.removeItems.forEach((item) => {
      let fromIndex = users.indexOf(item.name);
      users.splice(fromIndex, 1);
      let idIndex = ids.indexOf(item.id);
      ids.splice(idIndex, 1);
    });
  }
  return {
    users,
    ids,
  };
}
