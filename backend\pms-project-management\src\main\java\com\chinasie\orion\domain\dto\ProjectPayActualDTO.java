package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.util.Date;
import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProjectPayActual DTO对象
 *
 * <AUTHOR>
 * @since 2024-07-04 00:48:18
 */
@ApiModel(value = "ProjectPayActualDTO对象", description = "实际成本金额")
@Data
@ExcelIgnoreUnannotated
public class ProjectPayActualDTO extends  ObjectDTO   implements Serializable{

    /**
     * 项目定义
     */
    @ApiModelProperty(value = "项目定义")
    @ExcelProperty(value = "项目定义 ", index = 0)
    private String psphi;

    /**
     * 对象
     */
    @ApiModelProperty(value = "对象")
    @ExcelProperty(value = "对象 ", index = 1)
    private String objnr;

    /**
     * 对象名称
     */
    @ApiModelProperty(value = "对象名称")
    @ExcelProperty(value = "对象名称 ", index = 2)
    private String postOne;

    /**
     * WBS编码
     */
    @ApiModelProperty(value = "WBS编码")
    @ExcelProperty(value = "WBS编码 ", index = 3)
    private String posid;

    /**
     * 对象货币值
     */
    @ApiModelProperty(value = "对象货币值")
    @ExcelProperty(value = "对象货币值 ", index = 4)
    private String wogbtr;

    /**
     * 过账日期
     */
    @ApiModelProperty(value = "过账日期")
    @ExcelProperty(value = "过账日期 ", index = 5)
    private Date budat;

    /**
     * 凭证抬头文本
     */
    @ApiModelProperty(value = "凭证抬头文本")
    @ExcelProperty(value = "凭证抬头文本 ", index = 6)
    private String bltxt;

    /**
     * 凭证编码
     */
    @ApiModelProperty(value = "凭证编码")
    @ExcelProperty(value = "凭证编码 ", index = 7)
    private String belnr;

    /**
     * 参考凭证编码
     */
    @ApiModelProperty(value = "参考凭证编码")
    @ExcelProperty(value = "参考凭证编码 ", index = 8)
    private String refbn;

    /**
     * 陈本要素
     */
    @ApiModelProperty(value = "陈本要素")
    @ExcelProperty(value = "陈本要素 ", index = 9)
    private String kstar;

    /**
     * 成本要素名称
     */
    @ApiModelProperty(value = "成本要素名称")
    @ExcelProperty(value = "成本要素名称 ", index = 10)
    private String ktext;

    /**
     * 冲转科目名
     */
    @ApiModelProperty(value = "冲转科目名")
    @ExcelProperty(value = "冲转科目名 ", index = 11)
    private String gkont;

    /**
     * 采购凭证
     */
    @ApiModelProperty(value = "采购凭证")
    @ExcelProperty(value = "采购凭证 ", index = 12)
    private String ebeln;

    /**
     * 采购订单文本
     */
    @ApiModelProperty(value = "采购订单文本")
    @ExcelProperty(value = "采购订单文本 ", index = 13)
    private String txzOne;

    /**
     * 公司
     */
    @ApiModelProperty(value = "公司")
    @ExcelProperty(value = "公司 ", index = 14)
    private String bukrs;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @ExcelProperty(value = "名称 ", index = 15)
    private String sgtxt;

    /**
     * 功能范围
     */
    @ApiModelProperty(value = "功能范围")
    @ExcelProperty(value = "功能范围 ", index = 16)
    private String fkber;

    /**
     * 年度
     */
    @ApiModelProperty(value = "年度")
    @ExcelProperty(value = "年度 ", index = 17)
    private String gjahr;

    /**
     * 数据更新时间
     */
    @ApiModelProperty(value = "数据更新时间")
    @ExcelProperty(value = "数据更新时间 ", index = 18)
    private Date insertTime;

    /**
     * 本次数据更新时间
     */
    @ApiModelProperty(value = "本次数据更新时间")
    @ExcelProperty(value = "本次数据更新时间 ", index = 19)
    private Date updateTime;




}
