package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * RequireInfo VO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@ApiModel(value = "RequireInfoVO对象", description = "需求单")
@Data
public class RequireInfoTotalVO {

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "未结算金额汇总（原币）")
    private BigDecimal unusedAmt;

    @ApiModelProperty(value = "子订单金额汇总（原币）")
    private BigDecimal usedAmt;


    @ApiModelProperty(value = "框架合同总金额（原币）")
    private BigDecimal finalPrice;


    @ApiModelProperty(value = "剩余金额（原币）")
    private BigDecimal leftOveramt;


    @ApiModelProperty(value = "已使用额度占比")
    private String usedAmtProp;


    @ApiModelProperty(value = "剩余金额占比")
    private String leftOveramtProp;


}
