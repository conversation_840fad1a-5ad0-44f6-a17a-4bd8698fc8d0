package com.chinasie.orion.controller.projectStatistics;

import com.chinasie.orion.domain.dto.projectStatistics.ProjectPurchaseStatisticsDTO;

import com.chinasie.orion.domain.vo.ProjectPurchaseOrderListInfoVO;
import com.chinasie.orion.domain.vo.projectStatistics.ProjectPurchaseStatisticsVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.projectStatistics.ProjectPurchaseStatisticsService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/projectPurchaseStatistics")
@Api(tags = "项目内采购统计")
public class ProjectPurchaseStatisticsController {
    @Autowired
    private ProjectPurchaseStatisticsService projectPurchaseStatisticsService;


    @ApiOperation(value = "采购状态分布统计")
    @RequestMapping(value = "/getProjectPurchaseStatusStatistics", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【采购状态分布统计】", type = "项目内报表", bizNo = "")
    public ResponseDTO<ProjectPurchaseStatisticsVO> getProjectPurchaseStatusStatistics(@RequestBody ProjectPurchaseStatisticsDTO projectPurchaseStatisticsDTO) throws Exception {
        ProjectPurchaseStatisticsVO rsp =  projectPurchaseStatisticsService.getProjectPurchaseStatusStatistics(projectPurchaseStatisticsDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "采购状态负责人统计")
    @RequestMapping(value = "/getProjectPurchaseRspUserStatistics", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【采购状态负责人统计】", type = "项目内报表", bizNo = "")
    public ResponseDTO<List<ProjectPurchaseStatisticsVO>> getProjectPurchaseRspUserStatistics(@RequestBody ProjectPurchaseStatisticsDTO projectPurchaseStatisticsDTO) throws Exception {
        List<ProjectPurchaseStatisticsVO> rsp =  projectPurchaseStatisticsService.getProjectPurchaseRspUserStatistics(projectPurchaseStatisticsDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "采购状态趋势统计")
    @RequestMapping(value = "/getProjectPurchaseChangeStatusStatistics", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【采购状态趋势统计】", type = "项目内报表", bizNo = "")
    public ResponseDTO< List<ProjectPurchaseStatisticsVO>> getProjectPurchaseChangeStatusStatistics( @RequestBody ProjectPurchaseStatisticsDTO projectPurchaseStatisticsDTO) throws Exception {
        List<ProjectPurchaseStatisticsVO> rsp =  projectPurchaseStatisticsService.getProjectPurchaseChangeStatusStatistics(projectPurchaseStatisticsDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "采购新增趋势统计")
    @RequestMapping(value = "/getProjectPurchaseCreateStatistics", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【采购新增趋势统计】", type = "项目内报表", bizNo = "")
    public ResponseDTO< List<ProjectPurchaseStatisticsVO>> getProjectPurchaseCreateStatistics( @RequestBody ProjectPurchaseStatisticsDTO projectPurchaseStatisticsDTO) throws Exception {
        List<ProjectPurchaseStatisticsVO> rsp =  projectPurchaseStatisticsService.getProjectPurchaseCreateStatistics(projectPurchaseStatisticsDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "采购分页查询")
    @RequestMapping(value = "/getProjectPurchasePages", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【采购分页】", type = "项目内报表", bizNo = "")
    public ResponseDTO<Page<ProjectPurchaseOrderListInfoVO>> getProjectPurchasePages(@RequestBody Page<ProjectPurchaseStatisticsDTO> pageRequest) throws Exception {
        Page<ProjectPurchaseOrderListInfoVO> rsp =  projectPurchaseStatisticsService.getProjectPurchasePages(pageRequest);
        return new ResponseDTO<>(rsp);
    }
}
