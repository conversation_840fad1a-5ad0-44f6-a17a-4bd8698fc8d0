package com.chinasie.orion.domain.vo;

import com.chinasie.orion.domain.dto.pas.TypeAttrValueDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/10/13 11:20
 */
@Data
public class TypeAndTypeAttrValueVO implements Serializable {

    @ApiModelProperty("属性值")
    private List<TypeAttrValueDTO> typeAttrValueDTOList;

    private String typeName;
}
