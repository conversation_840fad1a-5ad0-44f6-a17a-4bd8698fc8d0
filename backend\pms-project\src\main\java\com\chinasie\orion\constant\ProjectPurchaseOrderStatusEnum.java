package com.chinasie.orion.constant;

/**
 * @author: yk
 * @date: 2023/10/25 20:40
 * @description:
 */
public enum ProjectPurchaseOrderStatusEnum {
    CREATED(120, "未审核"),
    AUDITING(110, "审核中"),
    AUDITED(130, "已审核"),
    CLOSE(111, "已关闭"),
    ;


    private Integer status;

    private String desc;


    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    ProjectPurchaseOrderStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
