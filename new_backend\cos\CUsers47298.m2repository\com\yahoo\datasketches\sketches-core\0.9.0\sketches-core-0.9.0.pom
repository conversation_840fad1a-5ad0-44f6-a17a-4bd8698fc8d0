<?xml version="1.0" encoding="UTF-8"?>
<!-- Copyright 2015, Yahoo! Inc.
     Licensed under the terms of the Apache License 2.0.
     See LICENSE file at the project root for terms. -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>sketches-core</artifactId>
    <name>${project.parent.groupId}:${project.artifactId}</name>
    <description>Sketches-core contains the core algorithms used by other repositories in the DataSketches library.</description>

    <parent>
        <groupId>com.yahoo.datasketches</groupId>
        <artifactId>sketches</artifactId>
        <version>0.9.0</version>
    </parent>

    <dependencies>
        <dependency>
            <groupId>com.yahoo.datasketches</groupId>
            <artifactId>memory</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
          
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <relocations>
                                <relocation>
                                    <pattern>com.yahoo.memory</pattern>
                                    <shadedPattern>shaded.com.yahoo.memory</shadedPattern>
                                </relocation>
                            </relocations>
                            <shadedArtifactAttached>true</shadedArtifactAttached>
                            <shadedClassifierName>with-shaded-memory</shadedClassifierName>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
