<template>
  <OrionTable
    v-if="roleId"
    ref="tableRef"
    :options="tableOptions"
    class="pdmBasicTable"
    @selectionChange="selectionChange"
  >
    <template #toolbarLeft>
      <BasicButton
        v-if="isPower('PMS_XMXQ_container_06_01_button_01',powerData)"
        type="primary"
        icon="add"
        @click="addNode"
      >
        新增
      </BasicButton>
      <BasicButton
        v-if="isPower('PMS_XMXQ_container_06_01_button_02',powerData)"
        icon="sie-icon-shanchu"
        :disabled="selectedKeys.length===0"
        @click="handleRemove"
      >
        移除
      </BasicButton>
    </template>
    <template #modifyTime="{ text }">
      {{ dayjs(text).format('YYYY-MM-DD HH:mm:ss') }}
    </template>

    <template #action="{record}">
      <BasicTableAction
        :actions="actionsBtn"
        :record="record"
      />
    </template>
  </OrionTable>

  <!-- 查看详情弹窗 -->
  <checkDetails
    v-if="pageType==='page'"
    :data="nodeData"
  />
  <SelectUserModal
    v-if="pageType==='page'"
    :on-ok="ok"
    :tree-data-api="treeDataApi"
    :default-select-user-data="selectUserData"
    :default-expand-all="true"
    @register="modalRegister"
  />
</template>
<script lang="ts">
import {
  defineComponent, h, inject, onMounted, reactive, Ref, ref, toRefs, watch,
} from 'vue';
import { message, Modal } from 'ant-design-vue';
import checkDetails from './checkmodal.vue';
import dayjs from 'dayjs';
import {
  BasicButton, BasicTableAction, isPower, OrionTable, SelectUserModal, useModal,
} from 'lyra-component-vue3';
import { addPeopleApi, deletePeopleApi, peoplePageApi } from '/@/views/pms/projectLaborer/api/projectList';
//   import { peoplePageApi, deletePeopleApi } from '/@/views/pmsx/api/projectList';
import Api from '/@/api';

export default defineComponent({
  name: '',
  components: {
    BasicTableAction,
    checkDetails,
    /* 选择用户 */
    SelectUserModal,
    BasicButton,
    OrionTable,
  },
  props: {
    roleId: {
      type: String,
      default: '',
    },
    projectId: {
      type: String,
      default: '',
    },
    pageType: {
      type: String,
      default: 'page',
    },
  },

  setup(props) {
    const [modalRegister, { openModal }] = useModal();
    const tableRef = ref(null);
    const selectUserData = ref([]);
    const powerData = inject('powerData');
    const state = reactive({
      powerData: [],
      //   成员id
      projectRoleId: '',
      /* 编辑send */
      editdataSource: {},
      /* 多选 */
      selectedRowKeys: [],
      addNodeModalData: {},
      /* 选择行id */
      selectedRows: [],
      showVisible: false,
      /* 简易弹窗提醒消息 */
      message: '',
      nodeData: {},
      searchData: {},
      params: {},
    });
    state.powerData = inject('powerData');

    watch(
      () => props.roleId,
      async (value) => {
        await getFormData();
      },
    );

    onMounted(() => {

    });

    const getFormData = async () => {
      tableRef.value?.reload();
    };
    const checkData2 = (data) => {
      state.nodeData = {
        ...[JSON.parse(JSON.stringify(data))],
      };
    };
    /* 检查选择条数fn */
    const lengthCheckHandle = () => {
      if (state.selectedRows.length > 1) {
        message.warning('请选择一条数据进行操作');
        return true;
      }
      if (state.selectedRows.length === 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };

    /* 新建项目 */
    const addNode = () => {
      if (!props.roleId) {
        message.warning('请选择一个项目角色进行操作');
        return;
      }
      selectUserHandle();
    };
    const selectUserHandle = () => {
      openModal();
    };
    /* 新建项目成功回调 */
    const successSave = () => {
      getFormData();
    };

    const ok = (userData) => {
      const params = userData.map((item) => ({
        projectRoleId: props.roleId,
        userId: item.id,
        name: item.name,
        number: item.code,
        projectId: props.projectId,
        remark: item.remark,
      }));
      addPeopleApi(params)
        .then(() => {
          getFormData();
        });
    };

    // 生成 queryCondition
    function getListParams(params) {
      if (params.searchConditions) {
        return {
          ...params,
          queryCondition: params.searchConditions.map((item) => ({
            column: item?.[0]?.field,
            type: 'like',
            link: 'or',
            value: item?.[0]?.values?.[0],
          })),
        };
      }
      return params;
    }

    const tableOptions = {
      deleteToolButton: 'add|delete|enable|disable',
      rowSelection: {},
      isFilter2: true,
      smallSearchField: ['name', 'number'],
      filterConfigName: 'PMS_PROJECTMANAGE_RESOURCEMANAGE_MEMBERMANAGE',
      api(params) {
        return peoplePageApi(getListParams({
          ...params,
          query: {
            projectRoleId: props.roleId,
            projectId: props.projectId,
          },
        }));
      },
      columns: [
        {
          title: '姓名',
          dataIndex: 'name',
          minWidth: 80,
          customRender({ record, text }) {
            if (isPower('PMS_XMXQ_container_06_01_button_04', state.powerData)) {
              return h('span', {
                class: 'action-btn',
                onClick: () => checkData2(record),
              }, text);
            }
            return text;
          },
        },
        {
          title: '工号',
          dataIndex: 'number',
          key: 'number',
          width: '110px',
          align: 'left',
          slots: { customRender: 'number' },
        },
        {
          title: '所在部门',
          dataIndex: 'orgName',
          width: '220px',
        },

        {
          title: '电话',
          dataIndex: 'mobile',
          width: '110px',
          slots: { customRender: 'mobile' },
        },
        {
          title: '邮箱',
          dataIndex: 'email',
          width: '170px',
          slots: { customRender: 'email' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 100,
          fixed: 'right',
          slots: { customRender: 'action' },
        },
      ],
    };

    const actionsBtn: any[] = [
      {
        text: '移除',
        isShow: () => isPower('PMS_XMXQ_container_06_01_button_03', state.powerData),
        modal(record: any) {
          return deletePeopleApi([record.id]).then(() => {
            message.success('移除成功');
            getFormData();
          });
        },
      },
    ];

    const selectedKeys: Ref<string[]> = ref([]);

    function handleRemove() {
      Modal.confirm({
        content: '您确定要移除成员吗？',
        onOk() {
          return deletePeopleApi(selectedKeys.value).then(() => {
            message.success('移除成功');
            getFormData();
          });
        },
      });
    }

    function selectionChange({ keys }) {
      selectedKeys.value = keys;
    }

    return {
      tableRef,
      ...toRefs(state),
      isPower,
      confirm,
      dayjs,
      successSave,
      modalRegister,
      selectUserHandle,
      ok,
      selectionChange,
      selectedKeys,
      selectUserData,
      addNode,
      actionsBtn,
      handleRemove,
      treeDataApi: () => new Api('/pmi/organization/treeListPage').fetch(
        {
          orders: [
            {
              asc: false,
              column: '',
            },
          ],
          pageNum: 0,
          pageSize: 0,
          query: { status: 1 },
        },
        '',
        'POST',
      ),
      tableOptions,
      powerData,
    };
  },
});
</script>
<style lang="less" scoped>
</style>
