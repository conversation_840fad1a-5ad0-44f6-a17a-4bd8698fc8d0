<template>
  <div ref="wrapper">
    <a-dropdown
      :getPopupContainer="getPopupContainer"
      :trigger="['click']"
    >
      <button
        class="current-button flex-te"
        :style="{backgroundColor:data?.data?.fill}"
        :title="data?.data?.label"
      >
        {{ data?.data?.label }}
        <CheckCircleOutlined
          v-if="data?.data?.isFinish"
          class="current-button-icon"
        />
      </button>
      <template #overlay>
        <ProgressTime
          v-if="data?.data?.actions?.length"
          :actions="data?.data?.actions"
        />
      </template>
    </a-dropdown>
  </div>
</template>

<script lang="ts">
import {
  defineComponent, inject, onMounted, ref,
} from 'vue';
import { Dropdown } from 'ant-design-vue';
import { CheckCircleOutlined } from '@ant-design/icons-vue';
import ProgressTime from './ProgressTime.vue';

export default defineComponent({
  name: 'ProgressNode',
  components: {
    ADropdown: Dropdown,
    ProgressTime,
    CheckCircleOutlined,
  },

  setup() {
    const data:any = ref({});
    const getNode:any = inject('getNode');
    const wrapper = ref();

    onMounted(() => {
      data.value = getNode();
    });

    function getPopupContainer() {
      return document.getElementsByClassName('life-container-current-2024-05-20')[0];
    }

    return {
      wrapper,
      data,
      getPopupContainer,
    };
  },
});
</script>
<style lang="less" scoped>
.current-button{
  width: 200px;
  height: 50px;
  font-size: 18px;
  font-weight:bold;
  border-radius: 10px;
  border: 0;
  color: #fff;
  cursor: pointer;
  position: relative;
  padding-right: 33px;

  .current-button-icon{
    position: absolute;
    right: 10px;
    top: 17px;
  }
}
</style>
