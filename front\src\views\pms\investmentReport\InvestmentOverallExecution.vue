<template>
  <Layout
    :options="{ body: { scroll: true } }"
    class="source"
  >
    <div
      v-loading="loadStatus"
      class="details-content-item"
    >
      <!--      <div class="content-title">-->
      <!--        <div-->
      <!--          class="information-title information-title-flex"-->
      <!--        >-->
      <!--          <span>投资计划总体执行情况查询报表{{ searchTitle }}</span>-->
      <!--          <span>单位：万元</span>-->
      <!--        </div>-->
      <!--      </div>-->
      <OrionTable
        ref="tableRef"
        class="card-list-table"
        :options="baseTableOption"
        @smallSearch="smallSearch"
        @smallSearchChange="smallSearchChange"
        @selectionChange="selectionChange"
        @filterOpenChange="filterOpenChange"
      >
        <template #toolbarLeft>
          <BasicButton
            icon="sie-icon-daochu"
            type="primary"
            :disabled="selectionKeys.length===0"
            @click="exportTableByIds"
          >
            导出所选
          </BasicButton>
          <BasicButton
            icon="sie-icon-daochu"
            type="primary"
            @click="exportTable"
          >
            导出全部
          </BasicButton>
        </template>

        <template #headerCell="{column}">
          <template v-if="column.key==='totalYDoRate'">
            <span style="margin-right: 5px;">年度投资执行率</span>
            <Tooltip
              placement="bottom"
            >
              <template #title>
                <span style="display: flex;text-align: center "> 年度投资计划执行/调整后年度投资计划<br>若没有调整<br>年度投资计划执行/年度投资计划</span>
              </template>
              <Icon icon="sie-icon-attr" />
            </Tooltip>
          </template>
          <template v-else-if="column.key==='carryForwardInvestment'">
            <span style="margin-right: 5px;">结投转资</span>
            <Tooltip
              placement="bottom"
            >
              <template #title>
                <span style="display: flex;text-align: center "> 年度投资计划执行-调整后年度投资计划<br>若没有调整<br>年度投资计划执行-年度投资计划</span>
              </template>
              <Icon icon="sie-icon-attr" />
            </Tooltip>
          </template>
          <template v-else-if="column.key==='cutOffGiveY'">
            <span style="margin-right: 5px;">至Y年累计下达投资计划</span>
            <Tooltip
              placement="bottom"
              title="截止Y年下达调整后投资计划之和"
            >
              <Icon icon="sie-icon-attr" />
            </Tooltip>
          </template>
          <template v-else-if="column.key==='cutOffCompleteY'">
            <span style="margin-right: 5px;">至Y年累计完成投资计划</span>
            <Tooltip
              placement="bottom"
              title="截止Y-1年累计完成投资+Y年1-M月实际完成投资"
            >
              <Icon icon="sie-icon-attr" />
            </Tooltip>
          </template>
          <template v-else-if="column.key==='cutOffCompleteYRate'">
            <span style="margin-right: 5px;">至Y年累计完成率</span>
            <Tooltip
              placement="bottom"
              title="至Y年累计完成投资计划/批复概算"
            >
              <Icon icon="sie-icon-attr" />
            </Tooltip>
          </template>
          <template v-else-if="column.key==='implementedEstimate'">
            <span style="margin-right: 5px;">待执行概算</span>
            <Tooltip
              placement="bottom"
              title="批复概算额-截止Y年累计完成投资额"
            >
              <Icon icon="sie-icon-attr" />
            </Tooltip>
          </template>
        </template>

        <template #filter>
          <div class="filter-wrap">
            <div class="padding-bottom-10">
              <TableHeadSearch
                v-show="showSearch"
                ref="searchRef"
                @click-search="clickSearch"
              />
            </div>
          </div>
        </template>
        <template
          v-if="!isTable"
          #otherContent="{dataSource}"
        >
          <div
            v-if="dataSource.length"
            ref="cardGrid"
            class="card-grid"
            :style="{'grid-template-columns': `repeat(${gridNum}, 1fr)`}"
          />
          <Empty
            v-else
            class="w-full h-full flex flex-ver flex-ac flex-pc"
            :image="Empty.PRESENTED_IMAGE_SIMPLE"
          />
        </template>
      </OrionTable>
    </div>

    <!--创建项目-->
  </Layout>
</template>

<script setup lang="ts">
import {
  h, onMounted, onUnmounted, Ref, ref, unref, watch, nextTick, computed,
} from 'vue';
import {
  BasicButton,
  BasicButtonGroup,
  DataStatusTag, downloadByData,
  Icon,
  isPower,
  Layout,
  OrionTable,
} from 'lyra-component-vue3';
import { useRoute, useRouter } from 'vue-router';

import {
  Empty, message, Modal, Tooltip,
} from 'ant-design-vue';

import { useUserStore } from '/@/store/modules/user';

import dayjs from 'dayjs';
import { query } from 'min-dom';
import {
  pagetotaldo,
} from './api';
import TableHeadSearch from './utils/InvestmentOverallExecution.vue';
import { calculatePercentage } from './utils/index';

const route = useRoute();
const router = useRouter();
const isTable: Ref<boolean> = ref(false);
const tableRef: Ref = ref();
const cardGrid: Ref = ref();
const dataCode = ref(route.query.dataCode);
const loading: Ref<boolean> = ref(false);
const loadStatus:Ref<boolean> = ref(false);
function formatNumToDecimals(inputValue) {
  let inputNumber = parseFloat(inputValue);
  if (isNaN(inputNumber)) {
    return '';
  }
  return inputNumber.toFixed(2).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}
async function filterOpenChange() {
  if (route.query.dataCode) {
    await router.replace({
      name: route.name,
    });
    return;
  }
  showSearch.value = !showSearch.value;
}

const columns = [
  // {
  //   title: '计划公司',
  //   dataIndex: 'companyName',
  // },
  {
    title: '年度',
    dataIndex: 'yearName',
    fixed: 'left',
  },
  {
    title: '项目编码',
    dataIndex: 'projectNumber',
    fixed: 'left',
  },
  {
    title: '项目名称',
    dataIndex: 'projectName',
    fixed: 'left',
  },
  {
    title: '项目状态',
    width: 100,
    dataIndex: 'projectStatusName',
    // customRender({ record }) {
    //   return h(DataStatusTag, {
    //     statusData: record.dataStatus,
    //   });
    // },
  },
  {
    title: '项目处室',
    dataIndex: 'rspDeptName',
  },
  {
    title: '项目负责人',
    dataIndex: 'rspUserName',
  },

  {
    title: '年度投资计划',
    dataIndex: 'total',
    align: 'right',
    customRender({ text }) {
      return formatNumToDecimals(text);
    },
  },
  {
    title: '调整后年度投资计划',
    dataIndex: 'totalChange',
    align: 'right',
    titleHelpMessage: '文档创建完成后自动生成编号',
    customRender({ text }) {
      return formatNumToDecimals(text);
    },

  },
  {
    title: '年度计划执行',
    dataIndex: 'totalDo',
    align: 'right',
    customRender({ text }) {
      return formatNumToDecimals(text);
    },
  },
  {
    title: '年度投资执行率',
    align: 'right',
    key: 'totalYDoRate',
    customRender({ record }) {
      //   record.totalChange为0的话就是没有调整
      if (Number(record.totalChange) === 0) {
        // 年度投资计划执行 / 年度投资计划
        return (calculatePercentage(record.totalDo, record.total));
      }
      // 年度投资计划执行 / 调整后年度投资计划
      return (calculatePercentage(record.totalDo, record.totalChange));
    },
  },
  {
    title: '结投转资',
    align: 'right',
    key: 'carryForwardInvestment',
    customRender({ record }) {
    //   record.totalChange为0的话就是没有调整
      if (Number(record.totalChange) === 0) {
      // 年度投资计划执行 - 年度投资计划
        return formatNumToDecimals((record.totalDo - record.total));
      }
      // 年度投资计划执行 - 调整后年度投资计划
      return formatNumToDecimals((record.totalDo - record.totalChange));
    },
  },
  {
    title: '至Y年累计下达投资计划',
    align: 'right',
    width: 200,
    dataIndex: 'cutOffGiveY',
  },
  {
    title: '至Y年累计完成投资计划',
    align: 'right',
    width: 200,
    dataIndex: 'cutOffCompleteY',
  },
  {
    title: '至Y年累计完成率',
    key: 'cutOffCompleteYRate',
    customRender({ record }) {
      // 至Y年累计完成投资计划 / 概算金额
      return (calculatePercentage(record.cutOffCompleteY, record.estimate));
    },
  },
  {
    title: '待执行概算',
    align: 'right',
    key: 'implementedEstimate',
    customRender({ record }) {
      //  概算金额 - 至Y年累计完成投资计划
      return formatNumToDecimals((record.estimate - record.cutOffCompleteY));
    },
  },
];
const showSearch = ref(!route.query.dataCode);
const keyword = ref<string>('');
const selectionKeys = ref<string[]>([]);
const searchRef: Ref = ref();
// 数据穿透类型名称
const searchTitle = computed(() => {
  if (dataCode.value === '1') {
    return '（全公司本年度投资计划）';
  } if (dataCode.value === '2') {
    return '（本年度重大投资项目投资计划）';
  }
  return '';
});
const baseTableOption = {
  rowSelection: {},
  columns,
  smallSearchField: ['name', 'number'],
  api: async (params) => {
    const data = await pagetotaldo({
      orders: params.orders,
      pageNum: params.pageNum,
      pageSize: params.pageSize,
      query: {
        pointProject: route.query.dataCode === '2',
        name: unref(keyword),
        ...unref(searchRef.value.modal),
        rspDeptId: searchRef.value.modal.rspDeptId ? searchRef.value.modal.rspDeptId.join() : '',
        yearName: searchRef.value.modal.yearName ? searchRef.value.modal.yearName.join('-') : '',
        companyName: searchRef.value.modal.companyName ? searchRef.value.modal.companyName.join() : '',
      },
    });
    loading.value = false;
    // setTimeout(() => { showSearch.value = false; }, 300);
    return data;
  },
  showToolButton: false,
  isFilter2: true,
  // immediate: !!route.query.dataCode,
};

onUnmounted(() => {
  window.removeEventListener('resize', onResize);
});

watch(() => isTable.value, (val) => {
  if (val) {
    document.querySelector('.table-other-content')
      .setAttribute('style', 'height:0px;');
  }
});

const gridNum: Ref<number> = ref();
async function clickSearch() {
  await nextTick();
  tableRef.value.reload();
}
function onResize() {
  gridNum.value = parseInt(window.innerWidth / 350);
}

function exportTable() {
  let isEmpty = tableRef.value.getDataSource().length === 0;
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk: async () => {
      loadStatus.value = true;
      const res = await downloadByData('/pms/investmentscheme-report/exporetotaldo/excel', {
        isEmpty,
        name: unref(keyword),
        pointProject: route.query.dataCode === '2',
        rspDeptId: searchRef.value.modal.rspDeptId.join(','),
        yearName: searchRef.value.modal.yearName ? searchRef.value.modal.yearName.join('-') : '',
        companyName: searchRef.value.modal.companyName ? searchRef.value.modal.companyName.join() : '',
      }, '', 'POST', true, false, '导出处理完成，现在开始下载');
      loadStatus.value = false;
    },
  });
}
function selectionChange({ rows, keys }) {
  selectionKeys.value = keys;
}

function exportTableByIds() {
  let isEmpty = tableRef.value.getDataSource().length === 0;
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk: async () => {
      loadStatus.value = true;
      const res = await downloadByData('/pms/investmentscheme-report/exporetotaldo/excel', {
        isEmpty,
        ids: selectionKeys.value,
      }, '', 'POST', true, false, '导出处理完成，现在开始下载');
      loadStatus.value = false;
    },
  });
}

// 模糊搜索
function smallSearch(val) {
  keyword.value = val;
  updateList();
}

function smallSearchChange(val) {
  keyword.value = val;
}

function updateList() {
  tableRef.value?.reload();
}
</script>

<style scoped lang="less">

.padding-bottom-10{
  padding-bottom:  ~`getPrefixVar('content-margin')`;
}
.card-grid {
  display: grid;
  gap: 16px 20px;
}

.hide-card :deep(.table-other-content ) {
  height: 0 !important;
}

.collect {
  cursor: pointer;
}

:deep(.card-list-table) {
  .form-wrap{
    margin-bottom:  ~`getPrefixVar('content-margin')`;
  }
  .ant-btn-group {
    margin-left: auto;

    .ant-btn + .ant-btn {
      margin-left: 0;
    }

    & + .card-list-table {
      width: auto;
      flex: 0;

      .ant-input-search {
        width: 220px;
      }
    }
  }
}
.content-title{
  padding: 20px 20px 0 20px;
  padding: var(--ant-button-margin);
}
:deep(.information-title) {
  font-size: 18px;
  font-weight: 500;
  color: rgba(0,0,0,0.85);
  line-height: 28px;
  position: relative;
  padding-left: 10px;
  border-bottom: 1px solid #cccccc;
  padding-bottom: 5px;
  &:before {
    content: '';
    height: 18px;
    width: 4px;
    background: ~`getPrefixVar('primary-color') `;
    display: inline-block;
    position: absolute;
    top: 5px;
    left: 0px;
  }
}

:deep(.information-title-flex){
  display: flex;
  width: 100%;
  justify-content: space-between;
  span+span{
    font-size: 14px;
    color: #959191;
  }
}
</style>
