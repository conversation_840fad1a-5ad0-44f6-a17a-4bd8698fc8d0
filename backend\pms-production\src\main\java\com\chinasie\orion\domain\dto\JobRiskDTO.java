package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * JobRisk DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-09 15:47:53
 */
@ApiModel(value = "JobRiskDTO对象", description = "作业与风险关联")
@Data
@ExcelIgnoreUnannotated
public class JobRiskDTO extends  ObjectDTO   implements Serializable{

    /**
     * 风险ID
     */
    @ApiModelProperty(value = "风险ID")
    @ExcelProperty(value = "风险ID ", index = 0)
    private String jobId;

    /**
     * 功能位置
     */
    @ApiModelProperty(value = "功能位置")
    @ExcelProperty(value = "功能位置 ", index = 1)
    private String functionalLocation;

    /**
     * 风险号
     */
    @ApiModelProperty(value = "风险号")
    @ExcelProperty(value = "风险号 ", index = 2)
    private String riskNumber;

    /**
     * 风险类型
     */
    @ApiModelProperty(value = "风险类型")
    @ExcelProperty(value = "风险类型 ", index = 3)
    private String riskType;

    /**
     * 风险长文本
     */
    @ApiModelProperty(value = "风险长文本")
    @ExcelProperty(value = "风险长文本 ", index = 4)
    private String riskText;

    /**
     * 风险描述
     */
    @ApiModelProperty(value = "风险描述")
    @ExcelProperty(value = "风险描述 ", index = 5)
    private String riskDesc;

    @ApiModelProperty(value = "订单号")
    private String jobCode;
    @ApiModelProperty(value = "风险分析")
    private String riskAnaly;
    @ApiModelProperty(value = "计划工厂")
    private String planFactory;
    @ApiModelProperty(value = "风险代码")
    private String riskCode;
    @ApiModelProperty(value = "md5_value")
    private String encryKey;
}
