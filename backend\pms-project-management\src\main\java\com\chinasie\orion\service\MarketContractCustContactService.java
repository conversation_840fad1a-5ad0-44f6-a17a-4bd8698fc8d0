package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.MarketContractCustContactDTO;
import com.chinasie.orion.domain.entity.MarketContract;
import com.chinasie.orion.domain.entity.MarketContractCustContact;
import com.chinasie.orion.domain.vo.MarketContractCustContactVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;


/**
 * <p>
 * MarketContractCustContact 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-06 16:48:09
 */
public interface MarketContractCustContactService extends OrionBaseService<MarketContractCustContact> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    MarketContractCustContactVO detail(String id, String pageCode) ;

    /**
     * 保存合同客户的相关联系人
     *
     * @param contacts 联系人
     * @param contract 合同
     */
    void saveContractContacts(List<MarketContractCustContact> contacts, MarketContract contract);

    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) ;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<MarketContractCustContactVO> pages(Page<MarketContractCustContactDTO> pageRequest) ;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<MarketContractCustContactVO> vos) ;
}
