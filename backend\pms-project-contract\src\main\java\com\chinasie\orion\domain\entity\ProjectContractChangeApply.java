package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * ProjectContractChangeApply Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-25 10:55:50
 */
@TableName(value = "pms_project_contract_change_apply")
@ApiModel(value = "ProjectContractChangeApply对象", description = "项目合同变更申请信息")
@Data
public class ProjectContractChangeApply extends ObjectEntity implements Serializable {

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_number")
    private String contractNumber;

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    @TableField(value = "contract_id")
    private String contractId;

    /**
     * 变更申请单号
     */
    @ApiModelProperty(value = "变更申请单号")
    @TableField(value = "number")
    private String number;

    /**
     * 申请人id
     */
    @ApiModelProperty(value = "申请人id")
    @TableField(value = "apply_user_id")
    private String applyUserId;

    /**
     * 申请日期
     */
    @ApiModelProperty(value = "申请日期")
    @TableField(value = "apply_date")
    private Date applyDate;

    /**
     * 变更原因
     */
    @ApiModelProperty(value = "变更原因")
    @TableField(value = "change_reason")
    private String changeReason;

    /**
     * 合同基本信息是否变更
     */
    @ApiModelProperty(value = "合同基本信息是否变更")
    @TableField(value = "is_contract_info")
    private Boolean isContractInfo;

    /**
     * 甲方签约主体是否变更
     */
    @ApiModelProperty(value = "甲方签约主体是否变更")
    @TableField(value = "is_our_signed_main")
    private Boolean isOurSignedMain;

    /**
     * 乙方签约主体是否变更
     */
    @ApiModelProperty(value = "乙方签约主体是否变更")
    @TableField(value = "is_supplier_signed_main")
    private Boolean isSupplierSignedMain;

    /**
     * 合同节点是否变更
     */
    @ApiModelProperty(value = "合同节点是否变更")
    @TableField(value = "is_pay_node")
    private Boolean isPayNode;

    /**
     * 合同附件是否变更
     */
    @ApiModelProperty(value = "合同附件是否变更")
    @TableField(value = "is_contract_file")
    private Boolean isContractFile;

    /**
     * 父变更id
     */
    @ApiModelProperty(value = "父变更id")
    @TableField(value = "parent_id")
    private String parentId;

}
