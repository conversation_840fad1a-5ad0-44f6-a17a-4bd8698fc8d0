package com.chinasie.orion.domain.dto.job;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/11/14/16:52
 * @description:
 */
@Data
public class SaveJobManageDTO implements Serializable {
    @ApiModelProperty(value = "大修伦次")
    @NotEmpty(message = "大修伦次不能为空")
    private String repairRound;

    @ApiModelProperty(value = "大修业务组织ID")
    @NotEmpty(message = "所属大修业务组织不能为空")
    private String repairOrgId;
    @ApiModelProperty(value = "需要加入的对象列表")
    private List<NewJobMangeDTO> addList;

    @ApiModelProperty(value = "需要移除的工单编号列表")
    private List<String> delNumberList;
}
