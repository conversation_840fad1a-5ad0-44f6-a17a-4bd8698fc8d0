package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * ContractExtendInfo VO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@ApiModel(value = "ContractExtendInfoVO对象", description = "合同拓展信息")
@Data
public class ContractExtendInfoVO extends ObjectVO implements Serializable {

    /**
     * 采购组织
     */
    @ApiModelProperty(value = "采购组织")
    private String procurementOrgName;


    /**
     * 采购组织ID
     */
    @ApiModelProperty(value = "采购组织ID")
    private String procurementOrgId;


    /**
     * 采购组
     */
    @ApiModelProperty(value = "采购组")
    private String procurementGroupName;


    /**
     * 采购组ID
     */
    @ApiModelProperty(value = "采购组ID")
    private String procurementGroupId;


    /**
     * 商务负责人
     */
    @ApiModelProperty(value = "商务负责人")
    private String businessRspUser;


    /**
     * 商务负责人ID
     */
    @ApiModelProperty(value = "商务负责人ID")
    private String businessRspUserId;


    /**
     * 技术负责人
     */
    @ApiModelProperty(value = "技术负责人")
    private String technicalRspUser;


    /**
     * 技术负责人ID
     */
    @ApiModelProperty(value = "技术负责人ID")
    private String technicalRspUserId;


    /**
     * 推荐依据
     */
    @ApiModelProperty(value = "推荐依据")
    private String recommendationBasis;


    /**
     * 节省总金额（RMB）
     */
    @ApiModelProperty(value = "节省总金额（RMB）")
    private BigDecimal negotiateSaveAmount;


    /**
     * 渠道优化节省金额
     */
    @ApiModelProperty(value = "渠道优化节省金额")
    private BigDecimal sumSaveAmount;


    /**
     * 谈判节省金额
     */
    @ApiModelProperty(value = "谈判节省金额")
    private BigDecimal compareSaveAmount;


    /**
     * 与立项相比节省金额
     */
    @ApiModelProperty(value = "与立项相比节省金额")
    private BigDecimal channelSaveAmount;


    /**
     * 优化采购节省金额
     */
    @ApiModelProperty(value = "优化采购节省金额")
    private BigDecimal optimizeSaveAmount;


    /**
     * 是否办理履约保证金
     */
    @ApiModelProperty(value = "是否办理履约保证金")
    private Boolean isProcessAmount;


    /**
     * 保证金支付方式
     */
    @ApiModelProperty(value = "保证金支付方式")
    private String prcessAmountPayWay;


    /**
     * 保证金
     */
    @ApiModelProperty(value = "保证金")
    private String prcessAmount;


    /**
     * 账户名称
     */
    @ApiModelProperty(value = "账户名称")
    private String accountName;


    /**
     * 银行账号
     */
    @ApiModelProperty(value = "银行账号")
    private String bankName;


    /**
     * 开户银行
     */
    @ApiModelProperty(value = "开户银行")
    private String bankAccount;


    /**
     * 银行代码
     */
    @ApiModelProperty(value = "银行代码")
    private String bankCode;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    private String mainTableId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;
}
