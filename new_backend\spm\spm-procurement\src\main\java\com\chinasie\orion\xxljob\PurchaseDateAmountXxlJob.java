package com.chinasie.orion.xxljob;


import com.chinasie.orion.constant.MsgHandlerConstant;
import com.chinasie.orion.domain.entity.EmailRecord;
import com.chinasie.orion.domain.vo.ContractXxlJobVO;
import com.chinasie.orion.repository.ContractInfoMapper;
import com.chinasie.orion.msc.api.MscBuildHandlerManager;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class PurchaseDateAmountXxlJob {

    @Resource
    ContractInfoMapper contractInfoMapper;

    @Resource
    MscBuildHandlerManager mscBuildHandlerManager;

    private static final String DATE_TYPE = "date";
    private static final String MONEY_TYPE = "money";

    @XxlJob("purchaseDateMountJobHandler")
    public void purchaseDateMountJobHandler() {
        List<ContractXxlJobVO> amountContract = contractInfoMapper.getContractXxlJobAmount();
        List<String> amountIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(amountContract)){
            amountIds = contractInfoMapper.getRequireInfo(amountContract.stream().map(ContractXxlJobVO::getId).collect(Collectors.toList()));
        }
        //排除不满足条件的数据
        List<String> finalAmountIds = amountIds;
        amountContract= amountContract.stream().filter(item -> !finalAmountIds.contains(item.getId())).collect(Collectors.toList());
        List<ContractXxlJobVO> dateContract = contractInfoMapper.getContractXxlJobDate();
        List<EmailRecord> emailRecord = contractInfoMapper.getEmailRecord();
        List<EmailRecord> dateRecord = new ArrayList<>();
        List<EmailRecord> moneyRecord = new ArrayList<>();
        List<ContractXxlJobVO> sendData = new ArrayList<>();
        sendData.addAll(amountContract);
        sendData.addAll(dateContract);
        emailRecord.forEach(item->{
            if (DATE_TYPE.equals(item.getType())){
                dateRecord.add(item);
            }else {
                moneyRecord.add(item);
            }
        });
        //判断哪些数据需要发送邮件,已发送过的类型移除
        for (ContractXxlJobVO contractXxlJobVO : amountContract) {
            String id = contractXxlJobVO.getId();
            String warningMoney = contractXxlJobVO.getWarningMoney();
            for (EmailRecord record : moneyRecord) {
                if (record.getDataId().equals(id)&&record.getTypeContent().equals(warningMoney)){
                    sendData.remove(contractXxlJobVO);
                    break;
                }
            }
        }

        for (ContractXxlJobVO contractXxlJobVO : dateContract) {
            String id = contractXxlJobVO.getId();
            String warningDay = contractXxlJobVO.getWarningDay();
            for (EmailRecord record : dateRecord) {
                if (record.getDataId().equals(id)&&record.getTypeContent().equals(warningDay)){
                    sendData.remove(contractXxlJobVO);
                    break;
                }
            }
        }

        List<EmailRecord> emailRecordList = new ArrayList<>();
        sendData.forEach(item->{
            mscBuildHandlerManager.send(item, MsgHandlerConstant.NODE_CONTRACT_DEADLINE);
            EmailRecord record = new EmailRecord();
            record.setDataId(item.getId());
            String id = UUID.randomUUID().toString().replaceAll("-", "");
            record.setId(id);
            if (StringUtils.hasText(item.getWarningDay())){
                record.setTypeContent(item.getWarningDay());
                record.setType(DATE_TYPE);
            }else {
                record.setTypeContent(item.getWarningMoney());
                record.setType(MONEY_TYPE);
            }
            emailRecordList.add(record);
        });

        if (!emailRecordList.isEmpty()){
            contractInfoMapper.insertBatch(emailRecordList);
        }
    }
}
