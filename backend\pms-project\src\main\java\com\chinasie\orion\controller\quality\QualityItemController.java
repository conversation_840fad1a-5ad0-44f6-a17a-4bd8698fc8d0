package com.chinasie.orion.controller.quality;

import com.chinasie.orion.domain.dto.quality.QualityItemAffirmDTO;
import com.chinasie.orion.domain.dto.quality.QualityItemDTO;
import com.chinasie.orion.domain.dto.quality.QualityItemToProductDTO;
import com.chinasie.orion.domain.request.quality.QualityStepVO;
import com.chinasie.orion.domain.vo.quality.QualityItemVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.feign.PasFeignService;
import com.chinasie.orion.feign.dto.QualityStepDTO;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.pdm.api.domain.vo.ProductEstimateMaterialVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.quality.QualityItemService;
import com.chinasie.orion.service.quality.QualityItemWFService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * QualityItem 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08 14:13:46
 */
@RestController
@RequestMapping("/qualityItem")
@Api(tags = "质量管控项")
public class QualityItemController {

    @Autowired
    private QualityItemService qualityItemService;

    @Resource(name = "QualityItemWFService")
    private QualityItemWFService qualityItemWFService;

    @Resource
    private PasFeignService pasFeignService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【质量管控项】数据详情【{{#number}}】", type = "QualityItem", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<QualityItemVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        QualityItemVO rsp = qualityItemService.detail(id, pageCode);
        LogRecordContext.putVariable("number", rsp.getNumber());
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add/{projectId}", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【质量管控项】数据", type = "QualityItem", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> create(@RequestBody List<String> qualityIds, @PathVariable("projectId")String projectId) throws Exception {
        Boolean rsp = qualityItemService.create(qualityIds,projectId);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param qualityItemDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【质量管控项】数据【{{#qualityItemDTO.number}}】", type = "QualityItem", subType = "编辑", bizNo = "{{#qualityItemDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody QualityItemDTO qualityItemDTO) throws Exception {
        Boolean rsp = qualityItemService.edit(qualityItemDTO);
        LogRecordContext.putVariable("number", qualityItemDTO.getNumber());
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【质量管控项】数据", type = "QualityItem", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = qualityItemService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【质量管控项】数据", type = "QualityItem", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = qualityItemService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【质量管控项】分页数据", type = "QualityItem", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<QualityItemVO>> pages(@RequestBody Page<QualityItemDTO> pageRequest) throws Exception {
        Page<QualityItemVO> rsp = qualityItemService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 待办跳转携带消息id查询
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "待办跳转携带消息id查询")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【质量管控项】待办跳转携带消息数据", type = "QualityItem", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page/messageId", method = RequestMethod.POST)
    public ResponseDTO<Page<QualityItemVO>> pagesByMessageId(@RequestBody Page<QualityItemDTO> pageRequest) throws Exception {
        Page<QualityItemVO> rsp = qualityItemService.pagesByMessageId(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("质量管控项导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出【质量管控项】数据", type = "QualityItem", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions,
                              HttpServletResponse response,
                              @RequestParam(value = "projectId", required = false) String projectId) throws Exception {
        qualityItemService.exportByExcel(searchConditions, response, projectId);
    }


    @ApiOperation("提交")
    @PostMapping(value = "/commit")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量提交【质量管控项】数据", type = "QualityStep", subType = "批量提交", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> commit(@RequestBody List<String> ids){
        Boolean commit = qualityItemWFService.commit(ids);
        return new ResponseDTO<>(commit);
    }

    @ApiOperation("确定")
    @PostMapping(value = "/affirm")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量确定【质量管控项】数据", type = "QualityStep", subType = "批量确定", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> affirm(@RequestBody List<String> ids){
        Boolean affirm = qualityItemWFService.affirm(ids);
        return new ResponseDTO<>(affirm);
    }

    @ApiOperation("驳回")
    @PostMapping(value = "/reject")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量驳回【质量管控项】数据", type = "QualityStep", subType = "批量驳回", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> reject(@RequestBody List<String> ids){
        Boolean commit = qualityItemWFService.reject(ids);
        return new ResponseDTO<>(commit);
    }

    @ApiOperation("完成确认")
    @PostMapping(value = "/complete")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量完成确认【质量管控项】数据", type = "QualityStep", subType = "批量完成确认", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> complete(@RequestBody QualityItemAffirmDTO qualityItemAffirmDTO){
        Boolean commit = qualityItemWFService.complete(qualityItemAffirmDTO);
        return new ResponseDTO<>(commit);
    }

    /**
     * 质控模板引入质控点
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "质量控制项关联项目计划")
    @RequestMapping(value = "/correlation/{id}", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】【质量管控项】质量控制项关联项目计划", type = "QualityItem", subType = "关联计划", bizNo = "{{#id}")
    public ResponseDTO<Boolean> correlation(@PathVariable("id") String id,
                                          @RequestBody List<String> schemeIds) throws Exception {
        Boolean rsp =  qualityItemWFService.correlation(id, schemeIds);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "质控实施分页")
    @LogRecord(success = "【{USER{#logUserId}}】查询【质量管控项】数据", type = "QualityStep", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/stepPage", method = RequestMethod.POST)
    public ResponseDTO<Page<QualityStepVO>> stepPage(@RequestBody Page<QualityStepDTO> pageRequest) throws Exception {
        ResponseDTO<Page<QualityStepVO>> pages;
        try {
//            QualityStepDTO query = pageRequest.getQuery();
//            if (query != null && StrUtil.isNotBlank(query.getProjectId())) {
//                String projectId = query.getProjectId();
//                LambdaQueryWrapperX<QualityItem> lambda = new LambdaQueryWrapperX<>();
//                lambda.in(QualityItem::getProjectId, projectId);
//                List<QualityItem> list = qualityItemService.list(lambda);
//                if(CollectionUtil.isNotEmpty(list)) {
//                    List<String> numbers = list.stream()
//                            .filter(item -> item.getQualityMeasureCode() != null && !item.getQualityMeasureCode().isEmpty())
//                            .map(QualityItem::getQualityMeasureCode)
//                            .collect(Collectors.toList());
//                    if(CollectionUtil.isNotEmpty(numbers)) {
//                        pageRequest.getQuery().setQualityItemCodes(numbers);
//                    }
//                }
//            }
//
            pages = pasFeignService.pages(pageRequest);
        } catch (Exception e) {
            throw new BaseException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR, "质控实施数据失败");
        }
        return pages;
    }

//    /**
//     * 分页头部按钮权限列表获取
//     *
//     * @param pageRequest
//     * @return
//     * @throws Exception
//     */
//    @ApiOperation(value = "分页头部按钮权限列表获取")
//    @Transactional(rollbackFor = Exception.class)
//    @RequestMapping(value = "/pagesAuth", method = RequestMethod.POST)
//    public ResponseDTO<List<String>> pagesAuth(@RequestBody Page<QualityItemDTO> pageRequest) throws Exception {
//        List<String> rsp = qualityItemService.qualityItemHeaderAuth(pageRequest);
//        return new ResponseDTO<>(rsp);
//    }


    @ApiOperation("质量管控项（生成文档）")
    @PostMapping(value = "/export/getDocument")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】生成【质量管控项】文档", type = "QualityItem", subType = "生成文档", bizNo = "")
    public ResponseDTO<Object> getDocument(@RequestParam(value = "templateId", required = false) String templateId,
                            @RequestParam(value = "projectId", required = false) String projectId) throws Exception {
        return new ResponseDTO<>(qualityItemService.getDocument(templateId, projectId));
    }

    @ApiOperation("质量管控项（产品列表）")
    @PostMapping(value = "/getProductList")
    @LogRecord(success = "【{USER{#logUserId}}】获取【质量管控项】产品列表", type = "ProductEstimateMaterial", subType = "获取产品列表", bizNo = "")
    public ResponseDTO<List<ProductEstimateMaterialVO>>  getProductList(@RequestParam(value = "projectId") String projectId) throws Exception {
        return new ResponseDTO<>(qualityItemService.getProductList(projectId));
    }


    /**
     * 质量控制项关联项目信息
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "质量控制项关联项目信息")
    @RequestMapping(value = "/related/product", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】【质量管控项】质量控制项关联项目信息", type = "QualityItemToProduct", subType = "关联项目", bizNo = "")
    public ResponseDTO<Boolean> relatedProduct(@RequestBody QualityItemToProductDTO qualityItemToProductDTO) throws Exception {
        Boolean rsp = qualityItemService.relatedProduct(qualityItemToProductDTO);
        return new ResponseDTO<>(rsp);
    }
}
