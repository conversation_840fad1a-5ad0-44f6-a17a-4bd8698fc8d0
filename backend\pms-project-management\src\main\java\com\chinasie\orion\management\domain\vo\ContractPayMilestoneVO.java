package com.chinasie.orion.management.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ContractPayMilestone VO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@ApiModel(value = "ContractPayMilestoneVO对象", description = "合同支付里程碑（计划）")
@Data
public class ContractPayMilestoneVO extends ObjectVO implements Serializable {

    /**
     * 里程碑业务描述
     */
    @ApiModelProperty(value = "里程碑业务描述")
    private String milestoneDesc;


    /**
     * 是否涉及境外付款
     */
    @ApiModelProperty(value = "是否涉及境外付款")
    private Boolean inOutPayment;


    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型")
    private String contractType;


    /**
     * 支付类型
     */
    @ApiModelProperty(value = "支付类型")
    private String paymentType;


    /**
     * 预计付款时间
     */
    @ApiModelProperty(value = "预计付款时间")
    private Date estPaymentDate;


    /**
     * 附件要求
     */
    @ApiModelProperty(value = "附件要求")
    private String attachmentReq;


    /**
     * 支付比例
     */
    @ApiModelProperty(value = "支付比例")
    private String paymentRatio;


    /**
     * 合同约定支付金额
     */
    @ApiModelProperty(value = "合同约定支付金额")
    private BigDecimal contractAgreedPayment;


    /**
     * 价格属性总价是否固定
     */
    @ApiModelProperty(value = "价格属性总价是否固定")
    private Boolean priceTotalFixed;


    /**
     * 开票类型
     */
    @ApiModelProperty(value = "开票类型")
    private String invoiceType;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    private String mainTableId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;
}
