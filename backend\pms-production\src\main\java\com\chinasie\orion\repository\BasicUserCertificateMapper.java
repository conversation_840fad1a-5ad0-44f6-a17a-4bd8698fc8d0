package com.chinasie.orion.repository;
import com.chinasie.orion.domain.entity.BasicUserCertificate;
import org.apache.ibatis.annotations.Mapper;

import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;


/**
 * <p>
 * BasicUserCertificate Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05 22:07:29
 */
@Mapper
public interface BasicUserCertificateMapper extends  OrionBaseMapper  <BasicUserCertificate> {

    /**
     * 查询还有一年到期的人员证书
     * @return 结果
     */
    @Select("SELECT * FROM pmsx_basic_user_certificate WHERE DATE(valid_to_date) = DATE(DATE_ADD(CURRENT_DATE, INTERVAL 1 YEAR)) and logic_status = 1;")
    List<BasicUserCertificate> getNotifyList();
}

