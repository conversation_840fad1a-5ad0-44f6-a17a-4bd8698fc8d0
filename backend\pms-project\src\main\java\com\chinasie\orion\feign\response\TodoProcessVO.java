package com.chinasie.orion.feign.response;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: yk
 * @date: 2023/9/19 9:54
 * @description:
 */
@ApiModel(value = "TodoProcessVO对象", description = "待我审的")
@Data
public class TodoProcessVO extends ObjectVO implements Serializable {
    @ApiModelProperty(value = "业务Id")
    private String businessId;
    /**
     * businessKey
     */
    @ApiModelProperty(value = "businessKey")
    private String businessKey;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    private Date startTime;


    /**
     * 到达日期
     */
    @ApiModelProperty(value = "到达日期")
    private Date arriveTime;


    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    private Date endTime;

    /**
     * 流程实例id
     */
    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;


    /**
     * 流程定义key
     */
    @ApiModelProperty(value = "流程定义key")
    private String processDefinitionKey;

    /**
     * 流程定义id
     */
    @ApiModelProperty(value = "流程定义id")
    private String processDefinitionId;

    /**
     * 流程定义名称
     */
    @ApiModelProperty(value = "流程定义名称")
    private String processDefinitionName;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String state;

    /**
     * 发起人Id
     */
    @ApiModelProperty(value = "发起人Id")
    private String applyUserId;


    /**
     * 发起人名称
     */
    @ApiModelProperty(value = "发起人名称")
    private String applyUserName;

    /**
     * 业务名称
     */
    @ApiModelProperty(value = "业务名称")
    private String businessName;

    /**
     * 消息url
     */
    @ApiModelProperty(value = "消息url")
    private String messageUrl;

    /**
     * 当前节点
     */
    @ApiModelProperty(value = "当前节点")
    private String currentNode;

    /**
     * 当前处理人
     */
    @ApiModelProperty(value = "当前处理人")
    private String currentAssigneeName;

    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    private String taskId;

    @ApiModelProperty(value = "单据号")
    private String number;

    @ApiModelProperty(value = "工时类型名称")
    private String workHoursTypeName;

    @ApiModelProperty(value = "标题")
    private String title;
}
