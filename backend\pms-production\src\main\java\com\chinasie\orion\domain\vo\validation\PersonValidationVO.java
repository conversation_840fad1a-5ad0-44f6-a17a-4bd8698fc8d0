package com.chinasie.orion.domain.vo.validation;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/08/07/16:07
 * @description:
 */
@Data
public class PersonValidationVO  implements Serializable {
    @ApiModelProperty("用户工号")
    private String userCode;
    @ApiModelProperty("用户名称")
    private String userName;
    @ApiModelProperty("未验证通过的原因")
    private String reason;

}

