package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.lock.LockInfo;
import com.baomidou.lock.LockTemplate;
import com.baomidou.lock.executor.RedisTemplateLockExecutor;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.base.api.domain.entity.DeptDO;
import com.chinasie.orion.base.api.domain.entity.UserDO;
import com.chinasie.orion.base.api.domain.vo.SimpleUserVO;
import com.chinasie.orion.base.api.service.UserBaseApiService;
import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.BillingCompanyEnum;
import com.chinasie.orion.conts.*;
import com.chinasie.orion.dict.IncomePlanDict;
import com.chinasie.orion.dict.MsgNumberDict;
import com.chinasie.orion.domain.dto.IncomePlanDTO;
import com.chinasie.orion.domain.dto.IncomePlanDataExportDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.IncomePlanVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.management.domain.entity.CustomerInfo;
import com.chinasie.orion.management.service.CustomerInfoService;
import com.chinasie.orion.msc.api.MscBuildHandlerManager;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.IncomePlanMapper;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <p>
 * IncomePlan 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29 19:14:00
 */
@Service
@Slf4j
public class IncomePlanServiceImpl extends  OrionBaseServiceImpl<IncomePlanMapper, IncomePlan>   implements IncomePlanService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;
    @Autowired
    private UserBaseApiService userBaseApiService;
    @Resource
    private DictBo dictBo;
    @Autowired
    private IncomePlanDataControlService incomePlanDataControlService;
    @Autowired
    private IncomePlanDataService incomePlanDataService;

    @Autowired
    private IncomePlanDataLockService incomePlanDataLockService;

    @Autowired
    private AdvancePaymentInformationService advancePaymentInformationService;

    @Autowired
    private BillingAccountInformationService billingAccountInformationService;

    @Autowired
    private IncomeProvisionInformationService incomeProvisionInformationService;

    @Resource
    private IncomePlanMapper incomePlanMapper;

    @Autowired
    private ContractMilestoneService contractMilestoneService;

    @Autowired
    private CustomerInfoService customerInfoService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ClassRedisHelper classRedisHelper;

    @Autowired
    private PersonRoleMaintenanceDetailService personRoleMaintenanceDetailService;

    @Autowired
    private PersonRoleMaintenanceService personRoleMaintenanceService;

    @Resource
    private MscBuildHandlerManager mscBuildHandlerManager;

    @Autowired
    private MarketContractService marketContractService;
    @Autowired
    private LockTemplate lockTemplate;

    @Autowired
    private ContractOurSignedSubjectService contractOurSignedSubjectService;

    @Autowired
    private ContractSupplierSignedSubjectService contractSupplierSignedSubjectService;




    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public IncomePlanVO detail(String id, String pageCode) throws Exception {
        IncomePlan incomePlan =this.getById(id);
        IncomePlanVO result = BeanCopyUtils.convertTo(incomePlan,IncomePlanVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param incomePlanDTO
     */
    @Override
    public  String create(IncomePlanDTO incomePlanDTO) throws Exception {
        IncomePlan incomePlan =BeanCopyUtils.convertTo(incomePlanDTO,IncomePlan::new);
        this.save(incomePlan);

        String rsp=incomePlan.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param incomePlanDTO
     */
    @Override
    public Boolean edit(IncomePlanDTO incomePlanDTO) throws Exception {
        IncomePlan incomePlan =BeanCopyUtils.convertTo(incomePlanDTO,IncomePlan::new);

        this.updateById(incomePlan);

        String rsp=incomePlan.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        incomePlanDataService.remove(new LambdaQueryWrapperX<>(IncomePlanData.class).in(IncomePlanData::getIncomePlanId,ids));
        incomePlanDataLockService.remove(new LambdaQueryWrapperX<>(IncomePlanDataLock.class).in(IncomePlanDataLock::getIncomePlanId,ids));
        incomePlanDataControlService.remove(new LambdaQueryWrapperX<>(IncomePlanDataControl.class).in(IncomePlanDataControl::getIncomePlanId,ids));
        advancePaymentInformationService.remove(new LambdaQueryWrapperX<>(AdvancePaymentInformation.class).in(AdvancePaymentInformation::getIncomePlanId,ids));
        billingAccountInformationService.remove(new LambdaQueryWrapperX<>(BillingAccountInformation.class).in(BillingAccountInformation::getIncomePlanId,ids));
        incomeProvisionInformationService.remove(new LambdaQueryWrapperX<>(IncomeProvisionInformation.class).in(IncomeProvisionInformation::getIncomePlanId,ids));
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<IncomePlanVO> pages( Page<IncomePlanDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<IncomePlan> condition = new LambdaQueryWrapperX<>( IncomePlan. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            condition.leftJoin(UserDO.class,UserDO::getId,IncomePlan::getIssuePerson);
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(IncomePlan::getCreateTime);


        Page<IncomePlan> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), IncomePlan::new));

        PageResult<IncomePlan> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<IncomePlanVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());

        if(CollUtil.isEmpty(page.getContent())){
           return pageResult;
        }
        List<IncomePlanVO> vos = BeanCopyUtils.convertListTo(page.getContent(), IncomePlanVO::new);
        setEveryName(vos);
        List<String>  ids = vos.stream().map(IncomePlanVO::getId).collect(Collectors.toList());
        List<IncomePlan> incomePlans = incomePlanMapper.getDataTotal(ids);
        Map<String,IncomePlan> incomePlanMap = incomePlans.stream().collect(Collectors.toMap(IncomePlan::getId,Function.identity()));
        for(IncomePlanVO in:vos){
            IncomePlan incomePlan = incomePlanMap.get(in.getId());
            if(ObjectUtil.isNotEmpty(incomePlan)){
                in.setIncomePlanCount(ObjectUtil.isNotEmpty(incomePlan.getIncomePlanCount()) ? incomePlan.getIncomePlanCount() : 0);
                in.setIncomePlanAmt(ObjectUtil.isNotEmpty(incomePlan.getIncomePlanAmt()) ? incomePlan.getIncomePlanAmt() : BigDecimal.ZERO);
            }
        }
        pageResult.setContent(vos);
        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "收入计划填报导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", IncomePlanDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            IncomePlanExcelListener excelReadListener = new IncomePlanExcelListener();
        EasyExcel.read(inputStream,IncomePlanDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<IncomePlanDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("收入计划填报导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<IncomePlan> incomePlanes =BeanCopyUtils.convertListTo(dtoS,IncomePlan::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::IncomePlan-import::id", importId, incomePlanes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<IncomePlan> incomePlanes = (List<IncomePlan>) orionJ2CacheService.get("pmsx::IncomePlan-import::id", importId);
        log.info("收入计划填报导入的入库数据={}", JSONUtil.toJsonStr(incomePlanes));

        this.saveBatch(incomePlanes);
        orionJ2CacheService.delete("pmsx::IncomePlan-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::IncomePlan-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<String> ids, HttpServletResponse response) throws Exception {


        response.setContentType("application/vnd.ms-excel");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("收入计划填报数据导出.xlsx", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName+".xlsx");

        // 创建 ExcelWriter，准备写入数据
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();
        List<IncomePlan> incomePlans = this.listByIds(ids);
        List<String> incomePlanIds = incomePlans.stream().map(IncomePlan::getId).collect(Collectors.toList());
        List<IncomePlanData> incomePlanDataList = incomePlanDataService.list(new LambdaQueryWrapperX<>(IncomePlanData.class)
        .in(IncomePlanData::getIncomePlanId,incomePlanIds));
        Map<String,List<IncomePlanData>> map = incomePlanDataList.stream().collect(Collectors.groupingBy(IncomePlanData::getIncomePlanId));
        int i = 0;
        for(IncomePlan incomePlan:incomePlans){
            if(CollUtil.isNotEmpty(map.get(incomePlan.getId()))) {
                WriteSheet writeSheet1 = EasyExcel.writerSheet(i,incomePlan.getWorkTopicsName()).head(IncomePlanDataExportDTO.class).build();
                List<IncomePlanDataExportDTO> dtos = BeanCopyUtils.convertListTo(map.get(incomePlan.getId()), IncomePlanDataExportDTO::new);
                excelWriter.write(dtos, writeSheet1);
                i++;
            }
        }
        excelWriter.finish();
    }

    @Override
    public void  setEveryName(List<IncomePlanVO> vos) throws Exception {
        if(CollUtil.isEmpty(vos)){
            return;
        }
        List<String> userIds= vos.stream().map(IncomePlanVO::getIssuePerson).filter(item->StrUtil.isNotBlank(item)).collect(Collectors.toList());
        List<SimpleUserVO> simpleUserVOS = userBaseApiService.getUserByIds(userIds);
        Map<String,String> userMap = simpleUserVOS.stream().collect(Collectors.toMap(SimpleUserVO::getId,SimpleUserVO::getName));
        Map<String, String> dictMap=  dictBo.getDictValue(IncomePlanDict.CONTROL_TYPE);
        Date twoMonthsAgo = DateUtil.offsetMonth(new Date(), -2);
        SimpleDateFormat originalFormat = new SimpleDateFormat("yyyy-MM");
        String dateFormat = originalFormat.format(twoMonthsAgo);
        vos.forEach(vo->{
            if(StrUtil.isNotBlank(vo.getIssuePerson())){
                vo.setIssuePersonName(userMap.get(vo.getIssuePerson()));
            }
            if(StrUtil.isNotBlank(vo.getLockStatus())){
                vo.setLockStatusName(dictMap.get(vo.getLockStatus()));
            }
            if(vo.getWorkTopics().compareTo(dateFormat)<=0){
                vo.setIsAuthority("2");
            }else{
                vo.setIsAuthority("1");
            }
        });

    }

    @Override
    public Boolean pushData(String date) throws ParseException {
        if(StrUtil.isBlank(date)){
            throw new PMSException(PMSErrorCode.PMS_ERR, "收入计划年月不能为空");
        }
        String userId = CurrentUserHelper.getCurrentUserId();
        SimpleDateFormat originalFormat = new SimpleDateFormat("yyyy-MM");
        SimpleDateFormat dataFormat = new SimpleDateFormat("yyyy年MM月收入计划");
        Date dates = originalFormat.parse(date);
        IncomePlan incomePlan = new IncomePlan();
        incomePlan.setIssueTime(new Date());
        incomePlan.setIncomePlanType(IncomePlanTypeEnum.COMPILE.getStatus());
        incomePlan.setIssuePerson(CurrentUserHelper.getCurrentUserId());
        incomePlan.setWorkTopics(date);
        incomePlan.setWorkTopicsName(dataFormat.format(dates));
        incomePlan.setLockStatus(IncomePlanControlEnum.UNCONTROL.getStatus());
        this.save(incomePlan);
        incomePlanDataControlService.saveIncomePlanDataControl(incomePlan.getId());
        List<IncomePlanData> mscData = new ArrayList<>();
        List<IncomePlanData> incomePlanDataList = saveContractMilestone(dates,incomePlan.getId());
        List<IncomePlanData> incomelList = saveLastMonthData(dates,incomePlan.getId());
        if(CollUtil.isNotEmpty(incomePlanDataList)) {
            mscData.addAll(incomePlanDataList);
        }
        if(CollUtil.isNotEmpty(incomelList)) {
            mscData.addAll(incomelList);
        }
        sendMsg(incomePlan,mscData);
        return true;
    }

    public void sendMsg(IncomePlan incomePlan ,List<IncomePlanData> mscData){
        if(CollUtil.isEmpty(mscData)){
            return;
        }
        List<String> centerIds =  mscData.stream().map(IncomePlanData::getExpertiseCenter).collect(Collectors.toList());
        List<String> stationIds = mscData.stream().map(IncomePlanData::getExpertiseStation).collect(Collectors.toList());
        List<PersonRoleMaintenanceDetail>  all = new ArrayList<>();
       List<PersonRoleMaintenanceDetail> centerDetailList = personRoleMaintenanceDetailService.list(new LambdaQueryWrapperX<>(PersonRoleMaintenanceDetail.class)
                .leftJoin(PersonRoleMaintenance.class,PersonRoleMaintenance::getId,PersonRoleMaintenanceDetail::getMianTableId)
                .in(PersonRoleMaintenance::getExpertiseCenter,centerIds).eq(PersonRoleMaintenanceDetail::getPersonType,"中心审核人员"));
        List<PersonRoleMaintenanceDetail> stationDetailList = personRoleMaintenanceDetailService.list(new LambdaQueryWrapperX<>(PersonRoleMaintenanceDetail.class)
                .leftJoin(PersonRoleMaintenance.class,PersonRoleMaintenance::getId,PersonRoleMaintenanceDetail::getMianTableId)
                .in(PersonRoleMaintenance::getExpertiseStation,stationIds).eq(PersonRoleMaintenanceDetail::getPersonType,"专业所审核人员"));
        if(CollUtil.isNotEmpty(centerDetailList)){
            all.addAll(centerDetailList);
        }
        if(CollUtil.isNotEmpty(stationDetailList)){
            all.addAll(stationDetailList);
        }
        List<String> ids = all.stream().map(PersonRoleMaintenanceDetail::getPersonId).distinct().collect(Collectors.toList());
        ids.forEach(item->{
            incomePlan.setUserId(item);
            mscBuildHandlerManager.send(incomePlan, MsgNumberDict.NODE_INCOME_PLAN,incomePlan.getWorkTopicsName());
        });

    }

    public List<IncomePlanData> saveContractMilestone(Date date, String incomePlanId) {
        LambdaQueryWrapperX<ContractMilestone> query = new LambdaQueryWrapperX(ContractMilestone.class);
        query.select(ContractMilestone::getId, ContractMilestone::getContractId, ContractMilestone::getCusPersonId, ContractMilestone::getProjectCode,
                ContractMilestone::getTaxRate, ContractMilestone::getMilestoneName, ContractMilestone::getContractNumber, ContractMilestone::getIsProvisionalEstimate,
                ContractMilestone::getExpectAcceptDate, ContractMilestone::getExceptAcceptanceAmt, ContractMilestone::getMileIncomeType,
                ContractMilestone::getExceptInvoiceDate, ContractMilestone::getMilestoneProvisionalEstimateAmt, ContractMilestone::getMilestoneAdvanceAmt,
                ContractMilestone::getMilestoneAmt, ContractMilestone::getUndertDept, ContractMilestone::getOfficeDept, ContractMilestone::getActualMilestoneAmt,
                ContractMilestone::getWbsCode, ContractMilestone::getTechRspUser);
        query.eq(ContractMilestone::getMilestoneType, 1);
        //query.eq(ContractMilestone::getParentId,"");
        query.and(q -> q.and(s -> s.isNull(ContractMilestone::getIncomePlanCode).le(ContractMilestone::getExceptInvoiceDate, DateUtil.endOfMonth(date)).eq(ContractMilestone::getIsProvisionalEstimate, 0))
                .or(
                        m -> m.between(ContractMilestone::getExceptInvoiceDate, DateUtil.beginOfMonth(date), DateUtil.endOfMonth(date)).isNull(ContractMilestone::getIncomePlanCode)
                                .eq(ContractMilestone::getIsProvisionalEstimate, 1)));
        List<ContractMilestone> contractMilestones = contractMilestoneService.list(query);
        List<IncomePlanData> incomePlanDataList = new ArrayList<>();
        List<BillingAccountInformation> billingAccountInformationList = new ArrayList<>();
        List<String> contractIds = contractMilestones.stream().map(ContractMilestone::getContractId).collect(Collectors.toList());
        if (CollUtil.isEmpty(contractIds)) {
            return incomePlanDataList;
        }
        List<MarketContract> marketContracts = marketContractService.list(new LambdaQueryWrapperX<>(MarketContract.class).select(MarketContract::getId, MarketContract::getName).in(MarketContract::getId, contractIds));

        List<ContractOurSignedSubject> subjects = contractOurSignedSubjectService.list(new LambdaQueryWrapperX<>(ContractOurSignedSubject.class)
                .select(ContractOurSignedSubject::getContractId,ContractOurSignedSubject::getCusContractNumber).in(ContractOurSignedSubject::getContractId,contractIds));

        List<ContractSupplierSignedSubject> signedSubjectList = contractSupplierSignedSubjectService.list(new LambdaQueryWrapperX<>(ContractSupplierSignedSubject.class)
                .select(ContractSupplierSignedSubject::getContractId,ContractSupplierSignedSubject::getSignedMainName).in(ContractSupplierSignedSubject::getContractId,contractIds));

        Map<String, String> subjectMap = subjects.stream()
                .collect(Collectors.groupingBy(
                        ContractOurSignedSubject::getContractId,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                groupList -> groupList.get(0).getCusContractNumber() // 取第一个对象的Name字段
                        )
                ));
        Map<String, String> signedSubjectMap = signedSubjectList.stream()
                .collect(Collectors.groupingBy(
                        ContractSupplierSignedSubject::getContractId,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                groupList -> groupList.get(0).getSignedMainName() // 取第一个对象的Name字段
                        )
                ));

        Map<String, MarketContract> marketContractMap = marketContracts.stream().collect(Collectors.toMap(MarketContract::getId, Function.identity()));
        if (CollUtil.isNotEmpty(contractMilestones)) {

            List<String> cusPersonIds = new ArrayList();
            List<String> ids = new ArrayList();
            List<String> projectNumbers = new ArrayList();
            for (ContractMilestone c : contractMilestones) {
                cusPersonIds.add(c.getCusPersonId());
                ids.add(c.getId());
                if (StrUtil.isNotEmpty(c.getProjectCode())) {
                    projectNumbers.add(c.getProjectCode());
                }
            }
            List<ContractMilestone> childContractMilestones = contractMilestoneService.list(new LambdaQueryWrapperX<>(ContractMilestone.class)
                    .select(ContractMilestone::getId, ContractMilestone::getProjectCode,
                            ContractMilestone::getTaxRate, ContractMilestone::getWbsCode, ContractMilestone::getParentId
                            , ContractMilestone::getTechRspUser, ContractMilestone::getExceptAcceptanceAmt).in(ContractMilestone::getParentId, ids));
            Map<String, List<ContractMilestone>> childMap = childContractMilestones.stream().collect(Collectors.groupingBy(ContractMilestone::getParentId));
            List<String> childProjectNumbers = childContractMilestones.stream().filter(item -> StrUtil.isNotBlank(item.getProjectCode())).map(ContractMilestone::getProjectCode).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(childProjectNumbers)) {
                projectNumbers.addAll(childProjectNumbers);
            }
            cusPersonIds = cusPersonIds.stream().distinct().collect(Collectors.toList());
            projectNumbers = projectNumbers.stream().distinct().collect(Collectors.toList());
            Map<String, CustomerInfo> customerInfoMap = new HashMap<>();
            List<CustomerInfo> customerInfoList = customerInfoService.list(new LambdaQueryWrapperX<>(CustomerInfo.class).select(CustomerInfo::getId, CustomerInfo::getGroupInOut).in(CustomerInfo::getId, cusPersonIds));
            if (CollUtil.isNotEmpty(customerInfoList)) {
                customerInfoMap = customerInfoList.stream().collect(Collectors.toMap(CustomerInfo::getId, Function.identity()));
            }
            Map<String, Project> projectMap = new HashMap<>();
            if (CollUtil.isNotEmpty(projectNumbers)) {
                projectMap = projectService.list(new LambdaQueryWrapperX<>(Project.class).select(Project::getNumber, Project::getId, Project::getResPerson, Project::getProjectType).in(Project::getNumber, projectNumbers)).stream().collect(Collectors.toMap(Project::getNumber, Function.identity()));
            }
            for (ContractMilestone contractMilestone : contractMilestones) {
                IncomePlanData incomePlanData = new IncomePlanData();
                incomePlanData.setTaxRate(contractMilestone.getTaxRate().toString());
                incomePlanData.setIncomeWbsNumber(contractMilestone.getWbsCode());
                incomePlanData.setMilestoneId(contractMilestone.getId());
                incomePlanData.setMilestoneName(contractMilestone.getMilestoneName());
                incomePlanData.setContractId(contractMilestone.getContractId());
                incomePlanData.setContractNumber(contractMilestone.getContractNumber());
                incomePlanData.setPartyADeptId(contractMilestone.getCusPersonId());
                if(StrUtil.isNotBlank(subjectMap.get(contractMilestone.getContractId()))){
                    incomePlanData.setPartyAContractNumber(subjectMap.get(contractMilestone.getContractId()));
                }
                String billingCompany = signedSubjectMap.get(contractMilestone.getContractId());
                if(StrUtil.isNotBlank(billingCompany)){
                    if(billingCompany.equals(BillingCompanyEnum.SRG_NING.getCode())){
                        incomePlanData.setBillingCompany(BillingCompanyEnum.SRG_SU.getCode());
                    }else {
                        incomePlanData.setBillingCompany(billingCompany);
                    }
                }
                incomePlanData.setIncomePlanId(incomePlanId);
                incomePlanData.setRepeatCount(0);
                CustomerInfo customerInfo = customerInfoMap.get(incomePlanData.getPartyADeptId());
                if (ObjectUtil.isNotEmpty(customerInfo)) {
                    incomePlanData.setInternalExternal(customerInfo.getGroupInOut());
                    incomePlanData.setIndustry(customerInfo.getIndustry());
                }
                MarketContract m = marketContractMap.get(incomePlanData.getContractId());
                incomePlanData.setContractName(m.getName());
                incomePlanData.setContractNumber(m.getNumber());
                incomePlanData.setLockStatus(IncomePlanLockEnum.UNLOCK.getStatus());
                incomePlanData.setDataSource(IncomePlanDataSourceEnum.CONTRACT_MILESTONE.getValue());
                incomePlanData.setDataVersion(IncomePlanTypeEnum.COMPILE.getStatus());
                if ("1".equals(contractMilestone.getIsProvisionalEstimate())) {
                    //暂估
                    incomePlanData.setIncomeConfirmType(IncomeConfirmTypeEnum.PROVISIONAL_INCOME.getValue());
                    incomePlanData.setEstimateInvoiceDate(contractMilestone.getExpectAcceptDate());
                    incomePlanData.setEstimateAmt(contractMilestone.getExceptAcceptanceAmt());
                } else {
                    //非暂估
                    if (StrUtil.equals(contractMilestone.getMileIncomeType(), IncomeMilestoneEnum.PROCESS_MONEY.getValue())
                            || StrUtil.equals(contractMilestone.getMileIncomeType(), IncomeMilestoneEnum.WARRANTY_MONEY.getValue())) {
                        incomePlanData.setIncomeConfirmType(IncomeConfirmTypeEnum.PROGRESS_PAYMENT_INVOICE.getValue());

                    }
                    if (StrUtil.equals(contractMilestone.getMileIncomeType(), IncomeMilestoneEnum.ADVANCE_MONEY.getValue())) {
                        incomePlanData.setIncomeConfirmType(IncomeConfirmTypeEnum.ADVANCE_PAYMENT_INVOICE.getValue());
                        incomePlanData.setIncomePlanAmt(BigDecimal.ZERO);
                    }
                    incomePlanData.setEstimateInvoiceDate(contractMilestone.getExceptInvoiceDate());
                    incomePlanData.setInvAmt(contractMilestone.getExceptAcceptanceAmt());
                }

                if (StrUtil.isEmpty(incomePlanData.getIncomeConfirmType())) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_ID_NULL, "合同信息不存在收入计划填报字段，请确认!");
                }
                incomePlanData.setMilestonEstimateAmt(contractMilestone.getMilestoneProvisionalEstimateAmt());
                incomePlanData.setMilestonePrePaidInvAmt(contractMilestone.getMilestoneAdvanceAmt());

                //TODO不含税金额计算
                if (ObjectUtil.isNotEmpty(contractMilestone.getMilestoneAmt())) {
                    incomePlanData.setMilestoneAmt(contractMilestone.getMilestoneAmt());
                } else {
                    incomePlanData.setMilestoneAmt(contractMilestone.getExceptAcceptanceAmt());
                }
                //里程碑已开票未开票待表建立

                incomePlanData.setExpertiseCenter(contractMilestone.getUndertDept());
                incomePlanData.setExpertiseStation(contractMilestone.getOfficeDept());
                String id = classRedisHelper.getUUID(IncomePlanData.class.getSimpleName());
                incomePlanData.setId(id);
                BigDecimal bigDecimal = BigDecimal.ZERO;
                if (CollUtil.isNotEmpty(childMap.get(contractMilestone.getId()))) {
                    List<ContractMilestone> contractMilestoneList = childMap.get(contractMilestone.getId());
                    String taxRate = "";
                    String incomeWbsNumbers = "";
                    for (ContractMilestone con : contractMilestoneList) {
                        BillingAccountInformation billingAccountInformation = new BillingAccountInformation();
                        billingAccountInformation.setTaxRate(con.getTaxRate());

                        Project p = projectMap.get(con.getProjectCode());
                        if (ObjectUtil.isNotEmpty(p)) {
                            billingAccountInformation.setProjectId(p.getId());
                            billingAccountInformation.setProjectType(p.getProjectType());
                            billingAccountInformation.setProjectNumber(con.getProjectCode());
                        }
                        billingAccountInformation.setProjectRspUserId(con.getTechRspUser());

                        billingAccountInformation.setIncomeWbsNumber(con.getWbsCode());
                        billingAccountInformation.setTotalAmtTax(con.getExceptAcceptanceAmt());
                        billingAccountInformation.setIncomePlanId(incomePlanId);

                        if (ObjectUtil.isNotEmpty(contractMilestone.getActualMilestoneAmt())) {
                            billingAccountInformation.setActualAcceptanceAmt(contractMilestone.getActualMilestoneAmt());
                        } else {
                            billingAccountInformation.setActualAcceptanceAmt(BigDecimal.ZERO);
                        }
                        billingAccountInformation.setIncomePlanDataId(id);
//                    billingAccountInformation.setVatAmt(con.getExceptAcceptanceAmt()
//                            .multiply(con.getTaxRate()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)
//                    );
//                    billingAccountInformation.setAmtExTax(billingAccountInformation.getTotalAmtTax().subtract(billingAccountInformation.getVatAmt()));
                        if(ObjectUtil.isNotEmpty(billingAccountInformation.getTotalAmtTax())&&ObjectUtil.isNotEmpty(billingAccountInformation.getTaxRate())) {
                            billingAccountInformation.setAmtExTax(billingAccountInformation.getTotalAmtTax()
                                    .divide(new BigDecimal(1).add(billingAccountInformation.getTaxRate().divide(new BigDecimal(100))), 2, RoundingMode.HALF_UP));
                            billingAccountInformation.setVatAmt(billingAccountInformation.getTotalAmtTax().subtract(billingAccountInformation.getAmtExTax()));
                            bigDecimal = bigDecimal.add(billingAccountInformation.getAmtExTax());
                        }
                        billingAccountInformationList.add(billingAccountInformation);
                        if (StrUtil.isNotBlank(billingAccountInformation.getIncomeWbsNumber())) {
                            incomeWbsNumbers = billingAccountInformation.getIncomeWbsNumber() + "、";
                        }
                        if(StrUtil.isBlank(taxRate)){
                            taxRate = billingAccountInformation.getTaxRate()+"";
                        }else{
                            taxRate =taxRate +"、"+billingAccountInformation.getTaxRate();
                        }
                    }
                    incomePlanData.setTaxRate(taxRate);
                    incomePlanData.setIncomeWbsNumber(incomeWbsNumbers);
                    incomePlanData.setProjectRspUserId("多项目");
                    incomePlanData.setIsMultipleProjects("1");
                } else {
                    BillingAccountInformation billingAccountInformation = new BillingAccountInformation();
                    billingAccountInformation.setTaxRate(contractMilestone.getTaxRate());
                    Project p = projectMap.get(contractMilestone.getProjectCode());
                    if (ObjectUtil.isNotEmpty(p)) {
                        billingAccountInformation.setProjectId(p.getId());
                        billingAccountInformation.setProjectType(p.getProjectType());
                        billingAccountInformation.setProjectNumber(p.getNumber());
                        incomePlanData.setProjectId(p.getId());
                        incomePlanData.setProjectNumber(p.getNumber());
                    }
                    billingAccountInformation.setProjectRspUserId(contractMilestone.getTechRspUser());
                    billingAccountInformation.setIncomeWbsNumber(contractMilestone.getWbsCode());
                    billingAccountInformation.setTotalAmtTax(contractMilestone.getExceptAcceptanceAmt());
                    if (ObjectUtil.isNotEmpty(contractMilestone.getActualMilestoneAmt())) {
                        billingAccountInformation.setActualAcceptanceAmt(contractMilestone.getActualMilestoneAmt());
                    } else {
                        billingAccountInformation.setActualAcceptanceAmt(BigDecimal.ZERO);
                    }
                    billingAccountInformation.setIncomePlanId(incomePlanId);
                    billingAccountInformation.setIncomePlanDataId(id);

                    if(ObjectUtil.isNotEmpty(billingAccountInformation.getTotalAmtTax())&&ObjectUtil.isNotEmpty(billingAccountInformation.getTaxRate())) {
                        billingAccountInformation.setAmtExTax(billingAccountInformation.getTotalAmtTax()
                                .divide(new BigDecimal(1).add(billingAccountInformation.getTaxRate().divide(new BigDecimal(100))), 2, RoundingMode.HALF_UP));
                        billingAccountInformation.setVatAmt(billingAccountInformation.getTotalAmtTax().subtract(billingAccountInformation.getAmtExTax()));
                        bigDecimal = bigDecimal.add(billingAccountInformation.getAmtExTax());
                    }
                    billingAccountInformationList.add(billingAccountInformation);
                    incomePlanData.setProjectRspUserId(contractMilestone.getTechRspUser());
                    incomePlanData.setIsMultipleProjects("0");



                }
                incomePlanData.setInvAmtExTax(BigDecimal.ZERO);
                incomePlanData.setWriteOffAmtExTax(BigDecimal.ZERO);
                incomePlanData.setAdvPayIncomeAmtExTax(BigDecimal.ZERO);
                incomePlanData.setEstimateAmtExTax(BigDecimal.ZERO);
                if (StrUtil.equals(incomePlanData.getIncomeConfirmType(), IncomeConfirmTypeEnum.PROVISIONAL_INCOME.getValue())) {
                    incomePlanData.setEstimateAmtExTax(bigDecimal);
                }
                if (!StrUtil.equals(incomePlanData.getIncomeConfirmType(), IncomeConfirmTypeEnum.PROVISIONAL_INCOME.getValue())
                        && !StrUtil.equals(incomePlanData.getIncomeConfirmType(), IncomeConfirmTypeEnum.INVALIDATION_INVOICE.getValue())) {
                    incomePlanData.setInvAmtExTax(bigDecimal);
                }
                if (!StrUtil.equals(incomePlanData.getIncomeConfirmType(), IncomeConfirmTypeEnum.ADVANCE_PAYMENT_INVOICE.getValue())) {
                    BigDecimal tatal = sumBigDecimals(incomePlanData.getEstimateAmtExTax()
                            , incomePlanData.getInvAmtExTax(), incomePlanData.getCancelInvAmtExTax()
                            , incomePlanData.getWriteOffAmtExTax(), incomePlanData.getAdvPayIncomeAmtExTax());
                    incomePlanData.setIncomePlanAmt(tatal);
                }
                incomePlanDataList.add(incomePlanData);
            }
            incomePlanDataService.saveBatch(incomePlanDataList);
            incomePlanDataService.saveLock(incomePlanDataList);
            billingAccountInformationService.saveBatch(billingAccountInformationList);
        }
        return incomePlanDataList;
    }


    public  List<IncomePlanData> saveLastMonthData(Date date,String incomePlanId){
        SimpleDateFormat originalFormat = new SimpleDateFormat("yyyy-MM");
        String workTopics = originalFormat.format(DateUtil.offsetMonth(date, -1));
        LambdaQueryWrapperX<IncomePlanData> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(IncomePlanData.class);
        lambdaQueryWrapperX.leftJoin(IncomePlan.class,IncomePlan::getId,IncomePlanData::getIncomePlanId).eq(IncomePlan::getIncomePlanType,IncomePlanData::getDataVersion)
                .eq(IncomePlan::getWorkTopics,workTopics).in(IncomePlanData::getStatus,IncomePlanDataStatusEnum.RECONCILIATION_PROCESS.getCode(),IncomePlanDataStatusEnum.BILLING_PROCESS.getCode());
        getSelect(lambdaQueryWrapperX);
        List<IncomePlanData> incomePlanDataList =  incomePlanDataService.list(lambdaQueryWrapperX);
        if(CollUtil.isNotEmpty(incomePlanDataList)) {
            List<String> ids = incomePlanDataList.stream().map(IncomePlanData::getId).collect(Collectors.toList());
            List<BillingAccountInformation> billingAccountInformationList = billingAccountInformationService
                    .list(new LambdaQueryWrapperX<>(BillingAccountInformation.class).in(BillingAccountInformation::getIncomePlanDataId,ids));
            Map<String,List<BillingAccountInformation>> map = billingAccountInformationList.stream().collect(Collectors.groupingBy(BillingAccountInformation::getIncomePlanDataId));
            List<BillingAccountInformation> billingAccountInformationArrayList = new ArrayList<>();
            for (IncomePlanData incomePlanData : incomePlanDataList) {
                incomePlanData.setDataVersion(IncomePlanTypeEnum.COMPILE.getStatus());
                List<BillingAccountInformation> billingAccounts = map.get(incomePlanData.getId());
                String id = classRedisHelper.getUUID(IncomePlanData.class.getSimpleName());
                if(CollUtil.isNotEmpty(billingAccounts)){
                    for (BillingAccountInformation billingAccount : billingAccounts) {
                        billingAccount.setId(null);
                        billingAccount.setCreatorId(null);
                        billingAccount.setCreateTime(null);
                        billingAccount.setModifyId(null);
                        billingAccount.setModifyTime(null);
                        billingAccount.setOwnerId(null);
                        billingAccount.setIncomePlanId(incomePlanId);
                        billingAccount.setIncomePlanDataId(id);
                    }
                    billingAccountInformationArrayList.addAll(billingAccounts);
                }
                incomePlanData.setId(id);
                incomePlanData.setCreatorId(null);
                incomePlanData.setCreateTime(null);
                incomePlanData.setModifyId(null);
                incomePlanData.setModifyTime(null);
                incomePlanData.setOwnerId(null);
                incomePlanData.setIsAdjustment("0");
                incomePlanData.setRepeatCount(incomePlanData.getRepeatCount()+1);
                incomePlanData.setIncomePlanId(incomePlanId);
            }
            incomePlanDataService.saveBatch(incomePlanDataList);
            incomePlanDataService.saveLock(incomePlanDataList);
            billingAccountInformationService.saveBatch(billingAccountInformationArrayList);
        }
        return  incomePlanDataList;
    }

    public static BigDecimal sumBigDecimals(BigDecimal... values) {
        BigDecimal sum = BigDecimal.ZERO;
        for (BigDecimal value : values) {
            if (value != null) {
                sum = sum.add(value);
            }
        }
        return sum;
    }


    @Override
    public Boolean adjustment(List<String> ids) throws Exception {
        long startTime = System.currentTimeMillis();
        List<IncomePlan> incomePlans = this.listByIds(ids);
        Date twoMonthsAgo = DateUtil.offsetMonth(new Date(), -2);
        SimpleDateFormat originalFormat = new SimpleDateFormat("yyyy-MM");
        String dateFormat = originalFormat.format(twoMonthsAgo);
        List<String> planIds = incomePlans.stream().map(in -> in.getId()).collect(Collectors.toList());
        String lockKey = planIds.stream().collect(Collectors.joining(":"));
        LockInfo lockInfo = lockTemplate.lock(lockKey, 60000L, 2000L, RedisTemplateLockExecutor.class);
        if (lockInfo == null) {
            throw new RuntimeException("业务处理中请稍后");
        }
        try {
            for (IncomePlan incomePlan : incomePlans) {
                if (!IncomePlanTypeEnum.COMPILE.getStatus().equals(incomePlan.getIncomePlanType())) {
                    throw new PMSException(PMSErrorCode.PMS_ERR, incomePlan.getWorkTopicsName() + "已处于调整中");
                }
                if (incomePlan.getWorkTopics().compareTo(dateFormat) <= 0) {
                    throw new PMSException(PMSErrorCode.PMS_ERR, incomePlan.getWorkTopicsName() + "超时不能开启调整");
                }
                incomePlan.setIncomePlanType(IncomePlanTypeEnum.ADJUST.getStatus());
            }
            long endTime = System.currentTimeMillis();
            System.out.println("执行时间： " + (endTime - startTime) + " 毫秒");
            List<BillingAccountInformation> billingAccountInformationArrayList = new ArrayList<>();
            long startTime1 = System.currentTimeMillis();
            LambdaQueryWrapperX<IncomePlanData> lambdaQueryWrapper = new LambdaQueryWrapperX<>(IncomePlanData.class);
            lambdaQueryWrapper.in(IncomePlanData::getIncomePlanId, ids);
            getSelect(lambdaQueryWrapper);
            List<IncomePlanData> incomePlanDataList = incomePlanDataService.list(lambdaQueryWrapper);
            if (CollUtil.isNotEmpty(incomePlanDataList)) {
                List<String> incomePlanDataIds = incomePlanDataList.stream().map(IncomePlanData::getId).collect(Collectors.toList());
                List<BillingAccountInformation> billingAccountInformationList = billingAccountInformationService
                        .list(new LambdaQueryWrapper<BillingAccountInformation>().in(BillingAccountInformation::getIncomePlanDataId, incomePlanDataIds));
                Map<String, List<BillingAccountInformation>> map = billingAccountInformationList.stream().collect(Collectors.groupingBy(BillingAccountInformation::getIncomePlanDataId));
                for (IncomePlanData incomePlanData : incomePlanDataList) {
                    incomePlanData.setCompileId(incomePlanData.getId());
                    List<BillingAccountInformation> billingAccounts = map.get(incomePlanData.getId());
                    String id = classRedisHelper.getUUID(IncomePlanData.class.getSimpleName());
                    if (CollUtil.isNotEmpty(billingAccounts)) {
                        for (BillingAccountInformation billingAccount : billingAccounts) {
                            billingAccount.setId(null);
                            billingAccount.setCreatorId(null);
                            billingAccount.setCreateTime(null);
                            billingAccount.setModifyId(null);
                            billingAccount.setModifyTime(null);
                            billingAccount.setOwnerId(null);
                            billingAccount.setIncomePlanId(incomePlanData.getIncomePlanId());
                            billingAccount.setIncomePlanDataId(id);
                        }
                        billingAccountInformationArrayList.addAll(billingAccounts);
                    }
                    incomePlanData.setId(id);
                    incomePlanData.setCreatorId(null);
                    incomePlanData.setCreateTime(null);
                    incomePlanData.setModifyId(null);
                    incomePlanData.setModifyTime(null);
                    incomePlanData.setOwnerId(null);
                    incomePlanData.setIsAdjustment("0");
                    incomePlanData.setDataVersion(IncomePlanTypeEnum.ADJUST.getStatus());
                }
                long endTime1 = System.currentTimeMillis();
                System.out.println("执行时间1： " + (endTime1 - startTime1) + " 毫秒");
                long startTime2 = System.currentTimeMillis();
                this.updateBatchById(incomePlans);
                incomePlanDataService.saveBatch(incomePlanDataList);
                billingAccountInformationService.saveBatch(billingAccountInformationArrayList);
                long endTime2 = System.currentTimeMillis();
                System.out.println("执行时间1： " + (endTime2 - startTime2) + " 毫秒");
            }
        } catch (Exception e) {
            log.error("调整数据失败:{}", e);
            throw e;
        } finally {
            lockTemplate.releaseLock(lockInfo);
        }
        return true;
    }

    public void getSelect(LambdaQueryWrapperX<IncomePlanData> condition){
        condition.select(
                IncomePlanData::getId,
                IncomePlanData::getStatus,
                IncomePlanData::getContractId,
                IncomePlanData::getContractNumber,
                IncomePlanData::getContractName,
                IncomePlanData::getMilestoneId,
                IncomePlanData::getMilestoneNumber,
                IncomePlanData::getMilestoneName,
                IncomePlanData::getPartyADeptId,
                IncomePlanData::getPartyADeptIdName,
                IncomePlanData::getNumber,
                IncomePlanData::getPowerContractCode,
                IncomePlanData::getLockStatus,
                IncomePlanData::getIncomeConfirmType,
                IncomePlanData::getEstimateInvoiceDate,
                IncomePlanData::getTaxRate,
                IncomePlanData::getEstimateAmt,
                IncomePlanData::getInvAmt,
                IncomePlanData::getCancelInvAmt,
                IncomePlanData::getMilestonEstimateAmt,
                IncomePlanData::getWriteOffAmt,
                IncomePlanData::getMilestonePrePaidInvAmt,
                IncomePlanData::getAdvancePayIncomeAmt,
                IncomePlanData::getIncomePlanAmt,
                IncomePlanData::getExpertiseCenter,
                IncomePlanData::getExpertiseStation,
                IncomePlanData::getProjectNumber,
                IncomePlanData::getProjectId,
                IncomePlanData::getProjectRspUserId,
                IncomePlanData::getBillingCompany,
                IncomePlanData::getInternalExternal,
                IncomePlanData::getMilestoneAmt,
                IncomePlanData::getMilestoneInvAmt,
                IncomePlanData::getMilestoneNoInvAmt,
                IncomePlanData::getEstimateAmtExTax,
                IncomePlanData::getInvAmtExTax,
                IncomePlanData::getCancelInvAmtExTax,
                IncomePlanData::getMilestoneAmtExTax,
                IncomePlanData::getWriteOffAmtExTax,
                IncomePlanData::getMilestoneInvAmtExTax,
                IncomePlanData::getAdvPayIncomeAmtExTax,
                IncomePlanData::getIsRevenue,
                IncomePlanData::getNoRevenuePlanReason,
                IncomePlanData::getOtherNotes,
                IncomePlanData::getRiskType,
                IncomePlanData::getChangeReason,
                IncomePlanData::getRiskLink,
                IncomePlanData::getDataVersion,
                IncomePlanData::getDataSource,
                IncomePlanData::getIncomePlanId,
                IncomePlanData::getIsAdjustment,
                IncomePlanData::getCompileId,
                IncomePlanData::getIsMultipleProjects,
                IncomePlanData::getRemark,
                IncomePlanData::getAcceptanceDate,
                IncomePlanData::getRepeatCount,
                IncomePlanData::getIndustry,
                IncomePlanData::getPartyAContractNumber

        );
    }

    @Override
    public Boolean authorized(List<String> ids) throws Exception {
        List<IncomePlan>  incomePlans = this.listByIds(ids);
        Date twoMonthsAgo = DateUtil.offsetMonth(new Date(), -2);
        SimpleDateFormat originalFormat = new SimpleDateFormat("yyyy-MM");
        String dateFormat = originalFormat.format(twoMonthsAgo);
        for(IncomePlan incomePlan : incomePlans){
            if(!IncomePlanTypeEnum.ADJUST.getStatus().equals(incomePlan.getIncomePlanType())){
                throw new PMSException(PMSErrorCode.PMS_ERR, incomePlan.getWorkTopicsName()+"已处于编制中");
            }
            if(incomePlan.getWorkTopics().compareTo(dateFormat)<=0){
                throw new PMSException(PMSErrorCode.PMS_ERR, incomePlan.getWorkTopicsName()+"超时不能重新编制");
            }
            incomePlan.setIncomePlanType(IncomePlanTypeEnum.COMPILE.getStatus());
        }
        List<IncomePlanData> incomePlanDataList = incomePlanDataService.list(new LambdaQueryWrapperX<>(IncomePlanData.class).select(IncomePlanData::getId).eq(IncomePlanData::getDataVersion,IncomePlanTypeEnum.ADJUST.getStatus()).in(IncomePlanData::getIncomePlanId,ids));
        if(CollUtil.isNotEmpty(incomePlanDataList)) {
            List<String> dataIds = incomePlanDataList.stream().map(IncomePlanData::getId).collect(Collectors.toList());
            this.updateBatchById(incomePlans);
            incomePlanDataService.removeBatchByIds(dataIds);
        }
        return true;
    }


    public static class IncomePlanExcelListener extends AnalysisEventListener<IncomePlanDTO> {

        private final List<IncomePlanDTO> data = new ArrayList<>();

        @Override
        public void invoke(IncomePlanDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<IncomePlanDTO> getData() {
            return data;
        }
    }




}
