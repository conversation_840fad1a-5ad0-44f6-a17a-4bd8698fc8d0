package com.chinasie.orion.management.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;

/**
 * SectorQualInfo Entity对象
 *
 * <AUTHOR>
 * @since 2024-11-26 10:59:54
 */
@TableName(value = "pmsx_sector_qual_info")
@ApiModel(value = "SectorQualInfoEntity对象", description = "采购合同板块资审信息")
@Data

public class SectorQualInfo extends  ObjectEntity  implements Serializable{

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @TableField(value = "supplier_code")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @TableField(value = "supplier_name")
    private String supplierName;

    /**
     * 板块名称
     */
    @ApiModelProperty(value = "板块名称")
    @TableField(value = "sector_name")
    private String sectorName;

    /**
     * 供应商级别
     */
    @ApiModelProperty(value = "供应商级别")
    @TableField(value = "supplier_level")
    private String supplierLevel;

    /**
     * 资审有效期
     */
    @ApiModelProperty(value = "资审有效期")
    @TableField(value = "qual_valid_date")
    private Date qualValidDate;

    /**
     * 采购品类
     */
    @ApiModelProperty(value = "采购品类")
    @TableField(value = "procurement_cat")
    private String procurementCat;

    /**
     * 采购品类编码
     */
    @ApiModelProperty(value = "采购品类编码")
    @TableField(value = "proc_cat_code")
    private String procCatCode;

}
