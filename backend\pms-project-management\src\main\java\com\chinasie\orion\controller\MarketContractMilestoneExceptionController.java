package  com.chinasie.orion.controller;

import com.chinasie.orion.domain.vo.MarketContractMilestoneRescheduleVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;




import com.chinasie.orion.domain.entity.MarketContractMilestoneException;
import com.chinasie.orion.domain.dto.MarketContractMilestoneExceptionDTO;
import com.chinasie.orion.domain.vo.MarketContractMilestoneExceptionVO;

import com.chinasie.orion.service.MarketContractMilestoneExceptionService;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * MarketContractMilestoneException 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-30 02:55:28
 */
@RestController
@RequestMapping("/marketContractMilestoneException")
@Api(tags = "市场合同里程碑异常信息")
public class  MarketContractMilestoneExceptionController  {

    @Autowired
    private MarketContractMilestoneExceptionService marketContractMilestoneExceptionService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据【{{#id}}】", type = "市场合同里程碑异常信息", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<MarketContractMilestoneExceptionVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        MarketContractMilestoneExceptionVO rsp = marketContractMilestoneExceptionService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param marketContractMilestoneExceptionDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#marketContractMilestoneExceptionDTO.name}}】", type = "市场合同里程碑异常信息", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody @Validated MarketContractMilestoneExceptionDTO marketContractMilestoneExceptionDTO) throws Exception {
        String rsp =  marketContractMilestoneExceptionService.create(marketContractMilestoneExceptionDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 查询里程碑异常列表
     *
     * @param milestoneId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "查询里程碑异常列表")
    @RequestMapping(value = "/{milestoneId}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了里程碑异常列表", type = "市场合同里程碑异常信息", subType = "查询里程碑异常列表", bizNo = "{{#milestoneId}}")
    public ResponseDTO<List<MarketContractMilestoneExceptionVO>> listByMilestoneId(@PathVariable(value = "milestoneId") String milestoneId) throws Exception {
        List<MarketContractMilestoneExceptionVO> rsp = marketContractMilestoneExceptionService.listByMilestoneId(milestoneId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 编辑
     *
     * @param marketContractMilestoneExceptionDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#marketContractMilestoneExceptionDTO.name}}】", type = "市场合同里程碑异常信息", subType = "编辑", bizNo = "{{#marketContractMilestoneExceptionDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  MarketContractMilestoneExceptionDTO marketContractMilestoneExceptionDTO) throws Exception {
        Boolean rsp = marketContractMilestoneExceptionService.edit(marketContractMilestoneExceptionDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "市场合同里程碑异常信息", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = marketContractMilestoneExceptionService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "市场合同里程碑异常信息", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = marketContractMilestoneExceptionService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "市场合同里程碑异常信息", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<MarketContractMilestoneExceptionVO>> pages(@RequestBody Page<MarketContractMilestoneExceptionDTO> pageRequest) throws Exception {
        Page<MarketContractMilestoneExceptionVO> rsp =  marketContractMilestoneExceptionService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("市场合同里程碑异常信息导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "市场合同里程碑异常信息", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        marketContractMilestoneExceptionService.downloadExcelTpl(response);
    }

    @ApiOperation("市场合同里程碑异常信息导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "市场合同里程碑异常信息", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = marketContractMilestoneExceptionService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("市场合同里程碑异常信息导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "市场合同里程碑异常信息", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  marketContractMilestoneExceptionService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消市场合同里程碑异常信息导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "市场合同里程碑异常信息", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  marketContractMilestoneExceptionService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("市场合同里程碑异常信息导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "市场合同里程碑异常信息", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        marketContractMilestoneExceptionService.exportByExcel(searchConditions, response);
    }
}
