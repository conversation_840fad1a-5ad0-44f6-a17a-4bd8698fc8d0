package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ProjectFullSizeReportDTO;
import com.chinasie.orion.domain.vo.ProjectFullSizeReportVO;
import com.chinasie.orion.service.ProjectFullSizeReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;

import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;

import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * ProjectFullSizeReport 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18 09:38:50
 */
@RestController
@RequestMapping("/projectFullSizeReport")
@Api(tags = "项目全口径报表")
public class ProjectFullSizeReportController {

    @Autowired
    private ProjectFullSizeReportService projectFullSizeReportService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "项目全口径报表", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectFullSizeReportVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        ProjectFullSizeReportVO rsp = projectFullSizeReportService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectFullSizeReportDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#projectFullSizeReportDTO.name}}】", type = "项目全口径报表", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody ProjectFullSizeReportDTO projectFullSizeReportDTO) throws Exception {
        String rsp = projectFullSizeReportService.create(projectFullSizeReportDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectFullSizeReportDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#projectFullSizeReportDTO.name}}】", type = "项目全口径报表", subType = "编辑", bizNo = "{{#projectFullSizeReportDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody ProjectFullSizeReportDTO projectFullSizeReportDTO) throws Exception {
        Boolean rsp = projectFullSizeReportService.edit(projectFullSizeReportDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "项目全口径报表", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = projectFullSizeReportService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "项目全口径报表", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectFullSizeReportService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "项目全口径报表", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ProjectFullSizeReportVO>> pages(@RequestBody Page<ProjectFullSizeReportDTO> pageRequest) throws Exception {
        Page<ProjectFullSizeReportVO> rsp = projectFullSizeReportService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("项目全口径报表导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "项目全口径报表", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        projectFullSizeReportService.downloadExcelTpl(response);
    }

    @ApiOperation("项目全口径报表导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "项目全口径报表", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = projectFullSizeReportService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("项目全口径报表导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "项目全口径报表", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = projectFullSizeReportService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消项目全口径报表导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "项目全口径报表", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = projectFullSizeReportService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("项目全口径报表导出（Excel）")
    @PostMapping(value = "/export/excel")
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "项目全口径报表", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody ProjectFullSizeReportDTO projectFullSizeReportDTO, HttpServletResponse response) throws Exception {
        projectFullSizeReportService.exportByExcel(projectFullSizeReportDTO, response);
    }

    @ApiOperation(value = "获取列表")
    @RequestMapping(value = "/getList", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】获取列表数据", type = "项目全口径报表", subType = "列表", bizNo = " ")
    public ResponseDTO< List<ProjectFullSizeReportVO>> getList(@RequestBody ProjectFullSizeReportDTO projectFullSizeReportDTO) throws Exception {
        List<ProjectFullSizeReportVO> rsp = projectFullSizeReportService.getList(projectFullSizeReportDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "同步数据")
    @RequestMapping(value = "/syncData", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】同步时间", type = "项目全口径报表", subType = "同步数据", bizNo = " ")
    public ResponseDTO<Boolean> syncData(@RequestParam Integer year) throws Exception {
        Boolean rsp = projectFullSizeReportService.syncData(year);
        return new ResponseDTO<>(rsp);
    }
}
