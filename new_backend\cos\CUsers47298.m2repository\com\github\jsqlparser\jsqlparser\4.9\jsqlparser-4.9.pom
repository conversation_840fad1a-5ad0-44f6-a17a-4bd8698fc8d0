<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.github.jsqlparser</groupId>
    <artifactId>jsqlparser</artifactId>
    <version>4.9</version>
    <name>JSQLParser library</name> 
    <inceptionYear>2004</inceptionYear>
    <organization>
        <name>JSQLParser</name> 
    </organization>
    <packaging>bundle</packaging>
    <url>https://github.com/JSQLParser/JSqlParser</url>

    <licenses>
        <license>
            <name>GNU Library or Lesser General Public License (LGPL) V2.1</name>
            <url>http://www.gnu.org/licenses/lgpl-2.1.html</url>
            <distribution>repo</distribution>
        </license>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <dependencies>
        <!-- needed for parsing the Keywords via JTree in ParserKeywordsUtils -->
        <dependency>
            <groupId>net.java.dev.javacc</groupId>
            <artifactId>javacc</artifactId>
            <version>7.0.13</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.11.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
        	<groupId>org.junit.jupiter</groupId>
        	<artifactId>junit-jupiter</artifactId>
        	<version>5.9.2</version>
        	<scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>4.8.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <version>4.8.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <version>3.23.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.12.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <version>2.1.214</version>
            <scope>test</scope>
        </dependency>

        <!-- This is needed for the Validation Framework tests       -->
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-all</artifactId>
            <version>1.3</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <developers>
        <developer>
            <name>Tobias Warneke</name>
            <email><EMAIL></email>
        </developer>
    </developers>

    <distributionManagement>
        <repository>
            <id>sonatype-nexus-staging</id>
            <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
        </repository>
        <snapshotRepository>
            <id>sonatype-nexus-snapshots</id>
            <url>https://oss.sonatype.org/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <scm>
        <connection>scm:git:https://github.com/JSQLParser/JSqlParser.git</connection>
        <developerConnection>scm:git:ssh://**************:JSQLParser/JSqlParser.git</developerConnection>
        <url>https://github.com/JSQLParser/JSqlParser.git</url>
        <tag>jsqlparser-4.9</tag>
    </scm>

    <issueManagement>
        <system>GitHub Issues</system>
        <url>https://github.com/JSQLParser/JSqlParser/issues</url>
    </issueManagement>

    <build>
        <plugins>
            <!-- Update they Keyword Whitelist in the Grammar's production RelObjectNameWithoutValue() -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <mainClass>net.sf.jsqlparser.parser.ParserKeywordsUtils</mainClass>
                    <arguments>
                        <argument>src/main/jjtree/net/sf/jsqlparser/parser/JSqlParserCC.jjt</argument>
                        <argument>src/site/sphinx/keywords.rst</argument>
                    </arguments>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-pmd-plugin</artifactId>
                <version>3.19.0</version>
                <configuration>
                    <rulesets>
                        <!-- Custom local file system rule set -->
                        <ruleset>${project.basedir}/config/pmd/ruleset.xml</ruleset>
                    </rulesets>
                    <excludes>
                        <exclude>**/*Bean.java</exclude>
                        <exclude>**/generated/*.java</exclude>
                    </excludes>
                    <excludeRoots>
                        <excludeRoot>target/generated-sources</excludeRoot>
                        <excludeRoot>target/generated-test-sources</excludeRoot>
                    </excludeRoots>
                    <printFailingErrors>true</printFailingErrors>
                </configuration>
                <executions>
                    <execution>
                        <id>pmd</id>
                        <goals>
                            <goal>check</goal>
                        </goals>
                        <phase>process-sources</phase>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>net.sourceforge.pmd</groupId>
                        <artifactId>pmd-core</artifactId>
                        <version>${pmdVersion}</version>
                    </dependency>
                    <dependency>
                        <groupId>net.sourceforge.pmd</groupId>
                        <artifactId>pmd-java</artifactId>
                        <version>${pmdVersion}</version>
                    </dependency>
                    <dependency>
                        <groupId>net.sourceforge.pmd</groupId>
                        <artifactId>pmd-javascript</artifactId>
                        <version>${pmdVersion}</version>
                    </dependency>
                    <dependency>
                        <groupId>net.sourceforge.pmd</groupId>
                        <artifactId>pmd-jsp</artifactId>
                        <version>${pmdVersion}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>3.2.0</version>
                <executions>
                    <execution>
                        <id>add-source</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>${project.build.directory}/generated-sources/javacc/</source>
                                <source>${project.build.directory}/generated-sources/jjtree/</source>
                            </sources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.10.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <showWarnings>true</showWarnings>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <showDeprecation>true</showDeprecation>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.javacc.plugin</groupId>
                <artifactId>javacc-maven-plugin</artifactId>
                <version>3.0.3</version>
                                
                <executions>
                    <execution>
                        <id>javacc</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>jjtree-javacc</goal>
                        </goals>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>net.java.dev.javacc</groupId>
                        <artifactId>javacc</artifactId>
                        <version>7.0.13</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-eclipse-plugin</artifactId>
                <version>2.9</version>
                <configuration>
                    <sourceIncludes>
                        <sourceInclude>/target/generated-sources/javacc</sourceInclude>
                    </sourceIncludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>license-maven-plugin</artifactId>
                <version>2.0.0</version>
                <configuration>
                    <verbose>false</verbose>
                    <addJavaLicenseAfterPackage>false</addJavaLicenseAfterPackage>
                    <canUpdateDescription>false</canUpdateDescription>
                    <licenseName>dual_lgpl_ap2</licenseName>
                    <licenseResolver>${project.baseUri}/src/license</licenseResolver>
                    <excludes>
                        <exclude>site/sphinx/**</exclude>
                    </excludes>
                </configuration>
                <executions>
                    <execution>
                        <id>first</id>
                        <goals>
                            <goal>update-file-header</goal>
                        </goals>
                        <phase>process-sources</phase>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <!-- dont switch to version 3.0.1, that makes the strange certificate problems --> 
                <version>3.0.0-M7</version>
                <configuration>
                    <localCheckout>true</localCheckout>
                    <pushChanges>false</pushChanges>
                    <mavenExecutorId>forked-path</mavenExecutorId>
                    <releaseProfiles>sign-release-artifacts</releaseProfiles>
                </configuration>
                <!-- 
                <dependencies>
                    <dependency>
                        <groupId>org.apache.maven.scm</groupId>
                        <artifactId>maven-scm-provider-gitexe</artifactId>
                        <version>1.9.5</version>
                    </dependency>
                </dependencies>
                -->
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>3.4.1</version>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <configuration>
                            <additionalOptions>${javadoc.opts}</additionalOptions>
                            <excludePackageNames>net.sf.jsqlparser.parser</excludePackageNames>
                            <doclint>none</doclint>
                        </configuration>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifestEntries>
                            <Automatic-Module-Name>net.sf.jsqlparser</Automatic-Module-Name>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-site-plugin</artifactId>
                <version>3.12.1</version>
                <executions>
                    <execution>
                        <id>attach-descriptor</id>
                        <goals>
                            <goal>attach-descriptor</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <locales>en</locales>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.eluder.coveralls</groupId>
                <artifactId>coveralls-maven-plugin</artifactId>
                <version>4.3.0</version>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>cobertura-maven-plugin</artifactId>
                <version>2.7</version>
                <configuration>
                    <format>xml</format>
                    <instrumentation>
                        <excludes>
                            <exclude>net/sf/jsqlparser/parser/*.class</exclude>
                            <exclude>net/sf/jsqlparser/JSQLParserException.class</exclude>
                        </excludes>
                    </instrumentation>
                    <check />
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>5.1.8</version>
                <extensions>true</extensions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.2.3</version>
                <configuration>
                    <trimStackTrace>false</trimStackTrace>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.10</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.diffplug.spotless</groupId>
                <artifactId>spotless-maven-plugin</artifactId>
                <version>2.28.0</version>
                <configuration>
                    <!-- optional: limit format enforcement to just the files changed by this feature branch -->
                    <ratchetFrom>origin/master</ratchetFrom>
                    <formats>
                        <!-- you can define as many formats as you want, each is independent -->
                        <format>
                            <!-- define the files to apply to -->
                            <includes>
                                <include>*.md</include>
                                <include>.gitignore</include>
                            </includes>
                            <!-- define the steps to apply to those files -->
                            <trimTrailingWhitespace />
                            <endWithNewline />
                            <indent>
                                <tabs>true</tabs>
                                <spacesPerTab>4</spacesPerTab>
                            </indent>
                        </format>
                    </formats>
                    <!-- define a language-specific format -->
                    <java>
                        <!-- These are the defaults, you can override if you want -->
                        <includes>
                            <include>src/main/java/**/*.java</include>
                            <include>src/test/java/**/*.java</include>
                        </includes>

                        <importOrder /> <!-- standard import order -->
                        <removeUnusedImports />

                        <!-- Apply Google style guide https://google.github.io/styleguide/javaguide.html -->
                        <eclipse>
                            <file>config/formatter/eclipse-java-google-style.xml</file>
                        </eclipse>

                        <formatAnnotations />  <!-- fixes formatting of type annotations, see below -->
                    </java>
                </configuration>
            </plugin>
        </plugins>
    </build>
    
    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-report-plugin</artifactId>
                <version>3.0.0-M7</version>
                <configuration>
                    <outputDirectory>${project.reporting.outputDirectory}/testresults</outputDirectory>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>3.4.1</version>
                <configuration>
                    <useStandardDocletOptions>true</useStandardDocletOptions>
                    <maxmemory>800m</maxmemory>
                    <doclint>none</doclint>
                    
                    <!-- Doclint does not work on the Test Sources
                    <doclint>all,-missing</doclint>
                    <excludePackageNames>net.sf.jsqlparser.parser</excludePackageNames>
                    <sourceFileExcludes>
                        <sourceFileExclude>src/test/**</sourceFileExclude>
                    </sourceFileExcludes>
                    -->
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-project-info-reports-plugin</artifactId>
                <version>3.4.1</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jxr-plugin</artifactId>
                <version>3.3.0</version>
            </plugin>
            
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>findbugs-maven-plugin</artifactId>
                <version>3.0.5</version>
            </plugin>
        </plugins>
    </reporting>

    <profiles>
        <profile>
            <id>sign-release-artifacts</id>
            <activation>
                <property>
                    <name>performRelease</name>
                    <value>true</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>1.6</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                                <configuration>
                                    <keyname>f22e0543</keyname>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    
        <profile>
            <id>check.sources</id>
            <activation>
                <property>
                    <name>!skipCheckSources</name>
                </property>
                <jdk>[1.8,)</jdk>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-checkstyle-plugin</artifactId>
                        <version>3.1.0</version>
                        <executions>
                            <execution>
                                <id>verify-style</id>
                                <phase>process-classes</phase>
                                <goals>
                                    <goal>check</goal>
                                </goals>
                            </execution>
                        </executions>
                        <configuration>
                            <logViolationsToConsole>true</logViolationsToConsole>
                            <includeTestSourceDirectory>true</includeTestSourceDirectory>
                            <sourceDirectories>${project.build.sourceDirectory}</sourceDirectories>
                            <checkstyleRules>
                                <module name="Checker">
                                    <module name="SuppressWarningsFilter" />
                                    <module name="FileTabCharacter" />
                                    <!-- git checkout may change linefeeds on the fly
                                    <module name="RegexpMultiline">
                                        <property name="format" value="(?s:(\r\n|\r).*)" />
                                        <property name="message" value="CRLF and CR line endings are prohibited, but this file uses them." />
                                    </module>
                                    -->
                                    <module name="TreeWalker">
                                        <module name="AvoidNestedBlocks" />
                                        <module name="ConstantName" />
                                        <module name="EmptyCatchBlock" />
                                        <module name="EmptyStatement" />
                                        <module name="MissingOverride" />
                                        <module name="MultipleVariableDeclarations" />
                                        <module name="ParameterAssignment" />
                                        <module name="StringLiteralEquality" />
                                        <module name="RedundantImport" />
                                        <module name="UnusedImports" />
                                        
                                        <module name="WhitespaceAfter" />
										
                                        <module name="NeedBraces" />
                                        <module name="UnnecessaryParentheses" />
                                        <module name="LeftCurly" />
                                        <module name="RightCurly" />
                                        
                                        <module name="SuppressWarningsHolder" />
                                    </module>
                                </module>
                            </checkstyleRules>
                        </configuration>
                        <dependencies>
                            <dependency>
                                <groupId>com.puppycrawl.tools</groupId>
                                <artifactId>checkstyle</artifactId>
                                <version>8.29</version>
                            </dependency>
                        </dependencies>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <!-- for building the parser with debug flags -->
            <id>skip.all</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <maven.test.skip>true</maven.test.skip>
                <checkstyle.skip>true</checkstyle.skip>
                <maven.javadoc.skip>true</maven.javadoc.skip>
                <license.skipUpdateLicense>true</license.skipUpdateLicense>
            </properties>
        </profile>
    </profiles>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <pmdVersion>6.53.0</pmdVersion>
    </properties>

    <description>JSqlParser parses an SQL statement and translate it into a hierarchy of Java classes.
        The generated hierarchy can be navigated using the Visitor Pattern.</description>
</project>
