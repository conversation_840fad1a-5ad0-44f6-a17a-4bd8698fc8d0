package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * ContractSignedStatisticDTO对象
 *
 */
@ApiModel(value = "ContractSignedStatisticDTO对象", description = "已签合同")
@Data
@ExcelIgnoreUnannotated
public class ContractSignedStatisticDTO {

    // 非核合同历史金额（已签订的旧合同）
    @ApiModelProperty(value = "非核历史金额")
    private BigDecimal nonNuclearHistoricalAmount;

    // 非核合同新签金额（新签订的合同）
    @ApiModelProperty(value = "非核新签金额")
    private BigDecimal nonNuclearNewSignAmount;

    // 非核合同总金额（历史和新签的总和）
    @ApiModelProperty(value = "新签总金额")
    private BigDecimal newSignTotalAmount;

    // 核合同历史金额（已签订的旧合同）
    @ApiModelProperty(value = "核历史金额")
    private BigDecimal nuclearHistoricalAmount;

    // 核合同新签金额（新签订的合同）
    @ApiModelProperty(value = "核新签金额")
    private BigDecimal nuclearNewSignAmount;

    // 核合同总金额（历史和新签的总和）
    @ApiModelProperty(value = "历史总金额")
    private BigDecimal historicalTotalAmount;

    // Getters and Setters
}
