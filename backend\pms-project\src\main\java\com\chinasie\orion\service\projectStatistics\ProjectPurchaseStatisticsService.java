package com.chinasie.orion.service.projectStatistics;

import com.chinasie.orion.domain.dto.projectStatistics.ProjectPurchaseStatisticsDTO;
import com.chinasie.orion.domain.vo.ProjectPurchaseOrderListInfoVO;
import com.chinasie.orion.domain.vo.projectStatistics.ProjectPurchaseStatisticsVO;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

public interface ProjectPurchaseStatisticsService {
    ProjectPurchaseStatisticsVO getProjectPurchaseStatusStatistics(ProjectPurchaseStatisticsDTO projectPurchaseStatisticsDTO);

    List<ProjectPurchaseStatisticsVO> getProjectPurchaseRspUserStatistics(ProjectPurchaseStatisticsDTO projectPurchaseStatisticsDTO);

    List<ProjectPurchaseStatisticsVO> getProjectPurchaseChangeStatusStatistics(ProjectPurchaseStatisticsDTO projectPurchaseStatisticsDTO);

    List<ProjectPurchaseStatisticsVO> getProjectPurchaseCreateStatistics(ProjectPurchaseStatisticsDTO projectPurchaseStatisticsDTO);

    Page<ProjectPurchaseOrderListInfoVO> getProjectPurchasePages(Page<ProjectPurchaseStatisticsDTO> pageRequest) throws Exception;
}
