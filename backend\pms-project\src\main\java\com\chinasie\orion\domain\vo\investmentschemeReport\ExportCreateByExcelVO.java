package com.chinasie.orion.domain.vo.investmentschemeReport;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;


@Data
@ColumnWidth(15)
public class ExportCreateByExcelVO {


    /**
     * 序号
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","序号"}, index = 0)
    private String order;

    /**
     * 计划编号
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","投资计划编号"}, index = 1)
    private String number;


    /**
     * 计划名称
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","投资计划名称"}, index = 2)
    private String name;


    /**
     * 申报状态
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","申报状态"}, index = 3)
    private String statusName;


    /**
     * 投资年份（Y）
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","投资年份（Y）"}, index = 4)
    private String yearName;


    /**
     * 是否申报投资计划
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","是否申报投资计划"}, index = 5)
    private String closeFlag;


    /**
     * 项目所属公司
     */
//    @ExcelProperty(value = {"投资计划申报报表 单位：万元","项目所属公司"}, index = 6)
//    private String companyName;

    /**
     * 项目编码
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","项目编码"}, index = 6)
    private String projectNumber;

    /**
     * 项目名称
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","项目名称"}, index = 7)
    private String projectName;

    /**
     * 项目状态
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","项目状态"}, index = 8)
    private String projectStatusName;


    /**
     * 项目处室
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","项目处室"}, index = 9)
    private String rspDeptName;

    /**
     * 项目负责人
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","项目负责人"}, index = 10)
    private String rspUserName;

    /**
     * 项目概算
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","项目概算"}, index = 11)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String estimate;


    /**
     * 总体预算
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","总体预算"}, index = 12)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String overallBudget;


    /**
     * 总体实际
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","总体实际"}, index = 13)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String overallReality;


    /**
     * 立项金额
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","立项金额"}, index = 14)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String projectAmount;


    /**
     * 合同金额
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","合同金额"}, index = 15)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String contractAmount;

    /**
     * 累计至（Y-2）年下达投资计划
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","累计至（Y-2）年下达投资计划"}, index = 16)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String cutOffGiveY_2;

    /**
     * 累计至（Y-2）年投资计划完成
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","累计至（Y-2）年投资计划完成"}, index = 17)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String cutOffCompleteY_2;


    /**
     * (Y-1)年投资计划
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","(Y-1)年投资计划"}, index = 18)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String lastYear;


    /**
     * (Y-1)年投资计划预计完成
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","(Y-1)年投资计划预计完成"}, index = 19)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String lastYearComplete = "";

    /**
     * (Y-1)年执行情况说明
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","(Y-1)年执行情况说明"}, index = 20)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String lastYearDoDesc;


    /**
     * 累计至(Y-1)年下达投资计划
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","累计至(Y-1)年下达投资计划"}, index = 21)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String cutOffGiveY_1;

    /**
     * 累计至(Y-1)年预计完成投资
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","累计至(Y-1)年预计完成投资"}, index = 22)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String cutOffCompleteY_1;


    /**
     * Y年投资计划
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","Y年投资计划"}, index = 23)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String total;


    /**
     * 建筑工程
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","建筑工程"}, index = 24)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String architecture = "";

    /**
     * 安装工程
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","安装工程"}, index = 25)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String installation = "";

    /**
     * 设备投资
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","设备投资"}, index = 26)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String device = "";

    /**
     * 其他费用
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","其他费用"}, index = 27)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String other = "";


    /**
     * 1月投资计划
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","1月投资计划"}, index = 28)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month1;


    /**
     * 1-2月投资计划
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","1-2月投资计划"}, index = 29)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month2;

    /**
     * 1-3月投资计划
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","1-3月投资计划"}, index = 30)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month3;

    /**
     * 1-4月投资计划
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","1-4月投资计划"}, index = 31)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month4;

    /**
     * 1-5月投资计划
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","1-5月投资计划"}, index = 32)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month5;

    /**
     * 1-6月投资计划
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","1-6月投资计划"}, index = 33)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month6;

    /**
     * 1-7月投资计划
     */
    @ExcelProperty(value ={"投资计划申报报表 单位：万元", "1-7月投资计划"}, index = 34)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month7;

    /**
     * 1-8月投资计划
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","1-8月投资计划"}, index = 35)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month8;

    /**
     * 1-9月投资计划
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","1-9月投资计划"}, index = 36)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month9;

    /**
     * 1-10月投资计划
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","1-10月投资计划"}, index = 37)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month10;

    /**
     * 1-11月投资计划
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","1-11月投资计划"}, index = 38)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month11;

    /**
     * 1-12月投资计划
     */
    @ExcelProperty(value ={"投资计划申报报表 单位：万元", "1-12月投资计划"}, index = 39)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month12;


    /**
     * Y年形象进度
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","Y年形象进度"}, index = 40)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String yearProcess;

    /**
     * Y+1年投资计划
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","Y+1年计划投资"}, index = 41)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String nextOneYear = "";

    /**
     * Y+2年投资计划
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","Y+2年计划投资"}, index = 42)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String nextTwoYear = "";


    /**
     * Y+3年投资计划
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","Y+3年计划投资"}, index = 43)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String nextThreeYear = "";


    /**
     * Y+4年投资计划
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","Y+4年计划投资"}, index = 44)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String nextFourYear = "";


    /**
     * Y+5年投资计划
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","Y+5年计划投资"}, index = 45)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String nextFiveYear = "";

    /**
     * 备注
     */
    @ExcelProperty(value = {"投资计划申报报表 单位：万元","备注"}, index = 46)
    private String remark = "";


}
