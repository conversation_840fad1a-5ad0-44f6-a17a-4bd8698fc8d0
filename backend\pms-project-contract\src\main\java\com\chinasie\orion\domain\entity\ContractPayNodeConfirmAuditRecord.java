package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * ContractPayNodeConfirmAuditRecord Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-27 15:12:55
 */
@TableName(value = "pms_contract_pay_node_confirm_audit_record")
@ApiModel(value = "ContractPayNodeConfirmAuditRecord对象", description = "合同支付节点确认审核记录")
@Data
public class ContractPayNodeConfirmAuditRecord extends ObjectEntity implements Serializable {

    /**
     * 审核时间
     */
    @ApiModelProperty(value = "审核时间")
    @TableField(value = "audit_date")
    private Date auditDate;

    /**
     * 审核意见
     */
    @ApiModelProperty(value = "审核意见")
    @TableField(value = "audit_comment")
    private String auditComment;

    /**
     * 审核用户Id
     */
    @ApiModelProperty(value = "审核用户Id")
    @TableField(value = "audit_user_id")
    private String auditUserId;

    /**
     * 支付节点确认id
     */
    @ApiModelProperty(value = "支付节点确认id")
    @TableField(value = "confirm_id")
    private String confirmId;

    /**
     * 提交id
     */
    @ApiModelProperty(value = "提交id")
    @TableField(value = "submit_id")
    private String submitId;
}
