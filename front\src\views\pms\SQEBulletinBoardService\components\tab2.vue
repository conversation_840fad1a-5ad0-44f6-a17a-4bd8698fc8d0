<!--
 * @Description:安全生产考核
 * @Autor: laotao117
 * @Date: 2024-08-22 19:21:33
 * @LastEditors: laotao117
 * @LastEditTime: 2024-08-28 11:45:38
-->
<template>
  <div>
    <BasicCard
      :isBorder="true"
      title="部门显示设置"
      class="card-border active-box"
    >
      <OrionTable
        ref="tableRef"
        class="radio-button-table"
        :options="tableOptions"
      />
    </BasicCard>
  </div>
</template>
<script setup lang="ts">
import {
  BasicCard,
  OrionTable,
} from 'lyra-component-vue3';
// Api
import {
  h,
  ref, Ref,
} from 'vue';
import Api from '/@/api';
// Checkbox
import { Checkbox } from 'ant-design-vue';
const tableRef: Ref = ref();
const selectedKeys: Ref<string[]> = ref([]);
const selectedRows: Ref<any[]> = ref([]);
// tableOptions 指标名称（事件等级）
const tableOptions = {
  // showSmallSearch: false,
  // rowSelection: {
  //   onChange(keys: string[], rows: any[]) {
  //     selectedKeys.value = keys || [];
  //     selectedRows.value = rows || [];
  //   },
  // },
  rowSelection: false,
  height: 500,
  smallSearchField: ['deptName', 'deptNumber'],
  //   分页隐藏
  // pagination: false,

  api: (params) => new Api('/pms').fetch({ ...params }, '/ampere_ring/board/config/dept/config/page', 'POST'),
  showToolButton: false,
  columns: [
    {
      title: '编号',
      dataIndex: 'deptCode',
    },
    {
      title: '部门名称',
      dataIndex: 'deptName',
    },
    {
      title: '部门编码',
      dataIndex: 'deptNumber',
    },
    {
      title: '在看板中展示',
      dataIndex: 'isDeptScoreShow',
      // Checkbox
      customRender: ({ record }) => h(Checkbox, {
        checked: record.isDeptScoreShow,
        onChange: (e) => {
          save(e, record);
        },
      }),
    },
  ],

};
// 是否展示
// POST
// /pms/ampere_ring/board/config/dept/config/isShow
function save(e, record) {
  const data = {
    id: record.id,
    deptCode: record.deptCode,
    isDeptScoreShow: e.target.checked,
  };
  new Api('/pms').fetch(data, '/ampere_ring/board/config/dept/config/isShow', 'POST').then(() => {
    tableRef.value.reload();
  });
}

</script>
<style  scoped lang="less">
.card-border {
    // border: 1px solid var(--ant-border-color-base);
    padding: 15px 0px;
    margin: 0 !important;
}
</style>