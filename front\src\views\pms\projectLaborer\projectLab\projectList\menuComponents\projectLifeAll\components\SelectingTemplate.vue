<script setup lang="ts">
import { h, ref } from 'vue';
import { OrionTable } from 'lyra-component-vue3';
import { page } from '/@/views/pms/api/projectLifeCycleTemplate';
import { message } from 'ant-design-vue';

const tableRef = ref(null);
const columns = [
  {
    title: '名称',
    dataIndex: 'name',
  },

  {
    title: '内容',
    dataIndex: 'content',
    customRender: ({ record }) => {
      const tempElement = document.createElement('div');
      tempElement.innerHTML = record.content;
      // 提取纯文本内容
      const textContent = tempElement.textContent || tempElement.innerText;
      return h('div', {
        title: textContent,
        class: 'flex-te',
      }, textContent);
    },
  },
  {
    title: '文件数',
    dataIndex: 'fileNum',
  },

];
const baseTableOption = {
  rowKey: 'id',
  // 是否显示工具栏默认按钮
  showToolButton: false,
  rowSelection: {
    type: 'radio',
  },
  columns,
  api: (params) => {
    params.query = { status: '1' };
    return page(params);
  },
  isFilter2: false,
};

const getSelectRowItemId = () => new Promise((resolve, reject) => {
  const data = tableRef.value.getSelectRows();
  if (data && data.length > 0) {
    resolve(data[0].id);
  } else {
    const msg = '请选择说明模板';
    message.error(msg);
    reject(msg);
  }
});

defineExpose({
  getSelectRowItemId,
});
</script>

<template>
  <div class="container">
    <OrionTable
      ref="tableRef"
      :options="baseTableOption"
    />
  </div>
</template>

<style scoped lang="less">
.container{
  height: 100%;
  overflow: hidden;
  position: relative;
}
</style>
