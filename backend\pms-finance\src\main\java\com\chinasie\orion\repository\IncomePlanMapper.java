package com.chinasie.orion.repository;


import com.chinasie.orion.domain.entity.IncomePlan;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <p>
 * IncomePlan Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29 19:14:00
 */
@Mapper
public interface IncomePlanMapper extends  OrionBaseMapper  <IncomePlan> {

   List<IncomePlan> getDataTotal(@Param("ids")List<String> ids);
}

