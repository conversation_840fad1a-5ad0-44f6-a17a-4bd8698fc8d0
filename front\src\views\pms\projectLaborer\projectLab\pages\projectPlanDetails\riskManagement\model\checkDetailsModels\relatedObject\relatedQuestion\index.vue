<template>
  <div>
    <OrionTable
      ref="tableRef"
      :options="options"
    />
  </div>
</template>

<script lang="ts">
import {
  defineComponent, h, reactive, toRefs,
} from 'vue';
import { OrionTable, DataStatusTag } from 'lyra-component-vue3';
import dayjs from 'dayjs';
import Api from '/@/api';
export default defineComponent({
  name: 'Index',
  components: { OrionTable },
  props: {
    id: {},
  },
  emits: [],
  setup(props) {
    const state = reactive({
      tableRef: null,
      options: {
        deleteToolButton: 'add|delete|enable|disable',
        rowSelection: {},
        showSmallSearch: false,
        showIndexColumn: false,
        smallSearchField: ['name'],
        // auto: {
        //   url: '/pas/question-type-attribute',
        //   params: {
        //     query: {
        //     },
        //   },
        // },
        // api:()=>new Api(`/pas/risk-management/relation/plan/${props.id}`).fetch('','','POST'),
        api: (P) => new Api(`/pas/risk-management/relation/question/${props.id}`).fetch(P, '', 'POST'),
        columns: [
          {
            title: '编号',
            dataIndex: 'number',
            align: 'left',
            key: 'number',

            width: '120px',
            // sorter: true,
            ellipsis: true,
          },
          {
            title: '名称',
            dataIndex: 'name',
            key: 'name',
            // customRender: function ({ record, text }) {
            //   return h(
            //       'span',
            //       {
            //         class: 'action-btn',
            //         // class: computed(()=>isPower('FX_container_button_08', state.powerData)) ?'action-btn':'',
            //         title: text,
            //         onClick: function (e) {
            //           if(true){
            //             checkData2(record)
            //           }
            //           e.stopPropagation();
            //         }
            //       },
            //       text
            //   );
            // },

            width: '240px',
            align: 'left',
            // slots: { customRender: 'name' },
            // sorter: true,
            ellipsis: true,
          },

          {
            title: '问题来源',
            dataIndex: 'questionSourceName',
            key: 'questionSourceName',
            width: '70px',
            align: 'left',
            // sorter: true,
            ellipsis: true,
          },
          {
            title: '问题类型',
            dataIndex: 'questionTypeName',
            key: 'questionTypeName',
            width: '70px',
            align: 'left',
            // sorter: true,
            ellipsis: true,
          },
          {
            title: '严重程度',
            dataIndex: 'seriousLevelName',
            key: 'seriousLevelName',
            width: '70px',
            align: 'left',
            // sorter: true,
            ellipsis: true,
          },
          {
            title: '问题内容',
            dataIndex: 'content',
            key: 'content',
            width: '70px',
            align: 'left',
            // sorter: true,
            ellipsis: true,
          },
          {
            title: '状态',
            dataIndex: 'dataStatus',
            key: 'dataStatus',

            width: '80px',
            align: 'left',
            // sorter: true,
            ellipsis: true,
            customRender: ({ record, text }) => h(DataStatusTag, { statusData: record.dataStatus }),
          },
          {
            title: '提出人',
            dataIndex: 'exhibitorName',
            key: 'exhibitorName',
            width: '70px',
            align: 'left',
            // sorter: true,
            ellipsis: true,
          },
          {
            title: '提出时间',
            dataIndex: 'proposedTime',
            key: 'proposedTime',

            width: '100px',
            align: 'left',
            // sorter: true,
            ellipsis: true,
            slots: { customRender: 'proposedTime' },
          },
        ],
      },
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang="less"></style>
