<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <artifactId>hadoop-thirdparty</artifactId>
    <groupId>org.apache.hadoop.thirdparty</groupId>
    <version>1.1.1</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>hadoop-shaded-guava</artifactId>
  <name>Apache Hadoop shaded Guava</name>
  <build>
    <resources>
      <resource>
        <targetPath>META-INF</targetPath>
        <directory>${project.basedir}/..</directory>
        <includes>
          <include>licenses-binary/*</include>
          <include>NOTICE.txt</include>
          <include>NOTICE-binary</include>
        </includes>
      </resource>
      <resource>
        <directory>${project.basedir}/src/main/resources</directory>
      </resource>
    </resources>
    <plugins>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <executions>
          <execution>
            <id>shade-guava</id>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <artifactSet>
                <excludes>
                  <exclude>com.google.code.findbugs:jsr305</exclude>
                </excludes>
              </artifactSet>
              <filters>
                <filter>
                  <artifact>org.checkerframework:checker-qual</artifact>
                  <excludes>
                    <exclude>META-INF/LICENSE.txt</exclude>
                  </excludes>
                </filter>
              </filters>
              <relocations>
                <relocation>
                  <pattern>com/google/</pattern>
                  <shadedPattern>${shaded.prefix}/com/google/</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>org/checkerframework/</pattern>
                  <shadedPattern>${shaded.prefix}/org/checkerframework/</shadedPattern>
                </relocation>
              </relocations>
              <transformers>
                <transformer>
                  <resources>
                    <resource>NOTICE</resource>
                    <resource>LICENSE</resource>
                  </resources>
                </transformer>
                <transformer>
                  <resource>META-INF/LICENSE.txt</resource>
                  <file>${basedir}/../LICENSE-binary</file>
                </transformer>
              </transformers>
            </configuration>
          </execution>
        </executions>
        <configuration>
          <createDependencyReducedPom>true</createDependencyReducedPom>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
