package com.chinasie.orion.domain.vo.resource;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/09/05/14:19
 * @description:
 */
@Data
public class MaterialQuarterCountVO implements Serializable {
    @ApiModelProperty("季度名称")
    private String quarterName;
    @ApiModelProperty("参修物资统计")
    private Integer materialCount=0;
    @ApiModelProperty("季度数")
    private Integer quarterNum=0;
    @ApiModelProperty("重叠天数")
    private Integer overlapDayCount=0;
    @ApiModelProperty("重叠物资数")
    private Integer overlapMaterialCount=0;
}
