<template>
  <Layout class="manHourPage">
    <div class="header">
      <div class="text-base">
        工时信息
      </div>
      <div class="text-xs">
        <a-space :size="40">
          <span>预估工时： {{ table.manHour }}</span>
          <span>已登记工时： {{ table.realityManHour }}</span>
          <span>剩余工时： {{ table.residueManHour }}</span>
          <span>工时进度： {{ table.manHourScheduleName }}</span>
          <span>预估偏差： {{ table.deviationManHour }}</span>
        </a-space>
      </div>
    </div>
    <BasicTable
      ref="tableRef"
      :row-selection="pageType==='page'?{ type: 'checkbox' }:false"
      :columns="columns"
      :data-source="table.manHourVos"
      :show-index-column="false"
      :pagination="false"
      row-key="id"
      :resize-height-offset="10"
      :loading="loading"
    />
  </Layout>
  <NewButtonModal
    v-if="pageType==='page'"
    :btn-object-data="btnConfig"
    @clickType="clickType"
  />
  <Edit
    v-if="edit.visible"
    :data="edit"
    @submit="submitEdit"
  />
</template>

<script>
import {
  Layout, BasicTable, isPower,
} from 'lyra-component-vue3';
import { message, Modal, Space } from 'ant-design-vue';
import NewButtonModal from '/@/views/pms/projectLaborer/componentsList/buttonModal/newButtonModal.vue';
import {
  onMounted, reactive, toRefs, ref, inject, computed,
} from 'vue';
import Api from '/@/api';
import Edit from './components/Edit.vue';
export default {
  name: 'Index',
  components: {
    NewButtonModal,
    Layout,
    Edit,
    BasicTable,
    ASpace: Space,
  },
  props: {
    pageType: {
      type: String,
      default: 'page',
    },
    formId: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const state = reactive({
      tableRef: ref(),
      searchAll: {},
      edit: {},
      viewDetails: {},
      columns: [
        {
          title: '姓名',
          dataIndex: 'name',
          align: 'left',
        },
        {
          title: '开始时间',
          dataIndex: 'startTime',
        },
        {
          title: '登记工时',
          dataIndex: 'realityManHour',
        },
        {
          title: '工作描述',
          dataIndex: 'remark',
        },
        {
          title: '修改人',
          dataIndex: 'modifyName',
        },
        {
          title: '修改时间',
          dataIndex: 'modifyTime',
        },
      ],
      table: {
        manHourVos: [],
      },
      loading: false,

      powerData: [],
    });
    state.powerData = inject('powerData');
    const state6 = reactive({
      btnConfig: {
        add: { show: computed(() => isPower('RWX_container_button_23', state.powerData)) },
        edit: { show: computed(() => isPower('RWX_container_button_24', state.powerData)) },
        remove: { show: computed(() => isPower('RWX_container_button_25', state.powerData)) },
      },
    });
    function getPage() {
      state.loading = true;
      const love = {
        id: props.formId,
        className: 'Plan',
        moduleName: '项目管理-计划管理-项目计划-工时', // 模块名称
        type: 'GET', // 操作类型
        remark: '获取/搜索了【工时列表】',
      };
      new Api('/pms', love)
        .fetch('', `man-hour/list?planId=${props.formId}`, 'GET')
        .then((res) => {
          state.table = res;
          state.loading = false;
          state.tableRef.clearSelectedRowKeys();
        })
        .catch(() => {
          state.loading = false;
        });
    }

    function isSelectCheck(type) {
      const selectedRowKeys = state.tableRef.getSelectRowKeys();
      if (selectedRowKeys.length === 0) {
        message.warning('请选择数据进行操作');
        return false;
      }
      if (selectedRowKeys.length === 1) {
        return selectedRowKeys[0];
      }
      if (selectedRowKeys.length > 1) {
        if (type === 'remove') {
          return true;
        }
        message.warning('请选择一条数据进行操作');
        return false;
      }
    }

    function clickType(type) {
      if (type === 'remove') {
        const id = isSelectCheck(type);
        if (id) {
          Modal.confirm({
            title: '确认提示',
            content: '请确认是否移除此选中工时？',
            onOk() {
              isConfirm();
            },
          });
        }
      }
      if (type === 'add') {
        state.edit = {
          visible: true,
          title: '添加工时',
          type,
          form: {
            planId: props.formId,
            memberId: undefined,
            name: undefined,
            startTime: undefined,
            manHour: state.table.manHour,
            residueManHour: state.table.residueManHour,
            realityManHour: undefined,
            remark: undefined,
          },
        };
      }
      if (type === 'edit') {
        const id = isSelectCheck(type);
        if (id) {
          new Api('/pms').fetch('', `man-hour/${id}`).then((res) => {
            state.edit = {
              visible: true,
              title: '编辑工时',
              type,
              form: {
                ...res,
                manHour: state.table.manHour,
                residueManHour: state.table.residueManHour,
              },
            };
          });
        }
      }
    }
    function isConfirm() {
      const love = {
        className: 'Plan',
        moduleName: '项目管理-计划管理-项目计划-工时',
        type: 'DELETE',
        remark: `删除了【${state.tableRef.getSelectRowKeys()}】`,
      };
      new Api('/pms', love)
        .fetch(state.tableRef.getSelectRowKeys(), `man-hour/batch?id=${props.formId}`, 'DELETE')
        .then(() => {
          message.success('操作成功');
          getPage();
        });
    }
    function submitEdit(val) {
      state.edit.visible = false;
      if (val) {
        getPage();
      }
    }

    onMounted(() => {
      getPage();
    });
    return {
      ...toRefs(state),
      ...toRefs(state6),
      clickType,
      getPage,
      submitEdit,
    };
  },
};
</script>

<style scoped lang="less">
  .manHourPage {
    width: calc(100% - 60px);
    flex: 1;
    height: 500px;
  }
  .header {
    margin: 10px;

    .sub-title {
      margin-top: 10px;
    }
  }
</style>
