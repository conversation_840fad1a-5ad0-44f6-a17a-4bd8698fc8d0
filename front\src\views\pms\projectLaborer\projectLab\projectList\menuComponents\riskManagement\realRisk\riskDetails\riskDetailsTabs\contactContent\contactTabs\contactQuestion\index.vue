<template>
  <layout :options="{ body: { scroll: true } }">
    <div class="productLibraryIndex1 layoutPage">
      <div class="productLibraryIndex_content layoutPage_content">
        <div class="productLibraryIndex_table">
          <BasicTable
            class="pdmBasicTable"
            :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
            :columns="columns"
            :data-source="dataSource"
            :bordered="false"
            :can-resize="true"
            :show-index-column="false"
            :pagination="false"
            :max-height="tableHeight"
            row-key="id"
            @change="handleChange"
            @rowClick="clickRow"
            @register="registerTable"
          >
            <template #modifyTime="{ text }">
              {{ text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '' }}
            </template>
            <template #proposedTime="{ text }">
              {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
            </template>
            <template #predictEndTime="{ text }">
              {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
            </template>
            <template #seriousLevelName="{ text }">
              <span :style="{ color: text === '严重' ? 'red' : '' }">{{ text }}</span>
            </template>
            <template #priorityLevelName="{ text }">
              <span :style="{ color: text === '最高' ? 'red' : '' }">{{ text }}</span>
            </template>
            <template #statusName="{ text }">
              <span
                :style="{ color: text === '未完成' ? '#ccc' : text == '已处理' ? 'green' : 'blue' }"
              >{{ text }}</span>
            </template>
          </BasicTable>
        </div>
      </div>

      <newButtonModal
        :btn-object-data="btnObjectData"
        @clickType="clickType"
      />
      <!-- 查看详情弹窗 -->
      <checkDetails :data="nodeData" />
      <!-- 简易弹窗提醒 -->
      <messageModal
        :title="'确认提示'"
        :show-visible="showVisible"
        @cancel="showVisible = false"
        @confirm="confirm"
      >
        <div class="messageVal">
          <InfoCircleOutlined />
          <span>{{ message }}</span>
        </div>
      </messageModal>
      <!-- 新建/编辑抽屉 -->
      <addProjectModal
        :data="addNodeModalData"
        :list-data="editdataSource"
        :projectid="id"
        @success="successSave"
      />
      <!-- 从系统添加 -->
      <AddSystemRole
        :id="id"
        :data="addSystemModalData"
        @success="successSave"
      />

      <!-- 高级搜索抽屉 -->
      <searchModal
        :data="searchData"
        :projectid="id"
        @search="searchTable"
      />
      <SearchModal
        @register="searchRegister"
        @searchEmit="searchEmit"
      />
    </div>
  </layout>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, computed, onMounted, inject, h,
} from 'vue';
import {
  Layout, BasicTable, useTable, isPower, useDrawer,
} from 'lyra-component-vue3';
import {
  Dropdown, Menu, message, Progress,
} from 'ant-design-vue';
import {
  PlusCircleOutlined,
  InfoCircleOutlined,
  DeleteOutlined,
  ImportOutlined,
  ExportOutlined,
  PlusOutlined,
} from '@ant-design/icons-vue';
import { formatterTime } from '/@/views/pms/projectLaborer/utils/time';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import addProjectModal from './modal/addProjectModal.vue';
import AddSystemRole from './modal/addSystemRole.vue';
import checkDetails from './modal/checkmodal.vue';
import searchModal from './modal/searchModal.vue';
import messageModal from '/@/views/pms/projectLaborer/components/MessageModal/messageModal.vue';
import newButtonModal from '/@/views/pms/projectLaborer/componentsList/buttonModal/newButtonModal.vue';
// import { columns } from './src/table.config';
import {
  getContactQuestionTableApi,
  deleteContactQuestionTableApi,
} from '/@/views/pms/projectLaborer/api/riskManege';
import SearchModal from './SearchModal.vue';
const [registerTable, { setLoading }] = useTable();
export default defineComponent({
  name: 'ProductLibraryIndex',
  components: {
    Layout,
    //   basicTitle,
    aDropdown: Dropdown,
    /* 表格 */
    BasicTable,
    aMenu: Menu,
    /* menu子item */
    aMenuItem: Menu.Item,
    /* 添加图标 */
    PlusCircleOutlined,
    /* 删除图标 */
    DeleteOutlined,
    //   提示图标
    InfoCircleOutlined,
    //   addNodeModal,
    messageModal,
    checkDetails,
    newButtonModal,
    /* 新建项目抽屉 */
    addProjectModal,
    /* 进度条 */
    Progress,
    /* 高级搜索 */
    searchModal,
    ImportOutlined,
    ExportOutlined,
    PlusOutlined,
    AddSystemRole,
    SearchModal,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
  },

  setup(props) {
    const [searchRegister, { openDrawer: openSearchDrawer }] = useDrawer();
    //   const router = useRouter();
    //   const layoutModelStore = layoutModel();
    const state = reactive({
      /* 搜索框value */
      searchvlaue: '',
      /* 编辑send */
      editdataSource: {},
      /* 多选 */
      selectedRowKeys: [],
      /* 列 */
      dataSource: [],
      tablehttp: {
        orders: [
          {
            asc: false,
            column: '',
          },
        ],

        query: {
          projectId: '',
        },
        // 条数
        pageSize: 10,
        /* 页数 */
        pageNum: 1,
        /* 总数 */
        total: 0,
        queryCondition: [],
      },
      // 条数
      pageSize: 10,
      /* 页数 */
      current: 1,
      /* 总数 */
      total: 20,
      addNodeModalData: {},
      /* 选择行id */
      selectedRows: [],
      addSystemModalData: {},

      showVisible: false,
      /* 简易弹窗提醒消息 */
      message: '',
      nodeData: {},
      searchData: {},
      params: {},
      /* 高度 */
      tableHeight: 400,
      powerData: [],

    });
    state.powerData = inject('powerData');
    const state6 = reactive({
      /* 右侧功能按钮 */
      btnObjectData: {
        check: { show: computed(() => isPower('FX_container_button_08', state.powerData)) },
        open: { show: computed(() => isPower('FX_container_button_09', state.powerData)) },
        add: { show: computed(() => isPower('FX_container_button_10', state.powerData)) },
        delete: { show: computed(() => isPower('FX_container_button_10', state.powerData)) },

        search: { show: computed(() => isPower('FX_container_button_12', state.powerData)) },
        //   edit: { show: true },
      },
      columns: [
        {
          title: '编号',
          dataIndex: 'number',
          align: 'left',
          key: 'number',

          width: '120px',
          // sorter: true,
          ellipsis: true,
        },
        {
          title: '名称',
          dataIndex: 'name',
          key: 'name',
          customRender({ record, text }) {
            return h(
              'span',
              {
                class: computed(() => isPower('FX_container_button_08', state.powerData)) ? 'action-btn' : '',
                title: text,
                onClick(e) {
                  if (isPower('FX_container_button_08', state.powerData)) {
                    checkData2(record);
                  }
                  e.stopPropagation();
                },
              },
              text,
            );
          },

          width: '240px',
          align: 'left',
          // slots: { customRender: 'name' },
          // sorter: true,
          ellipsis: true,
        },

        {
          title: '提出人',
          dataIndex: 'exhibitor',
          key: 'exhibitor',
          width: '70px',
          align: 'left',
          slots: { customRender: 'exhibitor' },
          // sorter: true,
          ellipsis: true,
        },
        {
          title: '提出时间',
          dataIndex: 'proposedTime',
          key: 'proposedTime',

          width: '100px',
          align: 'left',
          // sorter: true,
          ellipsis: true,
          slots: { customRender: 'proposedTime' },
        },
        {
          title: '期望完成时间',
          dataIndex: 'predictEndTime',
          key: 'predictEndTime',
          width: '100px',
          align: 'left',
          slots: { customRender: 'predictEndTime' },
          // sorter: true,
          ellipsis: true,
        },
        {
          title: '严重程度',
          dataIndex: 'seriousLevelName',
          key: 'seriousLevel',

          width: '100px',
          align: 'left',
          slots: { customRender: 'seriousLevelName' },
          // sorter: true,
          ellipsis: true,
        },
        {
          title: '优先级',
          dataIndex: 'priorityLevelName',
          key: 'priorityLevel',

          width: '80px',
          align: 'left',
          // sorter: true,
          ellipsis: true,
          slots: { customRender: 'priorityLevelName' },
        },
        {
          title: '进度',
          dataIndex: 'scheduleName',
          key: 'schedule',

          width: '80px',
          align: 'left',
          // sorter: true,
          ellipsis: true,
          slots: { customRender: 'scheduleName' },
        },
        {
          title: '状态',
          dataIndex: 'statusName',
          key: 'status',

          width: '80px',
          align: 'left',
          // sorter: true,
          ellipsis: true,
          slots: { customRender: 'statusName' },
        },
        {
          title: '负责人',
          dataIndex: 'principalName',
          key: 'principalId',

          width: '80px',
          align: 'left',
          // sorter: true,
          ellipsis: true,
          slots: { customRender: 'principalName' },
        },
        {
          title: '修改日期',
          dataIndex: 'modifyTime',
          key: 'modifyTime',

          width: '150px',
          align: 'left',
          // sorter: true,
          ellipsis: true,
          slots: { customRender: 'modifyTime' },
        },
      ],
    });
    /* 分页 */
    const pagination = computed(() => ({
      pageSize: state.tablehttp.pageSize,
      current: state.tablehttp.pageNum,
      total: state.tablehttp.total,
      // showQuickJumper: true,
      showSizeChanger: true,
      showTotal: (total) => `共${total}条`,
    }));
      /* 多选cb */
    const onSelectChange = (selectedRowKeys, selectedRows) => {
      state.selectedRowKeys = selectedRowKeys;
      state.selectedRows = selectedRows;
      // console.log('测试🚀🚀 ~~~ state.selectedRows', state.selectedRows);
    };
      /* 页数变化cb */
    const handleChange = (pag, filters, sorter: any) => {
      // 如果是多选触发,则不更新页面
      if (typeof pag.current === 'undefined') return;
      state.tablehttp.pageNum = pag.current;
      state.tablehttp.pageSize = pag.pageSize;
      state.tablehttp.orders[0].asc = sorter.order == 'ascend';
      state.tablehttp.orders[0].column = sorter.columnKey;

      getFormData();
    };
      /* 右按钮 */
    const clickType = (type) => {
      switch (type) {
        case 'edit':
          // editNode();
          break;
        case 'check':
          checkData();
          break;
        case 'add':
          addSystemRoleHandle();
          break;
        case 'open':
          // openDetail();
          openQuestion();
          break;
        case 'delete':
          // deleteNode();
          multiDelete();
          break;
        case 'search':
          // state.searchData = {};
          openSearchDrawer(true);
          break;
      }
    };
    const router = useRouter();
    let projectId: any = inject('projectId');
    const openQuestion = () => {
      if (lengthCheckHandle()) return;
      router.push({
        name: 'QuestionDetails',
        query: {
          id: state.selectedRowKeys[0],
          projectId: projectId.value,
          type: 0,
        },
      });
    };
    const addSystemRoleHandle = () => {
      // console.log('从系统创建角色');
      state.addSystemModalData = { formType: 'add' };
    };
      /* 编辑 */
    const editNode = () => {
      if (lengthCheckHandle()) return;
      // state.selectedRows = [];
      state.addNodeModalData = { formType: 'edit' };
      state.editdataSource = [...state.selectedRows];
    };
      /* 删除 */
    const deleteNode = () => {
      if (lengthCheckHandle()) return;
      // state.selectedRows = [];

      state.message = '请确认是否对当前选中数据进行删除？';
      state.showVisible = true;
    };
      /* 简易弹窗的确定cb */
    const confirm = () => {
      // 删除操作
      deletrow();
    };
    onMounted(() => {
      /* 高度变化 */
      state.tableHeight = document.body.clientHeight - 440;

      getFormData();
    });
    let riskItemId: any = inject('riskItemId');

    /* 删除操作 */
    const deletrow = () => {
      const newArr = {
        fromId: riskItemId.value,
        toIdList: state.selectedRowKeys,
      };
        // new Api('/pms')
        //   .fetch(state.selectedRowKeys, `project/removeBatch/`, 'DELETE')
      const love = {
        className: 'RiskManagement',
        moduleName: '项目管理-风险管理-实际风险-关联问题',
        type: 'DELETE',
        remark: `删除了【${state.selectedRowKeys}】`,
      };
      deleteContactQuestionTableApi(newArr, love)
        .then((res) => {
          message.success('删除成功');
          state.showVisible = false;
          getFormData();
        })
        .catch(() => {
          state.showVisible = false;
        });
    };

    const getFormData = async (obj = {}) => {
      setLoading(true);
      // state.tablehttp.query.projectId = riskItemId.value;
      const love = {
        id: riskItemId.value,
        className: 'RiskManagement',
        moduleName: '项目管理-风险管理-实际风险-关联问题',
        type: 'GET',
        remark: `获取/搜索了【${riskItemId.value}】关联问题列表`,
      };
      const res = await getContactQuestionTableApi(riskItemId.value, love, obj);
      state.dataSource = res;
      // state.tablehttp.total = res.totalSize;
      state.selectedRowKeys = [];
      state.selectedRows = [];
      setLoading(false);
    };
    function searchEmit(data) {
      getFormData(data);
    }
    /* 查看详情 */
    const checkData = () => {
      if (lengthCheckHandle()) return;
      state.nodeData = {
        ...state.dataSource.filter((item) => item.id == state.selectedRowKeys[0]),
      };
    };
    const checkData2 = (data) => {
      state.nodeData = {
        ...[JSON.parse(JSON.stringify(data))],
      };
    };
      /* 检查选择条数fn */
    const lengthCheckHandle = () => {
      if (state.selectedRows.length > 1) {
        message.warning('请选择一条数据进行操作');
        return true;
      }
      if (state.selectedRows.length == 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };
      /* 批量检查选择条数fn */
    const multiLengthCheckHandle = () => {
      if (state.selectedRows.length == 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };
    const searchTable = (params) => {
      state.tablehttp.query = params.params;
      state.tablehttp.queryCondition = params.queryCondition;

      getFormData();
      state.searchvlaue = '';
    };
      /* 打开按钮 */
    const openDetail = () => {
      if (lengthCheckHandle()) return;

      // toDetails(state.selectedRows[0]);
      state.searchvlaue = '';
    };
    const toDetails = (data) => {
      router.push({
        name: 'RiskDetails',
        query: {
          id: data.id,
          projectId: props.id,
          type: 0,
        },
      });
    };
      /* 新建项目 */
    const addNode = () => {
      state.addNodeModalData = {
        formType: 'add',
      };
    };
      /* 批量删除 */
    const multiDelete = () => {
      if (multiLengthCheckHandle()) return;

      state.message = '请确认是否对当前选中数据进行删除？';
      state.showVisible = true;
    };
      /* 搜索右上 */
    const onSearch = () => {
      /* gettable */
      state.tablehttp.queryCondition = <any>[
        {
          column: 'name',
          type: 'like',
          link: 'or',
          value: state.searchvlaue,
        },
        {
          column: 'number',
          type: 'like',
          link: 'or',
          value: state.searchvlaue,
        },
      ];
      state.tablehttp.query = { projectId: '' };
      getFormData();
    };
      /* 新建项目成功回调 */
    const successSave = () => {
      state.tablehttp.pageNum = 1;
      state.selectedRowKeys = [];
      state.selectedRows = [];

      getFormData();
      state.searchvlaue = '';
      onSearch();
    };
    const clickRow = (record, index) => {
      const num = state.selectedRowKeys.findIndex((item) => item === record.id);
      num === -1 ? state.selectedRowKeys.push(record.id) : state.selectedRowKeys.splice(num, 1);
      const row = state.selectedRows.findIndex((item) => item.id === record.id);
      row === -1 ? state.selectedRows.push(record) : state.selectedRows.splice(row, 1);
    };
    return {
      ...toRefs(state),
      ...toRefs(state6),
      clickRow,
      clickType,
      pagination,
      onSelectChange,
      handleChange,
      formatterTime,
      confirm,
      addNode,
      dayjs,
      multiDelete,
      onSearch,
      successSave,
      searchTable,
      addSystemRoleHandle,
      registerTable,
      setLoading,
      searchRegister,
      searchEmit,
    };
  },

  // mounted() {}
});
</script>
<style lang="less" scoped>
  @import url('/@/views/pms/projectLaborer/statics/style/page.less');
  @import url('/@/views/pms/projectLaborer/statics/style/margin.less');
</style>
