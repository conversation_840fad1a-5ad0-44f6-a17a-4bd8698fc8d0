package com.chinasie.orion.service.reporting.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.domain.dto.reporting.ProjectWeeklyContentDTO;
import com.chinasie.orion.domain.entity.reporting.ProjectWeeklyContent;
import com.chinasie.orion.domain.vo.ProjectInternalAssociationRedisVO;
import com.chinasie.orion.domain.vo.ProjectInternalAssociationRedisVO;
import com.chinasie.orion.domain.vo.reporting.ProjectDailyStatementContentVO;
import com.chinasie.orion.domain.vo.reporting.ProjectWeeklyContentVO;
import com.chinasie.orion.helper.InternalAssociationRedisHelper;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.helper.InternalAssociationRedisHelper;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.reporting.ProjectWeeklyContentMapper;
import com.chinasie.orion.sdk.metadata.page.Page;

import com.chinasie.orion.service.reporting.ProjectWeeklyContentService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * ProjectWeeklyContent 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-14 08:50:29
 */
@Service
public class ProjectWeeklyContentServiceImpl extends OrionBaseServiceImpl<ProjectWeeklyContentMapper, ProjectWeeklyContent> implements ProjectWeeklyContentService {

    @Autowired
    private InternalAssociationRedisHelper internalAssociationRedisHelper;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public ProjectWeeklyContentVO detail(String id) throws Exception {
        ProjectWeeklyContent projectWeeklyContent = this.getById(id);
        ProjectWeeklyContentVO result = BeanCopyUtils.convertTo(projectWeeklyContent, ProjectWeeklyContentVO::new);
        return result;
    }

    /**
     * 新增
     * <p>
     * * @param projectWeeklyContentDTO
     */
    @Override
    public ProjectWeeklyContentVO create(ProjectWeeklyContentDTO projectWeeklyContentDTO) throws Exception {
        ProjectWeeklyContent projectWeeklyContent = BeanCopyUtils.convertTo(projectWeeklyContentDTO, ProjectWeeklyContent::new);
        this.save(projectWeeklyContent);
        ProjectWeeklyContentVO rsp = BeanCopyUtils.convertTo(projectWeeklyContent, ProjectWeeklyContentVO::new);
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param projectWeeklyContentDTO
     */
    @Override
    public Boolean edit(ProjectWeeklyContentDTO projectWeeklyContentDTO) throws Exception {
        ProjectWeeklyContent projectWeeklyContent = BeanCopyUtils.convertTo(projectWeeklyContentDTO, ProjectWeeklyContent::new);
        return this.updateById(projectWeeklyContent);
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        return this.remove(ids);
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public Page<ProjectWeeklyContentVO> pages(Page<ProjectWeeklyContentDTO> pageRequest) throws Exception {


        return null;
    }

    @Override
    public List<ProjectWeeklyContentVO> listContentByWeeklyId(String id) {
        LambdaQueryWrapper<ProjectWeeklyContent> queryWrapper = new LambdaQueryWrapper<>(ProjectWeeklyContent.class);
        queryWrapper.eq(ProjectWeeklyContent::getWeeklyId, id);
        List<ProjectWeeklyContent> list = this.list(queryWrapper);
        return BeanCopyUtils.convertListTo(list, ProjectWeeklyContentVO::new);
    }

    @Override
    public Map<String, List<ProjectWeeklyContentVO>> getMapByDataIdList(List<String> idList) throws Exception {
        if (CollectionUtils.isBlank(idList)){
            return new HashMap<>();
        }
        LambdaQueryWrapperX<ProjectWeeklyContent> weeklyContentLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
//        projectDailyStatementContentLambdaQueryWrapper.select(ProjectDailyStatementContent::getId
//                ,ProjectDailyStatementContent::getContent
//                ,ProjectDailyStatementContent::getDailyStatementId);
        weeklyContentLambdaQueryWrapperX.in(ProjectWeeklyContent::getWeeklyId,idList);
        List<ProjectWeeklyContent> projectWeeklyContents = this.list(weeklyContentLambdaQueryWrapperX);
        if(CollectionUtils.isBlank(projectWeeklyContents)){
            return Collections.emptyMap();
        }
        List<ProjectWeeklyContentVO> projectWeeklyContentVOS = BeanCopyUtils.convertListTo(projectWeeklyContents, ProjectWeeklyContentVO::new);
        setRelationshipName(projectWeeklyContentVOS);
        return projectWeeklyContentVOS.stream().collect(Collectors.groupingBy(ProjectWeeklyContentVO::getWeeklyId));
    }


    //添加关联对象名称
    public void setRelationshipName(List<ProjectWeeklyContentVO> contentVOList) throws Exception {
        if (CollectionUtils.isBlank(contentVOList)){
            return;
        }
        List<String> entityByIdList = contentVOList.stream().map(ProjectWeeklyContentVO::getRelationship).distinct().collect(Collectors.toList());
        List<ProjectInternalAssociationRedisVO> projectInternalAssociationRedisVOS = internalAssociationRedisHelper.queryForEntityByIds(entityByIdList);
        Map<String, ProjectInternalAssociationRedisVO> map = projectInternalAssociationRedisVOS.stream().collect(Collectors.toMap(ProjectInternalAssociationRedisVO::getId, p -> p));
        contentVOList.forEach(c->{
            ProjectInternalAssociationRedisVO projectInternalAssociationRedisVO = map.get(c.getRelationship());
            if (projectInternalAssociationRedisVO != null){
                c.setRelationshipName("【"+projectInternalAssociationRedisVO.getName()+"】"+projectInternalAssociationRedisVO.getInnerName());
            }
        });
    }
}

