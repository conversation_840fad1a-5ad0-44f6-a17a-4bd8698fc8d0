<script setup lang="ts">
import { STable } from '@surely-vue/table';
import { computed, inject, reactive } from 'vue';

const detailsData: Record<string, any> = inject('detailsData', reactive({}));

const columns = [
  {
    title: '序号',
    dataIndex: 'no',
    width: 50,
    fixed: 'left',
    customRender({ index }) {
      return index + 1;
    },
  },
  {
    title: '姓名',
    dataIndex: 'userName',
    width: 100,
    fixed: 'left',
    customRender({ index, record }) {
      if (index === 0) {
        return {
          props: {
            colSpan: 3,
          },
          children: record.orgName,
        };
      }
    },
  },
  {
    title: '工号',
    dataIndex: 'userCode',
    width: 100,
    fixed: 'left',
  },
  {
    title: '所属中心/部门',
    dataIndex: 'orgName',
    width: 150,
    fixed: 'left',
  },
  {
    title: '服务地点',
    dataIndex: 'servicePlace',
    width: 100,
  },
  {
    title: '服务内容',
    dataIndex: 'serviceContent',
    width: 100,
  },
  {
    title: '合同岗级',
    dataIndex: 'jobGrade',
    width: 100,
  },
  {
    title: '工作量（人/月）',
    dataIndex: 'workload',
    width: 125,
  },
  {
    title: '现场管理费/基地后勤费（元）',
    dataIndex: 'logisticsAmt',
    width: 205,
    align: 'right',
  },
  {
    title: '差旅费（不含税）',
    children: [
      {
        title: '住宿费',
        dataIndex: 'hotelAmount',
        width: 100,
        align: 'right',
      },
      {
        title: '交通费',
        dataIndex: 'trafficAmount',
        width: 100,
        align: 'right',
      },
      {
        title: '换乘费',
        dataIndex: 'transferAmount',
        width: 100,
        align: 'right',
      },
    ],
  },
  {
    title: '其他可选费用（含税/不含税）',
    children: [
      {
        title: 'RP体检费',
        dataIndex: 'medicalExaminationAmt',
        width: 100,
        align: 'right',
      },
      {
        title: '劳保用品费',
        dataIndex: 'articlesAmt',
        width: 100,
        align: 'right',
      },
      {
        title: '餐厅管理费',
        dataIndex: 'restaurantAmt',
        width: 100,
        align: 'right',
      },
      {
        title: '其他费用',
        dataIndex: 'otherAmt',
        width: 100,
        align: 'right',
      },
    ],
  },
  {
    title: '出差情况',
    dataIndex: 'travelDays',
    width: 150,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 150,
  },
];

const dataSource = computed(() => {
  if (detailsData.acceptanceDetail?.id) {
    return [detailsData.acceptanceDetail].concat(detailsData.acceptanceDetail?.children || []);
  }
  return [];
});

</script>

<template>
  <STable
    rowKey="id"
    size="small"
    bordered
    :expandIconColumnIndex="-1"
    :dataSource="dataSource"
    :columns="columns"
    :pagination="false"
  />
</template>

<style scoped lang="less">

</style>
