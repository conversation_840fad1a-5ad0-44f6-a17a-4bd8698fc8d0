<script setup lang="ts">
import {
  BasicForm, useForm, BasicCard, openSelectUserModal, openModal, DataStatusTag, BasicEditor,
} from 'lyra-component-vue3';
import {
  h,
  onBeforeMount, onMounted, Ref, ref,
} from 'vue';
import Api from '/@/api';
import { message, Select as ASelect } from 'ant-design-vue';
import dayjs from 'dayjs';
import { initForm, getDictByTree } from '../utils';
import { useUserStore } from '/@/store/modules/user';
import { SelectListTableAndTable } from '/@/views/pms/components';

const props = withDefaults(defineProps<{
  formId:string|undefined,
  formType:string,
  drawerData:object,
  addQuestionTableApi:any
  isQuestion: boolean,
  id:string,
  dataSource: object,
}>(), {
  formId: '',
  formType: 'add',
  drawerData: () => ({}),
  addQuestionTableApi: null,
  isQuestion: false,
  id: '',
});
const userInfo:any = useUserStore().getUserInfo;
const problemPhenomenonOneOptions:Ref<any[]> = ref([]);
const problemPhenomenonTwoOptions:Ref<any[]> = ref([]);
const problemPhenomenonThOptions:Ref<any[]> = ref([]);
const opClassificationOptions:Ref<any[]> = ref([]);
const productNumberOptions:Ref<any[]> = ref([]);
const materialNumberOptions:Ref<any[]> = ref([]);
const principalId:Ref<string> = ref('');// 问题负责人ID
const exhibitor:Ref<string> = ref(userInfo.id);// 问题提出人ID
const reviewPoints:Ref<string> = ref('');// 评审要点ID
const [register, { setFieldsValue, validateFields, getFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas: initForm({
    openSelectUser,
    clearField,
    selectTable,
  }, {
    problemPhenomenonOneOptions,
    problemPhenomenonTwoOptions,
    problemPhenomenonThOptions,
    opClassificationOptions,
    productNumberOptions,
    materialNumberOptions,
  }, getProductEstimateList),
});
function openSelectUser(field) {
  openSelectUserModal([], {
    selectType: 'radio',
    okHandle(data:any[]) {
      if (data.length === 0) {
        message.warning('请选择人员');
        return new Promise((resolve, reject) => { reject(false); });
      }
      if (field === 'principalName') {
        setFieldsValue({ principalName: data[0].name });
        principalId.value = data[0].id;
      }
      if (field === 'exhibitorName') {
        setFieldsValue({ exhibitorName: data[0].name });
        exhibitor.value = data[0].id;
      }
    },
  });
}
function selectTable() {
  const selectListTableRef = ref();
  openModal({
    title: '添加评审要点',
    width: 1100,
    height: 700,
    content(h) {
      return h(SelectListTableAndTable, {
        ref: selectListTableRef,
        getListApi,
        getTableData,
        showLeftList: true,
        selectType: 'radio',
        rightName: 'content',
        columns: [
          {
            title: '评审阶段',
            dataIndex: 'reviewPhaseName',
          },
          {
            title: '要点类型',
            dataIndex: 'essentialsTypeName',
          },
          {
            title: '评审要点内容',
            dataIndex: 'content',
            minWidth: 250,
          },
          {
            title: '状态',
            dataIndex: 'dataStatus',
            customRender: ({ record }) => h(DataStatusTag, { statusData: record.dataStatus }),
          },
          {
            title: '修改人',
            dataIndex: 'modifyName',
          },
          {
            title: '修改时间',
            dataIndex: 'modifyTime',
            type: 'dateTime',
          },
        ],
        isTableTree: false,
      });
    },
    async onOk() {
      let selectTable = await selectListTableRef.value.getFormData();
      if (selectTable.selectedRowKeys.length === 0) {
        message.warning('请选择评审要点');
        return Promise.reject('');
      }
      await setFieldsValue({ reviewPointsName: selectTable.selectTableData[0].content });
      reviewPoints.value = selectTable.selectTableData[0].id;
    },
  });
}
function clearField(data) {
  setFieldsValue(data);
  if (typeof data.principalName !== 'undefined') {
    principalId.value = '';
  }

  if (typeof data.exhibitorName !== 'undefined') {
    exhibitor.value = '';
  }

  if (typeof data.reviewPointsName !== 'undefined') {
    reviewPoints.value = '';
  }
}
function getListApi(params) {
  return new Api('/pms').fetch(params, 'reviewLibrary/page', 'POST');
}
function getTableData(id, params) {
  return new Api('/pms').fetch(params, `reviewEssentials/page/${id}`, 'POST');
}
onMounted(async () => {
  loading.value = true;
  problemPhenomenonOneOptions.value = await getDictByTree('problemPhenomenonOne');
  opClassificationOptions.value = await getDictByTree('opinionClassification');
  productNumberOptions.value = await getProductList();
  if (props.formType === 'edit') {
    await getFormData();
  } else {
    loading.value = false;
  }
  // props.formId && getFormData();
});
async function getProductList() {
  return new Api('/pms').fetch({ projectId: props.drawerData.projectId }, 'projectToProduct/getProductEstimateMaterialList', 'GET');
}
async function getProductEstimateList(params) {
  params.projectId = props.drawerData.projectId;
  return new Api('/pms').fetch(params, 'projectToProduct/getMaterialList', 'GET');
}

const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  new Api('/pms').fetch('', `question-management/detail/${props.formId}`, 'GET').then(async (res) => {
    if (res.principalName) {
      principalId.value = res.principalId;
    }
    if (res.proposedTime) {
      res.proposedTime = dayjs(res.proposedTime).format('YYYY-MM-DD HH:mm:ss');
    }
    if (res.exhibitor) {
      exhibitor.value = res.exhibitor;
    }
    if (res.problemPhenomenonTwo) {
      let selectedItem = problemPhenomenonOneOptions.value.find((item) => item.number === res.problemPhenomenonOne);
      problemPhenomenonTwoOptions.value = selectedItem?.children || [];
    }
    if (res.problemPhenomenonTh) {
      let selectedItem = problemPhenomenonTwoOptions.value.find((item) => item.number === res.problemPhenomenonTwo);
      problemPhenomenonThOptions.value = selectedItem?.children || [];
      res.problemPhenomenonTh = [res.problemPhenomenonTh];
    } else {
      res.problemPhenomenonTh = [];
    }
    if (res.reviewPoints) {
      reviewPoints.value = res.reviewPoints;
    }
    if (res.opClassification) {
      res.opClassification = res.opClassification.split(',');
    }
    if (res.productNumber) {
      let itemData = productNumberOptions.value.find((item) => item.number === res.productNumber);
      materialNumberOptions.value = await getProductEstimateList({ productId: itemData.id });
    }
    await setFieldsValue(res);
    loading.value = false;
  }).catch((err) => {
    // state.loading = false;
  });
}

// eslint-disable-next-line vue/no-expose-after-await
defineExpose({
  async onSubmit() {
    let formData = await validateFields();
    if (formData.principalName) {
      formData.principalId = principalId.value;
    }
    if (formData.exhibitorName) {
      formData.exhibitor = exhibitor.value ? exhibitor.value : userInfo.id;
    }
    if (formData.reviewPointsName) {
      formData.reviewPoints = reviewPoints.value;
    }
    if (formData?.predictEndTime) {
      formData.predictEndTime = dayjs(formData.predictEndTime).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
    }
    if (formData?.proposedTime) {
      formData.proposedTime = dayjs(formData.proposedTime).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
    }
    if (formData.opClassification && Array.isArray(formData.opClassification)) {
      formData.opClassification = formData.opClassification.join(',');
    }
    if (Array.isArray(formData.problemPhenomenonTh) && formData.problemPhenomenonTh.length > 0) {
      formData.problemPhenomenonTh = formData.problemPhenomenonTh[0];
    } else {
      formData.problemPhenomenonTh = '';
    }
    if (props.isQuestion) {
      formData.isQuestion = true;
    }
    // let api = props.formType === 'add' ? 'question-management/save' : 'question-management/edit';
    if (props.formType === 'add') {
      formData = Object.assign(props.drawerData, formData);
      await props.addQuestionTableApi(formData);
    } else {
      formData.id = props.formId;
      await new Api('/pms').fetch(formData, 'question-management/edit', 'PUT');
    }
    message.success(props.isQuestion ? '转问题成功' : props.formType === 'add' ? '新增问题成功' : '编辑问题成功');
  },
});
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  >
    <template #content="{ model, field }">
      <BasicEditor v-model:value="model[field]" />
    </template>
    <template #contactPersonForTheIssue>
      <span
        class="contact-tips"
      >问题联系人: 刘占阳 13776062787</span>
    </template>
    <template #scoreInfo="{ model }">
      <BasicCard
        :title="model?.questionType === 'questionType_1'?'产品研发问题信息':'评审问题信息'"
        class="basic-card"
      />
    </template>

    <template #reasionThree="{ model }">
      <div>
        <ASelect
          v-model:value="model.problemPhenomenonTh"
          :options="problemPhenomenonThOptions"
        />
      </div>
    </template>
  </BasicForm>
</template>

<style scoped lang="less">
.basic-card{
  margin: 0 !important;
  border: 0 !important;
  :deep(.card-content){
    margin: 0 !important;
  }
}
.contact-tips{
  color: red;
  font-size: 12px;
  margin-left: 10px;

}
</style>
