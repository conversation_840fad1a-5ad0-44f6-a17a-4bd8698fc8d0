import Api from '/@/api';
const base = '/pms';
enum zApi {
  /* 风险分页 */
  riskManagePage = 'risk-management/getPage',
  /* 新增风险 */
  addRisk = 'risk-management/save',
  /* 批量删除风险 */
  deleteRisk = 'risk-management/removeBatch',
  // 编辑
  editRisk = 'risk-management/edit',
  // modal 风险预期发生时间
  startEffectRiskList = 'risk-management/predictStartTimeList',
  // modal风险概率
  probRiskList = 'risk-management/riskProbabilityList',
  // 风险类型
  RistTypeList = 'risk-management/riskTypeList',
  // 风险影响
  EffectRiskList = 'risk-management/riskInfluenceList',
  // 风险对应策略
  soluteRiskList = 'risk-management/copingStrategyList',
  // 模糊查询负责人
  roleList = 'project-role-user/getListByName',
  // 预警设置提醒人列表
  // PreRiskRoleList = '/project-role/getList',
  PreRiskRoleList = '/warning-setting/role',
  // 模糊查询负责人
  planTreeList = 'plan/tree/simple/list',
  // 状态类型列表
  statusList = 'risk-management/statusList',
  // 状态类型列表
  riskDetails = 'risk-management/detail',
  // 获取关联计划列表
  riskContactPlan = '/risk-management/relation/plan',
  // 获取关联计划列表
  DeleteRiskContactPlan = 'risk-management/relation/plan/batch',
  // 风险预案-page--获取
  getPreRiskPage = 'risk-plan/getPage',
  // 风险预案-page--获取详情
  getPreRiskItemDetails = 'risk-plan/detail',
  // 风险预案-page--详情编辑
  editPreRiskItemDetails = 'risk-plan/edit',
  // 风险预案-page--删除
  deletePreRiskPage = 'risk-plan/removeBatch',
  // 风险预案-page--add
  addPreRiskPage = 'risk-plan/save',
  // 风险预案-page-编辑
  editPreRiskPage = 'risk-plan/edit',
  // 风险预警设置-左侧类型
  getWarnType = 'warning-setting/typeList',
  // 风险预警设置-中间table
  getWarnTypePage = 'warning-setting/getList',
  // 风险预警设置-开启关闭 //0 禁用
  banWarnTypePage = 'warning-setting/takeEffectBatch',
  // 风险预警频率
  warningFrequencyList = 'warning-setting/warningFrequencyList',
  // 风险预警设置详情
  warningFrequencyItemDetails = 'warning-setting/detail',
  // 风险预警设置
  editwarningItemDetails = 'warning-setting/edit',
  // 预警方式
  getWarningWayList = 'warning-setting/warningWayList',
  // 关联问题
  getContactQuestionTable = 'risk-management/relation/question',
  // --关联问题
  deleteContactQuestionTable = 'risk-management/relation/question/batch',
  // --搜索添加
  searchContactQuestion = 'question-management/search/list',
  // --新增关联问题
  addContactQuestion = 'risk-management/relation/question'
}

// ++关联问题
export function addContactQuestionApi(params) {
  return new Api(base).fetch(params, `${zApi.addContactQuestion}/`, 'POST');
}
// --关联问题
export function searchContactQuestionApi(params) {
  return new Api(base).fetch(params, `${zApi.searchContactQuestion}/`, 'POST');
}
// --关联问题
export function deleteContactQuestionTableApi(params) {
  return new Api(base).fetch(params, `${zApi.deleteContactQuestionTable}/`, 'DELETE');
}
// get关联问题
export function getContactQuestionTableApi(params, obj = {}) {
  return new Api(base).fetch(obj, `${zApi.getContactQuestionTable}/${params}`, 'POST');
}
// 分页
export function riskManagePageApi(params) {
  return new Api(base).fetch(params, `${zApi.riskManagePage}/`, 'POST');
}
// ++
export function addRiskApi(params) {
  const love = {
    name: params?.name,
    className: 'Plan',
    moduleName: '零组件', // 模块名称
    type: 'SAVE', // 操作类型
    remark: `添加了【${params?.name}】`,
  };
  return new Api(base).fetch(params, `${zApi.addRisk}/`, 'POST');
}
// --
export function deleteRiskApi(params) {
  return new Api(base).fetch(params, `${zApi.deleteRisk}/`, 'DELETE');
}
// 编辑
export function editRiskApi(params, record) {
  const love = {
    id: params?.id,
    name: params?.name,
    className: 'Plan',
    moduleName: '零组件', // 模块名称
    type: 'UPDATE', // 操作类型
    remark: `编辑了【${params?.id}】`,
  };
  return new Api(base, record || love).fetch(params, `${zApi.editRisk}/`, 'PUT');
}
// modal 风险预期发生时间
export function startEffectRiskListApi() {
  return new Api(base).fetch('', `${zApi.startEffectRiskList}/`, 'GET');
}
// modal 风险概率
export function probRiskListApi() {
  return new Api(base).fetch('', `${zApi.probRiskList}/`, 'GET');
}
// modal 风险类型
export function RistTypeApi() {
  return new Api(base).fetch('', `${zApi.RistTypeList}/`, 'GET');
}
// modal 风险影响
export function EffectRiskListApi() {
  return new Api(base).fetch('', `${zApi.EffectRiskList}/`, 'GET');
}
// modal 风险对应策略
export function soluteRiskListApi() {
  return new Api(base).fetch('', `${zApi.soluteRiskList}/`, 'GET');
}
// modal 状态类型
export function statusListApi() {
  return new Api(base).fetch('', `${zApi.statusList}/`, 'GET');
}
// modal 模糊查询
export function roleListApi(params, projectId) {
  return new Api(base).fetch(params, `${zApi.roleList}/${projectId}?name=${params.name}`, 'POST');
}
// modal 关联任务plan-treet
export function planTreeListApi(params) {
  return new Api(base).fetch(params, `${zApi.planTreeList}/`, 'POST');
}
// 删除关联计划
export function DeleteRiskContactPlanApi(params) {
  return new Api(base).fetch(params, `${zApi.DeleteRiskContactPlan}/`, 'DELETE');
}
// modal 关联任务plan-treet
export function riskDetailsApi(params) {
  return new Api(base).fetch('', `${zApi.riskDetails}/${params}`, 'GET');
}
// modal 关联计划
export function riskContactPlanApi(params, obj = {}) {
  return new Api(base).fetch(obj, `${zApi.riskContactPlan}/${params}`, 'POST');
}
// 风险预案-page
export function getPreRiskPageApi(params) {
  return new Api(base).fetch(params, `${zApi.getPreRiskPage}/`, 'POST');
}
// 风险预案-详情
export function getPreRiskItemDetailsApi(params) {
  return new Api(base).fetch('', `${zApi.getPreRiskItemDetails}/${params}`, 'GET');
}
// 风险预案-page--删除
export function deletePreRiskPageApi(params) {
  return new Api(base).fetch(params, `${zApi.deletePreRiskPage}/`, 'DELETE');
}
// 风险预案-page--add
export function addPreRiskPageApi(params) {
  return new Api(base).fetch(params, `${zApi.addPreRiskPage}/`, 'POST');
}
// 风险预案-page--编辑
export function editPreRiskPageApi(params) {
  return new Api(base).fetch(params, `${zApi.editPreRiskPage}/`, 'PUT');
}
// 风险预警设置-左侧类型
export function getWarnTypeApi() {
  return new Api(base).fetch('', `${zApi.getWarnType}`, 'GET');
}
// 风险预警设置-中间table
export function getWarnTypePageApi(parmas) {
  return new Api(base).fetch(parmas, `${zApi.getWarnTypePage}`, 'POST');
}
// 风险预警设置-开启关闭
export function banWarnTypePageApi(parmas) {
  return new Api(base).fetch(parmas, `${zApi.banWarnTypePage}/`, 'PUT');
}
// 风险预案详情-编辑
export function editPreRiskItemDetailsApi(parmas) {
  return new Api(base).fetch(parmas, `${zApi.editPreRiskItemDetails}/`, 'PUT');
}
// 风险预警频率
export function warningFrequencyListApi() {
  return new Api(base).fetch('', `${zApi.warningFrequencyList}/`, 'GET');
}
// 风险预警详情
export function warningFrequencyItemDetailsApi(parmas) {
  return new Api(base).fetch('', `${zApi.warningFrequencyItemDetails}/${parmas}/`, 'GET');
}
// 风险预警详情编辑
export function editwarningItemDetailsApi(parmas) {
  return new Api(base).fetch(parmas, `${zApi.editwarningItemDetails}/`, 'PUT');
}
// 风险预警方式
export function getWarningWayListApi() {
  return new Api(base).fetch('', `${zApi.getWarningWayList}/`, 'GET');
}
// 风险预警提醒人列表
export function preRiskRoleListApi(params) {
  return new Api(base).fetch('', `${zApi.PreRiskRoleList}/${params}/`, 'GET');
}
