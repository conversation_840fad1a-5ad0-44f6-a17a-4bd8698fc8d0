<template>
  <div class="card-item">
    <div
      class="card-header"
      :style="{ cursor: isPower('PMS_XMLB_container_01_button_03', record?.['rdAuthList'] ?? [])?'pointer':'default'}"
      @click="()=>{
        isPower('PMS_XMLB_container_01_button_03', record?.['rdAuthList'] ?? []) && emit('actionClick','look',record)
      }"
    >
      <DataStatusTag
        v-if="record['dataStatus']"
        style="flex-shrink: 0"
        :statusData="record['dataStatus']"
      />
      <span
        class="title flex-te"
        :title="record['name']"
      >{{ record['name'] }}</span>
    </div>
    <div class="card-main">
      <div class="card-main-header">
        <span>本周里程碑完成：{{ record['weekCount'] }}</span>
        <span>本月里程碑完成：{{ record['monthCount'] }}</span>
      </div>
      <div class="card-main-content">
        <div class="left">
          <span>项目负责人</span><span class="percent">{{
            record['percent']
          }}</span>
          <span>{{ record['resPersonName'] }}</span>
          <!--          <span>执行 {{ formatMoney(record['executeSumMoney'],10000) }} / 预算 {{ formatMoney(record['budgetSumMoney'],10000) }}</span>-->
        </div>
        <div class="right">
          <span>里程碑完成度</span><span class="percent">{{
            record['finishedDegree']
          }}%</span><span>{{ record['finishedCount'] }} / {{ record['totalCount'] }} 项</span>
        </div>
      </div>
    </div>
    <div class="card-footer">
      <div>
        <Icon
          v-if="isPower('PMS_XMLB_container_01_button_01',record?.['rdAuthList']??[])"
          class="icon"
          icon="sie-icon-bianji"
          size="16"
          @click="emit('actionClick','edit', record)"
        />
        <Icon
          v-else
          class="disabled-icon"
          icon="sie-icon-bianji"
          size="16"
        />
      </div>
      <!--      <div>-->
      <!--        <Icon-->
      <!--          v-if="isPower('XM_list_button_04',record?.['rdAuthList']??[]) || isPower('XM_list_button_05',record?.['rdAuthList']??[])"-->
      <!--          class="icon"-->
      <!--          :icon="`fa ${record['like']?'fa-star':'fa-star-o'}`"-->
      <!--          size="16"-->
      <!--          :color="record['like']?'orange':''"-->
      <!--          @click="emit('actionClick','collect', record)"-->
      <!--        />-->
      <!--        <Icon-->
      <!--          v-else-->
      <!--          class="disabled-icon"-->
      <!--          :icon="`fa ${record['like']?'fa-star':'fa-star-o'}`"-->
      <!--          size="16"-->
      <!--        />-->
      <!--      </div>-->
      <!--      <div>-->
      <!--        <Icon-->
      <!--          v-if="isPower('PMS_XMLB_container_01_button_02',record?.['rdAuthList']??[])"-->
      <!--          class="icon"-->
      <!--          icon="sie-icon-del"-->
      <!--          size="16"-->
      <!--          @click="emit('actionClick','del', record)"-->
      <!--        />-->
      <!--        <Icon-->
      <!--          v-else-->
      <!--          class="disabled-icon"-->
      <!--          icon="sie-icon-del"-->
      <!--          size="16"-->
      <!--        />-->
      <!--      </div>-->
    </div>
  </div>
</template>

<script setup lang="ts">
import { DataStatusTag, Icon, isPower } from 'lyra-component-vue3';
import { computed } from 'vue';
import { formatMoney } from '../utils';

const props = defineProps<{
  record: any
  powerData:any
}>();

const emit = defineEmits<{
  (e: 'actionClick', key: string, record: string): void
}>();

const record = computed(() => {
  let {
    executeSumMoney,
    budgetSumMoney,
  } = props.record;
  executeSumMoney = Number(executeSumMoney);
  budgetSumMoney = Number(budgetSumMoney);

  return {
    ...props.record,
    executeSumMoney,
    budgetSumMoney,
  };
});

</script>

<style scoped lang="less">
.card-item {
  min-width: 330px;
  display: flex;
  flex-direction: column;
  border: 1px solid #E9E9E9;
  box-sizing: border-box;
  border-radius: 2px;
  overflow: hidden;

  &:hover {
    box-shadow: #004757;
  }

  &:hover .card-header .title {
    color: ~`getPrefixVar('primary-color')`;
  }

  .card-header {
    display: flex;
    align-items: center;
    padding: 10px 16px;

    .title {
      margin-left: 4px;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      font-weight: 600;
    }
  }

  .card-main {
    padding: 20px 0;
    border-top: 1px solid #E9E9E9;
    border-bottom: 1px solid #E9E9E9;

    &-header {
      width: 300px;
      margin: 0 auto;
      display: flex;
      align-items: center;
      justify-content: space-between;

      span {
        font-size: 14px;
        color: rgba(0, 0, 0, .65);
        line-height: 20px;
      }
    }

    &-content {
      border-radius: 4px;
      border: 1px solid #E9E9E9;
      padding: 15px;
      margin: 20px auto 0;
      display: flex;
      width: 300px;
      justify-content: space-between;

      div {
        display: flex;
        flex-direction: column;
        font-size: 12px;
        color: rgba(0, 0, 0, .45);

        .percent {
          color: ~`getPrefixVar('primary-color')`;
          font-size: 24px;
          line-height: 32px;
          margin: 4px 0;
          font-weight: 600;
        }
      }
    }
  }

  .card-footer {
    background-color: #FAFAFA;
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: 40px;

    > div {
      position: relative;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      flex: 1;

      .icon:hover {
        color: ~`getPrefixVar('primary-color')`;
        cursor: pointer;
      }
    }

    div + div::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      transform: translate(0, -50%);
      width: 1px;
      height: 14px;
      background-color: #E9E9E9;
    }
  }
}
.disabled-icon{
  opacity: 0.25;
  cursor: not-allowed;
}
</style>
