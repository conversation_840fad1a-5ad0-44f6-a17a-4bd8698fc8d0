package com.chinasie.orion.domain.dto;


import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.Boolean;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProjectIncome DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-15 09:24:02
 */
@ApiModel(value = "ProjectIncomeDTO对象", description = "收益管理")
@Data
@ExcelIgnoreUnannotated
public class ProjectIncomeDTO extends ObjectDTO implements Serializable{

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @ExcelProperty(value = "项目id ", index = 0)
    private String projectId;

    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    @ExcelProperty(value = "产品id", index = 1)
    private String productId;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    @ExcelProperty(value = "产品名称 ", index = 13)
    private String productName;

    /**
     * 产品编码
     */
    @ApiModelProperty(value = "产品编码")
    @ExcelProperty(value = "产品编码 ", index = 2)
    private String productNumber;

    /**
     * 销售是否结束
     */
    @ApiModelProperty(value = "销售是否结束")
    @ExcelProperty(value = "销售是否结束 ", index = 3)
    private Boolean saleOver;

//    /**
//     * 是否已签单
//     */
//    @ApiModelProperty(value = "是否已签单")
//    @ExcelProperty(value = "是否已签单 ", index = 4)
//    private Boolean signBill;

    /**
     * 预期合同年份
     */
    @ApiModelProperty(value = "预期合同年份")
    @ExcelProperty(value = "预期合同年份 ", index = 5)
    private Date expectedContractYear;

    /**
     * 原预期产品单价
     */
    @ApiModelProperty(value = "原预期产品单价")
    @ExcelProperty(value = "原预期产品单价 ", index = 6)
    private BigDecimal expectedProductPrice;

    /**
     * 原预期总产出
     */
    @ApiModelProperty(value = "原预期总产出")
    @ExcelProperty(value = "原预期总产出 ", index = 7)
    private BigDecimal origExpectedOutcome;

    /**
     * 现预期总产出
     */
    @ApiModelProperty(value = "现预期总产出")
    @ExcelProperty(value = "现预期总产出 ", index = 8)
    private BigDecimal expectedOutcomes;

//    /**
//     * 已签订合同金额
//     */
//    @ApiModelProperty(value = "已签订合同金额")
//    @ExcelProperty(value = "已签订合同金额 ", index = 9)
//    private BigDecimal contractAmount;
//
//    /**
//     * 预期差异比
//     */
//    @ApiModelProperty(value = "预期差异比")
//    @ExcelProperty(value = "预期差异比 ", index = 10)
//    private String expectedDiffRate;
//
//    /**
//     * 完成百分比
//     */
//    @ApiModelProperty(value = "完成百分比")
//    @ExcelProperty(value = "完成百分比 ", index = 11)
//    private String completeRate;

    /**
     * 收益策划id
     */
    @ApiModelProperty(value = "收益策划id")
    @ExcelProperty(value = "收益策划id ", index = 12)
    private String approvalIncomeId;




}
