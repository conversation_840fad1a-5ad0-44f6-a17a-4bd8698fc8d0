<template>
  <div style="height: 500px;overflow: hidden">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <div class="flex flex-ac">
          <span>审核编号：</span>
          <span>{{ $props.record?.number }}</span>
        </div>
      </template>
    </OrionTable>
  </div>
</template>

<script setup lang="ts">
import { DataStatusTag, OrionTable } from 'lyra-component-vue3';
import {
  h, inject, ref, Ref, unref,
} from 'vue';
import dayjs from 'dayjs';
import { childActionKey } from '../type';
// import { postHistoryPages } from '/@/views/pms/api';
import {
  contractDetailKey,
} from '../../types';
import Api from '/@/api';

const props = defineProps<{
  record:{
    number:string
  }
}>();
const emit = defineEmits<{
    (e:'closeModal'):void
}>();

const contractDetail = inject(contractDetailKey);
const childAction = inject(childActionKey);
const tableRef:Ref = ref();
const tableOptions = {
  showTableSetting: false,
  showSmallSearch: false,
  showToolButton: false,
  api(params) {
    return new Api('/pas/contractPayNodeConfirmAuditRecord').getPage({
      ...params,
      query: {
        confirmId: props?.record?.id,
      },
    });
  },
  columns: [
    {
      title: '审核时间',
      dataIndex: 'auditDate',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '';
      },
    },
    {
      title: '审核状态',
      dataIndex: 'dataStatus',
      customRender({ text }) {
        return text ? h(DataStatusTag, {
          statusData: text,
        }) : '';
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 120,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '查看',
      onClick(record) {
        emit('closeModal');
        childAction('history-look', record);
      },
    },
  ],
};
</script>

<style scoped>

</style>
