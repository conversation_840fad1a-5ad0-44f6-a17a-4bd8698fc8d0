package com.chinasie.orion.management.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.management.domain.dto.QuotationManageCustContactDTO;
import com.chinasie.orion.management.domain.entity.QuotationManageCustContact;
import com.chinasie.orion.management.domain.entity.QuotationManagement;
import com.chinasie.orion.management.domain.vo.QuotationManageCustContactVO;
import com.chinasie.orion.management.repository.QuotationManageCustContactMapper;
import com.chinasie.orion.management.service.QuotationManageCustContactService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <p>
 * QuotationManageCustContact 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-06 16:23:59
 */
@Service
@Slf4j
public class QuotationManageCustContactServiceImpl extends OrionBaseServiceImpl<QuotationManageCustContactMapper, QuotationManageCustContact> implements QuotationManageCustContactService {

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public QuotationManageCustContactVO detail(String id, String pageCode) {
        QuotationManageCustContact quotationManageCustContact = this.getById(id);
        QuotationManageCustContactVO result = BeanCopyUtils.convertTo(quotationManageCustContact, QuotationManageCustContactVO::new);
        setEveryName(Collections.singletonList(result));

        return result;
    }


    /**
     * 保存报检单客户的相关联系人
     *
     * @param contacts  联系人
     * @param quotation 报检单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveQuotationContacts(List<QuotationManageCustContact> contacts, QuotationManagement quotation) {
        final LambdaQueryWrapper<QuotationManageCustContact> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(QuotationManageCustContact::getQuotationId, quotation.getId());

        final List<QuotationManageCustContact> existsList = this.list(queryWrapper);

        // 默认所有existsList都做删除，如果根据id对比在contacts中存在，则做更新操作
        final List<String> removes = existsList.stream().map(QuotationManageCustContact::getId)
                .filter(id -> !contacts.stream().map(QuotationManageCustContact::getId).collect(Collectors.toList())
                        .contains(id)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(removes)) {
            this.removeBatchByIds(removes);
        }

        final List<QuotationManageCustContact> updateList = Lists.newArrayList();
        final List<QuotationManageCustContact> insertList = Lists.newArrayList();
        contacts.forEach(e -> {
            e.setQuotationId(quotation.getId());
            if (null != e.getId() && StringUtils.isNotBlank(e.getId())) {
                updateList.add(e);
            } else {
                insertList.add(e);
            }
        });
        if (!CollectionUtils.isEmpty(updateList)) {
            this.updateBatchById(updateList);
        }
        if (!CollectionUtils.isEmpty(insertList)) {
            this.saveBatch(insertList);
        }
    }

    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(List<String> ids) {
        this.removeBatchByIds(ids);
        return true;
    }

    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<QuotationManageCustContactVO> pages(Page<QuotationManageCustContactDTO> pageRequest) {

        LambdaQueryWrapperX<QuotationManageCustContact> condition = new LambdaQueryWrapperX<>(QuotationManageCustContact.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(QuotationManageCustContact::getCreateTime);

        PageResult<QuotationManageCustContact> page = this.getBaseMapper().selectPage(pageRequest, condition);

        Page<QuotationManageCustContactVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<QuotationManageCustContactVO> vos = BeanCopyUtils.convertListTo(page.getContent(), QuotationManageCustContactVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public void setEveryName(List<QuotationManageCustContactVO> vos) {

        vos.forEach(vo -> {
        });

    }

}
