<template>
  <BasicDrawer
    v-model:visible="father.visible"
    class="ui-2-0"
    :width="350"
    title="添加信息"
    :body-style="bodyStyle"
    :mask-closable="false"
  >
    <a-input-search
      v-model:value="before.keyword"
      placeholder="请输入内容"
      size="large"
      @search="onSearch"
    />
    <hr class="hr">
    <a-form
      ref="formRef"
      layout="vertical"
      :model="father.form"
      :rules="rules"
    >
      <a-form-item
        label="依赖关系"
        name="type"
      >
        <a-select
          v-model:value="father.form.type"
          placeholder="请选择依赖关系"
          size="large"
        >
          <a-select-option
            v-for="s in relationList"
            :key="s.id"
            :value="s.id"
          >
            {{ s.name }}
          </a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
    <!--    共搜索出 4 项结果-->
    <div class="content">
      <div class="sub-title1">
        当此任务开始时，关联任务必须结束。
      </div>
      <div class="sub-title2">
        共搜索出 {{ beforeList.length }} 项结果
      </div>
      <div
        v-loading="loadingBox"
        class="content-box"
      >
        <div
          v-for="s in beforeList"
          :key="s.id"
          class="plan-item"
          :class="[s.isActive ? 'plan-item-active' : '']"
          @click="s.isActive = !s.isActive"
        >
          <img
            class="plan-item-img"
            src="/images/1.png"
            alt=""
          >
          <div class="plan-item-content">
            <div class="plan-title">
              {{ s.name }}
            </div>
            <div class="plan-sub-title">
              <div>编号：{{ s.number }}</div>
              <div>负责人：{{ s.principalName }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="drawer-footer">
      <a-row :gutter="20">
        <a-col :span="12">
          <a-button
            size="large"
            block
            @click="handleClose"
          >
            取消
          </a-button>
        </a-col>
        <a-col :span="12">
          <a-button
            size="large"
            type="primary"
            block
            :loading="loading"
            @click="handleSave"
          >
            确认
          </a-button>
        </a-col>
      </a-row>
    </div>
  </BasicDrawer>
</template>

<script>
import {
  reactive, toRefs, ref, onMounted,
} from 'vue';
import {
  Row, Col, Drawer, Form, Input, Button, message, Select,
} from 'ant-design-vue';
import Api from '/@/api';
import {
  BasicDrawer,
} from 'lyra-component-vue3';
export default {
  name: 'CreateBaseline',
  components: {
    ARow: Row,
    ACol: Col,
    ASelect: Select,
    ASelectOption: Select.Option,
    AInputSearch: Input.Search,
    AButton: Button,
    AForm: Form,
    AFormItem: Form.Item,
    ADrawer: Drawer,
    BasicDrawer,
  },
  props: {
    data: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  emits: ['update:data', 'submit'],
  setup(props, { emit }) {
    const state = reactive({
      type: undefined,
      relationList: [],
      beforeList: [],
      bodyStyle: {
        overflow: 'auto',
        height: 'calc(100vh - 120px)',
      },
      before: {
        keyword: undefined,
        planId: props.data.form.toId,
      },
      father: props.data,
      loading: false,
      loadingBox: false,
      formRef: ref(),
      rules: {
        type: [
          {
            required: true,
            message: '请选择依赖关系',
            trigger: 'change',
          },
        ],
      },
    });

    function onSearch() {
      state.loadingBox = true;
      new Api('/pms')
        .fetch(state.before, 'before-after-to-plan/before/list', 'POST')
        .then((res) => {
          state.loadingBox = false;
          state.beforeList = res.map((s) => ({
            ...s,
            isActive: false,
          }));
        })
        .catch(() => {
          state.loadingBox = false;
        });
    }
    function handleSave() {
      state.formRef
        .validate()
        .then(() => {
          state.father.form.fromIdList = state.beforeList
            .filter((s) => s.isActive)
            .map((s) => s.id);
          if (state.father.form.fromIdList.length === 0) {
            return message.warning('必须选择一个任务');
          }
          state.loading = true;
          const love = {
            className: 'Plan',
            moduleName: '项目管理-计划管理-项目计划-前置计划', // 模块名称
            type: 'SAVE', // 操作类型
            remark: `新增了【${state.father.form?.id}】`,
          };
          new Api('/pms', love)
            .fetch(state.father.form, 'before-after-to-plan', 'POST')
            .then(() => {
              state.loading = false;
              message.success('操作成功');
              state.father.visible = false;
              emit('submit');
            })
            .catch(() => {
              state.loading = false;
            });
        })
        .catch(() => {
          message.warning('请检查必填项');
        });
    }
    function handleClose() {
      state.father.visible = false;
    }

    function getRelationList() {
      new Api('/pms').fetch('', 'before-after-to-plan/type/relation/list', 'GET').then((res) => {
        state.relationList = res;
      });
    }
    onMounted(() => {
      getRelationList();
    });

    return {
      ...toRefs(state),
      handleSave,
      handleClose,
      onSearch,
    };
  },
};
</script>

<style lang="less" scoped>
  .drawer-footer {
    position: absolute;
    bottom: 10px;
    width: 88%;
  }
  hr {
    margin: 20px 0;
  }
  .content {
    .sub-title1 {
      font-size: 14px;
    }
    .sub-title2 {
      font-size: 12px;
      margin: 10px 0;
    }
    .content-box {
      height: calc(100vh - 410px);
      overflow: auto;
      //border: 1px solid #00a073;
      .plan-item-active {
        background-color: #eef1fc;
      }
      .plan-item {
        height: 80px;
        border-bottom: 1px dashed #0a6cd5;
        position: relative;
        cursor: pointer;
        &:hover {
          background-color: #f7f8fd;
        }
        .plan-item-img {
          position: absolute;
          left: 10px;
          top: 10px;
          width: 60px;
          height: 60px;
        }
        .plan-item-content {
          margin-left: 80px;
          padding-top: 10px;
          .plan-title {
            font-size: 14px;
          }
          .plan-sub-title {
            font-size: 12px;
            color: #666666;
          }
        }
      }
    }
  }
</style>
