<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
    class="pdmBasicTable"
  >
    <template #toolbarLeft>
      <BasicButton
        v-if="isPower('PMS_XMXQ_container_06_02_button_01',powerData)"
        type="primary"
        icon="add"
        @click="addNode"
      >
        新增
      </BasicButton>
    </template>
    <template #action="{record}">
      <BasicTableAction
        :actions="actionsBtn"
        :record="record"
      />
    </template>
  </OrionTable>
  <!-- 查看详情弹窗 -->
  <checkDetails
    v-if="pageType==='page'"
    :data="nodeData"
  />
  <!-- 新建/编辑抽屉 -->
  <addProjectModal
    @register="registerDrawer"
    @success="getFormData"
  />
</template>
<script lang="ts">
import {
  computed, defineComponent, h, inject, onMounted, reactive, ref, toRefs,
} from 'vue';
import { message } from 'ant-design-vue';
import { formatterTime } from '/@/views/pms/projectLaborer/utils/time';
import addProjectModal from './StakeholderModal/addProjectModal.vue';
import checkDetails from './StakeholderModal/checkmodal.vue';
import {
  BasicButton, BasicTableAction, isPower, ITableActionItem, OrionTable, useDrawer,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
import { deletStakeHolderApi, getStakeHolderApi } from '/@/views/pms/projectLaborer/api/projectList';
export default defineComponent({
  name: 'Stakeholder',
  components: {
    BasicButton,
    BasicTableAction,
    OrionTable,
    checkDetails,
    /* 新建项目抽屉 */
    addProjectModal,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
    pageType: {
      type: String,
      default: 'page',
    },
  },
  setup(props) {
    const tableRef = ref(null);
    const powerData = inject('powerData');
    const [registerDrawer, { openDrawer }] = useDrawer();

    const state = reactive({
      powerData: [],
      /* 编辑send */
      editdataSource: {},
      addNodeModalData: {},
      showVisible: false,
      /* 简易弹窗提醒消息 */
      message: '',
      nodeData: {},
      searchData: {},
      params: {},
    });
    state.powerData = inject('powerData');

    /* 简易弹窗的确定cb */
    const confirm = () => {

    };

    const checkData2 = (data) => {
      state.nodeData = {
        ...[JSON.parse(JSON.stringify(data))],
      };
    };

    /* 新建项目 */
    const addNode = () => {
      openDrawer(true, {
        formType: 'add',
      });
      // state.addNodeModalData = {
      //   formType: 'add',
      // };
    };

    function getFormData() {
      tableRef.value?.reload();
    }

    // 生成 queryCondition
    function getListParams(params) {
      if (params.searchConditions) {
        return {
          ...params,
          queryCondition: params.searchConditions.map((item) => ({
            column: item?.[0]?.field,
            type: 'like',
            link: 'or',
            value: item?.[0]?.values?.[0],
          })),
        };
      }
      return params;
    }

    const tableOptions = {
      deleteToolButton: isPower('PMS_XMXQ_container_06_02_button_02', powerData) ? 'add|enable|disable' : 'add|enable|disable|delete',
      rowSelection: {},
      isFilter2: true,
      filterConfigName: 'PMS_PROJECTMANAGE_RESOURCEMANAGE_STAKEHOLDERMANAGE',
      api(params) {
        return getStakeHolderApi(getListParams({
          ...params,
          query: {
            projectId: props.id,
          },
        }));
      },
      batchDeleteApi({ ids }) {
        return deletStakeHolderApi(ids).then(() => {
          message.success('移除成功');
          getFormData();
        });
      },
      columns: [
        {
          title: '名称',
          dataIndex: 'name',
          minWidth: 250,
          customRender({ record, text }) {
            if (isPower('PMS_XMXQ_container_06_02_button_05', powerData)) {
              return h('span', {
                class: 'action-btn',
                onClick: () => checkData2(record),
              }, text);
            }
            return text;
          },
        },
        {
          title: '联系方式',
          dataIndex: 'contactTypeName',
          width: '80px',
        },
        {
          title: '联系信息',
          dataIndex: 'contactInfo',
          width: '200px',
        },
        {
          title: '地址',
          dataIndex: 'address',
          width: '300px',
        },
        {
          title: '备注',
          dataIndex: 'remark',
          width: '200px',
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 150,
          fixed: 'right',
          slots: { customRender: 'action' },
        },
      ],
    };

    const actionsBtn:ITableActionItem<any>[] = [
      {
        text: '编辑',
        isShow: (record:Record<string, any>) => isPower('PMS_XMXQ_container_06_02_button_03', powerData),
        onClick(record:any) {
          openDrawer(true, {
            ...record,
            type: 'edit',
          });

          // state.editdataSource = { ...record };
          // state.addNodeModalData = { formType: 'edit' };
        },
      },
      {
        text: '删除',
        isShow: (record:Record<string, any>) => isPower('PMS_XMXQ_container_06_02_button_04', powerData),
        modal(record:any) {
          return deletStakeHolderApi([record.id]).then(() => {
            message.success('删除成功');
            getFormData();
          });
        },
      },
    ];

    return {
      ...toRefs(state),
      tableRef,
      isPower,
      formatterTime,
      confirm,
      addNode,
      dayjs,
      actionsBtn,
      tableOptions,
      getFormData,
      registerDrawer,
      openDrawer,
    };
  },
});
</script>
<style lang="less" scoped>
</style>
