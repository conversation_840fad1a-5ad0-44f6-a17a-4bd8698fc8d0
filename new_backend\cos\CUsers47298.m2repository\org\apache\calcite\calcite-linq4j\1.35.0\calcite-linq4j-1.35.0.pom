<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.apache.calcite</groupId>
  <artifactId>calcite-linq4j</artifactId>
  <version>1.35.0</version>
  <name>Calcite Linq4j</name>
  <description>Calcite APIs for LINQ (Language-Integrated Query) in Java</description>
  <url>https://calcite.apache.org</url>
  <inceptionYear>2012</inceptionYear>
  <licenses>
    <license>
      <name>The Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
      <comments>A business-friendly OSS license</comments>
    </license>
  </licenses>
  <mailingLists>
    <mailingList>
      <name>Apache Calcite developers list</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>https://lists.apache.org/list.html?<EMAIL></archive>
    </mailingList>
  </mailingLists>
  <scm>
    <connection>scm:git:https://gitbox.apache.org/repos/asf/calcite.git</connection>
    <developerConnection>scm:git:https://gitbox.apache.org/repos/asf/calcite.git</developerConnection>
    <url>https://github.com/apache/calcite</url>
  </scm>
  <issueManagement>
    <system>Jira</system>
    <url>https://issues.apache.org/jira/browse/CALCITE</url>
  </issueManagement>
  <dependencies>
    <dependency>
      <groupId>org.apiguardian</groupId>
      <artifactId>apiguardian-api</artifactId>
      <version>1.1.2</version>
    </dependency>
    <dependency>
      <groupId>org.checkerframework</groupId>
      <artifactId>checker-qual</artifactId>
      <version>3.10.0</version>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>19.0</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <artifactId>annotations</artifactId>
          <groupId>org.jetbrains</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.calcite.avatica</groupId>
      <artifactId>avatica-core</artifactId>
      <version>1.23.0</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <artifactId>annotations</artifactId>
          <groupId>org.jetbrains</groupId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>
</project>
