package com.chinasie.orion.domain.vo.review;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ReviewMember VO对象
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:03
 */
@ApiModel(value = "ReviewMemberVO对象", description = "评审组成员")
@Data
public class ReviewMemberVO extends ObjectVO implements Serializable {

    /**
     * 级别
     */
    @ApiModelProperty(value = "级别")
    private Integer scale;


    /**
     * 成员
     */
    @ApiModelProperty(value = "成员")
    private String userId;
    @ApiModelProperty(value = "姓名")
    private String userName;


    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private Integer type;
    @ApiModelProperty(value = "类型名称")
    private String typeName;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    private String mainTableId;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String deptName;
}
