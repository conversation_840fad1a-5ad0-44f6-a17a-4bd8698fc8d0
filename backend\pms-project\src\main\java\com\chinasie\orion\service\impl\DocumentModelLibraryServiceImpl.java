package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.bo.LyraFileBO;
import com.chinasie.orion.constant.DocumentModelLibraryStatusEnum;
import com.chinasie.orion.domain.dto.DocumentModelLibraryDTO;
import com.chinasie.orion.domain.entity.DocumentDecomposition;
import com.chinasie.orion.domain.entity.DocumentModelLibrary;
import com.chinasie.orion.domain.entity.TaskDecomposition;
import com.chinasie.orion.domain.entity.TaskDecompositionPrePost;
import com.chinasie.orion.domain.vo.DocumentModelLibraryVO;
import com.chinasie.orion.domain.vo.SimpleVo;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.number.api.domain.GenerateNumberRequest;
import com.chinasie.orion.number.api.sdk.NumberApiService;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.DocumentModelLibraryMapper;
import com.chinasie.orion.sdk.domain.vo.business.ClassVO;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.helper.RevisionOrderCalculationHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.DocumentDecompositionService;
import com.chinasie.orion.service.DocumentModelLibraryService;
import com.chinasie.orion.service.TaskDecompositionPrePostService;
import com.chinasie.orion.service.TaskDecompositionService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;

import java.lang.String;
import java.util.*;
import java.util.stream.Collectors;

import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * DocumentModelLibrary 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-01 16:35:39
 */
@Service
@Slf4j
public class DocumentModelLibraryServiceImpl extends  OrionBaseServiceImpl<DocumentModelLibraryMapper, DocumentModelLibrary>   implements DocumentModelLibraryService {

    @Autowired
    private LyraFileBO fileBo;

    @Autowired
    private NumberApiService numberApiService;

    @Autowired
    private ClassRedisHelper classRedisHelper;

    @Autowired
    private DocumentDecompositionService documentDecompositionService;

    @Autowired
    private TaskDecompositionService taskDecompositionService;

    @Autowired
    private TaskDecompositionPrePostService taskDecompositionPrePostService;


    @Autowired
    private RevisionOrderCalculationHelper revisionOrderCalculationHelper;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public DocumentModelLibraryVO detail(String id, String pageCode) throws Exception {
        DocumentModelLibrary documentModelLibrary = this.getById(id);
        DocumentModelLibraryVO result = BeanCopyUtils.convertTo(documentModelLibrary, DocumentModelLibraryVO::new);
//        setEveryName(Collections.singletonList(result));

        List<FileVO> fileDtoList = fileBo.getFilesByDataId(id);
        result.setFileDtoList(fileDtoList);

        String useScope = documentModelLibrary.getUseScope();
        if (StrUtil.isNotBlank(useScope)) {

            List<ClassVO> classByNames = classRedisHelper.getClassByNames(Arrays.asList(useScope.split(",")));
            result.setUseScopeName(classByNames.stream().map(ClassVO::getRemark).collect(Collectors.joining(",")));
            result.setUseScopeList(classByNames.stream().map(m -> {
                SimpleVo simpleVo = new SimpleVo();
                simpleVo.setId(m.getClassName());
                simpleVo.setName(m.getRemark());
                return simpleVo;
            }).collect(Collectors.toList()));
        }
        return result;
    }

    /**
     *  新增
     *
     * * @param documentModelLibraryDTO
     */
    @Override
    public  String create(DocumentModelLibraryDTO documentModelLibraryDTO) throws Exception {
        DocumentModelLibrary documentModelLibrary = BeanCopyUtils.convertTo(documentModelLibraryDTO,DocumentModelLibrary::new);
        documentModelLibrary.setInitialRevId("A");
        documentModelLibrary.setRevId("A");
        documentModelLibrary.setRevOrder(1);
        documentModelLibrary.setRevKey(UUID.randomUUID().toString());
        documentModelLibrary.setIsMainRev(true);

        GenerateNumberRequest generateNumberRequest = new GenerateNumberRequest();
        generateNumberRequest.setClazzName(DocumentModelLibrary.class.getSimpleName());
        generateNumberRequest.setEffectFlag(true);
        String generate = numberApiService.generate(generateNumberRequest);
        documentModelLibrary.setNumber(generate);

        this.save(documentModelLibrary);

        String id = documentModelLibrary.getId();
        List<FileVO> fileDtoList = documentModelLibraryDTO.getFileDtoList();
        if (CollectionUtil.isNotEmpty(fileDtoList)) {
            fileDtoList.forEach(f -> f.setDataId(id));
            List<FileDTO> fileDTOS = BeanCopyUtils.convertListTo(fileDtoList, FileDTO::new);
            fileBo.addBatch(fileDTOS);
        }
        return id;
    }

    /**
     *  编辑
     *
     * * @param documentModelLibraryDTO
     */
    @Override
    public Boolean edit(DocumentModelLibraryDTO documentModelLibraryDTO) throws Exception {
        DocumentModelLibrary documentModelLibrary = BeanCopyUtils.convertTo(documentModelLibraryDTO,DocumentModelLibrary::new);

        this.updateById(documentModelLibrary);
        String id = documentModelLibrary.getId();
        List<FileVO> newFileDtoList = documentModelLibraryDTO.getFileDtoList();
        handleFile(id, newFileDtoList);
        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        long count = this.count(new LambdaQueryWrapperX<>(DocumentModelLibrary.class)
                .select(DocumentModelLibrary::getId)
                .in(DocumentModelLibrary::getId, ids)
                .eq(DocumentModelLibrary::getStatus, DocumentModelLibraryStatusEnum.EFFECT.getStatus()));
        if (count > 0){
            throw new PMSException(PMSErrorCode.KMS_EFFECT_DATA);
        }
        this.removeBatchByIds(ids);

        List<TaskDecomposition> taskDecompositionList = taskDecompositionService.list(new LambdaQueryWrapperX<>(TaskDecomposition.class)
                .select(TaskDecomposition::getId)
                .in(TaskDecomposition::getMainTableId, ids));

        if (CollectionUtil.isNotEmpty(taskDecompositionList)){
            List<String> taskDecompositionIds = taskDecompositionList.stream().map(TaskDecomposition::getId).collect(Collectors.toList());
            taskDecompositionPrePostService.remove(new LambdaQueryWrapperX<>(TaskDecompositionPrePost.class)
                    .in(TaskDecompositionPrePost::getTaskDecompositionId, taskDecompositionIds));
            taskDecompositionService.remove(taskDecompositionIds);
        }

        documentDecompositionService.remove(new LambdaQueryWrapperX<>(DocumentDecomposition.class).in(DocumentDecomposition::getMainTableId,ids));
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<DocumentModelLibraryVO> pages(String mainTableId, Page<DocumentModelLibraryDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<DocumentModelLibrary> condition = new LambdaQueryWrapperX<>( DocumentModelLibrary. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(DocumentModelLibrary::getCreateTime);

        condition.eq(DocumentModelLibrary::getMainTableId, mainTableId).eq(DocumentModelLibrary::getIsMainRev,true);

        Page<DocumentModelLibrary> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), DocumentModelLibrary::new));

        PageResult<DocumentModelLibrary> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<DocumentModelLibraryVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<DocumentModelLibraryVO> vos = BeanCopyUtils.convertListTo(page.getContent(), DocumentModelLibraryVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public void  setEveryName(List<DocumentModelLibraryVO> vos)throws Exception {

//        List<String> classNameList = vos.stream().map(m -> Arrays.asList(m.getUseScope().split(","))
//        ).flatMap(Collection::stream).collect(Collectors.toList());
//
//        vos.forEach(vo->{
//
//        });

    }

    @Override
    public Boolean upVersion(String id) throws Exception {
        DocumentModelLibrary documentModelLibrary = this.getById(id);
        documentModelLibrary.setId("");
        documentModelLibrary.setRevOrder(documentModelLibrary.getRevOrder() +1);
        documentModelLibrary.setRevId(revisionOrderCalculationHelper.AToN(documentModelLibrary.getRevId()));
        documentModelLibrary.setIsMainRev(true);
        this.save(documentModelLibrary);

        this.update(new LambdaUpdateWrapper<>(DocumentModelLibrary.class)
        .set(DocumentModelLibrary::getIsMainRev, "")
        .eq(DocumentModelLibrary::getRevKey, documentModelLibrary.getRevKey())
        .ne(DocumentModelLibrary::getId, documentModelLibrary.getId()));
         return Boolean.TRUE;
    }

    @Override
    public List<DocumentModelLibraryVO> getVersionRecords(String id) throws Exception {
        DocumentModelLibrary documentModelLibrary = this.getById(id);
        List<DocumentModelLibrary> list = this.list(new LambdaQueryWrapperX<>(DocumentModelLibrary.class).eq(DocumentModelLibrary::getRevKey, documentModelLibrary.getRevKey()));
        if (CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }
        return BeanCopyUtils.convertListTo(list, DocumentModelLibraryVO::new).stream()
                .sorted(Comparator.comparing(DocumentModelLibraryVO::getRevOrder)
                .reversed()).collect(Collectors.toList());
    }

    @Override
    public Boolean enable(List<String> ids) throws Exception {
        LambdaUpdateWrapper<DocumentModelLibrary> updateWrapper = new LambdaUpdateWrapper();
        updateWrapper.set(DocumentModelLibrary::getStatus, DocumentModelLibraryStatusEnum.EFFECT.getStatus());
        updateWrapper.in(DocumentModelLibrary::getId, ids);
        return this.update(updateWrapper);
    }

    @Override
    public Boolean disEnable(List<String> ids) throws Exception{
        LambdaUpdateWrapper<DocumentModelLibrary> updateWrapper = new LambdaUpdateWrapper();
        updateWrapper.set(DocumentModelLibrary::getStatus, DocumentModelLibraryStatusEnum.UN_EFFECT.getStatus());
        updateWrapper.in(DocumentModelLibrary::getId, ids);
        return this.update(updateWrapper);
    }

    /**
     * 新增删除文件
     *
     * @param id
     * @param newFileDtoList
     * @throws Exception
     */
    @Override
    public void handleFile(String id, List<FileVO> newFileDtoList) throws Exception {
        List<FileVO> fileDtoList = fileBo.getFilesByDataId(id);
        List<String> fileIdList = newFileDtoList.stream().map(FileVO::getId).filter(StrUtil::isNotBlank).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(fileDtoList)) {
            List<String> deleteIdList = fileDtoList.stream().map(FileVO::getId).filter(fId -> !fileIdList.contains(fId)).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(deleteIdList)) {
                fileBo.deleteFileByIds(deleteIdList);
            }
        }

        List<FileVO> saveFileList = newFileDtoList.stream().filter(f -> StrUtil.isBlank(f.getId())).peek(p -> p.setDataId(id)).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(saveFileList)) {
            List<FileDTO> fileDTOS = BeanCopyUtils.convertListTo(saveFileList, FileDTO::new);
            fileBo.addBatch(fileDTOS);
        }
    }

    @Override
    public Page<DocumentModelLibraryVO> getModelPages(Page<DocumentModelLibraryDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<DocumentModelLibrary> condition = new LambdaQueryWrapperX<>( DocumentModelLibrary. class);

        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.eq(DocumentModelLibrary::getStatus,1);
        condition.orderByDesc(DocumentModelLibrary::getCreateTime);


        Page<DocumentModelLibrary> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), DocumentModelLibrary::new));

        PageResult<DocumentModelLibrary> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<DocumentModelLibraryVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<DocumentModelLibraryVO> vos = BeanCopyUtils.convertListTo(page.getContent(), DocumentModelLibraryVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

}
