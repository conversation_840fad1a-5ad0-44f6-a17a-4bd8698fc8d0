package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;

/**
 * IncomeAccountConfirm Entity对象
 *
 * <AUTHOR>
 * @since 2024-12-18 21:16:26
 */
@TableName(value = "pmsx_income_account_confirm")
@ApiModel(value = "IncomeAccountConfirmEntity对象", description = "收入记账明细确认表")
@Data

public class IncomeAccountConfirm extends  ObjectEntity  implements Serializable{

    /**
     * 凭证类型
     */
    @ApiModelProperty(value = "凭证类型")
    @TableField(value = "voucher_type")
    private String voucherType;

    /**
     * 是否调账凭证
     */
    @ApiModelProperty(value = "是否调账凭证")
    @TableField(value = "adj_account_voucher")
    private String adjAccountVoucher;

    /**
     * 确认状态
     */
    @ApiModelProperty(value = "确认状态")
    @TableField(value = "confirm_status")
    private String confirmStatus;

    /**
     * 公司代码
     */
    @ApiModelProperty(value = "公司代码")
    @TableField(value = "company_code")
    private String companyCode;

    /**
     * 科目
     */
    @ApiModelProperty(value = "科目")
    @TableField(value = "subject")
    private String subject;

    /**
     * 分配编码
     */
    @ApiModelProperty(value = "分配编码")
    @TableField(value = "distributive_code")
    private String distributiveCode;

    /**
     * 过账期间
     */
    @ApiModelProperty(value = "过账期间")
    @TableField(value = "posting_date")
    private Date postingDate;

    /**
     * 凭证编号
     */
    @ApiModelProperty(value = "凭证编号")
    @TableField(value = "voucher_num")
    private String voucherNum;

    /**
     * 凭证日期
     */
    @ApiModelProperty(value = "凭证日期")
    @TableField(value = "voucher_date")
    private Date voucherDate;

    /**
     * 过账期间
     */
    @ApiModelProperty(value = "过账期间")
    @TableField(value = "posting_period")
    private String postingPeriod;

    /**
     * 本币金额
     */
    @ApiModelProperty(value = "本币金额")
    @TableField(value = "local_currency_amt")
    private BigDecimal localCurrencyAmt;

    /**
     * 利润中心
     */
    @ApiModelProperty(value = "利润中心")
    @TableField(value = "profit_center")
    private String profitCenter;

    /**
     * 成本中心
     */
    @ApiModelProperty(value = "成本中心")
    @TableField(value = "cost_center")
    private String costCenter;

    /**
     * 文本
     */
    @ApiModelProperty(value = "文本")
    @TableField(value = "con_text")
    private String conText;

    /**
     * 收入计划编号
     */
    @ApiModelProperty(value = "收入计划编号")
    @TableField(value = "income_plan_num")
    private String incomePlanNum;

    /**
     * 是否修改凭证类型
     */
    @ApiModelProperty(value = "是否修改凭证类型")
    @TableField(value = "is_update")
    private String isUpdate;

    /**
     * 是否标红
     */
    @ApiModelProperty(value = "是否标红")
    @TableField(value = "is_red")
    private String isRed;

}
