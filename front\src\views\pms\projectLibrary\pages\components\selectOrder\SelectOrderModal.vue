<script setup lang="ts">
import { BasicModal, useModalInner } from 'lyra-component-vue3';
import { ref, Ref } from 'vue';
import { message } from 'ant-design-vue';
import SelectOrderTable from './SelectOrderTable.vue';

const emits = defineEmits<{
  (e:'modalOk', data:any[], index:number):void
}>();

const [register, { setModalProps, closeModal }] = useModalInner((openProps:Record<string, any>) => {
  index.value = openProps.index;
  setModalProps({
    title: '选择物资',
  });
  visibleTable.value = true;
});

const visibleTable:Ref<boolean> = ref(false);
const tableRef:Ref = ref();
const index:Ref<number> = ref(0);

function visibleChange(visible: boolean) {
  !visible && (visibleTable.value = visible);
}

async function onOk() {
  const data = tableRef.value.getData();
  if (data.length) {
    emits('modalOk', data, index.value);
    closeModal();
  } else {
    message.error('请选择数据');
  }
}
</script>

<template>
  <BasicModal
    v-bind="$attrs"
    width="1000px"
    :height="400"
    :onVisibleChange="visibleChange"
    :showFooter="true"
    @register="register"
    @ok="onOk"
  >
    <SelectOrderTable
      v-if="visibleTable"
      ref="tableRef"
    />
  </BasicModal>
</template>

<style scoped lang="less">

</style>
