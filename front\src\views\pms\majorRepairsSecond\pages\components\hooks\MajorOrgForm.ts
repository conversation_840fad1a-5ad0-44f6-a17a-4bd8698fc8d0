import {
  openModal,
} from 'lyra-component-vue3';
import { Ref, ref, h } from 'vue';

// 作业下转
export function openDataMajorOrgForm(component: any, object: {
  record?: Record<string, any>,
  detailsData?: any,
  type?: any,
  radioType: any,
  brick: string,
}): void {
  const drawerRef: Ref = ref();
  openModal({
    title: '作业信息',
    width: '80%',
    height: 650,
    content() {
      return h(component, {
        ref: drawerRef,
        record: object?.record,
        detailsData: object.detailsData,
        jobImplDownEnum: object?.type,
        radioType: object?.radioType,
        brick: object?.brick,
      });
    },
    footer: null,
  });
}
