package com.chinasie.orion.controller;


import com.chinasie.orion.domain.dto.ProductEstimateMaterialDTO;
import com.chinasie.orion.domain.dto.ProjectIncomeUpdateBatchDTO;
import com.chinasie.orion.domain.dto.ProjectIncomeDTO;
import com.chinasie.orion.domain.vo.ProductEstimateMaterialVO;
import com.chinasie.orion.domain.vo.ProjectIncomeVO;
import com.chinasie.orion.pas.api.domain.vo.IncomeContractProductInfoVO;
import com.chinasie.orion.service.ProjectIncomeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.starter.annotation.LogRecord;

import javax.servlet.http.HttpServletResponse;
import java.lang.Exception;
import java.lang.String;
import java.util.List;

/**
 * <p>
 * ProjectIncome 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-15 09:24:02
 */
@RestController
@RequestMapping("/projectIncome")
@Api(tags = "收益管理")
public class ProjectIncomeController {

    @Autowired
    private ProjectIncomeService projectIncomeService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查看了数据", type = "收益管理", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectIncomeVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        ProjectIncomeVO rsp = projectIncomeService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 同步收益策划数据
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "同步收益策划数据")
    @RequestMapping(value = "/sync", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】同步收益策划数据", type = "收益管理", subType = "同步收益策划数据", bizNo = "{{#projectId}}")
    public ResponseDTO<Boolean> syncByProjectApprovalIncome(@RequestParam("projectId") String projectId) throws Exception {
        Boolean rsp =  projectIncomeService.syncByProjectApprovalIncome(projectId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectIncomeDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#projectIncomeDTO.name}}】", type = "收益管理", subType = "编辑", bizNo = "{{#projectIncomeDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  ProjectIncomeDTO projectIncomeDTO) throws Exception {
        Boolean rsp = projectIncomeService.edit(projectIncomeDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 销售是否结束变更
     * @param projectIncomeUpdateBatchDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "销售是否结束变更")
    @RequestMapping(value = "/updateSaleOver", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】销售是否结束变更", type = "收益管理", subType = "销售是否结束变更", bizNo = "{{#projectIncomeUpdateBatchDTO.ids.toString()}}")
    public ResponseDTO<Boolean> updateSaleOver(@RequestBody @Validated ProjectIncomeUpdateBatchDTO projectIncomeUpdateBatchDTO) throws Exception {
        Boolean rsp = projectIncomeService.updateSaleOver(projectIncomeUpdateBatchDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 现预期总产出变更
     * @param projectIncomeUpdateBatchDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "现预期总产出变更")
    @RequestMapping(value = "/updateExpectedOutcomes", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】现预期总产出变更", type = "收益管理", subType = "现预期总产出变更", bizNo = "{{#projectIncomeUpdateBatchDTO.ids.toString()}}")
    public ResponseDTO<Boolean> updateExpectedOutcomes(@RequestBody @Validated ProjectIncomeUpdateBatchDTO projectIncomeUpdateBatchDTO) throws Exception {
        Boolean rsp = projectIncomeService.updateExpectedOutcomes(projectIncomeUpdateBatchDTO);
        return new ResponseDTO<>(rsp);
    }

//    /**
//     * 删除
//     *
//     * @return
//     * @throws Exception
//     */
//    @ApiOperation(value = "删除")
//    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
//    @Transactional(rollbackFor = Exception.class)
//    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "收益管理", subType = "删除", bizNo = "{{#id}}")
//    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
//        Boolean rsp = projectIncomeService.remove(Collections.singletonList(id));
//        return new ResponseDTO(rsp);
//    }
//
//
//    /**
//     * 删除（批量）
//     *
//     * @return
//     * @throws Exception
//     */
//    @ApiOperation(value = "删除（批量）")
//    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
//    @Transactional(rollbackFor = Exception.class)
//    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "收益管理", subType = "批量删除", bizNo = "{{#ids.toString()}}")
//    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
//        Boolean rsp = projectIncomeService.remove(ids);
//        return new ResponseDTO(rsp);
//    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "收益管理", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ProjectIncomeVO>> pages(@RequestBody Page<ProjectIncomeDTO> pageRequest) throws Exception {
        Page<ProjectIncomeVO> rsp =  projectIncomeService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("收益管理导出（Excel）")
    @PostMapping(value = "/export/excel")
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "收益管理", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody Page<ProjectIncomeDTO> pageRequest, HttpServletResponse response) throws Exception {
        projectIncomeService.exportByExcel(pageRequest, response);
    }

    @ApiOperation("获取收益条目")
    @GetMapping(value = "/contract/list")
    @LogRecord(success = "【{USER{#logUserId}}】获取收益条目", type = "收益管理", subType = "获取收益条目", bizNo = "{{}#id}")
    public ResponseDTO<List<IncomeContractProductInfoVO>> getContractInfo(@RequestParam("id") String id) throws Exception {
        List<IncomeContractProductInfoVO> rsp =  projectIncomeService.getContractInfo( id);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("获取产品")
    @GetMapping(value = "/getProductEstimatePage")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】获取产品", type = "收益管理", subType = "获取收益条目", bizNo = "{{}#id}")
    public ResponseDTO<Page<ProductEstimateMaterialVO>> getContractInfo(Page<ProductEstimateMaterialDTO> pageRequest) throws Exception {
        Page<ProductEstimateMaterialVO> rsp =  projectIncomeService.getProductEstimatePage(pageRequest);
        return new ResponseDTO<>(rsp);
    }
}
