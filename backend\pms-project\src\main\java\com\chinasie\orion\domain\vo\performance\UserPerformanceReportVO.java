package com.chinasie.orion.domain.vo.performance;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: lsy
 * @date: 2024/5/22
 * @description:
 */
@Data
@ExcelIgnoreUnannotated
public class UserPerformanceReportVO implements Serializable {


    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    @ExcelProperty(value = "序号", index = 0)
    private Integer sort;

    /**
     * 人员id
     */
    @ApiModelProperty(value = "人员id")
    private String id;

    /**
     * 人员姓名
     */
    @ApiModelProperty(value = "人员姓名")
    @ExcelProperty(value = "人员姓名", index = 1)
    private String name;

    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    @ExcelProperty(value = "工号", index = 2)
    private String code;

    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    @ExcelProperty(value = "所属部门", index = 3)
    private String orgName;

    /**
     * 所属处室
     */
    @ApiModelProperty(value = "所属处室")
    @ExcelProperty(value = "所属处室", index = 4)
    private String deptName;

    /**
     * 参与项目数
     */
    @ApiModelProperty(value = "参与项目数")
    @ExcelProperty(value = "参与项目数", index = 5)
    private int projectNum = 0;

    /**
     * 负责计划数
     */
    @ApiModelProperty(value = "负责计划数")
    @ExcelProperty(value = "负责计划数", index = 6)
    private int resTaskNum = 0;

    /**
     * 已完成计划数
     */
    @ApiModelProperty(value = "已完成计划数")
    @ExcelProperty(value = "已完成计划数", index = 7)
    private long completeTaskNum = 0;

    /**
     * 逾期计划数
     */
    @ApiModelProperty(value = "逾期计划数")
    @ExcelProperty(value = "逾期计划数", index = 8)
    private long overdueTaskNum = 0;

    /**
     * 变更计划数
     */
    @ApiModelProperty(value = "变更计划数")
    @ExcelProperty(value = "变更计划数", index = 9)
    private int changeTaskNum = 0;

    /**
     * 交付物数量
     */
    @ApiModelProperty(value = "交付物数量")
    @ExcelProperty(value = "交付物数量", index = 10)
    private long deliverNum = 0;

    /**
     * 计划准时完成率
     */
    @ApiModelProperty(value = "计划准时完成率")
    @ExcelProperty(value = "计划准时完成率", index = 11)
    private String taskOnTimeCompleteRate;

    /**
     * 计划反馈次数
     */
    @ApiModelProperty(value = "计划反馈次数")
    @ExcelProperty(value = "计划反馈次数", index = 12)
    private long taskFeedbackNum = 0;

    /**
     * 计划工期
     */
    @ApiModelProperty(value = "计划工期")
    @ExcelProperty(value = "计划工期", index = 13)
    private int durationDays;

    /**
     * 实际工期
     */
    @ApiModelProperty(value = "实际工期")
    @ExcelProperty(value = "实际工期", index = 14)
    private int actualDurationDays;

    /**
     * 已报工工时（天）
     */
    @ApiModelProperty(value = "已报工工时（天）")
    @ExcelProperty(value = "已报工工时（天）", index = 15)
    private double manHour = 0;

    /**
     * 报工及时率
     */
    @ApiModelProperty(value = "报工及时率")
    @ExcelProperty(value = "报工及时率", index = 16)
    private String manHourRate;






}
