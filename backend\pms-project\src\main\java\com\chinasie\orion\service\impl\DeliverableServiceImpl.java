package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinasie.orion.api.code.domain.vo.CodeSegmentVO;
import com.chinasie.orion.bo.CodeBo;
import com.chinasie.orion.bo.DocumentBo;
import com.chinasie.orion.bo.UserBo;
import com.chinasie.orion.constant.ClassNameConstant;
import com.chinasie.orion.constant.DocumentClassNameConstant;
import com.chinasie.orion.constant.ProjectStatusEnum;
import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.dto.document.DocumentDTO;
import com.chinasie.orion.domain.dto.pas.EcrDTO;
import com.chinasie.orion.domain.entity.DeliverGoalsToDeliverable;
import com.chinasie.orion.domain.entity.Deliverable;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.vo.AnalysisVO;
import com.chinasie.orion.domain.vo.DeliverPage;
import com.chinasie.orion.domain.vo.DeliverableVo;
import com.chinasie.orion.domain.vo.performance.DeliverablePerformanceVO;
import com.chinasie.orion.domain.vo.projectOverview.ProjectDeliverCount;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.PasFeignService;
import com.chinasie.orion.feign.PlanFeignService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.RevisionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.DeliverableRepository;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.service.DeliverGoalsToDeliverableService;
import com.chinasie.orion.service.DeliverableService;
import com.chinasie.orion.service.ProjectSchemeService;
import com.chinasie.orion.service.ProjectService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.text.DecimalFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/23/19:08
 * @description:
 */
@Service
public class DeliverableServiceImpl extends OrionBaseServiceImpl<DeliverableRepository, Deliverable> implements DeliverableService {

    @Resource
    private UserBo userBo;
    @Lazy
    @Resource
    private ProjectSchemeService projectSchemeService;
    @Resource
    private ProjectService projectService;
    @Resource
    private DocumentBo documentBo;
    @Autowired
    private CodeBo codeBo;

    @Autowired
    private PasFeignService pasFeignService;

    @Autowired
    private DeliverGoalsToDeliverableService deliverGoalsToDeliverableService;

    @Autowired
    private PlanFeignService planFeignService;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Override
    public List<DeliverableVo> listByPlanId(String planId, DeliverableQueryDTO deliverableQueryDTO) throws Exception {


        if (StrUtil.isBlank(planId)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "参数异常");
        }
        ProjectScheme projectScheme = projectSchemeService.getById(planId);

        LambdaQueryWrapperX<Deliverable> deliverableLambdaQueryWrapper = new LambdaQueryWrapperX<>(Deliverable.class);
        deliverableLambdaQueryWrapper.eq(Deliverable::getPlanId, planId);
        if (!CollectionUtils.isEmpty(deliverableQueryDTO.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(deliverableQueryDTO.getSearchConditions(), deliverableLambdaQueryWrapper);
        }
        //todo 因为版本，查询要修改
        deliverableLambdaQueryWrapper.innerJoin("( SELECT max( rev_order ) AS revOrder, rev_key as revKey FROM pms_deliverable GROUP BY rev_key ) b ON t.rev_order = b.revOrder\n" +
                "\tAND t.rev_key = b.revKey");
        List<Deliverable> deliverableDTOList = this.list(deliverableLambdaQueryWrapper);
        if (CollectionUtils.isEmpty(deliverableDTOList)) {
            return new ArrayList<>();
        }
        List<DeliverableVo> deliverableVos = new ArrayList<>();
        Set<String> planIdSet = new HashSet<>();
        Set<String> projectIdSet = new HashSet<>();
        List<String> idList = new ArrayList<>();
        List<String> userIdList = new ArrayList<>();
        for (Deliverable deliverableDTO : deliverableDTOList) {
            planIdSet.add(deliverableDTO.getPlanId());
            if(StringUtils.hasText(deliverableDTO.getPrincipalId())){
                userIdList.add(deliverableDTO.getPrincipalId());
            }
            projectIdSet.add(deliverableDTO.getProjectId());
            idList.add(deliverableDTO.getId());
        }

        List<ProjectScheme> projectDTOList = projectSchemeService.list(new LambdaQueryWrapper<>(ProjectScheme.class).
                in(ProjectScheme::getId, planIdSet));
        Map<String, String> projectMap = projectDTOList.stream().collect(Collectors.toMap(ProjectScheme::getId, ProjectScheme::getName));

        Map<String, String> idToNameMapByIdList = projectService.getIdToNameMapByIdList(new ArrayList<>(projectIdSet));
        Map<String, UserVO> idToEntityMap = userRedisHelper.getUserMapByUserIds(CurrentUserHelper.getOrgId(), userIdList);

        Date actualEndTime = projectScheme.getActualEndTime();
        for (Deliverable deliverableDTO : deliverableDTOList) {
            if(StringUtils.hasText(deliverableDTO.getPrincipalId())){
                userIdList.add(idToEntityMap.getOrDefault(deliverableDTO.getPrincipalId(),new UserVO()).getName());
            }
            DeliverableVo deliverableVo = new DeliverableVo();
            String planId1 = deliverableDTO.getPlanId();
            String projectId = deliverableDTO.getProjectId();
            BeanCopyUtils.copyProperties(deliverableDTO, deliverableVo);
            deliverableVo.setPlanName(projectMap.get(planId1));
            deliverableVo.setProjectId(projectId);
            deliverableVo.setProjectName(idToNameMapByIdList.get(projectId));
            Integer status = projectScheme.getStatus();
            deliverableVo.setStatus(status);
            deliverableVo.setDataStatus(projectScheme.getDataStatus());
            if (Objects.nonNull(projectScheme.getDataStatus())) {
                deliverableVo.setStatusName(projectScheme.getDataStatus().getName());
            }
            if (!ObjectUtil.equal(null,actualEndTime)){
                deliverableVo.setActualEndTime(actualEndTime);
            }
            deliverableVos.add(deliverableVo);
        }
        return deliverableVos;
    }

    @Override
    public DeliverableVo getDetailById(String id) throws Exception {
        DeliverableVo deliverableVo = new DeliverableVo();
        Deliverable deliverableDTO = this.getById(id);
        if (ObjectUtils.isEmpty(deliverableDTO)) {
            return deliverableVo;
        }
        String creatorId = deliverableDTO.getCreatorId();
        String modifyId = deliverableDTO.getModifyId();
        String ownerId = deliverableDTO.getOwnerId();
        Set<String> userIdSet = new HashSet<>();
        userIdSet.add(creatorId);
        userIdSet.add(modifyId);
        userIdSet.add(ownerId);
        Map<String, String> nameByUserIdMap = userBo.getNameByUserIdMap(new ArrayList<>(userIdSet));
        BeanCopyUtils.copyProperties(deliverableDTO, deliverableVo);
        deliverableVo.setCreatorName(nameByUserIdMap.get(creatorId));
        deliverableVo.setModifyName(nameByUserIdMap.get(modifyId));
        deliverableVo.setOwnerName(nameByUserIdMap.get(ownerId));
        String planId = deliverableDTO.getPlanId();
        if (StringUtils.hasText(planId)) {
            ProjectScheme projectScheme = projectSchemeService.getById(planId);
            if (Objects.nonNull(projectScheme)) {
                deliverableVo.setPlanName(projectScheme.getName());
                deliverableVo.setPlanName(projectScheme.getName());
                Integer status = projectScheme.getStatus();
                deliverableVo.setStatus(status);
                deliverableVo.setDataStatus(projectScheme.getDataStatus());
                if (Objects.nonNull(projectScheme.getDataStatus())) {
                    deliverableVo.setStatusName(projectScheme.getDataStatus().getName());
                }
            }

        }
        String projectId = deliverableDTO.getProjectId();
        if (StringUtils.hasText(projectId)) {
            Map<String, String> idToNameMapByIdList = projectService.getIdToNameMapByIdList(Collections.singletonList(projectId));
            deliverableVo.setProjectName(idToNameMapByIdList.get(projectId));
        }
        deliverableVo.setProjectId(projectId);
        // 设置版本通过document 获取
        String documentId = deliverableDTO.getDocumentId();
        if (StringUtils.hasText(documentId)) {
            List<DocumentDTO> listByIdList = documentBo.getListByIdList(Collections.singletonList(documentId));
            Map<String, DocumentDTO> idToObjectMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(listByIdList)) {
                idToObjectMap = listByIdList.stream().collect(Collectors.toMap(DocumentDTO::getId, o -> o));
            }
            DocumentDTO documentDTO = idToObjectMap.get(id);
            if (!ObjectUtils.isEmpty(documentDTO)) {
                deliverableVo.setRevId(documentDTO.getRevId());
                deliverableVo.setModifyTime(documentDTO.getModifyTime());
            }
        }

        return deliverableVo;
    }

    @Override
    public DeliverPage pageList(com.chinasie.orion.sdk.metadata.page.Page<DeliverableDTO> pageRequest) throws Exception {
        DeliverableDTO query = pageRequest.getQuery();
        DeliverPage pageResult = new DeliverPage();
        IPage<Deliverable> deliverableIPage = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());

        LambdaQueryWrapperX<Deliverable> queryCondition0 = new LambdaQueryWrapperX<>(Deliverable.class);
        if (ObjectUtil.isNotEmpty(query)) {
            queryCondition0.eqIfPresent(Deliverable::getProjectId, query.getProjectId());
            if(StrUtil.isNotBlank(query.getName())) {
                queryCondition0.and(m -> m.like(Deliverable::getName, query.getName()).or().like(Deliverable::getNumber, query.getName()));
            }
        }
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), queryCondition0);
        }
        List<DeliverableVo> deliverableVos = new ArrayList<>();

        queryCondition0.innerJoin("( SELECT max( rev_order ) AS revOrder, rev_key as revKey FROM pms_deliverable GROUP BY rev_key ) b ON t.rev_order = b.revOrder\n" +
                "\tAND t.rev_key = b.revKey");
        IPage<Deliverable> page = this.page(deliverableIPage, queryCondition0);
        pageResult.setPageNum(pageRequest.getPageNum());
        pageResult.setPageSize(pageRequest.getPageSize());
        if (ObjectUtils.isEmpty(page) || page.getRecords() == null || page.getRecords().size() <= 0) {
            pageResult.setTotalSize(0L);
            pageResult.setContent(deliverableVos);
            return pageResult;
        }

        LambdaQueryWrapper<Deliverable> queryCondition = new LambdaQueryWrapper<>();
        queryCondition.eq(Deliverable::getStatus, ProjectStatusEnum.CLOSE.getStatus());
        if (ObjectUtil.isNotEmpty(query)) {
            if(StrUtil.isNotBlank(query.getProjectId())) {
                queryCondition.eq(Deliverable::getProjectId, query.getProjectId());
            }
            if(StrUtil.isNotBlank(query.getName())) {
                queryCondition.and(m -> m.like(Deliverable::getName, query.getName()).or().like(Deliverable::getNumber, query.getName()));
            }
        }

        long count = this.count(queryCondition);
        pageResult.setFinishCount(count);




        List<Deliverable> content = page.getRecords();
        Set<String> userIdSet = new HashSet<>();
        Set<String> planIdSet = new HashSet<>();
        Set<String> projectIdSet = new HashSet<>();
        List<String> idList = new ArrayList<>();
        for (Deliverable deliverable : content) {
            planIdSet.add(deliverable.getPlanId());
            projectIdSet.add(deliverable.getProjectId());
            String creatorId = deliverable.getCreatorId();
            String modifyId = deliverable.getModifyId();
            String ownerId = deliverable.getOwnerId();
            userIdSet.add(creatorId);
            userIdSet.add(modifyId);
            userIdSet.add(ownerId);
            if(StringUtils.hasText(deliverable.getPrincipalId())){
                userIdSet.add(deliverable.getPrincipalId());
            }
            idList.add(deliverable.getId());
        }
        List<ProjectScheme> projectDTOList = projectSchemeService.list(new LambdaQueryWrapper<>(ProjectScheme.class).
                in(ProjectScheme::getId, planIdSet));
        Map<String, ProjectScheme> map = projectDTOList.stream().collect(Collectors.toMap(ProjectScheme::getId, Function.identity(), (v1, v2) -> v2));
        Map<String, String> idToNameMapByIdList = projectService.getIdToNameMapByIdList(new ArrayList<>(projectIdSet));
        Map<String, String> nameByUserIdMap = userBo.getNameByUserIdMap(new ArrayList<>(userIdSet));
        for (Deliverable deliverable : content) {
            DeliverableVo deliverableVo = new DeliverableVo();
            BeanCopyUtils.copyProperties(deliverable, deliverableVo);
            String creatorId = deliverable.getCreatorId();
            String modifyId = deliverable.getModifyId();
            String ownerId = deliverable.getOwnerId();
            if(StringUtils.hasText(deliverable.getPrincipalId())){
                deliverableVo.setPrincipalName(nameByUserIdMap.getOrDefault(deliverable.getPrincipalId(),""));
            }
            deliverableVo.setCreatorName(nameByUserIdMap.get(creatorId));
            deliverableVo.setModifyName(nameByUserIdMap.get(modifyId));
            deliverableVo.setOwnerName(nameByUserIdMap.get(ownerId));
            String planId1 = deliverable.getPlanId();
            String projectId = deliverable.getProjectId();
            deliverableVo.setProjectId(projectId);

            ProjectScheme projectScheme = map.get(planId1);
            if (Objects.nonNull(projectScheme)) {
                deliverableVo.setPlanName(projectScheme.getName());
                Integer status = projectScheme.getStatus();
                deliverableVo.setStatus(status);
                deliverableVo.setDataStatus(projectScheme.getDataStatus());
                if (Objects.nonNull(projectScheme.getDataStatus())) {
                    deliverableVo.setStatusName(projectScheme.getDataStatus().getName());
                }
            }
            deliverableVo.setProjectName(idToNameMapByIdList.get(projectId));
            deliverableVos.add(deliverableVo);
        }
        pageResult.setContent(deliverableVos);
        pageResult.setTotalSize(page.getTotal());
        pageResult.setTotalPages(page.getPages());
        return pageResult;
    }

    @Override
    public String saveDeliver(DeliverableDTO deliverableDTO) throws Exception {
        //生成编码
        List<CodeSegmentVO> codeRuleList = codeBo.getCodeRuleList(ClassNameConstant.DELIVERABLE, ClassNameConstant.NUMBER);
        DocumentDTO documentDTO = new DocumentDTO();
        if (!CollectionUtils.isEmpty(codeRuleList)) {
            String code = codeBo.getCode(codeRuleList);
            deliverableDTO.setNumber(code);
            documentDTO.setNumber(code);
        }
        UserVO principal = userRedisHelper.getUserById(deliverableDTO.getPrincipalId());
        if(StringUtils.hasText(deliverableDTO.getPrincipalId())&&
                ObjectUtil.isNotEmpty(principal)){
            deliverableDTO.setPrincipalName(principal.getName());
        }

        Deliverable save = BeanCopyUtils.convertTo(deliverableDTO, Deliverable::new);
        RevisionUtils.setDefaultRevision(save, false);
        this.save(save);
        documentDTO.setName(deliverableDTO.getName());
        documentDTO.setClassName(DocumentClassNameConstant.Deliver_Document);
        String s = documentBo.insertDocument(documentDTO);
        deliverableDTO.setDocumentId(s);
        deliverableDTO.setId(save.getId());
        Deliverable update = BeanCopyUtils.convertTo(deliverableDTO, Deliverable::new);
        this.updateById(update);
        return save.getId();
    }

    @Override
    public Boolean updateDeliver(DeliverableDTO deliverableDTO) throws Exception {
        String id = deliverableDTO.getId();
        Deliverable deliverableDTO1 = this.getById(id);
        if (ObjectUtils.isEmpty(deliverableDTO1)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "未获取到数据，请重新操作");
        }
        Deliverable updateData = BeanCopyUtils.convertTo(deliverableDTO, Deliverable::new);

        Boolean update = this.updateById(updateData);
        String documentId = deliverableDTO1.getDocumentId();
        if (StringUtils.hasText(documentId)) {
            DocumentDTO documentDTO = new DocumentDTO();
            documentDTO.setId(documentId);
            documentDTO.setName(deliverableDTO.getName());
            documentDTO.setNumber(deliverableDTO.getNumber());
            documentDTO.setClassName(DocumentClassNameConstant.Deliver_Document);
            documentBo.updateDocument(documentDTO);
        }
        return update;
    }

    @Override
    public boolean delBatch(List<String> devIdList) throws Exception {
        List<Deliverable> deliverableDTOS = this.listByIds(devIdList);
        List<String> documentIdList = new ArrayList<>();
        for (Deliverable deliverableDTO : deliverableDTOS) {
            if (StringUtils.hasText(deliverableDTO.getId())) {
                documentIdList.add(deliverableDTO.getId());
            }
        }
        this.removeBatchByIds(devIdList);
        if (CollectionUtils.isEmpty(documentIdList)) {
            documentBo.delByIdList(documentIdList);
        }
        return false;
    }

    @Override
    public ProjectDeliverCount getCount(String projectId) throws Exception {
        ProjectDeliverCount projectDeliverCount = new ProjectDeliverCount();
        //todo 因为版本，查询要修改
        LambdaQueryWrapper<Deliverable> deliverableLambdaQueryWrapper = new LambdaQueryWrapper<>(Deliverable.class);
        deliverableLambdaQueryWrapper.eq(Deliverable::getProjectId, projectId);
        List<Deliverable> deliverableDTOList = this.list(deliverableLambdaQueryWrapper);
        if (CollectionUtils.isEmpty(deliverableDTOList)) {
            return projectDeliverCount;
        }
        int finishCount = 0;
        int unFinishCount = 0;
        for (Deliverable deliverableDTO : deliverableDTOList) {
            Integer status = deliverableDTO.getStatus();
            if (status.equals(130)) {
                finishCount += 1;
            } else {
                unFinishCount += 1;
            }
        }
        int total = finishCount + unFinishCount;
        projectDeliverCount.setCommitCount(finishCount);
        projectDeliverCount.setUnCommitCount(unFinishCount);
        double v = finishCount / total;
        double v1 = unFinishCount / total;
        DecimalFormat df = new DecimalFormat("0.00%");
        projectDeliverCount.setCommitPercentage(df.format(v));
        projectDeliverCount.setUnCommitPercentage(df.format(v1));
        projectDeliverCount.setTotalCount(total);
        return projectDeliverCount;
    }

    @Override
    public Boolean upgrade(String id) throws Exception {
        Deliverable originalDeliverableDTO = this.getById(id);
        if (Objects.isNull(originalDeliverableDTO)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }

        Deliverable revisionDeliverable = this.getBaseMapper().selectLatestRevisionOneById(id);
        Deliverable revision = RevisionUtils.upgradeRevision(revisionDeliverable);
        this.save(revision);
        String newId = revision.getId();
        // 新增壳
        DocumentDTO documentDTO = new DocumentDTO();
        documentDTO.setNumber(originalDeliverableDTO.getNumber());
        documentDTO.setName(originalDeliverableDTO.getName());
        documentDTO.setClassName(DocumentClassNameConstant.Deliver_Document);
        String s = documentBo.insertDocument(documentDTO);
        //修改绑定的document
        Deliverable deliverableDTO = this.getById(newId);
        deliverableDTO.setDocumentId(s);
        this.updateById(deliverableDTO);
        return Boolean.TRUE;
    }

    @Override
    public List<DeliverableVo> getDeliverableRevision(String revKey) throws Exception {
        LambdaQueryWrapper<Deliverable> wrapper = new LambdaQueryWrapper<>(Deliverable.class);
        wrapper.eq(Deliverable::getRevKey, revKey);
        wrapper.orderByAsc(Deliverable::getRevOrder);

        List<Deliverable> deliverableDTOS = this.list(wrapper);
        if (CollectionUtils.isEmpty(deliverableDTOS)) {
            return new ArrayList<>();
        }
        List<String> planIdSet = deliverableDTOS.stream().map(Deliverable::getPlanId).distinct().collect(Collectors.toList());

        List<ProjectScheme> projectDTOList = projectSchemeService.list(new LambdaQueryWrapper<>(ProjectScheme.class).
                in(ProjectScheme::getId, planIdSet));
        Map<String, ProjectScheme> planMap = projectDTOList.stream().collect(Collectors.toMap(ProjectScheme::getId, Function.identity()));


        List<DeliverableVo> deliverableVoList = BeanCopyUtils.convertListTo(deliverableDTOS, DeliverableVo::new);
        deliverableVoList.forEach(d -> {
            ProjectScheme projectScheme = planMap.get(d.getPlanId());
            if (Objects.nonNull(projectScheme)) {
                d.setPlanName(projectScheme.getName());
                Integer status = projectScheme.getStatus();
                d.setStatus(status);
                d.setDataStatus(projectScheme.getDataStatus());
                if (Objects.nonNull(projectScheme.getDataStatus())) {
                    d.setStatusName(projectScheme.getDataStatus().getName());
                }
            }
        });
        return deliverableVoList;

    }


    @Override
    public String change(String id, EcrDTO ecrDTO) throws Exception {
        Deliverable deliverableDTO = this.getById(id);
        ObjectDTO mtlObjectDTO = new ObjectDTO();
        mtlObjectDTO.setId(id);
        mtlObjectDTO.setClassName(deliverableDTO.getClassName());
        ecrDTO.setPasObjectDTOS(Collections.singletonList(mtlObjectDTO));
        if (StrUtil.isBlank(ecrDTO.getName())) {
            ecrDTO.setName(deliverableDTO.getName() + "变更申请单");
        }
        ecrDTO.setProjectId(deliverableDTO.getProjectId());
        ResponseDTO<String> addResponse = pasFeignService.add(ecrDTO);
        return addResponse.getResult();
    }

    @Override
    public List<DeliverableVo> search(SearchDTO searchDTO) throws Exception {
        LambdaQueryWrapperX<Deliverable> wrapper = new LambdaQueryWrapperX<>(Deliverable.class);
        if (!CollectionUtils.isEmpty(searchDTO.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchDTO.getSearchConditions(),wrapper);
        }
        List<String> ids = searchDTO.getIds();
        if (!CollectionUtils.isEmpty(ids)) {
            wrapper.in(Deliverable::getId, ids.toArray());
        }
        Integer status = searchDTO.getStatus();
        if (Objects.nonNull(status)) {
            wrapper.eq(Deliverable::getStatus, status);
        }
        List<Deliverable> deliverableDTOS = this.list(wrapper);
        List<DeliverableVo> deliverableVoList = BeanCopyUtils.convertListTo(deliverableDTOS, DeliverableVo::new);
        return deliverableVoList;
    }

    @Override
    public List<AnalysisVO> analysis(String id) throws Exception {
        Deliverable deliverableDTO = this.getById(id);
        AnalysisVO analysisVO = new AnalysisVO(id, "0", id);
        List<AnalysisVO> plans = new ArrayList<>();
        List<AnalysisVO> projects = new ArrayList<>();
        //交付物与计划的关系
        String planId = deliverableDTO.getPlanId();
        if(StringUtils.hasText(planId)){
            ProjectScheme projectScheme = projectSchemeService.getById(planId);
            if(projectScheme != null){
                 AnalysisVO planAnalysisVO = AnalysisVO.builder()
                .relationId(projectScheme.getId())
                .name(projectScheme.getName())
                .dataType(projectScheme.getClassName())
                .modifyTime(projectScheme.getModifyTime())
                .number(projectScheme.getNumber())
                .ownerId(projectScheme.getOwnerId())
                .owner(projectScheme.getOwnerName())
                .id(projectScheme.getId())
                .build();
                plans.add(planAnalysisVO);
                String projectId = projectScheme.getProjectId();
                if(StringUtils.hasText(projectId)){
                    Project project = projectService.getById(projectId);
                    if(project != null){
                        AnalysisVO projectAnalysisVO = AnalysisVO.builder()
                                .relationId(project.getId())
                                .name(project.getName())
                                .dataType(project.getClassName())
                                .modifyTime(project.getModifyTime())
                                .number(project.getNumber())
                                .ownerId(project.getOwnerId())
                                .owner(project.getOwnerName())
                                .id(project.getId())
                                .build();
                        projects.add(projectAnalysisVO);
                    }

                    planAnalysisVO.setChildren(projects);
                }
            }
        }
        List<AnalysisVO> result = new ArrayList<>();


        plans.forEach(p -> p.setParentId(id));
        analysisVO.setChildren(plans);
        result.add(analysisVO);
        return result;
    }

    @Override
    public List<DeliverableVo> listByDeliverGoals(String deliverGoalsId) throws Exception {

        List<DeliverGoalsToDeliverable> listByDeliverGoals = deliverGoalsToDeliverableService.getListByDeliverGoals(Collections.singletonList(deliverGoalsId));
        if (CollectionUtil.isEmpty(listByDeliverGoals)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapperX<Deliverable> deliverableLambdaQueryWrapper = new LambdaQueryWrapperX<>(Deliverable.class);
        deliverableLambdaQueryWrapper.in(Deliverable::getId, listByDeliverGoals.stream().map(DeliverGoalsToDeliverable::getToId).collect(Collectors.toList()));
        List<Deliverable> deliverableDTOList = this.list(deliverableLambdaQueryWrapper);
        if (CollectionUtils.isEmpty(deliverableDTOList)) {
            return new ArrayList<>();
        }

        List<ProjectScheme> projectSchemeList = projectSchemeService.list(new LambdaQueryWrapper<>(ProjectScheme.class)
                .select(ProjectScheme::getId, ProjectScheme::getName, ProjectScheme::getClassName, ProjectScheme::getStatus)
                .in(ProjectScheme::getId, deliverableDTOList.stream().map(Deliverable::getPlanId).distinct().collect(Collectors.toList())));
        Map<String, ProjectScheme> projectSchemeMap = projectSchemeList.stream().collect(Collectors.toMap(ProjectScheme::getId, Function.identity()));

        List<DeliverableVo> deliverableVos = new ArrayList<>();
        for (Deliverable deliverableDTO : deliverableDTOList) {
            DeliverableVo deliverableVo = new DeliverableVo();
            BeanCopyUtils.copyProperties(deliverableDTO, deliverableVo);
            ProjectScheme projectScheme = projectSchemeMap.get(deliverableDTO.getPlanId());
            if (ObjectUtil.isNotEmpty(projectScheme)) {
                deliverableVo.setPlanName(projectScheme.getName());
                DataStatusVO dataStatus = projectScheme.getDataStatus();
                if (ObjectUtil.isNotEmpty(dataStatus)) {
                    deliverableVo.setStatusName(dataStatus.getName());
                }
            }
            deliverableVos.add(deliverableVo);
        }
        return deliverableVos;
    }

    @Override
    public com.chinasie.orion.sdk.metadata.page.Page<DeliverablePerformanceVO> pageDeliverableByUserId(String userId, com.chinasie.orion.sdk.metadata.page.Page<DeliverableDTO> pageRequest) throws Exception {
        DeliverableDTO query = pageRequest.getQuery();
        if (StrUtil.isBlank(userId)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "用户id未传");
        }
        LambdaQueryWrapperX<ProjectScheme> wrapperX = new LambdaQueryWrapperX<>(ProjectScheme.class);
        wrapperX.select(ProjectScheme::getId, ProjectScheme::getName);
        wrapperX.eq(ProjectScheme::getRspUser, userId);
        if (query != null && StrUtil.isNotBlank(query.getProjectId())) {
            wrapperX.eq(ProjectScheme::getProjectId, query.getProjectId());
        }
        List<ProjectScheme> projectSchemeList = projectSchemeService.list(wrapperX);

        com.chinasie.orion.sdk.metadata.page.Page<DeliverablePerformanceVO> pageResult = new com.chinasie.orion.sdk.metadata.page.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());

        if (CollectionUtil.isNotEmpty(projectSchemeList)) {
            com.chinasie.orion.sdk.metadata.page.Page<Deliverable> realPageRequest = new com.chinasie.orion.sdk.metadata.page.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
            LambdaQueryWrapperX<Deliverable> condition = new LambdaQueryWrapperX<>(Deliverable.class);
            condition.in(Deliverable::getPlanId, projectSchemeList.stream().map(ProjectScheme::getId).collect(Collectors.toList()));
            PageResult<Deliverable> page = this.getBaseMapper().selectPage(realPageRequest, condition);
            List<Deliverable> content = page.getContent();
            if (CollectionUtil.isNotEmpty(content)) {
                Map<String, String> projectMap = projectService.list(new LambdaQueryWrapperX<>(Project.class)
                        .select(Project::getId, Project::getName)
                        .in(Project::getId, content.stream().map(Deliverable::getProjectId).collect(Collectors.toList())))
                        .stream().collect(Collectors.toMap(Project::getId, Project::getName));
                Map<String, String> projectSchemeMap = projectSchemeList.stream().collect(Collectors.toMap(ProjectScheme::getId, ProjectScheme::getName));
                List<DeliverablePerformanceVO> vos = content.stream().map(m -> {
                    DeliverablePerformanceVO deliverablePerformanceVO = BeanCopyUtils.convertTo(m, DeliverablePerformanceVO::new);
                    deliverablePerformanceVO.setPlanName(projectSchemeMap.get(m.getPlanId()));
                    deliverablePerformanceVO.setProjectName(projectMap.get(m.getProjectId()));
                    return deliverablePerformanceVO;
                }).collect(Collectors.toList());
                pageResult.setContent(vos);
                pageResult.setTotalSize(page.getTotalSize());
            }
        }

        return pageResult;
    }

    @Override
    public void exportExcelByUserId(String userId, com.chinasie.orion.sdk.metadata.page.Page<DeliverableDTO> pageRequest, HttpServletResponse response) throws Exception {
        DeliverableDTO query = pageRequest.getQuery();
        if (StrUtil.isBlank(userId)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "用户id未传");
        }
        LambdaQueryWrapperX<ProjectScheme> wrapperX = new LambdaQueryWrapperX<>(ProjectScheme.class);
        wrapperX.select(ProjectScheme::getId, ProjectScheme::getName);
        wrapperX.eq(ProjectScheme::getRspUser, userId);
        if (query != null && StrUtil.isNotBlank(query.getProjectId())) {
            wrapperX.eq(ProjectScheme::getProjectId, query.getProjectId());
        }
        List<ProjectScheme> projectSchemeList = projectSchemeService.list(wrapperX);
        List<DeliverablePerformanceVO> vos = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(projectSchemeList)) {
            LambdaQueryWrapperX<Deliverable> condition = new LambdaQueryWrapperX<>(Deliverable.class);
            condition.in(Deliverable::getPlanId, projectSchemeList.stream().map(ProjectScheme::getId).collect(Collectors.toList()));
            List<Deliverable> content = this.list(condition);
            if (CollectionUtil.isNotEmpty(content)) {
                Map<String, String> projectMap = projectService.list(new LambdaQueryWrapperX<>(Project.class)
                        .select(Project::getId, Project::getName)
                        .in(Project::getId, content.stream().map(Deliverable::getProjectId).collect(Collectors.toList())))
                        .stream().collect(Collectors.toMap(Project::getId, Project::getName));
                Map<String, String> projectSchemeMap = projectSchemeList.stream().collect(Collectors.toMap(ProjectScheme::getId, ProjectScheme::getName));
                int sort = 1;
                for (Deliverable deliverable : content) {
                    DeliverablePerformanceVO deliverablePerformanceVO = BeanCopyUtils.convertTo(deliverable, DeliverablePerformanceVO::new);
                    deliverablePerformanceVO.setSort(sort++);
                    deliverablePerformanceVO.setPlanName(projectSchemeMap.get(deliverable.getPlanId()));
                    deliverablePerformanceVO.setProjectName(projectMap.get(deliverable.getProjectId()));
                    vos.add(deliverablePerformanceVO);
                }
            }
        }

        String fileName = "交付物导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", DeliverablePerformanceVO.class, vos );


    }

    @Override
    public List<DeliverableVo> getListByIds(List<String> ids) {
        if (CollectionUtil.isEmpty(ids)){
            return new ArrayList<>();
        }
        List<Deliverable> listByIds = this.listByIds(ids);
        if (CollectionUtil.isEmpty(listByIds)){
            return new ArrayList<>();
        }
        List<DeliverableVo> deliverableVos = new ArrayList<>();
        List<String> userIds = listByIds.stream().map(Deliverable::getOwnerId).distinct().collect(Collectors.toList());
        List<String> projectIds = listByIds.stream().map(Deliverable::getProjectId).distinct().collect(Collectors.toList());
        Map<String, UserVO> userMapByUserIds = userRedisHelper.getUserMapByUserIds(userIds);
        List<Project> projects = projectService.listByIds(projectIds);
        Map<String, Project> projectNames = projects.stream().collect(Collectors.toMap(Project::getId, p->p));
        for (Deliverable deliverable : listByIds) {
            DeliverableVo deliverableVo = BeanCopyUtils.convertTo(deliverable, DeliverableVo::new);
            UserVO userVO = userMapByUserIds.get(deliverable.getOwnerId());
            Project project = projectNames.get(deliverable.getProjectId());
            deliverableVo.setOwnerName(userVO.getName());
            if (!Objects.isNull(project)){
                deliverableVo.setProjectName(project.getName());
                deliverableVo.setStatusName(project.getDataStatus().getName());
            }
            deliverableVos.add(deliverableVo);
        }
        return deliverableVos;
    }
}
