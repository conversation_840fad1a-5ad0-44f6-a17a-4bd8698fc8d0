package com.chinasie.orion.management.service.impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.management.domain.dto.ProjectFlowDTO;
import com.chinasie.orion.management.domain.entity.ProjectFlow;
import com.chinasie.orion.management.domain.vo.ProjectFlowVO;
import com.chinasie.orion.management.repository.ProjectFlowMapper;
import com.chinasie.orion.management.service.ProjectFlowService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <p>
 * ProjectFlow 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-31 14:17:01
 */
@Service
@Slf4j
public class ProjectFlowServiceImpl extends OrionBaseServiceImpl<ProjectFlowMapper, ProjectFlow> implements ProjectFlowService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProjectFlowVO detail(String id, String pageCode) throws Exception {
        ProjectFlow projectFlow = this.getById(id);
        ProjectFlowVO result = BeanCopyUtils.convertTo(projectFlow, ProjectFlowVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param projectFlowDTO
     */
    @Override
    public String create(ProjectFlowDTO projectFlowDTO) throws Exception {
        ProjectFlow projectFlow = BeanCopyUtils.convertTo(projectFlowDTO, ProjectFlow::new);
        this.save(projectFlow);

        String rsp = projectFlow.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param projectFlowDTO
     */
    @Override
    public Boolean edit(ProjectFlowDTO projectFlowDTO) throws Exception {
        ProjectFlow projectFlow = BeanCopyUtils.convertTo(projectFlowDTO, ProjectFlow::new);

        this.updateById(projectFlow);

        String rsp = projectFlow.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectFlowVO> pages(Page<ProjectFlowDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProjectFlow> condition = new LambdaQueryWrapperX<>(ProjectFlow.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ProjectFlow::getCreateTime);


        Page<ProjectFlow> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectFlow::new));

        PageResult<ProjectFlow> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectFlowVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectFlowVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectFlowVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "流程信息导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectFlowDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ProjectFlowExcelListener excelReadListener = new ProjectFlowExcelListener();
        EasyExcel.read(inputStream, ProjectFlowDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ProjectFlowDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("流程信息导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ProjectFlow> projectFlowes = BeanCopyUtils.convertListTo(dtoS, ProjectFlow::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ProjectFlow-import::id", importId, projectFlowes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ProjectFlow> projectFlowes = (List<ProjectFlow>) orionJ2CacheService.get("pmsx::ProjectFlow-import::id", importId);
        log.info("流程信息导入的入库数据={}", JSONUtil.toJsonStr(projectFlowes));

        this.saveBatch(projectFlowes);
        orionJ2CacheService.delete("pmsx::ProjectFlow-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ProjectFlow-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ProjectFlow> condition = new LambdaQueryWrapperX<>(ProjectFlow.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ProjectFlow::getCreateTime);
        List<ProjectFlow> projectFlowes = this.list(condition);

        List<ProjectFlowDTO> dtos = BeanCopyUtils.convertListTo(projectFlowes, ProjectFlowDTO::new);

        String fileName = "流程信息数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectFlowDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<ProjectFlowVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }

    @Override
    public ProjectFlowVO getByNumber(String number) throws Exception {
        LambdaQueryWrapperX<ProjectFlow> condition = new LambdaQueryWrapperX<>(ProjectFlow.class);
        condition.eq(ProjectFlow::getOrderNumber, number);
        List<ProjectFlow> list = this.list(condition);
        return CollectionUtils.isEmpty(list) ? new ProjectFlowVO() : BeanCopyUtils.convertTo(list.get(0), ProjectFlowVO::new);
    }


    public static class ProjectFlowExcelListener extends AnalysisEventListener<ProjectFlowDTO> {

        private final List<ProjectFlowDTO> data = new ArrayList<>();

        @Override
        public void invoke(ProjectFlowDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ProjectFlowDTO> getData() {
            return data;
        }
    }


}
