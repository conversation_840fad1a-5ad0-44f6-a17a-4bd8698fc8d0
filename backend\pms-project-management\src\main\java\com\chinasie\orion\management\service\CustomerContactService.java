package com.chinasie.orion.management.service;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.management.domain.dto.CustomerContactDTO;
import com.chinasie.orion.management.domain.entity.CustomerContact;
import com.chinasie.orion.management.domain.vo.CustomerContactVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * customerContact 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-30 16:25:24
 */
public interface CustomerContactService extends OrionBaseService<CustomerContact> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    CustomerContactVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param customerContactDTO
     */
    String create(CustomerContactDTO customerContactDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param customerContactDTO
     */
    Boolean edit(CustomerContactDTO customerContactDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     *
     * @param mainTableId
     */
    Page<CustomerContactVO> pages(String mainTableId, Page<CustomerContactDTO> pageRequest) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<CustomerContactVO> vos) throws Exception;

    /**
     * 导出联系人
     *
     * @param searchConditions
     * @param response
     * @throws Exception
     */
    void exportByContactExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;
}
