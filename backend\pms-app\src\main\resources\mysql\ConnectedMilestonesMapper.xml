<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.ConnectedMilestonesMapper">
    <select id="getConnectedMilestonesPage" resultType="com.chinasie.orion.domain.vo.ConnectedMilestonesVO">
        SELECT
            pipd.id AS id,
            pcm.contract_id AS contractId,
            pcm.contract_name AS contractName,
            pcm.contract_number AS contractNumber,
            pcm.milestone_id AS milestoneId,
            pcm.milestone_name AS milestoneName,
            pipd.remark AS contractNumRemark,
            pipd.milestone_name AS milestoneRemark,
            pipd.party_A_dept_id AS partyADeptId,
            pipd.id AS incomePlanId,
            pipd.number AS number,
            pipd.expertise_center AS expertiseCenter,
            pipd.expertise_station AS expertiseStation,
            pvn.posting_date AS postingDate,
            pvn.voucher_num AS certificateSerialNumber,
            ROUND( pvn.confirm_revenue_amount, 2 ) AS confirmRevenueAmount,
            ROUND( pvn.reverse_amount, 2 ) AS reverseAmount,
            pipd.estimate_invoice_date AS estimateInvoiceDate,
            pipd.income_confirm_type AS incomeConfirmType,
            pipd.billing_company AS billing_company
        FROM
            pmsx_income_plan_data pipd
                inner JOIN pmsx_income_plan pl ON pl.id = pipd.income_plan_id   AND pl.income_plan_type = pipd.data_version AND pipd.logic_status = 1 AND pl.logic_status = 1
                inner JOIN ( SELECT max( repeat_count ) repeat_count, number FROM pmsx_income_plan_data WHERE logic_status = 1 AND number IS NOT NULL GROUP BY number ) v ON pipd.number = v.number
                left join pmsx_connected_milestones pcm ON pcm.id = pipd.id
                LEFT JOIN (
                SELECT
                    revenue_plan_num,
                    max( posting_date ) posting_date,
                    sum( COALESCE ( confirm_revenue_amount, 0 ) ) + sum( COALESCE ( reverse_amount, 0 ) ) confirm_revenue_amount,
                    sum( COALESCE ( reverse_amount, 0 ) ) reverse_amount,
                    GROUP_CONCAT( voucher_num ORDER BY voucher_num SEPARATOR '、' ) voucher_num
                FROM
                    pmsx_voucher_num
                WHERE
                    logic_status = 1
                GROUP BY
                    revenue_plan_num
                ) pvn ON pvn.revenue_plan_num = pipd.`number`
        <if test="param.expertiseCenterName != null">
            LEFT JOIN pmi_dept cen ON cen.id  = pipd.expertise_center
        </if>
        <if test="param.expertiseStationName != null">
            LEFT JOIN pmi_dept sta ON sta.id  = pipd.expertise_station
        </if>
        WHERE
            pipd.contract_id IS NULL and pipd.status in (121,120,160)
        <if test="param.contractName != null ">
            AND pcm.contract_name like CONCAT('%', #{param.contractName}, '%')
        </if>

        <if test="param.contractNumber != null ">
            AND pcm.contract_number like CONCAT('%', #{param.contractNumber}, '%')
        </if>
        <if test="param.contractNumRemark != null ">
            AND pipd.remark like CONCAT('%', #{param.contractNumRemark}, '%')
        </if>
        <if test="param.milestoneName != null ">
            AND pcm.milestone_name like CONCAT('%', #{param.milestoneName}, '%')
        </if>
        <if test="param.postingDate != null ">
            AND pvn.posting_date = #{param.postingDate}
        </if>

        <if test="param.partyADeptIdName != null ">
            AND pipd.party_A_dept_id_name like CONCAT('%', #{param.partyADeptIdName}, '%')
        </if>

        <if test="param.billingCompany != null ">
            AND pipd.billing_company = #{param.billingCompany}
        </if>

        <if test="param.number != null ">
            AND pipd.number like CONCAT('%', #{param.number}, '%')
        </if>

        <if test="param.incomeConfirmType != null ">
            AND pipd.income_confirm_type = #{param.incomeConfirmType}
        </if>

        <if test="param.certificateSerialNumber != null ">
            AND pipd.certificate_serial_number like  CONCAT('%', #{param.certificateSerialNumber}, '%')
        </if>

        <if test="param.expertiseCenterName != null">
            AND cen.name like CONCAT('%', #{param.expertiseCenterName}, '%')
        </if>
        <if test="param.expertiseStationName != null">
            AND sta.name like CONCAT('%', #{param.expertiseStationName}, '%')
        </if>

        <if test="param.isPermission == false ">
            and (pipd.project_rsp_user_id = #{param.userId}
            <if test="param.centers.size >0" >
                or pipd.expertise_center in
                <foreach collection="param.centers" item="center" open="(" close=")" separator=",">
                    #{center}
                </foreach>
            </if>
            <if test="param.stations.size >0" >
                or pipd.expertise_station in
                <foreach collection="param.stations" item="station" open="(" close=")" separator=",">
                    #{station}
                </foreach>
            </if>
            )
        </if>


    </select>


    <select id="getConnectedMilestonesList" resultType="com.chinasie.orion.domain.vo.ConnectedMilestonesVO">
        SELECT
        pipd.id AS id,
        pcm.contract_id AS contractId,
        pcm.contract_name AS contractName,
        pcm.contract_number AS contractNumber,
        pcm.milestone_id AS milestoneId,
        pcm.milestone_name AS milestoneName,
        pipd.remark AS contractNumRemark,
        pipd.milestone_name AS milestoneRemark,
        pipd.party_A_dept_id AS partyADeptId,
        pipd.id AS incomePlanId,
        pipd.number AS number,
        pipd.expertise_center AS expertiseCenter,
        pipd.expertise_station AS expertiseStation,
        pvn.posting_date AS postingDate,
        pvn.voucher_num AS certificateSerialNumber,
        ROUND( pvn.confirm_revenue_amount, 2 ) AS confirmRevenueAmount,
        ROUND( pvn.reverse_amount, 2 ) AS reverseAmount,
        pipd.estimate_invoice_date AS estimateInvoiceDate,
        pipd.income_confirm_type AS incomeConfirmType,
        pipd.billing_company AS billing_company
        FROM
        pmsx_income_plan_data pipd
        inner JOIN pmsx_income_plan pl ON pl.id = pipd.income_plan_id   AND pl.income_plan_type = pipd.data_version AND pipd.logic_status = 1 AND pl.logic_status = 1
        inner JOIN ( SELECT max( repeat_count ) repeat_count, number FROM pmsx_income_plan_data WHERE logic_status = 1 AND number IS NOT NULL GROUP BY number ) v ON pipd.number = v.number
        left join pmsx_connected_milestones pcm ON pcm.id = pipd.id
        LEFT JOIN (
        SELECT
        revenue_plan_num,
        max( posting_date ) posting_date,
        sum( COALESCE ( confirm_revenue_amount, 0 ) ) + sum( COALESCE ( reverse_amount, 0 ) ) confirm_revenue_amount,
        sum( COALESCE ( reverse_amount, 0 ) ) reverse_amount,
        GROUP_CONCAT( voucher_num ORDER BY voucher_num SEPARATOR '、' ) voucher_num
        FROM
        pmsx_voucher_num
        WHERE
        logic_status = 1
        GROUP BY
        revenue_plan_num
        ) pvn ON pvn.revenue_plan_num = pipd.`number`
        <if test="param.expertiseCenterName != null">
            LEFT JOIN pmi_dept cen ON cen.id  = pipd.expertise_center
        </if>
        <if test="param.expertiseStationName != null">
            LEFT JOIN pmi_dept sta ON sta.id  = pipd.expertise_station
        </if>
        WHERE
        pipd.contract_id IS NULL and pipd.status in (121,120,160)
        <if test="param.contractName != null ">
            AND pcm.contract_name like CONCAT('%', #{param.contractName}, '%')
        </if>

        <if test="param.contractNumber != null ">
            AND pcm.contract_number like CONCAT('%', #{param.contractNumber}, '%')
        </if>
        <if test="param.contractNumRemark != null ">
            AND pipd.remark like CONCAT('%', #{param.contractNumRemark}, '%')
        </if>
        <if test="param.milestoneName != null ">
            AND pcm.milestone_name like CONCAT('%', #{param.milestoneName}, '%')
        </if>
        <if test="param.postingDate != null ">
            AND pvn.posting_date = #{param.postingDate}
        </if>

        <if test="param.partyADeptIdName != null ">
            AND pipd.party_A_dept_id_name like CONCAT('%', #{param.partyADeptIdName}, '%')
        </if>

        <if test="param.billingCompany != null ">
            AND pipd.billing_company = #{param.billingCompany}
        </if>

        <if test="param.number != null ">
            AND pipd.number like CONCAT('%', #{param.number}, '%')
        </if>

        <if test="param.incomeConfirmType != null ">
            AND pipd.income_confirm_type = #{param.incomeConfirmType}
        </if>

        <if test="param.certificateSerialNumber != null ">
            AND pipd.certificate_serial_number like  CONCAT('%', #{param.certificateSerialNumber}, '%')
        </if>

        <if test="param.expertiseCenterName != null">
            AND cen.name like CONCAT('%', #{param.expertiseCenterName}, '%')
        </if>
        <if test="param.expertiseStationName != null">
            AND sta.name like CONCAT('%', #{param.expertiseStationName}, '%')
        </if>

        <if test="param.isPermission == false ">
            and (pipd.project_rsp_user_id = #{param.userId}
            <if test="param.centers.size >0" >
                or pipd.expertise_center in
                <foreach collection="param.centers" item="center" open="(" close=")" separator=",">
                    #{center}
                </foreach>
            </if>
            <if test="param.stations.size >0" >
                or pipd.expertise_station in
                <foreach collection="param.stations" item="station" open="(" close=")" separator=",">
                    #{station}
                </foreach>
            </if>
            )
        </if>
    </select>
</mapper>