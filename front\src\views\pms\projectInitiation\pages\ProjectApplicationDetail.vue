<script setup lang="ts">
import {
  BasicButton,
  BasicTableAction,
  ITableActionItem,
  Layout3,
  Layout3Content,
  OrionTable,
  useDrawer,
  isPower,
  openDrawer,
} from 'lyra-component-vue3';
import { WorkflowAction, WorkflowProps, WorkflowView } from 'lyra-workflow-component-vue3';
import {
  computed, ComputedRef, h, onMounted, provide, readonly, ref, Ref, unref,
} from 'vue';
import { Spin, Empty } from 'ant-design-vue';
import { useRoute, useRouter } from 'vue-router';
import ProjectInfo from './components/ProjectInfo.vue';
import RelatedAccessories from './components/RelatedAccessories.vue';
import Achievement from './modal/Achievement.vue';
import Resource from './modal/Resource.vue';
import Milestone from './components/milestone.vue';
import RequirementInfo from './components/RequirementInfo.vue';
import EstimateTab from './modal/EstimateTab.vue';
import Establishment from './modal/Establishment.vue';
import ProductPlan from './modal/ProductPlan.vue';
import RiskPlaning from './modal/RiskPlaning.vue';
import ProductMessage from './modal/ProductMessage.vue';

import { declarationData, declarationDataId, updateDeclarationData } from './keys';
import Api from '/@/api';
import CreateAndEditDrawer from '../components/CreateAndEditDrawer/Index.vue';
import { setTitleByRootTabsKey } from '/@/utils';
import IncomePlan from '/@/views/pms/incomePlan/index.vue';
import AddTableNode from '/@/views/pms/projectInitiation/components/AddTableNode.vue';

const [registerCreateAndEdit, { openDrawer: openCreateAndEdit }] = useDrawer();
const route = useRoute();
// 立项数据id
const dataId: Ref<string> = ref(route.params.id as string);
provide(declarationDataId, readonly(dataId));
const detailsData: Ref = ref();
const powerData = ref([]);
provide(declarationData, detailsData);
provide('powerData', computed(() => powerData));
const router = useRouter();
const defaultActionId: Ref<string> = ref('');
const workflowActionRef: Ref = ref();
const workflowViewRef: Ref = ref();
const workflowProps: ComputedRef<WorkflowProps> = computed(() => ({
  Api,
  businessData: {
    ...detailsData.value,
  },
  afterEvent() {
    workflowViewRef.value?.init();
    getDetailData();
  },
}));
const loading: Ref<boolean> = ref(false);
// 详情顶部数据

interface MenuItem {
    id: string,
    name: string,
    children?: MenuItem[]
    isShow?:boolean
}

const menuData: Ref<MenuItem[]> = ref([]);

onMounted(() => {
  getDetailData();
});

// 获取详情数据
async function getDetailData() {
  // loading.value = true;
  try {
    const result = await new Api('/pms').fetch('', `projectApproval/${unref(dataId)}`, 'GET');
    loading.value = false;
    defaultActionId.value = menuData.value[0]?.id || 'lXXI';
    // 获取附件
    result.projectCode = result.number;
    detailsData.value = result || {};
    setTitleByRootTabsKey(route?.query?.rootTabsKey, result.name);
  } finally {
    loading.value = false;
  }
}

provide(updateDeclarationData, getDetailData);

function menuChange(option: { id: string, index: number, item: MenuItem }): void {
  defaultActionId.value = option.id;
}

// 添加流程
function handleAddWorkflow() {
  workflowActionRef.value?.onAddTemplate({
    messageUrl: route.fullPath,
  });
}

const isLoading = ref(false);

// 立项完成
function standOk() {
  isLoading.value = true;
  new Api('/pms/projectApproval/completeProjectApproval').fetch({ id: route.params.id }, '', 'GET').then(() => {
  }).finally(() => {
    isLoading.value = false;
  });
}

const actionsBtn:ComputedRef<ITableActionItem[]> = computed(() => [
  {
    text: '编辑',
    isShow: isPower('PMS_XMLXXQ_container_14_button_01', powerData),
    icon: 'sie-icon-bianji',
    onClick(record: any) {
      const drawerRef: Ref = ref();
      openDrawer({
        title: '编辑立项',
        width: 1000,
        content() {
          return h(AddTableNode, {
            ref: drawerRef,
            formType: 'edit',
            drawerData: detailsData.value,
          });
        },
        async onOk(): Promise<void> {
          await drawerRef.value.onSubmit();
          await getDetailData();
        },
      });
    },
  },
  {
    text: '发起流程',
    icon: 'sie-icon-qidongliucheng',
    isShow: isPower('PMS_XMLXXQ_container_14_button_02', powerData) && workflowActionRef.value?.isAdd,
    onClick(record: any) {
      handleAddWorkflow();
    },

  },
  {
    //  === 101
    text: '立项完成',
    isShow: isPower('PMS_XMLXXQ_container_14_button_03', powerData) && defaultActionId.value === 'lCB',
    onClick(record: any) {
      standOk();
    },
  },
]);

function getPowerDataHandle(data) {
  powerData.value = data;
  menuData.value = [
    {
      id: 'lXXI',
      name: '立项信息',
      isShow: isPower('PMS_XMLXXQ_container_01', powerData),
    },
    {
      id: 'XQXX',
      name: '需求评审信息',
      isShow: isPower('PMS_XMLXXQ_container_02', powerData),
    },
    {
      id: 'XTBZ',
      name: '协同编制',
      isShow: isPower('PMS_XMLXXQ_container_03', powerData),
    },
    {
      id: 'lCB',
      name: '里程碑计划',
      isShow: isPower('PMS_XMLXXQ_container_04', powerData),
    },
    {
      id: 'GSCH',
      name: '概算策划',
      isShow: isPower('PMS_XMLXXQ_container_05', powerData),
    },
    {
      id: 'SYCH',
      name: '收益策划',
      isShow: isPower('PMS_XMLXXQ_container_06', powerData),
    },
    {
      id: 'fxch',
      name: '风险策划',
      isShow: isPower('PMS_XMLXXQ_container_07', powerData),
    },
    {
      id: 'cGZS',
      name: '成果策划',
      isShow: isPower('PMS_XMLXXQ_container_08', powerData),
    },
    {
      id: 'zYCH',
      name: '资源策划',
      isShow: isPower('PMS_XMLXXQ_container_09', powerData),
    },
    {
      id: 'product',
      name: '产品策划',
      isShow: isPower('PMS_XMLXXQ_container_10', powerData),
    },
    {
      id: 'xGFJ',
      name: '相关附件',
      isShow: isPower('PMS_XMLXXQ_container_11', powerData),
    },
    {
      id: 'productMessage',
      name: '产品信息',
      isShow: isPower('PMS_XMLXXQ_container_12', powerData),
    },
    {
      id: 'sPLC',
      name: '审批流程',
      isShow: isPower('PMS_XMLXXQ_container_13', powerData),
    },
  ].filter((item) => typeof item.isShow === 'undefined' || item.isShow);
}
function changeTabs(record) {
  switch (record.processObject) {
    case 'taskDecomposition_1':
      router.push({
        name: 'EstablishmentTaskDetails',
        params: { id: record.id },
      });
      break;
    case 'taskDecomposition_2':
      defaultActionId.value = 'SYCH';
      break;
    case 'taskDecomposition_3':
      defaultActionId.value = 'GSCH';
      break;
    case 'taskDecomposition_4':
      defaultActionId.value = 'fxch';
      break;
    case 'taskDecomposition_7':
      defaultActionId.value = 'zYCH';
      break;
    case 'taskDecomposition_6':
      defaultActionId.value = 'product';
      break;
  }
}
</script>

<template>
  <Layout3
    v-get-power="{pageCode: 'PMS1026',getPowerDataHandle}"
    :defaultActionId="defaultActionId"
    :projectData="detailsData"
    :menuData="menuData"
    :type="2"
    :onMenuChange="menuChange"
  >
    <template #header-right>
      <BasicTableAction
        :actions="actionsBtn"
        type="button"
      />
    </template>
    <template #footer>
      <WorkflowAction
        v-if="detailsData?.id"
        ref="workflowActionRef"
        :workflow-props="workflowProps"
      />
    </template>
    <Layout3Content>
      <!--立项信息-->
      <ProjectInfo v-if="defaultActionId==='lXXI'" />
      <!--需求评审信息-->
      <RequirementInfo v-if="defaultActionId==='XQXX'" />
      <!--协同编制-->
      <Establishment
        v-if="defaultActionId==='XTBZ'"
        @change-tabs="changeTabs"
      />
      <!--里程碑计划-->
      <Milestone v-if="defaultActionId==='lCB'" />
      <!--概算策划-->
      <EstimateTab v-if="defaultActionId==='GSCH'" />
      <!--收益策划-->
      <IncomePlan
        v-if="defaultActionId==='SYCH'"
      />
      <!--成果策划-->
      <Achievement v-if="defaultActionId==='cGZS'" />
      <!--风险策划-->
      <RiskPlaning
        v-if="defaultActionId==='fxch'"
      />
      <!--资源策划-->
      <Resource v-if="defaultActionId==='zYCH'" />
      <!--        产品策划-->
      <ProductPlan v-if="defaultActionId==='product'" />
      <!--相关附件-->
      <RelatedAccessories v-if="defaultActionId==='xGFJ'" />
      <!--产品信息-->
      <ProductMessage v-if="defaultActionId==='productMessage'" />
      <!--审批流程-->
      <WorkflowView
        v-if="defaultActionId==='sPLC'"
        ref="workflowViewRef"
        :workflow-props="workflowProps"
      />
    </Layout3Content>
  </Layout3>
</template>

<style scoped lang="less">

</style>
