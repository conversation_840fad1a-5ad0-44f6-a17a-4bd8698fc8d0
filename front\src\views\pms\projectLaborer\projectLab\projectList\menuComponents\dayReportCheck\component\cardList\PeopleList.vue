<template>
  <div class="MonthBox">
    <div
      v-for="item of allPeopleCard"
      :key="item"
      class="monthBoxItem"
    >
      <div class="itemTop">
        <div class="weekNumber">
          {{ item?.creatorName }}
        </div>
        <div
          v-if="item?.score===0||item?.score"
          :class="{circleCore:true,fourColor:item.score % 5>=4,threeColor:item.score % 5===3,twoColor:item.score % 5<=2}"
        >
          {{ item.score }}
        </div>
        <div
          v-else
          class="topRightBtn"
        >
          <span
            v-if="item?.warn"
            class="action-btn mr10"
            @click="goEmit({type:'alert',id:item?.id})"
          >提醒</span>
          <span
            v-if="item?.audit"
            class="action-btn"
            @click="goEmit({type:'check',id:item?.id})"
          >审核</span>
        </div>
      </div>

      <div class="itemContent">
        <div
          v-for="(current,index) of item?.projectDailyStatementContentVOList"
          :key="index"
          class="flex-te "
        >
          <span
            class="mr20"
            :title="item"
            style="color:rgb(60,180,60)"
          >{{ current?.taskTime }}</span>
          <span :title="item">{{ current?.content }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, PropType } from 'vue';
import { Button } from 'ant-design-vue';
import CheckDrawer
  from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/dayReportCheck/component/addOrEdit/CheckDrawer.vue';
// import { PlusOutlined } from '@ant-design/icons-vue';
const AButton = Button;
const emits = defineEmits<{
    (e: 'emitChange', data: Object): void;
}>();
const props = defineProps({
  allPeopleCard: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
});

function goEmit(data) {
  emits('emitChange', data);
}
</script>

<style scoped lang="less">
.MonthBox {
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;

  .monthBoxItem {
    width: 14%;
    height: 180px;
    margin-bottom: 0.3%;
    margin-right: 0.33%;
    border-radius: 3px;
    box-sizing: border-box;
    border: 1px solid #ccc;
    padding: 35px 10px 10px 10px;
    position: relative;

    &:nth-child(7n) {
      margin-right: 0;
    }

    .itemTop {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: auto;
      background-color: red;
    }

    .weekNumber {
      position: absolute;
      width: 150px;
      height: 30px;
      line-height: 30px;
      left: 5px;
      top: 5px;
    }

    .circleCore {
      position: absolute;
      width: 30px;
      height: 30px;
      border-radius: 50%;
      text-align: center;
      line-height: 30px;
      right: 5px;
      top: 5px;
    }

    .topRightBtn {
      position: absolute;
      height: 30px;
      text-align: center;
      line-height: 30px;
      right: 5px;
      top: 5px;
    }

    .itemContent {
      overflow: auto;
      height: 100%;
      width: 100%;
    }
  }
}

.fourColor {
  background-color: #faa519;
}

.threeColor {
  background-color: rgb(102, 204, 204);
}

.twoColor {
  background-color: #ccc;
}
</style>
