package com.chinasie.orion.domain.dto.acceptance;

import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import java.util.Date;

/**
 * 验收单DTO.
 *
 * <AUTHOR>
 */
public class AcceptanceFormDTO extends ObjectDTO {

    // 项目Id
    private String projectId;

    // 验收单编号
    private String number;

    // 验收完成日期
    private Date completeTime;

    // 验收完成执行人Id
    private String completeUserId;

    // 验收完成执行人名称
    private String completeUserName;

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public Date getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }

    public String getCompleteUserId() {
        return completeUserId;
    }

    public void setCompleteUserId(String completeUserId) {
        this.completeUserId = completeUserId;
    }

    public String getCompleteUserName() {
        return completeUserName;
    }

    public void setCompleteUserName(String completeUserName) {
        this.completeUserName = completeUserName;
    }
}
