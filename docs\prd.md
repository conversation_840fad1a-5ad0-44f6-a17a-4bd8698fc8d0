<<<<<<< HEAD
# 精益管理一体化平台-员工访问流程说明

## 1. 登录页面
![登录页面](seq/login-page.png)
**页面说明：**
- 用户输入用户名和密码，点击"立即登录"按钮。
- 支持记住用户名、忘记密码等功能。

**后台API调用（抓包推测）**：
- POST /api/auth/login
  - 说明：提交账号和密码，进行身份认证。

---

## 2. 选择组织
![选择组织](seq/select-org.png)
**页面说明：**
- 登录后弹出组织选择框，用户需选择所属组织。
- 选择后点击"确定"进入系统主页。

**后台API调用（抓包推测）**：
- GET /api/org/list
  - 说明：获取可选组织列表。
- POST /api/org/select
  - 说明：提交用户选择的组织。

---

## 3. 系统主页
![系统主页](seq/home-page.png)
**页面说明：**
- 展示个人门户、待办、已办、发起、参与、待阅、已阅等模块。
- 提供办公助手、考勤、OA、PLM等快捷入口。
- 顶部显示当前登录用户信息。

**后台API调用（抓包推测）**：
- GET /api/user/home
  - 说明：获取首页数据和用户信息。
- GET /api/task/list
  - 说明：获取待办、已办等任务数据。
- GET /api/shortcut/list
  - 说明：获取办公助手快捷入口。

## 3.1 战略计划
![战略计划](seq/plan-board.png)
**页面说明：**
- 展示公司战略计划相关的任务、进度、全景图等信息。
- 提供战略目标、年度经营计划、绩效考核、风险管理等功能入口。

**后台API调用（推测）**：
- GET /api/plan/overview
- GET /api/plan/tasks
- GET /api/plan/performance

---

## 3.2 市场经营
![市场经营](seq/market-board.png)
**页面说明：**
- 展示市场经营一览、线索管理、需求管理、报价管理、合同管理、客户管理等模块。
- 提供市场数据分析、经营报表等功能。

**后台API调用（推测）**：
- GET /api/market/overview
- GET /api/market/leads
- GET /api/market/contracts

### 市场经营功能子页面明细

#### 1. 市场经营一览
![市场经营一览](images/front_system/市场经营_线索池_全部线索.png)
- **页面说明**：展示市场经营线索池、整体线索分布和经营总览。

---

#### 2. 需求管理
![需求管理](images/front_system/市场经营_需求管理.png)
- **页面说明**：管理市场需求，包括需求的创建、查询、附件、基本信息等。

![需求管理-创建需求](images/front_system/市场经营_需求管理_创建需求.png)
![需求管理-基本信息](images/front_system/市场经营_需求管理_基本信息.png)

---

#### 3. 报价管理
![报价管理](images/front_system/市场经营_报价管理.png)
- **页面说明**：管理所有市场报价，包括报价的创建、基本信息、双方签约主体信息等。

![报价管理-创建报价](images/front_system/市场经营_报价管理_创建报价.png)
![报价管理-基本信息](images/front_system/市场经营_报价管理_基本信息.png)

---

#### 4. 合同管理
![合同管理](images/front_system/市场经营_合同管理.png)
- **页面说明**：管理市场合同，包括合同的创建、合同详情等。

![合同管理-创建](images/front_system/市场经营_合同管理_创建.png)

---

#### 5. 客户管理
![客户管理](images/front_system/市场经营_客户管理.png)
- **页面说明**：管理客户信息，包括客户的新增、查询、详情等。

---

#### 6. 我的草稿
![我的草稿](images/front_system/市场经营_我的草稿.png)
- **页面说明**：集中管理市场经营模块下的所有草稿。

---

#### 7. 帮助中心
![帮助中心](images/front_system/市场经营_帮助中心.png)
- **页面说明**：提供市场经营相关的操作手册和帮助文档。

---

#### 8. 里程碑管理
![里程碑管理](images/front_system/市场经营_里程碑管理.png)
- **页面说明**：管理市场项目的里程碑。

---

#### 9. 经营报表
![合同报表](images/front_system/市场经营_合同报表.png)
![报价报表](images/front_system/市场经营_报价报表.png)
![里程碑报表](images/front_system/市场经营_里程碑报表.png)
![需求报表](images/front_system/市场经营_需求报表.png)
- **页面说明**：提供合同、报价、里程碑、需求等多维度报表。

---

#### 10. 主表
![主表](images/front_system/市场经营_线索池_全部线索.png)
- **页面说明**：市场经营相关的主数据管理页面。

---

## 3.3 采购管理
![采购管理](seq/procurement-board.png)
**页面说明：**
- 展示采购供应指标、采购项目、供应商管理、采购预警等内容。
- 提供采购合同、采购项目、供应商等数据的统计与分析。

**后台API调用（推测）**：
- GET /api/procurement/overview
- GET /api/procurement/projects
- GET /api/procurement/suppliers

### 采购管理功能子页面明细

#### 1. 采购管理一览
![采购管理一览](images/front_system/采购管理_采购供应指标.png)
- **页面说明**：展示采购管理的整体供应指标。

![采购供应指标看板](images/front_system/采购管理_采购供应指标看板.png)

---

#### 2. 采购项目管理
![采购项目管理](images/front_system/采购管理_采购项目实施.png)
- **页面说明**：管理所有采购项目，包括项目实施、立项申请等。

![采购立项申请](images/front_system/采购管理_采购立项申请.png)

---

#### 3. 供应商管理
![供应商管理](images/front_system/采购管理_供应商管理_技术配置合同管理.png)
- **页面说明**：管理供应商信息，包括技术配置合同管理、潜在供应商、合格供应商等。

![潜在供应商](images/front_system/采购管理_潜在供应商.png)
![公司级合格供应商](images/front_system/采购管理_供应商信息报表_公司级合格供应商.png)

---

#### 4. 采购预警
![采购预警](images/front_system/采购管理_不良行为供应商.png)
- **页面说明**：展示采购过程中的不良行为供应商等预警信息。

---

#### 5. 采购合同管理
![采购合同主列表](images/front_system/采购管理_采购合同执行_合同主列表.png)
- **页面说明**：管理采购合同，包括合同主列表、总价合同列表、框架合同等。

![采购合同总价列表](images/front_system/采购管理_采购合同执行_总价合同列表.png)
![采购框架合同列表](images/front_system/采购管理_框架合同列表.png)

---

#### 6. 采购报表
![供应商信息报表-资审中供应商](images/front_system/采购管理_供应商信息报表_资审中供应商.png)
- **页面说明**：提供采购相关的多维度报表。

---

#### 7. 采购主表
![采购主表](images/front_system/采购管理_采购供应指标.png)
- **页面说明**：采购管理相关的主数据管理页面。

---

## 3.4 生产服务
![生产服务](seq/production-board.png)
**页面说明：**
- 展示生产领域关键指标、日常作业、大修管理、人员管理、物资管理等模块。
- 提供生产任务、绩效、资源调配等功能。

**后台API调用（推测）**：
- GET /api/production/overview
- GET /api/production/tasks
- GET /api/production/resources

## 3.5 功能结构图

```mermaid
graph TD
  首页[系统主页]
  首页 --> 个人门户
  首页 --> 待办
  首页 --> 已办
  首页 --> 发起
  首页 --> 参与
  首页 --> 待阅
  首页 --> 已阅
  首页 --> 日常任务
  首页 --> 办公助手
  办公助手 --> OA办公
  办公助手 --> 安质环系统
  办公助手 --> 考勤系统
  办公助手 --> IDOC
  办公助手 --> 商旅平台
  办公助手 --> 行政后勤
  办公助手 --> PLM
  办公助手 --> 中广核商城
  办公助手 --> Ue系统
  办公助手 --> UPM
  办公助手 --> FIS
  办公助手 --> EPM
  办公助手 --> 任务督办
  办公助手 --> LIMS
  首页 --> 战略计划
  首页 --> 市场经营
  首页 --> 履约执行
  首页 --> 采购管理
  首页 --> 生产服务
```

**说明：**
- 功能结构图基于实际首页菜单与快捷入口，展示了系统主页的主要功能分区及下属模块。
- 办公助手下包含多个常用业务系统的快捷入口。

---

## 3.6 用例图

```mermaid
%% 用例图
flowchart TD
  User1(普通职员)
  User2(部门领导)
  System(系统主页)
  User1 -- 登录/选择组织/查看任务/发起流程/访问办公助手/查看数据 --> System
  User2 -- 登录/选择组织/审批流程/查看报表/管理下属/访问办公助手 --> System
```

**说明：**
- 普通职员可进行日常办公、流程发起、任务处理、访问各类业务系统等操作。
- 部门领导除具备普通职员权限外，还可审批流程、查看部门报表、管理下属。
- 所有操作均以系统主页为统一入口。

---

## 4. 用户操作时序图
```mermaid
sequenceDiagram
    participant 用户
    participant 登录页
    participant 后台API
    participant 组织选择页
    participant 主页
    用户->>登录页: 输入账号/密码，点击登录
    登录页->>后台API: POST /api/auth/login
    后台API-->>登录页: 返回登录结果
    登录页->>组织选择页: 跳转
    组织选择页->>后台API: GET /api/org/list
    用户->>组织选择页: 选择组织，点击确定
    组织选择页->>后台API: POST /api/org/select
    组织选择页->>主页: 跳转
    主页->>后台API: GET /api/user/home
    主页->>后台API: GET /api/task/list
    主页->>后台API: GET /api/shortcut/list
```

---

## 5. 组织结构图

```mermaid
graph TD
  A[公司] --> B[管理层]
  A --> C[市场部]
  A --> D[采购部]
  A --> E[生产部]
  A --> F[研发部]
  B --> B1[总经理]
  B --> B2[副总经理]
  C --> C1[销售组]
  C --> C2[客户关系组]
  D --> D1[采购组]
  D --> D2[供应商管理组]
  E --> E1[生产计划组]
  E --> E2[质量管理组]
  F --> F1[产品开发组]
  F --> F2[技术支持组]
```

**说明：**
- 该组织结构图为典型制造型企业的主要部门与层级示意。
- 实际组织结构可根据企业实际情况调整。

---

## 6. 用例图

```mermaid
%% 用例图
flowchart TD
  User1(普通职员)
  User2(部门领导)
  System(精益管理一体化平台)
  User1 -- 登录/选择组织/查看任务/发起流程/查看数据 --> System
  User2 -- 登录/选择组织/审批流程/查看报表/管理下属 --> System
```

**说明：**
- 普通职员可进行日常办公、流程发起、任务处理等操作。
- 部门领导除具备普通职员权限外，还可审批流程、查看部门报表、管理下属。
- 系统为所有用户提供统一入口和数据支撑。

---

> 注：API接口为推测，具体以实际接口为准。
=======
# 精益管理一体化平台-员工访问流程说明

## 1. 登录页面
![登录页面](seq/login-page.png)
**页面说明：**
- 用户输入用户名和密码，点击"立即登录"按钮。
- 支持记住用户名、忘记密码等功能。

**后台API调用（抓包推测）**：
- POST /api/auth/login
  - 说明：提交账号和密码，进行身份认证。

---

## 2. 选择组织
![选择组织](seq/select-org.png)
**页面说明：**
- 登录后弹出组织选择框，用户需选择所属组织。
- 选择后点击"确定"进入系统主页。

**后台API调用（抓包推测）**：
- GET /api/org/list
  - 说明：获取可选组织列表。
- POST /api/org/select
  - 说明：提交用户选择的组织。

---

## 3. 系统主页
![系统主页](seq/home-page.png)
**页面说明：**
- 展示个人门户、待办、已办、发起、参与、待阅、已阅等模块。
- 提供办公助手、考勤、OA、PLM等快捷入口。
- 顶部显示当前登录用户信息。

**后台API调用（抓包推测）**：
- GET /api/user/home
  - 说明：获取首页数据和用户信息。
- GET /api/task/list
  - 说明：获取待办、已办等任务数据。
- GET /api/shortcut/list
  - 说明：获取办公助手快捷入口。

## 3.1 战略计划
![战略计划](seq/plan-board.png)
**页面说明：**
- 展示公司战略计划相关的任务、进度、全景图等信息。
- 提供战略目标、年度经营计划、绩效考核、风险管理等功能入口。

**后台API调用（推测）**：
- GET /api/plan/overview
- GET /api/plan/tasks
- GET /api/plan/performance

---

## 3.2 市场经营
![市场经营](seq/market-board.png)
**页面说明：**
- 展示市场经营一览、线索管理、需求管理、报价管理、合同管理、客户管理等模块。
- 提供市场数据分析、经营报表等功能。

**后台API调用（推测）**：
- GET /api/market/overview
- GET /api/market/leads
- GET /api/market/contracts

### 市场经营功能子页面明细

#### 1. 市场经营一览
![市场经营一览](images/front_system/市场经营_线索池_全部线索.png)
- **页面说明**：展示市场经营线索池、整体线索分布和经营总览。

---

#### 2. 需求管理
![需求管理](images/front_system/市场经营_需求管理.png)
- **页面说明**：管理市场需求，包括需求的创建、查询、附件、基本信息等。

![需求管理-创建需求](images/front_system/市场经营_需求管理_创建需求.png)
![需求管理-基本信息](images/front_system/市场经营_需求管理_基本信息.png)

---

#### 3. 报价管理
![报价管理](images/front_system/市场经营_报价管理.png)
- **页面说明**：管理所有市场报价，包括报价的创建、基本信息、双方签约主体信息等。

![报价管理-创建报价](images/front_system/市场经营_报价管理_创建报价.png)
![报价管理-基本信息](images/front_system/市场经营_报价管理_基本信息.png)

---

#### 4. 合同管理
![合同管理](images/front_system/市场经营_合同管理.png)
- **页面说明**：管理市场合同，包括合同的创建、合同详情等。

![合同管理-创建](images/front_system/市场经营_合同管理_创建.png)

---

#### 5. 客户管理
![客户管理](images/front_system/市场经营_客户管理.png)
- **页面说明**：管理客户信息，包括客户的新增、查询、详情等。

---

#### 6. 我的草稿
![我的草稿](images/front_system/市场经营_我的草稿.png)
- **页面说明**：集中管理市场经营模块下的所有草稿。

---

#### 7. 帮助中心
![帮助中心](images/front_system/市场经营_帮助中心.png)
- **页面说明**：提供市场经营相关的操作手册和帮助文档。

---

#### 8. 里程碑管理
![里程碑管理](images/front_system/市场经营_里程碑管理.png)
- **页面说明**：管理市场项目的里程碑。

---

#### 9. 经营报表
![合同报表](images/front_system/市场经营_合同报表.png)
![报价报表](images/front_system/市场经营_报价报表.png)
![里程碑报表](images/front_system/市场经营_里程碑报表.png)
![需求报表](images/front_system/市场经营_需求报表.png)
- **页面说明**：提供合同、报价、里程碑、需求等多维度报表。

---

#### 10. 主表
![主表](images/front_system/市场经营_线索池_全部线索.png)
- **页面说明**：市场经营相关的主数据管理页面。

---

## 3.3 采购管理
![采购管理](seq/procurement-board.png)
**页面说明：**
- 展示采购供应指标、采购项目、供应商管理、采购预警等内容。
- 提供采购合同、采购项目、供应商等数据的统计与分析。

**后台API调用（推测）**：
- GET /api/procurement/overview
- GET /api/procurement/projects
- GET /api/procurement/suppliers

### 采购管理功能子页面明细

#### 1. 采购管理一览
![采购管理一览](images/front_system/采购管理_采购供应指标.png)
- **页面说明**：展示采购管理的整体供应指标。

![采购供应指标看板](images/front_system/采购管理_采购供应指标看板.png)

---

#### 2. 采购项目管理
![采购项目管理](images/front_system/采购管理_采购项目实施.png)
- **页面说明**：管理所有采购项目，包括项目实施、立项申请等。

![采购立项申请](images/front_system/采购管理_采购立项申请.png)

---

#### 3. 供应商管理
![供应商管理](images/front_system/采购管理_供应商管理_技术配置合同管理.png)
- **页面说明**：管理供应商信息，包括技术配置合同管理、潜在供应商、合格供应商等。

![潜在供应商](images/front_system/采购管理_潜在供应商.png)
![公司级合格供应商](images/front_system/采购管理_供应商信息报表_公司级合格供应商.png)

---

#### 4. 采购预警
![采购预警](images/front_system/采购管理_不良行为供应商.png)
- **页面说明**：展示采购过程中的不良行为供应商等预警信息。

---

#### 5. 采购合同管理
![采购合同主列表](images/front_system/采购管理_采购合同执行_合同主列表.png)
- **页面说明**：管理采购合同，包括合同主列表、总价合同列表、框架合同等。

![采购合同总价列表](images/front_system/采购管理_采购合同执行_总价合同列表.png)
![采购框架合同列表](images/front_system/采购管理_框架合同列表.png)

---

#### 6. 采购报表
![供应商信息报表-资审中供应商](images/front_system/采购管理_供应商信息报表_资审中供应商.png)
- **页面说明**：提供采购相关的多维度报表。

---

#### 7. 采购主表
![采购主表](images/front_system/采购管理_采购供应指标.png)
- **页面说明**：采购管理相关的主数据管理页面。

---

## 3.4 生产服务
![生产服务](seq/production-board.png)
**页面说明：**
- 展示生产领域关键指标、日常作业、大修管理、人员管理、物资管理等模块。
- 提供生产任务、绩效、资源调配等功能。

**后台API调用（推测）**：
- GET /api/production/overview
- GET /api/production/tasks
- GET /api/production/resources

## 3.5 功能结构图

```mermaid
graph TD
  首页[系统主页]
  首页 --> 个人门户
  首页 --> 待办
  首页 --> 已办
  首页 --> 发起
  首页 --> 参与
  首页 --> 待阅
  首页 --> 已阅
  首页 --> 日常任务
  首页 --> 办公助手
  办公助手 --> OA办公
  办公助手 --> 安质环系统
  办公助手 --> 考勤系统
  办公助手 --> IDOC
  办公助手 --> 商旅平台
  办公助手 --> 行政后勤
  办公助手 --> PLM
  办公助手 --> 中广核商城
  办公助手 --> Ue系统
  办公助手 --> UPM
  办公助手 --> FIS
  办公助手 --> EPM
  办公助手 --> 任务督办
  办公助手 --> LIMS
  首页 --> 战略计划
  首页 --> 市场经营
  首页 --> 履约执行
  首页 --> 采购管理
  首页 --> 生产服务
```

**说明：**
- 功能结构图基于实际首页菜单与快捷入口，展示了系统主页的主要功能分区及下属模块。
- 办公助手下包含多个常用业务系统的快捷入口。

---

## 3.6 用例图

```mermaid
%% 用例图
flowchart TD
  User1(普通职员)
  User2(部门领导)
  System(系统主页)
  User1 -- 登录/选择组织/查看任务/发起流程/访问办公助手/查看数据 --> System
  User2 -- 登录/选择组织/审批流程/查看报表/管理下属/访问办公助手 --> System
```

**说明：**
- 普通职员可进行日常办公、流程发起、任务处理、访问各类业务系统等操作。
- 部门领导除具备普通职员权限外，还可审批流程、查看部门报表、管理下属。
- 所有操作均以系统主页为统一入口。

---

## 4. 用户操作时序图
```mermaid
sequenceDiagram
    participant 用户
    participant 登录页
    participant 后台API
    participant 组织选择页
    participant 主页
    用户->>登录页: 输入账号/密码，点击登录
    登录页->>后台API: POST /api/auth/login
    后台API-->>登录页: 返回登录结果
    登录页->>组织选择页: 跳转
    组织选择页->>后台API: GET /api/org/list
    用户->>组织选择页: 选择组织，点击确定
    组织选择页->>后台API: POST /api/org/select
    组织选择页->>主页: 跳转
    主页->>后台API: GET /api/user/home
    主页->>后台API: GET /api/task/list
    主页->>后台API: GET /api/shortcut/list
```

---

## 5. 组织结构图

```mermaid
graph TD
  A[公司] --> B[管理层]
  A --> C[市场部]
  A --> D[采购部]
  A --> E[生产部]
  A --> F[研发部]
  B --> B1[总经理]
  B --> B2[副总经理]
  C --> C1[销售组]
  C --> C2[客户关系组]
  D --> D1[采购组]
  D --> D2[供应商管理组]
  E --> E1[生产计划组]
  E --> E2[质量管理组]
  F --> F1[产品开发组]
  F --> F2[技术支持组]
```

**说明：**
- 该组织结构图为典型制造型企业的主要部门与层级示意。
- 实际组织结构可根据企业实际情况调整。

---

## 6. 用例图

```mermaid
%% 用例图
flowchart TD
  User1(普通职员)
  User2(部门领导)
  System(精益管理一体化平台)
  User1 -- 登录/选择组织/查看任务/发起流程/查看数据 --> System
  User2 -- 登录/选择组织/审批流程/查看报表/管理下属 --> System
```

**说明：**
- 普通职员可进行日常办公、流程发起、任务处理等操作。
- 部门领导除具备普通职员权限外，还可审批流程、查看部门报表、管理下属。
- 系统为所有用户提供统一入口和数据支撑。

---

> 注：API接口为推测，具体以实际接口为准。
>>>>>>> fcf168aff39a310b2d11d6ddb32fea5602b814c5
