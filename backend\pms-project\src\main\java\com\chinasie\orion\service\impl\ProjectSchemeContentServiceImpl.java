package com.chinasie.orion.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.bo.LyraFileBO;
import com.chinasie.orion.constant.FileConstant;
import com.chinasie.orion.domain.dto.ProjectSchemeContentBatchDTO;
import com.chinasie.orion.domain.dto.ProjectSchemeContentDTO;
import com.chinasie.orion.domain.entity.ProjectSchemeContent;
import com.chinasie.orion.domain.vo.ProjectSchemeContentVO;
import com.chinasie.orion.feign.MessageCenterApi;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.ProjectSchemeContentRepository;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.service.ProjectSchemeContentService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.eclipse.jetty.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ProjectSchemeContentServiceImpl
 *
 * @author: yangFy
 * @date: 2023/4/19 16:11
 * @description:
 * <p>
 * 项目计划记录，业务处理层实现impl
 *
 * </p>
 */
@Service
public class ProjectSchemeContentServiceImpl extends OrionBaseServiceImpl<ProjectSchemeContentRepository, ProjectSchemeContent> implements ProjectSchemeContentService {

    @Autowired
    private ClassRedisHelper classRedisHelper;

    @Resource
    private LyraFileBO fileBo;

    @Resource
    private MessageCenterApi messageCenterApi;

    /**
     * 添加计划记录（批量）
     *
     * @param projectSchemeContentBatchDTO
     * @return
     * @throws Exception
     */
    @Transactional
    @Override
    public List<String> createBatch(ProjectSchemeContentBatchDTO projectSchemeContentBatchDTO) throws Exception {
        List<ProjectSchemeContentDTO> contentDTOS = projectSchemeContentBatchDTO.getContentDTOS();
        List<ProjectSchemeContent> list = new ArrayList<>();
        for (ProjectSchemeContentDTO projectSchemeDTO : contentDTOS) {
            ProjectSchemeContent schemeContent = BeanCopyUtils.convertTo(projectSchemeDTO, () -> new ProjectSchemeContent());
            schemeContent.setId(classRedisHelper.getUUID(ProjectSchemeContent.class.getSimpleName()));
            list.add(schemeContent);
        }

        this.saveBatch(list);


        List<FileDTO> attachments = Optional.ofNullable(projectSchemeContentBatchDTO.getAttachments()).orElse(new ArrayList<>());
        List<FileDTO> oldFilesResult = fileBo.listMaxNewFile(contentDTOS.get(0).getProjectSchemeId(), FileConstant.FILETYPE_PROJECT_SCHEME_RECORD);
        if (BeanUtil.isNotEmpty(oldFilesResult) && CollectionUtil.isNotEmpty(oldFilesResult)) {
            List<String> oldFileIds = oldFilesResult.stream()
                    .map(FileDTO::getId)
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(oldFileIds)) {
                fileBo.deleteFileByIds(oldFileIds);
            }
        }
        attachments.forEach(f -> {
            f.setDataId(contentDTOS.get(0).getProjectSchemeId());
            f.setDataType(FileConstant.FILETYPE_PROJECT_SCHEME_RECORD);
            f.setId(null);
        });
        if (CollectionUtil.isNotEmpty(attachments)) {
            fileBo.addBatch(attachments);
        }
        //项目计划反馈提醒代办消失
        List<String> businessIdList = contentDTOS.stream().map(m -> String.format("feedback_%s", m.getProjectSchemeId())).distinct().collect(Collectors.toList());
        messageCenterApi.todoMessageChangeStatusByBusinessIds(businessIdList);

        return list.stream().map(ProjectSchemeContent::getId).collect(Collectors.toList());
    }


    /**
     * 删除（批量）
     *
     * @param ids
     * @return
     * @throws Exception
     */
    @Transactional
    @Override
    public Boolean deleteByIds(List<String> ids) throws Exception {
        return this.removeBatchByIds(ids);
    }

    @Override
    public List<ProjectSchemeContentVO> getList(String id) throws Exception {
        List<ProjectSchemeContentVO> schemeVO = new ArrayList();
        List<ProjectSchemeContent> contents = this.list(new LambdaQueryWrapper<>(ProjectSchemeContent.class).eq(ProjectSchemeContent::getProjectSchemeId, id));
        if (CollectionUtil.isNotEmpty(contents)) {
            schemeVO = BeanCopyUtils.convertListTo(contents, ProjectSchemeContentVO::new);
        }
        return schemeVO;
    }

    @Override
    public Map<String, String> getMapBySchemeIds(List<String> schemeIds) {
        List<ProjectSchemeContent> contents = this.list(
                new LambdaQueryWrapper<>(ProjectSchemeContent.class).in(ProjectSchemeContent::getProjectSchemeId, schemeIds)
                        .select(ProjectSchemeContent::getProjectSchemeId, ProjectSchemeContent::getContent));
        if (CollectionUtil.isEmpty(contents)) {
            return new HashMap<>();
        }
        return  contents.stream().filter(c -> StringUtil.isNotBlank(c.getContent()))
                .collect(Collectors.groupingBy(
                ProjectSchemeContent::getProjectSchemeId,
                Collectors.mapping(
                        ProjectSchemeContent::getContent,
                        Collectors.joining("\n\t") // 使用换行符和制表符拼接
                )
        ));
    }
}
