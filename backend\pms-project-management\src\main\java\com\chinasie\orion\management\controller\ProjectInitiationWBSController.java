package com.chinasie.orion.management.controller;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.dto.ProjectInitiationWBSDTO;
import com.chinasie.orion.management.domain.vo.ProjectInitiationWBSVO;
import com.chinasie.orion.management.service.ProjectInitiationWBSService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * ProjectInitiationWBS 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-16 14:52:10
 */
@RestController
@RequestMapping("/projectInitiationWBS")
@Api(tags = "项目立项WBS预算")
public class ProjectInitiationWBSController {

    @Autowired
    private ProjectInitiationWBSService projectInitiationWBSService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "项目立项WBS预算", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectInitiationWBSVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        ProjectInitiationWBSVO rsp = projectInitiationWBSService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 根据立项编号查询
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "根据立项编号查询")
    @RequestMapping(value = "/getByProjectNumber", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】根据立项编号查询", type = "项目立项WBS预算", subType = "根据立项编号查询", bizNo = "{{#projectInitiationWBSDTO.projectNumber}}")
    public ResponseDTO<List<ProjectInitiationWBSVO>> getByProjectNumber(@RequestBody ProjectInitiationWBSDTO projectInitiationWBSDTO) throws Exception {
        List<ProjectInitiationWBSVO> rsp = projectInitiationWBSService.getByProjectNumber(projectInitiationWBSDTO.getProjectNumber());
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectInitiationWBSDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#projectInitiationWBSDTO.name}}】", type = "项目立项WBS预算", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody ProjectInitiationWBSDTO projectInitiationWBSDTO) throws Exception {
        String rsp = projectInitiationWBSService.create(projectInitiationWBSDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectInitiationWBSDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#projectInitiationWBSDTO.name}}】", type = "项目立项WBS预算", subType = "编辑", bizNo = "{{#projectInitiationWBSDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody ProjectInitiationWBSDTO projectInitiationWBSDTO) throws Exception {
        Boolean rsp = projectInitiationWBSService.edit(projectInitiationWBSDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "项目立项WBS预算", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = projectInitiationWBSService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "项目立项WBS预算", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectInitiationWBSService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "项目立项WBS预算", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ProjectInitiationWBSVO>> pages(@RequestBody Page<ProjectInitiationWBSDTO> pageRequest) throws Exception {
        Page<ProjectInitiationWBSVO> rsp = projectInitiationWBSService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("项目立项WBS预算导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "项目立项WBS预算", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        projectInitiationWBSService.downloadExcelTpl(response);
    }

    @ApiOperation("项目立项WBS预算导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "项目立项WBS预算", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = projectInitiationWBSService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("项目立项WBS预算导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "项目立项WBS预算", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = projectInitiationWBSService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消项目立项WBS预算导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "项目立项WBS预算", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = projectInitiationWBSService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("项目立项WBS预算导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "项目立项WBS预算", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        projectInitiationWBSService.exportByExcel(searchConditions, response);
    }
}
