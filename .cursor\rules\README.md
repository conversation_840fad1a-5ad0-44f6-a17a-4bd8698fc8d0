# Cursor企业级开发规则说明

本目录包含了针对企业级Java后端和Vue前端项目开发的Cursor规则模板，这些模板可以帮助开发团队更高效地使用Cursor进行AI辅助编码，提高开发效率和代码质量。

## 文件说明

1. **java-backend-rules.md** - Java后端开发规则模板
   - Spring Boot项目生成
   - 控制器层、服务层、数据访问层代码生成
   - 高级应用场景（分布式事务、缓存策略等）
   - 代码质量规范（异常处理、日志等）
   - 性能优化指南
   - 安全最佳实践

2. **vue-frontend-rules.md** - Vue前端开发规则模板
   - Vue3组件生成
   - API集成实现
   - 表单与列表组件生成
   - 高级应用场景（微前端、状态管理等）
   - 代码质量规范
   - 性能优化指南
   - 安全最佳实践

3. **enterprise-integration-rules.md** - 企业级集成开发规则模板
   - 全栈项目架构设计
   - CICD流水线设计
   - 数据库设计与集成
   - 前后端集成最佳实践
   - 企业级共享组件
   - AI增强开发实践

4. **prompt-engineering-templates.md** - 通用提示工程模板
   - 角色与任务定义模板
   - 代码生成模板
   - 文档生成模板
   - 测试相关模板
   - 开发效率模板

5. **config.json** - 规则配置文件
   - 规则与文件类型的关联配置
   - 自动建议设置
   - 优先级设置

6. **auto-suggest.js** - 自动建议脚本
   - 根据上下文匹配规则
   - 规则应用逻辑

## 使用方法

### 手动使用

1. **查找合适的规则**: 根据当前开发任务，从对应的规则文件中查找最接近的模板
2. **复制模板**: 将选中的模板复制到Cursor对话框中
3. **调整模板**: 根据实际项目需求，调整模板中的占位符和具体要求
4. **提交请求**: 提交给Cursor AI处理

### 自动应用

规则可以根据当前上下文自动应用，无需手动查找和复制：

1. **自动建议**: 在编辑特定类型文件或使用特定关键词时，系统会自动建议相关规则
2. **关键词触发**: 使用规则中定义的触发词，可以自动调用相应的规则模板
3. **上下文感知**: 系统会根据当前打开的文件类型，自动匹配最合适的规则

例如：
- 编辑Java文件时，自动应用Java后端开发规则
- 编辑Vue文件时，自动应用Vue前端开发规则
- 使用"生成控制器"等关键词时，自动应用相应的模板

## 配置说明

`config.json`文件包含以下配置项：

- **rules**: 规则列表，每条规则包含：
  - **name**: 规则名称
  - **file**: 规则文件路径
  - **filePatterns**: 匹配的文件模式（使用glob语法）
  - **triggers**: 触发关键词

- **settings**: 全局设置
  - **autoSuggest**: 是否启用自动建议（true/false）
  - **autoApply**: 是否自动应用建议（true/false）
  - **contextWindow**: 上下文窗口大小
  - **priorityOrder**: 规则优先级顺序

可以根据团队需求调整这些配置。

## 最佳实践

1. **提供足够上下文**: 在使用模板时，尽可能提供详细的业务上下文和技术要求
2. **分步骤请求**: 对于复杂任务，将其分解为多个小步骤，逐步引导AI完成
3. **迭代优化**: 对AI生成的结果进行审查和调整，通过多轮对话优化输出
4. **团队共享**: 鼓励团队成员分享有效的提示模板和使用技巧
5. **持续更新**: 根据项目经验，不断更新和完善规则模板
6. **自定义触发词**: 为团队中常用的术语设置触发词，使规则更容易被激活

## 规则维护

项目团队可以根据实际开发需求和经验，持续更新和扩展这些规则：

1. 添加新的模板类别
2. 完善现有模板内容
3. 记录成功的提示模式
4. 分享最佳实践案例
5. 更新配置和触发词

维护步骤：
1. 编辑相应的规则文件（.md文件）
2. 更新`config.json`中的配置
3. 如需更改自动建议逻辑，编辑`auto-suggest.js`

## 注意事项

1. 模板中的占位符需要替换为实际项目信息
2. 生成的代码需要经过人工审查和测试
3. 敏感信息不要包含在提示中
4. 遵循公司安全和合规政策
5. 自动建议仅作为辅助，最终决策仍需人工判断
6. 确保规则模板中的技术栈与项目保持一致 