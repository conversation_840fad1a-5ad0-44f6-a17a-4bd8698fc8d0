package com.chinasie.orion.domain.dto.approval.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * ProjectApprovalMilestone Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-07 16:23:23
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ExcelIgnoreUnannotated
public class ProjectApprovalMilestoneExportDTO implements Serializable{

    @ExcelProperty(value = "名称", index = 0)
    private String name;
    @ExcelProperty(value = "计划类型", index = 1)
    private String schemeType;
    @ExcelProperty(value = "计划活动项", index = 2)
    private String schemeActivity;
    @ExcelProperty(value = "计划状态", index = 3)
    private String schemeStatus;
    @ExcelProperty(value = "计划开始时间", index = 4)
    private String beginTime;
    @ExcelProperty(value = "工期", index = 5)
    private Integer durationDays;
    @ExcelProperty(value = "计划结束时间", index = 6)
    private String endTime;
//    @ExcelProperty(value = "负责人", index = 7)
//    private String resPerson;
//    @ExcelProperty(value = "责任部门", index = 8)
//    private String resDept;
//    @ExcelProperty(value = "责任科室（责任处室）", index = 9)
//    private String rspSectionId;
//    @ExcelProperty(value = "参与人", index = 10)
//    private String participantUsers;
    @ExcelProperty(value = "计划描述", index = 7)
    private String schemeDesc;
    @ExcelProperty(value = "创建人名字", index = 8)
    private String creatorName;
}

