package com.chinasie.orion.handler.status;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.chinasie.orion.amqp.entity.ChangeStatusMessageDTO;
import com.chinasie.orion.amqp.handler.AbstractChangeStatusReceiver;
import com.chinasie.orion.bo.DocumentBo;
import com.chinasie.orion.constant.GoodsStatusConstant;
import com.chinasie.orion.domain.entity.GoodsServicePlan;
import com.chinasie.orion.domain.entity.GoodsServiceStore;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.repository.GoodsServicePlanMapper;
import com.chinasie.orion.repository.GoodsServiceStoreMapper;
import com.chinasie.orion.sdk.domain.vo.business.ClassVO;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.util.IdUtils;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * 物资服务入库状态变更
 */
@Component
public class GoodsServiceStoreStatusReceiver extends AbstractChangeStatusReceiver {

    private final Logger logger = LoggerFactory.getLogger(DeliverableChangeStatusReceiver.class);


    private static final String CURRENT_CLASS = "GoodsServiceStore";

    @Resource
    private GoodsServiceStoreMapper goodsServiceStoreMapper;

    @Resource
    private GoodsServicePlanMapper goodsServicePlanMapper;

    @Resource
    private ClassRedisHelper classRedisHelper;



    @Override
    protected void process(ChangeStatusMessageDTO msg, Channel channel, Message message) {
        logger.info("物资服务入库状态变更消费：{}", msg);
        if (ObjectUtil.isNotEmpty(msg)) {
            ClassVO classVO = classRedisHelper.classInfo(IdUtils.getCode(msg.getBusinessId()));
            if (Objects.nonNull(classVO)) {
                if (CURRENT_CLASS.equals(classVO.getClassName())) {
                    msg.setClassName(classVO.getClassName());
                    ThreadUtil.execAsync(() -> {
                        try {
                            consumerCreateMessage(msg);
                        } catch (Exception e) {
                            processError(msg, channel, message, e);
                        }
                    });
                }
            }
        }
    }

    @Override
    protected void processError(ChangeStatusMessageDTO msg, Channel channel, Message message, Exception ex) {
        logger.error("物资服务入库状态变更异常，【{}】,message，【{}】,", JSONUtil.toJsonStr(msg), message, ex);
    }


    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = CURRENT_CLASS, durable = "true", autoDelete = "false"),
            exchange = @Exchange(value = "${orion.amqp.change-status-v2.exchange}", type = ExchangeTypes.DIRECT),
            key = CURRENT_CLASS
    ))
    @Override
    public void receiver(ChangeStatusMessageDTO msg, Channel channel, Message message) throws IOException {
        super.receiver(msg, channel, message);
    }

    /**
     * 消费消息
     *
     * @param message 消息
     */
    private void consumerCreateMessage(ChangeStatusMessageDTO message) throws Exception {
        //交付物 状态变更
        Integer status = message.getStatus();
        String businessId = message.getBusinessId();
        GoodsServiceStore goodsServiceStore = goodsServiceStoreMapper.selectById(businessId);
        goodsServiceStore.setStatus(status);
        goodsServiceStoreMapper.updateById(goodsServiceStore);
        // 入库审核完毕 返回去修改物资计划的值
        String goodsServiceNumber = goodsServiceStore.getGoodsServiceNumber();
        String projectId = goodsServiceStore.getProjectId();
        List<GoodsServicePlan> goodsServicePlans = goodsServicePlanMapper
                .selectList(new LambdaQueryWrapperX<GoodsServicePlan>()
                        .eq(GoodsServicePlan::getGoodsServiceNumber, goodsServiceNumber)
                        .eq(GoodsServicePlan::getProjectId,projectId)
                        .in(GoodsServicePlan::getStatus,GoodsStatusConstant.P_OVER_AUDITED,GoodsStatusConstant.P_OVER_STORAGE));
        if (BeanUtil.isNotEmpty(goodsServicePlans)){
            GoodsServicePlan goodsServicePlan = goodsServicePlans.get(0);
            goodsServicePlan.setStatus(GoodsStatusConstant.P_OVER_STORAGE);
            goodsServicePlan.setTotalStoreAmount(goodsServiceStore.getTotalStoreAmount());
            goodsServicePlanMapper.updateById(goodsServicePlan);
        }
        logger.info("物资服务入库状态消费成功-参数:{}-结果:{}", JSONUtil.toJsonStr(message), true);
    }

}
