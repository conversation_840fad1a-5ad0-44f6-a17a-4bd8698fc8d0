package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONUtil;
import com.chinasie.orion.amqp.entity.ChangeStatusMessageDTO;
import com.chinasie.orion.constant.IdeaFormStatusEnum;
import com.chinasie.orion.constant.RelationClassNameConstant;
import com.chinasie.orion.conts.MsgBusinessTypeEnum;
import com.chinasie.orion.domain.dto.SchemeMsgDTO;
import com.chinasie.orion.domain.entity.IdeaForm;
import com.chinasie.orion.domain.entity.InterfaceManagement;
import com.chinasie.orion.manager.SendMessageManager;
import com.chinasie.orion.service.IdeaFormService;
import com.chinasie.orion.service.InterfaceManagementService;
import com.chinasie.orion.service.InterfaceStatusUpdateService;
import com.chinasie.orion.util.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/01/30/19:19
 * @description:
 */
@Service
@Slf4j
public class InterfaceStatusUpdateServiceImpl  implements InterfaceStatusUpdateService {



    private InterfaceManagementService interfaceManagementService;

    private IdeaFormService ideaFormService;

    @Autowired
    public void setInterfaceManagementService(InterfaceManagementService interfaceManagementService) {
        this.interfaceManagementService = interfaceManagementService;
    }
    @Autowired
    public void setIdeaFormService(IdeaFormService ideaFormService) {
        this.ideaFormService = ideaFormService;
    }

    private SendMessageManager sendMessageManager;

    @Autowired
    public void setSendMessageManager(SendMessageManager sendMessageManager) {
        this.sendMessageManager = sendMessageManager;
    }

    @Override
    public boolean businessChange(ChangeStatusMessageDTO mscMessage) throws Exception {
        log.info("BUSINESS-CHANGE--接口--状态变更数据:{}", JsonUtils.obj2String(mscMessage));
        if (mscMessage != null) {
            log.info("接口状态变更的className:{}", mscMessage.getClassName());
            Integer status = mscMessage.getStatus();
            String businessId = mscMessage.getBusinessId();
            switch (mscMessage.getClassName()) {
                case RelationClassNameConstant.INTERFACE_MANAGEMENT:
                    InterfaceManagement interfaceManagement = interfaceManagementService.getById(businessId);
                    interfaceManagement.setStatus(status);
                    interfaceManagementService.updateById(interfaceManagement);
                    log.info("接口-传递单状态变更成功：{}", JSONUtil.toJsonStr(interfaceManagement));
                    // 发送消息
                    List<String> cooperationUsers = Arrays.stream(interfaceManagement.getCooperationUsers().split(",")).collect(Collectors.toList());
                    cooperationUsers.add(interfaceManagement.getManUser());
                    sendMessageManager.sendMsg(MsgBusinessTypeEnum.INTERFACE_MANAGEMENT,
                            SchemeMsgDTO.builder()
                                    .extParam(
                                            new HashMap<>() {{
                                                put("id", businessId);
                                                put("name", interfaceManagement.getDesc());
                                                put("flowType","接口审批完成");
                                            }}
                                    )
                            .recipientIds(cooperationUsers)
                            .build());
                    break;
                case RelationClassNameConstant.IDEA_FORM:
                    IdeaForm ideaForm = ideaFormService.getById(businessId);
                    ideaForm.setStatus(status);
                    ideaFormService.updateById(ideaForm);
                    log.info("接口-意见单状态变更成功：{}", JSONUtil.toJsonStr(ideaForm));
                    // 发送消息
                    String manUser = ideaForm.getManUser();
                    sendMessageManager.sendMsg(MsgBusinessTypeEnum.INTERFACE_MANAGEMENT,
                            SchemeMsgDTO.builder()
                                    .extParam(
                                            new HashMap<>() {{
                                                put("id", businessId);
                                                put("name", ideaForm.getDesc());
                                                put("flowType","意见单审批完成");
                                            }}
                                    )
                                    .recipientIds(ListUtil.toList(manUser))
                                    .build());
                    break;
                default:
                    break;
            }
        }
        return true;
    }
}
