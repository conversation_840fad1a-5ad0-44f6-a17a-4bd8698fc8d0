package com.chinasie.orion.domain.vo;


import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/18/10:28
 * @description:
 */
@Data
public class PlanDetailVo extends ObjectVO {

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "计划图片路径")
    private String planImage;
    /**
     * 预计开始时间
     */
    @ApiModelProperty(value = "预计开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planPredictStartTime;

    /**
     * 计划进度
     */
    @ApiModelProperty(value = "计划进度")
    private BigDecimal schedule;

    @ApiModelProperty(value = "计划进度描述")
    private String scheduleName;

    /**
     * 父级Id
     */
    @ApiModelProperty(value = "父级Id")
    private String parentId;

    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planEndTime;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planStartTime;
    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划类型Id")
    private String planType;
    @ApiModelProperty(value = "计划类型名称")
    private String planTypeName;

    /**
     * 预计结束时间
     */
    @ApiModelProperty(value = "预计结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planPredictEndTime;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "负责人ID")
    private String principalId;

    @ApiModelProperty(value = "负责人名称")
    private String principalName;


    @ApiModelProperty(value = "预期工时(小时)")
    private BigDecimal manHour;
    /**
     * 状态对象
     */
    @ApiModelProperty(value = "状态对象")
    private DataStatusVO dataStatus;


    @ApiModelProperty(value = "优先级")
    private String priorityLevel;

    @ApiModelProperty(value = "优先级名称")
    private String priorityLevelName;

    @ApiModelProperty(value = "内部状态ID")
    private String taskStatusId;

    @ApiModelProperty(value = "状态名称")
    private String statusName;


    @ApiModelProperty(value = "参与人")
    private List<String> participant;

    @ApiModelProperty(value = "参与人名称")
    private List<String> participantName;
    /**
     * 管理节点
     */
    @ApiModelProperty(value = "管理节点")
    private String manageNode;

    /**
     * 风险项
     */
    @ApiModelProperty(value = "风险项")
    private String riskItem;

    /**
     * 进度状态
     */
    @ApiModelProperty(value = "进度状态")
    private Integer speedStatus;

    /**
     * 责任单位
     */
    @ApiModelProperty(value = "责任单位")
    private String resOrg;

    /**
     * 参与单位
     */
    @ApiModelProperty(value = "参与单位")
    private List<String> joinOrgs;
    private String joinOrg;

    /**
     * 责任科室
     */
    @ApiModelProperty(value = "责任科室")
    private String resDept;
    @ApiModelProperty(value = "责任人科室名称")
    private String resDeptName;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    private String resUser;

    @ApiModelProperty(value = "责任人名称")
    private String resUserName;

    /**
     * 参与科室
     */
    @ApiModelProperty(value = "参与科室")
    private List<String> joinDepts;
    private String joinDept;

    /**
     * 责任单位
     */
    @ApiModelProperty(value = "责任单位")
    private String resOrgName;


    /**
     * 参与单位
     */
    @ApiModelProperty(value = "参与单位")
    private String joinOrgsName;



    private String joinDeptsName;


    @ApiModelProperty(value = "计划情况")
    private Integer circumstance;

    @ApiModelProperty(value = "计划情况名称")
    private String circumstanceName;

}
