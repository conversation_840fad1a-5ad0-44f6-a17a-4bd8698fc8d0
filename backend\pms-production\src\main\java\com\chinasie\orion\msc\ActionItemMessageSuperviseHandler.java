package com.chinasie.orion.msc;

import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.domain.entity.JobManage;
import com.chinasie.orion.domain.entity.ProdActionItem;
import com.chinasie.orion.msc.api.MscBuildHandler;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.chinasie.orion.constant.MessageNodeDict.NODE_ACTION_ITEM_SUPERVISE;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/10/30/14:00
 * @description:
 */
@Component
public class ActionItemMessageSuperviseHandler implements MscBuildHandler<ProdActionItem> {
    @Override
    public SendMessageDTO buildMsc(ProdActionItem prodActionItem, Object... objects) {
        Map<String, Object> message = new HashMap<>();
        message.put("$repairRound$",prodActionItem.getRepairRound());
        List<String> rspUserIdList = (List<String>)objects[0];
        String repairId = (String) objects[1];
        // 督办发邮件文案：【大修轮次】大修，由您负责的大修行动项任务完成时限即将到期，请进入系统尽早反馈。
        SendMessageDTO sendMessageDTO = SendMessageDTO.builder()
                .messageMap(message)
                .todoType(TodoTypeDict.TODO_TYPE_TASK)
                .titleMap(message)
                .todoStatus(0)
                .businessId(prodActionItem.getId())
                .businessTypeCode(prodActionItem.getClassName())
                .businessTypeName("大修行动项")
                .todoType(TodoTypeDict.TODO_TYPE_TASK)
                .messageUrl("/pms/actionItemManage/"+prodActionItem.getId()+"?repairId="+repairId)
                .messageUrlName("大修行动项")
                .recipientIdList(rspUserIdList)
                .senderTime(new Date())
                .senderId(prodActionItem.getCreatorId())
                .orgId(prodActionItem.getOrgId())
                .platformId(prodActionItem.getPlatformId())
                .build();
        return sendMessageDTO;
    }

    @Override
    public String support() {
        return NODE_ACTION_ITEM_SUPERVISE;
    }
}
