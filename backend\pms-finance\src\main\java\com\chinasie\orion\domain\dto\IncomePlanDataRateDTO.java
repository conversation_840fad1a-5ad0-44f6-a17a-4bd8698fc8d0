package com.chinasie.orion.domain.dto;


import com.chinasie.orion.domain.entity.AdvancePaymentInformation;
import com.chinasie.orion.domain.entity.BillingAccountInformation;
import com.chinasie.orion.domain.entity.IncomePlanData;
import com.chinasie.orion.domain.entity.IncomeProvisionInformation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * IncomePlanDataRate DTO对象
 *
 * <AUTHOR>
 * @since 2024-10-18 18:50:39
 */
@ApiModel(value = "IncomePlanDataRateDTO对象", description = "多项目税率维护查看")
@Data
public class IncomePlanDataRateDTO {
    /**
     * 收入计划填报数据
     */
    @ApiModelProperty(value = "收入计划填报数据")
    private IncomePlanData incomePlanDataRate;

    /**
     * 收入计提信息
     */
    @ApiModelProperty(value = "收入计提信息")
    private List<IncomeProvisionInformation> incomeProvisionInformation;

    /**
     * 收入计提信息
     */
    @ApiModelProperty(value = "开票核算信息")
    private List<BillingAccountInformation> billingAccountInformation;

    /**
     * 预收款信息
     */
    @ApiModelProperty(value = "预收款信息")
    private List<AdvancePaymentInformation> advancePaymentInformation;
}
