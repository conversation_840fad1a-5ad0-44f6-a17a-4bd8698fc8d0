package com.chinasie.orion.management.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@ApiModel(value = "PersonManagementStaticsDTO对象", description = "招标投标统计")
@Data
public class PersonManagementStaticsDTO implements Serializable {
    /**
     * 筛选年份
     */
    @ApiModelProperty(value = "筛选年份")
    @NotNull
    private Integer filterYear;
}
