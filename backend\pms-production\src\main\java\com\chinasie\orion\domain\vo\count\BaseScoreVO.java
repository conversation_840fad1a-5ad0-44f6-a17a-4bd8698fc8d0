package com.chinasie.orion.domain.vo.count;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/13/11:34
 * @description:
 */
@Data
public class BaseScoreVO  implements Serializable {
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "值")
    private BigDecimal value;
}
