import type { AppRouteRecordRaw, AppRouteModule } from '/@/router/types';

import { PAGE_NOT_FOUND_ROUTE, REDIRECT_ROUTE } from '/@/router/routes/basic';

// import { HIDE_MENU } from '/@/router/basicHideMenu';

import { mainOutRoutes } from './mainOut';
import { PageEnum } from '/@/enums/pageEnum';
import { t } from '/@/hooks/web/useI18n';
import { LAYOUT } from '/@/router/constant';

// const modules = import.meta.globEager('./modules/**/*.ts');

const routeModuleList: AppRouteModule[] = [];

// Object.keys(modules).forEach((key) => {
//   const mod = modules[key].default || {};
//   const modList = Array.isArray(mod) ? [...mod] : [mod];
//   routeModuleList.push(...modList);
// });

export const asyncRoutes = [PAGE_NOT_FOUND_ROUTE, ...routeModuleList];

export const RootRoute: AppRouteRecordRaw = {
  path: '/',
  name: 'Root',
  redirect: PageEnum.BASE_HOME,
  meta: {
    title: 'Root',
  },
};

export const LoginRoute: AppRouteRecordRaw = {
  path: '/login',
  name: 'Login',
  component: () => import('/@/views/sys/login/Login.vue'),
  meta: {
    title: t('routes.basic.login'),
  },
};
// export const LoginAdminRoute: AppRouteRecordRaw = {
//   path: '/login-admin',
//   name: 'LoginAdmin',
//   component: () => import('/@/views/sys/login/LoginAdmin.vue'),
//   meta: {
//     title: t('routes.basic.login'),
//   },
// };

export const PageNotFoundRoute: AppRouteRecordRaw = {
  path: '/404',
  name: 'Page404',
  component: () => import('/@/views/sys/exception/Exception.vue'),
  meta: {
    title: '未找到页面',
  },
};

// Basic routing without permission
export const basicRoutes = [
  LoginRoute,
  // LoginAdminRoute,
  PageNotFoundRoute,
  RootRoute,
  ...mainOutRoutes,
  REDIRECT_ROUTE,
];
