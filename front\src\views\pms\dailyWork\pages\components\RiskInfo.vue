<script setup lang="ts">
import { openFile, OrionTable, isPower } from 'lyra-component-vue3';
import {
  h, inject, nextTick, ref, Ref,
} from 'vue';
import Api from '/@/api';
import { Popover } from 'ant-design-vue';
import { useRoute, useRouter } from 'vue-router';
import { openRiskForm } from '/@/views/pms/dailyWork/pages/utils';
import { useRiskPageConfig } from './hooks';

const route = useRoute();
const router = useRouter();
const routeName = route.name as string;
const powerCodePrefix: Ref = inject('powerCodePrefix');
const pageConfig = useRiskPageConfig(routeName);
const detailsData: Record<string, any> = inject('detailsData');
const powerData: Ref = inject('powerData');
const tableRef: Ref = ref();
const tableOptions = {
  showToolButton: false,
  showSmallSearch: false,
  rowKey: 'jobId',
  api: async () => {
    const result = await new Api(`/pms/job-manage/risk/measure/detail/${detailsData?.id}`).fetch({}, '', 'GET');
    return result ? [result] : [];
  },
  columns: [
    {
      title: '高风险',
      dataIndex: 'heightRiskName',
    },
    {
      title: '首次执行',
      dataIndex: 'firstExecuteName',
    },
    {
      title: '新人参与',
      dataIndex: 'newParticipants',
      customRender({ text }) {
        return text ? '是' : text === false ? '否' : '';
      },
    },
    {
      title: '重要项目',
      dataIndex: 'importantProjectName',
    },
    {
      title: '管理措施落实证明',
      dataIndex: 'fileVOList',
      customRender({ text }) {
        if (isPower(`${powerCodePrefix.value}_container_04_01_button_03`, powerData.value)) {
          return h(Popover, { title: '附件' }, {
            default: () => h('div', { class: 'flex-te action-btn' }, '附件列表'),
            content: () => (text instanceof Array ? text : [])?.map((item: any) => h('p', {
              class: 'action-btn',
              onClick() {
                openFile(item);
              },
            }, item.name)),
          });
        }
        return '';
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 120,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '查看',
      isShow: () => isPower(`${powerCodePrefix.value}_container_04_01_button_01`, powerData.value),
      onClick: ({ id }: { id: string }) => navDetails(id),
    },
    {
      text: '编辑',
      isShow: () => isPower(`${powerCodePrefix.value}_container_04_01_button_02`, powerData.value),
      onClick(record: Record<string, any>) {
        openRiskForm(record, updateTable);
      },
    },
  ],
};

function navDetails(id: string) {
  router.push({
    name: pageConfig.detailRouteName,
    params: {
      id,
    },
  });
}

async function updateTable() {
  await nextTick();
  tableRef.value?.reload();
}
</script>

<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
  />
</template>

<style scoped lang="less">

</style>
