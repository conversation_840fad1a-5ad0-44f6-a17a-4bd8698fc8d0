<template>
  <BasicModal
    title="选择综合计划"
    width="1000px"
    :height="500"
    @register="registerModel"
    @cancel="cancelHandle"
    @ok="okHandle"
  >
    <SelectProPlanMain
      v-if="state.visible"
      :onInitTable="initTable"
    />
  </BasicModal>
</template>

<script setup lang="ts">
import { BasicModal, useModalInner } from 'lyra-component-vue3';
import {
  inject, reactive, Ref, ref,
} from 'vue';
import { message } from 'ant-design-vue';
import SelectProPlanMain from './SelectProPlanMain.vue';

const emit = defineEmits(['pushRows']);
const userOrgList:Ref = inject('userOrgList');
const state = reactive({
  visible: false,
  tableRef: null,
});
const tableRef = ref(null);
const [registerModel, { closeModal }] = useModalInner(() => {
  state.visible = true;
});
// 获取表格方法
function initTable(ref) {
  state.tableRef = ref;
}

function cancelHandle() {
  closeModal();
  state.visible = false;
}

function okHandle() {
  const selectList = state.tableRef.getSelectRows();
  if (selectList.length) {
    emit('pushRows', selectList);
    cancelHandle();
  } else {
    closeModal();
    message.warning('请勾选综合计划');
  }
}
</script>

<style scoped>

</style>
