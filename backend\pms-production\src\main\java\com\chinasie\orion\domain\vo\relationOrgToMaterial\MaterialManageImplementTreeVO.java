package com.chinasie.orion.domain.vo.relationOrgToMaterial;

import com.chinasie.orion.constant.StatisticField;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@ApiModel(value = "MaterialManagePlanTreeVO对象", description = "大修准备实施树")
@Data
public class MaterialManageImplementTreeVO extends ObjectVO implements Serializable {

    /**
     * 责任人id
     */
    @ApiModelProperty(value = "责任人id")
    private String rspUserId;

    /**
     * 责任人名称
     */
    @ApiModelProperty(value = "责任人名称")
    private String rspUserName;

    //  1.实际入场时间未报备：统计实际进场时间未填写的参修物资总数；
//    @StatisticField("inCount")
    @ApiModelProperty(value = "实际入场时间未报备")
    private Integer inCount;

    //统计状态为“已入场”的参修物资总数
//    @StatisticField("planInCount")
    @ApiModelProperty(value = "实际入场物资数")
    private Integer planInCount;

    //统计状态为“已离场”的参修物资总数
    @StatisticField("planOutCount")
    @ApiModelProperty(value = "实际离场物资数")
    private Integer planOutCount;

    //4.物资所在所有班组的计划结束时间均已到期，但物资未填写实际离场时间的总数。
    @StatisticField("planInNotCount")
    @ApiModelProperty(value = "实际离场未报备物资数")
    private Integer planInNotCount;

    /**
     * 资产名称
     */
    @ApiModelProperty(value = "资产名称")
    private String assetName;

    /**
     * 资产代码
     */
    @ApiModelProperty(value = "资产代码")
    private String assetCode;

    @ApiModelProperty(value = "实际离场日期")
    private Date actOutDate;

    @ApiModelProperty(value = "离场数量")
    private Integer outNum;
    @ApiModelProperty(value = "入库数量")
    private Integer inputStockNum;

    @ApiModelProperty(value = "出库原因")
    private String outReason;
    @ApiModelProperty(value = "出库原因名稱")
    private String outReasonName;

    @ApiModelProperty(value = "物资去向")
    private String materialDestination;

    @ApiModelProperty(value = "是否再次入场")
    private Boolean isAgainIn;

    @ApiModelProperty(value = "资产编码")
    private String number;

    @ApiModelProperty(value = "实际进场日期")
    private Date actInDate;

    @ApiModelProperty(value = "计划离场日期")
    private Date outDate;

    @ApiModelProperty(value = "物资库id")
    private String materialManageId;

    @ApiModelProperty(value = "固定资产能力库id")
    private String fixedAssetsId;

    public MaterialManageImplementTreeVO() {
        this.inCount = 0;
        this.planInCount = 0;
        this.planOutCount = 0;
        this.planInNotCount = 0;
    }
}
