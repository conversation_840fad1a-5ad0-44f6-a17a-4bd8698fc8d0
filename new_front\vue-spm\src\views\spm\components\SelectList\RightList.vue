<template>
  <div class="selected-wrap flex flex-ver">
    <div class="title flex">
      <h3 class="flex-f1">
        已选择<span>({{ selectedUser.length }})</span>
      </h3>
      <div
        v-if="selectedUser.length"
        class="fz12 action-btn"
        @click="deleteUser(null)"
      >
        删除全部
      </div>
    </div>
    <div class="flex-f1 selected-main">
      <template v-if="selectedUser&&selectedUser.length">
        <div
          v-for="(item, index) in selectedUser"
          :key="index"
          class="user-list "
        >
          <div
            class="user-name flex-te"
            :title="item[rightName]"
          >
            {{ item[rightName] }}
          </div>
          <div
            class="delete plr10"
            @click="deleteUser(item)"
          >
            <i class="fa fa-close" />
          </div>
        </div>
      </template>
      <div
        v-else
        class="flex flex-pac h-full"
      >
        <Empty description="请选择用户" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { reactive, defineComponent } from 'vue';
import { Empty } from 'ant-design-vue';

export default defineComponent({
  name: 'SelectedUser',
  components: {
    Empty,
  },
  props: {
    selectedUser: {
      type: Array,
      default: () => [],
    },
    rightName: {
      type: String,
      default: 'name',
    },
  },
  emits: ['deleteUser'],
  setup(props, { emit }) {
    const state:any = reactive({
      selectedArr: [],
    });

    return {
      addUser(user) {
        const oldUser = state.selectedArr.find((item) => item.id === user.id);
        if (!oldUser) {
          state.selectedArr.push(user);
        }
      },
      deleteUser(userItem) {
        emit('deleteUser', userItem);
      },
    };
  },
});
</script>

<style scoped lang="less">
  .selected-wrap {
    background: #f6f6f6;
    width: 100%;
    height: 100%;

    > .title {
      background: #eef2f3;
      height: 40px;
      line-height: 40px;
      padding: 0 10px;
      border-bottom: 1px solid ~`getPrefixVar('border-color-base')`;
      color: #535353;

      > h3 {
        font-weight: bold;
      }
    }

    > .selected-main {
      overflow-y: auto;
    }
  }

  .user-list {
    height: 36px;
    line-height: 36px;
    position: relative;
    padding: 0 10px;
    width: 100%;
    cursor: pointer;
    .user-name{
      width: 190px;
    }
    &:hover {
      background: #fff;

      .delete {
        opacity: 1;
        pointer-events: inherit;
      }
    }

    .delete {
      position: absolute;
      right: 0;
      top: 0;
      font-size: 12px;
      color: #797979;
      opacity: 0;
      pointer-events: none;
      cursor: pointer;
      transition: 0.3s;

      &:hover {
        color: ~`getPrefixVar('primary-color')`;
      }
    }
  }
</style>
