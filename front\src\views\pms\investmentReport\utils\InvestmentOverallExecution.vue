<template>
  <div
    class="form-wrap"
  >
    <Form
      ref="formRef"
      class="investment-form-inline"
      :model="modal"
    >
      <!--      <FormItem-->
      <!--        :required="true"-->
      <!--        name="companyName"-->
      <!--        label="计划公司"-->
      <!--        :label-col="formItemLayout.labelCol"-->
      <!--        :wrapper-col="formItemLayout.wrapperCol"-->
      <!--      >-->
      <!--        <Select-->
      <!--          v-model:value="modal.companyName"-->
      <!--          placeholder="请选择"-->
      <!--          allowClear-->
      <!--          showArrow-->
      <!--          mode="multiple"-->
      <!--          :options="companyNameOptions"-->
      <!--        />-->
      <!--      </FormItem>-->
      <FormItem
        name="yearName"
        label="年度"
        :label-col="formItemLayout.labelCol"
        :wrapper-col="formItemLayout.wrapperCol"
      >
        <RangePicker
          v-model:value="modal.yearName"
          picker="year"
          value-format="YYYY"
        />
      </FormItem>
      <FormItem
        name="projectNumber"
        label="项目编码"
        :label-col="formItemLayout.labelCol"
        :wrapper-col="formItemLayout.wrapperCol"
      >
        <Input
          v-model:value="modal.projectNumber"
          placeholder="请输入"
          class="table-input"
        />
      </FormItem>
      <FormItem
        name="projectName"
        label="项目名称"

        :label-col="formItemLayout.labelCol"
        :wrapper-col="formItemLayout.wrapperCol"
      >
        <Input
          v-model:value="modal.projectName"
          placeholder="请输入"
          class="table-input"
        />
      </FormItem>

      <FormItem
        name="projectStatusCode"
        label="项目状态"
        :label-col="formItemLayout.labelCol"
        :wrapper-col="formItemLayout.wrapperCol"
      >
        <Select
          v-model:value="modal.projectStatusCode"
          placeholder="请选择"
          allowClear
          :options="[
            {
              label:'已创建',
              value:'101'
            },
            {
              label:'已立项',
              value:'120'
            },

            {
              label:'已验收',
              value:'130'
            },
            {
              label:'已关闭',
              value:'140'
            },
            {
              label:'待立项',
              value:'110'
            },
          ]"
        />
      </FormItem>
      <FormItem
        name="rspDeptId"
        label="项目处室"
        :label-col="formItemLayout.labelCol"
        :wrapper-col="formItemLayout.wrapperCol"
      >
        <Select
          v-model:value="modal.rspDeptId"
          placeholder="请选择"
          :disabled="disabledRspDept"
          show-search
          allowClear
          :options="rspDeptOptions"
          :filter-option="filterRspDeptOptions"
          @change="handlePrincipal"
        />
      </FormItem>
      <!--      <FormItem-->
      <!--        name="rspDeptId"-->
      <!--        label="项目处室"-->
      <!--        :label-col="formItemLayout.labelCol"-->
      <!--        :wrapper-col="formItemLayout.wrapperCol"-->
      <!--      >-->
      <!--        <Select-->
      <!--          v-model:value="modal.rspDeptId"-->
      <!--          :allowClear="true"-->
      <!--          mode="multiple"-->
      <!--          :max-tag-count="1"-->
      <!--          placeholder="请选择"-->
      <!--          :disabled="disabledRspDept"-->
      <!--          show-search-->
      <!--          :options="[{label:'全部',value:''},...rspDeptOptions].filter(item=>item)"-->
      <!--          :filter-option="filterRspDeptOptions"-->
      <!--          @change="handlePrincipal"-->
      <!--        />-->
      <!--      </FormItem>-->
      <FormItem
        :label-col="{
          span:7,
          offset:7,
        }"
        :wrapper-col="{
          span:17,
          offset:7,
        }"
      >
        <BasicButton
          icon="sie-icon-sousuo"
          @click="submitForm"
        >
          查询
        </BasicButton>
        <BasicButton
          icon="sie-icon-chongzhi"
          @click="handleReset"
        >
          重置
        </BasicButton>
      </FormItem>
    </Form>
  </div>
</template>

<script setup lang="ts">
import {
  DatePicker, Form, FormItem, Select, SelectOption, Input,
  RangePicker,
} from 'ant-design-vue';
import { BasicButton, isPower } from 'lyra-component-vue3';
import {
  computed, onMounted,
  reactive, ref, Ref, watch,
  getCurrentInstance, toRaw,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import Api from '/@/api';
import { useUserStore } from '/@/store/modules/user';
import dayjs from 'dayjs';
import login from '/@/views/sys/login/Login.vue';

const props = defineProps<{
  searchKeys: string[]
}>();
const { emit } = getCurrentInstance();
const formRef: Ref = ref();
const route = useRoute();
const modal = ref({
  // companyName: [], // 项目所属公司
  yearName: route.query.dataCode ? [
    dayjs()
      .format('YYYY'),
    dayjs()
      .format('YYYY'),
  ] : null, // 年份
  status: undefined, // 月报状态
  projectNumber: undefined, // 项目名称
  rspDeptId: undefined, // 项目处室
  month: undefined, // 月份
  projectStatusCode: undefined, // 项目状态
});

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 7 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 17 },
  },
};
const userInfo = useUserStore().getUserInfo;
// 如果是经营计划处长和总经理部门的人可以选择
const disabledRspDept = ref(true);

// const disabledFlag = computed(() =>{route.query?.budgetType === 'all' };

function getLeaderCodeList(arr) {
  if (arr && arr.length > 0) return arr.map((item) => item.id);
}

onMounted(async () => {
  // console.log('当前用户信息', userInfo);
  // 首页投资计划完成情况跳转年度为当前年
  if (route.query?.budgetType === 'all') {
    const dateYear = String(new Date().getFullYear());
    // modal.value.yearName = [dateYear, dateYear];
    modal.value.yearName = [
      dayjs()
        .format(dateYear),
      dayjs()
        .format(dateYear),
    ];
  }
  // 判断禁用
  // 判断是否生产经验科/公司预算工程师
  let isCompanyBudgetManager = false;
  if (userInfo.roles && userInfo.roles.length > 0) {
    isCompanyBudgetManager = userInfo.roles.find((item) => item.code === 'company_budget_manager');
  }
  if (userInfo.simpleUser.orgCode === '32010000' || (userInfo.leaderType === 'divisionHead' && userInfo.simpleUser.orgCode === '32420000') || isCompanyBudgetManager) {
    disabledRspDept.value = false;
  } else {
    disabledRspDept.value = true;
  }
  // getCompanyNameOptions();
  await getRspDeptOptions();
  // 首页投资计划完成情况跳转投资计划总体执行
  if (route.query?.budgetType === 'all') {
    if (userInfo.leaderType === 'generalManager') {
      if (userInfo.code !== '19967041') {
        modal.value.rspDeptId = (getLeaderCodeList(userInfo?.simpleUser.leaderList));
      } else {
        modal.value.rspDeptId = '';
      }
    } else {
      modal.value.rspDeptId = [userInfo.simpleUser.orgId];
    }
  }
  // submitForm();
});

// 下拉选择全部和选项互斥
function handlePrincipal(val) {
  if (val.length > 0) {
    if (val.length > 1 && !val[0]) {
      modal.value.rspDeptId = val.splice(1, val.length);
    } else {
      // 判断全部
      let isAll = val.some((item) => item === '');
      if (isAll) {
        modal.value.rspDeptId = [''];
      }
    }
  }
}

// 获取公司名称
const companyNameOptions: Ref<any[]> = ref([]);

async function getCompanyNameOptions() {
  const result = await new Api('/pms/project/org/list').fetch('', '', 'GET');
  companyNameOptions.value = result.map((item) => ({
    ...item,
    label: item.name,
    value: item.id,
  }));
}

// 获取项目处室
const rspDeptOptions: Ref<any[]> = ref([]);

async function getRspDeptOptions() {
  const result = await new Api('/pmi/organization/org-type').fetch('', '', 'GET');
  rspDeptOptions.value = result.map((item) => ({
    ...item,
    label: item.name,
    value: item.id,
  }));

  // 有权限不去修改责任科室
  if (disabledRspDept.value) {
    modal.value.rspDeptId = [userInfo.simpleUser.orgId];
  } else {
    modal.value.rspDeptId = undefined;
  }
}

const submitForm = () => {
  // if (Array.isArray(modal.value.rspDeptId)) {
  //   modal.value.rspDeptId = modal.value.rspDeptId.filter((item) => item);
  //   modal.value.rspDeptId = modal.value.rspDeptId.toString();
  // }
  // console.log('点击', modal.value);
  emit('click-search', modal.value);
};

function filterRspDeptOptions(inputValue, treeNode) {
  return treeNode.props.label.includes(inputValue);
}

// 重置
function handleReset() {
  formRef.value.resetFields();
  // 如果没有权限不重置按钮
  if (disabledRspDept.value) {
    modal.value.rspDeptId = [userInfo.simpleUser.orgId];
  }
}

defineExpose({
  modal,
  submitForm,
});
</script>

<style scoped lang="less">
.form-wrap {
  position: relative;
  padding: ~`getPrefixVar('content-margin')`;
  display: flex;
  flex-direction: column;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.24);
  border-radius: ~`getPrefixVar('radius-base')`;

  :deep(.investment-form-inline) {
    display: flex;
    flex-wrap: wrap;

    .ant-form-item {
      width: 25%;
    }
  }

}
</style>
