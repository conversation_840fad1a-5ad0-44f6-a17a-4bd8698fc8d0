<script setup lang="ts">
import { isPower, OrionTable, useModal } from 'lyra-component-vue3';
import {
  inject, onMounted, ref, Ref,
} from 'vue';
import Api from '/@/api';
import { formatMoney } from '/@/views/pms/utils/utils';
import dayjs from 'dayjs';
import FilesModal from '../../components/filesModal/FilesModal.vue';

const [registerFilesModal, { openModal: openFilesModal }] = useModal();

const powerData: Ref = inject('powerData');
const dataId = inject('dataId');
const tableRef: Ref = ref();
const tableOptions = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  api: (params) => new Api('/pms/projectFundsReceived/pages').fetch({
    ...params,
    query: {
      receivableId: dataId,
    },
  }, '', 'POST'),
  columns: [
    {
      title: '客户名称',
      dataIndex: 'stakeholderName',
    },
    {
      title: '合同收款节点',
      dataIndex: 'collectionPoint',
    },
    {
      title: '实收日期',
      dataIndex: 'fundsReceivedDate',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '实收金额（元）',
      dataIndex: 'fundsReceived',
      customRender({ text }) {
        return text ? formatMoney(text) : '0';
      },
    },
    {
      title: '收款方式',
      dataIndex: 'paymentTerm',
    },
    {
      title: '发票号码',
      dataIndex: 'invoiceNumber',
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 80,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '下载附件',
      isShow: () => isPower('PMS_YSXQ_container_03_button_01', powerData.value),
      onClick(record:Record<string, any>) {
        openFilesModal(true, { id: record.id });
      },
    },
  ],
};
</script>

<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
  />
  <FilesModal @register="registerFilesModal" />
</template>

<style scoped lang="less">

</style>
