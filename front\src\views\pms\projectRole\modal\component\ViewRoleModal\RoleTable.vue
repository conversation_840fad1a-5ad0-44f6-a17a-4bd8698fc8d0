<template>
  <OrionTable
    :options="tableOptions"
    class="table"
  >
    <template #auth="{ record }">
      <div>{{ authArr(record) }}</div>
    </template>
    <template #description="{ record }">
      <div>{{ record.description || record.name }}</div>
    </template>
  </OrionTable>
</template>

<script lang="ts">
import { computed, defineComponent, ref } from 'vue';
import { OrionTable } from 'lyra-component-vue3';
import Api from '/@/api';

export default defineComponent({
  name: 'RoleTable',
  components: {
    OrionTable,
  },
  props: {
    roleId: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    /**
       * 排序方法
       * @param meaus
       */
    function sortFuc(menu: any[]) {
      menu.forEach((item) => {
        menu.sort((a, b) => (a.sort || 0) - (b.sort || 0));
        if (item.children && Array.isArray(item.children) && item.children.length > 0) {
          sortFuc(item.children);
        }
      });
    }

    return {
      authArr: computed(() => (record) => {
        let auth: string[] = [];

        if (record.isChecked) {
          auth.push('查看');
        }

        if (record.buttons && Array.isArray(record.buttons)) {
          record.buttons.forEach((buttonItem) => {
            buttonItem.isChecked && auth.push(buttonItem?.name);
          });
        }

        if (!auth.length) {
          auth.push('无');
        }
        return auth.join('、');
      }),
      tableOptions: {
        showToolButton: false,
        showSmallSearch: false,
        showIndexColumn: false,
        pagination: false,
        maxHeight: 300,
        // defaultExpandAllRows: true,
        columns: [
          {
            title: '功能模块',
            align: 'left',
            dataIndex: 'description',
            width: 350,
            slots: { customRender: 'description' },
          },
          {
            title: '功能权限',
            dataIndex: 'auth',
            slots: { customRender: 'auth' },
            align: 'left',
          },
        ],
        api: () => new Api('/pmi/power/function/menu/org-role').get(props.roleId).then((data) => {
          sortFuc(data);
          return data;
        }),
      },
    };
  },
});
</script>

<style scoped lang="less">
  .table {
    :deep(.ant-table-title) {
      display: none !important;
    }
  }
</style>
