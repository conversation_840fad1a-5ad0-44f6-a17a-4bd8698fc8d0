<script setup lang="ts">
import {
  BasicCard, BasicForm, FormSchema, useForm,
} from 'lyra-component-vue3';
import Api from '/@/api';
import {
  h, onMounted, ref, Ref,
} from 'vue';

const props = defineProps<{
  record: any
}>();

const schemas: FormSchema[] = [
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '隐患信息',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  {
    field: 'hiddenDangerCode',
    component: 'Input',
    label: '隐患编号',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'eventTopic',
    component: 'Input',
    label: '事件主题',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'eventLevel',
    component: 'Input',
    label: '事件等级',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'eventLocation',
    component: 'Input',
    label: '事件地点',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'reviewerName',
    component: 'Input',
    label: '检查人',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'deptName',
    component: 'Input',
    label: '检查人所在部门',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'classificationType',
    component: 'Input',
    label: '分类类型',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'rspCenterName',
    component: 'Input',
    label: '责任中心',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'hiddenDangerType',
    component: 'Input',
    label: '隐患类型',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'occurrenceDate',
    component: 'DatePicker',
    label: '事发日期',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'isMajorRepair',
    component: 'Select',
    label: '是否大修',
    componentProps: {
      disabled: true,
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
  },
  {
    field: 'hiddenEvent',
    component: 'ApiSelect',
    label: '隐患/事件领域',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'eventType',
    component: 'Input',
    label: '事件类型',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'isClosed',
    component: 'Select',
    label: '是否已关闭',
    componentProps: {
      disabled: true,
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
  },
  {
    field: 'currentProcess',
    component: 'Input',
    label: '当前流程',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '待补充信息',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  {
    field: 'baseCode',
    label: '所属基地',
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
      api: () => new Api('/pms/base-place/list').fetch('', '', 'POST'),
      labelField: 'name',
      valueField: 'code',
    },
    rules: [{ required: true }],
    component: 'ApiSelect',
  },
  {
    field: 'pyramidCategory',
    component: 'SelectDictVal',
    label: '金字塔类别',
    required: true,
    componentProps: {
      dictNumber: 'pms_pyramid_category',
    },
  },
  {
    field: 'majorRepairTurn',
    component: 'ApiSelect',
    label: '大修轮次',
    componentProps: {
      api: async () => {
        const result = await new Api('/pms/safety-quality-env/repairRound/list').fetch('', '', 'GET');
        return result.map((item: string) => ({
          label: item,
          value: item,
        }));
      },
    },
  },
  {
    field: 'isAssessed',
    component: 'Select',
    label: '是否考核',
    rules: [
      {
        required: true,
        type: 'boolean',
      },
    ],
    componentProps({ formModel }) {
      return {
        onChange() {
          if (formModel.isAssessed) {
            formModel.assessmentLevel = '';
          }
        },
        options: [
          {
            label: '是',
            value: true,
          },
          {
            label: '否',
            value: false,
          },
        ],
      };
    },
  },
  {
    field: 'assessmentLevel',
    component: 'SelectDictVal',
    label: '考核级别',
    required: true,
    ifShow({ model }) {
      return model.isAssessed;
    },
    componentProps: {
      fieldNames: {
        label: 'description',
        value: 'number',
      },
      dictNumber: 'pms_assessment_Level',
    },
  },
];

const [register, { validate, setFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  props?.record?.id && getFormData();
});

const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/safety-quality-env').fetch('', props?.record?.id, 'GET');
    await setFieldsValue(result);
  } finally {
    loading.value = false;
  }
}

defineExpose({
  async submit() {
    const formValues = await validate();
    return new Promise((resolve, reject) => {
      new Api('/pms/safety-quality-env').fetch({
        ...formValues,
        id: props?.record?.id,
      }, props?.record?.id ? 'edit' : 'add', props?.record?.id ? 'PUT' : 'POST').then(() => {
        resolve('');
      }).catch((e) => {
        reject(e);
      });
    });
  },
});

</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">

</style>
