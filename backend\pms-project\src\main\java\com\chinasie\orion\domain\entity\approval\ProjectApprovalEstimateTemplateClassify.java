package com.chinasie.orion.domain.entity.approval;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * ProjectApprovalEstimateTemplateClassify Entity对象
 *
 * <AUTHOR>
 * @since 2024-04-28 17:21:23
 */
@TableName(value = "pms_project_approval_estimate_template_classify")
@ApiModel(value = "ProjectApprovalEstimateTemplateClassifyEntity对象", description = "概算模板分类")
@Data
public class ProjectApprovalEstimateTemplateClassify extends ObjectEntity implements Serializable{

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField(value = "name")
    private String name;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    @TableField(value = "number")
    private String number;


    /**
     * 编号
     */
    @ApiModelProperty(value = "父级ID")
    @TableField(value = "parent_id")
    private String parentId;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @TableField(value = "description")
    private String description;

}
