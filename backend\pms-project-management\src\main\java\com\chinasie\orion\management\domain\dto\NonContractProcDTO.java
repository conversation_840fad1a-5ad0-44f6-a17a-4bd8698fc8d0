package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * NonContractProc DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-12 11:04:24
 */
@ApiModel(value = "NonContractProcDTO对象", description = "无合同采购表")
@Data
@ExcelIgnoreUnannotated
public class NonContractProcDTO extends ObjectDTO implements Serializable {

    /**
     * 工作主题
     */
    @ApiModelProperty(value = "工作主题")
    @ExcelProperty(value = "工作主题 ", index = 0)
    private String workTopic;

    /**
     * 流程名称
     */
    @ApiModelProperty(value = "流程名称")
    @ExcelProperty(value = "流程名称 ", index = 1)
    private String processName;

    /**
     * 发起时间
     */
    @ApiModelProperty(value = "发起时间")
    @ExcelProperty(value = "发起时间 ", index = 2)
    private Date initiationTime;

    /**
     * 发起人
     */
    @ApiModelProperty(value = "发起人")
    @ExcelProperty(value = "发起人 ", index = 3)
    private String initiator;

    /**
     * 报销人
     */
    @ApiModelProperty(value = "报销人")
    @ExcelProperty(value = "报销人 ", index = 4)
    private String claimant;

    /**
     * 申请公司编码
     */
    @ApiModelProperty(value = "申请公司编码")
    @ExcelProperty(value = "申请公司编码 ", index = 5)
    private String applyCompanyCode;

    /**
     * 申请公司名称
     */
    @ApiModelProperty(value = "申请公司名称")
    @ExcelProperty(value = "申请公司名称 ", index = 6)
    private String applyCompanyName;

    /**
     * 申请部门
     */
    @ApiModelProperty(value = "申请部门")
    @ExcelProperty(value = "申请部门 ", index = 7)
    private String applyDept;

    /**
     * 费用归属公司编码
     */
    @ApiModelProperty(value = "费用归属公司编码")
    @ExcelProperty(value = "费用归属公司编码 ", index = 8)
    private String expenseCompanyCode;

    /**
     * 费用归属公司名称
     */
    @ApiModelProperty(value = "费用归属公司名称")
    @ExcelProperty(value = "费用归属公司名称 ", index = 9)
    private String xpenseCompanyName;

    /**
     * 报销金额
     */
    @ApiModelProperty(value = "报销金额")
    @ExcelProperty(value = "报销金额 ", index = 10)
    private String reimbursedAmount;

    /**
     * 要求付款时间
     */
    @ApiModelProperty(value = "要求付款时间")
    @ExcelProperty(value = "要求付款时间 ", index = 11)
    private Date reqPaymentTime;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    @ExcelProperty(value = "币种 ", index = 12)
    private String currency;

    /**
     * 折合人民币
     */
    @ApiModelProperty(value = "折合人民币")
    @ExcelProperty(value = "折合人民币 ", index = 13)
    private String inRmb;

    /**
     * 申请原因
     */
    @ApiModelProperty(value = "申请原因")
    @ExcelProperty(value = "申请原因 ", index = 14)
    private String applyReason;

    /**
     * 费用信息
     */
    @ApiModelProperty(value = "费用信息")
    @ExcelProperty(value = "费用信息 ", index = 15)
    private String expenseInfo;

    /**
     * 供应商信息
     */
    @ApiModelProperty(value = "供应商信息")
    @ExcelProperty(value = "供应商信息 ", index = 16)
    private String supplierInfo;

    /**
     * 是否内部交易
     */
    @ApiModelProperty(value = "是否内部交易")
//    @ExcelProperty(value = "是否内部交易 ", index = 17)
    private Boolean isInternalTx;

    /**
     * 是否内部交易中文名
     */
    @ApiModelProperty(value = "是否内部交易")
    @ExcelProperty(value = "是否内部交易 ", index = 17)
    private String isInternalTxName;

    /**
     * 内部交易号
     */
    @ApiModelProperty(value = "内部交易号")
    @ExcelProperty(value = "内部交易号 ", index = 18)
    private String internalTxNumber;

    /**
     * 流程状态
     */
    @ApiModelProperty(value = "流程状态")
    @ExcelProperty(value = "流程状态 ", index = 19)
    private String processStatus;

    /**
     * 支付方式
     */
    @ApiModelProperty(value = "支付方式")
    @ExcelProperty(value = "支付方式 ", index = 20)
    private String paymentWay;

    /**
     * 立项类别
     */
    @ApiModelProperty(value = "立项类别")
    @ExcelProperty(value = "立项类别 ", index = 21)
    private String projectCategory;

    /**
     * 立项号
     */
    @ApiModelProperty(value = "立项号")
    @ExcelProperty(value = "立项号 ", index = 22)
    private String projectCode;

    /**
     * 立项名称
     */
    @ApiModelProperty(value = "立项名称")
    @ExcelProperty(value = "立项名称 ", index = 23)
    private String projectName;

    /**
     * 归口部门
     */
    @ApiModelProperty(value = "归口部门")
    @ExcelProperty(value = "归口部门 ", index = 24)
    private String bkDept;

    /**
     * 支付金额
     */
    @ApiModelProperty(value = "支付金额")
    @ExcelProperty(value = "支付金额 ", index = 25)
    private String paymentAmount;

    /**
     * 交易金额
     */
    @ApiModelProperty(value = "交易金额")
    @ExcelProperty(value = "交易金额 ", index = 26)
    private String transactionAmount;

    /**
     * 实际支付金额
     */
    @ApiModelProperty(value = "实际支付金额")
    @ExcelProperty(value = "实际支付金额 ", index = 27)
    private String actualPaymentAmount;

    /**
     * 申请笔数
     */
    @ApiModelProperty(value = "申请笔数")
    @ExcelProperty(value = "申请笔数 ", index = 28)
    private String applyNumber;

    /**
     * 报销金额
     */
    @ApiModelProperty(value = "报销金额")
    @ExcelProperty(value = "报销金额 ", index = 29)
    private String reimbursementAmount;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 30)
    private String number;


}
