package com.chinasie.orion.domain.dto;

import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ProjectSchemeApplyApprovalDTO
 *
 * @author: yangFy
 * @date: 2023/4/19 15:24
 * @description:
 * <p>
 *ProjectSchemeApplyApprovalDTO对象
 * </p>
 */
@Data
@ApiModel(value = "ProjectSchemeApplyApprovalDTO对象", description = "项目计划申请审批")
public class ProjectSchemeApplyApprovalDTO extends ObjectDTO {

    /**
     *
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;
    /**
     *
     */
    @ApiModelProperty(value = "项目计划id")
    private String projectSchemeId;
    /**
     *
     */
    @ApiModelProperty(value = "内容")
    private String content;
    /**
     *
     */
    @ApiModelProperty(value = "审批人")
    private String certifier;
    /**
     *
     */
    @ApiModelProperty(value = "是否通过")
    private String agreement;
    /**
     *
     */
    @ApiModelProperty(value = "审批意见")
    private String feedBack;


}
