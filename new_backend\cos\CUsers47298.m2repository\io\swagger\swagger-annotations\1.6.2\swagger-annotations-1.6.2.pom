<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>io.swagger</groupId>
        <artifactId>swagger-project</artifactId>
        <version>1.6.2</version>
        <relativePath>../..</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>io.swagger</groupId>
    <artifactId>swagger-annotations</artifactId>
    <version>1.6.2</version>
    <packaging>bundle</packaging>
    <name>swagger-annotations</name>
    <build>
        <sourceDirectory>src/main/java</sourceDirectory>
        <defaultGoal>install</defaultGoal>
        <plugins>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>${felix-version}</version>
                <extensions>true</extensions>
                <configuration>
                    <instructions>
                        <Export-Package>io.swagger.annotations</Export-Package>
                    </instructions>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
            <id>publish-javadocs</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-enforcer-plugin</artifactId>
                        <version>${enforcer-plugin-version}</version>
                        <executions>
                            <execution>
                                <id>enforce-no-snapshots</id>
                                <goals>
                                    <goal>enforce</goal>
                                </goals>
                                <configuration>
                                    <rules>
                                        <requireReleaseVersion>
                                            <message>Publish javadocs for a SNAPSHOT is not allowed.</message>
                                        </requireReleaseVersion>
                                    </rules>
                                    <fail>true</fail>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <artifactId>maven-resources-plugin</artifactId>
                        <version>2.7</version>
                        <executions>
                            <execution>
                                <id>copy-resources</id>
                                <!-- here the phase you need -->
                                <phase>deploy</phase>
                                <goals>
                                    <goal>copy-resources</goal>
                                </goals>
                                <configuration>
                                    <outputDirectory>${project.build.directory}/javadocprep/swagger-core/${project.version}/apidocs</outputDirectory>
                                    <resources>
                                        <resource>
                                            <directory>${project.build.directory}/apidocs</directory>
                                            <filtering>true</filtering>
                                        </resource>
                                    </resources>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>com.pyx4j</groupId>
                        <artifactId>maven-junction-plugin</artifactId>
                        <executions>
                            <execution>
                                <phase>deploy</phase>
                                <goals>
                                    <goal>link</goal>
                                </goals>
                            </execution>
                        </executions>
                        <configuration>
                            <links>
                                <!-- link the html generated by client to webapp directory -->
                                <link>
                                    <dst>${project.build.directory}/javadocprep/swagger-core/current</dst>
                                    <src>${project.build.directory}/javadocprep/swagger-core/${project.version}</src>
                                </link>
                            </links>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-scm-publish-plugin</artifactId>
                        <version>1.1</version>
                        <configuration>
                            <checkoutDirectory>${project.build.directory}/scmpublish</checkoutDirectory>
                            <checkinComment>Publishing javadoc for ${project.artifactId}:${project.version}</checkinComment>
                            <content>${project.build.directory}/javadocprep</content>
                            <skipDeletedFiles>true</skipDeletedFiles>
                            <pubScmUrl>scm:git:https://github.com/swagger-api/swagger-core</pubScmUrl>
                            <scmBranch>gh-pages</scmBranch>
                        </configuration>
                        <executions>
                            <execution>
                                <id>publish-javadocs</id>
                                <phase>deploy</phase>
                                <goals>
                                    <goal>publish-scm</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>