package com.chinasie.orion.service.impl;


import com.chinasie.orion.domain.dto.InvestmentSchemeEstimateDTO;
import com.chinasie.orion.domain.entity.InvestmentScheme;
import com.chinasie.orion.domain.entity.InvestmentSchemeEstimate;
import com.chinasie.orion.domain.entity.YearInvestmentScheme;
import com.chinasie.orion.domain.vo.InvestmentSchemeEstimateVO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.InvestmentSchemeEstimateRepository;

import com.chinasie.orion.service.InvestmentSchemeEstimateService;
import com.chinasie.orion.service.InvestmentSchemeService;
import com.chinasie.orion.service.YearInvestmentSchemeService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * InvestmentSchemeEstimate 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16 14:33:06
 */
@Service
public class InvestmentSchemeEstimateServiceImpl extends OrionBaseServiceImpl<InvestmentSchemeEstimateRepository,InvestmentSchemeEstimate> implements InvestmentSchemeEstimateService {

    @Resource
    private InvestmentSchemeService investmentSchemeService;

    @Resource
    private YearInvestmentSchemeService yearInvestmentSchemeService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public InvestmentSchemeEstimateVO detail(String id) throws Exception {
        InvestmentSchemeEstimate investmentSchemeEstimate = this.getById(id);
        InvestmentSchemeEstimateVO result = BeanCopyUtils.convertTo(investmentSchemeEstimate, InvestmentSchemeEstimateVO::new);
        return result;
    }

    /**
     * 新增
     * <p>
     * * @param investmentSchemeEstimateDTO
     */
    @Override
    public InvestmentSchemeEstimateVO create(InvestmentSchemeEstimateDTO investmentSchemeEstimateDTO) throws Exception {
        InvestmentScheme investmentScheme = investmentSchemeService.getById(investmentSchemeEstimateDTO.getInvestmentSchemeId());

        LambdaQueryWrapperX<InvestmentSchemeEstimate> estimateLambdaQueryWrapperX = new LambdaQueryWrapperX<>(InvestmentSchemeEstimate.class);
        estimateLambdaQueryWrapperX.eq(InvestmentSchemeEstimate::getInvestmentSchemeId, investmentSchemeEstimateDTO.getInvestmentSchemeId());
        estimateLambdaQueryWrapperX.eq(InvestmentSchemeEstimate::getSource, investmentSchemeEstimateDTO.getSource());
        List<InvestmentSchemeEstimate> investmentSchemeEstimates = this.list(estimateLambdaQueryWrapperX);
        if (!CollectionUtils.isEmpty(investmentSchemeEstimates)) {
            throw new BaseException(PMSErrorCode.PMS_ERR, "估算/概算版本已存在");
        }

        InvestmentSchemeEstimate investmentSchemeEstimate = BeanCopyUtils.convertTo(investmentSchemeEstimateDTO, InvestmentSchemeEstimate::new);
        investmentSchemeEstimate.setProjectId(investmentScheme.getProjectId());

        if (Objects.isNull(investmentSchemeEstimate.getEstimate())) {
            investmentSchemeEstimate.setEstimate(BigDecimal.ZERO);
        }

        if (Objects.isNull(investmentSchemeEstimate.getArchitecture())) {
            investmentSchemeEstimate.setArchitecture(BigDecimal.ZERO);
        }

        if (Objects.isNull(investmentSchemeEstimate.getDevice())) {
            investmentSchemeEstimate.setDevice(BigDecimal.ZERO);
        }

        if (Objects.isNull(investmentSchemeEstimate.getInstallation())) {
            investmentSchemeEstimate.setInstallation(BigDecimal.ZERO);
        }

        if (Objects.isNull(investmentSchemeEstimate.getOther())) {
            investmentSchemeEstimate.setOther(BigDecimal.ZERO);
        }
        if (Objects.isNull(investmentSchemeEstimate.getEstimateReserve())) {
            investmentSchemeEstimate.setEstimateReserve(BigDecimal.ZERO);
        }

         this.save(investmentSchemeEstimate);
        InvestmentSchemeEstimateVO rsp = BeanCopyUtils.convertTo(investmentSchemeEstimate, InvestmentSchemeEstimateVO::new);
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param investmentSchemeEstimateDTO
     */
    @Override
    public Boolean edit(InvestmentSchemeEstimateDTO investmentSchemeEstimateDTO) throws Exception {
        InvestmentSchemeEstimate dbInvestmentSchemeEstimate = this.getById(investmentSchemeEstimateDTO.getId());
        LambdaQueryWrapperX<InvestmentSchemeEstimate> estimateLambdaQueryWrapperX = new LambdaQueryWrapperX<>(InvestmentSchemeEstimate.class);
        estimateLambdaQueryWrapperX.ne(InvestmentSchemeEstimate::getId, investmentSchemeEstimateDTO.getId());
        estimateLambdaQueryWrapperX.eq(InvestmentSchemeEstimate::getInvestmentSchemeId, dbInvestmentSchemeEstimate.getInvestmentSchemeId());
        estimateLambdaQueryWrapperX.eq(InvestmentSchemeEstimate::getSource, investmentSchemeEstimateDTO.getSource());
        List<InvestmentSchemeEstimate> investmentSchemeEstimates = this.list(estimateLambdaQueryWrapperX);
        if (!CollectionUtils.isEmpty(investmentSchemeEstimates)) {
            throw new BaseException(PMSErrorCode.PMS_ERR, "估算/概算版本已存在");
        }

        InvestmentSchemeEstimate investmentSchemeEstimate = BeanCopyUtils.convertTo(investmentSchemeEstimateDTO, InvestmentSchemeEstimate::new);
        if (Objects.isNull(investmentSchemeEstimate.getEstimate())) {
            investmentSchemeEstimate.setEstimate(BigDecimal.ZERO);
        }

        if (Objects.isNull(investmentSchemeEstimate.getArchitecture())) {
            investmentSchemeEstimate.setArchitecture(BigDecimal.ZERO);
        }

        if (Objects.isNull(investmentSchemeEstimate.getDevice())) {
            investmentSchemeEstimate.setDevice(BigDecimal.ZERO);
        }

        if (Objects.isNull(investmentSchemeEstimate.getInstallation())) {
            investmentSchemeEstimate.setInstallation(BigDecimal.ZERO);
        }

        if (Objects.isNull(investmentSchemeEstimate.getOther())) {
            investmentSchemeEstimate.setOther(BigDecimal.ZERO);
        }
        if (Objects.isNull(investmentSchemeEstimate.getEstimateReserve())) {
            investmentSchemeEstimate.setEstimateReserve(BigDecimal.ZERO);
        }
        Boolean result = this.updateById(investmentSchemeEstimate);
        return result;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        LambdaQueryWrapperX<YearInvestmentScheme> yearInvestmentSchemeLambdaQueryWrapperX = new LambdaQueryWrapperX<>(YearInvestmentScheme.class);
        yearInvestmentSchemeLambdaQueryWrapperX.in(YearInvestmentScheme::getEstimateId, ids);
        List<YearInvestmentScheme> yearInvestmentSchemes = yearInvestmentSchemeService.list(yearInvestmentSchemeLambdaQueryWrapperX);
        if (!CollectionUtils.isEmpty(yearInvestmentSchemes)) {
            throw new BaseException(PMSErrorCode.PMS_ERR, "概算已关联年度投资计划");
        }
        Boolean result = this.removeBatchByIds(ids);
        return result;
    }


    @Override
    public List<InvestmentSchemeEstimateVO> list(String investmentSchemeId) throws Exception {
        LambdaQueryWrapperX<InvestmentSchemeEstimate> estimateLambdaQueryWrapperX = new LambdaQueryWrapperX<>(InvestmentSchemeEstimate.class);
        estimateLambdaQueryWrapperX.eq(InvestmentSchemeEstimate::getInvestmentSchemeId, investmentSchemeId);
        List<InvestmentSchemeEstimate> investmentSchemeEstimates = this.list(estimateLambdaQueryWrapperX);
        if (CollectionUtils.isEmpty(investmentSchemeEstimates)) {
            return new ArrayList<>();
        }
        List<InvestmentSchemeEstimateVO> result = BeanCopyUtils.convertListTo(investmentSchemeEstimates, InvestmentSchemeEstimateVO::new);
        return result;
    }

    @Override
    public List<InvestmentSchemeEstimateVO> listByProjectIdList(List<String> projectIdList) throws Exception {
        LambdaQueryWrapperX<InvestmentSchemeEstimate> estimateLambdaQueryWrapperX = new LambdaQueryWrapperX<>(InvestmentSchemeEstimate.class);
        estimateLambdaQueryWrapperX.select(InvestmentSchemeEstimate::getProjectId,InvestmentSchemeEstimate::getCreatorId);
        estimateLambdaQueryWrapperX.in(InvestmentSchemeEstimate::getProjectId, projectIdList);
        List<InvestmentSchemeEstimate> investmentSchemeEstimates = this.list(estimateLambdaQueryWrapperX);
        if (CollectionUtils.isEmpty(investmentSchemeEstimates)) {
            return new ArrayList<>();
        }
        List<InvestmentSchemeEstimateVO> result = BeanCopyUtils.convertListTo(investmentSchemeEstimates, InvestmentSchemeEstimateVO::new);
        return result;
    }
}
