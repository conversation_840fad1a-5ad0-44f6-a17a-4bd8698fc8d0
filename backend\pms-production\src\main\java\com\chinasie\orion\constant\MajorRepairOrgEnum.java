package com.chinasie.orion.constant;

import java.util.HashMap;
import java.util.Map;

public enum MajorRepairOrgEnum {


    LEVEL_TYPE_SPECIALTY_TEAM("specialtyTeam","专业班组"),
    LEVEL_TYPE_MANAGEMENT_ROLE("specialtyManagementRole","管理组-角色"),
    LEVEL_TYPE_EXECUTION_SPECIALTY("executionSpecialty","执行专业"),
    LEVEL_TYPE_REPAIR_ROLE("repairRole","大修指挥部角色"),
    LEVEL_TYPE_GROUP_PROJECT("project","项目"),
    LEVEL_TYPE_GROUP_BUSINESS_DATA("businessData","业务数据");

    private String code;
    private String desc;
    MajorRepairOrgEnum(String code,String desc){
        this.code = code;
        this.desc = desc;
    }
    public String getCode(){
        return code;
    }

    public String getDesc(){
        return desc;
    }

   public static Map<String,String> getMap(){
        Map<String,String> map = new HashMap<>();
       for(MajorRepairOrgEnum item : MajorRepairOrgEnum.values()){
           map.put(item.getCode(),item.getDesc());
       }
       return map;
   }

    public static String getDescByCode(String code){
        for(MajorRepairOrgEnum item : MajorRepairOrgEnum.values()){
            if(item.getCode().equals(code)){
                return item.getDesc();
            }
        }
        return null;
    }
}
