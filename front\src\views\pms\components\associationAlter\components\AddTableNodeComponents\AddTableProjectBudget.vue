<script setup lang="ts">
import {
  BasicButton, BasicForm, OrionTable, useForm,
  SelectUserModal,
  openTreeSelectModal,
  InputSelectUser, openSelectUserModal,
} from 'lyra-component-vue3';
import {
  computed, reactive, Ref, ref,
} from 'vue';

import {
  Input as AInput, message, InputSearch as AInputSearch, Select as ASelect, Modal,
} from 'ant-design-vue';
import {
  ecrProjectBudgetListTableColumns, simpleProjectTableColumns,
} from '../../tableColumns.js';

import Api from '/@/api';

import { AddSelectTableModal } from '../AddSelectTableModal/index';

const props = defineProps<{
  type: string | undefined
}>();

const loading: Ref<boolean> = ref(false);

const ecrProjectBudgetListTableRef = ref();
const ecrProjectBudgetListTableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  showSmallSearch: false,
  pagination: false,
  columns: ecrProjectBudgetListTableColumns,
  actions: [
    {
      text: '移除',
      onClick(record) {
        Modal.confirm({
          title: '是否移除当前数据？',
          onOk() {
            removeTableDataByRef(ecrProjectBudgetListTableRef, record);
          },
        });
      },
    },
  ],
});
function ecrProjectBudgetListAdd() {
  AddSelectTableModal(
    {
      title: '关联预算',
      width: '80%',
      selectType: 'checkbox',
      // selectedData: tableRef.value.getDataSource(),
      columns: ecrProjectBudgetListTableColumns,
      tableApi(option) {
        const params: Record<string, any> = {
          ...option,
          query: {
            projectId: commonProjectInfo.projectId,
          },
        };
        delete params.tableMethods;
        delete params.node;
        return new Api('/pms/budgetManagement/page').fetch(params, '', 'POST');
      },
      onOk({ tableData }) {
        projectBudgetPrams.ecrProjectBudgetList = tableData.map((obj) => ({ budgetId: obj.id }));
        ecrProjectBudgetListTableRef.value.setTableData(tableData);
      },

    },
  );
}

const commonProjectInfo = reactive({
  projectName: '',
  projectId: '',
  oldStatus: '',
  oldStatusName: '',

});
const projectManagerPrams = reactive({
  projectName: computed(() => commonProjectInfo.projectName),
  projectId: computed(() => commonProjectInfo.projectId),
  oldManager: '',
  oldManagerName: '',
  newManager: '',
  newManagerName: '',
});

const projectBudgetPrams = reactive({
  projectName: computed(() => commonProjectInfo.projectName),
  projectId: computed(() => commonProjectInfo.projectId),
  oldStatus: computed(() => commonProjectInfo.oldStatus),
  oldStatusName: computed(() => commonProjectInfo.oldStatusName),
  ecrProjectBudgetList: [],
});

function SelectSimpleProjectClick() {
  AddSelectTableModal({
    title: '项目列表',
    width: '80%',
    selectType: 'radio',
    selectedData: [
      {
        id: commonProjectInfo.projectId,
        name: commonProjectInfo.projectName,
      },
    ],
    columns: simpleProjectTableColumns,
    tableApi(option) {
      const params: Record<string, any> = {
        ...option,
        query: {

        },
      };
      delete params.node;
      delete params.tableMethods;
      delete params.orders;
      return new Api('/pms/project/getSimplePage').fetch(params, '', 'POST');
    },
    async onOk({ tableData }) {
      const obj = tableData[0];
      commonProjectInfo.projectName = obj.name;
      commonProjectInfo.projectId = obj.id;
      commonProjectInfo.oldStatusName = obj.dataStatus.name;
      commonProjectInfo.oldStatus = obj.dataStatus.statusValue;
      projectManagerPrams.oldManagerName = obj.pm;
      projectManagerPrams.oldManager = obj.pmId;
      setFieldsValue({
        projectName: obj.name,
        projectId: obj.id,
        oldManagerName: obj.pm,
        oldManager: obj.pmId,
        oldStatusName: obj.dataStatus.name,
        oldStatus: obj.dataStatus.statusValue,
      });
    },
  });
}

const [
  register,
  {
    validate, updateSchema, setFieldsValue, getFieldsValue,
  },
] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas: [
    {
      field: 'projectName',
      component: 'Input',
      slot: 'SelectSimpleProject',
      label: '选择项目',
      rules: [{ required: true }],
    },
    {
      field: 'ecrProjectBudgetList',
      slot: 'ecrProjectBudgetList',
      component: 'Input',
      label: '变更预算',
      colProps: {
        span: 24,
      },
    },

  ],
});

function removeTableDataByRef(tableRef, record) {
  let tableData = tableRef?.value?.getDataSource();
  tableData = tableData.filter((item) => item.id !== record.id);
  tableRef.value.setTableData(tableData);
}

defineExpose({
  async getFormData() {
    const formValues = await validate();
    if (formValues && props.type === 'project_budget') {
      return projectBudgetPrams;
    }
    return null;
  },
  async setFormData(record, detailData = null) {
    await setFieldsValue({ ...record });

    commonProjectInfo.projectName = record.projectName;
    commonProjectInfo.projectId = record.projectId;
    commonProjectInfo.oldStatus = record.oldStatus;
    commonProjectInfo.oldStatusName = record.oldStatusName;
    if (props.type === 'project_budget') {
      projectBudgetPrams.ecrProjectBudgetList = record.ecrProjectBudgetList;
      ecrProjectBudgetListTableRef.value.setTableData(record.ecrProjectBudgetList);
      // budget.value.setTableData(detailData.deliverableVOS);
      // budget
      // return projectBudgetPrams;
    }
  },
});
const tableRef = ref();

</script>

<template>
  <div>
    <BasicForm
      v-if="type!==''"
      :key="type"
      @register="register"
    >
      <template #SelectSimpleProject="{ model, field }">
        <AInput
          v-model:value="model[field]"
          style="width: 100%"
          @click="SelectSimpleProjectClick"
        />
      </template>

      <template #ecrProjectBudgetList>
        <OrionTable
          ref="ecrProjectBudgetListTableRef"
          class="min-table"
          :options="ecrProjectBudgetListTableOptions"
        >
          <template #toolbarLeft>
            <BasicButton
              type="primary"
              icon="sie-icon-tianjiaxinzeng"
              @click="ecrProjectBudgetListAdd"
            >
              添加
            </BasicButton>
            <!--                <BasicButton-->
            <!--                  icon="sie-icon-shanchu"-->
            <!--                  @click="btnClick('delete');"-->
            <!--                >-->
            <!--                  移除-->
            <!--                </BasicButton>-->
          </template>
        </OrionTable>
      </template>
    </BasicForm>
  </div>
</template>

<style scoped lang="less">
.edit-btn{
  color: red;
}
.min-table{
  min-height: 300px;
}
</style>
