package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * ContractCostType VO对象
 *
 * <AUTHOR>
 * @since 2024-10-17 09:39:59
 */
@ApiModel(value = "ContractCostTypeVO对象", description = "合同计划成本类型")
@Data
public class ContractCostTypeVO extends  ObjectVO   implements Serializable{

            /**
         * 合同编号
         */
        @ApiModelProperty(value = "合同编号")
        private String contractNumber;


        /**
         * 成本类型编号
         */
        @ApiModelProperty(value = "成本类型编号")
        private String costTypeNumber;


        /**
         * 成本类型名称
         */
        @ApiModelProperty(value = "成本类型名称")
        private String costTypeName;


        /**
         * 成本名称
         */
        @ApiModelProperty(value = "成本名称")
        private String costName;


        /**
         * 单价
         */
        @ApiModelProperty(value = "单价")
        private BigDecimal unitPrice;


        /**
         * 单位
         */
        @ApiModelProperty(value = "单位")
        private String unit;


    

}
