package com.chinasie.orion.service.approval;
import java.lang.String;
import java.util.List;


import com.chinasie.orion.domain.dto.RiskPlanDTO;
import com.chinasie.orion.domain.dto.approval.ProjectApprovalRiskPlanDTO;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalRiskPlan;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalRiskPlanVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;

import com.chinasie.orion.sdk.metadata.page.Page;

/**
 * <p>
 * ProjectApprovalRiskPlan 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-27 16:10:46
 */
public interface ProjectApprovalRiskPlanService  extends OrionBaseService<ProjectApprovalRiskPlan> {


    /**
     *  详情
     *
     * * @param id
     */
    ProjectApprovalRiskPlanVO detail(String id, String pageCode)throws Exception;

    /**
     *  新增
     *
     * * @param projectApprovalRiskPlanDTO
     */
    String create(ProjectApprovalRiskPlanDTO projectApprovalRiskPlanDTO)throws Exception;

    /**
     *  编辑
     *
     * * @param projectApprovalRiskPlanDTO
     */
    Boolean edit(ProjectApprovalRiskPlanDTO projectApprovalRiskPlanDTO)throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<ProjectApprovalRiskPlanVO> pages(Page<ProjectApprovalRiskPlanDTO> pageRequest)throws Exception;


    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void  setEveryName(List<ProjectApprovalRiskPlanVO> vos)throws Exception;


    /**
     * 批量新增风险策划
     * @param approvalId
     * @param projectId
     * @param riskPlanList
     * @return
     * @throws Exception
     */
    Boolean saveBatchRiskPlan(String approvalId, String projectId, List<RiskPlanDTO> riskPlanList) throws Exception;

    List<ProjectApprovalRiskPlanVO> getListPlan(String approvalId) throws Exception;
}
