﻿<template>
  <BasicDrawer
    :width="1000"
    wrap-class-name="addTableNode"
    @register="modalRegister"
    @visible-change="visibleChange"
  >
    <div
      v-loading="loading"
      class="formContent"
    >
      <div class="formContent_content">
        <BasicForm @register="registerForm">
          <template #number="{ model, field }">
            <div style="display: flex;">
              <a-input
                v-model:value="model[field]"
                style="width: 100%"
                disabled
                placeholder="创建完成后自动生成编号"
              />
            </div>
          </template>
        </BasicForm>
      </div>
      <div class="addDocumentFooter">
        <ACheckBox
          v-if="formType=='add'"
          v-model:checked="checked"
          class="addModalFooterNext"
        >
          继续创建下一个
        </ACheckBox>
        <div class="btnStyle">
          <AButton
            size="large"
            @click="cancel"
          >
            取消
          </AButton>
          <AButton
            size="large"
            type="primary"
            :loading="loadingBtn"
            @click="confirm"
          >
            确认
          </AButton>
        </div>
      </div>
    </div>
    <SelectUserModal
      selectType="radio"
      @register="selectUserRegister"
    />
  </BasicDrawer>
</template>
<script lang="ts">
import {
  computed, defineComponent, nextTick, onMounted, reactive, ref, toRefs,
} from 'vue';
import {
  BasicDrawer, BasicForm, SelectUserModal, useDrawerInner, useForm,
} from 'lyra-component-vue3';
import {
  Button, Checkbox, Input, message,
} from 'ant-design-vue';
import { useRoute } from 'vue-router';
import Api from '/@/api';
import dayjs from 'dayjs';

export default defineComponent({
  name: 'AddTableNode',
  components: {
    BasicDrawer,
    ACheckBox: Checkbox,
    AButton: Button,
    BasicForm,
    AInput: Input,
    SelectUserModal,
  },
  props: {
    treeData: {
      type: Array,
      default: () => [],
    },
  },
  emits: ['update'],
  setup(props, { emit }) {
    const route = useRoute();
    const state :any = reactive({
      loadingBtn: false,
      checked: false,
      formType: 'add',
      formId: '',
      projectId: route.query.id,
      planTypeOptions: [],
      principalIdOptions: [],
      priorityLevelOptions: [],
      drawerData: {},
      principalName: '',
      parentId: '',
    });
    const tableRef = ref();
    const [modalRegister, { closeDrawer, setDrawerProps }] = useDrawerInner((drawerData) => {
      state.checked = false;
      state.parentId = drawerData.data.parentId || '';
      state.loading = false;
      clearValidate();
      resetFields();
      state.formType = drawerData.type;
      if (drawerData.type === 'add') {
        setDrawerProps({ title: '新增里程碑' });
        setFieldsValue({ planType: state.planTypeOptions[0].value });
      } else {
        state.principalName = drawerData.data.principalName;
        state.formId = drawerData.data.id;
        getStatusOptions(state.formId);
        setDrawerProps({ title: '编辑里程碑' });
        setFieldsValue(drawerData.data);
      }
    });
    const [
      registerForm,
      {
        setFieldsValue, clearValidate, resetFields, validateFields,
      },
    ] = useForm({
      actionColOptions: {
        span: 24,
      },
      showActionButtonGroup: false,
      schemas: [
        {
          field: 'name',
          component: 'Input',
          label: '名称',
          colProps: {
            span: 24,
          },
          componentProps: {
            placeholder: '请输入名称',
            maxlength: 50,
          },
          required: true,
        },
        {
          field: 'number',
          component: 'Input',
          label: '编号',
          colProps: {
            span: 11,
          },
          helpMessage: '创建完成后自动生成编号',
          slot: 'number',
          componentProps: {
            // disabled: true
            disabled: true,
          },
        },
        {
          field: 'principalId',
          component: 'Select',
          label: '负责人',
          colProps: {
            span: 11,
            offset: 2,
          },
          required: true,
          componentProps: {
            options: computed(() => state.principalIdOptions),
            filterOption: (inputValue, treeNode) => treeNode.props.label.includes(inputValue),
            onChange: (val) => {
              if (val) {
                const obj = state.principalIdOptions.find((s) => s.value === val);
                state.principalName = obj.label;
              } else {
                state.principalName = '';
              }
            },
          },
        },
        {
          field: 'planPredictEndTime',
          component: 'DatePicker',
          label: '结束日期',
          colProps: {
            span: 11,
          },
          componentProps: {
            placeholder: '请选择结束日期',
            style: { width: '100%' },
            showTime: true,
            valueFormat: 'YYYY-MM-DD HH:mm:ss',
          },
        },
        {
          field: 'remark',
          component: 'InputTextArea',
          label: '描述',
          colProps: {
            span: 24,
          },
          componentProps: {
            placeholder: '请输入内容',
            maxlength: 500,
            style: { height: '130px' },
          },
        },
      ],
    });
    const cancel = () => {
      closeDrawer();
    };

    const confirm = async () => {
      let formData = await validateFields();
      formData.projectId = state.projectId;
      formData.principalName = state.principalName;
      if (state.formType === 'edit') {
        formData.id = state.formId;
      }
      if (formData.planPredictEndTime) {
        formData.planPredictEndTime = dayjs(formData.planPredictEndTime).format('YYYY-MM-DD HH:mm:ss');
      }
      if (state.parentId) {
        formData.parentId = state.parentId;
      }
      state.loadingBtn = true;
      new Api('/pms')
        .fetch(formData, 'milestone', state.formType === 'add' ? 'POST' : 'PUT')
        .then(() => {
          message.success('操作成功');
          if (state.checked) {
            clearValidate();
            resetFields();
          } else {
            closeDrawer();
          }
          emit('update');
          nextTick(() => {
            state.loadingBtn = false;
          });
          // if (type === 'create-task' && isGo) {
          //   state.father.form = {
          //     ...index.addForm(),
          //     parentId: state.father.form.parentId,
          //     projectId: state.father.form.projectId,
          //   };
          // } else {
          //   emit('submit', true);
          // }
        })
        .catch(() => {
          state.loadingBtn = false;
          state.loading = false;
        });
    };
    onMounted(() => {
      new Api('/pms').fetch('', `project-role-user/getListByName/${state.projectId}?name=`, 'POST').then((res) => {
        state.principalIdOptions = res.map((s) => ({
          label: s.name,
          value: s.id,
        }));
      });
    });
    function getStatusOptions(id) {
      new Api('/pms').fetch('', `project-task-status/policy/status/list/${id}`, 'GET').then((res) => {
        state.statusOptions = res.map((s) => ({
          label: s.name,
          value: s.value,
        }));
      });
    }

    return {
      ...toRefs(state),
      modalRegister,
      registerForm,
      cancel,
      confirm,
    };
  },
});

</script>
<style lang="less" scoped>
.addTableNode{
  .scrollbar__view{
    height: 100%;
  }

  .ant-drawer-body{
    padding: 0px;
  }
  .formContent{
    display: flex;
    height: 100%;
    flex-direction: column;
    .formContent_content{
      padding: 0px 24px;
      flex: 1 1 auto;
    }
    .moreMessage{
      color: #5976d6;
      cursor: pointer;
    }
    .actions{
      span{
        color: #5172DC;
        padding:0px 10px;
        cursor: pointer;
      }
      .actions1{
        border-right: 1px solid #5172DC;
      }
    }
    .addDocumentFooter{
      padding: 15px;
      border-top: 1px solid #e9ecf2;
      width: 100%;
      display: flex;
      justify-content: space-between;
      .addModalFooterNext{
        line-height: 40px !important;
      }
      .btnStyle{
        flex: 1;
        text-align: right;
        .ant-btn{
          margin-left:10px;
          border-radius: 4px;
          padding: 4px 30px;
        }
        .canncel{
          background: #5172dc19;
          color: #5172DC;
        }
        .confirm{
          background: #5172dc;
          color: #ffffff;
        }
      }
    }
  }
  .ant-form-item{
    display: block;
  }
  .ant-form-item-control{
    width: 100% !important;
  }
}
</style>
