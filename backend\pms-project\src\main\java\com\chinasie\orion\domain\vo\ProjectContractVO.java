package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ProjectContract Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-24 09:56:13
 */
@ApiModel(value = "ProjectContractVO对象", description = "项目合同信息")
@Data
public class ProjectContractVO extends ObjectVO implements Serializable {

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String name;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String number;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectName;




    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    private String projectNumber;

    /**
     * 合同类别
     */
    @ApiModelProperty(value = "合同类别")
    private String contractCategory;

    /**
     * 合同类别名称
     */
    @ApiModelProperty(value = "合同类别名称")
    private String contractCategoryName;

    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型")
    private String contractType;

    /**
     * 合同类型名称
     */
    @ApiModelProperty(value = "合同类型名称")
    private String contractTypeName;

    /**
     * 合同负责人id
     */
    @ApiModelProperty(value = "合同负责人id")
    private String principalId;

    /**
     * 合同负责人工号
     */
    @ApiModelProperty(value = "合同负责人工号")
    private String principalCode;

    /**
     * 合同负责人名称
     */
    @ApiModelProperty(value = "合同负责人名称")
    private String principalName;

    /**
     * 责任部门id
     */
    @ApiModelProperty(value = "责任部门id")
    private String rspDeptId;

    /**
     * 责任部门名称
     */
    @ApiModelProperty(value = "责任部门名称")
    private String rspDeptName;


    /**
     * 合同金额
     */
    @ApiModelProperty(value = "合同金额")
    private BigDecimal contractMoney;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    private String currency;

    /**
     * 合同开始日期
     */
    @ApiModelProperty(value = "合同开始日期")
    private Date startDate;

    /**
     * 合同结束日期
     */
    @ApiModelProperty(value = "合同结束日期")
    private Date endDate;

    /**
     * 合同签订日期
     */
    @ApiModelProperty(value = "合同签订日期")
    private Date signDate;

    /**
     * 是否具有质保期
     */
    @ApiModelProperty(value = "是否具有质保期")
    private Boolean isGuaranteePeriod;

    /**
     * 是否具有质保金
     */
    @ApiModelProperty(value = "是否具有质保金")
    private Boolean isGuaranteeMoney;

    /**
     * 预计质保期到期日期
     */
    @ApiModelProperty(value = "预计质保期到期日期")
    private Date guaranteeEndDate;

    /**
     * 质保金额
     */
    @ApiModelProperty(value = "质保金额")
    private BigDecimal guaranteeAmt;

    /**
     * 合同其他信息
     */
    @ApiModelProperty(value = "合同其他信息")
    private String contractOtherInfo;

    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商")
    private String signedMainName;

    /**
     * 支付百分比
     */
    @ApiModelProperty(value = "支付百分比")
    private BigDecimal payPercentage;

    /**
     * 已支付金额
     */
    @ApiModelProperty(value = "已支付金额")
    private BigDecimal payAmt;

    /**
     * 创建人工号
     */
    @ApiModelProperty("创建人工号")
    private String creatorCode;

    @ApiModelProperty(value = "合同其他信息")
    private String dirId;
}
