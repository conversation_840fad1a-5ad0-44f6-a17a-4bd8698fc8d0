package com.chinasie.orion.constant;

import lombok.Getter;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum SchemeNodeTypeEnum {


    PLAN_SCHEME("plan", "计划"),
    MILESTONE_SCHEME("milestone", "里程碑"),
    TASK_MODULE_SCHEME("taskModule","任务模块");

    private final String nodeType;

    private final String desc;

    private static final Map<String, String> map = new HashMap<>(3);

    static {
        for (SchemeNodeTypeEnum value : SchemeNodeTypeEnum.values()) {
            map.put(value.getNodeType(), value.getDesc());
        }
    }

    SchemeNodeTypeEnum(String nodeType, String desc) {
        this.nodeType = nodeType;
        this.desc = desc;
    }


    public static String  getDescByNodeType(String nodeType) {
        if(StringUtils.hasText(nodeType)){
            return map.getOrDefault(nodeType,"");
        }
        return "";
    }

}
