<?xml version="1.0" encoding="UTF-8"?>
<project>
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.bouncycastle</groupId>
  <artifactId>bcpkix-jdk18on</artifactId>
  <packaging>jar</packaging>
  <name>Bouncy Castle PKIX, CMS, EAC, TSP, PKCS, OCSP, CMP, and CRMF APIs</name>
  <version>1.73</version>
  <description>The Bouncy Castle Java APIs for CMS, PKCS, EAC, TSP, CMP, CRMF, OCSP, and certificate generation. This jar contains APIs for JDK 1.8 and up. The APIs can be used in conjunction with a JCE/JCA provider such as the one provided with the Bouncy Castle Cryptography APIs.</description>
  <url>https://www.bouncycastle.org/java.html</url>
  <licenses>
    <license>
      <name>Bouncy Castle Licence</name>
      <url>https://www.bouncycastle.org/licence.html</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <scm>
    <url>https://github.com/bcgit/bc-java</url>
  </scm>
  <issueManagement>
     <system>GitHub</system>
     <url>https://github.com/bcgit/bc-java/issues</url>
  </issueManagement>
  <developers>
    <developer>
      <id>feedback-crypto</id>
      <name>The Legion of the Bouncy Castle Inc.</name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <dependencies>
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcprov-jdk18on</artifactId>
      <version>1.73</version>
      <type>jar</type>
    </dependency>
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcutil-jdk18on</artifactId>
      <version>1.73</version>
      <type>jar</type>
    </dependency>
  </dependencies>
</project>
