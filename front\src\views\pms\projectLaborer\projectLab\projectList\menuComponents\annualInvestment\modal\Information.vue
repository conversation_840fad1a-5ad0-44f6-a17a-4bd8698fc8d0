<template>
  <div class="information">
    <div
      v-if="showApprove"
      class="information-content"
    >
      <div class="content-title">
        <div
          class="information-title"
        >
          年度投资基础信息
        </div>
      </div>
      <div class="project-table">
        <a-row
          :gutter="[20, 20]"
          class="information-row"
        >
          <template
            v-for="item in foundationFieldList"
            :key="item.field"
          >
            <ACol
              :span="item.span"
              class="task-item"
            >
              <div class="item-title">
                {{ item.label }}:
              </div>
              <div
                class="item-value flex-te"
              >
                {{
                  item.formatter
                    ? item.formatter(parentsFormData[item.field])
                    : parentsFormData[item.field]
                }}
              </div>
            </ACol>
          </template>
        </a-row>
      </div>
    </div>
    <div class="information-content">
      <div class="content-title">
        <div
          class="information-title"
        >
          {{ formData.year }}年度投资计划执行情况
        </div>
      </div>
      <div class="project-table">
        <a-row
          :gutter="[20, 20]"
          class="information-row"
        >
          <template
            v-for="item in formFieldList"
            :key="item.field"
          >
            <ACol
              :span="item.span"
              class="task-item"
              v-bind="item.componentProps"
            >
              <div class="item-title">
                {{ item.label }}:
              </div>
              <div
                class="item-value"
                :class="{'item-value-row':item.span===24,'felx-te':item.span<24}"
              >
                {{
                  item.formatter
                    ? item.formatter(formData[item.field])
                    : formData[item.field]
                }}
              </div>
            </ACol>
          </template>
        </a-row>
      </div>
    </div>

    <div class="information-content">
      <div class="content-title">
        <div
          class="information-title information-title-flex"
        >
          <span>分月计划执行情况</span>
          <span>单位：万元</span>
        </div>
      </div>
      <div class="table-content">
        <div class="table-list">
          <div class="item-tr">
            <div class="item-th item-th1" />
            <div class="item-th-value">
              <template
                v-for="(item,index) in formData.monthInvestmentSchemes"
                :key="index"
              >
                <div class="item-th">
                  {{ item.name }}
                </div>
              </template>
            </div>
          </div>
          <div class="item-tr">
            <div class="item-td item-td1 flex-te">
              投资计划
            </div>
            <div class="item-td-value">
              <template
                v-for="(item,index) in formData.monthInvestmentSchemes"
                :key="index"
              >
                <div class="item-td">
                  {{ formatMoney(item.predicate) }}
                </div>
              </template>
            </div>
          </div>
          <div class="item-tr">
            <div class="item-td item-td1 flex-te">
              执行数
            </div>
            <div class="item-td-value">
              <template
                v-for="(item,index) in formData.monthInvestmentSchemes"
                :key="index"
              >
                <div class="item-td flex-te">
                  {{ formatMoney(item.actual) }}
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import {
  defineComponent, inject, reactive, toRefs, watch, ref, onMounted,
} from 'vue';
import {
  Row, Col, Input, Modal,
} from 'ant-design-vue';
import dayjs from 'dayjs';
import { OrionTable } from 'lyra-component-vue3';
import {
  formatMoney, foundationFieldList, initForm, drawerColumns,
} from '../index';
import { getPlanDetails } from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/investmentPlan/index';

export default defineComponent({
  name: 'Information',
  components: {
    ARow: Row,
    ACol: Col,
  },
  props: {
    showApprove: {
      type: Boolean,
      default: false,
    },
  },
  setup(props) {
    const formData = inject('formData', {});
    const state = reactive({
      formData: formData?.value,
      foundationFieldList,
      formFieldList: initForm(formData.value.yearName),
      parentsFormData: {},
    });

    watch(
      () => formData?.value,
      (val) => {
        state.formData = val;
        getParentFormData();
      },
    );
    onMounted(() => {
      getParentFormData();
    });
    function getParentFormData() {
      if (props.showApprove && state.formData.investmentId) {
        getPlanDetails(state.formData.investmentId).then((res) => {
          state.parentsFormData = res;
        });
      }
    }
    return {
      ...toRefs(state),
      formatMoney,
    };
  },
});
</script>
<style lang="less" scoped>
.information {
  min-height: 100%;
  padding-bottom: 20px;
  .im-table {
    height: 300px;
    width: 100%;
    overflow: hidden;
  }
  .table-content{
    width: 100%;
    padding: 20px;
  }
  .border-item{
    padding-bottom: 20px;
    border-bottom: 1px dashed #eee;
  }
  .table-list{
    overflow: auto;
    width: 100%;
    border: 1px solid #cccccc;
    border-right: 0;
    border-bottom: 0;
    .item-tr{
      display: flex;
      width: 100%;
      .item-th{
        background: #f2f2f2;
        text-align: center;
        padding: 5px;
      }
      .item-td{
        padding: 5px;
      }
      .item-th,.item-td{
        border-bottom:1px solid #cccccc;
        border-right:1px solid #cccccc;
      }
      .item-th1,.item-td1{
        width: 100px;
      }
      .item-td-value{
        width: calc(~'100% - 100px' );
        display: flex;
        .item-td{
          flex:1;
          min-width: 150px;
          text-align: right;
        }
      }
      .item-th-value{
        width: calc(~'100% - 100px' );
        display: flex;
        .item-th{
          flex:1;
          min-width: 150px;
        }
      }
    }
    .item-tr+.item-tr{
      border-top:0
    }
  }
}
.content-title{
  padding: 20px 20px 0 20px;
}
.information-row {
  padding: 20px;
  width: 100%;
}
.task-item {
  display: flex;
  line-height: 30px;
  min-height: 30px;
  padding: 5px 10px !important;

  .item-title {
    padding-right: 20px;
    color: #000000a5;
  }
  .item-value {
    flex: 1;
  }
  .item-value-row{
    min-height: 80px;
  }
}
</style>
