<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<packaging>jar</packaging>

	<parent>
		<groupId>cn.hutool</groupId>
		<artifactId>hutool-parent</artifactId>
		<version>5.8.20</version>
	</parent>

	<artifactId>hutool-jwt</artifactId>
	<name>${project.artifactId}</name>
	<description>JWT生成、解析和验证实现</description>

	<properties>
		<Automatic-Module-Name>cn.hutool.jwt</Automatic-Module-Name>

		<!-- versions -->
		<bouncycastle.version>1.72</bouncycastle.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-json</artifactId>
			<version>${project.parent.version}</version>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-crypto</artifactId>
			<version>${project.parent.version}</version>
		</dependency>
		<!-- 测试特殊算法 -->
		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcprov-jdk15to18</artifactId>
			<version>${bouncycastle.version}</version>
			<scope>test</scope>
		</dependency>
	</dependencies>
</project>
