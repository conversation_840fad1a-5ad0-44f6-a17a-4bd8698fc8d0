package com.chinasie.orion.service.review.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.base.api.domain.vo.SimpleUserVO;
import com.chinasie.orion.base.api.domain.vo.UserDeptVO;
import com.chinasie.orion.base.api.service.DeptBaseApiService;
import com.chinasie.orion.base.api.service.UserBaseApiService;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.review.MemberTypeEnum;
import com.chinasie.orion.constant.review.ScaleTypeEnum;
import com.chinasie.orion.domain.dto.review.ReviewMemberDTO;
import com.chinasie.orion.domain.entity.review.ReviewMember;
import com.chinasie.orion.domain.vo.review.ReviewMemberVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.review.ReviewMemberMapper;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.domain.vo.user.DeptUserRelationVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.DeptUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.review.ReviewMemberService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <p>
 * ReviewMember 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:03
 */
@Service
@Slf4j
public class ReviewMemberServiceImpl extends OrionBaseServiceImpl<ReviewMemberMapper, ReviewMember> implements ReviewMemberService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;
    @Resource
    private DeptBaseApiService deptBaseApiService;
    @Resource
    private UserBaseApiService userBaseApiService;
    @Resource
    private UserRedisHelper userRedisHelper;
    @Resource
    private DeptUserHelper deptUserHelper;
    @Resource
    private ReviewMemberMapper mapper;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ReviewMemberVO detail(String id, String pageCode) throws Exception {
        ReviewMember reviewMember = this.getById(id);
        ReviewMemberVO result = BeanCopyUtils.convertTo(reviewMember, ReviewMemberVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param reviewMemberDTO
     */
    @Override
    public String create(ReviewMemberDTO reviewMemberDTO) throws Exception {

        LambdaQueryWrapperX<ReviewMember> wrapperX = new LambdaQueryWrapperX<>(ReviewMember.class);
        wrapperX.eq(ReviewMember::getMainTableId,reviewMemberDTO.getMainTableId())
                .eq(ReviewMember::getUserId,reviewMemberDTO.getUserId())
                .eq(ReviewMember::getType,reviewMemberDTO.getType());
        boolean exists = mapper.exists(wrapperX);
        if (exists){
            throw new BaseException(PMSErrorCode.PMS_ERROR_DATA_EXIST, "成员已存在不可重复添加！");
        }

        ReviewMember reviewMember = BeanCopyUtils.convertTo(reviewMemberDTO, ReviewMember::new);
        reviewMember.setScale(ScaleTypeEnum.CREW.getValue());
        this.save(reviewMember);
        String rsp = reviewMember.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param reviewMemberDTO
     */
    @Override
    public Boolean edit(ReviewMemberDTO reviewMemberDTO) throws Exception {
        ReviewMember reviewMember = BeanCopyUtils.convertTo(reviewMemberDTO, ReviewMember::new);

        this.updateById(reviewMember);

        String rsp = reviewMember.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        for (String id : ids) {


        }
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ReviewMemberVO> pages(String mainTableId, Page<ReviewMemberDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ReviewMember> condition = new LambdaQueryWrapperX<>(ReviewMember.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ReviewMember::getCreateTime);

        condition.eq(ReviewMember::getMainTableId, mainTableId);

        Page<ReviewMember> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ReviewMember::new));

        PageResult<ReviewMember> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ReviewMemberVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ReviewMemberVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ReviewMemberVO::new);
        if (!CollectionUtil.isEmpty(vos)) {
            setEveryName(vos);
        }
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public void setEveryName(List<ReviewMemberVO> vos) throws Exception {
        List<String> userIds = vos.stream().map(ReviewMemberVO::getUserId).distinct().collect(Collectors.toList());
        Map<String, List<UserDeptVO>> deptByByUserId = deptBaseApiService.getDeptByByUserId(CurrentUserHelper.getOrgId(), userIds);
        Map<String, UserVO> userMapByUserIds = userRedisHelper.getUserMapByUserIds(userIds);
        vos.forEach(vo -> {
            String userId = vo.getUserId();
            List<UserDeptVO> userDeptVOS = deptByByUserId.get(userId);
            if (!CollectionUtil.isEmpty(userDeptVOS)){
                UserDeptVO userDeptVO = userDeptVOS.get(0);
                vo.setDeptName(userDeptVO.getDeptName());
            }
            UserVO userVO = userMapByUserIds.get(userId);
            if (ObjectUtil.isNotNull(userVO)){
                vo.setUserName(userVO.getName());
            }
            MemberTypeEnum memberTypeEnum = MemberTypeEnum.MemberEnumMapping(vo.getType());
            vo.setTypeName(memberTypeEnum.getDesc());
        });


    }

    @Override
    public Boolean setAdmin(String id) {
        ReviewMember reviewMember = mapper.selectById(id);
        if (ObjectUtil.isNull(reviewMember)){
            throw new BaseException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST,"数据不存在请刷新后再操作！");
        }
        if (MemberTypeEnum.EXPERT.getValue().equals(reviewMember.getType())){
            throw new BaseException(PMSErrorCode.PMS_ERROR_PARAMS,"文审专家不能设为组长！");
        }
        LambdaUpdateWrapper<ReviewMember> update = new LambdaUpdateWrapper<>(ReviewMember.class);
        update.set(ReviewMember::getScale,ScaleTypeEnum.CREW.getValue())
            .eq(ReviewMember::getMainTableId,reviewMember.getMainTableId())
            .eq(ReviewMember::getType,MemberTypeEnum.CREW.getValue());
        mapper.update(update);
        reviewMember.setScale(ScaleTypeEnum.ADMIN.getValue());
        mapper.updateById(reviewMember);
        return true;
    }

    @Override
    public List<ReviewMemberVO> getList(String adminTableId) throws Exception {
        LambdaQueryWrapperX<ReviewMember> wrapperX = new LambdaQueryWrapperX<>(ReviewMember.class);
        wrapperX.eq(ReviewMember::getMainTableId,adminTableId)
                .orderByDesc(ReviewMember::getCreateTime, ReviewMember::getScale);
        List<ReviewMember> reviewMembers = mapper.selectList(wrapperX);
        if (CollectionUtil.isEmpty(reviewMembers)){
            return new ArrayList<>();
        }
        List<ReviewMemberVO> reviewMemberVOS = BeanCopyUtils.convertListTo(reviewMembers, ReviewMemberVO::new);
        setEveryName(reviewMemberVOS);
        return reviewMemberVOS;
    }


    public static class ReviewMemberExcelListener extends AnalysisEventListener<ReviewMemberDTO> {

        private final List<ReviewMemberDTO> data = new ArrayList<>();

        @Override
        public void invoke(ReviewMemberDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ReviewMemberDTO> getData() {
            return data;
        }
    }


}
