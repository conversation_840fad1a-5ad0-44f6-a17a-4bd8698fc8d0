package com.chinasie.orion.domain.vo.investmentschemeReport;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;


@Data
@ColumnWidth(15)
public class ExportMonthFeedbackByExcelVO {


    /**
     * 序号
     */
    @ExcelProperty(value = {"投资计划执行月报报表  单位：万元", "序号"}, index = 0)
    private String order;

    /**
     * 月报编号
     */
    @ExcelProperty(value = {"投资计划执行月报报表  单位：万元", "月报编号"}, index = 1)
    private String number;

    /**
     * 月报名称
     */
    @ExcelProperty(value = {"投资计划执行月报报表  单位：万元", "月报名称"}, index = 2)
    private String name;


    /**
     * 年度（Y）
     */
    @ExcelProperty(value = {"投资计划执行月报报表  单位：万元", "年度（Y）"}, index = 3)
    private String year;


    /**
     * 月份（M）
     */
    @ExcelProperty(value = {"投资计划执行月报报表  单位：万元", "月份（M）"}, index = 4)
    private String month;


    /**
     * 月报状态
     */
    @ExcelProperty(value = {"投资计划执行月报报表  单位：万元", "月报状态"}, index = 5)
    private String statusName;

//
//    /**
//     * 项目所属公司
//     */
//    @ExcelProperty(value = {"投资计划执行月报报表  单位：万元", "项目所属公司"}, index = 6)
//    private String companyName;

    /**
     * 项目编码
     */
    @ExcelProperty(value = {"投资计划执行月报报表  单位：万元", "项目编码"}, index = 7)
    private String projectNumber;

    /**
     * 项目名称
     */
    @ExcelProperty(value = {"投资计划执行月报报表  单位：万元", "项目名称"}, index = 8)
    private String projectName;

    /**
     * 项目状态
     */
    @ExcelProperty(value = {"投资计划执行月报报表  单位：万元", "项目状态"}, index = 9)
    private String projectStatusName;


    /**
     * 项目处室
     */
    @ExcelProperty(value = {"投资计划执行月报报表  单位：万元", "项目处室"}, index = 10)
    private String rspDeptName;

    /**
     * 项目负责人
     */
    @ExcelProperty(value = {"投资计划执行月报报表  单位：万元", "项目负责人"}, index = 11)
    private String rspUserName;


    /**
     * 年度形象进度
     */
    @ExcelProperty(value = {"投资计划执行月报报表  单位：万元", "年度形象进度"}, index = 12)
    private String yearProcess;


    /**
     * 项目总体进度执行情况
     */
    @ExcelProperty(value = {"投资计划执行月报报表  单位：万元", "项目总体进度执行情况"}, index = 13)
    private String totalProcess;


    /**
     * 项目总体进度滞后情况
     */
    @ExcelProperty(value = {"投资计划执行月报报表  单位：万元", "项目总体进度滞后情况"}, index = 14)
    private String delayDesc;


    /**
     * 本月进度执行情况
     */
    @ExcelProperty(value = {"投资计划执行月报报表  单位：万元", "本月进度执行情况"}, index = 15)
    private String monthProcess;

    /**
     * 本月投资计划执行状态
     */
    @ExcelProperty(value = {"投资计划执行月报报表  单位：万元", "本月投资计划执行状态"}, index = 16)
    private String monthDoStatus;


    /**
     * 本月执行偏差原因及纠偏措施
     */
    @ExcelProperty(value = {"投资计划执行月报报表  单位：万元", "本月执行偏差原因及纠偏措施"}, index = 17)
    private String reason;

    /**
     * 上月实际执行
     */
    @ExcelProperty(value = {"投资计划执行月报报表  单位：万元", "上月实际执行"}, index = 18)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String lastPracticeDo = "";


    /**
     * 1-M月计划
     */
    @ExcelProperty(value = {"投资计划执行月报报表  单位：万元", "1-M月计划"}, index = 19)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String mplanDo = "";


    /**
     * 1-M月实际执行
     */
    @ExcelProperty(value = {"投资计划执行月报报表  单位：万元", "1-M月实际执行"}, index = 20)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String mpracticeDo = "";

    /**
     * 1-M月计划执行率
     */
    @ExcelProperty(value = {"投资计划执行月报报表  单位：万元", "1-M月计划执行率"}, index = 21)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String mpracticeDoRate;


    /**
     * 下月进度计划
     */
    @ExcelProperty(value = {"投资计划执行月报报表  单位：万元", "下月进度计划"}, index = 22)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String nextProcess;


    /**
     * Y年投资计划
     */
    @ExcelProperty(value = {"投资计划执行月报报表  单位：万元", "Y年投资计划"}, index = 23)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String yinvestmentPlan = "";


    /**
     * 年度计划完成率
     */
    @ExcelProperty(value = {"投资计划执行月报报表  单位：万元", "年度计划完成率"}, index = 24)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String yearCompleteRate;

    /**
     * 备注
     */
    @ExcelProperty(value = {"投资计划执行月报报表  单位：万元", "备注"}, index = 25)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String remark;

}
