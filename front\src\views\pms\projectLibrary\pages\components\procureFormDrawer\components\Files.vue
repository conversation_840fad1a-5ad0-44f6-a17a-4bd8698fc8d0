<template>
  <div class="files-wrap">
    <UploadList
      class="pay-node-table"
      :listData="dataSource"
      type="modal"
      :on-change="setFileData"
    />
  </div>
</template>
<script setup lang="ts">
import { UploadList } from 'lyra-component-vue3';
import {
  inject, onMounted, Ref, ref, unref,
} from 'vue';

const dataId: Ref = inject('dataId');
const detailsData: Ref<Record<string, any>> = inject('detailsData');
const dataSource = ref([]);

onMounted(() => {
  if (dataId.value) {
    setFileData(detailsData.value?.documentVOList ?? []);
  }
});

function setFileData(listData:any[]) {
  dataSource.value = listData || [];
}

function getData() {
  return dataSource.value;
}

defineExpose({
  getData,
});
</script>

<style scoped lang="less">

.files-wrap {
  padding: ~`getPrefixVar('content-padding-top')` ~`getPrefixVar('content-padding-left')`;
  height: 100%;
  position: relative;
  overflow: hidden;
}
</style>
