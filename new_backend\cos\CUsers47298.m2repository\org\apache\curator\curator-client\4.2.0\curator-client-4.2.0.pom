<?xml version="1.0" encoding="UTF-8"?><!--
    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.apache.curator</groupId>
        <artifactId>apache-curator</artifactId>
        <version>4.2.0</version>
    </parent>

    <artifactId>curator-client</artifactId>
    <version>4.2.0</version>
    <packaging>bundle</packaging>

    <name>Curator Client</name>
    <description>Low-level API</description>
    <inceptionYear>2011</inceptionYear>

    <properties>
        <osgi.import.package>
            *
        </osgi.import.package>
        <osgi.export.package>
            org.apache.curator*;version="${project.version}";-noimport:=true
        </osgi.export.package>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <executions>
                    <execution>
                        <id>apache-curator-guava-shader</id>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <phase>package</phase>
                        <configuration>
                            <artifactSet>
                                <includes>
                                    <include>com.google.guava:guava</include>
                                    <include>com.google.guava:listenablefuture</include>
                                    <include>com.google.guava:failureaccess</include>
                                </includes>
                            </artifactSet>
                            <filters>
                                <filter>
                                    <artifact>com.google.guava:guava</artifact>
                                    <excludes>
                                        <exclude>com/google/common/base/Function.class</exclude>
                                        <exclude>com/google/common/base/Predicate.class</exclude>
                                        <exclude>com/google/common/reflect/TypeToken.class</exclude>
                                        <exclude>META-INF/**</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>

