package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.lang.String;

/**
 * ProjectPurchaseReceiveInfo Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-06 09:16:22
 */
@ApiModel(value = "ProjectPurchaseReceiveInfoDTO对象", description = "项目采购收货方信息")
@Data
public class ProjectPurchaseReceiveInfoDTO extends ObjectDTO implements Serializable{

    /**
     * 收货人姓名
     */
    @ApiModelProperty(value = "收货人姓名")
    @NotBlank(message = "收货人姓名不能为空")
    @Size(max = 100, message = "收货人姓名过长，建议控制在100字符以内")
    private String receivePerson;

    /**
     * 收货人电话
     */
    @ApiModelProperty(value = "收货人电话")
    @NotBlank(message = "收货人电话不能为空")
    @Size(max = 100, message = "收货人电话过长，建议控制在100字符以内")
    @Pattern(regexp = "^([0-9]*)$", message = "收货人电话格式错误")
    private String receivePhone;

    /**
     * 收货地址
     */
    @ApiModelProperty(value = "收货地址")
    @Size(max = 100, message = "收货地址过长，建议控制在100字符以内")
    private String receiveAddress;

    /**
     * 收货人邮箱
     */
    @ApiModelProperty(value = "收货人邮箱")
    @Size(max = 100, message = "收货人邮箱过长，建议控制在100字符以内")
    private String receiveEmail;

    /**
     * 采购单id
     */
    @ApiModelProperty(value = "采购单id")
    private String purchaseId;

}
