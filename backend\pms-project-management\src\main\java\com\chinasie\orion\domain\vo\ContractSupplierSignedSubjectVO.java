package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.util.List;
/**
 * ContractSupplierSignedSubject VO对象
 *
 * <AUTHOR>
 * @since 2024-05-28 21:50:47
 */
@ApiModel(value = "ContractSupplierSignedSubjectVO对象", description = "乙方签约主体")
@Data
public class ContractSupplierSignedSubjectVO extends ObjectVO implements Serializable{

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    private String contractId;


    /**
     * 签约主体名称
     */
    @ApiModelProperty(value = "签约主体名称")
    private String signedMainName;

    /**
     * 乙方客户名称
     */
    @ApiModelProperty(value = "乙方客户名称")
    private String custPersonName;

    /**
     * 公司税号
     */
    @ApiModelProperty(value = "公司税号")
    private String companyDutyParagraph;


    /**
     * 主要联系人
     */
    @ApiModelProperty(value = "主要联系人")
    private String mainContactPerson;


    /**
     * 主要联系人电话
     */
    @ApiModelProperty(value = "主要联系人电话")
    private String mainContactPhone;


    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    private String contractEmail;

    /**
     * 技术联系人
     */
    @ApiModelProperty(value = "技术联系人")
    private String techContactPerson;

    /**
     * 技术联系人电话
     */
    @ApiModelProperty(value = "技术联系人电话")
    private String techContactPhone;

    /**
     * 技术联系部门
     */
    @ApiModelProperty(value = "技术联系部门")
    private String techContactDept;

    /**
     * 联系地址
     */
    @ApiModelProperty(value = "联系地址")
    private String contactAddress;


}
