package com.chinasie.orion.domain.entity;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.tree.OrionTreeNode;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * CenterJobManage Entity对象
 *
 * <AUTHOR>
 * @since 2024-11-14 10:35:27
 */
@TableName(value = "pmsx_center_job_manage")
@ApiModel(value = "CenterJobManageEntity对象", description = "中心作业管理")
@Data

public class CenterJobManage extends ObjectEntity implements Serializable {

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    @TableField(value = "number")
    private String number;

    /**
     * 作业名
     */
    @ApiModelProperty(value = "作业名称")
    @TableField(value = "name")
    private String name;

    @ApiModelProperty(value = "作业类型")
    @TableField(value = "job_type")
    private String jobType;

    /**
     * N/O
     */
    @ApiModelProperty(value = "N/O")
    @TableField(value = "n_or_o")
    private String nOrO;

    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    @TableField(value = "repair_round")
    private String repairRound;


    /**
     * 工作中心
     */
    @ApiModelProperty(value = "工作中心")
    @TableField(value = "work_center")
    private String workCenter;

    /**
     * 责任中心
     */
    @ApiModelProperty(value = "责任中心")
    @TableField(value = "rsp_dept")
    private String rspDept;

    /**
     * 作业基地
     */
    @ApiModelProperty(value = "作业基地")
    @TableField(value = "job_base_name")
    private String jobBaseName;

    /**
     * 计划开工时间
     */
    @ApiModelProperty(value = "计划开工时间")
    @TableField(value = "begin_time")
    private Date beginTime;

    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    @TableField(value = "end_time")
    private Date endTime;

    /**
     * 计划工期
     */
    @ApiModelProperty(value = "计划工期")
    @TableField(value = "work_duration")
    private Integer workDuration;

    /**
     * 实际开工时间
     */
    @ApiModelProperty(value = "实际开工时间")
    @TableField(value = "actual_begin_time")
    private Date actualBeginTime;

    /**
     * 实际完工时间
     */
    @ApiModelProperty(value = "实际完工时间")
    @TableField(value = "actual_end_time")
    private Date actualEndTime;

    /**
     * 作业阶段：作业状态
     */
    @ApiModelProperty(value = "作业阶段：作业状态")
    @TableField(value = "phase")
    private String phase;

    /**
     * 是否匹配（0未匹配  1匹配）
     */
    @ApiModelProperty(value = "是否匹配（0未匹配  1匹配）")
    @TableField(value = "match_up")
    private Integer matchUp;

    /**
     * 是否已选择(0未选择   1已选择)
     */
    @ApiModelProperty("是否已选择(0未选择   1已选择)")
    @TableField(value = "selected")
    Integer selected;

    @ApiModelProperty("负责人Id")
    @TableField(value = "rsp_user_id")
    String rspUserId;

    @ApiModelProperty("负责人姓名")
    @TableField(value = "rsp_user_name")
    String rspUserName;
}
