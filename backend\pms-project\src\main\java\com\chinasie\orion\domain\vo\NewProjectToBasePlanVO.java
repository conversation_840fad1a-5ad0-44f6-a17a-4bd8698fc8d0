package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * NewProjectToBasePlan Entity对象
 *
 * <AUTHOR>
 * @since 2023-03-30 14:54:30
 */
@ApiModel(value = "NewProjectToBasePlanVO对象", description = "项目和综合计划的关系表（1;N）")
@Data
public class NewProjectToBasePlanVO extends ObjectVO implements Serializable{

            /**
         * 修改人ID
         */
        @ApiModelProperty(value = "修改人ID")
        private String modifyId;

        /**
         * 创建时间
         */
        @ApiModelProperty(value = "创建时间")
        private Timestamp createTime;

        /**
         * 组织Id
         */
        @ApiModelProperty(value = "组织Id")
        private String orgId;

        /**
         * 创建人Id
         */
        @ApiModelProperty(value = "创建人Id")
        private String creatorId;

        /**
         * 修改时间
         */
        @ApiModelProperty(value = "修改时间")
        private Timestamp modifyTime;

        /**
         * 平台Id
         */
        @ApiModelProperty(value = "平台Id")
        private String platformId;

        /**
         * 是否删除
         */
        @ApiModelProperty(value = "是否删除")
        private Integer logicStatus;

        /**
         * 项目ID
         */
        @ApiModelProperty(value = "项目ID")
        private String projectId;

        /**
         * 综合计划ID
         */
        @ApiModelProperty(value = "综合计划ID")
        private String basePlanId;

        /**
         * 来源类型
         */
        @ApiModelProperty(value = "来源类型")
        private String sourceType;

        /**
         * 来源类型名称
         */
        @ApiModelProperty(value = "来源类型名称")
        private String sourceTypeName;


        /**
         * 责任人
         */
        @ApiModelProperty(value = "责任人")
        private String resUserName;

        /**
         * 名称
         */
        @ApiModelProperty(value = "名称")
        private String sourceName;

        /**
         * 综合计划编号
         */
        @ApiModelProperty(value = "综合计划编号")
        private String basePlanNumber;

        /**
         * 项目编号
         */
        @ApiModelProperty(value = "项目编号")
        private String projectNumber;

        /**
         * 关联类型(0:主动,1:被动)
         */
        @ApiModelProperty(value = "关联类型(0:主动,1:被动)")
        private String relationType;

    }
