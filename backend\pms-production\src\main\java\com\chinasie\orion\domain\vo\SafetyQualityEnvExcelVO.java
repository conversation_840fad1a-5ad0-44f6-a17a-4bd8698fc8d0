package com.chinasie.orion.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/07/15:01
 * @description:
 */

@ApiModel(value = "SafetyQualityEnvVO对象", description = "安质环")
@Data
@ExcelIgnoreUnannotated
public class SafetyQualityEnvExcelVO extends ObjectVO implements Serializable {
    @ExcelProperty(value= "序号",index = 0)
    private Integer sort;


    /**
     *    *  序号、隐患编号、事件主题、事件等级、事件地点、检查人、检查人所在部门、
     */
    @ExcelProperty(value = "事件主题",index = 1)
    private String eventTopic;

    /**
     * 隐患编号
     */
    @ExcelProperty(value = "隐患编号",index = 2)
    private String hiddenDangerCode;
    /**
     * 事件等级
     */
    @ExcelProperty(value = "事件等级",index = 3)
    private String eventLevel;

    /**
     * 事件地点
     */
    @ExcelProperty(value = "事件地点",index = 4)
    private String eventLocation;


    @ExcelProperty(value = "检查人编号",index = 5)
    private String reviewerNumber;

    @ExcelProperty(value = "检查人名称",index = 6)
    private String reviewerName;

    @ExcelProperty(value = "检查人所在部门",index = 7)
    private String deptName;
/**
 *                      责任中心、事件描述、事件位置
 *      *          *  、分类类型、隐患类型、事发日期、大修轮次、
 *
 */

    /**
     * 责任中心
     */
    @ExcelProperty(value = "责任中心",index = 8)
    private String rspCenter;

    /**
     * 事件类型
     */
    @ExcelProperty(value = "事件描述",index = 9)
    private String eventDesc;

    /**
     * 事件位置
     */
//    @ExcelProperty(value = "事件位置",index = 10)
    @ExcelIgnore
    private String eventPosition;

    /**
     * 分类类型
     */
//    @ExcelProperty(value = "分类类型",index = 11)
    @ExcelIgnore
    private String classificationType;
    /**
     * 隐患类型
     */
//    @ExcelProperty(value = "隐患类型",index = 12)
    @ExcelIgnore
    private String hiddenDangerType;
    /**
     * 事发日期
     */
    @ExcelProperty(value = "事发日期",index = 10)
    private Date occurrenceDate;

    /**
     * 大修轮次
     */
    @ExcelProperty(value = "大修轮次",index = 11)
    private String majorRepairTurn;

    /**
     * 隐患/事件领域、事件类型、
     *  *      *          *  、是否已关闭、直接责任部门、金字塔类别、是否考核、考核级别。
     */
    /**
     * 隐患/事件领域
     */
    @ExcelProperty(value = "隐患/事件领域",index = 12)
    private String hiddenEvent;
    /**
     * 事件类型
     */
    @ExcelProperty(value = "事件类型",index = 13)
    private String eventType;
    /**
     * 是否已关闭
     */
    @ExcelProperty(value = "是否已关闭",index = 14)
    private String isClosed;
    @ExcelProperty(value = "直接责任部门",index = 15)
    private String rspDeptName;
    @ExcelProperty(value = "金字塔类别名称",index = 16)
    private String pyramidCategoryName;

    /**
     * 是否考核
     */
    @ExcelProperty(value = "是否考核",index = 17)
    private String isAssessed;

    @ExcelProperty(value = "考核级别名称",index = 18)
    private String assessmentLevelName;
}

