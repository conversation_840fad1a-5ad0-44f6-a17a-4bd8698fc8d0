package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.ProjectSchemeDTO;
import com.chinasie.orion.domain.entity.ProjectSchemeApplyApproval;
import com.chinasie.orion.domain.vo.ProjectSchemeVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;

import java.util.List;
import java.util.Map;

/**
 * ProjectSchemeApplyApprovalService
 *
 * @author: yangFy
 * @date: 2023/4/19 16:07
 * @description:
 * <p>
 * 项目计划申请审批
 * </p>
 */
public interface ProjectSchemeApplyApprovalService extends OrionBaseService<ProjectSchemeApplyApproval> {
    /**
     * 计划调整
     *
     * @param projectSchemeDTO
     * @return
     */
    Boolean modify(ProjectSchemeDTO projectSchemeDTO) throws Exception;

    /**
     * 同意调整
     *
     * @param id
     * @param projectSchemeApplyApproval
     * @return
     */
    Boolean agree(String id, ProjectSchemeApplyApproval projectSchemeApplyApproval) throws Exception;

    /**
     * 驳回调整
     *
     * @param id
     * @param projectSchemeApplyApproval
     * @return
     */
    Boolean reject(String id, ProjectSchemeApplyApproval projectSchemeApplyApproval) throws Exception;

    /**
     * 计划申请内容详情
     * @param id
     * @return
     */
    ProjectSchemeVO getDetail(String id) throws Exception;


    Integer getApprovalStatus(String schemeId);


    Map<String,Integer> getApprovalStatus(List<String> schemeIds);
}
