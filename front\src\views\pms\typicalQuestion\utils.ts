import {
  getDict, getDictBy<PERSON><PERSON>ber, isPower, openDrawer,
} from 'lyra-component-vue3';
import {
  computed, h, ref, Ref,
} from 'vue';
import dayjs from 'dayjs';
import { Router } from 'vue-router';
import { message } from 'ant-design-vue';
import Api from '/@/api';
import { useUserStore } from '/@/store/modules/user';

const userInfo:any = useUserStore().getUserInfo;
let validateTime = async (rule, value) => {
  if (!value) {
    return Promise.reject('请选择时间');
  }
  return Promise.resolve();
};

// 表格组件新增、编辑抽屉
export function openFormDrawer(component: any, record?: Record<string, any>, cb?: () => void): void {
  const drawerRef: Ref = ref();
  openDrawer({
    title: record?.id ? '编辑问题' : '新增问题',
    width: 1200,
    content() {
      return h(component, {
        ref: drawerRef,
        formId: record?.id,
        formType: record?.id ? 'edit' : 'add',
        drawerData: record,
      });
    },
    async onOk(): Promise<void> {
      await drawerRef.value.onSubmit();
      cb?.();
    },
  });
}

// 格式化表格
export function formatTableColumns(router:Router, isPower): any[] {
  return [
    {
      title: '名称',
      dataIndex: 'name',
      customRender({ record, text }) {
        return isPower('PMS_DXWTK_container_02_button_05', record.rdAuthList) ? h('span', {
          class: 'action-btn',
          onClick: () => openDetails(record, router),
        }, text) : text;
      },
    },
    {
      title: '问题编号',
      dataIndex: 'number',
    },
    {
      title: '问题提出人',
      dataIndex: 'exhibitorName',
    },
    {
      title: '提出日期',
      dataIndex: 'proposedTime',
      customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD') : ''),
      width: 150,
    },
    {
      title: '期望完成日期',
      dataIndex: 'predictEndTime',
      customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD') : ''),
      width: 150,
    },
    {
      title: '优先级',
      dataIndex: 'priorityLevelName',
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 150,
      slots: { customRender: 'status' },
    },
    {
      title: '问题类型',
      dataIndex: 'questionTypeName',
    },
    {
      title: '问题负责人',
      dataIndex: 'principalName',
    },
    {
      title: '采纳情况',
      dataIndex: 'adoptionSituationName',
      type: 'select',
    },
    {
      title: '修改日期',
      dataIndex: 'modifyTime',
      customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : ''),
      width: 180,
    },
    {
      title: '操作',
      dataIndex: 'actions',
      width: 200,
      align: 'left',
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
  ];
}
function openDetails(record, router) {
  router.push({
    name: 'PMSTypicalQuestionDetails',
    params: {
      id: record.id,
    },
  });
}

// 编辑页面表单回显
// 详情基本信息回显
export function setBasicInfo(list: any[]): any[] {
  list = list.map((item) => {
    if (item.formatter) {
      item.valueRender = ({ record }) => item.formatter(record[item.field], record);
    }
    item.wrap = true;
    return item;
  });
  return list;
}

// 格式化表格行权限配置
export function formatActionsPower(actions: any[]): any[] {
  return actions.map((item) => ({
    isShow: (record: Record<string, any>) => isPower(item.code, record.rdAuthList),
    ...item,
  }));
}
export function initFlowForm(method, optionsData, getProductEstimateList) {
  return [
    {
      field: 'name',
      label: '标题',
      colProps: { span: 24 },
      required: true,
      componentProps: {
        allowClear: true,
        placeholder: '请输入',
      },
      component: 'Input',
    },
    {
      field: 'principalName',
      label: '问题负责人',
      colProps: { span: 8 },
      componentProps: {
        allowClear: true,
        onClick() {
          method.openSelectUser('principalName');
        },
        addonAfter: h(
          'span',
          {
            onClick: () => {
              method.openSelectUser('principalName');
            },
          },
          '请选择',
        ),
        async onChange(value) {
          message.info('请选择');
          method.clearField({ principalId: '' });
        },
      },
      required: true,
      component: 'Input',
    },
    {
      field: 'questionType',
      label: '问题类型',
      colProps: { span: 8 },
      required: true,
      componentProps: {
        placeholder: '请选择',
        api: () => getDict('dict683de1d8a1f74f0798b1d5444f42e3a7'),
        labelField: 'description',
        valueField: 'number',
      },
      component: 'ApiSelect',
    },
    {
      field: 'exhibitorName',
      label: '问题提出人',
      required: true,
      colProps: { span: 8 },
      defaultValue: userInfo?.name,
      componentProps: {
        allowClear: true,
        onClick() {
          method.openSelectUser('exhibitorName');
        },
        addonAfter: h(
          'span',
          {
            onClick: () => {
              method.openSelectUser('exhibitorName');
            },
          },
          '请选择',
        ),
        async onChange(value) {
          message.info('请选择');
          method.clearField('exhibitorName');
        },
      },
      component: 'Input',
    },
    {
      field: 'proposedTime',
      label: '问题提出日期',
      rules: [
        {
          required: true,
          trigger: 'change',
          validator: validateTime,
        },
      ],
      colProps: { span: 8 },
      componentProps: {
        allowClear: true,
        placeholder: '请选择',
        valueFormat: 'YYYY-MM-DD',
      },
      component: 'DatePicker',
    },
    {
      field: 'priorityLevel',
      component: 'ApiSelect',
      label: '优先级:',
      colProps: {
        span: 8,
      },
      componentProps: {
        api: () => getDict('dictc56421e19b264d9c91394c48e447e4cb'),
        labelField: 'description',
        valueField: 'number',
      },
    },
    {
      field: 'predictEndTime',
      label: '期望完成日期',
      colProps: { span: 8 },
      componentProps: {
        allowClear: true,
        placeholder: '请选择',
        valueFormat: 'YYYY-MM-DD',
      },
      component: 'DatePicker',
    },
    {
      field: 'content',
      label: '问题描述',
      required: true,
      colProps: { span: 24 },
      componentProps: {
        placeholder: '请输入',
        maxlength: 200,
        showCount: true,
      },
      component: 'InputTextArea',
    },
    {
      component: 'Input',
      colProps: {
        span: 24,
      },
      slot: 'scoreInfo',
      ifShow: ({ model }) => model?.questionType,
    },
    {
      field: 'stage',
      label: '阶段',
      required: true,
      colProps: { span: 8 },
      componentProps: {
        placeholder: '请选择',
        api: () => getDictByNumber('stage'),
        labelField: 'description',
        valueField: 'number',
      },
      component: 'ApiSelect',
      ifShow: ({ model }) => model?.questionType === 'questionType_1',
    },
    {
      field: 'processLink',
      label: '过程环节',
      required: true,
      colProps: { span: 8 },
      componentProps: {
        placeholder: '请选择',
        api: () => getDictByNumber('processLink'),
        labelField: 'description',
        valueField: 'number',
      },
      component: 'ApiSelect',
      ifShow: ({ model }) => model?.questionType === 'questionType_1',
    },
    {
      field: 'processClassifi',
      label: '过程分类',
      // required: true,
      colProps: { span: 8 },
      componentProps: {
        placeholder: '请选择',
        options: [],
      },
      component: 'Select',
      ifShow: ({ model }) => model?.questionType === 'questionType_1',
    },
    {
      field: 'problemPhenomenonOne',
      label: '问题现象一级分类',
      required: true,
      colProps: { span: 8 },
      componentProps: {
        placeholder: '请选择',
        options: computed(() => optionsData.problemPhenomenonOneOptions.value),
        fieldNames: {
          label: 'description',
          value: 'number',
        },
        async onChange(value): Promise<void> {
          method.clearField({
            problemPhenomenonTwo: '',
            problemPhenomenonTh: [],
          });
          if (value) {
            let selectedItem = optionsData.problemPhenomenonOneOptions.value.find((item) => item.number === value);
            optionsData.problemPhenomenonTwoOptions.value = selectedItem?.children || [];
          } else {
            optionsData.problemPhenomenonTwoOptions.value = [];
          }
          optionsData.problemPhenomenonThOptions.value = [];
        },
      },
      component: 'Select',
      ifShow: ({ model }) => model?.questionType === 'questionType_1',
    },
    {
      field: 'problemPhenomenonTwo',
      label: '问题现象二级分类',
      required: true,
      colProps: { span: 8 },
      componentProps: {
        fieldNames: {
          label: 'description',
          value: 'number',
        },
        placeholder: '请选择',
        options: computed(() => optionsData.problemPhenomenonTwoOptions.value),
        async onChange(value): Promise<void> {
          method.clearField({ problemPhenomenonTh: [] });
          if (value) {
            let selectedItem = optionsData.problemPhenomenonTwoOptions.value.find((item) => item.number === value);
            optionsData.problemPhenomenonThOptions.value = selectedItem?.children || [];
          } else {
            optionsData.problemPhenomenonThOptions.value = [];
          }
        },
      },
      component: 'Select',
      ifShow: ({ model }) => model?.questionType === 'questionType_1',
    },
    {
      field: 'problemPhenomenonTh',
      label: '问题现象三级分类',
      colProps: { span: 8 },
      componentProps: {
        fieldNames: {
          label: 'description',
          value: 'number',
        },
        onChange(value) {
          if (Array.isArray(value) && value.length === 0) return;
          // 清除问题现象二级分类的值(因为问题现象二级分类的值是根据问题现象一级分类的值动态变化的
          method.clearField({ problemPhenomenonTh: [value[value.length - 1]] });
        },
        mode: 'tags',
        placeholder: '请选择',
        options: computed(() => optionsData.problemPhenomenonThOptions.value),
      },
      component: 'Select',
      ifShow: ({ model }) => model?.questionType === 'questionType_1',
    },
    {
      field: 'problemLevel',
      label: '问题等级分类',
      required: true,
      colProps: { span: 8 },
      componentProps: {
        placeholder: '请选择',
        api: () => getDictByNumber('problemLevel'),
        labelField: 'description',
        valueField: 'number',
      },
      component: 'ApiSelect',
      ifShow: ({ model }) => model?.questionType === 'questionType_1',
    },
    {
      field: 'productNumber',
      label: '产品编码',
      colProps: { span: 8 },
      componentProps: {
        placeholder: '请选择',
        options: computed(() => optionsData.productNumberOptions.value),
        fieldNames: {
          label: 'number',
          value: 'number',
        },
        async onChange(value): Promise<void> {
          if (value) {
            let itemData = optionsData.productNumberOptions.value.find((item) => item.number === value);
            optionsData.materialNumberOptions.value = await getProductEstimateList({ productId: itemData.id });
          } else {
            optionsData.materialNumberOptions.value = [];
          }
          method.clearField({ materialNumber: '' });
        },
      },
      component: 'Select',
      ifShow: ({ model }) => model?.questionType === 'questionType_1',
    },
    {
      field: 'materialNumber',
      label: '物料编码',
      colProps: { span: 8 },
      componentProps: {
        placeholder: '请选择',
        options: computed(() => optionsData.materialNumberOptions.value),
        fieldNames: {
          label: 'number',
          value: 'number',
        },
      },
      component: 'Select',
      ifShow: ({ model }) => model?.questionType === 'questionType_1',
    },
    {
      field: 'productionOrders',
      label: '生产订单',
      colProps: { span: 8 },
      componentProps: {
        allowClear: true,
        placeholder: '请输入',
      },
      component: 'Input',
      ifShow: ({ model }) => model?.questionType === 'questionType_1',
    },
    {
      field: 'productNumberSn',
      label: '产品编号（SN）',
      colProps: { span: 8 },
      componentProps: {
        allowClear: true,
        placeholder: '请输入',
      },
      component: 'Input',
      ifShow: ({ model }) => model?.questionType === 'questionType_1',
    },
    {
      component: 'Input',
      colProps: {
        span: 24,
      },
      slot: 'slotInfo',
      ifShow: ({ model }) => model?.questionType === 'questionType_1',
    },
    {
      field: 'reasionOne',
      label: '一级原因分类',
      required: true,
      colProps: { span: 8 },
      componentProps: {
        placeholder: '请选择',
        options: computed(() => optionsData.reasionOneOptions.value),
        fieldNames: {
          label: 'description',
          value: 'number',
        },
        async onChange(value): Promise<void> {
          method.clearField({
            reasionTwo: '',
            reasionThree: '',
          });
          if (value) {
            let selectedItem = optionsData.reasionOneOptions.value.find((item) => item.number === value);
            optionsData.reasionTwoOptions.value = selectedItem.children || [];
          } else {
            optionsData.reasionTwoOptions.value = [];
          }
          optionsData.reasionThreeOptions.value = [];
        },
      },
      component: 'Select',
      ifShow: ({ model }) => model?.questionType === 'questionType_1',
    },
    {
      field: 'reasionTwo',
      label: '二级原因分类',
      required: true,
      colProps: { span: 8 },
      componentProps: {
        placeholder: '请选择',
        options: computed(() => optionsData.reasionTwoOptions.value),
        fieldNames: {
          label: 'description',
          value: 'number',
        },
        async onChange(value): Promise<void> {
          method.clearField({ reasionThree: '' });
          if (value) {
            let selectedItem = optionsData.reasionTwoOptions.value.find((item) => item.number === value);
            optionsData.reasionThreeOptions.value = selectedItem.children || [];
          } else {
            optionsData.reasionThreeOptions.value = [];
          }
        },
      },
      component: 'Select',
      ifShow: ({ model }) => model?.questionType === 'questionType_1',
    },
    {
      field: 'reasionThree',
      label: '三级原因分类',
      required: true,
      colProps: { span: 8 },
      componentProps: {
        fieldNames: {
          label: 'description',
          value: 'number',
        },
        placeholder: '请选择',
        options: computed(() => optionsData.reasionThreeOptions.value),
      },
      component: 'Select',
      ifShow: ({ model }) => model?.questionType === 'questionType_1',
    },
    {
      field: 'reasionRemark',
      label: '原因分析描述',
      required: true,
      colProps: { span: 24 },
      componentProps: {
        placeholder: '请输入',
        maxlength: 200,
        showCount: true,
      },
      component: 'InputTextArea',
      ifShow: ({ model }) => model?.questionType === 'questionType_1',
    },
    {
      field: 'correctClassifi',
      label: '纠正分类',
      required: true,
      colProps: { span: 8 },
      componentProps: {
        placeholder: '请选择',
        api: () => getDictByNumber('correctClassification'),
        labelField: 'description',
        valueField: 'number',
      },
      component: 'ApiSelect',
      ifShow: ({ model }) => model?.questionType === 'questionType_1',
    },
    {
      field: 'ecnNumber',
      label: '关联ECN编号',
      colProps: { span: 8 },
      componentProps: {
        placeholder: '请选择',
        options: [],
      },
      component: 'Input',
      ifShow: ({ model }) => model?.questionType === 'questionType_1',
    },
    {
      field: 'correctRemark',
      label: '纠正描述',
      required: true,
      colProps: { span: 24 },
      componentProps: {
        placeholder: '请输入',
        maxlength: 200,
        showCount: true,
      },
      component: 'InputTextArea',
      ifShow: ({ model }) => model?.questionType === 'questionType_1',
    },
    {
      component: 'Input',
      colProps: {
        span: 24,
      },
      slot: 'slotInfoNext',
      ifShow: ({ model }) => model?.questionType === 'questionType_1',
    },

    {
      field: 'isDiTechnicalIssues',
      label: '是否疑难技术问题',
      rules: [
        {
          required: true,
          message: '请选择是否疑难技术问题',
          type: 'boolean',
        },
      ],
      colProps: { span: 8 },
      componentProps: {
        placeholder: '请选择',
        options: [
          {
            label: '是',
            value: true,
          },
          {
            label: '否',
            value: false,
          },
        ],
      },
      component: 'Select',
      ifShow: ({ model }) => model?.questionType === 'questionType_1',
    },
    {
      field: 'isAssess',
      label: '是否综合评估不再解决',
      rules: [
        {
          required: true,
          message: '请选择是否综合评估不再解决',
          type: 'boolean',
        },
      ],
      colProps: { span: 8 },
      componentProps: {
        placeholder: '请选择',
        options: [
          {
            label: '是',
            value: true,
          },
          {
            label: '否',
            value: false,
          },
        ],
      },
      component: 'Select',
      ifShow: ({ model }) => model?.questionType === 'questionType_1',
    },
    {
      field: 'isEcologicalIssues',
      label: '是否生态库问题',
      rules: [
        {
          required: true,
          message: '请选择是否生态库问题',
          type: 'boolean',
        },
      ],
      colProps: { span: 8 },
      componentProps: {
        placeholder: '请选择',
        options: [
          {
            label: '是',
            value: true,
          },
          {
            label: '否',
            value: false,
          },
        ],
      },
      component: 'Select',
      ifShow: ({ model }) => model?.questionType === 'questionType_1',
    },
    {
      field: 'isQualityUseCases',
      label: ' 是否典型质量案例',
      rules: [
        {
          required: true,
          message: '请选择是否典型质量案例',
          type: 'boolean',
        },
      ],
      colProps: { span: 8 },
      componentProps: {
        placeholder: '请选择',
        options: [
          {
            label: '是',
            value: true,
          },
          {
            label: '否',
            value: false,
          },
        ],
      },
      component: 'Select',
      ifShow: ({ model }) => model?.questionType === 'questionType_1',
    },
    {
      field: 'oneCaseToAnother',
      label: '举一反三',
      required: true,
      colProps: { span: 24 },
      componentProps: {
        placeholder: '请输入',
        maxlength: 200,
        showCount: true,
      },
      component: 'InputTextArea',
      ifShow: ({ model }) => model?.questionType === 'questionType_1',
    },
    {
      field: 'correAcDescription',
      label: '纠正措施描述',
      required: true,
      colProps: { span: 24 },
      componentProps: {
        placeholder: '请输入',
        maxlength: 200,
        showCount: true,
      },
      component: 'InputTextArea',
      ifShow: ({ model }) => model?.questionType === 'questionType_1',
    },

    {
      field: 'opinionCategories',
      label: '意见类别',
      colProps: { span: 8 },
      required: true,
      componentProps: {
        placeholder: '请选择',
        api: () => getDictByNumber('problemLevel'),
        labelField: 'description',
        valueField: 'number',
      },
      component: 'ApiSelect',
      ifShow: ({ model }) => model?.questionType === 'questionType_2',
    },
    {
      field: 'reviewPointsName',
      label: '评审要点',
      colProps: { span: 8 },
      required: true,
      componentProps: {
        allowClear: true,
        onClick() {
          method.selectTable();
        },
        addonAfter: h(
          'span',
          {
            onClick: () => {
              method.selectTable();
            },
          },
          '请选择',
        ),
        async onChange(value) {
          message.info('请选择');
          method.method.clearField('reviewPointsName');
        },
      },
      component: 'Input',
      ifShow: ({ model }) => model?.questionType === 'questionType_2',
    },
    {
      field: 'opClassification',
      label: '意见分类',
      colProps: { span: 8 },
      rules: [
        {
          required: true,
          message: '请选择意见分类',
          type: 'array',
        },
      ],
      componentProps: {
        placeholder: '请选择',
        options: computed(() => optionsData.opClassificationOptions.value),
        fieldNames: {
          label: 'description',
          value: 'number',
          children: 'children',
        },
        changeOnSelect: true,
      },
      component: 'Cascader',
      ifShow: ({ model }) => model?.questionType === 'questionType_2',
    },
    {
      field: 'adoptionSituation',
      label: '采纳情况',
      colProps: { span: 8 },
      required: true,
      componentProps: {
        placeholder: '请选择',
        api: () => getDictByNumber('adoptionSituation'),
        labelField: 'description',
        valueField: 'number',
      },
      component: 'ApiSelect',
      ifShow: ({ model }) => model?.questionType === 'questionType_2',
    },
    {
      field: 'isTechnicalIssues',
      label: '是否技术问题',
      colProps: { span: 8 },
      rules: [
        {
          required: true,
          message: '请选择是否技术问题',
          type: 'boolean',
        },
      ],
      componentProps: {
        placeholder: '请选择',
        options: [
          {
            label: '是',
            value: true,
          },
          {
            label: '否',
            value: false,
          },
        ],
      },
      component: 'Select',
      ifShow: ({ model }) => model?.questionType === 'questionType_2',
    },
    {
      field: 'isTypicalProblems',
      label: '是否典型问题',
      colProps: { span: 8 },
      rules: [
        {
          required: true,
          message: '请选择是否技术问题',
          type: 'boolean',
        },
      ],
      componentProps: {
        placeholder: '请选择',
        options: [
          {
            label: '是',
            value: true,
          },
          {
            label: '否',
            value: false,
          },
        ],
      },
      component: 'Select',
      ifShow: ({ model }) => model?.questionType === 'questionType_2',
    },
    {
      field: 'overallDescription',
      label: '整改情况描述',
      colProps: { span: 24 },
      componentProps: {
        placeholder: '请输入',
        maxlength: 200,
        showCount: true,
      },
      component: 'InputTextArea',
      ifShow: ({ model }) => model?.questionType === 'questionType_2',
    },
  ];
}
