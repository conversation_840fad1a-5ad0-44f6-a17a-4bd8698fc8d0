package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.PostProjectDTO;
import com.chinasie.orion.domain.dto.SearchDTO;
import com.chinasie.orion.domain.entity.PostProject;
import com.chinasie.orion.domain.vo.PostProjectVo;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.PageRequest;
import com.chinasie.orion.page.PageResult;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/23/17:57
 * @description:
 */
public interface PostProjectService extends OrionBaseService<PostProject> {

    /**
     * 获取与结项相关的项目信息
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    PostProjectDTO getProjectInfo(String projectId) throws Exception;

    /**
     * 新增结项
     *
     * @param postProjectDTO
     * @return
     * @throws Exception
     */
    String savePostProject(PostProjectDTO postProjectDTO) throws Exception;

    /**
     * 获取结项分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    PageResult<PostProjectVo> getPostProjectPage(PageRequest<PostProjectDTO> pageRequest) throws Exception;

    /**
     * 获取结项详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    PostProjectVo getPostProjectDetail(String id) throws Exception;

    /**
     * 编辑结项
     *
     * @param postProjectDTO
     * @return
     * @throws Exception
     */
    Boolean editPostProject(PostProjectDTO postProjectDTO) throws Exception;

    /**
     * 批量删除结项
     *
     * @param idList
     * @return
     * @throws Exception
     */
    Boolean removeBatchPostProject(List<String> idList) throws Exception;

    /**
     * 结项搜索
     *
     * @param searchDTO
     * @return
     */
    List<PostProjectVo> search(SearchDTO searchDTO) throws Exception;
}
