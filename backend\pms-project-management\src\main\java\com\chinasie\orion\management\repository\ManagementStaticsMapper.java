package com.chinasie.orion.management.repository;

import com.chinasie.orion.domain.entity.ContractMilestone;
import com.chinasie.orion.domain.entity.Scheme;
import com.chinasie.orion.management.domain.dto.ContractSignStatisticsDTO;
import com.chinasie.orion.management.domain.entity.QuotationManagement;
import com.chinasie.orion.management.domain.vo.MilestoneLineChartQueryVO;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.hadoop.yarn.webapp.hamlet2.Hamlet;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * customerContact Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-30 16:25:24
 */
@Mapper
public interface ManagementStaticsMapper extends OrionBaseMapper<ContractMilestone> {
    /**
     *
     * @param deptId 部门编号
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param type 类型核能和非核能
     * @return
     */
    BigDecimal selectMilestoneCompletion(@Param("deptId") String deptId, @Param("startTime") LocalDate startTime, @Param("endTime") LocalDate endTime, @Param("type") String type);
    //里程碑计划验收金额
    BigDecimal selectMilestonePlan(@Param("deptId") String deptId, @Param("startTime") LocalDate startTime, @Param("endTime") LocalDate endTime, @Param("type") String type);

    //获取合同总数
    Integer selectContractNum(@Param("deptId") String deptId, @Param("startTime") LocalDate startTime, @Param("endTime") LocalDate endTime, @Param("type") String type);
    //履行中合同数
    Integer fulfuilContractNum(@Param("deptId") String deptId, @Param("startTime") LocalDate startTime, @Param("endTime") LocalDate endTime, @Param("type") String type);
    //需求总数
    Integer requirementTotal(@Param("deptId") String deptId, @Param("startTime") LocalDate startTime, @Param("endTime") LocalDate endTime, @Param("type") String type);

    //已发出报价数量，按报价单是否有系统报价发出时间判断
    Integer sendQuoteNum(@Param("deptId") String deptId, @Param("startTime") LocalDate startTime, @Param("endTime") LocalDate endTime, @Param("type") String type);

    // 已发出报价单金额统计，按报价单是否有系统报价发出时间判断
    List<QuotationManagement> sendQuoteAmount(@Param("deptId") String deptId, @Param("startTime") LocalDate startTime, @Param("endTime") LocalDate endTime, @Param("type") String type);

    List<Scheme> selectStatistic(@Param("id") String id, @Param("orgId") String orgId);


    //中标报价数量
    Integer biddingQuotNum(@Param("deptId") String deptId, @Param("startTime") LocalDate startTime, @Param("endTime") LocalDate endTime, @Param("type") String type);

    //中标未进入合同状态
    List<ContractSignStatisticsDTO> findQuotations();

    Integer fulfuilTpContractNum(@Param("deptId") String deptId, @Param("startTime") LocalDate startTime, @Param("endTime") LocalDate endTime, @Param("type") String type);
    Integer fulfuilframeContractNum(@Param("deptId") String deptId, @Param("startTime") LocalDate startTime, @Param("endTime") LocalDate endTime, @Param("type") String type);
    Integer fulfuilCompositeContractNum(@Param("deptId") String deptId, @Param("startTime") LocalDate startTime, @Param("endTime") LocalDate endTime, @Param("type") String type);

    List<MilestoneLineChartQueryVO> actualAccept(@Param("filterYear") Integer filterYear, @Param("deptId")String deptId);

    List<MilestoneLineChartQueryVO> planAccept(@Param("filterYear") Integer filterYear, @Param("deptId")String deptId);

    Integer getExceptionReportNum();

    BigDecimal selectPlannedRevenue(@Param("deptId") String deptId, @Param("startTime") LocalDate startTime, @Param("endTime") LocalDate endTime, @Param("type") String type);

    BigDecimal selectAcceptedRevenue(@Param("deptId") String deptId, @Param("startTime") LocalDate startTime, @Param("endTime") LocalDate endTime, @Param("type") String type);
}

