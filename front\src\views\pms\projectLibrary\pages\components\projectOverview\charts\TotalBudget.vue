<script setup lang="ts">
import { formatMoney } from '/@/views/pms/utils/utils';
import { useChart } from './useChart';
import EmptyView from '/@/views/pms/components/EmptyView.vue';
import SpinView from '/@/views/pms/components/SpinView.vue';
import {
  computed, ComputedRef, inject, onMounted, ref, Ref, watch,
} from 'vue';
import Api from '/@/api';

const type: Ref<string> = inject('type');
const projectId: string = inject('projectId');
const loading:Ref<boolean> = ref(false);
const budgetInfo: Ref<Record<string, any>> = ref({});
const list: ComputedRef<any[]> = computed(() => budgetInfo.value?.items || []);
const chartOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
  },
  color: ['#3CB6E3'],
  grid: {
    top: 10,
    left: 20,
    right: 20,
    bottom: 20,
    containLabel: true,
  },
  dataZoom: [
    {
      show: list.value.length > 5,
      height: 0,
      moveHandleSize: 10,
      showDetail: false,
      maxValueSpan: 4,
      bottom: 10,
    },
  ],
  xAxis: {
    type: 'category',
    data: list.value.map((item) => item.name),
    axisTick: {
      show: false,
    },
    axisLabel: {
      interval: 0,
    },
  },
  yAxis: {
    type: 'value',
    splitLine: {
      lineStyle: {
        type: 'dashed',
      },
    },
  },
  series: [
    {
      barWidth: 20,
      data: list.value.map((item) => item.budgetMoney),
      type: 'bar',
    },
  ],
}));

const chartRef: Ref = ref();
const chartInstance = useChart(chartRef, loading);

watch(() => chartOption.value, (value) => {
  if (list.value.length) {
    chartInstance.value.setOption(value);
  }
}, { deep: true });

onMounted(() => {
  getBudgetInfo();
});

// 获取预算信息
async function getBudgetInfo() {
  loading.value = true;
  try {
    const result = await new Api(`/pms/projectOverview/zgh/projectBudgetOverview/${projectId}`).fetch({
      type: type.value,
    }, '', 'GET');
    budgetInfo.value = result || {};
  } finally {
    loading.value = false;
  }
}
</script>

<template>
  <div class="flex flex-ac mt15 mb15">
    <div class="label-value-item">
      <span>预算总金额：</span>
      <span class="value">{{ formatMoney(budgetInfo.budgetMoney) }}元</span>
    </div>
    <div class="label-value-item">
      <span>已花费预算：</span>
      <span class="value">{{ formatMoney(budgetInfo.expendMoney) }}元</span>
    </div>
    <div class="label-value-item">
      <span>剩余预算：</span>
      <span class="value">{{ formatMoney(budgetInfo.residueMoney) }}元</span>
    </div>
  </div>
  <spin-view
    v-if="loading"
    class="chart"
  />
  <div
    v-show="!loading && list.length"
    ref="chartRef"
    class="chart"
  />
  <empty-view
    v-show="!loading && list.length===0"
    class="chart"
  />
</template>

<style scoped lang="less">
.chart {
  width: 100%;
  height: 200px;
}
</style>
