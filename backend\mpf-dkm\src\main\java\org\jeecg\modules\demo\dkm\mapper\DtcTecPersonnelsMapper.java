package org.jeecg.modules.demo.dkm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.jeecg.modules.demo.dkm.dto.DtcPersonnelsDTO;
import org.jeecg.modules.demo.dkm.entity.DtcTecPersonnels;

import java.util.List;

/**
 * @Description: DtcTecPersonnels Mapper
 * @Author: tancheng
 * @Date: 2025-4-16
 */
@Mapper
public interface DtcTecPersonnelsMapper extends BaseMapper<DtcTecPersonnels> {
    
    /**
     * 查询数据完整性问题的记录
     * 一次性查询所有字段缺失的记录
     * @return 存在数据完整性问题的记录列表
     */
    List<DtcPersonnelsDTO> findTecDataIntegrityIssues();

    /**
     * 查询员工号重复的数据
     * @return 存在员工号重复的记录列表
     */
    List<DtcPersonnelsDTO> findDublicateStaffNoTecDataIssues();
}