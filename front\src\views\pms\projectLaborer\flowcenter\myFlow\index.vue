<template>
  <Layout
    :options="{ body: { scroll: true } }"
    content-title="我发起的"
  >
    <orion-table
      ref="instanceTableRef"
      :options="options"
      :filter-schemas="filterSchemas"
    >
      <template #name="{ record }">
        <a @click="goDetail(record)"> {{ record.procInstName }}</a>
      </template>
      <template #action="{ record }">
        <a
          v-if="record.statusCode === 'CANCELLED'"
          @click="onRestart(record)"
        >发起</a>
        <a
          v-if="record.statusCode === 'RUNNING'"
          @click="onWithdraw(record)"
        >撤回</a>
      </template>
    </orion-table>
  </Layout>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, ref,
} from 'vue';
import { Input, Button, message } from 'ant-design-vue';
import {
  BasicTable, Layout, useActionsRecord, OrionTable,
} from 'lyra-component-vue3';
// import Layout from '/@/components/Layout';
// import OrionTable from '/@/components/OrionTable';
import { useUserStore } from '/@/store/modules/user';
import { useRouter } from 'vue-router';
import { workflowApi } from '../util/apiConfig';
import Api from '/@/api/index';
export default defineComponent({
  name: 'MyFlow',
  components: {
    Layout,
    OrionTable,
    Input,
    AButton: Button,
  },
  setup() {
    const userStore: any = useUserStore();
    const instanceTableRef = ref<Nullable<any>>(null); // table的ref
    const router = useRouter();
    const state = reactive({
      options: {
        rowSelection: {},
        deleteToolButton: 'add|delete|enable|disable',
        smallSearchField: ['proc_inst_name'],
        auto: {
          url: `${workflowApi}/act-prearranged/my`,
          params: {
            query: {
              userId: userStore.getUserInfo.id,
              startedBy: userStore.getUserInfo.id,
              search: '',
            },
          },
          form: {
            labelWidth: 120,
            actionColOptions: {
              span: 24,
            },
            schemas: [],
          },
        },
        columns: [
          {
            title: '流程编号',
            dataIndex: 'businessKey',
          },
          {
            title: '实例标题',
            dataIndex: 'processInstanceName',
            slots: { customRender: 'name' },
          },
          {
            title: '流程名称',
            dataIndex: 'procDefName',
          },
          {
            title: '流程状态',
            dataIndex: 'statusName',
          },
          {
            title: '当前节点',
            dataIndex: 'currentTask',
          },
          {
            title: '发起时间',
            dataIndex: 'createTime',
          },
          {
            title: '结束时间',
            dataIndex: 'endTime',
          },
          {
            title: '操作',
            dataIndex: 'action',
            slots: { customRender: 'action' },
          },
        ],
      },
      filterSchemas: [
        {
          field: 'status',
          component: 'ApiSelect',
          label: '流程状态',
          colProps: {
            span: 8,
          },
          componentProps: {
            mode: 'multiple',
            api: () => new Api(workflowApi).fetch({}, 'act-system/proc-inst-status/page', 'POST'),
            labelField: 'name',
            valueField: 'code',
          },
        },
        {
          field: 'create_time',
          component: 'RangePicker',
          label: '发布时间',
          colProps: {
            span: 12,
          },
          componentProps: {},
        },
      ],
    });

    function goDetail(row) {
      router.push({
        path: '/flowcenter/detail',
        query: {
          id: row.id,
          processInstanceId: row.procInstId,
          processDefinitionId: row.procDefId,
        },
      });
    }

    function onWithdraw(row) {
      new Api(workflowApi)
        .fetch(
          {
            procInstIds: [row.procInstId],
            userId: userStore.getUserInfo.id,
          },
          'act-inst/retract',
          'PUT',
        )
        .then(() => {
          message.success('操作成功');
          instanceTableRef.value.reload();
        });
    }

    function onRestart(row) {
      new Api(workflowApi)
        .fetch(
          {
            procInstIds: [row.procInstId],
            userId: userStore.getUserInfo.id,
          },
          'act-inst/reserve',
          'PUT',
        )
        .then(() => {
          message.success('操作成功');
          instanceTableRef.value.reload();
        });
    }

    return {
      ...toRefs(state),
      instanceTableRef,
      goDetail,
      onWithdraw,
      onRestart,
    };
  },
});
</script>
<style scoped lang="less">
.btns-header {
  display: flex;
  flex-direction: row;
}
</style>
