<script setup lang="ts">
import {
  Layout, OrionTable,
} from 'lyra-component-vue3';
import { ref, Ref, inject } from 'vue';
import Api from '/@/api';

const detailsData: Record<string, any> = inject('supplierInfo');
const tableRef: Ref = ref();
const tableOptions = {
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  smallSearchField: undefined,
  showSmallSearch: false,
  columns: [
    {
      title: '资质证书名称',
      dataIndex: 'certName',
    },
    {
      title: '资质类别',
      dataIndex: 'certCategory',
    },
    {
      title: '资质等级',
      dataIndex: 'certLevel',
    },
    {
      title: '资质分组',
      dataIndex: 'certGroup',
    },
    {
      title: '代理品牌/产品名称',
      dataIndex: 'brandProduct',
    },
    {
      title: '证书有效期截止日期',
      dataIndex: 'expiryDate',
    },
    {
      title: '证书编码',
      dataIndex: 'certCode',
    },
  ],
  api: (params:Record<string, any>) => new Api('/spm/supplierCertInfo/getCertInfoByCode').fetch({
    ...params,
    query: {
      supplierCode: detailsData.value?.supplierNumber,
    },
  }, '', 'POST'),
};

</script>

<template>
  <Layout :options="{ body: { scroll: true } }">
    <OrionTable
      ref="tableRef"
      class="scroll-table"
      :options="tableOptions"
    />
  </Layout>
</template>

<style scoped lang="less">
.scroll-table {
  ::-webkit-scrollbar {
    height: 15px !important;
    width: 15px !important;
  }
  ::-webkit-scrollbar-thumb {
    border-radius:0;
  }
}
</style>
