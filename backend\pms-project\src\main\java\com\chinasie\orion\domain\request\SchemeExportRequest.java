package com.chinasie.orion.domain.request;


import com.chinasie.orion.conts.SchemeListTypeEnum;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import com.chinasie.orion.page.OrderItem;
import com.chinasie.orion.page.SearchCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "项目计划导出请求参数")
public class SchemeExportRequest extends ObjectEntity {

    @ApiModelProperty(value = "项目id", required = true)
    @NotBlank(message = "项目ID，不能为空")
    private String projectId;

    @ApiModelProperty(value = "需要导出的计划ID", required = false)
    private List<String> projectSchemeIds;

    @ApiModelProperty(name = "业务", value = "PROJECT_SCHEME-项目计划列表    ACCEPTANCE_FROM-验收单-验收计划列表")
    private SchemeListTypeEnum typeEnum;

    @ApiModelProperty(value = "高级搜索条件")
    private List<List<SearchCondition>> searchConditions;

    @ApiModelProperty(value = "排序")
    private List<OrderItem> orders;

    @ApiModelProperty(value = "类型 0-全部计划 1-我负责的 2-我协作的 3-我创建的")
    private String type;
}
