<script setup lang="ts">
import { BasicCard } from 'lyra-component-vue3';
import { computed } from 'vue';
import { formatRenderRes } from '../token/util';
interface Props{
  commodityList:any
}
const props = withDefaults(defineProps<Props>(), {
  commodityList: {},
});
const renderCommodityList = computed(() => props.commodityList?.list ?? []);
const useFormatPrice = (price:any) => (price ? `￥${Number(price).toFixed(2)}` : formatRenderRes(price));
</script>

<template>
  <div class="commodity-list">
    <BasicCard title="商品清单">
      <div class="goods-info">
        <div class="gi-row">
          <span class="property">总金额</span>
          <span class="value">{{ useFormatPrice(commodityList?.total) }}</span>
        </div>
        <div class="gi-row mt-16">
          <span class="property">附加费</span>
          <span class="value">{{ useFormatPrice(commodityList?.surcharge) }}</span>
        </div>
        <div class="gi-row mt-16">
          <span class="property">含附加费总金额</span>
          <span class="value">{{ useFormatPrice(commodityList?.allTotal) }}</span>
        </div>
      </div>
    </BasicCard>
    <div class="commodity-info">
      <div class="headers-row">
        <div class="header-column number-column">
          序号
        </div>
        <div class="header-column image-column">
          商品图
        </div>
        <div class="header-column name-column">
          商品名
        </div>
        <div class="header-column single-name-column">
          单品名称
        </div>
        <div class="header-column price-column">
          单价
        </div>
        <div class="header-column sum-column">
          数量
        </div>
        <div class="header-column money-column">
          金额
        </div>
        <div class="header-column remark-column">
          采购备注
        </div>
        <div class="header-column prinfo-column">
          PR信息
        </div>
      </div>
      <div class="commodity-body">
        <div
          v-for="(good,index) in renderCommodityList.list"
          :key="good.id"
          class="commodity-row"
        >
          <div class="commodity-column number-column">
            {{ index + 1 }}
          </div>
          <div class="commodity-column image-column" />
          <div class="commodity-column name-column">
            {{ formatRenderRes(good.inventoryName) }}
          </div>
          <div class="commodity-column single-name-column">
            {{ formatRenderRes(good.itemName) }}
          </div>
          <div class="commodity-column price-column">
            {{ formatRenderRes(good.univalence) }}
          </div>
          <div class="commodity-column sum-column">
            {{ formatRenderRes(good.quantity) }}
          </div>
          <div class="commodity-column money-column">
            {{ useFormatPrice(good.amount) }}
          </div>
          <div class="commodity-column remark-column">
            {{ formatRenderRes(good.notes) }}
          </div>
          <div class="commodity-column prinfo-column">
            <div>PR编号：{{ formatRenderRes(good.prMessage) }}</div>
            <div>行项目号：{{ formatRenderRes(good.prMessage) }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.commodity-list {
  .goods-info {
    .gi-row {
      display: flex;
      flex-wrap: nowrap;

      &.mt-16 {
        margin-top: ~`getPrefixVar('content-padding-top')`;
      }

      .property {
        width: 200px;
        color: ~`getPrefixVar('primary-10')`;
        font-weight: 400;
      }

      .value {

      }
    }
  }

  .commodity-info {
    padding: ~`getPrefixVar('button-margin')`;
    .header-column, .commodity-column {
      display: flex;
      align-items: center;
    }

    .headers-row {
      display: flex;
      flex-wrap: nowrap;
      height: 60px;
      padding: 10px;

      .header-column {
        display: flex;
        align-items: center;
      }
    }
    .commodity-body{
      .commodity-column{
        display: flex;
        align-items: center;
      }
      .image-column {
        height: 50px;
      }
      .commodity-row{
        .image-column{
          background: red;
        }
      }
      .prinfo-column{
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
      }
    }
    .commodity-row {
      display: flex;
      padding: 10px;

      .header-column {

      }
    }
    .number-column {
      width: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .image-column {
      width: 70px;
    }

    .name-column, .remark-column {
      flex: 1;
      min-width: 200px;
      text-overflow: ellipsis;
      overflow: hidden;
      line-clamp: 2;
      padding: 0 12px;
    }

    .single-name-column {
      width: 150px;
      padding-left: 12px;
    }

    .price-column {
      width: 120px;
      display: flex;
      align-items: center;
    }

    .sum-column {
      width: 100px;
      display: flex;
      align-items: center;
    }

    .money-column {
      width: 100px;
      display: flex;
      align-items: center;
    }

    .prinfo-column {
      width: 150px;
    }
  }
}
</style>