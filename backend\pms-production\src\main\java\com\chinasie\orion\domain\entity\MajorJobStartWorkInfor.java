package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.Boolean;
import java.lang.String;

/**
 * MajorJobStartWorkInfor Entity对象
 *
 * <AUTHOR>
 * @since 2024-11-19 09:46:56
 */
@TableName(value = "pmsx_major_job_start_work_infor")
@ApiModel(value = "MajorJobStartWorkInforEntity对象", description = "大修工单开工信息")
@Data

public class MajorJobStartWorkInfor extends  ObjectEntity  implements Serializable{

    /**
     * 工单编号
     */
    @ApiModelProperty(value = "工单编号")
    @TableField(value = "job_number")
    private String jobNumber;

    /**
     * 开工日期
     */
    @ApiModelProperty(value = "开工日期")
    @TableField(value = "start_work_date")
    private Date startWorkDate;

    @ApiModelProperty(value = "开工日期-字符 yyyy-MM-dd")
    @TableField(value = "start_work_date_str")
    private String startWorkDateStr;

    /**
     * 上午开工状态
     */
    @ApiModelProperty(value = "上午开工状态")
    @TableField(value = "morning_status")
    private Boolean morningStatus;

    /**
     * 下午开工状态
     */
    @ApiModelProperty(value = "下午开工状态")
    @TableField(value = "afternoon_status")
    private Boolean afternoonStatus;

    /**
     * 夜间状态
     */
    @ApiModelProperty(value = "夜间状态")
    @TableField(value = "night_status")
    private Boolean nightStatus;

}
