<template>
  <div class="MonthBox">
    <div
      v-for="item of 6"
      :key="item"
      class="monthBoxItem"
    >
      <div class="itemTop">
        <div class="weekNumber">
          李小白
        </div>
        <div
          v-if="false"
          :class="{circleCore:true,fourColor:item % 5>=4,threeColor:item % 5===3,twoColor:item % 5<=2}"
        >
          {{ item % 5 }}
        </div>
        <div class="topRightBtn">
          <span class="action-btn">提醒</span>
          <span class="action-btn">审核</span>
        </div>
      </div>

      <div class="itemContent">
        <div
          v-for="item of 18"
          :key="item"
          class="flex-te "
        >
          <span
            class="mr20"
            :title="item"
            style="color:rgb(60,180,60)"
          >3h</span>
          <span :title="item">完成计划管理开发方案</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, PropType } from 'vue';
import { Button } from 'ant-design-vue';
// import { PlusOutlined } from '@ant-design/icons-vue';
const AButton = Button;
const emits = defineEmits<{
  (e: 'update:title', hah: string): void;
}>();
const props = defineProps({
  trigger: {
    type: [Array] as PropType<('contextmenu' | 'click' | 'hover')[]>,
    default: () => ['contextmenu'],
  },
  selectedKeys: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
});

const state = reactive({
  one: 666666,
});
</script>

<style scoped lang="less">
.MonthBox {
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;

  .monthBoxItem {
    width: 48%;
    height: 200px;
    margin-bottom: 0.3%;
    margin-right: 0.33%;
    border-radius: 3px;
    box-sizing: border-box;
    border: 1px solid #ccc;
    padding: 35px 10px 10px 10px;
    position: relative;

    &:nth-child(7n) {
      margin-right: 0;
    }

    .itemTop {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: auto;
      background-color: red;
    }

    .weekNumber {
      position: absolute;
      width: 150px;
      height: 30px;
      line-height: 30px;
      left: 5px;
      top: 5px;
    }

    .circleCore {
      position: absolute;
      width: 30px;
      height: 30px;
      border-radius: 50%;
      text-align: center;
      line-height: 30px;
      right: 5px;
      top: 5px;
    }

    .topRightBtn {
      position: absolute;
      height: 30px;
      text-align: center;
      line-height: 30px;
      right: 5px;
      top: 5px;
    }

    .itemContent {
      overflow: auto;
      height: 100%;
      width: 100%;
    }
  }
}

.fourColor {
  background-color: #faa519;
}

.threeColor {
  background-color: rgb(102, 204, 204);
}

.twoColor {
  background-color: #ccc;
}
</style>
