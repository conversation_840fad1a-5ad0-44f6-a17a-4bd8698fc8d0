/**
 * Independent time operation tool to facilitate subsequent switch to dayjs
 */
import dayjs from 'dayjs';

const DATE_TIME_FORMAT = 'YYYY-MM-DD HH:mm:ss';
const DATE_FORMAT = 'YYYY-MM-DD';

export function formatToDateTime(
  date: dayjs.Dayjs | undefined = undefined,
  format = DATE_TIME_FORMAT,
): string {
  return dayjs(date).format(format);
}

export function formatToDate(
  date: dayjs.Dayjs | undefined = undefined,
  format = DATE_FORMAT,
): string {
  return dayjs(date).format(format);
}
/**
 * 对时间戳进行格式化
 * @param s 时间戳
 * @param format 格式化字符
 */
export const stampDate = (s: string | number, format = 'yyyy-MM-dd HH:mm:ss') => {
  if (s == null || s == undefined) {
    return '';
  }
  if (typeof s === 'string') {
    s = s.replace('Z', '');
  }
  const dtime = new Date(s);
  if (dtime < new Date('2015-1-1 00:00:00')) {
    return '';
  }
  const o = {
    'M+': dtime.getMonth() + 1, // month
    'd+': dtime.getDate(), // day
    'h+': dtime.getHours(), // hour
    'm+': dtime.getMinutes(), // minute
    's+': dtime.getSeconds(), // second
    'q+': Math.floor((dtime.getMonth() + 3) / 3), // quarter
    'f+': dtime.getMilliseconds(), // millisecond
    S: dtime.getMilliseconds(), // millisecond
  };
  if (/(y+)/.test(format)) format = format.replace(RegExp.$1, (`${dtime.getFullYear()}`).substr(4 - RegExp.$1.length));
  for (const k in o) {
    if (new RegExp(`(${k})`, 'gi').test(format)) {
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? o[k] : (`00${o[k]}`).substr((`${o[k]}`).length),
      );
    }
  }
  return format;
};

export const dateUtil = dayjs;
