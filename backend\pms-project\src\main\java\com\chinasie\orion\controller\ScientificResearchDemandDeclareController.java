package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.LeadManagementDTO;
import com.chinasie.orion.domain.dto.ScientificResearchDemandDeclareDTO;
import com.chinasie.orion.domain.dto.SearchDTO;
import com.chinasie.orion.domain.vo.LeadManagementVO;
import com.chinasie.orion.domain.vo.ScientificResearchDemandDeclareVO;
import com.chinasie.orion.service.ScientificResearchDemandDeclareService;
import com.mzt.logapi.starter.annotation.LogRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;
import java.lang.Exception;
import java.lang.String;

/**
 * <p>
 * ScientificResearchDemandDeclare 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-23 11:20:05
 */
@RestController
@RequestMapping("/scientificResearchDemandDeclare")
@Api(tags = "科研需求申报")
public class ScientificResearchDemandDeclareController {

    @Autowired
    private ScientificResearchDemandDeclareService scientificResearchDemandDeclareService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查看了【科研需求申报】{{#id}}", type = "科研需求申报",subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ScientificResearchDemandDeclareVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false) String pageCode) throws Exception {
        ScientificResearchDemandDeclareVO rsp = scientificResearchDemandDeclareService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param scientificResearchDemandDeclareDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增了【科研需求申报】", type = "科研需求申报", subType = "新增", bizNo = "{{#scientificResearchDemandDeclareDTO.name}}")
    public ResponseDTO<ScientificResearchDemandDeclareVO> create(@RequestBody ScientificResearchDemandDeclareDTO scientificResearchDemandDeclareDTO) throws Exception {
        ScientificResearchDemandDeclareVO rsp = scientificResearchDemandDeclareService.create(scientificResearchDemandDeclareDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param scientificResearchDemandDeclareDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了【科研需求申报】", type = "科研需求申报",subType = "编辑", bizNo = "{{#scientificResearchDemandDeclareDTO.name}}")
    public ResponseDTO<ScientificResearchDemandDeclareVO> edit(@RequestBody ScientificResearchDemandDeclareDTO scientificResearchDemandDeclareDTO) throws Exception {
        ScientificResearchDemandDeclareVO rsp = scientificResearchDemandDeclareService.edit(scientificResearchDemandDeclareDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了【科研需求申报】", type = "科研需求申报",subType = "删除（批量）", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = scientificResearchDemandDeclareService.remove(ids);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/ids", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了【科研需求申报】", type = "科研需求申报", subType = "删除（批量）", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> removeByIds(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = scientificResearchDemandDeclareService.removeByIds(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/pages", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询了【科研需求申报】", type = "科研需求申报",subType = "分页", bizNo = "")
    public ResponseDTO<Page<ScientificResearchDemandDeclareVO>> pages(@RequestBody Page<ScientificResearchDemandDeclareDTO> pageRequest) throws Exception {
        Page<ScientificResearchDemandDeclareVO> rsp = scientificResearchDemandDeclareService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 第三方获取分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "第三方获取分页")
    @RequestMapping(value = "/third/pages", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询了【科研需求申报】", type = "科研需求申报", subType = "第三方获取分页", bizNo = "")
    public ResponseDTO<Page<ScientificResearchDemandDeclareVO>> thirdPages(@RequestBody Page<SearchDTO> pageRequest) throws Exception {
        Page<ScientificResearchDemandDeclareVO> rsp = scientificResearchDemandDeclareService.thirdPages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "线索分页")
    @RequestMapping(value = "/getLeadPage", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询了【线索分页】", type = "科研需求申报", subType = "线索分页", bizNo = "")
    public ResponseDTO<Page<LeadManagementVO>> getLeadPage(@RequestBody Page<LeadManagementDTO> pageRequest) throws Exception {
        Page<LeadManagementVO> rsp = scientificResearchDemandDeclareService.getLeadPage(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 删除关联项目
     *
     * @param projectIds
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除关联项目")
    @RequestMapping(value = "/delete/relateProject/{id}", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord( success = "【{USER{#logUserId}}】删除关联项目", type = "科研需求申报",subType = "删除关联项目", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> deleteRelateProject(@PathVariable(value = "id") String id,@RequestBody List<String> projectIds) throws Exception {
        Boolean rsp = scientificResearchDemandDeclareService.deleteRelateProject(id,projectIds);
        return new ResponseDTO<>(rsp);
    }
}
