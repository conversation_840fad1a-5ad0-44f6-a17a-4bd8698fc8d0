<script setup lang="ts">
import { ref } from 'vue';
import { Icon, BasicTitle1, OrionTable } from 'lyra-component-vue3';

import { milestonePage } from '/@/views/pms/api/projectScheme';
import draggable from 'vuedraggable';

interface Props {
  projectId: string
  data: {id:string, name:string}[]
}

const props = withDefaults(defineProps<Props>(), {
  projectId: '',
  data: () => [],
});

const tableRef = ref(null);
const selectRows = ref(props.data);
const columns = [
  {
    title: '计划名称',
    dataIndex: 'name',
  },

  {
    title: '计划类型',
    dataIndex: 'nodeType',
    customRender: () => '里程碑',

  },
  {
    title: '责任部门',
    dataIndex: 'rspSubDeptName',
  },
  {
    title: '责任处室',
    dataIndex: 'rspSectionName',
  },
  {
    title: '责任人',
    dataIndex: 'rspUserName',
  },
];
const baseTableOption = {
  rowKey: 'id',
  // 是否显示工具栏默认按钮
  showToolButton: false,
  showSmallSearch: true,
  showTableSetting: false,
  isSpacing: false,
  columns,
  api: (params) => {
    params.query = { projectId: props.projectId };
    return milestonePage(params);
  },
};

const handleRowClick = (record) => {
  const row = selectRows.value.find((row) => row.id === record.id);
  if (!row) {
    selectRows.value.push(record);
  }
};

const getDataSource = () => selectRows.value.map((item) => item.id);
const handleDelete = (id) => {
  selectRows.value = selectRows.value.filter((item) => item.id !== id);
};

defineExpose({
  getDataSource,
});
</script>

<template>
  <div class="content-box">
    <div class="content-left">
      <OrionTable
        ref="tableRef"
        :options="baseTableOption"
        @row-click="handleRowClick"
      />
    </div>
    <div class="content-right">
      <div class="right-title">
        <BasicTitle1 title="已选阶段" />
      </div>

      <div class="right-box">
        <draggable
          :list="selectRows"
          :force-fallback="true"
          animation="300"
        >
          <template #item="{ element }">
            <div class="item-label">
              <div class="item-name flex-te">
                {{ element.name }}
              </div>
              <div class="item-icon">
                <Icon
                  icon="sie-icon-shanchu"
                  size="16"
                  @click="handleDelete(element.id)"
                />
                <Icon
                  icon="fa-arrows"
                  size="16"
                />
              </div>
            </div>
          </template>
        </draggable>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.content-box{
  height: 100%;
  display: flex;
  .content-left{
    flex: 5;
    border-right: 1px solid #ddd;
    overflow: hidden;
    position: relative;
    padding: 10px;
  }
  .content-right{
    flex: 2;
    display: flex;
    flex-direction: column;
    .right-title{
      padding: 10px;
      border-bottom: 1px solid #ddd;
    }

    .right-box{
      flex: 1;
      overflow: scroll;

      .item-label{
        cursor: move;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 10px 20px;
        border-bottom: 1px solid #ddd;
        .item-name{
          flex: 1;
        }

      }
    }
  }
}
</style>
