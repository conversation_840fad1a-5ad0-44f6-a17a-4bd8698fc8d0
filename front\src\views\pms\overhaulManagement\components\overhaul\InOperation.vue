<script setup lang="ts">
import { BasicCard, Empty, Icon } from 'lyra-component-vue3';
import { TabPane, Tabs } from 'ant-design-vue';
import {
  provide, reactive, ref, Ref, unref, watchEffect,
} from 'vue';
import dayjs from 'dayjs';
import SQEDeviation from './SQEDeviation.vue';
import HighRiskJob from './HighRiskJob.vue';
import MajorProjects from './MajorProjects.vue';
import DevicePerson from './DevicePerson.vue';
import Api from '/@/api';
import CheckRepairRound from '/@/views/pms/overhaulManagement/components/overhaul/CheckRepairRound.vue';
import axios from 'axios';

interface TabPaneItem {
  key: string
  tab: string
  isWarning: boolean
}

const tabKey: Ref<string> = ref('');
provide('repairRound', tabKey);
const tabPanes: Ref<TabPaneItem[]> = ref([]);

// 获取关注的实施中大修
const majorLoading: Ref<boolean> = ref(false);

async function getMajorInfo() {
  majorLoading.value = true;
  try {
    const result = await new Api('/pms/majorUserLike/major/info?statusEnum=IMPL').fetch('', '', 'POST');
    tabPanes.value = result?.map((item) => ({
      key: item.repairRound,
      tab: item.repairRound,
      isWarning: item.isWarning,
    })) || [];
    tabKey.value = tabPanes.value?.[0]?.key;
  } finally {
    majorLoading.value = false;
  }
}

watchEffect(() => {
  getMajorInfo();
});

const repairRoundList: Ref<any[]> = ref([]);

async function getMajorList() {
  const result = await new Api('/pms/majorUserLike/major/list?statusEnum=IMPL').fetch({}, '', 'POST');
  repairRoundList.value = result?.map((item) => item?.repairRound) || [];
}

watchEffect(() => {
  getMajorList();
});

const containerRef: Ref = ref();

// 安质环偏差相关
const sqeData: Record<string, any> = reactive({});
const sqeLoading: Ref<boolean> = ref(false);

async function getSqeData() {
  if (!tabKey.value) return;
  sqeLoading.value = true;
  try {
    const result = await new Api('/pms/safety-quality-env/listByStatistic').fetch({
      majorRepairTurn: tabKey.value,
    }, '', 'POST');
    Object.assign(sqeData, result);
  } finally {
    sqeLoading.value = false;
  }
}

watchEffect(() => {
  getSqeData();
});

// 高风险作业相关
const highRiskData = ref([]);
const highRiskDate: Ref<string> = ref(dayjs().format('YYYY-MM-DD'));
const highRiskLoading: Ref<boolean> = ref(false);

async function getStatistic() {
  if (!tabKey.value) return;
  highRiskLoading.value = true;
  try {
    const result = await new Api('/pms/job-manage/highRiskStatistic').fetch({
      statisticTime: unref(highRiskDate),
      repairRound: tabKey.value,
    }, '', 'POST');
    highRiskData.value = result || [];
  } finally {
    highRiskLoading.value = false;
  }
}

watchEffect(() => {
  getStatistic();
});

// 重大项目相关
const projectData: Record<string, any> = reactive({});
const projectLoading: Ref<boolean> = ref(false);

async function getImportantProject() {
  if (!tabKey.value) return;
  projectLoading.value = true;
  try {
    const result = await new Api('/pms/job-manage/bigJobStatistic').fetch({
      repairRound: tabKey.value,
    }, '', 'POST');
    Object.assign(projectData, result);
  } finally {
    projectLoading.value = false;
  }
}

watchEffect(() => {
  getImportantProject();
});



async function getpersonNum(){
  // axios.post('http://192.168.0.106:8700/personAllocation/getInfo',{ repairRound: tabKey.value,
  //   queryType:0,
  //   status:0,
  //   })
  //       .then(function (response) {
  //         personNum.value=response.data?.result.overNumbers
  //       })
  try {
    const obj = new Api('/pms/personAllocation/getInfo').fetch({
      repairRound: tabKey.value,
      queryType:0,
     status:0,
    }, '', 'POST').then((res)=>{
        personNum.value=res?.overNumbers
    })
    
  }
  catch{}
}
async function getmaterialNum(){
  // axios.post('http://192.168.0.106:8700/personAllocation/getInfo',{ repairRound: tabKey.value,
  //   queryType:1,
  //   status:0,
  //   })
  //       .then(function (response) {
  //           materialNum.value=response.data?.result.overNumbers          
  //       })
  try {
    const obj2 = new Api('/pms/personAllocation/getInfo').fetch({
      repairRound: tabKey.value,
      queryType:1,
     status:0,
    }, '', 'POST').then((res)=>{
        materialNum.value=res?.overNumbers
    })
    
  }
  catch{}
}



// 人员与设备相关
const personNum=ref(0)
const materialNum=ref(0)
const userMaterialLoading: Ref<boolean> = ref(false);
const personMaterialData = reactive({
  personOverlap: personNum.value,
  person: null,
  materialOverlap: materialNum.value,
  material: null,
});

async function getUserAndMaterial() {
  if (!tabKey.value) return;
  userMaterialLoading.value = true;
  try {
    const userApi = new Api('/pms/majorRepairStatistic/getPerson').fetch({
      repairRound: tabKey.value,
    }, '', 'POST');
    const userOverlapApi = new Api('/pms/resource-allocation/person/overlap/days').fetch({
      repairRound: tabKey.value,
      yearNum: dayjs().year(),
    }, '', 'POST');
    const materialApi = new Api('/pms/majorRepairStatistic/getMaterial').fetch({
      repairRound: tabKey.value,
    }, '', 'POST');
    const materialOverlapApi = new Api('/pms/resource-allocation/material/overlap/days').fetch({
      repairRound: tabKey.value,
      yearNum: dayjs().year(),
    }, '', 'POST');
    const results: any[] = await Promise.allSettled([
      userOverlapApi,
      userApi,
      materialOverlapApi,
      materialApi,
    ]);
    Object.keys(personMaterialData).forEach((key, index) => {
      personMaterialData[key] = results[index].status === 'fulfilled' ? results[index].value : 0;
    });  
    personMaterialData.personOverlap=Number(personNum.value);
    personMaterialData.materialOverlap=Number(materialNum.value);    
  } finally {
    userMaterialLoading.value = false;
  }
}



watchEffect(() => {
  getpersonNum();
  getmaterialNum();
  getUserAndMaterial();
});
</script>

<template>
  <div ref="containerRef" />
  <Tabs
    v-model:activeKey="tabKey"
    type="card"
  >
    <TabPane
      v-for="item in tabPanes"
      :key="item.key"
    >
      <template #tab>
        <div>
          <span>{{ item.tab }}</span>
          <Icon
            v-if="item.isWarning"
            class="ml5"
            style="color:red"
            icon="orion-icon-alert"
          />
        </div>
      </template>
    </TabPane>
    <template #rightExtra>
      <CheckRepairRound
        statusEnum="IMPL"
        :selectedKeys="tabPanes.map(item=>item.tab)"
        :options="repairRoundList"
        :get-popup-container="()=>containerRef"
        @update="getMajorInfo()"
      />
    </template>
  </Tabs>

  <div
    v-if="tabKey"
    class="grid-card"
  >
    <BasicCard
      v-loading="sqeLoading"
      style="position: relative"
      title="安质环偏差"
      :isSpacing="false"
      :isBorder="false"
    >
      <SQEDeviation :data="sqeData" />
    </BasicCard>
    <BasicCard
      v-loading="highRiskLoading"
      style="position: relative"
      :title="`高风险作业（今日计划高风险作业数 ${highRiskData?.map?.['总数']||0}）`"
      :isSpacing="false"
      :isBorder="false"
    >
      <HighRiskJob
        v-model:date="highRiskDate"
        :data="highRiskData"
      />
    </BasicCard>
    <BasicCard
      v-loading="projectLoading"
      style="position: relative"
      title="重大项目"
      :isSpacing="false"
      :isBorder="false"
    >
      <MajorProjects :data="projectData" />
    </BasicCard>
    <BasicCard
      v-loading="userMaterialLoading"
      style="position: relative"
      title="人员与设备"
      :isSpacing="false"
      :isBorder="false"
    >
      <DevicePerson
        :data="personMaterialData"
        :personData="personNum"
        :materialData="materialNum"
      />
    </BasicCard>
  </div>
  <div
    v-else
    v-loading="majorLoading"
    class="empty-wrap"
  >
    <Empty description="暂无关注的轮次信息" />
  </div>
</template>

<style scoped lang="less">
:deep(.ant-tabs-tab-active) {
  font-weight: inherit !important;
  font-size: inherit !important;
}

.empty-wrap {
  position: relative;
  padding: 1px;
}

.icon-button {
  background-color: #EEEEEE;
  width: 32px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  cursor: pointer;
}

.grid-card {
  display: grid;
  grid-template-columns: minmax(0, 3fr) minmax(0, 4fr);
  gap: 0 20px;
}
</style>
