package com.chinasie.orion.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.alibaba.nacos.api.remote.response.ResponseCode;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.domain.dto.allocation.*;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.BaseErrorCode;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.feign.TeamByPersonFeignService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.repository.*;
import com.chinasie.orion.service.ResourceAllocationOfService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.map.MultiValueMap;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ResourceAllocationOfServiceImpl implements ResourceAllocationOfService {
    // 定义一个静态的 DateTimeFormatter，确保线程安全
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    public static final String ADD_TYPE_PERSON = "p";//人员类型
    public static final String ADD_TYPE_MATERIAL = "m";//物资类型
    public static final Integer NORMAL_LOGIC_STATUS = 1;
    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ResourceAllocationOfMapper resourceAllocationOfMapper;

    @Autowired
    private PersonMangeMapper personMangeMapper;

    @Autowired
    private PmsxRelationOrgToPersonMapper pmsxRelationOrgToPersonMapper;

    @Autowired
    private MajorRepairOrgMapper majorRepairOrgMapper;

    @Autowired
    private MaterialManageMapper materialManageMapper;

    @Autowired
    private RelationOrgToMaterialMapper relationOrgToMaterialMapper;

    @Autowired
    private BasicUserMapper basicUserMapper;

    @Autowired
    private MajorRepairPlanMapper majorRepairPlanMapper;

    @Autowired
    private TeamByPersonFeignService teamByPersonFeignService;

    @Override
    public List<DeptTreeVO> getResourceAllocationOfPersonMaterial(QueryParams param) {

        String type = param.getSearchType();
        resourceAllocationOfMapper.setGroupConcatMaxLen();

        //获取员工所在的部门
        String staffNo = param.getStaffNo();
        DeptTreeDTO deptTreeDTO = new DeptTreeDTO();
        deptTreeDTO.setStaffNo(staffNo);
        deptTreeDTO.setSearchType(type);
        List<DeptTreeVO> deptTreeVOS = queryDeptTree(deptTreeDTO);
        if (CollectionUtils.isEmpty(deptTreeVOS)) {
            log.error("未获取到userId为" + param.getStaffNo() + "的部门和人员/物资数据");
            return new ArrayList<DeptTreeVO>();
        }
        //分类放置部门和人员/物资信息
        List<DeptTreeVO> deptTreeList = new ArrayList<>();
        List<DeptTreeVO> personAssetList = new ArrayList<>();
        for (DeptTreeVO deptTreeVO : deptTreeVOS) {
            deptTreeVO.setKey(new Random().nextInt(1000000000));
            if ("o".equals(deptTreeVO.getType())) {
                DeptTreeVO deptTreeVO1 = new DeptTreeVO();
                deptTreeVO1.setNumber(deptTreeVO.getNumber());
                deptTreeVO1.setName(deptTreeVO.getName());
                deptTreeVO1.setParentId(deptTreeVO.getParentId());
                deptTreeVO1.setType(deptTreeVO.getType());
                deptTreeList.add(deptTreeVO1);
            } else if ("pa".equals(deptTreeVO.getType())) {
                DeptTreeVO deptTreeVO1 = new DeptTreeVO();
                deptTreeVO1.setNumber(deptTreeVO.getNumber());
                deptTreeVO1.setName(deptTreeVO.getName());
                deptTreeVO1.setParentId(deptTreeVO.getParentId());
                deptTreeVO1.setType(deptTreeVO.getType());
                personAssetList.add(deptTreeVO1);
            }
        }
        //查询人员/物资的计划进出场时间
        List<ResourceAllocationOf> resourceAllocationOfList = new ArrayList<>();

        if ("p".equals(type)) {
            resourceAllocationOfList = resourceAllocationOfMapper
                    .getResourceAllocationOfPerson(param);
        }

        if ("m".equals(type)) {
            resourceAllocationOfList = resourceAllocationOfMapper
                    .getResourceAllocationOfMaterial(param);
        }

        List<ResourceAllocationOfDTO> resourceAllocationOfDTOList = getSectionTimes(resourceAllocationOfList);

        //将时间信息放到人员/物资信息中0
        for (DeptTreeVO deptPersonTreeVO : personAssetList) {
            deptPersonTreeVO.setKey(new Random().nextInt(1000000000));
            deptPersonTreeVO.setSectionTimes(new ArrayList<>());
            deptPersonTreeVO.setOverLapDays(new ArrayList<>());
            if (!resourceAllocationOfDTOList.isEmpty()) {
                resourceAllocationOfDTOList.forEach(item -> {
                    if (item.getNumber().equals(deptPersonTreeVO.getNumber())) {
                        deptPersonTreeVO.getSectionTimes().addAll(item.getSectionTimes());
                        deptPersonTreeVO.setTotalDays(item.getTotalDays());
                        deptPersonTreeVO.setOverlap(item.getOverlap());
                        deptPersonTreeVO.setOverDays(item.getOverDays());
                        deptPersonTreeVO.setRepairRoundCount(item.getRepairRound());
                        if(item.getOverLapDays() != null){
                            deptPersonTreeVO.setOverLapDays(item.getOverLapDays());
                        }
                        deptPersonTreeVO.setDataType(item.getDataType());
                    }
                });
            }else {
                log.error("未获取到人员/物资的计划进出场时间");
            }
        }

        List<DeptTreeVO> collect = deptTreeList.stream().filter(item -> "00014500".equals(item.getNumber())).collect(Collectors.toList());
        //如果缺少00014500部门，则添加
        if (collect.isEmpty()) {
            DeptTreeVO deptTreeVO1 = new DeptTreeVO();
            deptTreeVO1.setKey(000111);
            deptTreeVO1.setNumber("00014500");
            deptTreeVO1.setName("苏州热工研究院有限公司");
            deptTreeVO1.setParentId("00888888");
            if (!StringUtils.isEmpty(param.getRealStartDate()) && !StringUtils.isEmpty(param.getRealEndDate())) {
                deptTreeVO1.setRealStartDate(param.getRealStartDate());
                deptTreeVO1.setRealEndDate(param.getRealEndDate());
            }
            deptTreeList.add(deptTreeVO1);
        }
        for (DeptTreeVO deptTreeVO : deptTreeList) {
            deptTreeVO.setKey(new Random().nextInt(1000000000));
            deptTreeVO.setChildren(new ArrayList<>());
            personAssetList.forEach(item -> {
                //将人员/物资信息添加到部门信息中
                if (deptTreeVO.getNumber().equals(item.getParentId())) {
                    deptTreeVO.getChildren().add(item);
                }
            });
        }

        Map<String, List<DeptTreeVO>> parentIdUserVOMap = new HashMap<>();
        List<DeptTreeVO> userVOList = new ArrayList<>();

        // 构建 parentId 到 DeptTreeVO 的映射
        for (DeptTreeVO deptTreeVO : deptTreeList) {
            userVOList.add(deptTreeVO);
            String parentId = deptTreeVO.getParentId();
            if (parentIdUserVOMap.containsKey(parentId)) {
                parentIdUserVOMap.get(parentId).add(deptTreeVO);
            } else {
                List<DeptTreeVO> userVOs = new ArrayList<>();
                userVOs.add(deptTreeVO);
                parentIdUserVOMap.put(parentId, userVOs);
            }
        }

        // 设置每个节点的子节点
        for (DeptTreeVO deptTreeVO : deptTreeList) {
            List<DeptTreeVO> childs = parentIdUserVOMap.get(deptTreeVO.getNumber());
            if (childs != null) {
                deptTreeVO.setChildren(childs);
            }
        }

        // 找到根节点（number 为 00014500）
        List<DeptTreeVO> result = new ArrayList<>();
        for (DeptTreeVO deptTreeVO : userVOList) {
            if ("00888888".equals(deptTreeVO.getParentId())) {
                result.add(deptTreeVO);
                break; // 假设只有一个根节点
            }
        }

        // 如果根节点的 number 不是 00014500，而是 00014500 自身
        if (result.isEmpty()) {
            for (DeptTreeVO deptTreeVO : userVOList) {
                if ("00014500".equals(deptTreeVO.getNumber())) {
                    result.add(deptTreeVO);
                    break;
                }
            }
        }
        return result;
    }

    public List<ResourceAllocationOfDTO> getSectionTimes(List<ResourceAllocationOf> resourceAllocationOfList) {
        Set<ResourceAllocationOfDTO> nodes = new HashSet<ResourceAllocationOfDTO>();
        List<SectionTime> sectionTimes = new ArrayList<>();
        for (ResourceAllocationOf resourceAllocationOf : resourceAllocationOfList) {
            ResourceAllocationOfDTO resourceAllocationOfDTO = new ResourceAllocationOfDTO();
            resourceAllocationOfDTO.setNumber(resourceAllocationOf.getNumber());
            nodes.add(resourceAllocationOfDTO);

            SectionTime sectionTime = new SectionTime();
            sectionTime.setRowId(resourceAllocationOf.getRowId());
            sectionTime.setRealStartDate(resourceAllocationOf.getRealStartDate());
            sectionTime.setRealEndDate(resourceAllocationOf.getRealEndDate());
            sectionTime.setRelationId(resourceAllocationOf.getRelationId());
            sectionTime.setOrgId(resourceAllocationOf.getOrgId());
            sectionTime.setStaffNo(resourceAllocationOf.getNumber());
            sectionTime.setRepairName(resourceAllocationOf.getRepairRoundName());
            sectionTime.setBasePlaceCode(resourceAllocationOf.getBasePlaceCode());
            sectionTime.setBasePlaceName(resourceAllocationOf.getBasePlaceName());
            sectionTime.setTeamCode(resourceAllocationOf.getTeamCode());
            sectionTime.setTeamName(resourceAllocationOf.getTeamName());
            sectionTime.setSpecialtyCode(resourceAllocationOf.getSpecialtyCode());
            sectionTime.setSpecialtyName(resourceAllocationOf.getSpecialtyName());
            sectionTime.setRepairRoundName(resourceAllocationOf.getRepairRoundName());
            sectionTime.setRepairRoundCode(resourceAllocationOf.getRepairRoundCode());
            sectionTimes.add(sectionTime);
        }
        if (CollectionUtils.isNotEmpty(nodes)) {
            for (ResourceAllocationOfDTO node : nodes){
                List<SectionTime> sectionTimes_ = new ArrayList<>();
                Set<String> repairOrgCount = new HashSet<>();
                if (CollectionUtils.isNotEmpty(sectionTimes)){
                    sectionTimes.forEach(sectionTime -> {
                        if (node.getNumber().equals(sectionTime.getStaffNo())){
                            repairOrgCount.add(sectionTime.getRepairRoundCode());
                            sectionTimes_.add(sectionTime);
                        }
                    });
                    if (CollectionUtils.isNotEmpty(sectionTimes_)) {
                        Map<String, Object> resultMap = getIntervalAllDates(sectionTimes_);
                        //计算大修总天数
                        Integer totalDays_ = ((HashSet<LocalDate>) resultMap.get("dateSet")).size();
                        node.setTotalDays(totalDays_);
                        //计算重叠天数
                        List<LocalDate> dateList = (List<LocalDate>) resultMap.get("dateList");
                        Set<LocalDate> duplicates = findDuplicates(dateList);
                        node.setOverlap(duplicates.size());

                        List<String> dateStrings = duplicates.stream()
                                .map(date -> date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")))
                                .collect(Collectors.toList());
                        node.setOverLapDays(dateStrings);
                    }
                }
                node.setRepairRound(repairOrgCount.size());
                node.setSectionTimes(sectionTimes_);
            }
        }
        List<ResourceAllocationOfDTO> resourceAllocationOfDTO = new ArrayList<>(nodes);
        return resourceAllocationOfDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer deleteResourceAllocationOfPersonMaterial(AllocationOfPersonMaterialDTO dto) {

        Integer result = 0;
        int delete = 0;
        String deteleType = dto.getBusinessType();
        try {
            if (dto.getId() != null && dto.getRelationId() != null) {

                if ("p".equals(deteleType)) {//删除人员及关联数据

                    result = resourceAllocationOfMapper.deletePerson(dto.getId());

                    LambdaQueryWrapper<PmsxRelationOrgToPerson> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(PmsxRelationOrgToPerson::getId, dto.getRelationId());
                    delete = pmsxRelationOrgToPersonMapper.delete(queryWrapper);
                }

                if ("m".equals(deteleType)) {
                    result = resourceAllocationOfMapper.deleteMaterial(dto.getId());
                    LambdaQueryWrapper<RelationOrgToMaterial> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(RelationOrgToMaterial::getId, dto.getRelationId());
                    delete = relationOrgToMaterialMapper.delete(queryWrapper);
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("删除失败", e);
        }
        return result + delete;
    }

    @Override
    public Integer updateResourceAllocationOfPersonMaterial(Map<String, String> map) {
        return 0;
    }

    /**
     * 添加个人物资分配资源
     * <p>
     * 该方法根据业务类型处理人员或物资的分配信息，并与维修组织关联
     * 它通过解析传入的DTO对象中的信息，更新或插入相应的人员或物资管理表中
     *
     * @param dto 分配信息的DTO对象，包含业务类型和标记业务行列表
     * @return 返回影响的行数，表示操作是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer addResourceAllocationOfPersonMaterial(AllocationOfPersonMaterialDTO dto) {

        Integer result = 0;
        String addType = dto.getBusinessType();
        // 遍历标记业务行列表，处理每个人的或物资的分配信息
        for (MarkBusinessRow markBusinessRow : dto.getMarkBusinessRows()) {
            MajorRepairOrg majorRepairOrgTeam = getMajorRepairOrg(markBusinessRow);

            // 根据业务类型处理人员管理表
            if (ADD_TYPE_PERSON.equals(addType)) {
                result = refreshPersonManageInfo(markBusinessRow, majorRepairOrgTeam);
            } else if (ADD_TYPE_MATERIAL.equals(addType)) {// 根据业务类型处理物资管理表
                result = refreshMaterialManageInfo(markBusinessRow, majorRepairOrgTeam);
            }
        }
        return result;
    }


    @NotNull
    private Integer refreshPersonManageInfo(MarkBusinessRow markBusinessRow, MajorRepairOrg majorRepairOrgTeam) {
        Integer result;
        PersonMange personMange = newOrSelectPersonMange(markBusinessRow);

        // 处理pmsxRelationOrgToPerson
        PmsxRelationOrgToPerson pmsxRelationOrgToPerson = pmsxRelationOrgToPersonMapper.selectOne(new LambdaQueryWrapperX<PmsxRelationOrgToPerson>()
                .eq(PmsxRelationOrgToPerson::getId, markBusinessRow.getRelationId()));
        if (ObjectUtils.isEmpty(pmsxRelationOrgToPerson)) {
            pmsxRelationOrgToPerson = BeanFactory.getPmsxRelationOrgToPerson("haved");
        }
        pmsxRelationOrgToPerson.setPersonId(personMange.getId());
        pmsxRelationOrgToPerson.setRepairOrgId(majorRepairOrgTeam.getId());
        // 处理日期格式转换
        Date planBeginTime = DateUtil.parse(markBusinessRow.getRealStartDate(), DATE_FORMATTER);
        Date planEndTime = DateUtil.parse(markBusinessRow.getRealEndDate(), DATE_FORMATTER);
        pmsxRelationOrgToPerson.setPlanBeginTime(planBeginTime);
        pmsxRelationOrgToPerson.setPlanEndTime(planEndTime);

        // 根据pmsxRelationOrgToPerson对象的存在与否执行更新或插入操作
        if (StringUtils.isNotBlank(pmsxRelationOrgToPerson.getId())) {
            result = pmsxRelationOrgToPersonMapper.updateById(pmsxRelationOrgToPerson);
        } else {
            result = pmsxRelationOrgToPersonMapper.insert(pmsxRelationOrgToPerson);
        }
        return result;
    }

    @NotNull
    private Integer refreshMaterialManageInfo(MarkBusinessRow markBusinessRow, MajorRepairOrg majorRepairOrgTeam) {
        Integer result;
        MaterialManage materialManage = null;
        // 判断是否为修改操作
        if (StringUtils.isNotBlank(markBusinessRow.getRowId())) {
            materialManage = materialManageMapper.selectOne(new LambdaQueryWrapperX<MaterialManage>()
                    .eq(MaterialManage::getId, markBusinessRow.getRowId()));
        } else {
            materialManage = BeanFactory.getMaterialManage("haved");
        }
        if(StrUtil.isBlank(markBusinessRow.getBasePlaceCode())){
            throw new IllegalArgumentException("基地编码不可为空");
        }
        if(StrUtil.isNotBlank(markBusinessRow.getBasePlaceCode())){
            materialManage.setBaseCode(markBusinessRow.getBasePlaceCode());
        }
        materialManage.setNumber(markBusinessRow.getNumber());
        materialManage.setAssetName(markBusinessRow.getName());
        materialManage.setCostCenter(markBusinessRow.getCostCenterCode());
        // 处理日期格式转换
        Date planBeginTime = DateUtil.parse(markBusinessRow.getRealStartDate(), DATE_FORMATTER);
        Date planEndTime = DateUtil.parse(markBusinessRow.getRealEndDate(), DATE_FORMATTER);
        materialManage.setInDate(planBeginTime);
        materialManage.setOutDate(planEndTime);

        // 根据物资管理对象的存在与否执行更新或插入操作
        if (StringUtils.isNotBlank(materialManage.getId())) {
            materialManageMapper.updateById(materialManage);
        } else {
            materialManageMapper.insert(materialManage);
        }

        // 处理RelationOrgToMaterial
        RelationOrgToMaterial relationOrgToMaterial = relationOrgToMaterialMapper.selectOne(new LambdaQueryWrapperX<RelationOrgToMaterial>()
                .eq(RelationOrgToMaterial::getId, markBusinessRow.getRelationId()));
        if (ObjectUtils.isEmpty(relationOrgToMaterial)) {
            relationOrgToMaterial = BeanFactory.getRelationOrgToMaterial("haved");
        }
        relationOrgToMaterial.setMaterialId(materialManage.getId());
        relationOrgToMaterial.setRepairOrgId(majorRepairOrgTeam.getId());

        // 根据RelationOrgToMaterial对象的存在与否执行更新或插入操作
        if (StringUtils.isNotBlank(relationOrgToMaterial.getId())) {
            result = relationOrgToMaterialMapper.updateById(relationOrgToMaterial);
        } else {
            result = relationOrgToMaterialMapper.insert(relationOrgToMaterial);
        }
        return result;
    }

    /**
     * 根据MarkBusinessRow对象创建或更新PersonMange对象
     * 如果MarkBusinessRow对象中的rowId不为空，则表示更新操作，否则为插入操作
     * 该方法还会处理日期格式的转换，并根据操作类型调用相应的数据库操作方法
     *
     * @param markBusinessRow 包含人员管理信息的MarkBusinessRow对象，用于创建或更新PersonMange对象
     * @return 返回新创建或更新后的PersonMange对象
     * @throws BaseException 如果日期格式转换失败，抛出基础异常
     */
    @NotNull
    private PersonMange newOrSelectPersonMange(MarkBusinessRow markBusinessRow) {
        if(StrUtil.isBlank(markBusinessRow.getBasePlaceCode())){
            throw new IllegalArgumentException("基地编码不可为空");
        }
        PersonMange personMange = null;
        // 判断是否为修改操作
        if (StringUtils.isNotBlank(markBusinessRow.getRowId())) {
            personMange = personMangeMapper.selectOne(new LambdaQueryWrapperX<PersonMange>()
                    .eq(PersonMange::getId, markBusinessRow.getRowId()));
        } else {
            personMange = new PersonMange();
            personMange.setNumber(markBusinessRow.getStaffNo());
            personMange.setRepairRound(markBusinessRow.getRepairRoundCode());
        }
        if(StrUtil.isNotBlank(markBusinessRow.getBasePlaceCode())){
            personMange.setBaseCode(markBusinessRow.getBasePlaceCode());
        }
        if(StrUtil.isNotBlank(markBusinessRow.getBasePlaceName())){
            personMange.setBaseName(markBusinessRow.getBasePlaceName());
        }

        // 处理日期格式转换
        personMange.setInDate(DateUtil.parse(markBusinessRow.getRealStartDate(), DATE_FORMATTER));
        personMange.setOutDate(DateUtil.parse(markBusinessRow.getRealEndDate(), DATE_FORMATTER));

        // 根据人员管理对象的存在与否执行更新或插入操作
        if (StringUtils.isNotBlank(personMange.getId())) {
            personMangeMapper.updateById(personMange);
        } else {
            personMange.setIsBasePermanent(false);
            personMangeMapper.insert(personMange);
        }
        return personMange;
    }

    @Override
    public List<RepairRoundAnd> queryRepairPlan(String repairRound) {
        List<RepairRoundAnd> roundAndList = resourceAllocationOfMapper.queryRepairPlan(repairRound);
        return roundAndList;
    }



    /**
     * 远程调用生产使用，本地需注释
     *  查询专业和班组信息
     *
     */
    @Override
    public List<SpecialtyAndTeam> querySpecialtyAndTeam(String staffNo) {

        //调用远程服务
        try {
            ResponseDTO<List<SpecialtyAndTeam>> listResponseDTO = teamByPersonFeignService.teamByPersonList(staffNo);
            List<SpecialtyAndTeam> result = listResponseDTO.getResult();
            //处理和构建数据
            // 检查响应状态
            if (ResponseCode.SUCCESS.getCode() == listResponseDTO.getCode()) {
                return result;
            } else {
                log.info("Request failed with status: {}" + ResponseCode.SUCCESS.getCode());
            }
        } catch (RestClientException e) {
            log.error("Request failed with exception: {}", e.getMessage());
        }
        return new ArrayList<SpecialtyAndTeam>();
    }
    /**
     * 远程调用生产使用，本地需注释
     *  查询部门和人员信息
     *
     */
    public List<DeptTreeVO> queryDeptTree(DeptTreeDTO deptTreeDTO) {

        //调用远程服务
        try {
            ResponseDTO<List<DeptTreeVO>> listResponseDTO = teamByPersonFeignService.queryDeptPersonTree(deptTreeDTO);
            List<DeptTreeVO> result = listResponseDTO.getResult();
            //处理和构建数据
            // 检查响应状态
            if (ResponseCode.SUCCESS.getCode() == listResponseDTO.getCode()) {
                return result;
            } else {
                log.info("Request failed with status: {}" + ResponseCode.SUCCESS.getCode());
            }
        } catch (RestClientException e) {
            log.error("Request failed with exception: {}", e.getMessage());
        }
        return new ArrayList<DeptTreeVO>();
    }

    public Set<LocalDate> findDuplicates(List<LocalDate> list) {
        Set<LocalDate> duplicates = new HashSet<>();
        Set<LocalDate> seen = new HashSet<>();
        for (LocalDate element : list) {
            if (!seen.add(element)) {
                duplicates.add(element);
            }
        }
        return duplicates;
    }


    public Map getIntervalAllDates(List<SectionTime> sectionTimes) {

        List<LocalDate> dateList = new ArrayList<>();
        Set<LocalDate> dateSet = new HashSet<>();
        List<SectionTime> toRemove = new ArrayList<>();
        for (SectionTime sectionTime : sectionTimes) {
            String startDateStr = sectionTime.getRealStartDate();
            String endDateStr = sectionTime.getRealEndDate();
            try {
                if (StringUtils.isBlank(startDateStr) || StringUtils.isBlank(endDateStr)) {
                    toRemove.add(sectionTime); // 收集要删除的元素
                    continue; // 跳过空日期
                }
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                LocalDate start = LocalDate.parse(sectionTime.getRealStartDate(), formatter);
                LocalDate end = LocalDate.parse(sectionTime.getRealEndDate(), formatter);

                if (start.equals(end)) {
                    dateSet.add(start);
                    dateList.add(start);
                    break;
                }

                if (start.isAfter(end)) {
                    LocalDate temp = start;
                    start = end;
                    end = temp;

                }
                while (!start.isEqual(end)) {
                    dateSet.add(start);
                    dateList.add(start);
                    start = start.plusDays(1);
                }
                dateSet.add(start);
                dateList.add(start);
            }catch (Exception e){
                log.error("日期转换异常：{}", e.getMessage());
            }
        }

        // 统一删除
        sectionTimes.removeAll(toRemove);

        Map<String, Object> mapResult = new HashMap<>();
        mapResult.put("dateList", dateList);
        mapResult.put("dateSet", dateSet);
        return mapResult;
    }

    /**
     * 处理大修组织业务方法
     *
     * @param markBusinessRow
     * @return
     */
//    @Transactional
    public MajorRepairOrg getMajorRepairOrg(MarkBusinessRow markBusinessRow) {
        // 输入参数校验
        if (markBusinessRow == null || ObjectUtils.isEmpty(markBusinessRow.getSpecialtyCode())
                || ObjectUtils.isEmpty(markBusinessRow.getRepairRoundCode())
                || ObjectUtils.isEmpty(markBusinessRow.getTeamCode())) {
            throw new IllegalArgumentException("SpecialtyCode RepairRoundCode TeamCode 不可为空");
        }
        if (StrUtil.isNotBlank(markBusinessRow.getRowId())) {
            if (StrUtil.isBlank(markBusinessRow.getRelationId())) {
                throw new IllegalArgumentException("修改的时候rowId和relationId不可为空");
            }
        }else{
            markBusinessRow.setRelationId("");
        }
        //查询是否存在大修计划，没有就新增 pmsx_major_repair_plan.repair_round pmsx_major_repair_plan.base_code pmsx_major_repair_plan.base_name
        Long plantCount = majorRepairPlanMapper.selectCount(new LambdaQueryWrapperX<MajorRepairPlan>()
                .eq(MajorRepairPlan::getLogicStatus, NORMAL_LOGIC_STATUS)
                .eq(MajorRepairPlan::getRepairRound, markBusinessRow.getRepairRoundCode()));
        if (plantCount <= 0) {
            log.error("大修轮次{}不存在", markBusinessRow.getRepairRoundCode());
            throw new IllegalArgumentException("大修轮次" + markBusinessRow.getRepairRoundCode() + "不存在");
        }

        // 查询专业是否存在，不存在就创建
        MajorRepairOrg majorRepairOrgSpecialty = queryOrInsertSpecialty(markBusinessRow);

        // 查询班组是否存在，不存在就创建
        return queryOrInsertTeam(markBusinessRow, majorRepairOrgSpecialty);
    }

    /**
     * 查询或插入特定的MajorRepairOrg对象
     * 此方法首先根据专业代码和维修轮次代码查询数据库中是否存在对应的记录
     * 如果不存在，则创建一个新的MajorRepairOrg对象，并插入到数据库中
     *
     * @param markBusinessRow 包含专业代码和维修轮次代码的MarkBusinessRow对象，用于查询和插入
     * @return 返回查询到的或新插入的MajorRepairOrg对象
     * @throws IllegalArgumentException 如果输入参数为空或不包含必要的代码信息
     * @throws RuntimeException 如果查询或插入操作失败
     */
    private MajorRepairOrg queryOrInsertSpecialty(MarkBusinessRow markBusinessRow) {
        // 参数校验
        if (markBusinessRow == null || StringUtils.isEmpty(markBusinessRow.getSpecialtyCode()) || StringUtils.isEmpty(markBusinessRow.getRepairRoundCode())) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_RESULT, "SpecialtyCode 和 RepairRoundCode 不能为空");
        }

        List<MajorRepairOrg> majorRepairOrgs = majorRepairOrgMapper.selectList(new LambdaQueryWrapperX<MajorRepairOrg>()
                .eq(MajorRepairOrg::getLogicStatus, NORMAL_LOGIC_STATUS)
                .eq(MajorRepairOrg::getCode, markBusinessRow.getSpecialtyCode())
                .eq(MajorRepairOrg::getRepairRound, markBusinessRow.getRepairRoundCode()));
        if (majorRepairOrgs.size() > 1) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_RESULT, "大修轮次=" + markBusinessRow.getRepairRoundCode() + "专业编码=" + markBusinessRow.getSpecialtyCode() + "的专业数量=" + majorRepairOrgs.size() + ",数量超过1个,数据存在问题，请检查！");
        }

        // 尝试根据专业代码和维修轮次代码查询MajorRepairOrg对象
        MajorRepairOrg majorRepairOrgSpecialty = null;
        if(majorRepairOrgs.size() == 1 ){
            majorRepairOrgSpecialty = majorRepairOrgs.get(0);
        }

        // 如果查询结果为空，则创建并插入新的MajorRepairOrg对象
        if (ObjectUtils.isEmpty(majorRepairOrgSpecialty)) {
            majorRepairOrgSpecialty = createMajorRepairOrg(markBusinessRow, "executionSpecialty", 2, "0");
            // 直接设置 chainPath，在插入时一并保存
            majorRepairOrgSpecialty.setChainPath("0," + majorRepairOrgSpecialty.getId());
            majorRepairOrgMapper.insert(majorRepairOrgSpecialty);
        }
        return majorRepairOrgSpecialty;
    }


    /**
     * 查询或插入团队信息
     * 此方法首先根据团队代码和专业ID查询现有的 MajorRepairOrg 对象如果不存在，则创建并插入新的团队信息
     *
     * @param markBusinessRow 包含团队代码的业务行信息，用于查询或插入操作
     * @param majorRepairOrgSpecialty 专业 MajorRepairOrg 对象，用于获取专业ID和构建团队的链路路径
     * @return 查询到的或新插入的 MajorRepairOrg 对象
     * @throws IllegalArgumentException 如果输入参数无效
     * @throws RuntimeException 如果数据库操作失败
     */
    private MajorRepairOrg queryOrInsertTeam(MarkBusinessRow markBusinessRow, MajorRepairOrg majorRepairOrgSpecialty) {
        // 输入参数校验
        if (markBusinessRow == null || StringUtils.isEmpty(markBusinessRow.getTeamCode()) || majorRepairOrgSpecialty == null || majorRepairOrgSpecialty.getId() == null) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_RESULT, "班组的 teamCode 不可为空");
        }

        // 根据团队代码和专业ID查询 MajorRepairOrg 对象
        List<MajorRepairOrg> majorRepairOrgTeamList = majorRepairOrgMapper.selectList(new LambdaQueryWrapperX<MajorRepairOrg>()
                .eq(MajorRepairOrg::getLogicStatus, NORMAL_LOGIC_STATUS)
                .eq(MajorRepairOrg::getCode, markBusinessRow.getTeamCode())
                .eq(MajorRepairOrg::getRepairRound, markBusinessRow.getRepairRoundCode()));

        if (majorRepairOrgTeamList.size() > 1) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_RESULT, "大修轮次=" + markBusinessRow.getRepairRoundCode() + "组织编码=" + markBusinessRow.getTeamCode() + "的数量=" + majorRepairOrgTeamList.size() + ",数量超过1个,数据存在问题，请检查！");
        }

        // 尝试根据专业代码和维修轮次代码查询MajorRepairOrg对象
        MajorRepairOrg majorRepairOrgTeam = null;
        if(majorRepairOrgTeamList.size() == 1 ){
            majorRepairOrgTeam = majorRepairOrgTeamList.get(0);
        }

        // 如果没有查询到团队信息，则创建并插入新的 MajorRepairOrg 对象
        if (CollectionUtils.isEmpty(majorRepairOrgTeamList)) {
            try {
                majorRepairOrgTeam = createMajorRepairOrgTeam(markBusinessRow, "specialtyTeam", 3, majorRepairOrgSpecialty.getId());
                majorRepairOrgTeam.setChainPath("0," + majorRepairOrgSpecialty.getId() + "," + majorRepairOrgTeam.getId());
                majorRepairOrgMapper.insert(majorRepairOrgTeam);
            } catch (Exception e) {
                // 处理数据库操作异常
                log.error("Failed to insert or update MajorRepairOrg: {}", e.getMessage(), e);
                throw new RuntimeException("Database operation failed", e);
            }
        }
        return majorRepairOrgTeam;
    }

    private MajorRepairOrg createMajorRepairOrg(MarkBusinessRow markBusinessRow, String levelType, int level, String parentId) {
        MajorRepairOrg majorRepairOrg = BeanFactory.getMajorRepairOrg("haved");
        majorRepairOrg.setParentId(parentId);
        majorRepairOrg.setRepairRound(markBusinessRow.getRepairRoundCode());
        majorRepairOrg.setCode(markBusinessRow.getSpecialtyCode());
        majorRepairOrg.setName(markBusinessRow.getSpecialtyName());
        majorRepairOrg.setLogicStatus(NORMAL_LOGIC_STATUS);
        majorRepairOrg.setLevel(level);
        majorRepairOrg.setLevelType(levelType);
        return majorRepairOrg;
    }
    private MajorRepairOrg createMajorRepairOrgTeam(MarkBusinessRow markBusinessRow, String levelType, int level, String parentId) {
        MajorRepairOrg majorRepairOrg = BeanFactory.getMajorRepairOrg("haved");
        majorRepairOrg.setParentId(parentId);
        majorRepairOrg.setRepairRound(markBusinessRow.getRepairRoundCode());
        majorRepairOrg.setCode(markBusinessRow.getTeamCode());
        majorRepairOrg.setName(markBusinessRow.getTeamName());
        majorRepairOrg.setLevel(level);
        majorRepairOrg.setLevelType(levelType);
        majorRepairOrg.setLogicStatus(NORMAL_LOGIC_STATUS);
        return majorRepairOrg;
    }

}
