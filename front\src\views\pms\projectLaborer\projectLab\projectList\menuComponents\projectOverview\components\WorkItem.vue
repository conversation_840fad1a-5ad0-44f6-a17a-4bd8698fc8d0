<template>
  <div
    v-loading="loading"
    class="p10"
  >
    <div class="title">
      <span>任务类型统计</span>
      <span class="sub-text">（共 {{ count }} 个任务）</span>
    </div>
    <div
      ref="box"
      class="box"
    />
  </div>
</template>

<script lang="ts">
import {
  onMounted, reactive, toRefs, nextTick, inject,
} from 'vue';
import * as echarts from 'echarts';
import Api from '/@/api';

export default {
  name: 'WorkItem',
  setup() {
    const formData: any = inject('formData', {});
    const state = reactive({
      loading: false,
      count: undefined,
      box: null,
    });
    function myEcharts(node, option) {
      const myChart = echarts.init(node);
      myChart.setOption(option);
      window.onresize = function () {
        myChart.resize();
      };
    }
    function init() {
      const url = `project-overview/planTypeCount?projectId=${formData?.value?.id}`;
      state.loading = true;
      new Api('/pms')
        .fetch('', url, 'GET')
        .then((res) => {
          state.loading = false;
          state.count = res.count;
          if (res.content.length === 0) {
            res.content.push(
              {
                count: 0,
                finishCount: 0,
                planTypeName: '计划',
                runningCount: 0,
                unFinishCount: 0,
              },
              {
                count: 0,
                finishCount: 0,
                planTypeName: '任务',
                runningCount: 0,
                unFinishCount: 0,
              },
            );
          }
          const x = res.content.map((s) => `${s.planTypeName}（${s.count}）`);
          const y1 = res.content.map((s) => s.unFinishCount);
          const y2 = res.content.map((s) => s.runningCount);
          const y3 = res.content.map((s) => s.finishCount);
          const option = {
            tooltip: {
              trigger: 'axis',
              axisPointer: { type: 'shadow' },
            },
            legend: {
              right: '5%',
              top: '5%',
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '3%',
              containLabel: true,
            },
            xAxis: [
              {
                type: 'category',
                data: x,
              },
            ],
            yAxis: [{ type: 'value' }],
            series: [
              {
                name: '未开始',
                type: 'bar',
                stack: 'Ad',
                emphasis: { focus: 'series' },
                data: y1,
              },
              {
                name: '进行中',
                type: 'bar',
                stack: 'Ad',
                emphasis: { focus: 'series' },
                data: y2,
              },
              {
                name: '已完成',
                type: 'bar',
                stack: 'Ad',
                emphasis: { focus: 'series' },
                data: y3,
              },
            ],
          };
          myEcharts(state.box, option);
        })
        .catch((_) => {
          state.loading = false;
        });
    }
    onMounted(() => {
      nextTick(() => {
        init();
      });
    });
    return {
      ...toRefs(state),
    };
  },
};
</script>

<style scoped lang="less">
  .title {
    font-size: 18px;
    margin: 10px 0 20px;
  }

  .sub-text {
    font-size: 12px;
    color: #686f8b;
  }
  .box {
    width: 100%;
    height: 257px;
  }
</style>
