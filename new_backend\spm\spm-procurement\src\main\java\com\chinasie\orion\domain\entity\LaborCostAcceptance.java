package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * LaborCostAcceptance Entity对象
 *
 * <AUTHOR>
 * @since 2024-10-28 19:32:01
 */
@TableName(value = "pmsx_labor_cost_acceptance")
@ApiModel(value = "LaborCostAcceptanceEntity对象", description = "人力成本验收单")
@Data

public class LaborCostAcceptance extends  ObjectEntity  implements Serializable{

    /**
     * 申请单名称
     */
    @ApiModelProperty(value = "申请单名称")
    @TableField(value = "name")
    private String name;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_no")
    private String contractNo;

    /**
     * 验收年份
     */
    @ApiModelProperty(value = "验收年份")
    @TableField(value = "acceptance_year")
    private Integer acceptanceYear;

    /**
     * 验收中心
     */
    @ApiModelProperty(value = "验收中心")
    @TableField(value = "acceptance_dept_code")
    private String acceptanceDeptCode;

    /**
     * 验收季度
     */
    @ApiModelProperty(value = "验收季度")
    @TableField(value = "acceptance_quarter")
    private Integer acceptanceQuarter;

    /**
     * 验收用人单位
     */
    @ApiModelProperty(value = "验收用人单位")
    @TableField(value = "employ_dept_code")
    private String employDeptCode;

    /**
     * 项目组审核人
     */
    @ApiModelProperty(value = "项目组审核人")
    @TableField(value = "project_reviewer")
    private String projectReviewer;

    /**
     * 研究所审核人
     */
    @ApiModelProperty(value = "研究所审核人")
    @TableField(value = "dept_reviewer")
    private String deptReviewer;

    /**
     * 中心/部门审核人
     */
    @ApiModelProperty(value = "中心/部门审核人")
    @TableField(value = "org_reviewer")
    private String orgReviewer;

    /**
     * 归口部门审核人
     */
    @ApiModelProperty(value = "归口部门审核人")
    @TableField(value = "belong_dept_reviewer")
    private String belongDeptReviewer;

}
