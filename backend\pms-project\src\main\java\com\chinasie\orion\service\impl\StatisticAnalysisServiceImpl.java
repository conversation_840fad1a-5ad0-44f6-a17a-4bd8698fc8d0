package com.chinasie.orion.service.impl;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.constant.DeliverStatusEnum;
import com.chinasie.orion.constant.DictConstant;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.entity.DemandManagement;
import com.chinasie.orion.domain.entity.PlanCount;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.entity.StatusCount;
import com.chinasie.orion.domain.vo.PlanCountVo;
import com.chinasie.orion.domain.vo.PrincipalStatisticVo;
import com.chinasie.orion.domain.vo.SimpleVo;
import com.chinasie.orion.domain.vo.StatusCountVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/02/23/10:00
 * @description:
 */
@Service
public class StatisticAnalysisServiceImpl implements StatisticAnalysisService {

    @Resource
    private DictBo dictBo;

    @Resource
    private PlanCountService planCountService;

    @Resource
    private UserRedisHelper userRedisHelper;

    @Autowired
    private ProjectSchemeService projectSchemeService;

    @Autowired
    private DemandManagementService demandManagementService;

    @Resource
    private StatusCountService statusCountService;

    @Override
    public List<SimpleVo> getReportFormsTypeList() {
        List<DictValueVO> dictValueVOList = dictBo.getDictList(DictConstant.Report_Forms_Type);
        List<SimpleVo> simpleVoList = new ArrayList<>();
        dictValueVOList.forEach(o -> {
            SimpleVo simpleVo = new SimpleVo();
            simpleVo.setId(o.getSubDictId());
            simpleVo.setName(o.getDescription());
            simpleVoList.add(simpleVo);
        });
        return simpleVoList;
    }

    @Override
    public List<SimpleVo> getReportFormsList(String typeId) {
        List<SimpleVo> simpleVoList = new ArrayList<>();
        if (typeId.equals("0") || StrUtil.isBlank(typeId)) {
            List<DictValueVO> dictValueVOList = dictBo.getDictList(DictConstant.Report_Forms_Type);
            List<String> dictIdList = dictValueVOList.stream().map(DictValueVO::getSubDictId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(dictIdList)) {
                dictIdList.forEach(o -> {
                    if (StringUtils.hasText(o)) {
                        List<SimpleVo> dictIdAndDescList = dictBo.getDictIdAndDesList(o);
                        simpleVoList.addAll(dictIdAndDescList);
                    }
                });
            }
        } else {
            List<SimpleVo> dictIdAndDescList = dictBo.getDictIdAndDesList(typeId);
            simpleVoList.addAll(dictIdAndDescList);
        }
        return simpleVoList;
    }

    @Override
    public PlanCountVo getPlanStatusStatistic(String projectId) throws Exception {
        if (!StringUtils.hasText(projectId)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "项目id不能为空");
        }
        LambdaQueryWrapperX<ProjectScheme> schemeLambdaQueryWrapper = new LambdaQueryWrapperX<>();
        schemeLambdaQueryWrapper.eq(ProjectScheme :: getProjectId,projectId);
        schemeLambdaQueryWrapper.groupBy(ProjectScheme :: getStatus);
        schemeLambdaQueryWrapper.select("`status`,count(*) count ");
        List<Map<String, Object>> maps = projectSchemeService.listMaps(schemeLambdaQueryWrapper);
        Integer unFinishCount = 0;
        Integer finishCount = 0;
        Integer runningCount = 0;
        PlanCountVo planCountVo = new PlanCountVo();
        planCountVo.setFinishCount(finishCount);
        planCountVo.setRunningCount(runningCount);
        planCountVo.setUnFinishCount(unFinishCount);
        if(!CollectionUtils.isEmpty(maps)){
            maps.forEach(p ->{
                if(String.valueOf(Status.PENDING.getCode()).equals(p.get("status"))){
                    planCountVo.setUnFinishCount( p.get("count") == null ? 0 : ((Long)p.get("count")).intValue());
                }
                else if(String.valueOf(Status.PUBLISHED.getCode()).equals(p.get("status"))){
                    planCountVo.setRunningCount( p.get("count") == null ? 0 : ((Long)p.get("count")).intValue());
                }
                else if(String.valueOf(Status.FINISHED.getCode()).equals(p.get("status"))){
                    planCountVo.setFinishCount( p.get("count") == null ? 0 : ((Long)p.get("count")).intValue());
                }
            });
        }
        return planCountVo;
    }

    @Override
    public List<PrincipalStatisticVo> getPlanPrincipalStatistic(String projectId) {
        if (!StringUtils.hasText(projectId)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "项目id不能为空");
        }
        List<PrincipalStatisticVo> principalStatisticVos = new ArrayList<>();

        LambdaQueryWrapperX<ProjectScheme> schemeLambdaQueryWrapper = new LambdaQueryWrapperX<>();
        schemeLambdaQueryWrapper.eq(ProjectScheme :: getProjectId,projectId);
        schemeLambdaQueryWrapper.isNotNull(ProjectScheme :: getRspUser);
        schemeLambdaQueryWrapper.isNotNull(ProjectScheme :: getStatus);
        schemeLambdaQueryWrapper.groupBy(ProjectScheme :: getRspUser, ProjectScheme :: getStatus);
        schemeLambdaQueryWrapper.select("rsp_user rspUser,`status`,count(*) count ");
        List<Map<String, Object>> maps = projectSchemeService.listMaps(schemeLambdaQueryWrapper);
        if(!CollectionUtils.isEmpty(maps)){
            List<Object> rspUsers = maps.stream().map(map -> map.get("rspUser")).distinct().collect(Collectors.toList());
            List<String> rspUserList = (List<String>)(List)rspUsers;
            Map<String, SimpleUser> simpleUserMap = userRedisHelper.getSimpleUserMapByUserIds(rspUserList);
            Map<String,PrincipalStatisticVo> statisticVoMap = new HashMap<>();
            maps.forEach(p ->{
                String status = String.valueOf(p.get("status"));
                String rspUser = String.valueOf(p.get("rspUser"));
                int count = ((Long)p.get("count")).intValue();
                PrincipalStatisticVo principalStatisticVo = statisticVoMap.get(rspUser);
                if(principalStatisticVo == null){
                    principalStatisticVo =  new PrincipalStatisticVo();
                    principalStatisticVo.setUnFinishCount(0);
                    principalStatisticVo.setFinishingCount(0);
                    principalStatisticVo.setFinishCount(0);
                    principalStatisticVo.setCount(0);
                    principalStatisticVo.setPrincipalId(rspUser);
                    SimpleUser simpleUser = simpleUserMap.get(rspUser);
                    if(simpleUser != null){
                        principalStatisticVo.setPrincipalName(simpleUser.getName());
                    }
                    principalStatisticVos.add(principalStatisticVo);
                }
                if(String.valueOf(Status.PENDING.getCode()).equals(status)){
                    principalStatisticVo.setUnFinishCount(count);
                }
                else if(String.valueOf(Status.PUBLISHED.getCode()).equals(status)){
                    principalStatisticVo.setFinishingCount(count);
                }
                else if(String.valueOf(Status.FINISHED.getCode()).equals(status)){
                    principalStatisticVo.setFinishCount(count);
                }
                principalStatisticVo.setCount(principalStatisticVo.getCount() + count);
            });
        }
        return principalStatisticVos;
    }

    @Override
    public List<StatusCountVO> getPlanEveryStatusStatistic(String projectId) throws Exception {
        if (!StringUtils.hasText(projectId)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "项目id不能为空");
        }
        Date date = new Date();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String nowDay = df.format(date);
        date = df.parse(nowDay);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, -13);
        List<String> dateList = new ArrayList<>();
        dateList.add(df.format(calendar.getTime()));
        dateList.add(nowDay);
//        List<StatusCountDTO> statusCountVoList = statusCountService.queryForList(new OrionChainWrapper<>(StatusCount.class).eq(StatusCount::getProjectId, SqlOperatorDict.AND, projectId).
//                eq(StatusCount::getType, SqlOperatorDict.AND, type).
//                bt(StatusCount::getNowDate, SqlOperatorDict.AND, dateList.toArray()));
        List<PlanCount> planCountDTOList = planCountService.list(new LambdaQueryWrapper<>(PlanCount.class).eq(PlanCount::getProjectId, projectId).
                between(PlanCount::getDateStr, dateList.get(0), dateList.get(1)));
        List<StatusCountVO> statusCountVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(planCountDTOList)) {
            return statusCountVOList;
        }
        Map<String, List<PlanCount>> maps = planCountDTOList.stream().collect(Collectors.groupingBy(PlanCount::getDateStr));
        for (String dateStr : maps.keySet()) {
            StatusCountVO statusCountVO = new StatusCountVO();
            statusCountVO.setFinishCount(maps.get(dateStr).stream().mapToInt(PlanCount::getFinishCount).sum());
            statusCountVO.setFinishingCount(maps.get(dateStr).stream().mapToInt(PlanCount::getFinishingCount).sum());
            statusCountVO.setUnFinishCount(maps.get(dateStr).stream().mapToInt(PlanCount::getUnFinishCount).sum());
            statusCountVO.setDate(dateStr);
            statusCountVOList.add(statusCountVO);
        }
        return statusCountVOList.stream().sorted(Comparator.comparing(StatusCountVO::getDate)).collect(Collectors.toList());
    }

    @Override
    public Map<String, Integer> getPlanEverydayIncreasedStatistic(String projectId) {
        if (!StringUtils.hasText(projectId)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "项目id不能为空");
        }
        LambdaQueryWrapperX<ProjectScheme> schemeLambdaQueryWrapper = new LambdaQueryWrapperX<>();
        schemeLambdaQueryWrapper.eq(ProjectScheme :: getProjectId,projectId);
        schemeLambdaQueryWrapper.groupBy("DATE_FORMAT(create_time,\"%Y-%m-%d\")");
        schemeLambdaQueryWrapper.orderByDesc("DATE_FORMAT(create_time,\"%Y-%m-%d\")");
        schemeLambdaQueryWrapper.select("DATE_FORMAT(create_time,\"%Y-%m-%d\") createTime,count(*) count ");
        List<Map<String, Object>> maps = projectSchemeService.listMaps(schemeLambdaQueryWrapper);
        Map<String, Integer> dayToCountMap = new HashMap<>();
        if (CollectionUtils.isEmpty(maps)) {
            return dayToCountMap;
        }
        maps.forEach(p ->{
            dayToCountMap.put(String.valueOf(p.get("createTime")),((Long)p.get("count")).intValue());
        });

        return dayToCountMap;
    }


    @Override
    public StatusCountVO getDemandStatusStatistic(String projectId) throws Exception {
        if (!StringUtils.hasText(projectId)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "项目id不能为空");
        }
        LambdaQueryWrapperX<DemandManagement> schemeLambdaQueryWrapper = new LambdaQueryWrapperX<>();
        schemeLambdaQueryWrapper.eq(DemandManagement :: getProjectId,projectId);
        schemeLambdaQueryWrapper.groupBy(ProjectScheme :: getStatus);
        schemeLambdaQueryWrapper.select("`status`,count(*) count ");
        List<Map<String, Object>> maps = demandManagementService.listMaps(schemeLambdaQueryWrapper);
        Integer unFinishCount = 0;
        Integer finishCount = 0;
        Integer runningCount = 0;
        StatusCountVO statusCountVO = new StatusCountVO();
        statusCountVO.setFinishCount(finishCount);
        statusCountVO.setFinishingCount(runningCount);
        statusCountVO.setUnFinishCount(unFinishCount);
        if(!CollectionUtils.isEmpty(maps)){
            maps.forEach(p ->{
                if(String.valueOf(DeliverStatusEnum.UN_START.getStatus()).equals(p.get("status"))){
                    statusCountVO.setUnFinishCount( p.get("count") == null ? 0 : ((Long)p.get("count")).intValue());
                }
                else if(String.valueOf(DeliverStatusEnum.DEALING.getStatus()).equals(p.get("status"))){
                    statusCountVO.setFinishingCount( p.get("count") == null ? 0 : ((Long)p.get("count")).intValue());
                }
                else if(String.valueOf(DeliverStatusEnum.DEAL.getStatus()).equals(p.get("status"))){
                    statusCountVO.setFinishCount( p.get("count") == null ? 0 : ((Long)p.get("count")).intValue());
                }
            });
        }
        return statusCountVO;
    }

    @Override
    public List<PrincipalStatisticVo> getDemandPrincipalStatistic(String projectId) {
        if (!StringUtils.hasText(projectId)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "项目id不能为空");
        }
        List<PrincipalStatisticVo> principalStatisticVos = new ArrayList<>();

        LambdaQueryWrapperX<DemandManagement> schemeLambdaQueryWrapper = new LambdaQueryWrapperX<>();
        schemeLambdaQueryWrapper.eq(DemandManagement :: getProjectId,projectId);
        schemeLambdaQueryWrapper.isNotNull(DemandManagement :: getPrincipalId);
        schemeLambdaQueryWrapper.isNotNull(DemandManagement :: getStatus);
        schemeLambdaQueryWrapper.groupBy(DemandManagement :: getPrincipalId, DemandManagement :: getStatus);
        schemeLambdaQueryWrapper.select("principal_id rspUser,`status`,count(*) count ");
        List<Map<String, Object>> maps = demandManagementService.listMaps(schemeLambdaQueryWrapper);
        if(!CollectionUtils.isEmpty(maps)){
            List<Object> rspUsers = maps.stream().map(map -> map.get("rspUser")).distinct().collect(Collectors.toList());
            List<String> rspUserList = (List<String>)(List)rspUsers;
            Map<String, SimpleUser> simpleUserMap = userRedisHelper.getSimpleUserMapByUserIds(rspUserList);
            Map<String,PrincipalStatisticVo> statisticVoMap = new HashMap<>();
            maps.forEach(p ->{
                String status = String.valueOf(p.get("status"));
                String rspUser = String.valueOf(p.get("rspUser"));
                int count = ((Long)p.get("count")).intValue();
                PrincipalStatisticVo principalStatisticVo = statisticVoMap.get(rspUser);
                if(principalStatisticVo == null){
                    principalStatisticVo =  new PrincipalStatisticVo();
                    principalStatisticVo.setUnFinishCount(0);
                    principalStatisticVo.setFinishingCount(0);
                    principalStatisticVo.setFinishCount(0);
                    principalStatisticVo.setCount(0);
                    principalStatisticVo.setPrincipalId(rspUser);
                    SimpleUser simpleUser = simpleUserMap.get(rspUser);
                    if(simpleUser != null){
                        principalStatisticVo.setPrincipalName(simpleUser.getName());
                    }
                    principalStatisticVos.add(principalStatisticVo);
                }
                if(String.valueOf(Status.PENDING.getCode()).equals(status)){
                    principalStatisticVo.setUnFinishCount(count);
                }
                else if(String.valueOf(Status.PUBLISHED.getCode()).equals(status)){
                    principalStatisticVo.setFinishingCount(count);
                }
                else if(String.valueOf(Status.FINISHED.getCode()).equals(status)){
                    principalStatisticVo.setFinishCount(count);
                }
                principalStatisticVo.setCount(principalStatisticVo.getCount() + count);
            });
        }
        return principalStatisticVos;
    }

    @Override
    public List<StatusCountVO> getDemandEveryStatusStatistic(String projectId) throws Exception {
        if (!StringUtils.hasText(projectId)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "项目id不能为空");
        }
        Date date = new Date();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String nowDay = df.format(date);
        date = df.parse(nowDay);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, -13);
        List<String> dateList = new ArrayList<>();
        dateList.add(df.format(calendar.getTime()));
        dateList.add(nowDay);
//        List<StatusCountDTO> statusCountVoList = statusCountService.queryForList(new OrionChainWrapper<>(StatusCount.class).eq(StatusCount::getProjectId, SqlOperatorDict.AND, projectId).
//                eq(StatusCount::getType, SqlOperatorDict.AND, type).
//                bt(StatusCount::getNowDate, SqlOperatorDict.AND, dateList.toArray()));
        List<StatusCount>statusCountDTOList = statusCountService.list(new LambdaQueryWrapper<>(StatusCount.class).eq(StatusCount::getProjectId, projectId).
                between(StatusCount::getNowDate, dateList.get(0), dateList.get(1)));
        List<StatusCountVO> statusCountVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(statusCountDTOList)) {
            return statusCountVOList;
        }
        Map<Date, List<StatusCount>> maps = statusCountDTOList.stream().collect(Collectors.groupingBy(StatusCount::getNowDate));
        for (Date dateStr : maps.keySet()) {
            StatusCountVO statusCountVO = new StatusCountVO();
            statusCountVO.setFinishCount(maps.get(dateStr).stream().mapToInt(StatusCount::getFinishCount).sum());
            statusCountVO.setFinishingCount(maps.get(dateStr).stream().mapToInt(StatusCount::getFinishingCount).sum());
            statusCountVO.setUnFinishCount(maps.get(dateStr).stream().mapToInt(StatusCount::getUnFinishCount).sum());
            statusCountVO.setDate(new SimpleDateFormat("yyyy-MM-dd").format(dateStr));
            statusCountVOList.add(statusCountVO);
        }
        return statusCountVOList.stream().sorted(Comparator.comparing(StatusCountVO::getDate)).collect(Collectors.toList());
    }

    @Override
    public Map<String, Integer> getDemandEverydayIncreasedStatistic(String projectId) {
        if (!StringUtils.hasText(projectId)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "项目id不能为空");
        }
        LambdaQueryWrapperX<DemandManagement> schemeLambdaQueryWrapper = new LambdaQueryWrapperX<>();
        schemeLambdaQueryWrapper.eq(DemandManagement :: getProjectId,projectId);
        schemeLambdaQueryWrapper.groupBy("DATE_FORMAT(create_time,\"%Y-%m-%d\")");
        schemeLambdaQueryWrapper.orderByDesc("DATE_FORMAT(create_time,\"%Y-%m-%d\")");
        schemeLambdaQueryWrapper.select("DATE_FORMAT(create_time,\"%Y-%m-%d\") createTime,count(*) count ");
        List<Map<String, Object>> maps = demandManagementService.listMaps(schemeLambdaQueryWrapper);
        Map<String, Integer> dayToCountMap = new HashMap<>();
        if (CollectionUtils.isEmpty(maps)) {
            return dayToCountMap;
        }
        maps.forEach(p ->{
            dayToCountMap.put(String.valueOf(p.get("createTime")),((Long)p.get("count")).intValue());
        });

        return dayToCountMap;
    }
}
