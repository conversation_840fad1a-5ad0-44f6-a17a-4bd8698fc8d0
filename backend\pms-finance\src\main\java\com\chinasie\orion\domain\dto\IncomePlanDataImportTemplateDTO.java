package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * IncomePlanData DTO对象
 *
 * <AUTHOR>
 * @since 2024-09-29 19:19:47
 */
@ApiModel(value = "IncomePlanDataDTO对象", description = "收入计划填报数据")
@Data
@ExcelIgnoreUnannotated
public class IncomePlanDataImportTemplateDTO extends ObjectDTO implements Serializable {

    /**
     * 专业中心
     */
    @ApiModelProperty(value = "专业中心")
    @ExcelProperty(value = "*专业中心 ", index = 0)
    private String expertiseCenter;

    /**
     * 专业所
     */
    @ApiModelProperty(value = "专业所")
    @ExcelProperty(value = "*专业所 ", index = 1)
    private String expertiseStation;


    /**
     * 开票/收入确认公司
     */
    @ApiModelProperty(value = "开票/收入确认公司")
    @ExcelProperty(value = "*开票/收入确认公司 ", index = 2)
    private String billingCompany;


    /**
     * 合同编码
     */
    @ApiModelProperty(value = "合同编码")
    @ExcelProperty(value = "*合同编号 ", index = 3)
    private String contractNumber;

    @ApiModelProperty(value = "甲方合同号")
    @ExcelProperty(value = "甲方合同号", index = 4)
    private String partyAContractNumber;




    /**
     * 合同ID
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "*合同名称 ", index = 5)
    private String contractName;

    @ApiModelProperty(value = "里程碑名称")
    @ExcelProperty(value = "*里程碑名称 ", index = 6)
    private String milestoneName;

    @ApiModelProperty(value = "甲方单位名称")
    @ExcelProperty(value = "*甲方单位名称 ", index = 7)
    private String partyADeptId;


    @ApiModelProperty(value = "集团内（基地）/外")
    @ExcelProperty(value = "*集团内（基地）/外 ", index = 8)
    private String internalExternal;

    @ApiModelProperty(value = "项目编号")
    @ExcelProperty(value = "*项目编号 ", index = 9)
    private String projectNumber;

    @ApiModelProperty(value = "收入确认类型")
    @ExcelProperty(value = "*收入确认类型 ", index = 10)
    private String incomeConfirmType;

    @ApiModelProperty(value = "预计开票/暂估日期")
    @ExcelProperty(value = "*预计开票/暂估日期 ", index = 11)
    @DateTimeFormat("yyyy/MM/dd")
    private Date estimateInvoiceDate;

    @ApiModelProperty(value = "税率")
    @ExcelProperty(value = "*税率 ", index = 12)
    private String taxRate;

    /**
     * 本次暂估金额（价税合计）
     */
    @ApiModelProperty(value = "本次暂估金额（价税合计）")
    @ExcelProperty(value = "*本次暂估金额（价税合计） ", index = 13)
    private BigDecimal estimateAmt;

    /**
     * 开票金额（价税合计）
     */
    @ApiModelProperty(value = "本次开票金额（价税合计）")
    @ExcelProperty(value = "*本次开票金额（价税合计） ", index = 14)
    private BigDecimal invAmt;

    /**
     * 作废发票合计（价税合计）
     */
    @ApiModelProperty(value = "本次作废发票金额（价税合计）")
    @ExcelProperty(value = "*本次作废发票金额（价税合计）", index = 15)
    private BigDecimal cancelInvAmt;


    /**
     * 本次冲销暂估金额（价税合计）
     */
    @ApiModelProperty(value = "本次冲销暂估金额（价税合计）")
    @ExcelProperty(value = "*本次冲销暂估金额（价税合计） ", index = 16)
    private BigDecimal writeOffAmt;


    /**
     * 预收款转收入金额（价税合计）
     */
    @ApiModelProperty(value = "预收款转收入金额（价税合计）")
    @ExcelProperty(value = "*预收款转收入金额（价税合计） ", index = 17)
    private BigDecimal advancePayIncomeAmt;

    /**
     * 本次收入计划金额（价税合计）
     */
    @ApiModelProperty(value = "本次收入计划金额（不含税）")
    @ExcelProperty(value = "*本次收入计划金额（不含税） ", index = 18)
    private BigDecimal incomePlanAmt;

    /**
     * 项目负责人
     */
    @ApiModelProperty(value = "项目负责人")
    @ExcelProperty(value = "*项目负责人 ", index = 19)
    private String projectRspUserId;

    /**
     * 其他说明
     */
    @ApiModelProperty(value = "备注说明")
    @ExcelProperty(value = "*备注说明 ", index = 20)
    private String otherNotes;

    @ApiModelProperty(value = "验收日期")
    @ExcelProperty(value = "*备注说明 ", index = 21)
    @DateTimeFormat("yyyy/MM/dd")
    private String acceptanceDate;

    @ApiModelProperty(value = "甲方单位名称")
    private String partyADeptIdName;

    @ApiModelProperty(value = "所属行业")
    private String industry;

}
