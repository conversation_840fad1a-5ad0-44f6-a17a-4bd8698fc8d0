<template>
  <Layout3
    v-if="tabsOption?.length>0"
    :defaultActionId="actionId"
    :menuData="tabsOption"
    :projectData="projectInfo"
    :type="2"
    @menuChange="contentTabsChange2"
  >
    <template #tabsRight>
      <RemoveBtn />
    </template>
    <!--  概述  -->
    <DetailsTab
      v-if="actionId===9991 && isPower('WT_container_02', powerData)"
      :id="id"
      @editSuccess="editSuccess"
    />
    <!-- 关联内容 -->
    <ContactTab
      v-if="actionId===9992 && isPower('WT_container_03', powerData)"
      :id="id"
    />
    <!-- 流程控制 -->
    <QuestionProcessTab
      v-if="actionId===9993 && isPower('WT_container_04', powerData)"
      :id="id"
    />
  </Layout3>
</template>

<script lang="ts">
import {
  defineComponent, reactive, toRefs, provide, readonly, ref, onMounted, inject, getCurrentInstance, computed,
} from 'vue';
import {
  Layout3, isPower, useProjectPower,
} from 'lyra-component-vue3';
// import { Layout2 } from '/@/components/Layout2.0';
import DetailsTab from './DetailsTab/index.vue';
import ContactTab from './contactTab/index.vue';
import QuestionProcessTab from './questionProcessTab/index.vue';
// import QuestionProcessTab from './questionProcessTab/indexCopy.vue';
import { useRoute } from 'vue-router';
import useIndex from '/@/views/pms/projectLaborer/zkhooks/useLocalS.js';
import RemoveBtn from '/@/views/pms/projectLaborer/componentsList/returnBtn/index.vue';
import { questionDetailsPageApi } from '/@/views/pms/projectLaborer/api/questionManage';
import { setTitleByRootTabsKey } from '/@/utils';
export default defineComponent({
  // name: 'EndDetails',
  components: {
    Layout3,
    DetailsTab,
    ContactTab,
    QuestionProcessTab,
    RemoveBtn,
  },
  setup() {
    const route = useRoute();
    const state = reactive({
      tabsIndex: Number(route.query.type),
      id: route.query.id,
      projectId: route.query.projectId,
      className: '',
      actionId: 9991,
      projectInfo: {},
      powerData: [],
    });
    state.powerData = inject('powerData');
    const state6 = reactive({
      tabsOption: [
        // { name: '概述' }, // 0
        // { name: '关联内容' }, // 1
        // // { name: '流程' }, // 2
        // // { name: '日志' }, // 3
      ],
    });
    const QuestionDetailsLocal = useIndex('QuestionDetailsLocal');
    const questionContactLocal = useIndex('questionContactLocal');
    function contentTabsChange2(index) {
      // questionContactLocal.value = parseInt(JSON.stringify(0));
      // state.tabsIndex = index;
      QuestionDetailsLocal.value = index.id;
      state.actionId = index.id;
    }
    const internalInstance = getCurrentInstance();
    // 获取权限
    async function getProjectPower() {
      return new Promise((resolve, reject) => {
        useProjectPower(
          { pageCode: 'PMS0021' },
          (powerList) => {
            resolve(powerList || []);
          },
          internalInstance,
        );
      });
    }
    /* 获取详情 */
    const getDetail = async () => {
      const love = {
        id: state.id,
        className: 'QuestionManagement',
        moduleName: '项目管理-问题管理',
        type: 'GET',
        remark: `打开了【${state.id}】`,
      };
      await questionDetailsPageApi(state.id, love)
        .then((res) => {
          if (res) {
            state.projectInfo = { ...res };
            setTitleByRootTabsKey(route?.query?.rootTabsKey, res.name);
          }
        })
        .catch(() => {});
    };
    onMounted(async () => {
      state.powerData = await getProjectPower();
      isPower('WT_container_02', state.powerData) && state6.tabsOption.push({
        name: '概述',
        id: 9991,
      });
      isPower('WT_container_03', state.powerData) && state6.tabsOption.push({
        name: '关联内容',
        id: 9992,
      });
      isPower('WT_container_04', state.powerData) && state6.tabsOption.push({
        name: '流程',
        id: 9993,
      });
      if (!questionContactLocal.value) {
        questionContactLocal.value = parseInt(JSON.stringify(9991));
      }
      if (QuestionDetailsLocal.value !== 9991) {
        state.tabsIndex = QuestionDetailsLocal.value;
      }
      await getDetail();
    });
    /* 问题单条qusetionItemId和项目projectId */
    const qusetionItemId = ref(state.id);
    const projectId = ref(state.projectId);
    provide('qusetionItemId', readonly(qusetionItemId));
    provide('projectId', readonly(projectId));
    // 权限分发
    provide(
      'powerData',
      computed(() => state.powerData),
    );
    provide(
      'projectInfo',
      computed(() => state.projectInfo),
    );
    function getDetails() {
      return state.projectInfo;
    }
    function editSuccess() {
      getDetail();
    }
    provide('getForm', getDetail);
    provide('getDetails', getDetails);
    return {
      ...toRefs(state),
      ...toRefs(state6),
      contentTabsChange2,
      isPower,
      editSuccess,
    };
  },
});
</script>
