package com.chinasie.orion.service;


import java.lang.String;
import java.util.List;


import com.chinasie.orion.domain.dto.CollaborativeCompilationTaskDTO;
import com.chinasie.orion.domain.dto.ProjectSchemeDTO;
import com.chinasie.orion.domain.dto.UrgePlanRequestDTO;
import com.chinasie.orion.domain.dto.collaborativecompilationtask.TaskFallBackDTO;
import com.chinasie.orion.domain.dto.collaborativecompilationtask.TaskIssueDTO;
import com.chinasie.orion.domain.dto.projectscheme.IssueDTO;
import com.chinasie.orion.domain.entity.CollaborativeCompilationTask;
import com.chinasie.orion.domain.request.ListRequest;
import com.chinasie.orion.domain.vo.CollaborativeCompilationTaskVO;
import com.chinasie.orion.domain.vo.ProjectSchemeVO;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * CollaborativeCompilationTask 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 17:04:08
 */
public interface CollaborativeCompilationTaskService extends OrionBaseService<CollaborativeCompilationTask> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    CollaborativeCompilationTaskVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param collaborativeCompilationTaskDTO
     */
    String create(CollaborativeCompilationTaskDTO collaborativeCompilationTaskDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param collaborativeCompilationTaskDTO
     */
    Boolean edit(CollaborativeCompilationTaskDTO collaborativeCompilationTaskDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<CollaborativeCompilationTaskVO> pages(Page<CollaborativeCompilationTaskDTO> pageRequest) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<CollaborativeCompilationTaskVO> vos) throws Exception;

    List<CollaborativeCompilationTaskVO> getList(CollaborativeCompilationTaskDTO collaborativeCompilationTaskDTO) throws Exception;


    List<String> getUserTask( String userId,String approvalId);

    /**
     * 上移
     *
     * @param id
     * @return
     */
    Boolean up(String id) throws Exception;

    /**
     * 下移
     *
     * @param id
     * @return
     */
    Boolean down(String id) throws Exception;

    /**
     * 置顶
     *
     * @param id
     * @return
     */
    Boolean top(String id) throws Exception;


    /**
     * 计划下发
     *
     * @param issueDTO
     * @return
     */
    Boolean issue(TaskIssueDTO issueDTO) throws Exception;

    /**
     * 取消置顶
     *
     * @param id
     * @return
     */
    Boolean unTop(String id) throws Exception;

    /**
     * 计划撤回
     *
     * @param id
     * @return
     */
    String revocation(String id);


    /**
     * 计划完成确认
     *
     * @param collaborativeCompilationTaskDTO
     * @return
     */
    Boolean finish(CollaborativeCompilationTaskDTO collaborativeCompilationTaskDTO) throws Exception;

    /**
     * 计划催办
     *
     * @param urgePlanRequestDTO
     * @return
     */
    Boolean urgePlan(UrgePlanRequestDTO urgePlanRequestDTO);


    /**
     * 更新实际开始时间
     *
     * @param id
     * @return
     */
    Boolean updateActualBeginTime(String id);


    /**
     * 项目计划退回
     *
     * @param fallBackDTO
     * @return
     */
    Boolean taskFallback(TaskFallBackDTO fallBackDTO);


    /**
     * 完成确认
     * @param collaborativeCompilationTaskDTO
     * @return
     * @throws Exception
     */
    Boolean completeConfirmation(CollaborativeCompilationTaskDTO collaborativeCompilationTaskDTO) throws Exception;


    Boolean getDocument(String modelId, String approvalId) throws Exception;

    Boolean transfer(CollaborativeCompilationTaskDTO collaborativeCompilationTaskDTO);

    List<UserVO> getUser();

    List<DataStatusVO> getStatus();

    String  getContent(String id) throws Exception;

    Boolean editContent(CollaborativeCompilationTaskDTO collaborativeCompilationTaskDTO) throws Exception;
}
