package com.chinasie.orion.domain.entity;

import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.DictDataBind;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;

/**
 * ProjectContribute Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-27 19:32:36
 */
@TableName(value = "pms_project_contribute")
@ApiModel(value = "ProjectContributeEntity对象", description = "项目贡献情况")
@Data

public class ProjectContribute extends  ObjectEntity  implements Serializable{

    /**
     * 贡献类型
     */
    @ApiModelProperty(value = "贡献类型")
    @TableField(value = "type")
    @FieldBind(dataBind = DictDataBind.class, type = "pms_project_contribute", target = "typeName")
    private String type;

    /**
     * 奖项类型
     */
    @ApiModelProperty(value = "奖项类型")
    @TableField(value = "award_type")
    private String awardType;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField(value = "name")
    private String name;

    /**
     * 主要人员
     */
    @ApiModelProperty(value = "主要人员")
    @TableField(value = "major_user")
    private String majorUser;

    /**
     * 授权时间
     */
    @ApiModelProperty(value = "授权时间")
    @TableField(value = "authorization_time")
    private Date authorizationTime;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    private String projectId;

    @TableField(exist = false)
    private String typeName;

}
