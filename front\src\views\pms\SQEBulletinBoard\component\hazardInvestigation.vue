<!--
 * @Description:隐患排查分析
 * @Autor: laotao117
 * @Date: 2024-08-21 18:36:08
 * @LastEditors: laotao117
 * @LastEditTime: 2024-09-06 14:27:40
-->
<template>
  <BasicCard
    :isBorder="true"
    title="隐患排查分析"
    class="card-border active-box"
  >
    <template #title>
      <div class="card-border-tit">
        隐患排查分析
        <div class="card-border-tit-desc">
          <span v-if="state.problemsDate?.isStandard">（公司状态：<b class="tips-b">达标 绿灯</b></span>
          <span v-else>（公司状态：<b class="tips-b">不达标 红灯</b></span>
          <img
            v-if="state.problemsDate?.isStandard"
            class="img-tips"
            src="../images/success.png"
          >
          <img
            v-else
            class="img-tips"
            src="../images/error.png"
          >

          <span> 上级发现 {{ state.problemsDate?.groupSum }} ，公司监督 {{ state.problemsDate?.companySum }}， 部门自查自纠 {{
            state.problemsDate?.deptSum }} ）</span>
        </div>
      </div>
    </template>
    <template #titleRight>
      <div class="flex-right-box">
        <Select
          v-model:value="state.dateType"
          :showSearch="false"
          style="width: 100px;"
          :options="options"
          @change="init"
        />

        <DatePicker
          v-model:value="state.yearValue"
          :picker="state.dateType"
          :disabled-date="disabledDate"
          :allowClear="false"
          @change="init"
        />
      </div>
    </template>
    <div class="charts-box ">
      <div class="charts-box-com">
        <Chart :option="state.chartOptions1" />
      </div>
      <div class="charts-box-hr" />
      <div class="charts-box-com">
        <Chart :option="state.chartOptions2" />
      </div>
    </div>
  </BasicCard>
</template>
<script setup lang="ts">
import {
  DatePicker,
  Select,
} from 'ant-design-vue';
import dayjs from 'dayjs';
import {
  BasicCard,
  Chart,
} from 'lyra-component-vue3';
import { onMounted, reactive, onActivated } from 'vue';
import Api from '/@/api';
// 导入 png
const success = require('../images/success.png');
const error = require('../images/error.png');
const disabledDate = (time) => dayjs(time).year() < 2024;
// options 年度，按年统计，自然年，
const options = [
  {
    label: '年度',
    value: 'year',
  },
  {
    label: '季度',
    value: 'quarter',
  },
  {
    label: '月度',
    value: 'month',
  },
];

// 上级发现 公司监督 部门自查自纠 三个数据 柱形图

const state: any = reactive({
  yearValue: dayjs(),
  dateType: 'year',
  problemsDate: {},
  planData: {},
  // 当前日期
  currentDate: dayjs().format('YYYY-MM-DD'),
  riskLevel: '',
  chartOptions1: {},
  chartOptions2: {},
});
// 缓存载入生命周期
onActivated(() => {
  init();
});
onMounted(() => {
  init();
});
function init() {
  // 根据state.dateType来格式化state.yearValue时间
  // 1.年度 2.季度 3.月度
  let queryCheckProblemsDate = '';
  if (state.dateType === 'year') {
    queryCheckProblemsDate = dayjs(state.yearValue).format('YYYY');
  } else if (state.dateType === 'quarter') {
    queryCheckProblemsDate = dayjs(state.yearValue).format('YYYY-[Q]Q');
  } else if (state.dateType === 'month') {
    queryCheckProblemsDate = dayjs(state.yearValue).format('YYYY-MM');
  }

  new Api('/pms').fetch({
    queryCheckProblemsDateType: state.dateType,
    queryCheckProblemsDate,
  }, 'ampere/ring/board/statistics/total/check/problems', 'POST').then((res) => {
    state.problemsDate = res;
    // res = {
    //   部门: [
    //     {
    //       groupTotal: 0,
    //       isStandard: true,
    //       companyTotal: 15,
    //       deptCount: 496,
    //       name: '材料工程技术中心',
    //     },
    //     {
    //       groupTotal: 0,
    //       isStandard: true,
    //       companyTotal: 1,
    //       deptCount: 10,
    //       name: '安质环技术中心',
    //     },
    //     {
    //       groupTotal: 0,
    //       isStandard: true,
    //       companyTotal: 12,
    //       deptCount: 94,
    //       name: '在役检查技术中心',
    //     },
    //     {
    //       groupTotal: 0,
    //       isStandard: false,
    //       companyTotal: 9,
    //       deptCount: 50,
    //       name: '核安全与运行技术中心',
    //     },
    //     {
    //       groupTotal: 0,
    //       isStandard: true,
    //       companyTotal: 11,
    //       deptCount: 146,
    //       name: '产品研发中心',
    //     },
    //     {
    //       groupTotal: 0,
    //       isStandard: false,
    //       companyTotal: 1,
    //       deptCount: 2,
    //       name: '科研创新中心',
    //     },
    //     {
    //       groupTotal: 0,
    //       isStandard: true,
    //       companyTotal: 18,
    //       deptCount: 126,
    //       name: '检测公司',
    //     },
    //     {
    //       groupTotal: 0,
    //       isStandard: false,
    //       companyTotal: 10,
    //       deptCount: 29,
    //       name: '计量与检验中心',
    //     },
    //     {
    //       groupTotal: 0,
    //       isStandard: false,
    //       companyTotal: 1,
    //       deptCount: 5,
    //       name: '客户成功事业中心',
    //     },
    //     {
    //       groupTotal: 0,
    //       isStandard: false,
    //       companyTotal: 2,
    //       deptCount: 1,
    //       name: '综合管理部',
    //     },
    //     {
    //       groupTotal: 0,
    //       isStandard: false,
    //       companyTotal: 64,
    //       deptCount: 81,
    //       name: '系统工程技术中心',
    //     },
    //     {
    //       groupTotal: 0,
    //       isStandard: true,
    //       companyTotal: 5,
    //       deptCount: 61,
    //       name: '设备可靠性技术中心',
    //     },
    //   ],
    //   groupSum: 0,
    //   isStandard: true,
    //   companySum: 155,
    //   项目部: [
    //     {
    //       groupTotal: 0,
    //       isStandard: true,
    //       companyTotal: 0,
    //       deptCount: 12,
    //       name: '红沿河项目部',
    //     },
    //     {
    //       groupTotal: 0,
    //       isStandard: true,
    //       companyTotal: 1,
    //       deptCount: 125,
    //       name: '宁德项目部',
    //     },
    //     {
    //       groupTotal: 0,
    //       isStandard: true,
    //       companyTotal: 1,
    //       deptCount: 30,
    //       name: '阳江项目部',
    //     },
    //     {
    //       groupTotal: 0,
    //       isStandard: true,
    //       companyTotal: 0,
    //       deptCount: 2,
    //       name: '大亚湾项目部',
    //     },
    //     {
    //       groupTotal: 0,
    //       isStandard: true,
    //       companyTotal: 4,
    //       deptCount: 37,
    //       name: '防城港项目部',
    //     },
    //     {
    //       groupTotal: 0,
    //       isStandard: true,
    //       companyTotal: 0,
    //       deptCount: 3,
    //       name: '台山项目部',
    //     },
    //   ],
    //   deptSum: 1310,
    // };
    let BmData = res?.['部门'] || [];
    let XmbData = res?.['项目部'] || [];

    state.chartOptions1 = {
      title: {
        text: '部门',
        left: 'center',
        top: '0',
        textStyle: {
          color: '#333',
          fontSize: 16,
          fontWeight: 'bold',
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter(params) {
          // isStandard
          let isStandard = params[0].data.isStandard;
          let name = params[0].name;
          let result = '';
          if (isStandard) {
            result = `${name} <img src="${success}" style="width: 40px; height: 40px; margin-right: 5px;" />
            `;
          } else {
            result = `${name} <img src="${error}" style="width: 40px; height: 40px; margin-right: 5px;" />
            `;
          }

          params.forEach((item) => {
            result += `
              <div style="display: flex; align-items: center;">
              <span style="width: 8px; height: 8px; background-color: ${item.color}; border-radius: 50%; display: inline-block; margin-right: 5px;"></span>
              ${item.seriesName}: ${item.value || 0}
            </div>
              `;
          });
          return result;

          // return result;
        },

      },
      legend: {
        data: [
          '上级发现',
          '公司监督',
          '部门自查自纠',
        ],
        top: '30',
        left: 'right',
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: BmData.map((item: any) => item.name),
        axisLine: {
          lineStyle: {
            color: '#999',
          },
        },
        axisLabel: {
          rotate: 45, // 将标签旋转45度
        },
        splitArea: {
          show: true,
          interval: 0,
          areaStyle: {
            color: BmData.map((item: any) => ((!item.isStandard) ? 'rgba(240, 90, 64, 0.2)' : '')),
          },
        },
      },

      yAxis: {
        type: 'value',
        boundaryGap: ['0%', '6%'],
        axisLine: {
          lineStyle: {
            color: '#999',
          },
        },
      },
      series: [
        {
          name: '上级发现',
          type: 'bar',
          barMaxWidth: '20px',
          // barGap: '30%',
          data: BmData.map((item: any) =>
            ({
              value: item.groupTotal,
              isStandard: item.isStandard,
              // backgroundStyle: '#000',
              // backgroundStyle: item.isStandard ? '#linear-gradient( 180deg, #F05A40 0%, rgba(240,90,64,0) 100%);' : '',
            })),

        },
        {
          name: '公司监督',
          type: 'bar',
          barMaxWidth: '20px',
          // barGap: '30%',
          data: BmData.map((item: any) =>
            ({
              value: item.companyTotal,
              isStandard: item.isStandard,
              backgroundStyle: item.isStandard ? '#linear-gradient( 180deg, #F05A40 0%, rgba(240,90,64,0) 100%);' : '',
            })),
        },
        {
          name: '部门自查自纠',
          type: 'bar',
          barMaxWidth: '20px',
          // barGap: '30%',
          data: BmData.map((item: any) =>
            ({
              value: item.deptCount,
              isStandard: item.isStandard,
              backgroundStyle: item.isStandard ? '#linear-gradient( 180deg, #F05A40 0%, rgba(240,90,64,0) 100%);' : '',

            })),
        },
      ],
    };

    state.chartOptions2 = {
      title: {
        text: '项目部',
        left: 'center',
        top: '0',
        textStyle: {
          color: '#333',
          fontSize: 16,
          fontWeight: 'bold',
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter(params) {
          // isStandard
          let isStandard = params[0].data.isStandard;
          let name = params[0].name;
          let result = '';
          if (isStandard) {
            result = `${name} <img src="${success}" style="width: 40px; height: 40px; margin-right: 5px;" />
            `;
          } else {
            result = `${name} <img src="${error}" style="width: 40px; height: 40px; margin-right: 5px;" />
            `;
          }

          params.forEach((item) => {
            result += `
              <div style="display: flex; align-items: center;">
              <span style="width: 8px; height: 8px; background-color: ${item.color}; border-radius: 50%; display: inline-block; margin-right: 5px;"></span>
              ${item.seriesName}: ${item.value || 0}
            </div>
              `;
          });
          return result;

          // return result;
        },

      },
      // legend: {
      //   data: [
      //     '上级发现',
      //     '公司监督',
      //     '部门自查自纠',
      //   ],
      //   top: '30',
      //   left: 'right',
      // },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: XmbData.map((item: any) => item.name),
        axisLine: {
          lineStyle: {
            color: '#999',
          },
        },
        axisLabel: {
          rotate: 45, // 将标签旋转45度
        },
        splitArea: {
          show: true,
          areaStyle: {
            color: XmbData.map((item: any) => ((!item.isStandard) ? 'rgba(240, 90, 64, 0.2)' : '')),
          },
        },
      },
      yAxis: {
        type: 'value',
        boundaryGap: ['0%', '6%'],
        axisLine: {
          lineStyle: {
            color: '#999',
          },
        },
      },
      series: [
        {
          name: '上级发现',
          type: 'bar',
          barMaxWidth: '20px',
          data: XmbData.map((item: any) =>
            ({
              value: item.groupTotal,
              isStandard: item.isStandard,
              backgroundStyle: item.isStandard ? '#linear-gradient( 180deg, #F05A40 0%, rgba(240,90,64,0) 100%);' : '',

            })),

        },
        {
          name: '公司监督',
          type: 'bar',
          barMaxWidth: '20px',
          data: XmbData.map((item: any) =>
            ({
              value: item.companyTotal,
              isStandard: item.isStandard,
              backgroundStyle: item.isStandard ? '#linear-gradient( 180deg, #F05A40 0%, rgba(240,90,64,0) 100%);' : '',
            })),
        },
        {
          name: '部门自查自纠',
          type: 'bar',
          barMaxWidth: '20px',
          data: XmbData.map((item: any) =>
            ({
              value: item.deptCount,
              isStandard: item.isStandard,
              backgroundStyle: item.isStandard ? '#linear-gradient( 180deg, #F05A40 0%, rgba(240,90,64,0) 100%);' : '',

            })),
        },
      ],
    };
  });
}

</script>
<style scoped lang="less">
.tips-b {
  color: #000;
  font-size: 20px;
  font-weight: 800;
}
.img-tips {
  width: 60px;
  height: 60px;
  margin-left: 10px;
}
.card-border {
  border: 1px solid var(--ant-border-color-base);
  padding: 10px 15px;
  margin: 0 !important;
}

.flex-right-box {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 10px;
}

.active-box {

  // 鼠标移入时的样式 显示阴影效果
  &:hover {
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  }
}

.card-border-tit {
  font-weight: 700;
  line-height: 1;
  font-size: 16px;
  display: flex;
  justify-content: flex-start;
  align-items: center;

  &-desc {
    margin-left: 10px;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 500;
    font-size: 14px;
    color: #333333;
    line-height: 1;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
}

.charts-box {
  padding: 20px;

  &-com {
    width: 100%;
    height: 400px;
    // border-bottom: 1px solid #333333;
  }

  &-hr {
    height: 20px;
    width: 100%;
  }

}
</style>