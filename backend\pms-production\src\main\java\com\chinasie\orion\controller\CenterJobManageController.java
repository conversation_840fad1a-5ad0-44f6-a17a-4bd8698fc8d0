package com.chinasie.orion.controller;


import com.chinasie.orion.bo.JobManageTreeBO;
import com.chinasie.orion.domain.dto.JobManageSelectDTO;
import com.chinasie.orion.domain.vo.JobManageTreeVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.service.CenterJobManageService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * CenterJobManage 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-14 10:35:27
 */
@RestController
@RequestMapping("/centerJobManage")
@Api(tags = "中心作业管理")
public class  CenterJobManageController {

    @Autowired
    private CenterJobManageService centerJobManageService;

    @ApiOperation("获取中心作业")
    @PostMapping("/treeList")
    @LogRecord(success = "【{USER{#logUserId}}】查询【中心作业管理】中心作业列表数据", type = "JobManageTree", subType = "列表查询", bizNo = "")
    public ResponseDTO<List<JobManageTreeVO>> getTree(@RequestBody JobManageSelectDTO jobManageSelectDTO){
        List<JobManageTreeVO> res = centerJobManageService.treeList(jobManageSelectDTO);
        return new ResponseDTO<>(res);
    }




    @ApiOperation("获取已分配的作业")
    @PostMapping("/used/treeList")
    @LogRecord(success = "【{USER{#logUserId}}】查询【中心作业管理】已分配的作业列表数据", type = "JobManageTree", subType = "列表查询", bizNo = "")
    public ResponseDTO<List<JobManageTreeBO>> getTreeSelected(@RequestBody JobManageSelectDTO jobManageSelectDTO){
        List<JobManageTreeBO> res = centerJobManageService.usedTreeList(jobManageSelectDTO.getRepairOrgId(),jobManageSelectDTO.getKeyword());
        return new ResponseDTO<>(res);
    }


}
