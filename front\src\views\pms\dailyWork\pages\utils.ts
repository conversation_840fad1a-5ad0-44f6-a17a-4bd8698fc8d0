import { openDrawer, openModal } from 'lyra-component-vue3';
import { h, ref, Ref } from 'vue';
import { Modal } from 'ant-design-vue';
import MaterialInfoForm from './components/MaterialInfoForm.vue';
import StudyReviewForm from './components/StudyReviewForm.vue';
import WorkPackageForm from './components/WorkPackageForm.vue';
import ProgressForm from './components/ProgressForm.vue';
import RiskInfoForm from './components/RiskInfoForm.vue';
import Api from '/@/api';
import SelectPostModal from './pages/components/SelectPostModal.vue';
import AuthPersonModal from '/@/views/pms/overhaulManagement/components/AuthPersonModal.vue';

// 选择物资
export function openMaterialEnterForm(record: any, cb?: () => void) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: '选择物资',
    width: 1000,
    content() {
      return h(MaterialInfoForm, {
        ref: drawerRef,
        record,
      });
    },
    async onOk() {
      await drawerRef.value.submit();
      cb?.();
    },
  });
}

// 新增、编辑研读审查信息
export function openStudyReviewForm(record: any, cb?: () => void) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: record?.id ? '编辑研读审查' : '新增研读审查',
    width: 1000,
    content() {
      return h(StudyReviewForm, {
        ref: drawerRef,
        record,
      });
    },
    async onOk() {
      await drawerRef.value.submit();
      cb?.();
    },
  });
}

// 新增、编辑工作包
export function openWorkPackageForm(record: any, cb?: () => void) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: '大修作业',
    width: 1000,
    content() {
      return h(WorkPackageForm, {
        ref: drawerRef,
        record,
      });
    },
    async onOk() {
      await drawerRef.value.submit();
      cb?.();
    },
  });
}

// 新增、编辑工作进展
export function openWorkProgressForm(record: any, cb?: () => void) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: record?.id ? '编辑工作进展' : '新增工作进展',
    width: 1000,
    content() {
      return h(ProgressForm, {
        ref: drawerRef,
        record,
      });
    },
    async onOk() {
      await drawerRef.value.submit();
      cb?.();
    },
  });
}

// 新增、编辑风险措施
export function openRiskForm(record: any, cb?: () => void) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: record?.id ? '编辑风险措施' : '新增风险措施',
    width: 1000,
    content() {
      return h(RiskInfoForm, {
        ref: drawerRef,
        record,
      });
    },
    async onOk() {
      await drawerRef.value.submit();
      cb?.();
    },
  });
}

// 核验
export function authApi(jobId: string, ids: string[], updateTable: Function) {
  return new Promise((resolve, reject) => {
    new Api(`/pms/job-post-authorize/${jobId}/authorize/validation/new`).fetch(ids, '', 'POST').then((res) => {
      if (res.statusCode !== 200) {
        openModal({
          title: '授权核验结果',
          content() {
            return h(AuthPersonModal, {
              list: res.validationVOList || [],
              message: res.countMessage,
            });
          },
          footer: {
            isOk: false,
            // @ts-ignore
            cancelText: '关闭',
          },
          onCancel() {
            updateTable();
          },
        });
      } else {
        Modal.info({
          title: '系统提示',
          content: res.countMessage,
          okText: '确定',
          onOk() {
            updateTable();
          },
        });
      }
      resolve('');
    }).catch((e) => {
      reject(e);
    });
  });
}

// 选择岗位弹窗
export function openPostTableSelect(userCode: string, selectedList?: any[], cb?: (params: any[]) => void) {
  const drawerRef: Ref = ref();
  openModal({
    title: '选择岗位',
    width: 1200,
    content() {
      return h(SelectPostModal, {
        ref: drawerRef,
        selectedList,
        userCode,
      });
    },
    async onOk() {
      const data = await drawerRef.value.confirm();
      cb?.(data);
    },
  });
}

export function openModalOrDrawer() {

}
