<!--
 * @Author: ouyang
 * @Date: 2025-05-17 22:46:46
 * @LastEditors: ouweipiao
 * @LastEditTime: 2025-05-17 22:53:03
 * @Description: 页面功能描述
 * @version: 0.0.1
 * @@copyright: Copyright (c) 2018, Hand
-->
<script setup lang="ts">
import { UploadList, BasicCard } from 'lyra-component-vue3';
import {
  computed, inject, reactive, ref, unref, watchEffect,
} from 'vue';
import Api from '/@/api';
import { useRoute } from 'vue-router';

const route = useRoute();
const powerData = computed(() => inject('powerData'));
const itemId = ref(route.params.id);
const uploadProps = computed(() => ({
  height: 500,
  isSpacing: true,
  maxSize: 10000,
  accept: '.png,.jpg,.jpeg,.tiff,.gif,.mp4,.wav,.mp3,.acc,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.html,.htm,.rtf,.zip,.rar,.gz,.7z',
  buttonText: '上传附件',
}));
async function listApi() {
  const data: any[] = await new Api('/spm/ncfFormpurchaseRequest/getFileList').fetch({
    id: unref(itemId),
  }, '', 'POST');
  return data.map((item) => {
    delete item.children;
    return item;
  });
}

async function deleteApi(record: Record<string, any>) {
  return new Api('/spm/ncfFormpurchaseRequest/deleteFileList').fetch([{ id: record.id }], '', 'DELETE');
}

async function batchDeleteApi({ rows }) {
  const ids = rows.map((item: Record<string, any>) => ({
    id: item.id,
  }));
  return new Api('/spm/ncfFormpurchaseRequest/deleteFileList').fetch(ids, '', 'DELETE');
}

async function saveApi(files: Record<string, any>[]) {
  return new Api('/spm/ncfFormpurchaseRequest/uploadFile').fetch({
    attachments: files,
    id: unref(itemId),
  }, '', 'POST');
}

</script>

<template>
  <UploadList
    :listApi="listApi"
    type="page"
    :powerData="powerData"
    :powerCode="{
      delete:'PMS_CGLXXQXQ_container_02_02_button_02',
      upload:'PMS_CGLXXQXQ_container_02_02_button_01',
      download:'PMS_CGLXXQXQ_container_02_02_button_03',
    }"
    :edit="false"
    :deleteApi="deleteApi"
    :batchDeleteApi="batchDeleteApi"
    :saveApi="saveApi"
    v-bind="uploadProps"
  />
</template>

<style scoped lang="less">

</style>
