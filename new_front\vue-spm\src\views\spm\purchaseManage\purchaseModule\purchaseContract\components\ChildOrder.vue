<script setup lang="ts">
import {
  BasicTableAction, IOrionTableActionItem,
  Layout, OrionTable,
} from 'lyra-component-vue3';
import {
  inject,
  ref, Ref, unref,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import Api from '/@/api';
import { BasicInjectionsKey } from '../../tokens/basicKeys';
import { parsePriceByNumber } from '/@/views/spm/purchaseManage/purchaseModule/utils';

const router = useRouter();
const tableRef: Ref = ref();
const route = useRoute();
const projectId: Ref<string> = ref(route.params.id as string);
const basicInfo = inject(BasicInjectionsKey);
const tableOptions = {
  rowSelection: {
    onChange(keys: string[], rows: Record<string, any>[]) {
    },
  },
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  pagination: {},
  isSpacing: true,
  smallSearchField: undefined,
  columns: [
    {
      title: '操作',
      dataIndex: 'actions',
      width: 160,
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
    {
      title: '合同编号',
      dataIndex: 'contractNumber',
    },
    {
      title: '采购申请号',
      dataIndex: 'purchaseApplicant',
    },
    {
      title: '合同名称',
      dataIndex: 'contractName',
    },
    {
      title: '合同执行状态',
      dataIndex: 'statusName',
    },
    {
      title: '合同类型',
      dataIndex: 'type',
    },
    {
      title: '供应商',
      dataIndex: 'supplierName',
    },
    {
      title: '支付金额',
      dataIndex: 'payMoney',
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '最终价格（原币）',
      dataIndex: 'finalPrice',
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
  ],
  api: (params:Record<string, any>) => new Api('/spm/contractInfo/getByCode').fetch({
    ...params,
    query: {
      contractNumber: basicInfo?.data?.contractNumber,
    },
  }, '', 'POST'),
};
const actions: IOrionTableActionItem[] = [
  {
    text: '查看',
    event: 'view',
  },
];
function actionClick(actionItem: { event: string }, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'view':
      router.push({
        name: 'ChildOrderInfo',
        params: {
          id: record.id,
        },
        query: {
          source: 'ChildOrderInfo',
        },
      });
      break;
  }
}
</script>

<template>
  <OrionTable
    ref="tableRef"
    class="scroll-table"
    :options="tableOptions"
    false
  >
    <template #actions="{record}">
      <BasicTableAction
        :actions="actions"
        :record="record"
        @actionClick="actionClick($event,record)"
      />
    </template>
  </OrionTable>
</template>

<style scoped lang="less">
.scroll-table {
  ::-webkit-scrollbar {
    height: 15px !important;
    width: 15px !important;
  }
  ::-webkit-scrollbar-thumb {
    border-radius:0;
  }
}
</style>