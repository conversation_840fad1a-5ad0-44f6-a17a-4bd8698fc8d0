<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.google.inject</groupId>
    <artifactId>guice-parent</artifactId>
    <version>3.0</version>
  </parent>

  <artifactId>guice</artifactId>

  <name>Google Guice - Core Library</name>

  <properties>
    <cglib.version>2.2.1-v20090111</cglib.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>javax.inject</groupId>
      <artifactId>javax.inject</artifactId>
      <version>1</version>
    </dependency>
    <dependency>
      <groupId>aopalliance</groupId>
      <artifactId>aopalliance</artifactId>
      <version>1.0</version>
    </dependency>
    <!--
     | Replace with official CGLIB artifact when it's released
    -->
    <dependency>
      <groupId>org.sonatype.sisu.inject</groupId>
      <artifactId>cglib</artifactId>
      <version>${cglib.version}</version>
    </dependency>
    <dependency>
      <groupId>javax.inject</groupId>
      <artifactId>javax.inject-tck</artifactId>
      <version>1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-beans</artifactId>
      <version>3.0.5.RELEASE</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>biz.aQute</groupId>
      <artifactId>bnd</artifactId>
      <version>0.0.384</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.felix</groupId>
      <artifactId>org.apache.felix.framework</artifactId>
      <version>3.0.5</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <!--
       | Add standard LICENSE and NOTICE files
      -->
      <plugin>
        <artifactId>maven-remote-resources-plugin</artifactId>
      </plugin>
      <!--
       | Enable Java5 conformance checks
      -->
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>animal-sniffer-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <!--
           | Temporarily excluded tests
          -->
          <excludes>
            <exclude>**/OSGiContainerTest*</exclude>
            <exclude>**/ScopesTest*</exclude>
            <exclude>**/TypeConversionTest*</exclude>
          </excludes>
        </configuration>
      </plugin>
      <!--
       | Add OSGi manifest
      -->
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
      </plugin>
      <!--
       | Remove duplicate jarjar'd LICENSE and NOTICE
      -->
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <excludes>
            <exclude>LICENSE</exclude>
            <exclude>NOTICE</exclude>
          </excludes>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <!--
       | No-AOP profile: repeat the build lifecycle with munged no-AOP source
      -->
      <id>guice.with.no_aop</id>
      <activation>
        <property>
          <name>guice.with.no_aop</name>
          <value>!false</value>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.sonatype.plugins</groupId>
            <artifactId>munge-maven-plugin</artifactId>
            <version>1.0</version>
            <executions>
              <execution>
                <phase>prepare-package</phase>
                <goals>
                  <goal>munge-fork</goal>
                </goals>
                <configuration>
                  <symbols>NO_AOP</symbols>
                  <excludes>
                    **/InterceptorBinding.java,
                    **/InterceptorBindingProcessor.java,
                    **/InterceptorStackCallback.java,
                    **/LineNumbers.java,
                    **/MethodAspect.java,
                    **/ProxyFactory.java,
                    **/BytecodeGenTest.java,
                    **/IntegrationTest.java,
                    **/MethodInterceptionTest.java,
                    **/ProxyFactoryTest.java
                  </excludes>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <!--
           | Package the no-AOP build with its own OSGi manifest and attach using "no_aop" classifier
          -->
          <plugin>
            <artifactId>maven-jar-plugin</artifactId>
            <executions>
              <execution>
                <id>no_aop</id>
                <phase>package</phase>
                <goals>
                  <goal>jar</goal>
                </goals>
                <configuration>
                  <classesDirectory>${project.build.directory}/munged/classes</classesDirectory>
                  <classifier>no_aop</classifier>
                  <archive>
                    <manifestFile>${project.build.directory}/munged/classes/META-INF/MANIFEST.MF</manifestFile>
                  </archive>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <!--
       | JarJar build profile: re-package ASM and CGLIB classes under the Guice namespace
      -->
      <id>guice.with.jarjar</id>
      <activation>
        <property>
          <name>guice.with.jarjar</name>
          <value>!false</value>
        </property>
      </activation>
      <dependencies>
        <!--
         | Mark as optional: embedded by JarJar
        -->
        <dependency>
          <groupId>org.sonatype.sisu.inject</groupId>
          <artifactId>cglib</artifactId>
          <version>${cglib.version}</version>
          <optional>true</optional>
        </dependency>
      </dependencies>
      <build>
        <plugins>
          <plugin>
            <groupId>org.sonatype.plugins</groupId>
            <artifactId>jarjar-maven-plugin</artifactId>
            <configuration>
              <includes>
                <include>*:asm</include>
                <include>*:cglib</include>
              </includes>
            </configuration>
          </plugin>
          <plugin>
            <!--
             | Package the original non-JarJar'd classes so extensions can compile against them
            -->
            <artifactId>maven-jar-plugin</artifactId>
            <executions>
              <execution>
                <id>no_deps</id>
                <phase>package</phase>
                <goals>
                  <goal>jar</goal>
                </goals>
                <configuration>
                  <classesDirectory>${project.build.directory}/original-classes</classesDirectory>
                  <classifier>no_deps</classifier>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

</project>
