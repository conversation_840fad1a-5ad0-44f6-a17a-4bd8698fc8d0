package com.chinasie.orion.service;


import com.chinasie.orion.domain.entity.IdeaForm;
import com.chinasie.orion.domain.dto.IdeaFormDTO;
import com.chinasie.orion.domain.vo.IdeaFormVO;

import java.lang.String;
import java.util.List;
import java.util.Map;

import com.chinasie.orion.domain.vo.SimpleDictVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

/**
 * <p>
 * IdeaForm 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29 22:08:44
 */
public interface IdeaFormService extends OrionBaseService<IdeaForm> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    IdeaFormVO detail(String id) throws Exception;

    /**
     * 新增
     * <p>
     * * @param ideaFormDTO
     */
    IdeaFormVO create(IdeaFormDTO ideaFormDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param ideaFormDTO
     */
    Boolean edit(IdeaFormDTO ideaFormDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<IdeaFormVO> pages(Page<IdeaFormDTO> pageRequest) throws Exception;

    /**
     *  改传递单下面是否有未完成的 意见单
     * @param id
     * @return
     */
    boolean isHaveRunningForm(String id);

    Map<String,IdeaFormVO> getDataToByIdList(List<String> idList) throws  Exception;
    /**
     * 获取编码
     * <p>
     */
    String getIdeaFormNumber() throws Exception;


    /**
     * 相关单据列表
     * <p>
     * * @param ideaFormDTO
     */
    List<IdeaFormVO> correlationList(IdeaFormDTO ideaFormDTO);

    List<SimpleDictVO> getReplySuggest();

    List<SimpleDictVO> getFormTypeDict();

    void closeByImId(String id) throws Exception;
}
