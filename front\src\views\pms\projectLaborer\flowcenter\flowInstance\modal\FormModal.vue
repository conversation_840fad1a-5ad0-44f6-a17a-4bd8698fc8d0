<template>
  <BasicModal
    v-bind="$attrs"
    title="确认提示"
    @register="registerModal"
  >
    <BasicForm
      :show-action-button-group="false"
      @register="register"
    />
    <template #footer>
      <a-button @click="closeModal">
        取消
      </a-button>
      <a-button
        type="primary"
        @click="validateForm"
      >
        确定
      </a-button>
    </template>
  </BasicModal>
</template>
<script lang="ts">
import { defineComponent, toRefs, reactive } from 'vue';
import {
  BasicTable, Layout, useActionsRecord, OrionTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer, BasicForm, useForm,
} from 'lyra-component-vue3';
// import { BasicModal, useModalInner } from '/@/components/Modal';
import { Descriptions, Tabs } from 'ant-design-vue';
// import { BasicForm, useForm } from '/@/components/Form/index';

export default defineComponent({
  components: {
    BasicModal,
    aDescriptions: Descriptions,
    aTabs: Tabs,
    BasicForm,
  },
  props: {},
  setup(_, { emit }) {
    // 注册一个表单
    const state: any = reactive({
      type: '',
      formState: {
        labelWidth: 120,
        schemas: [
          {
            field: 'name',
            component: 'Input',
            label: '',
            colProps: {
              span: 20,
            },
            required: true,
          },
        ],
        actionColOptions: {
          span: 24,
        },
      },
    });
    const [register, { validateFields }] = useForm(state.formState);

    // 弹窗内部的注册函数,可以在内部自己关闭
    const [registerModal, { closeModal }] = useModalInner((title) => {
      state.formState.schemas[0].label = `${title}原因`;
      state.type = title;
    });
    /*
     * 验证增加和编辑时候的表单
     * */
    async function validateForm() {
      const res = await validateFields();
      emit('confirm', {
        type: state.type,
        name: res.name,
      });
      closeModal();
    }

    return {
      registerModal,
      closeModal,
      register,
      validateForm,
      ...toRefs(state),
    };
  },
});
</script>
