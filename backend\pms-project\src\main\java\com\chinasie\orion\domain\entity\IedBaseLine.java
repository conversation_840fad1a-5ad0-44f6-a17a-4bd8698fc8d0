package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * IedBaseLine Entity对象
 *
 * <AUTHOR>
 * @since 2024-01-30 13:47:05
 */
@TableName(value = "pms_ied_base_line")
@ApiModel(value = "IedBaseLineEntity对象", description = "ied基线")
@Data
public class IedBaseLine extends DeliverGoals implements Serializable{

//    /**
//     * 计划提交时间
//     */
//    @ApiModelProperty(value = "计划提交时间")
//    @TableField(value = "plan_submit_time")
//    private Date planSubmitTime;
//
//    /**
//     * 编写人
//     */
//    @ApiModelProperty(value = "编写人")
//    @TableField(value = "writer")
//    private String writer;
//
//    /**
//     * 责任人
//     */
//    @ApiModelProperty(value = "责任人")
//    @TableField(value = "res_person")
//    private String resPerson;
//
//    /**
//     * 责任部门
//     */
//    @ApiModelProperty(value = "责任部门")
//    @TableField(value = "res_dept")
//    private String resDept;
//
//    /**
//     * 类型
//     */
//    @ApiModelProperty(value = "类型")
//    @TableField(value = "type")
//    private String type;
//
//    /**
//     * 文件状态
//     */
//    @ApiModelProperty(value = "文件状态")
//    @TableField(value = "file_status")
//    private String fileStatus;
//
//    /**
//     * 版本
//     */
//    @ApiModelProperty(value = "版本")
//    @TableField(value = "rev_id")
//    private String revId;
//
//    /**
//     * 项目id
//     */
//    @ApiModelProperty(value = "项目id")
//    @TableField(value = "project_id")
//    private String projectId;

    /**
     * 原ied主键
     */
    @ApiModelProperty(value = "原ied主键")
    @TableField(value = "old_id")
    private String oldId;

    /**
     * 基线ID
     */
    @ApiModelProperty(value = "基线ID")
    @TableField(value = "base_line_id")
    private String baseLineId;

    @TableField(value = "exist_deliverable")
    @ApiModelProperty(value = "是否载挂技术文件")
    private String existDeliverable;
}
