package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * IncomePlanData VO对象
 *
 * <AUTHOR>
 * @since 2024-09-29 19:19:47
 */
@ApiModel(value = "IncomePlanDataVO对象", description = "收入计划填报统计数据")
@Data
public class IncomePlanDataTotalVO  implements Serializable {
    @ApiModelProperty(value = "笔数")
    private Integer incomePlanDataTotal;

    @ApiModelProperty(value = "本次收入计划金额")
    private BigDecimal incomePlanDataTotalAmt;
}
