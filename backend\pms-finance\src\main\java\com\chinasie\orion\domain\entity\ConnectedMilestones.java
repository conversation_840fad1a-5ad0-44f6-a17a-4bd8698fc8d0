package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ConnectedMilestones Entity对象
 *
 * <AUTHOR>
 * @since 2024-11-20 16:31:37
 */
@TableName(value = "pmsx_connected_milestones")
@ApiModel(value = "ConnectedMilestonesEntity对象", description = "挂接里程碑")
@Data

public class ConnectedMilestones extends ObjectEntity implements Serializable {

    /**
     * 合同里程碑
     */
    @ApiModelProperty(value = "合同里程碑")
    @TableField(value = "milestone_name")
    private String milestoneName;

    @ApiModelProperty(value = "合同里程碑")
    @TableField(value = "milestone_id")
    private String milestoneId;

    /**
     * 凭证编号
     */
    @ApiModelProperty(value = "凭证编号")
    @TableField(value = "certificate_serial_number")
    private String certificateSerialNumber;

    /**
     * 过账日期
     */
    @ApiModelProperty(value = "过账日期")
    @TableField(value = "posting_date")
    private Date postingDate;

    /**
     * 确认收入金额
     */
    @ApiModelProperty(value = "确认收入金额")
    @TableField(value = "confirm_revenue_amount")
    private BigDecimal confirmRevenueAmount;

    /**
     * 冲销暂估金额
     */
    @ApiModelProperty(value = "冲销暂估金额")
    @TableField(value = "reverse_amount")
    private BigDecimal reverseAmount;

    /**
     * 合同编码(备注)
     */
    @ApiModelProperty(value = "合同编码(备注)")
    @TableField(value = "contract_num_remark")
    private String contractNumRemark;

    /**
     * 里程碑名称(备注)
     */
    @ApiModelProperty(value = "里程碑名称(备注)")
    @TableField(value = "mile_stone_remark")
    private String mileStoneRemark;

    /**
     * 关联合同id
     */
    @ApiModelProperty(value = "关联合同id")
    @TableField(value = "contract_id")
    private String contractId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_number")
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @TableField(value = "contract_name")
    private String contractName;

    /**
     * 挂接状态:0未挂接，1已挂接
     */
    @ApiModelProperty(value = "挂接状态:0未挂接，1已挂接")
    @TableField(value = "hanging_connect_status")
    private Integer hangingConnectStatus;

    /**
     * 甲方单位id
     */
    @ApiModelProperty(value = "甲方单位id")
    @TableField(value = "party_A_dept_id")
    private String partyADeptId;

    /**
     * 收入计划id
     */
    @ApiModelProperty(value = "收入计划id")
    @TableField(value = "income_plan_id")
    private String incomePlanId;

    /**
     * 收入计划编号
     */
    @ApiModelProperty(value = "收入计划编号")
    @TableField(value = "number")
    private String number;

    /**
     * 专业中心
     */
    @ApiModelProperty(value = "专业中心")
    @TableField(value = "expertise_center")
    private String expertiseCenter;

    /**
     * 专业所
     */
    @ApiModelProperty(value = "专业所")
    @TableField(value = "expertise_station")
    private String expertiseStation;

    /**
     * 收入计划月份
     */
    @ApiModelProperty(value = "收入计划月份")
    @TableField(value = "estimate_invoice_date")
    private Date estimateInvoiceDate;

    /**
     * 收入确认类型
     */
    @ApiModelProperty(value = "收入确认类型")
    @TableField(value = "income_confirm_type")
    private String incomeConfirmType;

    /**
     * 开票/收入确认公司
     */
    @ApiModelProperty(value = "开票/收入确认公司")
    @TableField(value = "billing_company")
    private String billingCompany;

    /**
     * 甲方单位名称
     */
    @ApiModelProperty(value = "甲方单位名称")
    @TableField(value = "party_A_dept_id_name")
    private String partyADeptIdName;

    /**
     * 专业中心名称
     */
    @ApiModelProperty(value = "专业中心名称")
    @TableField(value = "expertise_center_name")
    private String expertiseCenterName;

    /**
     * 专业所名称
     */
    @ApiModelProperty(value = "专业所名称")
    @TableField(value = "expertise_station_name")
    private String expertiseStationName;

    /**
     * 开票/收入确认公司名称
     */
    @ApiModelProperty(value = "开票/收入确认公司名称")
    @TableField(value = "billing_company_name")
    private String billingCompanyName;


}
