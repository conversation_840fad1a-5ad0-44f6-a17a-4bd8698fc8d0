<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright (c) 2002-2016, the original author or authors.

    This software is distributable under the BSD license. See the terms of the
    BSD license in the documentation provided with this software.

    http://www.opensource.org/licenses/bsd-license.php

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.jline</groupId>
        <artifactId>jline-parent</artifactId>
        <version>3.9.0</version>
    </parent>

    <artifactId>jline</artifactId>
    <name>JLine Bundle</name>

    <dependencies>
        <dependency>
            <groupId>org.fusesource.jansi</groupId>
            <artifactId>jansi</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.googlecode.juniversalchardet</groupId>
            <artifactId>juniversalchardet</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.apache.sshd</groupId>
            <artifactId>sshd-core</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>process-sources</phase>
                        <goals>
                            <goal>unpack</goal>
                        </goals>
                        <configuration>
                            <artifactItems>
                                <!-- sources -->
                                <artifactItem>
                                    <groupId>org.jline</groupId>
                                    <artifactId>jline-terminal</artifactId>
                                    <classifier>sources</classifier>
                                    <type>jar</type>
                                    <overWrite>false</overWrite>
                                    <outputDirectory>${project.build.directory}/generated-sources</outputDirectory>
                                </artifactItem>
                                <artifactItem>
                                    <groupId>org.jline</groupId>
                                    <artifactId>jline-terminal-jansi</artifactId>
                                    <classifier>sources</classifier>
                                    <type>jar</type>
                                    <overWrite>false</overWrite>
                                    <outputDirectory>${project.build.directory}/generated-sources</outputDirectory>
                                </artifactItem>
                                <artifactItem>
                                    <groupId>org.jline</groupId>
                                    <artifactId>jline-terminal-jna</artifactId>
                                    <classifier>sources</classifier>
                                    <type>jar</type>
                                    <overWrite>false</overWrite>
                                    <outputDirectory>${project.build.directory}/generated-sources</outputDirectory>
                                </artifactItem>
                                <artifactItem>
                                    <groupId>org.jline</groupId>
                                    <artifactId>jline-reader</artifactId>
                                    <classifier>sources</classifier>
                                    <type>jar</type>
                                    <overWrite>false</overWrite>
                                    <outputDirectory>${project.build.directory}/generated-sources</outputDirectory>
                                </artifactItem>
                                <artifactItem>
                                    <groupId>org.jline</groupId>
                                    <artifactId>jline-builtins</artifactId>
                                    <classifier>sources</classifier>
                                    <type>jar</type>
                                    <overWrite>false</overWrite>
                                    <outputDirectory>${project.build.directory}/generated-sources</outputDirectory>
                                </artifactItem>
                                <artifactItem>
                                    <groupId>org.jline</groupId>
                                    <artifactId>jline-remote-ssh</artifactId>
                                    <classifier>sources</classifier>
                                    <type>jar</type>
                                    <overWrite>false</overWrite>
                                    <outputDirectory>${project.build.directory}/generated-sources</outputDirectory>
                                </artifactItem>
                                <artifactItem>
                                    <groupId>org.jline</groupId>
                                    <artifactId>jline-remote-telnet</artifactId>
                                    <classifier>sources</classifier>
                                    <type>jar</type>
                                    <overWrite>false</overWrite>
                                    <outputDirectory>${project.build.directory}/generated-sources</outputDirectory>
                                </artifactItem>
                                <artifactItem>
                                    <groupId>org.jline</groupId>
                                    <artifactId>jline-style</artifactId>
                                    <classifier>sources</classifier>
                                    <type>jar</type>
                                    <overWrite>false</overWrite>
                                    <outputDirectory>${project.build.directory}/generated-sources</outputDirectory>
                                </artifactItem>

                                <!-- resources -->
                                <artifactItem>
                                    <groupId>org.jline</groupId>
                                    <artifactId>jline-terminal</artifactId>
                                    <type>jar</type>
                                    <overWrite>false</overWrite>
                                    <outputDirectory>${project.build.directory}/generated-resources</outputDirectory>
                                    <excludes>**/*.class</excludes>
                                </artifactItem>
                                <artifactItem>
                                    <groupId>org.jline</groupId>
                                    <artifactId>jline-terminal-jansi</artifactId>
                                    <type>jar</type>
                                    <overWrite>false</overWrite>
                                    <outputDirectory>${project.build.directory}/generated-resources</outputDirectory>
                                    <excludes>**/*.class</excludes>
                                </artifactItem>
                                <artifactItem>
                                    <groupId>org.jline</groupId>
                                    <artifactId>jline-terminal-jna</artifactId>
                                    <type>jar</type>
                                    <overWrite>false</overWrite>
                                    <outputDirectory>${project.build.directory}/generated-resources</outputDirectory>
                                    <excludes>**/*.class</excludes>
                                </artifactItem>
                                <artifactItem>
                                    <groupId>org.jline</groupId>
                                    <artifactId>jline-reader</artifactId>
                                    <type>jar</type>
                                    <overWrite>false</overWrite>
                                    <outputDirectory>${project.build.directory}/generated-resources</outputDirectory>
                                    <excludes>**/*.class</excludes>
                                </artifactItem>
                                <artifactItem>
                                    <groupId>org.jline</groupId>
                                    <artifactId>jline-builtins</artifactId>
                                    <type>jar</type>
                                    <overWrite>false</overWrite>
                                    <outputDirectory>${project.build.directory}/generated-resources</outputDirectory>
                                    <excludes>**/*.class</excludes>
                                </artifactItem>
                                <artifactItem>
                                    <groupId>org.jline</groupId>
                                    <artifactId>jline-remote-ssh</artifactId>
                                    <type>jar</type>
                                    <overWrite>false</overWrite>
                                    <outputDirectory>${project.build.directory}/generated-resources</outputDirectory>
                                    <excludes>**/*.class</excludes>
                                </artifactItem>
                                <artifactItem>
                                    <groupId>org.jline</groupId>
                                    <artifactId>jline-remote-telnet</artifactId>
                                    <type>jar</type>
                                    <overWrite>false</overWrite>
                                    <outputDirectory>${project.build.directory}/generated-resources</outputDirectory>
                                    <excludes>**/*.class</excludes>
                                </artifactItem>
                                <artifactItem>
                                    <groupId>org.jline</groupId>
                                    <artifactId>jline-style</artifactId>
                                    <type>jar</type>
                                    <overWrite>false</overWrite>
                                    <outputDirectory>${project.build.directory}/generated-resources</outputDirectory>
                                    <excludes>**/*.class</excludes>
                                </artifactItem>
                            </artifactItems>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>1.12</version>
                <executions>
                    <execution>
                        <id>add-source</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>${project.build.directory}/generated-sources</source>
                            </sources>
                        </configuration>
                    </execution>
                    <execution>
                        <id>add-resource</id>
                        <phase>generate-resources</phase>
                        <goals>
                            <goal>add-resource</goal>
                        </goals>
                        <configuration>
                            <resources>
                                <resource>
                                    <directory>${project.build.directory}/generated-resources</directory>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <showWarnings>true</showWarnings>
                    <fork>true</fork>
                </configuration>
                <executions>
                    <execution>
                        <id>default-compile</id>
                        <configuration>
                            <excludes>
                                <exclude>**/TTop.java</exclude>
                            </excludes>
                            <compilerArgs>
                                <arg>-Xlint:all,-options</arg>
                                <arg>-Werror</arg>
                                <arg>-profile</arg>
                                <arg>compact1</arg>
                            </compilerArgs>
                        </configuration>
                    </execution>
                    <execution>
                        <id>noncompact</id>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <includes>
                                <include>**/TTop.java</include>
                            </includes>
                            <compilerArgs>
                                <arg>-Xlint:all,-options</arg>
                                <arg>-Werror</arg>
                                <arg>-profile</arg>
                                <arg>compact3</arg>
                            </compilerArgs>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <configuration>
                    <instructions>
                        <Export-Package>*;-noimport:=true</Export-Package>
                        <Import-Package>
                            com.sun.jna*,
                            org.apache.sshd*,
                            org.fusesource.jansi;version="[1.12,2)",
                            org.fusesource.jansi.internal;version="[1.6,2)",
                            *
                        </Import-Package>
                    </instructions>
                </configuration>
            </plugin>
       </plugins>
    </build>

</project>
