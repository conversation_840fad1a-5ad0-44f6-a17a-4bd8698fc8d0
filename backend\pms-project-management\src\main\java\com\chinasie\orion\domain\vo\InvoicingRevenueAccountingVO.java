package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;

import java.util.List;
/**
 * InvoicingRevenueAccounting VO对象
 *
 * <AUTHOR>
 * @since 2024-11-19 14:28:31
 */
@ApiModel(value = "InvoicingRevenueAccountingVO对象", description = "开票收入核算信息表")
@Data
public class InvoicingRevenueAccountingVO extends  ObjectVO   implements Serializable{

    /**
     * 收入计划编号
     */
    @ApiModelProperty(value = "收入计划编号")
    private String incomePlanNum;


    /**
     * 里程碑名称
     */
    @ApiModelProperty(value = "里程碑id")
    private String milestoneId;

    /**
     * 里程碑名称
     */
    @ApiModelProperty(value = "里程碑名称")
    private String milestoneName;


    /**
     * 收入确认类型
     */
    @ApiModelProperty(value = "收入确认类型")
    private String incomeVerifyType;

    /**
     * 收入确认类型名称
     */
    @ApiModelProperty(value = "收入确认类型名称")
    private String incomeVerifyTypeName;


    /**
     * 凭证编号
     */
    @ApiModelProperty(value = "凭证编号")
    private String certificateSerialNumber;


    /**
     * 凭证日期
     */
    @ApiModelProperty(value = "凭证日期")
    private Date documentDate;


    /**
     * 不含税金额
     */
    @ApiModelProperty(value = "不含税金额")
    private BigDecimal amtNoTax;


    /**
     * 税额
     */
    @ApiModelProperty(value = "税额")
    private BigDecimal tax;


    /**
     * 含税金额   【税额】+【收入净额】
     */
    @ApiModelProperty(value = "含税金额   【税额】+【收入净额】")
    private BigDecimal amtTax;


    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    private String projectNumber;


    /**
     * 关联合同id
     */
    @ApiModelProperty(value = "关联合同id")
    private String contractId;

    /**
     * 文本
     */
    @ApiModelProperty(value = "文本")
    private String conText;




}
