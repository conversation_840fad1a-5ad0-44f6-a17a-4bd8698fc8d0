package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;
import java.lang.String;
import java.lang.Integer;

import java.util.List;

/**
 * BudgetApplicationFrom VO对象
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:07
 */
@ApiModel(value = "BudgetApplicationFromVO对象", description = "预算申请单")
@Data
public class BudgetApplicationFormVO extends ObjectVO implements Serializable {

    /**
     * 申请标题
     */
    @ApiModelProperty(value = "申请标题")
    private String name;


    /**
     * 申请预算单编码
     */
    @ApiModelProperty(value = "申请预算单编码")
    private String number;


    /**
     * 申请预算金额
     */
    @ApiModelProperty(value = "申请预算金额")
    private BigDecimal budgetMoney;

    @ApiModelProperty(value = "概算总金额")
    private BigDecimal estimateMoney;


    /**
     * 申请预算条目数
     */
    @ApiModelProperty(value = "申请预算条目数")
    private Integer budgetCount;


    /**
     * 立项id
     */
    @ApiModelProperty(value = "立项id")
    private String approvalId;


    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;


    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目名称")
    private String projectNumber;


}
