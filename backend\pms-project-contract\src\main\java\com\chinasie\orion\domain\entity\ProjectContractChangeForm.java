package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ProjectContractChangeForm Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-26 15:37:42
 */
@TableName(value = "pms_project_contract_change_form")
@ApiModel(value = "ProjectContractChangeForm对象", description = "项目合同变更表单")
@Data
public class ProjectContractChangeForm extends ObjectEntity implements Serializable {

    /**
     * 字段编码
     */
    @ApiModelProperty(value = "字段编码")
    @TableField(value = "field_code")
    private String fieldCode;

    /**
     * 字段名称
     */
    @ApiModelProperty(value = "字段名称")
    @TableField(value = "field_name")
    private String fieldName;

    /**
     * 字段类型
     */
    @ApiModelProperty(value = "字段类型")
    @TableField(value = "field_type")
    private String fieldType;

    /**
     * keyValue
     */
    @ApiModelProperty(value = "keyValue")
    @TableField(value = "key_value")
    private String keyValue;

    /**
     * 是否必填
     */
    @ApiModelProperty(value = "是否必填")
    @TableField(value = "is_null")
    private Boolean isNull;

}
