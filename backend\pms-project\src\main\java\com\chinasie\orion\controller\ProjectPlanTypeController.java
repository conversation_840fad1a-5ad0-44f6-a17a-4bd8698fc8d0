package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ProjectPlanTypeDTO;
import com.chinasie.orion.domain.entity.ProjectPlanType;
import com.chinasie.orion.domain.vo.ProjectPlanTypeVO;
import com.chinasie.orion.domain.vo.TypeAndTypeAttrValueVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.operatelog.dict.OperateTypeDict;
import com.chinasie.orion.operatelog.dict.VariableDict;
import com.chinasie.orion.service.ProjectPlanTypeService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * RiskType 前端控制器
 * </p>
 *
 * <AUTHOR> sie
 * @since 2022-10-09
 */
@RestController
@RequestMapping("/projectPlan-type")
@Api(tags = "项目计划类型")
public class ProjectPlanTypeController {

    @Resource
    private ProjectPlanTypeService projectPlanTypeService;

    /**
     * 树
     * @param keyword
     * @return
     * @throws Exception
     */
    @ApiOperation("树")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "keyword", dataType = "String"),
            @ApiImplicitParam(name = "status", dataType = "Integer")
    })
    @GetMapping(value = "/tree")
    @LogRecord(
            success = "【{USER{#logUserId}}】在【项目计划类型-项目计划类型管理】获取项目计划类型树",
            fail = "【{USER{#logUserId}}】在【项目计划类型-项目计划类型管理】获取项目计划类型树，失败原因：「{{#_errorMsg}}」",
            type = "项目计划类型", subType = "树", bizNo = "",extra = OperateTypeDict.GET)
    public ResponseDTO<List<ProjectPlanTypeVO>> tree(@RequestParam(value = "keyword", required = false) String keyword
            , @RequestParam(value = "status", required = false) Integer status) throws Exception {
        return new ResponseDTO<>(projectPlanTypeService.tree(keyword, status));
    }

    /**
     *
     * @param typeDTO
     * @return
     * @throws Exception
     */
    @ApiOperation("添加")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "typeDTO", dataType = "TypeDTO")
    })
    @PostMapping(value = "")
    @LogRecord(
            success = "【{USER{#logUserId}}】在【项目计划类型-项目计划类型管理】添加项目计划类型【{{#typeDTO.name}}】",
            fail = "【{USER{#logUserId}}】在【项目计划类型-项目计划类型管理】添加项目计划类型【{{#typeDTO.name}}】，失败原因：「{{#_errorMsg}}」",
            type = "项目计划类型", subType = "添加", bizNo = "",extra = OperateTypeDict.SAVE)
    public ResponseDTO<String> add(@RequestBody ProjectPlanTypeDTO typeDTO) throws Exception {
        return new ResponseDTO<>(projectPlanTypeService.add(typeDTO));
    }

    /**
     *
     * @param typeDTO
     * @return
     * @throws Exception
     */
    @ApiOperation("编辑")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "typeDTO", dataType = "TypeDTO")
    })
    @PutMapping(value = "")
    @LogRecord(
            success = "【{USER{#logUserId}}】在【项目计划类型-项目计划类型管理】编辑项目计划类型【{FILED_VALUE{#typeDTO.id}}】",
            fail = "【{USER{#logUserId}}】在【项目计划类型-项目计划类型管理】编辑项目计划类型【{FILED_VALUE{#typeDTO.id}}】，失败原因：「{{#_errorMsg}}」",
            type = "项目计划类型", subType = "编辑", bizNo = "{{#typeDTO.id}}",extra = OperateTypeDict.SAVE)
    public ResponseDTO<Boolean> edit(@RequestBody ProjectPlanTypeDTO typeDTO) throws Exception {
        LogRecordContext.putVariable(VariableDict.SERVER_NAME, ProjectPlanTypeService.class);
        return new ResponseDTO<>(projectPlanTypeService.edit(typeDTO));
    }


    /**
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation("详情")
    @GetMapping(value = "/{id}")
    @LogRecord(
            success = "【{USER{#logUserId}}】在【项目计划类型-项目计划类型管理】获取项目计划类型【{FILED_VALUE{#id}}】详情",
            fail = "【{USER{#logUserId}}】在【项目计划类型-项目计划类型管理】获取项目计划类型【{FILED_VALUE{#id}}】详情，失败原因：「{{#_errorMsg}}」",
            type = "项目计划类型", subType = "详情", bizNo = "{{#id}}",extra = OperateTypeDict.GET)
    public ResponseDTO<ProjectPlanTypeVO> detail(@PathVariable("id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        LogRecordContext.putVariable(VariableDict.SERVER_NAME, ProjectPlanTypeService.class);
        return new ResponseDTO<>(projectPlanTypeService.detail(id,pageCode));
    }


    /**
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation("禁用")
    @PutMapping(value = "/ban/{id}")
    @LogRecord(
            success = "【{USER{#logUserId}}】在【项目计划类型-项目计划类型管理】禁用项目计划类型【{FILED_VALUE{#id}}】详情",
            fail = "【{USER{#logUserId}}】在【项目计划类型-项目计划类型管理】禁用项目计划类型【{FILED_VALUE{#id}}】详情，失败原因：「{{#_errorMsg}}」",
            type = "项目计划类型", subType = "禁用", bizNo = "{{#id}}",extra = OperateTypeDict.UPDATE)
    public ResponseDTO<Boolean> ban(@PathVariable("id") String id) throws Exception {
        LogRecordContext.putVariable(VariableDict.SERVER_NAME, ProjectPlanTypeService.class);
        return new ResponseDTO<>(projectPlanTypeService.ban(id));
    }

    /**
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation("启用")
    @PutMapping(value = "/use/{id}")
    @LogRecord(
            success = "【{USER{#logUserId}}】在【项目计划类型-项目计划类型管理】启用项目计划类型【{FILED_VALUE{#id}}】详情",
            fail = "【{USER{#logUserId}}】在【项目计划类型-项目计划类型管理】启用项目计划类型【{FILED_VALUE{#id}}】详情，失败原因：「{{#_errorMsg}}」",
            type = "项目计划类型", subType = "启用", bizNo = "{{#id}}",extra = OperateTypeDict.UPDATE)
    public ResponseDTO<Boolean> use(@PathVariable("id") String id) throws Exception {
        LogRecordContext.putVariable(VariableDict.SERVER_NAME, ProjectPlanTypeService.class);
        return new ResponseDTO<>(projectPlanTypeService.use(id));

    }


    /**
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation("删除")
    @DeleteMapping(value = "/{id}")
    @LogRecord(
            success = "【{USER{#logUserId}}】在【项目计划类型-项目计划类型管理】删除项目计划类型【{FILED_VALUE{#id}}】详情",
            fail = "【{USER{#logUserId}}】在【项目计划类型-项目计划类型管理】删除项目计划类型【{FILED_VALUE{#id}}】详情，失败原因：「{{#_errorMsg}}」",
            type = "项目计划类型", subType = "删除", bizNo = "{{#id}}",extra = OperateTypeDict.GET)
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        LogRecordContext.putVariable(VariableDict.SERVER_NAME, ProjectPlanTypeService.class);
        return new ResponseDTO<>(projectPlanTypeService.remove(id));
    }

    /**
     * 获取类型及属性值
     * @param typeId
     * @param riskId
     * @return
     * @throws Exception
     */
    @ApiOperation("获取类型及属性值")
    @GetMapping(value = "/attribute-value")
    @LogRecord(
            success = "【{USER{#logUserId}}】在【项目计划类型-项目计划类型管理】获取类型【{{#typeName}}】的属性值【{FILED_VALUE{#riskId}}】",
            fail = "【{USER{#logUserId}}】在【项目计划类型-项目计划类型管理】获取类型【{{#typeName}}】的属性值【{FILED_VALUE{#riskId}}】，失败原因：「{{#_errorMsg}}」",
            type = "项目计划类型", subType = "获取类型及属性值", bizNo = "{{#typeId}}",extra = OperateTypeDict.GET)
    public ResponseDTO<TypeAndTypeAttrValueVO> getTypeAndAttributeValues(@RequestParam("typeId") String typeId, @RequestParam("riskId") String riskId) throws Exception {
        ProjectPlanType projectPlanType = projectPlanTypeService.getById(typeId);
        if(ObjectUtils.isEmpty(projectPlanType)){
            LogRecordContext.putVariable("typeName", typeId);
        }else{
            LogRecordContext.putVariable("typeName", projectPlanType.getName());
        }
        return new ResponseDTO<>(projectPlanTypeService.getTypeAndAttributeValues(typeId, riskId));
    }


}
