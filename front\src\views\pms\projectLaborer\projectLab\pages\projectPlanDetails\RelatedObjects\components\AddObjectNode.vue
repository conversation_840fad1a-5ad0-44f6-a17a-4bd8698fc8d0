<template>
  <BasicModal
    v-bind="$attrs"
    title="添加相关对象"
    width="80%"
    :min-height="600"
    wrap-class-name="addChangeObjectNode"
    :footer="null"
    @register="registerModal"
  >
    <div class="addChangeObjectNodeContent">
      <div class="contentList">
        <AInputSearch
          v-model:value="searchTreeValue"
          style="margin-bottom: 8px"
          placeholder="请输入名称或者编号"
          @search="searchChange"
        />

        <div class="treeContent">
          <ATree
            v-model:selectedKeys="selectedTreeKeys"
            :tree-data="treeData"
            class="addChangeObjectNodeTreeContent"
            :field-names="{title:'name',key:'id'}"
            @select="selectNode"
          >
            <template #title="treeNode">
              <div
                class="treeNode"
              >
                <FolderFilled class="folderTitle" />
                <span class="titleSpan">{{ treeNode.name }}</span>
              </div>
            </template>
          </ATree>
        </div>
      </div>
      <div class="contentMiddle">
        <OrionTable
          ref="tableRef"
          :options="tableOptions"
        >
          <template #toolbarRight>
            <div>
              <AInputSearch
                v-model:value="searchTableValue"
                style="margin-bottom: 8px"
                placeholder="请输入名称或者编号"
                @search="searchTableChange"
              />
            </div>
          </template>
          <template #name="{record }">
            <div
              class="tableIndexName flex-te"
            >
              {{ record.name }}
            </div>
          </template>
        </OrionTable>
      </div>
      <div class="contentTight">
        <div class="contentTight_title">
          <span class="listLength">显示已选择（{{ rightData.length }}）</span>
          <span
            class="clearList"
            @click="clearAll"
          >清空</span>
        </div>
        <div class="listContent">
          <template
            v-for="item in rightData"
            :key="item.id"
          >
            <div class="listContent_item">
              <span class="itemStyle flex-te">{{ item.name }}</span>
              <span
                class="removeItem"
                @click="clearItem(item)"
              >移除</span>
            </div>
          </template>
        </div>
        <AButton
          type="primary"
          @click="addRelease"
        >
          确认添加
        </AButton>
      </div>
    </div>
  </BasicModal>
</template>
<script lang="ts">
import {
  defineComponent, onMounted, reactive, toRefs, computed, ref, inject,
} from 'vue';
import {
  BasicModal, useModalInner,
  OrionTable,
} from 'lyra-component-vue3';
import {
  Tree, Input, Button, message, Select,
} from 'ant-design-vue';
import { stampDate } from '/@/utils/dateUtil';
import Api from '/@/api';
export default defineComponent({
  name: 'AddChangeObjectNode',
  components: {
    BasicModal,
    OrionTable,
    ATree: Tree,
    AInputSearch: Input.Search,
    AButton: Button,
    ASelect: Select,
  },
  props: {
    modalTitle: {
      type: String,
      default: '',
    },
    relatedType: {
      type: String,
      default: 'page',
    },
  },
  emits: ['update', 'initData'],
  setup(props, { emit }) {
    const formData: any = inject('formData', {});
    const state :any = reactive({
      searchTreeValue: '',
      searchTableValue: '',
      treeData: [],
      selectedTreeKeys: [],
      selectedRowKeys: [],
      rightData: [],

    });
    const [registerModal, { closeModal, setModalProps }] = useModalInner(async ({ type, data, options }) => {
      state.rightData = [];
      state.selectedTreeKeys = [state.treeData[0].id];
      state.selectedRowKeys = [];
      state.params = {
        query: {
          status: 1,
        },
      };
      if (props.modalTitle) {
        setModalProps({ title: props.modalTitle });
      }
    });
    const tableRef = ref();
    const tableOptions = ref({
      deleteToolButton: 'add|enable|disable|delete',
      rowSelection: {
        selectedRowKeys: computed(() => state.selectedRowKeys),
        onSelect: (record, selected, selectedRows, nativeEvent) => {
          if (selected) {
            state.selectedRowKeys.push(record.id);
            state.rightData.push(record);
          } else {
            state.rightData.splice(state.selectedRowKeys.indexOf(record.id), 1);
            state.selectedRowKeys.splice(state.selectedRowKeys.indexOf(record.id), 1);
          }
        },
        onSelectAll: (selected, selectedRows, changeRows) => {
          let tableData = tableRef.value.getDataSource();
          if (selected) {
            tableData.forEach((item) => {
              if (state.selectedRowKeys.indexOf(item.id) < 0) {
                state.selectedRowKeys.push(item.id);
                state.rightData.push(item);
              }
            });
          } else {
            tableData.forEach((item) => {
              state.rightData.splice(state.selectedRowKeys.indexOf(item.id), 1);
              state.selectedRowKeys.splice(state.selectedRowKeys.indexOf(item.id), 1);
            });
            // let tableData=tabl
          }
        },
      },
      showSmallSearch: false,
      // dataSource: [],
      api: computed(() => getTableList(state.selectedTreeKeys[0])),
      columns: [
        {
          title: '名称',
          dataIndex: 'name',
          slots: { customRender: 'name' },
          align: 'left',
          minWidth: 100,
        },
        {
          title: '版本',
          dataIndex: 'revId',
          align: 'left',
          width: 100,
        },
        {
          title: '所属类型',
          dataIndex: 'typeName',
          align: 'left',
          width: 100,
        },
        {
          title: '状态',
          dataIndex: 'status',
          align: 'left',
          width: 150,
          slots: { customRender: 'status' },
        },
        {
          title: '修改人',
          dataIndex: 'modifyName',
          align: 'left',
          width: 100,
        },
        {
          title: '修改日期',
          dataIndex: 'modifyTime',
          type: 'dateTime',
          align: 'left',
          customRender: ({
            text, record, index, column,
          }) => (record.modifyTime.length > 0 ? stampDate(record.modifyTime, 'yyyy-MM-dd HH:mm:ss') : ''),
        },
      ],
    });
    function getTableList(className) {
      return (tableParams) => new Api('/union').fetch({ ...tableParams }, `common/model/${className}/pages`, 'POST');
    }

    const selectNode = (id) => {
      tableRef.value.setPagination({ current: 1 });
      state.searchTableValue = '';
      state.params = {
        query: {
          status: 1,
        },
      };
    };
    const searchTableChange = () => {
      state.params = {
        query: {
          name: state.searchTableValue,
          status: 1,
        },
      };
    };
    const searchChange = () => {
      getLeftData();
    };
    onMounted(() => {
      getLeftData();
    });
    function getLeftData() {
      let params = {
        name: state.searchTreeValue,
      };
      new Api('/union').fetch(params, `common/gettypeunionmodel/${props.relatedType}`, 'GET').then((res) => {
        let treeData = [];
        res.forEach((item) => {
          treeData.push({
            name: item.name,
            id: item.clazz,
          });
        });
        state.treeData = treeData;
        state.selectedTreeKeys = treeData.length > 0 ? [state.treeData[0].id] : [];
        emit('initData', state.treeData);
      });
    }
    const clearItem = (item) => {
      state.selectedRowKeys.splice(state.selectedRowKeys.indexOf(item.id), 1);
      let itemIndex = 0;
      for (let i = 0; i < state.rightData.length; i++) {
        if (state.rightData[i].id === item.id) {
          itemIndex = i;
          break;
        }
      }
      state.rightData.splice(itemIndex, 1);
    };
    const clearAll = () => {
      state.selectedRowKeys = [];
      state.rightData = [];
    };
    const addRelease = () => {
      if (state.rightData.length === 0) {
        message.warning('请选择数据');
        return;
      }
      emit('update', state.rightData);
      closeModal();
    };
    return {
      ...toRefs(state),
      registerModal,
      tableOptions,
      selectNode,
      searchChange,
      searchTableChange,
      clearItem,
      clearAll,
      addRelease,
      tableRef,
    };
  },
});

</script>
<style lang="less">
.addChangeObjectNode{
  .scrollbar{
    padding: 0px !important;
    padding-bottom: 10px !important;
  }
  .scrollbar__wrap{
    margin-bottom: 0px !important;
  }
  .ant-modal-header{
    border-color:#dddddd
  }
  .addChangeObjectNodeContent{
    display: flex;
    height: 100%;
    border-bottom: 1px solid #dddddd;
    margin-bottom: 10px;
  }
  .contentList{
    width: 250px;
    border-right: 1px solid #dddddd;
    padding: 10px;
    flex: 1;
    overflow: auto;
  }
  .contentMiddle{
    width:calc(~'100% - 450px');
    padding:10px;
    position: relative;
    height: 400px;
  }
  .contentTight{
    width: 200px;
    border-left: 1px solid #dddddd;
    padding:10px;
    display: flex;
    flex-direction: column;
    .contentTight_title{
      padding-bottom: 10px;
      .listLength{
        color: #333333;
        display: inline-block;
        width: 150px;
      }
      .clearList{
        color: ~`getPrefixVar('primary-color')`;
        cursor: pointer;
      }
    }
    .listContent{
      flex: 1;
      overflow: auto;
      .listContent_item{
        position: relative;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        .itemStyle{
          color: #8A92A5;
          height: 30px;
          line-height: 30px;
          display: inline-block;
          width: 120px;
          vertical-align: middle;
        }
        .removeItem{
          display: none;
          cursor: pointer;
          color: ~`getPrefixVar('primary-color')`;
          height: 30px;
          line-height: 30px;
          font-size: 12px;
          padding: 0px 10px;
          border-radius: 5px;
          &:hover{
            background: #edf1fc;
          }
        }
        &:hover{
          .itemStyle{
            color: ~`getPrefixVar('primary-color')`;
          }
          .removeItem{
            display: inline-block;
          }
        }
      }

    }
  }

}
</style>
