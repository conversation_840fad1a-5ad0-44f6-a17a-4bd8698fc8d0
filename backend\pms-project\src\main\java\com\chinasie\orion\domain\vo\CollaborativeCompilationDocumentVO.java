package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.util.TreeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;

import java.util.List;

/**
 * CollaborativeCompilationDocument VO对象
 *
 * <AUTHOR>
 * @since 2024-06-04 17:10:25
 */
@ApiModel(value = "CollaborativeCompilationDocumentVO对象", description = "协同编制文档分解")
@Data
public class CollaborativeCompilationDocumentVO extends ObjectVO implements TreeUtils.TreeNode<String, CollaborativeCompilationDocumentVO> {

    /**
     * 父级Id
     */
    @ApiModelProperty(value = "父级Id")
    private String parentId;


    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    private String taskId;

    @ApiModelProperty(value = "任务名称")
    private String taskName;


    /**
     * 条目标题
     */
    @ApiModelProperty(value = "条目标题")
    private String name;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 立项Id
     */
    @ApiModelProperty(value = "立项Id")
    private String approvalId;


    /**
     * 内容ID
     */
    @ApiModelProperty(value = "内容ID")
    private String content;


    /**
     * 编写要求
     */
    @ApiModelProperty(value = "编写要求")
    private String writeRequire;


    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "子项")
    private List<CollaborativeCompilationDocumentVO> children;

}
