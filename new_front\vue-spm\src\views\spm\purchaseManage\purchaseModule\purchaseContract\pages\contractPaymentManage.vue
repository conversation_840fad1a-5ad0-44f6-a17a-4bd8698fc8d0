<script setup lang="ts">

import {
  BasicTableAction, IOrionTableActionItem, Layout, OrionTable,
} from 'lyra-component-vue3';
import { onMounted, reactive } from 'vue';
import Api from '/src/api';
import { Modal } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { isBoolean } from 'lodash-es';
import MoneyRow from '../../components/MoneyRow.vue';
import { parseBooleanToRender, parsePriceByNumber } from '../../utils';

const router = useRouter();
const rowMoney = reactive([
  {
    key: 'total',
    title: '合同数量',
    value: '',
    suffix: '个',
  },
  {
    key: 'allMoney',
    title: '合同总金额',
    value: '',
    suffix: '元',
  },
  {
    key: 'saveMoney',
    title: '较立项节约总金额',
    value: '',
    suffix: '元',
  },
]);
const tableOptions = {
  rowSelection: {
    onChange(keys: string[], rows: Record<string, any>[]) {
    },
  },
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  smallSearchField: undefined,
  columns: [
    {
      title: '操作',
      dataIndex: 'actions',
      width: 160,
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
    {
      title: '采购订单号',
      dataIndex: 'procurementOrderNumber',
      width: 160,
    },
    {
      title: '合同编号',
      dataIndex: 'contractNumber',
      width: 160,
    },
    {
      title: '合同名称',
      dataIndex: 'name',
      width: 480,
      minWidth: 480,
    },
    {
      title: '合同执行状态',
      dataIndex: 'statusName',
      width: 130,
    },
    {
      title: '框架合同剩余金额',
      dataIndex: 'freamResidueAmount',
      width: 160,
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '合同类型',
      dataIndex: 'type',
      width: 100,
    },
    {
      title: '采购立项金额',
      dataIndex: 'procurementAmount',
      width: 100,
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '最终价格（原币）',
      dataIndex: 'finalPrice',
      width: 120,
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '供应商',
      dataIndex: 'supplierName',
      width: 220,
    },
    {
      title: '工厂',
      dataIndex: 'factoryName',
      width: 200,
    },
    {
      title: '商务负责人',
      dataIndex: 'businessRspUser',
      width: 160,
    },
    {
      title: '变更金额',
      dataIndex: 'changeMoney',
      width: 100,
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '变更比例',
      dataIndex: 'changePercent',
      width: 100,
    },
    {
      title: '支付金额',
      dataIndex: 'payMoney',
      width: 100,
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '是否合同终止',
      dataIndex: 'isContractTerminate',
      width: 100,
      customRender({ text }) {
        return parseBooleanToRender(text);
      },
    },
    {
      title: '索赔金额',
      dataIndex: 'claimAmount',
      width: 100,
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '索赔比例',
      dataIndex: 'claimPercent',
      width: 100,
    },
    {
      title: '终止金额',
      dataIndex: 'terminateAmount',
      width: 100,
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '终止比例',
      dataIndex: 'terminatePercent',
      width: 100,
    },
    {
      title: '框架开始时间',
      dataIndex: 'freamBeginTime',
      width: 150,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '框架结束时间',
      dataIndex: 'freamEndTime',
      width: 150,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '框架合同已使用金额',
      dataIndex: 'freamUsedAmount',
      width: 180,
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '是否框架有效期内',
      dataIndex: 'isFreamPeriod',
      width: 180,
      customRender({ text }) {
        return parseBooleanToRender(text);
      },
    },
    {
      title: '支付比例',
      dataIndex: 'payPercent',
      width: 100,
    },
    {
      title: '采购方式',
      dataIndex: 'procurementWay',
      width: 100,
    },
    {
      title: '采购周期',
      dataIndex: 'procurementCycle',
      width: 100,
    },
    {
      title: '节约金额',
      dataIndex: 'amountSaved',
      width: 100,
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
  ],
  api: (params: Record<string, any>) => {
    params.searchConditions = [];
    return new Api('/spm/contractInfo').fetch({
      ...params,
    }, 'page', 'POST');
  },
};
const actions: IOrionTableActionItem[] = [
  {
    text: '编辑',
    event: 'edit',
  },
  {
    text: '查看',
    event: 'view',
  },
  {
    text: '删除',
    event: 'delete',
  },
];

const getMoney = async () => {
  try {
    const res = await new Api('/spm/contractInfo/getNumMoney').fetch({
      searchConditions: [],
    }, '', 'POST');
    rowMoney.forEach((item) => {
      item.value = res[item.key];
    });
  } catch (e) {
  }
};

function actionClick(actionItem: { event: string }, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'edit':
      // openFormDrawer(PurchaseRequestEdit, record, updateTable);
      break;
    case 'view':
      router.push({
        name: 'PurchaseContractInfo',
        params: {
          id: record.id,
        },
        query: {
          source: 'contractPaymentManage',
        },
      });
      break;
    case 'delete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除这条数据吗？',
        onOk: () => deleteApi([record.id]),
      });
      break;
  }
}
function updateTable() {
  tableRef.value?.reload();
}

function deleteApi(ids: string[]) {
  return new Promise((resolve) => {
    new Api('/spm/contractInfo').fetch(ids, 'remove', 'DELETE').then(() => {
      updateTable();
    }).finally(() => {
      resolve('');
    });
  });
}
onMounted(async () => {
  // PurchaseContractInfo
  await getMoney();
});
</script>

<template>
  <Layout :options="{ body: { scroll: true } }">
    <MoneyRow :data="rowMoney" />
    <OrionTable
      ref="tableRef"
      class="scroll-table"
      :options="tableOptions"
    >
      <template #actions="{record}">
        <BasicTableAction
          :actions="actions"
          :record="record"
          @actionClick="actionClick($event,record)"
        />
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">
.scroll-table {
  ::-webkit-scrollbar {
    height: 15px !important;
    width: 15px !important;
  }
  ::-webkit-scrollbar-thumb {
    border-radius:0;
  }
}
</style>