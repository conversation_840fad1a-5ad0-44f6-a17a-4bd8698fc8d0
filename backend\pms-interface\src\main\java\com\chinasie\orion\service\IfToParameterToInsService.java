package com.chinasie.orion.service;
import com.chinasie.orion.domain.dto.ImOrIfToParamBatchDTO;
import com.chinasie.orion.domain.dto.pdm.ParameterPoolInsDTO;
import com.chinasie.orion.domain.entity.IfToParameterToIns;
import com.chinasie.orion.domain.dto.IfToParameterToInsDTO;
import com.chinasie.orion.domain.vo.IfToParameterToInsVO;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

/**
 * <p>
 * IfToParameterToIns 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-31 13:52:16
 */
public interface IfToParameterToInsService extends OrionBaseService<IfToParameterToIns> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    IfToParameterToInsVO detail(String id) throws Exception;

    /**
     * 新增
     * <p>
     * * @param ifToParameterToInsDTO
     */
    IfToParameterToInsVO create(IfToParameterToInsDTO ifToParameterToInsDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param ifToParameterToInsDTO
     */
    Boolean edit(IfToParameterToInsDTO ifToParameterToInsDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<IfToParameterToInsVO> pages(Page<IfToParameterToInsDTO> pageRequest) throws Exception;

    /**
     *  批量新增参数列表
     * @param imToParamBatchDTO
     * @return
     */
    boolean batchCreateOrUpdate(ImOrIfToParamBatchDTO imToParamBatchDTO);

    /**
     *  获取详情列表
     * @param ifToParameterToInsDTO
     * @return
     */
    List<IfToParameterToInsVO> detailList(IfToParameterToInsDTO ifToParameterToInsDTO) throws Exception;

    /**
     *
     * @param sourceId
     * @param targetId
     * @return
     * @throws Exception
     */
    boolean copySourceDataToTarget(String sourceId,String targetId) throws  Exception;

    /**
     *  填值
     * @param parameterPoolInsDTO
     * @return
     */
    Boolean addValue(ParameterPoolInsDTO parameterPoolInsDTO) throws Exception;
}
