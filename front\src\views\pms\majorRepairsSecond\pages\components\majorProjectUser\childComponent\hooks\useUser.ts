import {
  computed, h, inject, onMounted, Ref, ref, unref,
} from 'vue';
import {
  ExpandIcon, getDictByNumber, openBasicSelectModal,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
import Api from '/@/api';
import { message } from 'ant-design-vue';
import { handleUpdate, openDataPersonalForm } from './utils';
import MemoClickCellEdit from '/@/views/pms/majorRepairsSecond/pages/components/MemoClickCellEdit.vue';
import { getUserProps } from '/@/views/pms/majorRepairsSecond/pages/components/hooks/UserBasicSelect';
import router from '/@/router';
import PersonalForm from '../PersonalForm.vue';

const API_PATH_PREPARE = 'personManageNew/prepare';
const API_PATH_EXECUTE = 'personManageNew/execute';

const options = [
  {
    label: '是',
    value: true,
  },
  {
    label: '否',
    value: false,
  },
];

interface TableHooksOption {
  radioType: Ref<'preparation' | 'operation'>
  keyword: Ref<string>,
  allFlagKeys: Ref<boolean>,
}

interface TableMethods {
  isDetails: boolean
  updateTable: (isUpdateKey?: boolean) => void
}

interface PrepareParams {
  actInDate?: string;
  isBasePermanent?: boolean;
  newcomer?: boolean;
  newcomerMatchPerson?: string;
  newcomerMatchPersonCode?: string;
  personId?: string;
  planInDate?: string;
  planOutDate?: string;
}

interface ExecuteParams {
  actOutDate?: string;
  isAgainIn?: boolean;
  isBasePermanent?: boolean;
  isFinishOutHandover?: boolean;
  leaveReason?: string;
  personId?: string;
}

interface DictItem {
  name: string;
  number: string;
}

// 大修准备编辑参数
function getParams(data: PrepareParams) {
  return {
    actInDate: data?.actInDate,
    isBasePermanent: data?.isBasePermanent,
    newcomer: data?.newcomer,
    newcomerMatchPerson: data?.newcomerMatchPerson,
    newcomerMatchPersonCode: data?.newcomerMatchPersonCode,
    personId: data?.personId,
    planInDate: data?.planInDate,
    planOutDate: data?.planOutDate,
  };
}

// 大修实施编辑参数
function getExecuteParams(data: ExecuteParams) {
  return {
    actOutDate: data?.actOutDate,
    isAgainIn: data?.isAgainIn,
    isBasePermanent: data?.isBasePermanent,
    isFinishOutHandover: data?.isFinishOutHandover,
    leaveReason: data?.leaveReason,
    personId: data?.personId,
  };
}

function useUser(option: TableHooksOption, tableMethods: TableMethods) {
  const expandedRowKeys = ref<string[]>([]);
  const detailsData: Record<string, any> = inject('detailsData', {});
  const data = ref([]);
  const loadingRef = ref<boolean>(false);
  const recordNodeIds = ref<string[]>([]);

  onMounted(() => {
    dataApi();
  });

  const API_PATHS = {
    preparation: '/pms/personManageNew/personPrepareTree',
    implement: '/pms/personManageNew/personExecuteTree',
  };

  async function dataApi(recordNodeIds?: string[]) {
    const apiPath = option.radioType.value === 'preparation' ? API_PATHS.preparation : API_PATHS.implement;
    loadingRef.value = true;

    try {
      const keyword = unref(option.keyword);
      const repairOrgId = tableMethods.isDetails ? detailsData.id : undefined;
      const repairRound = detailsData.repairRound;

      const result = await new Api(apiPath).fetch({
        keyword,
        repairOrgId,
        repairRound,
      }, '', 'POST') as { parenIdList?: string[], oneOrTwoIdList?: string[], treeNodeVOList?: any[] };

      expandedRowKeys.value = keyword
        ? result?.parenIdList
        : recordNodeIds || (option.allFlagKeys?.value ? result?.parenIdList : result?.oneOrTwoIdList);

      data.value = result?.treeNodeVOList || [];
    } catch (error) {
      message.error('API request failed:', error);
      // 可以根据需求进一步处理错误，例如显示错误信息给用户
    } finally {
      loadingRef.value = false;
    }
  }

  const renderEditableCell = ({
    component,
    type,
    data,
    text,
    record,
    field,
    apiPath,
    disabledDate,
    componentProps = {},
    formatText = (t: string) => t,
    getValue = (v: any) => v,
    detailsData,
    beforeEvent,
  }: {
        component: string;
        type: string;
        data: any;
        text: string;
        record: any;
        field: string;
        apiPath: string;
        disabledDate?: boolean;
        componentProps?: any;
        formatText?: (text: string) => string;
        getValue?: (value: any) => any;
        detailsData?: any;
        beforeEvent?: Function
    }) => {
    const rowData = record?.data;
    const isEdit = !((field === 'newcomer' && rowData?.status === 1) || (field === 'newcomerMatchPerson' && rowData?.status === 1)) && record?.roleList?.includes('WRITE');
    if (!rowData) return null;
    return h('div', null, {
      default: () => [
        h(MemoClickCellEdit, {
          component,
          record: rowData,
          text: formatText(text),
          componentValue: text,
          componentProps,
          addClass: true,
          disabledDate,
          editFlag: isEdit,
          beforeEvent,
          async onSubmit(value, resolve) {
            const setParams = {
              ...rowData,
              [field]: getValue(value),
            };
            const params = type === 'isReady'
              ? getParams(setParams)
              : getExecuteParams(setParams);
            const sValue = getValue(value);
            await handleUpdate(
              data,
              record,
              params,
              field,
              sValue,
              resolve,
              apiPath,
              value,
              detailsData,
              tableMethods,
            );
          },
        }),
      ],
    });
  };

  const renderDatePicker = ({
    type,
    data,
    text,
    record,
    field,
    apiPath,
    disabledDate,
    detailsData,
    beforeEvent,
  }: {
        type: string;
        data: any;
        text: string;
        record: any;
        field: string;
        apiPath: string;
        disabledDate?: boolean;
        detailsData?: any;
        beforeEvent?: Function
    }) =>
    renderEditableCell({
      component: 'DatePicker',
      type,
      data,
      text,
      record,
      field,
      apiPath,
      disabledDate,
      componentProps: {
        valueFormat: 'YYYY-MM-DD',
      },
      formatText: (t: string) => (t ? dayjs(t).format('YYYY-MM-DD') : ''),
      getValue: (v: any) => v?.[1],
      detailsData,
      beforeEvent,
    });

  const renderSelect = ({
    type,
    data,
    text,
    record,
    field,
    apiPath,
    apiDict,
    detailsData,
    beforeEvent,
  }: {
        type: string;
        data: any;
        text: string;
        record: any;
        field: string;
        apiPath: string;
        apiDict?: string;
        detailsData?: any;
        beforeEvent?: Function
    }) =>
    renderEditableCell({
      component: 'Select',
      type,
      data,
      text,
      record,
      field,
      apiPath,
      beforeEvent,
      componentProps: {
        api: () => (apiDict ? getDictByNumber(apiDict).then((res) => {
          let arr = [];
          if (res) {
            // @ts-ignore
            arr = res.map((item: DictItem) => ({
              label: item.name,
              value: item.number,
            }));
          }
          return arr;
        }) : options),
      },
      // @ts-ignore
      formatText: (t: string) => {
        const booleanMap = {
          true: '是',
          false: '否',
        };
        return field === 'isBasePermanent'
          ? booleanMap[t] || ''
          : field !== 'leaveReason'
            ? booleanMap[t] || ''
            : t;
      },
      getValue: (option: any) => option.value,
      detailsData,
    });

  const renderInput = ({
    type,
    data,
    text,
    record,
    field,
    apiPath,
    detailsData,
  }: {
    type: string;
    data: any;
    text: string;
    record: any;
    field: string;
    apiPath: string;
    detailsData?: any
  }) =>
    renderEditableCell({
      component: 'Input',
      type,
      data,
      text,
      record,
      field,
      apiPath,
      getValue: (v: any) => v,
      detailsData,
    });

  const expandIcon = ({
    record, expandedRowKeys, onUpdate, rowId,
  }) =>
    h(ExpandIcon, {
      record,
      expandedRowKeys,
      onUpdate,
      rowId,
    });

  function findParentIds(data: any, targetId: number, path: number[] = []): number[] | null {
    for (const node of data) {
      if (node.data.id === targetId) {
        return [...path, node.data.parentId];
      }
      if (node.children.length > 0 && !path.includes(node.data.id)) {
        const result = findParentIds(node.children, targetId, [...path, node.data.id]);
        if (result) {
          return result;
        }
      }
    }
    return null;
  }

  const addUserIcon = ({ record, detailsData, tableMethods }) =>
    h(
      'div',
      {
        class: 'blue-icon',
        onClick: () => {
          openBasicSelectModal({
            ...getUserProps(record?.data?.code),
            onOk(records: any[]) {
              if (records?.length > 0) {
                return new Promise((resolve, reject) => {
                  new Api('/pms/personManageNew/add/batchNew')
                    .fetch(
                      {
                        baseCode: detailsData?.baseCode,
                        codeList: records.map((item) => item?.userCode),
                        repairOrgId: record?.data?.id,
                        repairRound: detailsData?.repairRound,
                      },
                      '',
                      'POST',
                    )
                    .then(() => {
                      resolve('');
                      const recordId = record?.data?.id;
                      const parentIds = findParentIds(data.value, recordId);
                      // 使用 Set 去重
                      const uniqueParentIds = Array.from(new Set([...parentIds, recordId]));
                      return dataApi(uniqueParentIds);
                    })
                    .catch((e) => {
                      reject(e);
                    });
                });
              }
              return new Promise((resolve, reject) => {
                message.warning('请选择参修人员');
                reject('');
              });
            },
          } as any);
        },
      },
      [h('span', { class: 'clamp-line-2' }, '人')],
    );

  const renderText = (text: string) => h('span', { class: 'clamp-line-2' }, text);
  const renderNumber = ({
    text, record, type, isClick = false,
  }: { text: string; record: any; type?: string, isClick?: boolean }) =>
    h('span', {
      class: isClick ? 'clamp-hover' : 'clamp-none',
      onClick: () => {
        if (!isClick || type === 'planInRate' || type === 'actualInRate') return;
        openDataPersonalForm(PersonalForm, {
          record,
          detailsData,
          type,
        });
      },
    }, Number(text) > 0 && (type === 'planInRate' || type === 'actualInRate') ? `${text}%` : text || '');

  const renderPerson = ({ record }) => {
    const style = {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'flex-start',
    };
    return h('div', {
      class: 'flex flex-ac flex-active',
      style,
    }, [h('span', { class: 'title-line-1' }, record?.data?.userName || ''), h('span', { class: 'title-line-2' }, `员工号:${record?.data?.code || ''}`)]);
  };

  const renderStatus = ({ record }) => {
    const colorBg = record?.data?.status === 0 ? 'warn-s-major' : (record?.data?.status === 1 ? 'green-s-major' : 'red-s-major');
    const name = record?.data?.status === 0 ? '未入场' : (record?.data?.status === 1 ? '已入场' : '已离场');
    return h('div', { class: 'common-center-major' }, [h('div', { class: ['common-s-major', colorBg] }, [h('span', { class: 'status-show' }, name)])]);
  };

  const columns = computed<any[]>(() => [
    {
      title: '大修组织架构',
      dataIndex: ['data', 'name'],
      width: 300,
      fixed: 'left',
      customRender({ text, record }) {
        return h('div', { class: 'flex flex-ac' }, [
          expandIcon({
            record,
            expandedRowKeys: expandedRowKeys.value,
            onUpdate: (newKeys) => (expandedRowKeys.value = newKeys),
            rowId: record?.data?.id,
          }),
          renderText(text),
          ...(record?.roleList.includes('WRITE') && record?.data?.nodeType === 'specialtyTeam'
            ? [
              addUserIcon({
                record,
                detailsData,
                tableMethods,
              }),
            ]
            : []),
        ]);
      },
    },
  ].concat(option.radioType.value === 'preparation'
    ? [
      {
        title: '责任人',
        dataIndex: ['data', 'rspUserName'],
        width: 100,
        // @ts-ignore
        customHeaderCell() {
          return {
            class: 'required-cell',
          };
        },
      },
      {
        title: '人数',
        width: 100,
        dataIndex: [
          'data',
          'data',
          'personCount',
        ],
        customRender: ({ text, record }) => renderNumber({
          text,
          record,
          type: 'personCount',
          isClick: Number(text) > 0,
        }),
      },
      {
        title: '新人数',
        width: 100,
        dataIndex: [
          'data',
          'data',
          'newPersonCount',
        ],
        customRender: ({ text, record }) => renderNumber({
          text,
          record,
          type: 'newPersonCount',
          isClick: Number(text) > 0,
        }),
      },
      {
        title: '必填项未填数',
        minWidth: 105,
        dataIndex: [
          'data',
          'data',
          'requiredCount',
        ],
        customRender: ({ text, record }) => renderNumber({
          text,
          record,
          type: 'requiredRepairFinishCount',
          isClick: Number(text) > 0,
        }),
      },
      {
        title: '计划入场时间未报备',
        dataIndex: [
          'data',
          'data',
          'noPlanIn',
        ],
        width: 160,
        customRender: ({ text, record }) => renderNumber({
          text,
          record,
          type: 'noPlanIn',
          isClick: Number(text) > 0,
        }),
      },
      {
        title: '计划入场率',
        width: 100,
        dataIndex: [
          'data',
          'data',
          'planInRate',
        ],
        customRender: ({ text, record }) => renderNumber({
          text,
          record,
          type: 'planInRate',
        }),
      },
      {
        title: '实际入场率',
        dataIndex: [
          'data',
          'data',
          'actualInRate',
        ],
        width: 100,
        customRender: ({ text, record }) => renderNumber({
          text,
          record,
          type: 'actualInRate',
        }),
      },
      {
        title: '实际入场时间未报备',
        width: 160,
        dataIndex: [
          'data',
          'data',
          'noActIn',
        ],
        customRender: ({ text, record }) => renderNumber({
          text,
          record,
          type: 'noActIn',
          isClick: Number(text) > 0,
        }),
      },
      {
        title: '实际入场人数',
        minWidth: 150,
        dataIndex: [
          'data',
          'data',
          'actInCount',
        ],
        customRender: ({ text, record }) => renderNumber({
          text,
          record,
          type: 'actInCount',
          isClick: Number(text) > 0,
        }),
      },
    ]
    : [
      {
        title: '责任人',
        dataIndex: ['data', 'rspUserName'],
        width: 100,
        // @ts-ignore
        customHeaderCell() {
          return {
            class: 'required-cell',
          };
        },
      },
      {
        title: '必填项未填数',
        width: 150,
        dataIndex: [
          'data',
          'data',
          'requiredCount',
        ],
        customRender: ({ text, record }) => renderNumber({
          text,
          record,
          type: 'requiredPrepImplCount',
          isClick: Number(text) > 0,
        }),
      },
      {
        title: '实际离场人数',
        dataIndex: [
          'data',
          'data',
          'actOutCount',
        ],
        width: 150,
        customRender: ({ text, record }) => renderNumber({
          text,
          record,
          type: 'actOutCount',
          isClick: Number(text) > 0,
        }),
      },
      {
        title: '实际离场未报备人数',
        dataIndex: [
          'data',
          'data',
          'actOutNotReportCount',
        ],
        minWidth: 220,
        customRender: ({ text, record }) => renderNumber({
          text,
          record,
          type: 'actOutNotReportCount',
          isClick: Number(text) > 0,
        }),
      },
    ]));
  const innerColumns = computed<any[]>(() => (option.radioType.value === 'preparation' ? [
    {
      title: '人员',
      dataIndex: ['data', 'asset'],
      width: 300,
      fixed: 'left',
      customRender: renderPerson,
    },
    {
      title: '性别',
      dataIndex: ['data', 'sex'],
      width: 100,
    },
    {
      title: '常驻',
      dataIndex: ['data', 'isBasePermanent'],
      width: 100,
      customHeaderCell() {
        return {
          class: 'required-cell',
        };
      },
      customRender: (props) =>
        renderSelect({
          type: 'isReady',
          data,
          ...props,
          field: 'isBasePermanent',
          apiPath: API_PATH_PREPARE,
          detailsData,
          beforeEvent(editValue: any) {
            if (editValue && props.record?.data?.newcomer && !props.record?.data?.newcomerMatchPerson) {
              message.error('请填写接口人');
              return Promise.reject();
            }
            return Promise.resolve();
          },
        }),
    },
    {
      title: '驻地',
      dataIndex: ['data', 'baseName'],
      width: 100,
    },
    {
      title: '新人',
      dataIndex: ['data', 'newcomer'],
      width: 100,
      customHeaderCell() {
        return {
          class: 'required-cell',
        };
      },
      customRender: (props) =>
        renderSelect({
          type: 'isReady',
          data,
          ...props,
          field: 'newcomer',
          apiPath: API_PATH_PREPARE,
          detailsData,
        }),
    },
    {
      title: '接口人',
      dataIndex: ['data', 'newcomerMatchPerson'],
      width: 120,
      customRender: (props) =>
        (props?.record?.data?.newcomer
          ? renderInput({
            type: 'isReady',
            data,
            ...props,
            field: 'newcomerMatchPerson',
            apiPath: API_PATH_PREPARE,
            detailsData,
          })
          : props?.record?.data?.newcomerMatchPerson),
    },
    {
      title: '计划进场日期',
      dataIndex: ['data', 'planInDate'],
      width: 150,
      customHeaderCell() {
        return {
          class: 'required-cell',
        };
      },
      customRender: (props) =>
        renderDatePicker({
          type: 'isReady',
          data,
          ...props,
          field: 'planInDate',
          apiPath: API_PATH_PREPARE,
          detailsData,
        }),
    },
    {
      title: '计划离场日期',
      dataIndex: ['data', 'planOutDate'],
      width: 150,
      customHeaderCell() {
        return {
          class: 'required-cell',
        };
      },
      customRender: (props) =>
        renderDatePicker({
          type: 'isReady',
          data,
          ...props,
          field: 'planOutDate',
          apiPath: API_PATH_PREPARE,
          detailsData,
        }),
    },
    {
      title: '实际进场日期',
      dataIndex: ['data', 'actInDate'],
      width: 150,
      customHeaderCell() {
        return {
          class: 'required-cell',
        };
      },
      customRender: (props) =>
        renderDatePicker({
          type: 'isReady',
          data,
          ...props,
          field: 'actInDate',
          apiPath: API_PATH_PREPARE,
          disabledDate: true,
          detailsData,
          beforeEvent() {
            if (props.record?.data?.newcomer && !props.record?.data?.newcomerMatchPerson) {
              message.error('请填写接口人');
              return Promise.reject();
            }
            if (!props.record?.data?.planInDate) {
              message.error('请选择计划进场日期');
              return Promise.reject();
            }
            if (!props.record?.data?.planOutDate) {
              message.error('请选择计划离场日期');
              return Promise.reject();
            }
            return Promise.resolve();
          },
        }),
    },
    {
      title: '授权记录',
      dataIndex: ['data', 'jobPostName'],
      width: 80,
    },
    {
      title: '进场倒计时',
      dataIndex: ['data', 'inDays'],
      width: 90,
    },
    {
      title: '人员状态',
      dataIndex: ['data', 'status'],
      customRender: renderStatus,
      minWidth: 120,
    },
  ] : [
    {
      title: '人员',
      dataIndex: ['data', 'asset'],
      width: 300,
      fixed: 'left',
      customRender: renderPerson,
    },
    {
      title: '性别',
      dataIndex: ['data', 'sex'],
      width: 100,
    },
    {
      title: '常驻',
      dataIndex: ['data', 'isBasePermanent'],
      width: 150,
      customHeaderCell() {
        return {
          class: 'required-cell',
        };
      },
      customRender: (props) =>
        renderSelect({
          type: 'isExecute',
          data,
          ...props,
          field: 'isBasePermanent',
          apiPath: API_PATH_EXECUTE,
          detailsData,
        }),
    },
    {
      title: '驻地',
      dataIndex: ['data', 'baseName'],
      width: 200,
    },
    // {
    //   title: '离场原因',
    //   dataIndex: ['data', 'leaveReasonName'],
    //   width: 150,
    //   customHeaderCell() {
    //     return {
    //       class: 'required-cell',
    //     };
    //   },
    //   customRender: (props) =>
    //     renderSelect({
    //       type: 'isExecute',
    //       data,
    //       ...props,
    //       field: 'leaveReason',
    //       apiPath: API_PATH_EXECUTE,
    //       apiDict: 'pms_out_factory_reason',
    //       detailsData,
    //     }),
    // },
    // {
    //   title: '完成离场交接',
    //   dataIndex: ['data', 'isFinishOutHandover'],
    //   width: 150,
    //   customHeaderCell() {
    //     return {
    //       class: 'required-cell',
    //     };
    //   },
    //   customRender: (props) =>
    //     renderSelect({
    //       type: 'isExecute',
    //       data,
    //       ...props,
    //       field: 'isFinishOutHandover',
    //       apiPath: API_PATH_EXECUTE,
    //       detailsData,
    //     }),
    // },
    // {
    //   title: '再次入场',
    //   dataIndex: ['data', 'isAgainIn'],
    //   width: 100,
    //   customHeaderCell() {
    //     return {
    //       class: 'required-cell',
    //     };
    //   },
    //   customRender: (props) =>
    //     renderSelect({
    //       type: 'isExecute',
    //       data,
    //       ...props,
    //       field: 'isAgainIn',
    //       apiPath: API_PATH_EXECUTE,
    //       detailsData,
    //     }),
    // },
    {
      title: '实际离场日期',
      dataIndex: ['data', 'actOutDate'],
      width: 200,
      customHeaderCell() {
        return {
          class: 'required-cell',
        };
      },
      customRender: (props) =>
        renderDatePicker({
          type: 'isExecute',
          data,
          ...props,
          field: 'actOutDate',
          apiPath: API_PATH_EXECUTE,
          disabledDate: true,
        }),
    },
    {
      title: '人员状态',
      dataIndex: ['data', 'status'],
      customRender: renderStatus,
      align: 'left',
      minWidth: 200,
    },
  ]));

  const NODE_TYPES = [
    'executionSpecialty',
    'specialtyTeam',
    'businessData',
  ];
  const ALLOWED_ROLES = new Set(['READ', 'WRITE']);

  const actions = [
    {
      text: '查看',
      icon: 'sie-icon-yanjing',
      disabled: (record) => !record?.data?.id,
      isShow: (record) => {
        if (!record?.data || record?.data?.id === '0') return false;

        const hasValidNodeType = NODE_TYPES.includes(record.data?.nodeType);
        const hasPermission = record.roleList?.some((role) => ALLOWED_ROLES.has(role));

        return (hasValidNodeType && !tableMethods.isDetails) || hasPermission;
      },
      onClick(record) {
        const targetId = record?.data?.id;
        if (!targetId) return;
        const isRole = record.roleList?.includes('WRITE') ? 'true' : 'false';
        router.push({
          name: 'MajorTreeTable',
          query: {
            id: targetId,
            isRole,
          },
        });
      },
    },
  ];

  const innerActions = [
    {
      text: '查看',
      icon: 'sie-icon-yanjing',
      disabled: (record) => !record?.data?.basicUserId,
      isShow: (record) => ['READ', 'WRITE'].some((role) => record.roleList.includes(role)),
      onClick(record) {
        if (!record?.data?.basicUserId) return;
        router.push({
          name: 'PMSEmployeeCapabilityPoolDetails',
          params: {
            id: record?.data?.basicUserId,
          },
        });
      },
    },
    {
      icon: 'sie-icon-shanchu',
      text: '删除',
      isShow: (record) => record.roleList.includes('WRITE') && record?.data.status !== 1,
      modalTitle: '移除提示!',
      modalContent: '确认移除当前数据？',
      modal(record) {
        const params = {
          relationId: record?.id,
          repairOrgId: tableMethods.isDetails ? detailsData?.id : record?.parentId,
          repairRound: detailsData?.repairRound,
        };
        return new Promise((resolve) => {
          new Api('/pms/personManageNew/deleteNew').fetch([params], '', 'DELETE').then(() => {
            const recordId = record?.parentId;
            const parentIds = findParentIds(data.value, recordId);
            // 使用 Set 去重
            const uniqueParentIds = Array.from(new Set([...parentIds, recordId]));
            dataApi(uniqueParentIds);
          }).finally(() => {
            resolve('');
          });
        });
      },
    },
  ];

  return {
    expandedRowKeys,
    columns,
    innerColumns,
    dataApi,
    actions,
    innerActions,
    data,
    loadingRef,
  };
}

export default useUser;
