package com.chinasie.orion.management.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * RequirementPayMangement VO对象
 *
 * <AUTHOR>
 * @since 2024-07-15 14:00:35
 */
@ApiModel(value = "RequirementPayMangementVO对象", description = "需求支付信息")
@Data
public class RequirementPayMangementVO extends ObjectVO implements Serializable {

    /**
     * 保证金
     */
    @ApiModelProperty(value = "保证金")
    private BigDecimal bond;


    /**
     * 截标时间
     */
    @ApiModelProperty(value = "截标时间")
    private Date tendersEndTime;


    /**
     * 支付方式
     */
    @ApiModelProperty(value = "支付方式")
    private String payWay;


    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    private Date applyTime;


    /**
     * 付款人姓名
     */
    @ApiModelProperty(value = "付款人姓名")
    private String payer;


    /**
     * 投标有效期
     */
    @ApiModelProperty(value = "投标有效期")
    private Date bidValidity;

    /**
     * 需求编号
     */
    @ApiModelProperty(value = "需求编号")
    private String requirementNumber;


}
