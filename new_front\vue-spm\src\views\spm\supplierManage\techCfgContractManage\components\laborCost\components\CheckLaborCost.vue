<script setup lang="ts">
import { STable, STableSummaryCell, STableSummaryRow } from '@surely-vue/table';
import { formatMoney } from 'lyra-component-vue3';
import { Input, InputNumber, Spin } from 'ant-design-vue';
import {
  h, nextTick, reactive, ref, Ref, watchEffect,
} from 'vue';
import Api from '/@/api';

const props = withDefaults(defineProps<{
  isModal?: boolean
  isEdit?: boolean;
  list: any[]
  row: Record<string, any>
}>(), {
  isModal: false,
  isEdit: true,
});

const tableData: Ref<any[]> = ref([]);

watchEffect(() => {
  tableData.value = props.list;
});

const editCellMap = reactive(new Map());

function editCustomRender({ text, column, record }) {
  const mapKey = formatEditCell({
    column,
    record,
  });
  if (editCellMap.has(mapKey)) {
    const editSubmit = () => {
      if (!props.isModal && editCellMap.get(mapKey).value === text) {
        editCellMap.get(mapKey).loading = true;
        new Api('/spm/laborCostAcceptanceStatistics/edit').fetch({
          id: record.id,
          [column.dataIndex]: text,
        }, '', 'PUT').finally(() => {
          editCellMap.delete(mapKey);
        });
      }
      editCellMap.delete(mapKey);
    };

    return h(Spin, {
      spinning: editCellMap.get(mapKey).loading,
    }, {
      default: () => h(column.component, {
        ...(column.componentProps(record, mapKey)),
        onClick(e) {
          e.stopPropagation();
        },
        value: text,
        onPressEnter: editSubmit,
        onBlur: editSubmit,
      }, {
        default: () => '',
      }),
    });
  }
  return text;
}

// 格式化当前编辑单元格
function formatEditCell({ record, column }) {
  return `${column.dataIndex}_${record.id}`;
}

const columns = [
  {
    title: '序号',
    dataIndex: 'no',
    width: 50,
    align: 'center',
    customRender({ index }) {
      return index + 1;
    },
  },
  {
    title: '合同岗级',
    dataIndex: 'jobGrade',
  },
  {
    title: '计划需求人数',
    dataIndex: 'planUserCount',
  },
  {
    title: '实际人数',
    dataIndex: 'actualUserCount',
  },
  {
    title: '工作量(人/月)',
    dataIndex: 'workload',
    component: InputNumber,
    focusClass: 'ant-input-number-input',
    componentProps(record, mapKey) {
      return {
        precision: 2,
        onChange(value) {
          record.workload = value;
          editCellMap.get(mapKey).value = value;
        },
      };
    },
    customRender: editCustomRender,
  },
  {
    title: '岗级成本(人/月)',
    dataIndex: 'jobGradeAmt',
    customRender({ text }) {
      return formatMoney(text, true);
    },
  },
  {
    title: '岗位总价(元)',
    dataIndex: 'jobGradeTotalAmt',
    customRender({ text }) {
      return formatMoney(text, true);
    },
  },
  {
    title: '备注',
    dataIndex: 'remark',
    component: Input,
    focusClass: 'ant-input',
    componentProps(record, mapKey) {
      return {
        onChange(e) {
          record.remark = e.target.value;
          editCellMap.get(mapKey).value = e.target.value;
        },
      };
    },
    customRender: editCustomRender,
  },
];

const columns2 = [
  {
    title: '合计',
    dataIndex: 'actualTotalAmount',
    align: 'right',
  },
  {
    title: '岗级成本',
    dataIndex: 'jobGradeAmt',
    align: 'right',
  },
  {
    title: '住宿费',
    dataIndex: 'hotelAmount',
    align: 'right',
  },
  {
    title: '换乘费',
    dataIndex: 'transferAmount',
    align: 'right',
  },
  {
    title: '基地后勤费',
    dataIndex: 'logisticsAmt',
    align: 'right',
  },
  {
    title: 'RP体检费',
    dataIndex: 'medicalExaminationAmt',
    align: 'right',
  },
  {
    title: '劳保用品费',
    dataIndex: 'articlesAmt',
    align: 'right',
  },
  {
    title: '餐厅管理费',
    dataIndex: 'restaurantAmt',
    align: 'right',
  },
  {
    title: '其他费',
    dataIndex: 'otherAmt',
    align: 'right',
  },
];

function customCell({ column, record }) {
  if ((column.dataIndex === 'workload' || column.dataIndex === 'remark') && props.isEdit) {
    return {
      style: {
        cursor: 'pointer',
        background: '#EBF8FF',
      },
      async onClick(e) {
        editCellMap.set(formatEditCell({
          column,
          record,
        }), {
          edit: true,
          loading: false,
          value: undefined,
        });
        await nextTick();
        const element = e.target.querySelector(`.${column.focusClass}`);
        element.focus();
      },
    };
  }
}

defineExpose({
  getData() {
    if (tableData?.value?.length) {
      return tableData.value;
    }
    return undefined;
  },
});
</script>

<template>
  <STable
    rowKey="jobGrade"
    size="small"
    :dataSource="tableData"
    :columns="columns"
    :pagination="false"
    :customCell="customCell"
  >
    <template #summary>
      <STableSummaryRow>
        <STableSummaryCell :index="1">
          合计
        </STableSummaryCell>
        <STableSummaryCell :index="6">
          <template #default="{total}">
            {{ formatMoney(total, true) }}
          </template>
        </STableSummaryCell>
      </STableSummaryRow>
    </template>
  </STable>
  <STable
    size="small"
    class="mt30"
    :dataSource="row?[row]:[]"
    :pagination="false"
    :columns="columns2"
  />
</template>

<style scoped lang="less">

</style>
