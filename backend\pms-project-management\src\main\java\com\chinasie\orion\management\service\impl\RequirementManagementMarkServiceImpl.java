package com.chinasie.orion.management.service.impl;


import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.management.domain.dto.RequirementManagementMarkDTO;
import com.chinasie.orion.management.domain.entity.RequirementManagementMark;
import com.chinasie.orion.management.domain.vo.RequirementManagementMarkVO;
import com.chinasie.orion.management.repository.RequirementManagementMarkMapper;
import com.chinasie.orion.management.service.RequirementManagementMarkService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * RequirementManagementMark 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-05 19:52:08
 */
@Service
@Slf4j
public class RequirementManagementMarkServiceImpl extends OrionBaseServiceImpl<RequirementManagementMarkMapper, RequirementManagementMark> implements RequirementManagementMarkService {

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public RequirementManagementMarkVO detail(String id, String pageCode) {
        RequirementManagementMark requirementManagementMark = this.getById(id);
        RequirementManagementMarkVO result = BeanCopyUtils.convertTo(requirementManagementMark, RequirementManagementMarkVO::new);

        setEveryName(Collections.singletonList(result));
        return result;
    }

    /**
     * 新增
     * <p>
     * * @param requirementManagementMarkDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String save(RequirementManagementMarkDTO requirementManagementMarkDTO) {
        RequirementManagementMark requirementManagementMark = BeanCopyUtils.convertTo(requirementManagementMarkDTO, RequirementManagementMark::new);
        if (null == requirementManagementMark.getId() || StringUtils.isBlank(requirementManagementMark.getId())) {
            this.save(requirementManagementMark);
        } else {
            this.updateById(requirementManagementMark);
        }

        return requirementManagementMark.getId();
    }

    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(List<String> ids) {
        this.removeBatchByIds(ids);
        return true;
    }

    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<RequirementManagementMarkVO> pages(Page<RequirementManagementMarkDTO> pageRequest) {

        LambdaQueryWrapperX<RequirementManagementMark> condition = new LambdaQueryWrapperX<>(RequirementManagementMark.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        if (null != pageRequest.getOrders()) {
            pageRequest.getOrders().forEach(order -> condition.orderBy(true, order.getAsc(), order.getColumn()));
        }

        PageResult<RequirementManagementMark> page = this.getBaseMapper().selectPage(pageRequest, condition);

        Page<RequirementManagementMarkVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<RequirementManagementMarkVO> vos = BeanCopyUtils.convertListTo(page.getContent(), RequirementManagementMarkVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public void setEveryName(List<RequirementManagementMarkVO> vos) {

        vos.forEach(vo -> {
        });

    }

}
