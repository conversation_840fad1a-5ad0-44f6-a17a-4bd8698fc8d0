package com.chinasie.orion.management.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * ProcurePkgGroup Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-21 14:49:40
 */
@TableName(value = "pmsx_procure_pkg_group")
@ApiModel(value = "ProcurePkgGroupEntity对象", description = "采购项目包组")
@Data

public class ProcurePkgGroup extends  ObjectEntity  implements Serializable{

    /**
     * 包组编号
     */
    @ApiModelProperty(value = "包组编号")
    @TableField(value = "group_id")
    private String groupId;

    /**
     * 包组名称
     */
    @ApiModelProperty(value = "包组名称")
    @TableField(value = "group_name")
    private String groupName;

    /**
     * 报价轮次
     */
    @ApiModelProperty(value = "报价轮次")
    @TableField(value = "bidding_rounds")
    private Integer biddingRounds;

    /**
     * 报价开始时间
     */
    @ApiModelProperty(value = "报价开始时间")
    @TableField(value = "bidding_begin_time")
    private Date BiddingBeginTime;

    /**
     * 报价截止时间
     */
    @ApiModelProperty(value = "报价截止时间")
    @TableField(value = "bidding_end_time")
    private Date BiddingEndTime;

    /**
     * 报价状态
     */
    @ApiModelProperty(value = "报价状态")
    @TableField(value = "bidding_status")
    private String BiddingStatus;

    /**
     * 需求编号
     */
    @ApiModelProperty(value = "需求编号")
    @TableField(value = "requirement_id")
    private String requirementId;

}
