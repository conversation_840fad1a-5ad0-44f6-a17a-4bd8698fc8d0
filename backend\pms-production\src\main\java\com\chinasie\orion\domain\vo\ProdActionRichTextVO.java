package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.util.List;
/**
 * ProdActionRishText VO对象
 *
 * <AUTHOR>
 * @since 2024-10-24 10:33:48
 */
@ApiModel(value = "ProdActionRishTextVO对象", description = "生产大修行动项富文本")
@Data
public class ProdActionRichTextVO extends  ObjectVO   implements Serializable{

            /**
         * 行动项ID
         */
        @ApiModelProperty(value = "行动项ID")
        private String actionId;


        /**
         * 数据类型
         */
        @ApiModelProperty(value = "数据类型")
        private String dataType;


        /**
         * 富文本
         */
        @ApiModelProperty(value = "富文本")
        private String richText;


    

}
