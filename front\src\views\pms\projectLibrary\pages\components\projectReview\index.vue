<script setup lang="ts">
import {
  OrionTable, BasicTableAction, IOrionTableActionItem, BasicButton, isPower, openDrawer,
} from 'lyra-component-vue3';
import {
  computed, ComputedRef, h, ref, Ref,
} from 'vue';
import { Modal } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { formatActionsPowerStates } from './utils';
import CRUorMWXqEdit from './CRUorMWXqEdit.vue';
import Api from '/@/api';

const props = defineProps<{
  projectId:string|undefined,
  projectName:string|undefined
}>();
const router = useRouter();
const tableRef: Ref = ref();
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const columns = [
  {
    title: '操作',
    dataIndex: 'actions',
    width: 160,
    fixed: 'right',
    slots: { customRender: 'actions' },
  },
  {
    title: '评审编码',
    dataIndex: 'number',
  },
  {
    title: '评审名称',
    dataIndex: 'name',
    customRender({
      record, text,
    }) {
      if (isPower('view_button_178288_CRUorMWXq_SKmeW8Sf', record.rdAuthList)) {
        return h('span', {
          class: 'action-btn',
          onClick() {
            router.push({
              name: 'CRUorMWXqDetails',
              params: {
                id: record.id,
              },
            });
          },
        }, text);
      }
      return text;
    },
  },
  {
    title: '评审状态',
    dataIndex: 'status',
    slots: { customRender: 'status' },
  },
  {
    title: '评审类型',
    dataIndex: 'reviewTypeName',
  },
  {
    title: '任务计划',
    dataIndex: 'planName',
    customRender({
      record, text,
    }) {
      if (isPower('view_button_178288_CRUorMWXq_SKmeW8Sf', record.rdAuthList)) {
        return h('span', {
          class: 'action-btn',
          onClick() {
            router.push({
              name: 'ProPlanDetails',
              params: { id: record.planId },
            });
          },
        }, text);
      }
      return text;
    },
  },
  {
    title: '创建人',
    dataIndex: 'creatorName',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    type: 'dateTime',
  },
];

const tableOptions = {
  rowSelection: {
    onChange(keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  smallSearchField: undefined,
  columns,
  isFilter2: true,
  filterConfigName: 'PMS_PROJECT_REVIEW',
  api: (params: Record<string, any>) => new Api('/pms/review').fetch({
    ...params,
    power: {
      pageCode: 'PMS0004',
      headContainerCode: 'PMS_XMXQ_container_18_01',
      containerCode: 'PMS_XMXQ_container_18_02',
    },
    query: {
      projectId: props.projectId,
    },
  }, 'page', 'POST'),
};

const toolButtons: ComputedRef<Record<string, any>[]> = computed(() => [
  {
    event: 'add',
    text: '发起评审',
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
    code: 'add_button_178288_CRUorMWXq_SKmeW8Sf',
  },

]);

function toolClick({ event }: Record<string, any>) {
  switch (event) {
    case 'add':
      const drawerRef: Ref = ref();
      openDrawer({
        title: '新增',
        width: 1000,
        content() {
          return h(CRUorMWXqEdit, {
            ref: drawerRef,
            projectId: props?.projectId,
            projectName: props?.projectName,
          });
        },
        async onOk(): Promise<void> {
          await drawerRef.value.onSubmit();
          updateTable();
        },
      });
      break;
  }
}

const actions: IOrionTableActionItem[] = formatActionsPowerStates([
  {
    text: '编辑',
    event: 'edit',
    code: 'edit_button_178288_CRUorMWXq_SKmeW8Sf',
  },
  {
    text: '删除',
    event: 'delete',
    code: 'delete_button_178288_CRUorMWXq_SKmeW8Sf',
  },
]);

function actionClick(actionItem: { event: string }, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'edit':
      const drawerRef: Ref = ref();
      openDrawer({
        title: '编辑',
        width: 1000,
        content() {
          return h(CRUorMWXqEdit, {
            ref: drawerRef,
            formId: record?.id,
            projectId: record?.projectId,
            projectName: props.projectName,
          });
        },
        async onOk(): Promise<void> {
          await drawerRef.value.onSubmit();
          updateTable?.();
        },
      });
      break;
    case 'delete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除这条数据吗？',
        onOk: () => deleteApi([record.id]),
      });
      break;
  }
}

function updateTable() {
  tableRef.value?.reload();
}

function deleteApi(ids: string[]) {
  return new Promise((resolve) => {
    new Api('/pms/review').fetch(ids, 'remove', 'DELETE').then(() => {
      updateTable();
    }).finally(() => {
      resolve('');
    });
  });
}

function getButtonProps(item) {
  if (item.event !== 'add') {
    item.disabled = !selectedRows.value.length;
  }
  return item;
}
</script>

<template>
  <OrionTable
    ref="tableRef"
    v-get-power="{pageCode:'PMS0004'}"
    :options="tableOptions"
  >
    <template #toolbarLeft>
      <template
        v-for="button in toolButtons"
        :key="button.event"
      >
        <BasicButton
          v-is-power="[button.code]"
          v-bind="getButtonProps(button)"
          @click="toolClick(button)"
        >
          {{ button.text }}
        </BasicButton>
      </template>
    </template>
    <template #actions="{record}">
      <BasicTableAction
        :actions="actions"
        :record="record"
        @actionClick="actionClick($event,record)"
      />
    </template>
  </OrionTable>
</template>

<style scoped lang="less">

</style>
