package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.humanResource.ProjectHumanResourceSettingDTO;
import com.chinasie.orion.domain.dto.humanResource.ProjectHumanResourcePageDTO;
import com.chinasie.orion.domain.vo.humanResource.ProjectHumanResourcePageVO;
import com.chinasie.orion.domain.vo.humanResource.ProjectHumanResourceSettingVO;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.service.ProjectHumanResourceServiceSetting;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;

/**
 * <p>
 * ProjectHumanResource 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-15 17:57:05
 */
@RestController
@RequestMapping("/projectHumanResource")
@Api(tags = "人力资源库")
public class ProjectHumanResourceController {

    @Autowired
    private ProjectHumanResourceServiceSetting projectHumanResourceServiceSetting;
    
    @Autowired
    private UserRedisHelper userRedisHelper;

    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据【{{#id}}】", type = "人力资源库", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectHumanResourceSettingVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        ProjectHumanResourceSettingVO rsp = projectHumanResourceServiceSetting.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectHumanResourceSettingDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#projectHumanResourceDTO.name}}】", type = "人力资源库", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody ProjectHumanResourceSettingDTO projectHumanResourceSettingDTO) throws Exception {
        String rsp =  projectHumanResourceServiceSetting.create(projectHumanResourceSettingDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectHumanResourceSettingDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#projectHumanResourceDTO.name}}】", type = "人力资源库", subType = "编辑", bizNo = "{{#projectHumanResourceDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody ProjectHumanResourceSettingDTO projectHumanResourceSettingDTO) throws Exception {
        Boolean rsp = projectHumanResourceServiceSetting.edit(projectHumanResourceSettingDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "人力资源库", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = projectHumanResourceServiceSetting.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "人力资源库", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectHumanResourceServiceSetting.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "人力资源库", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ProjectHumanResourceSettingVO>> pages(@RequestBody Page<ProjectHumanResourceSettingDTO> pageRequest) throws Exception {
        Page<ProjectHumanResourceSettingVO> rsp =  projectHumanResourceServiceSetting.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 人力资源报表统计分页
     *
     * @param projectHumanResourcePageDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "人力资源报表统计分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "人力资源库", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/human/resource/page", method = RequestMethod.POST)
    public ResponseDTO pages(@RequestBody ProjectHumanResourcePageDTO projectHumanResourcePageDTO) throws Exception {
        List<ProjectHumanResourcePageVO> rsp =  projectHumanResourceServiceSetting.humanResourcePages(projectHumanResourcePageDTO);
        return new ResponseDTO<>(rsp);
    }

}
