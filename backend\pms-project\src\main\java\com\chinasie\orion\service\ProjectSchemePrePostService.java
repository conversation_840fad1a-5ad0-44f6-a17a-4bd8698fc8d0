package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.PreSchemeDTO;
import com.chinasie.orion.domain.dto.ProjectSchemePrePostDTO;
import com.chinasie.orion.domain.entity.ProjectSchemePrePost;
import com.chinasie.orion.mybatis.service.OrionBaseService;

import java.util.List;

/**
 * ProjectSchemePrePostService
 *
 * @author: yangFy
 * @date: 2023/4/19 16:08
 * @description:
 * <p>
 *项目计划前置后置关系
 * </p>
 */
public interface ProjectSchemePrePostService extends OrionBaseService<ProjectSchemePrePost> {

    /**
     * 删除(批量)
     * @param ids
     * @return
     */
    boolean deleteByIds(List<String> ids) throws Exception;

    /**
     * 添加前后置关系(批量)
     * @param preSchemeDTO
     * @return
     */
    List<String> createBatch(PreSchemeDTO preSchemeDTO) throws Exception;


    /**
     * 变更前置关系，删除后新增
     * @param prePostDTOS 前置关系列表
     * @return
     */
    List<String> modify(String id,List<ProjectSchemePrePostDTO> prePostDTOS) throws Exception;
}
