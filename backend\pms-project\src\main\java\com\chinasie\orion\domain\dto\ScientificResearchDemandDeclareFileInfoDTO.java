package com.chinasie.orion.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

/**
 * ScientificResearchDemandDeclareFileInfo DTO对象
 *
 * <AUTHOR>
 * @since 2023-11-23 11:37:06
 */
@ApiModel(value = "ScientificResearchDemandDeclareFileInfoDTO对象", description = "科研需求申报文件信息")
@Data
public class ScientificResearchDemandDeclareFileInfoDTO extends ObjectDTO implements Serializable{

    /**
     * 文件id
     */
    @ApiModelProperty(value = "文件id")
    private String fileDataId;

    /**
     * 科研需求申报id
     */
    @ApiModelProperty(value = "科研需求申报id")
    private String declareId;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String type;
}

