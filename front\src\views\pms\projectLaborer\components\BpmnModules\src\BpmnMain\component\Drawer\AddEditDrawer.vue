<template>
  <BasicDrawer
    v-bind="$attrs"
    :title="(type === 'edit' ? '编辑' : '创建') + '信息'"
    width="340"
    :mask-closable="false"
    :show-footer="true"
    :after-visible-change="afterVisibleChange"
    @register="drawerRegister"
  >
    <template v-if="visible">
      <BasicForm @register="formRegister" />
      <div class="table-wrap">
        <OrionTable :options="tableOptions">
          <template #assignees="{ record }">
            <div>{{ getUserName(record) }}</div>
          </template>
          <template #action="{ record }">
            <div
              v-if="!record.applicant"
              class="action-btn"
              @click="addUser(record)"
            >
              指定
            </div>
          </template>
        </OrionTable>
      </div>
    </template>

    <!--选人-->
    <SelectUserModal
      :on-ok="selectUserChange"
      @register="selectUserRegister"
    />

    <template #footer>
      <div class="flex flex-pac">
        <div class="mr10">
          <DrawerBasicButton
            type="cancel"
            @click="closeDrawer"
          >
            取消
          </DrawerBasicButton>
        </div>
        <div class="mr10">
          <DrawerBasicButton
            :loading="saveLoadingStatus"
            @click="save"
          >
            保存
          </DrawerBasicButton>
        </div>
        <div>
          <DrawerBasicButton @click="saveAndStart">
            保存并启动
          </DrawerBasicButton>
        </div>
      </div>
    </template>
  </BasicDrawer>
</template>

<script lang="ts">
import {
  defineComponent, reactive, computed, toRefs, nextTick, inject, unref,
} from 'vue';
// import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
import { useForm, BasicForm } from '/@/components/Form';
import {
  useActionsRecord, Layout, OrionTable, BasicTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer, DrawerBasicButton, SelectUserModal, BasicDrawer,
} from 'lyra-component-vue3';
// import { DrawerBasicButton } from '/@/components/BasicButton';
// import { OrionTable } from '/@/components/OrionTable';
import Api from '/@/api';
import { message } from 'ant-design-vue';
// import { workflowApi } from '/@/views/flowcenter/util/apiConfig';
// import { SelectUserModal } from '/@/components/SelectUser';
// import { useModal } from '/@/components/Modal';
import { useRoute } from 'vue-router';
import { flowStart } from '../../methods/method';

export default defineComponent({
  name: 'AddEditDrawer',
  components: {
    BasicDrawer,
    BasicForm,
    DrawerBasicButton,
    OrionTable,
    SelectUserModal,
  },
  props: {
    // 模块ID
    deliveryId: {
      type: String,
      default: '',
    },
    dataType: {
      type: String,
      default: '',
    },
    userId: {
      type: String,
      default: '',
    },
    projectId: {
      type: String,
      default: '',
    },
    procInstName: {
      type: String,
      default: '',
    },
    groupId: {
      type: String,
      default: '',
    },
  },
  emits: ['addSuccessChange'],
  setup(props, context) {
    const { emit } = context;
    const route = useRoute();
    const bpmnModuleData = inject('bpmnModuleData');
    const state = reactive({
      visible: false,
      type: 'add',
      editData: null,
      // 表格数据
      dataSource: [],
      // 穿梭框实例
      transferModalRef: null,
      // 穿梭框数据
      transferDataSource: [],

      // 正在指定人员的行
      currentTableItem: null,
      // 选择流程下拉框数据
      selectFlowSelectData: [],

      // 状态列表
      statusList: [],
      // 项目ID
      projectId: props.projectId,
      // 当前选择的流程项
      selectFlowItem: null,

      // 保存按钮加载状态
      saveLoadingStatus: false,
    });

    // 穿梭框选中人员ID
    const selectIds = computed(() => {
      if (state.currentTableItem && state.currentTableItem.selectIds) {
        return state.currentTableItem.selectIds.split(',');
      }
      return [];
    });

    // 注册弹窗
    const [drawerRegister, { closeDrawer }] = useDrawerInner(async (paramsData) => {
      const { type, data } = paramsData;
      state.visible = true;
      state.type = type || 'add';

      await init();

      if (type === 'edit') {
        nextTick(() => {
          state.editData = data;
          editInit(data);
        });
      }
    });
    const [selectUserRegister, { openModal: selectUserOpenModal }] = useModal();

    async function init() {
      state.selectFlowSelectData = await loadSelectData();
      if (
        state.selectFlowSelectData
          && state.selectFlowSelectData.length
          && state.type === 'add'
      ) {
        setDefaultSelectOption(state.selectFlowSelectData[0]);
      }
    }

    // 编辑初始化
    async function editInit(editData) {
      const { procDefId } = editData;
      await setFieldsValue({
        flowSelect: procDefId,
      });
      selectChange(procDefId);
    }

    // 获取表格数据
    function loadTableData(procDefId) {
      new Api('/workflow/act-prearranged/all-tasks/page')
        .fetch(
          {
            query: {
              userId: props.userId,
              procDefId,
              procInstId: '',
              ...(() => {
                if (state.type === 'edit') {
                  return {
                    prearrangeId: state.editData.id,
                  };
                }
                return {};
              })(),
            },
            queryCondition: [],
          },
          '',
          'POST',
        )
        .then((data) => {
          state.dataSource = data;
        });
    }

    /**
       * 下拉选择事件
       * @param procDefId
       */
    function selectChange(procDefId) {
      const flowItem = getFlowItem(procDefId);
      loadTableData(procDefId);
      setFlowInfo(flowItem);
      _getStatusList(flowItem.id);
    }

    // 下载下拉选项
    function loadSelectData() {
      return new Api('/res/data-type/list')
        .fetch(
          {
            dataType: props.dataType,
          },
          '',
          'POST',
        )
        .then((data) => {
          if (data && data.length) {
            return new Api('/res/data-type-group/template/list')
              .fetch(
                {
                  dataTypeId: data[0].id,
                  groupId: props.groupId,
                  templateType: 'FLOW-TEMPLATE',
                },
                '',
                'POST',
              )
              .then((data) => (
                (data
                      && data.map((item) => ({
                        ...item,
                        label: item.name,
                        key: item.procDefId,
                        value: item.procDefId,
                      })))
                    || []
              ));
          }
          return [];
        });
    }

    // 下拉表单项
    const [formRegister, { setFieldsValue }] = useForm({
      actionColOptions: {
        span: 24,
      },
      showActionButtonGroup: false,
      schemas: [
        {
          field: 'flowSelect',
          component: 'Select',
          label: '选择流程:',
          required: true,
          colProps: {
            span: 28,
          },
          componentProps: {
            options: computed(() => state.selectFlowSelectData),
            onChange: (procDefId) => {
              selectChange(procDefId);
            },
          },
        },
      ],
    });

    const tableOptions = {
      deleteToolButton: 'add|delete|enable|disable',
      showIndexColumn: false,
      showSmallSearch: false,
      pagination: false,
      resizeHeightOffset: 60,
      dataSource: computed(() => state.dataSource),
      columns: [
        {
          title: '节点名称',
          align: 'left',
          width: 80,
          dataIndex: 'taskName',
        },
        {
          title: '负责人',
          dataIndex: 'assignees',
          slots: { customRender: 'assignees' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 60,
          slots: { customRender: 'action' },
        },
      ],
    };

    // 设置默认选择第一项
    function setDefaultSelectOption(selectItem) {
      const { id, procDefId } = selectItem;

      setFlowInfo(selectItem);
      setFieldsValue({
        flowSelect: procDefId,
      });
      _getStatusList(id);
      loadTableData(procDefId);
    }

    // 获取选中流程项
    function getFlowItem(procDefId) {
      return state.selectFlowSelectData.find((item) => item.procDefId === procDefId);
    }

    // 设置流程信息，
    function setFlowInfo(flowItem) {
      state.selectFlowItem = flowItem;
    }

    // 选择人员
    function selectUserChange(targetItems) {
      if (!state.currentTableItem.multiInst && targetItems.length > 1) {
        message.warn('此节点为单人审批，不能选择多个审批人');
        return Promise.reject();
      }
      // 会签模块，=====  还未与产品后端确认该功能模块的开发  =========
      if (state.currentTableItem && state.currentTableItem.tags.indexOf('JOINTLY_SIGN') !== -1) {
        // const currentTableItem = state.currentTableItem;
        // currentTableItem.countersignUserId = targetItems?.id || '';
        // currentTableItem.countersignUserName = targetItems?.name || '';
        // putJointlySignTableData(currentTableItem);
      } else {
        state.currentTableItem.assigneesUser = targetItems;
        return Promise.resolve();
      }
    }

    // 调用接口修改数据
    const putJointlySignTableData = (data): void => {
      data.desId = props.deliveryId;
      new Api('/pms').fetch(data, 'countersign', 'PUT').then(() => {
        JointlySignInfo();
      });
    };

    // 指定人员按钮
    async function addUser(record) {
      state.currentTableItem = record;
      selectUserOpenModal();
    }

    // 校验是否配置了审批人
    function _checkReviewer() {
      let check = true;
      const data = state.dataSource;
      if (!data[0].assigneeNeedless && !data[0].selectIds && !data[0].applicant) {
        check = false;
      }
      return check;
    }

    function _getStatusList(id) {
      new Api('/pmi')
        .fetch({}, `status/flow/condition?dataId=${props.deliveryId}&templateId=${id}`, 'GET')
        .then((res) => {
          state.initStatus = res[0]?.id;
          state.statusList = res;
        });
    }

    /**
       * 保存
       * @param isClose 是否关闭弹窗
       * @param isMessage 是否Message提示
       */
    function save(isClose = true, isMessage = true) {
      if (!_checkReviewer()) {
        message.warn('请选中流程节点负责人');
        return Promise.reject();
      }

      let statusItem = state.statusList.find((item) => item.id === state.initStatus);
      let statusName = statusItem?.remark;

      const { id, name, procDefId } = state.selectFlowItem;

      const params: any = {
        href: route.fullPath,
        prearranges: [],
        flowInfoId: id,
        bizId: state.projectId,
        procDefName: name,
        procInstName: props.procInstName,
        procDefId,
        userId: props.userId,
        deliveries: [
          {
            deliveryId: props.deliveryId,
            deliveryStatus: statusItem?.id,
            deliveryStatusName: statusName,
          },
        ],
      };

      function getAssignees(item): array | boolean {
        if (!item.assigneesUser || !item.assigneesUser) {
          return false;
        }
        const arr = item.assigneesUser.map((item) => item.id);
        return (arr.length && arr) || false;
      }

      state.dataSource.forEach((item) => {
        const assignees = getAssignees(item);
        if (assignees && assignees.length) {
          params.prearranges.push({
            assignees,
            taskDefinitionKey: item.taskDefinitionKey,
          });
        }
      });

      if (state.type === 'edit') {
        params.id = state.editData?.id;
      }
      state.saveLoadingStatus = true;

      return new Api('/workflow')
        .fetch(params, 'act-prearranged', 'POST')
        .then((newData) => {
          if (isClose) {
            closeDrawer();
          }
          isMessage && message.success('保存成功');
          emit('addSuccessChange');
          return newData;
        })
        .finally(() => {
          isClose && (state.saveLoadingStatus = false);
        })
        .catch(() => {
          throw new Error('保存失败');
        });
    }

    // 获取负责人姓名组合
    function getUserName(record) {
      const { assigneesUser } = record;
      if (!assigneesUser) {
        return '';
      }
      return assigneesUser.map((item) => item.name).join(',');
    }

    // 抽屉展开与隐藏
    function afterVisibleChange(visible) {
      if (!visible) {
        state.visible = visible;
      }
    }

    // 保存并启动
    async function saveAndStart() {
      try {
        const newData = await save(false, false);
        const moduleData = unref(bpmnModuleData);
        await flowStart(newData.id, props.userId, moduleData?.menuMethods?.load, false).finally(
          async () => {
            await closeDrawer();
          },
        );
        message.success('保存并启动成功');
        state.saveLoadingStatus = false;
      } catch (e) {
        state.saveLoadingStatus = false;
      }
    }

    return {
      ...toRefs(state),
      drawerRegister,
      selectUserRegister,
      selectIds,
      formRegister,
      tableOptions,
      closeDrawer,
      addUser,
      save,
      selectUserChange,
      afterVisibleChange,
      getUserName,
      saveAndStart,
    };
  },
});
</script>

<style lang="less" scoped>
  .table-wrap {
    :deep(.ant-table-wrapper) {
      padding: 0;

      .ant-table-title {
        display: none;
      }
    }
  }
</style>
