<template>
  <Layout
    v-get-power="{pageCode:'PMS_XMJSBB_PerformanceReport'}"
    :options="{ body: { scroll: true } }"
    contentTitle="项目绩效报表"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <BasicButton
          v-is-power="['PMS_XMJXBB_container_button01']"
          icon="fa-external-link"
          :disabled="tableInfo.disabled"
          @click="goDown(tableInfo.keys)"
        >
          导出所选
        </BasicButton>
        <BasicButton
          v-is-power="['PMS_XMJXBB_container_button02']"
          icon="orion-icon-upload"
          @click="goDown()"
        >
          导出全部
        </BasicButton>
      </template>
      <template #headerCell="{ column }">
        <template v-if="column.dataIndex === 'milestoneCompleteRate'">
          <span>
            {{ column.title }}
            <Tooltip
              title="按期完成的里程碑数/总里程碑数"
            >
              <Icon icon="sie-icon-attr" />
            </Tooltip>
          </span>
        </template>
        <template v-if="column.dataIndex === 'costBudgetRate'">
          <span>
            {{ column.title }}
            <Tooltip
              title="项目实际成本/项目预算成本"
            >
              <Icon icon="sie-icon-attr" />
            </Tooltip>
          </span>
        </template>
      </template>

      <template #filter>
        <div class="padding-bottom-10">
          <div class="form-wrap">
            <BasicForm @register="register">
              <template #datePicker="{model,field}">
                <TimeSelect v-model:value="model[field]" />
              </template>
              <template #Button>
                <BasicButton
                  icon="sie-icon-sousuo"
                  @click="search"
                >
                  查询
                </BasicButton>
                <BasicButton
                  icon="sie-icon-chongzhi"
                  @click="reset"
                >
                  重置
                </BasicButton>
              </template>
            </BasicForm>
          </div>
        </div>
      </template>
    </OrionTable>
  </Layout>
</template>

<script setup lang="ts">
import {
  h, reactive, ref,
} from 'vue';
import {
  OrionTable,
  useForm,
  BasicForm,
  BasicButton,
  DataStatusTag,
  Icon,
  useITable,
  downloadByData,
  Layout,
  getDictByNumber,
} from 'lyra-component-vue3';
import Api from '/@/api';
import { DatePicker, Tooltip, Modal } from 'ant-design-vue';
import { isArray } from 'lodash-es';
import dayjs from 'dayjs';
import TimeSelect from './src/TimeSelect.vue';

const open = ref(false);
const query: any = ref({});
const searchConditions: any = ref([]);
const paramsObj: any = ref({});
const [tableRef, tableInfo]: any = useITable({});
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  showSmallSearch: false,
  rowSelection: {},
  resizeHeightOffset: 175,
  showTableSetting: false,
  showIndexColumn: true,
  api: (tableParams) => {
    let params = {
      ...tableParams,
      query: query.value,
      searchConditions: searchConditions.value,
    };
    paramsObj.value = params;
    return new Api('/pms/projectPerformanceReport/getPage').fetch(params, '', 'POST');
  },
  columns: [
    {
      title: '项目编号',
      dataIndex: 'number',
    },
    {
      title: '项目名称',
      dataIndex: 'name',
    },
    {
      title: '项目状态',
      dataIndex: 'statusName',
      customRender: ({ record }) => h(DataStatusTag, { statusData: record.dataStatus }),
    },
    {
      title: '项目级别',
      dataIndex: 'levelName',
    },
    {
      title: '项目类型',
      dataIndex: 'projectTypeName',
    },
    {
      title: '项目经理',
      dataIndex: 'pm',
    },
    {
      title: '所属部门',
      dataIndex: 'deptName',
    },
    {
      title: '里程碑达成率',
      dataIndex: 'milestoneCompleteRate',
      slots: { customTitle: 'milestoneCompleteRate' },
    },
    {
      title: '项目成本预算管控率',
      dataIndex: 'costBudgetRate',
      key: 'costBudgetRate',
      slots: { customTitle: 'costBudgetRate' },
      width: 180,
    },
    {
      title: '测试检查实际完成',
      dataIndex: 'testCheckCompleteTime',
    },
    {
      title: '测试计划实际开始时间',
      dataIndex: 'testPlanStartTime',
    },
    // {
    //   title: '产品转状态按时达成率',
    //   align: 'left',
    //   dataIndex: 'number',
    //   slots: { customTitle: 'costBudgetRate' },
    // },
  ],
});
const [register, method] = useForm({
  schemas: [
    {
      component: 'Input',
      field: 'name',
      label: '项目名称',
    },
    {
      component: 'Input',
      field: 'number',
      label: '项目编号',
    },
    {
      component: 'ApiSelect',
      label: '项目状态',
      field: 'status',
      componentProps() {
        return {
          api: () => new Api('/pms/project-task-status/policy/status/list/project').fetch('', '', 'GET'),
          labelField: 'name',
        };
      },
    },
    {
      component: 'ApiSelect',
      label: '项目级别',
      field: 'level',
      componentProps() {
        return {
          api: () => getDictByNumber('project_level_type'),
          labelField: 'description',
        };
      },
    },
    {
      component: 'ApiSelect',
      label: '项目类型',
      field: 'projectType',
      componentProps() {
        return {
          mode: 'multiple',
          api: () => new Api('/pms/dict/code/pms_project_type').fetch('', '', 'GET'),
          labelField: 'name',
        };
      },
    },
    {
      component: 'ApiTreeSelect',
      label: '所属部门',
      field: 'orgId',
      componentProps: {
        multiple: true,
        showSearch: true,
        labelField: 'name',
        valueField: 'id',
        treeNodeFilterProp: 'name',
        api: () => new Api('/pmi/organization/business/org/tree').fetch([], '', 'POST'),
      },
    },
    {
      component: 'DatePicker',
      field: 'statisticalPeriod',
      label: '统计周期',
      slot: 'datePicker',
    },
    {
      component: 'Input',
      field: 'A4',
      label: '',
      slot: 'Button',
    },
  ],
  baseColProps: {
    span: 6,
  },
  showActionButtonGroup: false,
  layout: 'horizontal',
});

function selectAll() {
  open.value = false;
  method.setFieldsValue({ Aa: 'all' });
}

function dataFormat(data) {
  return '全部';
}

// 搜索
function search() {
  const res = method.getFieldsValue();
  let Conditions: any = [];
  let obj: any = {};
  if (res) {
    obj = JSON.parse(JSON.stringify(res));
    for (const key in obj) {
      if (!obj[key]) {
        delete obj[key];
      } else if (['name', 'number'].includes(key)) {
        Conditions.push({
          field: key,
          fieldType: 'String',
          values: [obj[key]],
          queryType: 'like',
        });
      } else if (key === 'statisticalPeriod') {
      } else {
        Conditions.push({
          field: key,
          fieldType: 'String',
          values: isArray(obj[key]) ? obj[key] : [obj[key]],
          queryType: 'eq',
        });
      }
    }
  }
  query.value = obj?.statisticalPeriod ? { statisticalPeriod: obj.statisticalPeriod } : {};
  searchConditions.value = Conditions?.length ? [Conditions] : [];
  tableRef.value.reload();
}

// 重置搜索
function reset() {
  method.resetFields();
  query.value = {};
  searchConditions.value = [];
  tableRef.value.reload();
}

function goDown(keys) {
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出？',
    onOk() {
      // 如果勾选了数据,需要传入id
      let obj: any = JSON.parse(JSON.stringify(paramsObj.value));
      if (keys?.length) {
        if (obj.searchConditions?.length) {
          obj.searchConditions[0].push({
            field: 'id',
            fieldType: 'String',
            values: keys,
            queryType: 'in',
          });
        } else {
          obj.searchConditions = [
            [
              {
                field: 'id',
                fieldType: 'String',
                values: keys,
                queryType: 'in',
              },
            ],
          ];
        }
      }
      downloadByData('/pms/projectPerformanceReport/export/excel', obj, '', 'POST', true, false, '导出处理完成，现在开始下载');
    },
  });
}
</script>

<style scoped lang="less">
.date-footer {
  text-align: center;
  cursor: pointer;
}

.form-wrap {
  position: relative;
  //padding: ~`getPrefixVar('content-margin')`;
  display: flex;
  flex-direction: column;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.24);
  border-radius: ~`getPrefixVar('radius-base')`;
}

.padding-bottom-10 {
  padding-bottom: ~`getPrefixVar('content-margin')`;
}
</style>
