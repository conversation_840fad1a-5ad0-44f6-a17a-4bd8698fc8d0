package com.chinasie.orion.domain.vo.material;

import com.chinasie.orion.constant.StatisticField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class MaterialOutVO implements Serializable {

    /**
     * 责任人id
     */
    @ApiModelProperty(value = "责任人id")
    private String rspUserId;

    /**
     * 责任人名称
     */
    @ApiModelProperty(value = "责任人名称")
    private String rspUserName;
    /**
     * 资产名称
     */
    @ApiModelProperty(value = "资产名称")
    private String assetName;

    /**
     * 资产代码
     */
    @ApiModelProperty(value = "资产代码")
    private String assetCode;

    @ApiModelProperty(value = "实际离场日期")
    private Date actOutDate;

    @ApiModelProperty(value = "离场数量")
    private Integer outNum;

    @ApiModelProperty(value = "出库原因")
    private String outReason;
    @ApiModelProperty(value = "出库原因名稱")
    private String outReasonName;

    @ApiModelProperty(value = "物资去向")
    private String materialDestination;

    @ApiModelProperty(value = "是否再次入场")
    private Boolean isAgainIn;

    @ApiModelProperty(value = "资产编码")
    private String number;

    @ApiModelProperty(value = "实际进场日期")
    private Date actInDate;

    @ApiModelProperty(value = "计划离场日期")
    private Date outDate;

    @ApiModelProperty(value = "物资库id")
    private String materialManageId;

    @ApiModelProperty(value = "固定资产能力库id")
    private String fixedAssetsId;



}
