package com.chinasie.orion.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;


import com.chinasie.orion.domain.entity.BasicUserCertificate;
import com.chinasie.orion.domain.dto.BasicUserCertificateDTO;
import com.chinasie.orion.domain.vo.BasicUserCertificateVO;

import com.chinasie.orion.service.BasicUserCertificateService;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * BasicUserCertificate 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05 22:07:29
 */
@RestController
@RequestMapping("/basic-user-certificate")
@Api(tags = "基础用户证书关系表")
public class  BasicUserCertificateController  {

    @Autowired
    private BasicUserCertificateService basicUserCertificateService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询基础用户证书关系详情", type = "BasicUserCertificate", subType = "查询详情", bizNo = "{{#id}}")
    public ResponseDTO<BasicUserCertificateVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        BasicUserCertificateVO rsp = basicUserCertificateService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param basicUserCertificateDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增基础用户证书关系数据【{{#basicUserCertificateDTO.number}}】", type = "BasicUserCertificate", subType = "新增", bizNo = "")
    public ResponseDTO<String> create(@RequestBody BasicUserCertificateDTO basicUserCertificateDTO) throws Exception {
        String rsp =  basicUserCertificateService.create(basicUserCertificateDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param basicUserCertificateDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑基础用户证书关系了数据【{{#basicUserCertificateDTO.number}}】", type = "BasicUserCertificate", subType = "编辑", bizNo = "{{#basicUserCertificateDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  BasicUserCertificateDTO basicUserCertificateDTO) throws Exception {
        Boolean rsp = basicUserCertificateService.edit(basicUserCertificateDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除基础用户证书关系【{{#id}}】数据", type = "BasicUserCertificate", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = basicUserCertificateService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除基础用户证书关系{{#ids.toString()}}数据", type = "BasicUserCertificate", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = basicUserCertificateService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询基础用户证书关系分页数据", type = "BasicUserCertificate", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<BasicUserCertificateVO>> pages(@RequestBody Page<BasicUserCertificateDTO> pageRequest) throws Exception {
        Page<BasicUserCertificateVO> rsp =  basicUserCertificateService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }



    /**
     * 获取指定人员的相关证书
     * @param userCode
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取指定人员的相关证书")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询基础用户证书关系【{{#userCode}}】的相关证书信息", type = "BasicUserCertificate", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/user/certificate/list", method = RequestMethod.POST)
    public ResponseDTO<List<BasicUserCertificateVO>> userCertificateList(@RequestParam("userCode") String userCode) throws Exception {
        List<BasicUserCertificateVO> rsp =  basicUserCertificateService.userCertificateList( userCode);
        return new ResponseDTO<>(rsp);
    }

}
