package com.chinasie.orion.service;


import com.chinasie.orion.amqp.entity.ChangeStatusMessageDTO;
import com.chinasie.orion.domain.dto.YearInvestmentSchemeDTO;
import com.chinasie.orion.domain.entity.YearInvestmentScheme;
import com.chinasie.orion.domain.vo.YearInvestmentSchemeVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * YearInvestmentScheme 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16 14:21:48
 */
public interface YearInvestmentSchemeService extends OrionBaseService<YearInvestmentScheme> {

    /**
     * 初始值
     *
     * @param investmentSchemeId
     * @param currentYear
     * @return
     */
    YearInvestmentSchemeVO initValue(String investmentSchemeId, String currentYear, String yearId) throws Exception;

    /**
     * 详情
     * <p>
     * * @param id
     */
    YearInvestmentSchemeVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param yearInvestmentSchemeDTO
     */
    YearInvestmentSchemeVO create(YearInvestmentSchemeDTO yearInvestmentSchemeDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param yearInvestmentSchemeDTO
     */
    YearInvestmentSchemeVO edit(YearInvestmentSchemeDTO yearInvestmentSchemeDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    List<YearInvestmentSchemeVO> list(String investmentSchemeId, String pageCode, String containerCode) throws Exception;

    /**
     * 导出
     *
     * @param yearIds
     * @param response
     */
    void exportV2(List<String> yearIds, HttpServletResponse response) throws Exception;


    /**
     * 是否可以点击年度投资计划调整按钮
     *
     * @param investId
     * @return
     */
    String canChange(String investId,String yearId) throws Exception;


    /**
     * 年度投资计划调整
     *
     * @param yearId
     * @param yearInvestmentSchemeDTO
     * @return
     */
    YearInvestmentSchemeVO change(String yearId, YearInvestmentSchemeDTO yearInvestmentSchemeDTO) throws Exception;

    /**
     * 调整记录列表
     *
     * @param yearId
     * @return
     */
    List<YearInvestmentSchemeVO> changeList(String yearId) throws Exception;

    /**
     * 状态变更
     *
     * @param message
     * @return
     */
    Boolean updateStatus(ChangeStatusMessageDTO message) throws Exception;

    /**
     *  修改数据中的所有数据对应的项目id和项目序号
     * @return
     */
    Boolean updateEntityProjectNumber() throws Exception;
}
