package com.chinasie.orion.dict;

public class GoodsServiceDict {

    /**
     * 物资服务类型字典编码
     */
    public static final String GOODS_SERVICE_TYPE_CODE = "goods_service_Type";

    /**
     * 物资服务类型字典编码-物资
     */
    public static final String GOODS_TYPE_CODE = "goodsType";

    /**
     * 物资服务类型字典编码-服务
     */
    public static final String SERVICE_TYPE_CODE = "serviceType";

    /**
     * 物资服务计量单位字典编码-物资
     */
    public static final String GOODS_TYPE_UNIT = "goods_type_unit";
    /**
     * 物资服务计量单位字典编码-物资-单位（吨）
     */
    public static final String GOODS_TYPE_UNIT_TONS = "unitTons";
    /**
     * 物资服务计量单位字典编码-物资-单位（KG）
     */
    public static final String GOODS_TYPE_UNIT_KG = "unitKG";
    /**
     * 物资服务计量单位字典编码-物资-单位（M-米）
     */
    public static final String GOODS_TYPE_UNIT_METER = "unitMeter";
    /**
     * 物资服务计量单位字典编码-物资-单位（件）
     */
    public static final String GOODS_TYPE_UNIT_PIECE = "unitPiece";

    /**
     * 物资服务计量单位字典编码-服务
     */
    public static final String SERVICE_TYPE_UNIT = "service_type_unit";
    /**
     * 物资服务计量单位字典编码-服务-单位（年）
     */
    public static final String SERVICE_TYPE_UNIT_YEAR = "unitYear";
    /**
     * 物资服务计量单位字典编码-服务-单位（月）
     */
    public static final String SERVICE_TYPE_UNIT_MONTH = "unitMonth";
    /**
     * 物资服务计量单位字典编码-服务-单位（日）
     */
    public static final String SERVICE_TYPE_UNIT_DAY = "unitDay";

}
