package com.chinasie.orion.domain.dto.projectscheme;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/8/26
 */
@Data
@ApiModel(value = "StatDTO对象", description = "个人中心统计计划数据")
public class StatDTO implements Serializable {

    @ApiModelProperty(value = "年份")
    String year;

    @ApiModelProperty(value = "类型 1-数据 2-所级 3-中心")
    Integer type;
}
