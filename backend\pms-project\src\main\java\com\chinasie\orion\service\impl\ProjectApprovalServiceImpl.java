package com.chinasie.orion.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.bo.CodeBo;
import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.bo.LyraFileBO;
import com.chinasie.orion.constant.ClassNameConstant;
import com.chinasie.orion.constant.DictConstant;
import com.chinasie.orion.constant.FileConstant;
import com.chinasie.orion.constant.ProjectApprovalStatusEnum;
import com.chinasie.orion.dict.ApprovalDict;
import com.chinasie.orion.domain.dto.ProjectApprovalDTO;
import com.chinasie.orion.domain.dto.ProjectDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.request.quality.QualityStepVO;
import com.chinasie.orion.domain.vo.ProjectApprovalVO;
import com.chinasie.orion.domain.vo.ProjectVO;
import com.chinasie.orion.exception.BaseErrorCode;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.pdm.api.domain.dto.ProductEstimateMaterialDTO;
import com.chinasie.orion.pdm.api.service.ProductApiService;
import com.chinasie.orion.repository.ProjectApprovalMapper;
import com.chinasie.orion.repository.ProjectRepository;
import com.chinasie.orion.repository.ProjectToBasePlanRepository;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * ProjectApproval 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-06 10:36:23
 */
@Service
public class ProjectApprovalServiceImpl extends OrionBaseServiceImpl<ProjectApprovalMapper, ProjectApproval> implements ProjectApprovalService {

    @Autowired
    private ProjectApprovalMapper projectApprovalMapper;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Resource
    private DictBo dictBo;

    @Autowired
    private CodeBo codeBo;

    @Autowired
    private ProjectApprovalMilestoneService projectApprovalMilestoneService;

    @Resource
    private LyraFileBO fileBo;

    @Resource
    private ProjectDeclareService projectDeclareService;

    @Resource
    private CurrentUserHelper currentUserHelper;


    @Autowired
    private RequireReviewFormService  requireReviewFormService;
    @Autowired
    private ProjectToProductService projectToProductService;
    @Autowired
    private ProjectApprovalProductService productService;
    @Autowired
    private ProductApiService productApiService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProjectApprovalVO detail(String id) throws Exception {
        ProjectApproval projectApproval = projectApprovalMapper.selectById(id);
        ProjectApprovalVO result = BeanCopyUtils.convertTo(projectApproval, ProjectApprovalVO::new);
        if (result != null) {
            setEveryName(Arrays.asList(result));
            if(StrUtil.isNotBlank(result.getRequireReviewId())){
             RequireReviewForm requireReviewForm =  requireReviewFormService.getById(result.getRequireReviewId());
             if(ObjectUtil.isNotEmpty(requireReviewForm)){
                 result.setRequireReviewName(requireReviewForm.getRequireReviewLogo());
             }
            }
            List<FileDTO> getFilesByDataIdResponse =  fileBo.listMaxNewFile(id, FileConstant.FILETYPE_PROJECTAPPROVAL_FILE);
            if (Objects.nonNull(getFilesByDataIdResponse) && !org.springframework.util.CollectionUtils.isEmpty(getFilesByDataIdResponse)) {
                result.setAttachments(getFilesByDataIdResponse);
            }
        }

        return result;
    }

    /**
     * 新增
     * <p>
     * * @param projectApprovalDTO
     */
    @Override
    public ProjectApprovalVO create(ProjectApprovalDTO projectApprovalDTO) throws Exception {
        String code = "";
        if (StrUtil.isNotBlank(projectApprovalDTO.getNumber())) {
            code = codeBo.createCode(ClassNameConstant.PROJECT_APPROVAL, ClassNameConstant.NUMBER, true, projectApprovalDTO.getNumber());
        } else {
            code = codeBo.createCode(ClassNameConstant.PROJECT_APPROVAL, ClassNameConstant.NUMBER, false, null);
        }
        projectApprovalDTO.setNumber(code);
        ProjectApproval projectApproval = BeanCopyUtils.convertTo(projectApprovalDTO, ProjectApproval::new);
        int insert = projectApprovalMapper.insert(projectApproval);
        ProjectApprovalVO rsp = BeanCopyUtils.convertTo(projectApproval, ProjectApprovalVO::new);
        // 调用res服务保存附件信息
        List<FileDTO> attachments = Optional.ofNullable(projectApprovalDTO.getAttachments()).orElse(new ArrayList<>());
        attachments.forEach(f -> {
            f.setDataId(rsp.getId());
            f.setDataType(FileConstant.FILETYPE_PROJECTAPPROVAL_FILE);
        });
        if (CollectionUtil.isNotEmpty(attachments)) {
//            List<FileDTO> fileDTOS = BeanCopyUtils.convertListTo(attachments, FileDTO::new);
            fileBo.addBatch(attachments);
        }
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param projectApprovalDTO
     */
    @Override
    @Transactional
    public Boolean edit(ProjectApprovalDTO projectApprovalDTO) throws Exception {
        ProjectApproval projectApproval = BeanCopyUtils.convertTo(projectApprovalDTO, ProjectApproval::new);
        int update = projectApprovalMapper.updateById(projectApproval);
        List<FileDTO> attachments = Optional.ofNullable(projectApprovalDTO.getAttachments()).orElse(new ArrayList<>());
        List<FileDTO> oldFilesResult = fileBo.listMaxNewFile(projectApprovalDTO.getId(), FileConstant.FILETYPE_PROJECTAPPROVAL_FILE);
        if (BeanUtil.isNotEmpty(oldFilesResult) && CollectionUtil.isNotEmpty(oldFilesResult)) {
            List<String> oldFileIds = oldFilesResult.stream()
                    .map(FileDTO::getId)
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(oldFileIds)) {
                fileBo.deleteFileByIds(oldFileIds,attachments);
            }
        }
        attachments.forEach(f -> {
            f.setDataId(projectApprovalDTO.getId());
            f.setDataType(FileConstant.FILETYPE_PROJECTAPPROVAL_FILE);
        });
        fileBo.addBatch(attachments);
        return SqlHelper.retBool(update);
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        List<ProjectApproval> projectApprovalList = projectApprovalMapper.selectList(ProjectApproval::getId, ids);
        if (CollectionUtils.isBlank(projectApprovalList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "项目立项记录不存在或被删除!");
        }
        if (projectApprovalList.stream().filter(item -> !item.getStatus().equals(ProjectApprovalStatusEnum.CREATED.getStatus())).findAny().isPresent()) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "项目立项记录当前状态不能删除!");
        }

        int delete = projectApprovalMapper.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectApprovalVO> pages(Page<ProjectApprovalDTO> pageRequest) throws Exception {
        Page<ProjectApproval> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectApproval::new));
        realPageRequest.setSearchConditions(pageRequest.getSearchConditions());
        LambdaQueryWrapperX<ProjectApproval> objectLambdaQueryWrapperX = new LambdaQueryWrapperX<>(ProjectApproval.class);
        if (!org.springframework.util.CollectionUtils.isEmpty(realPageRequest.getSearchConditions())) {
            objectLambdaQueryWrapperX = SearchConditionUtils.parseSearchConditionsWrapper(realPageRequest.getSearchConditions(), objectLambdaQueryWrapperX);
        }
        objectLambdaQueryWrapperX.orderByDesc(ProjectApproval::getCreateTime);
        PageResult<ProjectApproval> page = projectApprovalMapper.selectPage(realPageRequest, objectLambdaQueryWrapperX);
        Page<ProjectApprovalVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectApprovalVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectApprovalVO::new);
        if (!CollectionUtils.isBlank(vos)) {
            setEveryName(vos);
            pageResult.setContent(vos);
        }
        return pageResult;
    }

    private void setEveryName(List<ProjectApprovalVO> vos) throws Exception {
        Map<String,String> sourceMap =  dictBo.getDictValue(ApprovalDict.APPROVAL_SOURCE);
        Map<String,String> typeMap = dictBo.getDictValue(ApprovalDict.APPROVAL_TYPE);
        vos.forEach(vo -> {
            if(StrUtil.isNotBlank(typeMap.get(vo.getType()))) {
                vo.setTypeName(typeMap.get(vo.getType()));
            }
            if(StrUtil.isNotBlank(sourceMap.get(vo.getSource()))) {
                vo.setSourceName(sourceMap.get(vo.getSource()));
            }
            if(StrUtil.isNotBlank(vo.getCreatorId())&&ObjectUtil.isNotEmpty(userRedisHelper.getUserById(vo.getCreatorId()))) {
                UserVO userVO = userRedisHelper.getUserById(vo.getCreatorId());
                vo.setCreatorName(userVO.getName());
            }
            if(StrUtil.isNotBlank(vo.getRspUser())&&ObjectUtil.isNotEmpty(userRedisHelper.getUserById(vo.getRspUser()))) {
                UserVO userVO = userRedisHelper.getUserById(vo.getRspUser());
                List<DeptVO> deptVOS = userVO.getOrganizations();
                vo.setRspUserName(userVO.getName());
                if(CollUtil.isNotEmpty(deptVOS)) {
                    vo.setRspDeptName(deptVOS.get(0).getName());
                }
            }
        });

    }


    @Override
    public Page<ProjectVO> projectPages(Page<ProjectDTO> pageRequest) throws Exception {
        List<ProjectApproval> projectApprovalList = this.list();
        List<String> projectIds = projectApprovalList.stream().map(ProjectApproval::getProjectId).collect(Collectors.toList());

        LambdaQueryWrapperX<Project> projectOrionWrapper = new LambdaQueryWrapperX<>();
        // projectOrionWrapper.eq(Project :: getStatus, NewProjectStatusEnum.PROJECT_CREATED.getStatus());
        if (!CollectionUtils.isBlank(projectIds)) {
            projectOrionWrapper.notIn(Project::getId, projectIds);
        }

        Page<Project> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), Project::new));
        realPageRequest.setSearchConditions(pageRequest.getSearchConditions());
        projectOrionWrapper = SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), projectOrionWrapper);
        PageResult<Project> page = projectRepository.selectPage(realPageRequest, projectOrionWrapper);

        Page<ProjectVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectVO::new);
        List<ProjectDeclare> list = projectDeclareService.list();
        Map<String, BigDecimal> map = list.stream().filter(item -> ObjectUtil.isNotEmpty(item.getEstimateAmt())).collect(Collectors.toMap(ProjectDeclare::getProjectId, ProjectDeclare::getEstimateAmt));
        for (ProjectVO projectVO : vos) {
            projectVO.setEstimateMoney(map.get(projectVO.getId()));
        }
        pageResult.setContent(vos);
        return pageResult;
    }

    @Override
    public ProjectApprovalVO getProjectApproval(String id) throws Exception {
        LambdaQueryWrapperX<ProjectApproval> lambdaQueryWrapperX = new LambdaQueryWrapperX();
        lambdaQueryWrapperX.eq(ProjectApproval::getProjectId, id);
        ProjectApproval projectApproval = this.getOne(lambdaQueryWrapperX);
        ProjectApprovalVO result = BeanCopyUtils.convertTo(projectApproval, ProjectApprovalVO::new);
        if (result != null) {
            setEveryName(Arrays.asList(result));
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectApprovalVO completeProjectApproval(String id) throws Exception {
        ProjectApproval projectApproval = this.getById(id);
        if (projectApproval.getStatus() != 101) {
            throw new PMSException(PMSErrorCode.KMS_DATA_QUOTE_STATUS, "已在流程中");
        }
        projectApproval.setStatus(130);
        this.updateById(projectApproval);
        projectApprovalMilestoneService.syncProjectScheme(projectApproval.getId());
        String projectId = projectApproval.getProjectId();
        Project project = projectService.getById(projectId);
        project.setProjectApproveTime(new Date());
        projectService.updateById(project);
        ProjectApprovalVO result = BeanCopyUtils.convertTo(projectApproval, ProjectApprovalVO::new);
        return result;
    }

    @Override
    public ProjectVO getProjectById(String id) throws Exception {
        Project project = projectService.getById(id);
        ProjectVO projectVO = BeanCopyUtils.convertTo(project, ProjectVO::new);
        LambdaQueryWrapperX<ProjectDeclare> lambdaQueryWrapperX = new LambdaQueryWrapperX();
        lambdaQueryWrapperX.eq(ProjectDeclare::getProjectId, id);
        ProjectDeclare projectDeclare = projectDeclareService.getOne(lambdaQueryWrapperX);
        if (ObjectUtil.isNotEmpty(projectDeclare)) {
            projectVO.setEstimateMoney(projectDeclare.getEstimateAmt());
        }
        return projectVO;
    }

    @Override
    public Page<ProjectApprovalVO> userPages(Page<ProjectApprovalDTO> pageRequest) throws Exception {
        String userId = currentUserHelper.getUserId();
        Page<ProjectApproval> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectApproval::new));
        realPageRequest.setSearchConditions(pageRequest.getSearchConditions());
        LambdaQueryWrapperX<ProjectApproval> objectLambdaQueryWrapperX = new LambdaQueryWrapperX<>(ProjectApproval.class);
        if (!org.springframework.util.CollectionUtils.isEmpty(realPageRequest.getSearchConditions())) {
            objectLambdaQueryWrapperX = SearchConditionUtils.parseSearchConditionsWrapper(realPageRequest.getSearchConditions(), objectLambdaQueryWrapperX);
        }
        objectLambdaQueryWrapperX.eq(ProjectApproval::getCreatorId,userId);

        PageResult<ProjectApproval> page = projectApprovalMapper.selectPage(realPageRequest, objectLambdaQueryWrapperX);
        Page<ProjectApprovalVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectApprovalVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectApprovalVO::new);
        if (!CollectionUtils.isBlank(vos)) {
            setEveryName(vos);
            pageResult.setContent(vos);
        }
        pageResult.setContent(vos);
        return pageResult;
    }

    @Override
    public List<ProjectApproval> getProjectApprovalByProjectIds(List<String> projectIdList) throws Exception {
        return this.list(new LambdaQueryWrapperX<>(ProjectApproval.class).select(ProjectApproval::getProjectId, ProjectApproval::getId)
                .in(ProjectApproval::getProjectId, projectIdList));
    }

    @Override
    public Boolean relevanceProject(String approvalId, String projectId) throws Exception {
        if (StrUtil.isBlank(approvalId)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_ID_NULL,"立项id不能为空");
        }
        if (StrUtil.isBlank(projectId)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_ID_NULL,"项目id不能为空");
        }
        ProjectApproval byId = this.getById(approvalId);
        byId.setProjectId(projectId);

        LambdaQueryWrapperX<ProjectApprovalProduct> wrapperX = new LambdaQueryWrapperX<>(ProjectApprovalProduct.class);
        wrapperX.eq(ProjectApprovalProduct::getApprovalId,approvalId);
        List<ProjectApprovalProduct> products = productService.list(wrapperX);

        removeOldProjectToApproval(projectId);

        saveProjectToProduct(projectId, products);

        return this.updateById(byId);
    }

    private void removeOldProjectToApproval(String projectId) {
        LambdaUpdateWrapper<ProjectApproval> wrapper = new LambdaUpdateWrapper<>(ProjectApproval.class);
        wrapper.set(ProjectApproval::getProjectId,null)
                .eq(ProjectApproval::getProjectId,projectId);
        this.update(wrapper);
    }

    private void saveProjectToProduct(String projectId, List<ProjectApprovalProduct> products) throws Exception {
        LambdaQueryWrapperX<ProjectToProduct> wrapperX = new LambdaQueryWrapperX<>(ProjectToProduct.class);
        wrapperX.select(ProjectToProduct::getId);
        wrapperX.eq(ProjectToProduct::getProjectId,projectId);
        List<ProjectToProduct> toProductList = projectToProductService.list(wrapperX);
        if (CollectionUtil.isNotEmpty(toProductList)){
            // 删除之前的关联关系
            projectToProductService.removeBatchByIds(toProductList);
        }

        if (CollectionUtil.isNotEmpty(products)){
            List<ProductEstimateMaterialDTO> productEstimateMaterialDTOS = BeanCopyUtils.convertListTo(products, ProductEstimateMaterialDTO::new);
            List<String> ids = productApiService.addProducts(productEstimateMaterialDTOS);
            List<ProjectToProduct> toProducts = new ArrayList<>();
            for (String id : ids) {
                ProjectToProduct projectToProduct = new ProjectToProduct();
                projectToProduct.setProjectId(projectId);
                projectToProduct.setProductId(id);
                toProducts.add(projectToProduct);
            }
            projectToProductService.saveBatch(toProducts);
        }
    }


    @Override
    public Page<ProjectApprovalVO> notRelevance(Page<ProjectApprovalDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<ProjectApproval> condition = new LambdaQueryWrapperX<>(ProjectApproval.class);
        if (!org.springframework.util.CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        ProjectApprovalDTO query = pageRequest.getQuery();
        if (ObjectUtil.isNotNull(query) && StrUtil.isNotBlank(query.getName())){
            condition.like(ProjectApproval::getName, query.getName());
        }
        condition.isNull(ProjectApproval::getProjectId);
        condition.orderByDesc(ProjectApproval::getCreateTime);
        Page<ProjectApproval> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectApproval::new));

        PageResult<ProjectApproval> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectApprovalVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectApprovalVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectApprovalVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);
        return pageResult;
    }


}
