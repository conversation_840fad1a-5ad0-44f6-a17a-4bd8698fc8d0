package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * NcfFormpurchaseRequestDetail VO对象
 *
 * <AUTHOR>
 * @since 2024-06-04 10:01:14
 */
@ApiModel(value = "NcfFormpurchaseRequestDetailVO对象", description = "采购申请行项目表")
@Data
public class NcfFormpurchaseRequestDetailVO extends ObjectVO implements Serializable {

    /**
     * 行项目id
     */
    @ApiModelProperty(value = "行项目id")
    private String projectId;


    /**
     * 内部订单
     */
    @ApiModelProperty(value = "内部订单")
    private String internalOrder;


    /**
     * 物料
     */
    @ApiModelProperty(value = "物料")
    private String item;


    /**
     * 物料组
     */
    @ApiModelProperty(value = "物料组")
    private String itemGroup;


    /**
     * 总账科目
     */
    @ApiModelProperty(value = "总账科目")
    private String generalLedgerSubject;


    /**
     * 资产
     */
    @ApiModelProperty(value = "资产")
    private String asset;


    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    private String requiredQuantity;


    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;


    /**
     * 交货时间
     */
    @ApiModelProperty(value = "交货时间")
    private Date deliveryTime;


    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    private String unitPrice;


    /**
     * 总价
     */
    @ApiModelProperty(value = "总价")
    private String totalPrice;


    /**
     * 本位币金额
     */
    @ApiModelProperty(value = "本位币金额")
    private BigDecimal localCurrencyAmount;


    /**
     * 成本中心
     */
    @ApiModelProperty(value = "成本中心")
    private String costCenter;


    /**
     * 项目编号/名称
     */
    @ApiModelProperty(value = "项目编号/名称")
    private String projectIdName;


    /**
     * WBS编号
     */
    @ApiModelProperty(value = "WBS编号")
    private String wbsId;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    private String mainTableId;

    /**
     * 采购申请号
     */
    @ApiModelProperty(value = "采购申请号")
    private String projectCode;


}
