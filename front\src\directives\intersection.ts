// directives/intersection.ts
import {
  App, Directive, DirectiveBinding, ref,
} from 'vue';

const observer = ref();

const intersectionDirective: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    observer.value = new IntersectionObserver((entries) => {
      if (binding.arg) {
        if (entries[0].isIntersecting) {
          binding.value(binding.arg);
        } else {
          observer.value.unobserve(el);
        }
      } else {
        binding.value(entries[0].isIntersecting);
      }
    });

    observer.value.observe(el);
  },
  unmounted(el: HTMLElement) {
    observer.value.unobserve(el);
  },
};

export function setupIntersectionDirective(app: App) {
  app.directive('intersection', intersectionDirective);
}

export default intersectionDirective;
