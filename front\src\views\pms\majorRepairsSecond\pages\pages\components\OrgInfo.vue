<script setup lang="ts">
import { BasicForm, FormSchema, useForm } from 'lyra-component-vue3';
import {
  inject, nextTick, ref, watch, watchEffect,
} from 'vue';
import Api from '/@/api';

const props = defineProps<{
  isEdit: boolean
}>();

const detailsData: Record<string, any> = inject('detailsData', {});

const schemas: FormSchema[] = [
  {
    field: 'levelTypeName',
    component: 'Input',
    label: '组织类型',
    rules: [{ required: true }],
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'name',
    component: 'Input',
    label: detailsData?.levelType === 'executionSpecialty' ? '名称' : '班组名称',
    rules: [{ required: true }],
  },
  {
    field: 'rspUserId',
    component: 'SelectUser',
    label: detailsData?.levelType === 'executionSpecialty' ? '负责人' : '班组负责人',
    rules: [
      {
        required: true,
        type: 'array',
      },
    ],
  },
  {
    field: 'beginTime',
    component: 'DatePicker',
    label: '计划开始日期',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'endTime',
    component: 'DatePicker',
    label: '计划结束日期',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'actualBeginTime',
    component: 'DatePicker',
    label: '实际开始日期',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'actualEndTime',
    component: 'DatePicker',
    label: '实际完成日期',
    componentProps: {
      disabled: true,
    },
  },
];

const [register, { validate, setFieldsValue, updateSchema }] = useForm({
  layout: 'vertical',
  isSpacing: false,
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 8,
  },
  schemas,
});

watch(() => props.isEdit, (value) => {
  nextTick(async () => {
    await updateSchema([
      {
        field: 'name',
        componentProps: {
          disabled: !value,
          maxlength: 100,
        },
      },
      {
        field: 'rspUserId',
        componentProps: {
          disabled: !value,
          selectUserModalProps: {
            isRequired: true,
            selectType: 'radio',
          },
        },
      },
    ]);
    setTimeout(() => {
      updateFormValues();
    });
  });
}, {
  immediate: true,
});

watch(() => detailsData, () => {
  updateFormValues();
}, {
  deep: true,
});

function updateFormValues() {
  if (setFieldsValue) {
    nextTick(() => {
      const formValues = {
        ...detailsData,
        rspUserId: detailsData?.rspUserName ? [
          {
            id: detailsData?.rspUserId,
            name: detailsData?.rspUserName,
            code: detailsData?.rspUserCode,
          },
        ] : [],
      };
      setFieldsValue(formValues);
    });
  }
}

const fetchingRef = ref<boolean>(false);

defineExpose({
  async submit() {
    const formValues = await validate();
    fetchingRef.value = true;
    return new Promise((resolve, reject) => {
      new Api('/pms/majorRepairOrg/edit').fetch({
        id: detailsData?.id,
        name: formValues.name,
        rspUserCode: formValues.rspUserId[0].code,
        rspUserId: formValues.rspUserId[0].id,
        rspUserName: formValues.rspUserId[0].name,
      }, '', 'PUT').then(() => {
        resolve('');
      }).catch((e) => {
        reject(e);
      })
        .finally(() => {
          fetchingRef.value = false;
        });
    });
  },
});
</script>

<template>
  <BasicForm
    v-loading="fetchingRef"
    style="position: relative"
    @register="register"
  />
</template>

<style scoped lang="less">

</style>
