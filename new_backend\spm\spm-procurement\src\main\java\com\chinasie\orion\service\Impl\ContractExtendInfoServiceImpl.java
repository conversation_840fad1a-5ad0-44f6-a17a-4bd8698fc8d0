package com.chinasie.orion.service.Impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.domain.dto.ContractExtendInfoDTO;
import com.chinasie.orion.domain.entity.ContractExtendInfo;
import com.chinasie.orion.domain.vo.ContractExtendInfoVO;
import com.chinasie.orion.repository.ContractExtendInfoMapper;
import com.chinasie.orion.service.ContractExtendInfoService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <p>
 * ContractExtendInfo 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@Service
@Slf4j
public class ContractExtendInfoServiceImpl extends OrionBaseServiceImpl<ContractExtendInfoMapper, ContractExtendInfo> implements ContractExtendInfoService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ContractExtendInfoVO detail(String id, String pageCode) throws Exception {
        ContractExtendInfo contractExtendInfo = this.getById(id);
        ContractExtendInfoVO result = BeanCopyUtils.convertTo(contractExtendInfo, ContractExtendInfoVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    @Override
    public ContractExtendInfoVO getExtendInfo(String code) throws Exception {
        LambdaQueryWrapperX<ContractExtendInfo> condition = new LambdaQueryWrapperX<>(ContractExtendInfo.class);
        condition.eq(ContractExtendInfo::getContractNumber, code);
        List<ContractExtendInfo> list = this.list(condition);
        return CollectionUtils.isEmpty(list) ? new ContractExtendInfoVO() : BeanCopyUtils.convertTo(list.get(0), ContractExtendInfoVO::new);

    }


    /**
     * 新增
     * <p>
     * * @param contractExtendInfoDTO
     */
    @Override
    public String create(ContractExtendInfoDTO contractExtendInfoDTO) throws Exception {
        ContractExtendInfo contractExtendInfo = BeanCopyUtils.convertTo(contractExtendInfoDTO, ContractExtendInfo::new);
        this.save(contractExtendInfo);

        String rsp = contractExtendInfo.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param contractExtendInfoDTO
     */
    @Override
    public Boolean edit(ContractExtendInfoDTO contractExtendInfoDTO) throws Exception {
        ContractExtendInfo contractExtendInfo = BeanCopyUtils.convertTo(contractExtendInfoDTO, ContractExtendInfo::new);

        this.updateById(contractExtendInfo);

        String rsp = contractExtendInfo.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ContractExtendInfoVO> pages(String mainTableId, Page<ContractExtendInfoDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ContractExtendInfo> condition = new LambdaQueryWrapperX<>(ContractExtendInfo.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ContractExtendInfo::getCreateTime);

        condition.eq(ContractExtendInfo::getMainTableId, mainTableId);

        Page<ContractExtendInfo> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ContractExtendInfo::new));

        PageResult<ContractExtendInfo> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ContractExtendInfoVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ContractExtendInfoVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ContractExtendInfoVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ContractExtendInfoVO> getByCode(String contractNumber, Page<ContractExtendInfoDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<ContractExtendInfo> condition = new LambdaQueryWrapperX<>(ContractExtendInfo.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ContractExtendInfo::getCreateTime);
        condition.eq(ContractExtendInfo::getContractNumber, contractNumber);
        Page<ContractExtendInfo> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ContractExtendInfo::new));

        PageResult<ContractExtendInfo> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ContractExtendInfoVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ContractExtendInfoVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ContractExtendInfoVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "合同拓展信息导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractExtendInfoDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ContractExtendInfoExcelListener excelReadListener = new ContractExtendInfoExcelListener();
        EasyExcel.read(inputStream, ContractExtendInfoDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ContractExtendInfoDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("合同拓展信息导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ContractExtendInfo> contractExtendInfoes = BeanCopyUtils.convertListTo(dtoS, ContractExtendInfo::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("ncf::ContractExtendInfo-import::id", importId, contractExtendInfoes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ContractExtendInfo> contractExtendInfoes = (List<ContractExtendInfo>) orionJ2CacheService.get("ncf::ContractExtendInfo-import::id", importId);
        log.info("合同拓展信息导入的入库数据={}", JSONUtil.toJsonStr(contractExtendInfoes));

        this.saveBatch(contractExtendInfoes);
        orionJ2CacheService.delete("ncf::ContractExtendInfo-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("ncf::ContractExtendInfo-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ContractExtendInfo> condition = new LambdaQueryWrapperX<>(ContractExtendInfo.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ContractExtendInfo::getCreateTime);
        List<ContractExtendInfo> contractExtendInfoes = this.list(condition);

        List<ContractExtendInfoDTO> dtos = BeanCopyUtils.convertListTo(contractExtendInfoes, ContractExtendInfoDTO::new);

        String fileName = "合同拓展信息数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractExtendInfoDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<ContractExtendInfoVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }


    public static class ContractExtendInfoExcelListener extends AnalysisEventListener<ContractExtendInfoDTO> {

        private final List<ContractExtendInfoDTO> data = new ArrayList<>();

        @Override
        public void invoke(ContractExtendInfoDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ContractExtendInfoDTO> getData() {
            return data;
        }
    }


}
