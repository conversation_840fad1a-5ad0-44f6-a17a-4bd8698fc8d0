package com.chinasie.orion.repository;

import com.chinasie.orion.domain.entity.NcfFormPurchOrder;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * <p>
 * NcfFormPurchOrder Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-07 16:28:00
 */
@Mapper
public interface NcfFormPurchOrderMapper extends OrionBaseMapper<NcfFormPurchOrder> {

    /**
     * 查询订单最后一次交货时间与当前系统日期的差值=30天
     * @return 结果
     */
    List<NcfFormPurchOrder> selectByOrderNumber();

}

