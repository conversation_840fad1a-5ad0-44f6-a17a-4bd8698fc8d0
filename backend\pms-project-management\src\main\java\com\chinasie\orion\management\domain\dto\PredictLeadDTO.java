package com.chinasie.orion.management.domain.dto;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * PredictLeadDTO对象
 *
 */
@ApiModel(value = "PredictLeadDTO对象", description = "线索预测收入DTO")
@Data
@ExcelIgnoreUnannotated
public class PredictLeadDTO {
    @ApiModelProperty(value = "线索总数")
    private Integer sum;

    @ApiModelProperty(value = "线索预测收入")
    private BigDecimal account;
}
