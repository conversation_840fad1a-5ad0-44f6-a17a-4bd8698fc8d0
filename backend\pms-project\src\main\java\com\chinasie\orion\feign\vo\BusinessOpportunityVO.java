package com.chinasie.orion.feign.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * BusinessOpportunity VO对象
 *
 * <AUTHOR>
 * @since 2024-03-18 11:14:18
 */
@ApiModel(value = "BusinessOpportunityVO对象", description = "商机管理")
@Data
public class BusinessOpportunityVO extends ObjectVO implements Serializable {

    /**
     * 商机名称
     */
    @ApiModelProperty(value = "商机名称")
    private String name;

    /**
     * 商机阶段组
     */
    @ApiModelProperty(value = "商机阶段组")
    private String stageGroupId;

    /**
     * 商机阶段组名称
     */
    private String stageGroupName;

    /**
     * 结束原因
     */
    @ApiModelProperty(value = "结束原因")
    private String endingDesc;

    /**
     * 当前商机阶段
     */
    @ApiModelProperty(value = "当前商机阶段")
    private String recentlyStage;

    @ApiModelProperty(value = "线索Id")
    private String leadManagementId;

    @ApiModelProperty(value = "线索名称")
    private String leadManagementName;


}
