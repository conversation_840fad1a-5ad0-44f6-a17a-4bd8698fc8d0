<template>
  <div>
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <BasicButton
          icon="sie-icon-daochu"
          type="primary"
          @click="exportTable"
        >
          导出全部
        </BasicButton>
      </template>
    </OrionTable>
  </div>
</template>

<script lang="ts" setup>
import {
  BasicButton, downloadByData, Icon, IOrionTableOptions, OrionTable,
} from 'lyra-component-vue3';

import { Modal, Tooltip } from 'ant-design-vue';
import Api from '/@/api';
import { h, unref, ref } from 'vue';
import dayjs from 'dayjs';
const loadStatus = ref(false);
const props = withDefaults(defineProps<{
  expenseSubjectId:string,
  projectId:string,
  expenseAccountId:string
}>(), {
  expenseSubjectId: '',
  expenseAccountId: '',
});
const tableRef = ref();
const tableOptions:IOrionTableOptions | any = {
  showTableSetting: false,
  showToolButton: false,
  showSmallSearch: false,
  api(params) {
    return new Api('/pms/budgetExpendStatistics/page').fetch({
      ...params,
      query: {
        expenseAccountId: props.expenseSubjectId,
        projectId: props.projectId,
      },
    }, '', 'POST');
  },
  columns: [
    {
      title: '成本支出编码',
      dataIndex: 'expendNumber',
    },
    {
      title: '成本中心',
      dataIndex: 'costCenterName',
    },
    {
      title: '科目',
      dataIndex: 'expenseSubjectName',
    },
    {
      title: '发生人',
      dataIndex: 'occurrencePersonName',
    },
    {
      title: '发生时间',
      dataIndex: 'occurrenceTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '';
      },
    },
    {
      title: '预算编码',
      dataIndex: 'number',
    },
    {
      title: '预算名称',
      dataIndex: 'name',
    },
    {
      title: '币种',
      dataIndex: 'currencyName',
    },
    {
      title: '成本金额（元）',
      dataIndex: 'expendMoney',
    },
    {
      title: '占用/领用',
      dataIndex: 'occupationReceive',
    },
    {
      title: '描述',
      dataIndex: 'remark',
    },
  ],
};
function exportTable() {
  let isEmpty = tableRef.value.getDataSource().length === 0;
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk: async () => {
      loadStatus.value = true;
      const res = await downloadByData('/pms/budgetExpendStatistics/export/excel', {
        isEmpty,
        expenseSubjectId: props.expenseSubjectId,
        projectId: props.projectId,
        // name: unref(keyword),
        // ...unref(searchRef.value.modal),
      }, '', 'GET', true, false, '导出处理完成，现在开始下载');
      loadStatus.value = false;
    },
  });
}
</script>

<style lang="less" scoped>

</style>
