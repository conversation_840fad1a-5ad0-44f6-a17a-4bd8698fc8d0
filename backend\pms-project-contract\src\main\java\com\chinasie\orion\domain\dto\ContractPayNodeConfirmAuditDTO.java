package com.chinasie.orion.domain.dto;

import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.List;

/**
 * @author: yk
 * @date: 2023/10/27 14:53
 * @description:
 */
@ApiModel(value = "ContractPayNodeConfirmAudit对象", description = "项目合同支付节点确认审核")
@Data
public class ContractPayNodeConfirmAuditDTO extends ObjectDTO implements Serializable {
    @ApiModelProperty("审批意见")
    private String comment;

    /**
     * 节点审核支持性材料上传
     */
    @ApiModelProperty(value = "节点审核支持性材料上传")
    @Valid
    private List<FileInfoDTO> fileInfoDTOList;
}
