package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.excel.annotations.ExcelSelected;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * CertificateInfon DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-05 10:41:49
 */
@ApiModel(value = "CertificateInfonDTO对象", description = "证书信息")
@Data
@ExcelIgnoreUnannotated
public class CertificateInfoDTO extends  ObjectDTO   implements Serializable{

    /**
     * 是否需要复审
     */
    @ApiModelProperty(value = "是否需要复审")
    @ExcelSelected(source = {"是","否"}, firstRow = 1)
    @ExcelProperty(value = "是否需要复审 ", index = 0)
    private String isNeedRenewal;

    /**
     * 复审日期
     */
    @ApiModelProperty(value = "复审日期")
    @ExcelProperty(value = "复审日期 ", index = 1)
    private Date renewalDate;



    /**
     * 获取日期
     */
    @ApiModelProperty(value = "获取日期")
    @ExcelProperty(value = "获取日期 ", index = 2)
    private Date acquisitionDate;

    /**
     * 发证机构
     */
    @ApiModelProperty(value = "发证机构")
    @ExcelProperty(value = "发证机构 ", index = 3)
    private String issuingAuthority;

    /**
     * 等级
     */
    @ApiModelProperty(value = "等级")
    @ExcelProperty(value = "等级 ", index = 4)
    private String level;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @ExcelProperty(value = "名称 ", index = 5)
    private String name;

    /**
     * 证书类型
     */
    @ApiModelProperty(value = "证书类型")
    @ExcelProperty(value = "证书类型 ", index = 6)
    @ExcelSelected(source = {"职业资格","职称"}, firstRow = 1)
    private String certificateType;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    private String mainTableId;


    @ExcelProperty(value = "复审年限 ", index = 7)
    private Integer renewalYearNum;

}
