package com.chinasie.orion.service;
import com.chinasie.orion.domain.dto.tree.MajorRepairOrgEditDTO;
import com.chinasie.orion.domain.dto.tree.MajorTreeJobParamDTO;
import com.chinasie.orion.domain.dto.JobProgressDTO;
import com.chinasie.orion.domain.dto.tree.MajorTreeParamDTO;
import com.chinasie.orion.domain.entity.MajorRepairOrg;
import com.chinasie.orion.domain.dto.MajorRepairOrgDTO;
import com.chinasie.orion.domain.vo.MajorRepairOrgVO;
import java.lang.String;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.chinasie.orion.domain.vo.NodeVO;
import com.chinasie.orion.domain.vo.ObjectTreeInfoVO;
import com.chinasie.orion.domain.vo.TreeNodeVO;
import com.chinasie.orion.domain.vo.tree.MajorTreeVO;
import com.chinasie.orion.sdk.metadata.page.Page;

import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.tree.TreeNode;


/**
 * <p>
 * MajorRepairOrg 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18 09:32:26
 */
public interface MajorRepairOrgService  extends  OrionBaseService<MajorRepairOrg>  {


    /**
     *  详情
     *
     * * @param id
     */
    MajorRepairOrgVO detail(String id,String pageCode)throws Exception;
    /**
     *  新增
     *
     * * @param majorRepairOrgDTO
     */
    String create(MajorRepairOrgDTO majorRepairOrgDTO)throws Exception;
    /**
     *  编辑
     *
     * * @param majorRepairOrgDTO
     */
    Boolean edit(MajorRepairOrgDTO majorRepairOrgDTO)throws Exception;


        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;


        /**
         *  分页
         *
         * * @param pageRequest
         * 
         */
        Page<MajorRepairOrgVO> pages( Page<MajorRepairOrgDTO> pageRequest)throws Exception;



        /**
            *  设置各种名称
            *
            * * @param searchConditions
            * * @param response
            */
        void  setEveryName(List<MajorRepairOrgVO> vos)throws Exception;


        List<MajorRepairOrg> getList(String repairRound ,String parentId);

    ObjectTreeInfoVO<TreeNodeVO<NodeVO<MajorTreeVO>>> majorTree(MajorTreeParamDTO majorTreeParamDTO);

    /**
     * 新增进展
     * @param jobProgressDTO 参数
     */
    void addOrgProgress(JobProgressDTO jobProgressDTO);

    /**
     * 修改进展
     * @param jobProgressDTO 参数
     */
    void editOrgProgress(JobProgressDTO jobProgressDTO);

    /**
     * 删除
     * @param ids 参数
     * @return 结果
     */
    Boolean deleteProgress(List<String> ids);


    ObjectTreeInfoVO<TreeNodeVO<NodeVO<MajorTreeVO>>> majorTreeByMajorRepairOrgIds(MajorTreeJobParamDTO majorTreeParamDTO);

    List<MajorRepairOrg> listByLevel(String speciality,int i, String repairRound);

    List<String> jobMajorList(MajorTreeJobParamDTO majorTreeParamDTO);

    /**
     *  编辑数据通过ID
     * @param majorRepairOrgDTO
     * @return
     */
    Boolean editById(MajorRepairOrgEditDTO majorRepairOrgDTO);
    /**
     *
     * 获取当前人拥有的权限
     * @return
     */
    Map<String, Set<String>> getRoleByOrgIdCurrentUser(String repairRound);

    void updateByOrgId(String orgId,String repairRound);

    /**
     *
     * @param orgId
     * @return
     */
    MajorRepairOrg getSimpleEntityByOrgId(String orgId);
}
