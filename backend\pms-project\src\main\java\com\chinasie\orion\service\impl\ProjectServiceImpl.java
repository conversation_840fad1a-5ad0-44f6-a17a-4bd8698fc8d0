package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinasie.orion.base.api.domain.entity.DeptDO;
import com.chinasie.orion.base.api.domain.entity.DeptLeaderDO;
import com.chinasie.orion.base.api.domain.entity.UserDO;
import com.chinasie.orion.base.api.domain.vo.SimpleUserVO;
import com.chinasie.orion.base.api.repository.DeptDOMapper;
import com.chinasie.orion.base.api.repository.DeptLeaderDORepository;
import com.chinasie.orion.base.api.repository.DeptUserDOMapper;
import com.chinasie.orion.base.api.repository.UserDOMapper;
import com.chinasie.orion.base.api.service.UserBaseApiService;
import com.chinasie.orion.bo.*;
import com.chinasie.orion.constant.*;
import com.chinasie.orion.conts.ProjectSourceTypeEnum;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.dto.document.DocumentDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalEstimate;
import com.chinasie.orion.domain.query.ProjectAndUserTrdProjectQuery;
import com.chinasie.orion.domain.query.ProjectAndUserTrdUserQuery;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.domain.vo.performance.ProjectByUserVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.PMIService;
import com.chinasie.orion.feign.PasFeignService;
import com.chinasie.orion.feign.PlanFeignService;
import com.chinasie.orion.feign.vo.BusinessOpportunityVO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.number.api.domain.GenerateNumberRequest;
import com.chinasie.orion.number.api.sdk.NumberApiService;
import com.chinasie.orion.page.ConditionItem;
import com.chinasie.orion.page.PageRequest;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.pas.api.service.ProjectPeopleDayBasicDataApiService;
import com.chinasie.orion.permission.core.annotation.OrionDataPermission;
import com.chinasie.orion.repository.*;
import com.chinasie.orion.sdk.domain.vo.business.*;
import com.chinasie.orion.sdk.domain.vo.org.SimpleDeptVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.enums.TenantLineEnum;
import com.chinasie.orion.sdk.helper.*;
import com.chinasie.orion.service.*;
import com.chinasie.orion.service.approval.ProjectApprovalEstimateService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.PmsAuthUtil;
import com.chinasie.orion.util.ResponseUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/06/10:37
 * @description:
 */
@Slf4j
@Service
public class ProjectServiceImpl extends OrionBaseServiceImpl<ProjectRepository, Project> implements ProjectService {
    public static final String DICT_PROJECT_TYPE = "pms_project_type";

    private final String CACHE_PROJECT_KEY = "pms::project::%s";

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private ProjectService projectService;

    @Resource
    private UserBo userBo;

    @Resource
    private DictBo dictBo;

    @Autowired
    private PmsAuthUtil pmsAuthUtil;

    private RedisTemplate redisTemplate;

    @Autowired
    private UserDOMapper userDOMapper;
    @Autowired
    private DataStatusNBO dataStatusNBO;

    @Autowired
    public void setRedisTemplate(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }


    @Resource
    private DocumentTypeService documentTypeService;

    @Resource
    private WarningSettingService warningSettingService;

    @Resource
    private DictRedisHelper dictRedisHelper;

    @Resource
    private CurrentUserHelper currentUserHelper;

    @Resource
    private ProjectRoleService projectRoleService;
    @Lazy
    @Resource
    private ProjectRoleUserService projectRoleUserService;

    @Resource
    private DocumentBo documentBo;

    @Resource
    private ComponentBo componentBo;

    @Resource
    private ClassRedisHelper classRedisHelper;

    @Resource
    private DeptLeaderDORepository deptLeaderDORepository;

    @Resource
    private DeptDOMapper deptDOMapper;


    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Autowired
    private UserBaseApiService userBaseApiService;

    @Autowired
    private UserLikeProjectService userLikeProjectService;

    @Resource
    private WarningToRoleService warningToRoleService;

    @Autowired
    private ProjectDeclareRepository projectDeclareRepository;

    @Autowired
    private ProjectDeclareFileInfoService projectDeclareFileInfoService;

    @Autowired
    private ProjectToBasePlanService newProjectToBasePlanService;

    @Autowired
    private ProjectDeclareService projectDeclareService;


    @Autowired
    private ProjectSchemeService projectSchemeService;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private ProjectToBasePlanService projectToBasePlanService;

    @Autowired
    private ProjectSetUpService projectSetUpService;

    @Autowired
    private ProjectPlaceService projectPlaceService;

    @Autowired
    private ProjectFinanceIndexService projectFinanceIndexService;

    @Autowired
    private PMIService pmiService;

    @Autowired
    private ProjectToBasePlanRepository projectToBasePlanRepository;

    @Resource
    private ProjectCollectionToProjectService projectCollectionToProjectService;

    @Autowired
    private PlanFeignService planFeignService;

    @Autowired
    private PasFeignService pasFeignService;

    @Autowired
    private ScientificResearchDemandDeclareMapper scientificResearchDemandDeclareMapper;

    @Autowired
    private ProjectApprovalService projectApprovalService;

    @Autowired
    private ProjectApprovalEstimateService projectApprovalEstimateService;

    @Autowired
    private ProjectPeopleDayBasicDataApiService projectPeopleDayBasicDataApiService;

    @Autowired
    private NumberApiService numberApiService;

    @Resource
    private ProjectRoleUserRepository projectRoleUserRepository;

    @Autowired
    private ThreadPoolTaskExecutor projectPool;

    @Resource
    private ProjectInitiationApiService projectInitiationApiService;
    @Resource
    private ProjectMapper projectMapper;
    @Autowired
    private DeptUserDOMapper deptUserDOMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveProject(ProjectDTO projectDTO) throws Exception {
        String projectType = projectDTO.getProjectType();
        if (StrUtil.isBlank(projectType)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "项目类型不能为空");
        }

        List<Project> projectDTOList = this.list(new LambdaQueryWrapper<>(Project.class).
                eq(Project::getName, projectDTO.getName()));
        if (!CollectionUtils.isEmpty(projectDTOList)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "名字不能重复");
        }

        GenerateNumberRequest generateNumberRequest = new GenerateNumberRequest();
        generateNumberRequest.setClazzName(ClassNameConstant.PROJECT);
        String number = numberApiService.generate(generateNumberRequest);

        List<Project> projectDTOList2 = this.list(new LambdaQueryWrapper<>(Project.class).
                eq(Project::getNumber, number));
        if (!CollectionUtils.isEmpty(projectDTOList2)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "编号不能重复");
        }

        //todo编码规则
        projectDTO.setNumber(number);
        projectDTO.setSchedule(0d);
        String userId = currentUserHelper.getUserId();
        UserVO userVO = userBo.getUserById(userId);
        projectDTO.setPm(userVO.getName());

        String resPerson = projectDTO.getResPerson();
        if (StringUtils.hasText(resPerson)) {
            SimpleUser resSimpleUser = userRedisHelper.getSimpleUserById(resPerson);
            if (resSimpleUser != null) {
                projectDTO.setResDept(resSimpleUser.getOrgId());
                projectDTO.setResTeamGroup(resSimpleUser.getClassId());
                if (!StringUtils.hasText(resSimpleUser.getDeptId())) {
                    projectDTO.setResAdministrativeOffice(resSimpleUser.getDeptId());
                }
            }
        }
        Project project = BeanCopyUtils.convertTo(projectDTO, Project::new);

        this.save(project);
        //初始化项目经理 和数据创建人所在部门以及向上层级的所有部门的领导（主管/分管/领导组）
        String id = project.getId();
        List<RoleVO> pmsRoleList = projectRoleService.getPmsRoleList(ProjectRoleEnum.ROLE_XMJL.getName(), "");
        RoleVO roleVO = pmsRoleList.get(0);

//        ProjectRole projectRoleDTO = new ProjectRole();
//        projectRoleDTO.setCode(ProjectRoleEnum.ROLE_XMJL.getCode());
//        projectRoleDTO.setName(ProjectRoleEnum.ROLE_XMJL.getName());
//        projectRoleDTO.setNumber(String.format(ProjectRoleEnum.ROLE_XMJL.getNumber(), IdUtil.objectId()));
//        projectRoleDTO.setTakeEffect(TakeEffectEnum.EFFECT.getStatus());
//        if (!ObjectUtils.isEmpty(roleVO)) {
//            projectRoleDTO.setBusinessId(roleVO.getId());
//        }
//        projectRoleDTO.setProjectId(id);
//        projectRoleService.save(projectRoleDTO);
//        String roleId = projectRoleDTO.getId();
//        ProjectRoleUser projectRoleUserDTO = new ProjectRoleUser();
//        projectRoleUserDTO.setProjectId(id);
//        projectRoleUserDTO.setProjectRoleId(roleId);
//        projectRoleUserDTO.setUserId(userVO.getId());
//        projectRoleUserDTO.setName(userVO.getName());
//        projectRoleUserDTO.setNumber(userVO.getCode());
//        projectRoleUserDTO.setDefaultFlag(1);
//        projectRoleUserDTO.setRemark(userVO.getRemark());
//        projectRoleUserService.save(projectRoleUserDTO);
        this.initProjectRoleUser(userVO,id);
//        //初始化任务类型
//        TaskSubject task = new TaskSubject();
//        TaskSubject plan = new TaskSubject();
//        plan.setName("计划");
//        plan.setTakeEffect(TakeEffectEnum.EFFECT.getStatus());
//        plan.setNumber(String.format("RWKM%s", IdUtil.objectId()));
//        plan.setProjectId(id);
//        task.setName("任务");
//        task.setTakeEffect(TakeEffectEnum.EFFECT.getStatus());
//        task.setNumber(String.format("RWKM%s", IdUtil.objectId()));
//        task.setProjectId(id);
//        taskSubjectService.save(plan);
//        taskSubjectService.save(task);
        //初始化文档类型
        List<DictValueVO> dictValueVOList = dictBo.getDictList(DictConstant.DOCUMENT_TYPE);
        List<DocumentType> saveDocumentTypeList = new ArrayList<>();
        dictValueVOList.forEach(o -> {
            DocumentType documentTypeDTO = new DocumentType();
            documentTypeDTO.setProjectId(id);
            documentTypeDTO.setName(o.getValue());
            documentTypeDTO.setNumber(String.format("WDLX%s", IdUtil.objectId()));
            documentTypeDTO.setParentId("0");
            saveDocumentTypeList.add(documentTypeDTO);
        });
        documentTypeService.saveBatch(saveDocumentTypeList);
        List<String> documentTypeIdList = saveDocumentTypeList.stream().map(DocumentType::getId).collect(Collectors.toList());
        List<DocumentType> documentTypeDTOList = documentTypeService.list(new LambdaQueryWrapper<>(DocumentType.class)
                .in(DocumentType::getId, documentTypeIdList.toArray()));
        Map<String, DocumentType> documentTypeDTOMap = documentTypeDTOList.stream().collect(Collectors.toMap(DocumentType::getName, Function.identity()));
        List<DocumentType> updateDocumentTypeList = new ArrayList<>();
        dictValueVOList.forEach(d -> {
            if (documentTypeDTOMap.containsKey(d.getDescription())) {
                DocumentType documentTypeDTO = new DocumentType();
                documentTypeDTO.setId(documentTypeDTOMap.get(d.getValue()).getId());
                documentTypeDTO.setParentId(documentTypeDTOMap.get(d.getDescription()).getId());
                updateDocumentTypeList.add(documentTypeDTO);
            }
        });
        documentTypeService.updateBatchById(updateDocumentTypeList);
        DocumentType documentTypeDTO = documentTypeDTOMap.get(DocumentTypeNameConstant.PROJECT_DATA_AREA);
        DocumentDTO documentDTO = new DocumentDTO();

        documentDTO.setName(documentTypeDTO.getName());
        documentDTO.setNumber(documentTypeDTO.getNumber());
        documentDTO.setClassName(DocumentClassNameConstant.Document_Type_Document);
        String s = documentBo.insertDocument(documentDTO);

        //初始化风险预警设置
        List<DictValueVO> warningTypeDictList = dictBo.getDictList(DictConstant.Warning_Type);
        Map<String, String> warningValueDictMap = dictBo.getDictValueToDesMap(DictConstant.Warning_Setting);
        List<String> warningTypeSubIdList = warningTypeDictList.stream().map(DictValueVO::getSubDictId).collect(Collectors.toList());
        List<WarningSetting> warningSettingDTOList = new ArrayList<>();
        for (String dictId : warningTypeSubIdList) {
            List<DictValueVO> warningSettingDictList = dictBo.getDictList(dictId);
            if (CollectionUtils.isEmpty(warningSettingDictList)) {
                continue;
            }
            warningSettingDictList.forEach(o -> {
                WarningSetting warningSettingDTO = new WarningSetting();
                warningSettingDTO.setProjectId(id);
                warningSettingDTO.setTime("08:00");
                warningSettingDTO.setName(o.getDescription());
                warningSettingDTO.setNumber(String.format("FXYJ%s", IdUtil.objectId()));
                warningSettingDTO.setDayNum(Integer.valueOf(warningValueDictMap.get("提醒天数")));
                warningSettingDTO.setDictValueId(o.getId());
                warningSettingDTO.setWarningType(dictId);
                warningSettingDTO.setFrequency(warningValueDictMap.get("提醒频率"));
                warningSettingDTO.setWarningWay(warningValueDictMap.get("提醒方式"));
                warningSettingDTO.setTakeEffect(TakeEffectEnum.EFFECT.getStatus());
                warningSettingDTO.setRemark(o.getValue());
                warningSettingDTOList.add(warningSettingDTO);
            });
        }
        warningSettingService.saveBatch(warningSettingDTOList);

        LambdaQueryWrapper<WarningSetting> warningSettingWrapper = new LambdaQueryWrapper();
        warningSettingWrapper.eq(WarningSetting::getProjectId, id);
        List<WarningSetting> warningSettingList = warningSettingService.list(warningSettingWrapper);
        if (!CollectionUtils.isEmpty(warningSettingList)) {
            List<WarningToRole> warningToRoles = new ArrayList<>();
            for (WarningSetting warningSetting : warningSettingList) {
                WarningToRole warningToRole = new WarningToRole();
                warningToRole.setFromId(warningSetting.getId());
                warningToRole.setToId(WarningRoleConstant.PRINCIPAL);
                warningToRoles.add(warningToRole);
            }
            warningToRoleService.saveBatch(warningToRoles);
        }

        List<RelevancyPlanDTO> basePlanList = projectDTO.getPlanList();
        // 初始化绑定综合计划
        newProjectToBasePlanService.packageEntityListAndSave(id, number, basePlanList);
        // 初始化项目设置
        Map<String, String> projectSetupMap = ProjectSetUpService.PROJECT_SETUP_MAP;
        List<ProjectSetUp> projectSetUps = new ArrayList<>();
        projectSetupMap.forEach((k, v) -> {
            ProjectSetUp tmp = new ProjectSetUp();
            tmp.setProjectId(id);
            tmp.setKey(k);
            tmp.setValue(v);
            projectSetUps.add(tmp);
        });
        if (!CollectionUtils.isEmpty(projectSetUps)) {
            projectSetUpService.saveBatch(projectSetUps);
        }
        //初始化财务指标
        List<ProjectFinanceIndex> financeIndices = Lists.newArrayList();
        String[] financeNames = new String[] {"设备/软件使用费","日常行政管理费用","其那台项目部成本分摊","内部委托成本","税费"};
        for(String name : financeNames){
            ProjectFinanceIndex projectFinanceIndex = new ProjectFinanceIndex();
            projectFinanceIndex.setProjectId(id);
            projectFinanceIndex.setIndexName(name);
            projectFinanceIndex.setIndexPlan(BigDecimal.ZERO);
            projectFinanceIndex.setIndexCost(BigDecimal.ZERO);
            projectFinanceIndex.setResidue(BigDecimal.ZERO);
            financeIndices.add(projectFinanceIndex);
        }
        projectFinanceIndexService.saveBatch(financeIndices);
        //通用管理：项目人天基础数据新增
//        ProjectPeopleDayBasicDataApiDTO projectPeopleDayBasicDataDTO = new ProjectPeopleDayBasicDataApiDTO();
//        projectPeopleDayBasicDataDTO.setProjectId(id);
//        projectPeopleDayBasicDataDTO.setName(project.getName());
//        projectPeopleDayBasicDataDTO.setNumber(project.getNumber());
//        projectPeopleDayBasicDataApiService.createByProject(projectPeopleDayBasicDataDTO);
        return id;
    }

    public void initProjectRoleUser(UserVO creatorUser,String projectId) throws Exception {

        String userId =  creatorUser.getId();
        SimpleUser creatorUserSim= userRedisHelper.getSimpleUserById(userId);
        String orgId = CurrentUserHelper.getOrgId();

        creatorUserSim.getLeaderList();
        List<DeptVO> deptVOList = creatorUserSim.getAllOrganizationList();
        // 该方法为了满足 新增的时候默认将 领导加入项目角色
        Map<String, SimpleUserVO> userIdToSimUserMap = new HashMap<>();
        List<String> leadUserIdList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(deptVOList)){
            List<String> deptIdList = deptVOList.stream().map(DeptVO::getId).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(deptIdList)){
                LambdaQueryWrapperX<DeptDO> deptwrapper= new LambdaQueryWrapperX<>(DeptDO.class);
                deptwrapper.in(DeptDO::getId,deptIdList);
                deptwrapper.select(DeptDO::getChain,DeptDO::getId);
                List<DeptDO> deptDOS =deptDOMapper.selectList(deptwrapper);
                if(!CollectionUtils.isEmpty(deptDOS)){
                    deptDOS.forEach(item->{
                        String allChain = item.getChain();
                        deptIdList.addAll(Arrays.asList(allChain.split(",")));
                    });

                    List<String> distinctDept =  deptIdList.stream().filter(item-> !StrUtil.equals(item,"0")).distinct().collect(Collectors.toList());
                    // 因 底层未提供批量方法 顾只能 循环从缓存中获取
                    if(!CollectionUtils.isEmpty(distinctDept)){
                        LambdaQueryWrapperX<DeptLeaderDO> deptLeaderDOLambdaQueryWrapperX =new LambdaQueryWrapperX<>(DeptLeaderDO.class);
                        deptLeaderDOLambdaQueryWrapperX.in(DeptLeaderDO::getDeptId,distinctDept);
                        List<DeptLeaderDO> deptLeaderDOList=deptLeaderDORepository.selectList(deptLeaderDOLambdaQueryWrapperX);
                        if(!CollectionUtils.isEmpty(deptLeaderDOList)){
                            leadUserIdList.addAll(deptLeaderDOList.stream().map(DeptLeaderDO::getUserId).distinct().collect(Collectors.toList()));

                        }

                    }
                    if(!CollectionUtils.isEmpty(leadUserIdList)){
                        List<SimpleUserVO> simpleUserVOS = userBaseApiService.getUserByIds(leadUserIdList);
                        if(!CollectionUtils.isEmpty(simpleUserVOS)){
                            for (SimpleUserVO simpleUserVO : simpleUserVOS) {
                                userIdToSimUserMap.put(simpleUserVO.getId(),simpleUserVO);
                            }
                        }
                    }
                }
            }
        }
        List<String> roleCodeList  =new ArrayList<>();
        roleCodeList.add(ProjectRoleEnum.ROLE_XMJL.getCode());
        roleCodeList.add(ProjectRoleEnum.ROLE_PL.getCode());
        List<RoleVO> pmsRoleList = projectRoleService.getPmsRoleListByProjectId(projectId,roleCodeList);
        if(!CollectionUtils.isEmpty(pmsRoleList)){
            List<ProjectRole> projectRoles = new ArrayList<>();
            List<ProjectRoleUser> projectRoleUserList = new ArrayList<>();
            for (RoleVO roleVO : pmsRoleList) {
                ProjectRole projectRoleDTO = new ProjectRole();
                projectRoleDTO.setCode(roleVO.getCode());
                projectRoleDTO.setName(roleVO.getName());
                projectRoleDTO.setNumber(String.format(roleVO.getCode(), IdUtil.objectId()));
                projectRoleDTO.setTakeEffect(TakeEffectEnum.EFFECT.getStatus());
                if (!ObjectUtils.isEmpty(roleVO)) {
                    projectRoleDTO.setBusinessId(roleVO.getId());
                }

                projectRoleDTO.setCreatorId(creatorUser.getId());
                projectRoleDTO.setModifyId(creatorUser.getId());
                projectRoleDTO.setOwnerId(creatorUser.getId());
                projectRoleDTO.setPlatformId(creatorUser.getPlatformId());
                projectRoleDTO.setOrgId(creatorUser.getOrgId());
                projectRoleDTO.setProjectId(projectId);
                // 自动设置ID

                projectRoleDTO.setId(classRedisHelper.getUUID(ProjectRole.class.getSimpleName()));
                projectRoles.add(projectRoleDTO);
                String roleId = projectRoleDTO.getId();
                if(StrUtil.equals(ProjectRoleEnum.ROLE_XMJL.getCode(),roleVO.getCode() )){
                    ProjectRoleUser projectRoleUserDTO = new ProjectRoleUser();
                    projectRoleUserDTO.setProjectId(projectId);
                    projectRoleUserDTO.setProjectRoleId(roleId);
                    projectRoleUserDTO.setUserId(creatorUser.getId());
                    projectRoleUserDTO.setName(creatorUser.getName());
                    projectRoleUserDTO.setNumber(creatorUser.getCode());
                    projectRoleUserDTO.setDefaultFlag(1);
                    projectRoleUserDTO.setRemark(creatorUser.getRemark());
                    projectRoleUserDTO.setCreatorId(creatorUser.getId());
                    projectRoleUserDTO.setModifyId(creatorUser.getId());
                    projectRoleUserDTO.setOwnerId(creatorUser.getId());
                    projectRoleUserDTO.setPlatformId(creatorUser.getPlatformId());
                    projectRoleUserDTO.setOrgId(creatorUser.getOrgId());
                    projectRoleUserList.add(projectRoleUserDTO);
                }else if (StrUtil.equals(ProjectRoleEnum.ROLE_PL.getCode(),roleVO.getCode() )){
                    for (String item : leadUserIdList) {
                        SimpleUserVO simpleUserVO =userIdToSimUserMap.get(item);
                        if(Objects.isNull(simpleUserVO)){
                            continue;
                        }
                        ProjectRoleUser projectRoleUserDTO = new ProjectRoleUser();
                        projectRoleUserDTO.setProjectId(projectId);
                        projectRoleUserDTO.setProjectRoleId(roleId);
                        projectRoleUserDTO.setUserId(item);
                        projectRoleUserDTO.setName(simpleUserVO.getName());
                        projectRoleUserDTO.setNumber(simpleUserVO.getCode());
                        projectRoleUserDTO.setDefaultFlag(1);
                        projectRoleUserDTO.setCreatorId(creatorUser.getId());
                        projectRoleUserDTO.setModifyId(creatorUser.getId());
                        projectRoleUserDTO.setOwnerId(creatorUser.getId());
                        projectRoleUserDTO.setPlatformId(creatorUser.getPlatformId());
                        projectRoleUserDTO.setOrgId(creatorUser.getOrgId());
                        projectRoleUserList.add(projectRoleUserDTO);
                    }
                }
            }
            if(!CollectionUtils.isEmpty(projectRoles)){
                projectRoleService.saveOrUpdateBatch(projectRoles);
            }
            if(!CollectionUtils.isEmpty(projectRoleUserList)){
                projectRoleUserService.saveOrUpdateBatch(projectRoleUserList);
            }

        }
    }


    /**
     *  该方法用于处理 第三方同步过来的所有项目：进行初始化项目领导（有且只执行一次）
     */
    @Override
    @OrionDataPermission(tenantLine = TenantLineEnum.CUSTOM)
    public void initProjectRole(){
        List<Project> projects =   this.list();

        if(!CollectionUtils.isEmpty(projects)){
            Map<String, List<Project>> orgIdToPersonList=  projects.stream().collect(Collectors.groupingBy(ObjectEntity::getOrgId));
            orgIdToPersonList.forEach((orgId, persons) -> {
                List<List<Project>> peopleGroups = splitList(persons, 500);
//              List<Project> peopleGroups =Collections.singletonList(persons.get(0));
//
                log.info("执行到租户【{}】初始化用户信息",orgId);
                int num =0;
                for (List<Project> peopleGroup : peopleGroups) {
                    try {
                        num++;
                        log.info("执行到租户【{}】初始化用户信息第{}轮",orgId,num);
                        this.initProjectPLRoleUser(peopleGroup.stream().collect(Collectors.toMap(Project::getId,Project::getCreatorId)),orgId);
                        log.info("执行到租户【{}】初始化用户信息第{}轮成功",orgId,num);
                    } catch (Exception e) {
                        log.info("执行到租户【{}】初始化用户信息第{}轮失败",orgId,num);
                        throw new RuntimeException(e);
                    }
                }
            });
        }
    }


    private static <T> List<List<T>> splitList(List<T> list, final int length) {
        return IntStream.range(0, (list.size() + length - 1) / length)
                .mapToObj(i -> list.subList(i * length, Math.min(list.size(), (i + 1) * length)))
                .collect(Collectors.toList());
    }

    /**
     *  初始化项目领导
     * @param projectIdToUserIdMap
     * @throws Exception
     */
    public void initProjectPLRoleUser(Map<String,String> projectIdToUserIdMap,String orgId) throws Exception {

//        String userId =  creatorUser.getId();
        Set<String> userIdSet = new HashSet<>();
        for (Map.Entry<String, String> stringStringEntry : projectIdToUserIdMap.entrySet()) {
            userIdSet.add(stringStringEntry.getValue());
        }
        List<SimpleUser> userList= userRedisHelper.getSimplerUsers(orgId,new ArrayList<>(userIdSet));
        if(CollectionUtils.isEmpty(userList)){
            return;
        }
        List<String> deptIdList = new ArrayList<>();
        Map<String,List<String>> userToDeptIdList = new HashMap<>();
        for (SimpleUser simpleUser : userList) {
            List<DeptVO> deptVOList = simpleUser.getAllOrganizationList();
            if(!CollectionUtils.isEmpty(deptVOList)){
                List<String> deptIdListUser = deptVOList.stream().map(DeptVO::getId).collect(Collectors.toList());
                userToDeptIdList.put(simpleUser.getId(),deptIdListUser);
                deptIdList.addAll(deptIdListUser);
            }
        }

        // 因 底层未提供批量方法 顾只能 循环从缓存中获取

        Map<String, SimpleUserVO> userIdToSimUserMap = new HashMap<>();
        // 获取子部门对应的所有领导包括父级部门
        Map<String,List<String>> deptIdToAllLeadList = new HashMap<>();
        if(!CollectionUtils.isEmpty(deptIdList)){
            LambdaQueryWrapperX<DeptDO> deptwrapper= new LambdaQueryWrapperX<>(DeptDO.class);
            deptwrapper.in(DeptDO::getId,deptIdList);
            deptwrapper.select(DeptDO::getChain,DeptDO::getId);
            List<DeptDO> deptDOS =deptDOMapper.selectList(deptwrapper);
            if(!CollectionUtils.isEmpty(deptDOS)){
                deptDOS.forEach(item->{
                    String allChain = item.getChain();
                    deptIdList.addAll(Arrays.asList(allChain.split(",")));
                });

                List<String> distinctDept =  deptIdList.stream().filter(item-> !StrUtil.equals(item,"0")).distinct().collect(Collectors.toList());
                List<String> leadIdList = new ArrayList<>();

                // 获取但部门对应的领导类表
                Map<String,List<String>> deptIdToLeadList = new HashMap<>();

                if(!CollectionUtils.isEmpty(distinctDept)){
                    LambdaQueryWrapperX<DeptLeaderDO> deptLeaderDOLambdaQueryWrapperX =new LambdaQueryWrapperX<>(DeptLeaderDO.class);
                    deptLeaderDOLambdaQueryWrapperX.in(DeptLeaderDO::getDeptId,distinctDept);
                    List<DeptLeaderDO> deptLeaderDOList=deptLeaderDORepository.selectList(deptLeaderDOLambdaQueryWrapperX);
                    if(!CollectionUtils.isEmpty(deptLeaderDOList)){
                        deptLeaderDOList.forEach(item->{
                            leadIdList.add(item.getUserId());
                            List<String> leadList = deptIdToLeadList.getOrDefault(item.getDeptId(),new ArrayList<>());
                            leadList.add(item.getUserId());
                            if(!CollectionUtils.isEmpty(leadList)){
                                deptIdToLeadList.put(item.getDeptId(),leadList);
                            }
                        });

                    }
                }

                // 为了组装 子部门 对应的所有领导信息
                for (DeptDO deptDO : deptDOS) {
                    List<String> deptList= Arrays.asList(deptDO.getChain().split(","));
                    List<String> allLeadList = new ArrayList<>();
                    deptList.forEach(item->{
                        List<String> leadList = deptIdToLeadList.get(item);
                        if(!CollectionUtils.isEmpty(leadList)){
                            allLeadList.addAll(leadList);
                        }
                    });
                    if(!CollectionUtils.isEmpty(allLeadList)){
                        deptIdToAllLeadList.put(deptDO.getId(),allLeadList.stream().distinct().collect(Collectors.toList()));
                    }
                }


                if(!CollectionUtils.isEmpty(leadIdList)){
                    List<SimpleUserVO> simpleUserVOS = userBaseApiService.getUserByIds(orgId,leadIdList);
                    if(!CollectionUtils.isEmpty(simpleUserVOS)){
                        for (SimpleUserVO simpleUserVO : simpleUserVOS) {
                            userIdToSimUserMap.put(simpleUserVO.getId(),simpleUserVO);
                        }
                    }
                }
            }
        }

        List<String> roleCodeList  =new ArrayList<>();
        roleCodeList.add(ProjectRoleEnum.ROLE_PL.getCode());
        // 获取 角色信息
        List<String> projectIdList = new ArrayList<>();
        for (Map.Entry<String, String> stringStringEntry : projectIdToUserIdMap.entrySet()) {
            projectIdList.add(stringStringEntry.getKey());
        }

        List<RoleVO> pmsRoleList = projectRoleService.getPmsRoleListByProjectId(null,roleCodeList);
        if(!CollectionUtils.isEmpty(pmsRoleList)){

            List<ProjectRoleVO> projectRoleVOList = projectRoleService.getProjectRoleListByProjectIdList(projectIdList,pmsRoleList.get(0).getId());

            List<ProjectRole> projectRoles = new ArrayList<>();
            List<ProjectRoleUser> projectRoleUserList = new ArrayList<>();
            // 如果没有该角色直接新增
            if(CollectionUtils.isEmpty(projectRoleVOList)){
                for (Map.Entry<String, String> stringStringEntry : projectIdToUserIdMap.entrySet()) {
                    String projectId = stringStringEntry.getKey();
                    String creatorId = stringStringEntry.getValue();
                    List<String> userDeptIdList = userToDeptIdList.get(creatorId);
                    if(!CollectionUtils.isEmpty(userDeptIdList)){
                        List<String> leadList = new ArrayList<>();
                        for (String s : userDeptIdList) {
                            List<String> deptLeadList = deptIdToAllLeadList.get(s);
                            if(!CollectionUtils.isEmpty(deptLeadList)){
                                leadList.addAll(deptLeadList);
                            }
                        }

                        for (RoleVO roleVO : pmsRoleList) {
                            ProjectRole projectRoleDTO = new ProjectRole();
                            projectRoleDTO.setCode(roleVO.getCode());
                            projectRoleDTO.setName(roleVO.getName());
                            projectRoleDTO.setNumber(String.format(roleVO.getCode(), IdUtil.objectId()));
                            projectRoleDTO.setTakeEffect(TakeEffectEnum.EFFECT.getStatus());
                            if (!ObjectUtils.isEmpty(roleVO)) {
                                projectRoleDTO.setBusinessId(roleVO.getId());
                            }
                            projectRoleDTO.setCreatorId(creatorId);
                            projectRoleDTO.setModifyId(creatorId);
                            projectRoleDTO.setOwnerId(creatorId);
                            projectRoleDTO.setPlatformId(roleVO.getPlatformId());
                            projectRoleDTO.setOrgId(orgId);
                            projectRoleDTO.setProjectId(projectId);
                            // 自动设置ID

                            projectRoleDTO.setId(classRedisHelper.getUUID(ProjectRole.class.getSimpleName()));
                            projectRoles.add(projectRoleDTO);
                            String roleId = projectRoleDTO.getId();
                            if(CollectionUtils.isEmpty(leadList)){
                                continue;
                            }
                            List<String> distinctLeadList =   leadList.stream().distinct().collect(Collectors.toList());
                            for (String item : distinctLeadList) {
                                SimpleUserVO simpleUserVO =userIdToSimUserMap.get(item);
                                if(Objects.isNull(simpleUserVO)){
                                    continue;
                                }
                                ProjectRoleUser projectRoleUserDTO = new ProjectRoleUser();
                                projectRoleUserDTO.setProjectId(projectId);
                                projectRoleUserDTO.setProjectRoleId(roleId);
                                projectRoleUserDTO.setUserId(item);
                                projectRoleUserDTO.setName(simpleUserVO.getName());
                                projectRoleUserDTO.setNumber(simpleUserVO.getCode());
                                projectRoleUserDTO.setDefaultFlag(1);

                                projectRoleUserDTO.setCreatorId(simpleUserVO.getId());
                                projectRoleUserDTO.setModifyId(simpleUserVO.getId());
                                projectRoleUserDTO.setOwnerId(simpleUserVO.getId());
                                projectRoleUserDTO.setPlatformId(roleVO.getPlatformId());
                                projectRoleUserDTO.setOrgId(orgId);

                                projectRoleUserList.add(projectRoleUserDTO);
                            }
                        }
                    }
                }
            }else{
                // 如果有了该角色 ，还需要判断人员是否重复，只做新增不存在的人员，不作移除操作
                Map<String,List<ProjectRoleVO>> projectIdToUserList =projectRoleVOList.stream().collect(Collectors.groupingBy(ProjectRoleVO::getProjectId));

                for (Map.Entry<String, String> projectIdToUserId : projectIdToUserIdMap.entrySet()) {
                    String projectId = projectIdToUserId.getKey();

                    String creatorId = projectIdToUserId.getValue();
                    // 获取到当前人拥有的 所有领导
                    List<String> userDeptIdList = userToDeptIdList.get(creatorId);
                    if(!CollectionUtils.isEmpty(userDeptIdList)) {
                        List<String> leadList = new ArrayList<>();
                        for (String s : userDeptIdList) {
                            List<String> deptLeadList = deptIdToAllLeadList.get(s);
                            if (!CollectionUtils.isEmpty(deptLeadList)) {
                                leadList.addAll(deptLeadList);
                            }
                        }
                        if(!CollectionUtils.isEmpty(leadList)){
                            List<ProjectRoleVO> projectRoleVOS =  projectIdToUserList.get(projectId);
                            //获取到 当前项目中已经有的 领导信息
                            if(!CollectionUtils.isEmpty(projectRoleVOS)){
                                for (ProjectRoleVO projectRoleVO : projectRoleVOS) {
                                    List<String> userIdList = projectRoleVO.getUserIdList();
                                    if(!CollectionUtils.isEmpty(userIdList)){
                                        leadList.removeAll(userIdList);
                                    }
                                }
                                // 如果移除了 已有的数据后 还有新的领导需要入库
                                if(!CollectionUtils.isEmpty(leadList)){
                                    for (ProjectRoleVO projectRoleVO : projectRoleVOS) {
                                        List<String> distinctLeadList =   leadList.stream().distinct().collect(Collectors.toList());
                                        for (String item : distinctLeadList) {
                                            SimpleUserVO simpleUserVO =userIdToSimUserMap.get(item);
                                            if(Objects.isNull(simpleUserVO)){
                                                continue;
                                            }
                                            ProjectRoleUser projectRoleUserDTO = new ProjectRoleUser();
                                            projectRoleUserDTO.setProjectId(projectId);
                                            projectRoleUserDTO.setProjectRoleId(projectRoleVO.getId());
                                            projectRoleUserDTO.setUserId(item);
                                            projectRoleUserDTO.setName(simpleUserVO.getName());
                                            projectRoleUserDTO.setNumber(simpleUserVO.getCode());
                                            projectRoleUserDTO.setDefaultFlag(1);

                                            projectRoleUserDTO.setCreatorId(simpleUserVO.getId());
                                            projectRoleUserDTO.setModifyId(simpleUserVO.getId());
                                            projectRoleUserDTO.setOwnerId(simpleUserVO.getId());
                                            projectRoleUserDTO.setPlatformId(projectRoleVO.getPlatformId());
                                            projectRoleUserDTO.setOrgId(orgId);

                                            projectRoleUserList.add(projectRoleUserDTO);
                                        }
                                    }
                                }

                            }else{
                                // 如果当前项目没有角色以及用户信息--领导
                                if(!CollectionUtils.isEmpty(leadList)){
                                    for (RoleVO roleVO : pmsRoleList) {
                                        ProjectRole projectRoleDTO = new ProjectRole();
                                        projectRoleDTO.setCode(roleVO.getCode());
                                        projectRoleDTO.setName(roleVO.getName());
                                        projectRoleDTO.setNumber(String.format(roleVO.getCode(), IdUtil.objectId()));
                                        projectRoleDTO.setTakeEffect(TakeEffectEnum.EFFECT.getStatus());
                                        if (!ObjectUtils.isEmpty(roleVO)) {
                                            projectRoleDTO.setBusinessId(roleVO.getId());
                                        }
                                        projectRoleDTO.setCreatorId(creatorId);
                                        projectRoleDTO.setModifyId(creatorId);
                                        projectRoleDTO.setOwnerId(creatorId);
                                        projectRoleDTO.setPlatformId(roleVO.getPlatformId());
                                        projectRoleDTO.setOrgId(orgId);
                                        projectRoleDTO.setProjectId(projectId);
                                        // 自动设置ID

                                        projectRoleDTO.setId(classRedisHelper.getUUID(ProjectRole.class.getSimpleName()));
                                        projectRoles.add(projectRoleDTO);
                                        String roleId = projectRoleDTO.getId();
                                        if(CollectionUtils.isEmpty(leadList)){
                                            continue;
                                        }
                                        List<String> distinctLeadList =   leadList.stream().distinct().collect(Collectors.toList());
                                        for (String item : distinctLeadList) {
                                            SimpleUserVO simpleUserVO =userIdToSimUserMap.get(item);
                                            if(Objects.isNull(simpleUserVO)){
                                                continue;
                                            }
                                            ProjectRoleUser projectRoleUserDTO = new ProjectRoleUser();
                                            projectRoleUserDTO.setProjectId(projectId);
                                            projectRoleUserDTO.setProjectRoleId(roleId);
                                            projectRoleUserDTO.setUserId(item);
                                            projectRoleUserDTO.setName(simpleUserVO.getName());
                                            projectRoleUserDTO.setNumber(simpleUserVO.getCode());
                                            projectRoleUserDTO.setDefaultFlag(1);

                                            projectRoleUserDTO.setCreatorId(simpleUserVO.getId());
                                            projectRoleUserDTO.setModifyId(simpleUserVO.getId());
                                            projectRoleUserDTO.setOwnerId(simpleUserVO.getId());
                                            projectRoleUserDTO.setPlatformId(roleVO.getPlatformId());
                                            projectRoleUserDTO.setOrgId(orgId);

                                            projectRoleUserList.add(projectRoleUserDTO);
                                        }
                                    }
                                }
                            }
                        }


                    }




                }
            }
            if(!CollectionUtils.isEmpty(projectRoles)){
                projectRoleService.saveOrUpdateBatch(projectRoles);
            }
            if(!CollectionUtils.isEmpty(projectRoleUserList)){
                projectRoleUserService.saveOrUpdateBatch(projectRoleUserList);
            }

        }
    }

    @Override
    public List<SimpleVo> getProjectSimpleList(String keyword, Boolean isProjectMember) throws Exception {
        List<SimpleVo> simpleVoList = new ArrayList<>();
        LambdaQueryWrapperX<Project> projectLambdaQueryWrapperX = new LambdaQueryWrapperX<>(Project.class);
        projectLambdaQueryWrapperX.likeIfPresent(Project :: getName,keyword);
        if(isProjectMember){
            List<String> projectIds =  this.getMyProjectIds();
            if(CollectionUtils.isEmpty(projectIds)){
                return simpleVoList;
            }
            projectLambdaQueryWrapperX.in(Project :: getId,projectIds);
        }


        //todo 权限过滤
        List<Project> projectList = this.list(projectLambdaQueryWrapperX);

        if (CollectionUtils.isEmpty(projectList)) {
            return simpleVoList;
        }
        return BeanCopyUtils.convertListTo(projectList, SimpleVo::new);
    }

    @Override
//    @OperationPower(operationType = OperationPowerType.PAGE)
    public com.chinasie.orion.sdk.metadata.page.Page<NewProjectHomePageVO> getProjectPage(com.chinasie.orion.sdk.metadata.page.Page<ProjectDTO> pageRequest) throws Exception {
        long startTime = System.currentTimeMillis();
        com.chinasie.orion.sdk.metadata.page.Page<NewProjectHomePageVO> resultPage = new com.chinasie.orion.sdk.metadata.page.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), 0L, new ArrayList<>());
       pmsAuthUtil.setHeaderAuths(resultPage, CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), com.chinasie.orion.sdk.metadata.page.Page::setHeadAuthList, new ArrayList<>());
        LambdaQueryWrapperX<Project> projectOrionWrapper = new LambdaQueryWrapperX<>(Project.class);
        String userId = currentUserHelper.getUserId();
        ProjectDTO query = pageRequest.getQuery();
        if (Objects.nonNull(query)) {
            Boolean jumpFlag = query.getJumpFlag();
            if (jumpFlag) {
                List<String> ids = query.getIds();
                if (!CollectionUtils.isEmpty(ids)) {
                    projectOrionWrapper.in(Project::getId, ids);
                }
            }
            String scientificDeclareId= query.getScientificDeclareId();
            if(StrUtil.isNotBlank(scientificDeclareId)){
                projectOrionWrapper.eq(Project::getScientificDeclareId, scientificDeclareId);
            }
            if(StrUtil.isNotBlank(query.getResDept())){
                projectOrionWrapper.eq(Project::getResDept,query.getResDept());
            }
            if(StrUtil.isNotBlank(query.getResAdministrativeOffice())){
                projectOrionWrapper.eq(Project::getResAdministrativeOffice,query.getResAdministrativeOffice());
            }
            if (query.getStatus() != null) {
                projectOrionWrapper.eq(Project::getStatus, query.getStatus());
            }
        }
        long startTime1 = System.currentTimeMillis();
        log.info("第一阶段耗时：{}",(startTime1-startTime));
        // 如果通过了立项时间过滤，就只能查立项之后的数据；
        addStatus(pageRequest.getSearchConditions(), projectOrionWrapper);
        addContract(pageRequest.getSearchConditions(), projectOrionWrapper);
//        long startTime2 = System.currentTimeMillis();
//        log.info("第二阶段耗时：{}",(startTime2-startTime1));
        if(pageRequest.getSearchConditions() != null && pageRequest.getSearchConditions().size() > 0){
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), projectOrionWrapper);
        }


        IPage<Project> projectPageRequest = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());

        projectOrionWrapper.orderByDesc(Project::getProjectStartTime);

        // 获取用户所能看到的项目ID列表
        List<String> projectIdList = projectRoleUserRepository.getProjectIdList(userId);
        //分级分权
        List<String> depIds = getDepIdsByUser();
        if(CollectionUtils.isEmpty(depIds)){
            if(CollectionUtils.isEmpty(projectIdList)){
                return resultPage;
            }else{
                projectOrionWrapper.in(Project :: getId,projectIdList);
            }
        }else{
            if(CollectionUtils.isEmpty(projectIdList)){
                projectOrionWrapper.in(Project :: getResDept,depIds);
            }else{
                projectOrionWrapper.and(item ->item.in(Project :: getId,projectIdList)
                        .or().in(Project :: getResDept,depIds));
            }
        }


        long startTime3 = System.currentTimeMillis();
        log.info("第三阶段耗时：{}",(startTime3-startTime1));
        IPage<Project> pageResult = this.page(projectPageRequest, projectOrionWrapper);
        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(pageResult.getRecords())) {
            return resultPage;
        }
        List<Project> projectList = pageResult.getRecords();
        long startTime4 = System.currentTimeMillis();
        log.info("第四阶段耗时：{}",(startTime4-startTime3));
        /*
         * 查询关注的项目记录
         * */
        Map<String, UserLikeProjectVO> userLikeProjectMap = Maps.newHashMap();
        if(!CollectionUtils.isEmpty(projectIdList)){
            CompletableFuture<Map<String, UserLikeProjectVO>> userLikeProjectMapCompletableFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return userLikeProjectService.queryListByProjectIdListAndUserId(projectIdList, userId);
                } catch (Exception e) {
                    return new HashMap<>();
                }
            },projectPool);
            // 当前用户关注的项目集合
            userLikeProjectMap = userLikeProjectMapCompletableFuture.get();
        }

        List<NewProjectHomePageVO> projectVOList = BeanCopyUtils.convertListTo(projectList, NewProjectHomePageVO::new);

        long startTime5 = System.currentTimeMillis();
        log.info("第五阶段耗时：{}",(startTime5-startTime4));
        // 项目类型
        Map<String, String> projectTypeDictValueMap = dictBo.getDictValue(DICT_PROJECT_TYPE);

        long startTime6 = System.currentTimeMillis();
        log.info("第六阶段耗时：{}",(startTime6-startTime5));
        Map<String, UserLikeProjectVO> finalUserLikeProjectMap = userLikeProjectMap;
        projectVOList.forEach(o -> {
            String projectId = o.getId();
            o.setScheduleName(o.getFinishedDegree() + "%");
            o.setLike(finalUserLikeProjectMap.getOrDefault(projectId, null));
            o.setProjectTypeName(projectTypeDictValueMap.get(o.getProjectType()));
        });
        packageMilestone(projectVOList);
        long startTime7 = System.currentTimeMillis();
        log.info("第七阶段耗时：{}",(startTime7-startTime6));
        //权限设置
        Map<String, List<String>> dataRoleMap = getDataRoleMap(projectVOList);

        pmsAuthUtil.setRowAuths(CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), projectVOList, NewProjectHomePageVO::getId, NewProjectHomePageVO::getDataStatus, NewProjectHomePageVO::setRdAuthList,
                NewProjectHomePageVO::getCreatorId,
                NewProjectHomePageVO::getModifyId,
                NewProjectHomePageVO::getOwnerId,
                dataRoleMap);
        resultPage.setTotalSize(pageResult.getTotal());
        //填充合同信息
        List<String> projectNumList = projectVOList.stream().map(NewProjectHomePageVO::getNumber).collect(Collectors.toList());
        List<MarketContractApiVO> byProjectNumber = projectInitiationApiService.findByProjectNumber(projectNumList);

        if(!CollectionUtils.isEmpty(byProjectNumber)){
            Map<String,List<MarketContractApiVO>> map = byProjectNumber.stream().collect(Collectors.groupingBy(MarketContractApiVO::getProjectNumber));
            for(NewProjectHomePageVO projectVO:projectVOList){
                if(map.containsKey(projectVO.getNumber())){
                    projectVO.setContractAmt(map.get(projectVO.getNumber()).get(0).getContractAmt());
                    projectVO.setContractName(map.get(projectVO.getNumber()).get(0).getName());
                    projectVO.setContractNumber(map.get(projectVO.getNumber()).get(0).getNumber());
                }
            }
        }
        // 查询状态策略
        Map<Integer, DataStatusVO> statusMap = dataStatusNBO.getDataStatusMapByClassName(Project.class.getSimpleName());
        // 项目责任人
        List<String> userIds = projectVOList.stream().map(ProjectVO::getResPerson).distinct().collect(Collectors.toList());
        Map<String, String> userMap = UserConvertBO.getId2NameByIdsForRedis(userIds);
        projectVOList.forEach(x -> {
            if (Objects.nonNull(x.getStatus())){
                x.setDataStatus(statusMap.getOrDefault(x.getStatus(), new DataStatusVO()));
            }

            if (Objects.nonNull(x.getResPerson())){
                x.setResPersonName(userMap.getOrDefault(x.getResPerson(), ""));
            }
        });
        resultPage.setContent(projectVOList);
        long startTime8 = System.currentTimeMillis();
        log.info("第八阶段耗时：{}",(startTime8-startTime7));

        return resultPage;
    }

    private List<String> getDepIdsByUser() {
        String userId = CurrentUserHelper.getCurrentUserId();
//        UserVO userVO = userRedisHelper.getUserById(CurrentUserHelper.getOrgId(), userId);
//        List<DeptVO> organizations = userVO.getOrganizations();
//        List<String> leaderDeptIds = organizations.stream().map(DeptVO::getId).distinct().collect(Collectors.toList());
//        Map<String, DeptVO> deptVOMap = deptRedisHelper.mapAllDept();
//
//        List<DeptVO> deptVOS = org.apache.commons.compress.utils.Lists.newArrayList();
//
//        deptVOMap.forEach((k, vo) -> deptVOS.add(vo));
//
//        Map<String, List<DeptVO>> parentDeptVoMap = deptVOS.stream().collect(Collectors.groupingBy(DeptVO::getParentId));
//
//        List<String> deptIds = org.apache.commons.compress.utils.Lists.newArrayList();
//        leaderDeptIds.forEach(parentId->{
//            findSubDept(parentId,deptIds, parentDeptVoMap, deptVOMap);
//        });
        //看当前用户是否是领导
        LambdaQueryWrapperX<DeptLeaderDO> queryLeaderWrapper=new LambdaQueryWrapperX<>(DeptLeaderDO.class);
        queryLeaderWrapper.eq(DeptLeaderDO::getUserId,userId);
//        queryLeaderWrapper.select(DeptLeaderDO::getDeptId);
        boolean exists = deptLeaderDORepository.exists(queryLeaderWrapper);
        List<String> deptIds= Lists.newArrayList();
        if(exists){
            //查找当前领导所属的机构及其下属机构的所有的成员的
            List<DeptLeaderDO> deptLeaderDOS = deptLeaderDORepository.selectList(queryLeaderWrapper);
            List<String> leaderDeptIds = deptLeaderDOS.stream().map(DeptLeaderDO::getDeptId).distinct().collect(Collectors.toList());
            //查找下属的部门

            Map<String, DeptVO> deptVOMap = deptRedisHelper.mapAllDept();

            List<DeptVO> deptVOS= org.apache.commons.compress.utils.Lists.newArrayList();

            deptVOMap.forEach((k,vo)->deptVOS.add(vo));

            Map<String, List<DeptVO>> parentDeptVoMap = deptVOS.stream().collect(Collectors.groupingBy(DeptVO::getParentId));



            leaderDeptIds.forEach(parntId->{
                findSubDept(parntId,deptIds,parentDeptVoMap);
            });

            //获取相关机构的成员
//            if(!CollectionUtils.isEmpty(deptIds)){
//                LambdaQueryWrapperX<DeptUserDO> queryDeptUserWrapper=new LambdaQueryWrapperX();
//                queryDeptUserWrapper.in(DeptLeaderDO::getDeptId,deptIds);
//                queryDeptUserWrapper.select(DeptLeaderDO::getUserId);
//                List<DeptUserDO> deptUserDOS = deptUserDOMapper.selectList(queryDeptUserWrapper);
//                return deptUserDOS.stream().map(DeptUserDO::getDeptId).collect(Collectors.toList());
//            }
        }else {
            log.info("当前用户不是领导");
        }

        return deptIds;
    }

    /**
     * 查找下级
     *
     * @param parentId
     * @param deptIds
     * @param parentDeptVoMap
     * @param deptVOMap
     */
//    private void findSubDept(String parentId, List<String> deptIds, Map<String, List<DeptVO>> parentDeptVoMap, Map<String, DeptVO> deptVOMap) {
//        if (deptVOMap.containsKey(parentId)) {
//            DeptVO deptVO = deptVOMap.get(parentId);
//            deptIds.add(deptVO.getId());
//            if (parentDeptVoMap.containsKey(parentId)) {
//                List<DeptVO> deptVOS = parentDeptVoMap.get(parentId);
//                List<String> tempDeptIds = deptVOS.stream().map(DeptVO::getId).collect(Collectors.toList());
//                tempDeptIds.forEach(tempDeptId -> {
//                    findSubDept(tempDeptId, deptIds, parentDeptVoMap, deptVOMap);
//                });
//            }
//        }
//
//    }
    /**
     * 查找下级
     * @param parntId
     * @param deptIds
     * @param parentDeptVoMap
     */
    private void findSubDept(String parntId, List<String> deptIds, Map<String, List<DeptVO>> parentDeptVoMap) {
        deptIds.add(parntId);
        if(parentDeptVoMap.containsKey(parntId)){
            List<DeptVO> deptVOS = parentDeptVoMap.get(parntId);
            List<String> tempDeptIds = deptVOS.stream().map(DeptVO::getId).collect(Collectors.toList());
            tempDeptIds.forEach(tempDeptId->{
                findSubDept(tempDeptId,deptIds,parentDeptVoMap);
            });
        }
    }

    @Override
    public NewProjectHomePageVO getStatusPage(com.chinasie.orion.sdk.metadata.page.Page<ProjectDTO> pageRequest) throws Exception {
        //全部数量：
        NewProjectHomePageVO result = new NewProjectHomePageVO();
        String userId = currentUserHelper.getUserId();
        LambdaQueryWrapperX<Project> projectOrionWrapper = new LambdaQueryWrapperX<>(Project.class);
        // 获取用户所能看到的项目ID列表
        List<String> projectIdList = projectRoleUserRepository.getProjectIdList(userId);
        //分级分权
        List<String> depIds = getDepIdsByUser();
        if(CollectionUtils.isEmpty(depIds)){
            if(CollectionUtils.isEmpty(projectIdList)){
                return result;
            }else{
                projectOrionWrapper.in(Project :: getId,projectIdList);
            }
        }else{
            if(CollectionUtils.isEmpty(projectIdList)){
                projectOrionWrapper.in(Project :: getResDept,depIds);
            }else{
                projectOrionWrapper.and(item ->item.in(Project :: getId,projectIdList)
                        .or().in(Project :: getResDept,depIds));
            }
        }
        projectOrionWrapper.groupBy(Project ::getStatus);
        projectOrionWrapper.select("`status`,count(*) count ");
        List<Map<String, Object>> maps = this.listMaps(projectOrionWrapper);
        if(CollectionUtils.isEmpty(maps)){
            return result;
        }
        long allNum = 0;
        for(Map<String, Object> item : maps){
            String status = String.valueOf(item.get("status"));
            int count = ((Long)item.get("count")).intValue();
            allNum =  allNum + count;
            if(ProjectStatusEnum.CREATE.getStatus().toString().equals(status)){
                result.setCreateNum(count);
            }
            else if(ProjectStatusEnum.EXECUTION.getStatus().toString().equals(status)){
                result.setExecuteNum(count);
            }
            else if(ProjectStatusEnum.ACCEPTED.getStatus().toString().equals(status)){
                result.setReceiveNum(count);
            }
            else if(ProjectStatusEnum.END.getStatus().toString().equals(status)){
                result.setFinishNum(count);
            }
        }
        result.setAllNum(allNum);
//        result.setAllNum(getProjectPage(pageRequest).getTotalSize());
//
//        //已创建数量
//        ProjectDTO create = new ProjectDTO();
//        create.setStatus(101);
//        pageRequest.setQuery(create);
//        result.setCreateNum(getProjectPage(pageRequest).getTotalSize());
//
//        //已执行数量
//        ProjectDTO execute = new ProjectDTO();
//        execute.setStatus(121);
//        pageRequest.setQuery(execute);
//        result.setExecuteNum(getProjectPage(pageRequest).getTotalSize());
//
//        //已验收数量
//        ProjectDTO receive = new ProjectDTO();
//        receive.setStatus(130);
//        pageRequest.setQuery(receive);
//        result.setReceiveNum(getProjectPage(pageRequest).getTotalSize());
//
//        //结束数量
//        ProjectDTO finish = new ProjectDTO();
//        finish.setStatus(160);
//        pageRequest.setQuery(finish);
//        result.setReceiveNum(getProjectPage(pageRequest).getTotalSize());
        return result;
    }

    @Override
    public com.chinasie.orion.sdk.metadata.page.Page<ProjectZoneDTO> getProjectZone(com.chinasie.orion.sdk.metadata.page.Page<ProjectDTO> pageRequest) throws Exception {
        com.chinasie.orion.sdk.metadata.page.Page<ProjectZoneDTO> resultPage = new com.chinasie.orion.sdk.metadata.page.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), 0L, new ArrayList<>());
        LambdaQueryWrapperX<Project> projectOrionWrapper = new LambdaQueryWrapperX<>(Project.class);
        String userId = currentUserHelper.getUserId();
        ProjectDTO query = pageRequest.getQuery();
        if (Objects.nonNull(query)) {
            Boolean jumpFlag = query.getJumpFlag();
            if (jumpFlag) {
                List<String> ids = query.getIds();
                if (!CollectionUtils.isEmpty(ids)) {
                    projectOrionWrapper.in(Project::getId, ids);
                }
            }
            String scientificDeclareId= query.getScientificDeclareId();
            if(StrUtil.isNotBlank(scientificDeclareId)){
                projectOrionWrapper.eq(Project::getScientificDeclareId, scientificDeclareId);
            }
            if(StrUtil.isNotBlank(query.getResDept())){
                projectOrionWrapper.eq(Project::getResDept,query.getResDept());
            }
            if(StrUtil.isNotBlank(query.getResAdministrativeOffice())){
                projectOrionWrapper.eq(Project::getResAdministrativeOffice,query.getResAdministrativeOffice());
            }
            if (query.getStatus() != null) {
                projectOrionWrapper.eq(Project::getStatus, query.getStatus());
            }
        }
        addStatus(pageRequest.getSearchConditions(), projectOrionWrapper);
        SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), projectOrionWrapper);
        IPage<Project> projectPageRequest = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        projectOrionWrapper.orderByDesc(Project::getCreateTime);

        List<String> projectIdList = projectRoleUserRepository.getProjectIdList(userId);
        if(CollectionUtils.isEmpty(projectIdList)){
            return resultPage;
        }else{
            projectOrionWrapper.in(Project :: getId,projectIdList);
        }

        IPage<Project> pageResult = this.page(projectPageRequest, projectOrionWrapper);
        List<Project> projectlist = pageResult.getRecords();
        LambdaQueryWrapperX<ProjectToBasePlan> projectToBasePlanLambdaQueryWrapperX = new LambdaQueryWrapperX<>(ProjectToBasePlan.class);
        List<ProjectZoneDTO> projectZoneDTOS = new ArrayList<>();
        projectlist.forEach(project ->{
            ProjectZoneDTO projectZoneDTO = new ProjectZoneDTO();
            projectZoneDTO.setProjectId(Optional.ofNullable(project).map(Project::getId).orElse(null));
            projectZoneDTO.setProjectStartTime(Optional.ofNullable(project).map(Project::getProjectStartTime).orElse(null));
            projectZoneDTO.setProjectEndTime(Optional.ofNullable(project).map(Project::getProjectEndTime).orElse(null));
            projectZoneDTO.setProjectName(Optional.ofNullable(project).map(Project::getName).orElse(null));
            String name = userRedisHelper.getUserBaseCacheById(userId).getName();
            projectZoneDTO.setResPersonName(name);
            projectToBasePlanLambdaQueryWrapperX.eq(ProjectToBasePlan::getProjectId, project.getId());
            ProjectToBasePlan projectToBasePlan = projectToBasePlanRepository.selectOne(projectToBasePlanLambdaQueryWrapperX);
            if (projectToBasePlan != null && projectToBasePlan.getSourceType() != null) {
                if (org.springframework.util.StringUtils.hasText(projectToBasePlan.getSourceType())) {
                    String sourceDescription = ProjectSourceTypeEnum.getCode(projectToBasePlan.getSourceType());
                    if (sourceDescription != null) {
                        projectZoneDTO.setProjectSource(sourceDescription);
                    } else {
                        projectZoneDTO.setProjectSource(null);
                    }
                } else {
                    projectZoneDTO.setProjectSource(null);
                }
            } else {
                projectZoneDTO.setProjectSource(null);
            }

            projectZoneDTO.setSchedule(Optional.ofNullable(project).map(Project::getSchedule).orElse(null));

            projectZoneDTOS.add(projectZoneDTO);
        });

        com.chinasie.orion.sdk.metadata.page.Page<ProjectZoneDTO> objectPage = new com.chinasie.orion.sdk.metadata.
                page.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), projectlist.size());
        objectPage.setContent(projectZoneDTOS);

        return objectPage;
    }

    private void addStatus(List<List<SearchCondition>> searchConditions, LambdaQueryWrapperX<Project> projectOrionWrapper) {
        if (CollectionUtil.isEmpty(searchConditions)){
            return;
        }
        List<SearchCondition> newCollect = new ArrayList<>();
        searchConditions.forEach(ss -> {
            List<SearchCondition> collect = ss.stream().filter(s -> "projectApproveTime".equals(s.getField())).collect(Collectors.toList());
            newCollect.addAll(collect);
        });
        if (!CollectionUtil.isEmpty(newCollect)){
            projectOrionWrapper.ge(Project::getStatus,ProjectStatusEnum.APPROVED.getStatus());
        }
    }
    private void addContract(List<List<SearchCondition>> searchConditions, LambdaQueryWrapperX<Project> projectOrionWrapper) {
        if (CollectionUtil.isEmpty(searchConditions)){
            return;
        }
        List<SearchCondition> newCollect = new ArrayList<>();
        AtomicReference<String> contractNumberRef = new AtomicReference<>("");
        AtomicReference<String> contractNameRef = new AtomicReference<>("");
        AtomicReference<String> resPersonRef = new AtomicReference<>("");

        for (List<SearchCondition> ss : searchConditions) {
            ss.stream()
                    .filter(s -> "contractName".equals(s.getField()))
                    .findFirst()
                    .ifPresent(condition -> {
                        contractNameRef.set(condition.getValues().get(0).toString());
                        ss.removeIf(s -> "contractName".equals(s.getField()));
                    });
            ss.stream()
                    .filter(s -> "resPerson".equals(s.getField()))
                    .findFirst()
                    .ifPresent(condition -> {
                        resPersonRef.set(condition.getValues().get(0).toString());
                        ss.removeIf(s -> "resPerson".equals(s.getField()));
                    });

            ss.stream()
                    .filter(s -> "contractNumber".equals(s.getField()))
                    .findFirst()
                    .ifPresent(condition -> {
                        contractNumberRef.set(condition.getValues().get(0).toString());
                        ss.removeIf(s -> "contractNumber".equals(s.getField()));
                    });
        }

        searchConditions.removeIf(s -> s.size() <1);

        String contractNumber = contractNumberRef.get();
        String contractName = contractNameRef.get();
        String resPerson = resPersonRef.get();
        if(StringUtils.hasText(resPerson)){
            LambdaQueryWrapperX<UserDO> userDOLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            userDOLambdaQueryWrapperX.like(UserDO::getName,resPerson);
            userDOLambdaQueryWrapperX.or().like(UserDO::getCode,resPerson);
            List<UserDO> userDOS = userDOMapper.selectList(userDOLambdaQueryWrapperX);
            if(!CollectionUtils.isEmpty(userDOS)){
                SearchCondition searchCondition = new SearchCondition();
                searchCondition.setField("resPerson");
                searchCondition.setFieldType("String");
                searchCondition.setQueryType("in");
                searchCondition.setValues(userDOS.stream().map(UserDO :: getId).collect(Collectors.toList()));
                newCollect.add(searchCondition);
                searchConditions.add(newCollect);
            }
        }
        if(StringUtils.hasText(contractNumber) || StringUtils.hasText(contractName)){
            List<String> projectNumbers =  projectInitiationApiService.findByProjectNumber(contractNumber,contractName);
            if(!CollectionUtils.isEmpty(projectNumbers)){
                SearchCondition searchCondition = new SearchCondition();
                searchCondition.setField("number");
                searchCondition.setFieldType("String");
                searchCondition.setQueryType("in");
                searchCondition.setValues(projectNumbers.stream().map(Object.class::cast).collect(Collectors.toList()));
                newCollect.add(searchCondition);
                searchConditions.add(newCollect);
            }
        }

    }


    public Map<String, List<String>> getDataRoleMap(List<NewProjectHomePageVO> vos) throws Exception {
        if(CollectionUtils.isEmpty(vos)){
            return  new HashMap<>();
        }
        Map<String, List<String>> dataRoleCodeMap = new HashMap<>();
        String currentUserId = CurrentUserHelper.getCurrentUserId();
//        for (NewProjectHomePageVO v : vos) {
//            List<String> roles = pmsAuthUtil.getRoleCodeList(v.getId(), currentUserId);
//            dataRoleCodeMap.put(v.getId(), roles);
//        }
        List<String> projectIdList =  vos.stream().map(ProjectVO::getId).distinct().collect(Collectors.toList());
        List<UserProjectRoleDTO>  userProjectRoleDTOList = projectRoleUserRepository.getUserCodeToProjectId(projectIdList,currentUserId);
        if(CollectionUtils.isEmpty(userProjectRoleDTOList)){
            return  new HashMap<>();
        }
        return userProjectRoleDTOList.stream().collect(Collectors.groupingBy(UserProjectRoleDTO::getProjectId
                ,Collectors.mapping(UserProjectRoleDTO::getRoleCode,Collectors.toList())));
    }


    private void packageMilestone(List<NewProjectHomePageVO> vos) throws Exception {
        if (CollectionUtils.isEmpty(vos)) {
            return;
        }
        List<String> projectIdList = vos.stream().map(ProjectVO::getId).collect(Collectors.toList());

        CountDownLatch countDownLatch = new CountDownLatch(4);

        Map<String, ProjectMilestoneStatisticsVO> weekMap =new HashMap<>();
        Map<String, List<ProjectMilestoneStatisticsVO>> statusMap =new HashMap<>();
        Map<String, ProjectMilestoneStatisticsVO> monthMap = new HashMap<>();
        Map<String, String> map = new HashMap<>();

        //获取计划进度
        List<ProjectMilestoneStatisticsVO> milestoneStatisticsByStatus = new ArrayList<>();
        for (int i = 0; i < 4; i++) {
            int finalI = i;
            ThreadUtil.execAsync(() -> {
                try {
                    switch (finalI){
                        case 0:
                            //获取计划进度
                            LambdaQueryWrapperX<ProjectScheme> schemeLambdaQueryWrapperX=new LambdaQueryWrapperX(ProjectScheme.class);
                            schemeLambdaQueryWrapperX.select("project_id projectId,  Round(sum( CASE when`STATUS` = 111 THEN 1 ELSE 0 END )/count(*),2)*100 schedule");
                            schemeLambdaQueryWrapperX.in(ProjectScheme::getProjectId,projectIdList);
                            schemeLambdaQueryWrapperX.groupBy(ProjectScheme::getProjectId);
                            List<Map<String, Object>> projectSchemeObj = projectSchemeService.listMaps(schemeLambdaQueryWrapperX);
                            projectSchemeObj.forEach(item -> {
                                map.put(item.get("projectId").toString(), item.get("schedule").toString());
                            });
                            countDownLatch.countDown();
                            break;
                        case 1:
                            // 统计 项目状态里程碑统计数 项目计划中 里程碑 按项目，状态分组统计
                            LambdaQueryWrapperX<ProjectScheme> schemeLambdaQueryWrapper = new LambdaQueryWrapperX<>();
                            schemeLambdaQueryWrapper.select("project_id projectId,status,COUNT(*) as count");
                            schemeLambdaQueryWrapper.in(ProjectScheme::getProjectId, projectIdList);
                            schemeLambdaQueryWrapper.eq(ProjectScheme::getNodeType, ProjectSchemeNodeTypeEnum.MILESTONE.getValue());
                            schemeLambdaQueryWrapper.groupBy(ProjectScheme::getProjectId, ProjectScheme::getStatus);
                            List<Map<String, Object>> milestoneStatisticsByStatusObject = projectSchemeService.listMaps(schemeLambdaQueryWrapper);
                            List<ProjectMilestoneStatisticsVO> milestoneStatisticsByStatus1 = JSON.parseArray(JSON.toJSONString(milestoneStatisticsByStatusObject), ProjectMilestoneStatisticsVO.class);
                            if(!CollectionUtils.isEmpty(milestoneStatisticsByStatus1)){
                                milestoneStatisticsByStatus.addAll(milestoneStatisticsByStatus1);
                            }
                            countDownLatch.countDown();
                            break;
                        case 2:
                            // 统计 项目 已完成状态里程碑统计数 -》 项目计划中 里程碑 按项目 分组统计
                            Calendar monday = Calendar.getInstance();
                            monday.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
                            monday.set(Calendar.HOUR_OF_DAY, 0);
                            monday.set(Calendar.MINUTE, 0);
                            monday.set(Calendar.SECOND, 0);
                            monday.set(Calendar.MILLISECOND, 0);
                            LambdaQueryWrapperX<ProjectScheme> schemeLambdaQueryWrapper1 = new LambdaQueryWrapperX<>();
                            schemeLambdaQueryWrapper1.select("project_id projectId,COUNT(*) as count");
                            schemeLambdaQueryWrapper1.in(ProjectScheme::getProjectId, projectIdList);
                            schemeLambdaQueryWrapper1.eq(ProjectScheme::getStatus, Status.FINISHED.getCode());
                            schemeLambdaQueryWrapper1.eq(ProjectScheme::getNodeType, ProjectSchemeNodeTypeEnum.MILESTONE.getValue());
                            schemeLambdaQueryWrapper1.ge(ProjectScheme::getActualEndTime, monday.getTime());
                            schemeLambdaQueryWrapper1.groupBy(ProjectScheme::getProjectId);
                            List<Map<String, Object>> weekStatisticsObject = projectSchemeService.listMaps(schemeLambdaQueryWrapper1);
                            List<ProjectMilestoneStatisticsVO> weekStatistics = JSON.parseArray(JSON.toJSONString(weekStatisticsObject), ProjectMilestoneStatisticsVO.class);
                            weekStatistics.forEach(item-> weekMap.put(item.getProjectId(),item));
                            countDownLatch.countDown();
                            break;
                        case 3:
                            Calendar oneDay = Calendar.getInstance();
                            oneDay.set(Calendar.DAY_OF_MONTH, 1);
                            oneDay.set(Calendar.DAY_OF_MONTH, 1);
                            oneDay.set(Calendar.HOUR_OF_DAY, 0);
                            oneDay.set(Calendar.MINUTE, 0);
                            oneDay.set(Calendar.SECOND, 0);
                            oneDay.set(Calendar.MILLISECOND, 0);
                            LambdaQueryWrapperX<ProjectScheme> schemeLambdaQueryWrapper2 = new LambdaQueryWrapperX<>();
                            schemeLambdaQueryWrapper2.select("project_id projectId,COUNT(*) as count");
                            schemeLambdaQueryWrapper2.in(ProjectScheme::getProjectId, projectIdList);
                            schemeLambdaQueryWrapper2.eq(ProjectScheme::getStatus, Status.FINISHED.getCode());
                            schemeLambdaQueryWrapper2.eq(ProjectScheme::getNodeType, ProjectSchemeNodeTypeEnum.MILESTONE.getValue());
                            schemeLambdaQueryWrapper2.ge(ProjectScheme::getActualEndTime, oneDay.getTime());
                            schemeLambdaQueryWrapper2.groupBy(ProjectScheme::getProjectId);
                            List<Map<String, Object>> monthStatisticsObject = projectSchemeService.listMaps(schemeLambdaQueryWrapper2);
                            List<ProjectMilestoneStatisticsVO> monthStatistics = JSON.parseArray(JSON.toJSONString(monthStatisticsObject), ProjectMilestoneStatisticsVO.class);
                            monthStatistics.forEach(item-> monthMap.put(item.getProjectId(),item));
                            countDownLatch.countDown();
                            break;
                        default:
                            break;
                    }
                } catch (Exception e) {
                    log.error("异步调用报错：{}",e);
                }finally {
                    countDownLatch.countDown();
                }
            });


        }
        if(!CollectionUtils.isEmpty(milestoneStatisticsByStatus)){
            statusMap = milestoneStatisticsByStatus.stream().collect(Collectors.groupingBy(ProjectMilestoneStatisticsVO::getProjectId));
        }
        countDownLatch.await();
        Map<String, List<ProjectMilestoneStatisticsVO>> finalStatusMap = statusMap;
        vos.forEach(newProjectVO -> {
            String projectId = newProjectVO.getId();
            int totalCount = 0;
            int finishedCount = 0;
            int weekCount = 0;
            int monthCount = 0;
            BigDecimal finishedDegree = null;
            List<ProjectMilestoneStatisticsVO> statusList = finalStatusMap.get(projectId);
            if (!CollectionUtils.isEmpty(statusList)) {
                for (ProjectMilestoneStatisticsVO statisticsTemp : statusList) {
                    totalCount = totalCount + statisticsTemp.getCount();
                    if (Status.FINISHED.getCode().equals(statisticsTemp.getStatus())) {
                        finishedCount = finishedCount + statisticsTemp.getCount();
                    }
                }
            }
            ProjectMilestoneStatisticsVO weekStatisticsTemp = weekMap.get(projectId);
            if (weekStatisticsTemp != null) {
                weekCount = weekStatisticsTemp.getCount();
            }

            ProjectMilestoneStatisticsVO monthStatisticsTemp = monthMap.get(projectId);
            if (monthStatisticsTemp != null) {
                monthCount = monthStatisticsTemp.getCount();
            }
            if (totalCount == 0) {
                finishedDegree = BigDecimal.ZERO;
            } else if (finishedCount == 0) {
                finishedDegree = BigDecimal.ZERO;
            }
            if (totalCount != 0) {
                finishedDegree = new BigDecimal(finishedCount).divide(new BigDecimal(totalCount), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
            }
            newProjectVO.setTotalCount(totalCount);
            newProjectVO.setFinishedCount(finishedCount);
            newProjectVO.setWeekCount(weekCount);
            newProjectVO.setMonthCount(monthCount);
            newProjectVO.setFinishedDegree(finishedDegree.stripTrailingZeros());

            BigDecimal b1 = new BigDecimal(Double.toString(newProjectVO.getSchedule()));
            if(StrUtil.isNotBlank(map.get(projectId))){
                newProjectVO.setSchedule(Double.parseDouble(map.get(projectId)));
                newProjectVO.setScheduleName(map.get(projectId)+"0%");
            }else{
                newProjectVO.setSchedule(0.0);
                newProjectVO.setScheduleName("0%");
            }

        });
    }

    private void changePageParam(PageRequest<ProjectDTO> pageRequest, IPage<Project> projectPageRequest, LambdaQueryWrapper<Project> projectOrionWrapper) {

        List<ConditionItem> conditionItems = pageRequest.getQueryCondition();
        if (!CollectionUtils.isEmpty(conditionItems)) {
            Map<String, ConditionItem> columnToEntityMap = conditionItems.stream().collect(Collectors.toMap(ConditionItem::getColumn, Function.identity()));
            ConditionItem nameItem = columnToEntityMap.get("name");
            ConditionItem numberItem = columnToEntityMap.get("number");
            ConditionItem projectStartTimeItem = columnToEntityMap.get("projectStartTime");
            ConditionItem projectEndTimeItem = columnToEntityMap.get("projectEndTime");
            ConditionItem projectApproveTimeItem = columnToEntityMap.get("projectApproveTime");
            ConditionItem statusItem = columnToEntityMap.get("status");
            if (nameItem != null && numberItem != null) {
                String name = nameItem.getValue().toString();
                String number = numberItem.getValue().toString();
                if (StringUtils.hasText(name) && StringUtils.hasText(number)) {
                    projectOrionWrapper.and(r -> {
                        r.like(Project::getName, name).or().like(Project::getNumber, number);
                    });
                }
            }
            if (projectStartTimeItem != null) {
                Object value = projectStartTimeItem.getValue();
                List<Long> lists = JSONUtil.toList(value.toString(), Long.class);
                DateTime dateTime = DateUtil.date(lists.get(0));
                DateTime dateTime1 = DateUtil.date(lists.get(1));
                projectOrionWrapper.between(Project::getProjectStartTime, dateTime, dateTime1);
            }
            if (projectEndTimeItem != null) {
                Object value = projectEndTimeItem.getValue();
                List<Long> lists = JSONUtil.toList(value.toString(), Long.class);
                DateTime dateTime = DateUtil.date(lists.get(0));
                DateTime dateTime1 = DateUtil.date(lists.get(1));
                projectOrionWrapper.between(Project::getProjectEndTime, dateTime, dateTime1);
            }
            if (projectApproveTimeItem != null) {
                Object value = projectApproveTimeItem.getValue();
                List<Long> lists = JSONUtil.toList(value.toString(), Long.class);
                DateTime dateTime = DateUtil.date(lists.get(0));
                DateTime dateTime1 = DateUtil.date(lists.get(1));
                projectOrionWrapper.between(Project::getProjectApproveTime, dateTime, dateTime1);
            }
            if (statusItem != null) {
                Object value = statusItem.getValue();
                projectOrionWrapper.eq(Project::getStatus, value);
            }
        }
    }


    @Override
    public ProjectVO getProjectDetail(String id, String pageCode) throws Exception {
        Project projectDTO = this.getById(id);
        if (Objects.isNull(projectDTO)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "项目不存在!");
        }
        ProjectVO projectVO = new ProjectVO();
        BeanCopyUtils.copyProperties(projectDTO, projectVO);
        List<String> userIdList = new ArrayList<>();
        userIdList.add(projectVO.getCreatorId());
        userIdList.add(projectVO.getModifyId());
        userIdList.add(projectVO.getResPerson());
        Map<String, String> userIdAndNameMap = userBo.getNameByUserIdMap(userIdList);

//        if (StringUtils.hasText(projectVO.getProductId())) {
//            ProductVO productVO = componentBo.productVODetail(projectVO.getProductId());
//            projectVO.setProductName(productVO.getName());
//        }
        projectVO.setCreatorName(userIdAndNameMap.get(projectVO.getCreatorId()));
        projectVO.setModifyName(userIdAndNameMap.get(projectVO.getModifyId()));
        projectVO.setResPersonName(userIdAndNameMap.getOrDefault(projectVO.getResPerson(), ""));

        BigDecimal b1 = new BigDecimal(Double.toString(projectVO.getSchedule()));
        projectVO.setScheduleName(b1.multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");


        ProjectDeclare projectDeclare = projectDeclareRepository.selectOne(ProjectDeclare::getProjectId, id);
        if (projectDeclare != null) {
            ProjectDeclareVO projectDeclareVO = BeanCopyUtils.convertTo(projectDeclare, ProjectDeclareVO::new);
            projectVO.setProjectDeclareVO(projectDeclareVO);
            projectVO.setEstimateMoney(projectDeclareVO.getEstimateAmt());
            List<DocumentVO> materialList = projectDeclareFileInfoService.getDocumentList(ProjectDeclareFileTypeEnum.MATERIAL.getCode(), projectDeclare.getId(), null);
            projectVO.setMaterialList(materialList);
        }

        // 详情设置 综合计划
        projectVO.setPlanList(newProjectToBasePlanService.getPlanListByProjectId(id));

        String currentUserId = CurrentUserHelper.getCurrentUserId();
        List<String> roleCodeList = pmsAuthUtil.getRoleCodeList(id, currentUserId);
        pmsAuthUtil.setDetailAuths(projectVO, currentUserId, pageCode, projectVO.getDataStatus(), ProjectVO::setDetailAuthList, projectVO.getCreatorId(), projectVO.getModifyId(), projectVO.getOwnerId(), roleCodeList);

        setPlace(projectVO);

        if (ProjectTypeEnum.PROJECT_TYPE_RESEARCH.getValue().equals(projectVO.getProjectType())){
            setResearchBase(projectVO);
        }
        List<DocumentType> documentTypeDTOList = documentTypeService.list(new LambdaQueryWrapper<>(DocumentType.class)
                .eq(DocumentType::getName,"项目数据区")
                .eq(DocumentType::getParentId,0)
                .eq(DocumentType::getProjectId, id));
        if(!CollectionUtil.isEmpty(documentTypeDTOList)) {
            DocumentType documentType = documentTypeDTOList.get(0);
            projectVO.setDocumentId(documentType.getId());
        }
        Map<Integer, DataStatusVO> dataStatusMap= dataStatusNBO.getDataStatusMapByClassName(Project.class.getSimpleName());
        projectVO.setDataStatus(dataStatusMap.get(projectVO.getStatus()));
        return projectVO;
    }

    private void setResearchBase(ProjectVO projectVO) {
        Map<String, DictValueVO> levelDict = dictRedisHelper.getDictMapByCode(DictConstant.PROJECT_LEVEL_TYPE);
        Map<String, DictValueVO> researchDict = dictRedisHelper.getDictMapByCode(DictConstant.PROJECT_RESEARCH_TYPE);
        Map<String, DictValueVO> productDict = dictRedisHelper.getDictMapByCode(DictConstant.PROJECT_PRODUCT_TYPE);
        Map<String, DictValueVO> directionDict = dictRedisHelper.getDictMapByCode(DictConstant.PROJECT_DIRECTION_TYPE);
        if (!CollectionUtil.isEmpty(levelDict) && StrUtil.isNotBlank(projectVO.getLevel())){
            projectVO.setLevelName(levelDict.get(projectVO.getLevel()).getName());
        }
        if (!CollectionUtil.isEmpty(researchDict) && StrUtil.isNotBlank(projectVO.getResearch())){
            projectVO.setResearchName(researchDict.get(projectVO.getResearch()).getName());
        }
        if (!CollectionUtil.isEmpty(productDict) && StrUtil.isNotBlank(projectVO.getProductType())){
            projectVO.setProductTypeName(productDict.get(projectVO.getProductType()).getName());
        }
        if (!CollectionUtil.isEmpty(directionDict) && StrUtil.isNotBlank(projectVO.getDirection())){
            projectVO.setDirectionName(directionDict.get(projectVO.getDirection()).getName());
        }
        String projectType = projectVO.getProjectType();
        String projectSubType = projectVO.getProjectSubType();
        Map<String, String> researchChildren = dictBo.getDictValueToDesMap(DictConstant.PROJECT_TYPE_RESEARCH_CHILDREN);
        if(StringUtils.hasText(projectSubType) && ProjectTypeEnum.PROJECT_TYPE_RESEARCH.getValue().equals(projectType)){
            projectVO.setProjectSubTypeName(researchChildren.get(projectSubType));
        }
    }


    @Override
    public List<ProjectVO> getProjectList(List<String> ids) throws Exception {
        List<Project> records = projectRepository.selectList(Project :: getId,ids);
        return BeanCopyUtils.convertListTo(records, ProjectVO::new);
    }

    @Override
    public UserVO getProjectPm(String projectId) throws Exception {
        String projectRoleId = projectRoleService.getRoleIdByProjectIdAnCode(projectId, ProjectRoleEnum.ROLE_XMJL.getCode());
        if (StringUtils.hasText(projectRoleId)) {
            List<String> userIds = projectRoleUserService.getAllUserIdListByProjectIdAndRoleId(projectId, projectRoleId);
            if (!CollectionUtils.isEmpty(userIds)) {
                if (userIds.size() == 1) {
                    UserVO userVO = userRedisHelper.getUserById(userIds.get(0));
                    return userVO;
                } else {
                    List<UserVO> userVOS = userRedisHelper.getUserByIds(userIds);
                    if (!CollectionUtils.isEmpty(userVOS)) {
                        if (userVOS.size() == 1) {
                            return userVOS.get(0);
                        }
                        Project projectDTO = this.getById(projectId);
                        List<UserVO> pmUserVO = userVOS.stream().filter(p -> p.getName().equals(projectDTO.getPm())).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(pmUserVO)) {
                            return userVOS.get(0);
                        }
                        return pmUserVO.get(0);
                    }

                }


            }
        }

        return null;
    }

    @Override
    public ProjectVO getProjectDetailByNumber(String number) throws Exception {
        LambdaQueryWrapper<Project> projectOrionWrapper = new LambdaQueryWrapper<>(Project.class);
        projectOrionWrapper.eq(Project::getNumber, number);
        Project project = this.getOne(projectOrionWrapper);
        return BeanCopyUtils.convertTo(project, ProjectVO::new);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean editProject(ProjectDTO projectDTO) throws Exception {
        String number = projectDTO.getNumber();
        String id = projectDTO.getId();
        if (StrUtil.isBlank(number)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "编码不能为空");
        }
        List<Project> projectDTOList = this.list(new LambdaQueryWrapper<>(Project.class).
                ne(Project::getId, projectDTO.getId())
                .and(o -> o.eq(Project::getName, projectDTO.getName()).or().eq(Project::getNumber, number)));
        if (!CollectionUtils.isEmpty(projectDTOList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_NAME_REPEAT);
        }
        Project original = this.getById(projectDTO.getId());

        if (Objects.isNull(original)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        if (projectDTO.getIsDeclare() != null && !projectDTO.getIsDeclare()) {
            LambdaQueryWrapper<ProjectDeclare> projectDeclareLambdaQuery = new LambdaQueryWrapper<>(ProjectDeclare.class);
            projectDeclareLambdaQuery.eq(ProjectDeclare::getProjectId, projectDTO.getId());
            projectDeclareLambdaQuery.ne(ProjectDeclare::getStatus, ProjectDeclareStatusEnum.END.getStatus());
            ProjectDeclare projectDeclare = projectDeclareRepository.selectOne(projectDeclareLambdaQuery);
            if (projectDeclare != null) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "项目已发起申报，不能将项目改为不需要申报!");
            }
        }
        String statusId = projectDTO.getStatusId();
        if (StringUtils.hasText(statusId)) {
            projectDTO.setStatus(Integer.parseInt(statusId));
        }
        projectDTO.setSchedule(original.getSchedule());

        String resPerson = projectDTO.getResPerson();
        if (StringUtils.hasText(resPerson)) {
            SimpleUser resSimpleUser = userRedisHelper.getSimpleUserById(resPerson);
            if (resSimpleUser != null) {
                projectDTO.setResDept(resSimpleUser.getOrgId());
                projectDTO.setResTeamGroup(resSimpleUser.getClassId());
                if (!StringUtils.hasText(resSimpleUser.getDeptId())) {
                    projectDTO.setResAdministrativeOffice(resSimpleUser.getDeptId());
                }
            }
        }

        Project project = BeanCopyUtils.convertTo(projectDTO, Project::new);
        Boolean update = this.updateById(project);
        if (update) {
//            List<String> schemeIdList = projectDTO.getSchemeIdList();
//            addSchemeProjectRelation(projectDTO.getId(), schemeIdList);

            List<RelevancyPlanDTO> planList = projectDTO.getPlanList();
            // 初始化绑定综合计划
            newProjectToBasePlanService.packageEntityListAndSave(id, number, planList);
        }

        return update;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeProject(List<String> ids) throws Exception {
        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }
        //删除逻辑
        this.removeBatchByIds(ids);
        //删除项目集中项目信息
        projectCollectionToProjectService.remove(new LambdaQueryWrapper<>(ProjectCollectionToProject.class)
                .in(ProjectCollectionToProject::getToId,ids));
        warningSettingService.removeByProjectIds(ids);
        return true;
    }

    @Override
    public Map<String, String> getIdToNameMapByIdList(List<String> idList) throws Exception {
        if (CollectionUtils.isEmpty(idList)) {
            return new HashMap<>();
        }
        List<Project> projectDTOList = this.list(new LambdaQueryWrapper<>(Project.class).
                in(Project::getId, idList));
        if (CollectionUtils.isEmpty(projectDTOList)) {
            return new HashMap<>();
        }
        return projectDTOList.stream().collect(Collectors.toMap(Project::getId, Project::getName));
    }

    @Override
    public List<SimpleVo> getProductList() throws Exception {
        List<ProductVO> productVOList = componentBo.productList();
        if (CollectionUtils.isEmpty(productVOList)) {
            return new ArrayList<>();
        }
        return BeanCopyUtils.convertListTo(productVOList, SimpleVo::new);
    }

    @Override
    public Map<String, String> getIdToNumberByProjectIdList(List<String> projectIdList) throws Exception {
        List<Project> projectList = this.listByIds(projectIdList);
        if (CollectionUtils.isEmpty(projectIdList)) {
            return new HashMap<>();
        }
        return projectList.stream().collect(Collectors.toMap(Project::getId, Project::getNumber));
    }

    @Override
    public List<ProjectVO> search(SearchDTO searchDTO) throws Exception {
        LambdaQueryWrapper<Project> wrapper = new LambdaQueryWrapper<>(Project.class);
        List<String> ids = searchDTO.getIds();
        if (!CollectionUtils.isEmpty(ids)) {
            wrapper.in(Project::getId, ids);
        }
        Integer status = searchDTO.getStatus();
        if (Objects.nonNull(status)) {
            wrapper.eq(Project::getStatus, status);
        }
        List<Project> projectDTOS = this.list(wrapper);
        List<ProjectVO> projectVOS = BeanCopyUtils.convertListTo(projectDTOS, ProjectVO::new);
        return projectVOS;
    }

    @Override
    public List<ProjectPlanGanttVO> gantt(String projectId) throws Exception {
//        List<Plan> planDTOS = planService.list(new LambdaQueryWrapper<>(Plan.class).eq(Plan::getProjectId, projectId));
//        if (CollectionUtils.isEmpty(planDTOS)) {
//            return new ArrayList<>();
//        }
//        planDTOS.sort(Comparator.comparing(Plan::getCreateTime, Comparator.nullsLast(Date::compareTo)));
//        List<String> planIds = planDTOS.stream().map(Plan::getId).collect(Collectors.toList());
//
//        List<BeforeAfterToPlan> beforeAfterToPlans = beforeAfterToPlanService.list(new LambdaQueryWrapper<>(BeforeAfterToPlan.class).eq(BeforeAfterToPlan::getType, 1).in(BeforeAfterToPlan::getToId, planIds));
//
//        Map<String, List<String>> beforeAfterToPlanMap = beforeAfterToPlans.stream().collect(Collectors.groupingBy(BeforeAfterToPlan::getToId, Collectors.mapping(BeforeAfterToPlan::getFromId, Collectors.toList())));
//
//        //列表转树
//        List<PlanDTO> planDTOSTree = toTree(BeanCopyUtils.convertListTo(planDTOS, PlanDTO::new));
//
//        List<ProjectPlanGanttVO> result = new ArrayList<>();
//        //树转列表
//        AtomicInteger index = new AtomicInteger(1);
//        Map<String, Integer> idIndexMap = new HashMap<>();
//        operationTree(planDTOSTree, planDTO -> {
//            ProjectPlanGanttVO projectPlanGanttVO = new ProjectPlanGanttVO();
//            int andIncrement = index.getAndIncrement();
//            idIndexMap.put(planDTO.getId(), andIncrement);
//            projectPlanGanttVO.setId(andIncrement);
//            projectPlanGanttVO.setPlanId(planDTO.getId());
//            projectPlanGanttVO.setCreateTime(planDTO.getCreateTime());
//            projectPlanGanttVO.setCanWrite(false);
//            projectPlanGanttVO.setCollapsed(false);
//            projectPlanGanttVO.setParentId(planDTO.getParentId());
//            projectPlanGanttVO.setName(planDTO.getName());
//            Date startTime = planDTO.getPlanStartTime();
//            long startMillisecond = startTime.getTime();
//            projectPlanGanttVO.setStart(startMillisecond);
//
//            Date endTime = planDTO.getPlanEndTime();
//            long endMillisecond = endTime.getTime();
//            projectPlanGanttVO.setEnd(endMillisecond);
//
//            /*
//             * 时间间隔（天）
//             * */
//            long duration = DateUtil.between(DateUtil.beginOfDay(startTime), DateUtil.endOfDay(endTime), DateUnit.DAY);
//            projectPlanGanttVO.setDuration(duration);
//
//            projectPlanGanttVO.setTaskType(planDTO.getPlanType());
//
//            projectPlanGanttVO.setWarning("预警");
//
//            String resPerson = planDTO.getResUser();
//            String userName = userRedisHelper.getUserById(resPerson).getName();
//            DeptVO organization = deptRedisHelper.getDeptById(planDTO.getResDept());
//            if (Objects.nonNull(organization)) {
//                String deptName = organization.getName();
//                if (StrUtil.isNotBlank(userName) && StrUtil.isNotBlank(deptName)) {
//                    projectPlanGanttVO.setLeftText(String.format("%s：%s", deptName, userName));
//                } else if (StrUtil.isNotBlank(userName)) {
//                    projectPlanGanttVO.setLeftText(userName);
//                } else if (StrUtil.isNotBlank(deptName)) {
//                    projectPlanGanttVO.setLeftText(deptName);
//                }
//            }
//
//
//            projectPlanGanttVO.setLevel(planDTO.getLevel());
//            // 进度
//            float progress = Optional.ofNullable(planDTO.getSchedule())
//                    .orElse(new BigDecimal(0))
//                    .floatValue();
//            projectPlanGanttVO.setProgress(progress);
//            projectPlanGanttVO.setHasChild(CollectionUtil.isNotEmpty(planDTO.getChildren()));
//            projectPlanGanttVO.setStatus(SchemeStatusEnum.getSchemeStatusEnum(planDTO.getStatus()));
//            result.add(projectPlanGanttVO);
//            return planDTO;
//        }, 0);
//
//        result.forEach(r -> {
//            List<String> fromIds = beforeAfterToPlanMap.getOrDefault(r.getPlanId(), new ArrayList<>());
//            r.setDepends(idIndexMap.entrySet().stream().filter(o -> fromIds.contains(o.getKey())).map(o -> String.valueOf(o.getValue())).collect(Collectors.joining(",")));
//        });
//        return result;

        return new ArrayList<>();
    }

    @Override
    public Boolean updateStatus(String projectId, NewProjectStatusEnum newProjectStatusEnum) throws Exception {
        Project project = this.getById(projectId);
        if (ObjectUtil.isEmpty(project)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_NOT_ROLE, "当前项目不存在，请刷新后重试");
//            throw  ServiceExceptionUtil.exception0(PMSErrorCode.PMS_ERROR_NOT_ROLE.getErrorCode(), "当前项目不存在，请刷新后重试");
        }

        Boolean ret = internalUpdateStatus(projectId, newProjectStatusEnum);
        if (Objects.equals(Boolean.TRUE, ret)) {
            //注释了缓存
//            deleteCachedProjectEntity(projectId);
        }
        return ret;
    }

    @Override
    public Boolean updateStatus(ProjectUpdateStatusDTO projectUpdateStatusDTO) throws Exception {
        Project project = this.getById(projectUpdateStatusDTO.getProjectId());
        if (ObjectUtil.isEmpty(project)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_NOT_ROLE, "当前项目不存在，请刷新后重试");
        }
        Project project1 = new Project();
        project1.setId(projectUpdateStatusDTO.getProjectId());
        project1.setStatus(projectUpdateStatusDTO.getStatus());
        return this.updateById(project1);
    }

    @Override
    public Boolean updateAcceptanceFormInfo(String projectId, String acceptanceFormId, String acceptanceFormNumber) {
        Project project = new Project();
        project.setId(projectId);
        project.setAcceptanceFormId(acceptanceFormId);
        project.setAcceptanceFormNumber(acceptanceFormNumber);
        return this.updateById(project);
    }

    private Boolean internalUpdateStatus(String projectId, NewProjectStatusEnum status) {
        Project project = new Project();
        project.setId(projectId);
        project.setStatus(status.getStatus());
        //验收时间
        project.setFinishTime(new Date());
//        project
        return this.updateById(project);
    }


//    /**
//     * 删除缓存数据.
//     *
//     * @param projectId
//     */
//    private void deleteCachedProjectEntity(String... projectId) {
//        redisTemplate.delete(ListUtil.of(String.format(CACHE_PROJECT_KEY, projectId)));
//    }

    /**
     * 树化
     *
     * @param source 源
     * @return List<E>
     */
    public static List<PlanDTO> toTree(List<PlanDTO> source) {
        if (Objects.isNull(source) || source.isEmpty()) {
            return new ArrayList<>();
        }
        Map<String, PlanDTO> map = source.stream().collect(Collectors.toMap(PlanDTO::getId, Function.identity()));
        Set<PlanDTO> root = new HashSet<>();

        source.forEach(d -> {
            String parentId = d.getParentId();
            if (map.containsKey(parentId)) {
                PlanDTO parent = map.get(parentId);
                List<PlanDTO> child = parent.getChildren();
                if (child == null) {
                    child = new ArrayList<>();
                }
                child.add(d);
                child.sort(Comparator.comparing(PlanDTO::getCreateTime, Comparator.nullsLast(Date::compareTo)));
                parent.setChildren(child);
                root.remove(d);
            } else {
                root.add(d);
            }
        });
        return new ArrayList<>(root);
    }

    public static List<PlanDTO> operationTree(List<PlanDTO> source, Function<PlanDTO, PlanDTO> operation, int level) {
        List<PlanDTO> target = new ArrayList<>();
        source.forEach(o -> {
            o.setLevel(level);
            List<PlanDTO> child = o.getChildren();
            if (Objects.nonNull(child) && !child.isEmpty()) {
                PlanDTO parent = operation.apply(o);
                int tmpLevel = level + 1;
                List<PlanDTO> searched = operationTree(child, operation, tmpLevel);
                parent.setChildren(searched);
                target.add(parent);
            } else {
                target.add(operation.apply(o));
            }
        });
        return target;
    }

    /**
     * 新增项目和计划的关系
     *
     * @param projectId    项目ID
     * @param schemeIdList 计划ID列表
     * @throws Exception 异常
     */
    private void addSchemeProjectRelation(String projectId, List<String> schemeIdList) throws Exception {
//        schemeApi.addSchemeRelation(new ProjectToSchemeRelationDTO(projectId, schemeIdList));
    }


    @Override
    public com.chinasie.orion.sdk.metadata.page.Page<ProjectVO> pageProjectByUserCode(String userCode, com.chinasie.orion.sdk.metadata.page.Page<ProjectDTO> pageRequest) {
        SimpleUser simpleUser = userRedisHelper.getSimpleUserByCode(userCode);
        List<ProjectRoleUser> projectRoleUsers = projectRoleUserService.list(new LambdaQueryWrapper<>(ProjectRoleUser.class).eq(ProjectRoleUser::getUserId, simpleUser.getId()));
        List<String> projectIds = projectRoleUsers.stream().map(ProjectRoleUser::getProjectId).distinct().collect(Collectors.toList());
        LambdaQueryWrapper<Project> condition = new LambdaQueryWrapper<>(Project.class).in(Project::getId, projectIds);

        Page<Project> realPageQuest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());

        ProjectDTO query = pageRequest.getQuery();
        if (Objects.nonNull(query)) {
            String name = query.getName();
            if (StrUtil.isNotBlank(name)) {
                condition.like(Project::getName, name);
            }
        }


        Page<Project> page = this.page(realPageQuest, condition);
        List<ProjectVO> projectVOS = BeanCopyUtils.convertListTo(page.getRecords(), ProjectVO::new);
        com.chinasie.orion.sdk.metadata.page.Page<ProjectVO> result = new com.chinasie.orion.sdk.metadata.page.Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        result.setContent(projectVOS);

        return result;
    }


    /**
     * 1-准备中  : 已创建(101)、待立项(110)
     * 2-实施中  : 已立项(120)
     * 3-已完成  : 已验收(130)、已关闭(140)
     * 4-已超期  : 超过计划结束时间 且 已创建(101)、待立项(110)、已立项(120)
     *
     * @param type
     * @return
     * @throws Exception
     */
    @Override
    public List<Project> homePage(Integer type) throws Exception {
        List<Project> resultProject = CollUtil.toList();
        String userId = currentUserHelper.getUserId();
        List<ProjectRoleUser> projectRoleUsers = projectRoleUserService.list(new LambdaQueryWrapper<>(ProjectRoleUser.class)
                .eq(ProjectRoleUser::getUserId, userId));
        if (CollectionUtils.isEmpty(projectRoleUsers)) {
            return resultProject;
        }
        List<String> projectIdList = projectRoleUsers.stream().map(ProjectRoleUser::getProjectId).collect(Collectors.toList());
        List<Project> projects = this.listByIds(projectIdList);
        if (CollectionUtil.isNotEmpty(projects)) {
            List<String> ids = projects.stream().map(Project::getId).collect(Collectors.toList());
            LambdaQueryWrapperX<Project> lambdaQueryWrapperX = new LambdaQueryWrapperX();
            lambdaQueryWrapperX.eq(Project::getResPerson, userId);
            lambdaQueryWrapperX.notIn(Project::getId, ids);
            List<Project> projectList = this.list(lambdaQueryWrapperX);
            projects.addAll(projectList);
        }
        if (Objects.isNull(type)) {
            resultProject = projects.stream().limit(10).collect(Collectors.toList());
            return resultProject;
        }


        switch (type) {
            case 1:
                resultProject = projects.stream()
                        .filter(item -> {
                            Integer status = item.getStatus();
                            Date predictEndTime = item.getProjectEndTime();
                            return (NewProjectStatusEnum.PROJECT_CREATED.getStatus().equals(status)
                                    || NewProjectStatusEnum.PROJECT_APPROVALING.getStatus().equals(status)
                            )
                                    && (predictEndTime != null && predictEndTime.after(DateUtil.date()));
                        }).limit(10).collect(Collectors.toList());
                break;
            case 2:
                resultProject = projects.stream()
                        .filter(item -> {
                            Integer status = item.getStatus();
                            Date predictEndTime = item.getProjectEndTime();
                            return (NewProjectStatusEnum.PROJECT_APPROVALED.getStatus().equals(status))
                                    && (predictEndTime != null && predictEndTime.after(DateUtil.date()));
                        }).limit(10).collect(Collectors.toList());
                break;
            case 3:
                resultProject = projects.stream().filter(item -> NewProjectStatusEnum.PROJECT_CHECK_AND_ACCEPT.getStatus().equals(item.getStatus())
                        || NewProjectStatusEnum.PROJECT_CLOSE.getStatus().equals(item.getStatus())).limit(10).collect(Collectors.toList());
                break;
            case 4:
                resultProject = projects.stream().filter(item -> (NewProjectStatusEnum.PROJECT_CREATED.getStatus().equals(item.getStatus())
                        || NewProjectStatusEnum.PROJECT_APPROVALING.getStatus().equals(item.getStatus())
                        || NewProjectStatusEnum.PROJECT_APPROVALED.getStatus().equals(item.getStatus())
                )
                        && !ObjectUtils.isEmpty(item.getProjectEndTime()) && item.getProjectEndTime().compareTo(new Date()) < 1).limit(10).collect(Collectors.toList());
                break;
        }
        if (CollectionUtil.isNotEmpty(resultProject)) {
            List<String> strings = resultProject.stream().filter(item -> StrUtil.isNotBlank(item.getResPerson())).map(Project::getResPerson).collect(Collectors.toList());
            List<String> depts = resultProject.stream().filter(item -> StrUtil.isNotBlank(item.getResDept())).map(Project::getResDept).collect(Collectors.toList());
            Map<String, String> userIdAndNameMap = userBo.getNameByUserIdMap(strings);
            Map<String, SimpleDeptVO> deptNames = deptRedisHelper.getSimpleDeptMapByDeptIds(depts);
            for (Project p : resultProject) {
                if (ObjectUtil.isNotEmpty(userIdAndNameMap)) {
                    p.setResPersonName(userIdAndNameMap.get(p.getResPerson()));
                }
                if (ObjectUtil.isNotEmpty(deptNames)) {
                    SimpleDeptVO simpleDeptVO = deptNames.get(p.getResDept());
                    p.setResDeptName(simpleDeptVO == null ? "" : simpleDeptVO.getName());
                }
            }
        }
        return resultProject;
    }


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public NewProjectVO getSingleDetail(String id, String pageCode) throws Exception {
        Project newProject = this.queryForEntityById(id);
        if (newProject == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "当前项目不存在");
        }
        NewProjectVO result = BeanCopyUtils.convertTo(newProject, NewProjectVO::new);
        // 详情设置 综合计划
        result.setPlanList(newProjectToBasePlanService.getPlanListByProjectId(id));
        result.getEnables().put("capitalizeAssets", false);
        result.getEnables().put("traferAssets", false);
        result.getEnables().put("traferCapitalize", false);
        if (NewProjectStatusEnum.PROJECT_APPROVALED.getStatus().equals(newProject.getStatus())) {
            // 是否允许完工确认
            result.getEnables().put("completeProject", Boolean.TRUE);
        }
        if (NewProjectStatusEnum.PROJECT_APPROVALED.getStatus().equals(newProject.getStatus())) {
            result.getEnables().put("acceptProject", Boolean.TRUE);
        }
        String resDept = result.getResDept();
        if (StringUtils.hasText(resDept)) {
            SimpleDeptVO simpleOrganizationById = deptRedisHelper.getSimpleDeptById(resDept);
            if (!ObjectUtil.isEmpty(simpleOrganizationById)) {
                result.setResDeptName(simpleOrganizationById.getName());
            }
        }
        String resPerson = result.getResPerson();
        if (StringUtils.hasText(resPerson)) {
            UserVO user = userBo.getUserById(resPerson);
            if (!ObjectUtil.isEmpty(user)) {
                result.setResPersonName(user.getName());
            }
        }
        LambdaQueryWrapper<ProjectToBasePlan> orionWrapper = new LambdaQueryWrapper<>(ProjectToBasePlan.class);
        orionWrapper.eq(ProjectToBasePlan::getProjectId, result.getId());
        List<ProjectToBasePlan> newProjectToBasePlanList = projectToBasePlanService.list(orionWrapper);
        if (!CollectionUtils.isEmpty(newProjectToBasePlanList)) {

        }

        LambdaQueryWrapperX<ProjectDeclare> projectDeclareLambdaQueryWrapper = new LambdaQueryWrapperX<>(ProjectDeclare.class);
        projectDeclareLambdaQueryWrapper.eq(ProjectDeclare::getProjectId, result.getId());
        ProjectDeclare projectDeclare1 = projectDeclareService.getOne(projectDeclareLambdaQueryWrapper);
        if (ObjectUtil.isNotEmpty(projectDeclare1)) {
            result.setEstimateMoney(projectDeclare1.getEstimateAmt());
        }
        Map<String, String> projectTypeDictValueMap = dictBo.getDictValue(DICT_PROJECT_TYPE);
        result.setTypeName(projectTypeDictValueMap.get(newProject.getProjectType()));
        String org = result.getResOrg();
        List<RelevancyPlanDTO> planList = result.getPlanList();
        // 权限设置
        if (StringUtils.hasText(pageCode)) {
            String currentUserId = CurrentUserHelper.getCurrentUserId();
            List<String> roleCodeList = pmsAuthUtil.getRoleCodeList(id, currentUserId);
            pmsAuthUtil.setDetailAuths(result, currentUserId, pageCode, result.getDataStatus(), NewProjectVO::setDetailAuthList, null, null, result.getOwnerId(), roleCodeList);
        }
        return result;
    }

    public Project queryForEntityById(String projectId) throws Exception {
        Project project = getCachedProjectEntity(projectId);
        if (project != null) {
            return project;
        }

        project = super.getById(projectId);
        cacheProjectEntity(project);
        return project;
    }

    /**
     * 从缓存获取Project数据.
     *
     * @param projectId
     * @return
     */
    private Project getCachedProjectEntity(String projectId) {
        String format = String.format(CACHE_PROJECT_KEY, projectId);
        return (Project) redisTemplate.opsForValue().get(format);
    }

    /**
     * 缓存Project数据.
     *
     * @param project
     */
    private void cacheProjectEntity(Project project) {
        if (project == null) {
            return;
        }

        // 30天基础上加上一个随机值, 避免数据同时失效.
        long expireTime = projectCacheExpireTime();
        redisTemplate.opsForValue().set(String.format(CACHE_PROJECT_KEY, project.getId()), project, expireTime, TimeUnit.MINUTES);
    }

    private int projectCacheExpireTime() {
        return 24 * 60 * 30 + 24 * 8 * new Random().nextInt(10);
    }


    @Override
    public com.chinasie.orion.sdk.metadata.page.Page<ProjectVO> workBenchPage(Integer type, com.chinasie.orion.sdk.metadata.page.Page<ProjectDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<Project> projectOrionWrapper = new LambdaQueryWrapperX<>(Project.class);
        ProjectDTO query = pageRequest.getQuery();
        List<String> projectIds = new ArrayList<>();
        List<String> collect = new ArrayList<>();
        List<String> roleProjectIds = new ArrayList<>();
        List<ProjectRoleUser> projectRoleUsers = new ArrayList<>();
        Map<String, String> roleNameMap = new HashMap<>();
        if (type == 1) {
            String userId = currentUserHelper.getUserId();
            LambdaQueryWrapperX<Project> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            lambdaQueryWrapperX.eq(Project::getResPerson, userId);
            List<Project> projectList = this.list(lambdaQueryWrapperX);
            if (CollectionUtil.isNotEmpty(projectList)) {
                collect = projectList.stream().map(Project::getId).collect(Collectors.toList());
            }
            roleProjectIds = getProjectIds();
            projectIds.addAll(collect);
            projectIds.addAll(roleProjectIds);
        } else {
            projectRoleUsers = getRole();
            projectIds = projectRoleUsers.stream().map(ProjectRoleUser::getProjectId).collect(Collectors.toList());
        }
        if (CollectionUtil.isNotEmpty(projectRoleUsers)) {
            List<String> projectRoleIds = projectRoleUsers.stream().map(ProjectRoleUser::getProjectRoleId).collect(Collectors.toList());
            List<ProjectRole> projectRoles = projectRoleService.listByIds(projectRoleIds);
            for (ProjectRole projectRole : projectRoles) {
                String roleName = roleNameMap.get(projectRole.getProjectId());
                if (StrUtil.isNotBlank(roleName)) {
                    roleNameMap.put(projectRole.getProjectId(), roleName + "," + projectRole.getName());
                } else {
                    roleNameMap.put(projectRole.getProjectId(), projectRole.getName());
                }
            }
        }

        if (CollectionUtils.isEmpty(projectIds)) {
            return new com.chinasie.orion.sdk.metadata.page.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), 0L, new ArrayList<>());
        }
        projectOrionWrapper.in(Project::getId, projectIds);
        SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), projectOrionWrapper);
        IPage<Project> projectPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        projectOrionWrapper.orderByDesc(Project::getCreateTime);
        IPage<Project> pageResult = this.page(projectPageRequest, projectOrionWrapper);
        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(pageResult.getRecords())) {
            return new com.chinasie.orion.sdk.metadata.page.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), 0L, new ArrayList<>());
        }
        List<Project> projectList = pageResult.getRecords();
        List<ProjectVO> projectVOList = BeanCopyUtils.convertListTo(projectList, ProjectVO::new);
        // 项目类型
        Map<String, String> projectTypeDictValueMap = dictBo.getDictValue(DICT_PROJECT_TYPE);


        List<String> finalProjectIds = roleProjectIds;
        List<String> finalCollect = collect;
        //获取计划进度
        List<String> ids=projectVOList.stream().map(ProjectVO::getId).collect(Collectors.toList());
        LambdaQueryWrapperX<ProjectScheme> schemeLambdaQueryWrapperX=new LambdaQueryWrapperX(ProjectScheme.class);
        schemeLambdaQueryWrapperX.select("project_id projectId,  Round(sum( CASE when`STATUS` = 111 THEN 1 ELSE 0 END )/count(*),2)*100 schedule");
        schemeLambdaQueryWrapperX.in(ProjectScheme::getProjectId,ids);
        schemeLambdaQueryWrapperX.groupBy(ProjectScheme::getProjectId);
        List<Map<String, Object>> milestoneStatisticsByStatusObject = projectSchemeService.listMaps(schemeLambdaQueryWrapperX);
        Map<String, String> map = new HashMap<>();
        milestoneStatisticsByStatusObject.forEach(item -> {
            map.put(item.get("projectId").toString(), item.get("schedule").toString());
        });
        projectVOList.forEach(o -> {
            //BigDecimal b1 = new BigDecimal(Double.toString(o.getSchedule()));
            if (type == 1) {
                if (finalCollect.contains(o.getId())) {
                    o.setRoleName("项目负责人");
                }

                if (finalProjectIds.contains(o.getId())) {
                    String roleName = StrUtil.isBlank(o.getRoleName()) ? "" : o.getRoleName() + ",";
                    o.setRoleName(roleName + "项目经理");
                }
            } else {
                o.setRoleName(roleNameMap.get(o.getId()));
            }
            if(StrUtil.isNotBlank(map.get(o.getId()))){
                o.setSchedule(Double.parseDouble(map.get(o.getId())));
            }else{
                o.setSchedule(0.0);
            }
            //o.setScheduleName(b1.multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
            o.setProjectTypeName(projectTypeDictValueMap.get(o.getProjectType()));
        });
        //packageMilestone(projectVOList);
        return new com.chinasie.orion.sdk.metadata.page.Page<>(pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal(), projectVOList);
    }

    @Override
    public List<UserVO> getProjectPmListByProjectId(String projectId) throws Exception {
        List<String> userIdList = this.getRoleUserIdByProjectId(projectId);
        if (CollectionUtils.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        return userRedisHelper.getUserByIds(userIdList);
    }


    @Override
    public Boolean isPmToUserId(String projectId, String userId) throws Exception {
        List<String> userIdList = this.getRoleUserIdByProjectId(projectId);
        if (CollectionUtils.isEmpty(userIdList)) {
            return Boolean.FALSE;
        }
        return userIdList.contains(userId);
    }

    public List<String> getRoleUserIdByProjectId(String projectId) throws Exception {
        String projectRoleId = projectRoleService.getRoleIdByProjectIdAnCode(projectId, ProjectRoleEnum.ROLE_XMJL.getCode());
        if (StringUtils.hasText(projectRoleId)) {
            List<String> userIds = projectRoleUserService.getAllUserIdListByProjectIdAndRoleId(projectId, projectRoleId);
            return userIds;
        }
        return new ArrayList<>();
    }


    @Override
    public Map<String, List<String>> getRoleUserIdByProjectIdList(List<String> projectIdList) throws Exception {
        Map<String, String> pidToRuIdMap = projectRoleService.getRoleIdMapByProjectIdListAnCode(projectIdList, ProjectRoleEnum.ROLE_XMJL.getCode());
        if (0 == pidToRuIdMap.size()) {
            return new HashMap<>();
        }
        // 用户角色ID
        List<String> ruIdList = pidToRuIdMap.entrySet().stream().map(Map.Entry::getValue).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(ruIdList)) {
            return projectRoleUserService.getAllUserIdListByProjectIdListAndRoleIdList(projectIdList, ruIdList);
        }
        return new HashMap<>();
    }


    @Override
    public List<String> getProjectIds() throws Exception {
        String userId = currentUserHelper.getUserId();
        List<Project> projects = new ArrayList<>();
        LambdaQueryWrapperX<ProjectRoleUser> projectRoleLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        projectRoleLambdaQueryWrapperX.selectAll(ProjectRoleUser.class);
        projectRoleLambdaQueryWrapperX.leftJoin(ProjectRole.class, ProjectRole::getId, ProjectRoleUser::getProjectRoleId);
        projectRoleLambdaQueryWrapperX.eq(ProjectRoleUser::getUserId, userId);
        projectRoleLambdaQueryWrapperX.eq(ProjectRole::getNumber, ProjectRoleEnum.ROLE_XMJL.getNumber());
        List<ProjectRoleUser> projectRoleUsers = projectRoleUserService.list(projectRoleLambdaQueryWrapperX);
        if (CollectionUtil.isNotEmpty(projectRoleUsers)) {
            List<String> projectIdList = projectRoleUsers.stream().map(ProjectRoleUser::getProjectId).collect(Collectors.toList());
            projects = this.listByIds(projectIdList);
        }
        if (CollectionUtils.isEmpty(projects)) {
            return new ArrayList<>();
        }
        List<String> projectIds = projects.stream().map(Project::getId).collect(Collectors.toList());
        return projectIds;
    }

    public List<String> getMyProjectIds() throws Exception {
        String userId = currentUserHelper.getUserId();
        List<Project> projects = new ArrayList<>();
        LambdaQueryWrapperX<ProjectRoleUser> projectRoleLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        projectRoleLambdaQueryWrapperX.eq(ProjectRoleUser::getUserId, userId);
        List<ProjectRoleUser> projectRoleUsers = projectRoleUserService.list(projectRoleLambdaQueryWrapperX);
        if (CollectionUtil.isNotEmpty(projectRoleUsers)) {
            List<String> projectIdList = projectRoleUsers.stream().map(ProjectRoleUser::getProjectId).collect(Collectors.toList());
            projects = this.listByIds(projectIdList);
        }
        if (CollectionUtils.isEmpty(projects)) {
            return new ArrayList<>();
        }
        List<String> projectIds = projects.stream().map(Project::getId).collect(Collectors.toList());
        return projectIds;
    }

    public List<ProjectRoleUser> getRole() throws Exception {
        List<ProjectVO> projectVOList = new ArrayList<>();
        String userId = currentUserHelper.getUserId();

        List<Project> projects = new ArrayList<>();
        LambdaQueryWrapperX<Project> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(Project::getResPerson, userId);
        List<Project> projectList = this.list(lambdaQueryWrapperX);
        List<String> projectIds = projectList.stream().map(Project::getId).collect(Collectors.toList());
        LambdaQueryWrapperX<ProjectRoleUser> projectRoleLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        projectRoleLambdaQueryWrapperX.selectAll(ProjectRoleUser.class);
        projectRoleLambdaQueryWrapperX.leftJoin(ProjectRole.class, ProjectRole::getId, ProjectRoleUser::getProjectRoleId);
        projectRoleLambdaQueryWrapperX.eq(ProjectRoleUser::getUserId, userId);
        projectRoleLambdaQueryWrapperX.ne(ProjectRole::getNumber, ProjectRoleEnum.ROLE_XMJL.getNumber());
        if (CollectionUtil.isNotEmpty(projectIds)) {
            projectRoleLambdaQueryWrapperX.notIn(ProjectRole::getProjectId, projectIds);
        }
        List<ProjectRoleUser> projectRoleUsers = projectRoleUserService.list(projectRoleLambdaQueryWrapperX);
        return projectRoleUsers;
    }


    @Override
    public com.chinasie.orion.sdk.metadata.page.Page<NewProjectHomePageVO> getThirdProjectPage(com.chinasie.orion.sdk.metadata.page.Page<SearchDTO> pageRequest) throws Exception {

        com.chinasie.orion.sdk.metadata.page.Page<NewProjectHomePageVO> resultPage = new com.chinasie.orion.sdk.metadata.page.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), pageRequest.getTotalSize());


        LambdaQueryWrapperX<Project> projectOrionWrapper = new LambdaQueryWrapperX<>(Project.class);
        SearchDTO query = pageRequest.getQuery();
        if (Objects.nonNull(query)) {
            List<String> ids = query.getIds();
            if (!CollectionUtils.isEmpty(ids)) {
                projectOrionWrapper.in(Project::getId, ids);
            }
        }


        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), projectOrionWrapper);
        }


        IPage<Project> projectPageRequest = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());

        projectOrionWrapper.orderByDesc(Project::getCreateTime);
        IPage<Project> pageResult = this.page(projectPageRequest, projectOrionWrapper);
        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(pageResult.getRecords())) {
            return resultPage;
        }
        List<Project> projectList = pageResult.getRecords();

        List<NewProjectHomePageVO> projectVOList = BeanCopyUtils.convertListTo(projectList, NewProjectHomePageVO::new);
        List<String> userIdList = projectVOList.stream().map(ProjectVO::getModifyId).collect(Collectors.toList());
        userIdList.addAll(projectVOList.stream().map(ProjectVO::getCreatorId).collect(Collectors.toList()));
        Map<String, String> userIdAndNameMap = userBo.getNameByUserIdMap(userIdList.stream().distinct().collect(Collectors.toList()));
        List<String> productIdList = projectVOList.stream().map(ProjectVO::getProductId).distinct().collect(Collectors.toList());
        // 项目类型
        Map<String, String> projectTypeDictValueMap = dictBo.getDictValue(DICT_PROJECT_TYPE);

        projectVOList.forEach(o -> {
            String projectId = o.getId();
            o.setProjectTypeName(projectTypeDictValueMap.get(o.getProjectType()));
        });
        packageMilestone(projectVOList);
        //权限设置
        Map<String, List<String>> dataRoleMap = getDataRoleMap(projectVOList);
        pmsAuthUtil.setRowAuths(CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), projectVOList, NewProjectHomePageVO::getId, NewProjectHomePageVO::getDataStatus, NewProjectHomePageVO::setRdAuthList,
                NewProjectHomePageVO::getCreatorId,
                NewProjectHomePageVO::getModifyId,
                NewProjectHomePageVO::getOwnerId,
                dataRoleMap);
        resultPage.setTotalSize(pageResult.getTotal());
        resultPage.setContent(projectVOList);


        return resultPage;
    }

    public List<ProjectVO> getProjectListById(List<String> ids) throws Exception{
        List<Project> projectList=this.listByIds(ids);
        List<ProjectVO> projectVOList = BeanCopyUtils.convertListTo(projectList, ProjectVO::new);
        return projectVOList;
    }

    public List<ProjectVO> getProjectListByName(ProjectDTO projectDTO) throws Exception{
        LambdaQueryWrapperX<Project> lambdaQueryWrapperX=new LambdaQueryWrapperX();
        lambdaQueryWrapperX.like(Project::getName,projectDTO.getName());
        List<Project> projectList=this.list(lambdaQueryWrapperX);
        List<ProjectVO> projectVOList = BeanCopyUtils.convertListTo(projectList, ProjectVO::new);
        return projectVOList;
    }

    private void setPlace(ProjectVO projectVO) {
        if (ObjectUtil.isNull(projectVO)){
            return;
        }
        String id = projectVO.getId();
        List<ProjectPlace> places = projectPlaceService.getPlaceByProjectId(id);
        if (CollectionUtil.isEmpty(places)){
            return;
        }
        List<String> areaIds = places.stream().map(ProjectPlace::getAreaId).distinct().collect(Collectors.toList());
        ResponseDTO<List<BaseAreaVO>> byAreaIds;
        try {
            byAreaIds = pmiService.getByAreaIds(areaIds);
        } catch (Exception e) {
            throw new PMSException(PMSErrorCode.PMS_ERR,"获取base数据失败！");
        }
        List<BaseAreaVO> result = byAreaIds.getResult();
        if (CollectionUtil.isEmpty(result)){
            return;
        }
        Map<String, BaseAreaVO> areaVOMap = result.stream().collect(Collectors.toMap(BaseAreaVO::getAreaId, b -> b));
        List<PlaceVO> placeVOS = new ArrayList<>();
        for (ProjectPlace place : places) {
            String areaId = place.getAreaId();
            BaseAreaVO baseAreaVO = areaVOMap.get(areaId);
            if (!ObjectUtil.isNull(baseAreaVO)){
                PlaceVO placeVO = new PlaceVO();
                placeVO.setAreaId(areaId);
                placeVO.setArea(baseAreaVO.getArea());
                placeVOS.add(placeVO);
            }
        }
        projectVO.setPlaceVOS(placeVOS);
    }


    public List<String> getProjectIdList(ProjectDTO projectDTO) throws Exception{
        LambdaQueryWrapperX<Project> lambdaQueryWrapperX=new LambdaQueryWrapperX(Project.class);
        lambdaQueryWrapperX.likeIfPresent(Project::getName,projectDTO.getName());
        lambdaQueryWrapperX.likeIfPresent(Project::getNumber,projectDTO.getNumber());
        lambdaQueryWrapperX.likeIfPresent(Project::getPm,projectDTO.getPm());
        List<Project> projectList=this.list(lambdaQueryWrapperX);
        List<ProjectVO> projectVOList = BeanCopyUtils.convertListTo(projectList, ProjectVO::new);
        if(CollectionUtil.isNotEmpty(projectVOList)){
            List<String> idList = projectVOList.stream().map(ProjectVO::getId).collect(Collectors.toList());
            return idList;
        }
        return new ArrayList<>();
    }

    @Override
    public com.chinasie.orion.sdk.metadata.page.Page<ProjectVO> getWorkHourProjectPage(com.chinasie.orion.sdk.metadata.page.Page<ProjectDTO> page) throws Exception {

        ProjectDTO projectDTO = page.getQuery();
        IPage<Project> projectPageRequest = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(page.getPageNum(), page.getPageSize());
        LambdaQueryWrapperX<Project> lambdaQueryWrapperX=new LambdaQueryWrapperX(Project.class);
        lambdaQueryWrapperX.likeIfPresent(Project::getName,projectDTO.getName());
        lambdaQueryWrapperX.likeIfPresent(Project::getNumber,projectDTO.getNumber());
        lambdaQueryWrapperX.likeIfPresent(Project::getPm,projectDTO.getPm());

        IPage<Project> pageResult = this.page(projectPageRequest, lambdaQueryWrapperX);
        com.chinasie.orion.sdk.metadata.page.Page<ProjectVO> resultPage = new com.chinasie.orion.sdk.metadata.page.Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(pageResult.getRecords())) {
            return resultPage;
        }

        List<Project> projectList = pageResult.getRecords();
        List<ProjectVO> projectVOList = BeanCopyUtils.convertListTo(projectList,ProjectVO::new);
        resultPage.setTotalSize(pageResult.getTotal());
        resultPage.setContent(projectVOList);
        return resultPage;
    }


    @Override
    public com.chinasie.orion.sdk.metadata.page.Page<ProjectVO> listByPM(com.chinasie.orion.sdk.metadata.page.Page<ProjectDTO> pageRequest) throws Exception {
        com.chinasie.orion.sdk.metadata.page.Page<ProjectVO> resultPage = new com.chinasie.orion.sdk.metadata.page.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), 0L, new ArrayList<>());
        String userId = CurrentUserHelper.getCurrentUserId();
        LambdaQueryWrapper<ProjectRoleUser> roleUserWrapper = new LambdaQueryWrapper<>();
        roleUserWrapper.eq(ProjectRoleUser::getUserId, userId);
        List<ProjectRoleUser> projectRoleUsers = projectRoleUserService.list(roleUserWrapper);
        List<ProjectRole> roles = projectRoleService.list(new LambdaQueryWrapper<>(ProjectRole.class).eq(ProjectRole::getCode, ProjectRoleEnum.ROLE_XMJL.getCode()));
        if (CollUtil.isEmpty(projectRoleUsers) || CollUtil.isEmpty(roles)) {
            return resultPage;
        }
        List<String> roleIds = roles.stream().map(ProjectRole::getId).collect(Collectors.toList());
        List<String> projectIds = projectRoleUsers.stream().filter(item -> roleIds.contains(item.getProjectRoleId())).map(ProjectRoleUser::getProjectId).collect(Collectors.toList());
        IPage<Project> projectPageRequest = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        LambdaQueryWrapperX<Project> projectOrionWrapper = new LambdaQueryWrapperX<>(Project.class);
        projectOrionWrapper.in(Project::getId, projectIds);
        ProjectDTO query = pageRequest.getQuery();
        if (Objects.nonNull(query)) {
            if (StrUtil.isNotBlank(query.getResDept())) {
                projectOrionWrapper.eq(Project::getResDept, query.getResDept());
            }
            if (StrUtil.isNotBlank(query.getResAdministrativeOffice())) {
                projectOrionWrapper.eq(Project::getResAdministrativeOffice, query.getResAdministrativeOffice());
            }
        }
        // 如果通过了立项时间过滤，就只能查立项之后的数据；
//        addStatus(pageRequest.getSearchConditions(), projectOrionWrapper);
        SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), projectOrionWrapper);
        IPage<Project> page = this.page(projectPageRequest, projectOrionWrapper);
        List<Project> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return resultPage;
        }
        List<ProjectVO> projectVOS = BeanCopyUtils.convertListTo(records, ProjectVO::new);
        resultPage.setContent(projectVOS);
        resultPage.setTotalSize(page.getTotal());
        return resultPage;
    }

    @Resource
    private RoleRedisHelper roleRedisHelper;
    @Override
    public List<UserVO> listPD(List<String> projectIds) throws Exception {
        RoleVO role = roleRedisHelper.getRole(ProjectRoleEnum.ROLE_PD.getCode(), CurrentUserHelper.getOrgId());
        if (Objects.isNull(role)){
            return CollUtil.toList();
        }
        List<ProjectRole> roles = projectRoleService.list(new LambdaQueryWrapper<>(ProjectRole.class)
                .eq(ProjectRole::getBusinessId, role.getId())
                .in(ProjectRole::getProjectId, projectIds));
        if (CollUtil.isEmpty(roles)) {
            return CollUtil.toList();
        }
        LambdaQueryWrapper<ProjectRoleUser> roleUserWrapper = new LambdaQueryWrapper<>();
        roleUserWrapper.in(ProjectRoleUser::getProjectId, projectIds);
        roleUserWrapper.in(ProjectRoleUser::getProjectRoleId, roles.stream().map(ProjectRole::getId).collect(Collectors.toList()));
        List<ProjectRoleUser> projectRoleUsers = projectRoleUserService.list(roleUserWrapper);
        if (CollUtil.isEmpty(projectRoleUsers)) {
            return CollUtil.toList();
        }
        List<String> userIds = projectRoleUsers.stream().map(ProjectRoleUser::getUserId).collect(Collectors.toList());
        return userRedisHelper.getUserByIds(userIds);
    }

    @Override
    public com.chinasie.orion.sdk.metadata.page.Page<ProjectVO> allList(com.chinasie.orion.sdk.metadata.page.Page<ProjectDTO> page) {
        IPage<Project> projectPageRequest = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(page.getPageNum(), page.getPageSize());

        IPage<Project> pageResult = this.page(projectPageRequest);
        com.chinasie.orion.sdk.metadata.page.Page<ProjectVO> resultPage = new com.chinasie.orion.sdk.metadata.page.Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(pageResult.getRecords())) {
            return resultPage;
        }

        List<Project> projectList = pageResult.getRecords();
        List<ProjectVO> projectVOList = BeanCopyUtils.convertListTo(projectList,ProjectVO::new);

        /*Map<String, DictValueVO> dictMap = dictRedisHelper.getDictMapByCode("pms_project_type");
        projectVOList.forEach(vo ->{
            if(dictMap.get(vo.getProjectType()) != null){
                vo.setProjectTypeName(dictMap.get(vo.getProjectType()).getName());
            }

        });*/

        resultPage.setTotalSize(pageResult.getTotal());
        resultPage.setContent(projectVOList);
        return resultPage;
    }


    @Override
    public com.chinasie.orion.sdk.metadata.page.Page<ProjectVO> getProjectPageForProjectCollection(com.chinasie.orion.sdk.metadata.page.Page<ProjectDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<Project> projectOrionWrapper = new LambdaQueryWrapperX<>(Project.class);
//        addStatus(pageRequest.getSearchConditions(), projectOrionWrapper);
        projectOrionWrapper.select(Project::getId, Project::getName, Project::getPm, Project::getCreateTime,Project::getProjectSource, Project::getProjectType)
                .leftJoin(ProjectToBasePlan.class, ProjectToBasePlan::getProjectId, Project::getId)
                .selectAs(ProjectToBasePlan::getBasePlanId,Project::getProjectSource)
                .selectAs(ProjectToBasePlan::getSourceType, Project::getProjectType);
        String keyWord = pageRequest.getQuery().getKeyWord();
        if (StrUtil.isNotBlank(keyWord)){
            projectOrionWrapper.like(Project::getName,keyWord).or().like(Project::getPm,keyWord);
            if( ProjectSourceTypeEnum.PLAN.getDescription().contains(keyWord)){
                projectOrionWrapper.or().like(ProjectToBasePlan::getSourceType, ProjectSourceTypeEnum.PLAN.getCode());
            }
            if( ProjectSourceTypeEnum.CONTRACT.getDescription().contains(keyWord)){
                projectOrionWrapper.or().like(ProjectToBasePlan::getSourceType, ProjectSourceTypeEnum.CONTRACT.getCode());
            }
            if( ProjectSourceTypeEnum.DECLARE.getDescription().contains(keyWord)){
                projectOrionWrapper.or().like(ProjectToBasePlan::getSourceType, ProjectSourceTypeEnum.DECLARE.getCode());
            }
            if( ProjectSourceTypeEnum.LEAD.getDescription().contains(keyWord)){
                projectOrionWrapper.or().like(ProjectToBasePlan::getSourceType, ProjectSourceTypeEnum.LEAD.getCode());
            }
            if( ProjectSourceTypeEnum.BUSINESS_OPPORTUNITY.getDescription().contains(keyWord)){
                projectOrionWrapper.or().like(ProjectToBasePlan::getSourceType, ProjectSourceTypeEnum.BUSINESS_OPPORTUNITY.getCode());
            }
            if( ProjectSourceTypeEnum.PROJECT.getDescription().contains(keyWord)){
                projectOrionWrapper.or().like(ProjectToBasePlan::getSourceType, ProjectSourceTypeEnum.PROJECT.getCode());
            }
        }
        projectOrionWrapper.orderByDesc(Project::getCreateTime);
        projectOrionWrapper.distinct();
        IPage<Project> projectPageRequest = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());

        IPage<Project> pageResult = this.page(projectPageRequest, projectOrionWrapper);

        com.chinasie.orion.sdk.metadata.page.Page<ProjectVO> resultPage = new com.chinasie.orion.sdk.metadata.page.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), pageRequest.getTotalSize());
        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(pageResult.getRecords())) {
            return resultPage;
        }

        List<Project> records = pageResult.getRecords();
        List<ProjectVO> projectToBasePlanList = BeanCopyUtils.convertListTo(records,ProjectVO::new);

        Map<String,List<ProjectVO>> basePlanMap = projectToBasePlanList.stream().filter(p -> StringUtils.hasText(p.getProjectType())).collect(Collectors.groupingBy(ProjectVO :: getProjectType));
        List<ProjectVO> planList = basePlanMap.get(ProjectSourceTypeEnum.PLAN.getCode());
        List<ProjectVO> contractList = basePlanMap.get(ProjectSourceTypeEnum.CONTRACT.getCode());
        List<ProjectVO> declareList = basePlanMap.get(ProjectSourceTypeEnum.DECLARE.getCode());
        List<ProjectVO> leadList = basePlanMap.get(ProjectSourceTypeEnum.LEAD.getCode());
        List<ProjectVO> businessOpportunity = basePlanMap.get(ProjectSourceTypeEnum.BUSINESS_OPPORTUNITY.getCode());
        List<ProjectVO> project = basePlanMap.get(ProjectSourceTypeEnum.PROJECT.getCode());

        Map<String,SchemeVO> schemeVOMap = new HashMap<>();
        Map<String,ProjectContractVO> projectContractMap = new HashMap<>();
        Map<String,LeadManagementVO> leadManagementVOMap = new HashMap<>();
        Map<String,ScientificResearchDemandDeclare> scientificResearchDemandDeclareMap = new HashMap<>();
        Map<String, BusinessOpportunityVO> businessOpportunityMap = new HashMap<>();
        Map<String,Project> projectMap = new HashMap<>();

        if (CollectionUtil.isNotEmpty(planList)){
            List<String> planIds = planList.stream().map(ProjectVO :: getProjectSource).collect(Collectors.toList());
            ResponseDTO<List<SchemeVO>> planRes = planFeignService.listByIds(planIds);
            if(ResponseUtils.success(planRes)){
                List<SchemeVO> list = planRes.getResult();
                if(CollectionUtil.isNotEmpty(list)){
                    schemeVOMap = list.stream().collect(Collectors.toMap(SchemeVO :: getId, Function.identity()));
                }
            }
        }

        if (CollectionUtil.isNotEmpty(contractList)){
            List<String> contractIds = contractList.stream().map(ProjectVO :: getProjectSource).collect(Collectors.toList());
            ResponseDTO<List<ProjectContractVO>> contractRes = pasFeignService.listByIds(contractIds);
            if(ResponseUtils.success(contractRes)){
                List<ProjectContractVO> list = contractRes.getResult();
                if(CollectionUtil.isNotEmpty(list)){
                    projectContractMap = list.stream().collect(Collectors.toMap(ProjectContractVO :: getId, Function.identity()));
                }
            }
        }
        if (CollectionUtil.isNotEmpty(declareList)){
            List<String> declareIds = declareList.stream().map(ProjectVO :: getProjectSource).collect(Collectors.toList());
            List<ScientificResearchDemandDeclare> list = scientificResearchDemandDeclareMapper.selectBatchIds(declareIds);
            if(CollectionUtil.isNotEmpty(list)){
                scientificResearchDemandDeclareMap = list.stream().collect(Collectors.toMap(ScientificResearchDemandDeclare :: getId, Function.identity()));
            }
        }

        if (CollectionUtil.isNotEmpty(leadList)){
            List<String> leadIds = leadList.stream().map(ProjectVO :: getProjectSource).collect(Collectors.toList());
            ResponseDTO<List<LeadManagementVO>> leadRes = pasFeignService.getDeclareLeadList(leadIds);
            if(ResponseUtils.success(leadRes)){
                List<LeadManagementVO> list = leadRes.getResult();
                if(CollectionUtil.isNotEmpty(list)){
                    leadManagementVOMap = list.stream().collect(Collectors.toMap(LeadManagementVO :: getId, Function.identity()));
                }
            }
        }

        if (CollectionUtil.isNotEmpty(businessOpportunity)){
            List<String> businessOpportunityIds = businessOpportunity.stream().map(ProjectVO :: getProjectSource).collect(Collectors.toList());
            ResponseDTO<List<BusinessOpportunityVO>> listByIds = pasFeignService.getListByIds(businessOpportunityIds);
            if(ResponseUtils.success(listByIds)){
                List<BusinessOpportunityVO> list = listByIds.getResult();
                if(CollectionUtil.isNotEmpty(list)){
                    businessOpportunityMap = list.stream().collect(Collectors.toMap(BusinessOpportunityVO :: getId, Function.identity()));
                }
            }
        }
        if (CollectionUtil.isNotEmpty(project)){
            List<String> ids = project.stream().map(ProjectVO :: getProjectSource).collect(Collectors.toList());
            List<Project> list = this.listByIds(ids);
            if(CollectionUtil.isNotEmpty(list)){
                projectMap = list.stream().collect(Collectors.toMap(Project :: getId, Function.identity()));
            }
        }


        resultPage.setTotalSize(pageResult.getTotal());
        Map<String, ProjectContractVO> finalProjectContractMap = projectContractMap;
        Map<String, LeadManagementVO> finalLeadManagementVOMap = leadManagementVOMap;
        Map<String, SchemeVO> finalSchemeVOMap = schemeVOMap;
        Map<String, ScientificResearchDemandDeclare> finalScientificResearchDemandDeclareMap = scientificResearchDemandDeclareMap;
        Map<String, BusinessOpportunityVO> finalBusinessOpportunityMap = businessOpportunityMap;
        Map<String, Project> finalprojectMap = projectMap;
        resultPage.setContent(projectToBasePlanList.stream().peek(m->{

            if (ProjectSourceTypeEnum.CONTRACT.getCode().equals(m.getProjectType())){
                m.setSourceType(ProjectSourceTypeEnum.CONTRACT.getDescription());
                m.setBasePlanName(finalProjectContractMap.getOrDefault(m.getProjectSource(), new ProjectContractVO()).getName());
            }
            if (ProjectSourceTypeEnum.LEAD.getCode().equals(m.getProjectType())){
                m.setSourceType(ProjectSourceTypeEnum.LEAD.getDescription());
                m.setBasePlanName(finalLeadManagementVOMap.getOrDefault(m.getProjectSource(), new LeadManagementVO()).getName());
            }
            if (ProjectSourceTypeEnum.PLAN.getCode().equals(m.getProjectType())){
                m.setSourceType(ProjectSourceTypeEnum.PLAN.getDescription());
                m.setBasePlanName(finalSchemeVOMap.getOrDefault(m.getProjectSource(), new SchemeVO()).getName());
            }
            if (ProjectSourceTypeEnum.DECLARE.getCode().equals(m.getProjectType())){
                m.setSourceType(ProjectSourceTypeEnum.DECLARE.getDescription());
                m.setBasePlanName(finalScientificResearchDemandDeclareMap.getOrDefault(m.getProjectSource(), new ScientificResearchDemandDeclare()).getName());
            }
            if (ProjectSourceTypeEnum.BUSINESS_OPPORTUNITY.getCode().equals(m.getProjectType())){
                m.setSourceType(ProjectSourceTypeEnum.BUSINESS_OPPORTUNITY.getDescription());
                m.setBasePlanName(finalBusinessOpportunityMap.getOrDefault(m.getProjectSource(), new BusinessOpportunityVO()).getName());
            }
            if (ProjectSourceTypeEnum.PROJECT.getCode().equals(m.getProjectType())){
                m.setSourceType(ProjectSourceTypeEnum.PROJECT.getDescription());
                m.setBasePlanName(finalprojectMap.getOrDefault(m.getProjectSource(), new Project()).getName());
            }
        }).collect(Collectors.toList()));
        return resultPage;
    }

    @Override
    public List<ProjectPeopleDayDataVO> getProjectPeopleDayDataList(List<String> projectIdList) throws Exception {
        if (CollectionUtil.isNotEmpty(projectIdList)) {
            Map<String, String> approvalToProjectMap = projectApprovalService.getProjectApprovalByProjectIds(projectIdList)
                    .stream().collect(Collectors.toMap(ProjectApproval::getId, ProjectApproval::getProjectId, (v1, v2) -> v1));
            Map<String, ProjectApprovalEstimate> projectApprovalEstimateMap = projectApprovalEstimateService.getPeopleDataByProjectApprovalIds(new ArrayList<>(approvalToProjectMap.keySet()))
                    .stream().collect(Collectors.toMap(ProjectApprovalEstimate::getProjectApprovalId, Function.identity(), (v1, v2) -> v1));
            if (ObjectUtil.isNotEmpty(projectApprovalEstimateMap)) {
                return projectApprovalEstimateMap.entrySet().stream().map(m -> {
                    ProjectPeopleDayDataVO projectPeopleDayDataVO = new ProjectPeopleDayDataVO();
                    projectPeopleDayDataVO.setLaborFee(m.getValue().getLaborFee());
                    projectPeopleDayDataVO.setPeopleNum(m.getValue().getPeopleNum());
                    projectPeopleDayDataVO.setId(approvalToProjectMap.get(m.getKey()));
                    return projectPeopleDayDataVO;
                }).collect(Collectors.toList());
            }
        }
        return new ArrayList<>();
    }

    @Override
    public com.chinasie.orion.sdk.metadata.page.Page<ProjectByUserVO> pageProjectByUserId(String userId, com.chinasie.orion.sdk.metadata.page.Page<ProjectDTO> pageRequest) {
        List<ProjectRoleUser> projectRoleUsers = projectRoleUserService.list(new LambdaQueryWrapper<>(ProjectRoleUser.class)
                .eq(ProjectRoleUser::getUserId, userId));
        List<String> projectIds = projectRoleUsers.stream().map(ProjectRoleUser::getProjectId).distinct().collect(Collectors.toList());
        com.chinasie.orion.sdk.metadata.page.Page<ProjectByUserVO> result = new com.chinasie.orion.sdk.metadata.page.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        if (CollectionUtil.isEmpty(projectIds)) {
            return result;
        }
        LambdaQueryWrapper<Project> condition = new LambdaQueryWrapper<>(Project.class).in(Project::getId, projectIds);

        Page<Project> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());

        Page<Project> page = this.page(realPageRequest, condition);
        List<ProjectByUserVO> projectVOS = BeanCopyUtils.convertListTo(page.getRecords(), ProjectByUserVO::new);
        result.setContent(projectVOS);
        result.setTotalPages(page.getTotal());

        return result;
    }

    @Override
    public void exportExcelByUserId(String userId, HttpServletResponse response) throws Exception {
        List<ProjectRoleUser> projectRoleUsers = projectRoleUserService.list(new LambdaQueryWrapper<>(ProjectRoleUser.class)
                .eq(ProjectRoleUser::getUserId, userId));
        List<String> projectIds = projectRoleUsers.stream().map(ProjectRoleUser::getProjectId).distinct().collect(Collectors.toList());
        List<ProjectByUserVO> vos;
        if (CollectionUtil.isNotEmpty(projectIds)) {
            LambdaQueryWrapper<Project> condition = new LambdaQueryWrapper<>(Project.class).in(Project::getId, projectIds);
            condition.orderByDesc(Project::getCreateTime);
            List<Project> projectList = projectRepository.selectList(condition);
            vos = BeanCopyUtils.convertListTo(projectList, ProjectByUserVO::new);
        } else {
            vos = new ArrayList<>();
        }
        int i = 1;
        for (ProjectByUserVO vo : vos) {
            vo.setSort(i++);
            vo.setStatusName(vo.getDataStatus() != null ? vo.getDataStatus().getName() : "");
        }
        String fileName = "参与项目导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectByUserVO.class,vos );
    }




    @Override
    public com.chinasie.orion.sdk.metadata.page.Page<ProjectSimpleVO> getProjectSimplePage(com.chinasie.orion.sdk.metadata.page.Page<ProjectDTO> pageRequest) throws Exception {

        com.chinasie.orion.sdk.metadata.page.Page<ProjectSimpleVO> resultPage = new com.chinasie.orion.sdk.metadata.page.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), 0L, new ArrayList<>());
        pmsAuthUtil.setHeaderAuths(resultPage, CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), com.chinasie.orion.sdk.metadata.page.Page::setHeadAuthList, new ArrayList<>());

        LambdaQueryWrapperX<Project> projectOrionWrapper = new LambdaQueryWrapperX<>(Project.class);
        String userId = currentUserHelper.getUserId();

        // 如果通过了立项时间过滤，就只能查立项之后的数据；
        addStatus(pageRequest.getSearchConditions(), projectOrionWrapper);

        SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), projectOrionWrapper);

        IPage<Project> projectPageRequest = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());

        projectOrionWrapper.orderByDesc(Project::getCreateTime);

        LambdaQueryWrapper<ProjectRoleUser> projectRoleUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
        projectRoleUserLambdaQueryWrapper.eq(ProjectRoleUser :: getUserId,userId);
        List<ProjectRoleUser> projectRoleUsers =  projectRoleUserService.list(projectRoleUserLambdaQueryWrapper);
        List<String> projectIds = new ArrayList<>();
        if(!CollectionUtils.isEmpty(projectRoleUsers)){
            projectIds = projectRoleUsers.stream().map(ProjectRoleUser :: getProjectId).collect(Collectors.toList());
        }
        if(CollectionUtils.isEmpty(projectIds)){
            return resultPage;
        }
        else{
            projectOrionWrapper.in(Project :: getId,projectIds);
        }
        IPage<Project> pageResult = this.page(projectPageRequest, projectOrionWrapper);
        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(pageResult.getRecords())) {
            return resultPage;
        }
        List<Project> projectList = pageResult.getRecords();


        List<ProjectSimpleVO> projectVOList = BeanCopyUtils.convertListTo(projectList, ProjectSimpleVO::new);

        //获取项目经理  项目角色和项目角色用户可以考虑好 合并一起
        List<String> projectIdList = projectVOList.stream().map(ProjectSimpleVO::getId).collect(Collectors.toList());
        List<String> pmIdList = projectRoleService.list(new LambdaQueryWrapperX<>(ProjectRole.class)
                        .select(ProjectRole::getId)
                        .in(ProjectRole::getProjectId, projectIdList)
                        .eq(ProjectRole::getCode, ProjectRoleEnum.ROLE_XMJL.getCode()))
                .stream().map(ProjectRole::getId).collect(Collectors.toList());
        Map<String, List<String>> pmMap = new HashMap<>();
        Map<String, UserVO> userVOMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(pmIdList)) {
            List<ProjectRoleUser> projectRoleUserList = projectRoleUserService.list(new LambdaQueryWrapperX<>(ProjectRoleUser.class)
                    .select(ProjectRoleUser::getProjectId, ProjectRoleUser::getUserId)
                    .in(ProjectRoleUser::getProjectRoleId, pmIdList)
                    .orderByDesc(ProjectRoleUser::getCreateTime));
            pmMap.putAll(projectRoleUserList.stream().collect(Collectors.groupingBy(ProjectRoleUser::getProjectId,
                    Collectors.mapping(ProjectRoleUser::getUserId, Collectors.toList()))));
            List<String> userIdList = projectRoleUserList.stream().map(ProjectRoleUser::getUserId).distinct().collect(Collectors.toList());
            userVOMap.putAll(userRedisHelper.getUserMapByUserIds(userIdList));
        }
        projectVOList.forEach(f -> {
            if (pmMap.containsKey(f.getId())) {
                for (String pm : pmMap.get(f.getId())) {
                    if (userVOMap.containsKey(pm)) {
                        f.setPm(userVOMap.get(pm).getName());
                        f.setPmId(pm);
                        break;
                    }
                }
            }
        });

        resultPage.setTotalSize(pageResult.getTotal());
        resultPage.setContent(projectVOList);


        return resultPage;
    }

    @Override
    public List<String> getProjectIdsForStatus() throws Exception {
        List<Project> list = this.list(new LambdaQueryWrapperX<>(Project.class).select(Project::getId).eq(Project::getStatus, ProjectStatusEnum.PAUSED.getStatus()).or().eq(Project::getStatus, ProjectStatusEnum.TERMINATED.getStatus()));
        if (CollectionUtil.isEmpty(list)){
            return new ArrayList<>();
        }
        return list.stream().map(Project::getId).collect(Collectors.toList());
    }

    /**
     * 根据用户查询用户参与的项目
     * 根据员工号查询员工参与了哪些项目（员工主导的项目排前面，员工参与的项目排后面）
     *
     * @param query 用户编码或用户姓名
     * @return list
     */
    @Override
    @OrionDataPermission(tenantLine = TenantLineEnum.CUSTOM)
    public List<ProjectAndUserTrdVO> queryProjectByUser(ProjectAndUserTrdUserQuery query) {
        LambdaQueryWrapperX<Project> condition = new LambdaQueryWrapperX<>(Project.class);
        condition.selectAs(Project::getName, ProjectAndUserTrdVO::getProjectName)
                .selectAs(Project::getNumber, ProjectAndUserTrdVO::getProjectNumber);

        condition.leftJoin(ProjectRoleUser.class, "pr", on -> on.eq(ProjectRoleUser::getProjectId, Project::getId));

        condition.leftJoin(ProjectRole.class, "role", on -> on.eq(ProjectRole::getId, ProjectRoleUser::getProjectRoleId))
                .selectAs(ProjectRole::getName, ProjectAndUserTrdVO::getRoleName);

        condition.leftJoin(UserDO.class, "us", on -> on.eq(UserDO::getId, ProjectRoleUser::getUserId))
                .selectAs(UserDO::getCode, ProjectAndUserTrdVO::getUserCode)
                .selectAs(UserDO::getName, ProjectAndUserTrdVO::getUserName);

        condition.like(StringUtils.hasText(query.getUserCode()), UserDO::getCode, query.getUserCode());
        condition.like(StringUtils.hasText(query.getUserName()), UserDO::getName, query.getUserName());

        return this.selectJoinList(ProjectAndUserTrdVO.class, condition);
    }

    /**
     * 根据项目查找项目下参与的成员
     * 需要根据项目编号 和 项目名称（模糊查询） 可以查到项目下面有哪些项目成员信息
     *
     * @param query 项目编码或项目名称
     * @return list
     */
    @Override
    @OrionDataPermission(tenantLine = TenantLineEnum.CUSTOM)
    public List<ProjectAndUserTrdVO> queryUserByProject(ProjectAndUserTrdProjectQuery query) {
        LambdaQueryWrapperX<Project> condition = new LambdaQueryWrapperX<>(Project.class);
        condition.selectAs(Project::getName, ProjectAndUserTrdVO::getProjectName)
                .selectAs(Project::getNumber, ProjectAndUserTrdVO::getProjectNumber);

        condition.leftJoin(ProjectRoleUser.class, "pr", on -> on.eq(ProjectRoleUser::getProjectId, Project::getId));

        condition.leftJoin(ProjectRole.class, "role", on -> on.eq(ProjectRole::getId, ProjectRoleUser::getProjectRoleId))
                .selectAs(ProjectRole::getName, ProjectAndUserTrdVO::getRoleName);

        condition.leftJoin(UserDO.class, "us", on -> on.eq(UserDO::getId, ProjectRoleUser::getUserId))
                .selectAs(UserDO::getCode, ProjectAndUserTrdVO::getUserCode)
                .selectAs(UserDO::getName, ProjectAndUserTrdVO::getUserName);

        condition.like(StringUtils.hasText(query.getProjectName()), Project::getName, query.getProjectName());
        condition.like(StringUtils.hasText(query.getProjectNumber()), Project::getNumber, query.getProjectNumber());

        return this.selectJoinList(ProjectAndUserTrdVO.class, condition);
    }

    @Override
    public com.chinasie.orion.sdk.metadata.page.Page<ProjectVO> getPage(com.chinasie.orion.sdk.metadata.page.Page<ProjectDTO> pageRequest) {
        IPage<Project> projectPageRequest = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        LambdaQueryWrapperX<Project> projectOrionWrapper = new LambdaQueryWrapperX<>(Project.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), projectOrionWrapper);
        }
        IPage<Project> pageResult = this.page(projectPageRequest,projectOrionWrapper);
        com.chinasie.orion.sdk.metadata.page.Page<ProjectVO> resultPage = new com.chinasie.orion.sdk.metadata.page.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(pageResult.getRecords())) {
            return resultPage;
        }

        List<Project> projectList = pageResult.getRecords();
        List<ProjectVO> projectVOList = BeanCopyUtils.convertListTo(projectList,ProjectVO::new);


        resultPage.setTotalSize(pageResult.getTotal());
        resultPage.setContent(projectVOList);
        return resultPage;
    }

    @Override
    public List<ProjectSimpleBasicVO> getProjectBasicInfoList() {
        List<ProjectSimpleBasicVO> projectBasicInfoList =    projectRepository.getProjectBasicInfoList();
        return projectBasicInfoList;
    }

    @Override
    public Integer getStatus(String projectId) {
        LambdaQueryWrapperX<Project> condition = new LambdaQueryWrapperX<>(Project.class);
        condition.eq(Project::getId, projectId);
        condition.select(Project::getStatus);
        Project project = this.getOne(condition);
        if (Objects.nonNull(project)) {
            return project.getStatus();
        }
        return 0;
    }

}
