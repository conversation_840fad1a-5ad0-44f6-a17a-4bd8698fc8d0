package com.chinasie.orion.service.approval;

import java.lang.String;
import java.math.BigDecimal;
import java.util.List;
import com.chinasie.orion.domain.dto.approval.ProjectApprovalEstimateExpenseSubjectDTO;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalEstimateExpenseSubject;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalEstimateExpenseSubjectVO;
import com.chinasie.orion.domain.vo.approval.SubjectSummaryVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;

/**
 * <p>
 * ProjectApprovalEstimateExpenseSubject 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-06 10:05:25
 */
public interface ProjectApprovalEstimateExpenseSubjectService  extends OrionBaseService<ProjectApprovalEstimateExpenseSubject>{


    /**
     * 批量编辑科目概算金额
     * @param list
     * @return
     * @throws Exception
     */
    Boolean editAmountBatch(List<ProjectApprovalEstimateExpenseSubjectDTO> list) throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;

    /**
     * 引入模板
     * @param approvalId
     * @param estimateTemplateId
     * @return
     * @throws Exception
     */
    Boolean addTemplate(String approvalId, String estimateTemplateId) throws Exception;

    /**
     * 添加科目
     * @param projectApprovalEstimateExpenseSubjectDTOList
     * @param projectApprovalId
     * @return
     * @throws Exception
     */
    Boolean addExpenseSubject(List<ProjectApprovalEstimateExpenseSubjectDTO> projectApprovalEstimateExpenseSubjectDTOList, String projectApprovalId) throws Exception;

    /**
     * 获取概算科目树
     * @param approvalId
     * @return
     * @throws Exception
     */
    List<ProjectApprovalEstimateExpenseSubjectVO> getExpenseSubjectList(String approvalId) throws Exception;

    /**
     * 计算
     * @param id
     * @param list
     * @return
     * @throws Exception
     */
    BigDecimal calculate(String id, List<ProjectApprovalEstimateExpenseSubjectDTO> list) throws Exception;

    /**
     * param
     * @param approvalId
     * @param list
     * @return
     * @throws Exception
     */
    List<ProjectApprovalEstimateExpenseSubjectVO> calculateBatch(String approvalId, List<ProjectApprovalEstimateExpenseSubjectDTO> list) throws Exception;

    /**
     * 科目汇总
     * @param approvalId
     * @return
     * @throws Exception
     */
    SubjectSummaryVO subjectSummary(String approvalId) throws Exception;

    public List<ProjectApprovalEstimateExpenseSubjectVO> getExpenseSubjectListByApprovalId(String approvalId) throws Exception;

}
