package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.permission.RoleParamDTO;
import com.chinasie.orion.domain.entity.MajorRepairPlanRole;
import com.chinasie.orion.domain.dto.MajorRepairPlanRoleDTO;
import com.chinasie.orion.domain.vo.MajorRepairPlanRoleVO;

import java.lang.String;
import java.util.List;


import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * MajorRepairPlanRole 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30 19:21:00
 */
public interface MajorRepairPlanRoleService extends OrionBaseService<MajorRepairPlanRole> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    MajorRepairPlanRoleVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param majorRepairPlanRoleDTO
     */
    String create(MajorRepairPlanRoleDTO majorRepairPlanRoleDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param majorRepairPlanRoleDTO
     */
    Boolean edit(MajorRepairPlanRoleDTO majorRepairPlanRoleDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<MajorRepairPlanRoleVO> pages(Page<MajorRepairPlanRoleDTO> pageRequest) throws Exception;


    /**
     * 通过层级分页
     * <p>
     * * @param pageRequest
     */
    Page<MajorRepairPlanRoleVO> pagesByLevel(Page<MajorRepairPlanRoleDTO> pageRequest) throws Exception;


    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<MajorRepairPlanRoleVO> vos) throws Exception;

    Boolean batchAdd(RoleParamDTO roleParamDTO);

    /**
     *  列表
     * @param majorRepairPlanRoleDTO
     * @return
     */
    List<MajorRepairPlanRoleVO> listByEntity(MajorRepairPlanRoleDTO majorRepairPlanRoleDTO) throws Exception;

    MajorRepairPlanRole getEntityByRoundAndCode(String repairRound, String code);
}
