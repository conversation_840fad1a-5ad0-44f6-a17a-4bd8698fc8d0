package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.util.List;
/**
 * ProjectSchemeBom VO对象
 *
 * <AUTHOR>
 * @since 2024-06-22 14:50:50
 */
@ApiModel(value = "ProjectSchemeBomVO对象", description = "项目计划bom信息表")
@Data
public class ProjectSchemeBomVO    implements Serializable{

            /**
         * 物料名称
         */
        @ApiModelProperty(value = "物料名称")
        private String materielName;


        /**
         * 物料编码
         */
        @ApiModelProperty(value = "物料编码")
        private String materielCode;


        /**
         * 版本
         */
        @ApiModelProperty(value = "版本")
        private String version;


        /**
         * 物料类型
         */
        @ApiModelProperty(value = "物料类型")
        private String materielType;


        /**
         * 数量
         */
        @ApiModelProperty(value = "数量")
        private String amount;


        /**
         * 编码
         */
        @ApiModelProperty(value = "编码")
        private String useNum;


        /**
         * 物料状态
         */
        @ApiModelProperty(value = "物料状态")
        private String materielStatus;


        /**
         * plm创建人id
         */
        @ApiModelProperty(value = "plm创建人id")
        private String plmCreator;


        /**
         * 创建时间
         */
        @ApiModelProperty(value = "创建时间")
        private String plmCreateTime;


        /**
         * 修改人
         */
        @ApiModelProperty(value = "修改人")
        private String plmModify;


        /**
         * 修改时间
         */
        @ApiModelProperty(value = "修改时间")
        private String plmLastModifyTime;


        /**
         * 计划id
         */
        @ApiModelProperty(value = "计划id")
        private String schemeId;


        /**
         * 父级id
         */
        @ApiModelProperty(value = "父级id")
        private String parentCode;


    

}
