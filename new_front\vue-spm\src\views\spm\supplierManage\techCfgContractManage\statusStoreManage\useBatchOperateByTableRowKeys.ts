import { createVNode, h, ref } from 'vue';
import Api from '/@/api';
import { message, Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { get, map } from 'lodash-es';
import { createGlobalState } from '@vueuse/shared';
import dayjs from 'dayjs';

export const useBatchOperateByTableRowKeys = createGlobalState(() => {
  const selectRows = ref([]);
  function setSelectRows(rows) {
    selectRows.value = rows;
  }
  async function batchDeleteRows(updateContractKey?:any) {
    Modal.confirm({
      title: '系统提示！',
      content: '确认要删除全这些数据',
      onOk() {
        const bodyParams = map(selectRows.value, (row) => ({
          year: get(row, 'year'),
          contractNumber: get(row, 'contractNumber'),
        }));
        return new Promise((resolve, reject) => {
          new Api('/spm/contractMain/remove').fetch(bodyParams, '', 'DELETE').then((res) => {
            message.success('删除成功');
            updateContractKey?.();
            resolve({});
          });
        });
      },
    });
  }
  async function openNextYearEntry(updateContractKey?:any) {
    Modal.confirm({
      title: '系统提示！',
      content: `确认要开启${Number(dayjs().format('YYYY')) + 1}年录入吗`,
      centered: true,
      onOk() {
        return new Promise((resolve, reject) => {
          new Api('/spm/contractMain/action/next').fetch([], '', 'POST')
            .then((res) => {
              message.success('操作成功');
              updateContractKey?.();
              resolve({});
            });
        });
      },
    });
  }
  async function openCurrentYearAdjustment(updateContractKey?:any) {
    Modal.confirm({
      title: '系统提示！',
      content: '确认要开启当年调整吗',
      centered: true,
      onOk() {
        return new Promise((resolve, reject) => {
          new Api('/spm/contractMain/begin/edit').fetch([], '', 'POST')
            .then((res) => {
              message.success('操作成功');
              updateContractKey?.();
              resolve({});
            });
        });
      },
    });
  }
  async function issueAllContractPlan(updateContractKey?:any) {
    Modal.confirm({
      title: '确认提示',
      icon: h(ExclamationCircleOutlined, {
        style: {
          color: 'rgba(24, 144, 255, 1)',
        },
      }),
      centered: true,
      content: createVNode('div', {}, '请确认是否将技术配置合同下发至中心进行填报？'),
      async  onOk() {
        try {
          const result = await new Api('/spm/contractMain/issued').fetch([], '', 'POST');
          message.success('下发成功');
          updateContractKey?.();
        } catch (e) {}
      },
      onCancel() {},
      class: 'test',
    });
  }

  return {
    selectRows,
    setSelectRows,
    batchDeleteRows,
    openNextYearEntry,
    openCurrentYearAdjustment,
    issueAllContractPlan,
  };
});