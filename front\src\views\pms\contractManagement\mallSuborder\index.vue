<script lang="ts" setup>
// 详情顶部数据
import {
  computed, onMounted, provide, ref, Ref, unref,
} from 'vue';
import {
  BasicButton, Layout3, Layout3Content, openModal,
} from 'lyra-component-vue3';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import BasicOrderInfo from './basicOrderInfo/BasicOrderInfo.vue';
import PublishOrderModel from './components/PublishOrderModel.vue';
import Api from '/@/api';
import { declarationBasicOrderId } from './token/injectionKey';

interface MenuItem {
  id: string,
  name: string,
  children?: MenuItem[]
}

const route = useRoute();
const orderId: Ref<string> = ref(route.params.id as string);
provide(declarationBasicOrderId, orderId);

const basePublishForm = ref();
const loading: Ref<boolean> = ref(false);
const layoutData = computed(() => ({
  name: `合同名称「${orderId.value}」`,
  ownerName: '商城订单',
  dataStatus: 1,
  projectCode: `订单编号：${basicOrderInfo.value?.order?.orderNumber}`,
}));
const defaultActionId: Ref<string> = ref('jBXX');
const menuData: Ref<MenuItem[]> = ref([]);
const basicOrderInfo = ref({
  receive: {}, // 收货信息
  other: {}, // 其他信息
  invoice: {}, // 发票信息
  flow: {}, // 流程信息
  order: {}, // 订单信息
});
const commodityList = ref({});

function getPowerDataHandle(data) {
  menuData.value = menuData.value.concat([
    {
      id: 'jBXX',
      name: '基本信息',
    },
    {
      id: 'sPXX',
      name: '商品信息',
    },
    {
      id: 'fJ',
      name: '附件',
    },
  ]);
}

const getBasicOrderInfo = async () => {
  try {
    loading.value = true;
    const result = await new Api('/pms/projectOrder/getDetail').fetch({
      orderNumber: orderId.value,
    }, '', 'POST');
    basicOrderInfo.value = {
      ...result,
    };
  } finally {
    loading.value = false;
  }
};
const getCommodityListById = async () => {
  try {
    const result = await new Api('/pms//projectInventory/getList').fetch({
      orderNumber: orderId.value,
    }, '', 'POST');
    commodityList.value = {
      ...result,
    };
  } finally {
  }
};

function menuChange(option: { id: string, index: number, item: MenuItem }): void {
  defaultActionId.value = option.id;
}

const handleOrderPublish = () => {
  const modal = openModal({
    title: '商城订单分发',
    width: 1000,
    content(h) {
      return h(PublishOrderModel, {
        ref: basePublishForm,
      });
    },
    onOk: async () => {
      const formRefHandler = basePublishForm.value?.resolveValidatedForm;
      try {
        const result = await formRefHandler();
        const body = {
          ...result,
          id: unref(orderId),
        };
        await new Api('/pms/projectOrder/edit').fetch(body, '', 'PUT');
        await message.success('操作成功');
        await getBasicOrderInfo();
        await getCommodityListById();
      } catch (e) {
        return Promise.reject();
      }
    },
  });
};

onMounted(async () => {
  await getPowerDataHandle({});
  await getBasicOrderInfo();
  await getCommodityListById();
});
</script>

<template>
  <Layout3
    :menuData="menuData"
    :onMenuChange="menuChange"
    :projectData="layoutData"
    :type="2"
  >
    <template #header-right>
      <BasicButton
        ghost
        icon="sie-icon-chakantuisong"
        type="primary"
        @click="handleOrderPublish"
      >
        订单分发
      </BasicButton>
    </template>
    <div
      v-if="loading"
      class="w-full h-full flex flex-pc flex-ac"
    >
      <Spin />
    </div>
    <Layout3Content v-else>
      <BasicOrderInfo
        v-if="defaultActionId==='jBXX'"
        :data-props="basicOrderInfo"
      />
      <CommodityList
        v-if="defaultActionId==='sPXX'"
        :commodity-list="commodityList"
      />
    </Layout3Content>
  </Layout3>
</template>

<style lang="less" scoped>

</style>