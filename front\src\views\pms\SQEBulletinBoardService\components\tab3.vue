<!--
 * @Description:隐患排查
 * @Autor: laotao117
 * @Date: 2024-08-22 19:21:33
 * @LastEditors: laotao117
 * @LastEditTime: 2024-08-28 11:47:11
-->
<template>
  <div>
    <BasicCard
      :isBorder="true"
      title="部门显示设置"
      class="card-border active-box"
    >
      <OrionTable
        ref="tableRef"
        class="radio-button-table"
        :options="tableOptions"
      />
    </BasicCard>
  </div>
</template>
<script setup lang="ts">
import {
  Icon, isPower, Layout, OrionTable, BasicButton, BasicCard,
} from 'lyra-component-vue3';
// Api
import Api from '/@/api';
import {
  h, nextTick, provide, ref, Ref, watch, reactive,
} from 'vue';
import { Checkbox, message } from 'ant-design-vue';
const tableRef: Ref = ref();
const selectedKeys: Ref<string[]> = ref([]);
const selectedRows: Ref<any[]> = ref([]);
// tableOptions 指标名称（事件等级）
const tableOptions = {
  // showSmallSearch: false,
  rowSelection: false,
  height: 500,
  smallSearchField: ['deptName', 'deptNumber'],
  //   分页隐藏
  // pagination: false,

  api: (params) => new Api('/pms').fetch({ ...params }, '/ampere_ring/board/config/dept/config/page', 'POST'),
  showToolButton: false,
  columns: [
    {
      title: '编号',
      dataIndex: 'deptCode',
    },
    {
      title: '部门名称',
      dataIndex: 'deptName',
    },
    {
      title: '部门编码',
      dataIndex: 'deptNumber',
    },
    {
      title: '在看板中展示',
      dataIndex: 'isCheckProblemsShow',
      // Checkbox
      customRender: ({ record }) => h(Checkbox, {
        checked: record.isCheckProblemsShow,
        onChange: (e) => {
          save(e, record);
        },
      }),
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 220,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '上移',
      onClick: (record: any) => {
        handleMove(record.id, 'up');
      },
    },
    {
      text: '下移',
      onClick: (record: any) => {
        handleMove(record.id, 'down');
      },
    },
    {
      text: '置顶',
      onClick: (record: any) => {
        handleMove(record.id, 'top');
      },
    },
    {
      text: '置底',
      onClick: (record: any) => {
        handleMove(record.id, 'bottom');
      },
    },

  ],

};
// updateTable
function updateTable() {
  tableRef.value.reload();
}
// 移动
function handleMove(id, type) {
  new Api('/pms/ampere_ring/board/config/dept/config/move').fetch({
    id,
    operationType: type,
  }, '', 'POST').then(() => {
    message.success('操作成功');
    updateTable();
  });
}
// 是否展示
// POST
// /pms/ampere_ring/board/config/dept/config/isShow
function save(e, record) {
  const data = {
    id: record.id,
    deptCode: record.deptCode,
    isCheckProblemsShow: e.target.checked,
  };
  new Api('/pms').fetch(data, '/ampere_ring/board/config/dept/config/isShow', 'POST').then(() => {
    tableRef.value.reload();
  });
}

</script>
<style scoped lang="less">
.card-border {
  // border: 1px solid var(--ant-border-color-base);
  padding: 15px 0px;
  margin: 0 !important;
}
</style>