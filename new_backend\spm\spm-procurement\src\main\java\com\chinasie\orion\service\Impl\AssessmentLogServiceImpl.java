package com.chinasie.orion.service.Impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.CenterPlanEnum;
import com.chinasie.orion.domain.dto.AssessmentLogDTO;
import com.chinasie.orion.domain.entity.AssessmentLog;
import com.chinasie.orion.domain.vo.AssessmentLogVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.AssessmentLogMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.AssessmentLogService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * AssessmentLog 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-17 09:28:12
 */
@Service
@Slf4j
public class AssessmentLogServiceImpl extends  OrionBaseServiceImpl<AssessmentLogMapper, AssessmentLog>   implements AssessmentLogService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  AssessmentLogVO detail(String id,String pageCode) throws Exception {
        AssessmentLog assessmentLog =this.getById(id);
        AssessmentLogVO result = BeanCopyUtils.convertTo(assessmentLog,AssessmentLogVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param assessmentLogDTO
     */
    @Override
    public  String create(AssessmentLogDTO assessmentLogDTO) throws Exception {
        AssessmentLog assessmentLog =BeanCopyUtils.convertTo(assessmentLogDTO,AssessmentLog::new);
        this.save(assessmentLog);

        String rsp=assessmentLog.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param assessmentLogDTO
     */
    @Override
    public Boolean edit(AssessmentLogDTO assessmentLogDTO) throws Exception {
        AssessmentLog assessmentLog =BeanCopyUtils.convertTo(assessmentLogDTO,AssessmentLog::new);

        this.updateById(assessmentLog);

        String rsp=assessmentLog.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<AssessmentLogVO> pages( Page<AssessmentLogDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<AssessmentLog> condition = new LambdaQueryWrapperX<>( AssessmentLog. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(AssessmentLog::getCreateTime);


        Page<AssessmentLog> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), AssessmentLog::new));

        PageResult<AssessmentLog> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<AssessmentLogVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<AssessmentLogVO> vos = BeanCopyUtils.convertListTo(page.getContent(), AssessmentLogVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "审核记录表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", AssessmentLogDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            AssessmentLogExcelListener excelReadListener = new AssessmentLogExcelListener();
        EasyExcel.read(inputStream,AssessmentLogDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<AssessmentLogDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("审核记录表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<AssessmentLog> assessmentLoges =BeanCopyUtils.convertListTo(dtoS,AssessmentLog::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::AssessmentLog-import::id", importId, assessmentLoges, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<AssessmentLog> assessmentLoges = (List<AssessmentLog>) orionJ2CacheService.get("pmsx::AssessmentLog-import::id", importId);
        log.info("审核记录表导入的入库数据={}", JSONUtil.toJsonStr(assessmentLoges));

        this.saveBatch(assessmentLoges);
        orionJ2CacheService.delete("pmsx::AssessmentLog-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::AssessmentLog-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<AssessmentLog> condition = new LambdaQueryWrapperX<>( AssessmentLog. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(AssessmentLog::getCreateTime);
        List<AssessmentLog> assessmentLoges =   this.list(condition);

        List<AssessmentLogDTO> dtos = BeanCopyUtils.convertListTo(assessmentLoges, AssessmentLogDTO::new);

        String fileName = "审核记录表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", AssessmentLogDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<AssessmentLogVO> vos)throws Exception {

        Map<Integer, String> statusToName = CenterPlanEnum.getMap();
        vos.forEach(vo->{
            vo.setStatusName(statusToName.getOrDefault(vo.getStatus(),""));
        });


    }




    public static class AssessmentLogExcelListener extends AnalysisEventListener<AssessmentLogDTO> {

        private final List<AssessmentLogDTO> data = new ArrayList<>();

        @Override
        public void invoke(AssessmentLogDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<AssessmentLogDTO> getData() {
            return data;
        }
    }


}
