package com.chinasie.orion.constant;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2023/03/22/13:56
 * @description:
 */
public enum FormDataTypeEnums {
    TEXT("text","文本"),
    VARCHAR("string","字符串"),
    INTEGER("integer","整型"),
    DECIMAL("decimal","金额"),
    NUMBER("number","数字型"),
    BOOLEAN("boolean","布尔型"),
    DATETIME("dateTime","日期时间类型"),
    DATE("date","日期类型"),
    TIME("time","时间类型"),
    DICT("dict","字典类型"),
    USER("user","用户类型"),
    DEPT("dept","部门类型"),
    ;

    private String key;


    private String desc;

    public String getKey() {
        return key;
    }

    FormDataTypeEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    /**
     *  通过 映射的key 返回整个属性
     * @param key
     * @return
     */
    public static FormDataTypeEnums  getKey(String key){
        FormDataTypeEnums[] values = FormDataTypeEnums.values();
        for (FormDataTypeEnums value : values) {
            String key1 = value.key;
            if(key1.equals(key)){
                return  value;
            }
        }
        return  null;
    }
}
