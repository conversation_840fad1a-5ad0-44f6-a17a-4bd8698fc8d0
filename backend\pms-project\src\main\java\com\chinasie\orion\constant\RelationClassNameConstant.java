package com.chinasie.orion.constant;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/1/19 10:02
 */
public class RelationClassNameConstant {
    /**
     *  项目状态 的类名
     */
    public static final String PROJECT_TASK_STATUS = "ProjectTaskStatus";

    /**
     *  项目状态 的类名
     */
    public static final String TASK_SUBJECT = "TaskSubject";

    /**
     *  项目角色用户 的类名
     */
    public static final String PROJECT_ROLE_USER = "ProjectRoleUser";

    /**
     *  风险管理 的类名
     */
    public static final String RISK_MANAGEMENT = "RiskManagement";

    /**
     *  风险管理 的类名
     */
    public static final String RISK_PLAN = "RiskPlan";

    /**
     *  问题管理 的类名
     */
    public static final String QUESTION_MANAGEMENT = "QuestionManagement";

    /**
     *  需求管理 的类名
     */
    public static final String DEMAND_MANAGEMENT = "DemandManagement";

    /**
     *  计划 的类名
     */
    public static final String PLAN = "ProjectScheme";

    /**
     *  交付物 的类名
     */
    public static final String DELIVERABLE = "Deliverable";



    /**
     *  文档类型 的类名
     */
    public static final String DOCUMENT_TYPE = "DocumentType";

    /**
     *  结项管理 的类名
     */
    public static final String POST_PROJECT = "PostProject";
    /**
     * 验收管理 的类名
     */
    public static final String ACCEPTANCE_FORM = "AcceptanceForm";
    /**
     * 评价管理 的类名
     */
    public static final String EVALUATION_PROJECT = "EvaluationProject";
    /**
     *  变更管理 的类名
     */
    public static final String CHANGE_MANAGEMENT = "ChangeManagement";

    /**
     *  接口-传递单
     */
    public static final String INTERFACE_MANAGEMENT = "InterfaceManagement";
    /**
     *  接口-意见单
     */
    public static final String IDEA_FORM = "IdeaForm";
}
