package com.chinasie.orion.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.constant.IndicatorStatusEnum;
import com.chinasie.orion.domain.dto.PerformanceTemplateDTO;
import com.chinasie.orion.domain.dto.PerformanceTemplateToIndicatorDTO;
import com.chinasie.orion.domain.entity.IndicatorLibrary;
import com.chinasie.orion.domain.entity.PerformanceTemplate;
import com.chinasie.orion.domain.entity.PerformanceTemplateToIndicator;
import com.chinasie.orion.domain.vo.PerformanceTemplateVO;
import com.chinasie.orion.exception.BaseErrorCode;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.PerformanceTemplateMapper;
import com.chinasie.orion.sdk.helper.BusinessOrgPrivilegeRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.IndicatorLibraryService;
import com.chinasie.orion.service.PerformanceTemplateService;
import com.chinasie.orion.service.PerformanceTemplateToIndicatorService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.lang.String;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * PerformanceTemplate 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-26 19:59:56
 */
@Service
public class PerformanceTemplateServiceImpl extends OrionBaseServiceImpl<PerformanceTemplateMapper, PerformanceTemplate> implements PerformanceTemplateService, InitializingBean, Ordered {

    @Autowired
    private PerformanceTemplateMapper performanceTemplateMapper;
    @Autowired
    private IndicatorLibraryService indicatorLibraryService;

    @Autowired
    private PerformanceTemplateToIndicatorService performanceTemplateToIndicatorService;

    @Resource
    private BusinessOrgPrivilegeRedisHelper businessOrgPrivilegeRedisHelper;

    /***
     * 初始化默认类型数据
     * @param
     * @return void
     * <AUTHOR>
     * @date 2024/3/26       
     */
    @Override
    public void afterPropertiesSet() throws Exception {
//        List<String> getAllTenantIds = businessOrgPrivilegeRedisHelper.getAllTenantIds();
//        for (String tenantId : getAllTenantIds) {
//            String orgId = tenantId;
//            if (StringUtils.hasText(orgId)) {
//                List<PerformanceTemplate> dbEcrTypes = this.list(new LambdaQueryWrapper<>(PerformanceTemplate.class)
//                        .in(PerformanceTemplate::getCode, Arrays.asList("monthly", "quarterly", "semi-annually", "annually", "summary"))
//                        .eq(PerformanceTemplate::getOrgId, orgId)
//                );
//                if (CollectionUtils.isEmpty(dbEcrTypes)) {
//                    List<PerformanceTemplate> docConfS = new ArrayList<>() {{
//                        add(new PerformanceTemplate("月度绩效考核指标", orgId, "monthly"));
//                        add(new PerformanceTemplate("季度绩效考核指标", orgId, "quarterly"));
//                        add(new PerformanceTemplate("半年度绩效考核指标", orgId, "semi-annually"));
//                        add(new PerformanceTemplate("年度绩效考核指标", orgId, "annually"));
//                        add(new PerformanceTemplate("最终绩效考核指标", orgId, "summary"));
//
//                    }};
//                    this.saveBatch(docConfS);
//                }
//            }
//        }
    }

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public PerformanceTemplateVO detail(String id) throws Exception {
        PerformanceTemplate performanceTemplate = performanceTemplateMapper.selectById(id);
        PerformanceTemplateVO result = BeanCopyUtils.convertTo(performanceTemplate, PerformanceTemplateVO::new);
        LambdaQueryWrapperX<PerformanceTemplateToIndicator> lambdaQueryWrapperX = new LambdaQueryWrapperX(PerformanceTemplateToIndicator.class);
        lambdaQueryWrapperX.eq(PerformanceTemplateToIndicator::getTemplateId, id);
        List<PerformanceTemplateToIndicator> list = performanceTemplateToIndicatorService.list(lambdaQueryWrapperX);
        result.setPerformanceTemplateToIndicatorDTOS(BeanCopyUtils.convertListTo(list, PerformanceTemplateToIndicatorDTO::new));
        return result;
    }

    /**
     * 新增
     * <p>
     * * @param performanceTemplateDTO
     */
    @Override
    public PerformanceTemplateVO create(PerformanceTemplateDTO performanceTemplateDTO) throws Exception {
        PerformanceTemplate performanceTemplate = BeanCopyUtils.convertTo(performanceTemplateDTO, PerformanceTemplate::new);
        int insert = performanceTemplateMapper.insert(performanceTemplate);
        PerformanceTemplateVO rsp = BeanCopyUtils.convertTo(performanceTemplate, PerformanceTemplateVO::new);
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param performanceTemplateDTO
     */
    @Override
    public Boolean edit(PerformanceTemplateDTO performanceTemplateDTO) throws Exception {
        PerformanceTemplate performanceTemplate = BeanCopyUtils.convertTo(performanceTemplateDTO, PerformanceTemplate::new);
        int update = performanceTemplateMapper.updateById(performanceTemplate);
        return SqlHelper.retBool(update);
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        //TODO 6/12 审查 吴锋  边界问题 为空怎么办
        LambdaQueryWrapperX<PerformanceTemplateToIndicator> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(PerformanceTemplateToIndicator.class);
        lambdaQueryWrapperX.in(PerformanceTemplateToIndicator::getTemplateId, ids);
        lambdaQueryWrapperX.eq(PerformanceTemplateToIndicator::getStatus, IndicatorStatusEnum.ENABLE);
        List<PerformanceTemplateToIndicator> templateToIndicatorList = performanceTemplateToIndicatorService.list(lambdaQueryWrapperX);
        if (!CollectionUtils.isEmpty(templateToIndicatorList)) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL, "模版下关联了指标，不能直接删除！");
        }
        int delete = performanceTemplateMapper.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public Page<PerformanceTemplateVO> pages(Page<PerformanceTemplateDTO> pageRequest) throws Exception {
        Page<PerformanceTemplate> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), PerformanceTemplate::new));
        PageResult<PerformanceTemplate> page = performanceTemplateMapper.selectPage(realPageRequest, null);
        Page<PerformanceTemplateVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<PerformanceTemplateVO> vos = BeanCopyUtils.convertListTo(page.getContent(), PerformanceTemplateVO::new);
        pageResult.setContent(vos);
        return pageResult;
    }

    @Override
    public List<IndicatorLibrary> filterIndicatorLibrary(String templateId) {
        List<PerformanceTemplateToIndicator> performanceTemplateToIndicators = performanceTemplateToIndicatorService.list(new LambdaQueryWrapperX<>(PerformanceTemplateToIndicator.class)
                .eq(PerformanceTemplateToIndicator::getTemplateId, templateId));
        List<String> ids = performanceTemplateToIndicators.stream().map(PerformanceTemplateToIndicator::getIndicatorId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            List<IndicatorLibrary> indicatorLibraries = indicatorLibraryService.list(new LambdaQueryWrapperX<>(IndicatorLibrary.class)
                    .eq(IndicatorLibrary::getStatus, IndicatorStatusEnum.ENABLE.getValue()));
            return indicatorLibraries;
        }
        List<IndicatorLibrary> indicatorLibraries = indicatorLibraryService.list(new LambdaQueryWrapperX<>(IndicatorLibrary.class)
                .notIn(IndicatorLibrary::getId, ids)
                .eq(IndicatorLibrary::getStatus, IndicatorStatusEnum.ENABLE.getValue()));
        return indicatorLibraries;
    }

    @Override
    public List<PerformanceTemplate> querylistByIds(List<String> typeIds) {
        //TODO 6/12 审查 吴锋   边界问题 为空怎么办
        LambdaQueryWrapperX<PerformanceTemplate> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(PerformanceTemplate.class);
        lambdaQueryWrapperX.in(PerformanceTemplate::getId, typeIds);
        return this.list(lambdaQueryWrapperX);
    }

    @Override
    public List<PerformanceTemplate> listAll(PerformanceTemplateDTO performanceTemplateDTO) {
            LambdaQueryWrapperX<PerformanceTemplate> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(PerformanceTemplate.class);
        return this.list(lambdaQueryWrapperX);
    }


    @Override
    public int getOrder() {
        return Integer.MAX_VALUE;
    }


}
