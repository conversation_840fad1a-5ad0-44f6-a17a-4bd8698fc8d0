import { openDrawer, openModal } from 'lyra-component-vue3';
import { h, ref, Ref } from 'vue';
import SelectCertificateModal from '/@/views/pms/certificateStandards/components/SelectCertificateModal.vue';

// 表格组件新增、编辑抽屉
export function openFormDrawer(component: any, record?: Record<string, any>, cb?: () => void): void {
  const drawerRef: Ref = ref();
  openDrawer({
    title: record?.id ? '编辑' : '新增',
    width: 1000,
    content() {
      return h(component, {
        ref: drawerRef,
        record,
      });
    },
    async onOk(): Promise<void> {
      await drawerRef.value.onSubmit();
      cb?.();
    },
  });
}

// 选择证书弹窗
export function openCertificateTableSelect(options: {
    selectedList?: any[],
    cb?: Function,
    certificateType?: string
}) {
  const drawerRef: Ref = ref();
  openModal({
    title: '选择证书',
    width: 1200,
    content() {
      return h(SelectCertificateModal, {
        ref: drawerRef,
        selectedList: options?.selectedList,
        certificateType: options?.certificateType || '',
      });
    },
    async onOk() {
      const data = await drawerRef.value.confirm();
      options?.cb?.(data);
    },
  });
}
