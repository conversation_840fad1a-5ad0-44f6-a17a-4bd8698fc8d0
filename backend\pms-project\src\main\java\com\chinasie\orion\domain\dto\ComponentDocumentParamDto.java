package com.chinasie.orion.domain.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/14/10:22
 * @description:
 */
@Data
public class ComponentDocumentParamDto implements Serializable {

    /**
     * classifyId : string
     * id : string
     * ids : ["string"]
     * majorId : string
     * paramSetId : string
     * productId : string
     * productModelId : string
     * projectId : string
     * queryCondition : [{"column":"string","dataType":"string","link":"string","subLink":"string","type":"string","value":{}}]
     * revId : string
     * searchText : string
     * secretLevel : string
     * status : 0
     * type : string
     */

    private String classifyId;
    private String id;
    private String majorId;
    private String paramSetId;
    private String productId;
    private String productModelId;
    private String projectId;
    private String revId;
    private String searchText;
    private String secretLevel;
    private int status;
    private String type;
    private List<String> ids;
    private List<QueryConditionBean> queryCondition;

    public String getClassifyId() {
        return classifyId;
    }

    public void setClassifyId(String classifyId) {
        this.classifyId = classifyId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMajorId() {
        return majorId;
    }

    public void setMajorId(String majorId) {
        this.majorId = majorId;
    }

    public String getParamSetId() {
        return paramSetId;
    }

    public void setParamSetId(String paramSetId) {
        this.paramSetId = paramSetId;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getProductModelId() {
        return productModelId;
    }

    public void setProductModelId(String productModelId) {
        this.productModelId = productModelId;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getRevId() {
        return revId;
    }

    public void setRevId(String revId) {
        this.revId = revId;
    }

    public String getSearchText() {
        return searchText;
    }

    public void setSearchText(String searchText) {
        this.searchText = searchText;
    }

    public String getSecretLevel() {
        return secretLevel;
    }

    public void setSecretLevel(String secretLevel) {
        this.secretLevel = secretLevel;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<String> getIds() {
        return ids;
    }

    public void setIds(List<String> ids) {
        this.ids = ids;
    }

    public List<QueryConditionBean> getQueryCondition() {
        return queryCondition;
    }

    public void setQueryCondition(List<QueryConditionBean> queryCondition) {
        this.queryCondition = queryCondition;
    }

    public static class QueryConditionBean {
        /**
         * column : string
         * dataType : string
         * link : string
         * subLink : string
         * type : string
         * value : {}
         */

        private String column;
        private String dataType;
        private String link;
        private String subLink;
        private String type;
        private ValueBean value;

        public String getColumn() {
            return column;
        }

        public void setColumn(String column) {
            this.column = column;
        }

        public String getDataType() {
            return dataType;
        }

        public void setDataType(String dataType) {
            this.dataType = dataType;
        }

        public String getLink() {
            return link;
        }

        public void setLink(String link) {
            this.link = link;
        }

        public String getSubLink() {
            return subLink;
        }

        public void setSubLink(String subLink) {
            this.subLink = subLink;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public ValueBean getValue() {
            return value;
        }

        public void setValue(ValueBean value) {
            this.value = value;
        }

        public static class ValueBean {
        }
    }
}
