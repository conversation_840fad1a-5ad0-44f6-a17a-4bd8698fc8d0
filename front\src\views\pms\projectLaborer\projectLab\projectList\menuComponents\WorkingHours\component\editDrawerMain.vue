<template>
  <div class="viewDrawerMain">
    <BasicForm
      ref="formRef"
      :model="projectInfo"
      @register="register"
    />
    <div style="height: 425px;overflow: hidden">
      <OrionTable
        v-if="showTable"
        ref="tableRef"
        :options="tableOptions"
        :dataSource="tableSource"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  computed, h, onMounted, ref,
} from 'vue';
import {
  BasicForm, BasicTabs, OrionTable, useForm,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
import Api from '/@/api';
import { InputNumber } from 'ant-design-vue';
// import deepClone = Cell.deepClone;
const props = defineProps<{
  record: any,
  id:string,
  projectInfo:any
}>();
const [register, { setFieldsValue, getFieldsValue }] = useForm({
  actionColOptions: {
    span: 24,
  },
  showActionButtonGroup: false,
  schemas: [
    {
      field: 'startDate',
      component: 'DatePicker',
      label: '开始时间:',
      colProps: {
        span: 12,
      },
      rules: [
        {
          required: true,
          trigger: 'change',
          type: 'string',
        },
      ],

      componentProps: ({ formModel }) => ({
        style: { width: '100%' },
        value: computed((date:Date) => date),
        onChange: (date:Date) => {
          formRef.value.setFieldsValue({
            startDate: date,
          });
          handleDispose();
        },
        disabledDate: (date:Date) => (
          dayjs(date).valueOf() > dayjs(props.projectInfo.projectEndTime).valueOf() || dayjs(date).valueOf() < dayjs(props.projectInfo.projectStartTime).valueOf()
        ),
      }),

    },
    {
      field: 'endDate',
      component: 'DatePicker',
      label: '结束时间:',
      colProps: {
        span: 12,
      },
      rules: [
        {
          required: true,
          trigger: 'change',
          type: 'string',
        },
      ],
      componentProps: ({ formModel }) => ({
        style: { width: '100%' },
        onChange: (date:Date) => {
          formRef.value.setFieldsValue({
            endDate: date,
          });
          handleDispose();
        },
        disabledDate: (date:Date) => (
          dayjs(date).valueOf() > dayjs(props.projectInfo.projectEndTime).valueOf() || dayjs(date).valueOf() < dayjs(props.projectInfo.projectStartTime).valueOf()
        ),
      }),
    },
  ],
});
const showTable = ref(false);
const tableRef = ref(null);
const formRef = ref(null);
const currentWorkContent = ref([]);
const tableSource = ref([]);
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: false,
  showToolButton: false,
  smallSearchField: false,
  showSmallSearch: false,
  pagination: false,
  showIndexColumn: false,
  columns: [],
});

const tabsIndex = ref(0);
onMounted(() => {
  getDetail(props.id);
});

function getDetail(id) {
  new Api('/pms/workHourEstimate').fetch('', id, 'GET').then((res) => {
    setFieldsValue({
      number: res?.number || '',
      startDate: res?.startDate || '',
      endDate: res?.endDate || '',
    });

    currentWorkContent.value = res.detailList;
    let workArr = [];
    for (let i in res.detailList) {
      tableOptions.value.columns.push({
        title: res.detailList[i].workMonth,
        customRender({
          record, index, column,
        }) {
          return h(InputNumber, {
            value: computed(() => record[res.detailList[i].workMonth]),
            onChange(e) {
              record[res.detailList[i].workMonth] = e;
            },
          });
        },
      });
      workArr.push ({
        [res.detailList[i].workMonth]: res.detailList[i].workHour,
      });
    }
    let mergedObj = workArr.reduce((result, currentObj) => {
      Object.assign(result, currentObj);
      return result;
    }, {});
    let resultArr = [mergedObj];
    showTable.value = true;
    tableSource.value = resultArr;
    setTimeout(() => {
      tableRef.value.setTableData(resultArr);
    }, 20);
  });
}

function handleDispose() {
  let startTime = formRef.value.getFieldsValue().startDate;
  let endTime = formRef.value.getFieldsValue().endDate;
  let diffMonthValue = getAllMonthsBetweenDates(startTime, endTime);

  diffMonthValue.forEach((item) => {
    let matchItem = currentWorkContent.value.find((arrItem) => arrItem.workMonth === item.workDate);
    if (matchItem) {
      item.workHour = matchItem.workHour;
    } else {
      item.workHour = 0;
    }
  });
  let workArr = [];
  let colunms_ = [];
  for (let i in diffMonthValue) {
    colunms_.push({
      title: diffMonthValue[i].workDate,
      dataIndex: diffMonthValue[i].workDate,
      customRender({
        record, index, column,
      }) {
        return h(InputNumber, {
          value: computed(() => record[column.dataIndex]),
          onChange(e) {
            record[column.dataIndex] = e;
          },
        });
      },
    });
    workArr.push ({
      [diffMonthValue[i].workDate]: diffMonthValue[i].workHour,
    });
  }
  tableRef.value.setColumns(colunms_);
}

function getAllMonthsBetweenDates(startDate, endDate) {
  let result = [];
  let currentDate = new Date(startDate);
  while (currentDate <= endDate) {
    let year = currentDate.getFullYear();
    let month = currentDate.getMonth() + 1;
    result.push({
      workDate: `${year}年${month.toString().padStart(2, '0')}月`,
      workHour: 0,
    });
    currentDate.setMonth(currentDate.getMonth() + 1);
  }
  return result;
}

defineExpose({
  getTableData: () => tableSource.value,
  getFormData: () => formRef.value,
});
</script>
<style lang="less">
.viewDrawerMain {
  height:100%;
}
</style>
