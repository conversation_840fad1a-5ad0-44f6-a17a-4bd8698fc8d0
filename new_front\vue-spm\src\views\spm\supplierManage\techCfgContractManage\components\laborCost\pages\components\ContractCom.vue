<script setup lang="ts">
import { BasicButton, BasicTableAction, openModal } from 'lyra-component-vue3';
import { STable } from '@surely-vue/table';
import {
  computed, h, inject, reactive, ref, Ref, unref,
} from 'vue';
import Api from '/@/api';
import { Modal } from 'ant-design-vue';
import SelectContractModal from './components/SelectContractModal.vue';

const detailsData: Record<string, any> = inject('detailsData', reactive({}));

const fetching: Ref<boolean> = ref(false);
const dataSource: Ref<any[]> = ref(detailsData.assessmentStandardVOS || []);

async function getData() {
  fetching.value = true;
  try {
    const result = await new Api('/spm/laborCostAcceptanceAssessmentStandard/byAcceptanceId').fetch({}, detailsData.id, 'GET');
    dataSource.value = result || [];
  } finally {
    fetching.value = false;
  }
}

const columns = [
  {
    title: '序号',
    dataIndex: 'no',
    width: 60,
    customRender({ index }) {
      return index + 1;
    },
  },
  {
    title: '考核类别',
    dataIndex: 'assessmentType',
  },
  {
    title: '考核内容',
    dataIndex: 'assessmentContent',
  },
  {
    title: '评分标准',
    dataIndex: 'standard',
  },
  {
    title: '评分',
    dataIndex: '',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 80,
    fixed: 'right',
    customRender({ record }) {
      return h(BasicTableAction, {
        record,
        actions: unref(actions),
      });
    },
  },
];

const actions = computed(() => [
  {
    text: '移除',
    modalTitle: '移除提示！',
    modelContent: '确认移除当前数据？',
    modal: (record) => removeApi([record.id]),
  },
]);

function removeApi(ids: string[]) {
  return new Promise((resolve, reject) => {
    new Api('/spm/laborCostAcceptanceAssessmentStandard/byAcceptanceId/remove').fetch(ids, detailsData.acceptanceId, 'DELETE').then(() => {
      getData();
      resolve('');
    }).catch((e) => {
      reject(e);
    });
  });
}

const selectedRowKeys: Ref<string[]> = ref([]);
const rowSelection = {
  onChange: (keys: string[]) => {
    selectedRowKeys.value = keys;
  },
};

const toolButtons = computed(() => [
  {
    title: '添加',
    operation: 'add',
    props: {
      icon: 'sie-icon-tianjiaxinzeng',
      type: 'primary',
    },
  },
  {
    title: '移除',
    operation: 'remove',
    props: {
      icon: 'sie-icon-shanchu',
      disabled: unref(selectedRowKeys).length === 0,
    },
  },
]);

function handleToolButton(operation: string) {
  switch (operation) {
    case 'add':
      const contentRef: Ref = ref();
      openModal({
        title: '选择合同考核条款',
        width: 1000,
        content() {
          return h(SelectContractModal, {
            ref: contentRef,
            contractNumber: detailsData.contractNo,
          });
        },
        onOk() {
          const ids = contentRef.value.getSelectedRowKeys();
          return new Promise((resolve, reject) => {
            new Api(`/spm/laborCostAcceptanceAssessmentStandard/byAcceptanceId/add/${detailsData.id}`).fetch(ids, '', 'POST').then(() => {
              getData();
              resolve();
            }).catch((e) => {
              reject(e);
            });
          });
        },
      });
      break;
    case 'remove':
      Modal.confirm({
        title: '移除提示！',
        content: '确认移除已选择的数据？',
        onOk: () => removeApi(selectedRowKeys.value),
      });
      break;
  }
}
</script>

<template>
  <div class="mb15">
    <BasicButton
      v-for="item in toolButtons"
      :key="item.operation"
      v-bind="item.props"
      @click="handleToolButton(item.operation)"
    >
      {{ item.title }}
    </BasicButton>
  </div>
  <STable
    rowKey="id"
    :dataSource="dataSource"
    :rowSelection="rowSelection"
    :columns="columns"
    :pagination="false"
    :loading="fetching"
  />
</template>

<style scoped lang="less">

</style>
