package com.chinasie.orion.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.domain.entity.PmsJobPostLibrary;
import com.chinasie.orion.domain.dto.PmsJobPostLibraryDTO;
import com.chinasie.orion.domain.vo.PmsJobPostLibraryVO;
import com.chinasie.orion.service.PmsJobPostLibraryService;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * PmsJobPostLibrary 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 10:19:25
 */
@RestController
@RequestMapping("/job-post-library")
@Api(tags = "作业岗位库")
public class  PmsJobPostLibraryController  {

    @Autowired
    private PmsJobPostLibraryService pmsJobPostLibraryService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看【作业岗位库】信息【{{#name}}】", type = "PersonTrainInfoRecord", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<PmsJobPostLibraryVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        PmsJobPostLibraryVO rsp = pmsJobPostLibraryService.detail(id,pageCode);
        LogRecordContext.putVariable("name", rsp.getName());
        return new ResponseDTO<>(rsp);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "通过岗位编号获取岗位信息")
    @RequestMapping(value = "/byNumber/{number}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看【作业岗位库】信息【{{#number}}】-【{{#name}}】", type = "PersonTrainInfoRecord", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<PmsJobPostLibraryVO> detailByNumber(@PathVariable(value = "number") String number) throws Exception {
        PmsJobPostLibraryVO rsp = pmsJobPostLibraryService.detailByNumber(number);
        LogRecordContext.putVariable("name", rsp.getName());
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param pmsJobPostLibraryDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【作业岗位库】数据【{{#pmsJobPostLibraryDTO.number}}】-【{{#pmsJobPostLibraryDTO.name}}】", type = "PmsJobPostLibrary", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody PmsJobPostLibraryDTO pmsJobPostLibraryDTO) throws Exception {
        String rsp =  pmsJobPostLibraryService.create(pmsJobPostLibraryDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param pmsJobPostLibraryDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【作业岗位库】数据【{{#pmsJobPostLibraryDTO.number}}】-【{{#pmsJobPostLibraryDTO.name}}】", type = "PmsJobPostLibrary", subType = "编辑", bizNo = "{{#pmsJobPostLibraryDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  PmsJobPostLibraryDTO pmsJobPostLibraryDTO) throws Exception {
        Boolean rsp = pmsJobPostLibraryService.edit(pmsJobPostLibraryDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【作业岗位库】数据", type = "PmsJobPostLibrary", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = pmsJobPostLibraryService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【作业岗位库】数据", type = "PmsJobPostLibrary", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = pmsJobPostLibraryService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【作业岗位库】数据", type = "PmsJobPostLibrary", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<PmsJobPostLibraryVO>> pages(@RequestBody Page<PmsJobPostLibraryDTO> pageRequest) throws Exception {
        Page<PmsJobPostLibraryVO> rsp =  pmsJobPostLibraryService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("作业岗位库导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载【作业岗位库】导入模板", type = "PmsJobPostLibrary", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        pmsJobPostLibraryService.downloadExcelTpl(response);
    }

    @ApiOperation("作业岗位库导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验【作业岗位库】导入", type = "PmsJobPostLibrary", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = pmsJobPostLibraryService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("作业岗位库导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认【作业岗位库】导入", type = "PmsJobPostLibrary", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  pmsJobPostLibraryService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消作业岗位库导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消【作业岗位库】导入", type = "PmsJobPostLibrary", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  pmsJobPostLibraryService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("作业岗位库导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出【作业岗位库】数据", type = "PmsJobPostLibrary", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        pmsJobPostLibraryService.exportByExcel(searchConditions, response);
    }

    /**
     * 分页
     *
     */
    @ApiOperation(value = "列表获取岗位")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【作业岗位库】列表数据", type = "PmsJobPostLibrary", subType = "列表查询", bizNo = "")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public ResponseDTO<List<PmsJobPostLibraryVO>> list(@RequestBody PmsJobPostLibraryDTO postLibraryDTO) throws Exception {
        List<PmsJobPostLibraryVO> rsp =  pmsJobPostLibraryService.listEntity( postLibraryDTO);
        return new ResponseDTO<>(rsp);
    }
}
