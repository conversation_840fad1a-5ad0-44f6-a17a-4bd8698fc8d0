<template>
  <Layout
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="baseTableOption"
      :isTable="isTable"
    >
      <template #toolbarLeft>
        <BasicTableAction
          :actions="actionsBtn"
          type="button"
        />

        <!--        <BasicButton-->
        <!--          v-if="isPower('PMS_CBZX_container_button_01',powerData)"-->
        <!--          type="primary"-->
        <!--          icon="add"-->
        <!--          @click="() => openEditDrawer(true,{ type: 'add'})"-->
        <!--        >-->
        <!--          新增成本-->
        <!--        </BasicButton>-->
      </template>
    </OrionTable>
    <!--编辑抽屉-->
    <CostCenterDrawer
      @updatePage="updatePage"
      @register="registerEditDrawer"
    />
  </layout>
</template>
<script setup lang="ts">
import {
  computed, h, inject, nextTick, ref, onMounted, getCurrentInstance,
} from 'vue';
import { useRouter } from 'vue-router';
import {
  BasicButton, BasicTableAction,
  BasicUpload,
  DataStatusTag, isPower, ITableActionItem, Layout, OrionTable, useDrawer, useProjectPower,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
import {
  Modal, message,
} from 'ant-design-vue';
import Api from '/@/api';
import { getCostCenterList } from '/@/views/pms/api';
import CostCenterDrawer from './components/costCenterDrawer.vue';
const [registerEditDrawer, { openDrawer: openEditDrawer }] = useDrawer();
const router = useRouter();
const isTable = ref(true);
const tableRef = ref(null);
// const powerData = inject('powerData');
const powerData = ref();
const columns = [
  {
    title: '成本中心编码',
    dataIndex: 'number',
  },
  {
    title: '成本中心名称',
    dataIndex: 'name',
  },
  {
    title: '状态',
    dataIndex: 'dataStatus',
    customRender: ({ record }) => h(DataStatusTag, { statusData: record.dataStatus }),
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    slots: { customRender: 'action' },
  },
];
const actions = [
  {
    text: '编辑',
    isShow: () => isPower('PMS_CBZX_container_button_02', powerData.value),
    onClick(record) {
      openEditDrawer(true, {
        type: 'edit',
        formData: record,
      });
    },
  },
  {
    text: '删除',
    isShow: () => isPower('PMS_CBZX_container_button_03', powerData.value),
    modal(record) {
      return new Api(`/pms/costCenter/${record.id}`).fetch('', '', 'DELETE').then(() => {
        message.success('删除成功');
        tableRef.value.reload();
      });
    },
  },
  {
    text: (record) => (record.status === 1 ? '禁用' : '启用'),
    isShow: (record) => computed(() => isPower(record.status === 1 ? 'PMS_CBZX_container_button_05' : 'PMS_CBZX_container_button_04', powerData.value)),
    onClick(record) {
      Modal.confirm({
        title: `${record.status === 1 ? '禁用' : '启用'}确认提示`,
        content: `请确认是否${record.status === 1 ? '禁用' : '启用'}该数据？`,
        onOk() {
          if (record.status === 1) {
            new Api(`/pms/costCenter/ban/${record.id}`).fetch('', '', 'PUT').then(() => {
              message.success('禁用成功');
              tableRef.value.reload();
            });
          } else {
            new Api(`/pms/costCenter/use/${record.id}`).fetch('', '', 'PUT').then(() => {
              message.success('启用成功');
              tableRef.value.reload();
            });
          }
        },
      });
    },
  },
];
const baseTableOption = {
  rowSelection: {},
  columns,
  api: (params) => getCostCenterList(params),
  showToolButton: false,
  isFilter2: true,
  filterConfigName: 'PMS_COSTMANAGE_PAGES_FEESETTINGS_COSTCENTER_INDEX',
  actions,
};
function updatePage() {
  nextTick();
  tableRef.value.reload();
}
onMounted(async () => {
  powerData.value = await getProjectPower() as any;
});

const actionsBtn:ITableActionItem[] = [
  {
    text: '新增成本',
    isShow: computed(() => isPower('PMS_CBZX_container_button_01', powerData.value)),
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
    onClick(record:any) {
      openEditDrawer(true, { type: 'add' });
    },
  },

];

// 获取权限
async function getProjectPower() {
  return new Promise((resolve, reject) => {
    useProjectPower(
      { pageCode: 'CostCenter' },
      (powerList) => {
        resolve(powerList || []);
      },
      getCurrentInstance(),
    );
  });
}
</script>
<style scoped lang="less">
.card-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 16px 20px;
}
</style>
