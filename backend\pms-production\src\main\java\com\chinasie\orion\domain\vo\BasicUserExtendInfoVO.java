package com.chinasie.orion.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;

import java.util.List;

/**
 * BasicUserExtendInfo VO对象
 *
 * <AUTHOR>
 * @since 2024-06-05 10:41:49
 */
@ApiModel(value = "BasicUserExtendInfoVO对象", description = "人员拓展信息")
@Data
public class BasicUserExtendInfoVO extends ObjectVO implements Serializable {

    /**
     * 流程名称
     */
    @ApiModelProperty(value = "流程名称")
    private String processName;


    /**
     * 发起人
     */
    @ApiModelProperty(value = "发起人")
    private String initiator;


    /**
     * 发起时间
     */
    @ApiModelProperty(value = "发起时间")
    private Date initiationTime;


    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private String priority;


    /**
     * 申请类型
     */
    @ApiModelProperty(value = "申请类型")
    private String applicationType;


    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
    private String applicant;


    /**
     * 是否有亲属在集团内
     */
    @ApiModelProperty(value = "是否有亲属在集团内")
    private String hasRelativeInGroup;


    /**
     * 亲属姓名
     */
    @ApiModelProperty(value = "亲属姓名")
    private String relativeName;


    /**
     * 亲属职务
     */
    @ApiModelProperty(value = "亲属职务")
    private String relativePosition;


    /**
     * 亲属公司
     */
    @ApiModelProperty(value = "亲属公司")
    private String relativeCompany;


    /**
     * 最高学历
     */
    @ApiModelProperty(value = "最高学历")
    private String highestEducation;


    /**
     * 所学专业
     */
    @ApiModelProperty(value = "所学专业")
    private String major;


    /**
     * 职称
     */
    @ApiModelProperty(value = "职称")
    private String title;


    /**
     * 专业技术证书
     */
    @ApiModelProperty(value = "专业技术证书")
    private String professionalTechnicalCertificate;


    /**
     * 是否需要工号
     */
    @ApiModelProperty(value = "是否需要工号")
    private String needsEmployeeNumber;


    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;


    /**
     * 所属供应商
     */
    @ApiModelProperty(value = "所属供应商")
    private String affiliatedSupplier;


    /**
     * 是否从事放射性工作
     */
    @ApiModelProperty(value = "是否从事放射性工作")
    private String worksWithRadioactiveMaterials;


    /**
     * 工作内容
     */
    @ApiModelProperty(value = "工作内容")
    private String jobContent;


    /**
     * 常驻服务地点
     */
    @ApiModelProperty(value = "常驻服务地点")
    private String permanentServiceLocation;


    /**
     * 所属合同名称
     */
    @ApiModelProperty(value = "所属合同名称")
    private String contractName;


    /**
     * 合同级别
     */
    @ApiModelProperty(value = "合同级别")
    private String contractLevel;


    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;


    /**
     * 是否项目制人员
     */
    @ApiModelProperty(value = "是否项目制人员")
    private String projectBasedStaff;


    /**
     * 入场时间
     */
    @ApiModelProperty(value = "入场时间")
    private Date entryTime;


    /**
     * 联系方式
     */
    @ApiModelProperty(value = "联系方式")
    private String contactInformation;


    /**
     * 是否已完成体检
     */
    @ApiModelProperty(value = "是否已完成体检")
    private String completedPhysicalExamination;


    /**
     * 分管项目经理
     */
    @ApiModelProperty(value = "分管项目经理")
    private String departmentHeadProjectManager;


    /**
     * 办卡或授权选择
     */
    @ApiModelProperty(value = "办卡或授权选择")
    private String cardOrAuthorizationChoice;


    /**
     * 项目接口人
     */
    @ApiModelProperty(value = "项目接口人")
    private String projectInterfacePerson;


    /**
     * 是否技术配置
     */
    @ApiModelProperty(value = "是否技术配置")
    private String technicalConfiguration;


    /**
     * 入场备注
     */
    @ApiModelProperty(value = "入场备注")
    private String entryRemarks;


    /**
     * 离场时间
     */
    @ApiModelProperty(value = "离场时间")
    private Date departureTime;


    /**
     * 预计离岗时间
     */
    @ApiModelProperty(value = "预计离岗时间")
    private Date expectedDepartureDate;


    /**
     * 是否已取消授权
     */
    @ApiModelProperty(value = "是否已取消授权")
    private String authorizationCancelled;


    /**
     * 是否违反相关安全规范
     */
    @ApiModelProperty(value = "是否违反相关安全规范")
    private String violatedSafetyRegulations;


    /**
     * 是否完成离职体检
     */
    @ApiModelProperty(value = "是否完成离职体检")
    private String completedDeparturePhysical;


    /**
     * 离职备注
     */
    @ApiModelProperty(value = "离职备注")
    private String departure;


    /**
     * 锁定状态
     */
    @ApiModelProperty(value = "锁定状态")
    private String lockedStatus;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    private String mainTableId;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    private Date operationTime;

    /**
     * 操作人id
     */
    @ApiModelProperty(value = "操作人id")
    private String operationId;

    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人姓名")
    private String operationName;
}
