<template>
  <Layout
    :options="{ body: { scroll: true } }"
    contentTitle="风险策划"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <ButtonList :actions="actions" />
      </template>
    </OrionTable>
  </Layout>
</template>

<script setup lang="ts">
import {
  h, reactive, ref, inject,
} from 'vue';
import {
  DataStatusTag, Layout, OrionTable, useITable, openSelectModal, BasicButton, isPower,
} from 'lyra-component-vue3';
import { openContentDrawer } from '/@/views/pms/utils/utils';
import Api from '/@/api';
import { message } from 'ant-design-vue';
import { ButtonList } from '/@/views/components/index';
import { useRoute } from 'vue-router';
import AddRow from '../components/AddRow.vue';

const route = useRoute();
const [tableRef, tableInfo]: any = useITable({});
const powerData: any = inject('powerData');
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  showSmallSearch: true,
  rowSelection: {},
  showTableSetting: true,
  showIndexColumn: true,
  api: (tableParams) => {
    let params = {
      ...tableParams,
    };
    params.query = {
      approveId: route.params.id,
    };
    return new Api('/pms/projectApprovalRiskPlan/page').fetch(params, '', 'POST');
  },
  columns: [
    {
      title: '编号',
      dataIndex: 'number',
    },
    {
      title: '名称',
      dataIndex: 'name',
    },
    {
      title: '状态',
      dataIndex: 'dataStatus',
      width: 100,
      customRender({ record }) {
        return h(DataStatusTag, {
          statusData: record.dataStatus,
        });
      },
    },
    {
      title: '风险类型',
      dataIndex: 'riskTypeName',
    },
    {
      title: '风险描述',
      dataIndex: 'remark',
    },
    {
      title: '发生概率',
      dataIndex: 'riskProbabilityName',
    },
    {
      title: '应对措施',
      dataIndex: 'solutions',
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '编辑',
      isShow: (record) => (!record.riskId) && isPower('PMS_FXCH_container_RiskPlaning_bianji', powerData),
      onClick(record) {
        openContentDrawer({
          title: '编辑预案',
          content: (h) => h(AddRow, {
            type: 'edit',
            detail: record,
          }),
          onOk(data) {
            let params = {
              ...data,
              id: record.id,
            };
            return new Api('/pms/projectApprovalRiskPlan/edit').fetch(params, '', 'PUT').then(() => {
              reload();
            });
          },
        });
      },
    },
    {
      text: '删除',
      isShow: () => isPower('PMS_FXCH_container_RiskPlaning_shanchu', powerData),
      modal: (record) => goDelete([record.id]),
    },
  ],
});
const actions = reactive([
  {
    text: '新增预案',
    icon: 'sie-icon-tianjiaxinzeng',
    type: 'primary',
    isShow: () => isPower('PMS_FXCH_container_RiskPlaning_xinzeng', powerData),
    openDrawer: {
      title: '新增预案',
      content: (h) => h(AddRow, { type: 'add' }),
      onOk(data) {
        delete data.number;
        let params = {
          ...data,
          approveId: route.params.id,
          projectId: route.query.projectId,
        };
        return new Api('/pms/projectApprovalRiskPlan/add').fetch(params, '', 'POST').then(() => {
          reload();
        });
      },
    },
  },
  {
    text: '引用创建',
    icon: 'sie-icon-jihuaxiafa',
    isShow: () => isPower('PMS_FXCH_container_RiskPlaning_yycj', powerData),
    onClick() {
      openSelectModal({
        onOk: (params) => {
          if (params.allSelectKeys?.length < 1) {
            message.info('请选择至少一条数据');
            return Promise.reject();
          }
          const riskPlanList = params.allSelect.map((item) => ({
            id: item.id,
            riskInfluence: item.riskInfluence,
            riskProbability: item.riskProbability,
            riskType: item.riskType,
            name: item.name,
            remark: item.remark,
            solutions: item.solutions,
          }));
          return new Api(`/pms/projectApprovalRiskPlan/save/batch/${route.params.id}/${route.query.projectId}`).fetch(riskPlanList, '', 'POST').then(() => {
            reload();
          });
        },
        tableConfig: {
          tableApi: (params) => {
            params.query = {
              status: 130,
            };
            return new Api('/pas').fetch(params, 'riskLibrary/page', 'POST');
          },
          tableOptions: {
            showSmallSearch: true,
            columns: [
              {
                title: '风险编号',
                dataIndex: 'number',
                width: 100,
              },
              {
                title: '风险名称',
                dataIndex: 'name',
                minWidth: 200,
              },
              {
                title: '风险类型',
                dataIndex: 'riskTypeName',
                width: 100,
                align: 'left',
              },
              {
                title: '发生概率',
                dataIndex: 'riskProbabilityName',
                width: 100,
                align: 'left',
              },
              {
                title: '影响程度',
                dataIndex: 'riskInfluenceName',
                width: 100,
                align: 'left',
              },
              {
                title: '应对措施',
                dataIndex: 'solutions',
                width: 100,
                align: 'left',
              },
            ],
          },
        },
        modalConfig: {
          title: '引入风险库风险',
        },
      });
    },
  },
  {
    text: '删除',
    icon: 'sie-icon-shanchu',
    isShow: () => isPower('PMS_FXCH_container_RiskPlaning_shanchu', powerData),
    disabled: () => tableInfo.disabled,
    modal: () => goDelete(tableInfo.keys),
  },
]);

function reload() {
  message.success('操作成功');
  tableRef.value.reload({ page: 1 });
}

async function goDelete(ids) {
  return await new Api('/pms/projectApprovalRiskPlan/remove').fetch(ids, '', 'DELETE').then(() => {
    reload();
  });
}
</script>
