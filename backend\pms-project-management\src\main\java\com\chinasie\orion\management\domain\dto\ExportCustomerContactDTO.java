package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * customerContact DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-30 16:25:24
 */
@ApiModel(value = "customerContactDTO对象", description = "客户管理详情")
@Data
@ExcelIgnoreUnannotated
public class ExportCustomerContactDTO implements Serializable {

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @ExcelProperty(value = "备注 ", index = 0)
    private String pRemark;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    @ExcelProperty(value = "邮箱 ", index = 1)
    private String mail;

    /**
     * 手机
     */
    @ApiModelProperty(value = "手机")
    @ExcelProperty(value = "手机 ", index = 2)
    private String phoneNumber;

    /**
     * 职位
     */
    @ApiModelProperty(value = "职位")
    @ExcelProperty(value = "职位 ", index = 3)
    private String jobName;

    /**
     * 主要联系人
     */
    @ApiModelProperty(value = "主要联系人")
    @ExcelProperty(value = "主要联系人 ", index = 4)
    private String isMain;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    @ExcelProperty(value = "性别 ", index = 5)
    private String sex;

    /**
     * 角色
     */
    @ApiModelProperty(value = "角色")
    @ExcelProperty(value = "角色 ", index = 6)
    private String roleName;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @ExcelProperty(value = "姓名 ", index = 7)
    private String name;

    /**
     * 部门
     */
    @ApiModelProperty(value = "部门")
    @ExcelProperty(value = "部门 ", index = 8)
    private String department;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    @ExcelProperty(value = "客户名称 ", index = 9)
    private String cusName;
}
