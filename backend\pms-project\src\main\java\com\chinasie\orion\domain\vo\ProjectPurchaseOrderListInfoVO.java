package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * ProjectPurchaseOrderInfo Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-06 08:42:57
 */
@ApiModel(value = "ProjectPurchaseOrderListInfoVO对象", description = "采购订单列表信息")
@Data
public class ProjectPurchaseOrderListInfoVO extends ProjectPurchaseOrderInfoVO implements Serializable{

    /**
     * 订单行数
     */
    @ApiModelProperty(value = "订单行数")
    private Integer lineCount;

    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商")
    private String supplierName;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private List<String> descriptionList;
}
