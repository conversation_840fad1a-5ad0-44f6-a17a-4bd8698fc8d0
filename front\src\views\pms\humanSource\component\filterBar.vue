<template>
  <BasicForm @register="registerForm" />
</template>

<script setup lang="ts">
import { reactive, defineExpose } from 'vue';
import { BasicForm, useForm } from 'lyra-component-vue3';
import Api from '/@/api';

const emits = defineEmits(['change']);
const props = defineProps({
  type: {
    type: String,
    default: '',
  },
});
const [registerForm, { validateFields, validate }] = useForm({
  // layout: 'vertical',
  baseColProps: {
    span: 24,
  },
  schemas: [
    {
      field: 'name',
      component: 'Input',
      label: '姓名',
      colProps: {
        span: 8,
      },
      componentProps: {
        onChange,
      },
    },

    // {
    //   field: 'orgId',
    //   component: 'ApiTreeSelect',
    //   label: '组织',
    //   colProps: {
    //     span: 8,
    //   },
    //   componentProps: {
    //     api: () => new Api('/pmi/organization/treeListPage').fetch({
    //       orders: [{ asc: false, column: '' }],
    //       pageNum: 0,
    //       pageSize: 0,
    //       query: { status: 1 },
    //     }, '', 'POST'),
    //     labelField: 'name',
    //     valueField: 'id',
    //     onChange,
    //   },
    // }, {
    //   field: 'roleId',
    //   component: 'ApiTreeSelect',
    //   label: '角色',
    //   colProps: {
    //     span: 8,
    //   },
    //   componentProps: {
    //     // api: () => new Api('/tom/tenant-admin-role/list').fetch({}, '', 'POST'),
    //     api: () => new Api('/pmi/organization/business/org/tree').fetch([], '', 'POST'),
    //     labelField: 'name',
    //     valueField: 'id',
    //     onChange,
    //   },
    // },
  ],
});

async function getData() {
  const res = await validateFields();
  return res;
}

async function onChange() {
  const data = await getData();
  emits('change', data);
}

defineExpose({
  getData,
});
</script>

<style scoped lang="less"></style>
