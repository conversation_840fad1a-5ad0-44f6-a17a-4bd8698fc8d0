<template>
  <BasicDrawer
    v-bind="$attrs"
    :title="typeName+'里程碑节点'"
    placement="right"
    :closable="true"
    :width="1000"
    @register="modalRegister"
  >
    <div class="drawar-content">
      <div class="require-title red-star">
        {{ drawerName }}名称：
      </div>
      <Input
        v-model:value="formData.nodeName"
        class="content-textarea"
        :maxlength="250"
      />
    </div>

    <div class="drawar-content">
      <div class="require-title red-star">
        {{ drawerName }}描述说明：
      </div>
      <Textarea
        v-model:value="formData.description"
        class="content-textarea"
        :autosize="{ minRows: 12, maxRows: 18 }"
        :maxlength="250"
      />
    </div>

    <div class="drawar-content">
      <div class="require-title">
        {{ drawerName }}排序：
      </div>
      <InputNumber
        v-model:value="formData.sort"
        class="content-textarea"
        :precision="0"
        :controls="false"
        :maxlength="10"
      />
    </div>

    <template #footer>
      <div class="flex-right">
        <BasicButton
          style="margin-right: 8px"
          @click="handleClose"
        >
          取消
        </BasicButton>
        <BasicButton
          type="primary"
          :loading="submitLoading"
          @click="handleSubmit"
        >
          提交
        </BasicButton>
      </div>
    </template>
  </BasicDrawer>
</template>
<script lang="ts" setup>
import { defineEmits, ref } from 'vue';
import { BasicButton, BasicDrawer, useDrawerInner } from 'lyra-component-vue3';
import {
  Input, message, Textarea, InputNumber,
} from 'ant-design-vue';

import { postProjectSchemeMilestoneNode, putProjectSchemeMilestoneNode } from '../api';

const submitLoading = ref(false);
const [modalRegister, { closeDrawer }] = useDrawerInner((drawerData) => {
  if (drawerData.type === 'add') {
    typeName.value = '添加';
    formData.value = drawerData;
    // templateId.value = drawerData.templateId;
  } else {
    typeName.value = '编辑';
    formData.value = drawerData;
  }
});
const emit = defineEmits(['editSuccess']);

const drawerName = ref<string>('');
const typeName = ref<string>('添加');
const formData = ref<any>({});

const handleClose = () => {
  closeDrawer();
};
async function handleSubmit() {
  if (formData.value.nodeName === '') {
    message.success('里程碑名称不能为空');
    return;
  }

  if (formData.value.description === '') {
    message.success('描述说明内容不能为空');
    return;
  }
  if (formData.value.type === 'add') {
    const result = await postProjectSchemeMilestoneNode({
      nodeName: formData.value.nodeName,
      description: formData.value.description,
      templateId: formData.value.templateId,
      sort: formData.value.sort,
    });
    if (result) {
      closeDrawer();
      formData.value.type = '';
      formData.value.nodeName = '';
      formData.value.description = '';
      formData.value.templateId = '';
      formData.value.sort = '';
      message.success('操作成功');
      emit('editSuccess', result);
    }
  } else {
    const result = await putProjectSchemeMilestoneNode(formData.value);
    if (result) {
      closeDrawer();
      formData.value.type = '';
      formData.value.nodeName = '';
      formData.value.description = '';
      formData.value.templateId = '';
      formData.value.sort = '';
      message.success('操作成功');
      emit('editSuccess', result);
    }
  }
}
</script>

<style lang="less" scoped>
.drawar-content {
  padding: ~`getPrefixVar('content-margin') `;
  .require-title {
    margin: 0 0 ~`getPrefixVar('button-margin') ` 0;
    font-weight: 700;
  }
  .red-star::before {
    display: inline-block;
    margin-right: 4px;
    color: #ff4d4f;
    font-size: 14px;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: '*';
  }
  .content-textarea {
    margin: 0 0 ~`getPrefixVar('button-margin') ` 0;
  }
  .link-area {
    width: 600px;
    margin: ~`getPrefixVar('button-margin') ` 0 0 0;
  }
}
.flex-content {
  .flex-box {
    display: flex;
    align-items: center;
    margin-top: 22px;
    > div {
      margin-right: 10px;
    }
    .number-box {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid rgba(217, 217, 217, 1);
      border-radius: 4px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.25);
    }
    .relation-box {
      box-sizing: border-box;
      width: 128px;
      height: 32px;
      border-width: 1px;
      border-style: solid;
      border-color: rgba(217, 217, 217, 1);
      border-radius: 4px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.25);
      display: flex;
      align-items: center;
      padding-left: 12px;
    }
    .select-box {
      width: 460px;
    }
  }
}
.flex-right {
  display: flex;
  justify-content: right;
}
</style>
