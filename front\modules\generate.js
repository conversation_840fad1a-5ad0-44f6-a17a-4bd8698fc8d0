const fs = require('fs');
const path = require('path');
// const chokidar = require('chokidar');

// 要扫描的目录路径
const directoryPath = path.join(__dirname, '../src/views');

// 输出的 TypeScript 文件路径
const outputPath = path.join(__dirname, 'prodModules.ts');

// 递归扫描目录中的所有 .vue 文件
function getVueFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);

  files.forEach((file) => {
    const filePath = path.join(dir, file);
    if (fs.statSync(filePath).isDirectory()) {
      getVueFiles(filePath, fileList);
    } else if (file.endsWith('.vue')) {
      fileList.push(filePath);
    }
  });

  return fileList;
}

// 函数：生成 TypeScript 文件内容
function generateTypeScriptFile() {
  const files = getVueFiles(directoryPath);

  const imports = files.map((file) => {
    const relativePath = `./${path.relative(__dirname, file).replace(/\\/g, '/')}`;
    return `'${relativePath}': () => import('${relativePath}')`;
  });

  const fileContent = `export const modules = {\n  ${imports.join(',\n  ')}\n};\n\nexport default modules;\n`;

  fs.writeFileSync(outputPath, fileContent);

  // eslint-disable-next-line no-console
  console.log('生成模块Map成功:', outputPath, imports);
}

// 立即生成一次 TypeScript 文件
generateTypeScriptFile();

// 监视目录及其子目录中的 .vue 文件变化
// chokidar.watch(directoryPath, { ignoreInitial: true }).on('all', (event, filePath) => {
//   if (filePath.endsWith('.vue')) {
//     console.log(`File ${event} detected: ${filePath}`);
//     generateTypeScriptFile();
//   }
// });
