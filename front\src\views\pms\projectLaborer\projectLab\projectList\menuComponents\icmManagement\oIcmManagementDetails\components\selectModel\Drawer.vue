<template>
  <BasicDrawer
    destroyOnClose
    showFooter
    :width="1000"
    :title="state.title"
    @register="modalRegister"
    @visible-change="visibleChange"
    @ok="confirm"
  >
    <ModalForm
      ref="formModalRef"
      :action="state.action"
    />
  </BasicDrawer>
</template>

<script setup lang="ts">
import {
  defineProps, reactive, defineExpose, defineEmits, ref, inject, watch, onMounted,
} from 'vue';
import { BasicDrawer, useDrawer } from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import ModalForm from './ModalForm.vue';
import Api from '/@/api';

const [modalRegister, modalMethods] = useDrawer();
const emit = defineEmits(['update']);
const detailsInfo: any = inject('detailsInfo', {});
const getDetailData: any = inject('getDetailData');
watch(() => detailsInfo.value, () => {
  state.details = detailsInfo.value;
});
onMounted(() => {
  state.details = detailsInfo.value;
});

function initData() {
  return {
    action: 'add',
    title: '',
    originData: {},
    details: {},
  };
}

const state = reactive(initData());
const formModalRef = ref(null);

function visibleChange(show) {
  if (show) {
    state.details = detailsInfo.value;
  }
  !show && Object.assign(state, initData());
}

interface openModalTypes {
    action: String;// add  edit 等
    info?: any
}

function openDrawer(data: openModalTypes) {
  modalMethods.openDrawer(true);
  data && usualHandle(data);
  if (data.action === 'add' && state.originData?.record?.modelId) {
    setTimeout(() => {
      formModalRef.value.setDefaultValue(state.originData?.record?.modelId);
    });
  }
  if (data.action === 'edit') {
    setTimeout(() => {
      formModalRef.value.FormMethods.setFieldsValue(state.originData?.record);
      formModalRef.value.getModelDetails(state.originData?.record?.insId);
    });
  }
}

function usualHandle(data) {
  data?.action && (state.action = data?.action);
  data?.action === 'add' && (state.title = '填值');
  data?.action === 'edit' && (state.title = '编辑');
  if (data?.info) {
    state.originData = JSON.parse(JSON.stringify(data.info));
  }
}

async function confirm() {
  await formModalRef.value && await formModalRef.value.FormMethods.validate();
  modalMethods.setDrawerProps({ confirmLoading: true });
  try {
    await goFetch().then(() => {
      message.success('操作成功');
      emit('update');
      modalMethods.openDrawer(false);
    });
  } catch (_) {
  } finally {
    modalMethods.setDrawerProps({ confirmLoading: false });
  }
}

async function goFetch() {
  const formData = formModalRef.value && formModalRef.value.FormMethods.getFieldsValue();
  const params = JSON.parse(JSON.stringify(formData));
  params.relationId = state.originData?.record?.id;
  params.projectId = state.details?.projectId;
  if (state.originData?.record?.modelId) {
    params.moduleId = state.originData?.record?.modelId;
  }
  params.dataName = state.details?.name;
  params.parameterId = state.originData?.record?.parameterId;
  if (params.triggerType.type === 3) {
    let tableFormat = formModalRef.value.getData();
    params.tableFormat = JSON.stringify(tableFormat);
  }
  let data = {
    ...params.triggerType,
    ...params,
  };
  if (state.originData?.record?.insId) {
    data.id = state.originData.record.insId;
  }
  // params.txtDesc = params.triggerType?.txtDesc;
  // params.type = params.triggerType?.type;
  delete params.triggerType;
  data.dataId = state.details?.id;
  return await new Api('/pms/ifToParameterToIns/add/value').fetch(data, '', 'PUT');
}

defineExpose({
  modalMethods,
  openDrawer,
});
</script>

<style scoped lang="less">

</style>
