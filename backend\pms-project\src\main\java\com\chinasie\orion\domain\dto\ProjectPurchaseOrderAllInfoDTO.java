package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import java.util.List;

/**
 * @author: yk
 * @date: 2023/11/6 18:08
 * @description: 项目采购总信息
 */
@ApiModel(value = "ProjectPurchaseOrderAllInfoDTO对象", description = "项目采购总信息")
@Data
public class ProjectPurchaseOrderAllInfoDTO {

    /**
     * 项目采购基本信息
     */
    @ApiModelProperty(value = "项目采购基本信息")
    @Valid
    private ProjectPurchaseOrderInfoDTO projectPurchaseOrderInfoDTO;

    /**
     * 项目采购收货方信息
     */
    @ApiModelProperty(value = "项目采购收货方信息")
    @Valid
    private ProjectPurchaseReceiveInfoDTO projectPurchaseReceiveInfoDTO;

    /**
     * 项目采购供应商信息
     */
    @ApiModelProperty(value = "项目采购供应商信息")
    @Valid
    private ProjectPurchaseSupplierInfoDTO projectPurchaseSupplierInfoDTO;

    /**
     * 项目采购订单明细信息
     */
    @ApiModelProperty(value = "项目采购订单明细信息")
    @Valid
    private List<ProjectPurchaseOrderDetailDTO> purchaseOrderDetailDTOList;

    /**
     * 订单附件
     */
    @ApiModelProperty(value = "订单附件")
    List<FileInfoDTO> fileInfoDTOList;
}
