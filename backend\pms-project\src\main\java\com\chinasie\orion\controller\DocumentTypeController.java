package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.DemandManagementQueryDTO;
import com.chinasie.orion.domain.dto.DocumentTypeDTO;
import com.chinasie.orion.domain.vo.DocumentTypeTreeVO;
import com.chinasie.orion.domain.vo.TreeSimpleVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.service.DocumentTypeService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/22/14:41
 * @description:
 */
@RestController
@RequestMapping("/document-type")
@Api(tags = "文档类型")
public class DocumentTypeController {

    @Resource
    private DocumentTypeService documentTypeService;

    @ApiOperation("新增文档类型")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "documentTypeDTO", dataType = "DocumentTypeDTO")
    })
    @PostMapping(value = "/save")
    @LogRecord(success = "【{USER{#logUserId}}】新增文档类型", type = "文档类型", subType = "新增文档类型", bizNo = "")
    public ResponseDTO<String> saveDocumentType(@RequestBody DocumentTypeDTO documentTypeDTO) throws Exception {
        return new ResponseDTO<>(documentTypeService.saveDocumentType(documentTypeDTO));
    }

    @ApiOperation("获取文档类型树")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "demandManagementQueryDTO", dataType = "DemandManagementQueryDTO")
    })
    @PostMapping(value = "/getTree")
    @LogRecord(success = "【{USER{#logUserId}}】获取文档类型树", type = "文档类型", subType = "获取文档类型树", bizNo = "")
    public ResponseDTO<List<DocumentTypeTreeVO>> getDocumentTypeTree(@RequestBody DemandManagementQueryDTO demandManagementQueryDTO) throws Exception {
        return new ResponseDTO<>(documentTypeService.getDocumentTypeTree(demandManagementQueryDTO));
    }

    @ApiOperation("获取文档类型树 简洁版")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "demandManagementQueryDTO", dataType = "DemandManagementQueryDTO")
    })
    @PostMapping(value = "/getSimpleTree")
    @LogRecord(success = "【{USER{#logUserId}}】获取文档类型树 简洁版", type = "文档类型", subType = "获取文档类型树 简洁版", bizNo = "")
    public ResponseDTO<List<TreeSimpleVO>> getDocumentTypeSimpleTree(@RequestBody DemandManagementQueryDTO demandManagementQueryDTO) throws Exception {
        return new ResponseDTO<>(documentTypeService.getDocumentTypeSimpleTree(demandManagementQueryDTO));
    }

    @ApiOperation("编辑文档类型")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "documentTypeDTO", dataType = "DocumentTypeDTO")
    })
    @PutMapping(value = "/edit")
    @LogRecord(success = "【{USER{#logUserId}}】编辑文档类型", type = "文档类型", subType = "编辑文档类型", bizNo = "")
    public ResponseDTO<Boolean> editRiskManagement(@RequestBody DocumentTypeDTO documentTypeDTO) throws Exception {
        return new ResponseDTO<>(documentTypeService.editDocumentType(documentTypeDTO));
    }

    @ApiOperation("删除文档类型")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", dataType = "List")
    })
    @DeleteMapping(value = "/remove/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】删除文档类型", type = "文档类型", subType = "删除文档类型", bizNo = "#{{id}}")
    public ResponseDTO<Boolean> removeDocumentType(@PathVariable("id") String id) throws Exception {
        return new ResponseDTO<>(documentTypeService.removeDocumentType(id));
    }
}
