package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 22 日
 **/
@ApiModel(value = "AmpereRingEventCheckDataInfoVo对象",description = "安质环检查时间数据信息对象")
@Data
public class AmpereRingEventCheckDataInfoVo extends ObjectVO implements Serializable {
    /**
     * 检查问题编号
     */
    @ApiModelProperty(value = "检查问题编号")
    private String checkNumber;

    /**
     * 检查主题
     */
    @ApiModelProperty(value = "检查主题")
    private String checkSubject;

    /**
     * 事件等级
     */
    @ApiModelProperty(value = "事件等级")
    private String eventLevel;

    /**
     * 事件类型
     */
    @ApiModelProperty(value = "事件类型")
    private String eventType;

    /**
     * 事件地点
     */
    @ApiModelProperty(value = "事件地点")
    private String eventAddress;

    /**
     * 事件地点code
     */
    @ApiModelProperty(value = "事件地点code")
    private String eventAddressCode;

    /**
     * 事件发生时间
     */
    @ApiModelProperty(value = "事件发生的时间")
    private Date eventDate;

    /**
     * 监察人
     */
    @ApiModelProperty(value = "监察人")
    private String checkPerson;

    /**
     * 监察人所在部门
     */
    @ApiModelProperty(value = "监察人所在部门")
    private String checkPersonDept;

    /**
     * 是否监督发现
     */
    @ApiModelProperty(value = "是否监督发现")
    private Boolean isFind;

    /**
     * 时间描述
     */
    @ApiModelProperty(value = "时间描述")
    private String eventDesc;

    /**
     * 直接负责人
     */
    @TableField(value = "person_in_charge")
    private String personInCharge;

    /**
     * 直接部门
     */
    @ApiModelProperty(value = "直接部门")
    private String zrDeptCode;

    /**
     * 直接归口部门
     */
    @ApiModelProperty(value = "直接归口部门")
    private String gkDeptCode;

    /**
     * 直接责任部门
     */
    @ApiModelProperty(value = "直接责任部门")
    private String zrdept;

    /**
     * 直接归口部门
     */
    @ApiModelProperty(value = "直接归口部门")
    private String gkdept;

    /**
     * 流程状态
     */
    @ApiModelProperty(value = "流程状态")
    private String currentProcess;
}
