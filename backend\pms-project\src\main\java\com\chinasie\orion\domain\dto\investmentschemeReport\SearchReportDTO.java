package com.chinasie.orion.domain.dto.investmentschemeReport;

import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
public class SearchReportDTO extends ObjectDTO implements Serializable {
    private String name;

    private List<String> ids;

    private List<List<SearchCondition>> searchConditions;

    private String companyName;

    private String rspDeptId;

    private Integer projectStatusCode;

    private String month;

    private String projectNumber;

    private String projectStatusName;

    private String projectName;

    private String rspDeptName;

    private String yearName;

    /**
     * 如果该值为true表示前端没有查询数据
     */
    private Boolean isEmpty = true;


    /**
     * 月份
     */
    private boolean pointProject;
}
