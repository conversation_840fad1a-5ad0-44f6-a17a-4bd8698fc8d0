package com.chinasie.orion.controller;


import com.chinasie.orion.domain.dto.InvestmentSchemeDTO;
import com.chinasie.orion.domain.vo.InvestmentSchemeVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.service.InvestmentSchemeService;
import com.chinasie.orion.util.CollectionUtils;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * InvestmentScheme 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16 14:04:50
 */
@RestController
@RequestMapping("/investmentScheme")
@Api(tags = "投资计划")
public class InvestmentSchemeController {

    @Autowired
    private InvestmentSchemeService investmentSchemeService;


    /**
     * 自动创建投资计划
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "自动创建投资计划")
    @RequestMapping(value = "/create/{projectId}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】自动创建投资计划", type = "投资计划", subType = "自动创建投资计划", bizNo = "{{#projectId}}")
    public ResponseDTO<InvestmentSchemeVO> createByProjectId(@PathVariable("projectId") String projectId, @RequestParam(required = false) String pageCode) throws Exception {
        InvestmentSchemeVO rsp = investmentSchemeService.createByProjectId(projectId,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 自动获取的值
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "自动获取的值")
    @RequestMapping(value = "/init/{projectId}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】自动获取的值", type = "投资计划", subType = "自动获取的值", bizNo = "{{#projectId}}")
    public ResponseDTO<InvestmentSchemeVO> initValue(@PathVariable("projectId") String projectId) throws Exception {
        InvestmentSchemeVO rsp = investmentSchemeService.initValue(projectId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查询【InvestmentScheme详情】", subType = "详情", type = "投资计划", bizNo = "{{#id}}")
    public ResponseDTO<InvestmentSchemeVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        InvestmentSchemeVO rsp = investmentSchemeService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param investmentSchemeDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【InvestmentScheme】", type = "投资计划",subType = "新增",bizNo = "{{#investmentSchemeDTO.name}}")
    public ResponseDTO<String> create(@RequestBody InvestmentSchemeDTO investmentSchemeDTO) throws Exception {
        String rsp = investmentSchemeService.create(investmentSchemeDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除", type = "投资计划", subType = "删除批量", bizNo = "")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        if(CollectionUtils.isBlank(ids)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "请先选择投资计划!");
        }
        Boolean rsp = investmentSchemeService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 列表
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "列表")
    @RequestMapping(value = "/list/{projectId}", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【InvestmentScheme列表】", subType = "列表", type = "投资计划", bizNo = "{{#projectId}}")
    public ResponseDTO<List<InvestmentSchemeVO>> list(@PathVariable("projectId") String projectId) throws Exception {
        List<InvestmentSchemeVO> rsp = investmentSchemeService.list(projectId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 1月1号0点变更为已关闭
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "1月1号0点变更为已关闭")
    @RequestMapping(value = "/close", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】1月1号0点变更为已关闭", type = "投资计划", subType = "1月1号0点变更为已关闭", bizNo = "")
    public ResponseDTO<Boolean> close() throws Exception {
        Boolean rsp = investmentSchemeService.close();
        return new ResponseDTO<>(rsp);
    }
}
