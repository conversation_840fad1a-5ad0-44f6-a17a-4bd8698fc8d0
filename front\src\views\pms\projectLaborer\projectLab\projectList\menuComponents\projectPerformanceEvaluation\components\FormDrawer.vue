<script setup lang="ts">
import {
  BasicButton, BasicForm, FormSchema, getDict, OrionTable, useForm, openModal,
} from 'lyra-component-vue3';
import {
  FormItemRest, Input, InputNumber, message, Select as ASelect,

} from 'ant-design-vue';
import {
  computed,
  h, onMounted, Ref, ref,
} from 'vue';

import PerformanceModal from './performanceModal/index.vue';
import Api from '/@/api';

const props = defineProps<{
  id: string,
  projectId:string,
  record:any,
  isView: boolean
}>();
function openPerformanceModal(tableIndexList, cb?: () => void, isView: boolean = false) {
  const formRef: Ref = ref();
  openModal({
    title: (tableIndexList && tableIndexList.length > 0) ? '编辑绩效' : '新建绩效',
    width: 800,
    content() {
      return h(PerformanceModal, {
        ref: formRef,
        tableIndexList,
      });
    },
    footer: {
      isOk: !isView,
    },
    async onOk(): Promise<void> {
      tableRefDataSource.value = mergeLists(tableRefDataSource.value, formRef.value.getSelectData());
    },
  });
}
function mergeLists(list1, list2) {
  const resultMap = new Map();

  // 首先将 list1 中的项添加到 resultMap 中，以 ID 为键
  list1.forEach((item) => resultMap.set(item.id, item));

  // 然后遍历 list2，如果 ID 不在 resultMap 中，则添加到 resultMap 中
  list2.forEach((item) => {
    if (!resultMap.has(item.id)) {
      resultMap.set(item.id, item);
    }
  });

  // 最后将 resultMap 中的值转换为数组并返回
  return Array.from(resultMap.values());
}

const tableRefDataSource = ref([]);
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const tableOptions = {
  resizeHeightOffset: 1000,
  showToolButton: false,
  showSmallSearch: false,
  dataSource: computed(() => tableRefDataSource.value),
  columns: [
    {
      title: '绩效指标名称',
      dataIndex: 'name',
    },
    {
      title: '权重',
      dataIndex: 'weight',
      className: 'class-weight',
      customRender({ text, record }) {
        return h(FormItemRest, [
          h(InputNumber, {
            value: text,
            onChange: (e) => {
              record.weight = e;
            },
            min: 0,
            max: 100,
            addonAfter: '%',
            placeholder: '请输入',
          }),
        ]);
      },
    },
    {
      title: '评分说明',
      dataIndex: 'scoreStandard',
    },
    {
      title: '评分',
      dataIndex: 'score',
      className: 'class-score',
      customRender({ text, record }) {
        return h(FormItemRest, [
          h(InputNumber, {
            value: text,
            min: 0,
            max: 100,
            onChange: (e) => {
              record.score = e;
            },
            placeholder: '请输入',
          }),
        ]);
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 120,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '移除',
      onClick(record: Record<string, any>) {
        tableRefDataSource.value = tableRefDataSource.value.filter((item) => item.id !== record.id);
      },

      // modal: (record: Record<string, any>) => deleteApi([record?.id]),
    },
  ],
};
const tableRef: Ref = ref();

const dataSource: Ref<Record<string, any>[]> = ref([]);
function sumNumbersInList(list, propertyName) {
  return list.reduce((total, item) => total + Number(item[propertyName]), 0);
}

function sumScoreInList(list, propertyName) {
  return list.reduce((total, item) => {
    const percentage = Number(item[propertyName]) / 100 || 0; // 将百分比转换为小数，如果为空则为 0
    const score = Number(item.score) || 0; // 如果 item.score 为空，则将其转换为 0
    return total + (percentage * score);
  }, 0);
}
onMounted(() => {
  props.id && iniForm();
});

const loading: Ref<boolean> = ref(false);

async function iniForm() {
  loading.value = true;
  try {
    // setFieldsValue(props.record);
    await getEditData();
  } finally {
    loading.value = false;
  }
}

async function getEditData() {
  const res = await new Api(`/pms/projectPerformance/${props.id}`).fetch('', '', 'GET');
  tableRefDataSource.value = res.indicatorDTOList.map((item) => ({
    ...item,
    id: item.indicatorId,
    name: item.indicatorName,
  }));
  evaluateType.value = res.typeId;
  evaluateName.value = res.name;
}
defineExpose({
  async onSubmit() {
    if (weightTotal.value !== 100) {
      message.warn('评价指标权重合计只能100%');
      return Promise.reject();
    }
    if (!evaluateType.value) {
      message.warn('评价类型必选');
      return Promise.reject();
    }

    if (!evaluateName.value) {
      message.warn('评价名称必填');
      return Promise.reject();
    }
    if (tableRef.value.getDataSource().some((item) => item.score === undefined || item.score === null || item.score === '')) {
      message.warn('评分必填');
      return Promise.reject();
    }

    const params = {
      indicatorDTOList: [],
      typeId: evaluateType.value,
      name: evaluateName.value,
      projectId: props.projectId,
    };
    if (props.id) {
      params.id = props.id;
    }
    params.indicatorDTOList = tableRef.value.getDataSource().map((item) => ({
      indicatorId: item.id,
      scoreStandard: item.scoreStandard,
      indicatorName: item.name,
      weight: item.weight,
      score: item.score,
    }));
    await new Api('/pms/projectPerformance').fetch(params, '', props.id ? 'PUT' : 'POST');
  },
});
const toolButtons = computed(() => [
  {
    event: 'add',
    text: '添加指标',
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
  },
]);
function handleToolButton(operation: string) {
  switch (operation) {
    case 'add':
      openPerformanceModal(tableRef.value.getDataSource());
      break;
  }
}

const cellLeft = ref();
const left: Ref<number> = ref();
const weightTotal = computed(() => sumNumbersInList(tableRefDataSource.value, 'weight'));

const scoreCellLeft = ref();
const scoreLeft: Ref<number> = ref();
const scoreTotal = computed(() => sumScoreInList(tableRefDataSource.value, 'weight'));
const totalRef: Ref = ref();
const scoreRef: Ref = ref();
const evaluateOptions = ref([]);
const evaluateType = ref('');
const evaluateName = ref('');

async function getTemplateList() {
  const res = await new Api('/pms/performanceTemplate/list').fetch('', '', 'POST');
  evaluateOptions.value = res;
}
onMounted(() => {
  getTemplateList();

  setTimeout(() => {
    // 假设您有一个元素的引用，例如通过 document.getElementById() 或者其他方法获取到的
    const classWeightEl = document.getElementsByClassName('class-weight')[0];

    // 获取计算后的样式对象
    const classWeightElStyles = window.getComputedStyle(classWeightEl);

    // 提取 left 属性的值
    cellLeft.value = classWeightElStyles.getPropertyValue('left');

    const classScoreEl = document.getElementsByClassName('class-score')[0];

    // 获取计算后的样式对象
    const classScoreElStyles = window.getComputedStyle(classScoreEl);

    // 提取 left 属性的值
    scoreCellLeft.value = classScoreElStyles.getPropertyValue('left');
  });
});
</script>

<template>
  <div>
    <OrionTable
      ref="tableRef"
      class="table-performance"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <div class="m-b-r">
          <span>评价类型：</span>
          <ASelect
            ref="select"
            v-model:value="evaluateType"
            :field-names="{ label: 'name', value: 'id' }"
            allowClear
            style="width: 180px"
            class="table-input"
            placeholder="请选择模板名称"
            :options="evaluateOptions"
          />
        </div>
        <div class="m-b-r">
          <span>评价名称：</span>
          <Input
            v-model:value="evaluateName"
            allowClear
            style="width: 180px"
            class="table-input"
          />
        </div>
      </template>
      <template #toolbarRight>
        <BasicButton
          v-for="button in toolButtons"
          v-bind="button"
          :key="button.event"
          @click="handleToolButton(button.event)"
        >
          {{ button.text }}
        </BasicButton>
      </template>
      <template #footer>
        <div class="footer-content">
          <span>汇总</span>

          <span
            class="position-ab"
            :class="[{'color-red': weightTotal > 100}, {'color-green': weightTotal === 100}]"
            :style="{left: cellLeft}"
          >{{ weightTotal }}%</span>

          <span
            class="position-ab"

            :style="{left:scoreCellLeft}"
          >{{ scoreTotal }}</span>
        </div>
      </template>
    </OrionTable>
  </div>
</template>

<style scoped lang="less">
:deep(.ant-basic-table-wrap.default-spacing) {
  padding: 0;
}
.test-management ::v-deep .default-spacing {
  padding-left: 0;
  padding-right: 0;
}
.footer-content {
  position: relative;
  display: flex;
  align-items: center;

  .position-ab{
    position: absolute;
  }
  span {
    font-weight: bold;
  }
  .color-red{
    color: red;
  }
  .color-green{
     color: #52c41a;
   }

}
</style>
