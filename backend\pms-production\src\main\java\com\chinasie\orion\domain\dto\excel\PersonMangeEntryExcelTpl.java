package com.chinasie.orion.domain.dto.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.chinasie.orion.domain.vo.excel.ExcelYesOrNoConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 大修管理 - 人员入场导入 - excel模板
 * </p>
 *
 * <AUTHOR>
 * @since 2024/9/27 下午2:44
 */
@ApiModel(value = "PersonMangeEntryExcelTpl", description = "大修管理 - 人员入场导入 - excel模板")
@Data
@ExcelIgnoreUnannotated
public class PersonMangeEntryExcelTpl {

    private String id;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "人员ID： 人员管理ID")
    private String personId;

    @ApiModelProperty(value = "有无项目:默认有")
    private Boolean isHaveProject;

    @ExcelProperty("*员工号")
    @ApiModelProperty(value = "用户工号")
    private String userCode;

    @ExcelProperty("*姓名")
    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ColumnWidth(35)
    @ExcelProperty("*实际入场日期")
    @ApiModelProperty(value = "实际入场日期")
    private Date actInDate;

    @ExcelProperty("*身高/厘米")
    @ApiModelProperty(value = "身高/厘米")
    private String heightStr;

    @ExcelProperty("*体重/千克")
    @ApiModelProperty(value = "体重/千克")
    private String weightStr;

    @ExcelProperty(value = "*化学品/毒物使用或接触作业", converter = ExcelYesOrNoConverter.class)
    @ApiModelProperty(value = "化学品/毒物使用或接触作业：是/否 true/false")
    @ColumnWidth(50)
    private Boolean chemicalToxinUseJob;

    @ExcelProperty(value = "*一年内参与过集团内大修", converter = ExcelYesOrNoConverter.class)
    @ApiModelProperty(value = "一年内参与过集团内大修，码值：是、否")
    @ColumnWidth(50)
    private Boolean isJoinYearMajorRepair;

    @ExcelProperty(value = "*高剂量人员", converter = ExcelYesOrNoConverter.class)
    @ApiModelProperty(value = "高剂量人员（年个人剂量>8mSv为高剂量人员），码值：是、否")
    @ColumnWidth(80)
    private Boolean isHeightMeasurePerson;

    @ExcelProperty(value = "*是否常驻", converter = ExcelYesOrNoConverter.class)
    @ApiModelProperty(value = "是否基地常驻")
    private Boolean isBasePermanent;

    @ApiModelProperty(value = "计划入场日期")
    @ColumnWidth(25)
    private Date inDate;

    @ApiModelProperty(value = "计划离场日期")
    @ColumnWidth(25)
    private Date outDate;

}
