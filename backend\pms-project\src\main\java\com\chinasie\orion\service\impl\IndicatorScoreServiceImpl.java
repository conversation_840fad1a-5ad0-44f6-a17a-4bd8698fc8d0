package com.chinasie.orion.service.impl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.domain.dto.IndicatorScoreDTO;
import com.chinasie.orion.domain.entity.IndicatorScore;
import com.chinasie.orion.repository.IndicatorScoreMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.service.IndicatorScoreService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.String;
import java.util.List;

/**
 * <p>
 * IndicatorScore 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-25 16:41:31
 */
@Service
public class IndicatorScoreServiceImpl extends OrionBaseServiceImpl<IndicatorScoreMapper, IndicatorScore> implements IndicatorScoreService {


}
