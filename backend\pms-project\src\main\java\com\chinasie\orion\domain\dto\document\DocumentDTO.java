package com.chinasie.orion.domain.dto.document;

import com.chinasie.orion.domain.dto.file.RevisionClassDTO;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * DocumentDTO 文档DTO对象
 *
 * <AUTHOR> sie
 * @since 2021-04-22
 */
@Data
@ApiModel(value = "documentDTO", description = "文档")
public class DocumentDTO extends RevisionClassDTO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private String id;

    /**
     * 资源ID
     */
    @ApiModelProperty(value = "资源ID")
    private String resourceId;

    /**
     * 文档模板
     */
    @ApiModelProperty(value = "文档模板")
    private String docTemplate;

    /**
     * 数据类型ID
     */
    @ApiModelProperty(value = "数据类型ID")
    private String dataType;

    /**
     * 来源
     */
    @ApiModelProperty(value = "来源")
    private Integer source;

    /**
     * 链接
     */
    @ApiModelProperty(value = "链接")
    private String fileUrl;

    /**
     * Third
     */
    @ApiModelProperty(value = "Third 版本")
    private String ThirdRedId;

    /**
     *  文档ID——document 壳
     */
    @ApiModelProperty(value = "文档ID")
    private String documentId;
}
