<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <artifactId>assertj-parent-pom</artifactId>
    <groupId>org.assertj</groupId>
    <version>2.2.14</version>
    <relativePath>pom.xml</relativePath>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>assertj-core</artifactId>
  <name>AssertJ fluent assertions</name>
  <version>3.22.0</version>
  <description>Rich and fluent assertions for testing for Java</description>
  <issueManagement>
    <system>github</system>
    <url>https://github.com/assertj/assertj-core/issues</url>
  </issueManagement>
  <scm>
    <connection>scm:git:https://github.com/assertj/assertj-core.git</connection>
    <developerConnection>scm:git:https://github.com/assertj/assertj-core.git</developerConnection>
    <tag>assertj-core-3.22.0</tag>
    <url>https://github.com/assertj/assertj-core</url>
  </scm>
  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>net.alchim31.maven</groupId>
          <artifactId>yuicompressor-maven-plugin</artifactId>
          <version>1.5.1</version>
        </plugin>
        <plugin>
          <groupId>org.eclipse.m2e</groupId>
          <artifactId>lifecycle-mapping</artifactId>
          <version>1.0.0</version>
          <configuration>
            <lifecycleMappingMetadata>
              <pluginExecutions>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>com.mycila</groupId>
                    <artifactId>license-maven-plugin</artifactId>
                    <versionRange>[2.6,)</versionRange>
                    <goals>
                      <goal>format</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore />
                  </action>
                </pluginExecution>
              </pluginExecutions>
            </lifecycleMappingMetadata>
          </configuration>
        </plugin>
        <plugin>
          <groupId>com.github.siom79.japicmp</groupId>
          <artifactId>japicmp-maven-plugin</artifactId>
          <version>0.15.4</version>
          <configuration>
            <dependencies>
              <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
              </dependency>
              <dependency>
                <groupId>org.hamcrest</groupId>
                <artifactId>hamcrest</artifactId>
                <version>${hamcrest.version}</version>
              </dependency>
              <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-api</artifactId>
                <version>${junit-jupiter.version}</version>
              </dependency>
              <dependency>
                <groupId>org.opentest4j</groupId>
                <artifactId>opentest4j</artifactId>
                <version>${opentest4j.version}</version>
              </dependency>
            </dependencies>
            <parameter>
              <excludes>
                <exclude>org.assertj.core.internal</exclude>
              </excludes>
              <onlyBinaryIncompatible>true</onlyBinaryIncompatible>
              <onlyModified>true</onlyModified>
              <overrideCompatibilityChangeParameters>
                <overrideCompatibilityChangeParameter>
                  <compatibilityChange>METHOD_NEW_DEFAULT</compatibilityChange>
                  <binaryCompatible>true</binaryCompatible>
                  <sourceCompatible>true</sourceCompatible>
                </overrideCompatibilityChangeParameter>
              </overrideCompatibilityChangeParameters>
              <reportOnlyFilename>true</reportOnlyFilename>
            </parameter>
            <skipXmlReport>true</skipXmlReport>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <id>enforce-java-and-dependencies</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <bannedDependencies>
                  <includes>
                    <include>net.bytebuddy:byte-buddy</include>
                  </includes>
                  <excludes>
                    <exclude>org.assertj:assertj-core</exclude>
                    <exclude>org.hamcrest:hamcrest-core</exclude>
                    <exclude>*:*:*:jar:compile</exclude>
                  </excludes>
                </bannedDependencies>
                <dependencyConvergence />
                <requireJavaVersion>
                  <version>[11,)</version>
                </requireJavaVersion>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <executions>
          <execution>
            <id>jdk9</id>
            <goals>
              <goal>compile</goal>
            </goals>
            <configuration>
              <release>9</release>
              <compileSourceRoots>
                <compileSourceRoot>${project.basedir}/src/main/java9</compileSourceRoot>
              </compileSourceRoots>
              <multiReleaseOutput>true</multiReleaseOutput>
            </configuration>
          </execution>
        </executions>
        <configuration>
          <release>8</release>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <argLine>@{argLine}</argLine>
          <trimStackTrace>false</trimStackTrace>
          <excludes>
            <exclude>org/assertj/core/osgi/**</exclude>
            <exclude>org/assertj/core/internal/objects/Objects_assertHasOnlyFields_Test*</exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.2.4</version>
        <executions>
          <execution>
            <id>bytebuddy</id>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <createDependencyReducedPom>true</createDependencyReducedPom>
              <relocations>
                <relocation>
                  <pattern>net.bytebuddy</pattern>
                  <shadedPattern>org.assertj.core.internal.bytebuddy</shadedPattern>
                  <excludes>
                    <exclude>net/bytebuddy/experimental</exclude>
                  </excludes>
                </relocation>
              </relocations>
              <filters>
                <filter>
                  <artifact>net.bytebuddy:*</artifact>
                  <excludes>
                    <exclude>META-INF/**</exclude>
                  </excludes>
                </filter>
              </filters>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-dependency-plugin</artifactId>
        <executions>
          <execution>
            <id>unpack-shade</id>
            <phase>package</phase>
            <goals>
              <goal>unpack</goal>
            </goals>
            <configuration>
              <artifactItems>
                <artifactItem>
                  <groupId>${project.groupId}</groupId>
                  <artifactId>${project.artifactId}</artifactId>
                  <version>${project.version}</version>
                </artifactItem>
              </artifactItems>
              <outputDirectory>${project.build.directory}/classes</outputDirectory>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>biz.aQute.bnd</groupId>
        <artifactId>bnd-maven-plugin</artifactId>
        <version>${bnd.version}</version>
        <executions>
          <execution>
            <id>bundle-after-unpack-shaded</id>
            <phase>package</phase>
            <goals>
              <goal>bnd-process</goal>
            </goals>
            <configuration>
              <bnd>Import-Package:\
                  !org.assertj.core.*,\
                  *;resolution:="optional"
                Multi-Release: true
                -exportcontents: \
                  !org.assertj.core.internal.*,\
                  org.assertj.core.*
                -noclassforname: true
                -noextraheaders: true
                -snapshot: SNAPSHOT
                -removeheaders: Private-Package
                -fixupmessages: \
                  "Classes found in the wrong directory...";is:=ignore</bnd>
            </configuration>
          </execution>
          <execution>
            <id>bnd-process-tests</id>
            <phase>process-test-classes</phase>
            <goals>
              <goal>bnd-process-tests</goal>
            </goals>
            <configuration>
              <includeClassesDir>false</includeClassesDir>
              <bnd>-includepackage: org.assertj.core.osgi.*
                -noextraheaders: true
                -snapshot: SNAPSHOT
                -removeheaders: Private-Package</bnd>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-antrun-plugin</artifactId>
        <version>3.0.0</version>
        <executions>
          <execution>
            <id>remove-primary-artifact</id>
            <phase>package</phase>
            <goals>
              <goal>run</goal>
            </goals>
            <configuration>
              <target>
                <script>project.getReference("maven.project").getArtifact().setFile(null);</script>
              </target>
            </configuration>
          </execution>
        </executions>
        <dependencies>
          <dependency>
            <groupId>org.beanshell</groupId>
            <artifactId>bsh</artifactId>
            <version>2.0b5</version>
          </dependency>
        </dependencies>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <execution>
            <id>default-jar</id>
            <phase>prepare-package</phase>
            <goals>
              <goal>jar</goal>
            </goals>
            <configuration>
              <archive>
                <manifestEntries>
                  <Multi-Release>true</Multi-Release>
                </manifestEntries>
              </archive>
            </configuration>
          </execution>
          <execution>
            <id>final-jar</id>
            <phase>package</phase>
            <goals>
              <goal>jar</goal>
            </goals>
            <configuration>
              <archive>
                <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
              </archive>
            </configuration>
          </execution>
          <execution>
            <id>test-jar</id>
            <phase>package</phase>
            <goals>
              <goal>test-jar</goal>
            </goals>
            <configuration>
              <archive>
                <manifestFile>${project.build.testOutputDirectory}/META-INF/MANIFEST.MF</manifestFile>
              </archive>
              <includes>
                <include>org/assertj/core/osgi/**</include>
              </includes>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>net.alchim31.maven</groupId>
        <artifactId>yuicompressor-maven-plugin</artifactId>
        <executions>
          <execution>
            <phase>generate-sources</phase>
            <goals>
              <goal>compress</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <sourceDirectory>src/main/javadoc</sourceDirectory>
          <outputDirectory>${project.build.directory}/javadoc-stylesheet</outputDirectory>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <configuration>
          <locale>en_US</locale>
          <source>8</source>
          <stylesheetfile>${project.build.directory}/javadoc-stylesheet/assertj-javadoc-min.css</stylesheetfile>
          <additionalOptions>${javadocAdditionalOptions}</additionalOptions>
          <nohelp>true</nohelp>
          <show>private</show>
          <footer>&lt;link rel="stylesheet" href="./assertj-javadoc.css""&gt;
            &lt;script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.4.0/highlight.min.js"&gt;&lt;/script&gt;
            &lt;script type="text/javascript"&gt;hljs.initHighlightingOnLoad();&lt;/script&gt;</footer>
        </configuration>
      </plugin>
      <plugin>
        <groupId>biz.aQute.bnd</groupId>
        <artifactId>bnd-resolver-maven-plugin</artifactId>
        <version>${bnd.version}</version>
        <executions>
          <execution>
            <id>osgi-integration-resolving</id>
            <phase>pre-integration-test</phase>
            <goals>
              <goal>resolve</goal>
            </goals>
            <configuration>
              <bndruns>
                <bndrun>verify.bndrun</bndrun>
              </bndruns>
              <bundles>
                <bundle>target/${project.build.finalName}-tests.jar</bundle>
              </bundles>
              <failOnChanges>false</failOnChanges>
              <reportOptional>false</reportOptional>
              <includeDependencyManagement>true</includeDependencyManagement>
              <scopes>
                <scope>provided</scope>
                <scope>compile</scope>
                <scope>runtime</scope>
                <scope>test</scope>
              </scopes>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>biz.aQute.bnd</groupId>
        <artifactId>bnd-testing-maven-plugin</artifactId>
        <version>${bnd.version}</version>
        <executions>
          <execution>
            <id>osgi-integration-testing</id>
            <goals>
              <goal>testing</goal>
            </goals>
            <configuration>
              <bndruns>
                <bndrun>verify.bndrun</bndrun>
              </bndruns>
              <bundles>
                <bundle>target/${project.build.finalName}-tests.jar</bundle>
              </bundles>
              <failOnChanges>false</failOnChanges>
              <includeDependencyManagement>true</includeDependencyManagement>
              <resolve>false</resolve>
              <scopes>
                <scope>provided</scope>
                <scope>compile</scope>
                <scope>runtime</scope>
                <scope>test</scope>
              </scopes>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-invoker-plugin</artifactId>
        <version>3.2.2</version>
        <executions>
          <execution>
            <id>integration-test</id>
            <goals>
              <goal>install</goal>
              <goal>run</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <cloneProjectsTo>${project.build.directory}/it</cloneProjectsTo>
          <settingsFile>src/it/settings.xml</settingsFile>
          <localRepositoryPath>${project.build.directory}/local-repo</localRepositoryPath>
          <goals>
            <goal>clean</goal>
            <goal>test</goal>
          </goals>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.pitest</groupId>
        <artifactId>pitest-maven</artifactId>
        <version>1.7.3</version>
        <dependencies>
          <dependency>
            <groupId>org.pitest</groupId>
            <artifactId>pitest-junit5-plugin</artifactId>
            <version>0.15</version>
          </dependency>
          <dependency>
            <groupId>com.groupcdg</groupId>
            <artifactId>pitest-git-plugin</artifactId>
            <version>${cdg.pitest.version}</version>
          </dependency>
        </dependencies>
        <configuration>
          <threads>3</threads>
          <timestampedReports>false</timestampedReports>
          <failWhenNoMutations>false</failWhenNoMutations>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>java13+</id>
      <properties>
        <javadocAdditionalOptions>-html5 --allow-script-in-comments</javadocAdditionalOptions>
      </properties>
    </profile>
    <profile>
      <id>java16+</id>
      <properties>
        <argLine>-Dfile.encoding=${project.build.sourceEncoding}
          -Dnet.bytebuddy.experimental=true
          --add-opens=java.base/java.lang=ALL-UNNAMED
          --add-opens=java.base/java.math=ALL-UNNAMED
          --add-opens=java.base/java.util=ALL-UNNAMED
          --add-opens=java.base/sun.nio.fs=ALL-UNNAMED</argLine>
      </properties>
    </profile>
    <profile>
      <id>pitest</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.pitest</groupId>
            <artifactId>pitest-maven</artifactId>
            <executions>
              <execution>
                <id>pitest</id>
                <phase>test-compile</phase>
                <goals>
                  <goal>mutationCoverage</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>com.groupcdg</groupId>
            <artifactId>pitest-git-maven-plugin</artifactId>
            <version>${cdg.pitest.version}</version>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>japicmp-branch</id>
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <groupId>com.github.siom79.japicmp</groupId>
              <artifactId>japicmp-maven-plugin</artifactId>
              <configuration>
                <oldVersion>
                  <file>
                    <path>${japicmp.oldVersion.basedir}/target/${project.build.finalName}.${project.packaging}</path>
                  </file>
                </oldVersion>
              </configuration>
            </plugin>
          </plugins>
        </pluginManagement>
      </build>
    </profile>
    <profile>
      <id>coverage</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.jacoco</groupId>
            <artifactId>jacoco-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>jacoco-report</id>
                <phase>prepare-package</phase>
                <goals>
                  <goal>report</goal>
                </goals>
              </execution>
              <execution>
                <id>default-check</id>
                <phase>prepare-package</phase>
                <goals>
                  <goal>check</goal>
                </goals>
                <configuration>
                  <rules>
                    <rule>
                      <element>BUNDLE</element>
                      <limits>
                        <limit>
                          <counter>CLASS</counter>
                          <value>COVEREDRATIO</value>
                          <minimum>0.98</minimum>
                        </limit>
                      </limits>
                    </rule>
                  </rules>
                </configuration>
              </execution>
            </executions>
            <configuration>
              <excludes>
                <exclude>**/*hamcrest*/**</exclude>
              </excludes>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.13.2</version>
      <scope>provided</scope>
      <exclusions>
        <exclusion>
          <artifactId>hamcrest-core</artifactId>
          <groupId>org.hamcrest</groupId>
        </exclusion>
      </exclusions>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.hamcrest</groupId>
      <artifactId>hamcrest</artifactId>
      <version>2.2</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <version>5.8.2</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.opentest4j</groupId>
      <artifactId>opentest4j</artifactId>
      <version>1.2.0</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.11.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <version>2.13.1</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>jackson-annotations</artifactId>
          <groupId>com.fasterxml.jackson.core</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jackson-core</artifactId>
          <groupId>com.fasterxml.jackson.core</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>31.0.1-jre</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>failureaccess</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
        <exclusion>
          <artifactId>listenablefuture</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jsr305</artifactId>
          <groupId>com.google.code.findbugs</groupId>
        </exclusion>
        <exclusion>
          <artifactId>checker-qual</artifactId>
          <groupId>org.checkerframework</groupId>
        </exclusion>
        <exclusion>
          <artifactId>error_prone_annotations</artifactId>
          <groupId>com.google.errorprone</groupId>
        </exclusion>
        <exclusion>
          <artifactId>j2objc-annotations</artifactId>
          <groupId>com.google.j2objc</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>nl.jqno.equalsverifier</groupId>
      <artifactId>equalsverifier</artifactId>
      <version>3.8.1</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-collections4</artifactId>
      <version>4.4</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.12.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.eclipse.platform</groupId>
      <artifactId>org.eclipse.osgi</artifactId>
      <version>3.17.100</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.hibernate.orm</groupId>
      <artifactId>hibernate-core</artifactId>
      <version>6.0.0.Beta3</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>jakarta.inject-api</artifactId>
          <groupId>jakarta.inject</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jakarta.persistence-api</artifactId>
          <groupId>jakarta.persistence</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jakarta.transaction-api</artifactId>
          <groupId>jakarta.transaction</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jandex</artifactId>
          <groupId>org.jboss</groupId>
        </exclusion>
        <exclusion>
          <artifactId>classmate</artifactId>
          <groupId>com.fasterxml</groupId>
        </exclusion>
        <exclusion>
          <artifactId>hibernate-commons-annotations</artifactId>
          <groupId>org.hibernate.common</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jboss-logging</artifactId>
          <groupId>org.jboss.logging</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jakarta.activation-api</artifactId>
          <groupId>jakarta.activation</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jakarta.xml.bind-api</artifactId>
          <groupId>jakarta.xml.bind</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jaxb-runtime</artifactId>
          <groupId>org.glassfish.jaxb</groupId>
        </exclusion>
        <exclusion>
          <artifactId>antlr4-runtime</artifactId>
          <groupId>org.antlr</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.junit.platform</groupId>
      <artifactId>junit-platform-testkit</artifactId>
      <version>1.8.2</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>assertj-core</artifactId>
          <groupId>org.assertj</groupId>
        </exclusion>
        <exclusion>
          <artifactId>junit-platform-launcher</artifactId>
          <groupId>org.junit.platform</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <version>5.8.2</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>junit-jupiter-params</artifactId>
          <groupId>org.junit.jupiter</groupId>
        </exclusion>
        <exclusion>
          <artifactId>junit-jupiter-engine</artifactId>
          <groupId>org.junit.jupiter</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.junit.vintage</groupId>
      <artifactId>junit-vintage-engine</artifactId>
      <version>5.8.2</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>junit-platform-engine</artifactId>
          <groupId>org.junit.platform</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>4.2.0</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>byte-buddy-agent</artifactId>
          <groupId>net.bytebuddy</groupId>
        </exclusion>
        <exclusion>
          <artifactId>objenesis</artifactId>
          <groupId>org.objenesis</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-junit-jupiter</artifactId>
      <version>4.2.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-core</artifactId>
      <version>5.3.14</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>spring-jcl</artifactId>
          <groupId>org.springframework</groupId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>net.bytebuddy</groupId>
        <artifactId>byte-buddy</artifactId>
        <version>${byte-buddy.version}</version>
      </dependency>
      <dependency>
        <groupId>net.bytebuddy</groupId>
        <artifactId>byte-buddy-agent</artifactId>
        <version>${byte-buddy.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest</artifactId>
        <version>${hamcrest.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.logging</groupId>
        <artifactId>jboss-logging</artifactId>
        <version>3.4.2.Final</version>
      </dependency>
      <dependency>
        <groupId>org.opentest4j</groupId>
        <artifactId>opentest4j</artifactId>
        <version>${opentest4j.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <properties>
    <byte-buddy.version>1.12.6</byte-buddy.version>
    <cdg.pitest.version>0.1.0</cdg.pitest.version>
    <bnd.version>6.1.0</bnd.version>
    <junit-jupiter.version>5.8.2</junit-jupiter.version>
    <javadocAdditionalOptions>-html5 --allow-script-in-comments --no-module-directories</javadocAdditionalOptions>
    <junit.version>4.13.2</junit.version>
    <mockito.version>4.2.0</mockito.version>
    <hamcrest.version>2.2</hamcrest.version>
    <opentest4j.version>1.2.0</opentest4j.version>
    <jacoco-maven-plugin.version>0.8.7</jacoco-maven-plugin.version>
    <argLine>-Dfile.encoding=${project.build.sourceEncoding}</argLine>
  </properties>
</project>
