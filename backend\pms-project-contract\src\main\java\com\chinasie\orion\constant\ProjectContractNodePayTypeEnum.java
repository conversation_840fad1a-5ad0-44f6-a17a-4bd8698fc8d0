package com.chinasie.orion.constant;

/**
 * @author: yk
 * @date: 2023/10/18 19:12
 * @description: 合同类别
 */
public enum ProjectContractNodePayTypeEnum {
    FIRST("firstPayment", "首付款"),
    SCHEDULE("schedulePayment", "进度款"),
    SETTLEMENT("settlementPayment", "结算款"),
    GUARANTEE("guaranteePayment", "质保款"),
    ;

    private String code;

    private String description;

    ProjectContractNodePayTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
