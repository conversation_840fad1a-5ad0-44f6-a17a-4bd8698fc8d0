import { h } from 'vue';
import { DataStatusTag, isPower } from 'lyra-component-vue3';
import dayjs from 'dayjs';

// 操作栏
export function getActionsList({ router, state }) {
  return [
    {
      text: '查看',
      isShow: (record: Record<string, any>) => isPower('PMS_XMXQ_container_21_04_02_button_01', record?.rdAuthList),
      onClick: (record) => {
        router.push({
          name: 'WeekReportDetails',
          query: {
            id: record?.projectId,
            curId: record?.id,
          },
        });
      },
    },
    {
      text: '审核',
      isShow: (record: Record<string, any>) => isPower('PMS_XMXQ_container_21_04_02_button_02', record?.rdAuthList),
      onClick: (record) => {
        // console.log('state', record);
        state.addOrEditRef.openModal({
          action: 'add',
          info: {
            record,
            type: '1',
          },
        });
      },
    },
  ];
}

// 列数据
export function getColumns({ router }) {
  return [
    {
      title: '汇报日期',
      dataIndex: 'daily',
      width: 250,
      customRender: ({ record }) => {
        let begin = dayjs(record.weekBegin).format('YYYY-MM-DD');
        let end = dayjs(record.weekEnd).format('YYYY-MM-DD');
        let time = ` 第${record.week}周（${begin}~${end}）`;
        return time;
      },
    },
    {
      title: '工作内容',
      dataIndex: 'content',
      customRender: ({ record }) => {
        let name = '';
        if (record && record?.contentVOList?.length) {
          name = record?.contentVOList.map((item) => item.content).join(', ');
        }
        return h('span', {
          title: name,
          class: 'action-btn',
          onClick: () => {
            router.push({
              name: 'WeekReportDetails',
              query: {
                id: record?.projectId,
                curId: record?.id,
              },
            });
          },
        }, name);
      },
      // slots: { customRender: 'name' },
    },
    {
      title: '关联对象',
      dataIndex: 'relationshipName',
      // slots: { customRender: 'name' },
      customRender: ({ record }) => {
        if (record && record?.contentVOList?.length) {
          return record?.contentVOList.map((item) => item.relationshipName).join(', ');
        }
      },
    },

    {
      title: '责任人',
      dataIndex: 'respName',
      width: 120,
    },
    {
      title: '整体进度',
      dataIndex: 'overallProgressName',
      width: 120,
      customRender: ({ record }) => h(DataStatusTag, { statusData: record.overallProgressName }),
    },
    {
      title: '状态',
      dataIndex: 'statusName',
      width: 120,
      customRender: ({ record }) => h(DataStatusTag, { statusData: record.dataStatus }),
    },
    {
      title: '评分',
      dataIndex: 'score',
      width: 120,
    },
    {
      title: '评价',
      dataIndex: 'evaluate',
    },
    {
      title: '操作',
      dataIndex: 'actions',
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
  ];
}
