package com.chinasie.orion.management.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * SupplierInfo VO对象
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@ApiModel(value = "SupplierInfoVO对象", description = "供应商管理")
@Data
public class SupplierInfoVO extends ObjectVO implements Serializable {

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    private String supplierNumber;


    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String name;


    /**
     * 供应商账号
     */
    @ApiModelProperty(value = "供应商账号")
    private String account;


    /**
     * 找回密码邮箱
     */
    @ApiModelProperty(value = "找回密码邮箱")
    private String findPassEamil;


    /**
     * 供应商简称
     */
    @ApiModelProperty(value = "供应商简称")
    private String simName;


    /**
     * 供应商英文名称
     */
    @ApiModelProperty(value = "供应商英文名称")
    private String eName;


    /**
     * 注册国家和地区
     */
    @ApiModelProperty(value = "注册国家和地区")
    private String regCountryRegion;


    /**
     * 省
     */
    @ApiModelProperty(value = "省")
    private String province;


    /**
     * 城市
     */
    @ApiModelProperty(value = "城市")
    private String city;


    /**
     * 区/县
     */
    @ApiModelProperty(value = "区/县")
    private String county;


    /**
     * 注册地址
     */
    @ApiModelProperty(value = "注册地址")
    private String regAddress;


    /**
     * 邮政编码
     */
    @ApiModelProperty(value = "邮政编码")
    private String emsNumber;


    /**
     * 网址
     */
    @ApiModelProperty(value = "网址")
    private String url;


    /**
     * 固定电话
     */
    @ApiModelProperty(value = "固定电话")
    private String landlinePhone;


    /**
     * 分机
     */
    @ApiModelProperty(value = "分机")
    private String extension;


    /**
     * 传真
     */
    @ApiModelProperty(value = "传真")
    private String fax;


    /**
     * 组织类型
     */
    @ApiModelProperty(value = "组织类型")
    private String organizationType;


    /**
     * 法人代表
     */
    @ApiModelProperty(value = "法人代表")
    private String legalrep;


    /**
     * 企业性质
     */
    @ApiModelProperty(value = "企业性质")
    private String companyNature;


    /**
     * 中广核集团参股或控股公司
     */
    @ApiModelProperty(value = "中广核集团参股或控股公司")
    private String zghChild;


    /**
     * 注册资金币种
     */
    @ApiModelProperty(value = "注册资金币种")
    private String capitalCurrency;


    /**
     * 注册资本（万）
     */
    @ApiModelProperty(value = "注册资本（万）")
    private BigDecimal registeredCapital;


    /**
     * 上级主管单位
     */
    @ApiModelProperty(value = "上级主管单位")
    private String parentOrg;


    /**
     * 主要控股公司
     */
    @ApiModelProperty(value = "主要控股公司")
    private String majorShareholder;

    /**
     * 可提供产品/服务
     */
    @ApiModelProperty(value = "可提供产品/服务")
    private String productsServices;

    /**
     * 可提供产品/服务文字描述
     */
    @ApiModelProperty(value = "可提供产品/服务文字描述")
    private String productsServicesDesc;



    /**
     * 公司简介
     */
    @ApiModelProperty(value = "公司简介")
    private String companyOverview;


    /**
     * 营业执照注册号/统一社会信用代码
     */
    @ApiModelProperty(value = "营业执照注册号/统一社会信用代码")
    private String businessLicenseNum;


    /**
     * 营业执照有效期起
     */
    @ApiModelProperty(value = "营业执照有效期起")
    private Date businessLicenseStart;


    /**
     * 营业执照有效期至
     */
    @ApiModelProperty(value = "营业执照有效期至")
    private Date businessLicenseEnd;


    /**
     * 经营范围
     */
    @ApiModelProperty(value = "经营范围")
    private String operationScope;


    /**
     * 是否推荐供应商
     */
    @ApiModelProperty(value = "是否推荐供应商")
    private String recommendSupplier;


    /**
     * 板块名称
     */
    @ApiModelProperty(value = "板块名称")
    private String sectorName;


    /**
     * 供应商级别
     */
    @ApiModelProperty(value = "供应商级别")
    private String supplierLevel;


    /**
     * 资审有效期
     */
    @ApiModelProperty(value = "资审有效期")
    private Date qualValidity;


    /**
     * 采购品类
     */
    @ApiModelProperty(value = "采购品类")
    private String procurementCat;


    /**
     * 采购品类编码
     */
    @ApiModelProperty(value = "采购品类编码")
    private String procCatCode;


    /**
     * 供货范围文本描述
     */
    @ApiModelProperty(value = "供货范围文本描述")
    private String deliveryScopeDesc;


    /**
     * 供应商分类
     */
    @ApiModelProperty(value = "供应商分类")
    private String supplierType;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    /**
     * 供应商类别
     */
    @ApiModelProperty(value = "供应商类别")
    private String supplierCategory;

    /**
     * 供应商缴费有效截止日期
     */
    @ApiModelProperty(value = "供应商缴费有效截止日期")
    private Date paymentEffectiveDeadline;

    /**
     * 联系人姓名
     */
    @ApiModelProperty(value = "联系人姓名")
    private String contractName;

    /**
     * 联系人手机
     */
    @ApiModelProperty(value = "联系人手机")
    private String contractTel;

    /**
     * 联系人邮箱
     */
    @ApiModelProperty(value = "联系人邮箱")
    private String contractEmail;

    /**
     * 二级公司编码
     */
    @ApiModelProperty(value = "二级公司编码")
    private String secondaryCompanyCode;

    /**
     * 供应商类别
     */
    @ApiModelProperty(value = "供应商类别")
    private String secondaryCompanyName;

    /**
     * 营业执照注册日期
     */
    @ApiModelProperty(value = "营业执照注册日期")
    private Date licenseRegDate;

    /**
     * 营业执照有效日期
     */
    @ApiModelProperty(value = "营业执照有效日期")
    private Date licenseValidDate;
}
