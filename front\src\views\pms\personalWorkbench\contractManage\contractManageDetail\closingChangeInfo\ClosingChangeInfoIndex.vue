<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
  >
    <template #toolbarLeft />>
  </OrionTable>
</template>
<script setup lang="ts">

import {
  BasicTableAction,
  DataStatusTag,
  OrionTable,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
import { h } from 'vue';
import Template from '/@/views/pms/projectLaborer/knowledgeEditData/Template.vue';
import { Button, message } from 'ant-design-vue';
import { SyncOutlined } from '@ant-design/icons-vue';
import Api from '/@/api';

const tableOptions = {
  showTableSetting: false,
  showToolButton: false,
  showSmallSearch: false,
  rowSelection: null,
  api(params) {
    return new Api('/pas/projectContractChangeApply').getPage(params);
  },
  columns: [
    {
      title: '变更申请编号',
      dataIndex: 'contractNumber',
    },
    {
      title: '合同名称',
      dataIndex: 'contractName',
    },
    {
      title: '数据状态',
      dataIndex: 'dataStatus',
      customRender({ record }) {
        return h(DataStatusTag, {
          statusData: record.dataStatus,
        });
      },
    },

    {
      title: '申请人',
      dataIndex: 'applyUserName',

    },

    {
      title: '申请时间',
      dataIndex: 'applyDate',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '';
      },
    },

    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 220,
      fixed: 'right',
    },
  ],

  actions: [
    {
      text: '查看',
      onClick(record) {
        // 查看相关的操作
      },
    },
    {
      text: '编辑',
      onClick(record) {
        //   编辑相关的操作
      },
    },

    {
      text: '删除',
      modal(record) {
        return new Api(`/pms/costCenter/${record.id}`).fetch('', '', 'DELETE').then(() => {
          message.success('删除成功');
          tableRef.value.reload();
        });
      },
    },
    {
      text: '启动流程',
      onClick(record) {
        //   启动流程的操作
      },
    },
  ],

};

const uploadFile = () => {

};
const deleteMultiple = () => {

};

</script>

<style lang="sass" scoped></style>
