package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * QuestionLibrary DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-17 13:36:10
 */
@ApiModel(value = "QuestionLibraryPushDTO对象", description = "问题推送")
@Data
@ExcelIgnoreUnannotated
public class QuestionLibraryPushDTO extends ObjectDTO implements Serializable{

    /**
     * 角色ids
     */
    @ApiModelProperty(value = "角色ids")
    private List<String> roleIds;

    /**
     * 问题ids
     */
    @ApiModelProperty(value = "问题ids")
    private List<String> questionLibraryIds;

}
