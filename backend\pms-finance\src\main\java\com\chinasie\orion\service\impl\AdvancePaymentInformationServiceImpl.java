package com.chinasie.orion.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.domain.dto.AdvancePaymentInformationDTO;
import com.chinasie.orion.domain.entity.AdvancePaymentInformation;
import com.chinasie.orion.domain.vo.AdvancePaymentInformationVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.AdvancePaymentInformationMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.AdvancePaymentInformationService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <p>
 * AdvancePaymentInformation 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29 17:25:21
 */
@Service
@Slf4j
public class AdvancePaymentInformationServiceImpl extends  OrionBaseServiceImpl<AdvancePaymentInformationMapper, AdvancePaymentInformation>   implements AdvancePaymentInformationService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public AdvancePaymentInformationVO detail(String id, String pageCode) throws Exception {
        AdvancePaymentInformation advancePaymentInformation =this.getById(id);
        AdvancePaymentInformationVO result = BeanCopyUtils.convertTo(advancePaymentInformation,AdvancePaymentInformationVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param advancePaymentInformationDTO
     */
    @Override
    public  String create(AdvancePaymentInformationDTO advancePaymentInformationDTO) throws Exception {
        AdvancePaymentInformation advancePaymentInformation =BeanCopyUtils.convertTo(advancePaymentInformationDTO,AdvancePaymentInformation::new);
        this.save(advancePaymentInformation);

        String rsp=advancePaymentInformation.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param advancePaymentInformationDTO
     */
    @Override
    public Boolean edit(AdvancePaymentInformationDTO advancePaymentInformationDTO) throws Exception {
        AdvancePaymentInformation advancePaymentInformation =BeanCopyUtils.convertTo(advancePaymentInformationDTO,AdvancePaymentInformation::new);

        this.updateById(advancePaymentInformation);

        String rsp=advancePaymentInformation.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<AdvancePaymentInformationVO> pages( Page<AdvancePaymentInformationDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<AdvancePaymentInformation> condition = new LambdaQueryWrapperX<>( AdvancePaymentInformation. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(AdvancePaymentInformation::getCreateTime);


        Page<AdvancePaymentInformation> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), AdvancePaymentInformation::new));

        PageResult<AdvancePaymentInformation> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<AdvancePaymentInformationVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<AdvancePaymentInformationVO> vos = BeanCopyUtils.convertListTo(page.getContent(), AdvancePaymentInformationVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "预收款信息导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", AdvancePaymentInformationDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            AdvancePaymentInformationExcelListener excelReadListener = new AdvancePaymentInformationExcelListener();
        EasyExcel.read(inputStream,AdvancePaymentInformationDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<AdvancePaymentInformationDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("预收款信息导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<AdvancePaymentInformation> advancePaymentInformationes =BeanCopyUtils.convertListTo(dtoS,AdvancePaymentInformation::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::AdvancePaymentInformation-import::id", importId, advancePaymentInformationes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<AdvancePaymentInformation> advancePaymentInformationes = (List<AdvancePaymentInformation>) orionJ2CacheService.get("pmsx::AdvancePaymentInformation-import::id", importId);
        log.info("预收款信息导入的入库数据={}", JSONUtil.toJsonStr(advancePaymentInformationes));

        this.saveBatch(advancePaymentInformationes);
        orionJ2CacheService.delete("pmsx::AdvancePaymentInformation-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::AdvancePaymentInformation-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<AdvancePaymentInformation> condition = new LambdaQueryWrapperX<>( AdvancePaymentInformation. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(AdvancePaymentInformation::getCreateTime);
        List<AdvancePaymentInformation> advancePaymentInformationes =   this.list(condition);

        List<AdvancePaymentInformationDTO> dtos = BeanCopyUtils.convertListTo(advancePaymentInformationes, AdvancePaymentInformationDTO::new);

        String fileName = "预收款信息数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", AdvancePaymentInformationDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<AdvancePaymentInformationVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class AdvancePaymentInformationExcelListener extends AnalysisEventListener<AdvancePaymentInformationDTO> {

        private final List<AdvancePaymentInformationDTO> data = new ArrayList<>();

        @Override
        public void invoke(AdvancePaymentInformationDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<AdvancePaymentInformationDTO> getData() {
            return data;
        }
    }


}
