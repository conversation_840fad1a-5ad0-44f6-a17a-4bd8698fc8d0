package com.chinasie.orion.manager.impl.msg;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.conts.MsgBusinessTypeEnum;
import com.chinasie.orion.domain.dto.SchemeMsgDTO;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.entity.ProjectSchemeApplyApproval;
import com.chinasie.orion.manager.SendMessageCommonAdapter;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.service.ProjectSchemeService;
import com.chinasie.orion.service.ProjectService;
import com.chinasie.orion.service.impl.ProjectSchemeApplyApprovalServiceImpl;
import com.chinasie.orion.tenant.core.context.TenantContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * 提交计划调整申请时 ，消息推送
 * 清除待办：
 *
 * @see ProjectSchemeApplyApprovalServiceImpl#agree
 * @see ProjectSchemeApplyApprovalServiceImpl#reject
 */
@Slf4j
@Component("modifyApproveSendMsgAdapter")
public class ModifyApproveSendMsgAdapter extends SendMessageCommonAdapter {

    @Resource
    private ProjectService projectService;

    @Resource
    private ProjectSchemeService projectSchemeService;

    /**
     * 项目计划列表页
     */
    private final String JUMP_URL = "/pms/menuComponents?id=%s&tabsId=projectPlan";

    @Override
    protected <T> List<SendMessageDTO> buildMscMessageDTO(SchemeMsgDTO schemeMsgDTO) throws Exception {
        List<SendMessageDTO> msgDtoLis = CollUtil.toList();
        List<ProjectSchemeApplyApproval> approvalList = schemeMsgDTO.getProjectSchemeApprovalList();
        for (ProjectSchemeApplyApproval item : approvalList) {
            Project project = getProjectInfo(item.getProjectId());
            ProjectScheme projectScheme = getProjectSchemeInfo(item.getProjectSchemeId());
            SendMessageDTO sendMsc = SendMessageDTO.builder()
                    .businessData(JSON.toJSONString(MapUtil.builder().put("flowType", TYPE_FLOW_TYPE_MAP.get(MsgBusinessTypeEnum.MODIFY_APPROVE)).build()))
                    .businessId(packageBusinessId(MsgBusinessTypeEnum.MODIFY_APPROVE, item.getId()))
                    .todoStatus(0)
                    .todoType(TodoTypeDict.TODO_TYPE_TASK)
                    .urgencyLevel(0)
                    .messageMap(MapUtil.builder(new HashMap<String, Object>())
                            .put("$projectName$", project.getName())
                            .put("$schemeName$", projectScheme.getName())
                            .build())
                    .businessNodeCode(TYPE_CODE_MAP.get(MsgBusinessTypeEnum.MODIFY_APPROVE))
                    .businessTypeCode("ProjectScheme")
                    .businessTypeName("项目计划")
                    .titleMap(MapUtil.builder(new HashMap<String, Object>())
                            .put("$projectName$", project.getName())
                            .put("$schemeName$", projectScheme.getName())
                            .build())
                    .messageUrl(String.format(JUMP_URL, project.getId()))
                    .messageUrlName("详情")
                    .recipientIdList(getRecipientIds(projectScheme, schemeMsgDTO.getRecipientIds()))
                    .senderTime(new Date())
                    .senderId(userHelper.getUserId())
                    .platformId(CurrentUserHelper.getPId())
                    .orgId(TenantContextHolder.getTenantId())
                    .build();
            msgDtoLis.add(sendMsc);
        }
        return msgDtoLis;
    }

    private Project getProjectInfo(String projectId) throws Exception {
        return projectService.getById(projectId);
    }

    private ProjectScheme getProjectSchemeInfo(String projectSchemeId) throws Exception {
        return projectSchemeService.getById(projectSchemeId);
    }

    private List<String> getRecipientIds(ProjectScheme scheme, List<String> ids) {
        return CollUtil.isNotEmpty(ids) ? ids : CollUtil.toList(scheme.getCreatorId());
    }
}
