package com.chinasie.orion.management.service;


import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.dto.ContractInfoDTO;
import com.chinasie.orion.management.domain.dto.NcfFormPurchOrderDTO;
import com.chinasie.orion.management.domain.entity.ContractInfo;
import com.chinasie.orion.management.domain.vo.ContractInfoVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * ContractInfo 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
public interface ContractInfoService extends OrionBaseService<ContractInfo> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    ContractInfoVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param contractInfoDTO
     */
    String create(ContractInfoDTO contractInfoDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param contractInfoDTO
     */
    Boolean edit(ContractInfoDTO contractInfoDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ContractInfoVO> pages(Page<ContractInfoDTO> pageRequest) throws Exception;

    /**
     * 根据合同编号查询合同行列表信息
     * <p>
     * * @param code
     */
    Page<ContractInfoVO> getByCode(Page<ContractInfoDTO> pageRequest) throws Exception;

    /**
     * 根据合同编号查询合同行列表信息
     * <p>
     * * @param code
     */
    Page<ContractInfoVO> getLxByCode(Page<ContractInfoDTO> pageRequest) throws Exception;

    /**
     * 查询总数及金额
     * <p>
     * * @param searchConditions
     * * @param response
     */
    Map<String, Object> getNumMoney(Page<ContractInfoDTO> pageRequest);

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(ContractInfoDTO contractInfoDTO, HttpServletResponse response) throws Exception;

    void exportFrameByExcel(ContractInfoDTO contractInfoDTO, HttpServletResponse response) throws Exception;


    /**
     * 框架发送邮件提醒
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void sendEmailAndRemind();

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<ContractInfoVO> vos) throws Exception;

    /**
     * 获取时间段内数据
     * <p>
     * * @param searchConditions
     * * @param response
     */
    List<ContractInfo> getMonthList(LocalDate start, LocalDate end);

    Long getMonthContractNum(LocalDate start, LocalDate end);

    Long getMonthContractWinnerSupplierNum(LocalDate start, LocalDate end);

    Integer getRecordMonthList(LocalDate start, LocalDate end);

    /**
     * 获取审批完成数据
     * <p>
     * * @param searchConditions
     * * @param response
     */
    List<ContractInfo> getOverList(LocalDate start, LocalDate end);

    List<ContractInfo> getOverAvgList(LocalDate start, LocalDate end);

    List<ContractInfo> getOverAvgAllList(LocalDate start, LocalDate end);

    /**
     * 获取采购比计划数据-采购立项金额
     * <p>
     * * @param searchConditions
     * * @param response
     */
    List<ContractInfo> getApproavalList(LocalDate start, LocalDate end);

    /**
     * 获取采购较立项数据-审批价格
     * <p>
     * * @param searchConditions
     * * @param response
     */
    List<ContractInfo> getCostList(LocalDate start, LocalDate end);

    /**
     * 获取审批完成总价合同及子合同
     * <p>
     * * @param searchConditions
     * * @param response
     */
    List<String> getCompleteList(LocalDate start, LocalDate end);

    /**
     * 定时给表中合同执行状态  框架合同剩余金额  框架合同已使用金额  支付金额  支付比例  字段赋值
     * <p>
     */
    void updateContractInfo();


     Integer getFirstContractTotal(Date start, Date end);
     Integer getContractTotal(Date start, Date end);

}
