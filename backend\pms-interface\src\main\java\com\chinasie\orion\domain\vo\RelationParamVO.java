package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/02/22/22:21
 * @description:
 */
@ApiModel(value = "RelationParamVO对象", description = "计划关联参数对象")
@Data
public class RelationParamVO  extends ObjectVO implements Serializable {

    @ApiModelProperty(value = "计划ID")
    private String planId;

    /**
     * 参数ID
     */
    @ApiModelProperty(value = "参数ID")
    private String paramId;

    /**
     * 模板ID
     */
    @ApiModelProperty(value = "模板ID")
    private String modelId;

    /**
     * 名称
     */
    @ApiModelProperty(value = "参数名称")
    private String paramName;

    /**
     * 编码
     */
    @ApiModelProperty(value = "参数编码")
    private String paramNumber;

    /**
     * 提供部门名称
     */
    @ApiModelProperty(value = "参数提供部门")
    private String paramProviderDeptNames;

    /**
     * 名称
     */
    @ApiModelProperty(value = "模板名称")
    private String modelName;



    /**
     * 别名
     */
    @ApiModelProperty(value = "别名")
    private List<String> aliases;

    @ApiModelProperty(value = "实列值")
    private String insValue;

    @ApiModelProperty(value = "实列名称")
    private String insName;

    @ApiModelProperty(value = "实列创建时间")
    private Date insCreateTime;

    @ApiModelProperty(value = "实列id")
    private String insId;
}
