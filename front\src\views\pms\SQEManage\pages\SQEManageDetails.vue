<script setup lang="ts">
import { IDataStatus, Layout3, isPower } from 'lyra-component-vue3';
import {
  computed, onMounted, provide, reactive, ref, Ref, watchEffect,
} from 'vue';
import { useRoute } from 'vue-router';
import BasicInfo from './components/BasicInfo.vue';
import Api from '/@/api';

interface DetailsDataType {
  id: string,
  name: string,
  className: string,
  projectCode: string,
  ownerName?: string | undefined,
  status?: string | undefined | number,
  dataStatus?: IDataStatus | undefined,

  [propName: string]: any
}

const route = useRoute();
const actionId: Ref<string | null> = ref('');
const dataId = computed(() => route.params?.id);
const detailsData: DetailsDataType = reactive({
  id: '',
  name: '',
  className: '',
  projectCode: '',
});
provide('detailsData', detailsData);
const projectData = computed(() => ({
  name: detailsData.eventTopic,
  projectCode: detailsData.hiddenDangerCode,
  dataStatus: detailsData.dataStatus,
  ownerName: detailsData.ownerName,
}));

const menuData = computed(() => [
  {
    id: 'info',
    name: '基本信息',
    powerCode: 'PMS_AZHGLXQ_container_01',
  },
]);

function menuChange({ id }) {
  actionId.value = id;
}

watchEffect(() => {
  if (!actionId.value || (actionId.value && menuData.value.filter((item) => isPower(item.powerCode, powerData.value)).findIndex((item) => item.id === actionId.value) === -1)) {
    actionId.value = menuData.value?.[0]?.id;
  }
});

onMounted(() => {
  getDetails();
});

const powerData: Ref<any[]> = ref(undefined);
provide('powerData', powerData);
const loading: Ref<boolean> = ref(false);

async function getDetails() {
  loading.value = true;
  try {
    const result: Record<string, any> = await new Api('/pms/safety-quality-env').fetch({
      pageCode: 'PMSSQEManageDetails',
    }, dataId.value, 'GET');
    Object.keys(result || {}).forEach((key) => {
      detailsData[key] = result[key];
    });
    powerData.value = result?.detailAuthList;
  } finally {
    loading.value = false;
  }
}

</script>

<template>
  <Layout3
    v-get-power="{powerData}"
    v-loading="loading"
    :defaultActionId="actionId"
    :menuData="menuData"
    :projectData="projectData"
    :type="2"
    @menuChange="menuChange"
  >
    <template v-if="detailsData?.id">
      <BasicInfo v-if="actionId==='info'" />
    </template>
  </Layout3>
</template>

<style scoped lang="less">

</style>
