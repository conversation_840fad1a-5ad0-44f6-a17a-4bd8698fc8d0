<template>
  <layout :options="{ body: { scroll: true } }">
    <div class="productLibraryIndex1 layoutPage">
      <div class="productLibraryIndex_content layoutPage_content">
        <div class="productLibraryIndex_title">
          <div class="btnItem">
            <BasicButton
              v-if=" isPower('XMX_container_button_38', powerData) "
              class="mr10"
              @click="addNode"
            >
              <!-- <PlusCircleOutlined /> -->
              <PlusOutlined />

              <span class="labelSpan">创建风险</span>
            </BasicButton>
            <BasicButton
              v-if=" isPower('XMX_container_button_43', powerData) "
              class="mr10"
              @click="solveOdd"
            >
              <!-- <ImportOutlined /> -->
              <LinkOutlined />
              <span class="labelSpan">分解问题</span>
            </BasicButton>
            <BasicButton
              v-if=" isPower('XMX_container_button_44', powerData) "
              class="mr10"
              @click="solveEve"
            >
              <!-- <ExportOutlined /> -->
              <!-- <DisconnectOutlined /> -->
              <LinkOutlined />

              <span class="labelSpan">批量分解</span>
            </BasicButton>
            <!-- <div class="productLibraryIndex_btn blueFont">
              <ImportOutlined />
              <span class="labelSpan">导入数据</span>
            </div>
            <div class="productLibraryIndex_btn blueFont">
              <ExportOutlined />
              <span class="labelSpan">导出数据</span>
            </div> -->

            <!-- <div class="productLibraryIndex_btn" @click="multiDelete">
              <DeleteOutlined />
              <span class="labelSpan">批量删除</span>
            </div> -->
          </div>
          <div
            v-if=" isPower('XMX_container_button_45', powerData) "
            class="btnItem searchcenter"
          >
            <a-input-search
              v-model:value="searchvlaue"
              placeholder="请输入名称或编号"
              style="width: 240px; margin-right: 8px"
              allow-clear
              @search="onSearch"
            />
          </div>
        </div>
        <div class="productLibraryIndex_table">
          <BasicTable
            v-if="isShowTable"
            class="pdmBasicTable"
            :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
            :columns="columns"
            :data-source="dataSource"
            :bordered="false"
            :can-resize="true"
            :show-index-column="false"
            :pagination="pagination"
            row-key="id"
            @change="handleChange"
            @rowClick="clickRow"
          >
            <template #statusName="{ text }">
              <span
                v-if="text"
                :class="{
                  red: text === '未完成',
                  blue: text === '处理中',
                  green: text === '已处理'
                }"
              >
                {{ text }}
              </span>
            </template>
            <template #createTime="{ text }">
              {{ dayjs(text).format('YYYY-MM-DD HH:mm:ss') }}
            </template>
            <template #riskInfluenceName="{ text }">
              <span
                v-if="text"
                :class="{ red: text === '较大' }"
              >
                {{ text }}
              </span>
            </template>

            <template #predictStartTimeName="{ text }">
              <span
                v-if="text"
                :class="{ red: text === '较近' }"
              >{{ text }}</span>
            </template>
            <template #riskProbabilityName="{ text }">
              <span
                v-if="text"
                :class="{ red: text === '较高' }"
              >
                {{ text }}
              </span>
            </template>
          </BasicTable>
        </div>
      </div>

      <newButtonModal
        :btn-object-data="btnObjectData"
        @clickType="clickType"
      />
      <!-- 查看详情弹窗 -->
      <checkDetails :data="nodeData" />
      <!-- 简易弹窗提醒 -->
      <messageModal
        :title="'确认提示'"
        :show-visible="showVisible"
        @cancel="showVisible = false"
        @confirm="confirm"
      >
        <div class="messageVal">
          <InfoCircleOutlined />
          <span>{{ message }}</span>
        </div>
      </messageModal>
      <!-- 新建/编辑抽屉 -->
      <addProjectModal
        :data="addNodeModalData"
        :list-data="editdataSource"
        :projectid="id"
        @success="successSave"
      />
      <ZkAddModal
        :form-item-arr="solveOddArr"
        :data="odd"
        :projectid="id"
        :other-api="otherOddApi"
        :zkitemids="selectedRowKeys[0]"
        @success="successSave"
      />
      <ZkOddToEven
        :data="even"
        @success="successSave"
      />
      <!-- 高级搜索抽屉 -->
      <searchModal
        :data="searchData"
        :projectid="id"
        @search="searchTable"
      />
    </div>
  </layout>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, computed, onMounted, inject, nextTick, h,
} from 'vue';
import {
  Dropdown, Menu, message, Progress, Button,
} from 'ant-design-vue';
import {
  PlusCircleOutlined,
  InfoCircleOutlined,
  DeleteOutlined,
  ImportOutlined,
  ExportOutlined,
  PlusOutlined,
  LinkOutlined,
} from '@ant-design/icons-vue';
import { formatterTime } from '/@/views/pms/projectLaborer/utils/time';
import addProjectModal from './modal/addProjectModal.vue';
import checkDetails from './modal/checkmodal.vue';
import searchModal from './modal/searchModal.vue';
import messageModal from '/@/views/pms/projectLaborer/components/MessageModal/messageModal.vue';
import { useRouter } from 'vue-router';
import newButtonModal from '/@/views/pms/projectLaborer/componentsList/buttonModal/newButtonModal.vue';
import {
  useActionsRecord, Layout, OrionTable, BasicTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer, BasicButton, isPower,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
// import { columns } from './src/table.config';
import { riskManagePageApi, deleteRiskApi } from '/@/views/pms/projectLaborer/api/riskManege';
import { solveOddArr, otherOddApi } from './src/solveOdd';
import ZkAddModal from '/@/views/pms/projectLaborer/componentsList/ZkAddModal/index';
import ZkOddToEven from '/@/views/pms/projectLaborer/componentsList/ZkOddToEven/index';
import { riskToQuestionApi } from '/@/views/pms/projectLaborer/api/questionManage';
import useIndex from '/@/views/pms/projectLaborer/zkhooks/useLocalS.js';

export default defineComponent({
  name: 'ProductLibraryIndex',
  components: {
    Layout,
    //   basicTitle,
    aDropdown: Dropdown,
    /* 表格 */
    BasicTable,
    aMenu: Menu,
    /* menu子item */
    aMenuItem: Menu.Item,
    /* 添加图标 */
    PlusCircleOutlined,
    /* 删除图标 */
    DeleteOutlined,
    //   提示图标
    InfoCircleOutlined,
    //   addNodeModal,
    messageModal,
    checkDetails,
    newButtonModal,
    /* 新建项目抽屉 */
    addProjectModal,
    /* 进度条 */
    Progress,
    /* 高级搜索 */
    searchModal,
    ImportOutlined,
    ExportOutlined,
    PlusOutlined,
    ZkAddModal,
    LinkOutlined,
    ZkOddToEven,
    BasicButton: Button,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
  },

  setup(props) {
    const state = reactive({
      searchvlaue: '',
      editdataSource: {},
      isShowTable: true,
      selectedRowKeys: [],
      dataSource: [],
      powerData: [],
      tablehttp: {
        orders: [
          {
            asc: false,
            column: '',
          },
        ],

        query: {
          projectId: '',
        },
        pageSize: 10,
        pageNum: 1,
        total: 0,
        queryCondition: [],
      },
      pageSize: 10,
      current: 1,
      total: 20,
      addNodeModalData: {},
      odd: {},
      even: {},
      selectedRows: [],
      showVisible: false,
      message: '',
      nodeData: {},
      searchData: {},
      params: {},
      tableHeight: 400,
      // btnObjectData: {
      //   check: { show: true },
      //   open: { show: true },
      //   delete: { show: true },
      //   edit: { show: true },
      //   search: { show: true },
      // },
    });
    state.powerData = inject('powerData');
    const state6 = reactive({
      btnObjectData: {
        check: { show: computed(() => isPower('XMX_container_button_39', state.powerData)) },
        open: { show: computed(() => isPower('XMX_container_button_40', state.powerData)) },
        delete: { show: computed(() => isPower('XMX_container_button_42', state.powerData)) },
        edit: { show: computed(() => isPower('XMX_container_button_41', state.powerData)) },
        search: { show: computed(() => isPower('XMX_container_button_45', state.powerData)) },
      },
      columns: [
        {
          title: '编号',
          dataIndex: 'number',
          align: 'left',
          key: 'number',

          width: '120px',
          sorter: true,
          ellipsis: true,
        },
        {
          title: '名称',
          dataIndex: 'name',
          key: 'name',
          customRender({ record, text }) {
            return h(
              'span',
              {
                class: computed(() => isPower('XMX_container_button_39', state.powerData)) ? 'action-btn' : '',
                title: text,
                onClick(e) {
                  if (isPower('XMX_container_button_39', state.powerData)) {
                    checkData2(record);
                  }
                  e.stopPropagation();
                },
              },
              text,
            );
          },

          width: '240px',
          align: 'left',
          slots: { customRender: 'name' },
          sorter: true,
          ellipsis: true,
        },

        {
          title: '风险类型',
          dataIndex: 'riskTypeName',
          key: 'riskType',
          width: '100px',
          margin: '0 20px 0 0',
          align: 'left',
          slots: { customRender: 'riskTypeName' },
          sorter: true,
          ellipsis: true,
        },
        {
          title: '发生概率',
          dataIndex: 'riskProbabilityName',
          key: 'riskProbability',
          width: '90px',
          align: 'left',
          slots: { customRender: 'riskProbabilityName' },
          sorter: true,
          ellipsis: true,
        },
        {
          title: '影响程度',
          dataIndex: 'riskInfluenceName',
          key: 'riskInfluence',

          width: '80px',
          align: 'left',
          slots: { customRender: 'riskInfluenceName' },
          sorter: true,
          ellipsis: true,
        },
        {
          title: '预估发生时间',
          dataIndex: 'predictStartTimeName',
          key: 'predictStartTime',

          width: '130px',
          align: 'left',
          slots: { customRender: 'predictStartTimeName' },
          sorter: true,
          ellipsis: true,
        },
        {
          title: '应对策略',
          dataIndex: 'copingStrategyName',
          key: 'copingStrategy',

          width: '100px',
          align: 'left',
          sorter: true,
          ellipsis: true,
          slots: { customRender: 'copingStrategyName' },
        },
        {
          title: '状态',
          dataIndex: 'statusName',
          key: 'status',

          width: '80px',
          align: 'left',
          sorter: true,
          ellipsis: true,
          slots: { customRender: 'statusName' },
        },
        {
          title: '负责人',
          dataIndex: 'principalName',
          key: 'principalId',

          width: '80px',
          align: 'left',
          sorter: true,
          ellipsis: true,
          slots: { customRender: 'principalName' },
        },
        {
          title: '创建日期',
          dataIndex: 'createTime',
          key: 'createTime',

          width: '150px',
          align: 'left',
          sorter: true,
          ellipsis: true,
          slots: { customRender: 'createTime' },
        },
      ],
    });
    /* 分页 */
    const pagination = computed(() => ({
      pageSize: state.tablehttp.pageSize,
      current: state.tablehttp.pageNum,
      total: state.tablehttp.total,
      // showQuickJumper: true,
      showSizeChanger: true,
      showTotal: (total) => `共${total}条`,
    }));
      /* 多选cb */
    const onSelectChange = (selectedRowKeys, selectedRows) => {
      state.selectedRowKeys = selectedRowKeys;
      state.selectedRows = selectedRows;
    };
      /* 页数变化cb */
    const handleChange = (pag, filters, sorter: any) => {
      // 如果是多选触发,则不更新页面
      if (typeof pag.current === 'undefined') return;
      state.tablehttp.pageNum = pag.current;
      state.tablehttp.pageSize = pag.pageSize;
      state.tablehttp.orders[0].asc = sorter.order == 'ascend';
      state.tablehttp.orders[0].column = sorter.columnKey;

      getFormData();
    };
      /* 右按钮 */
    const clickType = (type) => {
      switch (type) {
        case 'edit':
          editNode();
          break;
        case 'check':
          checkData();
          break;
        case 'add':
          addNode();
          break;
        case 'open':
          openDetail();
          break;
        case 'delete':
          // deleteNode();
          multiDelete();
          break;
        case 'search':
          state.searchData = {};
          break;
      }
    };
    const router = useRouter();

    /* 编辑 */
    const editNode = () => {
      if (lengthCheckHandle()) return;
      // state.selectedRows = [];
      state.addNodeModalData = { formType: 'edit' };
      state.editdataSource = [...state.selectedRows];
    };
      /* 删除 */
    const deleteNode = () => {
      if (lengthCheckHandle()) return;
      // state.selectedRows = [];

      state.message = '请确认是否对当前选中数据进行删除？';
      state.showVisible = true;
    };
      /* 简易弹窗的确定cb */
    const confirm = () => {
      // 删除操作
      deletrow();
    };
    const riskDetailsIndexLocal = useIndex('riskDetailsIndexLocal');
    onMounted(() => {
      /* 高度变化 */
      riskDetailsIndexLocal.value = 0;
      state.tableHeight = document.body.clientHeight - 460;

      getFormData();
    });
    /* 删除操作 */
    const deletrow = () => {
      // new Api('/pms')
      //   .fetch(state.selectedRowKeys, `project/removeBatch/`, 'DELETE')
      const love = {
        className: 'RiskManagement',
        moduleName: '项目管理-风险管理-实际风险',
        type: 'DELETE',
        remark: `删除了【${state.selectedRowKeys}】`,
      };
      deleteRiskApi(state.selectedRowKeys, love)
        .then((res) => {
          message.success('删除成功');
          state.showVisible = false;
          getFormData();
        })
        .catch(() => {
          state.showVisible = false;
        });
    };
    const getFormData = async () => {
      state.tablehttp.query.projectId = props.id;
      state.tablehttp.queryCondition.push({
        column: 'projectId',
        type: 'eq',
        link: 'and',
        value: props.id,
      });
      const love = {
        id: props.id,
        className: 'RiskManagement',
        moduleName: '项目管理-风险管理-实际风险',
        type: 'GET',
        remark: `获取/搜索了【${props.id}】实际风险列表`,
      };
      state.isShowTable = false;
      const res = await riskManagePageApi(state.tablehttp, love);
      nextTick(() => {
        state.isShowTable = true;
      });
      state.dataSource = res.content;
      state.tablehttp.total = res.totalSize;
      state.selectedRowKeys = [];
      state.selectedRows = [];
    };
      /* 查看详情 */
    const checkData = () => {
      if (lengthCheckHandle()) return;
      state.nodeData = {
        ...state.dataSource.filter((item) => item.id == state.selectedRowKeys[0]),
      };
    };
    const checkData2 = (data) => {
      state.nodeData = {
        ...[JSON.parse(JSON.stringify(data))],
      };
    };
      /* 检查选择条数fn */
    const lengthCheckHandle = () => {
      if (state.selectedRows.length > 1) {
        message.warning('请选择一条数据进行操作');
        return true;
      }
      if (state.selectedRows.length == 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };
      /* 批量检查选择条数fn */
    const multiLengthCheckHandle = () => {
      if (state.selectedRows.length == 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };
    const searchTable = (params) => {
      state.tablehttp.query = params.params;
      state.tablehttp.queryCondition = params.queryCondition;
      for (const item in params.params) {
        if (params.params[item]) {
          state.tablehttp.queryCondition.push({
            column: item,
            type: 'eq',
            link: 'and',
            value: params.params[item],
          });
        }
      }
      getFormData();
      state.searchvlaue = '';
    };
      /* 打开按钮 */
    const openDetail = () => {
      if (lengthCheckHandle()) return;

      toDetails(state.selectedRows[0]);
      state.searchvlaue = '';
    };
    const toDetails = (data) => {
      router.push({
        name: 'RiskDetails',
        query: {
          id: data.id,
          projectId: props.id,
          type: 0,
        },
      });
    };
      /* 新建项目 */
    const addNode = () => {
      state.addNodeModalData = {
        formType: 'add',
      };
    };
      /* 批量删除 */
    const multiDelete = () => {
      if (multiLengthCheckHandle()) return;

      state.message = '请确认是否对当前选中数据进行删除？';
      state.showVisible = true;
    };
      /* 搜索右上 */
    const onSearch = () => {
      /* gettable */
      state.tablehttp.queryCondition = <any>[
        {
          column: 'name',
          type: 'like',
          link: 'or',
          value: state.searchvlaue,
        },
        {
          column: 'number',
          type: 'like',
          link: 'or',
          value: state.searchvlaue,
        },
      ];
      state.tablehttp.query = { projectId: '' };
      getFormData();
    };
      /* 新建项目成功回调 */
    const successSave = () => {
      state.tablehttp.pageNum = 1;
      state.selectedRowKeys = [];
      state.selectedRows = [];

      getFormData();
      state.searchvlaue = '';
      onSearch();
    };
    const clickRow = (record, index) => {
      const num = state.selectedRowKeys.findIndex((item) => item === record.id);
      num === -1 ? state.selectedRowKeys.push(record.id) : state.selectedRowKeys.splice(num, 1);
      const row = state.selectedRows.findIndex((item) => item.id === record.id);
      row === -1 ? state.selectedRows.push(record) : state.selectedRows.splice(row, 1);
    };
    const solveOdd = () => {
      if (lengthCheckHandle()) return;
      state.odd = {
        formType: 'add',
        configName: state.selectedRows[0].name,
        modifyTitle: '风险转问题',
      };
    };
    const solveEve = () => {
      if (lengthCheckHandle()) return;
      state.even = {
        formType: 'add',
        title: '风险转问题',
        zkprojectId: props.id,
        zkitemId: state.selectedRowKeys[0],
        zkaddfn: riskToQuestionApi,
      };
    };
    return {
      ...toRefs(state),
      ...toRefs(state6),
      solveOdd,
      solveEve,
      clickRow,
      clickType,
      /* 分页 */
      pagination,
      /* 行 */
      // columns,
      /* 多选 */
      onSelectChange,
      /* 多选变化 */
      handleChange,
      /* 格式化时间 */
      formatterTime,
      /* 简易弹窗cb */
      confirm,
      /* 新增按钮 */
      addNode,
      dayjs,
      /* 批量删除 */
      multiDelete,
      /* 搜索右上角 */
      onSearch,
      successSave,
      searchTable,
      solveOddArr,
      otherOddApi,
      isPower,
    };
  },
});
</script>
<style lang="less" scoped>
  .red {
    color: red;
  }
  .blue {
    color: blue;
  }
  .green {
    color: green;
  }

  //   @import url('./src/riskManage.less');
  @import url('/@/views/pms/projectLaborer/statics/style/margin.less');

  @import url('/@/views/pms/projectLaborer/statics/style/page.less');
</style>
