const path = require('path');
const packageName = require('./package.json').name;
// const SpeedMeasurePlugin = require('speed-measure-webpack-plugin');
// const smp = new SpeedMeasurePlugin();
const {
  getPlugins, getRules, isProduction, getOptimization,
} = require('./build');

module.exports = {
  entry: './src/main.ts',
  ...isProduction() ? {} : { devtool: 'eval' },
  ...isProduction() ? {
    // 排除不用打包的依赖
    externals: {
      echarts: 'echarts',
      vue: 'Vue',
      'ant-design-vue': 'antd',
      dayjs: 'dayjs',
      'lyra-component-vue3': 'lyraComponentVue3',
      'vue-router': 'VueRouter',
      'core-js/stable': 'undefined',
      axios: 'axios',
      'vue-i18n': 'VueI18n',
      '@wangeditor/editor': 'wangEditor',
      'crypto-js': 'CryptoJS',
      lodash: '_',
      'lodash-es': '_',
    },
  } : { },
  plugins: [...getPlugins(isProduction())],
  resolve: {
    extensions: [
      '.ts',
      '.tsx',
      '.js',
    ],
    alias: {
      '/@': path.resolve(__dirname, 'src'),
      // process: 'process/browser',
    },
  },
  module: {
    rules: [...getRules(isProduction())],
  },
  // ...isProduction() ? {} : {
  //   cache: {
  //     type: 'filesystem',
  //   },
  // },
  output: {
    ...(isProduction() ? {
      filename: 'static/js/[name]-[contenthash:8].js',
      chunkFilename: 'static/js/[name].[contenthash:8].chunk.js',
      assetModuleFilename: 'static/media/[name].[hash][ext]',
    } : {}),
    path: path.resolve(__dirname, 'dist'),
    library: `${packageName}-[name]`,
    libraryTarget: 'umd',
    publicPath: '/',
    clean: true,
  },
  ...getOptimization(isProduction()),
  devServer: {
    hot: true,
    static: './public',
    port: process.env.PORT,
    historyApiFallback: true,
    client: {
      overlay: false,
    },
    proxy: {
      // '/api/spm': {
      //   // target: 'http://1.14.66.165:18813', // 测试
      //   target: 'http://192.168.0.144:8700/', // 外-开发-思雨
      //   // target: 'http://192.168.17.36:8083', // 开发
      //   // target: 'http://1.14.66.165:38083', // 开发
      //   // target: 'http://orion.poohou.com:4080', // 生产
      //   // target: 'http://192.168.17.81:8083', // 测试
      //   // target: 'http://192.168.17.127:8700', // 测试
      //   changeOrigin: true,
      //   pathRewrite: { '^/api/spm': '' },
      //   ws: true,
      // },
      // '/api/spm': {
      //   target: 'http://192.168.17.103:8700',
      //   changeOrigin: true,
      //   pathRewrite: { '^/api/spm': '' },
      // },
      '/api': {
        target: 'http://183.136.206.207:8083', // 开发-广核
        changeOrigin: true,
        pathRewrite: { '^/api': '' },
      },
      '/file': {
        target: 'http://192.168.17.82:7400/file',
        changeOrigin: true,
        pathRewrite: { '^/file': '' },
      },
    },
    headers: {
      'Access-Control-Allow-Origin': '*',
    },
  },
};
