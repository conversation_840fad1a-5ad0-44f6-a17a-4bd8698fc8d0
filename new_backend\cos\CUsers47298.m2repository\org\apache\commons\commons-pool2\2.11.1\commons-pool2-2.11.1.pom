<?xml version="1.0"?>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<project
    xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache.commons</groupId>
    <artifactId>commons-parent</artifactId>
    <version>52</version>
  </parent>

  <artifactId>commons-pool2</artifactId>
  <version>2.11.1</version>
  <name>Apache Commons Pool</name>

  <inceptionYear>2001</inceptionYear>
  <description>The Apache Commons Object Pooling Library.</description>

  <url>https://commons.apache.org/proper/commons-pool/</url>

  <issueManagement>
    <system>jira</system>
    <url>https://issues.apache.org/jira/browse/POOL</url>
  </issueManagement>

  <scm>
    <connection>scm:git:https://gitbox.apache.org/repos/asf/commons-pool.git</connection>
    <developerConnection>scm:git:https://gitbox.apache.org/repos/asf/commons-pool.git</developerConnection>
    <url>https://gitbox.apache.org/repos/asf?p=commons-pool.git</url>
  </scm>

  <developers>
    <developer>
      <name>Morgan Delagrange</name>
      <id>morgand</id>
      <email></email>
      <organization></organization>
    </developer>
    <developer>
      <name>Geir Magnusson</name>
      <id>geirm</id>
      <email></email>
      <organization></organization>
    </developer>
    <developer>
      <name>Craig McClanahan</name>
      <id>craigmcc</id>
      <email></email>
      <organization></organization>
    </developer>
    <developer>
      <name>Rodney Waldhoff</name>
      <id>rwaldhoff</id>
      <email></email>
      <organization></organization>
    </developer>
    <developer>
      <name>David Weinrich</name>
      <id>dweinr1</id>
      <email></email>
      <organization></organization>
    </developer>
    <developer>
      <name>Dirk Verbeeck</name>
      <id>dirkv</id>
      <email></email>
      <organization></organization>
    </developer>
    <developer>
      <name>Robert Burrell Donkin</name>
      <id>rdonkin</id>
      <email></email>
      <organization>The Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>Sandy McArthur</name>
      <id>sandymac</id>
      <email></email>
      <organization>The Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>Simone Tripodi</name>
      <id>simonetripodi</id>
      <organization>The Apache Software Foundation</organization>
    </developer>
    <developer>
      <id>ggregory</id>
      <name>Gary Gregory</name>
      <email>ggregory at apache.org</email>
      <url>https://www.garygregory.com</url>
      <organization>The Apache Software Foundation</organization>
      <organizationUrl>https://www.apache.org/</organizationUrl>      
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>America/New_York</timezone>
      <properties>
        <picUrl>https://people.apache.org/~ggregory/img/garydgregory80.png</picUrl>
      </properties>
    </developer>
    <developer>
      <name>Matt Sicker</name>
      <id>mattsicker</id>
      <organization>The Apache Software Foundation</organization>
    </developer>
  </developers>
  <contributors>
    <contributor>
      <name>Todd Carmichael</name>
      <email><EMAIL></email>
    </contributor>
  </contributors>

  <dependencies>
    <dependency>
      <groupId>cglib</groupId>
      <artifactId>cglib</artifactId>
      <version>3.3.0</version>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.ow2.asm</groupId>
      <artifactId>asm-util</artifactId>
      <version>9.2</version>
      <optional>true</optional>
    </dependency>
    <!-- testing -->
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.hamcrest</groupId>
      <artifactId>hamcrest-library</artifactId>
      <version>2.2</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <distributionManagement>
    <!-- Cannot define in parent ATM, see COMMONSSITE-26 -->
    <site>
      <id>apache.website</id>
      <name>Apache Commons Site</name>
      <url>scm:svn:https://svn.apache.org/repos/infra/websites/production/commons/content/proper/commons-pool/</url>
    </site>
  </distributionManagement>
  
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
    <commons.componentid>pool</commons.componentid>
    <commons.module.name>org.apache.commons.pool2</commons.module.name>
    <commons.rc.version>RC1</commons.rc.version>
    <!-- Java 8 -->
    <commons.release.version>2.11.1</commons.release.version>
    <commons.release.desc>(Java 8)</commons.release.desc>
    <!-- Java 7 -->
    <commons.release.2.version>2.6.2</commons.release.2.version>
    <commons.release.2.desc>(Java 7)</commons.release.2.desc>
    <!-- Java 6 -->
    <commons.release.3.version>2.4.3</commons.release.3.version>
    <commons.release.3.desc>(Java 6)</commons.release.3.desc>
    <!-- Java 5 -->
    <commons.release.4.version>1.6</commons.release.4.version>
    <commons.release.4.desc>(Java 5)</commons.release.4.desc>
    <commons.release.4.binary.suffix>-bin</commons.release.4.binary.suffix>
    <!-- override parent name, because 1.x uses different artifactId -->
    <commons.release.4.name>commons-pool-${commons.release.4.version}</commons.release.4.name>
    <commons.jira.id>POOL</commons.jira.id>
    <commons.jira.pid>12310488</commons.jira.pid>
    <commons.scmPubCheckoutDirectory>site-content</commons.scmPubCheckoutDirectory>
    <commons.osgi.import>net.sf.cglib.proxy;resolution:=optional,*</commons.osgi.import>
    <commons.animal-sniffer.version>1.20</commons.animal-sniffer.version>
    <commons.checkstyle-plugin.version>3.1.2</commons.checkstyle-plugin.version>
    <commons.checkstyle.version>8.45.1</commons.checkstyle.version>
    <commons.felix.version>5.1.2</commons.felix.version>
    <spotbugs.plugin.version>4.3.0</spotbugs.plugin.version>
    <spotbugs.impl.version>4.3.0</spotbugs.impl.version>

    <!-- Commons Release Plugin -->
    <commons.bc.version>2.11.0</commons.bc.version>
    <commons.release.isDistModule>true</commons.release.isDistModule>
    <commons.releaseManagerName>Gary Gregory</commons.releaseManagerName>    
    <commons.releaseManagerKey>86fdc7e2a11262cb</commons.releaseManagerKey>
    
    <commons.japicmp.version>0.15.3</commons.japicmp.version>
    <clirr.skip>true</clirr.skip>
    <japicmp.skip>false</japicmp.skip>
    <junit.version>5.8.0-M1</junit.version>
    <spotbugs.skip>false</spotbugs.skip>
  </properties> 

  <build>
      <defaultGoal>clean verify apache-rat:check checkstyle:check japicmp:cmp javadoc:javadoc spotbugs:check</defaultGoal>
      <pluginManagement>
        <plugins>
          <plugin>
            <groupId>org.apache.felix</groupId>
            <artifactId>maven-bundle-plugin</artifactId>
            <version>${commons.felix.version}</version>
            <dependencies>
              <dependency>
                <groupId>biz.aQute.bnd</groupId>
                <artifactId>biz.aQute.bndlib</artifactId>
                <version>5.3.0</version>
              </dependency>
            </dependencies>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-release-plugin</artifactId>
            <configuration>
              <!-- correct config for GIT projects using staging -->
              <pushChanges>false</pushChanges>
              <localCheckout>true</localCheckout>
            </configuration>
          </plugin>
          <plugin>
            <!--  Fixes org.apache.bcel.classfile.ClassFormatException: Invalid byte tag in constant pool: 19 -->
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-project-info-reports-plugin</artifactId>
            <version>${commons.project-info.version}</version>
            <dependencies>
              <dependency>
                <groupId>org.apache.bcel</groupId>
                <artifactId>bcel</artifactId>
                <version>6.5.0</version>
              </dependency>
            </dependencies>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-checkstyle-plugin</artifactId>
            <version>${commons.checkstyle-plugin.version}</version>
            <dependencies>
              <dependency>
                <groupId>com.puppycrawl.tools</groupId>
                <artifactId>checkstyle</artifactId>
                <version>${commons.checkstyle.version}</version>
              </dependency>
            </dependencies>
            <configuration>
              <configLocation>${basedir}/checkstyle.xml</configLocation>
              <enableRulesSummary>false</enableRulesSummary>
              <headerLocation>${basedir}/license-header.txt</headerLocation>
            </configuration>
          </plugin>
          <plugin>
            <groupId>com.github.spotbugs</groupId>
            <artifactId>spotbugs-maven-plugin</artifactId>
            <version>${spotbugs.plugin.version}</version>
            <dependencies>
              <dependency>
                <groupId>com.github.spotbugs</groupId>
                <artifactId>spotbugs</artifactId>
                <version>${spotbugs.impl.version}</version>
              </dependency>
            </dependencies>
            <configuration>
              <excludeFilterFile>${basedir}/findbugs-exclude-filter.xml</excludeFilterFile>
            </configuration>
          </plugin>
        </plugins>
      </pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.rat</groupId>
          <artifactId>apache-rat-plugin</artifactId>
          <configuration>
            <excludes>
              <exclude>src/test/resources/test1</exclude>
              <exclude>src/test/resources/test2</exclude>
              <exclude>.checkstyle</exclude>
              <exclude>.fbprefs</exclude>
              <exclude>.pmd</exclude>
            </excludes>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>3.0.0-M5</version>
            <configuration>
             <!-- Don't allow test to run for more than 30 minutes -->
              <forkedProcessTimeoutInSeconds>1800</forkedProcessTimeoutInSeconds>
              <includes>
                <include>**/Test*.java</include>
              </includes>
              <excludes>
                <!-- nested classes are not handled properly by Surefire -->
                <exclude>**/Test*$*.java</exclude>
                <!-- Don't run this test by default - it uses lots of memory -->
                <exclude>**/TestSoftRefOutOfMemory.java</exclude>
              </excludes>
            </configuration>
          </plugin>
        <plugin>
          <artifactId>maven-assembly-plugin</artifactId>
          <configuration>
            <descriptors>
              <descriptor>src/assembly/bin.xml</descriptor>
              <descriptor>src/assembly/src-tar-gz.xml</descriptor>
              <descriptor>src/assembly/src-zip.xml</descriptor>
            </descriptors>
            <tarLongFileMode>gnu</tarLongFileMode>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-scm-publish-plugin</artifactId>
          <configuration>
            <ignorePathsToDelete>
              <ignorePathToDelete>api-*</ignorePathToDelete>
            </ignorePathsToDelete>
          </configuration>
        </plugin>
      <plugin>
        <groupId>com.github.siom79.japicmp</groupId>
        <artifactId>japicmp-maven-plugin</artifactId>
        <configuration>
          <parameter>
            <overrideCompatibilityChangeParameters>
              <overrideCompatibilityChangeParameter>
                <compatibilityChange>METHOD_NEW_DEFAULT</compatibilityChange>
                <binaryCompatible>true</binaryCompatible>
                <sourceCompatible>true</sourceCompatible>
                <semanticVersionLevel>PATCH</semanticVersionLevel>
              </overrideCompatibilityChangeParameter>
            </overrideCompatibilityChangeParameters>
          </parameter>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive>
            <manifestEntries>
              <Automatic-Module-Name>${commons.module.name}</Automatic-Module-Name>
            </manifestEntries>
          </archive>
        </configuration>
      </plugin>
      </plugins>
    </build>

    <reporting>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-changes-plugin</artifactId>
          <version>${commons.changes.version}</version>
          <configuration>
            <xmlPath>${basedir}/src/changes/changes.xml</xmlPath>
            <template>release-notes.vm</template>
            <templateDirectory>src/changes</templateDirectory>
          </configuration>
          <reportSets>
            <reportSet>
              <reports>
                 <report>changes-report</report>
              </reports>
            </reportSet>
          </reportSets>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-checkstyle-plugin</artifactId>
        </plugin>
        <plugin>
          <groupId>com.github.spotbugs</groupId>
          <artifactId>spotbugs-maven-plugin</artifactId>
        </plugin>
        <plugin>
          <groupId>org.apache.rat</groupId>
          <artifactId>apache-rat-plugin</artifactId>
          <configuration>
            <excludes>
              <exclude>src/test/resources/test1</exclude>
              <exclude>src/test/resources/test2</exclude>
              <exclude>.checkstyle</exclude>
              <exclude>.fbprefs</exclude>
              <exclude>.pmd</exclude>
            </excludes>
          </configuration>
        </plugin>
        <plugin>
          <groupId>com.github.siom79.japicmp</groupId>
          <artifactId>japicmp-maven-plugin</artifactId>
          <configuration>
            <parameter>
              <overrideCompatibilityChangeParameters>
                <overrideCompatibilityChangeParameter>
                  <compatibilityChange>METHOD_NEW_DEFAULT</compatibilityChange>
                  <binaryCompatible>true</binaryCompatible>
                  <sourceCompatible>true</sourceCompatible>
                  <semanticVersionLevel>PATCH</semanticVersionLevel>
                </overrideCompatibilityChangeParameter>
              </overrideCompatibilityChangeParameters>
            </parameter>
          </configuration>
        </plugin>    
        <plugin>
          <artifactId>maven-pmd-plugin</artifactId>
          <version>3.14.0</version>
          <configuration>
            <targetJdk>${maven.compiler.target}</targetJdk>
          </configuration>
          <reportSets>
            <reportSet>
              <reports>
                <report>pmd</report>
                <report>cpd</report>
              </reports>
            </reportSet>
          </reportSets>
        </plugin>
    </plugins>
  </reporting>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.junit</groupId>
        <artifactId>junit-bom</artifactId>
        <version>${junit.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>
    
  <profiles>
    <profile>
      <id>java9</id>
      <activation>
        <jdk>9</jdk>
      </activation>
      <properties>
        <!-- coverall version 4.3.0 does not work with java 9, see https://github.com/trautonen/coveralls-maven-plugin/issues/112 -->
        <coveralls.skip>true</coveralls.skip>
      </properties>
    </profile>
    <profile>
      <id>java16</id>
      <!-- For testing with CGLIB on Java 16 and 17-ea. -->
      <activation>
        <jdk>[16,)</jdk>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <argLine>
                --illegal-access=permit
                --add-opens java.base/java.lang=ALL-UNNAMED                     
              </argLine>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

</project>
