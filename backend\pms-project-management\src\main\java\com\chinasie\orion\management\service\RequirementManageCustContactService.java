package com.chinasie.orion.management.service;


import com.chinasie.orion.management.domain.dto.RequirementManageCustContactDTO;
import com.chinasie.orion.management.domain.entity.RequirementManageCustContact;
import com.chinasie.orion.management.domain.entity.RequirementMangement;
import com.chinasie.orion.management.domain.vo.RequirementManageCustContactVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;


/**
 * <p>
 * RequirementManageCustContact 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-06 11:00:57
 */
public interface RequirementManageCustContactService extends OrionBaseService<RequirementManageCustContact> {

    /**
     * 详情
     * <p>
     * * @param id
     */
    RequirementManageCustContactVO detail(String id, String pageCode);

    /**
     * 需求单保存客户相关联系人
     *
     * @param contacts    联系人
     * @param requirement 需求单
     */
    void saveRequirementContracts(List<RequirementManageCustContact> contacts, RequirementMangement requirement);

    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids);


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<RequirementManageCustContactVO> pages(Page<RequirementManageCustContactDTO> pageRequest);

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<RequirementManageCustContactVO> vos);
}
