<<<<<<< HEAD
@startuml 数据校验模型

!define TAB<PERSON>(name,desc) class name as "desc" << (T,#FFAAAA) >>
!define PK(x) <b><color:#b8861b>x</color></b>
!define FK(x) <color:#aaaaaa>x</color>

' 定义实体和关系
TABLE(pms_market_contract, "pms_market_contract\n市场合同表") {
  PK(id) : varchar(64) '主键
  class_name : varchar(64) '创建人
  creator_id : varchar(64) '创建人
  modify_time : datetime '修改时间
  owner_id : varchar(64) '拥有者
  create_time : datetime '创建时间
  modify_id : varchar(64) '修改人
  remark : varchar(1024) '备注
  platform_id : varchar(64) '平台ID
  org_id : varchar(64) '业务组织Id
  status : int '状态
  logic_status : int '逻辑删除字段
  number : varchar(64) '合同编号
  name : varchar(64) '合同名称
  contract_type : varchar(64) '合同类型
  quote_id : varchar(64) '定价id
  quote_number : varchar(64) '报价编号
  fream_contract : varchar(64) '框架合同编号
  tech_rsp_user : varchar(64) '技术负责人
  tech_rsp_dept : varchar(64) '承担部门
  contract_amt : decimal(20, 6) '合同金额
  currency : varchar(64) '币种
  tak_effect_date : datetime '生效日期
  end_date : datetime '终止日期
  rel_trans_appr : bit(1) '关联交易审批
  trans_appr_id : varchar(64) '交易审批id
  trans_appr_number : varchar(64) '交易审批单号
  content : varchar(1024) '主要内容
  is_quality_period : bit(1) '是否有质保期
  quality_end_date : datetime '质保到期日
  is_quality_amt : bit(1) '是否有质保金
  quality_amt : decimal(20, 6) '质保金额
  quality_level : varchar(64) '质保等级
  sign_time : datetime '签订时间
  begin_time : datetime '开始时间
  end_time : datetime '结束时间
  end_type : varchar(64) '完结类型
  sign_remark : varchar(1024) '签署备注
  commerce_rsp_user : varchar(64) '合同商务接口人
  close_date : datetime '关闭日期
  close_user_id : varchar(64) '关闭用户id
  is_purchase : bit(1) '是否需要采购
  frame_contract_id : varchar(64) '关联框架合同id
  frame_contract_amt : decimal(23, 6) '框架合同金额
  close_type : varchar(10) '关闭类型(0:正常关闭;1:合同未签署)
  requirement_id : varchar(64) '需求Id
  contract_sign_user_id : varchar(500) '合同签署人id
  contract_sign_user_name : varchar(500) '合同签署人名称
  cust_person_id : varchar(64) '客户id。pms_customer_info id
  cust_group_in_out : varchar(64) '客户-客户关系。编码
  cust_bus_revenue_type : varchar(64) '客户-业务收入类型。编码
  cust_sale_bus_type : varchar(64) '客户-销售业务分类。客户关系 + 所属行业
  office_leader : varchar(64) '所级负责人
  contract_method : varchar(64) '合同获取方式
  departmental : varchar(64) '技术接口人--所级
  priority : varchar(10) '优先级1低2中3高
  subOrder_type : varchar(255) '框架子订单类型
  client_project_nature : varchar(200) '客户项目性质
  business_type : varchar(255) '业务类型
  returned_money : decimal(20, 2) '已回款金额
  unrelated_reason : varchar(2048) '不关联交易原因
  data_sources : bit(1) '数据来源：0-系统录入(包含新增，导入)；1-数据导入(数据库导入)
}

TABLE(pmsx_quotation_management, "pmsx_quotation_management\n报价管理表") {
  PK(id) : varchar(64) '主键
  class_name : varchar(64) '创建人
  creator_id : varchar(64) '创建人
  modify_time : datetime '修改时间
  owner_id : varchar(64) '拥有者
  create_time : datetime '创建时间
  modify_id : varchar(64) '修改人
  remark : varchar(4096) '备注
  platform_id : varchar(64) '平台ID
  org_id : varchar(64) '业务组织Id
  status : int '状态
  logic_status : int '逻辑删除字段
  requirement_number : varchar(64) '项目编号-需求编号
  quotation_id : varchar(64) '报价单编码
  re_quotation_id : varchar(64) '重新报价，原报价单ID,pmsx_quotation_management 的id
  busi_goal : text '业务目标
  busi_goal_cont : text '业务目标内容
  busi_info : text '业务信息
  busi_info_cont : text '业务信息内容
  cost_est_res : text '成本估算（资源）
  cost_est_res_cont : text '成本估算（资源）内容
  cost_est_rr_res : text '成本估算（人力、资源占用）
  cost_est_hr_res_cont : text '成本估算（人力、资源占用）内容
  rev_anal : text '收益分析
  rev_anal_cont : text '收益分析内容
  other_info : text '其他信息
  other_info_cont : text '其他信息内容
  quote_content : text '报价内容
  quote_plan_detail : text '报价方案详情
  quote_amt : decimal(20, 6) '报价金额
  currency : varchar(64) '报出币种
  floor_price : decimal(20, 6) '底线价格
  issue_time : datetime '报价发出时间
  issuer : varchar(64) '发出报价用户
  result : varchar(64) '报价结果
  result_note : varchar(512) '报价结果备注
  quotation_status : varchar(64) '报价状态
  requirement_id : varchar(64) '需求ID
  fieldwork : varchar(8) '是否涉及现场工作
  incl_financing_trade : varchar(8) '是否涉及融资贸易业务
  quote_accept_pen : varchar(64) '报价接收人
  quote_accept_com : varchar(64) '报价接收方
  issue_way : varchar(64) '报价发出途径
  quotation_name : varchar(64) '报价名称
  quote_remark : varchar(512) '报价备注
  obsolete_reason : varchar(512)
  re_quote_reason : varchar(512)
  quote_execu_condition : varchar(64) '报价执行情况
  fin_trade_bus : varchar(255) '是否融资贸易业务
  business_type : varchar(255) '业务类型
  office_leader : varchar(64) '所级负责人
  ywsrlx : varchar(64) '业务收入类型
  send_out_user : varchar(64) '系统中触发发出报价的用户
  send_out_time : datetime '系统中触发发出报价的时间
  priority : varchar(10) '优先级1低2中3高
  winning_bid_amount : decimal(20, 2) '中标金额
}

TABLE(pms_contract_milestone, "pms_contract_milestone\n合同里程碑表") {
  PK(id) : varchar(64) '主键
  class_name : varchar(64) '创建人
  creator_id : varchar(64) '创建人
  modify_time : datetime '修改时间
  owner_id : varchar(64) '拥有者
  create_time : datetime '创建时间
  modify_id : varchar(64) '修改人
  remark : varchar(1024) '备注
  platform_id : varchar(64) '平台ID
  org_id : varchar(64) '业务组织Id
  status : int '状态
  logic_status : int '逻辑删除字段
  milestone_name : varchar(64) '里程碑名称
  milestone_type : varchar(64) '里程碑类型1-里程碑 0 -子里程碑
  parent_id : varchar(64) '关联里程碑Id
  tech_rsp_user : varchar(64) '技术负责人
  bus_rsp_user : varchar(64) '商务负责人
  plan_accept_date : datetime '合同约定验收日期
  cost_bus_type : varchar(256) '业务分类
  milestone_amt : decimal(20, 2) '合同约定验收金额
  undert_dept : varchar(64) '承接部门
  number : varchar(64) '里程碑编号
  rsp_user : varchar(64) '负责人
  actual_accept_date : datetime '实际验收日期
  actual_milestone_amt : decimal(10, 0) '实际验收金额
  total_accept_rate : decimal(10, 2) '累计验收比例
  FK(contract_id) : varchar(64) '合同id
  contract_number : varchar(64) '合同编号
  tax_rate : decimal(20, 2) '税率
  expect_accept_date : datetime '初始预估验收日期
  description : text '描述
  cust_person_id : varchar(64) '客户id
  is_plan : int '是否创建计划数据 0：否 1：是
  office_dept : varchar(64) '所级部门，pmi_dept id
  office_leader : varchar(64) '所级负责人
  departmental : varchar(64) '技术接口人--所级
  departmental_name : varchar(64) '技术接口人--所级
  income_type : varchar(255) '收入确认类型
  is_provisional_estimate : int '是否暂估0：否 1：是
  except_acceptance_amt : decimal(20, 2) '初始预估验收金额
  milestone_provisional_estimate_amt : decimal(10, 2) '里程碑已暂估金额
  milestone_advance_amt : decimal(10, 2) '里程碑已预收款开票金额（价税合计）
  contract_supplier_signed_main : varchar(255) '开票主体名称
  except_invoice_date : datetime '预计开票日期
  currency : varchar(255) '币种
  amt_tax : varchar(255) '含税金额
  amt_no_tax : varchar(255) '不含税金额
  income_plan_code : varchar(255) '收入计划编号
  project_code : varchar(255) '项目编号
  mile_income_type : varchar(255) '里程碑收入类型
  wbsCode : varchar(255) 'wbs编号
  signed_main_name : varchar(255) '开票主体名称
  ammount_type : varchar(64) '金额类型
  date_type : varchar(64) '日期类型
  voucher_num : varchar(255) '凭证号
  pass_account_date : datetime '过帐日期
  confirm_income_provisional_estimate : decimal(20, 2) '确认收入金额-暂估收入
  confirm_income_invoicing : decimal(20, 2) '确认收入金额-开票收入
  confirm_income_sum : decimal(20, 2) '确认收入金额-合计值
  milestone_number : varchar(255) '里程碑编号
  is_track_confirm : datetime '是否被跟踪确认
  business_income_type : varchar(255) '业务收入类型
  sequence_number : varchar(255) '里程碑序号
  project_name : varchar(255) '项目名称
  initiation_time : datetime '立项时间
  confirm_allocation_status : varchar(10) '确认分配状态 1:待分配,2:已分配
  planned_acceptance_amount : decimal(20, 2) '计划验收金额
  planned_acceptance_date : datetime '计划验收日期
}

TABLE(pms_requirement_mangement, "pms_requirement_mangement\n需求管理表") {
  PK(id) : varchar(64) '主键
  class_name : varchar(64) '创建人
  creator_id : varchar(64) '创建人
  modify_time : datetime '修改时间
  owner_id : varchar(64) '拥有者
  create_time : datetime '创建时间
  modify_id : varchar(64) '修改人
  remark : varchar(1024) '备注
  platform_id : varchar(64) '平台ID
  org_id : varchar(64) '业务组织Id
  status : int '状态
  logic_status : int '逻辑删除字段
  requirement_number : varchar(255) '需求编号
  requirement_name : varchar(64) '需求标题
  res_source : varchar(64) '需求来源
  bid_opening_tm : datetime '开标时间
  sign_start_time : datetime '报名开始日期
  sign_end_time : datetime '报名结束日期
  sign_deadln_time : datetime '报价截止时间
  cust_person : varchar(64) '客户
  cust_person_name : varchar(64) '客户名称
  cust_scope : varchar(64) '客户范围
  cust_con_person : varchar(64) '客户主要联系人
  cust_con_person_name : varchar(64) '客户主要联系人名称
  cust_contact_ph : varchar(64) '客户主要联系人电话
  cust_bs_person : varchar(255) '客户商务接口人
  cust_tec_person : varchar(255) '客户技术接口人
  business_person : varchar(64) '商务接口人
  business_person_name : varchar(64)
  tech_res_name : varchar(64)
  tech_res : varchar(64) '技术接口人(技术负责人)
  req_ownership : varchar(64) '需求归属中心
  cooperate_person : varchar(255) '配合部门接口人
  cooperate_dpt : varchar(255) '配合部门
  project_status : varchar(64) '需求状态
  response_status : varchar(64) '响应状态
  confirm_remark : varchar(512)
  section_name : varchar(64) '标段名称
  cust_dpt_name : varchar(64) '客户部门
  ecp_status : varchar(64) 'ECP状态
  ecp_update_time : datetime 'ECP上次更新时间
  undertake_dept : varchar(255) '承接部门
  is_published : varchar(255) '是否发布公示
  ecp_group : varchar(255) '来源ecp组织
  ecp_group_name : varchar(255) '来源ecp组织名称
  business_type : varchar(255) '业务类型
  had_quotation : tinyint '已报价 0.否 1.是 默认 0
  distribute_time : datetime '分发时间
  applicant_user : varchar(64) '报名申请人
  applicant_dept : varchar(64) '报名部门
  applicant_time : datetime '报名时间
  delete_flag : varchar(255) 'ECP系统删除标识
  priority : varchar(10) '优先级1低2中3高
  close_reason : varchar(1024) '关闭原因
  office_leader : varchar(64) '所级负责人
  unrelated_reason : varchar(2048) '不关联交易原因
  trans_appr_id : varchar(255) '关联交易表单Id
  rel_trans_appr : bit(1) '是否关联交易表单
  trans_appr_number : varchar(255) '关联交易表单编号
}

' 定义关系
pms_market_contract "1" -- "0..*" pms_contract_milestone : "包含 >\n合同ID"
pms_market_contract "0..*" -- "1" pmsx_quotation_management : "< 关联\n报价ID"
pms_market_contract "0..*" -- "1" pms_requirement_mangement : "< 关联\n需求ID"
pmsx_quotation_management "0..*" -- "1" pms_requirement_mangement : "< 关联\n需求ID"

=======
@startuml 数据校验模型

!define TABLE(name,desc) class name as "desc" << (T,#FFAAAA) >>
!define PK(x) <b><color:#b8861b>x</color></b>
!define FK(x) <color:#aaaaaa>x</color>

' 定义实体和关系
TABLE(pms_market_contract, "pms_market_contract\n市场合同表") {
  PK(id) : varchar(64) '主键
  class_name : varchar(64) '创建人
  creator_id : varchar(64) '创建人
  modify_time : datetime '修改时间
  owner_id : varchar(64) '拥有者
  create_time : datetime '创建时间
  modify_id : varchar(64) '修改人
  remark : varchar(1024) '备注
  platform_id : varchar(64) '平台ID
  org_id : varchar(64) '业务组织Id
  status : int '状态
  logic_status : int '逻辑删除字段
  number : varchar(64) '合同编号
  name : varchar(64) '合同名称
  contract_type : varchar(64) '合同类型
  quote_id : varchar(64) '定价id
  quote_number : varchar(64) '报价编号
  fream_contract : varchar(64) '框架合同编号
  tech_rsp_user : varchar(64) '技术负责人
  tech_rsp_dept : varchar(64) '承担部门
  contract_amt : decimal(20, 6) '合同金额
  currency : varchar(64) '币种
  tak_effect_date : datetime '生效日期
  end_date : datetime '终止日期
  rel_trans_appr : bit(1) '关联交易审批
  trans_appr_id : varchar(64) '交易审批id
  trans_appr_number : varchar(64) '交易审批单号
  content : varchar(1024) '主要内容
  is_quality_period : bit(1) '是否有质保期
  quality_end_date : datetime '质保到期日
  is_quality_amt : bit(1) '是否有质保金
  quality_amt : decimal(20, 6) '质保金额
  quality_level : varchar(64) '质保等级
  sign_time : datetime '签订时间
  begin_time : datetime '开始时间
  end_time : datetime '结束时间
  end_type : varchar(64) '完结类型
  sign_remark : varchar(1024) '签署备注
  commerce_rsp_user : varchar(64) '合同商务接口人
  close_date : datetime '关闭日期
  close_user_id : varchar(64) '关闭用户id
  is_purchase : bit(1) '是否需要采购
  frame_contract_id : varchar(64) '关联框架合同id
  frame_contract_amt : decimal(23, 6) '框架合同金额
  close_type : varchar(10) '关闭类型(0:正常关闭;1:合同未签署)
  requirement_id : varchar(64) '需求Id
  contract_sign_user_id : varchar(500) '合同签署人id
  contract_sign_user_name : varchar(500) '合同签署人名称
  cust_person_id : varchar(64) '客户id。pms_customer_info id
  cust_group_in_out : varchar(64) '客户-客户关系。编码
  cust_bus_revenue_type : varchar(64) '客户-业务收入类型。编码
  cust_sale_bus_type : varchar(64) '客户-销售业务分类。客户关系 + 所属行业
  office_leader : varchar(64) '所级负责人
  contract_method : varchar(64) '合同获取方式
  departmental : varchar(64) '技术接口人--所级
  priority : varchar(10) '优先级1低2中3高
  subOrder_type : varchar(255) '框架子订单类型
  client_project_nature : varchar(200) '客户项目性质
  business_type : varchar(255) '业务类型
  returned_money : decimal(20, 2) '已回款金额
  unrelated_reason : varchar(2048) '不关联交易原因
  data_sources : bit(1) '数据来源：0-系统录入(包含新增，导入)；1-数据导入(数据库导入)
}

TABLE(pmsx_quotation_management, "pmsx_quotation_management\n报价管理表") {
  PK(id) : varchar(64) '主键
  class_name : varchar(64) '创建人
  creator_id : varchar(64) '创建人
  modify_time : datetime '修改时间
  owner_id : varchar(64) '拥有者
  create_time : datetime '创建时间
  modify_id : varchar(64) '修改人
  remark : varchar(4096) '备注
  platform_id : varchar(64) '平台ID
  org_id : varchar(64) '业务组织Id
  status : int '状态
  logic_status : int '逻辑删除字段
  requirement_number : varchar(64) '项目编号-需求编号
  quotation_id : varchar(64) '报价单编码
  re_quotation_id : varchar(64) '重新报价，原报价单ID,pmsx_quotation_management 的id
  busi_goal : text '业务目标
  busi_goal_cont : text '业务目标内容
  busi_info : text '业务信息
  busi_info_cont : text '业务信息内容
  cost_est_res : text '成本估算（资源）
  cost_est_res_cont : text '成本估算（资源）内容
  cost_est_rr_res : text '成本估算（人力、资源占用）
  cost_est_hr_res_cont : text '成本估算（人力、资源占用）内容
  rev_anal : text '收益分析
  rev_anal_cont : text '收益分析内容
  other_info : text '其他信息
  other_info_cont : text '其他信息内容
  quote_content : text '报价内容
  quote_plan_detail : text '报价方案详情
  quote_amt : decimal(20, 6) '报价金额
  currency : varchar(64) '报出币种
  floor_price : decimal(20, 6) '底线价格
  issue_time : datetime '报价发出时间
  issuer : varchar(64) '发出报价用户
  result : varchar(64) '报价结果
  result_note : varchar(512) '报价结果备注
  quotation_status : varchar(64) '报价状态
  requirement_id : varchar(64) '需求ID
  fieldwork : varchar(8) '是否涉及现场工作
  incl_financing_trade : varchar(8) '是否涉及融资贸易业务
  quote_accept_pen : varchar(64) '报价接收人
  quote_accept_com : varchar(64) '报价接收方
  issue_way : varchar(64) '报价发出途径
  quotation_name : varchar(64) '报价名称
  quote_remark : varchar(512) '报价备注
  obsolete_reason : varchar(512)
  re_quote_reason : varchar(512)
  quote_execu_condition : varchar(64) '报价执行情况
  fin_trade_bus : varchar(255) '是否融资贸易业务
  business_type : varchar(255) '业务类型
  office_leader : varchar(64) '所级负责人
  ywsrlx : varchar(64) '业务收入类型
  send_out_user : varchar(64) '系统中触发发出报价的用户
  send_out_time : datetime '系统中触发发出报价的时间
  priority : varchar(10) '优先级1低2中3高
  winning_bid_amount : decimal(20, 2) '中标金额
}

TABLE(pms_contract_milestone, "pms_contract_milestone\n合同里程碑表") {
  PK(id) : varchar(64) '主键
  class_name : varchar(64) '创建人
  creator_id : varchar(64) '创建人
  modify_time : datetime '修改时间
  owner_id : varchar(64) '拥有者
  create_time : datetime '创建时间
  modify_id : varchar(64) '修改人
  remark : varchar(1024) '备注
  platform_id : varchar(64) '平台ID
  org_id : varchar(64) '业务组织Id
  status : int '状态
  logic_status : int '逻辑删除字段
  milestone_name : varchar(64) '里程碑名称
  milestone_type : varchar(64) '里程碑类型1-里程碑 0 -子里程碑
  parent_id : varchar(64) '关联里程碑Id
  tech_rsp_user : varchar(64) '技术负责人
  bus_rsp_user : varchar(64) '商务负责人
  plan_accept_date : datetime '合同约定验收日期
  cost_bus_type : varchar(256) '业务分类
  milestone_amt : decimal(20, 2) '合同约定验收金额
  undert_dept : varchar(64) '承接部门
  number : varchar(64) '里程碑编号
  rsp_user : varchar(64) '负责人
  actual_accept_date : datetime '实际验收日期
  actual_milestone_amt : decimal(10, 0) '实际验收金额
  total_accept_rate : decimal(10, 2) '累计验收比例
  FK(contract_id) : varchar(64) '合同id
  contract_number : varchar(64) '合同编号
  tax_rate : decimal(20, 2) '税率
  expect_accept_date : datetime '初始预估验收日期
  description : text '描述
  cust_person_id : varchar(64) '客户id
  is_plan : int '是否创建计划数据 0：否 1：是
  office_dept : varchar(64) '所级部门，pmi_dept id
  office_leader : varchar(64) '所级负责人
  departmental : varchar(64) '技术接口人--所级
  departmental_name : varchar(64) '技术接口人--所级
  income_type : varchar(255) '收入确认类型
  is_provisional_estimate : int '是否暂估0：否 1：是
  except_acceptance_amt : decimal(20, 2) '初始预估验收金额
  milestone_provisional_estimate_amt : decimal(10, 2) '里程碑已暂估金额
  milestone_advance_amt : decimal(10, 2) '里程碑已预收款开票金额（价税合计）
  contract_supplier_signed_main : varchar(255) '开票主体名称
  except_invoice_date : datetime '预计开票日期
  currency : varchar(255) '币种
  amt_tax : varchar(255) '含税金额
  amt_no_tax : varchar(255) '不含税金额
  income_plan_code : varchar(255) '收入计划编号
  project_code : varchar(255) '项目编号
  mile_income_type : varchar(255) '里程碑收入类型
  wbsCode : varchar(255) 'wbs编号
  signed_main_name : varchar(255) '开票主体名称
  ammount_type : varchar(64) '金额类型
  date_type : varchar(64) '日期类型
  voucher_num : varchar(255) '凭证号
  pass_account_date : datetime '过帐日期
  confirm_income_provisional_estimate : decimal(20, 2) '确认收入金额-暂估收入
  confirm_income_invoicing : decimal(20, 2) '确认收入金额-开票收入
  confirm_income_sum : decimal(20, 2) '确认收入金额-合计值
  milestone_number : varchar(255) '里程碑编号
  is_track_confirm : datetime '是否被跟踪确认
  business_income_type : varchar(255) '业务收入类型
  sequence_number : varchar(255) '里程碑序号
  project_name : varchar(255) '项目名称
  initiation_time : datetime '立项时间
  confirm_allocation_status : varchar(10) '确认分配状态 1:待分配,2:已分配
  planned_acceptance_amount : decimal(20, 2) '计划验收金额
  planned_acceptance_date : datetime '计划验收日期
}

TABLE(pms_requirement_mangement, "pms_requirement_mangement\n需求管理表") {
  PK(id) : varchar(64) '主键
  class_name : varchar(64) '创建人
  creator_id : varchar(64) '创建人
  modify_time : datetime '修改时间
  owner_id : varchar(64) '拥有者
  create_time : datetime '创建时间
  modify_id : varchar(64) '修改人
  remark : varchar(1024) '备注
  platform_id : varchar(64) '平台ID
  org_id : varchar(64) '业务组织Id
  status : int '状态
  logic_status : int '逻辑删除字段
  requirement_number : varchar(255) '需求编号
  requirement_name : varchar(64) '需求标题
  res_source : varchar(64) '需求来源
  bid_opening_tm : datetime '开标时间
  sign_start_time : datetime '报名开始日期
  sign_end_time : datetime '报名结束日期
  sign_deadln_time : datetime '报价截止时间
  cust_person : varchar(64) '客户
  cust_person_name : varchar(64) '客户名称
  cust_scope : varchar(64) '客户范围
  cust_con_person : varchar(64) '客户主要联系人
  cust_con_person_name : varchar(64) '客户主要联系人名称
  cust_contact_ph : varchar(64) '客户主要联系人电话
  cust_bs_person : varchar(255) '客户商务接口人
  cust_tec_person : varchar(255) '客户技术接口人
  business_person : varchar(64) '商务接口人
  business_person_name : varchar(64)
  tech_res_name : varchar(64)
  tech_res : varchar(64) '技术接口人(技术负责人)
  req_ownership : varchar(64) '需求归属中心
  cooperate_person : varchar(255) '配合部门接口人
  cooperate_dpt : varchar(255) '配合部门
  project_status : varchar(64) '需求状态
  response_status : varchar(64) '响应状态
  confirm_remark : varchar(512)
  section_name : varchar(64) '标段名称
  cust_dpt_name : varchar(64) '客户部门
  ecp_status : varchar(64) 'ECP状态
  ecp_update_time : datetime 'ECP上次更新时间
  undertake_dept : varchar(255) '承接部门
  is_published : varchar(255) '是否发布公示
  ecp_group : varchar(255) '来源ecp组织
  ecp_group_name : varchar(255) '来源ecp组织名称
  business_type : varchar(255) '业务类型
  had_quotation : tinyint '已报价 0.否 1.是 默认 0
  distribute_time : datetime '分发时间
  applicant_user : varchar(64) '报名申请人
  applicant_dept : varchar(64) '报名部门
  applicant_time : datetime '报名时间
  delete_flag : varchar(255) 'ECP系统删除标识
  priority : varchar(10) '优先级1低2中3高
  close_reason : varchar(1024) '关闭原因
  office_leader : varchar(64) '所级负责人
  unrelated_reason : varchar(2048) '不关联交易原因
  trans_appr_id : varchar(255) '关联交易表单Id
  rel_trans_appr : bit(1) '是否关联交易表单
  trans_appr_number : varchar(255) '关联交易表单编号
}

' 定义关系
pms_market_contract "1" -- "0..*" pms_contract_milestone : "包含 >\n合同ID"
pms_market_contract "0..*" -- "1" pmsx_quotation_management : "< 关联\n报价ID"
pms_market_contract "0..*" -- "1" pms_requirement_mangement : "< 关联\n需求ID"
pmsx_quotation_management "0..*" -- "1" pms_requirement_mangement : "< 关联\n需求ID"

>>>>>>> fcf168aff39a310b2d11d6ddb32fea5602b814c5
@enduml 