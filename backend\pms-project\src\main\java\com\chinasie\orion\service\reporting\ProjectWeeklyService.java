package com.chinasie.orion.service.reporting;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinasie.orion.domain.dto.reporting.ProjectWeeklyDTO;
import com.chinasie.orion.domain.dto.reporting.WeeklyBatchAuditParamDTO;
import com.chinasie.orion.domain.dto.reporting.WeeklySingleAuditParamDTO;
import com.chinasie.orion.domain.entity.reporting.ProjectWeekly;
import com.chinasie.orion.domain.entity.reporting.ProjectWeeklyContent;
import com.chinasie.orion.domain.request.reporting.AddWeeklyRequest;
import com.chinasie.orion.domain.request.reporting.ListDailyRequest;
import com.chinasie.orion.domain.request.reporting.ListWeeklyRequest;
import com.chinasie.orion.domain.vo.reporting.*;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.PageResult;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * <p>
 * ProjectWeekly 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-14 09:44:12
 */
public interface ProjectWeeklyService  extends OrionBaseService<ProjectWeekly> {
    /**
     *  详情
     *
     * * @param id
     */
    ProjectWeeklyVO detail(String id,String pageCode)  throws Exception;

    /**
     *  新增
     *
     * * @param projectWeeklyDTO
     */
    boolean create( ProjectWeeklyDTO projectWeeklyDTO)  throws Exception;

    /**
     *  编辑
     *
     * * @param projectWeeklyDTO
     */
    Boolean edit(ProjectWeeklyDTO projectWeeklyDTO) throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     */
    PageResult<ProjectWeeklyVO> pages(com.chinasie.orion.sdk.metadata.page.Page<ListWeeklyRequest> pageRequest) throws Exception;

    /**
     * 审核
     * @param weeklySingleAuditParamDTO
     * @return
     * @throws Exception
     */
    Boolean audit(WeeklySingleAuditParamDTO weeklySingleAuditParamDTO)throws Exception;

    /**
     * 批量审核
     * @param weeklyBatchAuditParamDTO
     * @return
     * @throws Exception
     */
    Boolean batchAudit(WeeklyBatchAuditParamDTO weeklyBatchAuditParamDTO)throws Exception;

    /**
     * 消息提醒
     *
     * @param id 数据id
     */
    Boolean warn(String id);

    /**
     *  卡片页面审核
     * @param request
     */
    TreeMap<Integer, ProjectWeeklyCardVO> checkCard(ListWeeklyRequest request) throws Exception;



    /**
     *  提交
     * @param id
     */
    Boolean submitById(String id);

    /**
     *  导出
     * @param listWeeklyRequest
     */
    void exportData(ListWeeklyRequest listWeeklyRequest, HttpServletResponse response) throws Exception;

    ProjectWeeklyVO getWeeklyContent(ProjectWeeklyDTO projectWeeklyDTO) throws Exception;

}
