package com.chinasie.orion.domain.dto;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.DeptDataBind;
import com.chinasie.orion.sdk.core.data.bind.DictDataBind;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


@Data
@ExcelIgnoreUnannotated
public class ContractMilestoneTreeExcelExportDTO implements Serializable {


    @ApiModelProperty(value = "id")
    private String id;


    @ApiModelProperty(value = "工作主题")
    @ExcelProperty(value = "工作主题 ", index = 0)
    private String workTopic;



    @ApiModelProperty("流程发起人工号")
    @ExcelProperty(value = "发起人工号 ", index = 1)
    private String flowCreatePersonNumber;

    @ApiModelProperty("流程发起人")
    @ExcelProperty(value = "发起人姓名 ", index = 2)
    private String flowCreatePersonName;

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    @TableField(value = "contract_id")
    private String contractId;

    @ApiModelProperty("流程状态")
    private Integer contractStatus;

    @ApiModelProperty("合同流程状态")
    @ExcelProperty(value = "流程状态 ", index = 3)
    private String contractStatusName;


    /**
     * 关联里程碑Id
     */
    @ApiModelProperty(value = "关联里程碑Id")
    private String parentId;

    /**
     * 承接部门
     */
    @ApiModelProperty(value = "承担部门")
    private String undertDept;

    /**
     * 承担部门名称
     */
    @ApiModelProperty(value = "承担部门名称")
    @ExcelProperty(value = "承担部门 ", index = 4)
    private String undertDeptName;


    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 5)
    private String contractNumber;


    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "项目合同名称 ", index = 6)
    private String contractName;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private String cusPersonId;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户")
    @ExcelProperty(value = "客户名称 ", index = 7)
    private String cusPersonName;


    @ApiModelProperty(value = "币种")
    private String currency;


    @ApiModelProperty(value = "币种")
    @ExcelProperty(value = "币种 ", index = 8)
    private String currencyName;


    /**
     * 合同金额
     */
    @ApiModelProperty(value = "合同金额")
    @ExcelProperty(value = "合同金额 ", index = 9)
    private BigDecimal contractAmt;

    @ApiModelProperty(value = "客户-销售业务分类。客户关系 + 所属行业")
    private String custSaleBusType;

    @ApiModelProperty(value = "客户-销售业务分类。客户关系 + 所属行业")
    @ExcelProperty(value = "销售业务分类 ", index = 10)
    private String custSaleBusTypeName;



    @ApiModelProperty(value = "里程碑金额")
    @ExcelProperty(value = "合同预计收入 ", index = 11)
    private BigDecimal milestoneAmt;


    /**
     * 里程碑名称
     */
    @ApiModelProperty(value = "里程碑名称")
    @ExcelProperty(value = "标题 ", index = 12)
    private String milestoneName;


    /**
     * 计划验收日期
     */
    @ApiModelProperty(value = "计划验收日期")
    @ExcelProperty(value = "计划验收日期 ", index = 13)
    private Date planAcceptDate;

    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "流程发起日期")
    @ExcelProperty(value = "流程发起日期", index = 14)
    private Date flowStartTime;

    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "流程结束日期")
    @ExcelProperty(value = "流程结束日期", index = 15)
    private Date flowEndTime;

    /**
     * 技术接口人--所级
     */
    @ApiModelProperty(value = "技术接口人--所级")
    private String departmental;

    /**
     * 技术接口人--所级名称
     */
    @ApiModelProperty(value = "技术接口人--所级名称")
    @ExcelProperty(value = "所级", index = 16)
    private String departmentalName;

    /**
     * 成本业务分类
     */
    @ApiModelProperty(value = "成本业务分类")
    @FieldBind(dataBind = DictDataBind.class, type = "cos_business_type", target = "costBusTypeName")
    private String costBusType;

    /**
     * 成本业务分类名称
     */
    @ApiModelProperty(value = "成本业务分类名称")
    @ExcelProperty(value = "业务分类", index = 17)
    private String costBusTypeName;

    /**
     * 税率
     */
    @ApiModelProperty(value = "税率")
    @ExcelProperty(value = "税率", index = 18)
    private BigDecimal taxRate;

    @ApiModelProperty(value = "所级负责人")
    private String officeLeader;

    @ApiModelProperty(value = "所级负责人")
    @ExcelProperty(value = "所级负责人", index = 19)
    private String officeLeaderName;


    @ApiModelProperty(value = "创建人id")
    private String creatorId;

    @ApiModelProperty(value = "创建人")
    @ExcelProperty(value = "创建人", index = 20)
    private String creatorName;


    @ApiModelProperty(value = "创建时间")
    @ExcelProperty(value = "创建时间", index = 21)
    private Date createTime;








}
