<template>
  <BasicDrawer
    :width="1000"
    :title="state.drawerName"
    :showFooter="true"
    @ok="confirmDrawer"
    @register="modalRegister"
    @visibleChange="visibleChange"
  >
    <BasicForm
      v-if="state.showForm"
      ref="formRef"
      @register="registerForm"
    >
      <template #budgetBut>
        <BasicButton
          type="primary"
          @click="openModalRegisterList"
        >
          选择预算
        </BasicButton>
      </template>
      <template #budgetList>
        <ul class="fundDivideUl">
          <li class="fundDivideLi">
            <div class="monthItem">
              预算编码
            </div>
            <div class="amountItem">
              {{ selectedBudget.number }}
            </div>
          </li>

          <li class="fundDivideLi">
            <div class="monthItem">
              预算名称
            </div>
            <div class="amountItem">
              {{ selectedBudget.name }}
            </div>
          </li>
          <li class="fundDivideLi">
            <div class="monthItem">
              成本中心
            </div>
            <div class="amountItem">
              {{ selectedBudget.costCenterName }}
            </div>
          </li>
          <li class="fundDivideLi">
            <div class="monthItem">
              费用科目
            </div>
            <div class="amountItem">
              {{ selectedBudget.expenseAccountName }}
            </div>
          </li>
          <li class="fundDivideLi">
            <div class="monthItem">
              年度
            </div>
            <div class="amountItem">
              {{ selectedBudget.year }}
            </div>
          </li>
        </ul>
      </template>
      <template #budgetUpload>
        <div style="height: 240px;overflow: hidden">
          <UploadList
            :listData="state.successAllList||[]"
            type="modal"
          />

          <!--          <OrionTable-->
          <!--            ref="tableRef"-->
          <!--            :options="tableOptions"-->
          <!--          >-->
          <!--            <template #toolbarLeft>-->
          <!--              <BasicUpload-->
          <!--                class="mb10"-->
          <!--                :max-number="100"-->
          <!--                :multiple="true"-->
          <!--                button-text="上传附件"-->
          <!--                button-type="primary"-->
          <!--                :isClassification="false"-->
          <!--                :accept="'.rar, .zip ,.doc ,.docx ,.pdf ,.jpg ,.png'"-->
          <!--                @saveChange="handleSaveFile"-->
          <!--              />-->
          <!--            </template>-->
          <!--          </OrionTable>-->
        </div>
      </template>
    </BasicForm>
  </basicdrawer>
  <!--  列表弹窗-->
  <CostExecuteModal
    @selectedBudgetTab="selectedBudgetTab"
    @register="modalRegisterList"
  />
</template>

<script setup lang="ts">
import { computed, reactive, ref } from 'vue';
import {
  BasicButton,
  BasicDrawer,
  BasicForm,
  FormSchema,
  openFile,
  UploadList,
  useDrawerInner,
  useForm,
  useModal,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
import Api from '/@/api';
import CostExecuteModal from './costExecuteModal.vue';

const emit = defineEmits(['updatePage']);
const [modalRegisterList, { openModal }] = useModal();
const formRef = ref();
const state = reactive({
  drawerName: '',
  formData: {},
  visibleStatus: false,
  showForm: true,
  id: '',
  projectId: '',
  successAllList: [], //  附件列表
});

const selectedBudget = ref({
  budgetProjectId: '',
  number: '',
  name: '',
  costCenterName: '',
  expenseAccountName: '',
  year: '',

});
const personnels = ref();
function selectedBudgetTab(tabData) {
  if (tabData instanceof Array && tabData.length > 0) {
    selectedBudget.value.budgetProjectId = tabData[0].id;
    selectedBudget.value.number = tabData[0].number;
    selectedBudget.value.name = tabData[0].name;
    selectedBudget.value.costCenterName = tabData[0].costCenterName;
    selectedBudget.value.expenseAccountName = tabData[0].expenseAccountName;
    selectedBudget.value.year = dayjs(tabData[0].year).format('YYYY-MM-DD');

    formRef.value.setFieldsValue({
      costCenterId: tabData[0].costCenterId,
      expenseAccountId: tabData[0].expenseAccountId,
    });
  } else if (Object.keys(tabData).length > 0) {
    // console.log('收到的未选中数据', tabData);
    for (let key in selectedBudget.value) {
      selectedBudget.value[key] = '';
    }
    formRef.value.resetFields(['costCenterId', 'expenseAccountId']);
  }
}
const [modalRegister, { closeDrawer, changeOkLoading }] = useDrawerInner(
  (openProps: { type, formData, projectId}) => {
    state.projectId = openProps.projectId;

    new Api(`/pms/project-role-user/${state.projectId}/user/list`).fetch('', '', 'GET').then((res) => {
      personnels.value = res.map((item) => ({
        value: item.id,
        label: item.name,
      }));
    });
    if (openProps.type === 'edit') {
      state.drawerName = '编辑成本';
      state.id = openProps.formData.id;
      state.successAllList = openProps.formData.attachments;
      formRef.value.setFieldsValue({
        number: openProps.formData.number,
        expenseAccountId: openProps.formData.expenseAccountId,
        costCenterId: openProps.formData.costCenterId,
        outTime: openProps.formData.outTime,
        // outPersonName: openProps.formData.outPersonName,
        outPersonId: openProps.formData.outPersonId,
        outMoney: openProps.formData.outMoney,
        description: openProps.formData.description,
      });
      selectedBudget.value.budgetProjectId = openProps.formData.budgetProjectId;
      selectedBudget.value.number = openProps.formData.projectBudgetVO.number;
      selectedBudget.value.name = openProps.formData.projectBudgetVO.name;
      selectedBudget.value.costCenterName = openProps.formData.projectBudgetVO.costCenterName;
      selectedBudget.value.expenseAccountName = openProps.formData.projectBudgetVO.expenseAccountName;
      selectedBudget.value.year = dayjs(openProps.formData.projectBudgetVO.year).format('YYYY');
    } else {
      state.drawerName = '新增成本';
    }
    // 设置为已打开状态
    state.visibleStatus = true;
  },
);
function visibleChange(visible: boolean) {
  // 窗口关闭时，设置状态值
  !visible && (state.visibleStatus = visible);
  if (!visible) {
    resetForm();
  }
}
// 远程菜单树处理
/** 全部菜单数据处理 */
function parseTreeSelect(menuList) {
  const res = [];
  for (let i in menuList) {
    let tmp = {
      label: menuList[i]?.name,
      value: menuList[i]?.id,
    };
    if (menuList[i].children) {
      tmp.disabled = menuList[i].children.length > 0;
      tmp.children = parseTreeSelect(menuList[i].children);
    }
    res.push(tmp);
  }
  return res;
}
// 预算列表弹窗
function openModalRegisterList() {
  openModal(true, {
    projectId: state.projectId,
  });
}
// 上传文件
function handleSaveFile(successAll, cb) {
  const files = successAll.map((item) => {
    const uniqueId = `id-${new Date().getTime().toString(36)}-${Math.random().toString(36).substr(2, 9)}`;
    const { classification, securityLimit } = item;
    const {
      filePath, filePostfix, fileSize, name,
    } = item.result;
    return {
      id: uniqueId, // 自定义唯一id
      fileTool: item.openTool,
      filePath,
      filePostfix,
      fileSize,
      name,
      secretLevel: classification,
      securityLimit,
    };
  });
  state.successAllList = state.successAllList.concat(files);
  // 编辑上传文件时先调用该接口
  if (state.drawerName === '编辑成本') {
    const api = new Api(`/pms/project-cost/importfiles/${state.id}`).fetch(state.successAllList, '', 'POST');
    cb(api);
  }
}

const schemas: FormSchema[] = [
  {
    field: 'number',
    component: 'Input',
    label: '成本支出编码',
    defaultValue: '新增完成时自动生成编码',
    componentProps: {
      disabled: true,
      placeholder: '新增完成时自动生成编号',
    },
  },
  {
    field: 'expenseAccountId',
    component: 'ApiTreeSelect',
    label: '费用科目',
    defaultValue: '',
    rules: [
      {
        required: true,
        trigger: 'change',
        type: 'string',
      },
    ],
    componentProps: {
      disabled: true,
      // immediate: true,
      api: () => new Api(`/pms/expenseSubject/tree/?status=${1}`).fetch('', '', 'GET').then((res) => parseTreeSelect(res)),
      labelField: 'label',
      valueField: 'value',
    },

  },
  {
    field: 'costCenterId',
    component: 'ApiSelect',
    label: '成本中心',
    defaultValue: '',
    rules: [
      {
        required: true,
        trigger: 'change',
        type: 'string',
      },
    ],
    componentProps: {
      disabled: true,
      // immediate: true,
      api: () => new Api('/pms/costCenter/list').fetch('', '', 'POST'),
      labelField: 'name',
      valueField: 'id',
    },
  },
  {
    field: 'outTime',
    component: 'DatePicker',
    label: '发生时间',
    rules: [
      {
        required: true,
        trigger: 'change',
        type: 'string',
      },
    ],
    componentProps: {
      // picker: 'year',
    },
  },
  {
    field: 'outPersonId',
    component: 'Select',
    label: '发生人',
    rules: [
      {
        required: true,
        type: 'string',
      },
    ],
    componentProps: {
      options: computed(() => personnels.value),
    },
  },
  {
    field: 'outMoney',
    component: 'InputNumber',
    label: '金额',
    defaultValue: 0,
    componentProps: {
      addonAfter: '元',
      style: 'width: 100%',
    },
  },
  {
    field: 'budgetBu',
    component: 'Input',
    label: '',
    slot: 'budgetBut',
    componentProps: {
    },
    colProps: {
      span: 24,
    },
  },
  {
    field: 'budgetList',
    component: 'Input',
    label: '预算列表',
    slot: 'budgetList',
    componentProps: {},
    colProps: {
      span: 24,
    },
  },
  {
    field: 'description',
    component: 'InputTextArea',
    label: '描述',
    componentProps: {
      placeholder: '请输入描述信息',
      maxlength: 1000,
      rows: 3,
    },
    colProps: {
      span: 24,
    },
  },
  {
    field: 'budgetUpload',
    component: 'Input',
    label: '',
    colSlot: 'budgetUpload',
    componentProps: {},
    colProps: {
      span: 24,
    },
  },
];

const [
  registerForm,
  {
    validate, resetFields, getFieldsValue, setFieldsValue,
  },
] = useForm({
  layout: 'vertical',
  schemas,
  baseColProps: {
    span: 12,
  },
});
function resetForm() {
  formRef.value.resetFields();
  for (let key in selectedBudget.value) {
    selectedBudget.value[key] = '';
  }
  state.successAllList = []; // 上传的文件
}
async function confirmDrawer() {
  await formRef.value.validate();
  const values = await formRef.value.getFieldsValue();
  changeOkLoading(true);
  if (state.drawerName === '编辑成本') {
    const data = {
      id: state.id,
      projectId: state.projectId,
      budgetProjectId: selectedBudget.value.budgetProjectId,
      expenseAccountId: values.expenseAccountId,
      costCenterId: values.costCenterId,
      outTime: dayjs(values.outTime).format('YYYY-MM-DD'),
      outPersonId: values.outPersonId,
      outMoney: values.outMoney,
      description: values.description,
      // attachments: state.successAllList.length > 0 ? state.successAllList : undefined,
      attachments: tableData.value,
    };
    if (data.attachments.length > 0) {
      for (let key in data.attachments) {
        if (data.attachments[key].id) {
          delete data.attachments[key].id;
        }
      }
    }
    await new Api('/pms/project-cost').fetch(data, '', 'PUT').then(() => {
    }).finally(() => {
      changeOkLoading(false);
    });
  } else {
    const data = {
      projectId: state.projectId,
      budgetProjectId: selectedBudget.value.budgetProjectId,
      expenseAccountId: values.expenseAccountId,
      costCenterId: values.costCenterId,
      outTime: dayjs(values.outTime).format('YYYY-MM-DD'),
      outPersonId: values.outPersonId,
      outMoney: values.outMoney,
      description: values.description,
      attachments: state.successAllList,

    };
    if (data.attachments.length > 0) {
      for (let key in data.attachments) {
        if (data.attachments[key].id) {
          delete data.attachments[key].id;
        }
      }
    }
    await new Api('/pms/project-cost').fetch(data, '', 'POST').then(async () => {
    }).finally(() => {
      changeOkLoading(false);
    });
  }
  closeDrawer();
  await emit('updatePage');// 更新父组件数据
}
const tableData = computed(() => state.successAllList);
const tableOptions = {
  showToolButton: false,
  showTableSetting: false,
  showSmallSearch: false,
  // rowSelection: {},
  pagination: false,
  dataSource: tableData,
  columns: [
    {
      title: '文件名称',
      dataIndex: 'name',
      customRender({ text, record }) {
        return `${text}.${record.filePostfix}`;
      },
    },
    // {
    //   title: '创建人',
    //   dataIndex: 'creatorName',
    //   customRender({ text }) {
    //     return text || '--';
    //   },
    // },
    // {
    //   title: '创建时间',
    //   dataIndex: 'createTime',
    //   customRender({ text }) {
    //     return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '--';
    //   },
    // },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 200,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '查看',
      onClick(fileData) {
        openFile(fileData);
      },
    },
    {
      text: '删除',
      modal(record) {
        return new Promise((resolve) => {
          const index = tableData.value.findIndex((item) => item.id === record.id);
          tableData.value.splice(index, 1);
          resolve(true);
        })
          .then(() => {
            new Api(`/pms/project-cost/importfiles/${state.id}`).fetch(tableData.value, '', 'POST').then(() => {

            }).then(() => {

            });
          });
      },
    },
  ],
};
</script>
<style scoped lang="less">
:deep(.ant-basic-table){
  padding: 0 ;
}
.labelName{
  padding: 0 0 8px;
  .labelNameAnnotate{
    color: rgba(0,0,0,.25);
  }
}
.fundDivideUl{
  display: flex;
  flex-wrap: nowrap;
  justify-content: center;
  align-content: center;
  width: 100%;
  border: 1px solid #cccccc;
  padding: 0;
  margin-bottom: 0;
  .fundDivideLi{
    list-style-type:none;
    border-right: 1px solid #cccccc;
    flex: 1;
    .monthItem{
      text-align: center;
      height: 38px;
      line-height: 38px;
      border-bottom: 1px solid #cccccc;
    }
    .amountItem{
      text-align: center;
      height: 38px;
      line-height: 38px;
      overflow: auto;
    }
  }
  .fundDivideLi:last-child{
    border:none;
  }

}
</style>
