<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
    class="pdmBasicTable"
  >
    <template #toolbarLeft>
      <BasicButton
        type="primary"
        icon="add"
        @click="addNode"
      >
        添加人员
      </BasicButton>
    </template>
    <template #modifyTime="{ text }">
      {{ dayjs(text).format('YYYY-MM-DD HH:mm:ss') }}
    </template>
  </OrionTable>

  <SelectUserModal
    :on-ok="ok"
    :tree-data-api="treeDataApi"
    :default-select-user-data="selectUserData"
    :default-expand-all="true"
    @register="modalRegister"
  />
</template>
<script lang="ts">
import {
  computed, defineComponent, h, inject, onMounted, reactive, ref, toRefs, watch,
} from 'vue';
import { message, Modal } from 'ant-design-vue';
import dayjs from 'dayjs';
import {
  BasicButton,
  BasicTableAction,
  isPower,
  ITableActionItem,
  OrionTable,
  SelectUserModal,
  useModal,
} from 'lyra-component-vue3';
//   import { peoplePageApi, deletePeopleApi } from '/@/views/pms/api/projectList';
import Api from '/@/api';
// import { peoplePageApi } from '/@/views/pms/projectLaborer/api/projectList';

export default defineComponent({
  name: '',
  components: {
    /* 选择用户 */
    SelectUserModal,
    BasicButton,
    OrionTable,
  },
  props: {
    roleId: {
      type: String,
      default: '',
    },
    projectId: {
      type: String,
      default: '',
    },
    pageType: {
      type: String,
      default: 'page',
    },
  },

  setup(props) {
    const [modalRegister, { openModal }] = useModal();
    const tableRef = ref(null);
    const selectUserData = ref([]);
    const state = reactive({
      powerData: [],
      //   成员id
      projectRoleId: '',
      /* 编辑send */
      editdataSource: {},
      /* 多选 */
      selectedRowKeys: [],
      addNodeModalData: {},
      /* 选择行id */
      selectedRows: [],
      showVisible: false,
      /* 简易弹窗提醒消息 */
      message: '',
      nodeData: {},
      searchData: {},
      params: {},
    });
    state.powerData = inject('powerData');

    watch(
      () => props.roleId,
      async (value) => {
        await getFormData();
      },
    );

    onMounted(() => {

    });

    const getFormData = async () => {
      tableRef.value?.reload();
    };
    const checkData2 = (data) => {
      state.nodeData = {
        ...[JSON.parse(JSON.stringify(data))],
      };
    };

    /* 新建项目 */
    const addNode = () => {
      if (!props.roleId) {
        message.warning('请选择一个项目角色进行操作');
        return;
      }
      selectUserHandle();
    };
    const selectUserHandle = () => {
      openModal();
    };
      /* 新建项目成功回调 */
    const successSave = () => {
      getFormData();
    };

    const ok = (userData) => {
      const params = userData.map((item) => ({
        projectRoleId: props.roleId,
        userId: item.id,
        name: item.name,
        number: item.code,
        projectId: props.projectId,
        remark: item.remark,
      }));
      new Api('/pms').fetch(params, 'project-role-user/approval/saveBatch', 'POST').then((res) => {
        message.success('新增成员成');
        tableRef.value.reload();
      });
    };

    // 生成 queryCondition
    function getListParams(params) {
      if (params.searchConditions) {
        return {
          ...params,
          queryCondition: params.searchConditions.map((item) => ({
            column: item?.[0]?.field,
            type: 'like',
            link: 'or',
            value: item?.[0]?.values?.[0],
          })),
        };
      }
      return params;
    }

    const tableOptions = {
      deleteToolButton: 'add|delete|enable|disable',
      rowSelection: {},
      isFilter2: false,

      api(params) {
        let newParams = getListParams({
          ...params,
          query: {
            projectRoleId: props.roleId,
            projectId: props.projectId,
          },
        });
        return new Api('/pms').fetch(newParams, 'project-role-user/approval/getPage', 'POST');
      },
      showSmallSearch: false,
      tool: [
        {
          type: 'button',
          position: 'before',
          buttonGroup: [
            [
              {
                icon: 'sie-icon-shanchu',
                name: '移除',
                enable: false,
                cb: ({ keys }: any) => {
                  Modal.confirm({
                    content: '您确定要移除成员吗？',
                    onOk() {
                      new Api('/pms').fetch(keys, 'project-role-user/removeBatch', 'DELETE').then((res) => {
                        message.success('移除成功');
                        getFormData();
                      });
                    },
                  });
                },
              },
            ],
          ],
        },
      ],
      columns: [
        {
          title: '姓名',
          dataIndex: 'name',
          minWidth: 80,
        },
        {
          title: '工号',
          dataIndex: 'number',
          key: 'number',
          width: '110px',
          align: 'left',
          slots: { customRender: 'number' },
        },
        {
          title: '所属单位',
          dataIndex: 'deptName1',
          width: '110px',
        },
        {
          title: '所在部门',
          dataIndex: 'deptName',
          width: '110px',
          slots: { customRender: 'deptName' },
        },
        {
          title: '所属专业',
          dataIndex: 'xxxx',
          width: '140px',
          slots: { customRender: 'xxxx' },
        },
        {
          title: '电话',
          dataIndex: 'mobile',
          width: '110px',
          slots: { customRender: 'mobile' },
        },
        {
          title: '邮箱',
          dataIndex: 'email',
          width: '170px',
          slots: { customRender: 'email' },
        },
      ],
    };

    return {
      tableRef,
      ...toRefs(state),
      isPower,
      confirm,
      dayjs,
      successSave,
      modalRegister,
      selectUserHandle,
      ok,
      selectUserData,
      addNode,
      selectionChange(data) {
      },
      treeDataApi: () => new Api('/pmi/organization/treeListPage').fetch(
        {
          orders: [
            {
              asc: false,
              column: '',
            },
          ],
          pageNum: 0,
          pageSize: 0,
          query: { status: 1 },
        },
        '',
        'POST',
      ),
      tableOptions,
    };
  },
});
</script>
<style lang="less" scoped>
</style>
