<template>
  <div>
    <DetailsLayout
      border-bottom
      title="物资服务计划信息"
      :data-source="props.detailsData"
      :list="projectBasicInfo"
      :column="3"
    />
    <BasicTitle1
      title="入库记录"
      class="title"
    />
    <div style="height: 400px;overflow: hidden">
      <OrionTable
        ref="tableRef"
        :options="baseTableOption"
        @selectionChange="selectionChange"
      >
        <template #toolbarLeft>
          <span class="total"> 总入库数量:</span>
          <span> {{ totalNum }}</span>
        </template>
      </OrionTable>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ref, Ref, onMounted, watch,
} from 'vue';
import { useRoute } from 'vue-router';
import { BasicButton, BasicTitle1, OrionTable } from 'lyra-component-vue3';

import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import { getMaterialServices } from '/@/views/pms/api';
import Api from '/@/api';
import { WorkflowAction, WorkflowProps, WorkflowView } from 'lyra-workflow-component-vue3';
import dayjs from 'dayjs';
const props = defineProps({
  detailsData: {
    type: Object,
  },
});
const route = useRoute();
const selectRows = ref([]);
const totalNum = ref();
const tableRef = ref();
// const projectBasicInfo: Ref<any[]> = ref([
//   {
//     label: '物资/服务计划编号',
//     field: 'planNumber',
//   },
//   {
//     label: '需求人',
//     field: 'creatorName',
//   },
//   {
//     label: '需求人工号',
//     field: 'creatorId',
//   },
//   {
//     label: '类型',
//     field: 'type',
//   },
//   {
//     label: '需求日期',
//     field: 'demandTime',
//     formatTime: 'YYYY-MM-DD',
//   },
//   {
//     label: '创建日期',
//     field: 'createTime',
//     formatTime: 'YYYY-MM-DD HH:mm:ss',
//   },
//   {
//     label: '备注',
//     field: 'remark',
//     gridColumn: '1/5',
//   },
// ]);
const projectBasicInfo: Ref<any[]> = ref([
  {
    label: '物资/服务类型',
    field: 'type',
  },
  {
    label: '物资/服务编码',
    field: 'goodsServiceNumber',
  },
  {
    label: '物资/服务描述',
    field: 'description',
  },
  {
    label: '规格型号',
    field: 'normsModel',
  },
  {
    label: '计量单位',
    field: 'unit',
  },
  {
    label: '备注',
    field: 'remark',
    gridColumn: '1/5',
  },
]);

const baseTableOption = {
  api: (params) => getMaterialServices({
    ...params,
    query: { goodsServiceStoreId: route.query?.id },
  }),
  // rowSelection: {},
  showToolButton: false,
  showTableSetting: false,
  showSmallSearch: false,
  columns: [
    {
      title: '类型',
      dataIndex: 'type',
      width: 120,
      customRender({ record }) {
        return record.goodsServiceStoreVO.type;
      },
    },
    {
      title: '物资服务编码',
      dataIndex: 'goodsServiceNumber',
      width: 150,
    },
    {
      title: '物资服务描述',
      dataIndex: 'description',
      width: 120,
    },
    {
      title: '规格型号',
      dataIndex: 'normsModel',
      width: 100,
      customRender({ record }) {
        return record.goodsServiceStoreVO.normsModel;
      },
    },
    {
      title: '计量单位',
      dataIndex: 'unit',
      width: 150,
      customRender({ record }) {
        return record.goodsServiceStoreVO.unit;
      },
    },
    {
      title: '本次入库数量',
      dataIndex: 'storeAmount',
      width: 120,
    },
    {
      title: '入库日期',
      dataIndex: 'storeTime',
      width: 150,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '--';
      },
    },
    {
      title: '添加时间',
      dataIndex: 'createTime',
      width: 180,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '--';
      },
    },
    {
      title: '添加人',
      dataIndex: 'creatorName',
      width: 120,
      customRender({ record }) {
        return record.goodsServiceStoreVO.creatorName;
      },
    },
    {
      title: '备注',
      dataIndex: 'remark',
      minWidth: 150,
    },

  ],
  afterFetch: (res) => {
    totalNum.value = `${res[0].goodsServiceStoreVO.totalStoreAmount}(${res[0].goodsServiceStoreVO.unit})`;
  },
};
function selectionChange({ rows }) {
  selectRows.value = rows;
}
</script>
<style scoped lang="less">
.title{
  margin-left: 20px;
  color: rgb(57, 63, 77);
  font-size: 16px;
  font-weight: 500;
  box-sizing: border-box;

}
.title-wrap{
  padding-left: 12px;
  padding-top: 0;
  padding-bottom: 0;
  line-height: 50px;
  display: flex;
  align-items: center;
}
.title-wrap:before{
  top: auto
}
.total{
  margin-right: 24px;
}
</style>
