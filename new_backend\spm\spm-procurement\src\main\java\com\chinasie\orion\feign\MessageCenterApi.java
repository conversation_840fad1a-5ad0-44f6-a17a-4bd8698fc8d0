package com.chinasie.orion.feign;

import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.domain.dto.MessageTodoStatusDTO;

import com.chinasie.orion.dto.ResponseDTO;

import com.chinasie.orion.feign.dto.MessageIdTodoStatusDTO;
import com.chinasie.orion.quality.UpdateMsgUrlDTO;
import com.chinasie.orion.sdk.core.conf.FeignConfig;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(value = "msc", configuration = FeignConfig.class)
@Lazy
public interface MessageCenterApi {

    @PutMapping("/message-center/todo-status")
    ResponseDTO<?> todoStatus(@RequestBody MessageTodoStatusDTO messageTODOStatusDTO);

    @ApiOperation(value = "待办事项变为已办（多人待办）,删除不是自己办理的", notes = "待办事项变为已办（多人待办）")
    @RequestMapping(value = "/message-center/done-cancel/todo-status", method = RequestMethod.PUT)
    ResponseDTO<Object> todoToDoneAndCancel(@RequestBody MessageTodoStatusDTO todoToDoneRequestDTO);



    @ApiOperation(value ="单个撤回消息实例",notes = "单个撤回消息实例")
    @RequestMapping(value ="message/revocation/{businessId",method = RequestMethod.DELETE)
    ResponseDTO<Boolean> evocationlessage(@PathVariable(value = "businessId") String businessId);

    @PostMapping("/message")
    ResponseDTO<String> createMessage(@RequestBody SendMessageDTO sendMessage);

    @ApiOperation(value = "待办事项变为已办（通过消息ID）【多人待办，其中一人已办，其他人的对应待办取消】", notes = "待办事项变为已办（通过消息ID）【多人待办，其中一人已办，其他人的对应待办取消】")
    @RequestMapping(value = "/message/done-cancel/todo-status/message-id", method = RequestMethod.PUT)
    ResponseDTO<?> todoMessageChangeStatusAndCancelOtherRecipientMsgByMessageIds(@RequestBody MessageIdTodoStatusDTO todoStatus) throws Exception;

    @ApiOperation(value ="待办事项变为已办(只通过业务id)",notes = "待办事项变为已办(只通过业务id)")
    @RequestMapping(value ="/message-center/done/todo-status/{businessId}",method = RequestMethod.PUT)
    ResponseDTO<Boolean> todoMessageChangeStatusByBusinessId(@PathVariable(value = "businessId") String businessId);


    @ApiOperation(value ="批量-待办事项变为已办 (只通过业务ID)",notes = "批量-待办事项变为已办 (只通过业务ID)")
    @RequestMapping(value ="/message-center/done/todo-status/business-ids",method = RequestMethod.PUT)
    ResponseDTO<Boolean> todoMessageChangeStatusByBusinessIds(@RequestBody List<String> businessIdList);

    @ApiOperation(value = "批量编辑消息的链接URL", notes = "批量编辑消息的链接URL")
    @RequestMapping(value = "/message/batch/update-url", method = RequestMethod.PUT)
    ResponseDTO<List<String>> batchUpdateMessageUrl(@RequestBody List<UpdateMsgUrlDTO> list);

}
