package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.DeptDataBind;
import com.chinasie.orion.sdk.core.data.bind.DictDataBind;
import com.chinasie.orion.sdk.core.data.bind.UserDataBind;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ProjectPurchaseOrderInfo Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-06 08:42:57
 */
@TableName(value = "pms_project_purchase_order_info")
@ApiModel(value = "ProjectPurchaseOrderInfo对象", description = "采购订单基本信息")
@Data
public class ProjectPurchaseOrderInfo extends ObjectEntity implements Serializable{

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id" )
    private String projectId;

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    @TableField(value = "number" )
    private String number;

    /**
     * 订单名称
     */
    @ApiModelProperty(value = "订单名称")
    @TableField(value = "name" )
    private String name;

    /**
     * 采购员
     */
    @ApiModelProperty(value = "采购员")
    @TableField(value = "res_user_id" )
    @FieldBind(dataBind = UserDataBind.class, target = "resUserName")
    private String resUserId;

    /**
     * 采购员名称
     */
    @ApiModelProperty(value = "采购员名称")
    @TableField(exist = false)
    private String resUserName;

    /**
     * 采购负责部门名称
     */
    @ApiModelProperty(value = "采购负责部门名称")
    @TableField(exist = false)
    private String rspDeptName;

    /**
     * 采购类型名称
     */
    @ApiModelProperty(value = "采购类型名称")
    @TableField(exist = false)
    private String purchaseTypeName;

    /**
     * 采购负责部门
     */
    @ApiModelProperty(value = "采购负责部门")
    @TableField(value = "rsp_dept_id" )
    @FieldBind(dataBind = DeptDataBind.class, target = "rspDeptName")
    private String rspDeptId;

    /**
     * 含税总金额
     */
    @ApiModelProperty(value = "含税总金额")
    @TableField(value = "have_tax_total_amt" )
    private BigDecimal haveTaxTotalAmt;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    @TableField(value = "currency" )
    private String currency;

    /**
     * 采购总数量
     */
    @ApiModelProperty(value = "采购总数量")
    @TableField(value = "purchase_total_amount" )
    private BigDecimal purchaseTotalAmount;

    /**
     * 采购类型
     */
    @ApiModelProperty(value = "采购类型")
    @TableField(value = "purchase_type" )
    @FieldBind(dataBind = DictDataBind.class, type = "goods_service_Type", target = "purchaseTypeName")
    private String purchaseType;

    /**
     * 订单到货日期
     */
    @ApiModelProperty(value = "订单到货日期")
    @TableField(value = "order_arrival_date" )
    private Date orderArrivalDate;

    /**
     * 订单说明
     */
    @ApiModelProperty(value = "订单说明")
    @TableField(value = "order_desc" )
    private String orderDesc;

    /**
     * 订单类型
     */
    @ApiModelProperty(value = "订单类型")
    @FieldBind(dataBind = DictDataBind.class, type = "projectPurchaseOrderType", target = "orderTypeName")
    @TableField(value = "order_type" )
    private String orderType;

    /**
     * 订单类型名称
     */
    @ApiModelProperty(value = "订单类型名称")
    @TableField(exist = false)
    private String orderTypeName;

    /**
     * 订单执行状态
     */
    @ApiModelProperty(value = "订单执行状态")
    @TableField(value = "order_excute_status" )
    @FieldBind(dataBind = DictDataBind.class, type = "projectPurchaseOrderExcuteStatus", target = "orderExcuteStatusName")
    private String orderExcuteStatus;

    /**
     * 订单执行状态名称
     */
    @ApiModelProperty(value = "订单执行状态名称")
    @TableField(exist = false)
    private String orderExcuteStatusName;

    /**
     * 订单来源
     */
    @ApiModelProperty(value = "订单来源")
    @TableField(value = "order_source" )
    @FieldBind(dataBind = DictDataBind.class, type = "projectPurchaseOrderSource", target = "orderSourceName")
    private String orderSource;

    /**
     * 订单来源名称
     */
    @ApiModelProperty(value = "订单来源名称")
    @TableField(exist = false)
    private String orderSourceName;

}
