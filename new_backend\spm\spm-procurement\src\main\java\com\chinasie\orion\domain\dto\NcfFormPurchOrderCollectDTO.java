package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * NcfFormPurchOrderCollect DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-21 14:55:52
 */
@ApiModel(value = "NcfFormPurchOrderCollectDTO对象", description = "采购-商城集采订单（总表）")
@Data
@ExcelIgnoreUnannotated
public class NcfFormPurchOrderCollectDTO extends ObjectDTO implements Serializable {

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    @ExcelProperty(value = "订单编号 ", index = 0)
    private String orderNumber;

    /**
     * PO订单号
     */
    @ApiModelProperty(value = "PO订单号")
    @ExcelProperty(value = "PO订单号 ", index = 1)
    private String poOrderNumber;

    /**
     * 电商渠道订单号
     */
    @ApiModelProperty(value = "电商渠道订单号")
    @ExcelProperty(value = "电商渠道订单号 ", index = 2)
    private String commerceChannelOrderNumber;

    /**
     * 企业名称
     */
    @ApiModelProperty(value = "企业名称")
    @ExcelProperty(value = "企业名称 ", index = 3)
    private String enterpriseName;

    /**
     * PR公司名称
     */
    @ApiModelProperty(value = "PR公司名称")
    @ExcelProperty(value = "PR公司名称 ", index = 4)
    private String prCompanyName;

    /**
     * 部门
     */
    @ApiModelProperty(value = "部门")
    @ExcelProperty(value = "部门 ", index = 5)
    private String department;

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @ExcelProperty(value = "供应商编码 ", index = 6)
    private String contractId;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @ExcelProperty(value = "供应商名称 ", index = 7)
    private String contractName;

    /**
     * 下单人
     */
    @ApiModelProperty(value = "下单人")
    @ExcelProperty(value = "下单人 ", index = 8)
    private String orderPlacer;

    /**
     * 下单人电话
     */
    @ApiModelProperty(value = "下单人电话")
    @ExcelProperty(value = "下单人电话 ", index = 9)
    private String orderPhoneNumber;

    /**
     * 下单时间
     */
    @ApiModelProperty(value = "下单时间")
    @ExcelProperty(value = "下单时间 ", index = 10)
    private Date orderTime;

    /**
     * 对账人
     */
    @ApiModelProperty(value = "对账人")
    @ExcelProperty(value = "对账人 ", index = 11)
    private String reconciler;

    /**
     * 收货负责人
     */
    @ApiModelProperty(value = "收货负责人")
    @ExcelProperty(value = "收货负责人 ", index = 12)
    private String consignee;

    /**
     * 收货审核人
     */
    @ApiModelProperty(value = "收货审核人")
    @ExcelProperty(value = "收货审核人 ", index = 13)
    private String receiptReviewer;

    /**
     * 支付负责人
     */
    @ApiModelProperty(value = "支付负责人")
    @ExcelProperty(value = "支付负责人 ", index = 14)
    private String paymentManager;

    /**
     * 验收方式
     */
    @ApiModelProperty(value = "验收方式")
    @ExcelProperty(value = "验收方式 ", index = 15)
    private String acceptanceMethod;

    /**
     * 结算方式
     */
    @ApiModelProperty(value = "结算方式")
    @ExcelProperty(value = "结算方式 ", index = 16)
    private String settlementMethod;

    /**
     * 要求到货日期
     */
    @ApiModelProperty(value = "要求到货日期")
    @ExcelProperty(value = "要求到货日期 ", index = 17)
    private Date requestDeliveryDate;

    /**
     * 订单总金额
     */
    @ApiModelProperty(value = "订单总金额")
    @ExcelProperty(value = "订单总金额 ", index = 18)
    private BigDecimal totalOrderAmount;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 19)
    private String number;

    /**
     * 订单待支付
     */
    @ApiModelProperty(value = "订单待支付")
    @ExcelProperty(value = "订单待支付 ", index = 20)
    private String orderPayDay;

    /**
     * 订单确认时间
     */
    @ApiModelProperty(value = "订单确认时间")
    @ExcelProperty(value = "订单确认时间 ", index = 21)
    private Date orderConfirmationTime;

    /**
     * 订单审批时间
     */
    @ApiModelProperty(value = "订单审批时间")
    @ExcelProperty(value = "订单审批时间 ", index = 22)
    private Date orderApprovalTime;

    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间")
    @ExcelProperty(value = "支付时间 ", index = 23)
    private Date paidTime;

    /**
     * 开票时间
     */
    @ApiModelProperty(value = "开票时间")
    @ExcelProperty(value = "开票时间 ", index = 24)
    private Date invoicingTime;

    /**
     * 申请开票时间
     */
    @ApiModelProperty(value = "申请开票时间")
    @ExcelProperty(value = "申请开票时间 ", index = 25)
    private Date applicationForInvoicingTime;

    /**
     * 对账确认时间
     */
    @ApiModelProperty(value = "对账确认时间")
    @ExcelProperty(value = "对账确认时间 ", index = 26)
    private Date reconciliationConfirmationTime;

    /**
     * 对账申请时间
     */
    @ApiModelProperty(value = "对账申请时间")
    @ExcelProperty(value = "对账申请时间 ", index = 27)
    private Date reconciliationApplicationTime;

    /**
     * 发货耗时
     */
    @ApiModelProperty(value = " 发货耗时")
    @ExcelProperty(value = " 发货耗时 ", index = 28)
    private Integer usedTime;

    /**
     * 订单最后一次交货时间
     */
    @ApiModelProperty(value = "订单最后一次交货时间")
    @ExcelProperty(value = "订单最后一次交货时间 ", index = 29)
    private Date timeOfDelivery;

    /**
     * 订单最后一次确认收货时间
     */
    @ApiModelProperty(value = "订单最后一次确认收货时间")
    @ExcelProperty(value = "订单最后一次确认收货时间 ", index = 30)
    private Date timeOfLastReceipt;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态")
    @ExcelProperty(value = "订单状态 ", index = 31)
    private String orderState;

    /**
     * 退货金额
     */
    @ApiModelProperty(value = "退货金额")
    @ExcelProperty(value = "退货金额 ", index = 32)
    private BigDecimal returnAmount;

    /**
     * 下单时间开始
     */
    @ApiModelProperty(value = "下单时间开始")
    @ExcelIgnore
    private String startDate;

    /**
     * 下单时间开始
     */
    @ApiModelProperty(value = "下单时间开始")
    @ExcelIgnore
    private String endDate;
}
