package com.chinasie.orion.service.impl;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.constant.DictConts;
import com.chinasie.orion.constant.MaterialToolTypeEnum;
import com.chinasie.orion.domain.dto.MaterialManageDTO;
import com.chinasie.orion.domain.dto.job.StartEndDTO;
import com.chinasie.orion.domain.dto.material.JobMaterialParamDTO;
import com.chinasie.orion.domain.entity.JobManage;
import com.chinasie.orion.domain.entity.JobMaterial;
import com.chinasie.orion.domain.dto.JobMaterialDTO;
import com.chinasie.orion.domain.entity.JobMaterialRecord;
import com.chinasie.orion.domain.entity.MaterialManage;
import com.chinasie.orion.domain.vo.JobMaterialVO;
import com.chinasie.orion.domain.vo.MaterialManageVO;
import com.chinasie.orion.enums.StatusEnum;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.service.*;
import com.chinasie.orion.repository.JobMaterialMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;

import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * JobMaterial 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-09 15:47:40
 */
@Service
@Slf4j
public class JobMaterialServiceImpl extends  OrionBaseServiceImpl<JobMaterialMapper, JobMaterial>   implements JobMaterialService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    private JobManageService jobManageService;

    private DictRedisHelper dictRedisHelper;

    private BasePlaceService basePlaceService;

    private MaterialManageService materialManageService;

    @Autowired
    private JobMaterialMapper jobMaterialMapper;

    @Autowired
    private JobMaterialService jobMaterialService;

    @Autowired
    private JobNodeStatusService jobNodeStatusService;

    private JobMaterialRecordService jobMaterialRecordService;

    private SchemeToMaterialService schemeToMaterialService;

    @Autowired
    private ClassRedisHelper classRedisHelper;

    @Autowired
    private void setSchemeToMaterialService(SchemeToMaterialService schemeToMaterialService){
        this.schemeToMaterialService = schemeToMaterialService;
    }

    @Autowired
    public void setJobMaterialRecordService(JobMaterialRecordService jobMaterialRecordService) {
        this.jobMaterialRecordService = jobMaterialRecordService;
    }

    @Autowired
    public void setMaterialManageService(MaterialManageService materialManageService) {
        this.materialManageService = materialManageService;
    }

    @Autowired
    public void setBasePlaceService(BasePlaceService basePlaceService) {
        this.basePlaceService = basePlaceService;
    }

    @Autowired
    public void setDictRedisHelper(DictRedisHelper dictRedisHelper) {
        this.dictRedisHelper = dictRedisHelper;
    }

    @Autowired
    public void setJobManageService(JobManageService jobManageService) {
        this.jobManageService = jobManageService;
    }

    @Override
    public List<String> listByMaterialId(String rsp) {
        LambdaQueryWrapperX<JobMaterial> condition = new LambdaQueryWrapperX<>( JobMaterial. class);
        condition.eq(JobMaterial::getMaterialId,rsp);
        condition.select(JobMaterial::getJobId);
        List<JobMaterial> jobMaterials = this.list(condition);
        if(!CollectionUtils.isEmpty(jobMaterials)){
            List<String> jobIds = jobMaterials.stream().map(JobMaterial::getJobId).collect(Collectors.toList());
            return jobIds;
        }
        return new ArrayList<>();
    }

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  JobMaterialVO detail(String id,String pageCode) throws Exception {
        JobMaterial jobMaterial =this.getById(id);
        JobMaterialVO result = BeanCopyUtils.convertTo(jobMaterial,JobMaterialVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param jobMaterialDTO
     */
    @Override
    public  String create(JobMaterialDTO jobMaterialDTO) throws Exception {
        JobMaterial jobMaterial =BeanCopyUtils.convertTo(jobMaterialDTO,JobMaterial::new);
        jobMaterial.setNumber(jobMaterialDTO.getMaterialNumber());
        jobMaterial.setAssetName(jobMaterialDTO.getMaterialName());
        String jobId = jobMaterialDTO.getJobId();
        // 将物资 放入 物资管理库中
        // 回调到 项目计划 人员
        JobManage jobManage= jobManageService.getSimpleInfo(jobId);
//        JobManage jobManage= jobManageService.getJobBaseInfo(jobId);
        if(Objects.isNull(jobManage)){
            throw new BaseException(HttpStatus.METHOD_NOT_ALLOWED.value(), "作业不存在");
//            throw new Exception("作业不存在");
        }else{
            jobMaterial.setBaseCode(jobManage.getJobBase());
            jobMaterial.setBaseName(jobManage.getJobBaseName());
        }
        this.existsData(jobId,jobMaterialDTO.getNumber());
        this.save(jobMaterial);
        // 需要先查询 改物资是否已经被加入物资库  基地相同的数据
        MaterialManage materialManage = materialManageService.getMaterialManageId(jobMaterial.getAssetType(), jobMaterial.getAssetCode()
                , jobMaterial.getNumber(), jobMaterial.getBaseCode());
        List<String> pahseList = new ArrayList<>();
        String materialManageId="";
        pahseList.add("materialInfoAdd");
        if(Objects.isNull(materialManage)){
            MaterialManageDTO materialManageDTO = new MaterialManageDTO();
            materialManageDTO.setAssetType(jobMaterial.getAssetType());
            materialManageDTO.setAssetCode(jobMaterial.getAssetCode());
            materialManageDTO.setNumber(jobMaterial.getNumber());
            materialManageDTO.setAssetName(jobMaterial.getAssetName());
            materialManageDTO.setCostCenterName(jobMaterial.getCostCenterName());
            materialManageDTO.setCostCenter(jobMaterial.getCostCenter());
            materialManageDTO.setSpecificationModel(jobMaterial.getSpecificationModel());
            materialManageDTO.setStockNum(jobMaterial.getStockNum());
            materialManageDTO.setInputStockNum(jobMaterial.getDemandNum());
            materialManageDTO.setNextVerificationDate(jobMaterial.getNextVerificationDate());
            materialManageDTO.setIsVerification(jobMaterial.getIsVerification());
            materialManageDTO.setBaseCode(jobMaterial.getBaseCode());
            materialManageDTO.setStatus(StatusEnum.DISABLE.getIndex());
            materialManageDTO.setToolStatus(jobMaterial.getToolStatus());
            materialManageDTO.setMaintenanceCycle(jobMaterial.getMaintenanceCycle());
            materialManageDTO.setProductCode(jobMaterial.getProductCode());
            materialManageId = materialManageService.create(materialManageDTO);
        }else{
            materialManageId = materialManage.getId();
            if(Objects.equals(materialManage.getStatus(), StatusEnum.DISABLE.getIndex())){
                pahseList.add("materialJoin");
            }
        }
        jobMaterial.setMaterialId(materialManageId);
        String rsp=jobMaterial.getId();
        jobMaterialMapper.updateMaterialId(rsp,jobMaterial.getMaterialId());
        // 插入关系表记录
        jobMaterialRecordService.addOrUpdate(jobId, jobMaterial.getNumber(),jobMaterial.getId());
        if(StringUtils.hasText(jobId)){
            jobNodeStatusService.setNodeStatus(jobId,pahseList);
            schemeToMaterialService.saveEntity(jobManage.getPlanSchemeId()
                    ,jobManage.getRepairRound(),jobManage.getJobBase()
                    ,jobMaterialDTO.getMaterialNumber(),jobMaterial.getStockNum()
                    ,jobMaterialDTO.getAssetType(),materialManageId,jobMaterialDTO.getAssetName()
                    ,jobMaterialDTO.getProductCode(),jobMaterialDTO.getToolStatus()
                    ,jobMaterialDTO.getMaintenanceCycle() );
        }
        return rsp;
    }

    /**
     *  编辑
     *
     * * @param jobMaterialDTO
     */
    @Override
    public Boolean edit(JobMaterialDTO jobMaterialDTO) throws Exception {
        JobMaterial jobMaterial =BeanCopyUtils.convertTo(jobMaterialDTO,JobMaterial::new);

        this.updateById(jobMaterial);

        String rsp=jobMaterial.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }

    @Override
    public Boolean removeBatchNew(JobMaterialParamDTO jobMaterialParamDTO) {
        List<String> materialIdList = jobMaterialParamDTO.getMaterialIdList();

        LambdaQueryWrapperX<JobMaterial> wrapperX = new LambdaQueryWrapperX<>(JobMaterial.class);
        wrapperX.in(JobMaterial::getMaterialId, materialIdList);
        wrapperX.notIn(JobMaterial::getId, jobMaterialParamDTO.getIds());
        wrapperX.select(JobMaterial::getMaterialId);
        List<JobMaterial> jobPostAuthorizeList = this.list(wrapperX);
        if (CollectionUtils.isEmpty(jobPostAuthorizeList)) {
            this.removeBatchByIds(jobMaterialParamDTO.getIds());
            materialManageService.removeBatchByIds(materialIdList);
            return Boolean.TRUE;
        }
        materialIdList.removeAll(jobPostAuthorizeList.stream().map(JobMaterial::getMaterialId).collect(Collectors.toList()));
        if (materialIdList.size() > 0) {
            materialManageService.removeBatchByIds(materialIdList);
        }
        this.removeBatchByIds(jobMaterialParamDTO.getIds());
        return Boolean.TRUE;
    }
    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)

    public Page<JobMaterialVO> pages( Page<JobMaterialDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<JobMaterial> condition = new LambdaQueryWrapperX<>( JobMaterial. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(JobMaterial::getCreateTime);

        JobMaterialDTO query = pageRequest.getQuery();
        if(null != query){
            String jobId = query.getJobId();
            if(StringUtils.hasText(jobId)){
                condition.eq(JobMaterial::getJobId,jobId);
            }
        }
        Page<JobMaterial> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), JobMaterial::new));

        PageResult<JobMaterial> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<JobMaterialVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<JobMaterialVO> vos = BeanCopyUtils.convertListTo(page.getContent(), JobMaterialVO::new);
        Map<String, Set<String>> materiaJobMap = new HashMap<>();
        // 遍历DTO列表，填充HashMap
        for (JobMaterialVO dto : vos) {
            String personId = dto.getNumber();
            String jobId = dto.getJobId();
            materiaJobMap.putIfAbsent(personId, new HashSet<>());
            materiaJobMap.get(personId).add(jobId);
        }
//        vos.forEach(dto ->{
//            dto.setJobNumber(materiaJobMap.get(dto.getNumber()).size());
//        });

        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    public long getOutDate(Date outDate) {
        if(!Objects.isNull(outDate)){
            Calendar startCalendar = Calendar.getInstance();
            Calendar endCalendar = Calendar.getInstance();

            startCalendar.setTime(DateUtil.beginOfDay(new Date()));
            endCalendar.setTime(DateUtil.beginOfDay(outDate));
            if(startCalendar.getTimeInMillis()>endCalendar.getTimeInMillis()){
                return 0;
            }else{
                return (endCalendar.getTimeInMillis() - startCalendar.getTimeInMillis()) / (24 * 60 * 60 * 1000);
            }
        }
        return 0;
    }
    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "作业相关的物资导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", JobMaterialDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            JobMaterialExcelListener excelReadListener = new JobMaterialExcelListener();
        EasyExcel.read(inputStream,JobMaterialDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<JobMaterialDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("作业相关的物资导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<JobMaterial> jobMateriales =BeanCopyUtils.convertListTo(dtoS,JobMaterial::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::JobMaterial-import::id", importId, jobMateriales, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<JobMaterial> jobMateriales = (List<JobMaterial>) orionJ2CacheService.get("pmsx::JobMaterial-import::id", importId);
        log.info("作业相关的物资导入的入库数据={}", JSONUtil.toJsonStr(jobMateriales));

        this.saveBatch(jobMateriales);
        orionJ2CacheService.delete("pmsx::JobMaterial-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::JobMaterial-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<JobMaterial> condition = new LambdaQueryWrapperX<>( JobMaterial. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(JobMaterial::getCreateTime);
        List<JobMaterial> jobMateriales =   this.list(condition);

        List<JobMaterialDTO> dtos = BeanCopyUtils.convertListTo(jobMateriales, JobMaterialDTO::new);

        String fileName = "作业相关的物资数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", JobMaterialDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<JobMaterialVO> vos)throws Exception {
        if(CollectionUtils.isEmpty(vos)){
            return;
        }
        List<String> materialIdList = vos.stream().map(JobMaterialVO::getMaterialId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<MaterialManageVO> materialManageVOS = materialManageService.listByIdList(materialIdList);
        Map<String,MaterialManageVO> idToEntity = new HashMap<>();
        if(!CollectionUtils.isEmpty(materialManageVOS)){
            for (MaterialManageVO materialManageVO : materialManageVOS) {
                idToEntity.put(materialManageVO.getId(),materialManageVO);
            }
        }
        List<DictValueVO> dictValueVOList = dictRedisHelper.getDictListByCode(DictConts.SUPPLIES_TYPE);
        Map<String,String> numberToDesc = dictValueVOList.stream().collect(Collectors.toMap(DictValueVO::getNumber,DictValueVO::getDescription));
        Map<String,String> keyToDesc =basePlaceService.allMapSimpleList();
        vos.forEach(vo->{
            vo.setToolStatusName(MaterialToolTypeEnum.getDescByKey(vo.getToolStatus()));
            // 14233
            vo.setBaseName(keyToDesc.getOrDefault(vo.getBaseCode(),""));
            vo.setAssetTypeName(numberToDesc.getOrDefault(vo.getAssetType(),""));
            vo.setStoragePlaceName(keyToDesc.getOrDefault(vo.getStoragePlaceCode(),""));
            vo.setToolStatusName(MaterialToolTypeEnum.getDescByKey(vo.getToolStatus()));
//            if(StringUtils.hasText(vo.getMaterialId())){
//                vo.setMaterialManageVO(idToEntity.getOrDefault(vo.getMaterialId(),null));
//            }
        });
    }

    @Override
    public List<JobMaterial> listByRepairRound(String repairRound) {
        LambdaQueryWrapperX<JobMaterial> condition = new LambdaQueryWrapperX<>( JobMaterial. class);
        condition.eq(JobMaterial::getRepairRound,repairRound);
        condition.select(JobMaterial::getId,JobMaterial::getRepairRound,JobMaterial::getAssetType
                ,JobMaterial::getDemandNum,JobMaterial::getNumber,JobMaterial::getAssetCode
                ,JobMaterial::getBaseCode,JobMaterial::getStoragePlaceCode);
        return this.list(condition);
    }

    @Override
    public void updateMaterialId(String jobId, String leadgerId, String materialId, String oldId,String number) {
        LambdaUpdateWrapper<JobMaterial> wrapper = new LambdaUpdateWrapper<>(JobMaterial.class);
//        wrapper.eq(JobMaterial::getJobId, jobId);
        wrapper.eq(JobMaterial::getMaterialId, oldId);

        wrapper.set(JobMaterial::getMaterialId, materialId);
        wrapper.set(JobMaterial::getMaterialLedgerId, leadgerId);
        this.update(wrapper);
        // 同时还需要更新关系表
        jobMaterialRecordService.addRelation(jobId,materialId,number);
    }

    @Override
    public void updateMaterialLedgerId(String id, String id1) {
        LambdaUpdateWrapper<JobMaterial> wrapper = new LambdaUpdateWrapper<>(JobMaterial.class);
        wrapper.eq(JobMaterial::getMaterialId, id);
        wrapper.set(JobMaterial::getMaterialLedgerId, id1);
        this.update(wrapper);
    }

    @Override
    public  void existsData  (String jobId,String number) throws Exception {

        LambdaQueryWrapperX<JobMaterial> wrapperX = new LambdaQueryWrapperX<>(JobMaterial.class);
        wrapperX.eq(JobMaterial::getNumber,number);
        if(StringUtils.hasText(number)){
            wrapperX.eq(JobMaterial::getNumber,number);
        }
        if(StringUtils.hasText(jobId)){
            wrapperX.eq(JobMaterial::getJobId,jobId);
        }
        wrapperX.select(JobMaterial::getJobId);
        if(this.count(wrapperX)>0) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "物资当前物资数据已重复，请重新添加");
        }
    }

    @Override
    public void copyByJoIdToTargetId(String sourceId, List<String> targetIdList) {
        // 查询出 来源做的 相关的 物资列表
        targetIdList.add(sourceId);
        LambdaQueryWrapperX<JobMaterial> condition = new LambdaQueryWrapperX<>( JobMaterial. class);
        condition.in(JobMaterial::getJobId,targetIdList);
        List<JobMaterial> jobMaterials = this.list(condition);
        if(CollectionUtils.isEmpty(jobMaterials)){
            return;
        }
        // 获取所有的物资数据

        Map<String,List<JobMaterial>> jobIdToList = jobMaterials.stream().collect(Collectors.groupingBy(JobMaterial::getJobId));
        List<JobMaterial> jobMaterialList = jobIdToList.getOrDefault(sourceId,new ArrayList<>());
        if(CollectionUtils.isEmpty(jobMaterialList)){
            return;
        }
        //移除掉 来源数据 方便进行循环操作
        jobIdToList.remove(sourceId);
        targetIdList.remove(sourceId);


        List<JobMaterialRecord> jobMaterialRecordList = new ArrayList<>();
//        List<JobMaterial> addRecordList = new ArrayList<>();
        Map<String,JobMaterial> numberToEntity = new HashMap<>();
        for (String jobId : targetIdList) {
            if(Objects.equals(jobId,sourceId)){
                continue;
            }
            // 目标数据
            List<JobMaterial> targetList = jobIdToList.get(jobId);
            if(CollectionUtils.isEmpty(targetList)){
                jobMaterialList.forEach(item->{
                    if(!numberToEntity.containsKey(item.getNumber())){
                        JobMaterialRecord jobMaterialRecord =new JobMaterialRecord();
                        String  uuId=classRedisHelper.getUUID(item.getClassName());
                        item.setJobId(jobId);
                        item.setId(uuId);
                        item.setCreateTime(null);
                        item.setCreatorId(null);
                        item.setModifyId(null);
                        item.setModifyTime(null);
                        item.setOwnerId(null);
                        jobMaterialRecord.setMateriaManageId(item.getMaterialId());
                        jobMaterialRecord.setJobId(jobId);
                        jobMaterialRecord.setMateriaCode(item.getNumber());
                        jobMaterialRecordList.add(jobMaterialRecord);
                        numberToEntity.put(item.getNumber(),item);
                    }

                });
            }else{
                // 对比
                List<String> targetNumberList =  targetList.stream().map(JobMaterial::getNumber).distinct().collect(Collectors.toList());
                jobMaterialList.forEach(item->{
                    if(!targetNumberList.contains(item.getNumber())){
                        if(!numberToEntity.containsKey(item.getNumber())){
                            JobMaterialRecord jobMaterialRecord =new JobMaterialRecord();
                            String  uuId=classRedisHelper.getUUID(item.getClassName());
                            item.setJobId(jobId);
                            item.setId(uuId);
                            item.setCreateTime(null);
                            item.setCreatorId(null);
                            item.setModifyId(null);
                            item.setModifyTime(null);
                            item.setOwnerId(null);
                            jobMaterialRecord.setMateriaManageId(item.getMaterialId());
                            jobMaterialRecord.setJobId(jobId);
                            jobMaterialRecord.setMateriaCode(item.getNumber());
                            jobMaterialRecordList.add(jobMaterialRecord);
                            numberToEntity.put(item.getNumber(),item);
                        }
                    }
                });
            }
        }
        if(MapUtil.isNotEmpty(numberToEntity)){
            this.saveBatch( numberToEntity.entrySet().stream().map(Map.Entry::getValue).distinct().collect(Collectors.toList()));
        }
        if(!CollectionUtils.isEmpty(jobMaterialRecordList)){
            jobMaterialRecordService.saveBatch(jobMaterialRecordList);
        }
    }

    @Override
    public List<String> getNumberListByJobId(String jobId) {
        LambdaQueryWrapperX<JobMaterial> condition = new LambdaQueryWrapperX<>( JobMaterial. class);
        condition.eq(JobMaterial::getJobId,jobId);
        condition.select(JobMaterial::getNumber);
        List<JobMaterial> jobMaterialList=  this.list(condition);
        if(CollectionUtils.isEmpty(jobMaterialList)){
            return  new ArrayList<>();
        }
        return jobMaterialList.stream().map(JobMaterial::getNumber).distinct().collect(Collectors.toList());
    }

    @Override
    public Boolean editDate(StartEndDTO startEndDTO) {
        String dataId = startEndDTO.getId();
        LambdaUpdateWrapper<JobMaterial> wrapperX = new LambdaUpdateWrapper<>(JobMaterial.class);
        wrapperX.eq(JobMaterial::getId, dataId);
        List<Date>  inAndOutDateList =  startEndDTO.getStartAndEndDateList();
        wrapperX.set(JobMaterial::getPlanBeginDate, inAndOutDateList.get(0));
        wrapperX.set(JobMaterial::getPlanEndDate, inAndOutDateList.get(1));
        wrapperX.set(JobMaterial::getModifyTime, new Date());
        wrapperX.set(JobMaterial::getModifyId, CurrentUserHelper.getCurrentUserId());
        return this.update(wrapperX);
    }

    public static class JobMaterialExcelListener extends AnalysisEventListener<JobMaterialDTO> {

        private final List<JobMaterialDTO> data = new ArrayList<>();

        @Override
        public void invoke(JobMaterialDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<JobMaterialDTO> getData() {
            return data;
        }
    }


}
