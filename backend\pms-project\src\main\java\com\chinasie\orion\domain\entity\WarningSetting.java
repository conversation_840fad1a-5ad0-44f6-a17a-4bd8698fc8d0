package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/03/14:00
 * @description:
 */
@Data
@TableName(value = "pms_warning_setting")
@ApiModel(value = "WarningSetting对象", description = "预警设置")
public class WarningSetting extends ObjectEntity {
    /**
     * 频率
     */
    @ApiModelProperty(value = "频率")
    @TableField(value = "frequency")
    private String frequency;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 提醒时间
     */
    @ApiModelProperty(value = "提醒时间")
    @TableField(value = "time")
    private String time;

    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    @TableField(value = "take_effect")
    private Integer takeEffect;

    /**
     * 提醒种类
     */
    @ApiModelProperty(value = "提醒种类(day:提醒天数;percentage:提醒工期百分比)")
    @TableField(value = "warning_category")
    private String warningCategory;

    /**
     * 提醒天数
     */
    @ApiModelProperty(value = "提醒天数")
    @TableField(value = "day_num")
    private Integer dayNum;

    @ApiModelProperty(value = "预警方式")
    @TableField(value = "warning_way")
    private String warningWay;

    @ApiModelProperty(value = "预警类型")
    @TableField(value = "warning_type")
    private String warningType;

    /**
     * 字典表的值id
     */
    @ApiModelProperty(value = "字典表的值id")
    @TableField(value = "dict_value_id")
    private String dictValueId;
}
