<script setup lang="ts">
import { Progress } from 'ant-design-vue';
import { Icon } from 'lyra-component-vue3';
import {
  computed,
  inject, onMounted, ref, Ref,
} from 'vue';
import Api from '/@/api';

const projectId:string = inject('projectId');
const hoursInfo:Ref<Record<string, any>> = ref({});

onMounted(() => {
  getHoursInfo();
});

// 获取工时信息
async function getHoursInfo() {
  const result = await new Api('/pms/projectCollectionStatistics/getProjectManhour').fetch({
    projectId,
  }, '', 'GET');
  hoursInfo.value = result || {};
}
function getFillSchedule() {
  return `${hoursInfo.value.fillSchedule}%`;
}
</script>

<template>
  <div
    ref="containerRef"
    class="container"
  >
    <div
      class="circle-flex-item"
      style="grid-column: 1/4"
    >
      <div class="circle default">
        <Icon icon="orion-icon-reloadtime" />
      </div>
      <div class="fg1">
        <Progress
          class="progress-grow"
          :percent="hoursInfo.fillSchedule||0"
          :format="getFillSchedule"
        />
        <span>项目工时进度</span>
      </div>
    </div>
    <div class="circle-flex-item">
      <div class="circle info">
        <Icon icon="orion-icon-Field-time" />
      </div>
      <div>
        <div class="value">
          {{ hoursInfo.estimateWorkHour||0 }}
        </div>
        <span>计划工时</span>
      </div>
    </div>
    <div class="circle-flex-item">
      <div class="circle warning">
        <Icon icon="orion-icon-Field-time" />
      </div>
      <div>
        <div class="value">
          {{ hoursInfo.fillWorkHour||0 }}
        </div>
        <span>填报工时</span>
      </div>
    </div>
    <div class="circle-flex-item">
      <div class="circle success">
        <Icon icon="orion-icon-Field-time" />
      </div>
      <div>
        <div class="value">
          {{ hoursInfo.surplusWorkHour||0 }}
        </div>
        <span>剩余工时</span>
      </div>
    </div>
    <div class="circle-flex-item">
      <div class="circle primary">
        <Icon icon="orion-icon-Field-time" />
      </div>
      <div>
        <div class="value estimate">
          {{ hoursInfo.estimateDeviation||0 }}
        </div>
        <span>预估偏差</span>
      </div>
    </div>
    <div class="circle-flex-item">
      <div class="circle primary">
        <Icon icon="orion-icon-Field-time" />
      </div>
      <div>
        <div class="value estimate">
          {{ `${hoursInfo.deviationRatio||0}%` }}
        </div>
        <span>偏差率</span>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.container {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  gap:20px 10px;

  > div {
    display: flex;
    align-items: center;
  }
}
:deep(.circle-flex-item > :last-child span){
  overflow: visible;
}
.estimate{
  color: red;
}
</style>
