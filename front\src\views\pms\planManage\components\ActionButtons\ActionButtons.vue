<template>
  <div class="flex flex flex-ac">
    <div
      v-for="(item, index) in actions"
      :key="index"
      class="flex flex-ac"
    >
      <template v-if="item.key==='delete'">
        <Popconfirm
          title="确认要删除吗？"
          @confirm="actionClick(item.key, record)"
        >
          <span
            class="action-btn"
            @click.stop
          >{{ item.label }}</span>
        </Popconfirm>
      </template>
      <span
        v-else
        class="action-btn"
        @click.stop="actionClick(item.key, record)"
      >{{ item.label }}</span>
      <Divider
        v-if="index!==actions.length-1"
        type="vertical"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { Divider, Popconfirm } from 'ant-design-vue';
import { reactive } from 'vue';

const props = defineProps<{
  record: any,
  actionClick: (key: string, record: any)=>void,
  actions: {label: string, key: string}[]
}>();

const state = reactive({
  actions: [
    {
      label: '创建计划',
      key: 'create',
    },
    {
      label: '编辑',
      key: 'edit',
    },
    {
      label: '删除',
      key: 'delete',
    },
  ],
});

</script>

<style scoped>

</style>
