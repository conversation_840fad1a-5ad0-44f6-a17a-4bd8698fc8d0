package com.chinasie.orion.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.management.domain.vo.CustomerInfoVO;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


@Data
public class MarketContractDetailMoneyVo  implements Serializable {



    /**
     * 合同总金额
     */
    @ApiModelProperty(value = "合同总金额")
    private BigDecimal contractTotalAmt;
    /**
     * 框架合同金额
     */
    @ApiModelProperty(value = "里程碑金额总计")
    private BigDecimal frameContractAmt;


    @ApiModelProperty(value = "里程碑验收金额总计")
    private BigDecimal  acceptanceMoney;



}
