<script setup lang="ts">
import {
  BasicButton, BasicForm, FormSchema, getDict, OrionTable, useForm,
} from 'lyra-component-vue3';
import { FormItemRest, Input } from 'ant-design-vue';
import {
  h, onMounted, Ref, ref,
} from 'vue';
import dayjs from 'dayjs';
import Api from '/@/api';

const props = defineProps<{
  id: string,
  testTaskId: string,
  record:any,
  isView: boolean
}>();

const schemas: FormSchema[] = [
  {
    field: 'name',
    component: 'Input',
    label: '指标名称',
    required: true,
    componentProps: {
      placeholder: '请输入指标名称',
      disabled: props.isView,
    },
  },
  {
    field: 'weight',
    component: 'InputNumber',
    label: '权重',
    required: true,
    componentProps: {
      style: 'width:100%',
      addonAfter: '%',
      precision: 0,
      min: 0,
      max: 100,
      formatter(value) {
        return value.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      },
      maxLength: 15,
    },
  },

  {
    field: 'scoreStandard',
    component: 'InputTextArea',
    label: '评分标准',
    colProps: {
      span: 24,
    },
    componentProps: {
      rows: 4,
      disabled: props.isView,
      placeholder: '请输入评分标准',
    },
  },
];

const [register, { validate, setFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

const tableRef: Ref = ref();
const dataSource: Ref<Record<string, any>[]> = ref([]);

onMounted(() => {
  props.id && iniForm();
});

const loading: Ref<boolean> = ref(false);

async function iniForm() {
  loading.value = true;
  try {
    setFieldsValue(props.record);
  } finally {
    loading.value = false;
  }
}

defineExpose({
  async onSubmit() {
    const formValues = await validate();
    const params = {
      ...formValues,
      id: props.id,
    };
    await new Api('/pms/indicatorLibrary').fetch(params, '', props.id ? 'PUT' : 'POST');
  },
});
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">
:deep(.ant-basic-table-wrap.default-spacing) {
  padding: 0;
}
.test-management ::v-deep .default-spacing {
  padding-left: 0;
  padding-right: 0;
}
</style>