<script setup lang="ts">
import {
  OrionTable,
} from 'lyra-component-vue3';
import {
  h, inject, ref, Ref,
} from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';

const powerData: Ref = inject('powerData');
const detailsData: Record<string, any> = inject('detailData');
const tableRef: Ref = ref();
const tableOptions = {
  showTableSetting: false,
  showSmallSearch: false,
  showToolButton: false,
  columns: [
    {
      title: '专业中心',
      dataIndex: 'expertiseCenterTitle',
    },
    {
      title: '专业所',
      dataIndex: 'expertiseStationTitle',
    },
    {
      title: '变更原因',
      dataIndex: 'changeReason',
    },
    {
      title: '变更人',
      dataIndex: 'changePersonName',
    },
    {
      title: '变更日期',
      dataIndex: 'changeTime',
      width: 180,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '变更内容',
      dataIndex: 'changeContent',
    },
    {
      title: '变更前',
      dataIndex: 'beforeChange',
    },
    {
      title: '变更后',
      dataIndex: 'afterChangfe',
    },
  ],
  api: null,
};
</script>

<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
    :dataSource="detailsData?.personRoleMaintenanceLogList"
  />
</template>

<style scoped lang="less">

</style>
