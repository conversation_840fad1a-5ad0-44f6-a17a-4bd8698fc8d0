package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.IedBaseLineInfoDTO;
import com.chinasie.orion.domain.entity.IedBaseLineInfo;
import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.vo.DeliverGoalsVO;
import com.chinasie.orion.domain.vo.IedBaseLineInfoVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

/**
 * <p>
 * IedBaseLineInfo 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29 13:50:13
 */
public interface IedBaseLineInfoService extends OrionBaseService<IedBaseLineInfo> {
    /**
     *  获取基线信息详情
     *
     * * @param id
     */
    IedBaseLineInfoVO detail(String id)  throws Exception;

    /**
     *  新增基线信息
     *
     * * @param iedBaseLineInfoDTO
     */
    String create(IedBaseLineInfoDTO iedBaseLineInfoDTO)  throws Exception;


    /**
     *  基线信息分页
     *
     * * @param pageRequest
     */
    Page<IedBaseLineInfoVO> pages(Page<IedBaseLineInfoDTO> pageRequest) throws Exception;

    /**
     * 获取基线内容详情
     * @param baseId
     * @return
     * @throws Exception
     */
    List<DeliverGoalsVO> getDeliverGoalsByBaseId(String baseId) throws Exception;

    /**
     * 获取基线信息列表
     * @param projectId
     * @return
     * @throws Exception
     */
    List<IedBaseLineInfoVO> getListProjectId(String projectId) throws Exception;

}
