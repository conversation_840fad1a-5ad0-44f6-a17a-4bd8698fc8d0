<script setup lang="ts">
import { BasicSteps } from 'lyra-component-vue3';
import {
  computed, nextTick, onMounted, Ref, ref, unref,
} from 'vue';
import { useRoute } from 'vue-router';
import Api from '/@/api';
import { Space as ASpace } from 'ant-design-vue';
import dayjs from 'dayjs';
import { get as loadGet } from 'lodash-es';
import { parseBooleanToRender } from '../../utils';

const route = useRoute();
const projectId: Ref<string> = ref(route.params.id as string);
const processData = ref({});
const currentStep = ref(0);
const defSteps = [
  {
    title: '一级分发',
    subTitle: '审批状态：status',
    disabled: true,
    key: 'firstDistribution', // 2024-01-11T17:01:00.000+08:00
  },
  {
    title: '二级分发',
    subTitle: '审批状态：status',
    disabled: true,
    key: 'secondaryDistribution', // 2024-01-12T17:01:00.000+08:00
  },
  {
    title: '接受确认',
    subTitle: '审批状态：status',
    disabled: true,
    key: 'acceptConfirmation', // 2024-01-13T17:01:00.000+08:00
  },
  {
    title: '采购启动',
    subTitle: '审批状态：status',
    disabled: true,
    key: 'initiationTime', // 2024-01-14T17:01:00.000+08:00
  },
  {
    title: '采购启动审批',
    subTitle: '审批状态：status',
    disabled: true,
    key: 'purchStartApproval', // 2024-01-15T17:01:00.000+08:00
  },
  {
    title: '询价签发',
    subTitle: '审批状态：status',
    disabled: true,
    key: 'inqIssuance', // 2024-01-16T17:01:00.000+08:00
  },
  {
    title: '报价截止',
    subTitle: '审批状态：status',
    disabled: true,
    key: 'quoteEnd', // 2024-01-17T17:01:00.000+08:00
  },
  {
    title: '开启报价',
    subTitle: '审批状态：status',
    disabled: true,
    key: 'openQuote', // 2024-01-18T17:01:00.000+08:00
  },
  {
    title: '采购评审',
    subTitle: '审批状态：status',
    disabled: true,
    key: 'reviewTime', // 2024-01-19T17:01:00.000+08:00
  },
  {
    title: 'UPM合同推荐',
    subTitle: '申请状态：status',
    disabled: true,
    key: 'upmApprovalInProgress', // 2024-01-21T17:01:00.000+08:00
  },
  {
    title: 'UPM审批完成',
    subTitle: '申请状态：status',
    disabled: true,
    key: 'upmApprovalComplete', // 2024-01-22T17:01:00.000+08:00
  },
  {
    title: '发送SAP ',
    subTitle: '审批状态：status',
    disabled: true,
    key: 'sendSap', // 2024-01-23T17:01:00.000+08:00
  },
];
const steps = computed(() => defSteps.map((stepItem, stepIdx) => {
  const dateVal = loadGet(processData.value, stepItem.key);
  const status = currentStep.value > stepIdx
    ? '已完成'
    : currentStep.value === stepIdx ? '进行中' : '未开始';
  return {
    ...stepItem,
    subTitle: !dateVal || stepItem.key === 'reviewTime'
      ? status
      : dayjs(dateVal).format('YYYY-MM-DD'),
  };
}));

const analyzeStepByDate = (status) => {
  const stepIdx = defSteps.findIndex((item) => item.title.trim() === (status || '').trim());
  currentStep.value = stepIdx + 1;
};

const getProcurementProcessById = async () => {
  try {
    const result = await new Api('/pms/ncfPurchProjectImplementation').fetch('', unref(projectId), 'GET');
    processData.value = result;
    analyzeStepByDate(result.executionStatus);
    // analyzeStepByDate('采购启动审批');
  } catch (e) {

  }
};
onMounted(async () => {
  await nextTick();
  await getProcurementProcessById();
});
const onChange = () => {

};
</script>

<template>
  <div id="projectOngoingItem">
    <a-space :size="30">
      <span>是否发布启动公示</span>
      <span>{{ parseBooleanToRender(processData.isPublicLaunch) }}</span>
    </a-space>
    <div id="process-step-wrapper">
      <BasicSteps
        v-model:current="currentStep"
        layout="horizontal"
        :steps="steps"
        @change="onChange"
      />
    </div>
  </div>
</template>

<style scoped lang="less">
#projectOngoingItem{
  padding: 20px 0;
  overflow-x:auto;
    :deep(.ant-steps-item-active){
      .flex-te{
        background: #ffffff !important;
        color: rgb(241, 182, 63) !important;
        border: 1px solid rgb(241, 182, 63) !important;
      }
      .step-dot{
        border-color:rgb(241, 182, 63) !important;
      }
    }
    :deep(.basic-scrollbar__bar){
      &.is-horizontal{
        height: 15px;
      }
    }
  }
</style>
