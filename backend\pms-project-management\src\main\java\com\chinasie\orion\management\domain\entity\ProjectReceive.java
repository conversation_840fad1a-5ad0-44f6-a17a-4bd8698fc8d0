package com.chinasie.orion.management.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * ProjectReceive Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-31 14:15:16
 */
@TableName(value = "pmsx_project_receive")
@ApiModel(value = "ProjectReceiveEntity对象", description = "收货信息")
@Data

public class ProjectReceive extends  ObjectEntity  implements Serializable{

    /**
     * 收货人
     */
    @ApiModelProperty(value = "收货人")
    @TableField(value = "receive_person")
    private String receivePerson;

    /**
     * 收货人电话
     */
    @ApiModelProperty(value = "收货人电话")
    @TableField(value = "receive_tel")
    private String receiveTel;

    /**
     * 收货人地址
     */
    @ApiModelProperty(value = "收货人地址")
    @TableField(value = "receive_address")
    private String receiveAddress;

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    @TableField(value = "order_number")
    private String orderNumber;

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @TableField(value = "supplier_number")
    private String supplierNumber;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @TableField(value = "supplier_name")
    private String supplierName;

    /**
     * 收货负责人
     */
    @ApiModelProperty(value = "收货负责人")
    @TableField(value = "receive_director")
    private String receiveDirector;

    /**
     * 收货审核人
     */
    @ApiModelProperty(value = "收货审核人")
    @TableField(value = "receive_reviewer")
    private String receiveReviewer;

    /**
     * 支付负责人
     */
    @ApiModelProperty(value = "支付负责人")
    @TableField(value = "pay_director")
    private String payDirector;

    /**
     * 对账人
     */
    @ApiModelProperty(value = "对账人")
    @TableField(value = "reconciliation_person")
    private String reconciliationPerson;

}
