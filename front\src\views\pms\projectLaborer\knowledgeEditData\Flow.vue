<template>
  <BasicModal
    v-bind="$attrs"
    title="选择流程"
    @register="registerModal"
  >
    <div>
      <div class="select-items">
        <div class="style-line">
          <span>选择流程：</span>
          <div>
            <a-select
              v-model:value="initValue"
              style="width: 260px"
              @change="onChangeFlow"
            >
              <select-option
                v-for="item in flowList"
                :key="item.procDefId"
                :value="item.procDefId"
              >
                {{ item.name }}
              </select-option>
            </a-select>
          </div>
        </div>
        <div
          class="style-line"
          style="margin-left: 20px"
        >
          <span>启动条件：</span>
          <div>
            <a-select
              v-model:value="initStatus"
              style="width: 200px"
            >
              <select-option
                v-for="item in statusList"
                :key="item.id"
                label-in-value
                :value="item.id"
              >
                {{ item.remark }}
              </select-option>
            </a-select>
          </div>
        </div>
      </div>
      <div>
        <orion-table
          v-if="options.auto.params.query.procDefId"
          ref="btnsTableRef"
          :options="options"
          style="max-height: 30vh"
          :pagination="false"
          row-key="taskName"
        >
          <template #action="{ record, index }">
            <a
              v-if="!record.applicant"
              @click="onOpenModal(record, index)"
            >指定人员</a>
          </template>
        </orion-table>
      </div>
      <div class="flow-title">
        <h3>流程图</h3>
      </div>
      <div class="bpmn-containers">
        <div
          id="flow-canvas"
          ref="canvas"
          class="canvas"
        />
      </div>
    </div>
    <TransferModal
      ref="transferModalRef"
      :data-source="dataSource"
      :default-expand-all="true"
      row-key="id"
      :target-keys="selectIds"
      show-search
      z-index="1003"
      module-title="人员"
      @submit="onSubmitSelectReviewer"
    />
    <template #footer>
      <a-button @click="closeModal">
        取消
      </a-button>
      <a-button
        type="primary"
        @click="validateForm()"
      >
        确定
      </a-button>
    </template>

    <!--  会签弹窗  -->
    <BasicModal
      title="会签处理"
      z-index="1002"
      @register="registerJointlySignModal"
      @ok="JointlySignOk"
      @cancel="JointlySignCancel"
    >
      <BasicTable
        ref="JointlySignTableRef"
        :columns="JointlySignColumns"
        :data-source="JointlySignData"
        :pagination="false"
      >
        <template #assignees="{ record, index }">
          <div
            class="pointer bd"
            @click="JointlySignModal(index, record)"
          >
            {{ record.countersignUserName ? record.countersignUserName : '请选择' }}
          </div>
        </template>
      </BasicTable>
    </BasicModal>
    <!--  会签弹窗  -->
  </BasicModal>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, ref, nextTick, inject,
} from 'vue';
import { Select, message } from 'ant-design-vue';
import {
  BasicTable, Layout, useActionsRecord, OrionTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer, TransferModal,
} from 'lyra-component-vue3';
import { useRoute } from 'vue-router';
// import TransferModal from '/@/components/TransferModal';
import Api from '/@/api/index';
import { useUserStore } from '/@/store/modules/user';
import MoveCanvasModule from 'diagram-js/lib/navigation/movecanvas';
import { workflowApi } from '/@/views/pms/projectLaborer/flowcenter/util/apiConfig';
import Viewer from 'bpmn-js/lib/Viewer';
import { unionBy } from 'lodash-es';
import CustomRenderer from '/@/views/pms/projectLaborer/flowcenter/detail/extra/index';
  interface IdType {
    id: string;
  }
export default defineComponent({
  components: {
    BasicModal,
    ASelect: Select,
    SelectOption: Select.Option,
    OrionTable,
    TransferModal,
    BasicTable,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
    dataType: {
      type: String,
      default: '',
    },
  },
  setup(props, { emit }) {
    const userStore: any = useUserStore();
    const docData: any = inject('docData', {});
    const mid: any = inject('mId');
    const route = useRoute();
    const btnsTableRef = ref<Nullable<any>>(null); // table的ref
    const transferModalRef: any = ref(null);
    const JointlySignTableRef: any = ref(null);
    // 弹窗内部的注册函数,可以在内部自己关闭
    const [registerModal, { closeModal }] = useModalInner((flowName) => {
      state.flowName = '';
      new Api('/res') // 模板管理 KnowledgeModel ，知识管理 KnowledgeInfo
        .fetch({ dataType: props.dataType }, 'data-type/list', 'POST')
        .then((resp) => {
          state.projectId = '';
          state.name = '';
          new Api('/res')
            .fetch(
              {
                dataTypeId: resp[0].id,
                groupId: 'bzdj41006ab0a28d4f6b98b1273aac5ff372',
                templateType: 'FLOW-TEMPLATE',
              },
              'data-type-group/template/list',
              'POST',
            )
            .then((res) => {
              renderFlowAfter(res[0].procDefId);
              state.flowList = res;
              state.options.auto.params.query.procDefId = res[0].procDefId;
              state.initValue = res[0].procDefId;
              state.initName = res[0].name;
              if (btnsTableRef.value) {
                btnsTableRef.value.reload();
              }
              _getStatusList(res[0].id);
            });
        });
    });

    function _getStatusList() {
      const obj = state.flowList.find((s) => s.procDefId === state.initValue);
      let flowKey = obj === undefined ? '' : obj.flowKey;
      new Api('/res')
        .fetch(
          {},
          `status/flow/group/condition?dataId=${props.id}&templateId=${flowKey}&groupId=bzdj41006ab0a28d4f6b98b1273aac5ff372`,
          'GET',
        )
        .then((res) => {
          state.initStatus = res[0]?.id;
          state.statusList = res;
        });
    }

    const state: any = reactive({
      /* 会签内容 */
      JointlySignInput: '',
      JointlySignColumns: [
        {
          title: '会签内容',
          dataIndex: 'content',
        },
        {
          title: '流程负责人',
          dataIndex: 'assignees',
          slots: { customRender: 'assignees' },
        },
      ],
      JointlySignData: [],
      // 存储所选数据索引
      JointlySignTableIndex: null,
      /* 会签内容 */
      dataSource: [],
      projectId: '',
      flowName: '',
      name: '',
      initValue: '',
      initName: '',
      flowInfoId: '',
      initStatus: '',
      flowList: [],
      statusList: [],
      currentItem: null,
      selectIds: [],
      user: [],
      organizations: [],
      roles: [],
      currentIndex: 0,
      isSingle: false,
      options: {
        deleteToolButton: 'add|delete|enable|disable',
        showSmallSearch: false,
        auto: {
          url: `${workflowApi}/act-prearranged/all-tasks`,
          params: {
            query: {
              userId: userStore.getUserInfo.id,
              procDefId: '',
              procInstId: '',
            },
          },
        },
        columns: [
          {
            title: '节点名称',
            dataIndex: 'taskName',
          },
          {
            title: '流程负责人',
            dataIndex: 'assignees',
          },
          {
            title: '操作',
            dataIndex: 'action',
            slots: { customRender: 'action' },
          },
        ],
      },
    });

    function renderFlowAfter(procDefId) {
      nextTick(() => {
        new Api(workflowApi)
          .fetch(
            {
              userId: userStore.getUserInfo.id,
              procDefId,
            },
            'act-flow/bpmn-by-proc-def-id',
            'GET',
          )
          .then((res) => {
            renderFlow(res);
          });
      });
    }

    function _changeStrToArr(str) {
      let arr: IdType[] = [];
      if (str) {
        let ids = str.split(',');
        ids.forEach((item) => {
          arr.push({
            id: item,
          });
        });
      }
      return arr;
    }

    /*
       * 验证增加和编辑时候的表单
       * */
    async function validateForm() {
      let tableData = btnsTableRef.value.getDataSource();
      if (tableData.length >= 2) {
        if (tableData[0].applicant) {
          if (!tableData[1].selectIds) {
            return message.warn('do节点之后第一个节点必须选择');
          }
        } else if (!tableData[0].selectIds) {
          return message.warn('第一个节点必须选择');
        }
      }
      if (!_checkReviewer()) {
        message.warn('流程节点负责人除编写节点第一个节点必须指定一个人员才能进行');
        return;
      }
      // if (!state.flowName) {
      //   message.warn('请填写流程名称');
      //   return;
      // }
      let statusName = '';
      state.statusList.forEach((item) => {
        if (item.id === state.initStatus) {
          statusName = item.remark;
        }
      });

      const flowObj = state.flowList.find((s) => s.procDefId === state.initValue);
      const params: any = {
        prearranges: [],
        flowInfoId: flowObj.id,
        // businessKey: '',
        bizId: state.projectId,
        procDefName: state.initName,
        procInstName: '知识模板流程',
        procDefId: state.initValue,
        userId: userStore.getUserInfo.id,
        deliveries: [
          {
            // deliveryId: mid.value,
            deliveryId: props.id,
            deliveryStatus: state.initStatus,
            deliveryStatusName: statusName,
          },
        ],
      };
      tableData.forEach((item) => {
        params.prearranges.push({
          assignees: item.selectIds ? item.selectIds.split(',') : [],
          taskDefinitionKey: item.taskDefinitionKey,
        });
      });
      closeModal();
      new Api(workflowApi).fetch(params, 'act-prearranged', 'POST').then(() => {
        message.success('操作成功');
        emit('update');
      });
    }

    function onChangeFlow(val) {
      let id = '';
      renderFlowAfter(val);
      state.options.auto.params.query.procDefId = val;
      btnsTableRef.value.reload();
      state.flowList.forEach((item) => {
        if (item.procDefId === val) {
          state.initName = item.name;
        }
        if (item.procDefId === val) {
          id = item.id;
        }
      });
      state.flowInfoId = id;
      _getStatusList(id);
    }

    // 校验是否配置了审批人
    function _checkReviewer() {
      const data = btnsTableRef.value.getDataSource();
      const selectIds = data.filter((s) => s.taskName !== '编写' && s.selectIds);
      return !!selectIds.length;
    }

    // 根据userId查用户
    function _getListByUserId(ids) {
      return new Api('/pmi').fetch(ids.split(','), 'user/ids', 'POST');
    }

    // 根据roleId查用户
    function _getListByRoleId(ids) {
      return new Api('/pmi').fetch(ids.split(','), 'user/role/ids', 'POST');
    }

    // 根据orgId查用户
    function _getListByOrgId(ids) {
      return new Api('/pmi').fetch(ids.split(','), 'user/org/ids', 'POST');
    }

    // 根据projectId查用户
    function _getListByProId(ids) {
      return new Api('/pms').fetch(
        {
          projectId: state.projectId,
          roleIds: ids.split(','),
        },
        'project/user/listAll',
        'POST',
      );
    }
    // 根据资质过滤用户
    function _getListByQua(value, paramsUser) {
      return new Api('/pmi').fetch(
        paramsUser,
        `gradeTemporarySyn/get-grade-list?gradeValue=${value}`,
        'POST',
      );
    }

    // 密级过滤
    function _filterBySecret(userList) {
      const userIds: string[] = [];
      userList.forEach((item) => {
        userIds.push(item.id);
      });
      // classificationId: docData.value.secretLevel
      return new Api('/pmi').fetch(
        {
          classificationId: '',
          userIds,
        },
        'data-classification/user-filter',
        'POST',
      );
      // state.dataSource = resp;
    }

    // 项目资源池过滤
    function _filterBySource(userList) {
      const userIds: string[] = [];
      userList.forEach((item) => {
        userIds.push(item.id);
      });
      new Api('/pms')
        .fetch(
          {
            projectId: state.projectId,
            userIds,
          },
          'project/resources/list',
          'POST',
        )
        .then((res) => {
          state.dataSource = res;
        });
    }

    // 获取用户
    async function _getUsers() {
      state.dataSource = [];
      let usersList: any[] = [];
      if (
        !state.currentItem.user
          && !state.currentItem.role
          && !state.currentItem.organization
          && !state.currentItem.projectRole
          && !state.currentItem.credentials
      ) {
        message.warn('没有人员可选择');
        return;
      }
      if (state.currentItem.user) {
        const ulist = await _getListByUserId(state.currentItem.user);
        usersList = usersList.concat(ulist);
      }
      if (state.currentItem.role) {
        const rlist = await _getListByRoleId(state.currentItem.role);
        usersList = usersList.concat(rlist);
      }
      if (state.currentItem.organization) {
        const olist = await _getListByOrgId(state.currentItem.organization);
        usersList = usersList.concat(olist);
      }
      if (state.currentItem.projectRole) {
        const plist = await _getListByProId(state.currentItem.projectRole);
        usersList = usersList.concat(plist);
      }
      state.dataSource = usersList;
    }

    /* 会签内容 */
    // 会签弹窗
    const [registerJointlySignModal, { openModal: openModalJointlySign }] = useModal();
    const JointlySignOk = (): void => {
      let users: string[] = [];
      let ids: string[] = [];
      JointlySignTableRef.value.getDataSource().forEach((item) => {
        if (item.countersignUserName) {
          users.push(item.countersignUserName);
          ids.push(item.countersignUserId);
        }
      });
      btnsTableRef.value.getDataSource()[state.currentIndex].assignees = unionBy(users).join(',');
      btnsTableRef.value.getDataSource()[state.currentIndex].selectIds = unionBy(ids).join(',');
      btnsTableRef.value.setProps(btnsTableRef.value.getDataSource());
      openModalJointlySign(false);
    };
    const JointlySignCancel = (): void => {};

    // 会签内容table
    const JointlySignInfo = async (): Promise<void> => {
      const res = await new Api('/pms').fetch(
        { desId: route.params.id },
        'countersign/list',
        'POST',
      );
      state.JointlySignData = res;
      openModalJointlySign(true);
    };

    const JointlySignModal = (index, record): void => {
      _getUsers();
      state.JointlySignTableIndex = index;
      state.selectIds = record.countersignUserId ? record.countersignUserId.split(',') : [];
      transferModalRef.value.openModal();
    };
      // 設置表格参数
    const setJointlySignTableData = (addData): void => {
      JointlySignTableRef.value.getDataSource()[state.JointlySignTableIndex].countersignUserId = addData?.id || '';
      JointlySignTableRef.value.getDataSource()[state.JointlySignTableIndex].countersignUserName = addData?.name || '';
      putJointlySignTableData(
        JointlySignTableRef.value.getDataSource()[state.JointlySignTableIndex],
      );
      transferModalRef.value.openModal(false);
    };
      // 调用接口修改数据
    const putJointlySignTableData = (data): void => {
      data.desId = route.params.id;
      new Api('/pms').fetch(data, 'countersign', 'PUT').then(() => {
        JointlySignInfo();
      });
    };

    /* 会签内容 */

    return {
      ...toRefs(state),
      onChangeFlow,
      validateForm,
      registerModal,
      closeModal,
      btnsTableRef,
      transferModalRef,
      dataApi: () => new Api(state.req.api).fetch(state.req.params, state.req.url, 'POST'),
      onOpenModal(row, index) {
        state.currentItem = row;
        state.currentIndex = index;
        if (row.tags && row.tags.indexOf('JOINTLY_SIGN') !== -1) {
          JointlySignInfo();
        } else {
          _getUsers();
          state.user = row.user.split(',');
          state.selectIds = row?.selectIds ? row?.selectIds.split(',') : [];
          state.isSingle = !row.multiInst; // 看是否多人审批
          state.organizations = _changeStrToArr(row.organization);
          state.roles = _changeStrToArr(row.role);
          transferModalRef.value.openModal();
        }
      },
      onSubmitSelectReviewer(add) {
        if (state.isSingle && add.targetItems.length > 1) {
          message.warn('此节点为单人审批，不能选择多个审批人');
          return;
        }
        if (state.currentItem && state.currentItem.tags.indexOf('JOINTLY_SIGN') !== -1) {
          setJointlySignTableData(add.targetItems[0]);
        } else {
          let users: string[] = [];
          let ids: string[] = [];
          add.targetItems.forEach((item) => {
            users.push(item.name);
            ids.push(item.id);
          });
          btnsTableRef.value.getDataSource()[state.currentIndex].assignees = users.join(',');
          btnsTableRef.value.getDataSource()[state.currentIndex].selectIds = ids.join(',');
          btnsTableRef.value.setProps(btnsTableRef.value.getDataSource());
          transferModalRef.value.openModal(false);
        }
      },
      /* 会签内容 */
      registerJointlySignModal,
      JointlySignOk,
      JointlySignCancel,
      JointlySignModal,
      JointlySignTableRef,
      /* 会签内容 */
    };
  },
});

function renderFlow(xml) {
  const container: any = document.getElementById('flow-canvas');
  if (container.childNodes.length > 0) {
    container.innerHTML = '';
  }
  const bpmnModeler = new Viewer({
    container: '#flow-canvas',
    additionalModules: [CustomRenderer, MoveCanvasModule],
  });

  bpmnModeler.importXML(xml, (err) => {
    if (err) {
      // console.error(err);
    } else {
      bpmnModeler.get('canvas').zoom('fit-viewport', 'auto'); // 自动调整流程在画布的位置
    }
  });
}
</script>
<style lang="less" scoped>
  .select-items {
    display: flex;
    flex-direction: row;
    .style-line {
      display: flex;
      flex-direction: row;
      align-items: center;
    }
  }
  .flow-title {
    border-bottom: 1px solid #e9ecf2;
    line-height: 50px;
    padding: 0 10px;
    display: flex;
    align-items: center;
    h3 {
      font-size: 16px;
      color: #19243b;
      margin: 0;
      padding-left: 15px;
      font-weight: normal;
      position: relative;
      flex: 1;
    }
    h3::after {
      content: '';
      position: absolute;
      left: 0;
      height: 16px;
      width: 4px;
      top: 50%;
      margin-top: -8px;
      background: #0960bd;
    }
  }
  :deep(.bpmn-containers) {
    width: 100%;
    height: 40vh;

    #flow-canvas {
      height: 100%;
      overflow: scroll;
    }

    img {
      display: none;
    }
  }
  :deep(.ant-table-body) {
    height: 20vh !important;
  }
  .bd {
    border: 1px solid #ccc;
  }
</style>
