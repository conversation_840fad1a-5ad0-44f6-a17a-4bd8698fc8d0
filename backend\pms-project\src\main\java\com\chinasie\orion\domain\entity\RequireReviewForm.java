package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;

/**
 * RequireReviewForm Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-03 20:42:14
 */
@TableName(value = "pmsx_require_review_form")
@ApiModel(value = "RequireReviewFormEntity对象", description = "需求评审单")
@Data

public class RequireReviewForm extends  ObjectEntity  implements Serializable{

    /**
     * 需求评审标识
     */
    @ApiModelProperty(value = "需求评审标识")
    @TableField(value = "require_review_logo")
    private String requireReviewLogo;

    /**
     * 军兵种
     */
    @ApiModelProperty(value = "军兵种")
    @TableField(value = "army_arms")
    private String armyArms;

    /**
     * 应用场景
     */
    @ApiModelProperty(value = "应用场景")
    @TableField(value = "application_scenarios")
    private String applicationScenarios;

    /**
     * 是否定型
     */
    @ApiModelProperty(value = "是否定型")
    @TableField(value = "is_case_hardened")
    private String isCaseHardened;

    /**
     * 是否军检
     */
    @ApiModelProperty(value = "是否军检")
    @TableField(value = "id_examine")
    private String idExamine;

    /**
     * 产品线
     */
    @ApiModelProperty(value = "产品线")
    @TableField(value = "product_line")
    private String productLine;

    /**
     * 公司预计签订金额-共计
     */
    @ApiModelProperty(value = "公司预计签订金额-共计")
    @TableField(value = "total_sign_amount")
    private BigDecimal totalSignAmount;

    /**
     * 预计签订金额
     */
    @ApiModelProperty(value = "预计签订金额")
    @TableField(value = "sign_amount")
    private BigDecimal signAmount;

    /**
     * 市场需求容量
     */
    @ApiModelProperty(value = "市场需求容量")
    @TableField(value = "demand_capacity")
    private String demandCapacity;

    @ApiModelProperty(value = "商机标识")
    @TableField(value = "lead_sign")
    private String leadSign;

    /**
     * 商机名称
     */
    @ApiModelProperty(value = "商机名称")
    @TableField(value = "lead_name")
    private String leadName;


    /**
     * 签单客户
     */
    @ApiModelProperty(value = "签单客户")
    @TableField(value = "sign_client")
    private String signClient;


    /**
     * 产品竞争情况
     */
    @ApiModelProperty(value = "产品竞争情况")
    @TableField(value = "competition_situation")
    private String competitionSituation;

}
