<template>
  <Layout2Content
    v-if="contentTabs?.length>0"
    v-model:contentTabsIndex="contentTabsIndex"
    :contentTabs="contentTabs"
  >
    <PeopleManege
      v-if="contentTabs[contentTabsIndex]?.name === '成员管理'"
      :id="id"
      :pageType="pageType"
      @business="businessPeopleManege"
    />
    <Stakeholder
      v-if="contentTabs[contentTabsIndex]?.name === '干系人管理'"
      :id="id"
      :pageType="pageType"
    />
  </Layout2Content>
</template>

<script lang="ts">
import {
  defineComponent, onMounted, reactive, toRefs, inject,
} from 'vue';
import {
  Layout2Content, isPower,
} from 'lyra-component-vue3';
import PeopleManege from './components/peopleManege.vue';
import Stakeholder from './components/Stakeholder.vue';
export default defineComponent({
  name: 'ProjectSet',
  components: {
    Stakeholder,
    PeopleManege,
    Layout2Content,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
    pageType: {
      type: String,
      default: 'page',
    },
  },
  setup(props) {
    const state = reactive({
      className: '',
      contentTabsIndex: 0,
      powerData: [],
      businessId: '',
    });
    const state6 = reactive({
      // [{ name: '成员管理' }, { name: '干系人管理' }]
      contentTabs: [],
    });
    state.powerData = inject('powerData');
    const initForm = (data) => {
      state.className = data.className;
    };

    function businessPeopleManege(businessId) {
      state.businessId = businessId;
    }
    onMounted(() => {
      if (isPower('XMX_container_09_01', state.powerData) || props.pageType === 'modal') {
        state6.contentTabs.push({ name: '成员管理' });
      }
      if (isPower('XMX_container_09_02', state.powerData) || props.pageType === 'modal') {
        state6.contentTabs.push({ name: '干系人管理' });
      }
    });
    return {
      ...toRefs(state),
      ...toRefs(state6),
      initForm,
      isPower,
      businessPeopleManege,
    };
  },
});
</script>

<style scoped></style>
