<script setup lang="ts">
import { BasicCard, OrionTable } from 'lyra-component-vue3';
import {
  computed, inject, nextTick, ref, watchEffect,
} from 'vue';
import Api from '/@/api';

const supplierInfo = inject('supplierInfo');
const baseInfoProps = ref();
const tableOptions = {
  showToolButton: false,
  showTableSetting: false,
  immediate: false,
  showSmallSearch: false,
  pagination: false,
  columns: [
    {
      title: '可提供产品一级',
      dataIndex: 'productLevelOne',
    },
    {
      title: '可提供产品二级',
      dataIndex: 'productLevelTwo',
    },
    {
      title: '可提供产品三级',
      dataIndex: 'productLevelThree',
    },
    {
      title: '可提供产品四级',
      dataIndex: 'productLevelFour',
    },
  ],
  dataSource: computed(() => baseInfoProps.value),
};
const loading = ref(false);
async function getDetails() {
  loading.value = true;
  try {
    baseInfoProps.value = await new Api('/spm/supplierProducts/getProductByCode').fetch({
      supplierCode: supplierInfo?.value?.supplierNumber,
    }, '', 'POST');
  } finally {
    loading.value = false;
  }
}

watchEffect(async () => {
  await nextTick();
  setTimeout(() => {
    getDetails();
  }, 1000);
});
</script>

<template>
  <BasicCard
    title="可提供产品信息"
    :isBorder="false"
  >
    <div class="product-code">
      <OrionTable
        ref="tableRef"
        class="scroll-table"
        :options="tableOptions"
      />
    </div>
  </BasicCard>
</template>

<style scoped lang="less">
.product-code{
  height: 300px;
  overflow: hidden;
  :deep(.ant-basic-table){
    &.default-spacing{
      padding: 0;
    }
  }
}
</style>