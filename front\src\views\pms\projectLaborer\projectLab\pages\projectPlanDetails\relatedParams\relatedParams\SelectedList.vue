<template>
  <div>
    <div class="flex flex-pj plr5 flex-ac selected-title">
      <span class="selectedNumber">已选择({{ state.selectedAllList?.length??0 }})</span>
      <span
        class="action-btn"
        @click="deleteAll"
      >删除全部</span>
    </div>
    <div class="selected-main">
      <template v-if="state.selectedAllList?.length">
        <div
          v-for="(item,index) of state.selectedAllList"
          :key="index"
          class="flex flex-pj plr5 flex-ac selected-item"
        >
          <div
            class="selected-name"
            :title="item.name"
          >
            {{ item.name }}
          </div>
          <div @click="deleteItem(item)">
            <i class="fa fa-close action-btn mr5" />
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch } from 'vue';
const state = reactive({
  selectedAllList: [],
});
const props = defineProps({
  currentAllSelected: {
    type: Array,
    default: () => [],
  },
  rowKey: {
    type: String,
    default: 'id',
  },
});
const emit = defineEmits(['deleteAfter']);
watch(() => props.currentAllSelected, () => {
  state.selectedAllList = JSON.parse(JSON.stringify(props.currentAllSelected));
}, {
  deep: true,
});
// 删除一条
function deleteItem(item) {
  const index = state.selectedAllList.findIndex((it) => item[props.rowKey] === it[props.rowKey]);
  state.selectedAllList.splice(index, 1);
  emit('deleteAfter', state.selectedAllList);
}
// 删除全部
function deleteAll() {
  state.selectedAllList = [];
  emit('deleteAfter', state.selectedAllList);
}
</script>

<style scoped lang="less">
.selected-title{
  height: 35px;
  background-color: #dadada;
  .selectedNumber{
    font-weight: 600;
    //font-size: 20px;
  }
}
.selected-main{
.selected-item{
  height: 30px;
  &:hover{
    background-color: #dedede;
  }
  border-bottom: 1px dotted #e0dede;
  .selected-name{
    color: #7d7d7d;
    font-size: 12px;
    cursor: pointer;
    text-overflow: ellipsis;
    overflow: hidden;
    width: 150px;
    white-space: nowrap;
  }
}

}
</style>