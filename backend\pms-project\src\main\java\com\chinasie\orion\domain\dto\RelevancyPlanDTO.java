package com.chinasie.orion.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2023/04/11/17:57
 * @description:
 */
@Data
public class RelevancyPlanDTO {
    @ApiModelProperty("综合计划编码")
    private String planNumber;
    /**
     * 综合计划ID
     */
    @ApiModelProperty(value = "综合计划ID")
    private String basePlanId;

    private String id;
    @ApiModelProperty("是否例行")
    private Boolean routine;
    /**
     * 来源类型
     */
    @ApiModelProperty(value = "来源类型")
    private String sourceType;

    /**
     * 来源类型名称
     */
    @ApiModelProperty(value = "来源类型名称")
    private String sourceTypeName;

}
