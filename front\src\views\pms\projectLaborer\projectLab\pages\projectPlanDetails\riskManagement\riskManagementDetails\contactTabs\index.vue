<template>
  <Layout2Content
    v-if="contentTabs?.length>0"
    v-model:contentTabsIndex="contentTabsIndex"
    :contentTabs="contentTabs"
    @contentTabsChange="contentTabsChange2"
  >
    <ContactPlan
      v-if="contentTabs[contentTabsIndex]?.name === '关联计划'"
      :id="id"
    />
    <ContactQuestion
      v-if="contentTabs[contentTabsIndex]?.name === '关联问题'"
      :id="id"
    />

    <!--关联变更-->
    <ContactAssociated
      v-if="contentTabs[contentTabsIndex]?.name === '关联变更'"
      :id="id"
    />

    <RiskContactDoc
      v-if="contentTabs[contentTabsIndex]?.name === '关联文档' "
      :id="id"
    />
  </Layout2Content>
</template>

<script lang="ts">
import {
  defineComponent, reactive, toRefs, onMounted, inject,
} from 'vue';
import {
  Layout2Content, isPower,
} from 'lyra-component-vue3';
import RiskContactDoc from './riskContactDoc/index.vue';
import ContactPlan from './contactPlan/index.vue';
import ContactQuestion from './contactQuestion/index.vue';
import ContactAssociated
  from '/@/views/pms/projectLaborer/projectLab/pages/projectPlanDetails/components/associatedChange/contactAssociated.vue';

// import { Layout2Content } from '/@/components/Layout2.0';
// import useIndex from '/@/views/pms/projectLaborer/zkhooks/useLocalS.js';

export default defineComponent({
  // name: 'ProjectSet',
  components: {
    ContactAssociated,
    RiskContactDoc,
    ContactPlan,
    Layout2Content,
    ContactQuestion,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
  },

  setup() {
    const state = reactive({
      className: '',
      contentTabsIndex: 0,
    });
    const state6 = reactive({
      // [{ name: '关联计划' }, { name: '关联风险' }, { name: '关联文档' }]
      contentTabs: [],
    });
    // const questionContactLocal = useIndex('questionContactLocal');
    function contentTabsChange2(index) {
      state.contentTabsIndex = index;
      // questionContactLocal.value = index;
    }
    const initForm = (data) => {
      state.className = data.className;
    };
    onMounted(() => {
      state6.contentTabs.push({ name: '关联计划' });
      state6.contentTabs.push({ name: '关联问题' });
      state6.contentTabs.push({ name: '关联变更' });
      state6.contentTabs.push({ name: '关联文档' });
      // if (questionContactLocal.value !== 0) {
      //   state.contentTabsIndex = questionContactLocal.value;
      // } else {
      //   state.contentTabsIndex = 0;
      // }
    });
    return {
      ...toRefs(state),
      ...toRefs(state6),
      initForm,
      contentTabsChange2,
    };
  },
});
</script>

<style scoped></style>
