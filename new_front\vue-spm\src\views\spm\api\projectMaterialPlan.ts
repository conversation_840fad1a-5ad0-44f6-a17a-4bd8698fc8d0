import Api from '/@/api';

/**
 * 新增
 * @param params 参数
 */
export const add = (params) => new Api('/spm/projectMaterialPlan/add').fetch(params, '', 'POST');
/**
 * 编辑
 * @param params 参数
 */
export const edit = (params) => new Api('/spm/projectMaterialPlan/edit').fetch(params, '', 'PUT');
/**
 * 分页
 * @param params 参数
 */
export const page = (params) => new Api('/spm/projectMaterialPlan/page').fetch(params, '', 'POST');
/**
 * 研发物料父级分页
 * @param params 参数
 */
export const materialParentPage = (params) => new Api('/spm/projectMaterialPlan/materialParent/page').fetch(params, '', 'POST');
/**
 * 物资追踪分页
 * @param params 参数
 */
export const tracePage = (params) => new Api('/spm/projectMaterialPlan/trace/page').fetch(params, '', 'POST');
/**
 * 物料分页
 * @param params 参数
 */
export const materialPage = (params) => new Api('/spm/projectMaterialPlan/material/page').fetch(params, '', 'POST');
/**
 * 详情
 * @param id 参数
 * @param pageCode 参数
 */
export const projectMaterialPlanById = (id, pageCode = '') => new Api(`/spm/projectMaterialPlan/${id}?pageCode=${pageCode}`).fetch('', '', 'GET');
