package com.chinasie.orion.domain.dto.reporting;

import com.chinasie.orion.domain.request.reporting.ListDailyRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2023/11/14/23:11
 * @description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExportParamDTO extends ListDailyRequest  implements Serializable {
    private List<String> idList;
}
