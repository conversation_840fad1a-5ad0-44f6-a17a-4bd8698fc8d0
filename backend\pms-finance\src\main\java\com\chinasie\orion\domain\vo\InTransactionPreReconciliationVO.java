package com.chinasie.orion.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.UserDataBind;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel(value = "InTransactionPreReconciliationVO对象", description = "内部交易预对账明细表")
@Data
public class InTransactionPreReconciliationVO implements Serializable {

    @ApiModelProperty(value = "收入计划id")
    private String id;

    @ApiModelProperty(value = "专业中心")
    private String expertiseCenter;

    @ApiModelProperty(value = "专业中心名称")
    private String expertiseCenterName;

    @ApiModelProperty(value = "专业所")
    private String expertiseStation;

    @ApiModelProperty(value = "专业所名称")
    private String expertiseStationName;

    @ApiModelProperty(value = "收入计划月份")
    private String workTopics;

    @ApiModelProperty(value = "工作主题名称")
    private String workTopicsName;

    @ApiModelProperty(value = "收入计划编号")
    private String incomePlanDataNumber;


    @ApiModelProperty(value = "开票/收入确认公司")
    private String billingCompany;

    @ApiModelProperty(value = "开票/收入确认公司名称")
    private String billingCompanyName;

    /**
     * 甲方单位id
     */
    @ApiModelProperty(value = "甲方单位id")
    private String partyADeptId;

    @ApiModelProperty(value = "甲方单位名称")
    private String partyADeptIdName;

    @ApiModelProperty(value = "所属行业")
    private String industry;

    @ApiModelProperty(value = "合同ID")
    private String contractId;

    @ApiModelProperty(value = "合同编码")
    private String contractNumber;

    @ApiModelProperty(value = "合同名称")
    private String contractName;

    @ApiModelProperty(value = "合同里程碑id")
    private String milestoneId;

    @ApiModelProperty(value = "合同里程碑名称")
    private String milestoneName;



    @ApiModelProperty(value = "计划验收日期")
    private Date planAcceptDate;


    @ApiModelProperty(value = "里程碑金额")
    private BigDecimal milestoneAmt;

    @ApiModelProperty(value = "税率")
    private String taxRate;

    @ApiModelProperty(value = "含税金额")
    private BigDecimal  includedTaxAmt;

    @ApiModelProperty(value = "不含税金额")
    private BigDecimal incomePlanAmt;

    @ApiModelProperty(value = "甲方所属二级单位")
    private String partASecondDept;

    @ApiModelProperty(value = "合同类型")
    private String contractType;

    @ApiModelProperty(value = "合同类型名称")
    private String contractTypeName;

    @ApiModelProperty(value = "合同状态")
    private String contractStatus;

    @ApiModelProperty(value = "合同状态名称")
    private String contractStatusName;

    @ApiModelProperty(value = "甲方合同号/订单号")
    private String orderNumber;

    @ApiModelProperty(value = "电厂项目性质")
    private String powerProjectPlant ;

    @ApiModelProperty(value = "电厂项目性质名称")
    private String powerProjectPlantName ;


    /**
     * 技术负责人
     */
    @ApiModelProperty(value = "技术负责人")
    private String techRspUser;

    /**
     * 技术负责人名称
     */
    @ApiModelProperty(value = "技术负责人名称")
    private String techRspUserName;


    /**
     * 商务负责人
     */
    @ApiModelProperty(value = "商务负责人")
    private String busRspUser;

    /**
     * 商务负责人名称
     */
    @ApiModelProperty(value = "商务负责人名称")
    private String busRspUserName;


    @ApiModelProperty(value = "客户技术联系人")
    private String cusTechRspUser;

    @ApiModelProperty(value = "客户技术联系人名称")
    private String cusTechRspUserName;

    @ApiModelProperty(value = "客户商务联系人")
    private String cusBusRspUser;

    @ApiModelProperty(value = "客户商务联系人名称")
    private String cusBusRspUserName;







}
