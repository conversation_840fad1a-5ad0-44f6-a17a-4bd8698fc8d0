<template>
  <a-form
    ref="formRef"
    label-align="left"
    :rules="rules"
    :model="father.form"
    :label-col="{ span: 4 }"
    :wrapper-col="{ span: 14 }"
  >
    <a-row :gutter="20">
      <a-col
        :span="12"
        class="content-box"
      >
        <BasicTitle title="基本信息">
          <a-form-item
            label="计划名称"
            name="name"
          >
            <a-input
              v-model:value="father.form.name"
              style="width: 100%"
              placeholder="请输入计划名称"
              allow-clear
              :maxlength="64"
              size="large"
            />
          </a-form-item>
          <a-form-item label="计划编号">
            {{ father.form.number }}
          </a-form-item>
          <a-form-item
            label="所属类型"
            name="planType"
          >
            <a-select
              v-model:value="father.form.planType"
              placeholder="请选择所属类型"
              allow-clear
              :options="subJectList"
              size="large"
            />
          </a-form-item>
          <a-form-item label="负责人">
            <a-select
              v-model:value="father.form.principalId"
              class="w-full"
              placeholder="请选择负责人"
              allow-clear
              show-search
              :filter-option="filterOption"
              :options="namesList"
              size="large"
              @change="handlePrincipal(namesList, father.form.principalId)"
            />
          </a-form-item>
          <a-form-item
            label="参与者"
            name="participantName"
          >
            <a-input
              v-model:value="father.form.participantName"
              style="width: 100%"
              placeholder="请选择参与者"
              allow-clear
              size="large"
              @click="changeValue"
              @change="changeValue1"
            >
              <template #addonAfter>
                <span @click="changeValue">请选择</span>
              </template>
            </a-input>
          </a-form-item>
          <a-form-item label="优先级">
            <a-select
              v-model:value="father.form.priorityLevel"
              placeholder="请选择优先级"
              allow-clear
              :options="priorityList"
              size="large"
            />
          </a-form-item>
          <a-form-item label="开始日期">
            <a-date-picker
              v-model:value="father.form.planPredictStartTime"
              show-time
              type="date"
              class="w-full"
              placeholder="请选择开始日期"
              allow-clear
              :disabled-date="disabledStartDate"
              value-format="YYYY-MM-DD HH:mm:ss"
              size="large"
            />
          </a-form-item>
          <a-form-item label="结束日期">
            <a-date-picker
              v-model:value="father.form.planPredictEndTime"
              show-time
              type="date"
              class="w-full"
              placeholder="请选择结束日期"
              allow-clear
              :disabled-date="disabledEndDate"
              value-format="YYYY-MM-DD HH:mm:ss"
              size="large"
            />
          </a-form-item>
          <a-form-item
            label="预估工时"
            name="manHour"
          >
            <a-input-number
              v-model:value="father.form.manHour"
              :min="0"
              :max="100000"
              placeholder="请输入工时"
              size="large"
            />
          </a-form-item>
          <a-form-item label="状态">
            <a-select
              v-model:value="father.form.status"
              class="w-full"
              placeholder="请选择状态"
              :options="statusList"
              size="large"
            />
          </a-form-item>
          <a-form-item label="描述">
            <a-textarea
              v-model:value="father.form.remark"
              placeholder="请输入描述"
              allow-clear
              :maxlength="255"
              size="large"
            />
          </a-form-item>
          <a-form-item label="修改人">
            {{ father.form.modifyName }}
          </a-form-item>
          <a-form-item label="修改时间">
            {{ formatDate(father.form.modifyTime) }}
          </a-form-item>
          <a-form-item label="创建人">
            {{ father.form.creatorName }}
          </a-form-item>
          <a-form-item label="创建时间">
            {{ formatDate(father.form.createTime) }}
          </a-form-item>
        </BasicTitle>
      </a-col>
      <a-col
        :span="12"
        class="content-box"
      >
        <BasicTitle title="计划属性">
          计划属性
        </BasicTitle>
      </a-col>
    </a-row>
  </a-form>
  <SelectUserModal
    @register="selectUserRegister"
  />
</template>

<script>
import {
  Row, Col, Input, DatePicker, Select, message, InputNumber,
  Form,
} from 'ant-design-vue';
import BasicTitle from '/@/views/pms/projectLaborer/componentsList/basicTitle/index.vue';
import { SelectUserModal, useModal } from 'lyra-component-vue3';

import dayjs from 'dayjs';
import {
  reactive, toRefs, onMounted, ref,
} from 'vue';
import Api from '/@/api';
import { parseURL } from '/@/views/pms/projectLaborer/utils/index';

export default {
  name: 'Edit',
  components: {
    ARow: Row,
    ACol: Col,
    BasicTitle,
    AForm: Form,
    AFormItem: Form.Item,
    AInput: Input,
    AInputNumber: InputNumber,
    ATextarea: Input.TextArea,
    ADatePicker: DatePicker,
    ASelect: Select,
    SelectUserModal,
  },
  props: {
    data: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  setup(props) {
    const [selectUserRegister, { openModal: selectUserOpenModal }] = useModal();
    const state = reactive({
      father: props.data,
      formRef: ref(),
      namesList: [],
      statusList: [],
      subJectList: [], // 所属类型
      priorityList: [], // 优先级
      rules: {
        name: [
          {
            required: true,
            message: '名称不能为空',
            trigger: 'blur',
          },
        ],
        planType: [
          {
            required: true,
            message: '所属类型不能为空',
            trigger: 'change',
          },
        ],
      },
    });

    function formatDate(t) {
      return t ? dayjs(t).format('YYYY-MM-DD HH:mm:ss') : '';
    }
    function filterOption(inputValue, node) {
      return node.props.label.includes(inputValue);
    }
    function init() {
      const url1 = `project-role-user/getListByName/${parseURL().projectId}?name=`;
      new Api('/pms').fetch('', url1, 'POST').then((res) => {
        state.namesList = res.map((s) => ({
          label: s.name,
          value: s.id,
        }));
      });

      const url2 = `project-task-status/policy/status/list/${parseURL().id}`;
      new Api('/pms').fetch('', url2, 'GET').then((res) => {
        state.statusList = res.map((s) => ({
          label: s.name,
          value: s.value,
        }));
      });

      const url3 = `task-subject/getList/${parseURL().projectId}`;
      new Api('/pms').fetch('', url3, 'GET').then((res) => {
        state.subJectList = res.map((s) => ({
          label: s.name,
          value: s.id,
        }));
      });
      new Api('/pms').fetch('', 'plan/priority/list', 'POST').then((res) => {
        state.priorityList = res.map((s) => ({
          label: s.name,
          value: s.id,
        }));
      });
    }
    function handlePrincipal(arr, id) {
      if (id) {
        const obj = arr.find((s) => s.value === id);
        state.father.form.principalName = obj.label;
      } else {
        state.father.form.principalName = undefined;
      }
    }
    function disabledStartDate(startValue) {
      if (!startValue || !state.father.form.planPredictEndTime) {
        return false;
      }
      return startValue.valueOf() > state.father.form.planPredictEndTime.valueOf();
    }
    function disabledEndDate(endValue) {
      if (!endValue || !state.father.form.planPredictStartTime) {
        return false;
      }
      return state.father.form.planPredictStartTime.valueOf() >= endValue.valueOf();
    }
    function submit(cb) {
      state.formRef
        .validate()
        .then(() => {
          cb(state.father.form);
        })
        .catch(() => {
          message.warning('请检查必填项');
        });
    }
    onMounted(() => {
      init();
      state.father.form.participantName = Array.isArray(state.father.form.participantName) ? state.father.form.participantName.join(';') : '';
    });
    const changeValue = () => {
      selectUserOpenModal(true, {
        async onOk(data) {
          state.father.form.participant = data.map((item) => item.id);
          state.father.form.participantName = data.map((item) => item.name);
        },
      });
    };
    const changeValue1 = () => {
      state.father.form.participantName = '';
      state.father.form.participant = [];
    };

    return {
      ...toRefs(state),
      formatDate,
      submit,
      handlePrincipal,
      disabledStartDate,
      disabledEndDate,
      filterOption,
      selectUserRegister,
      changeValue,
      changeValue1,
    };
  },
};
</script>

<style scoped lang="less">
  :deep(.basicTitle) {
    .basicTitle_content {
      padding-left: 40px !important;
      height: calc(100vh - 300px);
      overflow: auto;
    }
  }
</style>
