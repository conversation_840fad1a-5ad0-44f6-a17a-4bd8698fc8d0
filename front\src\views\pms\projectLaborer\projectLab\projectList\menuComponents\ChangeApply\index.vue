<template>
  <OrionTable
    v-if="showTable"
    ref="tableRef"
    :options="tableOptions"
  >
    <template
      #toolbarLeft
    >
      <div
        v-if="pageType==='page'&&showBtn"
        class="changeApplyMainTable_add"
      >
        <BasicButton
          type="primary"
          icon="add"
          @click="addTableNode"
        >
          创建变更
        </BasicButton>
      </div>
    </template>
    <!--    <template #toolbarRight>-->
    <!--      <AInputSearch-->
    <!--        v-model:value="tableSearchVal"-->
    <!--        style="width: 200px"-->
    <!--        placeholder="请输入名称或者编号"-->
    <!--        @search="searchForm"-->
    <!--      />-->
    <!--    </template>-->
    <template
      v-if="pageType==='page'"
      #name="{ record }"
    >
      <div
        class="action-btn flex-te"
        @click="openDetails(record)"
      >
        {{ record.name }}
      </div>
    </template>
  </OrionTable>
  <AddTableNode
    v-if="pageType==='page'"
    :addApi="addApi"
    :ecrDirName="ecrDirName"
    :formId="formId"
    @register="register"
    @update="updateData"
  />
</template>
<script lang="ts">
import {
  computed, defineComponent, inject, onMounted, reactive, ref, toRefs,
} from 'vue';
import { BasicButton, OrionTable, useDrawer } from 'lyra-component-vue3';
import { Input } from 'ant-design-vue';
import { stampDate } from '/@/views/pms/projectLaborer/utils/dateUtil';
import { useQiankun } from '/@/utils/qiankun/useQiankun';
import Api from '/@/api';
import AddTableNode from './components/AddTableNode.vue';

export default defineComponent({
  name: 'ChangeApply',
  components: {
    OrionTable,
    // AInputSearch: Input.Search,
    AddTableNode,
    BasicButton,
  },
  props: {
    pageType: {
      type: String,
      default: 'page',
    },
    addApi: {
      type: Function,
      default: () => null,
    },
    ecrDirName: {
      type: String,
      default: '',
    },
    formId: {
      type: String,
      default: '',
    },
    showBtn: {
      type: Boolean,
      default: true,
    },
  },
  setup(props) {
    const { mainRouter } = useQiankun();
    const powerData: any = inject('powerData', {});
    const state = reactive({
      formId: props.formId,
      tableSearchVal: '',
      params: {
        query: {
          objIds: [props.formId],
        },
      },
      powerData: [],
      showTable: false,
    });

    const [register, { openDrawer }] = useDrawer();
    const tableRef = ref(null);
    const tableOptions = ref({
      deleteToolButton: 'add|enable|disable|delete',
      rowSelection: props.pageType === 'page' ? {} : false,
      showSmallSearch: true,
      showIndexColumn: false,
      isFilter2: true,
      filterConfigName: 'PMS_PROJECTMANAGE_CHANGEMANAGE',
      api: computed(() => getChangeApplyTableList(state.params)),
      columns: [
        {
          title: '标题',
          dataIndex: 'name',
          slots: { customRender: 'name' },
          align: 'left',
          minWidth: 200,
        },
        {
          title: '编号',
          align: 'left',
          dataIndex: 'number',
          slots: { customRender: 'messageName' },
          width: 200,
        },
        {
          title: '变更类型',
          dataIndex: 'changeWay',
          align: 'left',
          width: 100,
          customRender: ({
            record,
          }) => (record.changeWay === 1 ? '快速变更' : '工程变更'),
        },
        {
          title: '所属类型',
          dataIndex: 'ecrTypeName',
          align: 'left',
          width: 100,
        },
        {
          title: '状态',
          dataIndex: 'status',
          align: 'left',
          width: 150,
          slots: { customRender: 'status' },
        },
        {
          title: '负责人',
          dataIndex: 'responsiblerName',
          width: 150,
          align: 'left',
        },
        {
          title: '申请时间',
          dataIndex: 'applyTime',
          type: 'dateTime',
          align: 'left',
          customRender: ({
            record,
          }) => (record.modifyTime.length > 0 ? stampDate(record.applyTime, 'yyyy-MM-dd HH:mm:ss') : ''),
        },
      ],
    });
    function getChangeApplyTableList(params) {
      return (tableParams) => new Api('/pas').fetch(Object.assign(params, tableParams), 'ecr/relation/pages', 'POST');
    }
    const searchForm = (val) => {
      if (props.showBtn) {
        state.params = {
          query: {
            objIds: [props.formId],
            name: val,
          },
        };
      } else {
        state.params = {
          query: {
            objIds: [props.formId],
            name: val,
          },
        };
      }
    };
    const addTableNode = () => {
      openDrawer(true, {
        type: 'add',
        data: { ecnDir: state.formId },
      });
    };

    const openDetails = (record) => {
      mainRouter.push({
        name: 'PASChangeApplyDetails',
        params:
            {
              id: record.id,
            },
      });
    };
    onMounted(async () => {
      state.params = {
        query: {
          objIds: [props.formId],
        },
      };
      state.showTable = true;
    });
    const updateData = () => {
      tableRef.value.reload();
    };

    return {
      ...toRefs(state),
      tableOptions,
      tableRef,
      addTableNode,
      register,
      openDetails,
      updateData,
      searchForm,
    };
  },
});
</script>
<style lang="less" scoped>
</style>
