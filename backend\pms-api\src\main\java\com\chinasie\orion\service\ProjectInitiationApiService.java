package com.chinasie.orion.service;

import com.chinasie.orion.domain.vo.MarketContractApiVO;
import com.chinasie.orion.domain.vo.NcfFormpurchaseRequestDetailAPIAsset;
import com.chinasie.orion.domain.vo.ProjectInitiationAPIWBS;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/8/23
 */
public interface ProjectInitiationApiService {
    /**
     * 查询立项个数
     *
     * @param year
     * @param type
     * @return
     */
    Integer findInitiationCount(String year, Integer type);

    /**
     * 查询关闭立项个数
     *
     * @param year
     * @param type
     * @return
     */
    Integer findCloseCount(String year, Integer type);

    /**
     * 查询合同信息
     * @param projectNumbers
     * @return
     */
    List<MarketContractApiVO> findByProjectNumber(List<String> projectNumbers);

    /**
     * 查询WBS详情
     * @param projectNumbers
     * @return
     */
    List<ProjectInitiationAPIWBS> getProjectAssetApplyDetailWbs(List<String> wbsIds);

    /**
     * 查询WBS分页
     * @param projectNumbers
     * @return
     */
    Page<ProjectInitiationAPIWBS> getProjectInitiationAPIWBSByProjectId(String projectId,Long pageNum,Long pageSize) throws Exception;

    /**
     * 查询NcfFormpurchaseRequestDetail详情
     * @param projectNumbers
     * @return
     */
    List<NcfFormpurchaseRequestDetailAPIAsset> getNcfFormpurchaseRequestDetailAPIAsset(List<String> assetIds);

    /**
     * 查询NcfFormpurchaseRequestDetail分页
     * @param projectNumbers
     * @return
     */
    Page<NcfFormpurchaseRequestDetailAPIAsset> getNcfFormpurchaseRequestDetailAPIAssetByProjectId(String projectId,Long pageNum,Long pageSize) throws Exception;
    /**
     * 查询项目编号
     * @param contractNumber
     * @param contractName
     * @return
     */
    List<String> findByProjectNumber(String contractNumber, String contractName);
}
