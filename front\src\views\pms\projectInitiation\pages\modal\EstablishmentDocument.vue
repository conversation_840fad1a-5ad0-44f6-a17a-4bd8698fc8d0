<template>
  <div class="establishment-task">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      :expandIconColumnIndex="3"
      :expandedRowKeys="defaultExpandedRowKeys"
      @expand="expandRows"
      @selectionChange="selectionChange"
    >
      <template #toolbarLeft>
        <div class="toolbar-left">
          <BasicButton
            v-if="isPower('PMS_XMLX_container_03_head_button_09',powerData)"
            type="primary"
            icon="add"
            @click="addTableNode"
          >
            添加条目
          </BasicButton>
          <BasicButton
            v-if="isPower('PMS_XMLX_container_03_head_button_10',powerData)"
            icon="sie-icon-jihuaxiafa"
            @click="createdTask"
          >
            生成任务
          </BasicButton>
          <BasicButton
            v-if="isPower('PMS_XMLX_container_03_head_button_11',powerData)"
            icon="delete"
            :disabled="selectedRowKeys.length===0"
            @click="deleteBatch"
          >
            删除
          </BasicButton>
        </div>
      </template>
      <template #toolbarRight>
        <slot name="slotRight" />
      </template>

      <template #index="{record}">
        {{ indexData?.filter(v => v?.id === record?.id)[0]?.index }}
      </template>
    </OrionTable>
  </div>
</template>

<script lang="ts" setup>
import {
  BasicButton, isPower, openDrawer, OrionTable,
} from 'lyra-component-vue3';
import {
  computed, h, inject, Ref, ref,
} from 'vue';
import dayjs from 'dayjs';
import { message, Modal } from 'ant-design-vue';
import AddDocumentNode from '../components/AddDocumentNode.vue';
import { declarationData } from '/@/views/pms/projectInitiation/pages/keys';
import Api from '/@/api';
import { initDocumentTableData } from '../../index';

const tableRef = ref();
const defaultExpandedRowKeys = ref<string[]>([]); // 展开的key
const detailsData = inject(declarationData);
const indexData:Ref<Record<any, any>[]> = ref([]);
const powerData = inject('powerData', []);
// 表格勾选数据
const selectedRowKeys = ref([]);
const selectedRows = computed(() => indexData.value.filter((item) => selectedRowKeys.value.filter((item1) => !item1.endsWith('_Top')).includes(item.id)));
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: {},
  showIndexColumn: false,
  showSmallSearch: false,
  pagination: false,
  showTableSetting: false,
  rowClassName: (record) =>
    (record.topSort ? 'table-striped' : null),
  api: async (tableParams) => {
    let result = await new Api('/pms').fetch({ approvalId: detailsData.value.id }, 'collaborativeCompilationDocument/getTree', 'POST');
    indexData.value = [];
    result = initDocumentTableData(result, '', indexData);
    defaultExpandedRowKeys.value = result.expandedRowKeys;
    return result.tableData;
  },

  columns: [
    {
      title: '序号',
      dataIndex: 'index',
      fixed: 'left',
      width: 70,
    },
    {
      title: '编码',
      dataIndex: 'number',
      fixed: 'left',
      width: 100,
    },
    {
      title: '条目标题',
      dataIndex: 'name',
      fixed: 'left',
      minWidth: 200,
    },
    {
      title: '编写要求',
      dataIndex: 'writeRequire',
      width: 230,
    },
    {
      title: '关联任务',
      dataIndex: 'taskName',
      width: 230,
    },
    {
      title: '修改人',
      dataIndex: 'modifyName',
      width: 120,
    },
    {
      title: '修改时间',
      dataIndex: 'modifyTime',
      width: 180,
      customRender({ text }) {
        return text ? dayjs(text)
          .format('YYYY-MM-DD') : '';
      },
    },

    {
      title: '操作',
      dataIndex: 'action',
      width: 240,
      align: 'left',
      fixed: 'right',
      slots: { customRender: 'action' },
    },
  ],
  actions: [
    {
      text: '编辑',
      isShow: (record) => isPower('PMS_XMLX_container_03_01_button_01', powerData),
      onClick: (record) => {
        openFormDrawer({
          type: 'edit',
          id: record.id,
        });
      },
    },
    {
      text: '删除',
      isShow: (record) => isPower('PMS_XMLX_container_03_01_button_02', powerData),
      onClick: (record) => {
        deleteBatchData([record.id], 'one');
      },
    },
    {
      text: '添加子项',
      isShow: (record) => isPower('PMS_XMLX_container_03_01_button_03', powerData),
      onClick: (record) => {
        openFormDrawer({
          type: 'add',
          parentId: record.id,
          approvalId: detailsData.value.id,
        });
      },
    },
  ],
});
function addTableNode() {
  openFormDrawer({
    type: 'add',
    parentId: '0',
    approvalId: detailsData.value.id,
  });
}

function openFormDrawer(drawerData) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: drawerData.type === 'add' ? '新增条目' : '编辑条目',
    width: 1000,
    content() {
      return h(AddDocumentNode, {
        ref: drawerRef,
        drawerData,
      });
    },
    async onOk(): Promise<void> {
      await drawerRef.value.onSubmit();
      upDateTable();
    },
  });
}
function expandRows(expanded, record) {
  if (expanded) {
    defaultExpandedRowKeys.value.push(record.id);
  } else {
    defaultExpandedRowKeys.value = defaultExpandedRowKeys.value.filter((item) => item !== record.id);
  }
}
function initTableData(tableData, indexItem) {
  let expandedRowKeys = [];
  tableData.forEach((item, index) => {
    item.index = indexItem + (index + 1);
    if (item.children && item.children.length === 0) {
      delete item.children;
    } else if (item.children && item.children.length) {
      expandedRowKeys.push(item.id);
      let newData = initTableData(item.children, `${item.index}.`);
      item.children = newData.tableData;
      expandedRowKeys = expandedRowKeys.concat(newData.expandedRowKeys);
    }
  });
  return {
    tableData,
    expandedRowKeys,
  };
}
function deleteBatch() {
  deleteBatchData(selectedRowKeys.value, 'batch');
}
function deleteBatchData(params, type) {
  Modal.confirm({
    title: '移除提示',
    content: type === 'batch' ? '是否移除选中的数据？' : '是否移除该条数据？',
    async onOk() {
      await new Api('/pms').fetch(params, 'collaborativeCompilationDocument/remove', 'DELETE');
      // await props.deleteQuestionBatchApi(params);
      message.success('删除成功');
      upDateTable();
    },
  });
}
function selectionChange(data) {
  selectedRowKeys.value = data.keys;
}
function upDateTable() {
  tableRef.value.reload();
}
function createdTask() {
  new Api('/pms').fetch({ id: detailsData.value.id }, 'collaborativeCompilationDocument/createTask', 'GET').then((res) => {
    message.success('生成任务成功');
    upDateTable();
  });
}
</script>
<style lang="less" scoped>
.establishment-task{
  height: 100%;
}
.toolbar-left{
  display: flex;
  align-items: center;
  flex-flow: wrap;
  width: calc(100% - 220px);
  gap: 10px 0;
}
</style>