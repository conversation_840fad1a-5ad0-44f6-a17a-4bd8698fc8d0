package com.chinasie.orion.management.schedule;

import com.chinasie.orion.constant.RequirementNodeDict;
import com.chinasie.orion.domain.vo.RequirementNoticeVO;
import com.chinasie.orion.management.repository.QuotationManagementMapper;
import com.chinasie.orion.management.repository.RequirementMangementMapper;
import com.chinasie.orion.msc.api.MscBuildHandlerManager;
import com.chinasie.orion.repository.MarketContractMapper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class QuotationDeadlineXxlJob {

    @Autowired
    QuotationManagementMapper quotationManagementMapper;

    @Autowired
    MscBuildHandlerManager mscBuildHandlerManager;

    @XxlJob("quotationDeadlineNotify")
    public void quotationDeadlineXxl() {
        List<RequirementNoticeVO> requirementNoticeVOS = quotationManagementMapper.queryData();
        requirementNoticeVOS.forEach(item->{
            mscBuildHandlerManager.send(item, RequirementNodeDict.NODE_QUTOTATION_DEADLINE);
        });
    }




}
