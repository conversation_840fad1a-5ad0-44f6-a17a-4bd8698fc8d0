<template>
  <BasicModal
    v-bind="$attrs"
    title="页面详情"
    width="100%"
    height="100%"
    :footer="null"
    :defaultFullscreen="true"
    :canFullscreen="false"
    @register="registerModal"

    @visible-change="visibleChange"
  >
    <div class="add-bookmark-table">
      <div class="add-bookmark-table-left">
        <div class="flex flex-align-center flex-pc m-b-t">
          <BasicButton
            type="primary"
            @click="saveForm"
          >
            保存
          </BasicButton>
        </div>
        <div class="scroll">
          <BasicScrollbar height="100%">
            <div
              v-for="(bookmark, index) in bookmarks"
              :key="index"
              class="m-b-tb"
            >
              <label
                :for="`input-${index}`"
              >{{ bookmark.bookMarkName }}</label>
              <AInput
                :id="`input-${index}`"
                v-model:value="bookmark.bookMarkVal"
                class="m-b-t"
              />
            </div>
          </BasicScrollbar>
        </div>
      </div>
      <div class="add-bookmark-table-middle">
        <EditFile
          v-if="showFile"
          :file-id="fileId"
        />
      </div>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
import {
  BasicModal, useModalInner, EditFile, BasicDrawer, BasicForm, useForm, isPower, BasicScrollbar,
} from 'lyra-component-vue3';
import {
  defineEmits, Ref, ref, defineProps,
} from 'vue';
import { Input as AInput, message } from 'ant-design-vue';
import Api from '/@/api';
import LabelTree from './LabelTree.vue';
import RightTable from './RightTable.vue';
import BasicButton from '/@/components/Button/src/BasicButton.vue';

const props = defineProps({
  formId: {
    type: String,
    default: '',
  },
});
const emits = defineEmits(['update']);
const bookmarks:any = ref();
const fileId:Ref<string> = ref('');
const showFile:Ref<boolean> = ref(false);
const rightTable = ref();
const schemas = ref();
const [
  registerForm,
  {
    setFieldsValue, clearValidate, resetFields, validateFields, updateSchema,
  },
] = useForm({
  actionColOptions: {
    span: 24,
  },
  showActionButtonGroup: false,
  schemas: schemas.value,
});
const [registerModal, { closeModal, setModalProps }] = useModalInner(async (modalData) => {
  fileId.value = modalData.fileId;
  showFile.value = true;
  generateDocument();
});
function generateDocument() {
  new Api(`/res/document-generate/generateDocument/getAllBookMarkValue/fileid/${fileId.value}`).fetch('', '', 'POST').then((res) => {
    bookmarks.value = res;
    showFile.value = true;
  });
}
function visibleChange(val) {
  if (!val) {
    showFile.value = false;
  }
}
function saveForm() {
  new Api(`/res/document-generate/generateDocument/updateBookMarkValues/fileid/${fileId.value}`).fetch(bookmarks.value, '', 'PUT').then((res) => {
    message.success('保存成功');

    showFile.value = false;
    generateDocument();
  });
}
function selectNode(nodeData) {
  rightTable.value.setCellName(nodeData);
}
</script>
<style lang="less" scoped>

.add-bookmark-table{
  display: flex;
  height: 100%;
  overflow: hidden;
  border-bottom: 1px solid #cccccc;
  .add-bookmark-table-left{
    width: 200px;
    border-right: 1px solid #cccccc;
    .scroll{
      overflow: hidden;
      height: calc(~'100% - 52px');
      padding:  ~`getPrefixVar('content-padding-top')` 10px;
    }

  }
  .add-bookmark-table-middle{
    flex: 1;
    min-width: 500px;
  }
}
</style>
