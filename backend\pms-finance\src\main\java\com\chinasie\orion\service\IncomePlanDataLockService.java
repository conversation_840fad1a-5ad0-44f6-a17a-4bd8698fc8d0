package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.IncomePlanDataLockDTO;
import com.chinasie.orion.domain.entity.IncomePlanDataLock;
import com.chinasie.orion.domain.vo.IncomePlanDataLockVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * IncomePlanDataLock 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29 18:50:39
 */
public interface IncomePlanDataLockService  extends  OrionBaseService<IncomePlanDataLock>  {


    /**
     *  详情
     *
     * * @param id
     */
    IncomePlanDataLockVO detail(String id, String pageCode)throws Exception;

    /**
     *  新增
     *
     * * @param incomePlanDataLockDTO
     */
    String create(IncomePlanDataLockDTO incomePlanDataLockDTO)throws Exception;

    /**
     *  编辑
     *
     * * @param incomePlanDataLockDTO
     */
    Boolean editCenter(List<IncomePlanDataLockDTO> incomePlanDataLockDTOList)throws Exception;

    /**
     *  编辑
     *
     * * @param incomePlanDataLockDTO
     */
    Boolean editStation(List<IncomePlanDataLockDTO> incomePlanDataLockDTOList)throws Exception;

    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  中心数据分页
     *
     * * @param pageRequest
     *
     */
    Page<IncomePlanDataLockVO> expertiseCenterPages( Page<IncomePlanDataLockDTO> pageRequest)throws Exception;

    /**
     *  所级数据分页
     *
     * * @param pageRequest
     *
     */
    Page<IncomePlanDataLockVO> expertiseStationPages( Page<IncomePlanDataLockDTO> pageRequest)throws Exception;

    /**
     *  下载模板
     *
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response)throws Exception;

    /**
     *  导入校验
     *
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


    /**
     *  确认导入
     *
     * * @param importId
     */
    Boolean importByExcel(String importId)throws Exception;

    /**
     *  取消导入
     *
     * * @param importId
     */
    Boolean importCancelByExcel(String importId)throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

    /**
     *  设置各种名称
     *
     * * @param searchConditions
     * * @param response
     */
    void  setEveryName(List<IncomePlanDataLockVO> vos)throws Exception;

    void  setExpertiseCenterEveryName(List<IncomePlanDataLockVO> vos)throws Exception;

    void  setExpertiseStationEveryName(List<IncomePlanDataLockVO> vos)throws Exception;

    /**
     * 判断当前登录用户是否为中心审核人员
     * @return 判断当前登录用户是否为中心审核人员
     */
    Boolean isExpertiseCentercPeople();
}
