<?xml version="1.0" encoding="UTF-8"?>

<!--
 ~ Copyright (c) 2010-2011 Sonatype, Inc.
 ~ All rights reserved. This program and the accompanying materials
 ~ are made available under the terms of the Eclipse Public License v1.0
 ~ which accompanies this distribution, and is available at
 ~   http://www.eclipse.org/legal/epl-v10.html
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.sonatype.forge</groupId>
    <artifactId>forge-parent</artifactId>
    <version>10</version>
  </parent>

  <groupId>org.sonatype.aether</groupId>
  <artifactId>aether</artifactId>
  <version>1.13.1</version>
  <packaging>pom</packaging>

  <name>Aether</name>
  <description>
    The parent and aggregator for the repository system.
  </description>
  <url>http://aether.sonatype.org/</url>
  <inceptionYear>2010</inceptionYear>

  <mailingLists>
    <mailingList>
      <name>Aether Developers List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <!-- archive>?</archive -->
    </mailingList>
    <mailingList>
      <name>Aether Users List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <!-- archive>?</archive -->
    </mailingList>
    <mailingList>
      <name>Aether Commits List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
    </mailingList>
  </mailingLists>

  <scm>
    <connection>scm:git:**************:sonatype/sonatype-aether.git</connection>
    <developerConnection>scm:git:**************:sonatype/sonatype-aether.git</developerConnection>
    <url>http://github.com/sonatype/sonatype-aether</url>
  </scm>

  <issueManagement>
    <system>jira</system>
    <url>https://issues.sonatype.org/browse/AETHER</url>
  </issueManagement>

  <ciManagement>
    <system>Hudson</system>
    <url>https://builds.sonatype.org/job/aether/</url>
  </ciManagement>

  <licenses>
    <license>
      <name>Eclipse Public License, Version 1.0</name>
      <url>http://www.eclipse.org/legal/epl-v10.html</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <modules>
    <module>aether-api</module>
    <module>aether-spi</module>
    <module>aether-util</module>
    <module>aether-impl</module>
    <module>aether-test-util</module>
    <module>aether-connector-file</module>
    <module>aether-connector-wagon</module>
    <module>aether-connector-asynchttpclient</module>
  </modules>

  <properties>
    <surefire.redirectTestOutputToFile>true</surefire.redirectTestOutputToFile>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.sonatype.aether</groupId>
        <artifactId>aether-api</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.sonatype.aether</groupId>
        <artifactId>aether-spi</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.sonatype.aether</groupId>
        <artifactId>aether-util</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.sonatype.aether</groupId>
        <artifactId>aether-impl</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.sonatype.aether</groupId>
        <artifactId>aether-connector-file</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.sonatype.aether</groupId>
        <artifactId>aether-connector-wagon</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.sonatype.aether</groupId>
        <artifactId>aether-connector-asynchttpclient</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.sonatype.aether</groupId>
        <artifactId>aether-test-util</artifactId>
        <version>${project.version}</version>
        <scope>test</scope>
      </dependency>

      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>4.8.2</version>
        <scope>test</scope>
      </dependency>

      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-component-annotations</artifactId>
        <version>1.5.5</version>
        <scope>provided</scope>
      </dependency>

      <dependency>
        <groupId>org.sonatype.sisu</groupId>
        <artifactId>sisu-inject-plexus</artifactId>
        <version>2.2.3</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>2.5</version>
          <configuration>
            <links>
              <link>http://download.oracle.com/javase/6/docs/api/</link>
            </links>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <version>2.1</version>
          <configuration>
            <autoVersionSubmodules>true</autoVersionSubmodules>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.9</version>
          <configuration>
            <argLine>-Xmx128m</argLine>
            <redirectTestOutputToFile>${surefire.redirectTestOutputToFile}</redirectTestOutputToFile>
            <systemPropertyVariables>
              <java.io.tmpdir>${project.build.directory}/surefire-tmp</java.io.tmpdir>
            </systemPropertyVariables>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>animal-sniffer-maven-plugin</artifactId>
          <version>1.7</version>
          <configuration>
            <signature>
              <groupId>org.codehaus.mojo.signature</groupId>
              <artifactId>java15</artifactId>
              <version>1.0</version>
            </signature>
          </configuration>
          <executions>
            <execution>
              <id>check-java-1.5-compat</id>
              <phase>process-classes</phase>
              <goals>
                <goal>check</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>clirr-maven-plugin</artifactId>
          <version>2.3</version>
          <executions>
            <execution>
              <id>check-api-compat</id>
              <phase>verify</phase>
              <goals>
                <goal>check-no-fork</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.codehaus.plexus</groupId>
          <artifactId>plexus-component-metadata</artifactId>
          <version>1.5.5</version>
          <executions>
            <execution>
              <id>generate-components-xml</id>
              <goals>
                <goal>generate-metadata</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>

  <profiles>
    <profile>
      <id>demo</id>
      <modules>
        <module>aether-demo</module>
      </modules>
    </profile>
    <profile>
      <id>release</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-assembly-plugin</artifactId>
            <dependencies>
              <dependency>
                <groupId>org.apache.apache.resources</groupId>
                <artifactId>apache-source-release-assembly-descriptor</artifactId>
                <version>1.0.2</version>
              </dependency>
            </dependencies>
            <executions>
              <execution>
                <id>attach-source-release-distro</id>
                <phase>package</phase>
                <goals>
                  <goal>single</goal>
                </goals>
                <configuration>
                  <runOnlyAtExecutionRoot>true</runOnlyAtExecutionRoot>
                  <descriptorRefs>
                    <descriptorRef>source-release</descriptorRef>
                  </descriptorRefs>
                  <tarLongFileFormat>gnu</tarLongFileFormat>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>snapshot-sources</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-sources</id>
                <goals>
                  <goal>jar-no-fork</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
