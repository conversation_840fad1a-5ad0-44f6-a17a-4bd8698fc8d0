package com.chinasie.orion.domain.dto;

/**
 * @author: yk
 * @date: 2023/5/25 16:33
 * @description: 项目计划关联合同信息
 */

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

/**
 * ProjectSchemeContract Entity对象
 *
 * <AUTHOR>
 * @since 2023-05-25 16:26:04
 */
@ApiModel(value = "ProjectSchemeContractDTO对象", description = "项目计划关联合同")
@Data
public class ProjectSchemeContractDTO extends ObjectDTO implements Serializable{

    /**
     * 项目计划id
     */
    @ApiModelProperty(value = "项目计划id")
    private String projectSchemeId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractId;

}
