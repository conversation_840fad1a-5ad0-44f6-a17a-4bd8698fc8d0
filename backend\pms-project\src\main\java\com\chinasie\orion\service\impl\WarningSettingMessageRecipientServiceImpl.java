package com.chinasie.orion.service.impl;


import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.domain.dto.WarningSettingMessageRecipientDTO;
import com.chinasie.orion.domain.entity.WarningSettingMessageRecipient;
import com.chinasie.orion.domain.vo.WarningSettingMessageRecipientVO;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.WarningSettingMessageRecipientRepository;
import com.chinasie.orion.service.WarningSettingMessageRecipientService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.String;
import java.util.List;

/**
 * <p>
 * WarningSettingMessageRecipient 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-17 17:01:13
 */
@Service
public class WarningSettingMessageRecipientServiceImpl extends OrionBaseServiceImpl<WarningSettingMessageRecipientRepository, WarningSettingMessageRecipient>  implements WarningSettingMessageRecipientService {

    @Autowired
    private WarningSettingMessageRecipientRepository warningSettingMessageRecipientRepository;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    public  WarningSettingMessageRecipientVO detail(String id) throws Exception {
        WarningSettingMessageRecipient warningSettingMessageRecipient = warningSettingMessageRecipientRepository.selectById(id);
        WarningSettingMessageRecipientVO result = BeanCopyUtils.convertTo(warningSettingMessageRecipient, WarningSettingMessageRecipientVO::new);
        return result;
    }

    /**
     *  新增
     *
     * * @param warningSettingMessageRecipientDTO
     */
    @Override
    public  WarningSettingMessageRecipientVO create(WarningSettingMessageRecipientDTO warningSettingMessageRecipientDTO) throws Exception {
        WarningSettingMessageRecipient warningSettingMessageRecipient =BeanCopyUtils.convertTo(warningSettingMessageRecipientDTO,WarningSettingMessageRecipient::new);
        int insert = warningSettingMessageRecipientRepository.insert(warningSettingMessageRecipient);
        WarningSettingMessageRecipientVO rsp = BeanCopyUtils.convertTo(warningSettingMessageRecipient,WarningSettingMessageRecipientVO::new);
        return rsp;
    }

    /**
     *  编辑
     *
     * * @param warningSettingMessageRecipientDTO
     */
    @Override
    public Boolean edit(WarningSettingMessageRecipientDTO warningSettingMessageRecipientDTO) throws Exception {
        WarningSettingMessageRecipient warningSettingMessageRecipient =BeanCopyUtils.convertTo(warningSettingMessageRecipientDTO,WarningSettingMessageRecipient::new);
        int update =  warningSettingMessageRecipientRepository.updateById(warningSettingMessageRecipient);
        return SqlHelper.retBool(update);
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        int delete = warningSettingMessageRecipientRepository.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


}
