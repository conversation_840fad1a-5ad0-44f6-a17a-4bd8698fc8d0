package com.chinasie.orion.service.Impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.LaborCostAcceptanceStatusEnum;
import com.chinasie.orion.domain.dto.LaborCostAcceptanceAssessmentStandardDTO;
import com.chinasie.orion.domain.entity.ContractAssessmentStandard;
import com.chinasie.orion.domain.entity.LaborCostAcceptance;
import com.chinasie.orion.domain.entity.LaborCostAcceptanceAssessmentStandard;
import com.chinasie.orion.domain.vo.ContractAssessmentStandardVO;
import com.chinasie.orion.domain.vo.LaborCostAcceptanceAssessmentStandardVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.SPMErrorCode;
import com.chinasie.orion.exception.SPMException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.ContractAssessmentStandardMapper;
import com.chinasie.orion.repository.LaborCostAcceptanceAssessmentStandardMapper;
import com.chinasie.orion.repository.LaborCostAcceptanceMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.LaborCostAcceptanceAssessmentStandardService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;




/**
 * <p>
 * LaborCostAcceptanceAssessmentStandard 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-29 19:13:30
 */
@Service
@Slf4j
public class LaborCostAcceptanceAssessmentStandardServiceImpl extends  OrionBaseServiceImpl<LaborCostAcceptanceAssessmentStandardMapper, LaborCostAcceptanceAssessmentStandard>   implements LaborCostAcceptanceAssessmentStandardService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private ContractAssessmentStandardMapper contractAssessmentStandardMapper;

    @Autowired
    private LaborCostAcceptanceMapper laborCostAcceptanceMapper;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public LaborCostAcceptanceAssessmentStandardVO detail(String id, String pageCode) throws Exception {
        LaborCostAcceptanceAssessmentStandard laborCostAcceptanceAssessmentStandard =this.getById(id);
        LaborCostAcceptanceAssessmentStandardVO result = BeanCopyUtils.convertTo(laborCostAcceptanceAssessmentStandard,LaborCostAcceptanceAssessmentStandardVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }


    @Override
    public List<ContractAssessmentStandardVO> byAcceptanceId(String acceptanceId) throws Exception {
        LambdaQueryWrapperX<LaborCostAcceptanceAssessmentStandard> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(LaborCostAcceptanceAssessmentStandard :: getAcceptanceId, acceptanceId);
        List<LaborCostAcceptanceAssessmentStandard> list = this.list(lambdaQueryWrapperX);
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        List<String> assessmentAtandardIds = list.stream().map(LaborCostAcceptanceAssessmentStandard :: getAssessmentAtandardId).collect(Collectors.toList());
        List<ContractAssessmentStandard> assessmentStandards =  contractAssessmentStandardMapper.selectBatchIds(assessmentAtandardIds);
        List<ContractAssessmentStandardVO> vos = BeanCopyUtils.convertListTo(assessmentStandards, ContractAssessmentStandardVO::new);
        return vos;
    }

    /**
     *  新增
     *
     * * @param laborCostAcceptanceAssessmentStandardDTO
     */
    @Override
    public  String create(LaborCostAcceptanceAssessmentStandardDTO laborCostAcceptanceAssessmentStandardDTO) throws Exception {
        LaborCostAcceptanceAssessmentStandard laborCostAcceptanceAssessmentStandard =BeanCopyUtils.convertTo(laborCostAcceptanceAssessmentStandardDTO,LaborCostAcceptanceAssessmentStandard::new);
        this.save(laborCostAcceptanceAssessmentStandard);

        String rsp=laborCostAcceptanceAssessmentStandard.getId();



        return rsp;
    }


    @Override
    public boolean insertByAcceptanceId(List<String> ids, String acceptanceId) throws Exception {
        if(CollectionUtils.isEmpty(ids)){
            throw new SPMException(SPMErrorCode.PMS_ERROR_ID_NULL, "审核标准id不能为空!");
        }
        LaborCostAcceptance laborCostAcceptance = laborCostAcceptanceMapper.selectById(acceptanceId);
        if(laborCostAcceptance == null){
            throw new SPMException(SPMErrorCode.PMS_ERROR_ID_NULL, "人力成本验收单未找到!");
        }

        Integer status = laborCostAcceptance.getStatus();
        if(LaborCostAcceptanceStatusEnum.UNSUBMIT.getKey().equals(status) || LaborCostAcceptanceStatusEnum.COMPLATE.getKey().equals(status)){
            throw new SPMException(SPMErrorCode.PMS_ERROR_ID_NULL, "人力成本验收单当前状态不能添加合同考核条款!");
        }

        LambdaQueryWrapperX<LaborCostAcceptanceAssessmentStandard> standardLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        standardLambdaQueryWrapperX.eq(LaborCostAcceptanceAssessmentStandard :: getAcceptanceId, acceptanceId);
        standardLambdaQueryWrapperX.in(LaborCostAcceptanceAssessmentStandard :: getAssessmentAtandardId,ids);
        List<LaborCostAcceptanceAssessmentStandard> list =  this.list(standardLambdaQueryWrapperX);
        if(!CollectionUtils.isEmpty(list)){
            List<String> existIds = list.stream().map(LaborCostAcceptanceAssessmentStandard :: getAssessmentAtandardId).collect(Collectors.toList());
            ids.removeAll(existIds);
        }
        List<LaborCostAcceptanceAssessmentStandard> insertList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(ids)){
            ids.forEach(item ->{
                LaborCostAcceptanceAssessmentStandard standard = new LaborCostAcceptanceAssessmentStandard();
                standard.setAcceptanceId(acceptanceId);
                standard.setAssessmentAtandardId(item);
                insertList.add(standard);
            });

            this.saveBatch(insertList);
        }
        return true;
    }

    @Override
    public boolean removeByAcceptanceId(List<String> ids, String acceptanceId) throws Exception {
        if(CollectionUtils.isEmpty(ids)){
            throw new SPMException(SPMErrorCode.PMS_ERROR_ID_NULL, "审核标准id不能为空!");
        }
        LaborCostAcceptance laborCostAcceptance = laborCostAcceptanceMapper.selectById(acceptanceId);
        if(laborCostAcceptance == null){
            throw new SPMException(SPMErrorCode.PMS_ERROR_ID_NULL, "人力成本验收单未找到!");
        }

        Integer status = laborCostAcceptance.getStatus();
        if(!(LaborCostAcceptanceStatusEnum.UNSUBMIT.getKey().equals(status) || LaborCostAcceptanceStatusEnum.COMPLATE.getKey().equals(status))){
            throw new SPMException(SPMErrorCode.PMS_ERROR_ID_NULL, "人力成本验收单当前状态不能移除合同考核条款!");
        }
        LambdaQueryWrapperX<LaborCostAcceptanceAssessmentStandard> acceptanceLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        acceptanceLambdaQueryWrapperX.in(LaborCostAcceptanceAssessmentStandard :: getAssessmentAtandardId, ids);
        acceptanceLambdaQueryWrapperX.eq(LaborCostAcceptanceAssessmentStandard :: getAcceptanceId,acceptanceId);
        this.remove(acceptanceLambdaQueryWrapperX);
        return true;
    }

    /**
     *  编辑
     *
     * * @param laborCostAcceptanceAssessmentStandardDTO
     */
    @Override
    public Boolean edit(LaborCostAcceptanceAssessmentStandardDTO laborCostAcceptanceAssessmentStandardDTO) throws Exception {
        LaborCostAcceptanceAssessmentStandard laborCostAcceptanceAssessmentStandard =BeanCopyUtils.convertTo(laborCostAcceptanceAssessmentStandardDTO,LaborCostAcceptanceAssessmentStandard::new);

        this.updateById(laborCostAcceptanceAssessmentStandard);

        String rsp=laborCostAcceptanceAssessmentStandard.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<LaborCostAcceptanceAssessmentStandardVO> pages( Page<LaborCostAcceptanceAssessmentStandardDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<LaborCostAcceptanceAssessmentStandard> condition = new LambdaQueryWrapperX<>( LaborCostAcceptanceAssessmentStandard. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(LaborCostAcceptanceAssessmentStandard::getCreateTime);


        Page<LaborCostAcceptanceAssessmentStandard> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), LaborCostAcceptanceAssessmentStandard::new));

        PageResult<LaborCostAcceptanceAssessmentStandard> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<LaborCostAcceptanceAssessmentStandardVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<LaborCostAcceptanceAssessmentStandardVO> vos = BeanCopyUtils.convertListTo(page.getContent(), LaborCostAcceptanceAssessmentStandardVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "验收人力成本与审核标准关联导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", LaborCostAcceptanceAssessmentStandardDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        LaborCostAcceptanceAssessmentStandardExcelListener excelReadListener = new LaborCostAcceptanceAssessmentStandardExcelListener();
        EasyExcel.read(inputStream,LaborCostAcceptanceAssessmentStandardDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<LaborCostAcceptanceAssessmentStandardDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("验收人力成本与审核标准关联导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<LaborCostAcceptanceAssessmentStandard> laborCostAcceptanceAssessmentStandardes =BeanCopyUtils.convertListTo(dtoS,LaborCostAcceptanceAssessmentStandard::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::LaborCostAcceptanceAssessmentStandard-import::id", importId, laborCostAcceptanceAssessmentStandardes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<LaborCostAcceptanceAssessmentStandard> laborCostAcceptanceAssessmentStandardes = (List<LaborCostAcceptanceAssessmentStandard>) orionJ2CacheService.get("pmsx::LaborCostAcceptanceAssessmentStandard-import::id", importId);
        log.info("验收人力成本与审核标准关联导入的入库数据={}", JSONUtil.toJsonStr(laborCostAcceptanceAssessmentStandardes));

        this.saveBatch(laborCostAcceptanceAssessmentStandardes);
        orionJ2CacheService.delete("pmsx::LaborCostAcceptanceAssessmentStandard-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::LaborCostAcceptanceAssessmentStandard-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<LaborCostAcceptanceAssessmentStandard> condition = new LambdaQueryWrapperX<>( LaborCostAcceptanceAssessmentStandard. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(LaborCostAcceptanceAssessmentStandard::getCreateTime);
        List<LaborCostAcceptanceAssessmentStandard> laborCostAcceptanceAssessmentStandardes =   this.list(condition);

        List<LaborCostAcceptanceAssessmentStandardDTO> dtos = BeanCopyUtils.convertListTo(laborCostAcceptanceAssessmentStandardes, LaborCostAcceptanceAssessmentStandardDTO::new);

        String fileName = "验收人力成本与审核标准关联数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", LaborCostAcceptanceAssessmentStandardDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<LaborCostAcceptanceAssessmentStandardVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class LaborCostAcceptanceAssessmentStandardExcelListener extends AnalysisEventListener<LaborCostAcceptanceAssessmentStandardDTO> {

        private final List<LaborCostAcceptanceAssessmentStandardDTO> data = new ArrayList<>();

        @Override
        public void invoke(LaborCostAcceptanceAssessmentStandardDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<LaborCostAcceptanceAssessmentStandardDTO> getData() {
            return data;
        }
    }


}
