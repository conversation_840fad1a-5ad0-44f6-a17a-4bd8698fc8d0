<template>
  <div
    ref="accountSummaryRef"
    class="account-summary"
  >
    <div class="account-summary-title">
      <span class="title-label">科目汇总</span>
      <span class="action-btn ">金额占比</span>
    </div>
    <EstimateCardList
      ref="estimateCardListRef"
      :formId="formId"
    />
    <div class="account-summary-table">
      <OrionTable
        ref="tableRef"
        :options="tableObjOptions"
        @initData="initData"
        @selectionChange="selectionChange"
      >
        <template
          #amount="{record}"
        >
          <AInputNumber
            v-if="!Array.isArray(record.children)||record.children.length===0"
            v-model:value="record.amount"
            :disabled="props.status!==101"
            @change="changeValue"
          />
          <!--          <span>{{ formatMoney(record.amount) }}</span>-->
        </template>
      </OrionTable>
    </div>
  </div>
</template>
<script setup lang="ts">
import {
  h, inject, Ref, ref, watch,
} from 'vue';
import { isPower, OrionTable } from 'lyra-component-vue3';
import { InputNumber as AInputNumber } from 'ant-design-vue';
import EstimateCardList from '../components/EstimateCardList.vue';
import Api from '/@/api';
import { formatMoney } from '../../index';
import ShowCalculateAmount from '../components/ShowCalculateAmount.vue';

const props = withDefaults(defineProps<{
    formId:string,
    status:number|string
}>(), {
  formId: '',
  status: 101,
});
const emit = defineEmits(['selectChange']);
const powerData = inject('powerData', []);
const accountSummaryRef = ref();
const estimateCardListRef = ref();
const selectRowKeys:Ref<string[]> = ref([]);
const tableRef = ref();
const tableObjOptions = ref({
  rowSelection: {},
  isTableHeader: false,
  showIndexColumn: false,
  expandIconColumnIndex: 3,
  pagination: false,
  api: (params) => new Api('/pms').fetch('', `projectApprovalEstimate/subject/tree/list?approvalId=${props.formId}`, 'POST'),
  columns: [
    {
      title: '科目序号',
      dataIndex: 'index',
      align: 'left',
      width: 100,
    },
    {
      title: '概算科目',
      align: 'left',
      dataIndex: 'name',
      minWidth: 200,
    },
    {
      title: '自动计算金额',
      dataIndex: 'calculateAmount',
      ellipsis: true,
      align: 'left',
      width: 150,
      customRender: ({ text, record }) => h(ShowCalculateAmount, {
        record,
      }),
    },
    {
      title: '概算金额',
      dataIndex: 'amount',
      align: 'left',
      width: 200,
      slots: { customRender: 'amount' },
      customRender: ({ text, record }) => {
        if (Array.isArray(record.children)) {
          return `总计：${formatMoney(record.amount)}`;
        }
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 200,
      align: 'left',
      fixed: 'right',
      slots: { customRender: 'action' },
    },
  ],

  actions: [
    {
      text: '填入',
      isShow: (record) => (!Array.isArray(record.children) || record.children.length === 0) && isPower('XMLXXQ_container_01_button_06', powerData),
      onClick(record) {
        record.amount = record.calculateAmount;
        let dataSource = tableRef.value.getDataSource();
        initSumData(dataSource);
        tableRef.value.setTableData(dataSource);
      },
    },
    {
      text: '计算',
      isShow: (record) => !(record.calculateAmount && !isNaN(record.calculateAmount)) && (!Array.isArray(record.children) || record.children.length === 0) && isPower('XMLXXQ_container_01_button_07', powerData),
      onClick(record) {
        calculateRecord(record);
      },
    },
    {
      text: '重新计算',
      isShow: (record) => record.calculateAmount && !isNaN(record.calculateAmount) && (!Array.isArray(record.children) || record.children.length === 0) && isPower('XMLXXQ_container_01_button_07', powerData),
      onClick(record) {
        calculateRecord(record);
      },
    },
  ],
});
function calculateRecord(record) {
  let tableData = tableRef.value.getDataSource();
  let indexData = [];
  initTableData(tableData, indexData);
  new Api('/pms').fetch(indexData, `projectApprovalEstimate/subject/calculate?id=${record.id}`, 'POST').then((res) => {
    record.amount = res;
    record.calculateAmount = res;
    let dataSource = tableRef.value.getDataSource();
    initSumData(dataSource);
    tableRef.value.setTableData(dataSource);
  });
}
function initTableData(data, indexData) {
  data.forEach((item, index) => {
    indexData.push({
      amount: item.amount,
      formula: item.formula,
      id: item.id,
      number: item.number,
      parentId: item.parentId,
    });
    if (Array.isArray(item.children) && item.children.length > 0) {
      initTableData(item.children, indexData);
    }
  });
}
function initData(data:any[], indexColumns = '') {
  data.forEach((item, index) => {
    if (indexColumns) {
      item.index = `${indexColumns}.${index + 1}`;
    } else {
      item.index = index + 1;
    }
  });
}
function selectionChange(data) {
  selectRowKeys.value = data.keys;
}
watch(() => selectRowKeys.value, (val) => {
  emit('selectChange', val);
});
let initDataTimeOut = null;
function changeValue(val) {
  if (initDataTimeOut) {
    clearTimeout(initDataTimeOut);
  }
  initDataTimeOut = setTimeout(() => {
    let dataSource = tableRef.value.getDataSource();
    initSumData(dataSource);
    tableRef.value.setTableData(dataSource);
  }, 300);
}
function initSumData(dataSource) {
  dataSource.forEach((item) => {
    if (Array.isArray(item.children)) {
      initSumData(item.children);
      item.amount = item.children.reduce((acc, item1) => {
        let amount = !isNaN(item1.amount) ? item1.amount : 0;
        return acc + amount;
      }, 0);
    }
  });
}
function update() {
  tableRef.value.reload();
}
function getTableData() {
  return tableRef.value.getDataSource();
}
function setTableData(data) {
  tableRef.value.setTableData(data);
}
function getIndexData() {
  let tableData = tableRef.value.getDataSource();
  let indexData = [];
  initTableData(tableData, indexData);
  return indexData;
}
defineExpose({
  update,
  getTableData,
  setTableData,
  getIndexData,
});
</script>
<style scoped lang="less">
.account-summary{
  margin-top: 30px;
  .account-summary-title{
    border-bottom: 1px solid #e9e9e9;
    padding-bottom: 15px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title-label{
      font-weight: 650;
      font-style: normal;
      font-size: 16px;
    }
    .action-btn{
      font-size: 12px;
    }
  }
  .account-summary-card-list{
    padding: 20px 10px;
    display: flex;
    gap: 20px 50px;
    flex-wrap: wrap;

    .card-item{
      border: 1px solid #e9e9e9;
      height: 154px;
      width: 270px;
      padding:20px;
      :deep(.left-label){
        font-weight: 400;
        font-style: normal;
        color: rgba(0, 0, 0, 0.***************);
      }
      :deep(.left-value){
        font-weight: 400;
        font-style: normal;
        color: rgba(0, 0, 0, 0.***************);
        text-align: left;
        line-height: 38px;
        font-size: 30px;

        span{
          font-size: 16px;
        }
      }
      :deep(.left-money){
        font-size: 20px;
        font-weight: 400;
        font-style: normal;
        color: rgba(0, 0, 0, 0.***************);
        span{
          font-size: 16px;
        }
      }
      .left-value-parent{
        display: flex;
        justify-content: space-between;
        align-items: baseline;
        .left-value-right{
          font-weight: 400;
          font-style: normal;
          color: rgba(0, 0, 0, 0.***************);
          font-size: 16px;
        }
      }
      .card-item-img{
        background: url('../../img/u9416.png') no-repeat right bottom;
        width: 100%;
        height: 40px;
        margin-top: 16px;
      }
    }
  }
  .account-summary-table{
    overflow: hidden;
    height:350px;
    :deep(.ant-basic-table){
      padding-left: 0 !important;
      padding-right: 0 !important;
    }
  }
}
</style>