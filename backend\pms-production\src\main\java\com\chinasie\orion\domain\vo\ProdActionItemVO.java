package com.chinasie.orion.domain.vo;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;

import java.util.List;
/**
 * ProdActionItem VO对象
 *
 * <AUTHOR>
 * @since 2024-10-24 10:28:59
 */
@ApiModel(value = "ProdActionItemVO对象", description = "生产大修行动项")
@Data
public class ProdActionItemVO extends  ObjectVO   implements Serializable{

            /**
         * 大修轮次
         */
        @ApiModelProperty(value = "大修轮次")
        private String repairRound;
        @ApiModelProperty(value = "大修轮次Id")
        private String repairId;


        /**
         * 责任人ID拼接
         */
        @ApiModelProperty(value = "责任人ID拼接")
        private String rspUserIds;


        /**
         * 责任人名称拼接
         */
        @ApiModelProperty(value = "责任人名称拼接")
        private String rspUserNames;


        /**
         * 完成时限
         */
        @ApiModelProperty(value = "完成时限")
        @DateTimeFormat(value = "yyyy-MM-dd")
        @JsonFormat(pattern="yyyy-MM-dd")
        private Date finishDeadline;


        /**
         * 维度字典
         */
        @ApiModelProperty(value = "维度字典")
        private String dimensionDict;


        /**
         * 验证人ID
         */
        @ApiModelProperty(value = "验证人ID")
        private String verifierId;


        /**
         * 验证人名称
         */
        @ApiModelProperty(value = "验证人名称")
        private String verifierName;


        /**
         * 问题描述
         */
        @ApiModelProperty(value = "问题描述")
        private String problemDesc;

        @ApiModelProperty(value = "反馈信息")
        private String feedback;

        private String updateSolutions;

        @ApiModelProperty(value = "子集数据列表")
        private List<ProdActionItemVO> childList;

        /**
         * 责任人ID拼接
         */
        @ApiModelProperty(value = "责任部门ID拼接")
        private String rspDeptIds;

        /**
         * 责任人名称拼接
         */
        @ApiModelProperty(value = "责任部门名称拼接")
        private String rspDeptNames;

        /**
         * 维度字典
         */
        @ApiModelProperty(value = "维度字典名称")
        private String dimensionDictName;
        @ApiModelProperty(value = "是否编辑反馈信息")
        private Boolean editFeedback=Boolean.FALSE;

        @ApiModelProperty(value = "是否编辑发起流程")
        private Boolean editUpdateFlow=Boolean.FALSE;

        @ApiModelProperty(value = "外层状态")
        private String statusName;

        /**
         * 附件
         */
        @ApiModelProperty(value = "附件")
        private List<FileVO> fileList;
}
