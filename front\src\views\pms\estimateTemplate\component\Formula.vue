<template>
  <div class="formula-page">
    <div class="formula">
      <div class="formula-top">
        <span class="result">计算结果=</span>
        <span
          class="preview"
          @click="clearValue"
        >清除</span>
      </div>
      <div class="formula-content">
        {{ formulaValueShow }}
      </div>
      <div class="formula-bottom">
        <div class="formula-bottom-left">
          <template
            v-for="item in reckoner"
            :key="item"
          >
            <div
              class="formula-item"
              :class="{'formula-item1':[ '≠',','].includes(item)}"
              @click="changeValue({value: item, name: item})"
            >
              {{ item }}
            </div>
          </template>
        </div>
        <div class="formula-bottom-content">
          <BasicScrollbar>
            <ATree
              v-model:expandedKeys="expandedKeys"
              v-model:selectedKeys="selectedTreeKeys"
              :tree-data="formulaVariable"
              :field-names="{title:'name',key:'id'}"
              @select="selectChange"
            />
          </BasicScrollbar>
        </div>

        <div class="formula-bottom-right">
          <BasicScrollbar>
            <div class="formula-bottom-list">
              <template
                v-for="item in formulaList"
                :key="item.value"
              >
                <span
                  class="formula-span"
                  @click="changeValue(item)"
                >{{ item.name }}</span>
              </template>
            </div>
          </BasicScrollbar>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { BasicButton, BasicScrollbar } from 'lyra-component-vue3';
import {
  ref, Ref, computed, onMounted,
} from 'vue';
import Api from '/@/api';
import { Tree as ATree } from 'ant-design-vue';

const props = withDefaults(defineProps<{
    estimateTemplateId:string
}>(), {
  estimateTemplateId: '',
});
const reckoner:Ref<string[]> = ref([
  '1',
  '2',
  '3',
  '+',
  '4',
  '5',
  '6',
  '-',
  '7',
  '8',
  '9',
  '*',
  '.',
  '0',
  '=',
  '/',
  '(',
  ')',
  '<',
  '>',
  '≠',
  ',',
]);
const formulaValue:Ref<string[]> = ref([]);
const formulaCode:Ref<string[]> = ref([]);
const formulaValueShow = computed(() => formulaValue.value.join(''));
const formulaList:Ref<Record<any, any>[]> = ref([]);
const formulaVariable:Ref<Record<any, any>[]> = ref([]);
const selectedTreeKeys:Ref<string[]> = ref([]);
const expandedKeys:Ref<string[]> = ref([]);
function changeValue(item) {
  formulaValue.value.push(item.name);
  formulaCode.value.push(item.value);
}
function clearValue() {
  formulaValue.value = [];
  formulaCode.value = [];
}

defineExpose({
  getFormValue,
});
function getFormValue() {
  return {
    formulaValue: formulaValue.value,
    formulaCode: formulaCode.value,
  };
}
onMounted(async () => {
  formulaList.value = await getFormulaList();
  formulaVariable.value = await getFormulaVariable();
  getExpandedKeys(formulaVariable.value);
});
function getFormulaList() {
  return new Api('/pms').fetch('', 'projectApprovalEstimateTemplateExpenseSubject/function/list', 'GET');
}
function getFormulaVariable() {
  return new Api('/pms').fetch('', `projectApprovalEstimateTemplateExpenseSubject/formulaVariable/list?estimateTemplateId=${props.estimateTemplateId}`, 'GET');
}
function getExpandedKeys(treeData) {
  treeData.forEach((item) => {
    if (Array.isArray(item.children) && item.children.length > 0) {
      expandedKeys.value.push(item.id);
      getExpandedKeys(item.children);
    }
  });
}
function selectChange(selectedKeys, e:{selected: boolean, selectedNodes, node, event}) {
  if (!e.selected) {
    selectedTreeKeys.value = [e.node.id];
  } else {
    formulaValue.value.push(e.node.name);
    formulaCode.value.push(e.node.number);
  }
}
</script>
<style lang="less" scoped>
.formula-page{
  padding: 10px 20px;
}
.formula{
  border: 1px solid #cccccc;
}
.formula-top{
  display: flex;
  background: #e1e5e9;
  justify-content: space-between;
  padding: 3px 5px;
  align-items: center;
  .preview{
    border: 1px solid #cccccc;
    background: #ffffff;
    padding: 3px 6px;
    border-radius: 5px;
    font-size: 12px;
    cursor: pointer;
  }
}
.formula-content{
  height: 50px;
  padding: 0 10px;
}
.formula-bottom{
  padding: 0 10px;
  border-top: 1px solid #cccccc;
  display: flex;
  gap: 5px;
  height: 240px;
  align-items: center;
}
.formula-bottom-left{
  display: flex;
  width: 200px;
  flex-wrap: wrap;
  gap: 5px;
  .formula-item{
    width: 46px;
    cursor: pointer;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background: #eceaea70;
    border-radius: 4px;
    &:active{
      background: #2a71ca;
      color: #ffffff;
    }
  }
  .formula-item1{
    width: 36px;
  }
}
.formula-bottom-content{
  height: 100%;
  width: 250px;
  border-left: 1px solid #cccccc;
  padding: 10px 0 10px 10px;
}

.formula-bottom-right{
  height: 100%;
  width: 250px;
  border-left: 1px solid #cccccc;
  padding: 10px 0 10px 10px;
  .formula-bottom-list{
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    .formula-span{
      font-size: 14px;
      padding: 5px 10px;
      cursor: pointer;
      text-align: center;
      background: #eceaea70;
      border-radius: 4px;
      &:active{
        background: #2a71ca;
        color: #ffffff;
      }
    }
  }
}
</style>
