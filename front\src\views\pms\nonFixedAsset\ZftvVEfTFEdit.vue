<script setup lang="ts">
import { BasicForm, useForm } from 'lyra-component-vue3';
import { onMounted, Ref, ref } from 'vue';
import Api from '/@/api';

const props = defineProps<{
  record: Record<string, any> | null,
}>();

const schemas = [
  {
    field: 'name',
    label: '资产名称',
    colProps: { span: 24 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'barcode',
    label: '资产条码',
    colProps: { span: 24 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    // rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'spModel',
    label: '规格型号',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'isNeedVerification',
    label: '是否需要检定',
    colProps: { span: 12 },
    componentProps: {
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
    rules: [
      {
        required: true,
        type: 'boolean',
      },
    ],
    component: 'Select',
  },
];

const [register, { validate, setFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  props?.record?.id && getFormData();
});

const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/non-fixed-assets').fetch('', props?.record?.id, 'GET');
    await setFieldsValue(result);
  } finally {
    loading.value = false;
  }
}

defineExpose({
  async onSubmit() {
    const formValues = await validate();
    const params = {
      id: props?.record?.id,
      ...formValues,

    };

    return new Promise((resolve, reject) => {
      new Api('/pms/non-fixed-assets').fetch(params, props?.record?.id ? 'edit' : 'add', props?.record?.id ? 'PUT' : 'POST').then(() => {
        resolve('');
      }).catch((err) => {
        reject(err);
      });
    });
  },
});
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">

</style>
