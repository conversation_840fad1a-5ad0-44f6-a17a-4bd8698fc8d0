<template>
  <div>
    <OrionTable
      ref="tableRef"
      :options="baseTableOption"
      @selectionChange="selectionChange"
    >
      <template #toolbarLeft>
        <BasicButton
          type="primary"
          icon="sie-icon-tianjiaxinzeng"
          :loading="disabledBtn"
          @click="openCheckProcureModal(true, {});"
        >
          添加订单
        </BasicButton>
        <!--        <BasicButton-->
        <!--          icon="sie-icon-del"-->
        <!--          :disabled="selectRows.length===0"-->
        <!--          @click="handleBatchDel"-->
        <!--        >-->
        <!--          删除-->
        <!--        </BasicButton>-->
      </template>
    </OrionTable>
    <!--关联采购订单-->
    <CheckProcureModal
      :onCheckProjectCallback="checkProjectCallback"
      @register="registerCheckProcure"
    />
  </div>
</template>
<script setup lang="ts">
import {
  ref, onMounted, h, Ref, unref, computed, inject, watch,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {
  BasicButton, isPower, OrionTable, useModal, DataStatusTag,
} from 'lyra-component-vue3';
import { Modal, message } from 'ant-design-vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import CheckProcureModal from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/contractManage/components/AddContract/components/CheckProcureModal/Index.vue';
import { deleteContractPayNodeConfirm } from '/@/views/pms/projectLaborer/projectLab/api';
const [registerCheckProcure, { openModal: openCheckProcureModal }] = useModal();
defineExpose({ getValues });
const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});
const router = useRouter();
const route = useRoute();
const tableRef = ref(null);
const selectRows = ref([]);
const disabledBtn = ref(false);
const powerData:Ref<any[]> = ref([]);
// const detail:any = inject('allData', {});
const dataId: Ref<string> = ref(route.query.id as string);
const baseTableOption = {
  api: (params) => {
    if (props?.id) {
      return new Api('/pms/projectPurchaseOrderInfo/getPurchaseOrderList').fetch({ id: props.id }, '', 'POST');
    }
    return [];
  },
  rowSelection: {},
  showToolButton: false,
  pagination: false,
  showSmallSearch: false,
  showTableSetting: false,
  columns: [
    {
      title: '订单编号',
      dataIndex: 'number',
      width: 150,
    },
    {
      title: '订单名称',
      dataIndex: 'name',
      minWidth: 120,
      customRender({
        record, text,
      }) {
        return h('span', {
          class: 'action-btn',
          onClick() {
            router.push({
              name: 'PlanCurrentTaskDetails',
              params: {
                id: record.id,
              },
            });
          },
        }, text);
      },
    },
    {
      title: '订单状态',
      dataIndex: 'dataStatus',
      width: 100,
      customRender({ text }) {
        return text ? h(DataStatusTag, { statusData: text }) : '';
      },
    },
    {
      title: '订单行数',
      dataIndex: 'lineCount',
      width: 150,
    },
    {
      title: '采购类型',
      dataIndex: 'purchaseTypeName',
      minWidth: 150,
    },
    {
      title: '物资服务信息',
      dataIndex: 'planSourceName',
      minWidth: 150,
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 100,
      slots: { customRender: 'action' },
    },
  ],
  actions: [
    {
      text: '删除',
      isShow: (record: Record<string, any>) => isPower('PMS_XMXQ_container_06_04_02_button_03', record.rdAuthList),
      modal: (record) => new Promise((resolve, reject) => {
        const DataSource = tableRef.value?.getDataSource().filter((item) => item.id !== record.id);
        tableRef.value?.setTableData(DataSource);
        resolve('');
      }),
    },
  ],
};

function getValues() {
  return tableRef.value?.getDataSource().map((item) => item.id);
}

function selectionChange({ rows }) {
  selectRows.value = rows;
}
function handleBatchDel() {
  Modal.confirm({
    title: '删除提示',
    content: '确认要删除已选择的数据？',
    onOk: () => batchDelete(selectRows.value.map((item) => item.id)),
  });
}
function batchDelete(ids) {
  return new Promise((resolve, reject) => {
    new Api('/plan/strategyPlan/goalManage/relateScheme').fetch(ids, unref(dataId), 'DELETE')
      .then(() => {
        upTableDate();
        resolve(true);
      })
      .catch(() => {
        reject();
      });
  });
}
function upTableDate() {
  tableRef.value?.reload();
}
// 选择信息计划
async function checkProjectCallback(data) {
  disabledBtn.value = true;
  const schemeIds = await data.map((item) => item);
  const tableVal = await tableRef.value?.getDataSource();
  const addData = schemeIds.filter((item) => !tableVal.some((tableItem) => tableItem.id === item.id));
  const newTableVal = tableVal.concat(addData);
  await tableRef.value?.setTableData(newTableVal);
  disabledBtn.value = false;
}

</script>
<style scoped lang="less">
</style>
