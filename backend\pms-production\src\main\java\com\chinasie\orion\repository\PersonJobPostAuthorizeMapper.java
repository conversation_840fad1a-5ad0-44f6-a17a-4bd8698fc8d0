package com.chinasie.orion.repository;
import com.chinasie.orion.domain.entity.PersonJobPostAuthorize;
import org.apache.ibatis.annotations.Mapper;

import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;


/**
 * <p>
 * PersonJobPostAuthorize Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-22 16:30:22
 */
@Mapper
public interface PersonJobPostAuthorizeMapper extends  OrionBaseMapper  <PersonJobPostAuthorize> {

    /**
     * 查询人员岗位授权临期数据
     * @return 结果
     */
    @Select("SELECT * FROM pmsx_person_job_post_authorize WHERE DATE(end_date) = DATE(DATE_ADD(CURRENT_DATE, INTERVAL 2 MONTH)) and logic_status = 1;")
    List<PersonJobPostAuthorize> positionAuthorize();
}

