package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * PojectPurchaseOrderDetail Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-06 09:20:41
 */
@TableName(value = "pms_project_purchase_order_detail")
@ApiModel(value = "ProjectPurchaseOrderDetail对象", description = "项目采购订单明细")
@Data
public class ProjectPurchaseOrderDetail extends ObjectEntity implements Serializable{

    /**
     * 来源单号
     */
    @ApiModelProperty(value = "来源单号")
    @TableField(value = "source_number" )
    private String sourceNumber;

    /**
     * 物资/服务计划编号
     */
    @ApiModelProperty(value = "物资/服务计划编号")
    @TableField(value = "plan_number" )
    private String planNumber;

    /**
     * 物资/服务编码
     */
    @ApiModelProperty(value = "物资/服务编码")
    @TableField(value = "goods_service_number" )
    private String goodsServiceNumber;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @TableField(value = "description" )
    private String description;

    /**
     * 规格型号
     */
    @ApiModelProperty(value = "规格型号")
    @TableField(value = "norms_model" )
    private String normsModel;

    /**
     * 计量单位
     */
    @ApiModelProperty(value = "计量单位")
    @TableField(value = "unit_code" )
    private String unitCode;

    /**
     * 采购数量
     */
    @ApiModelProperty(value = "采购数量")
    @TableField(value = "purchase_amount" )
    private BigDecimal purchaseAmount;

    /**
     * 需求日期
     */
    @ApiModelProperty(value = "需求日期")
    @TableField(value = "demand_date" )
    private Date demandDate;

    /**
     * 单价（不含税）
     */
    @ApiModelProperty(value = "单价（不含税）")
    @TableField(value = "no_tax_price" )
    private BigDecimal noTaxPrice;

    /**
     * 总金额（不含税）
     */
    @ApiModelProperty(value = "总金额（不含税）")
    @TableField(value = "no_tax_total_amt" )
    private BigDecimal noTaxTotalAmt;

    /**
     * 税率
     */
    @ApiModelProperty(value = "税率")
    @TableField(value = "tax_rate" )
    private BigDecimal taxRate;

    /**
     * 单价（含税）
     */
    @ApiModelProperty(value = "单价（含税）")
    @TableField(value = "have_tax_price" )
    private BigDecimal haveTaxPrice;

    /**
     * 总金额（含税）
     */
    @ApiModelProperty(value = "总金额（含税）")
    @TableField(value = "have_tax_total_amt" )
    private BigDecimal haveTaxTotalAmt;

    /**
     * 采购订单id
     */
    @ApiModelProperty(value = "采购订单id")
    @TableField(value = "purchase_id" )
    private String purchaseId;

}
