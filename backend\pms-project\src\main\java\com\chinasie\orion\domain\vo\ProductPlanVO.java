package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.util.List;
/**
 * ProductPlan VO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 10:51:24
 */
@ApiModel(value = "ProductPlanVO对象", description = "产品策划")
@Data
public class ProductPlanVO extends  ObjectVO   implements Serializable{

        /**
         * 项目立项ID
         */
        @ApiModelProperty(value = "项目立项ID")
        private String projectApprovalId;


        /**
         * 技术指标描述
         */
        @ApiModelProperty(value = "技术指标描述")
        private String description;


        /**
         * 技术指标名称
         */
        @ApiModelProperty(value = "技术指标名称")
        private String name;


        /**
         * 编码
         */
        @ApiModelProperty(value = "编码")
        private String number;


    

}
