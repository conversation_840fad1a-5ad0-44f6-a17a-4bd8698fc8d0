package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.dto.ApprovalTaskPrePostDTO;
import com.chinasie.orion.domain.dto.PreTaskDTO;
import com.chinasie.orion.domain.entity.ApprovalTaskPrePost;
import com.chinasie.orion.domain.entity.CollaborativeCompilationTask;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.repository.ApprovalTaskPrePostMapper;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.service.ApprovalTaskPrePostService;
import com.chinasie.orion.service.CollaborativeCompilationTaskService;
import com.chinasie.orion.service.ProjectSetUpService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.transaction.annotation.Transactional;
import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;


/**
 * <p>
 * ApprovalTaskPrePost 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 15:28:14
 */
@Service
@Slf4j
public class ApprovalTaskPrePostServiceImpl extends OrionBaseServiceImpl<ApprovalTaskPrePostMapper, ApprovalTaskPrePost> implements ApprovalTaskPrePostService {
    @Autowired
    private CollaborativeCompilationTaskService collaborativeCompilationTaskService;

    @Autowired
    private ClassRedisHelper classRedisHelper;

    /**
     * 删除(批量)
     *
     * @param ids 计划Id
     * @return Boolean
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteByIds(List<String> ids) throws Exception {
        List<ApprovalTaskPrePost> approvalTaskPrePosts = this.list(new LambdaQueryWrapperX<>(ApprovalTaskPrePost.class)
                .select(ApprovalTaskPrePost::getId, ApprovalTaskPrePost::getPreTaskId, ApprovalTaskPrePost::getPostTaskId, ApprovalTaskPrePost::getTaskId)
                .in(ApprovalTaskPrePost::getId, ids));

        List<String> taskIds = approvalTaskPrePosts.stream().map(ApprovalTaskPrePost::getTaskId).distinct().collect(Collectors.toList());
        List<ApprovalTaskPrePost> list = this.list(new LambdaQueryWrapper<>(ApprovalTaskPrePost.class)
                .select(ApprovalTaskPrePost::getId, ApprovalTaskPrePost::getPreTaskId, ApprovalTaskPrePost::getPostTaskId, ApprovalTaskPrePost::getTaskId)
                .in(ApprovalTaskPrePost::getPreTaskId, taskIds).or()
                .in(ApprovalTaskPrePost::getPostTaskId, taskIds));
        Map<String, Map<String, ApprovalTaskPrePost>> preMap = list.stream().filter(f -> StrUtil.isNotBlank(f.getPreTaskId()))
                .collect(Collectors.groupingBy(ApprovalTaskPrePost::getTaskId,
                        Collectors.toMap(ApprovalTaskPrePost::getPreTaskId, Function.identity(), (v1, v2) -> v1)));

        Map<String, Map<String, ApprovalTaskPrePost>> postMap = list.stream().filter(f -> StrUtil.isNotBlank(f.getPostTaskId()))
                .collect(Collectors.groupingBy(ApprovalTaskPrePost::getTaskId,
                        Collectors.toMap(ApprovalTaskPrePost::getPostTaskId, Function.identity(), (v1, v2) -> v1)));

        for (ApprovalTaskPrePost approvalTaskPrePost : approvalTaskPrePosts) {
            String projectSchemeId = approvalTaskPrePost.getTaskId();
            String preSchemeId = approvalTaskPrePost.getPreTaskId();
            String postSchemeId = approvalTaskPrePost.getPostTaskId();
            if (StrUtil.isNotBlank(preSchemeId)) {
                if (postMap.containsKey(preSchemeId) && postMap.get(preSchemeId).containsKey(projectSchemeId)) {
                    ids.add(postMap.get(preSchemeId).get(projectSchemeId).getId());
                }
            } else if (StrUtil.isNotBlank(postSchemeId)) {
                if (preMap.containsKey(postSchemeId) && preMap.get(postSchemeId).containsKey(projectSchemeId)) {
                    ids.add(preMap.get(postSchemeId).get(projectSchemeId).getId());
                }
            }
        }

        return this.removeBatchByIds(ids);
    }

    /**
     * 添加前后置关系(批量)
     *
     * @param prePostDTO 前置关系列表
     * @return List<String>
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<String> createBatch(PreTaskDTO prePostDTO) throws Exception {
        List<String> taskIds = prePostDTO.getTaskIds();
        if (CollectionUtil.isEmpty(taskIds)) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_SCHEME_NULL);
        }
        List<ApprovalTaskPrePostDTO> approvalTaskPrePostDTOS = prePostDTO.getApprovalTaskPrePostDTOS();
        if (CollectionUtil.isEmpty(approvalTaskPrePostDTOS)) {
            this.remove(new LambdaQueryWrapper<>(ApprovalTaskPrePost.class)
                    .in(ApprovalTaskPrePost::getTaskId, taskIds).or()
                    .in(ApprovalTaskPrePost::getPreTaskId, taskIds).or()
                    .in(ApprovalTaskPrePost::getPostTaskId, taskIds));
            return new ArrayList<>();
        }
        if (approvalTaskPrePostDTOS.stream()
                .anyMatch(a -> StrUtil.isBlank(a.getPostTaskId()) && StrUtil.isBlank(a.getPreTaskId()))) {
            throw new BaseException(PMSErrorCode.PMS_ERR, "存在前后置关系未选择计划");
        }
        List<CollaborativeCompilationTask> collaborativeCompilationTasks = collaborativeCompilationTaskService.listByIds(taskIds);
        if (collaborativeCompilationTasks.stream().anyMatch(s -> Status.FINISHED.getCode().equals(s.getStatus()))) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_EXIST_FINISHED);
        }

        String approvalId = approvalTaskPrePostDTOS.get(0).getApprovalId();

//        ProjectSetUpVO detail = projectSetUpService.detail(approvalId, ProjectSetUpService.prePlanTimeRule);

        // if (Objects.nonNull(detail) && StrUtil.equals(detail.getValue(), "true")) {
        List<String> preTaskIds = new ArrayList<>();
        List<String> postTaskIds = new ArrayList<>();
        for (ApprovalTaskPrePostDTO approvalTaskPrePostDTO : approvalTaskPrePostDTOS) {
            if (StrUtil.isNotBlank(approvalTaskPrePostDTO.getPreTaskId())) {
                preTaskIds.add(approvalTaskPrePostDTO.getPreTaskId());
            } else {
                postTaskIds.add(approvalTaskPrePostDTO.getPostTaskId());
            }
        }
        List<CollaborativeCompilationTask> preTasks;
        if (CollectionUtil.isNotEmpty(preTaskIds)) {
            preTasks = collaborativeCompilationTaskService.listByIds(preTaskIds);
        } else {
            preTasks = new ArrayList<>();
        }
        List<CollaborativeCompilationTask> postSchemes;
        if (CollectionUtil.isNotEmpty(postTaskIds)) {
            postSchemes = collaborativeCompilationTaskService.listByIds(postTaskIds);
        } else {
            postSchemes = new ArrayList<>();
        }

        collaborativeCompilationTasks.forEach(task -> {
            preTasks.forEach(pre -> {
                //校验前置计划的结束时间是否在当前计划的前面
                if (pre.getEndTime().after(task.getBeginTime())) {
                    throw new BaseException(PMSErrorCode.PMS_ERROR_PRJECT_SCHEME_PREPOST_TIME_ERROR, "您设置的前置计划完成时间超出本计划的开始时间，请确认");
                }
            });
            postSchemes.forEach(post -> {
                //校验后置计划的开始时间是否在当前计划的前面
                if (task.getEndTime().after(post.getBeginTime())) {
                    throw new BaseException(PMSErrorCode.PMS_ERROR_PRJECT_SCHEME_PREPOST_TIME_ERROR, "您设置的后置计划开始时间超出本计划的完成时间，请确认");
                }
            });
        });
        // }


        this.remove(new LambdaQueryWrapper<>(ApprovalTaskPrePost.class)
                .in(ApprovalTaskPrePost::getTaskId, taskIds).or()
                .in(ApprovalTaskPrePost::getPreTaskId, taskIds).or()
                .in(ApprovalTaskPrePost::getPostTaskId, taskIds));
        List<ApprovalTaskPrePost> list = new ArrayList<>();
        for (String taskId : taskIds) {
            for (ApprovalTaskPrePostDTO nodePrePostDTO : prePostDTO.getApprovalTaskPrePostDTOS()) {
                ApprovalTaskPrePost prePost = new ApprovalTaskPrePost();
                ApprovalTaskPrePost relationPrePost = new ApprovalTaskPrePost();
                if (StrUtil.isNotBlank(nodePrePostDTO.getPreTaskId())) {
                    prePost.setType(Status.SCHEME_PRE.getCode());
                    prePost.setPreTaskId(nodePrePostDTO.getPreTaskId());
                    relationPrePost.setType(Status.SCHEME_POST.getCode());
                    relationPrePost.setTaskId(nodePrePostDTO.getPreTaskId());
                    relationPrePost.setPostTaskId(taskId);
                } else {
                    prePost.setType(Status.SCHEME_POST.getCode());
                    prePost.setPostTaskId(nodePrePostDTO.getPostTaskId());
                    relationPrePost.setType(Status.SCHEME_PRE.getCode());
                    relationPrePost.setTaskId(nodePrePostDTO.getPostTaskId());
                    relationPrePost.setPreTaskId(taskId);
                }
                prePost.setId(classRedisHelper.getUUID(CollaborativeCompilationTask.class.getSimpleName()));
                prePost.setTaskId(taskId);
                prePost.setApprovalId(approvalId);
                relationPrePost.setId(classRedisHelper.getUUID(CollaborativeCompilationTask.class.getSimpleName()));
                relationPrePost.setApprovalId(approvalId);
                list.add(prePost);
                list.add(relationPrePost);
            }
        }
        this.saveBatch(list);
        return list.stream().map(ApprovalTaskPrePost::getId).collect(Collectors.toList());
    }

    /**
     * 变更前置关系，删除后新增
     *
     * @param id          项目计划Id
     * @param prePostDTOS 前置关系列表
     * @return List<String>
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<String> modify(String id, List<ApprovalTaskPrePostDTO> prePostDTOS) throws Exception {
        List<ApprovalTaskPrePost> prePostList = this.list(new LambdaQueryWrapper<>(ApprovalTaskPrePost.class).eq(ApprovalTaskPrePost::getTaskId, id));
        List<String> oldPreIds = prePostList.stream().map(ApprovalTaskPrePost::getId).collect(Collectors.toList());
        this.removeBatchByIds(oldPreIds);
        List<ApprovalTaskPrePost> prePosts = prePostDTOS.stream().map(pre -> BeanCopyUtils.convertTo(pre, ApprovalTaskPrePost::new))
                .peek(pre -> {
                    if (StrUtil.isNotBlank(pre.getPreTaskId())) {
                        pre.setType(Status.SCHEME_PRE.getCode());
                    } else {
                        pre.setType(Status.SCHEME_POST.getCode());
                    }
                }).collect(Collectors.toList());
        this.saveBatch(prePosts);
        return prePosts.stream().map(ApprovalTaskPrePost::getId).collect(Collectors.toList());
    }


}
