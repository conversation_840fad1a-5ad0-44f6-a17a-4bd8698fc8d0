package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.util.List;
/**
 * JobSecurityMeasure VO对象
 *
 * <AUTHOR>
 * @since 2024-06-09 15:47:47
 */
@ApiModel(value = "JobSecurityMeasureVO对象", description = "作业安措信息")
@Data
public class JobSecurityMeasureVO extends  ObjectVO   implements Serializable{

            /**
         * 作业id
         */
        @ApiModelProperty(value = "作业id")
        private String jobId;


        /**
         * 安全措施编码
         */
        @ApiModelProperty(value = "安全措施编码")
        private String measureCode;


        /**
         * 安全措施描述
         */
        @ApiModelProperty(value = "安全措施描述")
        private String measureDesc;


        /**
         * 措施类型
         */
        @ApiModelProperty(value = "措施类型")
        private String measureType;


        /**
         * 措施长文本
         */
        @ApiModelProperty(value = "措施长文本")
        private String measureText;

        @ApiModelProperty(value = "订单号")
        private String jobCode;
        @ApiModelProperty(value = "风险分析")
        private String riskAnaly;
        @ApiModelProperty(value = "计划工厂")
        private String planFactory;
        @ApiModelProperty(value = "风险代码")
        private String riskCode;
        @ApiModelProperty(value = "md5_value")
        private String encryKey;
    

}
