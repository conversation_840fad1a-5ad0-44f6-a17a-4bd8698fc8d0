package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.util.Date;
import java.lang.String;
import java.lang.Integer;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * JobPostAuthorize DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-08 20:33:32
 */
@ApiModel(value = "JobPostAuthorizeDTO对象", description = "作业授权信息")
@Data
@ExcelIgnoreUnannotated
public class JobPostAuthorizeDTO extends  ObjectDTO   implements Serializable{

    /**
     * 人员ID
     */
    @ApiModelProperty(value = "人员ID")
    @ExcelProperty(value = "人员ID ", index = 0)
    private String personId;

    /**
     * 人员编号/工号
     */
    @ApiModelProperty(value = "人员编号/工号")
    @ExcelProperty(value = "人员编号/工号 ", index = 1)
    private String userCode;

    /**
     * 所属作业ID
     */
    @ApiModelProperty(value = "所属作业ID")
    @ExcelProperty(value = "所属作业ID ", index = 2)
    private String jobId;

    /**
     * 作业所属基地编码
     */
    @ApiModelProperty(value = "作业所属基地编码")
    private String jobBaseCode;
    /**
     * 所属大修轮次
     */
    @ApiModelProperty(value = "所属大修轮次")
    @ExcelProperty(value = "所属大修轮次 ", index = 3)
    private String repairRound;

    /**
     * 所属计划ID
     */
    @ApiModelProperty(value = "所属计划ID")
    @ExcelProperty(value = "所属计划ID ", index = 4)
    private String planSchemeId;

    /**
     * 作业岗位编码
     */
    @ApiModelProperty(value = "作业岗位编码")
    @ExcelProperty(value = "作业岗位编码 ", index = 5)
    private String jobPostCode;

    /**
     * 是否满足授权*（0-待确认，1-不满足，2-满足）
     */
    @ApiModelProperty(value = "是否满足授权*（0-待确认，1-不满足，2-满足）")
    @ExcelProperty(value = "是否满足授权*（0-待确认，1-不满足，2-满足） ", index = 6)
    private Integer isAuthorization;

    /**
     * 授权到期日期
     */
    @ApiModelProperty(value = "授权到期日期")
    @ExcelProperty(value = "授权到期日期 ", index = 7)
    private Date endDate;

    /**
     * 人员所在基地
     */
    @ApiModelProperty(value = "人员所在基地")
    @ExcelProperty(value = "人员所在基地 ", index = 8)
    private String basePlaceCode;

    /**
     * 人员所在基地名称
     */
    @ApiModelProperty(value = "人员所在基地名称")
    @ExcelProperty(value = "人员所在基地名称 ", index = 9)
    private String basePlaceName;

    /**
     * 进入基地时间
     */
    @ApiModelProperty(value = "进入基地时间")
    @ExcelProperty(value = "进入基地时间 ", index = 10)
    private Date enterBaseDate;


    /**
     * 授权状态（101-未授权，130-已授权）
     */
    @ApiModelProperty(value = "授权状态（101-未授权，130-已授权）")
    @ExcelProperty(value = "授权状态（101-未授权，130-已授权） ", index = 11)
    @TableField(value = "authorize_status")
    private Integer authorizeStatus;


    /**
     * 是否申请岗位等效
     */
    @ApiModelProperty(value = "是否申请岗位等效")
    private Boolean isApplyJobEqu;

    /**
     * 岗位授权指引（冗余）
     */
    @ApiModelProperty(value = "岗位授权指引（冗余）")
    private String authorizationGuide;


    /**
     * 授权起始日期
     */
    @ApiModelProperty(value = "授权起始日期")
    private Date authorizeStartDate;


    @ApiModelProperty(value = "计划起始时间")
    private Date planBeginDate;
    @ApiModelProperty(value = "计划结束时间")
    private Date planEndDate;

    @ApiModelProperty(value = "实际起始时间")
    private Date actBeginDate;
    @ApiModelProperty(value = "实际结束时间")
    private Date actEndDate;

    @ApiModelProperty("关键词")
    private String keyword;

}
