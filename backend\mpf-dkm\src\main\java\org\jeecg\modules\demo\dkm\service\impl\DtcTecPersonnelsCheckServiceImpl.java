package org.jeecg.modules.demo.dkm.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.base.BaseServiceImpl;
import org.jeecg.modules.demo.dkm.config.DuplicateCheckConfig;
import org.jeecg.modules.demo.dkm.config.FieldCheckConfig;
import org.jeecg.modules.demo.dkm.dto.DtcPersonnelsDTO;
import org.jeecg.modules.demo.dkm.entity.DtcTecPersonnels;
import org.jeecg.modules.demo.dkm.mapper.DtcTecPersonnelsMapper;
import org.jeecg.modules.demo.dkm.service.DtcTecPersonnelsCheckService;
import org.jeecg.modules.demo.dkm.service.base.DataIntegrityCheckService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Description: DtcTecPersonnels Service实现类
 * @Author: tancheng
 * @Date: 2025-4-16
 */
@Service
@Slf4j
public class DtcTecPersonnelsCheckServiceImpl extends BaseServiceImpl<DtcTecPersonnelsMapper, DtcTecPersonnels>
    implements DtcTecPersonnelsCheckService {
    
    @Resource
    private DtcTecPersonnelsMapper dtcTecPersonnelsMapper;
    
    // 使用组合方式引入数据完整性检查服务
    private final DataIntegrityCheckService<DtcPersonnelsDTO> dataIntegrityCheckService;
    
    public DtcTecPersonnelsCheckServiceImpl() {
        // 创建匿名内部类实例
        this.dataIntegrityCheckService = new DataIntegrityCheckService<DtcPersonnelsDTO>() {};
    }

    @Override
    public String checkTecPersonnelsDataIntegrity() {
        // 配置各字段的检查条件、表头和行数据生成逻辑
        List<FieldCheckConfig<DtcPersonnelsDTO>> fieldConfigs = Arrays.asList(
                // 员工号缺失配置
                FieldCheckConfig.create(
                        "员工号",
                        DtcPersonnelsDTO::getStaffNo,
                        Arrays.asList("姓名", "部门名称", "操作人", "第一负责人编号", "第二负责人编号"),
                        DtcPersonnelsDTO::getStaffName,
                        DtcPersonnelsDTO::getDeptName,
                        DtcPersonnelsDTO::getOperator,
                        DtcPersonnelsDTO::getDeptLeaderId,
                        DtcPersonnelsDTO::getDeptHeader2Id
                ),
                // 员工姓名缺失配置
                FieldCheckConfig.create(
                        "员工姓名",
                        DtcPersonnelsDTO::getStaffName,
                        Arrays.asList("员工号", "部门名称", "操作人", "第一负责人编号", "第二负责人编号"),
                        DtcPersonnelsDTO::getStaffNo,
                        DtcPersonnelsDTO::getDeptName,
                        DtcPersonnelsDTO::getOperator,
                        DtcPersonnelsDTO::getDeptLeaderId,
                        DtcPersonnelsDTO::getDeptHeader2Id
                ),
                // 身份证号缺失配置
                FieldCheckConfig.create(
                        "身份证号",
                        DtcPersonnelsDTO::getStaffIdCard,
                        Arrays.asList("员工号", "姓名", "部门名称", "操作人", "第一负责人编号", "第二负责人编号"),
                        DtcPersonnelsDTO::getStaffNo,
                        DtcPersonnelsDTO::getStaffName,
                        DtcPersonnelsDTO::getDeptName,
                        DtcPersonnelsDTO::getOperator,
                        DtcPersonnelsDTO::getDeptLeaderId,
                        DtcPersonnelsDTO::getDeptHeader2Id
                ),
                // 部门编码缺失配置
                FieldCheckConfig.create(
                        "部门编码",
                        DtcPersonnelsDTO::getDeptNo,
                        Arrays.asList("员工号", "姓名", "部门名称", "操作人", "第一负责人编号", "第二负责人编号"),
                        DtcPersonnelsDTO::getStaffNo,
                        DtcPersonnelsDTO::getStaffName,
                        DtcPersonnelsDTO::getDeptName,
                        DtcPersonnelsDTO::getOperator,
                        DtcPersonnelsDTO::getDeptLeaderId,
                        DtcPersonnelsDTO::getDeptHeader2Id
                ),
                // 部门名称缺失配置
                FieldCheckConfig.create(
                        "部门名称",
                        DtcPersonnelsDTO::getDeptName,
                        Arrays.asList("员工号", "姓名", "部门编码", "操作人", "第一负责人编号", "第二负责人编号"),
                        DtcPersonnelsDTO::getStaffNo,
                        DtcPersonnelsDTO::getStaffName,
                        DtcPersonnelsDTO::getDeptNo,
                        DtcPersonnelsDTO::getOperator,
                        DtcPersonnelsDTO::getDeptLeaderId,
                        DtcPersonnelsDTO::getDeptHeader2Id
                )
        );
        
        // 查询员工号重复的数据
        List<DtcPersonnelsDTO> dublicateStaffNoIssues = dtcTecPersonnelsMapper.findDublicateStaffNoTecDataIssues();
        
        // 定义员工号重复的检查项
        List<DuplicateCheckConfig<DtcPersonnelsDTO>> duplicateConfigs = Collections.singletonList(
                DuplicateCheckConfig.create(
                        "员工号重复",
                        dublicateStaffNoIssues,
                        Arrays.asList("员工号", "姓名", "部门名称", "操作人", "第一负责人编号", "第二负责人编号"),
                        DtcPersonnelsDTO::getStaffNo,
                        DtcPersonnelsDTO::getStaffName,
                        DtcPersonnelsDTO::getDeptName,
                        DtcPersonnelsDTO::getOperator,
                        DtcPersonnelsDTO::getDeptLeaderId,
                        DtcPersonnelsDTO::getDeptHeader2Id
                )
        );

        // 使用数据完整性检查服务执行检查
        return dataIntegrityCheckService.executeIntegrityCheckWithDuplicates(
                "智科数据库", 
                () -> dtcTecPersonnelsMapper.findTecDataIntegrityIssues(),
                fieldConfigs,
                duplicateConfigs
        );
    }
}