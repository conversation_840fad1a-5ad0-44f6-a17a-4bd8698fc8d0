<?xml version="1.0" encoding="UTF-8"?>

<!--
Copyright 2019 the original author or authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <groupId>io.github.openfeign.form</groupId>
  <artifactId>parent</artifactId>
  <version>3.8.0</version>
  <packaging>pom</packaging>

  <modules>
    <module>feign-form</module>
    <module>feign-form-spring</module>
  </modules>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.build.resourceEncoding>UTF-8</project.build.resourceEncoding>

    <maven.compiler.source>1.6</maven.compiler.source>
    <maven.compiler.target>1.6</maven.compiler.target>

    <maven.compiler.testTarget>1.8</maven.compiler.testTarget>
    <maven.compiler.testSource>1.8</maven.compiler.testSource>
  </properties>

  <name>Open Feign Forms Parent</name>
  <description>
    Simple encoder for Netflix Feign project, which adds forms support (urlencoded and multipart)
  </description>
  <url>https://github.com/OpenFeign/feign-form</url>
  <inceptionYear>2016</inceptionYear>

  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <scm>
    <url>https://github.com/OpenFeign/feign-form</url>
    <connection>scm:git:https://github.com/OpenFeign/feign-form.git</connection>
    <developerConnection>scm:git:https://github.com/OpenFeign/feign-form.git</developerConnection>
    <tag>3.8.0</tag>
  </scm>

  <distributionManagement>
    <snapshotRepository>
      <id>ossrh</id>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
    <repository>
      <id>ossrh</id>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
  </distributionManagement>

  <issueManagement>
    <system>GitHub Issues</system>
    <url>https://github.com/OpenFeign/feign-form/issues</url>
  </issueManagement>

  <ciManagement>
    <system>Travis</system>
    <url>https://travis-ci.org/OpenFeign/feign-form</url>
  </ciManagement>

  <developers>
    <developer>
      <name>Artem Labazin</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <name>Tomasz Juchniewicz</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <name>Guillaume Simard</name>
    </developer>
  </developers>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-form</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-form-spring</artifactId>
        <version>${project.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <dependencies>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>1.18.6</version>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.7.26</version>
    </dependency>

    <dependency>
      <groupId>io.github.openfeign</groupId>
      <artifactId>feign-core</artifactId>
      <version>10.2.0</version>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>io.github.openfeign</groupId>
      <artifactId>feign-jackson</artifactId>
      <version>10.2.0</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
      <version>2.1.3.RELEASE</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <version>2.1.3.RELEASE</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <version>5.4.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-params</artifactId>
      <version>5.4.1</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <version>3.12.2</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>2.25.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-junit-jupiter</artifactId>
      <version>2.25.1</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>net.jcip</groupId>
      <artifactId>jcip-annotations</artifactId>
      <version>1.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.github.spotbugs</groupId>
      <artifactId>spotbugs-annotations</artifactId>
      <version>3.1.12</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>jsr305</artifactId>
      <version>3.0.2</version>
      <scope>provided</scope>
    </dependency>
  </dependencies>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <artifactId>maven-jar-plugin</artifactId>
          <version>3.0.2</version>
          <configuration>
            <archive>
              <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
            </archive>
          </configuration>
        </plugin>

        <plugin>
          <groupId>com.github.spotbugs</groupId>
          <artifactId>spotbugs-maven-plugin</artifactId>
          <version>3.1.11</version>
          <configuration>
            <effort>Max</effort>
            <threshold>Low</threshold>
            <xmlOutput>false</xmlOutput>
            <excludeFilterFile>${project.basedir}/../.codestyle/findbugs.xml</excludeFilterFile>
          </configuration>
          <executions>
            <execution>
              <id>spotbugs-validation</id>
              <phase>verify</phase>
              <goals>
                <goal>check</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-pmd-plugin</artifactId>
          <version>3.11.0</version>
          <configuration>
            <sourceEncoding>${project.build.sourceEncoding}</sourceEncoding>
            <targetJdk>${maven.compiler.source}</targetJdk>
            <failOnViolation>true</failOnViolation>
            <printFailingErrors>true</printFailingErrors>
            <analysisCache>true</analysisCache>
            <linkXRef>false</linkXRef>
            <rulesets>
              <ruleset>${project.basedir}/../.codestyle/pmd.xml</ruleset>
            </rulesets>
          </configuration>
          <executions>
            <execution>
              <id>pmd-validation</id>
              <phase>verify</phase>
              <goals>
                <goal>check</goal>
              </goals>
            </execution>
          </executions>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-checkstyle-plugin</artifactId>
          <version>3.0.0</version>
          <dependencies>
            <dependency>
              <groupId>com.puppycrawl.tools</groupId>
              <artifactId>checkstyle</artifactId>
              <version>8.18</version>
            </dependency>
          </dependencies>
          <executions>
            <execution>
              <id>checkstyle-validation</id>
              <phase>verify</phase>
              <goals>
                <goal>check</goal>
              </goals>
              <configuration>
                <configLocation>${project.basedir}/../.codestyle/checkstyle.xml</configLocation>
                <consoleOutput>true</consoleOutput>
                <failsOnError>true</failsOnError>
                <failOnViolation>true</failOnViolation>
                <linkXRef>false</linkXRef>
                <propertyExpansion>codestyleFolder=${project.basedir}/../.codestyle</propertyExpansion>
                <includeTestSourceDirectory>true</includeTestSourceDirectory>
              </configuration>
            </execution>
          </executions>
        </plugin>

        <plugin>
          <groupId>org.apache.felix</groupId>
          <artifactId>maven-bundle-plugin</artifactId>
          <version>3.2.0</version>
          <executions>
            <execution>
              <id>bundle-manifest</id>
              <phase>process-classes</phase>
              <goals>
                <goal>manifest</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
      </plugins>
    </pluginManagement>

    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <version>3.0.1</version>
        <executions>
          <execution>
            <id>attach-sources</id>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>pl.project13.maven</groupId>
        <artifactId>git-commit-id-plugin</artifactId>
        <version>2.2.6</version>
        <executions>
          <execution>
            <id>git-infos</id>
            <goals>
              <goal>revision</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <runOnlyOnce>true</runOnlyOnce>
          <failOnNoGitDirectory>false</failOnNoGitDirectory>
          <generateGitPropertiesFile>true</generateGitPropertiesFile>
          <verbose>false</verbose>
          <prefix>git</prefix>
          <generateGitPropertiesFilename>
            ${project.build.outputDirectory}/git.properties
          </generateGitPropertiesFilename>
          <dateFormat>yyyy-MM-dd'T'HH:mm:ssZ</dateFormat>
          <dotGitDirectory>${project.basedir}/.git</dotGitDirectory>
          <excludeProperties>
            <excludeProperty>git.build.user.*</excludeProperty>
            <excludeProperty>git.build.host</excludeProperty>
            <excludeProperty>git.commit.user.*</excludeProperty>
            <excludeProperty>git.total.commit.count</excludeProperty>
            <excludeProperty>git.closest.tag.commit.count</excludeProperty>
            <excludeProperty>git.closest.tag.name</excludeProperty>
          </excludeProperties>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>3.0.1</version>
        <configuration>
          <additionalOptions>-Xdoclint:none</additionalOptions>
          <additionalparam>-Xdoclint:none</additionalparam>
          <breakiterator>true</breakiterator>
          <charset>UTF-8</charset>
          <docencoding>UTF-8</docencoding>
          <encoding>UTF-8</encoding>
          <failOnError>true</failOnError>
          <show>protected</show>
          <version>true</version>
        </configuration>
        <executions>
          <execution>
            <id>attach-javadocs</id>
            <goals>
              <goal>jar</goal>
            </goals>
            <phase>package</phase>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.0</version>
        <executions>
           <!--Ensure main source tree compiles to Java ${main.java.version} bytecode.-->
          <execution>
            <id>default-compile</id>
            <phase>compile</phase>
            <goals>
              <goal>compile</goal>
            </goals>
            <configuration>
              <source>${main.java.version}</source>
              <target>${main.java.version}</target>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>animal-sniffer-maven-plugin</artifactId>
        <version>1.16</version>
        <executions>
          <execution>
            <id>signature-check</id>
            <phase>verify</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <signature>
            <groupId>org.codehaus.mojo.signature</groupId>
            <artifactId>java16</artifactId>
            <version>1.0</version>
          </signature>
        </configuration>
      </plugin>

      <plugin>
        <artifactId>maven-install-plugin</artifactId>
        <version>2.5.2</version>
        <configuration>
          <createChecksum>true</createChecksum>
        </configuration>
      </plugin>

      <!-- To release to Maven central -->
      <plugin>
        <groupId>org.sonatype.plugins</groupId>
        <artifactId>nexus-staging-maven-plugin</artifactId>
        <version>1.6.8</version>
        <extensions>true</extensions>
        <configuration>
          <serverId>ossrh</serverId>
          <nexusUrl>https://oss.sonatype.org/</nexusUrl>
          <autoReleaseAfterClose>true</autoReleaseAfterClose>
        </configuration>
      </plugin>

      <!-- To sign the artifacts -->
     <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-gpg-plugin</artifactId>
        <version>1.6</version>
        <executions>
          <execution>
            <id>sign-artifacts</id>
            <phase>install</phase>
            <goals>
              <goal>sign</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
