package com.chinasie.orion.conts;

public enum IncomePlanControlEnum {
    UNCONTROL("uncontrol", "未管控"),
    CONTROL("control", "已管控"),
    PARTIALCONTROL("partialControl", "部分中心管控");
    private String status;
    private String name;

    public String getStatus() {
        return status;
    }

    public String getName() {
        return name;
    }

    IncomePlanControlEnum(String status, String name) {
        this.status = status;
        this.name = name;
    }
}
