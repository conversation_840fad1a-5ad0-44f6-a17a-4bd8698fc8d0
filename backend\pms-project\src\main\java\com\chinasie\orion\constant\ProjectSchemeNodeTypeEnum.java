package com.chinasie.orion.constant;

import com.chinasie.orion.conts.ProjectSourceTypeEnum;

import java.util.Objects;

/**
 * @author: yk
 * @date: 2023/11/3 10:49
 * @description:
 */
public enum ProjectSchemeNodeTypeEnum {
    MILESTONE("milestone","里程碑"),PLAN("plan","计划");
    private String value;

    private String Name;

    ProjectSchemeNodeTypeEnum(String value, String Name) {
        this.value = value;
        this.Name = Name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return Name;
    }

    public void setName(String name) {
        Name = name;
    }

    public static String getNameByValue(String value) {
        for (ProjectSchemeNodeTypeEnum projectSchemeNodeTypeEnum : ProjectSchemeNodeTypeEnum.values()) {
            if (Objects.equals(projectSchemeNodeTypeEnum.getValue(), value)) {
                return projectSchemeNodeTypeEnum.Name;
            }
        }
        return "";
    }
}
