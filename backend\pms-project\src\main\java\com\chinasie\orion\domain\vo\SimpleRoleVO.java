package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2023/04/28/9:56
 * @description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "SimpleRoleVO对象", description = "简版角色对象")
public class SimpleRoleVO implements Serializable {
    @ApiModelProperty("角色ID")
    private String roleId;

    @ApiModelProperty("角色编号")
    private String roleCode;

    public String getRoleCode() {
        return roleCode;
    }

    public void setRoleCode(String roleCode) {
        this.roleCode = roleCode;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }
}
