<template>
  <div class="addNodeModal">
    <BasicDrawer
      v-model:visible="visible"
      :title="title"
      placement="right"
      width="340"
      class="addNodeModalDrawer pdmRightDrawer"
      :mask-closable="false"
      @close="close"
    >
      <a-form
        ref="formRef"
        :model="formState"
        class="pdmFormClass nodeForm"
        label-align="left"
      >
        <template
          v-for="(current, index) of formItemArr"
          :key="index"
        >
          <a-form-item
            :label="current.label"
            :name="current.field"
            style="text-align: left"
            :rules="current.rules"
          >
            <template v-if="current.type === 'input'">
              <a-input
                v-model:value="formState[current.field]"
                v-bind="current.apiConfig"
                :disabled="listData?.[0]?.code === 'pm'"
              />
            </template>
            <template v-else-if="current.type === 'textarea'">
              <a-textarea
                v-model:value="formState[current.field]"
                show-count
                v-bind="current.apiConfig"
              />
            </template>
            <template v-else-if="current.type === 'select'">
              <a-select
                v-model:value="formState[current.field]"
                v-bind="current.apiConfig"
              >
                <a-select-option
                  v-for="(item, index) in current.options"
                  :key="index"
                  :value="item.id"
                >
                  {{ item.name }}
                </a-select-option>
              </a-select>
            </template>
            <template v-else-if="current.type === 'select222'">
              <a-select
                v-model:value="formState[current.field]"
                v-bind="current.apiConfig"
              >
                <a-select-option
                  v-for="(item, index) in current.options"
                  :key="index"
                  :value="item.value"
                >
                  {{ item.name }}
                </a-select-option>
              </a-select>
            </template>
            <template v-else-if="current.type === 'selectSearchPeople'">
              <a-select
                v-model:value="formState[current.field]"
                v-bind="current.apiConfig"
                show-search
                :options="peopelOption[current.field]"
                :filter-option="filterHandle"
                @search="handleChange"
              />
            </template>
            <template v-else-if="current.type === 'dataPicker'">
              <a-date-picker
                v-model:value="formState[current.field]"
                v-bind="current.apiConfig"
                style="width:100%"
              />
            </template>
            <template v-else-if="current.type === 'inputIcon'">
              <div class="components-input-demo-presuffix">
                <!-- <a-input v-model:value="formState[current.field]" v-bind="current.apiConfig" /> -->
                <a-input-number
                  v-model:value="formState[current.field]"
                  v-bind="current.apiConfig"
                  :min="0"
                  :max="current.apiConfig.suffix === '小时' ? 9999 : 100"
                  :formatter="
                    (value) => {
                      return value && `${value}${current.apiConfig.suffix}`;
                    }
                  "
                  :parser="(value) => value.replace(`${current.apiConfig.suffix}`, '')"
                />
              </div>
            </template>
            <template v-else-if="current.type === 'treeSelect'">
              <a-tree-select
                v-model:value="formState[current.field]"
                style="width: 100%"
                :tree-data="optionsValue[current.field]"
                v-bind="current.apiConfig"
              />
            </template>
          </a-form-item>
        </template>

        <div
          v-if="formType == 'add'"
          class="nextCheck"
        >
          <aCheckbox v-model:checked="nextCheck">
            继续创建下一个
          </aCheckbox>
        </div>
        <a-form-item
          style="text-align: center"
          class="nodeItemBtn"
        >
          <a-button
            size="large"
            class="cancelBtn"
            @click="cancel"
          >
            取消
          </a-button>
          <a-button
            size="large"
            class="bgDC"
            type="primary"
            :loading="loading"
            @click="onSubmit"
          >
            确认
          </a-button>
        </a-form-item>
      </a-form>
    </BasicDrawer>
  </div>
  <messageModal
    :title="'确认提示'"
    :show-visible="showVisible"
    @cancel="showVisible = false"
    @confirm="confirm"
  >
    <div class="messageVal">
      <InfoCircleOutlined />
      <span>{{
        formType == 'add' ? '创建数据未保存是否确认关闭？' : '编辑数据未保存是否确认关闭？'
      }}</span>
    </div>
  </messageModal>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, ref, watch, onMounted, nextTick,
} from 'vue';
import {
  Checkbox,
  Drawer,
  Input,
  Button,
  Form,
  message,
  Select,
  TreeSelect,
  InputNumber,
  DatePicker,
} from 'ant-design-vue';
import messageModal from '/@/views/pms/projectLaborer/components/MessageModal/messageModal.vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
import { BasicDrawer } from 'lyra-component-vue3';
import dayjs from 'dayjs';
export default defineComponent({
  components: {
    aForm: Form,
    aFormItem: Form.Item,
    aCheckbox: Checkbox,
    aDrawer: Drawer,
    aButton: Button,
    aInput: Input,
    aTextarea: Input.TextArea,
    ASelect: Select,
    ASelectOption: Select.Option,
    messageModal,
    InfoCircleOutlined,
    ATreeSelect: TreeSelect,
    AInputNumber: InputNumber,
    ADatePicker: DatePicker,
    BasicDrawer,
  },
  props: {
    formItemArr: {
      type: Object,
      default: () => [],
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    otherApi: {
      type: Object,
      default: () => ({}),
    },
    formState: {
      type: Object,
      default: () => ({}),
    },
    listData: <any>{
      type: Array,
      default: () => [],
    },
    projectid: {
      type: String,
      default: '',
    },
    zkitemids: {
      type: String,
      default: '',
    },
  },
  setup(props, { emit }) {
    const state = reactive({
      formType: '',
      checkedValue: [],
      addVisible: false,
      visible: false,
      formState: <any>{},
      peopelOption: {},
      optionsValue: {},
      selectValue: '',
      title: '',
      nextCheck: false,
      loading: false,
      showVisible: false,
      flag: false,
    });
    const formRef = ref();
    const getRole = async (value, idkey) => {
      const newvalue = {
        name: value,
      };
      await props.otherApi.zkPeopel(newvalue, idkey).then((res) => {
        for (let N in state.peopelOption) {
          state.peopelOption[N] = res.map((item) => ({
            value: item.id,
            label: item.name,
          }));
        }
      });
    };
      /* arr-tree */
    const buildTree = (list) => {
      let temp = {};

      let tree = [];

      for (let i in list) {
        temp[list[i].id] = list[i];
      }

      for (let i in temp) {
        if (temp[i].parentId !== '-1') {
          if (!temp[temp[i].parentId].children) {
            temp[temp[i].parentId].children = [];
          }

          temp[temp[i].parentId].children.push(temp[i]);
        } else {
          tree.push(temp[i]);
        }
      }

      return tree;
    };
    const buildTree2 = (list) => {
      let temp = {};
      let tree = {};
      for (let i in list) {
        temp[list[i].id] = list[i];
      }
      for (let i in temp) {
        if (temp[i].parentId) {
          if (!temp[temp[i].parentId]?.children) {
            temp[temp[i].parentId].children = [];
          }
          temp[temp[i].parentId].children[temp[i].id] = temp[i];
        } else {
          tree[temp[i].id] = temp[i];
        }
      }
      return tree;
    };
    const convertTree = (tree) => {
      const result = [];
      tree.forEach((item) => {
        let children = item.child || [];
        // let expand = true;
        let { name: title, id: key, id: value } = item;
        if (children && children.length) {
          children = convertTree(children);
        }
        result.push({
          title,
          children,
          // expand,
          key,
          value,
        });
      });
      return result;
    };
    const convertTree2 = (tree) => {
      const result = [];
      tree.forEach((item) => {
        let children = item.children || [];
        // let expand = true;
        let { name: title, id: key, id: value } = item;
        if (children && children.length) {
          children = convertTree(children);
        }
        result.push({
          title,
          children,
          // expand,
          key,
          value,
        });
      });
      return result;
    };
      // 初始化时处理管理字段
    try {
      props.formItemArr.forEach(async (current) => {
        state.formState[current.field] = undefined;
        state.peopelOption[current.field] = [];
        if (current.options) {
          // if (current.addId === 'newitemid') {
          //   if (props.listData[0]?.id) {
          //     current.options = await current['getOptionFn'](props.listData[0].id);
          //   }
          // }
          if (current.addId === 'projectid') {
            current.options = await current.getOptionFn(props.projectid);
          }
          if (current.addId === 'itemid') {
            current.options = await current.getOptionFn(props.listData[0].id);
          }

          if (current.addId === 'objproject') {
            current.options = await current.getOptionFn({ projectId: props.projectid });
          }
          if (!current.addId) {
            current.options = await current.getOptionFn();
          }
        }
        if (current.optionsValue) {
          state.optionsValue[current.field] = [];
          const planTree = { projectId: props.projectid };
          if (current.addId === 'arr') {
            const res7 = await current.getOptionFn(planTree);
            state.optionsValue[current.field] = convertTree2(res7);
          }
          if (!current.addId) {
            const res6 = await current.getOptionFn(planTree);
            const arr = [res6];
            state.optionsValue[current.field] = convertTree(arr);
          }
        }
        if (current.radioOptions) {
          await getRole('', props.projectid);
        }
      });
    } catch (err) {
      message.warning('内部错误');
    }
    watch(
      () => props.data,
      (value) => {
        state.visible = true;
        state.nextCheck = false;
        try {
          props.formItemArr.forEach(async (current) => {
            console.log('current', current);
            if (current.optionsValue) {
              state.optionsValue[current.field] = [];
              const planTree = { projectId: props.projectid };
              if (current.addId === 'arr') {
                const res7 = await current.getOptionFn(planTree);
                state.optionsValue[current.field] = convertTree2(res7);
              }
              if (!current.addId) {
                const res6 = await current.getOptionFn(planTree);
                const arr = [res6];
                state.optionsValue[current.field] = convertTree(arr);
              }
            }
          });
        } catch (err) {
          message.warning('内部错误');
        }
        if (value.formType == 'add') {
          state.title = '创建信息';
          state.formType = 'add';
          if (value.modifyTitle) {
            state.title = value.modifyTitle;
          }
          if (value.mapcofig) {
            //   console.log('测试🚀🚀 ~~~ value.mapcofig', value.mapcofig);
            state.formState.parentId = value.mapcofig;
          }
          if (value.configName) {
            //   console.log('测试🚀🚀 ~~~ value.mapcofig', value.mapcofig);
            state.formState.name = value.configName;
            state.formState.xxxx = value.configName;
          }
        } else {
          state.title = '修改信息';
          state.formType = 'edit';
          if (props?.otherApi.zkItemDetails) {
            props.otherApi
              .zkItemDetails(props.listData[0].id)
              .then(async (res) => {
                for (let item of props.formItemArr) {
                  if (item.addId === 'newitemid') {
                    if (props.listData[0]?.id) {
                      item.options = await item.getOptionFn(props.listData[0].id);
                    }
                  }
                  if (item.type === 'dataPicker') {
                    res[item.field] = dayjs(res[item.field]);
                  }
                  state.formState[item.field] = res[item.field];
                }
              })
              .catch((err) => {
                console.log('测试🚀🚀 ~~~ err', err);
              });
          } else {
            for (let item of props.formItemArr) {
              if (props.listData[0][item.field]) {
                if (item.type === 'dataPicker') {
                  props.listData[0][item.field] = dayjs(props.listData[0][item.field]);
                }
                state.formState[item.field] = props.listData[0][item.field];
              }
            }
          }

          state.formState.id = props.listData[0].id;
        }
      },
    );
    /* 侦听表单是否变化 */
    //   watch(
    //     state.formState,
    //     () => {
    //       console.log('数据变化');
    //       state.flag = true;
    //     },
    //     { deep: true, immediate: true }
    //   );
    /* 表单取消按钮 */
    const cancel = () => {
      // console.log('取消按钮', 159);
      formRef.value.resetFields();
      state.formState = {};

      state.visible = false;
    };
      /* x按钮 */
    const close = () => {
      formRef.value.resetFields();
      state.formState = {};
      state.formType = '';
      state.visible = false;
      state.flag = false;
    };
      /* 提示弹窗确定cb */
    const confirm = () => {
      state.showVisible = false;
      state.visible = false;
      state.formState = {};
    };
      /* 提交按钮 */
    const onSubmit = () => {
      const httpValue = { ...state.formState };
      props.formItemArr.forEach((K) => {
        if (K.format) {
          if (state.formState[K.field]) {
            httpValue[K.field] = dayjs(
              dayjs(state.formState[K.field]).format('YYYY-MM-DD'),
            ).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
          }
        }
        if (K.format2) {
          if (state.formState[K.field]) {
            httpValue[K.field] = dayjs(
              dayjs(state.formState[K.field]).format('YYYY-MM-DD'),
            ).format('YYYY-MM-DD HH:mm:ss');
          }
        }
        //   是否添加负责人名称
        if (K.addValue) {
          httpValue.principalName = state.peopelOption[K.field].filter((c) => c.value === httpValue.principalId)[0]
            ?.label ?? '';
        }
      });
      if (props.data.configName) {
        delete state.formState.xxxx;
      }

      httpValue.projectId = props.projectid;
      zhttp(httpValue);
    };
    const addSuccess = () => {
      message.success('保存成功');
      state.loading = false;
      if (state.nextCheck) {
        formRef.value.resetFields();
        state.formState = {};
      } else {
        state.visible = false;
      }
      emit('success', false);
      formRef.value.resetFields();
      state.formState = {};
    };
    const zhttp = (httpValue) => {
      formRef.value
        .validate()
        .then(() => {
          state.loading = true;
          if (state.formType === 'edit') {
            /* 编辑 */

            if (props.otherApi.addId === 'itemid') {
              // delete state.formState.id;
              props.otherApi.zkEdit(props.listData[0].id, httpValue)
                .then(() => {
                  addSuccess();
                })
                .catch(() => {
                  state.loading = false;
                });
            }
            if (props.otherApi.addId === 'projectid') {
              // delete state.formState.id;
              props.otherApi.zkEdit(props.projectid, httpValue)
                .then(() => {
                  addSuccess();
                })
                .catch(() => {
                  state.loading = false;
                });
            }
            if (!props.otherApi.addId) {
              props.otherApi.zkEdit(httpValue)
                .then(() => {
                  addSuccess();
                })
                .catch(() => {
                  state.loading = false;
                });
            }
          } else {
            /* 走新增系列 */
            /* 如果需要拼接id和传对象数组,需要传入itemid */
            if (props.otherApi.addId === 'itemidandArr') {
              props.otherApi.zkAdd(props.zkitemids, [httpValue])
                .then(() => {
                  addSuccess();
                })
                .catch(() => {
                  state.loading = false;
                });
              console.log('测试🚀🚀 ~~~ props.otherApi.addIdqqqqqqq ', props.otherApi.addId);
            }
            /* 如果需要拼接projectID传对象 */
            if (props.otherApi.addId === 'projectid') {
              props.otherApi.zkAdd(props.projectid, httpValue)
                .then(() => {
                  addSuccess();
                })
                .catch(() => {
                  state.loading = false;
                });
            }
            /* 如果没有写addid,就只传对象 */
            if (!props.otherApi.addId) {
              props.otherApi.zkAdd(httpValue)
                .then(() => {
                  addSuccess();
                })
                .catch(() => {
                  state.loading = false;
                });
            }
          }
        })
        .catch(() => {
          state.loading = false;
        });
    };
      /* 模糊搜索系列 */
    const handleChange = (value) => {
      try {
        getRole(value, props.projectid);
      } catch (err) {
        message.warning('内部错误');
      }
    };

    const filterHandle = (newvalue, option) => option;
    return {
      ...toRefs(state),
      formRef,
      cancel,
      confirm,
      onSubmit,
      close,
      dayjs,
      handleChange,
      filterHandle,
    };
  },
});
</script>
<style lang="less" scoped>
  //.addNodeModalDrawer {
  //  .ant-checkbox-wrapper,
  //  .ant-form-item-label > label {
  //    color: #444b5e;
  //  }
  //  .nodeForm {
  //    padding: 10px 10px 80px 10px;
  //  }
  //  .ant-form-item-label {
  //    text-align: left;
  //    color: #444b5e;
  //  }
  //  .cancelBtn {
  //    color: #5172dc;
  //    background: #5172dc19;
  //    width: 120px;
  //    border-radius: 4px;
  //  }
  //  .bgDC {
  //    width: 120px;
  //    margin-left: 15px;
  //    border-radius: 4px;
  //  }
  //  .nextCheck {
  //    height: 40px;
  //    line-height: 40px;
  //  }
  //  .nodeItemBtn {
  //    position: fixed;
  //    bottom: 0px;
  //    padding: 20px 0px;
  //    text-align: center;
  //    width: 320px;
  //    height: 80px;
  //    background: #ffffff;
  //    margin-bottom: 0px;
  //  }
  //}
  .nodeForm {
    padding: 10px 10px 80px 10px;
  }
  .ant-form-item{
    display: block;
  }
  .nextCheck {
    height: 40px;
    line-height: 40px;
  }
  .nodeItemBtn {
    position: fixed;
    bottom: 0px;
    padding: 20px 0;
    text-align: center;
    width: 280px;
    height: 80px;
    background: #ffffff;
    margin-bottom: 0px;
  }
  .cancelBtn {
    color: #5172dc;
    background: #5172dc19;
    width: 110px;
    border-radius: 4px;
  }
  .bgDC {
    width: 110px;
    margin-left: 15px;
    border-radius: 4px;
  }
</style>
