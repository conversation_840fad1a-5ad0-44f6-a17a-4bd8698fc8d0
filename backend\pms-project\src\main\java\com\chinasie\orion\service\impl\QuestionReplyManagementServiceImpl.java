package com.chinasie.orion.service.impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.domain.dto.QuestionReplyManagementDTO;
import com.chinasie.orion.domain.entity.QuestionReplyManagement;
import com.chinasie.orion.domain.vo.QuestionReplyManagementVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.QuestionReplyManagementMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.QuestionReplyManagementService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <p>
 * QuestionReplyManagement 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-24 14:05:04
 */
@Service
@Slf4j
public class QuestionReplyManagementServiceImpl extends OrionBaseServiceImpl<QuestionReplyManagementMapper, QuestionReplyManagement> implements QuestionReplyManagementService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public QuestionReplyManagementVO detail(String id, String pageCode) throws Exception {
        QuestionReplyManagement questionReplyManagement = this.getById(id);
        QuestionReplyManagementVO result = BeanCopyUtils.convertTo(questionReplyManagement, QuestionReplyManagementVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param questionReplyManagementDTO
     */
    @Override
    public String create(QuestionReplyManagementDTO questionReplyManagementDTO) throws Exception {
        QuestionReplyManagement questionReplyManagement = BeanCopyUtils.convertTo(questionReplyManagementDTO, QuestionReplyManagement::new);
        this.save(questionReplyManagement);

        String rsp = questionReplyManagement.getId();


        return rsp;
    }

    @Override
    public boolean addList(List<QuestionReplyManagementDTO> questionReplyManagementDTOS) {
        List<QuestionReplyManagement> list = BeanCopyUtils.convertListTo(questionReplyManagementDTOS, QuestionReplyManagement::new);
        Boolean rsp = this.saveBatch(list);
        return rsp;
    }

    @Override
    public List<QuestionReplyManagementVO> getListByNumber(String number) throws Exception {
        LambdaQueryWrapperX<QuestionReplyManagement> condition = new LambdaQueryWrapperX<>(QuestionReplyManagement.class);
        condition.eq(QuestionReplyManagement::getQuestionNumber, number);
        condition.orderByDesc(QuestionReplyManagement::getCreateTime);
        List<QuestionReplyManagement> list = this.list(condition);
        List<QuestionReplyManagementVO> vos = BeanCopyUtils.convertListTo(list, QuestionReplyManagementVO::new);
        return vos;
    }

    /**
     * 编辑
     * <p>
     * * @param questionReplyManagementDTO
     */
    @Override
    public Boolean edit(QuestionReplyManagementDTO questionReplyManagementDTO) throws Exception {
        QuestionReplyManagement questionReplyManagement = BeanCopyUtils.convertTo(questionReplyManagementDTO, QuestionReplyManagement::new);

        this.updateById(questionReplyManagement);

        String rsp = questionReplyManagement.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<QuestionReplyManagementVO> pages(Page<QuestionReplyManagementDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<QuestionReplyManagement> condition = new LambdaQueryWrapperX<>(QuestionReplyManagement.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(QuestionReplyManagement::getCreateTime);


        Page<QuestionReplyManagement> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), QuestionReplyManagement::new));

        PageResult<QuestionReplyManagement> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<QuestionReplyManagementVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<QuestionReplyManagementVO> vos = BeanCopyUtils.convertListTo(page.getContent(), QuestionReplyManagementVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "问题答复导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", QuestionReplyManagementDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        QuestionReplyManagementExcelListener excelReadListener = new QuestionReplyManagementExcelListener();
        EasyExcel.read(inputStream, QuestionReplyManagementDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<QuestionReplyManagementDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("问题答复导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<QuestionReplyManagement> questionReplyManagementes = BeanCopyUtils.convertListTo(dtoS, QuestionReplyManagement::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::QuestionReplyManagement-import::id", importId, questionReplyManagementes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<QuestionReplyManagement> questionReplyManagementes = (List<QuestionReplyManagement>) orionJ2CacheService.get("pmsx::QuestionReplyManagement-import::id", importId);
        log.info("问题答复导入的入库数据={}", JSONUtil.toJsonStr(questionReplyManagementes));

        this.saveBatch(questionReplyManagementes);
        orionJ2CacheService.delete("pmsx::QuestionReplyManagement-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::QuestionReplyManagement-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<QuestionReplyManagement> condition = new LambdaQueryWrapperX<>(QuestionReplyManagement.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(QuestionReplyManagement::getCreateTime);
        List<QuestionReplyManagement> questionReplyManagementes = this.list(condition);

        List<QuestionReplyManagementDTO> dtos = BeanCopyUtils.convertListTo(questionReplyManagementes, QuestionReplyManagementDTO::new);

        String fileName = "问题答复数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", QuestionReplyManagementDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<QuestionReplyManagementVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }


    public static class QuestionReplyManagementExcelListener extends AnalysisEventListener<QuestionReplyManagementDTO> {

        private final List<QuestionReplyManagementDTO> data = new ArrayList<>();

        @Override
        public void invoke(QuestionReplyManagementDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<QuestionReplyManagementDTO> getData() {
            return data;
        }
    }


}
