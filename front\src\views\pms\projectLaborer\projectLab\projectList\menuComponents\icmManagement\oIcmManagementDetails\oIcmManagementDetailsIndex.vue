<template>
  <Layout3
    :defaultActionId="defaultActionId"
    :projectData="state.detailsInfo"
    :menuData="menuData"
    :type="2"
    :onMenuChange="menuChange"
  >
    <template #header-right>
      <BasicButton
        v-if="state.detailsInfo?.status===130"
        icon="sie-icon-tianjiaxinzeng"
        type="primary"
        @click="goReply"
      >
        回复接口
      </BasicButton>

      <BasicButton
        v-if="state.detailsInfo?.status===130"
        @click="goClose"
      >
        关闭接口
      </BasicButton>
      <BasicButton
        v-if="showWorkflowAdd"
        @click="handleAddWorkflow"
      >
        发起流程
      </BasicButton>
    </template>

    <div
      v-if="loading"
      class="w-full h-full flex flex-ac flex-pc"
    >
      <Spin />
    </div>
    <template v-else>
      <Layout3Content v-if="true">
        <!--基本信息-->
        <BasicInfo
          v-if="defaultActionId==='basicInfo'"
        />
        <BasicFileList v-if="defaultActionId==='basicFile'" />
        <RelatedDocument v-if="defaultActionId==='relatedDocument'" />
        <Params v-if="defaultActionId==='params'" />
        <Version v-if="defaultActionId==='version'" />
        <WorkflowView
          v-if="defaultActionId==='workFlow'"
          ref="workflowViewRef"
          :workflow-props="workflowProps"
        />
      </Layout3Content>
      <div
        v-else
        class="w-full h-full flex flex-ac flex-pc"
      >
        <Empty />
      </div>
    </template>
    <template #footer>
      <WorkflowAction
        v-if="state.detailsInfo?.id"
        ref="workflowActionRef"
        :workflow-props="workflowProps"
      />
    </template>
    <RelatedDocumentDrawer
      ref="relatedDocumentRef"
      @update="getDetailData()"
    />
  </Layout3>
</template>

<script setup lang="ts">
import {
  computed, onMounted, provide, reactive, ref, Ref,
} from 'vue';
import {
  BasicButton, Layout3, Layout3Content,
} from 'lyra-component-vue3';
import { Empty, Modal, Spin } from 'ant-design-vue';
import Api from '/@/api';
import { setTitleByRootTabsKey } from '/@/utils';
import { WorkflowAction, WorkflowProps, WorkflowView } from 'lyra-workflow-component-vue3';
import { useRoute } from 'vue-router';
import BasicInfo from './components/BasicInfo.vue';
import BasicFileList from './components/BasicFileList.vue';
import RelatedDocument from './components/RelatedDocument.vue';
import Params from './components/Params.vue';
import Version from './components/Version.vue';
import RelatedDocumentDrawer from './components/relatedDocumentDrawer/Drawer.vue';

const route = useRoute();
const emits = defineEmits([]);
const props = defineProps({});
const defaultActionId: Ref<string> = ref('basicInfo');
const workflowViewRef: Ref = ref();
const workflowActionRef: Ref = ref();
const menuData: Ref<any[]> = ref([
  {
    id: 'basicInfo',
    name: '基本信息',
  },
  {
    id: 'basicFile',
    name: '文件管理',
  },
  // {
  //   id: 'version',
  //   name: '版本管理',
  // },
  {
    id: 'relatedDocument',
    name: '相关单据',
  },
  {
    id: 'params',
    name: '相关参数',
  },
  {
    id: 'workFlow',
    name: '审批流程',
  },
]);
const loading: Ref<boolean> = ref(false);
const relatedDocumentRef = ref(null);
const state = reactive({
  detailsInfo: {} as any,
});
onMounted(() => {
  getDetailData();
});
provide('detailsInfo', computed(() => state.detailsInfo));
provide('getDetailData', getDetailData);

async function getDetailData() {
  loading.value = true;
  try {
    new Api(`/pms/idea-form/${route.params.id}`).fetch('', '', 'GET').then((res) => {
      res.projectCode = res.number;
      res.name = res.desc;
      state.detailsInfo = res;
    });
  } finally {
    loading.value = false;
  }
}

// 显示发起流程按钮
const showWorkflowAdd = computed(() => workflowActionRef.value?.isAdd && state.detailsInfo?.status
    === 101);
const workflowProps = computed<WorkflowProps>(() => ({
  Api,
  businessData: state.detailsInfo,
  afterEvent() {
    workflowViewRef.value?.init();
    getDetailData();
  },
}));

function menuChange({ id }) {
  defaultActionId.value = id;
}

function handleAddWorkflow() {
  workflowActionRef.value?.onAddTemplate({
    messageUrl: route.fullPath,
  });
}

function goClose() {
  Modal.confirm({
    title: '确认关闭提示',
    content: '请确认是否关闭？',
    onOk() {
      return new Api(`/pms/interface-management/close/${state.detailsInfo?.interfaceId}`).fetch('', '', 'PUT').then(() => {
        getDetailData();
      });
    },
    onCancel() {
      Modal.destroyAll();
    },
  });
}

// 回复
function goReply() {
  relatedDocumentRef.value.openDrawer({
    action: 'add',
    info: {
      projectId: state.detailsInfo?.projectId,
      id: state.detailsInfo?.id,
      record: state.detailsInfo,
    },
  });
}
</script>

<style scoped lang="less"></style>
