package com.chinasie.orion.domain.dto.person;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/09/09/10:26
 * @description:
 */
@Data
public class ObjJobDTO implements Serializable {
    @NotNull(message = "关系ID不能为空")
    private  String id ;
    @ApiModelProperty("计划开始/进场时间")
    @NotNull(message = "计划开始/进场时间不能为空")
    private Date inDate;
    @ApiModelProperty("计划结束/出场时间")
    @NotNull(message = "计划结束/出场时间不能为空")
    private Date outDate;
    @ApiModelProperty("修改的数据类型：0--人员进出，1--作业关系")
    private Integer type=1;
}
