package com.chinasie.orion.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.util.List;
import java.util.stream.Collectors;

/**
 * InterfaceManagement DTO对象
 *
 * <AUTHOR>
 * @since 2024-01-29 22:08:44
 */
@ApiModel(value = "InterfaceManagementDTO对象", description = "接口管理")
@Data
public class InterfaceManagementDTO extends ObjectDTO implements Serializable {

    /**
     * 发布单位
     */
    @ApiModelProperty(value = "发布单位")
    private String publishOrgName;

    /**
     * 接受单位
     */
    @ApiModelProperty(value = "接受单位")
    private String receiveOrgName;

    /**
     * 接口类型
     */
    @ApiModelProperty(value = "接口类型")
    @NotEmpty(message = "接口类型不能为空")
    private String type;

    /**
     * 流水号
     */
    @ApiModelProperty(value = "流水号")
    @NotEmpty(message = "流水号不能为空")
    private String number;

    /**
     * 发布部门
     */
    @ApiModelProperty(value = "发布部门")

    private String publishDeptId;

    /**
     * 接受部门
     */
    @ApiModelProperty(value = "接受部门")
    private String reviewDeptIds;

    /**
     * 回复时间
     */
    @ApiModelProperty(value = "回复时间")
    @NotNull(message = "要求回复时间")
    private Date replyTime;

    /**
     * 主办人
     */
    @ApiModelProperty(value = "主办人")
    @NotEmpty(message = "主办人不能为空")
    private String manUser;

    /**
     * 协办
     */
    @ApiModelProperty(value = "协办")
    private String cooperationUsers;

    /**
     * 第三方检查备案
     */
    @ApiModelProperty(value = "第三方检查备案")
    private String thirdVerify;

    /**
     * 专业代码
     */
    @ApiModelProperty(value = "专业代码")
    private String specialtyCode;

    /**
     * 接口描述
     */
    @ApiModelProperty(value = "接口描述")
    @NotEmpty(message = "接口描述不能为空")
    private String desc;

    @ApiModelProperty(value = "接受部门列表")
    @Size(min =1,message = "接受部门不能为空")
    private List<String> reviewDeptIdList;


    @ApiModelProperty(value = "协办人列表")
    @Size(min =1,message = "协办人不能为空")
    private List<String> cooperationUserIdList;

    @ApiModelProperty(value = "单据类型")
    private String formType;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "数据Id (项目id,产品Id)")
    private String dataId;

    @ApiModelProperty(value = "数据类型className")
    private String dataClassName;

    @ApiModelProperty(value = "接口状态")
    private String interfaceState;
}
