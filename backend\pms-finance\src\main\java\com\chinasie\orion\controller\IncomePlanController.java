package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.IncomePlanDTO;
import com.chinasie.orion.domain.vo.IncomePlanVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.IncomePlanService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * IncomePlan 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29 19:14:00
 */
@RestController
@RequestMapping("/incomePlan")
@Api(tags = "收入计划填报")
public class  IncomePlanController  {

    @Autowired
    private IncomePlanService incomePlanService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "收入计划填报", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<IncomePlanVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        IncomePlanVO rsp = incomePlanService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param incomePlanDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#incomePlanDTO.name}}】", type = "收入计划填报", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody IncomePlanDTO incomePlanDTO) throws Exception {
        String rsp =  incomePlanService.create(incomePlanDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param incomePlanDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#incomePlanDTO.name}}】", type = "收入计划填报", subType = "编辑", bizNo = "{{#incomePlanDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  IncomePlanDTO incomePlanDTO) throws Exception {
        Boolean rsp = incomePlanService.edit(incomePlanDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "收入计划填报", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = incomePlanService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "收入计划填报", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = incomePlanService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "收入计划填报", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<IncomePlanVO>> pages(@RequestBody Page<IncomePlanDTO> pageRequest) throws Exception {
        Page<IncomePlanVO> rsp =  incomePlanService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("收入计划填报导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "收入计划填报", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        incomePlanService.downloadExcelTpl(response);
    }

    @ApiOperation("收入计划填报导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "收入计划填报", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = incomePlanService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("收入计划填报导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "收入计划填报", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  incomePlanService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消收入计划填报导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "收入计划填报", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  incomePlanService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("收入计划填报导出（Excel）")
    @PostMapping(value = "/export/excel")
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "收入计划填报", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<String> ids, HttpServletResponse response) throws Exception {
        incomePlanService.exportByExcel(ids, response);
    }

    @ApiOperation(value = "数据推送")
    @RequestMapping(value = "/pushData", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】数据推送", type = "收入计划填报", subType = "数据推送", bizNo = "")
    public ResponseDTO<Boolean> pushData(@RequestParam String date)  throws Exception {
        Boolean rsp = incomePlanService.pushData(date);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("收入计划填报开启调整")
    @PostMapping(value = "/adjustment")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】开启调整", type = "收入计划填报", subType = "开启调整", bizNo = "")
    public ResponseDTO<Boolean> adjustment(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = incomePlanService.adjustment(ids);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("收入计划填报重新编制")
    @PostMapping(value = "/authorized")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】重新编制", type = "收入计划填报", subType = "重新编制", bizNo = "")
    public ResponseDTO<Boolean> authorized(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = incomePlanService.authorized(ids);
        return new ResponseDTO<>(rsp);
    }
}
