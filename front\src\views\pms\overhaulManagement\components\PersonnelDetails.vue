<script setup lang="ts">
import {
  BasicCard, openDrawer, openFile, OrionTable,
} from 'lyra-component-vue3';
import {
  h, reactive, ref, Ref, watchEffect,
} from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import TableDrawer from '/@/views/pms/technicalStaffAllocation/components/TableDrawer.vue';
import { Popover } from 'ant-design-vue';

const props = defineProps<{
  record: Record<string, any>
}>();

const fetching: Ref<boolean> = ref(false);
const personDetails = reactive({});

// 获取人员台账信息
async function getPersonDetails() {
  const result = await new Api(`/pms/person-manage-ledger/${props.record.personLedgerId}`).fetch({}, '', 'GET');
  Object.assign(personDetails, result);
}

watchEffect(() => {
  getPersonDetails();
});

const basicInfo = reactive({
  gap: '10px',
  list: [
    {
      label: '员工号',
      field: 'number',
    },
    {
      label: '姓名',
      field: 'name',
    },
    {
      label: '性别',
      field: 'sex',
    },
    {
      label: '人员性质',
      field: 'nature',
    },
    {
      label: '公司',
      field: 'companyName',
    },
    {
      label: '部门',
      field: 'deptName',
    },
    {
      label: '研究所',
      field: 'instituteName',
    },
    {
      label: '民族',
      field: 'nation',
    },
    {
      label: '政治面貌',
      field: 'politicalAffiliation',
    },
    {
      label: '籍贯',
      field: 'homeTown',
    },
    {
      label: '出生地',
      field: 'birthPlace',
    },
    {
      label: '现任职务',
      field: 'nowPosition',
    },
    {
      label: '职称',
      field: 'jobTitle',
    },
    {
      label: '参加工作时间',
      field: 'joinWorkTime',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '加入ZGH时间',
      field: 'addZghTime',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '加入本单位时间',
      field: 'addUnitTime',
      formatTime: 'YYYY-MM-DD',
    },
  ],
  dataSource: personDetails,
});

const trainTableOptions = {
  showToolButton: false,
  showTableSetting: false,
  showSmallSearch: false,
  isSpacing: false,
  pagination: false,
  api: () => new Api('/pms/personTrainInfoRecord/list').fetch({
    userCode: props.record?.userCode,
  }, '', 'POST'),
  columns: [
    {
      title: '培训名称',
      dataIndex: 'trainName',
    },
    {
      title: '培训基地',
      dataIndex: 'baseName',
    },
    {
      title: '培训课时',
      dataIndex: 'lessonHour',
    },
    {
      title: '培训完成日期',
      dataIndex: 'endDate',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '培训讲师',
      dataIndex: 'trainLecturer',
    },
    {
      title: '到期日期',
      dataIndex: 'expireTime',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '是否等效',
      dataIndex: 'isEquivalent',
      customRender({ text }) {
        return text ? '是' : '否';
      },
    },
    {
      title: '等效认定基地',
      dataIndex: 'equRecordVOList',
      customRender({ text }) {
        return h('div', {
          class: 'flex-te action-btn',
          onClick() {
            openDrawer({
              title: '培训等效认定',
              width: 1000,
              content() {
                return h(TableDrawer, {
                  data: text || [],
                });
              },
              footer: {
                isOk: false,
                canText: '返回',
              },
            });
          },
        }, '查看');
      },
    },
    {
      title: '培训内容',
      dataIndex: 'content',
    },
    {
      title: '培训记录',
      dataIndex: 'fileVOList',
      customRender({ text }) {
        return h(Popover, { title: '附件' }, {
          default: () => h('div', { class: 'flex-te action-btn' }, '附件列表'),
          content: () => (text instanceof Array ? text : [])?.map((item: any) => h('p', {
            class: 'action-btn',
            onClick() {
              openFile(item);
            },
          }, item.name)),
        });
      },
    },
  ],
};

const certificateTableOptions = {
  showToolButton: false,
  showTableSetting: false,
  showSmallSearch: false,
  isSpacing: false,
  pagination: false,
  api: () => new Api(`/pms/basic-user-certificate/user/certificate/list?userCode=${props.record?.userCode}`).fetch('', '', 'POST'),
  columns: [
    {
      title: '证书类型',
      dataIndex: 'certificateTypeName',
    },
    {
      title: '证书名称',
      dataIndex: 'certificateName',
    },
    {
      title: '证书等级',
      dataIndex: 'certificateLevelName',
    },
    {
      title: '发证机构',
      dataIndex: 'issuingAuthority',
    },
    {
      title: '获取日期',
      dataIndex: 'obtainDate',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '复审日期',
      dataIndex: 'reviewDate',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '证书信息',
      dataIndex: 'fileVOList',
      customRender({ text }) {
        return h(Popover, { title: '附件' }, {
          default: () => h('div', { class: 'flex-te action-btn' }, '附件列表'),
          content: () => (text instanceof Array ? text : [])?.map((item: any) => h('p', {
            class: 'action-btn',
            onClick() {
              openFile(item);
            },
          }, item.name)),
        });
      },
    },
  ],
};

const authTableOptions = {
  showTableSetting: false,
  showSmallSearch: false,
  showToolButton: false,
  pagination: false,
  isSpacing: false,
  api: () => new Api('/pms/personJobPostAuthorize/list').fetch({
    userCode: props.record?.userCode,
  }, '', 'POST'),
  columns: [
    {
      title: '所属基地',
      dataIndex: 'baseName',
    },
    {
      title: '作业岗位',
      dataIndex: 'jobPostName',
    },
    {
      title: '授权到期日期',
      dataIndex: 'endDate',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '授权状态',
      dataIndex: 'authorizeStatusName',
    },
    {
      title: '是否等效',
      dataIndex: 'isEquivalent',
      customRender({ text }) {
        return text ? '是' : '否';
      },
    },
    {
      title: '等效认定基地',
      dataIndex: 'personJobPostEquList',
      customRender({ text }) {
        return h('div', {
          class: 'flex-te action-btn',
          onClick() {
            openDrawer({
              title: '岗位等效认定',
              width: 1000,
              content() {
                return h(TableDrawer, {
                  data: text || [],
                });
              },
              footer: {
                isOk: false,
                canText: '返回',
              },
            });
          },
        }, '查看');
      },
    },
    {
      title: '授权记录',
      dataIndex: 'fileVOList',
      customRender({ text }) {
        return h(Popover, { title: '附件' }, {
          default: () => h('div', { class: 'flex-te action-btn' }, '附件列表'),
          content: () => (text instanceof Array ? text : [])?.map((item: any) => h('p', {
            class: 'action-btn',
            onClick() {
              openFile(item);
            },
          }, item.name)),
        });
      },
    },
  ],
};

const personIn = reactive({
  list: [
    {
      label: '计划进场日期',
      field: 'inDate',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '实际进场日期',
      field: 'actInDate',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '主工作中心',
      field: 'mainWorkCenter',
    },
    {
      label: '涉及控制作业',
      field: 'designCtrlZoneOp',
      isBoolean: true,
    },
    {
      label: '身高/米',
      field: 'heightStr',
    },
    {
      label: '体重/千克',
      field: 'weightStr',
    },
    {
      label: '化学品/毒物使用或者接触作业',
      field: 'chemicalToxinUseJob',
      isBoolean: true,
    },
    {
      label: '高剂量人员',
      field: 'isHeightMeasurePerson',
      isBoolean: true,
    },
    {
      label: '新人',
      field: 'newcomer',
      isBoolean: true,
    },
    {
      label: '一年内参与过集团内大修',
      field: 'isJoinYearMajorRepair',
      isBoolean: true,
    },
    {
      label: '接口人',
      field: 'contactUserName',
    },
    {
      label: '是否常驻基地',
      field: 'isBasePermanent',
      isBoolean: true,
    },
  ],
  dataSource: personDetails,
});

const personOut = reactive({
  list: [
    {
      label: '计划离场日期',
      field: 'outDate',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '实际离场日期',
      field: 'actOutDate',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '离场原因',
      field: 'leaveReasonName',
    },
    {
      label: '是否再次入场',
      field: 'isAgainIn',
      isBoolean: true,
    },
    {
      label: '是否完成离场工作交接、离场WBC测量（必要时）',
      field: 'isFinishOutHandover',
      isBoolean: true,
      gridColumn: '1/3',
    },
  ],
  dataSource: personDetails,
});
</script>

<template>
  <BasicCard
    title="基本信息"
    :is-border="false"
    :isContentSpacing="false"
    :grid-content-props="basicInfo"
  />
  <BasicCard
    title="培训信息"
    :isBorder="false"
    :isContentSpacing="false"
  >
    <div style="height: 200px;overflow: hidden">
      <OrionTable :options="trainTableOptions" />
    </div>
  </BasicCard>
  <BasicCard
    title="证书信息"
    :isBorder="false"
    :isContentSpacing="false"
  >
    <div style="height: 200px;overflow: hidden">
      <OrionTable :options="certificateTableOptions" />
    </div>
  </BasicCard>
  <BasicCard
    title="岗位授权信息"
    :isBorder="false"
    :isContentSpacing="false"
  >
    <div style="height: 200px;overflow: hidden">
      <OrionTable :options="authTableOptions" />
    </div>
  </BasicCard>
  <!--  <BasicCard-->
  <!--    title="人员进场信息"-->
  <!--    :isBorder="false"-->
  <!--    :isContentSpacing="false"-->
  <!--    :grid-content-props="personIn"-->
  <!--  />-->
  <!--  <BasicCard-->
  <!--    title="人员离场信息"-->
  <!--    :isBorder="false"-->
  <!--    :isContentSpacing="false"-->
  <!--    :grid-content-props="personOut"-->
  <!--  />-->
</template>

<style scoped lang="less">

</style>
