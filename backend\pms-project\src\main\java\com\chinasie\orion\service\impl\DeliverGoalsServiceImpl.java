package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.api.code.domain.vo.CodeSegmentVO;
import com.chinasie.orion.bo.CodeBo;
import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.bo.UserBo;
import com.chinasie.orion.constant.ClassNameConstant;
import com.chinasie.orion.domain.dto.DeliverGoalsDTO;
import com.chinasie.orion.domain.entity.DeliverGoals;
import com.chinasie.orion.domain.entity.DeliverGoalsToDeliverable;
import com.chinasie.orion.domain.vo.DeliverGoalsVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.OrderItem;
import com.chinasie.orion.repository.DeliverGoalsRepository;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.service.DeliverGoalsService;
import com.chinasie.orion.service.DeliverGoalsToDeliverableService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.lang.String;
import java.util.*;
import java.util.stream.Collectors;

import static com.chinasie.orion.constant.DictConstant.IED_TYPE;

/**
 * <p>
 * DeliverGoals 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29 13:50:13
 */
@Service
public class DeliverGoalsServiceImpl extends OrionBaseServiceImpl<DeliverGoalsRepository, DeliverGoals> implements DeliverGoalsService {

    @Resource
    private DeliverGoalsRepository deliverGoalsRepository;

    @Autowired
    private CodeBo codeBo;

    @Autowired
    private DictBo dictBo;

    @Autowired
    private UserBo userBo;

    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Autowired
    private DeliverGoalsToDeliverableService deliverGoalsToDeliverableService;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    public DeliverGoalsVO detail(String id) throws Exception {
        DeliverGoals deliverGoals = deliverGoalsRepository.selectById(id);
        DeliverGoalsVO deliverGoalsVO = BeanCopyUtils.convertTo(deliverGoals, DeliverGoalsVO::new);
        setDeliverGoalsVO(Collections.singletonList(deliverGoalsVO));
        return deliverGoalsVO;
    }

    /**
     *  新增
     *
     * * @param deliverGoalsDTO
     */
    @Override
    public String create(DeliverGoalsDTO deliverGoalsDTO) throws Exception {
        checkParams(deliverGoalsDTO);
        DeliverGoals deliverGoals =BeanCopyUtils.convertTo(deliverGoalsDTO,DeliverGoals::new);
        //生成编码
        List<CodeSegmentVO> codeRuleList = codeBo.getCodeRuleList(ClassNameConstant.DELIVER_GOALS, ClassNameConstant.NUMBER);
        if (!CollectionUtils.isEmpty(codeRuleList)) {
            String code = codeBo.getCode(codeRuleList);
            deliverGoals.setNumber(code);
        }
        deliverGoalsRepository.insert(deliverGoals);
        return deliverGoals.getId();
    }

    public void checkParams(DeliverGoalsDTO deliverGoalsDTO) {
        if (StrUtil.isBlank(deliverGoalsDTO.getProjectId())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "所属项目不能为空");
        }
        if (StrUtil.isBlank(deliverGoalsDTO.getName())) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_NAME_NULL, "名字不能为空");
        }
        if (ObjectUtil.isEmpty(deliverGoalsDTO.getPlanSubmitTime())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "计划提交时间不能为空");
        }
        if (StrUtil.isBlank(deliverGoalsDTO.getWriter())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "编写人不能为空");
        }
        if (StrUtil.isBlank(deliverGoalsDTO.getResPerson())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "当前责任方不能为空");
        }
        if (StrUtil.isBlank(deliverGoalsDTO.getResDept())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "责任部门不能为空");
        }
    }

    /**
     *  编辑
     *
     * * @param deliverGoalsDTO
     */
    @Override
    public Boolean edit(DeliverGoalsDTO deliverGoalsDTO) throws Exception {
        checkParams(deliverGoalsDTO);
        DeliverGoals deliverGoals =BeanCopyUtils.convertTo(deliverGoalsDTO,DeliverGoals::new);
        deliverGoals.setNumber(null);
        int update =  deliverGoalsRepository.updateById(deliverGoals);
        return SqlHelper.retBool(update);
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        long count = this.count(new LambdaQueryWrapperX<>(DeliverGoals.class).in(DeliverGoals::getId, ids).eq(DeliverGoals::getStatus, 110));
        if (count > 0) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "存在数据状态为流程中， 删除失败");
        }
        int delete = deliverGoalsRepository.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    public Page<DeliverGoalsVO> pages(Page<DeliverGoalsDTO> pageRequest) throws Exception {
        Page<DeliverGoals> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        LambdaQueryWrapperX<DeliverGoals> wrapperX = new LambdaQueryWrapperX<>(DeliverGoals.class);
        DeliverGoalsDTO query = pageRequest.getQuery();
        if (ObjectUtil.isNotEmpty(query)) {
            wrapperX.eqIfPresent(DeliverGoals::getProjectId, query.getProjectId());
        }
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), wrapperX);
        }
        List<OrderItem> orders = pageRequest.getOrders();
        if (CollectionUtil.isEmpty(orders)) {
            wrapperX.orderByDesc(DeliverGoals::getCreateTime);
        } else {
            SearchConditionUtils.analysisOrder(orders, wrapperX);
        }
        PageResult<DeliverGoals> page = deliverGoalsRepository.selectPage(realPageRequest,wrapperX);

        Page<DeliverGoalsVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        if (CollectionUtil.isNotEmpty(page.getContent())) {
            List<DeliverGoalsVO> list =BeanCopyUtils.convertListTo(page.getContent(), DeliverGoalsVO::new);
            setDeliverGoalsVO(list);
            pageResult.setContent(list);
        }

        return pageResult;
    }

    /**
     * 设置ied内容
     * @param deliverGoalsVOList
     * @throws Exception
     */
    public void setDeliverGoalsVO(List<DeliverGoalsVO> deliverGoalsVOList) throws Exception {
        List<String> userIdList = new ArrayList<>();
        List<String> deptIdList = new ArrayList<>();
        List<String> idList = new ArrayList<>();
        for (DeliverGoalsVO deliverGoalsVO : deliverGoalsVOList) {
            userIdList.add(deliverGoalsVO.getResPerson());
            userIdList.add(deliverGoalsVO.getWriter());
            userIdList.add(deliverGoalsVO.getOwnerId());
            deptIdList.add(deliverGoalsVO.getResDept());
            idList.add(deliverGoalsVO.getId());
        }
        Map<String, String> nameByUserIdMap = userBo.getNameByUserIdMap(userIdList);
        Map<String, String> nameByDeptIdMap = new HashMap<>();
        deptIdList = deptIdList.stream().filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(deptIdList)) {
            nameByDeptIdMap.putAll(deptRedisHelper.getDeptByIds(deptIdList)
                    .stream().collect(Collectors.toMap(DeptVO::getId, DeptVO::getName)));
        }
        List<String> existRelationIdList = deliverGoalsToDeliverableService.getListByDeliverGoals(idList).stream().map(DeliverGoalsToDeliverable::getFromId).distinct().collect(Collectors.toList());
        Map<String, String> typeMap = dictBo.getDictList(IED_TYPE).stream().collect(Collectors.toMap(DictValueVO::getId, DictValueVO::getDescription));
        for (DeliverGoalsVO deliverGoalsVO : deliverGoalsVOList) {
            deliverGoalsVO.setResPersonName(nameByUserIdMap.get(deliverGoalsVO.getResPerson()));
            deliverGoalsVO.setWriterName(nameByUserIdMap.get(deliverGoalsVO.getWriter()));
            deliverGoalsVO.setOwnerName(nameByUserIdMap.get(deliverGoalsVO.getOwnerId()));
            deliverGoalsVO.setResDeptName(nameByDeptIdMap.get(deliverGoalsVO.getResDept()));
            if (StrUtil.isBlank(deliverGoalsVO.getExistDeliverable())) {
                deliverGoalsVO.setExistDeliverable(existRelationIdList.contains(deliverGoalsVO.getId()) ? "是" : "否");
            }
            deliverGoalsVO.setTypeName(typeMap.get(deliverGoalsVO.getType()));
        }
    }
}
