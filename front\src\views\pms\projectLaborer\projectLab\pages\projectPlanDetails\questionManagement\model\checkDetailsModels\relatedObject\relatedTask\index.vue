<template>
  <div>
    <OrionTable
      ref="tableRef"
      :options="options"
    />
  </div>
</template>

<script lang="ts">
import {
  computed, defineComponent, h, reactive, toRefs,
} from 'vue';
import { OrionTable, DataStatusTag } from 'lyra-component-vue3';
import dayjs from 'dayjs';
import Api from '/@/api';
export default defineComponent({
  name: 'Index',
  components: { OrionTable },
  props: {
    id: {},
  },
  emits: [],
  setup(props) {
    const state = reactive({
      tableRef: null,
      options: {
        deleteToolButton: 'add|delete|enable|disable',
        rowSelection: false,
        showSmallSearch: false,
        showIndexColumn: false,
        pagination: false,
        smallSearchField: ['name'],
        // auto: {
        //   url: '/pas/question-type-attribute',
        //   params: {
        //     query: {
        //     },
        //   },
        // },
        // api:()=>new Api(`/pas/question-management/relation/plan/${props.id}`).fetch('','','POST'),
        api: (P) => new Api(`/pas/question-management/relation/plan/${props.id}`).fetch(P, '', 'POST'),
        columns: [
          {
            title: '编号',
            dataIndex: 'number',
            align: 'left',
            key: 'number',

            width: '120px',
            // sorter: true,
            ellipsis: true,
          },
          {
            title: '名称',
            dataIndex: 'name',
            key: 'name',
            // customRender: function ({ record, text }) {
            //   return h(
            //       'span',
            //       {
            //         class: computed(()=>isPower('WT_container_button_03', state.powerData)) ?'action-btn':'',
            //         title: text,
            //         onClick: function (e) {
            //           if(isPower('WT_container_button_03', state.powerData)){
            //             checkData2(record)
            //           }
            //           e.stopPropagation();
            //         }
            //       },
            //       text
            //   );
            // },

            width: '240px',
            align: 'left',
            // slots: { customRender: 'name' },
            // sorter: true,
            ellipsis: true,
          },

          {
            title: '计划类型',
            dataIndex: 'planTypeName',
            key: 'planType',
            width: '100px',
            margin: '0 20px 0 0',
            align: 'left',
            slots: { customRender: 'planTypeName' },
            // sorter: true,
            ellipsis: true,
          },
          {
            title: '负责人',
            dataIndex: 'principalName',
            key: 'principalId',

            width: '80px',
            align: 'left',
            // sorter: true,
            ellipsis: true,
            slots: { customRender: 'principalName' },
          },
          {
            title: '计划进度',
            dataIndex: 'scheduleName',
            key: 'schedule',
            width: '90px',
            align: 'left',
            slots: { customRender: 'scheduleName' },
            // sorter: true,
            ellipsis: true,
          },
          {
            title: '优先级',
            dataIndex: 'priorityLevelName',
            key: 'priorityLevel',

            width: '80px',
            align: 'left',
            slots: { customRender: 'priorityLevelName' },
            // sorter: true,
            ellipsis: true,
          },
          {
            title: '状态',
            dataIndex: 'statusName',
            key: 'status',

            width: '80px',
            align: 'left',
            sorter: true,
            ellipsis: true,
            slots: { customRender: 'statusName' },
          },
          {
            title: '开始日期',
            dataIndex: 'planPredictStartTime',
            key: 'planPredictStartTime',

            width: '130px',
            align: 'left',
            slots: { customRender: 'planPredictStartTime' },
            sorter: true,
            ellipsis: true,
          },

          {
            title: '结束日期',
            dataIndex: 'planPredictEndTime',
            key: 'planPredictEndTime',

            width: '150px',
            align: 'left',
            sorter: true,
            ellipsis: true,
            slots: { customRender: 'planPredictEndTime' },
          },
        ],
      },
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang="less"></style>
