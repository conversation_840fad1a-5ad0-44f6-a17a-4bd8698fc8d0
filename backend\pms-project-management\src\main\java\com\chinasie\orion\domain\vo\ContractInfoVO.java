package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class ContractInfoVO {

    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    @ApiModelProperty(value = "合同名称")
    private String contractName;

    @ApiModelProperty(value = "预计合同开始时间")
    private Date estimatedStartTime;

    @ApiModelProperty(value = "预计合同结束时间")
    private Date estimatedEndTime;

    @ApiModelProperty(value = "合同状态码")
    private Integer contractStatus;

    @ApiModelProperty(value = "合同状态名称")
    private String contractStatusName;

    @ApiModelProperty(value = "合同类型")
    private String contractType;

    @ApiModelProperty(value = "年份")
    private Integer year;

    @ApiModelProperty(value = "合同用人计划状态")
    private Integer status;

    @ApiModelProperty(value = "合同用人计划状态")
    private String statusName;
}
