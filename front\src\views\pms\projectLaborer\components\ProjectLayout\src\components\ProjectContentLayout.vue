<template>
  <div class="content-wrap">
    <ContentTitle v-bind="$attrs">
      <template
        v-for="item in Object.keys($slots)"
        #[item]="data"
      >
        <template v-if="item !== 'default'">
          <slot
            v-bind="data"
            :name="item"
          />
        </template>
      </template>
    </ContentTitle>
    <div class="content-main">
      <div
        v-if="$attrs.menuData || $slots.left"
        class="left"
      >
        <slot name="left">
          <BasicMenu
            v-if="$attrs.menuData"
            v-bind="$attrs"
          >
            <template
              v-for="item in Object.keys($slots)"
              #[item]="data"
            >
              <template v-if="item !== 'default'">
                <slot
                  v-bind="data"
                  :name="item"
                />
              </template>
            </template>
          </BasicMenu>
        </slot>
      </div>
      <div class="content">
        <slot />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import ContentTitle from './ContentTitle.vue';
import {
  BasicTable, Layout, useActionsRecord, OrionTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer,
} from 'lyra-component-vue3';
import BasicMenu from '/@/views/pms/projectLaborer/components/BasicMenu';
export default defineComponent({
  name: 'ProjectContentLayout',
  components: {
    ContentTitle,
    BasicMenu,
  },
});
</script>

<style lang="less" scoped>
  .content-wrap {
    display: flex;
    flex-direction: column;
    height: 100%;

    > .content-main {
      flex: 1;
      height: 1px;
      overflow: auto;
      display: flex;

      > .left {
        width: 258px;
        border-right: 1px solid #e9ecf2;
        background: #f4f4f4;
      }

      > .content {
        flex: 1;
        overflow: auto;
      }
    }
  }
</style>
