package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/07/14:57
 * @description:
 */

@ApiModel(value = "SafetyQualityEnvDTO对象", description = "安质环")
@Data
@ExcelIgnoreUnannotated
public class SafetyQualityEnvDTO extends ObjectDTO implements Serializable {

    /**
     * 事件主题
     */
    @ApiModelProperty(value = "事件主题")
    @ExcelProperty(value = "事件主题 ", index = 0)
    private String eventTopic;

    /**
     * 事件等级
     */
    @ApiModelProperty(value = "事件等级")
    @ExcelProperty(value = "事件等级 ", index = 1)
    private String eventLevel;

    /**
     * 事件地点
     */
    @ApiModelProperty(value = "事件地点")
    @ExcelProperty(value = "事件地点 ", index = 2)
    private String eventLocation;

    /**
     * 事件位置
     */
    @ApiModelProperty(value = "事件位置")
    @ExcelProperty(value = "事件位置 ", index = 3)
    private String eventPosition;

    /**
     * 分类类型
     */
    @ApiModelProperty(value = "分类类型")
    @ExcelProperty(value = "分类类型 ", index = 4)
    private String classificationType;

    /**
     * 隐患类型
     */
    @ApiModelProperty(value = "隐患类型")
    @ExcelProperty(value = "隐患类型 ", index = 5)
    private String hiddenDangerType;

    /**
     * 事发日期
     */
    @ApiModelProperty(value = "事发日期")
    @ExcelProperty(value = "事发日期 ", index = 6)
    private Date occurrenceDate;

    /**
     * 责任中心
     */
    @ApiModelProperty(value = "责任中心")
    @ExcelProperty(value = "责任中心 ", index = 7)
    private String rspCenter;

    /**
     * 是否大修
     */
    @ApiModelProperty(value = "是否大修")
    @ExcelProperty(value = "是否大修 ", index = 8)
    private Boolean isMajorRepair;

    /**
     * 隐患/事件领域
     */
    @ApiModelProperty(value = "隐患/事件领域")
    @ExcelProperty(value = "隐患/事件领域 ", index = 9)
    private String hiddenEvent;

    /**
     * 事件类型
     */
    @ApiModelProperty(value = "事件类型")
    @ExcelProperty(value = "事件类型 ", index = 10)
    private String eventType;

    /**
     * 是否已关闭
     */
    @ApiModelProperty(value = "是否已关闭")
    @ExcelProperty(value = "是否已关闭 ", index = 11)
    private Boolean isClosed;

    /**
     * 当前流程
     */
    @ApiModelProperty(value = "当前流程")
    @ExcelProperty(value = "当前流程 ", index = 12)
    private String currentProcess;

    /**
     * 金字塔类别
     */
    @ApiModelProperty(value = "金字塔类别")
    @ExcelProperty(value = "金字塔类别 ", index = 13)
    private String pyramidCategory;

    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    @ExcelProperty(value = "大修轮次 ", index = 14)
    private String majorRepairTurn;

    /**
     * 是否考核
     */
    @ApiModelProperty(value = "是否考核")
    @ExcelProperty(value = "是否考核 ", index = 15)
    private Boolean isAssessed;

    /**
     * 考核级别
     */
    @ApiModelProperty(value = "考核级别")
    @ExcelProperty(value = "考核级别 ", index = 16)
    private String assessmentLevel;

    /**
     * 隐患编号
     */
    @ApiModelProperty(value = "隐患编号")
    @ExcelProperty(value = "隐患编号 ", index = 17)
    private String hiddenDangerCode;



    @ApiModelProperty(value = "检查人ID")
    private String reviewerId;


    @ApiModelProperty(value = "检查人编号")
    private String reviewerNumber;

    @ApiModelProperty(value = "检查人名称")
    private String reviewerName;

    @ApiModelProperty(value = "检查人所在部门Id")
    private String deptId;


    @ApiModelProperty(value = "检查人所在部门")
    private String deptName;


    @ApiModelProperty(value = "直接责任部门Id")
    private String rspDeptId;


    @ApiModelProperty(value = "直接责任部门名称")
    private String rspDeptName;
    /**
     * 事件类型
     */
    @ApiModelProperty(value = "事件描述")
    private String eventDesc;

    /**
     * 事件地点
     */
    @ApiModelProperty(value = "事件地点编码")
    private String eventLocationCode;

    /**
     * 责任中心
     */
    @ApiModelProperty(value = "责任中心id")
    private String rspCenterId;
}

