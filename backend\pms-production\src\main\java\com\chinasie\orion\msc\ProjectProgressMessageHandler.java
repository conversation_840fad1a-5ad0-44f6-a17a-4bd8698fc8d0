package com.chinasie.orion.msc;

import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.constant.MessageNodeDict;

import com.chinasie.orion.domain.entity.ImportantProject;
import com.chinasie.orion.msc.api.MscBuildHandler;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
/**
 * <AUTHOR>
 */
public class ProjectProgressMessageHandler implements MscBuildHandler<ImportantProject> {
    @Override
    public SendMessageDTO buildMsc(ImportantProject importantProject, Object... objects) {
        Map<String, Object> message = new HashMap<>();
        message.put("$projectName$",importantProject.getProjectName());

        SendMessageDTO sendMessageDTO = SendMessageDTO.builder()
                .messageMap(message)
                .titleMap(message)
                .messageUrl("/pms/trainManage/")
                .messageUrlName("重大项目")
                .recipientIdList(Collections.singletonList(importantProject.getRspUserId()))
                .senderTime(new Date())
                .senderId(importantProject.getCreatorId())
                .orgId(importantProject.getOrgId())
                .platformId(importantProject.getPlatformId())
                .build();

        return sendMessageDTO;
    }

    @Override
    public String support() {
        return MessageNodeDict.NODE_PROJECT_PROGRESS;
    }
}
