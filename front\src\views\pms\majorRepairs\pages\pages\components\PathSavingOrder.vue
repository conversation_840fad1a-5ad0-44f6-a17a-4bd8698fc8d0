<script setup lang="ts">
import { BasicCard, UploadList } from 'lyra-component-vue3';
import {
  inject, reactive, readonly, Ref,
} from 'vue';

const detailsData: Record<string, any> = inject('detailsData', readonly({}));
const powerData: Ref = inject('powerData');
const orderInfo = reactive({
  list: [
    {
      label: '工单号',
      field: 'jobManageNumber',
    },
    {
      label: '工作抬头',
      field: 'workJobTitle',
    },
    {
      label: '工作名称',
      field: 'jobManageName',
    },
    {
      label: '大修轮次',
      field: 'majorRepairTurn',
    },
    {
      label: '是否重大项目',
      field: 'isMajorProject',
      isBoolean: true,
    },
    {
      label: '实际结束时间',
      field: 'actualEndTime',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '是否可沿用',
      field: 'isContinueUse',
      isBoolean: true,
    },
  ],
  dataSource: detailsData,
});

const pathInfo = reactive({
  list: [
    {
      label: '关键路径是否节约',
      field: 'isEconomize',
      isBoolean: true,
    },
    {
      label: '编号',
      field: 'number',
    },
    {
      label: '优化领域',
      field: 'optimizeFieldName',
    },
    {
      label: '大修类型',
      field: 'majorRepairTypeName',
    },
    {
      label: '应用机组类型',
      field: 'applicationCrewName',
    },
    {
      label: '创优技术或工作',
      field: 'innTechOrWork',
    },
    {
      label: '计划工期（H）',
      field: 'planDuration',
    },
    {
      label: '实际执行用时（H）',
      field: 'actualExeDuration',
    },
    {
      label: '节约（H）',
      field: 'economizeDuration',
    },
    {
      label: '内容介绍',
      field: 'content',
      wrap: true,
      gridColumn: '1/5',
    },
    {
      label: '延误原因',
      field: 'delayReason',
      wrap: true,
      gridColumn: '1/5',
    },
  ],
  dataSource: detailsData,
});

const uploadPowerCode = {
  download: 'PMS_GJLJJYXQ_container_02_01_button_01',
  preview: 'PMS_GJLJJYXQ_container_02_01_button_02',
};
</script>

<template>
  <BasicCard
    title="工单信息"
    :is-border="false"
    :grid-content-props="orderInfo"
  />
  <BasicCard
    title="关键路径节约信息"
    :is-border="false"
    :grid-content-props="pathInfo"
  />
  <BasicCard
    title="关键路径节约附件"
    :is-border="false"
  >
    <UploadList
      :height="300"
      :edit="false"
      :isFileEdit="false"
      :is-spacing="false"
      :powerData="powerData"
      :powerCode="uploadPowerCode"
      :listData="detailsData?.fileVOList||[]"
    />
  </BasicCard>
</template>

<style scoped lang="less">

</style>
