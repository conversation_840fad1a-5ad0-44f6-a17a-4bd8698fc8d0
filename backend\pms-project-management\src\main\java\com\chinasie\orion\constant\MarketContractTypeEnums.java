package com.chinasie.orion.constant;

/**
 * @author: yk
 * @date: 2023/10/10 13:54
 * @description:
 */
public enum MarketContractTypeEnums {
    TOTAL_PRICE_CONTRACT("totalPriceContract", "总价合同"),
    COMPOSITE_CONTRACT("compositeContract", "复合合同"),
    FRAME_CONTRACT("frameContract", "框架合同"),
    SUB_ORDER_CONTRACT("subOrderContract", "框架下子订单"),

    SHOPP_ORDER_CONTRACT("shoppOrderContract", "商城子订单"),
    SON_CONTRACT("sonContract", "子合同"),

    ;

    private String code;

    private String description;

    MarketContractTypeEnums(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
