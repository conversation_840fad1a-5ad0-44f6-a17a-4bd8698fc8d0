<script setup lang="ts">
import {
  BasicButton, isPower, Layout, OrionTable, Select,
} from 'lyra-component-vue3';
import { ref, Ref, unref } from 'vue';
import { usePagePower } from '/@/views/pms/hooks';
import { openRolePersonForm } from './utils';
import Api from '/@/api';

const tableRef: Ref = ref();
const tableOptions = {
  showToolButton: false,
  api: (params: object) => new Api('/pms/train-contact/page').fetch({
    ...params,
    power: {
      containerCode: 'PMS_JSRYGL_container_02',
      pageCode: 'PMSRolePerson',
    },
    query: {
      contactType: unref(manageType),
    },
  }, '', 'POST'),
  smallSearchField: [
    'contactPersonNames',
    'deptName',
    'baseName',
  ],
  columns: [
    {
      title: '角色',
      dataIndex: 'contactTypeName',
      width: 150,
    },
    {
      title: '管理属性',
      dataIndex: 'manageTypeName',
      width: 150,
    },
    {
      title: '所属基地',
      dataIndex: 'baseName',
      width: 150,
    },
    {
      title: '所属中心',
      dataIndex: 'deptName',
      width: 200,
    },
    {
      title: '管理员姓名',
      dataIndex: 'contactPersonNames',
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 100,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '编辑',
      isShow: (record) => isPower('PMS_JSRYGL_container_02_button_01', record?.rdAuthList),
      onClick({ id }) {
        openRolePersonForm({ id }, updateTable);
      },
    },
    {
      text: '移除',
      modelTitle: '移除提示！',
      modalContent: '确认移除当前数据？',
      isShow: (record) => isPower('PMS_JSRYGL_container_02_button_02', record?.rdAuthList),
      modal: (record) => deleteApi([record?.id]),
    },
  ],
};

function deleteApi(ids: string[]) {
  return new Promise((resolve) => {
    new Api('/pms/train-contact').fetch(ids, 'remove', 'DELETE').then(() => {
      updateTable();
    }).finally(() => {
      resolve('');
    });
  });
}

function updateTable() {
  tableRef.value.reload();
}

const { powerData, getPowerDataHandle } = usePagePower();

const manageType: Ref = ref();

async function roleListApi() {
  const result = await new Api('/pmi/role/list/module/PMS').fetch('', '', 'GET');
  return result?.map((item) => ({
    label: item.name,
    value: item.code,
  }));
}
</script>

<template>
  <Layout
    v-get-power="{pageCode:'PMSRolePerson', getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      class="custom-table-toolbar"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <BasicButton
          v-is-power="['PMS_JSRYGL_container_01_button_01']"
          type="primary"
          icon="sie-icon-tianjiaxinzeng"
          @click="openRolePersonForm(null,updateTable)"
        >
          添加角色人员
        </BasicButton>
        <div class="flex flex-ac mla">
          <span style="white-space: nowrap;">角色</span>
          <Select
            v-model:value="manageType"
            allowClear
            class="ml10"
            style="width: 200px"
            :api="roleListApi"
            @change="updateTable"
          />
        </div>
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">
:deep(.custom-table-toolbar) {
  width: auto;
  flex: 0;

  .ant-input-search {
    width: 215px;
    margin-left: 12px;
  }
}

.mla {
  margin-left: auto;
}
</style>
