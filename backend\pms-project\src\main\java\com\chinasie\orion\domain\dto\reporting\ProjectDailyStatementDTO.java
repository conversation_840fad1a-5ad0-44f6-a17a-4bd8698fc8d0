package com.chinasie.orion.domain.dto.reporting;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.chinasie.orion.constant.reporting.ReportingBusEnum;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;
import java.util.List;
import java.util.stream.Collectors;

/**
 * ProjectDailyStatement Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-14 15:21:01
 */
@ApiModel(value = "ProjectDailyStatementDTO对象", description = "计划日报")
@Data
public class ProjectDailyStatementDTO extends ObjectDTO implements Serializable {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    @NotEmpty(message = "")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date daily;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    private String resp;

    /**
     * 日报状态
     */
    @ApiModelProperty(value = "日报状态")
    private Integer busStatus;

    /**
     * 评价
     */
    @ApiModelProperty(value = "评价")
    private String evaluate;

    /**
     * 评分
     */
    @ApiModelProperty(value = "评分")
    private BigDecimal score;

    /**
     * 审核人
     */
    @ApiModelProperty(value = "审核人==提交人")
    private String reviewedBy;

    /**
     * 抄送人（英文逗号分割）
     */
    @ApiModelProperty(value = "抄送人（英文逗号分割）")
    private String carbonCopyBy;


    /**
     * 抄送人（英文逗号分割）
     */
    @ApiModelProperty(value = "抄送人id集合")
    private List<String> carbonCopyByList;

    /**
     * 汇报总结
     */
    @ApiModelProperty(value = "汇报总结")
    private String summary;

    /**
     * 日报内容
     */
    @ApiModelProperty(value = "当日日报内容")
    @Size(min = 1,message = "当日日报内容不能为空")
    private List<ProjectDailyStatementContentDTO> contentVOList;


    @ApiModelProperty(value = "明日日报内容")
    private List<ProjectDailyStatementContentDTO> nexDayVOList;

    @ApiModelProperty(value = "文件列表")
    private List<FileVO> fileDTOList;

    @ApiModelProperty(value = "日报状态：NOT_SUBMITTED(未提交) SUBMITTED（提交）")
    private ReportingBusEnum reportingKey;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    public void setCarbonCopyByList(List<String> carbonCopyByList) {
        if(!CollectionUtils.isEmpty(carbonCopyByList)){
            this.carbonCopyBy = carbonCopyByList.stream().collect(Collectors.joining(","));
        }
    }
}
