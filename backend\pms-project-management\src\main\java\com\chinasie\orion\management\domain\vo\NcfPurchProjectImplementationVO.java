package com.chinasie.orion.management.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * NcfPurchProjectImplementation VO对象
 *
 * <AUTHOR>
 * @since 2024-06-12 10:24:16
 */
@ApiModel(value = "NcfPurchProjectImplementationVO对象", description = "采购项目实施表")
@Data
public class NcfPurchProjectImplementationVO extends ObjectVO implements Serializable {

    /**
     * 流程名称
     */
    @ApiModelProperty(value = "流程名称")
    private String processName;


    /**
     * 发起人
     */
    @ApiModelProperty(value = "发起人")
    private String promoter;


    /**
     * 发起时间
     */
    @ApiModelProperty(value = "发起时间")
    private Date initiationTime;


    /**
     * 采购立项申请号
     */
    @ApiModelProperty(value = "采购立项申请号")
    private String purchReqEcpCode;


    /**
     * 采购申请号
     */
    @ApiModelProperty(value = "采购申请号")
    private String purchReqDocCode;


    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
    private String applicant;


    /**
     * 需求部门
     */
    @ApiModelProperty(value = "需求部门")
    private String applyDepartment;


    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;


    /**
     * 采购申请完成时间
     */
    @ApiModelProperty(value = "采购申请完成时间")
    private Date purchReqEndTime;


    /**
     * 采购立项申请金额
     */
    @ApiModelProperty(value = "采购立项申请金额")
    private BigDecimal purchReqAmount;


    /**
     * 商务人员
     */
    @ApiModelProperty(value = "商务人员")
    private String bizRespons;


    /**
     * 技术人员
     */
    @ApiModelProperty(value = "技术人员")
    private String techRespons;


    /**
     * 财务人员
     */
    @ApiModelProperty(value = "财务人员")
    private String financialStaff;


    /**
     * 其他人员
     */
    @ApiModelProperty(value = "其他人员")
    private String others;


    /**
     * 是否属于应集采范围
     */
    @ApiModelProperty(value = "是否属于应集采范围")
    private Boolean isCollectionPurch;


    /**
     * 期望合同签订时间
     */
    @ApiModelProperty(value = "期望合同签订时间")
    private String expectedContractSigningTime;


    /**
     * 采购计划需求编号
     */
    @ApiModelProperty(value = "采购计划需求编号")
    private String purchPlanNumber;


    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型")
    private String contractType;


    /**
     * 合同状态
     */
    @ApiModelProperty(value = "合同状态")
    private String contractState;


    /**
     * 采购类型
     */
    @ApiModelProperty(value = "采购类型")
    private String purchType;


    /**
     * 采购方式
     */
    @ApiModelProperty(value = "采购方式")
    private String purchMethod;


    /**
     * 下一步工作安排
     */
    @ApiModelProperty(value = "下一步工作安排")
    private String nextStepWorkArrangement;


    /**
     * 关注事项
     */
    @ApiModelProperty(value = "关注事项")
    private String concerns;


    /**
     * 已经耗时
     */
    @ApiModelProperty(value = "已经耗时")
    private String usedTime;


    /**
     * 一级分发
     */
    @ApiModelProperty(value = "一级分发")
    private Date firstDistribution;


    /**
     * 二级分发
     */
    @ApiModelProperty(value = "二级分发")
    private Date secondaryDistribution;


    /**
     * 接受确认
     */
    @ApiModelProperty(value = "接受确认")
    private Date acceptConfirmation;


    /**
     * 采购启动发起
     */
    @ApiModelProperty(value = "采购启动发起")
    private Date purchStart;


    /**
     * 采购启动审批
     */
    @ApiModelProperty(value = "采购启动审批")
    private Date purchStartApproval;


    /**
     * 询价签发
     */
    @ApiModelProperty(value = "询价签发")
    private Date inqIssuance;


    /**
     * 报价截止时间
     */
    @ApiModelProperty(value = "报价截止时间")
    private Date quoteEnd;


    /**
     * 开启报价时间
     */
    @ApiModelProperty(value = "开启报价时间")
    private Date openQuote;


    /**
     * 评审时间
     */
    @ApiModelProperty(value = "评审时间")
    private Date reviewTime;


    /**
     * 公示发布时间
     */
    @ApiModelProperty(value = "公示发布时间")
    private Date reviewOutTime;


    /**
     * UPM审批中
     */
    @ApiModelProperty(value = "UPM审批中")
    private Date upmApprovalInProgress;


    /**
     * UPM审批完成
     */
    @ApiModelProperty(value = "UPM审批完成")
    private Date upmApprovalComplete;


    /**
     * 发送SAP
     */
    @ApiModelProperty(value = "发送SAP")
    private Date sendSap;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;

    /**
     * 是否发布启动公示
     */
    @ApiModelProperty(value = "是否发布启动公示")
    private String isPublicLaunch;

    /**
     * 合同执行状态
     */
    @ApiModelProperty(value = "合同执行状态")
    private String executionStatus;

    /**
     * ECP采购申请号
     */
    @ApiModelProperty(value = "ECP采购申请号")
    private String ecpPurchaseAppNo;

    @ApiModelProperty(value = "需求部门编码")
    private String applyDepartmentCode;

    @ApiModelProperty(value = "需求部门编码(转换后)")
    private String applyDepartmentCodeTran;
}
