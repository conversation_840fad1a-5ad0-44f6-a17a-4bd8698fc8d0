package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;

import java.util.List;

/**
 * TrainEquivalent VO对象
 *
 * <AUTHOR>
 * @since 2024-06-04 17:09:24
 */
@ApiModel(value = "TrainEquivalentVO对象", description = "培训等效")
@Data
public class TrainEquivalentVO extends ObjectVO implements Serializable {


    /**
     * 等效基地编号
     */
    @ApiModelProperty(value = "等效基地编号")
    private String equivalentBaseCode;


    /**
     * 等效基地名称
     */
    @ApiModelProperty(value = "等效基地名称")
    private String equivalentBaseName;


    /**
     * 等效认定时间
     */
    @ApiModelProperty(value = "等效认定时间")
    private Date equivalentDate;

    /**
     * 人员编号
     */
    @ApiModelProperty(value = "人员编号")
    private String userCode;

    @ApiModelProperty(value = "现任职务")
    private String nowPosition;
    @ApiModelProperty(value = "全称呼")
    private String fullName;



    @ApiModelProperty(value = "落地Id")
    @TableField(value = "train_center_id")
    private String trainCenterId;
    /**
     * 是否等效
     */
    @ApiModelProperty(value = "是否等效")
    private Boolean isEquivalent;
    @ApiModelProperty(value = "人员编号和培训编号")
    private String  ukKey;
    @ApiModelProperty(value = "附件列表")
    private List<FileVO> fileVOList;

    @ApiModelProperty(value = "培训编号")
    @TableField(value = "train_number")
    private String trainNumber;

    @ApiModelProperty(value = "培训名称")
    private String trainName;

    @ApiModelProperty(value = "培训基地名称")
    private String baseName;

    /**
     * 培训课时
     */
    @ApiModelProperty(value = "培训课时")
    private BigDecimal lessonHour;



    /**
     * 培训讲师
     */
    @ApiModelProperty(value = "培训讲师")
    private String trainLecturer;
    /**
     * 办结时间
     */
    @ApiModelProperty(value = "办结时间")
    private Date endDate;

    @ApiModelProperty(value = "到期时间")
    private Date expireTime;


}
