<script setup lang="ts">
import { InputSelectOrganization } from 'lyra-component-vue3';
import { computed, nextTick, ref } from 'vue';
const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['change']);
const refDept = ref();
const isEdit = ref(false);
const name = computed(() => props.data.map((item:any) => item.name).join('，'));
const selectDeptData = ref([]);

function inputSelectDeptChange(depts) {
  emit('change', depts);
}

const handleMouseenter = async () => {
  isEdit.value = true;
  selectDeptData.value = props.data;
  await nextTick();
  refDept.value.$el.getElementsByTagName('input')[0].focus();
  refDept.value.$el.getElementsByTagName('input')[0].onblur = () => {
    isEdit.value = false;
  };
};
</script>

<template>
  <div
    v-if="!isEdit"
    class="flex-te"
    style="cursor: pointer;"
    @mouseenter="handleMouseenter"
  >
    {{ name }}&nbsp;
  </div>
  <InputSelectOrganization
    v-else
    ref="refDept"
    v-model:selectData="selectDeptData"
    @change="inputSelectDeptChange"
  />
</template>

<style scoped lang="less">

</style>
