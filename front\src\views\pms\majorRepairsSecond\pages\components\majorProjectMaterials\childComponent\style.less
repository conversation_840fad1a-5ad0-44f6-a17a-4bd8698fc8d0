:deep(.clamp-line-2) {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  max-width: 120px;
}
:deep(.flex-ac.flex-active){
  flex-direction: column;
  align-items: flex-start;
}
.flex-row{
  display: flex;
  justify-content: flex-end;
  margin-right: 10px;
}
:deep(.title-line-1){
  font-size: 14px;
}
:deep(.title-line-2){
  font-size: 12px;
}
:deep(.ant-table-wrapper), :deep(.surely-table-cell-inner) {
  .common-center-major {
    display: flex;
    justify-content: flex-start;
  }

  .common-s-major {
    background: inherit;
    box-sizing: border-box;
    border-width: 1px;
    border-style: solid;
    border-radius: 4px;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    font-weight: 400;
    font-style: normal;
    font-size: 12px;
    text-align: center;
    height: 22px;
    line-height: 20px;
    padding: 0 5px;
  }

  .green-s-major {
    width: 50px;
    background-color: rgba(246, 255, 237, 1);
    border-color: rgba(183, 235, 143, 1);
    color: #52C41A;
  }

  .warn-s-major {
    width: 50px;
    background-color: rgba(255, 251, 230, 1);
    border-color: rgba(255, 229, 143, 1);
    color: #FAAD14;
  }

  .blue-s-major {
    width: auto;
    background-color: rgba(230, 247, 255, 1);
    border-color: rgba(145, 213, 255, 1);
    color: #1890FF;
  }

  .red-s-major {
    width: 50px;
    background-color: rgba(254, 240, 239, 1);
    border-color: rgba(255, 163, 158, 1);
    color: #F5222D;
  }
}
:deep(.ant-table-cell.required) {
  &::after {
    position: absolute;
    content: '*';
    top: 50%;
    left: 2px;
    transform: translateY(-50%);
    color: red;
  }
}

// 操作栏样式
:deep(.ant-space.ant-space-horizontal){
  gap: 0!important;
  width: 100%;
}
:deep(.ant-space-item){
  width: 100%;
}
:deep(.ant-picker) {
  padding: 8px 11px;
}
:deep(.basic-import-select-wrap){
  .ant-input {
    height: 40px;
  }
  .ant-input-search-button {
    height: 40px;
  }
}
:deep(.ant-select-selector) {
  height: 40px!important;
  .ant-select-selection-search{
    .ant-select-selection-search-input{
      height: 40px;
    }
  }
  .ant-select-selection-placeholder{
    line-height: 40px;
  }
}
:deep(.ant-select-selection-search) {
  height: 40px!important;
  .ant-select-selection-search-input {
    height: 40px;
  }
}
:deep(.ant-select-single .ant-select-selector .ant-select-selection-item) {
  line-height: 40px;
}

:deep(.clamp-hover){
  display: inline-block;
  width: 100%;
  min-height: 22px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;
  &:hover{
    color: #1890ff;
  }
}
:deep(.buttons-wrapper) {
  align-items: center;
  justify-content: space-between;
  display: flex;
  .flex{
    width: 100%;
  }
}
