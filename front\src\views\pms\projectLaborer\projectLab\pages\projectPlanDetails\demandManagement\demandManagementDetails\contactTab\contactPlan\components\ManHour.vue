<template>
  <Layout class="manHourPage">
    <div class="header">
      <div class="text-base">
        工时信息
      </div>
      <div class="text-xs">
        <a-space :size="40">
          <span>预估工时： {{ table.manHour }}</span>
          <span>已登记工时： {{ table.realityManHour }}</span>
          <span>剩余工时： {{ table.residueManHour }}</span>
          <span>工时进度： {{ table.manHourScheduleName }}</span>
          <span>预估偏差： {{ table.deviationManHour }}</span>
        </a-space>
      </div>
    </div>
    <BasicTable
      ref="tableRef"
      :row-selection="false"
      :columns="columns"
      :data-source="table.manHourVos"
      :show-index-column="false"
      :pagination="false"
      row-key="id"
      :resize-height-offset="10"
      :loading="loading"
    />
  </Layout>
</template>

<script lang="ts">
import {
  Layout, BasicTable,
} from 'lyra-component-vue3';
import { Space } from 'ant-design-vue';
import {
  onMounted, reactive, toRefs, ref, inject, computed,
} from 'vue';
import Api from '/@/api';
export default {
  name: 'Index',
  components: {
    Layout,
    BasicTable,
    ASpace: Space,
  },
  setup(props) {
    const formData: any = inject('formData', {});
    const state = reactive({
      tableRef: ref(),
      searchAll: {},
      columns: [
        {
          title: '姓名',
          dataIndex: 'name',
          align: 'left',
        },
        {
          title: '开始时间',
          dataIndex: 'startTime',
        },
        {
          title: '登记工时',
          dataIndex: 'realityManHour',
        },
        {
          title: '工作描述',
          dataIndex: 'remark',
        },
        {
          title: '修改人',
          dataIndex: 'modifyName',
        },
        {
          title: '修改时间',
          dataIndex: 'modifyTime',
        },
      ],
      table: {
        manHourVos: [],
      },
      loading: false,

      powerData: [],
    });
    function getPage() {
      state.loading = true;
      new Api('/pms')
        .fetch('', `man-hour/list?planId=${formData?.value?.id}`, 'GET')
        .then((res) => {
          state.table = res;
          state.loading = false;
          state.tableRef.clearSelectedRowKeys();
        })
        .catch(() => {
          state.loading = false;
        });
    }

    onMounted(() => {
      getPage();
    });
    return {
      ...toRefs(state),
    };
  },
};
</script>

<style scoped lang="less">
  .manHourPage {
    width: calc(100% - 60px);
    flex: 1;
    height: 500px;
  }
  .header {
    margin: 10px;

    .sub-title {
      margin-top: 10px;
    }
  }
</style>
