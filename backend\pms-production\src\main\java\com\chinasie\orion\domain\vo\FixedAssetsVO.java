package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;

import java.util.List;

/**
 * NcfFormGVWdVZFnw VO对象
 *
 * <AUTHOR>
 * @since 2024-06-04 14:36:24
 */
@ApiModel(value = "FixedAssetsVO对象", description = "固定资产能力库")
@Data
public class FixedAssetsVO extends ObjectVO implements Serializable {

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 资产代码
     */
    @ApiModelProperty(value = "资产代码")
    private String code;


    /**
     * 资产名称
     */
    @ApiModelProperty(value = "资产名称")
    private String name;


    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private String numCount;


    /**
     * 成本中心编码
     */
    @ApiModelProperty(value = "成本中心编码")
    private String costCenter;

    /**
     * 规格型号
     */
    @ApiModelProperty(value = "规格型号")
    private String spModel;


    /**
     * 是否需要检定
     */
    @ApiModelProperty(value = "是否需要检定")
    private Boolean isNeedVerification;


    /**
     * 下次检定日期
     */
    @ApiModelProperty(value = "下次检定日期")
    private Date nextVerificationTime;


    /**
     * 责任人工号
     */
    @ApiModelProperty(value = "责任人工号")
    private String rspUserNumber;


    /**
     * 责任人姓名
     */
    @ApiModelProperty(value = "责任人姓名")
    private String rspUserName;


    /**
     * 使用人工号
     */
    @ApiModelProperty(value = "使用人工号")
    private String useUserNumber;


    /**
     * 使用人姓名
     */
    @ApiModelProperty(value = "使用人姓名")
    private String useUserName;


    /**
     * 资产存放地
     */
    @ApiModelProperty(value = "资产存放地")
    private String storageLocation;

    /**
     * 资产存放地
     */
    @ApiModelProperty(value = "资产存放地名称")
    private String storageLocationName;

    /**
     * 成本中心名称
     */
    @ApiModelProperty(value = "成本中心名称")
    private String costCenterName;


    /**
     * 使用人工号
     */
    @ApiModelProperty(value = "使用人Id")
    private String useUserId;

    /**
     * 使用人工号
     */
    @ApiModelProperty(value = "责任人Id")
    private String rspUserId;

    /**
     * 产品编码
     */
    @ApiModelProperty(value = "产品编码")
    private String productCode;

    /**
     * 工具状态
     */
    @ApiModelProperty(value = "工具状态")
    private String toolStatus;


    /**
     * 检定维护周期
     */
    @ApiModelProperty(value = "检定维护周期")
    private Integer maintenanceCycle;

    /**
     * 工具状态名称
     */
    @ApiModelProperty(value = "工具状态名称")
    private String toolStatusName;

    /**
     * 附件列表
     */
    @ApiModelProperty(value = "附件列表")
    private List<FileVO> fileList;
}
