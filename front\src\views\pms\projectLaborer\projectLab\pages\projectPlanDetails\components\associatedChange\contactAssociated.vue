<template>
  <OrionTable

    ref="tableRef"
    :options="tableOptions"
  >
    <template #toolbarLeft>
      <div class="button-margin-right">
        <BasicButton
          v-if="isPower('BGGL_container_button_01',powerData)"
          type="primary"
          icon="add"
          @click="addTableNode"
        >
          新增变更
        </BasicButton>
      </div>
    </template>
    <template #name="{ record }">
      <div
        class="tableIndexName flex-te action-btn"
        @click="openDetails(record)"
      >
        {{ record.name }}
      </div>
    </template>
    <template #action="{ record }">
      <BasicTableAction :actions="actionsBtn(record)" />
    </template>
  </OrionTable>
  <AddTableNode
    :isRisk="true"
    :from="from"
    @register="register"
    @update="updateData"
  />
</template>
<script setup lang="ts">
import {
  BasicButton, BasicTableAction, isPower, OrionTable, useDrawer,
} from 'lyra-component-vue3';
import AddTableNode from './component/AddTableNode.vue';
import {
  computed, inject, reactive, ref,
} from 'vue';
import { getChangeApplyTableClose, getChangeApplyTableDelete } from '/@/views/pms/projectLaborer/projectLab/pages/projectPlanDetails/api/changeApply';
import { message } from 'ant-design-vue';
import { useRoute, useRouter } from 'vue-router';
import { stampDate } from '/@/utils/dateUtil';
import Api from '/@/api';

const [register, { openDrawer }] = useDrawer();
const tableRef = ref(null);
const powerData = inject('powerData');
const router = useRouter();
const route = useRoute();
const updateData = () => {
  tableRef.value.reload();
};
const props = defineProps<{
  from: string;
}>();
const state = reactive({
  formId: '1111',
  tableSearchVal: '',
  btnList: [
    { type: 'check' },
    { type: 'open' },
    { type: 'edit' },
    {
      type: 'delete',
      powerCode: 'BGGL_container_button_02',
    },
    { type: 'search' },
    {
      type: 'close',
      name: '关闭',
      powerCode: 'BGGL_container_button_03',
    },
  ],
  params: {},
  treeData: [],
  powerData: [],
  powerCode: {
    addCode: 'TWD_container_button_01',
    editCode: 'TWD_container_button_02',
    deleteCode: 'TWD_container_button_05',
  },
});
const addTableNode = () => {
  openDrawer(true, {
    type: 'add',
    data: { ecrDir: state.formId },
  });
};
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: {},
  showSmallSearch: true,
  showIndexColumn: false,
  isFilter2: true,
  filterConfigName: 'PAS_BECURRENTMANAGE_CHANGEMANAGE_CHANGEREQUESTMANAGE',
  api(params) {
    return new Api(props.from === 'risk' ? '/pas/ecr/riskRelationEcr/page' : '/pas/ecr/questionRelationEcr/page').fetch({
      ...params,
      query: {
        dataSourceId: route.query.itemId,
      },
    }, '', 'POST');
  },
  columns: [
    {
      title: '标题',
      dataIndex: 'name',
      slots: { customRender: 'name' },
      align: 'left',
      minWidth: 200,
    },
    {
      title: '编号',
      align: 'left',
      dataIndex: 'number',
      slots: { customRender: 'messageName' },
      width: 200,
    },
    {
      title: '变更类型',
      dataIndex: 'changeWay',
      align: 'left',
      width: 100,
      customRender: ({
        text, record, index, column,
      }) =>
        (record.changeWay === 1 ? '快速变更' : '工程变更'),
    },
    {
      title: '所属类型',
      dataIndex: 'ecrTypeName',
      align: 'left',
      width: 100,
    },
    {
      title: '状态',
      dataIndex: 'status',
      align: 'left',
      width: 150,
      slots: { customRender: 'status' },
    },
    {
      title: '负责人',
      dataIndex: 'responsiblerName',
      width: 150,
      align: 'left',
    },
    {
      title: '申请时间',
      dataIndex: 'applyTime',
      type: 'dateTime',
      align: 'left',
      customRender: ({
        text, record, index, column,
      }) =>
        (record.applyTime && record.applyTime.length > 0
          ? stampDate(record.applyTime, 'yyyy-MM-dd HH:mm:ss')
          : ''),
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 150,
      align: 'left',
      fixed: 'right',
      slots: { customRender: 'action' },
    },
  ],
});

const openDetails = (record) => {
  router.push({
    name: 'PASChangeApplyDetails',
    params: {
      id: record.id,
    },
  });
};
const actionsBtn = computed(() => (record) => {
  const actions = [
    {
      text: '删除',
      isShow: computed(() => state.btnList.some((item) => item.type === 'delete')),
      modal() {
        return getChangeApplyTableDelete([record.id]).then((res) => {
          message.success('删除数据成功');
          tableRef.value.reload();
        });
      },
    },
    {
      text: '关闭',
      isShow: computed(() => state.btnList.some((item) => item.type === 'close')),
      modal() {
        return getChangeApplyTableClose([record.id]).then((res) => {
          message.success('关闭成功');
          tableRef.value.reload();
        });
      },
    },
  ];
  return actions;
});
</script>
