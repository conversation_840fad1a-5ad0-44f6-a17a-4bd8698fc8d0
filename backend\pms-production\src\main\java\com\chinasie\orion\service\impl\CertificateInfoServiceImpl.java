package com.chinasie.orion.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.DictConts;
import com.chinasie.orion.domain.dto.CertificateInfoDTO;
import com.chinasie.orion.domain.entity.CertificateInfo;
import com.chinasie.orion.domain.vo.CertificateInfoVO;
import com.chinasie.orion.domain.vo.SimVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.CertificateInfoMapper;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.CertificateInfoService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <p>
 * CertificateInfo 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05 10:41:49
 */
@Service
@Slf4j
public class CertificateInfoServiceImpl extends OrionBaseServiceImpl<CertificateInfoMapper, CertificateInfo> implements CertificateInfoService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private DictRedisHelper dictRedisHelper;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public CertificateInfoVO detail(String id, String pageCode) throws Exception {
        CertificateInfo CertificateInfo = this.getById(id);
        CertificateInfoVO result = BeanCopyUtils.convertTo(CertificateInfo, CertificateInfoVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param CertificateInfoDTO
     */
    @Override
    public String create(CertificateInfoDTO CertificateInfoDTO) throws Exception {
        CertificateInfo certificateInfo = BeanCopyUtils.convertTo(CertificateInfoDTO, CertificateInfo::new);

        String number = "ZS-" + RandomUtil.randomNumbers(8);
        certificateInfo.setNumber(number);
        this.save(certificateInfo);

        String rsp = certificateInfo.getId();

        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param CertificateInfoDTO
     */
    @Override
    public Boolean edit(CertificateInfoDTO CertificateInfoDTO) throws Exception {
        CertificateInfo CertificateInfo = BeanCopyUtils.convertTo(CertificateInfoDTO, CertificateInfo::new);

        this.updateById(CertificateInfo);

        String rsp = CertificateInfo.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<CertificateInfoVO> pages(Page<CertificateInfoDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<CertificateInfo> condition = new LambdaQueryWrapperX<>(CertificateInfo.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(CertificateInfo::getCreateTime);

        Page<CertificateInfo> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), CertificateInfo::new));

        PageResult<CertificateInfo> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<CertificateInfoVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<CertificateInfoVO> vos = BeanCopyUtils.convertListTo(page.getContent(), CertificateInfoVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "证书信息导入模板.xlsx";

        response.reset();
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("证书信息导入模板.xlsx", StandardCharsets.UTF_8));

        ExcelUtils.writeTemplate(response, CertificateInfoDTO.class, fileName, "证书信息导入模板", "证书信息导入模板");

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        CertificateInfoExcelListener excelReadListener = new CertificateInfoExcelListener();
        EasyExcel.read(inputStream, CertificateInfoDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<CertificateInfoDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("证书信息导入的原始数据={}", JSONUtil.toJsonStr(dtoS));

        Map<String, String> idToDesc = new HashMap<>(){{
            put("职业资格","pms_profe_qual");
            put("职称","pms_profe_title");
        }};


        List<CertificateInfo> CertificateInfoes = dtoS.stream().map(o -> {
            CertificateInfo tmp = BeanCopyUtils.convertTo(o, CertificateInfo::new);
            String certificateType = o.getCertificateType();
            tmp.setCertificateType(idToDesc.get(certificateType));
            return tmp;
        }).collect(Collectors.toList());


        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("ncf::CertificateInfo-import::id", importId, CertificateInfoes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<CertificateInfo> CertificateInfoes = (List<CertificateInfo>) orionJ2CacheService.get("ncf::CertificateInfo-import::id", importId);
        log.info("证书信息导入的入库数据={}", JSONUtil.toJsonStr(CertificateInfoes));

        this.saveBatch(CertificateInfoes);
        orionJ2CacheService.delete("ncf::CertificateInfo-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("ncf::CertificateInfo-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<CertificateInfo> condition = new LambdaQueryWrapperX<>(CertificateInfo.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(CertificateInfo::getCreateTime);
        List<CertificateInfo> CertificateInfoes = this.list(condition);

        Map<String, String> idToDesc = new HashMap<>(){{
            put("pms_profe_qual","职业资格");
            put("pms_profe_title","职称");
        }};


        List<CertificateInfoDTO> dtos = CertificateInfoes.stream().map(o -> {
            CertificateInfoDTO tmp = BeanCopyUtils.convertTo(o, CertificateInfoDTO::new);
            String certificateType = o.getCertificateType();
            tmp.setCertificateType(idToDesc.get(certificateType));
            return tmp;
        }).collect(Collectors.toList());

        String fileName = "证书信息数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", CertificateInfoDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<CertificateInfoVO> vos) throws Exception {
        if (CollectionUtils.isEmpty(vos)) {
            return;
        }
        List<DictValueVO> dictValueVOList = dictRedisHelper.getDictListByCode(DictConts.PMS_CERTIFICATE_TYPE);
        Map<String, String> idToDesc = new HashMap<>();
        if (!CollectionUtils.isEmpty(dictValueVOList)) {
            for (DictValueVO dictValueVO : dictValueVOList) {
                idToDesc.put(dictValueVO.getNumber(), dictValueVO.getDescription());
            }
        }
        List<DictValueVO> certificate_dict = dictRedisHelper.getDictListByCode(DictConts.CERTIFICATE_LEVEL);
        if (!CollectionUtils.isEmpty(certificate_dict)) {
            for (DictValueVO dictValueVO : certificate_dict) {
                idToDesc.put(dictValueVO.getNumber(), dictValueVO.getDescription());
            }
        }
        vos.forEach(vo -> {
            vo.setCertificateTypeName(idToDesc.getOrDefault(vo.getCertificateType(), ""));
            vo.setLevelName(idToDesc.getOrDefault(vo.getLevel(), ""));
        });


    }

    @Override
    public Map<String, CertificateInfoVO> getMapById(List<String> cIdList) throws Exception {
        if (CollectionUtils.isEmpty(cIdList)) {
            return new HashMap<>();
        }
        LambdaQueryWrapperX<CertificateInfo> condition = new LambdaQueryWrapperX<>(CertificateInfo.class);
        condition.select(CertificateInfo::getId, CertificateInfo::getId, CertificateInfo::getCertificateType
                , CertificateInfo::getLevel, CertificateInfo::getName, CertificateInfo::getAcquisitionDate
                , CertificateInfo::getIssuingAuthority, CertificateInfo::getIsNeedRenewal
                , CertificateInfo::getNumber
                , CertificateInfo::getRenewalDate);
        condition.in(CertificateInfo::getId, cIdList);
        List<CertificateInfo> list = this.list(condition);
        if (CollectionUtils.isEmpty(list)) {
            return new HashMap<>();
        }
        List<CertificateInfoVO> certificateInfoVOList = BeanCopyUtils.convertListTo(list, CertificateInfoVO::new);
        this.setEveryName(certificateInfoVOList);
        return certificateInfoVOList.stream().collect(Collectors.toMap(CertificateInfoVO::getId, Function.identity(), (k, k2) -> k2));
    }

    @Override
    public List<SimVO> allSimpleList() {
        LambdaQueryWrapperX<CertificateInfo> condition = new LambdaQueryWrapperX<>(CertificateInfo.class);
        condition.select(CertificateInfo::getName);
        condition.select(CertificateInfo::getId);
        List<CertificateInfo> list = this.list(condition);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<SimVO> certificateList = new ArrayList<>();
        for (CertificateInfo certificateInfo : list) {
            SimVO simVO = new SimVO();
            simVO.setId(certificateInfo.getId());
            simVO.setName(certificateInfo.getName());
            simVO.setNumber(certificateInfo.getNumber());
            certificateList.add(simVO);
        }
        return certificateList;
    }

    @Override
    public Map<String, String> allMap() {
        LambdaQueryWrapperX<CertificateInfo> condition = new LambdaQueryWrapperX<>(CertificateInfo.class);
        condition.select(CertificateInfo::getName);
        condition.select(CertificateInfo::getId);
        List<CertificateInfo> list = this.list(condition);
        if (CollectionUtils.isEmpty(list)) {
            return new HashMap<>();
        }
        Map<String, String> numberToName = new HashMap<>();
        for (CertificateInfo certificateInfo : list) {
            numberToName.put(certificateInfo.getId(), certificateInfo.getName());
        }
        return numberToName;
    }


    public static class CertificateInfoExcelListener extends AnalysisEventListener<CertificateInfoDTO> {

        private final List<CertificateInfoDTO> data = new ArrayList<>();

        @Override
        public void invoke(CertificateInfoDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<CertificateInfoDTO> getData() {
            return data;
        }
    }


}
