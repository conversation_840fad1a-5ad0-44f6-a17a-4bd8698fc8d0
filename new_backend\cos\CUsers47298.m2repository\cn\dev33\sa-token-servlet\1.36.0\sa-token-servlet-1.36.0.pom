<?xml version="1.0" encoding="utf-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>cn.dev33</groupId>
    <artifactId>sa-token-starter</artifactId>
    <version>1.36.0</version>
  </parent>
  <groupId>cn.dev33</groupId>
  <artifactId>sa-token-servlet</artifactId>
  <version>1.36.0</version>
  <name>sa-token-servlet</name>
  <description>sa-token authentication by Servlet API</description>
  <licenses>
    <license>
      <name>Apache 2</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
      <comments>A business-friendly OSS license</comments>
    </license>
  </licenses>
  <dependencies>
    <dependency>
      <groupId>cn.dev33</groupId>
      <artifactId>sa-token-core</artifactId>
    </dependency>
    <dependency>
      <groupId>javax.servlet</groupId>
      <artifactId>javax.servlet-api</artifactId>
      <optional>true</optional>
    </dependency>
  </dependencies>
</project>
