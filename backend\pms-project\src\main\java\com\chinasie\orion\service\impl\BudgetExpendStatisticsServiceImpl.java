package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.conts.BudgetEnum;
import com.chinasie.orion.conts.BudgetOccupationReceiveEnum;
import com.chinasie.orion.conts.BudgetStatusEnum;
import com.chinasie.orion.dict.BudgetDict;
import com.chinasie.orion.domain.dto.BudgetExpendFormDTO;
import com.chinasie.orion.domain.dto.BudgetManagementDTO;
import com.chinasie.orion.domain.entity.BudgetExpend;
import com.chinasie.orion.domain.entity.BudgetExpendForm;
import com.chinasie.orion.domain.entity.BudgetManagement;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;

import com.chinasie.orion.page.PageRequest;
import com.chinasie.orion.repository.BudgetExpendStatisticsMapper;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.calcite.rel.core.Collect;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BudgetExpendStatisticsServiceImpl implements BudgetExpendStatisticsService {
    @Autowired
    private BudgetExpendService budgetExpendService;

    @Autowired
    private BudgetExpendFormService budgetExpendFormService;

    @Autowired
    private BudgetManagementService budgetManagementService;

    @Resource
    private CostCenterDataService costCenterService;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Resource
    private BudgetExpendStatisticsMapper budgetExpendStatisticsMapper;

    @Autowired
    private DictBo dictBo;

    @Override
    public List<BudgetExpendStatisticsVO> getBudgetExpendStatistics(String projectId) throws Exception {
        LambdaQueryWrapperX<BudgetExpendForm> lambdaQueryWrapperX = new LambdaQueryWrapperX(BudgetExpendForm.class);
        lambdaQueryWrapperX.eq(BudgetExpendForm::getStatus, BudgetStatusEnum.COMPLETED.getCode());
        lambdaQueryWrapperX.eq(BudgetExpendForm::getProjectId, projectId);
        List<BudgetExpendForm> expendForms = budgetExpendFormService.list(lambdaQueryWrapperX);

        if(CollUtil.isEmpty(expendForms)){
            return new ArrayList<>();
        }
        //获取成本中心数据
        List<String> costCenterIds = expendForms.stream().distinct().map(BudgetExpendForm::getCostCenterId).collect(Collectors.toList());
        List<CostCenterVO> costCenterVOS = costCenterService.getList(costCenterIds);
        Map<String, String> costCenterMap = costCenterVOS.stream().collect(Collectors.toMap(CostCenterVO::getId, CostCenterVO::getName));

        expendForms.forEach(item -> {
            item.setCostCenterName(costCenterMap.get(item.getCostCenterId()));
        });

        Map<String, List<String>> costMap = expendForms.stream().collect(Collectors.groupingBy(BudgetExpendForm::getExpenseAccountId,
                Collectors.mapping(BudgetExpendForm::getCostCenterName, Collectors.toList())
        ));

        Map<String, List<String>> expendFormMap = expendForms.stream().collect(Collectors.groupingBy(BudgetExpendForm::getExpenseAccountId,
                Collectors.mapping(BudgetExpendForm::getNumber, Collectors.toList())
        ));
        List<String> ids = expendForms.stream().map(BudgetExpendForm::getId).collect(Collectors.toList());
        List<BudgetExpend> expendFormList = budgetExpendService.list(new LambdaQueryWrapper<BudgetExpend>().in(BudgetExpend::getFormId, ids));

        List<String> budgetIds = expendFormList.stream().map(BudgetExpend::getBudgetId).collect(Collectors.toList());

        List<BudgetManagement> budgetManagementList = budgetManagementService.list(new LambdaQueryWrapperX<BudgetManagement>(BudgetManagement.class)
                .in(BudgetManagement::getId, budgetIds));

        Map<String, List<String>> nameMap = budgetManagementList.stream().collect(Collectors.groupingBy(BudgetManagement::getExpenseSubjectNumber,
                Collectors.mapping(BudgetManagement::getName, Collectors.toList())
        ));
        Map<String, List<String>> numberMap = budgetManagementList.stream().collect(Collectors.groupingBy(BudgetManagement::getExpenseSubjectNumber,
                Collectors.mapping(BudgetManagement::getNumber, Collectors.toList())
        ));
        Map<String, BigDecimal> money = expendForms.stream().collect(Collectors.groupingBy(BudgetExpendForm::getExpenseAccountId, Collectors.reducing(
                BigDecimal.ZERO, // 初始值为零
                BudgetExpendForm::getExpendMoney,
                (a, b) -> a.add(b) // 累加器函数，将两个BigDecimal相加
        )));

        List<String> expenseAccountIds = expendForms.stream().map(BudgetExpendForm::getExpenseAccountId).collect(Collectors.toList());
        //获取科目所有父级
        List<ExpenseSubjectMsgVO> list = costCenterService.getAllParent(expenseAccountIds);
        List<BudgetExpendStatisticsVO> budgetExpendStatisticsVOS = new ArrayList<>();

        for (ExpenseSubjectMsgVO expenseSubjectMsgVO : list) {
            BudgetExpendStatisticsVO budgetExpendStatisticsVO = new BudgetExpendStatisticsVO();
            budgetExpendStatisticsVO.setExpenseSubjectName(expenseSubjectMsgVO.getName());
            budgetExpendStatisticsVO.setExpenseSubjectId(expenseSubjectMsgVO.getId());
            budgetExpendStatisticsVO.setId(expenseSubjectMsgVO.getId());
            budgetExpendStatisticsVO.setParentId(expenseSubjectMsgVO.getParentId());
            if (ObjectUtil.isNotEmpty(expenseSubjectMsgVO.getId())) {
                budgetExpendStatisticsVO.setExpendMoney(money.get(expenseSubjectMsgVO.getId()));
            }
            if (CollUtil.isNotEmpty(expendFormMap.get(expenseSubjectMsgVO.getId()))) {
                budgetExpendStatisticsVO.setExpendNumbers(expendFormMap.get(expenseSubjectMsgVO.getId()));
            }
            if (CollUtil.isNotEmpty(costMap.get(expenseSubjectMsgVO.getId()))) {
                budgetExpendStatisticsVO.setCostCenterIdNames(costMap.get(expenseSubjectMsgVO.getId()));
            }
            if (CollUtil.isNotEmpty(numberMap.get(expenseSubjectMsgVO.getNumber()))) {
                budgetExpendStatisticsVO.setBudgetNames(nameMap.get(expenseSubjectMsgVO.getNumber()));
                budgetExpendStatisticsVO.setBudgetNumbers(numberMap.get(expenseSubjectMsgVO.getNumber()));
            }
            budgetExpendStatisticsVOS.add(budgetExpendStatisticsVO);
        }

        Map<String, List<BudgetExpendStatisticsVO>> map = budgetExpendStatisticsVOS.stream().collect(Collectors.groupingBy(BudgetExpendStatisticsVO::getParentId));

        List<BudgetExpendStatisticsVO> expendStatisticsVOS = map.get("0");
        tree(expendStatisticsVOS, map);
        return expendStatisticsVOS;
    }

    private BigDecimal tree(List<BudgetExpendStatisticsVO> vos, Map<String, List<BudgetExpendStatisticsVO>> map) {
        BigDecimal total = BigDecimal.ZERO;
        for (BudgetExpendStatisticsVO vo : vos) {
            BigDecimal money = BigDecimal.ZERO;
            if (CollUtil.isNotEmpty(map.get(vo.getExpenseSubjectId()))) {
                List<BudgetExpendStatisticsVO> budgetExpendStatisticsVOS = map.get(vo.getExpenseSubjectId());
                money = money.add(tree(budgetExpendStatisticsVOS, map));
                vo.setChildren(budgetExpendStatisticsVOS);
            } else {
               money = money.add(vo.getExpendMoney());
            }
            vo.setExpendMoney(money);
            total = total.add(money);
        }
        return total;
    }

    @Override
    public Page<BudgetExpendDetailVO> getPage(PageRequest<BudgetExpendFormDTO> pageRequest) throws Exception {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<BudgetExpendDetailVO> realPageRequest = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<BudgetExpendDetailVO>(pageRequest.getPageNum(), pageRequest.getPageSize());
        com.chinasie.orion.sdk.metadata.page.Page<BudgetExpendDetailVO> resultPage = new com.chinasie.orion.sdk.metadata.page.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), 0, new ArrayList<>());
        if (ObjectUtil.isNotEmpty(pageRequest.getQuery())) {
            String id = pageRequest.getQuery().getExpenseAccountId();
            String projectId = pageRequest.getQuery().getProjectId();
            if(StrUtil.isBlank(id)&&StrUtil.isBlank(projectId)){
                return resultPage;
            }
            List<ExpenseSubjectMsgVO> expenseSubjectMsgVOS = costCenterService.getAllChild(id);
            List<String> ids = expenseSubjectMsgVOS.stream().map(ExpenseSubjectMsgVO::getId).collect(Collectors.toList());
            ids.add(id);
            com.baomidou.mybatisplus.extension.plugins.pagination.Page<BudgetExpendDetailVO> page = budgetExpendStatisticsMapper.getBudgetExpendDetailVOPage(ids,projectId, realPageRequest);
            List<BudgetExpendDetailVO> budgetExpendDetailVOS = page.getRecords();

            if (CollUtil.isEmpty(budgetExpendDetailVOS)) {
                return resultPage;
            }
            Map<String, String> currencyMap = dictBo.getDictValue(BudgetDict.BUDGET_CURRENCY);
            List<String> costCenterIds = budgetExpendDetailVOS.stream().map(BudgetExpendDetailVO::getCostCenterId).collect(Collectors.toList());
            List<CostCenterVO> costCenterVOS = costCenterService.getList(costCenterIds);
            Map<String, String> costCenterMap = costCenterVOS.stream().collect(Collectors.toMap(CostCenterVO::getId, CostCenterVO::getName));

            List<String> userIds = budgetExpendDetailVOS.stream().map(BudgetExpendDetailVO::getOccurrencePerson).collect(Collectors.toList());
            List<UserVO> userVOS = userRedisHelper.getUserByIds(userIds);
            Map<String, String> userMap = userVOS.stream().collect(Collectors.toMap(UserVO::getId, UserVO::getName));

            budgetExpendDetailVOS.forEach(item ->
            {
                if (StrUtil.isNotBlank(currencyMap.get(item.getCurrency()))) {
                    item.setCurrencyName(currencyMap.get(item.getCurrency()));
                }
                if (StrUtil.isNotBlank(costCenterMap.get(item.getCostCenterId()))) {
                    item.setCostCenterName(costCenterMap.get(item.getCostCenterId()));
                }
                if (StrUtil.isNotBlank(userMap.get(item.getOccurrencePerson()))) {
                    item.setOccurrencePersonName(userMap.get(item.getOccurrencePerson()));
                }
                if (StrUtil.isNotBlank(item.getOccupationReceive())) {
                    item.setOccupationReceive(BudgetOccupationReceiveEnum.getBudgetOccupationReceive(item.getOccupationReceive()));
                }
            });

            resultPage.setContent(budgetExpendDetailVOS);
            resultPage.setTotalSize(page.getTotal());
            return resultPage;
        }
        return resultPage;
    }

    @Override
    public void export(String expenseSubjectId,String projectId,HttpServletResponse response) throws Exception {
        List<ExpenseSubjectMsgVO> expenseSubjectMsgVOS = costCenterService.getAllChild(expenseSubjectId);
        List<String> ids = expenseSubjectMsgVOS.stream().map(ExpenseSubjectMsgVO::getId).collect(Collectors.toList());
        List<BudgetExpendDetailVO> result = budgetExpendStatisticsMapper.getList(ids,projectId);
        String fileName = "费用支出数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", BudgetExpendDetailVO.class, result);
    }

    @Override
    public void exportAll(String projectId,HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<BudgetExpendForm> lambdaQueryWrapperX = new LambdaQueryWrapperX(BudgetExpendForm.class);
        lambdaQueryWrapperX.eq(BudgetExpendForm::getStatus, BudgetStatusEnum.COMPLETED.getCode());
        lambdaQueryWrapperX.eq(BudgetExpendForm::getProjectId, projectId);
        List<BudgetExpendForm> expendForms = budgetExpendFormService.list(lambdaQueryWrapperX);
        if(CollUtil.isEmpty(expendForms)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS,"没有费用支出数据");
        }
        List<String> ids = expendForms.stream().map(BudgetExpendForm::getExpenseAccountId).collect(Collectors.toList());
        List<BudgetExpendDetailVO> result = budgetExpendStatisticsMapper.getList(ids,projectId);
        String fileName = "费用支出全部数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", BudgetExpendDetailVO.class, result);
    }

    public BudgetStatisticsVO getTotalList(String projectId) throws Exception {
        //获取支出总金额
        BudgetStatisticsVO budgetStatisticsVO = new BudgetStatisticsVO();
        LambdaQueryWrapperX<BudgetExpendForm> lambdaQueryWrapperX = new LambdaQueryWrapperX(BudgetExpendForm.class);
        lambdaQueryWrapperX.eq(BudgetExpendForm::getProjectId, projectId);
        lambdaQueryWrapperX.eq(BudgetExpendForm::getStatus, BudgetStatusEnum.COMPLETED.getCode());
        lambdaQueryWrapperX.selectSum(BudgetExpendForm::getExpendMoney);
        BudgetExpendForm budgetExpendForm = budgetExpendFormService.getOne(lambdaQueryWrapperX);
        BigDecimal expendMoney = BigDecimal.ZERO;
        if(ObjectUtil.isNotEmpty(budgetExpendForm)){
            expendMoney = budgetExpendForm.getExpendMoney();
        }
        //获取预算总金额
        LambdaQueryWrapperX<BudgetManagement> managementLambdaQueryWrapperX = new LambdaQueryWrapperX(BudgetManagement.class);
        managementLambdaQueryWrapperX.selectSum(BudgetManagement::getBudgetMoney);
        managementLambdaQueryWrapperX.eq(BudgetExpendForm::getProjectId, projectId);
        BudgetManagement budgetManagement = budgetManagementService.getOne(managementLambdaQueryWrapperX);
        BigDecimal budgetMoney = BigDecimal.ZERO;
        if(ObjectUtil.isNotEmpty(budgetManagement)){
            budgetMoney = budgetManagement.getBudgetMoney();
        }

        //计算预算信息
        BigDecimal residueMoney = budgetMoney.subtract(expendMoney);
        BigDecimal useRatio = BigDecimal.ZERO;
        BigDecimal residueRatio = BigDecimal.ZERO;

        if (budgetMoney.compareTo(BigDecimal.ZERO) > 0) {
            useRatio = expendMoney.divide(budgetMoney, 2, RoundingMode.HALF_UP);
            residueRatio = new BigDecimal(1).subtract(useRatio);
        }

        budgetStatisticsVO.setExpendMoney(expendMoney);
        budgetStatisticsVO.setResidueMoney(residueMoney);
        budgetStatisticsVO.setUseRatio(useRatio.multiply(new BigDecimal(100)));
        budgetStatisticsVO.setResidueRatio(residueRatio.multiply(new BigDecimal(100)));
        if (expendMoney.compareTo(budgetMoney) > 0) {
            budgetStatisticsVO.setOverspendMoney(expendMoney.subtract(budgetMoney));
            if (budgetMoney.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal overspendRatio = expendMoney.subtract(budgetMoney).divide(budgetMoney, 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
                budgetStatisticsVO.setOverspendRatio(overspendRatio);
                budgetStatisticsVO.setOverspendResidueRatio(new BigDecimal(100).subtract(overspendRatio));
            }
        }

        //获取材料、工资及劳务费预算和支出
        ////TODO 6/12 审查 魏宇航  什么原因要用中文？
        List<ExpenseSubjectMsgVO> cl = costCenterService.getExpenseSubjectIds("材料费");
        List<ExpenseSubjectMsgVO> gz = costCenterService.getExpenseSubjectIds("工资及劳务费");

        List<String> clNumbers = cl.stream().map(ExpenseSubjectMsgVO::getNumber).collect(Collectors.toList());
        List<String> gzNumbers = gz.stream().map(ExpenseSubjectMsgVO::getNumber).collect(Collectors.toList());


        if (CollUtil.isNotEmpty(clNumbers)) {
            LambdaQueryWrapperX<BudgetExpendForm> clLambdaQueryWrapperX = new LambdaQueryWrapperX(BudgetExpendForm.class);
            clLambdaQueryWrapperX.eq(BudgetExpendForm::getProjectId, projectId);
            clLambdaQueryWrapperX.selectSum(BudgetExpendForm::getExpendMoney);
            clLambdaQueryWrapperX.eq(BudgetExpendForm::getStatus, BudgetStatusEnum.COMPLETED.getCode());
            clLambdaQueryWrapperX.in(BudgetExpendForm::getExpenseAccountNumber, clNumbers);
            BudgetExpendForm clBudgetExpendForm = budgetExpendFormService.getOne(clLambdaQueryWrapperX);
            BigDecimal clExpendMoney = BigDecimal.ZERO;
            if(ObjectUtil.isNotEmpty(clBudgetExpendForm)) {
                clExpendMoney  = clBudgetExpendForm.getExpendMoney();
            }

            LambdaQueryWrapperX<BudgetExpendForm> occupationLambdaQueryWrapperX = new LambdaQueryWrapperX(BudgetExpendForm.class);
            occupationLambdaQueryWrapperX.eq(BudgetExpendForm::getProjectId, projectId);
            occupationLambdaQueryWrapperX.eq(BudgetExpendForm::getOccupationReceive, 1);
            occupationLambdaQueryWrapperX.eq(BudgetExpendForm::getStatus, BudgetStatusEnum.COMPLETED.getCode());
            occupationLambdaQueryWrapperX.selectSum(BudgetExpendForm::getExpendMoney);
            BudgetExpendForm occupationBudgetExpendForm = budgetExpendFormService.getOne(occupationLambdaQueryWrapperX);
            if(ObjectUtil.isNotEmpty(occupationBudgetExpendForm)) {
                BigDecimal occupationBudgetExpendMoney = occupationBudgetExpendForm.getExpendMoney();
                budgetStatisticsVO.setMaterialsOccupationMoney(occupationBudgetExpendMoney);
                budgetStatisticsVO.setMaterialsReceiveMoney(clExpendMoney.subtract(occupationBudgetExpendMoney));
                if(clExpendMoney.compareTo(BigDecimal.ZERO)>0) {
                    budgetStatisticsVO.setMaterialsOccupationRatio( occupationBudgetExpendMoney.divide(clExpendMoney, 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
                    budgetStatisticsVO.setMaterialsReceiveRatio( budgetStatisticsVO.getMaterialsReceiveMoney().divide(clExpendMoney, 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
                }
            }else{
                budgetStatisticsVO.setMaterialsOccupationMoney(BigDecimal.ZERO);
                budgetStatisticsVO.setMaterialsReceiveMoney(clExpendMoney);
                budgetStatisticsVO.setMaterialsOccupationRatio(BigDecimal.ZERO);
                budgetStatisticsVO.setMaterialsReceiveRatio(new BigDecimal(100));
            }



            LambdaQueryWrapperX<BudgetManagement> clManagementLambdaQueryWrapperX = new LambdaQueryWrapperX(BudgetManagement.class);
            clManagementLambdaQueryWrapperX.selectSum(BudgetManagement::getBudgetMoney);
            clManagementLambdaQueryWrapperX.eq(BudgetManagement::getProjectId, projectId);
            clManagementLambdaQueryWrapperX.in(BudgetManagement::getExpenseSubjectNumber, clNumbers);
            BudgetManagement clBudgetManagement = budgetManagementService.getOne(clManagementLambdaQueryWrapperX);
            BigDecimal clBudgetMoney = BigDecimal.ZERO;
            if(ObjectUtil.isNotEmpty(clBudgetManagement)) {
                clBudgetMoney  = clBudgetManagement.getBudgetMoney();
            }
            budgetStatisticsVO.setMaterialsExpendMoney(clExpendMoney);
            budgetStatisticsVO.setMaterialsResidueMoney(clBudgetMoney.subtract(clExpendMoney));
            if (clBudgetMoney.compareTo(BigDecimal.ZERO) > 0) {
                budgetStatisticsVO.setMaterialsUseRatio(clExpendMoney.divide(clBudgetMoney, 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
            }
        }
        if (CollUtil.isNotEmpty(gzNumbers)) {
            LambdaQueryWrapperX<BudgetExpendForm> gzLambdaQueryWrapperX = new LambdaQueryWrapperX(BudgetExpendForm.class);
            gzLambdaQueryWrapperX.eq(BudgetExpendForm::getProjectId, projectId);
            gzLambdaQueryWrapperX.selectSum(BudgetExpendForm::getExpendMoney);
            gzLambdaQueryWrapperX.eq(BudgetExpendForm::getStatus, BudgetStatusEnum.COMPLETED.getCode());
            gzLambdaQueryWrapperX.in(BudgetExpendForm::getExpenseAccountNumber, gzNumbers);
            BudgetExpendForm gzBudgetExpendForm = budgetExpendFormService.getOne(gzLambdaQueryWrapperX);
            BigDecimal gzExpendMoney = BigDecimal.ZERO;
            if(ObjectUtil.isNotEmpty(gzBudgetExpendForm)) {
                gzExpendMoney  = gzBudgetExpendForm.getExpendMoney();
            }

            LambdaQueryWrapperX<BudgetManagement> gzManagementLambdaQueryWrapperX = new LambdaQueryWrapperX(BudgetManagement.class);
            gzManagementLambdaQueryWrapperX.selectSum(BudgetManagement::getBudgetMoney);
            gzManagementLambdaQueryWrapperX.in(BudgetManagement::getExpenseSubjectNumber, gzNumbers);
            gzManagementLambdaQueryWrapperX.eq(BudgetManagement::getProjectId, projectId);
            BudgetManagement gzBudgetManagement = budgetManagementService.getOne(gzManagementLambdaQueryWrapperX);
            BigDecimal gzBudgetMoney = BigDecimal.ZERO;
            if(ObjectUtil.isNotEmpty(gzBudgetManagement)) {
                gzBudgetMoney  = gzBudgetManagement.getBudgetMoney();
            }


            budgetStatisticsVO.setWageExpendMoney(gzExpendMoney);
            budgetStatisticsVO.setWageResidueMoney(gzBudgetMoney.subtract(gzExpendMoney));
            if (gzBudgetMoney.compareTo(BigDecimal.ZERO) > 0) {
                budgetStatisticsVO.setWageUseRatio(gzExpendMoney.divide(gzBudgetMoney, 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
            }
        }

        return budgetStatisticsVO;
    }


}
