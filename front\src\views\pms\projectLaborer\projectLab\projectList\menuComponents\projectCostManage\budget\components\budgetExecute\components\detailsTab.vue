<template>
  <div style="height: 100%;overflow: hidden">
    <OrionTable
      ref="tableRef"
      :options="baseTableOption"
    >
      <template #annex="{record}">
        <div
          class=" describe"
        >
          <Popover
            v-if="record.attachments.length>1"
            trigger="hover"
            placement="left"
            title="预览附件列表"
          >
            <template #content>
              <p
                v-for="(item,index) in record.attachments"
                :key="index"
                class="action-btn"
                @click="preview(item)"
              >
                {{ item?.name }}.{{ item?.filePostfix }}
              </p>
            </template>
            <span class="action-btn">
              附件列表
            </span>
          </Popover>
          <span
            v-if="record.attachments.length==1"
            class="action-btn"
            @click="preview(record.attachments[0])"
          >{{ record.attachments[0].name }}.{{ record.attachments[0].filePostfix }}</span>
          <span
            v-if="record.attachments.length==0"
          >--</span>
        </div>
      </template>
    </oriontable>
    <div />
  </div>
</template>
<script setup lang="ts">
import { ref, defineProps } from 'vue';
import {
  openFile,
  OrionTable, useDrawerInner,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
import {
  Modal, message, Popover,
} from 'ant-design-vue';
import { getBudgetExecuteDetails } from '/@/views/pms/api';
const props = defineProps({
  state: {
    type: Object,
  },
});
const tableRef = ref(null);
const columns = [
  {
    title: '成本支出编码',
    dataIndex: 'number',
  },
  {
    title: '成本中心',
    dataIndex: 'costCenterName',
  },
  {
    title: '科目',
    dataIndex: 'expenseAccountName',
  },
  {
    title: '发生时间',
    dataIndex: 'createTime',
    customRender({ record, text }) {
      return dayjs(record.createTime).format('YYYY-MM-DD');
    },
  },
  {
    title: '发生人',
    dataIndex: 'outPersonName',
  },
  {
    title: '金额(元)',
    dataIndex: 'outMoney',
  },
  {
    title: '描述',
    dataIndex: 'description',
  },
  {
    title: '附件',
    dataIndex: 'annex',
    fixed: 'right',
    minWidth: 200,
    slots: { customRender: 'annex' },
  },
];
function preview(fileData) {
  openFile(fileData);
}
const baseTableOption = {
  columns,
  api: (params) => getBudgetExecuteDetails({
    ...params,
    query: {
      projectId: props.state.projectId,
      budgetProjectId: props.state.id,
    },
  }),
  showToolButton: false,
  isFilter2: false,
  showSmallSearch: false,
  showTableSetting: false,
};
</script>
<style scoped lang="less">
.describe{
  display: flex;
  align-content: center;
  .edit{
    padding:12px 8px 12px 0;
  }
  .deleteBox{
    display: flex;
    justify-content: center;
    align-items: center;
    .delete{
      border-left: 1px solid rgba(0,0,0,.06);
      border-right: 1px solid rgba(0,0,0,.06);
      padding: 0 8px;
      height: 0.9em;
      line-height: 0.9em;
    }
  }
  .downloadAttachments{
    padding:12px 8px;
  }
}

</style>
