package com.chinasie.orion.service.impl;





import com.chinasie.orion.domain.entity.ContractSupplierSignedSubject;
import com.chinasie.orion.domain.dto.ContractSupplierSignedSubjectDTO;
import com.chinasie.orion.domain.vo.ContractSupplierSignedSubjectVO;



import com.chinasie.orion.service.ContractSupplierSignedSubjectService;
import com.chinasie.orion.repository.ContractSupplierSignedSubjectMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;




/**
 * <p>
 * ContractSupplierSignedSubject 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28 21:50:47
 */
@Service
@Slf4j
public class ContractSupplierSignedSubjectServiceImpl extends OrionBaseServiceImpl<ContractSupplierSignedSubjectMapper, ContractSupplierSignedSubject> implements ContractSupplierSignedSubjectService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  ContractSupplierSignedSubjectVO detail(String id,String pageCode) throws Exception {
        ContractSupplierSignedSubject contractSupplierSignedSubject =this.getById(id);
        ContractSupplierSignedSubjectVO result = BeanCopyUtils.convertTo(contractSupplierSignedSubject,ContractSupplierSignedSubjectVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param contractSupplierSignedSubjectDTO
     */
    @Override
    public  String create(ContractSupplierSignedSubjectDTO contractSupplierSignedSubjectDTO) throws Exception {
        ContractSupplierSignedSubject contractSupplierSignedSubject =BeanCopyUtils.convertTo(contractSupplierSignedSubjectDTO,ContractSupplierSignedSubject::new);
        this.save(contractSupplierSignedSubject);

        String rsp=contractSupplierSignedSubject.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param contractSupplierSignedSubjectDTO
     */
    @Override
    public Boolean edit(ContractSupplierSignedSubjectDTO contractSupplierSignedSubjectDTO) throws Exception {
        ContractSupplierSignedSubject contractSupplierSignedSubject =BeanCopyUtils.convertTo(contractSupplierSignedSubjectDTO,ContractSupplierSignedSubject::new);

        this.updateById(contractSupplierSignedSubject);

        String rsp=contractSupplierSignedSubject.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        for (String id : ids) {




        }
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ContractSupplierSignedSubjectVO> pages( Page<ContractSupplierSignedSubjectDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ContractSupplierSignedSubject> condition = new LambdaQueryWrapperX<>( ContractSupplierSignedSubject. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ContractSupplierSignedSubject::getCreateTime);


        Page<ContractSupplierSignedSubject> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ContractSupplierSignedSubject::new));

        PageResult<ContractSupplierSignedSubject> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ContractSupplierSignedSubjectVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ContractSupplierSignedSubjectVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ContractSupplierSignedSubjectVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "乙方签约主体导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractSupplierSignedSubjectDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ContractSupplierSignedSubjectExcelListener excelReadListener = new ContractSupplierSignedSubjectExcelListener();
        EasyExcel.read(inputStream,ContractSupplierSignedSubjectDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ContractSupplierSignedSubjectDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("乙方签约主体导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ContractSupplierSignedSubject> contractSupplierSignedSubjectes =BeanCopyUtils.convertListTo(dtoS,ContractSupplierSignedSubject::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ContractSupplierSignedSubject-import::id", importId, contractSupplierSignedSubjectes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ContractSupplierSignedSubject> contractSupplierSignedSubjectes = (List<ContractSupplierSignedSubject>) orionJ2CacheService.get("pmsx::ContractSupplierSignedSubject-import::id", importId);
        log.info("乙方签约主体导入的入库数据={}", JSONUtil.toJsonStr(contractSupplierSignedSubjectes));

        this.saveBatch(contractSupplierSignedSubjectes);
        orionJ2CacheService.delete("pmsx::ContractSupplierSignedSubject-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ContractSupplierSignedSubject-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ContractSupplierSignedSubject> condition = new LambdaQueryWrapperX<>( ContractSupplierSignedSubject. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ContractSupplierSignedSubject::getCreateTime);
        List<ContractSupplierSignedSubject> contractSupplierSignedSubjectes =   this.list(condition);

        List<ContractSupplierSignedSubjectDTO> dtos = BeanCopyUtils.convertListTo(contractSupplierSignedSubjectes, ContractSupplierSignedSubjectDTO::new);

        String fileName = "乙方签约主体数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractSupplierSignedSubjectDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<ContractSupplierSignedSubjectVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class ContractSupplierSignedSubjectExcelListener extends AnalysisEventListener<ContractSupplierSignedSubjectDTO> {

        private final List<ContractSupplierSignedSubjectDTO> data = new ArrayList<>();

        @Override
        public void invoke(ContractSupplierSignedSubjectDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ContractSupplierSignedSubjectDTO> getData() {
            return data;
        }
    }


}
