package com.chinasie.orion.constant;



public enum PurchIndexEnum {

    UQF("UQF","应招标未招标数量","1"),
    IMA("IMA","不当手段规避招标","2"),
    DAS("DAS","决策审批不规范","3"),
    NPR("NPR","流程倒置数量","4"),
    NER("NER","非必要紧急采购比例","5"),
    PRR("PRR","采购需求评审比例（>=400万）","6"),
    NCS("NCS","围标串标数量","7"),
    FCB("FCB","框架合同超范围/期限/金额（事件数量）","8"),
    NSM("NSM","不良行为供应商处置数量","9"),
    TCR("TCR","技术配置费占营收比例","10"),
    SSR("SSR","单一来源比例","11"),
    APC("APC","平均采购周期（标准合同）","12"),
    CSR("CSR","采购较立项节约比例","13"),
    SIP("SIP","采购较立项节约金额","14"),
    POC("POC","集采金额占比（含框架订单）","15"),
    NOP("NOP","人均在执行采购项目数量","16"),
    FAP("FAP","一次验收合格率","17"),
    TDR("TDR","及时交付率","18"),
    ACT("ACT","供应商引入平均完成时间","19"),
    NOT("NOT","技术人员当月在岗人数","20"),
    TCB("TCB","技术配置预算匹配执行率","21"),
    IRQ("IRQ","供应商引入理由不充分退单数量","22"),
    NTV("NTV","技术配置相关违法违规数量","23"),
    TCS("TCS","供应商复审及时完成率","24"),
    TFC("TFC","技术配置框架合同到期按时续签率","25"),
    PPR("PPR","公开采购比例","26"),
    NON("NON","非邀请供应商平均报名数量","27"),
    SCR("SCR","重点供应商检查覆盖率","28"),
    SQE("SQE","供应商查询服务有效性","29"),
    NDE("NDE","当月离岗人数","30"),
    ACR("ACR","累计流动比例","31"),
    TOL("TOL","非技术推荐供应商参与竞争项目","32"),

    SSRN("SSRN","单一来源比例(数量）","33"),

    APCA("APCA","平均采购周期（所有合同）","34"),

    NTH("NTH","非技术推荐供应商有效报价合同数量","35"),
    NTW("NTW","非技术推荐供应商中标合同数量","36");

    private String name;
    private String desc;
    private String indexOrder;

    PurchIndexEnum(String name, String desc, String indexOrder) {
        this.name = name;
        this.desc = desc;
        this.indexOrder = indexOrder;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }
    public String getIndexOrder() {
        return indexOrder;
    }

    public static String getDesc(String value){

        for (PurchIndexEnum lt : PurchIndexEnum.values()) {
            if(lt.name.equals( value)){
                return lt.desc;
            }
        }
        return null;
    }

    public static String getIndexOrder(String value){

        for (PurchIndexEnum lt : PurchIndexEnum.values()) {
            if(lt.name.equals( value)){
                return lt.indexOrder;
            }
        }
        return null;
    }


}