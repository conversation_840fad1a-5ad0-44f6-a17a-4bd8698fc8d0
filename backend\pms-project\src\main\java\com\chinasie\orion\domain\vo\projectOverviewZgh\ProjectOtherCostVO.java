package com.chinasie.orion.domain.vo.projectOverviewZgh;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 设备/软件使用费，日常行政管理费用，前台项目部成本分摊，项目毛利，内部委托成本，税费
 */
@Data
@ApiModel(value = "ProjectOtherCostVO", description = "设备/软件使用费，日常行政管理费用，前台项目部成本分摊，项目毛利，内部委托成本，税费")
public class ProjectOtherCostVO {


    @ApiModelProperty("设备/软件使用费")
    private DeviceSoftwareVO deviceSoftware;

    @ApiModelProperty("日常行政管理费用")
    private DailyAdministrativeVO dailyAdministrative;

    @ApiModelProperty("前台项目部成本分摊")
    private FrontOfficeVO frontOffice;

    @ApiModelProperty("项目毛利")
    private ProjectGrossProfitVO projectGrossProfit;

    @ApiModelProperty("内部委托成本")
    private InternalDelegateVO internalDelegate;

    @ApiModelProperty("税费")
    private TaxVO tax;

    @Data
    @ApiModel(value = "ProjectOtherCostVO", description = "设备/软件使用费")
    public static class DeviceSoftwareVO {
        @ApiModelProperty("设备/软件使用费")
        private BigDecimal useCost= BigDecimal.ZERO;

        @ApiModelProperty("计划")
        private BigDecimal planAmount= BigDecimal.ZERO;

        @ApiModelProperty("达成率")
        private BigDecimal schedule= BigDecimal.ZERO;

        @ApiModelProperty("结余")
        private BigDecimal surplusAmount= BigDecimal.ZERO;

    }

    @Data
    @ApiModel(value = "DailyAdministrativeVO", description = "日常行政管理费用")
    public static class DailyAdministrativeVO {
        @ApiModelProperty("日常行政管理费用")
        private BigDecimal useCost= BigDecimal.ZERO;

        @ApiModelProperty("达成率")
        private BigDecimal schedule= BigDecimal.ZERO;

        @ApiModelProperty("结余")
        private BigDecimal surplusAmount= BigDecimal.ZERO;

    }

    @Data
    @ApiModel(value = "FrontOfficeVO", description = "前台项目部成本分摊")
    public static class FrontOfficeVO {
        @ApiModelProperty("前台项目部成本分摊")
        private BigDecimal useCost= BigDecimal.ZERO;

        @ApiModelProperty("达成率")
        private BigDecimal schedule= BigDecimal.ZERO;

        @ApiModelProperty("结余")
        private BigDecimal surplusAmount= BigDecimal.ZERO;
    }

    @Data
    @ApiModel(value = "ProjectGrossProfitVO", description = "项目毛利")
    public static class ProjectGrossProfitVO {
        @ApiModelProperty("项目毛利")
        private BigDecimal grossProfit= BigDecimal.ZERO;

        @ApiModelProperty("项目毛利率")
        private BigDecimal grossProfitRate= BigDecimal.ZERO;

        @ApiModelProperty("计划比")
        private BigDecimal planPercent= BigDecimal.ZERO;

        @ApiModelProperty("实际比")
        private BigDecimal practicePercent= BigDecimal.ZERO;
    }

    @Data
    @ApiModel(value = "InternalDelegateVO", description = "内部委托成本")
    public static class InternalDelegateVO {
        @ApiModelProperty("内部委托成本")
        private BigDecimal useCost= BigDecimal.ZERO;

        @ApiModelProperty("计划金额")
        private BigDecimal planAmount= BigDecimal.ZERO;

        @ApiModelProperty("结余金额")
        private BigDecimal surplusAmount= BigDecimal.ZERO;
    }


    @Data
    @ApiModel(value = "TaxVO", description = "税费")
    public static class TaxVO {
        @ApiModelProperty("税费")
        private BigDecimal useCost= BigDecimal.ZERO;

        @ApiModelProperty("计划金额")
        private BigDecimal planAmount= BigDecimal.ZERO;

        @ApiModelProperty("结余金额")
        private BigDecimal surplusAmount= BigDecimal.ZERO;
    }

}
