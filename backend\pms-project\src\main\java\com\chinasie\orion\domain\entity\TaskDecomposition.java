package com.chinasie.orion.domain.entity;

import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.DeptDataBind;
import com.chinasie.orion.sdk.core.data.bind.DictDataBind;
import com.chinasie.orion.sdk.core.data.bind.UserDataBind;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;

/**
 * TaskDecomposition Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-01 17:00:34
 */
@TableName(value = "pms_task_decomposition")
@ApiModel(value = "TaskDecompositionEntity对象", description = "任务分解")
@Data

public class TaskDecomposition extends  ObjectEntity  implements Serializable{

    /**
     * 父级id
     */
    @ApiModelProperty(value = "父级id")
    @TableField(value = "parent_id")
    private String parentId;

    /**
     * 责任部门
     */
    @ApiModelProperty(value = "责任部门")
    @TableField(value = "rsp_dept")
    @FieldBind(dataBind = DeptDataBind.class, target = "rspDeptName")
    private String rspDept;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    @TableField(value = "rsp_user")
    @FieldBind(dataBind = UserDataBind.class, target = "rspUserName")
    private String rspUser;

    /**
     * 工期
     */
    @ApiModelProperty(value = "工期")
    @TableField(value = "work_time")
    private Integer workTime;

    /**
     * 处理实例
     */
    @ApiModelProperty(value = "处理实例")
    @TableField(value = "process_instances")
    private String processInstances;

    /**
     * 处理对象
     */
    @ApiModelProperty(value = "处理对象")
    @TableField(value = "process_object")
    @FieldBind(dataBind = DictDataBind.class, type = "process_object_type", target = "processObjectName")
    private String processObject;


    /**
     *  文档分解ID
     */
    @ApiModelProperty(value = "文档分解ID")
    @TableField(value = "documentDecomposition_id")
    private String documentDecompositionId;

    /**
     *  任务名称
     */
    @ApiModelProperty(value = " 任务名称")
    @TableField(value = "name")
    private String name;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @TableField(value = "main_table_id")
    private String mainTableId;

    @ApiModelProperty(value = "处理对象名称")
    @TableField(exist = false)
    private String processObjectName;


    /**
     * 责任人名称
     */
    @ApiModelProperty(value = "责任人名称")
    @TableField(exist = false)
    private String rspUserName;

    /**
     * 责任部门名称
     */
    @ApiModelProperty(value = "责任部门名称")
    @TableField(exist = false)
    private String rspDeptName;
}
