package com.chinasie.orion.service.Impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.base.api.domain.entity.DeptDO;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.domain.dto.NcfPurchProjectImplementationDTO;
import com.chinasie.orion.domain.entity.NcfPurchProjectImplementation;
import com.chinasie.orion.domain.entity.PurchProjectWeekly;
import com.chinasie.orion.domain.vo.NcfPurchProjectImplementationVO;
import com.chinasie.orion.repository.NcfPurchProjectImplementationMapper;
import com.chinasie.orion.service.NcfPurchProjectImplementationService;
import com.chinasie.orion.service.PurchProjectWeeklyService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * <p>
 * NcfPurchProjectImplementation 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12 10:24:16
 */
@Service
@Slf4j
public class NcfPurchProjectImplementationServiceImpl extends OrionBaseServiceImpl<NcfPurchProjectImplementationMapper, NcfPurchProjectImplementation> implements NcfPurchProjectImplementationService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;
    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    @Autowired
    private UserRedisHelper userRedisHelper;
    @Lazy
    @Autowired
    private PurchProjectWeeklyService purchProjectWeeklyService;
    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public NcfPurchProjectImplementationVO detail(String id, String pageCode) throws Exception {
        NcfPurchProjectImplementation ncfPurchProjectImplementation = this.getById(id);
        NcfPurchProjectImplementationVO result = BeanCopyUtils.convertTo(ncfPurchProjectImplementation, NcfPurchProjectImplementationVO::new);
        //setEveryName(Collections.singletonList(result));
       if (StringUtils.isEmpty(result.getExecutionStatus())||"已完成".equals(result.getExecutionStatus())){
           result.setExecutionStatus("一级分发");
       }


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param ncfPurchProjectImplementationDTO
     */
    @Override
    public String create(NcfPurchProjectImplementationDTO ncfPurchProjectImplementationDTO) throws Exception {
        NcfPurchProjectImplementation ncfPurchProjectImplementation = BeanCopyUtils.convertTo(ncfPurchProjectImplementationDTO, NcfPurchProjectImplementation::new);
        this.save(ncfPurchProjectImplementation);

        String rsp = ncfPurchProjectImplementation.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param ncfPurchProjectImplementationDTO
     */
    @Override
    public Boolean edit(NcfPurchProjectImplementationDTO ncfPurchProjectImplementationDTO) throws Exception {
        NcfPurchProjectImplementation ncfPurchProjectImplementation = BeanCopyUtils.convertTo(ncfPurchProjectImplementationDTO, NcfPurchProjectImplementation::new);

        this.updateById(ncfPurchProjectImplementation);

        String rsp = ncfPurchProjectImplementation.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<NcfPurchProjectImplementationVO> pages(Page<NcfPurchProjectImplementationDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<NcfPurchProjectImplementation> condition = new LambdaQueryWrapperX<>(NcfPurchProjectImplementation.class);
        condition.leftJoin(DeptDO.class,DeptDO::getDeptCode,NcfPurchProjectImplementation::getApplyDepartmentCode);
        condition.leftJoin(DeptDO.class,DeptDO::getId,DeptDO::getParentId);
        condition.select(
                "t.*",
                "t2.name as applyCompany"
        );
//        condition.apply("where dept2.type = 10");
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        if (pageRequest.getQuery() != null) {
            //upm审批完成时间段
            if (pageRequest.getQuery().getStartDate() != null && pageRequest.getQuery().getEndDate() != null) {
                condition.between(NcfPurchProjectImplementation::getUpmApprovalComplete, pageRequest.getQuery().getStartDate(), pageRequest.getQuery().getEndDate());
            }

            //采购申请完成时间段
            if (pageRequest.getQuery().getApproveStartDate() != null && pageRequest.getQuery().getApproveEndDate() != null) {
                condition.between(NcfPurchProjectImplementation::getPurchReqEndTime, pageRequest.getQuery().getApproveStartDate(), pageRequest.getQuery().getApproveEndDate());
            }

            //已经耗时时间段
            if (pageRequest.getQuery().getStartUsedTime() != null && pageRequest.getQuery().getEndUsedTime() != null) {
                condition.apply("CAST(used_time AS SIGNED) >= {0} and CAST(used_time AS SIGNED) <= {1}", Integer.valueOf(pageRequest.getQuery().getStartUsedTime()), Integer.valueOf(pageRequest.getQuery().getEndUsedTime()));
            }
            //合同状态
            if (StringUtils.hasText(pageRequest.getQuery().getContractState())) {
                //0 进行中
                if("0".equals(pageRequest.getQuery().getContractState())){
                    condition.ne(NcfPurchProjectImplementation::getExecutionStatus,"发送SAP");
                }
                //1 已完成
                if("1".equals(pageRequest.getQuery().getContractState())){
                    condition.eq(NcfPurchProjectImplementation::getExecutionStatus,"发送SAP");
                }
            }
        }
        //发起时间倒叙
        condition.orderByDesc(NcfPurchProjectImplementation::getInitiationTime);


        Page<NcfPurchProjectImplementation> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), NcfPurchProjectImplementation::new));

        PageResult<NcfPurchProjectImplementation> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<NcfPurchProjectImplementationVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<NcfPurchProjectImplementationVO> vos = BeanCopyUtils.convertListTo(page.getContent(), NcfPurchProjectImplementationVO::new);

        //增加周报的商务人员权限
        CurrentUserHelper.getCurrentUserId();
        UserVO user = userRedisHelper.getUser(CurrentUserHelper.getOrgId(), CurrentUserHelper.getCurrentUserId());
        Pattern pattern = Pattern.compile("\\[(.*?)\\]");
        for (NcfPurchProjectImplementationVO vo : vos) {
            String executionStatus = vo.getExecutionStatus();
            if (StringUtils.isEmpty(executionStatus)||"已完成".equals(executionStatus)){
                vo.setExecutionStatus("一级分发");
            }
            if(!StringUtils.isEmpty(vo.getBizRespons()) ){
                List<String> bizResponsList = Arrays.asList(vo.getBizRespons().split(","));
                for (String bizRespon : bizResponsList) {
                    Matcher matcher = pattern.matcher(bizRespon);
                    if (matcher.find()) {
                        String userCode = matcher.group(1);
                        if(user != null && user.getCode() != null && user.getCode().equals(userCode)){
                            vo.setIsEdit(true);
                            break;
                        }
                    }
                }
            }
        }
        setEveryName(vos);
        //计算阶段耗时
        for (NcfPurchProjectImplementationVO vo : vos) {
            calculateDurationsDirectly(vo);
        }
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "采购项目实施表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", NcfPurchProjectImplementationDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        NcfPurchProjectImplementationExcelListener excelReadListener = new NcfPurchProjectImplementationExcelListener();
        EasyExcel.read(inputStream, NcfPurchProjectImplementationDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<NcfPurchProjectImplementationDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("采购项目实施表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<NcfPurchProjectImplementation> ncfPurchProjectImplementationes = BeanCopyUtils.convertListTo(dtoS, NcfPurchProjectImplementation::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pms::NcfPurchProjectImplementation-import::id", importId, ncfPurchProjectImplementationes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<NcfPurchProjectImplementation> ncfPurchProjectImplementationes = (List<NcfPurchProjectImplementation>) orionJ2CacheService.get("pms::NcfPurchProjectImplementation-import::id", importId);
        log.info("采购项目实施表导入的入库数据={}", JSONUtil.toJsonStr(ncfPurchProjectImplementationes));

        this.saveBatch(ncfPurchProjectImplementationes);
        orionJ2CacheService.delete("pms::NcfPurchProjectImplementation-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pms::NcfPurchProjectImplementation-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(Page<NcfPurchProjectImplementationDTO> pageRequest, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<NcfPurchProjectImplementation> condition = new LambdaQueryWrapperX<>(NcfPurchProjectImplementation.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        if (pageRequest.getQuery() != null) {
            //upm审批完成时间段
            if (pageRequest.getQuery().getStartDate() != null && pageRequest.getQuery().getEndDate() != null) {
                condition.between(NcfPurchProjectImplementation::getUpmApprovalComplete, pageRequest.getQuery().getStartDate(), pageRequest.getQuery().getEndDate());
            }

            //采购申请完成时间段
            if (pageRequest.getQuery().getApproveStartDate() != null && pageRequest.getQuery().getApproveEndDate() != null) {
                condition.between(NcfPurchProjectImplementation::getPurchReqEndTime, pageRequest.getQuery().getApproveStartDate(), pageRequest.getQuery().getApproveEndDate());
            }

            //已经耗时时间段
            if (pageRequest.getQuery().getStartUsedTime() != null && pageRequest.getQuery().getEndUsedTime() != null) {
                condition.apply("CAST(used_time AS SIGNED) >= {0} and CAST(used_time AS SIGNED) <= {1}", Integer.valueOf(pageRequest.getQuery().getStartUsedTime()), Integer.valueOf(pageRequest.getQuery().getEndUsedTime()));
            }
            if (pageRequest.getQuery().getContractState() != null) {
                //0 进行中
                if("0".equals(pageRequest.getQuery().getContractState())){
                    condition.ne(NcfPurchProjectImplementation::getExecutionStatus,"发送SAP");
                }
                //1 已完成
                if("1".equals(pageRequest.getQuery().getContractState())){
                    condition.eq(NcfPurchProjectImplementation::getExecutionStatus,"发送SAP");
                }
            }
        }
        condition.orderByDesc(NcfPurchProjectImplementation::getCreateTime);
        List<NcfPurchProjectImplementation> ncfPurchProjectImplementationes = this.list(condition);
        List<NcfPurchProjectImplementationVO> vos = BeanCopyUtils.convertListTo(ncfPurchProjectImplementationes, NcfPurchProjectImplementationVO::new);
        setEveryName(vos);
        for (NcfPurchProjectImplementationVO vo : vos) {
            String executionStatus = vo.getExecutionStatus();
            if (StringUtils.isEmpty(executionStatus)||"已完成".equals(executionStatus)){
                vo.setExecutionStatus("一级分发");
            }
        }

//
//        for (NcfPurchProjectImplementationDTO dto : dtos) {
//            if (dto.getContractState().equals("已完成") ||StringUtils.isEmpty(dto.getContractState())){
//                dto.setContractState("一级分发");
//            }
//        }

        List<NcfPurchProjectImplementationDTO> dtos = BeanCopyUtils.convertListTo(vos, NcfPurchProjectImplementationDTO::new);
        String fileName = "采购项目实施表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", NcfPurchProjectImplementationDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<NcfPurchProjectImplementationVO> vos) throws Exception {
//        final Date NOW = new Date();
//        Map<String, Function<NcfPurchProjectImplementationVO, Boolean>> STATUS_CHECKERS = new LinkedHashMap<>();
//        STATUS_CHECKERS.put("发送SAP", vo -> vo.getSendSap() != null && vo.getSendSap().before(NOW));
//        STATUS_CHECKERS.put("UPM审批完成", vo -> vo.getUpmApprovalComplete() != null && vo.getUpmApprovalComplete().before(NOW));
//        STATUS_CHECKERS.put("UPM合同推荐", vo -> vo.getUpmApprovalInProgress() != null && vo.getUpmApprovalInProgress().before(NOW));
//        STATUS_CHECKERS.put("评审时间", vo -> vo.getReviewTime() != null && vo.getReviewTime().before(NOW));
//        STATUS_CHECKERS.put("开启报价", vo -> vo.getOpenQuote() != null && vo.getOpenQuote().before(NOW));
//        STATUS_CHECKERS.put("报价截止", vo -> vo.getQuoteEnd() != null && vo.getQuoteEnd().before(NOW));
//        STATUS_CHECKERS.put("询价签发", vo -> vo.getInqIssuance() != null && vo.getInqIssuance().before(NOW));
//        STATUS_CHECKERS.put("采购启动审批", vo -> vo.getPurchStartApproval() != null && vo.getPurchStartApproval().before(NOW));
//        STATUS_CHECKERS.put("采购启动", vo -> vo.getPurchStart() != null && vo.getPurchStart().before(NOW));
//        STATUS_CHECKERS.put("接受确认", vo -> vo.getAcceptConfirmation() != null && vo.getAcceptConfirmation().before(NOW));
//        STATUS_CHECKERS.put("二级分发", vo -> vo.getSecondaryDistribution() != null && vo.getSecondaryDistribution().before(NOW));
//        STATUS_CHECKERS.put("一级分发", vo -> vo.getFirstDistribution() != null && vo.getSendSap() != null && vo.getSendSap().before(NOW));
//
//        vos.forEach(vo -> {
//
//            // 使用Stream API和映射表来查找和设置执行状态
//            STATUS_CHECKERS.entrySet().stream()
//                    .filter(entry -> entry.getValue().apply(vo))
//                    .findFirst() // 只处理第一个满足条件的执行状态
//                    .ifPresent(entry -> vo.setExecutionStatus(entry.getKey()));
//
////            //设置已经耗时，用采购申请完成时间和当前系统时间做计算，如果【UPM审批完成】没有值，就每天向上累加。如果【UPM审批完成】有值，那就停止累加。
////            if (vo.getUpmApprovalComplete() == null) {
////                LocalDate start = vo.getPurchReqEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
////                LocalDate end = NOW.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
////                long daysBetween = ChronoUnit.DAYS.between(start, end);
////                vo.setUsedTime(String.valueOf(daysBetween));
////            } else {
////                LocalDate start = vo.getUpmApprovalComplete().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
////                LocalDate end = NOW.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
////                long daysBetween = ChronoUnit.DAYS.between(start, end);
////                vo.setUsedTime(String.valueOf(daysBetween));
////            }
//        });
        //设置已经耗时，用采购申请完成时间和当前系统时间做计算，如果【UPM审批完成】没有值，就每天向上累加。如果【UPM审批完成】有值，那就停止累加。
        if(CollUtil.isNotEmpty(vos)) {
            List<String> purchReqDocCodes =  vos.stream().map(NcfPurchProjectImplementationVO::getPurchReqDocCode)
                    .filter(Objects::nonNull).distinct().collect(Collectors.toList());
            Map<String,Boolean> map = new HashMap<>();
            if(!CollectionUtils.isEmpty(purchReqDocCodes)){
                LambdaQueryWrapperX<PurchProjectWeekly> wrapperX = new LambdaQueryWrapperX<>(PurchProjectWeekly.class);
                wrapperX.in(PurchProjectWeekly::getPurchReqDocCode, purchReqDocCodes);
                wrapperX.select(PurchProjectWeekly::getPurchReqDocCode, PurchProjectWeekly::getIsSign);
                List<PurchProjectWeekly> purchProjectWeeklys = purchProjectWeeklyService.list(wrapperX);
                for (PurchProjectWeekly vo : purchProjectWeeklys) {
                    if(Objects.equals(Boolean.TRUE,vo.getIsSign())){
                        map.put(vo.getPurchReqDocCode(),vo.getIsSign());
                    }
                }
            }
            vos.forEach(vo -> {
                vo.setIsSign(map.get(vo.getPurchReqDocCode()));
                if (StrUtil.equals(vo.getExecutionStatus(), "采购立项完成")) {
                    vo.setExecutionStatus("一级分发");
                } else  if (StrUtil.equals(vo.getExecutionStatus(), "一级分发")) {
                    vo.setExecutionStatus("二级分发");
                } else if (StrUtil.equals(vo.getExecutionStatus(), "二级分发")) {
                    vo.setExecutionStatus("接受确认");
                } else if (StrUtil.equals(vo.getExecutionStatus(), "接受确认")) {
                    vo.setExecutionStatus("采购启动");
                }else if (StrUtil.equals(vo.getExecutionStatus(), "采购启动")) {
                    vo.setExecutionStatus("采购启动审批");
                }else if (StrUtil.equals(vo.getExecutionStatus(), "采购启动审批")) {
                    vo.setExecutionStatus("询价签发");
                }else if (StrUtil.equals(vo.getExecutionStatus(), "询价签发")) {
                    vo.setExecutionStatus("报价截止");
                }else if (StrUtil.equals(vo.getExecutionStatus(), "报价截止")) {
                    vo.setExecutionStatus("开启报价");
                }else if (StrUtil.equals(vo.getExecutionStatus(), "开启报价")) {
                    vo.setExecutionStatus("采购评审");
                }else if (StrUtil.equals(vo.getExecutionStatus(), "采购评审")) {
                    vo.setExecutionStatus("UPM合同推荐");
                }else if (StrUtil.equals(vo.getExecutionStatus(), "UPM合同推荐")) {
                    vo.setExecutionStatus("UPM审批完成");
                }else if (StrUtil.equals(vo.getExecutionStatus(), "UPM审批完成")) {
                    vo.setExecutionStatus("发送SAP");
                } else if (StrUtil.equals(vo.getExecutionStatus(), "发送SAP")) {
                    vo.setExecutionStatus("已完成");
                }
            });
        }
    }

    @Override
    public Map<String, Object> getNumMoney(Page<NcfPurchProjectImplementationDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<NcfPurchProjectImplementation> condition = new LambdaQueryWrapperX<>(NcfPurchProjectImplementation.class);
        condition.leftJoin(DeptDO.class,DeptDO::getDeptCode,NcfPurchProjectImplementation::getApplyDepartmentCodeTran);
        condition.leftJoin(DeptDO.class,DeptDO::getId,DeptDO::getParentId);

        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        if (pageRequest.getQuery() != null) {
            //upm审批完成时间段
            if (pageRequest.getQuery().getStartDate() != null && pageRequest.getQuery().getEndDate() != null) {
                condition.between(NcfPurchProjectImplementation::getUpmApprovalComplete, pageRequest.getQuery().getStartDate(), pageRequest.getQuery().getEndDate());
            }

            //采购申请完成时间段
            if (pageRequest.getQuery().getApproveStartDate() != null && pageRequest.getQuery().getApproveEndDate() != null) {
                condition.between(NcfPurchProjectImplementation::getPurchReqEndTime, pageRequest.getQuery().getApproveStartDate(), pageRequest.getQuery().getApproveEndDate());
            }

            //已经耗时时间段
            if (pageRequest.getQuery().getStartUsedTime() != null && pageRequest.getQuery().getEndUsedTime() != null) {
                condition.apply("CAST(used_time AS SIGNED) >= {0} and CAST(used_time AS SIGNED) <= {1}", Integer.valueOf(pageRequest.getQuery().getStartUsedTime()), Integer.valueOf(pageRequest.getQuery().getEndUsedTime()));
            }
            if (pageRequest.getQuery().getContractState() != null) {
                //0 进行中
                if("0".equals(pageRequest.getQuery().getContractState())){
                    condition.ne(NcfPurchProjectImplementation::getExecutionStatus,"发送SAP");
                }
                //1 已完成
                if("1".equals(pageRequest.getQuery().getContractState())){
                    condition.eq(NcfPurchProjectImplementation::getExecutionStatus,"发送SAP");
                }
            }
        }
        condition.orderByDesc(NcfPurchProjectImplementation::getCreateTime);

        String sql = " count(*) as total," +
                "sum(purch_req_amount) as allMoney";
        condition.select(sql);
        Map map = this.getMap(condition);
        return map;
    }

    @Override
    public List<NcfPurchProjectImplementation> getImplementationList(LocalDate start, LocalDate end) {
        LocalDateTime localDateTime = end.atTime(23,59,59);
        String sql = "select purch_req_ecp_code,biz_respons from pms_ncf_purch_project_implementation pi  left join pmi_dept d on d.dept_code = pi.apply_department_code  where pi.logic_status = '1' " +
                "and pi.purch_req_end_time between '" + start + "' and '" + localDateTime + "' and (pi.upm_approval_complete is null or pi.upm_approval_complete > '" + localDateTime + "')" + " and d.chain not like '%00013000%'";
        List<Map<String, Object>> mapList = this.namedParameterJdbcTemplate.queryForList(sql, new HashMap<>());
        List<NcfPurchProjectImplementation> infoList = new ArrayList<>();
        for (Map<String, Object> map : mapList) {
            NcfPurchProjectImplementation info = new NcfPurchProjectImplementation();
            info.setPurchReqEcpCode(map.get("purch_req_ecp_code").toString());
            info.setBizRespons(map.get("biz_respons").toString());
            infoList.add(info);
        }
        return infoList;
    }


    public static class NcfPurchProjectImplementationExcelListener extends AnalysisEventListener<NcfPurchProjectImplementationDTO> {

        private final List<NcfPurchProjectImplementationDTO> data = new ArrayList<>();

        @Override
        public void invoke(NcfPurchProjectImplementationDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<NcfPurchProjectImplementationDTO> getData() {
            return data;
        }
    }


    /**
     * 按照字段声明顺序计算节点耗时
     * @param vo 要计算的实体对象
     * 规则：
     * 1. 第一个节点未完成：当前时间 - initiationTime
     * 2. 其他节点：当前节点时间 - 上一节点时间
     * 3. 最后一个已完成节点的耗时设为0
     */
    public void calculateDurationsDirectly(NcfPurchProjectImplementationVO vo) {
        Date currentTime = new Date();
        Date lastCompletedTime = vo.getPurchReqEndTime();

        // 第一个节点特殊处理
        if (vo.getFirstDistribution() == null) {
            vo.setFirstDistributionDuration(calculateDaysBetween(lastCompletedTime, currentTime));
            return;
        } else {
            vo.setFirstDistributionDuration(calculateDaysBetween(lastCompletedTime, vo.getFirstDistribution()));
            lastCompletedTime = vo.getFirstDistribution();
        }

        // 二级分发
        if (vo.getSecondaryDistribution() != null) {
            vo.setSecondaryDistributionDuration(calculateDaysBetween(lastCompletedTime, vo.getSecondaryDistribution()));
            lastCompletedTime = vo.getSecondaryDistribution();
        } else {
            vo.setSecondaryDistributionDuration(calculateDaysBetween(lastCompletedTime, currentTime));
            return;
        }

        // 接受确认
        if (vo.getAcceptConfirmation() != null) {
            vo.setAcceptConfirmationDuration(calculateDaysBetween(lastCompletedTime, vo.getAcceptConfirmation()));
            lastCompletedTime = vo.getAcceptConfirmation();
        } else {
            vo.setAcceptConfirmationDuration(calculateDaysBetween(lastCompletedTime, currentTime));
            return;
        }

        // 采购启动发起
        if (vo.getPurchStart() != null) {
            vo.setPurchStartDuration(calculateDaysBetween(lastCompletedTime, vo.getPurchStart()));
            lastCompletedTime = vo.getPurchStart();
        } else {
            vo.setPurchStartDuration(calculateDaysBetween(lastCompletedTime, currentTime));
            return;
        }

        // 采购启动审批
        if (vo.getPurchStartApproval() != null) {
            vo.setPurchStartApprovalDuration(calculateDaysBetween(lastCompletedTime, vo.getPurchStartApproval()));
            lastCompletedTime = vo.getPurchStartApproval();
        } else {
            vo.setPurchStartApprovalDuration(calculateDaysBetween(lastCompletedTime, currentTime));
            return;
        }

        // 询价签发
        if (vo.getInqIssuance() != null) {
            vo.setInqIssuanceDuration(calculateDaysBetween(lastCompletedTime, vo.getInqIssuance()));
            lastCompletedTime = vo.getInqIssuance();
        } else {
            vo.setInqIssuanceDuration(calculateDaysBetween(lastCompletedTime, currentTime));
        }

        // 报价截止
        if (vo.getQuoteEnd() != null) {
            vo.setQuoteEndDuration(calculateDaysBetween(lastCompletedTime, vo.getQuoteEnd()));
            lastCompletedTime = vo.getQuoteEnd();
        } else {
            vo.setQuoteEndDuration(calculateDaysBetween(lastCompletedTime, currentTime));
            return;
        }

        // 开启报价
        if (vo.getOpenQuote() != null) {
            vo.setOpenQuoteDuration(calculateDaysBetween(lastCompletedTime, vo.getOpenQuote()));
            lastCompletedTime = vo.getOpenQuote();
        } else {
            vo.setOpenQuoteDuration(calculateDaysBetween(lastCompletedTime, currentTime));
            return;
        }

        // 评审时间
        if (vo.getReviewTime() != null) {
            vo.setReviewTimeDuration(calculateDaysBetween(lastCompletedTime, vo.getReviewTime()));
            lastCompletedTime = vo.getReviewTime();
        } else {
            vo.setReviewTimeDuration(calculateDaysBetween(lastCompletedTime, currentTime));
            return;
        }

        // 公示发布时间
        if (vo.getReviewOutTime() != null) {
            vo.setReviewOutTimeDuration(calculateDaysBetween(lastCompletedTime, vo.getReviewOutTime()));
            lastCompletedTime = vo.getReviewOutTime();
        } else {
            vo.setReviewOutTimeDuration(calculateDaysBetween(lastCompletedTime, currentTime));
            return;
        }

        // UPM审批中
        if (vo.getUpmApprovalInProgress() != null) {
            vo.setUpmApprovalInProgressDuration(calculateDaysBetween(lastCompletedTime, vo.getUpmApprovalInProgress()));
            lastCompletedTime = vo.getUpmApprovalInProgress();
        } else {
            vo.setUpmApprovalInProgressDuration(calculateDaysBetween(lastCompletedTime, currentTime));
            return;
        }

        // UPM审批完成
        if (vo.getUpmApprovalComplete() != null) {
            vo.setUpmApprovalCompleteDuration(calculateDaysBetween(lastCompletedTime, vo.getUpmApprovalComplete()));
            lastCompletedTime = vo.getUpmApprovalComplete();
        } else {
            vo.setUpmApprovalCompleteDuration(calculateDaysBetween(lastCompletedTime, currentTime));
            return;
        }

        // 发送SAP
        if (vo.getSendSap() != null) {
            vo.setSendSapDuration(calculateDaysBetween(lastCompletedTime, vo.getSendSap()));
            // 最后一个节点耗时设为0
            vo.setSendSapDuration(calculateDaysBetween(lastCompletedTime, currentTime));
        } else {
            vo.setSendSapDuration(calculateDaysBetween(lastCompletedTime, currentTime));
            return;
        }
    }

    private static long calculateDaysBetween(Date startDate, Date endDate) {
        if (startDate == null || endDate == null) return 0;
        long diffInMillis = endDate.getTime() - startDate.getTime();
        return TimeUnit.DAYS.convert(diffInMillis, TimeUnit.MILLISECONDS);
    }

}
