<script setup lang="ts">

import {
  computed, inject, reactive, watchEffect,
} from 'vue';
import { BasicCard } from 'lyra-component-vue3';

const detailsData: Record<string, any> = computed(() => inject('detailsData'));
const planInfo = reactive({
  list: [
    {
      label: '大修轮次',
      field: 'repairRound',
    },
    {
      label: '大修类别',
      field: 'typeName',
    },
    {
      label: '所属基地',
      field: 'baseName',
    },
    {
      label: '计划开始时间',
      field: 'beginTime',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '计划结束时间',
      field: 'endTime',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '计划工期',
      field: 'workDuration',
    },
    {
      label: '实际开始时间',
      field: 'actualBeginTime',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '实际结束时间',
      field: 'actualEndTime',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '实际工期',
      field: 'actualWorkDuration',
    },
    {
      label: '大修经理',
      field: 'repairManagerName',
    },
    {
      label: '大修状态',
      field: 'dataStatus',
    },
  ],
  dataSource: {},
});

watchEffect(() => {
  planInfo.dataSource = detailsData.value;
});
</script>

<template>
  <BasicCard
    :is-border="false"
    :grid-content-props="planInfo"
  />
</template>

<style scoped lang="less">

</style>
