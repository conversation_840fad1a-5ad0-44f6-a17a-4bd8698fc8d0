<template>
  <div class="locations">
    <OrionTable
      ref="tableRef"
      :options="tableOptionsSource"
      @selectionChange="selectionChange"
    >
      <template #toolbarLeft>
        <BasicButton
          type="primary"
          icon="sie-icon-tianjiaxinzeng"
          @click="addNode"
        >
          新增地点
        </BasicButton>
        <BasicButton
          icon="sie-icon-shanchu"
          :disabled="selectRowKeys.length===0"
          @click="deleteNode"
        >
          删除
        </BasicButton>
      </template>
      <template
        #areaId="{record}"
      >
        <div class="province-record">
          <span v-if="!record.isEdit">{{ record.area }}</span>
          <ACascader
            v-else
            v-model:value="record.areaId"
            :changeOnSelect="true"
            :options="cascaderOptions"
            :load-data="loadData"
            :fieldNames="{label:'area',value:'areaId'}"
          />
        </div>
      </template>
    </OrionTable>
  </div>
</template>
<script setup lang="ts">
import {
  h, inject, onMounted, Ref, ref,
} from 'vue';
import {
  DataStatusTag, isPower, OrionTable, BasicButton,
} from 'lyra-component-vue3';
import Api from '/@/api';
import dayjs from 'dayjs';
import { Cascader as ACascader, Modal, message } from 'ant-design-vue';
const selectRowKeys:Ref<string[]> = ref([]);
const formData:Record<string, any> = inject('formData', {});
const cascaderOptions:Ref<Record<string, any>[]> = ref([]);
const tableRef = ref();
const tableOptionsSource = {
  deleteToolButton: 'add|enable|disable|delete',
  showSmallSearch: false,
  showTableSetting: false,
  rowSelection: {},
  api: async (params) => {
    if (!formData.value.id) {
      return new Promise<any>((resolve) => resolve([]));
    }
    params.query = {
      projectId: formData.value.id,
    };
    return new Api('/pms').fetch(params, 'projectPlace/page', 'POST');
  },
  columns: [
    {
      title: '项目地点',
      dataIndex: 'areaId',
      slots: { customRender: 'areaId' },
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
      width: 100,
    },
    {
      title: '创建时间',
      ellipsis: true,
      dataIndex: 'createTime',
      width: 180,
      customRender({ text }) {
        return h('div', {
          title: text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '',
        }, text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '');
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 120,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '保存',
      isShow: (record) => record.isEdit,
      onClick(record: Record<string, any>) {
        let params = JSON.parse(JSON.stringify(record));
        params.projectId = formData.value.id;
        params.areaId = params.areaId[params.areaId.length - 1];
        delete params.id;
        delete params.isEdit;
        new Api('/pms').fetch(params, 'projectPlace/add', 'POST').then((res) => {
          message.success('保存成功');
          tableRef.value.reload({ page: 1 });
        });
      },
    },
    {
      text: '删除',
      isShow: (record) => !record.isEdit,
      onClick(record: Record<string, any>) {
        Modal.confirm({
          title: '删除提示',
          content: '是否删除当前的项目地点？',
          onOk() {
            new Api('/pms').fetch('', `projectPlace/${record.id}`, 'DELETE').then((res) => {
              message.success('删除成功');
              tableRef.value.reload({ page: 1 });
            });
          },
        });
      },
    },
  ],
};
function addNode() {
  let dataSource = tableRef.value.getDataSource();
  dataSource.push({
    areaId: [],
    id: `${new Date().getTime()}_addRows`,
    isEdit: true,
  });
  tableRef.value.setTableData(dataSource);
}
function selectionChange(selectData) {
  selectRowKeys.value = selectData.keys;
}
function deleteNode() {
  let dataSource = tableRef.value.getDataSource();

  Modal.confirm({
    title: '删除提示',
    content: '是否删除所选的项目地点？',
    onOk() {
      if (selectRowKeys.value.every((item) => item.indexOf('_addRows') >= 0)) {
        message.success('删除成功');
        tableRef.value.setTableData(dataSource.filter((item) => !selectRowKeys.value.includes(item.id)));
        selectRowKeys.value = [];
        return;
      }
      new Api('/pms').fetch(selectRowKeys.value, 'projectPlace/remove', 'DELETE').then((res) => {
        message.success('删除成功');
        tableRef.value.setTableData(dataSource.filter((item) => !selectRowKeys.value.includes(item.id)));
        selectRowKeys.value = [];
        // tableRef.value.reload({ page: 1 });
      });
    },
  });
}
onMounted(async () => {
  let res = await getAreaOptions({ type: 1 });
  cascaderOptions.value = res.map((item) => ({
    ...item,
    isLeaf: false,
  }));
});
async function loadData(selectedOptions) {
  const targetOption = selectedOptions[selectedOptions.length - 1];
  targetOption.loading = true;
  let res = await getAreaOptions({
    type: 2,
    paid: targetOption.areaId,
  });
  targetOption.loading = false;
  targetOption.children = res;
  cascaderOptions.value = [...cascaderOptions.value];
}
function getAreaOptions(params) {
  return new Api('/pmi').fetch(params, 'base-area', 'POST');
}
</script>
<style lang="less" scoped>
.locations{
  height: 400px;
  overflow: hidden;
  :deep(.ant-basic-table-wrap){
    padding-left: 0;
    padding-right: 0;
  }
  :deep(.orion-table-header-wrap){
    display: flex !important;
  }
}
</style>