<template>
  <BasicModal
    :width="'600px'"
    title="计划催办"
    zIndex="999"
    @register="modalRegister"
    @ok="handleDistributeOk"
  >
    <div class="dis-body">
      <div>
        <a-textarea
          v-model:value="remark"
          placeholder="请输入描述"
          :auto-size="{ minRows: 2, maxRows: 5 }"
        />
      </div>
      <div style="margin-top:24px">
        <!--  选择负责人-->
        <div
          style="margin-bottom: 10px"
        >
          知会人员
        </div>
        <div>
          <a-input :value="defaultSelectUserDataStr">
            <template
              #suffix
            >
              <Icon
                icon="sie-icon-tianjiaxinzeng"
                size="16"
                @click="showSelectUserModal"
              />
            </template>
          </a-input>
        </div>
      </div>
    </div>
    <SelectUserModal
      :on-ok="selectUserChange"
      @register="selectUserRegister"
    />
  </BasicModal>
</template>
<script lang="ts">
import { defineComponent, ref, reactive } from 'vue';
import {
  message,
  Textarea,
  Input,
} from 'ant-design-vue';
import {
  BasicModal, useModalInner, useModal, SelectUserModal, Icon,
} from 'lyra-component-vue3';
import Api from '/@/api';

export default defineComponent({
  name: 'PlanDone',
  components: {
    SelectUserModal,
    BasicModal,
    ATextarea: Textarea,
    AInput: Input,
    Icon,
  },
  emits: ['handleColse', 'updateForm'],
  setup(props, { emit }) {
    const remark = ref('');
    const notifyUserId = ref('');
    const defaultSelectUserData = ref([]);
    const defaultSelectUserDataStr = ref([]);
    const projectSchemeId = ref([]);
    const [selectUserRegister, { openModal: selectUserOpenModal }] = useModal();
    // const remark = ref('');
    const [modalRegister, { closeModal }] = useModalInner(
      (rowData) => {
        // debugger
        projectSchemeId.value = rowData.id;
      },
    );
    function selectUserChange(data) {
      defaultSelectUserData.value = data;
      if (defaultSelectUserData.value.length > 0) {
        defaultSelectUserDataStr.value = data.map((item) => item.name).join(',');
      } else {
        defaultSelectUserDataStr.value = '';
      }
    }
    function showSelectUserModal() {
      selectUserOpenModal(true, {
        defaultSelectUserData: defaultSelectUserData.value,
      });
    }

    // 计划下发确认
    const handleDistributeOk = () => {
      let notifyUserId = defaultSelectUserData.value.map((item) => item.id);
      const params = {
        notifyUserId,
        projectSchemeId: projectSchemeId.value,
        remark: remark.value,
      };
      new Api('/pms')
        .fetch(params, 'projectScheme/urgePlan', 'POST')
        .then((res) => {
          message.success('催办成功');
          emit('updateForm');
          closeModal();
          // emit('handleColse');
        });
    };

    return {
      modalRegister,
      handleDistributeOk,
      remark,
      selectUserChange,
      selectUserRegister,
      showSelectUserModal,
      notifyUserId,
      defaultSelectUserData,
      defaultSelectUserDataStr,
    };
  },
});
</script>
<style lang="less" scoped>
.dis-body {
  padding: 22px 22px 30px;

  .flex-box {
    display: flex;
    align-items: center;
    margin-top: 10px;

    > span {
      margin-right: 10px;
    }
  }
}
</style>
