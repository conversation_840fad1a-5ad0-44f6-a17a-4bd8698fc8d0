package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/07/15:13
 * @description:
 */
@Data
public class SimpleSqeDTO implements Serializable {

    /**
     * 金字塔类别
     */
    @ApiModelProperty(value = "金字塔类别")
    private String pyramidCategory;

    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    private String majorRepairTurn;

    /**
     * 是否考核
     */
    @ApiModelProperty(value = "是否考核")
    private Boolean isAssessed;

    /**
     * 考核级别
     */
    @ApiModelProperty(value = "考核级别")
    private String assessmentLevel;

    /**
     * 隐患编号
     */
    @ApiModelProperty(value = "隐患编号")
    private String hiddenDangerCode;

    /**
     * 隐患编号
     */
    @ApiModelProperty(value = "id")
    private String id;


    private String eventLocation;

    private String eventLocationCode;

    private String baseCode;

}
