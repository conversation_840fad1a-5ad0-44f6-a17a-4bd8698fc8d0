package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.tree.OrionTreeNode;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * DocumentModelLibraryDir Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-01 16:35:39
 */
@TableName(value = "pms_document_model_library_dir")
@ApiModel(value = "DocumentModelLibraryDirEntity对象", description = "文档模板库文件夹")
@Data

public class DocumentModelLibraryDir extends  OrionTreeNode  implements Serializable{
    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

}
