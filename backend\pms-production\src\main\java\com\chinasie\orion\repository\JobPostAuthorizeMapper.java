package com.chinasie.orion.repository;
import com.chinasie.orion.domain.dto.CountJobDTO;
import com.chinasie.orion.domain.dto.job.JobPersonNewcomerDTO;
import com.chinasie.orion.domain.dto.source.PersonSourceDTO;
import com.chinasie.orion.domain.entity.JobPostAuthorize;
import org.apache.ibatis.annotations.Mapper;

import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


/**
 * <p>
 * JobPostAuthorize Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-08 20:33:32
 */
@Mapper
public interface JobPostAuthorizeMapper extends  OrionBaseMapper  <JobPostAuthorize> {
    List<CountJobDTO> getJobPersonNumMap(@Param("personIdList") List<String> personIdList);

    List<PersonSourceDTO> getUserJobInfoList(@Param("repairRound") String repairRound
            ,@Param("keyword") String keyword ,@Param("year") int year);

    List<PersonSourceDTO> peronOverlapSourceList(@Param("repairRound") String repairRound
            ,@Param("keyword") String keyword ,@Param("year") int year,@Param("userCodeList") List<String> userCodeList);

    List<String> listByJobIdList(List<String> idList);

    List<JobPersonNewcomerDTO> listByPersonId(@Param("personId") String personId);

    List<String> listByJobIdAndNotPersonId(@Param("jobIdList")List<String> jobIdList, @Param("personId") String personId);
}

