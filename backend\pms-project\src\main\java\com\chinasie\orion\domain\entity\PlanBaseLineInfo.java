package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/16/15:51
 * @description:
 */
@Data
@TableName(value = "pms_plan_base_line_info")
@ApiModel(value = "BaseLineInfo对象", description = "基线信息表")
public class PlanBaseLineInfo extends ObjectEntity {

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @TableField(value = "version")
    private String version;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 版本key
     */
    @ApiModelProperty(value = "版本key")
    @TableField(value = "version_key")
    private String versionKey;
    @ApiModelProperty(value = "复制的计划数量")
    @TableField(value = "sum_number")
    private Long sumNumber;
}
