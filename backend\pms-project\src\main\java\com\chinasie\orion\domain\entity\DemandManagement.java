package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/18/14:57
 * @description:
 */
@Data
@TableName(value = "pms_demand_management")
@ApiModel(value = "DemandManagement对象", description = "需求管理")
public class DemandManagement extends ObjectEntity {
    /**
     * 负责人id
     */
    @ApiModelProperty(value = "负责人id")
    @TableField(value = "principal_id")
    private String principalId;

    /**
     * 负责人名称
     */
    @ApiModelProperty(value = "负责人名称")
    @TableField(value = "principal_name")
    private String principalName;

    /**
     * 接收人Id
     */
    @ApiModelProperty(value = "接收人Id")
    @TableField(value = "recipient_id")
    private String recipientId;

    /**
     * 接收人名称
     */
    @ApiModelProperty(value = "接收人名称")
    @TableField(value = "recipient_name")
    private String recipientName;

    /**
     * 需求来源
     */
    @ApiModelProperty(value = "需求来源")
    @TableField(value = "source")
    private String source;

    /**
     * 父级ID
     */
    @ApiModelProperty(value = "父级ID")
    @TableField(value = "parent_id")
    private String parentId;

    /**
     * 期望完成日期
     */
    @ApiModelProperty(value = "期望完成日期")
    @TableField(value = "predict_end_time")
    private Date predictEndTime;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    @TableField(value = "priority_level")
    private String priorityLevel;

    /**
     * 提出时间
     */
    @ApiModelProperty(value = "提出时间")
    @TableField(value = "proposed_time")
    private Date proposedTime;

    /**
     * 需求类型
     */
    @ApiModelProperty(value = "需求类型")
    @TableField(value = "type")
    private String type;

    /**
     * 提出人
     */
    @ApiModelProperty(value = "提出人")
    @TableField(value = "exhibitor")
    private String exhibitor;

    /**
     * 提出人名称
     */
    @ApiModelProperty(value = "提出人名称")
    @TableField(value = "exhibitor_name")
    private String exhibitorName;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 进度
     */
    @ApiModelProperty(value = "进度")
    @TableField(value = "schedule")
    private BigDecimal schedule;

    /**
     *  文档ID——document 壳
     */
    @ApiModelProperty(value = "文档ID")
    @TableField(value = "document_id")
    private String documentId;

}
