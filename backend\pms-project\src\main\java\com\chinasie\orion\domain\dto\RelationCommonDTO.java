package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/3/10 14:47
 */
@Data
@ApiModel(value = "RelationCommonDTO对象", description = "关联对象")
public class RelationCommonDTO implements Serializable {

    /**
     * id
     */
    @NotEmpty(message = "fromId不能为空")
    @ApiModelProperty(value = "fromId")
    private String fromId;

    /**
     * 计划id
     */
    @NotEmpty(message = "toIdList不能为空")
    @ApiModelProperty(value = "toIdList")
    private List<String> toIdList;
}
