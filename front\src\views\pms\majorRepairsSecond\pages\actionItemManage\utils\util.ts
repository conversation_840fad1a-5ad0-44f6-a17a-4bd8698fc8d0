import {
  h, reactive, ref, Ref,
} from 'vue';
import { openDrawer } from 'lyra-component-vue3';
import { get, hasIn, set } from 'lodash-es';

export function openFormDrawer(component: any, record?: Record<string, any>, cb?: () => void): void {
  const drawerRef: Ref = ref();
  const formData = reactive({ ...(record || {}) });
  openDrawer({
    title: record.title,
    width: 1000,
    ...setDefCfg(record, 'height'),
    content() {
      return h(component, {
        ref: drawerRef,
        formId: record?.id,
        formData,
        record,
      });
    },
    async onOk(): Promise<void> {
      const form = drawerRef.value;
      await form?.onSubmit?.();
      cb?.();
    },
  });
}
function setDefCfg(cfg:Record<string, any>, key:string) {
  const res = {};
  if (hasIn(cfg, key)) {
    set(res, key, get(cfg, key));
  }
  return res;
}