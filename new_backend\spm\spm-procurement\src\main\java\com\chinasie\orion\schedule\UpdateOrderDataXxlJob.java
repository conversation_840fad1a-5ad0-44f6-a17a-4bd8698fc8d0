package com.chinasie.orion.schedule;

import com.chinasie.orion.service.NcfFormPurchOrderCollectService;
import com.chinasie.orion.service.NcfFormPurchOrderDetailService;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: xch
 * @date: 2023/10/28 14:42
 * @description: 定时修改集采订单总表，明细表订单待支付字段
 */
@Component
public class UpdateOrderDataXxlJob {

    @Autowired
    private NcfFormPurchOrderCollectService ncfFormPurchOrderCollectService;
    @Autowired
    private NcfFormPurchOrderDetailService ncfFormPurchOrderDetailService;

    @XxlJob("UpdateOrderDataXxlJob")
    public void changeStatus() throws Exception {
        ncfFormPurchOrderCollectService.updateOrderPayDay();
        ncfFormPurchOrderDetailService.updateOrderPayDay();
    }
}
