import Api from '/@/api';

/**
 * 获取项目专员
 */
export const reviewManageUser = () => new Api('/spm/review/manageUser').fetch('', '', 'GET');
/**
 * 新增
 * @param params 参数
 */
export const add = (params) => new Api('/spm/review/add').fetch(params, '', 'POST');
/**
 * 编辑
 * @param params 参数
 */
export const edit = (params) => new Api('/spm/review/edit').fetch(params, '', 'PUT');
/**
 * 基础信息编辑
 * @param params 参数
 */
export const baseEdit = (params) => new Api('/spm/review/baseEdit').fetch(params, '', 'POST');
/**
 * 交付物信息查询
 * @param params 参数
 */
export const deliverableQuery = (id) => new Api(`/spm/review/deliverableQuery/${id}`).fetch('', '', 'GET');
/**
 * 交付物信息编辑
 * @param params 参数
 */
export const deliverableEdit = (params) => new Api('/spm/review/deliverableEdit').fetch(params, '', 'POST');
