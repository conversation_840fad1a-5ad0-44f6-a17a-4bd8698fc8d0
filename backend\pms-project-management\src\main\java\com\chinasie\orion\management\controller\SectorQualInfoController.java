package com.chinasie.orion.management.controller;


import com.chinasie.orion.management.domain.dto.SectorQualInfoDTO;
import com.chinasie.orion.management.domain.vo.SectorQualInfoVO;
import com.chinasie.orion.management.service.SectorQualInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * SectorQualInfo 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-26 10:59:54
 */
@RestController
@RequestMapping("/sectorQualInfo")
@Api(tags = "采购合同板块资审信息")
public class  SectorQualInfoController  {

    @Autowired
    private SectorQualInfoService sectorQualInfoService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "采购合同板块资审信息", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<SectorQualInfoVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        SectorQualInfoVO rsp = sectorQualInfoService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 列表
     *
     * @param supplierCode
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "列表")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "采购合同板块资审信息", subType = "列表查询", bizNo = "")
    @RequestMapping(value = "/list/{supplierCode}", method = RequestMethod.GET)
    public ResponseDTO<List<SectorQualInfoVO>> list(@PathVariable(value = "supplierCode") String supplierCode) throws Exception {
        List<SectorQualInfoVO> rsp =  sectorQualInfoService.list( supplierCode);
        return new ResponseDTO<>(rsp);
    }


}
