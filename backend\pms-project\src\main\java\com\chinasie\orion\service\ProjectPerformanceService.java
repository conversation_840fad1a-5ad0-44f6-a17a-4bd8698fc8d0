package com.chinasie.orion.service;


import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.ProjectPerformanceDTO;
import com.chinasie.orion.domain.entity.IndicatorScore;
import com.chinasie.orion.domain.entity.ProjectPerformance;
import com.chinasie.orion.domain.vo.PerformanceIndicatorVO;
import com.chinasie.orion.domain.vo.ProjectPerformanceVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

/**
 * <p>
 * ProjectPerformance 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-25 16:41:31
 */
public interface ProjectPerformanceService extends OrionBaseService<ProjectPerformance> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    ProjectPerformanceVO detail(String id) throws Exception;

    /**
     * 新增
     * <p>
     * * @param projectPerformanceDTO
     */
    ProjectPerformanceVO create(ProjectPerformanceDTO projectPerformanceDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param projectPerformanceDTO
     */
    Boolean edit(ProjectPerformanceDTO projectPerformanceDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ProjectPerformanceVO> pages(Page<ProjectPerformanceDTO> pageRequest, String projectId) throws Exception;

}
