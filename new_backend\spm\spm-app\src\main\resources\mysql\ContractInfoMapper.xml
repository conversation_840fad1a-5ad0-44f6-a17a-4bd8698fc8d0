<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.ContractInfoMapper">


    <insert id="insertBatch">
        insert into pmsx_email_record (id,type,data_id,type_content)
        values
        <foreach item="item" index="index" collection="emailRecordList" open="(" separator="),(" close=")">
           #{item.id},#{item.type}, #{item.dataId},#{item.typeContent}
        </foreach>
    </insert>


    <select id="getContractInfo" resultType="com.chinasie.orion.domain.entity.ContractExtendInfo">
        SELECT
            info.contract_name,
            info.contract_number,
            info.technical_rsp_user_id,
            info.org_id,
            info.platform_id,
            info.creator_id
        FROM
            ncf_form_contract_extend_info info
                LEFT JOIN ncf_form_actual_pay_milestone pay ON info.contract_number = pay.contract_number
        where pay.pay_type = '完工款' and pay.logic_status = 1 and is_acceptance_qualified = 0

    </select>


    <select id="getContractXxlJobDate" resultType="com.chinasie.orion.domain.vo.ContractXxlJobVO">
        SELECT i.id,
               i.estimated_end_time     AS estimatedEndTime,
               ei.business_rsp_user_id  AS rspBusinessUser,
               ei.technical_rsp_user_id AS rspTechUser,
               i.org_id                 AS orgId,
               i.warning_day            as warningDay,
               i.warning_money          as warningMoney,
               i.platform_id            AS platformId,
               i.warning_day            as warningDay,
               i.warning_money          as warningMoney
        FROM ncf_form_contract_info i
                 LEFT JOIN ncf_form_contract_extend_info ei ON i.contract_number = ei.contract_number
        WHERE type = '框架协议'
          AND i.logic_status = 1
          AND (
                i.warning_day = 102
                OR i.warning_day = 104
                OR i.warning_day = 106
                OR i.warning_day = 107
                OR i.warning_day = 108
                OR i.warning_day = 109
            )
          AND ei.contract_number is not null
    </select>



    <select id="getContractXxlJobAmount" resultType="com.chinasie.orion.domain.vo.ContractXxlJobVO">
        SELECT i.id,
               i.estimated_end_time     AS estimatedEndTime,
               ei.business_rsp_user_id  AS rspBusinessUser,
               ei.technical_rsp_user_id AS rspTechUser,
               i.org_id                 AS orgId,
               i.platform_id            AS platformId,
               i.warning_money          as warningMoney
        FROM ncf_form_contract_info i
                 LEFT JOIN ncf_form_contract_extend_info ei ON i.contract_number = ei.contract_number
        where i.logic_status = 1
          and (
                i.warning_money = 100
                OR i.warning_money = 102
                OR i.warning_money = 104
                OR i.warning_money = 106
            )
    </select>

    <select id="getEmailRecord" resultType="com.chinasie.orion.domain.entity.EmailRecord">
        SELECT
            id,
            type,
            data_id AS dataId,
            type_content AS typeContent
        FROM
            pmsx_email_record
    </select>

    <select id="getContractMainIds" resultType="java.lang.String">
        SELECT id
        FROM pmsx_contract_main
        WHERE contract_number IN
    <foreach item="item" collection="numberList" open="(" separator="," close=")">
        #{item}
    </foreach>
	AND YEAR ( `year` ) = #{year}
	AND logic_status = 1
    </select>

    <select id="getRequireInfo" resultType="java.lang.String">
        SELECT
        a.contract_number
        FROM
        (SELECT DISTINCT contract_number FROM ncf_form_require_info WHERE used_amt &gt;= total_amt AND contract_number IN
            <foreach item="item" collection="collect" open="(" separator="," close=")">
            #{item}
            </foreach>) a
        WHERE
        NOT EXISTS (
        SELECT 1 FROM ncf_form_require_info b
        WHERE b.contract_number = a.contract_number
        AND b.used_amt &lt; b.total_amt
        )
    </select>


</mapper>