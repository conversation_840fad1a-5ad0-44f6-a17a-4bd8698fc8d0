package com.chinasie.orion.domain.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * InvestmentScheme Entity对象
 *
 * <AUTHOR>
 * @since 2023-05-16 14:04:50
 */
@ApiModel(value = "InvestmentSchemeVO对象", description = "投资计划")
@Data
public class InvestmentSchemeVO extends ObjectVO implements Serializable {

    /**
     * 计划名称
     */
    @ApiModelProperty(value = "计划名称")
    private String name;

    /**
     * 计划编号
     */
    @ApiModelProperty(value = "计划编号")
    private String number;

    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    private String projectId;


    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;


    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;


    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    private String projectNumber;


    /**
     * 项目状态
     */
    @ApiModelProperty(value = "项目状态")
    private String projectStatusName;


    /**
     * 责任处室
     */
    @ApiModelProperty(value = "责任处室")
    private String rspDeptName;


    /**
     * 项目责任人
     */
    @ApiModelProperty(value = "项目责任人")
    private String rspUserName;


    /**
     * 总体预算
     */
    @ApiModelProperty(value = "总体预算")
    private String overallBudget;


    /**
     * 总体实际
     */
    @ApiModelProperty(value = "总体实际")
    private String overallReality;


    /**
     * 立项金额
     */
    @ApiModelProperty(value = "立项金额")
    private String projectAmount;


    /**
     * 合同金额
     */
    @ApiModelProperty(value = "合同金额")
    private String contractAmount;


    /**
     * 累计投资计划
     */
    @ApiModelProperty(value = "累计投资计划")
    private String totalInvestmentPlan;

    /**
     * 概算
     */
    @ApiModelProperty(value = "概算")
    private String estimate;

    /**
     * 累计完成投资计划
     */
    @ApiModelProperty(value = "累计完成投资计划")
    private String totalInvestmentCompletePlan;

    /**
     * 是否关闭
     */
    @ApiModelProperty(value = "是否关闭")
    private Boolean closeFlag;


    /**
     * 概算执行率
     */
    @ApiModelProperty(value = "概算执行率")
    private String estimatePercent;


    /**
     * 本年投资计划
     */
    @ApiModelProperty(value = "本年投资计划")
    private String currentYearInvest;


    /**
     * 本年完成投资计划
     */
    @ApiModelProperty(value = "本年完成投资计划")
    private String currentYearCompleteInvest;


    /**
     * 本年投资计划执行率
     */
    @ApiModelProperty(value = "本年投资计划执行率")
    private String currentYearCompleteInvestPercent;

}
