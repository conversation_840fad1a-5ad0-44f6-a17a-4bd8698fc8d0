package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;

/**
 * InvoiceInformation Entity对象
 *
 * <AUTHOR>
 * @since 2025-01-07 02:41:49
 */
@TableName(value = "pmsx_invoice_information")
@ApiModel(value = "InvoiceInformationEntity对象", description = "发票信息")
@Data

public class InvoiceInformation extends  ObjectEntity  implements Serializable{

    /**
     * 收入计划编号
     */
    @ApiModelProperty(value = "收入计划编号")
    @TableField(value = "income_plan_num")
    private String incomePlanNum;

    /**
     * 关联合同id
     */
    @ApiModelProperty(value = "关联合同id")
    @TableField(value = "contract_id")
    private String contractId;

    /**
     * 里程碑id
     */
    @ApiModelProperty(value = "里程碑id")
    @TableField(value = "milestone_id")
    private String milestoneId;

    /**
     * 里程碑名称
     */
    @ApiModelProperty(value = "里程碑名称")
    @TableField(value = "milestone_name")
    private String milestoneName;

    /**
     * 发票类型
     */
    @ApiModelProperty(value = "发票类型")
    @TableField(value = "invoice_type")
    private String invoiceType;

    /**
     * 发票号码
     */
    @ApiModelProperty(value = "发票号码")
    @TableField(value = "invoice_num")
    private String InvoiceNum;

    /**
     * 开票公司
     */
    @ApiModelProperty(value = "开票公司")
    @TableField(value = "bill_issue_company")
    private String billIssueCompany;

    /**
     * 发票购买方
     */
    @ApiModelProperty(value = "发票购买方")
    @TableField(value = "invoice_purchaser")
    private String invoicePurchaser;

    /**
     * 开票日期
     */
    @ApiModelProperty(value = "开票日期")
    @TableField(value = "invoice_date")
    private Date invoiceDate;

    /**
     * 不含税金额
     */
    @ApiModelProperty(value = "不含税金额")
    @TableField(value = "amt_no_tax")
    private BigDecimal amtNoTax;

    /**
     * 税额
     */
    @ApiModelProperty(value = "税额")
    @TableField(value = "tax")
    private BigDecimal tax;

    /**
     * 含税金额
     */
    @ApiModelProperty(value = "含税金额")
    @TableField(value = "amt_tax")
    private BigDecimal amtTax;

}
