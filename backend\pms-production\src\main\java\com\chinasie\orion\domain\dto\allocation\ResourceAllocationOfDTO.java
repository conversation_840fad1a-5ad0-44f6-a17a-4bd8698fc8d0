package com.chinasie.orion.domain.dto.allocation;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ResourceAllocationOfDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String key;
    private String id;
    private String number;
    private String costCenterCode;
    private String name;
    private Integer repairRound;

    private Integer overlap;
    private Integer totalDays;

    private String overDays;
    private String dataType;


    @ApiModelProperty(value = "开始时间")
    private String realStartDate;

    @ApiModelProperty(value = "结束时间")
    private String realEndDate;

    private List<String> overLapDays;

    private List<SectionTime> sectionTimes;

    private List<ResourceAllocationOfDTO> children;
}
