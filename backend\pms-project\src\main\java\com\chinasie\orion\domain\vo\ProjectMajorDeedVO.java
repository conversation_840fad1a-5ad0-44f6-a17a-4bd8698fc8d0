package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.lang.String;
import java.util.List;

/**
 * ProjectMajorDeed VO对象
 *
 * <AUTHOR>
 * @since 2024-05-27 19:32:36
 */
@ApiModel(value = "ProjectMajorDeedVO对象", description = "项目主要事迹")
@Data
public class ProjectMajorDeedVO extends  ObjectVO   implements Serializable{

        @ApiModelProperty(value = "起止时间")
        private List<String> beginEndTime = new ArrayList<>();

        /**
         * 主要事迹
         */
        @ApiModelProperty(value = "主要事迹")
        private String majorDeed;


        /**
         * 项目id
         */
        @ApiModelProperty(value = "项目id")
        private String projectId;


    

}
