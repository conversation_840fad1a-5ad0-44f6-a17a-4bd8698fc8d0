package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;

/**
 * ContractAssessmentStandard Entity对象
 *
 * <AUTHOR>
 * @since 2024-10-17 09:29:14
 */
@TableName(value = "pmsx_contract_assessment_standard")
@ApiModel(value = "ContractAssessmentStandardEntity对象", description = "审核标准表")
@Data

public class ContractAssessmentStandard extends  ObjectEntity  implements Serializable{

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_number")
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @TableField(value = "contract_name")
    private String contractName;

    /**
     * 审核类别
     */
    @ApiModelProperty(value = "审核类别")
    @TableField(value = "assessment_type")
    private String assessmentType;

    /**
     * 考核内容
     */
    @ApiModelProperty(value = "考核内容")
    @TableField(value = "assessment_content")
    private String assessmentContent;

    /**
     * 考核标准
     */
    @ApiModelProperty(value = "考核标准")
    @TableField(value = "standard")
    private String standard;

}
