package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.TaskDecompositionDTO;
import com.chinasie.orion.domain.vo.SimpleVo;
import com.chinasie.orion.domain.vo.TaskDecompositionVO;
import com.chinasie.orion.service.TaskDecompositionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;

/**
 * <p>
 * TaskDecomposition 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-01 17:00:34
 */
@RestController
@RequestMapping("/taskDecomposition")
@Api(tags = "任务分解")
public class  TaskDecompositionController  {

    @Autowired
    private TaskDecompositionService taskDecompositionService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "任务分解", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<TaskDecompositionVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        TaskDecompositionVO rsp = taskDecompositionService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param taskDecompositionDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#taskDecompositionDTO.name}}】", type = "任务分解", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody TaskDecompositionDTO taskDecompositionDTO) throws Exception {
        String rsp =  taskDecompositionService.create(taskDecompositionDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param taskDecompositionDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#taskDecompositionDTO.name}}】", type = "任务分解", subType = "编辑", bizNo = "{{#taskDecompositionDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  TaskDecompositionDTO taskDecompositionDTO) throws Exception {
        Boolean rsp = taskDecompositionService.edit(taskDecompositionDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "任务分解", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = taskDecompositionService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "任务分解", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = taskDecompositionService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "任务分解", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page/{mainTableId}", method = RequestMethod.POST)
    public ResponseDTO<Page<TaskDecompositionVO>> pages(@PathVariable("mainTableId") String mainTableId,@RequestBody Page<TaskDecompositionDTO> pageRequest) throws Exception {
        Page<TaskDecompositionVO> rsp =  taskDecompositionService.pages(mainTableId, pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 列表
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "列表")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "任务分解", subType = "列表查询", bizNo = "")
    @RequestMapping(value = "/list/{mainTableId}", method = RequestMethod.POST)
    public ResponseDTO<List<SimpleVo>> list(@PathVariable("mainTableId") String mainTableId) throws Exception {
        List<SimpleVo> rsp =  taskDecompositionService.list(mainTableId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 列表树
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "列表树")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "任务分解", subType = "列表查询", bizNo = "")
    @RequestMapping(value = "/list/tree/{mainTableId}", method = RequestMethod.POST)
    public ResponseDTO<List<TaskDecompositionVO>> listTree(@PathVariable("mainTableId") String mainTableId) throws Exception {
        List<TaskDecompositionVO> rsp =  taskDecompositionService.listTree(mainTableId);
        return new ResponseDTO<>(rsp);
    }

}
