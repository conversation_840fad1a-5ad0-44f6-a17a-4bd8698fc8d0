package com.chinasie.orion.domain.dto.prodAction;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/10/29/15:08
 * @description:
 */
@Data
public class ProdActionTreeParamDTO implements Serializable {

    @ApiModelProperty("关键词")
    private String keyword;


    @ApiModelProperty("所属大修伦次")
    @NotEmpty(message = "大修伦次不能为空")
    private String repairRound;
}
