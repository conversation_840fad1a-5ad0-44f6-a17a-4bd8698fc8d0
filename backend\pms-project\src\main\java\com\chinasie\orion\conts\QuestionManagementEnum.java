package com.chinasie.orion.conts;

public enum QuestionManagementEnum {
    CREATE(101,"未完成"),
    UNDER_WAY(110, "流程中"),
    COMPLETED(130, "已完成"),
    CLOSE(160, "已关闭");
    private Integer code;

    private String description;

    QuestionManagementEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
