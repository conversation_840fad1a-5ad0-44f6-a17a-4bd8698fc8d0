package com.chinasie.orion.constant;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class RequirementNodeDict {

    /**
     * 商城子订单分发确认
     */

    public static final String NODE_SUB_ORDER_DISTRIBUTE = "NODE_SUB_ORDER_DISTRIBUTE";

    /**
     * 需求分发确认提醒
     */
    public static final String NODE_REQUIREMENT_DISTRIBUTE = "NODE_REQUIREMENT_DISTRIBUTE";

    //市场经营需求分发确认
    public static final String NODE_REQUIREMENT_CONFIRM = "NODE_REQUIREMENT_CONFIRM";

    /**
     * 报价临期截止提醒
     */
    public static final String NODE_QUTOTATION_DEADLINE = "NODE_QUTOTATION_DEADLINE";

    /**
     * 商城子订单分发
     */
    public static final String MARKET_SUB_ORDER_DISTRIBUTE = "MARKET_SUB_ORDER_DISTRIBUTE";

    /**
     * 需求分发反馈
     */
    public static final String NODE_REQUIREMENT_FEEDBACK = "NODE_REQUIREMENT_FEEDBACK";

}
