package com.chinasie.orion.handler.status;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.ChangeStatusMessageDTO;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.amqp.handler.AbstractChangeStatusReceiver;
import com.chinasie.orion.bo.PmsMQProducer;
import com.chinasie.orion.constant.IdeaFormStatusEnum;
import com.chinasie.orion.constant.InterfaceManagementStatusEnum;
import com.chinasie.orion.conts.InterfaceNodeTypeEnum;
import com.chinasie.orion.conts.MsgBusinessTypeEnum;
import com.chinasie.orion.domain.dto.SchemeMsgDTO;
import com.chinasie.orion.domain.entity.IdeaForm;
import com.chinasie.orion.manager.SendMessageManager;
import com.chinasie.orion.sdk.domain.vo.business.ClassVO;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.util.IdUtils;
import com.chinasie.orion.service.IdeaFormService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Objects;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/02/01/22:32
 * @description:
 */
@Slf4j
@Component
public class IdeaFormStatusUpdateService extends AbstractChangeStatusReceiver {
    private final Logger logger = LoggerFactory.getLogger(AcceptanceFormStatusReceiver.class);

    private static final String CURRENT_CLASS = "IdeaForm";
    private ClassRedisHelper classRedisHelper;

    private IdeaFormService ideaFormService;

    @Resource
    protected PmsMQProducer mqProducer;

    @Autowired
    public void setIdeaFormService(IdeaFormService ideaFormService) {
        this.ideaFormService = ideaFormService;
    }
    @Autowired
    public void setClassRedisHelper(ClassRedisHelper classRedisHelper) {
        this.classRedisHelper = classRedisHelper;
    }

    private final static String IM_URL = "/pms/oIcmManagementDetailsIndex/%s";

    @Override
    protected void process(ChangeStatusMessageDTO msg, Channel channel, Message message) {
        logger.info("接口--意见单状态申报状态更改消息消费：{}", msg);
        if (ObjectUtil.isNotEmpty(msg)) {
            ClassVO classVO = classRedisHelper.classInfo(IdUtils.getCode(msg.getBusinessId()));
            if (Objects.nonNull(classVO)) {
                if (CURRENT_CLASS.equals(classVO.getClassName())) {
                    msg.setClassName(classVO.getClassName());
                    ThreadUtil.execAsync(() -> {
                        try {
                            consumerCreateMessage(msg);
                        } catch (Exception e) {
                            processError(msg, channel, message, e);
                        }
                    });
                }
            }
        }
    }

    @Override
    protected void processError(ChangeStatusMessageDTO msg, Channel channel, Message message, Exception e) {
        logger.error("接口--意见单状态更改消息消费异常，【{}】,message，【{}】,", JSONUtil.toJsonStr(msg), message, e);
    }

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = CURRENT_CLASS, durable = "true", autoDelete = "false"),
            exchange = @Exchange(value = "${orion.amqp.change-status-v2.exchange}", type = ExchangeTypes.DIRECT),
            key = CURRENT_CLASS
    ))
    @Override
    public void receiver(ChangeStatusMessageDTO msg, Channel channel, Message message) throws IOException {
        super.receiver(msg, channel, message);
    }


    private void consumerCreateMessage(ChangeStatusMessageDTO message) throws Exception {
        Integer status = message.getStatus();
        String businessId = message.getBusinessId();
        IdeaForm ideaForm = ideaFormService.getById(businessId);
        ideaForm.setStatus(status);
        if (IdeaFormStatusEnum.IF_STATUS_COMPLETE.getStatus().equals(status)){
            ideaForm.setReplyTime(new Date());
        }
        ideaFormService.updateById(ideaForm);
        logger.info("接口-意见单状态变更成功：{}", JSONUtil.toJsonStr(ideaForm));
        // 发送消息
        String manUser = ideaForm.getManUser();


        if(Objects.equals(status, IdeaFormStatusEnum.IF_STATUS_COMPLETE.getStatus())){
            SendMessageDTO sendMsc = SendMessageDTO.builder()
                    .businessData(JSON.toJSONString(MapUtil.builder().put("flowType", "意见单审批完成").build()))
                    .businessId(businessId)
                    .todoStatus(0)
                    .todoType(TodoTypeDict.TODO_TYPE_INTERFACE)
                    .urgencyLevel(0)
                    .messageMap(MapUtil.builder(new HashMap<String, Object>())
                            .put("$name$", ideaForm.getDesc())
                            .build())
                    .businessNodeCode(InterfaceNodeTypeEnum.NODE_INTERFACE_IDEA.getCode())
                    .titleMap(MapUtil.builder(new HashMap<String, Object>())
                            .put("$name$", ideaForm.getDesc())
                            .build())
                    .messageUrl(String.format(IM_URL, businessId))
                    .messageUrlName("意见单详情")
                    .recipientIdList(ListUtil.toList(manUser))
                    .senderTime(new Date())
                    .senderId(ideaForm.getManUser())
                    .platformId(ideaForm.getPlatformId())
                    .orgId(ideaForm.getOrgId())
                    .build();
            mqProducer.sendPmsMessage(sendMsc);
        }
    }
}
