package com.chinasie.orion.domain.vo;


import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * ProjectContractChangeApply Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-25 10:55:50
 */
@ApiModel(value = "ProjectContractChangeApplyVO对象", description = "项目合同变更申请信息")
@Data
public class ProjectContractChangeApplyVO extends ObjectVO implements Serializable {

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    private String contractId;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;

    /**
     * 变更申请单号
     */
    @ApiModelProperty(value = "变更申请单号")
    private String number;

    /**
     * 申请人id
     */
    @ApiModelProperty(value = "申请人id")
    private String applyUserId;

    /**
     * 申请人id
     */
    @ApiModelProperty(value = "申请人名称")
    private String applyUserName;


    /**
     * 申请日期
     */
    @ApiModelProperty(value = "申请日期")
    private Date applyDate;

    /**
     * 变更原因
     */
    @ApiModelProperty(value = "变更原因")
    private String changeReason;

    /**
     * 合同基本信息是否变更
     */
    @ApiModelProperty(value = "合同基本信息是否变更")
    private Boolean isContractInfo;

    /**
     * 甲方签约主体是否变更
     */
    @ApiModelProperty(value = "甲方签约主体是否变更")
    private Boolean isOurSignedMain;

    /**
     * 乙方签约主体是否变更
     */
    @ApiModelProperty(value = "乙方签约主体是否变更")
    private Boolean isSupplierSignedMain;

    /**
     * 合同节点是否变更
     */
    @ApiModelProperty(value = "合同节点是否变更")
    private Boolean isPayNode;

    /**
     * 合同附件是否变更
     */
    @ApiModelProperty(value = "合同附件是否变更")
    private Boolean isContractFile;

    /**
     * 父变更id
     */
    @ApiModelProperty(value = "父变更id")
    private String parentId;

}
