<template>
  <BasicDrawer
    :width="1000"
    wrap-class-name="modalDetails checkDrawer"
    title="查看基本信息"
    @register="modalRegister"
    @visible-change="visibleChange"
  >
    <div
      v-loading="loading"
      class="modalDetails_main"
    >
      <div class="modalDetails_content">
        <div class="viewTitle">
          <div class="rowItem titleLabel">
            {{ formData.name }}
          </div>
          <div class="rowItem">
            <div class="rowCell">
              <div class="rowCell_icon icon_user">
                <i class="orion-icon-user" />
              </div>
              <div class="rowCell_val">
                <div class="val_top">
                  {{ formData.creatorName }}
                </div>
                <div class="val_bot">
                  创建人
                </div>
              </div>
            </div>
            <div class="rowCell">
              <div class="rowCell_icon icon_calendar">
                <i class="orion-icon-hourglass" />
              </div>
              <div class="rowCell_val">
                <div class="val_top">
                  <DataStatusTag :status-data="formData.dataStatus" />
                </div>
                <div class="val_bot">
                  状态
                </div>
              </div>
            </div>

            <div class="rowCell">
              <div class="rowCell_icon icon_calendar">
                <i class="orion-icon-calendar" />
              </div>
              <div class="rowCell_val">
                <div class="val_top">
                  {{ stampDate(formData.createTime) }}
                </div>
                <div class="val_bot">
                  创建时间
                </div>
              </div>
            </div>
            <div class="rowCell">
              <div class="rowCell_icon icon_calendar">
                <i class="orion-icon-calendar" />
              </div>
              <div class="rowCell_val">
                <div class="val_top">
                  {{ stampDate(formData.modifyTime) }}
                </div>
                <div class="val_bot">
                  修改时间
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <BasicTabs
        v-model:tabsIndex="tabsIndex"
        :tabs="tabs"
        @tabsChange="tabsChange"
      />
      <div
        class="modalDetailsPage"
      >
        <ProjectOverview v-if="actionId===1111111" />
        <!--  计划管理  -->
        <PlanManagement
          v-if="actionId===1111112"
          pageType="modal"
        />
        <BusinessDemand
          v-if="actionId===1111113 "
          modelName="pms"
          pageType="modal"
        />
        <BusinessQuestion
          v-if="actionId===1111114 "
          modelName="pms"
          pageType="modal"
        />
        <!-- 风险管理 -->
        <RiskManagement
          v-if="actionId===1111115"
          :id="formId"
          pageType="modal"
        />
        <!--    变更管理-->
        <ChangeApply
          v-if="actionId===11111114"
          :formId="formId"
          ecrDirName="项目"
          :showBtn="false"
          pageType="modal"
        />
        <!-- 资源管理 -->
        <SourceManagement
          v-if="actionId===1111119"
          :id="formId"
          pageType="modal"
        />
        <!-- 文档管理 -->
        <DocManagement
          v-if="actionId===11111110"
          :id="formId"
          pageType="modal"
        />
        <!-- 结项管理 -->
        <EndManagement
          v-if="actionId===11111111"
          :id="formId"
          pageType="modal"
        />
        <!-- 统计分析 -->
        <StaticsticManagement
          v-if="actionId===11111112"
          :id="formId"
          pageType="modal"
        />
        <!-- 项目设置 -->
        <ProjectSet
          v-if="actionId===11111113"
          :id="formId"
          pageType="modal"
        />
      </div>
    </div>
  </BasicDrawer>
</template>
<script lang="ts">
import {
  defineComponent, onMounted, reactive, toRefs, computed, nextTick, provide, ref, readonly,
} from 'vue';
import {
  BasicDrawer, useDrawerInner, BasicTabs, DataStatusTag,
} from 'lyra-component-vue3';
import { stampDate } from '/@/utils/dateUtil';
import Api from '/@/api';
import ProjectOverview from '../menuComponents/projectOverview/index.vue';
import PlanManagement from '../menuComponents/planManagement/index.vue';
import { ChangeApply } from '../menuComponents/ChangeApply';
import { BusinessQuestion } from '../components/BusinessQuestion';
import { BusinessDemand } from '../components/BusinessDemand';
import RiskManagement from '../menuComponents/riskManagement/index.vue';
import SourceManagement from '../menuComponents/sourceManagement/index.vue';
import DocManagement from '../menuComponents/docManagement/index.vue';
import EndManagement from '../menuComponents/endManagement/index.vue';
import StaticsticManagement from '/src/views/pms/projectLaborer/projectLab/projectList/menuComponents/staticsticManagement/index.vue';
import ProjectSet from '../menuComponents/projectSet/index.vue';
export default defineComponent({
  name: 'ModalDetails',
  components: {
    BasicDrawer,
    BasicTabs,
    DataStatusTag,
    ProjectOverview,
    PlanManagement,
    BusinessDemand,
    ChangeApply,
    BusinessQuestion,
    RiskManagement,
    SourceManagement,
    DocManagement,
    EndManagement,
    StaticsticManagement,
    ProjectSet,
  },
  setup(props, { emit }) {
    const state:any = reactive({
      formData: {},
      formId: '',
      showTabs: true,
      loading: false,
      tabsIndex: 0,
      actionId: '',
      provideProjectId: '',
      tabs: [
        {
          name: '项目概况',
          id: 1111111,
        },
        {
          name: '计划管理',
          id: 1111112,
        },
        {
          name: '需求管理',
          id: 1111113,
        },
        {
          name: '问题管理',
          id: 1111114,
        },
        {
          name: '风险管理',
          id: 1111115,
        },
        {
          name: '变更管理',
          id: 11111114,
        },
        {
          name: '资源管理',
          id: 1111119,
        },
        {
          name: '文档管理',
          id: 11111110,
        },
        {
          name: '结项管理',
          id: 11111111,
        },
        {
          name: '统计分析',
          id: 11111112,
        },
        {
          name: '项目设置',
          id: 11111113,
        },
      ],
    });
    const [modalRegister, { closeDrawer, setDrawerProps, changeLoading }] = useDrawerInner((drawerData) => {
      state.formId = drawerData.id;
      getFormData(drawerData.id);
      state.provideProjectId = ref(state.formId);
    });

    // provide('provideProjectId', readonly(provideProjectId));
    function getFormData(id) {
      new Api(`/pms/project-overview/info?projectId=${id}`).fetch('', '', 'GET').then((data) => {
        state.formData = data;
        nextTick(() => {
          state.tabsIndex = 0;
          state.actionId = state.tabs[0].id;
        });
      });
    }
    const tabsChange = (index, item) => {
      state.actionId = item.id;
      // console.log(state.actionId);
    };
    // 数据
    provide(
      'formData',
      computed(() => state.formData),
    );
    // 数据
    provide(
      'provideProjectId',
      computed(() => readonly(state.provideProjectId)),
    );
    onMounted(() => {
    });

    const visibleChange = (val) => {
      if (!val) {
        state.actionId = '';
      }
      // 关闭之前清除插入的字段
      // removeSchemaByFiled
    };

    return {
      ...toRefs(state),
      modalRegister,
      stampDate,
      tabsChange,
      visibleChange,
    };
  },
});

</script>
<style lang="less" scoped>
.modalDetails{
  :deep(.ant-drawer-body){
    padding:0px;
    .scrollbar__view{
      height: 100%;
    }
  }
  .modalDetails_main{
    display: flex;
    height: 100%;
    flex-direction: column;
  }
  .modalDetails_content{
    padding: 10px;
    .fa {
      font-family: 'FontAwesome';
    }
    .viewTitle {
      * {
        font-family: 'MicrosoftYaHei-Bold', '微软雅黑 Bold', '微软雅黑';
      }
      padding-bottom: 20px;
      border-bottom: 1px dashed #e4e4e7;
      .titleLabel {
        font-weight: 700;
        font-style: normal;
        font-size: 20px;
        color: #000000;
        height: 60px;
        line-height: 60px;
      }
      .rowItem {
        display: flex;
        .rowCell {
          display: flex;
          width: 250px;
          .rowCell_icon {
            height: 40px;
            width: 40px;
            line-height: 40px;
            border-radius: 20px;
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            margin-right: 5px;
          }
          .icon_user {
            background: ~`getPrefixVar('primary-color')`;
            color: #ffffff;
          }
          .icon_calendar {
            background: ~`getPrefixVar('primary-color-deprecated-f-12')`;
            color: ~`getPrefixVar('primary-color')`;
          }
          .rowCell_val {
            .val_top {
              font-weight: 500;
              font-style: normal;
              font-size: 16px;
              height: 25px;
            }
            .val_bot {
              font-weight: 400;
              font-style: normal;
              font-size: 12px;
              color: #686f8b;
            }
          }
        }
      }
    }
  }
  :deep(.tabs-main){
    padding: 0px 10px;
  }
  .modalDetailsPage{
    flex: 1;
    overflow: auto;
    :deep(.information){
      overflow: auto !important;
    }
  }
}

</style>
