<template>
  <Layout
    v-loading="loading"
    class="ui-2-0"
  >
    <View
      v-if="showDetails"
      :data="view"
    />
  </Layout>
  <NewButtonModal
    v-if="pageType==='page'"
    :btn-object-data="btnConfig"
    @clickType="clickType"
  />
  <!-- 新增/编辑 -->
  <AddPlanNode
    v-if="pageType==='page'"
    type="details"
    @register="register"
    @update="updateData"
  />
</template>

<script>
import { message } from 'ant-design-vue';
import NewButtonModal from '/@/views/pms/projectLaborer/componentsList/buttonModal/newButtonModal.vue';
import {
  onMounted, reactive, toRefs, ref, inject, computed, watch,
} from 'vue';
import Api from '/@/api';
import {
  Layout, isPower, useDrawer,
} from 'lyra-component-vue3';
import View from './View.vue';
import AddPlanNode from '../../projectsPlan/components/AddPlanNode.vue';

export default {
  name: 'Summarize',
  components: {
    Layout,
    NewButtonModal,
    View,
    AddPlanNode,
  },
  props: {
    pageType: {
      type: String,
      default: 'page',
    },
    formId: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const formData = inject('formData', {});
    const getFormData = inject('getFormData');
    const state = reactive({
      powerData: [],
      loading: false,
      editRef: ref(),
      view: { form: formData.value },
      edit: { form: {} },
      showDetails: true,
      btnConfig: {
        edit: { show: computed(() => isPower('RWX_container_button_02', state.powerData)) },
        determine: { show: false },
        cancel: { show: false },
      },
    });
    watch(
      () => formData?.value,
      (val) => {
        state.view.form = val;
      },
    );
    const [register, { openDrawer }] = useDrawer();
    state.powerData = inject('powerData');

    function clickType(type) {
      new Api('/pms').fetch('', `plan/${state.view.form.id}`, 'GET').then((res) => {
        openDrawer(true, {
          type: 'edit',
          data: res,
        });
      });
    }
    function updateData() {
      getFormData.value();
    }

    onMounted(() => {
    });
    return {
      ...toRefs(state),
      clickType,
      register,
      updateData,
    };
  },
};
</script>

<style scoped lang="less">
  .ui-2-0 {
    width: calc(100% - 60px);
    flex: 1;
  }
</style>
