package com.chinasie.orion.service;


import java.lang.String;
import java.util.List;


import com.chinasie.orion.domain.dto.ProjectPayActualDTO;
import com.chinasie.orion.domain.entity.ProjectPayActual;
import com.chinasie.orion.domain.vo.ProjectPayActualVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import java.lang.String;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * ProjectPayActual 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04 00:48:18
 */
public interface ProjectPayActualService  extends  OrionBaseService<ProjectPayActual>  {


        /**
         *  详情
         *
         * * @param id
         */
    ProjectPayActualVO detail(String id, String pageCode)throws Exception;

        /**
         *  新增
         *
         * * @param projectPayActualDTO
         */
        String create(ProjectPayActualDTO projectPayActualDTO)throws Exception;

        /**
         *  编辑
         *
         * * @param projectPayActualDTO
         */
        Boolean edit(ProjectPayActualDTO projectPayActualDTO)throws Exception;


        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;


        /**
         *  分页
         *
         * * @param pageRequest
         * 
         */
        Page<ProjectPayActualVO> pages( Page<ProjectPayActualDTO> pageRequest)throws Exception;

        /**
         *  下载模板
         *
         * * @param response
         */
        void downloadExcelTpl(HttpServletResponse response)throws Exception;

        /**
         *  导入校验
         *
         * * @param file
         */
        ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


        /**
           *  确认导入
           *
           * * @param importId
           */
        Boolean importByExcel(String importId)throws Exception;

        /**
       *  取消导入
       *
       * * @param importId
       */
        Boolean importCancelByExcel(String importId)throws Exception;

        /**
            *  导出
            *
            * * @param searchConditions
            * * @param response
            */
        void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

        /**
            *  设置各种名称
            *
            * * @param searchConditions
            * * @param response
            */
        void  setEveryName(List<ProjectPayActualVO> vos)throws Exception;
}
