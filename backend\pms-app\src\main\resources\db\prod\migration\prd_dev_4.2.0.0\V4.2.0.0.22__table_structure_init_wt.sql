ALTER TABLE `pms_contract_milestone` ADD COLUMN `is_plan` int(1) default NULL COMMENT '是否创建计划数据 0：否 1：是';
INSERT INTO `xxl_job_info`( `job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (5, '合同里程碑生成项目计划', '2024-08-19 20:36:11', '2024-08-19 20:37:09', 'orion', '', 'CRON', '0 0 01 * * ?', 'DO_NOTHING', 'FIRST', 'projectRiskRelationPlan', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-08-19 20:36:11', '', 1, 0, 1724086800000);
ALTER TABLE `pms_project_scheme` ADD COLUMN `contract_milestone_id` varchar(64) default NULL COMMENT '合同里程碑id';