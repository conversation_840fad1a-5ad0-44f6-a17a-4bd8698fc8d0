package com.chinasie.orion.domain.vo.projectOverviewZgh;


import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 项目全生命周期监控
 */
@Data
@ApiModel(value = "ProjectLifeVO", description = "项目全生命周期监控")
public class ProjectLifeVO {

    /**
     * {
     * id: '4',
     * nodeKey: 'TOLD_ACTION',
     * name: '履约执行',
     * actions: [
     * {
     * id: '3-1',
     * name: '计划排程',
     * isOver: true,
     * isNot: false,
     * isFinish: false,
     * actions: [
     * {
     * id: '3-1-1',
     * name: '',
     * },
     * ],
     * },
     * {
     * id: '9',
     * nodeKey: 'CELL-SERVICE',
     * name: '',
     * x: 1320,
     * y: 200,
     * nodeType: 'SELF_DEFINE_NODE2',
     * nodeState: 'FINISHED',
     * isOver: true,
     * isNot: false,
     * isFinish: false,
     * },
     */

    public static final String NODE_TPL = "[{\"id\":\"0\",\"isOver\":true,\"nodeKey\":\"START\",\"nodeState\":\"START\",\"nodeType\":\"START_NODE\",\"shape\":\"custom-vue-start-node\",\"x\":0,\"y\":90,\"label\":\"开始\",\"parentId\":0},{\"id\":\"1\",\"nodeKey\":\"CLUED_NEEDS\",\"name\":\"线索与需求\",\"actions\":[{\"id\":\"0-1\",\"name\":\"市场需求\",\"isOver\":false,\"isNot\":true,\"isFinish\":true,\"x\":113,\"y\":180},{\"id\":\"0-2\",\"name\":\"综合线索\",\"isOver\":false,\"isNot\":true,\"isFinish\":false,\"x\":113,\"y\":240},{\"id\":\"0-3\",\"name\":\"科研需求\",\"isOver\":false,\"isNot\":true,\"isFinish\":false,\"x\":113,\"y\":300}],\"x\":100,\"y\":100,\"nodeType\":\"NORMAL_NODE\",\"nodeState\":\"FINISHED\",\"isFinish\":true,\"parentId\":1},{\"id\":\"2\",\"nodeKey\":\"ESTABLISHMENT_STAGE\",\"name\":\"收入/销售管理\",\"actions\":[{\"id\":\"1-1\",\"name\":\"需求响应\",\"isOver\":false,\"isNot\":true,\"isFinish\":false,\"x\":333,\"y\":180},{\"id\":\"1-2\",\"name\":\"项目报价\",\"isOver\":false,\"isNot\":true,\"isFinish\":false,\"x\":333,\"y\":230},{\"id\":\"1-3\",\"name\":\"项目投标\",\"isOver\":false,\"isNot\":true,\"isFinish\":false,\"x\":333,\"y\":280},{\"id\":\"1-4\",\"name\":\"市场签署合同\",\"isOver\":false,\"isNot\":true,\"isFinish\":false,\"x\":333,\"y\":330},{\"id\":\"1-5\",\"name\":\"项目立项\",\"isOver\":false,\"isNot\":true,\"isFinish\":false,\"x\":333,\"y\":413},{\"id\":\"1-6\",\"name\":\"项目预立项\",\"isOver\":false,\"isNot\":true,\"isFinish\":false,\"x\":333,\"y\":460}],\"x\":320,\"y\":100,\"nodeType\":\"NORMAL_NODE\",\"nodeState\":\"NOT_START\",\"parentId\":2},{\"id\":\"3\",\"nodeKey\":\"PROCUREMENT_MANAGEMENT\",\"name\":\"采购管理\",\"actions\":[{\"id\":\"2-1\",\"name\":\"采购申请\",\"isOver\":false,\"isNot\":true,\"isFinish\":false,\"x\":636,\"y\":180},{\"id\":\"2-2\",\"name\":\"采购启动\",\"isOver\":false,\"isNot\":true,\"isFinish\":false,\"x\":636,\"y\":230},{\"id\":\"2-3\",\"name\":\"采购实施\",\"isOver\":false,\"isNot\":true,\"isFinish\":false,\"x\":636,\"y\":280},{\"id\":\"2-4\",\"name\":\"合同签署\",\"isOver\":false,\"isNot\":true,\"isFinish\":false,\"x\":636,\"y\":330},{\"id\":\"2-5\",\"name\":\"无合同采购\",\"isOver\":false,\"isNot\":true,\"isFinish\":false,\"x\":636,\"y\":390},{\"id\":\"2-6\",\"name\":\"合同执行\",\"isOver\":false,\"isNot\":true,\"isFinish\":false,\"x\":636,\"y\":460},{\"id\":\"2-7\",\"name\":\"人机料法环落档\",\"isOver\":false,\"isNot\":true,\"isFinish\":false,\"x\":636,\"y\":520},{\"id\":\"2-8\",\"name\":\"成本采购\",\"isOver\":false,\"isNot\":true,\"isFinish\":false,\"x\":569,\"y\":160}],\"x\":580,\"y\":100,\"nodeType\":\"NORMAL_NODE\",\"nodeState\":\"UNDERWAY\",\"parentId\":3},{\"id\":\"4\",\"nodeKey\":\"TOLD_ACTION\",\"name\":\"履约执行\",\"actions\":[{\"id\":\"3-1\",\"name\":\"计划排程\",\"isOver\":false,\"isNot\":true,\"isFinish\":false,\"x\":893,\"y\":180},{\"id\":\"3-2\",\"name\":\"计划执行\",\"isOver\":false,\"isNot\":true,\"isFinish\":false,\"x\":893,\"y\":230,\"actions\":[{\"id\":\"3-2-1\",\"name\":\"现场作业\",\"isOver\":false,\"isNot\":true,\"isFinish\":false,\"x\":893,\"y\":263},{\"id\":\"3-2-2\",\"name\":\"关键路径\",\"isOver\":false,\"isNot\":true,\"isFinish\":false,\"x\":893,\"y\":298},{\"id\":\"3-2-3\",\"name\":\"合同里程碑\",\"isOver\":false,\"isNot\":true,\"isFinish\":false,\"x\":893,\"y\":332}]},{\"id\":\"3-3\",\"name\":\"项目完工\",\"isOver\":false,\"isNot\":true,\"isFinish\":false,\"x\":893,\"y\":383},{\"id\":\"3-4\",\"name\":\"日常任务 \",\"isOver\":false,\"isNot\":true,\"isFinish\":false,\"x\":893,\"y\":433}],\"x\":880,\"y\":100,\"nodeType\":\"NORMAL_NODE\",\"nodeState\":\"NOT_START\",\"parentId\":4},{\"id\":\"5\",\"nodeKey\":\"ACCEPTANCE_DELIVERY\",\"name\":\"验收交付\",\"actions\":[{\"id\":\"4-1\",\"name\":\"里程碑交付\",\"isOver\":false,\"isNot\":true,\"isFinish\":false,\"actions\":[{\"id\":\"4-1-1\",\"name\":\"售后服务\",\"nodeKey\":\"Sell-Service\",\"isOver\":false,\"isNot\":true,\"isFinish\":false,\"x\":1153,\"y\":255}],\"x\":1083,\"y\":180},{\"id\":\"4-2\",\"name\":\"验收结题\",\"isOver\":false,\"isNot\":true,\"isFinish\":false,\"x\":1083,\"y\":383},{\"id\":\"4-3\",\"name\":\"项目后评价\",\"isOver\":false,\"isNot\":true,\"isFinish\":false,\"x\":1083,\"y\":433}],\"x\":1070,\"y\":100,\"nodeType\":\"NORMAL_NODE\",\"nodeState\":\"NOT_START\",\"parentId\":5},{\"id\":\"6\",\"nodeKey\":\"END\",\"name\":\"财务结算\",\"actions\":[{\"id\":\"5-1\",\"name\":\"收入\",\"isOver\":false,\"isNot\":true,\"isFinish\":false,\"x\":1283,\"y\":180},{\"id\":\"5-2\",\"name\":\"支出\",\"isOver\":false,\"isNot\":true,\"isFinish\":false,\"x\":1283,\"y\":240},{\"id\":\"5-3\",\"name\":\"工时\",\"isOver\":false,\"isNot\":true,\"isFinish\":false,\"x\":1283,\"y\":300},{\"id\":\"5-4\",\"name\":\"分摊\",\"isOver\":false,\"isNot\":true,\"isFinish\":false,\"x\":1283,\"y\":360}],\"x\":1270,\"y\":100,\"nodeType\":\"NORMAL_NODE\",\"nodeState\":\"NOT_START\",\"parentId\":6},{\"id\":\"7\",\"isOver\":false,\"shape\":\"custom-vue-end-node\",\"nodeKey\":\"FLOW_END\",\"nodeState\":\"FLOW_END\",\"nodeType\":\"END_NODE\",\"x\":1308,\"y\":428,\"label\":\"结束\",\"parentId\":7}]";
    public static final Map<String, String> idNameMap = new HashMap<>();

    {
        idNameMap.put("0-1", "市场需求");
        idNameMap.put("0-2", "综合线索");
        idNameMap.put("1-1", "需求响应");
        idNameMap.put("0-3", "科研需求");
        idNameMap.put("1-2", "项目报价");
        idNameMap.put("2-1", "采购申请");
        idNameMap.put("1-3", "项目投标");
        idNameMap.put("2-2", "采购启动");
        idNameMap.put("3-1", "计划排程");
        idNameMap.put("1-4", "市场签署合同");
        idNameMap.put("2-3", "采购实施");
        idNameMap.put("3-2", "计划执行");
        idNameMap.put("3-2-2", "合同里程碑");
        idNameMap.put("4-1", "里程碑交付");
        idNameMap.put("1-5", "项目立项");
        idNameMap.put("2-4", "合同签署");
        idNameMap.put("3-3", "项目完工");
        idNameMap.put("4-2", "验收结题");
        idNameMap.put("5-1", "收入");
        idNameMap.put("1-6", "项目预立项");
        idNameMap.put("2-5", "无合同采购");
        idNameMap.put("3-4", "日常任务 ");
        idNameMap.put("4-3", "项目后评价");
        idNameMap.put("5-2", "支出");
        idNameMap.put("2-6", "合同执行");
        idNameMap.put("5-3", "工时");
        idNameMap.put("2-7", "人机料法环落档");
        idNameMap.put("5-4", "分摊");
        idNameMap.put("1", "线索与需求");
        idNameMap.put("2", "收入/销售管理");
        idNameMap.put("3", "采购管理");
        idNameMap.put("4", "履约执行");
        idNameMap.put("5", "验收交付");
        idNameMap.put("6", "财务结算");
        idNameMap.put("7", "");
        idNameMap.put("8", "");
        idNameMap.put("9", "");
    }

    private String id;
    private String nodeKey;
    private String name;
    private List<ProjectLifeItemVO> actions;
    private Integer x;
    private Integer y;
    // 开始结束 常用节点
    // START_END_NODE NORMAL_NODE
    private String nodeType;
    //  未开始  进行中 已完成
    //  NOT_START UNDERWAY FINISHED
    private String nodeState;

    private String shape;

    private String label;

    private String parentId;

    @ApiModelProperty("进行中")
    private Boolean isOver;
    @ApiModelProperty("未开始")
    private Boolean isNot;
    @ApiModelProperty("已完成")
    private Boolean isFinish;

    @Data
    @ApiModel(value = "ProjectLifeVO", description = "项目全生命周期监控")
    public class ProjectLifeItemVO {
        @ApiModelProperty("节点标识")
        private String id;
        @ApiModelProperty("数据Id")
        private String dataId;
        @ApiModelProperty("节点名称")
        private String name;
        @ApiModelProperty("节点key")
        private String nodeKey;
        @ApiModelProperty("进行中")
        private Boolean isOver;
        @ApiModelProperty("未开始")
        private Boolean isNot;
        @ApiModelProperty("已完成")
        private Boolean isFinish;
        private List<ProjectLifeItemVO> actions;
        private Integer x;
        private Integer y;
    }


    public static enum NodeState {
        FINISHED, UNDERWAY, NOT_START;
    }

    public static enum NodeType {
        START_END_NODE, NORMAL_NODE;
    }


    public List<ProjectLifeVO> setNodeStatus(Map<String, String> statusMap, Map<String, String> idDataIdMap) {
        List<ProjectLifeVO> nodes = JSONUtil.toBean(NODE_TPL, new TypeReference<>() {
        }, false);
        boolean isFinish = true;
        for(ProjectLifeVO n:nodes){
            String status = statusMap.get(n.getId());
            if (Objects.nonNull(status)) {
                n.setNodeState(status);
            } else {
                n.setNodeState(NodeState.NOT_START.name());
                n.setIsFinish(false);
            }
            if("0".equals(n.getId())){
                n.setNodeState("START");
                n.setIsOver(true);
            }
            List<ProjectLifeItemVO> nodeActions = n.getActions();
            if (!CollectionUtils.isEmpty(nodeActions)) {
                boolean parentProgressFlag = false;
                boolean parentNoStartFlag = false;
                for(ProjectLifeItemVO a:nodeActions){
                    List<ProjectLifeItemVO> subActions = a.getActions();
                    if (!CollectionUtils.isEmpty(subActions)) {
                        boolean progressFlag = false;
                        boolean noStartFlag = false;
                        for(ProjectLifeItemVO s:subActions){
                            String actionStatus = statusMap.get(s.getId());
                            s.setDataId(idDataIdMap.get(s.getId()));
                            if (Objects.nonNull(actionStatus)) {
                                if (NodeState.FINISHED.name().equals(actionStatus)) {
                                    s.setIsNot(false);
                                    s.setIsFinish(true);
                                    s.setIsOver(false);
                                }
                                if (NodeState.UNDERWAY.name().equals(actionStatus)) {
                                    s.setIsNot(false);
                                    s.setIsFinish(false);
                                    s.setIsOver(true);
                                    progressFlag = true;
                                }
                                if (NodeState.NOT_START.name().equals(actionStatus)) {
                                    s.setIsNot(true);
                                    s.setIsFinish(false);
                                    s.setIsOver(false);
                                    noStartFlag = true;
                                }

                            } else {
                                noStartFlag = true;
                                s.setIsNot(true);
                                s.setIsFinish(false);
                                s.setIsOver(false);
                            }
                        }
                        if(progressFlag){
                            a.setIsOver(true);
                            a.setIsNot(false);
                            a.setIsFinish(false);
                        }else if(!progressFlag && !noStartFlag){
                            a.setIsOver(false);
                            a.setIsNot(false);
                            a.setIsFinish(true);
                        }else{
                            a.setIsOver(false);
                            a.setIsNot(true);
                            a.setIsFinish(false);
                        }
                    }


                    String actionStatus = statusMap.get(a.getId());
                    a.setDataId(idDataIdMap.get(a.getId()));
                    if (Objects.nonNull(actionStatus)) {
                        if (NodeState.FINISHED.name().equals(actionStatus)) {
                            a.setIsNot(false);
                            a.setIsFinish(true);
                            a.setIsOver(false);
                        }
                        if (NodeState.UNDERWAY.name().equals(actionStatus)) {
                            a.setIsNot(false);
                            a.setIsFinish(false);
                            a.setIsOver(true);
                            parentProgressFlag = true;
                        }
                        if (NodeState.NOT_START.name().equals(actionStatus)) {
                            a.setIsNot(true);
                            a.setIsFinish(false);
                            a.setIsOver(false);
                            parentNoStartFlag = true;
                        }
                    } else {
                        parentNoStartFlag = true;
                        a.setIsNot(true);
                        a.setIsFinish(false);
                        a.setIsOver(false);
                    }
                }
                if(parentProgressFlag){
                    n.setIsOver(true);
                    n.setIsNot(false);
                    n.setIsFinish(false);
                }else if(!parentProgressFlag && !parentNoStartFlag){
                    n.setIsOver(false);
                    n.setIsNot(false);
                    n.setIsFinish(true);
                }else{
                    n.setIsOver(false);
                    n.setIsNot(true);
                    n.setIsFinish(false);
                }
                if(n.getIsNot() || n.getIsOver()){
                    isFinish = false;
                }
            }

            if("7".equals(n.getId())){
                if(isFinish){
                    n.setNodeState("FLOW_END");
                    n.setIsFinish(true);
                }else{
                    n.setNodeState("UNDERWAY");
                    n.setIsFinish(false);
                    n.setIsOver(true);
                }

            }
        }
        return nodes;
    }

}
