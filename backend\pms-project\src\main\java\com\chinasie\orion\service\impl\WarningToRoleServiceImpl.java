package com.chinasie.orion.service.impl;


import com.chinasie.orion.domain.entity.WarningToRole;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.WarningToRoleRepository;
import com.chinasie.orion.service.WarningToRoleService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/03/14:16
 * @description:
 */
@Service
public class WarningToRoleServiceImpl extends OrionBaseServiceImpl<WarningToRoleRepository,WarningToRole> implements WarningToRoleService {


    @Override
    public List<WarningToRole> saveParamList(String id, List<String> roleList) throws Exception {
        List<WarningToRole> warningToRoles = new ArrayList<>();

        for (String roleId : roleList) {
            WarningToRole warningToRole = new WarningToRole();
            warningToRole.setFromId(id);
            warningToRole.setToId(roleId);
            warningToRoles.add(warningToRole);
        }
        this.saveBatch(warningToRoles);
        return warningToRoles;
    }
}
