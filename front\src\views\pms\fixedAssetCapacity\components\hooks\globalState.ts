import { createGlobalState } from '@vueuse/shared';
import { ref } from 'vue';

export const useGlobalFixedAssetCapacityState = createGlobalState(() => {
  const refreshFixedAssetCapacityKey = ref('update');
  const updateFixedAssetCapacityKey = () => {
    refreshFixedAssetCapacityKey.value = Math.round(Math.random() * 100000000).toString(32);
  };
  return {
    updateFixedAssetCapacityKey,
    refreshFixedAssetCapacityKey,
  };
});