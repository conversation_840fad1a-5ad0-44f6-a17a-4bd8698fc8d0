<template>
  <BasicModal
    v-bind="$attrs"
    title="添加数据"
    width="80%"
    class="ModalBox"
    wrap-class-name="addDataSourceCAPP"
    :footer="null"
    @register="register"
    @visible-change="visibleChange"
  >
    <MyContent
      v-if="myShow"
      :dataType="dataType"
      @flowRows="flowRows"
    />
  </BasicModal>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, provide,
} from 'vue';
import {
  useActionsRecord, Layout, OrionTable, BasicTable, useTable, useModal, StatusTag, BasicModal, useModalInner,
} from 'lyra-component-vue3';
import { func } from 'vue-types';
import { emit } from 'process';
import MyContent from './MyContent.vue';
export default defineComponent({
  name: 'MyModal',
  components: {
    BasicModal,
    MyContent,
  },
  emits: ['flows'],
  setup(props, { emit }) {
    const state: any = reactive({
      myShow: false,
      dataType: '',
      qqq: {
        background: 'blue',
      },
    });

    /**
     * @description: 重置数据
     * @description: 改变loading
     * */
    function resetAll() {
      closeModal();
    }
    const [register, { closeModal }] = useModalInner((data) => {
      state.dataType = data.dataType;
      state.myShow = true;
    });
    function visibleChange(v) {
      if (!v) {
        state.myShow = v;
      }
    }
    function cancelClick() {
      resetAll();
    }
    provide('resetAll', resetAll);
    function flowRows(flowRows) {
      emit('flows', flowRows);
    }
    return {
      ...toRefs(state),
      register,
      visibleChange,
      flowRows,
    };
  },
});
</script>
<style lang="less">
//:deep(.ant-modal-root){
//  .ant-modal{
//    :deep(&.ant-modal-content){
//      background: blue !important;
//    }
//  }
//}
  //:deep(.ant-modal){
  //  background: #6e72fb;
  //  .ant-modal-content{
  //    .ant-modal-body{
  //      .scroll-container{
  //        padding:0 !important;
  //      }
  //      >.scrollbar{
  //        padding: 0 !important;
  //      }
  //    }
  //  }
  //
  //}
.addDataSourceCAPP {
  .scrollbar {
    padding: 0px !important;
  }
  .scroll-container{
    padding:0 !important;
  }
  .scrollbar__wrap {
    margin-bottom: 0px !important;
  }
}
</style>
