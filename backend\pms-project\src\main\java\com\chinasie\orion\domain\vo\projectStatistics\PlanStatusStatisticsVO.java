package com.chinasie.orion.domain.vo.projectStatistics;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * PlanStatusStatistics VO对象
 *
 * <AUTHOR>
 * @since 2023-12-21 11:28:29
 */
@ApiModel(value = "PlanStatusStatisticsVO对象", description = "项目内计划状态趋势统计表")
@Data
public class PlanStatusStatisticsVO implements Serializable{

    @ApiModelProperty(value = "统计ID")
    private String id;
    /**
     * 统计时间
     */
    @ApiModelProperty(value = "统计时间")
    private Date nowDay;

    /**
     * 时间展示
     */
    @ApiModelProperty(value = "时间展示")
    private String dateStr;

    /**
     * 唯一值
     */
    @ApiModelProperty(value = "唯一值")
    private String uk;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String typeId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 待发布数量
     */
    @ApiModelProperty(value = "待发布数量")
    private Integer waitReleaseCount;

    /**
     * 已发布数量
     */
    @ApiModelProperty(value = "已发布数量")
    private Integer releaseCount;

    /**
     * 已完成数量
     */
    @ApiModelProperty(value = "已完成数量")
    private Integer completeCount;

}
