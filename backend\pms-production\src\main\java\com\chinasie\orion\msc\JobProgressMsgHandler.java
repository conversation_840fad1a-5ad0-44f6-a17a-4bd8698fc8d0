package com.chinasie.orion.msc;

import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.constant.MessageNodeDict;
import com.chinasie.orion.domain.entity.JobManage;
import com.chinasie.orion.msc.api.MscBuildHandler;
import org.springframework.stereotype.Component;

import java.util.*;


/**
 * <AUTHOR>
 */
@Component
public class JobProgressMsgHandler implements MscBuildHandler<JobManage> {
    @Override
    public SendMessageDTO buildMsc(JobManage jobManage, Object... objects) {
        Map<String,Object> messageMap = new HashMap<>();
        messageMap.put("$name$",jobManage.getName());

        return SendMessageDTO.builder()
                .businessId(jobManage.getId())
                .todoStatus(0)
                .messageUrl("/pms/overhaulOperation/"+jobManage.getId())
                .messageUrlName("重大项目管理详情")
                .todoType(TodoTypeDict.TODO_TYPE_TASK)
                .recipientIdList(Collections.singletonList(objects[0].toString()))
                .messageMap(messageMap)
                .titleMap(messageMap)
                .senderTime(new Date())
                .senderId(jobManage.getCreatorId())
                .platformId(jobManage.getPlatformId())
                .orgId(jobManage.getOrgId())
                .build();
    }

    @Override
    public String support() {
        return MessageNodeDict.NODE_JOB_PROGRESS;
    }
}
