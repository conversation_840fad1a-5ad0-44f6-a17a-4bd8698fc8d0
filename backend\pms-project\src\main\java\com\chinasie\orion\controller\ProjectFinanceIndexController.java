package  com.chinasie.orion.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;




import com.chinasie.orion.domain.entity.ProjectFinanceIndex;
import com.chinasie.orion.domain.dto.ProjectFinanceIndexDTO;
import com.chinasie.orion.domain.vo.ProjectFinanceIndexVO;

import com.chinasie.orion.service.ProjectFinanceIndexService;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * ProjectFinanceIndex 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-15 16:41:01
 */
@RestController
@RequestMapping("/projectFinanceIndex")
@Api(tags = "项目-财务指标")
public class  ProjectFinanceIndexController  {

    @Autowired
    private ProjectFinanceIndexService projectFinanceIndexService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据【{{#id}}】", type = "项目-财务指标", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectFinanceIndexVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        ProjectFinanceIndexVO rsp = projectFinanceIndexService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectFinanceIndexDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#projectFinanceIndexDTO.name}}】", type = "项目-财务指标", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody ProjectFinanceIndexDTO projectFinanceIndexDTO) throws Exception {
        String rsp =  projectFinanceIndexService.create(projectFinanceIndexDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectFinanceIndexDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#projectFinanceIndexDTO.name}}】", type = "项目-财务指标", subType = "编辑", bizNo = "{{#projectFinanceIndexDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  ProjectFinanceIndexDTO projectFinanceIndexDTO) throws Exception {
        Boolean rsp = projectFinanceIndexService.edit(projectFinanceIndexDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "项目-财务指标", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = projectFinanceIndexService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "项目-财务指标", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectFinanceIndexService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "项目-财务指标", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ProjectFinanceIndexVO>> pages(@RequestBody Page<ProjectFinanceIndexDTO> pageRequest) throws Exception {
        Page<ProjectFinanceIndexVO> rsp =  projectFinanceIndexService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "列表")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "项目-财务指标", subType = "列表查询", bizNo = "")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public ResponseDTO<List<ProjectFinanceIndexVO>> list(@RequestBody ProjectFinanceIndexDTO projectFinanceIndexDTO) throws Exception {
        List<ProjectFinanceIndexVO> rsp =  projectFinanceIndexService.listByInfo( projectFinanceIndexDTO);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("项目-财务指标导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "项目-财务指标", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        projectFinanceIndexService.downloadExcelTpl(response);
    }

    @ApiOperation("项目-财务指标导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "项目-财务指标", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = projectFinanceIndexService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("项目-财务指标导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "项目-财务指标", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  projectFinanceIndexService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消项目-财务指标导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "项目-财务指标", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  projectFinanceIndexService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("项目-财务指标导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "项目-财务指标", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        projectFinanceIndexService.exportByExcel(searchConditions, response);
    }
}
