<script setup lang="ts">
import { BasicButton, isPower, OrionTable } from 'lyra-component-vue3';
import {
  computed, h, inject, ref, Ref, unref,
} from 'vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import Api from '/@/api';
import { get as _get } from 'lodash-es';
import { useSQEExcel } from '/@/views/pms/majorRepairsSecond/hooks/useImportAndExport';
import SQEDeviationTop from '/@/views/pms/majorRepairsSecond/pages/components/SQEDeviationTop.vue';
import SQEDeviationStatistics from '/@/views/pms/majorRepairsSecond/pages/components/SQEDeviationStatistics.vue';

const props = defineProps<{
  setSQEStatisticsData: Function
  radio: string
}>();

const { exportApi } = useSQEExcel();
const router = useRouter();
const loading = ref(false);
const detailsData: Record<string, any> = inject('detailsData');
const powerData: Ref<any[]> = inject('powerData', ref([]));
const powerRepairRound = computed(() => _get(detailsData, 'repairRound'));
const parentData = ref({});
const tableRef: Ref = ref();
const keyword: Ref<string> = ref('');
const tableOptions = {
  showToolButton: false,
  isSpacing: true,
  pagination: false,
  columns: [
    {
      title: '隐患编号',
      dataIndex: 'hiddenDangerCode',
    },
    {
      title: '事件主题',
      dataIndex: 'eventTopic',
    },
    {
      title: '事件等级',
      dataIndex: 'eventLevel',
    },
    {
      title: '事件地点',
      dataIndex: 'eventLocation',
    },
    // {
    //   title: '事件位置',
    //   dataIndex: 'eventPosition',
    // },
    // {
    //   title: '分类类型',
    //   dataIndex: 'classificationType',
    // },
    // {
    //   title: '隐患类型',
    //   dataIndex: 'hiddenDangerType',
    // },
    {
      title: '事发日期',
      dataIndex: 'occurrenceDate',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '大修轮次',
      dataIndex: 'majorRepairTurn',
    },
    {
      title: '隐患/事件领域',
      dataIndex: 'hiddenEvent',
    },
    {
      title: '事件类型',
      dataIndex: 'eventType',
    },
    {
      title: '金字塔类别',
      dataIndex: 'pyramidCategoryName',
    },
    {
      title: '是否考核',
      dataIndex: 'isAssessed',
      customRender({ text }) {
        return text === true ? '是' : text === false ? '否' : '';
      },
    },
    {
      title: '考核级别',
      dataIndex: 'assessmentLevelName',
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 80,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '查看',
      isShow: () => isPower('PMS_DXXQEC_container_05_button_01', powerData.value),
      onClick(record) {
        navDetails(record?.id);
      },
    },
  ],
  api: () => new Api('/pms/safety-quality-env').fetch({
    power: {
      pageCode: 'PMSMajorRepairsSecondDetail',
      containerCode: 'PMS_DXXQEC_container_05',
    },
    keyword: unref(keyword),
    majorRepairTurn: powerRepairRound.value,
  }, 'list', 'POST')
    .then((res) => {
      props?.setSQEStatisticsData?.(_get(res, 'evenStatisticMap', {}));
      return {
        ...res,
        content: _get(res, 'safetyQualityEnvVOList', []),
      };
    }),
};

function actionClick(actionItem: any, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'view':
      navDetails(record.id);
      break;
  }
}

function navDetails(id: string) {
  router.push({
    name: 'PMSSQEManageDetails',
    params: {
      id,
    },
  });
}
</script>

<template>
  <SQEDeviationStatistics v-if="radio==='statistics'" />

  <SQEDeviationTop v-if="radio==='top'" />

  <OrionTable
    v-if="radio==='detail'"
    ref="tableRef"
    v-model:keyword="keyword"
    :scroll="{y:300}"
    :options="tableOptions"
  >
    <template #toolbarLeft>
      <BasicButton
        icon="sie-icon-daochu"
        @click="exportApi(_get(detailsData, 'repairRound'))"
      >
        导出
      </BasicButton>
    </template>
  </OrionTable>
</template>

<style scoped lang="less">

</style>
