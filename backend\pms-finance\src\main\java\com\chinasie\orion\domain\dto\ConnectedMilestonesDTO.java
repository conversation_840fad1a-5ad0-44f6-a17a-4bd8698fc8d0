package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * ConnectedMilestones DTO对象
 *
 * <AUTHOR>
 * @since 2024-11-20 16:31:37
 */
@ApiModel(value = "ConnectedMilestonesDTO对象", description = "挂接里程碑")
@Data
@ExcelIgnoreUnannotated
public class ConnectedMilestonesDTO extends ObjectDTO implements Serializable {

    /**
     * 专业中心名称
     */
    @ApiModelProperty(value = "专业中心名称")
    @ExcelProperty(value = "专业中心 ", index = 0)
    private String expertiseCenterName;

    /**
     * 专业所名称
     */
    @ApiModelProperty(value = "专业所名称")
    @ExcelProperty(value = "专业所 ", index = 1)
    private String expertiseStationName;

    /**
     * 收入计划编号
     */
    @ApiModelProperty(value = "收入计划编号")
    @ExcelProperty(value = "收入计划编号 ", index = 2)
    private String number;

    /**
     * 收入计划月份
     */
    @ApiModelProperty(value = "收入计划月份")
    @ExcelProperty(value = "收入计划月份 ", index = 3)
    private Date estimateInvoiceDate;

    /**
     * 收入确认类型
     */
    @ApiModelProperty(value = "收入确认类型")
    @ExcelProperty(value = "收入确认类型 ", index = 4)
    private String incomeConfirmType;

    /**
     * 凭证编号
     */
    @ApiModelProperty(value = "凭证编号")
    @ExcelProperty(value = "凭证编号 ", index = 5)
    private String certificateSerialNumber;

    /**
     * 过账日期
     */
    @ApiModelProperty(value = "过账日期")
    @ExcelProperty(value = "过账日期 ", index = 6)
    private Date postingDate;

    /**
     * 确认收入金额
     */
    @ApiModelProperty(value = "确认收入金额")
    @ExcelProperty(value = "确认收入金额 ", index = 7)
    private BigDecimal confirmRevenueAmount;

    /**
     * 冲销暂估金额
     */
    @ApiModelProperty(value = "冲销暂估金额")
    @ExcelProperty(value = "冲销暂估金额 ", index = 8)
    private BigDecimal reverseAmount;

    /**
     * 合同编码(备注)
     */
    @ApiModelProperty(value = "合同编码(备注)")
    @ExcelProperty(value = "合同编码(备注) ", index = 9)
    private String contractNumRemark;

    /**
     * 里程碑名称(备注)
     */
    @ApiModelProperty(value = "里程碑名称(备注)")
    @ExcelProperty(value = "里程碑名称(备注) ", index = 10)
    private String mileStoneRemark;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号", index = 11)
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称", index = 12)
    private String contractName;

    /**
     * 合同里程碑
     */
    @ApiModelProperty(value = "合同里程碑")
    @ExcelProperty(value = "合同里程碑", index = 13)
    private String milestoneName;

    /**
     * 甲方单位名称
     */
    @ApiModelProperty(value = "甲方单位名称")
    @ExcelProperty(value = "甲方单位名称", index = 14)
    private String partyADeptIdName;

    /**
     * 开票/收入确认公司名称
     */
    @ApiModelProperty(value = "开票/收入确认公司名称")
    @ExcelProperty(value = "开票/收入确认公司", index = 15)
    private String billingCompanyName;

    /**
     * 关联合同id
     */
    @ApiModelProperty(value = "关联合同id")
    private String contractId;

    @ApiModelProperty(value = "合同里程碑Id")
    private String milestoneId;

    /**
     * 挂接状态:0未挂接，1已挂接
     */
    @ApiModelProperty(value = "挂接状态:0未挂接，1已挂接")
    private Integer hangingConnectStatus;

    /**
     * 甲方单位id
     */
    @ApiModelProperty(value = "甲方单位id")
    private String partyADeptId;

    /**
     * 专业中心
     */
    @ApiModelProperty(value = "专业中心")
    private String expertiseCenter;

    /**
     * 专业所
     */
    @ApiModelProperty(value = "专业所")
    private String expertiseStation;

    /**
     * 开票/收入确认公司
     */
    @ApiModelProperty(value = "开票/收入确认公司")
    private String billingCompany;

    @ApiModelProperty(value = "导出id")
    private List<String> ids;

    @ApiModelProperty(value = "专业中心")
    private List<String> centers;

    @ApiModelProperty(value = "专业所")
    private List<String> stations;

    @ApiModelProperty(value = "是否拥有全部查看权限")
    private Boolean isPermission;

    @ApiModelProperty(value = "用户编号")
    private String userId;

}
