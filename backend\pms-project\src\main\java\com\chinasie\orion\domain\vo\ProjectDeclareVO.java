package com.chinasie.orion.domain.vo;

/**
 * @author: yk
 * @date: 2023/10/18 10:43
 * @description:
 */
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;
import java.util.Date;
import java.util.List;

/**
 * ProjectDeclare Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-18 10:38:38
 */
@ApiModel(value = "ProjectDeclareVO对象", description = "项目申报")
@Data
public class ProjectDeclareVO extends ObjectVO implements Serializable{

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    private String projectNumber;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 项目预估金额
     */
    @ApiModelProperty(value = "项目预估金额")
    private BigDecimal estimateAmt;

    /**
     * 项目来源
     */
    @ApiModelProperty(value = "项目来源")
    private String projectSource;

    /**
     * 项目来源名称
     */
    @ApiModelProperty(value = "项目来源名称")
    private String projectSourceName;

    /**
     * 责任部门
     */
    @ApiModelProperty(value = "责任部门")
    private String rspDeptId;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    private String resUserId;

    /**
     * 责任部门名称
     */
    @ApiModelProperty(value = "责任部门名称")
    private String rspDept;
    /**
     * 责任部门名称
     */
    @ApiModelProperty(value = "责任部门名称")
    private String rspDeptName;

    /**
     * 责任人名称
     */
    @ApiModelProperty(value = "责任人名称")
    private String resUserName;


    /**
     * 项目类型
     */
    @ApiModelProperty(value = "项目类型")
    private String projectType;

    /**
     * 项目类型名称
     */
    @ApiModelProperty(value = "项目类型名称")
    private String projectTypeName;

    /**
     * 项目子类型
     */
    @ApiModelProperty(value = "项目子类型")
    private String projectSubType;

    /**
     * 项目子类型名称
     */
    @ApiModelProperty(value = "项目子类型名称")
    private String projectSubTypeName;

    /**
     * 项目申请理由
     */
    @ApiModelProperty(value = "项目申请理由")
    private String applyReason;

    /**
     * 项目申报申请单编码
     */
    @ApiModelProperty(value = "项目申报申请单编码")
    private String number;

    /**
     * 项目背景摘要
     */
    @ApiModelProperty(value = "项目背景摘要")
    private String projectBackground;

    /**
     * 项目目标摘要
     */
    @ApiModelProperty(value = "项目目标摘要")
    private String projectTarget;

    /**
     * 技术方案摘要
     */
    @ApiModelProperty(value = "技术方案摘要")
    private String technologyPlan;

    /**
     * 文件数据id
     */
    @ApiModelProperty(value = "文件数据id")
    List<String> fileDataIds;

    /**
     * 项目结束时间
     */
    @ApiModelProperty(value = "项目结束时间")
    private Date projectEndTime;

    /**
     * 项目开始时间
     */
    @ApiModelProperty(value = "项目开始时间")
    private Date projectStartTime;

    /**
     * 支持性材料列表
     */
    @ApiModelProperty(value = "支持性材料列表")
    private List<DocumentVO> materialList;
}
