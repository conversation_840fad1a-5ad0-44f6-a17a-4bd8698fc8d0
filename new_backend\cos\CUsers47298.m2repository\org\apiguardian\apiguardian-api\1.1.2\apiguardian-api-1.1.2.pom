<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to Gradle or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.apiguardian</groupId>
  <artifactId>apiguardian-api</artifactId>
  <version>1.1.2</version>
  <name>org.apiguardian:apiguardian-api</name>
  <description>@API Guardian</description>
  <url>https://github.com/apiguardian-team/apiguardian</url>
  <licenses>
    <license>
      <name>The Apache License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>apiguardian</id>
      <name>@API Guardian Team</name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:git://github.com/apiguardian-team/apiguardian.git</connection>
    <developerConnection>scm:git:git://github.com/apiguardian-team/apiguardian.git</developerConnection>
    <url>https://github.com/apiguardian-team/apiguardian</url>
  </scm>
</project>
