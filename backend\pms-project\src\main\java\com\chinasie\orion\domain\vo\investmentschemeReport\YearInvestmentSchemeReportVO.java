package com.chinasie.orion.domain.vo.investmentschemeReport;


import com.chinasie.orion.domain.vo.YearInvestmentSchemeVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "YearInvestmentSchemeReportVO对象", description = "年度投资计划申请、调整、总执行报表")
public class YearInvestmentSchemeReportVO extends YearInvestmentSchemeVO {
    @ApiModelProperty("公司名称")
    private String companyName;

    @ApiModelProperty("调整前年度投资计划")
    private YearInvestmentSchemeVO old;

    /**
     * 项目处室Id
     */
    @ApiModelProperty(value = "项目处室Id列表")
    private List<String> rspDeptIdList;


    /**
     * 项目处室Id
     */
    @ApiModelProperty(value = "项目处室Id")
    private String rspDeptId;

    /**
     * 项目状态
     */
    @ApiModelProperty(value = "项目状态")
    private Integer projectStatusCode;


    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;

    /**
     * 月份
     */
    @ApiModelProperty(value = "是否是重点投资 金额>=1000")
    private boolean pointProject;

}
