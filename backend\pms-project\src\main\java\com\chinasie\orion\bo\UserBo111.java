//package com.chinasie.orion.bo;
//
//import cn.hutool.core.collection.CollectionUtil;
//import cn.hutool.core.util.StrUtil;
//import com.chinasie.orion.dto.ResponseDTO;
//import com.chinasie.orion.exception.PMSErrorCode;
//import com.chinasie.orion.exception.PMSException;
//import com.chinasie.orion.feign.UserFeignService;
//import com.chinasie.orion.page.PageRequest;
//import com.chinasie.orion.page.PageResult;
//import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
//import com.chinasie.orion.sdk.domain.vo.business.RoleVO;
//import com.chinasie.orion.sdk.domain.vo.business.UserVO;
//import com.chinasie.orion.sdk.helper.UserRedisHelper;
//import com.chinasie.orion.util.ResponseUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import org.springframework.util.CollectionUtils;
//
//import javax.annotation.Resource;
//import java.util.*;
//import java.util.stream.Collectors;
//
///**
// * Created with IntelliJ IDEA.
// *
// * @author: lsy
// * @date: 2022/1/6 14:47
// * @description:
// */
//@Component
//public class UserBo {
//    @Autowired
//    private UserRedisHelper userRedisHelper;
//
//    @Resource
//    private UserFeignService userFeignService;
//
//    public Map<String, String> getNameByUserIdMap(List<String> userIdList) throws Exception {
//        List<UserVO> userList = getUserDetailByUserIdList(userIdList);
//        return userList.stream()
//                .filter(user -> StrUtil.isNotBlank(user.getId()))
//                .collect(Collectors.toMap(UserVO::getId, UserVO::getName));
//    }
//
//    public UserVO getUserById(String userId) throws Exception {
//        if (StrUtil.isBlank(userId)) {
//            return new UserVO();
//        }
//
//        return userRedisHelper.getUserById(userId);
//    }
//
//    public List<UserVO> getUserDetailByUserIdList(List<String> userIdList) throws Exception {
//        if (CollectionUtil.isEmpty(userIdList)) {
//            return new ArrayList<>();
//        }
//
//        List<UserVO> userList = userRedisHelper.getUserByIds(userIdList);
//        if (CollectionUtil.isEmpty(userList)) {
//            return new ArrayList<>();
//        }
//
//        return userList;
//    }
//
//    public List<RoleVO> getRoleByName(String name) throws Exception {
//        RoleVO roleVO = new RoleVO();
//        roleVO.setName(name);
//        roleVO.setStatus(1);
//        ResponseDTO<List<RoleVO>> responseDTO = userFeignService.getRoleByName(roleVO);
//        if (ResponseUtils.fail(responseDTO)) {
//            throw new PMSException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR);
//        }
//        List<RoleVO> result = responseDTO.getResult();
//        if (CollectionUtils.isEmpty(result)) {
//            return new ArrayList<>();
//        }
//        return result;
//    }
//
//    public List<RoleVO> getRoleByNameAndModuleId(String moduleId,String name) throws Exception {
//        RoleVO roleVO = new RoleVO();
//        roleVO.setName(name);
//        roleVO.setStatus(1);
//        ResponseDTO<List<RoleVO>> responseDTO = userFeignService.listModule(moduleId,name);
//        if (ResponseUtils.fail(responseDTO)) {
//            throw new PMSException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR);
//        }
//        List<RoleVO> result = responseDTO.getResult();
//        if (CollectionUtils.isEmpty(result)) {
//            return new ArrayList<>();
//        }
//        return result;
//    }
//
//
//    public PageResult<UserVO> getUserPage(PageRequest<UserVO> pageRequest) throws Exception {
//        ResponseDTO<PageResult<UserVO>> responseDTO = userFeignService.getUserPage(pageRequest);
//        if (ResponseUtils.fail(responseDTO)) {
//            throw new PMSException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR);
//        }
//        return responseDTO.getResult();
//    }
//
//    public List<ObjectVO> getUserList(UserVO userVO) throws Exception {
//        ResponseDTO<List<ObjectVO>> responseDTO = userFeignService.getUserList(userVO);
//        if (ResponseUtils.fail(responseDTO)) {
//            throw new PMSException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR);
//        }
//        return responseDTO.getResult();
//    }
//}
