package com.chinasie.orion.domain.dto.source;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/09/12/11:33
 * @description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OverlapResultDTO implements Serializable {
    private Date overlapStart;
    private Date overlapEnd;
    private int overlapDays;
}
