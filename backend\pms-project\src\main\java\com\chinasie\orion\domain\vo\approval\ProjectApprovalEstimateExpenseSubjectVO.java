package com.chinasie.orion.domain.vo.approval;

import com.chinasie.orion.util.TreeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.math.BigDecimal;
import java.lang.String;

import java.util.List;
/**
 * ProjectApprovalEstimateExpenseSubject VO对象
 *
 * <AUTHOR>
 * @since 2024-05-06 10:05:25
 */
@ApiModel(value = "ProjectApprovalEstimateExpenseSubjectVO对象", description = "概算科目")
@Data
public class ProjectApprovalEstimateExpenseSubjectVO extends ObjectVO implements TreeUtils.TreeNode<String, ProjectApprovalEstimateExpenseSubjectVO> {


        /**
         * 科目名称
         */
        @ApiModelProperty(value = "科目名称")
        private String name;

        /**
         * 项目立项id
         */
        @ApiModelProperty(value = "项目立项id")
        private String projectApprovalId;


        /**
         * 概算金额
         */
        @ApiModelProperty(value = "概算金额")
        private BigDecimal amount;


        /**
         * 父级
         */
        @ApiModelProperty(value = "父级")
        private String parentId;
//
//
//        /**
//         * 是否必填
//         */
//        @ApiModelProperty(value = "是否必填")
//        private Boolean required;


        /**
         * 编号
         */
        @ApiModelProperty(value = "编号")
        private String number;


        /**
         * 公式
         */
        @ApiModelProperty(value = "公式")
        private String formula;

        @ApiModelProperty(value = "自动计算金额")
        private BigDecimal calculateAmount;


        /**
         * 子项
         */
        @ApiModelProperty(value = "子项")
        private List<ProjectApprovalEstimateExpenseSubjectVO> children;
}
