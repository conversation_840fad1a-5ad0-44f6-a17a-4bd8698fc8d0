package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/3/23 10:51
 */
public class PrincipalStatisticVo implements Serializable {

    @ApiModelProperty(value = "数量")
    private Integer count = 0;

    @ApiModelProperty(value = "负责人")
    private String principalId;

    @ApiModelProperty(value = "负责人")
    private String principalName;

    /**
     * 未开始数量
     */
    @ApiModelProperty(value = "未开始数量")
    private Integer unFinishCount = 0;

    /**
     * 完成数量
     */
    @ApiModelProperty(value = "完成数量")
    private Integer finishCount = 0;

    /**
     * 进行中数量
     */
    @ApiModelProperty(value = "进行中数量")
    private Integer finishingCount = 0;

    public Integer getCount() {
        return unFinishCount + finishCount + finishingCount;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public String getPrincipalId() {
        return principalId;
    }

    public void setPrincipalId(String principalId) {
        this.principalId = principalId;
    }

    public String getPrincipalName() {
        return principalName;
    }

    public void setPrincipalName(String principalName) {
        this.principalName = principalName;
    }

    public Integer getUnFinishCount() {
        return unFinishCount;
    }

    public void setUnFinishCount(Integer unFinishCount) {
        this.unFinishCount = unFinishCount;
    }

    public Integer getFinishCount() {
        return finishCount;
    }

    public void setFinishCount(Integer finishCount) {
        this.finishCount = finishCount;
    }

    public Integer getFinishingCount() {
        return finishingCount;
    }

    public void setFinishingCount(Integer finishingCount) {
        this.finishingCount = finishingCount;
    }
}
