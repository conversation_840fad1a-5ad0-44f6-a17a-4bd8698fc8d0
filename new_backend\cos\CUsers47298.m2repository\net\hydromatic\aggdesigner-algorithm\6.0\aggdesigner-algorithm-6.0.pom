<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>net.hydromatic</groupId>
    <artifactId>aggdesigner</artifactId>
    <version>6.0</version>
  </parent>

  <artifactId>aggdesigner-algorithm</artifactId>
  <version>6.0</version>
  <packaging>jar</packaging>
  <name>Aggregate Designer Algorithm</name>
  <description>Algorithm that designs aggregate tables</description>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <top.dir>${project.basedir}/..</top.dir>
    <build.timestamp>${maven.build.timestamp}</build.timestamp>
  </properties>

  <dependencies>
    <!-- Sorted by groupId, artifactId. Put versions
         in dependencyManagement in the root POM, not here. -->
    <dependency>
      <groupId>commons-lang</groupId>
      <artifactId>commons-lang</artifactId>
    </dependency>
    <dependency>
      <groupId>commons-logging</groupId>
      <artifactId>commons-logging</artifactId>
    </dependency>

    <!-- Test dependencies. -->
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>net.hydromatic</groupId>
      <artifactId>foodmart-data-hsqldb</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
