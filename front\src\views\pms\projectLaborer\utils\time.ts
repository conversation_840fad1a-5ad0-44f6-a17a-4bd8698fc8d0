/**
 * @description 格式化时间
 */
export const formatterTime = (val) => {
  if (!val || val.length == 0) return '';
  val = new Date(val);
  let year = val.getFullYear();
  let month = val.getMonth() + 1;
  let day = val.getDate();
  let hours = val.getHours();
  let minutes = val.getMinutes();
  let seconds = val.getSeconds();
  month = month > 9 ? month : `0${month}`;
  day = day > 9 ? day : `0${day}`;
  hours = hours > 9 ? hours : `0${hours}`;
  minutes = minutes > 9 ? minutes : `0${minutes}`;
  seconds = seconds > 9 ? seconds : `0${seconds}`;
  return `${year}-${month}-${day}  ${hours}:${minutes}:${seconds}`;
};
export const parseTreeData = (data) => {
  data.forEach((item) => {
    item.disabled = item.status == 0;
    if (item.child && item.child.length > 0) {
      findChild(item.child);
    }
  });
  return data;
};
const findChild = (data) => {
  data.forEach((item) => {
    item.disabled = item.status !== 1;
    if (item.child && item.child.length > 0) {
      findChild(item.child);
    }
  });
};
