<template>
  <div
    ref="accountSummaryRef"
    class="account-summary-card-list"
  >
    <div
      class="card-item"
      :style="{width:cardWidth+'px'}"
    >
      <div class="p20">
        <div class="left-label">
          总成本额
        </div>
        <div class="left-value">
          {{ formatMoney(summaryData.expendMoney) }}
          <span>元</span>
        </div>
        <div class="left-money flex flex-pj card-item-top-middle">
          <div class="flex flex-ac">
            <span>使用比</span>
            <div class="p-b-lr-2 flex flex-ac">
              <span class="caret-up" />
            </div>

            {{ formatMoney(summaryData.useRatio) }}
          </div>
          <div class="flex flex-pj flex-ac">
            <span>余额比</span>
            <div class="p-b-lr-2 flex flex-ac">
              <span class="caret-down" />
            </div>
            {{ formatMoney(summaryData.residueRatio) }}
          </div>
        </div>
      </div>
      <div class="b-t-1">
        <div class="card-item-bottom p20">
          <div class="card-item-bottom-left">
            <span class="card-item-bottom-label">预算余额</span>
            <span>{{ formatMoney(summaryData.residueMoney) }}</span>
          </div>
        </div>
      </div>
    </div>
    <div
      class="card-item"
      :style="{width:cardWidth+'px'}"
    >
      <div class="p20">
        <div class="left-label">
          材料费
        </div>
        <div class="left-value-parent">
          <div class="left-value">
            {{ formatMoney(summaryData.materialsExpendMoney) }}
            <span>元</span>
          </div>
          <div class="left-value-right">
            <span>使用率</span> {{ summaryData.materialsUseRatio }}%
          </div>
        </div>
        <div class="card-item-img" />
      </div>
      <div class="b-t-1">
        <div class="card-item-bottom p20">
          <div class="card-item-bottom-left">
            <span class="card-item-bottom-label">材料费余额</span>
            <span>{{ formatMoney(summaryData.materialsResidueMoney) }}</span>
          </div>
        </div>
      </div>
    </div>
    <div
      class="card-item"
      :style="{width:cardWidth+'px'}"
    >
      <div class="p20">
        <div class="left-label">
          工资及劳务费
        </div>
        <div class="left-value-parent">
          <div class="left-value">
            {{ formatMoney(summaryData.wageExpendMoney) }}
            <span>元</span>
          </div>
          <div class="left-value-right">
            <span>使用率</span> {{ summaryData.wageUseRatio }}%
          </div>
        </div>
        <div class="card-item-img-bar" />
      </div>
      <div class="b-t-1">
        <div class="card-item-bottom p20">
          <div class="card-item-bottom-left">
            <span class="card-item-bottom-label">工资及劳务余额</span>
            <span>{{ formatMoney(summaryData.wageResidueMoney) }}</span>
          </div>
        </div>
      </div>
    </div>
    <div
      class="card-item"
      :style="{width:cardWidth+'px'}"
    >
      <div class="p20">
        <div class="flex flex-pj">
          <div>
            <div class="left-label">
              占用材料成本
            </div>
            <div class="left-value">
              {{ formatMoney(summaryData.materialsOccupationMoney) }}
              <span>元</span>
            </div>
          </div>
          <div>
            <div class="left-label">
              领用材料成本
            </div>
            <div class="left-value">
              {{ formatMoney(summaryData.materialsReceiveMoney) }}
              <span>元</span>
            </div>
          </div>
        </div>

        <AProgress
          class="card-item-top-middle"
          :percent="summaryData.materialsReceiveRatio||0"
        />
      </div>
      <div class="b-t-1">
        <div class="card-item-bottom p20">
          <div class="flex flex-pj">
            <span>占用比</span>
            <div class="p-b-lr-2 flex flex-ac">
              <span class="caret-up" />
            </div>
            {{ formatMoney(summaryData.materialsOccupationRatio) }}
          </div>
          <div class="flex flex-pj">
            <span>领用比</span>
            <div class="p-b-lr-2 flex flex-ac">
              <span class="caret-down" />
            </div>
            {{ formatMoney(summaryData.materialsReceiveRatio) }}
          </div>
        </div>
      </div>
    </div>
    <div
      class="card-item"
      :style="{width:cardWidth+'px'}"
    >
      <div class="p20">
        <div
          class="flex flex-ac"
          style="height: 131px"
        >
          <div style="height: 60px">
            <div class="card-item-indirect-fee-img" />
          </div>
          <div>
            <div class="left-label">
              成本超支情况
            </div>

            <div
              v-if="summaryData.overspendMoney>0"
              class="left-value"
            >
              <span style=" font-size: 14px;">已超支</span>
              {{ formatMoney(summaryData.overspendMoney) }}
              <span>元</span>
            </div>
            <div
              v-else
              class="left-value"
            >
              <span style="font-size: 14px;">--</span>
            </div>
          </div>
        </div>
      </div>
      <div class="b-t-1">
        <div class="card-item-bottom p20">
          <div class="flex flex-pj">
            <span>超支出</span>
            <div class="p-b-lr-2 flex flex-ac">
              <span class="caret-up" />
            </div>
            {{ formatMoney(summaryData.materialsOccupationRatio) }}
          </div>
          <div class="flex flex-pj">
            <span>余额比</span>
            <div class="p-b-lr-2 flex flex-ac">
              <span class="caret-down" />
            </div>
            {{ formatMoney(summaryData.residueRatio) }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import {
  BasicCard, OrionTable, BasicButton, BasicScrollbar,
} from 'lyra-component-vue3';
import { Progress as AProgress } from 'ant-design-vue';
import {
  onMounted, onUnmounted, ref, Ref,
} from 'vue';
import { formatMoney } from '/@/views/pms/projectInitiation/index';
import { Num } from 'windicss/types/lang/tokens';
import MaterialsCharts from './MaterialsCharts.vue';
import Api from '/@/api';
const props = withDefaults(defineProps<{
    formId:string
}>(), {
  formId: '',
});
const accountSummaryRef = ref();
const summaryData:Ref<Record<any, any>> = ref({});
const cardWidth:Ref<number> = ref(270);
onMounted(() => {
  getSummary();
  onResize();
  // 监听窗口大小变化
  window.addEventListener('resize', onResize);
});
onUnmounted(() => {
  window.removeEventListener('resize', onResize);
});
function onResize() {
  const tableWidth = accountSummaryRef.value.clientWidth;
  if ((tableWidth - 230) / 5 >= 270) {
    cardWidth.value = (tableWidth - 230) / 5;
  } else if ((tableWidth - 130) / 3 >= 270) {
    cardWidth.value = (tableWidth - 130) / 3;
  } else {
    cardWidth.value = (tableWidth - 80) / 2 >= 270 ? (tableWidth - 80) / 2 : 270;
  }
}
function getSummary() {
  new Api('/pms').fetch('', `budgetExpendStatistics/getTotalList/${props.formId}`, 'GET').then((res) => {
    res.dedicatedFeeRate = res.dedicatedFeeRate ? Number(res.dedicatedFeeRate.split('%')[0]) : 0;
    res.materialFeeRate = res.materialFeeRate ? Number(res.materialFeeRate.split('%')[0]) : 0;
    summaryData.value = res;
  });
}
</script>

<style lang="less" scoped>

.account-summary-card-list{
  padding: 20px 10px;
  display: flex;
  gap: 20px 50px;
  flex-wrap: wrap;

  .card-item{
    border: 1px solid #e9e9e9;
    width: 270px;
    .card-item-top-middle{
      height: 40px;
      margin-top: 16px;
    }

    .card-item-bottom{
      display: flex;
      align-items: center;
      justify-content: space-between;
      span{
        font-size: 14px;
      }
      .card-item-bottom-label{
        padding-right: 8px;
        color: #0000006d;
      }
    }
    :deep(.left-label){
      font-weight: 400;
      font-style: normal;
      color: rgba(0, 0, 0, 0.***************);
    }
    :deep(.left-value){
      font-weight: 400;
      font-style: normal;
      color: rgba(0, 0, 0, 0.***************);
      text-align: left;
      line-height: 38px;
      font-size: 30px;

      span{
        font-size: 16px;
      }
    }
    :deep(.left-money){
      font-size: 20px;
      font-weight: 400;
      font-style: normal;
      color: rgba(0, 0, 0, 0.***************);
      span{
        font-size: 16px;
      }
    }
    .left-value-parent{
      display: flex;
      justify-content: space-between;
      align-items: baseline;
      .left-value-right{
        font-weight: 400;
        font-style: normal;
        color: rgba(0, 0, 0, 0.***************);
        font-size: 16px;
      }
    }
    .card-item-img{
      background: url('./img/u9416.png') no-repeat right bottom;
      width: 100%;
      height: 40px;
      margin-top: 16px;
    }
    .card-item-img-bar{
      background: url('./img/u392.png') no-repeat right bottom;
      width: 100%;
      height: 40px;
      margin-top: 16px;
    }
    .ant-progress{
      margin-top: 25px;
    }

  }
  .card-item-fee{
    padding: 20px 0 !important;
    .card-item-fee-bottom{
      padding: 20px 10px 0 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .card-item-fee-bottom-val{
        display: flex;
        .val-label{
          font-weight: 400;
          font-style: normal;
          color: rgba(0, 0, 0, 0.42745098);
          font-size: 12px;
        }
      }
    }
  }
}
.caret-up{
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 5px 5px 5px;
  border-color: transparent transparent #46ba7b transparent;
}
.caret-down{
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 5px 5px 0 5px;
  border-color: red transparent transparent transparent;
}
.p-b-lr-2{
  padding-left: 2px;
  padding-right: 2px;
}
.b-t-1{
  border-top: 1px solid #e9e9e9;
}
.card-item-indirect-fee-img{
  width: 80px;
  background: url('./img/u9441.png') no-repeat right bottom;
  background-size: 60px 60px;
  margin-right: 20px;
  height: 100%;
}
</style>