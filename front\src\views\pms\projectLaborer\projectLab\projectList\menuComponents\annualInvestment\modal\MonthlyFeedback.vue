<template>
  <div class="project-life">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template
        #toolbarLeft
      >
        <div
          class="every-table-slots"
        >
          <BasicButton
            v-if="isPower('TZJH_container_button_11', powerData)"
            icon="add"
            type="primary"
            @click="addTable"
          >
            编制月度反馈表
          </BasicButton>
          <BasicButton
            v-if="isPower('TZJH_container_button_12', powerData)"
            icon="sie-icon-tijiao"
            type="primary"
            :disabled="!(selectedRows.length===1&&selectedRows[0].status===100)"
            @click="changeProcess"
          >
            提交
          </BasicButton>
          <BasicButton
            v-if="isPower('TZJH_container_button_13', powerData)"
            icon="edit"
            type="primary"
            :disabled="!(selectedRows.length===1&&selectedRows[0].status===100)"
            @click="editTable"
          >
            编辑
          </BasicButton>
          <BasicButton
            v-if="isPower('TZJH_container_button_14', powerData)"
            icon="delete"
            type="primary"
            :disabled="disabledDelete"
            @click="deleteTable"
          >
            删除
          </BasicButton>
        </div>
      </template>

      <template #name="{record}">
        <div
          :title="record.name"
          class="flex-te"
          @click="changePage(record)"
        >
          <span
            class="action-btn table-row-name"
          >{{ record.name }}</span>
        </div>
      </template>
    </OrionTable>

    <!--流程-->
    <!--    <ProcessSelect-->
    <!--      v-show="false"-->
    <!--      v-if="showProcess"-->
    <!--      ref="processRef"-->
    <!--      :selectedRows="selectedRows"-->
    <!--      href="/pms/monthlyFeedbackDetails"-->
    <!--      :process-name="processName"-->
    <!--      biz-catalog-name="月度计划反馈审批"-->
    <!--    />-->
    <AddMonthlyFeedback
      @register="register"
      @update="update"
    />
  </div>
</template>
<script lang="ts">
import {
  computed, defineComponent, inject, nextTick, provide, reactive, Ref, ref, toRefs,
} from 'vue';
import {
  BasicButton, isPower, OrionTable, useDrawer,
} from 'lyra-component-vue3';
import { useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import dayjs from 'dayjs';
import { deleteMonthFeedbackList, getMonthFeedbackList, monthlyFeedbackColumns } from '../index';
import AddMonthlyFeedback from '../components/AddMonthlyFeedback.vue';
// import ProcessSelect from '/@/views/pms/projectManage/components/ProcessSelect.vue';

export default defineComponent({
  name: 'InvestmentPlan',
  components: {
    OrionTable,
    BasicButton,
    // ProcessSelect,
    AddMonthlyFeedback,
  },
  setup() {
    const powerData = inject('powerData', null);
    const formData:Ref = inject('formData');
    const processRef = ref();
    const router = useRouter();
    const state = reactive({
      processName: computed(() => `${state.selectedRows.map((item) => item.name)
        .join(',')}`),
      formId: formData.value?.id,
      loadingProcess: false,
      params: {},
      selectedRows: [],
      selectedRowKeys: [],
      showProcess: false, // selectedRowKeys.length===0||selectedRows[0].status!==100
      disabledDelete: computed(() => {
        if (state.selectedRowKeys.length === 0) {
          return true;
        }
        return !state.selectedRows.every((item) => item.status === 100);
      }),
    });
    const [register, { openDrawer }] = useDrawer();

    const tableRef = ref();
    const tableOptions = ref({
      deleteToolButton: 'add|enable|disable|delete',
      rowSelection: {
        selectedRowKeys: computed(() => state.selectedRowKeys),
        onSelect: (record, selected, selectedRows, nativeEvent) => {
          state.selectedRows = selectedRows;
          state.selectedRowKeys = selectedRows.map((item) => item.id);
        },
        onSelectAll: (selected, selectedRows, changeRows) => {
          if (selected) {
            state.selectedRows = selectedRows;
            state.selectedRowKeys = selectedRows.map((item) => item.id);
          } else {
            state.selectedRows = [];
            state.selectedRowKeys = [];
          }
        },
      },
      showIndexColumn: false,
      showSmallSearch: false,
      pagination: false,
      // dataSource: [],
      api: (params) => getMonthFeedbackList(formData.value?.id),
      columns: monthlyFeedbackColumns,
    });

    function addTable() {
      openDrawer(true, {
        type: 'add',
        yearId: formData.value?.id,
      });
    }

    function editTable() {
      openDrawer(true, {
        type: 'edit',
        id: state.selectedRowKeys[0],
      });
    }

    function changePage(record) {
      router.push({
        name: 'MonthlyFeedbackDetails',
        params: {
          id: record.id,
        },
      });
    }

    function deleteTable() {
      Modal.confirm({
        title: '删除提示',
        content: '是否删除所选的数据？',
        onOk() {
          deleteMonthFeedbackList(state.selectedRowKeys)
            .then((res) => {
              message.success('删除数据成功');
              state.selectedRowKeys = [];
              tableRef.value.reload();
            });
        },
      });
    }

    function update(data) {
      state.selectedRowKeys = [];
      state.selectedRows = [];
      if (data.type === 'save') {
        tableRef.value.reload();
      } else {
        state.showProcess = true;
        state.loadingProcess = true;
        state.selectedRowKeys = [data.record.id];
        state.selectedRows = [data.record];
        nextTick(() => {
          processRef.value.bpmnMain.batchStartFlow([data.id], () => {
            // update();
          });
        });
      }
    }

    function changeProcess() {
      if (state.loadingProcess) return;
      state.showProcess = true;
      state.loadingProcess = true;
      nextTick(() => {
        processRef.value.bpmnMain.batchStartFlow(state.selectedRowKeys, () => {
          update({});
          state.showProcess = false;
        });
      });
    }

    function reLoadTable() {
      state.showProcess = false;
      state.loadingProcess = false;
      tableRef.value.reload({ page: 1 });
    }

    provide('reLoadTable', reLoadTable);

    return {
      ...toRefs(state),
      tableRef,
      tableOptions,
      addTable,
      register,
      changePage,
      deleteTable,
      update,
      editTable,
      processRef,
      changeProcess,
      isPower,
      powerData,
    };
  },
});
</script>

<style scoped lang="less">
.project-life {
  height: 500px;
  overflow: hidden;
}
</style>
