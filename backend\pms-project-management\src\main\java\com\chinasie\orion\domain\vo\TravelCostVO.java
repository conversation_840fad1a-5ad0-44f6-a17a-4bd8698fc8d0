package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

import java.util.List;
/**
 * TravelCost VO对象
 *
 * <AUTHOR>
 * @since 2024-10-28 14:45:17
 */
@ApiModel(value = "TravelCostVO对象", description = "差旅费用表")
@Data
public class TravelCostVO extends  ObjectVO   implements Serializable{


    /**
     * 年
     */
    @ApiModelProperty(value = "年")
    private String dataYear;

    /**
     * 月
     */
    @ApiModelProperty(value = "月")
    private Integer dataMonth;

    /**
     * 季度
     */
    @ApiModelProperty(value = "季度")
    private Integer dataQuarter;


    /**
     * 任务单号
     */
    @ApiModelProperty(value = "任务单号")
    private String taskNo;

    /**
     * 部门编号
     */
    @ApiModelProperty(value = "部门编号")
    private String deptNo;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String deptName;



    /**
     * 中心编码
     */
    @ApiModelProperty(value = "中心编码")
    private String orgCode;


    /**
     * 中心名称
     */
    @ApiModelProperty(value = "中心名称")
    private String orgName;


    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractCode;


    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;


    /**
     * 供应商编号
     */
    @ApiModelProperty(value = "供应商编号")
    private String supplierNo;


    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;


    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;


    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    private String userCode;


    /**
     * 差旅开始时间
     */
    @ApiModelProperty(value = "差旅开始时间")
    private Date startTime;


    /**
     * 差旅结束时间
     */
    @ApiModelProperty(value = "差旅结束时间")
    private Date endTime;


    /**
     * 差旅时长
     */
    @ApiModelProperty(value = "差旅时长")
    private Integer days;


    /**
     * 总住宿时长
     */
    @ApiModelProperty(value = "总住宿时长")
    private Integer hotelDays;


    /**
     * 住宿费补贴金额
     */
    @ApiModelProperty(value = "住宿费补贴金额")
    private BigDecimal hotelAllowance;


    /**
     * 住宿费总金额
     */
    @ApiModelProperty(value = "住宿费总金额")
    private BigDecimal hotelAmount;


    /**
     * 换乘总金额
     */
    @ApiModelProperty(value = "换乘总金额")
    private BigDecimal transferAmount;


    /**
     * 交通费总金额
     */
    @ApiModelProperty(value = "交通费总金额")
    private BigDecimal trafficAmount;


    /**
     * 综合补贴金额
     */
    @ApiModelProperty(value = "综合补贴金额")
    private BigDecimal compareAllowanceAmt;


    /**
     * 差旅报销总金额
     */
    @ApiModelProperty(value = "差旅报销总金额")
    private BigDecimal travelTotalAmt;


    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;




}
