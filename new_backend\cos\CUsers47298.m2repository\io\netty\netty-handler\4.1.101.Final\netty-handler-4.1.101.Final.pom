<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2012 The Netty Project
  ~
  ~ The Netty Project licenses this file to you under the Apache License,
  ~ version 2.0 (the "License"); you may not use this file except in compliance
  ~ with the License. You may obtain a copy of the License at:
  ~
  ~   https://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
  ~ WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
  ~ License for the specific language governing permissions and limitations
  ~ under the License.
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>io.netty</groupId>
    <artifactId>netty-parent</artifactId>
    <version>4.1.101.Final</version>
  </parent>

  <artifactId>netty-handler</artifactId>
  <packaging>jar</packaging>

  <properties>
    <javaModuleName>io.netty.handler</javaModuleName>
    <!-- Needed for SelfSignedCertificate -->
    <argLine.java9.extras>--add-exports java.base/sun.security.x509=ALL-UNNAMED</argLine.java9.extras>
  </properties>

  <name>Netty/Handler</name>

  <dependencies>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-common</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-resolver</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-buffer</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-transport</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-transport-native-unix-common</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-codec</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-tcnative-classes</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>${tcnative.artifactId}</artifactId>
      <classifier>${tcnative.classifier}</classifier>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcpkix-jdk15on</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bctls-jdk15on</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jetty.npn</groupId>
      <artifactId>npn-api</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jetty.alpn</groupId>
      <artifactId>alpn-api</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>${conscrypt.groupId}</groupId>
      <artifactId>${conscrypt.artifactId}</artifactId>
      <classifier>${conscrypt.classifier}</classifier>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
    </dependency>

    <dependency>
      <groupId>software.amazon.cryptools</groupId>
      <artifactId>AmazonCorrettoCryptoProvider</artifactId>
      <version>1.1.0</version>
      <classifier>linux-x86_64</classifier>
      <scope>test</scope>
    </dependency>

    <!-- Automatic native-image reflection metadata generation for handlers dependencies -->
    <dependency>
      <groupId>org.reflections</groupId>
      <artifactId>reflections</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-transport</artifactId>
      <version>${project.version}</version>
      <type>test-jar</type>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>

