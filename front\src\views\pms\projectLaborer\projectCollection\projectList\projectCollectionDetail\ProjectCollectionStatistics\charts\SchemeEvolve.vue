<script setup lang="ts">
import {
  computed, ComputedRef, inject, onMounted, ref, Ref, watch,
} from 'vue';
import { useChart } from './useChart';
import Api from '/@/api';
import EmptyView from '/@/views/pms/components/EmptyView.vue';
import SpinView from '/@/views/pms/components/SpinView.vue';

const loading: Ref<boolean> = ref(false);
const projectId: string = inject('projectId');
const evolveList: Ref<any[]> = ref([]);
const dataOptions: ComputedRef<any[]> = computed(() => [
  {
    color: '#33CC33',
    name: '已完成',
    data: evolveList.value.map((item) => item.completeCount),
  },
  {
    color: '#F7C19E',
    name: '逾期未完成',
    data: evolveList.value.map((item) => item.overdueCount),
  },
]);

const chartOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
  },
  grid: {
    top: 10,
    left: 20,
    right: 20,
    bottom: 0,
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    min(value) {
      return value.min - 1;
    },
    max(value) {
      return value.max + 1;
    },
    splitLine: {
      show: true,
      lineStyle: {
        type: 'dashed',
      },
    },
    axisTick: {
      show: false,
    },
    data: evolveList.value.map((item) => item.time),
  },
  yAxis: {
    type: 'value',
    splitLine: {
      show: false,
    },
  },
  series: dataOptions.value.map((item) => ({
    ...item,
    type: 'line',
  })),
}));

const chartRef: Ref = ref();
const chartInstance = useChart(chartRef);

watch(() => chartOption.value, (value) => {
  if (evolveList.value.length) {
    chartInstance.value.setOption(value);
  }
});

onMounted(() => {
  getEvolveList();
});

// 获取计划进展趋势列表
async function getEvolveList() {
  loading.value = true;
  try {
    const result = await new Api('/pms/projectCollectionStatistics/planMonth').fetch({
      projectId,
    }, '', 'GET');
    evolveList.value = result || [];
  } finally {
    setTimeout(() => {
      loading.value = false;
    }, 2000);
  }
}
</script>

<template>
  <div class="flex flex-ac mr10">
    <spin-view
      v-if="loading"
      class="project-scheme-chart"
    />
    <empty-view
      v-show="!loading && evolveList.length===0"
      class="project-scheme-chart"
    />
    <div
      v-show="!loading && evolveList.length"
      ref="chartRef"
      class="project-scheme-chart"
    />
    <div
      class="legend-main"
    >
      <div
        v-for="(item,index) in dataOptions"
        :key="index"
        class="custom-legend-item"
      >
        <span
          :style="{backgroundColor:item.color}"
        />
        <span>{{ item.name }}（{{ item.data.reduce((prev, next) => prev + next, 0) }}）</span>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.legend-main {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
}
</style>
