<template>
  <div class="card-item">
    <div class="card-header">
      <span class="status tag1">已创建</span>
      <span class="title">新建项目</span>
    </div>
    <div class="card-main">
      <div class="card-main-header">
        <span>本周里程碑完成：2</span>
        <span>本月里程碑完成：6</span>
      </div>
      <div class="card-main-content">
        <div class="left">
          <span>预算执行</span><span class="percent">12%</span><span>2执行 / 13预算</span>
        </div>
        <div class="right">
          <span>里程碑完成度</span><span class="percent">45%</span><span>2 / 13项目</span>
        </div>
      </div>
    </div>
    <div class="card-footer">
      <div>
        <Icon
          class="icon"
          icon="sie-icon-edit"
          size="16"
        />
      </div>
      <div>
        <Icon
          class="icon"
          icon="sie-icon-tianjiaxinzeng"
          size="16"
        />
      </div>
      <div>
        <Icon
          class="icon"
          icon="sie-icon-del"
          size="16"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Icon } from 'lyra-component-vue3';
</script>

<style scoped lang="less">
.card-item {
  display: flex;
  flex-direction: column;
  border: 1px solid #E9E9E9;
  box-sizing: border-box;
  border-radius: 2px;
  overflow: hidden;

  .card-header {
    display: flex;
    align-items: center;
    padding: 10px 16px;

    .status {
      font-size: 12px;
      height: 22px;
      border: 1px solid transparent;
      line-height: 20px;
      padding: 0 6px;
      margin-right: 4px;
      flex-shrink: 0;

      &.tag1 {
        color: ~`getPrefixVar('success-color')`;
        background-color: #F6FFED;
        border-color: #D9F7BE;
      }

      &.tag2 {
        color: ~`getPrefixVar('success-color')`;
        background-color: #F6FFED;
        border-color: #D9F7BE;
      }
    }

    .title {
      font-size: 16px;
      font-weight: 500;
      line-height: 16px;
      color: ~`getPrefixVar('primary-10')`;
      flex-grow: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .card-main {
    padding: 20px 30px;
    border-top: 1px solid #E9E9E9;
    border-bottom: 1px solid #E9E9E9;

    &-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      span {
        font-size: 14px;
        color: rgba(0, 0, 0, .65);
        line-height: 20px;
      }
    }

    &-content {
      border-radius: 4px;
      border: 1px solid #E9E9E9;
      padding: 15px 29px;
      margin: 20px auto 0;
      display: flex;
      justify-content: space-between;

      div {
        display: flex;
        flex-direction: column;
        font-size: 12px;
        color: rgba(0, 0, 0, .45);

        .percent {
          color: ~`getPrefixVar('primary-color')`;
          font-size: 24px;
          line-height: 32px;
          margin: 4px 0;
        }
      }
    }
  }

  .card-footer {
    background-color: #FAFAFA;
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: 40px;

    > div {
      position: relative;
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      flex: 1;

      .icon:hover {
        color: ~`getPrefixVar('primary-color')`;
        cursor: pointer;
      }
    }

    div + div::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      transform: translate(0, -50%);
      width: 1px;
      height: 14px;
      background-color: #E9E9E9;
    }
  }
}
</style>
