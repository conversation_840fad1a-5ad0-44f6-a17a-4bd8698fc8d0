<script setup lang="ts">
import {
  Layout,
  OrionTable,
  BasicTableAction,
  IOrionTableActionItem,
  BasicButton,
  BasicImport,
  useModal,
  downloadByData,
  isPower,
} from 'lyra-component-vue3';
import {
  computed, ComputedRef, h, ref, Ref,
} from 'vue';
import { Modal } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { openFormDrawer } from './utils';
import CertificateInfoEdit from './CertificateInfoEdit.vue';
import Api from '/@/api';
import { usePagePower } from '/@/views/pms/hooks';

const router = useRouter();
const tableRef: Ref = ref();
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const tableOptions = {
  rowSelection: {
    onChange(_keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  smallSearchField: undefined,
  columns: [
    {
      title: '操作',
      dataIndex: 'actions',
      width: 160,
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
    {
      title: '类型',
      dataIndex: 'certificateTypeName',
    },
    {
      title: '名称',
      dataIndex: 'name',
      customRender({ text, record }) {
        return h('span', {
          class: 'flex-te action-btn',
          title: text,
          onClick: () => navDetails(record?.id),
        }, text);
      },
    },
    {
      title: '等级',
      dataIndex: 'levelName',
    },
    {
      title: '发证机构',
      dataIndex: 'issuingAuthority',
    },
    {
      title: '是否需要复审',
      dataIndex: 'isNeedRenewal',
    },
  ],
  api: (params: Record<string, any>) => new Api('/pms/certificate-info').fetch({
    ...params,
    power: {
      containerCode: 'table-list-container-042a7f-certificateInfo-8GYSs37h',
      pageCode: 'list-container-042a7f-certificateInfo',
    },
  }, 'page', 'POST'),
};

const toolButtons: ComputedRef<Record<string, any>[]> = computed(() => [
  {
    event: 'add',
    text: '新增',
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
    powerCode: 'PMS_ZSBZK_container_01_button_01',
  },
  {
    event: 'import',
    text: '导入',
    icon: 'sie-icon-daoru',
    powerCode: 'PMS_ZSBZK_container_01_button_02',
  },
  {
    event: 'export',
    text: '导出',
    icon: 'sie-icon-daochu',
    powerCode: 'PMS_ZSBZK_container_01_button_03',
  },
  {
    event: 'batchDelete',
    text: '删除',
    icon: 'sie-icon-shanchu',
    powerCode: 'PMS_ZSBZK_container_01_button_04',
    disabled: selectedRows.value.length === 0,
  },
]);

function toolClick({ event }: Record<string, any>) {
  switch (event) {
    case 'add':
      openFormDrawer(CertificateInfoEdit, null, updateTable);
      break;
    case 'import':
      openImportModal(true, {});
      break;
    case 'export':
      Modal.confirm({
        title: '导出提示！',
        content: '确认导出数据？',
        onOk() {
          return downloadByData('/pms/certificate-info/export/excel', []);
        },
      });
      break;
    case 'batchDelete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除已勾选的数据吗？',
        onOk: () => deleteApi(selectedRows.value.map((item) => item.id)),
      });
      break;
  }
}

const actions: IOrionTableActionItem[] = [
  {
    text: '编辑',
    event: 'edit',
    isShow: () => isPower('PMS_ZSBZK_container_02_button_01', powerData.value),
  },
  {
    text: '查看',
    event: 'view',
    isShow: () => isPower('PMS_ZSBZK_container_02_button_02', powerData.value),
  },
  {
    text: '删除',
    event: 'delete',
    isShow: () => isPower('PMS_ZSBZK_container_02_button_03', powerData.value),
  },
];

function actionClick(actionItem: { event: string }, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'edit':
      openFormDrawer(CertificateInfoEdit, record, updateTable);
      break;
    case 'view':
      navDetails(record.id);
      break;
    case 'delete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除这条数据吗？',
        onOk: () => deleteApi([record.id]),
      });
      break;
  }
}

function navDetails(id: string) {
  router.push({
    name: 'PMSCertificateStandardsDetails',
    params: {
      id,
    },
  });
}

function updateTable() {
  tableRef.value?.reload();
}

function deleteApi(ids: string[]) {
  return new Promise((resolve) => {
    new Api('/pms/certificate-info').fetch(ids, 'remove', 'DELETE').then(() => {
      updateTable();
    }).finally(() => {
      resolve('');
    });
  });
}

const [register, { openModal: openImportModal }] = useModal();
const downloadFileObj = {
  url: '/pms/certificate-info/download/excel/tpl',
  method: 'GET',
};

function requestBasicImport(files: any[]) {
  const formData = new FormData();
  formData.append('file', files[0]);
  return new Api('/pms/certificate-info/import/excel/check').fetch(formData, '', 'POST');
}

function requestSuccessImport(importId: string) {
  return new Promise((resolve, reject) => {
    new Api(`/pms/certificate-info/import/excel/${importId}`).fetch('', '', 'POST').then(() => {
      updateTable();
      resolve(true);
    }).catch((e) => {
      reject(e);
    });
  });
}

function changeImportModalFlag({ succ, successImportFlag }) {
  if (!successImportFlag && succ) {
    return new Api(`/pms/certificate-info/import/excel/cancel/${succ}`).fetch('', '', 'POST');
  }
}

const { powerData, getPowerDataHandle } = usePagePower();
</script>

<template>
  <Layout
    v-get-power="{pageCode:'PMSCertificateStandards',getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <template
          v-for="button in toolButtons"
          :key="button.event"
        >
          <BasicButton
            v-is-power="[button.powerCode]"
            v-bind="button"
            @click="toolClick(button)"
          >
            {{ button.text }}
          </BasicButton>
        </template>
      </template>
      <template #actions="{record}">
        <BasicTableAction
          :actions="actions"
          :record="record"
          @actionClick="actionClick($event,record)"
        />
      </template>
    </OrionTable>

    <BasicImport
      :requestBasicImport="requestBasicImport"
      :requestSuccessImport="requestSuccessImport"
      :downloadFileObj="downloadFileObj"
      @register="register"
      @change-import-modal-flag="changeImportModalFlag"
    />
  </Layout>
</template>

<style scoped lang="less">

</style>
