import { Persistent, BasicKeys } from '/@/utils/cache/persistent';
import { CacheTypeEnum, TOKEN_KEY } from '/@/enums/cacheEnum';
import projectSetting from '/@/settings/projectSetting';
import { useUserStore } from '/@/store/modules/user';
import router from '/@/router';

const { permissionCacheType } = projectSetting;
const isLocal = permissionCacheType === CacheTypeEnum.LOCAL;

export function getToken() {
  const useStore = useUserStore();
  return useStore.getToken;
}

export function getAuthCache<T>(key: BasicKeys) {
  const fn = isLocal ? Persistent.getLocal : Persistent.getSession;
  return fn(key) as T;
}

export function setAuthCache(key: BasicKeys, value) {
  const fn = isLocal ? Persistent.setLocal : Persistent.setSession;
  return fn(key, value, true);
}

export function clearAuthCache(immediate = true) {
  const fn = isLocal ? Persistent.clearLocal : Persistent.clearSession;
  return fn(immediate);
}

/**
 * 锁定登出任务
 */
// 启动任务
async function startLockLogoutTask() {
  const userStore = useUserStore();
  // @ts-ignore
  const { getLockLogout: lockLogoutData } = userStore;
  if (lockLogoutData.freeTimeSeconds <= 0) {
    const { logout } = userStore;
    await logout();
    await router.push('/login');
  } else {
    setTimeout(() => {
      const userStore = useUserStore();
      const {
        getLockLogout: lockLogoutData,
        setLockData,
        getLockLogoutTaskStatus: taskStatus,
      } = userStore as any;
      if (!taskStatus) {
        return;
      }
      setLockData({
        ...lockLogoutData,
        freeTimeSeconds: lockLogoutData.freeTimeSeconds - 1,
      });
      startLockLogoutTask();
    }, 1000);
  }
}

export async function lockLogoutTask() {
  const userStore = useUserStore();
  const {
    getLockLogoutTaskStatus: taskStatus,
    resetLockTaskTime,
    setLockLogoutProgressStatus,
    getLockLogout,
  } = userStore as any;
  // 没有锁定信息
  if (!getLockLogout) {
    return;
  }
  // 如果没有启动
  if (!taskStatus) {
    setLockLogoutProgressStatus(true);
    await startLockLogoutTask();
  } else {
    // 重置时间
    resetLockTaskTime();
  }
}
