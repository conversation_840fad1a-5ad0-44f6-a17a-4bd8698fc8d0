package com.chinasie.orion.management.constant;

public enum ContractMethodsEnum {

    OPEN_TENDER("open_tender","公开招标"),
    INVITATION_TO_TENDER("invitation_to_tender","邀请招标"),
    INQUIRY("inquiry","询价"),
    COMPETITIVE_NEGOTIATIOn("competitive_negotiation","竞争性谈判"),
    SINGLE_SOURCE("single_source","单一来源"),
    NOT_DISCLOSED("not_disclosed","未公布"),
    OTHER("other","其他");

    public String name;
    public String desc;

    ContractMethodsEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(String value){

        for (ContractMethodsEnum lt : ContractMethodsEnum.values()) {
            if(lt.name.equals( value)){
                return lt.desc;
            }
        }
        return null;
    }

}
