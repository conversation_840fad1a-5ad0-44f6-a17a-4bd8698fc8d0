:deep(.surely-table-cell-inner) {
  .common-center {
    display: flex;
    justify-content: center;
  }

  .common-s {
    background: inherit;
    box-sizing: border-box;
    border-width: 1px;
    border-style: solid;
    border-radius: 4px;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    font-weight: 400;
    font-style: normal;
    font-size: 12px;
    text-align: center;
    height: 22px;
    line-height: 20px;
    padding: 0 5px;
  }

  .green-s {
    width: 50px;
    background-color: rgba(246, 255, 237, 1);
    border-color: rgba(183, 235, 143, 1);
    color: #52C41A;
  }

  .warn-s {
    width: 50px;
    background-color: rgba(255, 251, 230, 1);
    border-color: rgba(255, 229, 143, 1);
    color: #FAAD14;
  }

  .blue-s {
    width: auto;
    background-color: rgba(230, 247, 255, 1);
    border-color: rgba(145, 213, 255, 1);
    color: #1890FF;
  }

  .red-s {
    width: 50px;
    background-color: rgba(254, 240, 239, 1);
    border-color: rgba(255, 163, 158, 1);
    color: #F5222D;
  }
  .is-red {
    color: #F5222D;
  }
}
