import { isPower } from 'lyra-component-vue3';
import { h } from 'vue';
import { Router } from 'vue-router';
import Api from '/@/api';

// 格式化表格
export function formatTableColumns(router:Router): any[] {
  return [
    {
      title: '项目编码',
      dataIndex: 'projectNumber',
      customRender({ record, text }) {
        return h('span', {
          class: 'action-btn',
          onClick: () => openDetails(record, router), // 暂时先不做还没有相关页面
        }, text);
      },
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      customRender({ record, text }) {
        return h('span', {
          class: 'table-item',
        }, text);
      },
      width: 200,
    },
    {
      title: '承接中心',
      dataIndex: 'undertakingCenter',
      customRender({ record, text }) {
        return h('span', {
          class: 'table-item',
        }, text);
      },
      width: 200,
    },
    {
      title: '项目责任人',
      dataIndex: '',
      customRender({ record, text }) {
        return h('span', {
          class: 'table-item',
        }, text);
      },
      width: 200,
    },
    {
      title: '客户',
      dataIndex: 'custCompany',
      customRender({ record, text }) {
        return h('span', {
          class: 'table-item',
        }, text);
      },
      width: 200,
    },
    {
      title: '项目描述',
      dataIndex: 'principalName',
      customRender({ record, text }) {
        return h('span', {
          class: 'table-item',
        }, text);
      },
      width: 200,
    },
    {
      title: '项目成本（万）',
      dataIndex: 'projectCost',
      customRender({ record, text }) {
        return h('span', {
          class: 'table-item',
        }, text);
      },
      width: 200,
    },
    {
      title: '项目营收（万）',
      dataIndex: 'projectRevenue',
      customRender({ record, text }) {
        return h('span', {
          class: 'table-item',
        }, text);
      },
      width: 200,
    },
    {
      title: '客户满意度',
      dataIndex: '',
      customRender({ record, text }) {
        return h('span', {
          class: 'table-item',
        }, text);
      },
      width: 200,
    },
    {
      title: 'CCM 设备/隐患消除（个',
      dataIndex: '',
      customRender({ record, text }) {
        return h('span', {
          class: 'table-item',
        }, text);
      },
      width: 200,
    },
    {
      title: '大修工期节约 (H)',
      dataIndex: 'saveTime',
      customRender({ record, text }) {
        return h('span', {
          class: 'table-item',
        }, text);
      },
      width: 200,
    },
    {
      title: '集体剂量降低（man.mSv)',
      dataIndex: '',
      customRender({ record, text }) {
        return h('span', {
          class: 'table-item',
        }, text);
      },
      width: 200,
    },
    {
      title: '放射性固废减容减排',
      dataIndex: '',
      customRender({ record, text }) {
        return h('span', {
          class: 'table-item',
        }, text);
      },
      width: 200,
    },
    {
      title: '提升/恢复功率（兆瓦）',
      dataIndex: '',
      customRender({ record, text }) {
        return h('span', {
          class: 'table-item',
        }, text);
      },
      width: 200,
    },
    {
      title: '标准与导则 (份)',
      dataIndex: '',
      customRender({ record, text }) {
        return h('span', {
          class: 'table-item',
        }, text);
      },
      width: 200,
    },
    {
      title: '应急支持',
      dataIndex: '',
      customRender({ record, text }) {
        return h('span', {
          class: 'table-item',
        }, text);
      },
      width: 200,
    },
    {
      title: '安质环偏差',
      dataIndex: '',
      customRender({ record, text }) {
        return h('span', {
          class: 'table-item',
        }, text);
      },
      width: 200,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      customRender({ record, text }) {
        return h('span', {
          class: 'table-item',
        }, text);
      },
      width: 200,
    },
    {
      title: '操作',
      dataIndex: 'actions',
      width: 200,
      align: 'left',
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
  ];
}

// 点击项目编码跳转到详情页的操作处理（暂时先不做还没有相关页面）
function openDetails(record, router) {
  // router.push({
  //   name: 'TypicalQuestionDetails',
  //   params: {
  //     id: record.id,
  //   },
  // });
}
