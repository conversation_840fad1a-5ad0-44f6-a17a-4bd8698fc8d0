<script setup lang="ts">
import { BasicScrollbar } from 'lyra-component-vue3';
withDefaults(defineProps<{
  title: string,
  border?: boolean
}>(), {
  border: true,
});
</script>

<template>
  <div :class="['wrap',{'border':border}]">
    <div class="title">
      {{ title }}
    </div>
    <div class="content">
      <slot />
    </div>
  </div>
</template>

<style scoped lang="less">
.wrap {
  position: relative;
  padding: ~`getPrefixVar('content-padding-top')` ~`getPrefixVar('content-padding-left')`;

  &.border {
    border: 1px solid ~`getPrefixVar('border-color-base')`;
  }

  .title {
    position: relative;
    font-size: 16px;
    font-weight: bold;
    color: ~`getPrefixVar('text-color')`;
    padding: 0 ~`getPrefixVar('button-margin')`;
    line-height: 1;
    display: flex;
    align-items: center;

    &::before {
      position: absolute;
      content: '';
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 16px;
      background-color: ~`getPrefixVar('primary-color')`;
    }
  }

  .content {
    padding: ~`getPrefixVar('content-padding-top')` 0 0;
    height: calc(100% - 16px);
    display: flex;
    flex-direction: column;
  }
}
</style>
