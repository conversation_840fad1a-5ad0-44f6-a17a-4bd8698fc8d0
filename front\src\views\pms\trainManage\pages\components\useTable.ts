import { h, inject, Ref } from 'vue';
import dayjs from 'dayjs';
import MouseCellEdit from '/@/views/pms/trainManage/pages/components/MouseCellEdit.vue';
import Api from '/@/api';
import { message } from 'ant-design-vue';
import { DataStatusTag, isPower } from 'lyra-component-vue3';
import router from '/@/router';

interface ColumnsOptions {
    updateTable: Function
    isCheck?: boolean
    handleCenterName?: Function
    trainCenterStatus?: Ref<number>
    pageType?: string
}

export function useUserTable(options: ColumnsOptions) {
  const powerPrefix: string = inject('powerPrefix');
  const navDetails = (record) => {
    router.push({
      name: 'PMSEmployeeCapabilityPoolDetails',
      params: {
        id: record?.personId,
      },
    });
  };

  const columns = [
    {
      title: '员工号',
      dataIndex: 'userCode',
      fixed: 'left',
      width: 110,
      customRender({ text, record }) {
        if (record?.personId && isPower(`${powerPrefix}_03_03_button_01`, record?.rdAuthList)) {
          return h('span', {
            class: 'flex-te action-btn',
            title: text,
            onClick: () => navDetails(record),
          }, text);
        }
        return h('div', {
          class: 'flex-te',
          title: text,
        }, text);
      },
    },
    {
      title: '姓名',
      dataIndex: 'fullName',
      width: 110,
    },
    {
      title: '性别',
      dataIndex: 'sex',
      width: 110,
    },
    {
      title: '现任职务',
      dataIndex: 'nowPosition',
      width: 110,
    },
    {
      title: '所属中心',
      dataIndex: 'trainCenterName',
    },
    {
      title: '公司',
      dataIndex: 'companyName',
    },
    {
      title: '部门',
      dataIndex: 'deptName',
    },
    {
      title: '研究所',
      dataIndex: 'instituteName',
    },
    {
      title: '进入基地时间',
      dataIndex: '',
      isShow: options.isCheck,
      width: 110,
    },
    {
      title: '是否合格',
      dataIndex: 'isOK',
      isShow: options.isCheck,
      width: 110,
      customRender({ text, record }) {
        if (options?.trainCenterStatus.value !== 1 && isPower(`${powerPrefix}_03_03_button_03`, record?.rdAuthList)) {
          return h(MouseCellEdit, {
            component: 'Select',
            record,
            text: text ? '合格' : text === false ? '不合格' : '',
            componentValue: text,
            componentProps: {
              options: [
                {
                  label: '合格',
                  value: true,
                },
                {
                  label: '不合格',
                  value: false,
                },
              ],
            },
            onSubmit(option: { label: string, value: boolean }, resolve: (value: any) => void) {
              new Api(`/pms/train-person/setting/score${!option.value ? '/not' : ''}/ok`).fetch([record?.id], '', 'POST').then(() => {
                resolve(true);
                message.success('操作成功');
                options?.updateTable();
              });
            },
          });
        }
        return text ? '合格' : text === false ? '不合格' : '';
      },
    },
    {
      title: '培训成绩',
      dataIndex: 'score',
      isShow: options.isCheck,
      width: 110,
      customRender({ text, record }) {
        if (options?.trainCenterStatus.value !== 1 && record?.edit) {
          return h(MouseCellEdit, {
            component: 'InputNumber',
            record,
            text,
            componentValue: text,
            componentProps: {
              min: 0,
              max: 100,
              precision: 0,
            },
            onSubmit(value: any, resolve: (value: any) => void) {
              new Api('/pms/train-person/setting/score/value').fetch({
                id: record?.id,
                score: value,
              }, '', 'POST').then(() => {
                resolve(true);
                message.success('操作成功');
                options?.updateTable();
              });
            },
          });
        }
        return text;
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 170,
      fixed: 'right',
    },
  ].filter((item) => item.isShow || !('isShow' in item));

  return {
    columns,
    navDetails,
  };
}

export function useCenterTable(options: ColumnsOptions) {
  const powerPrefix: string = inject('powerPrefix');
  const columns = [
    {
      title: '中心名称',
      dataIndex: 'attendCenterName',
      width: 300,
      customRender({ text, record }) {
        if (isPower(`${powerPrefix}_03_01_button_01`, record?.rdAuthList)) {
          return h('span', {
            class: 'flex-te action-btn',
            title: text,
            onClick: () => options.handleCenterName(record),
          }, text);
        }
        return h('div', {
          class: 'flex-te',
          title: text,
        }, text);
      },
    },
    {
      title: '培训类型',
      dataIndex: ['trainManageVO', 'typeName'],
      ifShow: options.pageType === 'special',
      width: 100,
    },
    {
      title: '培训基地',
      dataIndex: ['trainManageVO', 'baseName'],
      width: 110,
    },
    {
      title: '培训名称',
      dataIndex: ['trainManageVO', 'name'],
      width: 150,
    },
    {
      title: '培训课时',
      dataIndex: ['trainManageVO', 'lessonHour'],
      width: 80,
    },
    {
      title: '拟完成时间',
      dataIndex: ['trainManageVO', 'completeDate'],
      width: 110,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '培训地点',
      dataIndex: 'trainAddress',
      width: 110,
      customRender({ text, record }) {
        if (record?.edit) {
          return h(MouseCellEdit, {
            component: 'Input',
            record,
            text,
            componentValue: text,
            onSubmit(value: any, resolve: (value: any) => void) {
              new Api('/pms/train-center/edit').fetch({
                ...record,
                trainAddress: value,
              }, '', 'PUT').then(() => {
                resolve(true);
                message.success('操作成功');
                options?.updateTable();
              });
            },
          });
        }
        return h('div', { class: 'flex-te' }, text);
      },
    },
    {
      title: '培训讲师',
      dataIndex: 'trainLecturer',
      width: 110,
      customRender({ text, record }) {
        if (record?.edit) {
          return h(MouseCellEdit, {
            component: 'Input',
            record,
            text,
            componentValue: text,
            onSubmit(value: any, resolve: (value: any) => void) {
              new Api('/pms/train-center/edit').fetch({
                ...record,
                trainLecturer: value,
              }, '', 'PUT').then(() => {
                resolve(true);
                message.success('操作成功');
                options?.updateTable();
              });
            },
          });
        }
        return h('div', { class: 'flex-te' }, text);
      },
    },
    {
      title: '中心培训联络人',
      dataIndex: 'contactPersonNames',
      width: 120,
      customRender({ text, record }) {
        if (isPower(`${powerPrefix}_03_01_button_05`, record?.rdAuthList)) {
          return h(MouseCellEdit, {
            component: 'InputSelectUser',
            record,
            text,
            componentValue: text ? text.split(',').map((name, index) => ({
              id: record?.contactPersonIds?.split(',')?.[index],
              name,
            })) : [],
            componentProps: {
              selectUserModalProps: {
                isRequired: true,
                treeDataApi: () => new Api('/pmi/organization/current/org/tree').fetch(
                  record?.attendCenterId ? [record.attendCenterId] : [],
                  '',
                  'POST',
                ),
              },
            },
            onSubmit(users: Record<string, any>[], resolve: (value: any) => void) {
              new Api('/pms/train-center/edit').fetch({
                ...record,
                contactPersonIds: users.map((item) => item.id).join(','),
                contactPersonNames: users.map((item) => item.name).join(','),
              }, '', 'PUT').then(() => {
                resolve(true);
                message.success('操作成功');
                options?.updateTable();
              });
            },
          });
        }
        return h('div', { class: 'flex-te' }, text);
      },
    },
    {
      title: '参培人数',
      dataIndex: 'trainNum',
      width: 80,
    },
    {
      title: '业务状态',
      dataIndex: 'dataStatus',
      width: 110,
      customRender({ text }) {
        return text ? h(DataStatusTag, { statusData: text }) : '';
      },
    },
    {
      title: '实际完成时间',
      dataIndex: 'endDate',
      width: 110,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 150,
      fixed: 'right',
    },
  ].filter((item) => item.ifShow || !('ifShow' in item));

  return {
    columns,
  };
}
