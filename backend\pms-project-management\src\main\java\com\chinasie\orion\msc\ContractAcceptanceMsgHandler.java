package com.chinasie.orion.msc;

import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.management.constant.MsgHandlerConstant;
import com.chinasie.orion.management.domain.entity.ContractExtendInfo;
import com.chinasie.orion.msc.api.MscBuildHandler;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class ContractAcceptanceMsgHandler implements MscBuildHandler<ContractExtendInfo> {

    @Autowired
    UserRedisHelper userRedisHelper;

    @Override
    public SendMessageDTO buildMsc(ContractExtendInfo contractExtendInfo, Object... objects) {
        Map<String, Object> message = new HashMap<>();
        String linkUrl = "";
        message.put("$contractNumber$", contractExtendInfo.getContractNumber());
        message.put("$contractName$", contractExtendInfo.getContractName());
        message.put("$link$", linkUrl);

        String userCode = contractExtendInfo.getTechnicalRspUserId();
        SimpleUser simpleUserByCode = userRedisHelper.getSimpleUserByCode(userCode);

        SendMessageDTO sendMessageDTO = SendMessageDTO.builder()
                .messageMap(message)
                .titleMap(message)
                .messageUrl("/pms/lumpSumContract")
                .messageUrlName("需求详情")
                .senderTime(new Date())
                .senderId(contractExtendInfo.getCreatorId())
                .recipientIdList(Collections.singletonList(simpleUserByCode.getId()))
                .platformId(contractExtendInfo.getPlatformId())
                .orgId(contractExtendInfo.getOrgId())
                .build();
        return sendMessageDTO;
    }

    @Override
    public String support() {
        return MsgHandlerConstant.NODE_CONTRACT_ACCEPTANCE;
    }
}
