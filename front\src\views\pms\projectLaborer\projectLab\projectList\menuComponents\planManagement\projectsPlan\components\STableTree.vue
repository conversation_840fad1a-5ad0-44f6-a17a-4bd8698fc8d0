<template>
  <OrionTable
    :options="tableOptions"
    default-expand-all-rows
    :columns="columns"
    :data-source="data"
    :pagination="false"
    :loading="loading"
    :custom-row="customRow"
    :showIndexColumn="false"
    :row-selection="pageType==='page'?{}:null"
    :expandIconColumnIndex="3"
    :border="true"
    @selectionChange="onChange"
  >
    <template
      v-if="showTitleBtn&&pageType==='page'"
      #toolbarLeft
    >
      <NavButton @buttonHandle="$attrs?.onButtonHandle">
        <template #upload>
          <Upload
            ref="upload"
            :before-upload="beforeUpload"
            :showUploadList="false"
            :multiple="false"
            accept=".xlsx"
            class="upload-btn-wrap"
          >
            <BasicButton
              class="mr10"
              :disabled="selectedRowKeys.length>1"
            >
              <ImportOutlined />
              批量导入
            </BasicButton>
          </Upload>
        </template>
      </NavButton>
    </template>
    <template #bodyCell="{ column, text ,record}">
      <template v-if="column.dataIndex === 'name'">
        <div
          v-if="isPower('XMX_container_button_07', powerData)"
          class="flex-te action-btn"
          :title="text"
        >
          <span
            @click.stop="checkClick(record)"
          >{{ text }}</span>
        </div>
        <div
          v-else
          class="flex-te"
          :title="text"
        >
          {{ text }}
        </div>
      </template>
      <template v-if="column.dataIndex === 'number'">
        <div
          class="flex-te"
          :title="text"
        >
          {{ text }}
        </div>
      </template>
      <template v-if="column.dataIndex === 'taskName'">
        <div
          class="flex-te"
          :title="text"
        >
          {{ text }}
        </div>
      </template>
      <template v-if="column.dataIndex === 'principalName'">
        <div
          class="flex-te"
          :title="text"
        >
          {{ text }}
        </div>
      </template>
      <template v-if="column.dataIndex === 'planTypeName'">
        <div
          class="flex-te"
          :title="text"
        >
          {{ text }}
        </div>
      </template>
      <template v-if="column.dataIndex === 'priorityLevelName'">
        <span :class="{ red: text === '最高' }">{{ text }}</span>
      </template>
      <template v-if="column.dataIndex === 'schedule'">
        {{ text === null ? '-' : text + '%' }}
      </template>
      <template v-if="column.dataIndex === 'status'">
        <DataStatusTag
          v-if="record.dataStatus"
          :status-data="record.dataStatus"
        />
      </template>
      <template v-if="column.dataIndex === 'action'">
        <BasicTableAction
          :actions="actionsBtn"
          :record="record"
        />
      </template>
    </template>
  </OrionTable>
</template>
<script lang="ts">
import {
  defineComponent, inject, onMounted, reactive, ref, toRefs,
} from 'vue';
import dayjs from 'dayjs';
import {
  BasicButton, DataStatusTag, isPower, OrionTable, ITableActionItem, BasicTableAction,
} from 'lyra-component-vue3';
import { message, Upload } from 'ant-design-vue';
import { ImportOutlined } from '@ant-design/icons-vue';
import { sTableTree } from '../data';
import NavButton from './NavButton.vue';
import Api from '/@/api';

export default defineComponent({
  components: {
    OrionTable,
    NavButton,
    DataStatusTag,
    BasicButton,
    ImportOutlined,
    Upload,
    BasicTableAction,
  },
  props: {
    formId: {
      type: String,
      default: '',
    },
    loading: Boolean,
    selectedRowKeys: {
      type: Array,
      default() {
        return [];
      },
    },
    data: {
      type: Array,
      default() {
        return [];
      },
    },
    pageType: {
      type: String,
      default: 'page',
    },
    showTitleBtn: {
      type: Boolean,
      default: true,
    },
    // 是否去除操作栏
    showAction: {
      type: Boolean,
      default: true,
    },
    onActionClick: {
      type: Function,
      default: () => null,
    },
  },
  emits: ['update:selectedRowKeys', 'checkClick'],
  setup(props, { emit }) {
    const upload = ref(null);
    const columnsToShow = props.showAction ? sTableTree.columns : sTableTree.columns.slice(0, sTableTree.columns.length - 1);

    const state = reactive({
      columns: columnsToShow,
      powerData: [],
      valueRadioGroup: 1,
    });
    state.powerData = inject('powerData');
    const getFormData = inject('getFormData');

    function onChange({ keys }) {
      emit('update:selectedRowKeys', keys);
    }

    function customRow(record) {
      return {
        onClick: () => {
          const selectedRowKeys = [...props.selectedRowKeys];
          if (selectedRowKeys.indexOf(record.id) >= 0) {
            selectedRowKeys.splice(selectedRowKeys.indexOf(record.id), 1);
          } else {
            selectedRowKeys.push(record.id);
          }
          emit('update:selectedRowKeys', selectedRowKeys);
        },
      };
    }

    function checkClick(data) {
      emit('checkClick', data);
    }

    function beforeUpload(res) {
      const formData = new FormData();
      formData.append('file', res);
      let id = props.selectedRowKeys.length === 1 ? props.selectedRowKeys[0] : props.formId;
      new Api('/pms').fetch(formData, `plan/import/excel/${id}`, 'POST').then((res) => {
        message.success('操作成功');
        getFormData.value();
      });
      return false;
    }

    const actionsBtn: ITableActionItem[] = [
      {
        text: '编辑',
        onClick(record: any) {
          return props.onActionClick?.('edit', record);
        },
      },
      {
        text: '删除',
        modal(record: any) {
          return props.onActionClick?.('delete', record);
        },
      },
    ];

    return {
      ...toRefs(state),
      upload,
      dayjs,
      beforeUpload,
      onChange,
      customRow,
      isPower,
      checkClick,
      tableOptions: {
        // settingCode: 'XMK_XMJH_table_01',
        deleteToolButton: 'add|delete|enable|disable',
        showSmallSearch: false,
        isFilter2: true,
        filterConfigName: 'PMS_PLANMANA_PROPLAN',
      },
      actionsBtn,
    };
  },
});
</script>

<style scoped>
.red {
  color: #cc2114;
}
</style>
