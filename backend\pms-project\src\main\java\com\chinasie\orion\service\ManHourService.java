package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.ManHourDTO;
import com.chinasie.orion.domain.entity.ManHour;
import com.chinasie.orion.domain.vo.ManHourVo;
import com.chinasie.orion.domain.vo.PlanManHourVo;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.PageRequest;
import com.chinasie.orion.page.PageResult;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/23/17:54
 * @description:
 */
public interface ManHourService extends OrionBaseService<ManHour> {

    /**
     *  分页获取工时信息
     * @param pageRequest
     * @return
     */
    PageResult<ManHourVo> pageList(PageRequest<ManHourDTO> pageRequest) throws Exception;

    /**
     *  通过计划ID获取工时列表
     * @param planId
     * @return
     */
    PlanManHourVo listByPlanId(String planId) throws Exception;

    /***
     *  新增工时
     * @param manHourDTO
     * @return
     */
    String saveManHour(ManHourDTO manHourDTO) throws Exception;

    /**
     *  修改工时
     * @param manHourDTO
     * @return
     */
    boolean updateManHour(ManHourDTO manHourDTO) throws Exception;

    /**
     *  删除数据通过数据ID列表
     * @param idList
     * @return
     */
    boolean delByIdList(List<String> idList) throws Exception;
}
