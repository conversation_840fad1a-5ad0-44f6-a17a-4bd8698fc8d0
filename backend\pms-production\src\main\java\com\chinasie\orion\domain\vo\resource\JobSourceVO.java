package com.chinasie.orion.domain.vo.resource;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/09/05/14:35
 * @description:
 */
@Data
public class JobSourceVO implements Serializable {
    @ApiModelProperty(value = "关系id")
    private String id;
    @ApiModelProperty(value = "人员code")
    private String userCode;
    @ApiModelProperty(value = "姓名")
    private String fullName;
    @ApiModelProperty(value = "大修轮次")
    private String repairRound;
    @ApiModelProperty(value = "作业名称")
    private String jobName;
    @ApiModelProperty(value = "重叠天数")
    private int overlapDayCount;
    @ApiModelProperty(value = "进出场时间")
    private List<InAndOutDateVO> inAndOutDateVOList;
}
