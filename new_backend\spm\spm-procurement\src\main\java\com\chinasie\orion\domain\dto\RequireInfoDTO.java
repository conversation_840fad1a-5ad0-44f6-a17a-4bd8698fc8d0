package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * RequireInfo DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@ApiModel(value = "RequireInfoDTO对象", description = "需求单")
@Data
@ExcelIgnoreUnannotated
public class RequireInfoDTO extends  ObjectDTO   implements Serializable{


    /**
     * 采购申请号
     */
    @ApiModelProperty(value = "采购申请号")
    @ExcelProperty(value = "采购申请号 ", index = 0)
    private String purchReqDocCode;

    @ApiModelProperty(value = "采购申请发起时间")
    @ExcelProperty(value = "采购申请发起时间 ", index = 1)
    private Date purchaseRequestInitTime;

    @ApiModelProperty(value = "采购立项完成时间")
    @ExcelProperty(value = "采购立项完成时间 ", index = 2)
    private Date projectEndTime;

    @ApiModelProperty(value = "商务负责人")
    @ExcelProperty(value = "商务负责人 ", index = 3)
    private String businessLeader;

    @ApiModelProperty(value = "采购申请金额（元）")
    @ExcelProperty(value = "采购申请金额（元） ", index = 4)
    private BigDecimal money;

    @ApiModelProperty(value = "剩余未使用金额")
    @ExcelProperty(value = "剩余未使用金额 ", index = 5)
    private BigDecimal unusedAmt;

    @ApiModelProperty(value = "已使用金额")
    @ExcelProperty(value = "已使用金额 ", index = 6)
    private BigDecimal usedAmt;

    /**
     * 时间
     */
    @ApiModelProperty(value = "时间")
    private Date time;

    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    private BigDecimal amount;

    /**
     * 责任部门
     */
    @ApiModelProperty(value = "责任部门")
    private String respDept;

    /**
     * 技术责任人
     */
    @ApiModelProperty(value = "技术责任人")
    private String techUser;

    /**
     * 汇总金额
     */
    @ApiModelProperty(value = "汇总金额")
    private BigDecimal totalAmt;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    private String mainTableId;

    /**
     * 剩余未使用金额
     */


    /**
     * 采购申请行号
     */
    @ApiModelProperty(value = "采购申请行号")
    private String projectID;





    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;

    @ApiModelProperty("搜索条件")
    List<List<SearchCondition>> searchConditions;






}
