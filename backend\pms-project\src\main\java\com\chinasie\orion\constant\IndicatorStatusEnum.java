package com.chinasie.orion.constant;

import java.util.Objects;

public enum IndicatorStatusEnum {
    DISENABLE(0, "禁用"),
    ENABLE(130, "启用");

    private String name;

    private Integer value;

    IndicatorStatusEnum(Integer value, String name) {
        this.name = name;
        this.value = value;
    }


    public String getName() {
        return name;
    }

    public Integer getValue() {
        return value;
    }


    public String getNameByValue(Integer value) {
        for (IndicatorStatusEnum indicatorStatusEnum : IndicatorStatusEnum.values()) {
            if (Objects.equals(indicatorStatusEnum.getValue(), value)) {
                return indicatorStatusEnum.getName();
            }
        }
        return "";
    }
}
