package com.chinasie.orion.domain.vo;

/**
 * @author: yk
 * @date: 2023/5/25 16:42
 * @description: 项目计划关联合同信息
 */
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

/**
 * ProjectSchemeContract Entity对象
 *
 * <AUTHOR>
 * @since 2023-05-25 16:26:04
 */
@ApiModel(value = "ProjectSchemeContractVO对象", description = "项目计划关联合同")
@Data
public class ProjectSchemeContractVO extends ObjectVO implements Serializable{

    /**
     * 项目计划id
     */
    @ApiModelProperty(value = "项目计划id")
    private String projectSchemeId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractId;

}
