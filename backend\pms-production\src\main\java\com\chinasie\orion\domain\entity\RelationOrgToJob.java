package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * RelationOrgToJob Entity对象
 *
 * <AUTHOR>
 * @since 2024-11-18 09:32:56
 */
@TableName(value = "pmsx_relation_org_to_job")
@ApiModel(value = "RelationOrgToJobEntity对象", description = "关系-大修组织工单关系")
@Data

public class RelationOrgToJob extends  ObjectEntity  implements Serializable{

    /**
     * 大修组织ID
     */
    @ApiModelProperty(value = "大修组织ID")
    @TableField(value = "repair_org_id")
    private String repairOrgId;

    /**
     * 作业工单号
     */
    @ApiModelProperty(value = "作业工单号")
    @TableField(value = "job_number")
    private String jobNumber;

}
