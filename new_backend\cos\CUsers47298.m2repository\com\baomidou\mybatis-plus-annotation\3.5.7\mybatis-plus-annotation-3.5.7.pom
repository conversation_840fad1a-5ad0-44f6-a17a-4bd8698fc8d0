<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.baomidou</groupId>
  <artifactId>mybatis-plus-annotation</artifactId>
  <version>3.5.7</version>
  <name>mybatis-plus</name>
  <description>An enhanced toolkit of Mybatis to simplify development.</description>
  <url>https://github.com/baomidou/mybatis-plus</url>
  <licenses>
    <license>
      <name>The Apache License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>baomidou</id>
      <name>hubin</name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <scm>
    <connection>scm:**************:Codearte/gradle-nexus-staging-plugin.git</connection>
    <developerConnection>scm:**************:Codearte/gradle-nexus-staging-plugin.git</developerConnection>
    <url>https://github.com/baomidou/mybatis-plus</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>org.mybatis</groupId>
      <artifactId>mybatis</artifactId>
      <version>3.5.16</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
  </dependencies>
</project>
