package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * PurchaseAppWbsWbs VO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@ApiModel(value = "PurchaseAppWbsWbsVO对象", description = "采购立项WBS信息")
@Data
public class PurchaseAppWbsWbsVO extends ObjectVO implements Serializable {

    /**
     * 项目编号/名称
     */
    @ApiModelProperty(value = "项目编号/名称")
    private String projectNumberName;


    /**
     * 总账科目
     */
    @ApiModelProperty(value = "总账科目")
    private String generalLedgerSubject;


    /**
     * WBS编号
     */
    @ApiModelProperty(value = "WBS编号")
    private String wbsNumber;


    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    private BigDecimal reqQuantity;


    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;


    /**
     * 交货时间
     */
    @ApiModelProperty(value = "交货时间")
    private Date deliveryTime;


    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    private BigDecimal unitPrice;


    /**
     * 总价
     */
    @ApiModelProperty(value = "总价")
    private BigDecimal totalPrice;


    /**
     * 本位币金额
     */
    @ApiModelProperty(value = "本位币金额")
    private BigDecimal localCurrencyAmt;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    private String mainTableId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;
}
