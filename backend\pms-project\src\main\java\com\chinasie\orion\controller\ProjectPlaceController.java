package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ProjectPlaceDTO;
import com.chinasie.orion.domain.vo.ProjectPlaceVO;
import com.chinasie.orion.service.ProjectPlaceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;





import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

/**
 * <p>
 * ProjectPlace 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-29 17:29:22
 */
@RestController
@RequestMapping("/projectPlace")
@Api(tags = "项目地点")
public class ProjectPlaceController {

    @Autowired
    private ProjectPlaceService projectPlaceService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据【{{#id}}】", type = "项目地点", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectPlaceVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        ProjectPlaceVO rsp = projectPlaceService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectPlaceDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#projectPlaceDTO.name}}】", type = "项目地点", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody ProjectPlaceDTO projectPlaceDTO) throws Exception {
        String rsp =  projectPlaceService.create(projectPlaceDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectPlaceDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#projectPlaceDTO.name}}】", type = "项目地点", subType = "编辑", bizNo = "{{#projectPlaceDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  ProjectPlaceDTO projectPlaceDTO) throws Exception {
        Boolean rsp = projectPlaceService.edit(projectPlaceDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "项目地点", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = projectPlaceService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "项目地点", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectPlaceService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "项目地点", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ProjectPlaceVO>> pages(@RequestBody Page<ProjectPlaceDTO> pageRequest) throws Exception {
        Page<ProjectPlaceVO> rsp =  projectPlaceService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

}
