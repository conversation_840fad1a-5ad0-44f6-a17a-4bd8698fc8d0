<script setup lang="ts">
import {
  computed, h, inject, ref,
} from 'vue';
import {
  BasicButton, isPower, OrionTable, randomString,
} from 'lyra-component-vue3';
import { Modal, Space } from 'ant-design-vue';
import { get, map } from 'lodash-es';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { useAssetsModal } from '../hooks/useAssetsModal';
import WbsTableModal from '../hooks/components/WbsTableModal.vue';
import Api from '/@/api';

const detailsData = inject('detailsData');
const updateRefreshKey = inject('updateRefreshKey');
const powerData = inject('powerData');

const tableRef = ref();
const selectKeys = ref([]);
const dataSource = computed(() => get(detailsData, 'detailWbsVOList' ?? []));
const tableOptions = {
  showToolButton: false,
  isSpacing: true,
  pagination: false,
  showSmallSearch: false,
  ...parseRowSelection({
    rowSelection: {
      onChange(val) {
        selectKeys.value = val;
      },
    },
  }),
  columns: computed(() => [
    {
      title: '级别',
      dataIndex: 'projectLevel',
      width: 60,
    },
    {
      title: '工作分解结构元素',
      dataIndex: 'wbsElement',
      width: 200,
    },
    {
      title: 'PS:短描述',
      dataIndex: 'description',
      width: 200,
      minWidth: 200,
    },
    {
      title: '业务分类名称',
      dataIndex: 'businessName',
      width: 150,
    },
    {
      title: '功能范围',
      dataIndex: 'functionalScope',
      width: 100,
    },
    {
      title: '开头状态',
      dataIndex: 'initialStatus',
      width: 100,
    },
    {
      title: '利润中心',
      dataIndex: 'profitCenterName',
      width: 300,
    },
    {
      title: '所属工厂',
      dataIndex: 'company',
      width: 120,
    },
    {
      title: '负责人编号',
      dataIndex: 'directorCode',
      width: 130,
    },
    {
      title: '负责人名称',
      dataIndex: 'directorName',
      width: 130,
    },
    ...parseColAction({
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 100,
      fixed: 'right',
    }),
  ]).value,
  actions: [
    {
      text: '移除',
      isShow: (record) => [isPower('PMS_ZCZGXQ_container_01_01_buton_02', powerData.value), [101].includes(detailsData.status)].every(Boolean),
      onClick(record) {
        Modal.confirm({
          title: '移除警告',
          icon: h(ExclamationCircleOutlined),
          content: '确定移除这条数据',
          async onOk() {
            try {
              const targetKey = [record.id];
              return await handleRemoveRequest(targetKey);
            } catch {
            }
          },
          onCancel() {},
        });
      },
    },
  ],
};

const showAddBtn = computed(() => [isPower('PMS_ZCZGXQ_container_01_01_buton_01', powerData.value), [101].includes(detailsData.status)].every(Boolean));
const showRemoveBtn = computed(() => [isPower('PMS_ZCZGXQ_container_01_01_buton_02', powerData.value), [101].includes(detailsData.status)].every(Boolean));

function parseColAction(cfg) {
  if (detailsData.status === 101) {
    return [cfg];
  }
  return [];
}
function parseRowSelection(cfg) {
  if (detailsData.status === 101) {
    return cfg;
  }
  return {};
}

function updateTable() {
  updateRefreshKey.value = randomString(50);
}
function handleAddWbsModal() {
  useAssetsModal(WbsTableModal, {
    title: '添加WBS',
    height: 720,
    detailsData,
    defaultSelectKeys: map(get(detailsData, 'detailWbsVOList' ?? []), 'id'),
  }, updateTable);
}
function handleRemoveRows() {
  Modal.confirm({
    title: '移除警告',
    icon: h(ExclamationCircleOutlined),
    content: '确定移除选中的数据',
    async onOk() {
      try {
        const targetKeys = tableRef.value?.getSelectRowKeys();
        return await handleRemoveRequest(targetKeys);
      } catch {
      }
    },
    onCancel() {},
  });
}
function handleRemoveRequest(idxs) {
  return new Promise((resolve) => {
    new Api('/pms/projectAssetApplyDetailWbs/remove')
      .fetch(idxs, '', 'DELETE')
      .then((res) => {
        updateTable();
        resolve(res);
      });
  });
}
</script>

<template>
  <div class="association-wbs-data">
    <OrionTable
      ref="tableRef"
      class="scroll-table"
      :dataSource="dataSource"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <Space
          :size="6"
          align="center"
        >
          <BasicButton
            v-if="showAddBtn"
            type="primary"
            icon="sie-icon-tianjiaxinzeng"
            @click="handleAddWbsModal"
          >
            添加
          </BasicButton>
          <BasicButton
            v-if="showRemoveBtn"
            :disabled="!selectKeys.length"
            icon="sie-icon-shanchu"
            @click="handleRemoveRows"
          >
            移除
          </BasicButton>
        </Space>
      </template>
    </OrionTable>
  </div>
</template>

<style scoped lang="less">
.association-wbs-data{
  height: 500px;
  overflow: hidden;
  margin-bottom: 20px;
  :deep(.ant-basic-table){
    padding:16px 0 0 0 !important;
  }
}
</style>