package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * ScientificResearchDemandDeclare VO对象
 *
 * <AUTHOR>
 * @since 2023-11-23 11:16:57
 */
@ApiModel(value = "ScientificResearchDemandDeclareVO对象", description = "科研需求申报")
@Data
public class ScientificResearchDemandDeclareVO extends ObjectVO implements Serializable{

    /**
     * 申报名称
     */
    @ApiModelProperty(value = "申报名称")
    private String name;

    /**
     * 申报编号
     */
    @ApiModelProperty(value = "申报编号")
    private String number;

    /**
     * 责任部门
     */
    @ApiModelProperty(value = "责任部门")
    private String resDept;

    /**
     * 责任部门名称
     */
    @ApiModelProperty(value = "责任部门名称")
    private String resDeptName;


    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    private String resPerson;

    /**
     * 责任人名称
     */
    @ApiModelProperty(value = "责任人名称")
    private String resPersonName;

    /**
     * 线索id
     */
    @ApiModelProperty(value = "线索id")
    private String clueId;

    /**
     * 线索id
     */
    @ApiModelProperty(value = "线索名称")
    private String clueName;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private String priority;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private String priorityName;

    /**
     * 申报理由
     */
    @ApiModelProperty(value = "申报理由")
    private String declareReason;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = " 开始时间")
    private Date beginTime;

    /**
     * 期望结束时间
     */
    @ApiModelProperty(value = "期望结束时间")
    private Date endTime;


    /**
     * 申报背景摘要
     */
    @ApiModelProperty(value = "申报背景摘要")
    private String declareBackground;

    /**
     * 申报目标
     */
    @ApiModelProperty(value = "申报目标")
    private String declareTarget;


    /**
     * 申报技术摘要
     */
    @ApiModelProperty(value = "申报技术摘要")
    private String declareTechnology;
    /**
     * 文件数据id
     */
    @ApiModelProperty(value = "文件数据id")
    List<String> fileDataIds;

    /**
     * 支持性材料列表
     */
    @ApiModelProperty(value = "支持性材料列表")
    private List<DocumentVO> materialList;
    /**
     * 线索信息
     */
    @ApiModelProperty(value = "线索信息")
    private LeadManagementVO leadManagementVO;

}

