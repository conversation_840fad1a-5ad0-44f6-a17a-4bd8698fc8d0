<script setup lang="ts">
import { STable } from '@surely-vue/table';
import {
  computed, h, inject, onActivated, onMounted, Ref, ref, unref,
} from 'vue';
import { BasicCard, BasicTableAction, DataStatusTag } from 'lyra-component-vue3';
import { DatePicker } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { useLaborCost } from './hooks';

const router = useRouter();
const basicContractEmployerPlan: Record<string, any> = inject('basicContractEmployerPlan');
const year: Ref<string> = ref(dayjs().year().toString());
const data: Ref<any[]> = ref([]);

const {
  getApi, tableKey, tableData, fetching, openCheckLaborCostDrawer,
} = useLaborCost();

onMounted(() => {
  reqTable();
});

onActivated(() => {
  reqTable();
});

function reqTable() {
  getApi({
    year: unref(year),
    contractNumber: basicContractEmployerPlan.contractNumber,
  });
}

const tableRef = ref();

const columns = [
  {
    title: '组织',
    dataIndex: 'name',
    fixed: 'left',
    width: 300,
    ellipsis: true,
  },
  {
    title: '验收状态',
    dataIndex: 'dataStatus',
    width: 100,
    customRender({ text }) {
      return text ? h(DataStatusTag, { statusData: text }) : '';
    },
  },
  {
    title: '当前审批人',
    dataIndex: 'currentApprover',
    width: 100,
  },
  {
    title: '实际总金额',
    dataIndex: 'actualTotalAmount',
    width: 120,
    align: 'right',
  },
  {
    title: '工作量(人/月)',
    dataIndex: 'workload',
    align: 'right',
    width: 120,
  },
  {
    title: '岗级成本',
    dataIndex: 'jobGradeAmt',
    width: 100,
    align: 'right',
  },
  {
    title: '住宿费',
    dataIndex: 'hotelAmount',
    width: 100,
    align: 'right',
  },
  {
    title: '换乘费',
    dataIndex: 'transferAmount',
    width: 100,
    align: 'right',
  },
  {
    title: '基地后勤费',
    dataIndex: 'logisticsAmt',
    width: 100,
    align: 'right',
  },
  {
    title: 'RP体检费',
    dataIndex: 'medicalExaminationAmt',
    width: 100,
    align: 'right',
  },
  {
    title: '劳保用品费',
    dataIndex: 'articlesAmt',
    width: 100,
    align: 'right',
  },
  {
    title: '餐厅管理费',
    dataIndex: 'restaurantAmt',
    width: 100,
    align: 'right',
  },
  {
    title: '其他费',
    dataIndex: 'otherAmt',
    width: 100,
    align: 'right',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 120,
    fixed: 'right',
    customRender({ record }) {
      if (record.type === 'quarter') {
        return h(BasicTableAction, {
          record,
          actions: unref(actions),
        });
      }
    },
  },
];

const tableOptions = computed(() => ({
  stripe: true,
  rowKey: 'id',
  size: 'small',
  key: tableKey.value,
  pagination: false,
  defaultExpandAllRows: true,
  scroll: { y: 500 },
  columns,
  loading: unref(fetching),
  dataSource: unref(tableData),
}));

const actions = computed(() => [
  {
    text: '查看',
    isShow: (record) => record.status === 120 || record.status === 140 || record.status === 130,
    onClick(record) {
      router.push({
        name: 'LaborCostDetail',
        params: {
          id: record.accepttanceId,
        },
      });
    },
  },
  {
    text: '修改',
    isShow: (record) => record.status === 140,
    onClick(record) {
      openCheckLaborCostDrawer({
        ...record,
        containerRef,
        contractName: basicContractEmployerPlan.contractName,
        contractNumber: basicContractEmployerPlan.contractNumber,
      }, () => {
        reqTable();
      });
    },
  },
  {
    text: '申请验收',
    isShow: (record) => record.status === 101,
    onClick(record) {
      openCheckLaborCostDrawer({
        ...record,
        containerRef,
        contractName: basicContractEmployerPlan.contractName,
        contractNumber: basicContractEmployerPlan.contractNumber,
      }, () => {
        reqTable();
      });
    },
  },
]);

const containerRef = ref();
</script>

<template>
  <div ref="containerRef" />
  <BasicCard
    title="人力成本数据统计"
  >
    <template #titleRight>
      <DatePicker
        v-model:value="year"
        :allowClear="false"
        picker="year"
        :disabled="fetching"
        valueFormat="YYYY"
        @change="reqTable()"
      />
    </template>
    <STable
      v-bind="tableOptions"
    />
  </BasicCard>
</template>

<style scoped lang="less">

</style>
