import { defineStore } from 'pinia';
import { listenerRouteChange } from '/@/logics/mitt/routeChange';

interface PageTitle {
  title?: string;
  style?: string;
}

export const usePageTitleStore = defineStore({
  id: 'page-title',
  state: (): any => ({
    pageTitle: null,
    nowRoute: null,
    show: true,
  }),
  getters: {
    getPageTitle() {
      const pageTitle: PageTitle = {
        title: '',
        style: undefined,
      };
      if (this.pageTitle) {
        Object.assign(pageTitle, {
          ...this.pageTitle,
        });
      } else if (this.nowRoute) {
        const {
          meta: { title },
        } = this.nowRoute;
        Object.assign(pageTitle, {
          title,
        });
      }
      return pageTitle;
    },
  },
  actions: {
    setPageTitle(pageTitle: PageTitle | null) {
      this.pageTitle = pageTitle;
    },
    setNowRoute(route) {
      this.nowRoute = route;
    },
    pageTitleInit() {
      listenerRouteChange((route) => {
        this.setPageTitle(null);
        this.setNowRoute(route);
      });
    },
  },
});
