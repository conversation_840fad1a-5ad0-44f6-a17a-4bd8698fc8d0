import { IOrionTableOptions, DataStatusTag } from 'lyra-component-vue3';
import Api from '/@/api';
import { h } from 'vue';
import { IGetConfigProps } from '.';
export default (props: IGetConfigProps): IOrionTableOptions => ({
  api() {
    return new Api('/pms/projectOverview/zgh/projectLife/postEvaluation').fetch({}, props.projectId, 'GET');
  },
  columns: [
    {
      title: '项目编码',
      dataIndex: 'projectNumber',
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
    },
    {
      title: '项目营收(万)',
      dataIndex: 'projectRevenue',
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 100,
      fixed: 'right',
    },
  ],
  actions: [

    {
      text: '查看',
      onClick(record) {
        // props.router.push('ProjectStatus');
        props.router.push({
          name: 'ProjectStatus',
          params: {
            id: record.id,
          },
        });
      },
    },

  ],
});
