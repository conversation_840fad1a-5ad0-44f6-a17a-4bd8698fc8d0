<script setup lang="ts">
import { IDataStatus, Layout3 } from 'lyra-component-vue3';
import {
  computed, onMounted, provide, reactive, ref, Ref, watchEffect,
} from 'vue';
import { useRoute } from 'vue-router';
import Api from '/@/api';
import WorkInfo from './components/WorkInfo.vue';
import AuthorizationInfo from './components/AuthorizationInfo.vue';
import MaterialInfo from './components/MaterialInfo.vue';
import RiskInfo from './components/RiskInfo.vue';
import StudyReview from './components/StudyReview.vue';
import WorkPackage from './components/WorkPackage.vue';
import { useDetailsCode } from '/@/views/pms/dailyWork/pages/hooks';

interface DetailsDataType {
  id: string,
  name: string,
  className: string,
  projectCode: string,
  ownerName?: string | undefined,
  status?: string | undefined | number,
  dataStatus?: IDataStatus | undefined,

  [propName: string]: any
}

const route = useRoute();
const actionId: Ref<string | null> = ref('');
const dataId = computed(() => route.params?.id);
const { powerCodePrefix } = useDetailsCode(route.name as string);
provide('powerCodePrefix', powerCodePrefix);
const detailsData: DetailsDataType = reactive({
  id: '',
  name: '',
  className: '',
  projectCode: '',
});
provide('detailsData', detailsData);
const projectData = computed(() => ({
  name: detailsData.name,
  projectCode: detailsData?.number,
  dataStatus: detailsData?.dataStatus,
}));

const menuData = computed(() => [
  {
    id: 'work',
    name: '作业信息',
    powerCode: `${powerCodePrefix.value}_container_01`,
  },
  {
    id: 'authorization',
    name: '授权管理',
    powerCode: `${powerCodePrefix.value}_container_02`,
  },
  {
    id: 'material',
    name: '物资管理',
    powerCode: `${powerCodePrefix.value}_container_03`,
  },
  {
    id: 'risk',
    name: '风险措施',
    powerCode: `${powerCodePrefix.value}_container_04`,
  },
  {
    id: 'package',
    name: '作业管理',
    isShow: () => detailsData?.norO === 'O',
    powerCode: `${powerCodePrefix.value}_container_05`,
  },
  {
    id: 'study',
    name: '作业管理',
    isShow: () => detailsData?.norO === 'N',
    powerCode: `${powerCodePrefix.value}_container_06`,
  },
]);

function menuChange({ id }) {
  actionId.value = id;
}

watchEffect(() => {
  if (!actionId.value || (actionId.value && menuData.value.findIndex((item) => item.id === actionId.value) === -1)) {
    actionId.value = menuData.value?.[0]?.id;
  }
});

onMounted(() => {
  getDetails();
});

const powerData: Ref<any[]> = ref(undefined);
provide('powerData', powerData);
const loading: Ref<boolean> = ref(false);

async function getDetails() {
  loading.value = true;
  try {
    const result: Record<string, any> = await new Api('/pms/job-manage').fetch({
      pageCode: route.name,
    }, dataId.value, 'GET');
    Object.keys(result).forEach((key) => {
      detailsData[key] = result[key];
    });
    powerData.value = result?.detailAuthList;
  } finally {
    loading.value = false;
  }
}

</script>

<template>
  <Layout3
    v-loading="loading"
    v-get-power="{powerData}"
    :defaultActionId="actionId"
    :menuData="menuData"
    :projectData="projectData"
    :type="2"
    @menuChange="menuChange"
  >
    <template v-if="detailsData?.id">
      <WorkInfo v-if="actionId==='work'" />
      <AuthorizationInfo
        v-if="actionId==='authorization'"
      />
      <MaterialInfo v-if="actionId==='material'" />
      <RiskInfo v-if="actionId==='risk'" />
      <StudyReview v-if="actionId==='study'" />
      <WorkPackage v-if="actionId==='package'" />
    </template>
  </Layout3>
</template>

<style scoped lang="less">

</style>
