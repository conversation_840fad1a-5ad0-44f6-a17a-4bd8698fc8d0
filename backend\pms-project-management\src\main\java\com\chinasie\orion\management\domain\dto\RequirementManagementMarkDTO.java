package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * RequirementManagementMark DTO对象
 *
 * <AUTHOR>
 * @since 2024-09-05 20:04:54
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "RequirementManagementMarkDTO对象", description = "需求管理标注")
@Data
@ExcelIgnoreUnannotated
public class RequirementManagementMarkDTO extends ObjectDTO implements Serializable {

    /**
     * 需求管理id，关联 pms_requirement_mangement id
     */
    @ApiModelProperty(value = "需求管理id，关联 pms_requirement_mangement id")
    @ExcelProperty(value = "需求管理id，关联 pms_requirement_mangement id ", index = 0)
    private String requirementId;

}
