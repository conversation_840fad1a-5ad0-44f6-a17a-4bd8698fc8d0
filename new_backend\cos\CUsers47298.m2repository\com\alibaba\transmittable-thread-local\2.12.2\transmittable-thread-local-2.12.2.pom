<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.alibaba</groupId>
  <artifactId>transmittable-thread-local</artifactId>
  <name>TransmittableThreadLocal(TTL)</name>
  <version>2.12.2</version>
  <description>&#x1f4cc; The missing Java™ std lib(simple &amp; 0-dependency) for framework/middleware,
		provide an enhanced InheritableThreadLocal that transmits ThreadLocal value between threads even using thread pooling components.</description>
  <url>https://github.com/alibaba/transmittable-thread-local</url>
  <issueManagement>
    <system>GitHub Issues</system>
    <url>https://github.com/alibaba/transmittable-thread-local/issues</url>
  </issueManagement>
  <inceptionYear>2013</inceptionYear>
  <developers>
    <developer>
      <id>oldratlee</id>
      <name>Jerry Lee</name>
      <email>oldratlee(AT)gmail(DOT)com</email>
      <url>https://github.com/oldratlee</url>
      <organization>Alibaba</organization>
      <organizationUrl>http://www.alibaba.com</organizationUrl>
      <roles>
        <role>Developer</role>
      </roles>
      <timezone>+8</timezone>
    </developer>
    <developer>
      <id>driventokill</id>
      <name>Yang Fang</name>
      <email>snoop(DOT)fy(AT)gmail(DOT)com</email>
      <url>https://github.com/driventokill</url>
      <organization>Alibaba</organization>
      <organizationUrl>http://www.alibaba.com</organizationUrl>
      <roles>
        <role>Developer</role>
      </roles>
      <timezone>+8</timezone>
    </developer>
    <developer>
      <id>wuwen5</id>
      <name>wuwen</name>
      <email>wuwen.55(AT)aliyun(DOT)com</email>
      <url>https://github.com/wuwen5</url>
      <organization>ofpay</organization>
      <organizationUrl>http://www.ofpay.com</organizationUrl>
      <roles>
        <role>Developer</role>
      </roles>
      <timezone>+8</timezone>
    </developer>
    <developer>
      <id>LNAmp</id>
      <name>David Dai</name>
      <email>351450944(AT)qq(DOT)com</email>
      <url>https://github.com/LNAmp</url>
      <organization>Alibaba</organization>
      <organizationUrl>http://www.alibaba.com</organizationUrl>
      <roles>
        <role>Developer</role>
      </roles>
      <timezone>+8</timezone>
    </developer>
  </developers>
  <licenses>
    <license>
      <name>Apache 2</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
      <comments>A business-friendly OSS license</comments>
    </license>
  </licenses>
  <scm>
    <connection>scm:git:**************:alibaba/transmittable-thread-local.git</connection>
    <developerConnection>scm:git:**************:alibaba/transmittable-thread-local.git</developerConnection>
    <url>**************:alibaba/transmittable-thread-local.git</url>
  </scm>
  <organization>
    <name>Alibaba</name>
    <url>http://www.alibaba.com</url>
  </organization>
  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <artifactId>maven-clean-plugin</artifactId>
          <version>3.1.0</version>
        </plugin>
        <plugin>
          <artifactId>maven-resources-plugin</artifactId>
          <version>3.2.0</version>
        </plugin>
        <plugin>
          <artifactId>maven-source-plugin</artifactId>
          <version>3.2.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>3.3.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.22.2</version>
        </plugin>
        <plugin>
          <artifactId>maven-site-plugin</artifactId>
          <version>3.9.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-install-plugin</artifactId>
          <version>2.5.2</version>
        </plugin>
        <plugin>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>2.8.2</version>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-maven-plugin</artifactId>
        <version>${kotlin.version}</version>
        <executions>
          <execution>
            <id>compile</id>
            <goals>
              <goal>compile</goal>
            </goals>
            <configuration>
              <sourceDirs>
                <sourceDir>${project.basedir}/src/main/java</sourceDir>
              </sourceDirs>
            </configuration>
          </execution>
          <execution>
            <id>test-compile</id>
            <goals>
              <goal>test-compile</goal>
            </goals>
            <configuration>
              <sourceDirs>
                <sourceDir>${project.basedir}/src/test/java</sourceDir>
              </sourceDirs>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.1</version>
        <executions>
          <execution>
            <id>default-compile</id>
            <phase>none</phase>
          </execution>
          <execution>
            <id>default-testCompile</id>
            <phase>none</phase>
          </execution>
          <execution>
            <id>java-compile</id>
            <phase>compile</phase>
            <goals>
              <goal>compile</goal>
            </goals>
          </execution>
          <execution>
            <id>java-test-compile</id>
            <phase>test-compile</phase>
            <goals>
              <goal>testCompile</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <source>1.6</source>
          <target>1.6</target>
          <encoding>${project.build.sourceEncoding}</encoding>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <version>3.2.0</version>
        <configuration>
          <archive>
            <manifestEntries>
              <Premain-Class>com.alibaba.ttl.threadpool.agent.TtlAgent</Premain-Class>
              <Boot-Class-Path>${project.build.finalName}.jar</Boot-Class-Path>
              <Can-Redefine-Classes>false</Can-Redefine-Classes>
              <Can-Retransform-Classes>true</Can-Retransform-Classes>
              <Can-Set-Native-Method-Prefix>false</Can-Set-Native-Method-Prefix>
            </manifestEntries>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.2.4</version>
        <executions>
          <execution>
            <id>shade-when-package</id>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <relocations>
                <relocation>
                  <pattern>javassist</pattern>
                  <shadedPattern>com.alibaba.ttl.threadpool.agent.internal.javassist</shadedPattern>
                </relocation>
              </relocations>
              <artifactSet>
                <includes>
                  <include>org.javassist:javassist</include>
                </includes>
              </artifactSet>
              <filters>
                <filter>
                  <artifact>org.javassist:javassist</artifact>
                  <excludes>
                    <exclude>META-INF/MANIFEST.MF</exclude>
                  </excludes>
                </filter>
              </filters>
              <shadeSourcesContent>true</shadeSourcesContent>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.sonatype.plugins</groupId>
        <artifactId>nexus-staging-maven-plugin</artifactId>
        <version>1.6.8</version>
        <extensions>true</extensions>
        <configuration>
          <serverId>ossrh</serverId>
          <nexusUrl>https://oss.sonatype.org/</nexusUrl>
          <autoReleaseAfterClose>true</autoReleaseAfterClose>
        </configuration>
      </plugin>
      <plugin>
        <groupId>com.github.spotbugs</groupId>
        <artifactId>spotbugs-maven-plugin</artifactId>
        <version>4.4.1</version>
      </plugin>
      <plugin>
        <artifactId>maven-enforcer-plugin</artifactId>
        <version>3.0.0</version>
        <executions>
          <execution>
            <id>enforce-maven</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireMavenVersion>
                  <version>3.3.9</version>
                </requireMavenVersion>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>versions-maven-plugin</artifactId>
        <version>2.8.1</version>
        <configuration>
          <rulesUri>file://${project.basedir}/src/versions-rules.xml</rulesUri>
          <generateBackupPoms>false</generateBackupPoms>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>gen-src</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-source-plugin</artifactId>
            <version>3.2.1</version>
            <executions>
              <execution>
                <id>attach-sources</id>
                <goals>
                  <goal>jar</goal>
                  <goal>jar-no-fork</goal>
                </goals>
              </execution>
            </executions>
            <inherited>true</inherited>
          </plugin>
          <plugin>
            <artifactId>maven-shade-plugin</artifactId>
            <executions>
              <execution>
                <id>shade-when-package</id>
                <configuration>
                  <createSourcesJar>true</createSourcesJar>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>gen-javadoc</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>3.3.1</version>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
                <configuration>
                  <source>8</source>
                  <show>protected</show>
                  <charset>UTF-8</charset>
                  <encoding>UTF-8</encoding>
                  <docencoding>UTF-8</docencoding>
                  <excludePackageNames>*.internal.*</excludePackageNames>
                  <sourceFileExcludes>
                    <sourceFilesourceFileExclude>com/alibaba/ttl/TtlEnhanced.java</sourceFilesourceFileExclude>
                  </sourceFileExcludes>
                  <overview>src/api/overview.html</overview>
                  <isOffline>true</isOffline>
                  <offlineLinks>
                    <offlineLink>
                      <url>https://docs.oracle.com/javase/10/docs/api/</url>
                      <location>/Users/<USER>/codes/open/transmittable-thread-local/src/package-list/java/</location>
                    </offlineLink>
                    <offlineLink>
                      <url>https://static.javadoc.io/com.github.spotbugs/spotbugs-annotations/3.1.12/</url>
                      <location>/Users/<USER>/codes/open/transmittable-thread-local/src/package-list/spotbugs-annotations/</location>
                    </offlineLink>
                  </offlineLinks>
                  <additionalJOptions>
                    <additionalJOption>--no-module-directories</additionalJOption>
                    <additionalJOption>-html5</additionalJOption>
                    <additionalJOption>-quiet</additionalJOption>
                    <additionalJOption>-J-Duser.language=en</additionalJOption>
                    <additionalJOption>-J-Duser.country=US</additionalJOption>
                    <additionalJOption>-Xdoclint:-missing</additionalJOption>
                  </additionalJOptions>
                </configuration>
              </execution>
              <execution>
                <id>attach-javadoc</id>
                <goals>
                  <goal>jar</goal>
                </goals>
                <configuration>
                  <source>8</source>
                  <show>protected</show>
                  <charset>UTF-8</charset>
                  <encoding>UTF-8</encoding>
                  <docencoding>UTF-8</docencoding>
                  <excludePackageNames>*.internal.*</excludePackageNames>
                  <sourceFileExcludes>
                    <sourceFilesourceFileExclude>com/alibaba/ttl/TtlEnhanced.java</sourceFilesourceFileExclude>
                  </sourceFileExcludes>
                  <overview>src/api/overview.html</overview>
                  <isOffline>true</isOffline>
                  <offlineLinks>
                    <offlineLink>
                      <url>https://docs.oracle.com/javase/10/docs/api/</url>
                      <location>/Users/<USER>/codes/open/transmittable-thread-local/src/package-list/java/</location>
                    </offlineLink>
                    <offlineLink>
                      <url>https://static.javadoc.io/com.github.spotbugs/spotbugs-annotations/3.1.12/</url>
                      <location>/Users/<USER>/codes/open/transmittable-thread-local/src/package-list/spotbugs-annotations/</location>
                    </offlineLink>
                  </offlineLinks>
                  <additionalJOptions>
                    <additionalJOption>--no-module-directories</additionalJOption>
                    <additionalJOption>-html5</additionalJOption>
                    <additionalJOption>-quiet</additionalJOption>
                    <additionalJOption>-J-Duser.language=en</additionalJOption>
                    <additionalJOption>-J-Duser.country=US</additionalJOption>
                    <additionalJOption>-Xdoclint:-missing</additionalJOption>
                  </additionalJOptions>
                </configuration>
              </execution>
            </executions>
            <inherited>true</inherited>
            <configuration>
              <source>8</source>
              <show>protected</show>
              <charset>UTF-8</charset>
              <encoding>UTF-8</encoding>
              <docencoding>UTF-8</docencoding>
              <excludePackageNames>*.internal.*</excludePackageNames>
              <sourceFileExcludes>
                <sourceFilesourceFileExclude>com/alibaba/ttl/TtlEnhanced.java</sourceFilesourceFileExclude>
              </sourceFileExcludes>
              <overview>src/api/overview.html</overview>
              <isOffline>true</isOffline>
              <offlineLinks>
                <offlineLink>
                  <url>https://docs.oracle.com/javase/10/docs/api/</url>
                  <location>/Users/<USER>/codes/open/transmittable-thread-local/src/package-list/java/</location>
                </offlineLink>
                <offlineLink>
                  <url>https://static.javadoc.io/com.github.spotbugs/spotbugs-annotations/3.1.12/</url>
                  <location>/Users/<USER>/codes/open/transmittable-thread-local/src/package-list/spotbugs-annotations/</location>
                </offlineLink>
              </offlineLinks>
              <additionalJOptions>
                <additionalJOption>--no-module-directories</additionalJOption>
                <additionalJOption>-html5</additionalJOption>
                <additionalJOption>-quiet</additionalJOption>
                <additionalJOption>-J-Duser.language=en</additionalJOption>
                <additionalJOption>-J-Duser.country=US</additionalJOption>
                <additionalJOption>-Xdoclint:-missing</additionalJOption>
              </additionalJOptions>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>gen-sign</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>3.0.1</version>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>lint</id>
      <build>
        <plugins>
          <plugin>
            <groupId>com.github.spotbugs</groupId>
            <artifactId>spotbugs-maven-plugin</artifactId>
            <executions>
              <execution>
                <phase>test-compile</phase>
                <goals>
                  <goal>check</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>gen-git-properties</id>
      <build>
        <plugins>
          <plugin>
            <groupId>io.github.git-commit-id</groupId>
            <artifactId>git-commit-id-maven-plugin</artifactId>
            <version>5.0.0</version>
            <executions>
              <execution>
                <id>get-the-git-infos</id>
                <goals>
                  <goal>revision</goal>
                </goals>
                <configuration>
                  <validationProperties>
                    <validationProperty>
                      <name>validating git dirty</name>
                      <value>${git.dirty}</value>
                      <shouldMatchTo>false</shouldMatchTo>
                    </validationProperty>
                  </validationProperties>
                  <generateGitPropertiesFile>true</generateGitPropertiesFile>
                  <generateGitPropertiesFilename>/Users/<USER>/codes/open/transmittable-thread-local/target/classes/META-INF/scm/com.alibaba/transmittable-thread-local/git.properties</generateGitPropertiesFilename>
                </configuration>
              </execution>
              <execution>
                <id>validate-the-git-infos</id>
                <goals>
                  <goal>validateRevision</goal>
                </goals>
                <configuration>
                  <validationProperties>
                    <validationProperty>
                      <name>validating git dirty</name>
                      <value>${git.dirty}</value>
                      <shouldMatchTo>false</shouldMatchTo>
                    </validationProperty>
                  </validationProperties>
                  <generateGitPropertiesFile>true</generateGitPropertiesFile>
                  <generateGitPropertiesFilename>/Users/<USER>/codes/open/transmittable-thread-local/target/classes/META-INF/scm/com.alibaba/transmittable-thread-local/git.properties</generateGitPropertiesFilename>
                </configuration>
              </execution>
            </executions>
            <configuration>
              <validationProperties>
                <validationProperty>
                  <name>validating git dirty</name>
                  <value>${git.dirty}</value>
                  <shouldMatchTo>false</shouldMatchTo>
                </validationProperty>
              </validationProperties>
              <generateGitPropertiesFile>true</generateGitPropertiesFile>
              <generateGitPropertiesFilename>/Users/<USER>/codes/open/transmittable-thread-local/target/classes/META-INF/scm/com.alibaba/transmittable-thread-local/git.properties</generateGitPropertiesFilename>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>gen-code-cov</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.jacoco</groupId>
            <artifactId>jacoco-maven-plugin</artifactId>
            <version>0.8.7</version>
            <executions>
              <execution>
                <goals>
                  <goal>prepare-agent</goal>
                </goals>
              </execution>
              <execution>
                <id>report</id>
                <phase>test</phase>
                <goals>
                  <goal>report</goal>
                </goals>
                <configuration>
                  <excludes>
                    <exclude>com/alibaba/ttl/threadpool/agent/**/*.class</exclude>
                    <exclude>com/alibaba/ttl/TtlTimerTask.class</exclude>
                  </excludes>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>force-jdk11-when-release</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-enforcer-plugin</artifactId>
            <executions>
              <execution>
                <id>enforce-jdk-versions</id>
                <goals>
                  <goal>enforce</goal>
                </goals>
                <configuration>
                  <rules>
                    <requireJavaVersion>
                      <version>11</version>
                    </requireJavaVersion>
                  </rules>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
  <dependencies>
    <dependency>
      <groupId>com.github.spotbugs</groupId>
      <artifactId>spotbugs-annotations</artifactId>
      <version>4.4.1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>jsr305</artifactId>
      <version>3.0.2</version>
      <scope>runtime</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.13.2</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>hamcrest-core</artifactId>
          <groupId>org.hamcrest</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.jetbrains.kotlin</groupId>
      <artifactId>kotlin-stdlib</artifactId>
      <version>1.4.32</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>kotlin-stdlib-common</artifactId>
          <groupId>org.jetbrains.kotlin</groupId>
        </exclusion>
        <exclusion>
          <artifactId>annotations</artifactId>
          <groupId>org.jetbrains</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.jetbrains.kotlinx</groupId>
      <artifactId>kotlinx-coroutines-core</artifactId>
      <version>1.4.3</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>kotlinx-coroutines-core-jvm</artifactId>
          <groupId>org.jetbrains.kotlinx</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>io.reactivex.rxjava2</groupId>
      <artifactId>rxjava</artifactId>
      <version>2.2.21</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>reactive-streams</artifactId>
          <groupId>org.reactivestreams</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>io.reactivex.rxjava2</groupId>
      <artifactId>rxkotlin</artifactId>
      <version>2.4.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.5</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <distributionManagement>
    <snapshotRepository>
      <id>ossrh</id>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
  </distributionManagement>
  <properties>
    <kotlin.version>1.4.32</kotlin.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <kotlin.coroutine.version>1.4.3</kotlin.coroutine.version>
  </properties>
</project>
