<template>
  <BasicForm @register="registerForm" />
</template>

<script setup lang="ts">
import { reactive, defineExpose } from 'vue';
import { BasicForm, useForm } from 'lyra-component-vue3';
import Api from '/@/api';

const emits = defineEmits([]);
const props = defineProps({
  type: {
    type: String,
    default: '',
  },
});
const [registerForm, { validateFields, validate }] = useForm({
  layout: 'vertical',
  baseColProps: {
    span: 24,
  },
  schemas: [
    {
      field: 'orgId',
      component: 'ApiTreeSelect',
      label: '组织',
      colProps: {
        span: 24,
      },
      rules: [
        {
          required: true,
          type: 'object',
        },
      ],
      componentProps: {
        api: () => new Api('/pmi/organization/business/org/tree').fetch([], '', 'POST'),
        labelField: 'name',
        valueField: 'id',
        labelInValue: true,
      },
      ifShow() {
        return props.type === 'org';
      },
    },
    {
      field: 'roleId',
      component: 'ApiTreeSelect',
      label: '角色',
      colProps: {
        span: 24,
      },
      rules: [
        {
          required: true,
          type: 'object',
        },
      ],
      componentProps: {
        api: () => new Api('/pmi/role/page').fetch({
          pageNum: 1,
          pageSize: 999999,
        }, '', 'POST').then((item) => item.content),
        labelField: 'name',
        valueField: 'id',
        labelInValue: true,
      },
      ifShow() {
        return props.type === 'role';
      },
    },
  ],
});

async function getData() {
  const res = await validateFields();
  return res;
}

defineExpose({
  getData,
});
</script>
