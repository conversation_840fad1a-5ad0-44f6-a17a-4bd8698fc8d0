<script setup lang="ts">
import {
  BasicForm, openModal, useForm,
} from 'lyra-component-vue3';
import {
  h, onMounted, reactive, ref,
} from 'vue';

import { projectMaterialPlanById } from '/@/views/pms/api/projectMaterialPlan';
import { message } from 'ant-design-vue';
import MaterialCode from './MaterialCode.vue';
interface CurrentValues {
  materialId:string,
  procurementCycle:string
}
const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});
const loading = ref(false);
const currentValues:CurrentValues = reactive({
  materialId: '',
  procurementCycle: '',
});
const validatePlanNum = async (_, value: number|null|undefined) => {
  if (value === 0) {
    return Promise.reject('计划数量不能为0');
  }
  if (!value) {
    return Promise.reject('计划数量不能为空');
  }
  if (value < 0) {
    return Promise.reject('计划数量不能为负数');
  }
  const regex = /^\d+(\.\d{1,3})?$/;
  if (!regex.test(`${value}`)) {
    return Promise.reject('计划数量最多保留三位小数');
  }
  return Promise.resolve();
};
const [register, formMethods] = useForm({
  schemas: [
    {
      field: 'number',
      component: 'Input',
      label: '物料编码',
      colProps: {
        span: 12,
      },
      required: true,
      componentProps: {
        readonly: true,
        placeholder: '请选择物料编码',
        onClick: () => handleClickTaskPlan(),
        addonAfter: h(
          'span',
          { onClick: () => handleClickTaskPlan() },
          '请选择',
        ),
        onChange: () => handleChangeTaskPlan(),
      },
    },
    {
      field: 'baseUnit',
      component: 'Input',
      label: '基本单位',
      required: false,
      colProps: {
        span: 12,
      },
      componentProps: {
        disabled: true,
      },
    },
    {
      field: 'remark',
      component: 'InputTextArea',
      label: '物料描述',
      required: false,
      colProps: {
        span: 24,
      },
      componentProps: {
        rows: 4,
        showCount: true,
        maxlength: 1000,
        disabled: true,
      },
    },
    {
      field: 'planUseTime',
      component: 'DatePicker',
      colProps: {
        span: 12,
      },
      label: '计划使用时间',
      componentProps: {
        style: { width: '100%' },
        valueFormat: 'YYYY-MM-DD',
      },
    },
    {
      field: 'planNum',
      component: 'InputNumber',
      colProps: {
        span: 12,
      },
      rules: [
        {
          required: true,
          validator: validatePlanNum,
        },
      ],
      defaultValue: 1,
      label: '计划数量',
      componentProps: {
        placeholder: '请输入计划数量',
      },
    },
  ],
  layout: 'vertical',
  baseColProps: {
    span: 12,
  },
});

async function handleChangeTaskPlan() {
  message.info('请选择');
  await formMethods.setFieldsValue({
    number: '',
    baseUnit: '',
    remark: '',
  });
  currentValues.materialId = '';
  currentValues.procurementCycle = '';
}

async function getValues() {
  const values = await formMethods.validate();
  return {
    ...values,
    materialId: currentValues.materialId,
    procurementCycle: currentValues.procurementCycle,
  };
}
function handleClickTaskPlan() {
  const refModal = ref();
  openModal({
    title: '选择物料编码',
    width: 1100,
    content(h) {
      return h(MaterialCode, {
        ref: refModal,
        type: 'radio',
      });
    },
    async onOk() {
      const { isSelectedAndGetData } = refModal.value;
      const values = await isSelectedAndGetData();
      const {
        id, number, basicUnit, remark, procurementCycle,
      } = values[0] || {};
      await formMethods.setFieldsValue({
        number,
        baseUnit: basicUnit,
        remark,
      });
      currentValues.materialId = id;
      currentValues.procurementCycle = procurementCycle;
    },
  });
}
onMounted(async () => {
  if (props.id) {
    try {
      loading.value = true;
      const result = await projectMaterialPlanById(props.id);
      formMethods.setFieldsValue({
        ...result,
      });
      currentValues.materialId = result.id;
      currentValues.procurementCycle = result.procurementCycle;
    } finally {
      loading.value = false;
    }
  }
});

defineExpose({
  getValues,
});
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">

</style>
