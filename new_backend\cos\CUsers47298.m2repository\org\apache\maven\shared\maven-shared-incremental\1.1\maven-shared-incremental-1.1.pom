<?xml version='1.0' encoding='UTF-8'?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements.  See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership.  The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied.  See the License for the
  specific language governing permissions and limitations
  under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.apache.maven.shared</groupId>
    <artifactId>maven-shared-components</artifactId>
    <version>19</version>
    <relativePath>../maven-shared-components/pom.xml</relativePath>
  </parent>

  <artifactId>maven-shared-incremental</artifactId>
  <version>1.1</version>
  <name>Maven Incremental Build support utilities</name>
  <description>
    Various utility classes and plexus components for supporting 
    incremental build functionality in maven plugins.
  </description>

  <scm>
    <connection>scm:svn:http://svn.apache.org/repos/asf/maven/shared/tags/maven-shared-incremental-1.1</connection>
    <developerConnection>scm:svn:https://svn.apache.org/repos/asf/maven/shared/tags/maven-shared-incremental-1.1</developerConnection>
    <url>http://svn.apache.org/viewvc/maven/shared/tags/maven-shared-incremental-1.1</url>
  </scm>

  <issueManagement>
    <system>jira</system>
    <url>https://jira.codehaus.org/browse/MSHARED/component/15650</url>
  </issueManagement>

  <properties>
    <mavenVersion>2.2.1</mavenVersion>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-plugin-api</artifactId>
      <version>${mavenVersion}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-core</artifactId>
      <version>${mavenVersion}</version>
      <exclusions>
        <exclusion>
          <groupId>org.apache.maven.reporting</groupId>
          <artifactId>maven-reporting-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.maven.wagon</groupId>
          <artifactId>wagon-file</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.maven.wagon</groupId>
          <artifactId>wagon-http-lightweight</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.maven.wagon</groupId>
          <artifactId>wagon-ssh</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.maven.wagon</groupId>
          <artifactId>wagon-ssh-external</artifactId>
        </exclusion>
        <exclusion>
          <groupId>commons-cli</groupId>
          <artifactId>commons-cli</artifactId>
        </exclusion>
        <exclusion>
          <groupId>classworlds</groupId>
          <artifactId>classworlds</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.codehaus.plexus</groupId>
          <artifactId>plexus-container-default</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.codehaus.plexus</groupId>
          <artifactId>plexus-interactivity-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.shared</groupId>
      <artifactId>maven-shared-utils</artifactId>
      <version>0.1</version>
    </dependency>

    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-component-annotations</artifactId>
      <version>1.5.5</version>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-component-api</artifactId>
      <version>1.0-alpha-16</version>
      <scope>provided</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-component-metadata</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>generate-metadata</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

</project>
