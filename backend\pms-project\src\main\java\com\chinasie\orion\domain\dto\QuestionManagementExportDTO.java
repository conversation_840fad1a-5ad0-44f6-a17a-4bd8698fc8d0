package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.poi.ss.usermodel.RichTextString;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionManagementExportDTO {

    @ExcelProperty(value = "序号", index = 0)
    private Integer order;

    @ApiModelProperty(value = "标题")
    @ExcelProperty(value = "标题 ", index = 1)
    private String name;

    @ApiModelProperty(value = "状态")
    @ExcelProperty(value = "状态",index = 2)
    private String statusName;

    @ApiModelProperty(value = "提出日期")
    @ExcelProperty(value = "提出日期 ", index = 3)
    private Date proposedTime;

    @ApiModelProperty(value = "负责人名称")
    @ExcelProperty(value = "负责人名称 ", index = 4)
    private String principalName;

    @ApiModelProperty(value = "提出人名称")
    @ExcelProperty(value = "提出人名称 ", index = 5)
    private String exhibitorName;

    @ApiModelProperty(value = "编号")
    @ExcelProperty(value = "编号", index = 6)
    private String number;

//    @ApiModelProperty(value = "产品编码")
//    private String productNumber;
//
//    @ApiModelProperty(value = "物料编码")
//    private String materialNumber;
//
//    @ApiModelProperty(value = "生产订单")
//    private String productionOrders;
//
//    @ApiModelProperty(value = "产品编号（SN）")
//    private String productNumberSn;
//
//    @ApiModelProperty(value = "问题内容")
//    private RichTextString content;
//
//    @ApiModelProperty(value = "阶段名称")
//    private String stageName;
//
//    @ApiModelProperty(value = "过程环节名称")
//    private String processLinkName;
//
//    @ApiModelProperty(value = "过程分类名称")
//    private String processClassifiName;
//
//    @ApiModelProperty(value = "问题现象一级分类名称")
//    private String problemPhenomenonOneName;
//
//    @ApiModelProperty(value = "问题现象二级分类名称")
//    private String problemPhenomenonTwoName;
//
//    @ApiModelProperty(value = "问题现象三级分类名称")
//    private String problemPhenomenonThName;

    @ApiModelProperty(value = "问题等级分类名称")
    @ExcelProperty(value = "问题等级分类名称 ", index = 7)
    private String problemLevelName;

    @ApiModelProperty(value = "优先级名称")
    @ExcelProperty(value = "优先级名称 ", index = 8)
    private String priorityLevelName;

    @ApiModelProperty(value = "期望完成时间")
    @ExcelProperty(value = "期望完成时间 ", index = 9)
    private Date predictEndTime;
}
