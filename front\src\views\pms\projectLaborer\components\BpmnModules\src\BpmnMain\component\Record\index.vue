<template>
  <div class="list-wrap">
    <div class="title">
      历史记录
    </div>
    <div
      v-loading="loadingStatus"
      class="flow-container"
    >
      <a-table
        :columns="columns"
        :data-source="tableData"
        rowKey="activityId"
        :pagination="false"
      />
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent, inject, onMounted, reactive, toRefs,
} from 'vue';
import { Table } from 'ant-design-vue';
import Api from '/@/api/index';
import { workflowApi } from '../../../util/config';

export default defineComponent({
  name: '',
  components: {
    ATable: Table,
  },
  setup() {
    const {
      userId,
      menuActionItem: { procDefId, procInstId, id: prearrangeId },
    } = inject('bpmnModuleData')?.value;
    const state: any = reactive({
      loadingStatus: false,
      tableData: [],
      columns: [
        {
          title: '审批环节',
          dataIndex: 'activityName',
          key: 'activityName',
        },
        {
          title: '负责人',
          dataIndex: 'assigneeName',
          key: 'assigneeName',
        },
        {
          title: '接收时间',
          dataIndex: 'startTime',
          key: 'startTime',
        },
        {
          title: '审批时间',
          key: 'endTime',
          dataIndex: 'endTime',
        },
        {
          title: '审批状态',
          key: 'commentStatus',
          dataIndex: 'commentStatus',
        },
        {
          title: '审批意见',
          key: 'comment',
          dataIndex: 'comment',
        },
      ],
    });

    onMounted(() => {
      init();
    });

    function init() {
      load();
    }

    function load() {
      state.loadingStatus = true;
      new Api(workflowApi)
        .fetch(
          {
            userId,
            procInstId,
          },
          'act-inst-detail/journal',
          'GET',
        )
        .then((list) => {
          state.tableData = list;
        })
        .finally(() => {
          state.loadingStatus = false;
        });
    }

    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang="less">
  .list-wrap {
    > .title {
      height: 40px;
      line-height: 40px;
      padding: 0 15px 0 35px;
      box-sizing: border-box;
      background: #f0f2f5;
      position: relative;

      &:after {
        content: '';
        width: 0;
        height: 0;
        border-width: 6px;
        border-style: solid;
        border-color: #969eb4 transparent transparent transparent;
        position: absolute;
        left: 15px;
        top: 18px;
      }
    }
  }
</style>
