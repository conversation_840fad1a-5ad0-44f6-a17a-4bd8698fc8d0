<script setup lang="ts">
import { BasicButton, openModal ,Icon,useFullscreen} from 'lyra-component-vue3';
import {
  DatePicker, InputSearch, message, Modal,
} from 'ant-design-vue';
import {
  nextTick, onMounted, ref, Ref, unref,watchEffect
} from 'vue';
import dayjs from 'dayjs';
import Api from '/@/api';
import TableView from './TableView.vue';
import axios from 'axios';
import person from './person.json'


const fullscreenRef: Ref = ref();
const { toggle, isFullscreen } = useFullscreen(fullscreenRef);
watchEffect(() => {
  // console.log(isFullscreen.value);
});


const props = defineProps<{
  type: 'person' | 'material' | 'personOverlap' | 'materialOverlap'
  repairRound: string
}>();

const config = {
  person: {
    api: 'person',
    vo: 'personSourceVOList',
    save: 'person',
  },
  material: {
    api: 'material',
    vo: 'materialSourceVOList',
    save: 'material',
  },
  personOverlap: {
    api: 'person/overlap',
    vo: 'personSourceVOList',
    save: 'person',
  },
  materialOverlap: {
    api: 'material/overlap',
    vo: 'materialSourceVOList',
    save: 'material',
  },
};

const year: Ref = ref(dayjs().format('YYYY'));

const legend = ref([
  {
    label: '计划进出基地日期',
    color: '#AFE8C5',
    class: 'in-out',
    disabled: false,
  },
  {
    label: '重叠天数',
    color: '#FF928A',
    class: 'overlap',
    disabled: false,
  },
]);

const keyword: Ref<string> = ref();

// 当前季度索引
const quarterKey: Ref<number> = ref(0);
const quarterData: Ref<Record<string, any>[]> = ref([
]);

onMounted(async () => {
  // await getDatas()
  await getData();
  // updateQuarter();
});

function updateQuarter() {
  const index = quarterData.value?.findIndex((item) => {
    switch (props.type) {
      case 'person':
        return item.personCount > 0;
      case 'material':
        return item.materialCount > 0;
      case 'personOverlap':
      case 'materialOverlap':
        return item.overlapDayCount > 0;
    }
  });
  if (Math.max(0, index) !== quarterKey.value) {
    getData();
  }
}

const data: Ref<any[]> = ref([]);
const minDate: Ref<number> = ref();
const loading: Ref<boolean> = ref(false);





async function getDatas() {
  axios.post('http://192.168.0.105:8700/personAllocation/getInfo', {
    repairRound: props?.repairRound,
    queryType: 1,
    status: 1,
  })
    .then(function (response) {

      data.value = [response.data.result];
      // data.value =[person.result];
      data.value = data.value.map(item => {
        const { id, baseName, resourceAllocationSpecialtyList, ...remainingProps } = item;
        return {
          ...item,
          key: item.baseCode,
          name: item.repairName,
          children: item.resourceAllocationSpecialtyList?.map(ele => {
            let { teamsList, ...remainingProps } = ele;
            return {
              ...ele,
              name: ele.name,
              key: ele.id,
              children: ele.children?.map(el => {
                let { personsList, ...remainingProps } = el;
                return {
                  ...el,
                  name: el.name,
                  key: el.id,
                  children: el.children?.map(value => {
                    let { personRepairRoundInfos, ...remainingProps } = value;
                    // let rowSpanvalue=0
                    // if(index==0){
                    //   rowSpanvalue=arr.length
                    // }else{
                    //   rowSpanvalue=0
                    // }
                    return {
                      ...value,
                      name: value.name,
                      isBasePermanent: value.isBasePermanent == true ? '是' : '否',
                      children: value.children?.map((cc, index, arr) => {
                        let { rowSpan, isBasePermanent, sectionTime, sectionTime2, ...remainingProps } = cc;
                        return {
                          ...cc,
                          name: cc.name,
                          key: cc.id,
                        }
                      })
                    }
                  })
                }
              })
            }
          })
        };
      });

    })
    .catch(function (error) {
      // 处理错误情况
      console.log(error);
    });

}



const urlParams = ref()
const queryType = ref()
const status = ref('')
async function getData() {
  loading.value = true;
  switch (props.type) {
    case 'person':
      urlParams.value = '/pms/personAllocation/getInfo';
      queryType.value = 0;
      status.value = '1';
      break;
    case 'material':
      urlParams.value = '/pms/personAllocation/getInfo';
      queryType.value = 1;
      status.value = '1';
      break;
    case 'personOverlap':
      urlParams.value = '/pms/personAllocation/getInfo';
      queryType.value = 0;
      status.value = '0';
      break;
    case 'materialOverlap':
      urlParams.value = '/pms/personAllocation/getInfo';
      queryType.value = 1;
      status.value = '0';
      break;
    default:
  }


  try {
    const result = await new Api(`${urlParams.value}`).fetch({
      keyword: unref(keyword),
      repairRound: props?.repairRound,
      queryType: queryType.value,
      yearNum: year.value,
      status: status.value,
    }, '', 'POST');
    data.value = [];
    data.value.push(result)
    data.value = data.value.map(item => {
      const { id, baseName, resourceAllocationSpecialtyList, ...remainingProps } = item;
      return {
        ...item,
        key: item.baseCode,
        name: item.repairName,
        children: item.resourceAllocationSpecialtyList?.map(ele => {
          let { teamsList, ...remainingProps } = ele;
          return {
            ...ele,
            name: ele.name,
            key: ele.id,
            children: ele.children?.map(el => {
              let { personsList, ...remainingProps } = el;
              return {
                ...el,
                name: el.name,
                key: el.id,
                children: el.children?.map(value => {
                  let { personRepairRoundInfos, ...remainingProps } = value;
                  // let rowSpanvalue=0
                  // if(index==0){
                  //   rowSpanvalue=arr.length
                  // }else{
                  //   rowSpanvalue=0
                  // }
                  return {
                    ...value,
                    name: value.name,
                    isBasePermanent: value.isBasePermanent == true ? '是' : '否',
                    children: value.children?.map((cc, index, arr) => {
                      let { rowSpan, isBasePermanent, sectionTime, sectionTime2, ...remainingProps } = cc;
                      return {
                        ...cc,
                        name: cc.name,
                        key: cc.id,
                      }
                    })
                  }
                })
              }
            })
          }
        })
      };
    });
  } finally {
    loading.value = false;
  }
}

async function changeDate() {
  if (!isEdit.value) {
    await getData();
    updateQuarter();
  } else {
    quarterKey.value = 0;
  }
}

async function handleChange(index: number) {
  if (!isEdit.value && quarterKey.value !== index) {
    await getData();
  } else {
    quarterKey.value = index;
    await nextTick();
    tableRef.value.quarterChange();
  }
}

const isEdit: Ref<boolean> = ref(false);

function changeEditMode(flag: boolean) {
  isEdit.value = flag;
}

const tableRef: Ref = ref();

const editData = ref([])

//修改的数据
const handleData = (value) => {
  editData.value = value.value.map((item) => (item))
}


function handleToolButton(type: string) {
  switch (type) {
    case 'edit':
      changeEditMode(true);
      break;
    case 'save':
      Modal.confirm({
        title: '系统提示',
        content: '是否确认保存信息',
        async onOk() {
          // axios.post('http://192.168.0.104:8700/personAllocation/editDate',{
          //         queryType:0,
          //         updateTimeDTOList:editData.value.reverse(),
          //       }).then(function (response) {})
          if (editData.value) {
            loading.value = true;
            try {
              await new Api(`/pms/personAllocation/editDate`).fetch(
                {
                  queryType: queryType.value,
                  updateTimeDTOList: editData.value.reverse(),
                }, '', 'POST');
              message.success('操作成功！');
              changeEditMode(false);
              getData();
            } finally {
              loading.value = false;
            }
          } else {
            changeEditMode(false);
          }
        },
      });
      break;
    case 'back':
      Modal.confirm({
        title: '系统提示',
        content: '是否确认放弃编辑内容',
        onOk() {
          changeEditMode(false);
          getData();
        },
      });
      break;
  }
}

function closeModal() {
  openModal.closeAll();
}



</script>

<template>
  <div ref="fullscreenRef" v-loading="loading" class="table-calendar-container">
    <div class="table-calendar-header">
      <div class="flex flex-ac">
        <BasicButton v-if="!isEdit && data.length" type="primary" @click="handleToolButton('edit')">
          任务编辑
        </BasicButton>
        <template v-else-if="isEdit">
          <BasicButton type="primary" @click="handleToolButton('save')">
            保存
          </BasicButton>
          <BasicButton @click="handleToolButton('back')">
            返回
          </BasicButton>
        </template>
        <div class="legend-wrap">
          <div v-for="(item, index) in legend" :key="index" :class="{ 'disabled-legend': item.disabled }"
            @click="() => item.disabled = !item.disabled">
            <div :class="['color-block', item.class]" :style="{ backgroundColor: item.color }" />
            <div class="label">
              {{ item.label }}
            </div>
          </div>
        </div>
        <DatePicker v-model:value="year" class="mr20 mla" style="width: 100px" picker="year" :allow-clear="false"
          value-format="YYYY" @change="changeDate" />
        <InputSearch v-model:value="keyword" placeholder="请输入关键字" style="width: 200px" @search="getData()" />
        <Icon
          class="ml15 mr15"
          style="font-size: 18px;"
          :icon="isFullscreen?'sie-icon-quxiaoquanping':'sie-icon-quanping'"
          @click="toggle"
        />
      </div>
      <div class="quarter">
        <div v-for="(item, index) in quarterData" :key="index" @click="handleChange(index)">
          <div :class="['content', { active: index === quarterKey }]">
            <span class="label">{{ item.label }}：</span>
            <span v-if="type === 'person'" class="value">参修人员{{ item.personCount || 0 }}人</span>
            <span v-else-if="type === 'material'" class="value">参修物资{{ item.materialCount || 0 }}个</span>
            <span v-else-if="type === 'personOverlap'" class="value">重叠度{{ item.overlapDayCount || 0 }}天 重叠{{
              item.overlapPersonCount || 0 }}人</span>
            <span v-else-if="type === 'materialOverlap'" class="value">重叠度{{ item.overlapDayCount || 0 }}天 重叠{{
              item.overlapMaterialCount || 0 }}个</span>
          </div>

          <div v-if="index === 0"
            :style="{ transform: `translateX(calc(${100 * quarterKey}% - ${2 * (quarterKey + 1)}px)` }"
            class="active-block" />
        </div>
      </div>
    </div>
  <TableView ref="tableRef" class="mt20" :quarter-key="quarterKey" :data="data" :type="type" :year="year"
    :legend="legend" :minDate="minDate" :repairRound="repairRound" :isEdit="isEdit" @close="closeModal"
    @clickThis="handleData" />
  </div>
</template>

<style scoped lang="less">
.tablebox{
  width: 100%;
  height: 500px;
  overflow: auto;
}
.table-calendar-container {
  background-color: #fff;
  // position: relative;
  height: 100%;
}

.table-calendar-header {
  flex-shrink: 0;
}

.legend-wrap {
  margin-left: 30px;
  display: flex;
  align-items: center;

  >div {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;

    &+div {
      margin-left: 30px;
    }

    .color-block {
      position: relative;
      width: 15px;
      height: 15px;

      &.job::before {
        position: absolute;
        content: '';
        top: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: #60D38D;
      }
    }

    .label {
      margin-left: 10px;
    }

    &.disabled-legend {
      opacity: 0.3;
    }
  }
}

.quarter {
  // margin-top: 20px;
  // position: relative;
  // display: grid;
  // grid-template-columns: repeat(4, minmax(0, 1fr));
  // gap: 0 2px;
  // background-color: #4A7FFA;
  // border: 2px solid #4A7FFA;

  >div {
    position: relative;
    background-color: #fff;
    height: 50px;
    line-height: 50px;
    padding: 0 20px;
    cursor: pointer;

    .content {
      position: relative;
      z-index: 2;
      transition: all .3s;

      &.active {
        color: #fff;
        cursor: default;
      }
    }

    .label {
      font-weight: bold;
    }

    .active-block {
      position: absolute;
      width: 100%;
      height: 100%;
      padding: 0 2px;
      background-color: #4A7FFA;
      z-index: 1;
      top: 0;
      left: 0;
      transition: all .3s;
      box-sizing: content-box;

      &::before {
        position: absolute;
        content: '';
        bottom: 0;
        left: 20px;
        width: 15px;
        height: 10px;
        transform: translateY(100%);
        clip-path: polygon(0 0, 100% 0, 50% 100%);
        background-color: #4A7FFA;
        z-index: 1;
      }
    }
  }
}.mla {
  margin-left: auto;
}
</style>
