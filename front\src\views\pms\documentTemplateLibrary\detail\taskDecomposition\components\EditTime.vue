<script lang="ts" setup>
import {
  nextTick,
  ref,
} from 'vue';
import { InputNumber } from 'ant-design-vue';

const props = defineProps({
  value: {
    type: Number,
    default: 0,
  },
});
const emit = defineEmits(['blur']);
const isEdit = ref(false);
const value_ = ref(0);
const refInputNumber = ref();
// 下拉框展开操作
function handleMouseleave() {
  isEdit.value = false;
}

const handleMouseenter = async () => {
  isEdit.value = true;
  await nextTick();
  refInputNumber.value.focus();
  value_.value = props.value;
};

function handleBlur(event) {
  isEdit.value = false;
  emit('blur', event.target.value);
}

</script>

<template>
  <div
    v-if="!isEdit"
    class="flex-te"
    style="cursor: pointer;"
    @mouseenter="handleMouseenter"
  >
    {{ value }}&nbsp;
  </div>
  <InputNumber
    v-else
    ref="refInputNumber"
    v-model:value="value_"
    style="width: 100px"
    :min="0"
    @mouseleave="handleMouseleave"
    @blur="handleBlur"
  />
</template>

<style scoped lang="less">
</style>
