package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ContractPayNodeConfirmAuditRecordDTO;
import com.chinasie.orion.domain.vo.ContractPayNodeConfirmAuditRecordVO;
import com.chinasie.orion.domain.vo.ContractPayNodeDetailVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ContractPayNodeConfirmAuditRecordService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * ContractPayNodeConfirmAuditRecord 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-27 15:12:55
 */
@RestController
@RequestMapping("/contractPayNodeConfirmAuditRecord")
@Api(tags = "合同支付节点确认审核记录")
public class ContractPayNodeConfirmAuditRecordController {

    @Autowired
    private ContractPayNodeConfirmAuditRecordService contractPayNodeConfirmAuditRecordService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(
            success = "【{USER{#logUserId}}】查看合同支付节点确认审核记录，业务编号：{#id}",
            type = "ContractPayNodeConfirmAuditRecord",
            subType = "详情",
            bizNo = "{#id}"
    )
    public ResponseDTO<ContractPayNodeDetailVO> detail(@PathVariable(value = "id") String id) throws Exception {
        ContractPayNodeDetailVO rsp = contractPayNodeConfirmAuditRecordService.detail(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param contractPayNodeConfirmAuditRecordDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】新增合同支付节点确认审核记录",
            type = "ContractPayNodeConfirmAuditRecord",
            subType = "新增",
            bizNo = ""
    )
    public ResponseDTO<ContractPayNodeConfirmAuditRecordVO> create(@RequestBody ContractPayNodeConfirmAuditRecordDTO contractPayNodeConfirmAuditRecordDTO) throws Exception {
        ContractPayNodeConfirmAuditRecordVO rsp = contractPayNodeConfirmAuditRecordService.create(contractPayNodeConfirmAuditRecordDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param contractPayNodeConfirmAuditRecordDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】编辑合同支付节点确认审核记录，业务编号：{#contractPayNodeConfirmAuditRecordDTO.id}",
            type = "ContractPayNodeConfirmAuditRecord",
            subType = "编辑",
            bizNo = "{#contractPayNodeConfirmAuditRecordDTO.id}"
    )
    public ResponseDTO<Boolean> edit(@RequestBody ContractPayNodeConfirmAuditRecordDTO contractPayNodeConfirmAuditRecordDTO) throws Exception {
        Boolean rsp = contractPayNodeConfirmAuditRecordService.edit(contractPayNodeConfirmAuditRecordDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】删除合同支付节点确认审核记录，业务编号：{ID_LIST{#ids}}",
            type = "ContractPayNodeConfirmAuditRecord",
            subType = "删除",
            bizNo = "{ID_LIST{#ids}}"
    )
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = contractPayNodeConfirmAuditRecordService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @LogRecord(
            success = "【{USER{#logUserId}}】执行合同支付节点确认审核记录分页查询",
            type = "ContractPayNodeConfirmAuditRecord",
            subType = "分页查询",
            bizNo = ""  // 分页无具体业务实体编号
    )
    public ResponseDTO<Page<ContractPayNodeConfirmAuditRecordVO>> pages(@RequestBody Page<ContractPayNodeConfirmAuditRecordDTO> pageRequest) throws Exception {
        Page<ContractPayNodeConfirmAuditRecordVO> rsp = contractPayNodeConfirmAuditRecordService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }
}
