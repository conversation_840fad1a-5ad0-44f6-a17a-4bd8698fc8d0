package  com.chinasie.orion.controller;

import com.chinasie.orion.domain.vo.InterfacePageDataVO;
import com.chinasie.orion.domain.vo.SimpleDictVO;
import com.mzt.logapi.starter.annotation.LogRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;




import com.chinasie.orion.domain.dto.InterfaceManagementDTO;
import com.chinasie.orion.domain.vo.InterfaceManagementVO;

import com.chinasie.orion.service.InterfaceManagementService;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
/**
 * <p>
 * InterfaceManagement 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29 22:08:44
 */
@RestController
@RequestMapping("/interface-management")
@Api(tags = "接口管理")
public class InterfaceManagementController {

    @Autowired
    private InterfaceManagementService interfaceManagementService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(
            success = "【{USER{#logUserId}}】查看【接口信息】，业务编号：{#id}",
            type = "InterfaceManagement",
            subType = "查看",
            bizNo = "{#id}"
    )
    public ResponseDTO<InterfaceManagementVO> detail(@PathVariable(value = "id") String id) throws Exception {
        InterfaceManagementVO rsp = interfaceManagementService.detail(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param interfaceManagementDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】新增【接口信息】", // 假设返回值中包含 id
            type = "InterfaceManagement",
            subType = "新增",
            bizNo = ""
    )
    public ResponseDTO<InterfaceManagementVO> create(@Validated  @RequestBody InterfaceManagementDTO interfaceManagementDTO) throws Exception {
        InterfaceManagementVO rsp =  interfaceManagementService.create(interfaceManagementDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param interfaceManagementDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】编辑【接口信息】，业务编号：{#interfaceManagementDTO.id}",
            type = "InterfaceManagement",
            subType = "编辑",
            bizNo = "{#interfaceManagementDTO.id}"
    )
    public ResponseDTO<Boolean> edit(@Validated  @RequestBody  InterfaceManagementDTO interfaceManagementDTO) throws Exception {
        Boolean rsp = interfaceManagementService.edit(interfaceManagementDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】删除【接口信息】，业务编号：{ID_LIST{#ids}}",
            type = "InterfaceManagement",
            subType = "删除",
            bizNo = "{ID_LIST{#ids}}"
    )
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = interfaceManagementService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/pages", method = RequestMethod.POST)
    @LogRecord(
            success = "【{USER{#logUserId}}】执行【接口信息】分页查询",
            type = "InterfaceManagement",
            subType = "分页查询",
            bizNo = ""  // 分页无具体业务实体编号
    )
    public ResponseDTO<Page<InterfacePageDataVO>> pages(@RequestBody Page<InterfaceManagementDTO> pageRequest) throws Exception {
        Page<InterfacePageDataVO> rsp =  interfaceManagementService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 关闭接口
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "关闭接口")
    @RequestMapping(value = "/close/{id}", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】关闭【接口】，业务编号：{#id}",
            type = "InterfaceManagement",
            subType = "关闭",
            bizNo = "{#id}"
    )
    public ResponseDTO<Boolean> close(@PathVariable("id") String id) throws Exception {
        Boolean rsp = interfaceManagementService.close(id);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "获取编码")
    @RequestMapping(value = "/number", method = RequestMethod.GET)
    @LogRecord(
            success = "【{USER{#logUserId}}】获取【接口编码】",
            type = "InterfaceManagement",
            subType = "获取编码",
            bizNo = ""  // 无具体业务编号
    )
    public ResponseDTO<String> getNumber() throws Exception {
        return new ResponseDTO<>(interfaceManagementService.getInterfaceNumber());
    }


    @ApiOperation(value = "获取接口类型")
    @RequestMapping(value = "/type/dict", method = RequestMethod.GET)
    @LogRecord(
            success = "【{USER{#logUserId}}】获取【接口类型字典】",
            type = "InterfaceManagement",
            subType = "获取接口类型",
            bizNo = ""  // 无具体业务编号
    )
    public ResponseDTO<List<SimpleDictVO>> typeDict() throws Exception {
        return new ResponseDTO<>(interfaceManagementService.getTypeDict());
    }

    @ApiOperation(value = "获取第三方检查备案类型")
    @RequestMapping(value = "/third/verify/dict", method = RequestMethod.GET)
    @LogRecord(
            success = "【{USER{#logUserId}}】获取【第三方检查备案类型字典】",
            type = "InterfaceManagement",
            subType = "获取第三方检查备案类型",
            bizNo = ""  // 无具体业务编号
    )
    public ResponseDTO<List<SimpleDictVO>> thirdVerifyDict() throws Exception {
        return new ResponseDTO<>(interfaceManagementService.thirdVerifyDict());
    }

    @ApiOperation(value = "获取接口审批状态")
    @RequestMapping(value = "/audit/status/dict", method = RequestMethod.GET)
    @LogRecord(
            success = "【{USER{#logUserId}}】获取【接口审批状态字典】",
            type = "InterfaceManagement",
            subType = "获取接口审批状态",
            bizNo = ""  // 无具体业务编号
    )
    public ResponseDTO<List<SimpleDictVO>> auditStatusDict() throws Exception {
        return new ResponseDTO<>(interfaceManagementService.auditStatusDict());
    }

    @ApiOperation(value = "获取接口状态")
    @RequestMapping(value = "/state/dict", method = RequestMethod.GET)
    @LogRecord(
            success = "【{USER{#logUserId}}】获取【接口状态字典】",
            type = "InterfaceManagement",
            subType = "获取接口状态",
            bizNo = ""  // 无具体业务编号
    )
    public ResponseDTO<List<String>> stateDictList() throws Exception {
        return new ResponseDTO<>(interfaceManagementService.stateDictList());
    }


    /**
     * 获取当前项目下的接口列表
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取当前项目下的接口列表")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @LogRecord(
            success = "【{USER{#logUserId}}】获取当前项目下的接口列表",
            type = "InterfaceManagement",
            subType = "获取接口列表",
            bizNo = ""  // 无具体业务编号
    )
    public ResponseDTO<List<InterfacePageDataVO>> list(@RequestBody InterfaceManagementDTO interfaceManagementDTO) throws Exception {
        List<InterfacePageDataVO> rsp =  interfaceManagementService.list(interfaceManagementDTO);
        return new ResponseDTO<>(rsp);
    }
}
