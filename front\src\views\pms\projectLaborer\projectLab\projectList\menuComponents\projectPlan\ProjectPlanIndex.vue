<template>
  <div ref="popoverContainer" class="plan-container">
    <div v-if="mode === 'gant' || mode === 'milestone'" class="type-node">
      <a-radio-group
        v-if="!$props.from"
        v-model:value="mode"
        class="select-btn"
      >
        <a-radio-button
          v-if="isPower('PMS_XMXQ_container_03_01_01_button_07', powerData)"
          value="table"
        >
          项目计划
        </a-radio-button>
        <a-radio-button
          v-if="isPower('PMS_XMXQ_container_03_01_01_button_08', powerData)"
          value="gant"
        >
          甘特图
        </a-radio-button>
        <a-radio-button
          v-if="isPower('PMS_XMXQ_container_03_01_01_button_09', powerData)"
          value="milestone"
        >
          里程碑
        </a-radio-button>
      </a-radio-group>
    </div>
    <OrionTable
      v-if="mode === 'table' || $props.from"
      ref="tableRef"
      :class="{ 'plan-table': true, plan: !$props.from }"
      :options="tableOptions"
      :expandIconColumnIndex="2"
      :rowKey="(record) => record.key"
      :expandedRowKeys="defaultExpandedRowKeys"
      @expand="expandRows"
      @initData="initData"
      @actionClick="actionClick"
      @row-drag-end="onRowDragEnd"
      xVirtual
    >
      <template #planIndex="{ record }">
        {{ indexData?.filter((v) => v?.id === record?.id)[0]?.index }}
      </template>
      <template #toolbarLeft>
        <div class="source-table-tab">
          <UnorderedListOutlined
            v-if="tabsList.length > 0"
            size="14"
            class="f-icon"
          />
          <BasicTabs
            v-model:tabsIndex="tabsIndex"
            :tabs="tabsList"
            @tabsChange="tabsChange"
          />
        </div>
        <div class="source-table-slots">
          <BasicButton
            v-if="isPower('PMS_XMXQ_container_03_01_01_button_10', powerData)"
            type="primary"
            icon="add"
            ghost
            @click="throttleAddPlan(1, null)"
          >
            添加计划
          </BasicButton>
          <BasicButton
            v-if="isPower('PMS_XMXQ_container_03_01_01_button_03', powerData)"
            :disabled="distributesDisabled"
            icon="sie-icon-jihuaxiafa"
            type="primary"
            ghost
            @click="planDistribute"
          >
            计划下发
          </BasicButton>
          <div
            v-if="isPower('PMS_XMXQ_container_03_01_01_button_13', powerData)"
            class="expanded-keys-change"
          >
            <span>显示层级</span>
            <ASelect
              v-model:value="showLevel"
              :options="showLevelOptions"
              @change="changeExpandedRowKeys"
            />
          </div>
          <div
            v-if="isPower('PMS_XMXQ_container_03_01_01_button_14', powerData)"
            class="ant-down-trigger"
          >
            <BasicButton class="hover-down-more" @click="handleToggle">
              更多
              <Icon
                icon="sie-icon-xiala2"
                size="14"
                :class="isDropDown ? 'ant-up-icon' : 'ant-down-icon'"
              />
            </BasicButton>
            <div v-show="isDropDown" class="dropdown-content">
              <BasicButton
                v-if="
                  isPower('PMS_XMXQ_container_03_01_01_button_10', powerData)
                "
                type="primary"
                ghost
                icon="add"
                :disabled="selectedRows.length != 1"
                @click="throttleAddPlan(2, null)"
              >
                添加子计划
              </BasicButton>
              <BasicButton
                v-if="
                  isPower('PMS_XMXQ_container_03_01_01_button_01', powerData)
                "
                icon="add"
                :disabled="addBtnDisabled"
                type="primary"
                ghost
                @click="() => addModalVisibleChange(true)"
              >
                批量添加
              </BasicButton>
              <BasicButton
                v-if="
                  isPower('PMS_XMXQ_container_03_01_01_button_04', powerData)
                "
                icon="sie-icon-daoru"
                :disabled="importDisabled"
                type="primary"
                ghost
                @click="handleImport"
              >
                导入
              </BasicButton>
              <BasicButton
                v-if="
                  isPower('PMS_XMXQ_container_03_01_01_button_05', powerData)
                "
                icon="sie-icon-daochu"
                type="primary"
                ghost
                @click="exportFile"
              >
                导出
              </BasicButton>
              <BasicButton
                v-if="
                  isPower('PMS_XMXQ_container_03_01_01_button_06', powerData)
                "
                icon="sie-icon-del"
                :disabled="selectedRows.length === 0"
                type="primary"
                ghost
                @click="handleBatchDel"
              >
                删除
              </BasicButton>
            </div>
          </div>
        </div>
      </template>
      <template #toolbarRight>
        <div v-if="mode === 'table'" class="toolbar-right">
          <a-radio-group
            v-if="!$props.from"
            v-model:value="mode"
            class="select-btn"
            :class="{ 'select-btn-table': mode === 'table' }"
          >
            <a-radio-button
              v-if="isPower('PMS_XMXQ_container_03_01_01_button_07', powerData)"
              value="table"
            >
              项目计划
            </a-radio-button>
            <a-radio-button
              v-if="isPower('PMS_XMXQ_container_03_01_01_button_08', powerData)"
              value="gant"
            >
              甘特图
            </a-radio-button>
            <a-radio-button
              v-if="isPower('PMS_XMXQ_container_03_01_01_button_09', powerData)"
              value="milestone"
            >
              里程碑
            </a-radio-button>
          </a-radio-group>
        </div>
      </template>
      <template #action="{ record }">
        <BasicTableAction :actions="getActions()" :record="record" />
      </template>
      <template #rowDragGhost="{ record, preTargetInfo, nextTargetInfo }">
        <Icon icon="orion-icon-swap" size="16" />
        <span style="color: red">
          从 {{ record.name }} 拖动到
          {{ preTargetInfo?.record.name || nextTargetInfo?.record.name }}
        </span>
      </template>
    </OrionTable>
    <GantView v-if="mode === 'gant'" :projectId="id" />
    <Milestone v-if="mode === 'milestone'" :projectId="id" />
    <!-- 计划编制 -->
    <AddModal
      :planActiveOptions="planActiveOptions"
      @register="registerAdd"
      @handleColse="() => addModalVisibleChange(false)"
    />
    <!-- 编辑，变更 -->
    <EditModal
      @register="registerEdit"
      @close="() => setEditPlanModal(false, null, null, '', '', '')"
      @closeUpdate="handleCloseUpdate"
    />
    <!-- 审批 -->
    <ApplyChange
      @register="registerApplyChange"
      @close="() => setApplyChange(false, null)"
    />
    <!-- 导入 -->
    <BasicImport
      :downloadFileObj="downloadFileObj"
      :requestBasicImport="requestBasicImport"
      :requestSuccessImport="requestSuccessImport"
      @register="register"
      @changeImportModalFlag="changeImportModalFlag"
    />
    <!-- 催办 -->
    <UrgePlanModal
      @updateForm="updateForm"
      @register="registerUrgePlanModal"
      @handleColse="() => updateForm()"
    />
  </div>
</template>
<script lang="ts" setup>
import {
  computed,
  createVNode,
  inject,
  nextTick,
  onMounted,
  onUnmounted,
  provide,
  Ref,
  ref,
  defineProps,
  reactive,
} from "vue";
import { throttle } from "lodash-es";
import {
  Button,
  message,
  Modal,
  Radio,
  Select as ASelect,
} from "ant-design-vue";
import {
  BasicButton,
  BasicImport,
  BasicTableAction,
  downloadByData as basicDownloadByData,
  getDictByNumber,
  isPower,
  openModal as openModalNew,
  OrionTable,
  useDrawer,
  useModal,
  BasicTabs,
  Icon,
} from "lyra-component-vue3";
import {
  InfoCircleOutlined,
  UnorderedListOutlined,
} from "@ant-design/icons-vue";
import { useRoute, useRouter } from "vue-router";
import dayjs from "dayjs";
import { postProjectSchemeList } from "../../../api";
import { projectIdKey } from "../../../types";
import AddModal from "./components/AddPlan.vue";
import ApplyChange from "./components/ApplyChange.vue";
import EditModal from "./components/EditPlan.vue";
import GantView from "./components/GanteView.vue";
import Milestone from "./components/Milestone.vue";
import PlanDelivery from "./components/PlanDelivery.vue";
import ReasonForReturn from "./components/ReasonForReturn.vue";
import UrgePlanModal from "./components/UrgePlanModal.vue";
import ProjectPlanDetail from "/@/views/pms/projectLaborer/projectLab/pages/projectPlanDetails/ProPlanDetails.vue";
import {
  baseLineRow,
  completePlanRow,
  delegateRowPlan,
  distributePlanRow,
  initActions,
  initColumns,
  initFilterConfig,
  initTableData,
  pausePlanRow,
  planDoneRow,
  planRecordsRow,
  deliverablesRecordsRow,
  startPlanRow,
  terminatePlanRow,
  writeDelayReasonRow,
} from "./index";
import Api from "/@/api";
import { useUserStore } from "/@/store/modules/user";
import { batchIncludes } from "/@/views/pms/utils/utils";
import { useRouteParamFormUserHome } from "/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/util";
const props = defineProps({
  projectData: {
    type: Object,
    default: () => ({}),
  },
  id: {
    type: String,
    default: "",
  },
  from: {
    type: String,
    default: "",
  },
  prefix: {
    type: String,
    default: "XM_container_button_",
  },
  interval: {
    type: Number,
    default: 21,
  },
  status: {
    type: Number,
    default: null,
  },
  circumstance: {
    type: Number,
    default: null,
  },
});
const router = useRouter();
const route = useRoute();
const AButton = Button;
const { isEnable, getRouteParamFormUserHome, removeRouteParamFormUserHome } =
  useRouteParamFormUserHome();
const ARadioGroup = Radio.Group;
const ARadioButton = Radio.Button;
const tableDataSource = ref([]);
const popoverContainer = ref(null);
const projectId = inject(projectIdKey) || inject("projectId");
const showLevel: Ref<string> = ref("all");
const planActiveOptions: Ref<Record<any, any>[]> = ref([]);
const showLevelOptions: Ref<Record<any, any>[]> = ref([
  {
    label: "全部",
    value: "all",
  },
  {
    label: "一级",
    value: "one",
  },
  {
    label: "二级",
    value: "two",
  },
  {
    label: "三级",
    value: "three",
  },
]);
provide("projectId", projectId);
const tableRef = ref(null);
const selectedRowKeys = ref([]);
const userStore = useUserStore();
const mode = ref('table');
const defaultExpandedRowKeys = ref<string[]>([]);
const downloadFileObj = {
  url: "/pms/projectScheme/template/download",
  method: "POST",
};
const powerData: Ref = inject("powerData");
const tabsIndex: Ref<number> = ref(0);
const tabsKey: Ref<number> = ref(0);
const isDropDown: Ref<boolean> = ref(false);
const editItem = ref(null);
const editType = ref(null);
const tabsList = computed(() =>
  [
    isPower("PMS_XMXQ_container_01_TAB_01", powerData)
      ? {
          key: 1,
          name: "全部计划",
        }
      : undefined,
    isPower("PMS_XMXQ_container_01_TAB_02", powerData)
      ? {
          key: 2,
          name: "我负责的",
        }
      : undefined,
    isPower("PMS_XMXQ_container_01_TAB_03", powerData)
      ? {
          key: 3,
          name: "我协助的",
        }
      : undefined,
    isPower("PMS_XMXQ_container_01_TAB_04", powerData)
      ? {
          key: 4,
          name: "我创建的",
        }
      : undefined,
  ].filter((item) => item)
);

const userList = ref([]);
const [register, { openModal }] = useModal();

const [registerUrgePlanModal, { openModal: openUrgePlanModal }] = useModal();

const [registerAdd, { openModal: setAddPlan }] = useModal();

const [registerApplyChange, { openDrawer: openApplyChange }] = useDrawer();
const [registerEdit, { openDrawer: openEdit }] = useDrawer();
const indexData: Ref<any[]> = ref([]);
const isChange: Ref<boolean> = ref(false);
const paramsSearchConditions = ref();
const condition = computed(() => {
  let arr = [];
  if (isChange.value) {
    return [];
  }
  if (props.status) {
    arr.push({
      field: "status",
      fieldType: "Integer",
      values: [props.status],
      queryType: "eq",
    });
  }
  if (props.circumstance) {
    arr.push({
      field: "circumstance",
      fieldType: "Integer",
      values: [props.circumstance],
      queryType: "eq",
    });
  }
  return arr.length ? [arr] : [];
});
provide("indexData", indexData);

const throttleAddPlan = throttle(addPlanClick, 1000, { trailing: false });
const tableOptions = ref({
  // settingCode: 'PMS_PROJECT_PLAN_INDEX',
  deleteToolButton: "add|enable|disable|delete",
  rowSelection: {
    checkStrictly: true,
    selectedRowKeys,
    onSelect: onSelectChange,
    onSelectAll,
  },
  showIndexColumn: false,
  showSmallSearch: false,
  pagination: { pageSize: 50 },
  isFilter2: true,
  filterConfig: initFilterConfig(projectId),
  afterFetch: () => {
    nextTick(() => {
      setTimeout(() => {
        if (isEnable()) {
          const targetPosiId = getRouteParamFormUserHome("posiId");
          if (!targetPosiId) {
            selectedRowKeys.value.push(targetPosiId);
          }
          tableRef.value?.scrollTo({ rowKey: targetPosiId });
          removeRouteParamFormUserHome();
        }
      });
    });
  },
  rowClassName: (record) => (record.topSort ? "table-striped" : null),
  api: async (tableParams) => {
    let params = props.from
      ? {
          type: tabsIndex.value,
          projectId,
          typeEnum: "ACCEPTANCE_FROM",
          power: {
            pageCode:
              props.prefix === "XMYS_container_button_"
                ? "PMS-ProjectAccpetanceDetail"
                : "PMS-cghAccpetanceDetail",
            containerCode:
              props.prefix === "XMYS_container_button_"
                ? "XMYS_container_03"
                : "CGYS_container_03",
          },
          searchConditions: tableParams.searchConditions,
        }
      : {
          type: tabsIndex.value,
          projectId,
          typeEnum: "PROJECT_SCHEME",
          power: {
            pageCode: "PMS90002",
            containerCode: "XM_container_04_01_01",
          },
          searchConditions: condition.value?.length
            ? condition.value
            : tableParams.searchConditions,
        };
    params.power = {
      pageCode: "PMS0004",
      containerCode: "PMS_XMXQ_container_03_01_02",
    };
    let result = await postProjectSchemeList(params);
    indexData.value = [];
    result = initTableData(result, "", indexData, 1, []);
    paramsSearchConditions.value = props.from ? tableParams.searchConditions : (condition.value?.length
            ? condition.value
            : tableParams.searchConditions);
    tableDataSource.value = result;
    return result;
  },
  filter2Change() {
    isChange.value = true;
  },
  columns: initColumns(
    userList,
    updateForm,
    planActiveOptions,
    popoverContainer,
    indexData.value,
    userStore.getUserInfo,
    actionClick,
    throttleAddPlan,
    actionView
  ),
});

function updateForm() {
  isDropDown.value = false;
  nextTick(() => {
    tableRef.value.reload();
  });
}

function getUserListByProjectId(projectId) {
  const url = `/pms/project-role-user/${projectId}/user/list`;
  return new Api(url).fetch("", "", "GET").then((res) =>
    res.map((item) => ({
      value: item.id,
      label: item.name,
      deptId: item.deptId,
      deptName: item.deptName,
    }))
  );
}

// 拖拽排序
const onRowDragEnd = ({ record, preTargetInfo, nextTargetInfo }) =>
  dragSort(record, preTargetInfo?.record, nextTargetInfo?.record);
async function dragSort(record, preTargetInfo, nextTargetInfo) {
  const flag = preTargetInfo?.level === 1 && nextTargetInfo?.level === 1;
  const id =
    preTargetInfo === undefined || nextTargetInfo === undefined || flag
      ? 0
      : preTargetInfo.level < nextTargetInfo.level
      ? preTargetInfo.id
      : preTargetInfo.parentId;

  const params = {
    id: record.id,
    parentId: record.parentId,
    level: record.level,
    projectId: record.projectId,
    sort: preTargetInfo?.sort,
    moveParentId: id,
    upLevelId: preTargetInfo?.id,
  };
  const url = "/pms/projectScheme/drag";

  try {
    await new Api(url).fetch(params, "", "PUT");
    updateForm();
  } catch (error) {
    message.error("拖拽排序失败，请重新尝试");
  }
}

// 自定义用户选择
function onSelectChange(record, selected) {
  if (selected) {
    if (record.key.endsWith("_Top")) {
      selectedRowKeys.value.push(record.id);
      selectedRowKeys.value.push(record.key);
    } else {
      selectedRowKeys.value.push(record.id);
      if (record.topSort !== 0) {
        selectedRowKeys.value.push(`${record.id}_Top`);
      }
    }
  }
  if (!selected) {
    if (record.topSort !== 0) {
      selectedRowKeys.value = selectedRowKeys.value.filter(
        (item) => ![`${record.id}_Top`, record.id].includes(item)
      );
    } else {
      selectedRowKeys.value = selectedRowKeys.value.filter(
        (item) => record.id !== item
      );
    }
  }
}

// 用户全选回调
function onSelectAll(selected, rows) {
  if (selected) {
    selectedRowKeys.value = rows.filter((item) => item).map((item) => item.key);
  } else {
    selectedRowKeys.value = [];
  }
}

// 删除 isPower('PL', roleUserInfo.value) 判断条件
const addBtnDisabled = computed(
  () =>
    selectedRows.value.length &&
    selectedRows.value.some(
      (val) =>
        val.approveStatus === 0 ||
        val.approveStatus === 1 ||
        val?.dataStatus?.statusValue === 111 ||
        (!isSchemeCreator.value(val) && !isSchemeRsp.value(val))
    )
);

// 判断计划下发是否可用
const distributesDisabled = computed(() => {
  if (selectedRows.value.length === 0) {
    return true;
  }
  const selected = selectedRows.value;
  const allHaveNonEmptyContractMilestoneId = selected.every(
    (val) => val.contractMilestoneId
  );
  // 检查原有条件
  const originalConditions = selected.some(
    (val) =>
      [0, 1].includes(val.approveStatus) ||
      [111].includes(val.status) ||
      !isSchemeCreator.value(val)
  );

  return allHaveNonEmptyContractMilestoneId ? false : originalConditions;
});

const importDisabled = computed(() =>
  selectedRows.value.some(
    (item) =>
      item.approveStatus <= 1 ||
      item.status === 111 ||
      !isSchemeCreator.value(item)
  )
);

// 计划创建人
const isSchemeCreator = computed(
  () => (row) => row.creatorId === useUserStore().getUserInfo.id
);
// 计划责任人
const isSchemeRsp = computed(
  () => (row) => row.rspUser === useUserStore().getUserInfo.id
);

// 表格勾选数据---不包含置顶的数据
const selectedRows = computed(() =>
  indexData.value.filter((item) =>
    selectedRowKeys.value
      .filter((item1) => !item1.endsWith("_Top"))
      .includes(item.id)
  )
);

function startExecution(record) {
  Modal.confirm({
    title: () => "执行提醒",
    icon: () => createVNode(InfoCircleOutlined),
    content: () => "是否开始执行此计划？",
    async onOk() {
      await startExecutionImpl(record?.id);
      updateForm();
    },
    onCancel() {},
  });
}

function startExecutionImpl(id) {
  const url = `/pms/projectScheme/projectScheme/actualBeginTime/update?id=${id}`;
  return new Api(url).fetch({}, "", "PUT");
}

function expandRows(expanded, record) {
  if (expanded) {
    defaultExpandedRowKeys.value.push(record.id);
  } else {
    defaultExpandedRowKeys.value = defaultExpandedRowKeys.value.filter(
      (item) => item !== record.id
    );
  }
}

function initData(data) {
  if (showLevel.value === "all") {
    defaultExpandedRowKeys.value = getDefaultExpandedRowKeys(data);
  } else {
    let levelNum = 0;
    if (showLevel.value === "one") {
      levelNum = 1;
    } else if (showLevel.value === "two") {
      levelNum = 2;
    } else {
      levelNum = 3;
    }
    defaultExpandedRowKeys.value = getExpandedRowKeys(data, levelNum - 1);
  }
}

function changeExpandedRowKeys(val) {
  let tableData = tableRef.value.getDataSource();
  if (showLevel.value === "all") {
    defaultExpandedRowKeys.value = getDefaultExpandedRowKeys(tableData);
  } else {
    let levelNum = 0;
    if (showLevel.value === "one") {
      levelNum = 1;
    } else if (showLevel.value === "two") {
      levelNum = 2;
    } else {
      levelNum = 3;
    }
    defaultExpandedRowKeys.value = getExpandedRowKeys(tableData, levelNum - 1);
  }
}

function getDefaultExpandedRowKeys(data) {
  let rowKeys = [];
  for (let i = 0; i < data.length; i++) {
    let item = data[i];
    if (item.children && item.children.length > 0) {
      rowKeys.push(item.id);
      let rowKeys1 = getDefaultExpandedRowKeys(item.children);
      if (rowKeys1) {
        rowKeys = rowKeys.concat(rowKeys1);
      }
    }
  }
  return rowKeys;
}

function getExpandedRowKeys(data, level) {
  if (level === 0) {
    return [];
  }
  let rowKeys = [];
  data.forEach((item) => {
    if (Array.isArray(item.children) && item.children.length > 0) {
      rowKeys.push(item.id);
      let rowKeys1 = getExpandedRowKeys(item.children, level - 1);
      if (rowKeys1) {
        rowKeys = rowKeys.concat(rowKeys1);
      }
    }
  });
  return rowKeys;
}

function addModalVisibleChange(value: boolean) {
  if (selectedRows.value.length > 1) {
    message.error("计划编制只能选择一条项目计划");
    return;
  }

  if (value) {
    setAddPlan(true, {
      parentIds: selectedRows.value.map((item) => item?.id),
      parentData: selectedRows.value,
      projectData: props?.projectData,
      from: props?.from || "",
    });
  }
  if (!value) {
    updateForm();
  }
}

function changeImportModalFlag({ successImportFlag, succ }) {
  if (successImportFlag) {
    updateForm();
  } else if (succ) {
    new Api(`/pms/projectScheme/import/excel/cancel/${succ}`).fetch(
      "",
      "",
      "post"
    );
  }
}

const handleImport = () => {
  if (selectedRows.value.length > 1) {
    message.error("导入只能选择一条项目计划");
    return;
  }
  openModal(true, {});
};

const handleCloseUpdate = (dataObj) => {
  const params = {
    isWork: 0,
    id: dataObj?.id,
    rspSubDept: dataObj?.rspSubDept,
    beginTime: dataObj?.beginTime,
    endTime: dataObj?.endTime,
    enforceBasePlace: dataObj?.enforceBasePlace,
    name: dataObj?.name,
    enforceType: dataObj?.enforceType,
    parent: dataObj?.parent,
    participantUserList: dataObj?.participantUserList,
    remark: dataObj?.remark,
    repairRound: dataObj?.repairRound,
    rspSectionName: dataObj?.rspSectionName,
    rspSubDeptName: dataObj?.rspSubDeptName,
    rspUser: dataObj?.rspUser,
    rspUserCode: dataObj?.rspUserCode,
    workContent: dataObj?.workContent,
  };
  const url = "projectScheme/edit";
  return new Api("/pms").fetch(params, url, "PUT").then((res) => {
    if (res) {
      updateForm();
    }
  });
};

const setEditPlanModal = (
  value,
  record = null,
  type = "edit",
  isWork,
  isCreate,
  id
) => {
  editItem.value = record;
  editType.value = type;
  if (value && record) {
    openEdit(value, {
      data: record,
      editType: type,
      isWork,
      isCreate,
      id,
    });
  }
  if (!value) {
    updateForm();
  }
};

const pushRecord = (record, key) => {
  new Api("/pms")
    .fetch("", `projectScheme/${key}/${record.id}`, "PUT")
    .then(() => {
      message.success(key === "unTop" ? "取消" : "置顶成功");
      updateForm();
    });
};

// 计划下发
const planDistribute = () => {
  if (selectedRows.value.length > 1) {
    message.error("计划下发只能选择一条项目计划");
    return;
  }
  let record = selectedRows.value[0];
  if (record.parentId !== "0") {
    let parentRecord = indexData.value.find(
      (item) => item.id === record.parentId
    );
    if (parentRecord.status === 101) {
      message.warning("请先下发父级计划");
      return;
    }
  }
  if (Array.isArray(record.children) && record.children.length > 0) {
    Modal.confirm({
      title: "已选中数据为父级计划，所有待发布子级计划会跟随下发",
      onOk: async () => {
        distributePlanRow(
          {
            schemeId: selectedRows.value[0].id,
            projectId,
            record: selectedRows.value[0],
          },
          updateForm
        );
      },
      onCancel: () => {},
    });
  } else {
    distributePlanRow(
      {
        schemeId: selectedRows.value[0].id,
        projectId,
        record: selectedRows.value[0],
      },
      updateForm
    );
  }
};

const handleToDetail = (row, type = true) => {
  if (!type) return;
  router.push({
    name: "ProPlanDetails",
    params: { id: row.id },
  });
};
const setApplyChange = (value, record) => {
  editItem.value = record;
  if (value) {
    openApplyChange(value, { data: record });
  } else {
    updateForm();
  }
};

// 上移
const onClick = async (action, row) => {
  if (action.key === "up") {
    upRecord(row.id);
  }
  if (action.key === "down") {
    downRecord(row.id);
  }
  if (action.key === "delete") {
    const id = Array.isArray(row) ? row : [row.id];
    deleteTable(id);
  }
  if (action.key === "urgePlan") {
    const id = Array.isArray(row) ? row : [row.id];
    deleteTable(id);
  }
};

const deleteTable = (params: string[], title?: string) => {
  Modal.confirm({
    title: title || "是否删除该数据?",
    onOk() {
      return new Promise((resolve) => {
        new Api("/pms")
          .fetch(params, "projectScheme", "DELETE")
          .then(() => {
            message.success("删除成功");
            updateForm();
            selectedRowKeys.value = selectedRowKeys.value.filter(
              (item) => !params.includes(item) && !params.includes(`${item}Top`)
            );
          })
          .finally(() => {
            resolve("");
          });
      });
    },
  });
};

const downRecord = (id) => {
  new Api("/pms").fetch("", `projectScheme/down/${id}`, "PUT").then(() => {
    message.success("下移成功");
    updateForm();
  });
};

const upRecord = (id) => {
  new Api("/pms").fetch("", `projectScheme/up/${id}`, "PUT").then(() => {
    message.success("上移成功");
    updateForm();
  });
};
const requestBasicImport = async (formData) =>
  new Promise((resolve) => {
    new Api("/pms")
      .importFile(
        formData[0],
        `/api/pms/projectScheme/import/excel/${projectId}?pid=${
          selectedRows.value?.[0]?.id || 0
        }`
      )
      .then((res) => {
        const { code, message } = res.data;
        if (code === 200) {
          // 转换oom   ---》 message
          let newResultData = res.data.result;
          if (res.data.result.oom) {
            newResultData.message = res.data.result.oom;
          }
          resolve(newResultData);
        } else {
          resolve({
            code: 4000,
            message,
          });
        }
      });
  });

const exportFile = async () => {
  await basicDownloadByData(
    "/api/pms/projectScheme/export/excel",
    {
      typeEnum: props.from ? 'ACCEPTANCE_FROM' : 'PROJECT_SCHEME',
      type: tabsIndex.value,
      searchConditions: paramsSearchConditions.value,
      projectId,
      projectSchemeIds: selectedRows.value
        ? selectedRows.value.map((item) => item.id)
        : [],
    },
    "",
    "POST",
    false,
    false
  );
};

const requestSuccessImport = (importId) =>
  new Promise((resolve) => {
    new Api(`/pms/projectScheme/import/excel/verify/${importId}`)
      .fetch("", "", "post")
      .then(() => {
        resolve({
          result: true,
        });
      });
  });

// 批量删除
function handleBatchDel() {
  if (selectedRows.value.every((item) => item.status === 101)) {
    if (selectedRows.value.some((item) => item.children)) {
      deleteTable(
        selectedRows.value.map((item) => item.id),
        "该计划下有子计划是否确定一并删除?"
      );
    } else {
      deleteTable(selectedRows.value.map((item) => item.id));
    }
  } else {
    message.info("选择计划存在不允许删除数据，请重新选择");
  }
}

// 设置tooltip挂载节点
function getPopupContainer(): Element {
  return document.querySelector(".plan-container");
}

function sendBack(record) {
  const refReasonForReturn = ref();
  openModalNew({
    title: "计划退回",
    height: 350,
    content(h) {
      return h(ReasonForReturn, { ref: refReasonForReturn });
    },
    async onOk() {
      const { reason } = await refReasonForReturn.value.refForm.validate();
      const data = {
        id: record.id,
        reason,
      };
      const url = "/pms/projectScheme/fallback";
      await new Api(url).fetch(data, "", "POST");
      updateForm();
    },
  });
}

// 查看详情
function onClickBulletFame(record) {
  const modalRef = ref();
  const modalData = {
    id: record.id,
  };
  openModalNew({
    title: "项目计划详情",
    width: 1200,
    height: 800,
    isFullScreen: false,
    content(h) {
      return h(ProjectPlanDetail, {
        ref: modalRef,
        modalData,
        router,
      });
    },
    footer: null,
  });
}

// 计划下发
function handleReissue(record) {
  const refPlanDelivery = ref();
  openModalNew({
    title: "计划下发",
    height: 350,
    content(h) {
      return h(PlanDelivery, {
        ref: refPlanDelivery,
        projectId,
      });
    },
    async onOk() {
      const result = await refPlanDelivery.value.handleSubmit();
      const schemeIds = [record.id];
      const data = {
        ...result,
        schemeIds,
      };
      const url = "/pms/projectScheme/issue";
      await new Api(url).fetch(data, "", "PUT");
      updateForm();
    },
  });
}

onMounted(async () => {
  userList.value = await getUserListByProjectId(projectId);
  planActiveOptions.value = await getDictByNumber("planActive");
});

function actionView(type, record) {
  switch (type) {
    case "check":
      onClickBulletFame(record);
      break;
    case "startExecution":
      startExecution(record);
      break;
    case "executionComplete":
      planDoneRow(record, updateForm);
      break;
    case "completePlanRow":
      completePlanRow(record, updateForm);
      break;
  }
}

function actionClick(type, record, isWork, isCreate, id) {
  switch (type) {
    // case 'check':
    //   onClickBulletFame(record);
    //   break;
    case "edit":
      setEditPlanModal(true, record, "edit", isWork, isCreate, id);
      break;
    case "delete":
      onClick({ key: "delete" }, record);
      break;
    case "pinned":
      pushRecord(record, "top");
      break;
    case "unPinned":
      pushRecord(record, "unTop");
      break;
    case "up":
      onClick({ key: "up" }, record);
      break;
    case "down":
      onClick({ key: "down" }, record);
      break;
    case "reissue":
      handleReissue(record);
      break;
    // case 'startExecution':
    //   startExecution(record);
    //   break;
    case "baseline":
      baseLineRow(record, updateForm);
      break;
    case "sendBack":
      sendBack(record);
      break;
    case "writeDelayReasonRow":
      writeDelayReasonRow(record, updateForm);
      break;
    case "planApproval":
      setApplyChange(true, record);
      break;
    case "planRecords":
      planRecordsRow(record, updateForm);
      break;
    // case 'executionComplete':
    //   planDoneRow(record, updateForm);
    //   break;
    case "apply":
      setEditPlanModal(true, record, "apply", "", "", "");
      break;
    // case 'completePlanRow':
    //   completePlanRow(record, updateForm);
    //   break;
    case "startPlanRow":
      startPlanRow(record, updateForm);
      break;
    case "pausePlanRow":
      pausePlanRow(record, updateForm);
      break;
    case "terminatePlanRow":
      terminatePlanRow(record, updateForm);
      break;
    case "transact":
      openUrgePlanModal(true, record);
      break;
    case "revocation":
      revocationChange(record);
      break;
    case "deliverables":
      deliverablesRecordsRow(record, updateForm);
      break;
    case "delegateRowPlan":
      delegateRowPlan(record, updateForm);
      break;
  }
}

// 创建计划
async function addPlanClick(type: number, isWork: number, record: any) {
  console.log(record,type)
  let params: any = {
    projectId,
    isWork,
    sort: type === 1 ? record?.sort : 0,
  };

  const getParamsFromRecordOrSelectedRow = () => {
    if (record) {
      return {
        parentId: type === 1 ? record.parentId || 0 : record.id,
        beginTime: record.endTime,
      };
    }
    if (selectedRows.value.length > 0) {
      return {
        parentId:
          type === 1
            ? selectedRows.value[0].parentId || 0
            : selectedRows.value[0].id,
        beginTime: selectedRows.value[0].endTime,
      };
    }
    return { parentId: 0 };
  };

  const additionalParams = getParamsFromRecordOrSelectedRow();
  if (additionalParams !== null) {
    params = {
      ...params,
      ...additionalParams,
    };
  } else if (type === 2) {
    message.warning("请先选择数据");
    return;
  }
  try {
    await new Api("/pms")
      .fetch(params, "projectScheme/create", "POST")
      .then((res) => {
        if (res) {
          message.success("创建计划成功");
          updateForm();
          if (isWork === 1) {
            nextTick(() => {
              actionClick("edit", record, isWork, true, res);
            });
          }
        }
      });
  } catch (error) {
    message.error("创建计划失败，请检查输入数据");
  }
}

function revocationChange(record) {
  let recordItem = indexData.value.find((item) => item.id === record.id);
  if (Array.isArray(recordItem.children) && recordItem.children.length > 0) {
    let statusList = getChildrenStatus(recordItem.children);
    if (statusList.some((item) => item !== 130)) {
      message.warning("当前已有子级计划被执行，无法撤回");
      return;
    }
  }
  Modal.confirm({
    title: "撤回提示",
    content:
      "正在撤回父级计划，所有已下发子级计划会同步撤回，撤回后可继续下发。",
    onOk() {
      new Api("/pms")
        .fetch({ id: record.id }, "projectScheme/revocation", "GET")
        .then((res) => {
          message.success(res);
          updateForm();
        });
    },
  });
}

function getChildrenStatus(data) {
  let statusList = [];
  data.forEach((item) => {
    statusList.push(item.status);
    if (Array.isArray(item.children) && item.children.length > 0) {
      statusList = statusList.concat(getChildrenStatus(item.children));
    }
  });
  return statusList;
}

function getActions() {
  return initActions(actionClick, userStore.getUserInfo, indexData);
}

// 切换页签
function tabsChange(index: number, item: { key: number }) {
  tabsKey.value = item.key;
  updateForm();
}

// 更多切换
const handleToggle = () => {
  isDropDown.value = !isDropDown.value;
};

defineExpose({
  viewDetail: onClickBulletFame,
});
</script>
<style lang="less" scoped>
:deep(.plan) {
  margin-top: -5px;
  .surely-table-cell-content {
    display: flex;
    align-items: center;
  }
  .surely-table-body {
    .surely-table-row.surely-table-row-hover,
    .surely-table-row.surely-table-row-hover {
      .add-icon {
        display: block;
      }
      .fw {
        display: block;
      }
      .fw-icon {
        display: block;
      }
      .st-icon {
        display: block;
      }
    }
  }
  .flex-row-center {
    display: flex;
  }
  .ant-space-align-center {
    justify-content: space-between;
    display: flex;
    width: 100%;
  }
  .ant-basic-table .surely-table-pagination.ant-pagination {
    margin-top: 15px;
    padding: 0;
  }
  .orion-table-header-wrap {
    min-width: 1280px;
    .flex-ac {
      padding: 0;
    }
    .toolbar-right {
      margin-right: 98px;
    }
    .plan-table {
      .flex:last-child {
        margin-right: -347px;
        margin-top: 5px;
      }
      .flex-pac {
        margin-top: 0 !important;
      }
    }
    & > .flex-f1.flex {
      display: flex;
      flex-direction: row;
      align-items: center;
      min-height: 48px;
      padding-bottom: 5px;
    }
    .tabs-wrap {
      box-shadow: none !important;
    }
  }
}
.plan-container {
  width: 100%;
  position: relative;
  .type-node {
    position: absolute;
    top: 20px;
    right: 30px;
    z-index: 2;
  }
}
.dis-body {
  padding: 22px 22px 30px;
}
.flex-box {
  display: flex;
  align-items: center;
  margin-top: 10px;

  > span {
    margin-right: 10px;
  }
}
.disable {
  color: #666;
}
.primary-color {
  color: ~`getPrefixVar("primary-color") `;
}
.hover-link {
  cursor: pointer;

  &:hover > span {
    color: ~`getPrefixVar("primary-color") `;
  }
}
.pre-post-tooltip {
  display: flex;
  flex-direction: column;
  font-size: 12px;
  padding: 0 5px;

  span {
    line-height: 1.5;
  }
}
.source-table-tab {
  position: relative;
  width: 360px;
  .f-icon {
    position: absolute;
    top: 18px;
    left: -1px;
  }
}
.source-table-slots {
  display: flex;
  gap: 10px 0;
  .ant-down-trigger {
    position: relative;
    margin-left: 10px;
    .dropdown-content {
      position: absolute;
      z-index: 9999;
      display: flex;
      flex-direction: column;
      background: #fff;
      padding: 10px;
      box-shadow: 1px 2px 8px #ddd;
      .basic-button.margin {
        margin: 5px 0;
      }
    }
    .icon-main-wrap {
      margin-left: 6px;
    }
    .ant-up-icon {
      transform: rotate(180deg);
    }
  }
  .expanded-keys-change {
    span {
      padding-right: 5px;
    }

    .ant-select {
      width: 80px;
    }
  }
}
.surely-table-body-container {
  .ant-dropdown-trigger {
    display: none;
  }
}
.source-table-slots-down {
  padding: 0 !important;
  .source-table-slots-add {
    display: flex;
    height: 32px;
    line-height: 32px;
  }

  .source-table-slots-add-label {
    padding-left: 12px;
    display: flex;
    align-items: center;
  }
  .ant-dropdown-trigger {
    height: 32px;
    line-height: 32px;
    padding: 0 12px;
    :deep(.icon-content) {
      left: 16% !important;
    }
  }
}
.plan-table {
  background-color: #fff;
  .ant-basic-table .surely-table-pagination.ant-pagination {
    margin-top: 15px;
    padding: 0;
  }
  :deep(.ant-dropdown-trigger) {
    display: none;
  }
  :deep(.ant-popover-inner-content) {
    padding: 1px 0;
  }
}
.ant-basic-table {
  .icon-container {
    display: flex;
    align-items: center;
  }
}
</style>
