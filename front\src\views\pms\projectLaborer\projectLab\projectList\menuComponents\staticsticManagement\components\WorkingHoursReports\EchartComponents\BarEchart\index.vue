<template>
  <div class="wrap">
    <div
      v-for="(item,index) in cardData"
      :key="index"
      class="card"
    >
      <span class="title">{{ item.name }}</span>
      <span class="number">{{ `${item.number}` }}</span>
      <div
        class="bar"
        :class="`bar_${index+1}`"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, watchEffect, onMounted } from 'vue';
const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
});
const cardData = ref();
watchEffect(() => {
  cardData.value = props.data;
});
</script>
<style lang="less" scoped>
.wrap{
  display: flex;
  justify-content: space-evenly;
  .card{
    width: 10%;
    display: flex;
    flex-direction: column;
    .title{
      color: #ccc;
      font-size: 12px;
    }
    .number{
      font-weight: bold;
      font-size: 20px;
    }
    .bar{
      width: 80%;
      height: 4px;
      border-radius:2px;
      background-color: #ccc;
    }
     .bar_1 {
      background-color: #08a9f2;
    }
    .bar_2 {
      background-color: #72b201;
    }
    .bar_3 {
      background-color: #FF828A;
    }
    .bar_4 {
      background-color: #FFA900;
    }
    .bar_5 {
      background-color: #FFA900;
    }
    .bar_6 {
      background-color: #FFA900;
    }
  }
}

</style>
