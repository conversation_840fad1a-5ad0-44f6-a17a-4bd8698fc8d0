<template>
  <div class="achievement-left">
    <div class="achievement-left-title">
      <BasicButton
        icon="add"
        type="primary"
        @click="addListItem"
      >
        新增文件
      </BasicButton>
    </div>
    <div class="achievement-left-content">
      <template
        v-for="item in leftList"
        :key="item.id"
      >
        <div
          class="left-item"
          :class="{'left-item-active':item.id===actionId}"
        >
          <MenuUnfoldOutlined />
          <div class="left-item-value">
            <Input
              v-if="item.edit"
              v-model:value="item.name"
              @pressEnter="pressEnter(item)"
            />
            <span
              v-else
              class="span-val flex-te"
              @click="actionClick(item)"
            >{{ item.name }}</span>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>
<script lang="ts" setup>
import {
  inject, defineProps, defineEmits, ref, onMounted, unref,
} from 'vue';
import { BasicButton } from 'lyra-component-vue3';
import { MenuUnfoldOutlined } from '@ant-design/icons-vue';
import { Input, message } from 'ant-design-vue';
import Api from '/@/api';

const props = defineProps<{
    formId: any,
    projectId:any
}>();
const emit = defineEmits(['actionClick']);
const leftList = ref([]);
const actionId = ref();
onMounted(() => {
  // for (let i = 0; i < 5; i++) {
  //   leftList.value.push({
  //     id: i + 1,
  //     name: `文件${i}`,
  //   });
  // }
  // actionId.value = leftList.value[0].id;
  // emit('actionClick', unref(actionId));
  getList();
});
function getList() {
  new Api('/pms').fetch('', `projectAchievementFloder/lists/${props.formId}`, 'POST').then((res) => {
    leftList.value = res;
    if (leftList.value.length > 0) {
      if (!actionId.value) {
        actionId.value = leftList.value[0].id;
      }
      emit('actionClick', leftList.value[0]);
    }
  });
}
function addListItem() {
  leftList.value.push({
    id: leftList.value.length + 2,
    name: `文件${leftList.value.length}`,
    edit: true,
    projectId: props.projectId,
  });
}
function pressEnter(item) {
  item.edit = false;
  let params = {
    name: item.name,
    approvalId: props.formId,
    projectId: props.projectId,
  };
  new Api('/pms').fetch(params, 'projectAchievementFloder', 'POST').then((res) => {
    message.success('新增文件成功');
    item.id = res.id;
  });
}
function actionClick(item) {
  actionId.value = item.id;
  emit('actionClick', item);
}
</script>
<style lang="less" scoped>
.achievement-left{
  .achievement-left-title{
    width:240px;
    background: white;
    position: fixed;
    height: 40px;
  }
  .achievement-left-content{
    padding-top: 40px;
    .left-item{
      color: #444b5e;
      display: flex;
      height: 40px;
      line-height: 40px;
      width: 100%;
      cursor: pointer;
      margin-top: 5px;

      &:hover{
        background: var(--ant-primary-color-deprecated-f-12) ;
        .left-item-value{
          color: var(--ant-primary-color);
        }
      }
      .anticon{
        font-size:18px;
        padding-right: 5px;
        :deep(svg){
          margin-top: 11px;
        }
      }
      .left-item-value{
        flex:1;
        width:200px;
        padding-right: 7px;
        .span-val{
          width: 100%;
          display: inline-block;
        }
      }
    }

    .left-item-active{
      background: var(--ant-primary-color-deprecated-f-12) ;
      .left-item-value{
        color: var(--ant-primary-color);
      }
    }
  }
}
</style>
