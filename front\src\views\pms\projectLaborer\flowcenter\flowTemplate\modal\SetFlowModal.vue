<template>
  <BasicModal
    v-bind="$attrs"
    title="设置分类"
    @register="registerModal"
  >
    <BasicForm
      :showActionButtonGroup="false"
      @register="register"
    />
    <template #footer>
      <a-button @click="closeModal">
        取消
      </a-button>
      <a-button
        type="primary"
        @click="validateForm"
      >
        确定
      </a-button>
    </template>
  </BasicModal>
</template>
<script lang="ts">
import { defineComponent, toRefs, reactive } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { Descriptions, Tabs, message } from 'ant-design-vue';
import Api from '/@/api/index';
import { workflowApi } from '../../util/apiConfig';
import { BasicForm, FormSchema, useForm } from '/@/components/Form/index';

interface paramsType {
  id: string;
  classifyId: string;
}

export default defineComponent({
  components: {
    BasicModal,
    aDescriptions: Descriptions,
    aTabs: Tabs,
    BasicForm,
  },
  props: {
    treeData: Object,
  },
  setup(props, { emit }) {
    // 使用 `toRefs` 创建对prop的 `data` property 的响应式引用
    const { treeData }: any = toRefs(props);
    const apiOrg = new Api(`${workflowApi}/act-flow/batch`);
    const state: any = reactive({
      list: [],
    });
    // 定义表单的字段规则
    const schemas: FormSchema[] = [
      {
        field: 'parentId',
        label: '所属分类',
        colProps: {
          span: 20,
        },
        component: 'Cascader',
        componentProps: {
          displayRender({ labels }: any) {
            // 只显示最后一级文字
            return labels[labels.length - 1];
          },
          fieldNames: {
            label: 'name',
            value: 'id',
          },
          changeOnSelect: true,
          options: treeData,
        },
      },
    ];

    // 注册一个表单
    const [register, { validateFields }] = useForm({
      labelWidth: 120,
      schemas,
      actionColOptions: {
        span: 24,
      },
    });

    // 弹窗内部的注册函数,可以在内部自己关闭
    const [registerModal, { closeModal }] = useModalInner((data) => {
      state.list = data;
    });
    /*
     * 验证增加和编辑时候的表单
     * */
    async function validateForm() {
      const res = await validateFields();
      const paramsData: paramsType[] = [];
      const classifyId = res.parentId[res.parentId.length - 1];
      state.list.forEach((item) => {
        paramsData.push({
          classifyId,
          id: item.id,
        });
      });
      apiOrg.update(paramsData).then(() => {
        closeModal();
        emit('updateTable', classifyId);
        message.success('更改成功');
      });
    }

    return {
      registerModal,
      closeModal,
      register,
      validateForm,
    };
  },
});
</script>
