<script lang="ts" setup>
import {
  BasicButton, BasicButtonGroup, isPower, Layout, randomString,
} from 'lyra-component-vue3';
import { Row, Space, DatePicker as ADatePicker } from 'ant-design-vue';
import {
  computed,
  onActivated, onMounted, provide, Ref, ref, watchEffect,
} from 'vue';
import { filter, get } from 'lodash-es';
import DisplayInCenter from '../views/DisplayInCenter.vue';
import DisplayToContract from '../views/DisplayToContract.vue';
import {
  useImportOrExportContractPlan,
} from '../hooks/useImportOrExportContractPlan';
import {
  useUpdateContractList,
} from '../statusStoreManage/useUpdateContractList';
import {
  useContractViewTableHeader,
} from '../statusStoreManage/useContractViewTableHeader';
import {
  useBatchOperateByTableRowKeys,
} from '../statusStoreManage/useBatchOperateByTableRowKeys';

const {
  updateContractKey, currentFilterYear,
} = useUpdateContractList();
const { getTableDynamicTheadHeaders, tableDynamicTheadHeaders } = useContractViewTableHeader();
const { importApi, exportApi, OrderImportRender } = useImportOrExportContractPlan();
const {
  setSelectRows,
  selectRows,
  batchDeleteRows,
  openNextYearEntry,
  openCurrentYearAdjustment,
  issueAllContractPlan,
} = useBatchOperateByTableRowKeys();

const currRenderViewDisplay = ref<'center'|'contract'>('center');
const updateRefreshKey = ref(randomString(30));
const displayInCenterRef = ref();
const powerData = ref();
function importCallBack() {
  handleUpdateKey();
}
function onSwitchView(type) {
  setSelectRows([]);
  handleSwitchView(type);
}
function exportBudgetStatement() {
  displayInCenterRef.value?.exportAllData?.();
}
function getPowerDataHandle(data) {
  powerData.value = data;
}

const renderViewController = computed(() => filter([
  {
    label: '按中心展示',
    name: 'center',
    code: 'PMS_JSHTPZ_container_01_pageView_01',
  },
  {
    label: '按合同展示',
    name: 'contract',
    code: 'PMS_JSHTPZ_container_01_pageView_02',
  },
], (item) => isPower(item.code, powerData.value)));
function handleSwitchView(type) {
  currRenderViewDisplay.value = type;
}

watchEffect(() => {
  handleSwitchView(get(renderViewController.value, '0.name'));
});
onMounted(async () => {
  await getTableDynamicTheadHeaders();
});

const renderKey: Ref<string> = ref();
const isKeep: Ref<boolean> = ref(false);
onActivated(async () => {
  if (!isKeep.value) return isKeep.value = true;
  renderKey.value = randomString();
  handleSwitchView(currRenderViewDisplay.value);
  await getTableDynamicTheadHeaders();
});

function handleUpdateKey() {
  updateRefreshKey.value = randomString(40);
}
function handleChange() {
  handleUpdateKey();
}
provide('homeRenderCtx', updateRefreshKey);
provide('powerData', powerData);
</script>

<template>
  <Layout
    v-get-power="{pageCode: 'PMSTechCfgContractManage',getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
  >
    <div
      class="tech-cfg-contract-manage"
    >
      <Row justify="space-between">
        <Space :size="3">
          <BasicButton
            v-if="isPower('PMS_JSHTPZ_container_001_Header_button_01',powerData)"
            class="primary-color"
            icon="sie-icon-daoru"
            type="primary"
            @click="()=>{
              handleUpdateKey();
              importApi()
            }"
          >
            导入合同数据
          </BasicButton>
          <BasicButton
            v-if="[
              currRenderViewDisplay==='contract',
              isPower('PMS_JSHTPZ_container_001_Header_button_02',powerData)].every(Boolean)"
            :disabled="!selectRows?.length"
            icon="delete"
            @click="batchDeleteRows(handleUpdateKey)"
          >
            删除
          </BasicButton>
          <BasicButton
            v-if="[
              currRenderViewDisplay==='contract',
              isPower('PMS_JSHTPZ_container_001_Header_button_03',powerData)].every(Boolean)"
            @click="()=>{
              issueAllContractPlan(handleUpdateKey)
            }"
          >
            全部下发
          </BasicButton>
          <BasicButton
            v-if="[
              currRenderViewDisplay==='center',
              isPower('PMS_JSHTPZ_container_001_Header_button_04',powerData)].every(Boolean)"
            @click="openNextYearEntry(handleUpdateKey)"
          >
            开启下年录入
          </BasicButton>
          <BasicButton
            v-if="[
              currRenderViewDisplay==='center',
              isPower('PMS_JSHTPZ_container_001_Header_button_05',powerData)].every(Boolean)"
            @click="openCurrentYearAdjustment(handleUpdateKey)"
          >
            开启当年调整
          </BasicButton>
          <BasicButton
            v-if="[
              currRenderViewDisplay==='center',
              isPower('PMS_JSHTPZ_container_001_Header_button_05',powerData)].every(Boolean)"
            @click="exportBudgetStatement"
          >
            导出预算报表
          </BasicButton>
        </Space>
        <Space
          :size="12"
          align="end"
        >
          <a-date-picker
            v-model:value="currentFilterYear"
            picker="year"
            format="YYYY年"
            value-format="YYYY"
            :allow-clear="false"
            @change="handleChange"
          />
          <BasicButtonGroup v-if="renderViewController.length>1">
            <BasicButton
              v-for="(btnCol,idx) in renderViewController"
              :key="idx"
              :type="currRenderViewDisplay===btnCol.name?'primary':'default'"
              :class="currRenderViewDisplay===btnCol.name?'primary-color':''"
              @click="onSwitchView(btnCol.name)"
            >
              {{ btnCol.label }}
            </BasicButton>
          </BasicButtonGroup>
        </Space>
      </Row>
      <div
        class="view-display-render"
      >
        <DisplayInCenter
          v-if="currRenderViewDisplay==='center'"
          ref="displayInCenterRef"
        />
        <DisplayToContract v-else-if="currRenderViewDisplay==='contract'" />
      </div>
    </div>
    <OrderImportRender
      :key="renderKey"
      :cb="importCallBack"
    />
  </Layout>
</template>

<style lang="less" scoped>
.tech-cfg-contract-manage{
  width: 100%;
  height: 100%;
  padding: ~`getPrefixVar('content-padding-top')` ~`getPrefixVar('content-padding-left')`;
  .primary-color{
    color: #fff;
  }
  .view-display-render{
    width: 100%;
    height: calc(100% - 32px);
  }
}
</style>
