package com.chinasie.orion.service.Impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.bo.PmsMQProducer;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.domain.dto.ActualPayMilestoneDTO;
import com.chinasie.orion.domain.entity.ActualPayMilestone;
import com.chinasie.orion.domain.entity.ContractInfo;
import com.chinasie.orion.domain.entity.PurchaseExecuteShcnge;
import com.chinasie.orion.domain.vo.ActualPayMilestoneVO;
import com.chinasie.orion.repository.ActualPayMilestoneMapper;
import com.chinasie.orion.service.ActualPayMilestoneService;
import com.chinasie.orion.service.ContractInfoService;
import com.chinasie.orion.service.PurchaseExecuteShcngeService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <p>
 * ActualPayMilestone 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@Service
@Slf4j
public class ActualPayMilestoneServiceImpl extends OrionBaseServiceImpl<ActualPayMilestoneMapper, ActualPayMilestone> implements ActualPayMilestoneService {



    @Autowired
    @Lazy
    private ContractInfoService contractInfoService;

    @Autowired
    private OrionJ2CacheService orionJ2CacheService;
    @Autowired
    private UserRedisHelper userRedisHelper;
    @Autowired
    private PurchaseExecuteShcngeService purchaseExecuteShcngeService;

    @Autowired
    private PmsMQProducer mqProducer;



    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ActualPayMilestoneVO detail(String id, String pageCode) throws Exception {
        ActualPayMilestone actualPayMilestone = this.getById(id);
        ActualPayMilestoneVO result = BeanCopyUtils.convertTo(actualPayMilestone, ActualPayMilestoneVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param actualPayMilestoneDTO
     */
    @Override
    public String create(ActualPayMilestoneDTO actualPayMilestoneDTO) throws Exception {
        ActualPayMilestone actualPayMilestone = BeanCopyUtils.convertTo(actualPayMilestoneDTO, ActualPayMilestone::new);
        this.save(actualPayMilestone);

        String rsp = actualPayMilestone.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param actualPayMilestoneDTO
     */
    @Override
    public Boolean edit(ActualPayMilestoneDTO actualPayMilestoneDTO) throws Exception {
        ActualPayMilestone actualPayMilestone = BeanCopyUtils.convertTo(actualPayMilestoneDTO, ActualPayMilestone::new);

        //插入变更记录表
        //数据库中数据
        ActualPayMilestone old = this.getById(actualPayMilestoneDTO.getId());
        if ((old.getIsOnetimeAcceptance() == null && actualPayMilestoneDTO.getIsOnetimeAcceptance() != null) || (old.getIsOnetimeAcceptance() != null && actualPayMilestoneDTO.getIsOnetimeAcceptance() == null) || (old.getIsOnetimeAcceptance() != null && actualPayMilestoneDTO.getIsOnetimeAcceptance() != null && !old.getIsOnetimeAcceptance().equals(actualPayMilestoneDTO.getIsOnetimeAcceptance()))) {
            PurchaseExecuteShcnge dto = new PurchaseExecuteShcnge();
            UserVO issuer = userRedisHelper.getUserById(CurrentUserHelper.getCurrentUserId());
            dto.setChanger(issuer.getName());
            dto.setChangeDate(new Date());
            dto.setChangeContent("是否一次验收合格");
            dto.setBeforeChange(Objects.equals(true, old.getIsOnetimeAcceptance()) ? "是" : Objects.equals(false, old.getIsOnetimeAcceptance()) ? "否" : null);
            dto.setAfterChange(actualPayMilestoneDTO.getIsOnetimeAcceptance() == null ? null : actualPayMilestoneDTO.getIsOnetimeAcceptance());
            dto.setContractNumber(old.getContractNumber());
            dto.setContractName(old.getContractName());
            purchaseExecuteShcngeService.save(dto);
        }
        if ((old.getActualAcceptanceTime() == null && actualPayMilestoneDTO.getActualAcceptanceTime() != null) || (old.getActualAcceptanceTime() != null && actualPayMilestoneDTO.getActualAcceptanceTime() == null) || (old.getActualAcceptanceTime() != null && actualPayMilestoneDTO.getActualAcceptanceTime() != null && old.getActualAcceptanceTime().compareTo(actualPayMilestoneDTO.getActualAcceptanceTime()) != 0)) {
            PurchaseExecuteShcnge dto = new PurchaseExecuteShcnge();
            UserVO issuer = userRedisHelper.getUserById(CurrentUserHelper.getCurrentUserId());
            dto.setChanger(issuer.getName());
            dto.setChangeDate(new Date());
            dto.setChangeContent("实际验收时间");
            dto.setBeforeChange(old.getActualAcceptanceTime() == null ? null : old.getActualAcceptanceTime().toString());
            dto.setAfterChange(actualPayMilestoneDTO.getActualAcceptanceTime() == null ? null : actualPayMilestoneDTO.getActualAcceptanceTime().toString());
            dto.setContractNumber(old.getContractNumber());
            dto.setContractName(old.getContractName());
            purchaseExecuteShcngeService.save(dto);
        }
        if ((old.getReason() == null && actualPayMilestoneDTO.getReason() != null) || (old.getReason() != null && actualPayMilestoneDTO.getReason() == null) || (old.getReason() != null && actualPayMilestoneDTO.getReason() != null && !old.getReason().equals(actualPayMilestoneDTO.getReason()))) {
            PurchaseExecuteShcnge dto = new PurchaseExecuteShcnge();
            UserVO issuer = userRedisHelper.getUserById(CurrentUserHelper.getCurrentUserId());
            dto.setChanger(issuer.getName());
            dto.setChangeDate(new Date());
            dto.setChangeContent("原因");
            dto.setBeforeChange(old.getReason() == null ? null : old.getReason());
            dto.setAfterChange(actualPayMilestoneDTO.getReason() == null ? null : actualPayMilestoneDTO.getReason());
            dto.setContractNumber(old.getContractNumber());
            dto.setContractName(old.getContractName());
            purchaseExecuteShcngeService.save(dto);
        }
        if ((old.getActualDeliveryTime() == null && actualPayMilestoneDTO.getActualDeliveryTime() != null) || (old.getActualDeliveryTime() != null && actualPayMilestoneDTO.getActualDeliveryTime() == null) || (old.getActualDeliveryTime() != null && actualPayMilestoneDTO.getActualDeliveryTime() != null && old.getActualDeliveryTime().compareTo(actualPayMilestoneDTO.getActualDeliveryTime()) != 0)) {
            PurchaseExecuteShcnge dto = new PurchaseExecuteShcnge();
            UserVO issuer = userRedisHelper.getUserById(CurrentUserHelper.getCurrentUserId());
            dto.setChanger(issuer.getName());
            dto.setChangeDate(new Date());
            dto.setChangeContent("实际交付时间");
            dto.setBeforeChange(old.getActualDeliveryTime() == null ? null : old.getActualDeliveryTime().toString());
            dto.setAfterChange(actualPayMilestoneDTO.getActualDeliveryTime() == null ? null : actualPayMilestoneDTO.getActualDeliveryTime().toString());
            dto.setContractNumber(old.getContractNumber());
            dto.setContractName(old.getContractName());
            purchaseExecuteShcngeService.save(dto);
        }

        this.updateById(actualPayMilestone);
        String rsp = actualPayMilestone.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ActualPayMilestoneVO> pages(String mainTableId, Page<ActualPayMilestoneDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ActualPayMilestone> condition = new LambdaQueryWrapperX<>(ActualPayMilestone.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ActualPayMilestone::getCreateTime);

        condition.eq(ActualPayMilestone::getMainTableId, mainTableId);

        Page<ActualPayMilestone> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ActualPayMilestone::new));

        PageResult<ActualPayMilestone> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ActualPayMilestoneVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ActualPayMilestoneVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ActualPayMilestoneVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ActualPayMilestoneVO> getByCode(Page<ActualPayMilestoneDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<ActualPayMilestone> condition = new LambdaQueryWrapperX<>(ActualPayMilestone.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.eq(ActualPayMilestone::getContractNumber, pageRequest.getQuery().getContractNumber());
        condition.orderByDesc(ActualPayMilestone::getCreateTime);
        Page<ActualPayMilestone> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ActualPayMilestone::new));

        PageResult<ActualPayMilestone> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ActualPayMilestoneVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ActualPayMilestoneVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ActualPayMilestoneVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "合同支付里程碑（实际）导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ActualPayMilestoneDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ActualPayMilestoneExcelListener excelReadListener = new ActualPayMilestoneExcelListener();
        EasyExcel.read(inputStream, ActualPayMilestoneDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ActualPayMilestoneDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("合同支付里程碑（实际）导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ActualPayMilestone> actualPayMilestonees = BeanCopyUtils.convertListTo(dtoS, ActualPayMilestone::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("ncf::ActualPayMilestone-import::id", importId, actualPayMilestonees, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ActualPayMilestone> actualPayMilestonees = (List<ActualPayMilestone>) orionJ2CacheService.get("ncf::ActualPayMilestone-import::id", importId);
        log.info("合同支付里程碑（实际）导入的入库数据={}", JSONUtil.toJsonStr(actualPayMilestonees));

        this.saveBatch(actualPayMilestonees);
        orionJ2CacheService.delete("ncf::ActualPayMilestone-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("ncf::ActualPayMilestone-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ActualPayMilestone> condition = new LambdaQueryWrapperX<>(ActualPayMilestone.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ActualPayMilestone::getCreateTime);
        List<ActualPayMilestone> actualPayMilestonees = this.list(condition);

        List<ActualPayMilestoneDTO> dtos = BeanCopyUtils.convertListTo(actualPayMilestonees, ActualPayMilestoneDTO::new);

        String fileName = "合同支付里程碑（实际）数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ActualPayMilestoneDTO.class, dtos);

    }

    @Override
    public void sendEmailAndRemind() {
        LambdaQueryWrapperX<ActualPayMilestone> condition = new LambdaQueryWrapperX<>(ActualPayMilestone.class);
        condition.isNotNull(ActualPayMilestone::getActualAcceptanceTime);
        condition.isNotNull(ActualPayMilestone::getEstimatedAcceptanceTime);
        condition.apply("estimated_acceptance_time < actual_acceptance_time");
        condition.isNull(ActualPayMilestone::getIsAcceptanceQualified);
        condition.eq(ActualPayMilestone::getPayType, "验收款");

        List<ActualPayMilestone> actualPayMilestones = this.list(condition);
        if (!CollectionUtils.isEmpty(actualPayMilestones)) {
            List<String> contractNumbers = actualPayMilestones.stream().map(ActualPayMilestone::getContractNumber).collect(Collectors.toList());
            List<ContractInfo> contractInfos = contractInfoService.list(new LambdaQueryWrapperX<ContractInfo>().in(ContractInfo::getContractNumber, contractNumbers));
            if (!CollectionUtils.isEmpty(contractInfos)) {
                sendEmailAndRemind(actualPayMilestones, contractInfos);
            }
        }
    }

    public void sendEmailAndRemind(List<ActualPayMilestone> actualPayMilestones, List<ContractInfo> contractInfos) {
        actualPayMilestones.forEach(vo -> {
            ContractInfo contract = contractInfos.stream().filter(contractInfo -> contractInfo.getContractNumber().equals(vo.getContractNumber())).findFirst().orElse(new ContractInfo());
            List<String> recipientIdList = new ArrayList<>();
            recipientIdList.add(contract.getBusinessRspUser());
            //技术负责人暂无字段

            Map<String, Object> businessData = new HashMap<>();
            businessData.put("contractName", contract.getContractName());
            businessData.put("contractNumber", contract.getContractNumber());
            businessData.put("url", "http://***********:1199/pms/purchaseContract");
            SendMessageDTO sendMsc = SendMessageDTO.builder()
                    .businessId(vo.getId())
                    .todoStatus(0)
                    .businessNodeCode("CONTRACT_MILESTONE_NOTICE")
                    .todoType(TodoTypeDict.TODO_TYPE_TASK)
                    .senderId(contract.getBusinessRspUser())
                    .recipientIdList(recipientIdList)
                    .senderTime(new Date())
                    .platformId(vo.getPlatformId())
                    .orgId(vo.getOrgId())
                    .businessData(JSON.toJSONString(businessData))
                    .build();
            mqProducer.sendPmsMessage(sendMsc);
        });
    }

    @Override
    public void setEveryName(List<ActualPayMilestoneVO> vos) throws Exception {


        vos.forEach(vo -> {
            if(StringUtils.isNotBlank(vo.getPayRatio())){
                vo.setPayRatio(vo.getPayRatio()+"%");
            }

        });


    }


    public static class ActualPayMilestoneExcelListener extends AnalysisEventListener<ActualPayMilestoneDTO> {

        private final List<ActualPayMilestoneDTO> data = new ArrayList<>();

        @Override
        public void invoke(ActualPayMilestoneDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ActualPayMilestoneDTO> getData() {
            return data;
        }
    }


}
