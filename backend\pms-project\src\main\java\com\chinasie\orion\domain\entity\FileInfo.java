package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/08/14:52
 * @description:
 */
@Data
@TableName(value = "pms_file_info")
@ApiModel(value = "FileInfo对象", description = "文件信息表")
public class FileInfo extends ObjectEntity {


    /**
     * 所属数据ID
     */
    @ApiModelProperty(value = "所属数据ID")
    @TableField(value = "data_id")
    private String dataId;

    /**
     * 文件大小
     */
    @ApiModelProperty(value = "文件大小")
    @TableField(value = "file_size")
    private Long fileSize;

    /**
     * 文件后缀
     */
    @ApiModelProperty(value = "文件后缀")
    @TableField(value = "file_postfix")
    private String filePostfix;

    /**
     * 文件路径
     */
    @ApiModelProperty(value = "文件路径")
    @TableField(value = "file_path")
    private String filePath;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    @TableField(value = "rev_id")
    private String revId;

    @ApiModelProperty(value = "签入签出")
    @TableField(value = "check_in", exist = false)
    private String checkIn;
}
