<template>
  <Layout
    :options="{ body: { scroll: true } }"
  >
    <UploadList
      :listApi="listApi"
      :saveApi="saveApi"
      :deleteApi="deleteApi"
      :batchDeleteApi="batchDeleteApi"
    />
  </layout>
</template>
<script setup lang="ts">
import {
  ref, onMounted, computed,
} from 'vue';
import { useRoute } from 'vue-router';
import {
  BasicButton, Layout, OrionTable, BasicUpload, downLoadById, UploadList,
} from 'lyra-component-vue3';
import { message, Modal } from 'ant-design-vue';
import Api from '/@/api';
import { getBudgeting } from '/@/views/pms/api/costManage';
import dayjs from 'dayjs';
import { removeBatchDetailsApi } from '/@/views/pms/projectLaborer/api/endManagement';
const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});
const route = useRoute();
const tableRef = ref(null);
const selectRows = ref([]);
const tableData = ref([]);

async function listApi(params) {
  return new Api(`/res/manage/file/new/${route.query?.id}`).fetch('', '', 'GET');
}

async function saveApi(files) {
  let api = '/pms/document/saveBatch';
  let fieldList = files.map((item) => {
    item.dataId = route.query?.id;
    return item;
  });
  return new Api(api).fetch(fieldList, '', 'POST');
}

async function deleteApi(deleteApi) {
  return batchDelete([deleteApi.id]);
}

async function batchDeleteApi({ keys, rows }) {
  if (keys.length === 0) {
    message.warning('请选择文件');
    return;
  }
  return batchDelete(rows.map((item) => item.id));
}

function getTableData() {
  new Api(`/res/manage/file/new/${route.query?.id}`).fetch('', '', 'GET').then((res) => {
    tableData.value = res.map(({ children, ...rest }) => rest);
  });
}

onMounted(() => {
  getTableData();
});
function handleSaveFile(successAll, cb) {
  const dataId = route.query?.id;
  const files = successAll.map((item) => ({
    ...item.result,
    dataId,
  }));
  cb(new Api('/pms/document/saveBatch').fetch(files, '', 'POST').then(() => {
    upTableDate();
  }));
}
function batchDownload() {
  for (let i = 0; i < selectRows.value.length; i++) {
    downLoadById(selectRows.value[i]?.id);
  }
}

function selectionChange({ rows }) {
  selectRows.value = rows;
}
function handleBatchDel() {
  Modal.confirm({
    title: '删除提示',
    content: '确认要删除已选择的评价？',
    onOk: () => batchDelete(selectRows.value.map((item) => item.id)),
  });
}
function batchDelete(ids) {
  return new Promise((resolve, reject) => {
    new Api('/res/manage/file').fetch(ids, '', 'DELETE')
      .then(() => {
        upTableDate();
        resolve(true);
      })
      .catch(() => {
        reject();
      });
  });
}
function upTableDate() {
  tableRef.value?.reload();
  getTableData();
}
function evaluationName(record) {
  // openEvaluateDrawer(true, { type: 'details', formData: record, projectId: props.id });
}
</script>
<style scoped lang="less">
</style>
