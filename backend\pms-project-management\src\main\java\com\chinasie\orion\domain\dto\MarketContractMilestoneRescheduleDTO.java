package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * MarketContractMilestoneReschedule DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-30 02:39:54
 */
@ApiModel(value = "MarketContractMilestoneRescheduleDTO对象", description = "市场合同里程碑改期信息")
@Data
@ExcelIgnoreUnannotated
public class MarketContractMilestoneRescheduleDTO extends  ObjectDTO   implements Serializable{

    /**
     * 变更原因
     */
    @ApiModelProperty(value = "变更原因")
    @ExcelProperty(value = "变更原因 ", index = 0)
    private String changeReason;

    /**
     * 里程碑id
     */
    @ApiModelProperty(value = "里程碑id")
    @ExcelProperty(value = "里程碑id ", index = 1)
    @NotEmpty(message = "里程碑id不能为空")
    private String milestoneId;

    /**
     * 原预计验收日期
     */
    @ApiModelProperty(value = "原预计验收日期")
    @ExcelProperty(value = "原预计验收日期 ", index = 2)
    private Date oldExpectAcceptDate;

    /**
     * 新预计验收日期
     */
    @ApiModelProperty(value = "新预计验收日期")
    @ExcelProperty(value = "新预计验收日期 ", index = 3)
    @NotNull(message = "新预计验收日期不能为空")
    private Date newExpectAcceptDate;
    /**
     * 初始预估验收日期
     */
    @ApiModelProperty(value = "初始预估验收日期")
    @ExcelProperty(value = "初始预估验收日期 ", index = 4)
    private Date expectAcceptDate;

    /**
     * 预估验收金额
     */
    @ApiModelProperty(value = "预估验收金额")
    @ExcelProperty(value = "预估验收金额 ", index = 5)
    private BigDecimal acceptMoney;

    /**
     * 预计开票日期
     */
    @ApiModelProperty(value = "预计开票日期")
    @ExcelProperty(value = "预计开票日期 ", index = 6)
    private Date billingDate;

    /**
     * 原预估验收金额
     */
    @ApiModelProperty(value = "原预估验收金额")
    @ExcelProperty(value = "预计开票日期 ", index = 7)
    private BigDecimal oldAcceptMoney;




}
