package com.chinasie.orion.domain.dto.quality;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * project: sieWorkCode
 * <p>  </p>
 *
 * @author: xxll
 * Date: 2024-08-2024/8/23【星期五】
 */
@Data
public class QualityItemToProductDTO {

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 产品信息
     */
    @ApiModelProperty(value = "产品IDS")
    List<QualityItemProductDTO> productList;

    /**
     * 质量管控项IDS
     */
    @ApiModelProperty(value = "质量管控项IDS")
    List<String> qualityItemIds;
}
