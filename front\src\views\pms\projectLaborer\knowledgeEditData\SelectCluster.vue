<template>
  <a-modal
    v-model:visible="data.visible"
    :title="data.title"
    @ok="handleOk"
  >
    <div class="box">
      <div class="title">
        已选择的分类：{{ text }}
      </div>
      <Alert
        v-if="data.message"
        style="margin-bottom: 10px"
        type="info"
        :message="data.message"
        show-icon
      />
      <a-cascader
        v-model:value="value"
        class="content"
        style="width: 100%"
        change-on-select
        :options="options"
        :fieldNames="fieldNames"
        :show-search="{ filter }"
        placeholder="请选择分类"
        :loading="true"
        @change="onChange"
      />
    </div>
  </a-modal>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
import {
  Cascader, message, Modal, Alert,
} from 'ant-design-vue';
import Api from '/@/api';
export default defineComponent({
  name: 'SelectCluster',
  components: {
    AModal: Modal,
    ACascader: <PERSON><PERSON>,
    <PERSON><PERSON>,
  },
  props: {
    data: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  setup(props, { emit }) {
    const state = reactive({
      text: '',
      selectedOptions: [],
      value: [],
      fieldNames: {
        label: 'name',
        value: 'id',
        children: 'children',
      },
      options: [],
    });
    function filter(inputValue, path) {
      return path.some(
        (option) => option.name.toLowerCase().indexOf(inputValue.toLowerCase()) > -1,
      );
    }
    function onChange(_, selectedOptions) {
      state.text = selectedOptions.map((o) => o.name).join(' > ');
      state.selectedOptions = selectedOptions;
    }
    function init() {
      new Api('/kms/baseClassify').fetch({ show: false }, '', 'GET').then((data) => {
        state.options = filterChildren(data);
      });
    }
    function filterChildren(arr) {
      for (const s of arr) {
        if (props.data.disabled) {
          s.disabled = !s.modelId;
        }
        if (s.children.length) {
          filterChildren(s.children);
        } else {
          delete s.children;
        }
      }
      return arr;
    }
    function handleOk() {
      if (state.value.length === 0) {
        return message.warning('请选择分类');
      }
      const node = state.selectedOptions[state.selectedOptions.length - 1];
      const data = {
        ...props.data,
        id: node.id,
        name: state.text,
        node,
      };
      emit('submit', data);
      props.data.visible = false;
    }
    init();
    return {
      ...toRefs(state),
      ...toRefs(props),
      handleOk,
      filter,
      onChange,
    };
  },
});
</script>

<style scoped>
  .box {
    height: 350px;
    margin: 10px;
  }

  .title {
    background-color: rgb(233, 238, 241);
    padding: 6px 15px;
    margin-bottom: 10px;
  }

  .content {
    margin-top: 10px !important;
    margin-left: 10px !important;
    width: 100% !important;
  }
</style>
