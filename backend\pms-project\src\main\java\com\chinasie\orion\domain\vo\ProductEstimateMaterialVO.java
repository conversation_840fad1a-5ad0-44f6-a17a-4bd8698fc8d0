package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel(value = "ProductEstimateMaterialVO对象", description = "产品物料")
@Data
public class ProductEstimateMaterialVO extends ObjectVO {
    @ApiModelProperty(value = "产品名称")
    private String name;

    @ApiModelProperty(value = "产品编码")
    private String number;

    @ApiModelProperty(value = "产品目录")
    private String matterDir;

    @ApiModelProperty(value = "产品分类")
    private String productClassify;

    @ApiModelProperty(value = "产品分类名称")
    private String productClassifyName;

    @ApiModelProperty(value = "军/民品分类")
    private String MilitaryCivilian;

    @ApiModelProperty(value = "军/民品分类名称")
    private String MilitaryCivilianName;

    @ApiModelProperty(value = "物料类别")
    private String materialType;

    @ApiModelProperty(value = "物料类别名称")
    private String materialTypeName;

    @ApiModelProperty(value = "产品组")
    private String productGroup;

    @ApiModelProperty(value = "产品组名称")
    private String productGroupName;

    @ApiModelProperty(value = "产品型号")
    private String productModelNumber;

    @ApiModelProperty(value = "物料数量")
    private Integer materialAmount;

    @ApiModelProperty(value = "物料价格")
    private BigDecimal materialPrice;

    @ApiModelProperty(value = "父级id")
    private String parentId;

    @ApiModelProperty(value = "产品/物料类型类型")
    private String productMaterialType;
    @ApiModelProperty(value = "WS状态批准时间 ")
    private Date csDate;

    @ApiModelProperty(value = "CS状态批准时间 ")
    private Date wsDate;

    @ApiModelProperty(value = "oa编码 ")
    private String oaNumber;

    @ApiModelProperty(value = "国产化管控 ")
    private String localizedControl;


    @ApiModelProperty(value = "国产化管控 ")
    private String localizedControlName;


    @ApiModelProperty(value = "高质量等级 ")
    private String highQualityLevel;


    @ApiModelProperty(value = "高质量等级名称")
    private String highQualityLevelName;


    @ApiModelProperty(value = "产品二级分类 ")
    private String productSecondClassify;

    @ApiModelProperty(value = "产品二级分类名称 ")
    private String productSecondClassifyName;

    @ApiModelProperty(value = "产品/物料编码")
    private String materialNumber;

    @ApiModelProperty(value = "子项")
    private List<ProductEstimateMaterialVO> children;
}

