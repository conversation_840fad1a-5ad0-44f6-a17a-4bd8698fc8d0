<script setup lang="ts">
import { DataStatusTag, OrionTable } from 'lyra-component-vue3';
import Api from '/@/api';
import { h, ref, Ref } from 'vue';
import {
  getDateTime,
} from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/technicalList/data';

const props = defineProps<{
  projectId:string
}>();

const getTableRef = () => tableRef.value;
const tableRef:Ref = ref();
const tableOptions = {
  deleteToolButton: 'add|delete|enable|disable',
  showIndexColumn: true,
  rowSelection: {},
  smallSearchField: ['name', 'number'],
  searchInfo: {
    query: { projectId: props.projectId },
  },
  columns: [
    {
      title: '编号',
      dataIndex: 'number',
    },
    {
      title: '名称',
      dataIndex: 'name',
      slots: { customRender: 'name' },
    },
    {
      title: '状态',
      dataIndex: 'dataStatus',
      customRender({ record }) {
        return h(DataStatusTag, {
          statusData: record.dataStatus,
        });
      },
    },
    {
      title: '计划提交时间',
      dataIndex: 'planSubmitTime',
      customRender: ({ record: { planSubmitTime } }) => getDateTime(planSubmitTime, 'YYYY-MM-DD'),
    },
    {
      title: '编写人',
      dataIndex: 'writerName',
    },
    {
      title: '当前责任方',
      dataIndex: 'resPersonName',
    },
    {
      title: '责任部门',
      dataIndex: 'resDeptName',
    },
  ],
  api(params) {
    const url = '/pms/deliverGoals';
    return new Api(url).getPage(params);
  },
};
defineExpose({
  getTableRef,
});
</script>

<template>
  <div class="content-box-table">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    />
  </div>
</template>

<style scoped lang="less">
.content-box-table{
  overflow: hidden;
  height: 100%;
}
</style>
