package com.chinasie.orion.domain.dto.excel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/09/27/9:57
 * @description:
 */
@Data
public class JobProgressParamDTO implements Serializable {
    @ApiModelProperty(value = "作业ID")
    @NotEmpty(message = "所属作业不能为空")
    private String jobId;
    @ApiModelProperty(value = "大修伦次")
    private  String repairRound;
}
