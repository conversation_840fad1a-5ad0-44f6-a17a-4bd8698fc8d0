package com.chinasie.orion.domain.dto.pdm;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * ParameterPool VO对象
 *
 * <AUTHOR>
 * @since 2024-01-29 18:32:16
 */
@ApiModel(value = "ParameterPoolVO对象", description = "参数")
@Data
public class ParameterPoolVO extends ObjectVO implements Serializable {

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 目录主键
     */
    @ApiModelProperty(value = "目录主键")
    private String dirId;
    /**
     * 目录主键名称
     */
    @ApiModelProperty(value = "目录主键名称")
    private String dirName;


    /**
     * 提供部门
     */
    @ApiModelProperty(value = "提供部门")
    private List<String> providerDeptIds;


    /**
     * 提供部门名称
     */
    @ApiModelProperty(value = "提供部门")
    private String providerDeptNames;

    /**
     * 别名
     */
    @ApiModelProperty(value = "别名")
    private List<String> aliases;


}
