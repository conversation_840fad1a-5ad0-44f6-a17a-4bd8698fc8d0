package com.chinasie.orion.service.quality.impl;

import com.chinasie.orion.domain.entity.quality.QualityItemToProduct;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.quality.QualityItemToProductMapper;
import com.chinasie.orion.service.quality.QualityItemToProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * project: sieWorkCode
 * <p>  </p>
 *
 * @author: xxll
 * Date: 2024-08-2024/8/23【星期五】
 */
@Service
@Slf4j
public class QualityItemToProductServiceImpl extends  OrionBaseServiceImpl<QualityItemToProductMapper, QualityItemToProduct>   implements QualityItemToProductService {

}
