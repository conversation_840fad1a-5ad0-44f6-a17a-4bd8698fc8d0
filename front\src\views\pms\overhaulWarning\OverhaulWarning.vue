<script setup lang="ts">
import { BasicButton, Layout } from 'lyra-component-vue3';
import { onMounted, ref, watch } from 'vue';
import { Collapse, CollapsePanel, message } from 'ant-design-vue';
import FormContent from './components/FormContent.vue';
import Api from '/@/api';

interface CollapseOption {
  header: string;
  code: string;
  fields: any[];
}

const collapseOptions: Array<CollapseOption[]> = [
  [
    {
      header: '组织机构',
      code: 'organization',
      fields: ['focusDays', 'warningDays'],
    },
    {
      header: '工单准备',
      code: 'work_order',
      fields: [
        'focusDays',
        'focusRate',
        'warningDays',
        'warningRate',
        'warningRole',
      ],
    },
    {
      header: '重大项目评审',
      code: 'major_project_review',
      fields: [
        'focusDays',
        'warningDays',
        'warningRole',
      ],
    },
    {
      header: '参修人员入场',
      code: 'personnel_entry',
      fields: [
        'focusDays',
        {
          label: '人员入场率（关注）',
          value: 'focusRate',
        },
        'warningDays',
        {
          label: '人员入场率（预警）',
          value: 'warningRate',
        },
        'warningRole',
      ],
    },
    {
      header: '关注人员面谈',
      code: 'personnel_interviews',
      fields:
          [
            'focusDays',
            'warningDays',
            'warningRole',
          ],
    },
    {
      header: '工器具入场',
      code: 'equipment_entry',
      fields:
          [
            'focusDays',
            {
              label: '物资入场率（关注）',
              value: 'focusRate',
            },
            'warningDays',
            {
              label: '物资入场率（预警）',
              value: 'warningRate',
            },
            'warningRole',
          ],
    },
    {
      header: '安全质量管理方案',
      code: 'safety_and_quality_management_plan',
      fields:
          [
            'focusDays',
            'warningDays',
            'warningRole',
          ],
    },
    {
      header: '大修前培训',
      code: 'pre_overhaul_training',
      fields:
          [
            'focusDays',
            'warningDays',
            'warningRole',
          ],
    },
    {
      header: '工作包审查/读包',
      code: 'package_review',
      fields:
          [
            'focusDays',
            {
              label: '审查完成率（关注）',
              value: 'focusRate',
            },
            'warningDays',
            {
              label: '审查完成率（预警）',
              value: 'warningRate',
            },
            'warningRole',
          ],
    },
    {
      header: '交底演练',
      code: 'briefing_exercises',
      fields:
          [
            'focusDays',
            'warningDays',
            'warningRole',
          ],
    },
    {
      header: '后勤保障',
      code: 'logistics_support',
      fields:
          [
            'focusDays',
            'warningDays',
            'warningRole',
          ],
    },
    {
      header: '大修动员会',
      code: 'overhaul_mobilization_meeting',
      fields:
          [
            'focusDays',
            'warningDays',
            'warningRole',
          ],
    },
  ],
  [
    {
      header: '安质环隐患预警',
      code: 'potential_safety_hazard',
      fields: ['warningRole', 'cronList'],
    },
  ],
];

const powerData = ref<any[]>([]);
const tabsValueRef = ref<number>(0);
const collapseKeyRef = ref<number[]>(Array.from(collapseOptions[tabsValueRef.value].keys()));

watch(() => tabsValueRef.value, async (value) => {
  collapseKeyRef.value = Array.from(collapseOptions[value].keys());
  if (detailsDataRef.value.length < 2) {
    loadingRef.value = true;
    try {
      await getDetails();
    } finally {
      loadingRef.value = false;
    }
  }
});

onMounted(() => {
  Promise.allSettled([getRoleOptions(), getDetails()]).finally(() => {
    loadingRef.value = false;
  });
});

const loadingRef = ref<boolean>(true);
const roleOptionsRef = ref<any[]>([]);

async function getRoleOptions() {
  const result = await new Api('/pms/cronConfig/getRole').fetch('', '', 'GET');
  roleOptionsRef.value = result?.map((item) => ({
    label: item.name,
    value: item.code,
  })) || [];
}

const detailsDataRef = ref<any[]>([]);

async function getDetails() {
  const result = await new Api(`/pms/cronConfig/${tabsValueRef.value === 0 ? 'majorRepair' : 'safetyQuality'}/getConfig`).fetch('', '', 'GET');
  switch (tabsValueRef.value) {
    case 0:
      detailsDataRef.value[tabsValueRef.value] = result;
      break;
    case 1:
      detailsDataRef.value[tabsValueRef.value] = result ? [
        {
          ...result,
          code: 'potential_safety_hazard',
        },
      ] : [];
      break;
  }
}

function getFormValue(code: string) {
  if (detailsDataRef.value[tabsValueRef.value]) {
    return detailsDataRef.value[tabsValueRef.value]?.find((item) => item.code === code);
  }
  return undefined;
}

const formRefs = ref<any[]>([]);

async function handleSave() {
  const results: Record<string, any>[] = await Promise.allSettled(formRefs.value.map((formRef) => formRef.validate()));
  const validateMessages = results.filter((item: Record<string, any>, index: number) => {
    item.message = collapseOptions[tabsValueRef.value][index].header;
    return item.status === 'rejected';
  }).reduce((prev, next: any) => {
    prev.push(`【${next.message}】`);
    return prev;
  }, []);

  if (validateMessages.length) {
    return message.error({
      content: `请完善${validateMessages.join('\n')}`,
      style: {
        whiteSpace: 'pre-line',
      },
    });
  }
  const formValues = results.map((form) => form.value);
  const params = detailsDataRef.value[tabsValueRef.value].map((item) => {
    const formValue = formValues.find((form) => form.code === item.code);
    const obj = {
      ...item,
      ...formValue,
      warningRole: formValue?.warningRole?.join('、') || '',
      ...(item.code === 'potential_safety_hazard' ? {
        cronList: item.cronList?.map((v) => ({
          ...v,
          status: formValue?.cronList?.includes(v.code) ? 1 : 0,
        })) || [],
      } : {}),
    };
    delete obj.code;
    delete obj.warningRoleName;
    return obj;
  });

  loadingRef.value = true;
  try {
    await new Api(`/pms/cronConfig/${tabsValueRef.value === 0 ? 'majorRepair' : 'safetyQuality'}/editConfig`).fetch(tabsValueRef.value === 0 ? params : params[0], '', 'POST');
    await getDetails();
    message.success('保存成功');
  } finally {
    loadingRef.value = false;
  }
}

function getPowerDataHandle(power: any) {
  powerData.value = power || [];
}

</script>

<template>
  <Layout
    v-get-power="{pageCode:'OverhaulWarning',getPowerDataHandle}"
    v-model:tabs-value="tabsValueRef"
    v-loading="loadingRef"
    :options="{ body: { scroll: true } }"
    :tabs="['大修准备', '大修实施']"
  >
    <div
      :key="tabsValueRef"
      class="content-wrapper"
    >
      <div class="tips">
        <span v-show="tabsValueRef===0">
          说明：设置关注天数；设置距大修实施开始多少天，当前环节未完成需关注；设置预警天数；设置距大修实施开始多少天，当前环节未完成需预警。
        </span>
        <BasicButton
          style="margin-left: auto"
          type="primary"
          @click="handleSave"
        >
          保存
        </BasicButton>
      </div>
      <Collapse v-model:activeKey="collapseKeyRef">
        <CollapsePanel
          v-for="({header,fields,code},index) in collapseOptions[tabsValueRef]"
          :key="index"
          :header="header"
        >
          <FormContent
            ref="formRefs"
            :fields="fields"
            :code="code"
            :roleOptions="roleOptionsRef"
            :formValues="getFormValue(code)"
          />
        </CollapsePanel>
      </Collapse>
    </div>
  </Layout>
</template>

<style scoped lang="less">
.tips {
  position: sticky;
  display: flex;
  align-items: center;
  top: 0;
  color: ~`getPrefixVar('warning-color')`;
  padding: ~`getPrefixVar('content-padding-top')` 0 10px;
  font-size: 12px;
  background-color: #fff;
  z-index: 10;
}

.content-wrapper {
  padding: 0 ~`getPrefixVar('content-padding-left')` ~`getPrefixVar('content-padding-top')`;
}
</style>
