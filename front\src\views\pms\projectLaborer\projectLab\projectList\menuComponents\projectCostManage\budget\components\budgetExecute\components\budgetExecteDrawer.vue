<template>
  <BasicDrawer
    v-bind="$attrs"
    :width="1000"
    :showFooter="false"
    :title="state.drawerName"
    @register="modalRegister"
    @visibleChange="visibleChange"
  >
    <DetailsTab
      v-if="state.visibleStatus"
      :state="state"
    />
  </BasicDrawer>
</template>

<script setup lang="ts">
import {
  reactive,
} from 'vue';
import { BasicDrawer, useDrawerInner } from 'lyra-component-vue3';
import dayjs from 'dayjs';
import DetailsTab from './detailsTab.vue';
const state = reactive({
  drawerName: '',
  formData: {},
  visibleStatus: false,
  showForm: true,
  id: '',
  projectId: '',
});
const [modalRegister, { closeDrawer, changeOkLoading }] = useDrawerInner(
  (openProps: { type, formData, projectId}) => {
    state.drawerName = openProps.type === 'add' ? '新增预算' : `${openProps.formData?.name}支出详情明细`;
    state.id = openProps.formData?.id;
    state.projectId = openProps.projectId;
    // 设置为已打开状态
    state.visibleStatus = true;
  },
);
function visibleChange(visible: boolean) {
  // 窗口关闭时，设置状态值
  !visible && (state.visibleStatus = visible);
}

</script>
<style scoped lang="less">
</style>
