package com.chinasie.orion.domain.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ProjectSetUp Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-08 16:29:57
 */
@ApiModel(value = "ProjectSetUpVO对象", description = "项目设置")
@Data
public class ProjectSetUpVO extends ObjectVO implements Serializable {

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    private String projectId;

    /**
     * 配置key
     */
    @ApiModelProperty(value = "配置key")
    private String key;

    /**
     * 配置value
     */
    @ApiModelProperty(value = "配置value")
    private String value;

}
