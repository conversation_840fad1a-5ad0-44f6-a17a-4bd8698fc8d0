package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.dto.TaskDecompositionDTO;
import com.chinasie.orion.domain.entity.TaskDecomposition;
import com.chinasie.orion.domain.entity.TaskDecompositionPrePost;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.TaskDecompositionMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.DocumentDecompositionService;
import com.chinasie.orion.service.TaskDecompositionPrePostService;
import com.chinasie.orion.service.TaskDecompositionService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.TreeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;

import java.lang.String;
import java.util.*;
import java.util.stream.Collectors;

import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * TaskDecomposition 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-01 17:00:34
 */
@Service
@Slf4j
public class TaskDecompositionServiceImpl extends  OrionBaseServiceImpl<TaskDecompositionMapper, TaskDecomposition>   implements TaskDecompositionService {

    @Autowired
    private DocumentDecompositionService documentDecompositionService;

    @Autowired
    private TaskDecompositionPrePostService taskDecompositionPrePostService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public TaskDecompositionVO detail(String id, String pageCode) throws Exception {
        TaskDecomposition taskDecomposition =this.getById(id);
        TaskDecompositionVO result = BeanCopyUtils.convertTo(taskDecomposition,TaskDecompositionVO::new);
        setEveryName(taskDecomposition.getMainTableId(),Collections.singletonList(result));
        return result;
    }

    /**
     *  新增
     *
     * * @param taskDecompositionDTO
     */
    @Override
    public  String create(TaskDecompositionDTO taskDecompositionDTO) throws Exception {
        TaskDecomposition taskDecomposition = BeanCopyUtils.convertTo(taskDecompositionDTO,TaskDecomposition::new);

        this.save(taskDecomposition);

        return taskDecomposition.getId();
    }

    /**
     *  编辑
     *
     * * @param taskDecompositionDTO
     */
    @Override
    public Boolean edit(TaskDecompositionDTO taskDecompositionDTO) throws Exception {
        TaskDecomposition taskDecomposition =BeanCopyUtils.convertTo(taskDecompositionDTO,TaskDecomposition::new);

        this.updateById(taskDecomposition);

        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<TaskDecompositionVO> pages(String mainTableId, Page<TaskDecompositionDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<TaskDecomposition> condition = new LambdaQueryWrapperX<>( TaskDecomposition. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(TaskDecomposition::getCreateTime);

            condition.eq(TaskDecomposition::getMainTableId, mainTableId);

        Page<TaskDecomposition> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), TaskDecomposition::new));

        PageResult<TaskDecomposition> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<TaskDecompositionVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<TaskDecompositionVO> vos = BeanCopyUtils.convertListTo(page.getContent(), TaskDecompositionVO::new);
        setEveryName(mainTableId, vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public List<SimpleVo> list(String mainTableId) throws Exception {
        List<TaskDecomposition> taskDecompositions = getTaskDecompositionByMainTableId(mainTableId);
        if (CollectionUtil.isEmpty(taskDecompositions)){
            return new ArrayList<>();
        }
        return taskDecompositions.stream().map(m->{
                SimpleVo simpleVo = new SimpleVo();
                simpleVo.setId(m.getId());
                simpleVo.setName(m.getName());
                return simpleVo;
            }).collect(Collectors.toList());
    }

    @Override
    public List<TaskDecompositionVO> listTree(String mainTableId) throws Exception {
        List<TaskDecompositionVO> taskDecompositionVOS = BeanCopyUtils.convertListTo(getTaskDecompositionByMainTableId(mainTableId), TaskDecompositionVO::new);

        //设置前后置计划
        List<String> taskDecompositionIds = taskDecompositionVOS.stream().map(TaskDecompositionVO::getId).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(taskDecompositionIds)){
            Map<String, String> nameMap = taskDecompositionVOS.stream().collect(Collectors.toMap(TaskDecompositionVO::getId, TaskDecompositionVO::getName));
            List<TaskDecompositionPrePost> taskDecompositionPrePosts = taskDecompositionPrePostService.list(new LambdaQueryWrapper<>(TaskDecompositionPrePost.class).in(TaskDecompositionPrePost::getTaskDecompositionId, taskDecompositionIds));
            if(CollectionUtil.isNotEmpty(taskDecompositionPrePosts)){
                Map<String, List<TaskDecompositionPrePost>> prePostMap = taskDecompositionPrePosts.stream().collect(Collectors.groupingBy(TaskDecompositionPrePost::getTaskDecompositionId));
                Map<String, Map<Integer, List<TaskDecompositionPrePostVO>>> prePostMapVOMap = new HashMap<>();
                prePostMap.forEach((k, v) -> {
                    List<TaskDecompositionPrePostVO> taskDecompositionPrePostVOS = BeanCopyUtils.convertListTo(v, TaskDecompositionPrePostVO::new);
                    taskDecompositionPrePostVOS.forEach(o -> {
                        if (StrUtil.isNotBlank(o.getPreTaskId())) {
                            o.setTaskPreOrPostName(nameMap.get(o.getPreTaskId()));
                        } else {
                            o.setTaskPreOrPostName(nameMap.get(o.getPostTaskId()));
                        }
                        prePostMapVOMap.put(k, taskDecompositionPrePostVOS.stream().collect(Collectors.groupingBy(TaskDecompositionPrePostVO::getType)));
                    });

                });
                taskDecompositionVOS.forEach(o -> {
                    if (prePostMapVOMap.containsKey(o.getId())) {
                        o.setSchemePrePostVOList(prePostMapVOMap.get(o.getId()).getOrDefault(Status.SCHEME_PRE.getCode(), new ArrayList<>()));
                        o.setSchemePostVOList(prePostMapVOMap.get(o.getId()).getOrDefault(Status.SCHEME_POST.getCode(), new ArrayList<>()));
                    }
                });
            }
        }
        setEveryName(mainTableId, taskDecompositionVOS);
        return TreeUtils.tree(taskDecompositionVOS);
    }

    @Override
    public List<SimpleVo> listForDecompositionId(String mainTableId) throws Exception {
        List<TaskDecomposition> taskDecompositions = getTaskDecompositionByMainTableId(mainTableId);
        if (CollectionUtil.isEmpty(taskDecompositions)){
            return new ArrayList<>();
        }
        return taskDecompositions.stream().map(m->{
            SimpleVo simpleVo = new SimpleVo();
            simpleVo.setId(m.getProcessInstances());
            simpleVo.setName(m.getName());
            return simpleVo;
        }).collect(Collectors.toList());
    }

    List<TaskDecomposition> getTaskDecompositionByMainTableId (String mainTableId){
        LambdaQueryWrapperX<TaskDecomposition> lambdaQueryWrapperX = new LambdaQueryWrapperX<>( TaskDecomposition. class);
        lambdaQueryWrapperX.eq(TaskDecomposition::getMainTableId, mainTableId);
        List<TaskDecomposition> list = this.list(lambdaQueryWrapperX);
        if (CollectionUtil.isEmpty(list)){
            return new ArrayList<>();
        }
        return list;
    }

    @Override
    public void  setEveryName(String mainTableId, List<TaskDecompositionVO> vos)throws Exception {

        List<SimpleVo> list = documentDecompositionService.list(mainTableId);
        Map<String, String>  documentDecompositionMap = list.stream().collect(Collectors.toMap(SimpleVo::getId, SimpleVo::getName));

        vos.forEach(vo->{
            String processInstances = vo.getProcessInstances();
            if (StrUtil.isNotBlank(processInstances)){
                vo.setProcessInstancesName(documentDecompositionMap.get(processInstances));
            }
        });
    }



}
