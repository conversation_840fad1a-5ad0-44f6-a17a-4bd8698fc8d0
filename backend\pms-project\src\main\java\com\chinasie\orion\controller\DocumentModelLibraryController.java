package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.DocumentModelLibraryDTO;
import com.chinasie.orion.domain.vo.DocumentModelLibraryVO;
import com.chinasie.orion.service.DocumentModelLibraryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;


import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;

/**
 * <p>
 * DocumentModelLibrary 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-01 16:35:39
 */
@RestController
@RequestMapping("/documentModelLibrary")
@Api(tags = "文档模板库")
public class  DocumentModelLibraryController  {

    @Autowired
    private DocumentModelLibraryService documentModelLibraryService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据【{{#id}}】", type = "文档模板库", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<DocumentModelLibraryVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        DocumentModelLibraryVO rsp = documentModelLibraryService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param documentModelLibraryDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#documentModelLibraryDTO.name}}】", type = "文档模板库", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody @Validated DocumentModelLibraryDTO documentModelLibraryDTO) throws Exception {
        String rsp =  documentModelLibraryService.create(documentModelLibraryDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param documentModelLibraryDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#documentModelLibraryDTO.name}}】", type = "文档模板库", subType = "编辑", bizNo = "{{#documentModelLibraryDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody @Validated DocumentModelLibraryDTO documentModelLibraryDTO) throws Exception {
        Boolean rsp = documentModelLibraryService.edit(documentModelLibraryDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "文档模板库", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = documentModelLibraryService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "文档模板库", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = documentModelLibraryService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "文档模板库", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page/{mainTableId}", method = RequestMethod.POST)
    public ResponseDTO<Page<DocumentModelLibraryVO>> pages(@PathVariable("mainTableId") String mainTableId,@RequestBody Page<DocumentModelLibraryDTO> pageRequest) throws Exception {
        Page<DocumentModelLibraryVO> rsp =  documentModelLibraryService.pages(mainTableId, pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 升版
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "升版")
    @RequestMapping(value = "/upVersion/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】升版了数据【{{#id}}】", type = "文档模板库", subType = "升版", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> upVersion(@PathVariable(value = "id") String id) throws Exception {
        return new ResponseDTO<>(documentModelLibraryService.upVersion(id));
    }

    /**
     * 版本记录
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "版本记录")
    @RequestMapping(value = "/getVersionRecords/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看了版本记录【{{#id}}】", type = "文档模板库", subType = "查看版本记录", bizNo = "{{#id}}")
    public ResponseDTO<List<DocumentModelLibraryVO>> getVersionRecords(@PathVariable(value = "id") String id) throws Exception {
        return new ResponseDTO<>(documentModelLibraryService.getVersionRecords(id));
    }


    /**
     * 启用
     *
     * @param ids
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "启用")
    @RequestMapping(value = "/enable", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】启用了数据", type = "文档模板库", subType = "启用", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> enable(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = documentModelLibraryService.enable(ids);
        return new ResponseDTO<>(rsp);
    }



    /**
     * 禁用
     *
     * @param ids
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "禁用")
    @RequestMapping(value = "/disable", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】禁用了数据", type = "文档模板库", subType = "禁用", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> disEnable(@RequestBody List<String> ids ) throws Exception {
        Boolean rsp = documentModelLibraryService.disEnable(ids);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "查询引用模板分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "文档模板库", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/getModelPages", method = RequestMethod.POST)
    public ResponseDTO<Page<DocumentModelLibraryVO>> pages(@RequestBody Page<DocumentModelLibraryDTO> pageRequest) throws Exception {
        Page<DocumentModelLibraryVO> rsp =  documentModelLibraryService.getModelPages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

}
