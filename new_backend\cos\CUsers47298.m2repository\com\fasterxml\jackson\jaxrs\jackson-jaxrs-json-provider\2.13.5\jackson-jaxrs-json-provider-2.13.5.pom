<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to Gradle or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion> 
  <parent>
    <groupId>com.fasterxml.jackson.jaxrs</groupId>
    <artifactId>jackson-jaxrs-providers</artifactId>
    <version>2.13.5</version>
  </parent>
  <artifactId>jackson-jaxrs-json-provider</artifactId>
  <name>Jackson-JAXRS: JSON</name>
  <packaging>bundle</packaging>
  <description>Functionality to handle JSON input/output for JAX-RS implementations (like Jersey and RESTeasy) using standard Jackson data binding.
  </description>

  <properties>
    <!-- Generate PackageVersion.java into this directory. -->
    <packageVersion.dir>com/fasterxml/jackson/jaxrs/json</packageVersion.dir>
    <packageVersion.package>${project.groupId}.json</packageVersion.package>
    <osgi.export>${project.groupId}.json.*;version=${project.version}</osgi.export>
    <!-- NOTE: JAXB annotations module is optional dependency, need to try to mark
         as such here.
      -->
    <osgi.import>javax.ws.rs;version="${javax.ws.rs.version}"
,javax.ws.rs.core;version="${javax.ws.rs.version}"
,javax.ws.rs.ext;version="${javax.ws.rs.version}"
,com.fasterxml.jackson.module.jaxb;resolution:=optional
,*
</osgi.import>
  </properties>

  <dependencies>
    <!-- builds on shared base JAX-RS handling code... -->
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>jackson-jaxrs-base</artifactId>
      <version>${project.version}</version>
    </dependency>
    <!-- also need JAXB annotation support -->
    <dependency> 
      <groupId>com.fasterxml.jackson.module</groupId>
      <artifactId>jackson-module-jaxb-annotations</artifactId>
    </dependency>

    <!-- test deps should mostly come from parent, including jersey,
         but here we also want to ensure other JAX-RS impls can
         at least load jackson provider
      -->

    <dependency> 
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>resteasy-jackson2-provider</artifactId>
      <!-- 15-Feb-2017, tatu: Alas, 3.0.20 compiled with Java 8, can't use
        -->
      <version>3.15.0.Final</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <groupId>${project.groupId}</groupId>
          <artifactId>jackson-jaxrs-json-provider</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency> 
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>resteasy-jaxrs</artifactId>
      <version>3.15.0.Final</version>
      <scope>test</scope>
    </dependency>

  </dependencies>
  <build>
    <plugins>
      <plugin>
        <!-- Inherited from oss-base. Generate PackageVersion.java.-->
        <groupId>com.google.code.maven-replacer-plugin</groupId>
        <artifactId>replacer</artifactId>
        <executions>
          <execution>
            <id>process-packageVersion</id>
            <phase>process-sources</phase>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <execution>
            <id>no-meta-inf-services</id>
            <goals>
              <goal>jar</goal>
            </goals>
            <configuration>
              <classifier>no-metainf-services</classifier>
              <excludes>
                <exclude>META-INF/services/**</exclude>
              </excludes>
              <archive>
                <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
              </archive>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.moditect</groupId>
        <artifactId>moditect-maven-plugin</artifactId>
      </plugin>

    </plugins>
  </build>
</project>
