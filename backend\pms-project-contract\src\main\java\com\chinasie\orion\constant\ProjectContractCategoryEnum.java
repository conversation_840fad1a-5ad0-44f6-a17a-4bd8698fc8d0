package com.chinasie.orion.constant;

/**
 * @author: yk
 * @date: 2023/10/18 19:12
 * @description: 合同类别
 */
public enum ProjectContractCategoryEnum {
    SALE("saleContract", "销售合同"),
    PURCHASE("purchaseContract", "采购合同"),
    ;

    private String code;

    private String description;

    ProjectContractCategoryEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
