package com.chinasie.orion.constant;

public enum ProcessStatusEnum {

    /**
     * 已创建
     */
    CREATED("CREATED", "已创建"),
    /**
     * 已完成
     */
    COMPLETED("COMPLETED", "已完成"),
    /**
     * 进行中
     */
    ACTIVE("ACTIVE", "进行中"),
    /**
     * 已驳回
     */
    REJECTED("REJECTED", "已驳回"),
    /**
     * 已中止
     */
    TERMINATED("TERMINATED", "已中止"),
    /**
     * 在流程实例上终止
     */
    TERMINATED_ADMIN("TERMINATED_ADMIN", "在流程实例上终止"),
    /**
     * 挂起
     */
    SUSPENDED("SUSPENDED", "挂起");

    public final String code;
    public final String description;

    ProcessStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据code获取对应的枚举
     *
     * @param code 状态代码
     * @return 对应的枚举，如果不存在则返回null
     */
    public static ProcessStatusEnum fromCode(String code) {
        for (ProcessStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
