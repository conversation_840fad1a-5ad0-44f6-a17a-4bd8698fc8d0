package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

/**
 * ProjectSetUp Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-08 16:29:57
 */
@ApiModel(value = "ProjectSetUpDTO对象", description = "项目设置")
@Data
public class ProjectSetUpDTO extends ObjectDTO implements Serializable{

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    private String projectId;

    /**
     * 配置key
     */
    @ApiModelProperty(value = "配置key")
    private String key;

    /**
     * 配置value
     */
    @ApiModelProperty(value = "配置value")
    private String value;

}
