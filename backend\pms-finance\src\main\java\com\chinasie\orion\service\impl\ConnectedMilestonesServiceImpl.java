package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.base.api.domain.entity.DeptDO;
import com.chinasie.orion.base.api.domain.entity.DeptLeaderDO;
import com.chinasie.orion.base.api.domain.entity.RoleDO;
import com.chinasie.orion.base.api.domain.entity.UserDO;
import com.chinasie.orion.base.api.repository.DeptDOMapper;
import com.chinasie.orion.base.api.repository.DeptLeaderDORepository;
import com.chinasie.orion.base.api.repository.RoleDOMapper;
import com.chinasie.orion.base.api.service.DeptBaseApiService;
import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.conts.IncomeConfirmTypeEnum;
import com.chinasie.orion.conts.IncomePlanDataStatusEnum;
import com.chinasie.orion.dict.IncomePlanDict;
import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.ConnectedMilestonesVO;
import com.chinasie.orion.domain.vo.IncomeAccountConfirmVO;
import com.chinasie.orion.domain.vo.IncomePlanDataVO;
import com.chinasie.orion.domain.vo.IncomePlanExecutionTrackVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.management.domain.entity.CustomerInfo;
import com.chinasie.orion.management.service.CustomerInfoService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.ConnectedMilestonesMapper;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.RoleUserHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * ConnectedMilestones 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-20 16:31:37
 */
@Service
@Slf4j
public class ConnectedMilestonesServiceImpl extends OrionBaseServiceImpl<ConnectedMilestonesMapper, ConnectedMilestones> implements ConnectedMilestonesService {

    @Autowired
    public IncomePlanDataService incomePlanDataService;

    @Autowired
    private DeptBaseApiService deptBaseApiService;
    @Resource
    private DictBo dictBo;

    @Autowired
    private CustomerInfoService customerInfoService;

    @Autowired
    private ContractMilestoneService contractMilestoneService;
    @Autowired
    private ContractSupplierSignedSubjectService contractSupplierSignedSubjectService;

    @Autowired
    private ConnectedMilestonesMapper connectedMilestonesMapper;

    @Autowired
    private DeptDOMapper deptDOMapper;

    @Autowired
    private DeptLeaderDORepository deptLeaderDORepository;

    @Resource
    private RoleDOMapper roleDOMapper;

    @Autowired
    private PersonRoleMaintenanceDetailService personRoleMaintenanceDetailService;

    @Autowired
    private PersonRoleMaintenanceService personRoleMaintenanceService;

    @Autowired
    private RoleUserHelper roleUserHelper;



    /**
     * 新增
     * <p>
     * * @param dto
     */
    @Override
    public String create(ConnectedMilestonesDTO dto) throws Exception {
        ConnectedMilestones connectedMilestones = BeanCopyUtils.convertTo(dto, ConnectedMilestones::new);
        this.save(connectedMilestones);
        return connectedMilestones.getId();
    }

    /**
     * 收入计划分页数据
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ConnectedMilestonesVO> incomePlanPage(Page<ConnectedMilestonesDTO> pageRequest) throws Exception {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page realPageRequest = new
                com.baomidou.mybatisplus.extension.plugins.pagination.Page(pageRequest.getPageNum(), pageRequest.getPageSize());
        ConnectedMilestonesDTO connectedMilestonesDTO = pageRequest.getQuery();
        if(ObjectUtil.isEmpty(connectedMilestonesDTO)){
            connectedMilestonesDTO = new ConnectedMilestonesDTO();
        }
        List<String> centers = new ArrayList<>();
        List<String> stations = new ArrayList<>();
        String userId = CurrentUserHelper.getCurrentUserId();
        List<PersonRoleMaintenanceDetail> detailList = personRoleMaintenanceDetailService.list(new LambdaQueryWrapperX<>(PersonRoleMaintenanceDetail.class)
                .eq(PersonRoleMaintenanceDetail::getPersonId, userId).eq(PersonRoleMaintenanceDetail::getPersonType, "财务人员")
        );
        if (CollUtil.isEmpty(detailList)) {
            List<PersonRoleMaintenance> station = personRoleMaintenanceService.list(new LambdaQueryWrapperX<>(PersonRoleMaintenance.class)
                    .select(PersonRoleMaintenance::getExpertiseStation).leftJoin(PersonRoleMaintenanceDetail.class, PersonRoleMaintenanceDetail::getMianTableId, PersonRoleMaintenance::getId).eq(PersonRoleMaintenanceDetail::getPersonId, userId).eq(PersonRoleMaintenanceDetail::getPersonType, "专业所审核人员")
            );
            List<String> stationIds = station.stream().map(PersonRoleMaintenance::getExpertiseStation).collect(Collectors.toList());
            List<PersonRoleMaintenance> center = personRoleMaintenanceService.list(new LambdaQueryWrapperX<>(PersonRoleMaintenance.class)
                    .select(PersonRoleMaintenance::getExpertiseCenter).leftJoin(PersonRoleMaintenanceDetail.class, PersonRoleMaintenanceDetail::getMianTableId, PersonRoleMaintenance::getId).eq(PersonRoleMaintenanceDetail::getPersonId, userId).eq(PersonRoleMaintenanceDetail::getPersonType, "专业中心审核人员")
            );
            List<String> centerIds = center.stream().map(PersonRoleMaintenance::getExpertiseCenter).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(centerIds) ) {
                centers.addAll(centerIds);
            }
            if ( CollUtil.isNotEmpty(stationIds)) {
                stations.addAll(stationIds);
            }
            connectedMilestonesDTO.setIsPermission(false);
        }else{
            connectedMilestonesDTO.setIsPermission(true);
        }
        connectedMilestonesDTO.setCenters(centers);
        connectedMilestonesDTO.setStations(stations);
        connectedMilestonesDTO.setUserId(CurrentUserHelper.getCurrentUserId());
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<ConnectedMilestonesVO> connectedMilestonesPage
                = connectedMilestonesMapper.getConnectedMilestonesPage(connectedMilestonesDTO,realPageRequest);
        Page<ConnectedMilestonesVO> pageResult = new Page<>(connectedMilestonesPage.getCurrent(), connectedMilestonesPage.getPages(), connectedMilestonesPage.getTotal());
        if(CollUtil.isEmpty(connectedMilestonesPage.getRecords())){
            return pageResult;
        }
        List<ConnectedMilestonesVO> connectedMilestonesVOS = connectedMilestonesPage.getRecords();
        setEveryName(connectedMilestonesVOS);
        pageResult.setContent(connectedMilestonesVOS);
        return pageResult;
    }






    /**
     * 调账凭证分页数据
     * <p>
     * * @param pageRequest
     */
    @Override
    public Page<ConnectedMilestonesVO> adjustmentVoucherPage(Page<ConnectedMilestonesDTO> pageRequest) {
        //todo FIS 电子单号对应收入计划编号为空的凭证信息
        return null;
    }

    /**
     * 收入计划列表数据
     * <p>
     * * @param dto
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public List<ConnectedMilestonesVO> incomePlanList(ConnectedMilestonesDTO dto) throws Exception {
        LambdaQueryWrapperX<ConnectedMilestones> lqw = new LambdaQueryWrapperX<>(ConnectedMilestones.class);


//
//        if(StrUtil.isNotBlank(dto.getExpertiseCenterName())){
//            lqw.leftJoin(DeptDO.class,"t1",DeptDO::getId,IncomePlanData::getExpertiseCenter);
//            lqw.like(DeptDO::getName,dto.getExpertiseCenterName());
//        }
//        if(StrUtil.isNotBlank(dto.getExpertiseStationName())){
//            lqw.leftJoin(DeptDO.class,"t2",DeptDO::getId,IncomePlanData::getExpertiseStation);
//            lqw.like(DeptDO::getName,dto.getExpertiseStation());
//        }
        //查询未挂接数据
        lqw.eq(ConnectedMilestones::getHangingConnectStatus, 0);
        lqw.groupBy(ConnectedMilestones::getCertificateSerialNumber);
        lqw.orderByDesc(ConnectedMilestones::getCreateTime);
        List<ConnectedMilestonesVO> vos = BeanCopyUtils.convertListTo(this.getBaseMapper().selectList(lqw), ConnectedMilestonesVO::new);
        setEveryName(vos);
        return vos;
    }

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    @Override
    public void exportByExcel(ConnectedMilestonesDTO connectedMilestonesDTO, HttpServletResponse response) throws Exception {

        if(ObjectUtil.isEmpty(connectedMilestonesDTO)){
            connectedMilestonesDTO = new ConnectedMilestonesDTO();
        }
        List<String> centers = new ArrayList<>();
        List<String> stations = new ArrayList<>();
        String userId = CurrentUserHelper.getCurrentUserId();
        List<PersonRoleMaintenanceDetail> detailList = personRoleMaintenanceDetailService.list(new LambdaQueryWrapperX<>(PersonRoleMaintenanceDetail.class)
                .eq(PersonRoleMaintenanceDetail::getPersonId, userId).eq(PersonRoleMaintenanceDetail::getPersonType, "财务人员")
        );
        if (CollUtil.isEmpty(detailList)) {
            List<PersonRoleMaintenance> station = personRoleMaintenanceService.list(new LambdaQueryWrapperX<>(PersonRoleMaintenance.class)
                    .select(PersonRoleMaintenance::getExpertiseStation).leftJoin(PersonRoleMaintenanceDetail.class, PersonRoleMaintenanceDetail::getMianTableId, PersonRoleMaintenance::getId).eq(PersonRoleMaintenanceDetail::getPersonId, userId).eq(PersonRoleMaintenanceDetail::getPersonType, "专业所审核人员")
            );
            List<String> stationIds = station.stream().map(PersonRoleMaintenance::getExpertiseStation).collect(Collectors.toList());
            List<PersonRoleMaintenance> center = personRoleMaintenanceService.list(new LambdaQueryWrapperX<>(PersonRoleMaintenance.class)
                    .select(PersonRoleMaintenance::getExpertiseCenter).leftJoin(PersonRoleMaintenanceDetail.class, PersonRoleMaintenanceDetail::getMianTableId, PersonRoleMaintenance::getId).eq(PersonRoleMaintenanceDetail::getPersonId, userId).eq(PersonRoleMaintenanceDetail::getPersonType, "专业中心审核人员")
            );
            List<String> centerIds = center.stream().map(PersonRoleMaintenance::getExpertiseCenter).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(centerIds) ) {
                centers.addAll(centerIds);
            }
            if ( CollUtil.isNotEmpty(stationIds)) {
                stations.addAll(stationIds);
            }
            connectedMilestonesDTO.setIsPermission(false);
        }else{
            connectedMilestonesDTO.setIsPermission(true);
        }
        connectedMilestonesDTO.setCenters(centers);
        connectedMilestonesDTO.setStations(stations);
        List<ConnectedMilestonesVO> connectedMilestonesVOS = connectedMilestonesMapper.getConnectedMilestonesList(connectedMilestonesDTO);
        setEveryName(connectedMilestonesVOS);
        List<ConnectedMilestonesExportDTO> dtos = BeanCopyUtils.convertListTo(connectedMilestonesVOS, ConnectedMilestonesExportDTO::new);
        String fileName = "挂接里程碑数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ConnectedMilestonesExportDTO.class, dtos);
    }

    @Override
    public void setEveryName(List<ConnectedMilestonesVO> vos) throws Exception {
        List<String> partyADeptIds = new ArrayList<>();
        List<String> deptIds = new ArrayList<>();

        for (ConnectedMilestonesVO connectedMilestonesVO : vos) {
            if (StrUtil.isNotBlank(connectedMilestonesVO.getExpertiseCenter())) {
                deptIds.add(connectedMilestonesVO.getExpertiseCenter());
            }
            if (StrUtil.isNotBlank(connectedMilestonesVO.getExpertiseStation())) {
                deptIds.add(connectedMilestonesVO.getExpertiseStation());
            }
        }
        Map<String, String> deptMap = new HashMap<>();
        if(CollUtil.isNotEmpty(deptIds)){
            deptIds = deptIds.stream().distinct().collect(Collectors.toList());
            List<DeptVO> deptVOS = deptBaseApiService.getDeptByIds(deptIds);
            deptMap = deptVOS.stream().collect(Collectors.toMap(DeptVO::getId, DeptVO::getName));
        }
        Map<String, String> customerInfoMap = new HashMap<>();
        if (CollUtil.isNotEmpty(partyADeptIds)) {
            partyADeptIds = partyADeptIds.stream().distinct().collect(Collectors.toList());
            List<CustomerInfo> customerInfoList = customerInfoService.list(new LambdaQueryWrapperX<>(CustomerInfo.class).select(CustomerInfo::getId, CustomerInfo::getCusName).in(CustomerInfo::getId, partyADeptIds));
            customerInfoMap = customerInfoList.stream().collect(Collectors.toMap(CustomerInfo::getId, CustomerInfo::getCusName));
        }
        Map<String, String> incomeConfirmTypeDict = dictBo.getDictValue(IncomePlanDict.INCOME_CONFIRM_TYPE);
        Map<String, String> billingDict = dictBo.getDictValue(IncomePlanDict.BILLING_RECOGNITION_COMPANY);
        Map<String, String> finalDeptMap = deptMap;
        Map<String, String> finalCustomerInfoMap = customerInfoMap;
        vos.forEach(vo -> {
            if(StrUtil.isNotBlank(vo.getExpertiseCenter())){
                vo.setExpertiseCenterName(finalDeptMap.get(vo.getExpertiseCenter()));
            }
            if(StrUtil.isNotBlank(vo.getExpertiseStation())){
                vo.setExpertiseStationName(finalDeptMap.get(vo.getExpertiseStation()));
            }
            if(StrUtil.isNotBlank(vo.getIncomeConfirmType())){
                vo.setIncomeConfirmTypeName(incomeConfirmTypeDict.get(vo.getIncomeConfirmType()));
            }
            if(StrUtil.isNotBlank(vo.getBillingCompany())){
                vo.setBillingCompanyName(billingDict.get(vo.getBillingCompany()));
            }
            vo.setPartyADeptIdName(finalCustomerInfoMap.get(vo.getPartyADeptId()));
            if (StrUtil.isBlank(vo.getPartyADeptIdName())) {
                vo.setPartyADeptIdName(vo.getPartyADeptId());
            }
        });
    }

    /**
     * 挂接确认
     *
     * @param
     * @return
     */
    @Override
    public Boolean hangingConnect(List<String> ids) {
        if(CollUtil.isEmpty(ids)){
            throw new PMSException(PMSErrorCode.PMS_ERR, "挂接数据问题");
        }
        //获取凭证编号数组
        List<ConnectedMilestones> csnList = this.list(new LambdaQueryWrapperX<>(ConnectedMilestones.class)
                .in(ConnectedMilestones::getId,ids));
        if(CollUtil.isEmpty(csnList)){
            throw new PMSException(PMSErrorCode.PMS_ERR, "数据未挂接");
        }
        List<String> milestoneIds = new ArrayList<>();
        List<String> contractIds = new ArrayList<>();
        for(ConnectedMilestones connectedMilestones:csnList){
            if(StrUtil.isNotBlank(connectedMilestones.getMilestoneId())){
                milestoneIds.add(connectedMilestones.getMilestoneId());
            }else{
                throw new PMSException(PMSErrorCode.PMS_ERR, "里程碑不存在");
            }
            contractIds.add(connectedMilestones.getContractId());
        }
        List<ContractMilestone> contractMilestones = contractMilestoneService.list(new LambdaQueryWrapperX<ContractMilestone>(ContractMilestone.class).in(ContractMilestone::getId,milestoneIds));
        List<String> cusPersonIds = contractMilestones.stream().map(ContractMilestone::getCusPersonId).collect(Collectors.toList());

        List<CustomerInfo> customerInfoList = customerInfoService.list(new LambdaQueryWrapperX<>(CustomerInfo.class).select(CustomerInfo::getId,CustomerInfo::getCusName).in(CustomerInfo::getId,cusPersonIds));
        Map<String,String> customerInfoMap = customerInfoList.stream().collect(Collectors.toMap(CustomerInfo::getId,CustomerInfo::getCusName));
        Map<String,ContractMilestone> contractMilestoneMap = contractMilestones.stream().collect(Collectors.toMap(ContractMilestone::getId, Function.identity()));

        List<IncomePlanData> incomePlanDataList = incomePlanDataService.list(new LambdaQueryWrapperX<>(IncomePlanData.class)
        .in(IncomePlanData::getId,ids));
        Map<String,IncomePlanData> incomePlanDataMap = new HashMap<>();
        if(CollUtil.isNotEmpty(incomePlanDataList)) {
            incomePlanDataMap = incomePlanDataList.stream().collect(Collectors.toMap(IncomePlanData::getId, Function.identity()));
        }
        List<ContractSupplierSignedSubject> subjects = contractSupplierSignedSubjectService.list(new LambdaQueryWrapperX<>(ContractSupplierSignedSubject.class).in(ContractSupplierSignedSubject::getContractId,contractIds));
        Map<String,String> subjectMap =   subjects.stream()
                .collect(Collectors.toMap(
                        ContractSupplierSignedSubject::getContractId,
                        ContractSupplierSignedSubject::getSignedMainName,
                        (existing, replacement) -> existing // 如果有重复的key，则保留现有的（即第一条记录）
                ));


        for(ConnectedMilestones connectedMilestones:csnList){
            ContractMilestone contractMilestone = contractMilestoneMap.get(connectedMilestones.getMilestoneId());
            if(StrUtil.isNotBlank(contractMilestone.getIncomePlanCode())) {
                throw new PMSException(PMSErrorCode.PMS_ERR, "合同里程碑已挂接");
            }
            IncomePlanData incomePlanData = incomePlanDataMap.get(connectedMilestones.getId());
            if(ObjectUtil.isNotEmpty(incomePlanData)) {
                incomePlanData.setContractId(connectedMilestones.getContractId());
                incomePlanData.setContractNumber(connectedMilestones.getContractNumber());
                incomePlanData.setContractName(connectedMilestones.getContractName());
                incomePlanData.setMilestoneId(connectedMilestones.getMilestoneId());
                incomePlanData.setMilestoneName(connectedMilestones.getMilestoneName());
                incomePlanData.setPartyADeptId(contractMilestone.getCusPersonId());
                incomePlanData.setPartyADeptIdName(customerInfoMap.get(contractMilestone.getCusPersonId()));
                contractMilestone.setIncomePlanCode(incomePlanData.getNumber());
                contractMilestone.setIncomeType(incomePlanData.getIncomeConfirmType());
            }

            contractMilestone.setVoucherNum(connectedMilestones.getCertificateSerialNumber());
            contractMilestone.setPassAccountDate(connectedMilestones.getPostingDate());
//            if(StrUtil.equals(connectedMilestones.getIncomeConfirmType(), IncomeConfirmTypeEnum.PROVISIONAL_INCOME.getValue())) {
//                contractMilestone.setConfirmIncomeProvisionalEstimate(connectedMilestones.getConfirmRevenueAmount());
//            }else{
//                contractMilestone.setConfirmIncomeInvoicing(connectedMilestones.getConfirmRevenueAmount());
//            }
            connectedMilestones.setHangingConnectStatus(1);
        }

        this.updateBatchById(csnList);
        //收入计划填报数据回写
        if(CollUtil.isNotEmpty(incomePlanDataList)) {
            incomePlanDataService.updateBatchById(incomePlanDataList);
        }

        if(CollUtil.isNotEmpty(contractMilestones)) {
            contractMilestoneService.updateBatchById(contractMilestones);
        }
        return true;
    }

    /**
     * 收入计划填报数据同步
     *
     * @param
     * @return
     */
    @Override
    public String edit(ConnectedMilestonesDTO dto) throws Exception {
        ConnectedMilestones connectedMilestones = BeanCopyUtils.convertTo(dto, ConnectedMilestones::new);
        this.saveOrUpdate(connectedMilestones);
        return connectedMilestones.getId();
    }
}
