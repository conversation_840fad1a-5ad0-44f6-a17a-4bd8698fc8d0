package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.TrainCenterDTO;
import com.chinasie.orion.domain.dto.train.TrainCenterParamDTO;
import com.chinasie.orion.domain.entity.TrainCenter;
import com.chinasie.orion.domain.vo.TrainCenterVO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * TrainCenter 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 17:09:07
 */
public interface TrainCenterService extends OrionBaseService<TrainCenter> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    TrainCenterVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param trainCenterDTO
     */
    String create(TrainCenterDTO trainCenterDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param trainCenterDTO
     */
    Boolean edit(TrainCenterDTO trainCenterDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<TrainCenterVO> pages(Page<TrainCenterDTO> pageRequest) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<TrainCenterVO> vos) throws Exception;


    void saveOrUpdateList(List<TrainCenter> trainCenters,String dataId,String dataNumber,Boolean isCheck);

    /**
     *  获取培训下的中心列表
     * @param trainCenterDTO
     * @return
     */
    List<TrainCenterVO> listByEntity(TrainCenterDTO trainCenterDTO) throws Exception;

    /**
     *  获取培训下的中心列表对应的证书列表
     * @param centerParamDTO
     * @return
     */
    List<TrainCenterVO> trainCertificateList(TrainCenterParamDTO centerParamDTO) throws Exception;

    /**
     * 导出培训证明附件
     * @param centerParamDTO
     * @param response
     */
    void exportAttachment(TrainCenterParamDTO centerParamDTO, HttpServletResponse response) throws Exception;

    /**
     *  附件上传
     * @param id
     * @param fileDTOS
     * @return
     */
    Boolean uploadFile(String id, List<FileDTO> fileDTOS) throws Exception;

    Map<String, TrainCenter> simpleMapByIds(List<String> centreIdList);

    /**
     *  办结数据
     * @param centerId
     * @return
     */
    Boolean endFinish(String centerId) throws Exception;

    List<TrainCenter> listByTrainId(String id);

    /**
     * 获取培训中心
     * @return 列表
     */
    List<TrainCenterVO> getTrainCenterList();


}
