package com.chinasie.orion.schedule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.domain.entity.AmpereringDictInfo;
import com.chinasie.orion.domain.entity.JobHeightRisk;
import com.chinasie.orion.domain.entity.JobHeightRiskCopy;
import com.chinasie.orion.domain.entity.JobManage;
import com.chinasie.orion.repository.AmpereringDictInfoMapper;
import com.chinasie.orion.repository.JobHeightRiskCopyMapper;
import com.chinasie.orion.repository.JobHeightRiskMapper;
import com.chinasie.orion.repository.JobManageMapper;
import com.chinasie.orion.util.CollectionUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @author: psd
 * @date: 2024/08/26 16:33
 * @description: 安质环任务清单定时任务
 */
@Slf4j
@Component
public class ProjectJobHeightRisk {
    @Autowired
    private JobHeightRiskCopyMapper jobHeightRiskCopyMapper;

    @Autowired
    //JobHeightRiskMapper
    private JobHeightRiskMapper jobHeightRiskMapper;

    @Autowired
    private JobManageMapper jobManageMapper;

    @Autowired
    private AmpereringDictInfoMapper ampereringDictInfoMapper;



    @XxlJob("projectJobHeightRiskInsertData")
    public void projectJobHeightRiskInsertData(String compStr) throws JsonProcessingException {
        compStr = XxlJobHelper.getJobParam();

        ObjectMapper mapper = new ObjectMapper();
        //数据全量逻辑
//        if (compStr != null && !compStr.isEmpty()) {
        jobHeightRiskMapper.delAllList();
        List<JobHeightRiskCopy> jobHeightRiskCopyList = jobHeightRiskCopyMapper.selectList();
        if(CollectionUtils.isBlank(jobHeightRiskCopyList)){
            return;
        }
        log.error("开始地址转换");
        convertJobAddressName(jobHeightRiskCopyList);

        final int BATCH_SIZE = 50;

        for (int i = 0; i < jobHeightRiskCopyList.size(); i += BATCH_SIZE) {
            List<JobHeightRiskCopy> subList = jobHeightRiskCopyList.subList(i, Math.min(i + BATCH_SIZE, jobHeightRiskCopyList.size()));
            jobManageMapper.updateJobManage(subList);
        }


        List<JobHeightRisk> jobHeightRiskList = getJobHeightRiskList(jobHeightRiskCopyList);
        for (JobHeightRisk jobHeightRisk : jobHeightRiskList) {
            jobHeightRisk.setId(UUID.randomUUID().toString());
        }
        jobHeightRiskMapper.insertBatch(jobHeightRiskList);
//        }
//        //数据增量逻辑
//        if (compStr == null || compStr.isEmpty()) {
//            LocalDateTime oneDayAgo = LocalDateTime.now().minusDays(1);
//            Timestamp oneDayAgoTimestamp = Timestamp.valueOf(oneDayAgo);
//            LambdaQueryWrapper<JobHeightRiskCopy> lambdaQueryWrapperRiskData = new LambdaQueryWrapper<>();
//            lambdaQueryWrapperRiskData.gt(JobHeightRiskCopy::getCreateTime, oneDayAgoTimestamp);
//            List<JobHeightRiskCopy> jobHeightRiskCopyList = jobHeightRiskCopyMapper.selectList(lambdaQueryWrapperRiskData);
//            if (jobHeightRiskCopyList != null && jobHeightRiskCopyList.size() > 0) {
//                List<JobHeightRisk> jobHeightRiskList = getJobHeightRiskList(jobHeightRiskCopyList);
//
//                for (JobHeightRisk jobHeightRisk : jobHeightRiskList) {
//                    jobHeightRisk.setId(UUID.randomUUID().toString());
//                }
//                jobHeightRiskMapper.insertBatch(jobHeightRiskList);
//            }
//        }
//        //修改了operational_risks_json字段更新逻辑
//        if (compStr == null || compStr.isEmpty()) {
//            LambdaQueryWrapper<JobHeightRiskCopy> lambdaQueryWrapperRiskData = new LambdaQueryWrapper<>();
//            lambdaQueryWrapperRiskData.having("create_time > modify_time");
//            List<JobHeightRiskCopy> jobHeightRiskCopyList = jobHeightRiskCopyMapper.selectList(lambdaQueryWrapperRiskData);
//            if (jobHeightRiskCopyList != null && jobHeightRiskCopyList.size() > 0) {
//                List<JobHeightRisk> jobHeightRiskList = getJobHeightRiskList(jobHeightRiskCopyList);
//                for (JobHeightRisk jobHeightRisk : jobHeightRiskList) {
//                    jobHeightRiskMapper.updateBatch(jobHeightRisk);
//                }
//            }
//        }
    }

    private void convertJobAddressName(List<JobHeightRiskCopy> jobHeightRiskCopyList){
        List<AmpereringDictInfo> dictInfoList = ampereringDictInfoMapper.selectList();
        if(!CollectionUtils.isBlank(dictInfoList)){
            Map<String,List<AmpereringDictInfo>> dictMap = dictInfoList.stream().filter(item -> StringUtils.isNotBlank(item.getCode())).collect(Collectors.groupingBy(AmpereringDictInfo::getCode));
            jobHeightRiskCopyList.forEach(item ->{
                String jobAddress = item.getJobAddress();
                String jobAddrName = "";
                if(StringUtils.isNotBlank(jobAddress)){
                    List<String> addresses = Arrays.asList(jobAddress.split(","));
                    String firstCode = addresses.get(0);
                    List<AmpereringDictInfo> dictList = dictMap.get(firstCode);
                    if(!CollectionUtils.isBlank(dictList)){
                        AmpereringDictInfo dictInfo = dictList.get(0);
                        jobAddrName = jobAddrName + dictInfo.getName();
                        String parentId = dictInfo.getId();
                        for (int i = 1; i < addresses.size(); i++) {
                            if(StringUtils.isBlank(parentId)){
                                break;
                            }
                            String code = addresses.get(i);
                            List<AmpereringDictInfo> dictInfoList1 = dictMap.get(code);
                            if(CollectionUtils.isBlank(dictInfoList1)){
                                break;
                            }
                            Map<String,List<AmpereringDictInfo>> parentMap=  dictInfoList1.stream().filter(item1 -> StringUtils.isNotBlank(item1.getParentId())).collect(Collectors.groupingBy(AmpereringDictInfo :: getParentId));
                            List<AmpereringDictInfo> parenList = parentMap.get(parentId);
                            if(CollectionUtils.isBlank(parenList)){
                                break;
                            }
                            AmpereringDictInfo subDict = parenList.get(0);
                            jobAddrName = jobAddrName + subDict.getName();
                            parentId = subDict.getId();

                        }
                    }
                }
                log.error("开始地址转换地址------------------------"+jobAddrName);
                item.setJobAddrName(jobAddrName);
            });

        }
    }


    public static List<JobHeightRisk> getJobHeightRiskList(List<JobHeightRiskCopy> jobHeightRiskCopyList) throws
            JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        List<JobHeightRisk> jobHeightRiskList = new ArrayList<>();
        if (jobHeightRiskCopyList != null && jobHeightRiskCopyList.size() > 0) {
            for (JobHeightRiskCopy jobHeightRiskCopy : jobHeightRiskCopyList) {
                String jsonStr = jobHeightRiskCopy.getOperationalRisksJson();
                if (jsonStr != null && !jsonStr.isEmpty()) {
                    JsonNode rootNode = mapper.readTree(jsonStr);
                    for (JsonNode node : rootNode) {
                        JobHeightRisk jobHeightRisk = new JobHeightRisk();
                        BeanUtils.copyProperties(jobHeightRiskCopy, jobHeightRisk);
                        jobHeightRisk.setJudgmentStandards(node.get("judgeStandard").asText());
                        jobHeightRisk.setJobContent(node.get("judgeStandardContent").asText());
                        String riskLevel = node.get("riskLevel").asText();
                        if(StringUtils.isBlank(riskLevel)){
                            continue;
                        }
                        if(riskLevel.contains("一级")){
                            riskLevel = "一级";
                        }
                        else if(riskLevel.contains("二级")){
                            riskLevel = "二级";
                        }
                        else if(riskLevel.contains("三级")){
                            riskLevel = "三级";
                        }
                        jobHeightRisk.setRiskLevel(riskLevel);
                        jobHeightRisk.setCopyId(jobHeightRiskCopy.getId());
                        jobHeightRisk.setJobAddrName(jobHeightRiskCopy.getJobAddrName());
                        jobHeightRiskList.add(jobHeightRisk);

                    }
                } else {
                    JobHeightRisk jobHeightRisk = new JobHeightRisk();
                    BeanUtils.copyProperties(jobHeightRiskCopy, jobHeightRisk);
                    jobHeightRiskList.add(jobHeightRisk);
                }
            }
        }
        return jobHeightRiskList;
    }
}

