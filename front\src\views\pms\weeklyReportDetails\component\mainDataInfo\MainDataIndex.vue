<template>
  <div class="dayReportDetailsBox">
    <DetailsLayout
      title="周报内容"
      :column="3"
    >
      <div
        style="height: 220px;overflow: hidden"
      >
        <TableDayInfo :dataInfo="dataInfo" />
      </div>
    </DetailsLayout>
    <DetailsLayout
      title="汇报总结"
    >
      <InputTextArea
        v-model:value="state.inputValue"
        disabled="true"
        :row="4"
      />
    </DetailsLayout>
    <DetailsLayout
      title="下周计划"
    >
      <TableDayAfterInfo />
    </DetailsLayout>
    <DetailsLayout
      title="相关附件"
      show-table-header
    >
      <div
        style="height: 220px;overflow-y: scroll"
      >
        <UploadList
          :listData="tableData"
          :edit="false"
          type="page"
        />

        <!--        <OrionTable-->
        <!--          ref="tableRef"-->
        <!--          :options="tableOptions"-->
        <!--          @selection-change="selectionChange"-->
        <!--        >-->
        <!--          <template #toolbarLeft>-->
        <!--            <BasicButton-->
        <!--              icon="orion-icon-download"-->
        <!--              type="primary"-->
        <!--              @click="batchDownLoad"-->
        <!--            >-->
        <!--              批量下载-->
        <!--            </BasicButton>-->
        <!--          </template>-->
        <!--          <template #actions="{record}">-->
        <!--            <div>-->
        <!--              <BasicTableAction-->
        <!--                :actions="getActionsList({state})"-->
        <!--                :record="record"-->
        <!--              />-->
        <!--            </div>-->
        <!--          </template>-->
        <!--        </OrionTable>-->
      </div>
    </DetailsLayout>
    <DetailsLayout
      title="评价信息"
      :column="4"
      :data-source="state.basicData?.allData"
      :list="appraisalSchema"
    />
    <DetailsLayout
      title="基本信息"
      :column="4"
      :data-source="state.basicData?.allData"
      :list="basicSchema"
    />
  </div>
</template>

<script setup lang="ts">
import {
  computed,
  defineProps, onMounted, reactive, ref, watch,
} from 'vue';
import { Button, Input, message } from 'ant-design-vue';
import {
  BasicButton, BasicTableAction, BasicTitle1, downLoadById, isPower, OrionTable, UploadList,
} from 'lyra-component-vue3';
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import dayjs from 'dayjs';
import { useRoute } from 'vue-router';
import TableDayInfo from './tableDayInfo/TableDayInfo.vue';
import FileTable from './fileTable/FileTable.vue';
import TableDayAfterInfo from './tableDayAfterInfo/TableDayAfterInfo.vue';
import Api from '/@/api';
import { getActionsList, getColumns } from '/@/views/pms/dayReportDetails/component/mainDataInfo/fileTable/config';

const InputTextArea = Input.TextArea;
const route = useRoute();
const props = defineProps({
  dataInfo: {
    type: Object,
    default: () => {
    },
  },
});
watch(() => props.dataInfo, () => {
  if (props.dataInfo) {
    state.inputValue = props.dataInfo?.summary;
    state.isShowbtn = props?.dataInfo.edit;
  } else {
    state.inputValue = '';
    state.isShowbtn = false;
  }
});
const AButton = Button;
const emits = defineEmits<{
    (e: 'update:title', hah: string): void;
}>();
const tableRef = ref(null);
const tableData = ref(null);
const state = reactive({
  downFlag: 1,
  isShowbtn: '',
  inputValue: '',
  keys: [],
  appraisalData: {
    score: '',
    appraiser: '',
    evaluationTime: '',
    evaluate: '',
  },
  detailInfo: {},
  basicData: {
    allData: {},
  },
});
const appraisalSchema = [
  {
    field: 'score',
    label: '评分',
    render: (data) => (data ? `${data ?? 0}分` : ''),
  },
  {
    field: 'reviewedByName',
    label: '评价人',
  },
  {
    field: 'evaluateDate',
    label: '评价时间',
  },
  {
    field: 'evaluate',
    label: '评价',
  },
];

const basicSchema = [
  {
    label: '汇报日期',
    field: 'daily',
  },
  {
    label: '责任人',
    field: 'creatorName',
  },
  {
    label: '总工时',
    field: 'totalHours',
  },
  {
    label: '整体进度',
    field: ['overallProgressName', 'name'],
  },
  {
    label: '状态',
    field: ['dataStatus', 'name'],
  },
  {
    label: '提交时间',
    field: 'commitTime',
  },
  {
    label: '提交人',
    field: 'reviewedName',
  },
  {
    label: '抄送人',
    field: 'carbonCopyByNames',
  },
  {
    label: '创建人',
    field: 'creatorName',
  },
  {
    label: '创建时间',
    field: 'createTime',
  },
  {
    label: '最后更新时间',
    field: 'modifyTime',
  },

];

function getDailyRemarkDetail() {
  new Api(`/pms/projectWeekly/${route.query?.curId}`).fetch('', '', 'GET').then((res) => {
    if (res) {
      res.daily = `${dayjs(res.weekBegin).format('YYYY-MM-DD')}~${dayjs(res.weekEnd).format('YYYY-MM-DD')}`;
      res.commitTime = res.dataStatus.name !== '待提交' ? dayjs(res.commitTime).format('YYYY-MM-DD') : ''; // 提交时间
      res.createTime = res.createTime ? dayjs(res.createTime).format('YYYY-MM-DD') : '';
      res.modifyTime = res.modifyTime ? dayjs(res.modifyTime).format('YYYY-MM-DD') : '';
      res.evaluateDate = res.evaluateDate ? dayjs(res.evaluateDate).format('YYYY-MM-DD') : '';
      res.score = res.dataStatus.name !== '待提交' ? res.score : '';
      res.reviewedName = res.dataStatus.name !== '待提交' ? res.reviewedByName : '';
      res.reviewedByName = res.dataStatus.name !== '待提交' ? res.reviewedByName : '';
      res.evaluateDate = res.dataStatus.name !== '待提交' ? res.evaluateDate : '';
      res.evaluate = res.dataStatus.name !== '待提交' ? res.evaluate : '';
      state.basicData.allData = res;
      let time = 0;
      if (res?.contentVOList && res.contentVOList?.length) {
        res.contentVOList.forEach((item) => {
          if (item?.taskTime) {
            time += item?.taskTime;
          }
        });
      }
      tableData.value = res.documentVOList ?? [];
      state.basicData.allData.totalHours = `${time}h`;
    }
  });
}
const tableOptions = reactive({
  rowSelection: {},
  canResize: false,
  maxHeight: 300,
  isTableHeader: false,
  dataSource: [],
  pagination: false,
  columns: getColumns(),
});
function selectionChange({ keys }) {
  state.keys = keys;
}
function batchDownLoad() {
  if (state.keys?.length) {
    state.keys.map((item) => {
      downLoadById(item?.id);
    });
  } else {
    message.info('请至少选择一条数据进行下载');
  }
}
onMounted(() => {
  getDailyRemarkDetail();
});
</script>

<style scoped lang="less">
.dayReportDetailsBox {
  width: 100vw;
}

.tableBox {
  height: 300px;
  overflow-y: auto;
}
.mg0 {
  margin:0px !important;
}
</style>
