import dayjs from 'dayjs';

export function getWeeksInMonth(year, month) {
  // 创建一个数组来存储结果
  const weeks = [];
  // 获取指定年月的第一天和最后一天
  const firstDay = new Date(year, month ? month - 1 : 0, 1);
  const lastDay = new Date(year, month || 12, 0);
  // 获取第一周的起始和结束日期以及周数
  const getFirstWeek = (date) => {
    const start = new Date(date);
    start.setDate(date.getDate() - date.getDay() + 1);
    const end = new Date(date);
    end.setDate(date.getDate() + (6 - date.getDay()));
    const weekNumber = getWeekNumber(start);
    return {
      startDate: start,
      endDate: end,
      weekNumber,
    };
  };

  // 获取下一周的起始和结束日期以及周数
  const getNextWeek = (date, weekNumber) => {
    const start = new Date(date);
    start.setDate(date.getDate());
    const end = new Date(date);
    end.setDate(date.getDate() + 6);
    weekNumber++;
    return {
      weekBegin: dayjs(start).format('YYYY-MM-DD'),
      weekEnd: dayjs(end).format('YYYY-MM-DD'),
      value: weekNumber,
      name: `第${weekNumber}周（${dayjs(start).format('YYYY-MM-DD')}~${dayjs(end).format('YYYY-MM-DD')}）`,
    };
  };

  // 获取指定日期所在周是当年的第几周
  const getWeekNumber = (date) => {
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const daysOffset = firstDayOfYear.getDay() - 1;
    const firstMondayOfYear = new Date(
      date.getFullYear(),
      0,
      1 + (daysOffset > 0 ? 7 - daysOffset : 0),
    );
    const diff = date - firstMondayOfYear;
    const weekNumber = Math.ceil(diff / (7 * 24 * 60 * 60 * 1000)); // 修正周数
    return weekNumber;
  };

  // 初始化起始日期和结束日期
  let startDate = getFirstWeek(firstDay).startDate;
  let endDate = getFirstWeek(firstDay).endDate;
  let weekNumber = getFirstWeek(firstDay).weekNumber === 0 ? getFirstWeek(firstDay).weekNumber : getFirstWeek(firstDay).weekNumber - 1;

  // 循环直到结束日期大于最后一天
  while (endDate <= lastDay) {
    weeks.push(getNextWeek(startDate, weekNumber));
    startDate.setDate(startDate.getDate() + 7);
    endDate.setDate(endDate.getDate() + 7);
    weekNumber++;
  }
  return weeks;
}
