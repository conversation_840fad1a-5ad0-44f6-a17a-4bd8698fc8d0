<script setup lang="ts">
import {
  computed,
  ref,
  Ref,
  onMounted,
  watch,
} from 'vue';
import {
  Layout3,
  OrionTable,
  IOrionTableOptions,
  isPower,
  BasicButton,
  downloadByData,
} from 'lyra-component-vue3';
import {
  RadioGroup as ARadioGroup, RadioButton as ARadioButton, Modal,
} from 'ant-design-vue';
import { useRouter, useRoute } from 'vue-router';
import Api from '/@/api';
import { getTableViewColumns } from '../columns';
import {
  FinancialViewFilterConfig,
} from '../filterIndex';

const router = useRouter();
const route = useRoute();
const dataType = computed(() => route.query?.type);
const dataId = computed(() => route.query?.id);
const tableRef = ref(null);
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const selectedKeys: Ref<string[]> = ref([]);
const detailsData = ref({});
const loadStatus: Ref<boolean> = ref(false);
const mode = ref('compilation');
const loading: Ref<boolean> = ref(false);
const powerData: Ref<any[]> = ref([]);

watch(() => dataType.value, (newValue) => {
  mode.value = newValue?.toString();
}, { immediate: true });

const tableOptions:IOrionTableOptions = {
  api: async (params:Record<string, any>) => {
    const result: any[] = await new Api('/pms').fetch({
      ...params,
      power: {
        pageCode: 'SRJHTBXQ_001',
      },
    }, 'incomePlan/page', 'POST');
    return result;
  },
  rowSelection: dataType.value === 'compilation' && mode.value === 'compilation' ? {
    onChange(_keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
      selectedKeys.value = _keys;
    },
  } : undefined,
  showSmallSearch: false,
  pagination: false,
  isFilter2: true,
  filterConfig: {
    fields: FinancialViewFilterConfig,
  },
  // 是否开启行添加，如果不开启，请自行在表单创建好后，调用表格的添加行数据方法
  isRowAdd: false,
  deleteToolButton: 'add|delete|enable|disable',
  canResize: false,
};

function getPowerDataHandle(power: any) {
  powerData.value = power || [];
}

onMounted(() => {
  getDetails();
});

async function getDetails() {
  loading.value = true;
  try {
    const result: Record<string, any> = await new Api(`/pms/incomePlanData/${dataId.value}`).fetch({
      pageCode: 'SRJHTBXQ001',
    }, '', 'GET');
    if (result) {
      detailsData.value = result;
    }
  } finally {
    loading.value = false;
  }
}

// 操作按钮
function handleOperation(type:string) {
  switch (type) {
    case 'export':
      handleExport(mode.value);
      break;
    case 'print':
      break;
    default:
      break;
  }
}

// 跳转详情
function navDetails(type: string, record:Record<string, any>) {
  let path = '';
  switch (type) {
    case '1':
      path = 'FinancialMilestones';
      break;
    default:
      path = 'FinancialManageDetails';
      break;
  }
  router.push({
    name: path,
    params: {
      id: record.id,
    },
  });
}

// 导出
async function handleExport(type:string) {
  const isEmpty = selectedKeys.value.length === 0;
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk: async () => {
      loadStatus.value = true;
      await downloadByData('/pms/investmentscheme-report/exportmonthfeedback/excel', {
        isEmpty,
        ids: selectedKeys.value,
      }, '', 'POST', true, false, '导出处理完成，现在开始下载');
      loadStatus.value = false;
    },
  });
}

// 更新表格
function updateTable() {
  tableRef.value?.reload();
}

// 切换填报模式
function handleModeChange(e) {
  mode.value = e.target.value;
}

</script>

<template>
  <Layout3
    v-get-power="{pageCode:'SRJHCK_001',getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
    :type="2"
  >
    <template #name>
      <div class="flex-row">
        <div class="font-weight">
          {{ detailsData?.name || '2024年08月收入计划' }}
        </div>
      </div>
    </template>
    <OrionTable
      ref="tableRef"
      :key="mode"
      :options="tableOptions"
      :columns="getTableViewColumns(dataType, mode, navDetails)"
    >
      <!-- 公共工具栏右侧 -->
      <template #toolbarRight>
        <div v-if="mode !== 'quarter'">
          <ARadioGroup
            v-model:value="mode"
            class="select-btn"
            @change="handleModeChange"
          >
            <ARadioButton value="compilation">
              编制版
            </ARadioButton>
            <ARadioButton value="adjustment">
              调整版
            </ARadioButton>
            <ARadioButton value="diff">
              差异版
            </ARadioButton>
          </ARadioGroup>
        </div>
      </template>
      <!-- 动态工具栏中心 -->
      <template #toolbarCenter>
        <div class="flex-r-btn">
          <BasicButton
            v-if="isPower('PMS_SRJHTBXQ_container_01_button_08', powerData)"
            type="primary"
            ghost
            icon="sie-icon-daochu"
            @click="handleOperation('export')"
          >
            导出
          </BasicButton>
          <BasicButton
            v-if="isPower('PMS_SRJHTBXQ_container_01_button_08', powerData)"
            type="primary"
            ghost
            icon="sie-icon-daochu"
            @click="handleOperation('printAll')"
          >
            报表打印
          </BasicButton>
          <div class="total-num-left">
            <span>{{ "总额锁定状态：已锁定" }}</span>
          </div>
        </div>
      </template>
    </OrionTable>
  </Layout3>
</template>

<style scoped lang="less">
.customColors {
  :deep(.ant-btn) {
    background-color: #ceedfc;
    color: #1890FF;
    border-color: #ceedfc;
  }
}
.select-btn {
  position: absolute;
  right: 115px;
  .ant-radio-button-wrapper:last-child {
    color: #f5222d;
  }
}
:deep(.surely-table-cell-inner) {
  .box-big{
    display: flex;
    flex-direction: row;
    align-items: center;
    color: #1890FF;
    cursor: pointer;
    .fa-edit {
      margin-left: 10px;
    }
  }
}
.flex-r-btn{
  display: flex;
  flex-direction: row;
  align-items: center;
}
.flex-total{
  .mb-10{
    margin-left: 20px;
  }
}
:deep(.user-name){
  display: none;
}
:deep(.header-wrap .project-title){
  width: auto!important;
}
:deep(.header-main){
  border-right: none!important;
}
.total-num-left{
  span:last-child{
    margin-left: 20px;
  }
}
:deep(.orion-table-header-wrap) {
  margin-bottom: 20px;
}
.flex-row{
  display: flex;
  flex-direction: row;
  align-items: center;
  .font-weight {
    font-weight: bold;
    font-size: 18px;
    margin-right: 20px;
  }
}
</style>
