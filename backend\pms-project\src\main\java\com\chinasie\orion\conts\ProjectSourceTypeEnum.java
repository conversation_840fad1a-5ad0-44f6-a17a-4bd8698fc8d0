package com.chinasie.orion.conts;


import org.springframework.util.StringUtils;

/**
 * @author: yk
 * @date: 2023/10/21 11:13
 * @description:
 */
public enum ProjectSourceTypeEnum {
    PLAN("plan", "综合计划"),
    CONTRACT("contract", "销售合同"),
    DECLARE("declare", "科研申报"),
    LEAD("lead", "线索响应"),
    BUSINESS_OPPORTUNITY("businessOpportunity", "商机"),
    PROJECT("project", "项目"),
    ;

    private String code;

    private String description;

    ProjectSourceTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     *  通过 映射的key 返回整个属性
     * @param code
     * @return
     */
    public static String getCode(String code){
        if(!StringUtils.hasText(code)){
            return "";
        }
        ProjectSourceTypeEnum[] values = ProjectSourceTypeEnum.values();
        for (ProjectSourceTypeEnum value : values) {
            String key1 = value.code;
            if(key1.equals(code)){
                return value.getDescription();
            }
        }
        return  null;
    }
}
