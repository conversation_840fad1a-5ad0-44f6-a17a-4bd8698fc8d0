import Api from '/@/api';
import { message } from 'ant-design-vue';

/**
 * 流程启动
 * @param id 流程实例ID
 * @param userId  用户ID
 * @param load  刷新实例列表方法
 * @param isMessage 是否message提示
 */
export function flowStart(id, userId, load, isMessage = true) {
  return new Api('/workflow/act-prearranged/start')
    .fetch(
      {
        ids: [id],
        userId,
      },
      '',
      'PUT',
    )
    .then(() => {
      isMessage && message.success('启动成功');
      load && load();
    });
}

/**
 * 流程提交
 * @param vmContext 模块上下文
 * @param taskDefinitionKey 流程节点键
 * @param comment 处理意见
 */
export function submitFlow(vmContext, taskDefinitionKey, comment) {
  const {
    attrs: {
      menuActionItem,
      userId,
      menuMethods: { load },
    },
  } = vmContext;
  const { currentTaskId } = menuActionItem;
  return new Api('/workflow/act-task/agree')
    .fetch(
      {
        comment: comment || '',
        targetTaskDefinitionKey: taskDefinitionKey,
        taskId: currentTaskId,
        userId,
        variables: {},
      },
      '',
      'PUT',
    )
    .then(() => {
      message.success('操作成功');
      load();
    });
}
