<template>
  <BasicModal
    v-bind="$attrs"
    title="添加节点"
    @register="registerModal"
  >
    <div class="style-line">
      <span>选择节点：</span>
      <div>
        <a-select
          v-model:value="selectList"
          style="width: 260px"
          mode="tags"
        >
          <select-option
            v-for="item in dataList"
            :key="item._id"
            :value="item._name"
          >
            {{ item._name }}
          </select-option>
        </a-select>
      </div>
    </div>
    <template #footer>
      <a-button @click="closeModal">
        取消
      </a-button>
      <a-button
        type="primary"
        @click="onConfirm"
      >
        确定
      </a-button>
    </template>
  </BasicModal>
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
import {
  BasicTable, Layout, useActionsRecord, OrionTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer,
} from 'lyra-component-vue3';
// import { BasicModal, useModalInner } from '/@/components/Modal';
import { Descriptions, Tabs, Select } from 'ant-design-vue';
export default defineComponent({
  components: {
    BasicModal,
    aDescriptions: Descriptions,
    aTabs: Tabs,
    ASelect: Select,
    SelectOption: Select.Option,
  },
  setup(_, { emit }) {
    // 弹窗内部的注册函数,可以在内部自己关闭
    const [registerModal, { closeModal }] = useModalInner((data) => {
      state.selectList = [];
      const { list, selectList } = data;
      selectList.forEach((item: any) => {
        state.selectList.push(item._name);
      });
      state.dataList = list;
    });

    const state: any = reactive({
      selectList: [],
      dataList: [],
    });

    function onConfirm() {
      let res: any[] = [];
      state.selectList.forEach((item) => {
        state.dataList.forEach((sourceItem) => {
          if (sourceItem._name === item) {
            res.push(sourceItem);
          }
        });
      });
      emit('update-list', res);
      closeModal();
    }

    return {
      registerModal,
      closeModal,
      ...toRefs(state),
      onConfirm,
    };
  },
});
</script>
<style scoped lang="less">
.style-line {
  display: flex;
  flex-direction: row;
  align-items: center;
}
</style>
