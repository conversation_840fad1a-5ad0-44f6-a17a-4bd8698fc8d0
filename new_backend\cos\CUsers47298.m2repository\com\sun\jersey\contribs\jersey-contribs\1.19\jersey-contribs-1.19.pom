<?xml version="1.0" encoding="UTF-8"?>
<!--

    DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS HEADER.

    Copyright (c) 2010-2015 Oracle and/or its affiliates. All rights reserved.

    The contents of this file are subject to the terms of either the GNU
    General Public License Version 2 only ("GPL") or the Common Development
    and Distribution License("CDDL") (collectively, the "License").  You
    may not use this file except in compliance with the License.  You can
    obtain a copy of the License at
    http://glassfish.java.net/public/CDDL+GPL_1_1.html
    or packager/legal/LICENSE.txt.  See the License for the specific
    language governing permissions and limitations under the License.

    When distributing the software, include this License Header Notice in each
    file and include the License file at packager/legal/LICENSE.txt.

    GPL Classpath Exception:
    Oracle designates this particular file as subject to the "Classpath"
    exception as provided by Oracle in the GPL Version 2 section of the License
    file that accompanied this code.

    Modifications:
    If applicable, add the following below the License Header, with the fields
    enclosed by brackets [] replaced by your own identifying information:
    "Portions Copyright [year] [name of copyright owner]"

    Contributor(s):
    If you wish your version of this file to be governed by only the CDDL or
    only the GPL Version 2, indicate your decision by adding "[Contributor]
    elects to include this software in this distribution under the [CDDL or GPL
    Version 2] license."  If you don't indicate a single choice of license, a
    recipient has the option to distribute your version of this file under
    either the CDDL, the GPL Version 2 or to extend the choice of license to
    its licensees as provided above.  However, if you add GPL Version 2 code
    and therefore, elected the GPL Version 2 license, then the option applies
    only if the new code is made subject to such option by the copyright
    holder.

-->

<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.sun.jersey</groupId>
        <artifactId>jersey-project</artifactId>
        <version>1.19</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <groupId>com.sun.jersey.contribs</groupId>
    <artifactId>jersey-contribs</artifactId>
    <packaging>pom</packaging>
    <name>Contributions to Jersey</name>
    <description>
        Projects that provide additional functionality to jersey, like integration
        with other projects/frameworks.
    </description>
    <modules>
	<module>ant-wadl-task</module>
        <module>spring</module>
        <module>maven-wadl-plugin</module>
        <module>wadl-resourcedoc-doclet</module>
        <module>jersey-multipart</module>
        <module>jersey-atom-abdera</module>
        <module>jersey-apache-client</module>
        <module>jersey-apache-client4</module>
        <module>jersey-freemarker</module>
        <module>jersey-moxy</module>
        <module>jersey-non-blocking-client</module>
        <module>jersey-simple-server</module>
        <module>jersey-guice</module>
        <module>jersey-wadl-json-schema</module>
        <module>jersey-oauth</module>
        <module>scala</module>
        <module>bill-burke-book</module>
    </modules>
</project>
