package com.chinasie.orion.manager;

import com.chinasie.orion.domain.entity.ProjectScheme;

import java.util.List;

public interface SchemeStatusProcessor {

    /**
     * 处理项目计划状态：
     * 已创建、待审核、待下发、进行中、已完成、预期
     * @param projectSchemeIds
     */
    void statusHandleById(List<String> projectSchemeIds) throws Exception;

    void statusHandle(String projectId) throws Exception;

    void statusHandle(List<ProjectScheme> projectSchemes) throws Exception;
}
