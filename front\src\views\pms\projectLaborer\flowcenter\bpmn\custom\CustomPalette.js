// CustomPalette.js
export default class CustomPalette {
  constructor(bpmnFactory, create, elementFactory, palette, translate) {
    this.bpmnFactory = bpmnFactory;
    this.create = create;
    this.elementFactory = elementFactory;
    this.translate = translate;

    palette.registerProvider(this);
  }

  getPaletteEntries(element) {
    const {
      bpmnFactory, create, elementFactory, translate,
    } = this;

    function createTask() {
      return function (event) {
        const businessObject = bpmnFactory.create('bpmn:UserTask'); // 其实这个也可以不要
        const shape = elementFactory.createShape({
          type: 'bpmn:UserTask',
          do: true,
          businessObject,
        });
        create.start(event, shape);
      };
    }

    return {
      'create.lindaidai-task': {
        group: 'model',
        className: 'icon-custom lindaidai-task',
        title: translate('创建编制任务节点'),
        action: {
          dragstart: createTask(),
          click: createTask(),
        },
      },
    };
  }
}

CustomPalette.$inject = [
  'bpmnFactory',
  'create',
  'elementFactory',
  'palette',
  'translate',
];
