<template>
  <DetailsLayout
    title="年度投资计划执行情况"
    :list="listField"
    label-width="200px"
    label-align="right"
    :data-source="formData"
    :border-bottom="true"
  >
    <template #slider>
      <div style=" height: 30px" />
    </template>
  </DetailsLayout>
  <DetailsLayout
    title="分月计划执行情况"
    :border-bottom="true"
  >
    <template #title-right>
      <span class="header-unit">单位：万元</span>
    </template>
    <template #table>
      <div style="height: 150px;overflow: hidden">
        <OrionTable
          ref="monthTableRef"
          bordered
          :dataSource="monthTableData"
          :options="monthTableOptions"
        />
      </div>
    </template>
  </DetailsLayout>
  <DetailsLayout
    title="月度反馈表"
    :show-table-header="true"
    :border-bottom="true"
  >
    <template #title-right>
      <span class="header-unit">单位：万元</span>
    </template>
    <template #table>
      <MonthlyFeedback />
    </template>
  </DetailsLayout>
</template>

<script lang="ts" setup>
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import MonthlyFeedback from './MonthlyFeedback.vue';
import {
  computed, inject, ref, Ref,
} from 'vue';
import { OrionTable } from 'lyra-component-vue3';
import { formatMoney } from '/@/views/pms/projectManage/utils';

const props = withDefaults(defineProps<{
  showApprove:boolean
}>(), {
  showApprove: false,
});

const formData: Ref = inject('formData');
const year: Ref<number> = ref();
year.value = Number(formData.value.yearName);
const listField: Ref<any[]> = ref([
  {
    label: '投资计划年度',
    field: 'yearName',
    gridColumn: '1/5',
  },
  {
    label: `累计至${year.value - 1}年投资计划`,
    field: 'cutOffGiveY_1',
    isMoney: true,
    unit: '万元',
  },
  {
    label: `累计至${year.value - 1}年投资计划执行`,
    field: 'cutOffCompleteY_1',
    isMoney: true,
    unit: '万元',
  },
  {
    label: `累计至${year.value}年投资计划`,
    field: 'cutOffGiveY',
    isMoney: true,
    unit: '万元',
  },
  {
    label: `累计至${year.value}年投资计划执行`,
    field: 'cutOffCompleteY',
    isMoney: true,
    unit: '万元',
  },
  {
    label: '项目总体执行情况',
    field: 'totalProcess',
    gridColumn: '1/5',
  },
  {
    label: '',
    slot: true,
    slotName: 'slider',
    gridColumn: '1/5',
  },
  {
    label: '本年预算',
    field: 'currentYearBudget',
    isMoney: true,
    precision: 10000,
    unit: '万元',
  },
  {
    label: '本年实际执行',
    field: 'currentYearBudgetDo',
    gridColumn: '2/5',
    isMoney: true,
    precision: 10000,
    unit: '万元',
  },
  {
    label: `${year.value}年度投资计划`,
    field: 'total',
    isMoney: true,
    unit: '万元',
  },
  {
    label: `调整后${year.value}年度投资计划`,
    field: 'totalChange',
    isMoney: true,
    unit: '万元',
  },
  {
    label: `${year.value}年度投资计划执行`,
    field: 'totalDo',
    isMoney: true,
    unit: '万元',
  },
  {
    label: `${year.value}年度投资执行率`,
    field: 'executionRate',
  },
  {
    label: `${year.value}年形象进度`,
    field: 'yearProcess',
    gridColumn: '1/5',
  },
]);

const monthTableData = computed(() => {
  let actualObj = {};
  let predicateObj = {};
  formData.value?.monthInvestmentSchemes?.forEach((item, index) => {
    actualObj[index + 1] = formatMoney(item.actual);
    predicateObj[index + 1] = formatMoney(item.predicate);
  });
  return [
    {
      ...predicateObj,
      name: '投资计划',
      id: '01',
    },
    {
      ...actualObj,
      name: '执行数',
      id: '02',
    },
  ];
});

const monthTableOptions = {
  showSmallSearch: false,
  showToolButton: false,
  showTableSetting: false,
  pagination: false,
  showIndexColumn: false,
  columns: [
    {
      title: '',
      dataIndex: 'name',
      align: 'right',
      minWidth: 114,
    },
    {
      title: '1月',
      dataIndex: '1',
      align: 'right',
      width: 145,
    },
    {
      title: '1-2月',
      dataIndex: '2',
      align: 'right',
      width: 145,
    },
    {
      title: '1-3月',
      dataIndex: '3',
      align: 'right',
      width: 145,
    },
    {
      title: '1-4月',
      dataIndex: '4',
      align: 'right',
      width: 145,
    },
    {
      title: '1-5月',
      dataIndex: '5',
      align: 'right',
      width: 145,
    },
    {
      title: '1-6月',
      dataIndex: '6',
      align: 'right',
      width: 145,
    },
    {
      title: '1-7月',
      dataIndex: '7',
      align: 'right',
      width: 145,
    },
    {
      title: '1-8月',
      dataIndex: '8',
      align: 'right',
      width: 145,
    },
    {
      title: '1-9月',
      dataIndex: '9',
      align: 'right',
      width: 145,
    },
    {
      title: '1-10月',
      dataIndex: '10',
      align: 'right',
      width: 145,
    },
    {
      title: '1-11月',
      dataIndex: '11',
      align: 'right',
      width: 145,
    },
    {
      title: '1-12月',
      dataIndex: '12',
      align: 'right',
      width: 145,
    },
  ],
};

</script>

<style scoped lang="less">
:deep(.surely-table-bordered) {
  border: none;

  .surely-table-empty-container {
    border-right: none;
  }
}

.header-unit{
  font-size: 14px;
  margin-left: auto;
}
</style>
