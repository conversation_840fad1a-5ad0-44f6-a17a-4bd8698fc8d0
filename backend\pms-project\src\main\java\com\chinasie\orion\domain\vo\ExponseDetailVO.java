package com.chinasie.orion.domain.vo;

import com.chinasie.orion.file.api.domain.vo.FileVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;
import java.util.List;

/**
 * ExponseDetail Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-16 15:22:28
 */
@ApiModel(value = "ExponseDetailVO对象", description = "支出详情表")
@Data
public class ExponseDetailVO extends ObjectVO implements Serializable {

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    /**
     * 成本中心ID
     */
    @ApiModelProperty(value = "成本中心ID")
    private String costCenterId;

    /**
     * 成本中心名字
     */
    @ApiModelProperty(value = "成本中心名字")
    private String costCenterName;

    /**
     * 费用科目ID
     */
    @ApiModelProperty(value = "费用科目ID")
    private String expenseAccountId;

    /**
     * 费用科目名字
     */
    @ApiModelProperty(value = "费用科目名字")
    private String expenseAccountName;

    /**
     * 支出人ID
     */
    @ApiModelProperty(value = "支出人ID")
    private String outPersonId;

    /**
     * 支出人姓名
     */
    @ApiModelProperty(value = "支出人姓名")
    private String outPersonName;

    /**
     * 支出金额
     */
    @ApiModelProperty(value = "支出金额")
    private BigDecimal outMoney;

    /**
     * 支出时间
     */
    @ApiModelProperty(value = "支出时间")
    private Date outTime;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 附件URL
     */
    @ApiModelProperty(value = "附件URL")
    private String tag;

    /**
     * 预算ID
     */
    @ApiModelProperty(value = "预算ID")
    private String budgetProjectId;

    /**
     * 预算名称
     */
    @ApiModelProperty(value = "预算名称")
    private String budgetProjectName;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;


    @ApiModelProperty(value = "附件文档")
    private List<FileVO> attachments;

    @ApiModelProperty(value = "编制预算信息")
    private ProjectBudgetVO projectBudgetVO;

}
