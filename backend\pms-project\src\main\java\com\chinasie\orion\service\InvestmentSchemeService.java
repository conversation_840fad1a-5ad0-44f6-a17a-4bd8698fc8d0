package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.InvestmentSchemeDTO;
import com.chinasie.orion.domain.entity.InvestmentScheme;
import com.chinasie.orion.domain.vo.InvestmentSchemeVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import java.util.List;

/**
 * <p>
 * InvestmentScheme 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16 14:04:50
 */
public interface InvestmentSchemeService extends OrionBaseService<InvestmentScheme> {


    /**
     * 自动创建投资计划
     *
     * @param projectId
     * @return
     */
    InvestmentSchemeVO createByProjectId(String projectId,String pageCode) throws Exception;


    /**
     * 初始值
     *
     * @param projectId
     * @return
     */
    InvestmentSchemeVO initValue(String projectId) throws Exception;

    /**
     * 详情
     * <p>
     * * @param id
     */
    InvestmentSchemeVO detail(String id,String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param investmentSchemeDTO
     */
    String create(InvestmentSchemeDTO investmentSchemeDTO) throws Exception;

    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    List<InvestmentSchemeVO> list(String projectId) throws Exception;

    /**
     * 1月1号0点变更为已关闭
     *
     * @return
     */
    Boolean close() throws Exception;

}
