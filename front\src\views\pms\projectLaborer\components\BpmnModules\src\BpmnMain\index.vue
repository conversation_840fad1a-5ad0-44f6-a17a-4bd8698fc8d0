<template>
  <div class="wrap">
    <div class="menu">
      <Menu
        v-bind="propsComputed"
        @change="menChange"
        @init="menuInit"
      />
    </div>
    <div class="content">
      <Content
        v-if="menuActionItem"
        :key="menuActionItem?.currentTaskId || menuActionItem?.id"
        :menuActionItem="menuActionItem"
        @approvalObjectRegister="approvalObjectRegister"
      />
      <div
        v-else
        class="h-full flex flex-pac"
      >
        <Empty description="请选择流程实例" />
      </div>
    </div>
    <div class="right">
      <Right
        v-bind="propsComputed"
        @init="rightInit"
      />
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed, defineComponent, reactive, toRefs, provide, unref, watch,
} from 'vue';
import Menu from './component/Menu.vue';
import Content from './component/Content.vue';
import Right from './component/Right.vue';
import Api from '/@/api';
import { BUTTON_TYPE } from '../enums/btnType';
import { Empty } from 'ant-design-vue';
import { useUserStore } from '/@/store/modules/user';

export default defineComponent({
  name: 'BpmnMain',
  components: {
    Menu,
    Content,
    Right,
    Empty,
  },
  props: {
    // 模块ID
    deliveryId: {
      type: String,
      default: '',
    },
    // 用户ID
    // userId: {
    //   type: String,
    //   default: 'as4f6f22875d92c34841a6a3fa7fd9b8ebc5'
    // },
    // className
    dataType: {
      type: String,
      default: '',
    },
    // 模块ID, 如有项目的则为项目ID
    bizId: {
      type: String,
      default: '',
    },
    // 模块名称,如：设计文件下的   设计文件书签
    procInstName: {
      type: String,
      default: '',
    },
    groupId: {
      type: String,
      default: '',
    },
  },
  // emits: ['openClick'],
  setup(props, context) {
    const userStore: any = useUserStore();
    const state: any = reactive({
      userId: userStore.getUserInfo.id,
      // 右侧按钮显示项
      // toolBtnList: ['add', 'edit', 'start', 'screening', 'rollback', 'agree'],
      // 左侧实例列表选中项
      menuActionItem: null,
      // 菜单栏方法
      menuMethods: null,
      // 工具栏方法
      rightMethods: null,
      // 任务按钮
      taskBtn: null,
      // 审批对象->审批物列表返回实例方法, 内部包含tableRef
      approvalObjectVm: null,
      // 模块ID
      projectId: props.bizId,
    });

    // 右侧按钮显示项
    const toolBtnList = computed(() => {
      let btnList = ['add'];

      if (state.menuActionItem) {
        btnList.push('open', 'check');
        const { statusCode } = state.menuActionItem;
        if (statusCode === 'NOT_STARTED' || statusCode === 'CANCELLED') {
          btnList = [
            'add',
            'start',
            'edit',
            'delete',
            'open',
          ];
        }
        if (statusCode === 'RUNNING') {
          btnList = ['add', 'rollback'];
          if (state.taskBtn) {
            if (state.taskBtn.find((item) => item.code === BUTTON_TYPE.agree)) {
              btnList.push('agree');
            }
            if (state.taskBtn.find((item) => item.code === BUTTON_TYPE.reject)) {
              btnList.push('reject');
            }
            if (state.taskBtn.find((item) => item.code === BUTTON_TYPE.turn)) {
              btnList.push('turn');
            }
          }
        }
      }

      return btnList;
    });

    // 实例传参整合
    const propsComputed = computed(() => ({
      ...props,
      ...context.attrs,
      ...state,
      toolBtnList: unref(toolBtnList),
    }));

    provide('bpmnModuleData', propsComputed);

    // 监听菜单变化，加载按钮
    watch(
      () => state.menuActionItem,
      (menuActionItem) => {
        state.taskBtn = null;
        if (menuActionItem) {
          const { procDefId, currentTaskId } = menuActionItem;
          new Api('/workflow/act-inst-detail/task-act')
            .fetch(
              {
                procDefId,
                userId: state.userId,
                taskId: currentTaskId,
              },
              '',
              'GET',
            )
            .then((data) => {
              state.taskBtn = data;
            });
        }
      },
    );

    function menChange(menuActionData) {
      state.menuActionItem = menuActionData ? menuActionData.item : null;
    }

    // 传递菜单实例方法
    function menuInit(menuMethods) {
      state.menuMethods = menuMethods;
    }

    // 工具栏传递实例方法
    function rightInit(rightMethods) {
      state.rightMethods = rightMethods;
    }

    // 审批物实例
    function approvalObjectRegister(data) {
      state.approvalObjectVm = data;
    }

    return {
      ...toRefs(state),
      toolBtnList,
      propsComputed,
      menuInit,
      rightInit,
      menChange,
      approvalObjectRegister,
    };
  },
});
</script>

<style scoped lang="less">
  .wrap {
    width: 100%;
    height: 100%;
    display: flex;

    :deep(.title) {
      height: 40px;
      line-height: 40px;
      padding: 0 15px 0 35px;
      box-sizing: border-box;
      background: #f0f2f5;
      position: relative;
      &:after {
        content: '';
        width: 0;
        height: 0;
        border-width: 6px;
        border-style: solid;
        border-color: #969eb4 transparent transparent transparent;
        position: absolute;
        left: 15px;
        top: 18px;
      }
    }
  }
  .menu {
    width: 280px;
    border-right: 1px solid #e9ecf2;
    height: 100%;
  }
  .content {
    flex: 1;
    width: 1px;
  }

  .right {
    width: 60px;
  }
</style>
