<script setup lang="ts">
import { OrionTable, DataStatusTag } from 'lyra-component-vue3';
import {
  h, inject, onMounted, ref, Ref,
} from 'vue';
import Api from '/@/api';

const purchaseTypeTitle: Ref<string> = inject('purchaseTypeTitle');
const purchaseTypeValue: string = inject('purchaseTypeValue');
const projectId: string = inject('projectId');
const tableRef: Ref = ref();
const selectRows: Ref<any[]> = ref([]);
const tableOptions = {
  rowSelection: {
    type: 'radio',
  },
  smallSearchField: [
    'number',
    'goods_service_number',
    'description',
  ],
  showTableSetting: false,
  showToolButton: false,
  api: (params: Record<string, any>) => new Api('/pms/goods-service-plan/getPage').fetch({
    ...params,
    query: {
      projectId,
    },
    searchConditions: getSearchConditions(params.searchConditions),
  }, '', 'POST'),
  columns: [
    {
      title: `${purchaseTypeTitle.value}计划编号`,
      dataIndex: 'number',
    },
    {
      title: '类型',
      dataIndex: 'type',
      width: 100,
    },
    {
      title: `${purchaseTypeTitle.value}编码`,
      dataIndex: 'goodsServiceNumber',
    },
    {
      title: `${purchaseTypeTitle.value}描述`,
      dataIndex: 'description',
    },
    {
      title: '需求数量',
      dataIndex: 'demandAmount',
    },
    {
      title: '状态',
      dataIndex: 'dataStatus',
      customRender({ text }) {
        return text ? h(DataStatusTag, { statusData: text }) : '';
      },
    },
  ],
};

function selectionChange({ rows }) {
  selectRows.value = rows;
}

// 获取搜索条件
function getSearchConditions(searchConditions: Array<Record<string, any>[]>) {
  const fixedParams = [
    {
      field: 'type_code',
      fieldType: 'String',
      values: [purchaseTypeValue],
      queryType: 'like',
    },
    {
      field: 'status',
      fieldType: 'Integer',
      values: [130, 160],
      queryType: 'in',
    },
  ];
  if (searchConditions) {
    return searchConditions.map((item) => item.concat(fixedParams));
  }
  return [fixedParams];
}

defineExpose({
  getData: () => selectRows.value,
});
</script>

<template>
  <div style="height: 400px;overflow: hidden">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      :onSelectionChange="selectionChange"
    />
  </div>
</template>

<style scoped lang="less">

</style>
