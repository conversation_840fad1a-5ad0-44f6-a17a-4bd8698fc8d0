package com.chinasie.orion.domain.entity.approval;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * ProjectApprovalIncome Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-14 14:11:17
 */
@TableName(value = "pms_project_approval_income")
@ApiModel(value = "ProjectApprovalIncomeEntity对象", description = "收益策划")
@Data
public class ProjectApprovalIncome extends ObjectEntity implements Serializable{

    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    @TableField(value = "product_id")
    private String productId;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    @TableField(value = "product_name")
    private String productName;

    /**
     * 预期合同年份
     */
    @ApiModelProperty(value = "预期合同年份")
    @TableField(value = "expected_contract_year")
    private Date expectedContractYear;

    /**
     * 制造工时
     */
    @ApiModelProperty(value = "制造工时")
    @TableField(value = "fabricate_hour")
    private BigDecimal fabricateHour;

    /**
     * 预期销售数量
     */
    @ApiModelProperty(value = "预期销售数量")
    @TableField(value = "expected_sale_number")
    private Integer expectedSaleNumber;

    /**
     * 预期收益
     */
    @ApiModelProperty(value = "预期收益")
    @TableField(value = "expected_income")
    private BigDecimal expectedIncome;

    /**
     * 项目立项id
     */
    @ApiModelProperty(value = "项目立项id")
    @TableField(value = "project_approval_id")
    private String projectApprovalId;

    /**
     * 产品编码
     */
    @ApiModelProperty(value = "产品编码")
    @TableField(value = "product_number")
    private String productNumber;

}
