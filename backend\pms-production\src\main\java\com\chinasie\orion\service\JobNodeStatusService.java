package com.chinasie.orion.service;


import com.chinasie.orion.domain.entity.JobNodeStatus;
import com.chinasie.orion.domain.dto.JobNodeStatusDTO;
import com.chinasie.orion.domain.vo.JobNodeStatusVO;

import java.lang.String;
import java.util.List;


import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * JobNodeStatus 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-08 14:45:20
 */
public interface JobNodeStatusService extends OrionBaseService<JobNodeStatus> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    JobNodeStatusVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param jobNodeStatusDTO
     */
    String create(JobNodeStatusDTO jobNodeStatusDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param jobNodeStatusDTO
     */
    Boolean edit(JobNodeStatusDTO jobNodeStatusDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<JobNodeStatusVO> pages(Page<JobNodeStatusDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<JobNodeStatusVO> vos) throws Exception;

    List<JobNodeStatusVO> listByJobId(String jobId) throws Exception;

    /**
     *  通过节点ID和key 设置节点信息
     * @param jobId
     * @param keyList
     * @return
     * @throws Exception
     */
    void setNodeStatus(String jobId,List<String> keyList) throws Exception;

    /**
     *  批量修改 节点状态
     * @param jobIdList
     * @param operator
     */
    void setNodeStatusByIdList(List<String> jobIdList, String operator);
}
