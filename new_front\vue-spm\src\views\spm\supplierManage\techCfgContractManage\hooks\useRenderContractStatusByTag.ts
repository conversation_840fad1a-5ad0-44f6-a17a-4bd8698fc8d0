import { h } from 'vue';
import { Tag } from 'ant-design-vue';

const colorContractMap = new Map([
  [121, 'default'],
  [130, 'processing'],
  [1, 'success'],
  [140, 'default'],
]);
const colorCenterMap = new Map([
  [121, 'default'],
  [110, 'default'],
  [120, 'processing'],
  [140, 'error'],
  [160, 'success'],
]);
export const useRenderContractStatusByTag = () => {
  const setContractStatus = (status, text) => {
    if (!status) {
      return '—';
    }

    const cfg = colorContractMap.get(status);
    return h(Tag, {
      color: cfg,
    }, text);
  };
  const getContractStatus = (status:number) => colorContractMap.get(status || 121);
  const setCenterStatus = (status, text) => {
    if (!status) {
      return '—';
    }
    const cfg = colorCenterMap.get(status);
    return h(Tag, {
      color: cfg,
    }, text);
  };

  return {
    setContractStatus,
    setCenterStatus,
    getContractStatus,
  };
};