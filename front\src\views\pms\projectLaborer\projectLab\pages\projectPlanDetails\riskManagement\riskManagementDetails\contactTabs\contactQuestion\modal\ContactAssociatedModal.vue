<script setup lang="ts">
import {
  BasicModal, DataStatusTag, isPower, OrionTable, useModalInner,
} from 'lyra-component-vue3';
import {
  computed, h, inject, onMounted, ref, Ref,
} from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';

const route = useRoute();
const tableRef:Ref = ref();
const dataId: Ref<string> = ref(route.query.id as string);
const paramsData = ref();
const visibleModal:Ref<boolean> = ref(false);
let projectId: any = inject('projectId');
const [register, { closeModal, changeOkLoading }] = useModalInner(() => {
  visibleModal.value = true;
});
const emits = defineEmits<{
  (e:'checkProjectCallback', data):void
}>();
const router = useRouter();
const tableOptions = {
  showToolButton: false,
  showTableSetting: false,
  rowSelection: {
    type: 'checkbox',
  },
  api: (params) => new Api('/pas/question-management/getPage').fetch({
    ...params,
    query: {
      riskId: route.query.itemId,
      projectId: projectId.value,
    },
  }, '', 'POST'),
  columns: [
    {
      title: '编号',
      dataIndex: 'number',
      align: 'left',
      key: 'number',

      width: '120px',
      // sorter: true,
      ellipsis: true,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      customRender({ record, text }) {
        return h(
          'span',
          {
            class: 'action-btn',
            // class: computed(()=>isPower('FX_container_button_08', state.powerData)) ?'action-btn':'',
            title: text,
            onClick(e) {
              router.push({
                name: 'QuestionManagementDetails',
                query: {
                  itemId: record.id,
                },
              });
            },
          },
          text,
        );
      },

      align: 'left',
      // slots: { customRender: 'name' },
      // sorter: true,
      ellipsis: true,
    },
    {
      title: '问题内容',
      dataIndex: 'content',
      key: 'content',
      align: 'left',
      // sorter: true,
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'dataStatus',
      key: 'dataStatus',
      width: '80px',
      align: 'left',
      // sorter: true,
      ellipsis: true,
      customRender: ({ record, text }) => h(DataStatusTag, { statusData: record.dataStatus }),
    },
    {
      title: '严重程度',
      dataIndex: 'seriousLevelName',
      key: 'seriousLevelName',
      align: 'left',
      // sorter: true,
      ellipsis: true,
    },
    {
      title: '问题来源',
      dataIndex: 'questionSourceName',
      key: 'questionSourceName',
      align: 'left',
      // sorter: true,
      ellipsis: true,
    },
    {
      title: '问题类型',
      dataIndex: 'questionTypeName',
      key: 'questionTypeName',

      align: 'left',
      // sorter: true,
      ellipsis: true,
    },

    {
      title: '负责人',
      dataIndex: 'principalName',
      key: 'principalName',
      align: 'left',
      // sorter: true,
      ellipsis: true,
      slots: { customRender: 'principalName' },
    },
    {
      title: '期望完成日期',
      dataIndex: 'predictEndTime',
      key: 'predictEndTime',
      customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD') : ''),
      align: 'left',
      // sorter: true,
      ellipsis: true,
    },

    {
      title: '提出人',
      dataIndex: 'exhibitorName',
      key: 'exhibitorName',
      width: '70px',
      align: 'left',
      // sorter: true,
      ellipsis: true,
    },
    {
      title: '提出日期',
      dataIndex: 'proposedTime',
      key: 'proposedTime',
      width: '100px',
      align: 'left',
      // sorter: true,
      ellipsis: true,
      slots: { customRender: 'proposedTime' },
    },
  ],

};

function visibleChange(visible: boolean) {
  // 窗口关闭时，设置状态值
  !visible && (visibleModal.value = visible);
}
async function onOk() {
  const selectRows = tableRef.value.getSelectRows();
  if (selectRows.length) {
    const data:Record<string, any> = selectRows;
    changeOkLoading(true);
    try {
      emits('checkProjectCallback', data);
      closeModal();
    } finally {
      changeOkLoading(false);
    }
  } else {
    message.warn('请选择关联问题');
  }
}
defineExpose({
  getSelectRows: computed(() => tableRef.value.getSelectRows),
});
</script>

<template>
  <BasicModal
    v-bind="$attrs"
    width="1200px"
    :height="500"
    title="关联问题"
    :onVisibleChange="visibleChange"
    @register="register"
    @ok="onOk"
  >
    <div style="height: 500px;overflow: hidden">
      <OrionTable
        ref="tableRef"
        :options="tableOptions"
      />
    </div>
  </BasicModal>
</template>

<style scoped lang="less">
.clue{
  width: 560px;
}
:deep(.ant-basic-form){
  padding:0 !important;
}
</style>
