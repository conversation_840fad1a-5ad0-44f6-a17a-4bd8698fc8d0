<script setup lang="ts">

import { Spin, Tag as ATag } from 'ant-design-vue';
import { Layout3, Layout3Content, DataStatusTag } from 'lyra-component-vue3';
import {
  computed, onMounted, provide, reactive, ref, Ref, unref,
} from 'vue';
import { useRoute } from 'vue-router';
import BasicInfo from '../components/BasicInfo/BasicInfo.vue';
import CarryOut from '../components/CarryOut/CarryOut.vue';
import RequisitionForm from '../components/RequisitionForm.vue';
import ChildOrder from '../components/ChildOrder.vue';
import PurchaseProjectApproval
  from '../components/PurchaseProjectApproval/PurchaseProjectApproval.vue';
import Api from '/@/api';
import { BasicInjectionsKey } from '../../tokens/basicKeys';
import ContractRuntime from '../components/ContractRuntime/ContractRuntime.vue';
import PaymentInfo from '../components/PaymentInfo/PaymentInfo.vue';

interface MenuItem {
  id: string,
  name: string,
  show?: boolean,
  children?: MenuItem[]
}

const typeMap = {
  total: '合同主列表',
  frameworkContract: '框架合同',
  lumpSumContract: '总价合同',
  childOrderInfo: '合同子订单',
};
const loading: Ref<boolean> = ref(false);
const layoutData = computed(() => ({
  name: basicInfo.data.name,
  ownerName: '张珊珊',
  projectCode: '申请单编码：3101202400101',
}));
const defaultActionId: Ref<string> = ref('jBXX');
const menuData: Ref<MenuItem[]> = ref([]);
const route = useRoute();
const pageSource = route.query.source;
const projectId: Ref<string> = ref(route.params.id as string);
const basicInfo = reactive({
  data: {},
});
provide(BasicInjectionsKey, basicInfo);

const getPowerDataHandle = async (data: any) => {
  const type = route.query.source || 'lumpSumContract';
  const basicArr = [
    {
      id: 'jBXX',
      name: '基本信息',
      show: true,
    },
    {
      id: 'cGLX',
      name: '采购立项',
    },
    {
      id: 'hTZX',
      name: '合同执行',
    },
  ];
  menuData.value = menuData.value.concat(basicArr);
};

function menuChange(option: { id: string, index: number, item: MenuItem }): void {
  defaultActionId.value = option.id;
}

const getInfo = async () => {
  try {
    const result = await new Api('/spm/contractInfo').fetch('', unref(projectId), 'GET');
    basicInfo.data = result;
  } catch (e) {

  }
};
onMounted(async () => {
  await getPowerDataHandle();
  await getInfo();
});
</script>

<template>
  <Layout3
    :projectData="layoutData"
    :menuData="menuData"
    :defaultActionId="defaultActionId"
    :type="2"
    :onMenuChange="menuChange"
  >
    <template #code>
      <h2
        class="page-title"
        :title="basicInfo.data?.contractName"
      >
        {{ basicInfo.data?.contractName }}
      </h2>
      <div class="page-subtitle">
        <span>合同编号：{{ basicInfo.data?.contractNumber }}</span>
        <span>采购立项号：{{ basicInfo.data?.projectCode }}</span>
        <span>采购申请号：{{ basicInfo.data?.purchaseApplicant }}</span>
      </div>
    </template>
    <template #header-info>
      <div class="type-map">
        <div class="type-wrapper">
          <h2 class="type-h2-enum">
            {{ typeMap[pageSource] }}
          </h2>
          <h2 class="type-p-enum">
            {{ typeMap[pageSource] ||'合同主列表' }}
          </h2>
        </div>
        <a-tag
          style="margin-left: 20px;"
          color="purple"
        >
          已审核
        </a-tag>
        <a-tag color="green">
          履行中
        </a-tag>
      </div>
    </template>
    <div
      v-if="loading"
      class="w-full h-full flex flex-pc flex-ac"
    >
      <Spin />
    </div>
    <Layout3Content v-else>
      <BasicInfo v-if="defaultActionId==='jBXX'" />
      <PurchaseProjectApproval v-if="defaultActionId==='cGLX'" />
      <CarryOut v-if="defaultActionId==='cGZX'" />
      <RequisitionForm v-if="defaultActionId==='xQD'" />
      <ChildOrder v-if="defaultActionId==='zDD'" />
      <ContractRuntime v-if="defaultActionId==='hTZX'" />
      <PaymentInfo v-if="defaultActionId==='zFXX'" />
    </Layout3Content>
  </Layout3>
</template>

<style scoped lang="less">
:deep(.header-wrap) {
  min-height: 60px !important;
  height: auto !important;
  .header-main{
    min-height: 33px !important;
    height: auto !important;
  }
  .project-title {
    width: auto !important;
    max-width: 600px !important;
    min-width: 300px;
    .flex-te{
      word-wrap: break-word;
      overflow-wrap: break-word;
      white-space: normal;
      line-height: 26px;
      padding-top: 10px;
    }
  }

  .layout-menu-warp {
    display: flex;
    align-items: center;

    .ant-menu {
      height: 60px !important;
    }
  }
}

.page-title {
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
  line-height: 26px;
  padding-top: 10px;
  font-size: 18px;
  color: #000000D9;
}
.page-subtitle {
  display: flex;
  flex-wrap: wrap;
  font-size: 14px;
  color: rgb(150, 158, 180);
  font-weight: 40;
  width: 400px;

  span {
    margin-right: 20px;
  }
}

.type-map {
  display: flex;
  align-items: center;
  .type-wrapper{
    display: flex;
    flex-direction: column;
  }

  .type-h2-enum {
    font-size: 14px;
    color: #000000D9;
    margin-bottom: 3px;
  }

  .type-p-enum {
    font-size: 12px;
    color: #000000D9;
    margin-bottom: 0;
  }
}

</style>