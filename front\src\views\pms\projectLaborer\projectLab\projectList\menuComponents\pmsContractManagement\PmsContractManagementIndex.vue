<template>
  <OrionTable
    v-is-power="['PMS_HTGL_container_htgl01']"
    :options="tableOptions"
  />
</template>

<script setup lang="ts">
import {
  reactive, ref, h, inject,
} from 'vue';
import { OrionTable, isPower } from 'lyra-component-vue3';
import Api from '/@/api';
import { formatMoney } from '/@/views/pms/utils/utils';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';

const router = useRouter();
const props = defineProps({});
const projectInfo: any = inject('formData');
const powerData: any = inject('powerData');
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  showSmallSearch: true,
  rowSelection: false,
  smallSearchField: ['number'],
  showIndexColumn: true,
  api: (tableParams) => new Api('/pas').fetch({
    ...tableParams,
    query: {
      projectNumber: projectInfo.value?.number,
    },
  }, 'incomeContract/page', 'POST'),
  columns: [
    {
      title: '合同编码',
      dataIndex: 'number',
      customRender: ({ record, text }) => h('span', {
        class: isPower('PMS_HTGL_button_goDetails', powerData) ? 'action-btn' : '',
        onClick: () => {
          if (!isPower('PMS_HTGL_button_goDetails', powerData)) {
            return;
          }
          router.push({
            name: 'PmsContractManagementDetails',
            params: {
              id: record?.id,
            },
          });
        },
      }, text),
    },
    {
      title: '销售部门',
      dataIndex: 'saleDept',
    },
    {
      title: '客户经理',
      dataIndex: 'accountManager',
    },
    {
      title: '客户代码',
      dataIndex: 'clientCode',
    },
    {
      title: '最终用户',
      dataIndex: 'finalUser',
    },
    {
      title: '商机编号',
      dataIndex: 'opportunityNumber',
    },
    {
      title: '项目编码',
      dataIndex: 'projectNumber',
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
    },
    {
      title: '合同类型',
      dataIndex: 'type',
    },
    {
      title: '订单类型',
      dataIndex: 'orderType',
    },
    {
      title: '产品线',
      dataIndex: 'productLine',
    },
    {
      title: '商务员',
      dataIndex: 'clerk',
    },
    {
      title: '金额（元）',
      dataIndex: 'amount',
      customRender: ({ text }) => (text ? formatMoney(text) : ''),
    },
    {
      title: '是否有研制条款',
      dataIndex: 'terms',
      customRender: ({ text }) => (text ? '是' : '否'),
    },
    {
      title: '产品运营分类',
      dataIndex: 'productOperateType',
    },
    {
      title: '项目背景',
      dataIndex: 'projectContext',
    },
    {
      title: '部门受理日期',
      dataIndex: 'deptAcceptTime',
      customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD') : ''),
    },
  ],
});
</script>
