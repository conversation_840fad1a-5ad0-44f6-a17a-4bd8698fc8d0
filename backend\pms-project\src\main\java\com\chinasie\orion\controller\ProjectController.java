package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ProjectDTO;
import com.chinasie.orion.domain.dto.ProjectUpdateStatusDTO;
import com.chinasie.orion.domain.dto.ProjectZoneDTO;
import com.chinasie.orion.domain.dto.SearchDTO;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.query.ProjectAndUserTrdProjectQuery;
import com.chinasie.orion.domain.query.ProjectAndUserTrdUserQuery;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectBaseInformationService;
import com.chinasie.orion.service.ProjectService;
import com.chinasie.orion.service.ProjectWFService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.*;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/06/10:38
 * @description:
 */
@RestController
@RequestMapping("/project")
@Api(tags = "项目")
public class ProjectController {

    @Resource
    private ProjectService projectService;

    @Resource(name = "projectWFService")
    private ProjectWFService wfService;

    @Resource(name = "projectBaseInformationService")
    private ProjectBaseInformationService projectBaseInformationService;

    @ApiOperation("新增项目")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectDTO", dataType = "ProjectDTO")
    })
    @PostMapping(value = "/save")
    @LogRecord(success = "【{USER{#logUserId}}】新增项目", type = "项目", subType = "新增项目", bizNo = "")
    public ResponseDTO<String> saveProject(@RequestBody @Validated ProjectDTO projectDTO) throws Exception {
        try {
            return new ResponseDTO<>(projectService.saveProject(projectDTO));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @ApiOperation("获取项目列表 简洁版")
    @GetMapping(value = "/getList")
    @LogRecord(success = "【{USER{#logUserId}}】获取项目列表 简洁版", type = "项目", subType = "列表 简洁版", bizNo = "")
    public ResponseDTO<List<SimpleVo>> getProjectSimpleList(@RequestParam(required = false) String keyword,@RequestParam(required = false) Boolean isProjectMember) throws Exception {
        try {
            if(isProjectMember == null){
                isProjectMember = false;
            }
            return new ResponseDTO<>(projectService.getProjectSimpleList(keyword,isProjectMember));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @ApiOperation("项目分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping(value = "/getPage")
    @LogRecord(success = "【{USER{#logUserId}}】项目分页", type = "项目", subType = "分页", bizNo = "")
    public ResponseDTO<Page<NewProjectHomePageVO>> getProjectPage(@RequestBody Page<ProjectDTO> pageRequest) throws Exception {
        try {
            return new ResponseDTO<>(projectService.getProjectPage(pageRequest));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @ApiOperation(value = "项目状态分页")
    @RequestMapping(value = "/getStatusPage", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】项目状态分页", type = "项目", subType = "项目状态分页", bizNo = "")
    public ResponseDTO<NewProjectHomePageVO> getStatusPage(@RequestBody Page<ProjectDTO> pageRequest) throws Exception {
        NewProjectHomePageVO rsp = projectService.getStatusPage(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "首页-项目专区")
    @RequestMapping(value = "/getProject", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】首页-项目专区", type = "项目", subType = "首页-项目专区", bizNo = "")
    public ResponseDTO<Page<ProjectZoneDTO>>getProjectZone(@RequestBody Page<ProjectDTO> pageRequest) throws Exception {
        Page<ProjectZoneDTO> rsp =  projectService.getProjectZone(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 搜索项目
     *
     * @param searchDTO
     * @return
     */
    @RequestMapping(value = "/search", method = {RequestMethod.POST})
    @LogRecord(success = "【{USER{#logUserId}}】搜索项目", type = "项目", subType = "搜索项目", bizNo = "")
    public ResponseDTO<List<ProjectVO>> search(@RequestBody SearchDTO searchDTO) throws Exception {
        List<ProjectVO> rsp = projectService.search(searchDTO);
        return new ResponseDTO<>(rsp);
    }

    @RequestMapping(value = "/listByPM", method = {RequestMethod.POST})
    @LogRecord(success = "【{USER{#logUserId}}】查询项目经理的项目", type = "项目", subType = "查询项目经理的项目", bizNo = "")
    public ResponseDTO<Page<ProjectVO>> listByPM(@RequestBody Page<ProjectDTO> pageRequest) throws Exception {
        Page<ProjectVO> rsp = projectService.listByPM(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    @RequestMapping(value = "/listPD", method = {RequestMethod.POST})
    @LogRecord(success = "【{USER{#logUserId}}】查询项目总监的项目", type = "项目", subType = "查询项目总监的项目", bizNo = "")
    public ResponseDTO<List<UserVO>> listPD(@RequestBody List<String> projectIds) throws Exception {
        List<UserVO> rsp = projectService.listPD(projectIds);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("项目详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", dataType = "String")
    })
    @GetMapping(value = "/detail/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】项目详情", type = "项目", subType = "详情", bizNo = "")
    public ResponseDTO<ProjectVO> getProjectDetail(@PathVariable("id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        try {
            return new ResponseDTO<>(projectService.getProjectDetail(id, pageCode));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @ApiOperation("项目列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", dataType = "List")
    })
    @GetMapping(value = "/list")
    @LogRecord(success = "【{USER{#logUserId}}】项目列表", type = "项目", subType = "列表", bizNo = "")
    public ResponseDTO<List<ProjectVO>> getProjectList(@RequestParam("ids") List<String> ids) throws Exception {
        try {
            return new ResponseDTO<>(projectService.getProjectList(ids));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }


    @ApiOperation("获取项目的项目经理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", dataType = "String")
    })
    @GetMapping(value = "/pm/{projectId}")
    @LogRecord(success = "【{USER{#logUserId}}】获取项目的项目经理", type = "项目", subType = "获取项目的项目经理", bizNo = "{{#projectId}}")
    public ResponseDTO<UserVO> getProjectPm(@PathVariable("projectId") String projectId) throws Exception {
        try {
            return new ResponseDTO<>(projectService.getProjectPm(projectId));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }


    @ApiOperation("编辑项目")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectDTO", dataType = "ProjectDTO")
    })
    @PutMapping(value = "/edit")
    @LogRecord(success = "【{USER{#logUserId}}】编辑项目", type = "项目", subType = "编辑项目", bizNo = "{{#projectDTO.id}}")
    public ResponseDTO<Boolean> editProject(@RequestBody @Validated ProjectDTO projectDTO) throws Exception {
        try {
            return new ResponseDTO<>(projectService.editProject(projectDTO));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @ApiOperation("批量删除项目")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", dataType = "List")
    })
    @DeleteMapping(value = "/removeBatch")
    @LogRecord(success = "【{USER{#logUserId}}】批量删除项目", type = "项目", subType = "批量删除项目", bizNo = "{{#ids.toString}}")
    public ResponseDTO<Boolean> removeProject(@RequestBody List<String> ids) throws Exception {
        try {
            return new ResponseDTO<>(projectService.removeProject(ids));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @ApiOperation("获取产品列表")
    @GetMapping(value = "/getProductList")
    @LogRecord(success = "【{USER{#logUserId}}】获取产品列表", type = "项目", subType = "获取产品列表", bizNo = "")
    public ResponseDTO<List<SimpleVo>> getProductList() throws Exception {
        try {
            return new ResponseDTO<>(projectService.getProductList());
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 项目计划甘特图
     *
     * @return
     */
    @ApiOperation("项目计划甘特图")
    @RequestMapping(value = "/gantt/{id}", method = {RequestMethod.GET})
    @LogRecord(success = "【{USER{#logUserId}}】获取项目计划甘特图", type = "项目", subType = "获取项目计划甘特图", bizNo = "{{#id}}")
    public ResponseDTO<List<ProjectPlanGanttVO>> gantt(@PathVariable("id") String projectId) throws Exception {
        List<ProjectPlanGanttVO> rsp = projectService.gantt(projectId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 获取这个人的项目
     */

    @ApiOperation("获取这个人的项目")
    @RequestMapping(value = "/pageByUserCode/{userCode}", method = {RequestMethod.POST})
    @LogRecord(success = "【{USER{#logUserId}}】获取这个人的项目", type = "项目", subType = "获取这个人的项目", bizNo = "")
    public ResponseDTO<com.chinasie.orion.sdk.metadata.page.Page<ProjectVO>> pageProjectByUserCode(@PathVariable("userCode") String userCode, @RequestBody Page<ProjectDTO> pageRequest) throws Exception {
        com.chinasie.orion.sdk.metadata.page.Page<ProjectVO> rsp = projectService.pageProjectByUserCode(userCode, pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("项目编号查询项目详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "number", dataType = "String")
    })
    @GetMapping(value = "/detailByNumber/{number}")
    @LogRecord(success = "【{USER{#logUserId}}】项目编号查询项目详情", type = "项目", subType = "项目编号查询项目详情", bizNo = "{{#number}}")
    public ResponseDTO<ProjectVO> getProjectDetailByNumber(@PathVariable("number") String number) throws Exception {
        try {
            return new ResponseDTO<>(projectService.getProjectDetailByNumber(number));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @ApiOperation("更新项目状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectUpdateStatusDTO", dataType = "projectUpdateStatusDTO")
    })
    @PostMapping(value = "/updateStatus")
    @LogRecord(success = "【{USER{#logUserId}}】更新项目状态", type = "项目", subType = "更新项目状态", bizNo = "{{#projectUpdateStatusDTO.projectId}}")
    public ResponseDTO<Boolean> updateStatus(@RequestBody @Validated ProjectUpdateStatusDTO projectUpdateStatusDTO) throws Exception {
        try {
            return new ResponseDTO<>(projectService.updateStatus(projectUpdateStatusDTO));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }


    @ApiOperation("门户项目列表")
    @ApiParam(name = "type", value = " 1-准备中 2-实施中 3-已完成 4-已超期 传空为全部")
    @GetMapping("/homePage")
    @LogRecord(success = "【{USER{#logUserId}}】门户项目列表", type = "项目", subType = "门户项目列表", bizNo = "")
    public ResponseDTO<List<Project>> homePage(@RequestParam(name = "type", required = false) Integer type) throws Exception {
        List<Project> result = projectService.homePage(type);
        return ResponseDTO.success(result);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】详情", type = "项目", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<NewProjectVO> getSingleDetail(@PathVariable(value = "id") String id, @RequestParam(value = "pageCode", required = false) String pageCode) throws Exception {
        NewProjectVO rsp = projectService.getSingleDetail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("工作台项目列表")
    @PostMapping("/workBenchPage/{type}")
    @LogRecord(success = "【{USER{#logUserId}}】工作台项目列表", type = "项目", subType = "工作台项目列表", bizNo = "{{#type}}")
    public ResponseDTO<Page<ProjectVO>> workBenchPage(@PathVariable(value = "type") Integer type, @RequestBody Page<ProjectDTO> pageRequest) throws Exception {
        Page<ProjectVO> result = projectService.workBenchPage(type, pageRequest);
        return ResponseDTO.success(result);
    }

    @ApiOperation("获取项目列表")
    @PostMapping("/getByIds")
    @LogRecord(success = "【{USER{#logUserId}}】获取项目列表", type = "项目", subType = "获取项目列表", bizNo = "")
    public ResponseDTO<List<ProjectVO>> getByIds(@RequestBody List<String> ids) throws Exception {
         List<ProjectVO> result = projectService.getProjectListById(ids);
        return ResponseDTO.success(result);
    }

    @ApiOperation("获取项目列表")
    @PostMapping("/getByName")
    @LogRecord(success = "【{USER{#logUserId}}】获取项目列表", type = "项目", subType = "获取项目列表", bizNo = "")
    public ResponseDTO<List<ProjectVO>> getByIds(@RequestBody ProjectDTO projectDTO) throws Exception{
        List<ProjectVO> result = projectService.getProjectListByName(projectDTO);
        return ResponseDTO.success(result);
    }


    /**
     * 第三方请求分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "第三方请求分页")
    @RequestMapping(value = "/third/pages", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】第三方请求分页", type = "项目", subType = "第三方请求分页", bizNo = "")
    public ResponseDTO<Page<NewProjectHomePageVO>> getThirdProjectPage(@RequestBody Page<SearchDTO> pageRequest) throws Exception {
        Page<NewProjectHomePageVO> rsp = projectService.getThirdProjectPage(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("判断用户在某项目下是否是项目经理的角色")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", dataType = "String"),
            @ApiImplicitParam(name = "projectId", dataType = "String")
    })
    @GetMapping(value = "/isPmToUserId/{userId}/{projectId}")
    @LogRecord(success = "【{USER{#logUserId}}】判断用户在某项目下是否是项目经理的角色", type = "项目", subType = "判断用户在某项目下是否是项目经理的角色", bizNo = "{{#projectId}}")
    public ResponseDTO<Boolean> removeProject(@PathVariable(value = "projectId")String projectId,@PathVariable(value = "userId")String userId) throws Exception {
        Boolean b = projectService.isPmToUserId(projectId, userId);
        return new ResponseDTO<>(b);
    }

    @ApiOperation("获取项目id列表")
    @PostMapping("/getProjectIds")
    @LogRecord(success = "【{USER{#logUserId}}】获取项目id列表", type = "项目", subType = "获取项目id列表", bizNo = "")
    public ResponseDTO<List<String>> getProjectIds(@RequestBody ProjectDTO projectDTO) throws Exception{
        List<String> result = projectService.getProjectIdList(projectDTO);
        return ResponseDTO.success(result);
    }

    @ApiOperation("工时获取项目分页")
    @PostMapping("/getWorkHourProjectPage")
    @LogRecord(success = "【{USER{#logUserId}}】工时获取项目分页", type = "项目", subType = "工时获取项目分页", bizNo = "")
    public ResponseDTO<Page<ProjectVO>> getWorkHourProjectPage(@RequestBody Page<ProjectDTO> page) throws Exception{
        Page<ProjectVO> result = projectService.getWorkHourProjectPage(page);
        return ResponseDTO.success(result);
    }

    @ApiOperation("工时获取项目分页")
    @PostMapping("/allList")
    @LogRecord(success = "【{USER{#logUserId}}】工时获取项目分页", type = "项目", subType = "工时获取项目分页", bizNo = "")
    public ResponseDTO<Page<ProjectVO>> allList(@RequestBody Page<ProjectDTO> pageRequest) throws Exception{
        Page<ProjectVO> result = projectService.allList(pageRequest);
        return ResponseDTO.success(result);
    }


    @ApiOperation("项目分页:用于项目集")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping(value = "/projectCollection/getPage")
    @LogRecord(success = "【{USER{#logUserId}}】项目分页:用于项目集", type = "项目", subType = "项目分页:用于项目集", bizNo = "")
    public ResponseDTO<Page<ProjectVO>> getProjectPageForProjectCollection(@RequestBody Page<ProjectDTO> pageRequest) throws Exception {
        try {
            return new ResponseDTO<>(projectService.getProjectPageForProjectCollection(pageRequest));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }


    @ApiOperation("启动项目")
    @GetMapping("/start/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】启动项目", type = "项目", subType = "启动项目", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> start(@PathVariable("id")String id){
        Boolean start = wfService.start(id);
        return new ResponseDTO<>(start);
    }

    @ApiOperation("关闭项目")
    @GetMapping("/close/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】关闭项目", type = "项目", subType = "关闭项目", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> close(@PathVariable("id")String id){
        Boolean close = wfService.close(id);
        return new ResponseDTO<>(close);
    }
    @ApiOperation("申请PLM产品编码")
    @GetMapping("/applyPLM/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】申请PLM产品编码", type = "项目", subType = "申请PLM产品编码", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> applyPLM(@PathVariable("id")String id){
        Boolean applyPLM = projectBaseInformationService.applyPLM(id);
        return new ResponseDTO<>(applyPLM);
    }
    @ApiOperation("项目分页 简洁版")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping(value = "/getSimplePage")
    @LogRecord(success = "【{USER{#logUserId}}】项目分页 简洁版", type = "项目", subType = "项目分页 简洁版", bizNo = "")
    public ResponseDTO<Page<ProjectSimpleVO>> getProjectSimplePage(@RequestBody Page<ProjectDTO> pageRequest) throws Exception {
        try {
            return new ResponseDTO<>(projectService.getProjectSimplePage(pageRequest));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @ApiOperation("同步立项&策划信息")
    @GetMapping("/syncApproval/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】同步立项&策划信息", type = "项目", subType = "同步立项&策划信息", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> syncApproval(@PathVariable("id")String id){
        Boolean syncApproval = projectBaseInformationService.syncApproval(id);
        return new ResponseDTO<>(syncApproval);
    }

    @ApiOperation("获取项目基础信息-地址")
    @GetMapping("/getPlace/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】获取项目基础信息-地址", type = "项目", subType = "获取项目基础信息-地址", bizNo = "{{#id}}")
    public ResponseDTO<List<PlaceVO> > getPlace(@PathVariable("id")String id){
        List<PlaceVO>  syncApproval = projectBaseInformationService.getPlace(id);
        return new ResponseDTO<>(syncApproval);
    }

    @ApiOperation("获取一个项目名称")
    @GetMapping("/getProjectName/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】获取一个项目名称", type = "项目", subType = "获取一个项目名称", bizNo = "{{#id}}")
    public ResponseDTO<String> getProjectName(@PathVariable("id")String id){
        Project byId = projectService.getById(id);
        return new ResponseDTO<>(byId.getName());
    }

    @ApiOperation("获取项目id列表-指定状态")
    @GetMapping("/getProjectIds/status")
    @LogRecord(success = "【{USER{#logUserId}}】获取项目id列表-指定状态", type = "项目", subType = "获取项目id列表-指定状态", bizNo = "")
    public ResponseDTO<List<String>> getProjectIdsForStatus() throws Exception {
        List<String> projectIdsForStatus = projectService.getProjectIdsForStatus();
        return new ResponseDTO<>(projectIdsForStatus);
    }


    @ApiOperation("初始化项目角色")
    @PostMapping("/init/project/role")
    @LogRecord(success = "【{USER{#logUserId}}】初始化项目角色", type = "项目", subType = "初始化项目角色", bizNo = "")
    public ResponseDTO<Boolean> initProjectRole() throws Exception {
        projectService.initProjectRole();
        return new ResponseDTO<>(Boolean.TRUE);
    }

    @ApiOperation("根据用户查询用户参与的项目")
    @PostMapping("/query/project/by/user")
    @LogRecord(success = "【{USER{#logUserId}}】根据用户查询用户参与的项目", type = "项目", subType = "根据用户查询用户参与的项目", bizNo = "")
    public ResponseDTO<?> queryProjectByUser(@RequestBody @Validated ProjectAndUserTrdUserQuery query)  {
        return new ResponseDTO<>(projectService.queryProjectByUser(query));
    }

    @ApiOperation("根据项目查找项目下参与的成员")
    @PostMapping("/query/user/by/project")
    @LogRecord(success = "【{USER{#logUserId}}】根据项目查找项目下参与的成员", type = "项目", subType = "根据项目查找项目下参与的成员", bizNo = "")
    public ResponseDTO<?> queryUserByProject(@RequestBody @Validated ProjectAndUserTrdProjectQuery query)  {
        return new ResponseDTO<>(projectService.queryUserByProject(query));
    }


    @ApiOperation("获取财务项目分页")
    @PostMapping("/getIncomePages")
    @LogRecord(success = "【{USER{#logUserId}}】获取财务项目分页", type = "项目", subType = "获取财务项目分页", bizNo = "")
    public ResponseDTO<Page<ProjectVO>> getIncomePages(@RequestBody Page<ProjectDTO> pageRequest) throws Exception{
        Page<ProjectVO> result = projectService.getPage(pageRequest);
        return ResponseDTO.success(result);
    }


    @ApiOperation("查看项目基础信息列表")
    @GetMapping("/basic/info/list")
    @LogRecord(success = "【{USER{#logUserId}}】 查看项目基础信息列表", type = "项目", subType = "查看项目基础信息列表", bizNo = "")
    public ResponseDTO<List<ProjectSimpleBasicVO>> getProjectBasicInfoList()  {
        return new ResponseDTO<>(projectService.getProjectBasicInfoList());
    }

}
