package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;

/**
 * EvaluationDetail Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-24 10:28:14
 */
@ApiModel(value = "EvaluationDetailVO对象", description = "项目评价详情")
@Data
public class EvaluationDetailVO extends ObjectVO implements Serializable{

            /**
         * 评分
         */
        @ApiModelProperty(value = "评分")
        private Integer score;

        /**
         * 数据字典所对应的编码
         */
        @ApiModelProperty(value = "数据字典所对应的编码")
        private String evaluationDetailType;

        /**
         * 评价内容
         */
        @ApiModelProperty(value = "评价内容")
        private String evaluationContent;

        /**
         * 评价id
         */
        @ApiModelProperty(value = "评价id")
        private String evaluationId;

        /**
         * 排序
         */
        @ApiModelProperty(value = "排序")
        private Integer sort;

        /**
         * 项目ID
         */
        @ApiModelProperty(value = "项目ID")
        private String projectId;
        /**
         * 详情评价名
         */
        @ApiModelProperty(value = "详情评价名")
        private String name;

    }
