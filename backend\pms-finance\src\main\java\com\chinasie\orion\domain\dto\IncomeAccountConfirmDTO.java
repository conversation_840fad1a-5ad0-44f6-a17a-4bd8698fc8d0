package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.page.SearchCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * IncomeAccountConfirm DTO对象
 *
 * <AUTHOR>
 * @since 2024-12-18 21:16:26
 */
@ApiModel(value = "IncomeAccountConfirmDTO对象", description = "收入记账明细确认表")
@Data
@ExcelIgnoreUnannotated
public class IncomeAccountConfirmDTO extends  ObjectDTO   implements Serializable{

    /**
     * 凭证类型
     */
    @ApiModelProperty(value = "凭证类型")
    @ExcelProperty(value = "凭证类型 ", index = 0)
    private String voucherType;

    /**
     * 是否调账凭证
     */
    @ApiModelProperty(value = "是否调账凭证")
    @ExcelProperty(value = "是否调账凭证 ", index = 1)
    private String adjAccountVoucher;

    /**
     * 确认状态
     */
    @ApiModelProperty(value = "确认状态")
    @ExcelProperty(value = "确认状态 ", index = 2)
    private String confirmStatus;

    /**
     * 公司代码
     */
    @ApiModelProperty(value = "公司代码")
    @ExcelProperty(value = "公司代码 ", index = 3)
    private String companyCode;

    /**
     * 科目
     */
    @ApiModelProperty(value = "科目")
    @ExcelProperty(value = "科目 ", index = 4)
    private String subject;

    /**
     * 分配编码
     */
    @ApiModelProperty(value = "分配编码")
    @ExcelProperty(value = "分配编码 ", index = 5)
    private String distributiveCode;

    /**
     * 过账期间
     */
    @ApiModelProperty(value = "过账期间")
    @ExcelProperty(value = "过账期间 ", index = 6)
    private Date postingDate;

    /**
     * 凭证编号
     */
    @ApiModelProperty(value = "凭证编号")
    @ExcelProperty(value = "凭证编号 ", index = 7)
    private String voucherNum;

    /**
     * 凭证日期
     */
    @ApiModelProperty(value = "凭证日期")
    @ExcelProperty(value = "凭证日期 ", index = 8)
    private Date voucherDate;

    /**
     * 过账期间
     */
    @ApiModelProperty(value = "过账期间")
    @ExcelProperty(value = "过账期间 ", index = 9)
    private String postingPeriod;

    /**
     * 本币金额
     */
    @ApiModelProperty(value = "本币金额")
    @ExcelProperty(value = "本币金额 ", index = 10)
    private BigDecimal localCurrencyAmt;

    /**
     * 利润中心
     */
    @ApiModelProperty(value = "利润中心")
    @ExcelProperty(value = "利润中心 ", index = 11)
    private String profitCenter;

    /**
     * 成本中心
     */
    @ApiModelProperty(value = "成本中心")
    @ExcelProperty(value = "成本中心 ", index = 12)
    private String costCenter;

    /**
     * 文本
     */
    @ApiModelProperty(value = "文本")
    @ExcelProperty(value = "成本中心 ", index = 13)
    private String conText;

    /**
     * 收入计划编号
     */
    @ApiModelProperty(value = "收入计划编号")
    @ExcelProperty(value = "收入计划编号 ", index = 14)
    private String incomePlanNum;

    /**
     * 是否修改凭证类型
     */
    @ApiModelProperty(value = "是否修改凭证类型")
    @ExcelProperty(value = "是否修改凭证类型 ", index = 15)
    private String isUpdate;

    /**
     * 是否标红
     */
    @ApiModelProperty(value = "是否标红")
    @ExcelProperty(value = "是否标红 ", index = 16)
    private String isRed;

    @ApiModelProperty(value = "凭证编号")
    private List<String> voucherNums;


    @ApiModelProperty(value = "导出id")
    private List<String> ids;

    @Autowired
    private List<List<SearchCondition>> searchConditions;


}
