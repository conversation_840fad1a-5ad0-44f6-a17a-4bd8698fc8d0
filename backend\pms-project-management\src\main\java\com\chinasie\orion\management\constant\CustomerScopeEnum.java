package com.chinasie.orion.management.constant;

/**
 * 客户范围字典
 */

public enum CustomerScopeEnum {

    OVERSEAS("overseas","境外"),
    DOMESTIC("domestic","境内");

    private String name;
    private String desc;

    CustomerScopeEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(String value){

        for (CustomerScopeEnum lt : CustomerScopeEnum.values()) {
            if(lt.name.equals( value)){
                return lt.desc;
            }
        }
        return null;
    }


}