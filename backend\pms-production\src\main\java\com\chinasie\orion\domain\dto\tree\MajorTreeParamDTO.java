package com.chinasie.orion.domain.dto.tree;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/11/20/9:03
 * @description:
 */
@Data
public class MajorTreeParamDTO implements Serializable {

    @ApiModelProperty("大修轮次")
    @NotEmpty(message = "大修轮次不能为空")
    private String repairRound;


    @ApiModelProperty("关键词")
    private String keyword;
    @ApiModelProperty("组织ID集合")
    private List<String> majorRepairOrgIds;
}
