package com.chinasie.orion.controller;

import com.chinasie.orion.constant.BasicUserEnum;
import com.chinasie.orion.domain.vo.BasicUserInfoVO;
import com.chinasie.orion.service.BasicUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.domain.dto.BasicUserDTO;
import com.chinasie.orion.domain.vo.BasicUserVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import java.util.stream.Collectors;

import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * BasicUser 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05 09:17:18
 */
@RestController
@RequestMapping("/basic-user")
@Api(tags = "员工能力库人员信息基础表")
public class  BasicUserController  {

    @Autowired
    private BasicUserService basicUserService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】【员工能力库】 查询员工号【{{#userCode}}】【{{#id}}】人员信息详情", type = "BasicUser", subType = "查询详情", bizNo = "{{#id}}")
    public ResponseDTO<BasicUserVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        BasicUserVO rsp = basicUserService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param basicUserDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】【员工能力库】 新增数据【{{#basicUserDTO.name}}】工号【{{#basicUserDTO.userCode}}】", type = "BasicUser", subType = "新增", bizNo = "")
    public ResponseDTO<String> create(@RequestBody BasicUserDTO basicUserDTO) throws Exception {
        String rsp =  basicUserService.create(basicUserDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }




    /**
     * 编辑
     *
     * @param BasicUserDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】 【员工能力库】 编辑了数据【{{#basicUserDTO.name}}】工号【{{#basicUserDTO.userCode}}】", type = "BasicUser", subType = "编辑", bizNo = "{{#basicUserDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  BasicUserDTO basicUserDTO) throws Exception {
        Boolean rsp = basicUserService.edit(basicUserDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】【员工能力库】 删除工号【{{#userCodes}}】数据", type = "BasicUser", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = basicUserService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】【员工能力库】批量删除工号【{{#userCodes}}】数据", type = "BasicUser", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = basicUserService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】【员工能力库】查询分页数据", type = "BasicUser", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<BasicUserVO>> pages(@RequestBody Page<BasicUserDTO> pageRequest) throws Exception {
        BasicUserDTO query = pageRequest.getQuery();
        if(query == null){
            query = new BasicUserDTO();
        }
        query.setPersonType(BasicUserEnum.BASIC_USER.getType());
        pageRequest.setQuery(query);
        Page<BasicUserVO> rsp =  basicUserService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "获取人员基本信息")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】 【员工能力库】获取人员{{#userCode}}基本信息", type = "BasicUser", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/detail/userCode", method = RequestMethod.GET)
    public ResponseDTO<BasicUserVO> detailUserCode(@RequestParam("userCode") String userCode ) throws Exception {
        BasicUserVO rsp =  basicUserService.detailUserCode( userCode);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页查询人员基本信息
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页查询人员基本信息")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】【员工能力库】查询分页数据", type = "BasicUser", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/getUserInfo", method = RequestMethod.POST)
    public ResponseDTO<Page<BasicUserInfoVO>> getUserInfo(@RequestBody Page<BasicUserDTO> pageRequest) throws Exception {
        Page<BasicUserInfoVO> rsp = basicUserService.getUserInfo(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("批量导出人员（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】【员工能力库】导出数据", type = "SupplierInfo", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody Page<BasicUserDTO> pageRequest, HttpServletResponse response) throws Exception {
        basicUserService.exportByExcel(pageRequest, response);
    }

}
