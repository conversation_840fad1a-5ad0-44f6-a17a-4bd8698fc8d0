package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.EvaluationDetailDTO;
import com.chinasie.orion.domain.dto.EvaluationProjectDTO;
import com.chinasie.orion.domain.vo.EvaluationDetailVO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.page.PageRequest;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.service.EvaluationDetailService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.dto.ResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.lang.Exception;
import java.lang.String;

/**
 * <p>
 * EvaluationDetail 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24 10:28:14
 */
@RestController
@RequestMapping("/evaluation-detail")
@Api(tags = "项目评价详情")
public class EvaluationDetailController {

    @Resource
    private EvaluationDetailService evaluationDetailService;


    /**
     * 新增
     *
     * @param evaluationDetailDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】新增", type = "项目评价详情", subType = "新增", bizNo = "")
    public ResponseDTO<EvaluationDetailVO> create(@RequestBody EvaluationDetailDTO evaluationDetailDTO) throws Exception {
        EvaluationDetailVO rsp = evaluationDetailService.create(evaluationDetailDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param evaluationDetailDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @LogRecord(success = "【{USER{#logUserId}}】编辑", type = "项目评价详情", subType = "编辑", bizNo = "")
    public ResponseDTO<List<EvaluationDetailVO>> edit(@RequestBody EvaluationDetailDTO evaluationDetailDTO) throws Exception {
        List<EvaluationDetailVO> rsp = evaluationDetailService.edit(evaluationDetailDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除", type = "项目评价详情", subType = "删除", bizNo = "")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = evaluationDetailService.remove(ids);
        return new ResponseDTO(rsp);
    }

//    @ApiOperation("项目评价详情页")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
//    })
//    @PostMapping("/detail")
//    public ResponseDTO<EvaluationProjectDetailVO> getEvaluationProjectDetail(@Valid @RequestBody EvaluationProjectDetailDTO evaluationProjectDetailDTO) throws Exception {
//        try {
//            return new ResponseDTO<>(evaluationDetailService.getEvaluationProjectDetail(evaluationProjectDetailDTO));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }

    @ApiOperation("项目评价详情页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @GetMapping("/detail/{evaluationProjectId}")
    @LogRecord(success = "【{USER{#logUserId}}】获取项目评价详情页", type = "项目评价详情", subType = "获取项目评价详情页", bizNo = "")
    public ResponseDTO<List<EvaluationDetailVO>> getEvaluationDetailList(@PathVariable(value = "evaluationProjectId") String evaluationProjectId) throws Exception {
        return new ResponseDTO<>(evaluationDetailService.getEvaluationDetailList(evaluationProjectId));
    }

    @ApiOperation("项目评价详情文件分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping("/file/getPage")
    @LogRecord(success = "【{USER{#logUserId}}】获取项目评价详情文件分页", type = "项目评价详情", subType = "获取项目评价详情文件分页", bizNo = "")
    public ResponseDTO<PageResult<FileVO>> getFilePage(@Valid @RequestBody PageRequest<EvaluationProjectDTO> pageRequest) throws Exception {
        return new ResponseDTO<>(evaluationDetailService.getFilePage(pageRequest));
    }

    /**
     * 导入文件
     *
     * @param files
     * @return
     */
    @ApiOperation("导入文件")
    @RequestMapping(value = "importfiles/{id}", method = {RequestMethod.POST})
    @LogRecord(success = "【{USER{#logUserId}}】导入文件", type = "项目评价详情", subType = "导入文件", bizNo = "")
    public ResponseDTO<List<String>> importFiles(@PathVariable("id") String id, @RequestBody List<FileDTO> files) throws Exception {
        List<String> rsp = evaluationDetailService.importFiles(id, files);
        return new ResponseDTO<>(rsp);
    }

}
