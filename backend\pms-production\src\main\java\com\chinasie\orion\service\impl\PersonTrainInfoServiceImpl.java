package com.chinasie.orion.service.impl;

import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.domain.entity.TrainManage;
import com.chinasie.orion.domain.entity.TrainPerson;
import com.chinasie.orion.domain.entity.view.PersonTrainInfo;
import com.chinasie.orion.domain.vo.TrainEquivalentVO;
import com.chinasie.orion.domain.vo.train.PersonTrainVO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.file.api.service.FileApiService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.PersonTrainInfoMapper;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.PersonTrainInfoService;
import com.chinasie.orion.service.TrainEquivalentService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/09/19:01
 * @description:
 */
@Service
public class PersonTrainInfoServiceImpl extends OrionBaseServiceImpl<PersonTrainInfoMapper, PersonTrainInfo> implements PersonTrainInfoService {


    @Autowired
    private FileApiService fileApiService;

    @Autowired
    private TrainEquivalentService trainEquivalentService;

    @Override
    public List<PersonTrainInfo> listByUserCodeList(List<String> userCodeList) {
        LambdaQueryWrapperX<PersonTrainInfo> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(PersonTrainInfo.class);
        lambdaQueryWrapperX.in(PersonTrainInfo::getUserCode,userCodeList);
        return this.list(lambdaQueryWrapperX);
    }

    @Override
    public Page<PersonTrainVO> pagesList(Page<PersonTrainInfo> pageRequest) throws Exception {
        LambdaQueryWrapperX<PersonTrainInfo> condition = new LambdaQueryWrapperX<>(PersonTrainInfo.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(PersonTrainInfo::getCreateTime);


        Page<PersonTrainInfo> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), PersonTrainInfo::new));

        PageResult<PersonTrainInfo> page = this.getBaseMapper().selectPage(realPageRequest, condition);
        Page<PersonTrainVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());

        List<PersonTrainInfo> content = page.getContent();
        if (CollectionUtils.isEmpty(content)) {

            return pageResult;
        }
        List<String> centerIdList = new ArrayList<>();
        content.forEach(trainPerson -> {
            String trainCenterId = trainPerson.getTrainCenterId();
            if (StringUtils.hasText(trainCenterId)) {
                centerIdList.add(trainCenterId);
            }
        });
        Map<String, List<FileVO>> dataIdToList = new HashMap<>();
        if (!CollectionUtils.isEmpty(centerIdList)) {
            List<FileVO> fileVOList = fileApiService.listMaxFileByDataIds(centerIdList);
            if (!CollectionUtils.isEmpty(fileVOList)) {
                dataIdToList = fileVOList.stream().collect(Collectors.groupingBy(FileVO::getDataId));
            }
        }
        Map<String,List<TrainEquivalentVO>>  idToEntity =trainEquivalentService.listSimpleByIds(centerIdList);
        List<PersonTrainVO> personTrainVOList = new ArrayList<>();
        setData(personTrainVOList,content);
        Map<String, List<FileVO>> finalDataIdToList = dataIdToList;
        personTrainVOList.forEach(item->{
            item.setFileVOList(finalDataIdToList.getOrDefault(item.getTrainCenterId(),new ArrayList<>()));
            item.setEquivalentVOList(idToEntity.getOrDefault(item.getTrainCenterId(),new ArrayList<>()));
        });
        pageResult.setContent(personTrainVOList);
        return pageResult;
    }

    @Override
    public List<PersonTrainVO> personTrainList(String userCode) throws Exception {
        if(StrUtil.isEmpty(userCode)){
            userCode = CurrentUserHelper.getCurrentUserId();
        }
        LambdaQueryWrapperX<PersonTrainInfo> condition = new LambdaQueryWrapperX<>(PersonTrainInfo.class);
        condition.eq(PersonTrainInfo::getUserCode,userCode);
        condition.orderByDesc(PersonTrainInfo::getCreateTime);

        List<PersonTrainInfo> list = this.getBaseMapper().selectList(condition);

        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<String> centerIdList = new ArrayList<>();
        list.forEach(trainPerson -> {
            String trainCenterId = trainPerson.getTrainCenterId();
            if (StringUtils.hasText(trainCenterId)) {
                centerIdList.add(trainCenterId);
            }
        });
        Map<String, List<FileVO>> dataIdToList = new HashMap<>();
        if (!CollectionUtils.isEmpty(centerIdList)) {
            List<FileVO> fileVOList = fileApiService.listMaxFileByDataIds(centerIdList);
            if (!CollectionUtils.isEmpty(fileVOList)) {
                dataIdToList = fileVOList.stream().collect(Collectors.groupingBy(FileVO::getDataId));
            }
        }
        Map<String,List<TrainEquivalentVO>>  idToEntity =trainEquivalentService.listSimpleByIds(centerIdList);
        List<PersonTrainVO> personTrainVOList = new ArrayList<>();
        setData(personTrainVOList,list);
        Map<String, List<FileVO>> finalDataIdToList = dataIdToList;
        personTrainVOList.forEach(item->{
            item.setFileVOList(finalDataIdToList.getOrDefault(item.getTrainCenterId(),new ArrayList<>()));
            item.setEquivalentVOList(idToEntity.getOrDefault(item.getTrainCenterId(),new ArrayList<>()));
        });
        return personTrainVOList;
    }

    public void setData(List<PersonTrainVO> personTrainVOList,List<PersonTrainInfo> content){
        for (PersonTrainInfo trainPerson : content) {
            PersonTrainVO personTrainVO = new PersonTrainVO();
            personTrainVO.setTrainNumber(trainPerson.getTrainNumber());
            personTrainVO.setTrainName(trainPerson.getTrainName());
            personTrainVO.setTrainCenterId(trainPerson.getTrainCenterId());
            personTrainVO.setExpireTime(trainPerson.getEndDate());
            personTrainVO.setEndDate(trainPerson.getEndDate());
            personTrainVO.setLessonHour(trainPerson.getLessonHour());
            personTrainVO.setIsEquivalent(trainPerson.getIsEquivalent());
            personTrainVO.setTrainLecturer(trainPerson.getTrainLecturer());
            personTrainVO.setContent(trainPerson.getContent());
            personTrainVO.setBaseCode(trainPerson.getBaseCode());
            personTrainVO.setBaseName(trainPerson.getBaseName());
            personTrainVOList.add(personTrainVO);
        }
    }
}
