package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/3/3 16:02
 * @description:
 */
@Data
@ApiModel(value = "WarningSettingVO对象", description = "预警设置")
public class WarningSettingVO extends ObjectVO {

    @ApiModelProperty(value = "频率")
    private String frequency;
    private String frequencyName;

    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 提醒种类
     */
    @ApiModelProperty(value = "提醒种类(day:提醒天数;percentage:提醒工期百分比)")
    private String warningCategory;

    @ApiModelProperty(value = "提醒时间")
    private String time;

    @ApiModelProperty(value = "是否启用")
    private Integer takeEffect;
    private String takeEffectName;

    @ApiModelProperty(value = "提醒天数")
    private Integer dayNum;

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "预警方式")
    private String warningWay;
    private List<String> warningWayList;
    private String warningWayName;

    @ApiModelProperty(value = "预警类型")
    private String warningType;

    @ApiModelProperty(value = "提醒人")
    private String roleName;
    private List<String> roleList;


}
