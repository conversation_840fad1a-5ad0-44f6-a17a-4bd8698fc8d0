package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.ProjectSchemeMilestoneNodePrePostDTO;
import com.chinasie.orion.domain.dto.TemplatePreSchemeDTO;
import com.chinasie.orion.domain.entity.ProjectSchemeMilestoneNodePrePost;
import com.chinasie.orion.mybatis.service.OrionBaseService;

import java.util.List;

/**
 * <p>
 * ProjectSchemeMilestoneNodePrePost 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10 14:29:13
 */
public interface ProjectSchemeMilestoneNodePrePostService extends OrionBaseService<ProjectSchemeMilestoneNodePrePost> {

    /**
     * 删除(批量)
     *
     * @param ids
     * @return
     */
    boolean deleteByIds(List<String> ids) throws Exception;

    /**
     * 添加前后置关系(批量)
     *
     * @param preSchemeDTO
     * @return
     */
    List<String> createBatch(TemplatePreSchemeDTO preSchemeDTO) throws Exception;


    /**
     * 变更前置关系，删除后新增
     *
     * @param prePostDTOS 前置关系列表
     * @return
     */
    List<String> modify(String id, List<ProjectSchemeMilestoneNodePrePostDTO> prePostDTOS) throws Exception;
}
