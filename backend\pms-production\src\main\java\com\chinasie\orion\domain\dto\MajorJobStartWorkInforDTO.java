package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.util.Date;
import java.lang.Boolean;
import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * MajorJobStartWorkInfor DTO对象
 *
 * <AUTHOR>
 * @since 2024-11-19 09:46:56
 */
@ApiModel(value = "MajorJobStartWorkInforDTO对象", description = "大修工单开工信息")
@Data
@ExcelIgnoreUnannotated
public class MajorJobStartWorkInforDTO extends  ObjectDTO   implements Serializable{

    /**
     * 工单编号
     */
    @ApiModelProperty(value = "工单编号")
    @ExcelProperty(value = "工单编号 ", index = 0)
    private String jobNumber;

    /**
     * 开工日期
     */
    @ApiModelProperty(value = "开工日期")
    @ExcelProperty(value = "开工日期 ", index = 1)
    private Date startWorkDate;

    /**
     * 上午开工状态
     */
    @ApiModelProperty(value = "上午开工状态")
    @ExcelProperty(value = "上午开工状态 ", index = 2)
    private Boolean morningStatus;

    /**
     * 下午开工状态
     */
    @ApiModelProperty(value = "下午开工状态")
    @ExcelProperty(value = "下午开工状态 ", index = 3)
    private Boolean afternoonStatus;

    /**
     * 夜间状态
     */
    @ApiModelProperty(value = "夜间状态")
    @ExcelProperty(value = "夜间状态 ", index = 4)
    private Boolean nightStatus;




}
