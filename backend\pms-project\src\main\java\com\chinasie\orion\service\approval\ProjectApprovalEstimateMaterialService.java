package com.chinasie.orion.service.approval;

import java.lang.String;
import java.util.List;
import com.chinasie.orion.domain.dto.approval.ChangeMaterialDTO;
import com.chinasie.orion.domain.dto.approval.ProjectApprovalEstimateMaterialCreateDTO;
import com.chinasie.orion.domain.dto.approval.ProjectApprovalEstimateMaterialDTO;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalEstimateMaterial;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalEstimateMaterialVO;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalEstimateVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;

/**
 * <p>
 * ProjectApprovalEstimateMaterial 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-07 10:45:22
 */
public interface ProjectApprovalEstimateMaterialService  extends OrionBaseService<ProjectApprovalEstimateMaterial>{
    /**
     *  详情
     *
     * * @param id
     */
    ProjectApprovalEstimateMaterialVO detail(String id, String pageCode)throws Exception;

    /**
     * 批量添加物料
     * @param projectApprovalEstimateMaterialCreateDTO
     * @return
     * @throws Exception
     */
    Boolean createBatch(ProjectApprovalEstimateMaterialCreateDTO projectApprovalEstimateMaterialCreateDTO) throws Exception;

    /**
     *
     * 批量编辑
     * @param projectApprovalEstimateMaterialDTOList
     * @return
     * @throws Exception
     */
    Boolean editAmountBatch(List<ProjectApprovalEstimateMaterialDTO> projectApprovalEstimateMaterialDTOList) throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;

    /**
     * 获取物料列表
     * @param projectApprovalId
     * @return
     * @throws Exception
     */
    ProjectApprovalEstimateVO getMaterialList(String projectApprovalId) throws Exception;


    /**
     * 物料改配
     *
     * @param changeMaterialDTO
     * @return
     * @throws Exception
     */
    ProjectApprovalEstimateMaterialVO changeMaterial(ChangeMaterialDTO changeMaterialDTO) throws Exception;

    ProjectApprovalEstimateVO getMaterialListByApprovalId(String projectApprovalId) throws Exception;

    /**
     * 同步价格
     * @param projectApprovalId
     * @return
     * @throws Exception
     */
    Boolean syncMaterialPrice(String projectApprovalId) throws Exception;
}
