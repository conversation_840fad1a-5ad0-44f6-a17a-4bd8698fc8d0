<script setup lang="ts">
import {
  computed,
  onMounted, ref,
} from 'vue';
import {
  BasicForm,
  useForm,
} from 'lyra-component-vue3';
import Api from '/@/api';
const props = withDefaults(defineProps<{
  projectId:string,
  qualityItemIds:Array<string>,
  productNumberArr:Array<string>,
}>(), {
  projectId: '',
  qualityItemIds: () => [],
  productNumberArr: () => [],
});
const productList = ref([]);

onMounted(async () => {
  await getProductList();
  if (props.productNumberArr.length > 0) {
    setFieldsValue({
      product: props.productNumberArr,
    });
  }
});
async function getProductList() {
  const res = await new Api('/pms').fetch('', `qualityItem/getProductList?projectId=${props.projectId}`, 'POST');
  if (res) {
    productList.value = res;
  }
}
const [formRegister, { validate, getFieldsValue, setFieldsValue }] = useForm({
  actionColOptions: {
    span: 24,
  },
  showActionButtonGroup: false,
  schemas: [
    {
      field: 'product',
      component: 'Select',
      label: '产品:',
      colProps: {
        span: 24,
      },
      rules: [
        {
          required: true,
          type: 'array',
        },
      ],
      required: true,
      componentProps: {
        showSearch: true,
        mode: 'multiple',
        options: computed(() => productList.value),
        fieldNames: {
          key: 'id',
          value: 'number',
          label: 'name',
        },
      },
    },
  ],
});
// 检查是否选中数据

defineExpose({
  async submit() {
    const formValues = await validate();
    if (formValues) {
      const filteredData = productList.value.filter((item) => formValues.product.includes(item.number));
      // 使用 filter 方法根据 ids 筛选出想要的数组
      const newFilteredData = filteredData.map((item) => ({
        productId: item.id,
        productName: item.name,
        productNumber: item.number,
      }));
      const params = {
        productList: newFilteredData,
        projectId: props.projectId,
        qualityItemIds: props.qualityItemIds,
      };
      return new Promise((resolve, reject) => {
        new Api('/pms/qualityItem/related/product').fetch(params, '', 'POST').then(() => {
          resolve('');
        }).catch((e) => {
          reject(e);
        });
      });
    }
  },
});
</script>

<template>
  <div class="form-wrap ">
    <BasicForm
      @register="formRegister"
    />
  </div>
</template>

<style scoped lang="less">

</style>
