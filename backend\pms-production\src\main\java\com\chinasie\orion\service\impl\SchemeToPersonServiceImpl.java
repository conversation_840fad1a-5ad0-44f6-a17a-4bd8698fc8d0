package com.chinasie.orion.service.impl;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.base.api.domain.entity.UserDO;
import com.chinasie.orion.base.api.repository.UserDOMapper;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.domain.dto.CountJobDTO;
import com.chinasie.orion.domain.dto.SchemeToPersonDTO;
import com.chinasie.orion.domain.dto.excel.PersonMangeEntryExcelTpl;
import com.chinasie.orion.domain.dto.excel.PersonPlanVO;
import com.chinasie.orion.domain.dto.person.AddParamDTO;
import com.chinasie.orion.domain.dto.scheme.RemoveRelationDTO;
import com.chinasie.orion.domain.dto.scheme.SchemeAuthorizeUserParamDTO;
import com.chinasie.orion.domain.dto.scheme.SchemePersonParamDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.PersonMangeVO;
import com.chinasie.orion.domain.vo.ResultVO;
import com.chinasie.orion.domain.vo.SchemeToPersonVO;
import com.chinasie.orion.domain.vo.excel.SchemeToPersonExportExcel;
import com.chinasie.orion.enums.StatusEnum;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.export.ImportExcelErrorNoteVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.BaseErrorCode;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.handler.status.write.ExcelCellSelectWriterHandler;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.JobPostAuthorizeMapper;
import com.chinasie.orion.repository.SchemeToPersonMapper;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.*;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.BasicUserService;
import com.chinasie.orion.service.MajorRepairPlanService;
import com.chinasie.orion.service.PersonMangeService;
import com.chinasie.orion.service.SchemeToPersonService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.chinasie.orion.constant.DictConts.PMS_OUT_FACTORY_REASON;


/**
 * <p>
 * SchemeToPerson 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-15 20:21:36
 */
@Service
@Slf4j
public class SchemeToPersonServiceImpl extends  OrionBaseServiceImpl<SchemeToPersonMapper, SchemeToPerson>   implements SchemeToPersonService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private ClassRedisHelper classRedisHelper;

    private PersonMangeService personMangeService;

    private JobPostAuthorizeMapper jobPostAuthorizeMapper;

    private UserRedisHelper userRedisHelper;

    private BasicUserService basicuserService;

    @Autowired
    private MajorRepairPlanService majorRepairPlanService;

    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Autowired
    private SchemeToPersonMapper schemeToPersonMapper;

    @Autowired
    private DictRedisHelper dictRedisHelper;

    @Autowired
    @Lazy
    private UserDOMapper userDOMapper;

    @Autowired
    public void setBasicuserService(BasicUserService basicuserService) {
        this.basicuserService = basicuserService;
    }


    @Autowired
    public void setUserRedisHelper(UserRedisHelper userRedisHelper) {
        this.userRedisHelper = userRedisHelper;
    }

    @Autowired
    public void setJobPostAuthorizeMapper(JobPostAuthorizeMapper jobPostAuthorizeMapper) {
        this.jobPostAuthorizeMapper = jobPostAuthorizeMapper;
    }



    @Autowired
    public void setPersonMangeService(PersonMangeService personMangeService){
        this.personMangeService = personMangeService;
    }

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  SchemeToPersonVO detail(String id,String pageCode) throws Exception {
        SchemeToPerson schemeToPerson =this.getById(id);
        if(Objects.isNull(schemeToPerson)){
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_QUERY_ENTITY_NULL);
        }
        SchemeToPersonVO result = BeanCopyUtils.convertTo(schemeToPerson,SchemeToPersonVO::new);
        setEveryName(Collections.singletonList(result));
        return result;
    }

    /**
     *  新增
     *
     * * @param schemeToPersonDTO
     */
    @Override
    public  String create(SchemeToPersonDTO schemeToPersonDTO) throws Exception {
        SchemeToPerson schemeToPerson =BeanCopyUtils.convertTo(schemeToPersonDTO,SchemeToPerson::new);
        this.save(schemeToPerson);
        String rsp=schemeToPerson.getId();
        return rsp;
    }

    /**
     *  编辑
     *
     * * @param schemeToPersonDTO
     */
    @Override
    public Boolean edit(SchemeToPersonDTO schemeToPersonDTO) throws Exception {
        SchemeToPerson schemeToPerson =BeanCopyUtils.convertTo(schemeToPersonDTO,SchemeToPerson::new);

        this.updateById(schemeToPerson);

        String rsp=schemeToPerson.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<SchemeToPersonVO> pages( Page<SchemeToPersonDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<SchemeToPerson> condition = new LambdaQueryWrapperX<>( SchemeToPerson. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(SchemeToPerson::getCreateTime);
        Page<SchemeToPerson> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), SchemeToPerson::new));
        SchemeToPersonDTO query = pageRequest.getQuery();
        if (!Objects.isNull(query)){
            if (!Objects.isNull(query.getPlanSchemeId())){
                condition.eq(SchemeToMaterial::getPlanSchemeId, query.getPlanSchemeId());
            }
        }
        PageResult<SchemeToPerson> page = this.getBaseMapper().selectPage(realPageRequest, condition);
        Page<SchemeToPersonVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<SchemeToPersonVO> vos = BeanCopyUtils.convertListTo(page.getContent(), SchemeToPersonVO::new);
        if(CollectionUtils.isEmpty(vos)){
            return pageResult;
        }

        setEveryName(vos);

        pageResult.setContent(vos);
        return pageResult;
    }

    @Override
    public void  setEveryName(List<SchemeToPersonVO> vos)throws Exception {
        List<String> userIds = vos.stream().map(SchemeToPersonVO::getUserId).filter(StringUtils::hasText).collect(Collectors.toList());
        List<SimpleUser> simplerUsers = userRedisHelper.getSimplerUsers(CurrentUserHelper.getOrgId(), userIds);
        Map<String,SimpleUser> idToEntity = new HashMap<>();
        simplerUsers.forEach(item->{
            idToEntity.put(item.getId(),item);
        });
        vos.forEach(item->{
            SimpleUser simpleUser =  idToEntity.get(item.getUserId());
            if (Objects.nonNull(simpleUser)){
                item.setDeptCode(simpleUser.getDeptCode());
                item.setDeptName(simpleUser.getDeptName());
                item.setUserName(simpleUser.getName());
            }
        });
    }

    @Override
    public Page<SchemeToPersonVO> personPages(Page<SchemeToPersonDTO> pageRequest) {
        LambdaQueryWrapperX<SchemeToPerson> condition = new LambdaQueryWrapperX<>( SchemeToPerson. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
//        condition.orderByDesc(SchemeToPerson::getCreateTime);
//        condition.distinct();
        condition.select(SchemeToPerson::getUserId,SchemeToPerson::getUserCode,SchemeToPerson::getUserName,SchemeToPerson::getPersonId,SchemeToPerson::getIsHaveProject);
        SchemeToPersonDTO schemeToPersonDTO= pageRequest.getQuery();

        Integer total= schemeToPersonMapper.countPage(schemeToPersonDTO.getRepairRound(),schemeToPersonDTO.getKeyword());
        Page<SchemeToPersonVO> pageResult = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), pageRequest.getTotalSize());
        pageResult.setTotalSize(total);
        if(Objects.isNull(total) || total<=0){
            return  pageResult;
        }
        long current=(pageRequest.getPageNum()-1)*pageRequest.getPageSize();
        List<SchemeToPerson> list=   schemeToPersonMapper.pageList(schemeToPersonDTO.getRepairRound(),schemeToPersonDTO.getKeyword()
                ,current,pageRequest.getPageSize());

        List<String> personIdList = list.stream().map(SchemeToPerson::getPersonId).filter(StringUtils::hasText).collect(Collectors.toList());

        List<SchemeToPersonVO>  schemeToPersonVOS = schemeToPersonMapper.getListByPersonIdList(schemeToPersonDTO.getRepairRound(),personIdList);

        Map<String,List<SchemeToPersonVO>> personIdToList =  schemeToPersonVOS.stream().collect(Collectors.groupingBy(SchemeToPersonVO::getPersonId));
        List<SchemeToPersonVO> vos = BeanCopyUtils.convertListTo(list, SchemeToPersonVO::new);


        List<PersonMangeVO> personMangeVOList= personMangeService.listByPersonIdList(personIdList);
        Map<String, PersonMangeVO> personToEntity = new HashMap<>();
        if (!CollectionUtils.isEmpty(personMangeVOList)) {
            for (PersonMangeVO personMange : personMangeVOList) {
                personToEntity.put(personMange.getId(), personMange);
            }
        }
        List<CountJobDTO> jobPersonNumList  = jobPostAuthorizeMapper.getJobPersonNumMap(personIdList);
        Map<String, Integer> jobPersonNumMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(jobPersonNumList)){
            for (CountJobDTO countJobDTO : jobPersonNumList) {
                jobPersonNumMap.put(countJobDTO.getKey(),countJobDTO.getValue());
            }
        }
         List<String> userCodeList = vos.stream().map(SchemeToPersonVO::getUserCode).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, BasicUser> userMap = basicuserService.getMapByNumberList(userCodeList);

        if(null == userMap){
            userMap = new HashMap<>();
        }
        Map<String, BasicUser> finalUserMap = userMap;
        vos.forEach(item->{
            PersonMangeVO personMangeVO=  personToEntity.get(item.getPersonId());
            if(Objects.nonNull(personMangeVO)){
                item.setPersonMangeVO(personMangeVO);
                item.setSex(personMangeVO.getSex());
                item.setNowPosition(personMangeVO.getNowPosition());
                BasicUser basicUser = finalUserMap.get(item.getUserCode());
                if(Objects.nonNull(basicUser)){
                    item.setSex(basicUser.getSex());
                    item.setPersonnelNature(basicUser.getPersonnelNature());
                    item.setInstituteName(basicUser.getInstituteName());
                    item.setNation(basicUser.getNation());
                    item.setDeptCode(basicUser.getDeptCode());
                    item.setDeptName(basicUser.getDeptName());
                }
                item.setIsBasePermanent(personMangeVO.getIsBasePermanent());
            }
            List<SchemeToPersonVO> scheme = personIdToList.get(item.getPersonId());
            if(!CollectionUtils.isEmpty(scheme)){
                item.setProjectName(scheme.stream().map(SchemeToPersonVO::getProjectName).filter(Objects::nonNull).collect(Collectors.joining(",")));
                item.setPlanSchemeName(scheme.stream().map(SchemeToPersonVO::getPlanSchemeName).filter(Objects::nonNull).collect(Collectors.joining(",")));
            }
            item.setJobNum(jobPersonNumMap.getOrDefault(item.getPersonId(),0));
        });
        pageResult.setContent(vos);
        return pageResult;
    }

    @Override
    public ResultVO addPersonList(SchemeAuthorizeUserParamDTO schemeAuthorizeUserParamDTO) throws Exception {
        if (!StringUtils.hasText(schemeAuthorizeUserParamDTO.getBaseCode())){
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL.getErrorCode(), "所属基地编码不能为空");
        }
        if (!StringUtils.hasText(schemeAuthorizeUserParamDTO.getRepairRound())){
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL.getErrorCode(), "所属计划大修轮次不能为空");
        }
        List<String> personIdList = schemeAuthorizeUserParamDTO.getPersonIdList();
        String reparirRound = schemeAuthorizeUserParamDTO.getRepairRound();
        // todo 可能会需要考虑加 锁
        LambdaQueryWrapperX<SchemeToPerson> wrapperX = new LambdaQueryWrapperX<>(SchemeToPerson.class);
        wrapperX.select(SchemeToPerson::getId,SchemeToPerson::getUserId,SchemeToPerson::getPlanSchemeId
                ,SchemeToPerson::getUserCode,SchemeToPerson::getUserName,SchemeToPerson::getIsHaveProject);
        wrapperX.eq(SchemeToPerson::getRepairRound, reparirRound);
        wrapperX.in(SchemeToPerson::getUserId, personIdList);
        List<SchemeToPerson> schemeToPersonList =  this.list(wrapperX);

        ResultVO resultVO = new ResultVO();
        resultVO.setCode(200);
        if(!CollectionUtils.isEmpty(schemeToPersonList)){
            // 如果不为空 那么需要考虑 是 无项目
            if(!schemeAuthorizeUserParamDTO.getIsHaveProject()){
                //  关系数据中人员可能存在重复
                // 需要判断 当前人员在
                Map<String,SchemeToPerson> schemeToPerson = new HashMap<>();
                for (SchemeToPerson scheme : schemeToPersonList) {
                    schemeToPerson.put(scheme.getUserId(),scheme);
                }

                List<String> existUserStr = new ArrayList<>();
                List<String> addUserStr = new ArrayList<>();
                for (Map.Entry<String, SchemeToPerson> stringSchemeToPersonEntry : schemeToPerson.entrySet()) {
                    if(personIdList.contains(stringSchemeToPersonEntry.getKey())){
                        SchemeToPerson scheme= stringSchemeToPersonEntry.getValue();
                        existUserStr.add(String.format("用户编号：%s ，用户名称：%s",scheme.getUserCode(),scheme.getUserName()));
                    }
                }
                for (String s : personIdList) {
                    if(!schemeToPerson.containsKey(s)){
                        addUserStr.add(s);
                    }
                }

                if(!CollectionUtils.isEmpty(addUserStr)){
                    this.savePersonList(addUserStr,reparirRound,schemeAuthorizeUserParamDTO.getPlanSchemeId()
                            ,schemeAuthorizeUserParamDTO.getBaseCode(),schemeAuthorizeUserParamDTO.getIsHaveProject());
                }
                if(!CollectionUtils.isEmpty(existUserStr)){
                    StringBuffer stringBuffer = new StringBuffer("有项目人员存在不更新【");
                    stringBuffer.append(existUserStr.stream().collect(Collectors.joining(";")));
                    stringBuffer.append(" 】");
                    resultVO.setMsg(stringBuffer.toString());
                    resultVO.setCode(201);
                    return resultVO;
                }
            }else {
                String schemeId= schemeAuthorizeUserParamDTO.getPlanSchemeId();
                if(StrUtil.isEmpty(schemeId)){
                    throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL.getErrorCode(), "计划ID不能为空");
                }
                List<SchemeToPerson>  filterList =schemeToPersonList.stream()
                        .filter(item-> (StringUtils.hasText(item.getPlanSchemeId()) && Objects.equals(item.getPlanSchemeId(),schemeAuthorizeUserParamDTO.getPlanSchemeId()))
                         || StrUtil.isEmpty(item.getPlanSchemeId()))
                        .collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(filterList)){
                    Map<String,SchemeToPerson> schemeToPerson = new HashMap<>();
                    for (SchemeToPerson scheme : filterList) {
                        schemeToPerson.put(scheme.getUserId(),scheme);
                    }
                    // 有项目的人员
                    List<String> haventUserStr = new ArrayList<>();
                    // 无有项目的人员
                    List<String> notHaventUserStr = new ArrayList<>();
                    List<SchemeToPerson> updateList = new ArrayList<>();
                    for (Map.Entry<String, SchemeToPerson> stringSchemeToPersonEntry : schemeToPerson.entrySet()) {
                        if(personIdList.contains(stringSchemeToPersonEntry.getKey())){
                            SchemeToPerson scheme= stringSchemeToPersonEntry.getValue();
                            String msc=String.format("用户编号：%s ，用户名称：%s",scheme.getUserCode(),scheme.getUserName());
                            if(scheme.getIsHaveProject()){
                                haventUserStr.add(msc);
                            }else{
                                notHaventUserStr.add(msc);
                                scheme.setIsHaveProject(Boolean.TRUE);
                                scheme.setPlanSchemeId(schemeId);
                                updateList.add(scheme);
                            }
                        }
                    }
                    List<String> addUserStr = new ArrayList<>();
                    for (String s : personIdList) {
                        if(!schemeToPerson.containsKey(s)){
                            addUserStr.add(s);
                        }
                    }
                    if(!CollectionUtils.isEmpty(addUserStr)){
                        this.savePersonList(addUserStr,reparirRound,schemeAuthorizeUserParamDTO.getPlanSchemeId()
                                ,schemeAuthorizeUserParamDTO.getBaseCode(),schemeAuthorizeUserParamDTO.getIsHaveProject());
                    }
                    if(!CollectionUtils.isEmpty(updateList)){
                        this.updateBatchById(updateList);
                    }
                    if(!CollectionUtils.isEmpty(notHaventUserStr)||!CollectionUtils.isEmpty(haventUserStr)) {
                        StringBuffer stringBuffer = new StringBuffer("");
                        if (!CollectionUtils.isEmpty(notHaventUserStr)) {
                            stringBuffer.append("无项目人员存在更新为有项目人员: 【");
                            stringBuffer.append(notHaventUserStr.stream().collect(Collectors.joining(";")));
                            stringBuffer.append(" 】");
                        }
                        if (!CollectionUtils.isEmpty(haventUserStr)) {
                            if (!CollectionUtils.isEmpty(haventUserStr)) {
                                stringBuffer.append("有项目人员存在不更新: 【");
                                stringBuffer.append(haventUserStr.stream().collect(Collectors.joining(";")));
                                stringBuffer.append(" 】");
                            }
                            resultVO.setMsg(stringBuffer.toString());
                            resultVO.setCode(201);
                        }
                    }
                    return resultVO;
                }
            }
        }
        this.savePersonList(personIdList,reparirRound,schemeAuthorizeUserParamDTO.getPlanSchemeId(),schemeAuthorizeUserParamDTO.getBaseCode(),schemeAuthorizeUserParamDTO.getIsHaveProject());
        return resultVO;
    }

    public void savePersonList(List<String> personIdList,String repairRound,String schemeId,String basCode,Boolean isHaveProject) throws Exception {
        List<UserVO> userList = userRedisHelper.getUserByIds(personIdList);
        if (CollectionUtils.isEmpty(userList)) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "用户数据不存在，请同步用户数据");
        }
        List<String> userCodeList = new ArrayList<>();
        for (UserVO simpleUser : userList) {
            if (StringUtils.hasText(simpleUser.getCode())) {
                userCodeList.add(simpleUser.getCode());
            }
        }

        List<SchemeToPerson> schemeToPeopleList = new ArrayList<>();
        //封装数据
        this.packageList(schemeToPeopleList,userList,repairRound,schemeId,basCode);
        schemeToPeopleList.forEach(item-> item.setIsHaveProject(isHaveProject));
        // 批量保存
        this.saveBatch(schemeToPeopleList);
        //更新人员管理以及添加人员管理id到关系表中
        personManageDataSave(basCode,userCodeList, schemeToPeopleList);
        this.updateBatchById(schemeToPeopleList);

    }

    @Override
    public Boolean removeBatchNew(SchemePersonParamDTO personParamDTO) {
        List<String> personIdList = personParamDTO.getPersonIdList();
        List<PersonMangeVO> personMangeVOS = personMangeService.listByPersonIdList(personIdList);
        LambdaQueryWrapperX<SchemeToPerson> wrapperX = new LambdaQueryWrapperX<>(SchemeToPerson.class);
        wrapperX.in(SchemeToPerson::getPersonId, personIdList);
        wrapperX.notIn(SchemeToPerson::getId, personParamDTO.getIds());
        wrapperX.select(SchemeToPerson::getPersonId);
        List<SchemeToPerson> schemeToPersonList = this.list(wrapperX);
        if (CollectionUtils.isEmpty(schemeToPersonList)) {
            this.removeBatchByIds(personParamDTO.getIds());
            this.removeOthers(personMangeVOS,personIdList);
            return Boolean.TRUE;
        }

        personIdList.removeAll(schemeToPersonList.stream().map(SchemeToPerson::getPersonId).collect(Collectors.toList()));
        if (personIdList.size() > 0) {
            this.removeOthers(personMangeVOS,personIdList);
        }
        this.removeBatchByIds(personParamDTO.getIds());
        return Boolean.TRUE;
    }

    @Override
    public Boolean removeBatchNewV2(RemoveRelationDTO removeRelationDTO) {
        String repairRount= removeRelationDTO.getRepairRound();
        List<String> userCodeList = removeRelationDTO.getUserCodeList();
        LambdaQueryWrapperX<SchemeToPerson> wrapperX = new LambdaQueryWrapperX<>(SchemeToPerson.class);
        wrapperX.eq(SchemeToPerson::getRepairRound, repairRount);
        wrapperX.in(SchemeToPerson::getUserCode, userCodeList);
        wrapperX.select(SchemeToPerson::getId ,SchemeToPerson::getPersonId,SchemeToPerson::getIsHaveProject);
        List<SchemeToPerson>  schemeToPersonList= this.list(wrapperX);
        if(CollectionUtils.isEmpty(schemeToPersonList)){
            return Boolean.TRUE;
        }
        long count= schemeToPersonList.stream().filter(item-> item.getIsHaveProject()).count();
        if(count >0){
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_POWER_LOGIC_DELETE.getErrorCode(),"所选中人员数据包含有项目的数据，请刷新后操作");
        }
        List<String> personIdList= schemeToPersonList.stream().map(SchemeToPerson::getPersonId).distinct().collect(Collectors.toList());
        List<String> idList= schemeToPersonList.stream().map(SchemeToPerson::getId).distinct().collect(Collectors.toList());
        this.removeBatchByIds(idList);
        personMangeService.removeBatchByIds(personIdList);
        return Boolean.TRUE;
    }

    /**
     * 导出人员入场离场信息
     *
     * @param query    查询
     * @param response 导出到浏览器
     */
    @Override
    public void exportSchemePersonExcel(SchemeToPersonDTO query, HttpServletResponse response) {
        LambdaQueryWrapperX<SchemeToPerson> condition = new LambdaQueryWrapperX<>(SchemeToPerson.class);
        condition.select(SchemeToPerson::getUserId, SchemeToPerson::getPersonId, SchemeToPerson::getIsHaveProject
                , SchemeToPerson::getUserCode, SchemeToPerson::getUserName);

        condition.leftJoin(PersonMange.class, "pm", on -> on.eq(SchemeToPerson::getPersonId, PersonMange::getId))
                .select(PersonMange::getIsBasePermanent, PersonMange::getInDate, PersonMange::getOutDate
                        , PersonMange::getActInDate, PersonMange::getActOutDate, PersonMange::getNewcomer,
                        PersonMange::getDesignCtrlZoneOp, PersonMange::getHeightStr, PersonMange::getWeightStr
                        , PersonMange::getChemicalToxinUseJob, PersonMange::getIsJoinYearMajorRepair,
                        PersonMange::getIsHeightMeasurePerson, PersonMange::getLeaveReason,
                        PersonMange::getIsFinishOutHandover, PersonMange::getIsAgainIn, PersonMange::getContactUserName,
                        PersonMange::getStatus);

        condition.leftJoin(BasicUser.class, "bu", on -> on.eq(PersonMange::getNumber, BasicUser::getUserCode))
                .select(BasicUser::getSex, BasicUser::getPersonnelNature, BasicUser::getCompanyName,
                        BasicUser::getInstituteName, BasicUser::getNowPosition,
                        BasicUser::getPoliticalAffiliation);

        condition.select("(select count(1) from pmsx_job_post_authorize pa where pa.person_manage_id = t.person_id ) as job_num");

        condition.eq(JobManage::getRepairRound, query.getRepairRound());
        List<SchemeToPersonExportExcel> excelList = this.selectJoinList(SchemeToPersonExportExcel.class, condition);

        // 去重逻辑，和page保持统一
        List<SchemeToPersonExportExcel> list = new ArrayList<>(excelList.stream()
                .collect(Collectors.toMap(
                        u -> new CompositeKey(u.getUserId(), u.getUserCode(), u.getUserName(), u.getPersonId(), u.getIsHaveProject()),
                        u -> u,
                        (u1, u2) -> u1
                )).values());

        final List<DictValueVO> reasonDicts = dictRedisHelper.getDictListByCode(PMS_OUT_FACTORY_REASON);
        Map<String, String> reasonMap = reasonDicts.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (k1, k2) -> k1));

        Map<String, BasicUser> userMap = basicuserService.getMapByNumberList(list.stream()
                .map(SchemeToPersonExportExcel::getUserCode).filter(Objects::nonNull).collect(Collectors.toList()));

        list.forEach(e -> {
            e.setOutDays(0);
            Calendar startCalendar;
            Calendar endCalendar;
            if (!Objects.isNull(e.getInDate())) {
                startCalendar = Calendar.getInstance();
                endCalendar = Calendar.getInstance();
                startCalendar.setTime(DateUtil.beginOfDay(e.getInDate()));
                endCalendar.setTime(DateUtil.beginOfDay(new Date()));
                e.setInDays(((startCalendar.getTimeInMillis() - endCalendar.getTimeInMillis()) / (24 * 60 * 60 * 1000)));
            }
            if (!Objects.isNull(e.getOutDate())) {
                startCalendar = Calendar.getInstance();
                endCalendar = Calendar.getInstance();
                startCalendar.setTime(DateUtil.beginOfDay(e.getOutDate()));
                endCalendar.setTime(DateUtil.beginOfDay(new Date()));
                e.setOutDays(((startCalendar.getTimeInMillis() - endCalendar.getTimeInMillis()) / (24 * 60 * 60 * 1000)));
            }
            e.setHadStationed(null != e.getStatus() && 1 == e.getStatus());
            e.setLeaveReasonName(reasonMap.getOrDefault(e.getLeaveReason(), ""));

            BasicUser basicUser = userMap.get(e.getUserCode());
            if (Objects.nonNull(basicUser)) {
                if (null != basicUser.getSex()) {
                    e.setSex(basicUser.getSex());
                }
                e.setPersonnelNature(basicUser.getPersonnelNature());
                e.setInstituteName(basicUser.getInstituteName());
                e.setDeptCode(basicUser.getDeptCode());
                e.setDeptName(basicUser.getDeptName());
            }else{
                e.setSex("1".equals(basicUser.getSex()) ? "男" : "0".equals(basicUser.getSex())? "女":basicUser.getSex());
            }
        });

        String fileName = "导出人员入场离场信息.xlsx";
        response.reset();
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        try {
            EasyExcel.write(response.getOutputStream(), SchemeToPersonExportExcel.class).sheet("sheet1").doWrite(list);
        } catch (Exception e) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERR.getErrorCode(), e.getMessage());
        }
    }


    @Override
    public void downloadExcelTplToPlan(String repairRound,HttpServletResponse response) throws Exception {

        InputStream inputStream = this.getClass().getResourceAsStream("计划.xlsx");
        List<PersonPlanVO> personPlanVOList= schemeToPersonMapper.listByRepairRound(repairRound);
        String fileName = "《参修人员计划入场信息表》导入.xlsx";
        response.reset();
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        try {
            EasyExcel.write(response.getOutputStream(), PersonPlanVO.class)
                    .withTemplate(inputStream)
                    .registerWriteHandler(new ExcelCellSelectWriterHandler(1,
                            CollectionUtils.isEmpty(personPlanVOList) ? 1 : personPlanVOList.size(), 4, 4, new String[]{"是", "否"}))
                    .sheet("sheet1").doWrite(personPlanVOList);
        } catch (Exception e) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERR.getErrorCode(), e.getMessage());
        }
    }

    @Override
    public ImportExcelCheckResultVO importCheckByExcelToPlan(String repairRound,MultipartFile file) throws IOException {
        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = file.getInputStream();
        PlanPersonListener excelReadListener = new PlanPersonListener();
        EasyExcel.read(inputStream, PersonPlanVO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<PersonPlanVO> importList = excelReadListener.getData();
        if (CollectionUtils.isEmpty(importList)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (importList.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        List<PersonPlanVO> personPlanVOList= schemeToPersonMapper.listByRepairRound(repairRound);

        log.info("《参修人员计划入场信息表》导入的原始数据={}", JSONUtil.toJsonStr(personPlanVOList));
        List<String> userCodeList = importList.stream().map(PersonPlanVO::getUserCode).filter(Objects::nonNull).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(userCodeList)){
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查模板");
            return result;
        }
        LambdaQueryWrapper<UserDO> queryWrapper = new LambdaQueryWrapper<>(UserDO.class);
        queryWrapper.in(UserDO::getCode, userCodeList);
        queryWrapper.select(UserDO::getName,UserDO::getCode);
        List<UserDO> userDOList =  userDOMapper.selectList(queryWrapper);
        Map<String, String> userMap = userDOList.stream().collect(Collectors.toMap(UserDO::getCode, UserDO::getName));


        Map<String,String> userCodeToPersonId = personPlanVOList.stream().collect(Collectors.toMap(PersonPlanVO::getUserCode,PersonPlanVO::getPersonId, (k1, k2) -> k1));
        // 校验数据
        String orgId= CurrentUserHelper.getOrgId();
        List<ImportExcelErrorNoteVO> importExcelErrorNoteVOS = new ArrayList<>();
        List<PersonPlanVO> voList = new ArrayList<>();
        for (int i = 0; i < importList.size(); i++) {
            String order = String.valueOf(i + 2);
            ImportExcelErrorNoteVO errorNoteVO = new ImportExcelErrorNoteVO();
            List<String> errorNotes = new ArrayList<>();
            PersonPlanVO personPlanVO = importList.get(i);
            errorNoteVO.setOrder(order);
            String userCode= personPlanVO.getUserCode();
            if(StrUtil.isEmpty(userCode)){
                errorNotes.add("用户工号不能为空");
            }
            if(!userCodeToPersonId.containsKey(userCode)){
                errorNotes.add("大修中无该人员信息");
            }else{
                personPlanVO.setPersonId(userCodeToPersonId.get(userCode));

            }
            String name= userMap.get(userCode);
            if (StrUtil.isBlank(name)){
                errorNotes.add("员工号与姓名不匹配");
            }
            // 校验 必填参数是否填写
            Date inDate = personPlanVO.getInDate();
            if(Objects.isNull(inDate)){
                errorNotes.add("计划入场时间不能为空");
            }
            Date outDate = personPlanVO.getOutDate();
            if(Objects.isNull(outDate)){
                errorNotes.add("计划离场时间不能为空");
            }
            // 请补充“是否新人”信息
            String isNewcomer = personPlanVO.getIsNewcomer();
            if(StrUtil.isEmpty(isNewcomer)){
                errorNotes.add("请补充“是否新人”信息");
            }
            // 是否新人”选“是”，未填写接口人姓名，则校验不通过（显示问题行数并提示：请补充“接口人”信息）
            if(StringUtils.hasText(isNewcomer)){
                if(Objects.equals("是",isNewcomer)){
                    String con= personPlanVO.getContactUserName();
                    if(StrUtil.isEmpty(con)){ //请补充“接口人”信息
                        errorNotes.add("请补充“接口人”信息");
                    }
                }
            }
            if(Objects.nonNull(inDate) && Objects.nonNull(outDate) ){
                if(outDate.compareTo(inDate) < 0){
                    errorNotes.add("“计划离场日期”不能早于“计划入场日期”");
                }
            }
            if(!CollectionUtils.isEmpty(errorNotes)){
                errorNoteVO.setErrorNotes(errorNotes);
                importExcelErrorNoteVOS.add(errorNoteVO);
            }else{
                voList.add(personPlanVO);
            }
        }
        result.setCode(200);
        if(!CollectionUtils.isEmpty(importExcelErrorNoteVOS)){
            result.setErr(importExcelErrorNoteVOS);
            return result;
        }
        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::plan-person-import::id", importId, voList, 24 * 60 * 60);
        result.setSucc(importId);
        return result;
    }

    @Override
    public Boolean importByExcelToPlan(String importId) {
        List<PersonPlanVO> voList = (List<PersonPlanVO>) orionJ2CacheService.get("pmsx::plan-person-import::id",importId);
        List<String> personIdList= voList.stream().map(PersonPlanVO::getPersonId).collect(Collectors.toList());
        personMangeService.updateByList(voList,personIdList);
        orionJ2CacheService.delete("ncf::BasePlace-import::id", importId);
        return  Boolean.TRUE;
    }

    @Override
    public List<SchemeToPersonVO> getAdmissionList(SchemeToPersonDTO dto) {
        if(!StringUtils.hasText(dto.getRepairRound())){
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL.getErrorCode(),"大修轮次不能为空");
        }
        return this.getBaseMapper().getAdmissionList(dto.getRepairRound(),1);
    }

    private void removeOthers(List<PersonMangeVO> personMangeVOS, List<String> personIdList){
        for (PersonMangeVO personMangeVO : personMangeVOS) {
            if (personMangeVO.getStatus() == 0 || personMangeVO.getStatus() == 2){
                continue;
            }
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_POWER_LOGIC_DELETE.getErrorCode(),"人员已入场，无法删除");
        }
        if (!personIdList.isEmpty()){
            //第三层删除
            LambdaUpdateWrapper<JobPostAuthorize> postWrapper = new LambdaUpdateWrapper<>(JobPostAuthorize.class);
            postWrapper.set(JobPostAuthorize::getLogicStatus,-1);
            postWrapper.in(JobPostAuthorize::getPersonManageId,personIdList);
            jobPostAuthorizeMapper.update(postWrapper);
            //人员管理
            this.personMangeService.removeBatchByIds(personIdList);
        }
    }

    private void existsUser(String schemeId, List<String> personIdList,String reparirRound) {
        LambdaQueryWrapperX<SchemeToPerson> wrapperX = new LambdaQueryWrapperX<>(SchemeToPerson.class);
        wrapperX.select(SchemeToPerson::getId);
        wrapperX.eq(SchemeToPerson::getRepairRound, reparirRound);
        wrapperX.in(SchemeToPerson::getPersonId, personIdList);
        long exist = this.count(wrapperX);
        if (exist > 0) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "选择的人员不允许和已有的数据重复");
        }
    }


    private void personManageDataSave(String schemeBase, List<String> userCodeList, List<SchemeToPerson> schemeToPersonList) throws Exception {
        AddParamDTO addParamDTO = new AddParamDTO();
        addParamDTO.setBaseCode(schemeBase);
        addParamDTO.setCodeList(userCodeList);
        Map<String, PersonMange> codeToIdMap = personMangeService.addBatchByCodeListNew(addParamDTO);
        schemeToPersonList.forEach(item -> {
            PersonMange personMange=  codeToIdMap.get(item.getUserCode());
            if(!Objects.isNull(personMange)){
                item.setPersonId(personMange.getId());
            }
        });
    }

    private void packageList(List<SchemeToPerson> schemeToPeopleList, List<UserVO> userList, String repairRound, String planSchemeId,String baseCode) {
//        MajorRepairPlan majorRepairPlan = majorRepairPlanService.getById(planSchemeId);
        userList.forEach(item -> {
            SchemeToPerson schemeToPeople = new SchemeToPerson();
            // 强制设置 ID
            schemeToPeople.setId(classRedisHelper.getUUID(JobPostAuthorize.class.getSimpleName()));
            schemeToPeople.setPersonId(item.getId());
            schemeToPeople.setUserCode(item.getCode());
            schemeToPeople.setRepairRound(repairRound);
            schemeToPeople.setPlanSchemeId(planSchemeId);
            schemeToPeople.setUserId(item.getId());
            schemeToPeople.setUserName(item.getName());
            schemeToPeople.setBaseCode(baseCode);
            schemeToPeopleList.add(schemeToPeople);
        });
    }
    @Override
    public void saveListEntity(List<SchemeToPerson> schemeToPersonList, String schemeId, String repairRound) {
        List<String> userIdList = schemeToPersonList.stream().map(SchemeToPerson::getUserId).distinct().collect(Collectors.toList());
        List<String> personIdList= schemeToPersonList.stream().map(SchemeToPerson::getPersonId).distinct().collect(Collectors.toList());
        LambdaQueryWrapperX<SchemeToPerson> wrapperX =new LambdaQueryWrapperX<>(SchemeToPerson.class);
//        wrapperX.and(item->{
//            item.eq(SchemeToPerson::getPlanSchemeId,schemeId).or().isNull(SchemeToPerson::getPlanSchemeId);
//        });
        wrapperX.eq(SchemeToPerson::getRepairRound,repairRound);
        wrapperX.in(SchemeToPerson::getUserId,userIdList);
        wrapperX.select(SchemeToPerson::getUserId,SchemeToPerson::getId,SchemeToPerson::getPersonId,SchemeToPerson::getIsHaveProject);
        List<SchemeToPerson> schemeList = this.list(wrapperX);
        if(!CollectionUtils.isEmpty(schemeList)){
            List<SchemeToPerson>  filterList =schemeList.stream()
                    .filter(item-> (StringUtils.hasText(item.getPlanSchemeId()) && Objects.equals(item.getPlanSchemeId(),schemeId))
                            || StrUtil.isEmpty(item.getPlanSchemeId()))
                    .collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(filterList)){
                Map<String,SchemeToPerson> schemeToPerson = new HashMap<>();
                for (SchemeToPerson scheme : filterList) {
                    schemeToPerson.put(scheme.getUserId(),scheme);
                }
                // 有项目的人员
                List<String> haventUserStr = new ArrayList<>();
                // 无有项目的人员
                List<String> notHaventUserStr = new ArrayList<>();
                List<SchemeToPerson> updateList = new ArrayList<>();
                for (Map.Entry<String, SchemeToPerson> stringSchemeToPersonEntry : schemeToPerson.entrySet()) {
                    if(personIdList.contains(stringSchemeToPersonEntry.getKey())){
                        SchemeToPerson scheme= stringSchemeToPersonEntry.getValue();
                        String msc=String.format("用户编号：%s ，用户名称：%s",scheme.getUserCode(),scheme.getUserName());
                        if(scheme.getIsHaveProject()){
                            haventUserStr.add(msc);
                        }else{
                            notHaventUserStr.add(msc);
                            scheme.setIsHaveProject(Boolean.TRUE);
                            scheme.setPlanSchemeId(schemeId);
                            updateList.add(scheme);
                        }
                    }
                }
                List<SchemeToPerson> addUserStr = new ArrayList<>();
                for (SchemeToPerson s : schemeToPersonList) {
                    if(!schemeToPerson.containsKey(s.getUserId())){
                        addUserStr.add(s);
                    }
                }
                if(!CollectionUtils.isEmpty(addUserStr)){
                    this.saveBatch(addUserStr);
                }
                if(!CollectionUtils.isEmpty(updateList)){
                    this.updateBatchById(updateList);
                }
                return;
            }
        }
        this.saveBatch(schemeToPersonList);

    }

    @AllArgsConstructor
    @Data
    protected static class CompositeKey {

        final String userId;

        final String userCode;

        final String userName;

        private String personId;

        private Boolean isHaveProject;
    }

    public static class PlanPersonListener extends AnalysisEventListener<PersonPlanVO> {

        private final List<PersonPlanVO> data = new ArrayList<>();

        @Override
        public void invoke(PersonPlanVO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<PersonPlanVO> getData() {
            return data;
        }
    }

}
