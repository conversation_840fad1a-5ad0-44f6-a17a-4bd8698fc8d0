<?xml version="1.0" encoding="UTF-8"?>

<!--
Copyright (c) 2008 Sonatype, Inc. All rights reserved.

This program is licensed to you under the Apache License Version 2.0,
and you may not use this file except in compliance with the Apache License Version 2.0.
You may obtain a copy of the Apache License Version 2.0 at http://www.apache.org/licenses/LICENSE-2.0.

Unless required by applicable law or agreed to in writing,
software distributed under the Apache License Version 2.0 is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the Apache License Version 2.0 for the specific language governing permissions and limitations there under.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.sonatype.forge</groupId>
    <artifactId>forge-parent</artifactId>
    <version>6</version>
  </parent>

  <groupId>org.sonatype.aether</groupId>
  <artifactId>aether-parent</artifactId>
  <version>1.7</version>
  <packaging>pom</packaging>

  <name>Aether</name>
  <description>
    The parent and aggregator for the repository system.
  </description>
  <url>http://aether.sonatype.org/</url>
  <inceptionYear>2010</inceptionYear>

  <organization>
    <name>Sonatype, Inc.</name>
    <url>http://www.sonatype.com</url>
  </organization>

  <mailingLists>
    <mailingList>
      <name>Aether Developers List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <!-- archive>?</archive -->
    </mailingList>
    <mailingList>
      <name>Aether Users List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <!-- archive>?</archive -->
    </mailingList>
    <mailingList>
      <name>Aether Commits List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
    </mailingList>
  </mailingLists>

  <scm>
    <connection>scm:git:**************:sonatype/sonatype-aether.git</connection>
    <developerConnection>scm:git:**************:sonatype/sonatype-aether.git</developerConnection>
    <url>**************:sonatype/sonatype-aether.git</url>
  </scm>

  <issueManagement>
    <system>jira</system>
    <url>https://issues.sonatype.org/browse/AETHER</url>
  </issueManagement>

  <ciManagement>
    <system>Hudson</system>
    <url>https://grid.sonatype.org/ci/job/Aether/</url>
  </ciManagement>

  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <modules>
    <module>aether-api</module>
    <module>aether-spi</module>
    <module>aether-util</module>
    <module>aether-impl</module>
    <module>aether-test-util</module>
    <module>aether-connector-file</module>
    <module>aether-connector-wagon</module>
  </modules>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <forgeReleaseUrl>https://repository.sonatype.org/service/local/staging/deploy/maven2</forgeReleaseUrl>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>4.8.1</version>
        <scope>test</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>2.2-beta-5</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>2.1</version>
          <configuration>
            <source>1.5</source>
            <target>1.5</target>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-gpg-plugin</artifactId>
          <version>1.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>2.5</version>
          <configuration>
            <links>
              <link>http://java.sun.com/javase/6/docs/api/</link>
            </links>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-release-plugin</artifactId>
          <version>2.0</version>
          <configuration>
            <autoVersionSubmodules>true</autoVersionSubmodules>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>animal-sniffer-maven-plugin</artifactId>
          <version>1.6</version>
          <configuration>
            <signature>
              <groupId>org.codehaus.mojo.signature</groupId>
              <artifactId>java15</artifactId>
              <version>1.0</version>
            </signature>
          </configuration>
          <executions>
            <execution>
              <id>check-java-1.5-compat</id>
              <phase>process-classes</phase>
              <goals>
                <goal>check</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.codehaus.plexus</groupId>
          <artifactId>plexus-component-metadata</artifactId>
          <version>1.5.5</version>
          <executions>
            <execution>
              <id>generate-components-xml</id>
              <goals>
                <goal>generate-metadata</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>

  <profiles>
    <profile>
      <id>demo</id>
      <modules>
        <module>aether-demo</module>
      </modules>
    </profile>
    <profile>
      <id>release</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-assembly-plugin</artifactId>
            <dependencies>
              <dependency>
                <groupId>org.apache.apache.resources</groupId>
                <artifactId>apache-source-release-assembly-descriptor</artifactId>
                <version>1.0.2</version>
              </dependency>
            </dependencies>
            <executions>
              <execution>
                <id>attach-source-release-distro</id>
                <phase>package</phase>
                <goals>
                  <goal>single</goal>
                </goals>
                <configuration>
                  <runOnlyAtExecutionRoot>true</runOnlyAtExecutionRoot>
                  <descriptorRefs>
                    <descriptorRef>source-release</descriptorRef>
                  </descriptorRefs>
                  <tarLongFileFormat>gnu</tarLongFileFormat>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
