package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;

import java.util.Date;
import java.lang.Boolean;
import java.lang.String;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * QuestionLibrary DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-17 13:36:10
 */
@ApiModel(value = "QuestionLibraryDTO对象", description = "问题库")
@Data
@ExcelIgnoreUnannotated
public class QuestionLibraryDTO extends ObjectDTO implements Serializable{

    /**
     * 问题来源
     */
    @ApiModelProperty(value = "问题来源")
    @ExcelProperty(value = "问题来源 ", index = 0)
    private String questionSource;

    /**
     * 严重程度
     */
    @ApiModelProperty(value = "严重程度")
    @ExcelProperty(value = "严重程度 ", index = 1)
    private String seriousLevel;

    /**
     * 预计完成时间
     */
    @ApiModelProperty(value = "预计完成时间")
    @ExcelProperty(value = "预计完成时间 ", index = 2)
    private Date predictEndTime;

    /**
     * 问题类型
     */
    @ApiModelProperty(value = "问题类型")
    @ExcelProperty(value = "问题类型 ", index = 3)
    private String questionType;

    /**
     * 提出人
     */
    @ApiModelProperty(value = "提出人")
    @ExcelProperty(value = "提出人 ", index = 4)
    private String exhibitor;

    /**
     * 提出人名称
     */
    @ApiModelProperty(value = "提出人名称")
    @ExcelProperty(value = "提出人名称 ", index = 5)
    private String exhibitorName;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @ExcelProperty(value = "项目ID ", index = 6)
    private String projectId;

    /**
     * 问题内容
     */
    @ApiModelProperty(value = "问题内容")
    @ExcelProperty(value = "问题内容 ", index = 7)
    private String content;

    /**
     * 提出时间
     */
    @ApiModelProperty(value = "提出时间")
    @ExcelProperty(value = "提出时间 ", index = 8)
    private Date proposedTime;

    /**
     * 进度
     */
    @ApiModelProperty(value = "进度")
    @ExcelProperty(value = "进度 ", index = 9)
    private String schedule;

    /**
     * 接收人
     */
    @ApiModelProperty(value = "接收人")
    @ExcelProperty(value = "接收人 ", index = 10)
    private String recipient;

    /**
     * 接收人名称
     */
    @ApiModelProperty(value = "接收人名称")
    @ExcelProperty(value = "接收人名称 ", index = 11)
    private String recipientName;

    /**
     * 负责人id
     */
    @ApiModelProperty(value = "负责人id")
    @ExcelProperty(value = "负责人id ", index = 12)
    private String principalId;

    /**
     * 负责人名称
     */
    @ApiModelProperty(value = "负责人名称")
    @ExcelProperty(value = "负责人名称 ", index = 13)
    private String principalName;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    @ExcelProperty(value = "优先级 ", index = 14)
    private String priorityLevel;

    /**
     * 文档ID
     */
    @ApiModelProperty(value = "文档ID")
    @ExcelProperty(value = "文档ID ", index = 15)
    private String documentId;

    /**
     * 产品编码
     */
    @ApiModelProperty(value = "产品编码")
    @ExcelProperty(value = "产品编码 ", index = 16)
    private String productNumber;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    @ExcelProperty(value = "物料编码 ", index = 17)
    private String materialNumber;

    /**
     * 生产订单
     */
    @ApiModelProperty(value = "生产订单")
    @ExcelProperty(value = "生产订单 ", index = 18)
    private String productionOrders;

    /**
     * 产品编号（SN）
     */
    @ApiModelProperty(value = "产品编号（SN）")
    @ExcelProperty(value = "产品编号（SN） ", index = 19)
    private String productNumberSn;

    /**
     * 阶段
     */
    @ApiModelProperty(value = "阶段")
    @ExcelProperty(value = "阶段 ", index = 20)
    private String stage;

    /**
     * 过程环节
     */
    @ApiModelProperty(value = "过程环节")
    @ExcelProperty(value = "过程环节 ", index = 21)
    private String processLink;

    /**
     * 过程分类
     */
    @ApiModelProperty(value = "过程分类")
    @ExcelProperty(value = "过程分类 ", index = 22)
    private String processClassifi;

    /**
     * 问题现象一级分类
     */
    @ApiModelProperty(value = "问题现象一级分类")
    @ExcelProperty(value = "问题现象一级分类 ", index = 23)
    private String problemPhenomenonOne;

    /**
     * 问题现象二级分类
     */
    @ApiModelProperty(value = "问题现象二级分类")
    @ExcelProperty(value = "问题现象二级分类 ", index = 24)
    private String problemPhenomenonTwo;

    /**
     * 问题现象三级分类
     */
    @ApiModelProperty(value = "问题现象三级分类")
    @ExcelProperty(value = "问题现象三级分类 ", index = 25)
    private String problemPhenomenonTh;

    /**
     * 问题等级分类
     */
    @ApiModelProperty(value = "问题等级分类")
    @ExcelProperty(value = "问题等级分类 ", index = 26)
    private String problemLevel;

    /**
     * 一级原因分类
     */
    @ApiModelProperty(value = "一级原因分类")
    @ExcelProperty(value = "一级原因分类 ", index = 27)
    private String reasionOne;

    /**
     * 二级原因分类
     */
    @ApiModelProperty(value = "二级原因分类")
    @ExcelProperty(value = "二级原因分类 ", index = 28)
    private String reasionTwo;

    /**
     * 三级原因分类
     */
    @ApiModelProperty(value = "三级原因分类")
    @ExcelProperty(value = "三级原因分类 ", index = 29)
    private String reasionThree;

    /**
     * 原因分析描述
     */
    @ApiModelProperty(value = "原因分析描述")
    @ExcelProperty(value = "原因分析描述 ", index = 30)
    private String reasionRemark;

    /**
     * 纠正分类
     */
    @ApiModelProperty(value = "纠正分类")
    @ExcelProperty(value = "纠正分类 ", index = 31)
    private String correctClassifi;

    /**
     * 关联ECN编号
     */
    @ApiModelProperty(value = "关联ECN编号")
    @ExcelProperty(value = "关联ECN编号 ", index = 32)
    private String ecnNumber;

    /**
     * 纠正描述
     */
    @ApiModelProperty(value = "纠正描述")
    @ExcelProperty(value = "纠正描述 ", index = 33)
    private String correctRemark;

    /**
     * 问题升级类型
     */
    @ApiModelProperty(value = "问题升级类型")
    @ExcelProperty(value = "问题升级类型 ", index = 34)
    private String questionUpType;

    /**
     * 是否疑难技术问题
     */
    @ApiModelProperty(value = "是否疑难技术问题")
    @ExcelProperty(value = "是否疑难技术问题 ", index = 35)
    private Boolean isDiTechnicalIssues;

    /**
     * 是否综合评估不再解决
     */
    @ApiModelProperty(value = "是否综合评估不再解决")
    @ExcelProperty(value = "是否综合评估不再解决 ", index = 36)
    private Boolean isAssess;

    /**
     * 是否生态问题
     */
    @ApiModelProperty(value = "是否生态问题")
    @ExcelProperty(value = "是否生态问题 ", index = 37)
    private Boolean isEcologicalIssues;

    /**
     * 是否典型质量案例
     */
    @ApiModelProperty(value = "是否典型质量案例")
    @ExcelProperty(value = "是否典型质量案例 ", index = 38)
    private Boolean isQualityUseCases;

    /**
     * 举一反三
     */
    @ApiModelProperty(value = "举一反三")
    @ExcelProperty(value = "举一反三 ", index = 39)
    private String oneCaseToAnother;

    /**
     * 纠正措施描述
     */
    @ApiModelProperty(value = "纠正措施描述")
    @ExcelProperty(value = "纠正措施描述 ", index = 40)
    private String correAcDescription;

    /**
     * 意见类别
     */
    @ApiModelProperty(value = "意见类别")
    @ExcelProperty(value = "意见类别 ", index = 41)
    private String opinionCategories;

    /**
     * 评审要点
     */
    @ApiModelProperty(value = "评审要点")
    @ExcelProperty(value = "评审要点 ", index = 42)
    private String reviewPoints;

    /**
     * 是否技术问题
     */
    @ApiModelProperty(value = "是否技术问题")
    @ExcelProperty(value = "是否技术问题 ", index = 43)
    private Boolean isTechnicalIssues;

    /**
     * 是否典型问题
     */
    @ApiModelProperty(value = "是否典型问题")
    @ExcelProperty(value = "是否典型问题 ", index = 44)
    private Boolean isTypicalProblems;

    /**
     * 意见分类
     */
    @ApiModelProperty(value = "意见分类")
    @ExcelProperty(value = "意见分类 ", index = 45)
    private String opClassification;

    /**
     * 采纳情况
     */
    @ApiModelProperty(value = "采纳情况")
    @ExcelProperty(value = "采纳情况 ", index = 46)
    private String adoptionSituation;

    /**
     * 整体改进描述
     */
    @ApiModelProperty(value = "整体改进描述")
    @ExcelProperty(value = "整体改进描述 ", index = 47)
    private String overallDescription;

    /**
     * 问题ID
     */
    @ApiModelProperty(value = "问题ID")
    @ExcelProperty(value = "问题ID ", index = 48)
    private String questionId;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @ExcelProperty(value = "名称 ", index = 49)
    private String name;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    @ExcelProperty(value = "编号 ", index = 50)
    private String number;


}
