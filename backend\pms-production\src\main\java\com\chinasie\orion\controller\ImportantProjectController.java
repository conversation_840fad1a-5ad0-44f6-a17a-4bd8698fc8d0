package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.dto.importantProject.ImportParamDTO;
import com.chinasie.orion.domain.vo.count.DateComputeVO;
import com.chinasie.orion.domain.entity.ImportantProject;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ImportantProjectService;
import com.chinasie.orion.service.JobManageService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.mzt.logapi.starter.annotation.LogRecords;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * ImportantProject 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-24 15:40:21
 */
@RestController
@RequestMapping("/importantProject")
@Api(tags = "重大项目")
public class  ImportantProjectController  {

    @Autowired
    private ImportantProjectService importantProjectService;

    @Autowired
    private JobManageService jobManageService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【重大项目】详情", type = "ImportantProject", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ImportantProjectVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        ImportantProjectVO rsp = importantProjectService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param importantProjectDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【重大项目】数据【{{#importantProjectDTO.projectName}}】", type = "ImportantProject", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody ImportantProjectDTO importantProjectDTO) throws Exception {
        String rsp =  importantProjectService.create(importantProjectDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param importantProjectDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【重大项目】数据【{{#importantProjectDTO.name}}】", type = "ImportantProject", subType = "编辑", bizNo = "{{#importantProjectDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  ImportantProjectDTO importantProjectDTO) throws Exception {
        Boolean rsp = importantProjectService.edit(importantProjectDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【重大项目】数据", type = "ImportantProject", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = importantProjectService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【重大项目】数据", type = "ImportantProject", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = importantProjectService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【重大项目】数据", type = "ImportantProject", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ImportantProjectVO>> pages(@RequestBody Page<ImportantProjectDTO> pageRequest) throws Exception {
        Page<ImportantProjectVO> rsp =  importantProjectService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("重大项目导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载【重大项目】导入模板", type = "ImportantProject", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        importantProjectService.downloadExcelTpl(response);
    }

    @ApiOperation("重大项目导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验【重大项目】导入", type = "ImportantProject", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = importantProjectService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("重大项目导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认【重大项目】导入", type = "ImportantProject", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  importantProjectService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消重大项目导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消【重大项目】导入", type = "ImportantProject", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  importantProjectService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("重大项目导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出【重大项目】数据", type = "ImportantProject", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody Page<ImportantProject> pageRequest, HttpServletResponse response) throws Exception {
        importantProjectService.exportByExcel(pageRequest, response);
    }

    @ApiOperation("计算日期")
    @PostMapping(value = "/computeDate")
    public ResponseDTO<DateComputeVO> computeDate(@RequestBody List<String> ids) throws Exception {
        DateComputeVO rsp = importantProjectService.computeDate(ids);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("获取相应大修没被选择的作业")
    @PostMapping(value = "/getUnSelectedJob")
    @LogRecord(success = "【{USER{#logUserId}}】获取相应大修没被选择的作业", type = "JobManage", subType = "查询", bizNo = "")
    public ResponseDTO<Page<JobManageVO>> getUnSelectedJob(@RequestBody Page<JobManageDTO> pageRequest) throws Exception {
        Page<JobManageVO> rsp = jobManageService.projectJobManage(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("获取重大项目相关进度")
    @PostMapping("/getImportantProjectProgress")
    public ResponseDTO<Page<JobProgressVO>> getJobProgressByProjectId(@RequestBody Page<JobProgressDTO> pageRequest) throws Exception {
        Page<JobProgressVO> rsp = importantProjectService.getJobProgressByProjectId(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param materialParamDTOPage
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取重大项目下相关物资列表")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询大修下的物资列表", type = "SchemeToMaterial", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/material/page", method = RequestMethod.POST)
    public ResponseDTO<Page<SchemeToMaterialVO>> materialPages(@RequestBody Page<ImportParamDTO> materialParamDTOPage) throws Exception {
        Page<SchemeToMaterialVO> rsp =  importantProjectService.materialPages( materialParamDTOPage);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "获取重大项目下相关物资统计")
    @RequestMapping(value = "/getMaterial/count", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<MajorPersonStatisticDTO> getMaterial(@RequestBody ImportParamDTO materialParamDTO) throws Exception {
        MajorPersonStatisticDTO rsp = importantProjectService.getMaterialStatistic(materialParamDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取重大项目下的人员列表")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询重大项目下的人员列表数据", type = "SchemeToPerson", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/person/page", method = RequestMethod.POST)
    public ResponseDTO<Page<SchemeToPersonVO>> personPages(@RequestBody Page<ImportParamDTO> pageRequest) throws Exception {
        Page<SchemeToPersonVO> rsp = importantProjectService.personPages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 大修人员统计
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "人员统计")
    @RequestMapping(value = "/getPerson", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<MajorPersonStatisticDTO> getPerson(@RequestBody ImportParamDTO importParamDTO) throws Exception {
        MajorPersonStatisticDTO rsp = importantProjectService.getPersonStatistic(importParamDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("新增进展")
    @PostMapping("/addProgress")
    public ResponseDTO<Boolean> addProgress(@RequestBody JobProgressDTO jobProgressDTO){
        importantProjectService.addProgress(jobProgressDTO);
        return new ResponseDTO<>(true);
    }

    @ApiOperation("修改进展")
    @PostMapping("/editProgress")
    public ResponseDTO<Boolean> editProgress(@RequestBody JobProgressDTO jobProgressDTO){
        importantProjectService.editProgress(jobProgressDTO);
        return new ResponseDTO<>(true);
    }

    @ApiOperation("删除进展")
    @PostMapping("/deleteProgress")
    public ResponseDTO<Boolean> deleteProgress(@RequestBody List<String> ids){
        return new ResponseDTO<>(importantProjectService.deleteProgress(ids));
    }


    @ApiOperation("通过projectId获取作业列表")
    @PostMapping("/jobList")
    public ResponseDTO<Page<JobManageVO>> jobList(@RequestBody Page<JobManageDTO> pageRequest) throws Exception {
        Page<JobManageVO> rsp = jobManageService.jobListByProjectId(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("查询关联隐患")
    @PostMapping(value = "/relation/safetyQualityEnv/lists/{projectId}/{jobManageId}")
    public ResponseDTO<List<SafetyQualityEnvVO>> relationToQuestionLists(@PathVariable("projectId") String projectId,
                                                                         @PathVariable("jobManageId") String jobManageId,
                                                                         @RequestBody(required = false) JobManageDTO planQueryDTO) throws Exception {
        List<SafetyQualityEnvVO> rsp = jobManageService.relationToSafetyQualityEnv(projectId,jobManageId, planQueryDTO);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("新增关联隐患")
    @PostMapping(value = "/relation/safetyQualityEnv/add")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】新增计划关联隐患", type = "项目计划", subType = "关联隐患(过程记录)", bizNo = "{{#fromIdsRelationToIdDTO.toId}}")
    })
    public ResponseDTO<Boolean> relationToSafetyQualityEnv(@RequestBody FromIdsRelationDTO fromIdsRelationToIdDTO) throws Exception {
        Boolean rsp = jobManageService.relationToSafetyQualityEnv(fromIdsRelationToIdDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("删除关联隐患")
    @DeleteMapping(value = "/relation/safetyQualityEnv/remove")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】删除计划关联隐患", type = "项目计划", subType = "删除关联隐患(过程记录)", bizNo = "{{#fromIdsRelationToIdDTO.toId}}")
    })
    public ResponseDTO<Boolean> removeRelationToRisk(@RequestBody FromIdsRelationDTO fromIdsRelationToIdDTO) throws Exception {
        Boolean rsp = jobManageService.removeRelationToSafetyQualityEnv(fromIdsRelationToIdDTO);
        return new ResponseDTO<>(rsp);
    }
}
