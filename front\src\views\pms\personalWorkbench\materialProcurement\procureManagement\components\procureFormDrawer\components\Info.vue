<script setup lang="ts">
import {
  BasicForm, FormSchema, InputSelectUser, useForm,
} from 'lyra-component-vue3';
import {
  computed, h, inject, nextTick, onMounted, ref, Ref, unref, watch, watchEffect,
} from 'vue';
import Api from '/@/api';
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import TableRender from '/src/views/pms/projectLibrary/pages/components/procureFormDrawer/components/TableRender.vue';

interface UserItem {
  id: string,
  name: string
}

interface OptionItem {
  label: string,
  value: string
}

const dataId:Ref = inject('dataId');
const detailsData: Record<string, any> = inject('detailsData');
// 采购订单基本信息 、项目采购订单明细
const { projectPurchaseOrderInfoVO, projectPurchaseOrderDetailVOList } = unref(detailsData);
// 采购员
const selectUserData: Ref<UserItem[]> = ref([]);
// 采购员所在部门
const selectDeptData: Ref<OptionItem[]> = ref([]);
// 采购类型
const procureTypeOptions: Ref<any[]> = ref([]);

const schemas: FormSchema[] = [
  {
    field: 'formTitle',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(DetailsLayout, {
        title: '订单基本信息',
        isFormItem: true,
        isTitle: true,
      });
    },
  },
  {
    field: 'name',
    component: 'Input',
    label: '订单名称',
    required: true,
    colProps: {
      span: 24,
    },
    componentProps: {
      showCount: true,
      maxlength: 100,
    },
  },
  {
    field: 'resUserId',
    component: 'Input',
    label: '采购员',
    required: true,
    render() {
      return h(InputSelectUser, {
        selectUserData,
        onChange: inputSelectUserChange,
        selectUserModalProps: { selectType: 'radio' },
      });
    },
  },
  {
    field: 'rspDeptId',
    component: 'Select',
    label: '采购负责部门',
    required: true,
    componentProps: {
      disabled: true,
      options: selectDeptData,
    },
  },
  {
    field: 'haveTaxTotalAmt',
    component: 'InputMoney',
    label: '含税总金额',
    rules: [
      {
        required: true,
        type: 'number',
        message: ' ',
      },
    ],
    componentProps: {
      style: 'width:100%',
      addonAfter: '元',
      disabled: true,
      min: 0,
    },
  },
  {
    field: 'currency',
    component: 'Input',
    label: '币种',
    required: true,
    componentProps: {
      placeholder: '请输入订单交易币种，例如：RMB、USD',
    },
  },
  {
    field: 'purchaseTotalAmount',
    component: 'InputMoney',
    label: '采购总数量',
    rules: [
      {
        required: true,
        type: 'number',
        message: ' ',
      },
    ],
    componentProps: {
      disabled: true,
      min: 0,
    },
  },
  {
    field: 'purchaseType',
    component: 'ApiSelect',
    label: '采购类型',
    required: true,
    defaultValue: 'goodsType',
    componentProps: {
      allowClear: false,
      api: async () => {
        const result = await new Api('/dme/dict-value/dict').fetch({
          dictId: 'dict1717099480769298432',
        }, '', 'GET');
        procureTypeOptions.value = result || [];
        return unref(procureTypeOptions);
      },
      labelField: 'description',
    },
  },
  {
    field: 'orderArrivalDate',
    component: 'DatePicker',
    label: '订单到货日期',
    required: true,
    componentProps: {},
  },
  {
    field: 'orderDesc',
    component: 'InputTextArea',
    label: '订单说明',
    colProps: {
      span: 24,
    },
    componentProps: {
      showCount: true,
      maxlength: 1000,
    },
  },
  {
    field: 'projectPurchaseOrderDetailVOList',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    ifShow: ({ values }) => values.purchaseType,
    slot: 'table-render',
  },
];

const [register, { validate, setFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

// 监听采购员变化
watchEffect(async () => {
  if (selectUserData.value.length) {
    const result: Record<string, any> | null = await new Api('/pmi/user/user-profile').fetch('', selectUserData.value[0].id, 'GET');
    selectDeptData.value = result ? [
      {
        label: result.orgName,
        value: result.orgId,
      },
    ] : [];
    await setFieldsValue({
      rspDeptId: result.orgId,
    });
  }
});

const tableRef: Ref = ref();

// 含税总金额
const haveTaxTotalAmt = computed(() => {
  const data = tableRef.value?.getData();
  if (data && data.length) {
    return data.reduce((prev: number, next: Record<string, any>) => prev + Number(next.haveTaxTotalAmt), 0);
  }
  return 0;
});
// 采购总数量
const purchaseTotalAmount = computed(() => {
  const data = tableRef.value?.getData();
  if (data && data.length) {
    return data.reduce((prev: number, next: Record<string, any>) => prev + Number(next.purchaseAmount), 0);
  }
  return 0;
});

watch(() => haveTaxTotalAmt.value, (value) => {
  setFieldsValue({
    haveTaxTotalAmt: value,
  });
});

watch(() => purchaseTotalAmount.value, (value) => {
  setFieldsValue({
    purchaseTotalAmount: value,
  });
});

onMounted(() => {
  initForm();
});

// 初始化表单
async function initForm() {
  if (projectPurchaseOrderInfoVO) {
    selectUserData.value = [
      {
        id: projectPurchaseOrderInfoVO?.resUserId,
        name: projectPurchaseOrderInfoVO?.resUserName,
      },
    ];
  }
  await setFieldsValue(projectPurchaseOrderInfoVO);
  await nextTick();
  if (dataId.value) {
    tableRef.value.setData(projectPurchaseOrderDetailVOList);
  }
}

// 选人表单组件回调
function inputSelectUserChange(users: UserItem[]) {
  selectUserData.value = users;
  setFieldsValue({
    resUserId: users[0].id,
  });
}

defineExpose({
  validate,
  validateOrderList: () => tableRef.value?.validate(),
});

</script>

<template>
  <BasicForm @register="register">
    <template #table-render="{model}">
      <TableRender
        :key="model['purchaseType']"
        ref="tableRef"
        :purchaseTypeValue="model['purchaseType']"
      />
    </template>
  </BasicForm>
</template>

<style scoped lang="less">

</style>
