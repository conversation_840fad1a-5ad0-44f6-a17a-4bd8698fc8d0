package com.chinasie.orion.service.impl;



import com.chinasie.orion.domain.dto.AmpereringDictInfoDTO;
import com.chinasie.orion.domain.entity.AmpereringDictInfo;
import com.chinasie.orion.domain.vo.AmpereringDictInfoVO;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.AmpereringDictInfoMapper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.AmpereringDictInfoService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * AmpereringDictInfo 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-30 00:02:31
 */
@Service
@Slf4j
public class AmpereringDictInfoServiceImpl extends  OrionBaseServiceImpl<AmpereringDictInfoMapper, AmpereringDictInfo>   implements AmpereringDictInfoService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public AmpereringDictInfoVO detail(String id, String pageCode) throws Exception {
        AmpereringDictInfo ampereringDictInfo =this.getById(id);
        AmpereringDictInfoVO result = BeanCopyUtils.convertTo(ampereringDictInfo,AmpereringDictInfoVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param ampereringDictInfoDTO
     */
    @Override
    public  String create(AmpereringDictInfoDTO ampereringDictInfoDTO) throws Exception {
        AmpereringDictInfo ampereringDictInfo =BeanCopyUtils.convertTo(ampereringDictInfoDTO,AmpereringDictInfo::new);
        this.save(ampereringDictInfo);

        String rsp=ampereringDictInfo.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param ampereringDictInfoDTO
     */
    @Override
    public Boolean edit(AmpereringDictInfoDTO ampereringDictInfoDTO) throws Exception {
        AmpereringDictInfo ampereringDictInfo =BeanCopyUtils.convertTo(ampereringDictInfoDTO,AmpereringDictInfo::new);

        this.updateById(ampereringDictInfo);

        String rsp=ampereringDictInfo.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<AmpereringDictInfoVO> pages( Page<AmpereringDictInfoDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<AmpereringDictInfo> condition = new LambdaQueryWrapperX<>( AmpereringDictInfo. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(AmpereringDictInfo::getCreateTime);


        Page<AmpereringDictInfo> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), AmpereringDictInfo::new));

        PageResult<AmpereringDictInfo> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<AmpereringDictInfoVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<AmpereringDictInfoVO> vos = BeanCopyUtils.convertListTo(page.getContent(), AmpereringDictInfoVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "业务字典表（明细表）导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", AmpereringDictInfoDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        AmpereringDictInfoExcelListener excelReadListener = new AmpereringDictInfoExcelListener();
        EasyExcel.read(inputStream,AmpereringDictInfoDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<AmpereringDictInfoDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("业务字典表（明细表）导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<AmpereringDictInfo> ampereringDictInfoes =BeanCopyUtils.convertListTo(dtoS,AmpereringDictInfo::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::AmpereringDictInfo-import::id", importId, ampereringDictInfoes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<AmpereringDictInfo> ampereringDictInfoes = (List<AmpereringDictInfo>) orionJ2CacheService.get("pmsx::AmpereringDictInfo-import::id", importId);
        log.info("业务字典表（明细表）导入的入库数据={}", JSONUtil.toJsonStr(ampereringDictInfoes));

        this.saveBatch(ampereringDictInfoes);
        orionJ2CacheService.delete("pmsx::AmpereringDictInfo-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::AmpereringDictInfo-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<AmpereringDictInfo> condition = new LambdaQueryWrapperX<>( AmpereringDictInfo. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(AmpereringDictInfo::getCreateTime);
        List<AmpereringDictInfo> ampereringDictInfoes =   this.list(condition);

        List<AmpereringDictInfoDTO> dtos = BeanCopyUtils.convertListTo(ampereringDictInfoes, AmpereringDictInfoDTO::new);

        String fileName = "业务字典表（明细表）数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", AmpereringDictInfoDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<AmpereringDictInfoVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class AmpereringDictInfoExcelListener extends AnalysisEventListener<AmpereringDictInfoDTO> {

        private final List<AmpereringDictInfoDTO> data = new ArrayList<>();

        @Override
        public void invoke(AmpereringDictInfoDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<AmpereringDictInfoDTO> getData() {
            return data;
        }
    }


}
