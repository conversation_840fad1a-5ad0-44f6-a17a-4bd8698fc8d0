package com.chinasie.orion.constant;

/**
 * @author: yk
 * @date: 2023/10/10 13:54
 */
public enum ProjectContractMessageNodeEnums {
    PAY_NODE_CONFIRM_COMMIT("Node_Contract_Pay_Node_Commit","","", "项目合同支付节点审批提交（待办）"),
    PAY_NODE_CONFIRM_REJECT("Node_Contract_Pay_Node_Reject", "","","项目合同支付节点审批驳回（待办）"),
    PAY_NODE_CONFIRM_AUDITED("Node_Contract_Pay_Node_Audited", "","","项目合同支付节点审批通过（待办）"),
    PAY_NODE_CONFIRM_EXPIRE("Node_Contract_Pay_Node_Expire", "","","项目合同支付节点到期（待办）"),
    ;

    private String code;

    private String description;

    private String messageUrlName;

    private String messageUrl;

    ProjectContractMessageNodeEnums(String code, String messageUrl, String messageUrlName, String description) {
        this.code = code;
        this.messageUrl = messageUrl;
        this.messageUrlName = messageUrlName;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public String getMessageUrlName() {
        return messageUrlName;
    }

    public String getMessageUrl() {
        return messageUrl;
    }
}
