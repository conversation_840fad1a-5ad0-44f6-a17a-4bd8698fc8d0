<script setup lang="ts">
import {
  BasicCard, BasicForm, DataStatusTag, FormSchema, OrionTable, useForm,
} from 'lyra-component-vue3';
import {
  computed,
  h, onMounted, Ref, ref,
} from 'vue';
import {
  get, debounce, minBy, maxBy, filter, map,
} from 'lodash-es';
import Api from '/@/api';
import { Popover } from 'ant-design-vue';
import dayjs from 'dayjs';

const props = defineProps<{
  record: Record<string, any> | null,
}>();
const loading = ref(false);
const schemas: FormSchema[] = [
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '项目信息',
        class: 'form-title-row',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  {
    field: 'projectName',
    label: '项目名称',
    component: 'Input',
    colProps: {
      span: 12,
    },
    rules: [
      {
        required: true,
      },
    ],
  },
  {
    field: 'rspUserId',
    label: '项目负责人',
    rules: [
      {
        required: true,
        type: 'array',
      },
    ],
    componentProps: {
      selectUserModalProps: {
        selectType: 'radio',
      },
      async onChange(user: any[]) {
        await setFieldsValue({
          deptId: user?.[0]?.simpleUser?.orgId,
          rspUserCode: get(user, '0.code'),
        });
        await validate(['rspUserId']);
      },
    },
    component: 'SelectUser',
  },
  {
    field: 'rspUserCode',
    label: '负责人工号',
    component: 'Input',
    colProps: {
      span: 12,
    },
    componentProps: {
      disabled: true,
      placeholder: '选择项目负责人后带出来',
    },
  },
  {
    field: 'deptId',
    label: '负责人所在中心',
    colProps: {
      span: 12,
    },
    componentProps: {
      disabled: true,
      placeholder: '选择项目负责人后带出来',
    },
    component: 'TreeSelectOrg',
  },
  {
    field: 'planStart',
    label: '计划开始日期',
    colProps: {
      span: 12,
    },
    componentProps: {
      disabled: true,
      placeholder: '选择作业后自动计算',
      valueFormat: 'YYYY-MM-DD',
    },
    component: 'DatePicker',
  },
  {
    field: 'planEnd',
    label: '计划结束日期',
    colProps: {
      span: 12,
    },
    componentProps: {
      disabled: true,
      placeholder: '选择作业后自动计算',
      valueFormat: 'YYYY-MM-DD',
    },
    component: 'DatePicker',
  },
  {
    field: 'actureStart',
    label: '实际开始日期',
    colProps: {
      span: 12,
    },
    componentProps: {
      disabled: true,
      placeholder: '选择作业后自动计算',
      valueFormat: 'YYYY-MM-DD',
    },
    component: 'DatePicker',
  },
  {
    field: 'actureEnd',
    label: '实际结束日期',
    colProps: {
      span: 12,
    },
    componentProps: {
      disabled: true,
      placeholder: '选择作业后自动计算',
      valueFormat: 'YYYY-MM-DD',
    },
    component: 'DatePicker',
  },
];
const [register, { validate, setFieldsValue, getFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

const selectedRows: Ref<Record<string, any>[]> = ref([]);
const selectedRowKeys = ref([]);
const _debounce = (fn) => {
  const _func = debounce(fn, 1000);
  _func();
};

const tableRef = ref();
const tableOptions = {
  showToolButton: false,
  showScreenButton: false,
  isSpacing: true,
  smallSearchField: [
    'name',
    'number',
    'rspUserName',
    'rspDeptName',
  ],
  rowSelection: {
    onChange(_keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
      selectedRowKeys.value = _keys;
      if (_keys.length && !rows.length) {
        return;
      }
      _debounce(() => {
        reduceTimeRangeByWorkId(selectedRows.value);
      });
    },
  },
  api: (params: any) => new Api('/pms/importantProject/getUnSelectedJob').fetch({
    ...params,
    query: {
      repairRound: props.record?.repairRound,
      projectId: get(props, 'record.id'),
    },
  }, '', 'POST'),
  columns: [
    {
      title: '作业名称',
      dataIndex: 'name',
    },
    {
      title: '工单号',
      dataIndex: 'number',
      width: 130,
    },
    {
      title: '作业负责人',
      width: 130,
      dataIndex: 'rspUserName',
    },
    {
      title: '负责人所在中心',
      dataIndex: 'rspDeptName',
      width: 130,
    },
    {
      title: '是否高风险',
      dataIndex: 'isHighRisk',
      width: 120,
      customRender({ text }) {
        return text ? '是' : text === false ? '否' : '';
      },
    },
    {
      title: '高风险等级',
      width: 130,
      dataIndex: 'heightRiskName',
    },
    {
      title: '作业状态',
      dataIndex: 'phase',
      width: 130,
    },
    {
      title: '计划开始日期',
      dataIndex: 'beginTime',
      width: 130,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '计划结束日期',
      dataIndex: 'endTime',
      width: 130,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '实际开工时间',
      dataIndex: 'actualBeginTime',
      width: 130,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '实际完成时间',
      dataIndex: 'actualEndTime',
      width: 130,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
  ],
};
const selectWorkSum = computed(() => selectedRowKeys.value?.length);

async function getMajorProjectInfo() {
  try {
    const result = await new Api('/pms/importantProject').fetch('', props.record.id, 'GET');
    await setFieldsValue({
      ...result,
      rspUserId: [
        {
          id: get(result, 'rspUserId'),
          name: get(result, 'rspUserName'),
        },
      ],
    });
    const jobList = get(result, 'jobList', []) ?? [];
    selectedRowKeys.value = jobList;
    tableRef.value?.setSelectedRowKeys(jobList);
  } catch (e) {}
}
function reduceTimeRangeByWorkId(rows) {
  const reduceMinTime = (key) => {
    const beforeRows = filter(rows, (row) => get(row, key));
    if (!beforeRows.length) {
      return null;
    }
    const afterResult = get(minBy(beforeRows, (row) => dayjs(get(row, key)).unix()), key);
    return dayjs(afterResult).format('YYYY-MM-DD');
  };
  const reduceMaxTime = (key) => {
    const beforeRows = filter(rows, (row) => get(row, key));
    if (!beforeRows.length) {
      return null;
    }
    const afterResult = get(maxBy(beforeRows, (row) => dayjs(get(row, key)).unix()), key);
    return dayjs(afterResult).format('YYYY-MM-DD');
  };
  try {
    const planStart = reduceMinTime('beginTime');
    const planEnd = reduceMaxTime('endTime');
    const actureStart = reduceMinTime('actualBeginTime');
    const actureEnd = reduceMaxTime('actualEndTime');
    setFieldsValue({
      planStart,
      planEnd,
      actureStart,
      actureEnd,
    });
  } catch (e) {}
}

defineExpose({
  async onSubmit() {
    const formValues = await validate();
    const formBody = {
      ...formValues,
      rspUserId: get(formValues, 'rspUserId.0.id'),
      rspUserName: get(formValues, 'rspUserId.0.name'),
      jobIdList: map(selectedRows.value, (row) => row.id),
      repairRound: get(props, 'record.repairRound'),
      id: get(props, 'record.id'),
    };
    return new Promise((resolve, reject) => {
      new Api('/pms/importantProject')
        .fetch(formBody, props?.record?.id ? 'edit' : 'add', props?.record?.id ? 'PUT' : 'POST')
        .then(() => {
          resolve('');
        }).catch((err) => {
          reject(err);
        });
    });
  },
});
onMounted(() => {
  get(props, 'record.id') && getMajorProjectInfo();
});
</script>

<template>
  <div class="major-project-form">
    <BasicForm
      v-loading="loading"
      @register="register"
    />
    <div class="basic-card-table">
      <BasicCard :isBorder="false">
        <template #title>
          <div class="table-title">
            <h2>重大项目信息</h2>
            <span>（选中作业数 {{ selectWorkSum }}）</span>
          </div>
        </template>
        <div class="form-table">
          <OrionTable
            ref="tableRef"
            class="radio-button-table"
            :options="tableOptions"
          />
        </div>
      </BasicCard>
    </div>
  </div>
</template>

<style scoped lang="less">
.major-project-form{
  :deep(.ant-basic-form){
    margin-bottom: 0 !important;
  }
  .basic-card-table{
    :deep(.basic-card-wrap){
      margin: 0 15px !important;
    }
  }
}
.table-title{
  display: flex;
  align-items: baseline;
  color: #000000d9;
  font-size: 12px;
  h2{
    padding-bottom: 0;
    margin-bottom: 0;
    font-size: 16px;
    font-weight: bold;
    font-variant: tabular-nums;
    line-height: 1.5715;
  }
}
.form-table{
  height: 400px;
  overflow: hidden;
  :deep(.ant-basic-table){
    padding: 0 !important;
  }
}
</style>