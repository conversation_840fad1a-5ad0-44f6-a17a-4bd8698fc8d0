package com.chinasie.orion.config;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.CellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Component;

import java.util.List;


public class CustomCellWriteHandler implements CellWriteHandler {

    private List<Integer> redRows; // 要标红的行号列表

    public CustomCellWriteHandler(List<Integer> redRows) {
        this.redRows = redRows;
    }

//    @Override
//    public void afterCellDataConverted(CellWriteHandlerContext context){
//        Cell cell = context.getCell();
//        if (redRows.contains(cell.getRowIndex())) {
//            CellStyle cellStyle = cell.getSheet().getWorkbook().createCellStyle();
//            Font font = cell.getSheet().getWorkbook().createFont();
//            font.setColor(IndexedColors.RED.getIndex());
//            cellStyle.setFont(font);
//            cell.setCellStyle(cellStyle);
//        }
//    }

    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                 List<WriteCellData<?>> cellDataList, Cell cell, Head head,
                                 Integer relativeRowIndex, Boolean isHead) {
        // 如果当前行需要改变字体颜色
        if (redRows.contains(cell.getRowIndex())) {
            // 获取Workbook对象
            Workbook workbook = writeSheetHolder.getSheet().getWorkbook();
            // 创建CellStyle
            CellStyle cellStyle = workbook.createCellStyle();
            // 创建Font
            Font font = workbook.createFont();
            // 设置字体颜色为红色
            font.setColor(IndexedColors.RED.getIndex());
            // 将Font应用到CellStyle
            cellStyle.setFont(font);
            // 将CellStyle应用到单元格
            cell.setCellStyle(cellStyle);
        }
    }
}
