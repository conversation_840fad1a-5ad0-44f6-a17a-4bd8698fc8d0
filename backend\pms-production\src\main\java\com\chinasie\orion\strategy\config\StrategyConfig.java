package com.chinasie.orion.strategy.config;

import com.chinasie.orion.domain.vo.PersonTmpVO;
import com.chinasie.orion.strategy.StrategyService;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class StrategyConfig implements InitializingBean {

    @Resource
    ApplicationContext applicationContext;


    private final Map<String, StrategyService<List<String>, List<PersonTmpVO>>> context = new HashMap<>();


    /**
     * 通过标记获取策略对象
     * @param mark 标记
     * @return 策略对象
     */
    public StrategyService<List<String>, List<PersonTmpVO>> getStrategy(String mark) {
        return context.get(mark);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Map<String, StrategyService> beansOfType = applicationContext.getBeansOfType(StrategyService.class);
        //将策略bean放到容器中
        beansOfType.forEach((k, v) -> context.put(v.getMark(), v));
    }


    /**
     * 通过钩子函数将策略类注入到策略容器中
     * @param args 参数
     * @throws Exception 异常
     */
//    @Override
//    public void run(String... args) throws Exception {
//        Map<String, StrategyService> beansOfType = applicationContext.getBeansOfType(StrategyService.class);
//        //将策略bean放到容器中
//        beansOfType.forEach((k, v) -> context.put(v.getMark(), v));
//    }
}
