<script setup lang="ts">
import { Spin } from 'ant-design-vue';
import { isPower, Layout3, Layout3Content } from 'lyra-component-vue3';
import {
  computed, provide, ref, Ref, unref, watchEffect,
} from 'vue';
import { useRoute } from 'vue-router';
import BasicInfo from '../components/BasicInfo.vue';
import Api from '/@/api';

interface MenuItem {
  id: string,
  name: string,
  children?: MenuItem[]
}

const basicInfo = ref({});
const loading: Ref<boolean> = ref(false);
const layoutData = computed(() => ({
  name: basicInfo.value?.projectName ?? '--',
  ownerName: basicInfo.value?.promoter ?? '--',
  projectCode: `申请单编码：${basicInfo.value?.purchReqDocCode}` ?? '--',
}));
const defaultActionId: Ref<string> = ref('jBXX');
const route = useRoute();
const projectId: Ref<string> = ref(route.params.id as string);
const powerData = ref();

const menuData: Ref<any[]> = computed(() => [
  {
    id: 'jBXX',
    name: '基本信息',
    code: 'PMS_CGXMSSXQ_container_01_01',
  },
].filter((item) => isPower(item.code, powerData.value)));

provide('projectOngoingItem', basicInfo);

const getDetail = async () => {
  try {
    const result = await new Api('/pms/ncfPurchProjectImplementation').fetch({
      pageCode: 'PMS_CGXMSS_container_01',
    }, unref(projectId), 'GET');
    basicInfo.value = result;
  } catch (e) {

  }
};
const getPowerDataHandle = async (data: any) => {
  powerData.value = data;
};
function menuChange(option: { id: string, index: number, item: MenuItem }): void {
  defaultActionId.value = option.id;
}

watchEffect(async () => {
  // getPowerDataHandle();
  await getDetail();
});
</script>

<template>
  <Layout3
    v-get-power="{pageCode: 'projectOngoingItem',getPowerDataHandle}"
    :projectData="layoutData"
    class="purchase-manage-layout"
    :menuData="menuData"
    :defaultActionId="defaultActionId"
    :type="2"
    :onMenuChange="menuChange"
  >
    <div
      v-if="loading"
      class="w-full h-full flex flex-pc flex-ac"
    >
      <Spin />
    </div>
    <Layout3Content v-else>
      <BasicInfo />
    </Layout3Content>
  </Layout3>
</template>

<style scoped lang="less">
:deep(.header-wrap) {
  min-height: 60px;
  height: auto;
  .header-main{
    min-height: 33px;
    height: auto;
  }
  .project-title {
    width: auto !important;
    max-width: 700px !important;
    min-width: 300px;
    .flex-te{
      word-wrap: break-word;
      overflow-wrap: break-word;
      white-space: normal;
      line-height: 26px;
      padding-top: 10px;
    }
  }
}
.purchase-manage-layout{
  :deep(.ant-menu-overflow){
    min-width: 200px;
  }
}
</style>