import { h } from 'vue';
import dayjs from 'dayjs';
import { DataStatusTag } from 'lyra-component-vue3';

function getTableColumns({ router }) {
  return [
    {
      title: '编码',
      dataIndex: 'number',
      align: 'left',
      minWidth: 200,
      customRender: ({ record, text }) => h('span', {
        class: 'action-btn',
        onClick: async () => {
          await router.push({
            name: 'IcmManagementDetailsIndex',
            params: {
              id: record?.id,
            },
          });
        },
      }, text),
    },
    {
      title: '接口类型',
      align: 'left',
      dataIndex: 'typeName',
    },
    {
      title: '接口状态',
      align: 'left',
      dataIndex: 'interfaceState',
    },
    {
      title: '发布方',
      align: 'left',
      dataIndex: 'publishDeptName',
    },
    {
      title: '接收方',
      align: 'left',
      dataIndex: 'reviewDeptNames',
    },
    {
      title: '接口描述',
      align: 'left',
      dataIndex: 'desc',
      minWidth: 250,
    },
    {
      title: '要求回复时间',
      align: 'left',
      dataIndex: 'replyTime',
      customRender: ({ text }) => (text ? h('span', {}, dayjs(text).format('YYYY-MM-DD')) : ''),
    },
    {
      title: '主办人',
      align: 'left',
      dataIndex: 'manUserName',
    },
    {
      title: '协办人',
      align: 'left',
      dataIndex: 'cooperationUserNames',
    },
    {
      title: '当前责任方',
      align: 'left',
      dataIndex: 'nowRspDeptName',
    },
    {
      title: '回复单据编码',
      align: 'left',
      dataIndex: 'ideaFormNumber',
    },
    {
      title: '回复单据状态',
      align: 'left',
      dataIndex: 'replySuggest',
    },
    {
      title: '意见单最新回复时间',
      align: 'left',
      dataIndex: 'newReplyTime',
      customRender: ({ text }) => (text ? h('span', {}, dayjs(text).format('YYYY-MM-DD')) : ''),
    },
    {
      title: '接口单流程节点',
      align: 'left',
      dataIndex: 'nowFlowNode',
    },
    {
      title: '接口备注',
      align: 'left',
      dataIndex: 'remark',
    },
    {
      title: '专业代码',
      align: 'left',
      dataIndex: 'specialtyCode',
    },
    {
      title: '第三方审查/备案',
      align: 'left',
      dataIndex: 'thirdVerifyName',
    },
    {
      title: '修改审批状态',
      align: 'left',
      dataIndex: 'dataStatus',
      customRender: ({ record }) => h(DataStatusTag, { statusData: record?.dataStatus }),
    },
    {
      title: '操作',
      fixed: 'right',
      align: 'left',
      dataIndex: 'action',
      slots: { customRender: 'action' },
    },
  ];
}

export {
  getTableColumns,
};
