<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>io.dropwizard.metrics</groupId>
    <artifactId>metrics-parent</artifactId>
    <version>4.2.13</version>
    <packaging>pom</packaging>
    <name>Metrics Parent</name>
    <description>
        The Metrics library.
    </description>
    <url>https://metrics.dropwizard.io</url>

    <modules>
        <module>docs</module>
        <module>metrics-bom</module>
        <module>metrics-annotation</module>
        <module>metrics-benchmarks</module>
        <module>metrics-caffeine</module>
        <module>metrics-caffeine3</module>
        <module>metrics-core</module>
        <module>metrics-collectd</module>
        <module>metrics-ehcache</module>
        <module>metrics-graphite</module>
        <module>metrics-healthchecks</module>
        <module>metrics-httpclient</module>
        <module>metrics-httpclient5</module>
        <module>metrics-httpasyncclient</module>
        <module>metrics-jakarta-servlet</module>
        <module>metrics-jakarta-servlets</module>
        <module>metrics-jcache</module>
        <module>metrics-jcstress</module>
        <module>metrics-jdbi</module>
        <module>metrics-jdbi3</module>
        <module>metrics-jersey2</module>
        <module>metrics-jersey3</module>
        <module>metrics-jetty9</module>
        <module>metrics-jetty10</module>
        <module>metrics-jetty11</module>
        <module>metrics-jmx</module>
        <module>metrics-json</module>
        <module>metrics-jvm</module>
        <module>metrics-log4j2</module>
        <module>metrics-logback</module>
        <module>metrics-logback13</module>
        <module>metrics-logback14</module>
        <module>metrics-servlet</module>
        <module>metrics-servlets</module>
    </modules>

    <properties>
        <project.build.outputTimestamp>2011-02-24T06:12:03Z</project.build.outputTimestamp>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <jackson.version>2.12.3</jackson.version>
        <jetty9.version>9.4.49.v20220914</jetty9.version>
        <jetty10.version>10.0.12</jetty10.version>
        <jetty11.version>11.0.12</jetty11.version>
        <slf4j.version>1.7.36</slf4j.version>
        <assertj.version>3.23.1</assertj.version>
        <mockito.version>4.6.1</mockito.version>
        <junit.version>4.13.1</junit.version>
        <hamcrest.version>1.3</hamcrest.version>
        <maven-compiler-plugin.version>3.10.1</maven-compiler-plugin.version>
        <errorprone.version>2.16</errorprone.version>
        <errorprone.javac.version>9+181-r4173-1</errorprone.javac.version>

        <sonar.projectKey>dropwizard_metrics</sonar.projectKey>
        <sonar.organization>dropwizard</sonar.organization>
        <sonar.host.url>https://sonarcloud.io</sonar.host.url>
        <sonar.moduleKey>${project.artifactId}</sonar.moduleKey>
    </properties>

    <developers>
        <developer>
            <name>Coda Hale</name>
            <email><EMAIL></email>
            <timezone>America/Los_Angeles</timezone>
            <roles>
                <role>architect</role>
            </roles>
        </developer>
        <developer>
            <name>Ryan Tenney</name>
            <email><EMAIL></email>
            <timezone>America/New_York</timezone>
            <roles>
                <role>committer</role>
            </roles>
        </developer>
        <developer>
            <name>Artem Prigoda</name>
            <email><EMAIL></email>
            <timezone>Europe/Berlin</timezone>
            <roles>
                <role>committer</role>
            </roles>
        </developer>
    </developers>

    <licenses>
        <license>
            <name>Apache License 2.0</name>
            <url>https://www.apache.org/licenses/LICENSE-2.0.html</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <scm>
        <connection>scm:git:git://github.com/dropwizard/metrics.git</connection>
        <developerConnection>scm:git:**************:dropwizard/metrics.git</developerConnection>
        <url>https://github.com/dropwizard/metrics/</url>
        <tag>v4.2.13</tag>
    </scm>

    <issueManagement>
        <system>github</system>
        <url>https://github.com/dropwizard/metrics/issues/</url>
    </issueManagement>

    <distributionManagement>
        <snapshotRepository>
            <id>ossrh</id>
            <name>Sonatype Nexus Snapshots</name>
            <url>https://s01.oss.sonatype.org/content/repositories/snapshots</url>
        </snapshotRepository>
        <repository>
            <id>ossrh</id>
            <name>Nexus Release Repository</name>
            <url>https://s01.oss.sonatype.org/service/local/staging/deploy/maven2/</url>
        </repository>
    </distributionManagement>

    <profiles>
        <profile>
            <id>jdk8</id>
            <activation>
                <jdk>1.8</jdk>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-compiler-plugin</artifactId>
                        <version>${maven-compiler-plugin.version}</version>
                        <configuration>
                            <fork>true</fork>
                            <compilerArgs combine.children="append">
                                <arg>-J-Xbootclasspath/p:${settings.localRepository}/com/google/errorprone/javac/${errorprone.javac.version}/javac-${errorprone.javac.version}.jar</arg>
                            </compilerArgs>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>jdk17</id>
            <activation>
                <jdk>[17,)</jdk>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-compiler-plugin</artifactId>
                        <version>${maven-compiler-plugin.version}</version>
                        <configuration>
                            <fork>true</fork>
                            <compilerArgs>
                                <arg>-Xlint:all</arg>
                                <arg>-XDcompilePolicy=simple</arg>
                                <arg>-Xplugin:ErrorProne -XepExcludedPaths:.*/target/generated-sources/.*</arg>
                                <arg>-J--add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED</arg>
                                <arg>-J--add-exports=jdk.compiler/com.sun.tools.javac.file=ALL-UNNAMED</arg>
                                <arg>-J--add-exports=jdk.compiler/com.sun.tools.javac.main=ALL-UNNAMED</arg>
                                <arg>-J--add-exports=jdk.compiler/com.sun.tools.javac.model=ALL-UNNAMED</arg>
                                <arg>-J--add-exports=jdk.compiler/com.sun.tools.javac.parser=ALL-UNNAMED</arg>
                                <arg>-J--add-exports=jdk.compiler/com.sun.tools.javac.processing=ALL-UNNAMED</arg>
                                <arg>-J--add-exports=jdk.compiler/com.sun.tools.javac.tree=ALL-UNNAMED</arg>
                                <arg>-J--add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED</arg>
                                <arg>-J--add-opens=jdk.compiler/com.sun.tools.javac.code=ALL-UNNAMED</arg>
                                <arg>-J--add-opens=jdk.compiler/com.sun.tools.javac.comp=ALL-UNNAMED</arg>
                            </compilerArgs>
                            <annotationProcessorPaths>
                                <path>
                                    <groupId>com.google.errorprone</groupId>
                                    <artifactId>error_prone_core</artifactId>
                                    <version>${errorprone.version}</version>
                                </path>
                            </annotationProcessorPaths>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>release-sign-artifacts</id>
            <activation>
                <property>
                    <name>performRelease</name>
                    <value>true</value>
                </property>
            </activation>
            <properties>
                <gpg.keyname>EDA86E9FB607B5FC9223FB767D4868B53E31E7AD</gpg.keyname>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-source-plugin</artifactId>
                        <version>3.2.1</version>
                        <executions>
                            <execution>
                                <id>attach-sources</id>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <version>3.4.1</version>
                        <configuration>
                            <source>8</source>
                            <doclint>none</doclint>
                            <quiet>true</quiet>
                            <notimestamp>true</notimestamp>
                        </configuration>
                        <executions>
                            <execution>
                                <id>attach-javadocs</id>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>3.0.1</version>
                        <configuration>
                            <gpgArguments>
                                <arg>--pinentry-mode</arg>
                                <arg>loopback</arg>
                            </gpgArguments>
                        </configuration>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.sonatype.plugins</groupId>
                        <artifactId>nexus-staging-maven-plugin</artifactId>
                        <version>1.6.13</version>
                        <configuration>
                            <serverId>ossrh</serverId>
                            <nexusUrl>https://s01.oss.sonatype.org/</nexusUrl>
                            <autoReleaseAfterClose>true</autoReleaseAfterClose>
                        </configuration>
                        <executions>
                            <execution>
                                <id>nexus-deploy</id>
                                <phase>deploy</phase>
                                <goals>
                                    <goal>deploy</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.cyclonedx</groupId>
                        <artifactId>cyclonedx-maven-plugin</artifactId>
                        <version>2.7.3</version>
                        <executions>
                            <execution>
                                <phase>package</phase>
                                <goals>
                                    <goal>makeAggregateBom</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <showWarnings>true</showWarnings>
                    <compilerArgs>
                        <arg>-Xlint:all</arg>
                        <arg>-XDcompilePolicy=simple</arg>
                        <arg>-Xplugin:ErrorProne -XepExcludedPaths:.*/target/generated-sources/.*</arg>
                    </compilerArgs>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>com.google.errorprone</groupId>
                            <artifactId>error_prone_core</artifactId>
                            <version>${errorprone.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>5.1.8</version>
                <extensions>true</extensions>
                <configuration>
                    <instructions>
                        <Import-Package><![CDATA[
                            javax.servlet*;version="[2.5.0,4.0.0)",
                            org.slf4j*;version="[1.6.0,2.0.0)",
                            sun.misc.*;resolution:=optional,
                            com.sun.management.*;resolution:=optional,
                            *
                        ]]>
                        </Import-Package>
                        <Automatic-Module-Name>${javaModuleName}</Automatic-Module-Name>
                    </instructions>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <configuration>
                    <argLine>@{argLine} -Djava.net.preferIPv4Stack=true</argLine>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>1.4.1</version>
                <executions>
                    <execution>
                        <id>enforce</id>
                        <configuration>
                            <rules>
                                <DependencyConvergence />
                            </rules>
                        </configuration>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>3.3.0</version>
                <executions>
                    <execution>
                        <id>analyze</id>
                        <goals>
                            <goal>analyze-only</goal>
                            <goal>analyze-dep-mgt</goal>
                            <goal>analyze-duplicate</goal>
                        </goals>
                        <phase>verify</phase>
                        <configuration>
                            <failOnWarning>true</failOnWarning>
                            <ignoreNonCompile>true</ignoreNonCompile>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>2.5.3</version>
                <configuration>
                    <autoVersionSubmodules>true</autoVersionSubmodules>
                    <mavenExecutorId>forked-path</mavenExecutorId>
                    <tagNameFormat>v@{project.version}</tagNameFormat>
                    <preparationGoals>clean test</preparationGoals>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                    <archive>
                        <manifest>
                            <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                        </manifest>
                        <manifestEntries>
                            <Automatic-Module-Name>${javaModuleName}</Automatic-Module-Name>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>3.2.0</version>
                <configuration>
                    <configLocation>checkstyle.xml</configLocation>
                    <includeTestSourceDirectory>true</includeTestSourceDirectory>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-site-plugin</artifactId>
                <version>3.12.1</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-project-info-reports-plugin</artifactId>
                <version>3.4.1</version>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.8</version>
                <executions>
                    <execution>
                        <id>prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
