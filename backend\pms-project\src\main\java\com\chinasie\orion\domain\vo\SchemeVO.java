package com.chinasie.orion.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Scheme Entity对象
 *
 * <AUTHOR>
 * @since 2023-03-30 11:01:08
 */
@ApiModel(value = "SchemeVO对象", description = "综合计划")
@Data
public class SchemeVO extends ObjectVO implements Serializable {
    /**
     * 计划来源
     */
    @ApiModelProperty(value = "计划来源")
    private String planSourceId;


    /**
     * 图标icon
     */
    @ApiModelProperty(value = "图标icon")
    private String icon;
    /**
     * 计划来源名称
     */
    @ApiModelProperty(value = "计划来源名称")
    private String planSourceName;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    /**
     * 主题
     */
    @ApiModelProperty(value = "主题")
    private String name;

    /**
     * 来源摘要*
     */
    @ApiModelProperty(value = "来源摘要*")
    private String sourceSummary;

    /**
     * 来源摘要*
     */
    @ApiModelProperty(value = "来源摘要*")
    private String mergeSourceSummary;

    /**
     * 节点类型
     */
    @ApiModelProperty(value = "节点类型")
    private String nodeType;

    /**
     * 节点类型名称
     */
    @ApiModelProperty(value = "节点类型名称")
    private String nodeTypeName;

    /**
     * 节点层级
     */
    @ApiModelProperty(value = "节点层级")
    private String nodeLevel;

    /**
     * 节点深度
     */
    @ApiModelProperty(value = "节点深度")
    private Integer deepNode;

    /**
     * 计划级别
     */
    @ApiModelProperty(value = "计划级别")
    private String level;


    /**
     * 计划层级
     */
    @ApiModelProperty(value = "计划层级")
    private String orderLevel;


    /**
     * 计划级别名称
     */
    @ApiModelProperty(value = "计划级别名称")
    private String levelName;

    /**
     * 是否需要督办领导/关闭领导*
     */
    @ApiModelProperty(value = "是否需要督办领导/关闭领导*")
    private Boolean urgeLeaderFlag;

    /**
     * 督办领导/关闭领导*
     */
    @ApiModelProperty(value = "督办领导/关闭领导*")
    private String urgeLeader;

    /**
     * 督办领导/关闭领导名称*
     */
    @ApiModelProperty(value = "督办领导/关闭领导名称*")
    private String urgeLeaderName;


    /**
     * 是否需要签批领导*
     */
    @ApiModelProperty(value = "是否需要签批领导*")
    private Boolean signLeaderFlag;

    /**
     * 签批领导*
     */
    @ApiModelProperty(value = "签批领导*")
    private String signLeader;


    /**
     * 签批领导名称*
     */
    @ApiModelProperty(value = "签批领导名称*")
    private String signLeaderName;


    /**
     * 责任部门
     */
    @ApiModelProperty(value = "责任部门")
    private String rspDept;

    /**
     * 责任部门名称
     */
    @ApiModelProperty(value = "责任部门名称")
    private String rspDeptName;

    /**
     * 责任科室
     */
    @ApiModelProperty(value = "责任科室")
    private String rspSubDept;

    /**
     * 责任科室名称
     */
    @ApiModelProperty(value = "责任科室名称")
    private String rspSubDeptName;


    /**
     * 责任班组
     */
    @ApiModelProperty(value = "责任班组")
    private String rspTeam;

    /**
     * 责任班组名称
     */
    @ApiModelProperty(value = "责任班组名称")
    private String rspTeamName;


    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    private Date beginTime;

    /**
     * 分配日期
     */
    @ApiModelProperty(value = "被分配方")
    private String assignName;


    /**
     * 分配日期
     */
    @ApiModelProperty(value = "分配日期")
    private Date assignTime;
    /**
     * 计划负责人
     */
    @ApiModelProperty(value = "计划负责人")
    private String rspUser;

    /**
     * 计划负责人名称
     */
    @ApiModelProperty(value = "计划负责人名称")
    private String rspUserName;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    /**
     * 计划属性*
     */
    @ApiModelProperty(value = "计划属性*")
    private List<String> attributes;

    /**
     * 计划属性名称*
     */
    @ApiModelProperty(value = "计划属性名称*")
    private List<String> attributeNames;

    /**
     * 反馈周期
     */
    @ApiModelProperty(value = "反馈周期")
    private String feedback;

    /**
     * 反馈周期名称
     */
    @ApiModelProperty(value = "反馈周期名称")
    private String feedbackName;

    /**
     * 反馈状态
     */
    @ApiModelProperty(value = "反馈状态")
    private DataStatusVO feedbackStatus;

    /**
     * 任务目标
     */
    @ApiModelProperty(value = "任务目标")
    private String taskGoal;

    /**
     * 衡量标准
     */
    @ApiModelProperty(value = "衡量标准")
    private String metrics;

    /**
     * 是否已有预算*
     */
    @ApiModelProperty(value = "是否已有预算*")
    private Boolean budgetHadFlag;


    /**
     * 预算额度申请汇总*
     */
    @ApiModelProperty(value = "预算额度申请汇总*")
    private String applyTotal;


    /**
     * 预估计划金额*
     */
    @ApiModelProperty(value = "预估计划金额*")
    private String estimateAmount;


    /**
     * 预估金额*
     */
    @ApiModelProperty(value = "预估金额*")
    private Boolean estimateAmountFlag;

    /**
     * 计划类型（1 日常计划）
     */
    @ApiModelProperty(value = "计划类型（1 日常计划）")
    private Integer planType;

    /**
     * 是否需要预算*
     */
    @ApiModelProperty(value = "是否需要预算*")
    private Boolean budgetNeedFlag;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 父级
     */
    @ApiModelProperty(value = "父级")
    private String parentId;

    /**
     * 子级
     */
    @ApiModelProperty(value = "子级")
    List<SchemeVO> children;


    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    private List<FileVO> attachments;

    /**
     * 根ID
     */
    @ApiModelProperty(value = "根ID")
    private String rootId;

    /**
     * 是否计划分解新增
     */
    @ApiModelProperty(value = "是否计划分解新增")
    private Boolean wbsFlag;

    /**
     * 变更次数
     */
    @ApiModelProperty(value = "变更次数")
    private Long changeCount;

    /**
     * 实际开始时间
     */
    @ApiModelProperty(value = "实际开始时间")
    private Date actualBeginTime;

    /**
     * 实际结束时间
     */
    @ApiModelProperty(value = "实际结束时间")
    private Date actualEndTime;


    /**
     * 完成情况说明
     */
    @ApiModelProperty(value = "完成情况说明")
    private String completionDesc;


    /**
     * 关闭申请ID
     */
    @ApiModelProperty(value = "关闭申请ID")
    private String closeApplyId;

    /**
     * 关闭申请说明
     */
    @ApiModelProperty(value = "关闭申请说明")
    private String closeApplyDesc;

    /**
     * 实际关闭时间
     */
    @ApiModelProperty(value = "实际关闭时间")
    private Date actualCloseApplyTime;
    /**
     * 录入批准时间
     */
    @ApiModelProperty(value = "录入批准时间")
    private Date recordTime;

    /**
     * 需要重新下发流程
     */
    @ApiModelProperty(value = "需要重新下发流程")
    private Boolean repeatFlag;

    /**
     * 是否例行 0-否 1-是
     */
    @ApiModelProperty(value = "是否例行 0-否 1-是")
    private Integer routine;


    @ApiModelProperty(value = "原计划完成时间")
    @ExcelProperty(value = "原计划完成时间", index = 5)
    private String oldEndTime;


    @ApiModelProperty(value = "变更原因")
    @ExcelProperty(value = "变更原因", index = 8)
    private String reason;


    /**
     * 维度类型
     */
    @ApiModelProperty(value = "维度类型")
    private Integer dimensionType;


    /**
     * 计划管理部门
     */
    @ApiModelProperty(value = "计划管理部门")
    private String manOrg;

    /**
     * 计划管理部门名称
     */
    @ApiModelProperty(value = "计划管理部门名称")
    private String manOrgName;

    /**
     * 计划管理部门代码
     */
    @ApiModelProperty(value = "计划管理部门代码")
    private String manOrgCode;

    @ApiModelProperty(value = "实施时间")
    private Date executeTime;

    @ApiModelProperty(value = "变更时间")
    private Date changeTime;

    public String getApplyTotal() {
        return "0";
    }
}
