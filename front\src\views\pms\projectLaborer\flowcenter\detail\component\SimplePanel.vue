<template>
  <div :class="[renderColorByStatus(), 'panel-container']">
    <div class="left">
      <span>审批人：</span>
      <span>审批状态：</span>
      <span>审批意见：</span>
      <span>处理时间：</span>
    </div>
    <div class="right">
      <span class="user-name">{{ name || '无' }}</span>
      <span>{{ status || '无' }}</span>
      <span>{{ comment || '无' }}</span>
      <span>{{ endTime || '无' }}</span>
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue';
export default defineComponent({
  name: 'Simplepanel',
  components: {},
  props: {
    name: {
      type: String,
    },
    status: {
      type: String,
    },
    comment: {
      type: String,
    },
    endTime: {
      type: String,
    },
    statusCode: {
      type: String,
    },
  },
  setup(prop) {
    return {
      renderColorByStatus() {
        switch (prop.statusCode) {
          case 'NOT_STARTED':
            return 'not-start';
          case 'CANCELLED':
            return 'cancel';
          case 'COMPLETED':
            return 'complete';
          default:
            return 'not-start';
        }
      },
    };
  },
});
</script>
<style lang="less" scoped>
.panel-container {
  display: flex;
  flex-direction: row;
  padding: 10px 30px 20px 20px;
  border: 1px solid rgb(233, 238, 241);
  width: 20vw;
  margin-left: 20px;
  margin-top: 10px;
  border-radius: 6px;
  border-left: solid 6px #55d187;

  .left {
    display: flex;
    flex-direction: column;
    text-align: end;
    white-space: nowrap;

    span {
      margin-top: 10px;
    }
  }

  .right {
    display: flex;
    flex-direction: column;

    span {
      margin-top: 10px;
    }

    .user-name {
      display: inline-block;
      width: 150px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      -webkit-line-clamp: 1;
    }
  }
}

.not-start {
  border-left: solid 6px #acb2bf;
}

.cancel {
  border-left: solid 6px #ec8200;
}
.complete {
  border-left: solid 6px #46ba7b;
}
</style>
