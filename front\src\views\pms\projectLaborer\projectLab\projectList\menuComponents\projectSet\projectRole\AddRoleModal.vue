<!--模态框组件 AddRoleModal-->
<template>
  <BasicModal
    title="添加项目角色"
    :width="1000"
    @register="modalRegister"
    @visibleChange="visibleChange"
    @ok="okBasicModal"
  >
    <!--每次打开，根据visibleStatus来重置业务组件-->
    <AddRoleComponent
      v-if="state.visibleStatus"
      :projectId="state.projectId"
      @change="selectionChange"
    />
  </BasicModal>
</template>

<script setup lang="ts">
import { reactive } from 'vue';
import { BasicModal, useModalInner } from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import AddRoleComponent from './AddRoleComponent.vue';
import { addAllRoleApi } from '/@/views/pms/projectLaborer/api/projectList';
const emit = defineEmits(['success']);
const state = reactive({
  projectId: '',
  visibleStatus: false,
  rowList: [],
});

const [modalRegister, { changeOkLoading, closeModal }] = useModalInner((openProps:{projectId: string}) => {
  // 接收，通过useModal的openModal方法打开时，传输过来的参数
  state.projectId = openProps.projectId;
  // 设置为已打开状态
  state.visibleStatus = true;
  state.rowList = [];
});

function visibleChange(visible: boolean) {
  // 窗口关闭时，设置状态值
  !visible && (state.visibleStatus = visible);
}

function selectionChange({ rows }) {
  state.rowList = rows.map((s) => ({
    businessId: s.id,
    name: s.name,
    projectId: state.projectId,
  }));
}

function okBasicModal() {
  if (state.rowList.length === 0) {
    return message.error('必须选择添加项目角色');
  }
  const love = {
    className: 'ProjectRole',
    moduleName: '项目管理-项目设置-项目角色',
    type: 'SAVE',
    remark: `新增了【${state.rowList.map((item) => item?.name)}】`,
  };
  changeOkLoading(true);
  addAllRoleApi(state.rowList, love)
    .then(() => {
      emit('success');
      message.success('添加成功');
      closeModal();
    })
    .catch(() => {
      // state.visible = false;
    }).finally(() => {
      changeOkLoading(false);
    });
}

</script>
