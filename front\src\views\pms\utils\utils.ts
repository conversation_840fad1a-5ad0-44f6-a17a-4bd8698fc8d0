import {
  isVNode, ref, Ref, h,
} from 'vue';
import {
  isPower, openDrawer, openModal, OpenModalProps,
} from 'lyra-component-vue3';
import {
  difference as _difference, isBoolean, isFunction, isNumber, isString, includes as _includes,
} from 'lodash-es';
import dayjs, { Dayjs } from 'dayjs';

// 格式化金额
export function formatMoney(value: number | string, precision: number = 1): string {
  // 第一个需要的数字，第二个是转换的单位
  let money = isNaN(Number(value || 0)) ? 0 : Number(value || 0)
    .toFixed(2);
  money = Number(Number(Number(money) / precision)
    .toFixed(2))
    .toLocaleString();

  if (money.includes('.')) {
    if (money.split('.')[1]?.toString()?.length === 1) {
      return `${money}0`;
    }
    return money;
  }
  return `${money}.00`;
}

// 求两个数的最大公约数
export function gcd(a, b) {
  if (b === 0) {
    return a;
  }
  return gcd(b, a % b);
}

// 格式化数字输入框输入金额
export function formatInputMoney(money: number | string) {
  money = money?.toString()?.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1') ?? '';
  let int = money.toString()
    .split('.')[0];
  let float = money.toString()
    .split('.')[1]?.slice(0, 2);
  money = [int, float]?.filter((item) => item)
    .join('.');
  return parseFloat(parseFloat(money).toFixed(2));
}

// 表单插入数据
export function appendFrom(fieldList, appendSchemaByField, field, formField = 'id') {
  fieldList.forEach((item, index) => {
    let options = [];
    let fieldItem: any = {
      field: item[formField],
      component: '',
      required: item.require === 1,
      label: item.name,
      colProps: {
        span: 12,
      },
    };
    if (item.type === '1') {
      fieldItem.component = 'Input';
      fieldItem.componentProps = {
        maxlength: 40,
        showCount: true,
      };
    } else {
      options = item.options.split(';').map((item1) => ({
        label: item1,
        value: item1,
      }));
      let componentProps: any = {
        options,
      };
      let rules = [
        {
          type: 'string',
          required: item.require === 1,
          message: `请选择${item.name}`,
          trigger: 'change',
        },
      ];
      if (item.type === '3') {
        componentProps.mode = 'multiple';
        rules[0].type = 'array';
        delete fieldItem.required;
        componentProps.rules = rules;
      }
      fieldItem.componentProps = componentProps;
      fieldItem.component = 'Select';
    }
    appendSchemaByField(
      fieldItem,
      field,
    );
  });
}

export function openContentModal(modalProps: OpenModalProps) {
  const modalRef: any = ref(null);
  openModal({
    ...modalProps,
    content: (h) => {
      let contentProps = {
        ref: modalRef,
      };
      if (isFunction(modalProps?.content)) {
        return h(modalProps.content(h), contentProps);
      }
      if (isVNode(modalProps?.content)) {
        return h(modalProps.content, contentProps);
      }
      return h('span', contentProps, modalProps.content);
    },
    async onOk() {
      const res = modalRef.value?.getData ? await modalRef.value?.getData() : {};
      if (isFunction(modalProps?.onOk)) {
        // @ts-ignore
        return modalProps.onOk(res, modalProps);
      }
    },
  });
}

export function openContentDrawer(modalProps: OpenModalProps) {
  const drawerRef: any = ref(null);
  openDrawer({
    ...modalProps,
    content: (h) => {
      let contentProps = {
        ref: drawerRef,
      };
      if (isFunction(modalProps?.content)) {
        return h(modalProps.content(h), contentProps);
      }
      if (isVNode(modalProps?.content)) {
        return h(modalProps.content, contentProps);
      }
      return h('span', contentProps, modalProps.content);
    },
    async onOk() {
      const res = drawerRef.value?.getData ? await drawerRef.value?.getData() : {};
      if (isFunction(modalProps?.onOk)) {
        // @ts-ignore
        return modalProps.onOk(res, modalProps);
      }
    },
  });
}

export function disabledStartDate(startDate: Dayjs, endDate: any) {
  return endDate ? dayjs(startDate).valueOf() >= dayjs(endDate).valueOf() : false;
}

export function disabledEndDate(endDate: Dayjs, startDate: any) {
  return startDate ? dayjs(endDate).valueOf() <= dayjs(startDate).valueOf() : false;
}

export function disabledStartDateEqual(startDate: Dayjs, endDate: any, otherDate: any) {
  return endDate ? dayjs(startDate).valueOf() > dayjs(endDate).valueOf() : false;
}

// 对象过滤指定属性
export function filterProperty(obj, prop) {
  const result = {};
  for (let key in obj) {
    // eslint-disable-next-line no-prototype-builtins
    if (obj.hasOwnProperty(key) && key !== prop) {
      result[key] = obj[key];
    }
  }
  return result;
}

export function parseTableHeaderButton(confStr, target) {
  const res = Object.keys(target || {}).filter((item) => target[item]);
  const fes = _difference(confStr.split('|'), res);
  return fes.join('|');
}

export function parsePriceByNumber(num) {
  if (!num || isNaN(Number(num))) {
    return '0.00';
  }
  let [integer, decimal = ''] = String(num).split('.');
  decimal = decimal
    .padEnd(2, '0');
  integer = integer.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  return [integer, decimal].join('.');
}
export function parseDecimalToPre(num) {
  if (!num || isNaN(Number(num))) {
    return '0%';
  }
  let [integer, decimal = ''] = String(num).split('.');
  if (`${decimal}`.length > 2) {
    return `${num.toFixed(2)}%`;
  }
  return `${num}%`;
}

export function parseBooleanToRender(val) {
  if (isBoolean(val)) {
    return val ? '是' : '否';
  }
  if (['0', '1'].includes(`${val}`)) {
    return `${val}` === '0' ? '否' : '是';
  }
  if (isString(val)) {
    return val.trim() ? val : '否';
  }
  return val ? '是' : '否';
}

// 过滤Layout3无权限数据
export function filterDataByPowerCode(data, powerData) {
  // 递归函数来处理每个节点
  function filterNode(node) {
    // 如果节点没有子节点，检查它自己的powerCode
    if (!node.children || node.children.length === 0) {
      return isPower(node.powerCode, powerData);
    }

    // 检查子节点，如果至少有一个子节点有效，则当前节点有效
    const hasValidChild = node.children.some((child) => filterNode(child));
    // 如果没有有效的子节点，则当前节点无效
    if (!hasValidChild) {
      return false;
    }

    // 如果当前节点有效，进一步过滤子节点
    node.children = node.children.filter((child) => filterNode(child));
    return true;
  }

  // 过滤顶层节点
  return data.filter((node) => filterNode(node)).map((node) => {
    // 如果子节点被过滤掉，返回当前节点但不包含空的子节点数组
    if (node.children && node.children.length === 0) {
      return {
        ...node,
        children: undefined,
      };
    }
    return node;
  });
}

export function batchIncludes(source:any, target:string|string[]) {
  target = Array.isArray(target) ? target : [target];

  return target.every((item:string) => _includes(source, item));
}

// 身份证校验格式
export function isValidIDCard(idCard: string): boolean {
  const pattern = /^[1-9]\d{5}(18|19|20|21|22)?\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|[Xx])$/;
  return pattern.test(idCard);
}

// 手机号格式校验
export function validatePhoneNumber(phone: number): boolean {
  const regex = /^1[3-9]\d{9}$/;
  return regex.test(String(phone));
}
export function openFormDrawerOrCenter(component: any, record?: Record<string, any>, cb?: () => void): void {
  const drawerRef: Ref = ref();
  const basicProps = {
    title: record?.title,
    width: record?.width ?? 1000,
    height: record?.height,
    ...(
      isBoolean(record?.footer) && !record?.footer
        ? {
          footer: record?.footer,
        }
        : {}),
    content() {
      return h(component, {
        ref: drawerRef,
        footer: isBoolean(record?.footer) ? record?.footer : {},
        customRequestHandler: record?.customRequestHandler,
        record,
      });
    },
    async onOk(): Promise<void> {
      const form = drawerRef.value;
      await form?.onSubmit?.();
      cb?.();
    },
  };
  if (record?.position === 'center') {
    openModal(basicProps);
  } else {
    openDrawer(basicProps);
  }
}
export function setBasicCard(list: any[]): any[] {
  list = list.map((item) => {
    switch (item.type) {
      case 'selectproject':
      case 'selectdept':
        item.valueRender = ({ record }) => record[`${item.field}Name`];
        break;
      case 'selectuser':
      case 'selectdict':
        item.valueRender = ({ record }) => record[`${item.field}Name`]?.map((v) => v.name)?.join('、') || '-';
        break;
    }
    if (item.formatter) {
      item.valueRender = ({ record }) => item.formatter(record[item.field], record);
    }
    item.wrap = true;
    return item;
  });
  return list;
}

/**
 * 下载 Excel 文件
 * @param fileDate 接口返回的文件数据
 * @param fileName 下载的文件名
 */
export const downloadExcel = (fileDate: any, fileName: string) =>
  // TODO fileDate 的ts类型应该是 Blob，但是我这里接口返回的数据类型是 res: AxiosResponse<any, any>，会导致提示类型不匹配，所以用any吧
  new Promise((resolve, reject) => {
    // 为 blob 设置文件类型，这里以 .xlsx 为例
    const blob = new Blob([fileDate], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.vnd.ms-excel;charset=utf-8',
    });

    // 创建一个临时的url指向blob对象
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    a.click();

    // 释放这个临时的对象url
    window.URL.revokeObjectURL(url);

    return resolve('导出成功');
  });
