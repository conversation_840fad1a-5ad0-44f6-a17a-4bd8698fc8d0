<template>
  <OrionTable :options="tableOptions" />
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue';
import { OrionTable } from 'lyra-component-vue3';
import Api from '/@/api';
import { formatMoney } from '/@/views/pms/utils/utils';
import { useRoute } from 'vue-router';
import dayjs from 'dayjs';

const route = useRoute();
const props = defineProps({});
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  showSmallSearch: false,
  rowSelection: false,
  showIndexColumn: true,
  smallSearchField: ['number'],
  api: (tableParams) => new Api('/pas/incomeContract/product/page').fetch({
    ...tableParams,
    query: { contractId: route.params?.id },
  }, '', 'POST'),
  columns: [
    {
      title: '产品型号',
      dataIndex: 'model',
    },
    {
      title: '产品二级分类',
      dataIndex: 'productSecondClassifyName',
    },
    {
      title: '合同签订产品编码',
      dataIndex: 'contrProductNumber',
    },
    {
      title: '基准目录产品编码',
      dataIndex: 'productNumber',
    },
    {
      title: '数量',
      dataIndex: 'num',
    },
    {
      title: '产品单价',
      dataIndex: 'price',
      customRender: ({ text }) => (text ? formatMoney(text) : ''),
    },
    {
      title: '产品金额',
      dataIndex: 'amount',
      customRender: ({ text }) => (text ? formatMoney(text) : ''),
    },
    {
      title: '是否在承制资格范围内',
      dataIndex: 'qualifiedScope',
      customRender: ({ text }) => (text ? '是' : '否'),
    },
    {
      title: '要求交付日期',
      dataIndex: 'requestDeliverTime',
      customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD') : ''),
    },
  ],
});
</script>
