package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

import java.util.List;
/**
 * EditLog VO对象
 *
 * <AUTHOR>
 * @since 2024-10-17 09:25:09
 */
@ApiModel(value = "EditLogVO对象", description = "调整记录表")
@Data
public class EditLogVO extends  ObjectVO   implements Serializable{

            /**
         * 计划id
         */
        @ApiModelProperty(value = "计划id")
        private String planId;


        /**
         * 中心名称
         */
        @ApiModelProperty(value = "中心名称")
        private String centerName;


        /**
         * 成本类型
         */
        @ApiModelProperty(value = "成本类型")
        private String costType;


        /**
         * 成本名称
         */
        @ApiModelProperty(value = "成本名称")
        private String costName;


        /**
         * 调整数量
         */
        @ApiModelProperty(value = "调整数量")
        private Integer num;


        /**
         * 调整人id
         */
        @ApiModelProperty(value = "调整人id")
        private String editPersonId;


        /**
         * 提交时间
         */
        @ApiModelProperty(value = "提交时间")
        private Date submitTime;


        /**
         * 审批时间
         */
        @ApiModelProperty(value = "审批时间")
        private Date assessmentTime;


        /**
         * 审批人id
         */
        @ApiModelProperty(value = "审批人id")
        private String assessmentPersonId;


        /**
         * 审批意见
         */
        @ApiModelProperty(value = "审批意见")
        private String assessmentAdvice;


        /**
         * 审批人姓名
         */
        @ApiModelProperty(value = "审批人姓名")
        private String assessmentPerson;


        /**
         * 调整人姓名
         */
        @ApiModelProperty(value = "调整人姓名")
        private String editPerson;


    

}
