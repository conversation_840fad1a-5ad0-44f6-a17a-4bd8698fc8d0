<script setup lang="ts">
import { BasicButton, BasicCard, OrionTable } from 'lyra-component-vue3';
import {
  h,
  inject, reactive, ref, Ref, unref,
} from 'vue';
import { useRoute } from 'vue-router';
import dayjs from 'dayjs';
import { isBoolean } from 'lodash-es';
import {
  openFormDrawer, parseBooleanToRender, parsePriceByNumber, setBasicInfo,
} from '../../../utils';
import Api from '/@/api';
import { BasicInjectionsKey } from '../../../tokens/basicKeys';
import purchaseContractForm2 from '../PurchaseContractForm2.vue';
import purchaseContractForm from '../PurchaseContractForm.vue';

const route = useRoute();
const projectId: Ref<string> = ref(route.params.id as string);
const basicInfo = inject(BasicInjectionsKey);
// 采购合同执行信息
const baseInfoProps = reactive({
  list: setBasicInfo([
    {
      label: '实际合同开始日期',
      field: 'actualStartTime',
      formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') || '' : '--'),
    },
    {
      label: '预计合同开始日期',
      field: 'estimatedStartTime',
      formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') || '' : '--'),
    },
    {
      label: '预计合同结束日期',
      field: 'estimatedEndTime',
      formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') || '' : '--'),
    },
    {
      label: '实际合同结束日期',
      field: 'actualEndTime',
      formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') || '' : '--'),
    },
    {
      label: '预计合同交付日期',
      field: 'estimatedDeliveryTime',
      formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') || '' : '--'),
    },
    {
      label: '合同履约状态',
      field: 'executionStatusName',
    },
    {
      label: '验收结果',
      field: 'acceptanceResults',
    },
    {
      label: '实际验收日期',
      field: 'actualAcceptanceTimes',
      formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') || '' : '--'),
    },
  ]),
  column: 3,
  dataSource: basicInfo.data,
});
// 合同变更信息
const tableOptions2 = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  isSpacing: true,
  scroll: {
    y: 200,
  },
  smallSearchField: undefined,
  columns: [
    {
      title: '变更编号',
      dataIndex: 'changeId',
    },
    {
      title: '变更标题',
      dataIndex: 'changeTitle',
    },
    {
      title: '变更类型',
      dataIndex: 'changeType',
    },
    {
      title: '变更申请日期',
      dataIndex: 'changeRequestDate',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '本次变更金额',
      dataIndex: 'thisChangeAmount',
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '累计变更金额',
      dataIndex: 'cumulativeChangeAmount',
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '累计变更比率',
      dataIndex: 'cumulativeChangeRate',
    },
    {
      title: '变更后合同承诺总价（总目标值）',
      dataIndex: 'contactAmountAfterChange',
    },
  ],
  api: (params:Record<string, any>) => new Api('/spm/contractChange/getByCode').fetch({
    ...params,
    query: {
      contractNumber: baseInfoProps.dataSource?.contractNumber,
    },
  }, '', 'POST'),
};
// 采购执行变更记录
const tableOptions3 = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  isSpacing: true,
  smallSearchField: undefined,
  scroll: {
    y: 200,
  },
  columns: [
    {
      title: '变更人',
      dataIndex: 'changer',
    },
    {
      title: '变更日期',
      dataIndex: 'changeDate',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '变更内容',
      dataIndex: 'changeContent',
    },
    {
      title: '变更前',
      dataIndex: 'beforeChange',
    },
    {
      title: '变更后',
      dataIndex: 'afterChange',
    },
  ],
  api: (params:Record<string, any>) => new Api('/spm/purchaseExecuteShcnge/getByCode').fetch({
    ...params,
    query: {
      contractNumber: baseInfoProps.dataSource?.contractNumber,
    },
  }, '', 'POST'),
};
// 合同终止信息
const tableOptions4 = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  isSpacing: true,
  scroll: {
    y: 200,
  },
  smallSearchField: undefined,
  columns: [
    {
      title: '是否签约前终止',
      dataIndex: 'isPreSignTermination',
      customRender({ text }) {
        return parseBooleanToRender(text);
      },
    },
    {
      title: '终止申请日期',
      dataIndex: 'terminationRequestDate',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '合同终止金额',
      dataIndex: 'contractTerminationAmount',
    },
  ],
  api: (params:Record<string, any>) => new Api('/spm/contractTermination/getByCode').fetch({
    ...params,
    query: {
      contractNumber: baseInfoProps.dataSource?.contractNumber,
    },
  }, '', 'POST'),
};
// 合同索赔信息
const tableOptions5 = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  isSpacing: true,
  smallSearchField: undefined,
  columns: [
    {
      title: '索赔编号',
      dataIndex: 'claimId',
    },
    {
      title: '索赔标题',
      dataIndex: 'claimTitle',
    },
    {
      title: '索赔状态',
      dataIndex: 'claimStatus',
    },
    {
      title: '索赔方向',
      dataIndex: 'claimDirection',
    },
    {
      title: '索赔申请时间',
      dataIndex: 'claimRequestTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '索赔处理时间',
      dataIndex: 'claimProcessTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '累计索赔金额（含本次）',
      dataIndex: 'cumulativeClaimAmount',
    },
    {
      title: '总累计索赔占原合同价%',
      dataIndex: 'totalClaimPctOfOrigPrice',
    },
  ],
  api: (params:Record<string, any>) => new Api('/spm/contractClaim/getByCode').fetch({
    ...params,
    query: {
      contractNumber: basicInfo?.data?.contractNumber,
    },
  }, '', 'POST'),
};
const titleVnode = h('div', {
  class: 'custom-btn-row',
}, [
  h('span', '采购合同执行信息'),
  h(BasicButton, {
    icon: 'sie-icon-bianji',
    type: 'primary',
    onClick() {
      openFormDrawer(purchaseContractForm, basicInfo.data, () => {

      });
    },
  }, '编辑'),
]);
</script>

<template>
  <!--  采购合同执行信息-->
  <BasicCard
    title="采购合同执行信息"
    :grid-content-props="baseInfoProps"
  />
  <!--  合同变更信息-->
  <BasicCard
    title="合同变更信息"
    :isBorder="false"
  >
    <div style="height: 360px;overflow: hidden;">
      <OrionTable
        ref="tableRef"
        class="scroll-table"
        :options="tableOptions2"
        false
      />
    </div>
  </BasicCard>
  <!--  合同索赔信息-->
  <BasicCard
    title="合同索赔信息"
    :isBorder="false"
  >
    <div style="height: 360px;overflow: hidden;">
      <OrionTable
        ref="tableRef"
        class="scroll-table"
        :options="tableOptions5"
        false
      />
    </div>
  </BasicCard>
  <!--  合同终止信息-->
  <BasicCard
    title="合同终止信息"
    :isBorder="false"
  >
    <div style="height: 360px;overflow: hidden;">
      <OrionTable
        ref="tableRef"
        class="scroll-table"
        :options="tableOptions4"
        false
      />
    </div>
  </BasicCard>
  <!--  采购执行变更记录-->
  <BasicCard
    title="采购执行变更记录"
    :isBorder="false"
  >
    <div style="height: 360px;overflow: hidden;">
      <OrionTable
        ref="tableRef"
        class="scroll-table"
        :options="tableOptions3"
        false
      />
    </div>
  </BasicCard>
</template>

<style scoped lang="less">
:deep(.custom-btn-row){
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.scroll-table {
  ::-webkit-scrollbar {
    height: 15px !important;
    width: 15px !important;
  }
  ::-webkit-scrollbar-thumb {
    border-radius:0;
  }
}
:deep(.ant-basic-table){
  padding: 0 !important;
}
</style>