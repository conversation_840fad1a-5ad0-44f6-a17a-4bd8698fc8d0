package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;

import java.util.List;
/**
 * JobAuthorizationinformations VO对象
 *
 * <AUTHOR>
 * @since 2024-06-05 10:41:49
 */
@ApiModel(value = "JobAuthorizeVO对象", description = "岗位授权信息")
@Data
public class JobAuthorizeVO extends  ObjectVO   implements Serializable{

            /**
         * 所属基地
         */
        @ApiModelProperty(value = "所属基地")
        private String basePlaceCode;


        /**
         * 作业岗位
         */
        @ApiModelProperty(value = "作业岗位")
        private String jobPositions;


        /**
         * 授权到期日期
         */
        @ApiModelProperty(value = "授权到期日期")
        private Date endDate;


        /**
         * 授权状态
         */
        @ApiModelProperty(value = "授权状态")
        private String authorizeStatus;


        /**
         * 是否等效
         */
        @ApiModelProperty(value = "是否等效")
        private String equivalentOrnot;


        /**
         * 等效认定基地
         */
        @ApiModelProperty(value = "等效认定基地")
        private String equivalentCertificationBase;


        /**
         * 授权记录
         */
        @ApiModelProperty(value = "授权记录")
        private String authorizationRecords;


        /**
         * 编码
         */
        @ApiModelProperty(value = "编码")
        private String number;


        /**
         * 主表ID
         */
        @ApiModelProperty(value = "主表ID")
        private String mainTableId;


    

}
