package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.domain.dto.ProjectRewardPunishmentDTO;
import com.chinasie.orion.domain.entity.ProjectRewardPunishment;
import com.chinasie.orion.domain.vo.ProjectRewardPunishmentVO;
import com.chinasie.orion.repository.ProjectRewardPunishmentMapper;
import com.chinasie.orion.service.ProjectRewardPunishmentService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import java.lang.String;
import java.util.*;
import com.chinasie.orion.cache.OrionJ2CacheService;
import java.util.stream.Collectors;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * ProjectRewardPunishment 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-27 19:32:36
 */
@Service
@Slf4j
public class ProjectRewardPunishmentServiceImpl extends  OrionBaseServiceImpl<ProjectRewardPunishmentMapper, ProjectRewardPunishment>   implements ProjectRewardPunishmentService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProjectRewardPunishmentVO detail(String id, String pageCode) throws Exception {
        ProjectRewardPunishment projectRewardPunishment =this.getById(id);
        ProjectRewardPunishmentVO result = BeanCopyUtils.convertTo(projectRewardPunishment,ProjectRewardPunishmentVO::new);

        return result;
    }

    /**
     * 新增编辑删除主要事迹
     * @param projectRewardPunishmentDTOList
     * @param projectId
     * @return
     * @throws Exception
     */
    @Override
    public Boolean saveOrRemove(List<ProjectRewardPunishmentDTO> projectRewardPunishmentDTOList, String projectId) throws Exception {
        List<String> existIdList = this.list(new LambdaQueryWrapperX<>(ProjectRewardPunishment.class)
                .select(ProjectRewardPunishment::getId)
                .eq(ProjectRewardPunishment::getProjectId, projectId))
                .stream().map(ProjectRewardPunishment::getId).collect(Collectors.toList());
        List<ProjectRewardPunishment> saveList = projectRewardPunishmentDTOList.stream()
                .filter(f -> StrUtil.isBlank(f.getId()))
                .map(m -> {
                    m.setProjectId(projectId);
                    return BeanCopyUtils.convertTo(m, ProjectRewardPunishment::new);
                }).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(saveList)) {
            this.saveBatch(saveList);
        }
        List<ProjectRewardPunishment> updateList = projectRewardPunishmentDTOList.stream()
                .filter(f -> StrUtil.isNotBlank(f.getId()))
                .map(m -> BeanCopyUtils.convertTo(m, ProjectRewardPunishment::new)).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(updateList)) {
            this.updateBatchById(updateList);
        }
        List<String> updateIdList = projectRewardPunishmentDTOList.stream()
                .filter(f -> StrUtil.isNotBlank(f.getId()))
                .map(ProjectRewardPunishmentDTO::getId).collect(Collectors.toList());;
        List<String> deleteIdList = existIdList.stream().filter(f -> !updateIdList.contains(f)).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(deleteIdList)) {
            this.removeByIds(deleteIdList);
        }
        return true;
    }

    /**
     * 获取主要事迹列表
     * @param projectId
     * @return
     * @throws Exception
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public List<ProjectRewardPunishmentVO> getList(String projectId) throws Exception {
        List<ProjectRewardPunishment> list = this.list(new LambdaQueryWrapperX<>(ProjectRewardPunishment.class)
                .eq(ProjectRewardPunishment::getProjectId, projectId)
                .orderByDesc(ProjectRewardPunishment::getCreateTime));
        return BeanCopyUtils.convertListTo(list, ProjectRewardPunishmentVO::new);
    }


}
