package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.IncomePlanDTO;
import com.chinasie.orion.domain.entity.IncomePlan;
import com.chinasie.orion.domain.vo.IncomePlanVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.List;


/**
 * <p>
 * IncomePlan 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29 19:14:00
 */
public interface IncomePlanService extends OrionBaseService<IncomePlan> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    IncomePlanVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param incomePlanDTO
     */
    String create(IncomePlanDTO incomePlanDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param incomePlanDTO
     */
    Boolean edit(IncomePlanDTO incomePlanDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<IncomePlanVO> pages(Page<IncomePlanDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<String> ids, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<IncomePlanVO> vos) throws Exception;

    Boolean pushData(String date) throws ParseException;

    Boolean adjustment(List<String> ids) throws Exception;

    Boolean authorized(List<String> ids) throws Exception ;

}
