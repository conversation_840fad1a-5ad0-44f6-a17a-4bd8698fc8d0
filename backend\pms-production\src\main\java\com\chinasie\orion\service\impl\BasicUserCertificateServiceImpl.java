package com.chinasie.orion.service.impl;

import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.domain.entity.BasicUserCertificate;
import com.chinasie.orion.domain.dto.BasicUserCertificateDTO;
import com.chinasie.orion.domain.entity.CertificateInfo;
import com.chinasie.orion.domain.vo.BasicUserCertificateVO;
import com.chinasie.orion.domain.vo.CertificateInfoVO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.file.api.service.FileApiService;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.service.BasicUserCertificateService;
import com.chinasie.orion.repository.BasicUserCertificateMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.CertificateInfoService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;


/**
 * <p>
 * BasicUserCertificate 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05 22:07:29
 */
@Service
@Slf4j
public class BasicUserCertificateServiceImpl extends OrionBaseServiceImpl<BasicUserCertificateMapper, BasicUserCertificate> implements BasicUserCertificateService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private FileApiService fileApiService;

    @Autowired
    private CertificateInfoService certificateInfoService;

    @Autowired
    private DictRedisHelper dictRedisHelper;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public BasicUserCertificateVO detail(String id, String pageCode) throws Exception {
        BasicUserCertificate basicUserCertificate = this.getById(id);
        BasicUserCertificateVO result = BeanCopyUtils.convertTo(basicUserCertificate, BasicUserCertificateVO::new);
        if(null == result){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "基础用户不存在");
        }
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param basicUserCertificateDTO
     */
    @Override
    public String create(BasicUserCertificateDTO basicUserCertificateDTO) throws Exception {
        BasicUserCertificate basicUserCertificate = BeanCopyUtils.convertTo(basicUserCertificateDTO, BasicUserCertificate::new);
        this.save(basicUserCertificate);

        String rsp = basicUserCertificate.getId();

        List<FileDTO> fileDTOList = basicUserCertificateDTO.getFileDTOList();
        if (!CollectionUtils.isEmpty(fileDTOList)) {
            fileDTOList.forEach(item -> {
                item.setDataId(rsp);
                item.setDataType("BasicUserCertificate");
            });
            fileApiService.batchSaveFile(fileDTOList);
        }
        return rsp;

    }

    /**
     * 编辑
     * <p>
     * * @param basicUserCertificateDTO
     */
    @Override
    public Boolean edit(BasicUserCertificateDTO basicUserCertificateDTO) throws Exception {
        BasicUserCertificate basicUserCertificate = BeanCopyUtils.convertTo(basicUserCertificateDTO, BasicUserCertificate::new);

        this.updateById(basicUserCertificate);

        String rsp = basicUserCertificate.getId();
        //编辑附件
        List<FileDTO> fileList = basicUserCertificateDTO.getFileDTOList();
        List<FileVO> existFileList = fileApiService.getFilesByDataId(basicUserCertificateDTO.getId());
        // 优先移除
        if (Objects.nonNull(existFileList)) {
            List<String> filesIds = existFileList.stream().map(FileVO::getId).collect(Collectors.toList());
            fileApiService.removeBatchByIds(filesIds);
        }
        // 批量新增
        if (!CollectionUtils.isEmpty(fileList)) {
            fileList.forEach(item -> {
                item.setId(null);
                item.setDataId(basicUserCertificateDTO.getId());
                item.setDataType("BasicUserCertificate");
            });
            fileApiService.batchSaveFile(fileList);
        }

        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        //todo   移除的时候需要处理判断 用户作业证书关系是否已经被使用 如果被使用不能删除
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<BasicUserCertificateVO> pages(Page<BasicUserCertificateDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<BasicUserCertificate> condition = new LambdaQueryWrapperX<>(BasicUserCertificate.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(BasicUserCertificate::getCreateTime);

        BasicUserCertificateDTO basicUserCertificateDTO =  pageRequest.getQuery();
        if(Objects.nonNull(basicUserCertificateDTO)){
            if(StringUtils.hasText(basicUserCertificateDTO.getUserCode())){
                condition.eq(BasicUserCertificate::getUserCode,basicUserCertificateDTO.getUserCode());
            }
        }
        Page<BasicUserCertificate> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), BasicUserCertificate::new));

        PageResult<BasicUserCertificate> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<BasicUserCertificateVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<BasicUserCertificateVO> vos = BeanCopyUtils.convertListTo(page.getContent(), BasicUserCertificateVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void setEveryName(List<BasicUserCertificateVO> vos) throws Exception {
        if(CollectionUtils.isEmpty(vos)){
            return;
        }
        List<String> dataIdList = new ArrayList<>();
        List<String> cIdList = new ArrayList<>();
        vos.forEach(vo -> {
            dataIdList.add(vo.getId());
            if(StringUtils.hasText(vo.getCertificateId())){
                cIdList.add(vo.getCertificateId());
            }
        });

        Map<String, CertificateInfoVO> numberToEntytyMap = certificateInfoService.getMapById(cIdList);
        List<FileVO> fileVOList = fileApiService.listMaxFileByDataIds(dataIdList);
        Map<String, List<FileVO>> collect =new HashMap<>();
        if(!CollectionUtils.isEmpty(fileVOList)){
            collect = fileVOList.stream().collect(Collectors.groupingBy(FileVO::getDataId));
        }
        Map<String, List<FileVO>> finalCollect = collect;
        List<DictValueVO> list = dictRedisHelper.getByDictNumber("pms_certificate_level", vos.get(0).getOrgId());
        Map<String, DictValueVO> certificateLevelMap =list.stream().collect(Collectors.toMap(DictValueVO::getNumber, Function.identity()));
        vos.forEach(item->{
            item.setFileVOList(finalCollect.getOrDefault(item.getId(),new ArrayList<>()));

            CertificateInfoVO certificateInfo = numberToEntytyMap.get(item.getCertificateId());
            if(null != certificateInfo){
                item.setCertificateName(certificateInfo.getName());
//                item.setCertificateLevel(certificateInfo.getLevel());
                if(certificateLevelMap.get(item.getCertificateLevel()) != null){
                    item.setCertificateLevelName(certificateLevelMap.get(item.getCertificateLevel()).getName());
                }
                item.setCertificateTypeName(certificateInfo.getCertificateTypeName());
                item.setCertificateType(certificateInfo.getCertificateType());
                item.setRenewalYearNum(certificateInfo.getRenewalYearNum());
                item.setDirectionInfo(certificateInfo.getDirectionInfo());
            }
        });
    }

    @Override
    public List<BasicUserCertificate> listByUserCodeList(List<String> userCodeList) {
        if(CollectionUtils.isEmpty(userCodeList)){
            return  new ArrayList<>();
        }
        LambdaQueryWrapperX<BasicUserCertificate> condition = new LambdaQueryWrapperX<>(BasicUserCertificate.class);
        condition.in(BasicUserCertificate::getUserCode,userCodeList);
        return this.list(condition);
    }

    @Override
    public List<BasicUserCertificateVO> userCertificateList(String userCode) throws Exception {
        if(StrUtil.isEmpty(userCode)){
            return  new ArrayList<>();
        }
        LambdaQueryWrapperX<BasicUserCertificate> condition = new LambdaQueryWrapperX<>(BasicUserCertificate.class);
        condition.eq(BasicUserCertificate::getUserCode,userCode);
        List<BasicUserCertificate> basicUserCertificateList = this.list(condition);
        if(CollectionUtils.isEmpty(basicUserCertificateList)){
            return  new ArrayList<>();
        }
        List<BasicUserCertificateVO> certificateVOS = BeanCopyUtils.convertListTo(basicUserCertificateList, BasicUserCertificateVO::new);
        this.setEveryName(certificateVOS);
        return  certificateVOS;
    }


}
