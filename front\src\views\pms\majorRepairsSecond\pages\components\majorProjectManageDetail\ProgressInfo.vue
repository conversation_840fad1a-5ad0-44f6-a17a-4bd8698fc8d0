<script setup lang="ts">
import {
  BasicButton, downloadByData, OrionTable,
} from 'lyra-component-vue3';
import {
  computed, h, ref, Ref, unref,
} from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import { openFormDrawerOrModal } from '/@/views/pms/majorRepairsSecond/pages/utils/util';
import { useRoute } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import MajorProjectProgressForm from './childComponent/MajorProjectProgressForm.vue';

interface Query {
  isSchedule?: boolean;
  [key: string]: any; // 允许其他任意属性
}

const props = withDefaults(defineProps<{
  isDetails?: boolean,
  params?: any,
  query?: Query,
  record?: any,
}>(), {
  isDetails: false,
  params: () => ({}),
  query: () => ({}), // 修改为函数形式
  record: () => ({}),
});

const route = useRoute();
const projectId = computed(() =>
  (props?.query?.isSchedule
    ? props?.query?.projectId || props?.record?.id
    : route.params.id));
const tableRef: Ref = ref();

function updateTable() {
  tableRef.value.reload();
}

const isActions = props?.query?.isSchedule ? [] : [
  {
    title: '操作',
    dataIndex: 'action',
    slots: { customRender: 'action' },
    width: 120,
    fixed: 'right',
  },
];

const tableOptions = {
  showToolButton: false,
  isSpacing: props?.query?.isSchedule,
  pagination: props?.query?.isSchedule,
  maxHeight: 300,
  showScreenButton: !props?.query?.isSchedule,
  showTableSetting: !props?.query?.isSchedule,
  smallSearchField: ['progressSchedule', 'progressDetail'],
  api: (params: object) => new Api(props.isDetails || props?.query?.isSchedule ? '/pms/majorRepairOrg/getRepairOrgProgress' : '/pms/importantProject/getImportantProjectProgress').fetch({
    ...params,
    query: {
      projectId: unref(projectId),
      repairOrgId: props?.params?.repairOrgId,
    },
  }, '', 'POST'),
  columns: [
    {
      title: '日期',
      dataIndex: 'workDate',
      width: 200,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '总体进展',
      dataIndex: 'progressSchedule',
      width: 200,
      customRender({ text }) {
        return text ? `${text}%` : '0%';
      },
    },
    {
      title: '工作进展',
      dataIndex: 'progressDetail',
    },
    {
      title: '备注',
      dataIndex: 'remark',
    },
    ...isActions,
  ],
  actions: [
    {
      text: '编辑',
      onClick(record) {
        handleOperateFormProgress({
          ...record,
          title: '编辑进展',
          msg: '更新成功',
        });
      },
    },
    {
      text: '删除',
      modal: ({ id }) => deleteProgressApi([id]),
    },
  ],
};

function deleteProgressApi(ids: string[]) {
  return new Promise((resolve, reject) => {
    new Api(props.isDetails || props?.query?.isSchedule ? '/pms/majorRepairOrg/delete/progress' : '/pms/importantProject/deleteProgress').fetch(ids, '', props.isDetails ? 'DELETE' : 'POST').then(() => {
      updateTable();
      resolve('');
    }).catch((e) => {
      reject(e);
    });
  });
}

const toolButtons = computed(() => [
  {
    text: '添加进展',
    event: 'add',
    icon: 'sie-icon-tianjiaxinzeng',
    type: 'primary',
  },
]);

function handleTool(event: string, record) {
  switch (event) {
    case 'add':
      handleOperateFormProgress({
        ...record,
        title: '添加进展',
        msg: '添加成功',
      });
      break;
  }
}
function handleOperateFormProgress(record) {
  openFormDrawerOrModal(MajorProjectProgressForm, {
    ...record,
    projectId: unref(projectId),
    position: 'right',
    isDetails: props.isDetails || props?.query?.isSchedule,
    repairOrgId: props.isDetails || props?.query?.isSchedule ? props?.params?.repairOrgId : undefined,
  }, () => {
    message.success(record.msg);
    updateTable();
  });
}
async function handleExportApi() {
  Modal.confirm({
    title: '系统提示！',
    content: '确认导出所选数据？',
    onOk() {
      return new Promise((resolve) => {
        downloadByData('/pms/importantProject/getImportantProjectProgress/export', {
          projectId: unref(projectId),
        }).then(() => {
          resolve('');
        }).catch((e) => {
          resolve(e);
        });
      });
    },
  });
}
</script>

<template>
  <div class="progress-info">
    <OrionTable
      ref="tableRef"
      style="position: relative"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <template v-if="!props?.query?.isSchedule">
          <BasicButton
            v-for="item in toolButtons"
            :key="item.event"
            v-bind="item"
            @click="handleTool(item.event,{})"
          >
            {{ item.text }}
          </BasicButton>
        </template>
        <BasicButton
          v-if="!props.isDetails && !props?.query?.isSchedule"
          icon="sie-icon-daochu"
          @click="handleExportApi"
        >
          导出
        </BasicButton>
      </template>
    </OrionTable>
  </div>
</template>

<style scoped lang="less">
.progress-info{
  height: 500px;
  overflow: hidden;
}
</style>
