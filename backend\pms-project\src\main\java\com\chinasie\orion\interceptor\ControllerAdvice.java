package com.chinasie.orion.interceptor;

import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSException;
import feign.RetryableException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import java.util.Objects;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/1/17 18:24
 * @description:
 */
@RestControllerAdvice
@Slf4j
public class ControllerAdvice {

    @ResponseBody
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public ResponseEntity<ResponseDTO> methodArgumentNotValidException(MethodArgumentNotValidException ex) {
        log.error("参数验证异常：", ex);
        ResponseDTO adviceResult = new ResponseDTO();
        BindingResult bindingResult = ex.getBindingResult();
        adviceResult.setMessage(String.format("%s", bindingResult.getFieldError().getDefaultMessage()));
        adviceResult.setCode(HttpStatus.BAD_REQUEST.value());
        return ResponseEntity.status(HttpStatus.OK).body(adviceResult);
    }

    @ResponseBody
    @ExceptionHandler(value = PMSException.class)
    public ResponseEntity<ResponseDTO> userDefinedException(PMSException ex) {
        String message = ex.getMessage();
        String errorDesc = ex.getErrorDesc();
        ResponseDTO adviceResult = new ResponseDTO();
        if( StrUtil.isBlank(message)){
            message = errorDesc;
        }
        adviceResult.setMessage(message);
        adviceResult.setCode(HttpStatus.BAD_REQUEST.value());
        return ResponseEntity.status(HttpStatus.OK).body(adviceResult);
    }

    @ResponseBody
    @ExceptionHandler(value = MethodArgumentTypeMismatchException.class)
    public ResponseEntity<ResponseDTO> methodArgumentTypeMismatchException(MethodArgumentTypeMismatchException ex) {
        String logStr = "参数转换失败，方法：" + Objects.requireNonNull(ex.getParameter().getMethod()).getName() + ",参数：" +
                ex.getName() + "，信息：" + ex.getLocalizedMessage();
        log.error(logStr);
        ResponseDTO adviceResult = new ResponseDTO();
        adviceResult.setMessage(logStr);
        adviceResult.setCode(HttpStatus.BAD_REQUEST.value());
        return ResponseEntity.status(HttpStatus.OK).body(adviceResult);
    }

    @ResponseBody
    @ExceptionHandler(value = HttpMessageNotReadableException.class)
    public ResponseEntity<ResponseDTO> httpMessageNotReadableException(HttpMessageNotReadableException ex) {
        log.error("消息不可读", ex);
        ResponseDTO adviceResult = new ResponseDTO();
        adviceResult.setMessage(String.format("%s", ex.getMessage()));
        adviceResult.setCode(HttpStatus.BAD_REQUEST.value());
        return ResponseEntity.status(HttpStatus.OK).body(adviceResult);
    }

    @ResponseBody
    @ExceptionHandler(value = RetryableException.class)
    public ResponseEntity<ResponseDTO> feignRetryableExceptionHandler(RetryableException ex) {
        log.error("服务未就绪", ex);
        ResponseDTO adviceResult = new ResponseDTO();
        adviceResult.setMessage(String.format("后台服务未就绪，请稍后重试!:%s", ex.getMessage()));
        adviceResult.setCode(HttpStatus.BAD_REQUEST.value());
        return ResponseEntity.status(HttpStatus.OK).body(adviceResult);
    }
    @ResponseBody
    @ExceptionHandler(value = Exception.class) // 扩大处理范围
    public ResponseEntity<ResponseDTO> errorHandler(Exception ex) {
        log.error("接口未知异常", ex);
        ResponseDTO adviceResult = new ResponseDTO();
        if(ex instanceof BaseException){
            BaseException baseException = (BaseException) ex;
            adviceResult.setMessage(baseException.getErrorDesc());
            adviceResult.setCode(baseException.getErrorCode());
            return ResponseEntity.status(HttpStatus.OK).body(adviceResult);
        }
        adviceResult.setMessage(String.format("%s", ex.getMessage()));
        adviceResult.setCode(HttpStatus.BAD_REQUEST.value());
        return ResponseEntity.status(HttpStatus.OK).body(adviceResult);
    }
    @ResponseBody
    @ExceptionHandler(Throwable.class)
    public ResponseEntity<ResponseDTO> handleAllExceptions(Throwable ex) {
        log.error("全局未捕获异常: ", ex);  // 注意保留异常堆栈
        ResponseDTO result = new ResponseDTO();
        result.setCode(HttpStatus.INTERNAL_SERVER_ERROR.value());
        result.setMessage("系统内部异常，请联系管理员");
        return ResponseEntity.status(HttpStatus.OK).body(result);
    }
}
