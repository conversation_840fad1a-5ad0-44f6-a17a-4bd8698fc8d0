package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;

import java.util.List;

/**
 * IncomeAccountConfirm VO对象
 *
 * <AUTHOR>
 * @since 2024-12-18 21:16:26
 */
@ApiModel(value = "IncomeAccountConfirmVO对象", description = "收入记账明细确认表")
@Data
public class IncomeAccountConfirmVO extends ObjectVO implements Serializable {

    /**
     * 凭证类型
     */
    @ApiModelProperty(value = "凭证类型")
    private String voucherType;

    @ApiModelProperty(value = "凭证类型名称")
    private String voucherTypeName;


    /**
     * 是否调账凭证
     */
    @ApiModelProperty(value = "是否调账凭证")
    private String adjAccountVoucher;

    @ApiModelProperty(value = "是否调账凭证名称")
    private String adjAccountVoucherName;


    /**
     * 确认状态
     */
    @ApiModelProperty(value = "确认状态")
    private String confirmStatus;

    @ApiModelProperty(value = "确认状态名称")
    private String confirmStatusName;


    /**
     * 公司代码
     */
    @ApiModelProperty(value = "公司代码")
    private String companyCode;


    /**
     * 科目
     */
    @ApiModelProperty(value = "科目")
    private String subject;


    /**
     * 分配编码
     */
    @ApiModelProperty(value = "分配编码")
    private String distributiveCode;


    /**
     * 过账期间
     */
    @ApiModelProperty(value = "过账期间")
    private Date postingDate;


    /**
     * 凭证编号
     */
    @ApiModelProperty(value = "凭证编号")
    private String voucherNum;


    /**
     * 凭证日期
     */
    @ApiModelProperty(value = "凭证日期")
    private Date voucherDate;


    /**
     * 过账期间
     */
    @ApiModelProperty(value = "过账期间")
    private String postingPeriod;


    /**
     * 本币金额
     */
    @ApiModelProperty(value = "本币金额")
    private BigDecimal localCurrencyAmt;


    /**
     * 利润中心
     */
    @ApiModelProperty(value = "利润中心")
    private String profitCenter;


    /**
     * 成本中心
     */
    @ApiModelProperty(value = "成本中心")
    private String costCenter;


    /**
     * 文本
     */
    @ApiModelProperty(value = "文本")
    private String conText;


    /**
     * 收入计划编号
     */
    @ApiModelProperty(value = "收入计划编号")
    private String incomePlanNum;


    /**
     * 是否修改凭证类型
     */
    @ApiModelProperty(value = "是否修改凭证类型")
    private String isUpdate;


    /**
     * 是否标红
     */
    @ApiModelProperty(value = "是否标红")
    private String isRed;


}
