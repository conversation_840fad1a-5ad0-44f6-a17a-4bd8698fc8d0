<script setup lang="ts">
import { randomString, Select } from 'lyra-component-vue3';
import {
  computed, CSSProperties, inject, onMounted, Ref, ref, unref,
} from 'vue';
import {
  Breadcrumb, BreadcrumbItem, Collapse, CollapsePanel,
} from 'ant-design-vue';
import { BarsOutlined, CaretRightOutlined } from '@ant-design/icons-vue';
import { useRoute, useRouter } from 'vue-router';
import { filter } from 'lodash-es';
// import { useGraph } from '../hooks/useGraph';
// import Api from '/@/api';

const emits = defineEmits<{
  (e: 'updateScroll', top: number): void
}>();

const route = useRoute();
const router = useRouter();
const detailsData: Record<string, any> = inject('detailsData');
const projectId = ref(route.query.projectId);
const jobId: Ref<string> = ref(route.params.id as string || null);
const jobList = computed(() => detailsData.allJobList.map((item) => ({
  label: `${item.name}（${item.number}）`,
  value: item.id,
})));
// const graphContainerRef: Ref<HTMLElement> = ref();
// const { updateStatus } = useGraph(graphContainerRef, emits);

const routes: Array<{
  breadcrumbName: string,
  to?: Record<string, any>
}> = computed(() => filter([
  {
    breadcrumbName: '大修管理',
    to: {
      name: 'Overhaul',
    },
    show: true,
  },
  {
    breadcrumbName: '大修详情',
    to: {
      name: 'MajorRepairsSecondDetail',
      params: {
        id: route.query?.id,
      },
      query: {
        query: randomString(5),
      },
    },
    show: true,
  },
  {
    breadcrumbName: '项目详情',
    to: {
      name: 'MajorProjectManageDetail',
      params: {
        id: unref(projectId),
      },
      query: {
        query: randomString(5),
        repairId: route.query?.id,
      },
    },
    show: computed(() => projectId.value).value,
  },
  {
    breadcrumbName: '作业详情',
    show: true,
  },
], (item) => item.show));

function handleRoute(to) {
  router.push(to);
}

// async function getLifeCycle() {
//   const result = await new Api(`/pms/job-manage/life/cycle/${detailsData.id}`).fetch('', '', 'GET');
//   updateStatus(result);
// }

onMounted(() => {
  // getLifeCycle();
});

function changeJobOrder(id: string) {
  if (!detailsData?.repairRound) return;
  router.push({
    name: route.name,
    params: {
      id,
    },
    query: {
      id: detailsData?.repairRound,
    },
  });
}

const activeKey: Ref = ref(['cycle']);

const customStyle: CSSProperties = {
  padding: '0',
  border: 'none',
};

defineExpose({
  updateLife() {
    // getLifeCycle();
  },
});
</script>

<template>
  <div class="cycle">
    <div class="header">
      <span class="fz16 fw-b">负责人{{ detailsData.rspUserName }}<span class="fz14 c99 ml5">{{
        detailsData.allJobList?.length || 0
      }}个工单</span></span>
      <Select
        v-model:value="jobId"
        style="width: 300px"
        class="ml20"
        :options="jobList||[]"
        @change="changeJobOrder"
      />
      <Breadcrumb style="margin-left: auto">
        <BreadcrumbItem
          v-for="(item,index) in routes"
          :key="index"
        >
          <bars-outlined v-if="index===0" />
          <span
            v-if="item.to"
            class="link"
            @click="handleRoute(item.to)"
          >{{ item.breadcrumbName }}</span>
          <span v-else>{{ item.breadcrumbName }}</span>
        </BreadcrumbItem>
      </Breadcrumb>
    </div>

    <!-- <Collapse
      v-model:activeKey="activeKey"
      ghost
      :bordered="false"
    >
      <template #expandIcon="{ isActive }">
        <caret-right-outlined :rotate="isActive ? 90 : 0" />
      </template>
      <CollapsePanel
        v-if="isShow"
        key="cycle"
        header="作业生命周期"
        :style="customStyle"
      >
        <div
          class="graph-container"
        >
          <div
            ref="graphContainerRef"
            class="graph-item"
          />
        </div>
      </CollapsePanel>
    </Collapse> -->
  </div>
</template>

<style scoped lang="less">
.link {
  cursor: pointer;

  &:hover {
    color: ~`getPrefixVar('primary-color')`;
  }
}

.cycle {
  position: sticky;
  top: 0;
  padding: ~`getPrefixVar('content-padding-top')` ~`getPrefixVar('content-padding-left')` 0;
  z-index: 5;
  background-color: #fff;

  .header {
    display: flex;
    align-items: center;
    padding-bottom: 12px;
  }
}

.graph-container {
  width: 100%;
  height: 240px;
  box-shadow: 0 0 2px 2px #eee;
  border-radius: 4px;

  .graph-item {
    width: 100% !important;
    height: 100% !important;
  }
}

:deep(.x6-node) {
  cursor: pointer !important;
}

:deep(.x6-edge) {
  > path {
    cursor: default !important;
  }
}

:deep(g[data-cell-id*="_"]) {
  cursor: default !important;
}

:deep(.ant-collapse-header) {
  padding-left: 0;
}

:deep(.ant-collapse-content-box) {
  padding: 0;
}
</style>
