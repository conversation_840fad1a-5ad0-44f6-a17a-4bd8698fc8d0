package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.ProjectPlanTypeDTO;
import com.chinasie.orion.domain.entity.ProjectPlanType;
import com.chinasie.orion.domain.vo.ProjectPlanTypeVO;
import com.chinasie.orion.domain.vo.TypeAndTypeAttrValueVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/10/9 17:21
 */
public interface ProjectPlanTypeService extends OrionBaseService<ProjectPlanType> {

    /**
     * 获取项目计划类型树
     * @param keyword
     * @return
     * @throws Exception
     */
    List<ProjectPlanTypeVO> tree(String keyword, Integer status) throws Exception;

    /**
     * 新增项目计划类型
     * @param typeDTO
     * @return
     * @throws Exception
     */
    String add(ProjectPlanTypeDTO typeDTO) throws Exception;

    /**
     * 编辑项目计划类型
     * @param typeDTO
     * @return
     * @throws Exception
     */
    Boolean edit(ProjectPlanTypeDTO typeDTO) throws Exception;

    /**
     * 获取项目计划类型详情
     * @param id
     * @return
     * @throws Exception
     */
    ProjectPlanTypeVO detail(String id,String pageCode) throws Exception;

    /**
     * 禁用项目计划类型
     * @param id
     * @return
     * @throws Exception
     */
    Boolean ban(String id) throws Exception;

    /**
     * 启用项目计划类型
     * @param id
     * @return
     * @throws Exception
     */
    Boolean use(String id) throws Exception;

    /**
     * 删除项目计划类型
     * @param id
     * @return
     * @throws Exception
     */
    Boolean remove(String id) throws Exception;

    /**
     * 获取该项目计划类型的所有父级(包括自己)
     * @param currentId
     * @return
     * @throws Exception
     */
    List<ProjectPlanType> getAllParents(String currentId, Boolean includingMyself) throws Exception;

    /**
     * 获取类型及属性值
     * @param typeId
     * @param riskId
     * @return
     * @throws Exception
     */
    TypeAndTypeAttrValueVO getTypeAndAttributeValues(String typeId, String riskId) throws Exception;

}
