CREATE TABLE `pmsx_center_job_manage`
(
    `id`                varchar(64) NOT NULL COMMENT '主键',
    `class_name`        varchar(64) COMMENT '创建人',
    `creator_id`        varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`       datetime    NOT NULL COMMENT '修改时间',
    `owner_id`          varchar(64) COMMENT '拥有者',
    `create_time`       datetime    NOT NULL COMMENT '创建时间',
    `modify_id`         varchar(64) NOT NULL COMMENT '修改人',
    `remark`            varchar(1024) COMMENT '备注',
    `platform_id`       varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`            varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`            int         NOT NULL COMMENT '状态',
    `logic_status`      int         NOT NULL COMMENT '逻辑删除字段',
    `number`            varchar(64) COMMENT '工单号',
    `name`              varchar(64) COMMENT '作业名称',
    `n_or_o`            varchar(4) COMMENT 'N/O',
    `repair_round`      varchar(64) COMMENT '大修轮次',
    `rsp_dept`          varchar(64) COMMENT '责任中心',
    ·work_center·       varchar(64) COMMENT '工作中心(默认值：SNPI)',
    `rsp_user_id`       varchar(64) COMMENT '责任人id',
    `rsp_user_name`     varchar(64) COMMENT '责任人姓名',
    `job_base_name`     varchar(64) COMMENT '作业基地',
    `begin_time`        datetime COMMENT '计划开工时间',
    `end_time`          datetime COMMENT '计划结束时间',
    `work_duration`     int COMMENT '计划工期',
    `actual_begin_time` datetime COMMENT '实际开工时间',
    `actual_end_time`   datetime COMMENT '实际完工时间',
    `phase`             int COMMENT '作业阶段：作业状态',
    `match`             int COMMENT '是否匹配（0未匹配  1匹配）',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='中心作业管理';
