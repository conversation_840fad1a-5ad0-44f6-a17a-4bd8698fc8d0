package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.ContractPayNodeConfirmAuditRecordDTO;
import com.chinasie.orion.domain.entity.ContractPayNodeConfirmAuditRecord;
import com.chinasie.orion.domain.vo.ContractPayNodeConfirmAuditRecordVO;
import com.chinasie.orion.domain.vo.ContractPayNodeDetailVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

/**
 * <p>
 * ContractPayNodeConfirmAuditRecord 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-27 15:12:55
 */
public interface ContractPayNodeConfirmAuditRecordService extends OrionBaseService<ContractPayNodeConfirmAuditRecord> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    ContractPayNodeDetailVO detail(String id) throws Exception;

    /**
     * 新增
     * <p>
     * * @param contractPayNodeConfirmAuditRecordDTO
     */
    ContractPayNodeConfirmAuditRecordVO create(ContractPayNodeConfirmAuditRecordDTO contractPayNodeConfirmAuditRecordDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param contractPayNodeConfirmAuditRecordDTO
     */
    Boolean edit(ContractPayNodeConfirmAuditRecordDTO contractPayNodeConfirmAuditRecordDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ContractPayNodeConfirmAuditRecordVO> pages(Page<ContractPayNodeConfirmAuditRecordDTO> pageRequest) throws Exception;

}
