package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ProjectReceivableDTO;
import com.chinasie.orion.domain.vo.ProjectReceivableVO;
import com.chinasie.orion.domain.vo.ProjectReceivableValueVO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectReceivableService;
import com.mzt.logapi.starter.annotation.LogRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.dto.ResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
/**
 * <p>
 * ProjectReceivable 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-23 17:36:54
 */
@RestController
@RequestMapping("/projectReceivable")
@Api(tags = "项目应收表")
public class ProjectReceivableController {

    @Autowired
    private ProjectReceivableService projectReceivableService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据【{{#id}}】", type = "项目应收表", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectReceivableVO> detail(@PathVariable(value = "id") String id,@RequestParam(value = "pageCode", required = false) String pageCode) throws Exception {
        ProjectReceivableVO rsp = projectReceivableService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectReceivableDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增了数据【{{#projectReceivableDTO.name}}】", type = "项目应收表", subType = "新增", bizNo = "")
    public ResponseDTO<ProjectReceivableVO> create(@RequestBody ProjectReceivableDTO projectReceivableDTO) throws Exception {
        ProjectReceivableVO rsp =  projectReceivableService.create(projectReceivableDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectReceivableDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#projectReceivableDTO.name}}】", type = "项目应收表", subType = "编辑", bizNo = "{{#projectReceivableDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  ProjectReceivableDTO projectReceivableDTO) throws Exception {
        Boolean rsp = projectReceivableService.edit(projectReceivableDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据【{{#ids}}】", type = "项目应收表", subType = "删除", bizNo = "{{#ids}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectReceivableService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/pages", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】分页了数据【{{#pageRequest.pageSize}}】", type = "项目应收表", subType = "分页", bizNo = "")
    public ResponseDTO<Page<ProjectReceivableVO>> pages(@RequestBody Page<ProjectReceivableDTO> pageRequest) throws Exception {
        Page<ProjectReceivableVO> rsp =  projectReceivableService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 列表
     *
     * @param
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "列表")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】列表了数据【{{#pageRequest.pageSize}}】", type = "项目应收表", subType = "列表", bizNo = "")
    public ResponseDTO<List<ProjectReceivableVO>> pages(@RequestBody ProjectReceivableDTO projectReceivableDTO) throws Exception {
        List<ProjectReceivableVO> rsp =  projectReceivableService.getList(projectReceivableDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 导入文件
     *
     * @param files
     * @return
     */
    @ApiOperation("编辑时导入文件")
    @RequestMapping(value = "importfiles/{id}", method = {RequestMethod.POST})
    @LogRecord(success = "【{USER{#logUserId}}】编辑时导入文件【{{#files}}】", type = "项目应收表", subType = "编辑时导入文件", bizNo = "{{#files}}")
    public ResponseDTO<List<String>> importFiles(@PathVariable("id") String id, @RequestBody List<FileDTO> files) throws Exception {
        List<String> rsp = projectReceivableService.importFiles(id, files);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "获取列表")
    @RequestMapping(value = "/getProjectReceivableVOs/{id}", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】获取列表【{{#id}}】", type = "项目应收表", subType = "获取列表", bizNo = "{{#id}}")
    public ResponseDTO<List<ProjectReceivableValueVO>> getProjectReceivableVOs(@PathVariable("id") String id) throws Exception {
        List<ProjectReceivableValueVO> rsp =  projectReceivableService.getProjectReceivableVOList(id);
        return new ResponseDTO<>(rsp);
    }

}

