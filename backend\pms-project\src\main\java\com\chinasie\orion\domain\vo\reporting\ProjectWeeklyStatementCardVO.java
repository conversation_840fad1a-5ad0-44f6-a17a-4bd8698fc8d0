package com.chinasie.orion.domain.vo.reporting;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.domain.vo.ObjectVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2023-11-14 15:21:01
 */
@ApiModel(value = "ProjectWeeklyStatementCardVO对象", description = "计划周报")
@Data
public class ProjectWeeklyStatementCardVO extends ObjectVO implements Serializable {
    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 所在年份第几周
     */
    @ApiModelProperty(value = "所在年份第几周")
    private Integer week;

    /**
     * 一周的开始时间
     */
    @ApiModelProperty(value = "一周的开始时间")
    private Date weekBegin;

    /**
     * 一周结束时间
     */
    @ApiModelProperty(value = "一周结束时间")
    private Date weekEnd;


    /**
     * 整体进度
     */
    @ApiModelProperty(value = "整体进度")
    private Integer busStatus;

    /**
     * 评分
     */
    @ApiModelProperty(value = "评分")
    private BigDecimal score;

    /**
     * 创建人名字
     */
    @ApiModelProperty("创建人名字")
    private String creatorName;


    @ApiModelProperty(value = "整体进度名称")
    private String  busStatusName;

    @ApiModelProperty(value = "是否审核")
    private Boolean audit;
    @ApiModelProperty(value = "是否修改")
    private Boolean edit;
    @ApiModelProperty(value = "是否提醒")
    private Boolean warn;
    @ApiModelProperty(value = "是否提交")
    private Boolean commit;

    /**
     * 日报内容
     */
    @ApiModelProperty(value = "日报内容")
    private List<ProjectWeeklyContentVO> projectWeeklyStatementContentVOList;
}
