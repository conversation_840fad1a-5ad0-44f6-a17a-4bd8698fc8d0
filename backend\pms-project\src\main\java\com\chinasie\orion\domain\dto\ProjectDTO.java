package com.chinasie.orion.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/06/10:37
 * @description:
 */
@Data
@ApiModel(value = "ProjectDTO对象", description = "项目")
public class ProjectDTO extends ObjectDTO {

    /**
     * 项目图片路径/hdfs
     */
    @ApiModelProperty(value = "项目图片路径/hdfs")
    private String projectImage;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String id;

    /**
     * 项目立项时间
     */
    @ApiModelProperty(value = "项目立项时间")
    private Date projectApproveTime;

    /**
     * 项目结束时间
     */
    @ApiModelProperty(value = "项目结束时间")
    @NotNull(message = "项目结束时间不能为空!")
    private Date projectEndTime;

    /**
     * 项目开始时间
     */
    @ApiModelProperty(value = "项目开始时间")
    @NotNull(message = "项目开始时间不能为空!")
    private Date projectStartTime;

    /**
     * 进度
     */
    @ApiModelProperty(value = "进度")
    private Double schedule;

    /**
     * 项目经理
     */
    @ApiModelProperty(value = "项目经理")
    private String pm;

    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    private String productId;

    /**
     * 项目状态
     */
    @ApiModelProperty(value = "项目状态id")
    private String statusId;

    /**
     * 是否需要申报
     */
    @ApiModelProperty(value = "是否需要申报")
    private Boolean isDeclare;

//    @ApiModelProperty("计划列表")
//    private List<String> schemeIdList = new ArrayList<>();

    @ApiModelProperty("综合计划列表 key 为id value为number")
    private List<RelevancyPlanDTO> planList;

    @ApiModelProperty(value = "项目类型")
    @NotBlank(message = "项目类型不能为空!")
    private String projectType;

    @ApiModelProperty(value = "项目子类型")
    private String projectSubType;

    @ApiModelProperty(value = "项目责任人")
    private String resPerson;

    @ApiModelProperty(value = "责任科室")
    private String resAdministrativeOffice;

    @ApiModelProperty(value = "责任部门")
    private String resDept;

    @ApiModelProperty(value = "责任班组")
    private String resTeamGroup;

    /**
     * 项目来源
     */
    @ApiModelProperty(value = "项目来源")
    private String projectSource;


    /**
     * 数据ids
     */
    @ApiModelProperty(value = "数据ids")
    private List<String> ids;


    @ApiModelProperty(value = "科研需求申报id")
    private String scientificDeclareId;

    /**
     * 是否跳转
     */
    @ApiModelProperty(value = "是否跳转")
    private Boolean jumpFlag = false;

    /**
     * 搜索条件
     */
    @ApiModelProperty(value = "搜索条件")
    private String keyWord;

    /**
     * 统计周期
     */
    @ApiModelProperty(value = "统计周期")
    private String statisticalPeriod;

    //================================================研发项目字段=====================================
    @ApiModelProperty(value = "研发类型")
    private String research;
    @ApiModelProperty(value = "项目级别")
    private String level;
    @ApiModelProperty(value = "业务方向")
    private String direction;
    @ApiModelProperty(value = "产品类型")
    private String productType;
    @ApiModelProperty(value = "是否需要启动流程")
    private Boolean needWorkFlow;
}
