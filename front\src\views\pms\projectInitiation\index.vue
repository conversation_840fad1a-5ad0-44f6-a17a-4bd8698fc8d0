<script setup lang="ts">
import {
  BasicButton, DataStatusTag, Layout, OrionTable, openDrawer, isPower, Layout3,
} from 'lyra-component-vue3';
import {
  h, onMounted, Ref, ref,
} from 'vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { message, Modal } from 'ant-design-vue';
import AddTableNode from './components/AddTableNode.vue';
import Api from '/@/api';
const router = useRouter();
const tableRef:Ref = ref();
const selectRows:Ref = ref([]);
const powerData = ref({});
const tableOptions = {
  showToolButton: false,
  rowSelection: {},
  smallSearchField: ['number', 'name'],
  api: async (params) => {
    params.power = {
      pageCode: 'PMS1025',
      containerCode: 'PMS_XMLX_LIST_container_02',
      headContainerCode: 'PMS_XMLX_container_01',
    };
    let res = await new Api('/pms').fetch(params, 'projectApproval/pages', 'POST');
    powerData.value = res.headAuthList;
    return res;
  },
  columns: [
    {
      title: '编号',
      dataIndex: 'number',
      width: 120,
    },
    {
      title: '名称',
      dataIndex: 'name',
      minWidth: 120,
      customRender({ text, record }) {
        return isPower('PMS_XMLX_LIST_container_02_button_03', record?.rdAuthList) ? h('span', {
          onClick() {
            handleDetail(record);
          },
          class: 'action-btn',
        }, text) : text;
      },
    },
    {
      title: '类型',
      dataIndex: 'typeName',
      width: 110,
    },
    {
      title: '状态',
      dataIndex: 'dataStatus',
      customRender({ text }) {
        return text ? h(DataStatusTag, { statusData: text }) : '';
      },
      width: 100,
    },
    {
      title: '负责人',
      dataIndex: 'rspUserName',
      width: 100,
    },
    {
      title: '负责部门',
      dataIndex: 'rspDeptName',
      width: 100,
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
      width: 120,
    },
    {
      title: '创建日期',
      dataIndex: 'createTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
      width: 180,
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 140,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '编辑',
      isShow: (record) => isPower('PMS_XMLX_LIST_container_02_button_01', record?.rdAuthList),
      onClick(record) {
        createTableNode('edit', record);
        // openCreateAndEdit(true, { id: record.id });
      },
    },
    {
      text: '删除',
      isShow: (record) => isPower('PMS_XMLX_LIST_container_02_button_02', record?.rdAuthList),
      onClick: (record) => {
        batchDelete([record.id], 'one');
      },
    },
  ],
};
function createTableNode(type, record = {}) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: type === 'edit' ? '编辑立项' : '新增立项',
    width: 1000,
    content() {
      return h(AddTableNode, {
        ref: drawerRef,
        formType: type,
        drawerData: record,
      });
    },
    async onOk(): Promise<void> {
      await drawerRef.value.onSubmit();
      updateTable();
    },
  });
}
function handleDetail(record) {
  router.push({
    name: 'ProjectInitiationDetail',
    params: {
      id: record.id,
    },
    query: {
      projectId: record.projectId,
    },
  });
}

function updateTable() {
  tableRef.value?.reload();
}

// 批量删除
// 批量删除
function handleBatchDel() {
  batchDelete(selectRows.value.map((item) => item.id), 'all');
}
function batchDelete(params, type) {
  Modal.confirm({
    title: '删除提示',
    content: type === 'all' ? '是否删除所选的数据？' : '是否删除当前的数据',
    onOk() {
      new Api('/pms').fetch(params, 'projectApproval', 'DELETE')
        .then(() => {
          message.success('删除成功');
          updateTable();
        });
    },
  });
}

// 表格勾选回调
function selectionChange({ rows }) {
  selectRows.value = rows;
}
</script>

<template>
  <Layout
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      :onSelectionChange="selectionChange"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="isPower('PMS_XMLX_container_01_button_01',powerData)"
          type="primary"
          icon="sie-icon-tianjiaxinzeng"
          @click="createTableNode('add')"
        >
          创建立项
        </BasicButton>
        <BasicButton
          v-if="isPower('PMS_XMLX_container_01_button_02',powerData)"
          icon="sie-icon-del"
          :disabled="selectRows.length===0"
          @click="handleBatchDel"
        >
          删除
        </BasicButton>
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">

</style>
