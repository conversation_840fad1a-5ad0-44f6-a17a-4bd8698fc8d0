<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <groupId>org.sonatype.oss</groupId>
        <artifactId>oss-parent</artifactId>
        <version>5</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>io.swagger</groupId>
    <artifactId>swagger-project</artifactId>
    <packaging>pom</packaging>
    <name>swagger-project</name>
    <version>1.6.2</version>
    <url>https://github.com/swagger-api/swagger-core</url>
    <scm>
        <connection>scm:git:**************:swagger-api/swagger-core.git</connection>
        <developerConnection>scm:git:**************:swagger-api/swagger-core.git</developerConnection>
        <url>https://github.com/swagger-api/swagger-core</url>
    </scm>
    <prerequisites>
        <maven>2.2.0</maven>
    </prerequisites>
    <developers>
        <developer>
            <id>fehguy</id>
            <name>Tony Tam</name>
            <email><EMAIL></email>
        </developer>
    </developers>
    <issueManagement>
        <system>github</system>
        <url>https://github.com/swagger-api/swagger-core/issues</url>
    </issueManagement>
    <mailingLists>
        <mailingList>
            <name>swagger-swaggersocket</name>
            <archive>https://groups.google.com/forum/#!forum/swagger-swaggersocket</archive>
        </mailingList>
    </mailingLists>
    <licenses>
        <license>
            <name>Apache License 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.html</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
    <build>
        <scriptSourceDirectory>src/main/scripts</scriptSourceDirectory>
        <outputDirectory>target/classes</outputDirectory>
        <testOutputDirectory>target/test-classes</testOutputDirectory>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <excludes>
                    <exclude>logback.xml</exclude>
                </excludes>
            </resource>
        </resources>
        <extensions>
            <extension>
                <groupId>org.jvnet.wagon-svn</groupId>
                <artifactId>wagon-svn</artifactId>
                <version>1.8</version>
            </extension>
            <extension>
                <groupId>org.apache.maven.wagon</groupId>
                <artifactId>wagon-ssh-external</artifactId>
                <version>1.0-alpha-6</version>
            </extension>
            <extension>
                <groupId>org.apache.maven.wagon</groupId>
                <artifactId>wagon-webdav</artifactId>
                <version>1.0-beta-1</version>
            </extension>
        </extensions>
        <defaultGoal>install</defaultGoal>
        <directory>target</directory>
        <finalName>${project.artifactId}-${project.version}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>${enforcer-plugin-version}</version>
                <executions>
                    <execution>
                        <id>enforce-versions</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <DependencyConvergence/>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.1.2</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.7</version>
                <configuration>
                    <aggregate>true</aggregate>
                    <source>1.7</source>
                    <encoding>UTF-8</encoding>
                    <maxmemory>1g</maxmemory>
                    <links>
                        <link>https://docs.oracle.com/javase/7/docs/api/</link>
                    </links>
                    <excludePackageNames>${javadoc.package.exclude}</excludePackageNames>
                </configuration>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.0</version>
                <configuration>
                    <source>1.7</source>
                    <target>1.7</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>2.4</version>
                <configuration>
                    <excludes>
                        <exclude>**/logback.xml</exclude>
                    </excludes>
                    <archive>
                        <manifestEntries>
                            <mode>development</mode>
                            <url>${project.url}</url>
                            <implementation-version>${project.version}</implementation-version>
                            <package>io.swagger</package>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-site-plugin</artifactId>
                <version>2.1</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>2.1</version>
            </plugin>

            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>default-prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-report</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-check</id>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <excludes>
                        <!-- TODO remove these excludes and add tests -->
                        <exclude>**/io/swagger/config/FilterFactory.*</exclude>
                        <exclude>**/io/swagger/model/ApiInfo.*</exclude>
                        <exclude>**/io/swagger/util/Yaml.*</exclude>
                        <exclude>**/io/swagger/jackson/*</exclude>
                        <exclude>**/io/swagger/jackson/mixin/*</exclude>
                        <exclude>**/io/swagger/converter/ModelConverters.*</exclude>

                        <!-- JAXRS -->
                        <exclude>**/io/swagger/jaxrs/json/JacksonJsonProvider.*</exclude>
                        <exclude>**/io/swagger/jaxrs/listing/ApiListingResource.*</exclude>
                        <exclude>**/io/swagger/jaxrs/listing/SwaggerSerializers.*</exclude>
                        <exclude>**/io/swagger/jaxrs/config/WebXMLReader.*</exclude>
                        <exclude>**/io/swagger/jaxrs/config/DefaultJaxrsScanner.*</exclude>
                        <exclude>**/io/swagger/jaxrs/config/DefaultJaxrsConfig.*</exclude>

                        <!-- Servlet -->
                        <exclude>**/io/swagger/servlet/config/WebXMLReader.*</exclude>
                        <exclude>**/io/swagger/servlet/config/ServletScanner.*</exclude>
                        <exclude>**/io/swagger/servlet/config/DefaultServletConfig.*</exclude>
                        <exclude>**/io/swagger/servlet/listing/ApiDeclarationServlet.*</exclude>
                        <exclude>**/*ServletReaderExtension$*.*</exclude>
                    </excludes>
                    <rules>
                        <rule>
                            <element>BUNDLE</element>
                            <limits>
                                <limit>
                                    <counter>COMPLEXITY</counter>
                                    <value>COVEREDRATIO</value>
                                    <minimum>${coverage.complexity.minimum}</minimum>
                                </limit>
                                <limit>
                                    <counter>CLASS</counter>
                                    <value>MISSEDCOUNT</value>
                                    <maximum>${coverage.missed.classes}</maximum>
                                </limit>
                            </limits>
                        </rule>
                        <rule>
                            <element>CLASS</element>
                            <limits>
                                <limit>
                                    <counter>LINE</counter>
                                    <value>COVEREDRATIO</value>
                                    <minimum>${coverage.line.minimum}</minimum>
                                </limit>
                            </limits>
                        </rule>
                    </rules>
                </configuration>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-gpg-plugin</artifactId>
                    <configuration>
                        <releaseProfiles>release</releaseProfiles>
                        <goals>sign</goals>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.eclipse.jetty</groupId>
                    <artifactId>jetty-maven-plugin</artifactId>
                    <version>${jetty-version}</version>
                    <dependencies>
                        <dependency>
                            <groupId>com.sun.jersey</groupId>
                            <artifactId>jersey-servlet</artifactId>
                            <version>${jersey-version}</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>${enforcer-plugin-version}</version>
                </plugin>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>0.7.2.201409121644</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
    <profiles>
        <profile>
            <id>release-sign-artifacts</id>
            <activation>
                <property>
                    <name>performRelease</name>
                    <value>true</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <!-- The auth module, which is not complete -->
            <id>auth</id>
            <activation>
                <property>
                    <name>env</name>
                    <value>auth</value>
                </property>
            </activation>
        </profile>
    </profiles>
    <modules>
        <module>modules/swagger-annotations</module>
        <module>modules/swagger-models</module>
        <module>modules/swagger-mule</module>
        <module>modules/swagger-core</module>
        <module>modules/swagger-jaxrs</module>
        <module>modules/swagger-jersey2-jaxrs</module>
        <module>modules/swagger-jersey-jaxrs</module>
        <module>modules/swagger-hibernate-validations</module>
        <module>modules/swagger-servlet</module>
        <!--module>modules/swagger-utils</module-->
        <!--module>modules/swagger-oauth2-auth-server</module-->
    </modules>
    <reporting>
        <outputDirectory>target/site</outputDirectory>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.9</version>
                <configuration>
                    <aggregate>true</aggregate>
                    <debug>true</debug>
                    <links>
                        <link>http://java.sun.com/javaee/5/docs/api</link>
                        <link>http://java.sun.com/j2se/1.5.0/docs/api</link>
                    </links>
                    <excludePackageNames/>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jxr-plugin</artifactId>
                <version>2.3</version>
                <configuration>
                    <aggregate>true</aggregate>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-project-info-reports-plugin</artifactId>
                <version>2.6</version>
                <reportSets>
                    <reportSet>
                        <reports>
                            <report>project-team</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
        </plugins>
    </reporting>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.apache.maven</groupId>
                <artifactId>maven-plugin-tools-api</artifactId>
                <version>2.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>${felix-version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-ext</artifactId>
                <version>${slf4j-version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j-version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang-version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>org.testng</groupId>
                <artifactId>testng</artifactId>
                <version>${testng-version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.jayway.restassured</groupId>
                <artifactId>rest-assured</artifactId>
                <version>${rest-assured-version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-all</artifactId>
                <version>${mockito-version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>servlet-api</artifactId>
                <version>${servlet-api-version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-log4j12</artifactId>
                <version>${slf4j-version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.sun.jersey</groupId>
                <artifactId>jersey-client</artifactId>
                <version>${jersey-version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.jersey</groupId>
                <artifactId>jersey-core</artifactId>
                <version>${jersey-version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.jersey</groupId>
                <artifactId>jersey-json</artifactId>
                <version>${jersey-version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.jersey</groupId>
                <artifactId>jersey-servlet</artifactId>
                <version>${jersey-version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io-version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.reflections</groupId>
                <artifactId>reflections</artifactId>
                <version>${reflections-version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava-version}</version>
            </dependency>
            <dependency>
                <!-- resolve reflections 0.9.11 vs jersey-common 2.25.1 conflict -->
                <groupId>org.javassist</groupId>
                <artifactId>javassist</artifactId>
                <version>${javassist-version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson-version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson-version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-yaml</artifactId>
                <version>${jackson-version}</version>
            </dependency>
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${joda-time-version}</version>
            </dependency>
            <dependency>
                <groupId>org.joda</groupId>
                <artifactId>joda-convert</artifactId>
                <version>${joda-version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback-version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback-version}</version>
            </dependency>
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>${validation-api-version}</version>
            </dependency>
            <dependency>
                <groupId>org.immutables</groupId>
                <artifactId>value</artifactId>
                <version>2.4.4</version>
            </dependency>
            <dependency>
                <groupId>javax.ws.rs</groupId>
                <artifactId>jsr311-api</artifactId>
                <version>1.1.1</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.hk2.external</groupId>
                <artifactId>javax.inject</artifactId>
                <version>2.2.0</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate-validator-version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.media</groupId>
                <artifactId>jersey-media-multipart</artifactId>
                <version>${jersey2-version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.containers</groupId>
                <artifactId>jersey-container-servlet-core</artifactId>
                <version>${jersey2-version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.jersey.contribs</groupId>
                <artifactId>jersey-multipart</artifactId>
                <version>${jersey-version}</version>
            </dependency>
            <dependency>
                <groupId>com.openpojo</groupId>
                <artifactId>openpojo</artifactId>
                <version>0.8.0</version>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-testng</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito</artifactId>
                <version>${powermock.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito-version}</version>
            </dependency>
            <dependency>
                <groupId>asm</groupId>
                <artifactId>asm</artifactId>
                <version>3.3.1</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <properties>
        <joda-version>1.8.1</joda-version>
        <joda-time-version>2.9.1</joda-time-version>

        <felix-version>3.3.0</felix-version>
        <servlet-api-version>2.5</servlet-api-version>
        <jersey-version>1.13</jersey-version>
        <jersey2-version>2.25.1</jersey2-version>
        <jackson-version>2.11.1</jackson-version>
        <logback-version>1.2.3</logback-version>
        <reflections-version>0.9.11</reflections-version>
        <guava-version>27.0.1-android</guava-version>
        <javassist-version>3.21.0-GA</javassist-version>

        <testng-version>6.14.3</testng-version>
        <mockito-version>1.9.5</mockito-version>
        <rest-assured-version>2.8.0</rest-assured-version>
        <surefire-version>2.18.1</surefire-version>
        <maven-plugin-version>1.0.0</maven-plugin-version>
        <commons-lang-version>3.2.1</commons-lang-version>
        <slf4j-version>1.7.22</slf4j-version>
        <jetty-version>9.2.14.v20151106</jetty-version>
        <servlet-api-version>2.5</servlet-api-version>
        <coverage.complexity.minimum>0.90</coverage.complexity.minimum>
        <coverage.line.minimum>0.90</coverage.line.minimum>
        <coverage.missed.classes>0</coverage.missed.classes>
        <commons-io-version>2.5</commons-io-version>
        <enforcer-plugin-version>1.4</enforcer-plugin-version>
        <failsafe-plugin-version>2.19.1</failsafe-plugin-version>
        <validation-api-version>1.1.0.Final</validation-api-version>
    </properties>
</project>
