package com.chinasie.orion.repository;

import com.chinasie.orion.domain.dto.JobHighRiskStatisticDTO;
import com.chinasie.orion.domain.entity.JobManage;
import com.chinasie.orion.domain.vo.CountVO;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import com.chinasie.orion.domain.entity.MajorRepairPlan;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/08/10:42
 * @description:
 */
@Mapper
public interface MajorRepairPlanMapper extends OrionBaseMapper<MajorRepairPlan> {

    @Select({
            "SELECT",
            "    jm.name AS jobName,",
            "    jhr.risk_level AS riskLevel,",
            "    jhr.judgment_standards AS riskName,",
            "    jm.rsp_dept AS rspDeptId,",
            "    jm.rsp_user_id AS rspUserId,",
            "    jm.actual_end_time AS actualEndTime,",
            "    jhr.manager_name AS managePersonName,",
            "    jhr.check_name AS monitorPersonName",
            "FROM",
            "    pmsx_job_manage jm",
            "LEFT JOIN",
            "    pmsx_job_height_risk jhr ON jm.number = jhr.job_number",
            "WHERE",
            "    jm.repair_round = #{repairRound} AND",
            "    (",
            "        (jm.actual_end_time IS NULL AND jm.actual_begin_time IS NOT NULL AND jm.actual_begin_time < #{statisticTime}) OR",
            "        (jm.actual_end_time IS NOT NULL AND jm.actual_begin_time IS NOT NULL AND jm.actual_begin_time <= #{statisticTime} AND jm.actual_end_time > #{statisticTime})",
            "    ) AND",
            "    jhr.job_number IS NOT NULL AND",
            "    jhr.process_status IN ('90', '92')"
    })

    List<JobHighRiskStatisticDTO> selectJobRiskInfo(@Param("repairRound") String repairRound, @Param("statisticTime") Date statisticTime);




    @Select({
            "SELECT",
            "    jm.number AS number,",
            "    jm.phase AS phase",
            "FROM",
            "    pmsx_job_manage jm",
            "WHERE",
            "    jm.repair_round = #{repairRound}",
            "    AND jm.plan_scheme_id = #{planSchemeId}",
            "    AND jm.is_high_risk = 1"
    })
     List<JobManage> selectJobManageStatisticByConditions(
            @Param("repairRound") String repairRound,
            @Param("planSchemeId") String planSchemeId
    );

    /**
     * 通过大修id获取相关大修的作业数量
     * @param ids ids
     * @return 结果
     */
    List<CountVO> countJobManageByRepairPlanId(@Param("ids") List<String> ids);

    /**
     * 通过大修id获取相关项目计划的数量
     * @param ids
     * @return
     */
    List<CountVO> countProjectPlanByRepairPlanId(@Param("ids") List<String> ids);
}

