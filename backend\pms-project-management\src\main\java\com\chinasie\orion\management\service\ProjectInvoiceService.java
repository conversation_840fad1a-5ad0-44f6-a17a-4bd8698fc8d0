package com.chinasie.orion.management.service;


import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.dto.ProjectInvoiceDTO;
import com.chinasie.orion.management.domain.entity.ProjectInvoice;
import com.chinasie.orion.management.domain.vo.ProjectInvoiceVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * ProjectInvoice 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-31 14:16:17
 */
public interface ProjectInvoiceService extends OrionBaseService<ProjectInvoice> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    ProjectInvoiceVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param projectInvoiceDTO
     */
    String create(ProjectInvoiceDTO projectInvoiceDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param projectInvoiceDTO
     */
    Boolean edit(ProjectInvoiceDTO projectInvoiceDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ProjectInvoiceVO> pages(Page<ProjectInvoiceDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<ProjectInvoiceVO> vos) throws Exception;

    /**
     * 根据number查询发票
     * <p>
     * * @param number
     */
    ProjectInvoiceVO getByNumber(String number) throws Exception;
}
