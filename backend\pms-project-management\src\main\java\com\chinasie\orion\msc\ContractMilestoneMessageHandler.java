package com.chinasie.orion.msc;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.bo.PmsMQProducer;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class ContractMilestoneMessageHandler {

    @Autowired
    protected PmsMQProducer mqProducer;

    public void sendMessage(String businessId, String url, String contractName, String milestoneName, List<String> toUser, String platformId, String orgId, String code) {
        Map<String, Object> messageMap = MapUtil.builder(new HashMap<String, Object>())
                .put("$contractName$", contractName)
                .put("$milestone$", milestoneName)
                .build();
        SendMessageDTO sendMsc = SendMessageDTO.builder()
                .businessData(JSON.toJSONString(MapUtil.builder().put("flowType", "里程碑验收").build()))
                .messageMap(messageMap)
                .businessId(businessId)
                .todoStatus(0)
                .todoType(TodoTypeDict.TODO_TYPE_TASK)
                .urgencyLevel(0)
                .businessNodeCode(code)
                .titleMap(messageMap)
                .messageUrl(url+"?"+"id="+businessId)
                .messageUrlName("里程碑详情")
                .recipientIdList(toUser)
                .senderTime(new Date())
                .senderId(CurrentUserHelper.getCurrentUserId())
                .platformId(platformId)
                .orgId(orgId)
                .build();
        mqProducer.sendPmsMessage(sendMsc);
    }

    public void sendEditMessage(String businessId, String url, String contractName, String milestoneName, List<String> toUser, String platformId, String orgId, String code,Date originDate,Date newDate) {
        Map<String, Object> messageMap = MapUtil.builder(new HashMap<String, Object>())
                .put("$contractName$", contractName)
                .put("$milestone$", milestoneName)
                .put("$originDate$", originDate)
                .put("$newDate$", newDate)
                .build();
        SendMessageDTO sendMsc = SendMessageDTO.builder()
                .businessData(JSON.toJSONString(MapUtil.builder().put("flowType", "里程碑预期日期修改").build()))
                .messageMap(messageMap)
                .businessId(businessId)
                .todoStatus(0)
                .todoType(TodoTypeDict.TODO_TYPE_TASK)
                .urgencyLevel(0)
                .businessNodeCode(code)
                .titleMap(messageMap)
                .messageUrl(url+"?"+"id="+businessId)
                .messageUrlName("里程碑详情")
                .recipientIdList(toUser)
                .senderTime(new Date())
                .senderId(CurrentUserHelper.getCurrentUserId())
                .platformId(platformId)
                .orgId(orgId)
                .build();
        mqProducer.sendPmsMessage(sendMsc);
    }
}
