


CREATE TABLE IF NOT EXISTS `pms_ncf_form_purch_order_collect` (
    `order_number` varchar(256) DEFAULT NULL COMMENT '订单编号',
    `po_order_number` varchar(256) DEFAULT NULL COMMENT 'PO订单号',
    `commerce_channel_order_number` varchar(256) DEFAULT NULL COMMENT '电商渠道订单号',
    `enterprise_name` varchar(256) DEFAULT NULL COMMENT '企业名称',
    `pr_company_name` varchar(256) DEFAULT NULL COMMENT 'PR公司名称',
    `department` varchar(256) DEFAULT NULL COMMENT '部门',
    `contract_id` varchar(256) DEFAULT NULL COMMENT '供应商编码',
    `contract_name` varchar(256) DEFAULT NULL COMMENT '供应商名称',
    `order_placer` varchar(256) DEFAULT NULL COMMENT '下单人',
    `order_phone_number` varchar(256) DEFAULT NULL COMMENT '下单人电话',
    `order_time` datetime DEFAULT NULL COMMENT '下单时间',
    `reconciler` varchar(256) DEFAULT NULL COMMENT '对账人',
    `consignee` varchar(256) DEFAULT NULL COMMENT '收货负责人',
    `receipt_reviewer` varchar(256) DEFAULT NULL COMMENT '收货审核人',
    `payment_manager` varchar(256) DEFAULT NULL COMMENT '支付负责人',
    `acceptance_method` varchar(256) DEFAULT NULL COMMENT '验收方式',
    `settlement_method` varchar(256) DEFAULT NULL COMMENT '结算方式',
    `request_delivery_date` datetime DEFAULT NULL COMMENT '要求到货日期',
    `total_order_amount` decimal(10,2) DEFAULT NULL COMMENT '订单总金额',
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '类名',
    `creator_id` varchar(64) NOT NULL COMMENT '创建人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '数据所有者',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
    `org_id` varchar(64) NOT NULL COMMENT '业务组织ID',
    `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status` int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number` varchar(64) DEFAULT NULL COMMENT '编码',
    `order_pay_day` decimal(10,2) DEFAULT NULL COMMENT '订单待支付',
    `order_confirmation_time` datetime DEFAULT NULL COMMENT '订单确认时间',
    `order_approval_time` datetime DEFAULT NULL COMMENT '订单审批时间',
    `paid_time` datetime DEFAULT NULL COMMENT '支付时间',
    `invoicing_time` datetime DEFAULT NULL COMMENT '开票时间',
    `application_for_invoicing_time` datetime DEFAULT NULL COMMENT '申请开票时间',
    `reconciliation_confirmation_time` datetime DEFAULT NULL COMMENT '对账确认时间',
    `reconciliation_application_time` datetime DEFAULT NULL COMMENT '对账申请时间',
    `used_time` int(4) DEFAULT NULL COMMENT ' 发货耗时',
    `time_of_delivery` datetime DEFAULT NULL COMMENT '订单最后一次交货时间',
    `time_of_last_receipt` datetime DEFAULT NULL COMMENT '订单最后一次确认收货时间',
    `order_state` varchar(256) DEFAULT NULL COMMENT '订单状态',
    `return_amount` decimal(10,2) DEFAULT NULL COMMENT '退货金额',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='采购-商城集采订单（总表）';

CREATE TABLE IF NOT EXISTS `ncf_form_contract_change` (
    `change_id` varchar(256) DEFAULT NULL COMMENT '变更编号',
    `change_title` varchar(256) DEFAULT NULL COMMENT '变更标题',
    `change_type` varchar(256) DEFAULT NULL COMMENT '变更类型',
    `change_request_date` datetime DEFAULT NULL COMMENT '变更申请日期',
    `this_change_amount` decimal(10,2) DEFAULT NULL COMMENT '本次变更金额',
    `cumulative_change_amount` decimal(10,2) DEFAULT NULL COMMENT '累计变更金额',
    `cumulative_change_rate` varchar(256) DEFAULT NULL COMMENT '累计变更比率',
    `contact_amount_after_change` decimal(10,2) DEFAULT NULL COMMENT '变更后合同承诺总价（总目标值）',
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '类名',
    `creator_id` varchar(64) NOT NULL COMMENT '创建人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '数据所有者',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
    `org_id` varchar(64) NOT NULL COMMENT '业务组织ID',
    `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status` int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number` varchar(64) DEFAULT NULL COMMENT '编码',
    `main_table_id` varchar(64) DEFAULT NULL COMMENT '主表ID',
    `contract_number` varchar(256) DEFAULT NULL COMMENT '合同编号',
    `contract_name` varchar(256) DEFAULT NULL COMMENT '合同名称',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='合同变更信息表';

CREATE TABLE IF NOT EXISTS `ncf_form_contract_claim` (
    `claim_id` varchar(256) DEFAULT NULL COMMENT '索赔编号',
    `claim_title` varchar(256) DEFAULT NULL COMMENT '索赔标题',
    `claim_status` varchar(256) DEFAULT NULL COMMENT '索赔状态',
    `claim_direction` varchar(256) DEFAULT NULL COMMENT '索赔方向',
    `claim_process_time` int(11) DEFAULT NULL COMMENT '索赔处理时间',
    `claim_request_time` datetime DEFAULT NULL COMMENT '索赔申请时间',
    `cumulative_claim_amount` decimal(10,2) DEFAULT NULL COMMENT '累计索赔金额（含本次）',
    `total_claim_pct_of_orig_price` varchar(256) DEFAULT NULL COMMENT '总累计索赔占原合同价%',
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '类名',
    `creator_id` varchar(64) NOT NULL COMMENT '创建人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '数据所有者',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
    `org_id` varchar(64) NOT NULL COMMENT '业务组织ID',
    `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status` int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number` varchar(64) DEFAULT NULL COMMENT '编码',
    `main_table_id` varchar(64) DEFAULT NULL COMMENT '主表ID',
    `contract_number` varchar(256) DEFAULT NULL COMMENT '合同编号',
    `contract_name` varchar(256) DEFAULT NULL COMMENT '合同名称',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='合同索赔信息表';

CREATE TABLE IF NOT EXISTS `ncf_form_contract_extend_info` (
    `procurement_org_name` varchar(256) DEFAULT NULL COMMENT '采购组织',
    `procurement_org_id` varchar(256) DEFAULT NULL COMMENT '采购组织ID',
    `procurement_group_name` varchar(256) DEFAULT NULL COMMENT '采购组',
    `procurement_group_id` varchar(256) DEFAULT NULL COMMENT '采购组ID',
    `business_rsp_user` varchar(256) DEFAULT NULL COMMENT '商务负责人',
    `business_rsp_user_id` varchar(256) DEFAULT NULL COMMENT '商务负责人ID',
    `technical_rsp_user` varchar(256) DEFAULT NULL COMMENT '技术负责人',
    `technical_rsp_user_id` varchar(256) DEFAULT NULL COMMENT '技术负责人ID',
    `recommendation_basis` varchar(256) DEFAULT NULL COMMENT '推荐依据',
    `negotiate_save_amount` decimal(10,2) DEFAULT NULL COMMENT '节省总金额（RMB）',
    `sum_save_amount` decimal(10,2) DEFAULT NULL COMMENT '渠道优化节省金额',
    `compare_save_amount` decimal(10,2) DEFAULT NULL COMMENT '谈判节省金额',
    `channel_save_amount` decimal(10,2) DEFAULT NULL COMMENT '与立项相比节省金额',
    `optimize_save_amount` decimal(10,2) DEFAULT NULL COMMENT '优化采购节省金额',
    `is_process_amount` bit(1) DEFAULT NULL COMMENT '是否办理履约保证金',
    `prcess_amount_pay_way` varchar(256) DEFAULT NULL COMMENT '保证金支付方式',
    `prcess_amount` varchar(256) DEFAULT NULL COMMENT '保证金',
    `account_name` varchar(256) DEFAULT NULL COMMENT '账户名称',
    `bank_name` varchar(256) DEFAULT NULL COMMENT '银行账号',
    `bank_account` varchar(256) DEFAULT NULL COMMENT '开户银行',
    `bank_code` varchar(256) DEFAULT NULL COMMENT '银行代码',
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '类名',
    `creator_id` varchar(64) NOT NULL COMMENT '创建人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '数据所有者',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
    `org_id` varchar(64) NOT NULL COMMENT '业务组织ID',
    `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status` int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number` varchar(64) DEFAULT NULL COMMENT '编码',
    `main_table_id` varchar(64) DEFAULT NULL COMMENT '主表ID',
    `contract_number` varchar(256) DEFAULT NULL COMMENT '合同编号',
    `contract_name` varchar(256) DEFAULT NULL COMMENT '合同名称',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='合同拓展信息';

CREATE TABLE IF NOT EXISTS `ncf_form_contract_info` (
    `contract_number` varchar(256) DEFAULT NULL COMMENT '合同编号',
    `is_fream` varchar(2) DEFAULT NULL COMMENT '是否框架合同',
    `purchase_applicant` varchar(256) DEFAULT NULL COMMENT '采购申请号',
    `contract_name` varchar(256) DEFAULT NULL COMMENT '合同名称',
    `status_name` varchar(256) DEFAULT NULL COMMENT '合同执行状态',
    `fream_residue_amount` decimal(10,2) DEFAULT NULL COMMENT '框架合同剩余金额',
    `type` varchar(256) DEFAULT NULL COMMENT '合同类型',
    `procurement_order_number` varchar(256) DEFAULT NULL COMMENT '采购订单号',
    `project_code` varchar(256) DEFAULT NULL COMMENT '采购立项号',
    `procurement_amount` decimal(10,2) DEFAULT NULL COMMENT '采购立项金额',
    `supplier_name` varchar(256) DEFAULT NULL COMMENT '供应商',
    `factory_name` varchar(256) DEFAULT NULL COMMENT '工厂',
    `business_rsp_user` varchar(256) DEFAULT NULL COMMENT '商务负责人',
    `change_money` decimal(10,2) DEFAULT NULL COMMENT '变更金额',
    `change_percent` varchar(256) DEFAULT NULL COMMENT '变更比例',
    `pay_money` decimal(10,2) DEFAULT NULL COMMENT '支付金额',
    `is_contract_terminate` bit(1) DEFAULT NULL COMMENT '是否合同终止',
    `claim_amount` decimal(10,2) DEFAULT NULL COMMENT '索赔金额',
    `claim_percent` varchar(256) DEFAULT NULL COMMENT '索赔比例',
    `terminate_amount` decimal(10,2) DEFAULT NULL COMMENT '终止金额',
    `terminate_percent` varchar(256) DEFAULT NULL COMMENT '终止比例',
    `fream_begin_time` datetime DEFAULT NULL COMMENT '框架开始时间',
    `fream_end_time` datetime DEFAULT NULL COMMENT '框架结束时间',
    `fream_used_amount` decimal(10,2) DEFAULT NULL COMMENT '框架合同已使用金额',
    `is_fream_period` bit(1) DEFAULT NULL COMMENT '是否框架有效期内',
    `pay_percent` varchar(256) DEFAULT NULL COMMENT '支付比例',
    `procurement_way` varchar(256) DEFAULT NULL COMMENT '采购方式',
    `procurement_cycle` varchar(256) DEFAULT NULL COMMENT '采购周期',
    `amount_saved` decimal(10,2) DEFAULT NULL COMMENT '节约金额',
    `business_activity_type` varchar(256) DEFAULT NULL COMMENT '商务活动类型',
    `end_procurement_way` varchar(256) DEFAULT NULL COMMENT '最终采购方式',
    `business_file_type` varchar(256) DEFAULT NULL COMMENT '商务文件类型',
    `estimated_start_time` datetime DEFAULT NULL COMMENT '预计合同开始日期',
    `estimated_end_time` datetime DEFAULT NULL COMMENT '预计合同结束日期',
    `pay_way` varchar(256) DEFAULT NULL COMMENT '付款方式',
    `execution_status_name` varchar(256) DEFAULT NULL COMMENT '合同履约状态',
    `object_type` varchar(256) DEFAULT NULL COMMENT '标的类别',
    `type_percent` varchar(256) DEFAULT NULL COMMENT '类别占比（%）',
    `approved_price` decimal(10,2) DEFAULT NULL COMMENT '审批价格（RMB）',
    `final_price` decimal(10,2) DEFAULT NULL COMMENT '最终价格（原币）',
    `actual_start_time` datetime DEFAULT NULL COMMENT '实际合同开始日期',
    `actual_end_time` datetime DEFAULT NULL COMMENT '实际合同结束日期',
    `estimated_delivery_time` datetime DEFAULT NULL COMMENT '预计合同交付日期',
    `acceptance_results` varchar(256) DEFAULT NULL COMMENT '验收结果',
    `actual_acceptance_times` datetime DEFAULT NULL COMMENT '实际验收日期',
    `is_public_launch` bit(1) DEFAULT NULL COMMENT '是否发布启动公示',
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '类名',
    `creator_id` varchar(64) NOT NULL COMMENT '创建人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '数据所有者',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
    `org_id` varchar(64) NOT NULL COMMENT '业务组织ID',
    `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status` int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number` varchar(64) DEFAULT NULL COMMENT '编码',
    `is_performance_bond` bit(1) DEFAULT NULL COMMENT '是否办理履约保证金',
    `margin_payment_method` varchar(256) DEFAULT NULL COMMENT '保证金支付方式',
    `security_deposit` decimal(10,2) DEFAULT NULL COMMENT '保证金',
    `ia_calculation` bit(1) DEFAULT NULL COMMENT '是否参与计算	',
    `project_end_time` datetime DEFAULT NULL COMMENT '采购立项审批完成时间',
    `recommend_end_time` datetime DEFAULT NULL COMMENT '合同推荐审批完成时间',
    `is_biz_recommend` bit(1) DEFAULT NULL COMMENT '商务是否推荐供应商',
    `price_model` varchar(255) DEFAULT NULL COMMENT '价格模式',
    `subdivision` varchar(255) DEFAULT NULL COMMENT '所属部处',
    `is_fill_onetime_acceptance` varchar(255) NOT NULL COMMENT '是否填写一次验收合格',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='合同主表信息';

CREATE TABLE IF NOT EXISTS `ncf_form_contract_line_info` (
    `line_number` varchar(256) DEFAULT NULL COMMENT '合同行项目',
    `num_count` decimal(10,2) DEFAULT NULL COMMENT '数量',
    `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价（含税）',
    `tax_rate` varchar(256) DEFAULT NULL COMMENT '税率',
    `list_price` decimal(10,2) DEFAULT NULL COMMENT '原始价格',
    `update_price` decimal(10,2) DEFAULT NULL COMMENT '修改价格',
    `planned_delivery_date` datetime DEFAULT NULL COMMENT '计划交货日期',
    `procurement_applicant_number` varchar(256) DEFAULT NULL COMMENT '采购申请号',
    `procurement_applicant_line_number` varchar(256) DEFAULT NULL COMMENT '采购申请行号',
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '类名',
    `creator_id` varchar(64) NOT NULL COMMENT '创建人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '数据所有者',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
    `org_id` varchar(64) NOT NULL COMMENT '业务组织ID',
    `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status` int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number` varchar(64) DEFAULT NULL COMMENT '编码',
    `main_table_id` varchar(64) DEFAULT NULL COMMENT '主表ID',
    `final_price` decimal(10,2) DEFAULT NULL COMMENT '最终价格',
    `contract_number` varchar(256) DEFAULT NULL COMMENT '合同编号',
    `contract_name` varchar(256) DEFAULT NULL COMMENT '合同名称',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='合同行项目信息';

CREATE TABLE IF NOT EXISTS `ncf_form_contract_pay_milestone` (
    `milestone_desc` varchar(256) DEFAULT NULL COMMENT '里程碑业务描述',
    `in_out_payment` bit(1) DEFAULT NULL COMMENT '是否涉及境外付款',
    `contract_type` varchar(256) DEFAULT NULL COMMENT '合同类型',
    `payment_type` varchar(256) DEFAULT NULL COMMENT '支付类型',
    `est_payment_date` datetime DEFAULT NULL COMMENT '预计付款时间',
    `attachment_req` varchar(256) DEFAULT NULL COMMENT '附件要求',
    `payment_ratio` varchar(256) DEFAULT NULL COMMENT '支付比例',
    `contract_agreed_payment` decimal(10,2) DEFAULT NULL COMMENT '合同约定支付金额',
    `price_total_fixed` bit(1) DEFAULT NULL COMMENT '价格属性总价是否固定',
    `invoice_type` varchar(256) DEFAULT NULL COMMENT '开票类型',
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '类名',
    `creator_id` varchar(64) NOT NULL COMMENT '创建人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '数据所有者',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
    `org_id` varchar(64) NOT NULL COMMENT '业务组织ID',
    `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status` int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number` varchar(64) DEFAULT NULL COMMENT '编码',
    `main_table_id` varchar(64) DEFAULT NULL COMMENT '主表ID',
    `contract_number` varchar(256) DEFAULT NULL COMMENT '合同编号',
    `contract_name` varchar(256) DEFAULT NULL COMMENT '合同名称',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='合同支付里程碑（计划）';

CREATE TABLE IF NOT EXISTS `ncf_form_contract_supplier_record` (
    `contract_number` varchar(256) DEFAULT NULL COMMENT '合同编号',
    `supplier_id` varchar(256) DEFAULT NULL COMMENT '供应商ID',
    `supplier_name` varchar(256) DEFAULT NULL COMMENT '供应商名称',
    `supplier_from` varchar(256) DEFAULT NULL COMMENT '供应商来源',
    `is_inquiry_supplier` bit(1) DEFAULT NULL COMMENT '是否询价供应商',
    `is_winner_supplier` bit(1) DEFAULT NULL COMMENT '是否中标供应商',
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '类名',
    `creator_id` varchar(64) NOT NULL COMMENT '创建人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '数据所有者',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
    `org_id` varchar(64) NOT NULL COMMENT '业务组织ID',
    `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status` int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number` varchar(64) DEFAULT NULL COMMENT '编码',
    `main_table_id` varchar(64) DEFAULT NULL COMMENT '主表ID',
    `contract_name` varchar(256) DEFAULT NULL COMMENT '合同名称',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='合同供应商记录表';

CREATE TABLE IF NOT EXISTS `ncf_form_contract_termination` (
    `is_pre_sign_termination` bit(1) DEFAULT NULL COMMENT '是否签约前终止',
    `termination_request_date` datetime DEFAULT NULL COMMENT '终止申请日期',
    `contract_termination_amount` decimal(10,2) DEFAULT NULL COMMENT '合同终止金额',
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '类名',
    `creator_id` varchar(64) NOT NULL COMMENT '创建人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '数据所有者',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
    `org_id` varchar(64) NOT NULL COMMENT '业务组织ID',
    `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status` int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number` varchar(64) DEFAULT NULL COMMENT '编码',
    `main_table_id` varchar(64) DEFAULT NULL COMMENT '主表ID',
    `contract_number` varchar(256) DEFAULT NULL COMMENT '合同编号',
    `contract_name` varchar(256) DEFAULT NULL COMMENT '合同名称',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='合同终止信息表';

CREATE TABLE IF NOT EXISTS `ncf_form_purchase_request` (
    `code` varchar(256) DEFAULT NULL COMMENT '采购申请单编码',
    `name` tinytext COMMENT '申请单名称',
    `project_end_time` datetime DEFAULT NULL COMMENT '采购立项完成时间',
    `project_code` varchar(256) DEFAULT NULL COMMENT '采购立项号',
    `file_name` varchar(256) DEFAULT NULL COMMENT '文件名称',
    `state` varchar(256) DEFAULT NULL COMMENT '申请单状态',
    `type` varchar(256) DEFAULT NULL COMMENT '申请单类型',
    `source` varchar(256) DEFAULT NULL COMMENT '申请单来源',
    `money` decimal(10,2) DEFAULT NULL COMMENT '采购申请金额（元）',
    `rate` decimal(10,2) DEFAULT NULL COMMENT '汇率',
    `estimated_begin_time` datetime DEFAULT NULL COMMENT '预计开工时间',
    `warranty_level` varchar(256) DEFAULT NULL COMMENT '质保等级',
    `applicant_dept_id` varchar(255) DEFAULT NULL COMMENT '申请部门id',
    `applicant_dept` varchar(256) DEFAULT NULL COMMENT '申请部门',
    `applicant_user_id` varchar(255) DEFAULT NULL COMMENT '申请人id',
    `applicant_user` varchar(256) DEFAULT NULL COMMENT '申请人',
    `currency` varchar(256) DEFAULT NULL COMMENT '币种',
    `with_safety` bit(1) DEFAULT NULL COMMENT '与现场安全相关',
    `purchase_plan_code` varchar(256) DEFAULT NULL COMMENT '采购计划号',
    `bk_dept` varchar(256) DEFAULT NULL COMMENT '归口部门',
    `bk_manage` varchar(256) DEFAULT NULL COMMENT '归口管理',
    `suggest_purchase_way` varchar(256) DEFAULT NULL COMMENT '建议采购方式',
    `purchase_content` tinytext COMMENT '采购内容',
    `rec_sup_list` varchar(256) DEFAULT NULL COMMENT '推荐供应商名单',
    `rec_pt_sup_list` varchar(256) DEFAULT NULL COMMENT '推荐潜在供应商名单',
    `is_frame_contrac` bit(1) DEFAULT NULL COMMENT '是否有匹配的框架合同',
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '类名',
    `creator_id` varchar(64) NOT NULL COMMENT '创建人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '数据所有者',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
    `org_id` varchar(64) NOT NULL COMMENT '业务组织ID',
    `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status` int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number` varchar(64) DEFAULT NULL COMMENT '编码',
    `contract_id` varchar(64) DEFAULT NULL COMMENT '框架合同编码',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='采购申请主表';

CREATE TABLE IF NOT EXISTS `ncf_form_purchase_request_attachment` (
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
    `creator_id` varchar(64) DEFAULT NULL COMMENT '创建人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) DEFAULT NULL COMMENT '平台ID',
    `org_id` varchar(64) DEFAULT NULL COMMENT '业务组织Id',
    `status` int(11) DEFAULT NULL COMMENT '状态',
    `logic_status` int(11) DEFAULT NULL COMMENT '逻辑删除字段',
    `code` varchar(64) DEFAULT NULL COMMENT '采购申请编号',
    `node` varchar(64) DEFAULT NULL COMMENT '节点',
    `attachment_name` varchar(255) DEFAULT NULL COMMENT '文件名',
    `classification_level` varchar(64) DEFAULT NULL COMMENT '文件密级',
    `secrecy_term` varchar(64) DEFAULT NULL COMMENT '保密期限',
    `document_type` varchar(64) DEFAULT NULL COMMENT '文档类型',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='采购申请附件';

CREATE TABLE IF NOT EXISTS `ncf_form_purchase_request_detail` (
    `project_id` varchar(256) DEFAULT NULL COMMENT '行项目id',
    `internal_order` varchar(256) DEFAULT NULL COMMENT '内部订单',
    `item` varchar(256) DEFAULT NULL COMMENT '物料',
    `item_group` varchar(256) DEFAULT NULL COMMENT '物料组',
    `general_ledger_subject` varchar(256) DEFAULT NULL COMMENT '总账科目',
    `asset` varchar(256) DEFAULT NULL COMMENT '资产',
    `required_quantity` varchar(256) DEFAULT NULL COMMENT '需求数量',
    `unit` varchar(256) DEFAULT NULL COMMENT '单位',
    `delivery_time` datetime DEFAULT NULL COMMENT '交货时间',
    `unit_price` varchar(256) DEFAULT NULL COMMENT '单价',
    `total_price` varchar(256) DEFAULT NULL COMMENT '总价',
    `local_currency_amount` decimal(10,2) DEFAULT NULL COMMENT '本位币金额',
    `cost_center` varchar(256) DEFAULT NULL COMMENT '成本中心',
    `project_id_name` varchar(256) DEFAULT NULL COMMENT '项目编号/名称',
    `wbs_id` varchar(256) DEFAULT NULL COMMENT 'WBS编号',
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '类名',
    `creator_id` varchar(64) NOT NULL COMMENT '创建人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '数据所有者',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
    `org_id` varchar(64) NOT NULL COMMENT '业务组织ID',
    `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status` int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number` varchar(64) DEFAULT NULL COMMENT '编码',
    `main_table_id` varchar(64) DEFAULT NULL COMMENT '主表ID',
    `project_code` varchar(64) DEFAULT NULL COMMENT '采购申请号',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='采购申请行项目表';

CREATE TABLE IF NOT EXISTS `pms_ncf_form_purch_order_collect` (
    `order_number` varchar(256) DEFAULT NULL COMMENT '订单编号',
    `po_order_number` varchar(256) DEFAULT NULL COMMENT 'PO订单号',
    `commerce_channel_order_number` varchar(256) DEFAULT NULL COMMENT '电商渠道订单号',
    `enterprise_name` varchar(256) DEFAULT NULL COMMENT '企业名称',
    `pr_company_name` varchar(256) DEFAULT NULL COMMENT 'PR公司名称',
    `department` varchar(256) DEFAULT NULL COMMENT '部门',
    `contract_id` varchar(256) DEFAULT NULL COMMENT '供应商编码',
    `contract_name` varchar(256) DEFAULT NULL COMMENT '供应商名称',
    `order_placer` varchar(256) DEFAULT NULL COMMENT '下单人',
    `order_phone_number` varchar(256) DEFAULT NULL COMMENT '下单人电话',
    `order_time` datetime DEFAULT NULL COMMENT '下单时间',
    `reconciler` varchar(256) DEFAULT NULL COMMENT '对账人',
    `consignee` varchar(256) DEFAULT NULL COMMENT '收货负责人',
    `receipt_reviewer` varchar(256) DEFAULT NULL COMMENT '收货审核人',
    `payment_manager` varchar(256) DEFAULT NULL COMMENT '支付负责人',
    `acceptance_method` varchar(256) DEFAULT NULL COMMENT '验收方式',
    `settlement_method` varchar(256) DEFAULT NULL COMMENT '结算方式',
    `request_delivery_date` datetime DEFAULT NULL COMMENT '要求到货日期',
    `total_order_amount` decimal(10,2) DEFAULT NULL COMMENT '订单总金额',
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '类名',
    `creator_id` varchar(64) NOT NULL COMMENT '创建人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '数据所有者',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
    `org_id` varchar(64) NOT NULL COMMENT '业务组织ID',
    `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status` int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number` varchar(64) DEFAULT NULL COMMENT '编码',
    `order_pay_day` decimal(10,2) DEFAULT NULL COMMENT '订单待支付',
    `order_confirmation_time` datetime DEFAULT NULL COMMENT '订单确认时间',
    `order_approval_time` datetime DEFAULT NULL COMMENT '订单审批时间',
    `paid_time` datetime DEFAULT NULL COMMENT '支付时间',
    `invoicing_time` datetime DEFAULT NULL COMMENT '开票时间',
    `application_for_invoicing_time` datetime DEFAULT NULL COMMENT '申请开票时间',
    `reconciliation_confirmation_time` datetime DEFAULT NULL COMMENT '对账确认时间',
    `reconciliation_application_time` datetime DEFAULT NULL COMMENT '对账申请时间',
    `used_time` int(4) DEFAULT NULL COMMENT ' 发货耗时',
    `time_of_delivery` datetime DEFAULT NULL COMMENT '订单最后一次交货时间',
    `time_of_last_receipt` datetime DEFAULT NULL COMMENT '订单最后一次确认收货时间',
    `order_state` varchar(256) DEFAULT NULL COMMENT '订单状态',
    `return_amount` decimal(10,2) DEFAULT NULL COMMENT '退货金额',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='采购-商城集采订单（总表）';

CREATE TABLE IF NOT EXISTS `pms_ncf_form_purch_order_detail` (
    `contract_number` varchar(256) DEFAULT NULL COMMENT '框架协议号',
    `tax_not_included` decimal(10,2) DEFAULT NULL COMMENT '不含税金额',
    `tax` varchar(256) DEFAULT NULL COMMENT '增值税率',
    `total_price` varchar(256) DEFAULT NULL COMMENT '总价',
    `unit_price` varchar(256) DEFAULT NULL COMMENT '单价',
    `unit` varchar(256) DEFAULT NULL COMMENT '计量单位',
    `purchase_quantity` varchar(256) DEFAULT NULL COMMENT '采购数量',
    `iten_type` varchar(256) DEFAULT NULL COMMENT '商品类型',
    `order_number` varchar(256) DEFAULT NULL COMMENT '订单编号',
    `contract_name` varchar(256) DEFAULT NULL COMMENT '供应商名称',
    `return_amount` decimal(10,2) DEFAULT NULL COMMENT '退货金额',
    `enterprise_name` varchar(256) DEFAULT NULL COMMENT '企业名称',
    `order_placer` varchar(256) DEFAULT NULL COMMENT '下单人',
    `order_time` datetime DEFAULT NULL COMMENT '下单时间',
    `time_of_delivery` datetime DEFAULT NULL COMMENT '订单最后一次交货时间',
    `time_of_last_receipt` datetime DEFAULT NULL COMMENT '订单最后一次确认收货时间',
    `used_time` int(4) DEFAULT NULL COMMENT '发货耗时',
    `reconciliation_application_time` datetime DEFAULT NULL COMMENT '对账申请时间',
    `reconciliation_confirmation_time` datetime DEFAULT NULL COMMENT '对账确认时间',
    `application_for_invoicing_time` datetime DEFAULT NULL COMMENT '申请开票时间',
    `invoicing_time` datetime DEFAULT NULL COMMENT '开票时间',
    `paid_time` datetime DEFAULT NULL COMMENT '支付时间',
    `po_order_number` varchar(256) DEFAULT NULL COMMENT 'PO订单号',
    `department` varchar(256) DEFAULT NULL COMMENT '部门',
    `contract_id` varchar(256) DEFAULT NULL COMMENT '供应商编码',
    `payment_manager` varchar(256) DEFAULT NULL COMMENT '支付负责人',
    `acceptance_method` varchar(256) DEFAULT NULL COMMENT '验收方式',
    `order_total_amount` decimal(10,2) DEFAULT NULL COMMENT '订单总金额（含税含费）',
    `purch_req_doc_code` varchar(256) DEFAULT NULL COMMENT '采购申请号',
    `pr_project_id` varchar(256) DEFAULT NULL COMMENT 'PR行项目',
    `order_state` varchar(256) DEFAULT NULL COMMENT '订单状态',
    `commodity_background_category` varchar(256) DEFAULT NULL COMMENT '商品后台类目',
    `item_coding` varchar(256) DEFAULT NULL COMMENT '单品编码',
    `item_name` varchar(256) DEFAULT NULL COMMENT '单品名称',
    `e_commerce_order_number` varchar(256) DEFAULT NULL COMMENT '电商订单编号',
    `suborder_number` varchar(256) DEFAULT NULL COMMENT '子订单号',
    `order_amount` decimal(10,2) DEFAULT NULL COMMENT '订单金额',
    `amount_payable` decimal(10,2) DEFAULT NULL COMMENT '应付金额',
    `settlement_status` varchar(256) DEFAULT NULL COMMENT '结算状态',
    `order_confirmation_time` datetime DEFAULT NULL COMMENT '订单确认时间',
    `order_approval_time` datetime DEFAULT NULL COMMENT '订单审批时间',
    `pr_company_name` varchar(256) DEFAULT NULL COMMENT 'PR公司名称',
    `commerce_channel_order_number` varchar(256) DEFAULT NULL COMMENT '电商渠道订单号',
    `reconciler` varchar(256) DEFAULT NULL COMMENT '对账人',
    `consignee` varchar(256) DEFAULT NULL COMMENT '收货负责人',
    `receipt_reviewer` varchar(256) DEFAULT NULL COMMENT '收货审核人',
    `settlement_method` varchar(256) DEFAULT NULL COMMENT '结算方式',
    `request_delivery_date` datetime DEFAULT NULL COMMENT '要求到货日期',
    `order_phone_number` varchar(256) DEFAULT NULL COMMENT '下单人电话',
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '类名',
    `creator_id` varchar(64) NOT NULL COMMENT '创建人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '数据所有者',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
    `org_id` varchar(64) NOT NULL COMMENT '业务组织ID',
    `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status` int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number` varchar(64) DEFAULT NULL COMMENT '编码',
    `order_pay_day` varchar(256) DEFAULT NULL COMMENT '订单待支付',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商城集采订单（明细表）';

CREATE TABLE IF NOT EXISTS `ncf_form_supplier_review` (
    `contract_id` varchar(256) DEFAULT NULL COMMENT '供应商编码',
    `contract_name` varchar(256) DEFAULT NULL COMMENT '供应商名称',
    `application_number` varchar(256) DEFAULT NULL COMMENT '申请编号',
    `project_category` varchar(256) DEFAULT NULL COMMENT '项目类别',
    `project_name` varchar(256) DEFAULT NULL COMMENT '项目名称/采购任务名称',
    `procure_number` varchar(256) DEFAULT NULL COMMENT '采购包号',
    `applicant_type` varchar(256) DEFAULT NULL COMMENT '申请类型',
    `applicant` varchar(256) DEFAULT NULL COMMENT '申请人',
    `declaring_company` varchar(256) DEFAULT NULL COMMENT '申请公司',
    `state` varchar(256) DEFAULT NULL COMMENT '状态',
    `reason` varchar(256) DEFAULT NULL COMMENT '原因',
    `process_step` varchar(256) DEFAULT NULL COMMENT '流程环节',
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '类名',
    `creator_id` varchar(64) NOT NULL COMMENT '创建人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '数据所有者',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
    `org_id` varchar(64) NOT NULL COMMENT '业务组织ID',
    `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status` int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number` varchar(64) DEFAULT NULL COMMENT '编码',
    `approval_completion_time` datetime DEFAULT NULL COMMENT '审批完成时间',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='资审供应商信息表';

CREATE TABLE IF NOT EXISTS `pms_ncf_purch_index` (
    `remarks` varchar(256) DEFAULT NULL COMMENT '备注说明',
    `chain_ratio` varchar(256) DEFAULT NULL COMMENT '环比',
    `year_basis` varchar(256) DEFAULT NULL COMMENT '同比',
    `goal` varchar(256) DEFAULT NULL COMMENT '目标',
    `current_month` varchar(256) DEFAULT NULL COMMENT '本月',
    `last_month` varchar(256) DEFAULT NULL COMMENT '上月',
    `index_name` varchar(256) DEFAULT NULL COMMENT '指标名称',
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '类名',
    `creator_id` varchar(64) DEFAULT NULL COMMENT '创建人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '数据所有者',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) DEFAULT NULL COMMENT '平台ID',
    `org_id` varchar(64) DEFAULT NULL COMMENT '业务组织ID',
    `status` int(11) DEFAULT '1' COMMENT '状态',
    `logic_status` int(11) DEFAULT '1' COMMENT '逻辑删除字段',
    `number` varchar(64) DEFAULT NULL COMMENT '编码',
    `index_year` varchar(256) DEFAULT NULL COMMENT '年份',
    `index_month` varchar(256) DEFAULT NULL COMMENT '月份',
    `indicator_ownership` varchar(256) DEFAULT NULL COMMENT '指标所属领域',
    `index_classification` varchar(256) DEFAULT NULL COMMENT '指标分类',
    `indicator_state` varchar(256) DEFAULT NULL COMMENT '指标状态',
    `index_order` int(4) DEFAULT NULL COMMENT '序号',
    `up_to_this_month` varchar(255) DEFAULT NULL COMMENT '截至到本月',
    `up_to_last_month` varchar(255) DEFAULT NULL COMMENT '截至到上月',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='采购供应指标';

CREATE TABLE IF NOT EXISTS `pms_ncf_purch_project_implementation` (
    `process_name` varchar(256) DEFAULT NULL COMMENT '流程名称',
    `promoter` varchar(256) DEFAULT NULL COMMENT '发起人',
    `initiation_time` datetime DEFAULT NULL COMMENT '发起时间',
    `purch_req_ecp_code` varchar(256) DEFAULT NULL COMMENT '采购立项申请号',
    `purch_req_doc_code` varchar(256) DEFAULT NULL COMMENT '采购申请号',
    `applicant` varchar(256) DEFAULT NULL COMMENT '申请人',
    `apply_department` varchar(256) DEFAULT NULL COMMENT '需求部门',
    `project_name` varchar(256) DEFAULT NULL COMMENT '项目名称',
    `purch_req_end_time` datetime DEFAULT NULL COMMENT '采购申请完成时间',
    `purch_req_amount` decimal(10,0) DEFAULT NULL COMMENT '采购立项申请金额',
    `biz_respons` varchar(256) DEFAULT NULL COMMENT '商务人员',
    `tech_respons` varchar(256) DEFAULT NULL COMMENT '技术人员',
    `financial_staff` varchar(256) DEFAULT NULL COMMENT '财务人员',
    `others` varchar(256) DEFAULT NULL COMMENT '其他人员',
    `is_collection_purch` bit(1) DEFAULT NULL COMMENT '是否属于应集采范围',
    `expected_contract_signing_time` varchar(256) DEFAULT NULL COMMENT '期望合同签订时间',
    `purch_plan_number` varchar(256) DEFAULT NULL COMMENT '采购计划需求编号',
    `contract_type` varchar(256) DEFAULT NULL COMMENT '合同类型',
    `contract_state` varchar(256) DEFAULT NULL COMMENT '合同状态',
    `purch_type` varchar(256) DEFAULT NULL COMMENT '采购类型',
    `purch_method` varchar(256) DEFAULT NULL COMMENT '采购方式',
    `next_step_work_arrangement` varchar(256) DEFAULT NULL COMMENT '下一步工作安排',
    `concerns` varchar(256) DEFAULT NULL COMMENT '关注事项',
    `used_time` varchar(256) DEFAULT NULL COMMENT '已经耗时',
    `first_distribution` datetime DEFAULT NULL COMMENT '一级分发',
    `secondary_distribution` datetime DEFAULT NULL COMMENT '二级分发',
    `accept_confirmation` datetime DEFAULT NULL COMMENT '接受确认',
    `purch_start` datetime DEFAULT NULL COMMENT '采购启动发起',
    `purch_start_approval` datetime DEFAULT NULL COMMENT '采购启动审批',
    `inq_issuance` datetime DEFAULT NULL COMMENT '询价签发',
    `quote_end` datetime DEFAULT NULL COMMENT '报价截止时间',
    `open_quote` datetime DEFAULT NULL COMMENT '开启报价时间',
    `review_time` datetime DEFAULT NULL COMMENT '评审时间',
    `review_out_time` datetime DEFAULT NULL COMMENT '公示发布时间',
    `upm_approval_in_progress` datetime DEFAULT NULL COMMENT 'UPM审批中',
    `upm_approval_complete` datetime DEFAULT NULL COMMENT 'UPM审批完成',
    `send_sap` datetime DEFAULT NULL COMMENT '发送SAP',
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '类名',
    `creator_id` varchar(64) NOT NULL COMMENT '创建人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '数据所有者',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
    `org_id` varchar(64) NOT NULL COMMENT '业务组织ID',
    `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status` int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number` varchar(64) DEFAULT NULL COMMENT '编码',
    `contract_number` varchar(64) DEFAULT NULL COMMENT '合同编号',
    `contract_name` varchar(255) DEFAULT NULL COMMENT '合同名称',
    `is_public_launch` varchar(255) DEFAULT NULL COMMENT '是否发布启动公示',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='采购项目实施表';

CREATE TABLE IF NOT EXISTS `pms_non_contract_proc` (
    `work_topic` varchar(256) DEFAULT NULL COMMENT '工作主题',
    `process_name` varchar(256) DEFAULT NULL COMMENT '流程名称',
    `initiation_time` datetime DEFAULT NULL COMMENT '发起时间',
    `initiator` varchar(256) DEFAULT NULL COMMENT '发起人',
    `claimant` varchar(256) DEFAULT NULL COMMENT '报销人',
    `apply_company_code` varchar(256) DEFAULT NULL COMMENT '申请公司编码',
    `apply_company_name` varchar(256) DEFAULT NULL COMMENT '申请公司名称',
    `apply_dept` varchar(256) DEFAULT NULL COMMENT '申请部门',
    `expense_company_code` varchar(256) DEFAULT NULL COMMENT '费用归属公司编码',
    `xpense_company_name` varchar(256) DEFAULT NULL COMMENT '费用归属公司名称',
    `reimbursed_amount` varchar(256) DEFAULT NULL COMMENT '报销金额',
    `req_payment_time` datetime DEFAULT NULL COMMENT '要求付款时间',
    `currency` varchar(256) DEFAULT NULL COMMENT '币种',
    `in_rmb` varchar(256) DEFAULT NULL COMMENT '折合人民币',
    `apply_reason` varchar(256) DEFAULT NULL COMMENT '申请原因',
    `expense_info` varchar(256) DEFAULT NULL COMMENT '费用信息',
    `supplier_info` varchar(256) DEFAULT NULL COMMENT '供应商信息',
    `is_internal_tx` bit(1) DEFAULT NULL COMMENT '是否内部交易',
    `internal_tx_number` varchar(256) DEFAULT NULL COMMENT '内部交易号',
    `process_status` varchar(256) DEFAULT NULL COMMENT '流程状态',
    `payment_way` varchar(256) DEFAULT NULL COMMENT '支付方式',
    `project_category` varchar(256) DEFAULT NULL COMMENT '立项类别',
    `project_code` varchar(256) DEFAULT NULL COMMENT '立项号',
    `project_name` varchar(256) DEFAULT NULL COMMENT '立项名称',
    `bk_dept` varchar(256) DEFAULT NULL COMMENT '归口部门',
    `payment_amount` varchar(256) DEFAULT NULL COMMENT '支付金额',
    `transaction_amount` varchar(256) DEFAULT NULL COMMENT '交易金额',
    `actual_payment_amount` varchar(256) DEFAULT NULL COMMENT '实际支付金额',
    `apply_number` varchar(256) DEFAULT NULL COMMENT '申请笔数',
    `reimbursement_amount` varchar(256) DEFAULT NULL COMMENT '报销金额',
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '类名',
    `creator_id` varchar(64) NOT NULL COMMENT '创建人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '数据所有者',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
    `org_id` varchar(64) NOT NULL COMMENT '业务组织ID',
    `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status` int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number` varchar(64) DEFAULT NULL COMMENT '编码',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='无合同采购表';

CREATE TABLE IF NOT EXISTS `pmsx_project_flow` (
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
    `creator_id` varchar(64) DEFAULT NULL COMMENT '创建人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) DEFAULT NULL COMMENT '平台ID',
    `org_id` varchar(64) DEFAULT NULL COMMENT '业务组织Id',
    `status` int(11) DEFAULT NULL COMMENT '状态',
    `logic_status` int(11) DEFAULT NULL COMMENT '逻辑删除字段',
    `flow_pay_person` varchar(64) DEFAULT NULL COMMENT '支付申请人',
    `flow_receive_person` varchar(64) DEFAULT NULL COMMENT '收货申请人',
    `order_number` varchar(64) DEFAULT NULL COMMENT '订单编号',
    `business_person` varchar(64) DEFAULT NULL COMMENT '商务接口人',
    `technical_person` varchar(64) DEFAULT NULL COMMENT '技术接口人',
    `bear_org` varchar(255) DEFAULT NULL COMMENT '承担部门',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='流程信息';

CREATE TABLE IF NOT EXISTS `pmsx_project_initiation` (
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
    `creator_id` varchar(64) DEFAULT NULL COMMENT '创建人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) DEFAULT NULL COMMENT '平台ID',
    `org_id` varchar(64) DEFAULT NULL COMMENT '业务组织Id',
    `status` int(11) DEFAULT NULL COMMENT '状态',
    `logic_status` int(11) DEFAULT NULL COMMENT '逻辑删除字段',
    `project_number` varchar(64) DEFAULT NULL COMMENT '立项编号',
    `project_name` varchar(255) DEFAULT NULL COMMENT '立项名称',
    `project_label` varchar(64) DEFAULT NULL COMMENT '立项标签',
    `project_type` varchar(64) DEFAULT NULL COMMENT '项目类型',
    `project_init_date` datetime DEFAULT NULL COMMENT '项目发起日期',
    `project_start_date` datetime DEFAULT NULL COMMENT '项目开始日期',
    `project_end_date` datetime DEFAULT NULL COMMENT '项目结束日期',
    `project_person` varchar(64) DEFAULT NULL COMMENT '项目责任人',
    `project_assume_center` varchar(64) DEFAULT NULL COMMENT '承担中心',
    `project_reson` varchar(1024) DEFAULT NULL COMMENT '立项理由',
    `contract_numbers` text COMMENT '合同编号拼接',
    `clue_numbers` text COMMENT '线索编号拼接',
    `project_status` varchar(64) DEFAULT NULL COMMENT '立项状态',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='项目立项';

CREATE TABLE IF NOT EXISTS `pmsx_project_inventory` (
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
    `creator_id` varchar(64) NOT NULL COMMENT '创建人',
    `modify_time` datetime NOT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `modify_id` varchar(64) NOT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
    `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
    `status` int(11) NOT NULL COMMENT '状态',
    `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
    `order_number` varchar(64) NOT NULL COMMENT '订单编号',
    `inventory_img` varchar(32) NOT NULL COMMENT '商品图',
    `inventory_name` varchar(32) NOT NULL COMMENT '商品名',
    `item_name` varchar(64) NOT NULL COMMENT '单品名称',
    `univalence` decimal(10,0) NOT NULL COMMENT '单价',
    `quantity` decimal(10,0) NOT NULL COMMENT '数量',
    `amount` decimal(10,2) NOT NULL COMMENT '金额',
    `notes` varchar(255) DEFAULT NULL COMMENT '采购备注',
    `prMessage` varchar(255) DEFAULT NULL COMMENT 'PR信息',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商品清单';

CREATE TABLE IF NOT EXISTS `pmsx_project_invoice` (
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
    `creator_id` varchar(64) NOT NULL COMMENT '创建人',
    `modify_time` datetime NOT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `modify_id` varchar(64) NOT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
    `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
    `status` int(11) NOT NULL COMMENT '状态',
    `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
    `invoice_type` varchar(255) NOT NULL COMMENT '发票类型',
    `invoice_address` varchar(255) NOT NULL COMMENT '地址',
    `invoice_tel` varchar(11) NOT NULL COMMENT '电话',
    `invoice_head` varchar(255) NOT NULL COMMENT '发票抬头',
    `invoice_bank` varchar(255) NOT NULL COMMENT '开户行',
    `invoice_account` varchar(64) NOT NULL COMMENT '账号',
    `invoice_A_identifier` varchar(64) NOT NULL COMMENT '纳税人识别号',
    `order_number` varchar(64) NOT NULL COMMENT '订单编号',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='发票信息';

CREATE TABLE IF NOT EXISTS `pmsx_project_order` (
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
    `creator_id` varchar(64) DEFAULT NULL COMMENT '创建人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) DEFAULT NULL COMMENT '平台ID',
    `org_id` varchar(64) DEFAULT NULL COMMENT '业务组织Id',
    `status` int(11) DEFAULT NULL COMMENT '状态',
    `logic_status` int(11) DEFAULT NULL COMMENT '逻辑删除字段',
    `order_number` varchar(64) DEFAULT NULL COMMENT '订单编号',
    `order_person` varchar(32) DEFAULT NULL COMMENT '下单人',
    `order_business` varchar(255) DEFAULT NULL COMMENT '下单企业',
    `order_tel` varchar(11) DEFAULT NULL COMMENT '下单人电话',
    `contract_number` varchar(64) DEFAULT NULL COMMENT '框架合同编号',
    `contract_name` varchar(255) DEFAULT NULL COMMENT '框架合同名称',
    `order_surcharge` decimal(10,2) DEFAULT NULL COMMENT '附加费',
    `contract_numbers` varchar(255) DEFAULT NULL COMMENT '合同编号拼接',
    `business_person_id` varchar(64) DEFAULT NULL COMMENT '商务接口人id',
    `business_person_name` varchar(64) DEFAULT NULL COMMENT '商务接口人名称',
    `technical_person_id` varchar(64) DEFAULT NULL COMMENT '技术接口人id',
    `technical_person_name` varchar(64) DEFAULT NULL COMMENT '技术接口人名称',
    `bear_org_id` varchar(64) DEFAULT NULL COMMENT '承接部门id',
    `bear_org_name` varchar(64) DEFAULT NULL COMMENT '承接部门名称',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商城订单';

CREATE TABLE IF NOT EXISTS `pmsx_project_order_other` (
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
    `creator_id` varchar(64) NOT NULL COMMENT '创建人',
    `modify_time` datetime NOT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `modify_id` varchar(64) NOT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
    `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
    `status` int(11) NOT NULL COMMENT '状态',
    `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
    `order_number` varchar(64) NOT NULL COMMENT '订单编号',
    `receive_date` datetime NOT NULL COMMENT '要求到货时间',
    `receive_demand` varchar(255) NOT NULL COMMENT '特殊送货要求',
    `buy_org_code` varchar(64) NOT NULL COMMENT '采购组织编码',
    `buy_org_name` varchar(255) NOT NULL COMMENT '采购组织名称',
    `shop` varchar(255) NOT NULL COMMENT '工厂',
    `confirm_control` varchar(255) NOT NULL COMMENT '确认控制',
    `settlementmethod` varchar(64) NOT NULL COMMENT '结算方式',
    `leave_word` varchar(255) NOT NULL COMMENT '买方留言',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='其他信息';

CREATE TABLE IF NOT EXISTS `pmsx_project_receive` (
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
    `creator_id` varchar(64) NOT NULL COMMENT '创建人',
    `modify_time` datetime NOT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `modify_id` varchar(64) NOT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
    `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
    `status` int(11) NOT NULL COMMENT '状态',
    `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
    `receive_person` varchar(255) NOT NULL COMMENT '收货人',
    `receive_tel` varchar(11) NOT NULL COMMENT '收货人电话',
    `receive_address` varchar(255) NOT NULL COMMENT '收货人地址',
    `order_number` varchar(64) NOT NULL COMMENT '订单编号',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='收货信息';

CREATE TABLE IF NOT EXISTS `ncf_form_purchase_app_wbs_wbs` (
    `project_number_name` varchar(256) DEFAULT NULL COMMENT '项目编号/名称',
    `general_ledger_subject` varchar(256) DEFAULT NULL COMMENT '总账科目',
    `wbs_number` varchar(256) DEFAULT NULL COMMENT 'WBS编号',
    `req_quantity` decimal(10,2) DEFAULT NULL COMMENT '需求数量',
    `unit` varchar(256) DEFAULT NULL COMMENT '单位',
    `delivery_time` datetime DEFAULT NULL COMMENT '交货时间',
    `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价',
    `total_price` decimal(10,2) DEFAULT NULL COMMENT '总价',
    `local_currency_amt` decimal(10,2) DEFAULT NULL COMMENT '本位币金额',
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '类名',
    `creator_id` varchar(64) NOT NULL COMMENT '创建人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '数据所有者',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
    `org_id` varchar(64) NOT NULL COMMENT '业务组织ID',
    `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status` int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number` varchar(64) DEFAULT NULL COMMENT '编码',
    `main_table_id` varchar(64) DEFAULT NULL COMMENT '主表ID',
    `contract_number` varchar(256) DEFAULT NULL COMMENT '合同编号',
    `contract_name` varchar(256) DEFAULT NULL COMMENT '合同名称',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='采购立项WBS信息';

CREATE TABLE IF NOT EXISTS `ncf_form_purchase_execute_shcnge` (
    `changer` varchar(256) DEFAULT NULL COMMENT '变更人',
    `change_date` datetime DEFAULT NULL COMMENT '变更日期',
    `change_content` varchar(256) DEFAULT NULL COMMENT '变更内容',
    `before_change` varchar(256) DEFAULT NULL COMMENT '变更前',
    `after_change` varchar(256) DEFAULT NULL COMMENT '变更后',
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '类名',
    `creator_id` varchar(64) NOT NULL COMMENT '创建人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '数据所有者',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
    `org_id` varchar(64) NOT NULL COMMENT '业务组织ID',
    `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status` int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number` varchar(64) DEFAULT NULL COMMENT '编码',
    `main_table_id` varchar(64) DEFAULT NULL COMMENT '主表ID',
    `contract_number` varchar(256) DEFAULT NULL COMMENT '合同编号',
    `contract_name` varchar(256) DEFAULT NULL COMMENT '合同名称',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='采购执行变更';

CREATE TABLE IF NOT EXISTS `ncf_form_require_info` (
                                                       `time` datetime DEFAULT NULL COMMENT '时间',
                                                       `amount` decimal(10,2) DEFAULT NULL COMMENT '金额',
    `resp_dept` varchar(256) DEFAULT NULL COMMENT '责任部门',
    `tech_user` varchar(256) DEFAULT NULL COMMENT '技术责任人',
    `total_amt` decimal(10,2) DEFAULT NULL COMMENT '汇总金额',
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '类名',
    `creator_id` varchar(64) NOT NULL COMMENT '创建人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '数据所有者',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
    `org_id` varchar(64) NOT NULL COMMENT '业务组织ID',
    `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status` int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number` varchar(64) DEFAULT NULL COMMENT '编码',
    `main_table_id` varchar(64) DEFAULT NULL COMMENT '主表ID',
    `unused_amt` decimal(10,2) DEFAULT NULL COMMENT '剩余未使用金额',
    `used_amt` decimal(10,2) DEFAULT NULL COMMENT '已使用金额',
    `project_ID` varchar(256) DEFAULT NULL COMMENT '采购申请行号',
    `purch_req_doc_code` varchar(256) DEFAULT NULL COMMENT '采购申请号',
    `contract_number` varchar(256) DEFAULT NULL COMMENT '合同编号',
    `contract_name` varchar(256) DEFAULT NULL COMMENT '合同名称',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='需求单';

CREATE TABLE IF NOT EXISTS `ncf_form_supplier_bank` (
                                                        `bank_code` tinytext COMMENT '银行代码',
                                                        `bank_branch` tinytext COMMENT '银行网点',
                                                        `currency` tinytext COMMENT '币种',
                                                        `default_account` tinytext COMMENT '默认银行账号',
                                                        `account_holder` tinytext COMMENT '账号名称（受益人）',
                                                        `bank_account` tinytext COMMENT '银行账号',
                                                        `swift_code` tinytext COMMENT '国际银行代码（SWIFT)',
                                                        `iban` tinytext COMMENT '国际银行账户号码（IBAN)	',
                                                        `teller_office` tinytext COMMENT '分理处/营业点',
                                                        `sub_branch` tinytext COMMENT '支行',
                                                        `branch` tinytext COMMENT '分行',
                                                        `bank_name` tinytext COMMENT '银行名称',
                                                        `bank_district` tinytext COMMENT '开户行地区',
                                                        `bank_city` tinytext COMMENT '开户行城市',
                                                        `bank_province` tinytext COMMENT '开户行省份',
                                                        `bank_country_area` tinytext COMMENT '开户行国家和地区',
                                                        `serial_number` tinytext COMMENT '序号',
                                                        `supplier_name` tinytext COMMENT '供应商名称',
                                                        `supplier_code` varchar(256) DEFAULT NULL COMMENT '供应商编码',
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '类名',
    `creator_id` varchar(64) NOT NULL COMMENT '创建人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '数据所有者',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
    `org_id` varchar(64) NOT NULL COMMENT '业务组织ID',
    `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status` int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number` varchar(64) DEFAULT NULL COMMENT '编码',
    `main_table_id` varchar(64) DEFAULT NULL COMMENT '主表ID',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='银行信息';

CREATE TABLE IF NOT EXISTS `ncf_form_supplier_business_info` (
    `supplier_code` varchar(256) DEFAULT NULL COMMENT '供应商编码',
    `supplier_name` varchar(256) DEFAULT NULL COMMENT '供应商名称',
    `serial_number` varchar(256) DEFAULT NULL COMMENT '序号',
    `contract_no` varchar(256) DEFAULT NULL COMMENT '合同编号',
    `contract_name` varchar(256) DEFAULT NULL COMMENT '合同名称',
    `effective_date` datetime DEFAULT NULL COMMENT '合同生效日期',
    `party_A_company` varchar(256) DEFAULT NULL COMMENT '甲方公司',
    `responsible_person` varchar(256) DEFAULT NULL COMMENT '负责人',
    `currency` varchar(256) DEFAULT NULL COMMENT '货币',
    `contract_amount_level` varchar(256) DEFAULT NULL COMMENT '合同金额档级',
    `contract_status` varchar(256) DEFAULT NULL COMMENT '合同状态',
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '类名',
    `creator_id` varchar(64) NOT NULL COMMENT '创建人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '数据所有者',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
    `org_id` varchar(64) NOT NULL COMMENT '业务组织ID',
    `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status` int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number` varchar(64) DEFAULT NULL COMMENT '编码',
    `main_table_id` varchar(64) DEFAULT NULL COMMENT '主表ID',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商务信息';

CREATE TABLE IF NOT EXISTS `ncf_form_supplier_cert_info` (
    `supplier_code` varchar(256) DEFAULT NULL COMMENT '供应商编码',
    `supplier_name` varchar(256) DEFAULT NULL COMMENT '供应商名称',
    `serial_number` varchar(256) DEFAULT NULL COMMENT '序号',
    `cert_name` varchar(256) DEFAULT NULL COMMENT '资质证书名称',
    `cert_category` varchar(256) DEFAULT NULL COMMENT '资质类别',
    `cert_level` varchar(256) DEFAULT NULL COMMENT '资质等级',
    `cert_group` varchar(256) DEFAULT NULL COMMENT '资质分组',
    `brand_product` varchar(256) DEFAULT NULL COMMENT '代理品牌/产品名称',
    `expiry_date` varchar(256) DEFAULT NULL COMMENT '证书有效期截止日期',
    `cert_code` varchar(256) DEFAULT NULL COMMENT '证书编码',
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '类名',
    `creator_id` varchar(64) NOT NULL COMMENT '创建人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '数据所有者',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
    `org_id` varchar(64) NOT NULL COMMENT '业务组织ID',
    `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status` int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number` varchar(64) DEFAULT NULL COMMENT '编码',
    `main_table_id` varchar(64) DEFAULT NULL COMMENT '主表ID',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='资质信息表';

CREATE TABLE IF NOT EXISTS `ncf_form_supplier_contact` (
    `supplier_code` varchar(256) DEFAULT NULL COMMENT '供应商编码',
    `supplier_name` varchar(256) DEFAULT NULL COMMENT '供应商名称',
    `contact_lastname` varchar(256) DEFAULT NULL COMMENT '联系人姓',
    `contact_firstname` varchar(256) DEFAULT NULL COMMENT '联系人名',
    `department` varchar(256) DEFAULT NULL COMMENT '部门	',
    `position` varchar(256) DEFAULT NULL COMMENT '职务',
    `landline` varchar(256) DEFAULT NULL COMMENT '固定电话',
    `mobile` varchar(256) DEFAULT NULL COMMENT '手机	',
    `extension` varchar(256) DEFAULT NULL COMMENT '分机',
    `fax` varchar(256) DEFAULT NULL COMMENT '传真',
    `default_contact` varchar(256) DEFAULT NULL COMMENT '默认联系人',
    `email` varchar(256) DEFAULT NULL COMMENT '电子邮箱',
    `id_number` varchar(256) DEFAULT NULL COMMENT '身份证号码',
    `responsible_area` varchar(256) DEFAULT NULL COMMENT '负责区域/专业',
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '类名',
    `creator_id` varchar(64) NOT NULL COMMENT '创建人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '数据所有者',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
    `org_id` varchar(64) NOT NULL COMMENT '业务组织ID',
    `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status` int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number` varchar(64) DEFAULT NULL COMMENT '编码',
    `main_table_id` varchar(64) DEFAULT NULL COMMENT '主表ID',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='供应商联系人';

CREATE TABLE IF NOT EXISTS `ncf_form_supplier_history` (
    `supplier_code` varchar(256) DEFAULT NULL COMMENT '供应商编码',
    `supplier_name` varchar(256) DEFAULT NULL COMMENT '供应商名称',
    `serial_number` varchar(256) DEFAULT NULL COMMENT '序号',
    `application_id` varchar(256) DEFAULT NULL COMMENT '申请编号',
    `application_type` varchar(256) DEFAULT NULL COMMENT '申请类型',
    `applicant` varchar(256) DEFAULT NULL COMMENT '申请人',
    `applying_company` varchar(256) DEFAULT NULL COMMENT '申请公司',
    `reviewing_company` varchar(256) DEFAULT NULL COMMENT '评审公司',
    `safety_expert_score` varchar(256) DEFAULT NULL COMMENT '安全专家评分',
    `tech_expert_score` varchar(256) DEFAULT NULL COMMENT '技术专家评分',
    `business_expert_score` varchar(256) DEFAULT NULL COMMENT '商务专家评分',
    `quality_assurance_expert_score` varchar(256) DEFAULT NULL COMMENT '质保专家评分',
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '类名',
    `creator_id` varchar(64) NOT NULL COMMENT '创建人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '数据所有者',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
    `org_id` varchar(64) NOT NULL COMMENT '业务组织ID',
    `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status` int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number` varchar(64) DEFAULT NULL COMMENT '编码',
    `main_table_id` varchar(64) DEFAULT NULL COMMENT '主表ID',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='历史资审记录';

CREATE TABLE IF NOT EXISTS `ncf_form_supplier_info` (
    `supplier_number` varchar(256) DEFAULT NULL COMMENT '供应商编码',
    `name` varchar(256) DEFAULT NULL COMMENT '供应商名称',
    `account` varchar(256) DEFAULT NULL COMMENT '供应商账号',
    `find_pass_eamil` varchar(256) DEFAULT NULL COMMENT '找回密码邮箱',
    `sim_name` varchar(256) DEFAULT NULL COMMENT '供应商简称',
    `e_name` varchar(256) DEFAULT NULL COMMENT '供应商英文名称',
    `reg_country_region` varchar(256) DEFAULT NULL COMMENT '注册国家和地区',
    `province` varchar(256) DEFAULT NULL COMMENT '省',
    `city` varchar(256) DEFAULT NULL COMMENT '城市',
    `county` varchar(256) DEFAULT NULL COMMENT '区/县',
    `reg_address` varchar(256) DEFAULT NULL COMMENT '注册地址',
    `ems_number` varchar(256) DEFAULT NULL COMMENT '邮政编码',
    `url` varchar(256) DEFAULT NULL COMMENT '网址',
    `landline_phone` varchar(256) DEFAULT NULL COMMENT '固定电话',
    `extension` varchar(256) DEFAULT NULL COMMENT '分机',
    `fax` varchar(256) DEFAULT NULL COMMENT '传真',
    `organization_type` varchar(256) DEFAULT NULL COMMENT '组织类型',
    `legalrep` varchar(256) DEFAULT NULL COMMENT '法人代表',
    `company_nature` varchar(256) DEFAULT NULL COMMENT '企业性质',
    `zgh_child` varchar(256) DEFAULT NULL COMMENT '中广核集团参股或控股公司',
    `capital_currency` varchar(256) DEFAULT NULL COMMENT '注册资金币种',
    `registered_capital` decimal(10,2) DEFAULT NULL COMMENT '注册资本（万）',
    `parent_org` varchar(256) DEFAULT NULL COMMENT '上级主管单位',
    `major_shareholder` varchar(256) DEFAULT NULL COMMENT '主要控股公司',
    `products_services` text COMMENT '可提供产品/服务文字描述',
    `company_overview` varchar(256) DEFAULT NULL COMMENT '公司简介',
    `business_license_num` varchar(256) DEFAULT NULL COMMENT '营业执照注册号/统一社会信用代码',
    `business_license_start` datetime DEFAULT NULL COMMENT '营业执照有效期起',
    `business_license_end` datetime DEFAULT NULL COMMENT '营业执照有效期至',
    `operation_scope` varchar(256) DEFAULT NULL COMMENT '经营范围',
    `recommend_supplier` varchar(256) DEFAULT NULL COMMENT '是否推荐供应商',
    `sector_name` varchar(256) DEFAULT NULL COMMENT '板块名称',
    `supplier_level` varchar(256) DEFAULT NULL COMMENT '供应商级别',
    `qual_validity` datetime DEFAULT NULL COMMENT '资审有效期',
    `procurement_cat` varchar(256) DEFAULT NULL COMMENT '采购品类',
    `proc_cat_code` varchar(256) DEFAULT NULL COMMENT '采购品类编码',
    `delivery_scope_desc` varchar(256) DEFAULT NULL COMMENT '供货范围文本描述',
    `supplier_type` varchar(256) DEFAULT NULL COMMENT '供应商分类',
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '类名',
    `creator_id` varchar(64) NOT NULL COMMENT '创建人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '数据所有者',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
    `org_id` varchar(64) NOT NULL COMMENT '业务组织ID',
    `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status` int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number` varchar(64) DEFAULT NULL COMMENT '编码',
    `supplier_category` varchar(255) DEFAULT NULL COMMENT '供应商类别',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='供应商管理';

CREATE TABLE IF NOT EXISTS `ncf_form_supplier_products` (
    `supplier_code` varchar(256) DEFAULT NULL COMMENT '供应商编码',
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '类名',
    `creator_id` varchar(64) NOT NULL COMMENT '创建人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '数据所有者',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
    `org_id` varchar(64) NOT NULL COMMENT '业务组织ID',
    `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status` int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number` varchar(64) DEFAULT NULL COMMENT '编码',
    `main_table_id` varchar(64) DEFAULT NULL COMMENT '主表ID',
    `supplier_name` varchar(256) DEFAULT NULL COMMENT '供应商名称',
    `product_level_one` varchar(256) DEFAULT NULL COMMENT '可提供产品一级',
    `product_level_two` varchar(256) DEFAULT NULL COMMENT '可提供产品二级',
    `product_level_three` varchar(256) DEFAULT NULL COMMENT '可提供产品三级',
    `product_level_four` varchar(256) DEFAULT NULL COMMENT '可提供产品四级',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='可提供产品';

CREATE TABLE IF NOT EXISTS `ncf_form_supplier_restricted_record` (
    `supplier_code` varchar(256) DEFAULT NULL COMMENT '供应商编码',
    `supplier_name` varchar(256) DEFAULT NULL COMMENT '供应商名称',
    `serial_number` varchar(256) DEFAULT NULL COMMENT '序号',
    `contract_number` varchar(256) DEFAULT NULL COMMENT '合同编号',
    `contract_name` varchar(256) DEFAULT NULL COMMENT '合同名称',
    `application_number` varchar(256) DEFAULT NULL COMMENT '申请编号',
    `restricted_type` varchar(256) DEFAULT NULL COMMENT '受限类型',
    `rectification_status` varchar(256) DEFAULT NULL COMMENT '整改状态',
    `blacklist_type` varchar(256) DEFAULT NULL COMMENT '黑名单类型',
    `project_name` varchar(256) DEFAULT NULL COMMENT '项目名称',
    `declaration_date` datetime DEFAULT NULL COMMENT '申报日期',
    `applicant` varchar(256) DEFAULT NULL COMMENT '申请人',
    `declaring_company` varchar(256) DEFAULT NULL COMMENT '申报公司',
    `content_description` varchar(256) DEFAULT NULL COMMENT '内容描述',
    `approval_completion_time` varchar(256) DEFAULT NULL COMMENT '审批完成时间',
    `whether_thawed` varchar(256) DEFAULT NULL COMMENT '是否解冻',
    `restricted_scope` varchar(256) DEFAULT NULL COMMENT '受限范围',
    `group_sends_sap` varchar(256) DEFAULT NULL COMMENT '集团发送SAP',
    `id` varchar(64) NOT NULL COMMENT '主键',
    `class_name` varchar(64) DEFAULT NULL COMMENT '类名',
    `creator_id` varchar(64) NOT NULL COMMENT '创建人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `owner_id` varchar(64) DEFAULT NULL COMMENT '数据所有者',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
    `org_id` varchar(64) NOT NULL COMMENT '业务组织ID',
    `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
    `logic_status` int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
    `number` varchar(64) DEFAULT NULL COMMENT '编码',
    `main_table_id` varchar(64) DEFAULT NULL COMMENT '主表ID',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='受限事件记录';