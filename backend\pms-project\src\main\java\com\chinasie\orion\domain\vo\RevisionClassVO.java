package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/9/26 17:49
 */
@Data
public class RevisionClassVO extends ObjectVO {
    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private String id;

    /**
     * 下一个版本
     */
    @ApiModelProperty(value = "下一个版本")
    private String nextRevId;

    /**
     * 版本顺序
     */
    @ApiModelProperty(value = "版本顺序")
    private Integer revOrder;

    /**
     * 版本KEY
     */
    @ApiModelProperty(value = "版本KEY")
    private String revKey;

    /**
     * 上一个版本
     */
    @ApiModelProperty(value = "上一个版本")
    private String previousRevId;

    /**
     * 版本值
     */
    @ApiModelProperty(value = "版本值")
    private String revId;

    /**
     * 初始版本
     */
    @ApiModelProperty(value = "初始版本")
    private String initialRevId;
}
