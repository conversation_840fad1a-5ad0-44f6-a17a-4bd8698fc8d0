package com.chinasie.orion.management.domain.entity;

import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.DictDataBind;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;
import java.util.Date;

/**
 * customerInfo Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-30 16:25:24
 */
@TableName(value = "pms_customer_info")
@ApiModel(value = "customerInfoEntity对象", description = "客户管理")
@Data
public class CustomerInfo extends ObjectEntity implements Serializable {

    /**
     * 冗余字段
     */
    @ApiModelProperty(value = "冗余字段")
    @TableField(value = "comnumber")
    private String comnumber;

    /**
     * 工商注册号
     */
    @ApiModelProperty(value = "工商注册号")
    @TableField(value = "bus_register_code")
    private String busRegisterCode;

    /**
     * 纳税人识别号
     */
    @ApiModelProperty(value = "纳税人识别号")
    @TableField(value = "tax_id_code")
    private String taxIdCode;

    /**
     * 企业全称
     */
    @ApiModelProperty(value = "企业全称")
    @TableField(value = "cus_full_name")
    private String cusFullName;

    /**
     * 客户级别
     */
    @ApiModelProperty(value = "客户级别")
    @FieldBind(dataBind = DictDataBind.class, type = "customer_level", target = "cusLevelName")
    @TableField(value = "cus_level")
    private String cusLevel;

    /**
     * 客户级别名称
     */
    @ApiModelProperty(value = "客户级别名称")
    @TableField(exist = false)
    private String cusLevelName;


    /**
     * 国家
     */
    @ApiModelProperty(value = "国家")
    @TableField(value = "country")
    private String country;

    /**
     * 组织机构代码
     */
    @ApiModelProperty(value = "组织机构代码")
    @TableField(value = "organizatioin_code")
    private String organizatioinCode;

    /**
     * 营业期限
     */
    @ApiModelProperty(value = "营业期限")
    @TableField(value = "biz_period")
    private String bizPeriod;

    /**
     * 注册资本
     */
    @ApiModelProperty(value = "注册资本")
    @TableField(value = "registered_capital")
    private String registeredCapital;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    @TableField(value = "uniform_credit_code")
    private String uniformCreditCode;

    /**
     * 企业类型
     */
    @ApiModelProperty(value = "企业类型")
    @TableField(value = "cus_category")
    private String cusCategory;

    /**
     * 成立日期
     */
    @ApiModelProperty(value = "成立日期")
    @TableField(value = "registration_time")
    private Date registrationTime;

    /**
     * 经营范围
     */
    @ApiModelProperty(value = "经营范围")
    @TableField(value = "business_scope")
    private String businessScope;

    /**
     * 税务登记证号
     */
    @ApiModelProperty(value = "税务登记证号")
    @TableField(value = "comtaxnumber")
    private String comtaxnumber;

    /**
     * 注册地址
     */
    @ApiModelProperty(value = "注册地址")
    @TableField(value = "registered_address")
    private String registeredAddress;

    /**
     * 法定代表人
     */
    @ApiModelProperty(value = "法定代表人")
    @TableField(value = "legal_repr")
    private String legalRepr;

    /**
     * 创建日期
     */
    @ApiModelProperty(value = "创建日期")
    @TableField(value = "cus_ceate_time")
    private Date cusCeateTime;

    /**
     * 公众号信息
     */
    @ApiModelProperty(value = "公众号信息")
    @TableField(value = "public_account_info")
    private String publicAccountInfo;

    /**
     * 企业规模
     */
    @ApiModelProperty(value = "企业规模")
    @TableField(value = "cus_num_count")
    private String cusNumCount;

    /**
     * 通讯地址
     */
    @ApiModelProperty(value = "通讯地址")
    @TableField(value = "cus_address")
    private String cusAddress;

    /**
     * 业务收入类型
     */
    @ApiModelProperty(value = "业务收入类型")
    @TableField(value = "ywsrlx")
    @FieldBind(dataBind = DictDataBind.class, type = "income_type", target = "ywsrlxName")
    private String ywsrlx;

    /**
     * 业务收入类型名称
     */
    @ApiModelProperty(value = "业务收入类型名称")
    @TableField(exist = false)
    private String ywsrlxName;


    /**
     * 所属行业
     */
    @ApiModelProperty(value = "所属行业")
    @TableField(value = "industry")
    private String industry;

    /**
     * 客户范围
     */
    @ApiModelProperty(value = "客户范围")
    @TableField(value = "bus_scope")
    @FieldBind(dataBind = DictDataBind.class, type = "customer_scope", target = "busScopeName")
    private String busScope;

    /**
     * 客户范围名称
     */
    @ApiModelProperty(value = "客户范围名称")
    @TableField(exist = false)
    private String busScopeName;


    /**
     * 客户关系(集团内外)
     */
    @ApiModelProperty(value = "客户关系(集团内外)")
    @TableField(value = "group_in_out")
    @FieldBind(dataBind = DictDataBind.class, type = "customer_relationship", target = "groupInOutName")
    private String groupInOut;

    /**
     * 客户关系(集团内外)名称
     */
    @ApiModelProperty(value = "客户关系(集团内外)名称")
    @TableField(exist = false)
    private String groupInOutName;


    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @TableField(value = "cus_remark")
    private String cusRemark;

    /**
     * 客户状态
     */
    @ApiModelProperty(value = "客户状态")
    @FieldBind(dataBind = DictDataBind.class, type = "customer_status", target = "cusStatusName")
    @TableField(value = "cus_status")
    private String cusStatus;

    /**
     * 客户状态名称
     */
    @ApiModelProperty(value = "客户状态名称")
    @TableField(exist = false)
    private String cusStatusName;

    /**
     * 客户部门
     */
    @ApiModelProperty(value = "客户部门")
    @TableField(value = "customerdepartmentent")
    private String customerdepartmentent;

    /**
     * 所属集团
     */
    @ApiModelProperty(value = "所属集团")
    @TableField(value = "group_info")
    private String groupInfo;

    /**
     * 客户编码
     */
    @ApiModelProperty(value = "客户编码")
    @TableField(value = "cus_number")
    private String cusNumber;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    @TableField(value = "cus_name")
    private String cusName;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 资质信息
     */
    @ApiModelProperty(value = "资质信息")
    @TableField(value = "zzxx")
    private String zzxx;

    /**
     * 公司类型
     */
    @ApiModelProperty(value = "公司类型")
    @TableField(value = "comtpye")
    private String comtpye;

    /**
     * 登记状态
     */
    @ApiModelProperty(value = "登记状态")
    @TableField(value = "regist_status")
    private String registStatus;

    /**
     * 实缴资本
     */
    @ApiModelProperty(value = "实缴资本")
    @TableField(value = "paid_in_capital")
    private String paidInCapital;

    /**
     * 核准日期
     */
    @ApiModelProperty(value = "核准日期")
    @TableField(value = "approved_date")
    private Date approvedDate;

    /**
     * 所属省份
     */
    @ApiModelProperty(value = "所属省份")
    @TableField(value = "province")
    private String province;

    /**
     * 所属城市
     */
    @ApiModelProperty(value = "所属城市")
    @TableField(value = "city")
    private String city;

    /**
     * 所属区县
     */
    @ApiModelProperty(value = "所属区县")
    @TableField(value = "county")
    private String county;

    /**
     * 国标行业门类
     */
    @ApiModelProperty(value = "国标行业门类")
    @TableField(value = "category")
    private String category;

    /**
     * 国标行业大类
     */
    @ApiModelProperty(value = "国标行业大类")
    @TableField(value = "large_category")
    private String largeCategory;

    /**
     * 国标行业中类
     */
    @ApiModelProperty(value = "国标行业中类")
    @TableField(value = "middle_category")
    private String middleCategory;

    /**
     * 英文名
     */
    @ApiModelProperty(value = "英文名")
    @TableField(value = "english_name")
    private String englishName;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    @TableField(value = "tel")
    private String tel;

    /**
     * 其他电话
     */
    @ApiModelProperty(value = "其他电话")
    @TableField(value = "other_tel")
    private String otherTel;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    @TableField(value = "email")
    private String email;


    /**
     * 是否被使用
     */
    @ApiModelProperty(value = "是否被使用0-没有被使用,1-被使用")
    @TableField(value = "is_used")
    private String isUsed;


    /**
     * 是否关联人士
     */
    @ApiModelProperty(value = "是否关联人士")
    @TableField(value = "is_person")
    private String isPerson;


    /**
     * 所属基地
     */
    @ApiModelProperty(value = "所属基地")
    @TableField(value = "home_base")
    private String homeBase;

    /**
     * 销售业务分类
     */
    @ApiModelProperty(value = "销售业务分类")
    @TableField(value = "sales_class")
    private String salesClass;

}
