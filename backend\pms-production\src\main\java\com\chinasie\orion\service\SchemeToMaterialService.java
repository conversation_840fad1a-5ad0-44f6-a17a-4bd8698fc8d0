package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.SchemeToMaterialDTO;
import com.chinasie.orion.domain.entity.SchemeToMaterial;
import com.chinasie.orion.domain.vo.SchemeToMaterialVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * SchemeToMaterial 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-15 20:21:30
 */
public interface SchemeToMaterialService extends OrionBaseService<SchemeToMaterial> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    SchemeToMaterialVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param schemeToMaterialDTO
     */
    String create(SchemeToMaterialDTO schemeToMaterialDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param schemeToMaterialDTO
     */
    Boolean edit(SchemeToMaterialDTO schemeToMaterialDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<SchemeToMaterialVO> pages(Page<SchemeToMaterialDTO> pageRequest) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<SchemeToMaterialVO> vos) throws Exception;


    Page<SchemeToMaterialVO> materialPages(Page<SchemeToMaterialDTO> pageRequest) throws Exception;

    void saveEntity(String planSchemeId, String repairRound
            , String jobBase, String number
            , Integer stockNum, String assetType
            , String materialManageId,String name,String productCode,String toolStatus,Integer maintenanceCycle);

    List<String> listByRepairRound(String repairRound);


    /**
     * 导出物资入场离场信息
     *
     * @param query    查询
     * @param response 导出到浏览器
     */
    void exportSchemeMaterialExcel(SchemeToMaterialDTO query, HttpServletResponse response);
}
