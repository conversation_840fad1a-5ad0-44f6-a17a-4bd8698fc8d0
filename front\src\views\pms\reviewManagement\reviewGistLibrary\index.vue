<script setup lang="ts">
import {
  computed, h, ref,
} from 'vue';
import {
  openDrawer,
  BasicButton,
  DataStatusTag, isPower, Layout, OrionTable, BasicImport, useModal, downloadByData,
} from 'lyra-component-vue3';

import { message, Modal } from 'ant-design-vue';
import { useRouter } from 'vue-router';

import DrawerAdd from './components/DrawerAdd.vue';

import {
  page, add, remove, reviewLibraryDelete, edit,
} from '/@/views/pms/api/reviewLibrary';

const router = useRouter();

const [registerModal, { openModal }] = useModal();
const headAuthList = ref([]);

const searchConditions = ref([]);
const powerCode = {
  pageCode: 'PMS_PSYDK',
  headContainerCode: 'PSYDK_container_01',
  containerCode: 'PSYDK_container_02',
  headAdd: 'PSYDK_container_01_button_01',
  headDelete: 'PSYDK_container_01_button_05',

  containerView: 'PSYDK_container_02_button_01',
  containerEdit: 'PSYDK_container_02_button_02',
  containerDelete: 'PSYDK_container_02_button_03',
};
const tableRef = ref(null);
const columns = [
  {
    title: '评审库名称',
    dataIndex: 'name',
    customRender({
      record, text,
    }) {
      if (isPower(powerCode.containerView, record.rdAuthList)) {
        return h('span', {
          class: 'action-btn',
          onClick() {
            router.push({
              name: 'ReviewGistLibraryDetail',
              params: {
                id: record.id,
              },
              query: {
                name: record.name,
              },
            });
          },
        }, text);
      }
      return text;
    },
  },
  {
    title: '评审专家',
    dataIndex: 'expertList',
    customRender: ({ text }) => (text ? text.map((item) => item.name).join(',') : ''),
  },
  {
    title: '维护部门',
    dataIndex: 'maintainDeptList',
    customRender: ({ text }) => (text ? text.map((item) => item.name).join(',') : ''),
  },
  {
    title: '要点数量',
    dataIndex: 'essentialsNumber',
  },
  {
    title: '修改人',
    dataIndex: 'modifyName',
  },
  {
    title: '修改时间',
    dataIndex: 'modifyTime',
    type: 'dateTime',
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    slots: { customRender: 'action' },
  },
];
const actions = [
  {
    text: '编辑',
    isShow: (record) => isPower(powerCode.containerEdit, record.rdAuthList),
    async onClick(record) {
      handleEdit(record.id);
    },
  },
  {
    text: '删除',
    isShow: (record) => isPower(powerCode.containerDelete, record.rdAuthList),
    async modal(record) {
      await reviewLibraryDelete(record.id);
      message.success('删除成功');
      tableRef.value.reload();
    },
  },

];
const baseTableOption = {
  rowKey: 'id',
  // 是否显示工具栏默认按钮
  showToolButton: true,
  // 删除默认的部分按钮'String'用'|'隔开, 如 'add|delete|enable|disable'
  // 权限配置使用计算属性
  deleteToolButton: computed(() => {
    let str = 'add|enable|disable';
    if (!isPower(powerCode.headDelete, headAuthList.value)) str += '|delete';
    return str;
  }),
  // 是否显示工具栏上的搜索
  showSmallSearch: true,
  // 工具栏搜索字段配置，string | string[] 默认 'name' , 传数组字段值则查询多个子字段
  smallSearchField: ['name'],

  rowSelection: {},
  columns,
  api: (params) => {
    params.power = {
      pageCode: powerCode.pageCode,
      headContainerCode: powerCode.headContainerCode,
      containerCode: powerCode.containerCode,
    };
    searchConditions.value = params?.searchConditions || [];
    return page(params).then((res) => {
      headAuthList.value = res.headAuthList || [];
      return res;
    });
  },
  isFilter2: false,
  filterConfigName: 'PAS_REVIEW_GIST_LIBRARY',
  actions,
  // 批量删除自定义api，有特殊情况才使用, data={ids:'选中的id组','选中的原始数据'}
  batchDeleteApi: async ({ ids }) => {
    await remove(ids);
    tableRef.value.reload();
  },

};

const handleAdd = () => {
  const refDrawer = ref();
  openDrawer({
    title: '新建评审要点库',
    width: 900,
    content(h) {
      return h(DrawerAdd, {
        ref: refDrawer,
      });
    },
    async onOk() {
      const { formMethods: { validate, resetFields } } = refDrawer.value;
      const values = await validate();
      const { expertList, maintainDept } = values;

      await add({
        ...values,
        expertList: expertList.map((item) => item.id),
        maintainDept: maintainDept[0].id,
      });
      resetFields();
      tableRef.value.reload();
    },
  });
};
const handleEdit = (id) => {
  const refDrawer = ref();
  openDrawer({
    title: '编辑评审要点库',
    width: 900,
    content(h) {
      return h(DrawerAdd, {
        ref: refDrawer,
        id,
      });
    },
    async onOk() {
      const { formMethods: { validate }, getDataDetail } = refDrawer.value;
      const values = await validate();
      const { expertList, maintainDept } = values;
      const dataDetail = getDataDetail();

      await edit({
        ...dataDetail,
        ...values,
        expertList: expertList.map((item) => item.id),
        maintainDept: maintainDept[0].id,
      });
      tableRef.value.reload();
    },
  });
};

</script>

<template>
  <Layout
    v-get-power="{pageCode:'PMS_PSYDK'}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="baseTableOption"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="isPower(powerCode.headAdd, headAuthList)"
          type="primary"
          icon="add"
          @click="handleAdd"
        >
          新建评审库
        </BasicButton>
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">

</style>
