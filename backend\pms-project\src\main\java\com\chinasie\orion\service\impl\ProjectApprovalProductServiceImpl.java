package com.chinasie.orion.service.impl;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.dict.ProductDict;
import com.chinasie.orion.domain.dto.ProjectApprovalProductDTO;
import com.chinasie.orion.domain.entity.ProjectApprovalProduct;
import com.chinasie.orion.domain.vo.ProjectApprovalProductVO;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ProjectApprovalProductMapper;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectApprovalProductService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;

import java.io.InputStream;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;


/**
 * <p>
 * ProjectApprovalProduct 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-23 15:11:39
 */
@Service
@Slf4j
public class ProjectApprovalProductServiceImpl extends OrionBaseServiceImpl<ProjectApprovalProductMapper, ProjectApprovalProduct> implements ProjectApprovalProductService {

    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private DictRedisHelper dictRedisHelper;

    @Resource
    private UserRedisHelper userRedisHelper;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProjectApprovalProductVO detail(String id, String pageCode) throws Exception {
        ProjectApprovalProduct projectApprovalProduct = this.getById(id);
       List<ProjectApprovalProductVO> result =  setEveryName(Collections.singletonList(projectApprovalProduct));
        return result.get(0);
    }

    /**
     * 新增
     * <p>
     * * @param projectApprovalProductDTO
     */
    @Override
    public String create(ProjectApprovalProductDTO projectApprovalProductDTO) throws Exception {
        ProjectApprovalProduct projectApprovalProduct = BeanCopyUtils.convertTo(projectApprovalProductDTO, ProjectApprovalProduct::new);
        this.save(projectApprovalProduct);

        String rsp = projectApprovalProduct.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param projectApprovalProductDTO
     */
    @Override
    public Boolean edit(ProjectApprovalProductDTO projectApprovalProductDTO) throws Exception {
        ProjectApprovalProduct projectApprovalProduct = BeanCopyUtils.convertTo(projectApprovalProductDTO, ProjectApprovalProduct::new);

        this.updateById(projectApprovalProduct);

        String rsp = projectApprovalProduct.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        for (String id : ids) {


        }
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectApprovalProductVO> pages(Page<ProjectApprovalProductDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProjectApprovalProduct> condition = new LambdaQueryWrapperX<>(ProjectApprovalProduct.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        if(ObjectUtil.isNotEmpty( pageRequest.getQuery())) {
           condition.eq(ProjectApprovalProduct::getApprovalId,pageRequest.getQuery().getApprovalId());
        }
        condition.orderByDesc(ProjectApprovalProduct::getCreateTime);


        Page<ProjectApprovalProduct> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectApprovalProduct::new));

        PageResult<ProjectApprovalProduct> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectApprovalProductVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectApprovalProductVO> vos = setEveryName(page.getContent());
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "项目立项产品导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectApprovalProductDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ProjectApprovalProductExcelListener excelReadListener = new ProjectApprovalProductExcelListener();
        EasyExcel.read(inputStream, ProjectApprovalProductDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ProjectApprovalProductDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("项目立项产品导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ProjectApprovalProduct> projectApprovalProductes = BeanCopyUtils.convertListTo(dtoS, ProjectApprovalProduct::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ProjectApprovalProduct-import::id", importId, projectApprovalProductes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ProjectApprovalProduct> projectApprovalProductes = (List<ProjectApprovalProduct>) orionJ2CacheService.get("pmsx::ProjectApprovalProduct-import::id", importId);
        log.info("项目立项产品导入的入库数据={}", JSONUtil.toJsonStr(projectApprovalProductes));

        this.saveBatch(projectApprovalProductes);
        orionJ2CacheService.delete("pmsx::ProjectApprovalProduct-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ProjectApprovalProduct-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ProjectApprovalProduct> condition = new LambdaQueryWrapperX<>(ProjectApprovalProduct.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ProjectApprovalProduct::getCreateTime);
        List<ProjectApprovalProduct> projectApprovalProductes = this.list(condition);

        List<ProjectApprovalProductDTO> dtos = BeanCopyUtils.convertListTo(projectApprovalProductes, ProjectApprovalProductDTO::new);

        String fileName = "项目立项产品数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectApprovalProductDTO.class, dtos);

    }

    @Override
    public List<ProjectApprovalProductVO> setEveryName(List<ProjectApprovalProduct> content) throws Exception {

        if (CollectionUtils.isEmpty(content)) {
            return new ArrayList<>();
        }

        List<DictValueVO> productClassifyDict = dictRedisHelper.getDictListByCode(ProductDict.PRODUCT_CLASSIFY);
        Map<String,String> productClassifyMap = productClassifyDict.stream().collect(Collectors.toMap(DictValueVO::getValue,DictValueVO::getDescription));

        List<DictValueVO> materialTypeDict = dictRedisHelper.getDictListByCode(ProductDict.MATERIAL_TYPE);
        Map<String,String> materialTypeMap = materialTypeDict.stream().collect(Collectors.toMap(DictValueVO::getValue,DictValueVO::getDescription));

        List<DictValueVO> militarycivilianDict = dictRedisHelper.getDictListByCode(ProductDict.MILITARY_CIVILIAN_TYPE);
        Map<String,String> militarycivilianMap = militarycivilianDict.stream().collect(Collectors.toMap(DictValueVO::getValue,DictValueVO::getDescription));


        List<DictValueVO> highQualityLevelDict = dictRedisHelper.getDictListByCode(ProductDict.HIGH_QUALITY_LEVEL);
        Map<String,String> highQualityLevelMap = highQualityLevelDict.stream().collect(Collectors.toMap(DictValueVO::getValue,DictValueVO::getDescription));


        List<DictValueVO> localizedControlDict = dictRedisHelper.getDictListByCode(ProductDict.LOCALIZED_CONTROL);
        Map<String,String> localizedControlMap = localizedControlDict.stream().collect(Collectors.toMap(DictValueVO::getValue,DictValueVO::getDescription));

        List<DictValueVO> productGroupDict = dictRedisHelper.getDictListByCode(ProductDict.PRODUCT_GROUP);
        Map<String,String> productGroupMap = productGroupDict.stream().collect(Collectors.toMap(DictValueVO::getValue,DictValueVO::getDescription));

        List<DictValueVO> productSecondClassifyDict = dictRedisHelper.getDictListByCode(ProductDict.PRODUCT_SECOND_CLASSIFY);
        Map<String,String> productSecondClassifyMap = productSecondClassifyDict.stream().collect(Collectors.toMap(DictValueVO::getValue,DictValueVO::getDescription));

        List<ProjectApprovalProductVO> productVOS = BeanCopyUtils.convertListTo(content, ProjectApprovalProductVO::new);
        List<String> modifyIds = productVOS.stream().map(ProjectApprovalProductVO::getModifyId).distinct().collect(Collectors.toList());
        Map<String, String> users = userRedisHelper.getUserByIds(modifyIds).stream().collect(Collectors.toMap(UserVO::getId, UserVO::getName));


        List<DictValueVO> materialLevelDict = dictRedisHelper.getDictListByCode(ProductDict.MATERIAL_LEVEL);
        Map<String,String> materialLevelDictMap = materialLevelDict.stream().collect(Collectors.toMap(DictValueVO::getValue,DictValueVO::getDescription));


        productVOS.forEach(vo -> {
            vo.setModifyName(users.get(vo.getModifyId()));
            if(StrUtil.isNotBlank(vo.getMaterialType())){
                vo.setMaterialTypeName(materialTypeMap.get(vo.getMaterialType()));
            }
            if(StrUtil.isNotBlank(vo.getMilitaryCivilian())){
                vo.setMilitaryCivilianName(militarycivilianMap.get(vo.getMilitaryCivilian()));
            }
            if(StrUtil.isNotBlank(vo.getProductClassify())){
                vo.setProductClassifyName(productClassifyMap.get(vo.getProductClassify()));
            }
            if(StrUtil.isNotBlank(vo.getProductGroup())){
                vo.setProductGroupName(productGroupMap.get(vo.getProductGroup()));
            }
            if(StrUtil.isNotBlank(vo.getLocalizedControl())){
                vo.setLocalizedControlName(localizedControlMap.get(vo.getLocalizedControl()));
            }
            if(StrUtil.isNotBlank(vo.getHighQualityLevel())){
                vo.setHighQualityLevelName(highQualityLevelMap.get(vo.getHighQualityLevel()));
            }
            if(StrUtil.isNotBlank(vo.getProductSecondClassify())){
                vo.setProductSecondClassifyName(productSecondClassifyMap.get(vo.getProductSecondClassify()));
            }
            if(StrUtil.isNotBlank(vo.getMaterialLevel())){
                vo.setMaterialLevelName(materialLevelDictMap.get(vo.getMaterialLevel()));
            }
        });
        return productVOS;

    }


    public static class ProjectApprovalProductExcelListener extends AnalysisEventListener<ProjectApprovalProductDTO> {

        private final List<ProjectApprovalProductDTO> data = new ArrayList<>();

        @Override
        public void invoke(ProjectApprovalProductDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ProjectApprovalProductDTO> getData() {
            return data;
        }
    }


}
