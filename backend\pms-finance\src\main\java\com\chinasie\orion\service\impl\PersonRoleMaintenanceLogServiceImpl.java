package com.chinasie.orion.service.impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.domain.dto.PersonRoleMaintenanceLogDTO;
import com.chinasie.orion.domain.entity.PersonRoleMaintenanceLog;
import com.chinasie.orion.domain.vo.PersonRoleMaintenanceLogVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.PersonRoleMaintenanceLogMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.PersonRoleMaintenanceLogService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;




/**
 * <p>
 * PersonRoleMaintenanceLog 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-09 19:49:38
 */
@Service
@Slf4j
public class PersonRoleMaintenanceLogServiceImpl extends  OrionBaseServiceImpl<PersonRoleMaintenanceLogMapper, PersonRoleMaintenanceLog>   implements PersonRoleMaintenanceLogService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public PersonRoleMaintenanceLogVO detail(String id, String pageCode) throws Exception {
        PersonRoleMaintenanceLog personRoleMaintenanceLog =this.getById(id);
        PersonRoleMaintenanceLogVO result = BeanCopyUtils.convertTo(personRoleMaintenanceLog,PersonRoleMaintenanceLogVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param personRoleMaintenanceLogDTO
     */
    @Override
    public  String create(PersonRoleMaintenanceLogDTO personRoleMaintenanceLogDTO) throws Exception {
        PersonRoleMaintenanceLog personRoleMaintenanceLog =BeanCopyUtils.convertTo(personRoleMaintenanceLogDTO,PersonRoleMaintenanceLog::new);
        this.save(personRoleMaintenanceLog);

        String rsp=personRoleMaintenanceLog.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param personRoleMaintenanceLogDTO
     */
    @Override
    public Boolean edit(PersonRoleMaintenanceLogDTO personRoleMaintenanceLogDTO) throws Exception {
        PersonRoleMaintenanceLog personRoleMaintenanceLog =BeanCopyUtils.convertTo(personRoleMaintenanceLogDTO,PersonRoleMaintenanceLog::new);

        this.updateById(personRoleMaintenanceLog);

        String rsp=personRoleMaintenanceLog.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<PersonRoleMaintenanceLogVO> pages( Page<PersonRoleMaintenanceLogDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<PersonRoleMaintenanceLog> condition = new LambdaQueryWrapperX<>( PersonRoleMaintenanceLog. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(PersonRoleMaintenanceLog::getCreateTime);


        Page<PersonRoleMaintenanceLog> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), PersonRoleMaintenanceLog::new));

        PageResult<PersonRoleMaintenanceLog> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<PersonRoleMaintenanceLogVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<PersonRoleMaintenanceLogVO> vos = BeanCopyUtils.convertListTo(page.getContent(), PersonRoleMaintenanceLogVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    /**
     *  审核人员变更记录分页
     *
     * * @param pageRequest,@param roleId
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<PersonRoleMaintenanceLogVO> personRoleDetailLogPages(String roleId, Page<PersonRoleMaintenanceLogDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<PersonRoleMaintenanceLog> condition = new LambdaQueryWrapperX<>( PersonRoleMaintenanceLog. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.eq(PersonRoleMaintenanceLog::getRoleId, roleId);
        condition.orderByDesc(PersonRoleMaintenanceLog::getCreateTime);
        Page<PersonRoleMaintenanceLog> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), PersonRoleMaintenanceLog::new));

        PageResult<PersonRoleMaintenanceLog> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<PersonRoleMaintenanceLogVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<PersonRoleMaintenanceLogVO> vos = BeanCopyUtils.convertListTo(page.getContent(), PersonRoleMaintenanceLogVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "人员角色维护日志导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", PersonRoleMaintenanceLogDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        PersonRoleMaintenanceLogExcelListener excelReadListener = new PersonRoleMaintenanceLogExcelListener();
        EasyExcel.read(inputStream,PersonRoleMaintenanceLogDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<PersonRoleMaintenanceLogDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("人员角色维护日志导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<PersonRoleMaintenanceLog> personRoleMaintenanceLoges =BeanCopyUtils.convertListTo(dtoS,PersonRoleMaintenanceLog::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::PersonRoleMaintenanceLog-import::id", importId, personRoleMaintenanceLoges, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<PersonRoleMaintenanceLog> personRoleMaintenanceLoges = (List<PersonRoleMaintenanceLog>) orionJ2CacheService.get("pmsx::PersonRoleMaintenanceLog-import::id", importId);
        log.info("人员角色维护日志导入的入库数据={}", JSONUtil.toJsonStr(personRoleMaintenanceLoges));

        this.saveBatch(personRoleMaintenanceLoges);
        orionJ2CacheService.delete("pmsx::PersonRoleMaintenanceLog-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::PersonRoleMaintenanceLog-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<PersonRoleMaintenanceLog> condition = new LambdaQueryWrapperX<>( PersonRoleMaintenanceLog. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(PersonRoleMaintenanceLog::getCreateTime);
        List<PersonRoleMaintenanceLog> personRoleMaintenanceLoges =   this.list(condition);

        List<PersonRoleMaintenanceLogDTO> dtos = BeanCopyUtils.convertListTo(personRoleMaintenanceLoges, PersonRoleMaintenanceLogDTO::new);

        String fileName = "人员角色维护日志数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", PersonRoleMaintenanceLogDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<PersonRoleMaintenanceLogVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class PersonRoleMaintenanceLogExcelListener extends AnalysisEventListener<PersonRoleMaintenanceLogDTO> {

        private final List<PersonRoleMaintenanceLogDTO> data = new ArrayList<>();

        @Override
        public void invoke(PersonRoleMaintenanceLogDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<PersonRoleMaintenanceLogDTO> getData() {
            return data;
        }
    }


}
