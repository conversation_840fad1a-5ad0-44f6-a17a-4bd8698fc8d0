<script lang="ts">
import { defineComponent, ref } from 'vue';
import { Select } from 'ant-design-vue';

export default defineComponent({
  name: 'EditBarSelect',
  components: { Select },
  props: {
    label: {
      type: String,
      default: null,
    },
    value: {
      type: String,
      default: null,
    },
    options: {
      type: Array,
      default: () => [],
    },
    isEdit: {
      type: Boolean,
      default: true,
    },
  },
  emits: ['change'],
  setup(props, { emit }) {
    const visible = ref(false);
    const dropdownVisible = ref(false);
    const value_:any = ref(props.value);
    // 失去焦点的时回调
    function handleBlur() {
      visible.value = false;
    }
    function handleMouseleave() {
      if (!dropdownVisible.value) {
        visible.value = false;
      }
    }
    function dropdownVisibleChange(bool) {
      dropdownVisible.value = bool;
    }

    function handleChange(value: any) {
      const row:any = props.options.find((item) => item.value === value);
      emit('change', {
        value,
        label: row.label,
      });
    }
    return {
      visible,
      value_,
      handleBlur,
      handleMouseleave,
      dropdownVisibleChange,
      handleChange,
    };
  },

});
</script>

<template>
  <div
    v-if="isEdit"
    style="width: 100%;min-height: 30px;"
    @mouseover="visible=true"
  >
    <Select
      v-if="visible"
      v-model:value="value_"
      placeholder="请选择"
      style="width: 100%"
      :options="options"
      @blur="handleBlur"
      @mouseleave="handleMouseleave"
      @dropdownVisibleChange="dropdownVisibleChange"
      @change="handleChange"
    />
    <div v-else>
      {{ label }}
    </div>
  </div>
  <div v-else>
    {{ label }}
  </div>
</template>

<style scoped lang="less">

</style>
