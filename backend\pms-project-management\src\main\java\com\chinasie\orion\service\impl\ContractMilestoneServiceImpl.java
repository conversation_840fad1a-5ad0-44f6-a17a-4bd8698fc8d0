package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.base.api.domain.entity.*;
import com.chinasie.orion.base.api.repository.*;
import com.chinasie.orion.bo.DataStatusNBO;
import com.chinasie.orion.bo.DictNBO;
import com.chinasie.orion.bo.UserDeptBo;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.*;
import com.chinasie.orion.domain.dto.ContractMilestoneDTO;
import com.chinasie.orion.domain.dto.ContractMilestoneExcelTplDTO;
import com.chinasie.orion.domain.dto.ContractMilestoneTreeDTO;
import com.chinasie.orion.domain.dto.ContractMilestoneTreeExcelExportDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.tree.MileStoneTreeVo;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.domain.vo.workflow.FlowTemplateBusinessVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.export.ImportExcelErrorNoteVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.BaseErrorCode;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.WorkflowFeignService;
import com.chinasie.orion.file.api.domain.vo.FileTreeVO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.file.api.service.FileApiService;
import com.chinasie.orion.management.domain.entity.CustomerInfo;
import com.chinasie.orion.management.domain.entity.ProjectInitiationSimple;
import com.chinasie.orion.management.domain.entity.ProjectInvoice;
import com.chinasie.orion.management.domain.vo.ProjectOrderVO;
import com.chinasie.orion.management.repository.ProjectInitiationSimpleMapper;
import com.chinasie.orion.management.service.CustomerInfoService;
import com.chinasie.orion.management.service.ProjectInitiationService;
import com.chinasie.orion.management.service.ProjectInvoiceService;
import com.chinasie.orion.management.service.ProjectOrderService;
import com.chinasie.orion.msc.ContractMilestoneMessageHandler;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.*;
import com.chinasie.orion.sdk.domain.vo.business.*;
import com.chinasie.orion.sdk.domain.vo.org.DeptLeaderRelationVO;
import com.chinasie.orion.sdk.domain.vo.org.SimpleDeptVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.domain.vo.user.UserPartTimeVO;
import com.chinasie.orion.sdk.helper.*;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.TreeInfoProcessor;
import com.chinasie.orion.util.TreeUtil;
import io.minio.messages.Permission;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <p>
 * ContractMilestone 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-29 15:01:52
 */
@Service
@Slf4j
public class ContractMilestoneServiceImpl extends OrionBaseServiceImpl<ContractMilestoneMapper, ContractMilestone> implements ContractMilestoneService {

    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private MarketContractMapper marketContractMapper;

    @Autowired
    private MarketContractMilestoneAcceptanceMapper marketContractMilestoneAcceptanceMapper;

    @Autowired
    private MarketContractMilestoneRescheduleMapper marketContractMilestoneRescheduleMapper;

    @Autowired
    private MarketContractMilestoneExceptionMapper marketContractMilestoneExceptionMapper;

    @Autowired
    private FileApiService fileApi;

    @Autowired
    private CustomerInfoService customerInfoService;

    @Autowired
    private ContractMilestoneMessageHandler messageHandler;

    @Autowired
    private DictRedisHelper dictRedisHelper;

    @Autowired
    private UserRedisHelper userRedisHelper;
    @Resource
    private DeptDOMapper deptDOMapper;
    @Autowired
    private DeptRedisHelper deptRedisHelper;
    @Autowired
    private RoleUserDOMapper roleUserDOMapper;
    @Autowired
    private DeptUserDOMapper deptUserDOMapper;
    @Autowired
    private UserDOMapper userDOMapper;
    @Autowired
    private RoleDOMapper roleDOMapper;
    @Autowired
    private RoleRedisHelper roleRedisHelper;
    @Autowired
    private RoleRedisHelper roleUserHelper;
    @Autowired
    private ProjectInitiationService projectInitiationService;
    @Autowired
    private DeptUserHelper deptUserHelper;
    @Autowired
    private ContractOurSignedSubjectService contractOurSignedSubjectService;
    @Autowired
    private CommonDataAuthRoleService commonDataAuthRoleService;
    @Autowired
    private MarketContractService marketContractService;
    @Autowired
    private ProjectInvoiceService projectInvoiceService;
    @Autowired
    private ProjectOrderService projectOrderService;
    @Autowired
    private DeptLeaderDORepository deptLeaderDORepository;
    @Autowired
    private CommonDataAuthRoleMapper commonDataAuthRoleMapper;


    @Autowired
    private ContractMilestoneMapper contractMilestoneMapper;

    @Autowired
    private MarketContractMapper MarketContractMapper;

    @Autowired
    private WorkflowFeignService workflowFeignService;

    @Autowired
    private ProjectInitiationSimpleMapper projectInitiationSimpleMapper;

    @Autowired
    private ContractMilestoneSimpleMapper contractMilestoneSimpleMapper;

    @Autowired
    private DataStatusHelper dataStatusHelper;
    @Autowired
    private DeptLeaderHelper deptLeaderHelper;
    @Autowired
    private InvoicingRevenueAccountingService invoicingRevenueAccountingService;
    @Autowired
    private ProvisionalIncomeAccountingService provisionalIncomeAccountingService;
    @Autowired
    private AdvancePaymentInvoicedService advancePaymentInvoicedService;
    @Autowired
    private UserDeptBo userDeptBo;
    @Autowired
    private DataStatusNBO dataStatusNBO;
    @Autowired
    private DictNBO dictNBO;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ContractMilestoneVO detail(String id, String pageCode) throws Exception {
        ContractMilestone contractMilestone = this.getById(id);
        ContractMilestoneVO result = BeanCopyUtils.convertTo(contractMilestone, ContractMilestoneVO::new);
        result.setNumber(contractMilestone.getNumber());
        setEveryName(Collections.singletonList(result));
        if (StrUtil.isNotBlank(contractMilestone.getOfficeLeader())) {
            UserVO officeLeader = userRedisHelper.getUserById(contractMilestone.getOfficeLeader());
            if (officeLeader != null) {
                result.setOfficeLeader(officeLeader.getName());
            } else {
                result.setOfficeLeader(null);
            }
        } else {
            result.setOfficeLeader(null);
        }
        if (StrUtil.isNotBlank(contractMilestone.getDepartmental())) {
            DeptVO dept = deptRedisHelper.getDeptById(contractMilestone.getDepartmental());
            if (dept != null) {
                result.setDepartmentalName(dept.getName());
            } else {
                result.setDepartmentalName(null);
            }
        } else {
            result.setDepartmentalName(null);
        }

        List<MarketContractMilestoneAcceptance> list = marketContractMilestoneAcceptanceMapper.selectList(MarketContractMilestoneAcceptance::getMilestoneId, id);
        result.setAcceptanceList(BeanCopyUtils.convertListTo(list, MarketContractMilestoneAcceptanceVO::new));
        List<MarketContractMilestoneReschedule> rescheduleList = marketContractMilestoneRescheduleMapper.selectList(MarketContractMilestoneReschedule::getMilestoneId, id);
        result.setRescheduleList(BeanCopyUtils.convertListTo(rescheduleList, MarketContractMilestoneRescheduleVO::new));

        List<MarketContractMilestoneException> exceptionList = marketContractMilestoneExceptionMapper.selectList(MarketContractMilestoneException::getMilestoneId, id);
        result.setExceptionList(BeanCopyUtils.convertListTo(exceptionList, MarketContractMilestoneExceptionVO::new));
        if (ObjectUtil.isNotEmpty(rescheduleList)) {
            List<String> reschedules = rescheduleList.stream().map(MarketContractMilestoneReschedule::getId).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(reschedules)) {
                List<FileTreeVO> fileTreeVOS1 = fileApi.getFilesByDataIds(reschedules);
                List<FileVO> fileVOS = BeanCopyUtils.convertListTo(fileTreeVOS1, FileVO::new);
                if (ObjectUtil.isNotEmpty(fileVOS)) {
                    result.setReschedulesFileList(fileVOS);

                }
            }
        }
        if (!CollectionUtils.isEmpty(list)) {
            List<String> dataIds = list.stream().map(MarketContractMilestoneAcceptance::getId).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(dataIds)) {
                List<FileTreeVO> fileTreeVOS = fileApi.getFilesByDataIds(dataIds);
                List<FileVO> fileVOS = BeanCopyUtils.convertListTo(fileTreeVOS, FileVO::new);
                if (ObjectUtil.isNotEmpty(fileVOS)) {
                    result.setFileList(fileVOS);
                }
            }
            BigDecimal sum = list.stream()
                    .map(MarketContractMilestoneAcceptance::getAcceptanceAmt) // 将对象A转换为BigDecimal B
                    .filter(Objects::nonNull) // 过滤掉null值
                    .reduce(BigDecimal.ZERO, BigDecimal::add); // 计算剩余金额的和
            result.setActualMilestoneAmt(sum);
        }

        String parentId = contractMilestone.getParentId();
        if (StringUtils.isNotBlank(parentId)) {
            ContractMilestone parent = this.getById(parentId);
            if (parent != null) {
                result.setParentName(parent.getMilestoneName());
            }
            //计算实际验收金额（不含税） 子里程碑的
            BigDecimal taxRate = result.getTaxRate();
            BigDecimal actualMilestoneAmt = result.getActualMilestoneAmt();
            if (ObjectUtil.isNotEmpty(taxRate) && ObjectUtil.isNotEmpty(actualMilestoneAmt)) {
                BigDecimal sum = BigDecimal.ONE.add(taxRate.divide(BigDecimal.valueOf(100)));
                BigDecimal actualAmtNoTax = actualMilestoneAmt.divide(sum, 2, RoundingMode.HALF_UP);
                result.setActualAmtNoTax(actualAmtNoTax);
            }
        } else {
            //是父里程碑 税率要显示子里程碑的拼接 并且实际验收金额（不含税）要算出来
            LambdaQueryWrapperX<ContractMilestone> contractMilestoneLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            contractMilestoneLambdaQueryWrapperX.eq(ContractMilestone::getParentId, id);
            List<ContractMilestone> childMilestones = this.list(contractMilestoneLambdaQueryWrapperX);
            if (childMilestones.size() > 1) {
                //实际验收金额（不含税）要把所有的子里程碑的都到哪都算出来 然后累加作为里程碑的实际验收金额（不含税）
                BigDecimal actualAmtNoTax = BigDecimal.ZERO;
                int size = childMilestones.size();
                for (int i = 0; i < size; i++) {
                    ContractMilestone milestone = childMilestones.get(i);
                    BigDecimal taxRate = milestone.getTaxRate();
                    BigDecimal actualMilestoneAmt = milestone.getActualMilestoneAmt();
                    if (ObjectUtil.isNotEmpty(taxRate) && ObjectUtil.isNotEmpty(actualMilestoneAmt)) {
                        BigDecimal sum = BigDecimal.ONE.add(taxRate.divide(BigDecimal.valueOf(100)));
                        actualAmtNoTax = actualAmtNoTax.add(actualMilestoneAmt.divide(sum, 2, RoundingMode.HALF_UP));
                    }
                    result.setActualAmtNoTax(actualAmtNoTax);
                }
            } else {
                //里程碑只有一个子里程碑或者没有里程碑的 那他的际验收金额（不含税）就是里程碑自己的 不需要考虑子里程碑
                if (childMilestones.size() == 1) {
                    ContractMilestone milestone = childMilestones.get(0);
                    result.setTaxRate(milestone.getTaxRate());
                }
                BigDecimal taxRate = result.getTaxRate();
                BigDecimal actualMilestoneAmt = result.getActualMilestoneAmt();
                if (ObjectUtil.isNotEmpty(taxRate) && ObjectUtil.isNotEmpty(actualMilestoneAmt)) {
                    BigDecimal sum = BigDecimal.ONE.add(taxRate.divide(BigDecimal.valueOf(100)));
                    BigDecimal actualAmtNoTax = actualMilestoneAmt.divide(sum, 2, RoundingMode.HALF_UP);
                    result.setActualAmtNoTax(actualAmtNoTax);
                }

            }

        }
        // 查找里程碑的子项目
        List<ContractMilestone> children = this.list(new LambdaQueryWrapper<>(ContractMilestone.class)
                .eq(ContractMilestone::getParentId, id));
        List<ContractMilestoneVO> contractMilestoneVOS1 = new ArrayList<>();
        List<DictValueVO> dictList = dictRedisHelper.getDictList("dict1796866395473141760");//市场合同类型
        Map<String, String> contractTypeMap = dictList.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription));
        MarketContract marketContract = marketContractService.getById(contractMilestone.getContractId());
        if (ObjectUtil.isNotEmpty(children)) {
            for (ContractMilestone child : children) {
                String childId = child.getId();
                ContractMilestoneVO detail = this.detail(childId, null);
                if (ObjectUtil.isNotEmpty(marketContract)) {
                    String name = contractTypeMap.get(marketContract.getContractType());
                    if (ObjectUtil.isNotEmpty(name)) {
                        detail.setContractTypeName(name);
                    }
                }
                contractMilestoneVOS1.add(detail);
            }
        }
        if (ObjectUtil.isNotEmpty(contractMilestoneVOS1)) {
            result.setChildren(contractMilestoneVOS1);
        }
        //框架合同商务负责人Id
        String frameContractId = contractMilestone.getContractId();
        if (ObjectUtil.isNotEmpty(frameContractId)) {
            MarketContract frameContract = marketContractService.getById(frameContractId);
            if (ObjectUtil.isNotEmpty(frameContract)) {
                String commerceRspUser = frameContract.getCommerceRspUser();
                if (ObjectUtil.isNotEmpty(commerceRspUser)) {
                    result.setFrameContractBusinessId(commerceRspUser);
                }
            }

        }
        //增加是否可以跳转到合同的权限
        result.setIsAbleJumpToContract(false);
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        SimpleUser simplerUser = userRedisHelper.getSimplerUser(CurrentUserHelper.getOrgId(), currentUserId);
        List<RoleVO> roleVOList = roleRedisHelper.getRoleByUserId(CurrentUserHelper.getOrgId(), CurrentUserHelper.getCurrentUserId());
        String orgId = simplerUser.getOrgId();
        List<String> roles = new ArrayList<>();
        if (!CollectionUtils.isEmpty(roleVOList)) {
            roles = roleVOList.stream().map(RoleVO::getCode).collect(Collectors.toList());
        }
        MarketContract marketContractServiceById = marketContractService.getById(frameContractId);
        if (ObjectUtil.isNotEmpty(marketContractServiceById)) {
            if ((roles.contains("Business_021") || roles.contains("Business_020") || roles.contains("Business_022") || roles.contains("Business_023")
                    || roles.contains("Business_06") || roles.contains("Company_01") || roles.contains("Company_03") || roles.contains("company100144"))) {//公司商务，计划经营部中心主任，营销管理分管领导，总经理，董事长
                result.setIsAbleJumpToContract(true);
            } else if (roles.contains("Business_01") || roles.contains("JY001") || roles.contains("Business_05")) {
                String techRspDept = marketContractServiceById.getTechRspDept();
                if (ObjectUtil.isNotEmpty(techRspDept) && techRspDept.equals(orgId)) {
                    result.setIsAbleJumpToContract(true);
                }
            } else {
                if (marketContractServiceById.getCreatorId().equals(currentUserId) || currentUserId.equals(marketContractServiceById.getTechRspUser())
                        || currentUserId.equals(marketContractServiceById.getCommerceRspUser())) {
                    result.setIsAbleJumpToContract(true);
                }
            }
        }
        //已确认开票收入
        LambdaQueryWrapper<InvoicingRevenueAccounting> invoicingRevenueAccountingLambdaQueryWrapper = new LambdaQueryWrapper<>();
        invoicingRevenueAccountingLambdaQueryWrapper.select(InvoicingRevenueAccounting::getId, InvoicingRevenueAccounting::getAmtNoTax);
        invoicingRevenueAccountingLambdaQueryWrapper.eq(InvoicingRevenueAccounting::getMilestoneId, id);
        List<InvoicingRevenueAccounting> invoicingRevenueAccountingList = invoicingRevenueAccountingService.list(invoicingRevenueAccountingLambdaQueryWrapper);
        BigDecimal confirmIncomeInvoicing = BigDecimal.ZERO;
        if (ObjectUtil.isNotEmpty(invoicingRevenueAccountingList)) {
            for (InvoicingRevenueAccounting invoicingRevenueAccounting : invoicingRevenueAccountingList) {
                BigDecimal amtNoTax = invoicingRevenueAccounting.getAmtNoTax();
                if (ObjectUtil.isNotEmpty(amtNoTax)) {
                    confirmIncomeInvoicing = confirmIncomeInvoicing.add(amtNoTax);
                }
            }
        }
        result.setConfirmIncomeInvoicing(confirmIncomeInvoicing);

        //已确认暂估收入
        LambdaQueryWrapper<ProvisionalIncomeAccounting> provisionalIncomeAccountingLambdaQueryWrapper = new LambdaQueryWrapper<>();
        provisionalIncomeAccountingLambdaQueryWrapper.select(ProvisionalIncomeAccounting::getId, ProvisionalIncomeAccounting::getAmtNoTax);
        provisionalIncomeAccountingLambdaQueryWrapper.eq(ProvisionalIncomeAccounting::getMilestoneId, id);
        List<ProvisionalIncomeAccounting> provisionalIncomeAccountingList = provisionalIncomeAccountingService.list(provisionalIncomeAccountingLambdaQueryWrapper);
        BigDecimal confirmIncomeProvisionalEstimate = BigDecimal.ZERO;
        if (ObjectUtil.isNotEmpty(provisionalIncomeAccountingList)) {
            for (ProvisionalIncomeAccounting provisionalIncomeAccounting : provisionalIncomeAccountingList) {
                BigDecimal amtNoTax = provisionalIncomeAccounting.getAmtNoTax();
                if (ObjectUtil.isNotEmpty(amtNoTax)) {
                    confirmIncomeProvisionalEstimate = confirmIncomeProvisionalEstimate.add(amtNoTax);
                }
            }
        }
        result.setConfirmIncomeProvisionalEstimate(confirmIncomeProvisionalEstimate);

        //已预收款开票金额
        LambdaQueryWrapper<AdvancePaymentInvoiced> advancePaymentInvoicedLambdaQueryWrapper = new LambdaQueryWrapper<>();
        advancePaymentInvoicedLambdaQueryWrapper.select(AdvancePaymentInvoiced::getId, AdvancePaymentInvoiced::getAmtNoTax);
        advancePaymentInvoicedLambdaQueryWrapper.eq(AdvancePaymentInvoiced::getMilestoneId, id);
        List<AdvancePaymentInvoiced> advancePaymentInvoicedList = advancePaymentInvoicedService.list(advancePaymentInvoicedLambdaQueryWrapper);
        BigDecimal milestoneAdvanceAmt = BigDecimal.ZERO;
        if (ObjectUtil.isNotEmpty(advancePaymentInvoicedList)) {
            for (AdvancePaymentInvoiced advancePaymentInvoiced : advancePaymentInvoicedList) {
                BigDecimal amtNoTax = advancePaymentInvoiced.getAmtNoTax();
                if (ObjectUtil.isNotEmpty(amtNoTax)) {
                    milestoneAdvanceAmt = milestoneAdvanceAmt.add(amtNoTax);
                }
            }
        }
        result.setMilestoneAdvanceAmt(milestoneAdvanceAmt);

        //
        BigDecimal confirmIncomeSum = confirmIncomeInvoicing.add(confirmIncomeProvisionalEstimate).add(milestoneAdvanceAmt);
        result.setConfirmIncomeSum(confirmIncomeSum);


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param contractMilestoneDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String create(ContractMilestoneDTO contractMilestoneDTO) {
        MarketContract marketContract = marketContractMapper.selectById(contractMilestoneDTO.getContractId());
        if (marketContract == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "未找到合同信息！");
        }
        contractMilestoneDTO.setCusPersonId(StringUtils.isNotBlank(marketContract.getCustPersonId()) ? marketContract.getCustPersonId() : "");
        contractMilestoneDTO.setExpectAcceptDate(contractMilestoneDTO.getPlanAcceptDate());
        ContractMilestone contractMilestone = BeanCopyUtils.convertTo(contractMilestoneDTO, ContractMilestone::new);
        //新增 如果custPerson被使用了，在表里增加标识
        String custPerson = contractMilestoneDTO.getCusPersonId();
        if (ObjectUtil.isNotEmpty(custPerson)) {
            CustomerInfo customerInfo = customerInfoService.getById(custPerson);
            if (ObjectUtil.isNotEmpty(customerInfo)) {
                customerInfo.setIsUsed("1");
                customerInfoService.updateById(customerInfo);
            }
        }
        String ammountType = contractMilestone.getAmmountType();
        if (ObjectUtil.isNotEmpty(ammountType) && ammountType.equals("milestone_amt")) {
            BigDecimal amt = contractMilestoneDTO.getAmt();
            contractMilestone.setMilestoneAmt(amt);


        } else if (ObjectUtil.isNotEmpty(ammountType) && ammountType.equals("except_acceptance_amt")) {
            BigDecimal amt = contractMilestoneDTO.getAmt();
            contractMilestone.setExceptAcceptanceAmt(amt);
        }
        String dateType = contractMilestone.getDateType();
        if (ObjectUtil.isNotEmpty(dateType) && dateType.equals("plan_accept_date")) {
            Date checkDate = contractMilestoneDTO.getCheckDate();
            contractMilestone.setPlanAcceptDate(checkDate);

        } else if (ObjectUtil.isNotEmpty(dateType) && dateType.equals("expect_accept_date")) {
            Date checkDate = contractMilestoneDTO.getCheckDate();
            contractMilestone.setExpectAcceptDate(checkDate);
        }

        this.save(contractMilestone);

//        this.updateParentMilestoneAmt(contractMilestoneDTO.getParentId());

        return contractMilestone.getId();
    }

    @Override
    public List<Map<String, Object>> queryFile(String dataId) throws Exception {
        List<Map<String, Object>> result = new ArrayList<>();

        ContractMilestone contractMilestone = this.getById(dataId);
        if (contractMilestone == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "里程碑信息未找到！");
        }

        Map<String, Object> acceptanceMap = new HashMap<>();
        acceptanceMap.put("type", "acceptance");
        result.add(acceptanceMap);
        Map<String, Object> rescheduleMap = new HashMap<>();
        rescheduleMap.put("type", "reschedule");
        result.add(rescheduleMap);
        Map<String, Object> exceptionMap = new HashMap<>();
        exceptionMap.put("type", "exception");
        result.add(exceptionMap);
        Map<String, Object> milestoneMap = new HashMap<>();
        milestoneMap.put("type", "milestone");
        result.add(exceptionMap);
        List<MarketContractMilestoneAcceptance> acceptanceList = marketContractMilestoneAcceptanceMapper.selectList(MarketContractMilestoneAcceptance::getMilestoneId, dataId);
        if (!CollectionUtils.isEmpty(acceptanceList)) {
            List<String> acceptanceIds = acceptanceList.stream().map(MarketContractMilestoneAcceptance::getId).collect(Collectors.toList());
            List<FileTreeVO> fileVOList = fileApi.getFilesByDataIds(acceptanceIds);
            acceptanceMap.put("files", fileVOList);
        }

        List<MarketContractMilestoneReschedule> rescheduleList = marketContractMilestoneRescheduleMapper.selectList(MarketContractMilestoneReschedule::getMilestoneId, dataId);
        if (!CollectionUtils.isEmpty(rescheduleList)) {
            List<String> rescheduleIds = rescheduleList.stream().map(MarketContractMilestoneReschedule::getId).collect(Collectors.toList());
            List<FileTreeVO> fileVOList = fileApi.getFilesByDataIds(rescheduleIds);
            rescheduleMap.put("files", fileVOList);
        }

        List<MarketContractMilestoneException> exceptionList = marketContractMilestoneExceptionMapper.selectList(MarketContractMilestoneException::getMilestoneId, dataId);
        if (!CollectionUtils.isEmpty(exceptionList)) {
            List<String> exceptionIds = exceptionList.stream().map(MarketContractMilestoneException::getId).collect(Collectors.toList());
            List<FileTreeVO> fileVOList = fileApi.getFilesByDataIds(exceptionIds);
            exceptionMap.put("files", fileVOList);
        }


        List<FileVO> fileVOList = fileApi.getFilesByDataId(dataId);
        milestoneMap.put("files", fileVOList);
        return result;
    }

    /**
     * 编辑
     * <p>
     * * @param contractMilestoneDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean edit(ContractMilestoneDTO contractMilestoneDTO) throws Exception {
        ContractMilestone oldContractMilestone = this.getById(contractMilestoneDTO.getId());
        if (oldContractMilestone == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "里程碑未找到！");
        }

        if (!MarketContractMilestoneStatusEnum.CREATED.getStatus().equals(oldContractMilestone.getStatus())) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "当前里程碑状态不是未启动状态，不能修改！");
        }
        //防止前端传过来的parentId存在有直属合同的 污染数据库 所以取出原来的再次替代更新数据
        String parentId = oldContractMilestone.getParentId();
        contractMilestoneDTO.setParentId(parentId);
        ContractMilestone contractMilestone = BeanCopyUtils.convertTo(contractMilestoneDTO, ContractMilestone::new);

        MarketContract marketContract = marketContractMapper.selectById(contractMilestone.getContractId());
        if (MarketContractStatusEnum.AUDITING.getStatus().equals(marketContract.getStatus())) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "合同正在审核中，不能修改里程碑！");
        }
        String dateTypeNew = contractMilestoneDTO.getDateType();
        String dateTypeOld = oldContractMilestone.getDateType();
        String techRspUser = contractMilestoneDTO.getTechRspUser();
        if (ObjectUtil.isNotEmpty(techRspUser)) {
            //如果技术接口人变了 所级和承担部门也变
            SimpleUser simplerUser = userRedisHelper.getSimplerUser(CurrentUserHelper.getOrgId(), techRspUser);
            if (ObjectUtil.isNotEmpty(simplerUser)) {
                String orgId = simplerUser.getOrgId();
                String deptId = simplerUser.getDeptId();
                contractMilestone.setUndertDept(orgId);
                contractMilestone.setOfficeDept(deptId);
            }
        }
        //当前端传过来的税率字段是null 则这个新增对象的税率不发生修改
        BigDecimal taxRate = contractMilestoneDTO.getTaxRate();
        BigDecimal taxRateOld = oldContractMilestone.getTaxRate();
        if (ObjectUtil.isEmpty(taxRate)) {
            contractMilestone.setTaxRate(taxRateOld);
        } else {
            contractMilestone.setTaxRate(taxRate);
        }

        String ammountType = contractMilestone.getAmmountType();

        if (ObjectUtil.isNotEmpty(ammountType) && ammountType.equals("milestone_amt")) {
            BigDecimal amt = contractMilestoneDTO.getAmt();
            contractMilestone.setMilestoneAmt(amt);
            contractMilestone.setExceptAcceptanceAmt(BigDecimal.ZERO);
        } else if (ObjectUtil.isNotEmpty(ammountType) && ammountType.equals("except_acceptance_amt")) {
            BigDecimal amt = contractMilestoneDTO.getAmt();
            contractMilestone.setExceptAcceptanceAmt(amt);
            contractMilestone.setMilestoneAmt(BigDecimal.ZERO);
        }

        String dateType = contractMilestone.getDateType();
        if (ObjectUtil.isNotEmpty(dateType) && dateType.equals("plan_accept_date")) {
            Date checkDate = contractMilestoneDTO.getCheckDate();
            contractMilestone.setPlanAcceptDate(checkDate);
            contractMilestone.setExpectAcceptDate(null);

        } else if (ObjectUtil.isNotEmpty(dateType) && dateType.equals("expect_accept_date")) {
            Date checkDate = contractMilestoneDTO.getCheckDate();
            contractMilestone.setExpectAcceptDate(checkDate);
            contractMilestone.setPlanAcceptDate(null);
        }

        this.updateById(contractMilestone);

        if (ObjectUtil.isNotEmpty(dateTypeNew) && ObjectUtil.isNotEmpty(dateTypeOld)) {
            if (!dateTypeOld.equals(dateTypeNew)) {
                if (dateTypeOld.equals("plan_accept_date")) {
                    this.baseMapper.setPlanAcceptDateToNull(contractMilestone.getId());
                } else {
                    this.baseMapper.setExpectAcceptDateToNull(contractMilestone.getId());
                }
            }
        }


        //当修改里程碑的客户时 这个里程碑的子里程碑的客户也都跟着修改
        String cusPersonId = contractMilestoneDTO.getCusPersonId();
        if (ObjectUtil.isNotEmpty(cusPersonId)) {
            LambdaQueryWrapperX<ContractMilestone> contractMilestoneLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            contractMilestoneLambdaQueryWrapperX.eq(ContractMilestone::getParentId, contractMilestoneDTO.getId());
            List<ContractMilestone> contractMilestones = this.list(contractMilestoneLambdaQueryWrapperX);
            if (ObjectUtil.isNotEmpty(contractMilestones)) {
                for (ContractMilestone milestone : contractMilestones) {
                    milestone.setCusPersonId(cusPersonId);
                    this.updateById(milestone);
                }
            }

        }

        if (StringUtils.isEmpty(contractMilestone.getContractId())) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "里程碑相关合同异常");
        }

        this.updateParentMilestoneAmt(oldContractMilestone.getParentId());

//        Set<String> toUser = new HashSet<>();
//        toUser.add(marketContract.getTechRspUser());
//        toUser.add(marketContract.getCommerceRspUser());
//        toUser.add(contractMilestone.getTechRspUser());
//        toUser.add(contractMilestone.getBusRspUser());
//        List<String> ids = new ArrayList<>(toUser);
//        String businessId = contractMilestoneDTO.getId();
//        messageHandler.sendEditMessage(businessId
//                , "/pas/milestones-details"
//                , marketContract.getName()
//                , contractMilestone.getMilestoneName()
//                , ids
//                , contractMilestone.getPlatformId()
//                , contractMilestone.getOrgId()
//                , ContractMilestoneNode.NODE_MILESTONE_DATE_EDIT
//                , contractMilestoneDTO.getOriginPlanAcceptDate()
//                , contractMilestoneDTO.getPlanAcceptDate());

        return true;
    }


    @Override
    public Boolean close(String id) throws Exception {
        ContractMilestone contractMilestone = this.getById(id);
        if (contractMilestone == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "里程碑未找到！");
        }

        if (!MarketContractMilestoneStatusEnum.PROGRESS.getStatus().equals(contractMilestone.getStatus())) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "里程碑不是进行中不能关闭！");
        }
        contractMilestone.setStatus(MarketContractMilestoneStatusEnum.COMPLATED.getStatus());
        this.updateById(contractMilestone);
        return true;
    }

    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(List<String> ids) {
        // 里程碑只有单个删除，更新一下父里程碑的情况
        final ContractMilestone contractMilestone = this.getById(ids.get(0));
        final String parentId = contractMilestone.getParentId();
        if (ObjectUtil.isEmpty(parentId)) {
            //要删除该里程碑下所有的子里程碑
            LambdaQueryWrapperX<ContractMilestone> contractMilestoneLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            contractMilestoneLambdaQueryWrapperX.eq(ContractMilestone::getParentId, contractMilestone.getId());
            this.remove(contractMilestoneLambdaQueryWrapperX);
        }

        this.removeBatchByIds(ids);

//        this.updateParentMilestoneAmt(parentId);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ContractMilestoneVO> pages(Page<ContractMilestoneDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ContractMilestone> condition = new LambdaQueryWrapperX<>(ContractMilestone.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ContractMilestone::getCreateTime);
        ContractMilestoneDTO contractMilestoneDTO = pageRequest.getQuery();
        if (contractMilestoneDTO != null) {
            String contractId = contractMilestoneDTO.getContractId();
            if (StringUtils.isNotBlank(contractId)) {
                condition.eq(ContractMilestone::getContractId, contractId);
            }
        }

        Page<ContractMilestone> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ContractMilestone::new));

        PageResult<ContractMilestone> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ContractMilestoneVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ContractMilestoneVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ContractMilestoneVO::new);
        if (!CollectionUtils.isEmpty(vos)) {
            vos.forEach(item -> {
                long surplusDays = 0L;
                Date expectAcceptDate = item.getExpectAcceptDate();
                if (expectAcceptDate != null) {
                    surplusDays = DateUtil.betweenDay(new Date(), expectAcceptDate, true);
                    if (surplusDays < 0) {
                        surplusDays = 0;
                    }
                }
                item.setSurplusDays(surplusDays);
            });
        }

        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public Page<ContractMilestoneVO> relationPages(Page<ContractMilestoneDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<ContractMilestone> condition = new LambdaQueryWrapperX<>(ContractMilestone.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ContractMilestone::getCreateTime);
        ContractMilestoneDTO contractMilestoneDTO = pageRequest.getQuery();
        if (contractMilestoneDTO != null) {
            String contractId = contractMilestoneDTO.getContractId();
            if (StringUtils.isNotBlank(contractId)) {
                condition.eq(ContractMilestone::getContractId, contractId);
            }
        }
        condition.eq(ContractMilestone::getStatus, MarketContractMilestoneStatusEnum.PROGRESS.getStatus());
        Page<ContractMilestone> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ContractMilestone::new));

        PageResult<ContractMilestone> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ContractMilestoneVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ContractMilestoneVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ContractMilestoneVO::new);
        setEveryName(vos);
        if (!CollectionUtils.isEmpty(vos)) {
            List<String> contractIds = vos.stream().map(ContractMilestoneVO::getContractId).collect(Collectors.toList());
            List<MarketContract> contracts = marketContractMapper.selectBatchIds(contractIds);
            Map<String, String> contractMap = contracts.stream().collect(Collectors.toMap(MarketContract::getId, MarketContract::getNumber));
            vos.forEach(item -> {
                item.setContractNumber(contractMap.get(item.getContractId()));
                long surplusDays = 0L;
                Date expectAcceptDate = item.getExpectAcceptDate();
                if (expectAcceptDate != null) {
                    surplusDays = DateUtil.betweenDay(new Date(), expectAcceptDate, true);
                    if (surplusDays < 0) {
                        surplusDays = 0;
                    }
                }
                item.setSurplusDays(surplusDays);
            });
        }
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public ContractMilestoneTreeTotalVO tree(ContractMilestoneTreeDTO contractMilestoneTreeDTO) throws Exception {
        ContractMilestoneTreeTotalVO rsp = new ContractMilestoneTreeTotalVO();

        MarketContract marketContract = marketContractMapper.selectById(contractMilestoneTreeDTO.getContractId());
        if (marketContract == null) {
            return rsp;
        }

        LambdaQueryWrapperX<ContractMilestone> condition = new LambdaQueryWrapperX<>(ContractMilestone.class);
        condition.eq(ContractMilestone::getContractId, contractMilestoneTreeDTO.getContractId());
        if (!CollectionUtils.isEmpty(contractMilestoneTreeDTO.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(contractMilestoneTreeDTO.getSearchConditions(), condition);
        }

        List<ContractMilestone> list = this.baseMapper.selectList(condition);
        List<ContractMilestoneVO> contractMilestoneVOList = BeanCopyUtils.convertListTo(list, ContractMilestoneVO::new);
        setEveryName(contractMilestoneVOList);
        List<ContractMilestoneTreeVO> contractMilestoneVOS = BeanCopyUtils.convertListTo(contractMilestoneVOList, ContractMilestoneTreeVO::new);
        contractMilestoneVOS.forEach(item -> {
            item.setRelationContractNumber(marketContract.getNumber());
        });
        List<MarketContract> marketContracts = marketContractMapper.selectList(MarketContract::getFrameContractId, contractMilestoneTreeDTO.getContractId());
        if (!CollectionUtils.isEmpty(marketContracts)) {
            List<String> contractIds = marketContracts.stream().map(MarketContract::getId).collect(Collectors.toList());
            Map<String, String> contractMap = marketContracts.stream().collect(Collectors.toMap(MarketContract::getId, MarketContract::getNumber));
            List<ContractMilestone> subList = this.baseMapper.selectList(ContractMilestone::getContractId, contractIds);
            if (!CollectionUtils.isEmpty(subList)) {
                List<ContractMilestoneTreeVO> subListVO = BeanCopyUtils.convertListTo(subList, ContractMilestoneTreeVO::new);
                subListVO.forEach(item -> {
                    item.setRelationContractNumber(contractMap.get(item.getContractId()));
                });
                contractMilestoneVOS.addAll(subListVO);
            }
        }

        BigDecimal contractTotalAmt = new BigDecimal(0);
        BigDecimal actualMilestoneTotalAmt = new BigDecimal(0);
        for (ContractMilestoneTreeVO item : contractMilestoneVOS) {
            long surplusDays = 0L;
            Date expectAcceptDate = item.getExpectAcceptDate();
            if (expectAcceptDate != null) {
                if (new Date().compareTo(expectAcceptDate) >= 0) {
                    surplusDays = 0;
                } else {
                    surplusDays = DateUtil.betweenDay(new Date(), expectAcceptDate, true);
                    if (surplusDays < 0) {
                        surplusDays = 0;
                    }
                }
            }

            item.setSurplusDays(surplusDays);

            // 里程碑的金额等于子项目金额之和，合同里程碑金额等于各里程碑之和，所以下面统计合同里程碑金额，仅统计类型是里程碑的
            if ("1".equals(item.getMilestoneType())) {
                BigDecimal milestoneAmt = item.getMilestoneAmt() == null ? new BigDecimal(0) : item.getMilestoneAmt();
                BigDecimal actualMilestoneAmt = item.getActualMilestoneAmt() == null ? new BigDecimal(0)
                        : item.getActualMilestoneAmt();
                contractTotalAmt = contractTotalAmt.add(milestoneAmt);
                actualMilestoneTotalAmt = actualMilestoneTotalAmt.add(actualMilestoneAmt);
            }
        }

        List<ContractMilestoneTreeVO> contractMilestoneTreeVOS = TreeUtil.listToTree(contractMilestoneVOS,
                ContractMilestoneTreeVO::getId,
                ContractMilestoneTreeVO::getParentId,
                ContractMilestoneTreeVO::getChildren,
                ContractMilestoneTreeVO::setChildren
        );
        rsp.setContractTotalPriceAmt(Objects.isNull(marketContract.getContractAmt()) ? new BigDecimal(0) : marketContract.getContractAmt());
        rsp.setContractMilestoneTree(contractMilestoneTreeVOS == null ? new ArrayList<ContractMilestoneTreeVO>() : contractMilestoneTreeVOS);
        rsp.setContractTotalAmt(contractTotalAmt);
        rsp.setActualMilestoneTotalAmt(actualMilestoneTotalAmt);
        return rsp;
    }

    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        String fileName = "合同里程碑导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractMilestoneExcelTplDTO.class, new ArrayList<>());
    }

    /**
     * 导入校验
     * <p>
     *
     * @param excel      excel文件
     * @param contractId 导入到哪个合同主数据里
     */
    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel, String contractId) throws Exception {
        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ContractMilestoneExcelListener excelReadListener = new ContractMilestoneExcelListener();
        EasyExcel.read(inputStream, ContractMilestoneDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ContractMilestoneDTO> dtoS = excelReadListener.getData();

        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (StringUtils.isEmpty(contractId)) {
            result.setCode(400);
            result.setOom("未找到合同，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }

        // 导入的数据都是名称，关联id查出来
        // 客户名称,商务接口人工号，技术接口人工号，承接部门名称
        List<String> customerQuery = Lists.newArrayList();
        List<String> deptQuery = Lists.newArrayList();
        List<String> techRspQuery = Lists.newArrayList();
        List<String> busRspQuery = Lists.newArrayList();
        List<String> parentQuery = Lists.newArrayList();

        // 业务分类名称转字典
        List<DictValueVO> cosDict = dictRedisHelper.getDictListByCode(DictConts.COS_BUSINESS_TYPE);
        Map<String, DictValueVO> cosDictMap = cosDict.stream().collect(Collectors.toMap(DictValueVO::getName, Function.identity()));

        List<ImportExcelErrorNoteVO> err = Lists.newArrayList();
        ImportExcelErrorNoteVO en;
        for (int i = 0; i < dtoS.size(); i++) {
            String order = String.valueOf(i + 2);
            ContractMilestoneDTO e = dtoS.get(i);

            e.setContractId(contractId);

            if (StringUtils.isEmpty(e.getMilestoneName())) {
                en = new ImportExcelErrorNoteVO();
                en.setOrder(order);
                en.setErrorNotes(List.of("第" + order + "行里程碑名称不能为空"));
                err.add(en);
            }

            if (StringUtils.isEmpty(e.getDescription())) {
                en = new ImportExcelErrorNoteVO();
                en.setOrder(order);
                en.setErrorNotes(List.of("第" + order + "行里程碑详情不能为空"));
                err.add(en);
            }

            if (null == e.getMilestoneAmt()) {
                en = new ImportExcelErrorNoteVO();
                en.setOrder(order);
                en.setErrorNotes(List.of("第" + order + "行里程碑金额不能为空"));
                err.add(en);
            }

            if (null == e.getTaxRate()) {
                en = new ImportExcelErrorNoteVO();
                en.setOrder(order);
                en.setErrorNotes(List.of("第" + order + "行税率不能为空"));
                err.add(en);
            }

            if (null == e.getPlanAcceptDate()) {
                en = new ImportExcelErrorNoteVO();
                en.setOrder(order);
                en.setErrorNotes(List.of("第" + order + "行计划验收日期不能为空"));
                err.add(en);
            }

            if (StringUtils.isEmpty(e.getMilestoneTypeName())) {
                en = new ImportExcelErrorNoteVO();
                en.setOrder(order);
                en.setErrorNotes(List.of("第" + order + "行里程碑类型不能为空"));
                err.add(en);
            } else {
                if ("子项目".equals(e.getMilestoneTypeName())) {
                    e.setMilestoneType("0");
                } else {
                    e.setMilestoneType("1");
                }
            }

            if (StringUtils.isEmpty(e.getCostBusTypeName())) {
                en = new ImportExcelErrorNoteVO();
                en.setOrder(order);
                en.setErrorNotes(List.of("第" + order + "行业务类型名称不能为空"));
                err.add(en);
            } else {
                if (!cosDictMap.containsKey(e.getCostBusTypeName())) {
                    en = new ImportExcelErrorNoteVO();
                    en.setOrder(order);
                    en.setErrorNotes(List.of("第" + order + "行业务类型名称错误未匹配到字典"));
                    err.add(en);
                } else {
                    e.setCostBusType(cosDictMap.get(e.getCostBusTypeName()).getNumber());
                }
            }

            if (!StringUtils.isEmpty(e.getCusPersonName())) {
                customerQuery.add(e.getCusPersonName());
            } else {
                en = new ImportExcelErrorNoteVO();
                en.setOrder(order);
                en.setErrorNotes(List.of("第" + order + "行客户编号不能为空"));
                err.add(en);
            }
            if (!StringUtils.isEmpty(e.getTechRspUserNo())) {
                techRspQuery.add(e.getTechRspUserNo());
            } else {
                en = new ImportExcelErrorNoteVO();
                en.setOrder(order);
                en.setErrorNotes(List.of("第" + order + "行技术负责人工号不能为空"));
                err.add(en);
            }

            if (!StringUtils.isEmpty(e.getBusRspUserNo())) {
                busRspQuery.add(e.getBusRspUserNo());
            } else {
                en = new ImportExcelErrorNoteVO();
                en.setOrder(order);
                en.setErrorNotes(List.of("第" + order + "行商务负责人工号不能为空"));
                err.add(en);
            }

            if (!StringUtils.isEmpty(e.getUndertDeptName())) {
                deptQuery.add(e.getUndertDeptName());
            } else {
                en = new ImportExcelErrorNoteVO();
                en.setOrder(order);
                en.setErrorNotes(List.of("第" + order + "行承担部门名称不能为空"));
                err.add(en);
            }

            if (StringUtils.isNotEmpty(e.getParentName())) {
                parentQuery.add(e.getParentName());
            }

        }

        if (!CollectionUtils.isEmpty(err)) {
            result.setCode(400);
            result.setOom("数据校验失败");
            result.setErr(err);
            return result;
        }

        // 按照名称去数据库里查找它们id的时候，如果查出来的数据个数和 name distinct数量不相等，说明有数据没有匹配到，直接抛错出去
        if (!CollectionUtils.isEmpty(customerQuery)) {
            List<CustomerInfo> customerInfos = customerInfoService.list(new LambdaQueryWrapper<>(CustomerInfo.class).in(CustomerInfo::getCusName, customerQuery.stream().distinct().collect(Collectors.toList())));
            Map<String, CustomerInfo> customerInfoMap = customerInfos.stream().collect(Collectors.toMap(CustomerInfo::getCusName, Function.identity()));
            for (int i = 0; i < dtoS.size(); i++) {
                ContractMilestoneDTO e = dtoS.get(i);
                String order = String.valueOf(i + 2);
                if (customerInfoMap.containsKey(e.getCusPersonName())) {
                    e.setCusPersonId(customerInfoMap.get(e.getCusPersonName()).getId());
                } else {
                    en = new ImportExcelErrorNoteVO();
                    en.setOrder(order);
                    en.setErrorNotes(List.of("第" + order + "行客户编号不存在"));
                    err.add(en);
                }
            }

            if (!CollectionUtils.isEmpty(err)) {
                result.setCode(400);
                result.setOom("数据校验失败");
                result.setErr(err);
                return result;
            }
        }

        if (!CollectionUtils.isEmpty(deptQuery)) {
            List<DeptDO> depts = deptDOMapper.selectList(new LambdaQueryWrapper<>(DeptDO.class).in(DeptDO::getName, deptQuery.stream().distinct().collect(Collectors.toList())));
            Map<String, DeptDO> deptDOMap = depts.stream().collect(Collectors.toMap(DeptDO::getName, Function.identity()));
            for (int i = 0; i < dtoS.size(); i++) {
                ContractMilestoneDTO e = dtoS.get(i);
                String order = String.valueOf(i + 2);
                if (deptDOMap.containsKey(e.getUndertDeptName())) {
                    e.setUndertDept(deptDOMap.get(e.getUndertDeptName()).getId());
                } else {
                    en = new ImportExcelErrorNoteVO();
                    en.setOrder(order);
                    en.setErrorNotes(List.of("第" + order + "行部门不存在"));
                    err.add(en);
                }
            }

            if (!CollectionUtils.isEmpty(err)) {
                result.setCode(400);
                result.setOom("数据校验失败");
                result.setErr(err);
                return result;
            }
        }

        if (!CollectionUtils.isEmpty(techRspQuery)) {
            List<SimpleUser> users = userRedisHelper.getSimpleUserByCode(techRspQuery.stream().distinct().collect(Collectors.toList()));
            Map<String, SimpleUser> userMap = users.stream().collect(Collectors.toMap(SimpleUser::getCode, Function.identity()));
            for (int i = 0; i < dtoS.size(); i++) {
                ContractMilestoneDTO e = dtoS.get(i);
                if (userMap.containsKey(e.getTechRspUserNo())) {
                    e.setTechRspUser(userMap.get(e.getTechRspUserNo()).getId());
                } else {
                    en = new ImportExcelErrorNoteVO();
                    en.setOrder(String.valueOf(i + 2));
                    en.setErrorNotes(List.of("第" + (i + 2) + "行技术负责人工号不存在"));
                    err.add(en);
                }
            }

            if (!CollectionUtils.isEmpty(err)) {
                result.setCode(400);
                result.setOom("数据校验失败");
                result.setErr(err);
                return result;
            }
        }

        if (!CollectionUtils.isEmpty(busRspQuery)) {
            List<SimpleUser> users = userRedisHelper.getSimpleUserByCode(busRspQuery.stream().distinct().collect(Collectors.toList()));
            Map<String, SimpleUser> userMap = users.stream().collect(Collectors.toMap(SimpleUser::getCode, Function.identity()));
            for (int i = 0; i < dtoS.size(); i++) {
                ContractMilestoneDTO e = dtoS.get(i);
                if (userMap.containsKey(e.getBusRspUserNo())) {
                    e.setBusRspUser(userMap.get(e.getBusRspUserNo()).getId());
                } else {
                    en = new ImportExcelErrorNoteVO();
                    en.setOrder(String.valueOf(i + 2));
                    en.setErrorNotes(List.of("第" + (i + 2) + "行商务负责人工号不存在"));
                    err.add(en);
                }
            }

            if (!CollectionUtils.isEmpty(err)) {
                result.setCode(400);
                result.setOom("数据校验失败");
                result.setErr(err);
                return result;
            }
        }

        if (!CollectionUtils.isEmpty(parentQuery)) {
            List<ContractMilestone> list = this.list(new LambdaQueryWrapper<ContractMilestone>()
                    .eq(ContractMilestone::getContractId, contractId)
                    .in(ContractMilestone::getMilestoneName, parentQuery.stream().distinct().collect(Collectors.toList())));
            Map<String, ContractMilestone> map = list.stream()
                    .collect(Collectors.toMap(ContractMilestone::getMilestoneName, Function.identity()));
            for (ContractMilestoneDTO e : dtoS) {
                if (map.containsKey(e.getParentName())) {
                    e.setParentId(map.get(e.getParentName()).getId());
                }
            }
        }

        log.info("合同里程碑导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ContractMilestone> contractMilestonees = BeanCopyUtils.convertListTo(dtoS, ContractMilestone::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ContractMilestone-import::id", importId, contractMilestonees, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }

    /**
     * 导入excel
     *
     * @param importId 导入校验后，生成的流水号
     */
    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ContractMilestone> list = (List<ContractMilestone>) orionJ2CacheService.get("pmsx::ContractMilestone-import::id", importId);
        log.info("合同里程碑导入的入库数据={}", JSONUtil.toJsonStr(list));
        this.saveBatch(list);
        orionJ2CacheService.delete("pmsx::ContractMilestone-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ContractMilestone-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ContractMilestone> condition = new LambdaQueryWrapperX<>(ContractMilestone.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ContractMilestone::getCreateTime);
        List<ContractMilestone> contractMilestonees = this.list(condition);

        List<ContractMilestoneDTO> dtos = BeanCopyUtils.convertListTo(contractMilestonees, ContractMilestoneDTO::new);

        String fileName = "合同里程碑数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractMilestoneDTO.class, dtos);

    }


    public void setTreeNameDetail(List<ContractMilestoneVO> vos, Map<String, MarketContract> contractMap, Map<String, ProjectInitiationSimple> projectMap) throws Exception {
        if (CollectionUtils.isEmpty(vos)) {
            return;
        }
        List<String> cusPersonIds = vos.stream().filter(item -> StringUtils.isNotBlank(item.getCusPersonId())).map(ContractMilestoneVO::getCusPersonId).distinct().collect(Collectors.toList());
        List<String> deptIds = vos.stream().filter(item -> StringUtils.isNotBlank(item.getOfficeDept())).map(ContractMilestoneVO::getOfficeDept).distinct().collect(Collectors.toList());
        List<DictValueVO> cosDict = dictRedisHelper.getDictListByCode(DictConts.COS_BUSINESS_TYPE);//业务类型
        Set<String> allDeptIds = new HashSet<>(deptIds);
        Map<String, String> collect = new HashMap<>();
        if (!cusPersonIds.isEmpty()) {
            LambdaQueryWrapperX<CustomerInfo> wrapperX = new LambdaQueryWrapperX<>(CustomerInfo.class);
            wrapperX.select(CustomerInfo::getId, CustomerInfo::getCusName);
            wrapperX.in(CustomerInfo::getId, cusPersonIds);
            List<CustomerInfo> customerInfos = customerInfoService.getBaseMapper().selectList(wrapperX);
            collect = customerInfos.stream().collect(Collectors.toMap(CustomerInfo::getId, CustomerInfo::getCusName));
        } else {
            collect = new HashMap<>();
        }
        List<DictValueVO> dictList = dictRedisHelper.getDictList("dict1796866395473141760");//市场合同类型
        List<DictValueVO> rateList = dictRedisHelper.getDictList("dict1845726393622073344");//税率
        List<DictValueVO> moneyTypeList = dictRedisHelper.getDictList("dict1862310899522666496");//金融类型
        List<DictValueVO> dateTypeList = dictRedisHelper.getDictList("dict1862311952813064192");//日期类型
        List<DictValueVO> milestoneTypeList = dictRedisHelper.getDictList("dict1846379341352013824");//收入类型
        List<DictValueVO> businessIncomeTypeList = dictRedisHelper.getDictList("dict1804393678006050816");//业务收入类型
        Map<String, DictValueVO> rateMap = rateList.stream().collect(Collectors.toMap(DictValueVO::getValue, Function.identity()));
        List<String> milestioneIds = vos.stream().map(ContractMilestoneVO::getId).collect(Collectors.toList());
        Map<String, String> contractTypeMap = dictList.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription));
        Map<String, String> moneyTypeMap = moneyTypeList.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription));
        Map<String, String> dateTypeMap = dateTypeList.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription));
        Map<String, String> milestoneTypeMap = milestoneTypeList.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription));
        Map<String, String> cosBusTypeMap = cosDict.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));
        Map<String, String> businessIncomeTypeMap = businessIncomeTypeList.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription));
        LambdaQueryWrapperX<ContractMilestoneSimple> contractMilestoneLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        contractMilestoneLambdaQueryWrapperX.in(ContractMilestoneSimple::getParentId, milestioneIds);
        contractMilestoneLambdaQueryWrapperX.select(ContractMilestoneSimple::getId, ContractMilestoneSimple::getParentId, ContractMilestoneSimple::getTaxRate);
        List<ContractMilestoneSimple> parentContractMilestones = contractMilestoneSimpleMapper.selectList(contractMilestoneLambdaQueryWrapperX);
        Map<String, List<ContractMilestoneSimple>> parentMilestoneMap = parentContractMilestones.stream().collect(Collectors.groupingBy(ContractMilestoneSimple::getParentId));
        List<DataStatusVO> dataStatusVOS = dataStatusHelper.getPolicyStatusInfo(ContractMilestone.class);
        Map<Integer, DataStatusVO> dataStatusVOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(dataStatusVOS)) {
            dataStatusVOMap = dataStatusVOS.stream().collect(Collectors.toMap(DataStatusVO::getStatusValue, Function.identity()));
        }
        Set<String> allUserIds = new HashSet<>();
        allUserIds.addAll(vos.stream().filter(item -> StringUtils.isNotBlank(item.getCreatorId())).map(ContractMilestoneVO::getCreatorId).collect(Collectors.toList()));
        allUserIds.addAll(vos.stream().filter(item -> StringUtils.isNotBlank(item.getTechRspUser())).map(ContractMilestoneVO::getTechRspUser).collect(Collectors.toList()));
        allUserIds.addAll(vos.stream().filter(item -> StringUtils.isNotBlank(item.getBusRspUser())).map(ContractMilestoneVO::getBusRspUser).collect(Collectors.toList()));
        Map<String, String> userMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(allUserIds)) {
            List<UserDO> userDOS = userDOMapper.selectBatchIds(allUserIds);
            userMap = userDOS.stream().collect(Collectors.toMap(UserDO::getId, UserDO::getName));
        }


        allDeptIds.addAll(vos.stream().filter(item -> StringUtils.isNotBlank(item.getUndertDept())).map(ContractMilestoneVO::getUndertDept).collect(Collectors.toList()));
        Map<String, String> deptMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(allDeptIds)) {
            List<DeptDO> deptDOS = deptDOMapper.selectBatchIds(allDeptIds);
            deptMap = deptDOS.stream().collect(Collectors.toMap(DeptDO::getId, DeptDO::getName));
        }

        List<String> uniqueIds = vos.stream()
                .map(ContractMilestoneVO::getId)
                .distinct()
                .collect(Collectors.toList());

        //已确认开票收入
        LambdaQueryWrapper<InvoicingRevenueAccounting> invoicingRevenueAccountingLambdaQueryWrapper = new LambdaQueryWrapper<>();
        invoicingRevenueAccountingLambdaQueryWrapper.select(InvoicingRevenueAccounting::getMilestoneId, InvoicingRevenueAccounting::getAmtNoTax);
        if (ObjectUtil.isNotEmpty(uniqueIds)) {
            invoicingRevenueAccountingLambdaQueryWrapper.in(InvoicingRevenueAccounting::getMilestoneId, uniqueIds);
        }
        List<InvoicingRevenueAccounting> invoicingRevenueAccountingList = invoicingRevenueAccountingService.list(invoicingRevenueAccountingLambdaQueryWrapper);
        invoicingRevenueAccountingList = invoicingRevenueAccountingList.stream()
                .filter(invoicingRevenueAccounting -> Objects.nonNull(invoicingRevenueAccounting.getMilestoneId()))
                .collect(Collectors.toList());
        Map<String, List<InvoicingRevenueAccounting>> groupedInvoicingRevenueAccounting = new HashMap<>();
        if (ObjectUtil.isNotEmpty(invoicingRevenueAccountingList)) {
            groupedInvoicingRevenueAccounting = invoicingRevenueAccountingList.stream()
                    .collect(Collectors.groupingBy(InvoicingRevenueAccounting::getMilestoneId));
        }

        BigDecimal confirmIncomeInvoicing = BigDecimal.ZERO;

        //已确认暂估收入
        LambdaQueryWrapper<ProvisionalIncomeAccounting> provisionalIncomeAccountingLambdaQueryWrapper = new LambdaQueryWrapper<>();
        provisionalIncomeAccountingLambdaQueryWrapper.select(ProvisionalIncomeAccounting::getMilestoneId, ProvisionalIncomeAccounting::getAmtNoTax);
        if (ObjectUtil.isNotEmpty(uniqueIds)) {
            provisionalIncomeAccountingLambdaQueryWrapper.in(ProvisionalIncomeAccounting::getMilestoneId, uniqueIds);
        }
        List<ProvisionalIncomeAccounting> provisionalIncomeAccountingList = provisionalIncomeAccountingService.list(provisionalIncomeAccountingLambdaQueryWrapper);
        provisionalIncomeAccountingList = provisionalIncomeAccountingList.stream()
                .filter(provisionalIncomeAccounting -> Objects.nonNull(provisionalIncomeAccounting.getMilestoneId()))
                .collect(Collectors.toList());
        Map<String, List<ProvisionalIncomeAccounting>> provisionalIncomeAccountingMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(provisionalIncomeAccountingList)) {
            provisionalIncomeAccountingMap = provisionalIncomeAccountingList.stream()
                    .collect(Collectors.groupingBy(ProvisionalIncomeAccounting::getMilestoneId));
        }
        BigDecimal confirmIncomeProvisionalEstimate = BigDecimal.ZERO;


        //已预收款开票金额
        LambdaQueryWrapper<AdvancePaymentInvoiced> advancePaymentInvoicedLambdaQueryWrapper = new LambdaQueryWrapper<>();
        advancePaymentInvoicedLambdaQueryWrapper.select(AdvancePaymentInvoiced::getMilestoneId, AdvancePaymentInvoiced::getAmtNoTax);
        if (ObjectUtil.isNotEmpty(uniqueIds)) {
            advancePaymentInvoicedLambdaQueryWrapper.in(AdvancePaymentInvoiced::getMilestoneId, uniqueIds);
        }
        List<AdvancePaymentInvoiced> advancePaymentInvoicedList = advancePaymentInvoicedService.list(advancePaymentInvoicedLambdaQueryWrapper);
        advancePaymentInvoicedList = advancePaymentInvoicedList.stream()
                .filter(advancePaymentInvoiced -> Objects.nonNull(advancePaymentInvoiced.getMilestoneId()))
                .collect(Collectors.toList());
        Map<String, List<AdvancePaymentInvoiced>> groupedAdvancePaymentInvoiced = new HashMap<>();
        if (ObjectUtil.isNotEmpty(advancePaymentInvoicedList)) {
            groupedAdvancePaymentInvoiced = advancePaymentInvoicedList.stream()
                    .collect(Collectors.groupingBy(AdvancePaymentInvoiced::getMilestoneId));
        }
        BigDecimal milestoneAdvanceAmt = BigDecimal.ZERO;


        for (ContractMilestoneVO vo : vos) {
            setCheckDate(vo);
            setAmt(vo);

            //已确认开票收入
            if (ObjectUtil.isNotEmpty(groupedInvoicingRevenueAccounting)) {
                List<InvoicingRevenueAccounting> invoicingRevenueAccountings = groupedInvoicingRevenueAccounting.get(vo.getId());
                if (ObjectUtil.isNotEmpty(invoicingRevenueAccountings)) {
                    for (InvoicingRevenueAccounting invoicingRevenueAccounting : invoicingRevenueAccountings) {
                        BigDecimal amtNoTax = invoicingRevenueAccounting.getAmtNoTax();
                        if (ObjectUtil.isNotEmpty(amtNoTax)) {
                            confirmIncomeInvoicing = confirmIncomeInvoicing.add(amtNoTax);
                        }
                    }
                }
            }
            vo.setConfirmIncomeInvoicing(confirmIncomeInvoicing);

            //已确认暂估收入
            if (ObjectUtil.isNotEmpty(provisionalIncomeAccountingMap)) {
                List<ProvisionalIncomeAccounting> provisionalIncomeAccountings = provisionalIncomeAccountingMap.get(vo.getId());
                if (ObjectUtil.isNotEmpty(provisionalIncomeAccountings)) {
                    for (ProvisionalIncomeAccounting provisionalIncomeAccounting : provisionalIncomeAccountings) {
                        BigDecimal amtNoTax = provisionalIncomeAccounting.getAmtNoTax();
                        if (ObjectUtil.isNotEmpty(amtNoTax)) {
                            confirmIncomeProvisionalEstimate = confirmIncomeProvisionalEstimate.add(amtNoTax);
                        }
                    }
                }
            }
            vo.setConfirmIncomeProvisionalEstimate(confirmIncomeProvisionalEstimate);

            //已预收款开票金额
            if (ObjectUtil.isNotEmpty(groupedAdvancePaymentInvoiced)) {
                List<AdvancePaymentInvoiced> advancePaymentInvoiceds = groupedAdvancePaymentInvoiced.get(vo.getId());
                if (ObjectUtil.isNotEmpty(advancePaymentInvoiceds)) {
                    for (AdvancePaymentInvoiced advancePaymentInvoiced : advancePaymentInvoiceds) {
                        BigDecimal amtNoTax = advancePaymentInvoiced.getAmtNoTax();
                        if (ObjectUtil.isNotEmpty(amtNoTax)) {
                            milestoneAdvanceAmt = milestoneAdvanceAmt.add(amtNoTax);
                        }
                    }
                }
            }
            vo.setMilestoneAdvanceAmt(milestoneAdvanceAmt);
            vo.setConfirmIncomeSum(confirmIncomeInvoicing.add(confirmIncomeProvisionalEstimate).add(milestoneAdvanceAmt));


            String creatorId = vo.getCreatorId();
            if (StringUtils.isNotBlank(creatorId)) {
                vo.setCreatorName(userMap.get(creatorId));
            }

            String techRspUser = vo.getTechRspUser();
            if (StringUtils.isNotBlank(techRspUser)) {
                vo.setTechRspUserName(userMap.get(techRspUser));
            }

            String busRspUser = vo.getBusRspUser();
            if (StringUtils.isNotBlank(busRspUser)) {
                vo.setBusRspUserName(userMap.get(busRspUser));
            }

            String undertDept = vo.getUndertDept();
            if (StringUtils.isNotBlank(undertDept)) {
                vo.setUndertDeptName(deptMap.get(undertDept));
            }

            String parentId = vo.getParentId();
            //判断当前的里程碑如果有两个或以上的子里程碑 则给综合税率rate赋值 这样前端展示 综合
            if (ObjectUtil.isNotEmpty(parentId)) {
                vo.setIsOneMileStone("2");
            } else {
                vo.setIsOneMileStone("1");
                List<ContractMilestoneSimple> contractMilestones = parentMilestoneMap.get(vo.getId());
                if (ObjectUtil.isNotEmpty(contractMilestones)) {
                    if (contractMilestones.size() > 1) {
                        //说明存在两个或以上的子里程碑
                        vo.setRate("综合");
                    }
                }

            }
            Integer status = vo.getStatus();
            if (status != null) {
                vo.setDataStatus(dataStatusVOMap.get(status));
            }
            String contractId = vo.getContractId();
            MarketContract marketContract = contractMap.get(contractId);
            if (ObjectUtil.isNotEmpty(marketContract) && ObjectUtil.isNotEmpty(marketContract.getNumber())) {
                vo.setContractNumber(marketContract.getNumber());
            }
            //合同类型
            if (ObjectUtil.isNotEmpty(marketContract)) {
                String contractType = marketContract.getContractType();
                if (StringUtils.isNotBlank(contractType)) {
                    String contractTypeName = contractTypeMap.get(contractType);
                    if (StringUtils.isNotBlank(contractTypeName)) {
                        vo.setContractTypeName(contractTypeName);
                    }

                }
            }

            BigDecimal taxRate = vo.getTaxRate();
            if (ObjectUtil.isNotEmpty(taxRate)) {
                BigDecimal stripTrailingZeros = taxRate.stripTrailingZeros();
                DictValueVO dictValueVO = rateMap.get(stripTrailingZeros.toString());
                if (dictValueVO != null) {
                    vo.setRateId(dictValueVO.getValue());
                    vo.setRateName(dictValueVO.getValue() + "%");
                }
            }

            //判断当前的里程碑如果有两个或以上的子里程碑 则给综合税率rate赋值 这样前端展示 综合
            if (ObjectUtil.isNotEmpty(parentId)) {
                vo.setIsOneMileStone("2");
                vo.setRate(vo.getRateName());
            } else {
                vo.setIsOneMileStone("1");
                List<ContractMilestoneSimple> contractMilestones = parentMilestoneMap.get(vo.getId());
                if (ObjectUtil.isNotEmpty(contractMilestones)) {
                    if (contractMilestones.size() > 1) {
                        HashSet<BigDecimal> uniqueTaxRate = new HashSet<>();
                        for (ContractMilestoneSimple contractMilestone : contractMilestones) {
                            if (ObjectUtil.isNotEmpty(contractMilestone.getTaxRate())) {
                                uniqueTaxRate.add(contractMilestone.getTaxRate());
                            }
                        }
                        if (uniqueTaxRate.size() > 1) {
                            //说明存在两个或以上 且值不相同的子里程碑
                            vo.setRate("综合");
                        } else {
                            if (ObjectUtil.isNotEmpty(uniqueTaxRate)) {
                                List<BigDecimal> uniqueTaxRates = new ArrayList<>(uniqueTaxRate);
                                if (ObjectUtil.isNotEmpty(uniqueTaxRates)) {
                                    BigDecimal bigDecimal = uniqueTaxRates.get(0);
                                    BigDecimal stripTrailingZeros = bigDecimal.stripTrailingZeros();
                                    vo.setRate(stripTrailingZeros + "%");
                                }
                            }

                        }
                    } else if (contractMilestones.size() == 1) {
                        ContractMilestoneSimple contractMilestone = contractMilestones.get(0);
                        if (ObjectUtil.isNotEmpty(contractMilestone.getTaxRate())) {
                            vo.setRate(contractMilestone.getTaxRate().stripTrailingZeros().toString() + "%");
                        }
                    } else {
                        vo.setRate(vo.getRateName());
                    }
                } else {
                    vo.setRate(vo.getRateName());
                }

            }

            //金融类型
            String ammountType = vo.getAmmountType();
            if (StringUtils.isNotBlank(ammountType)) {
                String ammountTypeName = moneyTypeMap.get(ammountType);
                if (StringUtils.isNotBlank(ammountTypeName)) {
                    vo.setMoneyTypeName(ammountTypeName);
                    vo.setMoneyTypeId(ammountType);
                }

            }


            //日期类型
            String dateType = vo.getDateType();
            if (StringUtils.isNotBlank(dateType)) {
                String dateTypeName = dateTypeMap.get(dateType);
                if (StringUtils.isNotBlank(dateTypeName)) {
                    vo.setDateTypeName(dateTypeName);
                    vo.setDateTypeId(dateType);
                }

            }

            //里程碑收入类型
            String mileIncomeType = vo.getMileIncomeType();
            if (StringUtils.isNotBlank(mileIncomeType)) {
                String mileIncomeTypeName = milestoneTypeMap.get(mileIncomeType);
                if (StringUtils.isNotBlank(mileIncomeTypeName)) {
                    vo.setIncomeTypeName(mileIncomeTypeName);
                    vo.setIncomeTypeId(mileIncomeType);
                }
            }

            //业务类型
            String costBusType = vo.getCostBusType();
            if (StringUtils.isNotBlank(costBusType)) {
                String costBusTypeName = cosBusTypeMap.get(costBusType);
                if (StringUtils.isNotBlank(costBusTypeName)) {
                    vo.setCostBusTypeName(costBusTypeName);
                    vo.setCostBusType(costBusType);
                }
            }

            //业务收入类型
            String businessIncomeType = vo.getBusinessIncomeType();
            if (StringUtils.isNotBlank(businessIncomeType)) {
                String businessIncomeTypeName = businessIncomeTypeMap.get(businessIncomeType);
                if (StringUtils.isNotBlank(businessIncomeTypeName)) {
                    vo.setBusinessIncomeTypeName(businessIncomeTypeName);
                    vo.setBusinessIncomeTypeId(businessIncomeType);
                }
            }


            String projectCode = vo.getProjectCode();
            if (ObjectUtil.isNotEmpty(projectCode)) {
                ProjectInitiationSimple projectInitiation = projectMap.get(projectCode);
                if (projectInitiation != null) {
                    String id = projectInitiation.getId();
                    if (ObjectUtil.isNotEmpty(id)) {
                        vo.setProjectId(id);
                    }
                }


            }
            vo.setCusPersonName(StringUtils.isEmpty(collect.get(vo.getCusPersonId())) ? null : collect.get(vo.getCusPersonId()));
            if (contractMap.containsKey(vo.getContractId())) {
                vo.setContractNumber(contractMap.get(vo.getContractId()).getNumber());
                vo.setContractName(contractMap.get(vo.getContractId()).getName());
                vo.setContractStatus(contractMap.get(vo.getContractId()).getStatus());
            }
            vo.setOfficeDeptName(deptMap.containsKey(vo.getOfficeDept()) ? deptMap.get(vo.getOfficeDept()) : "");
        }
    }


    public void setTreeNameDetail1(List<ContractMilestoneVO> vos, Map<String, MarketContract> contractMap, Map<String, ProjectInitiationSimple> projectMap) throws Exception {
        long startTime = System.currentTimeMillis();
        log.info("【合同详情-内层-里程碑-setTreeNameDetail1】 ---------开始");
        if (CollectionUtils.isEmpty(vos)) {
            return;
        }
        List<String> cusPersonIds = new ArrayList<>();
        List<String> deptIds = new ArrayList<>();
        List<ContractMilestoneVO> childContractMilestones = new ArrayList<>();
        for (ContractMilestoneVO vo : vos) {
            if (ObjectUtil.isNotEmpty(vo.getParentId())) {
                childContractMilestones.add(vo);
            }
            if (StringUtils.isNotBlank(vo.getCusPersonId())) {
                cusPersonIds.add(vo.getCusPersonId());
            }
            if (StringUtils.isNotBlank(vo.getOfficeDept())) {
                deptIds.add(vo.getOfficeDept());
            }
        }
        List<DictValueVO> cosDict = dictRedisHelper.getDictListByCode(DictConts.COS_BUSINESS_TYPE);//业务类型
        Set<String> allDeptIds = new HashSet<>(deptIds);
        log.info("【合同详情-内层-里程碑-setTreeNameDetail1-业务类型】耗时：{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        Map<String, String> collect = new HashMap<>();
        if (!cusPersonIds.isEmpty()) {
            LambdaQueryWrapperX<CustomerInfo> wrapperX = new LambdaQueryWrapperX<>(CustomerInfo.class);
            wrapperX.select(CustomerInfo::getId, CustomerInfo::getCusName);
            wrapperX.in(CustomerInfo::getId, cusPersonIds);
            List<CustomerInfo> customerInfos = customerInfoService.getBaseMapper().selectList(wrapperX);
            collect = customerInfos.stream().collect(Collectors.toMap(CustomerInfo::getId, CustomerInfo::getCusName));
            log.info("【合同详情-内层-里程碑-setTreeNameDetail1-客户管理】耗时：{} ", System.currentTimeMillis() - startTime);
            startTime = System.currentTimeMillis();
        } else {
            collect = new HashMap<>();
        }
        List<DictValueVO> dictList = dictRedisHelper.getDictList("dict1796866395473141760");//市场合同类型
        List<DictValueVO> rateList = dictRedisHelper.getDictList("dict1845726393622073344");//税率
        List<DictValueVO> moneyTypeList = dictRedisHelper.getDictList("dict1862310899522666496");//金融类型
        List<DictValueVO> dateTypeList = dictRedisHelper.getDictList("dict1862311952813064192");//日期类型
        List<DictValueVO> milestoneTypeList = dictRedisHelper.getDictList("dict1846379341352013824");//收入类型
        List<DictValueVO> businessIncomeTypeList = dictRedisHelper.getDictList("dict1804393678006050816");//业务收入类型
        Map<String, DictValueVO> rateMap = rateList.stream().collect(Collectors.toMap(DictValueVO::getValue, Function.identity()));
        Map<String, String> contractTypeMap = dictList.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription));
        Map<String, String> moneyTypeMap = moneyTypeList.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription));
        Map<String, String> dateTypeMap = dateTypeList.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription));
        Map<String, String> milestoneTypeMap = milestoneTypeList.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription));
        Map<String, String> cosBusTypeMap = cosDict.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));
        Map<String, String> businessIncomeTypeMap = businessIncomeTypeList.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription));
        log.info("【合同详情-内层-里程碑-setTreeNameDetail1-字典-缓存】耗时：{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        Map<String, List<ContractMilestoneVO>> parentMilestoneMap = childContractMilestones.stream().collect(Collectors.groupingBy(ContractMilestoneVO::getParentId));
        List<DataStatusVO> dataStatusVOS = dataStatusHelper.getPolicyStatusInfo(ContractMilestone.class);
        Map<Integer, DataStatusVO> dataStatusVOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(dataStatusVOS)) {
            dataStatusVOMap = dataStatusVOS.stream().collect(Collectors.toMap(DataStatusVO::getStatusValue, Function.identity()));
        }
        log.info("【合同详情-内层-里程碑-setTreeNameDetail1-状态-缓存】耗时：{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        Set<String> allUserIds = new HashSet<>();
        allUserIds.addAll(vos.stream().filter(item -> StringUtils.isNotBlank(item.getCreatorId())).map(ContractMilestoneVO::getCreatorId).collect(Collectors.toList()));
        allUserIds.addAll(vos.stream().filter(item -> StringUtils.isNotBlank(item.getTechRspUser())).map(ContractMilestoneVO::getTechRspUser).collect(Collectors.toList()));
        allUserIds.addAll(vos.stream().filter(item -> StringUtils.isNotBlank(item.getBusRspUser())).map(ContractMilestoneVO::getBusRspUser).collect(Collectors.toList()));
        Map<String, String> userMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(allUserIds)) {
            LambdaQueryWrapperX<UserDO> wrapperX = new LambdaQueryWrapperX<>(UserDO.class);
            wrapperX.select(UserDO::getId, UserDO::getName);
            wrapperX.in(UserDO::getId, allUserIds.stream().distinct().collect(Collectors.toList()));
            List<UserDO> userDOS = userDOMapper.selectList(wrapperX);
            userMap = userDOS.stream().collect(Collectors.toMap(UserDO::getId, UserDO::getName));
            log.info("【合同详情-内层-里程碑-setTreeNameDetail1-用户】耗时：{} ", System.currentTimeMillis() - startTime);
            startTime = System.currentTimeMillis();
        }
        allDeptIds.addAll(vos.stream().filter(item -> StringUtils.isNotBlank(item.getUndertDept())).map(ContractMilestoneVO::getUndertDept).collect(Collectors.toList()));
        Map<String, String> deptMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(allDeptIds)) {
            LambdaQueryWrapperX<DeptDO> wrapperX = new LambdaQueryWrapperX<>(DeptDO.class);
            wrapperX.select(DeptDO::getId, DeptDO::getName);
            wrapperX.in(DeptDO::getId, allDeptIds.stream().distinct().collect(Collectors.toList()));
            List<DeptDO> deptDOS = deptDOMapper.selectBatchIds(allDeptIds);
            deptMap = deptDOS.stream().collect(Collectors.toMap(DeptDO::getId, DeptDO::getName));
            log.info("【合同详情-内层-里程碑-setTreeNameDetail1-部门】耗时：{} ", System.currentTimeMillis() - startTime);
            startTime = System.currentTimeMillis();
        }
        for (ContractMilestoneVO vo : vos) {
            setCheckDate(vo);
            setAmt(vo);

            String creatorId = vo.getCreatorId();
            if (StringUtils.isNotBlank(creatorId)) {
                vo.setCreatorName(userMap.get(creatorId));
            }

            String techRspUser = vo.getTechRspUser();
            if (StringUtils.isNotBlank(techRspUser)) {
                vo.setTechRspUserName(userMap.get(techRspUser));
            }

            String busRspUser = vo.getBusRspUser();
            if (StringUtils.isNotBlank(busRspUser)) {
                vo.setBusRspUserName(userMap.get(busRspUser));
            }

            String undertDept = vo.getUndertDept();
            if (StringUtils.isNotBlank(undertDept)) {
                vo.setUndertDeptName(deptMap.get(undertDept));
            }

            String parentId = vo.getParentId();
            //判断当前的里程碑如果有两个或以上的子里程碑 则给综合税率rate赋值 这样前端展示 综合
            if (ObjectUtil.isNotEmpty(parentId)) {
                vo.setIsOneMileStone("2");
            } else {
                vo.setIsOneMileStone("1");
                List<ContractMilestoneVO> contractMilestones = parentMilestoneMap.get(vo.getId());
                if (ObjectUtil.isNotEmpty(contractMilestones)) {
                    if (contractMilestones.size() > 1) {
                        //说明存在两个或以上的子里程碑
                        vo.setRate("综合");
                    }
                }

            }
            Integer status = vo.getStatus();
            if (status != null) {
                vo.setDataStatus(dataStatusVOMap.get(status));
            }
            String contractId = vo.getContractId();
            MarketContract marketContract = contractMap.get(contractId);
            if (ObjectUtil.isNotEmpty(marketContract) && ObjectUtil.isNotEmpty(marketContract.getNumber())) {
                vo.setContractNumber(marketContract.getNumber());
            }
            //合同类型
            if (ObjectUtil.isNotEmpty(marketContract)) {
                String contractType = marketContract.getContractType();
                if (StringUtils.isNotBlank(contractType)) {
                    String contractTypeName = contractTypeMap.get(contractType);
                    if (StringUtils.isNotBlank(contractTypeName)) {
                        vo.setContractTypeName(contractTypeName);
                    }

                }
            }

            BigDecimal taxRate = vo.getTaxRate();
            if (ObjectUtil.isNotEmpty(taxRate)) {
                BigDecimal stripTrailingZeros = taxRate.stripTrailingZeros();
                DictValueVO dictValueVO = rateMap.get(stripTrailingZeros.toString());
                if (dictValueVO != null) {
                    vo.setRateId(dictValueVO.getValue());
                    vo.setRateName(dictValueVO.getValue() + "%");
                }
            }

            //判断当前的里程碑如果有两个或以上的子里程碑 则给综合税率rate赋值 这样前端展示 综合
            if (ObjectUtil.isNotEmpty(parentId)) {
                vo.setIsOneMileStone("2");
                vo.setRate(vo.getRateName());
            } else {
                vo.setIsOneMileStone("1");
                List<ContractMilestoneVO> contractMilestones = parentMilestoneMap.get(vo.getId());
                if (ObjectUtil.isNotEmpty(contractMilestones)) {
                    if (contractMilestones.size() > 1) {
                        HashSet<BigDecimal> uniqueTaxRate = new HashSet<>();
                        for (ContractMilestoneVO contractMilestone : contractMilestones) {
                            if (ObjectUtil.isNotEmpty(contractMilestone.getTaxRate())) {
                                uniqueTaxRate.add(contractMilestone.getTaxRate());
                            }
                        }
                        if (uniqueTaxRate.size() > 1) {
                            //说明存在两个或以上 且值不相同的子里程碑
                            vo.setRate("综合");
                        } else {
                            if (ObjectUtil.isNotEmpty(uniqueTaxRate)) {
                                List<BigDecimal> uniqueTaxRates = new ArrayList<>(uniqueTaxRate);
                                if (ObjectUtil.isNotEmpty(uniqueTaxRates)) {
                                    BigDecimal bigDecimal = uniqueTaxRates.get(0);
                                    BigDecimal stripTrailingZeros = bigDecimal.stripTrailingZeros();
                                    vo.setRate(stripTrailingZeros + "%");
                                }
                            }

                        }
                    } else if (contractMilestones.size() == 1) {
                        ContractMilestoneVO contractMilestone = contractMilestones.get(0);
                        if (ObjectUtil.isNotEmpty(contractMilestone.getTaxRate())) {
                            vo.setRate(contractMilestone.getTaxRate().stripTrailingZeros().toString() + "%");
                        }
                    } else {
                        vo.setRate(vo.getRateName());
                    }
                } else {
                    vo.setRate(vo.getRateName());
                }

            }

            //金融类型
            String ammountType = vo.getAmmountType();
            if (StringUtils.isNotBlank(ammountType)) {
                String ammountTypeName = moneyTypeMap.get(ammountType);
                if (StringUtils.isNotBlank(ammountTypeName)) {
                    vo.setMoneyTypeName(ammountTypeName);
                    vo.setMoneyTypeId(ammountType);
                }

            }


            //日期类型
            String dateType = vo.getDateType();
            if (StringUtils.isNotBlank(dateType)) {
                String dateTypeName = dateTypeMap.get(dateType);
                if (StringUtils.isNotBlank(dateTypeName)) {
                    vo.setDateTypeName(dateTypeName);
                    vo.setDateTypeId(dateType);
                }

            }

            //里程碑收入类型
            String mileIncomeType = vo.getMileIncomeType();
            if (StringUtils.isNotBlank(mileIncomeType)) {
                String mileIncomeTypeName = milestoneTypeMap.get(mileIncomeType);
                if (StringUtils.isNotBlank(mileIncomeTypeName)) {
                    vo.setIncomeTypeName(mileIncomeTypeName);
                    vo.setIncomeTypeId(mileIncomeType);
                }
            }

            //业务类型
            String costBusType = vo.getCostBusType();
            if (StringUtils.isNotBlank(costBusType)) {
                String costBusTypeName = cosBusTypeMap.get(costBusType);
                if (StringUtils.isNotBlank(costBusTypeName)) {
                    vo.setCostBusTypeName(costBusTypeName);
                    vo.setCostBusType(costBusType);
                }
            }

            //业务收入类型
            String businessIncomeType = vo.getBusinessIncomeType();
            if (StringUtils.isNotBlank(businessIncomeType)) {
                String businessIncomeTypeName = businessIncomeTypeMap.get(businessIncomeType);
                if (StringUtils.isNotBlank(businessIncomeTypeName)) {
                    vo.setBusinessIncomeTypeName(businessIncomeTypeName);
                    vo.setBusinessIncomeTypeId(businessIncomeType);
                }
            }


            String projectCode = vo.getProjectCode();
            if (ObjectUtil.isNotEmpty(projectCode)) {
                ProjectInitiationSimple projectInitiation = projectMap.get(projectCode);
                if (projectInitiation != null) {
                    String id = projectInitiation.getId();
                    if (ObjectUtil.isNotEmpty(id)) {
                        vo.setProjectId(id);
                    }
                }


            }
            vo.setCusPersonName(StringUtils.isEmpty(collect.get(vo.getCusPersonId())) ? null : collect.get(vo.getCusPersonId()));
            if (contractMap.containsKey(vo.getContractId())) {
                vo.setContractNumber(contractMap.get(vo.getContractId()).getNumber());
                vo.setContractName(contractMap.get(vo.getContractId()).getName());
                vo.setContractStatus(contractMap.get(vo.getContractId()).getStatus());
            }
            vo.setOfficeDeptName(deptMap.getOrDefault(vo.getOfficeDept(), ""));
        }
        log.info("【合同详情-内层-里程碑-setTreeNameDetail1-结束】耗时：{} ", System.currentTimeMillis() - startTime);
    }

    @Override
    public void setEveryName(List<ContractMilestoneVO> vos) throws Exception {
        if (CollectionUtils.isEmpty(vos)) {
            return;
        }
        Set<String> allUserIds = new HashSet<>();
        allUserIds.addAll(vos.stream().filter(item -> StringUtils.isNotBlank(item.getCreatorId())).map(ContractMilestoneVO::getCreatorId).collect(Collectors.toList()));
        allUserIds.addAll(vos.stream().filter(item -> StringUtils.isNotBlank(item.getTechRspUser())).map(ContractMilestoneVO::getTechRspUser).collect(Collectors.toList()));
        allUserIds.addAll(vos.stream().filter(item -> StringUtils.isNotBlank(item.getBusRspUser())).map(ContractMilestoneVO::getBusRspUser).collect(Collectors.toList()));
        Map<String, String> userMap;
        if (!CollectionUtils.isEmpty(allUserIds)) {
            List<UserDO> userDOS = userDOMapper.selectBatchIds(allUserIds);
            userMap = userDOS.stream().collect(Collectors.toMap(UserDO::getId, UserDO::getName));
        } else {
            userMap = new HashMap<>();
        }
        Map<Integer, DataStatusVO> dataStatusMap = dataStatusNBO.getDataStatusMapByClassName(ContractMilestone.class.getSimpleName());


        List<String> centerBusinessCodes = new ArrayList<>();
        centerBusinessCodes.add("Business_01");//中心商务
        centerBusinessCodes.add("JY001");//中心主任
        LambdaQueryWrapperX<RoleDO> centerRoleWrapperX = new LambdaQueryWrapperX<>();
        centerRoleWrapperX.in(RoleDO::getCode, centerBusinessCodes);
        centerRoleWrapperX.select(RoleDO::getId);
        List<RoleDO> centerRoleDOS = roleDOMapper.selectList(centerRoleWrapperX);
        List<RoleVO> roleVOList = roleRedisHelper.getRoleByUserId(CurrentUserHelper.getOrgId(), CurrentUserHelper.getCurrentUserId());
        List<String> existRoleIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(roleVOList)) {
            existRoleIds = roleVOList.stream().map(RoleVO::getId).collect(Collectors.toList());
        }
        List<String> finalExistRoleIds = existRoleIds;
        SimpleUser simplerUser = userRedisHelper.getSimplerUser(CurrentUserHelper.getOrgId(), CurrentUserHelper.getCurrentUserId());
        String orgId = simplerUser.getOrgId();//当前登陆人的20级部门

        List<DictValueVO> milestoneTypeList = dictRedisHelper.getDictList("dict1846379341352013824");//收入类型
        Map<String, String> milestoneTypeMap = milestoneTypeList.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription));
        List<String> contractIds = Lists.newArrayList();
        List<String> cusPersonIds = Lists.newArrayList();
        List<String> deptIds = Lists.newArrayList();
        vos.forEach(e -> {
            contractIds.add(e.getContractId());
            cusPersonIds.add(e.getCusPersonId());
            deptIds.add(e.getUndertDept());
            deptIds.add(e.getOfficeDept());
            e.setTechRspUserName(userMap.getOrDefault(e.getTechRspUser(), ""));
            e.setTechRspUserName(userMap.getOrDefault(e.getBusRspUser(), ""));
            e.setCreatorName(userMap.getOrDefault(e.getCreatorId(), ""));
            if (ObjectUtil.isNotEmpty(e.getStatus())) {
                e.setDataStatus(dataStatusMap.getOrDefault(e.getStatus(), null));
            }

            //需要判断当前登陆人是否是单据的中心商务角色
            boolean isCenterBusiness = false; //是否中心商务
            if (!CollectionUtils.isEmpty(centerRoleDOS)) {
                List<String> centerRoleIds = centerRoleDOS.stream().map(RoleDO::getId).collect(Collectors.toList());
                boolean hasIntersection1 = !Collections.disjoint(centerRoleIds, finalExistRoleIds);
                if (hasIntersection1) {
                    isCenterBusiness = true;
                } else {
                    e.setIsCenterBusiness(false);
                }
            }
            if (isCenterBusiness) {
                String undertDept = e.getUndertDept();
                if (ObjectUtil.isNotEmpty(undertDept) && orgId.equals(undertDept)) {
                    e.setIsCenterBusiness(true);
                } else {
                    e.setIsCenterBusiness(false);
                }
            }

        });
        //币种设置名称
        List<DictValueVO> currencyType = dictRedisHelper.getByDictNumber("currency_type", CurrentUserHelper.getOrgId());
        Map<String, String> currencyMap = currencyType.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription, (k1, k2) -> k1));
        for (ContractMilestoneVO vo : vos) {
            if (StrUtil.isNotEmpty(vo.getCurrency())) {
                vo.setCurrencyName(currencyMap.getOrDefault(vo.getCurrency(), ""));
            }
        }


        Map<String, MarketContract> contractMap;
        if (!CollectionUtils.isEmpty(contractIds)) {
            List<MarketContract> contracts = marketContractMapper.selectBatchIds(contractIds);
            contractMap = contracts.stream().collect(Collectors.toMap(MarketContract::getId, Function.identity()));
        } else {
            contractMap = new HashMap<>();
        }
        List<DictValueVO> cosBusTypeList = dictRedisHelper.getDictList("dict1803442064070205440");//业务类型
        Map<String, String> cosBusTypeMap = cosBusTypeList.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));
        Map<String, DeptVO> deptMap;
        if (!CollectionUtils.isEmpty(deptIds)) {
            List<DeptVO> depts = deptRedisHelper.getDeptByIds(deptIds);
            deptMap = depts.stream().collect(Collectors.toMap(DeptVO::getId, Function.identity()));
        } else {
            deptMap = new HashMap<>();
        }

        Map<String, String> collect;
        if (!cusPersonIds.isEmpty()) {
            LambdaQueryWrapperX<CustomerInfo> wrapperX = new LambdaQueryWrapperX<>(CustomerInfo.class);
            wrapperX.in(CustomerInfo::getId, cusPersonIds);
            List<CustomerInfo> customerInfos = customerInfoService.getBaseMapper().selectList(wrapperX);
            collect = customerInfos.stream().collect(Collectors.toMap(CustomerInfo::getId, CustomerInfo::getCusName));
        } else {
            collect = new HashMap<>();
        }
        ContractMilestoneVO contractMilestoneVO = vos.get(0);
        String milestioneIds = contractMilestoneVO.getId();
        LambdaQueryWrapperX<ContractMilestone> contractMilestoneLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        contractMilestoneLambdaQueryWrapperX.in(ContractMilestone::getParentId, milestioneIds);
        contractMilestoneLambdaQueryWrapperX.select(ContractMilestone::getId, ContractMilestone::getParentId, ContractMilestone::getTaxRate);
        List<ContractMilestone> parentContractMilestones = this.list(contractMilestoneLambdaQueryWrapperX);
        Map<String, List<ContractMilestone>> parentMilestoneMap = parentContractMilestones.stream().collect(Collectors.groupingBy(ContractMilestone::getParentId));
        vos.forEach(vo -> {
            contractIds.add(vo.getContractId());
            cusPersonIds.add(vo.getCusPersonId());
            deptIds.add(vo.getOfficeDept());
            String mileIncomeType = vo.getMileIncomeType();
            if (StringUtils.isNotBlank(mileIncomeType)) {
                String mileIncomeTypeName = milestoneTypeMap.get(mileIncomeType);
                if (StringUtils.isNotBlank(mileIncomeTypeName)) {
                    vo.setIncomeTypeName(mileIncomeTypeName);
                    vo.setIncomeTypeId(mileIncomeType);
                }
            }

            String parentId = vo.getParentId();
            //判断当前的里程碑如果有两个或以上的子里程碑 则给综合税率rate赋值 这样前端展示 综合
            if (ObjectUtil.isNotEmpty(parentId)) {
                vo.setIsOneMileStone("2");
                vo.setRate(vo.getTaxRate().stripTrailingZeros().toString() + "%");
            } else {
                vo.setIsOneMileStone("1");
                List<ContractMilestone> contractMilestones = parentMilestoneMap.get(vo.getId());
                if (ObjectUtil.isNotEmpty(contractMilestones)) {
                    if (contractMilestones.size() > 1) {
                        HashSet<BigDecimal> uniqueTaxRate = new HashSet<>();
                        for (ContractMilestone contractMilestone : contractMilestones) {
                            if (ObjectUtil.isNotEmpty(contractMilestone.getTaxRate())) {
                                uniqueTaxRate.add(contractMilestone.getTaxRate());
                            }
                        }
                        if (uniqueTaxRate.size() > 1) {
                            //说明存在两个或以上 且值不相同的子里程碑
                            vo.setRate("综合");
                        } else {
                            if (ObjectUtil.isNotEmpty(uniqueTaxRate)) {
                                List<BigDecimal> uniqueTaxRates = new ArrayList<>(uniqueTaxRate);
                                if (ObjectUtil.isNotEmpty(uniqueTaxRates)) {
                                    BigDecimal bigDecimal = uniqueTaxRates.get(0);
                                    BigDecimal stripTrailingZeros = bigDecimal.stripTrailingZeros();
                                    vo.setRate(stripTrailingZeros + "%");
                                }
                            }

                        }
                    } else if (contractMilestones.size() == 1) {
                        ContractMilestone contractMilestone = contractMilestones.get(0);
                        if (ObjectUtil.isNotEmpty(contractMilestone.getTaxRate())) {
                            vo.setRate(contractMilestone.getTaxRate().stripTrailingZeros().toString() + "%");
                        }
                    }
                } else {
                    vo.setRate(vo.getTaxRate().stripTrailingZeros().toString() + "%");
                }

            }
            vo.setCusPersonName(StringUtils.isEmpty(collect.get(vo.getCusPersonId())) ? null : collect.get(vo.getCusPersonId()));
            String costBusType = vo.getCostBusType();
            if (StringUtils.isNotBlank(costBusType)) {
                String costBusTypeName = cosBusTypeMap.get(costBusType);
                if (StringUtils.isNotBlank(costBusTypeName)) {
                    vo.setCostBusTypeName(costBusTypeName);
                    vo.setCostBusType(costBusType);
                }
            }
            if (contractMap.containsKey(vo.getContractId())) {
                vo.setContractNumber(contractMap.get(vo.getContractId()).getNumber());
                vo.setContractName(contractMap.get(vo.getContractId()).getName());
                vo.setContractStatus(contractMap.get(vo.getContractId()).getStatus());
            }
            vo.setOfficeDeptName(deptMap.containsKey(vo.getOfficeDept()) ? deptMap.get(vo.getOfficeDept()).getName() : "");
            vo.setUndertDeptName(deptMap.containsKey(vo.getUndertDept()) ? deptMap.get(vo.getUndertDept()).getName() : "");

        });
    }

    /**
     * 根据里程碑的金融类型展示金额
     */
    public ContractMilestoneVO setAmt(ContractMilestoneVO contractMilestone) {

        String ammountType = contractMilestone.getAmmountType();
        if (ObjectUtil.isNotEmpty(ammountType)) {
            if ("milestone_amt".equals(ammountType)) {
                BigDecimal milestoneAmt = contractMilestone.getMilestoneAmt();
                contractMilestone.setAmt(milestoneAmt);

            } else {
                BigDecimal exceptAcceptanceAmt = contractMilestone.getExceptAcceptanceAmt();
                contractMilestone.setAmt(exceptAcceptanceAmt);
            }
        }

        return contractMilestone;

    }

    /**
     * 根据里程碑的时间类型展示时间
     */
    public ContractMilestoneVO setCheckDate(ContractMilestoneVO contractMilestone) {

        String dateType = contractMilestone.getDateType();
        if (ObjectUtil.isNotEmpty(dateType)) {
            if ("plan_accept_date".equals(dateType)) {
                Date planAcceptDate = contractMilestone.getPlanAcceptDate();
                contractMilestone.setCheckDate(planAcceptDate);

            } else {
                Date expectAcceptDate = contractMilestone.getExpectAcceptDate();
                contractMilestone.setCheckDate(expectAcceptDate);
            }
        }

        return contractMilestone;

    }

    /**
     * 获取合同的里程碑树
     *
     * @param searchVO
     * @return
     */
    @Override
    public List<TreeNodeVO<NodeVO<MileStoneTreeVo>>> queryTree(MileStoneTreeVo searchVO) throws Exception {
        log.info("【合同详情-里程碑-树查询】开始 ");
        long startTime = System.currentTimeMillis();
        String contractId = searchVO.getId();
        ArrayList<String> contractIds = new ArrayList<>();
        //合同是否有子订单 默认有
        boolean flag = true;
        ArrayList<String> quanxian = new ArrayList<>(1);
        contractIds.add(contractId);
        //获取所有的子订单
        LambdaQueryWrapperX<MarketContract> marketContractLambdaQueryWrapperX = new LambdaQueryWrapperX<>(MarketContract.class);
        marketContractLambdaQueryWrapperX.and(item -> {
            item.eq(MarketContract::getFrameContractId, contractId).or().eq(MarketContract::getId, contractId); // 查询子订单和合同本身数据
        });
        marketContractLambdaQueryWrapperX.select(MarketContract::getId, MarketContract::getName
                , MarketContract::getContractType, MarketContract::getNumber, MarketContract::getStatus, MarketContract::getFrameContractId);
        List<MarketContract> marketContractList = MarketContractMapper.selectList(marketContractLambdaQueryWrapperX);
        Map<String, MarketContract> contractMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(marketContractList)) {
            for (MarketContract marketContract : marketContractList) {
                if (Objects.equals(marketContract.getFrameContractId(), contractId)) {
                    contractIds.add(marketContract.getId());
                }
                contractMap.put(marketContract.getId(), marketContract);
            }
        } else {
            flag = false;
        }
        quanxian = contractIds;
        log.info("【合同详情-里程碑-市场合同查询】耗时：{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();

        List<DataStatusVO> marketDataStatusVOS = dataStatusHelper.getPolicyStatusInfo(MarketContract.class);
        List<DataStatusVO> contractMileDataStatusVOS = dataStatusHelper.getPolicyStatusInfo(ContractMilestone.class);
        Map<Integer, DataStatusVO> dataStatusVOMap = new HashMap<>();
        Map<Integer, DataStatusVO> marketDataStatusVOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(marketDataStatusVOS)) {
            marketDataStatusVOMap = marketDataStatusVOS.stream().collect(Collectors.toMap(DataStatusVO::getStatusValue, Function.identity()));
        }
        if (!CollectionUtils.isEmpty(contractMileDataStatusVOS)) {
            dataStatusVOMap = contractMileDataStatusVOS.stream().collect(Collectors.toMap(DataStatusVO::getStatusValue, Function.identity()));
        }
        log.info("【合同详情-里程碑-市场合同查询-字典-合同状态】耗时：{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        // 获取合同里程碑
        LambdaQueryWrapperX<ContractMilestoneSimple> contractMilestoneLambdaQueryWrapperX = new LambdaQueryWrapperX<>(ContractMilestoneSimple.class);
        contractMilestoneLambdaQueryWrapperX.in(ContractMilestoneSimple::getContractId, contractIds);
        if (!CollectionUtils.isEmpty(searchVO.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchVO.getSearchConditions(), contractMilestoneLambdaQueryWrapperX);
        }
        List<ContractMilestoneSimple> contractMilestones = contractMilestoneSimpleMapper.selectList(contractMilestoneLambdaQueryWrapperX);
        List<ContractMilestoneVO> contractMilestoneVOS = new ArrayList<ContractMilestoneVO>();
        HashSet<String> marketSet = new HashSet<>();
        log.info("【合同详情-里程碑-合同里程碑查询】耗时：{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        for (ContractMilestoneSimple contractMilestone : contractMilestones) {
            ContractMilestoneVO contractMilestoneVO = new ContractMilestoneVO();
            BeanCopyUtils.copyProperties(contractMilestone, contractMilestoneVO);
            contractMilestoneVOS.add(contractMilestoneVO);
            String milestoneVOContractId = contractMilestoneVO.getContractId();
            if (ObjectUtil.isNotEmpty(milestoneVOContractId)) {
                marketSet.add(milestoneVOContractId);
            }
        }
        List<String> projectCodes = contractMilestoneVOS.stream().filter(item -> StringUtils.isNotBlank(item.getProjectCode())).map(ContractMilestoneVO::getProjectCode).distinct().collect(Collectors.toList());
        Map<String, ProjectInitiationSimple> projectMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(projectCodes)) {
            LambdaQueryWrapperX<ProjectInitiationSimple> projectInitiationLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            projectInitiationLambdaQueryWrapperX.in(ProjectInitiationSimple::getProjectNumber, projectCodes);
            projectInitiationLambdaQueryWrapperX.select(ProjectInitiationSimple::getId, ProjectInitiationSimple::getProjectName, ProjectInitiationSimple::getInitiationTime, ProjectInitiationSimple::getProjectNumber);
            List<ProjectInitiationSimple> initiationList = projectInitiationSimpleMapper.selectList(projectInitiationLambdaQueryWrapperX);
            if (!CollectionUtils.isEmpty(initiationList)) {
                projectMap = initiationList.stream().collect(Collectors.toMap(ProjectInitiationSimple::getProjectNumber, Function.identity(), (k1, k2) -> k1));
            }
        }
        log.info("【合同详情-里程碑-项目立项查询】耗时：{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        setTreeNameDetail1(contractMilestoneVOS, contractMap, projectMap);
        log.info("【合同详情-里程碑-setTreeNameDetail1】耗时：{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();

        String kongbaiId = contractId + "kongbai";
        // 节点数据 里程碑的数据
        List<NodeVO<MileStoneTreeVo>> nodeVOS = new ArrayList<>();
        //整理出来所有有子里程碑的里程碑
        List<String> orderList = new ArrayList<>(); // 获取 里程碑订单id
        for (ContractMilestoneVO contractMilestone : contractMilestoneVOS) {
            String parentId = contractMilestone.getParentId();
            NodeVO<MileStoneTreeVo> nodeVO = new NodeVO<>();
            orderList.add(contractMilestone.getContractId());
            //子里程碑，parentId取他们父里程碑的id
            if (ObjectUtil.isNotEmpty(parentId)) { //有父级id 就是子里程碑
                Integer status = contractMilestone.getStatus();
                contractMilestone.setDataStatus(dataStatusVOMap.get(status));
                quanxian.add(contractMilestone.getId());
                nodeVO.setId(contractMilestone.getId());
                nodeVO.setName(contractMilestone.getMilestoneName());
                nodeVO.setParentId(contractMilestone.getParentId());
                nodeVO.setNodeType("contractMilestoneSon");
                MileStoneTreeVo mileStoneTreeVo = new MileStoneTreeVo();
                BeanCopyUtils.copyProperties(contractMilestone, mileStoneTreeVo);
                mileStoneTreeVo.setChildernCount(0);//代表是子里程碑
                nodeVO.setData(mileStoneTreeVo);
            } else {
                // 里程碑，parentId取他们的合同id
                //判断是否是直属合同的里程碑
                if (contractId.equals(contractMilestone.getContractId())) {
                    Integer status = contractMilestone.getStatus();
                    contractMilestone.setDataStatus(dataStatusVOMap.get(status));
                    quanxian.add(contractMilestone.getId());
                    nodeVO.setId(contractMilestone.getId());
                    nodeVO.setName(contractMilestone.getMilestoneName());
                    if (flag) {
                        nodeVO.setParentId(kongbaiId);
                    } else {
                        nodeVO.setParentId(contractId);
                    }
                    MileStoneTreeVo mileStoneTreeVo = new MileStoneTreeVo();
                    BeanCopyUtils.copyProperties(contractMilestone, mileStoneTreeVo);
                    mileStoneTreeVo.setChildernCount(1);//代表不是子里程碑
                    mileStoneTreeVo.setMilestoneAmtTJ(contractMilestone.getMilestoneAmt()); //一级里程碑的金额就代表着要统计的金额
                    mileStoneTreeVo.setExceptAcceptanceAmtTJ(contractMilestone.getExceptAcceptanceAmt()); //一级里程碑的金额就代表着要统计的金额
                    nodeVO.setNodeType("contractMilestone");
                    nodeVO.setData(mileStoneTreeVo);
                } else {
                    Integer status = contractMilestone.getStatus();
                    contractMilestone.setDataStatus(dataStatusVOMap.get(status));
                    nodeVO.setId(contractMilestone.getId());
                    quanxian.add(contractMilestone.getId());
                    nodeVO.setName(contractMilestone.getMilestoneName());
                    nodeVO.setParentId(contractMilestone.getContractId());
                    MileStoneTreeVo mileStoneTreeVo = new MileStoneTreeVo();
                    BeanCopyUtils.copyProperties(contractMilestone, mileStoneTreeVo);
                    mileStoneTreeVo.setChildernCount(1);//代表不是子里程碑
                    //判断当前里程碑有没有子里程碑 如果有 把里程碑的金额都设为0
                    mileStoneTreeVo.setMilestoneAmtTJ(contractMilestone.getMilestoneAmt());//一级里程碑的金额就代表着要统计的金额
                    mileStoneTreeVo.setExceptAcceptanceAmtTJ(contractMilestone.getExceptAcceptanceAmt());//一级里程碑的金额就代表着要统计的金额
                    nodeVO.setData(mileStoneTreeVo);
                    nodeVO.setNodeType("contractMilestone");

                }
            }
            nodeVOS.add(nodeVO);
        }

        if (flag) {
            MarketContract marketContract = contractMap.get(contractId);
            //子订单空白行的数据
            NodeVO<MileStoneTreeVo> nodeVO = new NodeVO<>();
            nodeVO.setId(kongbaiId);
            quanxian.add(kongbaiId);
            nodeVO.setName(marketContract.getName());
            nodeVO.setParentId(contractId);
            MileStoneTreeVo mileStoneTreeVo = new MileStoneTreeVo();
            mileStoneTreeVo.setDataStatus(marketDataStatusVOMap.get(marketContract.getStatus()));
            mileStoneTreeVo.setIsStatistics(true);
            nodeVO.setData(mileStoneTreeVo);
            nodeVO.setNodeType("subOrder");
            nodeVOS.add(nodeVO);
        }
        //子订单行的数据(不包括空白行) 去掉了没有里程碑的子订单
        if (ObjectUtil.isNotEmpty(marketContractList)) {
            for (MarketContract marketContract : marketContractList) {
                NodeVO<MileStoneTreeVo> nodeVO = new NodeVO<>();
                if (!marketContract.getId().equals(contractId) && orderList.contains(marketContract.getId())) {
                    //子订单的框架合同id
                    nodeVO.setId(marketContract.getId());
                    nodeVO.setName(marketContract.getName());
                    nodeVO.setParentId(contractId);
                    MileStoneTreeVo mileStoneTreeVo = new MileStoneTreeVo();
                    BeanCopyUtils.copyProperties(marketContract, mileStoneTreeVo);
                    DataStatusVO dataStatusVO = marketDataStatusVOMap.get(marketContract.getStatus());
                    mileStoneTreeVo.setDataStatus(dataStatusVO);
                    mileStoneTreeVo.setIsStatistics(true);
                    nodeVO.setData(mileStoneTreeVo);
                    nodeVO.setNodeType("subOrder");
                    nodeVOS.add(nodeVO);
                }
            }
        }
        //主合同的数据
        NodeVO<MileStoneTreeVo> nodeVO2 = new NodeVO<>();
        MarketContract marketContract = contractMap.get(contractId);
        if (Objects.isNull(marketContract)) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERR.getErrorCode(), "合同不存在");
        }
        nodeVO2.setId(marketContract.getId());
        nodeVO2.setName(marketContract.getName());
        nodeVO2.setParentId(null);
        MileStoneTreeVo mileStoneTreeVo2 = new MileStoneTreeVo();
        BeanCopyUtils.copyProperties(marketContract, mileStoneTreeVo2);
        mileStoneTreeVo2.setDataStatus(marketDataStatusVOMap.get(marketContract.getStatus()));
        mileStoneTreeVo2.setIsStatistics(true);
        nodeVO2.setNodeType("markertContract");
        nodeVO2.setData(mileStoneTreeVo2);
        nodeVOS.add(nodeVO2);
        log.info("【合同详情-里程碑-数据转换】耗时：{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        //设置权限
        HashMap<String, Set<String>> currentPermissions = new HashMap<>();
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        LambdaQueryWrapperX<RoleDO> roleUserDOQueryWrapper = new LambdaQueryWrapperX<>(RoleDO.class);
        roleUserDOQueryWrapper.eq(RoleUserDO::getUserId, currentUserId);
        roleUserDOQueryWrapper.innerJoin(RoleUserDO.class, RoleUserDO::getRoleId, RoleDO::getId);
        roleUserDOQueryWrapper.select(RoleDO::getCode);
        List<RoleDO> roles = roleDOMapper.selectList(roleUserDOQueryWrapper);
        log.info("【合同详情-里程碑-角色获取】耗时：{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        List<String> roleCode = roles.stream().map(RoleDO::getCode).distinct().collect(Collectors.toList());
        UserPartTimeVO userPrincipal = userDeptBo.getUserDeptInfo(currentUserId);
        log.info("【合同详情-里程碑-缓存获取】耗时：{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        String deptCode = userPrincipal.getOrgCode();
        Map<String, Set<String>> dataIdToRoleMap = this.currentUserRoles(quanxian);
        log.info("【合同详情-里程碑-权限获取】耗时：{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        //判断角色
        Set<String> roleSet = new HashSet<>();
        if (roleCode.contains("Business_06") || roleCode.contains("Business_05") || roleCode.contains("Business_020")
                || roleCode.contains("Business_023") || roleCode.contains("Business_021") || roleCode.contains("Business_022")) {
            //这些角色给所有的权限
            roleSet.add(Permission.WRITE.name());
            currentPermissions.put(marketContract.getId(), roleSet);
        } else if (roleCode.contains("Business_01")) {
            //合同的权限
            String contractTechRspDept = marketContract.getTechRspDept();
            if (ObjectUtil.isNotEmpty(contractTechRspDept) && contractTechRspDept.equals(deptCode)) {
                roleSet.add(Permission.WRITE.name());
                currentPermissions.put(marketContract.getId(), roleSet);
            }

            //子订单一行的权限
            for (MarketContract contract : marketContractList) {
                String techRspDept = contract.getTechRspDept();
                if (ObjectUtil.isNotEmpty(techRspDept) && deptCode.equals(techRspDept)) {
                    roleSet.add(Permission.WRITE.name());
                    currentPermissions.put(contract.getId(), roleSet);
                }
            }
            //里程碑的权限（包含子里程碑的权限）
            for (ContractMilestoneSimple contractMilestone : contractMilestones) {
                String undertDept = contractMilestone.getUndertDept();
                if (ObjectUtil.isNotEmpty(undertDept) && deptCode.equals(undertDept)) {
                    roleSet.add(Permission.WRITE.name());
                    currentPermissions.put(contractMilestone.getId(), roleSet);
                    roleSet.clear();
                }
            }

        } else {
            //检查是不是合同的商务接口人或者技术接口人
            String contractTechRspUser = marketContract.getTechRspUser();
            String contractCommerceRspUser = marketContract.getCommerceRspUser();
            if (ObjectUtil.isNotEmpty(contractCommerceRspUser)) {
                if (currentUserId.equals(contractCommerceRspUser)) {
                    roleSet.add(Permission.WRITE.name());
                }
            }
            if (ObjectUtil.isNotEmpty(contractTechRspUser)) {
                if (currentUserId.equals(contractTechRspUser)) {
                    roleSet.add(Permission.WRITE.name());
                }
            }
            if (!roleSet.isEmpty()) {
                currentPermissions.put(marketContract.getId(), roleSet);
            }
            roleSet.clear();
            //检查是不是子订单的商务接口人或者技术接口人
            for (MarketContract contract : marketContractList) {
                String commerceRspUser = contract.getCommerceRspUser();
                String techRspUser = contract.getTechRspUser();
                if (ObjectUtil.isNotEmpty(commerceRspUser)) {
                    if (currentUserId.equals(commerceRspUser)) {
                        roleSet.add(Permission.WRITE.name());
                    }
                }
                if (ObjectUtil.isNotEmpty(techRspUser)) {
                    if (currentUserId.equals(techRspUser)) {
                        roleSet.add(Permission.WRITE.name());
                    }
                }
                if (!roleSet.isEmpty()) {
                    currentPermissions.put(contract.getId(), roleSet);
                }
                roleSet.clear();
            }
            //检查是不是里程碑的商务接口人或者技术接口人
            for (ContractMilestoneSimple contractMilestone : contractMilestones) {
                String techRspUser = contractMilestone.getTechRspUser();
                String busRspUser = contractMilestone.getBusRspUser();
                if (ObjectUtil.isNotEmpty(busRspUser)) {
                    if (currentUserId.equals(busRspUser)) {
                        roleSet.add(Permission.READ.name());
                    }
                }
                if (ObjectUtil.isNotEmpty(techRspUser)) {
                    if (currentUserId.equals(techRspUser)) {
                        roleSet.add(Permission.READ.name());
                    }
                }
                if (!roleSet.isEmpty()) {
                    currentPermissions.put(contractMilestone.getId(), roleSet);
                }
                roleSet.clear();
            }
        }
        HashMap<String, Set<String>> finialCurrentPermissions = new HashMap<>(currentPermissions);
        finialCurrentPermissions.putAll(dataIdToRoleMap);
        log.error(JSONObject.toJSONString(nodeVOS));
        log.info("【合同详情-里程碑-权限设置】耗时：{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        TreeInfoProcessor<NodeVO<MileStoneTreeVo>> processor = new TreeInfoProcessor<>(
                nodeVOS,
                NodeVO::getId,
                NodeVO::getParentId,
                NodeVO::getRspUserId,
                NodeVO::getDelimiter,
                CurrentUserHelper.getCurrentUserId(),
                finialCurrentPermissions,
                false,
                new HashMap<>()
        );
        List<TreeNodeVO<NodeVO<MileStoneTreeVo>>> root = processor.getRootList();
        log.info("【合同详情-里程碑-树转换】耗时：{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        return root;

    }

    public Map<String, Set<String>> currentUserRoles(List<String> idList) throws Exception {
        if (CollectionUtils.isEmpty(idList)) {
            return new HashMap<>();
        }
        return commonDataAuthRoleService.currentUserRoles(idList);
    }

    /**
     * 预估里程碑审批
     *
     * @param milestoneDTOS
     * @return
     */
    @Override
    public String approval(List<ContractMilestoneDTO> milestoneDTOS) {
        int size = milestoneDTOS.size();
        if (size > 3) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "单次发起最多三条预估里程碑");
        }
        for (ContractMilestoneDTO milestoneDTO : milestoneDTOS) {
            String id = milestoneDTO.getId();
            ContractMilestone contractMilestone = this.getById(id);
            String ammountType = contractMilestone.getAmmountType();
            if (ObjectUtil.isNotEmpty(ammountType) && !MarketContractMilestoneEnum.EXCEPTACCEPTAMT.getCode().equals(ammountType)) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在非预估里程碑！");
            }
            //获取关联框架合同id
            String contractId = contractMilestone.getContractId();
            MarketContract marketContract = marketContractService.getById(contractId);
            if (ObjectUtil.isNotEmpty(marketContract)) {
                Integer status = marketContract.getStatus();
                //关联框架合同必须是子订单的时候才可以发起审批
                if (!MarketContractStatusEnum.FULFIL.getStatus().equals(status)) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "框架合同履行中时里程碑才能提供审批功能。");
                }
            }
            contractMilestone.setStatus(MarketContractMilestoneStatusEnum.APPROVAL.getStatus());
            this.updateById(contractMilestone);
        }
        return "成功";

    }

    /**
     * 获取子订单的里程碑树
     *
     * @param searchVO
     * @return
     * @throws Exception
     */
    @Override
    public List<TreeNodeVO<NodeVO<MileStoneTreeVo>>> queryOrderTree(MileStoneTreeVo searchVO) throws Exception {
        //获取当前子订单下所有的里程碑(包括子里程碑)
        String id = searchVO.getId();
        LambdaQueryWrapperX<ContractMilestone> contractMilestoneLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        contractMilestoneLambdaQueryWrapperX.eq(ContractMilestone::getContractId, id);
        if (!CollectionUtils.isEmpty(searchVO.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchVO.getSearchConditions(), contractMilestoneLambdaQueryWrapperX);
        }
        List<ContractMilestone> contractMilestones = this.list(contractMilestoneLambdaQueryWrapperX);
        List<ContractMilestoneVO> contractMilestoneVOS = new ArrayList<ContractMilestoneVO>();
        for (ContractMilestone contractMilestone : contractMilestones) {
            ContractMilestoneVO contractMilestoneVO = new ContractMilestoneVO();
            BeanCopyUtils.copyProperties(contractMilestone, contractMilestoneVO);
            contractMilestoneVOS.add(contractMilestoneVO);
        }

        //拿到所有 有里程碑的合同的id（包括子订单）
        List<String> markertContractIds = contractMilestoneVOS.stream().filter(item -> StringUtils.isNotBlank(item.getContractId())).map(ContractMilestoneVO::getContractId).distinct().collect(Collectors.toList());
        //拿到所有 有里程碑的的合同（包括子订单）
        List<MarketContract> marketContractList = new ArrayList<>();
        Map<String, MarketContract> contractMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(markertContractIds)) {
            LambdaQueryWrapperX<MarketContract> contractWrapper = new LambdaQueryWrapperX<>();
            contractWrapper.in(MarketContract::getId, markertContractIds);
            contractWrapper.select(MarketContract::getId, MarketContract::getName, MarketContract::getNumber, MarketContract::getStatus);
            marketContractList = MarketContractMapper.selectList(contractWrapper);
            contractMap = marketContractList.stream().collect(Collectors.toMap(MarketContract::getId, Function.identity()));
        }
        List<String> projectCodes = contractMilestoneVOS.stream().filter(item -> StringUtils.isNotBlank(item.getProjectCode())).map(ContractMilestoneVO::getProjectCode).distinct().collect(Collectors.toList());
        Map<String, ProjectInitiationSimple> projectMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(projectCodes)) {
            LambdaQueryWrapperX<ProjectInitiationSimple> projectInitiationLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            projectInitiationLambdaQueryWrapperX.in(ProjectInitiationSimple::getProjectNumber, projectCodes);
            projectInitiationLambdaQueryWrapperX.select(ProjectInitiationSimple::getId, ProjectInitiationSimple::getProjectName, ProjectInitiationSimple::getInitiationTime, ProjectInitiationSimple::getProjectNumber);
            List<ProjectInitiationSimple> initiationList = projectInitiationSimpleMapper.selectList(projectInitiationLambdaQueryWrapperX);
            if (!CollectionUtils.isEmpty(initiationList)) {
                projectMap = initiationList.stream().collect(Collectors.toMap(ProjectInitiationSimple::getProjectNumber, Function.identity(), (k1, k2) -> k1));
            }
        }

        setTreeNameDetail(contractMilestoneVOS, contractMap, projectMap);
        List<NodeVO<MileStoneTreeVo>> nodeVOS = new ArrayList<>();
        //里程碑和子里程碑的节点数据
        for (ContractMilestoneVO contractMilestone : contractMilestoneVOS) {
            String parentId = contractMilestone.getParentId();
            NodeVO<MileStoneTreeVo> nodeVO = new NodeVO<>();
            //子里程碑，parentId取他们父里程碑的id
            if (ObjectUtil.isNotEmpty(parentId)) { //没有父级id 就是子里程碑
                nodeVO.setId(contractMilestone.getId());
                nodeVO.setName(contractMilestone.getMilestoneName());
                nodeVO.setParentId(contractMilestone.getParentId());
                MileStoneTreeVo mileStoneTreeVo = new MileStoneTreeVo();
                BeanCopyUtils.copyProperties(contractMilestone, mileStoneTreeVo);
                mileStoneTreeVo.setChildernCount(0);//代表是子里程碑
                nodeVO.setData(mileStoneTreeVo);
            } else {
                // 里程碑，parentId取他们的合同id
                nodeVO.setId(contractMilestone.getId());
                nodeVO.setName(contractMilestone.getMilestoneName());
                nodeVO.setParentId(contractMilestone.getContractId());
                MileStoneTreeVo mileStoneTreeVo = new MileStoneTreeVo();
                BeanCopyUtils.copyProperties(contractMilestone, mileStoneTreeVo);
                mileStoneTreeVo.setChildernCount(1);//代表不是子里程碑
                nodeVO.setData(mileStoneTreeVo);

            }
            nodeVOS.add(nodeVO);
        }

        //子订单的节点数据
        NodeVO<MileStoneTreeVo> nodeVO = new NodeVO<>();
        //子订单的框架合同id
        MarketContract marketContract = marketContractService.getById(id);
        nodeVO.setId(marketContract.getId());
        nodeVO.setName(marketContract.getName());
        nodeVO.setParentId(null);
        MileStoneTreeVo mileStoneTreeVo = new MileStoneTreeVo();
        BeanCopyUtils.copyProperties(marketContract, mileStoneTreeVo);
        mileStoneTreeVo.setIsStatistics(true);
        nodeVO.setData(mileStoneTreeVo);
        nodeVOS.add(nodeVO);

        //设置权限
        HashMap<String, Set<String>> currentPermissions = new HashMap<>();
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        QueryWrapper<RoleUserDO> roleUserDOQueryWrapper = new QueryWrapper<>();
        roleUserDOQueryWrapper.eq("user_id", currentUserId);
        List<RoleUserDO> roleUserDOS = roleUserDOMapper.selectList(roleUserDOQueryWrapper);
        List<String> collect = roleUserDOS.stream().map(RoleUserDO::getRoleId).distinct().collect(Collectors.toList());
        List<RoleVO> roles = roleRedisHelper.getRoleByIds(collect);
        List<String> roleCode = roles.stream().map(RoleVO::getCode).distinct().collect(Collectors.toList());
        SimpleUser simpleUser = userRedisHelper.getSimplerUser(CurrentUserHelper.getOrgId(), currentUserId);
        String deptCode = simpleUser.getOrgCode();
        //判断角色
        if (roleCode.contains("Business_06") || roleCode.contains("Business_05") || roleCode.contains("Business_020")
                || roleCode.contains("Business_023") || roleCode.contains("Business_021") || roleCode.contains("Business_022")) {
            //这些角色给所有的权限
            currentPermissions.put(marketContract.getId(), Set.of(Permission.WRITE.name()));
        } else if (roleCode.contains("Business_01")) {


            //子订单一行的权限
            String techRspDept = marketContract.getTechRspDept();
            if (ObjectUtil.isNotEmpty(techRspDept) && deptCode.equals(techRspDept)) {
                currentPermissions.put(marketContract.getId(), Set.of(Permission.WRITE.name()));
            }

            //里程碑的权限（包含子里程碑的权限）
            for (ContractMilestone contractMilestone : contractMilestones) {
                String undertDept = contractMilestone.getUndertDept();
                if (ObjectUtil.isNotEmpty(undertDept) && deptCode.equals(undertDept)) {
                    currentPermissions.put(contractMilestone.getId(), Set.of(Permission.WRITE.name()));
                }
            }

        } else {
            //检查是不是子订单的商务接口人或者技术接口人
            String commerceRspUser = marketContract.getCommerceRspUser();
            String techRspUser = marketContract.getTechRspUser();
            if (ObjectUtil.isNotEmpty(commerceRspUser)) {
                if (currentUserId.equals(commerceRspUser)) {
                    currentPermissions.put(marketContract.getId(), Set.of(Permission.WRITE.name()));
                }
            }
            if (ObjectUtil.isNotEmpty(techRspUser)) {
                if (currentUserId.equals(techRspUser)) {
                    currentPermissions.put(marketContract.getId(), Set.of(Permission.WRITE.name()));
                }
            }

            //检查是不是里程碑的商务接口人或者技术接口人
            for (ContractMilestone contractMilestone : contractMilestones) {
                String techRspUser2 = contractMilestone.getTechRspUser();
                String busRspUser2 = contractMilestone.getBusRspUser();
                if (ObjectUtil.isNotEmpty(busRspUser2)) {
                    if (currentUserId.equals(busRspUser2)) {
                        currentPermissions.put(contractMilestone.getId(), Set.of(Permission.READ.name()));
                    }
                }
                if (ObjectUtil.isNotEmpty(techRspUser2)) {
                    if (currentUserId.equals(techRspUser2)) {
                        currentPermissions.put(contractMilestone.getId(), Set.of(Permission.READ.name()));
                    }
                }
            }
        }

        TreeInfoProcessor<NodeVO<MileStoneTreeVo>> processor = new TreeInfoProcessor<>(
                nodeVOS,
                NodeVO::getId,
                NodeVO::getParentId,
                NodeVO::getRspUserId,
                NodeVO::getDelimiter,
                CurrentUserHelper.getCurrentUserId(),
                currentPermissions,
                false,
                new HashMap<>()
        );
        List<TreeNodeVO<NodeVO<MileStoneTreeVo>>> root = processor.getRootList();
        log.info("Json数据：{}", JSONObject.toJSONString(root));
        return root;
    }


    @Getter
    public static class ContractMilestoneExcelListener extends AnalysisEventListener<ContractMilestoneDTO> {

        private final List<ContractMilestoneDTO> data = new ArrayList<>();

        @Override
        public void invoke(ContractMilestoneDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

    }

    /**
     * 里程碑 - 子项目更新的时候，更新对应的里程碑金额
     *
     * @param parentId 父里程碑id
     */
    private void updateParentMilestoneAmt(String parentId) {
        if (StringUtils.isNotBlank(parentId)) {
            ContractMilestone parent = this.getById(parentId);
            if (parent != null) {
                // 查找所有里程碑下的子项目，统计金额
                List<ContractMilestone> children = this.list(new LambdaQueryWrapper<ContractMilestone>()
                        .eq(ContractMilestone::getParentId, parentId));
                if (!CollectionUtils.isEmpty(children)) {
                    parent.setMilestoneAmt(BigDecimal.ZERO);
                    parent.setExceptAcceptanceAmt(BigDecimal.ZERO);
                    for (ContractMilestone child : children) {
                        if (ObjectUtil.isNotEmpty(child.getMilestoneAmt())) {
                            parent.setMilestoneAmt(parent.getMilestoneAmt().add(child.getMilestoneAmt()));
                        }
                    }
                    for (ContractMilestone child : children) {
                        if (ObjectUtil.isNotEmpty(child.getExceptAcceptanceAmt())) {
                            parent.setExceptAcceptanceAmt(parent.getExceptAcceptanceAmt().add(child.getExceptAcceptanceAmt()));
                        }
                    }


                } else {
                    parent.setMilestoneAmt(BigDecimal.ZERO);
                }
                this.updateById(parent);
            }
        }
    }

    @Override
    public void exportExcelDatatree(ContractMilestoneTreeDTO contractMilestoneTreeDTO, HttpServletResponse response) {
        //用户id
        List<String> userTempIds = new ArrayList<>();
        String milestoneName = "";
        if (!CollectionUtils.isEmpty(contractMilestoneTreeDTO.getSearchConditions())) {
            List<List<SearchCondition>> searchConditions = contractMilestoneTreeDTO.getSearchConditions();
            for (List<SearchCondition> searchCondition : searchConditions) {
                for (SearchCondition searchConditionNow : searchCondition) {
                    if ("milestone_name".equals(searchConditionNow.getField())) {
                        milestoneName = searchConditionNow.getValues().get(0).toString();
                        continue;
                    }
                }
            }

        }
        List<ContractMilestone> contractMilestones = contractMilestoneMapper.selectListContract(contractMilestoneTreeDTO.getContractId(), milestoneName);
        List<ContractMilestoneTreeExcelExportDTO> contractMilestoneTreeExcelExportDTOS = BeanCopyUtils.convertListTo(contractMilestones, ContractMilestoneTreeExcelExportDTO::new);
        //合同id
        List<String> contractIds = contractMilestoneTreeExcelExportDTOS.stream().map(ContractMilestoneTreeExcelExportDTO::getContractId).distinct().collect(Collectors.toList());

        //所级负责人
        List<String> officeLeadIds = contractMilestoneTreeExcelExportDTOS.stream().map(ContractMilestoneTreeExcelExportDTO::getOfficeLeader).distinct().collect(Collectors.toList());
        //客户
        List<String> cusPersonIds = contractMilestoneTreeExcelExportDTOS.stream().map(ContractMilestoneTreeExcelExportDTO::getCusPersonId).distinct().collect(Collectors.toList());
        Map<String, MarketContract> contractMap = new HashMap<>();
        Map<String, FlowTemplateBusinessVO> flowMap = new HashMap<>();
        //流程人id
        List<String> collectFolwUserIds = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(contractIds)) {
            List<MarketContract> marketContracts = marketContractMapper.selectBatchIds(contractIds);
            for (MarketContract marketContract : marketContracts) {
                contractMap.put(marketContract.getId(), marketContract);
            }

            ResponseDTO<List<FlowTemplateBusinessVO>> listByBusinessIds = null;
            try {
                listByBusinessIds = workflowFeignService.findListByBusinessIds(contractIds);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (ObjectUtil.isNotEmpty(listByBusinessIds)) {
                List<FlowTemplateBusinessVO> result = listByBusinessIds.getResult();
                for (FlowTemplateBusinessVO flowTemplateBusinessVO : result) {
                    flowMap.put(flowTemplateBusinessVO.getBusinessId(), flowTemplateBusinessVO);
                }
                //收集流程人员id
                collectFolwUserIds = result.stream().map(FlowTemplateBusinessVO::getApplyUserId).distinct().collect(Collectors.toList());
            }

        }
        userTempIds.addAll(officeLeadIds);
        userTempIds.addAll(collectFolwUserIds);
        userTempIds.addAll(cusPersonIds);

        List<String> totalUserIds = userTempIds.stream().distinct().collect(Collectors.toList());
        //承担部门
        Map<String, String> deptMap = new HashMap<>();
        List<String> deptIds = contractMilestoneTreeExcelExportDTOS.stream().map(ContractMilestoneTreeExcelExportDTO::getUndertDept).distinct().collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(deptIds)) {
            List<DeptDO> deptDOS = deptDOMapper.selectBatchIds(deptIds);
            for (DeptDO deptDO : deptDOS) {
                deptMap.put(deptDO.getId(), deptDO.getName());
            }
        }

        //币种
        List<DictValueVO> currencyList = dictRedisHelper.getDictListByCode("currency_type");
        Map<String, String> currencyeMap = currencyList.stream().collect(
                Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (v1, v2) -> v1));

        //用户
        Map<String, UserVO> uesrMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(totalUserIds)) {
            List<UserVO> userByIds = userRedisHelper.getUserByIds(totalUserIds);
            for (UserVO userById : userByIds) {
                uesrMap.put(userById.getId(), userById);
            }
        }

        for (ContractMilestoneTreeExcelExportDTO excelExportDTO : contractMilestoneTreeExcelExportDTOS) {
            MarketContract marketContract = contractMap.get(excelExportDTO.getContractId());
            //合同信息
            if (ObjectUtil.isNotEmpty(marketContract)) {
                excelExportDTO.setContractNumber(marketContract.getNumber());
                excelExportDTO.setContractName(marketContract.getName());
                excelExportDTO.setContractStatus(marketContract.getStatus());
                excelExportDTO.setContractAmt(marketContract.getContractAmt());
                //销售业务分类
                excelExportDTO.setCustSaleBusType(marketContract.getCustSaleBusType());
                if (ObjectUtil.isNotEmpty(excelExportDTO.getCustSaleBusType())) {
                    DictValueVO byNumber = dictRedisHelper.getByNumber(excelExportDTO.getCustSaleBusType(), CurrentUserHelper.getOrgId());
                    excelExportDTO.setCustSaleBusTypeName(byNumber == null ? "" : byNumber.getDescription());
                }
                if (101 == excelExportDTO.getContractStatus()) {
                    excelExportDTO.setContractStatusName("编制中");
                }
                if (130 == excelExportDTO.getContractStatus()) {
                    excelExportDTO.setContractStatusName("履行中");
                }
                if (160 == excelExportDTO.getContractStatus()) {
                    excelExportDTO.setContractStatusName("已完成");
                }
                if (140 == excelExportDTO.getContractStatus()) {
                    excelExportDTO.setContractStatusName("待分发");
                }
                if (110 == excelExportDTO.getContractStatus()) {
                    excelExportDTO.setContractStatusName("审核中");
                }
                if (121 == excelExportDTO.getContractStatus()) {
                    excelExportDTO.setContractStatusName("待签署");
                }

            }
            //币种
            excelExportDTO.setCurrencyName(currencyeMap.getOrDefault(excelExportDTO.getCurrency(), ""));
            //承担部门
            excelExportDTO.setUndertDeptName(deptMap.getOrDefault(excelExportDTO.getUndertDept(), ""));
            //工作主题
            excelExportDTO.setWorkTopic(excelExportDTO.getUndertDeptName() + "-" + excelExportDTO.getContractName() + "- 合同管理流程");
            //流程信息
            FlowTemplateBusinessVO flowTemplateBusinessVO = flowMap.get(excelExportDTO.getContractId());
            if (ObjectUtil.isNotEmpty(flowTemplateBusinessVO)) {
                excelExportDTO.setFlowStartTime(flowTemplateBusinessVO.getApplyTime());
                excelExportDTO.setFlowEndTime(flowTemplateBusinessVO.getEndTime());
                excelExportDTO.setFlowCreatePersonName(flowTemplateBusinessVO.getApplyUserName());
                excelExportDTO.setFlowCreatePersonNumber(uesrMap.get(flowTemplateBusinessVO.getApplyUserId()).getCode());
            }

            //负责人
            if (ObjectUtil.isNotEmpty(excelExportDTO.getOfficeLeader())) {
                UserVO userVO = uesrMap.get(excelExportDTO.getOfficeLeader());
                excelExportDTO.setOfficeLeaderName(userVO == null ? "" : userVO.getName());
            }
            //客户
            if (ObjectUtil.isNotEmpty(excelExportDTO.getCusPersonId())) {
                UserVO userVO = uesrMap.get(excelExportDTO.getCusPersonId());
                excelExportDTO.setCusPersonName(userVO == null ? "" : userVO.getName());
            }
        }

        String fileName = "合同里程碑报表.xlsx";
        response.reset();
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        try {
            EasyExcel.write(response.getOutputStream(), ContractMilestoneTreeExcelExportDTO.class).sheet("sheet1").doWrite(contractMilestoneTreeExcelExportDTOS);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BaseException(BaseErrorCode.SYSTEM_ERR.getErrorCode(), e.getMessage());
        }


    }

    /**
     * 跟踪确认
     *
     * @param contractMilestoneDTO
     * @return
     * @throws Exception
     */
    @Override
    public String trackConfirmation(ContractMilestoneDTO contractMilestoneDTO) throws Exception {
        ContractMilestone milestone = new ContractMilestone();
        BeanCopyUtils.copyProperties(contractMilestoneDTO, milestone);
//        milestone.setIsTrackConfirm(MileStoneTrackConfirmEnum.TRUE.getCode());
        this.updateById(milestone);
        //todo 后续补充统执行变更并记录
        return milestone.getId();
    }

    /**
     * 里程碑管理列表
     *
     * @param
     * @return
     * @throws Exception
     */
    @Override
    public List<TreeNodeVO<NodeVO<MileStoneTreeVo>>> milestoneManage(MileStoneTreeVo searchVO) throws Exception {
        List<TreeNodeVO<NodeVO<MileStoneTreeVo>>> root = new ArrayList<>();
        //获取当前登陆人的所属中心
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        SimpleUser simplerUser = userRedisHelper.getSimplerUser(CurrentUserHelper.getOrgId(), currentUserId);
        String planManageOrgCode = "50132796";
        String orgCode = simplerUser.getOrgCode();
        String currentCenterId = simplerUser.getOrgId();
        List<NodeVO<MileStoneTreeVo>> nodeVOS = new ArrayList<>();


        //获取所有的里程碑 根据里程碑的关联合同id去拿所有的合同 然后再去放到树里
        LambdaQueryWrapperX<ContractMilestoneSimple> contractMilestoneLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        Map<String, Object> resultSearchCondition = milestoneManageSearchCondition(searchVO.getSearchConditions(), contractMilestoneLambdaQueryWrapperX);
        if (!CollectionUtils.isEmpty(searchVO.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchVO.getSearchConditions(), contractMilestoneLambdaQueryWrapperX);
        }

        //公司商务
        List<String> companyBusinessCodes = new ArrayList<>();
        companyBusinessCodes.add("RL100137");
        companyBusinessCodes.add("Business_020");
        companyBusinessCodes.add("Business_021");
        companyBusinessCodes.add("Business_022");
        companyBusinessCodes.add("Business_023");
        LambdaQueryWrapperX<RoleDO> roleDOLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        roleDOLambdaQueryWrapperX.in(RoleDO::getCode, companyBusinessCodes);
        roleDOLambdaQueryWrapperX.select(RoleDO::getId);
        List<RoleDO> roleDOS = roleDOMapper.selectList(roleDOLambdaQueryWrapperX);
        List<String> companyBussinessIds = roleDOS.stream().map(RoleDO::getId).collect(Collectors.toList());
        List<RoleVO> roleVOList = roleUserHelper.getRoleByUserId(CurrentUserHelper.getOrgId(), currentUserId);
        List<String> existRoleIds = new ArrayList<>();
        List<String> existRoleCodes = new ArrayList<>();
        if (!CollectionUtils.isEmpty(roleVOList)) {
            existRoleIds = roleVOList.stream().map(RoleVO::getId).collect(Collectors.toList());
            existRoleCodes = roleVOList.stream().map(RoleVO::getCode).collect(Collectors.toList());
        }
        boolean hasIntersection = !Collections.disjoint(companyBussinessIds, existRoleIds);
        boolean isCompanyBusiness = false; //是否公司商务
        boolean isCenterBusiness = false; //是否中心商务
        boolean isPlanDirector = false; //是否计划经营部主任
        boolean isCenterDirectorr = false; //是否中心主任
        if (hasIntersection) {
            isCompanyBusiness = true;   //是公司商务
        } else {
            List<String> centerBusinessCodes = new ArrayList<>();
//            centerBusinessCodes.add("RL100138");
            centerBusinessCodes.add("Business_01");
            LambdaQueryWrapperX<RoleDO> centerRoleWrapperX = new LambdaQueryWrapperX<>();
            centerRoleWrapperX.in(RoleDO::getCode, centerBusinessCodes);
            centerRoleWrapperX.select(RoleDO::getId);
            List<RoleDO> centerRoleDOS = roleDOMapper.selectList(centerRoleWrapperX);
            if (!CollectionUtils.isEmpty(centerRoleDOS)) {
                List<String> centerRoleIds = centerRoleDOS.stream().map(RoleDO::getId).collect(Collectors.toList());
                boolean hasIntersection1 = !Collections.disjoint(centerRoleIds, existRoleIds);
                if (hasIntersection1) {
                    isCenterBusiness = true;
                }
            }
        }
        Map<String, List<CommonDataAuthRole>> authRoleMap = new HashMap();
        //公司商务查看所有数据，查看是否是计划经营部主任和中心主任
        if (!isCompanyBusiness) {
            LambdaQueryWrapperX<DeptDO> centerDeptWrapper = new LambdaQueryWrapperX<>(DeptDO.class);
            centerDeptWrapper.eq(DeptDO::getType, "20");
            centerDeptWrapper.select(DeptDO::getId, DeptDO::getName);
            List<DeptDO> centerdeptDOS = deptDOMapper.selectList(centerDeptWrapper);
            List<String> centerDeptIds = centerdeptDOS.stream().map(DeptDO::getId).collect(Collectors.toList());
            LambdaQueryWrapperX<DeptLeaderDO> deptLeaderWrapperX = new LambdaQueryWrapperX<>(DeptLeaderDO.class);
            deptLeaderWrapperX.eq(DeptLeaderDO::getUserId, currentUserId);
            deptLeaderWrapperX.in(DeptLeaderDO::getDeptId, centerDeptIds);
            deptLeaderWrapperX.select(DeptLeaderDO::getDeptId, DeptLeaderDO::getUserId);
            List<DeptLeaderDO> deptLeaderDOS = deptLeaderDORepository.selectList(deptLeaderWrapperX);
            if (!CollectionUtils.isEmpty(deptLeaderDOS)) {
                List<String> planDeptLeaderIds = deptLeaderDOS.stream().filter(item -> planManageOrgCode.equals(item.getDeptId())).map(DeptLeaderDO::getUserId).distinct().collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(planDeptLeaderIds)) {
                    isPlanDirector = true;
                }
                isCenterDirectorr = true;
            }
            LambdaQueryWrapperX<CommonDataAuthRole> authRoleLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            authRoleLambdaQueryWrapperX.eq(CommonDataAuthRole::getBusinessType, BusinessTypeEnum.MILESTONE_LIST.getCode());
            if (CollectionUtils.isEmpty(existRoleCodes)) {
                authRoleLambdaQueryWrapperX.eq(CommonDataAuthRole::getObjectValue, currentUserId);
            } else {
                existRoleCodes.add(currentUserId);
                authRoleLambdaQueryWrapperX.in(CommonDataAuthRole::getObjectValue, existRoleCodes);
            }
            List<CommonDataAuthRole> authRoleList = commonDataAuthRoleMapper.selectList(authRoleLambdaQueryWrapperX);
            if (!CollectionUtils.isEmpty(authRoleList)) {
                authRoleMap = authRoleList.stream().collect(Collectors.groupingBy(CommonDataAuthRole::getDataType));
            }
        }
        List<CommonDataAuthRole> contractAuthList = new ArrayList<>();
        List<CommonDataAuthRole> milestoneAuthList = new ArrayList<>();
        //不是公司商务和不是计划经营部主任查看所有数据
        if (!isCompanyBusiness && !isPlanDirector) {
            List<String> contractIds = new ArrayList<>();
            List<String> milestoneIds = new ArrayList<>();
            List<String> userIds = new ArrayList<>();
            //中心商务或则中心主任，可以查看或则自己部门的数据
            if (isCenterBusiness || isCenterDirectorr) {
                if (StringUtils.isNotBlank(currentCenterId)) {
                    List<String> allOrdIds = new ArrayList<>();
                    allOrdIds.add(currentCenterId);
                    LambdaQueryWrapperX<DeptDO> deptDOLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
                    deptDOLambdaQueryWrapperX.like(DeptDO::getChain, currentCenterId);
                    deptDOLambdaQueryWrapperX.select(DeptDO::getId);
                    List<DeptDO> childDepts = deptDOMapper.selectList(deptDOLambdaQueryWrapperX);
                    if (!CollectionUtils.isEmpty(childDepts)) {
                        allOrdIds.addAll(childDepts.stream().map(DeptDO::getId).distinct().collect(Collectors.toList()));
                    }
                    LambdaQueryWrapperX<DeptUserDO> deptUserDOLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
                    deptUserDOLambdaQueryWrapperX.in(DeptUserDO::getDeptId, allOrdIds);
                    deptUserDOLambdaQueryWrapperX.select(DeptUserDO::getDeptId, DeptUserDO::getUserId);
                    List<DeptUserDO> deptUserDOS = deptUserDOMapper.selectList(DeptUserDO::getDeptId, allOrdIds);
                    if (CollectionUtils.isEmpty(deptUserDOS)) {
                        userIds = deptUserDOS.stream().map(DeptUserDO::getUserId).distinct().collect(Collectors.toList());
                        //  contractMilestoneLambdaQueryWrapperX.and(m -> m.in(ContractMilestone::getContractId, contractIdLists).or().in(ContractMilestone :: getTechRspUser,userIds).or().in(ContractMilestone :: getBusRspUser,userIds));
                    }
                }
            }
            //自己查看自己数据
            userIds.add(currentUserId);
            userIds = userIds.stream().distinct().collect(Collectors.toList());
            String contract_number = resultSearchCondition.get("contract_number") == null ? null : resultSearchCondition.get("contract_number").toString();
            String contract_name = resultSearchCondition.get("contract_name") == null ? null : resultSearchCondition.get("contract_name").toString();
            LambdaQueryWrapperX<MarketContract> marketContractLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            marketContractLambdaQueryWrapperX.in(MarketContract::getCommerceRspUser, userIds);
            marketContractLambdaQueryWrapperX.or().in(MarketContract::getTechRspUser, userIds);
            if (StringUtils.isNotBlank(contract_number)) {
                marketContractLambdaQueryWrapperX.like(MarketContract::getNumber, contract_number);
            }
            if (StringUtils.isNotBlank(contract_name)) {
                marketContractLambdaQueryWrapperX.like(MarketContract::getName, contract_name);
            }
            marketContractLambdaQueryWrapperX.select(MarketContract::getId);
            List<MarketContract> marketContracts = MarketContractMapper.selectList(marketContractLambdaQueryWrapperX);

            if (!CollectionUtils.isEmpty(marketContracts)) {
                List<String> contractIdsTemp = marketContracts.stream().map(MarketContract::getId).collect(Collectors.toList());
                contractIds.addAll(contractIdsTemp);
                LambdaQueryWrapperX<MarketContract> marketContractWrapperX = new LambdaQueryWrapperX<>();
                marketContractWrapperX.in(MarketContract::getFrameContractId, contractIdsTemp);
                marketContractWrapperX.select(MarketContract::getId);
                List<MarketContract> submarketContracts = MarketContractMapper.selectList(marketContractWrapperX);
                if (!CollectionUtils.isEmpty(submarketContracts)) {
                    contractIds.addAll(submarketContracts.stream().map(MarketContract::getId).collect(Collectors.toList()));
                }
            }

            if (!authRoleMap.isEmpty()) {
                List<CommonDataAuthRole> contractAuthRoleList = authRoleMap.get("contract");
                if (!CollectionUtils.isEmpty(contractAuthRoleList)) {
                    contractAuthList.addAll(contractAuthRoleList);
                    contractIds.addAll(contractAuthRoleList.stream().map(CommonDataAuthRole::getDataId).collect(Collectors.toList()));
                }
                List<CommonDataAuthRole> subOrderAuthRoleList = authRoleMap.get("subOrder");
                if (!CollectionUtils.isEmpty(subOrderAuthRoleList)) {
                    contractAuthList.addAll(subOrderAuthRoleList);
                    contractIds.addAll(subOrderAuthRoleList.stream().map(CommonDataAuthRole::getDataId).collect(Collectors.toList()));
                }
                List<CommonDataAuthRole> milestoneAuthRoleList = authRoleMap.get("milestone");
                if (!CollectionUtils.isEmpty(milestoneAuthRoleList)) {
                    milestoneAuthList.addAll(milestoneAuthRoleList);
                    milestoneIds.addAll(milestoneAuthRoleList.stream().map(CommonDataAuthRole::getDataId).collect(Collectors.toList()));
                }
                List<CommonDataAuthRole> subMilestoneAuthRoleList = authRoleMap.get("subMilestone");
                if (!CollectionUtils.isEmpty(subMilestoneAuthRoleList)) {
                    milestoneAuthList.addAll(subMilestoneAuthRoleList);
                    milestoneIds.addAll(subMilestoneAuthRoleList.stream().map(CommonDataAuthRole::getDataId).collect(Collectors.toList()));
                }
            }
            final List<String> userListids = userIds;
            contractMilestoneLambdaQueryWrapperX.and(m ->
            {
                m.in(ContractMilestoneSimple::getId, userListids).or().in(ContractMilestoneSimple::getBusRspUser, userListids);
                if (!CollectionUtils.isEmpty(contractIds)) {
                    m.or().in(ContractMilestoneSimple::getContractId, contractIds);
                }
                if (!CollectionUtils.isEmpty(milestoneIds)) {
                    m.or().in(ContractMilestoneSimple::getId, milestoneIds);
                }
            });
        }
        contractMilestoneLambdaQueryWrapperX.ne(ContractMilestoneSimple::getStatus, MarketContractMilestoneStatusEnum.CREATED.getStatus());
        List<ContractMilestoneSimple> contractMilestones = contractMilestoneSimpleMapper.selectList(contractMilestoneLambdaQueryWrapperX);
        if (CollectionUtils.isEmpty(contractMilestones)) {
            return root;
        }


        Map<String, Set<String>> contractAuthMap = groupAndTransform(contractAuthList);
        Map<String, Set<String>> mileStoneAuthMap = groupAndTransform(milestoneAuthList);


        Map<String, NodeVO<MileStoneTreeVo>> existNode = new HashMap<>();

        //拿到所有 有里程碑的合同的id（包括子订单）
        List<String> markertContractIds = contractMilestones.stream().filter(item -> StringUtils.isNotBlank(item.getContractId())).map(ContractMilestoneSimple::getContractId).distinct().collect(Collectors.toList());
        //拿到所有 有里程碑的的合同（包括子订单）
        List<MarketContract> marketContractList = new ArrayList<>();
        Map<String, MarketContract> contractMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(markertContractIds)) {
            LambdaQueryWrapperX<MarketContract> contractWrapper = new LambdaQueryWrapperX<>();
            contractWrapper.in(MarketContract::getId, markertContractIds);
            contractWrapper.select(MarketContract::getId, MarketContract::getName, MarketContract::getNumber, MarketContract::getTechRspUser, MarketContract::getCommerceRspUser, MarketContract::getStatus, MarketContract::getContractType);
            marketContractList = MarketContractMapper.selectList(contractWrapper);
            contractMap = marketContractList.stream().collect(Collectors.toMap(MarketContract::getId, Function.identity()));
        }
//        List<String> mainContrctType = new ArrayList<>();
//        mainContrctType.add(MarketContractTypeEnums.FRAME_CONTRACT.getCode());
//        mainContrctType.add(MarketContractTypeEnums.TOTAL_PRICE_CONTRACT.getCode());
//        mainContrctType.add(MarketContractTypeEnums.COMPOSITE_CONTRACT.getCode());
//
//        List<MarketContractSimple> mainContractList = marketContractList.stream().filter(item -> mainContrctType.contains(item.getContractType())).collect(Collectors.toList());
//        if (CollectionUtils.isEmpty(mainContractList)) {
//            return root;
//        }
        //设置权限
        HashMap<String, Set<String>> currentPermissions = new HashMap<>();
        List<String> allTechUserIds = new ArrayList<>();

       // allTechUserIds.addAll(marketContractList.stream().filter(item -> StringUtils.isNotBlank(item.getTechRspUser())).map(MarketContractSimple::getTechRspUser).collect(Collectors.toList()));
        allTechUserIds.addAll(contractMilestones.stream().filter(item -> StringUtils.isNotBlank(item.getTechRspUser())).map(ContractMilestoneSimple::getTechRspUser).collect(Collectors.toList()));
        boolean isTrue = handleTreeNode(isCompanyBusiness, isCenterBusiness, isPlanDirector, isCenterDirectorr, currentCenterId, currentUserId, allTechUserIds, nodeVOS, existNode, currentPermissions);
        if (!isTrue) {
            return root;
        }
        List<ContractMilestoneVO> contractMilestoneVOS = BeanCopyUtils.convertListTo(contractMilestones, ContractMilestoneVO::new);


        List<String> projectCodes = contractMilestoneVOS.stream().filter(item -> StringUtils.isNotBlank(item.getProjectCode())).map(ContractMilestoneVO::getProjectCode).distinct().collect(Collectors.toList());
        Map<String, ProjectInitiationSimple> projectMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(projectCodes)) {
            LambdaQueryWrapperX<ProjectInitiationSimple> projectInitiationLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            projectInitiationLambdaQueryWrapperX.in(ProjectInitiationSimple::getProjectNumber, projectCodes);
            projectInitiationLambdaQueryWrapperX.select(ProjectInitiationSimple::getId, ProjectInitiationSimple::getProjectName, ProjectInitiationSimple::getInitiationTime, ProjectInitiationSimple::getProjectNumber);
            List<ProjectInitiationSimple> initiationList = projectInitiationSimpleMapper.selectList(projectInitiationLambdaQueryWrapperX);
            if (!CollectionUtils.isEmpty(initiationList)) {
                projectMap = initiationList.stream().collect(Collectors.toMap(ProjectInitiationSimple::getProjectNumber, Function.identity(), (k1, k2) -> k1));
            }
        }
        setTreeNameDetail(contractMilestoneVOS, contractMap, projectMap);


        handleMileStoneTree(contractMilestoneVOS, contractMap, existNode, nodeVOS, currentUserId, currentPermissions,mileStoneAuthMap);

        TreeInfoProcessor<NodeVO<MileStoneTreeVo>> processor = new TreeInfoProcessor<>(
                nodeVOS,
                NodeVO::getId,
                NodeVO::getParentId,
                NodeVO::getRspUserId,
                NodeVO::getDelimiter,
                CurrentUserHelper.getCurrentUserId(),
                currentPermissions,
                false,
                new HashMap<>()
        );
        root = processor.getRootList();
        return root;
    }


    private Map<String, Object> milestoneManageSearchCondition(List<List<SearchCondition>> searchConditions, LambdaQueryWrapperX<ContractMilestoneSimple> milestoneSimpleWrapper) {
        Map<String, Object> result = new HashMap<>();
        if (CollectionUtil.isEmpty(searchConditions)) {
            return result;
        }
        List<SearchCondition> newCollect = new ArrayList<>();
        AtomicReference<String> custPersonNameRef = new AtomicReference<>("");
        AtomicReference<String> contractNumberRef = new AtomicReference<>("");
        AtomicReference<String> contractNameRef = new AtomicReference<>("");
        for (List<SearchCondition> ss : searchConditions) {
            ss.stream()
                    .filter(s -> "cust_person_name".equals(s.getField()))
                    .findFirst()
                    .ifPresent(condition -> {
                        custPersonNameRef.set(condition.getValues().get(0).toString());
                        ss.removeIf(s -> "cust_person_name".equals(s.getField()));
                    });
            ss.stream()
                    .filter(s -> "contract_number".equals(s.getField()))
                    .findFirst()
                    .ifPresent(condition -> {
                        contractNumberRef.set(condition.getValues().get(0).toString());
                        ss.removeIf(s -> "contract_number".equals(s.getField()));
                    });
            ss.stream()
                    .filter(s -> "contract_name".equals(s.getField()))
                    .findFirst()
                    .ifPresent(condition -> {
                        contractNameRef.set(condition.getValues().get(0).toString());
                        ss.removeIf(s -> "contract_name".equals(s.getField()));
                    });
        }

        searchConditions.removeIf(s -> s.size() < 1);
        result.put("contract_number", contractNumberRef.get());
        result.put("contract_name", contractNameRef.get());
        String custPersonName = custPersonNameRef.get();
        if (org.springframework.util.StringUtils.hasText(custPersonName)) {
            LambdaQueryWrapperX<CustomerInfo> customerInfoLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            customerInfoLambdaQueryWrapperX.like(CustomerInfo::getCusName, custPersonName);
            List<CustomerInfo> customerInfos = customerInfoService.list(customerInfoLambdaQueryWrapperX);
            List<Object> cusPersonIds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(customerInfos)) {
                cusPersonIds = customerInfos.stream().map(CustomerInfo::getId).collect(Collectors.toList());
            } else {
                cusPersonIds.add("");
            }
            SearchCondition searchCondition = new SearchCondition();
            searchCondition.setField("cust_person_id");
            searchCondition.setFieldType("String");
            searchCondition.setQueryType("in");
            searchCondition.setValues(cusPersonIds);
            newCollect.add(searchCondition);
            searchConditions.add(newCollect);
        }
        return result;
    }


    public Map<String, Set<String>> groupAndTransform(List<CommonDataAuthRole> contractAuthList) {
        return contractAuthList.stream()
                .flatMap(role -> {
                    String[] permissions = role.getPermissionCode().split(",");
                    return Arrays.stream(permissions)
                            .map(String::trim) // 去除前后空格
                            .map(permission -> new AbstractMap.SimpleEntry<>(role.getDataId(), permission));
                })
                .collect(Collectors.groupingBy(
                        Map.Entry::getKey,
                        Collectors.mapping(Map.Entry::getValue, Collectors.toSet())
                ));
    }


    private void handleMileStoneTree(List<ContractMilestoneVO> contractMilestoneVOSList, Map<String, ProjectInitiationSimple> projectMap, MarketContract marketContract, List<NodeVO<MileStoneTreeVo>> nodeVOS, String currentUserId, HashMap<String, Set<String>> currentPermissions, Map<String, Set<String>> mileStoneAuthMap) {
        if (!CollectionUtils.isEmpty(contractMilestoneVOSList)) {
            NodeVO<MileStoneTreeVo> wlxNodeVO = null;
            Map<String, NodeVO<MileStoneTreeVo>> existProjectCode = new HashMap<>();
            List<ContractMilestoneVO> parentMilestoneList = contractMilestoneVOSList.stream().filter(item -> StringUtils.isBlank(item.getParentId())).collect(Collectors.toList());
            List<ContractMilestoneVO> subMilestoneList = contractMilestoneVOSList.stream().filter(item -> StringUtils.isNotBlank(item.getParentId())).collect(Collectors.toList());
            Map<String, List<ContractMilestoneVO>> subMilestoneMap = subMilestoneList.stream().collect(Collectors.groupingBy(ContractMilestoneVO::getParentId));
            if (!CollectionUtils.isEmpty(parentMilestoneList)) {
                for (ContractMilestoneVO parentMilestone : parentMilestoneList) {
                    String projectCode = parentMilestone.getProjectCode();
                    NodeVO<MileStoneTreeVo> projectVO = null;
                    NodeVO<MileStoneTreeVo> parentMilestoneVO = new NodeVO<>();
                    if (StringUtils.isNotBlank(projectCode)) {
                        ProjectInitiationSimple projectInitiation = projectMap.get(projectCode);
                        if (projectInitiation != null) {
                            String projectNumber = projectInitiation.getProjectNumber();
                            NodeVO<MileStoneTreeVo> existProjectVO = existProjectCode.get(projectNumber);
                            if (existProjectVO == null) {
                                projectVO = new NodeVO<>();
                                projectVO.setId("project:" + marketContract.getId() + "-" + projectInitiation.getId());
                                projectVO.setName(projectInitiation.getProjectName());
                                projectVO.setDataId(projectInitiation.getId());
                                projectVO.setNodeType("project");
                                projectVO.setParentId("contract:" + marketContract.getId());
                                MileStoneTreeVo projectTreeVo = new MileStoneTreeVo();
                                projectTreeVo.setIsStatistics(true);
                                projectTreeVo.setProjectName(projectInitiation.getProjectName());
                                projectTreeVo.setProjectCode(projectInitiation.getProjectNumber());
                                projectTreeVo.setInitiationTime(projectInitiation.getInitiationTime());
                                projectVO.setData(projectTreeVo);
                                nodeVOS.add(projectVO);
                                existProjectCode.put(projectNumber, projectVO);
                            }
                        }
                    }
                    if (projectVO == null) {
                        if (wlxNodeVO == null) {
                            wlxNodeVO = new NodeVO<>();
                            String wlxId = marketContract.getId() + "wlx";
                            wlxNodeVO.setId("project:" + wlxId);
                            wlxNodeVO.setName("未立项里程碑");
                            wlxNodeVO.setNodeType("project");
                            wlxNodeVO.setDataId(wlxId);
                            wlxNodeVO.setParentId("contract:" + marketContract.getId());
                            MileStoneTreeVo wlxMileStoneTreeVo = new MileStoneTreeVo();
                            wlxNodeVO.setData(wlxMileStoneTreeVo);
                            nodeVOS.add(wlxNodeVO);
                        }
                        parentMilestoneVO.setParentId(wlxNodeVO.getId());
                    } else {
                        parentMilestoneVO.setParentId(projectVO.getId());
                    }

                    parentMilestoneVO.setId("milestone:" + parentMilestone.getId());
                    parentMilestoneVO.setName(parentMilestone.getMilestoneName());
                    parentMilestoneVO.setNodeType("milestone");
                    parentMilestoneVO.setDataId(parentMilestone.getId());
                    MileStoneTreeVo parentMilestoneVo = new MileStoneTreeVo();
                    parentMilestoneVo.setChildernCount(1);
                    parentMilestoneVO.setRspUserId(parentMilestone.getTechRspUser());
                    parentMilestoneVO.setRspUserName(parentMilestone.getTechRspUserName());
                    BeanCopyUtils.copyProperties(parentMilestone, parentMilestoneVo);
                    if (parentMilestoneVo.getMilestoneAmt() != null) {
                        parentMilestoneVo.setMilestoneAmtTJ(parentMilestoneVo.getMilestoneAmt());
                    }
                    if (parentMilestoneVo.getExceptAcceptanceAmt() != null) {
                        parentMilestoneVo.setExceptAcceptanceAmtTJ(parentMilestoneVo.getExceptAcceptanceAmt());
                    }
                    BigDecimal actualMilestoneAmt = parentMilestoneVo.getActualMilestoneAmt();
                    BigDecimal taxRate = parentMilestoneVo.getTaxRate();
                    if (actualMilestoneAmt != null && taxRate != null) {
                        BigDecimal exceptAcceptanceAmtNoRateTJ = actualMilestoneAmt.subtract(actualMilestoneAmt.multiply(taxRate.divide(new BigDecimal(100), RoundingMode.HALF_UP)));
                        parentMilestoneVo.setExceptAcceptanceAmtNoRateTJ(exceptAcceptanceAmtNoRateTJ);
                    }
                    if (parentMilestoneVo.getConfirmIncomeInvoicing() != null) {
                        parentMilestoneVo.setConfirmIncomeInvoicingTJ(parentMilestoneVo.getConfirmIncomeInvoicing());
                    }
                    if (parentMilestoneVo.getConfirmIncomeProvisionalEstimate() != null) {
                        parentMilestoneVo.setConfirmIncomeProvisionalEstimateTJ(parentMilestoneVo.getConfirmIncomeProvisionalEstimate());
                    }
                    if (parentMilestoneVo.getMilestoneAdvanceAmt() != null) {
                        parentMilestoneVo.setMilestoneAdvanceAmtTJ(parentMilestoneVo.getMilestoneAdvanceAmt());
                    }
                    if (parentMilestoneVo.getConfirmIncomeSum() != null) {
                        parentMilestoneVo.setConfirmIncomeSumTJ(parentMilestoneVo.getConfirmIncomeSum());
                    }
                    BigDecimal exceptAcceptanceAmt = parentMilestoneVo.getExceptAcceptanceAmt();
                    BigDecimal confirmIncomeSum = parentMilestoneVo.getConfirmIncomeSum();
                    if (exceptAcceptanceAmt != null && confirmIncomeSum != null) {
                        parentMilestoneVo.setRemaineUnconfiremIncomeAmtTJ(exceptAcceptanceAmt.subtract(confirmIncomeSum));
                    }
                    parentMilestoneVo.setContractNumber(marketContract.getNumber());
                    parentMilestoneVo.setContractName(marketContract.getName());
                    parentMilestoneVO.setData(parentMilestoneVo);
                    nodeVOS.add(parentMilestoneVO);

                    Set<String> parentMilestonePermissions = new HashSet<>();
                    if (currentUserId.equals(parentMilestone.getTechRspUser()) || currentUserId.equals(parentMilestone.getBusRspUser())) {
                        parentMilestonePermissions.add(Permission.WRITE.name());
                    }
                    Set<String> parentMilestonePermissionAuth = mileStoneAuthMap.get(parentMilestone.getId());
                    if (!CollectionUtils.isEmpty(parentMilestonePermissionAuth)) {
                        parentMilestonePermissions.addAll(parentMilestonePermissionAuth);
                    }
                    if (!CollectionUtils.isEmpty(parentMilestonePermissions)) {
                        currentPermissions.put("milestone:" + parentMilestone.getId(), parentMilestonePermissions);
                    }

                    List<ContractMilestoneVO> subMilestones = subMilestoneMap.get(parentMilestone.getId());
                    if (!CollectionUtils.isEmpty(subMilestones)) {
                        parentMilestoneVo.setMilestoneAmtTJ(BigDecimal.ZERO);
                        parentMilestoneVo.setExceptAcceptanceAmtTJ(BigDecimal.ZERO);
                        parentMilestoneVo.setExceptAcceptanceAmtNoRateTJ(BigDecimal.ZERO);
                        parentMilestoneVo.setConfirmIncomeInvoicingTJ(BigDecimal.ZERO);
                        parentMilestoneVo.setConfirmIncomeProvisionalEstimateTJ(BigDecimal.ZERO);
                        parentMilestoneVo.setMilestoneAdvanceAmtTJ(BigDecimal.ZERO);
                        parentMilestoneVo.setRemaineUnconfiremIncomeAmtTJ(BigDecimal.ZERO);

                        parentMilestoneVo.setConfirmIncomeSumTJ(BigDecimal.ZERO);
                        parentMilestoneVo.setIsStatistics(true);
                        for (ContractMilestoneVO subMilestone : subMilestones) {
                            NodeVO<MileStoneTreeVo> subMilestoneVO = new NodeVO<>();
                            subMilestoneVO.setId("milestone:" + subMilestone.getId());
                            subMilestoneVO.setName(subMilestone.getMilestoneName());
                            subMilestoneVO.setRspUserId(subMilestone.getTechRspUser());
                            subMilestoneVO.setRspUserName(subMilestone.getTechRspUserName());
                            subMilestoneVO.setDataId(subMilestone.getId());
                            subMilestoneVO.setNodeType("subMilestone");
                            subMilestoneVO.setParentId("milestone:" + parentMilestone.getId());
                            MileStoneTreeVo subMilestoneVo = new MileStoneTreeVo();
                            BeanCopyUtils.copyProperties(subMilestone, subMilestoneVo);
                            if (subMilestoneVo.getMilestoneAmt() != null) {
                                subMilestoneVo.setMilestoneAmtTJ(subMilestoneVo.getMilestoneAmt());
                            }
                            if (subMilestoneVo.getExceptAcceptanceAmt() != null) {
                                subMilestoneVo.setExceptAcceptanceAmtTJ(subMilestoneVo.getExceptAcceptanceAmt());
                            }

                            actualMilestoneAmt = subMilestoneVo.getActualMilestoneAmt();
                            taxRate = subMilestoneVo.getTaxRate();
                            if (actualMilestoneAmt != null && taxRate != null) {
                                BigDecimal exceptAcceptanceAmtNoRateTJ = actualMilestoneAmt.subtract(actualMilestoneAmt.multiply(taxRate.divide(new BigDecimal(100), RoundingMode.HALF_UP)));
                                subMilestoneVo.setExceptAcceptanceAmtNoRateTJ(exceptAcceptanceAmtNoRateTJ);
                            }
                            if (subMilestoneVo.getConfirmIncomeInvoicing() != null) {
                                subMilestoneVo.setConfirmIncomeInvoicingTJ(subMilestoneVo.getConfirmIncomeInvoicing());
                            }
                            if (subMilestoneVo.getConfirmIncomeProvisionalEstimate() != null) {
                                subMilestoneVo.setConfirmIncomeProvisionalEstimateTJ(subMilestoneVo.getConfirmIncomeProvisionalEstimate());
                            }
                            if (subMilestoneVo.getMilestoneAdvanceAmt() != null) {
                                subMilestoneVo.setMilestoneAdvanceAmtTJ(subMilestoneVo.getMilestoneAdvanceAmt());
                            }
                            if (subMilestoneVo.getConfirmIncomeSum() != null) {
                                subMilestoneVo.setConfirmIncomeSumTJ(subMilestoneVo.getConfirmIncomeSum());
                            }
                            exceptAcceptanceAmt = subMilestoneVo.getExceptAcceptanceAmt();
                            confirmIncomeSum = subMilestoneVo.getConfirmIncomeSum();
                            if (exceptAcceptanceAmt != null && confirmIncomeSum != null) {
                                subMilestoneVo.setRemaineUnconfiremIncomeAmtTJ(exceptAcceptanceAmt.subtract(confirmIncomeSum));
                            }
                            subMilestoneVo.setContractNumber(marketContract.getNumber());
                            subMilestoneVo.setContractName(marketContract.getName());
                            subMilestoneVO.setData(subMilestoneVo);
                            nodeVOS.add(subMilestoneVO);
                            if (currentUserId.equals(subMilestone.getTechRspUser()) || currentUserId.equals(subMilestone.getBusRspUser())) {
                                Set<String> permissions = new HashSet<>();
                                permissions.add(Permission.WRITE.name());
                                currentPermissions.put("milestone:" + subMilestone.getId(), permissions);
                            }

                            Set<String> subMilestonePermissions = new HashSet<>();
                            if (currentUserId.equals(subMilestone.getTechRspUser()) || currentUserId.equals(subMilestone.getBusRspUser())) {
                                subMilestonePermissions.add(Permission.WRITE.name());
                            }
                            Set<String> subMilestonePermissionAuth = mileStoneAuthMap.get(subMilestone.getId());
                            if (!CollectionUtils.isEmpty(subMilestonePermissionAuth)) {
                                subMilestonePermissions.addAll(subMilestonePermissionAuth);
                            }
                            if (!CollectionUtils.isEmpty(subMilestonePermissions)) {
                                currentPermissions.put("milestone:" + subMilestone.getId(), subMilestonePermissions);
                            }
                        }

                    }
                }
            }
        }
    }

    private void handleMileStoneTree(List<ContractMilestoneVO> contractMilestoneVOSList, Map<String, MarketContract> contractMap, Map<String, NodeVO<MileStoneTreeVo>> existNode, List<NodeVO<MileStoneTreeVo>> nodeVOS, String currentUserId, HashMap<String, Set<String>> currentPermissions, Map<String, Set<String>> mileStoneAuthMap) {
        if (!CollectionUtils.isEmpty(contractMilestoneVOSList)) {
            List<ContractMilestoneVO> parentMilestoneList = contractMilestoneVOSList.stream().filter(item -> StringUtils.isBlank(item.getParentId())).collect(Collectors.toList());
            List<ContractMilestoneVO> subMilestoneList = contractMilestoneVOSList.stream().filter(item -> StringUtils.isNotBlank(item.getParentId())).collect(Collectors.toList());
            Map<String, List<ContractMilestoneVO>> subMilestoneMap = subMilestoneList.stream().collect(Collectors.groupingBy(ContractMilestoneVO::getParentId));
            if (!CollectionUtils.isEmpty(parentMilestoneList)) {
                for (ContractMilestoneVO parentMilestone : parentMilestoneList) {
                    String techRspUser = parentMilestone.getTechRspUser();
                    if (StringUtils.isNotBlank(techRspUser) && existNode.get(techRspUser) != null) {
                        NodeVO<MileStoneTreeVo> parentMilestoneVO = new NodeVO<>();
                        parentMilestoneVO.setParentId("user:" + techRspUser);
                        parentMilestoneVO.setId("milestone:" + parentMilestone.getId());
                        parentMilestoneVO.setName(parentMilestone.getMilestoneName());
                        parentMilestoneVO.setNodeType("milestone");
                        parentMilestoneVO.setDataId(parentMilestone.getId());
                        MileStoneTreeVo parentMilestoneVo = new MileStoneTreeVo();
                        parentMilestoneVo.setChildernCount(1);
                        parentMilestoneVO.setRspUserId(parentMilestone.getTechRspUser());
                        parentMilestoneVO.setRspUserName(parentMilestone.getTechRspUserName());
                        BeanCopyUtils.copyProperties(parentMilestone, parentMilestoneVo);
                        if (parentMilestoneVo.getMilestoneAmt() != null) {
                            parentMilestoneVo.setMilestoneAmtTJ(parentMilestoneVo.getMilestoneAmt());
                        }
                        if (parentMilestoneVo.getExceptAcceptanceAmt() != null) {
                            parentMilestoneVo.setExceptAcceptanceAmtTJ(parentMilestoneVo.getExceptAcceptanceAmt());
                        }
                        BigDecimal actualMilestoneAmt = parentMilestoneVo.getActualMilestoneAmt();
                        BigDecimal taxRate = parentMilestoneVo.getTaxRate();
                        if (actualMilestoneAmt != null && taxRate != null) {
                            BigDecimal exceptAcceptanceAmtNoRateTJ = actualMilestoneAmt.subtract(actualMilestoneAmt.multiply(taxRate.divide(new BigDecimal(100), RoundingMode.HALF_UP)));
                            parentMilestoneVo.setExceptAcceptanceAmtNoRateTJ(exceptAcceptanceAmtNoRateTJ);
                        }
                        if (parentMilestoneVo.getConfirmIncomeInvoicing() != null) {
                            parentMilestoneVo.setConfirmIncomeInvoicingTJ(parentMilestoneVo.getConfirmIncomeInvoicing());
                        }
                        if (parentMilestoneVo.getConfirmIncomeProvisionalEstimate() != null) {
                            parentMilestoneVo.setConfirmIncomeProvisionalEstimateTJ(parentMilestoneVo.getConfirmIncomeProvisionalEstimate());
                        }
                        if (parentMilestoneVo.getMilestoneAdvanceAmt() != null) {
                            parentMilestoneVo.setMilestoneAdvanceAmtTJ(parentMilestoneVo.getMilestoneAdvanceAmt());
                        }
                        if (parentMilestoneVo.getConfirmIncomeSum() != null) {
                            parentMilestoneVo.setConfirmIncomeSumTJ(parentMilestoneVo.getConfirmIncomeSum());
                        }
                        BigDecimal exceptAcceptanceAmt = parentMilestoneVo.getExceptAcceptanceAmt();
                        BigDecimal confirmIncomeSum = parentMilestoneVo.getConfirmIncomeSum();
                        if (exceptAcceptanceAmt != null && confirmIncomeSum != null) {
                            parentMilestoneVo.setRemaineUnconfiremIncomeAmtTJ(exceptAcceptanceAmt.subtract(confirmIncomeSum));
                        }
                        parentMilestoneVO.setData(parentMilestoneVo);
                        nodeVOS.add(parentMilestoneVO);
                        Set<String> parentMilestonePermissions = new HashSet<>();
                        String contractId = parentMilestone.getContractId();
                        if(StringUtils.isNotBlank(contractId)){
                            MarketContract marketContract = contractMap.get(contractId);
                            if(marketContract != null){
                                parentMilestoneVo.setContractNumber(marketContract.getNumber());
                                parentMilestoneVo.setContractName(marketContract.getName());
                                if (currentUserId.equals(marketContract.getTechRspUser()) || currentUserId.equals(marketContract.getCommerceRspUser())) {
                                    parentMilestonePermissions.add(Permission.WRITE.name());
                                }
                                else{
                                    String frameContractId = marketContract.getFrameContractId();
                                    if(StringUtils.isNotBlank(frameContractId)){
                                        MarketContract frameContract = contractMap.get(frameContractId);
                                        if(frameContract != null){
                                            if (currentUserId.equals(frameContract.getTechRspUser()) || currentUserId.equals(frameContract.getCommerceRspUser())) {
                                                parentMilestonePermissions.add(Permission.WRITE.name());
                                            }
                                        }
                                    }
                                }
                            }

                        }
                        if (currentUserId.equals(parentMilestone.getTechRspUser()) || currentUserId.equals(parentMilestone.getBusRspUser())) {
                            parentMilestonePermissions.add(Permission.WRITE.name());
                        }
                        Set<String> parentMilestonePermissionAuth = mileStoneAuthMap.get(parentMilestone.getId());
                        if (!CollectionUtils.isEmpty(parentMilestonePermissionAuth)) {
                            parentMilestonePermissions.addAll(parentMilestonePermissionAuth);
                        }
                        if (!CollectionUtils.isEmpty(parentMilestonePermissions)) {
                            currentPermissions.put("milestone:" + parentMilestone.getId(), parentMilestonePermissions);
                        }

                        List<ContractMilestoneVO> subMilestones = subMilestoneMap.get(parentMilestone.getId());
                        if (!CollectionUtils.isEmpty(subMilestones)) {
                            parentMilestoneVo.setMilestoneAmtTJ(BigDecimal.ZERO);
                            parentMilestoneVo.setExceptAcceptanceAmtTJ(BigDecimal.ZERO);
                            parentMilestoneVo.setExceptAcceptanceAmtNoRateTJ(BigDecimal.ZERO);
                            parentMilestoneVo.setConfirmIncomeInvoicingTJ(BigDecimal.ZERO);
                            parentMilestoneVo.setConfirmIncomeProvisionalEstimateTJ(BigDecimal.ZERO);
                            parentMilestoneVo.setMilestoneAdvanceAmtTJ(BigDecimal.ZERO);
                            parentMilestoneVo.setRemaineUnconfiremIncomeAmtTJ(BigDecimal.ZERO);

                            parentMilestoneVo.setConfirmIncomeSumTJ(BigDecimal.ZERO);
                            parentMilestoneVo.setIsStatistics(true);
                            for (ContractMilestoneVO subMilestone : subMilestones) {
                                NodeVO<MileStoneTreeVo> subMilestoneVO = new NodeVO<>();
                                subMilestoneVO.setId("milestone:" + subMilestone.getId());
                                subMilestoneVO.setName(subMilestone.getMilestoneName());
                                subMilestoneVO.setRspUserId(subMilestone.getTechRspUser());
                                subMilestoneVO.setRspUserName(subMilestone.getTechRspUserName());
                                subMilestoneVO.setDataId(subMilestone.getId());
                                subMilestoneVO.setNodeType("subMilestone");
                                subMilestoneVO.setParentId("milestone:" + parentMilestone.getId());
                                MileStoneTreeVo subMilestoneVo = new MileStoneTreeVo();
                                BeanCopyUtils.copyProperties(subMilestone, subMilestoneVo);
                                if (subMilestoneVo.getMilestoneAmt() != null) {
                                    subMilestoneVo.setMilestoneAmtTJ(subMilestoneVo.getMilestoneAmt());
                                }
                                if (subMilestoneVo.getExceptAcceptanceAmt() != null) {
                                    subMilestoneVo.setExceptAcceptanceAmtTJ(subMilestoneVo.getExceptAcceptanceAmt());
                                }

                                actualMilestoneAmt = subMilestoneVo.getActualMilestoneAmt();
                                taxRate = subMilestoneVo.getTaxRate();
                                if (actualMilestoneAmt != null && taxRate != null) {
                                    BigDecimal exceptAcceptanceAmtNoRateTJ = actualMilestoneAmt.subtract(actualMilestoneAmt.multiply(taxRate.divide(new BigDecimal(100), RoundingMode.HALF_UP)));
                                    subMilestoneVo.setExceptAcceptanceAmtNoRateTJ(exceptAcceptanceAmtNoRateTJ);
                                }
                                if (subMilestoneVo.getConfirmIncomeInvoicing() != null) {
                                    subMilestoneVo.setConfirmIncomeInvoicingTJ(subMilestoneVo.getConfirmIncomeInvoicing());
                                }
                                if (subMilestoneVo.getConfirmIncomeProvisionalEstimate() != null) {
                                    subMilestoneVo.setConfirmIncomeProvisionalEstimateTJ(subMilestoneVo.getConfirmIncomeProvisionalEstimate());
                                }
                                if (subMilestoneVo.getMilestoneAdvanceAmt() != null) {
                                    subMilestoneVo.setMilestoneAdvanceAmtTJ(subMilestoneVo.getMilestoneAdvanceAmt());
                                }
                                if (subMilestoneVo.getConfirmIncomeSum() != null) {
                                    subMilestoneVo.setConfirmIncomeSumTJ(subMilestoneVo.getConfirmIncomeSum());
                                }
                                exceptAcceptanceAmt = subMilestoneVo.getExceptAcceptanceAmt();
                                confirmIncomeSum = subMilestoneVo.getConfirmIncomeSum();
                                if (exceptAcceptanceAmt != null && confirmIncomeSum != null) {
                                    subMilestoneVo.setRemaineUnconfiremIncomeAmtTJ(exceptAcceptanceAmt.subtract(confirmIncomeSum));
                                }
                                subMilestoneVo.setContractNumber(parentMilestoneVo.getContractNumber());
                                subMilestoneVo.setContractName(parentMilestoneVo.getContractName());
                                subMilestoneVO.setData(subMilestoneVo);
                                nodeVOS.add(subMilestoneVO);
                                if (currentUserId.equals(subMilestone.getTechRspUser()) || currentUserId.equals(subMilestone.getBusRspUser())) {
                                    Set<String> permissions = new HashSet<>();
                                    permissions.add(Permission.WRITE.name());
                                    currentPermissions.put("milestone:" + subMilestone.getId(), permissions);
                                }

                                Set<String> subMilestonePermissions = new HashSet<>();
                                if (currentUserId.equals(subMilestone.getTechRspUser()) || currentUserId.equals(subMilestone.getBusRspUser())) {
                                    subMilestonePermissions.add(Permission.WRITE.name());
                                }
                                Set<String> subMilestonePermissionAuth = mileStoneAuthMap.get(subMilestone.getId());
                                if (!CollectionUtils.isEmpty(subMilestonePermissionAuth)) {
                                    subMilestonePermissions.addAll(subMilestonePermissionAuth);
                                }
                                if (!CollectionUtils.isEmpty(subMilestonePermissions)) {
                                    currentPermissions.put("milestone:" + subMilestone.getId(), subMilestonePermissions);
                                }
                            }

                        }
                    }
                }
            }
        }
    }

    private boolean handleTreeNode(boolean isCompanyBusiness, boolean isCenterBusiness, boolean isPlanDirector, boolean isCenterDirector, String currentCenterId, String currentUserId, List<String> allTechUserIds, List<NodeVO<MileStoneTreeVo>> nodeVOS, Map<String, NodeVO<MileStoneTreeVo>> existNode, HashMap<String, Set<String>> currentPermissions) {
//        List<String> allUserIds = new ArrayList<>();
//        List<String> allTechUserIds = new ArrayList<>();
       // List<String> allTechUserIds = mainContractList.stream().filter(item -> StringUtils.isNotBlank(item.getTechRspUser())).map(MarketContractSimple::getTechRspUser).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(allTechUserIds)) {
            return false;
        }
        allTechUserIds = allTechUserIds.stream().distinct().collect(Collectors.toList());
        //        allUserIds.addAll(techRspUsers);
//        allTechUserIds.addAll(techRspUsers);
//        List<String> busRspUsers = contractMilestones.stream().filter(item -> StringUtils.isNotBlank(item.getBusRspUser())).map(ContractMilestone :: getBusRspUser).collect(Collectors.toList());
//        allUserIds.addAll(busRspUsers);
//        List<String> allContractIds = contractMilestones.stream().map(ContractMilestone :: getContractId).distinct().collect(Collectors.toList());
//        LambdaQueryWrapperX<MarketContract> contractLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
//        contractLambdaQueryWrapperX.select(MarketContract :: getId,MarketContract :: getFrameContractId);
//        contractLambdaQueryWrapperX.in(MarketContract::getId,allContractIds);
//        List<MarketContract> marketContracts = marketContractService.list(contractLambdaQueryWrapperX);
//        List<String> frameContracIds =  marketContracts.stream().filter(item -> StringUtils.isNotBlank(item.getFrameContractId())).map(MarketContract :: getFrameContractId).collect(Collectors.toList());
//        allContractIds.addAll(frameContracIds);
//        LambdaQueryWrapperX<MarketContract> allContractWrapperX = new LambdaQueryWrapperX<>();
//        allContractWrapperX.in(MarketContract::getId,allContractIds);
//        allContractWrapperX.select(MarketContract::getTechRspUser,MarketContract :: getCommerceRspUser);
//        List<MarketContract> allMarketContracts = marketContractService.list(allContractWrapperX);
//        List<String> contractTechRspUsers = allMarketContracts.stream().filter(item -> StringUtils.isNotBlank(item.getTechRspUser())).map(MarketContract :: getTechRspUser).collect(Collectors.toList());
//
        //  List<String> contractCommerceRspUsers = allMarketContracts.stream().filter(item -> StringUtils.isNotBlank(item.getCommerceRspUser())).map(MarketContract :: getCommerceRspUser).collect(Collectors.toList());
        //    allTechUserIds.addAll(contractTechRspUsers);
        LambdaQueryWrapperX<DeptUserDO> deptUserDOLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        deptUserDOLambdaQueryWrapperX.in(DeptUserDO::getUserId, allTechUserIds);
        deptUserDOLambdaQueryWrapperX.select(DeptUserDO::getUserId, DeptUserDO::getDeptId);
        List<DeptUserDO> deptUserDOS = deptUserDOMapper.selectList(DeptUserDO::getUserId, allTechUserIds);
        List<String> allDeptIds = deptUserDOS.stream().map(DeptUserDO::getDeptId).distinct().collect(Collectors.toList());
        LambdaQueryWrapperX<DeptDO> deptDOLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        deptDOLambdaQueryWrapperX.select(DeptDO::getId, DeptDO::getDeptCode, DeptDO::getName, DeptDO::getType, DeptDO::getChain);
        List<DeptDO> allDeptDOS = deptDOMapper.selectList(deptDOLambdaQueryWrapperX);
        Map<String, DeptDO> allDeptMap = allDeptDOS.stream().collect(Collectors.toMap(DeptDO::getId, Function.identity()));
        List<String> allCenterList = allDeptDOS.stream().filter(item -> "20".equals(item.getType())).map(DeptDO::getId).collect(Collectors.toList());
        List<String> existCenterDeptCodes = new ArrayList<>();
        String companyId = "00014500";
        //根节点
        NodeVO<MileStoneTreeVo> rootNodeVO = new NodeVO<>();
        rootNodeVO.setId("dept:" + companyId);
        rootNodeVO.setName("苏州热工研究院有限公司");
        rootNodeVO.setParentId(null);
        rootNodeVO.setDataId(companyId);
        rootNodeVO.setNodeType("company");
        MileStoneTreeVo rootTreeVo = new MileStoneTreeVo();
        rootTreeVo.setIsStatistics(true);
        rootNodeVO.setData(rootTreeVo);
        nodeVOS.add(rootNodeVO);
        if (isCompanyBusiness) {
            Set<String> permissions = new HashSet<>();
            permissions.add(Permission.WRITE.name());
            currentPermissions.put("dept:" + companyId, permissions);
        } else if (isPlanDirector) {
            Set<String> permissions = new HashSet<>();
            permissions.add(Permission.READ.name());
            currentPermissions.put("dept:" + companyId, permissions);
        }
        log.error("allDeptIds:-------------------------" + JSONObject.toJSONString(allDeptIds));
        for (String deptIdTemp : allDeptIds) {
            DeptDO deptDO = allDeptMap.get(deptIdTemp);
            if (deptDO != null) {
                String type = deptDO.getType();
                if ("20".equals(type)) {
                    if (!existCenterDeptCodes.contains(deptDO.getDeptCode())) {
                        NodeVO<MileStoneTreeVo> centerDeptNodeVO = new NodeVO<>();
                        centerDeptNodeVO.setId("dept:" + deptDO.getDeptCode());
                        centerDeptNodeVO.setName(deptDO.getName());
                        centerDeptNodeVO.setParentId("dept:" + companyId);
                        centerDeptNodeVO.setDataId(deptDO.getDeptCode());
                        centerDeptNodeVO.setNodeType("center");
                        MileStoneTreeVo mileStoneTreeVo = new MileStoneTreeVo();
                        mileStoneTreeVo.setIsStatistics(true);
                        centerDeptNodeVO.setData(mileStoneTreeVo);
                        existCenterDeptCodes.add(deptDO.getDeptCode());
                        nodeVOS.add(centerDeptNodeVO);
                        if (isCenterBusiness && deptDO.getId().equals(currentCenterId)) {
                            Set<String> permissions = new HashSet<>();
                            permissions.add(Permission.WRITE.name());
                            currentPermissions.put("dept:" + deptDO.getDeptCode(), permissions);
                        } else if (isCenterDirector && deptDO.getId().equals(currentCenterId)) {
                            Set<String> permissions = new HashSet<>();
                            permissions.add(Permission.READ.name());
                            currentPermissions.put("dept:" + deptDO.getDeptCode(), permissions);
                        }
                        existNode.put(deptDO.getDeptCode(), centerDeptNodeVO);
                    }
                } else {
                    String chain = deptDO.getChain();
                    if (StringUtils.isNotBlank(chain)) {
                        List<String> chainList = Arrays.asList(chain.split(","));
                        String centerId = chainList.stream()
                                .filter(item -> allCenterList.contains(item))
                                .findFirst().orElse(null);
                        if (StringUtils.isNotBlank(centerId)) {
                            DeptDO centerDept = allDeptMap.get(centerId);
                            if (centerDept != null) {
                                if (!existCenterDeptCodes.contains(centerDept.getDeptCode())) {
                                    NodeVO<MileStoneTreeVo> centerDeptNodeVO = new NodeVO<>();
                                    centerDeptNodeVO.setId("dept:" + centerDept.getDeptCode());
                                    centerDeptNodeVO.setName(centerDept.getName());
                                    centerDeptNodeVO.setParentId("dept:" + companyId);
                                    centerDeptNodeVO.setDataId(centerDept.getDeptCode());
                                    MileStoneTreeVo mileStoneTreeVo = new MileStoneTreeVo();
                                    mileStoneTreeVo.setIsStatistics(true);
                                    centerDeptNodeVO.setNodeType("center");
                                    centerDeptNodeVO.setData(mileStoneTreeVo);
                                    existCenterDeptCodes.add(centerDept.getDeptCode());
                                    nodeVOS.add(centerDeptNodeVO);
                                    if (isCenterBusiness && deptDO.getId().equals(currentCenterId)) {
                                        Set<String> permissions = new HashSet<>();
                                        permissions.add(Permission.WRITE.name());
                                        currentPermissions.put("dept:" + centerDept.getDeptCode(), permissions);
                                    } else if (isCenterDirector && deptDO.getId().equals(currentCenterId)) {
                                        Set<String> permissions = new HashSet<>();
                                        permissions.add(Permission.READ.name());
                                        currentPermissions.put("dept:" + deptDO.getDeptCode(), permissions);
                                    }
                                    existNode.put(centerDept.getDeptCode(), centerDeptNodeVO);
                                }

                                NodeVO<MileStoneTreeVo> deptNodeVO = new NodeVO<>();
                                deptNodeVO.setId("dept:" + deptDO.getDeptCode());
                                deptNodeVO.setName(deptDO.getName());
                                deptNodeVO.setParentId("dept:" + centerDept.getDeptCode());
                                deptNodeVO.setDataId(deptDO.getDeptCode());
                                deptNodeVO.setNodeType("dept");
                                MileStoneTreeVo mileStoneTreeVo = new MileStoneTreeVo();
                                mileStoneTreeVo.setIsStatistics(true);
                                deptNodeVO.setData(mileStoneTreeVo);
                                nodeVOS.add(deptNodeVO);
                                existNode.put(deptDO.getDeptCode(), deptNodeVO);
                            } else {
                                log.info("deptDO---为空------------------------------------：{}", deptDO.getDeptCode());
                            }
                        } else {
                            log.info("centerId为空------------------------------------：{}", deptDO.getDeptCode());
                        }
                    } else {
                        log.info("chain为空------------------------------------：{}", deptDO.getDeptCode());
                    }

                }
            } else {
                log.info("deptDO为空------------------------------------：{}", deptIdTemp);
            }
        }
        LambdaQueryWrapperX<UserDO> userDOLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        userDOLambdaQueryWrapperX.in(UserDO::getId, allTechUserIds.stream().distinct().collect(Collectors.toList()));
        userDOLambdaQueryWrapperX.select(UserDO::getId, UserDO::getName);
        List<UserDO> userDOS = userDOMapper.selectList(userDOLambdaQueryWrapperX);
        Map<String, UserDO> userDOMap = userDOS.stream().collect(Collectors.toMap(UserDO::getId, Function.identity()));
        List<String> existUserId = new ArrayList<>();
        for (DeptUserDO deptUserDO : deptUserDOS) {
            UserDO userDO = userDOMap.get(deptUserDO.getUserId());
            if (userDO != null) {
                DeptDO deptDO = allDeptMap.get(deptUserDO.getDeptId());
                if (deptDO != null) {
                    if (existNode.get(deptDO.getDeptCode()) != null && !existUserId.contains(userDO.getId())) {
                        NodeVO<MileStoneTreeVo> userNodeVO = new NodeVO<>();
                        existUserId.add(userDO.getId());
                        userNodeVO.setId("user:" + userDO.getId());
                        userNodeVO.setName(userDO.getName());
                        userNodeVO.setParentId("dept:" + deptDO.getDeptCode());
                        userNodeVO.setDataId(userDO.getId());
                        userNodeVO.setNodeType("user");
                        MileStoneTreeVo mileStoneTreeVo = new MileStoneTreeVo();
                        mileStoneTreeVo.setIsStatistics(true);
                        userNodeVO.setData(mileStoneTreeVo);
                        nodeVOS.add(userNodeVO);
                        existNode.put(userDO.getId(), userNodeVO);
                        if (userDO.getId().equals(currentUserId)) {
                            Set<String> permissions = new HashSet<>();
                            permissions.add(Permission.WRITE.name());
                            currentPermissions.put("user:" + userDO.getId(), permissions);
                        }
                    }
                }
            }

        }
        return true;
    }

    /**
     * 获取合同下的甲方签约主体
     */
    @Override
    public List<CustomerInfo> getOurSignByContractId(String id) {
        LambdaQueryWrapperX<ContractOurSignedSubject> contractOurSignedSubjectLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        contractOurSignedSubjectLambdaQueryWrapperX.eq(ContractOurSignedSubject::getContractId, id);
        List<ContractOurSignedSubject> ourSignedSubjectList = contractOurSignedSubjectService.list(contractOurSignedSubjectLambdaQueryWrapperX);
        ArrayList<String> customerListIds = new ArrayList<>();
        for (ContractOurSignedSubject contractOurSignedSubject : ourSignedSubjectList) {
            String customer = contractOurSignedSubject.getCustomer();
            customerListIds.add(customer);
        }
        LambdaQueryWrapperX<CustomerInfo> customerInfoLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        customerInfoLambdaQueryWrapperX.in(CustomerInfo::getId, customerListIds);
        List<CustomerInfo> customerInfos = customerInfoService.list(customerInfoLambdaQueryWrapperX);
        return customerInfos;
    }


    /**
     * 新建一个空的里程碑
     *
     * @param mileStoneTreeVo
     * @return
     * @throws Exception
     */
    @Override
    public ContractMilestone createNew(MileStoneTreeVo mileStoneTreeVo) throws Exception {
        Integer nodeType = mileStoneTreeVo.getNodeType();
        Integer level = mileStoneTreeVo.getLevel();
        String id = mileStoneTreeVo.getId();
        ContractMilestone contractMilestone = new ContractMilestone();
        String contractId = mileStoneTreeVo.getContractId();
        MarketContract marketContract = marketContractService.getById(contractId); //合同
        if (ObjectUtil.isEmpty(level)) {
            //新增直属合同里程碑
            contractMilestone.setContractId(contractId);
            // 对一级里程碑，新增里程碑时，默认带出合同的客户；
            LambdaQueryWrapperX<ContractOurSignedSubject> contractOurSignedSubjectLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            contractOurSignedSubjectLambdaQueryWrapperX.eq(ContractOurSignedSubject::getContractId, contractId);
            List<ContractOurSignedSubject> contractOurSignedSubjects = contractOurSignedSubjectService.list(contractOurSignedSubjectLambdaQueryWrapperX);
            if (ObjectUtil.isNotEmpty(contractOurSignedSubjects)) {
                ContractOurSignedSubject contractOurSignedSubject = contractOurSignedSubjects.get(0);
                contractMilestone.setCusPersonId(contractOurSignedSubject.getCustomer());
            }
            setMilestone(contractMilestone, marketContract);
        } else if (level.equals(2) && nodeType.equals(2)) {
            //里程碑加子级
            ContractMilestone parentContractMilestone = this.getById(id);
            contractMilestone.setParentId(id);
            setChildMilestone(contractMilestone, parentContractMilestone);
        } else if (level.equals(2) && nodeType.equals(1)) {
            //里程碑加同级
            ContractMilestone self = this.getById(id);
            contractMilestone.setContractId(self.getContractId());
            // 对一级里程碑，新增里程碑时，默认带出合同的客户；
            LambdaQueryWrapperX<ContractOurSignedSubject> contractOurSignedSubjectLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            contractOurSignedSubjectLambdaQueryWrapperX.eq(ContractOurSignedSubject::getContractId, contractId);
            List<ContractOurSignedSubject> contractOurSignedSubjects = contractOurSignedSubjectService.list(contractOurSignedSubjectLambdaQueryWrapperX);
            if (ObjectUtil.isNotEmpty(contractOurSignedSubjects)) {
                ContractOurSignedSubject contractOurSignedSubject = contractOurSignedSubjects.get(0);
                contractMilestone.setCusPersonId(contractOurSignedSubject.getCustomer());
            }
            setMilestone(contractMilestone, marketContract);
        } else if (level.equals(3) && nodeType.equals(1)) {

            //判断当前的是否是父里程碑
            //里程碑加同级
            ContractMilestone self = this.getById(id);
            String parentId = self.getParentId();
            if (ObjectUtil.isEmpty(parentId)) {
                contractMilestone.setContractId(self.getContractId());
                // 对一级里程碑，新增里程碑时，默认带出合同的客户；
                LambdaQueryWrapperX<ContractOurSignedSubject> contractOurSignedSubjectLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
                contractOurSignedSubjectLambdaQueryWrapperX.eq(ContractOurSignedSubject::getContractId, contractId);
                List<ContractOurSignedSubject> contractOurSignedSubjects = contractOurSignedSubjectService.list(contractOurSignedSubjectLambdaQueryWrapperX);
                if (ObjectUtil.isNotEmpty(contractOurSignedSubjects)) {
                    ContractOurSignedSubject contractOurSignedSubject = contractOurSignedSubjects.get(0);
                    contractMilestone.setCusPersonId(contractOurSignedSubject.getCustomer());
                }
                setMilestone(contractMilestone, marketContract);
            } else {
                //子里程碑加同级
                contractMilestone.setParentId(parentId);
                ContractMilestone parentContractMilestone = this.getById(parentId);
                setChildMilestone(contractMilestone, parentContractMilestone);
            }
        } else if (level.equals(3) && nodeType.equals(2)) {
            //里程碑加子级
            ContractMilestone parentContractMilestone = this.getById(id);
            contractMilestone.setParentId(id);
            setChildMilestone(contractMilestone, parentContractMilestone);
        } else {
            //子里程碑加同级
            ContractMilestone son = this.getById(id);
            String parentId = son.getParentId();
            contractMilestone.setParentId(parentId);
            ContractMilestone parentContractMilestone = this.getById(parentId);
            setChildMilestone(contractMilestone, parentContractMilestone);
        }
        String contractType = marketContract.getContractType();
        if ("frameContract".equals(contractType)) {
            contractMilestone.setDateType("expect_accept_date");
        } else {
            contractMilestone.setDateType("plan_accept_date");//默认合同约定日期
        }

//        contractMilestone.setIncomeType("process_money");//默认进度款
        contractMilestone.setMileIncomeType("process_money");//默认进度款
        contractMilestone.setBusinessIncomeType("nuclear_nergy");//默认核能
        contractMilestone.setIsProvisionalEstimate(0);
        contractMilestone.setNumber("-");
        contractMilestone.setTaxRate(BigDecimal.valueOf(6));
        contractMilestone.setMilestoneProvisionalEstimateAmt(BigDecimal.ZERO);
        boolean save = this.save(contractMilestone);
        if (save) {
            return contractMilestone;
        } else {
            return null;
        }

    }

    private void setChildMilestone(ContractMilestone contractMilestone, ContractMilestone parentContractMilestone) {
        contractMilestone.setMilestoneName("新增子里程碑");
        contractMilestone.setCurrency(parentContractMilestone.getCurrency());
        contractMilestone.setMilestoneType("0");
        contractMilestone.setContractId(parentContractMilestone.getContractId());
        String costBusType = parentContractMilestone.getCostBusType();
        contractMilestone.setCostBusType(costBusType);
        contractMilestone.setOfficeLeader(parentContractMilestone.getOfficeLeader());
        contractMilestone.setOfficeDept(parentContractMilestone.getOfficeDept());

        //对于二级里程碑，新增里程碑时，默认带出父级的客户
        String cusPersonId = parentContractMilestone.getCusPersonId();
        if (ObjectUtil.isNotEmpty(cusPersonId)) {
            contractMilestone.setCusPersonId(cusPersonId);
        }
        if (ObjectUtil.isNotEmpty(parentContractMilestone.getTechRspUser())) {
            //如果有技术负责人，新增的时候要把所级和承担部门带上
            String techRspUser = parentContractMilestone.getTechRspUser();
            SimpleUser simplerUsers = userRedisHelper.getSimplerUser(CurrentUserHelper.getOrgId(), techRspUser);
            String orgId = simplerUsers.getOrgId();//承担部门
            String deptId = simplerUsers.getDeptId();//所级
            contractMilestone.setUndertDept(orgId);
            contractMilestone.setOfficeDept(deptId);
            contractMilestone.setTechRspUser(techRspUser);
            contractMilestone.setTechRspUserName(parentContractMilestone.getTechRspUserName());
        }
        if (ObjectUtil.isNotEmpty(parentContractMilestone.getBusRspUser())) {
            contractMilestone.setBusRspUser(parentContractMilestone.getBusRspUser());
            contractMilestone.setBusRspUserName(parentContractMilestone.getBusRspUserName());
        }
    }

    private void setMilestone(ContractMilestone contractMilestone, MarketContract marketContract) {
        contractMilestone.setMilestoneName("新增里程碑");
        contractMilestone.setCurrency("CNY");//默认人民币
        contractMilestone.setMilestoneType("1");
        // contractMilestone.setCostBusType("ZZ");//默认综合其他
        LambdaQueryWrapperX<ContractOurSignedSubject> contractOurSignedSubjectLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        if (ObjectUtil.isNotEmpty(marketContract.getTechRspUser())) {
            //如果有技术负责人，新增的时候要把所级和承担部门带上
            String techRspUser = marketContract.getTechRspUser();
            UserPartTimeVO userDeptInfo = userDeptBo.getUserDeptInfo(techRspUser);
            String orgId = userDeptInfo.getOrgId();//承担部门
            String deptId = userDeptInfo.getDeptId();//所级
            contractMilestone.setUndertDept(orgId);
            contractMilestone.setOfficeDept(deptId);
            contractMilestone.setTechRspUser(techRspUser);
            contractMilestone.setTechRspUserName(marketContract.getTechRspUserName());
            String officeLeader = "";
            //要把所级负责人也加上 取值逻辑是单据技术负责人的所级领导
            List<DeptLeaderRelationVO> deptLeaderRelationVOS = deptLeaderHelper.getDeptLeaderRelationByDeptId(CurrentUserHelper.getOrgId(), deptId);
            for (DeptLeaderRelationVO deptLeaderRelationVO : deptLeaderRelationVOS) {
                String type = deptLeaderRelationVO.getType();
                if ("main".equals(type)) {
                    officeLeader = deptLeaderRelationVO.getUserId();
                    break;
                }
            }
            contractMilestone.setOfficeLeader(officeLeader);

        }

        String busRspUser = marketContract.getCommerceRspUser();
        contractMilestone.setBusRspUser(busRspUser);
        contractMilestone.setBusRspUserName(marketContract.getCommerceRspUserName());
    }

    /**
     * 复制
     *
     * @param mileStoneTreeVo
     * @return
     * @throws Exception
     */
    @Override
    public ContractMilestone copy(MileStoneTreeVo mileStoneTreeVo) throws Exception {
        String id = mileStoneTreeVo.getId();
        ContractMilestone contractMilestone = this.getById(id);
        contractMilestone.setId(new ContractMilestone().getId());
        contractMilestone.setCreatorId(CurrentUserHelper.getCurrentUserId());
        contractMilestone.setCreateTime(new Date());
        this.save(contractMilestone);
        return contractMilestone;
    }

    /**
     * 获取当前合同的所有的所级
     *
     * @param searchVO
     * @return
     * @throws Exception
     */
    @Override
    public List<SimpleDeptVO> getOfficeDept(MileStoneTreeVo searchVO) throws Exception {
        String contractId = searchVO.getContractId();
        if (ObjectUtil.isEmpty(contractId)) {
            //获取所有的所级
            List<SimpleDeptVO> simpleDeptByIds = deptRedisHelper.getAllSimpleDept();
            List<SimpleDeptVO> simpleDeptVOS = new ArrayList<>();
            for (SimpleDeptVO simpleDeptById : simpleDeptByIds) {
                String type = simpleDeptById.getType();
                if (ObjectUtil.isNotEmpty(type) && type.equals("30")) ;
                {
                    simpleDeptVOS.add(simpleDeptById);
                }
            }
            return simpleDeptVOS;
        }
        ArrayList<String> contractIds = new ArrayList<>();
        contractIds.add(contractId);
        //获取所有的子订单
        LambdaQueryWrapperX<MarketContract> marketContractLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        marketContractLambdaQueryWrapperX.eq(MarketContract::getFrameContractId, contractId);
        List<MarketContract> marketContracts = marketContractService.list(marketContractLambdaQueryWrapperX);
        if (ObjectUtil.isNotEmpty(marketContracts)) {
            for (MarketContract marketContract : marketContracts) {
                String id = marketContract.getId();
                contractIds.add(id);
            }
        }
        //获取所有的里程碑（包括子里程碑）
        LambdaQueryWrapperX<ContractMilestone> contractMilestoneLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        contractMilestoneLambdaQueryWrapperX.in(ContractMilestone::getContractId, contractIds);
        if (!CollectionUtils.isEmpty(searchVO.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchVO.getSearchConditions(), contractMilestoneLambdaQueryWrapperX);
        }
        List<ContractMilestone> contractMilestones = this.list(contractMilestoneLambdaQueryWrapperX);
        HashSet<String> officeDeptIds = new HashSet<>();
        for (ContractMilestone contractMilestone : contractMilestones) {
            String officeDept = contractMilestone.getOfficeDept();
            officeDeptIds.add(officeDept);
        }
        List<String> officeDepts = new ArrayList<>(officeDeptIds);
        List<SimpleDeptVO> simpleDeptByIds = deptRedisHelper.getSimpleDeptByIds(officeDepts);
        return simpleDeptByIds;
    }

    /**
     * 商城子订单新建一个空的里程碑
     *
     * @param mileStoneTreeVo
     * @return
     * @throws Exception
     */
    @Override
    public ContractMilestone createNewProjectOrder(MileStoneTreeVo mileStoneTreeVo) throws Exception {
        Integer nodeType = mileStoneTreeVo.getNodeType();
        Integer level = mileStoneTreeVo.getLevel();
        String id = mileStoneTreeVo.getId();
        ContractMilestone contractMilestone = new ContractMilestone();
        String contractId = mileStoneTreeVo.getContractId();
        MarketContract marketContract = marketContractService.getById(contractId); //商城子订单
        if (ObjectUtil.isEmpty(level)) {
            //新增直属合同里程碑
            contractMilestone.setContractId(contractId);
            // 对一级里程碑，新增里程碑时，默认带出关联框架合同的客户；
            String custPersonId = marketContract.getCustPersonId();
            contractMilestone.setCusPersonId(marketContract.getCustPersonId());
            setMilestone(contractMilestone, marketContract);
            //获取「订单总金额（含税含费）」作为金额
            LambdaQueryWrapperX<ProjectInvoice> projectInvoiceLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            projectInvoiceLambdaQueryWrapperX.eq(ProjectInvoice::getOrderNumber, contractId);
            List<ProjectInvoice> list = projectInvoiceService.list(projectInvoiceLambdaQueryWrapperX);
            if (ObjectUtil.isNotEmpty(list)) {
                ProjectInvoice projectInvoice = list.get(0);
                contractMilestone.setMilestoneAmt(projectInvoice.getTotalOrderAmountTax());
            }
            //delivery_time 到货时间作为验收日期
            ProjectOrderVO projectOrderVO = projectOrderService.getByNumber(CurrentUserHelper.getOrgId(), contractId);
            if (ObjectUtil.isNotEmpty(projectOrderVO)) {
                String deliveryTime = projectOrderVO.getDeliveryTime();
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH::mm::ss");
                if (StringUtils.isNotBlank(deliveryTime)) {
                    Date parse = simpleDateFormat.parse(deliveryTime);
                    contractMilestone.setPlanAcceptDate(parse);
                }
            }
        } else if (level.equals(2) && nodeType.equals(2)) {
            //里程碑加子级
            ContractMilestone parentContractMilestone = this.getById(id);
            contractMilestone.setParentId(id);
            contractMilestone.setPlanAcceptDate(parentContractMilestone.getPlanAcceptDate());
            setChildMilestone(contractMilestone, parentContractMilestone);
        } else if (level.equals(2) && nodeType.equals(1)) {
            //里程碑加同级
            ContractMilestone self = this.getById(id);
            contractMilestone.setContractId(self.getContractId());
            // 对一级里程碑，新增里程碑时，默认带出合同的客户；
            String custPersonId = marketContract.getCustPersonId();
            contractMilestone.setCusPersonId(marketContract.getCustPersonId());
            setMilestone(contractMilestone, marketContract);
            //获取「订单总金额（含税含费）」作为金额
            LambdaQueryWrapperX<ProjectInvoice> projectInvoiceLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            projectInvoiceLambdaQueryWrapperX.eq(ProjectInvoice::getOrderNumber, contractId);
            List<ProjectInvoice> list = projectInvoiceService.list(projectInvoiceLambdaQueryWrapperX);
            if (ObjectUtil.isNotEmpty(list)) {
                ProjectInvoice projectInvoice = list.get(0);
                contractMilestone.setMilestoneAmt(projectInvoice.getTotalOrderAmountTax());
            }
            //delivery_time 到货时间作为验收日期
            ProjectOrderVO projectOrderVO = projectOrderService.getByNumber(CurrentUserHelper.getOrgId(), contractId);
            if (ObjectUtil.isNotEmpty(projectOrderVO)) {
                String deliveryTime = projectOrderVO.getDeliveryTime();
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH::mm::ss");
                if (StringUtils.isNotBlank(deliveryTime)) {
                    Date parse = simpleDateFormat.parse(deliveryTime);
                    contractMilestone.setPlanAcceptDate(parse);
                }
            }
        } else if (level.equals(3) && nodeType.equals(1)) {
            ContractMilestone self = this.getById(id);
            String parentId = self.getParentId();
            if (ObjectUtil.isEmpty(parentId)) {
                //里程碑加同级
                contractMilestone.setContractId(self.getContractId());
                // 对一级里程碑，新增里程碑时，默认带出合同的客户；
                String custPersonId = marketContract.getCustPersonId();
                contractMilestone.setCusPersonId(marketContract.getCustPersonId());
                setMilestone(contractMilestone, marketContract);
                //获取「订单总金额（含税含费）」作为金额
                LambdaQueryWrapperX<ProjectInvoice> projectInvoiceLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
                projectInvoiceLambdaQueryWrapperX.eq(ProjectInvoice::getOrderNumber, contractId);
                List<ProjectInvoice> list = projectInvoiceService.list(projectInvoiceLambdaQueryWrapperX);
                if (ObjectUtil.isNotEmpty(list)) {
                    ProjectInvoice projectInvoice = list.get(0);
                    contractMilestone.setMilestoneAmt(projectInvoice.getTotalOrderAmountTax());
                }
                //delivery_time 到货时间作为验收日期
                ProjectOrderVO projectOrderVO = projectOrderService.getByNumber(CurrentUserHelper.getOrgId(), contractId);
                if (ObjectUtil.isNotEmpty(projectOrderVO)) {
                    String deliveryTime = projectOrderVO.getDeliveryTime();
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH::mm::ss");
                    if (StringUtils.isNotBlank(deliveryTime)) {
                        Date parse = simpleDateFormat.parse(deliveryTime);
                        contractMilestone.setPlanAcceptDate(parse);
                    }
                }
            } else {
                //子里程碑加同级
                contractMilestone.setParentId(parentId);
                ContractMilestone parentContractMilestone = this.getById(parentId);
                contractMilestone.setPlanAcceptDate(parentContractMilestone.getPlanAcceptDate());
                setChildMilestone(contractMilestone, parentContractMilestone);
            }

        } else if (level.equals(3) && nodeType.equals(2)) {
            //里程碑加子级
            ContractMilestone parentContractMilestone = this.getById(id);
            contractMilestone.setParentId(id);
            contractMilestone.setPlanAcceptDate(parentContractMilestone.getPlanAcceptDate());
            setChildMilestone(contractMilestone, parentContractMilestone);
        } else {
            //子里程碑加同级
            ContractMilestone son = this.getById(id);
            String parentId = son.getParentId();
            contractMilestone.setParentId(parentId);
            ContractMilestone parentContractMilestone = this.getById(parentId);
            contractMilestone.setPlanAcceptDate(parentContractMilestone.getPlanAcceptDate());
            setChildMilestone(contractMilestone, parentContractMilestone);
        }
//        contractMilestone.setAmmountType("milestone_amt");//默认合同约定金额
        contractMilestone.setDateType("plan_accept_date");//默认合同约定日期
        contractMilestone.setIncomeType("process_money");//默认进度款
        contractMilestone.setBusinessIncomeType("nuclear_nergy");//默认核能
        contractMilestone.setNumber("-");
        contractMilestone.setIsProvisionalEstimate(0);
        contractMilestone.setTaxRate(BigDecimal.valueOf(6));
        contractMilestone.setMilestoneProvisionalEstimateAmt(BigDecimal.ZERO);
        boolean save = this.save(contractMilestone);
        if (save) {
            return contractMilestone;
        } else {
            return null;
        }
    }

    /**
     * 框架子订单新建一个空的里程碑
     *
     * @param mileStoneTreeVo
     * @return
     * @throws Exception
     */
    @Override
    public ContractMilestone createNewFrame(MileStoneTreeVo mileStoneTreeVo) throws Exception {
        Integer nodeType = mileStoneTreeVo.getNodeType();
        Integer level = mileStoneTreeVo.getLevel();
        String id = mileStoneTreeVo.getId();
        ContractMilestone contractMilestone = new ContractMilestone();
        String contractId = mileStoneTreeVo.getContractId();
        MarketContract marketContract = marketContractService.getById(contractId); //合同
        if (ObjectUtil.isEmpty(level)) {
            //新增直属合同里程碑
            contractMilestone.setContractId(contractId);
            // 对一级里程碑，新增里程碑时，默认带出框架子订单的客户；
            LambdaQueryWrapperX<ContractOurSignedSubject> contractOurSignedSubjectLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            contractOurSignedSubjectLambdaQueryWrapperX.eq(ContractOurSignedSubject::getContractId, contractId);
            List<ContractOurSignedSubject> contractOurSignedSubjects = contractOurSignedSubjectService.list(contractOurSignedSubjectLambdaQueryWrapperX);
            if (ObjectUtil.isNotEmpty(contractOurSignedSubjects)) {
                ContractOurSignedSubject contractOurSignedSubject = contractOurSignedSubjects.get(0);
                contractMilestone.setCusPersonId(contractOurSignedSubject.getCustomer());
            }
            setMilestone(contractMilestone, marketContract);
        } else if (level.equals(2) && nodeType.equals(1)) {
            //里程碑加同级
            ContractMilestone self = this.getById(id);
            contractMilestone.setContractId(self.getContractId());
            // 对一级里程碑，新增里程碑时，默认带出合同的客户；
            LambdaQueryWrapperX<ContractOurSignedSubject> contractOurSignedSubjectLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            contractOurSignedSubjectLambdaQueryWrapperX.eq(ContractOurSignedSubject::getContractId, contractId);
            List<ContractOurSignedSubject> contractOurSignedSubjects = contractOurSignedSubjectService.list(contractOurSignedSubjectLambdaQueryWrapperX);
            if (ObjectUtil.isNotEmpty(contractOurSignedSubjects)) {
                ContractOurSignedSubject contractOurSignedSubject = contractOurSignedSubjects.get(0);
                contractMilestone.setCusPersonId(contractOurSignedSubject.getCustomer());
            }
            setMilestone(contractMilestone, marketContract);
        } else if (level.equals(2) && nodeType.equals(2)) {
            ContractMilestone parentContractMilestone = this.getById(id);
            contractMilestone.setParentId(id);
            setChildMilestone(contractMilestone, parentContractMilestone);
        } else if (level.equals(3) && nodeType.equals(1)) {
            ContractMilestone self = this.getById(id);
            String parentId = self.getParentId();
            if (ObjectUtil.isEmpty(parentId)) {
                //里程碑加同级
                contractMilestone.setContractId(self.getContractId());
                // 对一级里程碑，新增里程碑时，默认带出合同的客户；
                LambdaQueryWrapperX<ContractOurSignedSubject> contractOurSignedSubjectLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
                contractOurSignedSubjectLambdaQueryWrapperX.eq(ContractOurSignedSubject::getContractId, contractId);
                List<ContractOurSignedSubject> contractOurSignedSubjects = contractOurSignedSubjectService.list(contractOurSignedSubjectLambdaQueryWrapperX);
                if (ObjectUtil.isNotEmpty(contractOurSignedSubjects)) {
                    ContractOurSignedSubject contractOurSignedSubject = contractOurSignedSubjects.get(0);
                    contractMilestone.setCusPersonId(contractOurSignedSubject.getCustomer());
                }
                setMilestone(contractMilestone, marketContract);
            } else {
                //子里程碑加同级
                contractMilestone.setParentId(parentId);
                ContractMilestone parentContractMilestone = this.getById(parentId);
                setChildMilestone(contractMilestone, parentContractMilestone);
            }


        } else if (level.equals(3) && nodeType.equals(2)) {
            //里程碑加子级
            ContractMilestone parentContractMilestone = this.getById(id);
            contractMilestone.setParentId(id);
            setChildMilestone(contractMilestone, parentContractMilestone);
        } else {
            //子里程碑加同级
            ContractMilestone son = this.getById(id);
            String parentId = son.getParentId();
            contractMilestone.setParentId(parentId);
            ContractMilestone parentContractMilestone = this.getById(parentId);
            setChildMilestone(contractMilestone, parentContractMilestone);
        }
        contractMilestone.setMilestoneAmt(BigDecimal.ZERO);
//        contractMilestone.setAmmountType("except_acceptance_amt");//默认合同约定金额
        contractMilestone.setDateType("plan_accept_date");//默认合同约定日期
        contractMilestone.setIncomeType("process_money");//默认进度款
        contractMilestone.setBusinessIncomeType("nuclear_nergy");//默认核能
        contractMilestone.setNumber("-");
        contractMilestone.setIsProvisionalEstimate(0);
        contractMilestone.setTaxRate(BigDecimal.valueOf(6));
        contractMilestone.setMilestoneProvisionalEstimateAmt(BigDecimal.ZERO);
        boolean save = this.save(contractMilestone);
        if (save) {
            return contractMilestone;
        } else {
            return null;
        }
    }

    @Override
    public Boolean getContractMilestoneMoney(String contractId) throws Exception {
          /* 1. 对合同类型为框架合同，
        2. 或合同类型为子合同，且「子合同类型」为框架的情况，
        3. 或合同类型为框架下子订单，且「子合同类型」为框架的情况；
        这三种情况不去进行判断 */
//        MarketContract contract = marketContractService.getById(contractId);
//        String contractType = contract.getContractType();
//        //1. 对合同类型为框架合同，
//        if (contractType.equals(MarketContractTypeEnums.FRAME_CONTRACT.getCode())) {
//            return true;
//        }
//        // 合同类型为子合同，且「子合同类型」为框架的情况，
//        if (contractType.equals(MarketContractTypeEnums.SON_CONTRACT.getCode())) {
//            String subOrderType = contract.getSubOrderType();
//            if (ObjectUtil.isNotEmpty(subOrderType) && subOrderType.equals("frame")) {
//                return true;
//            }
//        }
//        //或合同类型为框架下子订单，且「子合同类型」为框架的情况；
//        if (contractType.equals(MarketContractTypeEnums.SUB_ORDER_CONTRACT.getCode())) {
//            String subOrderType = contract.getSubOrderType();
//            if (ObjectUtil.isNotEmpty(subOrderType) && subOrderType.equals("frame")) {
//                return true;
//            }
//        }
        //获取该合同下所有的里程碑（包括子里程碑） 先拿到他所有的子订单
        ArrayList<String> contractIds = new ArrayList<>();
        contractIds.add(contractId);
        MarketContract self = marketContractService.getById(contractId);
        if (ObjectUtil.isEmpty(self)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "该合同信息不存在，请核验");
        }
        BigDecimal selfMilestoneAmt = self.getContractAmt();
        //获取所有的子订单
        LambdaQueryWrapperX<MarketContract> marketContractLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        marketContractLambdaQueryWrapperX.eq(MarketContract::getFrameContractId, contractId);
        List<MarketContract> marketContracts = marketContractService.list(marketContractLambdaQueryWrapperX);
        if (ObjectUtil.isNotEmpty(marketContracts)) {
            for (MarketContract marketContract : marketContracts) {
                String id = marketContract.getId();
                contractIds.add(id);
            }
        }
        //获取所有的里程碑（包括子里程碑）
        LambdaQueryWrapperX<ContractMilestone> contractMilestoneLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        if (ObjectUtil.isNotEmpty(contractIds)) {
            contractMilestoneLambdaQueryWrapperX.in(ContractMilestone::getContractId, contractIds);
        }
        List<ContractMilestone> contractMilestones = this.list(contractMilestoneLambdaQueryWrapperX);

        // 把所有的里程碑拿出来
        List<ContractMilestone> parent = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(contractMilestones)) {
            parent = contractMilestones.stream().filter(item -> item.getParentId() == null).collect(Collectors.toList());
        }
        //把这些父里程碑再次分成有子里程碑的和没有子里程碑的
        // Step 1: 提取有子里程碑的里程碑
        Map<String, ContractMilestone> parentIdToMilestone = new HashMap<>();
        for (ContractMilestone milestone : parent) {
            if (milestone != null && milestone.getId() != null) {
                parentIdToMilestone.put(milestone.getId(), milestone);
            }
        }

        Set<ContractMilestone> hasSon = new HashSet<>();
        for (ContractMilestone contractMilestone : contractMilestones) {
            String parentId = contractMilestone.getParentId();
            if (parentId != null && parentIdToMilestone.containsKey(parentId)) {
                hasSon.add(parentIdToMilestone.get(parentId));
            }
        }

        // Step 2: 提取没有子里程碑的里程碑
        Set<String> hasSonIds = hasSon.stream().map(ContractMilestone::getId).collect(Collectors.toSet());
        List<ContractMilestone> noSon = parent.stream()
                .filter(item -> item != null && !hasSonIds.contains(item.getId()))
                .collect(Collectors.toList());

        // Step 3: 计算里程碑金额
        BigDecimal sum = BigDecimal.ZERO;

        // 获取该合同下所有的子里程碑
        List<ContractMilestone> sons = contractMilestones.stream()
                .filter(item -> item != null && item.getParentId() != null)
                .collect(Collectors.toList());

        // 计算有子里程碑的里程碑的所有子里程碑合同约定金额之和
        for (ContractMilestone son : sons) {
            BigDecimal milestoneAmt = son.getMilestoneAmt();
            if (milestoneAmt != null) {
                sum = sum.add(milestoneAmt);
            }
        }

        // 计算没有子里程碑的里程碑的合同约定金额之和
        for (ContractMilestone contractMilestone : noSon) {
            BigDecimal milestoneAmt = contractMilestone.getMilestoneAmt();
            if (milestoneAmt != null) {
                sum = sum.add(milestoneAmt);
            }
        }

        // Step 4: 比较总金额
        if (selfMilestoneAmt != null && sum != null) {
            return selfMilestoneAmt.compareTo(sum) == 0;
        }

        return false;
    }

    @Override
    public Boolean checkMilestoneAmt(String contractId) throws Exception {

        HashSet<String> nameSet = new HashSet<>();
        LambdaQueryWrapper<ContractMilestone> contractMilestoneLambdaQueryWrapper = new LambdaQueryWrapper<>();
        contractMilestoneLambdaQueryWrapper.eq(ContractMilestone::getContractId, contractId);
        List<ContractMilestone> contractMilestonesCheck = this.list(contractMilestoneLambdaQueryWrapper);
        //检查里程碑是否有重名
        if (NameDuplicateChecker(contractMilestonesCheck, nameSet)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "该合同下存在里程碑重名，请修改后再发起流程。");
        }
        //获取该合同下所有的里程碑（包括子里程碑） 先拿到他所有的子订单
        ArrayList<String> contractIds = new ArrayList<>();
        contractIds.add(contractId);
        //获取所有的子订单
        LambdaQueryWrapperX<MarketContract> marketContractLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        marketContractLambdaQueryWrapperX.eq(MarketContract::getFrameContractId, contractId);
        List<MarketContract> marketContracts = marketContractService.list(marketContractLambdaQueryWrapperX);
        if (ObjectUtil.isNotEmpty(marketContracts)) {
            for (MarketContract marketContract : marketContracts) {
                String id = marketContract.getId();
                contractIds.add(id);
            }
        }
        //获取所有的里程碑（包括子里程碑）
        LambdaQueryWrapperX<ContractMilestone> contractMilestoneLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        contractMilestoneLambdaQueryWrapperX.in(ContractMilestone::getContractId, contractIds);
        List<ContractMilestone> contractMilestones = this.list(contractMilestoneLambdaQueryWrapperX);
        for (ContractMilestone contractMilestone : contractMilestonesCheck) {
            String milestoneName = contractMilestone.getMilestoneName();
            String ammountType = contractMilestone.getAmmountType();
            if (ObjectUtil.isEmpty(ammountType)) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在金额类型为空的里程碑，请检查:" + milestoneName);
            }
            String costBusType = contractMilestone.getCostBusType();
            if (ObjectUtil.isEmpty(costBusType)) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在业务分类为空的里程碑，请检查:" + milestoneName);
            }
            BigDecimal taxRate = contractMilestone.getTaxRate();
            if (ObjectUtil.isEmpty(taxRate)) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在税率为空的里程碑，请检查:" + milestoneName);
            }
            BigDecimal milestoneAmt = contractMilestone.getMilestoneAmt();
            BigDecimal exceptAcceptanceAmt = contractMilestone.getExceptAcceptanceAmt();
            if (ObjectUtil.isEmpty(milestoneAmt) && ObjectUtil.isEmpty(exceptAcceptanceAmt)) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在里程碑金额为空的里程碑，请检查:" + milestoneName);
            }
            String dateType = contractMilestone.getDateType();
            if (ObjectUtil.isEmpty(dateType)) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在日期类型为空的里程碑，请检查:" + milestoneName);
            }
            Date expectAcceptDate = contractMilestone.getExpectAcceptDate();
            Date planAcceptDate = contractMilestone.getPlanAcceptDate();
            if (ObjectUtil.isEmpty(expectAcceptDate) && ObjectUtil.isEmpty(planAcceptDate)) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在验收日期为空的里程碑，请检查:" + milestoneName);
            }
            String mileIncomeType = contractMilestone.getMileIncomeType();
            if (ObjectUtil.isEmpty(mileIncomeType)) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在收入类型为空的里程碑，请检查:" + milestoneName);
            }
            String busRspUser = contractMilestone.getBusRspUser();
            if (ObjectUtil.isEmpty(busRspUser)) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在商务负责人为空的里程碑，请检查:" + milestoneName);
            }
            String techRspUser = contractMilestone.getTechRspUser();
            if (ObjectUtil.isEmpty(techRspUser)) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在技术负责人为空的里程碑，请检查:" + milestoneName);
            }
            String cusPersonId = contractMilestone.getCusPersonId();
            if (ObjectUtil.isEmpty(cusPersonId)) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在客户为空的里程碑，请检查:" + milestoneName);
            }
        }

        // 把所有的里程碑拿出来
        List<ContractMilestone> parent = contractMilestones.stream().filter(item -> item.getParentId() == null).collect(Collectors.toList());//里程碑
        List<ContractMilestone> son = contractMilestones.stream().filter(item -> item.getParentId() != null).collect(Collectors.toList());//子里程碑
        //把这些父里程碑再次分成有子里程碑的和没有子里程碑的
        Set<ContractMilestone> hasSon = new HashSet<>(); //有子里程碑的里程碑
        for (ContractMilestone contractMilestone : contractMilestones) {
            String parentId = contractMilestone.getParentId();
            if (ObjectUtil.isNotEmpty(parentId)) {
                for (ContractMilestone milestone : parent) {
                    String id = milestone.getId();
                    if (id.equals(parentId))
                        hasSon.add(milestone);
                }
            }
        }

        for (ContractMilestone contractMilestone : hasSon) {
            BigDecimal sonMilestoneAmt = BigDecimal.ZERO;
            BigDecimal exceptAcceptanceAmt = BigDecimal.ZERO;
            String id = contractMilestone.getId();
            String ammountType = contractMilestone.getAmmountType();
            if (ObjectUtil.isNotEmpty(son)) {
                if ("milestone_amt".equals(ammountType)) {
                    for (ContractMilestone milestone : son) {
                        String parentId = milestone.getParentId();
                        if (parentId.equals(id)) {
                            if (ObjectUtil.isEmpty(milestone.getMilestoneAmt())) {
                                throw new PMSException(PMSErrorCode.PMS_ERR, "里程碑金额不能为空！");
                            }
                            sonMilestoneAmt = sonMilestoneAmt.add(milestone.getMilestoneAmt());
                        }
                    }
                    if (!sonMilestoneAmt.equals(contractMilestone.getMilestoneAmt())) {
                        throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, contractMilestone.getMilestoneName() + "里程碑金额必须等于子里程碑金额之和！");
                    }
                } else {
                    for (ContractMilestone milestone : son) {
                        String parentId = milestone.getParentId();
                        if (parentId.equals(id)) {
                            exceptAcceptanceAmt = exceptAcceptanceAmt.add(milestone.getExceptAcceptanceAmt());
                        }
                    }
                    if (!exceptAcceptanceAmt.equals(contractMilestone.getExceptAcceptanceAmt())) {
                        throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, contractMilestone.getMilestoneName() + "里程碑金额必须等于子里程碑金额之和！");
                    }
                }
            }

        }


        return Boolean.TRUE;
    }

    @Override
    public Boolean checkRepeatName(String contractId) throws Exception {
        HashSet<String> nameSet = new HashSet<>();
        LambdaQueryWrapper<ContractMilestone> contractMilestoneLambdaQueryWrapper = new LambdaQueryWrapper<>();
        contractMilestoneLambdaQueryWrapper.eq(ContractMilestone::getContractId, contractId);
        List<ContractMilestone> contractMilestonesCheck = this.list(contractMilestoneLambdaQueryWrapper);
        for (ContractMilestone contractMilestone : contractMilestonesCheck) {
            if(ObjectUtil.equals(MarketContractMilestoneStatusEnum.CREATED.getStatus(),contractMilestone.getStatus())) {
                String milestoneName = contractMilestone.getMilestoneName();
                String ammountType = contractMilestone.getAmmountType();
                if (ObjectUtil.isEmpty(ammountType)) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在金额类型为空的里程碑，请检查:" + milestoneName);
                }
                String costBusType = contractMilestone.getCostBusType();
                if (ObjectUtil.isEmpty(costBusType)) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在业务分类为空的里程碑，请检查:" + milestoneName);
                }
                BigDecimal taxRate = contractMilestone.getTaxRate();
                if (ObjectUtil.isEmpty(taxRate)) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在税率为空的里程碑，请检查:" + milestoneName);
                }
                BigDecimal milestoneAmt = contractMilestone.getMilestoneAmt();
                BigDecimal exceptAcceptanceAmt = contractMilestone.getExceptAcceptanceAmt();
                if (ObjectUtil.isEmpty(milestoneAmt) && ObjectUtil.isEmpty(exceptAcceptanceAmt)) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在里程碑金额为空的里程碑，请检查:" + milestoneName);
                }
                String dateType = contractMilestone.getDateType();
                if (ObjectUtil.isEmpty(dateType)) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在日期类型为空的里程碑，请检查:" + milestoneName);
                }
                Date expectAcceptDate = contractMilestone.getExpectAcceptDate();
                Date planAcceptDate = contractMilestone.getPlanAcceptDate();
                if (ObjectUtil.isEmpty(expectAcceptDate) && ObjectUtil.isEmpty(planAcceptDate)) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在验收日期为空的里程碑，请检查:" + milestoneName);
                }
                String mileIncomeType = contractMilestone.getMileIncomeType();
                if (ObjectUtil.isEmpty(mileIncomeType)) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在收入类型为空的里程碑，请检查:" + milestoneName);
                }
                String busRspUser = contractMilestone.getBusRspUser();
                if (ObjectUtil.isEmpty(busRspUser)) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在商务负责人为空的里程碑，请检查:" + milestoneName);
                }
                String techRspUser = contractMilestone.getTechRspUser();
                if (ObjectUtil.isEmpty(techRspUser)) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在技术负责人为空的里程碑，请检查:" + milestoneName);
                }
                String cusPersonId = contractMilestone.getCusPersonId();
                if (ObjectUtil.isEmpty(cusPersonId)) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在客户为空的里程碑，请检查:" + milestoneName);
                }
            }
        }
        //检查里程碑是否有重名
        if (NameDuplicateChecker(contractMilestonesCheck, nameSet)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "该合同下存在里程碑重名，请修改后再发起流程。");
        }
        return true;
    }

    @Override
    public Boolean checkRepeatNameByContractMilestoneId(String mileStoneId) throws Exception {
        HashSet<String> nameSet = new HashSet<>();
        LambdaQueryWrapper<ContractMilestone> contractMilestoneLambdaQueryWrapper = new LambdaQueryWrapper<>();
        contractMilestoneLambdaQueryWrapper.eq(ContractMilestone::getId, mileStoneId);
        List<ContractMilestone> contractMilestonesCheck = this.list(contractMilestoneLambdaQueryWrapper);
        ContractMilestone contractMilestone1 = contractMilestonesCheck.get(0);
        for (ContractMilestone contractMilestone : contractMilestonesCheck) {
            if(ObjectUtil.equals(MarketContractMilestoneStatusEnum.CREATED.getStatus(),contractMilestone.getStatus())) {
                String milestoneName = contractMilestone.getMilestoneName();
                String ammountType = contractMilestone.getAmmountType();
                if (ObjectUtil.isEmpty(ammountType)) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在金额类型为空的里程碑，请检查:" + milestoneName);
                }
                String costBusType = contractMilestone.getCostBusType();
                if (ObjectUtil.isEmpty(costBusType)) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在业务分类为空的里程碑，请检查:" + milestoneName);
                }
                BigDecimal taxRate = contractMilestone.getTaxRate();
                if (ObjectUtil.isEmpty(taxRate)) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在税率为空的里程碑，请检查:" + milestoneName);
                }
                BigDecimal milestoneAmt = contractMilestone.getMilestoneAmt();
                BigDecimal exceptAcceptanceAmt = contractMilestone.getExceptAcceptanceAmt();
                if (ObjectUtil.isEmpty(milestoneAmt) && ObjectUtil.isEmpty(exceptAcceptanceAmt)) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在里程碑金额为空的里程碑，请检查:" + milestoneName);
                }
                String dateType = contractMilestone.getDateType();
                if (ObjectUtil.isEmpty(dateType)) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在日期类型为空的里程碑，请检查:" + milestoneName);
                }
                Date expectAcceptDate = contractMilestone.getExpectAcceptDate();
                Date planAcceptDate = contractMilestone.getPlanAcceptDate();
                if (ObjectUtil.isEmpty(expectAcceptDate) && ObjectUtil.isEmpty(planAcceptDate)) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在验收日期为空的里程碑，请检查:" + milestoneName);
                }
                String mileIncomeType = contractMilestone.getMileIncomeType();
                if (ObjectUtil.isEmpty(mileIncomeType)) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在收入类型为空的里程碑，请检查:" + milestoneName);
                }
                String busRspUser = contractMilestone.getBusRspUser();
                if (ObjectUtil.isEmpty(busRspUser)) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在商务负责人为空的里程碑，请检查:" + milestoneName);
                }
                String techRspUser = contractMilestone.getTechRspUser();
                if (ObjectUtil.isEmpty(techRspUser)) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在技术负责人为空的里程碑，请检查:" + milestoneName);
                }
                String cusPersonId = contractMilestone.getCusPersonId();
                if (ObjectUtil.isEmpty(cusPersonId)) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在客户为空的里程碑，请检查:" + milestoneName);
                }
            }
        }
        //检查里程碑是否有重名
        if (NameDuplicateChecker(contractMilestonesCheck, nameSet)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "该合同下存在里程碑重名，请修改后再发起流程。");
        }
        //发起流程前 把里程碑填写的都为初始预估日期/金额，则计划验收日期/金额在里程碑状态为【进行中】时全部从填写的初始预估日期/金额获取
        if (ObjectUtil.isNotEmpty(contractMilestone1.getPlanAcceptDate())) {
            contractMilestone1.setPlannedAcceptanceDate(contractMilestone1.getPlanAcceptDate());
        } else if (ObjectUtil.isNotEmpty(contractMilestone1.getExpectAcceptDate())) {
            contractMilestone1.setPlannedAcceptanceDate(contractMilestone1.getExpectAcceptDate());
        }

        if (ObjectUtil.isNotEmpty(contractMilestone1.getMilestoneAmt()) && !(contractMilestone1.getMilestoneAmt().compareTo(BigDecimal.ZERO) == 0)) {
            contractMilestone1.setPlannedAcceptanceAmount(contractMilestone1.getMilestoneAmt());
        } else if (ObjectUtil.isNotEmpty(contractMilestone1.getExceptAcceptanceAmt()) && !(contractMilestone1.getExceptAcceptanceAmt().compareTo(BigDecimal.ZERO) == 0)) {
            contractMilestone1.setPlannedAcceptanceAmount(contractMilestone1.getExceptAcceptanceAmt());
        }
        this.updateById(contractMilestone1);
        //
        return true;
    }

    /**
     * 判断里程碑名字有没有重复的
     *
     * @param contractMilestones
     * @param nameSet
     * @return
     */
    private boolean NameDuplicateChecker(List<ContractMilestone> contractMilestones, HashSet<String> nameSet) {
        for (ContractMilestone contractMilestone : contractMilestones) {
            if (!nameSet.add(contractMilestone.getMilestoneName())) {
                return true;
            }
        }
        return false;
    }
}
