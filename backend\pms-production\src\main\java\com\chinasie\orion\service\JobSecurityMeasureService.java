package com.chinasie.orion.service;
import com.chinasie.orion.domain.entity.JobSecurityMeasure;
import com.chinasie.orion.domain.dto.JobSecurityMeasureDTO;
import com.chinasie.orion.domain.vo.JobSecurityMeasureVO;

import java.lang.String;
import java.util.List;


import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;
import java.util.Map;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * JobSecurityMeasure 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-09 15:47:47
 */
public interface JobSecurityMeasureService extends OrionBaseService<JobSecurityMeasure> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    JobSecurityMeasureVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param jobSecurityMeasureDTO
     */
    String create(JobSecurityMeasureDTO jobSecurityMeasureDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param jobSecurityMeasureDTO
     */
    Boolean edit(JobSecurityMeasureDTO jobSecurityMeasureDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<JobSecurityMeasureVO> pages(Page<JobSecurityMeasureDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<JobSecurityMeasureVO> vos) throws Exception;

    /**
     *  获取作业下的 安措信息列表
     * @param id
     * @return
     */
    List<JobSecurityMeasureVO> getListByJobId(String id);

    /**
     *  通过唯一key 获取数据列表
     * @param encryKeyList
     * @return
     */
    Map<String, List<JobSecurityMeasureVO>> getListByKeyList(List<String> encryKeyList);
}
