package com.chinasie.orion.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/23/18:10
 * @description:
 */
@Data
@ApiModel(value = "DeliverableDTO对象", description = "交付物")
public class DeliverableDTO extends RevisionClassDTO {

    @ApiModelProperty(value = "项目id")
    @NotEmpty(message = "所属项目不能为空")
    private String projectId;

    /**
     * 计划ID
     */
    @ApiModelProperty(value = "计划ID")
    @NotEmpty(message = "所属计划不能为空")
    private String planId;

    /**
     * 负责人名称
     */
    @ApiModelProperty(value = "负责人名称")
    private String principalName;

    /**
     * 负责人id
     */
    @ApiModelProperty(value = "负责人id")
    private String principalId;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 交付时间
     */
    @ApiModelProperty(value = "交付时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date deliveryTime;

    /**
     *  预计交付时间
     */
    @ApiModelProperty(value = "预计交付时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date predictDeliverTime;
    /**
     *  文档ID——document 壳
     */
    @ApiModelProperty(value = "文档ID")
    private String documentId;

}
