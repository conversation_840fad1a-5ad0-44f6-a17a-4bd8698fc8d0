<script setup lang="ts">
import {
  h, ref,
} from 'vue';
import {
  DataStatusTag, OrionTable,
} from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import { associationProject } from '/@/views/pms/api/projectApproval';
const tableRef = ref(null);
const props = defineProps<{
  projectId: string
}>();
const columns = [
  {
    title: '编号',
    dataIndex: 'projectNumber',
    width: 200,
  },
  {
    title: '名称',
    dataIndex: 'projectName',
  },

  {
    title: '类型',
    dataIndex: 'projectType',
    width: 130,
  },
  {
    title: '状态',
    dataIndex: 'dataStatus',
    width: 130,
    customRender: ({ record }) => h(DataStatusTag, { statusData: record.dataStatus }),
  },
];
const baseTableOption = {
  rowKey: 'id',
  // 是否显示工具栏默认按钮
  showToolButton: false,
  // 是否显示工具栏上的搜索
  showSmallSearch: true,
  // 工具栏搜索字段配置，string | string[] 默认 'name' , 传数组字段值则查询多个子字段
  smallSearchField: ['name', 'number'],
  rowSelection: {
    type: 'radio',
  },
  columns,
  api: () => associationProject(),
  isFilter2: false,
};
// 检查是否选中数据
const isSelectedAndGetData = () => new Promise((resolve, reject) => {
  const rows = tableRef.value.getSelectRows();
  if (rows.length > 0) {
    rows[0].projectId = props.projectId;
    resolve(rows);
  } else {
    message.warning('请选择数据');
    reject();
  }
});

defineExpose({
  isSelectedAndGetData,
});
</script>

<template>
  <div style="height: 100%;overflow: hidden;">
    <OrionTable
      ref="tableRef"
      :options="baseTableOption"
    />
  </div>
</template>

<style scoped lang="less">

</style>
