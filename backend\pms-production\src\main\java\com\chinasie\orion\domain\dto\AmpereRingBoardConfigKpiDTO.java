package com.chinasie.orion.domain.dto;

import com.chinasie.orion.domain.entity.AmpereRingBoardConfigKpi;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 22 日
 * 安质环kpi 考核DTO对象
 **/
@ApiModel(value = "AmpereRingBoardCOonfigKpiDTO 对象" ,description = "安质环kpi 考核DTO 对象")
@Data
public class AmpereRingBoardConfigKpiDTO extends ObjectDTO implements Serializable {
    /**
     * 事件等级code
     */
    @ApiModelProperty(value = "事件等级code")
    private String eventCode;

    /**
     * 事件等级
     */
    @ApiModelProperty(value = "事件等级")
    private String eventLevel;

    /**
     * 考核指标
     */
    @ApiModelProperty(value = "考核指标")
    private String kpiCode;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    private Integer sort;

    /**
     * 等级数量统计
     */
    @ApiModelProperty(value = "事件数量统计")
    private Integer eventLevelCount;

    /**
     * 考核指标移动
     */
    @ApiModelProperty(value = "考核指标移动: up 上移;down 下移;top 置顶;bottom 置底")
    private String operationType;

    /**
     * 查询年份
     */
    @ApiModelProperty(value = "查询kpi 年份")
    private Integer year;

    /**
     * 部门考核的分类
     */
    @ApiModelProperty(value = "部门考核的分类 部门：dept 项目部：project")
    private String deptScoreType;

    /**
     * 部门编码
     */
    @ApiModelProperty(value = "部门编码")
    private String deptCode;

    /**
     * 添加kpi 事件
     */
    @ApiModelProperty(value = "kpi  事件集合")
    private List<AmpereRingBoardConfigKpi> kpiList;

    /**
     * 隐患排查查询年份类型
     */
    @ApiModelProperty(value = "隐患排查查询年份类型 year 年度,month 月 ,季度 quarter ")
    private String queryCheckProblemsDateType;

    /**
     * 隐患排查查询时间
     */
    @ApiModelProperty(value = "隐患排查时间")
    private String queryCheckProblemsDate;

}
