package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.api.code.domain.vo.CodeSegmentVO;
import com.chinasie.orion.bo.*;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.*;
import com.chinasie.orion.conts.QuestionManagementEnum;
import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.dto.document.DocumentDTO;
import com.chinasie.orion.domain.dto.pas.QuestionTypeAttributeValueDTO;
import com.chinasie.orion.domain.dto.pas.TypeAttrValueDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.entity.review.ReviewEssentials;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.number.api.domain.GenerateNumberRequest;
import com.chinasie.orion.number.api.sdk.NumberApiService;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.QuestionManagementRepository;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueTreeVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.*;
import com.chinasie.orion.service.review.ReviewEssentialsService;
import com.chinasie.orion.service.review.ReviewOpinionService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.PmsAuthUtil;
import org.apache.commons.compress.utils.Lists;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/17/16:32
 * @description:
 */
@Service
public class QuestionManagementServiceImpl extends OrionBaseServiceImpl<QuestionManagementRepository, QuestionManagement> implements QuestionManagementService {

    @Resource
    private ProjectService projectService;
    @Resource
    private DictBo dictBo;
    @Resource
    private PlanToQuestionManagementService planToQuestionManagementService;
    @Resource
    private ProjectSchemeService projectSchemeService;
    @Resource
    private DocumentBo documentBo;
    @Resource
    private QuestionToRiskService questionToRiskService;
    @Resource
    private StatusBo statusBo;
    @Resource
    private DocumentService documentService;
    @Resource
    private FileInfoService fileInfoService;
    @Autowired
    private CodeBo codeBo;
    @Autowired
    private PasBo pasBo;
    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private PmsAuthUtil pmsAuthUtil;

    @Resource
    private UserBo userBo;

    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private DictRedisHelper dictRedisHelper;

    @Autowired
    private CurrentUserHelper currentUserHelper;

    @Autowired
    private NumberApiService numberApiService;
    @Autowired
    @Lazy
    private ReviewOpinionService reviewOpinionService;

    @Autowired
    private QuestionLibraryService questionLibraryService;

    @Autowired
    private ReviewEssentialsService reviewEssentialsService;

    @Autowired
    private RiskManagementService riskManagementService;

    @Resource
    private DataStatusNBO dataStatusNBO;
    private final String DATE_FORMAT = "yyyy-MM-dd";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveQuestionManagement(QuestionManagementDTO questionManagementDTO) throws Exception {

//        List<QuestionManagement> questionManagementDTOList = this.list(new LambdaQueryWrapper<>(QuestionManagement.class).
//                eq(QuestionManagement::getName, questionManagementDTO.getName()).
//                eq(QuestionManagement::getProjectId, questionManagementDTO.getProjectId()));
//        if (!CollectionUtils.isEmpty(questionManagementDTOList)) {
//            throw new PMSException(PMSErrorCode.PMS_ERROR_NAME_REPEAT);
//        }
        //风险转计划
        String riskId = null;
        if(questionManagementDTO.getIsQuestion() != null && questionManagementDTO.getIsQuestion()){
            if(!StringUtils.hasText(questionManagementDTO.getId())){
                throw new PMSException(null,"风险id不能为空");
            }
            riskId = questionManagementDTO.getId();
            questionManagementDTO.setId(null);
        }
        List<CodeSegmentVO> codeRuleList = codeBo.getCodeRuleList(ClassNameConstant.QUESTION_NAME, ClassNameConstant.NUMBER);
        if (!CollectionUtils.isEmpty(codeRuleList)) {
            String code = codeBo.getCode(codeRuleList);
            questionManagementDTO.setNumber(code);
        }
        QuestionManagement questionManagement = BeanCopyUtils.convertTo(questionManagementDTO, QuestionManagement::new);
        questionManagement.setOwnerId(questionManagement.getPrincipalId());
        this.save(questionManagement);
        String id = questionManagement.getId();
        //添加属性值
        List<TypeAttrValueDTO> typeAttrValueDTOList = questionManagementDTO.getTypeAttrValueDTOList();
        if (!CollectionUtils.isEmpty(typeAttrValueDTOList)) {
            List<QuestionTypeAttributeValueDTO> questionTypeAttributeValueDTOList = BeanCopyUtils.convertListTo(typeAttrValueDTOList, QuestionTypeAttributeValueDTO::new);
            questionTypeAttributeValueDTOList.forEach(d -> {
                d.setQuestionId(id);
                d.setTypeId(d.getTypeId());
            });
            pasBo.addQuestionTypeAttributeValue(questionTypeAttributeValueDTOList);
        }


        DocumentDTO documentDTO = new DocumentDTO();
        documentDTO.setName(questionManagementDTO.getName());
        documentDTO.setNumber(questionManagementDTO.getNumber());
        documentDTO.setClassName(DocumentClassNameConstant.Question_Document);
        String s = documentBo.insertDocument(documentDTO);
        questionManagementDTO.setId(id);
        questionManagementDTO.setDocumentId(s);
        QuestionManagement questionManagement2 = BeanCopyUtils.convertTo(questionManagementDTO, QuestionManagement::new);
        questionManagement2.setStatus(QuestionManagementEnum.COMPLETED.getCode());
        this.updateById(questionManagement2);
        //添加风险问题关系
        if(riskId != null){
            questionToRiskService.saveParam(riskId, id);
        }
        return id;
    }

    @Override
    public List<QuestionManagementVO> createBatch(List<QuestionManagementDTO> questionManagementList) throws Exception {

        if (CollectionUtil.isEmpty(questionManagementList)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_NAME_REPEAT);
        }
//        List<String> projectIds = questionManagementList.stream().map(QuestionManagementDTO::getProjectId).collect(Collectors.toList());
//        List<String> names = questionManagementList.stream().map(QuestionManagementDTO::getName).collect(Collectors.toList());
//
//        LambdaQueryWrapperX<QuestionManagement> condition = new LambdaQueryWrapperX<>(QuestionManagement.class);
//        condition.in(QuestionManagement::getProjectId, projectIds)
//                 .or()
//                 .in(QuestionManagement::getName, names);
//        long count = this.count(condition);
//        if (count > 0){
//            throw new BaseException(PMSErrorCode.PMS_ERROR_PARAMS, "同个项目下不能存在相同的问题名称");
//        }

        List<QuestionManagement> questionManagements = BeanCopyUtils.convertListTo(questionManagementList, QuestionManagement::new);
        questionManagements.forEach(item ->{
            item.setStatus(QuestionManagementEnum.COMPLETED.getCode());
        });
        this.saveBatch(questionManagements);

        // 批量获取编号
        AtomicInteger i = new AtomicInteger();
        List<GenerateNumberRequest> generateNumberList= questionManagements.stream().map(m -> {
            GenerateNumberRequest generateNumberRequest = new GenerateNumberRequest();
            generateNumberRequest.setClazzName(QuestionManagement.class.getSimpleName());
            generateNumberRequest.setDataId(m.getId());
            generateNumberRequest.setSort(i.getAndIncrement());
            return generateNumberRequest;
        }).collect(Collectors.toList());

        Map<String, String> numberMap = numberApiService.generateBatch(generateNumberList);

        this.updateBatchById(questionManagements.stream().peek(m->{
            m.setNumber(numberMap.get(m.getId()));
            m.setOwnerId(m.getPrincipalId());
        }).collect(Collectors.toList()));

        return BeanCopyUtils.convertListTo(questionManagements, QuestionManagementVO::new);
    }

    @Override
    public Page<QuestionManagementVO> getQuestionManagementPage(Page<QuestionManagementDTO> pageRequest) throws Exception {

        Page<QuestionManagementVO> resultPage = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), 0L, new ArrayList<>());
        pmsAuthUtil.setHeaderAuths(resultPage, CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), com.chinasie.orion.sdk.metadata.page.Page::setHeadAuthList, new ArrayList<>());

        LambdaQueryWrapperX<QuestionManagement> condition = new LambdaQueryWrapperX<>(QuestionManagement.class);

        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }

        QuestionManagementDTO query = pageRequest.getQuery();
        Optional.ofNullable(query)
                .ifPresent(q -> {
                    String projectId = q.getProjectId();
                    if (StrUtil.isNotBlank(projectId)) {
                        condition.eq(QuestionManagement::getProjectId, projectId);
                    }

                    Integer status = q.getStatus();
                    if (ObjectUtil.isNotNull(status)) {
                        condition.eq(QuestionManagement::getStatus, status);
                    }
                });
        condition.orderByDesc(QuestionManagement::getCreateTime);
        PageResult<QuestionManagement> pageResult = this.getBaseMapper().selectPage(pageRequest, condition);
        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(pageResult.getContent())) {
            return resultPage;
        }
        List<QuestionManagement> questionManagementList = pageResult.getContent();
        List<QuestionManagementVO> questionManagementVOList = BeanCopyUtils.convertListTo(questionManagementList, QuestionManagementVO::new);
        setContent(questionManagementVOList);

        //权限设置
        Map<String, List<String>> dataRoleMap = getDataRoleMap(questionManagementVOList);

        pmsAuthUtil.setRowAuths(CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), questionManagementVOList, QuestionManagementVO::getId, QuestionManagementVO::getDataStatus, QuestionManagementVO::setRdAuthList,
                QuestionManagementVO::getCreatorId,
                QuestionManagementVO::getModifyId,
                QuestionManagementVO::getOwnerId,
                dataRoleMap);
        resultPage.setContent(questionManagementVOList);
        resultPage.setTotalSize(pageResult.getTotalSize());
        return resultPage;
    }

    public Map<String, List<String>> getDataRoleMap(List<QuestionManagementVO> vos) throws Exception {
        Map<String, List<String>> dataRoleCodeMap = new HashMap<>();
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        for (QuestionManagementVO v : vos) {
            List<String> roles = pmsAuthUtil.getRoleCodeList(v.getProjectId(), currentUserId);
            dataRoleCodeMap.put(v.getId(), roles);
        }
        return dataRoleCodeMap;
    }

    @Override
    public QuestionManagementVO getQuestionManagementDetail(String id, @RequestParam(required = false) String pageCode) throws Exception {
        QuestionManagement questionManagementDTO = this.getById(id);
        if (Objects.isNull(questionManagementDTO)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }

        Set<String> userIdSet = new HashSet<>();
        userIdSet.add(questionManagementDTO.getCreatorId());
        userIdSet.add(questionManagementDTO.getModifyId());
        userIdSet.add(questionManagementDTO.getOwnerId());
        Map<String, String> nameByUserIdMap = userBo.getNameByUserIdMap(new ArrayList<>(userIdSet));
        questionManagementDTO.setCreatorName(nameByUserIdMap.get(questionManagementDTO.getCreatorId()));
        questionManagementDTO.setModifyName(nameByUserIdMap.get(questionManagementDTO.getModifyId()));
        questionManagementDTO.setOwnerName(nameByUserIdMap.get(questionManagementDTO.getOwnerId()));

        QuestionManagementVO questionManagementVO = new QuestionManagementVO();
        BeanCopyUtils.copyProperties(questionManagementDTO, questionManagementVO);
        List<String> projectUserIdList = new ArrayList<>();
        if (StringUtils.hasText(questionManagementVO.getRecipient())) {
            projectUserIdList.add(questionManagementVO.getRecipient());
        }
        if (StringUtils.hasText(questionManagementVO.getPrincipalId())) {
            projectUserIdList.add(questionManagementVO.getPrincipalId());
        }
        Project project = projectService.getById(questionManagementVO.getProjectId());
        Map<String, String> questionSourceValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.QUESTION_SOURCE);
        Map<String, String> questionSeriousLevelValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.QUESTION_SERIOUS_LEVEL);
        Map<String, String> priorityLevelValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.PRIORITY_LEVEL);
        Map<Integer, String> statusValueToNameMap = statusBo.getStatusValueToNameMapByPolicyId(StatusPolicyConstant.QUESTION_POLICY_ID);

        Map<String, UserVO> userMapByUserIds = userRedisHelper.getUserMapByUserIds(projectUserIdList);
        if (StringUtils.hasText(questionManagementVO.getRecipient())) {
            UserVO userVO = userMapByUserIds.get(questionManagementVO.getRecipient());
            questionManagementVO.setRecipientName(userVO == null ? "" : userVO.getName());
        }
        if (StringUtils.hasText(questionManagementVO.getPrincipalId())) {
            UserVO userVO = userMapByUserIds.get(questionManagementVO.getPrincipalId());
            questionManagementVO.setPrincipalName(userVO == null ? "" : userVO.getName());
        }

        if (ObjectUtil.isNotNull(project)){
            questionManagementVO.setProjectName(project.getName());
        }
        questionManagementVO.setQuestionSourceName(questionSourceValueToDesMap.get(questionManagementVO.getQuestionSource()));
        questionManagementVO.setSeriousLevelName(questionSeriousLevelValueToDesMap.get(questionManagementVO.getSeriousLevel()));
        questionManagementVO.setPriorityLevelName(priorityLevelValueToDesMap.get(questionManagementVO.getPriorityLevel()));
        questionManagementVO.setProposedTime(questionManagementVO.getCreateTime());
        questionManagementVO.setStatusName(statusValueToNameMap.get(questionManagementVO.getStatus()));
        questionManagementVO.setScheduleName(ObjectUtil.isNull(questionManagementVO.getSchedule()) ? "0%" : questionManagementVO.getSchedule() + "%");
        List<QuestionToRisk> questionToRisks = questionToRiskService.list(new LambdaQueryWrapper<>(QuestionToRisk.class)
                .eq(QuestionToRisk::getToId,id));
        if(CollectionUtil.isNotEmpty(questionToRisks)) {
            RiskManagement riskManagement = riskManagementService.getById(questionToRisks.get(0).getFromId());
            if(ObjectUtil.isNotEmpty(riskManagement)){
                questionManagementVO.setRiskId(riskManagement.getId());
                questionManagementVO.setRiskName(riskManagement.getName());
            }
        }
//        if (StringUtils.hasText(typeId)) {
//            TypeAndTypeAttrValueVO typeAndTypeAttrValueVO = pasBo.getQuestionTypeAndAttributeValues(typeId, id);
//            questionManagementVO.setQuestionTypeName(typeAndTypeAttrValueVO.getTypeName());
//            questionManagementVO.setTypeAttrValueDTOList(typeAndTypeAttrValueVO.getTypeAttrValueDTOList());
//        }
        if (StringUtils.hasText(pageCode)) {
            String currentUserId = CurrentUserHelper.getCurrentUserId();
            List<String> roleCodeList = pmsAuthUtil.getRoleCodeList(questionManagementVO.getProjectId(), currentUserId);
            pmsAuthUtil.setDetailAuths(questionManagementVO, currentUserId, pageCode, questionManagementVO.getDataStatus(), QuestionManagementVO::setDetailAuthList, questionManagementVO.getCreatorId(), questionManagementVO.getModifyId(), questionManagementVO.getOwnerId(), roleCodeList);

        }
        setEveryName(List.of(questionManagementVO));
        return questionManagementVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean editQuestionManagement(QuestionManagementDTO questionManagementDTO) throws Exception {
        String id = questionManagementDTO.getId();
        List<QuestionManagement> questionManagementDTOList = this.list(new LambdaQueryWrapper<>(QuestionManagement.class).
                ne(QuestionManagement::getId, id).
                eq(QuestionManagement::getName, questionManagementDTO.getName()).
                eq(QuestionManagement::getProjectId, questionManagementDTO.getProjectId()));
        if (!CollectionUtils.isEmpty(questionManagementDTOList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_NAME_REPEAT);
        }
        QuestionManagement questionManagement = BeanCopyUtils.convertTo(questionManagementDTO, QuestionManagement::new);
        this.updateById(questionManagement);
        //删除属性值并重新添加
        pasBo.deleteTypeAttributeValueByQuestionIds(Collections.singletonList(id));
        List<TypeAttrValueDTO> typeAttrValueDTOList = questionManagementDTO.getTypeAttrValueDTOList();
        if (!CollectionUtils.isEmpty(typeAttrValueDTOList)) {
            List<QuestionTypeAttributeValueDTO> questionTypeAttributeValueDTOList = BeanCopyUtils.convertListTo(typeAttrValueDTOList, QuestionTypeAttributeValueDTO::new);
            questionTypeAttributeValueDTOList.forEach(d -> {
                d.setQuestionId(id);
                d.setTypeId(d.getTypeId());
            });
            pasBo.addQuestionTypeAttributeValue(questionTypeAttributeValueDTOList);
        }

        QuestionManagement questionManagementDTO1 = this.getById(id);
        String documentId = questionManagementDTO1.getDocumentId();
        if (StringUtils.hasText(documentId)) {
            DocumentDTO documentDTO = new DocumentDTO();
            documentDTO.setId(documentId);
            documentDTO.setName(questionManagementDTO.getName());
            documentDTO.setNumber(questionManagementDTO.getNumber());
            documentDTO.setClassName(DocumentClassNameConstant.Question_Document);
            documentBo.updateDocument(documentDTO);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeQuestionManagement(List<String> ids) throws Exception {
        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }
        List<QuestionManagement> questionManagementDTOList = this.list(new LambdaQueryWrapper<>(QuestionManagement.class).
                in(QuestionManagement::getId, ids));
        if (CollectionUtils.isEmpty(questionManagementDTOList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        List<Integer> statusList = questionManagementDTOList.stream().map(QuestionManagement::getStatus).distinct().collect(Collectors.toList());
//        List<DataStatusVO> dataStatusVOS = statusBo.getStatusList(StatusPolicyConstant.QUESTION_POLICY_ID);
        if (!statusList.stream().allMatch(element -> Objects.equals(element, QuestionManagementEnum.CREATE.getCode())
                || Objects.equals(element, QuestionManagementEnum.CLOSE.getCode()))) {
            throw new PMSException(PMSErrorCode.KMS_EFFECT_DATA, "存在数据已生效");
        }
        List<QuestionToRisk> questionToRisks = questionToRiskService.list(new LambdaQueryWrapper<>(QuestionToRisk.class).in(QuestionToRisk::getToId, ids));
        if (!CollectionUtils.isEmpty(questionToRisks)) {
            questionToRiskService.remove(new LambdaQueryWrapper<>(QuestionToRisk.class).in(QuestionToRisk::getToId, ids));
        }
        planToQuestionManagementService.remove(new LambdaQueryWrapper<>(PlanToQuestionManagement.class).in(PlanToQuestionManagement::getFromId, ids));

        List<FileInfo> fileInfoDTOList = fileInfoService.list(new LambdaQueryWrapper<>(FileInfo.class)
                .in(FileInfo::getDataId, ids));
        if (!CollectionUtils.isEmpty(fileInfoDTOList)) {
            documentService.deleteBatchFileInfo(fileInfoDTOList);
        }

        List<String> documentIdList = new ArrayList<>();
        for (QuestionManagement questionManagementDTO : questionManagementDTOList) {
            String documentId = questionManagementDTO.getDocumentId();
            if (StringUtils.hasText(documentId)) {
                documentIdList.add(documentId);
            }
        }
        if (!CollectionUtils.isEmpty(documentIdList)) {
            documentBo.delByIdList(documentIdList);
        }

        pasBo.deleteQuestionDirToQuestionManagementBQuestionIds(ids);
       if (this.isEnterLibrary(ids)){
           throw new PMSException(PMSErrorCode.PMS_ERROR_NOT_ROLE, "已入典型问题库无法删除");
       }
        return this.removeBatchByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteQuestionRelationRiskAndPlan(List<String> questionIdList) throws Exception {
        List<QuestionToRisk> questionToRisks = questionToRiskService.list(new LambdaQueryWrapper<>(QuestionToRisk.class).in(QuestionToRisk::getToId, questionIdList));
        if (!CollectionUtils.isEmpty(questionToRisks)) {
            List<String> collect = questionToRisks.stream().map(QuestionToRisk::getId).collect(Collectors.toList());
            questionToRiskService.removeBatchByIds(collect);
        }
        planToQuestionManagementService.remove(new LambdaQueryWrapper<>(PlanToQuestionManagement.class).in(PlanToQuestionManagement::getFromId, questionIdList));
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean relationToPlan(RelationToPlanDTO relationToPlanDTO) throws Exception {
        if (!CollectionUtils.isEmpty(planToQuestionManagementService.list(new LambdaQueryWrapper<>(PlanToQuestionManagement.class)
                .eq(PlanToQuestionManagement::getFromId, relationToPlanDTO.getId())
                .in(PlanToQuestionManagement::getToId, relationToPlanDTO.getPlanIds())))) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST);
        }

        List<PlanToQuestionManagement> planToQuestionManagements = new ArrayList<>();

        for (String planId : relationToPlanDTO.getPlanIds()) {
            PlanToQuestionManagement planToQuestionManagement = new PlanToQuestionManagement();
            planToQuestionManagement.setClassName("PlanToQuestionManagement");
            planToQuestionManagement.setFromId(relationToPlanDTO.getId());
            planToQuestionManagement.setFromClass("QuestionManagement");
            planToQuestionManagement.setToId(planId);
            planToQuestionManagement.setToClass("Plan");
            planToQuestionManagements.add(planToQuestionManagement);
        }
        planToQuestionManagementService.saveBatch(planToQuestionManagements);
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeRelationToPlan(RelationToPlanDTO relationToPlanDTO) throws Exception {
        //TODO 6/12 审查 吴永松  通报
        for (String toId : relationToPlanDTO.getPlanIds()) {
            planToQuestionManagementService.remove(new LambdaQueryWrapper<>(PlanToQuestionManagement.class).eq(PlanToQuestionManagement::getFromId, relationToPlanDTO.getId()).eq(PlanToQuestionManagement::getToId, toId));
        }
        return Boolean.TRUE;
    }


    @Override
    public List<PlanDetailVo> getPlanManagementListByQuestion(String id, PlanQueryDTO planQueryDTO) throws Exception {
        if (!StringUtils.hasText(id)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "问题id不能为空");
        }


        LambdaQueryWrapperX<PlanToQuestionManagement> condition = new LambdaQueryWrapperX<>(PlanToQuestionManagement.class)
                .eq(PlanToQuestionManagement::getFromId, id);
        List<PlanToQuestionManagement> relationList = planToQuestionManagementService.list(condition);
        if (CollectionUtils.isEmpty(relationList)) {
            return new ArrayList<>();
        }
        List<String> planIdList = relationList.stream().map(PlanToQuestionManagement::getToId).collect(Collectors.toList());
        if (Objects.isNull(planQueryDTO)) {
            planQueryDTO = new PlanQueryDTO();
        }
        planQueryDTO.setIds(planIdList);

        SearchDTO searchDTO = new SearchDTO();
        searchDTO.setIds(planIdList);
        searchDTO.setSearchConditions(planQueryDTO.getSearchConditions());
        return projectSchemeService.search(searchDTO);

    }

    @Override
    public List<QuestionManagementVO> getQuestionManagementListByPlan(String planId, QuestionManagementQueryDTO questionManagementQueryDTO) throws Exception {
        if (!StringUtils.hasText(planId)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "计划id不能为空");
        }
        List<PlanToQuestionManagement> relationList = planToQuestionManagementService.list(new LambdaQueryWrapper<>(PlanToQuestionManagement.class)
                        .eq(PlanToQuestionManagement::getToId, planId)
//                .eq(PlanToQuestionManagement::getToClass, RelationClassNameConstant.PLAN)
        );
        if (CollectionUtils.isEmpty(relationList)) {
            return new ArrayList<>();
        }
        return this.queryByQuestionManagementQueryDTO(relationList.stream().map(PlanToQuestionManagement::getFromId).collect(Collectors.toList()), questionManagementQueryDTO);
    }

    @Override
    public List<QuestionManagementVO> getQuestionManagementListByRisk(String riskId, QuestionManagementQueryDTO questionManagementQueryDTO) throws Exception {
        if (!StringUtils.hasText(riskId)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "风险id不能为空");
        }
        List<QuestionToRisk> questionToRiskList = questionToRiskService.list(new LambdaQueryWrapper<>(QuestionToRisk.class)
                .eq(QuestionToRisk::getFromId, riskId));
        if (CollectionUtils.isEmpty(questionToRiskList)) {
            return new ArrayList<>();
        }
        return this.queryByQuestionManagementQueryDTO(questionToRiskList.stream().map(QuestionToRisk::getToId).collect(Collectors.toList()), questionManagementQueryDTO);
    }

    private List<QuestionManagementVO> queryByQuestionManagementQueryDTO(List<String> idList, QuestionManagementQueryDTO questionManagementQueryDTO) throws Exception {
        LambdaQueryWrapper<QuestionManagement> wrapper = new LambdaQueryWrapper<>(QuestionManagement.class);
        wrapper.in(QuestionManagement::getId, idList.toArray());
        if (!ObjectUtils.isEmpty(questionManagementQueryDTO)) {
            String keyword = questionManagementQueryDTO.getKeyword();
            if (StringUtils.hasText(keyword)) {
                wrapper.like(QuestionManagement::getName, keyword);
                wrapper.like(QuestionManagement::getNumber, keyword);
            }
            String questionType = questionManagementQueryDTO.getQuestionType();
            if (StringUtils.hasText(questionType)) {
                wrapper.eq(QuestionManagement::getQuestionType, questionType);
            }
            String questionSource = questionManagementQueryDTO.getQuestionSource();
            if (StringUtils.hasText(questionSource)) {
                wrapper.eq(QuestionManagement::getQuestionSource, questionSource);
            }
            String seriousLevel = questionManagementQueryDTO.getSeriousLevel();
            if (StringUtils.hasText(seriousLevel)) {
                wrapper.eq(QuestionManagement::getSeriousLevel, seriousLevel);
            }
            Integer status = questionManagementQueryDTO.getStatus();
            if (Objects.nonNull(status)) {
                wrapper.eq(QuestionManagement::getStatus, status);
            }
        }
        List<QuestionManagement> questionManagementDTOList = this.list(wrapper);
        List<QuestionManagementVO> questionManagementVOList = BeanCopyUtils.convertListTo(questionManagementDTOList, QuestionManagementVO::new);
        setContent(questionManagementVOList);
        return questionManagementVOList;
    }

    @Override
    public PlanSearchDataVo searchList(KeywordDto keywordDto) throws Exception {
        PlanSearchDataVo planSearchDataVo = new PlanSearchDataVo();
        String keyword = keywordDto.getKeyword();
        String projectId = keywordDto.getProjectId();
        LambdaQueryWrapper<QuestionManagement> wrapper = new LambdaQueryWrapper<>(QuestionManagement.class);
        if (StrUtil.isNotBlank(projectId)) {
            wrapper.eq(QuestionManagement::getProjectId, projectId);
        }
        if (!StrUtil.isBlank(keyword)) {
            wrapper.and(sub -> sub.like(QuestionManagement::getName, keyword).or().like(QuestionManagement::getNumber, keyword));
        }

        List<QuestionManagement> questionManagementDTOList = this.list(wrapper);
        if (CollectionUtils.isEmpty(questionManagementDTOList)) {
            return planSearchDataVo;
        }
        Set<String> userIdList = new HashSet<>();
        for (QuestionManagement questionManagementDTO : questionManagementDTOList) {
            String principalId = questionManagementDTO.getPrincipalId();
            if (StringUtils.hasText(principalId)) {
                userIdList.add(principalId);
            }
        }
        Map<String, UserVO> userMapByUserIds = userRedisHelper.getUserMapByUserIds(new ArrayList<>(userIdList));
//        Map<String, String> nameByUserIdMap = new HashMap<>();
//        if(userIdList.size() >0){
//            nameByUserIdMap = userBo.getNameByUserIdMap(new ArrayList<>(userIdList));
//        }
        List<PlanSearchVo> simpleVos = BeanCopyUtils.convertListTo(questionManagementDTOList, PlanSearchVo::new);
//        Map<String, String> finalNameByUserIdMap = nameByUserIdMap;
        simpleVos.forEach(o -> {
            String principalId = o.getPrincipalId();
            if (StringUtils.hasText(principalId)) {
                UserVO userVO = userMapByUserIds.get(principalId);
                o.setPrincipalName(userVO == null ? "" : userVO.getName());
            }
        });
        planSearchDataVo.setSize(simpleVos.size());
        planSearchDataVo.setPlanSearchVos(simpleVos);
        return planSearchDataVo;
    }


    @Override
    public void setContent(List<QuestionManagementVO> questionManagementVOList) throws Exception {
        if (questionManagementVOList.size() > 0) {
            List<QuestionManagementVO> list = questionManagementVOList.stream().filter(item -> StringUtils.hasText(item.getProjectId())).collect(Collectors.toList());
            String projectName = "";
            if (!CollectionUtils.isEmpty(list)) {
                Project projectDTO = projectService.getById(list.get(0).getProjectId());
                projectName = null == projectDTO ? "" : projectDTO.getName();
            }
            List<String> projectUserIdList = new ArrayList<>();
            for (QuestionManagementVO o : questionManagementVOList) {
                if (StringUtils.hasText(o.getPrincipalId())) {
                    projectUserIdList.add(o.getPrincipalId());
                }
                if (StringUtils.hasText(o.getRecipient())) {
                    projectUserIdList.add(o.getRecipient());
                }
            }

            Map<String, String> questionSourceValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.QUESTION_SOURCE);
            Map<Integer, String> statusValueToNameMap = statusBo.getStatusValueToNameMapByPolicyId(StatusPolicyConstant.QUESTION_POLICY_ID);
            Map<String, UserVO> userMapByUserIds = userRedisHelper.getUserMapByUserIds(projectUserIdList);
            Map<Integer, DataStatusVO> dataStatusMap= dataStatusNBO.getDataStatusMapByClassName(QuestionManagement.class.getSimpleName());

            Map<Integer, String> questValueToNameMap = statusBo.getStatusValueToNameMapByPolicyId(StatusPolicyConstant.QUESTION_TYPE_ID);
            String finalProjectName = projectName;
            List<ProjectScheme> projectSchemes = projectSchemeService.list(new LambdaQueryWrapper<>(ProjectScheme.class)
                    .in(ProjectScheme::getQuestionId, questionManagementVOList.stream().map(QuestionManagementVO::getId).collect(Collectors.toList())));
            Map<String, List<ProjectScheme>> projectSchemeMap = projectSchemes.stream().collect(Collectors.groupingBy(ProjectScheme::getQuestionId));
            questionManagementVOList.forEach(o -> {
                if (StringUtils.hasText(o.getPrincipalId())) {
                    UserVO userVO1 = userMapByUserIds.get(o.getPrincipalId());
                    o.setPrincipalName(userVO1 == null ? "" : userVO1.getName());
                }
                if (StringUtils.hasText(o.getRecipient())) {
                    UserVO userVO1 = userMapByUserIds.get(o.getRecipient());
                    o.setRecipientName(userVO1 == null ? "" : userVO1.getName());
                }

                o.setStatusName(statusValueToNameMap.get(o.getStatus()));
                o.setDataStatus(dataStatusMap.getOrDefault(o.getStatus(),null));

                o.setProjectName(finalProjectName);
                o.setQuestionSourceName(questionSourceValueToDesMap.get(o.getQuestionSource()));
                o.setScheduleName(ObjectUtil.isNull(o.getSchedule()) ? "0%" : o.getSchedule() + "%");
                String typeId = o.getQuestionType();
//                if (StringUtils.hasText(typeId)) {
//                    o.setQuestionTypeName(questValueToNameMap.getOrDefault(typeId,""));
//                }
                if(!CollectionUtils.isEmpty(projectSchemeMap.get(o.getId()))){
                    o.setIsPlan(Boolean.TRUE);
                }else {
                    o.setIsPlan(Boolean.FALSE);
                }
            });
            setEveryName(questionManagementVOList);
        }
    }



    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        String filePath = "问题管理导入模板_产品研发问题.xlsx";
        ExcelUtils.writeTemplate(response, QuestionManagementImportDTO.class, filePath, "问题管理导入模板_产品研发问题", "问题管理导入模板_产品研发问题");
    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel,String projectId) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        QuestionManagementExcelListener excelReadListener = new QuestionManagementExcelListener();
        EasyExcel.read(inputStream,QuestionManagementImportDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<QuestionManagementImportDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }

        /*
         * 渲染文档导入的错误结果VO
         */
        List<ImportExcelErrorNoteVO> errorNotes = renderExcelResult(dtoS);
        List<QuestionManagement> questionManagements = BeanCopyUtils.convertListTo(dtoS,QuestionManagement::new);
        questionManagements.forEach(o -> {
            if (ObjectUtil.isNotNull(o)){
                o.setProjectId(projectId);
            }
        });
        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pms::QuestionManagement-import::id", importId, questionManagements, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);
        if (CollectionUtil.isNotEmpty(errorNotes)){
            result.setErr(errorNotes);
            result.setSucc("");
        }
        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<QuestionManagement> questionManagements = (List<QuestionManagement>) orionJ2CacheService.get("pms::QuestionManagement-import::id", importId);

        // 问题类型
        Map<String, String> questionTypeNewDictMap = getQuestionTypeNewDictMap();
        // 阶段
        Map<String, String> stageDictMap = getStageDictMap();
        // 过程环节
        Map<String, String> processLinkDictMap = getProcessLinkDictMap();
        // 问题现象一级分类
        Map<String, String> problemPhenomenonOneDictMap = getProblemPhenomenonOneDictMap();
        // 问题等级分类
        Map<String, String> problemLevelDictMap = getProblemLevelDictMap();
        // 优先级
        Map<String, String> priorityLevelDictMap = getPriorityLevelDictMap();

        questionManagements.forEach(c->{
            if (StrUtil.isNotBlank(c.getQuestionType())){
                c.setQuestionType(questionTypeNewDictMap.getOrDefault(c.getQuestionType(),""));
            }
            if (StrUtil.isNotBlank(c.getStage())){
                c.setStage(stageDictMap.getOrDefault(c.getStage(), ""));
            }
            if (StrUtil.isNotBlank(c.getProcessLink())){
                c.setProcessLink(processLinkDictMap.getOrDefault(c.getProcessLink(), ""));
            }
            if (StrUtil.isNotBlank(c.getProblemPhenomenonOne())){
                c.setProblemPhenomenonOne(problemPhenomenonOneDictMap.getOrDefault(c.getProblemPhenomenonOne(), ""));
            }
            if (StrUtil.isNotBlank(c.getProblemPhenomenonTwo())){
                c.setProblemPhenomenonTwo(problemPhenomenonOneDictMap.getOrDefault(c.getProblemPhenomenonTwo(), ""));
            }
            if (StrUtil.isNotBlank(c.getProblemPhenomenonTh())){
                c.setProblemPhenomenonTh(problemPhenomenonOneDictMap.getOrDefault(c.getProblemPhenomenonTh(), ""));
            }
            if (StrUtil.isNotBlank(c.getProblemLevel())){
                c.setProblemLevel(problemLevelDictMap.getOrDefault(c.getProblemLevel(), ""));
            }
            if (StrUtil.isNotBlank(c.getPriorityLevel())){
                c.setPriorityLevel(priorityLevelDictMap.getOrDefault(c.getPriorityLevel(), ""));
            }
        });

        this.createBatch(BeanCopyUtils.convertListTo(questionManagements, QuestionManagementDTO::new));
        orionJ2CacheService.delete("pms::QuestionManagement-import::id", importId);
        return true;
    }

    @NotNull
    private Map<String, String> getPriorityLevelDictMap() {
        List<DictValueTreeVO> childDictValueListByCode6 = dictRedisHelper.getChildDictValueListByCode(DictConstant.PRIORITY_DICT);
        return childDictValueListByCode6.stream().collect(Collectors.toMap(DictValueTreeVO::getDescription, DictValueTreeVO::getNumber));
    }

    @NotNull
    private Map<String, String> getProblemLevelDictMap() {
        List<DictValueTreeVO> childDictValueListByCode5 = dictRedisHelper.getChildDictValueListByCode(DictConstant.PROBLEM_LEVEL_DICT);
        return childDictValueListByCode5.stream().collect(Collectors.toMap(DictValueTreeVO::getDescription, DictValueTreeVO::getNumber));
    }

    @NotNull
    private Map<String, String> getProblemPhenomenonOneDictMap() {
        List<DictValueTreeVO> childDictValueListByCode4 = dictRedisHelper.getChildDictValueListByCode(DictConstant.PROBLEM_PHENOMENON_ONE_DICT);
        return childDictValueListByCode4.stream().collect(Collectors.toMap(DictValueTreeVO::getDescription, DictValueTreeVO::getNumber));
    }

    @NotNull
    private Map<String, String> getProcessLinkDictMap() {
        List<DictValueTreeVO> childDictValueListByCode3 = dictRedisHelper.getChildDictValueListByCode(DictConstant.PROCESS_LINK_DICT);
        return childDictValueListByCode3.stream().collect(Collectors.toMap(DictValueTreeVO::getDescription, DictValueTreeVO::getNumber));
    }

    @NotNull
    private Map<String, String> getStageDictMap() {
        List<DictValueTreeVO> childDictValueListByCode2 = dictRedisHelper.getChildDictValueListByCode(DictConstant.STAGE_DICT);
        return childDictValueListByCode2.stream().collect(Collectors.toMap(DictValueTreeVO::getDescription, DictValueTreeVO::getNumber));
    }

    @NotNull
    private Map<String, String> getQuestionTypeNewDictMap() {
        List<DictValueTreeVO> childDictValueListByCode1 = dictRedisHelper.getChildDictValueListByCode(DictConstant.QUESTION_TYPE_NEW_DICT);
        return childDictValueListByCode1.stream().collect(Collectors.toMap(DictValueTreeVO::getDescription, DictValueTreeVO::getNumber));
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pms::QuestionManagement-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<QuestionManagement> condition = new LambdaQueryWrapperX<>( QuestionManagement. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(QuestionManagement::getCreateTime);
        List<QuestionManagement> questionManagementes =   this.list(condition);

        List<QuestionManagementVO> questionManagementVOList = BeanCopyUtils.convertListTo(questionManagementes, QuestionManagementVO::new);
        questionManagementVOList.forEach(item->{
            if (item.getStatus()==0){
                item.setStatusName(ProjectStatusEnum.getStringByStatus(item.getStatus()));
            }
        });
        setContent(questionManagementVOList);
//        setEveryName(questionManagementVOList);
        List<QuestionManagementExportDTO> questionManagementExportExcelList = BeanCopyUtils.convertListTo(questionManagementVOList, QuestionManagementExportDTO::new);
        AtomicReference<Integer> i = new AtomicReference<>(1);
        questionManagementExportExcelList.forEach(c-> c.setOrder(i.getAndSet(i.get() + 1)));

        String fileName = "问题管理数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", QuestionManagementExportDTO.class,questionManagementExportExcelList);

    }

    @Override
    public void setEveryName(List<QuestionManagementVO> vos)throws Exception {
        // 阶段
        List<DictValueTreeVO> childDictValueListByCode = dictRedisHelper.getChildDictValueListByCode(DictConstant.STAGE_DICT);
        Map<String, String> stageDictMap = childDictValueListByCode.stream().collect(Collectors.toMap(DictValueTreeVO:: getNumber, DictValueTreeVO::getDescription));
        // 过程环节
        List<DictValueTreeVO> childDictValueListByCode2 = dictRedisHelper.getChildDictValueListByCode(DictConstant.PROCESS_LINK_DICT);
        Map<String, String> processLinkDictMap = childDictValueListByCode2.stream().collect(Collectors.toMap(DictValueTreeVO::getNumber, DictValueTreeVO::getDescription));
        // 问题现象一级分类
        List<DictValueTreeVO> childDictValueListByCode3 = dictRedisHelper.getChildDictValueListByCode(DictConstant.PROBLEM_PHENOMENON_ONE_DICT);
        Map<String, String> problemPhenomenonOneDictMap = childDictValueListByCode3.stream().collect(Collectors.toMap(DictValueTreeVO::getNumber, DictValueTreeVO::getDescription));
        // 问题等级分类
        List<DictValueTreeVO> childDictValueListByCode4 = dictRedisHelper.getChildDictValueListByCode(DictConstant.PROBLEM_LEVEL_DICT);
        Map<String, String> problemLevelDictMap = childDictValueListByCode4.stream().collect(Collectors.toMap(DictValueTreeVO::getNumber, DictValueTreeVO::getDescription));
        // 纠正分类
        List<DictValueTreeVO> childDictValueListByCode5 = dictRedisHelper.getChildDictValueListByCode(DictConstant.CORRECT_CLASSIFICATION_DICT);
        Map<String, String> correctClassificationDictMap = childDictValueListByCode5.stream().collect(Collectors.toMap(DictValueTreeVO::getNumber, DictValueTreeVO::getDescription));
        // 一级原因分类
        List<DictValueTreeVO> childDictValueListByCode6 = dictRedisHelper.getChildDictValueListByCode(DictConstant.REASION_ONE_DICT);
        Map<String, String> reasionOneDictMap = childDictValueListByCode6.stream().collect(Collectors.toMap(DictValueTreeVO::getNumber, DictValueTreeVO::getDescription));
        // 意见分类
        List<DictValueTreeVO> childDictValueListByCode7 = dictRedisHelper.getChildDictValueListByCode(DictConstant.OPINION_CLASSIFICATION_DICT);
        Map<String, String> opinionClassificationDictMap = childDictValueListByCode7.stream().collect(Collectors.toMap(DictValueTreeVO::getNumber, DictValueTreeVO::getDescription));
        // 采纳情况
        List<DictValueTreeVO> childDictValueListByCode8 = dictRedisHelper.getChildDictValueListByCode(DictConstant.ADOPTION_SITUATION_DICT);
        Map<String, String> adoptionSituationDictMap = childDictValueListByCode8.stream().collect(Collectors.toMap(DictValueTreeVO::getNumber, DictValueTreeVO::getDescription));
        // 优先级
        List<DictValueTreeVO> childDictValueListByCode9 = dictRedisHelper.getChildDictValueListByCode(DictConstant.PRIORITY_DICT);
        Map<String, String> priorityLevellDictMap = childDictValueListByCode9.stream().collect(Collectors.toMap(DictValueTreeVO::getNumber, DictValueTreeVO::getDescription));

        // 严重程度
        List<DictValueTreeVO> childDictValueListByCode10 = dictRedisHelper.getChildDictValueListByCode(DictConstant.QUESTION_SERIOUS_LEVEL_DICT);
        Map<String, String> severityDictMap = childDictValueListByCode10.stream().collect(Collectors.toMap(DictValueTreeVO::getNumber, DictValueTreeVO::getDescription));

        Map<Integer, DataStatusVO> dataStatusMap= dataStatusNBO.getDataStatusMapByClassName(QuestionManagement.class.getSimpleName());

        Map<String, String> reviewEssentialsMap = new HashMap<>();
        List<String> reviewPointsList = vos.stream()
                .map(QuestionManagementVO::getReviewPoints).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(reviewPointsList)){
            List<ReviewEssentials> reviewEssentials = reviewEssentialsService.listByIds(reviewPointsList);
            if (CollectionUtil.isNotEmpty(reviewEssentials)){
                reviewEssentialsMap = reviewEssentials.stream().collect(Collectors.toMap(ReviewEssentials::getId, ReviewEssentials::getContent));
            }
        }
        Map<String, String> finalReviewEssentialsMap = reviewEssentialsMap;
        vos.forEach(vo->{
            if (ObjectUtil.isNotEmpty(vo.getStatus())){
                vo.setDataStatus(dataStatusMap.getOrDefault(vo.getStatus(),null));
            }
            if (StrUtil.isNotBlank(vo.getStage())){
                vo.setStageName(stageDictMap.get(vo.getStage()));
            }
            if (StrUtil.isNotBlank(vo.getProcessLink())){
                vo.setProcessLinkName(processLinkDictMap.get(vo.getProcessLink()));
            }
            if (StrUtil.isNotBlank(vo.getProblemPhenomenonOne())){
                vo.setProblemPhenomenonOneName(problemPhenomenonOneDictMap.get(vo.getProblemPhenomenonOne()));
            }
            if (StrUtil.isNotBlank(vo.getProblemPhenomenonTwo())){
                vo.setProblemPhenomenonTwoName(problemPhenomenonOneDictMap.get(vo.getProblemPhenomenonTwo()));
            }
            if (StrUtil.isNotBlank(vo.getProblemPhenomenonTh())){
                vo.setProblemPhenomenonThName(problemPhenomenonOneDictMap.getOrDefault(vo.getProblemPhenomenonTh(), vo.getProblemPhenomenonTh()));
            }
            if (StrUtil.isNotBlank(vo.getProblemLevel())){
                vo.setProblemLevelName(problemLevelDictMap.get(vo.getProblemLevel()));
            }
            if (StrUtil.isNotBlank(vo.getCorrectClassifi())){
                vo.setCorrectClassifiName(correctClassificationDictMap.get(vo.getCorrectClassifi()));
            }
            if (StrUtil.isNotBlank(vo.getReasionOne())){
                vo.setReasionOneName(reasionOneDictMap.get(vo.getReasionOne()));
            }
            if (StrUtil.isNotBlank(vo.getReasionTwo())){
                vo.setReasionTwoName(reasionOneDictMap.get(vo.getReasionTwo()));
            }
            if (StrUtil.isNotBlank(vo.getReasionThree())){
                vo.setReasionThreeName(reasionOneDictMap.get(vo.getReasionThree()));
            }
            if (StrUtil.isNotBlank(vo.getOpClassification())){
                String[] split = vo.getOpClassification().split(",");
                if (split.length > 1){
                    vo.setOpClassificationName(String.format("%s-%s", opinionClassificationDictMap.get(split[0]), opinionClassificationDictMap.get(split[1])));
                }else {
                    vo.setOpClassificationName(split[0]);
                }

            }
            if (StrUtil.isNotBlank(vo.getAdoptionSituation())){
                vo.setAdoptionSituationName(adoptionSituationDictMap.get(vo.getAdoptionSituation()));
            }
            if (StrUtil.isNotBlank(vo.getPriorityLevel())){
                vo.setPriorityLevelName(priorityLevellDictMap.get(vo.getPriorityLevel()));
            }
            if (StrUtil.isNotBlank(vo.getSeriousLevel())){
                vo.setSeriousLevelName(severityDictMap.get(vo.getSeriousLevel()));
            }

            if (StrUtil.isNotBlank(vo.getReviewPoints())){
                vo.setReviewPointsName(finalReviewEssentialsMap.get(vo.getReviewPoints()));
            }

        });


    }

    @Override
    public Boolean close(List<String> ids) throws Exception {
        if (CollectionUtil.isEmpty(ids)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "操作数据不能为空");
        }
        List<QuestionManagement> list = this.list(new LambdaQueryWrapperX<>(QuestionManagement.class)
                .select(QuestionManagement::getId,
                        QuestionManagement::getStatus)
              //  .eq(QuestionManagement::getExhibitor, currentUserHelper.getUserId())
                .eq(QuestionManagement::getStatus, QuestionManagementEnum.COMPLETED.getCode())
                .in(QuestionManagement::getId, ids));

        if (CollectionUtil.isEmpty(list)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "没有符合关闭条件数据");
        }
        return this.updateBatchById(list.stream().peek(m->{
           m.setCloseTime(new Date()); // 做项目评价统计新添字段
           m.setStatus(QuestionManagementEnum.CLOSE.getCode());
        }).collect(Collectors.toList()));
    }

    @Override
    public Boolean open(List<String> ids) throws Exception {

        if (CollectionUtil.isEmpty(ids)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "操作数据不能为空");
        }
        List<QuestionManagement> list = this.list(new LambdaQueryWrapperX<>(QuestionManagement.class)
                .select(QuestionManagement::getId,
                        QuestionManagement::getStatus)
               // .eq(QuestionManagement::getPrincipalId, currentUserHelper.getUserId())
                .eq(QuestionManagement::getStatus, QuestionManagementEnum.CLOSE.getCode())
                .in(QuestionManagement::getId, ids));

        if (CollectionUtil.isEmpty(list)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "没有符合激活条件数据");
        }
//        if (this.isEnterLibrary(ids)){
//            throw new PMSException(PMSErrorCode.PMS_ERROR_NOT_ROLE, "已入典型问题库无法激活");
//        }
        return this.updateBatchById(list.stream().peek(m->{
            m.setCloseTime(null); // 做项目评价统计新添字段
            m.setStatus(QuestionManagementEnum.COMPLETED.getCode());
        }).collect(Collectors.toList()));
    }

    @Override
    public Boolean relationToReviewForm(String questionId, String reviewFormId) throws Exception {
        QuestionManagement questionManagement = checkRelationToReviewFrom(questionId, reviewFormId);
        if (StrUtil.isNotBlank(questionManagement.getQuestionSource())){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "已经关联评审单");
        }
        questionManagement.setQuestionSource(reviewFormId);
        questionManagement.setIsActiveRelation(true);
        return this.updateById(questionManagement);
    }



    @Override
    public Boolean removeRelationToReviewForm(String questionId, String reviewFormId) throws Exception {
        QuestionManagement questionManagement = checkRelationToReviewFrom(questionId, reviewFormId);
        if (ObjectUtil.isNull(questionManagement.getIsActiveRelation()) || !questionManagement.getIsActiveRelation()){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "非问题主动关联评审单无法删除");
        }
        questionManagement.setQuestionSource("");
        reviewOpinionService.removeQuestionBind(questionId);
        return this.updateById(questionManagement);
    }

    @Override
    public Boolean isEnterLibrary(List<String> questionIds) throws Exception {
        long count = questionLibraryService.count(new LambdaQueryWrapper<>(QuestionLibrary.class)
                .select(QuestionLibrary::getId)
                .in(QuestionLibrary::getQuestionId, questionIds));
        if (count >= 1) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public Boolean questionChangePlan(String id, List<ProjectSchemeDTO> projectSchemes) throws Exception {
        for(ProjectSchemeDTO project : projectSchemes){
            project.setQuestionId(id);
        }
        List<String> planIds = projectSchemeService.createBatch("0",projectSchemes);
        List<PlanToQuestionManagement> planToQuestionManagements = Lists.newArrayList();
        for (String planId : planIds) {
            PlanToQuestionManagement planToQuestionManagement = new PlanToQuestionManagement();
            planToQuestionManagement.setClassName("PlanToQuestionManagement");
            planToQuestionManagement.setFromId(id);
            planToQuestionManagement.setFromClass("QuestionManagement");
            planToQuestionManagement.setToId(planId);
            planToQuestionManagement.setToClass("Plan");

            planToQuestionManagements.add(planToQuestionManagement);
        }
        planToQuestionManagementService.saveBatch(planToQuestionManagements);
        return Boolean.TRUE;
    }

    @NotNull
    private QuestionManagement checkRelationToReviewFrom(String questionId, String reviewFormId) {
        if (StrUtil.isBlank(questionId)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "关联问题ID不能为空");
        }
        if (StrUtil.isBlank(reviewFormId)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "关联评审单ID不能为空");
        }
        QuestionManagement questionManagement = this.getById(questionId);
        if (ObjectUtil.isNull(questionManagement)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "问题不存在");
        }
        return questionManagement;
    }


    public static class QuestionManagementExcelListener extends AnalysisEventListener<QuestionManagementImportDTO> {

        private final List<QuestionManagementImportDTO> data = new ArrayList<>();

        @Override
        public void invoke(QuestionManagementImportDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<QuestionManagementImportDTO> getData() {
            return data;
        }
    }


    /**
     * 渲染文档导入的错误结果VO
     *
     * @param questionManagementImportList 导入数据列表
     */
    private List<ImportExcelErrorNoteVO> renderExcelResult(List<QuestionManagementImportDTO> questionManagementImportList) throws Exception {
        List<ImportExcelErrorNoteVO> errorNoteVOList = new ArrayList<>();

        Map<String, String> userMap = userRedisHelper.getAllUser().stream().collect(Collectors.toMap(UserVO::getName, UserVO::getId, (k1, k2) -> k1));
        // 阶段
        Map<String, String> stageDictMap = getStageDictMap();
        // 过程环节
        Map<String, String> processLinkDictMap = getProcessLinkDictMap();
        // 问题现象一级分类
        Map<String, String> problemPhenomenonOneDictMap = getProblemPhenomenonOneDictMap();
        // 问题等级分类
        Map<String, String> problemLevelDictMap = getProblemLevelDictMap();
        // 优先级
        Map<String, String> priorityLevelDictMap = getPriorityLevelDictMap();


        int j = 0;
        for (QuestionManagementImportDTO questionManagementImportDTO : questionManagementImportList) {
            j++;
            List<String> errorNotes = new ArrayList<>();

            if (StrUtil.isBlank(questionManagementImportDTO.getName()) || questionManagementImportDTO.getName().length() > 100) {
                errorNotes.add("问题标题不允许为空或超过100个字符");
            }
            if (StrUtil.isBlank(questionManagementImportDTO.getPrincipalId()) || !userMap.containsKey(questionManagementImportDTO.getPrincipalId())) {
                errorNotes.add("责任人为空或非系统成员");
            }

            if (StrUtil.isNotBlank(questionManagementImportDTO.getProposedTimeStr())) {
                if (dateFormat(questionManagementImportDTO.getProposedTimeStr())) {
                    errorNotes.add("提出日期格式错误  正确格式：yyyy-MM-dd");
                } else {
                    questionManagementImportDTO.setProposedTime(DateUtil.parse(questionManagementImportDTO.getProposedTimeStr(), DATE_FORMAT));
                }
            }

            if (StrUtil.isBlank(questionManagementImportDTO.getExhibitor()) || !userMap.containsKey(questionManagementImportDTO.getExhibitor())) {
                errorNotes.add("提出人为空或非系统成员");
            }

            if (StrUtil.isBlank(questionManagementImportDTO.getContent()) || questionManagementImportDTO.getContent().length() > 500) {
                errorNotes.add("问题描述不允许为空或超过100个字符");
            }

            // 阶段
            if (StrUtil.isBlank(questionManagementImportDTO.getStage()) || StrUtil.isBlank(stageDictMap.get(questionManagementImportDTO.getStage()))) {
                errorNotes.add("阶段不允许为空或不存在此阶段");
            }

            // 过程环节
            if (StrUtil.isBlank(questionManagementImportDTO.getProcessLink()) || StrUtil.isBlank(processLinkDictMap.get(questionManagementImportDTO.getProcessLink()))) {
                errorNotes.add("过程环节不允许为空或不存在此过程环节");
            }

            // todo 过程分类 (产品还未提供)
//            if (StrUtil.isBlank(excelDTO.getProcessClassifi()) || StrUtil.isBlank(stageDictMap.get(excelDTO.getProcessClassifi()))) {
//                errorNotes.add("过程分类不允许为空或不存在此过程分类");
//            }

            // 问题现象一级分类
            if (StrUtil.isBlank(questionManagementImportDTO.getProblemPhenomenonOne()) || StrUtil.isBlank(problemPhenomenonOneDictMap.get(questionManagementImportDTO.getProblemPhenomenonOne()))) {
                errorNotes.add("问题现象一级分类不允许为空或不存在此问题现象一级分类");
            }

            // 问题现象二级分类
            if (StrUtil.isBlank(questionManagementImportDTO.getProblemPhenomenonTwo()) || StrUtil.isBlank(problemPhenomenonOneDictMap.get(questionManagementImportDTO.getProblemPhenomenonTwo()))) {
                errorNotes.add("问题现象二级分类不允许为空或不存在此问题现象二级分类");
            }

            // 问题现象三级分类
            if (StrUtil.isNotBlank(questionManagementImportDTO.getProblemPhenomenonTh())) {
                if (StrUtil.isBlank(problemPhenomenonOneDictMap.get(questionManagementImportDTO.getProblemPhenomenonTh()))) {
                    errorNotes.add("不存在此问题现象三级分类");
                }

            }

            // 问题等级分类
            if (StrUtil.isBlank(questionManagementImportDTO.getProblemLevel()) || StrUtil.isBlank(problemLevelDictMap.get(questionManagementImportDTO.getProblemLevel()))) {
                errorNotes.add("问题等级分类不允许为空或不存在此阶段");
            }

            // 优先级
            if (StrUtil.isNotBlank(questionManagementImportDTO.getPriorityLevel())) {
                if (StrUtil.isBlank(priorityLevelDictMap.get(questionManagementImportDTO.getPriorityLevel()))) {
                    errorNotes.add("不存在此优先级");
                }
            }

            if (StrUtil.isNotBlank(questionManagementImportDTO.getPredictEndTimeStr())) {
                if (dateFormat(questionManagementImportDTO.getPredictEndTimeStr())) {
                    errorNotes.add("期望完成日期格式错误  正确格式：yyyy-MM-dd");
                } else {
                    questionManagementImportDTO.setPredictEndTime(DateUtil.parse(questionManagementImportDTO.getPredictEndTimeStr(), DATE_FORMAT));
                }
            }

            if (!CollectionUtils.isEmpty(errorNotes)) {
                ImportExcelErrorNoteVO errorNoteVO = new ImportExcelErrorNoteVO();
                errorNoteVO.setOrder(String.valueOf(j + 1));
                errorNoteVO.setErrorNotes(errorNotes);
                errorNoteVOList.add(errorNoteVO);
            }
        }
        return errorNoteVOList;
    }




    private boolean dateFormat(String str) {
        try {
            DateUtil.parseLocalDateTime(str, DATE_FORMAT);
            return false;
        } catch (Exception e) {
            return true;
        }
    }
}
