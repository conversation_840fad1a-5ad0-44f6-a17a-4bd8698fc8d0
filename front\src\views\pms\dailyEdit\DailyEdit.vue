<template>
    <OrionTable :options="tableOptions"></OrionTable>
  </template>
<script setup lang="ts">
import { OrionTable, IOrionTableOptions } from 'lyra-component-vue3'
import { ref, h } from 'vue'
import { Select } from 'ant-design-vue'

const tableOptions:IOrionTableOptions = {
  async api() {
    return [{
      id: '123',
      name: '张三',
    }];
  },
  isRowAdd: true,
  isAddRowMore: true,
  /**
   * 新增接口，在开启行添加时使用
   * @param record 添加的行数据
   * @param params 其他参数
   */
  addRowApi(record, params) {
    console.log('添加数据', {record, params})
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // new Date().valueOf().toString()
        resolve(record.map((item, index)=>index+(new Date().valueOf().toString())));
      }, 1000);
    });
  },
  columns: [
    {
      dataIndex: 'name',
      title: '输入框',
      //  开启编辑
      edit: true,
      // 表单组件
      editComponent: 'Input',
      // 组件参数
      editComponentProps: {
      },
    },
    {
      dataIndex: 'InputNumber',
      title: '数字输入框',
      edit: true,
      editComponent: 'InputNumber',
      editComponentProps: {
      },
      editRule: true
    },
    // {
    //   dataIndex: 'sex',
    //   title: '自定义的',
    //   edit: true,
    //   editRule: (text, record) => ({
    //     required: true,
    //   }),
    //   format(params) {
    //     if (params === '1') {
    //       return '男';
    //     }
    //     if (params === '0') {
    //       return '女';
    //     }
    //     return '-';
    //   },
    //   editComponent(methods) {
    //     const selectRef = ref();
    //     return h(Select, {
    //       class: 'w-full',
    //       ref: selectRef,
    //       value: methods.getComponentValue(),
    //       options: [
    //         {
    //           label: '男',
    //           value: '1',
    //         },
    //         {
    //           label: '女',
    //           value: '0',
    //         },
    //       ],
    //       onVnodeMounted() {
    //         // 判断是否符合
    //         if (methods.isFocus()) {
    //           selectRef.value.focus();
    //         }
    //       },
    //       // 选中
    //       onSelect(e) {
    //         // 数据保存
    //         methods.edit(e);
    //         // 触发保存，调用接口
    //         methods.save();
    //       },
    //       // 离开时
    //       onBlur(e) {
    //         if (!methods.isValueEdit()) {
    //           methods.setEdit(false);
    //         }
    //       },
    //     });
    //   },
    // },
  ],
  deleteToolButton: 'enable|disable',
  canResize: false,
  rowSelection: {},
  isSpacing:false
}
</script>


<style scoped>

</style>
