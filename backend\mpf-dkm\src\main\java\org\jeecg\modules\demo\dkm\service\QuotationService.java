package org.jeecg.modules.demo.dkm.service;

/**
 * @Description: 报价管理数据校验服务
 * @Author: tancheng
 * @Date: 2025-5-15
 * @Version: V1.0
 */
public interface QuotationService {
    
    /**
     * 检查报价管理表数据准确性
     * 检查报价名称重名、报价编号为空
     * @return 标准Markdown表格格式的字符串，多个表格间用空行分隔
     */
    String checkQuotationDataIntegrity();
    
    /**
     * 检查报价管理表数据准确性并导出Excel
     * 检查报价名称重名、报价编号为空
     * @return Excel文件路径
     */
    String checkQuotationDataIntegrityExcel();
} 