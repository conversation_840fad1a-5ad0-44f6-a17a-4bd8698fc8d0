package com.chinasie.orion.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinasie.orion.api.code.domain.vo.CodeSegmentVO;
import com.chinasie.orion.bo.CodeBo;
import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.bo.UserBo;
import com.chinasie.orion.constant.ClassNameConstant;
import com.chinasie.orion.constant.DictConstant;
import com.chinasie.orion.domain.dto.StakeholderDTO;
import com.chinasie.orion.domain.entity.Stakeholder;
import com.chinasie.orion.domain.vo.StakeholderVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.repository.StakeholderRepository;
import com.chinasie.orion.service.StakeholderService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.ExcelUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/06/10:42
 * @description:
 */
@Service
public class StakeholderServiceImpl extends OrionBaseServiceImpl<StakeholderRepository, Stakeholder> implements StakeholderService {

    @Resource
    private UserBo userBo;
    @Resource
    private ExcelUtils excelUtils;
    @Resource
    private DictBo dictBo;
    @Resource
    private CodeBo codeBo;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveStakeholder(StakeholderDTO stakeholderDTO) throws Exception {
        //todo编码规则
        List<CodeSegmentVO> codeRuleList = codeBo.getCodeRuleList(ClassNameConstant.STAKE_HOLDER, ClassNameConstant.NUMBER);
        if (!CollectionUtils.isEmpty(codeRuleList)) {
            String code = codeBo.getCode(codeRuleList);
            stakeholderDTO.setNumber(code);
        }
        String number = stakeholderDTO.getNumber();
        if ( StrUtil.isBlank(number)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "未获取到干系人的编码");
        }
        Stakeholder stakeholder = BeanCopyUtils.convertTo(stakeholderDTO, Stakeholder::new);
        this.save(stakeholder);
        return stakeholder.getId();
    }

    @Override
    public com.chinasie.orion.sdk.metadata.page.Page<StakeholderVO> getStakeholderPage(com.chinasie.orion.sdk.metadata.page.Page<StakeholderDTO> pageRequest) throws Exception {
        com.chinasie.orion.sdk.metadata.page.Page<StakeholderVO> resultPage = new com.chinasie.orion.sdk.metadata.page.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), 0L, new ArrayList<>());
        IPage<Stakeholder> stakeholderIPage = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());

        LambdaQueryWrapperX<Stakeholder> stakeholderLambdaQueryWrapper = new LambdaQueryWrapperX<>();
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), stakeholderLambdaQueryWrapper);
        }
        stakeholderLambdaQueryWrapper.eq(Stakeholder::getProjectId, pageRequest.getQuery().getProjectId());

        IPage<Stakeholder> pageResult = this.page(stakeholderIPage, stakeholderLambdaQueryWrapper);
        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(pageResult.getRecords())) {
            return resultPage;
        }
        List<Stakeholder> stakeholderList = pageResult.getRecords();
        List<StakeholderVO> stakeholderVOList = BeanCopyUtils.convertListTo(stakeholderList, StakeholderVO::new);
        List<String> userIdList = stakeholderVOList.stream().map(StakeholderVO::getCreatorId).collect(Collectors.toList());
        userIdList.addAll(stakeholderVOList.stream().map(StakeholderVO::getModifyId).collect(Collectors.toList()));
        Map<String, String> userVOIdAndNameMap = userBo.getNameByUserIdMap(userIdList.stream().distinct().collect(Collectors.toList()));
        Map<String, String> contactTypeValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.STAKEHOLDER_CONTACT_TYPES);

        stakeholderVOList.forEach(o -> {
            o.setContactTypeName(contactTypeValueToDesMap.get(o.getContactType()));
            o.setModifyName(userVOIdAndNameMap.get(o.getModifyId()));
            o.setCreatorName(userVOIdAndNameMap.get(o.getCreatorId()));
        });

        resultPage.setTotalSize(pageResult.getTotal());
        resultPage.setContent(stakeholderVOList);
        return resultPage;
    }



    @Override
    public StakeholderVO getStakeholderDetail(String id,String pageCode) throws Exception {
        Stakeholder stakeholderDTO = this.getById(id);
        if (Objects.isNull(stakeholderDTO)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        StakeholderVO stakeholderVO = new StakeholderVO();
        BeanCopyUtils.copyProperties(stakeholderDTO, stakeholderVO);
        List<String> userIdList = new ArrayList<>();
        userIdList.add(stakeholderVO.getCreatorId());
        userIdList.add(stakeholderVO.getModifyId());
        Map<String, String> userVOIdAndNameMap = userBo.getNameByUserIdMap(userIdList);
        Map<String, String> contactTypeValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.STAKEHOLDER_CONTACT_TYPES);
        stakeholderVO.setContactTypeName(contactTypeValueToDesMap.get(stakeholderVO.getContactType()));
        stakeholderVO.setCreatorName(userVOIdAndNameMap.get(stakeholderVO.getCreatorId()));
        stakeholderVO.setModifyName(userVOIdAndNameMap.get(stakeholderVO.getModifyId()));
        return stakeholderVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean editStakeholder(StakeholderDTO stakeholderDTO) throws Exception {
        Stakeholder stakeholder = BeanCopyUtils.convertTo(stakeholderDTO, Stakeholder::new);
        return this.updateById(stakeholder);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeStakeholder(List<String> idList) throws Exception {
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }
        return this.removeBatchByIds(idList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveStakeholderByExcel(MultipartFile file) throws Exception {
        Map<String, String> map = new HashMap<>();
        map.put("名称", "name");
        map.put("联系方式", "contactTypeName");
        map.put("联系信息", "contactInfo");
        map.put("地址", "address");
        map.put("描述", "remark");
        StakeholderVO stakeholderVO = new StakeholderVO();
        List<StakeholderVO> stakeholderVOList = new ArrayList<>(excelUtils.excelToPojo(file, stakeholderVO.getClass(), map));
        if (CollectionUtils.isEmpty(stakeholderVOList)) {
            return true;
        }
        Map<String, String> contactTypeValueToDesMap = dictBo.getDictDesToValueMap(DictConstant.STAKEHOLDER_CONTACT_TYPES);
        stakeholderVOList.forEach(o -> {
            o.setContactType(contactTypeValueToDesMap.get(o.getContactTypeName()));
            if (!StringUtils.hasText(o.getName()) || Objects.isNull(o.getContactType()) ||
                    !StringUtils.hasText(o.getContactInfo()) || !StringUtils.hasText(o.getAddress())) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "名称、联系方式、联系信息、地址不能为空！");
            }
            if (o.getName().length() > 64 || o.getContactInfo().length() > 64 || o.getAddress().length() > 255 || o.getRemark().length() > 255) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "名称或联系信息或地址或描述字符输入过长，请重新输入");
            }
            //todo编码规则
            o.setNumber(IdUtil.simpleUUID());
        });
        List<Stakeholder> stakeholderDTOList = BeanCopyUtils.convertListTo(stakeholderVOList, Stakeholder::new);
        this.saveBatch(stakeholderDTOList);
        return true;
    }

    @Override
    public List<StakeholderVO> getStakeholderList(StakeholderDTO stakeholderDTO) throws Exception {
        LambdaQueryWrapperX<Stakeholder> lambdaQueryWrapperX=new LambdaQueryWrapperX();
        lambdaQueryWrapperX.eqIfPresent(Stakeholder::getProjectId,stakeholderDTO.getProjectId());
        List<Stakeholder> list=this.list(lambdaQueryWrapperX);
        List<StakeholderVO> stakeholderVOS=BeanCopyUtils.convertListTo(list, StakeholderVO::new);
        return stakeholderVOS;
    }
}
