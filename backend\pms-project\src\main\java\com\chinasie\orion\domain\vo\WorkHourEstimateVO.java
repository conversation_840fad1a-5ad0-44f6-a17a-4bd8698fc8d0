package com.chinasie.orion.domain.vo;

import com.chinasie.orion.domain.entity.WorkHourEstimateDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;
import java.util.List;

/**
 * WorkHourEstimate Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-14 16:23:45
 */
@ApiModel(value = "WorkHourEstimateVO对象", description = "工时预估")
@Data
public class WorkHourEstimateVO extends ObjectVO implements Serializable{

    /**
     * 单据编号
     */
    @ApiModelProperty(value = "单据编号")
    private String number;

    /**
     * 成员id
     */
    @ApiModelProperty(value = "成员id")
    private String memberId;

    /**
     * 成员名称
     */
    @ApiModelProperty(value = "成员名称")
    private String memberName;

    /**
     * 工时时长
     */
    @ApiModelProperty(value = "工时时长")
    private Integer workHour;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    private Date startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    private Date endDate;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 成员角色
     */
    @ApiModelProperty(value = "成员角色")
    private String memberRoleName;

    /**
     * 工时预估明细
     */
    @ApiModelProperty(value = "工时预估明细")
    List<WorkHourEstimateDetail> detailList;
}
