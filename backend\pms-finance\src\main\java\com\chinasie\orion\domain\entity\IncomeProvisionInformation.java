package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * IncomeProvisionInformation Entity对象
 *
 * <AUTHOR>
 * @since 2024-09-29 11:32:18
 */
@TableName(value = "pmsx_income_provision_information")
@ApiModel(value = "IncomeProvisionInformationEntity对象", description = "收入计提信息表")
@Data

public class IncomeProvisionInformation extends  ObjectEntity  implements Serializable{

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @TableField(value = "year")
    private String year;

    /**
     * 凭证号
     */
    @ApiModelProperty(value = "凭证号")
    @TableField(value = "voucher_number")
    private String voucherNumber;

    /**
     * 挂账金额
     */
    @ApiModelProperty(value = "挂账金额")
    @TableField(value = "accrued_amt")
    private BigDecimal accruedAmt;

    /**
     * 已冲销金额
     */
    @ApiModelProperty(value = "已冲销金额")
    @TableField(value = "amortized_amt")
    private BigDecimal amortizedAmt;

    /**
     * 剩余未冲销金额
     */
    @ApiModelProperty(value = "剩余未冲销金额")
    @TableField(value = "remain_unamortized_amt")
    private BigDecimal remainUnamortizedAmt;

    /**
     * 本次冲销金额
     */
    @ApiModelProperty(value = "本次冲销金额")
    @TableField(value = "amortization_amount")
    private BigDecimal amortizationAmount;

    /**
     * 本次冲销金额（不含税）
     */
    @ApiModelProperty(value = "本次冲销金额（不含税）")
    @TableField(value = "amortization_amt_ex_tax")
    private BigDecimal amortizationAmtExTax;

    /**
     * 不冲销原因
     */
    @ApiModelProperty(value = "不冲销原因")
    @TableField(value = "no_amortization_reason")
    private String noAmortizationReason;

    /**
     * 收入填报Id
     */
    @ApiModelProperty(value = "收入填报Id")
    @TableField(value = "income_plan_id")
    private String incomePlanId;

    /**
     * 收入填报数据ID
     */
    @ApiModelProperty(value = "收入填报数据ID")
    @TableField(value = "income_plan_data_id")
    private String incomePlanDataId;

}
