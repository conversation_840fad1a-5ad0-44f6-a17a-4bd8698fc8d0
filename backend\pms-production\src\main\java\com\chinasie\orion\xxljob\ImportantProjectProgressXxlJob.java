package com.chinasie.orion.xxljob;

import com.chinasie.orion.constant.MessageNodeDict;
import com.chinasie.orion.domain.entity.ImportantProject;
import com.chinasie.orion.msc.api.MscBuildHandlerManager;
import com.chinasie.orion.repository.JobManageMapper;
import com.chinasie.orion.service.ImportantProjectService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ImportantProjectProgressXxlJob {


    @Autowired
    JobManageMapper jobManageMapper;

    @Autowired
    ImportantProjectService importantProjectService;

    @Autowired
    MscBuildHandlerManager mscBuildHandlerManager;


    @XxlJob(value = "importantProjectProgressXxlJob")
    public void importantProjectProgressXxlJob(){
        List<String> noProgressProject = jobManageMapper.getNoProgressProject();
        List<ImportantProject> importantProjects = importantProjectService.listByIds(noProgressProject);
        log.info("循环外");
        for (ImportantProject importantProject : importantProjects) {
            log.info("发送消息");
            mscBuildHandlerManager.send(importantProject, MessageNodeDict.NODE_PROJECT_PROGRESS);
        }
    }


}
