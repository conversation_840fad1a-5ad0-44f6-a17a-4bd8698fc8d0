package com.chinasie.orion.management.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.management.domain.entity.ProcurePkgGroup;
import com.chinasie.orion.management.domain.entity.ReqClarificationRecord;
import com.chinasie.orion.management.domain.entity.RequirementPayMangement;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * RequirementMangement VO对象
 *
 * <AUTHOR>
 * @since 2024-05-28 15:55:19
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "RequirementMangementVO对象", description = "主表")
@Data
public class RequirementMangementVO extends ObjectVO implements Serializable {

    /**
     * 需求编号
     */
    @ApiModelProperty(value = "需求编号")
    private String requirementNumber;


    /**
     * 需求标题
     */
    @ApiModelProperty(value = "需求标题")
    private String requirementName;


    /**
     * 需求来源
     */
    @ApiModelProperty(value = "需求来源")
    private String resSource;

    /**
     * 需求来源名称
     */
    @ApiModelProperty(value = "需求来源名称")
    private String resSourceName;


    /**
     * 开标时间
     */
    @ApiModelProperty(value = "开标时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date bidOpeningTm;


    /**
     * 报名开始日期
     */
    @ApiModelProperty(value = "报名开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signStartTime;


    /**
     * 报名结束日期
     */
    @ApiModelProperty(value = "报名结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signEndTime;


    /**
     * 报价截止时间
     */
    @ApiModelProperty(value = "报价截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signDeadlnTime;


    /**
     * 客户
     */
    @ApiModelProperty(value = "客户")
    private String custPerson;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    @Size(max = 512)
    private String custPersonName;


    /**
     * 客户范围
     */
    @ApiModelProperty(value = "客户范围")
    private String custScope;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String statusName;

    /**
     * 客户主要联系人
     */
    @ApiModelProperty(value = "客户主要联系人")
    private String custConPerson;

    /**
     * 客户主要联系人名称
     */
    @ApiModelProperty(value = "客户主要联系人名称")
    private String custConPersonName;


    /**
     * 客户主要联系人电话
     */
    @ApiModelProperty(value = "客户主要联系人电话")
    private String custContactPh;


    /**
     * 客户商务接口人
     */
    @ApiModelProperty(value = "客户商务接口人")
    private List<String> custBsPerson;

    /**
     * 客户商务接口人名称
     */
    @ApiModelProperty(value = "客户商务接口人名称")
    private List<String> custBsPersonName;

    /**
     * 客户技术接口人
     */
    @ApiModelProperty(value = "客户技术接口人")
    private List<String> custTecPerson;

    /**
     * 客户技术接口人名称
     */
    @ApiModelProperty(value = "客户技术接口人名称")
    private List<String> custTecPersonName;

    /**
     * 承接部门
     */
    @ApiModelProperty(value = "承接部门")
    private String undertakeDept;

    /**
     * 商务接口人
     */
    @ApiModelProperty(value = "商务接口人")
    private String businessPerson;
    /**
     * 商务接口人名称
     */
    @ApiModelProperty(value = "商务接口人名称")
    private String businessPersonName;


    /**
     * 技术接口人(技术负责人)
     */
    @ApiModelProperty(value = "技术接口人(技术负责人)")
    private String techRes;

    @ApiModelProperty(value = "技术接口人名称(技术负责人名称)")
    private String techResName;


    /**
     * 需求归属中心
     */
    @ApiModelProperty(value = "需求归属中心")
    private String reqOwnership;

    /**
     * 需求归属中心
     */
    @ApiModelProperty(value = "需求归属中心")
    private String reqOwnershipName;


    /**
     * 配合部门接口人
     */
    @ApiModelProperty(value = "配合部门接口人")
    private List<String> cooperatePerson;


    /**
     * 配合部门
     */
    @ApiModelProperty(value = "配合部门")
    private List<String> cooperateDpt;

    /**
     * 配合部门接口人
     */
    @ApiModelProperty(value = "配合部门接口人")
    private List<String> cooperatePersonName;


    /**
     * 配合部门名称
     */
    @ApiModelProperty(value = "配合部门名称")
    private List<String> cooperateDptName;


    /**
     * 需求状态
     */
    @ApiModelProperty(value = "需求状态")
    private String projectStatus;


    /**
     * 响应状态
     */
    @ApiModelProperty(value = "响应状态")
    private String responseStatus;


    /**
     * 富文本框，需求详情
     */
    @ApiModelProperty(value = "需求详情")
    @ExcelProperty(value = "需求详情 ")
    private String projectDetail;

    /**
     * 附件列表
     */
    @ApiModelProperty(value = "附件列表")
    @ExcelProperty(value = "附件列表 ")
    private List<FileVO> fileList;


    /**
     * 需求确认备注
     */
    @ApiModelProperty(value = "需求确认备注")
    private String confirmRemark;

    /**
     * 标段名称
     */
    @ApiModelProperty(value = "标段名称")
    private String sectionName;


    /**
     * 客户部门
     */
    @ApiModelProperty(value = "客户部门")
    private String custDptName;


    /**
     * 销售业务分类
     */
    @ApiModelProperty(value = "销售业务分类")
    private String salesClassification;

    /**
     * ECP状态
     */
    @ApiModelProperty(value = "ECP状态")
    private String ecpStatus;


    /**
     * ECP上次更新时间
     */
    @ApiModelProperty(value = "ECP上次更新时间")
    private Date ecpUpdateTime;

    /**
     * 客户范围
     */
    @ApiModelProperty(value = "客户范围")
    private String busScope;
    /**
     * 业务收入类型
     */
    @ApiModelProperty(value = "业务收入类型")
    private String ywsrlx;
    /**
     * 所属行业
     */
    @ApiModelProperty(value = "所属行业")
    private String industry;

    /**
     * 所属行业名称
     */
    @ApiModelProperty(value = "所属行业名称")
    private String industryName;

    /**
     * 客户关系(集团内外)
     */
    @ApiModelProperty(value = "客户关系(集团内外)")
    private String groupInOut;

    /**
     * 业务收入类型名称
     */
    @ApiModelProperty(value = "业务收入类型名称")
    private String ywsrlxName;


    /**
     * 客户级别名称
     */
    @ApiModelProperty(value = "客户级别名称")
    private String cusLevelName;

    /**
     * 客户范围名称
     */
    @ApiModelProperty(value = "客户范围名称")
    private String busScopeName;

    /**
     * 客户关系名称(集团内外)
     */
    @ApiModelProperty(value = "客户关系名称(集团内外)")
    private String groupInOutName;


    /**
     * 客户状态名称
     */
    @ApiModelProperty(value = "客户状态名称")
    private String cusStatusName;


    /**
     * 项目包组集合
     */
    @ApiModelProperty(value = "项目包组集合")
    private List<ProcurePkgGroup> procurePkgGroupList;

    /**
     * 澄清记录集合
     */
    @ApiModelProperty(value = "澄清记录集合")
    private List<ReqClarificationRecord> reqClarificationRecordList;

    /**
     * 是否发布公示
     */
    @ApiModelProperty(value = "是否发布公示")
    private String isPublished;

    /**
     * 来源ecp组织
     */
    @ApiModelProperty(value = "来源ecp组织")
    private String ecpGroup;

    /**
     * 来源ecp组织名称
     */
    @ApiModelProperty(value = "来源ecp组织名称")
    private String ecpGroupName;

    @ApiModelProperty(value = "保证金信息")
    private RequirementPayMangement requirementPayMangement;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessTypeName;

    @ApiModelProperty(value = "已报价 1.是 0.否")
    private Integer hadQuotation;

    /**
     * 已报价
     */
    @ApiModelProperty(value = "已报价")
    private String hadQuotationName;

    @ApiModelProperty(value = "客户-联系人")
    private List<RequirementManageCustContactVO> custContacts;

    @ApiModelProperty(value = "分发时间")
    private Date distributeTime;

    /**
     * 报名申请人
     */
    @ApiModelProperty(value = "报名申请人")
    private String applicantUser;

    /**
     * 报名申请人名称
     */
    @ApiModelProperty(value = "报名申请人名称")
    private String applicantUserName;

    /**
     * 报名部门
     */
    @ApiModelProperty(value = "报名部门")
    private String applicantDept;

    /**
     * 报名部门名称
     */
    @ApiModelProperty(value = "报名部门名称")
    private String applicantDeptName;

    /**
     * 报名时间
     */
    @ApiModelProperty(value = "报名时间")
    private Date applicantTime;

    /**
     * 优先级1低2中3高
     */
    @ApiModelProperty(value = "优先级1低2中3高")
    private String priority;

    /**
     * ECP系统删除标识
     */
    @ApiModelProperty(value = "ECP系统删除标识")
    private String deleteFlag;


    @ApiModelProperty(value = "是否关联人士")
    private String isPerson;

    /**
     * 是否中心商务
     */
    @ApiModelProperty(value = "是否中心商务")
    private Boolean isCenterBusiness;

    /**
     * ECP系统删除标识
     */
    @ApiModelProperty(value = "所级负责人")
    private String officeLeaderName;


    /**
     * 关闭原因
     */
    @ApiModelProperty(value = "关闭原因")
    private String closeReason;

    /**
     * 关闭原因
     */
    @ApiModelProperty(value = "关闭标识")
    private Boolean closeFlag;

    /**
     * 所级负责人
     */
    @ApiModelProperty(value = "所级负责人")
    private String officeLeader;

    @ApiModelProperty("需求确认时间")
    private Date feedBackTime;


    /**
     * 关联交易表单Id
     */
    @ApiModelProperty(value = "关联交易表单Id")
    private String transApprId;


    /**
     * 是否关联交易表单
     */
    @ApiModelProperty(value = "是否关联交易表单")
    private Boolean relTransAppr;


    /**
     * 不关联原因
     */
    @ApiModelProperty(value = "不关联原因")
    private String unrelatedReason;


    /**
     * 关联交易表单编号
     */
    @ApiModelProperty(value = "关联交易表单编号")
    private String transApprNumber;

}
