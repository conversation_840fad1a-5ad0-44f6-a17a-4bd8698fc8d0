<template>
  <div class="tips-box-wrap flex">
    <i><Icon
      :icon="icon"
      size="18"
    /></i>
    <div class="flex-f1">
      <slot name="default" />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import Icon from '/@/components/Icon';

export default defineComponent({
  name: 'TipsBoxMain',
  components: {
    Icon,
  },
  props: {
    icon: {
      type: String,
      default: 'fa-warning',
    },
  },
});
</script>

<style scoped lang="less">
  .tips-box-wrap {
    line-height: 30px;
    font-size: 13px;
    background: rgba(~`getPrefixVar('primary-color')`, 0.06);
    border: 1px solid rgba(~`getPrefixVar('primary-color')`, 0.13);
    padding: 10px;

    > div {
      padding-left: 10px;

      :deep(p) {
        margin: 0 !important;
        padding: 0 !important;
      }
    }

    > i {
      color: ~`getPrefixVar('primary-color')`;
    }
  }
</style>
