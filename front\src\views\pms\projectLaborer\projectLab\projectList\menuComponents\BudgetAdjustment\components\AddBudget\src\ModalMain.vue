<script setup lang="ts">
import {
  computed, ComputedRef, inject, nextTick, onMounted, provide, ref, Ref, watch,
} from 'vue';
import { Empty, Icon, BasicTree } from 'lyra-component-vue3';
import { ConfigProvider } from 'ant-design-vue';
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import Api from '/@/api';
import TableMain from './TableMain.vue';
import SelectedList from './SelectedList.vue';

import { findNode } from '/@/utils/helper/treeHelper';

const attrs: Record<string, any> = inject('attrs', {});
const treeData: Ref<any[]> = ref([]);
provide('treeData', treeData);

const tableRef: Ref = ref();
const selectedRef: Ref = ref();
const treeRef: Ref = ref();
const expandedKeys: Ref<string[]> = ref([]);
const selectedKeys: Ref<string[]> = ref([]);
const selectNode: ComputedRef<Record<string, any>> = computed(() => {
  let obj = {};
  findNode(treeData.value, (node) => {
    if (node.id === selectedKeys.value[0]) {
      obj = node;
    }
  }, { pid: 'parentId' });
  return obj;
});

onMounted(() => {
  tableRef.value?.updateTable();
});
function defaultTreeApi() {
  return new Api('/pmi/associationObject/tree').fetch('', '', 'GET');
}

// 表格选择
function selectTable(rows: Record<string, any>[]) {
  selectedRef.value.setData(rows);
}

// 更新表格选择项
function updateTableSelect(rows: Record<string, any>[] = []) {
  tableRef.value?.setSelectedRows(rows);
}

// 获取树形数据
async function getTreeData() {
  const result = await (attrs.treeApi || defaultTreeApi)();
  if (result instanceof Array) {
    treeData.value = result?.map((item: Record<string, any>) => {
      if (!attrs.treeApi && item.parentId === '0') {
        item.selectable = false;
      }
      return item;
    });
  } else {
    treeData.value = [];
  }
  return treeData.value;
}

// 展开折叠切换
function changeExpand(id: string, selectable: boolean | undefined) {
  if (selectable === undefined) return;
  const index = expandedKeys.value.indexOf(id);
  if (index === -1) {
    expandedKeys.value.push(id);
  } else {
    expandedKeys.value.splice(index, 1);
  }
  treeRef.value?.closeAll();
  for (let i = 0; i < expandedKeys.value?.length; i++) {
    treeRef.value?.onExpandByKey(expandedKeys.value[i]);
  }
}

// 监听展开按钮
function expand(keys: string[]) {
  expandedKeys.value = keys;
}

defineExpose({
  getSelectedData() {
    return selectedRef.value.getData();
  },
  getTreeNode() {
    return selectNode.value;
  },
});
</script>

<template>
  <ConfigProvider :locale="zhCN">
    <div class="modal-main-wrap">
      <div class="center-wrap">
        <TableMain
          ref="tableRef"
          :node="selectNode"
          @update="updateTableSelect(selectedRef.getData())"
          @selectTable="selectTable"
        />
      </div>
      <div class="right-wrap">
        <SelectedList
          ref="selectedRef"
          @updateTableSelect="updateTableSelect"
        />
      </div>
    </div>
  </ConfigProvider>
</template>

<style scoped lang="less">
.modal-main-wrap {
  width: 100%;
  height: 100%;
  display: flex;

  .left-wrap, .right-wrap {
    width: 220px;
    flex-shrink: 0;
  }

  .left-wrap {
    display: flex;
    flex-direction: column;
  }

  .center-wrap {
    flex-grow: 1;
    width: 0;
    border-left: 1px solid #f0f0f0;
    border-right: 1px solid #f0f0f0;
  }
}
</style>
