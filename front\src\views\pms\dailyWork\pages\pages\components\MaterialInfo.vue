<script setup lang="ts">
import { BasicCard } from 'lyra-component-vue3';
import { inject, reactive } from 'vue';

const detailsData: Record<string, any> = inject('detailsData');

const basicInfo = reactive({
  list: [
    {
      label: '资产类型',
      field: 'assetTypeName',
    },
    {
      label: '资产代码',
      field: 'assetCode',
    },
    {
      label: '资产编码/条码',
      field: 'number',
    },
    {
      label: '资产名称',
      field: 'assetName',
    },
    {
      label: '成本中心名称',
      field: 'costCenterName',
    },
    {
      label: '数量',
      field: 'demandNum',
    },
    {
      label: '规格型号',
      field: 'specificationModel',
    },
    {
      label: '是否需要检定',
      field: 'isVerification',
      isBoolean: true,
    },
    {
      label: '下次检定日期',
      field: 'nextVerificationDate',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '物资所在基地',
      field: 'baseName',
    },
    {
      label: '进入基地时间',
      field: 'actInDate',
      formatTime: 'YYYY-MM-DD',
    },
  ],
  dataSource: detailsData,
});
</script>

<template>
  <BasicCard
    title="物资基本信息"
    :grid-content-props="basicInfo"
    :is-border="false"
  />
</template>

<style scoped lang="less">

</style>
