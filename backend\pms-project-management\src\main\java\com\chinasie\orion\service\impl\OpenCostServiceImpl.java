package com.chinasie.orion.service.impl;

import com.chinasie.orion.domain.dto.OpenCostDTO;
import com.chinasie.orion.domain.entity.OpenCost;
import com.chinasie.orion.domain.vo.OpenCostVO;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.OpenCostMapper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.OpenCostService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * OpenCost 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28 15:17:25
 */
@Service
@Slf4j
public class OpenCostServiceImpl extends  OrionBaseServiceImpl<OpenCostMapper, OpenCost>   implements OpenCostService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public OpenCostVO detail(String id, String pageCode) throws Exception {
        OpenCost openCost =this.getById(id);
        OpenCostVO result = BeanCopyUtils.convertTo(openCost,OpenCostVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param openCostDTO
     */
    @Override
    public  String create(OpenCostDTO openCostDTO) throws Exception {
        OpenCost openCost =BeanCopyUtils.convertTo(openCostDTO,OpenCost::new);
        this.save(openCost);

        String rsp=openCost.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param openCostDTO
     */
    @Override
    public Boolean edit(OpenCostDTO openCostDTO) throws Exception {
        OpenCost openCost =BeanCopyUtils.convertTo(openCostDTO,OpenCost::new);

        this.updateById(openCost);

        String rsp=openCost.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<OpenCostVO> pages( Page<OpenCostDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<OpenCost> condition = new LambdaQueryWrapperX<>( OpenCost. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(OpenCost::getCreateTime);


        Page<OpenCost> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), OpenCost::new));

        PageResult<OpenCost> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<OpenCostVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<OpenCostVO> vos = BeanCopyUtils.convertListTo(page.getContent(), OpenCostVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "开口项费用导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", OpenCostDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        OpenCostExcelListener excelReadListener = new OpenCostExcelListener();
        EasyExcel.read(inputStream,OpenCostDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<OpenCostDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("开口项费用导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<OpenCost> openCostes =BeanCopyUtils.convertListTo(dtoS,OpenCost::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::OpenCost-import::id", importId, openCostes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<OpenCost> openCostes = (List<OpenCost>) orionJ2CacheService.get("pmsx::OpenCost-import::id", importId);
        log.info("开口项费用导入的入库数据={}", JSONUtil.toJsonStr(openCostes));

        this.saveBatch(openCostes);
        orionJ2CacheService.delete("pmsx::OpenCost-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::OpenCost-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<OpenCost> condition = new LambdaQueryWrapperX<>( OpenCost. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(OpenCost::getCreateTime);
        List<OpenCost> openCostes =   this.list(condition);

        List<OpenCostDTO> dtos = BeanCopyUtils.convertListTo(openCostes, OpenCostDTO::new);

        String fileName = "开口项费用数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", OpenCostDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<OpenCostVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class OpenCostExcelListener extends AnalysisEventListener<OpenCostDTO> {

        private final List<OpenCostDTO> data = new ArrayList<>();

        @Override
        public void invoke(OpenCostDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<OpenCostDTO> getData() {
            return data;
        }
    }


}
