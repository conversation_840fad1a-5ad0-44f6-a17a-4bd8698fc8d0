<template>
  <div
    v-loading="loading"
    class="complete-plan"
  >
    <BasicForm
      @register="register"
    />
  </div>
</template>
<script lang="ts" setup>
import { BasicForm, openSelectUserModal, useForm } from 'lyra-component-vue3';
import {
  Ref, ref, onMounted, computed, h,
} from 'vue';
import { message } from 'ant-design-vue';
import Api from '/@/api';

const props = withDefaults(defineProps<{
    record:object
}>(), {
  record: () => ({}),
});

const notifyUserId:Ref<string[]> = ref([]);
const loading:Ref<boolean> = ref(false);
const [
  register,
  {
    validate, setFieldsValue, getFieldsValue, validateFields,
  },
] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas: [
    {
      field: 'remark',
      component: 'InputTextArea',
      label: '描述',
      colProps: {
        span: 24,
      },
      required: true,
      componentProps: {
        maxlength: 500,
        placeholder: '请输入原因',
        showCount: true,
        rows: 4,
      },
    },

    {
      field: 'notifyUserName',
      label: '知会人员',
      required: true,
      colProps: { span: 24 },
      componentProps: {
        allowClear: true,
        onClick() {
          openSelectUser();
        },
        addonAfter: h(
          'span',
          {
            onClick: () => {
              openSelectUser();
            },
          },
          '请选择',
        ),
        async onChange(value) {
          message.info('请选择');
          await setFieldsValue({ notifyUserName: '' });
        },
      },
      component: 'Input',
    },
  ],
});
function openSelectUser() {
  openSelectUserModal([], {
    okHandle(data:any[]) {
      if (data.length === 0) {
        message.warning('请选择人员');
        return new Promise((resolve, reject) => { reject(false); });
      }
      notifyUserId.value = data.map((item) => item.id);
      setFieldsValue({ notifyUserName: data.map((item) => item.name).join(',') });
    },
  });
}

onMounted(async () => {
});

defineExpose({
  async onSubmit() {
    let params = await validateFields();
    params.notifyUserId = notifyUserId.value;
    params.projectSchemeId = props.record.id;
    await new Api('/pms').fetch(params, 'collaborativeCompilationTask/urgePlan', 'POST');
    message.success('催办成功');
  },
});
</script>
<style lang="less" scoped>
.complete-plan{
  padding-top: 1px;
}
//.upload-list{
//  height: 200px;
//  overflow: hidden;
//}

.task-item {
  display: flex;
  line-height: 30px;
  min-height: 30px;
  .item-title {
    padding-right: 5px;
    color: #000000a5;
    width: 135px;
  }
  .item-value {
    flex: 1;
    width: calc(~'100% - 135px');
  }
}
</style>