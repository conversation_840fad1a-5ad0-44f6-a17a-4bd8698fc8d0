package com.chinasie.orion.msc;

import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.constant.RequirementNodeDict;
import com.chinasie.orion.domain.dto.ContractMilestoneDTO;
import com.chinasie.orion.domain.entity.ContractMilestone;
import com.chinasie.orion.management.constant.MsgHandlerConstant;
import com.chinasie.orion.management.domain.dto.RequirementMangementDTO;
import com.chinasie.orion.msc.api.MscBuildHandler;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
    public class MileStoneMsgHandler implements Msc<PERSON>uildHandler<ContractMilestone> {
    @Override
    public SendMessageDTO buildMsc(ContractMilestone contractMilestone, Object... objects) {
        Map<String,Object> messageMap = new HashMap<>();
        String userId =(String) objects[0];
        //todo 表格数据处理
        List<ContractMilestone> object = (List<ContractMilestone>)objects[1];
        SendMessageDTO sendMessageDTO = SendMessageDTO.builder()
                .messageMap(messageMap)
                .titleMap(messageMap)
                .businessId(contractMilestone.getId())
                .messageUrl("/pas/milestones-details?id=" + contractMilestone.getContractId())
                .messageUrlName("里程碑详情")
                .senderTime(new Date())
                .senderId(contractMilestone.getCreatorId())
                .recipientIdList(Collections.singletonList(userId))
                .platformId(contractMilestone.getPlatformId())
                .orgId(contractMilestone.getOrgId())
                .build();
        return sendMessageDTO;
    }

    @Override
    public String support() {
        return MsgHandlerConstant.NOOD_ONE;
    }
}
