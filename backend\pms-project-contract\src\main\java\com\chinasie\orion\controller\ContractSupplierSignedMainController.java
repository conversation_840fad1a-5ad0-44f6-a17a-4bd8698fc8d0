package com.chinasie.orion.controller;

import com.chinasie.orion.domain.vo.ContractSupplierSignedMainVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.service.ContractSupplierSignedMainService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * ContractSupplierSignedMain 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24 10:23:42
 */
@RestController
@RequestMapping("/contractSupplierSignedMain")
@Api(tags = "乙方签约主体")
public class ContractSupplierSignedMainController {

    @Autowired
    private ContractSupplierSignedMainService contractSupplierSignedMainService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(
            success = "【{USER{#logUserId}}】查看乙方签约主体详情，业务编号：{#id}",
            type = "ContractSupplierSignedMain",
            subType = "详情",
            bizNo = "{#id}"
    )
    public ResponseDTO<ContractSupplierSignedMainVO> detail(@PathVariable(value = "id") String id) throws Exception {
        ContractSupplierSignedMainVO rsp = contractSupplierSignedMainService.detail(id);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 根据合同id获取详情
     *
     * @param contractId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "根据合同id获取详情")
    @RequestMapping(value = "/contractId/{contractId}", method = RequestMethod.GET)
    @LogRecord(
            success = "【{USER{#logUserId}}】根据合同编号查看乙方签约主体详情，合同编号：{#contractId}",
            type = "ContractSupplierSignedMain",
            subType = "根据合同id获取详情",
            bizNo = "{#contractId}"
    )
    public ResponseDTO<ContractSupplierSignedMainVO> detailByContractId(@PathVariable(value = "contractId") String contractId) throws Exception {
        ContractSupplierSignedMainVO rsp = contractSupplierSignedMainService.detailByContractId(contractId);
        return new ResponseDTO<>(rsp);
    }
}
