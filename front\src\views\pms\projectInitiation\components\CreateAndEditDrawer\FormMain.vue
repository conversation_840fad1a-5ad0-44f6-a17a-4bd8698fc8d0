<script setup lang="ts">
import {
  BasicForm, FormSchema, useForm, useModal, SelectUserModal, getDict,
} from 'lyra-component-vue3';
import TableRender from './TableRender.vue';
import dayjs from 'dayjs';
import {
  h, nextTick, onMounted, ref, Ref,
} from 'vue';
import { CheckProjectModal } from '..';
import Api from '/@/api';
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';

const props = defineProps<{
  detail: any
}>();

interface UserData {
  id: string,
  name: string
}

const [registerCheckProject, { openModal: openCheckProjectModal }] = useModal();
const [registerSelectUser, { openModal: openSelectUserModal }] = useModal();

const tableRef: Ref = ref();
const deptOptions: Ref<any[]> = ref([]);
// 已选择项目id
const selectProjectId: Ref<string> = ref();
// 已选择项目负责人
const selectUser: Ref<UserData> = ref();
const schemas: FormSchema[] = [
  {
    field: 'title',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(DetailsLayout, {
        title: '项目关键信息',
        isFormItem: true,
      });
    },
  },
  {
    field: 'projectNumber',
    component: 'InputSearch',
    label: '项目编号',
    required: true,
    componentProps: {
      disabled: props.detail?.operationType === 'fixed' || props.detail?.type === 'goDetails',
      readOnly: true,
      allowClear: false,
      onSearch(value, event) {
        if (event.type === 'click') {
          openCheckProjectModal(true, {});
        }
      },
    },
  },
  {
    field: 'projectName',
    component: 'Input',
    label: '项目名称',
    required: true,
    componentProps: {
      disabled: true,
      allowClear: false,
      onChange() {
        selectProjectId.value = undefined;
      },
      onSearch(value, event) {
        if (event.type === 'click') {
          openCheckProjectModal(true, {});
        }
      },
    },
  },
  // {
  //   field: 'estimateMoney',
  //   component: 'InputNumber',
  //   label: '项目预估金额',
  //   componentProps: {
  //     disabled: props.detail?.type === 'goDetails', // props.detail?.id === undefined,
  //     style: 'width:100%',
  //     addonAfter: '元',
  //     precision: 0,
  //     min: 0,
  //     formatter(value) {
  //       return value.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  //     },
  //     maxLength: 15,
  //   },
  // },
  {
    field: 'projectType',
    component: 'ApiSelect',
    label: '项目类型',
    componentProps: {
      disabled: props.detail?.type === 'goDetails', // props.detail?.id === undefined,
      allowClear: false,
      api: () => new Api('/pms/dict/code').fetch('', 'pms_project_type', 'GET'),
      labelField: 'description',
    },
  },
  {
    field: 'createTime',
    component: 'DatePicker',
    label: '发起日期',
    required: true,
    defaultValue: props.detail?.type === 'goDetails' ? dayjs().format('YYYY-MM-DD') : '',
    componentProps: {
      disabled: props.detail?.type === 'goDetails', // props.detail?.id === undefined,
    },
  },
  // {
  //   field: 'projectSource',
  //   component: 'ApiSelect',
  //   label: '项目来源',
  //   componentProps: {
  //     disabled: props.detail?.type === 'goDetails', // props.detail?.id === undefined,
  //     api: async () => await getDict('dict1714906542609989632'),
  //     labelField: 'description',
  //   },
  // },
  {
    field: 'projectStartTime',
    component: 'DatePicker',
    label: '项目开始时间',
    // required: true,
    componentProps: ({ formModel }) => ({
      disabledDate: (date) => (formModel.projectEndTime ? dayjs(date).valueOf() >= dayjs(formModel.projectEndTime).valueOf() : false),
    }),
  },
  {
    field: 'projectEndTime',
    component: 'DatePicker',
    label: '项目结束时间',
    // required: true,
    componentProps: ({ formModel }) => ({
      disabledDate: (date) => (formModel.projectStartTime ? dayjs(date).valueOf() <= dayjs(formModel.projectStartTime).valueOf() : false),
    }),
  },
  {
    field: 'resPersonName',
    component: 'InputSearch',
    label: '项目负责人',
    // required: true,
    componentProps: {
      placeholder: '请选择',
      allowClear: false,
      onFocus(e) {
        e.target.blur();
        openSelectUserModal(true);
      },
    },
  },
  {
    field: 'resDept',
    component: 'Select',
    label: '责任部门',
    // required: true,
    componentProps: {
      disabled: true,
      options: deptOptions,
    },
  },
  {
    field: 'approvalReason',
    component: 'Input',
    label: '立项理由',
    colProps: {
      span: 24,
    },
  },
  {
    field: 'attachments',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    render() {
      return h(TableRender, { ref: tableRef });
    },
  },

];

const [register, { setFieldsValue, validate }] = useForm({
  layout: 'vertical',
  schemas,
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
});

onMounted(() => {
  (props.detail?.id || props.detail?.operationType === 'fixed') && initForm();
});

// 初始化表单
async function initForm() {
  await setFieldsValue(props.detail);
  selectProjectId.value = props.detail?.projectId;
  deptOptions.value = [
    {
      label: props.detail?.resDeptName,
      value: props.detail?.resDept,
    },
  ];
  selectUser.value = {
    id: props.detail?.resPerson,
    name: props.detail?.resPersonName,
  };
  await nextTick();
  tableRef.value.setData(props.detail?.materialList);
}

// 选择项目回调
function checkProjectCallback(project, dept, user) {
  selectProjectId.value = project.id;
  deptOptions.value = dept;
  selectUser.value = user;

  setFieldsValue({
    resDept: dept[0]?.value ?? '', // 责任部门
    resPersonName: user.name, // 责任人
    projectName: project.name, // 项目名称
    projectNumber: project.number, // 项目编号
    estimateMoney: project.estimateMoney, // 预估金额
    projectType: project.projectType, // 项目类型
    projectSource: project.projectSource, // 项目来源
    projectStartTime: project.projectStartTime, // 结束时间
    projectEndTime: project.projectEndTime, // 开始时间
  });
}

// 选择人员回调
function selectUserCallback(user) {
  deptOptions.value = user[0]?.organizations?.filter((item) => !item.virtually)?.map((item) => ({
    label: item.name,
    value: item.id,
  }));
  selectUser.value = {
    id: user[0].id,
    name: user[0].name,
  };
  setFieldsValue({
    resPersonName: user[0].name,
    resDept: user[0]?.organizations?.[0]?.id,
  });
}

defineExpose({
  validate,
  selectUser,
  selectProjectId,
  getTableData: () => tableRef.value.getData(),
});
</script>

<template>
  <BasicForm @register="register" />
  <CheckProjectModal
    :onCheckProjectCallback="checkProjectCallback"
    @register="registerCheckProject"
  />
  <SelectUserModal
    selectType="radio"
    @ok="selectUserCallback"
    @register="registerSelectUser"
  />
</template>

<style scoped lang="less">

</style>
