<template>
  <div
    class="add-body"
    style="height: 425px;overflow: hidden"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      :dataSource="tableSource"
    >
      <template #toolbarLeft>
        <div class="source-table-slots">
          <!--            <BasicButton-->
          <!--              icon="add"-->
          <!--              @click="() => openTemplateModal(true)"-->
          <!--            >-->
          <!--              选择计划模板-->
          <!--            </BasicButton>-->
          <div class="flex flex-ac top-select">
            <div>
              <BasicButton
                type="primary"
                icon="add"
                @click="() => addNewRow('milestone')"
              >
                添加一行
              </BasicButton>
            </div>
            <div class="m-b-r">
              <span>模板：</span>
              <a-select
                ref="select"
                v-model:value="templateId"
                allowClear
                style="width: 180px"
                class="table-input"
                placeholder="请选择模板名称"
                :options="templateOptions"
                @change="(value) => onChangeTemplateValue(value)"
              />
            </div>
            <div>
              <BasicButton
                icon="delete"
                :disabled="!selectedRowKeys.length"
                @click="deleteBatchNode"
              >
                删除
              </BasicButton>
            </div>
            <div class="m-b-r">
              <a-tag>切换模板会清空之前的数据</a-tag>
            </div>
          </div>
        </div>
      </template>
      <template #name="{ record }">
        <div
          class="flex flex-ac"
        >
          <!--计划图标-->
          <Icon
            v-if="record['nodeType']==='plan'"
            icon="orion-icon-carryout"
            class="primary-color"
            size="16"
          />
          <!--里程碑图标-->
          <Icon
            v-if="record['nodeType']==='milestone'"
            color="#FFB118"
            size="16"
            icon="orion-icon-flag"
          />
          <a-input
            v-model:value="record.name"
            placeholder="请输入计划名称"
            class="table-input ml10"
            @change="(value) => onChangeValue(value, record, 'name')"
          />
        </div>
      </template>
      <template #nodeType="{ record }">
        <a-select
          ref="select"
          v-model:value="record.nodeType"
          :disabled="true"
          style="width: 100%"
          class="table-input"
          :options="[
            {
              label:'计划',
              value:'plan'
            },
            {
              label:'里程碑',
              value:'milestone'
            }
          ]"
        />
      </template>
      <template #schemeActivityList="{ record }">
        <a-select
          ref="select"
          v-model:value="record.schemeActivityList"
          style="width: 100%"
          class="table-input"
          mode="multiple"
          :options="schemeActivityListOptions"
          :fieldNames="{ label: 'description', value: 'value' }"
        />
      </template>
      <!--开始时间-->
      <template #beginTime="{ record }">
        <a-date-picker
          v-model:value="record.beginTime"
          class="table-input"
          @change="(value) => onChangeValueForEndTime(value, record)"
        />
      </template>
      <template #durationDays="{ record }">
        <a-input-number
          v-model:value="record.durationDays"
          min="0"
          placeholder="请输入工期"
          class="table-input ml10"
          @change="(value) => onChangeValueForEndTime(value, record)"
        />
      </template>
      <!--结束时间-->
      <template #endTime="{ record }">
        <a-date-picker
          v-model:value="record.endTime"

          class="table-input"
          :disabled-date="
            (current) => getDisableDate(current, record.beginTime)
          "
          @change="(value) => onChangeValueForDays(value, record)"
        />
      </template>
      <!--计划描述-->
      <template #schemeDesc="{ record }">
        <a-input
          v-model:value="record.schemeDesc"
          :placeholder="parentData?.[0]?.id===record?.id?'':'请输入计划描述'"
          class="table-input"
        />
        <!--          @change="(value) => onChangeValue(value, record, 'schemeDesc')"-->
      </template>
    </OrionTable>
  </div>
</template>
<script lang="ts">
import { cloneDeep, throttle } from 'lodash-es';
import {
  computed, defineComponent, inject, reactive, Ref, ref, onMounted, defineExpose,
} from 'vue';
import {
  DatePicker, Input, message, Tag, Select, InputNumber,
} from 'ant-design-vue';
import {
  BasicButton, BasicModal, Icon, OrionTable, useModal, useModalInner,
} from 'lyra-component-vue3';

import Api from '/@/api';
import dayjs from 'dayjs';
import { postProjectSchemeMilestoneTemplatelist } from '/@/views/pms/projectLaborer/projectLab/api';

export default defineComponent({
  name: 'AddModal',
  components: {
    OrionTable,
    ASelect: Select,
    BasicButton,
    AInput: Input,
    ADatePicker: DatePicker,
    ATag: Tag,
    AInputNumber: InputNumber,
    Icon,
  },
  props: {
    schemeActivityListOptions: {
      type: Array,
      default: () => [],
    },
    visible: {
      type: Boolean,
      default: false,
    },
    parentIds: {
      type: Array,
      default: () => [],
    },
    parentData: {
      type: Object,
      default: () => {
      },
    },
    projectData: {
      type: Object,
      default: () => {
      },
    },
    from: {
      type: String,
      default: () => '',
    },
  },
  emits: ['handleColse'],
  setup(props, { emit }) {
    const projectData = ref<any>({});
    const parentIds = ref([]);
    const parentData = ref<any>({});
    const from = ref<string>('');
    const tableSource = ref([]);
    const tableRef = ref();
    const selectedRowKeys = ref([]);
    const projectId = inject('projectId') as string;
    const templateOptions = ref<any>([]);
    const [registerTemplate, { openModal: setSelectTemplate }] = useModal();

    const templateId = ref<string>('');
    const tableOptions = ref({
      deleteToolButton: 'add|enable|disable|delete',
      rowSelection: {
        selectedRowKeys,
        onChange: (keys = []) => {
          selectedRowKeys.value = keys;
        },
      },
      showSmallSearch: false,
      pagination: false,
      customHeaderCell(column) {
        if (column.required) {
          return {
            className: 'surely-table-cell surely-table-header-cell required',
          };
        }
      },
      columns: [
        {
          title: '计划名称',
          dataIndex: 'name',
          required: true,
          slots: { customRender: 'name' },
        },
        {
          title: '计划类型',
          dataIndex: 'nodeType',
          required: true,
          align: 'left',
          width: 110,
          slots: { customRender: 'nodeType' },
        },
        {
          title: '计划活动项',
          dataIndex: 'schemeActivityList',
          required: true,
          align: 'left',
          width: 110,
          slots: { customRender: 'schemeActivityList' },
        },
        {
          title: '开始时间',
          align: 'left',
          required: true,
          dataIndex: 'beginTime',
          width: 140,
          slots: { customRender: 'beginTime' },
        },
        {
          title: '工期（天）',
          align: 'left',
          dataIndex: 'durationDays',
          slots: { customRender: 'durationDays' },
          width: 140,
        },
        {
          title: '结束时间',
          align: 'left',
          dataIndex: 'endTime',
          width: 140,
          required: true,
          slots: { customRender: 'endTime' },
        },
        {
          title: '计划描述',
          align: 'left',
          width: 140,
          dataIndex: 'schemeDesc',
          slots: { customRender: 'schemeDesc' },
        },
      ],
    });
    const rspDeptOptions = ref([]);
    const isTimeOut = ref(false);
    const showTips = ref(false);
    const deptList = ref([]);
    const userList = reactive({});

    async function onChangeTemplateValue(value) {
      // debugger;
      if (value) {
        tableSource.value = [];

        let res = await new Api('/pms')
          .fetch({ }, `projectSchemeMilestoneNode/milestone/list/${value}`, 'POST');
        tableRef.value.setTableData(res);
      }

      // tableSource.value = res;
    }

    // 添加一行
    const addNewRow = (nodeType) => {
      const list = tableRef.value.getDataSource();
      list.push({
        id: new Date().getTime()
          .toString(),
        name: '',
        nodeType,
        rspSubDept: '',
        rspSectionId: '',
        rspUser: '',
        rspUserCode: '',
        processFlag: false,
        participantUserList: [],
        oldCode: '',
        beginTime: '',
        durationDays: '',
        endTime: '',
        schemeDesc: '',
        projectId,
      });
      tableRef.value.setTableData(list);
      // tableSource.value = list;
    };
    async function getTemplateOptions() {
      try {
        templateOptions.value = await postProjectSchemeMilestoneTemplatelist();
        templateOptions.value = templateOptions.value.map((item) => ({
          ...item,
          label: item.templateName,
          value: item.id,
        }));
      } finally {
      }
    }
    const deleteBatchNode = () => {
      const list = cloneDeep(tableRef.value.getDataSource());

      selectedRowKeys.value.forEach((item) => {
        const index = list.findIndex((val) => val.id === item);
        if (index !== -1) {
          list.splice(index, 1);
        }
      });

      tableRef.value.setTableData(list);
      // selectedRowKeys.value = [];
    };
    const getDayTime = (time = ''): number => {
      const date = new Date(time);
      return dayjs(dayjs(date)
        .format('YYYY-MM-DD'))
        .valueOf();
    };

    const getShowTips = (val, type) => {
      const value = getDayTime(val);
      const projectEndValue = getDayTime(
        projectData.value.predictEndTime || '',
      );
      const projectStartValue = getDayTime(
        projectData.value.predictStartTime || '',
      );
      const parentBeginValue = getDayTime(
        parentData.value.length ? parentData.value[0].beginTime : '',
      );
      const parentEndValue = getDayTime(
        parentData.value.length ? parentData.value[0].endTime : '',
      );
      if (type === 1) {
        if (
          value < projectStartValue
            && !parentBeginValue
            && !parentData.value.length
        ) {
          return true;
        }
        if (
          parentData.value.length
            && parentBeginValue
            && value < parentBeginValue
        ) {
          return true;
        }
      } else {
        if (
          value > projectEndValue
            && !parentEndValue
            && !parentData.value.length
        ) {
          return true;
        }
        if (
          parentData.value.length
            && parentEndValue
            && value > parentEndValue
        ) {
          return true;
        }
      }

      return false;
    };

    const onChangeValue = throttle((e, record, keyName) => {
      let value = e?.target?.value
        ? e?.target?.value
        : typeof e === 'string'
          || keyName === 'beginTime'
          || keyName === 'endTime'
          ? e
          : '';
      if (keyName === 'name' && value?.length > 100) {
        value = value.slice(0, 100);
      }

      tableSource.value.forEach((item) => {
        if (item.id === record.id) {
          if (keyName === 'rspSubDept') {
            item.rspUser = '';
          }
          if (keyName === 'beginTime') {
            item.endTime = '';
          }
          item[keyName] = value;
        }
      });
      const list = [];
      tableSource.value.forEach((item) => {
        list.push(getShowTips(item.beginTime, 1));
        list.push(getShowTips(item.endTime, 2));
      });
      showTips.value = list.includes(true);
    }, 500);
    onMounted(() => {
      getTemplateOptions();
    });
    function getChildrenDisableDate(current, record) {
      if (record.children) {

      }
    }

    const onChangeValueForEndTime = throttle((e, record) => {
      if (record.durationDays && record.durationDays !== 0 && record.beginTime) {
        let params = {
          businessId: '1',
          start: dayjs(record.beginTime).format('YYYY-MM-DD'),
          workdayNum: record.durationDays,
        };
        new Api('/pmi').fetch(
          params,
          'api-pmi/holidays/compute/end-date',
          'POST',
        ).then((res) => {
          // console.log(res);
          record.endTime = dayjs(res);
        });
      } else {
        record.endTime = '';
      }
    }, 500);

    const onChangeValueForDays = throttle((e, record) => {
      // // 获取开始时间 没有接口
      // if (record.durationDays && record.endTime && !record.beginTime) {
      //   const params = {
      //     businessId: '1',
      //     workdayNum: record.durationDays,
      //     endDate: dayjs(record.endTime).format('YYYY-MM-DD'),
      //   };
      //
      //   new Api('/pmi').fetch(
      //     params,
      //     'api-pmi/holidays/compute/start-date',
      //     'POST',
      //   ).then((res) => {
      //     // console.log(res);
      //     record.beginTime = dayjs(res);
      //   });
      // }
      // 获取工期时间
      if (record.beginTime && record.endTime) {
        const params = {
          businessId: '1',
          startDate: dayjs(record.beginTime).format('YYYY-MM-DD'),
          endDate: dayjs(record.endTime).format('YYYY-MM-DD'),
        };

        new Api('/pmi').fetch(
          params,
          'api-pmi/holidays/compute/interval-days',
          'POST',
        ).then((res) => {
          // console.log(res);
          record.durationDays = res;
        });
      }
      // if (
      //   (record.durationDays && record.durationDays !== 0 && record.beginTime)
      //     || (record.beginTime && record.endTime)
      //     || (record.durationDays && record.beginTime && !record.endTime)
      // ) {
      //   let url = 'end-date';
      // if (record.durationDays && record.durationDays !== 0 && record.beginTime) {
      //   params = {
      //     businessId: '1',
      //     start: dayjs(record.beginTime).format('YYYY-MM-DD'),
      //     workdayNum: record.durationDays,
      //   };
      // } else
      // /interval-days

      // if (record.beginTime && record.endTime) {
      //   params = {
      //     businessId: '1',
      //     start: dayjs(record.beginTime).format('YYYY-MM-DD'),
      //     end: dayjs(record.endTime).format('YYYY-MM-DD'),
      //   };
      //   url = 'interval-days';
      // } else
      // let params = {};
      // if (record.durationDays && record.beginTime) {
      //   params = {
      //     businessId: '1',
      //     start: dayjs(record.beginTime).format('YYYY-MM-DD'),
      //     workdayNum: record.durationDays,
      //   };
      //   url = 'end-date';
      // }

      //   new Api('/pmi').fetch(
      //     params,
      //     `api-pmi/holidays/compute/${url}`,
      //     'POST',
      //   ).then((res) => {
      //     // console.log(res);
      //     record.endTime = dayjs(res);
      //   });
      // } else {
      //   record.endTime = '';
      // }
    }, 500);

    function checkTreeForEmptyValues(tree, valuesToCheck) {
      let emptyValuesFound = [];

      function traverse(node) {
        valuesToCheck.forEach((value) => {
          if (
            !node[value]
              || (typeof node[value] === 'string' && node[value].trim() === '')
              || (Array.isArray(node[value]) && node[value].length === 0)
          ) {
            emptyValuesFound.push({
              id: node.id,
              field: value,
            });
          }
        });

        if (node.children && node.children.length > 0) {
          node.children.forEach((child) => traverse(child));
        }
      }

      tree.forEach((node) => traverse(node));

      return emptyValuesFound;
    }

    const handleClosed = () => {
      emit('handleColse');
    };

    const confirm = (data) => {
      if (data) {
        const list = cloneDeep(tableSource.value);
        for (let i in data) {
          list.push({
            id: new Date().getTime()
              .toString() + i,
            name: data[i].nodeName,
            nodeType: 'milestone',
            rspSubDept: '',
            rspSectionId: '',
            rspUser: '',
            rspUserCode: '',
            oldCode: '',
            beginTime: '',
            endTime: '',
            schemeDesc: data[i].schemeDesc,
            projectId,
          });
        }
        tableSource.value = list;
      }
    };

    function openTemplateModal(value: boolean) {
      if (value) {
        setSelectTemplate(true, {});
      }
    }

    // 获取禁用时间范围
    const getDisableDate = (current, value) =>
      new Date(value).getTime() >= new Date(current).getTime();

    // 更新当前行数据
    function updateRecord(record, value, option) {
      record.rspUser = option.id;

      record.rspSubDept = option.deptId;
      record.rspSubDeptName = option.deptName;

      record.rspSectionId = option.sectionId;
      record.rspSectionName = option.sectionName;

      record.rspUserCode = option.code;
    }

    function handleShow(visible: boolean) {
      templateId.value = null;
      tableSource.value = [];
    }

    async function saveData() {
      // 假设这是您要检查的数组
      const valuesToCheck = [
        'name',
        'nodeType',
        'schemeActivityList',
        'beginTime',
        'endTime',
      ];

      // 调用函数来检查树中的是否为空
      const result = checkTreeForEmptyValues(tableRef.value.getDataSource(), valuesToCheck);

      if (result && result.length !== 0) {
        message.error('带*号的为必填项，请完善');
        return null;
      }
      return tableRef.value.getDataSource();
    }
    defineExpose({
      saveData,
    });

    return {
      tableRef,
      tableOptions,
      tableSource,
      addNewRow,
      deleteBatchNode,
      onChangeValue,
      selectedRowKeys,
      rspDeptOptions,
      isTimeOut,
      showTips,
      deptList,
      userList,
      handleClosed,
      getDisableDate,
      registerTemplate,
      setSelectTemplate,
      openTemplateModal,
      confirm,
      updateRecord,
      templateOptions,
      templateId,
      onChangeTemplateValue,
      handleShow,
      onChangeValueForEndTime,
      getChildrenDisableDate,
      saveData,
      onChangeValueForDays,
      schemeActivityListOptions1: props.schemeActivityListOptions,
    };
  },
});
</script>
