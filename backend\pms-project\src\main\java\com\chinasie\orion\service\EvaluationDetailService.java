package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.EvaluationDetailDTO;
import com.chinasie.orion.domain.dto.EvaluationProjectDTO;
import com.chinasie.orion.domain.dto.EvaluationProjectDetailDTO;
import com.chinasie.orion.domain.vo.EvaluationDetailVO;
import com.chinasie.orion.domain.vo.EvaluationProjectDetailVO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.page.PageRequest;
import com.chinasie.orion.page.PageResult;

import java.lang.String;
import java.util.List;

/**
 * <p>
 * EvaluationDetail 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24 10:28:14
 */
public interface EvaluationDetailService {


    /**
     * 新增
     * <p>
     * * @param evaluationDetailDTO
     */
    EvaluationDetailVO create(EvaluationDetailDTO evaluationDetailDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param evaluationDetailDTO
     */
    List<EvaluationDetailVO> edit(EvaluationDetailDTO evaluationDetailDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    EvaluationProjectDetailVO getEvaluationProjectDetail(EvaluationProjectDetailDTO evaluationProjectDetailDTO) throws Exception;

    List<EvaluationDetailVO> getEvaluationDetailList(String evaluationProjectId);

    /**
     * 导入文件
     *
     * @param files
     * @return
     */
    List<String> importFiles(String id, List<FileDTO> files) throws Exception;

    PageResult<FileVO> getFilePage(PageRequest<EvaluationProjectDTO> pageRequest) throws Exception;
}
