package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ProjectToProductDTO;
import com.chinasie.orion.domain.vo.ProjectApprovalProductVO;
import com.chinasie.orion.domain.vo.ProjectBaseToProductVO;
import com.chinasie.orion.domain.vo.ProjectToProductVO;
import com.chinasie.orion.pdm.api.domain.vo.ProductEstimateMaterialVO;
import com.chinasie.orion.pdm.api.domain.vo.ProductToMaterialVO;
import com.chinasie.orion.service.ProjectToProductService;
import groovyjarjarantlr4.v4.runtime.misc.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;





import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * ProjectToProduct 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-22 11:01:33
 */
@RestController
@RequestMapping("/projectToProduct")
@Api(tags = "项目产品关联关系表")
public class ProjectToProductController {

    @Autowired
    private ProjectToProductService projectToProductService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据", type = "项目产品关联关系表", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectToProductVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        ProjectToProductVO rsp = projectToProductService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectToProductDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#projectToProductDTO.name}}】", type = "项目产品关联关系表", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody ProjectToProductDTO projectToProductDTO) throws Exception {
        String rsp =  projectToProductService.create(projectToProductDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "项目产品关联关系表", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = projectToProductService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "项目产品关联关系表", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectToProductService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】分页了数据", type = "项目产品关联关系表", subType = "分页", bizNo = "")
    public ResponseDTO<Page<ProjectToProductVO>> pages(@RequestBody Page<ProjectToProductDTO> pageRequest) throws Exception {
        Page<ProjectToProductVO> rsp =  projectToProductService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 根据项目id获取产品数据列表
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "根据项目id获取产品数据列表")
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】根据项目id获取产品数据列表", type = "项目产品关联关系表", subType = "根据项目id获取产品数据列表", bizNo = "")
    public ResponseDTO<Page<ProjectApprovalProductVO>> getListByProjectId(@RequestBody Page<ProjectToProductDTO> pageRequest) throws Exception {
        Page<ProjectApprovalProductVO> rsp =  projectToProductService.getListByProjectId(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "根据项目id获取产品数据列表")
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping(value = "/getProductEstimateMaterialList", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】根据项目id获取产品数据列表", type = "项目产品关联关系表", subType = "根据项目id获取产品数据列表", bizNo = "{{#productId}}")
    public ResponseDTO<List<ProductEstimateMaterialVO>> getProductEstimateMaterialList(@RequestParam String projectId) throws Exception {
        List<ProductEstimateMaterialVO> rsp =  projectToProductService.getProductEstimateMaterialList(projectId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "根据产品id获取物料")
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping(value = "/getMaterialList", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】根据产品id获取物料", type = "项目产品关联关系表", subType = "根据产品id获取物料", bizNo = "{{#productId}}")
    public ResponseDTO<List<ProductToMaterialVO>> getMaterialList(@RequestParam String productId) throws Exception {
        List<ProductToMaterialVO> rsp =  projectToProductService.getProductToMaterialList(productId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "根据产品ID列表获取对应项目ID列表")
    @RequestMapping(value = "/getProjectIdByProductIds", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】根据产品ID列表获取对应项目ID列表", type = "项目产品关联关系表", subType = "根据产品ID列表获取对应项目ID列表", bizNo = "")
    public ResponseDTO<List<String>> getProjectIdByProductIds(@Valid @NotNull @RequestBody List<String> ids) throws Exception {
        List<String> rsp =  projectToProductService.getProjectIdByProductIds(ids);
        return new ResponseDTO<>(rsp);
    }
}
