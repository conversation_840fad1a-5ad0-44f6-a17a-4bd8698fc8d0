package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;

import java.util.List;
/**
 * RelatedTransactionForm VO对象
 *
 * <AUTHOR>
 * @since 2024-05-31 14:07:31
 */
@ApiModel(value = "RelatedTransactionFormVO对象", description = "关联交易表单")
@Data
public class RelatedTransactionFormVO extends ObjectVO implements Serializable{

    /**
     * 工作主题
     */
    @ApiModelProperty(value = "工作主题")
    private String workTitle;


    /**
     * 发起人
     */
    @ApiModelProperty(value = "发起人")
    private String startUser;


    /**
     * 发起时间
     */
    @ApiModelProperty(value = "发起时间")
    private Date startDate;


    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    private Date endDate;


    /**
     * 表单状态
     */
    @ApiModelProperty(value = "表单状态")
    private String formStatus;




}
