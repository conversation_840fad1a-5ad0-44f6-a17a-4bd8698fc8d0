<template>
  <div class="wrap flex flex-ver">
    <div>
      <InputSearch
        v-model:value="filterValue"
        placeholder="请输入内容"
      />
    </div>
    <div class="list-main">
      <CheckboxGroup
        v-bind="$attrs"
        class="w-full"
        @change="onCheckboxChange"
      >
        <Checkbox
          v-for="(item, index) in filterList"
          :key="index"
          :value="item.id"
          :style="{ display: 'block', marginLeft: 0, height: '30px', lineHeight: '30px' }"
        >
          {{ item.name }}
        </Checkbox>
      </CheckboxGroup>
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed, defineComponent, onMounted, reactive, toRefs,
} from 'vue';
import { Input, Checkbox } from 'ant-design-vue';
import Api from '/@/api';

export default defineComponent({
  name: 'AddRuleComponent',
  components: {
    InputSearch: Input.Search,
    Checkbox,
    CheckboxGroup: Checkbox.Group,
  },
  emits: ['change'],
  setup(props, context) {
    const state = reactive({
      ruleList: [],
      filterValue: '',
    });

    function loadRuleList() {
      new Api('/pmi/privilege-rule').getList({}).then((data) => {
        state.ruleList = data;
      });
    }

    const filterList = computed(() => {
      if (!state.filterValue) {
        return state.ruleList;
      }

      return state.ruleList.filter((item) => item.name.includes(state.filterValue));
    });

    onMounted(() => {
      loadRuleList();
    });

    return {
      ...toRefs(state),
      filterList,
      onCheckboxChange(values) {
        context.emit(
          'change',
          state.ruleList.filter((item) => values.includes(item.id)),
        );
      },
    };
  },
});
</script>

<style scoped lang="less">
  .list-main {
    height: 200px;
    overflow-y: auto;
    padding-top: 10px;
  }
</style>
