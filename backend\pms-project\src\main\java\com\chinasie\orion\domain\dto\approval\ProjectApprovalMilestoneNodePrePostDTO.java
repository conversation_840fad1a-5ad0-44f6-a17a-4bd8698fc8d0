package com.chinasie.orion.domain.dto.approval;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProjectApprovalMilestoneNodePrePost DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-27 17:39:58
 */
@ApiModel(value = "ProjectApprovalMilestoneNodePrePostDTO对象", description = "项目立项里程碑节点前后置关系")
@Data
@ExcelIgnoreUnannotated
public class ProjectApprovalMilestoneNodePrePostDTO extends  ObjectDTO   implements Serializable{

/**
 * 前置计划Id
 */
@ApiModelProperty(value = "前置计划Id")
@ExcelProperty(value = "前置计划Id ", index = 0)
private String preSchemeId;

/**
 * 后置计划Id
 */
@ApiModelProperty(value = "后置计划Id")
@ExcelProperty(value = "后置计划Id ", index = 1)
private String postSchemeId;

/**
 * 立项里程碑id
 */
@ApiModelProperty(value = "立项里程碑id")
@ExcelProperty(value = "立项里程碑id ", index = 2)
private String ApprovalMilestoneId;

/**
 * 模板id
 */
@ApiModelProperty(value = "模板id")
@ExcelProperty(value = "模板id ", index = 3)
private String templateId;

/**
 * 前后置类型
 */
@ApiModelProperty(value = "前后置类型")
@ExcelProperty(value = "前后置类型 ", index = 4)
private String type;




}
