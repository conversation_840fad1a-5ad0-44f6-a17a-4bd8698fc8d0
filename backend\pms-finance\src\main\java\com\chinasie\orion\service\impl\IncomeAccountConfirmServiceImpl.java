package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.constant.ContractMilestoneNode;
import com.chinasie.orion.conts.AdjAccountVoucherEnum;
import com.chinasie.orion.conts.IncomeConfirmTypeEnum;
import com.chinasie.orion.conts.VoucherTypeEnum;
import com.chinasie.orion.dict.IncomePlanDict;
import com.chinasie.orion.domain.dto.IncomeAccountConfirmDTO;
import com.chinasie.orion.domain.dto.IncomeAccountConfirmExportDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.MessageCenterApi;
import com.chinasie.orion.msc.api.MscBuildHandlerManager;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.*;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;

import java.io.InputStream;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;


/**
 * <p>
 * IncomeAccountConfirm 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18 21:16:26
 */
@Service
@Slf4j
public class IncomeAccountConfirmServiceImpl extends OrionBaseServiceImpl<IncomeAccountConfirmMapper, IncomeAccountConfirm> implements IncomeAccountConfirmService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;
    @Autowired
    private DictBo dictBo;

    @Autowired
    private AdvancePaymentInvoicedService advancePaymentInvoicedService;

    @Autowired
    private ProvisionalIncomeAccountingService provisionalIncomeAccountingService;

    @Resource
    private ProvisionalIncomeAccountingMapper provisionalIncomeAccountingMapper;


    @Autowired
    private InvoicingRevenueAccountingService invoicingRevenueAccountingService;

    @Resource
    private InvoicingRevenueAccountingMapper invoicingRevenueAccountingMapper;


    @Autowired
    private IncomePlanDataService incomePlanDataService;

    @Resource
    private IncomeAccountConfirmMapper incomeAccountConfirmMapper;

    @Autowired
    private AdjustmentVoucherService adjustmentVoucherService;


    @Autowired
    private ContractMilestoneService contractMilestoneService;

    @Autowired
    private MilestoneIncomeAllocationService milestoneIncomeAllocationService;

    @Resource
    private AdvancePaymentInvoicedMapper advancePaymentInvoicedMapper;

    @Autowired
    private MscBuildHandlerManager mscBuildHandlerManager;

    @Autowired
    private MarketContractService marketContractService;

    @Autowired
    private MessageCenterApi messageCenterApi;




    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public IncomeAccountConfirmVO detail(String id, String pageCode) throws Exception {
        IncomeAccountConfirm incomeAccountConfirm = this.getById(id);
        IncomeAccountConfirmVO result = BeanCopyUtils.convertTo(incomeAccountConfirm, IncomeAccountConfirmVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param incomeAccountConfirmDTO
     */
    @Override
    public String create(IncomeAccountConfirmDTO incomeAccountConfirmDTO) throws Exception {
        IncomeAccountConfirm incomeAccountConfirm = BeanCopyUtils.convertTo(incomeAccountConfirmDTO, IncomeAccountConfirm::new);
        this.save(incomeAccountConfirm);

        String rsp = incomeAccountConfirm.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param incomeAccountConfirmDTO
     */
    @Override
    public Boolean edit(IncomeAccountConfirmDTO incomeAccountConfirmDTO) throws Exception {
        IncomeAccountConfirm incomeAccountConfirm = BeanCopyUtils.convertTo(incomeAccountConfirmDTO, IncomeAccountConfirm::new);
        if(StrUtil.isBlank(incomeAccountConfirm.getVoucherType())){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "凭证类型为空");
        }
        if(StrUtil.isBlank(incomeAccountConfirm.getVoucherNum())){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "凭证编号为空");
        }
        List<String> ids = this.list(new LambdaQueryWrapperX<>(IncomeAccountConfirm.class).select(IncomeAccountConfirm::getId)
                .eq(IncomeAccountConfirm::getVoucherNum,incomeAccountConfirm.getVoucherNum())).stream().map(IncomeAccountConfirm::getId).collect(Collectors.toList());

        if(StrUtil.equals(incomeAccountConfirm.getVoucherType(), VoucherTypeEnum.CONFIRM_VOUCHER_TYPE.getValue())){
            incomeAccountConfirm.setAdjAccountVoucher(AdjAccountVoucherEnum.ACCOUNT_ADJUSTMENT_VOUCHER.getValue());
        }else{
            incomeAccountConfirm.setAdjAccountVoucher(AdjAccountVoucherEnum.NO_ACCOUNT_ADJUSTMENT_VOUCHER.getValue());
        }
        LambdaUpdateWrapper<IncomeAccountConfirm> lambdaUpdateWrapper = new LambdaUpdateWrapper();
        lambdaUpdateWrapper.set(IncomeAccountConfirm::getVoucherType,incomeAccountConfirm.getVoucherType());
        lambdaUpdateWrapper.set(IncomeAccountConfirm::getIsUpdate,"1");
        lambdaUpdateWrapper.set(IncomeAccountConfirm::getAdjAccountVoucher,incomeAccountConfirm.getAdjAccountVoucher());
        lambdaUpdateWrapper.in(IncomeAccountConfirm::getId,ids);
        this.update(lambdaUpdateWrapper);
        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<IncomeAccountConfirmVO> pages(Page<IncomeAccountConfirmDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<IncomeAccountConfirm> condition = new LambdaQueryWrapperX<>(IncomeAccountConfirm.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(IncomeAccountConfirm::getCreateTime);


        Page<IncomeAccountConfirm> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), IncomeAccountConfirm::new));

        PageResult<IncomeAccountConfirm> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<IncomeAccountConfirmVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<IncomeAccountConfirmVO> vos = BeanCopyUtils.convertListTo(page.getContent(), IncomeAccountConfirmVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "收入记账明细确认表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", IncomeAccountConfirmDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        IncomeAccountConfirmExcelListener excelReadListener = new IncomeAccountConfirmExcelListener();
        EasyExcel.read(inputStream, IncomeAccountConfirmDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<IncomeAccountConfirmDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("收入记账明细确认表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<IncomeAccountConfirm> incomeAccountConfirmes = BeanCopyUtils.convertListTo(dtoS, IncomeAccountConfirm::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::IncomeAccountConfirm-import::id", importId, incomeAccountConfirmes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<IncomeAccountConfirm> incomeAccountConfirmes = (List<IncomeAccountConfirm>) orionJ2CacheService.get("pmsx::IncomeAccountConfirm-import::id", importId);
        log.info("收入记账明细确认表导入的入库数据={}", JSONUtil.toJsonStr(incomeAccountConfirmes));

        this.saveBatch(incomeAccountConfirmes);
        orionJ2CacheService.delete("pmsx::IncomeAccountConfirm-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::IncomeAccountConfirm-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(IncomeAccountConfirmDTO incomeAccountConfirmDTO, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<IncomeAccountConfirm> condition = new LambdaQueryWrapperX<>(IncomeAccountConfirm.class);
        if(CollUtil.isNotEmpty(incomeAccountConfirmDTO.getIds())){
            condition.in(ConnectedMilestones::getId,incomeAccountConfirmDTO.getIds());
        }else {
            if (!CollectionUtils.isEmpty(incomeAccountConfirmDTO.getSearchConditions())) {
                SearchConditionUtils.parseSearchConditionsWrapper(incomeAccountConfirmDTO.getSearchConditions(), condition);
            }
        }
        condition.orderByDesc(IncomeAccountConfirm::getCreateTime);
        List<IncomeAccountConfirm> incomeAccountConfirmes = this.list(condition);
        List<IncomeAccountConfirmVO> result = BeanCopyUtils.convertListTo(incomeAccountConfirmes, IncomeAccountConfirmVO::new);
        setEveryName(result);
        List<IncomeAccountConfirmExportDTO> dtos = BeanCopyUtils.convertListTo(result, IncomeAccountConfirmExportDTO::new);

        String fileName = "收入记账明细确认表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", IncomeAccountConfirmExportDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<IncomeAccountConfirmVO> vos) throws Exception {
        if(CollUtil.isEmpty(vos)){
            return;
        }
        Map<String, String> voucherTypeDictMap = dictBo.getDictValue(IncomePlanDict.VOUCHER_TYPE);
        Map<String, String> adjAccountVoucherDictMap =  dictBo.getDictValue(IncomePlanDict.ADJ_ACCOUNT_VOUCHER);
        vos.forEach(vo -> {
            if(StrUtil.isNotBlank(vo.getAdjAccountVoucher())){
                vo.setAdjAccountVoucherName(adjAccountVoucherDictMap.get(vo.getAdjAccountVoucher()));
            }
            if(StrUtil.isNotBlank(vo.getVoucherType())){
                vo.setVoucherTypeName(voucherTypeDictMap.get(vo.getVoucherType()));
            }
            if(StrUtil.equals("1", vo.getConfirmStatus())){
                vo.setConfirmStatusName("已确定");
            }else{
                vo.setConfirmStatusName("未确认");
            }
        });
    }

    @Override
    public Boolean dataConfirm(List<String> ids) {
        long startTime = System.currentTimeMillis();
        if(CollUtil.isEmpty(ids)){
            return true;
        }
        //收入记账明细确认改为已确认
        List<IncomeAccountConfirm> incomeAccountConfirms = this.list(new LambdaQueryWrapperX<>(IncomeAccountConfirm.class).select(IncomeAccountConfirm::getVoucherNum)
                .in(IncomeAccountConfirm::getId, ids));
        List<String> voucherNums = incomeAccountConfirms.stream().map(IncomeAccountConfirm::getVoucherNum).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(voucherNums)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "凭证编号为空");
        }
        LambdaUpdateWrapper<IncomeAccountConfirm> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(IncomeAccountConfirm::getConfirmStatus,"1");
        updateWrapper.in(IncomeAccountConfirm::getVoucherNum,voucherNums);
        this.update(updateWrapper);
        long startTime1 = System.currentTimeMillis();
        log.info("第一阶段耗时：{}",(startTime1-startTime));


        //开票收入核算信息表
        List<InvoicingRevenueAccounting> invoicingRevenueAccountings = new ArrayList<>();
        //暂估收入核算信息
        List<ProvisionalIncomeAccounting> provisionalIncomeAccountings = new ArrayList<>();
        //预收款开票挂账信息
        List<AdvancePaymentInvoiced> advancePaymentInvoiceds = new ArrayList<>();

        //获取收入记账明细确认统计信息
        List<IncomeAccountConfirmStatisticsVO>  confirms = incomeAccountConfirmMapper.getStatistics(voucherNums);
        List<String> voucherNumList = new ArrayList<>();
        List<String> confirmVoucherNum = new ArrayList<>();

        //校验是否存在存在凭证编号存在不同的凭证类型或收入计划编码
        boolean hasDuplicateId = confirms.stream()
                .collect(Collectors.groupingBy(IncomeAccountConfirmStatisticsVO::getCertificateSerialNumber))
                .values()
                .stream()
                .anyMatch(list -> list.size() > 1);

        if (hasDuplicateId) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "凭证编号存在不同的凭证类型或收入计划编码");
        }

        for(IncomeAccountConfirmStatisticsVO statisticsVO:confirms){
            //获取已确认凭证编号
            if(StrUtil.equals(statisticsVO.getConfirmStatus(),"1")){
                confirmVoucherNum.add(statisticsVO.getCertificateSerialNumber());
            }
            //获取有收入计划编号的凭证编号
            if(StrUtil.isBlank(statisticsVO.getIncomePlanNum())){
                voucherNumList.add(statisticsVO.getCertificateSerialNumber());
            }
            if(StrUtil.equals(statisticsVO.getVoucherType(), VoucherTypeEnum.CONFIRM_VOUCHER_TYPE.getValue())){
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在待确认凭证类型");
            }
        }

        Map<String,AdjustmentVoucher> adjustmentVoucherMap = new HashMap<>();
        if(CollUtil.isNotEmpty(voucherNumList)){

           List<AdjustmentVoucher> adjustmentVouchers = adjustmentVoucherService.list(new LambdaQueryWrapperX<>(AdjustmentVoucher.class).in(AdjustmentVoucher::getVoucherNum,voucherNumList));
            adjustmentVoucherMap =   adjustmentVouchers.stream()
                    .collect(Collectors.toMap(
                            AdjustmentVoucher::getVoucherNum,
                            Function.identity(),
                            (existing, replacement) -> existing // 如果有重复的key，则保留现有的（即第一条记录）
                    ));
        }

        //删除已确认编号核算信息
        if(CollUtil.isNotEmpty(confirmVoucherNum)){
            confirmVoucherNum = confirmVoucherNum.stream().distinct().collect(Collectors.toList());
            advancePaymentInvoicedService.remove(new LambdaQueryWrapperX<>(AdvancePaymentInvoiced.class).in(AdvancePaymentInvoiced::getCertificateSerialNumber,confirmVoucherNum));
            provisionalIncomeAccountingService.remove(new LambdaQueryWrapperX<>(ProvisionalIncomeAccounting.class).in(ProvisionalIncomeAccounting::getCertificateSerialNumber,confirmVoucherNum));
            invoicingRevenueAccountingService.remove(new LambdaQueryWrapperX<>(InvoicingRevenueAccounting.class).in(InvoicingRevenueAccounting::getCertificateSerialNumber,confirmVoucherNum));
        }
        List<String> milestoneIds = new ArrayList<>();
        List<String> advmilestoneIds = new ArrayList<>();
        List<String> allmilestoneIds = new ArrayList<>();

        long startTime2 = System.currentTimeMillis();
        log.info("第二阶段耗时：{}",(startTime2-startTime1));
        //生成新的核算信息
        for(IncomeAccountConfirmStatisticsVO statisticsVO:confirms){
            if(StrUtil.isBlank(statisticsVO.getContractId())){
                if(ObjectUtil.isEmpty(adjustmentVoucherMap.get(statisticsVO.getCertificateSerialNumber()))){
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "凭证未关联合同");
                }else{
                    AdjustmentVoucher adjustmentVoucher = adjustmentVoucherMap.get(statisticsVO.getCertificateSerialNumber());
                    statisticsVO.setContractId(adjustmentVoucher.getContractId());
                    statisticsVO.setMilestoneId(adjustmentVoucher.getMilestoneId());
                    statisticsVO.setMilestoneName(adjustmentVoucher.getMilestoneName());
                }
            }
            allmilestoneIds.add(statisticsVO.getMilestoneId());
            if(StrUtil.equals(statisticsVO.getVoucherType(),VoucherTypeEnum.INVOICE_RECOGNIZE_REVENUE.getValue())){
                InvoicingRevenueAccounting invoicingRevenueAccounting = BeanCopyUtils.convertTo(statisticsVO,InvoicingRevenueAccounting::new);
                BigDecimal tax = (invoicingRevenueAccounting.getTax()!= null ? invoicingRevenueAccounting.getTax() : BigDecimal.ZERO);
                BigDecimal amtNoTax = (invoicingRevenueAccounting.getAmtNoTax() != null ? invoicingRevenueAccounting.getAmtNoTax() : BigDecimal.ZERO);
                BigDecimal amtTax = tax.add(amtNoTax);
                invoicingRevenueAccounting.setAmtTax(amtTax);
                invoicingRevenueAccountings.add(invoicingRevenueAccounting);
                milestoneIds.add(invoicingRevenueAccounting.getMilestoneId());
            }
            if(StrUtil.equals(statisticsVO.getVoucherType(),VoucherTypeEnum.INVOICE_ADVANCE_PAYMENT.getValue())){
                AdvancePaymentInvoiced advancePaymentInvoiced = BeanCopyUtils.convertTo(statisticsVO,AdvancePaymentInvoiced::new);
                advancePaymentInvoiceds.add(advancePaymentInvoiced);
                advmilestoneIds.add(advancePaymentInvoiced.getMilestoneId());
            }

            if(StrUtil.equals(statisticsVO.getVoucherType(),VoucherTypeEnum.ADVANCE_PAYMENT_INCOME.getValue())){
                AdvancePaymentInvoiced advancePaymentInvoiced = BeanCopyUtils.convertTo(statisticsVO,AdvancePaymentInvoiced::new);
                advancePaymentInvoiceds.add(advancePaymentInvoiced);
                advmilestoneIds.add(advancePaymentInvoiced.getMilestoneId());
            }
            if(StrUtil.equals(statisticsVO.getVoucherType(),VoucherTypeEnum.PROVISIONAL_RECOGNITION_INCOME.getValue())){
                ProvisionalIncomeAccounting provisionalIncomeAccounting = BeanCopyUtils.convertTo(statisticsVO,ProvisionalIncomeAccounting::new);
                BigDecimal tax = (provisionalIncomeAccounting.getTax()!= null ? provisionalIncomeAccounting.getTax() : BigDecimal.ZERO);
                BigDecimal amtNoTax = (provisionalIncomeAccounting.getAmtNoTax() != null ? provisionalIncomeAccounting.getAmtNoTax() : BigDecimal.ZERO);
                BigDecimal amtTax = tax.add(amtNoTax);
                provisionalIncomeAccounting.setAmtTax(amtTax);
                provisionalIncomeAccountings.add(provisionalIncomeAccounting);
                milestoneIds.add(provisionalIncomeAccounting.getMilestoneId());
            }

            if(StrUtil.equals(statisticsVO.getVoucherType(),VoucherTypeEnum.WRITE_OFF_PROVISIONAL_INCOME.getValue())){
                ProvisionalIncomeAccounting provisionalIncomeAccounting = BeanCopyUtils.convertTo(statisticsVO,ProvisionalIncomeAccounting::new);
                BigDecimal tax = (provisionalIncomeAccounting.getTax()!= null ? provisionalIncomeAccounting.getTax() : BigDecimal.ZERO);
                BigDecimal amtNoTax = (provisionalIncomeAccounting.getAmtNoTax() != null ? provisionalIncomeAccounting.getAmtNoTax() : BigDecimal.ZERO);
                BigDecimal amtTax = tax.add(amtNoTax);
                provisionalIncomeAccounting.setAmtTax(amtTax);
                provisionalIncomeAccountings.add(provisionalIncomeAccounting);
                milestoneIds.add(provisionalIncomeAccounting.getMilestoneId());
            }

        }
        //保存新的核算信息
        if(CollUtil.isNotEmpty(invoicingRevenueAccountings)){
            invoicingRevenueAccountingService.saveBatch(invoicingRevenueAccountings);
        }
        if(CollUtil.isNotEmpty(advancePaymentInvoiceds)){
            advancePaymentInvoicedService.saveBatch(advancePaymentInvoiceds);
        }
        if(CollUtil.isNotEmpty(provisionalIncomeAccountings)){
            provisionalIncomeAccountingService.saveBatch(provisionalIncomeAccountings);
        }
        long startTime3 = System.currentTimeMillis();
        log.info("第三阶段耗时：{}",(startTime3-startTime2));

        if(CollUtil.isNotEmpty(allmilestoneIds)){
            milestoneIds = milestoneIds.stream().distinct().collect(Collectors.toList());
            List<ContractMilestone> updateChild = new ArrayList<>();
            List<String> removeIds = new ArrayList<>();
            List<String> removeIdsMsc = new ArrayList<>();
            List<MilestoneIncomeAllocation> milestoneIncomeAllocations = new ArrayList<>();
            List<ContractMilestone> sendContractMilestone = new ArrayList<>();
            Map<String, List<ContractMilestone>> childMap = new HashMap<>();
            Map<String,BigDecimal> invMap = new HashMap<>();
            Map<String,BigDecimal> proMap = new HashMap<>();
            Map<String,BigDecimal> advMap = new HashMap<>();
            if(CollUtil.isNotEmpty(milestoneIds)) {
                //统计合同核算金额数据
                invMap = invoicingRevenueAccountingMapper.getMilestoneTotal(milestoneIds).stream().collect(Collectors.toMap(InvoicingRevenueAccountingVO::getMilestoneId, InvoicingRevenueAccountingVO::getAmtTax));
                proMap = provisionalIncomeAccountingMapper.getMilestoneTotal(milestoneIds).stream().collect(Collectors.toMap(ProvisionalIncomeAccountingVO::getMilestoneId, ProvisionalIncomeAccountingVO::getAmtTax));
                List<ContractMilestone> childMilestone = contractMilestoneService.list(new LambdaQueryWrapperX<>(ContractMilestone.class).select(ContractMilestone::getId,
                        ContractMilestone::getConfirmIncomeSum,ContractMilestone::getActualMilestoneAmt,ContractMilestone::getConfirmAllocationStatus,ContractMilestone::getParentId)
                        .in(ContractMilestone::getParentId,milestoneIds)
                );

                if(CollUtil.isNotEmpty(childMilestone)){
                    childMap =  childMilestone.stream().collect(Collectors.groupingBy(ContractMilestone::getParentId));
                }
            }
            if(CollUtil.isNotEmpty(advmilestoneIds)) {
                advMap= advancePaymentInvoicedMapper.getMilestoneTotal(advmilestoneIds).stream().collect(Collectors.toMap(AdvancePaymentInvoicedVO::getMilestoneId,AdvancePaymentInvoicedVO::getAmtTax));
            }
            List<ContractMilestone> contractMilestones = contractMilestoneService.list(new LambdaQueryWrapperX<>(ContractMilestone.class).select(ContractMilestone::getId,
                    ContractMilestone::getConfirmIncomeSum,ContractMilestone::getActualMilestoneAmt
                    ,ContractMilestone::getConfirmAllocationStatus,ContractMilestone::getOrgId,ContractMilestone::getPlatformId,ContractMilestone::getContractId,
                    ContractMilestone::getMilestoneName,ContractMilestone::getTechRspUser)
                    .in(ContractMilestone::getId,allmilestoneIds)
            );
            //修改确认合并金额
            for(ContractMilestone contractMilestone:contractMilestones){
                if(milestoneIds.contains(contractMilestone.getId())) {
                    BigDecimal inv = invMap.get(contractMilestone.getId()) != null ? invMap.get(contractMilestone.getId()) : BigDecimal.ZERO;
                    BigDecimal pro = proMap.get(contractMilestone.getId()) != null ? proMap.get(contractMilestone.getId()) : BigDecimal.ZERO;
                    BigDecimal total = inv.add(pro);
                    List<ContractMilestone> childs = childMap.get(contractMilestone.getId());
                    if (ObjectUtil.isNotEmpty(contractMilestone.getActualMilestoneAmt()) && total.compareTo(contractMilestone.getActualMilestoneAmt()) == 0) {
                        contractMilestone.setConfirmIncomeSum(total);
                        contractMilestone.setConfirmIncomeInvoicing(inv);
                        contractMilestone.setConfirmIncomeProvisionalEstimate(pro);
                        contractMilestone.setConfirmAllocationStatus("2");
                        removeIds.add(contractMilestone.getId());
                        removeIdsMsc.add("MilestoneIncomeAllocation_"+contractMilestone.getId());
                        if (CollUtil.isNotEmpty(childs)) {
                            childs.forEach(item -> {
                                if (ObjectUtil.isNotEmpty(item.getActualMilestoneAmt())) {
                                    item.setConfirmIncomeSum(item.getActualMilestoneAmt());
                                }
                                item.setConfirmAllocationStatus("2");
                            });
                            updateChild.addAll(childs);
                        }
                    } else if (ObjectUtil.isEmpty(contractMilestone.getActualMilestoneAmt()) || total.compareTo(contractMilestone.getActualMilestoneAmt()) != 0) {
                        if (CollUtil.isNotEmpty(childs)) {
                            if (!StrUtil.equals(contractMilestone.getConfirmAllocationStatus(), "1")) {
                                MilestoneIncomeAllocation milestoneIncomeAllocation = new MilestoneIncomeAllocation();
                                milestoneIncomeAllocation.setContractId(contractMilestone.getContractId());
                                milestoneIncomeAllocation.setMilestoneId(contractMilestone.getId());
                                contractMilestone.setConfirmAllocationStatus("1");
                                milestoneIncomeAllocations.add(milestoneIncomeAllocation);
                            }
                            contractMilestone.setConfirmAllocationStatus("1");
                            sendContractMilestone.add(contractMilestone);
                        }
                        contractMilestone.setConfirmIncomeSum(total);
                        contractMilestone.setConfirmIncomeInvoicing(inv);
                        contractMilestone.setConfirmIncomeProvisionalEstimate(pro);
                    }
                }
                if(advmilestoneIds.contains(contractMilestone.getId())){
                    contractMilestone.setMilestoneAdvanceAmt(advMap.get(contractMilestone.getId()));
                }
            }
            if(CollUtil.isNotEmpty(updateChild)){
                contractMilestones.addAll(updateChild);
            }
            contractMilestoneService.updateBatchById(contractMilestones);
            if(CollUtil.isNotEmpty(removeIds)){
                milestoneIncomeAllocationService.removeBatchByIds(removeIds);
                messageCenterApi.todoMessageChangeStatusByBusinessIds(removeIdsMsc);
            }
            if(CollUtil.isNotEmpty(milestoneIncomeAllocations)) {
                milestoneIncomeAllocationService.saveBatch(milestoneIncomeAllocations);
            }
            long startTime4 = System.currentTimeMillis();
            log.info("第四阶段耗时：{}",(startTime4-startTime3));

            //发送消息通知客户
            if(CollUtil.isNotEmpty(sendContractMilestone)) {
                List<String> contractIds = sendContractMilestone.stream().map(ContractMilestone::getContractId).collect(Collectors.toList());
                Map<String,String> marketContractMap =  marketContractService.list(
                        new LambdaQueryWrapperX<>(MarketContract.class).
                                select(MarketContract::getId,MarketContract::getName)
                        .in(MarketContract::getId,contractIds)
                ).stream().collect(Collectors.toMap(MarketContract::getId,MarketContract::getName));
                for (ContractMilestone contractMilestone:sendContractMilestone) {
                    contractMilestone.setContractName(marketContractMap.get(contractMilestone.getContractId()));
                    mscBuildHandlerManager.send(contractMilestone, ContractMilestoneNode.CONTRACT_MILESTONE_INCOME, null);
                }
            }

            long startTime5 = System.currentTimeMillis();
            log.info("第五阶段耗时：{}",(startTime5-startTime));
        }
        return true;
    }

    @Override
    public Boolean editBatch(IncomeAccountConfirmDTO incomeAccountConfirmDTO) throws Exception {

        if(StrUtil.isBlank(incomeAccountConfirmDTO.getVoucherType())){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "凭证类型为空");
        }
        if(CollUtil.isEmpty(incomeAccountConfirmDTO.getVoucherNums())){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "凭证编号为null");
        }

        List<String> voucherNums = incomeAccountConfirmDTO.getVoucherNums().stream().distinct().collect(Collectors.toList());

        List<String> ids = this.list(new LambdaQueryWrapperX<>(IncomeAccountConfirm.class).select(IncomeAccountConfirm::getId)
                .in(IncomeAccountConfirm::getVoucherNum,voucherNums)).stream().map(IncomeAccountConfirm::getId).collect(Collectors.toList());
        String  adjAccountVoucher = "";
        if(StrUtil.equals(incomeAccountConfirmDTO.getVoucherType(), VoucherTypeEnum.CONFIRM_VOUCHER_TYPE.getValue())){
            adjAccountVoucher = AdjAccountVoucherEnum.ACCOUNT_ADJUSTMENT_VOUCHER.getValue();
        }else{
            adjAccountVoucher = AdjAccountVoucherEnum.NO_ACCOUNT_ADJUSTMENT_VOUCHER.getValue();
        }
        LambdaUpdateWrapper<IncomeAccountConfirm> lambdaUpdateWrapper = new LambdaUpdateWrapper();
        lambdaUpdateWrapper.set(IncomeAccountConfirm::getVoucherType,incomeAccountConfirmDTO.getVoucherType());
        lambdaUpdateWrapper.set(IncomeAccountConfirm::getIsUpdate,"1");
        lambdaUpdateWrapper.set(IncomeAccountConfirm::getAdjAccountVoucher,adjAccountVoucher);
        lambdaUpdateWrapper.in(IncomeAccountConfirm::getId,ids);
        this.update(lambdaUpdateWrapper);
        return true;
    }


    public static class IncomeAccountConfirmExcelListener extends AnalysisEventListener<IncomeAccountConfirmDTO> {

        private final List<IncomeAccountConfirmDTO> data = new ArrayList<>();

        @Override
        public void invoke(IncomeAccountConfirmDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<IncomeAccountConfirmDTO> getData() {
            return data;
        }
    }


}
