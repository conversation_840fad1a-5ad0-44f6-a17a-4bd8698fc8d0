package com.chinasie.orion.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.CellExtra;
import com.alibaba.excel.read.listener.ReadListener;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.PersonManageLedgerTypeEnum;
import com.chinasie.orion.domain.dto.SchemePersonOffExcelTemplate;
import com.chinasie.orion.domain.dto.SchemeToPersonDTO;
import com.chinasie.orion.domain.entity.JobPostAuthorize;
import com.chinasie.orion.domain.entity.PersonManageLedger;
import com.chinasie.orion.domain.entity.PersonMange;
import com.chinasie.orion.domain.entity.SchemeToPerson;
import com.chinasie.orion.domain.vo.BasicUserVO;
import com.chinasie.orion.domain.vo.PersonMangeVO;
import com.chinasie.orion.domain.vo.SchemeToPersonVO;
import com.chinasie.orion.enums.StatusEnum;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.export.ImportExcelErrorNoteVO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.user.UserBaseCacheVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.chinasie.orion.constant.DictConts.PMS_OUT_FACTORY_REASON;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/9/27
 */
@Service
@Slf4j
public class SchemeToPersonExcelServiceImpl implements SchemeToPersonExcelService {

    @Autowired
    private OrionJ2CacheService orionJ2CacheService;
    @Autowired
    private UserRedisHelper userRedisHelper;
    @Autowired
    private SchemeToPersonService schemeToPersonService;
    @Autowired
    private PersonMangeService personMangeService;
    @Autowired
    private BasicUserService basicUserService;
    @Autowired
    private DictRedisHelper dictRedisHelper;
    @Autowired
    private PersonManageLedgerService personManageLedgerService;
    @Autowired
    private JobPostAuthorizeService jobPostAuthorizeService;
    @Autowired
    private JobNodeStatusService jobNodeStatusService;

    @Override
    public ImportExcelCheckResultVO offImportCheckByExcel(MultipartFile file, String repairRound) throws IOException {
        ImportExcelCheckResultVO resultVO = new ImportExcelCheckResultVO();
        InputStream inputStream = file.getInputStream();
        UserExcelReadListener taskExcelReadListener = new UserExcelReadListener();
        EasyExcel.read(inputStream, SchemePersonOffExcelTemplate.class, taskExcelReadListener).sheet().headRowNumber(1).doRead();
        List<SchemePersonOffExcelTemplate> schemePersonOffExcelTemplates = taskExcelReadListener.getUserExcelDTOS();
        if (CollectionUtils.isEmpty(schemePersonOffExcelTemplates)) {
            resultVO.setOom("非正确模板，无法解析数据");
            resultVO.setCode(4000);
            return resultVO;
        }
        if (schemePersonOffExcelTemplates.size() > 1000) {
            resultVO.setOom("最多导入1000条");
            resultVO.setCode(400);
            return resultVO;
        }
        //校验
        checkExcel(schemePersonOffExcelTemplates,resultVO,repairRound);
        if(resultVO.getCode() != null){
            return resultVO;
        }
        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::SchemeToPersonOff-import::id", importId, schemePersonOffExcelTemplates, 24 * 60 * 60);
        resultVO.setCode(200);
        resultVO.setSucc(importId);
        return resultVO;
    }

    @Override
    public Boolean importExcel(String importId, String repairRound) throws Exception {
        List<SchemePersonOffExcelTemplate> templates = (List<SchemePersonOffExcelTemplate>) orionJ2CacheService.get("pmsx::SchemeToPersonOff-import::id", importId);
        log.info("技术支持人员导入的入库数据={}", JSONUtil.toJsonStr(templates));
        List<String> userCodes = templates.stream().map(SchemePersonOffExcelTemplate::getUserCode).collect(Collectors.toList());
        Map<String,List<SchemePersonOffExcelTemplate>> userMap = templates.stream().collect(Collectors.groupingBy(SchemePersonOffExcelTemplate::getUserCode));
        //先查找对应数据
        List<SchemeToPerson> schemeToPersonList = schemeToPersonService.list(new LambdaQueryWrapperX<SchemeToPerson>()
                .eq(SchemeToPerson::getRepairRound, repairRound)
                .in(SchemeToPerson::getUserCode, userCodes));
        if(CollectionUtils.isEmpty(schemeToPersonList)){
            throw new RuntimeException("未找到对应大修轮次人员信息");
        }
        List<String> personIds = schemeToPersonList.stream().map(SchemeToPerson::getPersonId).collect(Collectors.toList());
        List<PersonMange> personMangeList = personMangeService.list(new LambdaQueryWrapper<PersonMange>()
                .in(PersonMange::getId,personIds));

        List<DictValueVO> outFactoryList= dictRedisHelper.getDictListByCode(PMS_OUT_FACTORY_REASON);
        Map<String,String> descToNum = new HashMap<>();
        if(!CollectionUtils.isEmpty(outFactoryList)){
            for (DictValueVO dictValueVO : outFactoryList) {
                descToNum.put(dictValueVO.getDescription(),dictValueVO.getNumber());
            }
        }

        List<PersonMangeVO> personMangeVOS = BeanCopyUtils.convertListTo(personMangeList, PersonMangeVO::new);
        personMangeService.setEveryName(personMangeVOS);
        //查找技术人员信息
        List<BasicUserVO> basicUserVOS = basicUserService.listByUserCodes(userCodes);
        Map<String, BasicUserVO> basicUserMap = basicUserVOS.stream().collect(Collectors.toMap(BasicUserVO::getUserCode, item -> item));
        //台账信息
        List<PersonManageLedger> personManageLedgers = Lists.newArrayList();
        Map<String,String> ledgerMap = Maps.newHashMap();
        personMangeVOS.forEach(item->{
            item.setLeaveReason(descToNum.getOrDefault(userMap.get(item.getNumber()).get(0).getReason(),""));
            item.setActOutDate(userMap.get(item.getNumber()).get(0).getActOutDate());
            item.setIsFinishOutHandover("是".equals(userMap.get(item.getNumber()).get(0).getWorkHandover()));
            item.setIsAgainIn("是".equals(userMap.get(item.getNumber()).get(0).getIsAgainIn()));
            if(Objects.nonNull(userMap.get(item.getNumber()).get(0).getInDate())){
                item.setInDate(userMap.get(item.getNumber()).get(0).getInDate());
            }
            if(Objects.nonNull(userMap.get(item.getNumber()).get(0).getOutDate())){
                item.setOutDate(userMap.get(item.getNumber()).get(0).getOutDate());
            }


            item.setStatus(StatusEnum.DRAFT.getIndex());
            //台账信息
            PersonManageLedger personManageLedger = BeanCopyUtils.convertTo(item, PersonManageLedger::new);
            if(basicUserMap.containsKey(item.getNumber())){
                personManageLedger.setName(basicUserMap.get(item.getNumber()).getFullName());
                personManageLedger.setSex(basicUserMap.get(item.getNumber()).getSex());
                personManageLedger.setIdCard(basicUserMap.get(item.getNumber()).getIdCard());
                personManageLedger.setPoliticalAffiliation(basicUserMap.get(item.getNumber()).getPoliticalAffiliation());
                personManageLedger.setInstituteName(basicUserMap.get(item.getNumber()).getInstituteName());
                personManageLedger.setNowPosition(basicUserMap.get(item.getNumber()).getNowPosition());
                personManageLedger.setNature(basicUserMap.get(item.getNumber()).getPersonnelNature());
                personManageLedger.setCompanyName(basicUserMap.get(item.getNumber()).getCompanyName());
                personManageLedger.setDeptName(basicUserMap.get(item.getNumber()).getDeptName());
            }
            personManageLedger.setType(PersonManageLedgerTypeEnum.OUT.getKey());
            Date date = new Date();
            personManageLedger.setCreateTime(date);
            personManageLedger.setModifyTime(date);
            personManageLedger.setPersonManageId(item.getId());
            personManageLedger.setId(null);
            personManageLedgers.add(personManageLedger);
        });
        //保存台账信息
        personManageLedgerService.saveBatch(personManageLedgers);
        for(PersonManageLedger personManageLedger:personManageLedgers){
            ledgerMap.put(personManageLedger.getPersonManageId(),personManageLedger.getId());
        }
        //修改人员信息
        List<PersonMange> personMets = BeanCopyUtils.convertListTo(personMangeVOS, PersonMange::new);
        personMangeService.updateBatchById(personMets);
        //作业授权信息
        LambdaQueryWrapperX<JobPostAuthorize> wrapper = new LambdaQueryWrapperX<>(JobPostAuthorize.class);
        wrapper.in(JobPostAuthorize::getPersonManageId, personIds);
        List<JobPostAuthorize> jobPostAuthorizeList = jobPostAuthorizeService.list(wrapper);
        if(!CollectionUtils.isEmpty(jobPostAuthorizeList)){
            for(JobPostAuthorize item:jobPostAuthorizeList){
                item.setPersonLedgerId(ledgerMap.get(item.getPersonManageId()));
            }
            jobPostAuthorizeService.updateBatchById(jobPostAuthorizeList);
            List<String> collect = jobPostAuthorizeList.stream().map(JobPostAuthorize::getJobId).distinct().collect(Collectors.toList());
            jobNodeStatusService.setNodeStatusByIdList(collect,"personOut");
        }
        return true;
    }

    private void checkExcel(List<SchemePersonOffExcelTemplate> excelDtoS, ImportExcelCheckResultVO resultVO,String repairRound) {
        List<ImportExcelErrorNoteVO> err = new ArrayList<>();
        //系统人员
        List<UserBaseCacheVO> allUserBaseCache = userRedisHelper.getAllUserBaseCache(CurrentUserHelper.getOrgId());
        Map<String,List<UserBaseCacheVO>> userMap = allUserBaseCache.stream().collect(Collectors.groupingBy(UserBaseCacheVO:: getCode));

        //大修人员
        SchemeToPersonDTO dto = new SchemeToPersonDTO();
        dto.setRepairRound(repairRound);
        List<SchemeToPersonVO> admissionList = schemeToPersonService.getAdmissionList(dto);
        Map<String,List<SchemeToPersonVO>> admissionMap = admissionList.stream().collect(Collectors.groupingBy(SchemeToPersonVO:: getUserCode));
        for(int i = 0;i<excelDtoS.size();i++){
            List<String> errorNotes = Lists.newArrayList();
//            if(!StringUtils.hasText(excelDtoS.get(i).getPersonType())){
//                errorNotes.add("类型不能为空");
//            }
            SchemePersonOffExcelTemplate excelDto = excelDtoS.get(i);
            if(!StringUtils.hasText(excelDto.getUserCode())){
                errorNotes.add("员工号不能为空");
            }else if(!userMap.containsKey(excelDto.getUserCode())){
                errorNotes.add("员工号不存在");
            }else if(!admissionMap.containsKey(excelDto.getUserCode())){
                errorNotes.add("员工号未入场");
            }


            if(!StringUtils.hasText(excelDto.getUserName())){
                errorNotes.add("姓名不能为空");
            }else if (!userMap.get(excelDto.getUserCode()).get(0).getName().equals(excelDto.getUserName())){
                errorNotes.add("员工号"+excelDto.getUserCode()+"姓名不正确");
            }else if (admissionMap.containsKey(excelDto.getUserCode())
                    && !admissionMap.get(excelDto.getUserCode()).get(0).getUserName().equals(excelDto.getUserName())){
                errorNotes.add("员工号"+excelDto.getUserCode()+"姓名在大修中不正确");
            }

            if(!Objects.nonNull(excelDto.getActOutDate())){
                errorNotes.add("实际离场日期不能为空");
            }
            if(!StringUtils.hasText(excelDto.getReason())){
                errorNotes.add("离场原因不能为空");
            }else if(!"项目结束、人员调动、其他".contains(excelDto.getReason())){
                errorNotes.add("离场原因请填写【项目结束、人员调动、其他】");
            }
            if(!StringUtils.hasText(excelDto.getWorkHandover())){
                errorNotes.add("是否完成离场工作交接离场WBC测量（必要时）不能为空");
            }else if(!"是，否".contains(excelDto.getWorkHandover())){
                errorNotes.add("是否完成离场工作交接离场WBC测量（必要时）请填写【是、否】");
            }
            if(!StringUtils.hasText(excelDto.getIsAgainIn())){
                errorNotes.add("是否再次入场不能为空");
            }else if(!"是，否".contains(excelDto.getIsAgainIn())){
                errorNotes.add("是否再次入场请填写【是、否】");
            }else if("是".equals(excelDto.getIsAgainIn())
                    && (!Objects.nonNull(excelDto.getInDate()) || !Objects.nonNull(excelDto.getOutDate()))){
                errorNotes.add("再次入场如为是，则计划入场离场时间必填");
            }else if("否".equals(excelDto.getIsAgainIn())
                    && (Objects.nonNull(excelDto.getInDate()) || Objects.nonNull(excelDto.getOutDate()))){
                errorNotes.add("再次入场如为否，则计划入场离场时间不填");
            }
//            if(Objects.nonNull(excelDto.getActOutDate()) && !isValidDate(excelDto.getActOutDate())){
//                errorNotes.add("实际离场日期格式不正确，请输入yyyy/MM/dd");
//            }
//            if(StringUtils.hasText(excelDto.getInDate()) && !isValidDate(excelDto.getInDate())){
//                errorNotes.add("计划入场日期格式不正确，请输入yyyy/MM/dd");
//            }
//            if(StringUtils.hasText(excelDto.getOutDate()) && !isValidDate(excelDto.getOutDate())){
//                errorNotes.add("计划离场日期格式不正确，请输入yyyy/MM/dd");
//            }
            if(Objects.nonNull(excelDto.getInDate()) && Objects.nonNull(excelDto.getOutDate()) ){
                if(excelDto.getOutDate().compareTo(excelDto.getInDate()) < 0){
                    errorNotes.add("“计划离场日期”不能早于“计划入场日期”");
                }
            }
            if(!CollectionUtils.isEmpty(errorNotes)){
                ImportExcelErrorNoteVO importExcelErrorNoteVO = new ImportExcelErrorNoteVO();
                importExcelErrorNoteVO.setOrder((i+2)+"");
                importExcelErrorNoteVO.setErrorNotes(errorNotes);
                err.add(importExcelErrorNoteVO);
            }
        }

        if(!CollectionUtils.isEmpty(err)){
            resultVO.setErr(err);
            resultVO.setCode(200);
            return;
        }
        resultVO.setErr(err);
    }

    private static final String DATE_PATTERN = "^(\\d{4})([/-])(0[1-9]|1[0-2])\\2(0[1-9]|[12]\\d|3[01])$";

    public static boolean isValidDate(String dateStr) {
        if (dateStr == null) {
            return false;
        }
        Pattern pattern = Pattern.compile(DATE_PATTERN);
        Matcher matcher = pattern.matcher(dateStr);
        return matcher.matches();
    }
    public static class UserExcelReadListener implements ReadListener<SchemePersonOffExcelTemplate> {
        private final List<SchemePersonOffExcelTemplate> userExcelDTOS = new ArrayList<>();


        public List<SchemePersonOffExcelTemplate> getUserExcelDTOS() {
            return userExcelDTOS;
        }

        @Override
        public void invoke(SchemePersonOffExcelTemplate data, AnalysisContext context) {
            userExcelDTOS.add(data);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {

        }

        @Override
        public void extra(CellExtra extra, AnalysisContext context) {
        }
    }
}
