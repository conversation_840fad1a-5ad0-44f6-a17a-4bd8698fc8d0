package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.Boolean;
import java.util.List;


/**
 * ProjectMaterialPreparation VO对象
 *
 * <AUTHOR>
 * @since 2024-06-25 15:41:46
 */
@ApiModel(value = "ProjectMaterialPreparationVO对象", description = "备料与加工申请")
@Data
public class ProjectMaterialPreparationVO extends  ObjectVO   implements Serializable{

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;


    /**
     * 申请部门
     */
    @ApiModelProperty(value = "申请部门")
    private String applyDept;


    /**
     * 产品/电路编码
     */
    @ApiModelProperty(value = "产品/电路编码")
    private String materialNumber;


    /**
     * 需要完成时间
     */
    @ApiModelProperty(value = "需要完成时间")
    private Date requireCompleteTime;


    /**
     * 申请原因
     */
    @ApiModelProperty(value = "申请原因")
    private String applyReason;


    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;


    /**
     * 剩余材料费
     */
    @ApiModelProperty(value = "剩余材料费")
    private BigDecimal restMaterialFee;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private String revId;


    /**
     * 是否是主版本
     */
    @ApiModelProperty(value = "是否是主版本")
    private Boolean isMainRev;


    /**
     * 审核时间
     */
    @ApiModelProperty(value = "审核时间")
    private Date auditTime;


    /**
     * 版本key
     */
    @ApiModelProperty(value = "版本key")
    private String revKey;


    @ApiModelProperty(value = "备料信息")
    private List<ProjectMaterialPreparationInfoVO> preparationInfoList;

}
