package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.page.SearchCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * AdjustmentVoucher DTO对象
 *
 * <AUTHOR>
 * @since 2024-12-24 10:44:40
 */
@ApiModel(value = "AdjustmentVoucherDTO对象", description = "调账凭证数据表")
@Data
@ExcelIgnoreUnannotated
public class AdjustmentVoucherDTO extends  ObjectDTO   implements Serializable{

    /**
     * 公司代码
     */
    @ApiModelProperty(value = "公司代码")
    @ExcelProperty(value = "公司代码 ", index = 0)
    private String companyCode;

    /**
     * 科目
     */
    @ApiModelProperty(value = "科目")
    @ExcelProperty(value = "科目 ", index = 1)
    private String subject;

    /**
     * 分配
     */
    @ApiModelProperty(value = "分配")
    @ExcelProperty(value = "分配 ", index = 2)
    private String allocation;

    /**
     * 过账期间
     */
    @ApiModelProperty(value = "过账期间")
    @ExcelProperty(value = "过账期间 ", index = 3)
    private String postingPeriod;

    /**
     * 凭证编号
     */
    @ApiModelProperty(value = "凭证编号")
    @ExcelProperty(value = "凭证编号 ", index = 4)
    private String voucherNum;

    /**
     * 凭证日期
     */
    @ApiModelProperty(value = "凭证日期")
    @ExcelProperty(value = "凭证日期 ", index = 5)
    private Date voucherDate;

    /**
     * 过账日期
     */
    @ApiModelProperty(value = "过账日期")
    @ExcelProperty(value = "过账日期 ", index = 6)
    private Date postingDate;

    /**
     * 本币金额
     */
    @ApiModelProperty(value = "本币金额")
    @ExcelProperty(value = "本币金额 ", index = 7)
    private BigDecimal localCurrencyAmount;

    /**
     * 利润中心
     */
    @ApiModelProperty(value = "利润中心")
    @ExcelProperty(value = "利润中心 ", index = 8)
    private String profitCenter;

    /**
     * 成本中心
     */
    @ApiModelProperty(value = "成本中心")
    @ExcelProperty(value = "成本中心 ", index = 9)
    private String costCenter;

    /**
     * 文本文档
     */
    @ApiModelProperty(value = "文本文档")
    @ExcelProperty(value = "文本文档 ", index = 10)
    private String conText;

    /**
     * wbs要素
     */
    @ApiModelProperty(value = "wbs要素")
    @ExcelProperty(value = "wbs要素 ", index = 11)
    private String wbsElement;

    /**
     * Tr.prt
     */
    @ApiModelProperty(value = "Tr.prt")
    @ExcelProperty(value = "Tr.prt ", index = 12)
        private String trprt;

    /**
     * 承诺项目
     */
    @ApiModelProperty(value = "承诺项目")
    @ExcelProperty(value = "承诺项目 ", index = 13)
    private String committedProject;

    /**
     * 基金中心
     */
    @ApiModelProperty(value = "基金中心")
    @ExcelProperty(value = "基金中心 ", index = 14)
    private String fundingCenter;

    /**
     * 付款基准日期
     */
    @ApiModelProperty(value = "付款基准日期")
    @ExcelProperty(value = "付款基准日期 ", index = 15)
    private Date payReferenceDate;

    @ApiModelProperty(value = "关联合同id")
    private String contractId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNum;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;


    @ApiModelProperty(value = "合同里程碑")
    private String milestoneName;

    @ApiModelProperty(value = "合同里程碑id")
    private String milestoneId;

    @ApiModelProperty(value = "挂接状态:0未挂接，1已挂接")
    private Integer hangingConnectStatus;


    @ApiModelProperty(value = "导出id")
    private List<String> ids;

    @ApiModelProperty
    private List<List<SearchCondition>> searchConditions;
}
