<template>
  <layout :options="{ body: { scroll: true } }">
    <div class="productLibraryIndex1 layoutPage">
      <div class="productLibraryIndex_content layoutPage_content">
        <div class="productLibraryIndex_table">
          <BasicTable
            class="pdmBasicTable"
            :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
            :columns="columns"
            :data-source="dataSource"
            :bordered="false"
            :can-resize="true"
            :show-index-column="false"
            :pagination="false"
            :max-height="tableHeight"
            row-key="id"
            @change="handleChange"
            @rowClick="clickRow"
            @register="registerTable"
          >
            <template #modifyTime="{ text }">
              {{ text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '' }}
            </template>
          </BasicTable>
        </div>
      </div>

      <newButtonModal
        :btn-object-data="btnObjectData"
        @clickType="clickType"
      />
      <!-- 查看详情弹窗 -->
      <checkDetails :data="nodeData" />
      <!-- 简易弹窗提醒 -->
      <messageModal
        :title="'确认提示'"
        :show-visible="showVisible"
        @cancel="showVisible = false"
        @confirm="confirm"
      >
        <div class="messageVal">
          <InfoCircleOutlined />
          <span>{{ message }}</span>
        </div>
      </messageModal>
      <!-- 新建/编辑抽屉 -->
      <addProjectModal
        :data="addNodeModalData"
        :list-data="editdataSource"
        :projectid="id"
        @success="successSave"
      />
      <!-- 从系统添加 -->
      <AddSystemRole
        :id="id"
        :data="addSystemModalData"
        @success="successSave"
      />

      <!-- 高级搜索抽屉 -->
      <searchModal
        :data="searchData"
        :projectid="id"
        @search="searchTable"
      />
    </div>
  </layout>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, computed, onMounted, inject,
} from 'vue';
// import Layout from '/@/components/Layout';
import {
  useActionsRecord, Layout, OrionTable, BasicTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer, isPower,
} from 'lyra-component-vue3';
import {
  Dropdown, Menu, message, Progress,
} from 'ant-design-vue';
import {
  PlusCircleOutlined,
  InfoCircleOutlined,
  DeleteOutlined,
  ImportOutlined,
  ExportOutlined,
  PlusOutlined,
} from '@ant-design/icons-vue';
/* 格式化时间 */
import { formatterTime } from '/@/views/pms/projectLaborer/utils/time';
import addProjectModal from './modal/addProjectModal.vue';
import AddSystemRole from './modal/addSystemRole.vue';
import checkDetails from './modal/checkmodal.vue';
import searchModal from './modal/searchModal.vue';
import messageModal from '/@/views/pms/projectLaborer/components/MessageModal/messageModal.vue';
import { useRouter } from 'vue-router';
import newButtonModal from '/@/views/pms/projectLaborer/componentsList/buttonModal/newButtonModal.vue';
/* table */
// import { BasicTable, useTable } from '/@/components/Table';
import dayjs from 'dayjs';
import { columns } from './src/table.config';
import { concactTableApi, deleteItemApi } from '/@/views/pms/projectLaborer/api/planList';
import { parseURL } from '/@/views/pms/projectLaborer/utils/index';
const [registerTable, { setLoading }] = useTable();
export default defineComponent({
  name: 'ProductLibraryIndex',
  components: {
    Layout,
    //   basicTitle,
    // aDropdown: Dropdown,
    /* 表格 */
    BasicTable,
    // aMenu: Menu,
    // /* menu子item */
    // aMenuItem: Menu.Item,
    // /* 添加图标 */
    // PlusCircleOutlined,
    // /* 删除图标 */
    // DeleteOutlined,
    //   提示图标
    InfoCircleOutlined,
    //   addNodeModal,
    messageModal,
    checkDetails,
    newButtonModal,
    /* 新建项目抽屉 */
    addProjectModal,
    /* 进度条 */
    // Progress,
    /* 高级搜索 */
    searchModal,
    // ImportOutlined,
    // ExportOutlined,
    // PlusOutlined,
    AddSystemRole,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
  },

  setup(props) {
    //   const router = useRouter();
    //   const layoutModelStore = layoutModel();
    const state = reactive({
      /* 搜索框value */
      searchvlaue: '',
      /* 编辑send */
      editdataSource: {},
      /* 多选 */
      selectedRowKeys: [],
      /* 列 */
      dataSource: [],
      tablehttp: {
        orders: [
          {
            asc: false,
            column: '',
          },
        ],

        query: {
          projectId: '',
        },
        // 条数
        pageSize: 10,
        /* 页数 */
        pageNum: 1,
        /* 总数 */
        total: 0,
        queryCondition: [],
      },
      // 条数
      pageSize: 10,
      /* 页数 */
      current: 1,
      /* 总数 */
      total: 20,
      addNodeModalData: {},
      /* 选择行id */
      selectedRows: [],
      addSystemModalData: {},

      showVisible: false,
      /* 简易弹窗提醒消息 */
      message: '',
      nodeData: {},
      searchData: {},
      params: {},
      /* 高度 */
      tableHeight: 400,
      powerData: [],
    });
    const state6 = reactive({
      btnObjectData: {
        check: { show: computed(() => isPower('RWX_container_button_45', state.powerData)) },
        // open: { show: isPower('RWX_container_button_46', state.powerData) },
        add: { show: computed(() => isPower('RWX_container_button_47', state.powerData)) },
        delete: { show: computed(() => isPower('RWX_container_button_48', state.powerData)) },

        search: { show: computed(() => isPower('RWX_container_button_49', state.powerData)) },
        //   edit: { show: true },
      },
    });
    /* 分页 */
    const pagination = computed(() => ({
      pageSize: state.tablehttp.pageSize,
      current: state.tablehttp.pageNum,
      total: state.tablehttp.total,
      // showQuickJumper: true,
      showSizeChanger: true,
      showTotal: (total) => `共${total}条`,
    }));
      /* 多选cb */
    const onSelectChange = (selectedRowKeys, selectedRows) => {
      state.selectedRowKeys = selectedRowKeys;
      state.selectedRows = selectedRows;
      console.log('测试🚀🚀 ~~~ state.selectedRows', state.selectedRows);
    };
      /* 页数变化cb */
    const handleChange = (pag, filters, sorter: any) => {
      // 如果是多选触发,则不更新页面
      if (typeof pag.current === 'undefined') return;
      state.tablehttp.pageNum = pag.current;
      state.tablehttp.pageSize = pag.pageSize;
      state.tablehttp.orders[0].asc = sorter.order == 'ascend';
      state.tablehttp.orders[0].column = sorter.columnKey;

      getFormData();
    };
      /* 右按钮 */
    const clickType = (type) => {
      switch (type) {
        case 'edit':
          editNode();
          break;
        case 'check':
          checkData();
          break;
        case 'add':
          addSystemRoleHandle();
          break;
        case 'open':
          // openDetail();
          break;
        case 'delete':
          // deleteNode();
          multiDelete();
          break;
        case 'search':
          // state.searchData = {};
          break;
      }
    };
    const router = useRouter();
    const addSystemRoleHandle = () => {
      // console.log('从系统创建角色');
      state.addSystemModalData = { formType: 'add' };
    };
      /* 编辑 */
    const editNode = () => {
      if (lengthCheckHandle()) return;
      // state.selectedRows = [];
      state.addNodeModalData = { formType: 'edit' };
      state.editdataSource = [...state.selectedRows];
    };
      /* 删除 */
    const deleteNode = () => {
      if (lengthCheckHandle()) return;
      // state.selectedRows = [];

      state.message = '请确认是否对当前选中数据进行删除？';
      state.showVisible = true;
    };
      /* 简易弹窗的确定cb */
    const confirm = () => {
      // 删除操作
      deletrow();
    };
    onMounted(() => {
      /* 高度变化 */
      state.tableHeight = document.body.clientHeight - 440;

      getFormData();
    });
    let riskItemId: any = inject('riskItemId');

    /* 删除操作 */
    const deletrow = () => {
      const newArr = {
        fromId: parseURL().id,
        toIdList: state.selectedRowKeys,
      };
      const love = {
        className: 'Plan',
        moduleName: '项目管理-计划管理-项目计划-关联内容-关联零组件',
        type: 'DELETE',
        remark: `删除了【${state.selectedRowKeys}】`,
      };
        // new Api('/pms')
        //   .fetch(state.selectedRowKeys, `project/removeBatch/`, 'DELETE')
      deleteItemApi(newArr, love)
        .then((res) => {
          message.success('删除成功');
          state.showVisible = false;
          getFormData();
        })
        .catch(() => {
          state.showVisible = false;
        });
    };

    const getFormData = async () => {
      setLoading(true);
      // state.tablehttp.query.projectId = riskItemId.value;
      const res = await concactTableApi(parseURL().id, 'plan');
      state.dataSource = res;
      // state.tablehttp.total = res.totalSize;
      state.selectedRowKeys = [];
      state.selectedRows = [];
      setLoading(false);
    };
      /* 查看详情 */
    const checkData = () => {
      if (lengthCheckHandle()) return;
      state.nodeData = {
        ...state.dataSource.filter((item) => item.id == state.selectedRowKeys[0]),
      };
    };
      /* 检查选择条数fn */
    const lengthCheckHandle = () => {
      if (state.selectedRows.length > 1) {
        message.warning('请选择一条数据进行操作');
        return true;
      }
      if (state.selectedRows.length == 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };
      /* 批量检查选择条数fn */
    const multiLengthCheckHandle = () => {
      if (state.selectedRows.length == 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };
    const searchTable = (params) => {
      state.tablehttp.query = params.params;
      state.tablehttp.queryCondition = params.queryCondition;

      getFormData();
      state.searchvlaue = '';
    };
      /* 打开按钮 */
    const openDetail = () => {
      if (lengthCheckHandle()) return;

      // toDetails(state.selectedRows[0]);
      state.searchvlaue = '';
    };
    const toDetails = (data) => {
      router.push({
        name: 'RiskDetails',
        query: {
          id: data.id,
          projectId: props.id,
          type: 0,
        },
      });
    };
      /* 新建项目 */
    const addNode = () => {
      state.addNodeModalData = {
        formType: 'add',
      };
    };
      /* 批量删除 */
    const multiDelete = () => {
      if (multiLengthCheckHandle()) return;

      state.message = '请确认是否对当前选中数据进行删除？';
      state.showVisible = true;
    };
      /* 搜索右上 */
    const onSearch = () => {
      /* gettable */
      state.tablehttp.queryCondition = <any>[
        {
          column: 'name',
          type: 'like',
          link: 'or',
          value: state.searchvlaue,
        },
        {
          column: 'number',
          type: 'like',
          link: 'or',
          value: state.searchvlaue,
        },
      ];
      state.tablehttp.query = { projectId: '' };
      getFormData();
    };
      /* 新建项目成功回调 */
    const successSave = () => {
      state.tablehttp.pageNum = 1;
      state.selectedRowKeys = [];
      state.selectedRows = [];

      getFormData();
      state.searchvlaue = '';
      onSearch();
    };
    const clickRow = (record, index) => {
      const num = state.selectedRowKeys.findIndex((item) => item === record.id);
      num === -1 ? state.selectedRowKeys.push(record.id) : state.selectedRowKeys.splice(num, 1);
      const row = state.selectedRows.findIndex((item) => item.id === record.id);
      row === -1 ? state.selectedRows.push(record) : state.selectedRows.splice(row, 1);
    };
    return {
      ...toRefs(state),
      ...toRefs(state6),
      clickRow,
      clickType,
      /* 分页 */
      pagination,
      /* 行 */
      columns,
      /* 多选 */
      onSelectChange,
      /* 多选变化 */
      handleChange,
      /* 格式化时间 */
      formatterTime,
      /* 简易弹窗cb */
      confirm,
      /* 新增按钮 */
      addNode,
      dayjs,
      /* 批量删除 */
      multiDelete,
      /* 搜索右上角 */
      onSearch,
      successSave,
      searchTable,
      addSystemRoleHandle,
      registerTable,
      setLoading,
    };
  },

  // mounted() {}
});
</script>
<style lang="less" scoped>
  @import url('/@/views/pms/projectLaborer/statics/style/page.less');
  @import url('/@/views/pms/projectLaborer/statics/style/margin.less');
</style>
