<template>
  <BasicForm @register="registerForm">
    <template #triggerType="{ model, field }">
      <ShowTemplate
        ref="argumentTemplateRef"
        modal-type="edit"
        :data="model[field]"
      />
    </template>
  </BasicForm>
</template>

<script setup lang="ts">
import {
  defineExpose, watch, reactive, computed, h, defineProps, ref,
} from 'vue';
import {
  BasicForm, FormSchema, useForm, Empty, BasicCard, Description, useDescription,
} from 'lyra-component-vue3';
import Api from '/@/api';
import dayjs from 'dayjs';
import ShowTemplate
  from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/icmManagement/icmManagementDetails/components/showTemplate/ArgumentTemplate.vue';

const argumentTemplateRef = ref(null);
const state = reactive({
  modelDetails: {},
});
const props = defineProps({
  action: {
    type: String,
    default: 'add',
  },
});

async function valtriggerTypeAsAdd(rule, value) {
  return Promise.resolve();
}

const schemas: FormSchema[] = [
  {
    field: 'name',
    component: 'Input',
    label: '实例名称：',
    colProps: {
      span: 12,
    },
    rules: [{ required: true }],
    componentProps: {
      disabled: props.action === 'edit',
    },
  },
  {
    field: 'way',
    component: 'Select',
    label: '使用方式',
    colProps: {
      span: 12,
    },
    rules: [{ required: true }],
    componentProps: {
      options: [
        {
          label: '拷贝',
          value: '1',
        },
        {
          label: '引用',
          value: '2',
        },
      ],
    },
  },
  {
    field: 'triggerType',
    component: 'Select',
    label: '参数类型：',
    colProps: {
      span: 24,
    },
    defaultValue: {
      type: '',
    },
    rules: [
      {
        type: 'object',
        required: true,
        validator: valtriggerTypeAsAdd,
        trigger: 'change',
      },
    ],
    slot: 'triggerType',
  },
];
const [registerForm, FormMethods] = useForm({
  // layout: 'vertical',
  schemas,
  baseColProps: {
    span: 12,
  },
  actionColOptions: {
    span: 24,
  },
});

function getModelDetails(id) {
  new Api(`/pdm/parameterPoolIns/${id}`).fetch('', '', 'GET').then((res) => {
    if (res) {
      state.modelDetails = res;
      let triggerType = {};
      [
        'type',
        'txtDesc',
        'numValueType',
        'numValue',
        'numValueUnit',
        'tableFormat',
        'images',
        'equation',
        'href',
        'attachments',
      ].forEach((item) => {
        triggerType[item] = res[item];
      });
      state.modelDetails.triggerType = triggerType;
      state.modelDetails.way = String(state.modelDetails.way);
      FormMethods.setFieldsValue(state.modelDetails);
    } else {
      state.modelDetails = {};
    }
  }).finally(() => {
    argumentTemplateRef.value.canChangeType();
  });
}

function getData() {
  let tableFormat = argumentTemplateRef.value.getData();
  return tableFormat;
}

async function setDefaultValue(id, insId) {
  await new Api(`/pdm/parameterPoolModule/${id}`).fetch('', '', 'GET').then((res) => {
    if (res) {
      argumentTemplateRef.value.changeType({ value: res.type });
    }
  });
  if (insId) {
    await new Api(`/pdm/parameterPoolIns/${insId}`).fetch('', '', 'GET').then((res) => {
      if (res) {
        let triggerType = {};
        [
          'type',
          'txtDesc',
          'numValueType',
          'numValue',
          'numValueUnit',
          'tableFormat',
          'images',
          'equation',
          'href',
          'attachments',
        ].forEach((item) => {
          triggerType[item] = res[item];
        });
        FormMethods.setFieldsValue({ triggerType });
      } else {
        state.modelDetails = {};
      }
    });
  }
  // canChangeType
  argumentTemplateRef.value.canChangeType();
}

defineExpose({
  FormMethods,
  getModelDetails,
  setDefaultValue,
  getData,
});
</script>

<style scoped lang="less">
.fontStyle {
  font-size: 20px;
}
</style>
