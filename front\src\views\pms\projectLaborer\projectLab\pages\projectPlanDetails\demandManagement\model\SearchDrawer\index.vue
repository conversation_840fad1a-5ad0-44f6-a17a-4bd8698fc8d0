<template>
  <BasicDrawer
    v-bind="$attrs"
    title="搜索"
    width="400"
    showFooter
    :showCancelBtn="false"
    :showOkBtn="false"
    wrapClassName="demandManagementSearcch"
    @register="drawerRegister"
    @visibleChange="visibleChange"
  >
    <Content
      :principalOptions="principalOptions"
      :priorityLevelOptions="priorityLevelOptions"
      :statusOptions="statusOptions"
      :exhibitorOptions="exhibitorOptions"
      @init="formInit"
    />

    <template #footer>
      <DrawerFooterButtons
        :isContinue="false"
        @clearClick="clearClick"
        @cancelClick="cancelClick('close')"
        @okClick="okClick"
      />
    </template>

    <!--    <template #centerFooter />-->

    <!--    <template #appendFooter>-->
    <!--      <div class="demandManagementSearcch_bottom">-->
    <!--        <a-button-->
    <!--          size="large"-->
    <!--          @click="clearClick"-->
    <!--        >-->
    <!--          清空-->
    <!--        </a-button>-->
    <!--        <a-button-->
    <!--          size="large"-->
    <!--          @click="cancelClick('close')"-->
    <!--        >-->
    <!--          取消-->
    <!--        </a-button>-->
    <!--        <a-button-->
    <!--          size="large"-->
    <!--          type="primary"-->
    <!--          @click="okClick"-->
    <!--        >-->
    <!--          确认-->
    <!--        </a-button>-->
    <!--      </div>-->
    <!--    </template>-->
  </BasicDrawer>
</template>

<script lang="ts">
import { BasicDrawer, getDict, useDrawerInner } from 'lyra-component-vue3';
import { onMounted, reactive, toRefs } from 'vue';
import Content from './Content.vue';
import DrawerFooterButtons from '/@/views/pms/components/DrawerFooterButtons.vue';
import Api from '/@/api';

export default {
  name: 'Index',
  components: {
    BasicDrawer,
    Content,
    DrawerFooterButtons,
  },
  setup(props, { emit }) {
    const state = reactive({
      visible: false,
      formMethods: null,
      onOk: null,
      principalOptions: [],
      priorityLevelOptions: [],
      statusOptions: [],
      exhibitorOptions: [],
    });
    const [drawerRegister, { closeDrawer }] = useDrawerInner((drawerData) => {
      state.formMethods?.resetFields();
      state.formMethods?.setFieldsValue(drawerData);
    });

    function formInit({ formMethods }) {
      state.formMethods = formMethods;
    }

    function cancelClick() {
      closeDrawer();
    }

    function visibleChange(visible) {
      state.visible = visible;
    }
    function clearClick() {
      state.formMethods?.resetFields();
      emit('search', {});
      closeDrawer();
    }

    function okClick() {
      const formParams:any = state.formMethods?.getFieldsValue();
      if (formParams.principalId) {
        let principalItem = state.principalOptions.find((item) => item.id === formParams.principalId);
        formParams.principalIdName = principalItem.name;
      }
      if (formParams.priorityLevel) {
        let priorityLevelItem = state.priorityLevelOptions.find((item) => item.value === formParams.priorityLevel);
        formParams.priorityLevelName = priorityLevelItem.description;
      }
      if (formParams.exhibitor) {
        let exhibitorItem = state.exhibitorOptions.find((item) => item.id === formParams.exhibitor);
        formParams.exhibitorName = exhibitorItem.name;
      }
      if (formParams.status) {
        let statusData = state.statusOptions.find((item) => item.statusValue === formParams.status);
        formParams.statusName = statusData.name;
      }
      emit('search', formParams);
      closeDrawer();
    }
    onMounted(async () => {
      state.principalOptions = await new Api('/pas/demand-management/principal/list').fetch({}, '', 'GET');
      state.priorityLevelOptions = await getDict('dictc56421e19b264d9c91394c48e447e4cb');
      state.statusOptions = await new Api('/pas/demand-management/status/list').fetch({}, '', 'GET');
      state.exhibitorOptions = await new Api('/pas/demand-management/exhibitor/list').fetch({}, '', 'GET');
      // statusOptions: [],
      // exhibitorOptions: [],
    });
    return {
      ...toRefs(state),
      drawerRegister,
      cancelClick,
      okClick,
      formInit,
      visibleChange,
      clearClick,
    };
  },
};
</script>

<style scoped lang="less">
.demandManagementSearcch{
  .demandManagementSearcch_bottom{
    .ant-btn+.ant-btn{
      margin-left: 10px;
    }
  }
}
</style>
