package com.chinasie.orion.repository;
import com.chinasie.orion.domain.dto.MajorRepairOrgJobSimpleDTO;
import com.chinasie.orion.domain.entity.MajorRepairOrg;
import com.chinasie.orion.domain.vo.MajorRepairOrgSimpleVO;
import org.apache.ibatis.annotations.Mapper;

import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <p>
 * MajorRepairOrg Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18 09:32:26
 */
@Mapper
public interface MajorRepairOrgMapper extends  OrionBaseMapper  <MajorRepairOrg> {


    List<MajorRepairOrgSimpleVO> listByRepairRoundAndParenId(@Param("repairRound") String repairRound, @Param("parentId") String parentId);

    List<MajorRepairOrgJobSimpleDTO> listSimpleByOrgId(@Param("repairOrgId") String repairOrgId);

    Integer getMaxSort(@Param("parentId") String parentId);
}

