package com.chinasie.orion.exp.marketManagement;

import com.chinasie.orion.permission.core.core.type.ValueExpType;
import com.chinasie.orion.permission.core.exp.IExp;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * Keafmd
 *
 * @ClassName: CenterBusinessOrTechnicalExp
 * @Description: 商务接口人 or 技术接口人
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/8/12 11:37
 * @Blog: https://keafmd.blog.csdn.net/
 */
@Component
@Slf4j
public class CenterBusinessOrTechnicalExp implements IExp {
    @Override
    public ValueExpType group() {
        return ValueExpType.CUSTOM;
    }

    @Override
    public String expName() {
        return "商务接口人 or 技术接口人 本人";
    }

    @Override
    public List<String> exp(String s) {
        return Arrays.asList(CurrentUserHelper.getCurrentUserId());
    }

    @Override
    public Boolean apply() {
        return true;
    }
}
