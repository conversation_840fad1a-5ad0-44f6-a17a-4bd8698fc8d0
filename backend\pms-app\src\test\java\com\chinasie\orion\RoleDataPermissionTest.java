package com.chinasie.orion;

import com.chinasie.orion.management.domain.entity.ProjectInitiation;
import com.chinasie.orion.management.repository.ProjectInitiationMapper;
import com.chinasie.orion.management.service.ProjectInitiationService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/07/26/13:57
 * @description:
 */
@SpringBootTest(classes = PMSApplication.class)
public class RoleDataPermissionTest {

    @Autowired
    private ProjectInitiationService projectInitiationService;

    @Autowired
    private ProjectInitiationMapper projectInitiationMapper;


    @Test
    public  void firstRoleDataPermissionTest(){


//        CurrentUserHelper.setUserId("as4f1787364811265425408");
        CurrentUserHelper.setUserId("as4fb3e2cd10e9474c0f9653a367614461a4");
        CurrentUserHelper.setAttributes("ykovb40e9fb1061b46fb96c4d0d3333dcc13", "rxlm04256c6e0d9d429a89084be972fdfa7f");

        List<String> classIds = Arrays.asList(
                "oglr1792431136308420608",
                "oglr1793554558153359360",
                "oglr1795344102154563584",
                "oglr1796057808853139456",
                "oglr1796058106686472192",
                "oglr1796058634120200192",
                "oglr1800360179712364544",
                "oglr1800497065672036352"
        );

        List<ProjectInitiation> classAttributes = projectInitiationMapper.selectDataPermissionList(ProjectInitiation.class
                , new LambdaQueryWrapperX<>(ProjectInitiation.class)
        );
//        List<SysParamConf> classAttributes2 = sysParamConfMapper.selectList(new LambdaQueryWrapperX<>(SysParamConf.class)
//                .in(SysParamConf::getId, classIds)
//        );
//
//        PageDTO<SysParamConf> mpPage = new PageDTO<>(1, 10);
//
//        sysParamConfMapper.selectDataPermissionPage(mpPage, SysParamConf.class, new LambdaQueryWrapperX<>(SysParamConf.class)
//                .in(SysParamConf::getId, classIds)
//        );

        System.out.println(1);


    }
}
