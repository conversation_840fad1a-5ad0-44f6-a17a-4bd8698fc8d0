<template>
  <Card
    content-title="快捷入口"
    class="icon-nav-wrap"
    :isMore="true"
    @moreClick="moreClick"
  >
    <div class="icon-nav-content flex">
      <div
        v-for="(item, index) in iconArr"
        :key="index"
        class="flex flex-pc"
        @click="goModule(item)"
      >
        <div class="flex flex-ac flex-ver">
          <div
            class="icon"
            :class="item.id"
          />
          <div>{{ item.name }}</div>
        </div>
      </div>
    </div>
  </Card>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { useRouter } from 'vue-router';
import Card from './Card.vue';

export default defineComponent({
  name: 'IconNav',
  components: {
    Card,
  },
  setup() {
    const router = useRouter();
    return {
      moreClick() {
        console.log('更多');
      },
      goModule(item) {
        const { id } = item;
        switch (id) {
          case 'icon01':
            router.push({
              name: 'EstablishLibrary',
            });
            break;
          case 'icon02':
            router.push({
              name: 'PersonalWorkbench',
            });
            break;
          case 'icon03':
            router.push({
              name: 'PrivateData',
            });
            break;
          case 'icon04':
            router.push({
              name: 'KMSHomePage',
            });
            break;
          default:
            break;
        }
      },
      iconArr: [
        {
          id: 'icon01',
          name: '项目库',
        },
        {
          id: 'icon02',
          name: '个人工作台',
        },
        {
          id: 'icon03',
          name: '个人数据区',
        },
        {
          id: 'icon04',
          name: '知识工程',
        },
      ],
    };
  },
});
</script>

<style scoped lang="less">
  .icon-nav-wrap {
    margin-top: 16px;
    height: 200px;
  }

  .icon-nav-content {
    padding: 15px 0 0;

    > div {
      flex: 1;

      > div {
        width: 100px;
        cursor: pointer;
        border-radius: 3px;
        padding: 10px;
        transition: 0.3s;

        &:hover {
          background: #f6f8fa;
        }
      }

      .icon {
        width: 50px;
        height: 50px;
        border-radius: 100px;
        margin-bottom: 10px;
        background-repeat: no-repeat;
        background-position: center;
        background-size: 26px;

        //&.icon01 {
        //  background-color: #3a7deb;
        //  background-image: url('../img/icon01.png');
        //}
        //
        //&.icon02 {
        //  background-color: #ff5b33;
        //  background-image: url('../img/icon02.png');
        //}
        //
        //&.icon03 {
        //  background-color: #4e6ed8;
        //  background-image: url('../img/icon03.png');
        //  background-size: 22px;
        //}
        //
        //&.icon04 {
        //  background-color: #7986cb;
        //  background-image: url('../img/icon04.png');
        //}
      }
    }
  }
</style>
