package com.chinasie.orion.controller.projectStatistics;

import com.chinasie.orion.domain.dto.projectStatistics.ProjectRiskStatisticsDTO;
import com.chinasie.orion.domain.dto.projectStatistics.ProjectWorkHourStatisticsDTO;
import com.chinasie.orion.domain.vo.RiskManagementVO;
import com.chinasie.orion.domain.vo.projectStatistics.ProjectWorkHourStatisticsVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.projectStatistics.ProjectWorkHourStatisticsService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/projectWorkHourStatistics")
@Api(tags = "项目内工时统计")
public class ProjectWorkHourStatisticsController {
    @Autowired
    private ProjectWorkHourStatisticsService projectWorkHourStatisticsService;


    @ApiOperation(value = "工时总数统计")
    @RequestMapping(value = "/getProjectWorkHourTotalStatistics", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【工时总数统计】", type = "项目内报表", bizNo = "")
    public ResponseDTO<ProjectWorkHourStatisticsVO> getProjectWorkHourTotalStatistics(@RequestBody ProjectWorkHourStatisticsDTO projectWorkHourStatisticsDTO) throws Exception {
        ProjectWorkHourStatisticsVO rsp =  projectWorkHourStatisticsService.getProjectWorkHourTotalStatistics(projectWorkHourStatisticsDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "工时统计")
    @RequestMapping(value = "/getProjectWorkHourTimeStatistics", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【工时统计】", type = "项目内报表", bizNo = "")
    public ResponseDTO<List<ProjectWorkHourStatisticsVO>> getProjectWorkHourTimeStatistics(@RequestBody ProjectWorkHourStatisticsDTO projectWorkHourStatisticsDTO) throws Exception {
        List<ProjectWorkHourStatisticsVO> rsp =  projectWorkHourStatisticsService.getProjectWorkHourTimeStatistics(projectWorkHourStatisticsDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "工时分页查询")
    @RequestMapping(value = "/getProjectWorkHourPages", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【工时分页】", type = "项目内报表", bizNo = "")
    public ResponseDTO<Page<ProjectWorkHourStatisticsVO>> getProjectWorkHourPages(@RequestBody Page<ProjectWorkHourStatisticsDTO> pageRequest) throws Exception {
        Page<ProjectWorkHourStatisticsVO> rsp =  projectWorkHourStatisticsService.getProjectWorkHourPages(pageRequest);
        return new ResponseDTO<>(rsp);
    }
}
