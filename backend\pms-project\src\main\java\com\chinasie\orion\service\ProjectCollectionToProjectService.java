package com.chinasie.orion.service;

import com.chinasie.orion.domain.entity.ProjectCollectionToProject;
import com.chinasie.orion.mybatis.service.OrionBaseService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * ProjectCollectionToProject 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-25 17:33:42
 */
public interface ProjectCollectionToProjectService extends OrionBaseService<ProjectCollectionToProject> {
    List<ProjectCollectionToProject> getAllChild(List<String> currentIds, List<ProjectCollectionToProject> all, int maxCircul) throws Exception;

    List<String> getAllChildProject(String currentId) throws Exception;

    List<ProjectCollectionToProject> getAllParents(String currentId);

    List<String> getTree(List<String> ids, Map<String, List<String>> map, Map<String, List<String>> result);


    List<ProjectCollectionToProject> getAllChildNew(List<String> parentIds, List<ProjectCollectionToProject> listByParentIds, List<ProjectCollectionToProject> result) throws Exception;
}
