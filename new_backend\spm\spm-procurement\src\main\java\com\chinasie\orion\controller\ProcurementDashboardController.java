package com.chinasie.orion.controller;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.domain.dto.ProcurementDashboardDTO;
import com.chinasie.orion.domain.vo.ProcurementBarDashboardVO;
import com.chinasie.orion.domain.vo.ProcurementDashboardVO;
import com.chinasie.orion.service.ProcurementDashboardService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * ActualPayMilestone 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@RestController
@RequestMapping("/procurementDashboard")
@Api(tags = "采购供应商指标看板")
public class ProcurementDashboardController {

    @Autowired
    private ProcurementDashboardService procurementDashboardsService;

    /**
     * 查询看板块图数据
     *
     * @param procurementDashboard
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "查询看板块图数据")
    @RequestMapping(value = "/getLumpData", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询看板块图数据", type = "采购供应商指标看板", subType = "查询看板块图数据", bizNo = "{{#id}}")
    public ResponseDTO<List<ProcurementDashboardVO>> getLumpData(@RequestBody ProcurementDashboardDTO procurementDashboard) throws Exception {
        return new ResponseDTO<>(procurementDashboardsService.getLumpData(procurementDashboard));
    }

    /**
     * 查询柱状图数据
     *
     * @param procurementDashboard
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "查询柱状图数据")
    @RequestMapping(value = "/getColumnData", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询柱状图数据", type = "采购供应商指标看板", subType = "查询柱状图数据", bizNo = "{{#id}}")
    public ResponseDTO<List<ProcurementBarDashboardVO>> getColumnData(@RequestBody ProcurementDashboardDTO procurementDashboard) throws Exception {
        return new ResponseDTO<>(procurementDashboardsService.getColumnData(procurementDashboard));
    }
}
