package com.chinasie.orion.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.chinasie.orion.domain.entity.acceptance.AcceptanceFormItem;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;

import org.apache.ibatis.annotations.Mapper;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 验收单关联明细Repository.
 *
 * <AUTHOR>
 * @since 2023-05-03 19:45:01
 */
@Mapper
public interface AcceptanceFormItemRepository extends OrionBaseMapper<AcceptanceFormItem> {

}