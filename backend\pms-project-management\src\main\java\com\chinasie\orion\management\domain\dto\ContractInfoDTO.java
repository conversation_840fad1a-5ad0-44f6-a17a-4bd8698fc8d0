package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * ContractInfo DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@ApiModel(value = "ContractInfoDTO对象", description = "合同主表信息")
@Data
@ExcelIgnoreUnannotated
public class ContractInfoDTO extends ObjectDTO implements Serializable {

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 0)
    private String contractNumber;

    /**
     * 是否框架合同
     */
    @ApiModelProperty(value = "是否框架合同")
    @ExcelProperty(value = "是否框架合同 ", index = 1)
    private String isFream;

    /**
     * 采购申请号
     */
    @ApiModelProperty(value = "采购申请号")
    @ExcelProperty(value = "采购申请号 ", index = 2)
    private String purchaseApplicant;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称 ", index = 3)
    private String contractName;

    /**
     * 合同执行状态
     */
    @ApiModelProperty(value = "合同执行状态")
    @ExcelProperty(value = "合同执行状态 ", index = 4)
    private String statusName;

    /**
     * 框架合同剩余金额
     */
    @ApiModelProperty(value = "框架合同剩余金额")
    @ExcelProperty(value = "框架合同剩余金额 ", index = 5)
    private BigDecimal freamResidueAmount;

    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型")
    @ExcelProperty(value = "合同类型 ", index = 6)
    private String type;

    /**
     * 采购订单号
     */
    @ApiModelProperty(value = "采购订单号")
    @ExcelProperty(value = "采购订单号 ", index = 7)
    private String procurementOrderNumber;

    /**
     * 采购立项号
     */
    @ApiModelProperty(value = "采购立项号")
    @ExcelProperty(value = "采购立项号 ", index = 8)
    private String projectCode;

    /**
     * 采购立项金额
     */
    @ApiModelProperty(value = "采购立项金额")
    @ExcelProperty(value = "采购立项金额 ", index = 9)
    private BigDecimal procurementAmount;

    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商")
    @ExcelProperty(value = "供应商 ", index = 10)
    private String supplierName;

    /**
     * 工厂
     */
    @ApiModelProperty(value = "工厂")
    @ExcelProperty(value = "工厂 ", index = 11)
    private String factoryName;

    /**
     * 商务负责人
     */
    @ApiModelProperty(value = "商务负责人")
    @ExcelProperty(value = "商务负责人 ", index = 12)
    private String businessRspUser;

    /**
     * 变更金额
     */
    @ApiModelProperty(value = "变更金额")
    @ExcelProperty(value = "变更金额 ", index = 13)
    private BigDecimal changeMoney;

    /**
     * 变更比例
     */
    @ApiModelProperty(value = "变更比例")
    @ExcelProperty(value = "变更比例 ", index = 14)
    private String changePercent;

    /**
     * 支付金额
     */
    @ApiModelProperty(value = "支付金额")
    @ExcelProperty(value = "支付金额 ", index = 15)
    private BigDecimal payMoney;

    /**
     * 是否合同终止
     */
    @ApiModelProperty(value = "是否合同终止")
    @ExcelProperty(value = "是否合同终止 ", index = 16)
    private String isContractTerminate;

    /**
     * 索赔金额
     */
    @ApiModelProperty(value = "索赔金额")
    @ExcelProperty(value = "索赔金额 ", index = 17)
    private BigDecimal claimAmount;

    /**
     * 索赔比例
     */
    @ApiModelProperty(value = "索赔比例")
    @ExcelProperty(value = "索赔比例 ", index = 18)
    private String claimPercent;

    /**
     * 终止金额
     */
    @ApiModelProperty(value = "终止金额")
    @ExcelProperty(value = "终止金额 ", index = 19)
    private BigDecimal terminateAmount;

    /**
     * 终止比例
     */
    @ApiModelProperty(value = "终止比例")
    @ExcelProperty(value = "终止比例 ", index = 20)
    private String terminatePercent;

    /**
     * 框架开始时间
     */
    @ApiModelProperty(value = "框架开始时间")
    @ExcelProperty(value = "框架开始时间 ", index = 21)
    private Date freamBeginTime;

    /**
     * 框架结束时间
     */
    @ApiModelProperty(value = "框架结束时间")
    @ExcelProperty(value = "框架结束时间 ", index = 22)
    private Date freamEndTime;

    /**
     * 框架合同已使用金额
     */
    @ApiModelProperty(value = "框架合同已使用金额")
    @ExcelProperty(value = "框架合同已使用金额 ", index = 23)
    private BigDecimal freamUsedAmount;

    /**
     * 是否框架有效期内
     */
    @ApiModelProperty(value = "是否框架有效期内")
    @ExcelProperty(value = "是否框架有效期内 ", index = 24)
    private String isFreamPeriod;

    /**
     * 支付比例
     */
    @ApiModelProperty(value = "支付比例")
    @ExcelProperty(value = "支付比例 ", index = 25)
    private String payPercent;

    /**
     * 采购方式
     */
    @ApiModelProperty(value = "采购方式")
    @ExcelProperty(value = "采购方式 ", index = 26)
    private String procurementWay;

    /**
     * 采购周期
     */
    @ApiModelProperty(value = "采购周期")
    @ExcelProperty(value = "采购周期 ", index = 27)
    private String procurementCycle;

    /**
     * 节约金额
     */
    @ApiModelProperty(value = "节约金额")
    @ExcelProperty(value = "节约金额 ", index = 28)
    private BigDecimal amountSaved;

    /**
     * 商务活动类型
     */
    @ApiModelProperty(value = "商务活动类型")
    @ExcelProperty(value = "商务活动类型 ", index = 29)
    private String businessActivityType;

    /**
     * 最终采购方式
     */
    @ApiModelProperty(value = "最终采购方式")
    @ExcelProperty(value = "最终采购方式 ", index = 30)
    private String endProcurementWay;

    /**
     * 商务文件类型
     */
    @ApiModelProperty(value = "商务文件类型")
    @ExcelProperty(value = "商务文件类型 ", index = 31)
    private String businessFileType;

    /**
     * 预计合同开始日期
     */
    @ApiModelProperty(value = "预计合同开始日期")
    @ExcelProperty(value = "预计合同开始日期 ", index = 32)
    private Date estimatedStartTime;

    /**
     * 预计合同结束日期
     */
    @ApiModelProperty(value = "预计合同结束日期")
    @ExcelProperty(value = "预计合同结束日期 ", index = 33)
    private Date estimatedEndTime;

    /**
     * 付款方式
     */
    @ApiModelProperty(value = "付款方式")
    @ExcelProperty(value = "付款方式 ", index = 34)
    private String payWay;

    /**
     * 合同履约状态
     */
    @ApiModelProperty(value = "合同履约状态")
    @ExcelProperty(value = "合同履约状态 ", index = 35)
    private String executionStatusName;

    /**
     * 标的类别
     */
    @ApiModelProperty(value = "标的类别")
    @ExcelProperty(value = "标的类别 ", index = 36)
    private String objectType;

    /**
     * 类别占比（%）
     */
    @ApiModelProperty(value = "类别占比（%）")
    @ExcelProperty(value = "类别占比（%） ", index = 37)
    private String typePercent;

    /**
     * 审批价格（RMB）
     */
    @ApiModelProperty(value = "审批价格（RMB）")
    @ExcelProperty(value = "审批价格（RMB） ", index = 38)
    private BigDecimal approvedPrice;

    /**
     * 最终价格（原币）
     */
    @ApiModelProperty(value = "最终价格（原币）")
    @ExcelProperty(value = "最终价格（原币） ", index = 39)
    private BigDecimal finalPrice;

    /**
     * 实际合同开始日期
     */
    @ApiModelProperty(value = "实际合同开始日期")
    @ExcelProperty(value = "实际合同开始日期 ", index = 40)
    private Date actualStartTime;

    /**
     * 实际合同结束日期
     */
    @ApiModelProperty(value = "实际合同结束日期")
    @ExcelProperty(value = "实际合同结束日期 ", index = 41)
    private Date actualEndTime;

    /**
     * 预计合同交付日期
     */
    @ApiModelProperty(value = "预计合同交付日期")
    @ExcelProperty(value = "预计合同交付日期 ", index = 42)
    private Date estimatedDeliveryTime;

    /**
     * 验收结果
     */
    @ApiModelProperty(value = "验收结果")
    @ExcelProperty(value = "验收结果 ", index = 43)
    private String acceptanceResults;

    /**
     * 实际验收日期
     */
    @ApiModelProperty(value = "实际验收日期")
    @ExcelProperty(value = "实际验收日期 ", index = 44)
    private Date actualAcceptanceTimes;

    /**
     * 是否发布启动公示
     */
    @ApiModelProperty(value = "是否发布启动公示")
    @ExcelProperty(value = "是否发布启动公示 ", index = 45)
    private String isPublicLaunch;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 46)
    private String number;

    /**
     * 是否办理履约保证金
     */
    @ApiModelProperty(value = "是否办理履约保证金")
    @ExcelProperty(value = "是否办理履约保证金 ", index = 47)
    private String isPerformanceBond;

    /**
     * 保证金支付方式
     */
    @ApiModelProperty(value = "保证金支付方式")
    @ExcelProperty(value = "保证金支付方式 ", index = 48)
    private String marginPaymentMethod;

    /**
     * 保证金
     */
    @ApiModelProperty(value = "保证金")
    @ExcelProperty(value = "保证金 ", index = 49)
    private BigDecimal securityDeposit;

    /**
     * 是否参与计算
     */
    @ApiModelProperty(value = "是否参与计算	")
    @ExcelProperty(value = "是否参与计算	 ", index = 50)
    private String isCalculation;

    /**
     * 采购立项审批完成时间
     */
    @ApiModelProperty(value = "采购立项审批完成时间")
    @ExcelProperty(value = "采购立项审批完成时间 ", index = 51)
    private Date projectEndTime;

    /**
     * 合同推荐审批完成时间
     */
    @ApiModelProperty(value = "合同推荐审批完成时间")
    @ExcelProperty(value = "合同推荐审批完成时间 ", index = 52)
    private Date recommendEndTime;

    /**
     * 商务是否推荐供应商
     */
    @ApiModelProperty(value = "商务是否推荐供应商")
    @ExcelProperty(value = "商务是否推荐供应商 ", index = 53)
    private String isBizRecommend;

    /**
     * 价格模式
     */
    @ApiModelProperty(value = "价格模式")
    @ExcelProperty(value = "价格模式 ", index = 54)
    private String priceModel;

    /**
     * 所属部处
     */
    @ApiModelProperty(value = "所属部处")
    @ExcelProperty(value = "所属部处 ", index = 55)
    private String subdivision;

    /**
     * 是否填写一次验收合格
     */
    @ApiModelProperty(value = "是否填写一次验收合格")
    @ExcelProperty(value = "是否填写一次验收合格 ", index = 56)
    private String isFillOnetimeAcceptance;

    /**
     * 合同推荐审批完成时间开始
     */
    @ApiModelProperty(value = "合同推荐审批完成时间开始")
    @ExcelIgnore
    private String startDate;

    /**
     * 合同推荐审批完成时间结束
     */
    @ApiModelProperty(value = "合同推荐审批完成时间结束")
    @ExcelIgnore
    private String endDate;

    /**
     * 预警日期
     */
    @ApiModelProperty(value = "预警日期")
    @ExcelProperty(value = "预警日期 ", index = 57)
    private String warningDay;

    /**
     * 预警金额
     */
    @ApiModelProperty(value = "预警金额")
    @ExcelProperty(value = "预警金额 ", index = 58)
    private String warningMoney;

    /**
     * wbs编码
     */
    @ApiModelProperty(value = "wbs编码")
    @ExcelIgnore
    private String wbsId;

    @ApiModelProperty(value = "供应商来源")
    private String supplierFrom;


    /**
     * 是否非技术推荐供应商参与
     */
    @ApiModelProperty(value = "是否非技术推荐供应商参与")
    private String isTechSupplier;

    /**
     * 采购计划号
     */
    @ApiModelProperty(value = "采购计划号")
    private String purchasePlanCode;

    /**
     * 采购申请单编码
     */
    @ApiModelProperty(value = "采购申请单编码")
    private String purchaseRequestCode;

    @ApiModelProperty(value = "申请单名称")
    private String applicationName;

    @ApiModelProperty(value = "归口管理")
    private String bkManage;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "发送Sap时间")
    private Date sendSapTime;

    @ApiModelProperty(value = "高级搜索")
    List<List<SearchCondition>> searchConditions;

}
