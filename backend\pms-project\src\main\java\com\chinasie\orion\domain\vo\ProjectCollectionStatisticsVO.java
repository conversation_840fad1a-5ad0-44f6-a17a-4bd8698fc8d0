package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "ProjectCollectionStatisticsVO对象", description = "项目集统计表")
@Data
public class ProjectCollectionStatisticsVO extends ProjectCollectionVO {

    /**
     * 里程碑总数
     */
    @ApiModelProperty(value = "计划总数")
    private Integer totalCount =0;

    /**
     * 里程碑完成数
     */
    @ApiModelProperty(value = "计划完成数")
    private Integer finishedCount=0;

    @ApiModelProperty(value = "计划未完成数")
    private Integer noFinishedCount=0;

    @ApiModelProperty(value = "执行总金额")
    private BigDecimal executeSumMoney=BigDecimal.ZERO;
    @ApiModelProperty(value = "预算总金额")
    private BigDecimal budgetSumMoney=BigDecimal.ZERO;

    @ApiModelProperty(value = "预算执行率")
    private BigDecimal implementation=BigDecimal.ZERO;

    @ApiModelProperty(value = "项目进度")
    private String schedule;


    /**
     * 问题总数
     */
    @ApiModelProperty(value = "问题总数")
    private Integer questionTotalCount=0;

    /**
     * 问题完成数
     */
    @ApiModelProperty(value = "问题完成数")
    private Integer questionFinishedCount=0;
    @ApiModelProperty(value = "问题未完成数")
    private Integer questionNOFinishedCount=0;

    /**
     * 问题总数
     */
    @ApiModelProperty(value = "项目总数")
    private Integer projectTotalCount=0;

    /**
     * 问题完成数
     */
    @ApiModelProperty(value = "项目完成数")
    private Integer projectFinishedCount=0;

}
