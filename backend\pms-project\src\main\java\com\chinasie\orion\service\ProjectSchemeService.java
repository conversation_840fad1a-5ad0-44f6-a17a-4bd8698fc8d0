package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.dto.projectscheme.FallBackDTO;
import com.chinasie.orion.domain.dto.projectscheme.IssueDTO;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.domain.vo.performance.ProjectSchemePerformanceVO;
import com.chinasie.orion.domain.vo.projectscheme.SchemeOperateLogVO;
import com.chinasie.orion.domain.vo.quality.QualityItemVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.pas.api.domain.dto.EcrDTO;
import com.chinasie.orion.pas.api.domain.dto.EcrProjectSchemeAddDTO;
import com.chinasie.orion.pas.api.domain.vo.EcrVO;
import com.chinasie.orion.sdk.metadata.page.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * ProjectSchemeService
 *
 * @author: yangFy
 * @date: 2023/4/19 16:07
 * @description:
 * <p>
 * 项目计划
 * </p>
 */
public interface ProjectSchemeService extends OrionBaseService<ProjectScheme> {


    /**
     * 创建项目计划（批量）
     *
     * @param projectSchemeDTOS
     * @return
     */
    List<String> createBatch(String pid, List<ProjectSchemeDTO> projectSchemeDTOS) throws Exception;

    List<String> createTreeBatch(String projectId,String parentId, List<ProjectSchemeDTO> projectSchemeDTOS);


    /**
     * 编辑
     *
     * @param projectSchemeDTO
     * @return
     */
    ResponseVO edit(ProjectSchemeDTO projectSchemeDTO) throws Exception;

    /**
     * 删除(批量)
     *
     * @param ids
     * @return
     */
    Boolean deleteByIds(List<String> ids) throws Exception;

    /**
     * 详情
     *
     * @param id
     * @return
     */
    ProjectSchemeVO getDetail(String id, String pageCode) throws Exception;


    /**
     * 上移
     *
     * @param id
     * @return
     */
    Boolean up(String id) throws Exception;

    /**
     * 下移
     *
     * @param id
     * @return
     */
    Boolean down(String id) throws Exception;

    /**
     * 置顶
     *
     * @param id
     * @return
     */
    Boolean top(String id) throws Exception;


    /**
     * 计划下发
     *
     * @param issueDTO
     * @return
     */
    Boolean issue(IssueDTO issueDTO) throws Exception;
//
//    /**
//     * 计划重新下发
//     * @param issueDTO
//     * @return
//     */
//    Boolean issueAgain(IssueDTO issueDTO);

    /**
     * 取消置顶
     *
     * @param id
     * @return
     */
    Boolean unTop(String id) throws Exception;


    /**
     * 根据结束时间定时更新计划情况：正常、已临期、已逾期
     *
     * @return
     */
    Boolean refreshCircumstance();

    ProjectSchemeVO parentTree(String id);

    List<ProjectSchemeVO> tree(ProjectSchemeDTO schemeDTO) throws Exception;

    /**
     * 计划完成确认
     *
     * @param projectSchemeDTO
     * @return
     */
    Boolean finish(ProjectSchemeDTO projectSchemeDTO) throws Exception;


    /**
     * 查询所有待发布、已发布计划
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    List<ProjectSchemeVO> getSchemeList(String projectId) throws Exception;

    /**
     * 获取项目所有里程碑
     *
     * @param projectId
     * @return
     */
    List<ProjectSchemeVO> getAllMilestoneByProjectId(String projectId) throws Exception;

    /**
     * 用户验证是否是数据的负责人
     *
     * @param userId
     * @return
     */
    boolean verifyUser(String userId, String projectId) throws Exception;

    /**
     * 导入
     *
     * @param pid
     * @param projectSchemeDTOS 导入数据
     * @throws Exception 异常
     */
    void importByExcelVerify(String pid, List<ProjectSchemeDTO> projectSchemeDTOS) throws Exception;

    boolean finishBatch(List<String> projectSchemeIds) throws Exception;

    void dataHandle() throws Exception;

    /**
     * 计划补充超时原因
     *
     * @param projectSchemeDTO
     * @return
     */
    Boolean writeDelayReason(ProjectSchemeDTO projectSchemeDTO);

    /**
     * 计划催办
     *
     * @param urgePlanRequestDTO
     * @return
     */
    Boolean urgePlan(UrgePlanRequestDTO urgePlanRequestDTO);

    /**
     * 获取这个人的项目下的需要走流程的项目计划
     *
     * @param userCode
     * @param projectCode
     * @param pageRequest
     * @return
     */
    Page<ProjectSchemeVO> pageProjectSchemeByUserCode(String userCode, String projectCode, Page<ProjectSchemeDTO> pageRequest);

    /**
     * 第三方修改项目计划
     *
     * @param projectSchemeDTO
     * @return
     */
    Boolean updateProjectSchemeByThird(ProjectSchemeDTO projectSchemeDTO);


    /**
     * 新增关联需求
     *
     * <AUTHOR>
     * @date 2023/10/25 10:42
     */
    Boolean relationToDemand(FromIdsRelationToIdDTO fromIdsRelationToIdDTO);

    /**
     * 查询关联需求
     *
     * <AUTHOR>
     * @date 2023/10/25 12:22
     */
    List<DemandManagementVO> relationToDemandLists(String id, PlanQueryDTO planQueryDTO);

    /**
     * 删除关联需求
     *
     * <AUTHOR>
     * @date 2023/10/25 12:21
     */
    Boolean removeRelationToDemand(FromIdsRelationToIdDTO fromIdsRelationToIdDTO);


    /**
     * 新增关联问题
     *
     * <AUTHOR>
     * @date 2023/10/25 10:42
     */
    Boolean relationToQuestion(FromIdsRelationToIdDTO fromIdsRelationToIdDTO);

    /**
     * 删除关联问题
     *
     * <AUTHOR>
     * @date 2023/10/25 13:47
     */
    Boolean removeRelationToQuestion(FromIdsRelationToIdDTO fromIdsRelationToIdDTO);

    /**
     * 查询关联问题
     *
     * <AUTHOR>
     * @date 2023/10/25 13:47
     */
    List<QuestionManagementVO> relationToQuestionLists(String id, PlanQueryDTO planQueryDTO) throws Exception;

    /**
     * 新增关联风险
     *
     * <AUTHOR>
     * @date 2023/10/25 10:42
     */
    Boolean relationToRisk(FromIdsRelationToIdDTO fromIdsRelationToIdDTO);

    /**
     * 删除关联风险
     *
     * <AUTHOR>
     * @date 2023/10/25 13:48
     */
    Boolean removeRelationToRisk(FromIdsRelationToIdDTO fromIdsRelationToIdDTO);

    /**
     * 查询关联风险
     *
     * <AUTHOR>
     * @date 2023/10/25 13:48
     */
    List<RiskManagementVO> relationToRiskLists(String id, PlanQueryDTO planQueryDTO) throws Exception;

    /**
     * 获取计划列表
     * <AUTHOR>
     * @date 2023/10/27 09:39
     */
    PlanSearchDataVo searchList(KeywordDto keywordDto);

    /**
     * 获取计划列表
     * <AUTHOR>
     * @date 2023/10/27 10:25
     */
    List<PlanDetailVo> search(SearchDTO searchDTO) throws Exception;

    Page<ProjectSchemeVO> userPages(String type,Page<ProjectSchemeDTO> pageRequest) throws Exception;

    /**
     * 新增关联IED
     * @param id
     * @param deliverGoalsIdList
     * @return
     */
    Boolean relationToDeliverGoals(String id, List<String> deliverGoalsIdList);

    /**
     * 删除关联IED
     * @param id
     * @param deliverGoalsIdList
     * @return
     */
    Boolean removeRelationToDeliverGoals(String id, List<String> deliverGoalsIdList);

    /**
     * 查询关联IED
     * @param id
     * @return
     * @throws Exception
     */
    List<DeliverGoalsVO> relationToDeliverGoals(String id) throws Exception;

    /**
     * 工时根据id和时间段获取项目计划
     * @param userId 用户id
     * @param start 开始日期 yyyy-MM-dd
     * @param end 结束日期 yyyy-MM-dd
     * @return
     */
    List<ProjectSchemeVO> workHoursHainByRspUser(String userId, String start, String end);

    /**
     * 工时根据id集合获取项目计划
     * @param ids 计划id集合
     * @return
     */
    List<ProjectSchemeVO> getListByIds(List<String> ids);


    /**
     * 日报当前人员的获取项目计划
     * @param pageRequest
     * @return
     */
    Page<ProjectSchemeVO> getSchemeByUsers(Page<ProjectSchemeDailyStatementDTO> pageRequest);


    Page<ProjectSchemeVO> getSchemeByUser(String type,Page<ProjectSchemeDTO> pageRequest);

    ProjectSchemeVO getSchemeDetail(String id) throws Exception;

    /**
     * 更新实际开始时间
     * @param id
     * @return
     */
    Boolean updateActualBeginTime(String id);

    /**
     * 项目计划退回
     * @param fallBackDTO
     * @return
     */
    Boolean schemeFallback(FallBackDTO fallBackDTO);

    Page<ProjectSchemeVO> getPageByexamineUser(Page<ProjectSchemeDTO> pageRequest);

    /**
     * 根据用户ID获取某时间段包含的项目计划
     * @param userIds 用户ID集合
     * @param beginTime 开始时间 yyyy-MM-dd
     * @param endTime 结束时间 yyyy-MM-dd
     * @return
     */
    Map<String, List<ProjectScheme>> getSchemeByRspUsersForTime(List<String> userIds, Date beginTime, Date endTime);


    /**
     * 查询指定用户的项目计划
     * @param userId 用户ID
     * @param pageRequest
     * @return
     * @throws Exception
     */
    Page<ProjectSchemeVO> schemeByUserIdPages(String userId,Page<ProjectSchemeDTO> pageRequest) throws Exception;


    /**
     * 计划完成确认
     *
     * @param
     * @return
     */
    Boolean examineTypeFinish(String id) throws Exception;

    ProjectSchemeVO getBySchemeId(String id);

    /**
     * 通过用户id获取用户负责的项目计划分页（人员参与度报表用）
     * @param pageRequest
     * @return
     * @throws Exception
     */
    Page<ProjectSchemePerformanceVO> pageProjectSchemeByUserId(Page<ProjectSchemeReportDTO> pageRequest) throws Exception;

    /**
     * 导出通过用户id获取用户负责的项目计划（人员参与度报表用）
     * @param projectSchemeReportDTO
     * @param response
     * @throws Exception
     */
    void exportExcelByUserId(ProjectSchemeReportDTO projectSchemeReportDTO, HttpServletResponse response) throws Exception;

    /**
     * 获取项目所有里程碑分页
     * @param pageRequest
     * @return
     * @throws Exception
     */
    Page<ProjectSchemeVO> getMilestonePage(Page<ProjectSchemeDTO> pageRequest) throws Exception;


    /**
     * 根据计划ID查询日志
     *
     * <AUTHOR>
     * @date 2023/10/25 13:48
     */
    List<SchemeOperateLogVO> logListById(String id) throws Exception;

    /**
     * 获取项目下平铺计划分页数据
     * @param pageRequest
     * @return
     * @throws Exception
     */
    Page<ProjectSchemeVO> getSchemePage(Page<ProjectSchemeDTO> pageRequest) throws Exception;


    /**
     * 新增关联质控措施
     *
     * <AUTHOR>
     * @date 2023/10/25 10:42
     */
    Boolean relationToQualityItem(FromIdsRelationToIdDTO fromIdsRelationToIdDTO);

    /**
     * 删除关联质控措施
     *
     * <AUTHOR>
     * @date 2023/10/25 13:48
     */
    Boolean removeRelationToQualityItem(FromIdsRelationToIdDTO fromIdsRelationToIdDTO);

    /**
     * 查询关联质控措施
     *
     * <AUTHOR>
     * @date 2023/10/25 13:48
     */
    List<QualityItemVO> relationToQualityItem(String id, PlanQueryDTO planQueryDTO) throws Exception;

    /**
     * 计划撤回
     * @param id
     * @return
     */
    String revocation(String id);


    /**
     * 完成确认
     * @param projectSchemeDTO
     * @return
     * @throws Exception
     */
    Boolean completeConfirmation(ProjectSchemeDTO projectSchemeDTO) throws Exception;

    /**
     *暂停计划
     * @param projectSchemeDTO
     * @return
     * @throws Exception
     */
    String suspendScheme(ProjectSchemeDTO projectSchemeDTO) throws Exception;

    /**
     * 终止计划
     * @param projectSchemeDTO
     * @return
     * @throws Exception
     */
    String terminateScheme(ProjectSchemeDTO projectSchemeDTO) throws Exception;

    /**
     * 启动计划
     * @param projectSchemeDTO
     * @return
     * @throws Exception
     */
    String startScheme(ProjectSchemeDTO projectSchemeDTO) throws Exception;

    /**
     * 引入立项计划
     * @param projectId
     * @return
     */
    Boolean getApprovalScheme(String projectId);

    /**
     * 创建项目计划
     * @param projectSchemeDTO
     * @return
     */
    String createScheme(ProjectSchemeDTO projectSchemeDTO) throws Exception;
    /**
     * 通过项目计划获取反馈信息
     * @param id
     * @return
     * @throws Exception
     */
    List<ProjectSchemeContentVO> getProjectSchemeContentList(String id) throws Exception;

    /**
     * 通过前后置关系计算时间
     * @param projectSchemeTemplateEditDTO
     * @return
     * @throws Exception
     */
    List<ProjectSchemeTemplateEditVO> getTimeByPrePostRelation(ProjectSchemeTemplateEditDTO projectSchemeTemplateEditDTO) throws Exception;

    Page<ProjectSchemeVO> getPlanOrMilestone(Page<SearchDTO> pageRequest, String type);

    Boolean transfer(ProjectSchemeDTO projectSchemeDTO);

    String createEcrScheme(EcrProjectSchemeAddDTO ecrProjectSchemeAddDTO) throws Exception;

    Page<EcrVO>  getEcrSchemePage(Page<EcrDTO> page) throws Exception;

    /**
     * 拖拽
     * @param projectSchemeDTO
     * @return
     */
    boolean drag(ProjectSchemeDragDTO projectSchemeDTO);

    /**
     * 查询计划数据
     * @param projectSchemeId
     * @return
     */
    ProjectSchemeApiVO getBySchemeIdApi(String projectSchemeId);

    /**
     * 个人中心统计个数
     *
     * @param year
     * @param type
     * @return
     */
    ProjectCenterStatVO getProjectStat(String year, Integer type);

    /**
     * 个人总线统计头部
     * @param year
     * @param type
     * @return
     */
    ProjectCenterStatVO.PlanMilestoneData getProjectStatHead(String year, Integer type);

    /**
     *
     * @return
     */
    Object test();

    /**
     * 查询项目计划责任人部门
     * @param projectId
     * @return
     */
    List<SchemeVO> ganttDep(String projectId);

    /**
     * 查询项目计划责任人
     * @param projectId
     * @return
     */
    List<SchemeVO> ganttUser(String projectId);


    /**
     * 新增关联物资设备
     * @param fromIdsRelationToIdDTO
     * @return
     */
    Boolean relationToFixedAsset(FromIdsRelationToIdDTO fromIdsRelationToIdDTO);

    /**
     * 删除关联物资设备
     * @param fromIdsRelationToIdDTO
     * @return
     */
    Boolean removeRelationToFixedAsset(FromIdsRelationToIdDTO fromIdsRelationToIdDTO);

    /**
     * 查询关联物资设备列表
     * @param id
     * @param planQueryDTO
     * @return
     */
    List<FixedAssetsVO> relationToFixedAssetLists(String id, PlanQueryDTO planQueryDTO) throws Exception;


    /**
     * 新增关联采购行
     * @param fromIdsRelationToIdDTO
     * @return
     */
    Boolean relationToNcFormpurchase(FromIdsRelationToIdDTO fromIdsRelationToIdDTO);
    /**
     * 删除关联采购行
     * @param fromIdsRelationToIdDTO
     * @return
     */
    Boolean removeRelationToNcFormpurchase(FromIdsRelationToIdDTO fromIdsRelationToIdDTO);
    /**
     * 查询关联采购行列表
     * @param id
     * @param planQueryDTO
     * @return
     */
    List<NcfFormpurchaseRequestDetailAPIAsset> relationToNcFormpurchaseLists(String id, PlanQueryDTO planQueryDTO);

    /**
     * 只获取 id和状态
     * @return
     */
    List<ProjectScheme> getSimpleListByIds(ArrayList<String> strings);
}
