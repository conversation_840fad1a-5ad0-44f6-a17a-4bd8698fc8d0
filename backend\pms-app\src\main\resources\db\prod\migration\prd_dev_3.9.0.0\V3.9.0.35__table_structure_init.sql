DROP TABLE IF EXISTS `pmsx_req_clarification_record`;
CREATE TABLE `pmsx_req_clarification_record` (
`id` varchar(64) NOT NULL  COMMENT '主键',
`class_name` varchar(64)   COMMENT '创建人',
`creator_id` varchar(64) NOT NULL  COMMENT '创建人',
`modify_time` datetime NOT NULL  COMMENT '修改时间',
`owner_id` varchar(64)   COMMENT '拥有者',
`create_time` datetime NOT NULL  COMMENT '创建时间',
`modify_id` varchar(64) NOT NULL  COMMENT '修改人',
`remark` varchar(1024)   COMMENT '备注',
`platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
`org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
`status` int NOT NULL  COMMENT '状态',
`logic_status` int NOT NULL  COMMENT '逻辑删除字段',
`clarification_theme` varchar(64)   COMMENT '澄清主题',
`clarification_type` varchar(64)   COMMENT '澄清类型',
`clarification_lot` varchar(64)   COMMENT '澄清批次',
`clarification_context` varchar(4096)   COMMENT '澄清内容',
`is_update_time` varchar(64)   COMMENT '是否更新报价时间',
`requirement_id` varchar(64)   COMMENT '需求编号',
`release_time` datetime   COMMENT '发布时间',
`check_time` datetime   COMMENT '查看时间',
`check_status` char(64)   COMMENT '查看状态',
`origin_quote_start_time` datetime NOT NULL  COMMENT '原报价开始时间',
`origin_quote_dl_time` datetime   COMMENT '原报价截止时间',
`origin_quote_open_time` datetime   COMMENT '原开启报价时间',
`new_quote_open_time` datetime   COMMENT '新开启报价时间',
`new_quote_dl_time` datetime NOT NULL  COMMENT '新报价截止时间',
`new_quote_start_time` datetime   COMMENT '新报价开始时间',
PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='需求澄清记录';
