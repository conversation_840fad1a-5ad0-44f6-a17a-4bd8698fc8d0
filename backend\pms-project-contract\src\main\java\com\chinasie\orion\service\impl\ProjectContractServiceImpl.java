package com.chinasie.orion.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.api.code.SysCodeApi;
import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.constant.ContractDictConstant;
import com.chinasie.orion.constant.ProjectContractCategoryEnum;
import com.chinasie.orion.constant.ProjectContractPayNodeStatusEnum;
import com.chinasie.orion.constant.ProjectContractStatusEnum;
import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.*;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.DocumentService;
import com.chinasie.orion.service.ProjectContractService;
import com.chinasie.orion.service.ProjectService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.CollectionUtils;
import com.chinasie.orion.util.ResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * ProjectContract 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24 09:56:13
 */
@Service
public class ProjectContractServiceImpl extends OrionBaseServiceImpl<ProjectContractRepository, ProjectContract> implements ProjectContractService {

    @Autowired
    private ProjectContractRepository projectContractRepository;

    @Autowired
    private ContractPayNodeRepository contractPayNodeRepository;

    @Autowired
    private ContractOurSignedMainRepository contractOurSignedMainRepository;

    @Autowired
    private ContractSupplierSignedMainRepository contractSupplierSignedMainRepository;

    @Autowired
    private ProjectRepository projectRepository;


    @Autowired
    private DocumentService documentService;

    @Autowired
    private SysCodeApi sysCodeApi;


    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Resource
    private CurrentUserHelper currentUserHelper;
    @Autowired
    private ProjectService projectService;

    @Resource
    private DictBo dictBo;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public ProjectContractVO detail(String id) throws Exception {
        ProjectContract projectContract = projectContractRepository.selectById(id);
        ProjectContractVO result = BeanCopyUtils.convertTo(projectContract, ProjectContractVO::new);
        if (StringUtils.hasText(result.getCreatorId())) {
            UserVO createUser = userRedisHelper.getUserById(result.getCreatorId());
            result.setCreatorName(null == createUser ? "" : createUser.getName());
        }
        if (StringUtils.hasText(result.getCreatorId())) {
            SimpleUser createUser = userRedisHelper.getSimpleUserById(result.getCreatorId());
            result.setCreatorCode(createUser == null ? "" : createUser.getCode());
        }
        return result;
    }

    @Override
    public ProjectContractMainInfoVO mainInfo(String id) throws Exception {
        ProjectContractMainInfoVO projectContractMainInfoVO = new ProjectContractMainInfoVO();
        ProjectContract projectContract = projectContractRepository.selectById(id);
        ProjectContractVO projectContractVO = BeanCopyUtils.convertTo(projectContract, ProjectContractVO::new);

        if (StringUtils.hasText(projectContractVO.getRspDeptId())) {
            DeptVO dept = deptRedisHelper.getDeptById(projectContractVO.getRspDeptId());
            projectContractVO.setRspDeptName(null == dept ? "" : dept.getName());
        }
        if (StringUtils.hasText(projectContractVO.getPrincipalId())) {
            UserVO rspUser = userRedisHelper.getUserById(projectContractVO.getPrincipalId());
            projectContractVO.setPrincipalName(null == rspUser ? "" : rspUser.getName());
        }

        if (StringUtils.hasText(projectContractVO.getCreatorId())) {
            SimpleUser createUser = userRedisHelper.getSimpleUserById(projectContractVO.getCreatorId());
            projectContractVO.setCreatorCode(createUser == null ? "" : createUser.getCode());
        }

        if (StringUtils.hasText(projectContractVO.getCreatorId())) {
            UserVO createUser = userRedisHelper.getUserById(projectContractVO.getCreatorId());
            projectContractVO.setCreatorName(null == createUser ? "" : createUser.getName());
        }


        ContractOurSignedMain contractOurSignedMain = contractOurSignedMainRepository.selectOne(ContractOurSignedMain::getContractId, projectContract.getId());
        ContractOurSignedMainVO contractOurSignedMainVO = BeanCopyUtils.convertTo(contractOurSignedMain, ContractOurSignedMainVO::new);

        ContractSupplierSignedMain contractSupplierSignedMain = contractSupplierSignedMainRepository.selectOne(ContractSupplierSignedMain::getContractId, projectContract.getId());
        ContractSupplierSignedMainVO contractSupplierSignedMainVO = BeanCopyUtils.convertTo(contractSupplierSignedMain, ContractSupplierSignedMainVO::new);

        projectContractMainInfoVO.setProjectContractVO(projectContractVO);
        projectContractMainInfoVO.setContractOurSignedMainVO(contractOurSignedMainVO);
        projectContractMainInfoVO.setContractSupplierSignedMainVO(contractSupplierSignedMainVO);
        return projectContractMainInfoVO;
    }

    @Override
    public ProjectContractAllInfoVO allInfo(String id) throws Exception {
        ProjectContractAllInfoVO projectContractAllInfoVO = new ProjectContractAllInfoVO();
        ProjectContract projectContract = projectContractRepository.selectById(id);
        ProjectContractVO projectContractVO = BeanCopyUtils.convertTo(projectContract, ProjectContractVO::new);
        if (StringUtils.hasText(projectContractVO.getRspDeptId())) {
            DeptVO dept = deptRedisHelper.getDeptById(projectContractVO.getRspDeptId());
            projectContractVO.setRspDeptName(null == dept ? "" : dept.getName());
        }
        if (StringUtils.hasText(projectContractVO.getPrincipalId())) {
            UserVO rspUser = userRedisHelper.getUserById(projectContractVO.getPrincipalId());
            projectContractVO.setPrincipalName(null == rspUser ? "" : rspUser.getName());
            projectContractVO.setPrincipalCode(null == rspUser ? "" : rspUser.getCode());
        }

        if (StringUtils.hasText(projectContractVO.getCreatorId())) {
            SimpleUser createUser = userRedisHelper.getSimpleUserById(projectContractVO.getCreatorId());
            projectContractVO.setCreatorCode(createUser == null ? "" : createUser.getCode());
        }

        if (StringUtils.hasText(projectContractVO.getCreatorId())) {
            UserVO createUser = userRedisHelper.getUserById(projectContractVO.getCreatorId());
            projectContractVO.setCreatorName(null == createUser ? "" : createUser.getName());
        }


        ContractOurSignedMain contractOurSignedMain = contractOurSignedMainRepository.selectOne(ContractOurSignedMain::getContractId, projectContract.getId());
        ContractOurSignedMainVO contractOurSignedMainVO = BeanCopyUtils.convertTo(contractOurSignedMain, ContractOurSignedMainVO::new);

        ContractSupplierSignedMain contractSupplierSignedMain = contractSupplierSignedMainRepository.selectOne(ContractSupplierSignedMain::getContractId, projectContract.getId());
        ContractSupplierSignedMainVO contractSupplierSignedMainVO = BeanCopyUtils.convertTo(contractSupplierSignedMain, ContractSupplierSignedMainVO::new);

        List<ContractPayNode> contractPayNodeList = contractPayNodeRepository.selectList(ContractPayNode::getContractId, projectContract.getId());
        List<ContractPayNodeVO> contractPayNodeVOList = BeanCopyUtils.convertListTo(contractPayNodeList, ContractPayNodeVO::new);

        List<DocumentVO> documentVOList = documentService.getDocumentList(projectContract.getId(), null);

        projectContractAllInfoVO.setContractPayNodeVOList(contractPayNodeVOList);
        projectContractAllInfoVO.setDocumentVOList(documentVOList);
        projectContractAllInfoVO.setProjectContractVO(projectContractVO);
        projectContractAllInfoVO.setContractOurSignedMainVO(contractOurSignedMainVO);
        projectContractAllInfoVO.setContractSupplierSignedMainVO(contractSupplierSignedMainVO);
        return projectContractAllInfoVO;
    }

    /**
     * 新增
     * <p>
     * * @param projectContractDTO
     */
    @Override
    @Transactional
    public ProjectContractAllInfoVO create(ProjectContractAllInfoDTO projectContractAllInfoDTO) throws Exception {
        ProjectContractAllInfoVO projectContractAllInfoVO = new ProjectContractAllInfoVO();
        ProjectContractDTO projectContractDTO = projectContractAllInfoDTO.getProjectContractDTO();
        BigDecimal contractMoney = projectContractDTO.getContractMoney();
        List<ContractPayNodeDTO> contractPayNodeDTOList = projectContractAllInfoDTO.getContractPayNodeDTOList();
        BigDecimal totalInitPlanPayAmt = new BigDecimal(0);
        if (!CollectionUtils.isBlank(contractPayNodeDTOList)) {
            contractPayNodeDTOList.forEach(p -> {
                if (p.getInitPlanPayAmt() != null) {
                    BigDecimal payPercentage = p.getPayPercentage();
                    BigDecimal payPercentage2 = p.getInitPlanPayAmt().divide(contractMoney, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
                    if (payPercentage.compareTo(payPercentage2) != 0) {
                        throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, payPercentage + "支付百分比计算不正确!");
                    }
                    totalInitPlanPayAmt.add(p.getInitPlanPayAmt());
                } else {
                    BigDecimal payPercentage = p.getPayPercentage();
                    if (payPercentage != null || payPercentage.compareTo(new BigDecimal(0)) != 0) {
                        throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, payPercentage + "支付百分比计算不正确!");
                    }
                }
            });
        }
        if (totalInitPlanPayAmt.compareTo(contractMoney) > 0) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "支付节点信息的总金额不能大于合同总金额!");
        }

        if (!StringUtils.hasText(projectContractDTO.getProjectNumber())) {
            if (!StringUtils.hasText(projectContractDTO.getProjectId())) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "项目id不能为空!");
            }
            Project project = projectRepository.selectById(projectContractDTO.getProjectId());
            if (project == null) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "项目不存在!");
            }
            projectContractDTO.setProjectNumber(project.getNumber());
        }
        ProjectContract projectContract = BeanCopyUtils.convertTo(projectContractDTO, ProjectContract::new);
        String contractCategory = projectContract.getContractCategory();
        String dataType = "";
        if (ProjectContractCategoryEnum.SALE.getCode().equals(contractCategory)) {
            dataType = "ProjectContractSale";
        } else if (ProjectContractCategoryEnum.PURCHASE.getCode().equals(contractCategory)) {
            dataType = "ProjectContractPurchase";
        } else {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "合同类别不存在!");
        }
        ResponseDTO<String> responseDTO = sysCodeApi.rulesAndSegmentCreate(dataType, "number", false, "");
        if (ResponseUtils.fail(responseDTO)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, responseDTO.getMessage());
        }
        String number = responseDTO.getResult();
        if (!StringUtils.hasText(number)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "生成项目合同编号失败");
        }
        //合同基本信息
        projectContract.setNumber(number);
        if (!projectContract.getIsGuaranteeMoney()) {
            projectContract.setGuaranteeAmt(null);
        }
        if (!projectContract.getIsGuaranteePeriod()) {
            projectContract.setGuaranteeEndDate(null);
        }
        projectContractRepository.insert(projectContract);
        ProjectContractVO projectContractVO = BeanCopyUtils.convertTo(projectContract, ProjectContractVO::new);
        projectContractAllInfoVO.setProjectContractVO(projectContractVO);

        //甲方签约主体
        ContractOurSignedMainDTO contractOurSignedMainDTO = projectContractAllInfoDTO.getContractOurSignedMainDTO();
        contractOurSignedMainDTO.setContractId(projectContractVO.getId());
        contractOurSignedMainDTO.setContractNumber(projectContractVO.getNumber());
        ContractOurSignedMain contractOurSignedMain = BeanCopyUtils.convertTo(contractOurSignedMainDTO, ContractOurSignedMain::new);
        contractOurSignedMainRepository.insert(contractOurSignedMain);
        ContractOurSignedMainVO contractOurSignedMainVO = BeanCopyUtils.convertTo(contractOurSignedMain, ContractOurSignedMainVO::new);
        projectContractAllInfoVO.setContractOurSignedMainVO(contractOurSignedMainVO);

        //乙方签约主体
        ContractSupplierSignedMainDTO contractSupplierSignedMainDTO = projectContractAllInfoDTO.getContractSupplierSignedMainDTO();
        contractSupplierSignedMainDTO.setContractId(projectContractVO.getId());
        contractSupplierSignedMainDTO.setContractNumber(projectContractVO.getNumber());
        ContractSupplierSignedMain contractSupplierSignedMain = BeanCopyUtils.convertTo(contractSupplierSignedMainDTO, ContractSupplierSignedMain::new);
        contractSupplierSignedMainRepository.insert(contractSupplierSignedMain);
        ContractSupplierSignedMainVO contractSupplierSignedMainVO = BeanCopyUtils.convertTo(contractSupplierSignedMain, ContractSupplierSignedMainVO::new);
        projectContractAllInfoVO.setContractSupplierSignedMainVO(contractSupplierSignedMainVO);

        //合同支付节点信息

        if (!CollectionUtils.isBlank(contractPayNodeDTOList)) {
            List<ContractPayNode> contractPayNodeList = BeanCopyUtils.convertListTo(contractPayNodeDTOList, ContractPayNode::new);
            contractPayNodeList.forEach(p -> {
                p.setContractId(projectContractVO.getId());
                p.setContractNumber(projectContractVO.getNumber());
            });
            contractPayNodeRepository.insertBatch(contractPayNodeList);
            List<ContractPayNodeVO> contractPayNodeVOList = BeanCopyUtils.convertListTo(contractPayNodeList, ContractPayNodeVO::new);
            projectContractAllInfoVO.setContractPayNodeVOList(contractPayNodeVOList);
        }

        //合同附件
        List<FileInfoDTO> fileInfoDTOList = projectContractAllInfoDTO.getFileInfoDTOList();
        if (!CollectionUtils.isBlank(fileInfoDTOList)) {
            fileInfoDTOList.forEach(p -> {
                p.setDataId(projectContractVO.getId());
            });
            List<String> fileDataIdList = documentService.saveBatchAdd(fileInfoDTOList);
        }
        return projectContractAllInfoVO;
    }

    /**
     * 编辑
     * <p>
     * * @param projectContractDTO
     */
    @Override
    @Transactional
    public Boolean edit(ProjectContractAllInfoDTO projectContractAllInfoDTO) throws Exception {

        //合同基本信息
        ProjectContractDTO projectContractDTO = projectContractAllInfoDTO.getProjectContractDTO();


        BigDecimal contractMoney = projectContractDTO.getContractMoney();
        List<ContractPayNodeDTO> contractPayNodeDTOList = projectContractAllInfoDTO.getContractPayNodeDTOList();
        BigDecimal totalInitPlanPayAmt = new BigDecimal(0);
        if (!CollectionUtils.isBlank(contractPayNodeDTOList)) {
            contractPayNodeDTOList.forEach(p -> {
                if (p.getInitPlanPayAmt() != null) {
                    BigDecimal payPercentage = p.getPayPercentage();
                    BigDecimal payPercentage2 = p.getInitPlanPayAmt().divide(contractMoney, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
                    if (payPercentage.compareTo(payPercentage2) != 0) {
                        throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, payPercentage + "支付百分比计算不正确!");
                    }
                    totalInitPlanPayAmt.add(p.getInitPlanPayAmt());
                } else {
                    BigDecimal payPercentage = p.getPayPercentage();
                    if (payPercentage != null || payPercentage.compareTo(new BigDecimal(0)) != 0) {
                        throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, payPercentage + "支付百分比计算不正确!");
                    }
                }
            });
        }
        if (totalInitPlanPayAmt.compareTo(contractMoney) > 0) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "支付节点信息的总金额不能大于合同总金额!");
        }


        ProjectContract projectContract1 = projectContractRepository.selectById(projectContractDTO.getId());
        if (projectContract1 == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "合同不存在!");
        }
        Integer status = projectContract1.getStatus();
        if (!ProjectContractStatusEnum.CREATED.getStatus().equals(status)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "当前合同状态不能修改!");
        }

        if (StringUtils.hasText(projectContractDTO.getProjectId()) && !StringUtils.hasText(projectContractDTO.getProjectNumber())) {
            Project project = projectRepository.selectById(projectContractDTO.getProjectId());
            if (project == null) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "项目不存在!");
            }
            projectContractDTO.setProjectNumber(project.getNumber());
        }

        String oldContractCategory = projectContract1.getContractCategory();
        String newContractCategory = projectContractDTO.getContractCategory();
        if (!oldContractCategory.equals(newContractCategory)) {
            String dataType = "";
            if (ProjectContractCategoryEnum.SALE.getCode().equals(newContractCategory)) {
                dataType = "ProjectContractSale";
            } else if (ProjectContractCategoryEnum.PURCHASE.getCode().equals(newContractCategory)) {
                dataType = "ProjectContractPurchase";
            } else {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "合同类别不存在!");
            }
            ResponseDTO<String> responseDTO = sysCodeApi.rulesAndSegmentCreate(dataType, "number", false, "");
            if (ResponseUtils.fail(responseDTO)) {
                throw new PMSException(PMSErrorCode.PMS_ERR, responseDTO.getMessage());
            }
            String number = responseDTO.getResult();
            if (!StringUtils.hasText(number)) {
                throw new PMSException(PMSErrorCode.PMS_ERR, "生成项目合同编号失败");
            }
            projectContractDTO.setNumber(number);
        }


        ProjectContract projectContract = BeanCopyUtils.convertTo(projectContractDTO, ProjectContract::new);

        if (!projectContract.getIsGuaranteeMoney()) {
            projectContract.setGuaranteeAmt(null);
        }
        if (!projectContract.getIsGuaranteePeriod()) {
            projectContract.setGuaranteeEndDate(null);
        }
        int update = projectContractRepository.updateById(projectContract);
        if (!StringUtils.hasText(projectContract.getNumber())) {
            projectContract = projectContractRepository.selectById(projectContract.getId());
        }
        String number = projectContract.getNumber();
        //甲方签约主体
        ContractOurSignedMainDTO contractOurSignedMainDTO = projectContractAllInfoDTO.getContractOurSignedMainDTO();
        if (contractOurSignedMainDTO == null || !StringUtils.hasText(contractOurSignedMainDTO.getId())) {
            if (contractOurSignedMainDTO != null) {
                ContractOurSignedMain contractOurSignedMain = BeanCopyUtils.convertTo(contractOurSignedMainDTO, ContractOurSignedMain::new);
                ContractOurSignedMain contractOurSignedMain1 = contractOurSignedMainRepository.selectOne(ContractOurSignedMain::getContractId, projectContract.getId());
                if (contractOurSignedMain1 == null) {
                    contractOurSignedMain.setContractId(projectContract.getId());
                    contractOurSignedMain.setContractNumber(projectContract.getNumber());
                    contractOurSignedMainRepository.insert(contractOurSignedMain);
                } else {
                    contractOurSignedMain.setId(contractOurSignedMain1.getId());
                    contractOurSignedMainRepository.updateById(contractOurSignedMain);
                }
            }
        } else {
            ContractOurSignedMain contractOurSignedMain = BeanCopyUtils.convertTo(contractOurSignedMainDTO, ContractOurSignedMain::new);
            contractOurSignedMainRepository.updateById(contractOurSignedMain);
        }

        //乙方签约主体
        ContractSupplierSignedMainDTO contractSupplierSignedMainDTO = projectContractAllInfoDTO.getContractSupplierSignedMainDTO();
        if (contractSupplierSignedMainDTO == null || !StringUtils.hasText(contractSupplierSignedMainDTO.getId())) {
            if (contractSupplierSignedMainDTO != null) {
                ContractSupplierSignedMain contractSupplierSignedMain = BeanCopyUtils.convertTo(contractSupplierSignedMainDTO, ContractSupplierSignedMain::new);
                ContractSupplierSignedMain contractSupplierSignedMain1 = contractSupplierSignedMainRepository.selectOne(ContractSupplierSignedMain::getContractId, projectContract.getId());
                if (contractSupplierSignedMain1 == null) {
                    contractSupplierSignedMain.setContractId(projectContract.getId());
                    contractSupplierSignedMain.setContractNumber(projectContract.getNumber());
                    contractSupplierSignedMainRepository.insert(contractSupplierSignedMain);
                } else {
                    contractSupplierSignedMain.setId(contractSupplierSignedMain1.getId());
                    contractSupplierSignedMainRepository.updateById(contractSupplierSignedMain);
                }
            }
        } else {
            ContractSupplierSignedMain contractSupplierSignedMain = BeanCopyUtils.convertTo(contractSupplierSignedMainDTO, ContractSupplierSignedMain::new);
            contractSupplierSignedMainRepository.updateById(contractSupplierSignedMain);
        }

        //合同支付节点信息
        List<ContractPayNode> contractPayNodeList = BeanCopyUtils.convertListTo(contractPayNodeDTOList, ContractPayNode::new);
        List<ContractPayNode> contractPayNodeList1 = contractPayNodeRepository.selectList(ContractPayNode::getContractId, projectContract.getId());
        if (!(CollectionUtils.isBlank(contractPayNodeList) && CollectionUtils.isBlank(contractPayNodeList1))) {
            if (CollectionUtils.isBlank(contractPayNodeList)) {
                contractPayNodeRepository.deleteBatchIds(contractPayNodeList1.stream().map(ContractPayNode::getId).collect(Collectors.toList()));
            } else if (CollectionUtils.isBlank(contractPayNodeList1)) {
                contractPayNodeList.forEach(p -> {
                    p.setContractId(projectContractDTO.getId());
                    p.setContractNumber(number);
                });
                contractPayNodeRepository.insertBatch(contractPayNodeList);
            } else {
                List<ContractPayNode> updateContractPayNode = contractPayNodeList.stream().filter(p -> StringUtils.hasText(p.getId())).collect(Collectors.toList());
                List<ContractPayNode> insertContractPayNode = contractPayNodeList.stream().filter(p -> !StringUtils.hasText(p.getId())).collect(Collectors.toList());
                List<String> contractPayNodeIdList = updateContractPayNode.stream().map(ContractPayNode::getId).collect(Collectors.toList());
                List<String> deleteContractPayNodeIds = contractPayNodeList1.stream().filter(p -> !contractPayNodeIdList.contains(p.getId())).map(ContractPayNode::getId).collect(Collectors.toList());
                if (!CollectionUtils.isBlank(updateContractPayNode)) {
                    updateContractPayNode.forEach(p -> {
                        p.setContractId(projectContractDTO.getId());
                        p.setContractNumber(number);
                    });
                    contractPayNodeRepository.updateBatch(updateContractPayNode, updateContractPayNode.size());
                }

                if (!CollectionUtils.isBlank(deleteContractPayNodeIds)) {
                    contractPayNodeRepository.deleteBatchIds(deleteContractPayNodeIds);
                }

                if (!CollectionUtils.isBlank(insertContractPayNode)) {
                    insertContractPayNode.forEach(p -> {
                        p.setContractId(projectContractDTO.getId());
                        p.setContractNumber(number);
                    });
                    contractPayNodeRepository.insertBatch(insertContractPayNode);
                }
            }

        }

        //合同附件
        List<FileInfoDTO> fileInfoDTOList = projectContractAllInfoDTO.getFileInfoDTOList();
        List<FileInfoDTO> fileInfoDTOList1 = documentService.getFileInfoList(projectContractDTO.getId());
        if (!(CollectionUtils.isBlank(fileInfoDTOList) && CollectionUtils.isBlank(fileInfoDTOList1))) {
            if (CollectionUtils.isBlank(fileInfoDTOList)) {
                documentService.deleteBatchFile(fileInfoDTOList1.stream().map(FileInfoDTO::getId).collect(Collectors.toList()), projectContractDTO.getId());
            } else if (CollectionUtils.isBlank(fileInfoDTOList1)) {
                fileInfoDTOList.forEach(p -> {
                    p.setDataId(projectContractDTO.getId());
                });
                documentService.saveBatchAdd(fileInfoDTOList);
            } else {
                List<FileInfoDTO> updateContractFile = fileInfoDTOList.stream().filter(p -> StringUtils.hasText(p.getId())).collect(Collectors.toList());
                List<FileInfoDTO> insertContractFile = fileInfoDTOList.stream().filter(p -> !StringUtils.hasText(p.getId())).collect(Collectors.toList());
                List<String> contractFileIdList = updateContractFile.stream().map(FileInfoDTO::getId).collect(Collectors.toList());
                List<String> deleteContractFileIds = fileInfoDTOList1.stream().filter(p -> !contractFileIdList.contains(p.getId())).map(FileInfoDTO::getId).collect(Collectors.toList());
                if (!CollectionUtils.isBlank(updateContractFile)) {
                    updateContractFile.forEach(p -> {
                        p.setDataId(projectContractDTO.getId());
                    });
                    List<FileDTO> fileDtoList = BeanCopyUtils.convertListTo(updateContractFile, FileDTO::new);
                    documentService.updateBatchDocument(fileDtoList);
                }

                if (!CollectionUtils.isBlank(deleteContractFileIds)) {
                    documentService.deleteBatchFile(deleteContractFileIds, projectContractDTO.getId());
                }

                if (!CollectionUtils.isBlank(insertContractFile)) {
                    insertContractFile.forEach(p -> {
                        p.setDataId(projectContractDTO.getId());
                    });
                    documentService.saveBatchAdd(insertContractFile);
                }
            }

        }
        return SqlHelper.retBool(update);
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        List<ProjectContract> projectContractList = projectContractRepository.selectList(ProjectContract::getId, ids);
        if (CollectionUtils.isBlank(projectContractList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "合同不存在!");
        }
        if (projectContractList.stream().filter(item -> !item.getStatus().equals(ProjectContractStatusEnum.CREATED.getStatus())).findAny().isPresent()) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "合同状态不能删除!");
        }

        int delete = projectContractRepository.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public Page<ProjectContractVO> pages(Page<ProjectContractDTO> pageRequest) throws Exception {
        Page<ProjectContract> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectContract::new));
        realPageRequest.setSearchConditions(pageRequest.getSearchConditions());
        LambdaQueryWrapperX<ProjectContract> lambdaQueryWrapper = new LambdaQueryWrapperX<>();
        ProjectContract query = realPageRequest.getQuery();
        if (query != null) {
            if (StringUtils.hasText(query.getProjectId())) {
                lambdaQueryWrapper.eq(ProjectContract::getProjectId, query.getProjectId());
            }
            if (StringUtils.hasText(query.getContractCategory())) {
                lambdaQueryWrapper.eq(ProjectContract::getContractCategory, query.getContractCategory());
            }
        }


        lambdaQueryWrapper.orderByDesc(ProjectContract::getCreateTime);
        lambdaQueryWrapper = SearchConditionUtils.parseSearchConditionsWrapper(realPageRequest.getSearchConditions(), lambdaQueryWrapper);
        PageResult<ProjectContract> page = projectContractRepository.selectPage(realPageRequest, lambdaQueryWrapper);

        Page<ProjectContractVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectContractVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectContractVO::new);
        Map<String, String> contractCategoryValueToDesMap = dictBo.getDictValueToDesMap(ContractDictConstant.CONTRACT_CATEGORY);
        Map<String, String> contractTypeValueToDesMap = dictBo.getDictValueToDesMap(ContractDictConstant.CONTRACT_TYPE);

        if (!CollectionUtils.isBlank(vos)) {
            List<String> contractIds = vos.stream().map(ProjectContractVO::getId).collect(Collectors.toList());
            List<ContractOurSignedMain> contractOurSignedMainList = contractOurSignedMainRepository.selectList(ContractOurSignedMain::getContractId, contractIds);
            Map<String, String> contractOurSignedMainMap = contractOurSignedMainList.stream().collect(Collectors.toMap(ContractOurSignedMain::getContractId, ContractOurSignedMain::getSignedMainName));
            List<ContractSupplierSignedMain> contractSupplierSignedMainList = contractSupplierSignedMainRepository.selectList(ContractSupplierSignedMain::getContractId, contractIds);
            Map<String, String> contractSupplierSignedMainMap = contractSupplierSignedMainList.stream().collect(Collectors.toMap(ContractSupplierSignedMain::getContractId, ContractSupplierSignedMain::getSignedMainName));
            LambdaQueryWrapper<ContractPayNode> contractPayNodeLambdaQueryWrapper = new LambdaQueryWrapper<>();
            contractPayNodeLambdaQueryWrapper.in(ContractPayNode::getContractId, contractIds);
            contractPayNodeLambdaQueryWrapper.eq(ContractPayNode::getStatus, ProjectContractPayNodeStatusEnum.COMPLETE.getStatus());
            List<ContractPayNode> contractPayNodeList = contractPayNodeRepository.selectList(contractPayNodeLambdaQueryWrapper);
            Map<String, BigDecimal> contractPayNodeMap = new HashMap<>();
            if (!CollectionUtils.isBlank(contractPayNodeList)) {
                contractPayNodeMap = contractPayNodeList.stream().collect(Collectors.groupingBy(ContractPayNode::getContractId
                        , Collectors.mapping(ContractPayNode::getInitPlanPayAmt, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
            }
            Map<String, BigDecimal> contractPayNodeMap1 = contractPayNodeMap;

            List<String> principalIdList = vos.stream().map(ProjectContractVO::getPrincipalId).collect(Collectors.toList());
            List<UserVO> userVOList = userRedisHelper.getUserByIds(principalIdList);
            Map<String, String> userVoMap = userVOList.stream().collect(Collectors.toMap(UserVO::getId, UserVO::getName));


            vos.forEach(p -> {
                p.setContractCategoryName(contractCategoryValueToDesMap.get(p.getContractCategory()));
                p.setContractTypeName(contractTypeValueToDesMap.get(p.getContractType()));
                if (ProjectContractCategoryEnum.SALE.getCode().equals(p.getContractCategory())) {
                    p.setSignedMainName(contractOurSignedMainMap.get(p.getId()));
                } else if (ProjectContractCategoryEnum.PURCHASE.getCode().equals(p.getContractCategory())) {
                    p.setSignedMainName(contractSupplierSignedMainMap.get(p.getId()));
                }
                BigDecimal payAmt = contractPayNodeMap1.get(p.getId()) == null ? new BigDecimal(0) : contractPayNodeMap1.get(p.getId());
                BigDecimal payPercentage = payAmt.divide(p.getContractMoney(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
                p.setPayPercentage(payPercentage);
                p.setPayAmt(payAmt);
                p.setPrincipalName(userVoMap.get(p.getPrincipalId()));
            });
        }

        pageResult.setContent(vos);

        return pageResult;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public Page<ProjectContractVO> userPage(Page<ProjectContractDTO> pageRequest) throws Exception {
        Page<ProjectContract> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectContract::new));
        realPageRequest.setSearchConditions(pageRequest.getSearchConditions());
        LambdaQueryWrapperX<ProjectContract> lambdaQueryWrapper = new LambdaQueryWrapperX<>();
        ProjectContract query = realPageRequest.getQuery();

        String userId = currentUserHelper.getUserId();
        lambdaQueryWrapper.eq(ProjectContract::getCreatorId, userId);
        lambdaQueryWrapper.orderByDesc(ProjectContract::getCreateTime);
        lambdaQueryWrapper = SearchConditionUtils.parseSearchConditionsWrapper(realPageRequest.getSearchConditions(), lambdaQueryWrapper);
        PageResult<ProjectContract> page = projectContractRepository.selectPage(realPageRequest, lambdaQueryWrapper);

        Page<ProjectContractVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectContractVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectContractVO::new);
        Map<String, String> contractCategoryValueToDesMap = dictBo.getDictValueToDesMap(ContractDictConstant.CONTRACT_CATEGORY);
        Map<String, String> contractTypeValueToDesMap = dictBo.getDictValueToDesMap(ContractDictConstant.CONTRACT_TYPE);

        if (!CollectionUtils.isBlank(vos)) {
            List<String> contractIds = vos.stream().map(ProjectContractVO::getId).collect(Collectors.toList());
            List<ContractOurSignedMain> contractOurSignedMainList = contractOurSignedMainRepository.selectList(ContractOurSignedMain::getContractId, contractIds);
            Map<String, String> contractOurSignedMainMap = contractOurSignedMainList.stream().collect(Collectors.toMap(ContractOurSignedMain::getContractId, ContractOurSignedMain::getSignedMainName));
            List<ContractSupplierSignedMain> contractSupplierSignedMainList = contractSupplierSignedMainRepository.selectList(ContractSupplierSignedMain::getContractId, contractIds);
            Map<String, String> contractSupplierSignedMainMap = contractSupplierSignedMainList.stream().collect(Collectors.toMap(ContractSupplierSignedMain::getContractId, ContractSupplierSignedMain::getSignedMainName));
            LambdaQueryWrapper<ContractPayNode> contractPayNodeLambdaQueryWrapper = new LambdaQueryWrapper<>();
            contractPayNodeLambdaQueryWrapper.in(ContractPayNode::getContractId, contractIds);
            contractPayNodeLambdaQueryWrapper.eq(ContractPayNode::getStatus, ProjectContractPayNodeStatusEnum.COMPLETE.getStatus());
            List<ContractPayNode> contractPayNodeList = contractPayNodeRepository.selectList(contractPayNodeLambdaQueryWrapper);
            Map<String, BigDecimal> contractPayNodeMap = new HashMap<>();
            if (!CollectionUtils.isBlank(contractPayNodeList)) {
                contractPayNodeMap = contractPayNodeList.stream().collect(Collectors.groupingBy(ContractPayNode::getContractId
                        , Collectors.mapping(ContractPayNode::getInitPlanPayAmt, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
            }
            Map<String, BigDecimal> contractPayNodeMap1 = contractPayNodeMap;

            List<String> principalIdList = vos.stream().map(ProjectContractVO::getPrincipalId).collect(Collectors.toList());
            List<UserVO> userVOList = userRedisHelper.getUserByIds(principalIdList);
            Map<String, String> userVoMap = userVOList.stream().collect(Collectors.toMap(UserVO::getId, UserVO::getName));
            List<String> projectIds = vos.stream().map(ProjectContractVO::getProjectId).collect(Collectors.toList());
            List<Project> projects = projectService.listByIds(projectIds);
            Map<String, String> projectNameMap = projects.stream().collect(Collectors.toMap(Project::getId, Project::getName));

            vos.forEach(p -> {
                p.setContractCategoryName(contractCategoryValueToDesMap.get(p.getContractCategory()));
                p.setContractTypeName(contractTypeValueToDesMap.get(p.getContractType()));
                if (ProjectContractCategoryEnum.SALE.getCode().equals(p.getContractCategory())) {
                    p.setSignedMainName(contractOurSignedMainMap.get(p.getId()));
                } else if (ProjectContractCategoryEnum.PURCHASE.getCode().equals(p.getContractCategory())) {
                    p.setSignedMainName(contractSupplierSignedMainMap.get(p.getId()));
                }
                BigDecimal payAmt = contractPayNodeMap1.get(p.getId()) == null ? new BigDecimal(0) : contractPayNodeMap1.get(p.getId());
                BigDecimal payPercentage = payAmt.divide(p.getContractMoney(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
                p.setPayPercentage(payPercentage);
                p.setPayAmt(payAmt);
                p.setProjectName(projectNameMap.get(p.getProjectId()));
                p.setPrincipalName(userVoMap.get(p.getPrincipalId()));
            });
        }

        pageResult.setContent(vos);

        return pageResult;
    }
}
