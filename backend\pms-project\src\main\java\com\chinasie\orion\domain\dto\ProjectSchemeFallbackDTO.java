package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * SchemeFallback DTO对象
 *
 * <AUTHOR>
 * @since 2024-04-16 13:50:11
 */
@ApiModel(value = "ProjectSchemeFallbackDTO对象", description = "项目计划回退")
@Data
@ExcelIgnoreUnannotated
public class ProjectSchemeFallbackDTO extends ObjectDTO implements Serializable{

/**
 * 项目计划id
 */
@ApiModelProperty(value = "项目计划id")
@ExcelProperty(value = "项目计划id ", index = 0)
private String projectSchemeId;

/**
 * 退回原因
 */
@ApiModelProperty(value = "退回原因")
@ExcelProperty(value = "退回原因 ", index = 1)
private String reason;




}
