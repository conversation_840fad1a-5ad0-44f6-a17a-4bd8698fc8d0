<script setup lang="ts">
import {
  FormTable,
  type ITableFormSchemeItem,
  SubFormSchemeTypeEnum,
  BasicButton,
} from 'lyra-component-vue3';
import { computed, h, ref } from 'vue';
import { DatePicker } from 'ant-design-vue';
import dayjs from 'dayjs';

const schemes:ITableFormSchemeItem[] = [
  {

    field: 'name',
    label: '名称',
    type: SubFormSchemeTypeEnum.Input,
    required: true,
  },

  {
    field: 'newBeginTime',
    label: '新计划开始时间',
    type: SubFormSchemeTypeEnum.DatePicker,

    required: true,
    render(params) {
      return h(DatePicker, {
        value: computed(() => (params.record.newBeginTime ? dayjs(params.record.newBeginTime) : null)),
        disabledDate: (date) => (
          params.record.newEndTime && dayjs(date).isAfter(dayjs(params.record.newEndTime))
        ),
        onChange(val) {
          if (val) {
            params.record.newBeginTime = dayjs(val).format('YYYY-MM-DD');
          }
        },
        allowClear: false,
        placeholder: '请选择',
        style: {
          width: '100%',
        },
      });
    },
  },
  {
    field: 'newEndTime',
    label: '新计划结束时间',
    type: SubFormSchemeTypeEnum.DatePicker,
    required: true,
    render(params) {
      return h(DatePicker, {
        value: computed(() => (params.record.newEndTime ? dayjs(params.record.newEndTime) : null)),
        disabledDate: (date) => (
          params.record.newBeginTime && dayjs(date).isBefore(dayjs(params.record.newBeginTime))
        ),
        onChange(val) {
          if (val) {
            params.record.newEndTime = dayjs(val).format('YYYY-MM-DD');
          }
        },
        allowClear: false,
        placeholder: '请选择',
        style: {
          width: '100%',
        },
      });
    },
    // props: {
    //   style: {
    //     width: '100%',
    //   },
    //   disabledDate: (current) => {
    //     let beginTime = '2024-06-15';
    //     if (beginTime) {
    //       let startTime = dayjs(beginTime).format('YYYY-MM-DD');
    //       let endTime = dayjs(current).format('YYYY-MM-DD');
    //       return !(Date.parse(endTime) >= Date.parse(startTime));
    //     }
    //     return false;
    //   },
    // },
  },
  {
    field: 'rspUserName',
    label: '新责任人',
    type: SubFormSchemeTypeEnum.InputSelectUser,
    required: true,
    props: {
      selectUserModalProps: {
        selectType: 'checkbox',
      },
    },
  },

];

function api() {
  // return new Promise((resolve, reject) => {
  //   setTimeout(() => {
  //     resolve([
  //       {
  //         id: 'id1',
  //         name: '张三',
  //         age: 16,
  //       },
  //     ]);
  //   }, 1000);
  // });
}

function batchDeleteApi(records: any[]) {
  // return new Promise((resolve, reject) => {
  //   setTimeout(() => {
  //     resolve();
  //   }, 1000);
  // });
}

function deleteApi(record: any) {

}

const formTableRef = ref();

function setData() {
  formTableRef.value.setData([
    {
      id: 'id2',
      name: '李四',
      age: 28,
    },
    {
      id: 'id3',
      name: '王五',
      age: 30,
    },
  ]);
}

function reload() {
  formTableRef.value.reload();
}
</script>

<template>
  <FormTable
    ref="formTableRef"
    :schemes="schemes"
    :canResize="false"
    :api="api"
    :batchDeleteApi="batchDeleteApi"
    :deleteApi="deleteApi"
  />
</template>

copied