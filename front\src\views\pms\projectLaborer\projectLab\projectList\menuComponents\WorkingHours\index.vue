<template>
  <div class="working-hours">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template
        #toolbarLeft
      >
        <BasicButton
          type="primary"
          icon="add"
          @click="addEstimate
          "
        >
          新增预估
        </BasicButton>

        <BasicButton
          type="primary"
          @click="handleSubmit"
        >
          提交
        </BasicButton>
        <BasicButton
          icon="delete"
          @click="multiDelete"
        >
          删除
        </BasicButton>
      </template>
    </OrionTable>

    <AddEstimate @register="registerModal" />
    <ViewDrawer @register="registerDrawer" />
    <EditDrawer @register="registerEditDrawer" />
  </div>
</template>
<script lang="ts" setup>
import {
  h, inject, onMounted, reactive, Ref, ref, nextTick, provide,
} from 'vue';
import {
  BasicButton, DataStatusTag, OrionTable, useDrawer, useModal,
} from 'lyra-component-vue3';
import { message, Modal } from 'ant-design-vue';
import dayjs from 'dayjs';
import { useRoute } from 'vue-router';
import AddEstimate from './component/addEstimate.vue';
import ViewDrawer from './component/viewDrawer.vue';
import EditDrawer from './component/editDrawer.vue';
import Api from '/@/api';
import { useUserStore } from '/@/store/modules/user';

const state = reactive({
  formId: '',
});
const tableRef = ref();
const [registerModal, { openModal }] = useModal();
const [registerDrawer, { openDrawer }] = useDrawer();
const [registerEditDrawer, { openDrawer: openEditDrawer }] = useDrawer();
const userStore = useUserStore();
const route = useRoute();
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: {
    // selectedRowKeys: computed(() => state.selectedRowKeys),
  },
  // filterConfig: { fields: tableFilterConfig },
  smallSearchField: ['number', 'memberName'],
  showIndexColumn: true,
  api: (params) => new Api('/pms').fetch({

    ...params,
    query: {
      projectId,
    },
  }, 'workHourEstimate/page', 'POST'),
  columns: [
    {
      title: '单据编号',
      dataIndex: 'number',
      align: 'left',
    },
    {
      title: '成员姓名',
      align: 'left',
      dataIndex: 'memberName',
    },
    {
      title: '成员角色',
      align: 'left',
      dataIndex: 'memberRoleName',
    },

    {
      title: '开始时间',
      align: 'left',
      dataIndex: 'startDate',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },

    {
      title: '结束时间',
      align: 'left',
      dataIndex: 'endDate',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '工时时长（小时）',
      align: 'left',
      dataIndex: 'workHour',
    },
    {
      title: '单据状态',
      dataIndex: 'dataStatus',
      align: 'left',
      width: 100,
      customRender({ record }) {
        return h(DataStatusTag, {
          statusData: record.dataStatus,
        });
      },
    },

    {
      title: '创建时间',
      align: 'left',
      dataIndex: 'createTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 150,
      align: 'left',
      fixed: 'right',
      slots: { customRender: 'action' },
    },
  ],

  actions: [

    {
      text: '查看',
      // isShow: (record) => record.controlsType === 'details',
      onClick(record) {
        openDrawer(true, {
          ...record,
          type: 'details',
        });
        record.controlsType = 'details';
      },
    },
    {
      text: '编辑',
      // isShow: (record) => record.controlsType !== 'edit' && isPower('WLGL_container_button_15', powerData),
      onClick(record) {
        openEditDrawer(true, {
          ...record,
          projectInfo: basicInfo.value,
          type: 'edit',
        });
      },
    },
    {
      text: '删除',
      onClick(record) {
        Modal.confirm({
          title: '是否删除当前单据，删除后可重新添加?',
          onOk() {
            deleteRows([record.id]);
          },
        });
      },

    },

  ],
});
const projectId: string = route.query.id;
const basicInfo: Ref<{
  [propName: string]: any
}> = ref({});
async function getBasicInfo() {
  const result = await new Api(`/pms/project/detail/${projectId}`).fetch('', '', 'GET');
  basicInfo.value = result || {};
}
// 新增工时预估弹窗
function addEstimate() {
  openModal(true, basicInfo.value);
}
onMounted(() => {
  getBasicInfo();
});

// 更新表格
function updateTable() {
  nextTick(() => {
    tableRef.value.reload();
  });
}

// 提交
function handleSubmit() {
  if (tableRef.value.getSelectRows().length > 0) {
    new Api('/pms').fetch(tableRef.value.getSelectRows().map((item) => item.id), 'workHourEstimate/batchSubmit', 'PUT').then((res) => {
      message.success('提交成功');
      tableRef.value.reload();
    }).catch((err) => {
    });
  } else {
    message.warning('请选择工时提交！');
  }
}

function multiDelete() {
  if (tableRef.value.getSelectRowKeys().length === 0) {
    return message.error('请选择一条数据');
  }

  Modal.confirm({
    title: '删除提示',
    content: '是否删除当前单据，删除后可重新添加？',
    onOk: () => deleteRows(tableRef.value.getSelectRows().map((item) => item.id)),
  });
}
function deleteRows(ids) {
  new Api('/pms').fetch(ids, 'workHourEstimate', 'DELETE').then((res) => {
    message.success('删除数据成功');
    tableRef.value.reload();
  }).catch((err) => {
  });
}
provide('updateNodePages', updateTable);
</script>
