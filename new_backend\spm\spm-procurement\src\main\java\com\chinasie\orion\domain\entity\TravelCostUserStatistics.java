package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "TravelCostStatistics对象", description = "差旅费用统计")
@Data
public class TravelCostUserStatistics {

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    @TableField(value = "user_name")
    private String userName;

    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    @TableField(value = "user_code")
    private String userCode;
    /**
     * 差旅时长
     */
    @ApiModelProperty(value = "差旅时长")
    @TableField(value = "travelDays")
    private Integer travelDays;


    /**
     * 中心名称
     */
    @ApiModelProperty(value = "中心名称")
    @TableField(value = "org_name")
    private String orgName;

    /**
     * 住宿费
     */
    @ApiModelProperty(value = "住宿费")
    @TableField(value = "hotel_amount")
    private BigDecimal hotelAmount;

    /**
     * 换乘费
     */
    @ApiModelProperty(value = "换乘费")
    @TableField(value = "transfer_amount")
    private BigDecimal transferAmount;

    /**
     * 交通费
     */
    @ApiModelProperty(value = "交通费")
    @TableField(value = "traffic_amount")
    private BigDecimal trafficAmount;

}
