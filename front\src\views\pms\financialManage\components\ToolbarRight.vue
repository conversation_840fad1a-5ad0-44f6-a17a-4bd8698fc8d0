<template>
  <div>
    <ARadioGroup
      :value="mode"
      class="select-btn"
      @change="handleModeChange"
    >
      <ARadioButton value="compilation">
        编制版
      </ARadioButton>
      <ARadioButton value="adjustment">
        调整版
      </ARadioButton>
      <ARadioButton value="diff">
        差异版
      </ARadioButton>
    </ARadioGroup>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';
import { Radio } from 'ant-design-vue';

const ARadioGroup = Radio.Group;
const ARadioButton = Radio.Button;

export default defineComponent({
  components: {
    ARadioGroup,
    ARadioButton,
  },
  props: {
    mode: {
      type: String,
      required: true,
    },
  },
  emits: ['update:mode', 'handleModeChange'],
  methods: {
    handleModeChange(event: Event) {
      const value = (event.target as any).value;
      this.$emit('update:mode', value);
      this.$emit('handleModeChange', event);
    },
  },
});
</script>

<style scoped lang="less">
.select-btn {
  position: absolute;
  right: 115px;
  .ant-radio-button-wrapper:last-child {
    color: #f5222d;
  }
}
</style>
