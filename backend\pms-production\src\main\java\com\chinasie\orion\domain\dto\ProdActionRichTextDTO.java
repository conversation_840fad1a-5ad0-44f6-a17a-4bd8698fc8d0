package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProdActionRishText DTO对象
 *
 * <AUTHOR>
 * @since 2024-10-24 10:33:48
 */
@ApiModel(value = "ProdActionRishTextDTO对象", description = "生产大修行动项富文本")
@Data
@ExcelIgnoreUnannotated
public class ProdActionRichTextDTO extends  ObjectDTO   implements Serializable{

    /**
     * 行动项ID
     */
    @ApiModelProperty(value = "行动项ID")
    @ExcelProperty(value = "行动项ID ", index = 0)
    private String actionId;

    /**
     * 数据类型
     */
    @ApiModelProperty(value = "数据类型")
    @ExcelProperty(value = "数据类型 ", index = 1)
    private String dataType;

    /**
     * 富文本
     */
    @ApiModelProperty(value = "富文本")
    @ExcelProperty(value = "富文本 ", index = 2)
    private String richText;




}
