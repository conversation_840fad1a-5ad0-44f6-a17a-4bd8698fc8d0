package com.chinasie.orion.management.controller;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.dto.ContractLineInfoDTO;
import com.chinasie.orion.management.domain.vo.ContractLineInfoVO;
import com.chinasie.orion.management.service.ContractLineInfoService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * ContractLineInfo 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@RestController
@RequestMapping("/contractLineInfo")
@Api(tags = "合同行项目信息")
public class ContractLineInfoController {

    @Autowired
    private ContractLineInfoService contractLineInfoService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "合同行项目信息", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ContractLineInfoVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        ContractLineInfoVO rsp = contractLineInfoService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param contractLineInfoDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#contractLineInfoDTO.name}}】", type = "合同行项目信息", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody ContractLineInfoDTO contractLineInfoDTO) throws Exception {
        String rsp = contractLineInfoService.create(contractLineInfoDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param contractLineInfoDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#contractLineInfoDTO.name}}】", type = "合同行项目信息", subType = "编辑", bizNo = "{{#contractLineInfoDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody ContractLineInfoDTO contractLineInfoDTO) throws Exception {
        Boolean rsp = contractLineInfoService.edit(contractLineInfoDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "合同行项目信息", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = contractLineInfoService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "合同行项目信息", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = contractLineInfoService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "合同行项目信息", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page/{mainTableId}", method = RequestMethod.POST)
    public ResponseDTO<Page<ContractLineInfoVO>> pages(@PathVariable("mainTableId") String mainTableId, @RequestBody Page<ContractLineInfoDTO> pageRequest) throws Exception {
        Page<ContractLineInfoVO> rsp = contractLineInfoService.pages(mainTableId, pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 根据合同编号查询合同行列表信息
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "根据合同编号查询合同行列表信息")
    @LogRecord(success = "【{USER{#logUserId}}】根据合同编号查询合同行列表信息", type = "合同行项目信息", subType = "根据合同编号查询合同行列表信息", bizNo = "")
    @RequestMapping(value = "/getByCode", method = RequestMethod.POST)
    public ResponseDTO<Page<ContractLineInfoVO>> getByCode(@RequestBody Page<ContractLineInfoDTO> pageRequest) throws Exception {
        Page<ContractLineInfoVO> rsp = contractLineInfoService.getByCode(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("合同行项目信息导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "合同行项目信息", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        contractLineInfoService.downloadExcelTpl(response);
    }

    @ApiOperation("合同行项目信息导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "合同行项目信息", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = contractLineInfoService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("合同行项目信息导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "合同行项目信息", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = contractLineInfoService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消合同行项目信息导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "合同行项目信息", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = contractLineInfoService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("合同行项目信息导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "合同行项目信息", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        contractLineInfoService.exportByExcel(searchConditions, response);
    }
}
