package com.chinasie.orion.management.service;


import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.dto.NcfFormpurchaseRequestDTO;
import com.chinasie.orion.management.domain.dto.NcfPurchProjectImplementationDTO;
import com.chinasie.orion.management.domain.entity.NcfPurchProjectImplementation;
import com.chinasie.orion.management.domain.vo.NcfPurchProjectImplementationVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * <p>
 * NcfPurchProjectImplementation 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12 10:24:16
 */
public interface NcfPurchProjectImplementationService extends OrionBaseService<NcfPurchProjectImplementation> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    NcfPurchProjectImplementationVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param ncfPurchProjectImplementationDTO
     */
    String create(NcfPurchProjectImplementationDTO ncfPurchProjectImplementationDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param ncfPurchProjectImplementationDTO
     */
    Boolean edit(NcfPurchProjectImplementationDTO ncfPurchProjectImplementationDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<NcfPurchProjectImplementationVO> pages(Page<NcfPurchProjectImplementationDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(Page<NcfPurchProjectImplementationDTO> pageRequest, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<NcfPurchProjectImplementationVO> vos) throws Exception;

    Map<String, Object> getNumMoney(Page<NcfPurchProjectImplementationDTO> pageRequest) throws Exception;

    /**
     * 获取采购项目实施数据
     * <p>
     * * @param searchConditions
     * * @param response
     */
    List<NcfPurchProjectImplementation> getImplementationList(LocalDate start, LocalDate end);
}
