<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.JobMaterialMapper">



    <select id="getMaterialNumMap" resultType="com.chinasie.orion.domain.dto.CountJobDTO">
        select  material_id as `key` ,count(material_id) as  `value`
        from  pmsx_job_material
        <where>
            logic_status = 1
            <if test=" null != materialIdList and  materialIdList.size()>0">
                and  material_id in
                <foreach collection="materialIdList" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        group by material_id
    </select>


    <select id="getMaterialJobInfoList" resultType="com.chinasie.orion.domain.dto.source.MaterialSourceDTO">
        select p.id as relationId ,j.id as jobId,p.number as materialCode,p.asset_name as materialName
             ,j.repair_round as repairRound,j.`name` as jobName,p.material_id  as materialId
             ,p.plan_begin_date as taskBeginDate,p.plan_end_date as taskEndDate
             ,j.begin_time as jobBeginDate,j.end_time as jobEndDate,p.create_time
        from  pmsx_job_material p
                  JOIN pmsx_job_manage j on p.job_id = j.id and j.logic_status=1
                  JOIN pmsx_material_manage m  on p.material_id=m.id and m.logic_status=1
        where j.repair_round =#{repairRound} and p.logic_status =1
        <if test="null != keyword and keyword != ''">
            <!-- ! or j.name like concat('%',#{keyword},'%') -->
            and (p.asset_name like concat('%',#{keyword},'%') or p.number like concat('%',#{keyword},'%') )
        </if>
        <if test="null != year and year != ''">
            and  <![CDATA[YEAR(j.begin_time) <= #{year}]]> and <![CDATA[YEAR(j.end_time) >= #{year}]]>
        </if>
    </select>


    <select id="materiallapSourceList" resultType="com.chinasie.orion.domain.dto.source.MaterialSourceDTO">
        select p.id as relationId ,j.id as jobId,p.number as materialCode,p.asset_name as materialName
        ,j.repair_round as repairRound,j.`name` as jobName,j.job_base as baseCode,p.material_id  as materialId
        ,p.plan_begin_date as taskBeginDate,p.plan_end_date as taskEndDate
        ,j.begin_time as jobBeginDate,j.end_time as jobEndDate,p.create_time
        from  pmsx_job_material p
        JOIN pmsx_job_manage j on p.job_id = j.id and j.logic_status=1
        JOIN pmsx_material_manage m  on p.material_id=m.id and m.logic_status=1
        where   p.logic_status =1
        <if test="null != keyword and keyword != ''">
            and (p.asset_name like concat('%',#{keyword},'%') or p.number like concat('%',#{keyword},'%'))
        </if>
        <if test="null != year and year != ''">
            and  <![CDATA[YEAR(j.begin_time) <= #{year}]]> and <![CDATA[YEAR(j.end_time) >= #{year}]]>
        </if>
        <if test=" null != materialNumberList and  materialNumberList.size()>0">
            and p.number in
            <foreach collection="materialNumberList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>
