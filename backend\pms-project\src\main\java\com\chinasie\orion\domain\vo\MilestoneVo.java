package com.chinasie.orion.domain.vo;

import com.chinasie.orion.domain.dto.ObjectDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/22/15:51
 * @description:
 */
@Data
public class MilestoneVo  extends ObjectDTO {
    @ApiModelProperty("创建人名字")
    private String creatorName;
    @ApiModelProperty("拥有者名字")
    private String ownerName;
    @ApiModelProperty("修改人名字")
    private String modifyName;

    @ApiModelProperty(value = "计划结束时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date planEndTime;
    @ApiModelProperty(value = "计划结束时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date planPredictEndTime;
    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "负责人ID")
    private String principalId;

    @ApiModelProperty(value = "负责人名称")
    private String principalName;

    @ApiModelProperty(value = "内部状态名称")
    private String statusName;
    @ApiModelProperty(value = "内部状态id")
    private String taskStatusId;
}
