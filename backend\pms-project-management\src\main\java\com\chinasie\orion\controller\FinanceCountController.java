package com.chinasie.orion.controller;


import com.chinasie.orion.domain.dto.InvoicingRevenueAccountingDTO;
import com.chinasie.orion.domain.vo.FinanceCountVO;
import com.chinasie.orion.domain.vo.InvoicingRevenueAccountingVO;
import com.chinasie.orion.domain.vo.PageResultVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.FinanceCountService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/finance-count")
@Api(tags = "财务统计-合并合同项目详情四个接口")
public class FinanceCountController {

    @Autowired
    private FinanceCountService financeCountService;


    @GetMapping("/getCount")
    @ApiOperation(value = "财务统计-合并合同项目详情四个接口")
    @LogRecord(success = "【{USER{#logUserId}}】查看【合同详情】【{{#contractId}}】-【财务统计】统计【{{#id}}】", type = "超额", subType = "详情", bizNo = "{{#contractId}}")
    public ResponseDTO<FinanceCountVO> getCount(@RequestParam String contractId)
    {
        return new ResponseDTO<>(financeCountService.getCount(contractId));
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "财务相关-分页合并（因接口过多分页查询合并）")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看财务相关-分页合并（因接口过多分页查询合并）", type = "开票收入核算信息表", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<PageResultVO> pages(@RequestBody Page<InvoicingRevenueAccountingDTO> pageRequest) throws Exception {
        return new ResponseDTO<>(financeCountService.getPageResult(pageRequest));
    }


}
