package com.chinasie.orion.msc;

import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.constant.RequirementNodeDict;
import com.chinasie.orion.management.domain.dto.RequirementMangementDTO;
import com.chinasie.orion.msc.api.MscBuildHandler;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
    public class RequirementDistributeFeedBackHandler implements MscBuildHandler<RequirementMangementDTO> {
    @Override
    public SendMessageDTO buildMsc(RequirementMangementDTO requirementMangementDTO, Object... objects) {
        Map<String,Object> messageMap = new HashMap<>();
        messageMap.put("$name$",requirementMangementDTO.getRequirementName());

        List<String> distinct = new ArrayList<>();
        Object object = objects[0];
        String toUser = object.toString();
        distinct.add(toUser);
        SendMessageDTO sendMessageDTO = SendMessageDTO.builder()
                .messageMap(messageMap)
                .titleMap(messageMap)
                .businessId(requirementMangementDTO.getId())
                .messageUrl("/pas/MarketDemandManagementDetails/" + requirementMangementDTO.getId())
                .messageUrlName("需求分发反馈")
                .senderTime(new Date())
                .senderId(CurrentUserHelper.getCurrentUserId())
                .recipientIdList(distinct)
                .platformId(requirementMangementDTO.getPlatformId())
                .orgId(requirementMangementDTO.getOrgId())
                .build();
        return sendMessageDTO;
    }

    @Override
    public String support() {
        return RequirementNodeDict.NODE_REQUIREMENT_FEEDBACK;
    }
}
