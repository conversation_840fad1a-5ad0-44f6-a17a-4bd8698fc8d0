<template>
  <div class="MonthBox">
    <div class="monthBoxItem">
      <div class="weekNumber">
        {{ 44 }}w
      </div>
      <div class="boxContent">
        <Popover
          trigger="hover"
          placement="right"
        >
          <template #content>
            <p class="clickItem">
              <span>Content</span>
            </p>
            <p class="clickItem">
              <span>Content</span>
            </p>
          </template>
          <div class="mb30">
            <span class="mr20">已完成</span>
            <span>22</span>
          </div>
        </Popover>
        <Popover
          trigger="hover"
          placement="right"
        >
          <template #content>
            <p class="clickItem">
              <span>Content</span>
            </p>
            <p class="clickItem">
              <span>Content</span>
            </p>
          </template>
          <div>
            <span class="mr20">未完成</span>
            <span>22</span>
          </div>
        </Popover>
      </div>
    </div>
    <div class="monthBoxItem">
      <div class="weekNumber">
        {{ 44 }}w
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, PropType } from 'vue';
import { Button, Popover } from 'ant-design-vue';
// import { PlusOutlined } from '@ant-design/icons-vue';
const AButton = Button;
const emits = defineEmits<{
    (e: 'update:title', hah: string): void;
}>();
const props = defineProps({
  trigger: {
    type: [Array] as PropType<('contextmenu' | 'click' | 'hover')[]>,
    default: () => ['contextmenu'],
  },
  selectedKeys: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
});

const state = reactive({
  one: 666666,
});
</script>

<style scoped lang="less">
.MonthBox {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-content: flex-start;

  .monthBoxItem {
    width: 49%;
    height: 300px;
    margin-bottom: 1%;
    border-radius: 3px;
    box-sizing: border-box;
    border: 1px solid #ccc;
    padding: 40px;
    position: relative;

    .weekNumber {
      position: absolute;
      width: 30px;
      height: 30px;
      text-align: center;
      line-height: 30px;
      left: 5px;
      top: 5px;
    }

    .boxContent {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translateX(-50%) translateY(-50%);

      .clickItem {
        &:hover {
          background-color: ~`getPrefixVar('primary-color')`;
        }

        &:hover span {
          background-color: ~`getPrefixVar('primary-color')`;
        }
      }
    }

  }
}
</style>
