package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ApprovalTaskPrePostDTO;
import com.chinasie.orion.domain.dto.PreSchemeDTO;
import com.chinasie.orion.domain.dto.PreTaskDTO;
import com.chinasie.orion.domain.dto.ProjectSchemePrePostDTO;
import com.chinasie.orion.service.ApprovalTaskPrePostService;
import com.chinasie.orion.service.ProjectSchemePrePostService;
import com.mzt.logapi.starter.annotation.LogRecords;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * ApprovalTaskPrePost 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 15:28:14
 */
@RestController
@RequestMapping("/approvalTaskPrePost")
@Api(tags = "项目立项任务前后置关系表")
public class  ApprovalTaskPrePostController  {

    @Autowired
    private ApprovalTaskPrePostService approvalTaskPrePostService;


    @ApiOperation("添加前后置关系(批量)")
    @PostMapping(value = "/createBatch")
    @LogRecord(success = "【{USER{#logUserId}}】添加前后置关系】", type = "项目立项任务前后置关系表", subType = "添加前后置关系", bizNo = "")
    public ResponseDTO<List<String>> createBatch(@RequestBody PreTaskDTO preTaskDTO) throws Exception {
        return ResponseDTO.success(approvalTaskPrePostService.createBatch(preTaskDTO));
    }

    @ApiOperation("删除(批量)")
    @DeleteMapping(value = "")
    @LogRecord(success = "【{USER{#logUserId}}】删除】", type = "项目立项任务前后置关系表", subType = "删除", bizNo = "{{#ids}}")
    public ResponseDTO<Boolean> deleteByIds(@RequestBody List<String> ids) throws Exception {
        return ResponseDTO.success(approvalTaskPrePostService.deleteByIds(ids));
    }


    @ApiOperation("变更前后置任务(批量)")
    @PutMapping(value = "/modify/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】变更前后置任务】", type = "项目立项任务前后置关系表", subType = "变更前后置任务", bizNo = "")
    public ResponseDTO<Boolean> modify(@PathVariable("id") String id, List<ApprovalTaskPrePostDTO> prePostDTOS) throws Exception {
        return ResponseDTO.success(approvalTaskPrePostService.modify(id, prePostDTOS));
    }
}
