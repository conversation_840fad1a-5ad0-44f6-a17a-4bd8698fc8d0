package com.chinasie.orion.domain.dto.quality;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import java.io.Serializable;

import java.lang.String;
import java.util.List;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * QualityItemScheme DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-08 14:13:47
 */
@ApiModel(value = "QualityItemSchemeDTO对象", description = "质量管控项和计划关联关系")
@Data
@ExcelIgnoreUnannotated
public class QualityItemSchemeDTO extends ObjectDTO implements Serializable {

    /**
     * 管控项id
     */
    @ApiModelProperty(value = "管控项id")
    @ExcelProperty(value = "管控项id ", index = 0)
    private String qualityItemId;

    /**
     * 计划id
     */
    @ApiModelProperty(value = "计划id")
    @ExcelProperty(value = "计划id ", index = 1)
    private String projectSchemeId;


}
