package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import java.util.List;

/**
 * @author: yk
 * @date: 2023/11/6 18:08
 * @description: 项目采购总信息
 */
@ApiModel(value = "ProjectPurchaseOrderAllInfoVO对象", description = "项目采购总信息")
@Data
public class ProjectPurchaseOrderAllInfoVO {

    /**
     * 项目采购基本信息
     */
    @ApiModelProperty(value = "项目采购基本信息")
    @Valid
    private ProjectPurchaseOrderInfoVO projectPurchaseOrderInfoVO;

    /**
     * 项目采购收货方信息
     */
    @ApiModelProperty(value = "项目采购收货方信息")
    @Valid
    private ProjectPurchaseReceiveInfoVO projectPurchaseReceiveInfoVO;

    /**
     * 项目采购供应商信息
     */
    @ApiModelProperty(value = "项目采购供应商信息")
    @Valid
    private ProjectPurchaseSupplierInfoVO projectPurchaseSupplierInfoVO;

    /**
     * 项目采购订单明细信息
     */
    @ApiModelProperty(value = "项目采购订单明细信息")
    @Valid
    private List<ProjectPurchaseOrderDetailVO> projectPurchaseOrderDetailVOList;

    /**
     * 合同附件信息
     */
    @ApiModelProperty(value = "合同附件信息")
    private List<DocumentVO> documentVOList;
}
