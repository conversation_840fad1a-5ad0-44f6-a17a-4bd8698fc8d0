package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/12/14:54
 * @description:
 */
@ApiModel(value = "MajorRepairPlanEconomizeDTO对象", description = "大修计划关键路径节约")
@Data
@ExcelIgnoreUnannotated
public class MajorRepairPlanEconomizeDTO extends ObjectDTO implements Serializable {


    /**
     * 作业ID
     */
    @ApiModelProperty(value = "作业ID")
    @ExcelProperty(value = "作业ID ", index = 0)
    private String jobManageId;

    /**
     * 集体剂量是否降低
     */
    @ApiModelProperty(value = "集体剂量是否降低")
    @ExcelProperty(value = "集体剂量是否降低 ", index = 1)
    private Boolean isReduce;

    /**
     * 结项日期
     */
    @ApiModelProperty(value = "结项日期")
    @ExcelProperty(value = "结项日期 ", index = 2)
    private Date closeDate;

    /**
     * 关键路径是否节约
     */
    @ApiModelProperty(value = "关键路径是否节约")
    @ExcelProperty(value = "关键路径是否节约 ", index = 3)
    private Boolean isEconomize;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    @ExcelProperty(value = "编号 ", index = 4)
    private String number;

    /**
     * 优化领域
     */
    @ApiModelProperty(value = "优化领域")
    @ExcelProperty(value = "优化领域 ", index = 5)
    private String optimizeField;

    /**
     * 大修类型
     */
    @ApiModelProperty(value = "大修类型")
    @ExcelProperty(value = "大修类型 ", index = 6)
    private String majorRepairType;

    /**
     * 应用机组类型
     */
    @ApiModelProperty(value = "应用机组类型")
    @ExcelProperty(value = "应用机组类型 ", index = 7)
    private String applicationCrew;

    /**
     * 计划工期(H)
     */
    @ApiModelProperty(value = "计划工期(H)")
    @ExcelProperty(value = "计划工期(H) ", index = 8)
    private BigDecimal planDuration;

    /**
     * 实际执行用时(H)
     */
    @ApiModelProperty(value = "实际执行用时(H)")
    @ExcelProperty(value = "实际执行用时(H) ", index = 9)
    private BigDecimal actualExeDuration;

    /**
     * 节约(H)
     */
    @ApiModelProperty(value = "节约(H)")
    @ExcelProperty(value = "节约(H) ", index = 10)
    private BigDecimal economizeDuration;

    /**
     * 内容介绍
     */
    @ApiModelProperty(value = "内容介绍")
    @ExcelProperty(value = "内容介绍 ", index = 11)
    private String content;

    /**
     * 延误原因
     */
    @ApiModelProperty(value = "延误原因")
    @ExcelProperty(value = "延误原因 ", index = 12)
    private String delayReason;

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    @ExcelProperty(value = "工单号 ", index = 13)
    private String jobManageNumber;

    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    @ExcelProperty(value = "大修轮次 ", index = 14)
    private String majorRepairTurn;

    /**
     * 创优技术或工作
     */
    @ApiModelProperty(value = "创优技术或工作")
    @ExcelProperty(value = "创优技术或工作 ", index = 15)
    private String innTechOrWork;


    @ApiModelProperty(value = "附件列表")
    private List<FileDTO> fileDTOList;


    /**
     * 是否可沿用
     */
    @ApiModelProperty(value = "是否可沿用")
    private Boolean isContinueUse;
    @ApiModelProperty(value = "关键词")
    private String keyword;
}
