package com.chinasie.orion.service.impl;





import com.chinasie.orion.domain.entity.ContractOurSignedSubject;
import com.chinasie.orion.domain.dto.ContractOurSignedSubjectDTO;
import com.chinasie.orion.domain.vo.ContractOurSignedSubjectVO;



import com.chinasie.orion.service.ContractOurSignedSubjectService;
import com.chinasie.orion.repository.ContractOurSignedSubjectMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;




/**
 * <p>
 * ContractOurSignedSubject 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28 21:49:21
 */
@Service
@Slf4j
public class ContractOurSignedSubjectServiceImpl extends OrionBaseServiceImpl<ContractOurSignedSubjectMapper, ContractOurSignedSubject> implements ContractOurSignedSubjectService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  ContractOurSignedSubjectVO detail(String id,String pageCode) throws Exception {
        ContractOurSignedSubject contractOurSignedSubject =this.getById(id);
        ContractOurSignedSubjectVO result = BeanCopyUtils.convertTo(contractOurSignedSubject,ContractOurSignedSubjectVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param contractOurSignedSubjectDTO
     */
    @Override
    public  String create(ContractOurSignedSubjectDTO contractOurSignedSubjectDTO) throws Exception {
        ContractOurSignedSubject contractOurSignedSubject =BeanCopyUtils.convertTo(contractOurSignedSubjectDTO,ContractOurSignedSubject::new);
        this.save(contractOurSignedSubject);

        String rsp=contractOurSignedSubject.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param contractOurSignedSubjectDTO
     */
    @Override
    public Boolean edit(ContractOurSignedSubjectDTO contractOurSignedSubjectDTO) throws Exception {
        ContractOurSignedSubject contractOurSignedSubject =BeanCopyUtils.convertTo(contractOurSignedSubjectDTO,ContractOurSignedSubject::new);

        this.updateById(contractOurSignedSubject);

        String rsp=contractOurSignedSubject.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        for (String id : ids) {




        }
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ContractOurSignedSubjectVO> pages( Page<ContractOurSignedSubjectDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ContractOurSignedSubject> condition = new LambdaQueryWrapperX<>( ContractOurSignedSubject. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ContractOurSignedSubject::getCreateTime);


        Page<ContractOurSignedSubject> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ContractOurSignedSubject::new));

        PageResult<ContractOurSignedSubject> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ContractOurSignedSubjectVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ContractOurSignedSubjectVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ContractOurSignedSubjectVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "甲方签约主体导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractOurSignedSubjectDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ContractOurSignedSubjectExcelListener excelReadListener = new ContractOurSignedSubjectExcelListener();
        EasyExcel.read(inputStream,ContractOurSignedSubjectDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ContractOurSignedSubjectDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("甲方签约主体导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ContractOurSignedSubject> contractOurSignedSubjectes =BeanCopyUtils.convertListTo(dtoS,ContractOurSignedSubject::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ContractOurSignedSubject-import::id", importId, contractOurSignedSubjectes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ContractOurSignedSubject> contractOurSignedSubjectes = (List<ContractOurSignedSubject>) orionJ2CacheService.get("pmsx::ContractOurSignedSubject-import::id", importId);
        log.info("甲方签约主体导入的入库数据={}", JSONUtil.toJsonStr(contractOurSignedSubjectes));

        this.saveBatch(contractOurSignedSubjectes);
        orionJ2CacheService.delete("pmsx::ContractOurSignedSubject-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ContractOurSignedSubject-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ContractOurSignedSubject> condition = new LambdaQueryWrapperX<>( ContractOurSignedSubject. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ContractOurSignedSubject::getCreateTime);
        List<ContractOurSignedSubject> contractOurSignedSubjectes =   this.list(condition);

        List<ContractOurSignedSubjectDTO> dtos = BeanCopyUtils.convertListTo(contractOurSignedSubjectes, ContractOurSignedSubjectDTO::new);

        String fileName = "甲方签约主体数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractOurSignedSubjectDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<ContractOurSignedSubjectVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class ContractOurSignedSubjectExcelListener extends AnalysisEventListener<ContractOurSignedSubjectDTO> {

        private final List<ContractOurSignedSubjectDTO> data = new ArrayList<>();

        @Override
        public void invoke(ContractOurSignedSubjectDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ContractOurSignedSubjectDTO> getData() {
            return data;
        }
    }


}
