<script setup lang="ts">
import { Chart } from 'lyra-component-vue3';
import { computed } from 'vue';

const props = defineProps<{
  data: Record<string, any>
}>();

const dataConfig = [
  {
    label: '今日离场',
    field: 'todayOutTotal',
  },
  {
    label: '今日入场',
    field: 'todayInTotal',
  },
  {
    label: '大于50周岁',
    field: 'ageCompareFifthTotal',
  },
];

const placeOption = computed(() => ({
  grid: {
    left: 0,
    right: 20,
    bottom: 0,
    top: 10,
    containLabel: true,
  },
  xAxis: {
    type: 'value',
    minInterval: 1,
    axisTick: {
      show: false,
    },
    splitLine: {
      show: false,
    },
  },
  yAxis: {
    type: 'category',
    axisTick: {
      show: false,
    },
    splitLine: {
      show: false,
    },
    data: dataConfig.map((item) => item.label),
  },
  series: [
    {
      data: dataConfig.map((item) => props?.data?.basePersonCountVO?.[item.field] || 0),
      type: 'bar',
      showBackground: true,
      label: {
        show: true,
        position: 'right',
      },
      backgroundStyle: {
        color: '#EBF1FF',
      },
      itemStyle: {
        color: '#6796FB',
      },
      barWidth: 30,
    },
  ],
}));

const overhaulOption = computed(() => ({
  grid: {
    left: 0,
    right: 50,
    bottom: 0,
    top: 10,
    containLabel: true,
  },
  xAxis: {
    type: 'value',
    minInterval: 1,
    axisTick: {
      show: false,
    },
    splitLine: {
      show: false,
    },
  },
  yAxis: {
    type: 'category',
    data: dataConfig.map((item) => item.label),
    axisTick: {
      show: false,
    },
    splitLine: {
      show: false,
    },
  },
  series: [
    {
      data: dataConfig.map((item) => props?.data?.majorPersonCountVO?.[item.field] || 0),
      type: 'bar',
      showBackground: true,
      label: {
        show: true,
        position: 'right',
      },
      backgroundStyle: {
        color: '#F5F3FF',
      },
      itemStyle: {
        color: '#9F92F9',
      },
      barWidth: 30,
    },
  ],
}));
</script>

<template>
  <div class="user-manage">
    <div class="place">
      <div class="title">
        基地人员总览
      </div>
      <div class="legend">
        <span>在场人数: {{ data?.basePersonCountVO?.['personTotal'] || 0 }}</span>
        <span class="left-block">单位: 人</span>
      </div>
      <div class="chart-wrap">
        <Chart :option="placeOption" />
      </div>
    </div>
    <div class="overhaul">
      <div class="title">
        大修人员一览
      </div>
      <div class="legend">
        <span>计划参修人数: {{ data?.majorPersonCountVO?.['personTotal'] || 0 }}</span>
        <span class="right-block">单位: 人</span>
      </div>
      <div class="chart-wrap">
        <Chart :option="overhaulOption" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.user-manage {
  box-shadow: 0 0 5px 0 #eee;
  height: 300px;
  padding: 15px 10px;
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  gap: 0 20px;

  > div {
    display: flex;
    flex-direction: column;

    .title {
      font-size: 16px;
      line-height: 1;

      > span {
        font-size: 14px;
        color: #aaa;
      }
    }

    .legend {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 10px;

      > span:nth-child(2) {
        position: relative;

        &::before {
          position: absolute;
          content: '';
          width: 14px;
          height: 14px;
          top: 50%;
          left: -10px;
          transform: translate(-100%, -50%);
        }

        &.left-block::before {
          background-color: #6B97F9;
        }

        &.right-block::before {
          background-color: #9F92FB;
        }
      }
    }

    .chart-wrap {
      flex-grow: 1;
      height: 0;
    }
  }
}
</style>
