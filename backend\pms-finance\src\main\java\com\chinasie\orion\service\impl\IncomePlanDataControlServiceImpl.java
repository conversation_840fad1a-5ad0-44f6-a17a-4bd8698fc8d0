package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.base.api.domain.entity.DeptDO;
import com.chinasie.orion.base.api.repository.DeptDOMapper;
import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.conts.IncomePlanControlEnum;
import com.chinasie.orion.conts.IncomePlanLockEnum;
import com.chinasie.orion.dict.IncomePlanDict;
import com.chinasie.orion.domain.dto.IncomePlanDataControlDTO;
import com.chinasie.orion.domain.entity.IncomePlan;
import com.chinasie.orion.domain.entity.IncomePlanData;
import com.chinasie.orion.domain.entity.IncomePlanDataControl;
import com.chinasie.orion.domain.vo.IncomePlanDataControlVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.IncomePlanDataControlMapper;
import com.chinasie.orion.sdk.domain.vo.org.SimpleDeptVO;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.IncomePlanDataControlService;
import com.chinasie.orion.service.IncomePlanDataService;
import com.chinasie.orion.service.IncomePlanService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <p>
 * IncomePlanDataControl 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29 18:59:40
 */
@Service
@Slf4j
public class IncomePlanDataControlServiceImpl extends OrionBaseServiceImpl<IncomePlanDataControlMapper, IncomePlanDataControl> implements IncomePlanDataControlService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;
    @Autowired
    private IncomePlanService incomePlanService;
    @Autowired
    private DeptRedisHelper deptRedisHelper;
    @Autowired
    private DictBo dictBo;;
    @Autowired
    private DeptDOMapper deptDOMapper;

    @Autowired
    private IncomePlanDataService incomePlanDataService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public IncomePlanDataControlVO detail(String id, String pageCode) throws Exception {
        IncomePlanDataControl incomePlanDataControl = this.getById(id);
        IncomePlanDataControlVO result = BeanCopyUtils.convertTo(incomePlanDataControl, IncomePlanDataControlVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param incomePlanDataControlDTO
     */
    @Override
    public String create(IncomePlanDataControlDTO incomePlanDataControlDTO) throws Exception {
        IncomePlanDataControl incomePlanDataControl = BeanCopyUtils.convertTo(incomePlanDataControlDTO, IncomePlanDataControl::new);
        this.save(incomePlanDataControl);

        String rsp = incomePlanDataControl.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param incomePlanDataControlDTO
     */
    @Override
    public Boolean edit(IncomePlanDataControlDTO incomePlanDataControlDTO) throws Exception {
        IncomePlanDataControl incomePlanDataControl = BeanCopyUtils.convertTo(incomePlanDataControlDTO, IncomePlanDataControl::new);

        this.updateById(incomePlanDataControl);

        String rsp = incomePlanDataControl.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<IncomePlanDataControlVO> pages(Page<IncomePlanDataControlDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<IncomePlanDataControl> condition = new LambdaQueryWrapperX<>(IncomePlanDataControl.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(IncomePlanDataControl::getCreateTime);


        Page<IncomePlanDataControl> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), IncomePlanDataControl::new));

        PageResult<IncomePlanDataControl> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<IncomePlanDataControlVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<IncomePlanDataControlVO> vos = BeanCopyUtils.convertListTo(page.getContent(), IncomePlanDataControlVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "收入计划数据管控导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", IncomePlanDataControlDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {
        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        IncomePlanDataControlExcelListener excelReadListener = new IncomePlanDataControlExcelListener();
        EasyExcel.read(inputStream, IncomePlanDataControlDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<IncomePlanDataControlDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("收入计划数据管控导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<IncomePlanDataControl> incomePlanDataControles = BeanCopyUtils.convertListTo(dtoS, IncomePlanDataControl::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::IncomePlanDataControl-import::id", importId, incomePlanDataControles, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<IncomePlanDataControl> incomePlanDataControles = (List<IncomePlanDataControl>) orionJ2CacheService.get("pmsx::IncomePlanDataControl-import::id", importId);
        log.info("收入计划数据管控导入的入库数据={}", JSONUtil.toJsonStr(incomePlanDataControles));
        this.saveBatch(incomePlanDataControles);
        orionJ2CacheService.delete("pmsx::IncomePlanDataControl-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::IncomePlanDataControl-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<IncomePlanDataControl> condition = new LambdaQueryWrapperX<>(IncomePlanDataControl.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(IncomePlanDataControl::getCreateTime);
        List<IncomePlanDataControl> incomePlanDataControles = this.list(condition);

        List<IncomePlanDataControlDTO> dtos = BeanCopyUtils.convertListTo(incomePlanDataControles, IncomePlanDataControlDTO::new);

        String fileName = "收入计划数据管控数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", IncomePlanDataControlDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<IncomePlanDataControlVO> vos) throws Exception {
        if(CollUtil.isEmpty(vos)){
            return;
        }
        List<String> deptIds = vos.stream().map(IncomePlanDataControlVO::getExpertiseCenter).collect(Collectors.toList());
        if(CollUtil.isEmpty(deptIds)){
            return;
        }
        List<SimpleDeptVO> simpleDepts = deptRedisHelper.getSimpleDeptByIds(deptIds);
        Map<String, String> dictMap=  dictBo.getDictValue(IncomePlanDict.LOCK_TYPE);
        Map<String,String> deptMap = simpleDepts.stream().collect(Collectors.toMap(SimpleDeptVO::getId,SimpleDeptVO::getName));
        vos.forEach(vo -> {
            if(StrUtil.isNotBlank(vo.getExpertiseCenter())){
                vo.setExpertiseCenterName(deptMap.get(vo.getExpertiseCenter()));
            }
            if(StrUtil.isNotBlank(vo.getLockStatus())){
                vo.setLockStatusName(dictMap.get(vo.getLockStatus()));
            }
        });


    }

    @Override
    public Boolean lock(List<IncomePlanDataControlDTO> incomePlanDataControlDTOs) throws ParseException {
        //判断数据是否有问题
        if(CollUtil.isEmpty(incomePlanDataControlDTOs)){
            throw new PMSException(PMSErrorCode.PMS_ERR, "锁定数据不能为空");
        }
        for(IncomePlanDataControlDTO incomePlanDataControlDTO:incomePlanDataControlDTOs){
            if(ObjectUtil.isEmpty(incomePlanDataControlDTO.getExpertiseCenterMoney())){
                throw new PMSException(PMSErrorCode.PMS_ERR, "锁定金额不能为空");
            }
            incomePlanDataControlDTO.setLockStatus(IncomePlanLockEnum.LOCKDOWN.getStatus());
        }
        List<IncomePlanDataControl> list = BeanCopyUtils.convertListTo(incomePlanDataControlDTOs,IncomePlanDataControl::new);
        this.updateBatchById(list);
        String incomePlanId = list.get(0).getIncomePlanId();
        IncomePlan incomePlan = incomePlanService.getById(incomePlanId);
        Map<String,BigDecimal> centersMap = new HashMap<>();
        List<String> centerIds = new ArrayList<>();
        for(IncomePlanDataControl incomePlanDataControl : list){
                centerIds.add(incomePlanDataControl.getExpertiseCenter());
                centersMap.put(incomePlanDataControl.getExpertiseCenter(),incomePlanDataControl.getExpertiseCenterMoney());
        }
        if(CollUtil.isNotEmpty(centerIds)){
            SimpleDateFormat originalFormat = new SimpleDateFormat("yyyy-MM");
            Date date = originalFormat.parse(incomePlan.getWorkTopics());
           List<IncomePlanData> incomePlanDatas= incomePlanDataService.list(new LambdaQueryWrapperX<>(IncomePlanData.class)
                   .in(IncomePlanData::getExpertiseCenter,centerIds)
                   .eq(IncomePlanData::getIncomePlanId,incomePlanId)
                   .eq(IncomePlanData::getDataVersion,incomePlan.getIncomePlanType())
                   .between(IncomePlanData::getEstimateInvoiceDate, DateUtil.beginOfMonth(date),DateUtil.endOfMonth(date))
           );
            Map<String, BigDecimal> sumByIncomePlanAmt = incomePlanDatas.stream()
                    .collect(Collectors.groupingBy(
                            IncomePlanData::getExpertiseCenter,
                            Collectors.reducing(
                                    BigDecimal.ZERO,
                                    IncomePlanData::getIncomePlanAmt,
                                    BigDecimal::add
                            )
                    ));
            centersMap.forEach((key,value)->{
                if(ObjectUtil.isNotEmpty(sumByIncomePlanAmt.get(key))&&sumByIncomePlanAmt.get(key).compareTo(value)>0){
                    throw new PMSException(PMSErrorCode.PMS_ERR, "收入金额大于锁定金额,无法锁定");
                }
            });
        }

        List<IncomePlanDataControl> unLockControls = this.list(new LambdaQueryWrapperX<>(IncomePlanDataControl.class).eq(IncomePlanDataControl::getIncomePlanId,incomePlanId)
                .eq(IncomePlanDataControl::getLockStatus,IncomePlanLockEnum.UNLOCK.getStatus()));

        if(CollUtil.isNotEmpty(unLockControls)){
            incomePlan.setLockStatus(IncomePlanControlEnum.PARTIALCONTROL.getStatus());
        }else{
            incomePlan.setLockStatus(IncomePlanControlEnum.CONTROL.getStatus());
        }
        incomePlanService.updateById(incomePlan);
        return true;
    }

    @Override
    public Boolean unLock(List<IncomePlanDataControlDTO> incomePlanDataControlDTOs) {
        if(CollUtil.isEmpty(incomePlanDataControlDTOs)){
            throw new PMSException(PMSErrorCode.PMS_ERR, "解锁数据不能为空");
        }
        for(IncomePlanDataControlDTO incomePlanDataControlDTO:incomePlanDataControlDTOs){
            incomePlanDataControlDTO.setLockStatus(IncomePlanLockEnum.UNLOCK.getStatus());
        }

        List<IncomePlanDataControl> list = BeanCopyUtils.convertListTo(incomePlanDataControlDTOs,IncomePlanDataControl::new);
        this.updateBatchById(list);
        String incomePlanId = list.get(0).getIncomePlanId();
        List<IncomePlanDataControl> lockControls = this.list(new LambdaQueryWrapperX<>(IncomePlanDataControl.class).eq(IncomePlanDataControl::getIncomePlanId,incomePlanId)
                .eq(IncomePlanDataControl::getLockStatus,IncomePlanLockEnum.LOCKDOWN.getStatus()));
        IncomePlan incomePlan = incomePlanService.getById(incomePlanId);
        if(CollUtil.isNotEmpty(lockControls)){
            incomePlan.setLockStatus(IncomePlanControlEnum.PARTIALCONTROL.getStatus());
        }else{
            incomePlan.setLockStatus(IncomePlanControlEnum.UNCONTROL.getStatus());
        }
        incomePlanService.updateById(incomePlan);
        return true;
    }

    @Override
    public void saveIncomePlanDataControl(String incomePlanId) {
        LambdaQueryWrapperX<DeptDO> deptCenterQuery = new LambdaQueryWrapperX<>(DeptDO.class);
        deptCenterQuery.eq(DeptDO::getType, "20");
        deptCenterQuery.eq(DeptDO::getTypeCode, "21");
        deptCenterQuery.eq(DeptDO::getLogicStatus, 1);
        List<DeptDO> deptDOS = deptDOMapper.selectList(deptCenterQuery);
        List<IncomePlanDataControl> controlList = new ArrayList<>();
        for (DeptDO deptDO : deptDOS) {
            IncomePlanDataControl incomePlanDataControl = new IncomePlanDataControl();
            incomePlanDataControl.setExpertiseCenter(deptDO.getId());
            incomePlanDataControl.setIncomePlanId(incomePlanId);
            incomePlanDataControl.setExpertiseCenterMoney(BigDecimal.ZERO);
            incomePlanDataControl.setLockStatus(IncomePlanLockEnum.UNLOCK.getStatus());
            controlList.add(incomePlanDataControl);
        }
        if(CollUtil.isNotEmpty(controlList)) {
            this.saveBatch(controlList);
        }
    }

    @Override
    public List<IncomePlanDataControlVO> getList(String id,List<List<SearchCondition>> searchConditions) throws Exception {
        LambdaQueryWrapperX<IncomePlanDataControl> condition = new LambdaQueryWrapperX<>(IncomePlanDataControl.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            condition.leftJoin(DeptDO.class,DeptDO::getId,IncomePlanDataControl::getExpertiseCenter);
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.eq(IncomePlanDataControl::getIncomePlanId,id);
        List<IncomePlanDataControl> list = this.list(condition);
        List<IncomePlanDataControlVO> result = BeanCopyUtils.convertListTo(list,IncomePlanDataControlVO::new);
        setEveryName(result);
        return result;
    }


    public static class IncomePlanDataControlExcelListener extends AnalysisEventListener<IncomePlanDataControlDTO> {

        private final List<IncomePlanDataControlDTO> data = new ArrayList<>();

        @Override
        public void invoke(IncomePlanDataControlDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<IncomePlanDataControlDTO> getData() {
            return data;
        }
    }


}
