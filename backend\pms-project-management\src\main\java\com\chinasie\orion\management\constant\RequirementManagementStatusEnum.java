package com.chinasie.orion.management.constant;


import lombok.Getter;

@Getter
public enum RequirementManagementStatusEnum {

    TO_BE_DISTRIBUTED(121, "待分发"),
    HAVE_CONFIRMED(130, "已确认"),
    TO_BE_DECIDED(120, "待确定"),
    NO_RESPONSE(140, "不响应"),
    CANCELED(111, "作废"),
    ;

    private Integer status;

    private String desc;

    RequirementManagementStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
