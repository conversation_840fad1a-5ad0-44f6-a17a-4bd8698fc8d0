package com.chinasie.orion.domain.dto.reporting;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2023/11/14/22:59
 * @description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AuditParamDTO {
    @ApiModelProperty(value = "数据IDList")
    private List<String> idList;

    /**
     * 评价
     */
    @ApiModelProperty(value = "评价")
    private String evaluate;

    @ApiModelProperty(value = "详情跳转地址")
    private String detailUrl;

    /**
     * 评分
     */
    @ApiModelProperty(value = "评分")
    private BigDecimal score =BigDecimal.ZERO;



}
