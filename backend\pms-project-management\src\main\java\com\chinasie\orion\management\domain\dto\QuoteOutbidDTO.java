package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.chinasie.orion.management.domain.vo.QuoteOutbidVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


@ApiModel(value = "QuoteOutbidDTO对象", description = "报价管理")
@Data
@ExcelIgnoreUnannotated
public class QuoteOutbidDTO extends QuoteOutbidVO {
    @ApiModelProperty(value = "核能报价总额")
    private BigDecimal nuclearEnergyTotal;

    @ApiModelProperty(value = "非核能报价总额")
    private BigDecimal nonNuclearEnergyTotal;

    @ApiModelProperty(value = "总报价总额")
    private BigDecimal sumTotal;

    @ApiModelProperty(value = "发出总数量")
    private Integer number;




}
