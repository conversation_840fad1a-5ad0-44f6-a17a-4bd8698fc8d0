package com.chinasie.orion.constant;

/**
 * @author: yk
 * @date: 2023/10/25 20:40
 */
public enum ProjectContractChangeApplyStatusEnum {
    CREATED(101, "编制中"),
    RUNING(110, "进行中"),
    FINISH(130, "已完成"),
    ;

    private Integer status;

    private String desc;


    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    ProjectContractChangeApplyStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
