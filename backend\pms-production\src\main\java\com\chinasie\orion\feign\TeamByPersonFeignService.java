package com.chinasie.orion.feign;


import com.chinasie.orion.domain.dto.allocation.DeptTreeDTO;
import com.chinasie.orion.domain.dto.allocation.DeptTreeVO;
import com.chinasie.orion.domain.dto.allocation.SpecialtyAndTeam;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.core.conf.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "icm",configuration = {FeignConfig.class})
@Lazy
public interface TeamByPersonFeignService {

    @PostMapping("/teamByPerson/list")
    ResponseDTO<List<SpecialtyAndTeam>> teamByPersonList(@RequestParam(name = "staffNo", defaultValue = "") String staffNo);

    @PostMapping("/deptTree/list")
    ResponseDTO<List<DeptTreeVO>> queryDeptPersonTree(@RequestBody DeptTreeDTO deptTreeDTO);


}
