<template>
  <div
    v-loading="loading"
    class="h-full detail-wrap"
  >
    <BasicCard
      title="基本信息"
      :gridContentProps="gridContentProps"
      :column="4"
    />
  </div>
</template>

<script>
import {
  inject, onMounted, reactive, ref, toRefs,
} from 'vue';
import Api from '/@/api';
import {
  BasicCard,
} from 'lyra-component-vue3';
import Edit from './Edit.vue';
import View from './View.vue';

export default {
  name: 'Summarize',
  components: {
    BasicCard,
  },
  props: {
    id: String,
    projectId: String,
    pageType: {
      type: String,
      default: 'page',
    },
  },
  setup(props) {
    const state = reactive({
      loading: false,
      editRef: ref(),
      view: { form: {} },
      edit: { form: {} },
      powerData: [],
      showDetails: true,
    });
    state.powerData = inject('powerData');
    const gridContentProps = ref({
      list: [
        {
          label: '编号',
          field: 'number',
        },
        {
          label: '名称',
          field: 'name',
          wrap: true,
          gridColumn: '1/5',
        },
        {
          label: '所属项目',
          field: 'projectName',
        },
        {
          label: '所属任务',
          field: 'planName',
          wrap: true,
          gridColumn: '1/5',
        },
        {
          label: '状态',
          field: 'statusName',
        },
        {
          label: '版本',
          field: 'revId',
        },
        {
          label: '负责人',
          field: 'principalName',
        },
        {
          label: '计划交付时间',
          field: 'predictDeliverTime',
        },
        {
          label: '实际交付时间',
          field: 'deliveryTime',
        },
        {
          label: '描述',
          field: 'remark',
          wrap: true,
          gridColumn: '1/5',
        },
        {
          label: '修改人',
          field: 'modifyName',
        },
        {
          label: '修改时间',
          field: 'modifyTime',
        },
        {
          label: '创建人',
          field: 'creatorName',
        },
        {
          label: '创建时间',
          field: 'createTime',
        },
      ],
      dataSource: {

      },
    });
    function handleEdit() {
      state.edit = JSON.parse(JSON.stringify(state.view));
      state.showDetails = false;
    }
    function submit(val) {
      state.loading = true;
      new Api('/pms')
        .fetch(val, 'deliverable', 'PUT')
        .then(() => {
          state.loading = false;
          state.showDetails = true;
          getForm();
        })
        .catch(() => {
          state.loading = false;
        });
    }

    gridContentProps.value.dataSource = inject('projectInfo');

    function onSave() {
      state.editRef.submit(submit);
    }

    return {
      ...toRefs(state),
      onSave,
      handleEdit,
      gridContentProps,
    };
  },
};
</script>

<style scoped lang="less">
.detail-wrap {
  padding: ~`getPrefixVar('content-margin')`;
}
</style>
