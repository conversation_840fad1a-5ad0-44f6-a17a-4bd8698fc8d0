<script setup lang="ts">
import {
  BasicButton,
  OrionATable, randomString, isPower, downloadByData,
} from 'lyra-component-vue3';
import {
  ref, inject,
} from 'vue';
import { RadioButton, RadioGroup, Modal } from 'ant-design-vue';
import useMaterialsExcel from './childComponent/hooks/useMaterials';

const props = withDefaults(defineProps<{
  isDetails: boolean
  updateKey: string
}>(), {
  isDetails: false,
});

const emits = defineEmits<{
  (e: 'update:updateKey', key: string): void
}>();

const radioType = ref<'preparation' | 'operation'>('preparation');
const keyword = ref<string | undefined>(undefined);
const allFlagKeys = ref(false);
const expandFlag = ref(false);
const detailsData: Record<string, any> = inject('detailsData');
const powerData = inject('powerData');
const loadStatus = ref(false);

const {
  expandedRowKeys, columns, innerColumns, dataApi, actions, innerActions, data, loadingRef,
} = useMaterialsExcel({
  radioType,
  keyword,
  allFlagKeys,
}, {
  isDetails: props.isDetails,
  updateTable,
});

function updateTable(isUpdateKey: boolean = false, allFlag: boolean = false) {
  allFlagKeys.value = allFlag;
  dataApi();
  if (isUpdateKey) {
    emits('update:updateKey', randomString());
  }
}

function changeRadio() {
  data.value = [];
  dataApi();
}

function expandAll() {
  allFlagKeys.value = !expandFlag.value;
  expandFlag.value = !expandFlag.value;
  dataApi();
}

// 准备导出（进场）实施导出（离场）
async function handleExport() {
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk: async () => {
      const params = {
        repairRound: detailsData?.repairRound,
      };
      loadStatus.value = true;
      const url = radioType.value === 'preparation' ? '/pms/majorExcel/material/in/export' : '/pms/majorExcel/material/out/export';
      await downloadByData(url, params, '', 'POST', true, false, '导出处理完成，现在开始下载');
      loadStatus.value = false;
    },
  });
}

</script>

<template>
  <OrionATable
    v-model:expandedRowKeys="expandedRowKeys"
    v-model:keyword="keyword"
    :dataSource="data"
    :columns="columns"
    :scroll="{y:500}"
    :loading="loadingRef"
    :pagination="false"
    :rowKey="(r)=>r?.data?.id"
    :actions="actions"
    :innerActions="innerActions"
    :innerColumns="innerColumns"
    @search="dataApi"
  >
    <template #buttons>
      <div class="flex flex-pac">
        <BasicButton
          :icon="expandFlag ? 'fa-angle-up' : 'fa-angle-down'"
          @click="expandAll()"
        >
          {{ expandFlag ? '收起' : '展开所有' }}
        </BasicButton>
        <BasicButton
          v-if="isPower('PMS_DXXQEC_container_04_button_04', powerData)"
          type="primary"
          ghost
          icon="sie-icon-daoru"
          @click="handleExport()"
        >
          导出
        </BasicButton>
        <BasicButton
          icon="sie-icon-chongzhi"
          style="margin-left: auto"
          @click="updateTable(false,true)"
        >
          刷新统计
        </BasicButton>
        <RadioGroup
          v-model:value="radioType"
          @change="changeRadio"
        >
          <RadioButton value="preparation">
            大修准备
          </RadioButton>
          <RadioButton value="operation">
            大修实施
          </RadioButton>
        </RadioGroup>
      </div>
    </template>
  </OrionATable>
</template>
<style lang="less">
.ant-table-cell.required-cell {
  &::after {
    position: absolute;
    content: '*';
    top: 50%;
    left: 2px;
    transform: translateY(-50%);
    color: red;
  }

  &.center::after {
    left: calc(50% - 35px);
  }
}
.blue-icon {
  border: 1px solid rgb(35, 112, 195);
  border-radius: 50%;
  width: 16px;
  height: 16px;
  font-size: 10px;
  margin-left:auto;
  display: flex;
  text-align: initial;
  color: rgb(35, 112, 195);
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

// 操作栏样式
.table-container{
  .ant-table-body {
    .ant-table-wrapper,
    .surely-table-cell-inner {
      .common-center-major {
        display: flex;
        justify-content: flex-start;
      }

      .common-s-major {
        background: inherit;
        box-sizing: border-box;
        border-width: 1px;
        border-style: solid;
        border-radius: 4px;
        -moz-box-shadow: none;
        -webkit-box-shadow: none;
        box-shadow: none;
        font-weight: 400;
        font-style: normal;
        font-size: 12px;
        text-align: center;
        height: 22px;
        line-height: 20px;
        padding: 0 5px;
      }

      .green-s-major {
        width: 50px;
        background-color: rgba(246, 255, 237, 1);
        border-color: rgba(183, 235, 143, 1);
        color: #52C41A;
      }

      .warn-s-major {
        width: 50px;
        background-color: rgba(255, 251, 230, 1);
        border-color: rgba(255, 229, 143, 1);
        color: #FAAD14;
      }

      .blue-s-major {
        width: auto;
        background-color: rgba(230, 247, 255, 1);
        border-color: rgba(145, 213, 255, 1);
        color: #1890FF;
      }

      .red-s-major {
        width: 50px;
        background-color: rgba(254, 240, 239, 1);
        border-color: rgba(255, 163, 158, 1);
        color: #F5222D;
      }
    }
  }
}
</style>
<style scoped lang="less">
@import url('./childComponent/style.less');
</style>
