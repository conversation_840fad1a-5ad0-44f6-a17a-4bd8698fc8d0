<script lang="ts" setup>
import { BasicForm, FormSchema, useForm } from 'lyra-component-vue3';
import { Input, Button as AButton, Select, RangePicker } from 'ant-design-vue'
import { disable } from '../../api/documentModelLibrary';
import type { SelectProps } from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';
import axios from 'axios';
import Api from '/@/api';
import { defineComponent, ref, nextTick, reactive, onMounted, computed, h, watch, Ref, watchEffect } from 'vue';
import { useUserStore } from '/@/store/modules/user';
const userStore = useUserStore();
const dateFormat = 'YYYY-MM-DD';
const props = withDefaults(defineProps<{
  record: any,
  column: any,
  markBusinessId: any
}>(), {
  record: null,
  column: null,
  markBusinessId: null
});

// console.log("弹窗",props.record,props.column,props.markBusinessId);
// const realEndDate = props.markBusinessId.realEndDate?.props.markBusinessId.realEndDate;
// const realStartDate = props.markBusinessId.realStartDate?.props.markBusinessId.realStartDate;
// function standardizeDateString(dateString) {
//   const parts = dateString.split('-');
//   if (parts.length !== 3) {
//   }
//   const year = parts[0];
//   const month = parts[1].padStart(2, '0'); // 确保月份为两位数
//   const day = parts[2].padStart(2, '0');   // 确保日期为两位数
//   return `${year}-${month}-${day}`;
// }


function findTimeInterval(timestamp, sectionTimes) {
  const inputDate = new Date(timestamp);
  for (let i = 0; i < sectionTimes.length; i++) {
    const section = sectionTimes[i];

    const realStartDate = new Date(section.realStartDate);
    const realEndDate = new Date(section.realEndDate);
    if (inputDate >= realStartDate && inputDate <= realEndDate) {
      return section; // 返回匹配的 section 对象
    }
  }
  return null;
}
const sectionTimes = props.record?.sectionTimes;
const timestamp = props.column.dataIndex;
const resultDate = findTimeInterval(timestamp, sectionTimes);




const schemas: FormSchema[] = [
  {
    field: 'userId1',
    component: 'Input',
    label: '调配人员',
    rules: [{ required: true }],
    defaultValue: props.record.name,
    colProps: {
      span: 24,
    },
    dynamicDisabled: true,
  },
  {
    field: 'repairRound',
    component: 'Select',
    label: '大修轮次',
    rules: [{ required: true }],
    slot: 'customRepairSelect',
    colProps: {
      span: 24,
    },
    defaultValue: resultDate?.repairRoundName
  },
  {
    field: 'data',
    component: 'RangePicker',
    label: '计划进出基地日期',
    rules: [{ required: true }],
    componentProps: {
      defaultValue: props.markBusinessId
        ? [dayjs(props.markBusinessId.realStartDate), dayjs(props.markBusinessId.realEndDate)]
        : null
    },
    colProps: {
      span: 24,
    },
  },
  {
    field: 'costCenterCode',
    component: 'Select',
    label: '班组信息',
    rules: [{ required: true }],
    slot: 'customSelect',
    colProps: {
      span: 24,
    },
    defaultValue: resultDate?.teamName
  },
];

const [register, { validate }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});


defineExpose({
  getValues,
});













const repairOptions = ref<SelectProps['options']>();

const options = ref<SelectProps['options']>();

const costCenterCode_ = ref(props.record.costCenterCode);

//待优化 todo方法封装
const basePlaceCode = ref(props.markBusinessId === null ? '' : props.markBusinessId.basePlaceCode);
const basePlaceName = ref(props.markBusinessId === null ? '' : props.markBusinessId.basePlaceName);
const repairRoundCode = ref(props.markBusinessId === null ? '' : props.markBusinessId.repairRoundCode);
const repairRoundName = ref(props.markBusinessId === null ? '' : props.markBusinessId.repairRoundName);
const handleRepairChange = (value: string) => {
  const parts = value.split("-");//parts[0]为基地编码，parts[1]为大修轮次
  basePlaceCode.value = parts[0];
  basePlaceName.value = parts[1];
  repairRoundCode.value = parts[2];
  repairRoundName.value = parts[3];
  costCenterCode_.value = value;
  //处理大修轮次
}

const specialtyCode = ref(props.markBusinessId === null ? '' : props.markBusinessId.specialtyCode);
const specialtyName = ref(props.markBusinessId === null ? '' : props.markBusinessId.specialtyName);
const relationId = ref(props.markBusinessId === null ? '' : props.markBusinessId.relationId);
const teamName = ref(props.markBusinessId === null ? '' : props.record.sectionTimes[0].teamName);
const teamCode = ref(props.markBusinessId === null ? '' : props.record.sectionTimes[0].teamCode);
const handleChange = (value: string) => {
  const parts = value.split("-");//parts[0]专业编码，parts[1]班组编码
  // costCenterCode_.value = value;
  //处理专业班组
  specialtyCode.value = parts[0];
  specialtyName.value = parts[2];
  teamCode.value = parts[1];
  teamName.value = parts[3];
}

async function getValues() {
  const values = await validate();
  let markBusinessId_ = {
    // rowId: props.column.markBusinessId?.rowId,
    rowId: resultDate?.rowId,
    realStartDate: dayjs(values.data[0]).format(dateFormat),
    realEndDate: dayjs(values.data[1]).format(dateFormat),
    repairName: values.repairRound,
    basePlaceCode: basePlaceCode.value,
    basePlaceName: basePlaceName.value,
    specialtyCode: specialtyCode.value,
    specialtyName: specialtyName.value,
    teamCode: teamCode.value,
    teamName: teamName.value,
    relationId: resultDate?.relationId
  };

  let formData = {
    number: props.record.number,
    name: props.record.name,
    costCenterCode: costCenterCode_.value,
    repairRound: values.repairRound,
    realStartDate: values.data[0].format(dateFormat),
    realEndDate: values.data[1].format(dateFormat),
    markBusinessId: markBusinessId_,
  };
  return formData;
}

const loading: Ref<boolean> = ref(false);
onMounted(() => {
  getOptionsMock();
  // getOptionsMock_();
})
const getOptionsMock_ = async () => {
  loading.value = true;
  try {
    new Promise((resolve, reject) => {
      setTimeout(function () {
        options.value = [
          {
            label: '其他专业',
            options: [
              {
                label: '表面班组',
                value: 'OTHER-其他专业-TEAM001-表面班组'
              },
              {
                label: '风控班组',
                value: 'OTHER-其他专业-TEAM002-风控班组'
              },
            ]
          },
        ];
        repairOptions.value = [
          {
            label: '大亚湾基地',
            options: [
              {
                label: 'H208',
                value: 'DYW-大亚湾基地-H208-H208'
              },
              {
                label: 'H602',
                value: 'DYW-大亚湾基地-H602-H602'
              },
            ]
          },
        ];
      }, 1000);
    });
  } finally {
    setTimeout(() => {
      loading.value = false;
    }, 100)
  }
}
// const getOptionsMock = async () => {
//   loading.value = true;
//   try {
//     axios.post('http://192.168.0.105:8700/resource-allocation-of/resourceAllocation/queryRepairPlanAndSpecialtyTeam',
//       {
//         repairRound: '', staffNo: 'P199129',
//       },
//       {
//         headers: {
//           'Content-Type': 'application/json; charset=utf-8',
//           'dtc_access': 'ok_yes',
//           'X-Access-Token': 'dtc_access'
//         }
//       }
//     )
//       .then(function (response) {
//         console.log(response);
//         // 处理响应数据
//         options.value = response.data.result.specialtyAndTeams;
//         repairOptions.value = response.data.result.repairRoundAnds;
//         // loading.value = false;
//         // console.log(response.data);
//       })
//       .catch(function (error) {
//         // 处理错误情况
//         console.log(error);
//       });
//   } finally {
//     setTimeout(() => {
//       loading.value = false;
//     }, 2000)
//   }
// }
async function getOptionsMock() {
  loading.value = true;
    try {
      const result = await new Api('/pms/resource-allocation-of/resourceAllocation/queryRepairPlanAndSpecialtyTeam').fetch({ 
        repairRound: '',
        staffNo: userStore.getUserInfo.code,
      }, '', 'POST');
      options.value = result.specialtyAndTeams;
      repairOptions.value = result.repairRoundAnds;
    } finally {
      loading.value = false;
    }
}
</script>

<template>
  <div class="form-component-wrap ant-col-24">
    <BasicForm @register="register">
      <template #customRepairSelect="{ model, field }">
        <Select class="ant-col-24" v-model:value="model[field]" :options="repairOptions" style="width: 100%"
          @change="handleRepairChange"></Select>
      </template>
      <template #customSelect="{ model, field }">
        <Select class="ant-col-24" v-model:value="model[field]" :options="options" style="width: 100%"
          @change="handleChange"></Select>
      </template>
    </BasicForm>
  </div>
</template>

<style scoped></style>
