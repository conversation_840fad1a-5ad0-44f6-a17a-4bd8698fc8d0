package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.chinasie.orion.constant.BasicUserEnum;
import com.chinasie.orion.domain.entity.BasicUser;
import com.chinasie.orion.domain.entity.BasicUserExtendInfo;
import com.chinasie.orion.domain.dto.BasicUserExtendInfoDTO;
import com.chinasie.orion.domain.vo.BasicUserExtendInfoVO;
import com.chinasie.orion.service.BasicUserExtendInfoService;
import com.chinasie.orion.repository.BasicUserExtendInfoMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import com.mzt.logapi.context.LogRecordContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;

import javax.servlet.http.HttpServletResponse;

import com.chinasie.orion.excel.util.ExcelUtils;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.chinasie.orion.cache.OrionJ2CacheService;

import java.io.InputStream;
import java.util.stream.Collectors;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;


/**
 * <p>
 * BasicUserExtendInfo 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05 10:41:49
 */
@Service
@Slf4j
public class BasicUserExtendInfoServiceImpl extends OrionBaseServiceImpl<BasicUserExtendInfoMapper, BasicUserExtendInfo> implements BasicUserExtendInfoService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public BasicUserExtendInfoVO detail(String id, String pageCode) throws Exception {
        BasicUserExtendInfo basicUserExtendInfo = this.getById(id);
        BasicUserExtendInfoVO result = BeanCopyUtils.convertTo(basicUserExtendInfo, BasicUserExtendInfoVO::new);
        setEveryName(Collections.singletonList(result));

        LogRecordContext.putVariable("userCode", basicUserExtendInfo.getNumber());
        return result;
    }

    /**
     * 新增
     * <p>
     * * @param basicUserExtendInfoDTO
     */
    @Override
    public String create(BasicUserExtendInfoDTO basicUserExtendInfoDTO) throws Exception {
        BasicUserExtendInfo basicUserExtendInfo = BeanCopyUtils.convertTo(basicUserExtendInfoDTO, BasicUserExtendInfo::new);
        this.save(basicUserExtendInfo);

        String rsp = basicUserExtendInfo.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param basicUserExtendInfoDTO
     */
    @Override
    public Boolean edit(BasicUserExtendInfoDTO basicUserExtendInfoDTO) throws Exception {
        BasicUserExtendInfo basicUserExtendInfo = BeanCopyUtils.convertTo(basicUserExtendInfoDTO, BasicUserExtendInfo::new);

        this.updateById(basicUserExtendInfo);

        String rsp = basicUserExtendInfo.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        LambdaQueryWrapperX<BasicUserExtendInfo> condition = new LambdaQueryWrapperX<>(BasicUserExtendInfo.class);
        condition.in(BasicUserExtendInfo::getId, ids);
        condition.select(BasicUserExtendInfo::getNumber);
        List<BasicUserExtendInfo> basicUserExtendInfo = this.list(condition);
        if(!CollectionUtils.isEmpty(basicUserExtendInfo)){
            LogRecordContext.putVariable("userCodes", basicUserExtendInfo.stream().map(BasicUserExtendInfo::getNumber).collect(Collectors.joining(",")));
        }
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<BasicUserExtendInfoVO> pages(String mainTableId, Page<BasicUserExtendInfoDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<BasicUserExtendInfo> condition = new LambdaQueryWrapperX<>(BasicUserExtendInfo.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(BasicUserExtendInfo::getCreateTime);

        condition.eq(BasicUserExtendInfo::getMainTableId, mainTableId);

        Page<BasicUserExtendInfo> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), BasicUserExtendInfo::new));

        PageResult<BasicUserExtendInfo> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<BasicUserExtendInfoVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<BasicUserExtendInfoVO> vos = BeanCopyUtils.convertListTo(page.getContent(), BasicUserExtendInfoVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "人员拓展信息导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", BasicUserExtendInfoDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        BasicUserExtendInfoExcelListener excelReadListener = new BasicUserExtendInfoExcelListener();
        EasyExcel.read(inputStream, BasicUserExtendInfoDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<BasicUserExtendInfoDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("人员拓展信息导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<BasicUserExtendInfo> basicUserExtendInfoes = BeanCopyUtils.convertListTo(dtoS, BasicUserExtendInfo::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("ncf::BasicUserExtendInfo-import::id", importId, basicUserExtendInfoes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<BasicUserExtendInfo> basicUserExtendInfoes = (List<BasicUserExtendInfo>) orionJ2CacheService.get("ncf::BasicUserExtendInfo-import::id", importId);
        log.info("人员拓展信息导入的入库数据={}", JSONUtil.toJsonStr(basicUserExtendInfoes));

        this.saveBatch(basicUserExtendInfoes);
        orionJ2CacheService.delete("ncf::BasicUserExtendInfo-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("ncf::BasicUserExtendInfo-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<BasicUserExtendInfo> condition = new LambdaQueryWrapperX<>(BasicUserExtendInfo.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(BasicUserExtendInfo::getCreateTime);
        List<BasicUserExtendInfo> basicUserExtendInfoes = this.list(condition);

        List<BasicUserExtendInfoDTO> dtos = BeanCopyUtils.convertListTo(basicUserExtendInfoes, BasicUserExtendInfoDTO::new);

        String fileName = "人员拓展信息数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", BasicUserExtendInfoDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<BasicUserExtendInfoVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }

    @Override
    public List<BasicUserExtendInfo> getUserExtendInfoByCode(List<String> userCodes) throws Exception {
        LambdaQueryWrapperX<BasicUserExtendInfo> condition = new LambdaQueryWrapperX<>(BasicUserExtendInfo.class);
        condition.in(BasicUserExtendInfo::getNumber, userCodes);
        List<BasicUserExtendInfo> basicUserExtendInfoes = this.list(condition);
        return basicUserExtendInfoes;
    }

    @Override
    public int getUserByTime(List<String> userCodes, String type, Date startTime, Date endTime) throws Exception {
        LambdaQueryWrapperX<BasicUserExtendInfo> condition = new LambdaQueryWrapperX<>(BasicUserExtendInfo.class);
        if (type.equals("入场")) {
            condition.between(BasicUserExtendInfo::getEntryTime, startTime, endTime);
        } else if (type.equals("离场")) {
            condition.between(BasicUserExtendInfo::getDepartureTime, startTime, endTime);
        } else if (type.equals("黑名单")) {
            condition.between(BasicUserExtendInfo::getDepartureTime, startTime, endTime);
            condition.eq(BasicUserExtendInfo::getViolatedSafetyRegulations, "是");
        }
        condition.in(BasicUserExtendInfo::getNumber, userCodes);
        List<BasicUserExtendInfo> basicUserExtendInfoes = this.list(condition);
        return basicUserExtendInfoes.size();
    }


    @Override
    public Double getUserEquivalentByTime(LocalDate startDate, LocalDate endDate) throws Exception {

        Long days = calculateWorkdayRatio(startDate,  endDate);
        if(days==0){
            return 0.0;
        }
        LambdaQueryWrapperX<BasicUserExtendInfo> condition = new LambdaQueryWrapperX<>(BasicUserExtendInfo.class);
        condition.leftJoin(BasicUser.class,  BasicUser::getUserCode,BasicUserExtendInfo::getNumber);
        condition.eq(BasicUser::getPersonType, BasicUserEnum.TECHNICAL.getType());
        condition.and(item-> item.le(BasicUserExtendInfo::getEntryTime, endDate).and(item1->item1.ge(BasicUserExtendInfo::getDepartureTime,startDate)).or()
                .le(BasicUserExtendInfo::getEntryTime, endDate).and(item1->item1.isNull((BasicUserExtendInfo::getDepartureTime))));
        List<BasicUserExtendInfo> userExtendInfos = this.list(condition);
        if(CollUtil.isEmpty(userExtendInfos)){
            return 0.0;
        }
        Double equivalent = 0.0;
        for(BasicUserExtendInfo basicUserExtendInfo:userExtendInfos){
            if(ObjectUtil.isEmpty(basicUserExtendInfo.getEntryTime())){
                continue;
            }
            LocalDate localDate = basicUserExtendInfo.getEntryTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate departureTime = endDate;
            if(ObjectUtil.isNotEmpty(basicUserExtendInfo.getDepartureTime())){
                 departureTime = basicUserExtendInfo.getDepartureTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            }
            if(localDate.isBefore(startDate)||localDate.equals(startDate)&&(departureTime == null||departureTime.isAfter(endDate)||departureTime.equals(endDate))){
                equivalent = equivalent+1;
            }else {
                if (localDate.isBefore(startDate)||localDate.equals(startDate)) {
                    localDate = startDate;
                }

                if (departureTime == null||departureTime.isAfter(endDate)||departureTime.equals(endDate)) {
                    departureTime = endDate;
                }
                Long day = calculateWorkdayRatio(localDate,  departureTime);
                if(day>0){
                    equivalent = equivalent+ Math.round((day / days) * 10.0) / 10.0;
                }
            }

        }
        return equivalent;
    }


    // 计算某个月的工作日天数
    public static int getWorkdaysInMonth(int year, int month) {
        LocalDate firstDayOfMonth = LocalDate.of(year, month, 1);
        LocalDate lastDayOfMonth = firstDayOfMonth.plusMonths(1).minusDays(1);

        int workdays = 0;
        for (LocalDate date = firstDayOfMonth; !date.isAfter(lastDayOfMonth); date = date.plusDays(1)) {
            if (!isWeekend(date)) {
                workdays++;
            }
        }
        return workdays;
    }

    // 判断是否是周末
    public static boolean isWeekend(LocalDate date) {
        return date.getDayOfWeek().getValue() >= 6; // 6 表示周六，7 表示周日
    }

    // 计算工作日工作比例
    public Long  calculateWorkdayRatio(LocalDate startDate, LocalDate endDate) {
        long workdays = 0;
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            if (!isWeekend(date)) {
                workdays++;
            }
        }
        return  workdays ; // 工作日
    }

    @Override
    public int getUserByEndTime(List<String> userCodes,String type, Date endTime) throws Exception {
        LambdaQueryWrapperX<BasicUserExtendInfo> condition = new LambdaQueryWrapperX<>(BasicUserExtendInfo.class);
        if (type.equals("入场")) {
            condition.lt(BasicUserExtendInfo::getEntryTime, endTime);
        } else if (type.equals("离场")) {
            condition.lt(BasicUserExtendInfo::getDepartureTime, endTime);
        } else if (type.equals("黑名单")) {
            condition.lt(BasicUserExtendInfo::getDepartureTime, endTime);
            condition.eq(BasicUserExtendInfo::getViolatedSafetyRegulations, "是");
        }
        condition.in(BasicUserExtendInfo::getNumber, userCodes);
        List<BasicUserExtendInfo> basicUserExtendInfoes = this.list(condition);
        return basicUserExtendInfoes.size();
    }

    @Override
    public int getOpeningData(List<String> userCodes, String type, String year) {
        LambdaQueryWrapperX<BasicUserExtendInfo> condition = new LambdaQueryWrapperX<>(BasicUserExtendInfo.class);
        if (type.equals("入场")) {
            condition.lt(BasicUserExtendInfo::getEntryTime, year+"-01-01");
        } else if (type.equals("离场")) {
            condition.lt(BasicUserExtendInfo::getDepartureTime, year+"-01-01");
        } else if (type.equals("黑名单")) {
            condition.lt(BasicUserExtendInfo::getDepartureTime, year+"-01-01");
            condition.eq(BasicUserExtendInfo::getViolatedSafetyRegulations, "是");
        }
        condition.in(BasicUserExtendInfo::getNumber, userCodes);
        List<BasicUserExtendInfo> basicUserExtendInfoes = this.list(condition);
        return basicUserExtendInfoes.size();
    }


    public static class BasicUserExtendInfoExcelListener extends AnalysisEventListener<BasicUserExtendInfoDTO> {

        private final List<BasicUserExtendInfoDTO> data = new ArrayList<>();

        @Override
        public void invoke(BasicUserExtendInfoDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<BasicUserExtendInfoDTO> getData() {
            return data;
        }
    }


}
