<script setup lang="ts">
import {
  WorkflowProps,
  WorkflowView,
} from 'lyra-workflow-component-vue3';
import Api from '/@/api';
import { ref } from 'vue';

const props = defineProps<{
  businessData: {
    id:string,
    name:string,
    [propName:string]:any
  }
}>();

const workflowProps:WorkflowProps = {
  Api,
  businessData: props.businessData,
};
const processRef = ref();
const init = () => {
  processRef.value.init();
};
defineExpose({
  init,
});

</script>

<template>
  <WorkflowView
    ref="processRef"
    :workflow-props="workflowProps"
  />
</template>
