<template>
  <Layout3
    v-if="tabsOption?.length>0"
    :defaultActionId="actionId"
    :menuData="tabsOption"
    :projectData="projectInfo"
    :type="2"
    @menuChange="contentTabsChange2"
  >
    <template #tabsRight>
      <RemoveBtn />
    </template>
    <!--  概述  -->
    <DetailsTab
      v-if="actionId===7771 && isPower('XQ_container_02', powerData)"
      :id="id"
      @editSuccess="editSuccess"
    />
    <!-- 关联内容 -->
    <ContactTab
      v-if="actionId===7772 && isPower('XQ_container_03', powerData)"
      :id="id"
    />
  </Layout3>
</template>

<script lang="ts">
import {
  defineComponent, reactive, toRefs, provide, readonly, ref, inject, onMounted, computed, getCurrentInstance,
} from 'vue';
// import { Layout2 } from '/@/components/Layout2.0';
import ContactTab from './contactTab/index.vue';
import {
  Layout3, isPower, useProjectPower,
} from 'lyra-component-vue3';
import DetailsTab from './DetailsTab/index.vue';
import { useRoute } from 'vue-router';
import RemoveBtn from '/@/views/pms/projectLaborer/componentsList/returnBtn/index.vue';
import { itemDetailsApi } from '/@/views/pms/projectLaborer/api/demandManagement';
import { setTitleByRootTabsKey } from '/@/utils';

export default defineComponent({
  // name: 'EndDetails',
  components: {
    Layout3,
    DetailsTab,
    ContactTab,
    RemoveBtn,
  },
  setup() {
    const route = useRoute();
    const state = reactive({
      tabsIndex: Number(route.query.type),
      id: route.query.id,
      projectId: route.query.projectId,
      projectInfo: {},
      className: '',
      actionId: 7771,
      // tabsOption: [
      //   { name: '概述' }, // 0
      //   { name: '关联内容' }, // 1
      //   // { name: '日志' }, // 2
      // ],
      powerData: [],
    });
    const internalInstance = getCurrentInstance();
    // 获取权限
    async function getProjectPower() {
      return new Promise((resolve, reject) => {
        useProjectPower(
          { pageCode: 'PMS0020' },
          (powerList) => {
            resolve(powerList || []);
          },
          internalInstance,
        );
      });
    }
    const state6 = reactive({
      tabsOption: [],
    });
    const getDetail = () => {
      const love = {
        id: state.id,
        className: 'DemandManagement',
        moduleName: '项目管理-需求管理-概述', // 模块名称
        type: 'GET', // 操作类型
        remark: `打开查看了需求管理【${state.id}】详情`,
      };
      itemDetailsApi(state.id, love)
        .then((res) => {
          if (res) {
            state.projectInfo = { ...res };
            setTitleByRootTabsKey(route?.query?.rootTabsKey, res.name);
          }
        })
        .catch(() => {});
    };
    onMounted(async () => {
      state.powerData = await getProjectPower();
      isPower('XQ_container_02', state.powerData) && state6.tabsOption.push({
        name: '概述',
        id: 7771,
      });
      isPower('XQ_container_03', state.powerData) && state6.tabsOption.push({
        name: '关联内容',
        id: 7772,
      });
      await getDetail();
    });
    function contentTabsChange2(index) {
      state.actionId = index.id;
    }
    // 权限分发
    provide(
      'powerData',
      computed(() => state.powerData),
    );
    /* 问题单条qusetionItemId和项目projectId */
    const demandItemId = ref(state.id);
    const projectId = ref(state.projectId);
    function editSuccess() {
      getDetail();
    }
    provide('demandItemId', readonly(demandItemId));
    provide('projectId', readonly(projectId));
    return {
      ...toRefs(state),
      ...toRefs(state6),
      contentTabsChange2,
      isPower,
      editSuccess,
    };
  },
});
</script>
