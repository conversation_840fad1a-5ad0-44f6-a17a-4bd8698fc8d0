package com.chinasie.orion.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.lock.LockInfo;
import com.baomidou.lock.LockTemplate;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.base.api.domain.entity.DeptDO;
import com.chinasie.orion.base.api.domain.entity.UserDO;
import com.chinasie.orion.base.api.domain.vo.SimpleUserVO;
import com.chinasie.orion.base.api.service.UserBaseApiService;
import com.chinasie.orion.bo.DataStatusNBO;
import com.chinasie.orion.bo.JobManageTreeBO;
import com.chinasie.orion.bo.OrionProductionConfig;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.*;
import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.dto.excel.JobManageWPStatusExcelTpl;
import com.chinasie.orion.domain.dto.job.*;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.domain.vo.count.DateComputeVO;
import com.chinasie.orion.domain.vo.job.*;
import com.chinasie.orion.domain.vo.jobDown.JobDownVO;
import com.chinasie.orion.domain.vo.tree.RelationOrgJobInfoVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.enums.StatusEnum;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.export.ImportExcelErrorNoteVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.BaseErrorCode;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.MyExceptionCode;
import com.chinasie.orion.feign.IcmJobFeignService;
import com.chinasie.orion.feign.dto.RepairJobManagerDTO;
import com.chinasie.orion.feign.vo.NewJobMangeVO;
import com.chinasie.orion.feign.vo.PageVO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileTreeVO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.file.api.service.FileApiService;
import com.chinasie.orion.handler.status.write.ExcelCellSelectWriterHandler;
import com.chinasie.orion.msc.api.MscBuildHandlerManager;
import com.chinasie.orion.mybatis.domain.entity.LyraEntity;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.JobManageMapper;
import com.chinasie.orion.repository.MajorRepairPlanMapper;
import com.chinasie.orion.repository.ProjectJobManageMapper;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.domain.vo.user.DeptUserRelationVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.domain.vo.user.UserBaseCacheVO;
import com.chinasie.orion.sdk.helper.*;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.*;
import com.chinasie.orion.tree.TreeNode;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.date.DateUtils;
import com.chinasie.orion.util.date.DateUtils;
import com.mzt.logapi.context.LogRecordContext;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.springframework.beans.BeanUtils.copyProperties;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/08/10:50
 * @description:
 */

@Service
@Slf4j
public class JobManageServiceImpl extends OrionBaseServiceImpl<JobManageMapper, JobManage> implements JobManageService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private BasePlaceService basePlaceService;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private UserBaseApiService userBaseApiService;

    @Autowired
    private JobManageMapper jobManageMapper;

    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Autowired
    private DictRedisHelper dictRedisHelper;

    @Autowired
    private FileApiService fileApiService;

    @Autowired
    private JobRiskService jobRiskService;

    @Autowired
    private LockTemplate lockTemplate;

    @Autowired
    private MajorRepairPlanMapper majorRepairPlanMapper;

    @Autowired
    private JobPackageService jobPackageService;


    @Autowired
    private JobStudyReviewService  jobStudyReviewService;

    @Autowired
    private MajorRepairPlanEconomizeService majorRepairPlanEconomizeService;

    @Autowired
    private MajorRepairPlanMeterReduceService majorRepairPlanMeterReduceService;

    @Autowired
    private StatusRedisHelper statusRedisHelper;

    @Autowired
    private JobManageService jobManageService;

    @Autowired
    private MajorRepairPlanService majorRepairPlanService;

    @Resource
    private ProjectJobManageMapper projectJobManageMapper;

    private MajorRepairPlanMemberService majorRepairPlanMemberService;

    @Autowired
    private ProjectSchemeApi2Service projectSchemeApi2Service;

    private JobNodeStatusService jobNodeStatusService;

    private JobMaterialService jobMaterialService;

    private JobPostAuthorizeService jobPostAuthorizeService;

    @Autowired
    @Lazy
    private JobProgressService jobProgressService;

    @Autowired
    private DeptUserHelper deptUserHelper;

    @Autowired
    private ImportantProjectService importantProjectService;

    @Autowired
    private CenterJobManageService centerJobManageService;

    @Autowired
    private RelationOrgToJobService relationOrgToJobService;

    @Autowired
    private  JobToSafetyQualityEnvService jobToSafetyQualityEnvService;

    @Autowired
    private  SafetyQualityEnvService safetyQualityEnvService;

    @Autowired
    public void setJobMaterialService(JobMaterialService jobMaterialService) {
        this.jobMaterialService = jobMaterialService;
    }

    @Autowired
    public void setJobPostAuthorizeService(JobPostAuthorizeService jobPostAuthorizeService) {
        this.jobPostAuthorizeService = jobPostAuthorizeService;
    }

    @Autowired
    public void setJobNodeStatusService(JobNodeStatusService jobNodeStatusService) {
        this.jobNodeStatusService = jobNodeStatusService;
    }

    @Autowired
    private MscBuildHandlerManager mscBuildHandlerManager;

    @Autowired
    private JobHeightRiskService jobHeightRiskService;

    @Autowired
    private IcmJobFeignService icmJobFeignService;

    @Autowired
    private MajorRepairOrgService majorRepairOrgService;

    @Autowired
    private FileApiService fileApi;

    @Autowired
    private DataStatusNBO dataStatusNBO;



    @Autowired
    public void setMajorRepairPlanMemberService(MajorRepairPlanMemberService majorRepairPlanMemberService) {
        this.majorRepairPlanMemberService = majorRepairPlanMemberService;
    }


    @Autowired
    private OrionProductionConfig orionProductionConfig;

    @Value("${orion.pms.major.role.mrc.code:mrc_lead}")
    private String roleCode;

    private String LOCKED_KEY ="pmsx::JobManage-init-data::locked::id";

    // 作业状态
    private final String[] phaseArr = {"NPLN", "ASGN", "INPL", "PLND", "RPLN", "PRWD", "APPV", "SCHD", "RTW", "WIP", "CSR", "CPL", "REJ"};


    @Override
    public Boolean editJob(JobManageDTO jobManageDTO) throws Exception {
        LambdaQueryWrapper<JobManage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(JobManage::getId, jobManageDTO.getId());
        JobManage jobManage = this.getOne(queryWrapper, false);
        if (jobManage == null) {
            throw new Exception("Record not found with id: " + jobManage.getId());
        }
        BeanCopyUtils.copyProperties(jobManageDTO,jobManage);

        LambdaQueryWrapper<MajorRepairPlanMember> queryWrapper1 = new LambdaQueryWrapper<>();
        queryWrapper1
                .eq(MajorRepairPlanMember::getLogicStatus,1)
                .eq(MajorRepairPlanMember::getMajorRepairTurn,jobManageDTO.getRepairRound())
                .eq(MajorRepairPlanMember::getUserId,CurrentUserHelper.getCurrentUserId());
        List<MajorRepairPlanMember> list = majorRepairPlanMemberService.list(queryWrapper1);
        if(list.isEmpty()){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "不是本轮大修指定成员无法修改");
        }
        boolean res = this.updateById(jobManage);
        if (!jobManage.getIsMajorProject()){
            jobManageMapper.deleteProjectJobRelationByJobId(jobManage.getId());
        }
        return res;
    }

    @Override
    public List<NodeInfoDTO> getProductionLifeCycle(String id) throws Exception {

        String json =  orionProductionConfig.getJobLifecycle();
        log.info("读取配置信息：【{}】",json);
        List<NodeInfoDTO> nodeInfoDTOList = JSONObject.parseArray(json,NodeInfoDTO.class);

        List<JobNodeStatusVO> jobNodeStatusList=  jobNodeStatusService.listByJobId(id);
        if(CollectionUtils.isEmpty(jobNodeStatusList)){
            return nodeInfoDTOList;
        }
        JobManage jobManage = this.getById(id);
        if(Objects.isNull(jobManage)){
            return nodeInfoDTOList;
        }
        Map<String,Boolean> phaseStatusMap = new HashMap<>();
        String status =jobManage.getBusStatus();
        if(Objects.equals(status, JobManageBusStatusEnum.PREPARE.getStatus().toString())){
            phaseStatusMap.put(JobManageBusStatusEnum.PREPARE.getCode(),true);
        }else if(Objects.equals(status, JobManageBusStatusEnum.READY_COMPLETE.getStatus().toString())){
            phaseStatusMap.put(JobManageBusStatusEnum.PREPARE.getCode(),true);
            phaseStatusMap.put(JobManageBusStatusEnum.READY_COMPLETE.getCode(),true);
        }else if(Objects.equals(status, JobManageBusStatusEnum.IMPL.getStatus().toString())){
            phaseStatusMap.put(JobManageBusStatusEnum.PREPARE.getCode(),true);
            phaseStatusMap.put(JobManageBusStatusEnum.READY_COMPLETE.getCode(),true);
            phaseStatusMap.put(JobManageBusStatusEnum.IMPL.getCode(),true);
        }else if(Objects.equals(status, JobManageBusStatusEnum.FINISH.getStatus().toString())){
            phaseStatusMap.put(JobManageBusStatusEnum.PREPARE.getCode(),true);
            phaseStatusMap.put(JobManageBusStatusEnum.READY_COMPLETE.getCode(),true);
            phaseStatusMap.put(JobManageBusStatusEnum.IMPL.getCode(),true);
            phaseStatusMap.put(JobManageBusStatusEnum.FINISH.getCode(),true);
        }
        String phase = jobManage.getPhase();
        List<String> allPhaseList =JobStatusEnum.phaseList(phase);

        JobNodeStatusVO jobNodeStatusVO =  jobNodeStatusList.get(0);
        String nodeKeyJson =jobNodeStatusVO.getNodeKeyJson();
        List<NodeInfoDTO> nodeInfoDTOS = new ArrayList<>();
        if(StringUtils.hasText(nodeKeyJson)){
            List<String> nodeKeyList =   JSONUtil.toList(nodeKeyJson,String.class);
            nodeInfoDTOList.forEach(item->{
                String code=   item.getCode();

                Boolean boo= phaseStatusMap.getOrDefault(code,false);
                item.setIsLightUp(boo);
                List<NodeInfoDTO.ChildrenDTO>  childrenDTOS=  item.getChildren();
                if(!CollectionUtils.isEmpty(childrenDTOS)){
                    setChiledMap(childrenDTOS,nodeKeyList);
                    item.setChildren(childrenDTOS);
                }
                List<Map<String,Boolean>> phaseMap=  item.getPhaseList();

                // 设置阶段标颜色
                List<Map<String,Boolean>> phaseMapListNew= setPhase(phaseMap,allPhaseList);
                item.setPhaseList(phaseMapListNew);
                nodeInfoDTOS.add(item);
            });
        }
        return  nodeInfoDTOS;
    }

    public void setChiledMap(List<NodeInfoDTO.ChildrenDTO> chiledMap,List<String> nodeList){
        chiledMap.forEach(item->{
            String code=   item.getCode();
            if(nodeList.contains(code)){
                item.setIsLightUp(true);
            }
        });
    }

    public List<Map<String,Boolean>> setPhase(List<Map<String,Boolean>> phaseMapList,List<String> allPhaseList){
        List<Map<String,Boolean>> phaseMapListNew = new ArrayList<>();
        for (Map<String, Boolean> phaseMap : phaseMapList) {
            for (Map.Entry<String, Boolean> stringBooleanEntry : phaseMap.entrySet()) {
                String phaseNode = stringBooleanEntry.getKey();
                if(allPhaseList.contains(phaseNode)){
                    phaseMap.put(phaseNode,true);
                }else{
                    phaseMap.put(phaseNode,false);
                }
            }
            phaseMapListNew.add(phaseMap);
        }
        return phaseMapListNew;
    }

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  JobManageVO detail(String id,String pageCode) throws Exception {
        JobManage jobManage =this.getById(id);
        cn.hutool.core.lang.Assert.isTrue(Objects.nonNull(jobManage),()-> new BaseException(MyExceptionCode.ERROR_PARAM));
        JobManageVO result = BeanCopyUtils.convertTo(jobManage,JobManageVO::new);
        result.setIsHighRisk(jobManage.getIsHighRisk());
        setEveryName(Collections.singletonList(result),jobManage.getRepairRound());
        String  repairRound = jobManage.getRepairRound();
        result.setAllJobList(this.getSimpListByUserAndRepairRound(jobManage.getRspUserId(),repairRound));
        jobNodeStatusService.setNodeStatus(id,Arrays.asList("jobDetail"));
        LogRecordContext.putVariable("number",jobManage.getNumber());
        return result;
    }


    @Override
    public  JobManageVO detailById(String id, String pageCode) throws Exception {
        JobManage jobManage =this.getById(id);
        cn.hutool.core.lang.Assert.isTrue(Objects.nonNull(jobManage),()-> new BaseException(MyExceptionCode.ERROR_PARAM));
        JobManageVO result = BeanCopyUtils.convertTo(jobManage,JobManageVO::new);
        result.setIsHighRisk(jobManage.getIsHighRisk());

        Map<String, String> firstMap = DictFirstExecuteEnum.map();
        result.setFirstExecuteName(firstMap.getOrDefault(result.getFirstExecute(),""));
        List<DictValueVO> importantProDict = dictRedisHelper.getDictListByCode(DictConts.IMPORTANT_PROJECT);
        Map<String, String> importantMap = importantProDict.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription));
        result.setImportantProjectName(importantMap.getOrDefault(result.getImportantProject(),""));
        return result;
    }


    private List<SimVO> getSimpListByUserAndRepairRound(String rspUserId, String repairRound) {
        LambdaQueryWrapperX<JobManage> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(JobManage.class);
        lambdaQueryWrapperX.eq(JobManage::getRspUserId,rspUserId);
        if(StringUtils.hasText(repairRound)){
            lambdaQueryWrapperX.eq(JobManage::getRepairRound,repairRound);
        }
        lambdaQueryWrapperX.eq(JobManage::getMatchUp,StatusEnum.ENABLE.getIndex());
//        lambdaQueryWrapperX.and(item->{
//            item.isNotNull(JobManage::getProjectNumber).ne(JobManage::getProjectNumber,"");
//        });
//        lambdaQueryWrapperX.and(item->{
//            item.isNotNull(JobManage::getPlanSchemeId).ne(JobManage::getPlanSchemeId,"");
//        });
//        lambdaQueryWrapperX.and(item->{
//            item.isNotNull(JobManage::getNOrO).ne(JobManage::getNOrO,"");
//        });
        lambdaQueryWrapperX.select(JobManage::getId,JobManage::getName,JobManage::getNumber);
        List<JobManage> jobManageList = this.list(lambdaQueryWrapperX);
        if(CollectionUtils.isEmpty(jobManageList)){
           return  new ArrayList<>();
        }
        return jobManageList.stream().map(jobManage -> {
            SimVO simVO = new SimVO();
            simVO.setId(jobManage.getId());
            simVO.setName(jobManage.getName());
            simVO.setNumber(jobManage.getNumber());
            return simVO;
        }).collect(Collectors.toList());
    }

    /**
     *  新增
     *
     * * @param jobManageDTO
     */
    @Override
    public  String create(JobManageDTO jobManageDTO) throws Exception {
        JobManage jobManage =BeanCopyUtils.convertTo(jobManageDTO,JobManage::new);

        String schemeId= jobManageDTO.getPlanSchemeId();
        if(isExist(jobManageDTO.getNumber(),null)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "该工单号已绑定计划，请核对");
        }
        String jobId = jobManage.getId();
        if(StringUtils.hasText(jobId)){
            this.updateById(jobManage);
        }else{
            jobManage.setBusStatus(JobManageBusStatusEnum.PREPARE.getStatus().toString());
            jobManage.setPhase(JobStatusEnum.NPLN.getKey());
            this.save(jobManage);
        }
        String rsp=jobManage.getId();
        // 如果是日常计划 -- 默认插入一条 研读审查
        if(Objects.equals(jobManage.getNOrO(),"N")){
            jobStudyReviewService.saveDefault(rsp);
        }

        // 默认插入状态
        Date  act=  jobManage.getActualBeginTime();
        Date  aed= jobManage.getActualEndTime();
        List<String> keyList = new ArrayList<>();
        if(!Objects.isNull(act)){
            keyList.add("jobActBeginTimeMaintenance");
        }
        if(!Objects.isNull(aed)){
            keyList.add("jobActEndTimeMaintenance");
        }
        if(!CollectionUtils.isEmpty(keyList)){
            jobNodeStatusService.setNodeStatus(jobManage.getId(),keyList);
        }
        return rsp;
    }


    public Boolean isExist(String number,String id ){
        LambdaQueryWrapperX<JobManage> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(JobManage.class);
        lambdaQueryWrapperX.eq(JobManage::getNumber,number);
        lambdaQueryWrapperX.isNotNull(JobManage::getProjectNumber);
        lambdaQueryWrapperX.isNotNull(JobManage::getNOrO);
        if(StringUtils.hasText(id)){
            lambdaQueryWrapperX.ne(JobManage::getId,id);
        }
        List<JobManage> list = this.list(lambdaQueryWrapperX);
        if(CollectionUtils.isEmpty(list)){
            return  Boolean.FALSE;
        }
        return  Boolean.TRUE;
    }

    /**
     *  编辑
     *
     * * @param jobManageDTO
     */
    @Override
    public Boolean edit(JobManageDTO jobManageDTO) throws Exception {
        JobManage jobManage =BeanCopyUtils.convertTo(jobManageDTO,JobManage::new);
        if(isExist(jobManageDTO.getNumber(),jobManageDTO.getId())){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "该工单号已绑定计划，请核对");
        }
        jobManage.setEndTime(this.setDateTime(jobManage.getWorkDuration(),jobManage.getBeginTime()));
//        if(isExist(jobManageDTO.getNumber(),jobManageDTO.getId())){
//            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "该工单号已绑定计划，请核对");
//        }
        this.updateById(jobManage);
        String rsp=jobManage.getId();

        Date  act=  jobManageDTO.getActualBeginTime();
        Date  aed= jobManageDTO.getActualEndTime();
        List<String> keyList = new ArrayList<>();
        if(!Objects.isNull(act)){
            keyList.add("jobActBeginTimeMaintenance");
        }
        if(!Objects.isNull(aed)){
            keyList.add("jobActEndTimeMaintenance");
        }
        if(!CollectionUtils.isEmpty(keyList)){
            jobNodeStatusService.setNodeStatus(jobManage.getId(),keyList);
        }
//        jobNodeStatusService.setNodeStatus(jobManage.getId(),keyList);
        List<ProjectJobIdsVO> res = jobManageMapper.selectIdsByProjectIdAndJobId(jobManageDTO.getId());
        //更新相关重大项目日期节点
        if (!CollectionUtils.isEmpty(res)){
            List<String> jobIds = res.stream().map(ProjectJobIdsVO::getJobId).collect(Collectors.toList());
            String projectId = res.get(0).getProjectId();
            DateComputeVO dateComputeVO = importantProjectService.computeDate(jobIds);
            LambdaUpdateWrapper<ImportantProject> updateWrapper = new LambdaUpdateWrapper<>(ImportantProject.class);
            updateWrapper.eq(ImportantProject::getId,projectId);
            updateWrapper.set(ImportantProject::getPlanEnd,dateComputeVO.getPlanEnd());
            updateWrapper.set(ImportantProject::getPlanStart,dateComputeVO.getPlanStart());
            updateWrapper.set(ImportantProject::getActureEnd,dateComputeVO.getActureEnd());
            updateWrapper.set(ImportantProject::getActureStart,dateComputeVO.getActureStart());
            importantProjectService.update(updateWrapper);
        }

        //获取相关班组id
        LambdaQueryWrapperX<RelationOrgToJob> wrapperX = new LambdaQueryWrapperX<>(RelationOrgToJob.class);
        wrapperX.eq(RelationOrgToJob::getJobNumber,jobManage.getNumber());
        wrapperX.select(RelationOrgToJob::getRepairOrgId);

        List<RelationOrgToJob> list = relationOrgToJobService.list(wrapperX);

        if (!CollectionUtils.isEmpty(list)&&list.size()==1){
            String repairOrgId = list.get(0).getRepairOrgId();
            //更新相关班组
            majorRepairOrgService.updateByOrgId(repairOrgId,jobManage.getRepairRound());
        }

        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        List<JobManage> jobManages = listByIds(ids);
        long count=  jobManageMapper.count(ids);
        if(count>0){
            throw  new BaseException(HttpStatus.BAD_REQUEST.value(), "所选作业已存在数据（人员、物资、高风险等信息），请清理后再试");
        }
        jobManages.forEach(x -> {
            x.setProjectName("");
            x.setProjectNumber("");
            x.setPlanSchemeId("");
            x.setPlanSchemeName("");
        });
        this.updateBatchById(jobManages);
        LogRecordContext.putVariable("numbers",jobManages.stream().map(JobManage::getNumber).collect(Collectors.joining(",")));
         return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<JobManageVO> pages( Page<JobManageDTO> pageRequest) throws Exception, IllegalAccessException, InvocationTargetException, NoSuchMethodException {

        LambdaQueryWrapperX<JobManage> condition = new LambdaQueryWrapperX<>( JobManage. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.distinct();
        condition.orderByDesc(JobManage::getProjectName).orderByDesc(JobManage::getPlanSchemeName).orderByDesc(JobManage::getCreateTime);
        JobManageDTO query=   pageRequest.getQuery();
        this.setCondition(condition,query);

        Page<JobManage> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), JobManage::new));

        PageResult<JobManage> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<JobManageVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());

        List<JobManageVO> vos = new ArrayList<>();
        List<String> userIdList = new ArrayList<>();
        for (JobManageVO vo : vos) {
            String personId = vo.getRspUserId();
            if(StringUtils.hasText(personId)){
                userIdList.add(personId);
            }
        }
        List<UserVO> userVOList = userRedisHelper.getUserByIds(userIdList);
        Map<String, String> userIdToName = userVOList.stream().collect(Collectors.toMap(UserVO::getId, UserVO::getName));
        for (JobManage jobManage : page.getContent()) {
            JobManageVO vo = new JobManageVO();
            // 复制所有属性
            copyProperties(jobManage, vo);
            // 手动复制特定字段
            vo.setRspUserName(userIdToName.getOrDefault(vo.getRspUserId(),""));
            vos.add(vo);
        }
        String repairRound=null;
        if(Objects.nonNull(query)){
            repairRound=query.getRepairRound();
        }
         setEveryName(vos,repairRound);

        pageResult.setContent(vos);

        return pageResult;
    }

    private void setCondition(LambdaQueryWrapperX<JobManage> condition, JobManageDTO query) {
        if(null != query){
            Boolean user= false;
            String keyword= query.getKeyword();
            if(StringUtils.hasText(keyword)){
                condition.leftJoin(UserDO.class, "u", UserDO::getId, JobManage::getRspUserId);
                condition.and(item->{
                    item.like(JobManage::getNumber,keyword)
                            .or().like(JobManage::getName,keyword).or().like(JobManage::getProjectName,keyword)
                            .or().like(JobManage::getPlanSchemeName,keyword);
                    item.or().like(UserDO::getName,keyword);
                });
                user= true;
            }
            if(StringUtils.hasText(query.getBusStatus())){
                condition.eq(JobManage::getBusStatus, query.getBusStatus());
            }
            if(StringUtils.hasText(query.getNOrO())){
                if(Objects.equals("O",query.getNOrO())){
                    condition.in(JobManage::getNOrO,Arrays.asList("O","T","P"));
                }
                if(Objects.equals("N",query.getNOrO())){
                    condition.in(JobManage::getNOrO,Arrays.asList("N","M"));
                }
            }
            if(StringUtils.hasText(query.getRepairRound())){
                condition.eq(JobManage::getRepairRound,query.getRepairRound());
            }
            if(StringUtils.hasText(query.getPlanSchemeId())){
                condition.eq(JobManage::getPlanSchemeId,query.getPlanSchemeId());
            }
            if(StringUtils.hasText(query.getProjectNumber())){
                condition.eq(JobManage::getProjectNumber,query.getProjectNumber());
            }
            if(StringUtils.hasText(query.getRspUserId())){
                condition.eq(JobManage::getRspUserId,query.getRspUserId());
            }
            List<String> sqlConditionList = query.getSqlConditionList();
            if(!CollectionUtils.isEmpty(sqlConditionList)){
                Boolean riskLevel =  sqlConditionList.stream().anyMatch(item-> item.contains("jh."));
                if(riskLevel){
                    condition.leftJoin(JobHeightRisk.class, "jh", JobHeightRisk::getJobNumber, JobManage::getNumber);
                }
                Boolean dept =  sqlConditionList.stream().anyMatch(item-> item.contains("d."));
                if(dept){
                    condition.leftJoin(DeptDO.class, "d", DeptDO::getId, JobManage::getRspDept);
                }
                Boolean haveUser =  sqlConditionList.stream().anyMatch(item-> item.contains("u."));
                if(!user && haveUser){
                    condition.leftJoin(UserDO.class, "u", UserDO::getId, JobManage::getRspUserId);
                }
//                Boolean newer =  sqlConditionList.stream().anyMatch(item-> item.contains("newer."));
//                if(newer){
//                    condition.leftJoin(JobPostAuthorize.class, "job", JobPostAuthorize::getJobId, JobManage::getId);
//                    condition.leftJoin(PersonMange.class, "newer", PersonMange::getId,JobPostAuthorize::getPersonManageId);
//                }
                sqlConditionList.forEach(item->{
                    condition.apply(String.format("  %s",item));
                });
            }
        }
        condition.eq(JobManage::getMatchUp, StatusEnum.ENABLE.getIndex());
//        condition.and(item->{
//            item.isNotNull(JobManage::getProjectNumber).ne(JobManage::getProjectNumber,"");
//        });
//        condition.and(item->{
//            item.isNotNull(JobManage::getPlanSchemeId).ne(JobManage::getPlanSchemeId,"");
//        });
//        condition.and(item->{
//            item.isNotNull(JobManage::getNOrO).ne(JobManage::getNOrO,"");
//        });
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        String fileName = "作业管理导入模板.xlsx";
        response.reset();
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));

        ExcelUtils.writeTemplate(response, JobManageDTO.class, fileName, "作业管理导入模板", "作业管理导入模板");
    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcelNew(MultipartFile file,String repairRound) throws IOException {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = file.getInputStream();
        JobManageExcelListener excelReadListener = new JobManageExcelListener();
        EasyExcel.read(inputStream,JobManageDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<JobManageDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        List<ImportExcelErrorNoteVO> errorNotes = new ArrayList<>();
        for (int i = 0; i < dtoS.size(); i++) {
            if (!StringUtils.hasText(dtoS.get(i).getNumber())||dtoS.get(i).getNumber().length()!=12){
                ImportExcelErrorNoteVO importExcelErrorNoteVO = new ImportExcelErrorNoteVO();
                importExcelErrorNoteVO.setOrder(String.valueOf(i+1));
                importExcelErrorNoteVO.setErrorNotes(Collections.singletonList("工单号不能为空或者工单号长度不正确！"));
                errorNotes.add(importExcelErrorNoteVO);
            }
        }

        if (errorNotes.size()>0){
            result.setCode(400);
            result.setOom("工单号");
            result.setErr(errorNotes);
            return result;
        }

        //工单数据重复校验以及状态设置
        log.info("作业管理导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        errorNotes = checkExcelDataNew(dtoS,repairRound);


        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::JobManage-import::id", importId, dtoS, 24 * 60 * 60);
        if (!CollectionUtils.isEmpty(errorNotes)){
            result.setCode(500);
            result.setErr(errorNotes);
        }else {
            result.setCode(200);

        }
        result.setSucc(importId);
        return result;
    }

    private List<ImportExcelErrorNoteVO> checkExcelDataNew(List<JobManageDTO> dtoS,String repairRound) {
        List<ImportExcelErrorNoteVO> res = new ArrayList<>();
        Map<String,Integer> numberToIndex = new HashMap<>();

        LambdaQueryWrapperX<JobManage> wrapperX = new LambdaQueryWrapperX<>(JobManage.class);
        wrapperX.select(JobManage::getNumber);
        wrapperX.isNotNull(JobManage::getRepairOrgId);
        List<JobManage> list = list(wrapperX);
        List<String> numbers = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)){
            numbers = list.stream().map(JobManage::getNumber).collect(Collectors.toList());
        }

        List<String> jobNumbers = dtoS.stream().map(JobManageDTO::getNumber).collect(Collectors.toList());
        //调用三方接口获取总工单
        RepairJobManagerDTO repairJobManagerDTO = new RepairJobManagerDTO();
        repairJobManagerDTO.setRepairRound(repairRound);
        repairJobManagerDTO.setPageSize(9999);
        repairJobManagerDTO.setJobNumberList(jobNumbers);
        ResponseDTO<PageVO<NewJobMangeVO>> pageVOResponseDTO;

        //字典
        List<DictValueVO> firstExecute = dictRedisHelper.getByDictNumber(DictConstant.PMS_FIRST_EXECUTE, CurrentUserHelper.getOrgId());
        Map<String, String> firstExecuteMap = firstExecute.stream().collect(Collectors.toMap(DictValueVO::getDescription,DictValueVO::getValue));

        List<DictValueVO>  antiForfeignLevelDic = dictRedisHelper.getByDictNumber(DictConts.DUST_PROTECTION_LEVEL, CurrentUserHelper.getOrgId());
        Map<String, String>   antiForfeignLevelNameMap= antiForfeignLevelDic.stream().collect(Collectors.toMap(DictValueVO::getDescription,DictValueVO::getValue));


        List<String> numberList = new ArrayList<>();
        try {
            log.info("开始调用三方接口");
            log.info("调用参数{}", JSONUtil.toJsonStr(repairJobManagerDTO));
            pageVOResponseDTO = icmJobFeignService.jobManageList(repairJobManagerDTO);
            log.info("三方接口返回数据={}", JSONUtil.toJsonStr(pageVOResponseDTO));
            List<NewJobMangeVO> thirdJobList = pageVOResponseDTO.getResult().getContent();
            numberList = thirdJobList.stream().map(NewJobMangeVO::getNumber).collect(Collectors.toList());
            Map<String, NewJobMangeVO> numberToEntity = thirdJobList.stream().collect(Collectors.toMap(NewJobMangeVO::getNumber, Function.identity()));
            log.info("开始copy properties");


            for (JobManageDTO dto : dtoS) {
                if (numberToEntity.containsKey(dto.getNumber())){
                    NewJobMangeVO newJobMangeVO = numberToEntity.getOrDefault(dto.getNumber(),null);
                    if (Objects.nonNull(newJobMangeVO)){
                        log.info("copy前dto数据:{}", JSONUtil.toJsonStr(dto));
                        log.info("copy前vo数据:{}", JSONUtil.toJsonStr(newJobMangeVO));

                        dto.setAntiForfeignLevel(antiForfeignLevelNameMap.getOrDefault(dto.getAntiForfeignLevel(),""));
                        dto.setFirstExecute(firstExecuteMap.getOrDefault(dto.getFirstExecute(), ""));
                        dto.setName(newJobMangeVO.getName());
                        if ("是".equals(dto.getHighRiskName())){
                            dto.setIsHighRisk(true);
                        }else{
                            dto.setIsHighRisk(false);
                        }
                        dto.setNOrO(newJobMangeVO.getNOrO());
                        dto.setWorkCenter(newJobMangeVO.getWorkCenter());
                        dto.setRepairRound(newJobMangeVO.getRepairRound());
                        dto.setRspDept(newJobMangeVO.getRspDept());
                        dto.setJobBaseName(newJobMangeVO.getJobBaseName());
                        dto.setPhase(newJobMangeVO.getPhase());

                        log.info("copy后数据:{}", JSONUtil.toJsonStr(dto));
                        //如果计划结束时间和计划开始时间不为空 且工期为空，则计算工期
                        if (Objects.nonNull(dto.getBeginTime())&&Objects.nonNull(dto.getEndTime())&&dto.getWorkDuration()==null){
                            long diff = Math.abs(dto.getEndTime().getTime() - dto.getBeginTime().getTime());
                            //转换为天数
                            long diffInDays = diff / (24 * 60 * 60 * 1000);
                            dto.setWorkDuration(Integer.valueOf(String.valueOf(diffInDays)));
                        }
                        //如果工期不为空 计算计划结束时间
                        if (Objects.nonNull(dto.getBeginTime())&&dto.getWorkDuration() != null) {
                            dto.setEndTime(DateUtil.offsetDay(dto.getBeginTime(),dto.getWorkDuration()));
                        }

                    }
                }
            }
        }catch (Exception e){
            log.error("三方接口调用失败！");
            numberList.addAll(jobNumbers);
            for (JobManageDTO dto : dtoS) {
                BeanUtils.copyProperties(dto,dto);
            }
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR.getErrorCode(),"三方接口调用失败");
        }



        for (int i = 0; i < dtoS.size(); i++) {
            ImportExcelErrorNoteVO resultVO = new ImportExcelErrorNoteVO();
            List<String> errorNote = new ArrayList<>();
            JobManageDTO jobManageDTO = dtoS.get(i);
            if(-1 == numberToIndex.getOrDefault(jobManageDTO.getNumber(),-1)){
                numberToIndex.put(jobManageDTO.getNumber(),i+1);
            }else {
                int index = numberToIndex.get(jobManageDTO.getNumber());
                errorNote.add("第"+(i+1)+"行和第"+index+"行工单号重复，导入保留最下方数据");
                dtoS.remove(index);
            }

            if (numbers.contains(jobManageDTO.getNumber())){
                errorNote.add("第"+(i+1)+"行工单已被使用,工单号为:"+jobManageDTO.getNumber());
                dtoS.remove(i);
                continue;
            }

            if (numberList.contains(jobManageDTO.getNumber())){
                jobManageDTO.setMatchUp(1);
            }else {
                jobManageDTO.setMatchUp(0);
            }
            if(!CollectionUtils.isEmpty(errorNote)){
                resultVO.setOrder(String.valueOf(i));
                resultVO.setErrorNotes(errorNote);
                res.add(resultVO);
            }
        }

        return res;
    }

    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel,String planSchemeId) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        JobManageExcelListener excelReadListener = new JobManageExcelListener();
        EasyExcel.read(inputStream,JobManageDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<JobManageDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        //数据必填校验
        log.info("作业管理导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ImportExcelErrorNoteVO> errorNotes = checkExcelData(dtoS);
        if (!CollectionUtils.isEmpty(errorNotes)){
            result.setCode(200);
            result.setErr(errorNotes);
            return result;
        }
        //数据业务校验
        List<ImportExcelErrorNoteVO> importExcelErrorNoteVOS = checkExcelDataValid(dtoS, planSchemeId);
        if (!CollectionUtils.isEmpty(importExcelErrorNoteVOS)){
            result.setCode(200);
            result.setErr(importExcelErrorNoteVOS);
            return result;
        }
        dtoS.forEach(item->{
            if ("是".equals(item.getHighRiskName())){
                item.setIsHighRisk(true);
            }else {
                item.setIsHighRisk(false);
            }
        });
        List<JobManage> jobManages =BeanCopyUtils.convertListTo(dtoS,JobManage::new);
        JobManageImportVO jobManageImportVO = projectJobManageMapper.getJobManageImportVO(planSchemeId);
        List<DictValueVO> byDictNumber = dictRedisHelper.getByDictNumber("pms_important_project", CurrentUserHelper.getOrgId());
        Map<String, String> descToValue = byDictNumber.stream().collect(Collectors.toMap(DictValueVO::getDescription, DictValueVO::getValue));
        jobManages.forEach(item->{
            item.setPlanSchemeId(planSchemeId);
            item.setProjectNumber(jobManageImportVO.getProjectNumber());
            item.setProjectName(jobManageImportVO.getProjectName());
            item.setPlanSchemeName(jobManageImportVO.getSchemeName());
            item.setJobBase(jobManageImportVO.getBasePlace());
            item.setJobBaseName(jobManageImportVO.getBasePlaceName());
            item.setNOrO(jobManageImportVO.getWorkContent());
            item.setRepairRound(jobManageImportVO.getRepairRound());
            //封装重要项目code
            if(StringUtils.hasText(item.getImportantProject())){
                item.setImportantProject(descToValue.getOrDefault(item.getImportantProject(),""));
            }
        });
        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::JobManage-import::id", importId, jobManages, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }

    public List<ImportExcelErrorNoteVO> checkExcelDataValid(List<JobManageDTO> data,String planSchemeId){
        List<ImportExcelErrorNoteVO> importExcelErrorNoteVOS = new ArrayList<>();
        List<String> numbers = new ArrayList<>();
        List<String> userCodes = new ArrayList<>();

        data.forEach(item->{
            if (StringUtils.hasText(item.getNumber())){
                numbers.add(item.getNumber());
            }
            if (StringUtils.hasText(item.getRspUserCode())){
                userCodes.add(item.getRspUserCode());
            }
        });

        List<SimpleUser> simpleUserByCode = userRedisHelper.getSimpleUserByCode(userCodes);
        LambdaQueryWrapperX<JobManage> wrapperX = new LambdaQueryWrapperX<>(JobManage.class);
        wrapperX.in(JobManage::getNumber,numbers);
        wrapperX.select(JobManage::getPlanSchemeId,JobManage::getNumber,JobManage::getNOrO,JobManage::getPlanSchemeId,JobManage::getRepairRound,JobManage::getProjectNumber);
        //判断所需数据
        List<JobManage> list = this.list(wrapperX);
        String repairRound = projectJobManageMapper.getIdToRepairRound(planSchemeId);
        Map<String, String> typeToBase = projectJobManageMapper.getTypeToBase(planSchemeId);
        if (Objects.isNull(typeToBase)){
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_QUERY_ENTITY_NULL.getErrorCode(),"项目计划工作内容和基地为空！");
        }
        Map<String, String> userCodeToName = new HashMap<>();
        if (!simpleUserByCode.isEmpty()){
            userCodeToName = simpleUserByCode.stream().collect(Collectors.toMap(SimpleUser::getCode, SimpleUser::getName));
        }
        Map<String, JobManage> numberToJobManage = list.stream().filter(item -> StringUtils.hasText(item.getNumber()))
                .collect(Collectors.toMap(JobManage::getNumber, Function.identity(),(existing, replacement) -> existing));

        for (int i = 0; i < data.size(); i++) {
            ImportExcelErrorNoteVO importExcelErrorNoteVO = new ImportExcelErrorNoteVO();
            List<String> errorNotes = new ArrayList<>();
            JobManageDTO jobManage = data.get(i);
            //获取相对应的JobManage
            JobManage orDefault = numberToJobManage.getOrDefault(jobManage.getNumber(), null);
            if (orDefault == null){
                errorNotes.add("作业编号不存在！");
                importExcelErrorNoteVO.setOrder(String.valueOf(i + 1));
                importExcelErrorNoteVO.setErrorNotes(errorNotes);
                importExcelErrorNoteVOS.add(importExcelErrorNoteVO);
                continue;
            }else {
                if (StringUtils.hasText(orDefault.getPlanSchemeId())&&StringUtils.hasText(orDefault.getProjectNumber())){
                    errorNotes.add("工单已被使用！");
                    importExcelErrorNoteVO.setOrder(String.valueOf(i + 1));
                    importExcelErrorNoteVO.setOrder(String.valueOf(i + 1));
                    importExcelErrorNoteVO.setErrorNotes(errorNotes);
                    importExcelErrorNoteVOS.add(importExcelErrorNoteVO);
                    continue;
                }
            }

            String nOrO = orDefault.getNOrO();
            List<String> repair = Arrays.asList("O","T","P");
            List<String> daily = Arrays.asList("N","M");
            //判断计划和作业是否都是大修或者日常
            if (typeToBase.containsValue("pms_major_repair_con")&&(!StringUtils.hasText(nOrO)||repair.contains(nOrO))){
//                if (!StringUtils.hasText(repairRound)){
//                    errorNotes.add("导入作业大修轮次为空！");
//                }
                if(StringUtils.hasText(repairRound)&&!repairRound.equals(orDefault.getRepairRound())){
                    errorNotes.add("导入作业的大修轮次与项目计划的大修轮次不一致！");
                }
            }else if(typeToBase.containsValue("pms_daily_con")&&(!StringUtils.hasText(nOrO)||daily.contains(nOrO))){
                if (StringUtils.hasText(orDefault.getJobBase())&&!typeToBase.getOrDefault("pms_daily_con","").equals(orDefault.getJobBase())){
                    errorNotes.add("导入作业基地与项目计划基地不一致！");
                }
            }else{
                errorNotes.add("导入作业的作业内容和计划内容不一致！");
            }

            if (!userCodeToName.isEmpty()){
                if (!userCodeToName.getOrDefault(jobManage.getRspUserCode(),"").equals(jobManage.getRspUserName())){
                    errorNotes.add("负责人工号与姓名不匹配！");
                }
            }else {
                errorNotes.add("负责人工号不存在！");
            }

            if(!errorNotes.isEmpty()){
                importExcelErrorNoteVO.setOrder(String.valueOf(i + 1));
                importExcelErrorNoteVO.setErrorNotes(errorNotes);
                importExcelErrorNoteVOS.add(importExcelErrorNoteVO);
            }

        }
        return importExcelErrorNoteVOS;
    }

    public List<ImportExcelErrorNoteVO> checkExcelData(List<JobManageDTO> data){
        List<ImportExcelErrorNoteVO> excelErrorNoteVOS = new ArrayList<>();
        for (int i = 0; i < data.size(); i++) {
            JobManageDTO item = data.get(i);
            ImportExcelErrorNoteVO excelErrorNoteVO = new ImportExcelErrorNoteVO();
            List<String> errorNotes = new ArrayList<>();
            if (!StringUtils.hasText(item.getNumber())){
                errorNotes.add("工单号不能为空！");
            }

            if (!StringUtils.hasText(item.getHighRiskName())){
                errorNotes.add("是否高风险字段不能为空！");
            }

            if (!StringUtils.hasText(item.getRspUserCode())){
                errorNotes.add("负责人工号不能为空！");
            }

            if(!StringUtils.hasText(item.getRspUserName())){
                errorNotes.add("负责人姓名不能为空！");
            }

            if (Objects.isNull(item.getBeginTime())){
                errorNotes.add("计划开工时间不能为空！");
            }

            if (item.getWorkDuration()==null){
                errorNotes.add("计划工期不能为空！");
            }
            if (!errorNotes.isEmpty()){
                excelErrorNoteVO.setErrorNotes(errorNotes);
                excelErrorNoteVO.setOrder(String.valueOf(i+1));
            }
        }
        return excelErrorNoteVOS;
    }

    @Override
    public List<JobManageTreeBO> importByExcelNew(String importId, String repairRound) {
        List<JobManageDTO> jobManages = (List<JobManageDTO>) orionJ2CacheService.get("pmsx::JobManage-import::id", importId);

        List<JobManageTreeBO> param;
        param = BeanCopyUtils.convertListTo(jobManages, JobManageTreeBO::new);
        List<String> codes = param.stream().map(JobManageTreeBO::getRspUserCode).collect(Collectors.toList());
        List<SimpleUser> userBaseCacheByCode = userRedisHelper.getSimpleUserByCode(CurrentUserHelper.getOrgId(),codes);
        Map<String, SimpleUser> codeToEntity = userBaseCacheByCode.stream().collect(Collectors.toMap(SimpleUser::getCode, Function.identity(), (v1, v2) -> v2));
        param.forEach(item->{
            SimpleUser simpleUser = codeToEntity.get(item.getRspUserCode());
            if(Objects.nonNull(simpleUser)){
                item.setRspUserId(simpleUser.getId());
                item.setRspDept(simpleUser.getOrgId());
                item.setRspUserName(simpleUser.getName());
            }
        });


        List<String> jobNumbers = jobManages.stream().map(JobManageDTO::getNumber).collect(Collectors.toList());

        List<JobManageTreeVO> tree = centerJobManageService.getTree(param);
        return param;
    }

    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<JobManage> jobManagees = (List<JobManage>) orionJ2CacheService.get("pmsx::JobManage-import::id", importId);
        List<String> numbers = new ArrayList<>();
        jobManagees.forEach(item->{
            numbers.add(item.getNumber());
        });
        LambdaQueryWrapperX<JobManage> wrapperX = new LambdaQueryWrapperX<>(JobManage.class);
        wrapperX.in(JobManage::getNumber,numbers);
        List<JobManage> jobManageList = this.list(wrapperX);
        Map<String, JobManage> numberToJobManage = jobManagees.stream().collect(Collectors.toMap(JobManage::getNumber, Function.identity()));
        List<String> userCodes = jobManagees.stream().map(JobManage :: getRspUserCode).distinct().collect(Collectors.toList());
        List<UserBaseCacheVO> simpleUsers =  userRedisHelper.getUserBaseCacheByCode(CurrentUserHelper.getOrgId(),userCodes);
        simpleUsers.stream().collect(Collectors.toMap(UserBaseCacheVO :: getId, UserBaseCacheVO:: getCode));

        UserBaseCacheVO userBase = orionJ2CacheService.get("pmi::user-base::code", "P638379", UserBaseCacheVO::new);
        if (Objects.nonNull(userBase)) {
            log.error("###################" + JSONObject.toJSONString(userBase));
            String userId = userBase.getId();
            List<DeptUserRelationVO> deptUserRelationList = deptUserHelper.getDeptUserRelationByUserId(CurrentUserHelper.getOrgId(), userId);
            log.error("--------------" + JSONObject.toJSONString(deptUserRelationList));
        }
        List<DictValueVO> dictValueVOList = dictRedisHelper.getByDictNumber("pms_dust_protection_level", CurrentUserHelper.getOrgId());
        Map<String, String> desToNumber = dictValueVOList.stream().collect(Collectors.toMap(DictValueVO::getDescription, DictValueVO::getValue));

        jobManageList.forEach(item->{
            JobManage orDefault = numberToJobManage.getOrDefault(item.getNumber(), null);
            item.setNumber(orDefault.getNumber());
            item.setPlanSchemeId(orDefault.getPlanSchemeId());
            item.setProjectNumber(orDefault.getProjectNumber());
            item.setProjectName(orDefault.getProjectName());
            item.setPlanSchemeName(orDefault.getPlanSchemeName());
            item.setIsHighRisk(orDefault.getIsHighRisk());
            if (StringUtils.hasText(orDefault.getAntiForfeignLevel())){
                String anti = desToNumber.getOrDefault(orDefault.getAntiForfeignLevel(), "");
                if (StringUtils.hasText(anti)){
                    item.setAntiForfeignLevel(anti);
                }else{
                    item.setAntiForfeignLevel(orDefault.getAntiForfeignLevel());
                }
            }
            if (StringUtils.hasText(orDefault.getFirstExecute())){
                item.setFirstExecute(orDefault.getFirstExecute());
            }
            if(StringUtils.hasText(orDefault.getImportantProject())){
                item.setImportantProject(orDefault.getImportantProject());
            }

            if (!StringUtils.hasText(item.getNOrO())){
                item.setNOrO(orDefault.getNOrO());
            }
            if (!StringUtils.hasText(item.getJobBase())){
                item.setJobBase(orDefault.getJobBase());
                item.setJobBaseName(orDefault.getJobBaseName());
            }
            if (!StringUtils.hasText(item.getRepairRound())){
                item.setRepairRound(orDefault.getRepairRound());
            }
            item.setRspUserCode(orDefault.getRspUserCode());
            SimpleUser simpleUserByCode = userRedisHelper.getSimpleUserByCode(orDefault.getRspUserCode());

            log.error("11111111111111111111111111111111111111"+JSONObject.toJSONString(simpleUserByCode));
            item.setRspUserId(simpleUserByCode.getId());
            item.setRspDept(simpleUserByCode.getOrgId());
            //根据计划开始日期和计划工期计算计划截止日期
            Date beginTime = orDefault.getBeginTime();
            Integer workDuration = orDefault.getWorkDuration();
            item.setBeginTime(beginTime);
            item.setWorkDuration(workDuration);
         //   Date date = DateUtils.addDate(beginTime, Calendar.DATE, workDuration);
            Date date = DateUtil.offsetDay(beginTime, workDuration);
            item.setEndTime(date);
            item.setCreateTime(new Date());
            item.setModifyTime(new Date());
            item.setOwnerId(CurrentUserHelper.getCurrentUserId());
            item.setCreatorId(CurrentUserHelper.getCurrentUserId());
            item.setModifyId(CurrentUserHelper.getCurrentUserId());
            //根据首次执行描述获取code
            if (StringUtils.hasText(orDefault.getFirstExecute())){
                item.setFirstExecute(FirstExecuteConstant.getCode(orDefault.getFirstExecute()));
            }
        });
        log.info("作业管理导入的入库数据={}", JSONUtil.toJsonStr(jobManageList));

        this.updateBatchById(jobManageList);
        orionJ2CacheService.delete("pmsx::JobManage-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::JobManage-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(JobExportExcelParamDTO jobExportExcelParamDTO, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<JobManage> condition = new LambdaQueryWrapperX<>( JobManage. class);
        condition.eq(JobManage::getRepairRound,jobExportExcelParamDTO.getRepairRound());
        if(!CollectionUtils.isEmpty(jobExportExcelParamDTO.getIdList())){
            condition.in(JobManage::getId,jobExportExcelParamDTO.getIdList());
        }
        condition.eq(JobManage::getMatchUp,StatusEnum.ENABLE.getIndex());
//        condition.and(item->{
//            item.isNotNull(JobManage::getProjectNumber).ne(JobManage::getProjectNumber,"");
//        });
//        condition.and(item->{
//            item.isNotNull(JobManage::getPlanSchemeId).ne(JobManage::getPlanSchemeId,"");
//        });
//        condition.and(item->{
//            item.isNotNull(JobManage::getNOrO).ne(JobManage::getNOrO,"");
//        });
        condition.eq(JobManage::getMatchUp, StatusEnum.ENABLE.getIndex());
        List<JobManage> jobManagees =   this.list(condition);
        List<JobManageVO> voList = BeanCopyUtils.convertListTo(jobManagees, JobManageVO::new);
        this.setEveryName(voList, jobExportExcelParamDTO.getRepairRound());
        List<JobExportVO> jobExportVOList= new ArrayList<>();
        AtomicInteger serialNumber = new AtomicInteger(1);
        voList.forEach(item->{
            JobExportVO jobExportVO =   BeanCopyUtils.convertTo(item,JobExportVO::new);
            jobExportVO.setSerialNumber(serialNumber.get());
            jobExportVO.setHighRiskLevel(item.getHeightRiskName());
            // 系统条件
            jobExportVO.setSystemCondition(item.getNOrO());
            // 功能位置
            jobExportVO.setFunctionalLocation(item.getFunctionalLocation());
            // 进展阶段
            if(Objects.nonNull(item.getDataStatus())){
                jobExportVO.setStatusName(item.getDataStatus().getName());
            }
            jobExportVO.setImportantProject(item.getImportantProjectName());
            jobExportVO.setAntiForfeignLevel(item.getAntiForfeignLevelName());
            jobExportVO.setFirstExecute(item.getFirstExecuteName());
            jobExportVO.setNewParticipants(Objects.isNull(item.getNewParticipants())?"":Objects.equals(item.getNewParticipants(),true)? "是":"否");
            jobExportVO.setIsCarryTool(Objects.isNull(item.getIsCarryTool())?"":Objects.equals(item.getIsCarryTool(),true)? "是":"否");
            jobExportVO.setIsMajorProject(Objects.isNull(item.getIsMajorProject())?"":Objects.equals(item.getIsMajorProject(),true)? "是":"否");
            jobExportVO.setIsHighRisk(Objects.isNull(item.getIsHighRisk())?"":Objects.equals(item.getIsHighRisk(),true)? "是":"否");
            // 工作包审查状态
            jobExportVO.setStudyExamineStatus(item.getStudyExamineStatusName());
            // 时间： 计划开始，计划结束 ，实际开始 ，实际结束
            jobExportVO.setActualBeginTime(DateUtil.format(item.getActualBeginTime(),"yyyy-MM-dd"));
            jobExportVO.setActualEndTime(DateUtil.format(item.getActualEndTime(),"yyyy-MM-dd"));
            jobExportVO.setBeginTime(DateUtil.format(item.getBeginTime(),"yyyy-MM-dd"));
            jobExportVO.setEndTime(DateUtil.format(item.getEndTime(),"yyyy-MM-dd"));
            jobExportVOList.add(jobExportVO);
            serialNumber.getAndIncrement();
        });
        String fileName = "作业管理数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", JobExportVO.class,jobExportVOList );
    }

    /**
     * 作业管理 更新作业状态（phase） excel 导入模板
     *
     * @param response 下载到浏览器
     */
    @Override
    public void downloadWPExcelTpl(HttpServletResponse response) throws Exception {
        String fileName = "作业状态导入模板.xlsx";
        response.reset();
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));

        EasyExcel.write(response.getOutputStream(), JobManageWPStatusExcelTpl.class)
                .registerWriteHandler(new ExcelCellSelectWriterHandler(1, 100, 1, 1, phaseArr))
                .sheet("sheet1").doWrite(List.of());
    }

    /**
     * 作业管理 更新作业状态（phase） excel 导入
     *
     * @param excel       excel文件
     * @param repairRound 和大修主表的关联字段
     * @return ImportExcelCheckResultVO
     */
    @Override
    public ImportExcelCheckResultVO importCheckByWPExcel(MultipartFile excel, String repairRound) throws Exception {
        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();

        InputStream inputStream = excel.getInputStream();
        JobManageWPStatusExcelTplListener excelReadListener = new JobManageWPStatusExcelTplListener();
        EasyExcel.read(inputStream, JobManageWPStatusExcelTpl.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<JobManageWPStatusExcelTpl> dtoS = excelReadListener.getData();

        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (!StringUtils.hasText(repairRound)) {
            result.setCode(400);
            result.setOom("参数错误，请传入大修轮次，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }

        final List<String> phaseList = List.of(phaseArr);

        final List<String> numbers = Lists.newArrayList();

        final List<ImportExcelErrorNoteVO> err = Lists.newArrayList();
        ImportExcelErrorNoteVO en;
        for (int i = 0; i < dtoS.size(); i++) {
            String order = String.valueOf(i + 2);
            JobManageWPStatusExcelTpl e = dtoS.get(i);

            if (!StringUtils.hasText(e.getNumber())) {
                en = new ImportExcelErrorNoteVO();
                en.setOrder(order);
                en.setErrorNotes(List.of("第" + order + "作业工单号不能为空"));
                err.add(en);
            }

            if (!StringUtils.hasText(e.getPhase())) {
                en = new ImportExcelErrorNoteVO();
                en.setOrder(order);
                en.setErrorNotes(List.of("第" + order + "作业状态不能为空"));
                err.add(en);
            } else if (!phaseList.contains(e.getPhase())) {
                en = new ImportExcelErrorNoteVO();
                en.setOrder(order);
                en.setErrorNotes(List.of("第" + order + "作业状态错误，未在配置中找到"));
                err.add(en);
            }

            numbers.add(e.getNumber());
        }

        // 单号不可重复
        if (numbers.stream().distinct().count() != numbers.size()) {
            en = new ImportExcelErrorNoteVO();
            en.setOrder("1");
            en.setErrorNotes(List.of("作业工单号不能重复，请您检查"));
            err.add(en);
        }

        final LambdaQueryWrapperX<JobManage> query = new LambdaQueryWrapperX<>(JobManage.class);
        query.in(JobManage::getNumber, numbers.stream().distinct().collect(Collectors.toList()))
                .eq(JobManage::getRepairRound, repairRound);
        final List<JobManage> manages = this.list(query);
        final Map<String, JobManage> manageMap = manages.stream().collect(
                Collectors.toMap(JobManage::getNumber, Function.identity(), (k1, k2) -> k1));
        String key;
        for (int i = 0; i < dtoS.size(); i++) {
            String order = String.valueOf(i + 2);
            JobManageWPStatusExcelTpl e = dtoS.get(i);
            key = e.getNumber();
            if (!manageMap.containsKey(key)) {
                en = new ImportExcelErrorNoteVO();
                en.setOrder(order);
                en.setErrorNotes(List.of("第" + order + "，此大修轮次中，未找到作业工单号"));
                err.add(en);
            } else {
                e.setId(manageMap.get(key).getId());
            }
        }
        if (!CollectionUtils.isEmpty(err)) {
            result.setCode(200);
            result.setOom("数据校验失败");
            result.setErr(err);
            return result;
        }

        final String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::JobManageWPStatusExcelTpl-import::id", importId, dtoS, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }

    /**
     * 作业管理 更新作业状态（phase） excel 确认导入
     *
     * @param importId 导入校验后，生成的流水号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean importByWPExcel(String importId) {
        final List<JobManageWPStatusExcelTpl> list = (List<JobManageWPStatusExcelTpl>) orionJ2CacheService
                .get("pmsx::JobManageWPStatusExcelTpl-import::id", importId);
        final List<JobManage> manages = Lists.newArrayList();
        for (JobManageWPStatusExcelTpl e : list) {
            JobManage item = new JobManage();
            item.setId(e.getId());
            item.setPhase(e.getPhase());
            manages.add(item);
        }

        log.info("作业管理 更新作业状态 导入的入库数据={}", JSONUtil.toJsonStr(list));
        this.updateBatchById(manages);
        orionJ2CacheService.delete("pmsx::JobManageWPStatusExcelTpl-import::id", importId);
        return true;
    }

    @Override
    public void  setEveryName(List<JobManageVO> vos, String repRound)throws Exception {
        if(CollectionUtils.isEmpty(vos)){
            return;
        }

        List<String> userIdList = new ArrayList<>();
        List<String> deptIdList = new ArrayList<>();
        List<String> jobNumberList = new ArrayList<>();
        List<String> idList = new ArrayList<>();
        for (JobManageVO vo : vos) {
            String personId = vo.getRspUserId();
            if(StringUtils.hasText(personId)){
                userIdList.add(personId);
            }
            String rspDept = vo.getRspDept();
            if(StringUtils.hasText(rspDept)){
                deptIdList.add(rspDept);
            }
            if(StringUtils.hasText(vo.getNumber())){
                jobNumberList.add(vo.getNumber());
            }
            idList.add(vo.getId());
        }
        List<UserVO> userVOList = userRedisHelper.getUserByIds(userIdList);
        Map<String, String> userIdToName = userVOList.stream().collect(Collectors.toMap(UserVO::getId, UserVO::getName));
        List<DeptVO> deptByIds = deptRedisHelper.getDeptByIds(deptIdList);
        Map<String, String> deptIdToName = deptByIds.stream().collect(Collectors.toMap(DeptVO::getId, DeptVO::getName));

        Map<String, String> firstMap = DictFirstExecuteEnum.map();
        List<DictValueVO> importantProDict = dictRedisHelper.getDictListByCode(DictConts.IMPORTANT_PROJECT);
        Map<String, String> importantMap = importantProDict.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription));


        List<DictValueVO> dustDict = dictRedisHelper.getDictListByCode(DictConts.DUST_PROTECTION_LEVEL);
        Map<String, String> dustNumberToDescMap = dustDict.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));
        Map<String,String> map = basePlaceService.allMapSimpleList();
        Map<String,String> mapStatusMap= AuditStatus.getMap();
        Map<String,String> stringStringMap =JobManageBusStatusEnum.getStatusMap();
        Boolean isMange;
        if(StringUtils.hasText(repRound)){
            isMange = majorRepairPlanMemberService.isMange(roleCode,repRound);
        } else {
            isMange = false;
        }
        List<DataStatusVO> busStatusList = statusRedisHelper.getStatusList(DictConts.STATUS_CODE);
        Map<String, DataStatusVO> busStatusMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(busStatusList)) {
            busStatusMap = busStatusList.stream().collect(Collectors.toMap(item-> String.valueOf(item.getStatusValue()), Function.identity(), (k1, k2) -> k1));
        }
        Map<String, DataStatusVO> finalBusStatusMap = busStatusMap;
        Map<String, String> highRiskMap = getHighRisk(jobNumberList);
        //封装工作包审查状态（临时处理）
        List<String> jobIds = vos.stream().map(JobManageVO::getId).collect(Collectors.toList());
        vos.forEach(vo->{
            vo.setRspUserName(userIdToName.getOrDefault(vo.getRspUserId(),""));
            vo.setRspDeptName(deptIdToName.getOrDefault(vo.getRspDept(),""));
            vo.setFirstExecuteName(firstMap.getOrDefault(vo.getFirstExecute(),""));
            vo.setImportantProjectName(importantMap.getOrDefault(vo.getImportantProject(),""));
            vo.setAntiForfeignLevelName(dustNumberToDescMap.getOrDefault(vo.getAntiForfeignLevel(),""));
            vo.setJobBaseName(map.getOrDefault(vo.getJobBase(),""));
            if(StringUtils.hasText(vo.getStudyExamineStatus())){
                vo.setStudyExamineStatusName(mapStatusMap.get(vo.getStudyExamineStatus()));
            }
            if(StringUtils.hasText(vo.getWorkPackageStatus())){
                vo.setWorkPackageStatusName(mapStatusMap.get(vo.getWorkPackageStatus()));
            }

            vo.setBusStatusName(stringStringMap.getOrDefault(vo.getBusStatus(),""));
            vo.setBusDataStatus(finalBusStatusMap.get(vo.getBusStatus()));
            vo.setIsMajorProjectEdit(isMange);

            if (vo != null ) {
                String desc=  highRiskMap.getOrDefault(vo.getNumber(),"");
                if(Objects.nonNull(vo.getIsHighRisk()) && vo.getIsHighRisk()){
                    vo.setHeightRiskName(desc);
                } else {
                    vo.setHeightRiskName("");
                }
            } else {
                vo.setHeightRiskName("");
            }
//            vo.setNewParticipants(jobIdList.contains(vo.getId()));
        });
    }


    @Override
    public Map<String,String> getHighRisk( List<String> jobNumbers){
        LambdaQueryWrapperX<JobHeightRisk> jobHeightRiskLambdaQueryWrapperX = new LambdaQueryWrapperX<>( JobHeightRisk. class);
        if(!jobNumbers.isEmpty()) {
            jobHeightRiskLambdaQueryWrapperX.in(JobHeightRisk::getJobNumber, jobNumbers);
            // .in(JobHeightRisk::getProcessStatus,90,92)
        }
        jobHeightRiskLambdaQueryWrapperX.select(JobHeightRisk::getId,JobHeightRisk::getJobNumber,JobHeightRisk::getRiskLevel);
        List<JobHeightRisk> jobHeightRiskList = jobHeightRiskService.list(jobHeightRiskLambdaQueryWrapperX);  //作业的number和风险等级列表

        Map<String, Set<String>> riskLevelMap = jobHeightRiskList.stream()
                .collect(Collectors.toMap(
                        JobHeightRisk::getJobNumber,
                        jobHeightRisk -> {
                            Set<String> riskLevelSet = new HashSet<>();
                            riskLevelSet.add(jobHeightRisk.getRiskLevel());
                            return riskLevelSet;
                        },
                        (existingValue, newValue) -> {
                            existingValue.addAll(newValue);
                            return existingValue;
                        }
                ));

        Map<String, String> result = new HashMap<>();
        for (Map.Entry<String, Set<String>> entry : riskLevelMap.entrySet()) {
            Set<String> riskLevels = entry.getValue();
            result.put(entry.getKey(), riskLevels.stream().collect(Collectors.joining(",")));
        }
        return  result;
    }

    @Override
    public JobImportantDTO importantJobStatistic(JobManageDTO jobManageDTO) throws Exception {
        LambdaQueryWrapperX<JobManage> condition = new LambdaQueryWrapperX<>( JobManage. class);
        condition.eq(JobManage::getRepairRound,jobManageDTO.getRepairRound())
                 .eq(JobManage::getLogicStatus,1)
                 .select(JobManage::getPhase,JobManage::getIsMajorProject,JobManage::getId);
//        condition.and(item->{
//            item.isNotNull(JobManage::getProjectNumber).or().ne(JobManage::getProjectNumber,"");
//        });
//        condition.and(item->{
//            item.isNotNull(JobManage::getPlanSchemeId).or().ne(JobManage::getPlanSchemeId,"");
//        });
        condition.eq(JobManage::getMatchUp, StatusEnum.ENABLE.getIndex());
        List<JobManage> list = jobManageService.list(condition);
        JobImportantDTO jobImportantDTO = new JobImportantDTO();
        long bigJobComNum=0; // 重大项目已完成数
        long jobComNum=0; // 已完成作业数
        long jobOperation=0; // 实施中作业数
        long bigTotal =0;
        for(JobManage jobManage : list){
            if((JobStatusEnum.CSR.getKey().equals(jobManage.getPhase()))  ||  (JobStatusEnum.CPL.getKey().equals(jobManage.getPhase()))
            ||  (JobStatusEnum.REJ.getKey().equals(jobManage.getPhase())) ){
                if(Objects.nonNull(jobManage.getIsMajorProject())){
                    if(jobManage.getIsMajorProject()){
                        bigJobComNum++;
                    }
                }

                jobComNum++;
            }
            if((JobStatusEnum.RTW.getKey().equals(jobManage.getPhase()))  ||  (JobStatusEnum.WIP.getKey().equals(jobManage.getPhase()))){
                jobOperation++;

            }
            if(Objects.nonNull(jobManage.getIsMajorProject())){
                if(jobManage.getIsMajorProject()){
                    bigTotal++;
                }
            }
        }
        jobImportantDTO.setCompleted(jobComNum);
        jobImportantDTO.setOperation(jobOperation);
        jobImportantDTO.setBigTotal(bigTotal);  // 重大项目总数
        jobImportantDTO.setBigCompleted(bigJobComNum);
        jobImportantDTO.setJobTotal(CollectionUtils.isEmpty(list)?0:list.size());  // 作业总数
        return jobImportantDTO;
    }




    @Override
    public JobHighRiskStatisticsPageDTO highRiskStatistic(JobManageDTO jobManageDTO) throws Exception {
        Date statisticTime = jobManageDTO.getStatisticTime();
        // 创建一个日历实例并设置时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(statisticTime);
        calendar.set(Calendar.HOUR_OF_DAY, 23);   // 小时设为23
        calendar.set(Calendar.MINUTE, 59);       // 分钟设为59
        calendar.set(Calendar.SECOND, 59);       // 秒设为59
        calendar.set(Calendar.MILLISECOND, 999); // 毫秒设为999
        statisticTime = calendar.getTime();     // 更新statisticTime为新的值
        List<JobHighRiskStatisticDTO> jobHighRiskStatisticDTOS = majorRepairPlanMapper.selectJobRiskInfo(jobManageDTO.getRepairRound(),statisticTime);
        List<JobHighRiskStatisticDTO> sortedJobHighRiskStatisticDTOS = jobHighRiskStatisticDTOS.stream()
                .map(job -> {
                    job.setId(UUID.randomUUID().toString()); // 设置为不同的UUID
                    return job;
                })
                .sorted(Comparator.comparing(JobHighRiskStatisticDTO::getJobName, Comparator.nullsLast(String::compareTo)))
                .collect(Collectors.toList());

        Map<String, Integer> riskLevelOrder = Map.of(
                "一级", 1,
                "二级", 2,
                "三级", 3
        );

        List<JobHighRiskStatisticDTO> sortedJobHighRiskStatisticDTOS1 = sortedJobHighRiskStatisticDTOS.stream()
                .sorted(Comparator.comparing(
                        dto -> riskLevelOrder.getOrDefault(dto.getRiskLevel(), Integer.MAX_VALUE),
                        Comparator.nullsLast(Comparator.naturalOrder())
                ))
                .collect(Collectors.toList());



        List<String> rspDeptIdList = jobHighRiskStatisticDTOS.stream()
                .map(JobHighRiskStatisticDTO::getRspDeptId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<DeptVO> deptByIds = deptRedisHelper.getDeptByIds( rspDeptIdList);
        Map<String, String> deptIdToName = deptByIds.stream().collect(Collectors.toMap(DeptVO::getId, DeptVO::getName));
        if (jobHighRiskStatisticDTOS != null) {
            for (JobHighRiskStatisticDTO item : sortedJobHighRiskStatisticDTOS1) {
                if (item != null) {

                    String rspDeptId = item.getRspDeptId();
                    if (rspDeptId != null) {

                        String deptName = deptIdToName.get(rspDeptId);

                        item.setJobDeptName(deptName);
                    } else {

                        item.setJobDeptName(null);
                    }
                }
            }
        }

        JobHighRiskStatisticsPageDTO statistic = getStatistic(sortedJobHighRiskStatisticDTOS);
        List<JobHighRiskStatisticDTO> sortedList = statistic.getJobHighRiskStatisticDTOList().stream()
                .sorted(Comparator.comparing(JobHighRiskStatisticDTO::getJobName, Comparator.nullsLast(String::compareTo)))
                .collect(Collectors.toList());
        statistic.setJobHighRiskStatisticDTOList(sortedList);
        return statistic;

    }

    public JobHighRiskStatisticsPageDTO getStatistic(List<JobHighRiskStatisticDTO> jobHighRiskStatisticDTOS){
        jobHighRiskStatisticDTOS.forEach(item ->{
            item.setRspUserName(userRedisHelper.getUserById(item.getRspUserId()).getName());
        });
        Integer level1Count = 0, level2Count = 0, level3Count = 0;
        for (JobHighRiskStatisticDTO dto : jobHighRiskStatisticDTOS) {

            if ("一级".equals(dto.getRiskLevel())) {
                level1Count++;
            } else if ("二级".equals(dto.getRiskLevel())) {
                level2Count++;
            } else if ("三级".equals(dto.getRiskLevel())) {
                level3Count++;
            }
        }
        int distinctJobNameCount = (int)jobHighRiskStatisticDTOS.stream()
                .map(JobHighRiskStatisticDTO::getJobName) // 将每个DTO对象映射到它的jobName属性
                .filter(Objects::nonNull) // 过滤掉null值
                .collect(Collectors.toSet()) // 去重并收集到一个Set集合
                .size(); // 获取Set的大小，即不同jobName的数量

        JobHighRiskStatisticsPageDTO jobHighRiskStatisticsPageDTO = new JobHighRiskStatisticsPageDTO();
        HashMap<String, Integer> stringIntegerHashMap = new HashMap<>();
        jobHighRiskStatisticsPageDTO.setJobHighRiskStatisticDTOList(jobHighRiskStatisticDTOS);
        stringIntegerHashMap.put("一级", level1Count);
        stringIntegerHashMap.put("二级", level2Count);
        stringIntegerHashMap.put("三级", level3Count);
        stringIntegerHashMap.put("总数", Integer.valueOf(distinctJobNameCount));
        jobHighRiskStatisticsPageDTO.setMap(stringIntegerHashMap);
        return jobHighRiskStatisticsPageDTO;
    }

    @Override
    public Boolean isImportantProject(ImportProjectParamDTO importProjectParamDTO) {
        // todo 需要判断角色 必须 大修指挥部（项目部）
        String id = importProjectParamDTO.getId();
        Boolean isImportProject = importProjectParamDTO.getIsMajorProject();

        LambdaUpdateWrapper<JobManage> wrapper = new LambdaUpdateWrapper<>(JobManage.class);
        wrapper.eq(LyraEntity::getId,id);
//        wrapper.set(JobManage::getImportantProject,isImportProject);
        wrapper.set(JobManage::getIsMajorProject,isImportProject);
        boolean update = this.update(wrapper);
        if (update){
            if (importProjectParamDTO.getIsMajorProject()){
                JobManage job = this.getById(importProjectParamDTO.getId());
                if (Objects.nonNull(job)){
                    mscBuildHandlerManager.send(job,MessageNodeDict.NODE_REPAIR_IMPORTANT);
                }
            }
        }
        return update;
    }

    @Override
    public JobManage getSimpleInfo(String jobId) {
        LambdaQueryWrapperX<JobManage> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(JobManage.class);
        lambdaQueryWrapperX.eq(JobManage::getId,jobId);
        lambdaQueryWrapperX.select(JobManage::getId,JobManage::getRepairRound
                ,JobManage::getJobBase,JobManage::getJobBaseName,JobManage::getNumber,JobManage::getPlanSchemeId);
        List<JobManage> list = this.list(lambdaQueryWrapperX);
        if(CollectionUtils.isEmpty(list)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "作业不存在，请刷新后重试");
        }

        return list.get(0);
    }

    public static  final  String PMS_RISK_MEASURE="JobRiskMeasure";
    @Override
    public JobRiskMeasureVO riskMeasureDetail(String id) throws Exception {

        JobRiskMeasureVO jobRiskMeasureVO =new JobRiskMeasureVO();
        JobManageVO jobManage = this.detailById(id,null);
        jobRiskMeasureVO.setJobId(jobManage.getId());
        jobRiskMeasureVO.setIsHighRisk(jobManage.getIsHighRisk());
        jobRiskMeasureVO.setHeightRisk(jobManage.getHeightRisk());
        jobRiskMeasureVO.setHeightRiskName(jobManage.getHeightRiskName());
        jobRiskMeasureVO.setFirstExecute(jobManage.getFirstExecute());
        jobRiskMeasureVO.setFirstExecuteName(jobManage.getFirstExecuteName());
        jobRiskMeasureVO.setImportantProject(jobManage.getImportantProject());
        jobRiskMeasureVO.setImportantProjectName(jobManage.getImportantProjectName());
        List<JobRiskVO> jobRiskVOList= jobRiskService.getListByJobId(id);

        jobRiskMeasureVO.setRiskVOList(jobRiskVOList);
//        jobRiskMeasureVO.setSecurityMeasureVOList(jobSecurityMeasureService.getListByJobId(id));
        jobRiskMeasureVO.setNewParticipants(jobManage.getNewParticipants());

        List<FileTreeVO> fileVOList = fileApiService.getFilesByDataIdAndDataType(id,PMS_RISK_MEASURE);
        jobRiskMeasureVO.setFileVOList(fileVOList);

        return jobRiskMeasureVO;
    }

    @Override
    public Boolean riskMeasure(JobRiskMeasureDTO jobRiskMeasureDTO) throws Exception {
        String jobId = jobRiskMeasureDTO.getJobId();
        List<FileDTO> fileDTOList = jobRiskMeasureDTO.getFileDTOList();
        List<FileTreeVO> files = fileApiService.getFilesByDataIdAndDataType(jobId, PMS_RISK_MEASURE);
        if(!CollectionUtils.isEmpty(files)){
            fileApiService.removeByIds(files.stream().map(TreeNode::getId).distinct().collect(Collectors.toList()));
        }
        if(!CollectionUtils.isEmpty(fileDTOList)){
            fileDTOList.forEach(item-> item.setId(null));
            this.saveFile(fileDTOList,jobId,PMS_RISK_MEASURE);
        }
        jobNodeStatusService.setNodeStatus(jobId,Arrays.asList("riskInfo"));
        return Boolean.TRUE;
    }

    public static  final  String PMS_JOB_PACKAGE="JobPackage";
    @Override
    public JobPackageVO packageInfo(String id) throws Exception {
        //todo  可能 工作包数据会和 作业主表信息合并
        JobManageVO jobManage = this.detail(id,null);
        JobPackage jobPackage = jobPackageService.getInfoByJobId(id);
        if(null == jobManage){
            return  null;
        }
        JobPackageVO  jobPackageVO = new JobPackageVO();
        BeanCopyUtils.copyProperties(jobManage,jobPackageVO);
        jobPackageVO.setName(jobManage.getName());
        jobPackageVO.setMajorRepairTurn(jobManage.getRepairRound());
        if(Objects.nonNull(jobPackage)){
            jobPackageVO.setId(jobPackage.getId());
            jobPackageVO.setStatus(jobPackage.getStatus());
            jobPackageVO.setDataStatus(jobPackage.getDataStatus());
            jobPackageVO.setId(jobPackage.getId());
            jobPackageVO.setClassName(jobPackage.getClassName());
            jobPackageVO.setEquipmentSystem(jobPackage.getEquipmentSystem());
            jobPackageVO.setFunctionalLocation(jobPackage.getFunctionalLocation());
        }else{
            jobPackageVO.setClassName("");
        }
        jobPackageVO.setJobManageVO(jobManage);
        jobPackageVO.setRiskVOList(jobRiskService.getListByJobId(id));
        List<FileTreeVO> fileVOList = fileApiService.getFilesByDataIdAndDataType(id,PMS_JOB_PACKAGE);
        jobPackageVO.setFileVOList(fileVOList);
        return jobPackageVO;
    }

    @Override
    public Boolean packageInfoEdit(JobRiskMeasureDTO jobRiskMeasureDTO) throws Exception {
        String jobId = jobRiskMeasureDTO.getJobId();
        List<FileDTO> fileDTOList = jobRiskMeasureDTO.getFileDTOList();
        if(CollectionUtils.isEmpty(fileDTOList)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "未传入文件，请选择文件后上传");
        }
        List<FileDTO> insertInfoDTOList = fileDTOList.stream().filter(p -> !StringUtils.hasText(p.getId())).collect(Collectors.toList());
//        List<FileTreeVO> files = fileApiService.getFilesByDataIdAndDataType(jobId, PMS_JOB_PACKAGE);
        List<FileVO> existFileList = fileApi.getFilesByDataId(jobId);
        // 优先移除
        if (Objects.nonNull(existFileList)) {
            // existFileList 中不包含 fileDTOList的删除
            List<String> filesIds = existFileList.stream().map(FileVO::getId).filter(item -> !fileDTOList.stream()
                            .map(FileDTO::getId).filter(Objects::nonNull).collect(Collectors.toList()).contains(item))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(filesIds)) {
                fileApi.removeBatchByIds(filesIds);
            }
        }
       // fileDTOList.forEach(item-> item.setId(null));
        this.saveFile(fileDTOList,jobId,PMS_JOB_PACKAGE);
        return Boolean.TRUE;
    }

    @Override
    public JobDevelopStatisticDTO developList(String repairRound) {
        if(!StringUtils.hasText(repairRound)){
            return  new JobDevelopStatisticDTO();
        }
        LambdaQueryWrapperX<JobManage> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(JobManage.class);
        lambdaQueryWrapperX.eq(JobManage::getRepairRound,repairRound);
        lambdaQueryWrapperX.eq(JobManage::getMatchUp, StatusEnum.ENABLE.getIndex());
        lambdaQueryWrapperX.eq(JobManage::getLogicStatus,1);
        lambdaQueryWrapperX.innerJoin(RelationOrgToJob.class, RelationOrgToJob::getJobNumber, JobManage::getNumber);
        lambdaQueryWrapperX.innerJoin(MajorRepairOrg.class, MajorRepairOrg::getId, RelationOrgToJob::getRepairOrgId);
        lambdaQueryWrapperX.eq(MajorRepairOrg::getRepairRound,repairRound);
        lambdaQueryWrapperX.select(JobManage::getRepairRound,JobManage::getId,JobManage::getRspDept,JobManage::getPhase,JobManage::getCreateTime);
        List<JobManage> list = this.list(lambdaQueryWrapperX);
        if(CollectionUtils.isEmpty(list)){
            return  new JobDevelopStatisticDTO();
        }
        List<String> deptIdList = list.stream().map(JobManage::getRspDept).distinct().collect(Collectors.toList());
        List<DeptVO> deptByIds = deptRedisHelper.getDeptByIds(deptIdList);
        Map<String, String> idToName = deptByIds.stream().collect(Collectors.toMap(DeptVO::getId, DeptVO::getName,(k1,k2)->k1));

        Map<String, Map<String, Long>> genderCountMap = list.stream() // 中心id，阶段，计数
                .filter(item -> StringUtils.hasText(item.getPhase()) && StringUtils.hasText(item.getRspDept()))
                .collect(Collectors.groupingBy(JobManage::getRspDept, Collectors.groupingBy(JobManage::getPhase, Collectors.counting())));
        return  this.packageCount(genderCountMap,idToName,repairRound,list);
//        return jobDevelopStatisticDTO;
    }

    public JobDevelopStatisticDTO packageCount(Map<String, Map<String, Long>> genderCountMap,Map<String, String> idToName
            ,String repairRound,List<JobManage> list){
        List<JobDevelopVO> jobDevelopVOS = new ArrayList<>();
        long jobPreNum=0,jobComNum=0,jobProNum=0;
        Map<String, String> map = JobStatusEnum.keyName();
        for (Map.Entry<String, Map<String, Long>> stringMapEntry : genderCountMap.entrySet()) {
            String key = stringMapEntry.getKey();
            Map<String, Long> value = stringMapEntry.getValue();
            jobPreNum =jobPreNum+ value.getOrDefault(JobStatusEnum.NPLN.getKey(),0l)
                    + value.getOrDefault(JobStatusEnum.ASGN.getKey(),0l)
                    +value.getOrDefault(JobStatusEnum.INPL.getKey(),0l)
                    +value.getOrDefault(JobStatusEnum.PLND.getKey(),0l)
                    +value.getOrDefault(JobStatusEnum.RPLN.getKey(),0l)
                    +value.getOrDefault(JobStatusEnum.APPV.getKey(),0l)
                    +value.getOrDefault(JobStatusEnum.SCHD.getKey(),0l)
                    + value.getOrDefault(JobStatusEnum.PRWD.getKey(),0l);
            jobProNum=jobProNum+value.getOrDefault(JobStatusEnum.RTW.getKey(),0l)
                    +value.getOrDefault(JobStatusEnum.WIP.getKey(),0l);
            jobComNum=jobComNum+value.getOrDefault(JobStatusEnum.CSR.getKey(),0l)
                    +value.getOrDefault(JobStatusEnum.CPL.getKey(),0l)
                    +value.getOrDefault(JobStatusEnum.REJ.getKey(),0l);
            // 完成数量  准备数量 实施数量
            long finishNum=0,propNum=0,jicNum=0;
            /**
             * 准备率=（APPV+SCHD+RTW+WIP+CSR+CPL+REJ）/工单总数×100%
             * 完成率=（CSR+CPL+REJ）/工单总数×100%
             * 实施率=（RTW+WIP+CSR+CPL+REJ）/工单总数×100%
             */
            propNum =value.getOrDefault(JobStatusEnum.APPV.getKey(),0l)
                    +value.getOrDefault(JobStatusEnum.SCHD.getKey(),0l)
                    +value.getOrDefault(JobStatusEnum.RTW.getKey(),0l)
                    +value.getOrDefault(JobStatusEnum.WIP.getKey(),0l)
                    +value.getOrDefault(JobStatusEnum.CSR.getKey(),0l)
                    +value.getOrDefault(JobStatusEnum.CPL.getKey(),0l)
                    +value.getOrDefault(JobStatusEnum.REJ.getKey(),0l);

            finishNum=value.getOrDefault(JobStatusEnum.CSR.getKey(),0l)
                    +value.getOrDefault(JobStatusEnum.CPL.getKey(),0l)
                    +value.getOrDefault(JobStatusEnum.REJ.getKey(),0l);
            jicNum=value.getOrDefault(JobStatusEnum.RTW.getKey(),0l)
                    +value.getOrDefault(JobStatusEnum.WIP.getKey(),0l)
                    +value.getOrDefault(JobStatusEnum.CSR.getKey(),0l)
                    +value.getOrDefault(JobStatusEnum.CPL.getKey(),0l)
                    +value.getOrDefault(JobStatusEnum.REJ.getKey(),0l);
            JobDevelopVO jobDevelopVO = new JobDevelopVO();
            jobDevelopVO.setRspDeptId(key);
            jobDevelopVO.setRspDeptName(idToName.get(key));

            List<JobDevelopVO.JobStatusCount> jobStatusCounts = new ArrayList<>();
            Long  total = 0l;
            for (Map.Entry<String, String> stringStringEntry : map.entrySet()) {
                JobDevelopVO.JobStatusCount jobStatusCount = new JobDevelopVO.JobStatusCount();
                String  phase= stringStringEntry.getKey();
                Long count =value.get(phase);
                if(JobStatusEnum.TOTAL.getKey().equals(phase) || JobStatusEnum.RESRAT.getKey().equals(phase)){
                    continue;
                }
                jobStatusCount.setJobStatusKey(phase);
                jobStatusCount.setCount(null == count? 0L:count);
                total = total+jobStatusCount.getCount();
                jobStatusCounts.add(jobStatusCount);
            }

            JobDevelopVO.JobStatusCount jobStatusCountRes = new JobDevelopVO.JobStatusCount();
            jobStatusCountRes.setJobStatusKey(JobStatusEnum.RESRAT.getKey());
            if (!value.isEmpty() && propNum > 0) {
                BigDecimal prepRate1 = BigDecimal.valueOf(propNum).divide(BigDecimal.valueOf(total), 2, BigDecimal.ROUND_HALF_UP);
                int v = prepRate1.multiply(new BigDecimal(100)).setScale(0, BigDecimal.ROUND_HALF_UP).intValue();
                jobStatusCountRes.setCount(Long.parseLong(String.valueOf(v)));
            } else {
                jobStatusCountRes.setCount(0L);
            }
            jobStatusCounts.add(jobStatusCountRes);

            JobDevelopVO.JobStatusCount jobStatusCountCom = new JobDevelopVO.JobStatusCount();
            jobStatusCountCom.setJobStatusKey(JobStatusEnum.COMRAT.getKey());
            if (value != null && !value.isEmpty() && finishNum > 0) {
                BigDecimal prepRate1 = BigDecimal.valueOf(finishNum).divide(BigDecimal.valueOf(total),2,BigDecimal.ROUND_HALF_UP);
                int v = prepRate1.multiply(new BigDecimal(100)).setScale(0, BigDecimal.ROUND_HALF_UP).intValue();
                jobStatusCountCom.setCount(Long.parseLong(String.valueOf(v)));

            } else {
                jobStatusCountCom.setCount(0L);
            }
            jobStatusCounts.add(jobStatusCountCom);


            JobDevelopVO.JobStatusCount jicNumCountCom = new JobDevelopVO.JobStatusCount();
            jicNumCountCom.setJobStatusKey(JobStatusEnum.JICRAT.getKey());
            if (value != null && !value.isEmpty() && jicNum > 0) {
                BigDecimal jicRate1 = BigDecimal.valueOf(jicNum).divide(BigDecimal.valueOf(total),2,BigDecimal.ROUND_HALF_UP);
                int v = jicRate1.multiply(new BigDecimal(100)).setScale(0, BigDecimal.ROUND_HALF_UP).intValue();
                jicNumCountCom.setCount(Long.parseLong(String.valueOf(v)));
            } else {
                jicNumCountCom.setCount(0L);
            }
            jobStatusCounts.add(jicNumCountCom);

            JobDevelopVO.JobStatusCount sumCount = new JobDevelopVO.JobStatusCount();
            sumCount.setJobStatusKey(JobStatusEnum.SUM.getKey());
            sumCount.setCount(total);
            jobStatusCounts.add(sumCount);

            jobDevelopVO.setJobStatusCountList(jobStatusCounts);
            jobDevelopVOS.add(jobDevelopVO);
        }
        JobDevelopStatisticDTO jobDevelopStatisticDTO = new JobDevelopStatisticDTO();
        HashMap<String, Long> statisticMap = new HashMap<>();
        statisticMap.put(JobStatusEnum.JPC.getKey(),jobPreNum);
        statisticMap.put(JobStatusEnum.JIC.getKey(),jobProNum);
        statisticMap.put(JobStatusEnum.JC.getKey(),jobComNum);
        jobDevelopStatisticDTO.setJobDevelopVOS(jobDevelopVOS);

        LambdaQueryWrapperX<MajorRepairPlan> lambdaQueryWrapper = new LambdaQueryWrapperX<>(MajorRepairPlan.class);
        lambdaQueryWrapper.eq(JobManage::getRepairRound,repairRound).last("limit 1");
        MajorRepairPlan one = majorRepairPlanService.getOne(lambdaQueryWrapper);
        long planJob=0,newJob=0,sumJob=0;
        for (JobManage item : list) {
            Date createTime = item.getCreateTime();
            if (createTime != null) {
                Date actualBeginTime = one.getActualBeginTime();
                if (actualBeginTime != null) {
                    if (createTime.before(actualBeginTime)) {
                        planJob++;
                    }
                    if (createTime.after(actualBeginTime)) {
                        newJob++;
                    }
                }
            }
        }
        sumJob= list.size();
        statisticMap.put(JobStatusEnum.PWC.getKey(),planJob);
        statisticMap.put(JobStatusEnum.AWC.getKey(),newJob);
        statisticMap.put(JobStatusEnum.TWC.getKey(),sumJob);
        jobDevelopStatisticDTO.setStatisticMap(statisticMap);
        JobDevelopVO jobDevelopVO = new JobDevelopVO();
        jobDevelopVO.setRspDeptId(JobStatusEnum.SUM.getOrder().toString());
        jobDevelopVO.setRspDeptName(JobStatusEnum.SUM.getName());
        // 汇总
        List<String> columnList = JobStatusEnum.getColumnList();
        List<JobDevelopVO.JobStatusCount> counts = this.packagePhaseSum(jobDevelopVOS,columnList);
        jobDevelopVO.setJobStatusCountList(counts);
        List<JobDevelopVO> jobDevelopVOS1 = new ArrayList<>();
        jobDevelopVOS1.add(jobDevelopVO);
        jobDevelopVOS1.addAll(jobDevelopVOS);
        jobDevelopStatisticDTO.setJobDevelopVOS(jobDevelopVOS1);
        return jobDevelopStatisticDTO;
    }

    public List<JobDevelopVO.JobStatusCount> packagePhaseSum(List<JobDevelopVO> jobDevelopVOS,List<String> columnList){
        Map<String, Long> map =new HashMap<>();
        for (JobDevelopVO developVO : jobDevelopVOS) {
            List<JobDevelopVO.JobStatusCount> developVOList = developVO.getJobStatusCountList();

            Map<String,Long> jobStatusCountMap =developVOList.stream().collect(Collectors.toMap(JobDevelopVO.JobStatusCount::getJobStatusKey, JobDevelopVO.JobStatusCount::getCount));

            columnList.forEach(item->{
                Long lo= jobStatusCountMap.getOrDefault(item,0L);
                Long count= map.getOrDefault(item,0L);
                map.put(item,lo+count);
            });
        }
        List<JobDevelopVO.JobStatusCount> developVOList = new ArrayList<>();
        long sumTotal =0l;
        for (Map.Entry<String, Long> stringLongEntry : map.entrySet()) {
            JobDevelopVO.JobStatusCount jobStatusCount = new JobDevelopVO.JobStatusCount();
            jobStatusCount.setCount(stringLongEntry.getValue());
            jobStatusCount.setJobStatusKey(stringLongEntry.getKey());
            developVOList.add(jobStatusCount);
            sumTotal=sumTotal+stringLongEntry.getValue();
        }
        long finishNum=0,propNum=0,jicNum=0;
        propNum =map.getOrDefault(JobStatusEnum.APPV.getKey(),0l)
                +map.getOrDefault(JobStatusEnum.SCHD.getKey(),0l)
                +map.getOrDefault(JobStatusEnum.RTW.getKey(),0l)
                +map.getOrDefault(JobStatusEnum.WIP.getKey(),0l)
                +map.getOrDefault(JobStatusEnum.CSR.getKey(),0l)
                +map.getOrDefault(JobStatusEnum.CPL.getKey(),0l)
                +map.getOrDefault(JobStatusEnum.REJ.getKey(),0l);

        finishNum=map.getOrDefault(JobStatusEnum.CSR.getKey(),0l)
                +map.getOrDefault(JobStatusEnum.CPL.getKey(),0l)
                +map.getOrDefault(JobStatusEnum.REJ.getKey(),0l);
        jicNum=map.getOrDefault(JobStatusEnum.RTW.getKey(),0l)
                +map.getOrDefault(JobStatusEnum.WIP.getKey(),0l)
                +map.getOrDefault(JobStatusEnum.CSR.getKey(),0l)
                +map.getOrDefault(JobStatusEnum.CPL.getKey(),0l)
                +map.getOrDefault(JobStatusEnum.REJ.getKey(),0l);

        JobDevelopVO.JobStatusCount sumCount = new JobDevelopVO.JobStatusCount();
        sumCount.setJobStatusKey(JobStatusEnum.SUM.getKey());
        sumCount.setCount(sumTotal);
        developVOList.add(sumCount);


        JobDevelopVO.JobStatusCount jobStatusCountRes = new JobDevelopVO.JobStatusCount();
        jobStatusCountRes.setJobStatusKey(JobStatusEnum.RESRAT.getKey());
        if ( propNum>0) {
            BigDecimal prepRate1 = BigDecimal.valueOf(propNum).divide(BigDecimal.valueOf(sumTotal),2,BigDecimal.ROUND_HALF_UP);
            int v = prepRate1.multiply(new BigDecimal(100)).setScale(0, BigDecimal.ROUND_HALF_UP).intValue();
            jobStatusCountRes.setCount(Long.parseLong(String.valueOf(v)));
        } else {
            jobStatusCountRes.setCount(0L);
        }
        developVOList.add(jobStatusCountRes);

        JobDevelopVO.JobStatusCount jobStatusCountCom = new JobDevelopVO.JobStatusCount();
        jobStatusCountCom.setJobStatusKey(JobStatusEnum.COMRAT.getKey());
        if (finishNum > 0) {
            BigDecimal prepRate1 = BigDecimal.valueOf(finishNum).divide(BigDecimal.valueOf(sumTotal),2,BigDecimal.ROUND_HALF_UP);
            int v = prepRate1.multiply(new BigDecimal(100)).setScale(0, BigDecimal.ROUND_HALF_UP).intValue();
            jobStatusCountCom.setCount(Long.parseLong(String.valueOf(v)));

        } else {
            jobStatusCountCom.setCount(0L);
        }
        developVOList.add(jobStatusCountCom);

        JobDevelopVO.JobStatusCount jicNumCountCom = new JobDevelopVO.JobStatusCount();
        jicNumCountCom.setJobStatusKey(JobStatusEnum.JICRAT.getKey());
        if ( jicNum > 0) {
            BigDecimal jicRate1 = BigDecimal.valueOf(jicNum).divide(BigDecimal.valueOf(sumTotal),2,BigDecimal.ROUND_HALF_UP);
            int v = jicRate1.multiply(new BigDecimal(100)).setScale(0, BigDecimal.ROUND_HALF_UP).intValue();
            jicNumCountCom.setCount(Long.parseLong(String.valueOf(v)));
        } else {
            jicNumCountCom.setCount(0L);
        }
        developVOList.add(jicNumCountCom);
        return developVOList;
    }

    @Override
    public List<JobManage> listByRepairRound(String repairRound) {
        LambdaQueryWrapperX<JobManage> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(JobManage.class);
        lambdaQueryWrapperX.eq(JobManage::getRepairRound,repairRound);
        lambdaQueryWrapperX.eq(JobManage::getMatchUp, StatusEnum.ENABLE.getIndex());
//        lambdaQueryWrapperX.and(item->{
//            item.isNotNull(JobManage::getProjectNumber).ne(JobManage::getProjectNumber,"");
//        });
//        lambdaQueryWrapperX.and(item->{
//            item.isNotNull(JobManage::getPlanSchemeId).ne(JobManage::getPlanSchemeId,"");
//        });

        lambdaQueryWrapperX.select(JobManage::getId,JobManage::getCreateTime,JobManage::getStudyExamineStatus
                ,JobManage::getWorkPackageStatus,JobManage::getStatus);
        return  this.list(lambdaQueryWrapperX);
    }

    @Override
    public JobManageVO detailByNumber(String number,String repairRound) {
        LambdaQueryWrapperX<JobManage> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(JobManage.class);
        lambdaQueryWrapperX.eq(JobManage::getNumber,number);
        if(StringUtils.hasText(repairRound)){
            lambdaQueryWrapperX.eq(JobManage::getRepairRound,repairRound);
        }
        List<JobManage> list = this.list(lambdaQueryWrapperX);
        if(CollectionUtils.isEmpty(list)){
            if(StringUtils.hasText(repairRound)){
                throw new BaseException(HttpStatus.BAD_REQUEST.value(), "非本轮大修作业");
            }else{
                throw new BaseException(HttpStatus.BAD_REQUEST.value(), "无当前编号的作业");
            }
        }
        return BeanCopyUtils.convertTo(list.get(0),JobManageVO::new);
    }

    @Override
    public JobManageVO allListByNumber(String number, String repairRound) {
        LambdaQueryWrapperX<JobManage> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(JobManage.class);
        lambdaQueryWrapperX.eq(JobManage::getNumber,number);
        lambdaQueryWrapperX.and(item->{
            item.isNull(JobManage::getProjectNumber).or().eq(JobManage::getProjectNumber,"");
        });
        lambdaQueryWrapperX.and(item->{
            item.isNull(JobManage::getPlanSchemeId).or().eq(JobManage::getPlanSchemeId,"");
        });
        List<JobManage> list = this.list(lambdaQueryWrapperX);
        if(CollectionUtils.isEmpty(list)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "无当前编号的作业");
        }
        JobManageVO jobManageVO= BeanCopyUtils.convertTo(list.get(0),JobManageVO::new);
        Object value= orionJ2CacheService.get(LOCKED_KEY, jobManageVO.getId());
        if(!Objects.isNull(value)){
            throw new RuntimeException("该工单已锁定（业务使用中）请半小时后再试");
        }
        final LockInfo lockInfo = lockTemplate.lock(LOCKED_KEY + number, 30000L, 5000L);
        if (lockInfo == null){
            throw new RuntimeException("该工单已锁定（业务使用中）请半小时后再试");
        }
        try {
            // 设置数据锁
            orionJ2CacheService.set(LOCKED_KEY, jobManageVO.getId(),jobManageVO.getId(),  30 * 60);
        }finally {
            lockTemplate.releaseLock(lockInfo);
        }
        return jobManageVO;
    }

    @Override
    public List<JobManageVO> listByJobNumberList(List<String> jobNumberList) {
        if(CollectionUtils.isEmpty(jobNumberList)){
            return  new ArrayList<>();
        }
        LambdaQueryWrapperX<JobManage> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(JobManage.class);
        lambdaQueryWrapperX.in(JobManage::getNumber,jobNumberList.stream().distinct().collect(Collectors.toList()));
        List<JobManage> list = this.list(lambdaQueryWrapperX);
        if(CollectionUtils.isEmpty(list)){
            return  new ArrayList<>();
        }
        List<String> rspDeptIdList = new ArrayList<>();
        for (JobManage jobManage : list) {
            if(StringUtils.hasText(jobManage.getRspDept())){
                rspDeptIdList.add(jobManage.getRspDept());
            }
        }
        List<DeptVO> deptVOS =  deptRedisHelper.getDeptByIds(rspDeptIdList);
        Map<String,String> idToName = deptVOS.stream().collect(Collectors.toMap(DeptVO::getId,DeptVO::getName,(k1,ke)->k1));
        List<JobManageVO> jobManageVOList =BeanCopyUtils.convertListTo(list,JobManageVO::new);
        jobManageVOList.forEach(item->{
            item.setRspDeptName(idToName.getOrDefault(item.getRspDept(),""));
        });
        return jobManageVOList;
    }

    @Override
    public List<SimStatusVO> statusList() {
        List<SimStatusVO> statusVOList = new ArrayList<>();
        JobManageStatusEnum[] values = JobManageStatusEnum.values();
        for (JobManageStatusEnum value : values) {
            SimStatusVO simStatusVO = new SimStatusVO();
            simStatusVO.setStatus(value.getStatus());
            simStatusVO.setDesc(value.getDesc());
            statusVOList.add(simStatusVO);
        }
        return statusVOList;
    }


    public void saveFile(List<FileDTO> fileDTOList,String dataId,String dataType) throws Exception {
        fileDTOList.forEach(item->{
            item.setDataType(dataType);
            item.setDataId(dataId);
        });
        fileApiService.batchSaveFile(fileDTOList);
    }


    public static class JobManageExcelListener extends AnalysisEventListener<JobManageDTO> {

        private final List<JobManageDTO> data = new ArrayList<>();

        @Override
        public void invoke(JobManageDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<JobManageDTO> getData() {
            return data;
        }
    }



    @Override
    public JobManageVO detailByNumberUseSave(String number) {
        LambdaQueryWrapperX<JobManage> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(JobManage.class);
        lambdaQueryWrapperX.eq(JobManage::getNumber,number);
        lambdaQueryWrapperX.eq(JobManage::getProjectNumber,number);
        List<JobManage> list = this.list(lambdaQueryWrapperX);
        if(CollectionUtils.isEmpty(list)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "通过编号获取数据，未查询到数据");
        }
        return BeanCopyUtils.convertTo(list.get(0),JobManageVO::new);
    }

    @Override
    public Page<JobManageVO> economizePages(Page<JobManageDTO> pageRequest) throws Exception {
        JobManageDTO query=   pageRequest.getQuery();
        if(null ==query){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "参数错误，请传入对应的大修轮次");
        }
        String repairRound = query.getRepairRound();
        List<String> jobCodeList = majorRepairPlanEconomizeService.listByRepairRound(repairRound);
        return this.getPage(pageRequest,jobCodeList,repairRound);
    }

    public Page<JobManageVO> getPage(Page<JobManageDTO> pageRequest,List<String> jobCodeList,String repairRound) throws Exception {
        LambdaQueryWrapperX<JobManage> condition = new LambdaQueryWrapperX<>( JobManage. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.eq(JobManage::getRepairRound,repairRound);
        condition.orderByDesc(JobManage::getCreateTime);
        if(!CollectionUtils.isEmpty(jobCodeList)){
            condition.notIn(JobManage::getNumber,jobCodeList);
        }
        condition.eq(JobManage::getMatchUp,StatusEnum.ENABLE.getIndex());
//        condition.and(item-> item.isNotNull(JobManage::getProjectNumber).ne(JobManage::getProjectNumber,""));
//        condition.and(item-> item.isNotNull(JobManage::getPlanSchemeId).ne(JobManage::getPlanSchemeId,""));
//        condition.and(item-> item.isNotNull(JobManage::getNOrO).ne(JobManage::getNOrO,""));
        Page<JobManage> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), JobManage::new));
        PageResult<JobManage> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<JobManageVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<JobManageVO> vos = BeanCopyUtils.convertListTo(page.getContent(), JobManageVO::new);
        setEveryName(vos,null);
        pageResult.setContent(vos);
        return  pageResult;
    }

    @Override
    public Page<JobManageVO> reducePages(Page<JobManageDTO> pageRequest) throws Exception {
        JobManageDTO query=   pageRequest.getQuery();
        if(null ==query){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "参数错误，请传入对应的大修轮次");
        }
        String repairRound = query.getRepairRound();
        List<String> jobCodeList = majorRepairPlanMeterReduceService.listByRepairRound(repairRound);
        return this.getPage(pageRequest,jobCodeList,repairRound);
    }

    @Override
    public Boolean cancelById(String id) {
        /**
         * 取消锁定
         */
        orionJ2CacheService.delete(LOCKED_KEY, id);
        return Boolean.TRUE;
    }

    @Override
    public Page<JobManageVO> projectPages(Page<JobManageDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<JobManage> condition = new LambdaQueryWrapperX<>( JobManage. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(JobManage::getCreateTime);

        JobManageDTO query=   pageRequest.getQuery();
        String repairRound="";
        if(null != query){
            if(StringUtils.hasText(query.getBusStatus())){
                condition.eq(JobManage::getBusStatus, query.getBusStatus());
            }
            if(StringUtils.hasText(query.getNOrO())){
                // O 为 OPT   N为 N，M
                if(Objects.equals("O",query.getNOrO())){
                    condition.in(JobManage::getNOrO,Arrays.asList("O","T","P"));
                }
                if(Objects.equals("N",query.getNOrO())){
                    condition.in(JobManage::getNOrO,Arrays.asList("N","M"));
                }
            }
            if(StringUtils.hasText(query.getRepairRound())){
                condition.eq(JobManage::getRepairRound,query.getRepairRound());
                repairRound=query.getRepairRound();
            }
            if(StringUtils.hasText(query.getPlanSchemeId())){
                condition.eq(JobManage::getPlanSchemeId,query.getPlanSchemeId());
            }
            if(StringUtils.hasText(query.getProjectNumber())){
                condition.eq(JobManage::getProjectNumber,query.getProjectNumber());
            }
            if(!StringUtils.hasText(query.getPlanSchemeId()) && !StringUtils.hasText(query.getProjectNumber())){
                condition.and(item-> item.isNull(JobManage::getProjectNumber).or().eq(JobManage::getProjectNumber,""));
                condition.and(item-> item.isNull(JobManage::getPlanSchemeId).or().eq(JobManage::getPlanSchemeId,""));
            }
        }else {
            condition.and(item-> item.isNull(JobManage::getProjectNumber).or().eq(JobManage::getProjectNumber,""));
            condition.and(item-> item.isNull(JobManage::getPlanSchemeId).or().eq(JobManage::getPlanSchemeId,""));
        }

        Page<JobManage> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), JobManage::new));

        PageResult<JobManage> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<JobManageVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<JobManageVO> vos = BeanCopyUtils.convertListTo(page.getContent(), JobManageVO::new);
        setEveryName(vos,repairRound);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public boolean saveProjectManage(List<JobManageDTO> dtoList) {
        List<JobManage> vos = BeanCopyUtils.convertListTo(dtoList, JobManage::new);
        ProjectSchemeApiVO projectApiVO = projectSchemeApi2Service.findById(dtoList.get(0).getPlanSchemeId());
        for(JobManage jobManage:vos){
            //作业基地为空
            jobManage.setJobBase(projectApiVO.getEnforceBasePlace());
            //作业系统条件为空
            if("pms_daily_con".equals(projectApiVO.getWorkContent())){
                if(StrUtil.isEmpty(jobManage.getNOrO())){
                    jobManage.setNOrO("N");
                }
            }
            if("pms_major_repair_con".equals(projectApiVO.getWorkContent())){
                if(StrUtil.isEmpty(jobManage.getNOrO())){
                    jobManage.setNOrO("O");
                }
            }
            //大修轮次为空
            jobManage.setRepairRound(projectApiVO.getRepairRound());
            //项目名称
            jobManage.setProjectName(projectApiVO.getProjectName());

            jobManage.setCreateTime(new Date());
            jobManage.setCreatorId(CurrentUserHelper.getCurrentUserId());
            jobManage.setOwnerId(CurrentUserHelper.getCurrentUserId());
            jobManage.setModifyTime(new Date());
            jobManage.setModifyId(CurrentUserHelper.getCurrentUserId());

            jobManage.setEndTime(this.setDateTime(jobManage.getWorkDuration(),jobManage.getBeginTime()));
        }
        return this.updateBatchById(vos);
    }

    @Override
    public JobManage getJobBaseInfo(String jobId) {
        LambdaQueryWrapperX<JobManage> condition = new LambdaQueryWrapperX<>( JobManage. class);
        condition.eq(JobManage::getId,jobId);
        condition.select(JobManage::getId,JobManage::getJobBase,JobManage::getJobBaseName);
        List<JobManage> jobManageList =this.list(condition);
        if(CollectionUtils.isEmpty(jobManageList)){
            throw  new BaseException(HttpStatus.BAD_REQUEST.value(), "参数错误，当前作业被删除或者不存在");
        }
        return jobManageList.get(0);
    }

    @Override
    public Boolean copy(JobCopyParamDTO jobCopyParamDTO) {
        String sourceId= jobCopyParamDTO.getSourceId();
        List<String> targetIdList = jobCopyParamDTO.getTargetIdList();
        jobMaterialService.copyByJoIdToTargetId(sourceId,targetIdList);
        jobPostAuthorizeService.copyByJoIdToTargetId(sourceId,targetIdList);
        return Boolean.TRUE;
    }

    @Override
    public Page<JobManageVO> targetPage(Page<JobManageDTO> pageRequest) {

        LambdaQueryWrapperX<JobManage> condition = new LambdaQueryWrapperX<>( JobManage. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(JobManage::getCreateTime);

        JobManageDTO query=   pageRequest.getQuery();
        String repairRound="";
        if(Objects.isNull(query)){
            throw  new BaseException(HttpStatus.BAD_REQUEST.value(), "参数错误，请传入必须传入的来源数据");
        }
        if(null != query){
            if(StringUtils.hasText(query.getBusStatus())){
                condition.eq(JobManage::getBusStatus, query.getBusStatus());
            }
            if(StringUtils.hasText(query.getNOrO())){
                // O 为 OPT   N为 N，M
                if(Objects.equals("O",query.getNOrO())){
                    condition.in(JobManage::getNOrO,Arrays.asList("O","T","P"));
                }
                if(Objects.equals("N",query.getNOrO())){
                    condition.in(JobManage::getNOrO,Arrays.asList("N","M"));
                }
            }
            if(StringUtils.hasText(query.getRepairRound())){
                condition.eq(JobManage::getRepairRound,query.getRepairRound());
                repairRound=query.getRepairRound();
            }
            if(StringUtils.hasText(query.getPlanSchemeId())){
                condition.eq(JobManage::getPlanSchemeId,query.getPlanSchemeId());
            }
            if(StringUtils.hasText(query.getProjectNumber())){
                condition.eq(JobManage::getProjectNumber,query.getProjectNumber());
            }
        }
        condition.eq(JobManage::getMatchUp,StatusEnum.ENABLE.getIndex());
//        condition.and(item->{
//            item.isNotNull(JobManage::getProjectNumber).ne(JobManage::getProjectNumber,"");
//        });
//        condition.and(item->{
//            item.isNotNull(JobManage::getPlanSchemeId).ne(JobManage::getPlanSchemeId,"");
//        });
//        condition.and(item->{
//            item.isNotNull(JobManage::getNOrO).ne(JobManage::getNOrO,"");
//        });
        condition.ne(JobManage::getId,query.getSourceId());
        Page<JobManage> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), JobManage::new));

        PageResult<JobManage> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<JobManageVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<JobManageVO> vos = BeanCopyUtils.convertListTo(page.getContent(), JobManageVO::new);
//        setEveryName(vos,repairRound);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public Page<JobManageVO> projectJobManage(Page<JobManageDTO> pageRequest) {
        LambdaQueryWrapperX<JobManage> condition = new LambdaQueryWrapperX<>(JobManage. class);
        List<List<SearchCondition>> searchConditions = pageRequest.getSearchConditions();
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchCondition searchCondition = searchConditions.get(0).get(0);
            condition.innerJoin(UserDO.class,UserDO::getId, JobManage::getRspUserId);
            condition.innerJoin(DeptDO.class,DeptDO::getId, JobManage::getRspDept);
            Object value = searchCondition.getValues().get(0);
            condition.and(item->{
                item.like(JobManage::getNumber, value)
                        .or().like(JobManage::getName,value);
                item.or().like(UserDO::getName,value);
                item.or().like(DeptDO::getName,value);
            });
        }
        String projectId = pageRequest.getQuery().getProjectId();
        if (!StringUtils.hasText(projectId)){
            projectId = "";
        }
        List<String> jobIds = jobManageMapper.selectJobIdsByRepairRound(pageRequest.getQuery().getRepairRound(), projectId);
        //过滤已经被选择的作业
        condition.eq(JobManage::getRepairRound,pageRequest.getQuery().getRepairRound());
        condition.eq(JobManage::getIsMajorProject,true);
        condition.in(JobManage::getNOrO,Arrays.asList("O","P","T"));
        if (!CollectionUtils.isEmpty(jobIds)){
            condition.notIn(JobManage::getId,jobIds);
        }
        condition.orderByDesc(JobManage::getCreateTime);
        Page<JobManage> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), JobManage::new));

        PageResult<JobManage> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<JobManageVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<JobManageVO> vos = BeanCopyUtils.convertListTo(page.getContent(), JobManageVO::new);
        List<DictValueVO> heightRisk = dictRedisHelper.getByDictNumber("pms_height_level", CurrentUserHelper.getOrgId());
        Map<String, String> valueToName = heightRisk.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));
        List<String> rspUserIds = vos.stream().map(JobManageVO::getRspUserId).collect(Collectors.toList());
        List<SimpleUser> userByIds = userRedisHelper.getSimpleUserByIds(rspUserIds);
        Map<String, SimpleUser> userIdToUser = userByIds.stream().collect(Collectors.toMap(SimpleUser::getId, Function.identity()));
        vos.forEach(vo->{
            SimpleUser userById = userIdToUser.getOrDefault(vo.getRspUserId(), null);
            if(Objects.nonNull(userById)){
                vo.setRspUserName(userById.getName());
                vo.setRspDeptName(userById.getOrgName());
            }
            vo.setHeightRiskName(valueToName.getOrDefault(vo.getHeightRisk(),""));
        });
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public Page<JobManageVO> jobListByProjectId(Page<JobManageDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<JobManage> condition = new LambdaQueryWrapperX<>(JobManage. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.innerJoin(ProjectJobDTO.class,ProjectJobDTO::getJobId,JobManage::getId);
        condition.eq(ProjectJobDTO::getProjectId,pageRequest.getQuery().getProjectId());
        condition.orderByDesc(JobManage::getCreateTime);
        Page<JobManage> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());

        PageResult<JobManage> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<JobManageVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<JobManageVO> vos = BeanCopyUtils.convertListTo(page.getContent(), JobManageVO::new);
        if (!CollectionUtils.isEmpty(vos)){
            List<String> jobIds = vos.stream().map(JobManageVO::getId).collect(Collectors.toList());

            List<DictValueVO> heightRisk = dictRedisHelper.getByDictNumber("pms_height_level", CurrentUserHelper.getOrgId());
            List<DictValueVO> firstExecute = dictRedisHelper.getByDictNumber("pms_first_execute", CurrentUserHelper.getOrgId());
            List<DictValueVO> importantProject = dictRedisHelper.getByDictNumber("pms_important_project", CurrentUserHelper.getOrgId());
            List<JobPackageStatusVO> statusVOList = jobManageMapper.selectProjectPackageStatusByJobIds(jobIds);
            List<String> rspUserIds = vos.stream().map(JobManageVO::getRspUserId).collect(Collectors.toList());

            Map<String, String> importantProjectMap = importantProject.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));
            Map<String, String> firstExecuteMap = firstExecute.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));
            Map<String, String> valueToName = heightRisk.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));
            Map<String, String> statusMap = JobManageBusStatusEnum.getStatusMap();
            Map<String, Integer> jobIdToStatus = statusVOList.stream().collect(Collectors.toMap(JobPackageStatusVO::getJobId, JobPackageStatusVO::getStatus));
            HashMap<Integer, String> statusToName = JobPackageStatusEnum.getMap();

            List<SimpleUser> userByIds = userRedisHelper.getSimpleUserByIds(rspUserIds);
            Map<String, SimpleUser> userIdToUser = userByIds.stream().collect(Collectors.toMap(SimpleUser::getId, Function.identity()));
            List<DataStatusVO> dataStatusVOList = dataStatusNBO.getDataStatusListByClassName(JobManage.class.getSimpleName());
            if (CollectionUtils.isEmpty(dataStatusVOList)){
                dataStatusVOList = new ArrayList<>();
            }
            final Map<Integer, DataStatusVO> statusToVo = dataStatusVOList.stream().collect(Collectors.toMap(DataStatusVO::getStatusValue, x -> x));
            vos.forEach(vo->{
                vo.setDataStatus(statusToVo.getOrDefault(vo.getStatus(), new DataStatusVO()));
                SimpleUser userById = userIdToUser.getOrDefault(vo.getRspUserId(), null);
                if(Objects.nonNull(userById)){
                    vo.setRspUserName(userById.getName());
                    vo.setRspDeptName(userById.getOrgName());
                }
                vo.setFirstExecuteName(firstExecuteMap.getOrDefault(vo.getFirstExecute(), ""));
                vo.setHeightRiskName(valueToName.getOrDefault(vo.getHeightRisk(),""));
                vo.setBusStatusName(statusMap.getOrDefault(vo.getBusStatus(),""));
                vo.setImportantProjectName(importantProjectMap.getOrDefault(vo.getImportantProject(), ""));
                //封装工作包状态
                vo.setWorkPackageStatus(String.valueOf(jobIdToStatus.getOrDefault(vo.getId(),0)));
                vo.setWorkPackageStatusName(statusToName.getOrDefault(jobIdToStatus.getOrDefault(vo.getId(),0), "未审查"));
            });
            pageResult.setContent(vos);
        }
        return pageResult;
    }

    @Override
    public Page<JobManageVO> pagesByDevelopDTO(Page<DevelopDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<JobManage> condition = new LambdaQueryWrapperX<>( JobManage. class);
        condition.orderByDesc(JobManage::getCreateTime);
        DevelopDTO query=   pageRequest.getQuery();
        String repairRound="";
        if(null != query){
            if(StringUtils.hasText(query.getPhase())){
                condition.eq(JobManage::getPhase, query.getPhase());
            }else{
                condition.in(JobManage::getPhase, JobStatusEnum.allPhase());
            }
            if(StringUtils.hasText(query.getRspDept())){
                condition.eq(JobManage::getRspDept, query.getRspDept());
            }
            if(StringUtils.hasText(query.getNOrO())){
                if(Objects.equals("O",query.getNOrO())){
                    condition.in(JobManage::getNOrO,Arrays.asList("O","T","P"));
                }
                if(Objects.equals("N",query.getNOrO())){
                    condition.in(JobManage::getNOrO,Arrays.asList("N","M"));
                }
            }
            condition.eq(JobManage::getMatchUp,StatusEnum.ENABLE.getIndex());
            condition.innerJoin(RelationOrgToJob.class, RelationOrgToJob::getJobNumber, JobManage::getNumber);
            condition.innerJoin(MajorRepairOrg.class, MajorRepairOrg::getId, RelationOrgToJob::getRepairOrgId);
            if(StringUtils.hasText(query.getRepairRound())){
                condition.eq(JobManage::getRepairRound,query.getRepairRound());
                condition.eq(MajorRepairOrg::getRepairRound,query.getRepairRound());
                repairRound=query.getRepairRound();
            }
            condition.isNotNull(JobManage::getRspDept);
        }
        Page<JobManage> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), JobManage::new));
        PageResult<JobManage> page = this.getBaseMapper().selectPage(realPageRequest, condition);
        Page<JobManageVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());

        List<JobManageVO> vos = new ArrayList<>();
        List<String> userIdList = new ArrayList<>();
        for (JobManageVO vo : vos) {
            String personId = vo.getRspUserId();
            if(StringUtils.hasText(personId)){
                userIdList.add(personId);
            }
        }
        List<UserVO> userVOList = userRedisHelper.getUserByIds(userIdList);
        Map<String, String> userIdToName = userVOList.stream().collect(Collectors.toMap(UserVO::getId, UserVO::getName));
        for (JobManage jobManage : page.getContent()) {
            JobManageVO vo = new JobManageVO();
            // 复制所有属性
            copyProperties(jobManage, vo);
            // 手动复制特定字段
            vo.setRspUserName(userIdToName.getOrDefault(vo.getRspUserId(),""));
            vos.add(vo);
        }
        setEveryName(vos,repairRound);
        pageResult.setContent(vos);
        return pageResult;
    }

    @Override
    public Page<JobPageProgressVO> getImportantProjectProgressPage(Page<JobPageProgressDTO> pageRequest) {
        LambdaQueryWrapperX<JobManage> condition = new LambdaQueryWrapperX<>(JobManage. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.innerJoin(ProjectJobDTO.class,ProjectJobDTO::getJobId,JobManage::getId);

        JobPageProgressDTO jobPageProgressDTO=  pageRequest.getQuery();
        condition.select(JobManage::getId,JobManage::getRepairRound,JobManage::getName,JobManage::getNumber,JobManage::getRspUserId);
        condition.eq(ProjectJobDTO::getProjectId,jobPageProgressDTO.getImportProjectId());
        condition.orderByDesc(JobManage::getCreateTime);
        Page<JobManage> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());

        PageResult<JobManage> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<JobPageProgressVO> result = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<JobManageVO> vos = BeanCopyUtils.convertListTo(page.getContent(), JobManageVO::new);
        List<JobPageProgressVO> jobPageProgressVOS = new ArrayList<>();
        if (!CollectionUtils.isEmpty(vos)){
            List<String> rspUserIds= new ArrayList<>();
            vos.forEach(item->{
                String rspUserId = item.getRspUserId();
                if(StringUtils.hasText(rspUserId)){
                    rspUserIds.add(rspUserId);
                }
            });
            List<SimpleUser> userByIds = userRedisHelper.getSimpleUserByIds(rspUserIds);
            Map<String, SimpleUser> userIdToUser = userByIds.stream().collect(Collectors.toMap(SimpleUser::getId, Function.identity()));

            List<String> jobIdList = new ArrayList<>();
            vos.forEach(vo->{
                JobPageProgressVO jobPageProgressVO = new JobPageProgressVO();
                SimpleUser userById = userIdToUser.getOrDefault(vo.getRspUserId(), null);
                if(Objects.nonNull(userById)){
                    jobPageProgressVO.setRspUserName(userById.getName());
                }
                jobPageProgressVO.setJobId(vo.getId());
                jobIdList.add(vo.getId());
                jobPageProgressVO.setJobName(vo.getName());
                jobPageProgressVO.setJobNumber(vo.getNumber());
                jobPageProgressVO.setImportProjectName(jobPageProgressDTO.getImportProjectName());
                jobPageProgressVOS.add(jobPageProgressVO);
            });
            Map<String,JobProgress> jobIdToProgressMap =jobProgressService.listByJobIdsAndDate(jobIdList,jobPageProgressDTO.getWorkDate());

            jobPageProgressVOS.forEach(item->{
                JobProgress jobProgress =jobIdToProgressMap.get(item.getJobId());
                if(Objects.nonNull(jobProgress)){
                    item.setWorkDate(jobProgress.getWorkDate());
                    item.setProgressSchedule(jobProgress.getProgressSchedule());
                    item.setProgressDetail(jobProgress.getProgressDetail());
                    item.setRemark(jobProgress.getRemark());
                }
            });
        }
        result.setContent(jobPageProgressVOS);
        return result;
    }


    @Override
    public List<String> getAllPhaseList() {
        return   JobStatusEnum.allPhase();
    }
    @Override
    public List<BaseStatusVO> getWorkPackageList() {
        List<BaseStatusVO> baseStatusVOList = new ArrayList<>();
        JobPackageStatusEnum[] list= JobPackageStatusEnum.values();
        for (JobPackageStatusEnum jobPackageStatusEnum : list) {
            BaseStatusVO baseStatusVO = new BaseStatusVO();
            baseStatusVO.setStatus(jobPackageStatusEnum.getStatus());
            baseStatusVO.setStatusName(jobPackageStatusEnum.getName());
            baseStatusVOList.add(baseStatusVO);
        }
        return baseStatusVOList;
    }



    @Override
    public List<JobUserVO> getPersonSimpleList(JobManageDTO query) {
        LambdaQueryWrapperX<JobManage> condition = new LambdaQueryWrapperX<>( JobManage. class);
        condition.distinct();
        condition.select(JobManage::getRspUserId);
        condition.isNotNull(JobManage::getRspUserId);
        this.setCondition(condition,query);
        List<JobManage> jobManageList =  this.list(condition);
        if(CollectionUtils.isEmpty(jobManageList)){
            return  new ArrayList<>();
        }
        List<String> rspUserIdList = new ArrayList<>();
        jobManageList.forEach(item->{
            if(Objects.nonNull(item) && StringUtils.hasText(item.getRspUserId())){
                rspUserIdList.add(item.getRspUserId());
            }
        });
        if(CollectionUtils.isEmpty(rspUserIdList)){
            return  new ArrayList<>();
        }
        List<SimpleUserVO> simpleUserVOS= userBaseApiService.getUserByIds(
                rspUserIdList.stream().distinct().collect(Collectors.toList()));
        List<JobUserVO>  jobUserVOS = new ArrayList<>();
        for (SimpleUserVO simpleUserVO : simpleUserVOS) {
            JobUserVO jobUserVO = new JobUserVO();
            jobUserVO.setCode(simpleUserVO.getCode());
            jobUserVO.setName(simpleUserVO.getName());
            jobUserVO.setId(simpleUserVO.getId());
            jobUserVOS.add(jobUserVO);
        }
        return jobUserVOS;
    }

    @Override
    public void updateNewcomer(List<String> jobIdList, Boolean bool) {
            LambdaUpdateWrapper<JobManage> updateWrapper = new LambdaUpdateWrapper<>(JobManage.class);
            updateWrapper.set(JobManage::getNewParticipants, bool);
            updateWrapper.in(JobManage::getId,jobIdList);
            this.update(updateWrapper);
    }

    @Override
    public void setNames(List<JobDownVO> relationOrgJobInfoVOList) {
        if (!CollectionUtils.isEmpty(relationOrgJobInfoVOList)){
            List<String> jobNumberList = relationOrgJobInfoVOList.stream().map(JobDownVO::getJobNumber).distinct().collect(Collectors.toList());

            List<DictValueVO> firstExecute = dictRedisHelper.getByDictNumber(DictConstant.PMS_FIRST_EXECUTE, CurrentUserHelper.getOrgId());
            List<String> rspUserIds = relationOrgJobInfoVOList.stream().map(JobDownVO::getRspUserId).collect(Collectors.toList());
            Map<String, String> firstExecuteMap = firstExecute.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));


            List<DictValueVO>  antiForfeignLevelDic = dictRedisHelper.getByDictNumber(DictConts.DUST_PROTECTION_LEVEL, CurrentUserHelper.getOrgId());
            Map<String, String>   antiForfeignLevelNameMap= antiForfeignLevelDic.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));

            Map<String, String> highRiskMap = getHighRisk(jobNumberList);
            List<SimpleUser> userByIds = userRedisHelper.getSimpleUserByIds(rspUserIds);
            Map<String, SimpleUser> userIdToUser = userByIds.stream().collect(Collectors.toMap(SimpleUser::getId, Function.identity()));
            relationOrgJobInfoVOList.forEach(vo->{
                SimpleUser userById = userIdToUser.getOrDefault(vo.getRspUserId(), null);
                if(Objects.nonNull(userById)){
                    vo.setRspUserName(userById.getName());
                }
                vo.setFirstExecuteName(firstExecuteMap.getOrDefault(vo.getFirstExecute(), ""));
                vo.setAntiForfeignLevelName(antiForfeignLevelNameMap.getOrDefault(vo.getAntiForfeignLevel(),""));
                if (vo != null ) {
                    String desc=  highRiskMap.getOrDefault(vo.getJobNumber(),"");
                    if(Objects.nonNull(vo.getIsHighRisk()) && vo.getIsHighRisk()){
                        vo.setHeightRiskLevelName(desc);
                    } else {
                        vo.setHeightRiskLevelName("");
                    }
                } else {
                    vo.setHeightRiskLevelName("");
                }
            });
        }
    }


    public void setNames(List<RelationOrgJobInfoVO> relationOrgJobInfoVOList,String repairRound) {
        if (!CollectionUtils.isEmpty(relationOrgJobInfoVOList)){
            List<String> jobNumberList = relationOrgJobInfoVOList.stream().map(RelationOrgJobInfoVO::getJobNumber).distinct().collect(Collectors.toList());

            List<DictValueVO> firstExecute = dictRedisHelper.getByDictNumber(DictConstant.PMS_FIRST_EXECUTE, CurrentUserHelper.getOrgId());
            List<String> rspUserIds = relationOrgJobInfoVOList.stream().map(RelationOrgJobInfoVO::getRspUserId).collect(Collectors.toList());
            Map<String, String> firstExecuteMap = firstExecute.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));


            List<DictValueVO>  antiForfeignLevelDic = dictRedisHelper.getByDictNumber(DictConts.DUST_PROTECTION_LEVEL, CurrentUserHelper.getOrgId());
            Map<String, String>   antiForfeignLevelNameMap= antiForfeignLevelDic.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));

            Map<String, String> highRiskMap = getHighRisk(jobNumberList);
            List<SimpleUser> userByIds = userRedisHelper.getSimpleUserByIds(rspUserIds);
            Map<String, SimpleUser> userIdToUser = userByIds.stream().collect(Collectors.toMap(SimpleUser::getId, Function.identity()));
            relationOrgJobInfoVOList.forEach(vo->{
                SimpleUser userById = userIdToUser.getOrDefault(vo.getRspUserId(), null);
                if(Objects.nonNull(userById)){
                    vo.setRspUserName(userById.getName());
                }
                vo.setFirstExecuteName(firstExecuteMap.getOrDefault(vo.getFirstExecute(), ""));
                vo.setAntiForfeignLevelName(antiForfeignLevelNameMap.getOrDefault(vo.getAntiForfeignLevel(),""));
                if (vo != null ) {
                    String desc=  highRiskMap.getOrDefault(vo.getJobNumber(),"");
                    if(Objects.nonNull(vo.getIsHighRisk()) && vo.getIsHighRisk()){
                        vo.setHeightRiskLevelName(desc);
                    } else {
                        vo.setHeightRiskLevelName("");
                    }
                } else {
                    vo.setHeightRiskLevelName("");
                }
            });
        }

    }

    @Override
    public void removeByJobNumberList(List<String> jobNumberList) {
        LambdaQueryWrapperX<JobManage> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(JobManage.class);
        lambdaQueryWrapperX.in(JobManage::getNumber,jobNumberList);
        this.remove(lambdaQueryWrapperX);
    }

    @Override
    public Map<String, String> getNumberToList(List<String> jobNumberList) {
        if (CollectionUtils.isEmpty(jobNumberList)) {
            return new HashMap<>();
        }
        // 这里查询 所有工单 对已删除的数据进行更新
        List<JobManage> jobManageList=  jobManageMapper.listByNumberList(jobNumberList);
        if (!CollectionUtils.isEmpty(jobManageList)) {
            return jobManageList.stream().collect(Collectors.toMap(JobManage::getNumber, JobManage::getId));
        }
        return new HashMap<>();
    }

    @Override
    public List<SafetyQualityEnvVO> relationToSafetyQualityEnv(String projectId,String jobManageId, JobManageDTO planQueryDTO) throws Exception {
        List<JobToSafetyQualityEnv> relationList = jobToSafetyQualityEnvService.list(
                new LambdaQueryWrapper<>(JobToSafetyQualityEnv.class)
                        .eq(JobToSafetyQualityEnv::getJobManageId, jobManageId)
                .eq(JobToSafetyQualityEnv::getProjectId, projectId));
        if (CollectionUtils.isEmpty(relationList)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<SafetyQualityEnv> condition = new LambdaQueryWrapper<>();
        List<String> demandIdList = relationList.stream().map(JobToSafetyQualityEnv::getSafetyQualityEnvId).collect(Collectors.toList());
        condition.in(SafetyQualityEnv::getId, demandIdList);
        //查询条件待定
//        if (Objects.nonNull(planQueryDTO)) {
//            if (StrUtil.isNotBlank(planQueryDTO.getKeyword())) {
//                condition.and(sub -> sub.like(SafetyQualityEnv::getAssessmentLevel, planQueryDTO.getKeyword()).or().like(SafetyQualityEnv::getEventType, planQueryDTO.getKeyword()));
//            }
//        }
        List<SafetyQualityEnv> safetyQualityEnvs = safetyQualityEnvService.list(condition);
        if (CollectionUtils.isEmpty(safetyQualityEnvs)) {
            return new ArrayList<>();
        }
        List<SafetyQualityEnvVO> safetyQualityEnvVOList = BeanCopyUtils.convertListTo(safetyQualityEnvs, SafetyQualityEnvVO::new);
        safetyQualityEnvService.setEveryName(safetyQualityEnvVOList);
        return safetyQualityEnvVOList;
    }

    @Override
    public Boolean removeRelationToSafetyQualityEnv(FromIdsRelationDTO fromIdsRelationDTO) {
        jobToSafetyQualityEnvService.remove(new LambdaQueryWrapper<>(JobToSafetyQualityEnv.class)
                .eq(JobToSafetyQualityEnv::getJobManageId, fromIdsRelationDTO.getJobManageId())
                .eq(JobToSafetyQualityEnv::getProjectId, fromIdsRelationDTO.getProjectId())
                .in(JobToSafetyQualityEnv::getSafetyQualityEnvId, fromIdsRelationDTO.getSafetyQualityEnvId()));
        return true;
    }

    @Override
    public Boolean relationToSafetyQualityEnv(FromIdsRelationDTO fromIdsRelationDTO) {
        List<JobToSafetyQualityEnv> old = jobToSafetyQualityEnvService.list(new LambdaQueryWrapper<>(JobToSafetyQualityEnv.class)
                .in(JobToSafetyQualityEnv::getSafetyQualityEnvId, fromIdsRelationDTO.getSafetyQualityEnvId())
                .eq(JobToSafetyQualityEnv::getProjectId, fromIdsRelationDTO.getProjectId())
                .eq(JobToSafetyQualityEnv::getJobManageId, fromIdsRelationDTO.getJobManageId()));
        if (!CollectionUtils.isEmpty(old)) {
            throw new RuntimeException("数据已经存在不能再次创建");
        }

        List<JobToSafetyQualityEnv> list = new ArrayList<>();

        for (String fromId : fromIdsRelationDTO.getSafetyQualityEnvId()) {
            JobToSafetyQualityEnv jobToSafetyQualityEnv = new JobToSafetyQualityEnv();
            jobToSafetyQualityEnv.setSafetyQualityEnvId(fromId);
            jobToSafetyQualityEnv.setProjectId(fromIdsRelationDTO.getProjectId());
            jobToSafetyQualityEnv.setJobManageId(fromIdsRelationDTO.getJobManageId());
            list.add(jobToSafetyQualityEnv);
        }
        jobToSafetyQualityEnvService.saveBatch(list);
        return Boolean.TRUE;
    }


    @Override
    public String getBaseCodeByJobId(String jobId) {
        if(StringUtils.isEmpty(jobId)){
            return null;
        }
        LambdaQueryWrapperX<JobManage> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(JobManage.class);
        lambdaQueryWrapperX.eq(JobManage::getId,jobId);
        lambdaQueryWrapperX.select(JobManage::getId,JobManage::getJobBase);
        List<JobManage> jobManageList = this.list(lambdaQueryWrapperX);
        if(CollectionUtils.isEmpty(jobManageList)){
           return  null;
        }
        return jobManageList.get(0).getJobBase();
    }

    public Date setDateTime(Integer duration,Date date){
        if(!Objects.isNull(duration) && Objects.nonNull(date)){
            Calendar calo = Calendar.getInstance();
            calo.setTime(date);
            calo.add(Calendar.DATE, duration);
            return calo.getTime();
        }
        return null;
    }

    @Getter
    public static class JobManageWPStatusExcelTplListener extends AnalysisEventListener<JobManageWPStatusExcelTpl> {
        private final List<JobManageWPStatusExcelTpl> data = new ArrayList<>();

        @Override
        public void invoke(JobManageWPStatusExcelTpl dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        }
    }

}
