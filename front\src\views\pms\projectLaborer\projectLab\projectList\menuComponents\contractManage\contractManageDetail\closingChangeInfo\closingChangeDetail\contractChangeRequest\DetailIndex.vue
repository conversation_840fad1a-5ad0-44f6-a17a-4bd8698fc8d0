<template>
  <DetailsLayout
    title="合同基本信息"
    :column="3"
    :data-source="detail?.projectContractVO"
    :list="basicList"
  >
    <!--    <template #contractMoney="{text,record}">-->
    <!--      <div class="slot-wrap">-->
    <!--        &lt;!&ndash; <span>{{ formatMoney(text) }}</span> &ndash;&gt;-->
    <!--        <span class="label">币种：</span>-->
    <!--        <span class="value">{{ record?.currency }}</span>-->
    <!--      </div>-->
    <!--    </template>-->
  </DetailsLayout>
  <DetailsLayout
    title="变更申请信息"
    :list="alterationInfo"
    :data-source="detail?.projectContractChangeApplyVO"
  />

  <DetailsLayout
    title="合同变更内容"
  >
    <div
      style="height: 220px;overflow: hidden"
    >
      <OrionTable
        ref="contentTableRef"
        :options="contentTable"
      />
    </div>
  </DetailsLayout>
</template>

<script setup lang="ts">

import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import {
  computed, inject,
  nextTick, onMounted, provide, reactive, ref, Ref, unref, toRef,
} from 'vue';
import { OrionTable } from 'lyra-component-vue3';
import { useRoute } from 'vue-router';
import log
  from '/src/views/pms/projectLaborer/projectLab/projectList/menuComponents/planManagement/planDetails/log/index.vue';
// import { contractDetailKey } from '../../type';
// import { formatMoney } from '/@/views/pms/projectManage/utils';

// const contractDetail = inject(contractDetailKey);
const projectContractDetail: Ref = ref({});
const route = useRoute();
// const state = reactive({
//   allData: inject('allData'),
// });

const detail:any = inject('allData', {});

// const projectNumber:string = inject('projectNumber');
// 合同主体信息
const basicList = [
  {
    label: '合同名称',
    field: 'name',
  },
  {
    label: '合同编号',
    field: 'number',
  },
  {
    label: '合同类别',
    field: 'contractCategory',
  },
  {
    label: '合同类型',
    field: 'contractType',
  },
  {
    label: '合同负责人',
    field: 'principalName',
  },

  {
    label: '合同负责人工号',
    field: 'principalCode',
  },

  {
    label: '合同负责部门',
    field: 'rspDeptName',
  },
  {
    label: '合同总金额',
    field: 'contractMoney',
    isMoney: true,
  },
  {
    label: '合同币种',
    field: 'currency',
  },
  {
    label: '合同创建人',
    field: 'creatorName',
  },
  {
    label: '合同创建人工号',
    field: 'creatorId',
  },
  {
    label: '合同开始日期',
    field: 'startDate',
    formatTime: 'YYYY-MM-DD',
  },

  {
    label: '合同结束日期',
    field: 'endDate',
    formatTime: 'YYYY-MM-DD',
  },
  {
    label: '合同签订日期',
    field: 'startDate',
    formatTime: 'YYYY-MM-DD',
  },
  {
    label: '合同创建时间',
    field: 'createTime',
    formatTime: 'YYYY-MM-DD',
  },
];

// 变更信息
const alterationInfo = [
  {
    label: '是否具有质保期',
    field: 'number',
  },
  {
    label: '变更申请人',
    field: 'applyUserName',
  },
  {
    label: '合同变更申请时间',
    field: 'applyDate',
  },
  {
    label: '合同变更原因',
    field: 'changeReason',
  },

];

// 合同变更内容
const contentTableRef:Ref = ref();
const contentTable = reactive({
  pagination: false,
  showSmallSearch: false,
  showToolButton: false,
  showTableSetting: false,
  rowKey: 'id',
  columns: [
    {
      title: '变更要素',
      dataIndex: 'fieldName',
    },
    {
      title: '变更前',
      dataIndex: 'oldValue',
    },
    {
      title: '变更后',
      dataIndex: 'newValue',
    },
  ],
});

onMounted(() => {
  getDetail();
});

// 初始化数据
async function getDetail() {
  nextTick(() => {
    // let contractSupplierSignedMainVO = [];
    // contractSupplierSignedMainVO.push(detail.value?.contractSupplierSignedMainVO);
    // contentTableRef.value?.setTableData(detail.value?projectContractChangeVOList);
  });
}

</script>

<style scoped lang="less">
.slot-wrap{
  white-space: nowrap;
  display: flex;
  align-items: center;
  flex-grow: 1;
  width: 0;

  .label{
    color: ~`getPrefixVar('primary-10')`;
    margin-left: auto;
  }
  .value{
    margin-right: auto;
  }
}
</style>
