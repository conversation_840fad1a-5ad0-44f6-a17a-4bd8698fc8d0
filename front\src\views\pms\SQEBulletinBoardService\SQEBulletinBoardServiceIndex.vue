<!--
 * @Description:安置环看板维护
 * @Autor: laotao117
 * @Date: 2024-08-22 16:09:23
 * @LastEditors: laotao117
 * @LastEditTime: 2024-08-27 14:36:11
-->
<template>
  <Layout
    v-get-power="{ pageCode: 'PMSSQEBulletinBoardServiceIndex', getPowerDataHandle }"
    :options="{ body: { scroll: true } }"
  >
    <div
      v-if="state.loading"
      class="w-full h-full flex flex-pac"
    >
      <Spin />
    </div>
    <div class="page-com">
      <!-- Tabs -->
      <Tabs>
        <TabPane
          key="1"
          tab="考核指标"
        >
          <Tab1 />
        </TabPane>
        <TabPane
          key="2"
          tab="安全生产考核"
        >
          <Tab2 />
        </TabPane>
        <!-- 隐患排查 -->
        <TabPane
          key="3"
          tab="隐患排查"
        >
          <Tab3 />
        </TabPane>
        <!-- 作业信息 -->
        <TabPane
          key="4"
          tab="作业信息"
        >
          <Tab4 />
        </TabPane>
      </Tabs>
    </div>
  </Layout>
</template>
<script setup lang="ts">
import {
  Layout,
} from 'lyra-component-vue3';
import {
  provide,
  reactive,
} from 'vue';
// tabs
import { TabPane, Tabs } from 'ant-design-vue';
import Tab1 from './components/tab1.vue';
import Tab2 from './components/tab2.vue';
import Tab3 from './components/tab3.vue';
import Tab4 from './components/tab4.vue';
// state
import { usePagePower } from '/@/views/pms/hooks';
const { powerData, getPowerDataHandle } = usePagePower();
provide('powerData', powerData);
const state: any = reactive({
  loading: false,
});

</script>
<style scoped lang="less">
.page-com {
    padding: 10px 20px;
}

.mb20 {
    margin-bottom: 20px;
}

</style>