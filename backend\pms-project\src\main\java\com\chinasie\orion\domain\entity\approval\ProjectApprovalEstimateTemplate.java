package com.chinasie.orion.domain.entity.approval;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * ProjectApprovalEstimateTemplate Entity对象
 *
 * <AUTHOR>
 * @since 2024-04-28 17:21:24
 */
@TableName(value = "pms_project_approval_estimate_template")
@ApiModel(value = "ProjectApprovalEstimateTemplateEntity对象", description = "概算模板")
@Data
public class ProjectApprovalEstimateTemplate extends ObjectEntity implements Serializable{

    /**
    * 编号
    */
    @ApiModelProperty(value = "编号")
    @TableField(value = "number")
    private String number;


    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField(value = "name")
    private String name;

    /**
     * 科目数量
     */
    @ApiModelProperty(value = "科目数量")
    @TableField(value = "subject_number")
    private Integer subjectNumber;

    /**
     * 所属分类ID
     */
    @ApiModelProperty(value = "所属分类ID")
    @TableField(value = "template_classify_id")
    private String templateClassifyId;
}
