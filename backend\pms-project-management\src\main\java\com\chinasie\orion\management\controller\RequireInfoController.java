package com.chinasie.orion.management.controller;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.dto.RequireInfoDTO;
import com.chinasie.orion.management.domain.vo.RequireInfoTotalVO;
import com.chinasie.orion.management.domain.vo.RequireInfoVO;
import com.chinasie.orion.management.service.RequireInfoService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * RequireInfo 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@RestController
@RequestMapping("/requireInfo")
@Api(tags = "需求单")
public class RequireInfoController {

    @Autowired
    private RequireInfoService requireInfoService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "需求单", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<RequireInfoVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        RequireInfoVO rsp = requireInfoService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param requireInfoDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#requireInfoDTO.name}}】", type = "需求单", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody RequireInfoDTO requireInfoDTO) throws Exception {
        String rsp = requireInfoService.create(requireInfoDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param requireInfoDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#requireInfoDTO.name}}】", type = "需求单", subType = "编辑", bizNo = "{{#requireInfoDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody RequireInfoDTO requireInfoDTO) throws Exception {
        Boolean rsp = requireInfoService.edit(requireInfoDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "需求单", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = requireInfoService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "需求单", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = requireInfoService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "需求单", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page/{mainTableId}", method = RequestMethod.POST)
    public ResponseDTO<Page<RequireInfoVO>> pages(@PathVariable("mainTableId") String mainTableId, @RequestBody Page<RequireInfoDTO> pageRequest) throws Exception {
        Page<RequireInfoVO> rsp = requireInfoService.pages(mainTableId, pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 根据合同编号查询需求单信息
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "根据合同编号查询需求单信息")
    @LogRecord(success = "【{USER{#logUserId}}】根据合同编号查询需求单信息", type = "ContractLineInfo", subType = "根据合同编号查询需求单信息", bizNo = "")
    @RequestMapping(value = "/getByCode", method = RequestMethod.POST)
    public ResponseDTO<List<RequireInfoVO>> getRequireInfoByCode(@RequestBody RequireInfoDTO requireInfoDTO) throws Exception {
        List<RequireInfoVO> rsp = requireInfoService.getByCode(requireInfoDTO);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("需求单导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "需求单", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        requireInfoService.downloadExcelTpl(response);
    }

    @ApiOperation("需求单导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "需求单", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = requireInfoService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("需求单导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "需求单", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = requireInfoService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消需求单导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "需求单", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = requireInfoService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("需求单导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "RequireInfo", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody RequireInfoDTO requireInfoDTO, HttpServletResponse response) throws Exception {
        requireInfoService.exportByExcel(requireInfoDTO, response);
    }


    @ApiOperation(value = "获取需求单金额数据")
    @LogRecord(success = "【{USER{#logUserId}}】获取需求单金额数据", type = "RequireInfoVO", subType = "查询", bizNo = "")
    @RequestMapping(value = "/getTotal", method = RequestMethod.POST)
    public ResponseDTO<RequireInfoTotalVO> getByCode(@RequestBody RequireInfoDTO requireInfoDTO) throws Exception {
        RequireInfoTotalVO rsp = requireInfoService.getRequireInfoTotal(requireInfoDTO);
        return new ResponseDTO<>(rsp);
    }
}
