package com.chinasie.orion.service;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.humanResource.ProjectHumanResourceSettingDTO;
import com.chinasie.orion.domain.dto.humanResource.ProjectHumanResourcePageDTO;
import com.chinasie.orion.domain.entity.humanResource.ProjectHumanResourceSetting;
import com.chinasie.orion.domain.vo.humanResource.ProjectHumanResourcePageVO;
import com.chinasie.orion.domain.vo.humanResource.ProjectHumanResourceSettingVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

/**
 * <p>
 * ProjectHumanResource 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-15 17:57:05
 */
public interface ProjectHumanResourceServiceSetting extends OrionBaseService<ProjectHumanResourceSetting> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    ProjectHumanResourceSettingVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param projectHumanResourceDTO
     */
    String create(ProjectHumanResourceSettingDTO projectHumanResourceSettingDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param projectHumanResourceDTO
     */
    Boolean edit(ProjectHumanResourceSettingDTO projectHumanResourceSettingDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ProjectHumanResourceSettingVO> pages(Page<ProjectHumanResourceSettingDTO> pageRequest) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    List<ProjectHumanResourcePageVO> humanResourcePages(ProjectHumanResourcePageDTO projectHumanResourcePageDTO) throws Exception;

}
