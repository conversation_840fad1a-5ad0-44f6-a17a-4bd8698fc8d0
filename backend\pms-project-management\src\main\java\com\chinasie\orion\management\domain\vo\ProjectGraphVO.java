package com.chinasie.orion.management.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;
import java.lang.Integer;

import java.util.List;

/**
 * ProjectGraph VO对象
 *
 * <AUTHOR>
 * @since 2024-06-22 14:42:47
 */
@ApiModel(value = "ProjectGraphVO对象", description = "技术人员统计表")
@Data
public class ProjectGraphVO extends ObjectVO implements Serializable {

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    private Integer indexOrder;


    /**
     * 指标名称
     */
    @ApiModelProperty(value = "指标名称")
    private String indexName;


    /**
     * 一月
     */
    @ApiModelProperty(value = "一月")
    private String jan;


    /**
     * 二月
     */
    @ApiModelProperty(value = "二月")
    private String feb;


    /**
     * 三月
     */
    @ApiModelProperty(value = "三月")
    private String mar;


    /**
     * 第一季度
     */
    @ApiModelProperty(value = "第一季度")
    private String firstQuarter;


    /**
     * 四月
     */
    @ApiModelProperty(value = "四月")
    private String apr;


    /**
     * 五月
     */
    @ApiModelProperty(value = "五月")
    private String may;


    /**
     * 六月
     */
    @ApiModelProperty(value = "六月")
    private String jun;


    /**
     * 七月
     */
    @ApiModelProperty(value = "七月")
    private String jul;


    /**
     * 八月
     */
    @ApiModelProperty(value = "八月")
    private String aug;


    /**
     * 第二季度
     */
    @ApiModelProperty(value = "第二季度")
    private String secondQuarter;


    /**
     * 九月
     */
    @ApiModelProperty(value = "九月")
    private String sept;


    /**
     * 第三季度
     */
    @ApiModelProperty(value = "第三季度")
    private String thirdQuarter;


    /**
     * 十月
     */
    @ApiModelProperty(value = "十月")
    private String oct;


    /**
     * 十一月
     */
    @ApiModelProperty(value = "十一月")
    private String nov;


    /**
     * 十二月
     */
    @ApiModelProperty(value = "十二月")
    private String dece;


    /**
     * 第四季度
     */
    @ApiModelProperty(value = "第四季度")
    private String fourthQuarter;


    /**
     * 年度
     */
    @ApiModelProperty(value = "年度")
    private String indexYear;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String projectGraphType;

}
