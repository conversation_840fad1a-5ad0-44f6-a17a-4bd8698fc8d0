package com.chinasie.orion.management.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * SupplierProducts VO对象
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@ApiModel(value = "SupplierProductsVO对象", description = "可提供产品")
@Data
public class SupplierProductsVO extends ObjectVO implements Serializable {

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    private String mainTableId;


    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;


    /**
     * 可提供产品一级
     */
    @ApiModelProperty(value = "可提供产品一级")
    private String productLevelOne;


    /**
     * 可提供产品二级
     */
    @ApiModelProperty(value = "可提供产品二级")
    private String productLevelTwo;


    /**
     * 可提供产品三级
     */
    @ApiModelProperty(value = "可提供产品三级")
    private String productLevelThree;


    /**
     * 可提供产品四级
     */
    @ApiModelProperty(value = "可提供产品四级")
    private String productLevelFour;


}
