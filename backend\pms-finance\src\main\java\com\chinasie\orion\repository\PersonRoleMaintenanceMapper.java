package com.chinasie.orion.repository;

import com.chinasie.orion.domain.entity.PersonRoleMaintenance;
import com.chinasie.orion.domain.vo.PersonRoleMaintenanceVO;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * <p>
 * PersonRoleMaintenance Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08 17:28:59
 */
@Mapper
public interface PersonRoleMaintenanceMapper extends  OrionBaseMapper  <PersonRoleMaintenance> {
    List<String> personnelSearch(String serchValue);
    List<PersonRoleMaintenanceVO> searchStationName(String id);
}

