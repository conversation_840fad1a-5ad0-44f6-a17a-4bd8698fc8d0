import Api from '/@/api';

/**
 * 获取项目奖惩情况列表
 * @param projectId 项目id
 */
export const getList = (projectId) => new Api(`/pms/projectRewardPunishment/getList?projectId=${projectId}`).fetch('', '', 'GET');
/**
 * 保存项目奖惩情况
 * @param projectId 项目id
 */
export const saveOrRemove = (projectId, params) => new Api(`/pms/projectRewardPunishment/saveOrRemove?projectId=${projectId}`).fetch(params, '', 'POST');
