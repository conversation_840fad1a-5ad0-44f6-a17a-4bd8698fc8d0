import BaseRenderer from 'diagram-js/lib/draw/BaseRenderer';

import {
  append as svgAppend,
  attr as svgAttr,
  classes as svgClasses,
  create as svgCreate,
  remove as svgRemove,
  select,
  selectAll,
} from 'tiny-svg';

import { getRoundRectPath } from 'bpmn-js/lib/draw/BpmnRenderUtil';

import { is } from 'bpmn-js/lib/util/ModelUtil';

const HIGH_PRIORITY = 1500;
const TASK_BORDER_RADIUS = 2;

const completeColor = '#0960BD';

export default class CustomRenderer extends BaseRenderer {
  constructor(eventBus, bpmnRenderer) {
    super(eventBus, HIGH_PRIORITY);

    this.bpmnRenderer = bpmnRenderer;
    this.completeActivity = null;
  }

  canRender(element) {
    return true;
  }

  drawShape(parentNode, element) {
    const isDo = element.businessObject.$attrs['activiti:assignee']
      && element.businessObject.$attrs['activiti:assignee'] === '${startBy}';
    const shape = this.bpmnRenderer.drawShape(parentNode, element);
    if (isDo) {
      let customIcon = svgCreate('rect');
      svgAttr(customIcon, {
        x: 0,
        y: 0,
        width: 100,
        height: 80,
        rx: 10,
        ry: 10,
        fill: '#fff',
        stroke: '#000',
        strokeWidth: 1.5,
      });
      svgAppend(parentNode, customIcon);
      // 渲染文字
      if (element.businessObject.name) {
        const wrapperText = svgCreate('text', {
          lineHeight: 1.2,
          x: 30,
          style:
            'font-family: Arial, sans-serif; font-size: 12px; font-weight: normal; fill: black;',
        });
        svgAppend(parentNode, wrapperText);
        wrapperText.innerHTML = element.businessObject.name;
        const nodeWidth = wrapperText.getBoundingClientRect().width;
        svgAttr(wrapperText, {
          x: (100 - nodeWidth) / 2,
          y: 43,
        });
      }
      // 渲染图标
      const leftIcon = svgCreate('image', {
        href: '/images/do-left.jpg',
        width: 20,
        height: 20,
        x: 4,
        y: 4,
      });
      svgAppend(parentNode, leftIcon);
      if (this.completeActivity && this.completeActivity[element.id]) {
        svgAttr(customIcon, {
          stroke: completeColor,
          strokeWidth: 2,
          fill: '#fff',
        });
      }
      return customIcon;
    }
    if (this.completeActivity && this.completeActivity[element.id]) {
      svgAttr(shape, {
        stroke: completeColor,
        strokeWidth: 2,
        fill: '#fff',
      });
    }

    return shape;
  }

  _removeMidddleText(parentNode) {
    const text = select(parentNode, 'text');
    const tspan = select(text, 'tspan');
    const flowVal = tspan.textContent;
    return {
      text,
      flowVal,
    };
  }

  getShapePath(shape) {
    if (is(shape, 'bpmn:Task')) {
      return getRoundRectPath(shape, TASK_BORDER_RADIUS);
    }

    return this.bpmnRenderer.getShapePath(shape);
  }

  setComplete(completeActivity) {
    this.completeActivity = completeActivity || null;
  }
}

CustomRenderer.$inject = ['eventBus', 'bpmnRenderer'];
