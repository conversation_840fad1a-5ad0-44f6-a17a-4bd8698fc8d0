<script setup lang="ts">
import { BasicModal, useModalInner } from 'lyra-component-vue3';
import { Ref, ref } from 'vue';
import { message } from 'ant-design-vue';
import TableMain from './TableMain.vue';
import Api from '/@/api';

const emits = defineEmits<{
  (e:'checkProjectCallback', data):void
}>();

const [register, { closeModal, changeOkLoading }] = useModalInner(() => {
  visibleModal.value = true;
});

const tableRef:Ref = ref();
const visibleModal:Ref<boolean> = ref(false);

function visibleChange(visible: boolean) {
  // 窗口关闭时，设置状态值
  !visible && (visibleModal.value = visible);
}

async function onOk() {
  const selectRows = tableRef.value.getSelectRows();
  if (selectRows.length) {
    const data:Record<string, any> = selectRows;
    changeOkLoading(true);
    try {
      emits('checkProjectCallback', data);
      closeModal();
    } finally {
      changeOkLoading(false);
    }
  } else {
    message.warn('请选择采购订单');
  }
}
</script>

<template>
  <BasicModal
    v-bind="$attrs"
    width="1200px"
    :height="500"
    title="选择采购订单"
    :onVisibleChange="visibleChange"
    @register="register"
    @ok="onOk"
  >
    <TableMain
      v-if="visibleModal"
      ref="tableRef"
    />
  </BasicModal>
</template>

<style scoped lang="less">

</style>
