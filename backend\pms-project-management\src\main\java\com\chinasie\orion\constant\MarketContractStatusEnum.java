package com.chinasie.orion.constant;

/**
 * @author: yk
 * @date: 2023/10/25 20:40
 * @description:
 */
public enum MarketContractStatusEnum {
    CREATED(101, "编制中"),
    AUDITING(110, "审核中"),
    SIGNING(121, "待签署"),
    FULFIL(130, "履行中"),
    COMPLATED(160, "已完成"),
    PREDISTRIBUTE(140, "待分发"),
    DISTRIBUTED(111, "已下发"),
    ;


    private Integer status;

    private String desc;


    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    MarketContractStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
