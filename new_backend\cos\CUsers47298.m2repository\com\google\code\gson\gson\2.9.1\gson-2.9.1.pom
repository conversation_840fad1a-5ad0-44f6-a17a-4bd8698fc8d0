<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.google.code.gson</groupId>
    <artifactId>gson-parent</artifactId>
    <version>2.9.1</version>
  </parent>

  <artifactId>gson</artifactId>
  <name>Gson</name>

  <licenses>
    <license>
      <name>Apache-2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <executions>
          <execution>
            <id>default-compile</id>
            <configuration>
              <excludes>
                <!-- module-info.java is compiled using ModiTect -->
                <exclude>module-info.java</exclude>
              </excludes>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <!-- Note: Javadoc plugin has to be run in combination with >= `package` 
        phase, e.g. `mvn package javadoc:javadoc`, otherwise it fails with
        "Aggregator report contains named and unnamed modules" -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>3.0.0-M7</version>
        <configuration>
          <!-- Deny illegal access, this is required for ReflectionAccessTest -->
          <!-- Requires Java >= 9; Important: In case future Java versions 
            don't support this flag anymore, don't remove it unless CI also runs with 
            that Java version. Ideally would use toolchain to specify that this should 
            run with e.g. Java 11, but Maven toolchain requirements (unlike Gradle ones) 
            don't seem to be portable (every developer would have to set up toolchain 
            configuration locally). -->
          <argLine>--illegal-access=deny</argLine>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <configuration>
          <excludePackageNames>com.google.gson.internal:com.google.gson.internal.bind</excludePackageNames>
        </configuration>
      </plugin>
      <!-- Add module-info to JAR, see https://github.com/moditect/moditect#adding-module-descriptors-to-existing-jar-files -->
      <!-- Uses ModiTect instead of separate maven-compiler-plugin executions 
        for better Eclipse IDE support, see https://github.com/eclipse-m2e/m2e-core/issues/393 -->
      <plugin>
        <groupId>org.moditect</groupId>
        <artifactId>moditect-maven-plugin</artifactId>
        <version>1.0.0.RC2</version>
        <executions>
          <execution>
            <id>add-module-info</id>
            <phase>package</phase>
            <goals>
              <goal>add-module-info</goal>
            </goals>
            <configuration>
              <jvmVersion>9</jvmVersion>
              <module>
                <moduleInfoFile>${project.build.sourceDirectory}/module-info.java</moduleInfoFile>
              </module>
              <!-- Overwrite the previously generated JAR file, if any -->
              <overwriteExistingFiles>true</overwriteExistingFiles>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>biz.aQute.bnd</groupId>
        <artifactId>bnd-maven-plugin</artifactId>
        <version>6.3.1</version>
        <executions>
          <execution>
            <goals>
              <goal>bnd-process</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive>
            <!-- Use existing manifest generated by BND plugin -->
            <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>templating-maven-plugin</artifactId>
        <version>1.0.0</version>
        <executions>
          <execution>
            <id>filtering-java-templates</id>
            <goals>
              <goal>filter-sources</goal>
            </goals>
            <configuration>
              <sourceDirectory>${basedir}/src/main/java-templates</sourceDirectory>
              <outputDirectory>${project.build.directory}/generated-sources/java-templates</outputDirectory>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>com.coderplus.maven.plugins</groupId>
        <artifactId>copy-rename-maven-plugin</artifactId>
        <version>1.0.1</version>
        <executions>
          <execution>
            <id>pre-obfuscate-class</id>
            <phase>process-test-classes</phase>
            <goals>
              <goal>rename</goal>
            </goals>
            <configuration>
              <fileSets>
                <fileSet>
                  <sourceFile>${project.build.directory}/test-classes/com/google/gson/functional/EnumWithObfuscatedTest.class</sourceFile>
                  <destinationFile>${project.build.directory}/test-classes-obfuscated-injar/com/google/gson/functional/EnumWithObfuscatedTest.class</destinationFile>
                </fileSet>
                <fileSet>
                  <sourceFile>${project.build.directory}/test-classes/com/google/gson/functional/EnumWithObfuscatedTest$Gender.class</sourceFile>
                  <destinationFile>${project.build.directory}/test-classes-obfuscated-injar/com/google/gson/functional/EnumWithObfuscatedTest$Gender.class</destinationFile>
                </fileSet>
              </fileSets>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>com.github.wvengen</groupId>
        <artifactId>proguard-maven-plugin</artifactId>
        <version>2.6.0</version>
        <executions>
          <execution>
            <phase>process-test-classes</phase>
            <goals>
              <goal>proguard</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <obfuscate>true</obfuscate>
          <injar>test-classes-obfuscated-injar</injar>
          <outjar>test-classes-obfuscated-outjar</outjar>
          <inFilter>**/*.class</inFilter>
          <proguardInclude>${basedir}/src/test/resources/testcases-proguard.conf</proguardInclude>
          <libs>
            <lib>${project.build.directory}/classes</lib>
            <lib>${java.home}/jmods/java.base.jmod</lib>
          </libs>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-resources-plugin</artifactId>
        <version>3.3.0</version>
        <executions>
          <execution>
            <id>post-obfuscate-class</id>
            <phase>process-test-classes</phase>
            <goals>
              <goal>copy-resources</goal>
            </goals>
            <configuration>
              <outputDirectory>${project.build.directory}/test-classes/com/google/gson/functional</outputDirectory>
              <resources>
                <resource>
                  <directory>${project.build.directory}/test-classes-obfuscated-outjar/com/google/gson/functional</directory>
                  <includes>
                    <include>EnumWithObfuscatedTest.class</include>
                    <include>EnumWithObfuscatedTest$Gender.class</include>
                  </includes>
                </resource>
              </resources>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
