import {
  computed, ComputedRef, h, ref, Ref, unref,
} from 'vue';
import Api from '/@/api';
import { BasicImport, downloadByData, useModal } from 'lyra-component-vue3';
import { Modal } from 'ant-design-vue';

interface ImportUrls {
    downloadUrl: string;
    checkUrl: string;
    importUrl: string;
    cancelUrl?: string;
}

export function getImportProps(urls: ImportUrls, cb: Function) {
  const downloadFileObj = {
    url: urls.downloadUrl,
    method: 'GET',
  };

  function requestBasicImport(files: any[]) {
    const formData = new FormData();
    formData.append('file', files[0]);
    return new Api(urls.checkUrl).fetch(formData, '', 'POST');
  }

  function requestSuccessImport(importId: string) {
    return new Promise((resolve, reject) => {
      new Api(`${urls.importUrl}/${importId}`).fetch('', '', 'POST').then((res) => {
        cb?.(res);
        resolve(true);
      }).catch((e) => {
        reject(e);
      });
    });
  }

  function onChangeImportModalFlag({ succ, successImportFlag }) {
    if (!successImportFlag && succ && urls.cancelUrl) {
      return new Api(`${urls.cancelUrl}/${succ}`).fetch('', '', 'POST');
    }
  }

  return {
    downloadFileObj,
    requestBasicImport,
    requestSuccessImport,
    onChangeImportModalFlag,
  };
}

// 大修第二层作业详情导入、导出
export function useJobExcel(): {
    OrderImportRender: Function
    importApi: Function
    exportApi: Function
    } {
  const [register, { openModal: importApi }] = useModal();
  const OrderImportRender = (props: {
        repairRound: string
        cb: Function
    }) => h(BasicImport, {
    onRegister: register,
    ...getImportProps({
      downloadUrl: '/pms/job-manage/download/wp/excel/tpl',
      checkUrl: '/pms/contractInfo/import/excel/check',
      importUrl: '/pms/job-manage/import/wp/excel',
    }, props.cb),
  });

  // 导出
  function exportApi(params: {
        idList?: string[]
        repairRound: string
    }) {
    Modal.confirm({
      title: '系统提示！',
      content: params?.idList?.length > 0 ? '确认导出所选数据？' : '确认导出全部数据？',
      onOk() {
        return new Promise((resolve) => {
          downloadByData('/pms/job-manage/export/excel', params).then(() => {
            resolve('');
          }).catch((e) => {
            resolve(e);
          });
        });
      },
    });
  }

  return {
    OrderImportRender,
    importApi,
    exportApi,
  };
}

// 大修第二层人员管理导入、导出
export function usePersonExcel() {
  const [register, { openModal: openImportModal }] = useModal();
  const activeKey: Ref<number> = ref(0);
  const importTypes: any[] = [
    {
      name: '计划入场信息导入',
      key: 0,
    },
    {
      name: '实际入场信息导入',
      key: 1,
    },
    {
      name: '实际离场信息导入',
      key: 2,
    },
  ];

  const title: ComputedRef<string> = computed(() => importTypes[activeKey.value].name);

  const OrderImportRender = (props: {
        repairRound: string
        cb: Function
        title: string
    }) => h(BasicImport, {
    title: unref(title),
    onRegister: register,
    downloadFileObj: activeKey.value === 0 ? {
      url: `/pms/schemeToPerson/plan/download/excel/tpl/${props.repairRound}`,
      method: 'GET',
    } : activeKey.value === 1 ? {
      url: `/pms/schemeToPerson/download/entry/excel/tpl?repairRound=${props.repairRound}`,
      method: 'GET',
    } : activeKey.value === 2 ? {
      url: '/pms/schemeToPerson/off/template/download',
      method: 'POST',
      params: {
        repairRound: props.repairRound,
      },
    } : {},
    requestBasicImport(files: any[]) {
      const formData = new FormData();
      formData.append('file', files[0]);
      let url = '';
      switch (activeKey.value) {
        case 0:
          url = `/pms/schemeToPerson/plan/import/excel/check/${props.repairRound}`;
          break;
        case 1:
          url = `/pms/schemeToPerson/import/entry/excel/check/${props.repairRound}`;
          break;
        case 2:
          url = `/pms/schemeToPerson/off/import/excel/check?repairRound=${props.repairRound}`;
          break;
      }
      return new Api(url).fetch(formData, '', 'POST');
    },
    requestSuccessImport(importId: string) {
      let url = '';
      switch (activeKey.value) {
        case 0:
          url = `/pms/schemeToPerson/plan/import/excel/${importId}`;
          break;
        case 1:
          url = `/pms/schemeToPerson/import/entry/excel/${importId}`;
          break;
        case 2:
          url = `/pms/schemeToPerson/off/import/excel/${importId}?repairRound=${props.repairRound}`;
          break;
      }
      return new Promise((resolve, reject) => {
        new Api(url).fetch('', '', activeKey.value === 2 ? 'GET' : 'POST').then(() => {
          props?.cb?.();
          resolve(true);
        }).catch((e) => {
          reject(e);
        });
      });
    },
  });

  function importApi({ key }) {
    activeKey.value = key;
    openImportModal();
  }

  // 导出
  function exportApi(params: {
        repairRound: string
    }) {
    Modal.confirm({
      title: '系统提示！',
      content: '确认导出全部数据？',
      onOk() {
        return new Promise((resolve) => {
          downloadByData('/pms/schemeToPerson/export/scheme/person/excel', params).then(() => {
            resolve('');
          }).catch((e) => {
            resolve(e);
          });
        });
      },
    });
  }

  return {
    OrderImportRender,
    importApi,
    exportApi,
    importTypes,
  };
}

// 大修第二层物资管理导出
export function useMaterialExcel() {
  // 导出
  function exportApi(params: {
        repairRound: string,
    }) {
    Modal.confirm({
      title: '系统提示！',
      content: '确认导出全部数据？',
      onOk() {
        return new Promise((resolve) => {
          downloadByData('/pms/schemeToMaterial/export/scheme/material/excel', params).then(() => {
            resolve('');
          }).catch((e) => {
            resolve(e);
          });
        });
      },
    });
  }

  return {
    exportApi,
  };
}

// 大修第二层安质环管理导出
export function useSQEExcel() {
  // 导出
  function exportApi(majorRepairTurn: string) {
    Modal.confirm({
      title: '系统提示！',
      content: '确认导出全部数据？',
      onOk() {
        return new Promise((resolve) => {
          downloadByData(`/pms/safety-quality-env/export/excel?majorRepairTurn=${majorRepairTurn}`, {}).then(() => {
            resolve('');
          }).catch((e) => {
            resolve(e);
          });
        });
      },
    });
  }

  return {
    exportApi,
  };
}

// 大修第二层关键路径节约导出
export function usePathExcel() {
  // 导出
  function exportApi(params: {
        repairRound: string
    }) {
    Modal.confirm({
      title: '系统提示！',
      content: '确认导出全部数据？',
      onOk() {
        return new Promise((resolve) => {
          downloadByData('/pms/major-repair-plan-economize/export', params).then(() => {
            resolve('');
          }).catch((e) => {
            resolve(e);
          });
        });
      },
    });
  }

  return {
    exportApi,
  };
}

// 大修第二层集体计量降低导出
export function useMeterExcel() {
  // 导出
  function exportApi(params: {
        repairRound: string
    }) {
    Modal.confirm({
      title: '系统提示！',
      content: '确认导出全部数据？',
      onOk() {
        return new Promise((resolve) => {
          downloadByData('/pms/major-repair-plan-meter-reduce/export', params).then(() => {
            resolve('');
          }).catch((e) => {
            resolve(e);
          });
        });
      },
    });
  }

  return {
    exportApi,
  };
}

// 大修第三层作业进展信息导出
export function useJobProgressExcel() {
  // 导出
  function exportApi(params: {
        jobId: string
        repairRound: string
    }) {
    Modal.confirm({
      title: '系统提示！',
      content: '确认导出全部数据？',
      onOk() {
        return new Promise((resolve) => {
          downloadByData('/pms/job-progress/export/excel', params).then(() => {
            resolve('');
          }).catch((e) => {
            resolve(e);
          });
        });
      },
    });
  }

  return {
    exportApi,
  };
}

// 大修第二层参修物资管理导出
export function useMaterialsExcel() {
  // 导出
  function exportApi(params: {
        repairRound: string
    }) {
    Modal.confirm({
      title: '系统提示！',
      content: '确认导出全部数据？',
      onOk() {
        return new Promise((resolve) => {
          downloadByData('/pms/major-repair-plan-economize/export', params).then(() => {
            resolve('');
          }).catch((e) => {
            resolve(e);
          });
        });
      },
    });
  }

  return {
    exportApi,
  };
}

// 大修第二层作业详情导入、导出
export function useWorkOrderImport(): {
    OrderImportRender: Function
    importApi: Function
    } {
  const [register, { openModal: importApi }] = useModal();
  const OrderImportRender = (props: {
        repairRound: string
        cb: Function
    }) => h(BasicImport, {
    onRegister: register,
    ...getImportProps({
      downloadUrl: '/pms/job-manage/download/excel/tpl',
      checkUrl: `/pms/job-manage/import/excel/checkNew/${props?.repairRound}`,
      importUrl: `/pms/job-manage/import/excelNew/${props?.repairRound}`,
    }, props.cb),
  });

  return {
    OrderImportRender,
    importApi,
  };
}
