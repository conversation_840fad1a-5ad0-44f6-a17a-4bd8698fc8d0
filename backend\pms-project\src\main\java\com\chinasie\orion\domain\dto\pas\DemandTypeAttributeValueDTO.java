package com.chinasie.orion.domain.dto.pas;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * QuestionTypeAttributeValueDTO 问题类型属性值DTO对象
 *
 * <AUTHOR> sie
 * @since 2022-10-13
 */
@Data
@ApiModel(value = "QuestionTypeAttributeValueDTO对象", description = "问题类型属性值")
public class DemandTypeAttributeValueDTO implements Serializable {
    /**
     * 分类主键
     */
    @ApiModelProperty(value = "分类主键")
    private String typeId;

    /**
     * 需求主键
     */
    @ApiModelProperty(value = "需求主键")
    private String demandId;

    /**
     * 属性主键
     */
    @ApiModelProperty(value = "属性主键")
    private String attributeId;

    /**
     * 属性值
     */
    @ApiModelProperty(value = "属性值")
    private String value;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 属性名称
     */
    @ApiModelProperty(value = "属性名称")
    private String name;
}
