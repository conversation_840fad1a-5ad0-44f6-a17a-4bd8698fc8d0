package com.chinasie.orion.constant;

/**
 * @author: yk
 * @date: 2023/10/25 20:40
 * @description:
 */
public enum WorkHourFillStatusEnum {
    CREATED(101, "已创建"),
    FLOWING(110, "流程中"),
    AUDITED(130, "已生效"),
    ;


    private Integer status;

    private String desc;


    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    WorkHourFillStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
