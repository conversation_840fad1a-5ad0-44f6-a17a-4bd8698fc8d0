package com.chinasie.orion.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.api.code.SysCodeApi;
import com.chinasie.orion.bo.DataStatusNBO;
import com.chinasie.orion.bo.OrionRoleConfig;
import com.chinasie.orion.bo.StatusConvertBO;
import com.chinasie.orion.bo.UserConvertBO;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.DictConts;
import com.chinasie.orion.domain.dto.TrainManageDTO;
import com.chinasie.orion.domain.dto.train.SimpleDTO;
import com.chinasie.orion.domain.entity.TrainCenter;
import com.chinasie.orion.domain.entity.TrainContact;
import com.chinasie.orion.domain.entity.TrainManage;
import com.chinasie.orion.domain.vo.BasePlaceVO;
import com.chinasie.orion.domain.vo.TrainManageVO;
import com.chinasie.orion.domain.vo.detailvo.TrainManageDetailVO;
import com.chinasie.orion.domain.vo.train.SimpleTrainVO;
import com.chinasie.orion.enums.StatusEnum;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.TrainManageMapper;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.org.DeptBaseInfoVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.BasePlaceService;
import com.chinasie.orion.service.TrainCenterService;
import com.chinasie.orion.service.TrainContactService;
import com.chinasie.orion.service.TrainManageService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <p>
 * TrainManage 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 17:09:01
 */
@Service
@Slf4j
public class TrainManageServiceImpl extends OrionBaseServiceImpl<TrainManageMapper, TrainManage> implements TrainManageService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private TrainCenterService trainCenterService;

    @Autowired
    private SysCodeApi sysCodeApi;

    @Autowired
    private DictRedisHelper dictRedisHelper;

    @Autowired
    private DeptRedisHelper deptRedisHelper;


    private TrainContactService trainContactService;

    @Autowired
    private OrionRoleConfig orionRoleConfig;

    @Autowired
    private BasePlaceService basePlaceService;

    @Autowired
    private DataStatusNBO dataStatusBO;

//    private static final String TRAIN_ENGINEER_CODE = "train_pms_engineer";


    @Autowired
    public void setTrainContactService(TrainContactService trainContactService) {
        this.trainContactService = trainContactService;
    }

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public TrainManageVO detail(String id, String pageCode) throws Exception {
        TrainManage trainManage = this.getById(id);
        TrainManageVO result = BeanCopyUtils.convertTo(trainManage, TrainManageVO::new);
        setEveryName(Collections.singletonList(result));

        List<TrainCenter> trainCenters = trainCenterService.listByTrainId(id);
        List<SimpleDTO> simpleDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(trainCenters)) {
            Boolean isCheck = trainManage.getIsCheck();
            if (isCheck) {
                TrainCenter trainCenter = trainCenters.get(0);
                result.setTrainLecturer(trainCenter.getTrainLecturer());
                result.setTrainAddress(trainCenter.getTrainAddress());
                result.setTrainCenterId(trainCenter.getId());
            } else {
                for (TrainCenter trainCenter : trainCenters) {
                    SimpleDTO simpleDTO = new SimpleDTO();
                    simpleDTO.setCode(trainCenter.getAttendCenter());
                    simpleDTO.setName(trainCenter.getAttendCenterName());
                    DeptBaseInfoVO deptBaseInfoVO = deptRedisHelper.getDeptBaseInfoByDeptCode(trainCenter.getOrgId(), trainCenter.getAttendCenter());
                    simpleDTO.setId(null == deptBaseInfoVO ? "" : deptBaseInfoVO.getId());
                    simpleDTOList.add(simpleDTO);
                }
            }
            result.setAttendCenterCodeList(simpleDTOList);
        }


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param trainManageDTO
     */
    @Override
    public String create(TrainManageDTO trainManageDTO) throws Exception {
        TrainManage trainManage = BeanCopyUtils.convertTo(trainManageDTO, TrainManage::new);

        Boolean isCheck = trainManageDTO.getIsCheck();
        List<SimpleDTO> attendCenterCodeList = trainManageDTO.getAttendCenterCodeList();
        List<TrainCenter> trainCenters = new ArrayList<>();

        //
        this.packageTrainCenter(isCheck, attendCenterCodeList, trainCenters, trainManageDTO);
        // todo 自动生成编码
        String trainNumber = this.getTrainNumber();
        trainManage.setTrainNumber(trainNumber);
        trainManage.setStatus(StatusEnum.DISABLE.getIndex());
        this.save(trainManage);
        String dataId = trainManage.getId();
        trainCenters.forEach(i -> {
            i.setTrainNumber(trainNumber);
            i.setTrainId(dataId);
        });

        //  插入中心数据
        trainCenterService.saveOrUpdateList(trainCenters, dataId, trainNumber, isCheck);
        return dataId;
    }

    public String getTrainNumber() throws Exception {
//            ResponseDTO<String> responseDTO = sysCodeApi.rulesAndSegmentCreate("TrainManage", "number", Boolean.FALSE, "");
//            if(responseDTO.getCode() == HttpServletResponse.SC_OK){
//                if(StringUtils.hasText(responseDTO.getResult())){
//                    return  responseDTO.getResult();
//                }
        return "PX" + RandomUtil.randomNumbers(6);
//            }else{
//                return "PX"+ RandomUtil.randomNumbers(6);
//            }
    }

    public void packageTrainCenter(boolean isCheck, List<SimpleDTO> attendCenterCodeList, List<TrainCenter> trainCenters, TrainManageDTO trainManageDTO) {
        if (!isCheck) {
            String baseCode = trainManageDTO.getBaseCode();
            if (CollectionUtils.isEmpty(attendCenterCodeList)) {
                throw new BaseException(HttpStatus.BAD_REQUEST.value(), "参培中心不能为空");
            }
            if (StringUtils.isEmpty(baseCode)){
                throw new BaseException(HttpStatus.BAD_REQUEST.value(), "培训基地不能为空");
            }
            List<String> deptIds = new ArrayList<>();
            attendCenterCodeList.forEach(item->{
                deptIds.add(item.getId());
            });
            LambdaQueryWrapperX<TrainContact> wrapperX = new LambdaQueryWrapperX<>(TrainContact.class);
            wrapperX.in(TrainContact::getDeptCode,deptIds);
            wrapperX.eq(TrainContact::getBaseCode,baseCode);
            wrapperX.eq(TrainContact::getContactType,"contact_person");
            List<TrainContact> list = trainContactService.list(wrapperX);
            Map<String,TrainContact> deptIdToTrainContact = new HashMap<>();
            list.forEach(item->{
                deptIdToTrainContact.put(item.getDeptId(),item);
            });

            for (SimpleDTO simpleDTO : attendCenterCodeList) {
                TrainCenter trainCenter = new TrainCenter();
                trainCenter.setAttendCenter(simpleDTO.getCode());
                trainCenter.setAttendCenterName(simpleDTO.getName());
                trainCenter.setTrainAddress(trainManageDTO.getTrainAddress());
                trainCenter.setTrainLecturer(trainManageDTO.getTrainLecturer());
                trainCenter.setExpirationMonth(trainManageDTO.getExpirationMonth());
                TrainContact orDefault = deptIdToTrainContact.getOrDefault(simpleDTO.getId(), new TrainContact());
                if(Objects.nonNull(orDefault)){
                    trainCenter.setContactPersonIds(orDefault.getContactPersonIds());
                    trainCenter.setContactPersonNames(orDefault.getContactPersonNames());
                }
                trainCenters.add(trainCenter);
            }
        } else {
            TrainCenter trainCenter = new TrainCenter();
            trainCenter.setTrainAddress(trainManageDTO.getTrainAddress());
            trainCenter.setTrainLecturer(trainManageDTO.getTrainLecturer());
            trainCenter.setExpirationMonth(trainManageDTO.getExpirationMonth());
            trainCenters.add(trainCenter);
        }
    }

    /**
     * 编辑
     * <p>
     * * @param trainManageDTO
     */
    @Override
    public Boolean edit(TrainManageDTO trainManageDTO) throws Exception {

        String userId = CurrentUserHelper.getCurrentUserId();
        List<String> baseCodeList = trainContactService.getCodeMap(userId, orionRoleConfig.getTrainEngineerCode());
        if(!baseCodeList.contains(trainManageDTO.getBaseCode())){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "您没有权限编辑此培训");
        }

        TrainManage trainManage = BeanCopyUtils.convertTo(trainManageDTO, TrainManage::new);
        String rsp = trainManage.getId();

        Boolean isCheck = trainManageDTO.getIsCheck();
        List<SimpleDTO> attendCenterCodeList = trainManageDTO.getAttendCenterCodeList();
        List<TrainCenter> trainCenters = new ArrayList<>();

        // 组装数据
        this.packageTrainCenter(isCheck, attendCenterCodeList, trainCenters, trainManageDTO);
        this.updateById(trainManage);
        String dataId = trainManage.getId();
        trainCenters.forEach(i -> {
            i.setTrainNumber(trainManage.getTrainNumber());
            i.setTrainId(dataId);
        });

        //  插入中心数据
        trainCenterService.saveOrUpdateList(trainCenters, dataId, trainManage.getTrainNumber(), isCheck);

        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        String userId = CurrentUserHelper.getCurrentUserId();
        List<String> baseCodeList = trainContactService.getCodeMap(userId, orionRoleConfig.getTrainEngineerCode());

        LambdaQueryWrapperX<TrainManage> wrapperX = new LambdaQueryWrapperX<>(TrainManage.class);
        wrapperX.in(TrainManage::getId,ids);
        wrapperX.select(TrainManage::getBaseCode,TrainManage::getId);
        List<TrainManage> trainManageList= this.list(wrapperX);
        if(CollectionUtils.isEmpty(trainManageList)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "请刷新数据后尝试");
        }
        trainManageList.forEach(i->{
            if(!baseCodeList.contains(i.getBaseCode())){
                throw new BaseException(HttpStatus.BAD_REQUEST.value(), "您没有权限删除此培训");
            }
        });
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<TrainManageVO> pages(Page<TrainManageDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<TrainManage> condition = new LambdaQueryWrapperX<>(TrainManage.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(TrainManage::getCreateTime);

        TrainManageDTO trainManageDTO = pageRequest.getQuery();
        if (Objects.nonNull(trainManageDTO)) {
            if (StringUtils.hasText(trainManageDTO.getTrainType())) {
                //1 业务执行培训  2 大修日常培训
                if (Objects.equals(trainManageDTO.getTrainType(), "1")) {
                    condition.isNull(TrainManage::getType);
                } else {
                    condition.isNotNull(TrainManage::getType);
                }
            }
        }
        Page<TrainManage> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), TrainManage::new));

        PageResult<TrainManage> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<TrainManageVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<TrainManageVO> vos = BeanCopyUtils.convertListTo(page.getContent(), TrainManageVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "培训管理导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", TrainManageDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        TrainManageExcelListener excelReadListener = new TrainManageExcelListener();
        EasyExcel.read(inputStream, TrainManageDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<TrainManageDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("培训管理导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<TrainManage> trainManagees = BeanCopyUtils.convertListTo(dtoS, TrainManage::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::TrainManage-import::id", importId, trainManagees, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<TrainManage> trainManagees = (List<TrainManage>) orionJ2CacheService.get("pmsx::TrainManage-import::id", importId);
        log.info("培训管理导入的入库数据={}", JSONUtil.toJsonStr(trainManagees));

        this.saveBatch(trainManagees);
        orionJ2CacheService.delete("pmsx::TrainManage-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::TrainManage-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<TrainManage> condition = new LambdaQueryWrapperX<>(TrainManage.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(TrainManage::getCreateTime);
        List<TrainManage> trainManagees = this.list(condition);

        List<TrainManageDTO> dtos = BeanCopyUtils.convertListTo(trainManagees, TrainManageDTO::new);

        String fileName = "培训管理数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", TrainManageDTO.class, dtos);

    }

    @Override
    public void  setEveryName(List<TrainManageVO> vos)throws Exception {
//        Map<String,String> baseToOpMap = trainContactService.listByCurrentUser(CurrentUserHelper.getCurrentUserId());
        List<DataStatusVO> dataStatusVOList = dataStatusBO.getDataStatusListByClassName(TrainManage.class.getSimpleName());
        if (CollectionUtils.isEmpty(dataStatusVOList)){
            dataStatusVOList = new ArrayList<>();
        }
        final Map<Integer, DataStatusVO> statusToVo = dataStatusVOList.stream().collect(Collectors.toMap(DataStatusVO::getStatusValue, x -> x));
        List<DictValueVO> dictListByCode = dictRedisHelper.getDictListByCode(DictConts.TRAIN_TYPE);
        Map<String, String> numberToDescMap = dictListByCode.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription));

        String userId = CurrentUserHelper.getCurrentUserId();
        List<String> baseCodeList = trainContactService.getCodeMap(userId, orionRoleConfig.getTrainEngineerCode());

        vos.forEach(vo -> {
            vo.setDataStatus(statusToVo.get(vo.getStatus()));
            vo.setTypeName(numberToDescMap.getOrDefault(vo.getType(), ""));
            if(baseCodeList.contains(vo.getBaseCode()) && vo.getStatus() == 0){
                vo.setEdit(true);
            }
        });


    }

    @Override
    public List<SimpleTrainVO> simpleList() {
        LambdaQueryWrapperX<TrainManage> condition = new LambdaQueryWrapperX<>(TrainManage.class);
        condition.select(TrainManage::getTrainNumber, TrainManage::getName, TrainManage::getId);
        List<TrainManage> list = this.list(condition);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<SimpleTrainVO> simpleTrainVOList = new ArrayList<>();
        for (TrainManage trainManage : list) {
            SimpleTrainVO simpleTrainVO = new SimpleTrainVO();
            simpleTrainVO.setId(trainManage.getId());
            simpleTrainVO.setName(trainManage.getName());
            simpleTrainVO.setNumber(trainManage.getTrainNumber());
            simpleTrainVOList.add(simpleTrainVO);
        }
        return simpleTrainVOList;
    }

    @Override
    public SimpleTrainVO getSingleByTrainNumber(String trainNumber) {
        LambdaQueryWrapperX<TrainManage> condition = new LambdaQueryWrapperX<>(TrainManage.class);
        condition.select(TrainManage::getTrainNumber, TrainManage::getName, TrainManage::getId, TrainManage::getTrainKey
                , TrainManage::getExpireTime);
        condition.eq(TrainManage::getTrainNumber, trainNumber);
        List<TrainManage> trainManageList = this.list(condition);
        if (CollectionUtils.isEmpty(trainManageList)) {
            return null;
        }
        TrainManage trainManage = trainManageList.get(0);
        SimpleTrainVO simpleTrainVO = new SimpleTrainVO();
        simpleTrainVO.setId(trainManage.getId());
        simpleTrainVO.setName(trainManage.getName());
        simpleTrainVO.setNumber(trainManage.getTrainNumber());
        simpleTrainVO.setTrainKey(trainManage.getTrainKey());
        simpleTrainVO.setExpireTime(trainManage.getExpireTime());
        return simpleTrainVO;
    }

    @Override
    public TrainManageVO getDetailById(String trainId) throws Exception {
        TrainManage trainManage = this.getById(trainId);
        TrainManageVO trainManageVO = BeanCopyUtils.convertTo(trainManage, TrainManageVO::new);
        this.setEveryName(Collections.singletonList(trainManageVO));
        return trainManageVO;
    }

    @Override
    public List<BasePlaceVO> allBasePlaceList() {
        return basePlaceService.allList();

    }

    @Override
    public TrainManageVO detailV2(String id, String pageCode) throws Exception {
        TrainManage trainManage = this.getById(id);
        TrainManageVO result = BeanCopyUtils.convertTo(trainManage, TrainManageVO::new);
        setEveryName(Collections.singletonList(result));

        List<TrainCenter> trainCenters = trainCenterService.listByTrainId(id);
        List<SimpleDTO> simpleDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(trainCenters)) {
            Boolean isCheck = trainManage.getIsCheck();
            if (isCheck) {
                TrainCenter trainCenter = trainCenters.get(0);
                result.setTrainLecturer(trainCenter.getTrainLecturer());
                result.setTrainAddress(trainCenter.getTrainAddress());
                result.setTrainCenterId(trainCenter.getId());
            } else {
                for (TrainCenter trainCenter : trainCenters) {
                    SimpleDTO simpleDTO = new SimpleDTO();
                    simpleDTO.setCode(trainCenter.getAttendCenter());
                    simpleDTO.setName(trainCenter.getAttendCenterName());
                    DeptBaseInfoVO deptBaseInfoVO = deptRedisHelper.getDeptBaseInfoByDeptCode(trainCenter.getOrgId(), trainCenter.getAttendCenter());
                    simpleDTO.setId(null == deptBaseInfoVO ? "" : deptBaseInfoVO.getId());
                    simpleDTOList.add(simpleDTO);
                }
            }
            result.setAttendCenterCodeList(simpleDTOList);
        }


        return result;
    }

    public static class TrainManageExcelListener extends AnalysisEventListener<TrainManageDTO> {

        private final List<TrainManageDTO> data = new ArrayList<>();

        @Override
        public void invoke(TrainManageDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<TrainManageDTO> getData() {
            return data;
        }
    }


}
