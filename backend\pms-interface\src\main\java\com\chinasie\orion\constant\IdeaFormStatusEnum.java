package com.chinasie.orion.constant;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsr
 * @date: 2024/01/30/10:10
 * @description:
 */
public enum IdeaFormStatusEnum {

    IF_STATUS_CREAT(101, "已创建"),
    IF_STATUS_BEING(110, "流程中"),
    IF_STATUS_COMPLETE(130, "已审批"),
    IF_STATUS_CLOSE(111, "已关闭");


    private Integer status;

    private String desc;



    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    IdeaFormStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
