<template>
  <OrionTable
    ref="tableRef"
    :options="options"
    :filter-schemas="filterSchemas"
    :schemas-label-width="50"
    @selectionChange="selectionChange"
    @initData="initData"
  >
    <template #toolbarLeft>
      <div class="mr10">
        <BasicButton
          icon="add"
          type="primary"
          @click="openSelectUser"
        >
          添加用户
        </BasicButton>
        <DeleteButton
          class="ml10"
          type="default"
          :disabled="!selectionRowKeys.length"
          @click="removeUser()"
        >
          移除
        </DeleteButton>
        <AButton
          :disabled="!selectionRowKeys.length"
          class="ml10"
          @click="openAdjustmentUser"
        >
          <template #icon>
            <Icon icon="fa-arrows" />
          </template>
          调整角色
        </AButton>
        <AButton
          class="ml10"
          @click="openViewRoleAll"
        >
          <template #icon>
            <Icon icon="fa-eye" />
          </template>
          查看用户权限
        </AButton>
      </div>
    </template>
    <template #status="{ record }">
      <UserStatus :status="record.status" />
    </template>

    <template #name="{ record }">
      <a
        href="javascript:void(0)"
        class="action-btn"
        @click="openDetails(record)"
      >
        {{ record.name }}
      </a>
    </template>
  </OrionTable>
  <!--选择用户-->
  <SelectUserModal
    :on-ok="selectUserOk"
    @register="selectUserModalRegister"
  />
  <!--调整角色-->
  <AdjustmentUserModal
    :role-list="roleList"
    :on-ok="adjustmentUserApi"
    @register="adjustmentUserModalRegister"
  />
  <!-- 查看用户详情 -->
  <!--  <Details-->
  <!--    :width="700"-->
  <!--    :user="userInfo"-->
  <!--    @register="userDetailRegister"-->
  <!--  />-->

  <!-- 查看权限 -->
  <ViewRoleModal @register="viewRoleRegister" />
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
import {
  OrionTable, BasicButton, DeleteButton, Icon, useModal, UserStatus, SelectUserModal,
} from 'lyra-component-vue3';
import { Button, message, Modal } from 'ant-design-vue';
import AdjustmentUserModal from '../modal/AdjustmentUserModal.vue';
import ViewRoleModal from '../modal/ViewRoleModal.vue';
// import Details from '/@/views/pmi/organization/modal/UserDetail.vue';
import Api from '/@/api';

export default defineComponent({
  name: 'UserList',
  components: {
    OrionTable,
    BasicButton,
    DeleteButton,
    AButton: Button,
    Icon,
    SelectUserModal,
    AdjustmentUserModal,
    ViewRoleModal,
    UserStatus,
    // Details,
  },
  props: {
    roleId: {
      type: String,
      default: '',
    },
    roleList: {
      type: Array,
      default: () => [],
    },
  },
  setup(props) {
    const state = reactive({
      tableRef: null,
      selectionRowKeys: [],
      userInfo: {},
    });

    // 添加用户
    const [selectUserModalRegister, { openModal: openSelectUser }] = useModal();
    // 调整角色
    const [adjustmentUserModalRegister, { openModal: openAdjustmentUser }] = useModal();
    // 查看用户详情
    const [userDetailRegister, { openModal: openUserDetail }] = useModal();
    // 查看用户权限
    const [viewRoleRegister, { openModal: openViewRole }] = useModal();

    // 移除用户
    function removeUser(id, name) {
      Modal.confirm({
        title: '移除确认',
        content: '您确认移除选中用户吗？',
        onOk() {
          const { selectedRowKeys } = state.tableRef.getRowSelection();
          let params;
          if (id) {
            params = [id];
          } else {
            params = selectedRowKeys;
          }
          return new Api('/pmi/role/role-remove-user')
            .fetch(params, `?roleId=${props.roleId}`, 'POST')
            .then(() => {
              message.success('移除成功');
              state.tableRef.reload();
              state.selectionRowKeys = [];
            });
        },
      });
    }
    function initData() {
    }

    return {
      ...toRefs(state),
      selectUserModalRegister,
      adjustmentUserModalRegister,
      userDetailRegister,
      viewRoleRegister,
      openSelectUser,
      openAdjustmentUser,
      initData,
      adjustmentUserApi(roleId) {
        const { selectedRowKeys } = state.tableRef.getRowSelection();
        return new Api('/pmi/role/role-adjustment-user')
          .fetch(selectedRowKeys, `?newRoleId=${roleId}&oldRoleId=${props.roleId}`, 'POST')
          .then(() => {
            state.tableRef.reload();
          });
      },
      // 勾选
      selectionChange({ keys }) {
        state.selectionRowKeys = keys;
      },
      // 添加用户
      selectUserOk(selectUserData) {
        return new Api('/pmi/role/role-add-user')
          .fetch(
            selectUserData.map((item) => item.id),
            `?roleId=${props.roleId}`,
            'POST',
          )
          .then((data) => {
            message.success('添加成功');
            state.tableRef.reload();
          });
      },
      // 移除用户
      removeUser,
      openDetails(item: any) {
        state.userInfo = item;
        openUserDetail(true, item);
      },
      openViewRoleAll() {
        openViewRole(true, {
          type: 'search',
          userInfo: null,
        });
      },
      // 筛选
      filterSchemas: [
        {
          field: 'secretlevel',
          component: 'ApiSelect',
          label: '密级',
          colProps: {
            span: 6,
          },
          componentProps: {
            api: () => new Api('/pmi/classification')
              .getList({
                status: 1,
              })
              .then((data) => {
                if (data) {
                  return data
                    .map((item) => ({
                      label: item.name,
                      value: item.id,
                      key: item.id,
                      id: item.id,
                      ...item,
                    }))
                    .sort((a, b) => a.sort - b.sort);
                }
              }),
          },
        },
        {
          field: 'orgId',
          component: 'ApiSelect',
          label: '部门',
          colProps: {
            span: 6,
          },
          componentProps: {
            api: () => new Api('/pmi/role/role-user-org')
              .fetch(
                {
                  id: props.roleId,
                },
                '',
                'POST',
              )
              .then((data) => data.map((item) => ({
                label: item.name,
                id: item.id,
                key: item.id,
                value: item.id,
              }))),
          },
        },
        {
          field: 'status',
          component: 'Select',
          label: '状态',
          colProps: {
            span: 6,
          },
          componentProps: {
            // mode: 'multiple',
            options: [
              {
                label: '锁定',
                value: '0',
                key: 0,
              },
              {
                label: '正常',
                value: '1',
                key: 1,
              },
              {
                label: '已离职',
                value: '2',
                key: 2,
              },
            ],
          },
        },
      ],
      options: {
        deleteToolButton: 'add|delete|enable|disable',
        rowSelection: {},
        auto: {
          url: '/pmi/organization/userAllList',
          params: {
            queryCondition: [
              {
                column: 'roleId',
                link: 'AND',
                type: 'eq',
                value: props.roleId,
              },
            ],
          },
        },
        // 操作区配置
        actionColumn: {
          width: 120,
          item: [
            {
              name: '查看权限',
              cb: (e: any) => {
                openViewRole(true, {
                  type: 'user',
                  userInfo: e,
                });
              },
            },
            {
              name: '移除',
              cb: (e: any) => {
                removeUser(e.id, e.name);
              },
            },
          ],
        },
        columns: [
          {
            title: '姓名',
            dataIndex: 'name',
            slots: { customRender: 'name' },
            width: 100,
          },
          {
            title: '所属部门',
            dataIndex: 'orgName',
          },
          {
            title: '涉密邮箱',
            dataIndex: 'email',
          },
          {
            title: '状态',
            dataIndex: 'status',
            slots: { customRender: 'status' },
            width: 80,
          },
          {
            title: '密级等级',
            dataIndex: 'secretlevel',
            width: 120,
          },
          {
            title: '修改人',
            dataIndex: 'modifyName',
            width: 120,
          },
          {
            title: '修改时间',
            dataIndex: 'modifyTime',
            type: 'dateTime',
          },
        ],
      },
    };
  },
});
</script>

<style scoped></style>
