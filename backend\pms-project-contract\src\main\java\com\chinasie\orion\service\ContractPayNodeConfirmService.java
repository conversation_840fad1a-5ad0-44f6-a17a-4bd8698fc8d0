package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.ContractPayNodeConfirmAuditDTO;
import com.chinasie.orion.domain.dto.ContractPayNodeConfirmDTO;
import com.chinasie.orion.domain.entity.ContractPayNodeConfirm;
import com.chinasie.orion.domain.vo.ContractPayNodeConfirmListVO;
import com.chinasie.orion.domain.vo.ContractPayNodeConfirmSelectVO;
import com.chinasie.orion.domain.vo.ContractPayNodeConfirmVO;
import com.chinasie.orion.domain.vo.ContractPayNodeDetailVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

/**
 * <p>
 * ContractPayNodeConfirm 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26 21:44:48
 */
public interface ContractPayNodeConfirmService extends OrionBaseService<ContractPayNodeConfirm> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    ContractPayNodeDetailVO detail(String id) throws Exception;

    /**
     * 新增
     * <p>
     * * @param contractPayNodeConfirmDTO
     */
    ContractPayNodeConfirmVO create(ContractPayNodeConfirmDTO contractPayNodeConfirmDTO) throws Exception;

    /**
     * 提交
     * <p>
     * * @param contractPayNodeConfirmDTO
     */
    Boolean submit(ContractPayNodeConfirmDTO contractPayNodeConfirmDTO) throws Exception;


    /**
     * 编辑
     * <p>
     * * @param contractPayNodeConfirmDTO
     */
    Boolean edit(ContractPayNodeConfirmDTO contractPayNodeConfirmDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ContractPayNodeConfirmListVO> pages(Page<ContractPayNodeConfirmDTO> pageRequest) throws Exception;

    /**
     * 选择支付节点列表
     *
     * @param contractId
     * @return
     * @throws Exception
     */
    List<ContractPayNodeConfirmSelectVO> payNodeListByContractId(String contractId) throws Exception;

    /**
     *
     * @param contractId
     * @return
     * @throws Exception
     */
    List<ContractPayNodeConfirmSelectVO> payNodeStatusConfirmListByContractId(String contractId) throws Exception;


    /**
     * 选择支付节点列表
     *
     * @param id
     * @return
     * @throws Exception
     */
    List<ContractPayNodeConfirmSelectVO> payNodeListById(String id) throws Exception;

    /**
     * 同意
     *
     * @param confirmAuditDTO
     * @return
     * @throws Exception
     */
    Boolean agree(ContractPayNodeConfirmAuditDTO confirmAuditDTO) throws Exception;

    /**
     * 驳回
     *
     * @param confirmAuditDTO
     * @return
     * @throws Exception
     */
    Boolean reject(ContractPayNodeConfirmAuditDTO confirmAuditDTO) throws Exception;
}
