<script setup lang="ts">
import { OrionTable, useModal } from 'lyra-component-vue3';
import { onMounted, ref, Ref } from 'vue';
import Api from '/@/api';
import { formatMoney } from '/@/views/pms/utils/utils';
import dayjs from 'dayjs';
import FilesModal from '../filesModal/FilesModal.vue';

const props = defineProps<{
  receivableId:string
}>();

const [registerFilesModal, { openModal: openFilesModal }] = useModal();

const tableRef: Ref = ref();
const tableOptions = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  api: (params) => new Api('/pms/projectFundsReceived/pages').fetch({
    ...params,
    query: {
      receivableId: props.receivableId,
    },
  }, '', 'POST'),
  columns: [
    {
      title: '客户名称',
      dataIndex: 'stakeholderName',
    },
    {
      title: '合同收款节点',
      dataIndex: 'collectionPoint',
      width: 120,
    },
    {
      title: '实收日期',
      dataIndex: 'fundsReceivedDate',
      width: 120,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '实收金额（元）',
      dataIndex: 'fundsReceived',
      width: 120,
      customRender({ text }) {
        return text ? formatMoney(text) : '0';
      },
    },
    {
      title: '收款方式',
      dataIndex: 'paymentTerm',
      width: 120,
    },
    {
      title: '发票号码',
      dataIndex: 'invoiceNumber',
      width: 120,
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 80,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '下载附件',
      onClick(record) {
        openFilesModal(true, { id: record.id });
      },
    },
  ],
};
</script>

<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
  />
  <FilesModal @register="registerFilesModal" />
</template>

<style scoped lang="less">

</style>
