<template>
  <div class="information">
    <a-row
      :gutter="[20, 20]"
      class="information-row"
    >
      <template
        v-for="item in formFieldList"
        :key="item.field"
      >
        <ACol
          :span="item.span"
          class="task-item"
        >
          <div class="item-title">
            {{ item.label }}:
          </div>
          <div
            class="item-value flex-te"
          >
            {{
              formData[item.field]?item.formatter
                ? item.formatter(formData[item.field])
                : formData[item.field]:''
            }}
          </div>
        </ACol>
      </template>
    </a-row>
  </div>
</template>
<script lang="ts">
import {
  defineComponent, inject, reactive, toRefs, watch, ref,
} from 'vue';
import {
  Row, Col, Input, Modal,
} from 'ant-design-vue';
import dayjs from 'dayjs';
import { formatMoney1, formatMoney } from '../index';

export default defineComponent({
  name: 'Information',
  components: {
    ARow: Row,
    ACol: Col,
  },
  setup() {
    const formData = inject('formData', {});
    const state = reactive({
      formData: formData?.value,
      formFieldList: [
        {
          label: '项目编号',
          span: 6,
          field: 'projectNumber',
        },
        {
          label: '项目名称',
          span: 6,
          field: 'projectName',
        },
        {
          label: '项目部门',
          span: 6,
          field: 'rspDeptName',
        },
        {
          label: '项目负责人',
          span: 6,
          field: 'rspUserName',
        },
        {
          label: '项目概算',
          span: 6,
          field: 'estimate',
          formatter: formatMoney1,
        },
        {
          label: '累计投资计划',
          span: 6,
          field: 'totalInvestmentPlan',
          formatter: formatMoney1,
        },
        {
          label: '累计完成投资计划',
          span: 6,
          field: 'totalInvestmentCompletePlan',
          formatter: formatMoney1,
        },
        {
          label: '概算执行率',
          span: 6,
          field: 'estimatePercent',
        },
        {
          label: '总体预算',
          span: 6,
          field: 'overallBudget',
          formatter: formatMoney1,
        },
        {
          label: '总体实际',
          span: 6,
          field: 'overallReality',
          formatter: formatMoney1,
        },
        {
          label: '立项金额',
          span: 6,
          field: 'projectAmount',
          formatter: formatMoney1,
        },
        {
          label: '合同金额',
          span: 6,
          field: 'contractAmount',
          formatter: formatMoney1,
        },
        {
          label: '本年投资计划',
          span: 6,
          field: 'currentYearInvest',
          formatter: formatMoney1,
        },
        {
          label: '本年完成投资计划',
          span: 6,
          field: 'currentYearCompleteInvest',
          formatter: formatMoney1,
        },
        {
          label: '本年投资计划执行率',
          span: 12,
          field: 'currentYearCompleteInvestPercent',
        },
      ],
    });
    function formatterTime(val) {
      return val ? dayjs(val).format('YYYY-MM-DD') : '';
    }

    watch(
      () => formData?.value,
      (val) => {
        state.formData = val;
      },
    );
    return {
      ...toRefs(state),
    };
  },
});
</script>
<style lang="less" scoped>
.information {
  height: 100%;
  width: 100%;

  .information-row {
    padding: 20px;
    width: 100%;
  }
.task-item {
    display: flex;
    line-height: 20px;

    .item-title {
      padding-right: 20px;
      color: #000000a5;
      width: 200px;
      text-align: right;
    }
    .item-value {
      flex: 1;
    }
  }
}

</style>
