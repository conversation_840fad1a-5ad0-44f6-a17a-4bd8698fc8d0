package com.chinasie.orion.domain.dto.reporting;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.Boolean;
import java.lang.String;

/**
 * ProjectWeeklyContent Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-14 08:50:29
 */
@ApiModel(value = "ProjectWeeklyContentDTO对象", description = "项目周报内容表")
@Data
public class ProjectWeeklyContentDTO extends ObjectDTO implements Serializable{

    /**
     * 工作内容
     */
    @ApiModelProperty(value = "工作内容")
    private String content;

    /**
     * 工时
     */
    @ApiModelProperty(value = "工时")
    private BigDecimal taskTime;

    /**
     * 是否为计划内
     */
    @ApiModelProperty(value = "是否为计划内")
    private Integer thePlan;

    /**
     * 关联关系
     */
    @ApiModelProperty(value = "关联关系")
    private String relationship;

    /**
     * 周报ID
     */
    @ApiModelProperty(value = "周报ID")
    private String weeklyId;

    /**
     * 关联类型
     */
    @ApiModelProperty(value = "关联类型")
    private String relationType;

    /**
     * 是否为下周计划
     */
    @ApiModelProperty(value = "是否为下周计划")
    private String isNext;

    /**
     * 计划是否已完成
     */
    @ApiModelProperty(value = "计划是否已完成")
    private String completeStatus;

}
