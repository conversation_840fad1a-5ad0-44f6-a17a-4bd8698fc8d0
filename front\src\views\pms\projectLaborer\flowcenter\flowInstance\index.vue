<template>
  <Layout
    :options="{ body: { scroll: true } }"
    content-title="流程实例"
  >
    <orion-table
      ref="instanceTableRef"
      :options="options"
      :filter-schemas="filterSchemas"
      @initData="initData"
    >
      <template #name="{ record }">
        <a @click="goDetail(record)">{{ record.procInstName }}</a>
      </template>
      <template #action="{ record }">
        <a
          v-if="record.statusCode === 'RUNNING'"
          @click="abort(record)"
        >终止流程</a>
      </template>
    </orion-table>
    <form-modal
      :width="600"
      @register="registerForm"
      @confirm="onConfirm"
    />
  </Layout>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, ref, inject,
} from 'vue';
import {
  BasicTable, Layout, useActionsRecord, OrionTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer, isPower,
} from 'lyra-component-vue3';
// import { isPower } from '/@/hooks/power/useBusinessPrivilege';
import { Input, Button, message } from 'ant-design-vue';
import { useRouter } from 'vue-router';
// import Layout from '/@/components/Layout';
// import { useModal } from '/@/components/Modal';
import { useUserStore } from '/@/store/modules/user';
// import OrionTable from '/@/components/OrionTable';
import FormModal from './modal/FormModal.vue';
import Api from '/@/api/index';
import { workflowApi } from '../util/apiConfig';
// import { useActionsRecord } from '/@/hooks/actionsRecord/useActionsRecord';

  const enum ControlType {
    'URGENT' = '催办',
    'SUSPEND' = '挂起',
    'END' = '终止'
  }
export default defineComponent({
  name: 'FlowInstance',
  components: {
    Layout,
    OrionTable,
    Input,
    AButton: Button,
    FormModal,
  },
  setup() {
    const powerData = inject('powerData', {});
    const userStore: any = useUserStore();
    const instanceTableRef = ref<Nullable<any>>(null); // table的ref
    const router = useRouter();
    // 注册一个表单弹窗,用于增加和更新部门
    const [registerForm, { openModal: openModalForm, setModalProps: setModalFormProps }] = useModal();

    const state = reactive({
      procInstId: '',
      actionItem: null,
      options: {
        rowSelection: {},
        deleteToolButton: 'add|delete|enable|disable',
        smallSearchField: ['proc_inst_name'],
        tool: [
          {
            type: 'button',
            buttonGroup: [
              [
                {
                  icon: 'fa fa-lock',
                  name: '终止流程',
                  cb: () => {
                    openModalForm(true, ControlType.END);
                  },
                },
              ],
            ],
          },
        ],
        auto: {
          url: `${workflowApi}/act-prearranged/all`,
          params: {
            query: {
              userId: userStore.getUserInfo.id,
            },
          },
          form: {
            labelWidth: 120,
            actionColOptions: {
              span: 24,
            },
            schemas: [],
          },
        },
        columns: [
          {
            title: '流程编号',
            dataIndex: 'businessKey',
          },
          {
            title: '实例标题',
            dataIndex: 'procInstName',
            slots: { customRender: 'name' },
          },
          {
            title: '流程名称',
            dataIndex: 'procDefName',
          },
          {
            title: '流程状态',
            dataIndex: 'statusName',
          },
          {
            title: '发起人',
            dataIndex: 'creatorName',
          },
          {
            title: '发起时间',
            dataIndex: 'createTime',
          },
          {
            title: '当前节点',
            dataIndex: 'currentTask',
          },
          {
            title: '待审批人',
            dataIndex: 'currentTaskAssigneeName',
          },
          {
            title: '操作',
            dataIndex: 'action',
            slots: { customRender: 'action' },
          },
        ],
      },
      filterSchemas: [
        {
          field: 'status',
          component: 'ApiSelect',
          label: '流程状态',
          colProps: {
            span: 8,
          },
          componentProps: {
            mode: 'multiple',
            api: () => new Api(workflowApi).fetch({}, 'act-system/proc-inst-status/page', 'POST'),
            labelField: 'name',
            valueField: 'code',
          },
        },
        {
          field: 'create_time',
          component: 'RangePicker',
          label: '发布时间',
          colProps: {
            span: 12,
          },
          componentProps: {},
        },
      ],
    });

    const getIds = (rows) => {
      let resIds: string[] = [];
      rows.forEach((item) => {
        resIds.push(item.procInstId);
      });
      return resIds;
    };

    function goDetail(row) {
      if (!isPower('LCSL_container_button_01', powerData, true)) {
        return;
      }
      router.push({
        path: '/flowcenter/detail',
        query: {
          id: row.id,
          processInstanceId: row.procInstId,
          processDefinitionId: row.procDefId,
        },
      });
    }

    return {
      initData() {
      },
      powerData,
      isPower,
      ...toRefs(state),
      instanceTableRef,
      registerForm,
      setModalFormProps,
      goDetail,
      onConfirm({ type, name }) {
        if (type === ControlType.END) {
          const rows = instanceTableRef.value.getSelectRows();
          const ids = rows.length > 0 ? getIds(rows) : [state.procInstId];
          new Api(workflowApi)
            .fetch(
              {
                procInstIds: ids,
                reason: name,
                userId: userStore.getUserInfo.id,
              },
              'act-inst/stop',
              'DELETE',
            )
            .then(() => {
              message.success('操作成功');
              instanceTableRef.value.reload();

              if (state.actionItem) {
                // 单条
              } else {
              }
              state.actionItem = null;
            });
        }
      },
      abort(row) {
        state.procInstId = row.procInstId;
        state.actionItem = row;
        openModalForm(true, ControlType.END);
      },
    };
  },
});
</script>
<style scoped lang="less">
  .btns-header {
    display: flex;
    flex-direction: row;
  }
</style>
