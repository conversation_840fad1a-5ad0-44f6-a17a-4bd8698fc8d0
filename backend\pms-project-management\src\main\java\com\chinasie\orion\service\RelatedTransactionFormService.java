package com.chinasie.orion.service;





import com.chinasie.orion.domain.entity.RelatedTransactionForm;
import com.chinasie.orion.domain.dto.RelatedTransactionFormDTO;
import com.chinasie.orion.domain.vo.RelatedTransactionFormVO;
import java.lang.String;
import java.util.List;

import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import java.lang.String;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * RelatedTransactionForm 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-31 14:07:31
 */
public interface RelatedTransactionFormService  extends OrionBaseService<RelatedTransactionForm>{
    /**
     *  详情
     *
     * * @param id
     */
    RelatedTransactionFormVO detail(String id,String pageCode)throws Exception;

    /**
     *  新增
     *
     * * @param relatedTransactionFormDTO
     */
    String create(RelatedTransactionFormDTO relatedTransactionFormDTO)throws Exception;

    /**
     *  编辑
     *
     * * @param relatedTransactionFormDTO
     */
    Boolean edit(RelatedTransactionFormDTO relatedTransactionFormDTO)throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<RelatedTransactionFormVO> pages( Page<RelatedTransactionFormDTO> pageRequest)throws Exception;

    /**
     *  导入校验
     *
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response)throws Exception;

    /**
     *  导入校验
     *
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


    /**
     *  确认导入
     *
     * * @param importId
     */
    Boolean importByExcel(String importId)throws Exception;

    /**
     *  取消导入
     *
     * * @param importId
     */
    Boolean importCancelByExcel(String importId)throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void  setEveryName(List<RelatedTransactionFormVO> vos)throws Exception;

}
