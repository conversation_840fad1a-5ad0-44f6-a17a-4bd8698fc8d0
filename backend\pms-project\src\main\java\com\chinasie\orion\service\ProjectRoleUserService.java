package com.chinasie.orion.service;

import com.chinasie.orion.base.api.domain.vo.SimpleUserVO;
import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.entity.ProjectRoleUser;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.domain.vo.SimpleVO;
import com.chinasie.orion.sdk.domain.vo.business.RoleVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/06/10:47
 * @description:
 */
public interface ProjectRoleUserService extends OrionBaseService<ProjectRoleUser> {

    /**
     * 批量新增成员
     *
     * @param projectRoleUserDTOList
     * @return
     * @throws Exception
     */
    List<String> saveBatchProRoleUser(List<ProjectRoleUserDTO> projectRoleUserDTOList) throws Exception;

    /**
     * 项目立项_批量新增成员
     *
     * <AUTHOR>
     * @date 2023/11/07 14:42
     */
    List<String> saveBatchApprovalProjectRoleUser(List<ProjectRoleUserDTO> projectRoleUserDTOList) throws Exception;

    /**
     * 名称模糊查询列表
     *
     * @param name
     * @return
     * @throws Exception
     */
    List<SimpleVo> getProjectRoleUserList(String projectId, String name) throws Exception;

    /**
     * 获取成员分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    Page<ProjectRoleUserVO> getProjectRoleUserPage(Page<UserQueryDTO> pageRequest) throws Exception;


    /**
     * 立项详情_成员分页
     *
     * <AUTHOR>
     * @date 2023/11/07 14:21
     */
    Page<ProjectRoleUserVO> getProjectApprovalRoleUserPage(Page<UserQueryDTO> pageRequest) throws Exception;

    /**
     * 获取成员详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    ProjectRoleUserVO getProjectRoleUserDetail(String id,String pageCode) throws Exception;

    /**
     * 批量删除成员
     *
     * @param idList
     * @return
     * @throws Exception
     */
    Boolean removeProjectRoleUser(List<String> idList) throws Exception;

    /**
     * 搜索项目下的用户
     *
     * @param keywordDto
     * @return
     * @throws Exception
     */
    List<ProjectRoleUserSearchVO> searchProjectUser(KeywordDto keywordDto) throws Exception;

    /**
     * 通过用户ids搜索用户列表
     *
     * @param projectId
     * @param ids
     * @return
     * @throws Exception
     */
    List<ProjectRoleUserSearchVO> getProjectRoleUserByIds(String projectId, List<String> ids) throws Exception;

    /**
     * 获取列表通过角色ID和项目ID
     *
     * @param businessIdList
     * @param projectId
     * @return
     * @throws Exception
     */
    List<ProjectRoleUserSearchVO> getListByRoleIdAndProjectId(List<String> businessIdList, String projectId) throws Exception;

    List<UserVO> getSimpleUserList(String projectId) throws Exception;

     List<SimpleUserVO> getSimpleUserByProjectId(String projectId) throws Exception;

    List<UserVO> getAllSimpleUserList(String projectId) throws Exception;

    List<UserVO> getUserListByProjectId(String projectId) throws Exception;

    List<String> getAllUserListByProjectId(String projectId) throws Exception;

    /**
     * 获取对应项目对应角色的用户ID
     *
     * @param projectId
     * @param ruleCode
     * @return
     */
    List<ProjectRoleUser> findUserListByCode(String projectId, String ruleCode) throws Exception;

    /**
     * 获取当前用户的在某个项目的所有项目角色
     *
     * @param projectId 项目ID
     * @return
     * @throws Exception
     */
    List<SimpleRoleVO> getCurrentUserRoleByProjectId(String projectId) throws Exception;


    /**
     * 通过项目信息 以及 用户信息 获取 所处角色信息列表
     *
     * @param roleByUserParamDTO
     * @return
     */
    List<SimpleRoleVO> getRoleInfoByProjectAndUser(RoleByUserParamDTO roleByUserParamDTO) throws Exception;

    List<ProjectUserInfoVO> listProjectUser(String projectId) throws Exception;

    List<ProjectUserInfoVO> listAllProjectUser(String projectId) throws Exception;


    ProjectUserInfoVO getUserInfoByProjectId(String projectId, String userCode) throws Exception;

    /**
     * 通过项目信息获取所有项目成员
     *
     * @param projectId
     * @return
     */
    List<SimpleVO> getAllUserByProjectId(String projectId) throws Exception;

    /**
     * 是否是项目经理
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    boolean isPmRoleUser(String projectId) throws Exception;

    /**
     * 通過項目id 以及 角色id获取用户列表
     *
     * @param projectId
     * @param roleId
     * @return
     * @throws Exception
     */
    List<String> getAllUserIdListByProjectIdAndRoleId(String projectId, String roleId) throws Exception;

    List<String> getRoleInfoByProjectAndUserId(String projectId, String userId) throws Exception;

    List<RoleVO> getRoleByProjectAndUserId(String projectId, String userId) throws Exception;

    List<SimpleVO> getDeptListByProject(String projectId) throws Exception;

    /**
     * 当前用户是否是项目成员
     *
     * <AUTHOR>
     * @date 2023/11/03 00:50
     */
    Boolean projectExistCurrentUser(String projectId) throws Exception;


    /**
     *  获取数据用户列表 通过项目ID列表和角色ID列表
     * @param projectIdList
     * @param roleIdList
     * @return
     * @throws Exception
     */
    Map<String,List<String>> getAllUserIdListByProjectIdListAndRoleIdList(List<String> projectIdList, List<String> roleIdList) throws Exception;



    /**
     * 通过项目ID里列表和角色ID列表获取所有人员列表
     * @param roleUserParamDTO
     * @return
     */
    List<String> getUserIdToProjectIdsAndRoleIds(RoleUserParamDTO roleUserParamDTO);

    List<ProjectRoleUserVO> getUserByProjectIds(List<String> projectIds);

    /**
     *  通过项目角色ID列表 项目ID对应的用户ID列表
     * @param projectRoleIdList
     * @return
     */
    Map<String, List<String>> getProjectIdToListByProjectRoleIdList(List<String> projectRoleIdList);
}
