package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.JobMaterialStatisticDTO;
import com.chinasie.orion.domain.dto.job.StartEndDTO;
import com.chinasie.orion.domain.dto.material.InAndOutDTO;
import com.chinasie.orion.domain.dto.material.JobMaterialParamDTO;
import com.chinasie.orion.domain.entity.JobManage;
import com.chinasie.orion.domain.entity.JobMaterial;
import com.chinasie.orion.domain.dto.JobMaterialDTO;
import com.chinasie.orion.domain.vo.JobMaterialVO;

import java.lang.String;
import java.util.List;


import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * JobMaterial 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-09 15:47:40
 */
public interface JobMaterialService extends OrionBaseService<JobMaterial> {


    List<String> listByMaterialId(String rsp);

    /**
     * 详情
     * <p>
     * * @param id
     */
    JobMaterialVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param jobMaterialDTO
     */
    String create(JobMaterialDTO jobMaterialDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param jobMaterialDTO
     */
    Boolean edit(JobMaterialDTO jobMaterialDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    Boolean removeBatchNew(JobMaterialParamDTO jobMaterialParamDTO);

    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<JobMaterialVO> pages(Page<JobMaterialDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<JobMaterialVO> vos) throws Exception;

    List<JobMaterial> listByRepairRound(String repairRound);

    /**
     * 修改作业物资上的物资ID信息
     * @param jobId
     * @param materialId
     * @param jobId
     * @param id1
     */
    void updateMaterialId(String jobId, String leadgerId, String materialId, String id1,String number);

    /**
     *  修改所有数据中 的 台账ID为新的台账ID
     * @param id
     * @param id1
     */
    void updateMaterialLedgerId(String id, String id1);

    void existsData(String jobId, String number) throws Exception;

    void copyByJoIdToTargetId(String sourceId, List<String> targetIdList);

    List<String> getNumberListByJobId(String jobId);

    Boolean editDate(StartEndDTO startEndDTO);
}
