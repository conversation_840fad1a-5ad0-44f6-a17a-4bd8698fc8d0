<script setup lang="ts">
import {
  Form, FormItem, Input, InputGroup, RangePicker,
} from 'ant-design-vue';
import {
  BasicButton, BasicCard, InputSelectUser, Select, SelectDictVal, TreeSelectOrg,
} from 'lyra-component-vue3';
import { reactive } from 'vue';
import { cloneDeep } from 'lodash-es';

const props = defineProps<{
  width: number
  filterOptions: any[]
}>();

const emits = defineEmits<{
  (e: 'search', query: any): void
}>();

const model: Record<string, any> = reactive(cloneDeep(formatFilterModel(props.filterOptions)));

const modelCache: Record<string, any> = {};

function onModelInit() {
  Object.assign(model, cloneDeep(formatFilterModel(props.filterOptions)));
}

function formatFilterModel(options: any[]) {
  const obj = {};
  options.reduce((prev, next) => prev.concat(next.forms), []).forEach((item) => {
    let value;
    switch (item.component) {
      case 'Input':
      case 'Select':
      case 'TreeSelectOrg':
        value = null;
        break;
      case 'SelectUser':
      case 'RangePicker':
        value = [];
        break;
    }
    obj[Array.isArray(item.field) ? item.field[0] : item.field] = {
      value,
      condition: item.condition,
      component: item.component,
      field: item.field,
    };
  });
  return obj;
}

function getCondition(component: string) {
  switch (component) {
    case 'Input':
      return [
        {
          label: '包含',
          value: 'like',
        },
        {
          label: '不包含',
          value: 'not like',
        },
        {
          label: '等于',
          value: '=',
        },
        {
          label: '不等于',
          value: '<>',
        },
      ];
    case 'SelectUser':
    case 'TreeSelectOrg':
    case 'Select':
    case 'SelectDictVal':
      return [
        {
          label: '等于',
          value: '=',
        },
        {
          label: '不等于',
          value: '<>',
        },
      ];
  }
}

function inputSelectUserChange(users: any[], field: string) {
  if (users) {
    model[field].value = users.map((item) => item.id).join('');
    model[field].formValue = users;
  } else {
    model[field].value = '';
    model[field].formValue = [];
  }
}

function isTruthy(value) {
  // 检查是否为布尔类型
  if (typeof value === 'boolean') {
    return true;
  }

  // 对于数组，检查其length属性
  if (Array.isArray(value)) {
    return value.length > 0;
  }

  // 对于其他类型，直接转换为布尔值
  return Boolean(value);
}

function handleSearch() {
  const values = [];
  Object.keys(model).filter((key: string) => isTruthy(model[key].value)).forEach((key) => {
    switch (model[key].component) {
      case 'RangePicker':
        values.push(`${key} BETWEEN '${model[key].value[0]}' AND '${model[key].value[1]}'`);
        break;
      default:
        values.push(`${key} ${model[key].condition} ${typeof model[key].value === 'boolean' ? model[key].value : (model[key].condition.indexOf('like') === -1 ? `'${model[key].value}'` : `'%${model[key].value}%'`)}`);
    }
  });
  Object.assign(modelCache, model);
  emits('search', values);
}

function changeSelect(field: string) {
  if (field !== 't.work_package_status') return;
  if (model['t.work_package_status'].value) {
    model['t.is_major_project'].value = true;
    model['t.is_major_project'].condition = '=';
  } else {
    model['t.is_major_project'].value = undefined;
  }
}

defineExpose({
  setCache() {
    Object.assign(model, modelCache);
  },
});
</script>

<template>
  <div
    :style="{width:(width-20)+'px'}"
    class="filter-container"
    @click.prevent.stop
  >
    <Form
      noStyle
      :model="model"
      :label-col="{ style: { width: '130px' } }"
    >
      <BasicCard
        v-for="(item,index) in filterOptions"
        :key="index"
        :title="item.title"
      >
        <template #titleRight>
          <BasicButton
            v-if="index===0"
            @click="onModelInit"
          >
            重置
          </BasicButton>
          <BasicButton
            v-if="index===0"
            type="primary"
            style="margin-right: 0"
            @click="handleSearch"
          >
            筛选
          </BasicButton>
        </template>
        <div
          class="forms-grid"
          :style="item.style||{}"
        >
          <FormItem
            v-for="(form,formIndex) in item.forms"
            :key="formIndex"
            class="w-full flex mb0"
            :style="form.style||{}"
            :label="form.label"
          >
            <InputGroup compact>
              <Select
                v-if="form.component!=='RangePicker'&&form.component!=='empty'"
                v-model:value="model[form.field].condition"
                style="width: 85px;flex-shrink: 0"
                :disabled="form.field==='t.is_major_project' && model['t.work_package_status'].value"
                :options="getCondition(form.component)"
              />

              <Input
                v-if="form.component==='Input'"
                v-model:value="model[form.field].value"
                :maxlength="50"
                allowClear
              />
              <Select
                v-if="form.component==='Select'"
                v-model:value="model[form.field].value"
                :disabled="form.field==='t.is_major_project' && model['t.work_package_status'].value"
                v-bind="form.componentProps"
                allowClear
                @change="changeSelect(form.field)"
              />
              <RangePicker
                v-if="form.component==='RangePicker'"
                v-model:value="model[Array.isArray(form.field)?form.field[0]:form.field].value"
                value-format="YYYY-MM-DD"
              />
              <InputSelectUser
                v-if="form.component==='SelectUser'"
                :selectUserData="model[form.field].formValue||[]"
                :selectUserModalProps="{
                  selectType:'radio'
                }"
                @change="(users)=>inputSelectUserChange(users,form.field)"
              />
              <TreeSelectOrg
                v-if="form.component==='TreeSelectOrg'"
                v-model:value="model[form.field].value"
                allowClear
              />
              <SelectDictVal
                v-if="form.component==='SelectDictVal'"
                v-model:value="model[form.field].value"
                v-bind="form.componentProps"
              />
            </InputGroup>
          </FormItem>
        </div>
      </BasicCard>
    </Form>
  </div>
</template>

<style scoped lang="less">
.filter-container {
  position: absolute;
  top: 50px;
  left: 10px;
  background-color: #fff;
  box-shadow: 0 3px 6px -4px #0000001f, 0 6px 16px 0 #00000014, 0 9px 28px 8px #0000000d;
  border-radius: 2px;
  padding: 12px 16px;
  z-index: 10;
}

.forms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  justify-items: end;
  gap: 15px 10px;
  padding: 15px 0;
}

:deep(.ant-input-group.ant-input-group-compact) {
  display: flex;

  & > :last-child {
    flex-grow: 1;
    width: 0;
  }
}

:deep(.ant-input-search) {
  width: 100% !important;
  margin-left: 0 !important;
}

:deep(.ant-input-search > .ant-input-wrapper > .ant-input) {
  border-radius: 0 !important;
}
</style>
