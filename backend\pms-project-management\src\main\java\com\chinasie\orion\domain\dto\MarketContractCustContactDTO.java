package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * MarketContractCustContact DTO对象
 *
 * <AUTHOR>
 * @since 2024-09-06 16:48:09
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MarketContractCustContactDTO对象", description = "市场合同-客户-联系人")
@Data
@ExcelIgnoreUnannotated
public class MarketContractCustContactDTO extends ObjectDTO implements Serializable {

    /**
     * 市场合同id，pms_market_contract id
     */
    @ApiModelProperty(value = "市场合同id，pms_market_contract id")
    @ExcelProperty(value = "市场合同id，pms_market_contract id ", index = 0)
    private String contractId;

    /**
     * 客户联系人id，pms_customer_contact id
     */
    @ApiModelProperty(value = "客户联系人id，pms_customer_contact id")
    @ExcelProperty(value = "客户联系人id，pms_customer_contact id ", index = 1)
    private String custContactId;

    /**
     * 联系人名称
     */
    @ApiModelProperty(value = "联系人名称")
    @ExcelProperty(value = "联系人名称 ", index = 2)
    private String contactName;

    /**
     * 联系人手机号
     */
    @ApiModelProperty(value = "联系人手机号")
    @ExcelProperty(value = "联系人手机号 ", index = 3)
    private String contactPhone;

    /**
     * 联系人类型；business.商务联系人；technology.技术负责人
     */
    @ApiModelProperty(value = "联系人类型；business.商务联系人；technology.技术负责人")
    @ExcelProperty(value = "联系人类型；business.商务联系人；technology.技术负责人 ", index = 4)
    private String contactType;


}
