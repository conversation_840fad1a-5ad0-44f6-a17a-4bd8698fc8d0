package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
/**
 * ProjectSchemeApplyApprovalVO
 *
 * @author: yangFy
 * @date: 2023/4/19 15:50
 * @description:
 * <p>
 *
 * </p>
 */
@Data
@ApiModel(value = "ProjectSchemeApplyApprovalVO对象", description = "项目计划申请审批")
public class ProjectSchemeApplyApprovalVO extends ObjectVO {

    /**
     *
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;
    /**
     *
     */
    @ApiModelProperty(value = "项目计划id")
    private String projectSchemeId;
    /**
     *
     */
    @ApiModelProperty(value = "内容")
    private String content;
    /**
     *
     */
    @ApiModelProperty(value = "审批人")
    private String certifier;
    /**
     *
     */
    @ApiModelProperty(value = "是否通过")
    private String agreement;
    /**
     *
     */
    @ApiModelProperty(value = "审批意见")
    private String feedBack;

}
