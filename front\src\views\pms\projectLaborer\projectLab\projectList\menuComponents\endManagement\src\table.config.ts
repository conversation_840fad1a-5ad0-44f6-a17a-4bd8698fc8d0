export const columns = [
  {
    title: '编号',
    dataIndex: 'number',
    align: 'left',
    key: 'number',

    width: '120px',
    sorter: true,
    ellipsis: true,
  },
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',

    width: '200px',
    align: 'left',
    slots: { customRender: 'name' },
    sorter: true,
    ellipsis: true,
  },

  {
    title: '结项类型',
    dataIndex: 'typeName',
    key: 'type',
    width: '70px',
    margin: '0 20px 0 0',
    align: 'left',
    slots: { customRender: 'type' },
    sorter: true,
    ellipsis: true,
  },
  {
    title: '开始日期',
    dataIndex: 'startTime',
    key: 'startTime',
    width: '70px',
    align: 'left',
    slots: { customRender: 'startTime' },
    sorter: true,
    ellipsis: true,
  },
  {
    title: '实际结束日期',
    dataIndex: 'endTime',
    key: 'endTime',

    width: '70px',
    align: 'left',
    slots: { customRender: 'endTime' },
    sorter: true,
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'statusName',
    key: 'status',

    width: '50px',
    align: 'left',
    sorter: true,
    ellipsis: true,
    slots: { customRender: 'status' },
  },
  {
    title: '负责人',
    dataIndex: 'principalName',
    key: 'principalName',

    width: '80px',
    align: 'left',
    sorter: true,
    ellipsis: true,
    slots: { customRender: 'principalName' },
  },
  {
    title: '修改日期',
    dataIndex: 'modifyTime',
    key: 'modifyTime',

    width: '90px',
    align: 'left',
    sorter: true,
    ellipsis: true,
    slots: { customRender: 'modifyTime' },
  },
];
