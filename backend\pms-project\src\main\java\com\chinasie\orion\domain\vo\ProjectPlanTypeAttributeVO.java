package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;

/**
 * ProjectPlanTypeTypeAttribute VO对象
 *
 * <AUTHOR>
 * @since 2024-03-26 10:54:06
 */
@ApiModel(value = "ProjectPlanTypeTypeAttributeVO对象", description = "项目计划类型属性")
@Data
public class ProjectPlanTypeAttributeVO extends ObjectVO implements Serializable{

            /**
         * 编码
         */
        @ApiModelProperty(value = "编码")
        private String number;

        /**
         * 选项值
         */
        @ApiModelProperty(value = "选项值")
        private String options;

        /**
         * 名称
         */
        @ApiModelProperty(value = "名称")
        private String name;

        /**
         * 是否必填
         */
        @ApiModelProperty(value = "是否必填")
        private Integer require;

        /**
         * 编码
         */
        @ApiModelProperty(value = "编码")
        private String code;



        /**
         * 关系id
         */
        @ApiModelProperty(value = "关系id")
        private String relationId;

        /**
         * 类型id
         */
        @ApiModelProperty(value = "类型id")
        private String typeId;

        /**
         * 所属类型
         */
        @ApiModelProperty(value = "所属类型")
        private String typeName;

        private Boolean currentType;

    }
