package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * ProjectMaterialPlan DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-11 15:34:49
 */
@ApiModel(value = "ProjectMaterialPlanDTO对象", description = "项目物资计划")
@Data
@ExcelIgnoreUnannotated
public class ProjectMaterialPlanDTO extends  ObjectDTO   implements Serializable{

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    @NotBlank(message = "物料编码不能为空")
    private String number;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @NotBlank(message = "项目id不能为空")
    private String projectId;

    /**
     * 物料id
     */
    @ApiModelProperty(value = "物料id")
    @NotBlank(message = "物料id不能为空")
    private String materialId;

    /**
     * 计划使用时间
     */
    @ApiModelProperty(value = "计划使用时间")
    private Date planUseTime;

    /**
     * 计划数量
     */
    @ApiModelProperty(value = "计划数量")
    @NotNull(message = "计划数量不能为空")
    private Integer planNum;

    /**
     * 基本单位
     */
    @ApiModelProperty(value = "基本单位")
    private String baseUnit;


    /**
     * 采购周期
     */
    @ApiModelProperty(value = "采购周期")
    private BigDecimal procurementCycle;

}

