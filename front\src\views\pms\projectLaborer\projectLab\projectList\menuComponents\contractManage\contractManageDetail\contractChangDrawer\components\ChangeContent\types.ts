// 字段所对应的更改组件枚举类型
export enum FieldTypeEnum {
  // 文本
  TEXT = 'text',
  // 字符串
  STRING = 'string',
  // 整型
  INTEGER = 'integer',
  // 金额
  DECIMAL = 'decimal',
  // 数字型
  NUMBER = 'number',
  // 布尔型
  BOOLEAN = 'boolean',
  // 日期时间类型
  DATETIME = 'dateTime',
  // 日期类型
  DATE = 'date',
  // 时间类型
  TIME = 'time',
  // 字典类型
  DICT = 'dict',
  // 用户类型
  USER = 'user',
  // 部门类型
  DEPT = 'dept'
}

// 表格行数据
export interface RecordItem {
  // id
  id: string,
  // 已选字段
  fieldCode: string,
  // 变更前的数据
  oldValue: string,
  // 变更后的新数据
  newValue: string
}

// 可更改字段项
export interface FormOptionsItem {
  // 字段编码
  fieldCode: string,
  // 字段名称
  fieldName: string,
  // 组件类型
  fieldType:FieldTypeEnum
  // 变更前的值
  fieldValue: String
}
