package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.domain.dto.train.SimpleDTO;
import com.chinasie.orion.domain.vo.train.RolePermission;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.Boolean;
import java.lang.String;

import java.util.List;

/**
 * TrainManage VO对象
 *
 * <AUTHOR>
 * @since 2024-06-04 17:09:01
 */
@ApiModel(value = "TrainManageVO对象", description = "培训管理")
@Data
public class TrainManageVO extends ObjectVO implements Serializable {

    /**
     * 培训编码
     */
    @ApiModelProperty(value = "培训编码")
    private String trainNumber;
    /**
     * 培训编码
     */
    @ApiModelProperty(value = "培训唯一")
    private String trainKey;


    /**
     * 培训类型
     */
    @ApiModelProperty(value = "培训类型")
    private String type;

    @ApiModelProperty(value = "培训类型")
    private String typeName;
    /**
     * 培训名称
     */
    @ApiModelProperty(value = "培训名称")
    private String name;


    /**
     * 是否考核
     */
    @ApiModelProperty(value = "是否考核")
    private Boolean isCheck;


    /**
     * 培训基地编码
     */
    @ApiModelProperty(value = "培训基地编码")
    private String baseCode;


    /**
     * 培训基地名称
     */
    @ApiModelProperty(value = "培训基地名称")
    private String baseName;


    /**
     * 拟完成时间
     */
    @ApiModelProperty(value = "拟完成时间")
    private Date completeDate;


    /**
     * 培训课时
     */
    @ApiModelProperty(value = "培训课时")
    private BigDecimal lessonHour;


    /**
     * 办结时间
     */
    @ApiModelProperty(value = "办结时间")
    private Date endDate;


    /**
     * 培训内容
     */
    @ApiModelProperty(value = "培训内容")
    private String content;

    //todo 到期时间  规则缺失需要确定
    @ApiModelProperty(value = "到期时间")
    private Date expireTime;

    @ApiModelProperty(value = "有效期限（月）")
    private Integer expirationMonth;


    @ApiModelProperty(value = "参培中心--来源组织结构的信息")
    private List<SimpleDTO> attendCenterCodeList;
    /**
     * 培训地点
     */
    @ApiModelProperty(value = "培训地点")
    private String trainAddress;

    /**
     * 培训讲师
     */
    @ApiModelProperty(value = "培训讲师")
    private String trainLecturer;

    @ApiModelProperty(value = "中心ID:只有当数据需要考核的时候才有值")
    private String  trainCenterId;

    @ApiModelProperty(value = "数据权限")
    private RolePermission rolePermission;
    @ApiModelProperty(value = "是否拥有操作权限：编辑/删除")
    private Boolean edit;
}
