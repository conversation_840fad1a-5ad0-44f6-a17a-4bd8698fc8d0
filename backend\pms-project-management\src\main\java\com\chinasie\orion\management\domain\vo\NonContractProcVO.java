package com.chinasie.orion.management.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * NonContractProc VO对象
 *
 * <AUTHOR>
 * @since 2024-06-12 11:04:24
 */
@ApiModel(value = "NonContractProcVO对象", description = "无合同采购表")
@Data
public class NonContractProcVO extends ObjectVO implements Serializable {

    /**
     * 工作主题
     */
    @ApiModelProperty(value = "工作主题")
    private String workTopic;


    /**
     * 流程名称
     */
    @ApiModelProperty(value = "流程名称")
    private String processName;


    /**
     * 发起时间
     */
    @ApiModelProperty(value = "发起时间")
    private Date initiationTime;


    /**
     * 发起人
     */
    @ApiModelProperty(value = "发起人")
    private String initiator;


    /**
     * 报销人
     */
    @ApiModelProperty(value = "报销人")
    private String claimant;


    /**
     * 申请公司编码
     */
    @ApiModelProperty(value = "申请公司编码")
    private String applyCompanyCode;


    /**
     * 申请公司名称
     */
    @ApiModelProperty(value = "申请公司名称")
    private String applyCompanyName;


    /**
     * 申请部门
     */
    @ApiModelProperty(value = "申请部门")
    private String applyDept;


    /**
     * 费用归属公司编码
     */
    @ApiModelProperty(value = "费用归属公司编码")
    private String expenseCompanyCode;


    /**
     * 费用归属公司名称
     */
    @ApiModelProperty(value = "费用归属公司名称")
    private String xpenseCompanyName;


    /**
     * 报销金额
     */
    @ApiModelProperty(value = "报销金额")
    private String reimbursedAmount;


    /**
     * 要求付款时间
     */
    @ApiModelProperty(value = "要求付款时间")
    private Date reqPaymentTime;


    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    private String currency;


    /**
     * 折合人民币
     */
    @ApiModelProperty(value = "折合人民币")
    private String inRmb;


    /**
     * 申请原因
     */
    @ApiModelProperty(value = "申请原因")
    private String applyReason;


    /**
     * 费用信息
     */
    @ApiModelProperty(value = "费用信息")
    private String expenseInfo;


    /**
     * 供应商信息
     */
    @ApiModelProperty(value = "供应商信息")
    private String supplierInfo;


    /**
     * 是否内部交易
     */
    @ApiModelProperty(value = "是否内部交易")
    private Boolean isInternalTx;


    /**
     * 内部交易号
     */
    @ApiModelProperty(value = "内部交易号")
    private String internalTxNumber;


    /**
     * 流程状态
     */
    @ApiModelProperty(value = "流程状态")
    private String processStatus;


    /**
     * 支付方式
     */
    @ApiModelProperty(value = "支付方式")
    private String paymentWay;


    /**
     * 立项类别
     */
    @ApiModelProperty(value = "立项类别")
    private String projectCategory;


    /**
     * 立项号
     */
    @ApiModelProperty(value = "立项号")
    private String projectCode;


    /**
     * 立项名称
     */
    @ApiModelProperty(value = "立项名称")
    private String projectName;


    /**
     * 归口部门
     */
    @ApiModelProperty(value = "归口部门")
    private String bkDept;


    /**
     * 支付金额
     */
    @ApiModelProperty(value = "支付金额")
    private String paymentAmount;


    /**
     * 交易金额
     */
    @ApiModelProperty(value = "交易金额")
    private String transactionAmount;


    /**
     * 实际支付金额
     */
    @ApiModelProperty(value = "实际支付金额")
    private String actualPaymentAmount;


    /**
     * 申请笔数
     */
    @ApiModelProperty(value = "申请笔数")
    private String applyNumber;


    /**
     * 报销金额
     */
    @ApiModelProperty(value = "报销金额")
    private String reimbursementAmount;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


}
