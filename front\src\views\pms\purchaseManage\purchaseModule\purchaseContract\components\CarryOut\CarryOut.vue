<script setup lang="ts">
import {
  BasicButton, BasicCard, BasicTableAction, IOrionTableActionItem, OrionTable,
} from 'lyra-component-vue3';
import {
  h,
  inject, nextTick, reactive, ref, Ref, unref, watchEffect,
} from 'vue';
import { useRoute } from 'vue-router';
import dayjs from 'dayjs';
import { isBoolean } from 'lodash-es';
import {
  openFormDrawer, parseBooleanToRender, parsePriceByNumber, setBasicInfo,
} from '../../../utils';
import Api from '/@/api';
import { BasicInjectionsKey } from '../../../tokens/basicKeys';
import PurchaseContractForm2 from '../PurchaseContractForm2.vue';

const route = useRoute();
const recordTableRef = ref();
const notifyUpdateStr = inject('refreshPurchaseExecutionChangeRecord');
const basicInfo = inject(BasicInjectionsKey);
// 采购合同执行信息
const baseInfoProps = reactive({
  list: setBasicInfo([
    {
      label: '实际合同开始日期',
      field: 'actualStartTime',
      formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') || '' : '--'),
    },
    {
      label: '实际合同结束日期',
      field: 'actualEndTime',
      formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') || '' : '--'),
    },
    {
      label: '预计合同开始日期',
      field: 'estimatedStartTime',
      formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') || '' : '--'),
    },
    {
      label: '预计合同结束日期',
      field: 'estimatedEndTime',
      formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') || '' : '--'),
    },
    {
      label: '预计合同交付日期',
      field: 'estimatedDeliveryTime',
      formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') || '' : '--'),
    },
    {
      label: '实际验收日期',
      field: 'actualAcceptanceTimes',
      formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') || '' : '--'),
    },
    {
      label: '合同履约状态',
      field: 'executionStatusName',
    },
    {
      label: '验收结果',
      field: 'acceptanceResults',
    },
  ]),
  column: 3,
  dataSource: basicInfo.data,
});
// 支付里程碑
const payMilestoneRef = ref();
const tableOptions = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  isSpacing: true,
  scroll: {
    y: 200,
  },
  smallSearchField: undefined,
  columns: [
    {
      title: '操作',
      dataIndex: 'actions',
      width: 100,
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
    {
      title: '支付编号',
      dataIndex: 'payNumber',
    },
    {
      title: '支付申请人',
      dataIndex: 'payReqUserName',
    },
    {
      title: '支付申请发起时间',
      dataIndex: 'payReqStartTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '支付类型',
      dataIndex: 'payType',
    },
    {
      title: '预计付款时间',
      dataIndex: 'estimatedPayTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '实际交付时间',
      dataIndex: 'actualDeliveryTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '是否一次验收合格',
      dataIndex: 'isOnetimeAcceptance',
      customRender({ text }) {
        return parseBooleanToRender(text);
      },
    },
    {
      title: '实际验收时间',
      dataIndex: 'actualAcceptanceTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '原因',
      dataIndex: 'reason',
    },
    {
      title: '是否按时交付',
      dataIndex: 'isDeliverOnTime',
      customRender({ text }) {
        return parseBooleanToRender(text);
      },
    },
    {
      title: '未按时交付验收原因',
      dataIndex: 'reasonOfUndeliver',
    },
    {
      title: '里程碑业务描述',
      dataIndex: 'milestoneDesc',
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
    },
    {
      title: '本次支付汇总金额',
      dataIndex: 'currentPayTotalAmount',
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '支付比例',
      dataIndex: 'payRatio',
    },
    {
      title: '已支付金额',
      dataIndex: 'paidAmount',
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '币种',
      dataIndex: 'currency',
    },
    {
      title: '是否有质保金',
      dataIndex: 'isHaveQualityAmount',
      customRender({ text }) {
        return parseBooleanToRender(text);
      },
    },
  ],
  api: (params:Record<string, any>) => new Api('/pms/actualPayMilestone/getByCode').fetch({
    ...params,
    query: {
      contractNumber: baseInfoProps.dataSource?.contractNumber,
    },
  }, '', 'POST'),
};
const actions: IOrionTableActionItem[] = [
  {
    text: '编辑',
    event: 'edit',
  },
];
// 合同变更信息
const tableOptions2 = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  isSpacing: true,
  scroll: {
    y: 200,
  },
  smallSearchField: undefined,
  columns: [
    {
      title: '变更编号',
      dataIndex: 'changeId',
    },
    {
      title: '变更标题',
      dataIndex: 'changeTitle',
    },
    {
      title: '变更类型',
      dataIndex: 'changeType',
    },
    {
      title: '变更申请日期',
      dataIndex: 'changeRequestDate',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '本次变更金额',
      dataIndex: 'thisChangeAmount',
    },
    {
      title: '累计变更金额',
      dataIndex: 'cumulativeChangeAmount',
    },
    {
      title: '累计变更比率',
      dataIndex: 'cumulativeChangeRate',
    },
    {
      title: '变更后合同承诺总价（总目标值）',
      dataIndex: 'contactAmountAfterChange',
    },
  ],
  api: (params:Record<string, any>) => new Api('/pms/contractChange/getByCode').fetch({
    ...params,
    query: {
      contractNumber: baseInfoProps.dataSource?.contractNumber,
    },
  }, '', 'POST'),
};
// 采购执行变更记录
const tableOptions3 = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  isSpacing: true,
  smallSearchField: undefined,
  scroll: {
    y: 200,
  },
  columns: [
    {
      title: '变更人',
      dataIndex: 'changer',
    },
    {
      title: '变更日期',
      dataIndex: 'changeDate',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '变更内容',
      dataIndex: 'changeContent',
    },
    {
      title: '变更前',
      dataIndex: 'beforeChange',
    },
    {
      title: '变更后',
      dataIndex: 'afterChange',
    },
  ],
  api: (params:Record<string, any>) => new Api('/pms/purchaseExecuteShcnge/getByCode').fetch({
    ...params,
    query: {
      contractNumber: baseInfoProps.dataSource?.contractNumber,
    },
  }, '', 'POST'),
};
// 合同终止信息
const tableOptions4 = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  isSpacing: true,
  scroll: {
    y: 200,
  },
  smallSearchField: undefined,
  columns: [
    {
      title: '是否签约前终止',
      dataIndex: 'isPreSignTermination',
      customRender({ text }) {
        return parseBooleanToRender(text);
      },
    },
    {
      title: '终止申请日期',
      dataIndex: 'terminationRequestDate',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '合同终止金额',
      dataIndex: 'contractTerminationAmount',
    },
  ],
  api: (params:Record<string, any>) => new Api('/pms/contractTermination/getByCode').fetch({
    ...params,
    query: {
      contractNumber: baseInfoProps.dataSource?.contractNumber,
    },
  }, '', 'POST'),
};
// 合同索赔信息
const tableOptions5 = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  isSpacing: true,
  smallSearchField: undefined,
  columns: [
    {
      title: '索赔编号',
      dataIndex: 'claimId',
    },
    {
      title: '索赔标题',
      dataIndex: 'claimTitle',
    },
    {
      title: '索赔状态',
      dataIndex: 'claimStatus',
    },
    {
      title: '索赔方向',
      dataIndex: 'claimDirection',
    },
    {
      title: '索赔申请时间',
      dataIndex: 'claimRequestTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '索赔处理时间',
      dataIndex: 'claimProcessTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '累计索赔金额（含本次）',
      dataIndex: 'cumulativeClaimAmount',
    },
    {
      title: '总累计索赔占原合同价%',
      dataIndex: 'totalClaimPctOfOrigPrice',
    },
  ],
  api: (params:Record<string, any>) => new Api('/pms/contractClaim/getByCode').fetch({
    ...params,
    query: {
      contractNumber: baseInfoProps.dataSource?.contractNumber,
    },
  }, '', 'POST'),
};
const titleVnode = h('div', {
  class: 'custom-btn-row',
}, [
  h('span', '采购合同执行信息'),
  h(BasicButton, {
    icon: 'sie-icon-bianji',
    type: 'primary',
    onClick() {
      openFormDrawer(purchaseContractForm2, basicInfo.data, () => {

      });
    },
  }, '编辑'),
]);
const handleEditRow = async (actionItem: { event: string }, record: Record<string, any>) => {
  openFormDrawer(PurchaseContractForm2, record, () => {
    payMilestoneRef.value?.reload();
  });
};

watchEffect(async () => {
  if (notifyUpdateStr.value) {
    await nextTick();
    await recordTableRef.value?.reload();
  }
});
</script>

<template>
  <BasicCard
    :title="'采购合同执行信息'"
    :grid-content-props="baseInfoProps"
  />
  <!--  支付里程碑-->
  <BasicCard
    title="支付里程碑"
    :isBorder="false"
  >
    <div style="height: 360px;overflow: hidden;">
      <OrionTable
        ref="payMilestoneRef"
        class="scroll-table"
        :options="tableOptions"
      >
        <template #actions="{record}">
          <BasicTableAction

            :actions="actions"
            :record="record"
            @actionClick="handleEditRow($event,record)"
          />
        </template>
      </OrionTable>
    </div>
  </BasicCard>
  <!--  合同变更信息-->
  <BasicCard
    title="合同变更信息"
    :isBorder="false"
  >
    <div style="height: 360px;overflow: hidden;">
      <OrionTable
        ref="tableRef"
        class="scroll-table"
        :options="tableOptions2"
        false
      />
    </div>
  </BasicCard>
  <!--  合同索赔信息-->
  <BasicCard
    title="合同索赔信息"
    :isBorder="false"
  >
    <div style="height: 360px;overflow: hidden;">
      <OrionTable
        ref="tableRef"
        class="scroll-table"
        :options="tableOptions5"
        false
      />
    </div>
  </BasicCard>
  <!--  合同终止信息-->
  <BasicCard
    title="合同终止信息"
    :isBorder="false"
  >
    <div style="height: 360px;overflow: hidden;">
      <OrionTable
        ref="tableRef"
        class="scroll-table"
        :options="tableOptions4"
        false
      />
    </div>
  </BasicCard>
  <!--  采购执行变更记录-->
  <BasicCard
    title="采购执行变更记录"
    :isBorder="false"
  >
    <div style="height: 360px;overflow: hidden;">
      <OrionTable
        ref="recordTableRef"
        class="scroll-table"
        :options="tableOptions3"
        false
      />
    </div>
  </BasicCard>
</template>

<style scoped lang="less">
:deep(.custom-btn-row){
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.scroll-table {
  ::-webkit-scrollbar {
    height: 15px !important;
    width: 15px !important;
  }
  ::-webkit-scrollbar-thumb {
    border-radius:0;
  }
}
</style>