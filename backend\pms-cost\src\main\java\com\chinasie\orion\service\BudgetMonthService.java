package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.BudgetMonthDTO;
import com.chinasie.orion.domain.vo.BudgetMonthVO;

import java.util.List;

/**
 * <p>
 * BudgetMonth 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16 15:58:25
 */
public interface BudgetMonthService {
    /**
     * 详情
     * <p>
     * * @param id
     */
    BudgetMonthVO detail(String id) throws Exception;

    /**
     * 新增
     * <p>
     * * @param budgetMonthDTO
     */
    BudgetMonthVO create(BudgetMonthDTO budgetMonthDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param budgetMonthDTO
     */
    Boolean edit(BudgetMonthDTO budgetMonthDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


}