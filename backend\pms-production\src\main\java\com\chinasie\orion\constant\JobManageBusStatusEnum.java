package com.chinasie.orion.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/28/11:38
 * @description:
 */
public enum JobManageBusStatusEnum {

    PREPARE(121,"准备中","job_prepare")
    ,READY_COMPLETE(101,"准备完成","job_ready_complete")
    ,IMPL(110,"实施中","job_impl")
    ,FINISH(111,"已完成","job_finish")
    ;


    private Integer status;

    private String desc;

    private String code;

    JobManageBusStatusEnum(Integer status, String desc, String code) {
        this.status = status;
        this.desc = desc;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }


    public static Map<String,String> getStatusMap(){
        Map<String,String> map = new HashMap<>();
        JobManageBusStatusEnum[] manageBusStatusEnums = JobManageBusStatusEnum.values();
        for (JobManageBusStatusEnum manageBusStatusEnum : manageBusStatusEnums) {
            map.put(manageBusStatusEnum.getStatus().toString(),manageBusStatusEnum.getDesc());
        }
        return  map;
    }
}
