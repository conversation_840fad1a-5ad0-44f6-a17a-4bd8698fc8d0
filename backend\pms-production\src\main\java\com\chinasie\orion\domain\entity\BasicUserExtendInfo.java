package com.chinasie.orion.domain.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;

/**
 * BasicUserExtendInfo Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-05 10:41:49
 */
@TableName(value = "pmsx_basic_user_extend_info")
@ApiModel(value = "BasicUserExtendInfoEntity对象", description = "人员拓展信息")
@Data

public class BasicUserExtendInfo extends  ObjectEntity  implements Serializable{

    /**
     * 流程名称
     */
    @ApiModelProperty(value = "流程名称")
    @TableField(value = "process_name")
    private String processName;

    /**
     * 发起人
     */
    @ApiModelProperty(value = "发起人")
    @TableField(value = "initiator")
    private String initiator;

    /**
     * 发起时间
     */
    @ApiModelProperty(value = "发起时间")
    @TableField(value = "initiation_time")
    private Date initiationTime;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    @TableField(value = "priority")
    private String priority;

    /**
     * 申请类型
     */
    @ApiModelProperty(value = "申请类型")
    @TableField(value = "application_type")
    private String applicationType;

    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
    @TableField(value = "applicant")
    private String applicant;

    /**
     * 是否有亲属在集团内
     */
    @ApiModelProperty(value = "是否有亲属在集团内")
    @TableField(value = "has_relative_in_group")
    private String hasRelativeInGroup;

    /**
     * 亲属姓名
     */
    @ApiModelProperty(value = "亲属姓名")
    @TableField(value = "relative_name")
    private String relativeName;

    /**
     * 亲属职务
     */
    @ApiModelProperty(value = "亲属职务")
    @TableField(value = "relative_position")
    private String relativePosition;

    /**
     * 亲属公司
     */
    @ApiModelProperty(value = "亲属公司")
    @TableField(value = "relative_company")
    private String relativeCompany;

    /**
     * 最高学历
     */
    @ApiModelProperty(value = "最高学历")
    @TableField(value = "highest_education")
    private String highestEducation;

    /**
     * 所学专业
     */
    @ApiModelProperty(value = "所学专业")
    @TableField(value = "major")
    private String major;

    /**
     * 职称
     */
    @ApiModelProperty(value = "职称")
    @TableField(value = "title")
    private String title;

    /**
     * 专业技术证书
     */
    @ApiModelProperty(value = "专业技术证书")
    @TableField(value = "professional_technical_certificate")
    private String professionalTechnicalCertificate;

    /**
     * 是否需要工号
     */
    @ApiModelProperty(value = "是否需要工号")
    @TableField(value = "needs_employee_number")
    private String needsEmployeeNumber;

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @TableField(value = "supplier_code")
    private String supplierCode;

    /**
     * 所属供应商
     */
    @ApiModelProperty(value = "所属供应商")
    @TableField(value = "affiliated_supplier")
    private String affiliatedSupplier;

    /**
     * 是否从事放射性工作
     */
    @ApiModelProperty(value = "是否从事放射性工作")
    @TableField(value = "works_with_radioactive_materials")
    private String worksWithRadioactiveMaterials;

    /**
     * 工作内容
     */
    @ApiModelProperty(value = "工作内容")
    @TableField(value = "job_content")
    private String jobContent;

    /**
     * 常驻服务地点
     */
    @ApiModelProperty(value = "常驻服务地点")
    @TableField(value = "permanent_service_location")
    private String permanentServiceLocation;

    /**
     * 所属合同名称
     */
    @ApiModelProperty(value = "所属合同名称")
    @TableField(value = "contract_name")
    private String contractName;

    /**
     * 合同级别
     */
    @ApiModelProperty(value = "合同级别")
    @TableField(value = "contract_level")
    private String contractLevel;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_number")
    private String contractNumber;

    /**
     * 是否项目制人员
     */
    @ApiModelProperty(value = "是否项目制人员")
    @TableField(value = "project_based_staff")
    private String projectBasedStaff;

    /**
     * 入场时间
     */
    @ApiModelProperty(value = "入场时间")
    @TableField(value = "entry_time")
    private Date entryTime;

    /**
     * 联系方式
     */
    @ApiModelProperty(value = "联系方式")
    @TableField(value = "contact_information")
    private String contactInformation;

    /**
     * 是否已完成体检
     */
    @ApiModelProperty(value = "是否已完成体检")
    @TableField(value = "completed_physical_examination")
    private String completedPhysicalExamination;

    /**
     * 分管项目经理
     */
    @ApiModelProperty(value = "分管项目经理")
    @TableField(value = "department_head_project_manager")
    private String departmentHeadProjectManager;

    /**
     * 办卡或授权选择
     */
    @ApiModelProperty(value = "办卡或授权选择")
    @TableField(value = "card_or_authorization_choice")
    private String cardOrAuthorizationChoice;

    /**
     * 项目接口人
     */
    @ApiModelProperty(value = "项目接口人")
    @TableField(value = "project_interface_person")
    private String projectInterfacePerson;

    /**
     * 是否技术配置
     */
    @ApiModelProperty(value = "是否技术配置")
    @TableField(value = "technical_configuration")
    private String technicalConfiguration;

    /**
     * 入场备注
     */
    @ApiModelProperty(value = "入场备注")
    @TableField(value = "entry_remarks")
    private String entryRemarks;

    /**
     * 离场时间
     */
    @ApiModelProperty(value = "离场时间")
    @TableField(value = "departure_time")
    private Date departureTime;

    /**
     * 预计离岗时间
     */
    @ApiModelProperty(value = "预计离岗时间")
    @TableField(value = "expected_departure_date")
    private Date expectedDepartureDate;

    /**
     * 是否已取消授权
     */
    @ApiModelProperty(value = "是否已取消授权")
    @TableField(value = "authorization_cancelled")
    private String authorizationCancelled;

    /**
     * 是否违反相关安全规范
     */
    @ApiModelProperty(value = "是否违反相关安全规范")
    @TableField(value = "violated_safety_regulations")
    private String violatedSafetyRegulations;

    /**
     * 是否完成离职体检
     */
    @ApiModelProperty(value = "是否完成离职体检")
    @TableField(value = "completed_departure_physical")
    private String completedDeparturePhysical;

    /**
     * 离职备注
     */
    @ApiModelProperty(value = "离职备注")
    @TableField(value = "departure")
    private String departure;

    /**
     * 锁定状态
     */
    @ApiModelProperty(value = "锁定状态")
    @TableField(value = "locked_status")
    private String lockedStatus;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @TableField(value = "main_table_id")
    private String mainTableId;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    @TableField(value = "operation_time")
    private Date operationTime;

    /**
     * 操作人id
     */
    @ApiModelProperty(value = "操作人id")
    @TableField(value = "operation_id")
    private String operationId;

    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人姓名")
    @TableField(value = "operation_name")
    private String operationName;

}
