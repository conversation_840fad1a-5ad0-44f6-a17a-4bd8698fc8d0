package com.chinasie.orion.domain.dto;

import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * WorkHourFillManageDayDTO Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-15 10:14:51
 */
@ApiModel(value = "WorkHourFillManageDayDTO对象", description = "工时填报每天工时(项目经理)")
@Data
public class WorkHourFillManageDayDTO extends ObjectDTO implements Serializable{

    /**
     * 工时
     */
    @ApiModelProperty(value = "工时")
    @Max(value = 24, message = "工时时长不能超过24小时")
    private Integer workHour;

    /**
     * 工时日期
     */
    @ApiModelProperty(value = "工时日期")
    @NotBlank(message = "工时填报日期不能为空")
    private String workDate;

}
