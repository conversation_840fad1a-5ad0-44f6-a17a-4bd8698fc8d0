package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


@Data
@ExcelIgnoreUnannotated
public class MileStoneImportCheckDTO  implements Serializable {

    /**
     * 里程碑名称
     */
    @ApiModelProperty(value = "里程碑名称")
    @ExcelProperty(value = "里程碑标题*")
    private String milestoneName;


    @ApiModelProperty(value = "里程碑编号")
    @ExcelProperty(value = "里程碑编号")
    private String milestoneNumber;


    @ApiModelProperty(value = "上级里程碑编号")
    @ExcelProperty(value = "上级里程碑编号")
    private String parentMilestoneNumber;


    /**
     * 税率
     */
    @ApiModelProperty(value = "税率")
    @ExcelProperty(value = "税率（%）")
    private String taxRate;


    /**
     * 含税金额
     */
    @ApiModelProperty(value = "金额")
    @ExcelProperty(value = "金额*")
    private BigDecimal amtTax;


    /**
     * 金额类型
     */
    @ApiModelProperty(value = "金额类型")
    @ExcelProperty(value = "金额类型*")
    private String ammountType;


    /**
     * 初始预估验收日期
     */
    @ApiModelProperty(value = "初始预估验收日期")
    @ExcelProperty(value = "初始预估验收日期*")
    @DateTimeFormat("yyyy/MM/dd")
    private String expectAcceptDate;


    /**
     * 日期类型
     */
    @ApiModelProperty(value = "日期类型")
    @ExcelProperty(value = "日期类型*")
    private String dateType;


    /**
     * 收入确认类型
     */
    @ApiModelProperty(value = "收入类型")
    @ExcelProperty(value = "收入类型*")
    private String incomeType;


    @ApiModelProperty(value = "商务接口人")
    @NotEmpty(message = "商务接口人不能为空")
    @ExcelProperty(value = "商务接口人")
    private String busRspUser;

    @ApiModelProperty(value = "商务接口人工号")
    @ExcelProperty(value = "商务接口人工号*")
    private String busRspUserNo;



    /**
     * 技术负责人
     */
    @ApiModelProperty(value = "技术接口人")
    @ExcelProperty(value = "技术接口人")
    private String techRspUser;

    @ApiModelProperty(value = "技术负责人工号")
    @ExcelProperty(value = "技术接口人工号*")
    private String techRspUserNo;


    /**
     * 成本业务分类
     */
    @ApiModelProperty(value = "业务类型")
    @NotEmpty(message = "业务类型不能为空")
    @ExcelProperty(value = "业务类型*")
    private String costBusType;




    @ApiModelProperty(value = "客户")
    @ExcelProperty(value = "客户名称")
    private String cusPersonName;



    @ApiModelProperty(value = "客户编码")
    @ExcelProperty(value = "客户编码*")
    private String cusPersonNumber;

    /**
     * 收入确认类型
     */
    @ApiModelProperty(value = "业务收入类型")
    @ExcelProperty(value = "业务收入类型*")
    private String bussIncomeType;


    /**
     * 确认收入金额-暂估收入
     */
    @ApiModelProperty(value = "暂估收入金额")
    @ExcelProperty(value = "暂估收入金额")
    private BigDecimal confirmIncomeProvisionalEstimate;

}
