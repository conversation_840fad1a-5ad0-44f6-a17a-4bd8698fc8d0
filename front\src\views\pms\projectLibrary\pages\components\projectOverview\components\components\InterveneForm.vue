<script setup lang="ts">
import {
  BasicForm, FormSchema, useForm,
} from 'lyra-component-vue3';
import {
  message,
} from 'ant-design-vue';
import {
  computed, ref, onMounted, Ref,
} from 'vue';
import dayjs from 'dayjs';
import Api from '/@/api';

const props = defineProps<{
  record: {
    id: string,
    projectId: string,
    type: string,
  }
}>();

const dataObj = ref<any>(props.record);
const nodeTypeOptions = [
  {
    label: '里程碑节点',
    name: '里程碑节点',
    value: 'milestone',
  },
  {
    label: '计划',
    value: 'plan',
    name: '计划',
  },
  {
    label: '任务模块',
    name: '任务模块',
    value: 'taskModule',
  },
];

const schemas: FormSchema[] = [
  {
    field: 'name',
    component: 'Input',
    label: '计划名称',
    required: true,
    colProps: {
      span: 24,
    },
  },
  {
    field: 'rspUser',
    component: 'Select',
    label: '责任人',
    colProps: {
      span: 12,
    },
    rules: [{ required: true }],
    componentProps: {
      showSearch: true,
      onSelect(value, option) {
        dataObj.value = {
          ...dataObj.value,
          rspSectionId: option.sectionId || '',
          rspSubDept: option.deptId || '',
        };
      },
      options: computed(() => projectUserList.value),
      filterOption: (input: string, option: any) => option.label.toLowerCase().indexOf(input.toLowerCase()) !== -1,
    },
  },
  {
    field: 'participantUserList',
    component: 'Select',
    label: '参与人',
    colProps: {
      span: 12,
    },
    rules: [
      {
        required: false,
        type: 'array',
      },
    ],
    componentProps: {
      showSearch: true,
      mode: 'multiple',
      options: computed(() => projectUserList.value),
      filterOption: (input: string, option: any) => option.label.toLowerCase().indexOf(input.toLowerCase()) !== -1,
    },
  },
  {
    field: 'nodeType',
    component: 'Select',
    label: '计划类型',
    colProps: {
      span: 12,
    },
    required: true,
    componentProps: {
      placeholder: '请选择',
      options: computed(() => nodeTypeOptions),
    },
  },
  {
    field: 'durationDays',
    component: 'Input',
    label: '工时',
    required: true,
    colProps: {
      span: 12,
    },
  },
  {
    field: 'beginTime',
    component: 'DatePicker',
    label: '计划开始日期',
    required: true,
    colProps: {
      span: 12,
    },
  },
  {
    field: 'endTime',
    component: 'DatePicker',
    label: '计划结束日期',
    required: true,
    colProps: {
      span: 12,
    },
  },
  {
    field: 'remark',
    component: 'InputTextArea',
    label: '计划描述',
    colProps: {
      span: 24,
    },
    componentProps: {
      rows: 6,
      maxlength: 1000,
      showCount: true,
    },
  },
];

const [register, { validate, setFieldsValue, getFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  const pid = props?.record?.id;
  const projectId = props?.record?.projectId;
  if (pid) {
    reqProjectUserList(projectId);
    getDetails(pid);
  }
});

function getIndicator(record) {

}

// 获取项目成员列表
const projectUserList: Ref = ref([]);

async function reqProjectUserList(projectId) {
  const result = await new Api(`/pms/project-role-user/${projectId}/user/list`).fetch(
    '',
    '',
    'GET',
  );
  projectUserList.value = result.map((item) => ({
    ...item,
    label: item.name,
    value: item.id,
  }));
}

async function getDetails(id) {
  const result = await new Api(`/pms/projectScheme/${id}`).fetch(
    '',
    '',
    'GET',
  );
  dataObj.value = {
    ...dataObj.value,
    rspSubDept: result.rspSubDept || '',
  };
  await setFieldsValue({
    ...result,
  });
}

defineExpose({
  async submit() {
    const formValues = await getFieldsValue();
    delete dataObj.value?.type;
    const params = {
      ...dataObj.value,
      ...formValues,
      beginTime: dayjs(formValues.beginTime).format('YYYY-MM-DD'),
      endTime: dayjs(formValues.endTime).format('YYYY-MM-DD'),
    };
    const editRes = await new Api('/pms/projectScheme/edit').fetch(params, '', 'put');
    if (editRes) {
      message.success('编辑成功');
    } else {
      message.error('编辑失败');
    }
  },
});

</script>

<template>
  <BasicForm @register="register" />
</template>

<style scoped lang="less">
</style>