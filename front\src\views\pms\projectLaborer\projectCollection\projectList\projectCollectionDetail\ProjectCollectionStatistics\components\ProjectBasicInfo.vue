<script setup lang="ts">
import { Icon } from 'lyra-component-vue3';
import { Progress } from 'ant-design-vue';
import {
  inject, onMounted, Ref, ref,
} from 'vue';
import Api from '/@/api';

const projectId: string = inject('projectId');
const basicInfo: Ref<{
  [propName: string]: any
}> = ref({});

onMounted(() => {
  getBasicInfo();
});

async function getBasicInfo() {
  const result = await new Api('/pms/projectCollectionStatistics/planCount').fetch({
    projectId,
  }, '', 'GET');
  basicInfo.value = result || {};
}

const formData = inject(
  'formData',
);

</script>

<template>
  <div class="project-basic-info">
    <div class="circle-flex-item">
      <div class="circle primary">
        <Icon icon="orion-icon-user" />
      </div>
      <div>
        <div class="value">
          {{ formData.resPersonName || '--' }}
        </div>
        <span>项目负责人</span>
      </div>
    </div>

    <div
      class="circle-flex-item"
      title="进行中计划"
    >
      <div class="circle info">
        <Icon icon="orion-icon-filesync" />
      </div>
      <div>
        <div class="value">
          {{ basicInfo.underwayCount || 0 }}
        </div>
        <span>进行中计划</span>
      </div>
    </div>
    <div
      class="circle-flex-item"
      title="未开始计划"
    >
      <div class="circle warning">
        <Icon icon="orion-icon-file-exception" />
      </div>
      <div>
        <div class="value">
          {{ basicInfo.noStartCount || 0 }}
        </div>
        <span>未开始计划</span>
      </div>
    </div>
    <div
      class="circle-flex-item"
      title="已完成计划"
    >
      <div class="circle success">
        <Icon icon="orion-icon-filedone" />
      </div>
      <div>
        <div class="value">
          {{ basicInfo.completeCount || 0 }}
        </div>
        <span>已完成计划</span>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.project-basic-info {
  display: grid;
  grid-template-columns: minmax(135px, 3fr) minmax(125px, 2fr) minmax(270px, 3fr) minmax(125px, 2fr) minmax(0, 4fr);
  gap: 0 10px;
}

.right-slider::before {
  content: '';
  width: 1px;
  height: 45px;
  background-color: #f1f1f1;
}

.flex-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;

  > div {
    flex: 1;
  }
}
</style>
