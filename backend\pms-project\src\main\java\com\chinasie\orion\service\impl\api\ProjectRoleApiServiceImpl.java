package com.chinasie.orion.service.impl.api;

import com.chinasie.orion.domain.entity.ProjectRole;
import com.chinasie.orion.domain.vo.ProjectRoleVO;
import com.chinasie.orion.domain.vo.statics.BatchProjectUserVO;
import com.chinasie.orion.service.ProjectRoleApiService;
import com.chinasie.orion.service.ProjectRoleService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: lsy
 * @date: 2024/6/4
 * @description:
 */
@RestController
public class ProjectRoleApiServiceImpl implements ProjectRoleApiService {

    @Autowired
    private ProjectRoleService projectRoleService;

    public List<ProjectRoleVO> getProjectRoleVO(List<String> roleIdList) throws Exception {
        List<ProjectRole> projectRoleList = projectRoleService.listByIds(roleIdList);
        return BeanCopyUtils.convertListTo(projectRoleList, ProjectRoleVO::new);
    }


    @Override
    public List<BatchProjectUserVO> getProjectUserBatch(List<String> projectIds) throws Exception {
      return   projectRoleService.getProjectUserBatch(projectIds);
    }
}
