package com.chinasie.orion.domain.dto;


import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.domain.entity.MajorRepairPlanEconomize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/08/01/14:32
 * @description:
 */
@Data
public class MajorRepairPlanBoardDTO {

    /**
     * 大修id
     */
    @ApiModelProperty(value = "大修轮次（全局唯一）")
    private String repairRoundId;


    /**
     * 大修轮次（全局唯一）
     */
    @ApiModelProperty(value = "大修轮次（全局唯一）")
    private String repairRound;


    /**
     * 大修类别
     */
    @ApiModelProperty(value = "大修类别")
    private String type;

    @ApiModelProperty(value = "大修类别名称")
    private String typeName;

    @ApiModelProperty(value = "大修基地名称")
    private String BaseName;


    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private Date beginTime;


    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private Date endTime;




    /**
     * 工期（天数）
     */
    @ApiModelProperty(value = "工期（天数）")
    private Integer workDuration;

    /**
     * 节约集体剂量
     */
    @ApiModelProperty(value = "节约集体剂量")
    @TableField(value = "conserve_meter")
    private BigDecimal conserveMeter;

    /**
     * 节约(H)
     */
    @ApiModelProperty(value = "节约(H)")
    @TableField(value = "economize_duration")
    private BigDecimal economizeDuration;

    /**
     * 大修统计
     */
    @ApiModelProperty(value = "大修统计")
    private HashMap<String,Integer> majorPlanMap;


    /**
     * 大修统计
     */
    @ApiModelProperty(value = "六个零")
    private String allZero;


    /**
     * 大修状态
     */
    @ApiModelProperty(value = "大修状态")
    private Integer majorStatus;


    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    private Date beginPlanTime;


    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    private Date endPlanTime;


    /**
     * 实际开始时间
     */
    @ApiModelProperty(value = "实际开始时间")
    private Date actualBeginTime;


    /**
     * 实际结束时间
     */
    @ApiModelProperty(value = "实际结束时间")
    private Date actualEndTime;


    /**
     * 大修经理
     */
    @ApiModelProperty(value = "大修经理")
    private String majorRepairManagerName;

}
