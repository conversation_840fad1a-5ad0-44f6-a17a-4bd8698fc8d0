package com.chinasie.orion.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.lock.LockInfo;
import com.baomidou.lock.LockTemplate;
import com.baomidou.lock.executor.RedisTemplateLockExecutor;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.bo.CommonRoleBo;
import com.chinasie.orion.constant.MajorRepairOrgEnum;
import com.chinasie.orion.constant.MaterialDownEnum;
import com.chinasie.orion.constant.MaterialTypeEnum;
import com.chinasie.orion.domain.dto.relationOrgToMaterial.*;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.NodeVO;
import com.chinasie.orion.domain.vo.ObjectTreeInfoVO;
import com.chinasie.orion.domain.vo.TreeNodeVO;
import com.chinasie.orion.domain.vo.material.MaterialInVO;
import com.chinasie.orion.domain.vo.material.MaterialOutVO;
import com.chinasie.orion.domain.vo.relationOrgToMaterial.MaterialDownVO;
import com.chinasie.orion.domain.vo.relationOrgToMaterial.MaterialManageImplementTreeVO;
import com.chinasie.orion.domain.vo.relationOrgToMaterial.MaterialManagePlanTreeVO;
import com.chinasie.orion.domain.vo.tree.SearchVO;
import com.chinasie.orion.enums.StatusEnum;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.mybatis.domain.entity.LyraEntity;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.MaterialManageMapper;
import com.chinasie.orion.repository.RelationOrgToMaterialMapper;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.service.MajorRepairOrgService;
import com.chinasie.orion.service.MaterialManageService;
import com.chinasie.orion.service.MaterialOutManageService;
import com.chinasie.orion.service.RelationOrgToMaterialService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.TooUtils;
import com.chinasie.orion.util.TreeInfoProcessor;
import com.mzt.logapi.context.LogRecordContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.chinasie.orion.constant.DictConts.PMS_OUT_REASON;

/**
 * <p>
 * RelationOrgToMaterial 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18 09:41:25
 */
@Service
@Slf4j
public class RelationOrgToMaterialServiceImpl extends OrionBaseServiceImpl<RelationOrgToMaterialMapper, RelationOrgToMaterial> implements RelationOrgToMaterialService {

    @Autowired
    private MaterialManageService materialManageService;

    @Autowired
    private MaterialManageMapper materialManageMapper;

    @Autowired
    private ClassRedisHelper classRedisHelper;

    @Autowired
    private RelationOrgToMaterialMapper relationOrgToMaterialMapper;

    @Autowired
    private MaterialOutManageService materialOutManageService;

    @Autowired
    @Lazy
    private MajorRepairOrgService majorRepairOrgService;

    @Autowired
    private LockTemplate lockTemplate;

    @Autowired
    private CommonRoleBo commonRoleBo;

    private String LOCKED_KEY_BATCH = "pmsx::Relation-org-to-material::locked::batch";
    @Autowired
    private DictRedisHelper dictRedisHelper;
    /**
     * 组织物资新增
     *
     * @param dto
     * @return
     */
    @Override
    public void addRelation(OrgMaterialRelationDTO dto) {
        final LockInfo lockInfo = lockTemplate.lock(LOCKED_KEY_BATCH, 3000L, 5000L, RedisTemplateLockExecutor.class);
        if (ObjectUtil.isNull(lockInfo)) {
            throw new RuntimeException("业务处理中,请稍后再试");
        }
        StringBuffer data = new StringBuffer();
        try {
            //物资编码
            List<MaterialManage> mmList = dto.getMaterialManageList();
            List<String> collectNumber = mmList.stream().map(MaterialManage::getNumber).distinct().collect(Collectors.toList());

            //日志记录
            mmList.forEach(x -> data.append(x.getNumber()).append("-").append(x.getAssetName()));
            //基地编码
            String baseCode = dto.getBaseCode();
            String repairOrgId = dto.getRepairOrgId();

            Map<String, MaterialManage> materialManageMap = new HashMap<>();
            //判断表是否存在数据
            List<MaterialManage> materialManageList = getMaterialManageList(baseCode, collectNumber);
            for (MaterialManage materialManage : materialManageList) {
                materialManageMap.put(materialManage.getNumber(), materialManage);
            }
            //根据大修组织id查询关系表的物资库id
            LambdaQueryWrapperX<RelationOrgToMaterial> sql = new LambdaQueryWrapperX<>(RelationOrgToMaterial.class);
            sql.select(RelationOrgToMaterial::getMaterialId);
            sql.eq(RelationOrgToMaterial::getRepairOrgId, repairOrgId);
            List<RelationOrgToMaterial> oldList = relationOrgToMaterialMapper.selectList(sql);
            Set<String> materialIdList = oldList.stream().map(RelationOrgToMaterial::getMaterialId).collect(Collectors.toSet());

            //关系数据入库
            List<RelationOrgToMaterial> relationOrgToMaterials = new ArrayList<>();

            List<MaterialManage> insertBatch = new ArrayList<>();
            List<MaterialManage> updateBatch = new ArrayList<>();
            //数据处理
            for (MaterialManage materialManage : mmList) {
                MaterialManage materialManageTemp = materialManageMap.get(materialManage.getNumber());
                //存在就编辑
                if (ObjectUtil.isNotEmpty(materialManageTemp)) {
                    materialManage.setId(materialManageTemp.getId());
                    materialManage.setStatus(StatusEnum.DISABLE.getIndex());
                    materialManage.setBaseCode(dto.getBaseCode());
                    updateBatch.add(materialManage);
                } else {
                    materialManage.setId(classRedisHelper.getUUID(MaterialManage.class.getSimpleName()));
                    materialManage.setIsReport(Boolean.FALSE);
                    materialManage.setStatus(StatusEnum.DISABLE.getIndex());
                    materialManage.setBaseCode(dto.getBaseCode());
                    //物资类型 当前默认固定资产，数据写死，如果后续提供非固定资产数据来源后移除，
                    materialManage.setAssetType("固定资产");
                    insertBatch.add(materialManage);
                }
                //如果新增数据的物资库id存在关系，则不添加相关关系
                String id = materialManage.getId();
                if (!materialIdList.contains(id)) {
                    RelationOrgToMaterial relationOrgToMaterial = new RelationOrgToMaterial();
                    relationOrgToMaterial.setMaterialId(id);
                    relationOrgToMaterial.setRepairOrgId(repairOrgId);
                    relationOrgToMaterial.setMaterialNumber(materialManage.getNumber());
                    relationOrgToMaterials.add(relationOrgToMaterial);
                }
            }

            //物资表入库
            //没有就新增
            if (ObjectUtil.isNotEmpty(insertBatch)) {
                materialManageMapper.insertBatch(insertBatch);
            }
            //存在就编辑
            if (ObjectUtil.isNotEmpty(updateBatch)) {
                materialManageMapper.updateBatch(updateBatch, 10);
            }
            //关联表入库
            relationOrgToMaterialMapper.insertBatch(relationOrgToMaterials);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            LogRecordContext.putVariable("place", dto.getRepairRound() + "大修轮次下的" + dto.getName() + "班组中");
            LogRecordContext.putVariable("data", data);
            lockTemplate.releaseLock(lockInfo);
        }
    }

    public List<MaterialManage> getMaterialManageList(String baseCode, List<String> collectNumber) {
        if (ObjectUtil.isEmpty(collectNumber) || StrUtil.isEmpty(baseCode)) {
            return null;
        }
        LambdaQueryWrapperX<MaterialManage> condition = new LambdaQueryWrapperX<>(MaterialManage.class);
        condition.in(MaterialManage::getNumber, collectNumber);
        condition.eq(MaterialManage::getBaseCode, baseCode);
        return materialManageMapper.selectList(condition);
    }

    public long getInDate(Date inDate) {
        if (!Objects.isNull(inDate)) {
            Calendar startCalendar = Calendar.getInstance();
            Calendar endCalendar = Calendar.getInstance();

            startCalendar.setTime(DateUtil.beginOfDay(inDate));
            endCalendar.setTime(DateUtil.beginOfDay(new Date()));
            if (startCalendar.getTimeInMillis() > endCalendar.getTimeInMillis()) {
                return ((startCalendar.getTimeInMillis() - endCalendar.getTimeInMillis()) / (24 * 60 * 60 * 1000));

            } else {
                return (startCalendar.getTimeInMillis() - endCalendar.getTimeInMillis()) / (24 * 60 * 60 * 1000);
            }
        }
        return 0;
    }

    /**
     * 编辑物资进场信息
     *
     * @param dto
     * @return
     */
    @Override
    public MaterialInVO editMaterialInfo(MaterialInDTO dto) {
        String id = dto.getId();
        MaterialInVO rsp = new MaterialInVO();
        MaterialManage materialManage = materialManageService.getById(id);
        if (Objects.isNull(materialManage)) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "数据不存在，请刷新后重试");
        }
        Integer status = materialManage.getStatus();
        // 如果数据状态为已入场，对于入场数据是不能重复编辑的
//        if (Objects.equals(status, StatusEnum.ENABLE.getIndex())) {
//            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "已入场的数据是不能再次编辑的");
//        }

        // 判断是否有填写实际入场时间
        Date actInDate = dto.getActInDate();
        if (Objects.nonNull(actInDate)) {
            if (actInDate.after(new Date())) {
                throw new BaseException(HttpStatus.BAD_REQUEST.value(), "实际入场时间不能大于当今时间");
            }
            materialManage.setActInDate(actInDate);
            //填写了实际入场时间，那么变更状态为已入场，生成当前时间戳的台账
            MaterialOutManage materialOutManage = this.packageMaterialOutManage(materialManage,
                    getMaterialOutManageId(materialManage.getId(), StatusEnum.ENABLE.getIndex(), 0));
            materialManage.setStatus(StatusEnum.ENABLE.getIndex());
            materialOutManage.setStatus(StatusEnum.ENABLE.getIndex());
            //如果物资重新进场，以下字段数据重置
            materialOutManage.setIsReport(materialManage.getIsReport());
            materialOutManage.setActOutDate(null);
            materialOutManage.setIsAgainIn(false);
            materialOutManage.setMaterialDestination(null);
            materialOutManage.setOutNum(null);
            materialOutManage.setOutReason(null);
            materialOutManageService.save(materialOutManage);
        }

        //进场信息变更
        materialManage.setInDate(dto.getInDate());
        materialManage.setOutDate(dto.getOutDate());
        materialManage.setInputStockNum(dto.getInputStockNum());
        materialManage.setIsReport(dto.getIsReport());
        //如果物资重新进场，以下字段数据重置
        materialManage.setActOutDate(null);
        materialManage.setIsAgainIn(false);
        // 设置距离入场天数
        if (Objects.nonNull(dto.getInDate())) {
            materialManage.setInDays(TooUtils.getInDate(dto.getInDate()));
        }
        materialManageService.updateById(materialManage);

        editLog(materialManage, dto.getRepairRound(), dto.getName());

        rsp.setRspUserId(materialManage.getRspUserId());
        rsp.setRspUserName(materialManage.getRspUserName());
        rsp.setAssetName(materialManage.getAssetName());
        rsp.setAssetCode(materialManage.getAssetCode());
        rsp.setAssetType(materialManage.getAssetType());
        rsp.setNumber(materialManage.getNumber());
        rsp.setInDate(materialManage.getInDate());
        rsp.setOutDate(materialManage.getOutDate());
        rsp.setActInDate(materialManage.getActInDate());
        rsp.setIsReport(materialManage.getIsReport());
        rsp.setSpecificationModel(materialManage.getSpecificationModel());
        rsp.setIsMetering(materialManage.getIsMetering());
        rsp.setIsVerification(materialManage.getIsVerification());
        rsp.setNextVerificationDate(materialManage.getNextVerificationDate());
        rsp.setMaterialManageId(materialManage.getId());
        rsp.setStoragePlaceName(materialManage.getStoragePlaceName());
        rsp.setInputStockNum(materialManage.getInputStockNum());
        return rsp;
    }

    /**
     * 编辑物资离场信息
     *
     * @param dto
     * @return
     */
    @Override
    public MaterialOutVO editOutMaterialInfo(MaterialOutDTO dto) {
        String id = dto.getId();
        MaterialOutVO rsp = new MaterialOutVO();
        MaterialManage materialManage = materialManageService.getById(id);
        if (Objects.isNull(materialManage)) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "数据不存在，请刷新后重试");
        }
        Integer status = materialManage.getStatus();
        if (!Objects.equals(status, StatusEnum.ENABLE.getIndex())) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "只有状态为已入库的数据才能编辑数据");
        }
        // 通过当前ID 构成 台账唯一ID
        MaterialOutManage outManage = new MaterialOutManage();
        boolean exist = false;

        // 判断是否有填写实际离场时间
        Date actOutDate = dto.getActOutDate();
        if (Objects.nonNull(actOutDate)) {
            if (actOutDate.after(new Date())) {
                throw new BaseException(HttpStatus.BAD_REQUEST.value(), "实际离场时间不能大于当今时间");
            }

            outManage = this.packageMaterialOutManage(materialManage,
                    getMaterialOutManageId(materialManage.getId(), StatusEnum.DRAFT.getIndex(), 0));
            outManage.setType(MaterialTypeEnum.OUT.getKey());
            outManage.setStatus(StatusEnum.DRAFT.getIndex());
            outManage.setActOutDate(dto.getActOutDate());
            materialManage.setStatus(StatusEnum.DRAFT.getIndex());
            materialManage.setActOutDate(actOutDate);
            exist = true;
        } else {
            //根据生成的id查询是否存在台账
            String momId = getMaterialOutManageId(materialManage.getId(), StatusEnum.DRAFT.getIndex(), 1);
            MaterialOutManage mom = materialOutManageService.getById(momId);
            if (Objects.nonNull(mom)) {
                //台账数据存在，修改台账数据
                BeanUtil.copyProperties(mom, outManage);
                outManage.setModifyTime(new Date());
            } else {
                //台账数据不存在，根据物资管理数据生成台账数据
                outManage = this.packageMaterialOutManage(materialManage, momId);
                exist = true;
            }
            outManage.setType(MaterialTypeEnum.EDIT_INFO.getKey());
            outManage.setStatus(StatusEnum.ENABLE.getIndex());
            materialManage.setStatus(StatusEnum.ENABLE.getIndex());
        }
        materialManage.setIsAgainIn(dto.getIsAgainIn());
        materialManageService.updateById(materialManage);
//        outManage.setOutNum(dto.getOutNum());
        // 同进同出
        outManage.setOutNum(materialManage.getInputStockNum());
        outManage.setMaterialDestination(dto.getMaterialDestination());
        outManage.setOutReason(dto.getOutReason());
        outManage.setIsAgainIn(dto.getIsAgainIn());
        if (exist) {
            materialOutManageService.save(outManage);
        } else {
            materialOutManageService.updateById(outManage);
        }
        editLog(materialManage, dto.getRepairRound(), dto.getName());
        rsp.setRspUserId(materialManage.getId());
        rsp.setRspUserName(materialManage.getRspUserName());
        rsp.setAssetName(materialManage.getAssetName());
        rsp.setAssetCode(materialManage.getAssetCode());
        rsp.setActOutDate(materialManage.getActOutDate());
        rsp.setOutNum(outManage.getOutNum());
        rsp.setOutReason(outManage.getOutReason());
        rsp.setOutDate(materialManage.getOutDate());

        Map<String, DictValueVO> dictMapByCode = dictRedisHelper.getDictMapByCode(PMS_OUT_REASON);
        rsp.setOutReasonName(dictMapByCode.getOrDefault(outManage.getOutReason(),new DictValueVO()).getDescription());
        rsp.setMaterialDestination(outManage.getMaterialDestination());
        rsp.setIsAgainIn(outManage.getIsAgainIn());
        rsp.setMaterialManageId(materialManage.getId());
        rsp.setNumber(materialManage.getNumber());
        return rsp;
    }

    /**
     * 物资大修编辑日志记录
     *
     * @param mm
     * @param repairRound
     * @param name
     */
    private void editLog(MaterialManage mm, String repairRound, String name) {
        //日志记录
        StringBuffer data = new StringBuffer();
        data.append(mm.getNumber()).append("-").append(mm.getAssetName());
        LogRecordContext.putVariable("place", repairRound + "大修轮次下的" + name + "班组中");
        LogRecordContext.putVariable("data", data);
    }

    /**
     * 根据物资管理数据生成台账数据
     *
     * @param materialManage
     * @return
     */
    private MaterialOutManage packageMaterialOutManage(MaterialManage materialManage, String id) {
        MaterialOutManage materialOutManage = new MaterialOutManage();
        BeanCopyUtils.copyProperties(materialManage, materialOutManage);
        materialOutManage.setRspUserNo(materialManage.getRspUserNo());
        materialOutManage.setRspUserName(materialManage.getRspUserName());
        materialOutManage.setDemandNum(materialManage.getDemandNum());
        materialOutManage.setInDays(materialManage.getInDays());
        materialOutManage.setIsReport(materialManage.getIsReport());
        materialOutManage.setStockNum(materialManage.getInputStockNum());
        materialOutManage.setIsOverdue(materialManage.getIsOverdue());
        materialOutManage.setIsMetering(materialManage.getIsMetering());
        materialOutManage.setBaseCode(materialManage.getBaseCode());
        Date date = materialManage.getNextVerificationDate();
        if (Objects.nonNull(date)) {
            materialOutManage.setIsOverdue(date.compareTo(new Date()) > 0);
        }
        materialOutManage.setIsPass(null == materialManage.getIsOverdue() ? null : !materialManage.getIsOverdue());
        materialOutManage.setCreatorId(null);
        materialOutManage.setCreateTime(null);
        materialOutManage.setModifyId(null);
        materialOutManage.setModifyTime(null);
        materialOutManage.setOwnerId(null);
        materialOutManage.setId(null);
        materialOutManage.setClassName("MaterialOutManage");
        // 插入物资入场记录
        materialOutManage.setSourceId(materialManage.getId());
        materialOutManage.setType(MaterialTypeEnum.INPUT.getKey());
        // 设置生成的唯一ID 是由来源数据物资表的ID拼接 日期构成
        materialOutManage.setId(id);
        return materialOutManage;
    }

    /**
     * 获取台账id
     *
     * @param id   物资管理id
     * @param type 操作类型：1-入场操作，2-离场操作
     * @param mode 模式：0-当前时间戳，1-当前日期时间戳
     * @return
     */
    private String getMaterialOutManageId(String id, Integer type, int mode) {
        if (mode == 0) {
            //物资管理id+操作方式+当前时间戳
            return id + type + System.currentTimeMillis();
        } else {
            //物资管理id+操作方式+当前日期时间戳
            return id + type + DateUtil.parse(DateUtil.today()).getTime();
        }
    }

    /**
     * @param
     * @return 删除组织物资关系
     */
    @Override
    public Boolean deleteRelation(List<OrgMaterialRelationDeleteDTO> list) {
        //当前删除组织物资关系为单条删除
        //删除物资关联关系后判断是否需要删除该物资库对应的物资
        if (CollUtil.isNotEmpty(list)) {
            StringBuffer data = new StringBuffer();
            //根据关系id查关系表数据
            Set<String> relationIdList = list.stream().map(OrgMaterialRelationDeleteDTO::getRelationId)
                    .filter(Objects::nonNull).collect(Collectors.toSet());
            if (CollUtil.isNotEmpty(relationIdList)) {
                List<RelationOrgToMaterial> relationList = this.getBaseMapper().selectBatchIds(relationIdList);
                //删除关联关系(先查再删除)
                this.removeBatchByIds(relationIdList);

                //查询该物资是否存在对应的组织关系-物资库id与基地关系为一对一，不存在对应的组织关系则代表物资库不存在该物资
                //获取当前删除关系的所有物资库id
                Set<String> materialIdList = relationList.stream().map(RelationOrgToMaterial::getMaterialId)
                        .filter(Objects::nonNull).collect(Collectors.toSet());
                if (CollUtil.isNotEmpty(materialIdList)) {
                    Set<String> dataIdList = new HashSet<>();
                    //根据物资库id查询关联关系表,查询存在的物资库id数据
                    LambdaQueryWrapperX<RelationOrgToMaterial> sql = new LambdaQueryWrapperX<>(RelationOrgToMaterial.class);
                    sql.select(RelationOrgToMaterial::getId);
                    sql.in(RelationOrgToMaterial::getMaterialId, materialIdList);
                    List<String> materialList = this.listObjs(new LambdaQueryWrapper<RelationOrgToMaterial>()
                                    .select(RelationOrgToMaterial::getMaterialId)
                                    .in(RelationOrgToMaterial::getMaterialId, materialIdList),
                            obj -> (String) obj // 类型转换
                    );
                    if (CollUtil.isNotEmpty(materialList)) {
                        materialIdList.forEach(x -> {
                            //存在的物资库id列表不包含该物资库id,添加到物资库删除数据
                            if (!materialList.contains(x)) {
                                dataIdList.add(x);
                            }
                        });
                    } else {
                        //如果查询数据为空,添加该批次所有物资库id
                        dataIdList.addAll(materialIdList);
                    }
                    //当对应的组织关系为0，物资库删除该物资
                    if (CollUtil.isNotEmpty(dataIdList)) {
                        LambdaQueryWrapperX<MaterialManage> wrapperX = new LambdaQueryWrapperX<>(MaterialManage.class);
                        wrapperX.in(MaterialManage::getId, dataIdList);
                        materialManageMapper.delete(wrapperX);
                    }
                }
                //日志记录
                List<MaterialManage> mmList = materialManageMapper.selectBatchIds(materialIdList);
                mmList.forEach(x -> data.append(x.getNumber()).append("-").append(x.getAssetName()));
            }
            LogRecordContext.putVariable("place", list.get(0) + "大修轮次下的" + list.get(0) + "班组中");
            LogRecordContext.putVariable("data", data);
        }
        return Boolean.TRUE;
    }

    /**
     * 大修准备树
     *
     * @param searchVO
     * @return
     */
    @Override
    public ObjectTreeInfoVO<TreeNodeVO<NodeVO<MaterialManagePlanTreeVO>>> planTree(SearchVO searchVO) throws Exception {
        String repairOrgId = searchVO.getRepairOrgId();
        String repairRound = searchVO.getRepairRound();
        String keyword = searchVO.getKeyword();
        //查修大修组织 所有
        List<MajorRepairOrg> majorRepairOrgListAll= majorRepairOrgService.getList(repairRound, null);
        //大修组织为空
        if (ObjectUtil.isEmpty(majorRepairOrgListAll)) {
            return new ObjectTreeInfoVO<>();
        }
        // 过滤处理
        List<MajorRepairOrg> majorRepairOrgList= majorRepairOrgListAll;
        if(StringUtils.hasText(repairOrgId)){
            majorRepairOrgList= majorRepairOrgListAll.stream().filter(item-> Objects.equals(item.getParentId(),repairOrgId) || item.getChainPath().contains(repairOrgId)).collect(Collectors.toList());
        }
        //收集节点id
        List<String> idList = majorRepairOrgList.stream().map(LyraEntity::getId).collect(Collectors.toList());
        //查询关联表
        LambdaQueryWrapperX<RelationOrgToMaterial> condition = new LambdaQueryWrapperX<>(RelationOrgToMaterial.class);
        condition.in(RelationOrgToMaterial::getRepairOrgId, idList);
        List<RelationOrgToMaterial> relationList = this.list(condition);

        // 节点数据
        List<String> dataIdList = new ArrayList<>();
        List<NodeVO<MaterialManagePlanTreeVO>> nodeList = new ArrayList<>();
        if (CollUtil.isNotEmpty(majorRepairOrgList)) {
            majorRepairOrgList.forEach(x -> {
                NodeVO<MaterialManagePlanTreeVO> vo = new NodeVO<>();
                vo.setChainPath(x.getChainPath());
                vo.setSort(x.getSort());
                vo.setNodeType(x.getLevelType());
                vo.setParentId(x.getParentId());
                vo.setRspUserId(x.getRspUserId());
                vo.setId(x.getId());
                vo.setName(x.getName());
                vo.setCode(x.getCode());
                vo.setRspUserName(x.getRspUserName());
                MaterialManagePlanTreeVO dataVO = new MaterialManagePlanTreeVO();
                vo.setData(dataVO);
                nodeList.add(vo);
                dataIdList.add(x.getId());
            });
        }
        Map<String, List<NodeVO<MaterialManagePlanTreeVO>>> idToDetailMap = new HashMap<>();
        if (CollUtil.isNotEmpty(relationList)) {
            //查询物资信息
            List<String> collectMeterialIds = relationList.stream().map(RelationOrgToMaterial::getMaterialId).distinct().collect(Collectors.toList());
            LambdaQueryWrapperX<MaterialManage> lqw = new LambdaQueryWrapperX<>(MaterialManage.class);
            lqw.selectAll(MaterialManage.class);
            lqw.selectAs(FixedAssets::getId, MaterialManage::getRemark);
            if (CollUtil.isNotEmpty(collectMeterialIds)) {
                lqw.in(MaterialManage::getId, collectMeterialIds);
            }
            //用物资名称搜索
            if (ObjectUtil.isNotEmpty(keyword)) {
                lqw.and(e -> e.like(MaterialManage::getAssetName, keyword).or().like(MaterialManage::getNumber, keyword));
            }
            lqw.leftJoin(FixedAssets.class, FixedAssets::getNumber, MaterialManage::getNumber);
            List<MaterialManage> mmList = materialManageMapper.selectJoinList(MaterialManage.class, lqw);
            //物资分组
            Map<String, MaterialManage> materialManageMap = mmList.stream()
                    .collect(Collectors.toMap(MaterialManage::getId, Function.identity(), (k1, k2) -> k1));

            // 业务数据转换 保持行数据一致 方便统一统计
            if (!materialManageMap.isEmpty()) {
                for (RelationOrgToMaterial x : relationList) {
                    List<NodeVO<MaterialManagePlanTreeVO>> nodeVOList = idToDetailMap.getOrDefault(x.getRepairOrgId(), new ArrayList<>());
                    NodeVO<MaterialManagePlanTreeVO> nodeVO = new NodeVO<>();
                    MaterialManagePlanTreeVO vo = new MaterialManagePlanTreeVO();
                    MaterialManage entity = materialManageMap.get(x.getMaterialId());
                    if (ObjectUtil.isNull(entity)) {
                        continue;
                    }
                    BeanCopyUtils.copyProperties(entity, vo);
                    vo.setMaterialManageId(entity.getId());
                    vo.setFixedAssetsId(entity.getRemark());
                    nodeVO.setId(x.getId());
                    nodeVO.setCode(vo.getNumber());
                    nodeVO.setRspUserName(vo.getRspUserName());
                    nodeVO.setCreateTime(x.getCreateTime());
                    nodeVO.setNodeType(MajorRepairOrgEnum.LEVEL_TYPE_GROUP_BUSINESS_DATA.getCode());
                    this.planSimpleCount(vo);
                    nodeVO.setData(vo);
                    nodeVO.setParentId(x.getRepairOrgId());
                    nodeVOList.add(nodeVO);
                    // 设计单行的统计数
                    idToDetailMap.put(x.getRepairOrgId(), nodeVOList);
                    dataIdList.add(x.getId());
                }
            }
        }

        //业务数据排序
        if (!MapUtils.isEmpty(idToDetailMap)) {
            idToDetailMap = idToDetailMap.entrySet().stream().collect(
                    Collectors.toMap(Map.Entry::getKey, entry -> {
                        List<NodeVO<MaterialManagePlanTreeVO>> nodeVOList = entry.getValue();
                        nodeVOList.sort(Comparator.comparing(NodeVO::getCreateTime));
                        return nodeVOList;
                    })
            );
        }
        //过滤父级节点为空的数据且节点不为顶级节点
        List<NodeVO<MaterialManagePlanTreeVO>> voList = nodeList.stream().filter(x -> StrUtil.equals("0", x.getId()) || StrUtil.isNotBlank(x.getParentId())).collect(Collectors.toList());
        //获取当前人拥有的权限对于数据列表
        Map<String, Set<String>> dataIdToRoleMap = commonRoleBo.currentUserRoles(dataIdList, majorRepairOrgList);
        List<NodeVO<MaterialManagePlanTreeVO>> nodeList1 =  this.levelCount(voList,idToDetailMap);
        log.info("【阶段--传入参数】：{}", JSONObject.toJSONString(nodeList1));
        TreeInfoProcessor<NodeVO<MaterialManagePlanTreeVO>> processor = new TreeInfoProcessor<>(
                nodeList1, NodeVO::getId, NodeVO::getParentId, NodeVO::getRspUserId, CurrentUserHelper.getCurrentUserId(),
                NodeVO::getSort, dataIdToRoleMap, false,false, idToDetailMap
        );
        ObjectTreeInfoVO<TreeNodeVO<NodeVO<MaterialManagePlanTreeVO>>> res = new ObjectTreeInfoVO<>();
        res.setTreeNodeVOList(processor.getRootList());
        res.setParenIdList(processor.getParenIdList());
        res.setOneOrTwoIdList(processor.getOneOrTwoIdList());
        return res;
    }

    private List<NodeVO<MaterialManagePlanTreeVO>> levelCount(List<NodeVO<MaterialManagePlanTreeVO>> nodeList, Map<String, List<NodeVO<MaterialManagePlanTreeVO>>> idToDetailMap) {
        //所有节点 然后进行 去重统计
        log.info("【阶段--节点物资--传入参数】：{}", JSONObject.toJSONString(nodeList));
        Map<String,Set<String>> idToAllChildList = new HashMap<>();
        Map<String,Set<String>> nodeIdToDataIdSet = new HashMap<>();
        List<NodeVO<MaterialManagePlanTreeVO>> nodeList1 = new ArrayList<>();
        Map<String ,NodeVO<MaterialManagePlanTreeVO>> businessDataMap = new HashMap<>();
        Map<String ,NodeVO<MaterialManagePlanTreeVO>> nodeMap = nodeList.stream().collect(Collectors.toMap(NodeVO::getId,Function.identity(),(k1, k2)->k1));
        for (Map.Entry<String, List<NodeVO<MaterialManagePlanTreeVO>>> stringListEntry : idToDetailMap.entrySet()) {
            // 获取节点信息
            NodeVO<MaterialManagePlanTreeVO> nodeVO = nodeMap.get(stringListEntry.getKey());
            // 获取节点的全路径
            String chainPath = nodeVO.getChainPath();
            String[]  parentIdList = chainPath.split(",");
            for (String s : parentIdList) {
                 Set<String> chiledSet = idToAllChildList.getOrDefault(s,new HashSet<>());
                 chiledSet.add(nodeVO.getId());
                 idToAllChildList.put(s,chiledSet);
            }
            nodeIdToDataIdSet.put(nodeVO.getId(),stringListEntry.getValue().stream().map(NodeVO::getId).collect(Collectors.toSet()));
            // 设置业务ID对应的 业务数据
            List<NodeVO<MaterialManagePlanTreeVO>> businessList =stringListEntry.getValue();
            for (NodeVO<MaterialManagePlanTreeVO> business : businessList) {
                businessDataMap.put(business.getId(),business);
            }
        }
        for (Map.Entry<String, Set<String>> stringSetEntry : idToAllChildList.entrySet()) {
            NodeVO<MaterialManagePlanTreeVO> nodeVO = nodeMap.get(stringSetEntry.getKey());
            if (ObjectUtil.isEmpty(nodeVO)){
                continue;
            }
            Set<String> childIdList=  stringSetEntry.getValue();
            Set<String> disDataIdSet = new HashSet<>();
            for (String s : childIdList) {
                Set<String> dataIdSet = nodeIdToDataIdSet.get(s);
                if(dataIdSet != null && !dataIdSet.isEmpty()){
                    disDataIdSet.addAll(dataIdSet);
                }
            }
            if(!disDataIdSet.isEmpty()){
                int number = 0;
                int tooNumber= 0;
                int notPlan = 0;
                int actInCount= 0;
                int planInCount=0;
                int actInCountNo=0;
                for (String s : disDataIdSet) {
                    NodeVO<MaterialManagePlanTreeVO>  business =    businessDataMap.get(s);
                    if(null != business) {
                        number += business.getData().getDeviceNumber();
                        tooNumber = tooNumber + business.getData().getToolNumber();
                        notPlan+= business.getData().getNoPlanIn();
                        actInCount+= business.getData().getActInCount();
                        planInCount+= business.getData().getPlanIn();
                        actInCountNo+= business.getData().getActInCountNo();
                    }
                }
                MaterialManagePlanTreeVO data =  nodeVO.getData();
                data.setDeviceNumber(number);
                data.setToolNumber(tooNumber);
                data.setPlanIn(planInCount);
                data.setNoPlanIn(notPlan);
                data.setActInCount(actInCount);
                data.setActInCountNo(actInCountNo);
                if(actInCount > 0 && number > 0){
                    BigDecimal actualInRate = BigDecimal.valueOf(actInCount).divide(BigDecimal.valueOf(number), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
                    data.setActualInRate(actualInRate.setScale(0, RoundingMode.HALF_UP).doubleValue());
                }else{
                    data.setPlanInRate(BigDecimal.ZERO.doubleValue());
                }
                if( planInCount > 0 && number > 0){
                    BigDecimal planRate = BigDecimal.valueOf(planInCount).divide(BigDecimal.valueOf(number), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
                    data.setPlanInRate(planRate.setScale(0, RoundingMode.HALF_UP).doubleValue());
                }else {
                    data.setPlanInRate(BigDecimal.ZERO.doubleValue());
                }
                nodeVO.setData(data);
                nodeMap.put(nodeVO.getId(),nodeVO);
            }
        }
        for (Map.Entry<String, NodeVO<MaterialManagePlanTreeVO>> stringNodeVOEntry : nodeMap.entrySet()) {
            nodeList1.add(stringNodeVOEntry.getValue());
        }
        return nodeList1;
    }

    /**
     * 大修实施树
     *
     * @param searchVO
     * @return
     */
    @Override
    public ObjectTreeInfoVO<TreeNodeVO<NodeVO<MaterialManageImplementTreeVO>>> implementTree(SearchVO searchVO) throws Exception {
        String repairOrgId = searchVO.getRepairOrgId();
        String repairRound = searchVO.getRepairRound();
        String keyword = searchVO.getKeyword();
        //查修大修组织
        List<MajorRepairOrg> majorRepairOrgListAll= majorRepairOrgService.getList(repairRound, null);
        //大修组织为空
        if (ObjectUtil.isEmpty(majorRepairOrgListAll)) {
            return new ObjectTreeInfoVO<>();
        }
        // 过滤处理
        List<MajorRepairOrg> majorRepairOrgList= majorRepairOrgListAll;
        if(StringUtils.hasText(repairOrgId)){
            majorRepairOrgList= majorRepairOrgListAll.stream().filter(item-> Objects.equals(item.getParentId(),repairOrgId) || item.getChainPath().contains(repairOrgId)).collect(Collectors.toList());
            if (ObjectUtil.isEmpty(majorRepairOrgListAll)) {
                return new ObjectTreeInfoVO<>();
            }
        }

        //收集节点id
        List<String> idList = majorRepairOrgList.stream().map(LyraEntity::getId).collect(Collectors.toList());
        //查询关联表
        LambdaQueryWrapperX<RelationOrgToMaterial> condition = new LambdaQueryWrapperX<>(RelationOrgToMaterial.class);
        condition.in(RelationOrgToMaterial::getRepairOrgId, idList);
        List<RelationOrgToMaterial> relationList = this.list(condition);

        // 节点数据
        List<String> dataIdList = new ArrayList<>();
        List<NodeVO<MaterialManageImplementTreeVO>> nodeList = new ArrayList<>();
        if (CollUtil.isNotEmpty(majorRepairOrgList)) {
            majorRepairOrgList.forEach(x -> {
                NodeVO<MaterialManageImplementTreeVO> vo = new NodeVO<>();
                vo.setSort(x.getSort());
                vo.setRspUserId(x.getRspUserId());
                vo.setNodeType(x.getLevelType());
                vo.setParentId(x.getParentId());
                vo.setName(x.getName());
                vo.setChainPath(x.getChainPath());
                vo.setId(x.getId());
                vo.setCode(x.getCode());
                vo.setRspUserName(x.getRspUserName());
                MaterialManageImplementTreeVO dataVO = new MaterialManageImplementTreeVO();
                vo.setData(dataVO);
                nodeList.add(vo);
                dataIdList.add(x.getId());
            });
        }

        // 业务数据转换 保持行数据一致 方便统一统计
        Map<String, List<NodeVO<MaterialManageImplementTreeVO>>> idToDetailMap = new HashMap<>();

        if (CollUtil.isNotEmpty(relationList)) {
            //查询业务数据-查询物资数据以及根据物资id关联查询台账数据
            List<String> collectMeterialIds = relationList.stream().map(RelationOrgToMaterial::getMaterialId).distinct().collect(Collectors.toList());
            LambdaQueryWrapperX<MaterialManage> lqw = new LambdaQueryWrapperX<>(MaterialManage.class);
            lqw.select(MaterialManage::getRspUserId, MaterialManage::getRspUserName, MaterialManage::getActInDate
                    , MaterialManage::getAssetName, MaterialManage::getAssetCode, MaterialManage::getOutDate, MaterialManage::getCreateTime
                    , MaterialManage::getActOutDate, MaterialManage::getNumber, MaterialManage::getId, MaterialManage::getStatus);
            lqw.select(MaterialOutManage::getOutReason, MaterialOutManage::getMaterialDestination, MaterialOutManage::getIsAgainIn
                    , MaterialOutManage::getCreateTime, MaterialOutManage::getModifyTime, MaterialOutManage::getOutNum);
            lqw.selectAs(MaterialManage::getId, MaterialManageImplementTreeVO::getMaterialManageId);
            lqw.selectAs(FixedAssets::getId, MaterialManageImplementTreeVO::getFixedAssetsId);
            lqw.selectAs(MaterialManage::getInputStockNum, MaterialManageImplementTreeVO::getInputStockNum);
            // 同进痛处 数量一致
            lqw.selectAs(MaterialManage::getInputStockNum, MaterialManageImplementTreeVO::getOutNum);
            lqw.leftJoin(MaterialOutManage.class, MaterialOutManage::getSourceId, MaterialManage::getId);
            lqw.leftJoin(FixedAssets.class, FixedAssets::getNumber, MaterialManage::getNumber);
            //用物资名称搜索
            if (StrUtil.isNotBlank(keyword)) {
                lqw.and(e -> e.like(MaterialManage::getAssetName, keyword).or().like(MaterialManage::getNumber, keyword));
            }
            if (CollUtil.isNotEmpty(collectMeterialIds)) {
                lqw.in(MaterialManage::getId, collectMeterialIds);
            }
            //获取大修实施最新创建时间的数据，根据id进行分组
            List<MaterialManageImplementTreeVO> mmiList = materialManageMapper.selectJoinList(MaterialManageImplementTreeVO.class, lqw);
            Map<String, MaterialManageImplementTreeVO> map = new HashMap<>();
            if (CollUtil.isNotEmpty(mmiList)) {
                final List<DictValueVO> reasonDicts = dictRedisHelper.getDictListByCode(PMS_OUT_REASON);
                Map<String, String> reasonMap = reasonDicts.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (k1, k2) -> k1));
                mmiList.forEach(x -> {
                    MaterialManageImplementTreeVO vo = map.get(x.getId());
                    if(StringUtils.hasText(x.getOutReason())){
                        x.setOutReasonName(reasonMap.getOrDefault(x.getOutReason(), ""));
                    }
                    if (ObjectUtil.isNull(vo)) {
                        map.put(x.getId(), x);
                    } else {
                        if (x.getModifyTime().compareTo(vo.getModifyTime()) > 0) {
                            map.put(x.getId(), x);
                        }
                    }
                });
            }

            if (CollUtil.isNotEmpty(relationList) && map.size() > 0) {
                //根据大修组织和大修组织物资关系设置节点数据
                for (RelationOrgToMaterial x : relationList) {
                    List<NodeVO<MaterialManageImplementTreeVO>> nodeVOList = idToDetailMap.getOrDefault(x.getRepairOrgId(), new ArrayList<>());
                    NodeVO<MaterialManageImplementTreeVO> nodeVO = new NodeVO<>();
                    MaterialManageImplementTreeVO vo = map.get(x.getMaterialId());
                    if (ObjectUtil.isNull(vo)) {
                        continue;
                    }
                    nodeVO.setId(x.getId());
                    nodeVO.setCode(vo.getNumber());
                    nodeVO.setRspUserName(vo.getRspUserName());
                    nodeVO.setNodeType(MajorRepairOrgEnum.LEVEL_TYPE_GROUP_BUSINESS_DATA.getCode());
                    nodeVO.setParentId(x.getRepairOrgId());
                    nodeVO.setCreateTime(x.getCreateTime());
                    this.implementSimpleCount(vo);
                    nodeVO.setData(vo);
                    nodeVOList.add(nodeVO);
                    // 设计单行的统计数
                    idToDetailMap.put(x.getRepairOrgId(), nodeVOList);
                    dataIdList.add(x.getId());
                }
            }
        }

        //业务数据排序
        if (!MapUtils.isEmpty(idToDetailMap)) {
            idToDetailMap = idToDetailMap.entrySet().stream().collect(
                    Collectors.toMap(Map.Entry::getKey, entry -> {
                        List<NodeVO<MaterialManageImplementTreeVO>> nodeVOList = entry.getValue();
                        nodeVOList.sort(Comparator.comparing(NodeVO::getCreateTime));
                        return nodeVOList;
                    })
            );
        }

        //过滤父级节点为空的数据且节点不为顶级节点
        List<NodeVO<MaterialManageImplementTreeVO>> voList = nodeList.stream().filter(x -> StrUtil.equals("0", x.getId()) || StrUtil.isNotBlank(x.getParentId())).collect(Collectors.toList());
        //获取当前人拥有的权限对于数据列表
        Map<String, Set<String>> dataIdToRoleMap = commonRoleBo.currentUserRoles(dataIdList, majorRepairOrgList);
        List<NodeVO<MaterialManageImplementTreeVO>> nodeList1 = this.levelImplCount(voList,idToDetailMap);
        log.info("【阶段--传入参数】：{}", JSONObject.toJSONString(nodeList1));
        TreeInfoProcessor<NodeVO<MaterialManageImplementTreeVO>> processor = new TreeInfoProcessor<>(
                nodeList1, NodeVO::getId, NodeVO::getParentId, NodeVO::getRspUserId, CurrentUserHelper.getCurrentUserId(),
                NodeVO::getSort, dataIdToRoleMap, false,false, idToDetailMap
        );
        ObjectTreeInfoVO<TreeNodeVO<NodeVO<MaterialManageImplementTreeVO>>> res = new ObjectTreeInfoVO<>();
        res.setTreeNodeVOList(processor.getRootList());
        res.setParenIdList(processor.getParenIdList());
        res.setOneOrTwoIdList(processor.getOneOrTwoIdList());
        return res;
    }

    private  List<NodeVO<MaterialManageImplementTreeVO>> levelImplCount(List<NodeVO<MaterialManageImplementTreeVO>> nodeList, Map<String, List<NodeVO<MaterialManageImplementTreeVO>>> idToDetailMap) {
//所有节点 然后进行 去重统计
        Map<String,Set<String>> idToAllChildList = new HashMap<>();
        Map<String,Set<String>> nodeIdToDataIdSet = new HashMap<>();
        List<NodeVO<MaterialManageImplementTreeVO>> nodeList1 = new ArrayList<>();
        Map<String ,NodeVO<MaterialManageImplementTreeVO>> businessDataMap = new HashMap<>();
        Map<String ,NodeVO<MaterialManageImplementTreeVO>> nodeMap = nodeList.stream().collect(Collectors.toMap(NodeVO::getId,Function.identity(),(k1, k2)->k1));
        for (Map.Entry<String, List<NodeVO<MaterialManageImplementTreeVO>>> stringListEntry : idToDetailMap.entrySet()) {
            // 获取节点信息
            NodeVO<MaterialManageImplementTreeVO> nodeVO = nodeMap.get(stringListEntry.getKey());
            // 获取节点的全路径
            String chainPath = nodeVO.getChainPath();
            String[]  parentIdList = chainPath.split(",");
            for (String s : parentIdList) {
                Set<String> chiledSet = idToAllChildList.getOrDefault(s,new HashSet<>());
                chiledSet.add(nodeVO.getId());
                idToAllChildList.put(s,chiledSet);
            }
            nodeIdToDataIdSet.put(nodeVO.getId(),stringListEntry.getValue().stream().map(NodeVO::getId).collect(Collectors.toSet()));
            // 设置业务ID对应的 业务数据
            List<NodeVO<MaterialManageImplementTreeVO>> businessList =stringListEntry.getValue();
            for (NodeVO<MaterialManageImplementTreeVO> business : businessList) {
                businessDataMap.put(business.getId(),business);
            }
        }
        for (Map.Entry<String, Set<String>> stringSetEntry : idToAllChildList.entrySet()) {
            NodeVO<MaterialManageImplementTreeVO> nodeVO = nodeMap.get(stringSetEntry.getKey());
            if (ObjectUtil.isEmpty(nodeVO)){
                continue;
            }
            Set<String> childIdList=  stringSetEntry.getValue();
            Set<String> disDataIdSet = new HashSet<>();
            for (String s : childIdList) {
                Set<String> dataIdSet = nodeIdToDataIdSet.get(s);
                if(dataIdSet != null && !dataIdSet.isEmpty()){
                    disDataIdSet.addAll(dataIdSet);
                }
            }
            if(!disDataIdSet.isEmpty()){
                int planOutCount= 0;
                int planInNotCount=0;
                for (String s : disDataIdSet) {
                    NodeVO<MaterialManageImplementTreeVO>  business =    businessDataMap.get(s);
                    if(null != business) {
                        planOutCount+= business.getData().getPlanOutCount();
                        planInNotCount+= business.getData().getPlanInNotCount();
                    }
                }
                MaterialManageImplementTreeVO data =  nodeVO.getData();
                data.setPlanOutCount(planOutCount);
                data.setPlanInCount(planInNotCount);
                nodeVO.setData(data);
                nodeMap.put(nodeVO.getId(),nodeVO);
            }
        }
        for (Map.Entry<String, NodeVO<MaterialManageImplementTreeVO>> stringNodeVOEntry : nodeMap.entrySet()) {
            nodeList1.add(stringNodeVOEntry.getValue());
        }
        return nodeList1;
    }

    /**
     * 大修准备数据赋值
     *
     * @param vo
     */
    private void planSimpleCount(MaterialManagePlanTreeVO vo) {
        if (ObjectUtil.isNotNull(vo)) {
            //统计数据
            //设备数
            vo.setDeviceNumber(vo.getInputStockNum() == null?0:vo.getInputStockNum());
            //计量数
            if (vo.getIsMetering() != null && vo.getIsMetering()) {
                vo.setToolNumber(vo.getDeviceNumber());
            }
            //计划入场时间未报备
            vo.setNoPlanIn(vo.getInDate() == null ? vo.getDeviceNumber() : 0);
            vo.setPlanIn(vo.getInDate() != null ? vo.getDeviceNumber() : 0);

            vo.setActInCount(vo.getStatus() == 1 ? vo.getDeviceNumber() : 0);
            vo.setActInCountNo(vo.getActInDate() == null ? vo.getDeviceNumber() : 0);
        }
    }

    /**
     * 大修实施数据赋值
     *
     * @param vo
     */
    private void implementSimpleCount(MaterialManageImplementTreeVO vo) {
        if (ObjectUtil.isNotNull(vo)) {
            //统计数据
            //实际离场物资数
            vo.setPlanOutCount(vo.getOutNum() == null? 0:vo.getOutNum());
            //实际离场未报备物资数
             Date outDate = vo.getOutDate();
             if(Objects.isNull(vo.getActOutDate())){
                 if(Objects.nonNull(outDate) && outDate.compareTo(new Date()) <=0){
                     vo.setPlanInNotCount(1);
                 }
             }


        }
    }

    @Override
    public Boolean isExistRelation(List<String> repairOrgIdList) {
        if (CollectionUtils.isEmpty(repairOrgIdList)) {
            return false;
        }
        LambdaQueryWrapperX<RelationOrgToMaterial> condition = new LambdaQueryWrapperX<>(RelationOrgToMaterial.class);
        condition.in(RelationOrgToMaterial::getRepairOrgId, repairOrgIdList);
        List<RelationOrgToMaterial> list = this.list(condition);
        if (ObjectUtil.isNotEmpty(list)) {
            return true;
        }
        return false;
    }

    /**
     * 获取准备阶段的统计下转物资列表
     *
     * @param dto
     * @return
     */
    @Override
    public List<MaterialDownVO> prepareDownList(MaterialDownDTO dto) throws Exception {
        //获取大修组织下的子组织
        String repairRound = dto.getRepairRound();
        String majorRepairOrg = dto.getMajorRepairOrg();
        List<MajorRepairOrg> orgList = majorRepairOrgService.getList(repairRound, null);
        List<String> orgIds = orgList.stream().filter(org-> org.getChainPath().contains(majorRepairOrg)).map(MajorRepairOrg::getId).collect(Collectors.toList());

        //查修大修组织
        LambdaQueryWrapperX<RelationOrgToMaterial> lqw = new LambdaQueryWrapperX<>(RelationOrgToMaterial.class);
        lqw.selectAll(MaterialManage.class);
        lqw.selectAs(MaterialManage::getId, MaterialDownVO::getMaterialId);
        lqw.selectAs(RelationOrgToMaterial::getId, MaterialDownVO::getId);
        lqw.select(MajorRepairOrg::getParentId, MajorRepairOrg::getChainPath, MajorRepairOrg::getName);
        lqw.select(MaterialOutManage::getOutNum, MaterialOutManage::getOutReason,
                MaterialOutManage::getMaterialDestination, MaterialOutManage::getModifyTime);
        lqw.selectAs(MajorRepairOrg::getId, MaterialDownVO::getRelationId);
        lqw.leftJoin(MajorRepairOrg.class, MajorRepairOrg::getId, RelationOrgToMaterial::getRepairOrgId);
        lqw.leftJoin(MaterialManage.class, MaterialManage::getId, RelationOrgToMaterial::getMaterialId);
        lqw.leftJoin(MaterialOutManage.class, MaterialOutManage::getSourceId, MaterialManage::getId);
        lqw.in(MajorRepairOrg::getId, orgIds);
        String keyword = dto.getKeyword();
        if (ObjectUtil.isNotEmpty(keyword)) {
            lqw.and(e -> e.like(MaterialManage::getAssetName, keyword).or().like(MaterialManage::getNumber, keyword));
        }
        MaterialDownEnum materialDownEnum = dto.getMaterialDownEnum();
        if (StrUtil.equals(materialDownEnum.name(), "deviceNumber")) {
            lqw.distinct();
        }
        lqw.apply(materialDownEnum.getSqlDesc());
        List<MaterialDownVO> mdList = this.selectJoinList(MaterialDownVO.class, lqw);
        if (CollUtil.isNotEmpty(mdList)) {
            //数据去重，根据最新的修改时间获取物资数据
            Map<String, MaterialDownVO> map = new HashMap<>();
            mdList.forEach(x -> {
                String materialId = x.getMaterialId();
                MaterialDownVO vo = map.get(materialId);
                if (vo == null) {
                    map.put(materialId, x);
                } else if (x.getModifyTime().compareTo(vo.getModifyTime()) > 0) {
                    map.put(materialId, x);
                }
            });
            ArrayList<MaterialDownVO> list = new ArrayList<>(map.values());
            //获取当前人拥有的权限对于数据列表
            List<MajorRepairOrg> majorRepairOrgList = majorRepairOrgService.getList(repairRound, null);
            Map<String, Set<String>> id2RoleMap = commonRoleBo.currentUserRolesList(list, majorRepairOrgList);
            //权限数据绑定
            list.forEach(x -> x.setRoleList(id2RoleMap.get(x.getId())));
            return list;
        }
        return mdList;
    }

}