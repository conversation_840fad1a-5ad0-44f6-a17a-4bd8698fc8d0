package com.chinasie.orion.manager.impl.msg;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.conts.MsgBusinessTypeEnum;
import com.chinasie.orion.domain.dto.SchemeMsgDTO;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.manager.SendMessageCommonAdapter;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.tenant.core.context.TenantContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import java.util.*;


@Slf4j
@Component("fallbackSendMsgAdapter")
public class FallbackSendMsgAdapter extends SendMessageCommonAdapter {



    protected <T> List<SendMessageDTO> buildMscMessageDTO(SchemeMsgDTO schemeMsgDTO) throws Exception {
        List<SendMessageDTO> messageDTOList = CollUtil.toList();
        List<ProjectScheme> projectSchemeList = schemeMsgDTO.getProjectSchemeList();
        List<String> recipientIdList = schemeMsgDTO.getRecipientIds();

        projectSchemeList.forEach(item -> {
            String parentId = "";
            //获取最父级id
            if(item.getLevel() != 1){
                String[] ids = item.getParentChain().split(",");
                if(ids.length > 1){
                    parentId = ids[1];
                }
            }else{
                parentId = item.getId();
            }
            SendMessageDTO backMessageDTO = SendMessageDTO.builder()
                    .businessData(JSON.toJSONString(MapUtil.builder()
                            .put("flowType", TYPE_FLOW_TYPE_MAP.get(MsgBusinessTypeEnum.FALLBACK))
                            .put("parentId",parentId)
                            .put("id",item.getId())
                            .put("projectId", item.getProjectId())
                            .build()))
                    .titleMap(MapUtil.builder(new HashMap<String, Object>())
                            .put("$name$", item.getName())
                            .build())
                    .messageMap(MapUtil.builder(new HashMap<String, Object>())
                            .put("$name$", item.getName())
                            .build())
                    .businessNodeCode(TYPE_CODE_MAP.get(MsgBusinessTypeEnum.FALLBACK))
                    .businessTypeCode("ProjectScheme")
                    .businessTypeName("项目计划")
                    .todoType(TodoTypeDict.TODO_TYPE_TASK)
                    .messageUrl(String.format("/pms/menuComponents?id=%stabType=project_plan", item.getProjectId()))
                    .messageUrlName("项目计划列表页")
                    .recipientIdList(recipientIdList)
                    .senderId(userHelper.getUserId())
                    .senderTime(new Date())
                    .businessId(packageBusinessId(MsgBusinessTypeEnum.FALLBACK, item.getId()))
                    .platformId(CurrentUserHelper.getPId())
                    .orgId(TenantContextHolder.getTenantId())
                    .build();
            messageDTOList.add(backMessageDTO);
        });
        return messageDTOList;
    }


}
