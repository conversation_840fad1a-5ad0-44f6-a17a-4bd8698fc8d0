package com.chinasie.orion.service.impl;

import com.chinasie.orion.domain.entity.TrainEquivalentRelation;
import com.chinasie.orion.domain.dto.TrainEquivalentRelationDTO;
import com.chinasie.orion.domain.vo.TrainEquivalentRelationVO;
import com.chinasie.orion.service.TrainEquivalentRelationService;
import com.chinasie.orion.repository.TrainEquivalentRelationMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * TrainEquivalentRelation 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 17:09:19
 */
@Service
@Slf4j
public class TrainEquivalentRelationServiceImpl extends  OrionBaseServiceImpl<TrainEquivalentRelationMapper, TrainEquivalentRelation>   implements TrainEquivalentRelationService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  TrainEquivalentRelationVO detail(String id,String pageCode) throws Exception {
        TrainEquivalentRelation trainEquivalentRelation =this.getById(id);
        TrainEquivalentRelationVO result = BeanCopyUtils.convertTo(trainEquivalentRelation,TrainEquivalentRelationVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param trainEquivalentRelationDTO
     */
    @Override
    public  String create(TrainEquivalentRelationDTO trainEquivalentRelationDTO) throws Exception {
        TrainEquivalentRelation trainEquivalentRelation =BeanCopyUtils.convertTo(trainEquivalentRelationDTO,TrainEquivalentRelation::new);
        this.save(trainEquivalentRelation);

        String rsp=trainEquivalentRelation.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param trainEquivalentRelationDTO
     */
    @Override
    public Boolean edit(TrainEquivalentRelationDTO trainEquivalentRelationDTO) throws Exception {
        TrainEquivalentRelation trainEquivalentRelation =BeanCopyUtils.convertTo(trainEquivalentRelationDTO,TrainEquivalentRelation::new);

        this.updateById(trainEquivalentRelation);

        String rsp=trainEquivalentRelation.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<TrainEquivalentRelationVO> pages( Page<TrainEquivalentRelationDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<TrainEquivalentRelation> condition = new LambdaQueryWrapperX<>( TrainEquivalentRelation. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(TrainEquivalentRelation::getCreateTime);


        Page<TrainEquivalentRelation> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), TrainEquivalentRelation::new));

        PageResult<TrainEquivalentRelation> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<TrainEquivalentRelationVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<TrainEquivalentRelationVO> vos = BeanCopyUtils.convertListTo(page.getContent(), TrainEquivalentRelationVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "员工培训等效关联表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", TrainEquivalentRelationDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            TrainEquivalentRelationExcelListener excelReadListener = new TrainEquivalentRelationExcelListener();
        EasyExcel.read(inputStream,TrainEquivalentRelationDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<TrainEquivalentRelationDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("员工培训等效关联表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<TrainEquivalentRelation> trainEquivalentRelationes =BeanCopyUtils.convertListTo(dtoS,TrainEquivalentRelation::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::TrainEquivalentRelation-import::id", importId, trainEquivalentRelationes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<TrainEquivalentRelation> trainEquivalentRelationes = (List<TrainEquivalentRelation>) orionJ2CacheService.get("pmsx::TrainEquivalentRelation-import::id", importId);
        log.info("员工培训等效关联表导入的入库数据={}", JSONUtil.toJsonStr(trainEquivalentRelationes));

        this.saveBatch(trainEquivalentRelationes);
        orionJ2CacheService.delete("pmsx::TrainEquivalentRelation-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::TrainEquivalentRelation-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<TrainEquivalentRelation> condition = new LambdaQueryWrapperX<>( TrainEquivalentRelation. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(TrainEquivalentRelation::getCreateTime);
        List<TrainEquivalentRelation> trainEquivalentRelationes =   this.list(condition);

        List<TrainEquivalentRelationDTO> dtos = BeanCopyUtils.convertListTo(trainEquivalentRelationes, TrainEquivalentRelationDTO::new);

        String fileName = "员工培训等效关联表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", TrainEquivalentRelationDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<TrainEquivalentRelationVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class TrainEquivalentRelationExcelListener extends AnalysisEventListener<TrainEquivalentRelationDTO> {

        private final List<TrainEquivalentRelationDTO> data = new ArrayList<>();

        @Override
        public void invoke(TrainEquivalentRelationDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<TrainEquivalentRelationDTO> getData() {
            return data;
        }
    }


}
