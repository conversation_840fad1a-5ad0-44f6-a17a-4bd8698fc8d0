package com.chinasie.orion.service.impl;

import cn.zhxu.bs.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.base.api.domain.entity.RoleDO;
import com.chinasie.orion.base.api.repository.RoleDOMapper;
import com.chinasie.orion.domain.dto.permission.RoleParamDTO;
import com.chinasie.orion.domain.entity.MajorRepairPlanRole;
import com.chinasie.orion.domain.dto.MajorRepairPlanRoleDTO;
import com.chinasie.orion.domain.vo.MajorRepairPlanRoleVO;


import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.MyExceptionCode;
import com.chinasie.orion.service.MajorRepairPlanMemberService;
import com.chinasie.orion.service.MajorRepairPlanRoleService;
import com.chinasie.orion.repository.MajorRepairPlanRoleMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import java.util.*;
import java.util.stream.Collectors;

import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * MajorRepairPlanRole 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30 19:21:00
 */
@Service
@Slf4j
public class MajorRepairPlanRoleServiceImpl extends  OrionBaseServiceImpl<MajorRepairPlanRoleMapper, MajorRepairPlanRole>   implements MajorRepairPlanRoleService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private MajorRepairPlanMemberService majorRepairPlanMemberService;

    @Autowired
    private  MajorRepairPlanRoleService majorRepairPlanRoleService;

    @Autowired
    private MajorRepairPlanRoleMapper majorRepairPlanRoleMapper;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  MajorRepairPlanRoleVO detail(String id,String pageCode) throws Exception {
        MajorRepairPlanRole majorRepairPlanRole =this.getById(id);
        MajorRepairPlanRoleVO result = BeanCopyUtils.convertTo(majorRepairPlanRole,MajorRepairPlanRoleVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param majorRepairPlanRoleDTO
     */
    @Override
    public  String create(MajorRepairPlanRoleDTO majorRepairPlanRoleDTO) throws Exception {
        MajorRepairPlanRole majorRepairPlanRole =BeanCopyUtils.convertTo(majorRepairPlanRoleDTO,MajorRepairPlanRole::new);
        this.save(majorRepairPlanRole);

        String rsp=majorRepairPlanRole.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param majorRepairPlanRoleDTO
     */
    @Override
    public Boolean edit(MajorRepairPlanRoleDTO majorRepairPlanRoleDTO) throws Exception {
        LambdaQueryWrapper<MajorRepairPlanRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MajorRepairPlanRole::getId, majorRepairPlanRoleDTO.getId());
        MajorRepairPlanRole majorRepairPlanRole = this.getOne(queryWrapper, false);
        if (majorRepairPlanRole == null) {
            throw new Exception("Record not found with id: " + majorRepairPlanRoleDTO.getId());
        }

        majorRepairPlanRole.setRoleLevel(majorRepairPlanRoleDTO.getRoleLevel());

        return this.updateById(majorRepairPlanRole);
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {

       Boolean exist = majorRepairPlanMemberService.existByBusinessIds(ids);
        if(exist){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "角色下有成员，不能删除");

        }
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<MajorRepairPlanRoleVO> pages( Page<MajorRepairPlanRoleDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<MajorRepairPlanRole> condition = new LambdaQueryWrapperX<>( MajorRepairPlanRole. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.leftJoin(RoleDO.class, RoleDO::getCode, MajorRepairPlanRole::getRoleCode);
        condition.selectAs(RoleDO::getName, MajorRepairPlanRoleVO::getRemark);
        condition.select(MajorRepairPlanRole::getId,MajorRepairPlanRole::getRoleCode,MajorRepairPlanRole::getRemark,MajorRepairPlanRole::getModifyId,
                MajorRepairPlanRole::getModifyTime,MajorRepairPlanRole::getMajorRepairTurn,MajorRepairPlanRole::getRoleLevel);
        condition.orderByDesc(MajorRepairPlanRole::getCreateTime);

        MajorRepairPlanRoleDTO repairPlanRoleDTO= pageRequest.getQuery();
        if(Objects.nonNull(repairPlanRoleDTO)){
            if(org.springframework.util.StringUtils.hasText(repairPlanRoleDTO.getMajorRepairTurn())){
                condition.eq(MajorRepairPlanRole::getMajorRepairTurn,repairPlanRoleDTO.getMajorRepairTurn());
            }
            if(StringUtils.isNotBlank(repairPlanRoleDTO.getRoleName())){
                condition.like(RoleDO::getName,repairPlanRoleDTO.getRoleName());
            }
        }

        Page<MajorRepairPlanRole> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), MajorRepairPlanRole::new));

        PageResult<MajorRepairPlanRole> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<MajorRepairPlanRoleVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<MajorRepairPlanRoleVO> vos = BeanCopyUtils.convertListTo(page.getContent(), MajorRepairPlanRoleVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public Page<MajorRepairPlanRoleVO> pagesByLevel(Page<MajorRepairPlanRoleDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<MajorRepairPlanRole> condition = new LambdaQueryWrapperX<>( MajorRepairPlanRole. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.leftJoin(RoleDO.class, RoleDO::getCode, MajorRepairPlanRole::getRoleCode);
        condition.selectAs(RoleDO::getName, MajorRepairPlanRoleVO::getRemark);
        condition.select(MajorRepairPlanRole::getId,MajorRepairPlanRole::getRoleCode,MajorRepairPlanRole::getRemark
                ,MajorRepairPlanRole::getMajorRepairTurn,MajorRepairPlanRole::getRoleLevel);
        condition.orderByDesc(MajorRepairPlanRole::getCreateTime);

        MajorRepairPlanRoleDTO repairPlanRoleDTO= pageRequest.getQuery();
        if(Objects.nonNull(repairPlanRoleDTO)){
            if(org.springframework.util.StringUtils.hasText(repairPlanRoleDTO.getMajorRepairTurn())){
                condition.eq(MajorRepairPlanRole::getMajorRepairTurn,repairPlanRoleDTO.getMajorRepairTurn());
            }

            condition.eq(MajorRepairPlanRole::getRoleLevel,pageRequest.getQuery().getRoleLevel());
            condition.eq(MajorRepairPlanRole::getLogicStatus,"1");

            if(StringUtils.isNotBlank(repairPlanRoleDTO.getRoleName())){
                condition.like(RoleDO::getName,repairPlanRoleDTO.getRoleName());
            }
        }

        Page<MajorRepairPlanRole> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), MajorRepairPlanRole::new));

        PageResult<MajorRepairPlanRole> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<MajorRepairPlanRoleVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<MajorRepairPlanRoleVO> vos = BeanCopyUtils.convertListTo(page.getContent(), MajorRepairPlanRoleVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public void  setEveryName(List<MajorRepairPlanRoleVO> vos)throws Exception {
        vos.forEach(vo->{
            vo.setRoleName(vo.getRemark());
        });


    }

    @Override
    public Boolean batchAdd(RoleParamDTO roleParamDTO) {

        String majorRepairTurn = roleParamDTO.getMajorRepairTurn();
        List<String> roleCodeList = roleParamDTO.getRoleCodeList();

        LambdaQueryWrapperX<MajorRepairPlanRole> condition = new LambdaQueryWrapperX<>( MajorRepairPlanRole. class);
        condition.eq(MajorRepairPlanRole::getMajorRepairTurn,majorRepairTurn);
        condition.in(MajorRepairPlanRole::getRoleCode,roleCodeList);
        condition.select(MajorRepairPlanRole::getId,MajorRepairPlanRole::getRoleCode);
        condition.select(MajorRepairPlanRole::getId,MajorRepairPlanRole::getRoleCode,MajorRepairPlanRole::getRemark,MajorRepairPlanRole::getModifyId,
                MajorRepairPlanRole::getModifyTime,MajorRepairPlanRole::getMajorRepairTurn);
        List<MajorRepairPlanRole> majorRepairPlanRolees = this.list(condition);
        if (!CollectionUtils.isEmpty(majorRepairPlanRolees)) {
            List<String> rocodes = majorRepairPlanRolees.stream().map(MajorRepairPlanRole::getRoleCode).distinct().collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(rocodes)){
                roleCodeList.removeAll(rocodes);
            }
        }
        if(!CollectionUtils.isEmpty(roleCodeList)){
            List<MajorRepairPlanRole> majorRepairPlanRoleList = new ArrayList<>();
            for (String s : roleCodeList) {
                MajorRepairPlanRole majorRepairPlanRole = new MajorRepairPlanRole();
                majorRepairPlanRole.setMajorRepairTurn(majorRepairTurn);
                majorRepairPlanRole.setRoleCode(s);
                majorRepairPlanRoleList.add(majorRepairPlanRole);
            }
            return this.saveBatch(majorRepairPlanRoleList);
        }
        return Boolean.TRUE;
    }

    @Override
    public List<MajorRepairPlanRoleVO> listByEntity(MajorRepairPlanRoleDTO majorRepairPlanRoleDTO) throws Exception {
        String majorRepairTurn =majorRepairPlanRoleDTO.getMajorRepairTurn();
        if (StringUtils.isBlank(majorRepairTurn)){
            throw new BaseException(MyExceptionCode.ERROR_PARAM);
        }
        LambdaQueryWrapperX<MajorRepairPlanRole> condition = new LambdaQueryWrapperX<>( MajorRepairPlanRole. class);
        condition.eq(MajorRepairPlanRole::getMajorRepairTurn,majorRepairTurn);
        condition.leftJoin(RoleDO.class, RoleDO::getCode, MajorRepairPlanRole::getRoleCode);
        condition.selectAs(RoleDO::getName, MajorRepairPlanRoleVO::getRemark);
        condition.select(MajorRepairPlanRole::getId,MajorRepairPlanRole::getRoleCode,MajorRepairPlanRole::getRemark,MajorRepairPlanRole::getMajorRepairTurn);
        List<MajorRepairPlanRole> majorRepairPlanRolees = this.list(condition);
        if(CollectionUtils.isEmpty(majorRepairPlanRolees)){
            new ArrayList<>();
        }
        List<MajorRepairPlanRoleVO> vos= BeanCopyUtils.convertListTo(majorRepairPlanRolees,MajorRepairPlanRoleVO::new);
        this.setEveryName(vos);
        return vos;
    }

    @Override
    public MajorRepairPlanRole getEntityByRoundAndCode(String repairRound, String code) {
        if(StringUtils.isNotBlank(repairRound)&&StringUtils.isNotBlank(code)){
            LambdaQueryWrapperX<MajorRepairPlanRole> condition = new LambdaQueryWrapperX<>( MajorRepairPlanRole. class);
            condition.eq(MajorRepairPlanRole::getMajorRepairTurn,repairRound);
            condition.eq(MajorRepairPlanRole::getRoleCode,code);
            condition.select(MajorRepairPlanRole::getId,MajorRepairPlanRole::getRoleCode,MajorRepairPlanRole::getRemark,MajorRepairPlanRole::getMajorRepairTurn);
            List<MajorRepairPlanRole> list = this.list(condition);
            if(!CollectionUtils.isEmpty(list)){
                return list.get(0);
            }
        }
        return null;
    }


}
