<template>
  <Spin
    :delay="300"
    :spinning="$props.spinning"
  >
    <div
      :class="['details-container',{'is-form-item':isFormItem},{'table-header-hide':!$props.showTableHeader}]"
    >
      <div
        v-if="!$props.isContent"
        ref="detailsRef"
        :class="['details-container-title',{'is-form-item':isFormItem},{'border-bottom':$props.borderBottom}]"
      >
        <span
          v-if="props.required"
          class="required-icon"
        >*</span>
        <span>{{ $props.title }}</span>
        <slot name="title-right" />
      </div>
      <div
        :style="{width:containerWidth}"
        :class="['details-container-content',{'is-form-item':isFormItem},{'no-content':$props.noContent},{'is-content':$props.isContent}]"
      >
        <slot>
          <div
            v-if="isEmpty"
            style="height: 300px"
            class="w-full flex flex-ac flex-pc"
          >
            <Empty :image="Empty.PRESENTED_IMAGE_SIMPLE" />
          </div>
          <!--默认插槽使用grid布局-->
          <div
            v-else-if="$props.list.length"
            :class="['details-grid',{'mb20':isTable}]"
            :style="{'grid-template-columns': `repeat(${$props.column}, 1fr)`,gap:$props.gap}"
          >
            <template
              v-for="item in $props.list"
              :key="item.field"
            >
              <div
                v-if="!item.hidden"
                :class="item['class']"
                :style="{'grid-column':item.gridColumn||undefined}"
              >
                <!--label自定义渲染-->
                <component
                  :is="item?.['labelRender']()"
                  v-if="item?.['labelRender']"
                />
                <div
                  v-else
                  class="label flex-te"
                  :style="{fontWeight:item.labelBold||$props.labelBold?'600':'400',width:$props.labelWidth,textAlign:$props.labelAlign}"
                >
                  {{ item.label ? item.label + '：' : '' }}
                </div>

                <!--value自定义渲染-->
                <component
                  :is="item['valueRender']({text:getValue(item),record:$props.dataSource})"
                  v-if="item?.['valueRender']"
                />
                <!--目前只支持自定义value插槽-->
                <template v-else-if="item.slot && item.slotName">
                  <slot
                    :name="item.slotName"
                    :field="item.field"
                    :record="$props.dataSource"
                    :text="getValue(item)"
                  />
                </template>
                <div
                  v-else-if="Object.keys($props.dataSource||{}).length"
                  :class="['value',{'flex-te':!item.wrap}]"
                >
                  <template v-if="item.field==='dataStatus' && getValue(item)">
                    <DataStatusTag :statusData="getValue(item)" />
                  </template>
                  <template v-else-if="item.formatTime && getValue(item)">
                    {{
                      dayjs(getValue(item))
                        .format(item.formatTime)
                    }}
                  </template>
                  <template v-else-if="item.isMoney">
                    {{ formatMoney(getValue(item), item?.precision) }}{{ item.unit || '' }}
                  </template>
                  <template v-else-if="item.isBoolean">
                    {{ getValue(item) === false ? '否' : getValue(item) === true ? '是' : '--' }}
                  </template>
                  <template v-else>
                    <span :title="getValue(item)||'--'">{{ getValue(item) || '--' }}</span>
                  </template>
                </div>
              </div>
            </template>
          </div>
        </slot>
        <!--表格插槽-->
        <slot
          name="table"
        />
      </div>
    </div>
  </Spin>
</template>

<script setup lang="ts">
import { Empty, Spin } from 'ant-design-vue';
import {
  computed, onMounted, onUnmounted, ref, useSlots, VNode,
} from 'vue';
import dayjs from 'dayjs';
import { DataStatusTag } from 'lyra-component-vue3';
import { formatMoney } from '/@/views/pms/projectManage/utils';

interface ItemType {
  label: string,
  slot?: boolean,
  gridColumn?: string,
  slotName?: string,
  wrap?: boolean,
  formatTime?: string,
  hidden?: any,
  labelBold?: boolean,
  isMoney?: boolean,
  isBoolean?: boolean,
  field: Array<string> | string,
  unit?: string,
  precision?: number,
  labelRender?: VNode,
  valueRender?: VNode
}

interface Props {
  // 必填标识
  required?: boolean,
  // 是否加载中
  spinning?: boolean,
  // 标题
  title?: string,
  // 是否开启标签字体加粗
  labelBold?: boolean,
  // grid布局上下左右间距
  gap?: string,
  // grid布局列数
  column?: number,
  // 是否显示空状态
  isEmpty?: boolean,
  // 是否为表单项
  isFormItem?: boolean,
  // 是否为空内容
  noContent?: boolean,
  // 字段数据
  list?: ItemType[],
  // 请求数据
  dataSource?: any,
  // 详情模式标签的宽度
  labelWidth?: string,
  // 详情模式标签的对齐方式
  labelAlign?: string,
  // 是否展示表格头部区域
  showTableHeader?: boolean
  // 是否显示标题底部分割线
  borderBottom?: boolean
  // 是否只展示内容
  isContent?: boolean
}

const slots = useSlots();

const isTable = computed(() => Object.keys(slots)
  .includes('table'));

const props = withDefaults(defineProps<Props>(), {
  spinning: false,
  labelBold: false,
  gap: '20px 60px',
  column: 4,
  isEmpty: false,
  isFormItem: false,
  noContent: false,
  name: '',
  title: '',
  list: () => [],
  dataSource: () => ({}),
  labelWidth: 'auto',
  labelAlign: 'left',
  showTableHeader: false,
  borderBottom: false,
  isContent: false,
});

const containerWidth = ref<string>('calc(100% - 40px)');
const detailsRef = ref();
onMounted(() => {
  setTimeout(() => {
    windowResize();
  });
  detailsRef.value && window.addEventListener('resize', windowResize);
});

onUnmounted(() => {
  detailsRef.value && window.removeEventListener('resize', windowResize);
});

async function windowResize() {
  containerWidth.value = `${detailsRef?.value?.clientWidth}px`;
}

// 根据字段获取对应值
function getValue(item) {
  if (typeof item.field === 'string') {
    return props.dataSource?.[item?.field];
  }
  if (item.field instanceof Array) {
    let value = '';
    item?.field?.forEach((v, index) => {
      if (index === 0) {
        value = props.dataSource?.[v];
      } else {
        value = value?.[v];
      }
    });
    return value;
  }
}
</script>

<style scoped lang="less">
.details-container {
  display: flex;
  flex-direction: column;
  padding: ~`getPrefixVar('content-margin')` 0;

  &.is-form-item {
    padding: 0;
  }

  .details-container-title {
    position: relative;
    font-size: 18px;
    font-weight: 500;
    color: ~`getPrefixVar('text-color')`;
    padding: 0 ~`getPrefixVar('button-margin')`;
    //margin: 0 ~`getPrefixVar('content-margin')`;
    line-height: 28px;
    display: flex;
    align-items: center;

    &.border-bottom::after {
      content: '';
      position: absolute;
      bottom: -5px;
      left: 0;
      width: 100%;
      height: 1px;
      background-color: #ccc;
    }

    .required-icon {
      color: ~`getPrefixVar('error-color')`;
      font-family: SimSun, sans-serif;
      margin-right: 4px;
      font-size: 14px;
    }

    &::before {
      position: absolute;
      content: '';
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 18px;
      background-color: ~`getPrefixVar('primary-color')`;
    }

    &.is-form-item {
      margin: 0;
    }
  }

  .details-container-content {
    //margin: 0 ~`getPrefixVar('content-margin')`;
    padding: ~`getPrefixVar('content-margin')` ~`getPrefixVar('button-margin')` 0;

    &.is-content {
      margin: 0;
      padding: 0;
    }

    &.no-content {
      padding: 0;
    }

    .details-grid {
      display: grid;

      > div {
        display: flex;

        .label, .value {
          color: ~`getPrefixVar('primary-10')`;
        }

        .value {
          flex-grow: 1;
          width: 0;
          word-break: break-all;
        }

        &.flex-ver .value {
          width: 100%;
          white-space: pre-wrap;
        }
      }
    }

    &.is-form-item {
      margin: 0;
    }
  }

  :deep(.ant-basic-table) {
    padding: 0;
  }
}
:slotted(.right){
  margin-left: auto;
}
:slotted(.right-tip){
  font-size: 12px;
}

</style>
<style scoped lang="less">
.table-header-hide {
  .ant-basic-table {
    .orion-table-header-wrap {
      display: none;
    }
  }
}
</style>
