<script setup lang="ts">
import {
  BasicTableAction, ITableActionItem, Layout3, Layout3Content, useDrawer, isPower,
} from 'lyra-component-vue3';
import { WorkflowAction, WorkflowProps, WorkflowView } from 'lyra-workflow-component-vue3';
import {
  computed, ComputedRef, onMounted, provide, readonly, ref, Ref, unref,
} from 'vue';
import { Empty, Spin } from 'ant-design-vue';
import { useRoute } from 'vue-router';
import { MenuItem } from './type';
import ProjectInfo from './components/ProjectInfo.vue';
import MeetingMinutes from './components/MeetingMinutes.vue';
import RelatedAccessories from './components/RelatedAccessories.vue';
import { declarationData, declarationDataId, updateDeclarationData } from './keys';
import Api from '/@/api';
import { CreateAndEditDrawer } from '/@/views/pms/projectApplication/components';
import { setTitleByRootTabsKey } from '/@/utils';

const [registerCreateAndEdit, { openDrawer: openCreateAndEdit }] = useDrawer();
const route = useRoute();
// 申报数据id
const dataId: Ref<string> = ref(route.params.id as string);
provide(declarationDataId, readonly(dataId));
const projectData: Ref = ref();
provide(declarationData, projectData);
const powerData: Ref = ref(undefined);
provide('powerData', powerData);
const defaultActionId: Ref<string> = ref('xMXX');
const workflowActionRef: Ref = ref();
const workflowViewRef: Ref = ref();
const workflowProps = computed<WorkflowProps>(() => ({
  Api,
  businessData: {
    ...projectData.value,
    name: projectData.value.projectName,
  },
  afterEvent() {
    workflowViewRef.value?.init();
    getDetailData();
  },
}));
const loading: Ref<boolean> = ref(false);
// 显示发起流程按钮
const showWorkflowAdd = computed(() => workflowActionRef.value?.isAdd && projectData.value?.status === 101);
// 详情顶部数据
const layoutData = computed(() => ({
  name: projectData.value?.projectName,
  ownerName: projectData.value?.resUserName,
  dataStatus: projectData.value?.dataStatus,
  projectCode: projectData.value?.projectNumber,
}));
const menuData: ComputedRef<MenuItem[]> = computed(() => [
  isPower('PMS_XMSB_container_details_projectInfo', powerData.value) ? {
    id: 'xMXX',
    name: '项目信息',
  } : undefined,
  // isPower('PMS_XMSBXQ_container_02', powerData.value) ? {
  //   id: 'ySXX',
  //   name: '预算信息',
  // } : undefined,
  // isPower('PMS_XMSBXQ_container_03', powerData.value) ? {
  //   id: 'hYJY',
  //   name: '会议纪要',
  // } : undefined,
  // isPower('PMS_XMSBXQ_container_04', powerData.value) ? {
  //   id: 'xGFJ',
  //   name: '相关附件',
  // } : undefined,
  isPower('PMS_XMSB_container_details_process', powerData.value) ? {
    id: 'sPLC',
    name: '审批流程',
  } : undefined,
].filter((item) => item));

onMounted(() => {
  getDetailData();
});

// 获取详情数据
async function getDetailData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/projectDeclare').fetch({ pageCode: 'PMS0009' }, unref(dataId), 'GET');
    projectData.value = result || {};
    powerData.value = result.detailAuthList || [];
    setTitleByRootTabsKey(route?.query?.rootTabsKey as string, result.name);
  } finally {
    loading.value = false;
  }
}

provide(updateDeclarationData, getDetailData);

function menuChange(option: { id: string, index: number, item: MenuItem }): void {
  defaultActionId.value = option.id;
}

// 添加流程
function handleAddWorkflow() {
  workflowActionRef.value?.onAddTemplate({
    messageUrl: route.fullPath,
  });
}

// 获取权限数据

const actionsBtn: ITableActionItem[] = [
  {
    text: '编辑',
    isShow: computed(() => projectData.value?.status === 101 && isPower('PMS_XMSB_container_details_top_edit', powerData)),
    icon: 'sie-icon-bianji',
    onClick(record: any) {
      openCreateAndEdit(true, { id: projectData.value?.id });
    },
  },
  {
    text: '发起流程',
    icon: 'sie-icon-qidongliucheng',
    isShow: computed(() => showWorkflowAdd.value && isPower('PMS_XMSB_container_details_top_process', powerData)),
    onClick(record: any) {
      handleAddWorkflow();
    },

  },
];

</script>

<template>
  <Layout3
    v-get-power="{powerData:powerData}"
    :defaultActionId="defaultActionId"
    :projectData="layoutData"
    :menuData="menuData"
    :type="2"
    :onMenuChange="menuChange"
  >
    <template #header-right>
      <BasicTableAction
        :actions="actionsBtn"
        type="button"
      />

      <!--      <BasicButton-->
      <!--        v-if="projectData?.status===101"-->
      <!--        v-is-power="['PMS_XMSBXQ_container_06_button_01']"-->
      <!--        icon="sie-icon-bianji"-->
      <!--        type="primary"-->
      <!--        @click="openCreateAndEdit(true,{id:projectData?.id})"-->
      <!--      >-->
      <!--        编辑-->
      <!--      </BasicButton>-->
      <!--      <BasicButton-->
      <!--        v-if="showWorkflowAdd"-->
      <!--        v-is-power="['PMS_XMSBXQ_container_06_button_02']"-->
      <!--        type="primary"-->
      <!--        icon="sie-icon-qidongliucheng"-->
      <!--        @click="handleAddWorkflow"-->
      <!--      >-->
      <!--        发起流程-->
      <!--      </BasicButton>-->
    </template>
    <template #footer>
      <WorkflowAction
        v-if="projectData?.id"
        ref="workflowActionRef"
        :workflow-props="workflowProps"
      />
    </template>
    <div
      v-if="loading"
      class="w-full h-full flex flex-pc flex-ac"
    >
      <Spin />
    </div>
    <Layout3Content v-else>
      <!--项目信息-->
      <ProjectInfo v-if="defaultActionId==='xMXX'" />
      <!--预算信息设计中-->
      <div
        v-if="defaultActionId==='ySXX'"
        class="w-full h-full flex flex-ac flex-pc"
      >
        <Empty description="模块设计中..." />
      </div>
      <!--会议纪要-->
      <MeetingMinutes v-if="defaultActionId==='hYJY'" />
      <!--相关附件-->
      <RelatedAccessories v-if="defaultActionId==='xGFJ'" />
      <!--审批流程-->
      <WorkflowView
        v-if="defaultActionId==='sPLC'"
        ref="workflowViewRef"
        :workflow-props="workflowProps"
      />
    </Layout3Content>

    <!--编辑申报-->
    <CreateAndEditDrawer
      :onConfirmCallback="getDetailData"
      @register="registerCreateAndEdit"
    />
  </Layout3>
</template>

<style scoped lang="less">

</style>
