package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.entity.QuestionManagement;
import com.chinasie.orion.domain.vo.ImportExcelCheckResultVO;
import com.chinasie.orion.domain.vo.PlanDetailVo;
import com.chinasie.orion.domain.vo.PlanSearchDataVo;
import com.chinasie.orion.domain.vo.QuestionManagementVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/17/16:32
 * @description:
 */
public interface QuestionManagementService extends OrionBaseService<QuestionManagement> {

    /**
     * 新增问题
     *
     * @param questionManagementDTO
     * @return
     * @throws Exception
     */
    String saveQuestionManagement(QuestionManagementDTO questionManagementDTO) throws Exception;


    /**
     * 新增问题
     *
     * @param questionManagementList
     * @return
     * @throws Exception
     */
    List<QuestionManagementVO> createBatch(List<QuestionManagementDTO> questionManagementList) throws Exception;


    /**
     * 获取问题分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    Page<QuestionManagementVO> getQuestionManagementPage(Page<QuestionManagementDTO> pageRequest) throws Exception;

    /**
     * 获取问题详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    QuestionManagementVO getQuestionManagementDetail(String id, String pageCode) throws Exception;

    /**
     * 编辑问题
     *
     * @param questionManagementDTO
     * @return
     * @throws Exception
     */
    Boolean editQuestionManagement(QuestionManagementDTO questionManagementDTO) throws Exception;

    /**
     * 批量移除问题
     *
     * @param ids
     * @return
     * @throws Exception
     */
    Boolean removeQuestionManagement(List<String> ids) throws Exception;

    boolean deleteQuestionRelationRiskAndPlan(List<String> questionIdList) throws Exception;

    /**
     * 新增问题的关联任务
     *
     * @param relationToPlanDTO
     * @return
     * @throws Exception
     */
    Boolean relationToPlan(RelationToPlanDTO relationToPlanDTO) throws Exception;

    /**
     * 批量删除问题的关联计划
     *
     * @param relationToPlanDTO
     * @return
     */
    Boolean removeRelationToPlan(RelationToPlanDTO relationToPlanDTO) throws Exception;

    /**
     * 通过问题获取关联任务列表
     *
     * @param id
     * @return
     * @throws Exception
     */
    List<PlanDetailVo> getPlanManagementListByQuestion(String id, PlanQueryDTO planQueryDTO) throws Exception;

    /**
     * 通过任务获取关联问题列表
     *
     * @param planId
     * @return
     * @throws Exception
     */
    List<QuestionManagementVO> getQuestionManagementListByPlan(String planId, QuestionManagementQueryDTO questionManagementQueryDTO) throws Exception;

    /**
     * 通过风险获取关联问题列表
     *
     * @param riskId
     * @return
     * @throws Exception
     */
    List<QuestionManagementVO> getQuestionManagementListByRisk(String riskId, QuestionManagementQueryDTO questionManagementQueryDTO) throws Exception;

    /**
     * 搜索获取数据列表
     *
     * @param keywordDto
     * @return
     */
    PlanSearchDataVo searchList(KeywordDto keywordDto) throws Exception;

    /**
     * 设置各种名称
     *
     * <AUTHOR>
     * @date 2023/10/25 13:56
     */
    void setContent(List<QuestionManagementVO> questionManagementVOList) throws Exception;

    /**
     *  下载模板
     *
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response)throws Exception;


    /**
     *  导入校验
     *
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file,String projectId)throws Exception;


    /**
     *  确认导入
     *
     * * @param importId
     */
    Boolean importByExcel(String importId)throws Exception;

    /**
     *  取消导入
     *
     * * @param importId
     */
    Boolean importCancelByExcel(String importId)throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response)throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void  setEveryName(List<QuestionManagementVO> vos)throws Exception;


    /**
     * 关闭
     *
     * @param ids
     * @return
     * @throws Exception
     */
    Boolean close (List<String> ids) throws Exception;


    /**
     * 激活
     *
     * @param ids
     * @return
     * @throws Exception
     */
    Boolean open (List<String> ids) throws Exception;


    /**
     * 关联评审单
     *
     * @param questionId 问题ID
     * @param reviewFormId 评审单ID
     * @return
     * @throws Exception
     */
    Boolean relationToReviewForm (String questionId, String reviewFormId) throws Exception;

    /**
     * 删除关联评审单
     *
     * @param questionId  问题ID
     * @param reviewFormId 评审单ID
     * @return
     * @throws Exception
     */
    Boolean removeRelationToReviewForm (String questionId, String reviewFormId) throws Exception;


    /**
     * 根据问题ID校验是否入典型问题库
     *
     * @param questionIds 问题ID
     * @return
     * @throws Exception
     */
    Boolean isEnterLibrary (List<String> questionIds) throws Exception;

    /**
     * 问题转计划
     * @param id
     * @param projectSchemes
     * @return
     */
    Boolean questionChangePlan(String id, List<ProjectSchemeDTO> projectSchemes) throws Exception;
}
