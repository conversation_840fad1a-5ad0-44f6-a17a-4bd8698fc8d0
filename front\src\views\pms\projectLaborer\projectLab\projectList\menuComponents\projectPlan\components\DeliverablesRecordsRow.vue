<script setup lang="ts">
import {
  BasicForm, FormSchema, useForm,
} from 'lyra-component-vue3';
import Api from '/@/api';
import {
  computed, onMounted, ref, Ref, unref,
} from 'vue';

const props = defineProps<{
  record: any
}>();

const namesList = ref([]);

const schemas: FormSchema[] = [
  {
    field: 'name',
    label: '名称',
    componentProps: {
      allowClear: true,
      placeholder: '请输入名称',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'principalId',
    component: 'Select',
    label: '责任人',
    required: true,
    componentProps: {
      options: computed(() => namesList.value),
      fieldNames: {
        key: 'id',
        value: 'id',
        label: 'name',
      },
    },
  },
  {
    field: 'predictDeliverTime',
    component: 'DatePicker',
    label: '计划交付时间',
    required: true,
    componentProps: {
      placeholder: '请选择计划交付时间',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    field: 'remark',
    component: 'InputTextArea',
    label: '描述',
    colProps: {
      span: 24,
    },
    componentProps: {
      placeholder: '请输入内容',
      maxlength: 500,
      row: 5,
      style: { height: '130px' },
      showCount: true,
    },
  },
];

const [register, { validate, setFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  getProjectRole(); // 负责人
});

function getProjectRole() {
  const url = `project-role-user/getListByName/${unref(props.record?.projectId)}?name=`;
  new Api('/pms').fetch('', url, 'POST').then((res) => {
    namesList.value = res;
  });
}

defineExpose({
  async onSubmit() {
    const formValues = await validate();
    return new Promise((resolve, reject) => {
      new Api('/pms/deliverable').fetch({
        ...formValues,
        id: props?.record?.id,
        planId: props?.record.id,
        projectId: props?.record?.projectId,
      }, '', 'POST').then(() => {
        resolve('');
      }).catch((e) => {
        reject(e);
      });
    });
  },
});

</script>

<template>
  <BasicForm
    @register="register"
  />
</template>

<style scoped lang="less">

</style>
