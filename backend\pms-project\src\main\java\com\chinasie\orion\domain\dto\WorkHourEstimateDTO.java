package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;
import java.util.List;

/**
 * WorkHourEstimate Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-14 16:23:45
 */
@ApiModel(value = "WorkHourEstimateDTO对象", description = "工时预估")
@Data
public class WorkHourEstimateDTO extends ObjectDTO implements Serializable{

    /**
     * 单据编号
     */
    @ApiModelProperty(value = "单据编号")
    private String number;

    /**
     * 成员id
     */
    @ApiModelProperty(value = "成员id")
    private String memberId;

    /**
     * 成员名称
     */
    @ApiModelProperty(value = "成员名称")
    private String memberName;

    /**
     * 工时时长
     */
    @ApiModelProperty(value = "工时时长")
    private Integer workHour;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    @NotNull(message = "开始日期不能为空")
    private Date startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    @NotNull(message = "结束日期不能为空")
    private Date endDate;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @NotBlank(message = "项目id不能为空")
    private String projectId;

    /**
     * 工时预估明细
     */
    @ApiModelProperty(value = "工时预估明细")
    @NotNull(message = "工时明细不能为空")
    @Valid
    private List<WorkHourEstimateDetailDTO> detailList;
}
