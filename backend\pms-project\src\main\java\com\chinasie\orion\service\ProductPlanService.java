package com.chinasie.orion.service;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.ProductPlanDTO;
import com.chinasie.orion.domain.entity.ProductPlan;
import com.chinasie.orion.domain.vo.ProductPlanVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

/**
 * <p>
 * ProductPlan 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 10:51:24
 */
public interface ProductPlanService  extends OrionBaseService<ProductPlan> {


        /**
         *  详情
         *
         * * @param id
         */
        ProductPlanVO detail(String id, String pageCode)throws Exception;

        /**
         *  新增
         *
         * * @param productPlanDTO
         */
        String create(ProductPlanDTO productPlanDTO)throws Exception;

        /**
         *  编辑
         *
         * * @param productPlanDTO
         */
        Boolean edit(ProductPlanDTO productPlanDTO)throws Exception;


        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;

        /**
         *  分页
         *
         * * @param pageRequest
         * 
         */
        Page<ProductPlanVO> pages(Page<ProductPlanDTO> pageRequest)throws Exception;

        /**
            *  设置各种名称
            *
            * * @param searchConditions
            * * @param response
            */
        void  setEveryName(List<ProductPlanVO> vos)throws Exception;


        public List<ProductPlanVO> getList( String approvalId) throws Exception;
}
