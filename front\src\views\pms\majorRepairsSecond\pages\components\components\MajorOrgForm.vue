<script lang="ts" setup>
import { h, ref } from 'vue';
import { OrionTable } from 'lyra-component-vue3';
import Api from '/@/api';
import dayjs from 'dayjs';
import ShowOrEdit from '/@/views/pms/majorRepairsSecond/pages/components/components/ShowOrEdit.vue';

const props = defineProps({
  record: {
    type: Object,
    default: () => ({}),
  },
  detailsData: {
    type: Object,
    default: () => ({}),
  },
  jobImplDownEnum: {
    type: String,
    default: '',
  },
  radioType: {
    type: Object,
    default: () => ({}),
  },
  brick: {
    type: String,
    default: '',
  },
});

const tableRef = ref(null);

// 提取 API_URLS 到函数外部
const API_URLS = {
  PREPARATION: '/pms/relationOrgToJob/prepare/down/list',
  IMPLEMENTATION: '/pms/relationOrgToJob/impl/down/list',
};

// 开工报备公共表头配置
const commonChildrenColumns = (dateString: string) => [
  {
    title: '上午',
    width: 65,
    dataIndex: [
      'beforeAndAfterFourDayMap',
      dateString,
      'dateSelectedMap',
      'MORNING',
    ],
    customRender({ text, record }) {
      return h(ShowOrEdit, {
        record,
        text,
        editType: 'checkTag',
        field: 'MORNING',
        dateString,
        editPermission: record?.roleList && record?.roleList.includes('WRITE'),
        apiType: 'brickSub',
        repairRound: props?.detailsData?.repairRound,
        baseCode: props?.detailsData?.baseCode,
        brick: props?.brick,
        async onUpdateValue(resolve) {
          await updateTable();
          resolve('');
        },
      });
    },
  },
  {
    title: '下午',
    width: 65,
    dataIndex: [
      'beforeAndAfterFourDayMap',
      dateString,
      'dateSelectedMap',
      'AFTERNOON',
    ],
    customRender({ text, record }) {
      return h(ShowOrEdit, {
        record,
        text,
        editType: 'checkTag',
        field: 'AFTERNOON',
        dateString,
        editPermission: record?.roleList && record?.roleList.includes('WRITE'),
        apiType: 'brickSub',
        repairRound: props?.detailsData?.repairRound,
        baseCode: props?.detailsData?.baseCode,
        brick: props?.brick,
        async onUpdateValue(resolve) {
          await updateTable();
          resolve('');
        },
      });
    },
  },
  {
    title: '夜间',
    width: 65,
    dataIndex: [
      'beforeAndAfterFourDayMap',
      dateString,
      'dateSelectedMap',
      'NIGHT',
    ],
    customRender({ text, record }) {
      return h(ShowOrEdit, {
        record,
        text,
        editType: 'checkTag',
        field: 'NIGHT',
        dateString,
        editPermission: record?.roleList && record?.roleList.includes('WRITE'),
        apiType: 'brickSub',
        repairRound: props?.detailsData?.repairRound,
        baseCode: props?.detailsData?.baseCode,
        brick: props?.brick,
        async onUpdateValue(resolve) {
          await updateTable();
          resolve('');
        },
      });
    },
  },
];

const options = {
  deleteToolButton: 'add|delete|enable|disable',
  rowSelection: false,
  showSmallSearch: false,
  showIndexColumn: false,
  isFilter2: false,
  isFullScreen: false,
  showTableSetting: false,
  pagination: false,
  bordered: true,
  maxHeight: 500,
  api: () => {
    const radioType = props?.radioType?.value || '';
    const url = radioType === 'preparation' ? API_URLS.PREPARATION : API_URLS.IMPLEMENTATION;

    const buildParams = (enumKey) => ({
      levelType: props?.record?.level,
      majorRepairOrg: props?.record?.data?.id,
      [enumKey]: props?.jobImplDownEnum,
      repairRound: props?.detailsData?.repairRound,
      baseCode: props?.detailsData?.baseCode,
    });

    const params2 = radioType === 'preparation'
      ? buildParams('jobPrepDownEnum')
      : buildParams('jobImplDownEnum');

    return new Api(url).fetch({
      ...params2,
    }, '', 'POST')
      .then((res) => res || [])
      .catch(() => []);
  },
  columns: [
    {
      title: '作业名称',
      dataIndex: ['jobName'],
      minWidth: 260,
    },
    {
      title: '工单号',
      dataIndex: ['jobNumber'],
      minWidth: 120,
    },
    {
      title: '状态',
      dataIndex: ['phase'],
      width: 80,
    },
    {
      title: '责任人',
      minWidth: 130,
      customHeaderCell() {
        return {
          class: 'required-cell',
        };
      },
      dataIndex: 'rspUserName',
      customRender({ text, record }) {
        return h(ShowOrEdit, {
          record,
          text,
          editType: 'user',
          apiType: 'brickSub',
          editPermission: record?.roleList.includes('WRITE'),
          brick: props?.brick,
          class: !record?.rspUserName ? 'yellow' : '',
          async onUpdateValue(resolve) {
            await updateTable();
            resolve('');
          },
        });
      },
    },
    {
      title: '重大项目（指挥部设置）',
      dataIndex: ['isMajorProject'],
      minWidth: 180,
      // customHeaderCell() {
      //   return {
      //     class: 'required-cell',
      //   };
      // },
      customRender({ text, record }) {
        return h(ShowOrEdit, {
          record,
          text,
          editType: 'checkTag',
          field: 'isMajorProject',
          apiType: 'brickSub',
          repairRound: props?.detailsData?.repairRound,
          baseCode: props?.detailsData?.baseCode,
          editPermission: record?.roleList.includes('WRITE'),
          brick: props?.brick,
          async onUpdateValue(resolve) {
            await updateTable();
            resolve('');
          },
        });
      },
    },
    {
      title: '计划开始日期',
      dataIndex: ['beginTime'],
      minWidth: 130,
      customHeaderCell() {
        return {
          class: 'required-cell',
        };
      },
      customRender({ text, record }) {
        return h(ShowOrEdit, {
          record,
          text,
          editType: 'date',
          field: 'beginTime',
          apiType: 'brickSub',
          repairRound: props?.detailsData?.repairRound,
          baseCode: props?.detailsData?.baseCode,
          brick: props?.brick,
          class: !record?.beginTime ? 'yellow' : '',
          editPermission: record?.roleList.includes('WRITE'),
          componentProps: {
            disabledDate(currentDate) {
              if (record?.endTime) {
                return currentDate.valueOf() > dayjs(record?.endTime).valueOf();
              }
              return false;
            },
          },
          async onUpdateValue(resolve) {
            await updateTable();
            resolve('');
          },
        });
      },
    },
    {
      title: '计划结束日期',
      dataIndex: ['endTime'],
      minWidth: 130,
    },
    {
      title: '计划工期',
      dataIndex: ['workDuration'],
      minWidth: 100,
      customHeaderCell() {
        return {
          class: 'required-cell',
        };
      },
      customRender({ text, record }) {
        return h(ShowOrEdit, {
          record,
          text,
          editType: 'number',
          field: 'workDuration',
          editPermission: record?.roleList.includes('WRITE'),
          apiType: 'brickSub',
          repairRound: props?.detailsData?.repairRound,
          baseCode: props?.detailsData?.baseCode,
          brick: props?.brick,
          class: !record?.workDuration ? 'yellow' : '',
          async onUpdateValue(resolve) {
            await updateTable();
            resolve('');
          },
        });
      },
    },
    {
      title: '实际开始日期',
      dataIndex: ['actualBeginTime'],
      minWidth: 130,
      // customHeaderCell() {
      //   return {
      //     class: 'required-cell',
      //   };
      // },
      customRender({ text, record }) {
        return h(ShowOrEdit, {
          record,
          text,
          editType: 'date',
          field: 'actualBeginTime',
          editPermission: false,
          apiType: 'brickSub',
          repairRound: props?.detailsData?.repairRound,
          baseCode: props?.detailsData?.baseCode,
          brick: props?.brick,
          async onUpdateValue(resolve) {
            await updateTable();
            resolve('');
          },
        });
      },
    },
    {
      title: '实际结束日期',
      dataIndex: ['actualEndTime'],
      minWidth: 130,
      // customHeaderCell() {
      //   return {
      //     class: 'required-cell',
      //   };
      // },
      customRender({ text, record }) {
        return h(ShowOrEdit, {
          record,
          text,
          editType: 'date',
          field: 'actualEndTime',
          editPermission: false,
          apiType: 'brickSub',
          repairRound: props?.detailsData?.repairRound,
          baseCode: props?.detailsData?.baseCode,
          brick: props?.brick,
          async onUpdateValue(resolve) {
            await updateTable();
            resolve('');
          },
        });
      },
    },
    {
      title: '开工报备',
      customHeaderCell() {
        return {
          class: 'center',
        };
      },
      children: [
        {
          title: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
          children: commonChildrenColumns(dayjs().subtract(1, 'day').format('YYYY-MM-DD')),
        },
        {
          title: dayjs().format('YYYY-MM-DD'),
          children: commonChildrenColumns(dayjs().format('YYYY-MM-DD')),
        },
        {
          title: dayjs().add(1, 'day').format('YYYY-MM-DD'),
          children: commonChildrenColumns(dayjs().add(1, 'day').format('YYYY-MM-DD')),
        },
        {
          title: dayjs().add(2, 'day').format('YYYY-MM-DD'),
          children: commonChildrenColumns(dayjs().add(2, 'day').format('YYYY-MM-DD')),
        },
      ],
    },
    {
      title: '首次执行',
      dataIndex: ['firstExecuteName'],
      minWidth: 130,
      customHeaderCell(record) {
        return {
          class: 'required-cell',
        };
      },
      customRender({ text, record }) {
        return h(ShowOrEdit, {
          record,
          text,
          editType: 'dict',
          field: 'firstExecute',
          dictNumber: 'pms_first_execute',
          apiType: 'brickSub',
          repairRound: props?.detailsData?.repairRound,
          baseCode: props?.detailsData?.baseCode,
          editPermission: record?.roleList.includes('WRITE'),
          brick: props?.brick,
          class: !record?.firstExecuteName ? 'yellow' : '',
          async onUpdateValue(resolve) {
            await updateTable();
            resolve('');
          },
        });
      },
    },
    {
      title: '防异物等级',
      dataIndex: ['antiForfeignLevelName'],
      minWidth: 100,
      customHeaderCell() {
        return {
          class: 'required-cell',
        };
      },
      customRender({ text, record }) {
        return h(ShowOrEdit, {
          record,
          text,
          editType: 'dict',
          field: 'antiForfeignLevel',
          dictNumber: 'pms_dust_protection_level',
          apiType: 'brickSub',
          repairRound: props?.detailsData?.repairRound,
          baseCode: props?.detailsData?.baseCode,
          editPermission: record?.roleList.includes('WRITE'),
          brick: props?.brick,
          class: !record?.antiForfeignLevelName ? 'yellow' : '',
          async onUpdateValue(resolve) {
            await updateTable();
            resolve('');
          },
        });
      },
    },
    {
      title: '高风险',
      dataIndex: ['isHighRisk'],
      minWidth: 100,
      customHeaderCell() {
        return {
          class: 'required-cell',
        };
      },
      customRender({ text, record }) {
        const flag = record?.isHighRisk === null || record?.isHighRisk === '';
        return h(ShowOrEdit, {
          record,
          text,
          editType: 'flagSelect',
          field: 'isHighRisk',
          apiType: 'brickSub',
          repairRound: props?.detailsData?.repairRound,
          baseCode: props?.detailsData?.baseCode,
          editPermission: record?.roleList.includes('WRITE'),
          brick: props?.brick,
          class: flag ? 'yellow' : '',
          async onUpdateValue(resolve) {
            await updateTable();
            resolve('');
          },
        });
      },
    },
    {
      title: '高风险等级',
      dataIndex: ['heightRiskLevelName'],
      minWidth: 100,
    },
    {
      title: '工作包审查状态',
      dataIndex: ['workPackageStatus'],
      minWidth: 120,
    },
    {
      title: '监督人员',
      dataIndex: ['supervisoryStaffName'],
      minWidth: 100,
    },
    {
      title: '管理人员',
      dataIndex: ['managePersonName'],
      minWidth: 100,
    },
    {
      title: '协同配合专业',
      dataIndex: ['collaborationNames'],
      minWidth: 110,
    },
  ],
};

// 更新表格
function updateTable() {
  tableRef.value?.reload();
}

</script>
<template>
  <OrionTable
    ref="tableRef"
    :options="options"
  />
</template>
<style lang="less" scoped>
:deep(.clamp-hover) {
  display: inline-block;
  width: 100%;
  min-height: 22px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;

  &:hover {
    color: #1890ff;
  }
}
</style>
<style lang="less">
.surely-table-cell-group.required-cell.center {
  .header-column-wrap {
    text-align: center;
  }
}

.surely-table-cell.required-cell {
  &::after {
    position: absolute;
    content: '*';
    top: 50%;
    left: 2px;
    transform: translateY(-50%);
    color: red;
  }

  &.center::after {
    top: 17%;
    left: calc(50% - 35px);
  }
}
</style>
<style scoped lang="less">
:deep(.basic-import-select-clear){
  top: 0;
}
.ant-table-cell.required-cell {
  &::after {
    position: absolute;
    content: '*';
    top: 50%;
    left: 2px;
    transform: translateY(-50%);
    color: red;
  }

  &.center::after {
    left: calc(50% - 35px);
  }
}
:deep(.permission-write) {
  position: absolute;
  top: 0;
  right: 0;
  content: '';
  width: 18px;
  height: 18px;
  background-color: #78b7e3;
  clip-path: polygon(0 0, 100% 0, 100% 100%);
}

// 操作栏样式
:deep(.ant-space.ant-space-horizontal){
  gap: 0!important;
  width: 100%;
}
:deep(.ant-space-item){
  width: 100%;
}
:deep(.ant-picker) {
  padding: 8px 11px;
}
:deep(.basic-import-select-wrap){
  .ant-input {
    height: 40px;
  }
  .ant-input-search-button {
    height: 40px;
  }
}
:deep(.ant-select-selector) {
  height: 40px!important;
  .ant-select-selection-search{
    .ant-select-selection-search-input{
      height: 40px;
    }
  }
  .ant-select-selection-placeholder{
    line-height: 40px;
  }
}
:deep(.ant-select-selection-search) {
  height: 40px!important;
  .ant-select-selection-search-input {
    height: 40px;
  }
}
:deep(.ant-select-single .ant-select-selector .ant-select-selection-item) {
  line-height: 40px;
}
:deep(.clamp-hover){
  display: inline-block;
  width: 100%;
  min-height: 22px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;
  &:hover{
    color: #1890ff;
  }
}

</style>
