package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.domain.dto.ProjectMajorDeedDTO;
import com.chinasie.orion.domain.entity.ProjectMajorDeed;
import com.chinasie.orion.domain.vo.ProjectMajorDeedVO;
import com.chinasie.orion.repository.ProjectMajorDeedMapper;
import com.chinasie.orion.service.ProjectMajorDeedService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import java.lang.String;
import java.util.*;
import java.util.stream.Collectors;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * ProjectMajorDeed 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-27 19:32:36
 */
@Service
@Slf4j
public class ProjectMajorDeedServiceImpl extends  OrionBaseServiceImpl<ProjectMajorDeedMapper, ProjectMajorDeed>   implements ProjectMajorDeedService {


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  ProjectMajorDeedVO detail(String id,String pageCode) throws Exception {
        ProjectMajorDeed projectMajorDeed =this.getById(id);
        ProjectMajorDeedVO result = BeanCopyUtils.convertTo(projectMajorDeed, ProjectMajorDeedVO::new);


        return result;
    }

    /**
     * 新增编辑删除主要事迹
     * @param projectMajorDeedDTOList
     * @param projectId
     * @return
     * @throws Exception
     */
    @Override
    public Boolean saveOrRemove(List<ProjectMajorDeedDTO> projectMajorDeedDTOList, String projectId) throws Exception {
        List<String> existIdList = this.list(new LambdaQueryWrapperX<>(ProjectMajorDeed.class)
                .select(ProjectMajorDeed::getId)
                .eq(ProjectMajorDeed::getProjectId, projectId))
                .stream().map(ProjectMajorDeed::getId).collect(Collectors.toList());
        List<ProjectMajorDeed> saveList = projectMajorDeedDTOList.stream()
                .filter(f -> StrUtil.isBlank(f.getId()))
                .map(m -> {
                    ProjectMajorDeed projectMajorDeed = new ProjectMajorDeed();
                    projectMajorDeed.setProjectId(projectId);
                    projectMajorDeed.setMajorDeed(m.getMajorDeed());
                    if (m.getBeginEndTime().size() > 1) {
                        projectMajorDeed.setBeginTime(m.getBeginEndTime().get(0));
                        projectMajorDeed.setEndTime(m.getBeginEndTime().get(1));
                        projectMajorDeed.setBeginEndTime(String.format("%s~%s", DateUtil.formatDate(m.getBeginEndTime().get(0)), DateUtil.formatDate(m.getBeginEndTime().get(1))));
                    }
                    return projectMajorDeed;
                }).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(saveList)) {
            this.saveBatch(saveList);
        }
        List<ProjectMajorDeed> updateList = projectMajorDeedDTOList.stream()
                .filter(f -> StrUtil.isNotBlank(f.getId()))
                .map(m -> {
                    ProjectMajorDeed projectMajorDeed = new ProjectMajorDeed();
                    projectMajorDeed.setId(m.getId());
                    projectMajorDeed.setMajorDeed(m.getMajorDeed());
                    if (m.getBeginEndTime().size() > 1) {
                        projectMajorDeed.setBeginTime(m.getBeginEndTime().get(0));
                        projectMajorDeed.setEndTime(m.getBeginEndTime().get(1));
                        projectMajorDeed.setBeginEndTime(String.format("%s~%s", DateUtil.formatDate(m.getBeginEndTime().get(0)), DateUtil.formatDate(m.getBeginEndTime().get(1))));
                    }
                    return projectMajorDeed;
                }).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(updateList)) {
            this.updateBatchById(updateList);
        }
        List<String> updateIdList = projectMajorDeedDTOList.stream()
                .filter(f -> StrUtil.isNotBlank(f.getId()))
                .map(ProjectMajorDeedDTO::getId).collect(Collectors.toList());;
        List<String> deleteIdList = existIdList.stream().filter(f -> !updateIdList.contains(f)).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(deleteIdList)) {
            this.removeByIds(deleteIdList);
        }
        return true;
    }

    /**
     * 获取主要事迹列表
     * @param projectId
     * @return
     * @throws Exception
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public List<ProjectMajorDeedVO> getList(String projectId) throws Exception {
        List<ProjectMajorDeed> list = this.list(new LambdaQueryWrapperX<>(ProjectMajorDeed.class)
                .eq(ProjectMajorDeed::getProjectId, projectId)
                .orderByDesc(ProjectMajorDeed::getCreateTime));
        return list.stream().map(m -> {
            ProjectMajorDeedVO projectMajorDeedVO = new ProjectMajorDeedVO();
            projectMajorDeedVO.setId(m.getId());
            projectMajorDeedVO.setMajorDeed(m.getMajorDeed());
            if (m.getBeginTime() != null) {
                projectMajorDeedVO.setBeginEndTime(Arrays.asList(DateUtil.formatDate(m.getBeginTime()), DateUtil.formatDate(m.getEndTime())));
            }
            return projectMajorDeedVO;
        }).collect(Collectors.toList());
    }

}
