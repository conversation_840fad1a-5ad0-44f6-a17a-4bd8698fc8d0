<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
  />
</template>

<script setup lang="ts">
import {
  computed, reactive, watch, ref,
} from 'vue';
import {
  BasicButton, Layout, OrionTable, BasicTableAction,
} from 'lyra-component-vue3';
import {
  Button, Radio,
} from 'ant-design-vue';
import Api from '/@/api';
import { AlignLeftOutlined, AppstoreOutlined } from '@ant-design/icons-vue';
import { useRoute } from 'vue-router';
import { getActionsList, getColumns } from './config/index';

const route = useRoute();

const AButton = Button;
const ARadioGroup = Radio.Group;
const ARadioButton = Radio.Button;
const addOrEditRef = ref(null);
const tableRef = ref(null);
const state = reactive({
  tableRef,
  addOrEditRef,
});
const tableOptions = reactive({
  rowSelection: false,
  canResize: false,
  minHeight: 100,
  maxHeight: 300,
  isTableHeader: false,
  api: (params) => new Api(`/pms/projectDaily-statement/${route.query?.curId}`).fetch(params, '', 'GET').then((res) => res.nexDayVOList),
  pagination: false,
  columns: getColumns(),
});

function handle(type) {
  switch (type) {
    case 'add':
      addOrEditRef.value.openDrawer({ action: 'add' });
      break;
    case 'edit':
      addOrEditRef.value.openDrawer({ action: 'edit' });
      break;
  }
}
</script>

<style scoped lang="less"></style>
