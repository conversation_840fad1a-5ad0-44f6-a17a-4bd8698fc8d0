spring:
  profiles:
    active: prd_demo  # local 用于本地联调; dev 开发验证环境;  test 测试环境;
  #    active: prd_dev_zgh_out

  application:
    name: pms
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
#    service-registry:
#      auto-registration:
#        enabled: false
    nacos:
      username: devops # 账号
      password: devops123 # 密码
      config:
        file-extension: yaml # 配置文件后缀
        server-addr: 183.136.206.207:18848 # 配置中心地址
        namespace: ${spring.profiles.active} # 命名空间
        # 用于共享的配置文件
        shared-configs:
          - data-id: _orion-spring.yaml # 配置文件名
            group: DEFAULT_GROUP # 配置分组
            refresh: true # 是否动态刷新
          - data-id: _orion-register.yaml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: _orion-redis.yaml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: _orion-datasource_beta.yaml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: _orion-rabbitmq.yaml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: _orion-j2cache.yaml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: _orion-job.yaml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: _orion-search-client.yaml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: _orion-swagger.yaml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: _orion-file.yaml
            group: DEFAULT_GROUP
            refresh: true

orion:
  license:
    verify:
      subject: 舍得酒业 #
      publicAlias: sheDeCert
      publicKeysStorePath: D://orion/sheDeCerts.keystore #放到服务器上某个指定目录（全路径）主要位置要隐蔽一些
      storePass: "ORION@LICENSE#2024"
      licensePath: D://orion/license.lic  #放到服务器上某个指定目录（全路径） 主要位置要隐蔽一些
