package com.chinasie.orion.domain.entity.approval;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;
import java.math.BigDecimal;

/**
 * ProjectApprovalEstimateLaborFees Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-07 10:45:22
 */
@TableName(value = "pms_project_approval_estimate_labor_fee")
@ApiModel(value = "ProjectApprovalEstimateLaborFeesEntity对象", description = "概算工资及劳务费")
@Data
public class ProjectApprovalEstimateLaborFee extends ObjectEntity implements Serializable{

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    @TableField(value = "user_id")
    private String userId;


    /**
     * 职级
     */
    @ApiModelProperty(value = "职级")
    @TableField(value = "job_position_name")
    private String jobPositionName;

    /**
     * 职级费率
     */
    @ApiModelProperty(value = "职级费率")
    @TableField(value = "job_position_rate")
    private String jobPositionRate;

    /**
     * 岗位名称
     */
    @ApiModelProperty(value = "岗位名称")
    @TableField(value = "job_name")
    private String jobName;

    /**
     * 职级id
     */
    @ApiModelProperty(value = "职级id")
    @TableField(value = "job_position_id")
    private String jobPositionId;

    /**
     * 岗位id
     */
    @ApiModelProperty(value = "岗位id")
    @TableField(value = "job_id")
    private String jobId;

    /**
     * 人员需求数量
     */
    @ApiModelProperty(value = "人员需求数量")
    @TableField(value = "required_num")
    private BigDecimal requiredNum;

    /**
     * 人天
     */
    @ApiModelProperty(value = "人天")
    @TableField(value = "people_days")
    private BigDecimal peopleDays;

    /**
     * 工资费用
     */
    @ApiModelProperty(value = "工资费用")
    @TableField(value = "labor_fee")
    private BigDecimal laborFee;

    /**
     * 项目立项id
     */
    @ApiModelProperty(value = "项目立项id")
    @TableField(value = "project_approval_id")
    private String projectApprovalId;


    /**
     * 员工名称
     */
    @ApiModelProperty(value = "员工名称")
    @TableField(value = "name")
    private String name;
}
