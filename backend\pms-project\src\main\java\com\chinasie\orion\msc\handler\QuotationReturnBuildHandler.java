package com.chinasie.orion.msc.handler;

import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.dict.MessageNodeNumberDict;
import com.chinasie.orion.domain.vo.QuotationReturnVO;
import com.chinasie.orion.msc.api.MscBuildHandler;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class QuotationReturnBuildHandler implements Msc<PERSON><PERSON>Handler<QuotationReturnVO> {

    @Override
    public SendMessageDTO buildMsc(QuotationReturnVO quotationReturnVO, Object... objects) {
        Map<String, Object> messageMap = new HashMap<>();
        messageMap.put("$name$",quotationReturnVO.getName());
        messageMap.put("$status$", "1".equals(quotationReturnVO.getStatus())?"已中标":"未中标");

        ArrayList<String> idList = new ArrayList<>();
        idList.add(quotationReturnVO.getCustBsPerson());
        idList.add(quotationReturnVO.getCustTecPerson());
        SendMessageDTO sendMessage = SendMessageDTO.builder()
                .titleMap(messageMap)
                .messageMap(messageMap)
                .messageUrl("/pas/QuotationManagementDetails/"+quotationReturnVO.getId())
                .messageUrlName("报价详情")
                .recipientIdList(idList)
                .senderId(CurrentUserHelper.getCurrentUserId())
                .senderTime(new Date())
                .businessId(quotationReturnVO.getId())
                .platformId(quotationReturnVO.getPlatformId())
                .orgId(quotationReturnVO.getOrgId())
                .build();

        return sendMessage;
    }

    @Override
    public String support() {
        return MessageNodeNumberDict.QUOTATION_RETURN_NOTIFICATION;
    }
}
