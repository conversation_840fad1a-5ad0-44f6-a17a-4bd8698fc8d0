ALTER TABLE `ncf_form_contract_change`
    ADD COLUMN `procurement_group_id` varchar(256) NULL COMMENT '采购组Id' AFTER `contract_number`,
ADD COLUMN `procurement_group_name` varchar(256) NULL COMMENT '采购组名称' AFTER `procurement_group_id`,
ADD COLUMN `business_rsp_user_id` varchar(256) NULL COMMENT '商务负责人ID' AFTER `procurement_group_name`,
ADD COLUMN `business_rsp_user_name` varchar(255) NULL COMMENT '商务负责人名称' AFTER `business_rsp_user_id`;



ALTER TABLE `ncf_form_contract_claim`
    ADD COLUMN `procurement_group_id` varchar(256) NULL COMMENT '采购组Id' AFTER `contract_number`,
ADD COLUMN `procurement_group_name` varchar(256) NULL COMMENT '采购组名称' AFTER `procurement_group_id`,
ADD COLUMN `business_rsp_user_id` varchar(256) NULL COMMENT '商务负责人ID' AFTER `procurement_group_name`,
ADD COLUMN `business_rsp_user_name` varchar(255) NULL COMMENT '商务负责人名称' AFTER `business_rsp_user_id`;

ALTER TABLE `ncf_form_contract_termination`
    ADD COLUMN `procurement_group_id` varchar(256) NULL COMMENT '采购组Id' AFTER `contract_number`,
ADD COLUMN `procurement_group_name` varchar(256) NULL COMMENT '采购组名称' AFTER `procurement_group_id`,
ADD COLUMN `business_rsp_user_id` varchar(256) NULL COMMENT '商务负责人ID' AFTER `procurement_group_name`,
ADD COLUMN `business_rsp_user_name` varchar(255) NULL COMMENT '商务负责人名称' AFTER `business_rsp_user_id`;