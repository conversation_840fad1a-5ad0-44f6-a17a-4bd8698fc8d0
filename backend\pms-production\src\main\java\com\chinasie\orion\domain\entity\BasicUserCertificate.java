package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/05/22:02
 * @description:
 */
@TableName(value = "pmsx_basic_user_certificate")
@ApiModel(value = "BasicUserCertificateEntity对象", description = "基础用户证书关系表")
@Data

public class BasicUserCertificate extends  ObjectEntity  implements Serializable{

    /**
     * 证书编号
     */
    @ApiModelProperty(value = "证书id")
    @TableField(value = "certificate_id")
    private String certificateId;

    /**
     * 用户编码
     */
    @ApiModelProperty(value = "用户编码")
    @TableField(value = "user_code")
    private String userCode;

    /**
     * 获取日期
     */
    @ApiModelProperty(value = "获取日期")
    @TableField(value = "obtain_date")
    private Date obtainDate;

    /**
     * 复审日期
     */
    @ApiModelProperty(value = "复审日期")
    @TableField(value = "review_date")
    private Date reviewDate;
    @ApiModelProperty(value = "发证机构")
    @TableField(value = "issuing_authority")
    private String issuingAuthority;

    @ApiModelProperty(value = "初次取证日期")
    @TableField(value = "initial_certification_date")
    private Date initialCertificationDate;

    @ApiModelProperty(value = "有效期至")
    @TableField(value = "valid_to_date")
    private Date validToDate;

    @ApiModelProperty(value = "证书编号")
    @TableField(value = "number")
    private String number;

    @ApiModelProperty(value = "证书级别")
    @TableField(value = "certificate_level")
    private String certificateLevel;
}
