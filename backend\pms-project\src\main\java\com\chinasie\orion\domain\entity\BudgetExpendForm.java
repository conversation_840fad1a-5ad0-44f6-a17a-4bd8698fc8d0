package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;
import java.lang.String;

/**
 * BudgetExpendForm Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:07
 */
@TableName(value = "pmsx_budget_expend_form")
@ApiModel(value = "BudgetExpendFormEntity对象", description = "预算支出表单")
@Data
public class BudgetExpendForm extends ObjectEntity implements Serializable {
    /**
     * 成本支出编码
     */
    @ApiModelProperty(value = "成本支出编码")
    @TableField(value = "number")
    private String number;

    /**
     * 科目名称
     */
    @ApiModelProperty(value = "科目名称")
    @TableField(value = "expense_account_name")
    private String expenseAccountName;

    /**
     * 科目编码
     */
    @ApiModelProperty(value = "科目编码")
    @TableField(value = "expense_account_number")
    private String expenseAccountNumber;

    /**
     * 科目Id
     */
    @ApiModelProperty(value = "科目Id")
    @TableField(value = "expense_account_id")
    private String expenseAccountId;

    /**
     * 成本中心id
     */
    @ApiModelProperty(value = "成本中心id")
    @TableField(value = "cost_center_id")
    private String costCenterId;

    /**
     * 发生时间
     */
    @ApiModelProperty(value = "发生时间")
    @TableField(value = "occurrence_time")
    private Date occurrenceTime;

    /**
     * 发生人
     */
    @ApiModelProperty(value = "发生人")
    @TableField(value = "occurrence_person")
    private String occurrencePerson;

    /**
     * 支出金额
     */
    @ApiModelProperty(value = "支出金额")
    @TableField(value = "expend_money")
    private BigDecimal expendMoney;

    @ApiModelProperty(value = "占/领用")
    @TableField(value = "occupation_receive")
    private String occupationReceive;
    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    @TableField(value = "project_id")
    private String projectId;

    @ApiModelProperty(value = "成本中心id")
    @TableField(exist = false)
    private String costCenterName;

}
