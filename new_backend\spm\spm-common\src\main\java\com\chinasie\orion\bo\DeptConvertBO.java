package com.chinasie.orion.bo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.base.api.domain.entity.DeptDO;
import com.chinasie.orion.base.api.repository.DeptDOMapper;
import com.chinasie.orion.base.api.service.DeptBaseApiService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.org.DeptBaseInfoVO;
import com.chinasie.orion.sdk.domain.vo.org.SimpleDeptVO;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.tenant.core.context.TenantContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 部门公共类转换
 **/
@Slf4j
@Component
public class DeptConvertBO {

    @Resource
    private DeptRedisHelper deptRedisHelperTemp;
    private static DeptRedisHelper deptRedisHelper;

    @Resource
    private DeptBaseApiService deptBaseApiServiceTemp;
    private static DeptBaseApiService deptBaseApiService;

    @Resource
    private DeptDOMapper deptDOMapperTemp;
    private static DeptDOMapper deptDOMapper;

    @PostConstruct
    public void init() {
        deptRedisHelper = deptRedisHelperTemp;
        deptBaseApiService = deptBaseApiServiceTemp;
        deptDOMapper = deptDOMapperTemp;
    }

    //根据id使用Redis获取用户名称
    public static String getNameByIdForRedis(String id) {
        return getDataByIdForRedis(id, true);
    }

    //根据id使用Redis获取用户编号
    public static String getDeptCodeByIdForRedis(String id) {
        return getDataByIdForRedis(id, false);
    }

    //根据id使用Redis获取用户数据
    public static String getDataByIdForRedis(String id, boolean flag) {
        if (StrUtil.isNotBlank(id)) {
            DeptVO dept = deptRedisHelper.getDeptById(id);
            if (ObjectUtil.isNotNull(dept))
                return flag ? dept.getName() : dept.getDeptCode();
        }
        return null;
    }

    //根据id使用Api获取用户名称
    public static String getNameByIdForApi(String id) {
        return getDataByIdForApi(id, true);
    }

    //根据id使用Api获取用户编号
    public static String getDeptCodeByIdForApi(String id) {
        return getDataByIdForApi(id, false);
    }

    //根据id使用Api获取用户数据
    public static String getDataByIdForApi(String id, boolean flag) {
        if (StrUtil.isNotBlank(id)) {
            DeptVO dept = deptBaseApiService.getDeptById(id);
            if (ObjectUtil.isNotNull(dept))
                return flag ? dept.getName() : dept.getDeptCode();
        }
        return null;
    }

    //根据id使用Mapper获取用户名称
    public static String getNameByIdForMapper(String id) {
        return getDataByIdForMapper(id, true);
    }

    //根据id使用Mapper获取用户编号
    public static String getDeptCodeByIdForMapper(String id) {
        return getDataByIdForMapper(id, false);
    }

    //根据id使用Mapper获取用户名称
    public static String getDataByIdForMapper(String id, boolean flag) {
        if (StrUtil.isNotBlank(id)) {
            DeptDO dept = deptDOMapper.selectById(id);
            if (ObjectUtil.isNotNull(dept))
                return flag ? dept.getName() : dept.getDeptCode();
        }
        return null;
    }

    //根据code使用Redis获取用户名称
    public static String getNameByCodeForRedis(String code) {
        return getDataByCodeForRedis(code, true);
    }

    //根据code使用Redis获取用户id
    public static String getIdByCodeForRedis(String code) {
        return getDataByCodeForRedis(code, false);
    }

    //根据code使用Redis获取用户数据
    public static String getDataByCodeForRedis(String code, boolean flag) {
        if (StrUtil.isNotBlank(code)) {
            DeptBaseInfoVO dept = deptRedisHelper.getDeptBaseInfoByDeptCode(TenantContextHolder.getTenantId(), code);
            if (ObjectUtil.isNotNull(dept))
                return flag ? dept.getName() : dept.getDeptCode();
        }
        return null;
    }

    //根据code使用Mapper获取用户名称
    public static String getNameByCodeForMapper(String code) {
        return getDataByCodeForMapper(code, true);
    }

    //根据code使用Mapper获取用户id
    public static String getIdByCodeForMapper(String code) {
        return getDataByCodeForMapper(code, false);
    }

    //根据code使用Mapper获取用户数据
    public static String getDataByCodeForMapper(String code, boolean flag) {
        if (StrUtil.isNotBlank(code)) {
            LambdaQueryWrapperX<DeptDO> lqw = new LambdaQueryWrapperX<>(DeptDO.class);
            lqw.select(DeptDO::getName);
            lqw.eq(DeptDO::getDeptCode, code);
            lqw.last("limit 1");
            DeptDO dept = deptDOMapper.selectOne(lqw);
            if (ObjectUtil.isNotNull(dept))
                return flag ? dept.getName() : dept.getId();
        }
        return null;
    }

    //根据id使用Redis获取用户名称（批量）
    public static Map<String, String> getId2NameByIdsForRedis(List<String> ids) {
        return getMapByIdsForRedis(ids, true);
    }

    //根据id使用Redis获取用户编号（批量）
    public static Map<String, String> getId2CodeByIdsForRedis(List<String> ids) {
        return getMapByIdsForRedis(ids, false);
    }

    //根据id使用Redis获取用户数据（批量）
    public static Map<String, String> getMapByIdsForRedis(List<String> ids, boolean flag) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        List<DeptVO> deptList = deptRedisHelper.getDeptByIds(ids);
        return generateMap(deptList,
                flag ? DeptVO::getId : DeptVO::getDeptCode,
                flag ? DeptVO::getName : DeptVO::getDeptCode);
    }

    //根据id使用Api获取用户名称（批量）
    public static Map<String, String> getId2NameByIdsForApi(List<String> ids) {
        return getMapByIdsForApi(ids, true);
    }

    //根据id使用Api获取用户编号（批量）
    public static Map<String, String> getId2CodeByIdsForApi(List<String> ids) {
        return getMapByIdsForApi(ids, false);
    }

    //根据id使用Api获取用户数据（批量）
    public static Map<String, String> getMapByIdsForApi(List<String> ids, boolean flag) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        List<DeptVO> deptList = deptBaseApiService.getDeptByIds(ids);
        return generateMap(deptList,
                flag ? DeptVO::getId : DeptVO::getDeptCode,
                flag ? DeptVO::getName : DeptVO::getDeptCode);
    }

    //根据id使用Mapper获取用户名称（批量）
    public static Map<String, String> getId2NameByIdsForMapper(List<String> ids) {
        return getMapByIdsForMapper(ids, true);
    }

    //根据id使用Mapper获取用户编号（批量）
    public static Map<String, String> getId2CodeByIdsForMapper(List<String> ids) {
        return getMapByIdsForMapper(ids, false);
    }

    //根据id使用Mapper获取用户数据（批量）
    public static Map<String, String> getMapByIdsForMapper(List<String> ids, boolean flag) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        List<DeptDO> deptList = deptDOMapper.selectBatchIds(ids);
        return generateMap(deptList,
                flag ? DeptDO::getId : DeptDO::getDeptCode,
                flag ? DeptDO::getName : DeptDO::getDeptCode);
    }

    //根据code使用Mapper获取用户名称（批量）
    public static Map<String, String> getDeptCode2NameByCodesForMapper(List<String> codeList) {
        return getMapByCodesForMapper(codeList, true);
    }

    //根据code使用Mapper获取用户编号（批量）
    public static Map<String, String> getDeptCode2IdByCodesForMapper(List<String> codeList) {
        return getMapByCodesForMapper(codeList, false);
    }

    //根据code使用Mapper获取用户数据（批量）
    public static Map<String, String> getMapByCodesForMapper(List<String> codeList, boolean flag) {
        if (CollUtil.isEmpty(codeList)) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapperX<DeptDO> lqw = new LambdaQueryWrapperX<>(DeptDO.class);
        lqw.select(DeptDO::getDeptCode, flag ? DeptDO::getName : DeptDO::getId);
        lqw.in(DeptDO::getDeptCode, codeList);
        List<DeptDO> deptList = deptDOMapper.selectList(lqw);
        return generateMap(deptList,
                DeptDO::getDeptCode,
                flag ? DeptDO::getName : DeptDO::getId);
    }

    //使用Redis获取所有的id-name用户数据
    public static Map<String, String> getId2NameAllForRedis() {
        return allForRedis(1);
    }

    //使用Redis获取所有的id-code用户数据
    public static Map<String, String> getId2CodeAllForRedis() {
        return allForRedis(2);
    }

    //使用Redis获取所有的code-name用户数据
    public static Map<String, String> getDeptCode2NameAllForRedis() {
        return allForRedis(3);
    }

    //使用Redis获取所有的code-id用户数据
    public static Map<String, String> getDeptCode2IdAllForRedis() {
        return allForRedis(4);
    }

    //根据code使用Redis获取用户数据（批量）
    public static Map<String, String> allForRedis(int type) {
        List<SimpleDeptVO> deptList = deptRedisHelper.getAllSimpleDept(TenantContextHolder.getTenantId());
        return getAllData(deptList, type,
                SimpleDeptVO::getId, SimpleDeptVO::getName,
                SimpleDeptVO::getDeptCode, SimpleDeptVO::getId);
    }

    //使用Api获取所有的id-name用户数据
    public static Map<String, String> getId2NameAllForApi() {
        return allForApi(1);
    }

    //使用Api获取所有的id-code用户数据
    public static Map<String, String> getId2CodeAllForApi() {
        return allForApi(2);
    }

    //使用Api获取所有的code-name用户数据
    public static Map<String, String> getDeptCode2NameAllForApi() {
        return allForApi(3);
    }

    //使用Api获取所有的code-id用户数据
    public static Map<String, String> getDeptCode2IdAllForApi() {
        return allForApi(4);
    }

    //根据code使用Api获取用户数据（批量）
    public static Map<String, String> allForApi(int type) {
        List<DeptVO> deptList = deptBaseApiService.getAllDeptByOrgId();
        return getAllData(deptList, type,
                DeptVO::getId, DeptVO::getName,
                DeptVO::getDeptCode, DeptVO::getId);
    }

    //使用Mapper获取所有的id-name用户数据
    public static Map<String, String> getId2NameAllForMapper() {
        return allForMapper(1);
    }

    //使用Mapper获取所有的id-code用户数据
    public static Map<String, String> getId2CodeAllForMapper() {
        return allForMapper(2);
    }

    //使用Mapper获取所有的code-name用户数据
    public static Map<String, String> getDeptCode2NameAllForMapper() {
        return allForMapper(3);
    }

    //使用Mapper获取所有的code-id用户数据
    public static Map<String, String> getDeptCode2IdAllForMapper() {
        return allForMapper(4);
    }

    //根据code使用Mapper获取用户数据（批量）
    public static Map<String, String> allForMapper(int type) {
        LambdaQueryWrapperX<DeptDO> lqw = new LambdaQueryWrapperX<>(DeptDO.class);
        if (type == 1 || type == 2) {
            lqw.select(DeptDO::getId, type == 1 ? DeptDO::getName : DeptDO::getDeptCode);
        } else {
            lqw.select(DeptDO::getDeptCode, type == 3 ? DeptDO::getName : DeptDO::getId);
        }
        List<DeptDO> deptList = deptDOMapper.selectList(lqw);
        return getAllData(deptList, type,
                DeptDO::getId, DeptDO::getName,
                DeptDO::getDeptCode, DeptDO::getId);
    }

    // 提取公共方法：根据类型和字段生成映射
    private static <T> Map<String, String> generateMap(List<T> list, Function<T, String> keyExtractor, Function<T, String> valueExtractor) {
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyMap(); // 返回空集合，避免返回 null
        }
        return list.stream()
                .collect(Collectors.toMap(keyExtractor, valueExtractor, (k1, k2) -> k1)); // 处理键冲突
    }

    // 提取公共方法：根据类型生成所有用户数据
    private static <T> Map<String, String> getAllData(List<T> list, int type, Function<T, String> keyExtractor1, Function<T, String> valueExtractor1,
                                                      Function<T, String> keyExtractor2, Function<T, String> valueExtractor2) {
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyMap();
        }
        switch (type) {
            case 1:
                return generateMap(list, keyExtractor1, valueExtractor1);
            case 2:
                return generateMap(list, keyExtractor1, valueExtractor2);
            case 3:
                return generateMap(list, keyExtractor2, valueExtractor1);
            case 4:
                return generateMap(list, keyExtractor2, valueExtractor2);
            default:
                return Collections.emptyMap();
        }
    }
}
