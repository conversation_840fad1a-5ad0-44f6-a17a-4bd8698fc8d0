package  com.chinasie.orion.controller;

import com.chinasie.orion.amqp.entity.ChangeStatusMessageDTO;
import com.chinasie.orion.domain.vo.SimpleDictVO;
import com.chinasie.orion.service.InterfaceStatusUpdateService;
import com.mzt.logapi.starter.annotation.LogRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;




import com.chinasie.orion.domain.entity.IdeaForm;
import com.chinasie.orion.domain.dto.IdeaFormDTO;
import com.chinasie.orion.domain.vo.IdeaFormVO;

import com.chinasie.orion.service.IdeaFormService;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
/**
 * <p>
 * IdeaForm 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29 22:08:44
 */
@RestController
@RequestMapping("/idea-form")
@Api(tags = "意见单")
public class IdeaFormController {

    @Autowired
    private IdeaFormService ideaFormService;

    @Autowired
    private InterfaceStatusUpdateService interfaceStatusUpdateService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(
            success = "【{USER{#logUserId}}】查看【意见单关系】",
            type = "IdeaFormToIdeaForm",
            subType = "查看",
            bizNo = "{#id}"
    )
    public ResponseDTO<IdeaFormVO> detail(@PathVariable(value = "id") String id) throws Exception {
        IdeaFormVO rsp = ideaFormService.detail(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param ideaFormDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【意见单】", type = "IdeaForm", subType = "新增", bizNo = "")
    public ResponseDTO<IdeaFormVO> create(@RequestBody @Validated IdeaFormDTO ideaFormDTO) throws Exception {
        IdeaFormVO rsp =  ideaFormService.create(ideaFormDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param ideaFormDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【意见单】", type = "IdeaForm", subType = "编辑", bizNo = "")
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<Boolean> edit(@RequestBody  IdeaFormDTO ideaFormDTO) throws Exception {
        Boolean rsp = ideaFormService.edit(ideaFormDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【意见单】", type = "IdeaForm", subType = "删除", bizNo = "")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = ideaFormService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/pages", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】分页查询【意见单】", type = "IdeaForm", subType = "分页", bizNo = "")
    public ResponseDTO<Page<IdeaFormVO>> pages(@RequestBody Page<IdeaFormDTO> pageRequest) throws Exception {
        Page<IdeaFormVO> rsp =  ideaFormService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 获取编码
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取编码")
    @RequestMapping(value = "/number", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】获取编码", type = "IdeaForm", subType = "查看", bizNo = "")
    public ResponseDTO<String> getNumber() throws Exception {
        return new ResponseDTO<>(ideaFormService.getIdeaFormNumber());
    }


    /**
     * 相关单据列表
     *
     * @param ideaFormDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "相关单据列表")
    @RequestMapping(value = "/correlationList", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查看相关单据列表", type = "IdeaForm", subType = "查看", bizNo = "")
    public ResponseDTO<List<IdeaFormVO>> list(@RequestBody IdeaFormDTO ideaFormDTO) throws Exception {
        List<IdeaFormVO> list =  ideaFormService.correlationList(ideaFormDTO);
        return new ResponseDTO<>(list);
    }

    @ApiOperation(value = "状态推动")
    @RequestMapping(value = "changeStatus", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】变更状态", type = "IdeaForm", subType = "变更", bizNo = "")
    public ResponseDTO<Boolean> changeStatus(@RequestBody ChangeStatusMessageDTO messageDTO) throws Exception {
        Boolean b = interfaceStatusUpdateService.businessChange(messageDTO);
        return new ResponseDTO<>(b);
    }

    @ApiOperation(value = "答复意见类型接口")
    @RequestMapping(value = "/getReplySuggest/dict", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】获取答复意见类型", type = "IdeaForm", subType = "查看", bizNo = "")
    public ResponseDTO<List<SimpleDictVO>> getReplySuggest() throws Exception {
        return new ResponseDTO<>(ideaFormService.getReplySuggest());
    }

    @ApiOperation(value = "意见类型")
    @RequestMapping(value = "/getFormTypeDict/dict", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】获取答复意见类型字典", type = "IdeaForm", subType = "查看", bizNo = "")
    public ResponseDTO<List<SimpleDictVO>> getFormTypeDict() throws Exception {
        return new ResponseDTO<>(ideaFormService.getFormTypeDict());
    }
}
