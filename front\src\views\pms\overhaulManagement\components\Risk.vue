<script setup lang="ts">
import { isPower, openFile, OrionTable } from 'lyra-component-vue3';
import {
  CSSProperties, h, inject, nextTick, ref, Ref,
} from 'vue';
import Api from '/@/api';
import { Popover } from 'ant-design-vue';
import { openRiskForm } from '/@/views/pms/dailyWork/pages/utils';
import { useRouter } from 'vue-router';

const router = useRouter();
const detailsData: Record<string, any> = inject('detailsData');
const powerData: Ref = inject('powerData');
const tableWrapStyle: CSSProperties = {
  height: '90px',
  overflow: 'hidden',
};

const tableRef: Ref = ref();
const tableOptions = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  isSpacing: false,
  pagination: false,
  resizeHeightOffset: -40,
  rowKey: 'jobId',
  api: async () => {
    const result = await new Api(`/pms/job-manage/risk/measure/detail/${detailsData?.id}`).fetch({}, '', 'GET');
    return result ? [result] : [];
  },
  columns: [
    {
      title: '首次执行',
      dataIndex: 'firstExecuteName',
    },
    {
      title: '新人参与',
      dataIndex: 'newParticipants',
      customRender({ text }) {
        return text ? '是' : text === false ? '否' : '';
      },
    },
    {
      title: '重要项目',
      dataIndex: 'importantProjectName',
    },
    {
      title: '管理措施落实证明',
      dataIndex: 'fileVOList',
      customRender({ text }) {
        if (isPower('PMS_DXZYXQNEW_container_01_03_button_01', powerData.value)) {
          return h(Popover, { title: '附件' }, {
            default: () => h('div', { class: 'flex-te action-btn' }, '附件列表'),
            content: () => (text instanceof Array ? text : [])?.map((item: any) => h('p', {
              class: 'action-btn',
              onClick() {
                openFile(item);
              },
            }, item.name)),
          });
        }
        return '';
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 120,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '查看',
      isShow: () => isPower('PMS_DXZYXQNEW_container_01_03_button_02', powerData.value),
      onClick: (record) => {
        navDetails(record.jobId);
      },
    },
    {
      text: '编辑',
      isShow: () => isPower('PMS_DXZYXQNEW_container_01_03_button_03', powerData.value),
      onClick(record: Record<string, any>) {
        openRiskForm({
          ...record,
          number: detailsData?.number,
        }, updateTable);
      },
    },
  ],
};

function navDetails(id: string) {
  console.log(id)
  router.push({
    name: 'PMSMajorRepairsRiskDetails',
    params: {
      id,
    },
  });
}

async function updateTable() {
  await nextTick();
  tableRef.value?.reload();
}
</script>

<template>
  <div :style="tableWrapStyle">
    <OrionTable
      ref="tableRef"
      style="position: relative"
      :options="tableOptions"
    />
  </div>
</template>

<style scoped lang="less">
:deep(.orion-table-header-wrap) {
  display: none;
}
</style>
