package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;

/**
 * ProjectPlanTypeTypeAttributeValue DTO对象
 *
 * <AUTHOR>
 * @since 2024-03-26 10:54:06
 */
@ApiModel(value = "ProjectPlanTypeTypeAttributeValueDTO对象", description = "项目类型属性值")
@Data
public class ProjectPlanTypeAttributeValueDTO extends ObjectDTO implements Serializable{

/**
 * imageId
 */
@ApiModelProperty(value = "imageId")
private String imageId;

/**
 * 编码
 */
@ApiModelProperty(value = "编码")
private String number;

/**
 * typeId
 */
@ApiModelProperty(value = "typeId")
private String typeId;

/**
 * 名称
 */
@ApiModelProperty(value = "名称")
private String name;

/**
 * 排序
 */
@ApiModelProperty(value = "排序")
private Integer sort;

/**
 * attributeId
 */
@ApiModelProperty(value = "attributeId")
private String attributeId;

/**
 * 属性值
 */
@ApiModelProperty(value = "属性值")
private String value;

/**
 * code
 */
@ApiModelProperty(value = "code")
private String code;

}
