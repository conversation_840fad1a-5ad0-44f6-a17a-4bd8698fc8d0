package com.chinasie.orion.service.impl;





import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.domain.dto.ProjectApprovalProductDTO;
import com.chinasie.orion.domain.dto.ProjectToProductDTO;
import com.chinasie.orion.domain.entity.ProjectApprovalProduct;
import com.chinasie.orion.domain.entity.ProjectToProduct;
import com.chinasie.orion.domain.vo.ProjectApprovalProductVO;
import com.chinasie.orion.domain.vo.ProjectBaseToProductVO;
import com.chinasie.orion.domain.vo.ProjectToProductVO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.pdm.api.domain.vo.ProductEstimateMaterialVO;
import com.chinasie.orion.pdm.api.domain.vo.ProductToMaterialVO;
import com.chinasie.orion.pdm.api.service.MaterialsApiService;
import com.chinasie.orion.pdm.api.service.ProductApiService;
import com.chinasie.orion.repository.ProjectToProductMapper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectApprovalProductService;
import com.chinasie.orion.service.ProjectToProductService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;

import javax.servlet.http.HttpServletResponse;

import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;

import java.io.InputStream;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;


/**
 * <p>
 * ProjectToProduct 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-22 11:01:33
 */
@Service
@Slf4j
public class ProjectToProductServiceImpl extends OrionBaseServiceImpl<ProjectToProductMapper, ProjectToProduct> implements ProjectToProductService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private ProjectApprovalProductService productService;

    @Autowired
    private ProductApiService productApiService;

    @Autowired
    private MaterialsApiService materialsApiService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProjectToProductVO detail(String id, String pageCode) throws Exception {
        ProjectToProduct projectToProduct = this.getById(id);
        ProjectToProductVO result = BeanCopyUtils.convertTo(projectToProduct, ProjectToProductVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param projectToProductDTO
     */
    @Override
    public String create(ProjectToProductDTO projectToProductDTO) throws Exception {
        ProjectToProduct projectToProduct = BeanCopyUtils.convertTo(projectToProductDTO, ProjectToProduct::new);
        this.save(projectToProduct);

        String rsp = projectToProduct.getId();


        return rsp;
    }

    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        for (String id : ids) {
        }
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectToProductVO> pages(Page<ProjectToProductDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProjectToProduct> condition = new LambdaQueryWrapperX<>(ProjectToProduct.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ProjectToProduct::getCreateTime);


        Page<ProjectToProduct> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectToProduct::new));

        PageResult<ProjectToProduct> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectToProductVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectToProductVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectToProductVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public void setEveryName(List<ProjectToProductVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }

    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectApprovalProductVO> getListByProjectId(Page<ProjectToProductDTO> pageRequest) throws Exception {
        ProjectToProductDTO query = pageRequest.getQuery();
        if (ObjectUtil.isNull(query) || StrUtil.isBlank(pageRequest.getQuery().getProjectId())) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_ID_NULL, "项目id不能为空！");
        }
        String projectId = pageRequest.getQuery().getProjectId();

        LambdaQueryWrapperX<ProjectToProduct> condition = new LambdaQueryWrapperX<>(ProjectToProduct.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.eq(ProjectToProduct::getProjectId, projectId);
        condition.orderByDesc(ProjectToProduct::getCreateTime);
        Page<ProjectToProduct> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectToProduct::new));

        PageResult<ProjectToProduct> page = this.getBaseMapper().selectPage(realPageRequest, condition);
        Page<ProjectApprovalProductVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectToProduct> content = page.getContent();
        if (CollectionUtil.isEmpty(content)) {
            return new Page<>();
        }
        List<String> productIds = content.stream().map(ProjectToProduct::getProductId).distinct().collect(Collectors.toList());
        List<ProductEstimateMaterialVO> list = productApiService.getList(productIds);
        List<ProjectApprovalProductVO> projectApprovalProductVOS = BeanCopyUtils.convertListTo(list, ProjectApprovalProductVO::new);
        return pageResult.setContent(projectApprovalProductVOS);
    }

    @Override
    public List<ProductEstimateMaterialVO> getProductEstimateMaterialList(String projectId) {
        LambdaQueryWrapperX<ProjectToProduct> condition = new LambdaQueryWrapperX<>(ProjectToProduct.class);
        condition.eq(ProjectToProduct::getProjectId, projectId);
        List<ProjectToProduct> projectToProducts = this.list(condition);
        if (CollUtil.isEmpty(projectToProducts)) {
            return new ArrayList<>();
        }
        List<String> ids = projectToProducts.stream().map(ProjectToProduct::getProductId).collect(Collectors.toList());
        List<ProductEstimateMaterialVO> productEstimateMaterialVOS = productApiService.getList(ids);
        return productEstimateMaterialVOS;
    }

    @Override
    public List<ProductToMaterialVO> getProductToMaterialList(String productId) throws Exception {
        List<ProductToMaterialVO> productToMaterialVOS = materialsApiService.getMaterialTree(productId);
        return productToMaterialVOS;
    }

    @Override
    public List<String> getProjectIdByProductIds(List<String> productIds) throws Exception {
        List<ProjectToProduct> list = this.list(new LambdaQueryWrapperX<>(ProjectToProduct.class).select(ProjectToProduct::getProjectId)
                .in(ProjectToProduct::getProductId, productIds));
        return list.stream().map(ProjectToProduct::getProjectId).distinct().collect(Collectors.toList());
    }


    public static class ProjectToProductExcelListener extends AnalysisEventListener<ProjectToProductDTO> {

        private final List<ProjectToProductDTO> data = new ArrayList<>();

        @Override
        public void invoke(ProjectToProductDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ProjectToProductDTO> getData() {
            return data;
        }
    }


}
