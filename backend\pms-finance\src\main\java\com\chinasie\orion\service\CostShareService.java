package com.chinasie.orion.service;


import java.lang.String;
import java.util.List;


import com.alibaba.excel.context.AnalysisContext;
import com.chinasie.orion.domain.dto.CostShareDTO;
import com.chinasie.orion.domain.dto.CostShareTemplateDTO;
import com.chinasie.orion.domain.entity.CostShare;
import com.chinasie.orion.domain.vo.CostShareVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.lang.String;
import java.util.concurrent.CompletableFuture;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * CostShare 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18 09:29:00
 */
public interface CostShareService extends OrionBaseService<CostShare> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    CostShareVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param costShareDTO
     */
    String create(CostShareDTO costShareDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param costShareDTO
     */
    Boolean edit(CostShareDTO costShareDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<CostShareVO> pages(Page<CostShareDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    Object importCheckByExcel(MultipartFile file,HttpServletResponse response) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(CostShareDTO costShareDTO, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<CostShareVO> vos) throws Exception;


}
