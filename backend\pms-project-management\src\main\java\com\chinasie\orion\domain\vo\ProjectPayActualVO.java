package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;

import java.util.List;
/**
 * ProjectPayActual VO对象
 *
 * <AUTHOR>
 * @since 2024-07-04 00:48:18
 */
@ApiModel(value = "ProjectPayActualVO对象", description = "实际成本金额")
@Data
public class ProjectPayActualVO extends  ObjectVO   implements Serializable{

            /**
         * 项目定义
         */
        @ApiModelProperty(value = "项目定义")
        private String psphi;

        /**
         * 对象
         */
        @ApiModelProperty(value = "对象")
        private String objnr;
        /**
         * 对象名称
         */
        @ApiModelProperty(value = "对象名称")
        private String postOne;
        /**
         * WBS编码
         */
        @ApiModelProperty(value = "WBS编码")
        private String posid;
        /**
         * 对象货币值
         */
        @ApiModelProperty(value = "对象货币值")
        private String wogbtr;

        /**
         * 过账日期
         */
        @ApiModelProperty(value = "过账日期")
        private Date budat;


        /**
         * 凭证抬头文本
         */
        @ApiModelProperty(value = "凭证抬头文本")
        private String bltxt;


        /**
         * 凭证编码
         */
        @ApiModelProperty(value = "凭证编码")
        private String belnr;


        /**
         * 参考凭证编码
         */
        @ApiModelProperty(value = "参考凭证编码")
        private String refbn;


        /**
         * 陈本要素
         */
        @ApiModelProperty(value = "陈本要素")
        private String kstar;


        /**
         * 成本要素名称
         */
        @ApiModelProperty(value = "成本要素名称")
        private String ktext;


        /**
         * 冲转科目名
         */
        @ApiModelProperty(value = "冲转科目名")
        private String gkont;


        /**
         * 采购凭证
         */
        @ApiModelProperty(value = "采购凭证")
        private String ebeln;


        /**
         * 采购订单文本
         */
        @ApiModelProperty(value = "采购订单文本")
        private String txzOne;


        /**
         * 公司
         */
        @ApiModelProperty(value = "公司")
        private String bukrs;


        /**
         * 名称
         */
        @ApiModelProperty(value = "名称")
        private String sgtxt;


        /**
         * 功能范围
         */
        @ApiModelProperty(value = "功能范围")
        private String fkber;


        /**
         * 年度
         */
        @ApiModelProperty(value = "年度")
        private String gjahr;


        /**
         * 数据更新时间
         */
        @ApiModelProperty(value = "数据更新时间")
        private Date insertTime;


        /**
         * 本次数据更新时间
         */
        @ApiModelProperty(value = "本次数据更新时间")
        private Date updateTime;


    

}
