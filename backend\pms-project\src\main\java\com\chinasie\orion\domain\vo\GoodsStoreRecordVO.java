package com.chinasie.orion.domain.vo;

import com.chinasie.orion.domain.entity.GoodsServiceStore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * GoodsStoreRecord Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-26 10:06:03
 */
@ApiModel(value = "GoodsStoreRecordVO对象", description = "物资/服务存储记录表")
@Data
public class GoodsStoreRecordVO extends ObjectVO implements Serializable{

            /**
         * 编码
         */
        @ApiModelProperty(value = "编码")
        private String number;

        /**
         * 名称
         */
        @ApiModelProperty(value = "名称")
        private String name;



        /**
         * 项目ID
         */
        @ApiModelProperty(value = "项目ID")
        private String projectId;

        /**
         * 描述
         */
        @ApiModelProperty(value = "描述")
        private String description;

        /**
         * 物资服务入库ID
         */
        @ApiModelProperty(value = "物资服务入库ID")
        private String goodsServiceStoreId;

        /**
         * 入库时间
         */
        @ApiModelProperty(value = "入库时间")
        private Date storeTime;

        /**
         * 物资服务编码
         */
        @ApiModelProperty(value = "物资服务编码")
        private String goodsServiceNumber;

        /**
         * 本次入库数量
         */
        @ApiModelProperty(value = "本次入库数量")
        private BigDecimal storeAmount;

        /**
         * 入库基本信息
         */
        @ApiModelProperty(value = "入库基本信息")
        private GoodsServiceStoreVO goodsServiceStoreVO;
    }
