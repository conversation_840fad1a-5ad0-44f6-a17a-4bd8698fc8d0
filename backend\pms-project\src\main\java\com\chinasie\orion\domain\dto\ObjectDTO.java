package com.chinasie.orion.domain.dto;

import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/06/10:35
 * @description:
 */
@Data
@ApiModel(value = "ObjectEntityDTO对象", description = "Pmsx 基类")
public class ObjectDTO implements Serializable {

    /**
     * 拥有者ID
     */
    @ApiModelProperty(value = "拥有者ID")
    private String ownerId;
    private String ownerName;

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private String id;

    @ApiModelProperty(value = "排序字段")
    private Long sort;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 描述/备注
     */
    @ApiModelProperty(value = "描述/备注")
    private String remark;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * logicStatus 逻辑删除字段
     */
    @ApiModelProperty(value = "logicStatus 逻辑删除字段")
    private Integer logicStatus;

    /**
     * 修改者ID
     */
    @ApiModelProperty(value = "修改者ID")
    private String modifyId;
    private String modifyName;

    /**
     * 创建者id
     */
    @ApiModelProperty(value = "创建者id")
    private String creatorId;
    private String creatorName;

    /**
     * 类名称
     */
    @ApiModelProperty(value = "类名称")
    private String className;

    /**
     * 平台ID
     */
    @ApiModelProperty(value = "平台ID")
    private String platformId;
    /**
     * 组织Id
     */
    @ApiModelProperty(value = "组织ID")
    private String orgId;

    /**
     * 状态对象
     */
    @ApiModelProperty(value = "状态对象")
    private DataStatusVO dataStatus;

}
