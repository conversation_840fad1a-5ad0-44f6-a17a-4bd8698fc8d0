package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ActualPayMilestone DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@ApiModel(value = "ActualPayMilestoneDTO对象", description = "合同支付里程碑（实际）")
@Data
@ExcelIgnoreUnannotated
public class ActualPayMilestoneDTO extends ObjectDTO implements Serializable {

    /**
     * 支付编号
     */
    @ApiModelProperty(value = "支付编号")
    @ExcelProperty(value = "支付编号 ", index = 0)
    private String payNumber;

    /**
     * 支付申请人
     */
    @ApiModelProperty(value = "支付申请人")
    @ExcelProperty(value = "支付申请人 ", index = 1)
    private String payReqUser;

    /**
     * 支付申请发起时间
     */
    @ApiModelProperty(value = "支付申请发起时间")
    @ExcelProperty(value = "支付申请发起时间 ", index = 2)
    private Date payReqStartTime;

    /**
     * 支付类型
     */
    @ApiModelProperty(value = "支付类型")
    @ExcelProperty(value = "支付类型 ", index = 3)
    private String payType;

    /**
     * 预计付款时间
     */
    @ApiModelProperty(value = "预计付款时间")
    @ExcelProperty(value = "预计付款时间 ", index = 4)
    private Date estimatedPayTime;

    /**
     * 里程碑业务描述
     */
    @ApiModelProperty(value = "里程碑业务描述")
    @ExcelProperty(value = "里程碑业务描述 ", index = 5)
    private String milestoneDesc;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @ExcelProperty(value = "供应商名称 ", index = 6)
    private String supplierName;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    @ExcelProperty(value = "币种 ", index = 7)
    private String currency;

    /**
     * 本次支付汇总金额
     */
    @ApiModelProperty(value = "本次支付汇总金额")
    @ExcelProperty(value = "本次支付汇总金额 ", index = 8)
    private BigDecimal currentPayTotalAmount;

    /**
     * 支付比例
     */
    @ApiModelProperty(value = "支付比例")
    @ExcelProperty(value = "支付比例 ", index = 9)
    private String payRatio;

    /**
     * 已支付金额
     */
    @ApiModelProperty(value = "已支付金额")
    @ExcelProperty(value = "已支付金额 ", index = 10)
    private BigDecimal paidAmount;

    /**
     * 合同预计验收时间
     */
    @ApiModelProperty(value = "合同预计验收时间")
    @ExcelProperty(value = "合同预计验收时间 ", index = 11)
    private Date estimatedAcceptanceTime;

    /**
     * 合同验收时间
     */
    @ApiModelProperty(value = "合同验收时间")
    @ExcelProperty(value = "合同验收时间 ", index = 12)
    private Date acceptanceTime;

    /**
     * 是否有质保金
     */
    @ApiModelProperty(value = "是否有质保金")
    @ExcelProperty(value = "是否有质保金 ", index = 13)
    private Boolean isHaveQualityAmount;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 14)
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @ExcelProperty(value = "主表ID ", index = 15)
    private String mainTableId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 16)
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称 ", index = 17)
    private String contractName;

    /**
     * 实际支付时间
     */
    @ApiModelProperty(value = "实际支付时间")
    @ExcelProperty(value = "实际支付时间 ", index = 18)
    private Date actualPayTime;

    /**
     * 原因
     */
    @ApiModelProperty(value = "原因")
    @ExcelProperty(value = "原因 ", index = 19)
    private String reason;

    /**
     * 实际验收时间
     */
    @ApiModelProperty(value = "实际验收时间")
    @ExcelProperty(value = "实际验收时间 ", index = 20)
    private Date actualAcceptanceTime;

    /**
     * 是否一次验收
     */
    @ApiModelProperty(value = "是否一次验收")
    @ExcelProperty(value = "是否一次验收 ", index = 21)
    private String isAcceptacneQualified;

    /**
     * 是否一次验收合格
     */
    @ApiModelProperty(value = "是否一次验收合格")
    @ExcelProperty(value = "是否一次验收", index = 22)
    private String isOnetimeAcceptance;

    /**
     * 是否按时交付
     */
    @ApiModelProperty(value = "是否按时交付")
    @ExcelProperty(value = "是否按时交付", index = 23)
    private String isDeliverOnTime;

    /**
     * 未按时交付验收原因
     */
    @ApiModelProperty(value = "未按时交付验收原因")
    @ExcelProperty(value = "未按时交付验收原因", index = 24)
    private String reasonOfUndeliver;

    /**
     * 实际交付时间
     */
    @ApiModelProperty(value = "实际交付时间")
    @ExcelProperty(value = "实际交付时间 ", index = 19)
    private Date actualDeliveryTime;


        /**
     * 支付申请人名称
     */
    @ApiModelProperty(value = "支付申请人名称")
    private String payReqUserName;

    /**
     * 供应商编号
     */
    @ApiModelProperty(value = "供应商名称")
    private String supplierCode;
}
