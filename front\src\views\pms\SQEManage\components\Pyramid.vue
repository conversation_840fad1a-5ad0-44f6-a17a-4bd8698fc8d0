<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps<{
  data: {
    quality: any,
    safety: any
  }
}>();

const countList = computed(() => props.data?.quality?.countList?.slice(0, 4));
const listData = computed<Record<string, any>[]>(() => countList.value?.map((_item: any, index: number) => ({
  safety: props.data.safety.countList[index].count,
  quality: props.data.quality.countList[index].count,
})));

function getTitle(index) {
  switch (index) {
    case 0:
      return '外部安质环监督';
    case 1:
      return '接口部门检查';
    case 2:
      return '项目部检查';
    case 3:
      return '专业中心自查';
  }
}
</script>

<template>
  <div class="pyramid">
    <div
      v-for="(item,index) in listData"
      :key="index"
      :title="getTitle(index)"
    >
      <span
        class="security"
        :style="{marginRight:(20*index)+10+'px'}"
      >{{ item.safety }}</span>
      <span
        class="quality"
        :style="{marginLeft:(20*index)+10+'px'}"
      >{{ item.quality }}</span>
    </div>
  </div>
  <div class="footer">
    <span>安全</span>
    <span>质量</span>
  </div>
</template>

<style scoped lang="less">
.pyramid {
  position: relative;
  width: 100%;
  height: 400px;
  display: flex;
  flex-direction: column;
  background-image: linear-gradient(to bottom,
  red 0%, red 25%,
  orange 25%, orange 50%,
  yellow 50%, yellow 75%,
  green 75%, green 100%);
  background-repeat: no-repeat;
  clip-path: polygon(50% 0, 100% 100%, 0% 100%);

  &::after {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    content: '';
    width: 2px;
    height: 100%;
    background-color: rgba(0, 0, 0, .5);
  }
}

.pyramid > div {
  flex-basis: 25%;
  font-size: 20px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;

  &:nth-child(1) > span {
    align-self: flex-end;
  }
}

.footer {
  width: 100%;
  padding: 20px;
  font-size: 16px;
  font-weight: bold;
  color: ~`getPrefixVar('primary-color')`;
  display: flex;
  align-items: center;
  justify-content: space-around;
}
</style>
