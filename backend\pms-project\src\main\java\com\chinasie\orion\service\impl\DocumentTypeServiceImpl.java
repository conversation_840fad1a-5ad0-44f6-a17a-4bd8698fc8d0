package com.chinasie.orion.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.bo.DocumentBo;
import com.chinasie.orion.bo.LyraFileBO;
import com.chinasie.orion.constant.DocumentClassNameConstant;
import com.chinasie.orion.constant.DocumentTypeNameConstant;
import com.chinasie.orion.constant.RelationClassNameConstant;
import com.chinasie.orion.domain.dto.DemandManagementQueryDTO;
import com.chinasie.orion.domain.dto.DocumentTypeDTO;
import com.chinasie.orion.domain.dto.document.DocumentDTO;
import com.chinasie.orion.domain.entity.DocumentType;
import com.chinasie.orion.domain.entity.FileInfo;
import com.chinasie.orion.domain.vo.DocumentTypeTreeVO;
import com.chinasie.orion.domain.vo.TreeSimpleVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.OrderItem;
import com.chinasie.orion.repository.DocumentTypeRepository;
import com.chinasie.orion.service.DocumentTypeService;
import com.chinasie.orion.service.FileInfoService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/22/14:40
 * @description:
 */
@Service
public class DocumentTypeServiceImpl extends OrionBaseServiceImpl<DocumentTypeRepository, DocumentType> implements DocumentTypeService {

    @Resource
    private LyraFileBO fileBo;
    @Resource
    private FileInfoService fileInfoService;
    @Resource
    private DocumentBo documentBo;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveDocumentType(DocumentTypeDTO documentTypeDTO) throws Exception {
        String name = documentTypeDTO.getName();
        if ( StrUtil.isBlank(name)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "文档类型名称不能为空");
        }


        List<DocumentType> documentTypeDTOList = this.list(new LambdaQueryWrapper<>(DocumentType.class).
                eq(DocumentType::getName, documentTypeDTO.getName()).
                eq(DocumentType::getProjectId, documentTypeDTO.getProjectId()).
                eq(DocumentType::getParentId, documentTypeDTO.getParentId()));
        if (!CollectionUtils.isEmpty(documentTypeDTOList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_NAME_REPEAT);
        }
        //todo编码规则
        documentTypeDTO.setNumber(String.format("WDLX%s", IdUtil.objectId()));
        DocumentType documentType = BeanCopyUtils.convertTo(documentTypeDTO, DocumentType::new);
        this.save(documentType);
        String id = documentType.getId();
        DocumentDTO documentDTO = new DocumentDTO();
        documentDTO.setName(documentTypeDTO.getName());
        documentDTO.setNumber(documentTypeDTO.getNumber());
        documentDTO.setClassName(DocumentClassNameConstant.Document_Type_Document);
        String s = documentBo.insertDocument(documentDTO);

        documentTypeDTO.setId(id);
        documentTypeDTO.setDocumentId(s);
        DocumentType documentType2 = BeanCopyUtils.convertTo(documentTypeDTO, DocumentType::new);
        this.updateById(documentType2);
        return id;
    }

    @Override
    public List<DocumentTypeTreeVO> getDocumentTypeTree(DemandManagementQueryDTO demandManagementQueryDTO) throws Exception {
        String projectId = demandManagementQueryDTO.getProjectId();
        List<OrderItem> orderList = new ArrayList<>();
        OrderItem orderItem = new OrderItem();
        orderItem.setColumn("sort");
        orderItem.setAsc(true);
        orderList.add(orderItem);
        List<DocumentType> documentTypeDTOList = this.list(new LambdaQueryWrapper<>(DocumentType.class).
                eq(DocumentType::getProjectId, projectId).orderByAsc(DocumentType::getSort));
        List<DocumentTypeTreeVO> treeVOList = BeanCopyUtils.convertListTo(documentTypeDTOList, DocumentTypeTreeVO::new);
        List<DocumentTypeTreeVO> list = new ArrayList<>();
        Map<String, DocumentTypeTreeVO> treeSimpleVoMap = treeVOList.stream().collect(Collectors.toMap(DocumentTypeTreeVO::getId, Function.identity()));
        treeVOList.forEach(d -> {
            String parentId = d.getParentId();
            try {
                switch (d.getName()) {
                    case DocumentTypeNameConstant.PLAN_DOCUMENT:
                        d.setCount(fileInfoService.list(new LambdaQueryWrapper<>(FileInfo.class)
                                .eq(FileInfo::getClassName, RelationClassNameConstant.PLAN + "File").
                                        eq(FileInfo::getProjectId, projectId)).size());
                        break;
                    case DocumentTypeNameConstant.DEMAND_DOCUMENT:
                        d.setCount(fileInfoService.list(new LambdaQueryWrapper<>(FileInfo.class)
                                .eq(FileInfo::getClassName, RelationClassNameConstant.DEMAND_MANAGEMENT + "File").
                                        eq(FileInfo::getProjectId, projectId)).size());
                        break;
                    case DocumentTypeNameConstant.QUESTION_DOCUMENT:
                        d.setCount(fileInfoService.list(new LambdaQueryWrapper<>(FileInfo.class)
                                .eq(FileInfo::getClassName, RelationClassNameConstant.QUESTION_MANAGEMENT + "File").
                                        eq(FileInfo::getProjectId, projectId)).size());
                        break;
                    case DocumentTypeNameConstant.RISK_DOCUMENT:
                        d.setCount(fileInfoService.list(new LambdaQueryWrapper<>(FileInfo.class)
                                .and(sub -> sub.eq(FileInfo::getClassName, RelationClassNameConstant.RISK_MANAGEMENT + "File")
                                        .or().eq(FileInfo::getClassName, RelationClassNameConstant.RISK_PLAN + "File"))
                                .eq(FileInfo::getProjectId, projectId)).size());
                        break;
                    case DocumentTypeNameConstant.CHANGE_DOCUMENT:
                        d.setCount(0L);
                        break;
                    case DocumentTypeNameConstant.POST_PROJECT_DOCUMENT:
                        d.setCount(fileInfoService.list(new LambdaQueryWrapper<>(FileInfo.class)
                                .eq(FileInfo::getClassName, RelationClassNameConstant.POST_PROJECT + "File").
                                        eq(FileInfo::getProjectId, projectId)).size());
                        break;
                    default:
                        d.setCount(fileInfoService.list(new LambdaQueryWrapper<>(FileInfo.class)
                                .eq(FileInfo::getDataId, d.getId())).size());
                        break;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (treeSimpleVoMap.containsKey(parentId)) {
                DocumentTypeTreeVO treeSimpleVo = treeSimpleVoMap.get(parentId);
                List<DocumentTypeTreeVO> child = treeSimpleVo.getChild();
                if (Objects.isNull(child)) {
                    child = new ArrayList<>();
                }
                child.add(d);
                treeSimpleVo.setCount(treeSimpleVo.getCount() + d.getCount());
                treeSimpleVo.setChild(child);
            } else {
                list.add(d);
            }
        });
        return list;
    }

    @Override
    public List<TreeSimpleVO> getDocumentTypeSimpleTree(DemandManagementQueryDTO demandManagementQueryDTO) throws Exception {
        List<DocumentType> documentTypeDTOList = this.list(new LambdaQueryWrapper<>(DocumentType.class).
                eq(DocumentType::getProjectId, demandManagementQueryDTO.getProjectId()));
        List<TreeSimpleVO> treeSimpleVOList = BeanCopyUtils.convertListTo(documentTypeDTOList, TreeSimpleVO::new);
        List<TreeSimpleVO> list = new ArrayList<>();
        Map<String, TreeSimpleVO> treeSimpleVoMap = treeSimpleVOList.stream().collect(Collectors.toMap(TreeSimpleVO::getId, Function.identity()));
        treeSimpleVOList.forEach(d -> {
            String parentId = d.getParentId();
            if (treeSimpleVoMap.containsKey(parentId)) {
                TreeSimpleVO treeSimpleVo = treeSimpleVoMap.get(parentId);
                List<TreeSimpleVO> child = treeSimpleVo.getChild();
                if (Objects.isNull(child)) {
                    child = new ArrayList<>();
                }
                child.add(d);
                treeSimpleVo.setChild(child);
            } else {
                if (!Objects.equals(DocumentTypeNameConstant.PROCESS_DATA_AREA, d.getName())) {
                    list.add(d);
                }
            }
        });

        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean editDocumentType(DocumentTypeDTO documentTypeDTO) throws Exception {
        String name = documentTypeDTO.getName();
        if ( StrUtil.isBlank(name)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "文档类型名称不能为空");
        }
        String id = documentTypeDTO.getId();
        List<DocumentType> documentTypeDTOList = this.list(new LambdaQueryWrapper<>(DocumentType.class)
                .ne(DocumentType::getId, id)
                .eq(DocumentType::getName, documentTypeDTO.getName())
                .eq(DocumentType::getProjectId, documentTypeDTO.getProjectId())
                .eq(DocumentType::getParentId, documentTypeDTO.getParentId()));
        if (!CollectionUtils.isEmpty(documentTypeDTOList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_NAME_REPEAT);
        }

        List<String> idList = new ArrayList<>();
        List<String> parentIdList = new ArrayList<>();
        String parentId = documentTypeDTO.getParentId();
        parentIdList.add(id);
        idList.add(id);
        getChildren(idList, parentIdList);
        if (idList.contains(parentId)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "父级不能为自身或父级不能为自身的子级");
        }

        DocumentType documentType = BeanCopyUtils.convertTo(documentTypeDTO, DocumentType::new);
        Boolean result = this.updateById(documentType);
        DocumentType documentTypeDTO1 = this.getById(id);


        String documentId = documentTypeDTO1.getDocumentId();
        if (StringUtils.hasText(documentId)) {
            DocumentDTO documentDTO = new DocumentDTO();
            documentDTO.setId(documentId);
            documentDTO.setName(documentTypeDTO.getName());
            documentDTO.setNumber(documentTypeDTO.getNumber());
            documentDTO.setClassName(DocumentClassNameConstant.Document_Type_Document);
            documentBo.updateDocument(documentDTO);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeDocumentType(String id) throws Exception {
        DocumentType documentTypeDTO = this.getById(id);
        if (Objects.isNull(documentTypeDTO)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        List<String> idList = new ArrayList<>();
        List<String> parentIdList = new ArrayList<>();
        parentIdList.add(id);
        idList.add(id);
        getChildren(idList, parentIdList);
        List<FileInfo> fileInfoDTOList = fileInfoService.list(new LambdaQueryWrapper<>(FileInfo.class)
                .in(FileInfo::getDataId, idList.toArray()));
        if (!CollectionUtils.isEmpty(fileInfoDTOList)) {
            fileInfoDTOList.forEach(o -> {
                o.setDataId(documentTypeDTO.getParentId());
            });
            fileInfoService.updateBatchById(fileInfoDTOList);
            List<FileDTO> fileDtoList = BeanCopyUtils.convertListTo(fileInfoDTOList, FileDTO::new);
            fileBo.updateBatchFile(fileDtoList);
        }

        List<DocumentType> documentTypeDTOS = this.listByIds(idList);
        List<String> documentIdList = new ArrayList<>();
        for (DocumentType typeDTO : documentTypeDTOS) {
            String documentId = typeDTO.getDocumentId();
            if (StringUtils.hasText(documentId)) {
                documentIdList.add(documentId);
            }
        }
        if (!CollectionUtils.isEmpty(documentIdList)) {
            documentBo.delByIdList(documentIdList);
        }

        return this.removeBatchByIds(idList);
    }

    private void getChildren(List<String> idList, List<String> parentIdList) throws Exception {
        List<DocumentType> documentTypeDTOList = this.list(new LambdaQueryWrapper<>(DocumentType.class)
                .in(DocumentType::getParentId, parentIdList.toArray()));
        if (!CollectionUtils.isEmpty(documentTypeDTOList)) {
            parentIdList = documentTypeDTOList.stream().map(DocumentType::getId).collect(Collectors.toList());
            idList.addAll(parentIdList);
            getChildren(idList, parentIdList);
        }
    }
}
