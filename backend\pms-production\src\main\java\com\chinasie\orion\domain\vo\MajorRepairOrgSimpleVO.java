package com.chinasie.orion.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * MajorRepairOrg Entity对象
 *
 * <AUTHOR>
 * @since 2024-11-18 09:32:26
 */
@ApiModel(value = "MajorRepairOrgSimpleVO对象", description = "大修组织")
@Data
public class MajorRepairOrgSimpleVO   implements Serializable{

    private String id;

    /**
     * 责任人编码
     */
    @ApiModelProperty(value = "责任人编码")
    private String rspUserCode;

    /**
     * 责任人名称
     */
    @ApiModelProperty(value = "责任人名称")
    private String rspUserName;

    /**
     * 父级ID
     */
    @ApiModelProperty(value = "父级ID")
    private String parentId;

    /**
     * 全路径
     */
    @ApiModelProperty(value = "全路径")
    private String chainPath;

    /**
     * 大修伦次
     */
    @ApiModelProperty(value = "大修伦次")
    private String repairRound;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 组织类型：
     */
    @ApiModelProperty(value = "组织类型：")
    private String type;

    /**
     * 组织名称
     */
    @ApiModelProperty(value = "组织名称")
    private String name;

    /**
     * 组织层级
     */
    @ApiModelProperty(value = "组织层级")
    private Integer level;


    @ApiModelProperty(value = "组织code")
    private String code;
    @ApiModelProperty(value = "责任人ID")
    private String rspUserId;


    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    private Date beginTime;

    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    private Date endTime;

    /**
     * 实际开始时间
     */
    @ApiModelProperty(value = "实际开始时间")
    private Date actualBeginTime;

    /**
     * 实际结束时间
     */
    @ApiModelProperty(value = "实际结束时间")
    private Date actualEndTime;


    @ApiModelProperty(value = "层级类型")
    @ExcelProperty(value = "层级类型：repairRole(大修指挥部角色)，executionSpecialty(执行专业) ,specialtyManagementRole(管理组-角色)，specialtyTeam(专业班组) ", index = 9)
    private String levelType;

    @ApiModelProperty(value = "类名称")
    private String className;

}
