package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * LaborCostAcceptanceStatistics DTO对象
 *
 * <AUTHOR>
 * @since 2024-10-29 14:23:45
 */
@ApiModel(value = "LaborCostAcceptanceStatisticsDTO对象", description = "验收人力成本费用统计")
@Data
@ExcelIgnoreUnannotated
public class LaborCostAcceptanceStatisticsDTO extends  ObjectDTO   implements Serializable{

    /**
     * 验收单id
     */
    @ApiModelProperty(value = "验收单id")
    @ExcelProperty(value = "验收单id ", index = 0)
    private String acceptanceId;

    /**
     * 人员岗级
     */
    @ApiModelProperty(value = "人员岗级")
    @ExcelProperty(value = "人员岗级 ", index = 1)
    private String jobGrade;

    /**
     * 计划需求人数
     */
    @ApiModelProperty(value = "计划需求人数")
    @ExcelProperty(value = "计划需求人数 ", index = 2)
    private Integer planUserCount;

    /**
     * 实际人数
     */
    @ApiModelProperty(value = "实际人数")
    @ExcelProperty(value = "实际人数 ", index = 3)
    private Integer actualUserCount;

    /**
     * 工作量(人/月)
     */
    @ApiModelProperty(value = "工作量(人/月)")
    @ExcelProperty(value = "工作量(人/月) ", index = 4)
    private BigDecimal workload;

    /**
     * 岗级成本
     */
    @ApiModelProperty(value = "岗级成本")
    @ExcelProperty(value = "岗级成本 ", index = 5)
    private BigDecimal jobGradeAmt;

    /**
     * 岗级总价 (元)
     */
    @ApiModelProperty(value = "岗级总价 (元)")
    @ExcelProperty(value = "岗级总价 (元) ", index = 6)
    private BigDecimal jobGradeTotalAmt;




}
