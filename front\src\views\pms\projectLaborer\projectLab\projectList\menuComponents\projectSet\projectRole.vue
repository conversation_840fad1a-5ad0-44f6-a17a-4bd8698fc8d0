<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
    @selection-change="selectionChange"
  >
    <template #toolbarLeft>
      <BasicButton
        v-if="isPower('PMS_XMXQ_container_12_02_button_01', powerData) "
        class="mr10"
        @click="openAddRoleModalHandle"
      >
        <ApartmentOutlined />
        <span class="labelSpan">系统添加</span>
      </BasicButton>
      <BasicButton
        v-if=" isPower('PMS_XMXQ_container_12_02_button_02', powerData) "
        class="mr10"
        icon="sie-icon-jinyong"
        @click="banState"
      >
        <span class="labelSpan">禁用</span>
      </BasicButton>
      <BasicButton
        v-if=" isPower('PMS_XMXQ_container_12_02_button_03', powerData) "
        class="mr10"
        icon="sie-icon-qiyong"
        @click="useState"
      >
        <span class="labelSpan">启用</span>
      </BasicButton>
      <BasicButton
        v-if=" isPower('PMS_XMXQ_container_12_02_button_04', powerData) "
        class="mr10"
        icon="delete"
        @click="multiDelete"
      >
        <span class="labelSpan">删除</span>
      </BasicButton>
    </template>
  </OrionTable>

  <!-- 查看详情弹窗 -->
  <checkDetails
    :data="nodeData"
    @close="closecheck"
  />
  <!-- 简易弹窗提醒 -->
  <messageModal
    :title="'确认提示'"
    :show-visible="showVisible"
    @cancel="showVisible = false"
    @confirm="confirm"
  >
    <div class="messageVal">
      <InfoCircleOutlined />
      <span>{{ message }}</span>
    </div>
  </messageModal>

  <!-- 从系统添加 -->
  <addSystemModal
    v-if="pageType==='page'"
    :id="id"
    :data="addSystemModalData"
    @success="successSave"
  />
  <AddRoleNode
    v-if="pageType==='page'"
    @register="register"
    @update="upDateData"
  />
  <AddRoleModal
    @register="modalAddRoleRegister"
    @success="successSave"
  />
</template>
<script lang="ts">
import {
  defineComponent, nextTick, ref, reactive, toRefs, computed, onMounted, inject, h,
} from 'vue';
import {
  isPower, useDrawer, useModal, OrionTable,
  BasicButton, DataStatusTag,
} from 'lyra-component-vue3';
import { message, Button } from 'ant-design-vue';
import { InfoCircleOutlined, ApartmentOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import addSystemModal from './roleModal/addSystemRole.vue';
import checkDetails from './roleModal/checkmodal.vue';
import messageModal from '/@/views/pms/projectLaborer/components/MessageModal/messageModal.vue';
import newButtonModal from '/@/views/pms/projectLaborer/componentsList/buttonModal/newButtonModal.vue';
import { getProjectRoleApi, deletRoleApi, banRoleApi } from '/@/views/pms/projectLaborer/api/projectList';
import AddRoleNode from './components/AddRoleNode.vue';
import AddRoleModal from './projectRole/AddRoleModal.vue';

import { getPageApi, getProjectApi, removeApi } from '/@/views/pms/projectLaborer/api/endManagement';

export default defineComponent({
  name: 'ProjectRole',
  components: {
    AddRoleModal,
    //   提示图标
    InfoCircleOutlined,
    //   addNodeModal,
    messageModal,
    checkDetails,
    ApartmentOutlined,
    addSystemModal,
    AddRoleNode,
    OrionTable,
    BasicButton,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
    pageType: {
      type: String,
      default: 'page',
    },
  },

  setup(props) {
    const [register, { openDrawer }] = useDrawer();
    const [modalAddRoleRegister, { openModal: openAddRoleModal }] = useModal();
    const tableRef = ref(null);
    const state = reactive({
      powerData: [],
      contentHeight: 600,
      /* 搜索框value */
      searchvlaue: '',
      /* 编辑send */
      /* 多选 */
      selectedRowKeys: [],
      /* 列 */
      dataSource: [],
      addSystemModalData: {},
      /* 选择行id */
      selectedRows: [],
      showVisible: false,
      /* 简易弹窗提醒消息 */
      message: '',
      nodeData: {},
      searchData: {},
      params: {},
      /* 高度 */
      tableHeight: 400,
    });
    state.powerData = inject('powerData');
    /* 多选cb */
    const selectionChange = (selectedObject) => {
      state.selectedRowKeys = selectedObject.keys;
      state.selectedRows = selectedObject.rows;
    };
    const onSelectChange = (selectedRowKeys, selectedRows) => {
      state.selectedRowKeys = selectedRowKeys;
      state.selectedRows = selectedRows;
    };
    /* 禁用 */
    const banState = async () => {
      if (multiLengthCheckHandle()) return;
      takeEffect(0);
    };
    /* 启用 */
    const useState = async () => {
      // if (lengthCheckHandle()) return;
      if (multiLengthCheckHandle()) return;

      takeEffect(1);
    };
    /* 启用禁用handle */
    const takeEffect = async (ban) => {
      const banparams = {
        idList: state.selectedRowKeys,
        takeEffect: ban,
      };
      const love = {
        // className: 'ProjectRole',
        // moduleName: '项目管理-项目设置-项目角色',
        // type: 'UPDATE',
        // remark: `${ban === 1 ? '启用' : '禁用'}了【${state.selectedRowKeys}】`,
      };
      await banRoleApi(banparams, love);
      await getFormData();
      state.selectedRows = [];
      state.selectedRowKeys = [];
    };

    function openAddRoleModalHandle() {
      openAddRoleModal(
        true,
        {
          projectId: props.id,
          state1: '这个是打开模态框时，传的参数',
        },
      );
    }

    /* 编辑 */
    const editNode = () => {
      if (lengthCheckHandle()) return;
      let row = {
        ...state.dataSource.find((item) => item.id === state.selectedRowKeys[0]),
      };
      openDrawer(true, {
        type: 'edit',
        ...row,
      });
    };
    /* 删除 */
    const deleteNode = () => {
      if (lengthCheckHandle()) return;
      // state.selectedRows = [];
      state.message = '请确认是否对当前选中数据进行删除？';
      state.showVisible = true;
    };
    /* 简易弹窗的确定cb */
    const confirm = () => {
      deletrow();
      state.selectedRowKeys = [];
      state.selectedRows = [];
    };
    onMounted(() => {
      /* 高度变化 */
      state.tableHeight = document.body.clientHeight - 460;
      getFormData();
    });
    /* 删除操作 */
    const deletrow = () => {
      const love = {};
      deletRoleApi(state.selectedRowKeys, love)
        .then((res) => {
          message.success('删除成功');
          state.showVisible = false;
          getFormData();
        })
        .catch(() => {
          state.showVisible = false;
        });
    };
    const getFormData = async () => {
      tableRef.value?.reload();
    };
    const checkData2 = (data) => {
      state.nodeData = {
        ...[JSON.parse(JSON.stringify(data))],
      };
    };
    /* 检查选择条数fn */
    const lengthCheckHandle = () => {
      if (state.selectedRows.length > 1) {
        message.warning('请选择一条数据进行操作');
        return true;
      }
      if (state.selectedRows.length === 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };
    /* 批量检查选择条数fn */
    const multiLengthCheckHandle = () => {
      if (state.selectedRows.length === 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };

    /* 新建项目 */
    const addNode = () => {
      openDrawer(true, {
        type: 'add',
        projectId: props.id,
      });
    };

    function upDateData() {
      getFormData();
    }

    /* 批量删除 */

    const multiDelete = () => {
      if (multiLengthCheckHandle()) return;
      state.message = '请确认是否对当前选中数据进行删除？';
      state.showVisible = true;
    };
    const successSave = () => {
      getFormData();
      state.selectedRowKeys = [];
      state.selectedRows = [];
    };
    /* 查看关闭 */
    const closecheck = () => {
      state.selectedRowKeys = [];
      state.selectedRows = [];
    };
    const addSystemRoleHandle = () => {
      // console.log('从系统创建角色');
      state.addSystemModalData = { formType: 'add' };
    };

    // 生成 queryCondition
    function getListParams(params) {
      if (params.searchConditions) {
        return {
          ...params,
          queryCondition: params.searchConditions.map((item) => ({
            column: item?.[0]?.field,
            type: 'like',
            link: 'or',
            value: item?.[0]?.values?.[0],
          })),
        };
      }
      return params;
    }

    const tableOptions = {
      deleteToolButton: 'add|enable|disable|delete',
      rowSelection: {},
      // isFilter2: true,
      // filterConfigName: '',
      columns: [
        {
          title: '编号',
          dataIndex: 'number',
          key: 'number',
          align: 'left',
          slots: { customRender: 'number' },
          // sorter: true,
          ellipsis: true,
        },
        {
          title: '名称',
          dataIndex: 'name',
          key: 'name',
          align: 'left',
          customRender({ record, text }) {
            if (isPower('PMS_XMXQ_container_12_02_button_05', record.rdAuthList)) {
              h('span', {
                class: 'action-btn',
                onClick: () => checkData2(record),
              }, text);
            }
            return text;
          },
        },
        {
          title: '状态',
          dataIndex: 'takeEffectName',
        },
        {
          title: '修改人',
          dataIndex: 'modifyName',
          key: 'modifyName',
          align: 'left',
          slots: { customRender: 'modifyName' },
          // sorter: true
          // ellipsis: true
        },
        {
          title: '修改时间',
          dataIndex: 'modifyTime',
          key: 'modifyTime',
          align: 'left',
          slots: { customRender: 'modifyTime' },
          customRender({ text }) {
            return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '';
          },
        },
      ],
      api(params) {
        return getProjectRoleApi(getListParams({
          ...params,
          query: {
            projectId: props.id,
          },
          orders: [
            {
              asc: false,
              column: '',
            },
          ],
          queryCondition: [],
        }));
      },

    };

    return {
      ...toRefs(state),
      /* 多选 */
      onSelectChange,
      /* 多选变化 */
      /* 简易弹窗cb */
      confirm,
      /* 新增按钮 */
      addNode,
      dayjs,
      successSave,
      closecheck,
      /* 从系统创建角色 */
      addSystemRoleHandle,
      modalAddRoleRegister,
      isPower,
      register,
      upDateData,
      tableOptions,
      banState,
      useState,
      /* 批量删除 */
      multiDelete,
      selectionChange,
      tableRef,
      openAddRoleModalHandle,
    };
  },
});
</script>
<style lang="less" scoped>
.red {
  color: red;
}
</style>
