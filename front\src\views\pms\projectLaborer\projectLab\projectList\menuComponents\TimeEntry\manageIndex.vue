<template>
  <div class="time-entry">
    <OrionTable
      ref="tableRefPj"
      :options="tableOptionsIsPj"
      class="card-list-table"
    >
      <template
        #toolbarLeft
      >
        <BasicButton
          type="primary"
          icon="add"
          @click="addManage"
        >
          工时填报
        </BasicButton>

        <BasicButton
          type="primary"
          @click="handleSubmit"
        >
          提交
        </BasicButton>

        <BasicButton
          icon="delete"
          @click="multiDeleteManage"
        >
          删除
        </BasicButton>
      </template>
    </OrionTable>
    <AddDrawer @register="registerDrawer" />
    <EditDrawer @register="registerDrawerEdit" />
    <AddManageModal @register="registeModal" />
    <EditManage @register="registeEditModal" />
  </div>
</template>
<script lang="ts">
import {
  computed, defineComponent, h, nextTick, onMounted, provide, reactive, ref, toRefs,
} from 'vue';
import {
  BasicButton, DataStatusTag, OrionTable, useDrawer, useModal,
} from 'lyra-component-vue3';
import { message, Modal } from 'ant-design-vue';
import dayjs from 'dayjs';
import { useRoute, useRouter } from 'vue-router';
import Api from '/@/api';
import AddDrawer from './component/addDrawer.vue';

import EditDrawer from './component/editDrawer.vue';

import { useUserStore } from '/@/store/modules/user';
import AddManageModal from './component/addManage.vue';
import EditManage from './component/editManage.vue';

export default defineComponent({
  name: 'TimeEntry',
  components: {
    AddManageModal,
    OrionTable,
    BasicButton,
    AddDrawer,
    EditDrawer,
    EditManage,
  },
  setup() {
    const [registerDrawer, { openDrawer }] = useDrawer();
    const [registerDrawerEdit, { openDrawer: openDrawerEdit }] = useDrawer();
    const [registeModal, { openModal: openModalAdd }] = useModal();
    const [registeEditModal, { openModal: openModalEdit }] = useModal();

    const router = useRouter();
    const route = useRoute();
    const userStore = useUserStore();
    const werkArr = [
      '日',
      '一',
      '二',
      '三',
      '四',
      '五',
      '六',
    ];
    const currentDateContent = ref({
      currentDay: '',
      currentWeek: '',
    });
    const currentMonth = ref([]);
    const state = reactive({
      formId: '',
      isTable: false,
      dateValue: dayjs(new Date()).format('YYYY-MM-DD'),
      basicInfo: {},
      currentPage: 0,
    });
    const tableRef = ref();
    const tableRefPj = ref();
    const timeEntryFilterConfig = [];
    const projectId: string = route.query.id;
    const tableOptions = ref({
      deleteToolButton: 'add|enable|disable|delete',
      rowSelection: {},
      filterConfig: { fields: timeEntryFilterConfig },
      smallSearchField: ['title', 'number'],
      showIndexColumn: true,
      api: (params) => new Api('/pms').fetch({
        ...params,
        query: {
          projectId,
        },
      }, 'workHourFill/page', 'POST'),
      columns: [
        {
          title: '单据编号',
          dataIndex: 'number',
          slots: { customRender: 'number' },
          align: 'left',
          minWidth: 200,
          sorter: {},
        },
        {
          title: '类型',
          align: 'left',
          dataIndex: 'workHourType',
          slots: { customRender: 'workHourType' },
          width: 200,
          sorter: {},
        },

        {
          title: '标题',
          align: 'left',
          dataIndex: 'title',
          slots: { customRender: 'title' },
          width: 200,
          sorter: {},
        },
        {
          title: '单据状态',
          dataIndex: 'status',
          align: 'left',
          width: 100,
          customRender({ record }) {
            return record.dataStatus ? h(DataStatusTag, {
              statusData: record.dataStatus,
            }) : '';
          },
          sorter: {},
        },

        {
          title: '审批人',
          align: 'left',
          dataIndex: 'assigneeName',
          width: 100,
          sorter: {},
        },
        {
          title: '填报发起日期',
          align: 'left',
          dataIndex: 'createTime',
          slots: { customRender: 'createTime' },
          width: 120,
          customRender({ text }) {
            return text ? dayjs(text).format('YYYY-MM-DD') : '';
          },
          sorter: {},
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 150,
          align: 'left',
          fixed: 'right',
          slots: { customRender: 'action' },
        },
      ],

      actions: [

        {
          text: '查看',
          // isShow: (record) => record.controlsType === 'details',
          onClick(record) {
            router.push({
              name: 'TimeEntryDetail',
              query: {
                id: record.id,
                type: 1,
              },
            });
          },
        },
        {
          text: '编辑',
          // isShow: (record) => record.controlsType !== 'edit' && isPower('WLGL_container_button_15', powerData),
          onClick(record) {
            openDrawerEdit(true, {
              ...record,
              type: 'edit',
            });
          },
        },
        {
          text: '删除',
          onClick(record) {
            Modal.confirm({
              title: '是否删除当前单据，删除后可重新添加?',
              onOk() {
                deleteRows([record.id]);
              },
            });
          },

        },

      ],
    });

    const tableOptionsIsPj = ref({
      deleteToolButton: 'add|enable|disable|delete',
      rowSelection: {},
      filterConfig: { fields: timeEntryFilterConfig },
      smallSearchField: ['title', 'number'],
      showIndexColumn: true,
      api: (params) => new Api('/pms').fetch({
        ...params,
        query: {
          projectId,
        },
      }, 'manage/workHourFill/page', 'POST'),
      columns: [
        {
          title: '单据编号',
          dataIndex: 'number',
          align: 'left',
        },
        {
          title: '成员姓名',
          dataIndex: 'memberName',
          align: 'left',
        },
        {
          title: '成员角色',
          dataIndex: 'memberRoleName',
          align: 'left',
          minWidth: 200,
          sorter: {},
        },
        {
          title: '开始时间',
          align: 'left',
          dataIndex: 'startDate',
          width: 120,
          customRender({ text }) {
            return text ? dayjs(text).format('YYYY-MM-DD') : '';
          },
          sorter: {},
        },
        {
          title: '结束时间',
          align: 'left',
          dataIndex: 'endDate',
          width: 120,
          customRender({ text }) {
            return text ? dayjs(text).format('YYYY-MM-DD') : '';
          },
          sorter: {},
        },
        {
          title: '工时时长（小时）',
          align: 'left',
          dataIndex: 'workHour',
        },
        {
          title: '单据状态',
          dataIndex: 'status',
          align: 'left',
          width: 100,
          customRender({ record }) {
            return record.dataStatus ? h(DataStatusTag, {
              statusData: record.dataStatus,
            }) : '';
          },
          sorter: {},
        },
        {
          title: '创建时间',
          align: 'left',
          dataIndex: 'createTime',
          slots: { customRender: 'createTime' },
          width: 120,
          customRender({ text }) {
            return text ? dayjs(text).format('YYYY-MM-DD') : '';
          },
          sorter: {},
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 150,
          align: 'left',
          fixed: 'right',
          slots: { customRender: 'action' },
        },
      ],

      actions: [

        {
          text: '查看',
          onClick(record) {
            router.push({
              name: 'TimeEntryDetail',
              query: {
                id: record.id,
                type: 2,
              },
            });
          },
        },
        {
          text: '编辑',
          onClick(record) {
            openModalEdit(true, {
              ...record,
              id: record.id,
              type: 'edit',
            });
          },
        },
        {
          text: '删除',
          onClick(record) {
            Modal.confirm({
              title: '是否删除当前单据，删除后可重新添加?',
              onOk() {
                deleteRowsManage([record.id]);
              },
            });
          },

        },

      ],
    });
    onMounted(() => {
      getMonth(dayjs(new Date()).format('YYYY年MM月'));
      getBasicInfo();
    });
    function getMonth(month) {
      new Api('/pms').fetch({
        month,
        projectId,
      }, 'workHourFill/byMonth', 'GET').then((res) => {
        currentMonth.value = res;
      }).catch((err) => {
      });
    }
    function addTableNode() {
      openDrawer(true, {});
    }
    const selectChange = (value) => {
      new Api('/pms').fetch({
        workDate: dayjs(value.valueOf()).format('YYYY-MM-DD'),
        projectId,
      }, 'workHourFill/byDate', 'GET').then((res) => {
        currentDateContent.value = res;

        currentDateContent.value.currentDay = `${dayjs(value.valueOf()).format('DD')}日`;
        currentDateContent.value.currentWeek = `${dayjs(value.valueOf()).day()}`;
      }).catch((err) => {
      });
    };
    function changeYear(val, onChange, current) {
      return onChange(current.year(+val));
    }
    function changeMonth(val, onChange, current, type = 'add') {
      let newVal = 0;
      if (type === 'add') {
        newVal = val + 1;
      } else {
        newVal = val - 1;
      }
      return onChange(current.month(parseInt(String(newVal), 10)));
    }

    function handleSubmit() {
      if (tableRefPj.value.getSelectRows().length > 0) {
        new Api('/pms').fetch(tableRefPj.value.getSelectRows().map((item) => item.id), 'manage/workHourFill/batchSubmit', 'PUT').then((res) => {
          message.success('提交成功');
          tableRefPj.value.reload();
        }).catch((err) => {
        });
      } else {
        message.warning('请选择工时提交！');
      }
    }

    async function getBasicInfo() {
      const result = await new Api(`/pms/project/detail/${projectId}`).fetch('', '', 'GET');
      state.basicInfo = result || {};
    }

    provide(
      'basicInfo',
      computed(() => state.basicInfo),
    );

    function multiDelete() {
      Modal.confirm({
        title: '删除提示',
        content: '是否删除当前单据，删除后可重新添加？',
        onOk: () => deleteRows(tableRef.value.getSelectRows().map((item) => item.id)),
      });
    }
    function multiDeleteManage() {
      if (tableRefPj.value.getSelectRowKeys().length === 0) {
        return message.error('请选择一条数据');
      }
      Modal.confirm({
        title: '删除提示',
        content: '是否删除当前单据，删除后可重新添加？',
        onOk: () => deleteRowsManage(tableRefPj.value.getSelectRows().map((item) => item.id)),
      });
    }
    function deleteRows(ids) {
      new Api('/pms').fetch(ids, 'workHourFill', 'DELETE').then((res) => {
        message.success('删除数据成功');
        tableRef.value.reload();
      }).catch((err) => {
      });
    }

    function deleteRowsManage(ids) {
      new Api('/pms').fetch(ids, 'manage/workHourFill', 'DELETE').then((res) => {
        message.success('删除数据成功');
        tableRefPj.value.reload();
      }).catch((err) => {
      });
    }
    const getListData = (value) => {
      // console.log(dayjs(value.valueOf()).format('YYYY-MM-DD'));
      let listData;
      for (let i in currentMonth.value) {
        if (dayjs(value.valueOf()).format('YYYY-MM-DD') === currentMonth.value[i].workDate) {
          if (currentMonth.value[i].status === 101) {
            listData = [{ color: 'rgba(217, 217, 217, 1)' }];
          } else if (currentMonth.value[i].status === 130) {
            listData = [{ color: '#87d068' }];
          } else if (currentMonth.value[i].status === 110) {
            listData = [{ color: '#2db7f5' }];
          }
        }
      }
      return listData || [];
    };
    function updateTable() {
      nextTick(() => {
        tableRef.value.reload();
      });
    }
    function updateTablePj() {
      nextTick(() => {
        tableRefPj.value.reload();
      });
    }
    function addManage() {
      openModalAdd(true, {});
    }
    function calculateCurrentPage(step) {
      step === 1 ? state.currentPage++ : state.currentPage--;
    }
    provide('updateNodePages', updateTable);
    provide('updateTablePj', updateTablePj);
    return {
      ...toRefs(state),
      tableRef,
      tableOptions,
      selectChange,
      changeYear,
      changeMonth,
      show: ref<boolean>(true),
      addTableNode,
      openDrawer,
      registerDrawer,
      multiDelete,
      handleSubmit,
      currentDateContent,
      currentMonth,
      getListData,
      userStore,
      tableOptionsIsPj,
      tableRefPj,
      openDrawerEdit,
      registerDrawerEdit,
      werkArr,
      multiDeleteManage,
      calculateCurrentPage,
      registeModal,
      openModalAdd,
      addManage,
      registeEditModal,
      updateTablePj,
    };
  },
});
</script>
<style lang="less" scoped>

:deep(.card-list-table) {
  .ant-btn-group {
    margin-left: auto;

    .ant-btn + .ant-btn {
      margin-left: 0;
    }

    & + .card-list-table {
      width: auto;
      flex: 0;

      .ant-input-search {
        width: 220px;
      }
    }
  }
}
.card-list-calender{
  .sie-calender{
    width: 700px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    .sie-calender-title{
      padding: 10px 0;
      text-align: center;
    }
    .active {

    }
  }
}
.current-date{
  position: relative;
  z-index: 2;
  cursor: pointer;
  &:hover{
    color: red;
  }
}
:deep(.table-other-content) {
  display:block !important;
}
:deep(.ant-picker-cell) {
  cursor: default;
}

.current-date-content {
  .blue {
    color:#0854a5;
  }
  .left {
    text-align: left;
  }
  .right {
    text-align: right;
  }
}
.time-entry {
  height:100%;
  width:100%;
  .card-list-calender {
    height:100%;
    background: #fff;
    border:1px solid rgba(0,0,0,.06);
  }
}
</style>
