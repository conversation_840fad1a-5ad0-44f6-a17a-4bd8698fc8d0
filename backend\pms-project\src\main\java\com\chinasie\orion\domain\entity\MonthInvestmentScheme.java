package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * MonthInvestmentScheme Entity对象
 *
 * <AUTHOR>
 * @since 2023-05-16 14:17:18
 */
@TableName(value = "pms_month_investment_scheme")
@ApiModel(value = "MonthInvestmentScheme对象", description = "月投资计划")
@Data
public class MonthInvestmentScheme extends ObjectEntity implements Serializable {

    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 实际
     */
    @ApiModelProperty(value = "实际")
    @TableField(value = "actual")
    private BigDecimal actual = new BigDecimal("0");

    /**
     * 预计
     */
    @ApiModelProperty(value = "预计")
    @TableField(value = "predicate")
    private BigDecimal predicate = new BigDecimal("0");

    /**
     * 年度投资计划Id
     */
    @ApiModelProperty(value = "年度投资计划Id")
    @TableField(value = "year_id")
    private String yearId;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    @TableField(value = "name")
    private String name;


    /**
     * 是否已执行
     */
    @ApiModelProperty(value = "是否已执行")
    @TableField(value = "has_do")
    private Integer hasDo;


    /**
     * 关联计划反馈
     */
    @ApiModelProperty(value = "关联计划反馈")
    @TableField(value = "feedback_id")
    private String feedbackId;


}
