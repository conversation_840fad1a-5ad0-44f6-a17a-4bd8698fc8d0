<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
  >
    <template
      v-if="pageType==='page'"
      #toolbarLeft
    >
      <div
        class="businessRiskMainTable_add"
      >
        <BasicButton
          v-if="isPower('PMS_XMXQ_container_07_01_01_button_01',powerData)"
          type="primary"
          icon="add"
          @click="addTableNode"
        >
          新增风险
        </BasicButton>
      </div>
    </template>
    <template #action="{record}">
      <BasicTableAction
        :actions="actionsBtn"
        :record="record"
      />
    </template>
  </OrionTable>
  <AddTableNode
    v-if="pageType==='page'"
    :formId="formId"
    @register="register"
    @update="updateData"
  />
</template>
<script lang="ts">
import {
  defineComponent, h, inject, reactive, ref, toRefs,
} from 'vue';

import { useRouter } from 'vue-router';
import {
  BasicButton, BasicTableAction, isPower, ITableActionItem, OrionTable, useDrawer,
} from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import { useQiankun } from '/@/utils/qiankun/useQiankun';
import Api from '/@/api';
import dayjs from 'dayjs';
import AddTableNode from './components/AddTableNode.vue';
import {
  openFormDrawer,
} from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/questionManagement/utils';
import AddTableModal from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/questionManagement/components/AddTableModal.vue';

export default defineComponent({
  name: 'BusinessRisk',
  components: {
    BasicTableAction,
    OrionTable,
    // AInputSearch: Input.Search,
    AddTableNode,
    BasicButton,
  },
  props: {
    pageType: {
      type: String,
      default: 'page',
    },
    // 设置对象的文件夹地址
    modelName: {
      type: String,
      default: 'pms',
    },
  },
  setup(props) {
    const { mainRouter } = useQiankun();
    const formData: any = inject('formData', {});
    const powerData = inject('powerData');
    const state = reactive({
      formId: formData?.value?.id,
      tableSearchVal: '',
      btnList: [
        'check',
        'open',
        'edit',
        'delete',
        'search',
      ],
      searchParams: {},
    });

    const [register, { openDrawer }] = useDrawer();
    const tableRef = ref(null);
    const tableOptions = ref({
      deleteToolButton: isPower('PMS_XMXQ_container_07_01_01_button_02', powerData) ? 'add|enable|disable' : 'add|enable|disable|delete',
      rowSelection: props.pageType === 'page' ? {} : false,
      showSmallSearch: true,
      showIndexColumn: false,
      isFilter2: true,
      filterConfigName: 'PMS_PROJECTMANAGE_RISKMANAGE_REALITYRISK',
      api: (params) => new Api('/pms').fetch({
        ...params,
        query: {
          projectId: formData?.value?.id,
        },
        power: {
          pageCode: 'PMS0004',
          containerCode: 'PMS_XMXQ_container_07_01_02',
          headContainerCode: 'PMS_XMXQ_container_07_01_01',
        },
      }, 'risk-management/getPage', 'POST'),
      async batchDeleteApi({ ids }) {
        return new Api('/pas').fetch(ids, 'risk-management/removeBatch', 'DELETE').then((res) => {
          message.success('删除数据成功');
          tableRef.value.reload();
        });
      },
      columns: [
        {
          title: '名称',
          dataIndex: 'name',
          minWidth: 200,
          customRender({ record, text }) {
            if (isPower('PMS_XMXQ_container_07_01_02_button_03', powerData)) {
              return h('span', {
                class: 'action-btn',
                onClick: () => openDetails(record),
              }, text);
            }
            return text;
          },
        },
        {
          title: '编号',
          dataIndex: 'number',
          width: 150,
        },
        {
          title: '风险类型',
          dataIndex: 'riskTypeName',
          key: 'riskType',
          width: '100px',
          align: 'left',
          // sorter: true,
          ellipsis: true,
        },
        {
          title: '发生概率',
          dataIndex: 'riskProbabilityName',
          width: '90px',
          customRender: ({ record }) => h('span', {
            class: record.riskProbability === '5' || record.riskProbability === '4' ? 'red-text' : '',
          }, record.riskProbabilityName),
        },
        {
          title: '影响程度',
          dataIndex: 'riskInfluenceName',
          width: '80px',
          customRender: ({ record }) => h('span', {
            class: record.riskInfluence === '5' || record.riskInfluence === '4' ? 'red-text' : '',
          }, record.riskInfluenceName),
        },
        {
          title: '预估发生时间',
          dataIndex: 'predictStartTimeName',
          key: 'predictStartTimeName',
          width: '130px',
          align: 'left',
          // sorter: true,
          ellipsis: true,
        },
        {
          title: '是否典型风险',
          dataIndex: 'isTypicalRisk',
          width: '130px',
          customRender: ({
            text,
          }) => (text ? '是' : '否'),
        },
        {
          title: '应对策略',
          dataIndex: 'copingStrategyName',
          key: 'copingStrategy',

          width: '100px',
          align: 'left',
          // sorter: true,
          ellipsis: true,
        },
        {
          title: '状态',
          dataIndex: 'status',
          align: 'left',
          width: 150,
          slots: { customRender: 'status' },
        },
        {
          title: '是否转问题',
          dataIndex: 'isToQuestion',
          width: 100,
          customRender: ({
            text,
          }) => (text ? '是' : '否'),
        },
        {
          title: '负责人',
          dataIndex: 'principalName',
          width: 100,
        },
        {
          title: '修改日期',
          ellipsis: true,
          dataIndex: 'modifyTime',
          customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : ''),
          width: 180,
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 150,
          align: 'left',
          fixed: 'right',
          slots: { customRender: 'action' },
        },
      ],
      beforeFetch,
    });

    function beforeFetch(T) {
      return {
        ...T,
        query: {
          ...state.searchParams,
          projectId: formData?.value?.id,
        },
        queryCondition: [
          {
            column: 'name',
            type: 'like',
            link: 'or',
            value: state.tableSearchVal,
          },
          {
            column: 'number',
            type: 'like',
            link: 'or',
            value: state.tableSearchVal,
          },
        ],
      };
    }

    const addTableNode = (type: string, record: any) => {
      if (type === 'question') {
        openFormDrawer(AddTableModal, {
          id: record.id,
          projectId: formData?.value?.id,
          fromObjName: formData?.value?.name,
          modelName: 'pms',
          isQuestion: true,
          formType: 'Add',
          dataSource: record,
        }, updateData);
      } else {
        let drawerData: any = {
          projectId: formData?.value?.id,
          fromObjName: formData?.value?.name,
          modelName: props.modelName,
        };
        let dataSource = tableRef.value.getDataSource();
        if (dataSource.length > 0) {
          drawerData.dirId = dataSource[0].dirId;
        }
        openDrawer(true, {
          type: 'add',
          data: drawerData,
        });
      }
    };
    const router = useRouter();
    const openDetails = (record) => {
      router.push({
        name: 'PMSRiskManagementDetails',
        params: {
          id: record.id,
        },
      });
    };

    const updateData = () => {
      tableRef.value.reload();
    };
    const searchTable = (params) => {
      state.searchParams = params;
      if (params.name) {
        state.tableSearchVal = params.name;
      }
      tableRef.value.reload();
    };
    const searchForm = (val) => {
      tableRef.value.reload();
    };

    const actionsBtn: ITableActionItem<any>[] = [
      {
        text: '编辑',
        isShow: () => isPower('PMS_XMXQ_container_07_01_02_button_01', powerData),
        onClick(record: any) {
          openDrawer(true, {
            type: 'edit',
            data: record,
          });
        },
      },
      {
        text: '删除',
        isShow: () => isPower('PMS_XMXQ_container_07_01_02_button_02', powerData),
        modal(record: any) {
          return new Api('/pas').fetch([record.id], 'risk-management/removeBatch', 'DELETE').then((res) => {
            message.success('删除数据成功');
            tableRef.value.reload();
          });
        },
      },
      {
        text: '转问题',
        isShow: () => isPower('PMS_XMXQ_container_07_01_02_button_04', powerData),
        onClick(record: any) {
          addTableNode('question', record);
        },
      },
    ];

    return {
      ...toRefs(state),
      tableOptions,
      tableRef,
      addTableNode,
      register,
      openDetails,
      updateData,
      searchForm,
      searchTable,
      actionsBtn,
      powerData,
      isPower,
    };
  },
});
</script>
<style lang="less" scoped>
:deep(.red-text) {
  color: red;
}
</style>
