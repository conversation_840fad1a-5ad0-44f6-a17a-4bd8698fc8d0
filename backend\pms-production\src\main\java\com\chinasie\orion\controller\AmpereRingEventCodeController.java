package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.AmpereRingEventCodeDTO;
import com.chinasie.orion.domain.entity.AmpereRingEventCode;
import com.chinasie.orion.domain.vo.AmpereRingBoardConfigKpiVO;
import com.chinasie.orion.domain.vo.AmpereRingEventCodeVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.AmpereRingEventCodeService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 22 日
 **/
@Api(tags = "安质环码表管理")
@RequestMapping("/ampere/ring/event/code")
@RestController
public class AmpereRingEventCodeController {
    @Autowired
    private AmpereRingEventCodeService ampereRingEventCodeService;

    /**
     * 查询事件列表
     */
    @ApiOperation("查询事件列表")
    @PostMapping("/query/list")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询事件列表", type = "AmpereRingEventCode", subType = "列表查询", bizNo = "")
    public ResponseDTO<List<AmpereRingEventCodeVO>> queryList(@RequestBody AmpereRingEventCodeVO ampereRingEventCodeVO) {
        List<AmpereRingEventCodeVO> kpiVOS = ampereRingEventCodeService.queryList(ampereRingEventCodeVO);
        return new ResponseDTO<>(kpiVOS);
    }

    /**
     * 查询事件分页
     */
    @ApiOperation("查询事件列表")
    @PostMapping("/query/page")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询事件分页列表", type = "AmpereRingEventCode", subType = "列表查询", bizNo = "")
    public ResponseDTO<Page<AmpereRingEventCodeVO>> queryPage(@RequestBody Page<AmpereRingEventCodeDTO> pageRequest) {
        Page<AmpereRingEventCodeVO> kpiVOS = ampereRingEventCodeService.queryPage(pageRequest);
        return new ResponseDTO<>(kpiVOS);
    }

    /**
     * 查询事件类型
     */
    @ApiOperation(("查询事件类型"))
    @PostMapping("/query/eventType")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询事件类型", type = "AmpereRingEventCode", subType = "查询事件类型", bizNo = "")
    public ResponseDTO<List<AmpereRingEventCodeVO>> queryEventType(@RequestBody AmpereRingEventCodeVO eventCodeVO) {
        List<AmpereRingEventCodeVO> codeVOS = ampereRingEventCodeService.queryEventType(eventCodeVO);
        return new ResponseDTO<>(codeVOS);
    }

}
