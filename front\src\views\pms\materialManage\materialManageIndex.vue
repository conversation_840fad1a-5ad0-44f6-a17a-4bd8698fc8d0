<script setup lang="ts">
import {
  BasicButton, isPower, Layout, OrionTable,
} from 'lyra-component-vue3';
import {
  computed, nextTick, ref, Ref, unref,
} from 'vue';
import { FormItem, RadioGroup, Select } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { typeOptions } from './options';
import { getColumns } from './columns';
import { openMaterialEnterForm, openMaterialLeaveForm } from './utils';
import Api from '/@/api';
import { useBaseData } from '/@/views/pms/userManage/hooks';
import { usePagePower } from '/@/views/pms/hooks';

const {
  basePlaceCode,
  baseOptions,
  fetching,
} = useBaseData();

const router = useRouter();
const type: Ref<'ledger' | 'manage'> = ref('manage');

const actions = computed(() => {
  if (type.value === 'manage') {
    return [
      {
        text: '编辑',
        // isShow: false,
        isShow: (record) => isPower('PMS_WZGL_container_01_02_button_01', record?.rdAuthList),
        onClick(record: { id: string }) {
          openMaterialEnterForm(record, updateTable);
        },
      },
      {
        text: '出库',
        // isShow: false,
        isShow: (record) => isPower('PMS_WZGL_container_01_02_button_02', record?.rdAuthList) && record?.stockNum > 0,
        onClick(record: { id: string }) {
          openMaterialLeaveForm(record, updateTable);
        },
      },
      {
        text: '查看',
        onClick: (record: { id: string }) => navDetails(record?.id),
      },
    ];
  }
  return [
    {
      text: '查看',
      isShow: (record) => isPower('PMS_WZGL_container_02_01_button_01', record?.rdAuthList),
      onClick: (record: { id: string }) => navDetails(record?.id),
    },
  ];
});

const tableRef: Ref = ref();
const selectedKeys: Ref<string[]> = ref([]);
const selectedRows: Ref<any[]> = ref([]);
const tableOptions = {
  rowSelection: {
    onChange(keys: string[], rows: any[]) {
      selectedKeys.value = keys || [];
      selectedRows.value = rows || [];
    },
  },
  smallSearchField: ['assetName', 'productCode'],
  api: (params: any) => new Api(`/pms/${type.value === 'manage' ? 'material-manage' : 'material-in-out-manage'}`).fetch({
    ...params,
    query: {
      baseCode: unref(basePlaceCode),
    },
    power: {
      pageCode: 'PMSMaterialManage',
      containerCode: type.value === 'manage' ? 'PMS_WZGL_container_01_02' : 'PMS_WZGL_container_02_01',
    },
  }, 'page', 'POST'),
  showToolButton: false,
  actions,
};

async function updateTable() {
  await nextTick();
  tableRef.value.reload();
}

const toolButtons = computed(() => (type.value === 'manage' ? [
  {
    event: 'add',
    text: '物资入库',
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
    powerCode: 'PMS_WZGL_container_01_01_button_01',
    disabled: baseOptions.value.length === 0,
  },
] : []));

function handleToolButton(event: string) {
  switch (event) {
    case 'add':
      openMaterialEnterForm({
        baseCode: unref(basePlaceCode),
      }, updateTable);
      break;
    case 'leave':
      openMaterialLeaveForm(null, updateTable);
      break;
  }
}

function navDetails(id: string) {
  router.push({
    name: 'PMSMaterialManageDetails',
    params: {
      id,
    },
    query: {
      type: type.value,
    },
  });
}

const { powerData, getPowerDataHandle } = usePagePower();

</script>

<template>
  <Layout
    v-get-power="{pageCode:'PMSMaterialManage', getPowerDataHandle}"
    v-loading="fetching"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      v-if="!fetching"
      ref="tableRef"
      :key="type"
      class="radio-button-table"
      :options="tableOptions"
      :columns="getColumns(type,navDetails)"
    >
      <template #toolbarLeft>
        <BasicButton
          v-for="item in toolButtons"
          :key="item.event"
          v-is-power="[item.powerCode]"
          v-bind="item"
          @click="handleToolButton(item.event)"
        >
          {{ item.text }}
        </BasicButton>
        <FormItem
          label="基地名称"
          class="mb0i"
        >
          <Select
            v-model:value="basePlaceCode"
            style="width: 200px"
            placeholder="请选择"
            :options="baseOptions"
            :fieldNames="{label:'name',value:'code'}"
            @change="updateTable()"
          />
        </FormItem>
        <RadioGroup
          v-model:value="type"
          class="mla"
          option-type="button"
          button-style="solid"
          :options="typeOptions"
        />
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">
:deep(.radio-button-table) {
  width: auto;
  flex: 0;

  .ant-input-search {
    width: 215px;
    margin-left: 12px;
  }
}

.mla {
  margin-left: auto;
}

.mb0i {
  margin-bottom: 0 !important;
}
</style>
