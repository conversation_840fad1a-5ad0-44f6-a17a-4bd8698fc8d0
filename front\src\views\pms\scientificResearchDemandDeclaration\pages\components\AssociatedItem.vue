<script setup lang="ts">
import {
  Layout,
  OrionTable,
  BasicButton,
  BasicUpload,
  downLoadById,
  ITableActionItem,
  BasicTableAction,
  DataStatusTag,
  useDrawer, isPower,
} from 'lyra-component-vue3';
import { useRoute } from 'vue-router';
import {
  inject, onMounted, ref, Ref, unref, h, computed, reactive,
} from 'vue';
import Api from '/@/api';
import { Modal, Progress } from 'ant-design-vue';
import dayjs from 'dayjs';
import AddTableNode from './components/addNode.vue';
import { declarationData, declarationDataId } from '../keys';
const state = reactive({
  searchvlaue: '',
  showVisible: false,
  message: '',
  powerData: [],
  nodeData: [],
  // 搜索弹窗
  searchModalVisible: false,
  searchData: {},
  params: {},
  queryCondition: [],
  searchStatus: '',
  btnList: [
    {
      type: 'edit',
      powerCode: 'PMS_XMLB_container_01_button_01',
    },
    {
      type: 'delete',
      powerCode: 'PMS_XMLB_container_01_button_02',
    },
  ],
});

const dataId: Ref = inject(declarationDataId);
const data = inject(declarationData);
const route = useRoute();
const tableRef: Ref = ref();
const uploadRef: Ref = ref();
const selectRows: Ref<any[]> = ref([]);
const [registerAdd, { openDrawer: openDrawerAdd }] = useDrawer();
const tableOptions = {
  showToolButton: false,
  rowSelection: {},
  api: (params) => new Api('/pms/project/getPage').fetch({
    ...params,
    query: {
      scientificDeclareId: route.query.id,
    },
  }, '', 'POST').then((res) =>
    // state.powerData = res?.headAuthList;
    res),
  columns: [
    {
      title: '项目编号',
      dataIndex: 'number',
      width: '100px',
      slots: { customRender: 'number' },
    },
    {
      title: '项目名称',
      dataIndex: 'name',
      minWidth: 220,
      customRender({ record, text }) {
        return h(
          'span',
          {
            // class: computed(() => (isPower('PMS_XMLB_container_01_button_03', record?.rdAuthList) ? 'action-btn' : '')).value,
            title: text,
            onClick(e) {
              // if (isPower('PMS_XMLB_container_01_button_03', record?.rdAuthList)) {
              //   toDetails(record);
              // }
              e.stopPropagation();
            },
          },
          text,
        );
      },
    },
    {
      title: '是否需要申报',
      dataIndex: 'isDeclare',
      width: '150px',
      customRender({ text }) {
        return text ? '是' : text === false ? '否' : '--';
      },
    },
    {
      title: '项目进度',
      dataIndex: 'schedule',
      width: '170px',
      slots: { customRender: 'schedule' },
    },
    {
      title: '状态',
      dataIndex: 'statusIdName',
      width: '100px',
      slots: { customRender: 'statusIdName' },
    },
    {
      title: '项目经理',
      dataIndex: 'pm',
      width: '120px',
      slots: { customRender: 'pm' },
    },
    {
      title: '立项日期',
      dataIndex: 'projectApproveTime',
      width: '120px',
      slots: { customRender: 'projectApproveTime' },
    },
    {
      title: '开始日期',
      dataIndex: 'projectStartTime',
      width: '120px',
      slots: { customRender: 'projectStartTime' },
    },
    {
      title: '结束日期',
      dataIndex: 'projectEndTime',
      width: '120px',
      slots: { customRender: 'projectEndTime' },
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 100,
      align: 'left',
      fixed: 'right',
      slots: { customRender: 'action' },
    },
  ],
};
const actionsBtn: ITableActionItem[] = [
  {
    text: '编辑',
    // isShow: computed(() => state.btnList.some((item) => item.type === 'edit')),
    // isShow: computed((record) => isPower('PMS_XMLB_container_01_button_01', record?.rdAuthList)),
    onClick(record: any) {
      openDrawerAdd(true, {
        type: 'edit',
        id: record.id,
        projectType: record.projectType || '',
      });
    },
  },
  {
    text: '删除',
    // isShow: computed(() => state.btnList.some((item) => item.type === 'delete')),
    // isShow: computed((record) => isPower('PMS_XMLB_container_01_button_02', record?.rdAuthList)),
    modal(record: any) {
      // return deleteRecords([record.id]).then(() => {
      //   reloadTable();
      // });
    },
  },
];
function getListParams(params) {
  // if (ids?.length) {
  //   params.query = {
  //     ids,
  //     jumpFlag: true,
  //   };
  // }
  return params;
  // if (params.searchConditions) {
  //   return {
  //     ...params,
  //     queryCondition: params.searchConditions.map((item) => ({
  //       column: item?.[0]?.field,
  //       type: 'like',
  //       link: 'or',
  //       value: item?.[0]?.values?.[0],
  //     })),
  //   };
  // }
  // return params;
}

function updateTable() {
  tableRef.value.reload();
}

// 打开文件上传弹窗
function onUploadFile() {
  uploadRef.value.openModal(true);
}

// 保存回调
async function saveChange(successAll, cb) {
  const params = successAll.map((item) => ({
    ...item.result,
    dataId: unref(dataId),
    projectId: data.value?.projectId,
  }));
  await cb(new Api('/pms/projectDeclareFileInfo/meeting').fetch(params, 'saveBatch', 'POST'));
  updateTable();
}

// 表格多选
function selectionChange({ rows }) {
  selectRows.value = rows;
}

// 批量删除
function batchDelete(ids) {
  let projectId = {
    projectIds: ids,
  };
  return new Promise((resolve, reject) => {
    new Api(`/pms/scientificResearchDemandDeclare/delete/relateProject/${unref(dataId)}`).fetch(ids, '', 'PUT')
      .then(() => {
        updateTable();
        resolve(true);
      })
      .catch(() => {
        reject();
      });
  });
}
function getTableAction() {
  const tableAction = unref(tableRef);
  if (!tableAction) {
    throw new Error('内部错误');
  }
  return tableAction;
}
const successSave = () => {
  getTableAction().reload({
    page: 1,
  });
  getTableAction().clearSelectedRowKeys();
  state.searchStatus = '';
};
// 批量删除
function handleBatchDel() {
  Modal.confirm({
    title: '删除提示',
    content: '确认要删除已选择的记录？',
    onOk: () => batchDelete(selectRows.value.map((item) => item.id)),
  });
}
</script>

<template>
  <Layout
    :options="{ body: { scroll: true } }"
    contentTitle="关联项目"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      :onSelectionChange="selectionChange"
    >
      <template #toolbarLeft>
        <BasicButton
          icon="sie-icon-del"
          :disabled="selectRows.length===0"
          @click="handleBatchDel"
        >
          批量删除
        </BasicButton>
      </template>

      <template #projectApproveTime="{ text }">
        {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
      </template>
      <template #projectStartTime="{ text }">
        {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
      </template>
      <template #projectEndTime="{ text }">
        {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
      </template>

      <template #schedule="{ text }">
        <Progress
          v-if="text < 100"
          :percent="text"
          size="small"
          style="padding-right: 40px"
        />
        <Progress
          v-else-if="(text = 100)"
          size="small"
          style="padding-right: 40px"
          :stroke-color="{
            from: '#67af64',
            to: '#63c3c2'
          }"
          :percent="text"
          status="active"
        />
      </template>

      <template #statusIdName="{ record }">
        <DataStatusTag :status-data="record?.dataStatus" />
      </template>

      <template #action="{record}">
        <BasicTableAction
          :actions="actionsBtn"
          :record="record"
        />
      </template>
    </OrionTable>
    <AddTableNode
      @update="successSave"
      @register="registerAdd"
    />
  </Layout>
</template>

<style scoped lang="less">

</style>
