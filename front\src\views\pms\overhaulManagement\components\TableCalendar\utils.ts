// 获取某年的年月日数组
import dayjs from 'dayjs';

export function getYearTimestamps(year): {
    yearTimestamps: object,
    yearQuarterTimestamps: Array<Array<number>>
} {
  const isLeapYear = (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0;
  const monthDays = [
    31,
    isLeapYear ? 29 : 28,
    31,
    30,
    31,
    30,
    31,
    31,
    30,
    31,
    30,
    31,
  ];
  const yearTimestamps = {};
  const yearQuarterTimestamps = [
    [],
    [],
    [],
    [],
  ]; // 四个季度的数组

  for (let month = 0; month < 12; month++) {
    for (let day = 1; day <= monthDays[month]; day++) {
      const timestamp = dayjs(`${year}-${month + 1}-${day}`).valueOf();
      yearTimestamps[timestamp] = [];
      yearQuarterTimestamps[parseInt(month / 3)].push(timestamp); // 根据月份确定季度并添加时间戳
    }
  }

  return {
    yearTimestamps,
    yearQuarterTimestamps,
  };
}
