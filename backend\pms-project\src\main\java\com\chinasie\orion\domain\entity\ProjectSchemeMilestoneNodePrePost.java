package com.chinasie.orion.domain.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ProjectSchemePrePost
 *
 * @author: yangFy
 * @date: 2023/4/18
 * @description:
 * <p>
 * 项目计划前后置关系 entity
 * </p>
 */
@Data
@TableName(value = "pmsx_project_scheme_milestone_node_pre_post")
@ApiModel(value = "ProjectSchemeMilestoneNodePrePost对象", description = "项目计划模板前后置关系")
public class ProjectSchemeMilestoneNodePrePost extends ObjectEntity implements Serializable {

    /**
     * 前置计划Id
     */
    @ApiModelProperty(value = "前置计划Id")
    @TableField(value = "pre_scheme_id")
    private String preSchemeId;

    /**
     *后置计划Id
     */
    @ApiModelProperty(value = "后置计划Id")
    @TableField(value = "post_scheme_id")
    private String postSchemeId;

    /**
     * 项目计划id
     */
    @ApiModelProperty(value = "项目计划id")
    @TableField(value = "project_scheme_id")
    private String projectSchemeId;


    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "template_id")
    private String templateId;
    /**
     * 前后置类型
     */
    @ApiModelProperty(value = "前后置类型")
    @TableField(value = "type")
    private Integer type;
}
