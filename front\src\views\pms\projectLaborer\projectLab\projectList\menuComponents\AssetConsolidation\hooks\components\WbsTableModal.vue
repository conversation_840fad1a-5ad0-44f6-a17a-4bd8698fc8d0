<script setup lang="ts">
import { OrionTable } from 'lyra-component-vue3';
import Api from '/@/api';
import { get } from 'lodash-es';
import { ref } from 'vue';
import { message } from 'ant-design-vue';

const props = withDefaults(defineProps<{
  record:Record<string, any>
}>(), {
  record: () => ({}),
});

const tableRef = ref();
const tableOptions = {
  showToolButton: false,
  isSpacing: true,
  pagination: false,
  showTableSetting: false,
  rowSelection: {
  },
  columns: [
    {
      title: '工作分解结构元素',
      dataIndex: 'wbsElement',
      width: 200,
      fixed: 'left',
    },
    {
      title: 'PS:短描述',
      dataIndex: 'description',
      width: 200,
    },
    {
      title: '利润中心',
      dataIndex: 'profitCenterName',
      width: 300,
    },
    {
      title: '所属工厂',
      dataIndex: 'company',
      width: 120,
    },
    {
      title: '负责人编号',
      dataIndex: 'directorCode',
      width: 130,
    },
    {
      title: '负责人名称',
      dataIndex: 'directorName',
      width: 130,
    },
  ],
  api: (params: Record<string, any>) => new Api('/pms/projectAssetApplyDetailWbs/page').fetch({
    ...params,
    pageNum: 1,
    pageSize: 10000,
    query: {
      projectId: get(props, 'record.detailsData.projectId'),
    },
  }, '', 'POST'),
};

defineExpose({
  onSubmit() {
    return new Promise((resolve) => {
      const targetKeys = tableRef.value?.getSelectRowKeys();
      new Api('/pms/projectAssetApplyDetailWbs/add').fetch({
        assetApplyId: get(props, 'record.detailsData.id'),
        wbsIds: targetKeys,
      }, '', 'POST')
        .then((res) => {
          message.success('添加成功');
          resolve(res);
        });
    });
  },
});
</script>

<template>
  <div class="wbs-modal-table">
    <OrionTable
      ref="tableRef"
      class="scroll-table"
      :options="tableOptions"
    />
  </div>
</template>

<style scoped lang="less">
.wbs-modal-table{
  height: 570px;
  overflow: hidden;
  :deep(.ant-basic-table){
    padding-bottom: 0 !important;
  }
}
</style>
