{"name": "Activiti", "uri": "http://activiti.org/bpmn", "prefix": "activiti", "xml": {"tagAlias": "lowerCase"}, "associations": [], "types": [{"name": "Assignable", "extends": ["bpmn:UserTask"], "properties": [{"name": "assignee", "isAttr": true, "type": "String"}, {"name": "category", "isAttr": true, "type": "String"}, {"name": "priority", "isAttr": true, "type": "String"}, {"name": "candidateUsers", "isAttr": true, "type": "String"}, {"name": "candidateGroups", "isAttr": true, "type": "String"}, {"name": "formKey", "isAttr": true, "type": "String"}, {"name": "owner", "isAttr": true, "type": "String"}, {"name": "async", "isAttr": true, "type": "String"}, {"name": "businessCalendarName", "isAttr": true, "type": "String"}, {"name": "extensionId", "isAttr": true, "type": "String"}, {"name": "skipExpression", "isAttr": true, "type": "String"}, {"name": "dueDate", "isAttr": true, "type": "String"}, {"name": "followUpDate", "isAttr": true, "type": "String"}]}, {"name": "buttons", "properties": [{"name": "action", "isMany": true, "type": "action"}]}, {"name": "action", "properties": [{"name": "id", "isAttr": true, "type": "String"}, {"name": "name", "isAttr": true, "type": "String"}, {"name": "code", "isAttr": true, "type": "String"}, {"name": "targetRef", "isAttr": true, "type": "String"}, {"name": "listener", "isMany": true, "type": "listener"}]}, {"name": "features", "properties": [{"name": "applicant", "isMany": true, "type": "applicant"}]}, {"name": "applicant", "properties": [{"name": "value", "isAttr": true, "type": "String"}]}, {"name": "setting", "properties": [{"name": "multi-inst", "isMany": true, "type": "multi-inst"}]}, {"name": "multi-inst", "properties": [{"name": "isSequential", "isAttr": true, "type": "String"}, {"name": "rate", "isAttr": true, "type": "String"}]}, {"name": "prearranged", "properties": [{"name": "constraint", "isMany": true, "type": "constraint"}, {"name": "other", "isMany": true, "type": "other"}]}, {"name": "constraint", "properties": [{"name": "assignee", "isMany": true, "type": "assignee"}]}, {"name": "assignee", "properties": [{"name": "credentials", "isMany": true, "type": "credentials"}, {"name": "user", "isMany": true, "type": "user"}, {"name": "role", "isMany": true, "type": "role"}, {"name": "organization", "isMany": true, "type": "organization"}, {"name": "project_role", "isMany": true, "type": "project_role"}, {"name": "taskId", "isAttr": true, "type": "String"}]}, {"name": "credentials", "properties": [{"name": "value", "isAttr": true, "type": "String"}]}, {"name": "user", "properties": [{"name": "value", "isAttr": true, "type": "String"}]}, {"name": "role", "properties": [{"name": "value", "isAttr": true, "type": "String"}]}, {"name": "organization", "properties": [{"name": "value", "isAttr": true, "type": "String"}]}, {"name": "project_role", "properties": [{"name": "value", "isAttr": true, "type": "String"}]}, {"name": "other", "properties": [{"name": "assignee", "isMany": true, "type": "assignee"}]}, {"name": "listeners", "properties": [{"name": "listener", "isMany": true, "type": "listener"}]}, {"name": "listener", "properties": [{"name": "id", "isAttr": true, "type": "String"}, {"name": "name", "isAttr": true, "type": "String"}, {"name": "code", "isAttr": true, "type": "String"}, {"name": "service_code", "isAttr": true, "type": "String"}, {"name": "service_name", "isAttr": true, "type": "String"}]}, {"name": "businesses", "properties": [{"name": "business", "isMany": true, "type": "business"}]}, {"name": "business", "properties": [{"name": "id", "isAttr": true, "type": "String"}, {"name": "service_code", "isAttr": true, "type": "String"}, {"name": "service_name", "isAttr": true, "type": "String"}]}, {"name": "Collectable", "isAbstract": true, "extends": ["bpmn:MultiInstanceLoopCharacteristics"], "superClass": ["activiti:AsyncCapable"], "properties": [{"name": "collection", "isAttr": true, "type": "String"}, {"name": "elementVariable", "isAttr": true, "type": "String"}]}, {"name": "AsyncCapable", "isAbstract": true, "extends": ["bpmn:Activity", "bpmn:Gateway", "bpmn:Event"], "properties": []}], "emumerations": []}