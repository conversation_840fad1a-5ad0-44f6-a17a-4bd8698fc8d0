package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 项目 + 项目成员 第三方接口视图
 *
 * <AUTHOR>
 * @since 2024年9月24日
 */
@Data
@ApiModel(value = "ProjectAndUserTrdVO", description = "项目 + 项目成员 第三方接口视图")
public class ProjectAndUserTrdVO {

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目编码")
    private String projectNumber;

    @ApiModelProperty(value = "用户工号")
    private String userCode;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "角色")
    private String roleName;

}
