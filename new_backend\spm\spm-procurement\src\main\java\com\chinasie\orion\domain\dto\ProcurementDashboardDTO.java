package com.chinasie.orion.domain.dto;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ProcurementDashboard VO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@ApiModel(value = "ProcurementDashboardVO对象", description = "采购供应商指标看板")
@Data
public class ProcurementDashboardDTO extends ObjectVO implements Serializable {

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    private String indexYear;


    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String indexMonth;
}
