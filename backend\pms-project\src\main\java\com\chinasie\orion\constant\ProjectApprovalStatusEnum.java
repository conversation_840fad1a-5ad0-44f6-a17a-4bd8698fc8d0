package com.chinasie.orion.constant;


/**
 * <p>
 * ProjectDeclare 项目立项状态
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18 10:38:38
 */
public enum ProjectApprovalStatusEnum {
    CREATED(101,"已创建"),FLOWING(110,"流程中"),
    FINISH(130,"已生效"),REJECT(140,"已驳回");


    private Integer status;

    private String desc;


    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }
    ProjectApprovalStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

}
