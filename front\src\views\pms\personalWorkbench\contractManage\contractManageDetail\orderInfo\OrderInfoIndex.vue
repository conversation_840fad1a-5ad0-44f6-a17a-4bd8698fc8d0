<template>
  <Layout2 left-title="采购订单">
    <template #left>
      <SpinMain
        v-if="loading"
      />
      <BasicMenu
        v-else
        :defaultActionId="orderId"
        :showHeader="false"
        :menuData="menus"
        actionLineAlign="right"
        @menuChange="menuChange"
      />
    </template>
    <DetailsLayout
      title="采购订单信息"
      :list="orderInfo"
      :spinning="orderLoading"
      :data-source="orderDetail"
      :column="3"
    >
      <template #table>
        <div
          style="height: 500px;overflow: hidden;"
        >
          <OrionTable
            ref="tableRef"
            :options="tableOptions"
          />
        </div>
      </template>
    </DetailsLayout>
  </Layout2>
</template>

<script setup lang="ts">
import { BasicMenu, Layout2, OrionTable } from 'lyra-component-vue3';
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
// import { getPurchaseOrder, getPurOrderList, postOrderListInfoPages } from '/@/views/pms/api';
import {
  computed, h, inject, nextTick, onMounted, ref, Ref, unref, watchEffect,
} from 'vue';
import { contractDetailKey } from '../types';
import SpinMain from '/@/views/pms/projectLaborer/components/SpanMain/SpinMain.vue';

const menus: Ref = ref([]);
const contractDetail = inject(contractDetailKey);
const orderId: Ref<string> = ref('');
const orderDetail: Ref = ref({});
const loading: Ref<boolean> = ref(false);
const tableRef: Ref = ref();
const projectNumber:string = inject('projectNumber');
const orderInfo: Ref = ref([
  {
    label: '采购订单号',
    field: 'orderNumber',
  },
  {
    label: '采购类型',
    field: 'purchaseType',
    gridColumn: '2/4',
  },
  {
    label: '是否签订纸质合同',
    field: 'isSignContract',
    isBoolean: true,
  },
  {
    label: '是否具有质保金',
    field: 'isCarryOutAgreementEarnestMoney',
    isBoolean: true,
  },
  {
    label: '质保金金额',
    field: 'carryOutAgreementEarnestMoney',
  },
]);

const orderNumber = computed(() => menus.value?.filter((item) => item.id === unref(orderId))?.[0]?.orderNumber);

const tableOptions = {
  showTableSetting: false,
  showSmallSearch: false,
  showToolButton: false,
  immediate: false,
  api: (params) => postOrderListInfoPages({
    ...params,
    query: {
      projectNumber,
      contractNumber: unref(contractDetail)?.number,
      orderNumber: unref(orderNumber),
    },
  }),
  columns: [
    {
      title: '采购计划编号',
      dataIndex: 'purchaseApplyForNumber',
    },
    {
      title: '采购申请号',
      dataIndex: 'purchaseApplyForNumber',
    },
    {
      title: '采购单行号',
      dataIndex: 'purchaseApplyForLineNumber',
    },
    {
      title: '物资/服务计划编号',
      dataIndex: 'suppliesNumber',
    },
    {
      title: '计划类型',
      dataIndex: 'sortText',
    },
    {
      title: '物资/服务编码',
      dataIndex: 'isNeedFinalUserDeclare',
    },
    {
      title: '物资/服务描述',
      dataIndex: 'amountNum',
    },
    {
      title: '规格型号',
      dataIndex: 'eachUnit',
    },
    {
      title: '服务期限',
      dataIndex: 'taxCode',
    },

    {
      title: '计量单位',
      dataIndex: 'taxCode',
    },
    {
      title: '需求数量',
      dataIndex: 'taxCode',
    },

    {
      title: '需求日期',
      dataIndex: 'taxCode',
    },
    {
      title: '需求人',
      dataIndex: 'taxCode',
    },
    {
      title: '采购员',
      dataIndex: 'taxCode',
    },
    {
      title: '备注',
      dataIndex: 'taxCode',
    },
    // {
    //   title: '税率',
    //   dataIndex: 'taxRate',
    //   customRender({ text }) {
    //     return h('div', `${text}%`);
    //   },
    // },
    // {
    //   title: '单价（含税）',
    //   dataIndex: 'unitPriceInclude',
    //   isMoney: true,
    //   align: 'right',
    // },
    // {
    //   title: '金额（含税）',
    //   dataIndex: 'moneyInclude',
    //   isMoney: true,
    //   align: 'right',
    // },
  ],
};

onMounted(() => {
  getMenus();
});

// 默认选中第一条数据
watchEffect(() => {
  if (menus.value?.length && !orderId.value) {
    orderId.value = menus.value[0]?.id;
    getOrderInfo();
    updateTable();
  }
});

async function getMenus() {
  loading.value = true;
  try {
    const result = await getPurOrderList(unref(contractDetail)?.number);
    menus.value = result.map((item) => ({
      ...item,
      name: item.orderNumber,
    }));
  } finally {
    loading.value = false;
  }
}

const orderLoading: Ref<boolean> = ref(false);

// 获取采购订单信息
async function getOrderInfo() {
  if (!unref(orderId)) return;
  orderLoading.value = true;
  try {
    orderDetail.value = await getPurchaseOrder(unref(orderId));
  } finally {
    orderLoading.value = false;
  }
}

function menuChange({ id }) {
  if (orderId.value === id) return;
  orderId.value = id;
  getOrderInfo();
  updateTable();
}

function updateTable() {
  nextTick(() => {
    tableRef.value.reload();
  });
}
</script>

<style scoped>

</style>
