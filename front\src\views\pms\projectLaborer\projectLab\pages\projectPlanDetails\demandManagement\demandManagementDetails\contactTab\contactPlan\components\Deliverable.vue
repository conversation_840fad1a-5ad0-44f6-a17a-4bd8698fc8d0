<template>
  <Layout class="deliverablePage">
    <BasicTable
      ref="tableRef"
      :row-selection="false"
      :columns="columns"
      :data-source="dataSource"
      :show-index-column="false"
      :pagination="false"
      row-key="id"
      :resize-height-offset="10"
      :loading="loading"
    />
  </Layout>
</template>
<script lang="ts">
import { Layout, BasicTable } from 'lyra-component-vue3';
import {
  onMounted, reactive, toRefs, ref, inject,
} from 'vue';
import Api from '/@/api';
export default {
  name: 'Deliverable',
  components: {
    Layout,
    BasicTable,
  },
  setup(props, { emit }) {
    const formData: any = inject('formData', {});
    const state = reactive({
      tableRef: ref(),
      columns: [
        {
          title: '编号',
          dataIndex: 'number',
          align: 'left',
          minWidth: 200,
        },
        {
          title: '交付物名称',
          dataIndex: 'name',
          align: 'left',
          minWidth: 200,
        },
        {
          title: '计划交付物时间',
          dataIndex: 'predictDeliverTime',
          width: 150,
        },
        {
          title: '实际交付物时间',
          dataIndex: 'deliveryTime',
          width: 150,
        },
        {
          title: '所属任务',
          dataIndex: 'planName',
          width: 150,
        },
        {
          title: '状态',
          dataIndex: 'statusName',
          width: 100,
        },
        {
          title: '负责人',
          dataIndex: 'principalName',
          width: 150,
        },
        {
          title: '修改时间',
          dataIndex: 'modifyTime',
          width: 100,
        },
      ],
      dataSource: [],
      loading: false,
    });
    function getPage(obj = {}) {
      state.loading = true;
      new Api('/pms')
        .fetch(obj, `deliverable/list?planId=${formData?.value.id}`, 'POST')
        .then((res) => {
          state.dataSource = res;
          state.loading = false;
        })
        .catch(() => {
          state.loading = false;
        });
    }

    onMounted(() => {
      getPage();
    });
    return {
      ...toRefs(state),
    };
  },
};
</script>

<style scoped>
.deliverablePage {
  width: calc(100% - 60px);
  flex: 1;
  height: 500px;
}
</style>
