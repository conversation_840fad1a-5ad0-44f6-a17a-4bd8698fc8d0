<script setup lang="ts">
import {
  getDictByNumber, BasicButton, BasicCard, OrionTable,
} from 'lyra-component-vue3';
import {
  computed,
  ComputedRef,
  h,
  inject, reactive, ref, Ref,
  onMounted,
} from 'vue';

import {
  message, InputNumber,
} from 'ant-design-vue';
import Api from '/@/api';

import { differenceBy } from 'lodash-es';
const detailsData: Record<string, any> = inject('detailsData', reactive({}));
const baseInfoProps = reactive({
  list: [
    {
      label: '调整单编号',
      field: 'number',
    },
    {
      label: '状态',
      field: 'dataStatus',
    },
    {
      label: '项目编码',
      field: 'projectNumber',
    },
    {
      label: '项目名称',
      field: 'projectName',
    },
    {
      label: '调整标题',
      field: 'name',
      gridColumn: '1/5',
    },
    {
      label: '创建时间',
      field: 'createTime',
      formatTime: 'YYYY-MM-DD HH:mm:ss',

    },
    {
      label: '创建人',
      field: 'ownerName',

    },

    {
      label: '调整原因',
      field: 'remark',
    },

  ],
  column: 4,
  dataSource: detailsData,
});
const dataSource:Ref<Record<any, any>[]> = ref([]);
const tableOptions = {
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  showSmallSearch: false,
  columns: [
    {
      title: '预算申请编码',
      dataIndex: 'number',
    },
    {
      title: '预算名称',
      dataIndex: 'name',
    },
    {
      title: '成本中心',
      dataIndex: 'costCenterId',
    },
    {
      title: '科目名称',
      dataIndex: 'expenseSubjectName',
    },
    {
      title: '科目编码',
      dataIndex: 'expenseSubjectNumber',
    },
    {
      title: '期间类型',
      dataIndex: 'timeTypeName',
      align: 'center',
    },
    {
      title: '预算期间',
      dataIndex: 'budgetTime',
    },
    {
      title: '预算对象类型',
      dataIndex: 'budgetObjectTypeName',
    },
    {
      title: '预算对象',
      dataIndex: 'budgetObjectName',
    },
    {
      title: '币种',
      dataIndex: 'currencyName',
    },
    {
      title: '预算申请总额（元）',
      dataIndex: 'budgetMoney',
      slots: { customRender: 'budgetMoney' },
    },
    {
      title: '1月',
      dataIndex: 'januaryMoney',
      align: 'center',
      slots: { customRender: 'januaryMoney' },
    },
    {
      title: '2月',
      dataIndex: 'februaryMoney',
      align: 'center',
      slots: { customRender: 'februaryMoney' },
    },
    {
      title: '3月',
      dataIndex: 'marchMoney',
      align: 'center',
      slots: { customRender: 'marchMoney' },
    },
    {
      title: '第一季度',
      dataIndex: 'firstQuarterMoney',
      align: 'center',
      slots: { customRender: 'firstQuarterMoney' },
    },
    {
      title: '4月',
      dataIndex: 'aprilMoney',
      align: 'center',
      slots: { customRender: 'aprilMoney' },
    },
    {
      title: '5月',
      dataIndex: 'mayMoney',
      align: 'center',
      slots: { customRender: 'mayMoney' },
    },
    {
      title: '6月',
      dataIndex: 'juneMoney',
      align: 'center',
      slots: { customRender: 'juneMoney' },
    },
    {
      title: '第二季度',
      dataIndex: 'secondQuarter',
      align: 'center',
      slots: { customRender: 'secondQuarter' },
    },
    {
      title: '7月',
      dataIndex: 'julyMoney',
      align: 'center',
      slots: { customRender: 'julyMoney' },
    },
    {
      title: '8月',
      dataIndex: 'augustMoney',
      align: 'center',
      slots: { customRender: 'augustMoney' },
    },
    {
      title: '9月',
      dataIndex: 'septemberMoney',
      align: 'center',
      slots: { customRender: 'septemberMoney' },
    },
    {
      title: '第三季度',
      dataIndex: 'thirdQuarter',
      align: 'center',
      slots: { customRender: 'thirdQuarter' },
    },
    {
      title: '10月',
      dataIndex: 'octoberMoney',
      align: 'center',
      slots: { customRender: 'octoberMoney' },
    },
    {
      title: '11月',
      dataIndex: 'novemberMoney',
      align: 'center',
      slots: { customRender: 'novemberMoney' },
    },
    {
      title: '12月',
      dataIndex: 'decemberMoney',
      align: 'center',
      slots: { customRender: 'decemberMoney' },
    },
    {
      title: '第四季度',
      dataIndex: 'fourthQuarter',
      align: 'center',
      slots: { customRender: 'fourthQuarter' },
    },

  ],

  api: () => new Promise((resolve) => resolve(dataSource.value)),

};

function fakerDelete(idToDelete) {
  return new Promise((resolve, reject) => {
    let tableData = tableRef.value.getDataSource();
    tableData = tableData.filter((item) => item.id !== idToDelete);
    dataSource.value = tableData;
    tableRef.value.reload();
    resolve(true);
  });
}
function getBudgetTableData() {
  new Api('/pms/budgetAdjustment/list').fetch('', `?formId=${detailsData?.id}`, 'GET').then((res) => {
    dataSource.value = res;
    tableRef.value.reload();
  });
}
const toolButtons: ComputedRef<Record<string, any>[]> = computed(() => [
  {
    event: 'save',
    text: '保存',
    icon: 'sie-icon-baocun',
    code: 'PMS_YSTZXQ_container_02_button_01',
  },
]);
const costCenterOptions = ref();
async function costCenterIdListApi() {
  const res = await new Api('/pms/costCenter/list').fetch('', '', 'POST');
  costCenterOptions.value = res;
}
const expenseSubjectNameOptions = ref([]);

function getExpenseSubjectNameOptions() {
  return new Api('/pms/costCenter/list').fetch('', '', 'POST');
}
function getButtonProps(item) {
  // if (item.event !== 'add') {
  //   item.disabled = !selectedRows.value.length;
  // }
  return item;
}
const timeTypeOptions = ref([]);
const budgetObjectTypeOptions = ref([]);
const currencyOptions = ref([]);
const expenseSubjectNumberTreeData = ref([]);
async function getDictOptions() {
  new Api(`/pms/expenseSubject/tree?status=${1}`).fetch('', '', 'GET').then((res) => {
    expenseSubjectNumberTreeData.value = res;
  });
  timeTypeOptions.value = await getDictByNumber('budgetPeriodType');
  budgetObjectTypeOptions.value = await getDictByNumber('budgetObjectType');
  currencyOptions.value = await getDictByNumber('budgetCurrency');
}
function changeMainFour(value, record, index) {

}

onMounted(() => {
  getDictOptions();
  costCenterIdListApi();
  getBudgetTableData();
});
const tableRef = ref();

function generateRandomString(length) {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let randomString = '';

  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    randomString += characters.charAt(randomIndex);
  }
  return `add-sie-${randomString}`;
}
function addBudgetFun() {
  let tableData = tableRef.value.getDataSource();
  tableData.push(
    {
      id: generateRandomString(10),
      number: '',
      name: '',
      projectId: detailsData?.projectId,
      projectName: detailsData?.projectName,
      costCenterId: '',
      expenseSubjectName: '',
      expenseSubjectNumber: '',
      timeType: '',
      budgetTime: '',
      budgetObjectType: 'budgetProject',
      currency: 'budgetRMB',
      budgetMoney: null,
      januaryMoney: null,
      februaryMoney: null,
      marchMoney: null,
      firstQuarterMoney: null,
      formId: detailsData?.id,
      aprilMoney: null,
      mayMoney: null,
      juneMoney: null,
      secondQuarter: null,
      julyMoney: null,
      augustMoney: null,
      septemberMoney: null,
      thirdQuarter: null,
      octoberMoney: null,
      novemberMoney: null,
      decemberMoney: null,
      fourthQuarter: null,

    },
  );
  dataSource.value = tableData;
  tableRef.value.reload();
}
async function getSaveBatchParams() {
  return dataSource.value.filter((item) => item.isChange === '1');
}

async function saveBatchFun() {
  let params = await getSaveBatchParams();

  const result = await new Api('/pms/budgetAdjustment/saveBatch').fetch(params, '', 'POST');

  if (result) {
    message.success('保存成功');
    getBudgetTableData();
  }
}
function toolClick({ event }: Record<string, any>) {
  switch (event) {
    case 'enable':
      break;
    case 'disable':
      break;
    case 'save':
      saveBatchFun();
      break;
    case 'addBudget':
      addBudgetFun();
      break;
  }
}

function calculateQuarterMoney(record, quarterNumber = 0) {
  if (record.timeType === 'budgetMonth') {
    let months;
    let quarter;
    switch (quarterNumber) {
      case 1:
        months = [
          'januaryMoney',
          'februaryMoney',
          'marchMoney',
        ];
        quarter = 'firstQuarterMoney';
        break;
      case 2:
        months = [
          'aprilMoney',
          'mayMoney',
          'juneMoney',
        ];
        quarter = 'secondQuarter';
        break;
      case 3:
        months = [
          'julyMoney',
          'augustMoney',
          'septemberMoney',
        ];
        quarter = 'thirdQuarter';
        break;
      case 4:
        months = [
          'octoberMoney',
          'novemberMoney',
          'decemberMoney',
        ];
        quarter = 'fourthQuarter';
        break;
      default:
        months = [
          'januaryMoney',
          'februaryMoney',
          'marchMoney',
          'aprilMoney',
          'mayMoney',
          'juneMoney',
          'julyMoney',
          'augustMoney',
          'septemberMoney',
          'octoberMoney',
          'novemberMoney',
          'decemberMoney',
        ];
        quarter = 'budgetMoney';
        break;
    }
    // 检查并处理可能的空值
    const totalMoney = months.reduce((total, month) => total + (record[month] ?? 0), 0);
    record[quarter] = totalMoney;
    return totalMoney;
  }
}

function calculateAllQuarterMoney(record) {
  const arr = [
    'firstQuarterMoney',
    'secondQuarter',
    'thirdQuarter',
    'fourthQuarter',
  ];
  const totalMoney = arr.reduce((total, month) => total + (record[month] ?? 0), 0);
  return totalMoney;
}

</script>

<template>
  <BasicCard
    title="基本信息"
    :grid-content-props="baseInfoProps"
    :isBorder="false"
  />
  <BasicCard
    title="预算编制"
  >
    <OrionTable
      v-if="detailsData.status===120"
      ref="tableRef"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <template
          v-for="button in toolButtons"
          :key="button.event"
        >
          <BasicButton
            v-is-power="[button.code]"
            v-bind="getButtonProps(button)"
            @click="toolClick(button)"
          >
            {{ button.text }}
          </BasicButton>
        </template>
      </template>
      <template #budgetMoney="{ record }">
        <span
          v-if="record.isChange==='0'"
        >
          {{ record.budgetMoney }}
        </span>
        <InputNumber
          v-else-if="record.timeType==='budgetMonth'"
          :value="calculateQuarterMoney(record,0)"
          :disabled="(record.timeType==='budgetMonth' || record.timeType==='budgetQuarter')"
        />
        <InputNumber
          v-else-if="record.timeType==='budgetQuarter'"
          :value="calculateAllQuarterMoney(record)"
          :disabled="(record.timeType==='budgetMonth' || record.timeType==='budgetQuarter')"
        />
        <InputNumber
          v-else
          v-model:value="record.budgetMoney"
          :disabled="(record.timeType==='budgetMonth' || record.timeType==='budgetQuarter')"
        />
      </template>
      <template #januaryMoney="{ record }">
        <span
          v-if="record.isChange==='0'"
        >
          {{ record.januaryMoney }}
        </span>
        <InputNumber
          v-else
          v-model:value="record.januaryMoney"
          :disabled="record.timeType!=='budgetMonth'"
        />
      </template>
      <template #februaryMoney="{ record }">
        <span
          v-if="record.isChange==='0'"
        >
          {{ record.februaryMoney }}
        </span>
        <InputNumber
          v-else
          v-model:value="record.februaryMoney"
          :disabled="record.timeType!=='budgetMonth'"
        />
      </template>
      <template #marchMoney="{ record }">
        <span
          v-if="record.isChange==='0'"
        >
          {{ record.marchMoney }}
        </span>
        <InputNumber
          v-else
          v-model:value="record.marchMoney"
          :disabled="record.timeType!=='budgetMonth'"
        />
      </template>
      <template #firstQuarterMoney="{ record }">
        <span
          v-if="record.isChange==='0'"
        >
          {{ record.firstQuarterMoney }}
        </span>
        <InputNumber
          v-else-if="record.timeType==='budgetMonth'"
          :value="calculateQuarterMoney(record,1)"
          :disabled="record.timeType !== 'budgetQuarter'"
        />
        <InputNumber
          v-else
          v-model:value="record.firstQuarterMoney"
          :disabled="record.timeType !== 'budgetQuarter'"
        />
      </template>
      <template #aprilMoney="{ record }">
        <span
          v-if="record.isChange==='0'"
        >
          {{ record.aprilMoney }}
        </span>
        <InputNumber
          v-else
          v-model:value="record.aprilMoney"
          :disabled="record.timeType!=='budgetMonth'"
        />
      </template>
      <template #mayMoney="{ record }">
        <span
          v-if="record.isChange==='0'"
        >
          {{ record.mayMoney }}
        </span>
        <InputNumber
          v-else
          v-model:value="record.mayMoney"
          :disabled="record.timeType!=='budgetMonth'"
        />
      </template>
      <template #juneMoney="{ record }">
        <span
          v-if="record.isChange==='0'"
        >
          {{ record.juneMoney }}
        </span>
        <InputNumber
          v-else
          v-model:value="record.juneMoney"
          :disabled="record.timeType!=='budgetMonth'"
        />
      </template>
      <template #secondQuarter="{ record }">
        <span
          v-if="record.isChange==='0'"
        >
          {{ record.secondQuarter }}
        </span>
        <InputNumber
          v-else-if="record.timeType==='budgetMonth'"
          :value="calculateQuarterMoney(record,2)"
          :disabled="record.timeType!=='budgetQuarter'"
        />

        <InputNumber
          v-else
          v-model:value="record.secondQuarter"
          :disabled="record.timeType !== 'budgetQuarter'"
        />
      </template>
      <template #julyMoney="{ record }">
        <span
          v-if="record.isChange==='0'"
        >
          {{ record.julyMoney }}
        </span>
        <InputNumber
          v-else
          v-model:value="record.julyMoney"
          :disabled="record.timeType!=='budgetMonth'"
        />
      </template>
      <template #augustMoney="{ record }">
        <span
          v-if="record.isChange==='0'"
        >
          {{ record.augustMoney }}
        </span>
        <InputNumber
          v-else
          v-model:value="record.augustMoney"
          :disabled="record.timeType!=='budgetMonth'"
        />
      </template>
      <template #septemberMoney="{ record }">
        <span
          v-if="record.isChange==='0'"
        >
          {{ record.septemberMoney }}
        </span>
        <InputNumber
          v-else
          v-model:value="record.septemberMoney"
          :disabled="record.timeType!=='budgetMonth'"
        />
      </template>
      <template #thirdQuarter="{ record }">
        <span
          v-if="record.isChange==='0'"
        >
          {{ record.thirdQuarter }}
        </span>
        <InputNumber
          v-else-if="record.timeType==='budgetMonth'"
          :value="calculateQuarterMoney(record,3)"
          :disabled="record.timeType!=='budgetQuarter'"
        />

        <InputNumber
          v-else
          v-model:value="record.thirdQuarter"
          :disabled="record.timeType !== 'budgetQuarter'"
        />
      </template>
      <template #octoberMoney="{ record }">
        <span
          v-if="record.isChange==='0'"
        >
          {{ record.octoberMoney }}
        </span>
        <InputNumber
          v-else
          v-model:value="record.octoberMoney"
          :disabled="record.timeType!=='budgetMonth'"
        />
      </template>
      <template #novemberMoney="{ record }">
        <span
          v-if="record.isChange==='0'"
        >
          {{ record.novemberMoney }}
        </span>
        <InputNumber
          v-else
          v-model:value="record.novemberMoney"
          :disabled="record.timeType!=='budgetMonth'"
        />
      </template>
      <template #decemberMoney="{ record }">
        <span
          v-if="record.isChange==='0'"
        >
          {{ record.decemberMoney }}
        </span>
        <InputNumber
          v-else
          v-model:value="record.decemberMoney"
          :disabled="record.timeType!=='budgetMonth'"
        />
      </template>
      <template #fourthQuarter="{ record }">
        <span
          v-if="record.isChange==='0'"
        >
          {{ record.fourthQuarter }}
        </span>
        <InputNumber
          v-else-if="record.timeType==='budgetMonth'"
          :value="calculateQuarterMoney(record,4)"
          :disabled="record.timeType!=='budgetQuarter'"
        />
        <InputNumber
          v-else
          v-model:value="record.fourthQuarter"
          :disabled="record.timeType!=='budgetQuarter'"
        />
      </template>
    </OrionTable>

    <OrionTable
      v-else
      ref="tableRef"
      :options="tableOptions"
    />
  </BasicCard>
</template>

<style scoped lang="less">

</style>
