<template>
  <BasicDrawer
    v-model:isContinue="isContinue"
    :width="1000"
    wrap-class-name="addTableNode"
    :showFooter="true"
    :showContinue="formType==='add'"
    @register="modalRegister"
    @visible-change="visibleChange"
    @ok="confirm"
    @cancel="cancel"
  >
    <div
      class="formContent"
    >
      <div class="formContent_content">
        <Wrap
          title="添加项目"
        >
          <OrionTable
            ref="tableRef"
            :max-height="200"
            :options="tableOptions"
            @selection-change="onSelectionChange"
          >
            <template #toolbarLeft>
              <BasicButton
                type="primary"
                icon="sie-icon-tianjiaxinzeng"
                ghost
                @click="openSource()"
              >
                添加项目
              </BasicButton>
              <BasicButton
                icon="sie-icon-del"
                :disabled="disabledBtn"
                @click="removeData"
              >
                移除
              </BasicButton>
            </template>
          </OrionTable>
        </Wrap>
        <Wrap title="基本信息">
          <BasicForm @register="registerForm" />
        </Wrap>
      </div>
    </div>
  </BasicDrawer>
</template>
<script lang="ts">
import {
  computed,
  defineComponent,
  defineExpose,
  h,
  onMounted,
  provide,
  reactive,
  readonly,
  ref,
  Ref,
  toRefs, unref,
  watch,
} from 'vue';
import {
  BasicButton,
  BasicDrawer,
  BasicForm,
  DataStatusTag,
  getDict,
  InputSelectUser,
  OrionTable,
  useDrawerInner,
  useForm,
  useModal, openSelectModal,
  openModal,
} from 'lyra-component-vue3';
import { message, Modal } from 'ant-design-vue';
import Api from '/@/api';

import { getProductListApi } from '/@/views/pms/projectLaborer/api/projectLab';
import SourceModal from './SourceModal/index.vue';

import { useUserStore } from '/@/store/modules/user';
import { getUserProfile } from '/@/views/pms/api';
import Wrap from './Wrap.vue';

export default defineComponent({
  name: 'AddTableNode',
  components: {
    BasicDrawer,
    BasicForm,
    BasicButton,
    Wrap,
    OrionTable,

  },
  emits: ['update'],
  setup(props, { emit }) {
    const selectRows = ref([]);
    const disabledBtn = computed(() => selectRows.value.length === 0);
    const state = reactive({
      loadingBtn: false,
      checked: false,
      formType: 'add',
      formId: '',
      productOptions: [],
      projectTypeOptions: [],
      statusOptions: [],
      schemeIdList: [],
      projectSourceDict: [],
      projectType: '',
      userOrgList: [], // 用户概要信息
      // 责任人
      selectPrincipalUser: [],
      principalDeptOptions: [],
      // 项目干系人
      selectRelatedPerson: [],

    });
    let useData = useUserStore().getUserInfo;
    const isContinue: Ref<boolean> = ref(false);

    const tableRef = ref();

    const dataSource = ref([]);

    // 获取用户概要信息
    async function reqUserProfile(uid) {
      if (!uid) return;
      try {
        const {
          orgName, orgId, orgCode, deptName, deptId, deptCode, className, classId, code,
        } = await getUserProfile(uid);
        state.userOrgList = [
          { // 部门
            label: orgName,
            value: orgId,
            code: orgCode,
          },
          { // 科室
            label: deptName,
            value: deptId,
            code: deptCode,
          },
          { // 班组
            label: className,
            value: classId,
            code,
          },
        ].filter((item) => item.label && item.value);
      } finally {
        // state.loading = false;
      }
    }

    function visibleChange(flag) {
      isContinue.value = false;
      if (!flag) {
        resetFields();

        // 责任人
        state.selectPrincipalUser = [];
        state.principalDeptOptions = [];
        // 项目干系人
        state.selectRelatedPerson = [];
      }
    }

    // 移除表格数据
    function removeData() {
      Modal.confirm({
        title: '移除确认提示',
        content: '请确认是否移除该关联计划信息？',
        onOk(closeFn) {
          dataSource.value = dataSource.value.filter((item) => selectRows.value.every((row) => row.uid !== item.uid));
          tableRef.value.clearSelectedRowKeys();
          closeFn();
        },
        onCancel() {
          Modal.destroyAll();
        },
      });
    }

    const columns = [
      {
        title: '项目编码',
        dataIndex: 'number',
      },
      {
        title: '项目名称',
        dataIndex: 'name',
      },
      {
        title: '项目类型',
        dataIndex: 'projectTypeName',
      },
      {
        title: '项目负责人',
        dataIndex: 'resPersonName',
      },
      // {
      //   title: '编号',
      //   dataIndex: 'number',
      //
      // },
      // {
      //   title: '名称',
      //   dataIndex: 'name',
      // },
      //
      // {
      //   title: '状态',
      //   dataIndex: 'dataStatus',
      //   width: 100,
      //   customRender({ text }) {
      //     return text ? h(DataStatusTag, {
      //       statusData: text,
      //     }) : '';
      //   },
      // },
      // {
      //   title: '责任人',
      //   width: 100,
      //   dataIndex: 'resPersonName',
      // },

    ];

    const tableOptions = {
      rowSelection: {},
      columns,
      dataSource,
      pagination: false,
      rowKeys: 'uid',
      showTableSetting: false,
      showToolButton: false,
      showSmallSearch: false,
    };

    const [modalRegister, { closeDrawer, setDrawerProps, changeOkLoading }] = useDrawerInner((drawerData) => {
      isContinue.value = false;
      state.loadingBtn = false;
      state.formType = drawerData.type;
      state.formId = drawerData.id;
      // state.projectType = drawerData.projectType;
      state.schemeIdList = [];
      dataSource.value = [];

      // 已选合同责任人
      state.selectPrincipalUser = [];
      // 合同责任部门选项
      state.principalDeptOptions = [];

      // state.principalDeptOptions = [userInfo].map((item) => ({
      //   value: item.orgId,
      //   label: item.orgName,
      //   key: item.orgId,
      // }));
      reqUserProfile(useData.id);
      clearValidate();
      resetFields();

      getProjectSourceDict();

      if (drawerData.type === 'add') {
        setDrawerProps({ title: '新增项目集' });
      } else {
        // state.addMore = true;
        setDrawerProps({ title: '编辑项目集' });
        // setFieldsValue(drawerData);

        getFormData(state.formId);
      }
    });

    function extractIds(data, idFieldName) {
      let resultArr = data.map((item) => item[idFieldName]);
      return resultArr.join();
    }

    // async function getBasePlanPage(id) {
    //   const result = await new Api('/pms/new-project-to-base-plan/page').fetch({
    //     pageSize: 999,
    //     pageNum: 1,
    //     query: {
    //       projectId: id,
    //     },
    //   }, '', 'POST');
    //   let arr = result.content.map((item) => ({
    //     ...item,
    //     name: item.sourceName,
    //     rspUserName: item.resUserName,
    //     type: item.sourceTypeName,
    //   }));
    //
    // }
    async function getProjectSourceDict() {
      state.projectSourceDict = await getDict('dict1714906542609989632').then((res) => res?.map((item) => ({
        ...item,
        label: item.description,
        value: item.value,
      })) ?? []);
    }

    function getFormData(id) {
      new Api(`/pms/projectCollection/${id}`)
        .fetch('', '', 'GET')
        .then((res) => {
          if (res.projectVOList?.length) {
            dataSource.value = res.projectVOList.map((item) => ({
              uid: `${item.id}$${item.projectSource}`, // 解决没有唯一id的问题
              ...item,
            }));
          } else {
            dataSource.value = [];
          }
          tableRef.value.setTableData(dataSource);

          state.principalDeptOptions = [
            {
              value: res.resDept,
              label: res.resDeptName,
              key: res.resDept,
            },
          ];

          state.selectPrincipalUser = [
            {
              id: res.resPerson,
              name: res.resPersonName,
            },
          ];
          // res.relatedPerson = res.relatedPerson;

          state.selectRelatedPerson = res.relatedPersonList;

          setFieldsValue(res);
        });
    }

    /**
     * 设置责任部门
     *
     */
    async function setPrincipalDept(principalId?: string) {
      if (!principalId) {
        setFieldsValue({
          resDept: '',
        });
        return;
      }
      const userInfo = await new Api(`/pmi/user/user-profile/${principalId}`).fetch('', '', 'GET');

      state.principalDeptOptions = [userInfo].map((item) => ({
        value: item.orgId,
        label: item.orgName,
        key: item.orgId,
      }));
      setFieldsValue({
        resDept: state.principalDeptOptions?.[0]?.value ?? '',
      });
    }

    function onSelectionChange({ rows }) {
      selectRows.value = rows;
    }

    const [
      registerForm,
      {
        setFieldsValue, clearValidate, resetFields, validateFields, getFieldsValue,
      },
    ] = useForm({
      actionColOptions: {
        span: 24,
      },
      showActionButtonGroup: false,
      schemas: [
        {
          field: 'name',
          component: 'Input',
          label: '项目集名称:',
          colProps: {
            span: 11,
          },
          rules: [
            {
              required: true,
              trigger: 'blur',
              type: 'string',
            },
          ],
          componentProps: {
            maxlength: 100,
          },
        },
        {
          field: 'resPersonName',
          label: '负责人名称',
          component: 'Input',
          show: false,
        },
        {
          field: 'resDept',
          label: '负责部门',
          component: 'Input',
          show: false,
        },
        {
          field: 'resPerson',
          component: 'InputSearch',
          label: '负责人',
          colProps: {
            span: 11,
          },
          rules: [
            {
              required: true,
              trigger: 'change',
              type: 'string',
            },
          ],
          render({ model, field }) {
            return h(InputSelectUser, {
              selectType: 'radio',
              selectUserData: state.selectPrincipalUser,
              onChange(users) {
                state.selectPrincipalUser = users;

                const userId = users?.[0]?.id;
                model[field] = userId ?? '';

                setFieldsValue({
                  resPersonName: users?.[0]?.name ?? '',
                });
                setPrincipalDept(userId);
                validateFields(['resPerson']);
              },
              selectUserModalProps: {
                selectType: 'radio',
                treeDataApi: () => new Api('/pmi/organization/treeListPage').fetch(
                  {
                    orders: [
                      {
                        asc: false,
                        column: '',
                      },
                    ],
                    pageNum: 0,
                    pageSize: 0,
                    query: { status: 1 },
                  },
                  '',
                  'POST',
                ),
              },
            });
          },
        },
        // {
        //   field: 'projectCollectionType',
        //   component: 'ApiSelect',
        //   label: '项目集类型',
        //   colProps: {
        //     span: 11,
        //   },
        //   rules: [
        //     { required: true, trigger: 'change', type: 'string' },
        //   ],
        //   componentProps: {
        //     api: () => getDict('dict1774974536895303680'),
        //     labelField: 'description',
        //     valueField: 'value',
        //   },
        // },
        {
          field: 'relatedPerson',
          component: 'InputSearch',
          label: '项目干系人',
          colProps: {
            span: 11,
          },
          render({ model, field }) {
            return h(InputSelectUser, {
              selectUserData: state.selectRelatedPerson,
              onChange(users) {
                state.selectRelatedPerson = users;

                const userId = extractIds(users, 'id');

                model[field] = userId ?? '';
                validateFields(['relatedPerson']);
              },
              selectUserModalProps: {
                selectType: 'checkbox',
                treeDataApi: () => new Api('/pmi/organization/treeListPage').fetch(
                  {
                    orders: [
                      {
                        asc: false,
                        column: '',
                      },
                    ],
                    pageNum: 0,
                    pageSize: 0,
                    query: { status: 1 },
                  },
                  '',
                  'POST',
                ),
              },

            });
          },
        },
        {
          field: 'projectCollectionLevel',
          component: 'ApiSelect',
          label: '项目集级别',
          colProps: {
            span: 11,
          },
          required: true,
          componentProps: {
            api: () => getDict('dict1774974891121053696'),
            labelField: 'description',
            valueField: 'value',
          },
        },

        {
          field: 'remark',
          component: 'InputTextArea',
          label: '描述:',
          colProps: {
            span: 24,
          },
          componentProps: {
            rows: 4,
            showCount: true,
            maxlength: 250,
          },
        },
      ],

    });
    const cancel = () => {
      closeDrawer();
    };

    const openTreeSelect = () => {

    };

    function openSource() {
      openSelectModal({
        onOk: (params) => {
          dataSource.value = params.allSelect;
        },
        tableConfig: {
          fieldKey: ['id', 'name'],
          selected: dataSource.value,
          tableApi: (params) => new Api('/pms/project/getPage').fetch({
            ...params,
          }, '', 'POST'),
          tableOptions: {
            showSmallSearch: true,
            columns: [
              {
                title: '项目编码',
                dataIndex: 'number',
              },
              {
                title: '项目名称',
                dataIndex: 'name',
              },
              {
                title: '项目类型',
                dataIndex: 'projectTypeName',
              },
              {
                title: '项目负责人',
                dataIndex: 'resPersonName',
              },
            ],
          },
        },
        modalConfig: {
          title: '添加项目',
        },
      });
    }

    const confirm = async () => {
      let formData: any = await validateFields();

      // 使用 map 方法提取所需属性
      let planList = dataSource.value;

      formData.projectIds = planList.map((item) => item.id);
      state.loadingBtn = true;

      changeOkLoading(true);
      if (state.formType === 'add') {
        new Api('/pms/projectCollection').fetch(formData, '', 'POST').then((res) => {
          state.loadingBtn = false;
          changeOkLoading(false);
          message.success('新增成功');
          emit('update');
          if (isContinue.value) {
            state.selectRelatedPerson = [];
            state.selectPrincipalUser = [];
            resetFields();
          } else {
            closeDrawer();
          }
        }).catch((err) => {
          changeOkLoading(false);
          state.loadingBtn = false;
        });
      } else {
        formData.id = state.formId;
        new Api('/pms/projectCollection').fetch(formData, '', 'PUT').then((res) => {
          message.success('编辑成功');
          state.loadingBtn = false;
          changeOkLoading(false);
          emit('update');
          closeDrawer();
        }).catch((err) => {
          state.loadingBtn = false;
          changeOkLoading(false);
        });
      }
    };
    onMounted(async () => {
      state.productOptions = await getProductListApi();
      state.projectTypeOptions = await new Api('/pms').fetch('', 'dict/code/pms_project_type', 'GET');
    });

    return {
      ...toRefs(state),
      modalRegister,
      registerForm,
      cancel,
      confirm,
      tableRef,
      tableOptions,

      removeData,
      disabledBtn,
      onSelectionChange,
      isContinue,
      visibleChange,
      openTreeSelect,
      openSource,
    };
  },
});

</script>
<style lang="less">
.addTableNode {
  .scrollbar__view {
    height: 100%;
  }

  .ant-drawer-body {
    padding: 0;
  }

  .formContent {
    display: flex;
    height: 100%;
    flex-direction: column;

    .formContent_content {
      padding: 0 24px;
      flex: 1 1 auto;
    }

    .wrap {
      border: none;
    }

    .moreMessage {
      color: #5976d6;
      cursor: pointer;
    }

    .actions {
      span {
        color: #5172DC;
        padding: 0px 10px;
        cursor: pointer;
      }

      .actions1 {
        border-right: 1px solid #5172DC;
      }
    }

    .addDocumentFooter {
      padding: 15px;
      border-top: 1px solid #e9ecf2;
      width: 100%;
      display: flex;
      justify-content: space-between;

      .addModalFooterNext {
        line-height: 40px !important;
      }

      .btnStyle {
        flex: 1;
        text-align: right;

        .ant-btn {
          margin-left: 10px;
          border-radius: 4px;
          padding: 4px 30px;
        }

        .cancel {
          background: #5172dc19;
          color: #5172DC;
        }

        .confirm {
          background: #5172dc;
          color: #ffffff;
        }
      }
    }
  }

  .ant-form-item {
    display: block;
  }

  .ant-form-item-control {
    width: 100% !important;
  }
}
</style>
