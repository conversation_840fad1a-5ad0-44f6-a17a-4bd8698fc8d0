package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ProjectDTO;
import com.chinasie.orion.domain.dto.ProjectDeclareDTO;
import com.chinasie.orion.domain.vo.ProjectDeclareVO;
import com.chinasie.orion.domain.vo.ProjectVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectDeclareService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
/**
 * <p>
 * ProjectDeclare 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18 10:38:38
 */
@RestController
@RequestMapping("/projectDeclare")
@Api(tags = "项目申报")
public class ProjectDeclareController {

    @Autowired
    private ProjectDeclareService projectDeclareService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查询了详情", type = "项目申报", subType = "根据用户查询用户参与的项目", bizNo = "{{#id}}")
    public ResponseDTO<ProjectDeclareVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        ProjectDeclareVO rsp = projectDeclareService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 根据项目id获取申报信息
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "根据项目id获取申报信息")
    @RequestMapping(value = "getByProjectId/{projectId}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】根据项目id获取申报信息", type = "项目申报", subType = "根据项目id获取申报信息", bizNo = "{{#projectId}}")
    public ResponseDTO<ProjectDeclareVO> getByProjectId(@PathVariable(value = "projectId") String projectId) throws Exception {
        ProjectDeclareVO rsp = projectDeclareService.getByProjectId(projectId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectDeclareDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增", type = "项目申报", subType = "新增", bizNo = "")
    public ResponseDTO<ProjectDeclareVO> create(@RequestBody @Validated ProjectDeclareDTO projectDeclareDTO) throws Exception {
        ProjectDeclareVO rsp =  projectDeclareService.create(projectDeclareDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectDeclareDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑", type = "项目申报", subType = "编辑", bizNo = "")
    public ResponseDTO<ProjectDeclareVO> edit(@RequestBody @Validated ProjectDeclareDTO projectDeclareDTO) throws Exception {
        ProjectDeclareVO rsp = projectDeclareService.edit(projectDeclareDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除", type = "项目申报", subType = "删除", bizNo = "")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        if(CollectionUtils.isEmpty(ids)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "请选择要删除的数据!");
        }
        Boolean rsp = projectDeclareService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】分页", type = "项目申报", subType = "分页", bizNo = "")
    public ResponseDTO<Page<ProjectDeclareVO>> pages(@RequestBody Page<ProjectDeclareDTO> pageRequest) throws Exception {
        Page<ProjectDeclareVO> rsp =  projectDeclareService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 待发起申报项目分页
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "待发起申报项目分页")
    @RequestMapping(value = "/project/page", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】待发起申报项目分页", type = "项目申报", subType = "待发起申报项目分页", bizNo = "")
    public ResponseDTO<Page<ProjectVO>> projectList(@RequestBody Page<ProjectDTO> pageRequest) throws Exception {
        Page<ProjectVO> rsp = projectDeclareService.projectPages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 我的申报项目分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "我的申报项目分页")
    @RequestMapping(value = "/userPages", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】我的申报项目分页", type = "项目申报", subType = "我的申报项目分页", bizNo = "")
    public ResponseDTO<Page<ProjectDeclareVO>> userPages(@RequestBody Page<ProjectDeclareDTO> pageRequest) throws Exception {
        Page<ProjectDeclareVO> rsp =  projectDeclareService.userPages(pageRequest);
        return new ResponseDTO<>(rsp);
    }
}
