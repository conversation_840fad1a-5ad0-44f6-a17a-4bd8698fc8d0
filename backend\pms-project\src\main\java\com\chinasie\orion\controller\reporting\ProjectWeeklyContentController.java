package com.chinasie.orion.controller.reporting;

import com.chinasie.orion.domain.dto.reporting.ProjectWeeklyContentDTO;
import com.chinasie.orion.domain.vo.reporting.ProjectWeeklyContentVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.reporting.ProjectWeeklyContentService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * ProjectWeeklyContent 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-14 08:50:29
 */
@RestController
@RequestMapping("/projectWeeklyContent")
@Api(tags = "项目周报内容表")
public class ProjectWeeklyContentController {

    @Autowired
    private ProjectWeeklyContentService projectWeeklyContentService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查询【项目周报内容】数据详情", type = "ProjectWeeklyContent", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectWeeklyContentVO> detail(@PathVariable(value = "id") String id) throws Exception {
        ProjectWeeklyContentVO rsp = projectWeeklyContentService.detail(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectWeeklyContentDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【项目周报内容】数据", type = "ProjectWeeklyContent", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<ProjectWeeklyContentVO> create(@RequestBody ProjectWeeklyContentDTO projectWeeklyContentDTO) throws Exception {
        ProjectWeeklyContentVO rsp =  projectWeeklyContentService.create(projectWeeklyContentDTO);
        LogRecordContext.putVariable("id",rsp.getId());
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectWeeklyContentDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【项目周报内容】数据", type = "ProjectWeeklyContent", subType = "编辑", bizNo = "{{#projectWeeklyContentDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  ProjectWeeklyContentDTO projectWeeklyContentDTO) throws Exception {
        Boolean rsp = projectWeeklyContentService.edit(projectWeeklyContentDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【项目周报内容】数据", type = "ProjectWeeklyContent", subType = "编辑", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectWeeklyContentService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/pages", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】获取【项目周报内容】分页数据", type = "ProjectWeeklyContent", subType = "分页查询", bizNo = "")
    public ResponseDTO<Page<ProjectWeeklyContentVO>> pages(@RequestBody Page<ProjectWeeklyContentDTO> pageRequest) throws Exception {
        Page<ProjectWeeklyContentVO> rsp =  projectWeeklyContentService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }
}

