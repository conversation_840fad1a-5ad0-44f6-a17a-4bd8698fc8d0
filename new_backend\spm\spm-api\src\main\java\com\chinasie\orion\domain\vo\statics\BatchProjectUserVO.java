package com.chinasie.orion.domain.vo.statics;

import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/1/10 15:27
 * @description:
 */
@Data
@ApiModel(value = "BatchProjectUserVO对象", description = "项目人员信息")
public class BatchProjectUserVO{
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String projectId;

    /**
     * 项目成员
     */
    @ApiModelProperty(value = "项目成员")
    List<UserVO> userVOS;
}
