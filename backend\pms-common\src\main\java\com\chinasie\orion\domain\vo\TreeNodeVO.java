package com.chinasie.orion.domain.vo;

import cn.hutool.core.util.IdUtil;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.*;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/11/12/15:13
 * @description:
 */
public class TreeNodeVO<T extends NodeVO> implements Serializable {
    private T data;
    private List<TreeNodeVO<T>> children = new ArrayList<>();
    private Set<String> roleList = new HashSet<>();

    private Integer level=0;


    private Boolean isDelimiter;
    @ApiModelProperty("排序")
    private int sort;

    /**
     * 业务数据
     */
    private List<T> businessDataList= new ArrayList<>();

    public TreeNodeVO(T data) {
        this.data = data;
        this.children = new ArrayList<>();
        // 初始化为空集合
        this.roleList = new HashSet<>();
    }
    public TreeNodeVO() {
        this.children = new ArrayList<>();
        // 初始化为空集合
        this.roleList = new HashSet<>();
    }

    public  TreeNodeVO(T item, Integer sort) {
        this.data = item;
        this.sort = sort;
    }


    public void addChild(TreeNodeVO<T> child) {
//        child.parent = this;
        this.children.add(child);
    }

    public T getData() {
        return data;
    }


    public void setData(T data) {
        this.data = data;
    }

    public TreeNodeVO(Boolean delimiter) {

        this.isDelimiter = delimiter;
        this.children = new ArrayList<>();
    }

    public List<TreeNodeVO<T>> getChildren() {
        return children;
    }

    public void setChildren(List<TreeNodeVO<T>> children) {
        this.children = children;
    }

    public Set<String> getRoleList() {
        return roleList;
    }

    public void setRoleList(Set<String> roleList) {
        // 确保 roleList 是可变集合
        this.roleList = new HashSet<>(roleList);

    }

    public void setAddRole(String role) {
        if (Objects.nonNull(role)) {
            if (this.roleList == null) {
                this.roleList = new HashSet<>();
            }
            if (!this.roleList.contains(role)) {
                this.roleList.add(role);
            }
        }
    }


    public Object getFieldValue(String fieldName) throws IllegalAccessException {
        Field field = getField(fieldName);
        if (field != null) {
            field.setAccessible(true);
            return field.get(data.getData());
        }
        return null;
    }

    public void setFieldValue(String fieldName, Object value) throws IllegalAccessException {
        Field field = getField(fieldName);
        if (field != null) {
            field.setAccessible(true);
            field.set(data.getData(), value);
        }
    }

    private Field getField(String fieldName) {
        try {
            return data.getData().getClass().getDeclaredField(fieldName);
        } catch (NoSuchFieldException e) {
            return null;
        }
    }

    public Object getFieldValueToBusiness(String fieldName, T object) throws IllegalAccessException {
       try {
           Field field =  object.getData().getClass().getDeclaredField(fieldName);
           if (field != null) {
               field.setAccessible(true);
               return field.get(object.getData());
           }
       }catch (Exception e){
           e.printStackTrace();
       }
        return null;
    }

    public List<T> getBusinessDataList() {
        return businessDataList;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public void setBusinessDataList(List<T> businessDataList) {
        this.businessDataList = businessDataList;
    }


    public Boolean getDelimiter() {
        return isDelimiter;
    }

    public void setDelimiter(Boolean delimiter) {
        isDelimiter = delimiter;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }
}
