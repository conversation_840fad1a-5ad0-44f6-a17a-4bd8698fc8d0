package com.chinasie.orion.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.chinasie.orion.bo.JobManageTreeBO;
import com.chinasie.orion.domain.dto.JobManageSelectDTO;
import com.chinasie.orion.domain.dto.job.NewJobMangeDTO;
import com.chinasie.orion.domain.entity.CenterJobManage;


import com.chinasie.orion.domain.entity.JobManage;
import com.chinasie.orion.domain.entity.RelationOrgToJob;
import com.chinasie.orion.domain.vo.JobManageCenter;
import com.chinasie.orion.domain.vo.JobManageTree;
import com.chinasie.orion.domain.vo.JobManageTreeVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.enums.StatusEnum;
import com.chinasie.orion.feign.IcmJobFeignService;
import com.chinasie.orion.feign.dto.RepairJobManagerDTO;
import com.chinasie.orion.feign.vo.NewJobMangeVO;
import com.chinasie.orion.feign.vo.PageVO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.service.CenterJobManageService;
import com.chinasie.orion.repository.CenterJobManageMapper;

import com.chinasie.orion.service.JobManageService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <p>
 * CenterJobManage 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-14 10:35:27
 */
@Service
@Slf4j
public class CenterJobManageServiceImpl extends OrionBaseServiceImpl<CenterJobManageMapper, CenterJobManage> implements CenterJobManageService {

    @Autowired
    JobManageService jobManageService;

    @Autowired
    UserRedisHelper userRedisHelper;

    @Autowired
    private IcmJobFeignService icmJobFeignService;
    @Autowired
    private CenterJobManageMapper centerJobManageMapper;

    @Override
    public List<JobManageTreeVO> treeList(JobManageSelectDTO jobManageSelectDTO) {
        LambdaQueryWrapperX<CenterJobManage> wrapperX = new LambdaQueryWrapperX<>(CenterJobManage.class);

        if (StringUtils.hasText(jobManageSelectDTO.getKeyword())) {
            wrapperX.like(CenterJobManage::getName, jobManageSelectDTO.getKeyword())
                    .or()
                    .like(CenterJobManage::getNumber, jobManageSelectDTO.getKeyword());
        }

        if (StringUtils.hasText(jobManageSelectDTO.getRepairRound())){
            wrapperX.eq(CenterJobManage::getRepairRound, jobManageSelectDTO.getRepairRound());
        }

        List<CenterJobManage> centerJobManages = this.getBaseMapper().selectList(wrapperX);
        List<JobManageTreeBO> jobManageTreeParam = BeanCopyUtils.convertListTo(centerJobManages, JobManageTreeBO::new);

        return getTree(jobManageTreeParam);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<CenterJobManage> dealDataByNumberList(List<NewJobMangeDTO> newJobMangeDTOS,String repairRound) {
        List<String> jobNumberList= newJobMangeDTOS.stream().map(NewJobMangeDTO::getNumber).distinct().collect(Collectors.toList());
        // 到这里直接去第三方匹配然后判断是否匹配
        List<String> numberList = new ArrayList<>();
        List<CenterJobManage> centerJobManages1 = new ArrayList<>();
        boolean b = false;
        try {
            RepairJobManagerDTO repairJobManagerDTO = new RepairJobManagerDTO();
            repairJobManagerDTO.setRepairRound(repairRound);
            repairJobManagerDTO.setJobNumberList(jobNumberList);
            repairJobManagerDTO.setPageSize(jobNumberList.size());
            repairJobManagerDTO.setPageNum(1);
            ResponseDTO<PageVO<NewJobMangeVO>> pageVOResponseDTO;
            log.info("调用第三方获取数据参数：{}", JSONObject.toJSONString(repairJobManagerDTO));
            pageVOResponseDTO = icmJobFeignService.jobManageList(repairJobManagerDTO);
            log.info("调用第三方获取数据结果：{}", JSONObject.toJSONString(pageVOResponseDTO));
            List<NewJobMangeVO> thirdJobList = pageVOResponseDTO.getResult().getContent();
            numberList = thirdJobList.stream().map(NewJobMangeVO::getNumber).collect(Collectors.toList());
            // 如果获取第三放的 工单如果存在则为匹配 设置 匹配状态
            if(!CollectionUtils.isEmpty(numberList)){
                log.info("如果获取第三方的 工单如果存在则为匹配 设置 匹配状态：{}", JSONObject.toJSONString(numberList));
                List<String> finalNumberList = numberList;
                newJobMangeDTOS.forEach(item->{
                    CenterJobManage centerJobManage=  BeanCopyUtils.convertTo(item, CenterJobManage::new);
                    centerJobManage.setRepairRound(repairRound);
                    if(finalNumberList.contains(item.getNumber())){
                        centerJobManage.setMatchUp(StatusEnum.ENABLE.getIndex());
                    }else{
                        centerJobManage.setMatchUp(StatusEnum.DISABLE.getIndex());
                    }
                    centerJobManage.setSelected(StatusEnum.ENABLE.getIndex());
                    centerJobManages1.add(centerJobManage);
                });
            }else{
                newJobMangeDTOS.forEach(item->{
                    CenterJobManage centerJobManage=  BeanCopyUtils.convertTo(item, CenterJobManage::new);
                    centerJobManage.setRepairRound(repairRound);
                    centerJobManage.setMatchUp(StatusEnum.DISABLE.getIndex());
                    centerJobManage.setSelected(StatusEnum.ENABLE.getIndex());
                    centerJobManages1.add(centerJobManage);
                });
                log.info("没有匹配：{}", JSONObject.toJSONString(centerJobManages1));

            }
            b = true;
        }catch (Exception e){
            log.error("icm接口调用失败！{}",e.getCause());
            b = false;
        }
        LambdaQueryWrapperX<CenterJobManage> wrapperX = new LambdaQueryWrapperX<>(CenterJobManage.class);
        wrapperX.in(CenterJobManage::getNumber,jobNumberList);
        List<CenterJobManage> centerJobManages = this.list(wrapperX);
        if(!CollectionUtils.isEmpty(centerJobManages)){
            log.info("获取中心库中的工单列表：{}", JSONObject.toJSONString(centerJobManages));

            Map<String, CenterJobManage>  centerJobManageMap= centerJobManages.stream().collect(Collectors.toMap(CenterJobManage::getNumber, Function.identity()));
            centerJobManages1.forEach(item->{
                CenterJobManage centerJobManage=  centerJobManageMap.get(item.getNumber());
                if( Objects.nonNull(centerJobManage)){
                    item.setId(centerJobManage.getId());
                }
            });
            if(!b){
                centerJobManages.forEach(item->{
                    item.setRepairRound(repairRound);
                    item.setSelected(StatusEnum.ENABLE.getIndex());
                    item.setMatchUp(StatusEnum.DISABLE.getIndex());
                });
            }
        }else{
            if(!b){
                log.info("未获取到中心库的工单列表：{}", JSONObject.toJSONString(newJobMangeDTOS));
                newJobMangeDTOS.forEach(item->{
                    CenterJobManage centerJobManage=  BeanCopyUtils.convertTo(item, CenterJobManage::new);
                    centerJobManage.setRepairRound(repairRound);
                    centerJobManage.setSelected(StatusEnum.ENABLE.getIndex());
                    centerJobManage.setMatchUp(StatusEnum.DISABLE.getIndex());
                    centerJobManages1.add(centerJobManage);
                });
            }
        }
        if(!CollectionUtils.isEmpty(centerJobManages1)){
            this.saveOrUpdateBatch(centerJobManages1);
        }
        return centerJobManages1;
    }


    @Override
    public List<JobManageTreeBO> usedTreeList(String repairOrgId, String keyword) {
        LambdaQueryWrapperX<JobManage> wrapperX = new LambdaQueryWrapperX<>(JobManage.class);
        wrapperX.innerJoin(RelationOrgToJob.class, RelationOrgToJob::getJobNumber, JobManage::getNumber);
        wrapperX.eq(RelationOrgToJob::getRepairOrgId, repairOrgId);
        if (StringUtils.hasText(keyword)){
            wrapperX.like(JobManage::getName,keyword)
                    .or()
                    .like(JobManage::getNumber,keyword);
        }

        List<JobManage> list = jobManageService.list(wrapperX);
        if (CollectionUtils.isEmpty(list)){
            return null;
        }
        List<JobManageTreeBO> param = BeanCopyUtils.convertListTo(list, JobManageTreeBO::new);
        List<String> userIds = param.stream().map(JobManageTreeBO::getRspUserId).distinct().collect(Collectors.toList());
        List<UserVO> userByIds = userRedisHelper.getUserByIds(userIds);
        Map<String, String> idToName = userByIds.stream().collect(Collectors.toMap(UserVO::getId, UserVO::getName));

        for (JobManageTreeBO jobManageTreeBO : param) {
            jobManageTreeBO.setRspUserName(idToName.getOrDefault(jobManageTreeBO.getRspUserId(),""));
        }

        return param;
    }

    @Override
    public void updateByNumberList(List<String> jobNumberList) {
        centerJobManageMapper.updateByJobNumberListMatchUp(jobNumberList,StatusEnum.DISABLE.getIndex());
        centerJobManageMapper.removeNotMatchUpData(jobNumberList);
    }

    @Override
    public List<JobManageTreeVO> getTree(List<JobManageTreeBO> jobManageTreeParam){
        List<JobManageTreeVO> res = new ArrayList<>();

        Map<String, List<JobManageTreeBO>> roundToBo = jobManageTreeParam.stream()
                                                        .filter(item-> StringUtils.hasText(item.getRepairRound()))
                                                        .collect(Collectors.groupingBy(JobManageTreeBO::getRepairRound));

        //第一层
        roundToBo.forEach((k,v)->{
            JobManageTreeVO jobManageTreeVO = new JobManageTreeVO();
            jobManageTreeVO.setRepairRound(k);
            jobManageTreeVO.setCode(k);

            List<JobManageCenter> jobManageCenters = new ArrayList<>();
            Map<String, List<JobManageTreeBO>> centerToBo = v.stream()
                                                         .filter(item-> StringUtils.hasText(item.getWorkCenter())&&(Objects.nonNull(item.getMatchUp())&&item.getMatchUp() == 1))
                                                         .collect(Collectors.groupingBy(JobManageTreeBO::getWorkCenter));

            List<JobManageTreeBO> noMatch = v.stream()
                                                .filter(item -> item.getMatchUp()!=null&&(!StringUtils.hasText(item.getWorkCenter()) || item.getMatchUp() == 0))
                                                .collect(Collectors.toList());

            //第二层
            centerToBo.forEach((k2,v2)->{
                JobManageCenter jobManageCenter = new JobManageCenter();
                jobManageCenter.setWorkCenter(k2);
                jobManageCenter.setCode(k2);
                jobManageCenter.setParentCode(k);
                //第三层
                List<JobManageTree> jobManage = BeanCopyUtils.convertListTo(v2, JobManageTree::new);
                jobManage.forEach(item-> {
                    item.setParentCode(k2);
                    item.setCode(item.getNumber());
                    item.setClassName("jobManage");
                });
                jobManageCenter.setJobList(jobManage);
                jobManageCenters.add(jobManageCenter);
            });
            JobManageCenter jobManageCenter = new JobManageCenter();
            jobManageCenter.setWorkCenter("未匹配");
            jobManageCenter.setCode(jobManageCenter.getWorkCenter());
            jobManageCenter.setParentCode(k);
            List<JobManageTree> jobList = BeanCopyUtils.convertListTo(noMatch, JobManageTree::new);
            for (JobManageTree jobManageTree : jobList) {
                jobManageTree.setClassName("jobManage");
                jobManageTree.setCode(jobManageTree.getNumber());
                jobManageTree.setParentCode(jobManageCenter.getCode());
            }
            jobManageCenter.setJobList(jobList);
            jobManageCenters.add(jobManageCenter);
            jobManageTreeVO.setCenterList(jobManageCenters);
            res.add(jobManageTreeVO);
        });

        return res;
    }



















}
