<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
  />
</template>

<script setup lang="ts">
import {
  computed, reactive, watch, ref, defineProps,
} from 'vue';
import {
  BasicButton, Layout, OrionTable, BasicTableAction,
} from 'lyra-component-vue3';
import {
  Button, Radio,
} from 'ant-design-vue';
import Api from '/@/api';
import { AlignLeftOutlined, AppstoreOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import { getActionsList, getColumns } from './config/index';

const AButton = Button;
const ARadioGroup = Radio.Group;
const ARadioButton = Radio.Button;
const addOrEditRef = ref(null);
const tableRef = ref(null);
const state = reactive({
  addOrEditRef,
  dataSource: [],
});
const props = defineProps({
  dataInfo: {
    type: Object,
    default: () => {
    },
  },
});
const tableOptions = reactive({
  rowSelection: false,
  canResize: false,
  maxHeight: 300,
  isTableHeader: false,
  dataSource: state.dataSource,
  api: () => new Promise((resolve) => {
    setTimeout(() => {
      resolve(state.dataSource);
    });
  }),
  pagination: false,
  columns: getColumns(),
});
watch(() => props.dataInfo, () => {
  if (props.dataInfo) {
    state.dataSource = props.dataInfo?.contentVOList;
    setTimeout(() => {
      tableRef.value.reload();
    });
  } else {
    state.dataSource = [];
    setTimeout(() => {
      tableRef.value.reload();
    });
  }
});

function handle(type) {
  switch (type) {
    case 'add':
      addOrEditRef.value.openDrawer({ action: 'add' });
      break;
    case 'edit':
      addOrEditRef.value.openDrawer({ action: 'edit' });
      break;
  }
}
</script>

<style scoped lang="less"></style>
