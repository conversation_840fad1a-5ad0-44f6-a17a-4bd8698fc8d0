package com.chinasie.orion.feign.request;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Map;

/**
 * FlowTemplateBusiness Entity对象
 *
 * <AUTHOR>
 * @since 2023-09-26 17:01:52
 */
@ApiModel(value = "FlowTemplateBusinessDTO对象", description = "流程模版与业务关联表")
@Data
public class FlowTemplateBusinessDetailBatchDTO implements Serializable {
    /**
     * 业务Id
     */
    @ApiModelProperty(value = "业务Id")
    @NotBlank(message = "业务Id不能为空")
    private String businessId;

    /**
     * 数据类型编号
     */
    @ApiModelProperty(value = "数据类型编号")
    private String dataTypeCode;


    /**
     * 数据类型名称
     */
    @ApiModelProperty(value = "数据类型名称")
    private String dataTypeName;

    /**
     * 业务名称
     */
    @ApiModelProperty(value = "业务名称")
   // @NotBlank(message = "业务名称不能为空")
    private String businessName;

    /**
     * 消息url
     */
    @ApiModelProperty(value = "消息url")
    private String messageUrl;

    @ApiModelProperty("表单数据")
    private JSONObject formData;

    @ApiModelProperty("变量")
    private Map<String, Object> variables;

}
