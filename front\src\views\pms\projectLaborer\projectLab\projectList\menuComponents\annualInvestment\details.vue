<template>
  <Layout3
    v-loading="loading"
    :projectData="formData"
    :menuData="menuData"
    :type="2"
    :defaultActionId="tabsId"
    :onMenuChange="menuChange"
    class="investment-plan-details"
  >
    <template #code>
      <div>{{ `编码：${formData?.number??''}` }}</div>
    </template>
    <!--      v-if="showApprove"-->
    <template
      #header-right
    >
      <div>
        <BasicTableAction
          :actions="actionsList"
          type="button"
          :record="{data: formData}"
          :showItemNumber="2"
        />
      </div>
    </template>
    <template #footer>
      <WorkflowAction
        v-if="formData?.id"
        ref="workflowActionRef"
        :workflow-props="workflowProps"
      />
      <!--        -->
      <!--        <BpmnFooterTool-->
      <!--          :bpmnMainRef="processRef?.bpmnMain"-->
      <!--        />-->
    </template>
    <!--    <Information-->
    <!--      v-if="tabsId==='applyDetails'"-->
    <!--      :showApprove="showApprove"-->
    <!--    />-->
    <Execution
      v-if="tabsId==='applyDetails'"
      :showApprove="showApprove"
    />
    <MonthlyFeedback v-if="tabsId==='monthlyFeedback'" />
    <div
      v-if="processType"
      :class="{'process-position':tabsId!=='process'}"
      class="process-dev"
    >
      <WorkflowView
        ref="workflowViewRef"
        :workflow-props="workflowProps"
      />
    </div>
    <Adjustment
      v-if="tabsId==='adjustment'"
      ref="adjustmentRef"
      @change-page="changePage"
    />
    <AddInvestmentPlan
      @register="registerPlan"
      @update="update"
    /><!--      年度调整-->
    <AnnualInvestment
      @register="registerAnnual"
      @update="update"
    />
  </Layout3>
</template>

<script lang="ts">
import {
  computed, defineComponent, getCurrentInstance, onMounted, provide, reactive, Ref, ref, toRefs,
} from 'vue';
import {
  BasicTableAction, BpmnFooterTool, isPower, Layout3, useDrawer,
} from 'lyra-component-vue3';
import { useRoute, useRouter } from 'vue-router';
import Process from '/@/views/pms/projectManage/components/Process.vue';
import MonthlyFeedback from './modal/MonthlyFeedback.vue';
import Adjustment from './modal/Adjustment.vue';
import { getYearPlanDetails } from '../investmentPlan/index';
import AddInvestmentPlan from '../investmentPlan/components/AddInvestmentPlan.vue';
import AnnualInvestment from '../investmentPlan/components/AnnualInvestment.vue';
import { renderNotAuthPage } from '/@/views/pms/utils';
import Execution from './modal/Execution.vue';
import ProcessDetails from './modal/ProcessDetails.vue';
import { useUserStore } from '/@/store/modules/user';
import { WorkflowAction, WorkflowProps, WorkflowView } from 'lyra-workflow-component-vue3';
import Api from '/@/api';

export default defineComponent({
  name: 'InvestmentPlanDetails',
  components: {
    WorkflowAction,
    Execution,
    AddInvestmentPlan,
    Layout3,
    // Information,
    MonthlyFeedback,
    Adjustment,
    BasicTableAction,
    AnnualInvestment,
    WorkflowView,
  },
  setup() {
    const userInfo = useUserStore().getUserInfo;
    const adjustmentRef = ref();
    const route = useRoute();
    const router = useRouter();
    const processRef = ref();
    const workflowActionRef: Ref = ref();
    const workflowViewRef: Ref = ref();
    const state = reactive({
      processName: computed(() => {
        let name = `${state.formData.yearName}年投资计划`;
        if (state.formData?.dataStatus?.name.indexOf('调整') < 0) {
          name += '申报表';
        } else {
          name += '调整申请表';
        }
        return name;
      }),
      loading: false,
      processType: false,
      formData: {},
      menuData: [],
      tabsId: '',
      deliveries: [],
      showApprove: route.query.type === 'process',
      showEdit: false,
      actionsList: [
        {
          event: 'edit',
          text: '编辑',
          icon: 'sie-icon-bianji',
          onClick() {
            if (state.formData.dataStatus.name.indexOf('调整') < 0) {
              openDrawerPlan(true, {
                type: 'edit',
                id: state.formData.id,
              });
            } else {
              openDrawerAnnual(true, {
                type: 'edit',
                id: state.formData.id,
              });
            }
          },
          isShow: computed(() => {
            if (!isPower('TZJH_container_button_16', powerData)) return false;
            if (state.formData.status === 100) {
              return state.formData.creatorId === userInfo.id;
            }
            return state.showEdit;
          }),
        },
        {
          text: '发起流程',
          icon: 'sie-icon-qidongliucheng',
          isShow: workflowActionRef.value?.isAdd && state.formData?.status === 100,
          onClick() {
            workflowActionRef.value?.onAddTemplate({
              messageUrl: route.fullPath,
            });
          },
        },
      ],
    });
    function showEditBtn(data) {
      if (data.id === state.formData.id) {
        state.showEdit = data.showEditBtn;
      }
    }
    const workflowProps = computed<WorkflowProps>(() => ({
      Api,
      businessData: state.formData,
      afterEvent() {
        workflowViewRef.value?.init();
        getFormData();
      },
    }));
    const [registerPlan, { openDrawer: openDrawerPlan }] = useDrawer();
    const [registerAnnual, { openDrawer: openDrawerAnnual }] = useDrawer();
    function menuChange(val) {
      state.tabsId = val.id;
    }
    provide('formData', computed(() => state.formData));
    onMounted(() => {
      getFormData();
    });

    function setMenu() {
      state.menuData = [];
      if (route.query.type !== 'process') {
        if (isPower('TZJH_container_07', powerData)) {
          state.menuData.push({
            name: '年度投资计划执行',
            id: 'applyDetails',
          });
        }

        // if (isPower('TZJH_container_08', powerData)) {
        //   state.menuData.push({
        //     name: '月度反馈',
        //     id: 'monthlyFeedback',
        //   });
        // }
        //
        // if (isPower('TZJH_container_09', powerData)) {
        //   state.menuData.push({
        //     name: '调整记录',
        //     id: 'adjustment',
        //   });
        // }

        if (isPower('TZJH_container_10', powerData)) {
          state.menuData.push({
            name: '流程',
            id: 'process',
          });
        }
      } else {
        if (isPower('TZJH_container_07', powerData)) {
          state.menuData.push({
            name: '概述',
            id: 'applyDetails',
          });
        }

        if (isPower('TZJH_container_10', powerData)) {
          state.menuData.push({
            name: '流程',
            id: 'process',
          });
        }
      }
    }

    const currentInstance = getCurrentInstance();
    const powerData = ref([]);
    provide('powerData', powerData);

    function getFormData() {
      state.loading = true;
      getYearPlanDetails(route.params.id, 'PMS1002').then((res) => {
        powerData.value = res?.detailAuthList ?? [];
        renderNotAuthPage({
          vm: currentInstance,
          powerData: powerData.value,
        });

        setMenu();

        res.yearName = Number(res.yearName);
        let fieldList = [
          'architecture',
          'device',
          'other',
          'installation',
        ];
        let allValue = 0;
        for (let name in res) {
          if (fieldList.indexOf(name) >= 0 && res[name]) {
            allValue += Number(res[name]);
          }
        }
        res.yearComplete = allValue;
        res.executionRate = Number(res.yearComplete) ? `${((res.totalDo / res.yearComplete) * 100).toFixed(2)}%` : '0%';
        state.formData = res;
        state.loading = false;

        state.deliveries = [
          {
            deliveryId: state.formData.id,
          },
        ];
        if (state.formData.oldId) {
          state.deliveries.push({
            deliveryId: state.formData.oldId,
          });
        }
        setTimeout(() => {
          state.processType = true;
        }, 1000);
        if (!state.tabsId) {
          state.tabsId = route.query?.tabsType ? 'process' : 'applyDetails';
        }
        if (state.tabsId === 'adjustment') {
          adjustmentRef.value.update();
        }
      }).catch((err) => {
        state.loading = false;
      });
    }
    provide('getFormData', getFormData);
    provide(
      'formData',
      computed(() => state.formData),
    );
    function update() {
      getFormData();
    }
    function updateForm(id) {
      if (state.formData.id === id) {
        getFormData();
      }
    }
    function changePage(id) {
      if (id === state.formData.id) {
        state.tabsId = 'applyDetails';
      } else {
        router.push({
          name: 'AnnualInvestment',
          params: {
            id,
          },
        });
      }
    }
    return {
      ...toRefs(state),
      menuChange,
      processRef,
      registerPlan,
      update,
      changePage,
      adjustmentRef,
      isPower,
      updateForm,
      registerAnnual,
      showEditBtn,
      workflowProps,
      workflowActionRef,
      workflowViewRef,
    };
  },
});

</script>

<style lang="less" scoped>
:deep(.project-title){
  width: 300px !important;
}
:deep(.information-title) {
  font-size: 18px;
  font-weight: 500;
  color: rgba(0,0,0,0.85);
  line-height: 28px;
  position: relative;
  padding-left: 10px;
  border-bottom: 1px solid #cccccc;
  padding-bottom: 5px;
    &:before {
        content: '';
        height: 18px;
        width: 4px;
        background: ~`getPrefixVar('primary-color') `;
        display: inline-block;
        position: absolute;
        top: 5px;
        left: 0px;
    }
}

:deep(.information-title-flex){
  display: flex;
  width: 100%;
  justify-content: space-between;
  span+span{
    font-size: 14px;
    color: #959191;
  }
}

.footer-wrap {
  border-top: 1px solid #e9ecf2;
  margin: 0 ~`getPrefixVar('content-margin-left') `;
}

.process-position{
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
  pointer-events: none;
}
.process-dev{
  height: 100%;
  width: 100%;
}
</style>
