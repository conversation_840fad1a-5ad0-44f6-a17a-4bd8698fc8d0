import Api from '/@/api';
const base = '/pms';
enum zApi {
  statisticTyep = 'statistic-analysis/report-forms-type',
  statisticTable = 'statistic-analysis/report-forms-content',
  statisticEve = 'statistic-analysis/plan-status',
  peopleStatus = 'statistic-analysis/plan-status-principal',
  everydayStatus = 'statistic-analysis/plan-everyday-status',
  everydayUp = '/statistic-analysis/plan-everyday-increased'
}

export function everydayUpApi(params) {
  return new Api(base).fetch('', `${zApi.everydayUp}/${params}`, 'GET');
}
export function everydayStatusApi(params) {
  return new Api(base).fetch('', `${zApi.everydayStatus}/${params}`, 'GET');
}
export function peopleStatusApi(params) {
  return new Api(base).fetch('', `${zApi.peopleStatus}/${params}`, 'GET');
}
export function statisticTyepApi() {
  return new Api(base).fetch('', `${zApi.statisticTyep}/`, 'GET');
}
export function statisticTableApi(params) {
  return new Api(base).fetch('', `${zApi.statisticTable}/${params}`, 'GET');
}
export function statisticEveApi(params) {
  return new Api(base).fetch('', `${zApi.statisticEve}/${params}`, 'GET');
}
