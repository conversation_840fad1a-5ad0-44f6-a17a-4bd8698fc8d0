//package com.chinasie.orion.service.impl;
//
//
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.chinasie.orion.domain.dto.PlanParam;
//import com.chinasie.orion.domain.dto.PlanQueryDTO;
//import com.chinasie.orion.domain.entity.PlanToPlan;
//import com.chinasie.orion.domain.vo.PlanDetailVo;
//import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
//import com.chinasie.orion.repository.PlanToPlanRepository;
//import com.chinasie.orion.service.PlanService;
//import com.chinasie.orion.service.PlanToPlanService;
//import org.springframework.stereotype.Service;
//import org.springframework.util.CollectionUtils;
//import org.springframework.util.StringUtils;
//
//import javax.annotation.Resource;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.stream.Collectors;
//
///**
// * Created with IntelliJ IDEA.
// *
// * @author: wys
// * @date: 2022/01/17/17:33
// * @description:
// */
//@Service
//public class PlanToPlanServiceImpl extends OrionBaseServiceImpl<PlanToPlanRepository,PlanToPlan> implements PlanToPlanService {
//
//    @Resource
//    private PlanService planService;
//
//    @Override
//    public  List<PlanDetailVo> list(PlanParam planParam) throws Exception {
//        List<PlanDetailVo> planDetailVos = new ArrayList<>();
//        String planId = planParam.getPlanId();
//        LambdaQueryWrapper<PlanToPlan> planToPlanOrionChainWrapper = new LambdaQueryWrapper<>(PlanToPlan.class);
//        planToPlanOrionChainWrapper.eq(PlanToPlan::getFromId, planId);
//        List<PlanToPlan> planToPlans = this.list(planToPlanOrionChainWrapper);
//        if(CollectionUtils.isEmpty(planToPlans)){
//            return planDetailVos;
//        }
//        List<String> toIdList = planToPlans.stream().map(PlanToPlan::getToId).collect(Collectors.toList());
//        String keyWord = planParam.getKeyWord();
//        PlanQueryDTO planQueryDTO = new PlanQueryDTO();
//        if (StringUtils.hasText(keyWord)) {
//            planQueryDTO.setKeyword(keyWord);
//        }
//        planQueryDTO.setIds(toIdList);
//        List<PlanDetailVo> listByIdList = planService.getListByIdList(null, planQueryDTO);
//        if(CollectionUtils.isEmpty(listByIdList)){
//            return  planDetailVos;
//        }
//        return listByIdList;
//    }
//
//
//
//}
