package com.chinasie.orion.management.service.impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.domain.entity.MarketContract;
import com.chinasie.orion.domain.vo.MarketContractApiVO;
import com.chinasie.orion.management.constant.ProjectLabelEnum;
import com.chinasie.orion.management.constant.ProjectTypeEnum;
import com.chinasie.orion.management.domain.dto.ProjectInitiationDTO;
import com.chinasie.orion.management.domain.entity.ProjectInitiation;
import com.chinasie.orion.management.domain.vo.ProjectInitiationVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.MyBatisUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.management.repository.ProjectInitiationMapper;
import com.chinasie.orion.permission.core.core.entity.DataPermissionSearchCondition;
import com.chinasie.orion.permission.core.core.entity.FieldConf;
import com.chinasie.orion.permission.core.core.entity.SearchConditions;
import com.chinasie.orion.permission.core.core.entity.ValueExpression;
import com.chinasie.orion.permission.core.core.type.OpsType;
import com.chinasie.orion.permission.core.core.type.OptionType;
import com.chinasie.orion.permission.core.core.type.ValueExpType;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.management.service.ProjectInitiationService;
import com.chinasie.orion.service.MarketContractService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <p>
 * ProjectInitiation 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-29 17:48:53
 */
@Service
@Slf4j
public class ProjectInitiationServiceImpl extends OrionBaseServiceImpl<ProjectInitiationMapper, ProjectInitiation> implements ProjectInitiationService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private ProjectInitiationMapper projectInitiationMapper;
    @Autowired
    private MarketContractService marketContractService;
    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProjectInitiationVO detail(String id, String pageCode) throws Exception {
        ProjectInitiation projectInitiation = this.getById(id);
        ProjectInitiationVO result = BeanCopyUtils.convertTo(projectInitiation, ProjectInitiationVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param projectInitiationDTO
     */
    @Override
    public String create(ProjectInitiationDTO projectInitiationDTO) throws Exception {
        ProjectInitiation projectInitiation = BeanCopyUtils.convertTo(projectInitiationDTO, ProjectInitiation::new);
        this.save(projectInitiation);

        String rsp = projectInitiation.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param projectInitiationDTO
     */
    @Override
    public Boolean edit(ProjectInitiationDTO projectInitiationDTO) throws Exception {
        ProjectInitiation projectInitiation = BeanCopyUtils.convertTo(projectInitiationDTO, ProjectInitiation::new);

        this.updateById(projectInitiation);

        String rsp = projectInitiation.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectInitiationVO> pages(Page<ProjectInitiationDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProjectInitiation> condition = new LambdaQueryWrapperX<>(ProjectInitiation.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        //高级搜索条件
        if (pageRequest.getQuery() != null) {
            //立项标签
            if (pageRequest.getQuery().getProjectLabel() != null) {
                // 项目立项，合同编号不为空
                if (pageRequest.getQuery().getProjectLabel().equals(ProjectLabelEnum.XMLX.getDesc())) {
                    condition.isNotNull(ProjectInitiation::getContractNumbers);
                }
                // 项目立项，合同编号为空
                else if (pageRequest.getQuery().getProjectLabel().equals(ProjectLabelEnum.XMYLX.getDesc())) {
                    condition.isNull(ProjectInitiation::getContractNumbers);
                }
            }
            //申请日期
            if (pageRequest.getQuery().getStartDate() != null && pageRequest.getQuery().getEndDate() != null) {
                condition.between(ProjectInitiation::getProjectInitDate, pageRequest.getQuery().getStartDate(), pageRequest.getQuery().getEndDate());
            }
        }


        condition.orderByDesc(ProjectInitiation::getCreateTime);
        Page<ProjectInitiation> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectInitiation::new));

        //
        IPage<ProjectInitiation> mpPage = MyBatisUtils.buildPage(realPageRequest);
        IPage<ProjectInitiation> result = projectInitiationMapper.selectDataPermissionPage(mpPage,ProjectInitiation.class,condition);

        Page<ProjectInitiationVO> pageResult = new Page<>(realPageRequest.getPageNum(), realPageRequest.getPageSize(), result.getTotal());
//        new PageResult(mpPage.getRecords(), realPageRequest.getPageNum(), realPageRequest.getPageSize(), mpPage.getTotal(), mpPage.getPages());
//        PageResult<ProjectInitiation> page = this.getBaseMapper().selectPage(realPageRequest, condition);
        if(CollectionUtils.isEmpty(result.getRecords())){
            return pageResult;
        }

        List<ProjectInitiationVO> vos = BeanCopyUtils.convertListTo(result.getRecords(), ProjectInitiationVO::new);
        setEveryName(vos);

        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "项目立项导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectInitiationDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ProjectInitiationExcelListener excelReadListener = new ProjectInitiationExcelListener();
        EasyExcel.read(inputStream, ProjectInitiationDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ProjectInitiationDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("项目立项导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ProjectInitiation> projectInitiationes = BeanCopyUtils.convertListTo(dtoS, ProjectInitiation::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ProjectInitiation-import::id", importId, projectInitiationes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ProjectInitiation> projectInitiationes = (List<ProjectInitiation>) orionJ2CacheService.get("pmsx::ProjectInitiation-import::id", importId);
        log.info("项目立项导入的入库数据={}", JSONUtil.toJsonStr(projectInitiationes));

        this.saveBatch(projectInitiationes);
        orionJ2CacheService.delete("pmsx::ProjectInitiation-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ProjectInitiation-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ProjectInitiation> condition = new LambdaQueryWrapperX<>(ProjectInitiation.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ProjectInitiation::getCreateTime);
        List<ProjectInitiation> projectInitiationes = this.list(condition);
        List<ProjectInitiationVO> vos = BeanCopyUtils.convertListTo(projectInitiationes, ProjectInitiationVO::new);
        setEveryName(vos);
        List<ProjectInitiationDTO> dtos = BeanCopyUtils.convertListTo(vos, ProjectInitiationDTO::new);

        String fileName = "项目立项数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectInitiationDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<ProjectInitiationVO> vos) throws Exception {
        vos.forEach(vo -> {
            //有合同编号，则项目为正式项目，无合同编号则为预立项
            if (StringUtils.isEmpty(vo.getContractNumbers())) {
                vo.setProjectLabel(ProjectLabelEnum.XMYLX.getName());
            } else {
                vo.setProjectLabel(ProjectLabelEnum.XMLX.getName());
            }
            //项目类型暂时写死为销售项目
            vo.setProjectType(ProjectTypeEnum.XSXM.getName());
        });
    }

    @Override
    public Page<ProjectInitiationVO> permissionPages(Page<ProjectInitiationDTO> pageRequest) {

        List<ProjectInitiation> classAttributes = projectInitiationMapper.selectDataPermissionList(ProjectInitiation.class
                , new LambdaQueryWrapperX<>(ProjectInitiation.class)
        );
        System.out.println(JSONUtil.toJsonStr(classAttributes));




    //  统一编写 searchcondition
        SearchConditions searchConditions = SearchConditions.builder()
                .ops(OpsType.OR.name())
                .children(new ArrayList<>() {
                    {
                        add(
                                DataPermissionSearchCondition.builder()
                                        .key(FieldConf.builder().value("project_person_id").label("项目责任人ID").dataType("Varchar").build())
                                        .op(OptionType.EQ.name())
                                        .value(ValueExpression.builder().paramMethodExp("@projectInitiationExp.exp(#param)")
                                                .methodParam("1").type(ValueExpType.CUSTOM.name()).conditionExp("#{@projectInitiationExp.apply()}").build()).build());
                    }
                    {
                        add(
                                DataPermissionSearchCondition.builder()
                                        .key(FieldConf.builder().value("project_assume_center_id").label("承担中心id").dataType("Varchar").build())
                                        .op(OptionType.IN.name())
                                        .value(ValueExpression.builder().paramMethodExp("@projectInitiationDeptExp.exp(#param)")
                                                .methodParam("1").type(ValueExpType.CUSTOM.name()).conditionExp("#{@projectInitiationDeptExp.apply()}").build()).build());
                    }
                })
                .build();

        System.out.println(JSONUtil.toJsonStr(searchConditions));
        return null;
    }

    @Override
    public Page<ProjectInitiationVO> pagePages(Page<ProjectInitiationDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<ProjectInitiation> condition = new LambdaQueryWrapperX<>(ProjectInitiation.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        //高级搜索条件
        if (pageRequest.getQuery() != null) {
            //立项标签
            if (pageRequest.getQuery().getProjectLabel() != null) {
                // 项目立项，合同编号不为空
                if (pageRequest.getQuery().getProjectLabel().equals(ProjectLabelEnum.XMLX.getDesc())) {
                    condition.isNotNull(ProjectInitiation::getContractNumbers);
                }
                // 项目立项，合同编号为空
                else if (pageRequest.getQuery().getProjectLabel().equals(ProjectLabelEnum.XMYLX.getDesc())) {
                    condition.isNull(ProjectInitiation::getContractNumbers);
                }
            }
            //申请日期
            if (pageRequest.getQuery().getStartDate() != null && pageRequest.getQuery().getEndDate() != null) {
                condition.between(ProjectInitiation::getProjectInitDate, pageRequest.getQuery().getStartDate(), pageRequest.getQuery().getEndDate());
            }
            if(!StringUtils.isEmpty(pageRequest.getQuery().getProjectId())){
                condition.eq(ProjectInitiation::getProjectId,pageRequest.getQuery().getProjectId());
            }else{
                condition.and(item-> item.isNull(ProjectInitiation::getProjectId).or().eq(ProjectInitiation::getProjectId,""));
            }
        }else{
            condition.and(item-> item.isNull(ProjectInitiation::getProjectId).or().eq(ProjectInitiation::getProjectId,""));
        }

        condition.orderByDesc(ProjectInitiation::getCreateTime);
        Page<ProjectInitiation> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectInitiation::new));

        //
        IPage<ProjectInitiation> mpPage = MyBatisUtils.buildPage(realPageRequest);
        IPage<ProjectInitiation> result = projectInitiationMapper.selectDataPermissionPage(mpPage,ProjectInitiation.class,condition);

        Page<ProjectInitiationVO> pageResult = new Page<>(realPageRequest.getPageNum(), realPageRequest.getPageSize(), result.getTotal());
        if(CollectionUtils.isEmpty(result.getRecords())){
            return pageResult;
        }

        List<ProjectInitiationVO> vos = BeanCopyUtils.convertListTo(result.getRecords(), ProjectInitiationVO::new);
        setEveryName(vos);

        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public Boolean projectSave(List<ProjectInitiationDTO> dtoList) {
        List<ProjectInitiation> vos = BeanCopyUtils.convertListTo(dtoList, ProjectInitiation::new);
        return this.updateBatchById(vos);
    }

    @Override
    public List<MarketContractApiVO> findByProjectNumber(List<String> projectNumbers) {
        List<MarketContractApiVO> marketContractApiVOS = marketContractService.findByProjectNumber(projectNumbers);
        return marketContractApiVOS;
    }


    public static class ProjectInitiationExcelListener extends AnalysisEventListener<ProjectInitiationDTO> {

        private final List<ProjectInitiationDTO> data = new ArrayList<>();

        @Override
        public void invoke(ProjectInitiationDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ProjectInitiationDTO> getData() {
            return data;
        }
    }


}
