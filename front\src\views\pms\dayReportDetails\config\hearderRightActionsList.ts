import Api from '/@/api';
import { reactive } from 'vue';

const isShowbtn = reactive({
  isEdit: '',
  isCommit: '',
  isAudit: '',
});

function getDayilyData() {
  return new Api('/pms/projectDaily-statement/1ep61725512259939094528').fetch('', '', 'GET').then((res) => {
    if (res) {
      return [
        isShowbtn.isEdit = res.edit,
        isShowbtn.isCommit = res.commit,
        isShowbtn.isAudit = res.audit,
      ];
    }
  });
}

export function getHeaderRightActionsList() {
  return [
    {
      event: 'edit',
      text: '编辑',
      icon: 'sie-icon-bianji',
      isShow: (data) => data?.edit,
    },
    {
      event: 'approve',
      text: '审核',
      icon: '',
      isShow: (data) => data?.audit,
    },
    {
      event: 'commit',
      text: '提交',
      icon: '',
      isShow: (data) => data?.commit,
    },
  ];
}
