package com.chinasie.orion.service.impl;





import com.chinasie.orion.constant.ContractMilestoneNode;
import com.chinasie.orion.constant.MarketContractMilestoneStatusEnum;
import com.chinasie.orion.domain.entity.ContractMilestone;
import com.chinasie.orion.domain.entity.MarketContract;
import com.chinasie.orion.domain.entity.MarketContractMilestoneException;
import com.chinasie.orion.domain.dto.MarketContractMilestoneExceptionDTO;
import com.chinasie.orion.domain.entity.MarketContractMilestoneReschedule;
import com.chinasie.orion.domain.vo.MarketContractMilestoneExceptionVO;


import com.chinasie.orion.domain.vo.MarketContractMilestoneRescheduleVO;
import com.chinasie.orion.exception.BaseErrorCode;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.msc.ContractMilestoneMessageHandler;
import com.chinasie.orion.repository.ContractMilestoneMapper;
import com.chinasie.orion.repository.MarketContractMapper;
import com.chinasie.orion.service.MarketContractMilestoneExceptionService;
import com.chinasie.orion.repository.MarketContractMilestoneExceptionMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * MarketContractMilestoneException 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-30 02:55:28
 */
@Service
@Slf4j
public class MarketContractMilestoneExceptionServiceImpl extends  OrionBaseServiceImpl<MarketContractMilestoneExceptionMapper, MarketContractMilestoneException>   implements MarketContractMilestoneExceptionService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private ContractMilestoneMapper contractMilestoneMapper;

    @Autowired
    private MarketContractMapper marketContractMapper;

    @Autowired
    private ContractMilestoneMessageHandler milestoneMessageHandler;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  MarketContractMilestoneExceptionVO detail(String id,String pageCode) throws Exception {
        MarketContractMilestoneException marketContractMilestoneException =this.getById(id);
        MarketContractMilestoneExceptionVO result = BeanCopyUtils.convertTo(marketContractMilestoneException,MarketContractMilestoneExceptionVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param marketContractMilestoneExceptionDTO
     */
    @Override
    public  String create(MarketContractMilestoneExceptionDTO marketContractMilestoneExceptionDTO) throws Exception {
        MarketContractMilestoneException marketContractMilestoneException =BeanCopyUtils.convertTo(marketContractMilestoneExceptionDTO,MarketContractMilestoneException::new);

        String milestoneId = marketContractMilestoneException.getMilestoneId();
        ContractMilestone contractMilestone = contractMilestoneMapper.selectById(milestoneId);
        if(contractMilestone == null){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "未找到里程碑信息！");
        }

        if(!MarketContractMilestoneStatusEnum.PROGRESS.getStatus().equals(contractMilestone.getStatus())){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "当前里程碑状态不是进行中，不能添加异常信息！");
        }
        this.save(marketContractMilestoneException);


        String rsp=marketContractMilestoneException.getId();
        if (StringUtils.isEmpty(contractMilestone.getContractId())){
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST,"里程碑相关合同异常");
        }
        MarketContract marketContract = marketContractMapper.selectById(contractMilestone.getContractId());
        Set<String> toUser = new HashSet<>();
        toUser.add(marketContract.getTechRspUser());
        toUser.add(marketContract.getCommerceRspUser());
        toUser.add(contractMilestone.getTechRspUser());
        toUser.add(contractMilestone.getBusRspUser());
        List<String> ids = new ArrayList<>(toUser);
        milestoneMessageHandler.sendMessage(marketContractMilestoneExceptionDTO.getMilestoneId()
                ,"/pas/milestones-details"
                ,marketContract.getName()
                ,contractMilestone.getMilestoneName()
                ,ids
                ,contractMilestone.getPlatformId()
                ,contractMilestone.getOrgId()
                , ContractMilestoneNode.NODE_CONTRACT_MILESTONE_END);

        return rsp;
    }

    @Override
    public List<MarketContractMilestoneExceptionVO> listByMilestoneId(String milestoneId) throws Exception {
        LambdaQueryWrapperX<MarketContractMilestoneException> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(MarketContractMilestoneException :: getMilestoneId,milestoneId);
        lambdaQueryWrapperX.orderByDesc(MarketContractMilestoneException :: getCreateTime);
        List<MarketContractMilestoneException> rsp =  this.list(lambdaQueryWrapperX);
        return BeanCopyUtils.convertListTo(rsp, MarketContractMilestoneExceptionVO::new);
    }

    /**
     *  编辑
     *
     * * @param marketContractMilestoneExceptionDTO
     */
    @Override
    public Boolean edit(MarketContractMilestoneExceptionDTO marketContractMilestoneExceptionDTO) throws Exception {
        MarketContractMilestoneException marketContractMilestoneException =BeanCopyUtils.convertTo(marketContractMilestoneExceptionDTO,MarketContractMilestoneException::new);

        this.updateById(marketContractMilestoneException);

        String rsp=marketContractMilestoneException.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<MarketContractMilestoneExceptionVO> pages( Page<MarketContractMilestoneExceptionDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<MarketContractMilestoneException> condition = new LambdaQueryWrapperX<>( MarketContractMilestoneException. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(MarketContractMilestoneException::getCreateTime);


        Page<MarketContractMilestoneException> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), MarketContractMilestoneException::new));

        PageResult<MarketContractMilestoneException> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<MarketContractMilestoneExceptionVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<MarketContractMilestoneExceptionVO> vos = BeanCopyUtils.convertListTo(page.getContent(), MarketContractMilestoneExceptionVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "市场合同里程碑异常信息导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", MarketContractMilestoneExceptionDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        MarketContractMilestoneExceptionExcelListener excelReadListener = new MarketContractMilestoneExceptionExcelListener();
        EasyExcel.read(inputStream,MarketContractMilestoneExceptionDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<MarketContractMilestoneExceptionDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("市场合同里程碑异常信息导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<MarketContractMilestoneException> marketContractMilestoneExceptiones =BeanCopyUtils.convertListTo(dtoS,MarketContractMilestoneException::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::MarketContractMilestoneException-import::id", importId, marketContractMilestoneExceptiones, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<MarketContractMilestoneException> marketContractMilestoneExceptiones = (List<MarketContractMilestoneException>) orionJ2CacheService.get("pmsx::MarketContractMilestoneException-import::id", importId);
        log.info("市场合同里程碑异常信息导入的入库数据={}", JSONUtil.toJsonStr(marketContractMilestoneExceptiones));

        this.saveBatch(marketContractMilestoneExceptiones);
        orionJ2CacheService.delete("pmsx::MarketContractMilestoneException-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::MarketContractMilestoneException-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<MarketContractMilestoneException> condition = new LambdaQueryWrapperX<>( MarketContractMilestoneException. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(MarketContractMilestoneException::getCreateTime);
        List<MarketContractMilestoneException> marketContractMilestoneExceptiones =   this.list(condition);

        List<MarketContractMilestoneExceptionDTO> dtos = BeanCopyUtils.convertListTo(marketContractMilestoneExceptiones, MarketContractMilestoneExceptionDTO::new);

        String fileName = "市场合同里程碑异常信息数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", MarketContractMilestoneExceptionDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<MarketContractMilestoneExceptionVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class MarketContractMilestoneExceptionExcelListener extends AnalysisEventListener<MarketContractMilestoneExceptionDTO> {

        private final List<MarketContractMilestoneExceptionDTO> data = new ArrayList<>();

        @Override
        public void invoke(MarketContractMilestoneExceptionDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<MarketContractMilestoneExceptionDTO> getData() {
            return data;
        }
    }


}
