<script setup lang="ts">
import { Table } from 'ant-design-vue';
import dayjs from 'dayjs';
import { h } from 'vue';
import { useRouter } from 'vue-router';

const props = defineProps<{
  years: number[]
  data: Record<string, any>
  loading: boolean
}>();

const router = useRouter();

const columns: any[] = [
  {
    title: '',
    dataIndex: 'index',
    width: 40,
    align: 'center',
    fixed: 'left',
  },
  {
    title: '大修轮次',
    dataIndex: 'repairRound',
    width: 80,
    align: 'center',
    fixed: 'left',
    customRender({ text, record }) {
      return h('div', {
        class: 'flex-te action-btn',
        title: text,
        onClick: () => navDetails(record?.repairRoundId),
      }, text);
    },
  },
  {
    title: '工期(天)',
    dataIndex: 'workDuration',
    width: 70,
    align: 'center',
    fixed: 'left',
  },
  {
    title: '开始时间',
    dataIndex: 'beginTime',
    width: 100,
    align: 'center',
    fixed: 'left',
    customRender({ text }) {
      const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
      return h('div', {
        class: 'flex-te',
        title: str,
      }, str);
    },
  },
  {
    title: '完成时间',
    dataIndex: 'endTime',
    width: 100,
    align: 'center',
    fixed: 'left',
    customRender({ text }) {
      const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
      return h('div', {
        class: 'flex-te',
        title: str,
      }, str);
    },
  },
  {
    title: '大修状态',
    dataIndex: 'majorStatus',
    align: 'center',
    fixed: 'left',
    customRender({ text }) {
      switch (text) {
        case 121:
          return '大修准备';
        case 110:
          return '大修实施';
        case 160:
          return '大修完结';
      }
    },
  },
  {
    title: '安全违章数量',
    dataIndex: '',
    children: [
      {
        title: 'A类',
        amount: true,
        dataIndex: ['majorPlanMap', 'pms_class_a_violation'],
        width: 100,
        align: 'center',
      },
      {
        title: 'B类',
        amount: true,
        dataIndex: ['majorPlanMap', 'pms_class_b_violation'],
        width: 100,
        align: 'center',
      },
      {
        title: 'C类',
        amount: true,
        dataIndex: ['majorPlanMap', 'pms_class_c_violation'],
        width: 100,
        align: 'center',
      },
    ],
  },
  {
    title: '质量缺陷数量',
    dataIndex: '',
    children: [
      {
        title: 'F1',
        amount: true,
        dataIndex: ['majorPlanMap', 'pms_f1_defect'],
        width: 100,
        align: 'center',
      },
      {
        title: 'F2',
        amount: true,
        dataIndex: ['majorPlanMap', 'pms_f2_defect'],
        width: 100,
        align: 'center',
      },
    ],
  },
  {
    title: '指标事件',
    dataIndex: '',
    children: [
      {
        title: '异物',
        amount: true,
        dataIndex: ['majorPlanMap', 'pms_index_event_fo'],
        width: 100,
        align: 'center',
      },
      {
        title: '工业安全',
        amount: true,
        dataIndex: ['majorPlanMap', 'pms_index_event_is'],
        width: 100,
        align: 'center',
      },
      {
        title: '辐射防护',
        amount: true,
        dataIndex: ['majorPlanMap', 'pms_index_event_rp'],
        width: 100,
        align: 'center',
      },
      {
        title: '其他',
        amount: true,
        dataIndex: ['majorPlanMap', 'pms_index_event_other'],
        width: 100,
        align: 'center',
      },
    ],
  },
  {
    title: '价值创造',
    dataIndex: '',
    children: [
      {
        title: '工期节省(小时)',
        amount: true,
        dataIndex: 'economizeDuration',
        width: 120,
        align: 'center',
      },
      {
        title: '剂量节省(毫西弗)',
        amount: true,
        dataIndex: 'conserveMeter',
        width: 130,
        align: 'center',
      },
      {
        title: '是否六个0',
        subTitle: '(Y/N)',
        dataIndex: 'allZero',
        width: 100,
        align: 'center',
      },
    ],
  },
];

function navDetails(id: string) {
  router.push({
    name: 'MajorRepairsSecondDetail',
    params: {
      id,
    },
  });
}

function getAmount(dataIndex: string | string[]) {
  switch (dataIndex) {
    case 'economizeDuration':
    case 'conserveMeter':
      return props.data?.majorRepairPlanBoardDTOList?.reduce((prev, next) => prev + Number(next?.[dataIndex] || 0), 0);
    default:
      return props.data?.majorRepairPlanBoardDTOList?.reduce((prev, next) => prev + Number(next?.majorPlanMap?.[dataIndex[dataIndex.length - 1]] || 0), 0);
  }
}

function customRow(record) {
  return {
    class: `${record.majorStatus === 160 ? 'finished' : record.majorStatus === 110 ? 'progress' : record.majorStatus === 121 ? 'prepare' : ''} table-body-row`,
  };
}

function customHeaderRow() {
  return {
    class: 'table-header-row',
  };
}
</script>

<template>
  <div class="calendar-table">
    <Table
      :customRow="customRow"
      :customHeaderRow="customHeaderRow"
      :dataSource="data?.list||[]"
      :columns="columns"
      :pagination="false"
      bordered
      :loading="loading"
      size="small"
      :scroll="{x:1750,y:300}"
    >
      <template #headerCell="{title, column}">
        <div>{{ title }}</div>
        <div
          v-if="column.subTitle"
          class="c99"
        >
          {{ column.subTitle }}
        </div>
        <div
          v-if="column.amount"
          class="c99"
        >
          (合计{{ getAmount(column.dataIndex) }})
        </div>
      </template>
      <template #bodyCell="{ index, column}">
        <span v-if="column.dataIndex==='index'">{{ index + 1 }}</span>
      </template>
    </Table>
  </div>
</template>

<style scoped lang="less">
:deep(.ant-table-header) {
  & > table {
    border-top: none !important;
  }
}

:deep(.table-header-row) {
  > th {
    background-color: #eee !important;
    border-right: 2px solid #fff !important;
    border-bottom: 2px solid #fff !important;

    &.ant-table-cell-scrollbar {
      display: none;
    }
  }
}

:deep(.table-body-row) {
  > td {
    border-right: 2px solid #fff !important;
    border-bottom: 2px solid #fff !important;
  }

  &.finished > td {
    background-color: #DBECBA;
  }

  &.progress > td {
    background-color: #B3E3F8;
  }

  &.prepare > td {
    background-color: #FFF3B3;
  }
}
</style>
