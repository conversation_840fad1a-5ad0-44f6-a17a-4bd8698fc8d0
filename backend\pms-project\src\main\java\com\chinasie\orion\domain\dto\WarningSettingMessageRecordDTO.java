package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;

/**
 * WarningSettingMessageRecord Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-17 16:49:23
 */
@ApiModel(value = "WarningSettingMessageRecordDTO对象", description = "项目预警设置消息记录")
@Data
public class WarningSettingMessageRecordDTO extends ObjectDTO implements Serializable{

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 业务id
     */
    @ApiModelProperty(value = "业务id")
    private String businessId;

    /**
     * 业务名称
     */
    @ApiModelProperty(value = "业务名称")
    private String businessName;

    /**
     * 预警类型
     */
    @ApiModelProperty(value = "预警类型")
    private String warningType;

    /**
     * 预警模版字典Id
     */
    @ApiModelProperty(value = "预警模版字典Id")
    private String warningDictId;

    /**
     * 发送者id
     */
    @ApiModelProperty(value = "发送者id")
    private String senderId;

    /**
     * 发送时间
     */
    @ApiModelProperty(value = "发送时间")
    private Date senderTime;

    /**
     * 消息内容
     */
    @ApiModelProperty(value = "消息内容")
    private String messageContent;

    /**
     * 实际发送时间
     */
    @ApiModelProperty(value = "实际发送时间")
    private Date actualSenderTime;

}
