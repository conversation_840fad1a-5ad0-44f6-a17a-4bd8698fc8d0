package com.chinasie.orion.domain.entity;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;

/**
 * AdvancePaymentInvoiced Entity对象
 *
 * <AUTHOR>
 * @since 2024-11-19 15:08:49
 */
@TableName(value = "pmsx_advance_payment_invoiced")
@ApiModel(value = "AdvancePaymentInvoicedEntity对象", description = "预收款开票挂账信息")
@Data

public class AdvancePaymentInvoiced extends  ObjectEntity  implements Serializable{

    /**
     * 收入计划编号
     */
    @ApiModelProperty(value = "收入计划编号")
    @TableField(value = "income_plan_num")
    private String incomePlanNum;

    /**
     * 里程碑名称
     */
    @ApiModelProperty(value = "里程碑id")
    @TableField(value = "milestone_id")
    private String milestoneId;

    /**
     * 里程碑名称
     */
    @ApiModelProperty(value = "里程碑名称")
    @TableField(value = "milestone_name")
    private String milestoneName;
    /**
     * 收入确认类型
     */
    @ApiModelProperty(value = "收入确认类型")
    @TableField(value = "income_verify_type")
    private String incomeVerifyType;

    /**
     * 凭证编号
     */
    @ApiModelProperty(value = "凭证编号")
    @TableField(value = "certificate_serial_number")
    private String certificateSerialNumber;

    /**
     * 凭证日期
     */
    @ApiModelProperty(value = "凭证日期")
    @TableField(value = "document_date")
    private Date documentDate;

    /**
     * 不含税金额
     */
    @ApiModelProperty(value = "不含税金额")
    @TableField(value = "amt_no_tax")
    private BigDecimal amtNoTax;

    /**
     * 税额
     */
    @ApiModelProperty(value = "税额")
    @TableField(value = "tax")
    private BigDecimal tax;

    /**
     * 含税金额
     */
    @ApiModelProperty(value = "含税金额")
    @TableField(value = "amt_tax")
    private BigDecimal amtTax;

    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    @TableField(value = "project_number")
    private String projectNumber;

    /**
     * 关联合同id
     */
    @ApiModelProperty(value = "关联合同id")
    @TableField(value = "contract_id")
    private String contractId;

    /**
     * 文本
     */
    @ApiModelProperty(value = "文本")
    @TableField(value = "con_text")
    private String conText;

}
