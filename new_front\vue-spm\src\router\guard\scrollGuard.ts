import type { RouteLocationNormalized, Router } from 'vue-router';
import { guardQueue } from '/@/router';

const isHash = (href: string) => /^#/.test(href);

export function createScrollGuard(router: Router) {
  const body = document.body;

  const afterGuard = router.afterEach(async (to) => {
    // scroll top
    isHash((to as RouteLocationNormalized & { href: string })?.href) && body.scrollTo(0, 0);
    return true;
  });

  guardQueue.push(afterGuard);
}
