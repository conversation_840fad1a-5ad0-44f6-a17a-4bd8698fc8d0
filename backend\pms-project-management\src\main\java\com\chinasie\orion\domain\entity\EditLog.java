package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * EditLog Entity对象
 *
 * <AUTHOR>
 * @since 2024-10-17 09:25:09
 */
@TableName(value = "pmsx_edit_log")
@ApiModel(value = "EditLogEntity对象", description = "调整记录表")
@Data

public class EditLog extends  ObjectEntity  implements Serializable{

    /**
     * 计划id
     */
    @ApiModelProperty(value = "计划id")
    @TableField(value = "plan_id")
    private String planId;

    /**
     * 中心名称
     */
    @ApiModelProperty(value = "中心名称")
    @TableField(value = "center_name")
    private String centerName;

    /**
     * 成本类型
     */
    @ApiModelProperty(value = "成本类型")
    @TableField(value = "cost_type")
    private String costType;

    /**
     * 成本名称
     */
    @ApiModelProperty(value = "成本名称")
    @TableField(value = "cost_name")
    private String costName;

    /**
     * 调整数量
     */
    @ApiModelProperty(value = "调整数量")
    @TableField(value = "num")
    private Integer num;

    /**
     * 调整人id
     */
    @ApiModelProperty(value = "调整人id")
    @TableField(value = "edit_person_id")
    private String editPersonId;

    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间")
    @TableField(value = "submit_time")
    private Date submitTime;

    /**
     * 审批时间
     */
    @ApiModelProperty(value = "审批时间")
    @TableField(value = "assessment_time")
    private Date assessmentTime;

    /**
     * 审批人id
     */
    @ApiModelProperty(value = "审批人id")
    @TableField(value = "assessment_person_id")
    private String assessmentPersonId;

    /**
     * 审批意见
     */
    @ApiModelProperty(value = "审批意见")
    @TableField(value = "assessment_advice")
    private String assessmentAdvice;

    /**
     * 审批人姓名
     */
    @ApiModelProperty(value = "审批人姓名")
    @TableField(value = "assessment_person")
    private String assessmentPerson;

    /**
     * 调整人姓名
     */
    @ApiModelProperty(value = "调整人姓名")
    @TableField(value = "edit_person")
    private String editPerson;


    @ApiModelProperty(value = "调整前数量")
    @TableField(value = "before_num")
    private Integer beforeNum;
}
