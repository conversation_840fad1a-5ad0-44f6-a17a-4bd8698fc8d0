<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.springframework.cloud</groupId>
  <artifactId>spring-cloud-dependencies-parent</artifactId>
  <version>3.1.6</version>
  <packaging>pom</packaging>
  <name>spring-cloud-dependencies-parent</name>
  <description>Spring Cloud Build Dependencies</description>
  <url>https://projects.spring.io/spring-cloud/</url>
  <organization>
    <name>Pivotal Software, Inc.</name>
    <url>https://www.spring.io</url>
  </organization>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
      <comments>Copyright 2014-2021 the original author or authors.

				Licensed under the Apache License, Version 2.0 (the "License");
				you may not use this file except in compliance with the License.
				You may obtain a copy of the License at

				https://www.apache.org/licenses/LICENSE-2.0

				Unless required by applicable law or agreed to in writing, software
				distributed under the License is distributed on an "AS IS" BASIS,
				WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
				implied.

				See the License for the specific language governing permissions and
				limitations under the License.</comments>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>dsyer</id>
      <name>Dave Syer</name>
      <email>dsyer at pivotal.io</email>
      <organization>Pivotal Software, Inc.</organization>
      <organizationUrl>https://www.spring.io</organizationUrl>
      <roles>
        <role>Project lead</role>
      </roles>
    </developer>
    <developer>
      <id>sgibb</id>
      <name>Spencer Gibb</name>
      <email>sgibb at pivotal.io</email>
      <organization>Pivotal Software, Inc.</organization>
      <organizationUrl>https://www.spring.io</organizationUrl>
      <roles>
        <role>Project lead</role>
      </roles>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:git://github.com/spring-cloud/spring-cloud-build.git</connection>
    <developerConnection>scm:git:ssh://**************/spring-cloud/spring-cloud-build.git</developerConnection>
    <url>https://github.com/spring-cloud/spring-cloud-build</url>
  </scm>
  <distributionManagement>
    <repository>
      <id>repo.spring.io</id>
      <name>Spring Release Repository</name>
      <url>https://repo.spring.io/libs-release-local</url>
    </repository>
    <snapshotRepository>
      <id>repo.spring.io</id>
      <name>Spring Snapshot Repository</name>
      <url>https://repo.spring.io/libs-snapshot-local</url>
    </snapshotRepository>
    <site>
      <id>spring-docs</id>
      <url>https:/docs.spring.io/spring-cloud-dependencies-parent/docs/3.1.6/reference/html/</url>
    </site>
    <downloadUrl>https://github.com/spring-cloud</downloadUrl>
  </distributionManagement>
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>
  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <artifactId>maven-enforcer-plugin</artifactId>
          <version>${maven-enforcer-plugin.version}</version>
          <executions>
            <execution>
              <id>enforce-versions</id>
              <goals>
                <goal>enforce</goal>
              </goals>
            </execution>
          </executions>
          <configuration>
            <fail>false</fail>
            <rules>
              <dependencyConvergence />
            </rules>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>
  <profiles>
    <profile>
      <id>milestone</id>
      <distributionManagement>
        <repository>
          <id>repo.spring.io</id>
          <name>Spring Milestone Repository</name>
          <url>https://repo.spring.io/libs-milestone-local</url>
        </repository>
      </distributionManagement>
    </profile>
    <profile>
      <id>central</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>flatten-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>flatten</id>
                <phase>process-resources</phase>
                <goals>
                  <goal>flatten</goal>
                </goals>
                <configuration>
                  <updatePomFile>true</updatePomFile>
                  <flattenMode>bom</flattenMode>
                  <pomElements>
                    <parent>expand</parent>
                    <pluginManagement>keep</pluginManagement>
                    <properties>keep</properties>
                    <repositories>remove</repositories>
                    <profiles>keep</profiles>
                  </pomElements>
                </configuration>
              </execution>
              <execution>
                <id>flatten-clean</id>
                <phase>clean</phase>
                <goals>
                  <goal>clean</goal>
                </goals>
              </execution>
            </executions>
            <inherited>true</inherited>
          </plugin>
          <plugin>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>1.6</version>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
      <distributionManagement>
        <repository>
          <id>sonatype-nexus-staging</id>
          <name>Nexus Release Repository</name>
          <url>https://s01.oss.sonatype.org/service/local/staging/deploy/maven2/</url>
        </repository>
        <snapshotRepository>
          <id>sonatype-nexus-snapshots</id>
          <name>Sonatype Nexus Snapshots</name>
          <url>https://s01.oss.sonatype.org/content/repositories/snapshots</url>
        </snapshotRepository>
      </distributionManagement>
    </profile>
    <profile>
      <id>spring</id>
      <repositories>
        <repository>
          <snapshots>
            <enabled>true</enabled>
          </snapshots>
          <id>spring-snapshots</id>
          <name>Spring Snapshots</name>
          <url>https://repo.spring.io/snapshot</url>
        </repository>
        <repository>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
          <id>spring-milestones</id>
          <name>Spring Milestones</name>
          <url>https://repo.spring.io/milestone</url>
        </repository>
        <repository>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
          <id>spring-releases</id>
          <name>Spring Releases</name>
          <url>https://repo.spring.io/release</url>
        </repository>
      </repositories>
      <pluginRepositories>
        <pluginRepository>
          <snapshots>
            <enabled>true</enabled>
          </snapshots>
          <id>spring-snapshots</id>
          <name>Spring Snapshots</name>
          <url>https://repo.spring.io/snapshot</url>
        </pluginRepository>
        <pluginRepository>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
          <id>spring-milestones</id>
          <name>Spring Milestones</name>
          <url>https://repo.spring.io/milestone</url>
        </pluginRepository>
        <pluginRepository>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
          <id>spring-releases</id>
          <name>Spring Releases</name>
          <url>https://repo.spring.io/release</url>
        </pluginRepository>
      </pluginRepositories>
    </profile>
  </profiles>
</project>
