package com.chinasie.orion.msc;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.constant.MessageNodeDict;
import com.chinasie.orion.domain.vo.BasicUserCertificateVO;
import com.chinasie.orion.msc.api.MscBuildHandler;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class PersonVerificationMsgHandler implements MscBuildHandler<BasicUserCertificateVO> {

    @Autowired
    UserRedisHelper userRedisHelper;

    @Override
    public SendMessageDTO buildMsc(BasicUserCertificateVO basicUserCertificate, Object... objects) {
        Map<String,Object> messageMap = new HashMap<>();
        SimpleUser simpleUserByCode = userRedisHelper.getSimpleUserByCode(basicUserCertificate.getUserCode());
        messageMap.put("$date$", DateUtil.format(basicUserCertificate.getObtainDate(), "yyyy-MM-dd"));
        messageMap.put("$level$",basicUserCertificate.getCertificateLevelName());
        messageMap.put("$name$",basicUserCertificate.getCertificateName());
        log.info(" 证书消息相关的实体：{} ", JSONUtil.toJsonStr(basicUserCertificate));
        log.info(" 证书消息相关的级别：{} ",basicUserCertificate.getCertificateLevelName());
        SendMessageDTO sendMsc = SendMessageDTO.builder()
                .businessId(basicUserCertificate.getId())
                .todoStatus(0)
                .messageUrl("/pms/employee-capability-pool/"+objects[0].toString())
                .messageUrlName("员工能力详情")
                .todoType(TodoTypeDict.TODO_TYPE_TASK)
                .recipientIdList(Collections.singletonList(simpleUserByCode.getId()))
                .messageMap(messageMap)
                .titleMap(messageMap)
                .senderTime(new Date())
                .senderId(basicUserCertificate.getCreatorId())
                .platformId(basicUserCertificate.getPlatformId())
                .orgId(basicUserCertificate.getOrgId())
                .build();

        return sendMsc;
    }

    @Override
    public String support() {
        return MessageNodeDict.NODE_PERSON_VERIFICATION;
    }
}
