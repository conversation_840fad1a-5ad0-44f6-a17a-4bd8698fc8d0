package com.chinasie.orion.schedule;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.amqp.entity.OrionNormalMessageDTO;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.bo.PmsMQProducer;
import com.chinasie.orion.bo.UserBo;
import com.chinasie.orion.constant.ProjectStatusEnum;
import com.chinasie.orion.constant.TakeEffectEnum;
import com.chinasie.orion.constant.WarningFrequencyConstant;
import com.chinasie.orion.constant.WarningSettingWarningCategoryEnum;
import com.chinasie.orion.conts.ProjectNodeTypeEnum;
import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.ComputeIntervalDayVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.feign.UserFeignService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.repository.WarningSettingMessageRecipientRepository;
import com.chinasie.orion.repository.WarningSettingMessageRecordRepository;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.ResponseUtils;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.chinasie.orion.constant.DictConstant.*;
import static com.chinasie.orion.constant.MessageNodeConstant.*;
import static com.chinasie.orion.constant.StatusConstant.*;
import static com.chinasie.orion.constant.WarningRoleConstant.CREATOR;
import static com.chinasie.orion.constant.WarningRoleConstant.PRINCIPAL;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/9/26 9:29
 */
@Component
public class WarningSettingXxlJob {

    @Resource
    private WarningSettingService warningSettingService;

    @Resource
    private ProjectSchemeService projectSchemeService;

    @Resource
    private DemandManagementService demandManagementService;

    @Resource
    private QuestionManagementService questionManagementService;

    @Resource
    private WarningToRoleService warningToRoleService;

    @Resource
    private PmsMQProducer pmsMQProducer;

    @Resource
    private ProjectRoleUserService projectRoleUserService;

    @Resource
    private UserBo userBo;

    @Resource
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Resource
    private UserRedisHelper userRedisHelper;

    @Resource
    private WarningSettingMessageRecordRepository warningSettingMessageRecordRepository;

    @Resource
    private WarningSettingMessageRecipientRepository warningSettingMessageRecipientRepository;

    @Resource
    private RiskManagementService riskManagementService;

    @Resource
    private UserFeignService userFeignService;

    @Resource
    private ProjectService projectService;

    private final Logger logger = LoggerFactory.getLogger(WarningSettingXxlJob.class);


    @XxlJob("warningSettingJobHandler")
    public void sendMessage() throws Exception {
        logger.info("------------------------------------风险预警发送消息--------------------------------------------------");

        XxlJobHelper.log("业务组织的查询数据范围缓存，开始时间：{}", DateUtil.date());
        try{
//        String key = "WarningSettingTimer";
//        Boolean b = redisTemplate.opsForValue().setIfAbsent(key, key, 300000, TimeUnit.MILLISECONDS);
//        if (b == null || !b) {
//            return;
//        }

            //发送人 系统管理员
            SimpleUser simpleUser = userRedisHelper.getSimpleUserByCode("100001");
            List<UserVO> userVOS = new ArrayList<>();
            if(simpleUser != null){
                userVOS.add(userRedisHelper.getUserById(simpleUser.getId()));
            }

            if (CollectionUtils.isEmpty(userVOS)) {
                return;
            }
            Map<String, String> pIdToUserIdMap = userVOS.stream().collect(Collectors.toMap(ObjectVO::getPlatformId, ObjectVO::getId));
            List<String> pIdList = new ArrayList<>(pIdToUserIdMap.keySet());
            Date date = new Date();
            //已暂停、已终止的项目关闭定时
            List<String> projectIdList = projectService.list(new LambdaQueryWrapperX<>(Project.class).select(Project::getId)
                    .in(Project::getStatus, CollUtil.toList(ProjectStatusEnum.PAUSED.getStatus(), ProjectStatusEnum.TERMINATED.getStatus())))
                    .stream().map(Project::getId).collect(Collectors.toList());
            this.planAhead(date, pIdList, pIdToUserIdMap, projectIdList);
            this.planOver(date, pIdList, pIdToUserIdMap, projectIdList);
            this.milestoneAhead(date, pIdList, pIdToUserIdMap, projectIdList);
            this.milestoneOver(date, pIdList, pIdToUserIdMap, projectIdList);
            this.riskOver(date, pIdList, pIdToUserIdMap, projectIdList);
            this.riskAhead(date, pIdList, pIdToUserIdMap, projectIdList);
            this.questionAhead(date, pIdList, pIdToUserIdMap, projectIdList);
            this.questionOver(date, pIdList, pIdToUserIdMap, projectIdList);
        }
        catch (Exception e){

        }


     //   redisTemplate.delete(key);

    }

    private void planAhead(Date date, List<String> pIdList, Map<String, String> pIdToUserIdMap, List<String> projectIdList) throws Exception {
        LambdaQueryWrapper<WarningSetting> warningSettingWrapper = new LambdaQueryWrapper<>(WarningSetting.class);
        warningSettingWrapper
                .eq(WarningSetting::getDictValueId, Warning_Plan_Ahead)
                .eq(WarningSetting :: getTakeEffect, TakeEffectEnum.EFFECT.getStatus())
                .in(WarningSetting::getPlatformId, pIdList.toArray());
        if (CollectionUtil.isNotEmpty(projectIdList)) {
            warningSettingWrapper.notIn(WarningSetting::getProjectId, projectIdList);
        }
        List<WarningSetting> warningSettingDTOList = warningSettingService.list(warningSettingWrapper);
        for (WarningSetting w : warningSettingDTOList) {
                String frequency = w.getFrequency();
                String warningWay = w.getWarningWay();
                Integer dayNum = w.getDayNum();
                if(dayNum == null || dayNum <= 0){
                    continue;
                }
                if (StringUtils.hasText(frequency) && StringUtils.hasText(warningWay) && Objects.nonNull(dayNum)) {
                    //提醒人
                    List<WarningToRole> warningToRoleList = warningToRoleService.list(new LambdaQueryWrapper<>(WarningToRole.class)
                            .eq(WarningToRole::getFromId, w.getId()));
                    if (!CollectionUtils.isEmpty(warningToRoleList)) {
                        String projectId = w.getProjectId();
                        LambdaQueryWrapper<ProjectScheme> wrapper = new LambdaQueryWrapper<>(ProjectScheme.class);
                        wrapper.eq(ProjectScheme::getProjectId, projectId).
                                eq(ProjectScheme::getNodeType, ProjectNodeTypeEnum.PLAN.getCode()).
                                ne(ProjectScheme::getStatus, PLAN_FINISH_STATUS);
                        List<ProjectScheme> sendPlans = new ArrayList<>();
                        boolean isTrue = handleSchemeAhead(sendPlans,w,date,dayNum,wrapper);
                        if(!isTrue || CollectionUtils.isEmpty(sendPlans)){
                            continue;
                        }
                        List<ProjectSchemeDTO> planDTOList = BeanCopyUtils.convertListTo(sendPlans, ProjectSchemeDTO::new);
                        this.planCommon(planDTOList, projectId, warningToRoleList, PMS_JH_LQ, pIdToUserIdMap.get(w.getPlatformId()), "/pms/planManagement/planDetails", w.getTime(), w.getRemark(), w.getWarningType(), w.getDictValueId());
                    }

                }
        }
    }

    private void planOver(Date date, List<String> pIdList, Map<String, String> pIdToUserIdMap, List<String> projectIdList) throws Exception {
        LambdaQueryWrapper<WarningSetting> warningSettingWrapper = new LambdaQueryWrapper<>(WarningSetting.class);
        warningSettingWrapper
                .eq(WarningSetting::getDictValueId, Warning_Plan_Over)
                .eq(WarningSetting::getTakeEffect, TakeEffectEnum.EFFECT.getStatus())
                .in(WarningSetting::getPlatformId, pIdList.toArray());
        if (CollectionUtil.isNotEmpty(projectIdList)) {
            warningSettingWrapper.notIn(WarningSetting::getProjectId, projectIdList);
        }
        List<WarningSetting> warningSettingDTOList = warningSettingService.list(warningSettingWrapper);
        for (WarningSetting w : warningSettingDTOList) {
                String frequency = w.getFrequency();
                String warningWay = w.getWarningWay();
                Integer dayNum = w.getDayNum();
                if(dayNum == null || dayNum <= 0){
                    continue;
                }
                if (StringUtils.hasText(frequency) && StringUtils.hasText(warningWay) && Objects.nonNull(dayNum)) {
                    //提醒人
                    List<WarningToRole> warningToRoleList = warningToRoleService.list(new LambdaQueryWrapper<>(WarningToRole.class)
                            .eq(WarningToRole::getFromId, w.getId()));
                    if (!CollectionUtils.isEmpty(warningToRoleList)) {
                        String projectId = w.getProjectId();
                        LambdaQueryWrapper<ProjectScheme> wrapper = new LambdaQueryWrapper<>(ProjectScheme.class);
                        wrapper.eq(ProjectScheme::getProjectId, projectId).
                                eq(ProjectScheme::getNodeType, ProjectNodeTypeEnum.PLAN.getCode()).
                                ne(ProjectScheme::getStatus, PLAN_FINISH_STATUS);

                        List<ProjectScheme> sendPlans = new ArrayList<>();
                        boolean isTrue = handleSchemeOver(sendPlans,w,date,dayNum,wrapper);
                        if(!isTrue || CollectionUtils.isEmpty(sendPlans)){
                            continue;
                        }

                        List<ProjectSchemeDTO> planDTOList = BeanCopyUtils.convertListTo(sendPlans, ProjectSchemeDTO::new);
                        this.planCommon(planDTOList, projectId, warningToRoleList, PMS_JH_YQ, pIdToUserIdMap.get(w.getPlatformId()), "/pms/planManagement/planDetails", w.getTime(), w.getRemark(), w.getWarningType(), w.getDictValueId());
                    }
            }
        }
    }

    private void planCommon(List<ProjectSchemeDTO> planDTOList, String projectId, List<WarningToRole> warningToRoleList, String node, String sendId, String url, String time, String msgContent, String warningType, String dictValueId) throws Exception {
        if (!CollectionUtils.isEmpty(planDTOList)) {
            List<String> toIdList = warningToRoleList.stream().map(WarningToRole::getToId).collect(Collectors.toList());
            List<String> roleIdList = toIdList.stream()
                    .filter(r -> !Objects.equals(CREATOR, r) && !Objects.equals(PRINCIPAL, r)).collect(Collectors.toList());
            boolean isCreator = toIdList.contains(CREATOR);
            boolean isPrincipal = toIdList.contains(PRINCIPAL);
            List<String> userIdList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(roleIdList)) {
                List<ProjectRoleUser> projectRoleUserDTOList = projectRoleUserService.list(new LambdaQueryWrapper<>(ProjectRoleUser.class)
                        .eq(ProjectRoleUser::getProjectId, projectId)
                        .in(ProjectRoleUser::getProjectRoleId, roleIdList.toArray()));
                userIdList.addAll(projectRoleUserDTOList.stream().map(ProjectRoleUser::getUserId).collect(Collectors.toList()));
            }
            for (ProjectSchemeDTO planDTO : planDTOList) {
                List<String> recipientIdList = new ArrayList<>();
                if (isCreator) {
                    recipientIdList.add(planDTO.getCreatorId());
                }
                if (isPrincipal) {
                    recipientIdList.add(planDTO.getRspUser());
                }
                recipientIdList.addAll(userIdList);
                if(StringUtils.hasText(msgContent)){
                    packageNormalMessage(projectId, planDTO.getId(), planDTO.getName(), recipientIdList.stream().distinct().collect(Collectors.toList())
                            , sendId, String.format("%s?id=%s&projectId=%s", url, planDTO.getId(), planDTO.getProjectId())
                            , planDTO.getPlatformId(), planDTO.getOrgId(), time, msgContent ,warningType, dictValueId, planDTO.getPlatformId());
                }
                else{
                    packageMessage(node, projectId, planDTO.getId(), planDTO.getName(), recipientIdList.stream().distinct().collect(Collectors.toList())
                            , sendId, String.format("%s?id=%s&projectId=%s", url, planDTO.getId(), planDTO.getProjectId())
                            , planDTO.getPlatformId(), planDTO.getOrgId(), time, warningType, dictValueId, planDTO.getPlatformId());
                }
            }
        }
    }

    private void milestoneAhead(Date date, List<String> pIdList, Map<String, String> pIdToUserIdMap, List<String> projectIdList) throws Exception {
        LambdaQueryWrapper<WarningSetting> warningSettingWrapper = new LambdaQueryWrapper<>(WarningSetting.class);
        warningSettingWrapper
                .eq(WarningSetting::getDictValueId, Warning_Milestone_Ahead)
                .eq(WarningSetting :: getTakeEffect, TakeEffectEnum.EFFECT.getStatus())
                .in(WarningSetting::getPlatformId, pIdList.toArray());
        if (CollectionUtil.isNotEmpty(projectIdList)) {
            warningSettingWrapper.notIn(WarningSetting::getProjectId, projectIdList);
        }
        List<WarningSetting> warningSettingDTOList = warningSettingService.list(warningSettingWrapper);
        for (WarningSetting w : warningSettingDTOList) {
            String frequency = w.getFrequency();
            String warningWay = w.getWarningWay();
            Integer dayNum = w.getDayNum();
            if(dayNum == null || dayNum <= 0){
                continue;
            }
            if (StringUtils.hasText(frequency) && StringUtils.hasText(warningWay) && Objects.nonNull(dayNum)) {
                //提醒人
                List<WarningToRole> warningToRoleList = warningToRoleService.list(new LambdaQueryWrapper<>(WarningToRole.class)
                        .eq(WarningToRole::getFromId, w.getId()));
                if (!CollectionUtils.isEmpty(warningToRoleList)) {
                    String projectId = w.getProjectId();
                    LambdaQueryWrapper<ProjectScheme> wrapper = new LambdaQueryWrapper<>(ProjectScheme.class);
                    wrapper.eq(ProjectScheme::getProjectId, projectId).
                            eq(ProjectScheme::getNodeType, ProjectNodeTypeEnum.MILESTONE.getCode()).
                            ne(ProjectScheme::getStatus, PLAN_FINISH_STATUS);
                    List<ProjectScheme> sendPlans = new ArrayList<>();
                    boolean isTrue = handleSchemeAhead(sendPlans,w,date,dayNum,wrapper);
                    if(!isTrue || CollectionUtils.isEmpty(sendPlans)){
                        continue;
                    }
                    List<ProjectSchemeDTO> planDTOList = BeanCopyUtils.convertListTo(sendPlans, ProjectSchemeDTO::new);
                    this.planCommon(planDTOList, projectId, warningToRoleList, PMS_LCB_LQ, pIdToUserIdMap.get(w.getPlatformId()), "/pms/planManagement/planDetails", w.getTime(), w.getRemark(), w.getWarningType(), w.getDictValueId());
                }

            }
        }
    }

    private void milestoneOver(Date date, List<String> pIdList, Map<String, String> pIdToUserIdMap, List<String> projectIdList) throws Exception {
        LambdaQueryWrapper<WarningSetting> warningSettingWrapper = new LambdaQueryWrapper<>(WarningSetting.class);
        warningSettingWrapper
                .eq(WarningSetting::getDictValueId, Warning_Milestone_Over)
                .eq(WarningSetting::getTakeEffect, TakeEffectEnum.EFFECT.getStatus())
                .in(WarningSetting::getPlatformId, pIdList.toArray());
        if (CollectionUtil.isNotEmpty(projectIdList)) {
            warningSettingWrapper.notIn(WarningSetting::getProjectId, projectIdList);
        }
        List<WarningSetting> warningSettingDTOList = warningSettingService.list(warningSettingWrapper);
        for (WarningSetting w : warningSettingDTOList) {
                String frequency = w.getFrequency();
                String warningWay = w.getWarningWay();
                Integer dayNum = w.getDayNum();
                if (StringUtils.hasText(frequency) && StringUtils.hasText(warningWay) && Objects.nonNull(dayNum)) {
                    //提醒人
                    List<WarningToRole> warningToRoleList = warningToRoleService.list(new LambdaQueryWrapper<>(WarningToRole.class)
                            .eq(WarningToRole::getFromId, w.getId()));
                    if (!CollectionUtils.isEmpty(warningToRoleList)) {
                        String projectId = w.getProjectId();
                        LambdaQueryWrapper<ProjectScheme> wrapper = new LambdaQueryWrapper<>(ProjectScheme.class);
                        wrapper.eq(ProjectScheme::getProjectId, projectId).
                                eq(ProjectScheme::getNodeType, ProjectNodeTypeEnum.MILESTONE.getCode()).
                                ne(ProjectScheme::getStatus, PLAN_FINISH_STATUS);
                        List<ProjectScheme> sendPlans = new ArrayList<>();
                        boolean isTrue = handleSchemeOver(sendPlans,w,date,dayNum,wrapper);
                        if(!isTrue || CollectionUtils.isEmpty(sendPlans)){
                            continue;
                        }
                        List<ProjectSchemeDTO> planDTOList = BeanCopyUtils.convertListTo(sendPlans, ProjectSchemeDTO::new);
                        this.planCommon(planDTOList, projectId, warningToRoleList, PMS_LCB_YQ, pIdToUserIdMap.get(w.getPlatformId()), "/pms/planManagement/milestoneDetails", w.getTime(), w.getRemark(), w.getWarningType(), w.getDictValueId());
                    }
            }
        }
    }

    private boolean handleSchemeAhead(List<ProjectScheme> sendPlans,WarningSetting w,Date date, Integer dayNum,LambdaQueryWrapper<ProjectScheme> wrapper){
        String warningCategory = w.getWarningCategory();
        if(WarningSettingWarningCategoryEnum.DAY.getCode().equals(warningCategory)){
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            calendar.add(Calendar.DATE, dayNum);
            Date endDate  = calendar.getTime();
            if (Objects.equals(WarningFrequencyConstant.Once, w.getFrequency())) {
                calendar.add(Calendar.DATE, -1);
            }
            else{
                calendar.setTime(date);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
            }
            Date startDate = calendar.getTime();
            wrapper.between(ProjectScheme::getEndTime, startDate, endDate);
        }
        else if(WarningSettingWarningCategoryEnum.PERCENTAGE.getCode().equals(warningCategory)){
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            wrapper.ge(ProjectScheme::getEndTime, calendar.getTime());
            wrapper.lt(ProjectScheme::getBeginTime, calendar.getTime());
        }
        else{
            return false;
        }


        List<ProjectScheme> plans = projectSchemeService.list(wrapper);
        if(CollectionUtils.isEmpty(plans)){
            return false;
        }


        //提醒工期百分比
        if(WarningSettingWarningCategoryEnum.PERCENTAGE.getCode().equals(warningCategory)){
            List<ComputeIntervalDayDTO> computeIntervalDayDTOSTotal = new ArrayList<>();
            List<ComputeIntervalDayDTO> computeIntervalDayDTOSUsed = new ArrayList<>();
            for(ProjectScheme projectScheme : plans){
                ComputeIntervalDayDTO computeIntervalDayDTOTotal = new ComputeIntervalDayDTO();
                computeIntervalDayDTOTotal.setBusinessId(projectScheme.getId());
                computeIntervalDayDTOTotal.setStartDate(projectScheme.getBeginTime());
                computeIntervalDayDTOTotal.setEndDate(projectScheme.getEndTime());

                ComputeIntervalDayDTO computeIntervalDayDTOUsed = new ComputeIntervalDayDTO();
                computeIntervalDayDTOUsed.setBusinessId(projectScheme.getId());
                computeIntervalDayDTOUsed.setStartDate(projectScheme.getBeginTime());
                computeIntervalDayDTOUsed.setEndDate(new Date());
                computeIntervalDayDTOSTotal.add(computeIntervalDayDTOTotal);
                computeIntervalDayDTOSUsed.add(computeIntervalDayDTOUsed);
            }
            try{
                ResponseDTO<List<ComputeIntervalDayVO>> responseDTOTotal = userFeignService.computeIntervalDayBatch(computeIntervalDayDTOSTotal);
                if(ResponseUtils.fail(responseDTOTotal)){
                    logger.error(JSONObject.toJSONString(responseDTOTotal));
                    return false;
                }

                ResponseDTO<List<ComputeIntervalDayVO>> responseDTOUsed = userFeignService.computeIntervalDayBatch(computeIntervalDayDTOSUsed);
                if(ResponseUtils.fail(responseDTOUsed)){
                    logger.error(JSONObject.toJSONString(responseDTOUsed));
                    return false;
                }

                List<ComputeIntervalDayVO> computeIntervalDayVOListTotal =  responseDTOTotal.getResult();

                Map<String,ComputeIntervalDayVO> computeIntervalDayVOMapTotal = computeIntervalDayVOListTotal.stream().collect(Collectors.toMap(ComputeIntervalDayVO :: getBusinessId, Function.identity()));

                List<ComputeIntervalDayVO> computeIntervalDayVOListUsed =  responseDTOUsed.getResult();

                Map<String,ComputeIntervalDayVO> computeIntervalDayVOMapUsed = computeIntervalDayVOListUsed.stream().collect(Collectors.toMap(ComputeIntervalDayVO :: getBusinessId, Function.identity()));

                for(ProjectScheme projectScheme : plans){
                    ComputeIntervalDayVO computeIntervalDayVOTotal = computeIntervalDayVOMapTotal.get(projectScheme.getId());
                    ComputeIntervalDayVO computeIntervalDayVOUsed = computeIntervalDayVOMapUsed.get(projectScheme.getId());
                    Long workdayNumTotal = computeIntervalDayVOTotal.getWorkdayNum();
                    Long workdayNumUsed = computeIntervalDayVOUsed.getWorkdayNum();
                    if(workdayNumTotal == null || workdayNumTotal < 1){
                        continue;
                    }
                    if(workdayNumUsed == null || workdayNumUsed < 1){
                        continue;
                    }
                    if(workdayNumUsed.floatValue() / workdayNumTotal >= dayNum) {
                        if(Objects.equals(WarningFrequencyConstant.Once, w.getFrequency())){
                            LambdaQueryWrapperX<WarningSettingMessageRecord> messageRecordWrapper = new LambdaQueryWrapperX<>();
                            messageRecordWrapper.eq(WarningSettingMessageRecord :: getBusinessId,projectScheme.getId());
                            messageRecordWrapper.eq(WarningSettingMessageRecord :: getWarningDictId,w.getDictValueId());
                            List<WarningSettingMessageRecord> warningSettingMessageRecords = warningSettingMessageRecordRepository.selectList(messageRecordWrapper);
                            if(!CollectionUtils.isEmpty(warningSettingMessageRecords)){
                                continue;
                            }
                        }
                        sendPlans.add(projectScheme);
                    }
                }
            }catch (Exception e){
                e.printStackTrace();
                logger.error(e.getMessage());
                return false;
            }
        }
        else{
            sendPlans.addAll(plans);
        }
        return true;
    }

    private boolean handleSchemeOver(List<ProjectScheme> sendPlans,WarningSetting w,Date date, Integer dayNum,LambdaQueryWrapper<ProjectScheme> wrapper){
        String warningCategory = w.getWarningCategory();
        if(WarningSettingWarningCategoryEnum.DAY.getCode().equals(warningCategory)){
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            calendar.add(Calendar.DATE, -dayNum);

            Date startDate = calendar.getTime();

            if (Objects.equals(WarningFrequencyConstant.Once, w.getFrequency())) {
                calendar.add(Calendar.DATE, 1);
            }
            else{
                calendar.setTime(date);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
            }
            Date endDate  = calendar.getTime();
            wrapper.between(ProjectScheme::getEndTime, startDate, endDate);
        }
        else if(WarningSettingWarningCategoryEnum.PERCENTAGE.getCode().equals(warningCategory)){
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            wrapper.lt(ProjectScheme::getEndTime, calendar.getTime());
        }
        else{
            return false;
        }

        List<ProjectScheme> plans = projectSchemeService.list(wrapper);
        if(CollectionUtils.isEmpty(plans)){
            return false;
        }
        //提醒工期百分比
        if(WarningSettingWarningCategoryEnum.PERCENTAGE.getCode().equals(warningCategory)){
            List<ComputeIntervalDayDTO> computeIntervalDayDTOSTotal = new ArrayList<>();
            List<ComputeIntervalDayDTO> computeIntervalDayDTOSUsed = new ArrayList<>();
            for(ProjectScheme projectScheme : plans){
                ComputeIntervalDayDTO computeIntervalDayDTOTotal = new ComputeIntervalDayDTO();
                computeIntervalDayDTOTotal.setBusinessId(projectScheme.getId());
                computeIntervalDayDTOTotal.setStartDate(projectScheme.getBeginTime());
                computeIntervalDayDTOTotal.setEndDate(projectScheme.getEndTime());

                ComputeIntervalDayDTO computeIntervalDayDTOUsed = new ComputeIntervalDayDTO();
                computeIntervalDayDTOUsed.setBusinessId(projectScheme.getId());
                computeIntervalDayDTOUsed.setStartDate(projectScheme.getEndTime());
                computeIntervalDayDTOUsed.setEndDate(new Date());
                computeIntervalDayDTOSTotal.add(computeIntervalDayDTOTotal);
                computeIntervalDayDTOSUsed.add(computeIntervalDayDTOUsed);
            }
            try{
                ResponseDTO<List<ComputeIntervalDayVO>> responseDTOTotal = userFeignService.computeIntervalDayBatch(computeIntervalDayDTOSTotal);
                if(ResponseUtils.fail(responseDTOTotal)){
                    logger.error(JSONObject.toJSONString(responseDTOTotal));
                    return false;
                }

                ResponseDTO<List<ComputeIntervalDayVO>> responseDTOUsed = userFeignService.computeIntervalDayBatch(computeIntervalDayDTOSUsed);
                if(ResponseUtils.fail(responseDTOUsed)){
                    logger.error(JSONObject.toJSONString(responseDTOUsed));
                    return false;
                }

                List<ComputeIntervalDayVO> computeIntervalDayVOListTotal =  responseDTOTotal.getResult();

                Map<String,ComputeIntervalDayVO> computeIntervalDayVOMapTotal = computeIntervalDayVOListTotal.stream().collect(Collectors.toMap(ComputeIntervalDayVO :: getBusinessId, Function.identity()));

                List<ComputeIntervalDayVO> computeIntervalDayVOListUsed =  responseDTOUsed.getResult();

                Map<String,ComputeIntervalDayVO> computeIntervalDayVOMapUsed = computeIntervalDayVOListUsed.stream().collect(Collectors.toMap(ComputeIntervalDayVO :: getBusinessId, Function.identity()));

                for(ProjectScheme projectScheme : plans){
                    ComputeIntervalDayVO computeIntervalDayVOTotal = computeIntervalDayVOMapTotal.get(projectScheme.getId());
                    ComputeIntervalDayVO computeIntervalDayVOUsed = computeIntervalDayVOMapUsed.get(projectScheme.getId());
                    Long workdayNumTotal = computeIntervalDayVOTotal.getWorkdayNum();
                    Long workdayNumUsed = computeIntervalDayVOUsed.getWorkdayNum();
                    if(workdayNumTotal == null || workdayNumTotal < 1){
                        continue;
                    }
                    if(workdayNumUsed == null || workdayNumUsed < 1){
                        continue;
                    }
                    if(workdayNumUsed.floatValue() / workdayNumTotal >= dayNum) {
                        if(Objects.equals(WarningFrequencyConstant.Once, w.getFrequency())){
                            LambdaQueryWrapperX<WarningSettingMessageRecord> messageRecordWrapper = new LambdaQueryWrapperX<>();
                            messageRecordWrapper.eq(WarningSettingMessageRecord :: getBusinessId,projectScheme.getId());
                            messageRecordWrapper.eq(WarningSettingMessageRecord :: getWarningDictId,w.getDictValueId());
                            List<WarningSettingMessageRecord> warningSettingMessageRecords = warningSettingMessageRecordRepository.selectList(messageRecordWrapper);
                            if(!CollectionUtils.isEmpty(warningSettingMessageRecords)){
                                continue;
                            }
                        }
                        sendPlans.add(projectScheme);
                    }
                }
            }catch (Exception e){
                e.printStackTrace();
                logger.error(e.getMessage());
                return false;
            }
        }
        else{
            sendPlans.addAll(plans);
        }
        return true;
    }

    private void riskAhead(Date date, List<String> pIdList, Map<String, String> pIdToUserIdMap, List<String> projectIdList) throws Exception {
        LambdaQueryWrapper<WarningSetting> warningSettingWrapper = new LambdaQueryWrapper<>(WarningSetting.class);
        warningSettingWrapper
                .eq(WarningSetting::getDictValueId, Warning_Demand_Ahead)
                .eq(WarningSetting::getTakeEffect, TakeEffectEnum.EFFECT.getStatus())
                .in(WarningSetting::getPlatformId, pIdList.toArray());
        if (CollectionUtil.isNotEmpty(projectIdList)) {
            warningSettingWrapper.notIn(WarningSetting::getProjectId, projectIdList);
        }
        List<WarningSetting> warningSettingDTOList = warningSettingService.list(warningSettingWrapper);
        for (WarningSetting w : warningSettingDTOList) {
                String frequency = w.getFrequency();
                String warningWay = w.getWarningWay();
                Integer dayNum = w.getDayNum();
                if(dayNum == null || dayNum <= 0){
                    continue;
                }
                if (StringUtils.hasText(frequency) && StringUtils.hasText(warningWay) && Objects.nonNull(dayNum)) {
                    //提醒人
                    List<WarningToRole> warningToRoleList = warningToRoleService.list(new LambdaQueryWrapper<>(WarningToRole.class)
                            .eq(WarningToRole::getFromId, w.getId()));
                    if (!CollectionUtils.isEmpty(warningToRoleList)) {
                        String projectId = w.getProjectId();
                        LambdaQueryWrapper<RiskManagement> wrapper = new LambdaQueryWrapper<>(RiskManagement.class);
                        wrapper.eq(RiskManagement::getProjectId, projectId).
                                ne(RiskManagement::getStatus, RISK_FINISH_STATUS);
                        wrapper.isNotNull(RiskManagement :: getPredictEndTime);

                        List<RiskManagement> sendRisks = new ArrayList<>();


                        String warningCategory = w.getWarningCategory();
                        if(WarningSettingWarningCategoryEnum.DAY.getCode().equals(warningCategory)){
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(date);
                            calendar.set(Calendar.HOUR_OF_DAY, 0);
                            calendar.set(Calendar.MINUTE, 0);
                            calendar.set(Calendar.SECOND, 0);
                            calendar.set(Calendar.MILLISECOND, 0);
                            calendar.add(Calendar.DATE, dayNum);
                            Date endDate  = calendar.getTime();
                            if (Objects.equals(WarningFrequencyConstant.Once, w.getFrequency())) {
                                calendar.add(Calendar.DATE, -1);
                            }
                            else{
                                calendar.setTime(date);
                                calendar.set(Calendar.HOUR_OF_DAY, 0);
                                calendar.set(Calendar.MINUTE, 0);
                                calendar.set(Calendar.SECOND, 0);
                                calendar.set(Calendar.MILLISECOND, 0);
                            }
                            Date startDate = calendar.getTime();
                            wrapper.between(RiskManagement::getPredictEndTime, startDate, endDate);
                        }
                        else if(WarningSettingWarningCategoryEnum.PERCENTAGE.getCode().equals(warningCategory)){
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(date);
                            calendar.set(Calendar.HOUR_OF_DAY, 0);
                            calendar.set(Calendar.MINUTE, 0);
                            calendar.set(Calendar.SECOND, 0);
                            calendar.set(Calendar.MILLISECOND, 0);
                            wrapper.ge(RiskManagement::getPredictEndTime, calendar.getTime());
                            wrapper.lt(RiskManagement::getCreateTime, calendar.getTime());
                        }
                        else{
                            continue;
                        }

                        List<RiskManagement> riskManagementDTOList = riskManagementService.list(wrapper);
                        if(CollectionUtils.isEmpty(riskManagementDTOList)){
                            continue;
                        }

                        //提醒工期百分比
                        if(WarningSettingWarningCategoryEnum.PERCENTAGE.getCode().equals(warningCategory)){
                            List<ComputeIntervalDayDTO> computeIntervalDayDTOSTotal = new ArrayList<>();
                            List<ComputeIntervalDayDTO> computeIntervalDayDTOSUsed = new ArrayList<>();
                            for(RiskManagement riskManagement : riskManagementDTOList){
                                ComputeIntervalDayDTO computeIntervalDayDTOTotal = new ComputeIntervalDayDTO();
                                computeIntervalDayDTOTotal.setBusinessId(riskManagement.getId());
                                computeIntervalDayDTOTotal.setStartDate(riskManagement.getCreateTime());
                                computeIntervalDayDTOTotal.setEndDate(riskManagement.getPredictEndTime());

                                ComputeIntervalDayDTO computeIntervalDayDTOUsed = new ComputeIntervalDayDTO();
                                computeIntervalDayDTOUsed.setBusinessId(riskManagement.getId());
                                computeIntervalDayDTOUsed.setStartDate(riskManagement.getCreateTime());
                                computeIntervalDayDTOUsed.setEndDate(new Date());
                                computeIntervalDayDTOSTotal.add(computeIntervalDayDTOTotal);
                                computeIntervalDayDTOSUsed.add(computeIntervalDayDTOUsed);
                            }
                            try{
                                ResponseDTO<List<ComputeIntervalDayVO>> responseDTOTotal = userFeignService.computeIntervalDayBatch(computeIntervalDayDTOSTotal);
                                if(ResponseUtils.fail(responseDTOTotal)){
                                    logger.error(JSONObject.toJSONString(responseDTOTotal));
                                    continue;
                                }

                                ResponseDTO<List<ComputeIntervalDayVO>> responseDTOUsed = userFeignService.computeIntervalDayBatch(computeIntervalDayDTOSUsed);
                                if(ResponseUtils.fail(responseDTOUsed)){
                                    logger.error(JSONObject.toJSONString(responseDTOUsed));
                                    continue;
                                }

                                List<ComputeIntervalDayVO> computeIntervalDayVOListTotal =  responseDTOTotal.getResult();

                                Map<String,ComputeIntervalDayVO> computeIntervalDayVOMapTotal = computeIntervalDayVOListTotal.stream().collect(Collectors.toMap(ComputeIntervalDayVO :: getBusinessId, Function.identity()));

                                List<ComputeIntervalDayVO> computeIntervalDayVOListUsed =  responseDTOUsed.getResult();

                                Map<String,ComputeIntervalDayVO> computeIntervalDayVOMapUsed = computeIntervalDayVOListUsed.stream().collect(Collectors.toMap(ComputeIntervalDayVO :: getBusinessId, Function.identity()));

                                for(RiskManagement riskManagement : riskManagementDTOList){
                                    ComputeIntervalDayVO computeIntervalDayVOTotal = computeIntervalDayVOMapTotal.get(riskManagement.getId());
                                    ComputeIntervalDayVO computeIntervalDayVOUsed = computeIntervalDayVOMapUsed.get(riskManagement.getId());
                                    Long workdayNumTotal = computeIntervalDayVOTotal.getWorkdayNum();
                                    Long workdayNumUsed = computeIntervalDayVOUsed.getWorkdayNum();
                                    if(workdayNumTotal == null || workdayNumTotal < 1){
                                        continue;
                                    }
                                    if(workdayNumUsed == null || workdayNumUsed < 1){
                                        continue;
                                    }
                                    if(workdayNumUsed.floatValue() / workdayNumTotal >= dayNum) {
                                        if(Objects.equals(WarningFrequencyConstant.Once, w.getFrequency())){
                                            LambdaQueryWrapperX<WarningSettingMessageRecord> messageRecordWrapper = new LambdaQueryWrapperX<>();
                                            messageRecordWrapper.eq(WarningSettingMessageRecord :: getBusinessId,riskManagement.getId());
                                            messageRecordWrapper.eq(WarningSettingMessageRecord :: getWarningDictId,w.getDictValueId());
                                            List<WarningSettingMessageRecord> warningSettingMessageRecords = warningSettingMessageRecordRepository.selectList(messageRecordWrapper);
                                            if(!CollectionUtils.isEmpty(warningSettingMessageRecords)){
                                                continue;
                                            }
                                        }
                                        sendRisks.add(riskManagement);
                                    }
                                }
                            }catch (Exception e){
                                e.printStackTrace();
                                logger.error(e.getMessage());
                                continue;
                            }
                        }
                        else{
                            sendRisks.addAll(riskManagementDTOList);
                        }


                        if(CollectionUtils.isEmpty(sendRisks)){
                            continue;
                        }

                        List<RiskManagementDTO> riskManagementDTOS = BeanCopyUtils.convertListTo(riskManagementDTOList, RiskManagementDTO::new);

                        this.riskCommon(riskManagementDTOS, projectId, warningToRoleList, PMS_FX_LQ, pIdToUserIdMap.get(w.getPlatformId()), "/pms/demandDetails", w.getTime(), w.getRemark(), w.getWarningType(), w.getDictValueId());
                    }
            }
        }
    }

    private void riskOver(Date date, List<String> pIdList, Map<String, String> pIdToUserIdMap, List<String> projectIdList) throws Exception {
        LambdaQueryWrapper<WarningSetting> warningSettingWrapper = new LambdaQueryWrapper<>(WarningSetting.class);
        warningSettingWrapper
                .eq(WarningSetting::getDictValueId, Warning_Demand_Over)
                .eq(WarningSetting::getTakeEffect, TakeEffectEnum.EFFECT.getStatus())
                .in(WarningSetting::getPlatformId, pIdList.toArray());
        if (CollectionUtil.isNotEmpty(projectIdList)) {
            warningSettingWrapper.notIn(WarningSetting::getProjectId, projectIdList);
        }
        List<WarningSetting> warningSettingDTOList = warningSettingService.list(warningSettingWrapper);
        for (WarningSetting w : warningSettingDTOList) {
                String frequency = w.getFrequency();
                String warningWay = w.getWarningWay();
                Integer dayNum = w.getDayNum();
                if(dayNum == null || dayNum <= 0){
                    continue;
                }
                if (StringUtils.hasText(frequency) && StringUtils.hasText(warningWay) && Objects.nonNull(dayNum)) {
                    //提醒人
                    List<WarningToRole> warningToRoleList = warningToRoleService.list(new LambdaQueryWrapper<>(WarningToRole.class)
                            .eq(WarningToRole::getFromId, w.getId()));
                    if (!CollectionUtils.isEmpty(warningToRoleList)) {
                        String projectId = w.getProjectId();
                        LambdaQueryWrapper<RiskManagement> wrapper = new LambdaQueryWrapper<>(RiskManagement.class);
                        wrapper.eq(RiskManagement::getProjectId, projectId).
                                ne(RiskManagement::getStatus, RISK_FINISH_STATUS);
                        wrapper.isNotNull(RiskManagement::getPredictEndTime);
                        List<RiskManagement> sendRisks = new ArrayList<>();
                        String warningCategory = w.getWarningCategory();
                        if(WarningSettingWarningCategoryEnum.DAY.getCode().equals(warningCategory)){
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(date);
                            calendar.set(Calendar.HOUR_OF_DAY, 0);
                            calendar.set(Calendar.MINUTE, 0);
                            calendar.set(Calendar.SECOND, 0);
                            calendar.set(Calendar.MILLISECOND, 0);
                            calendar.add(Calendar.DATE, -dayNum);

                            Date startDate = calendar.getTime();

                            if (Objects.equals(WarningFrequencyConstant.Once, w.getFrequency())) {
                                calendar.add(Calendar.DATE, 1);
                            }
                            else{
                                calendar.setTime(date);
                                calendar.set(Calendar.HOUR_OF_DAY, 0);
                                calendar.set(Calendar.MINUTE, 0);
                                calendar.set(Calendar.SECOND, 0);
                                calendar.set(Calendar.MILLISECOND, 0);
                            }
                            Date endDate  = calendar.getTime();
                            wrapper.between(RiskManagement::getPredictEndTime, startDate, endDate);
                        }
                        else if(WarningSettingWarningCategoryEnum.PERCENTAGE.getCode().equals(warningCategory)){
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(date);
                            calendar.set(Calendar.HOUR_OF_DAY, 0);
                            calendar.set(Calendar.MINUTE, 0);
                            calendar.set(Calendar.SECOND, 0);
                            calendar.set(Calendar.MILLISECOND, 0);
                            wrapper.lt(RiskManagement::getPredictEndTime, calendar.getTime());
                        }
                        else{
                            continue;
                        }

                        List<RiskManagement> riskManagementDTOList = riskManagementService.list(wrapper);
                        if(CollectionUtils.isEmpty(riskManagementDTOList)){
                            continue;
                        }
                        //提醒工期百分比
                        if(WarningSettingWarningCategoryEnum.PERCENTAGE.getCode().equals(warningCategory)){
                            List<ComputeIntervalDayDTO> computeIntervalDayDTOSTotal = new ArrayList<>();
                            List<ComputeIntervalDayDTO> computeIntervalDayDTOSUsed = new ArrayList<>();
                            for(RiskManagement riskManagement : riskManagementDTOList){
                                ComputeIntervalDayDTO computeIntervalDayDTOTotal = new ComputeIntervalDayDTO();
                                computeIntervalDayDTOTotal.setBusinessId(riskManagement.getId());
                                computeIntervalDayDTOTotal.setStartDate(riskManagement.getCreateTime());
                                computeIntervalDayDTOTotal.setEndDate(riskManagement.getPredictEndTime());

                                ComputeIntervalDayDTO computeIntervalDayDTOUsed = new ComputeIntervalDayDTO();
                                computeIntervalDayDTOUsed.setBusinessId(riskManagement.getId());
                                computeIntervalDayDTOUsed.setStartDate(riskManagement.getPredictEndTime());
                                computeIntervalDayDTOUsed.setEndDate(new Date());
                                computeIntervalDayDTOSTotal.add(computeIntervalDayDTOTotal);
                                computeIntervalDayDTOSUsed.add(computeIntervalDayDTOUsed);
                            }
                            try{
                                ResponseDTO<List<ComputeIntervalDayVO>> responseDTOTotal = userFeignService.computeIntervalDayBatch(computeIntervalDayDTOSTotal);
                                if(ResponseUtils.fail(responseDTOTotal)){
                                    logger.error(JSONObject.toJSONString(responseDTOTotal));
                                    continue;
                                }

                                ResponseDTO<List<ComputeIntervalDayVO>> responseDTOUsed = userFeignService.computeIntervalDayBatch(computeIntervalDayDTOSUsed);
                                if(ResponseUtils.fail(responseDTOUsed)){
                                    logger.error(JSONObject.toJSONString(responseDTOUsed));
                                    continue;
                                }

                                List<ComputeIntervalDayVO> computeIntervalDayVOListTotal =  responseDTOTotal.getResult();

                                Map<String,ComputeIntervalDayVO> computeIntervalDayVOMapTotal = computeIntervalDayVOListTotal.stream().collect(Collectors.toMap(ComputeIntervalDayVO :: getBusinessId, Function.identity()));

                                List<ComputeIntervalDayVO> computeIntervalDayVOListUsed =  responseDTOUsed.getResult();

                                Map<String,ComputeIntervalDayVO> computeIntervalDayVOMapUsed = computeIntervalDayVOListUsed.stream().collect(Collectors.toMap(ComputeIntervalDayVO :: getBusinessId, Function.identity()));

                                for(RiskManagement riskManagement : riskManagementDTOList){
                                    ComputeIntervalDayVO computeIntervalDayVOTotal = computeIntervalDayVOMapTotal.get(riskManagement.getId());
                                    ComputeIntervalDayVO computeIntervalDayVOUsed = computeIntervalDayVOMapUsed.get(riskManagement.getId());
                                    Long workdayNumTotal = computeIntervalDayVOTotal.getWorkdayNum();
                                    Long workdayNumUsed = computeIntervalDayVOUsed.getWorkdayNum();
                                    if(workdayNumTotal == null || workdayNumTotal < 1){
                                        continue;
                                    }
                                    if(workdayNumUsed == null || workdayNumUsed < 1){
                                        continue;
                                    }
                                    if(workdayNumUsed.floatValue() / workdayNumTotal >= dayNum) {
                                        if(Objects.equals(WarningFrequencyConstant.Once, w.getFrequency())){
                                            LambdaQueryWrapperX<WarningSettingMessageRecord> messageRecordWrapper = new LambdaQueryWrapperX<>();
                                            messageRecordWrapper.eq(WarningSettingMessageRecord :: getBusinessId,riskManagement.getId());
                                            messageRecordWrapper.eq(WarningSettingMessageRecord :: getWarningDictId,w.getDictValueId());
                                            List<WarningSettingMessageRecord> warningSettingMessageRecords = warningSettingMessageRecordRepository.selectList(messageRecordWrapper);
                                            if(!CollectionUtils.isEmpty(warningSettingMessageRecords)){
                                                continue;
                                            }
                                        }
                                        sendRisks.add(riskManagement);
                                    }
                                }
                            }catch (Exception e){
                                e.printStackTrace();
                                logger.error(e.getMessage());
                                continue;
                            }
                        }
                        else{
                            sendRisks.addAll(riskManagementDTOList);
                        }

                        if(CollectionUtils.isEmpty(sendRisks)){
                            continue;
                        }



                        List<RiskManagementDTO> riskManagementDTOS = BeanCopyUtils.convertListTo(riskManagementDTOList, RiskManagementDTO::new);

                        this.riskCommon(riskManagementDTOS, projectId, warningToRoleList, PMS_FX_YQ, pIdToUserIdMap.get(w.getPlatformId()), "/pms/demandDetails", w.getTime(), w.getRemark(), w.getWarningType(), w.getDictValueId());
                    }
            }
        }
    }

    private void riskCommon(List<RiskManagementDTO> riskManagementDTOList, String projectId, List<WarningToRole> warningToRoleList, String node, String sendId, String url, String time, String msgContent,  String warningType, String dictValueId) throws Exception {
        if (!CollectionUtils.isEmpty(riskManagementDTOList)) {
            List<String> toIdList = warningToRoleList.stream().map(WarningToRole::getToId).collect(Collectors.toList());
            List<String> roleIdList = toIdList.stream()
                    .filter(r -> !Objects.equals(CREATOR, r) && !Objects.equals(PRINCIPAL, r)).collect(Collectors.toList());
            boolean isCreator = toIdList.contains(CREATOR);
            boolean isPrincipal = toIdList.contains(PRINCIPAL);
            List<String> userIdList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(roleIdList)) {
                List<ProjectRoleUser> projectRoleUserDTOList = projectRoleUserService.list(new LambdaQueryWrapper<>(ProjectRoleUser.class)
                        .eq(ProjectRoleUser::getProjectId, projectId)
                        .in(ProjectRoleUser::getProjectRoleId, roleIdList.toArray()));
                userIdList.addAll(projectRoleUserDTOList.stream().map(ProjectRoleUser::getUserId).collect(Collectors.toList()));
            }
            for (RiskManagementDTO riskManagementDTO : riskManagementDTOList) {
                List<String> recipientIdList = new ArrayList<>();
                if (isCreator) {
                    recipientIdList.add(riskManagementDTO.getCreatorId());
                }
                if (isPrincipal) {
                    recipientIdList.add(riskManagementDTO.getPrincipalId());
                }
                recipientIdList.addAll(userIdList);

                if(StringUtils.hasText(msgContent)){
                    packageNormalMessage(projectId, riskManagementDTO.getId(), riskManagementDTO.getName(), recipientIdList.stream().distinct().collect(Collectors.toList())
                            , sendId, String.format("%s?id=%s&projectId=%s", url, riskManagementDTO.getId(), riskManagementDTO.getProjectId())
                            , riskManagementDTO.getPlatformId(), riskManagementDTO.getOrgId(), time, msgContent, warningType, dictValueId, riskManagementDTO.getPlatformId());
                }
                else{
                    packageMessage(node, projectId, riskManagementDTO.getId(), riskManagementDTO.getName(), recipientIdList.stream().distinct().collect(Collectors.toList())
                            , sendId, String.format("%s?id=%s&projectId=%s", url, riskManagementDTO.getId(), riskManagementDTO.getProjectId())
                            , riskManagementDTO.getPlatformId(), riskManagementDTO.getOrgId(), time, warningType, dictValueId, riskManagementDTO.getPlatformId());
                }


            }
        }
    }

    private void questionAhead(Date date, List<String> pIdList, Map<String, String> pIdToUserIdMap, List<String> projectIdList) throws Exception {
        LambdaQueryWrapper<WarningSetting> warningSettingWrapper = new LambdaQueryWrapper<>(WarningSetting.class);
        warningSettingWrapper
                .eq(WarningSetting::getDictValueId, Warning_Question_Ahead)
                .eq(WarningSetting::getTakeEffect, TakeEffectEnum.EFFECT.getStatus())
                .in(WarningSetting::getPlatformId, pIdList.toArray());
        if (CollectionUtil.isNotEmpty(projectIdList)) {
            warningSettingWrapper.notIn(WarningSetting::getProjectId, projectIdList);
        }
        List<WarningSetting> warningSettingDTOList = warningSettingService.list(warningSettingWrapper);
        for (WarningSetting w : warningSettingDTOList) {
                String frequency = w.getFrequency();
                String warningWay = w.getWarningWay();
                Integer dayNum = w.getDayNum();
                if (StringUtils.hasText(frequency) && StringUtils.hasText(warningWay) && Objects.nonNull(dayNum)) {
                    //提醒人
                    List<WarningToRole> warningToRoleList = warningToRoleService.list(new LambdaQueryWrapper<>(WarningToRole.class)
                            .eq(WarningToRole::getFromId, w.getId()));
                    if (!CollectionUtils.isEmpty(warningToRoleList)) {
                        String projectId = w.getProjectId();
                        LambdaQueryWrapper<QuestionManagement> wrapper = new LambdaQueryWrapper<>(QuestionManagement.class);
                        wrapper.eq(QuestionManagement::getProjectId, projectId).
                                ne(QuestionManagement::getStatus, QUESTION_FINISH_STATUS);
                        wrapper.isNotNull(QuestionManagement::getPredictEndTime);
                        wrapper.isNotNull(QuestionManagement::getProposedTime);

                        List<QuestionManagement> sendQuestions = new ArrayList<>();
                        String warningCategory = w.getWarningCategory();
                        if(WarningSettingWarningCategoryEnum.DAY.getCode().equals(warningCategory)){
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(date);
                            calendar.set(Calendar.HOUR_OF_DAY, 0);
                            calendar.set(Calendar.MINUTE, 0);
                            calendar.set(Calendar.SECOND, 0);
                            calendar.set(Calendar.MILLISECOND, 0);
                            calendar.add(Calendar.DATE, dayNum);
                            Date endDate  = calendar.getTime();
                            if (Objects.equals(WarningFrequencyConstant.Once, w.getFrequency())) {
                                calendar.add(Calendar.DATE, -1);
                            }
                            else{
                                calendar.setTime(date);
                                calendar.set(Calendar.HOUR_OF_DAY, 0);
                                calendar.set(Calendar.MINUTE, 0);
                                calendar.set(Calendar.SECOND, 0);
                                calendar.set(Calendar.MILLISECOND, 0);
                            }
                            Date startDate = calendar.getTime();
                            wrapper.between(QuestionManagement::getPredictEndTime, startDate, endDate);
                        }
                        else if(WarningSettingWarningCategoryEnum.PERCENTAGE.getCode().equals(warningCategory)){
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(date);
                            calendar.set(Calendar.HOUR_OF_DAY, 0);
                            calendar.set(Calendar.MINUTE, 0);
                            calendar.set(Calendar.SECOND, 0);
                            calendar.set(Calendar.MILLISECOND, 0);
                            wrapper.ge(QuestionManagement::getPredictEndTime, calendar.getTime());
                            wrapper.lt(QuestionManagement::getProposedTime, calendar.getTime());
                        }
                        else{
                            continue;
                        }

                        List<QuestionManagement> questionManagementDTOList = questionManagementService.list(wrapper);
                        if(CollectionUtils.isEmpty(questionManagementDTOList)){
                            continue;
                        }

                        //提醒工期百分比
                        if(WarningSettingWarningCategoryEnum.PERCENTAGE.getCode().equals(warningCategory)){
                            List<ComputeIntervalDayDTO> computeIntervalDayDTOSTotal = new ArrayList<>();
                            List<ComputeIntervalDayDTO> computeIntervalDayDTOSUsed = new ArrayList<>();
                            for(QuestionManagement questionManagement : questionManagementDTOList){
                                ComputeIntervalDayDTO computeIntervalDayDTOTotal = new ComputeIntervalDayDTO();
                                computeIntervalDayDTOTotal.setBusinessId(questionManagement.getId());
                                computeIntervalDayDTOTotal.setStartDate(questionManagement.getProposedTime());
                                computeIntervalDayDTOTotal.setEndDate(questionManagement.getPredictEndTime());

                                ComputeIntervalDayDTO computeIntervalDayDTOUsed = new ComputeIntervalDayDTO();
                                computeIntervalDayDTOUsed.setBusinessId(questionManagement.getId());
                                computeIntervalDayDTOUsed.setStartDate(questionManagement.getProposedTime());
                                computeIntervalDayDTOUsed.setEndDate(new Date());
                                computeIntervalDayDTOSTotal.add(computeIntervalDayDTOTotal);
                                computeIntervalDayDTOSUsed.add(computeIntervalDayDTOUsed);
                            }
                            try{
                                ResponseDTO<List<ComputeIntervalDayVO>> responseDTOTotal = userFeignService.computeIntervalDayBatch(computeIntervalDayDTOSTotal);
                                if(ResponseUtils.fail(responseDTOTotal)){
                                    logger.error(JSONObject.toJSONString(responseDTOTotal));
                                    continue;
                                }

                                ResponseDTO<List<ComputeIntervalDayVO>> responseDTOUsed = userFeignService.computeIntervalDayBatch(computeIntervalDayDTOSUsed);
                                if(ResponseUtils.fail(responseDTOUsed)){
                                    logger.error(JSONObject.toJSONString(responseDTOUsed));
                                    continue;
                                }

                                List<ComputeIntervalDayVO> computeIntervalDayVOListTotal =  responseDTOTotal.getResult();

                                Map<String,ComputeIntervalDayVO> computeIntervalDayVOMapTotal = computeIntervalDayVOListTotal.stream().collect(Collectors.toMap(ComputeIntervalDayVO :: getBusinessId, Function.identity()));

                                List<ComputeIntervalDayVO> computeIntervalDayVOListUsed =  responseDTOUsed.getResult();

                                Map<String,ComputeIntervalDayVO> computeIntervalDayVOMapUsed = computeIntervalDayVOListUsed.stream().collect(Collectors.toMap(ComputeIntervalDayVO :: getBusinessId, Function.identity()));

                                for(QuestionManagement questionManagement : questionManagementDTOList){
                                    ComputeIntervalDayVO computeIntervalDayVOTotal = computeIntervalDayVOMapTotal.get(questionManagement.getId());
                                    ComputeIntervalDayVO computeIntervalDayVOUsed = computeIntervalDayVOMapUsed.get(questionManagement.getId());
                                    Long workdayNumTotal = computeIntervalDayVOTotal.getWorkdayNum();
                                    Long workdayNumUsed = computeIntervalDayVOUsed.getWorkdayNum();
                                    if(workdayNumTotal == null || workdayNumTotal < 1){
                                        continue;
                                    }
                                    if(workdayNumUsed == null || workdayNumUsed < 1){
                                        continue;
                                    }
                                    if(workdayNumUsed.floatValue() / workdayNumTotal >= dayNum) {
                                        if(Objects.equals(WarningFrequencyConstant.Once, w.getFrequency())){
                                            LambdaQueryWrapperX<WarningSettingMessageRecord> messageRecordWrapper = new LambdaQueryWrapperX<>();
                                            messageRecordWrapper.eq(WarningSettingMessageRecord :: getBusinessId,questionManagement.getId());
                                            messageRecordWrapper.eq(WarningSettingMessageRecord :: getWarningDictId,w.getDictValueId());
                                            List<WarningSettingMessageRecord> warningSettingMessageRecords = warningSettingMessageRecordRepository.selectList(messageRecordWrapper);
                                            if(!CollectionUtils.isEmpty(warningSettingMessageRecords)){
                                                continue;
                                            }
                                        }
                                        sendQuestions.add(questionManagement);
                                    }
                                }
                            }catch (Exception e){
                                e.printStackTrace();
                                logger.error(e.getMessage());
                                continue;
                            }
                        }
                        else{
                            sendQuestions.addAll(questionManagementDTOList);
                        }
                        if(CollectionUtils.isEmpty(sendQuestions)){
                            continue;
                        }


                        List<QuestionManagementDTO> questionManagementDTOs = BeanCopyUtils.convertListTo(questionManagementDTOList,QuestionManagementDTO::new);
                        this.questionCommon(questionManagementDTOs, projectId, warningToRoleList, PMS_WT_LQ, pIdToUserIdMap.get(w.getPlatformId()), "/pms/questionDetails", w.getTime(), w.getRemark(), w.getWarningType(), w.getDictValueId());
                    }
            }
        }
    }

    private void questionOver(Date date, List<String> pIdList, Map<String, String> pIdToUserIdMap, List<String> projectIdList) throws Exception {
        LambdaQueryWrapper<WarningSetting> warningSettingWrapper = new LambdaQueryWrapper<>(WarningSetting.class);
        warningSettingWrapper
                .eq(WarningSetting::getDictValueId, Warning_Question_Over)
                .eq(WarningSetting::getTakeEffect, TakeEffectEnum.EFFECT.getStatus())
                .in(WarningSetting::getPlatformId, pIdList.toArray());
        if (CollectionUtil.isNotEmpty(projectIdList)) {
            warningSettingWrapper.notIn(WarningSetting::getProjectId, projectIdList);
        }
        List<WarningSetting> warningSettingDTOList = warningSettingService.list(warningSettingWrapper);
        for (WarningSetting w : warningSettingDTOList) {
                String frequency = w.getFrequency();
                String warningWay = w.getWarningWay();
                Integer dayNum = w.getDayNum();
                if (StringUtils.hasText(frequency) && StringUtils.hasText(warningWay) && Objects.nonNull(dayNum)) {
                    //提醒人
                    List<WarningToRole> warningToRoleList = warningToRoleService.list(new LambdaQueryWrapper<>(WarningToRole.class)
                            .eq(WarningToRole::getFromId, w.getId()));
                    if (!CollectionUtils.isEmpty(warningToRoleList)) {
                        String projectId = w.getProjectId();
                        LambdaQueryWrapper<QuestionManagement> wrapper = new LambdaQueryWrapper<>(QuestionManagement.class);
                        wrapper.eq(QuestionManagement::getProjectId, projectId).
                                ne(QuestionManagement::getStatus, QUESTION_FINISH_STATUS);


//                        Calendar calendar = Calendar.getInstance();
//                        calendar.setTime(date);
//                        calendar.set(Calendar.HOUR_OF_DAY, 0);
//                        calendar.set(Calendar.MINUTE, 0);
//                        calendar.set(Calendar.SECOND, 0);
//                        calendar.set(Calendar.MILLISECOND, 0);
//                        calendar.add(Calendar.DATE, -dayNum);
//
//
//                        Date startDate = calendar.getTime();
//
//                        if (Objects.equals(WarningFrequencyConstant.Once, w.getFrequency())) {
//                            calendar.add(Calendar.DATE, 1);
//                        }
//                        else{
//                            calendar.setTime(date);
//                            calendar.set(Calendar.HOUR_OF_DAY, 0);
//                            calendar.set(Calendar.MINUTE, 0);
//                            calendar.set(Calendar.SECOND, 0);
//                            calendar.set(Calendar.MILLISECOND, 0);
//                        }
//                        Date endDate  = calendar.getTime();
//                        wrapper.between(QuestionManagement::getPredictEndTime, startDate, endDate);
//
//                        List<QuestionManagement> questionManagementDTOList = questionManagementService.list(wrapper);
//                        if(CollectionUtils.isEmpty(questionManagementDTOList)){
//                            continue;
//                        }

                        List<QuestionManagement> sendQuestions = new ArrayList<>();
                        String warningCategory = w.getWarningCategory();
                        if(WarningSettingWarningCategoryEnum.DAY.getCode().equals(warningCategory)){
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(date);
                            calendar.set(Calendar.HOUR_OF_DAY, 0);
                            calendar.set(Calendar.MINUTE, 0);
                            calendar.set(Calendar.SECOND, 0);
                            calendar.set(Calendar.MILLISECOND, 0);
                            calendar.add(Calendar.DATE, -dayNum);

                            Date startDate = calendar.getTime();

                            if (Objects.equals(WarningFrequencyConstant.Once, w.getFrequency())) {
                                calendar.add(Calendar.DATE, 1);
                            }
                            else{
                                calendar.setTime(date);
                                calendar.set(Calendar.HOUR_OF_DAY, 0);
                                calendar.set(Calendar.MINUTE, 0);
                                calendar.set(Calendar.SECOND, 0);
                                calendar.set(Calendar.MILLISECOND, 0);
                            }
                            Date endDate  = calendar.getTime();
                            wrapper.between(QuestionManagement::getPredictEndTime, startDate, endDate);
                        }
                        else if(WarningSettingWarningCategoryEnum.PERCENTAGE.getCode().equals(warningCategory)){
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(date);
                            calendar.set(Calendar.HOUR_OF_DAY, 0);
                            calendar.set(Calendar.MINUTE, 0);
                            calendar.set(Calendar.SECOND, 0);
                            calendar.set(Calendar.MILLISECOND, 0);
                            wrapper.lt(QuestionManagement::getPredictEndTime, calendar.getTime());
                        }
                        else{
                            continue;
                        }

                        List<QuestionManagement> questionManagementDTOList = questionManagementService.list(wrapper);
                        if(CollectionUtils.isEmpty(questionManagementDTOList)){
                            continue;
                        }
                        //提醒工期百分比
                        if(WarningSettingWarningCategoryEnum.PERCENTAGE.getCode().equals(warningCategory)){
                            List<ComputeIntervalDayDTO> computeIntervalDayDTOSTotal = new ArrayList<>();
                            List<ComputeIntervalDayDTO> computeIntervalDayDTOSUsed = new ArrayList<>();
                            for(QuestionManagement questionManagement : questionManagementDTOList){
                                ComputeIntervalDayDTO computeIntervalDayDTOTotal = new ComputeIntervalDayDTO();
                                computeIntervalDayDTOTotal.setBusinessId(questionManagement.getId());
                                computeIntervalDayDTOTotal.setStartDate(questionManagement.getProposedTime());
                                computeIntervalDayDTOTotal.setEndDate(questionManagement.getPredictEndTime());

                                ComputeIntervalDayDTO computeIntervalDayDTOUsed = new ComputeIntervalDayDTO();
                                computeIntervalDayDTOUsed.setBusinessId(questionManagement.getId());
                                computeIntervalDayDTOUsed.setStartDate(questionManagement.getPredictEndTime());
                                computeIntervalDayDTOUsed.setEndDate(new Date());
                                computeIntervalDayDTOSTotal.add(computeIntervalDayDTOTotal);
                                computeIntervalDayDTOSUsed.add(computeIntervalDayDTOUsed);
                            }
                            try{
                                ResponseDTO<List<ComputeIntervalDayVO>> responseDTOTotal = userFeignService.computeIntervalDayBatch(computeIntervalDayDTOSTotal);
                                if(ResponseUtils.fail(responseDTOTotal)){
                                    logger.error(JSONObject.toJSONString(responseDTOTotal));
                                    continue;
                                }

                                ResponseDTO<List<ComputeIntervalDayVO>> responseDTOUsed = userFeignService.computeIntervalDayBatch(computeIntervalDayDTOSUsed);
                                if(ResponseUtils.fail(responseDTOUsed)){
                                    logger.error(JSONObject.toJSONString(responseDTOUsed));
                                    continue;
                                }

                                List<ComputeIntervalDayVO> computeIntervalDayVOListTotal =  responseDTOTotal.getResult();

                                Map<String,ComputeIntervalDayVO> computeIntervalDayVOMapTotal = computeIntervalDayVOListTotal.stream().collect(Collectors.toMap(ComputeIntervalDayVO :: getBusinessId, Function.identity()));

                                List<ComputeIntervalDayVO> computeIntervalDayVOListUsed =  responseDTOUsed.getResult();

                                Map<String,ComputeIntervalDayVO> computeIntervalDayVOMapUsed = computeIntervalDayVOListUsed.stream().collect(Collectors.toMap(ComputeIntervalDayVO :: getBusinessId, Function.identity()));

                                for(QuestionManagement questionManagement : questionManagementDTOList){
                                    ComputeIntervalDayVO computeIntervalDayVOTotal = computeIntervalDayVOMapTotal.get(questionManagement.getId());
                                    ComputeIntervalDayVO computeIntervalDayVOUsed = computeIntervalDayVOMapUsed.get(questionManagement.getId());
                                    Long workdayNumTotal = computeIntervalDayVOTotal.getWorkdayNum();
                                    Long workdayNumUsed = computeIntervalDayVOUsed.getWorkdayNum();
                                    if(workdayNumTotal == null || workdayNumTotal < 1){
                                        continue;
                                    }
                                    if(workdayNumUsed == null || workdayNumUsed < 1){
                                        continue;
                                    }
                                    if(workdayNumUsed.floatValue() / workdayNumTotal >= dayNum) {
                                        if(Objects.equals(WarningFrequencyConstant.Once, w.getFrequency())){
                                            LambdaQueryWrapperX<WarningSettingMessageRecord> messageRecordWrapper = new LambdaQueryWrapperX<>();
                                            messageRecordWrapper.eq(WarningSettingMessageRecord :: getBusinessId,questionManagement.getId());
                                            messageRecordWrapper.eq(WarningSettingMessageRecord :: getWarningDictId,w.getDictValueId());
                                            List<WarningSettingMessageRecord> warningSettingMessageRecords = warningSettingMessageRecordRepository.selectList(messageRecordWrapper);
                                            if(!CollectionUtils.isEmpty(warningSettingMessageRecords)){
                                                continue;
                                            }
                                        }
                                        sendQuestions.add(questionManagement);
                                    }
                                }
                            }catch (Exception e){
                                e.printStackTrace();
                                logger.error(e.getMessage());
                                continue;
                            }
                        }
                        else{
                            sendQuestions.addAll(questionManagementDTOList);
                        }
                        if(CollectionUtils.isEmpty(sendQuestions)){
                            continue;
                        }

                        List<QuestionManagementDTO> questionManagements = BeanCopyUtils.convertListTo(questionManagementDTOList,QuestionManagementDTO::new);
                        this.questionCommon(questionManagements, projectId, warningToRoleList, PMS_WT_YQ, pIdToUserIdMap.get(w.getPlatformId()), "/pms/questionDetails", w.getTime(), w.getRemark(), w.getWarningType(), w.getDictValueId());
                    }
            }
        }
    }

    private void questionCommon(List<QuestionManagementDTO> questionManagementDTOList, String projectId, List<WarningToRole> warningToRoleList, String node, String sendId, String url, String time, String msgContent, String warningType, String dictValueId) throws Exception {
        if (!CollectionUtils.isEmpty(questionManagementDTOList)) {
            List<String> toIdList = warningToRoleList.stream().map(WarningToRole::getToId).collect(Collectors.toList());
            List<String> roleIdList = toIdList.stream()
                    .filter(r -> !Objects.equals(CREATOR, r) && !Objects.equals(PRINCIPAL, r)).collect(Collectors.toList());
            boolean isCreator = toIdList.contains(CREATOR);
            boolean isPrincipal = toIdList.contains(PRINCIPAL);
            List<String> userIdList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(roleIdList)) {
                List<ProjectRoleUser> projectRoleUserDTOList = projectRoleUserService.list(new LambdaQueryWrapper<>(ProjectRoleUser.class)
                        .eq(ProjectRoleUser::getProjectId, projectId)
                        .in(ProjectRoleUser::getProjectRoleId, roleIdList.toArray()));
                userIdList.addAll(projectRoleUserDTOList.stream().map(ProjectRoleUser::getUserId).collect(Collectors.toList()));
            }
            for (QuestionManagementDTO questionManagementDTO : questionManagementDTOList) {
                List<String> recipientIdList = new ArrayList<>();
                if (isCreator) {
                    recipientIdList.add(questionManagementDTO.getCreatorId());
                }
                if (isPrincipal) {
                    recipientIdList.add(questionManagementDTO.getPrincipalId());
                }
                recipientIdList.addAll(userIdList);
                if(StringUtils.hasText(msgContent)){
                    packageNormalMessage(projectId, questionManagementDTO.getId(), questionManagementDTO.getName(), recipientIdList.stream().distinct().collect(Collectors.toList())
                            , sendId, String.format("%s?id=%s&projectId=%s", url, questionManagementDTO.getId(), questionManagementDTO.getProjectId())
                            , questionManagementDTO.getPlatformId(), questionManagementDTO.getOrgId(), time, msgContent, warningType, dictValueId, questionManagementDTO.getPlatformId());
                }
                else{
                    packageMessage(node, projectId, questionManagementDTO.getId(), questionManagementDTO.getName(), recipientIdList.stream().distinct().collect(Collectors.toList())
                            , sendId, String.format("%s?id=%s&projectId=%s", url, questionManagementDTO.getId(), questionManagementDTO.getProjectId())
                            , questionManagementDTO.getPlatformId(), questionManagementDTO.getOrgId(), time, warningType, dictValueId, questionManagementDTO.getPlatformId());
                }


            }
        }

    }

    private void packageMessage(String node, String projectId, String bussinessId, String name, List<String> recipientIdList, String sendId,
                                String url, String pId, String orgId, String time, String  warningType, String dictValueId, String platformId) {
        SendMessageDTO mscMessageDTO = new SendMessageDTO();
        mscMessageDTO.setBusinessNodeCode(node);
        Map<String, Object> msTitle = new HashMap<>(1);
        msTitle.put("$name$", name);
        mscMessageDTO.setTitleMap(msTitle);
        mscMessageDTO.setMessageUrl(url);
        mscMessageDTO.setRecipientIdList(recipientIdList);
        mscMessageDTO.setSenderId(sendId);
        mscMessageDTO.setSenderTime(new Date());
        mscMessageDTO.setPlatformId(pId);
        mscMessageDTO.setOrgId(orgId);
        mscMessageDTO.setBusinessId(bussinessId);
       // threadPoolTaskExecutor.execute(() -> {
            try {
                String delayTime = time;
                if(StringUtils.hasText(delayTime)){
                    delayTime = handleMillisecond(delayTime);
                }
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(new Date());
                if(StringUtils.hasText(delayTime) && isInteger(delayTime)){
                    calendar.add(Calendar.MILLISECOND,Integer.parseInt(delayTime));
                    pmsMQProducer.sendPmsDelayedMessage(mscMessageDTO,delayTime);
                }
                else{
                    pmsMQProducer.sendPmsMessage(mscMessageDTO);
                }
                insertWarningSettingMessageRecord(projectId, bussinessId, name, warningType, dictValueId, sendId, new Date(),calendar.getTime(), node, recipientIdList, platformId,orgId);
            } catch (Exception e) {
                e.printStackTrace();
            }
       // });
    }

    private void packageNormalMessage(String projectId, String bussinessId, String name, List<String> recipientIdList, String sendId,
                                String url, String pId, String orgId, String time, String msgContent, String  warningType, String dictValueId, String platformId) {
        OrionNormalMessageDTO mscMessageDTO = new OrionNormalMessageDTO();
        mscMessageDTO.setTitle(msgContent);
        mscMessageDTO.setContent(msgContent);
        mscMessageDTO.setMessageUrl(url);
        mscMessageDTO.setRecipientIdList(recipientIdList);
        mscMessageDTO.setSenderId(sendId);
        mscMessageDTO.setSenderTime(new Date());
        mscMessageDTO.setPlatformId(pId);
        mscMessageDTO.setOrgId(orgId);
        mscMessageDTO.setBusinessId(bussinessId);
        // threadPoolTaskExecutor.execute(() -> {
        try {
            String delayTime = time;
            if(StringUtils.hasText(delayTime)){
                delayTime = handleMillisecond(delayTime);
            }
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            if(StringUtils.hasText(delayTime) && isInteger(delayTime)){
                calendar.add(Calendar.MILLISECOND,Integer.parseInt(delayTime));
                pmsMQProducer.sendPmsDelayedMessage(mscMessageDTO,delayTime);
            }
            else{
                pmsMQProducer.sendPmsMessage(mscMessageDTO);
            }
            insertWarningSettingMessageRecord(projectId, bussinessId, name, warningType, dictValueId, sendId, new Date(),calendar.getTime(), msgContent, recipientIdList,platformId,orgId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        // });
    }

    private String handleMillisecond(String delayTime){
        if(!StringUtils.hasText(delayTime)){
            return delayTime;
        }
        if(StringUtils.hasText(delayTime)){
            if(StringUtils.hasText(delayTime) && delayTime.contains(":")){
                SimpleDateFormat sf = null;
                if(delayTime.length() < 6){
                    sf = new SimpleDateFormat("hh:mm");
                }
                else if(delayTime.length() < 9){
                    sf = new SimpleDateFormat("hh:mm:ss");
                }
                try{
                    if(sf != null){
                        Date sendTime = sf.parse(delayTime);
                        Calendar c1 = Calendar.getInstance();
                        c1.setTime(sendTime);
                        Calendar c2 = Calendar.getInstance();
                        c2.set(Calendar.HOUR_OF_DAY, c1.get(Calendar.HOUR_OF_DAY));
                        c2.set(Calendar.MINUTE,c1.get(Calendar.MINUTE));
                        long millisecond =  DateUtil.betweenMs(new Date(),c2.getTime());
                        delayTime = String.valueOf(millisecond);

                    }
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        }
        return delayTime;
    }
    private void insertWarningSettingMessageRecord(String projectId, String businessId, String businessName,
                                                   String warningType, String warningDictId, String senderId,
                                                   Date senderTime, Date actualSenderTime, String messageContent, List<String> recipientIdList, String platformId, String orgId){
        WarningSettingMessageRecord warningSettingMessageRecord = new WarningSettingMessageRecord();
        warningSettingMessageRecord.setSenderId(senderId);
        warningSettingMessageRecord.setActualSenderTime(actualSenderTime);
        warningSettingMessageRecord.setBusinessId(businessId);
        warningSettingMessageRecord.setBusinessName(businessName);
        warningSettingMessageRecord.setMessageContent(messageContent);
        warningSettingMessageRecord.setProjectId(projectId);
        warningSettingMessageRecord.setSenderTime(senderTime);
        warningSettingMessageRecord.setWarningDictId(warningDictId);
        warningSettingMessageRecord.setWarningType(warningType);
        warningSettingMessageRecord.setPlatformId(platformId);
        warningSettingMessageRecord.setOrgId(orgId);
        warningSettingMessageRecordRepository.insert(warningSettingMessageRecord);
        for(String recipientId : recipientIdList){
            WarningSettingMessageRecipient warningSettingMessageRecipient =  new WarningSettingMessageRecipient();
            warningSettingMessageRecipient.setMessageId(warningSettingMessageRecord.getBusinessId());
            warningSettingMessageRecipient.setRecipientId(recipientId);
            warningSettingMessageRecipient.setPlatformId(platformId);
            warningSettingMessageRecipientRepository.insert(warningSettingMessageRecipient);
        }
    }

    private boolean isInteger(String str) {
        // 使用正则表达式匹配整数
        return str.matches("^-?\\d+$");
    }
}
