package com.chinasie.orion.domain.request;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/9/9
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "基础支持人员库导出请求参数")
public class BasicUserExportRequest extends ObjectEntity {

    @ApiModelProperty(value = "ids", required = false)
    private List<String> ids;

    @ApiModelProperty(value = "state [离岗、到岗]", required = false)
    private String state;
}
