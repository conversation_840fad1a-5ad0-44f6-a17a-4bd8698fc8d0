<template>
  <BasicForm @register="registerForm" />
  <div
    v-if="state.modelId&&state.showDetails"
    v-loading="state.detailsLoading"
  >
    <span class="fontStyle pl30 pb50">{{ state.modelDetails?.name ?? '' }}</span>
    <div class="pl40 pr30 pt25">
      <Description
        :bordered="false"
        :colums="3"
        @register="appraisalDesRegister"
      />
    </div>

    <BasicCard title="模板样式">
      <ShowTemplate
        modal-type="check"
        :data="state.modelDetails"
      />
    </BasicCard>
  </div>
  <Empty v-else />
</template>

<script setup lang="ts">
import {
  defineExpose, watch, reactive, computed, h, defineProps,
} from 'vue';
import {
  BasicForm, FormSchema, useForm, Empty, BasicCard, Description, useDescription,
} from 'lyra-component-vue3';
import Api from '/@/api';
import dayjs from 'dayjs';
import ShowTemplate
  from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/icmManagement/icmManagementDetails/components/showTemplate/ArgumentTemplate.vue';

const props = defineProps({
  paramsId: {
    type: String,
    default: '',
  },
});
const state = reactive({
  paramsId: JSON.parse(JSON.stringify(props.paramsId)),
  modelId: '',
  modelDetails: {},
  detailsLoading: false,
  showDetails: true,
});

const schemas: FormSchema[] = [
  {
    field: 'modelId',
    component: 'ApiSelect',
    label: '选择参数模板',
    colProps: {
      span: 12,
    },
    rules: [{ required: true }],
    componentProps: {
    // api: () => new Api('/pdm/parameterPoolModule/search').fetch('', '', 'POST'),
      api: () => new Api(`/pdm/parameterPoolModule/lists/${state.paramsId}`).fetch('', '', 'GET'),
      labelField: 'name',
      valueField: 'id',
      onChange: (v) => {
        state.modelId = v;
      },
    },
  },
];
const [registerForm, FormMethods] = useForm({
  // layout: 'vertical',
  schemas,
  baseColProps: {
    span: 12,
  },
  actionColOptions: {
    span: 24,
  },
});
watch(() => state.modelId, () => {
  if (state.modelId) {
    state.detailsLoading = true;
    new Api(`/pdm/parameterPoolModule/${state.modelId}`).fetch('', '', 'GET').then((res) => {
      if (res) {
        state.modelDetails = res;
      } else {
        state.modelDetails = {};
      }
    }).finally(() => {
      state.detailsLoading = false;
    });
  } else {
    state.modelDetails = {};
  }
});

function getModelDetails(id) {
  state.modelId = id;
  state.showDetails = false;
  state.detailsLoading = true;
  new Api(`/pdm/parameterPoolModule/${id}`).fetch('', '', 'GET').then((res) => {
    if (res) {
      state.modelDetails = res;
    } else {
      state.modelDetails = {};
    }
  }).finally(() => {
    state.detailsLoading = false;
    state.showDetails = true;
  });
}

const appraisalSchema = [
  {
    field: 'type',
    label: '模板类型',
    span: 1,
    render: (curVal) => (curVal === 1 ? '文本' : curVal === 2 ? '数值' : curVal === 3 ? '表格' : curVal === 4 ? '图片' : curVal === 5 ? '公式' : curVal === 6 ? '链接' : curVal === 7 ? '附件' : ''),
  },
  {
    field: 'sourceName',
    label: '模板来源',
    span: 1,
  },
  {
    field: 'dataName',
    label: '关联内容',
    span: 2,
    render: (curVal, data) => h('span', {
      // class: 'action-btn',
      onClick: () => {
      },
    }, curVal),
  },
  {
    field: 'remark',
    label: '模板描述',
    span: 7,
  },

];
const [appraisalDesRegister] = useDescription(
  {
    schema: appraisalSchema,
    data: computed(() => state.modelDetails),
  },
);
defineExpose({
  FormMethods,
  getModelDetails,
});
</script>

<style scoped lang="less">
.fontStyle {
  font-size: 20px;
}
</style>
