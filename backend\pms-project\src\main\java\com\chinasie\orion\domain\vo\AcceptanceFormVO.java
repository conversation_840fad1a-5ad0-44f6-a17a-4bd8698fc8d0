package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class AcceptanceFormVO extends ObjectVO {

    // 验收单类型
    private String type;

    // 验收单编号
    private String number;

    // 验收完成日期
    private Date completeTime;

    // 验收完成执行人Id
    private String completeUserId;

    // 验收完成执行人名称
    private String completeUserName;

    // 项目Id
    private String projectId;

    // 项目名
    private String projectName;

    // 项目编号
    private String projectNumber;

    // 项目开始日期
    private Date projectBeginTime;

    // 项目结束日期
    private Date projectEndTime;

    // 项目责任处室Id
    private String projectRspSubDeptId;

    // 项目责任处室名
    private String projectRspSubDeptName;

    // 项目责任人Id
    private String projectRspUserId;

    // 项目责任人名称
    private String projectRspUserName;

    // 验收人工号
    private String creatorCode;

    // 前端enables点
    private Map<String, Boolean> enables = new HashMap<>();

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public Date getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }

    public String getCompleteUserId() {
        return completeUserId;
    }

    public void setCompleteUserId(String completeUserId) {
        this.completeUserId = completeUserId;
    }

    public String getCompleteUserName() {
        return completeUserName;
    }

    public void setCompleteUserName(String completeUserName) {
        this.completeUserName = completeUserName;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectNumber() {
        return projectNumber;
    }

    public void setProjectNumber(String projectNumber) {
        this.projectNumber = projectNumber;
    }

    public Date getProjectBeginTime() {
        return projectBeginTime;
    }

    public void setProjectBeginTime(Date projectBeginTime) {
        this.projectBeginTime = projectBeginTime;
    }

    public Date getProjectEndTime() {
        return projectEndTime;
    }

    public void setProjectEndTime(Date projectEndTime) {
        this.projectEndTime = projectEndTime;
    }

    public String getProjectRspSubDeptId() {
        return projectRspSubDeptId;
    }

    public void setProjectRspSubDeptId(String projectRspSubDeptId) {
        this.projectRspSubDeptId = projectRspSubDeptId;
    }

    public String getProjectRspSubDeptName() {
        return projectRspSubDeptName;
    }

    public void setProjectRspSubDeptName(String projectRspSubDeptName) {
        this.projectRspSubDeptName = projectRspSubDeptName;
    }

    public String getProjectRspUserId() {
        return projectRspUserId;
    }

    public void setProjectRspUserId(String projectRspUserId) {
        this.projectRspUserId = projectRspUserId;
    }

    public String getProjectRspUserName() {
        return projectRspUserName;
    }

    public void setProjectRspUserName(String projectRspUserName) {
        this.projectRspUserName = projectRspUserName;
    }

    public String getCreatorCode() {
        return creatorCode;
    }

    public void setCreatorCode(String creatorCode) {
        this.creatorCode = creatorCode;
    }

    public Map<String, Boolean> getEnables() {
        return enables;
    }

    public void setEnables(Map<String, Boolean> enables) {
        this.enables = enables;
    }
}
