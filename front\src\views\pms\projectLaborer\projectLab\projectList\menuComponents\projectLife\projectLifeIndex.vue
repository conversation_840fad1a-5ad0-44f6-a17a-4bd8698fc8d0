<template>
  <div class="life-container">
    <div class="life-radio">
      <BasicButton
        icon="orion-icon-zoomin"
        class="life-radio-button1"
        @click="setZoom(0.1)"
      >
        放大
      </BasicButton>
      <BasicButton
        class="life-radio-button2"
        icon="orion-icon-zoomout"
        @click="setZoom(-0.1)"
      >
        缩小
      </BasicButton>
    </div>
    <BasicScrollbar
      ref="scrollbarRef"
      class="scrollBarClass"
    >
      <div
        id="life-canvas"
        ref="canvasRef"
      />
    </BasicScrollbar>

    <RightDrawer
      :projectId="projectId"
      :projectStatus="projectStatus"
      :selectData="selectData"
      :isDisabledBtn="isDisabledBtn"
      :standData="standData"
      @changeTabsId="changeTabsId"
      @editCacheData="editCacheData"
    />
  </div>
</template>
<script lang="ts" setup>
import {
  inject, onMounted, reactive, ref,
} from 'vue';
import {
  Graph, Node,
} from '@antv/x6';
import { BasicScrollbar, Icon, BasicButton } from 'lyra-component-vue3';

import RightDrawer from './components/rightDrawer.vue';

import { useRoute, useRouter } from 'vue-router';
import { getProjectNodes } from '/@/views/pms/projectLaborer/projectLab/api';
import Api from '/@/api';

const props = defineProps<{
  projectId: string,
  projectStatus:number,
  projectBudget: string;
  projectType:string,
}>();
const route = useRoute();
const router = useRouter();
const canvasRef = ref(null);
const graph = ref(null);
const detail = {
  status: 110,
};
const radioGroupValue = ref('');
const showRightDrawer = ref(false);
const selectData = reactive<any>({});
const isDisabledBtn = ref(true);
const standData = ref();// 该项目下的立项信息
const emit = defineEmits(['changeTabsId']);

const changeTabsId = (tabId) => {
  emit('changeTabsId', tabId);
};
const prevClickedNode = ref(null);
// 判断是否存在立项
async function projectApproval() {
  await new Api('/pms/projectApproval/getProjectApproval').fetch({ id: route.query.id }, '', 'GET').then((res) => {
    standData.value = res;
    selectData.value.actions = selectData.value?.actions.filter((item) => (res ? item.key === 'viewApproval' : item.key === 'startApproval'));
  });
}

const onNodeClick = ({
  e, x, y, node, view,
}) => {
  if (node.shape === 'sie-rect') {
    const defaultNodeFill = node.attr('body/fill'); // 获取节点的原始 body/fill 颜色
    const defaultTextColor = node.attr('label/fill'); // 获取节点的原始 label/fill 颜色

    for (let i in nodeLists.value) {
      if (nodeLists.value[i].nodeKey === node.id) {
        selectData.value = nodeLists.value[i];
      }
    }
    if (node.id === 'PROJECT_APPROVAL') {
      projectApproval();// 项目立项判断按钮是否展示
    }

    if (prevClickedNode.value) {
      prevClickedNode.value.attr('body/fill', defaultNodeFill); // 设置上一个节点的默认填充色
      prevClickedNode.value.attr('label/fill', defaultTextColor); // 设置上
    }

    node.attr('body/fill', '#1890FFFF'); // 设置选中节点的颜色
    node.attr('label/fill', '#fff'); // 设置当前节点的高亮文本颜色

    prevClickedNode.value = node; // 保存当前点击的节点
    showRightDrawer.value = true;
  }

  // if (node.attrs?.myNodeColor) {
  //   isDisabledBtn.value = node.attrs?.myNodeColor !== '#bfbfbf';
  // }

  // if (node.attrs.state && node.attrs.state.status === 'processNode') {
  //   oldNodes.value = newNode.value;
  //
  //   oldNodes.value.attr('body/fill', '#fff');
  //   oldNodes.value.attr('body/stroke', 'rgba(24, 144, 255, 1)');
  //   oldNodes.value.attr('text/fill', oldNodes.value?.attrs?.myNodeColor);
  //   oldNodes.value.attr('avatar/xlink:href', oldNodes.value.attrs.state.uncheckImg);
  //
  //   node.attr('state/checked', 'disChecked');
  //   for (let i in results.value.nodes) {
  //     if (results.value.nodes[i].nodeKey === node.id) {
  //       selectData.value = results.value.nodes[i];
  //     }
  //   }
  // newNode.value = node;
  // if (node.attrs.state.status === 'processNode' && node.attrs.state.checked === 'disChecked') {
  //   node.attr('avatar/xlink:href', node.attrs.state.checkImg);
  //   node.attr('body/fill', 'rgba(49, 104, 236, 1)');
  //   node.attr('body/stroke', 'rgba(49, 104, 236, 1)');
  //   node.attr('text/fill', '#fff');
  //
  //   node.attr('body/filter', {
  //     name: 'dropShadow',
  //     args: {
  //       color: 'rgba(24,144,255,0.375)',
  //       dx: 0,
  //       dy: 0,
  //       blur: 3,
  //     },
  //   });
  //   node.attr('state/checked', 'checked');
  // }
  // } else {
  // }
};

function editCacheData() {
  getAllNodeInfo();
}

const onNodeHover = ({
  e, x, y, node, view,
}) => {
  if (node.shape === 'sie-rect') {
    node.attr('body/filter', {
      name: 'dropShadow',
      args: {
        color: 'rgba(24,144,255,0.375)',
        dx: 0,
        dy: 0,
        blur: 3,
      },
    });
  }
};

const nodeLists = ref([]);
const onNodeLeave = ({
  e, x, y, node, view,
}) => {
  node.attr('body/filter', {
    name: 'dropShadow',
    args: {
      color: 'rgba(24,144,255,0.375)',
      dx: 0,
      dy: 0,
      blur: 0,
    },
  });
};

const projectInfo = inject('formData');
async function getAllNodeInfo() {
  const res = await new Api('/pms').fetch('', `project-life-cycle/${props.projectId}`, 'GET');
  nodeLists.value = res.nodes;
  res.nodes
    .forEach((item) => {
      if (item.nodeKey === selectData.value.nodeKey) {
        selectData.value = item;
      }
    });
}
async function initX6Dom() {
  // 定义不同形状的节点
  // 检查节点是否已注册
  if (!Node.registry.get('sie-rect')) {
    // 定义新节点
    Node.registry.register('sie-rect', {
      inherit: 'rect',
      width: 200,
      height: 50,
      markup: [
        {
          tagName: 'rect',
          selector: 'body',
        },
        {
          tagName: 'text',
          selector: 'label',
        },
      ],

      attrs: {
        bodyStyle: {
          cursor: 'pointer',
        },
        body: {
          fill: '#fff',
          stroke: '#1890FFFF',
          rx: 8,
          ry: 8,
          cursor: 'pointer',
          class: 'node-hover',
        },
        label: {
          fill: '#1890FFFF',
          fontSize: 18,
          fontWeight: 'bold',
          cursor: 'pointer',
        },

      },
    });
  }

  if (!Node.registry.get('sie-ellipse')) {
    // 定义新节点
    Node.registry.register('sie-ellipse', {
      inherit: 'rect',
      width: 120,
      height: 50,
      markup: [
        {
          tagName: 'rect',
          selector: 'body',
        },
        {
          tagName: 'text',
          selector: 'label',
        },
      ],
      attrs: {
        body: {
          fill: '#F09509FF',
          stroke: '#F09509FF',
          rx: 25,
          ry: 25,
        },
        label: {
          fill: '#fff',
          fontSize: 18,
          fontWeight: 'bold',
        },
      },
    });
  }

  if (!Node.registry.get('sie-circle')) {
    // 定义新节点
    Node.registry.register('sie-circle', {
      inherit: 'circle',
      width: 70,
      height: 70,
      markup: [
        {
          tagName: 'circle',
          selector: 'body',
        },
        {
          tagName: 'text',
          selector: 'label',
        },
      ],
      attrs: {
        body: {
          fill: '#D60072',
          stroke: '#D60072',
          r: 35,
        },
        label: {
          fill: '#fff',
          fontSize: 18,
          fontWeight: 'bold',
        },
      },
    });
  }

  // 注册节点

  const res = await new Api('/pms').fetch('', `project-life-cycle/${props.projectId}`, 'GET');

  graph.value = new Graph({
    container: document.getElementById('life-canvas'),
    width: 1280,
    height: 710,
    panning: true,
    interacting: {
      nodeMovable: false, // 禁用节点的拖拽功能
    },
  });

  nodeLists.value = res.nodes;
  const data = res;

  const nodes = {};
  data.nodes.forEach((node) => {
    let shape;
    let width;
    let height;
    if (node.nodeType === 'START_END_NODE') {
      shape = 'sie-circle';
      width = 70;
      height = 70;
    } else if (node.nodeType === 'STATUS_NODE') {
      shape = 'sie-ellipse';
      width = 120;
      height = 50;
    } else {
      shape = 'sie-rect';
      width = 160;
      height = 50;
    }
    let projectType = projectInfo.value.projectType || 'sell';

    if (node.nodeKey === 'PROJECT_DECLARE') {
      nodes[node.nodeKey] = graph.value.addNode({
        id: node.nodeKey,
        shape,
        width,
        height,
        label: node.name,
        draggable: false, // 禁用拖拽
        x: node.x,
        y: node.y,
        attrs: {
          isDefaultClick: true,
          bodyStyle: {
            cursor: 'pointer',
          },
          body: {
            fill: '#1890FFFF',

            rx: 8,
            ry: 8,
            cursor: 'pointer',
            class: 'node-hover',
          },
          label: {
            fill: '#fff',
            fontSize: 18,
            fontWeight: 'bold',
            cursor: 'pointer',
          },
        },
      });

      selectData.value = node;
      prevClickedNode.value = nodes[node.nodeKey];
    } else if (node.nodeKey === 'CONTRACT_SIGNING' && projectType !== 'scientific_research') {
      nodes[node.nodeKey] = graph.value.addNode({
        id: node.nodeKey,
        shape,
        width,
        height,
        label: node.name,
        draggable: false, // 禁用拖拽
        x: node.x,
        y: node.y,
        attrs: {
          isDefaultClick: true,
          bodyStyle: {
            cursor: 'pointer',
          },
          body: {
            fill: '#1890FFFF',

            rx: 8,
            ry: 8,
            cursor: 'pointer',
            class: 'node-hover',
          },
          label: {
            fill: '#fff',
            fontSize: 18,
            fontWeight: 'bold',
            cursor: 'pointer',
          },
        },
      });
      if (!selectData.value) {
        selectData.value = node;
        prevClickedNode.value = nodes[node.nodeKey];
      }
    } else {
      nodes[node.nodeKey] = graph.value.addNode({
        id: node.nodeKey,
        shape,
        width,
        attrs: {
          isHighlight: node.isHighlight,
        },
        height,
        label: node.name,
        draggable: false, // 禁用拖拽
        x: node.x,
        y: node.y,
      });
    }
  });

  data.edges.forEach((edge) => {
    if (edge.source === 'APPROVED') {
      const sourceNode = nodes[edge.source];
      const targetNode = nodes[edge.target];
      // 调整起点和终点的位置
      const sourcePosition = {
        x: sourceNode.getBBox().x + sourceNode.getBBox().width + ((targetNode.getBBox().x - sourceNode.getBBox().x - sourceNode.getBBox().width) / 2),
        y: sourceNode.getBBox().y + sourceNode.getBBox().height / 2,
      };
      const targetPosition = {
        x: sourceNode.getBBox().x + sourceNode.getBBox().width + ((targetNode.getBBox().x - sourceNode.getBBox().x - sourceNode.getBBox().width) / 2),
        y: targetNode.getBBox().y + targetNode.getBBox().height / 2,
      };
      graph.value.addEdge({
        source: { cell: nodes[edge.source] },
        target: { cell: nodes[edge.target] },
        connector: 'rounded',
        vertices: [sourcePosition, targetPosition],
        attrs: {
          line: {
            stroke: '#3168EC',
          },
        },
      });
    } else if (edge.target === 'PROJECT_ACCEPTANCE') {
      const sourceNode = nodes[edge.source];
      const targetNode = nodes[edge.target];
      // 调整起点和终点的位置
      const sourcePosition = {
        x: targetNode.getBBox().x - 40,
        y: sourceNode.getBBox().y + sourceNode.getBBox().height / 2,
      };
      const targetPosition = {
        x: targetNode.getBBox().x - 40,
        y: targetNode.getBBox().y + targetNode.getBBox().height / 2,
      };
      graph.value.addEdge({
        source: { cell: nodes[edge.source] },
        target: { cell: nodes[edge.target] },
        connector: 'rounded',
        vertices: [sourcePosition, targetPosition],
        attrs: {
          line: {
            stroke: '#3168EC',
          },
        },
      });
    } else {
      graph.value.addEdge({
        source: { cell: nodes[edge.source] },
        target: { cell: nodes[edge.target] },
        connector: 'rounded',

        attrs: {
          line: {
            stroke: '#3168EC',
          },

        },

      });
    }
  });

  graph.value.on('node:click', onNodeClick);
  graph.value.on('node:mouseenter', onNodeHover);
  const element = document.querySelector('.scrollBarClass');
  const { width } = element.getBoundingClientRect();
  const zoom = width / 1280;

  graph.value.zoom(-(1 - zoom));
  graph.value.centerContent();
  graph.value.translate(10, 20); // 将画布向右和向下移动100个单位

  // 根据 isHighlight 属性设置节点颜色
  graph.value.getNodes().forEach((node) => {
    const isHighlighted = node.attr('isHighlight'); // 获取节点的 isHighlight 属性
    // const originalColor = node.getProp('originalColor'); // 获取节点的原始颜色

    const isDefaultClick = node.attr('isDefaultClick');
    if (!isHighlighted && !isDefaultClick) {
      // 如果 isHighlight 为 true，设置节点为蓝色
      node.attr('body/fill', '#bfbfbf');
      node.attr('body/stroke', '#bfbfbf');
      node.attr('text/fill', '#fff');
      node.attr('label/fill', '#fff');
    }

    //   处理出入线规则
    const connectEdges = graph.value.getConnectedEdges(node.id);
    connectEdges.forEach((item) => {
      const sourceId = item.getSource().cell;
      const targetId = item.getTarget().cell;
      if (node.id === sourceId && !isHighlighted) {
        item.attr('line/stroke', '#bfbfbf');
      }
      if (node.id === targetId && !isHighlighted) {
        item.attr('line/stroke', '#bfbfbf');
      }
    });
  });
}

function setNodeColor(graph, node, state) {
  const color = '';

  // node.attr('body/style/fill', color);
  node.attr('body/style/stroke', color);
  node.attr('text/fill', color);
  node.attr('myNodeColor', color);
  if (node.getAttrs().body.fill === 'rgba(49, 104, 236, 1)') {
    node.attr('text/fill', '#fff');
  }

  // if (node.getAttrs().body.fill === '#D60072' || node.getAttrs().body.fill === 'rgba(240, 149, 9, 1)' || node.getAttrs().body.fill === '#f09509') {
  //   node.attr('body/style/fill', color);
  // }
}

const setZoom = (number:number) => {
  // event实例
  // number是放大或缩小
  graph.value.zoom(number);
};

onMounted(async () => {
  await initX6Dom();
});
</script>
<style lang="less" scoped>

.life-container {
  display: flex;
  margin: ~`getPrefixVar('content-margin-top')`
  ~`getPrefixVar('content-margin-left')` 0;
  justify-content: space-between;
}

.node-hover {
  background: red;
}
.node-hover:hover {
  box-shadow: 0px 0px 10px rgba(49, 104, 236, 1);
}
.scrollBarClass {
  width: 1280px;
  height: 710px;
}
.content-wrap>.content-main>.content>.basic-scrollbar {
  height: auto !important;
}
.life-radio{
  display: flex;
  position: absolute;
  right: 475px;
  z-index:100;
  .life-radio-button1{
    border-radius: 4px 0 0 4px;
    border-right: 0;
    margin-right: 0;
  }
  .life-radio-button2{
    border-radius: 0 4px 4px 0;
    margin-right: 0;
  }
}

</style>
