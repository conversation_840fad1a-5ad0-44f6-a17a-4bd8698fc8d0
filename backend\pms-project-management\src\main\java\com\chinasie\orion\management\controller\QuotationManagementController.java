package com.chinasie.orion.management.controller;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.management.domain.dto.QuotationManagementDTO;
import com.chinasie.orion.management.domain.vo.QuotationManagementVO;
import com.chinasie.orion.management.service.QuotationManagementService;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;


/**
 * <p>
 * QuotationManagement 报价管理 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-29 13:34:44
 */
@RestController
@RequestMapping("/quotationManagement")
@Transactional
@Api(tags = "报价管理")
@RequiredArgsConstructor
public class QuotationManagementController {

    private final QuotationManagementService quotationManagementService;

    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "报价管理", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<QuotationManagementVO> detail(
            @PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        QuotationManagementVO rsp = quotationManagementService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#quotationManagementDTO.name}}】",
            type = "报价管理", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody QuotationManagementDTO quotationManagementDTO) throws Exception {
        String rsp = quotationManagementService.create(quotationManagementDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#quotationManagementDTO.id}}】", type = "报价管理",
            subType = "编辑", bizNo = "{{#quotationManagementDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody QuotationManagementDTO quotationManagementDTO) throws Exception {
        Boolean rsp = quotationManagementService.edit(quotationManagementDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "发出报价")
    @RequestMapping(value = "/send/out", method = RequestMethod.PUT)
    @LogRecord(success = "【{USER{#logUserId}}】发出报价【{{#quotationManagementDTO.id}}】", type = "报价管理",
            subType = "发出报价", bizNo = "{{#quotationManagementDTO.id}}")
    public ResponseDTO<?> sendOut(@RequestBody QuotationManagementDTO quotationManagementDTO) {
        quotationManagementService.sendOut(quotationManagementDTO);
        return new ResponseDTO<>("");
    }

    @ApiOperation(value = "返回报价结果")
    @RequestMapping(value = "/bid/result", method = RequestMethod.PUT)
    @LogRecord(success = "【{USER{#logUserId}}】返回报价结果【{{#quotationManagementDTO.id}}】", type = "报价管理",
            subType = "返回报价结果", bizNo = "{{#quotationManagementDTO.id}}")
    public ResponseDTO<?> bidResult(@RequestBody QuotationManagementDTO quotationManagementDTO) {
        quotationManagementService.bidResult(quotationManagementDTO);
        return new ResponseDTO<>("");
    }

    @ApiOperation(value = "作废")
    @RequestMapping(value = "/abandon", method = RequestMethod.PUT)
    @LogRecord(success = "【{USER{#logUserId}}】作废【{{#quotationManagementDTO.id}}】", type = "报价管理",
            subType = "作废", bizNo = "{{#quotationManagementDTO.id}}")
    public ResponseDTO<?> abandon(@RequestBody QuotationManagementDTO quotationManagementDTO) {
        quotationManagementService.abandon(quotationManagementDTO);
        return new ResponseDTO<>("");
    }

    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "报价管理", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<?> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = quotationManagementService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "报价管理", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<?> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = quotationManagementService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "报价管理", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<QuotationManagementVO>> pages(@RequestBody Page<QuotationManagementDTO> pageRequest) throws Exception {
        Page<QuotationManagementVO> rsp = quotationManagementService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 查询已中标分页
     */
    @ApiOperation(value = "查询已中标分页")
    @LogRecord(success = "【{USER{#logUserId}}】查询已中标分页", type = "报价管理", subType = "查询已中标分页", bizNo = "")
    @RequestMapping(value = "/success/page", method = RequestMethod.POST)
    public ResponseDTO<Page<QuotationManagementVO>> successPages(
            @RequestBody Page<QuotationManagementDTO> pageRequest) {
        Page<QuotationManagementVO> rsp = quotationManagementService.successPages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 重新报价的，不需要走审批，直接通过到待报价状态
     */
    @ApiOperation(value = "确认报价单")
    @LogRecord(success = "【{USER{#logUserId}}】确认报价单", type = "报价管理", subType = "确认", bizNo = "")
    @RequestMapping(value = "/confirm", method = RequestMethod.POST)
    public ResponseDTO<?> confirm(@RequestBody QuotationManagementDTO quotationDto) {
        quotationManagementService.confirmQuotation(quotationDto);
        return new ResponseDTO<>("");
    }

    @ApiOperation("主数据状态的枚举")
    @GetMapping(value = "/status/list")
    @LogRecord(success = "【{USER{#logUserId}}】主数据状态的枚举", type = "报价管理", subType = "主数据状态的枚举", bizNo = "")
    public ResponseDTO<List<DataStatusVO>> statusList() {
        return new ResponseDTO<>(quotationManagementService.listDataStatus());
    }

    @ApiOperation("获取ECP公司商务用户")
    @GetMapping(value = "/get/issuer")
    @LogRecord(success = "【{USER{#logUserId}}】获取ECP公司商务用户", type = "报价管理", subType = "获取ECP公司商务用户", bizNo = "")
    public ResponseDTO<?> getIssuer() {
        return new ResponseDTO<>(quotationManagementService.getIssuer());
    }


    @ApiOperation("报价报表导出")
    @PostMapping(value = "/export/excelData",produces = "application/octet-stream")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "报价管理", subType = "导出数据", bizNo = "")
    public void exportExcelData(@RequestBody Page<QuotationManagementDTO> pageRequest, HttpServletResponse response) throws Exception {
        quotationManagementService.exportExcelData(pageRequest, response);
    }

    /**
     * 删除附件
     *
     * @param fileIds
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除附件")
    @RequestMapping(value = "/file/{fileId}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除报价附件【{{#fileIds}}】", type = "报价管理", subType = "删除附件", bizNo = "{{#fileIds}}")
    public ResponseDTO<Boolean> deleteFile(@RequestBody List<String> fileIds) throws Exception {
        Boolean rsp = quotationManagementService.deleteFile(fileIds);

        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("发起流程前检查")
    @RequestMapping(value = "/check/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】发起流程前检查", type = "报价管理", subType = "发起流程前检查", bizNo = "")
    public ResponseDTO<Boolean> check(@PathVariable(value = "id") String id) throws Exception {
        Boolean rsp = quotationManagementService.check(id);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("报价报表导出新")
    @PostMapping(value = "/export/excelData/new",produces = "application/octet-stream")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】报价报表导出新", type = "报价管理", subType = "报价报表导出新", bizNo = "")
    public void exportExcelDataNew(@RequestBody Page<QuotationManagementDTO> pageRequest, HttpServletResponse response) throws Exception {
        quotationManagementService.exportExcelDataNew(pageRequest, response);
    }

    /**
     * 分页
     */
    @ApiOperation(value = "菜单分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "报价管理", subType = "菜单分页", bizNo = "")
    @RequestMapping(value = "/page/menu", method = RequestMethod.POST)
    public ResponseDTO<Page<QuotationManagementVO>> pagesMenu(@RequestBody Page<QuotationManagementDTO> pageRequest) throws Exception {
        Page<QuotationManagementVO> rsp = quotationManagementService.pagesMenu(pageRequest);
        return new ResponseDTO<>(rsp);
    }
}

