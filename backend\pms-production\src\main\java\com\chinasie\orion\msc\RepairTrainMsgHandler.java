package com.chinasie.orion.msc;

import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.constant.MessageNodeDict;
import com.chinasie.orion.domain.entity.TrainManage;
import com.chinasie.orion.msc.api.MscBuildHandler;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class RepairTrainMsgHandler implements MscBuildHandler<TrainManage> {
    @Override
    public SendMessageDTO buildMsc(TrainManage trainManage, Object... objects) {
        Map<String, Object> message = new HashMap<>();
        message.put("$startTime$",trainManage.getCreateTime());
        message.put("$trainBase$",trainManage.getBaseName());
        message.put("$trainName$",trainManage.getName());

        SendMessageDTO sendMessageDTO = SendMessageDTO.builder()
                .messageMap(message)
                .titleMap(message)
                .messageUrl("/pms/trainManage/" + trainManage.getId())
                .messageUrlName("培训详情")
                .recipientIdList((List<String>) objects[0])
                .senderTime(new Date())
                .senderId(trainManage.getCreatorId())
                .orgId(trainManage.getOrgId())
                .platformId(trainManage.getPlatformId())
                .build();

        return sendMessageDTO;
    }

    @Override
    public String support() {
        return MessageNodeDict.NODE_REPAIR_TRAIN;
    }
}
