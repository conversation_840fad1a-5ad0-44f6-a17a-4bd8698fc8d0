package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * WorkHourFillDetail Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-15 17:48:39
 */
@ApiModel(value = "WorkHourFillDetailVO对象", description = "工时填报明细")
@Data
public class WorkHourFillDetailVO extends ObjectVO implements Serializable{

    /**
     * 工时填报天id
     */
    @ApiModelProperty(value = "工时填报天id")
    private String fillDayId;

    /**
     * 工时日期
     */
    @ApiModelProperty(value = "工时日期")
    private String workDate;

    /**
     * 工时
     */
    @ApiModelProperty(value = "工时")
    private Integer workHour;

    /**
     * 项目地点
     */
    @ApiModelProperty(value = "项目地点")
    private String projectPlace;

    /**
     * 关联对象
     */
    @ApiModelProperty(value = "关联对象")
    private String relateObject;

    /**
     * 任务内容
     */
    @ApiModelProperty(value = "任务内容")
    private String taskContent;

}
