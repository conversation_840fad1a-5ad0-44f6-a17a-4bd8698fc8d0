// 在 main.js 中引入并使用这个插件
import { createApp } from 'vue';
import App from './App.vue';
import errorHandler from './errorHandler';
import router from './router'; // 假设你有一个路由配置文件

const app = createApp(App);
app.use(errorHandler);
app.use(router);

// 全局路由守卫，记录页面路径
router.beforeEach((to, from, next) => {
  localStorage.setItem('currentPage', to.path);
  next();
});

// 定时发送错误信息到后台 （uid来源是4A）
function sendLogsAndErrors() {
  const errors = JSON.parse(localStorage.getItem('errors')) || [];
  const logs = JSON.parse(localStorage.getItem('logs')) || [];
  const uid = localStorage.getItem('uid');

  if ((errors.length > 0 || logs.length > 0) && uid) {
    const payload = {
      errors: errors,
      logs: logs,
      uid: uid
    };

    // 局域网测试：http://milado:9999/log/record/trace/db ， 内网测试：http://10.60.16.205:9999/log/record/trace/db ， -- 内网生产：http://lmp:9999/log/record/trace/db
    fetch('http://milado:9999/log/record/trace/db', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    })
        .then(response => response.json())
        .then(data => {
          console.log('Success:', data);
          // 清空已发送的错误信息和日志信息
          localStorage.removeItem('errors');
          localStorage.removeItem('logs');
        })
        .catch((error) => {
          console.error('Error:', error);
        });
  }
}

// 每 5 分钟发送一次错误信息和日志信息
setInterval(sendLogsAndErrors, 1 * 60 * 1000);

app.mount('#app');