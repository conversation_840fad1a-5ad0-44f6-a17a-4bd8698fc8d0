package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * NcfPurchProjectImplementation DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-12 10:24:16
 */
@ApiModel(value = "NcfPurchProjectImplementationDTO对象", description = "采购项目实施表")
@Data
@ExcelIgnoreUnannotated
public class NcfPurchProjectImplementationDTO extends ObjectDTO implements Serializable {

    /**
     * 流程名称
     */
    @ApiModelProperty(value = "流程名称")
    @ExcelProperty(value = "流程名称 ", index = 0)
    private String processName;

    /**
     * 发起人
     */
    @ApiModelProperty(value = "发起人")
    @ExcelProperty(value = "发起人 ", index = 1)
    private String promoter;

    /**
     * 发起时间
     */
    @ApiModelProperty(value = "发起时间")
    @ExcelProperty(value = "发起时间 ", index = 2)
    private Date initiationTime;

    /**
     * 采购立项申请号
     */
    @ApiModelProperty(value = "采购立项申请号")
    @ExcelProperty(value = "采购立项申请号 ", index = 3)
    private String purchReqEcpCode;

    /**
     * 采购申请号
     */
    @ApiModelProperty(value = "采购申请号")
    @ExcelProperty(value = "采购申请号 ", index = 4)
    private String purchReqDocCode;

    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
    @ExcelProperty(value = "申请人 ", index = 5)
    private String applicant;

    /**
     * 需求部门
     */
    @ApiModelProperty(value = "需求部门")
    @ExcelProperty(value = "需求部门 ", index = 6)
    private String applyDepartment;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    @ExcelProperty(value = "项目名称 ", index = 7)
    private String projectName;

    /**
     * 采购申请完成时间
     */
    @ApiModelProperty(value = "采购申请完成时间")
    @ExcelProperty(value = "采购申请完成时间 ", index = 8)
    private Date purchReqEndTime;

    /**
     * 采购立项申请金额
     */
    @ApiModelProperty(value = "采购立项申请金额")
    @ExcelProperty(value = "采购立项申请金额 ", index = 9)
    private BigDecimal purchReqAmount;

    /**
     * 商务人员
     */
    @ApiModelProperty(value = "商务人员")
    @ExcelProperty(value = "商务人员 ", index = 10)
    private String bizRespons;

    /**
     * 技术人员
     */
    @ApiModelProperty(value = "技术人员")
    @ExcelProperty(value = "技术人员 ", index = 11)
    private String techRespons;

    /**
     * 财务人员
     */
    @ApiModelProperty(value = "财务人员")
    @ExcelProperty(value = "财务人员 ", index = 12)
    private String financialStaff;

    /**
     * 其他人员
     */
    @ApiModelProperty(value = "其他人员")
    @ExcelProperty(value = "其他人员 ", index = 13)
    private String others;

    /**
     * 是否属于应集采范围
     */
    @ApiModelProperty(value = "是否属于应集采范围")
    @ExcelProperty(value = "是否属于应集采范围 ", index = 14)
    private Boolean isCollectionPurch;

    /**
     * 期望合同签订时间
     */
    @ApiModelProperty(value = "期望合同签订时间")
    @ExcelProperty(value = "期望合同签订时间 ", index = 15)
    private String expectedContractSigningTime;

    /**
     * 采购计划需求编号
     */
    @ApiModelProperty(value = "采购计划需求编号")
    @ExcelProperty(value = "采购计划需求编号 ", index = 16)
    private String purchPlanNumber;

    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型")
    @ExcelProperty(value = "合同类型 ", index = 17)
    private String contractType;

    /**
     * 合同状态
     */
    @ApiModelProperty(value = "合同状态")
    private String contractState;

    @ApiModelProperty(value = "合同执行状态")
    @ExcelProperty(value = "合同状态 ", index = 18)
    private String executionStatus;

    /**
     * 采购类型
     */
    @ApiModelProperty(value = "采购类型")
    @ExcelProperty(value = "采购类型 ", index = 19)
    private String purchType;

    /**
     * 采购方式
     */
    @ApiModelProperty(value = "采购方式")
    @ExcelProperty(value = "采购方式 ", index = 20)
    private String purchMethod;

    /**
     * 下一步工作安排
     */
    @ApiModelProperty(value = "下一步工作安排")
    @ExcelProperty(value = "下一步工作安排 ", index = 21)
    private String nextStepWorkArrangement;

    /**
     * 关注事项
     */
    @ApiModelProperty(value = "关注事项")
    @ExcelProperty(value = "关注事项 ", index = 22)
    private String concerns;

    /**
     * 已经耗时
     */
    @ApiModelProperty(value = "已经耗时")
    @ExcelProperty(value = "已经耗时 ", index = 23)
    private String usedTime;

    /**
     * 一级分发
     */
    @ApiModelProperty(value = "一级分发")
    @ExcelProperty(value = "一级分发 ", index = 24)
    private Date firstDistribution;

    /**
     * 二级分发
     */
    @ApiModelProperty(value = "二级分发")
    @ExcelProperty(value = "二级分发 ", index = 25)
    private Date secondaryDistribution;

    /**
     * 接受确认
     */
    @ApiModelProperty(value = "接受确认")
    @ExcelProperty(value = "接受确认 ", index = 26)
    private Date acceptConfirmation;

    /**
     * 采购启动发起
     */
    @ApiModelProperty(value = "采购启动发起")
    @ExcelProperty(value = "采购启动发起 ", index = 27)
    private Date purchStart;

    /**
     * 采购启动审批
     */
    @ApiModelProperty(value = "采购启动审批")
    @ExcelProperty(value = "采购启动审批 ", index = 28)
    private Date purchStartApproval;

    /**
     * 询价签发
     */
    @ApiModelProperty(value = "询价签发")
    @ExcelProperty(value = "询价签发 ", index = 29)
    private Date inqIssuance;

    /**
     * 报价截止时间
     */
    @ApiModelProperty(value = "报价截止时间")
    @ExcelProperty(value = "报价截止时间 ", index = 30)
    private Date quoteEnd;

    /**
     * 开启报价时间
     */
    @ApiModelProperty(value = "开启报价时间")
    @ExcelProperty(value = "开启报价时间 ", index = 31)
    private Date openQuote;

    /**
     * 评审时间
     */
    @ApiModelProperty(value = "评审时间")
    @ExcelProperty(value = "评审时间 ", index = 32)
    private Date reviewTime;

    /**
     * 公示发布时间
     */
    @ApiModelProperty(value = "公示发布时间")
    @ExcelProperty(value = "公示发布时间 ", index = 33)
    private Date reviewOutTime;

    /**
     * UPM审批中
     */
    @ApiModelProperty(value = "UPM审批中")
    @ExcelProperty(value = "UPM审批中 ", index = 34)
    private Date upmApprovalInProgress;

    /**
     * UPM审批完成
     */
    @ApiModelProperty(value = "UPM审批完成")
    @ExcelProperty(value = "UPM审批完成 ", index = 35)
    private Date upmApprovalComplete;

    /**
     * 发送SAP
     */
    @ApiModelProperty(value = "发送SAP")
    @ExcelProperty(value = "发送SAP ", index = 36)
    private Date sendSap;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 37)
    private String number;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 38)
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称 ", index = 39)
    private String contractName;

    /**
     * 是否发布启动公示
     */
    @ApiModelProperty(value = "是否发布启动公示")
    @ExcelProperty(value = "是否发布启动公示 ", index = 40)
    private String isPublicLaunch;

    /**
     * upm审批完成时间开始
     */
    @ApiModelProperty(value = "upm审批完成时间开始")
    @ExcelIgnore
    private String startDate;

    /**
     * upm审批完成时间结束
     */
    @ApiModelProperty(value = "upm审批完成时间结束")
    @ExcelIgnore
    private String endDate;

    /**
     * 采购申请完成时间开始
     */
    @ApiModelProperty(value = "采购申请完成时间开始")
    @ExcelIgnore
    private String approveStartDate;

    /**
     * 采购申请完成时间结束
     */
    @ApiModelProperty(value = "采购申请完成时间结束")
    @ExcelIgnore
    private String approveEndDate;

    /**
     * 已经耗时开始
     */
    @ApiModelProperty(value = "已经耗时开始")
    @ExcelIgnore
    private String startUsedTime;

    /**
     * 已经耗时结束
     */
    @ApiModelProperty(value = "已经耗时结束")
    @ExcelIgnore
    private String endUsedTime;

    /**
     * ECP采购申请号
     */
    @ApiModelProperty(value = "ECP采购申请号")
    @TableField(value = "ecp_purchase_app_no")
    private String ecpPurchaseAppNo;

    @ApiModelProperty(value = "需求部门编码")
    private String applyDepartmentCode;

    @ApiModelProperty(value = "需求部门编码(转换后)")
    private String applyDepartmentCodeTran;
}
