<template>
  <div>
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="isPower('HTGL_container_button_01', powerData)"
          icon="add"
          type="primary"
          @click="onAdd"
        >
          创建合同
        </BasicButton>
      </template>

      <template #payPercentage="{ text }">
        <Progress
          v-if="text < 100"
          :percent="text"
          size="small"
          style="padding-right: 40px"
        />
        <Progress
          v-else-if="(text = 100)"
          size="small"
          style="padding-right: 40px"
          :stroke-color="{
            from: '#67af64',
            to: '#63c3c2'
          }"
          :percent="text"
          status="active"
        />
      </template>
    </OrionTable>

    <AddContractDrawer @register="addContractRegister" />
  </div>
</template>

<script setup lang="ts">
import { h, ref, inject } from 'vue';
import {
  AddButton, BasicButton, isPower, OrionTable, useDrawer,
} from 'lyra-component-vue3';
import { formatMoney } from '/@/views/pms/utils/utils';
import { message, Progress, Tooltip } from 'ant-design-vue';
import dayjs from 'dayjs';
import { useRoute, useRouter } from 'vue-router';
import { AddContractDrawer } from './components';
import Api from '/@/api';

const router = useRouter();
const props = defineProps<{
  projectId: string,
  projectName: string
}>();

const tableRef = ref();

const [addContractRegister, { openDrawer }] = useDrawer();
const detail:any = inject('allData', {});
const powerData = inject('powerData', {});

const route = useRoute();
function onAdd() {
  openDrawer(true, {
    type: 'add',
    projectId: props.projectId,
    projectName: props.projectName,
    successChange() {
      tableReload();
    },
  });
}

function tableReload() {
  tableRef.value?.reload();
}

const tableOptions = {
  deleteToolButton: 'add|delete|enable|disable',
  api(params) {
    return new Api('/pas/projectContract').getPage(

      {
        ...params,
        query: {
          projectId: route.query.id,
        },
      },
    );
  },
  columns: [
    {
      title: '合同编号',
      dataIndex: 'number',
    },
    {
      title: '合同名称',
      dataIndex: 'name',
    },
    {
      title: '合同状态',
      dataIndex: 'dataStatus',
      slots: { customRender: 'status' },
    },
    {
      title: '合同类别',
      dataIndex: 'contractCategoryName',
    },
    {
      title: '合同类型',
      dataIndex: 'contractTypeName',
    },
    {
      title: '供应商',
      dataIndex: 'signedMainName',
    },
    {
      title: '合同总金额',
      dataIndex: 'contractMoney',
      align: 'right',
      customRender({ text }) {
        return h('span', formatMoney(text));
      },
    },
    {
      title: '合同支付进度',
      dataIndex: 'payPercentage',
      slots: { customRender: 'payPercentage' },

      // customRender({
      //   record,
      //   text,
      // }) {
      //   return h(Tooltip, {
      //     class: 'tooltip-class',
      //     title: record.payPercentage === 0 ? `未支付金额：${formatMoney(record.contractMoney || 0) || ''}` : `未支付金额：${formatMoney(record.payAmt || 0) || ''}`,
      //   }, h(Progress, {
      //     percent: isNaN(Number(text)) ? 0 : Number(Number(Number(text) * 100).toFixed()),
      //     size: 'small',
      //   }));
      // },
    },
    {
      title: '合同负责人',
      dataIndex: 'principalName',
    },
    {
      title: '合同开始日期',
      dataIndex: 'startDate',
      customRender({ text }) {
        return h('span', text ? dayjs(text)
          .format('YYYY-MM-DD') : '');
      },
    },

    {
      title: '合同结束日期',
      dataIndex: 'endDate',
      customRender({ text }) {
        return h('span', text ? dayjs(text)
          .format('YYYY-MM-DD') : '');
      },
    },

    {
      title: '合同签订日期',
      dataIndex: 'signDate',
      customRender({ text }) {
        return h('span', text ? dayjs(text)
          .format('YYYY-MM-DD') : '');
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      customRender({ text }) {
        return h('span', text ? dayjs(text)
          .format('YYYY-MM-DD HH:mm:ss') : '');
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 160,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '查看',
      isShow: isPower('HTGL_container_button_04', powerData),
      onClick(record) {
        router.push({
          name: 'ContractManageDetail',
          query: {
            id: record.id,
            projectId: route.query.id,
          },
        });
      },
    },
    {
      text: '编辑',
      isShow(record) {
        return record.dataStatus?.statusValue === 101 && isPower('HTGL_container_button_03', powerData);
      },
      onClick(record) {
        openDrawer(true, {
          type: 'edit',
          projectId: props.projectId,
          projectName: props.projectName,
          editData: record,
          successChange() {
            tableReload();
          },
        });
      },
    },
    {
      text: '删除',
      isShow(record) {
        return record.dataStatus?.statusValue === 101 && isPower('HTGL_container_button_04', powerData);
      },
      modal(record) {
        return new Api('/pas/projectContract').fetch([record.id], '', 'DELETE').then(() => {
          tableReload();
          message.success('删除成功');
        });
      },
    },
  ],
};
</script>

<style scoped lang="less">

</style>
