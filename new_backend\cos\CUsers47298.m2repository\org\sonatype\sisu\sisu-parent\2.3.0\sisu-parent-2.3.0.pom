<?xml version="1.0" encoding="UTF-8"?>

<!--
 ~ Copyright (c) 2010-2011 Sonatype, Inc.
 ~ All rights reserved. This program and the accompanying materials
 ~ are made available under the terms of the Eclipse Public License v1.0
 ~ and Apache License v2.0 which accompanies this distribution.
 ~ The Eclipse Public License is available at
 ~   http://www.eclipse.org/legal/epl-v10.html
 ~ The Apache License v2.0 is available at
 ~   http://www.apache.org/licenses/LICENSE-2.0.html
 ~ You may elect to redistribute this code under either of these licenses.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.sonatype.forge</groupId>
    <artifactId>forge-parent</artifactId>
    <version>10</version>
  </parent>

  <packaging>pom</packaging>

  <groupId>org.sonatype.sisu</groupId>
  <artifactId>sisu-parent</artifactId>
  <version>2.3.0</version>

  <name>Sisu</name>

  <url>http://sisu.sonatype.org/</url>
  <inceptionYear>2010</inceptionYear>

  <organization>
    <name>Sonatype, Inc.</name>
    <url>http://www.sonatype.com/</url>
  </organization>

  <mailingLists>
    <mailingList>
      <name>Sisu Developers List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <!-- archive>?</archive -->
    </mailingList>
    <mailingList>
      <name>Sisu Users List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <!-- archive>?</archive -->
    </mailingList>
    <mailingList>
      <name>Sisu Commits List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
    </mailingList>
  </mailingLists>

  <scm>
    <connection>scm:git:**************:sonatype/sisu.git</connection>
    <developerConnection>scm:git:**************:sonatype/sisu.git</developerConnection>
    <url>**************:sonatype/sisu.git</url>
  </scm>

  <issueManagement>
    <system>jira</system>
    <url>https://issues.sonatype.org/browse/SISU</url>
  </issueManagement>

  <ciManagement>
    <system>Hudson</system>
    <url>https://builds.sonatype.org/job/sisu/</url>
  </ciManagement>

  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
    <license>
      <name>Eclipse Public License, Version 1.0</name>
      <url>http://www.eclipse.org/legal/epl-v10.html</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <modules>
    <module>sisu-inject</module>
  </modules>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <forgeReleaseUrl>https://repository.sonatype.org/service/local/staging/deploy/maven2</forgeReleaseUrl>
  </properties>

  <dependencies>
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <dependencyManagement>
    <dependencies>

      <dependency>
        <groupId>javax.inject</groupId>
        <artifactId>javax.inject</artifactId>
        <version>1</version>
      </dependency>
      <dependency>
        <groupId>aopalliance</groupId>
        <artifactId>aopalliance</artifactId>
        <version>1.0</version>
      </dependency>
      <dependency>
        <groupId>javax.annotation</groupId>
        <artifactId>jsr250-api</artifactId>
        <version>1.0</version>
      </dependency>
      <dependency>
        <groupId>javax.enterprise</groupId>
        <artifactId>cdi-api</artifactId>
        <version>1.0</version>
        <exclusions>
          <exclusion>
            <groupId>javax.el</groupId>
            <artifactId>el-api</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.jboss.ejb3</groupId>
            <artifactId>jboss-ejb3-api</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.jboss.interceptor</groupId>
            <artifactId>jboss-interceptor-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>1.6.2</version>
      </dependency>
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-classic</artifactId>
        <version>0.9.29</version>
      </dependency>

      <dependency>
        <groupId>org.osgi</groupId>
        <artifactId>org.osgi.core</artifactId>
        <version>4.2.0</version>
      </dependency>
      <dependency>
        <groupId>org.osgi</groupId>
        <artifactId>org.osgi.compendium</artifactId>
        <version>4.2.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.felix</groupId>
        <artifactId>org.apache.felix.framework</artifactId>
        <version>3.2.2</version>
      </dependency>

      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>4.9</version>
      </dependency>
      <dependency>
        <groupId>org.testng</groupId>
        <artifactId>testng</artifactId>
        <version>6.2</version>
      </dependency>

    </dependencies>
  </dependencyManagement>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>2.3.2</version>
          <configuration>
            <source>1.5</source>
            <target>1.5</target>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-jar-plugin</artifactId>
          <version>2.3.2</version>
        </plugin>
        <plugin>
          <groupId>org.apache.felix</groupId>
          <artifactId>maven-bundle-plugin</artifactId>
          <version>2.3.5</version>
          <configuration>
            <instructions>
              <Bundle-Name>${project.artifactId}</Bundle-Name>
              <Bundle-Copyright>Copyright (C) 2010 Sonatype Inc.</Bundle-Copyright>
              <Bundle-DocURL>https://github.com/sonatype/sisu/</Bundle-DocURL>
              <Bundle-Vendor>Sonatype, Inc.</Bundle-Vendor>
              <Bundle-RequiredExecutionEnvironment>
                J2SE-1.5,JavaSE-1.6
              </Bundle-RequiredExecutionEnvironment>
              <_nouses>true</_nouses>
              <_removeheaders>
                Embed-Dependency,Embed-Transitive,
                Built-By,Tool,Created-By,Build-Jdk,
                Originally-Created-By,Archiver-Version,
                Include-Resource,Private-Package,
                Bnd-LastModified
              </_removeheaders>
            </instructions>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>2.3</version>
        </plugin>
        <plugin>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>2.8</version>
        </plugin>
        <plugin>
          <artifactId>maven-source-plugin</artifactId>
          <version>2.1.2</version>
        </plugin>
        <plugin>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.9</version>
          <configuration>
            <redirectTestOutputToFile>true</redirectTestOutputToFile>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-site-plugin</artifactId>
          <version>3.0</version>
        </plugin>
        <plugin>
          <artifactId>maven-gpg-plugin</artifactId>
          <version>1.3</version>
        </plugin>
        <plugin>
          <artifactId>maven-release-plugin</artifactId>
          <version>2.2.1</version>
          <configuration>
            <autoVersionSubmodules>true</autoVersionSubmodules>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <artifactId>maven-enforcer-plugin</artifactId>
        <version>1.0.1</version>
        <executions>
          <execution>
            <id>enforce-java</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireMavenVersion>
                  <version>3.0</version>
                </requireMavenVersion>
                <requireJavaVersion>
                  <version>1.6.0</version>
                </requireJavaVersion>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>animal-sniffer-maven-plugin</artifactId>
        <version>1.7</version>
        <configuration>
          <signature>
            <groupId>org.codehaus.mojo.signature</groupId>
            <artifactId>java15</artifactId>
            <version>1.0</version>
          </signature>
          <ignores>
            <ignore>javax.annotation.processing.*</ignore>
            <ignore>javax.lang.model.*</ignore>
            <ignore>javax.tools.*</ignore>
          </ignores>
        </configuration>
        <executions>
          <execution>
            <id>check-java-1.5-compat</id>
            <phase>process-classes</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <artifactId>maven-project-info-reports-plugin</artifactId>
        <version>2.4</version>
      </plugin>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>2.8</version>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>cobertura-maven-plugin</artifactId>
        <version>2.5.1</version>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>findbugs-maven-plugin</artifactId>
        <version>2.3.2</version>
      </plugin>
    </plugins>
  </reporting>

</project>
