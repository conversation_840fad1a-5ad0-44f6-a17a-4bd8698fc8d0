package com.chinasie.orion.service;





import com.chinasie.orion.domain.entity.JobHeightRisk;
import com.chinasie.orion.domain.dto.JobHeightRiskDTO;
import com.chinasie.orion.domain.vo.JobHeightRiskVO;
import java.lang.String;
import java.util.List;


import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import java.lang.String;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * JobHeightRisk 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-07 11:41:22
 */
public interface JobHeightRiskService  extends  OrionBaseService<JobHeightRisk>  {


        /**
         *  详情
         *
         * * @param id
         */
    JobHeightRiskVO detail(String id,String pageCode)throws Exception;

        /**
         *  新增
         *
         * * @param jobHeightRiskDTO
         */
        String create(JobHeightRiskDTO jobHeightRiskDTO)throws Exception;

        /**
         *  编辑
         *
         * * @param jobHeightRiskDTO
         */
        Boolean edit(JobHeightRiskDTO jobHeightRiskDTO)throws Exception;


        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;


        /**
         *  分页
         *
         * * @param pageRequest
         * 
         */
        Page<JobHeightRiskVO> pages( Page<JobHeightRiskDTO> pageRequest)throws Exception;

        /**
         *  下载模板
         *
         * * @param response
         */
        void downloadExcelTpl(HttpServletResponse response)throws Exception;

        /**
         *  导入校验
         *
         * * @param file
         */
        ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


        /**
           *  确认导入
           *
           * * @param importId
           */
        Boolean importByExcel(String importId)throws Exception;

        /**
       *  取消导入
       *
       * * @param importId
       */
        Boolean importCancelByExcel(String importId)throws Exception;

        /**
            *  导出
            *
            * * @param searchConditions
            * * @param response
            */
        void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

        /**
            *  设置各种名称
            *
            * * @param searchConditions
            * * @param response
            */
        void  setEveryName(List<JobHeightRiskVO> vos)throws Exception;

    List<JobHeightRiskVO> listByEntity(Page<JobHeightRiskDTO> jobHeightRiskDTO);

    List<JobHeightRiskVO> listByJobNumber(List<String> numberList);


    /**
     * 未完工详情
     * @param riskVOPage
     * @return
     */
    Page<JobHeightRiskVO> jobUndoneDetails(Page<JobHeightRiskVO> riskVOPage);

    /**
     * 查询计划开工的查询
     * @param riskVOPage
     * @return
     */
    Page<JobHeightRiskVO> queryPlanStartWorkDetails(Page<JobHeightRiskDTO> riskVOPage);
}
