package com.chinasie.orion.service.impl;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinasie.orion.bo.LyraFileBO;
import com.chinasie.orion.constant.FileConstant;
import com.chinasie.orion.domain.dto.ProjectSchemeDocumentDTO;
import com.chinasie.orion.domain.entity.ProjectSchemeDocument;
import com.chinasie.orion.domain.vo.FileExtVO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.ProjectSchemeDocumentRepository;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectSchemeDocumentService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * ProjectSchemeDocument 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-26 13:33:08
 */
@Service
public class ProjectSchemeDocumentServiceImpl extends OrionBaseServiceImpl<ProjectSchemeDocumentRepository, ProjectSchemeDocument> implements ProjectSchemeDocumentService {

//    @Resource
//    private FileService resFeignClientService;
    @Resource
    private LyraFileBO fileBO;// resFeignClientService;


    @Autowired
    private UserRedisHelper userRedisHelper;

    @Transactional
    @Override
    public Boolean create(String projectSchemeId, List<FileDTO> files) throws Exception {
        // 调用res服务保存文件到res
        files.forEach(item -> {
            item.setDataId(projectSchemeId);
            item.setDataType(FileConstant.FILETYPE_PROJECT_SCHEME_FILE);
            item.setRevKey(UUID.randomUUID().toString().replaceAll("-", ""));
            item.setRevOrder(1);
        });
        List<String> fileIds = fileBO.addBatch(files);//.getResult();

        try {
            List<ProjectSchemeDocument> files1 = fileIds.stream().map(fileId -> {
                ProjectSchemeDocument file = new ProjectSchemeDocument();
                file.setProjectSchemeId(projectSchemeId);
                file.setFileId(fileId);
                return file;
            }).collect(Collectors.toList());
            this.saveBatch(files1);
        } catch (Exception e) {
            throw e;
        }
        return true;
    }


    @Override
    public Boolean deleteFiles(String projectSchemeId, List<String> fileIds) throws Exception {
        LambdaQueryWrapperX<ProjectSchemeDocument> wrapper = new LambdaQueryWrapperX<>(ProjectSchemeDocument.class);
        wrapper.eq(ProjectSchemeDocument::getProjectSchemeId, projectSchemeId);
        wrapper.in(ProjectSchemeDocument::getFileId, fileIds);
        this.remove(wrapper);
        return true;
    }

    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public Page<FileExtVO> pages(String projectSchemeId, Page<ProjectSchemeDocumentDTO> pageRequest) throws Exception {
        IPage<ProjectSchemeDocument> realPageRequest = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());

        LambdaQueryWrapperX<ProjectSchemeDocument> wrapper = new LambdaQueryWrapperX<>(ProjectSchemeDocument.class);
        wrapper.orderByDesc(ProjectSchemeDocument::getCreateTime);
        wrapper.eq(ProjectSchemeDocument::getProjectSchemeId, projectSchemeId);
        IPage<ProjectSchemeDocument> page = this.page(realPageRequest, wrapper);

        // 获取res文件信息
        List<String> fileIdList = page.getRecords().stream().map(ProjectSchemeDocument::getFileId).collect(Collectors.toList());
        List<FileVO> fileDTOList = fileBO.getFileByIds(fileIdList)//.getResult()
                .stream()
                .sorted(Comparator.comparing(FileVO::getCreateTime).reversed())
                .collect(Collectors.toList());
        List<String> userIds = fileDTOList.stream().map(FileVO::getCreatorId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<String, UserVO> userVOMap = userRedisHelper.getUserByIds(userIds).stream().collect(Collectors.toMap(UserVO::getId, entry -> entry));
        List<FileExtVO> vos = BeanCopyUtils.convertListTo(fileDTOList, FileExtVO::new);

        vos.stream().forEach(item -> {
            UserVO userVO = userVOMap.get(item.getCreatorId());
            if (userVO != null) {
                item.setCreateUserName(userVO.getName());
                //todo DeptVO？？
                item.setCreatorCode(userVO.getCode());
            }
        });

        return new Page<>(page.getCurrent(), page.getSize(), page.getTotal(), vos);
    }
}

