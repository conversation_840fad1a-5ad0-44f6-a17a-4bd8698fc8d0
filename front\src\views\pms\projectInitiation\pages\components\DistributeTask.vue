<template>
  <div class="dis-body">
    <p class="dis-body-message">
      任务将通过待办通知下发给责任人，任务下发后无法撤回，请悉知
    </p>
    <a-radio-group v-model:value="distributesState.needNote">
      <a-radio :value="false">
        不需要抄送
      </a-radio>
      <a-radio :value="true">
        需要抄送
      </a-radio>
    </a-radio-group>

    <div
      v-if="distributesState.needNote"
      class="flex-box"
    >
      <span>请选择抄送人：</span>
      <AInput
        v-model:value="beNotifiedPersonsName"
        placeholder="请选择抄送人"
        style="width: 400px"
        @click="openSelectUser"
      />
      <!--      <a-select-->
      <!--        v-model:value="distributesState.beNotifiedPersons"-->
      <!--        placeholder="请选择抄送人"-->
      <!--        style="width: 400px"-->
      <!--        mode="multiple"-->
      <!--        :options="userList"-->
      <!--      />-->
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, Ref, ref } from 'vue';
import {
  message, Radio as ARadio, RadioGroup as ARadioGroup, Input as AInput,
} from 'ant-design-vue';
import Api from '/@/api';
import { openSelectUserModal } from 'lyra-component-vue3';

const props = withDefaults(defineProps<{
   modalData:object
}>(), {
  modalData: () => ({}),
});
const checked = ref(false);
const userList = ref([]);
const beNotifiedPersonsName:Ref<string> = ref('');
const beNotifiedPersonsList:Ref<any[]> = ref([]);
const distributesState:Ref<Record<any, any>> = ref({
  needNote: false,
  beNotifiedPersons: [],
});

onMounted(() => {
});
function openSelectUser() {
  openSelectUserModal(beNotifiedPersonsList.value, {
    okHandle(data:any[]) {
      if (data.length === 0) {
        message.warning('请选择人员');
        return new Promise((resolve, reject) => { reject(false); });
      }
      beNotifiedPersonsList.value = data;
      beNotifiedPersonsName.value = data.map((item) => item.name).join(',');
      distributesState.value.beNotifiedPersons = data.map((item) => item.id);
    },
  });
}

// 计划下发确认

defineExpose({
  async onSubmit() {
    const params = {
      ...props.modalData,
      ...distributesState.value,

    };
    await new Api('/pms').fetch(params, 'collaborativeCompilationTask/issue', 'PUT');
    message.success('下发成功');
  },
});
</script>
<style lang="less" scoped>
.dis-body {
  padding: 22px 22px 30px;
  .dis-body-message{
    font-size: 15px;
    font-weight: 500;
  }
  .workProcess{
    font-weight: 500;
    padding: 20px 0 10px 0;
  }
  .flex-box {
    display: flex;
    align-items: center;
    margin-top: 10px;

    > span {
      margin-right: 10px;
    }
  }
}
.plan-time{
  display: flex;
  align-items: center;
  gap: 0 10px;
  .plan-time-number{
    width: 80px !important;
  }
}
</style>
