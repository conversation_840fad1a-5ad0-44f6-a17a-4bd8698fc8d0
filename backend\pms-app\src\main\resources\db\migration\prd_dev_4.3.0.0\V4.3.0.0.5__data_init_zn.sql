ALTER TABLE `pmsx_quotation_management`
    ADD COLUMN `office_leader` varchar(64) NULL COMMENT '所级负责人';



ALTER TABLE `pms_market_contract`
    MODIFY COLUMN `cust_person_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '客户id。pms_customer_info id',
    ADD COLUMN `cust_group_in_out`     varchar(64) NULL COMMENT '客户-客户关系。编码' AFTER `cust_person_id`,
    ADD COLUMN `cust_bus_revenue_type` varchar(64) NULL COMMENT '客户-业务收入类型。编码' AFTER `cust_group_in_out`,
    ADD COLUMN `cust_sale_bus_type`    varchar(64) NULL COMMENT '客户-销售业务分类。客户关系 + 所属行业' AFTER `cust_bus_revenue_type`,
    ADD COLUMN `office_leader`         varchar(64) NULL COMMENT '所级负责人';



ALTER TABLE `pms_contract_milestone`
    ADD COLUMN `office_dept` varchar(64) NULL COMMENT '所级部门，pmi_dept id';
