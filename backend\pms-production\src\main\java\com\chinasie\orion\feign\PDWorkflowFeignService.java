package com.chinasie.orion.feign;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.feign.dto.FlowBusinessDTO;
import com.chinasie.orion.sdk.core.conf.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/11/01/11:06
 * @description:
 */
@FeignClient(name = "wf", configuration = {FeignConfig.class})
@Lazy
public interface PDWorkflowFeignService {


    @RequestMapping(value = "/flowTemplateBusiness/batch/start", method = RequestMethod.POST)
    ResponseDTO<Boolean> createList(@RequestBody @Validated List<FlowBusinessDTO> flowTemplateBusinessDTOS, @RequestParam(value = "dataType",required = false) String dataType) throws Exception;

    @RequestMapping(value = "/flowTemplateBusiness/one/start", method = RequestMethod.POST)
    ResponseDTO<Boolean> oneStart(@RequestBody @Validated FlowBusinessDTO flowBusinessDTO) throws Exception;
}
