<script setup lang="ts">
import { BasicCard } from 'lyra-component-vue3';
import {
  inject, onMounted, reactive, ref, unref,
} from 'vue';
import Api from '/@/api';
import { useRoute } from 'vue-router';
import dayjs from 'dayjs';
import { parseBooleanToRender, parsePriceByNumber, setBasicInfo } from '../../utils';

const route = useRoute();
const infoId = ref(route.params.id);
const detailsData = inject('detailsDataInfo');
const baseInfoProps = reactive({
  list: setBasicInfo([
    {
      label: '工作主题',
      field: 'workTopic',
    },
    {
      label: '流程名称',
      field: 'processName',
    },
    {
      label: '发起时间',
      field: 'initiationTime',
      formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') || '' : '--'),
    },
    {
      label: '发起人',
      field: 'initiator',
    },
    {
      label: '报销人',
      field: 'claimant',
    },
    {
      label: '申请公司编码',
      field: 'applyCompanyCode',
    },
    {
      label: '申请公司名称',
      field: 'applyCompanyName',
    },
    {
      label: '申请部门',
      field: 'applyDept',
    },
    {
      label: '费用归属公司编码',
      field: 'expenseCompanyCode',
    },
    {
      label: '费用归属公司名称',
      field: 'xpenseCompanyName',
    },
    {
      label: '立项类别',
      field: 'projectCategory',
    },
    {
      label: '立项号',
      field: 'projectCode',
    },
    {
      label: '立项名称',
      field: 'projectName',
    },
    {
      label: '归口部门',
      field: 'bkDept',
    },
    {
      label: '支付金额',
      field: 'paymentAmount',
      formatter: parseBooleanToRender,
    },
    {
      label: '交易金额',
      field: 'transactionAmount',
      formatter: parseBooleanToRender,
    },
    {
      label: '实际支付金额',
      field: 'actualPaymentAmount',
      formatter: parseBooleanToRender,
    },
    {
      label: '申请笔数',
      field: 'applyNumber',
    },
    {
      label: '报销金额',
      field: 'reimbursementAmount',
      formatter: parseBooleanToRender,
    },
  ]),
  column: 3,
  dataSource: detailsData,
});
const baseInfoProps2 = reactive({
  list: setBasicInfo([
    {
      label: '报销金额',
      field: 'reimbursedAmount',
    },
    {
      label: '要求付款时间',
      field: 'reqPaymentTime',
      formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') || '' : '--'),
    },
    {
      label: '币种',
      field: 'currency',
    },
    {
      label: '折合人民币',
      field: 'inRmb',
    },
    {
      label: '申请原因',
      field: 'applyReason',
    },
    {
      label: '费用信息',
      field: 'expenseInfo',
    },
    {
      label: '供应商信息',
      field: 'supplierInfo',
    },
    {
      label: '是否内部交易',
      field: 'isInternalTx',
      formatter: parseBooleanToRender,
    },
    {
      label: '内部交易号',
      field: 'internalTxNumber',
    },
    {
      label: '流程状态',
      field: 'processStatus',
    },
    {
      label: '支付方式',
      field: 'paymentWay',
    },
    {
      label: '支付金额',
      field: 'paymentAmount',
    },
    {
      label: '交易金额',
      field: 'transactionAmount',
    },
    {
      label: '实际支付金额',
      field: 'actualPaymentAmount',
    },
    {
      label: '申请笔数',
      field: 'applyNumber',
    },
    {
      label: '报销金额',
      field: 'reimbursementAmount',
    },
  ]),
  column: 3,
  dataSource: detailsData,
});
const baseInfoProps3 = reactive({
  list: setBasicInfo([
    {
      label: '立项类别',
      field: 'projectCategory',
    },
    {
      label: '立项号',
      field: 'projectCode',
    },
    {
      label: '立项名称',
      field: 'projectName',
    },
    {
      label: '归口部门',
      field: 'bkDept',
    },
  ]),
  column: 3,
  dataSource: detailsData,
});

</script>

<template>
  <BasicCard
    title="基本信息"
    :grid-content-props="baseInfoProps"
    :isBorder="false"
  />
  <BasicCard
    title="支付信息"
    :grid-content-props="baseInfoProps2"
    :isBorder="false"
  />
  <BasicCard
    title="立项信息"
    :grid-content-props="baseInfoProps3"
    :isBorder="false"
  />
</template>

<style scoped lang="less">

</style>
