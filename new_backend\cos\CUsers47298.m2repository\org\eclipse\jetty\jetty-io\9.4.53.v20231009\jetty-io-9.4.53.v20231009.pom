<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <artifactId>jetty-project</artifactId>
    <groupId>org.eclipse.jetty</groupId>
    <version>9.4.53.v20231009</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>jetty-io</artifactId>
  <name>Jetty :: IO Utility</name>
  <properties>
    <bundle-symbolic-name>${project.groupId}.io</bundle-symbolic-name>
    <spotbugs.onlyAnalyze>org.eclipse.jetty.io.*</spotbugs.onlyAnalyze>
  </properties>
  <dependencies>
    <dependency>
      <groupId>org.eclipse.jetty</groupId>
      <artifactId>jetty-util</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jetty</groupId>
      <artifactId>jetty-jmx</artifactId>
      <version>${project.version}</version>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jetty.toolchain</groupId>
      <artifactId>jetty-test-helper</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
