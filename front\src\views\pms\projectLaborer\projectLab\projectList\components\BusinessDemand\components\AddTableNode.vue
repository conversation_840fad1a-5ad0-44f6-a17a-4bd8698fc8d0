<template>
  <BasicDrawer
    :width="1000"
    wrap-class-name="addTableNode"
    @register="modalRegister"
    @visible-change="visibleChange"
  >
    <div
      class="demandManagement-formContent"
    >
      <div class="formContent_content">
        <BasicForm @register="registerForm">
          <template #number="{ model, field }">
            <div style="display: flex;">
              <a-input
                v-model:value="model[field]"
                style="width: 100%"
                disabled
                placeholder="文档创建完成后自动生成编号"
              />
            </div>
          </template>
        </BasicForm>
        <span
          v-if="!addMore"
          class="action-btn"
          @click="showMore"
        >添加更多信息</span>
      </div>
    </div>
    <SelectUserModal
      selectType="radio"
      @register="selectUserRegister"
    />
    <template #footer>
      <div class="addDocumentFooter">
        <ACheckBox
          v-if="formType=='add'"
          v-model:checked="checked"
          class="addModalFooterNext"
        >
          继续创建下一个
        </ACheckBox>
        <div class="btnStyle">
          <AButton
            @click="cancel"
          >
            取消
          </AButton>
          <AButton
            type="primary"
            :loading="loadingBtn"
            @click="confirm"
          >
            确认
          </AButton>
        </div>
      </div>
    </template>
    <SelectListModal
      selectType="radio"
      :show-left-tree="true"
      :getTreeApi="getTreeData"
      :get-table-data="getTableData"
      :isTableTree="true"
      :columns="columns"
      @register="selectListRegister"
      @confirm="createNode"
    />
  </BasicDrawer>
</template>
<script lang="ts">
import {
  defineComponent, onMounted, reactive, toRefs, computed, ref, nextTick, h,
} from 'vue';
import {
  BasicDrawer, useDrawerInner, BasicForm, useForm, SelectUserModal, useModal, getDict,
} from 'lyra-component-vue3';
import {
  Checkbox, Button, message, Input, Image,
} from 'ant-design-vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import { useUserStore } from '/@/store/modules/user';
import SelectListModal from '/@/views/components/SelectList/SelectListModal.vue';
export default defineComponent({
  name: 'AddTableNode',
  components: {
    BasicDrawer,
    ACheckBox: Checkbox,
    AButton: Button,
    BasicForm,
    AInput: Input,
    SelectUserModal,
    SelectListModal,
  },
  props: {
    addApi: {
      type: Function,
      default: () => null,
    },
    dirIdName: {
      type: String,
      default: '',
    },
    formId: {
      type: String,
      default: '',
    },
    columns: {
      type: Array,
      default: () => [],
    },
  },
  emits: ['update'],
  setup(props, { emit }) {
    const userInfo:any = useUserStore().getUserInfo;
    const [selectUserRegister, { openModal: selectUserOpenModal }] = useModal();
    const [selectListRegister, { openModal }] = useModal();
    const state = reactive({
      loadingBtn: false,
      checked: false,
      formType: 'add',
      typeTree: [],
      fieldList: [],
      dirIdOptions: [],
      formId: '',
      addMore: false,
      drawerData: {},
      statusOptions: [],
      demandTree: [],
      // 编辑的数据
      editData: null,
      // 负责人id
      principalId: undefined,
      // 提出人id
      exhibitor: undefined,
      deriveList: [],
      stageOptions: [],
    });
    const tableRef = ref();
    const [modalRegister, { closeDrawer, setDrawerProps }] = useDrawerInner(async (drawerData) => {
      state.checked = false;
      state.loadingBtn = false;
      state.formType = drawerData.type;
      state.editData = null;
      state.principalId = undefined;
      state.exhibitor = undefined;
      state.deriveList = [];
      clearValidate();
      resetFields();
      if (drawerData.type === 'add') {
        setDrawerProps({ title: '新增需求' });
        state.drawerData = drawerData.data;
        let defaultData :any = { stage: state.stageOptions.length > 0 ? state.stageOptions[0].value : '' };
        if (drawerData.data.dirId) {
          defaultData.dirId = findParent(state.dirIdOptions, drawerData.data.dirId);
          delete state.drawerData.dirId;
        }
        setFieldsValue(defaultData);
        // let ecrItem = state.dirIdOptions.find((item) => item.name.indexOf(props.dirIdName) >= 0);
        // setFieldsValue({ dirId: [ecrItem.id] });
      } else {
        state.addMore = true;
        state.formId = drawerData.data.id;
        setDrawerProps({ title: '编辑需求' });
        getItemData(state.formId);
      }
    });

    function findParent(data, val) {
      for (let i = 0; i < data.length; i++) {
        let item = data[i];
        if (item.id === val) {
          return [val];
        }
        if (Array.isArray(item.children) && item.children.length > 0) {
          let item1:any = findParent(item.children, val);
          if (item1) {
            return [item.id].concat(item1);
          }
        }
      }
    }
    function getItemData(id) {
      state.loading = true;
      new Api(`/pas/demand-management/detail/${id}`).fetch('', '', 'GET').then((res) => {
        state.loading = false;
        if (!res.type) {
          setFieldsValue(res);
          return;
        }
        new Api('/pas').fetch({ status: 1 }, `demand-type-to-demand-type-attribute/list/${res.type}`, 'GET').then((res1) => {
          state.fieldList = res1;
          appendFrom(state.fieldList, appendSchemaByField, 'type', 'number');
          let dirIdList = findParent(state.dirIdOptions, res.dirId);
          res = Object.assign(res, { dirId: dirIdList });
          if (res.principalName) {
            state.principalId = res.principalId;
          }
          if (res.exhibitorName) {
            state.exhibitor = res.exhibitor;
          }
          if (Array.isArray(res.derive) && res.derive.length > 0) {
            res.deriveName = res.derive.map((item) => item.name).join(',');
            state.deriveList = res.derive;
          }
          if (Array.isArray(res.typeAttrValueDTOList)) {
            res.typeAttrValueDTOList.forEach((item) => {
              let fileItem = res1.find((item1) => item1.number === item.attributeId);
              res[item.attributeId] = fileItem.type === '3' ? item.value ? item.value.split(';') : [] : item.value;
            });
          }
          state.editData = res;
          setFieldsValue(res);
        });
      }).catch((err) => {
        state.loading = false;
      });
    }
    const [
      registerForm,
      {
        setFieldsValue, clearValidate, resetFields, validateFields, appendSchemaByField, removeSchemaByFiled, getFieldsValue,
      },
    ] = useForm({
      actionColOptions: {
        span: 24,
      },
      showActionButtonGroup: false,
      schemas: [
        {
          field: 'name',
          component: 'Input',
          colProps: {
            span: 24,
          },
          label: '标题:',
          rules: [
            {
              required: true,
              trigger: 'blur',
              type: 'string',
            },
          ],
          componentProps: {
          },
        },
        {
          field: 'number',
          component: 'Input',
          label: '编号:',
          colProps: {
            span: 12,
          },
          rules: [
            // { required: true, trigger: 'blur' },
          ],
          componentProps: {
            disabled: true,
            placeholder: '新增完成时自动生成编号',
          },
          defaultValue: '新增完成时自动生成编号',
        },
        {
          field: 'stage',
          component: 'Select',
          label: '需求阶段:',
          colProps: {
            span: 12,
          },
          defaultValue: state.stageOptions.length > 0 ? state.stageOptions[0].value : '',
          required: true,
          componentProps: {
            options: computed(() => state.stageOptions),
            fieldNames: {
              label: 'description',
              value: 'value',
            },
            placeholder: '请选择需求阶段',
          },
        },
        {
          field: 'deriveName',
          component: 'Input',
          label: '需求派生',
          colProps: {
            span: 12,
          },
          componentProps: {

            disabled: computed(() => state.formType === 'edit'),
            placeholder: '请选择',
            onClick() {
              if (state.formType === 'edit') return;
              openModal(true, { title: '需求派生' });
            },
            addonAfter: h(
              'span',
              {
                disabled: computed(() => state.formType === 'edit'),
                // class: 'boxs_zkw',
                onClick: () => {
                  if (state.formType === 'edit') return;
                  openModal(true, { title: '需求派生' });
                },
              },
              '请选择',
            ),
            async onChange(value) {
              message.info('请选择');
              await setFieldsValue({ deriveName: '' });
              state.deriveList = [];
            },
          },
        },
        {
          field: 'dirId',
          component: 'Cascader',
          label: '路径',
          colProps: {
            span: 12,
          },
          componentProps: {
            fieldNames: {
              label: 'name',
              value: 'id',
            },
            disabled: true,
            options: computed(() => state.dirIdOptions),
          },
        },
        {
          field: 'principalName',
          component: 'Input',
          label: '负责人',
          colProps: {
            span: 12,
          },
          required: true,
          componentProps: {
            placeholder: '请选择',
            onClick() {
              selectUserOpenModal(true, {
                async onOk(data) {
                  await setFieldsValue({ principalName: data[0].name });
                  state.principalId = data[0].id;
                },
              });
            },
            addonAfter: h(
              'span',
              {
                // class: 'boxs_zkw',
                onClick: () => {
                  selectUserOpenModal(true, {
                    async onOk(data) {
                      await setFieldsValue({ principalName: data[0].name });
                      state.principalId = data[0].id;
                    },
                  });
                },
              },
              '请选择',
            ),
            async onChange(value) {
              message.info('请选择');
              await setFieldsValue({ principalName: '' });
              state.principalId = '';
            },
          },
        },
        {
          field: 'predictEndTime',
          component: 'DatePicker',
          label: '期望完成日期:',
          colProps: {
            span: 12,
          },
          componentProps: {
            style: { width: '100%' },
          },
        },
        {
          field: 'status',
          label: '状态',
          colProps: {
            span: 12,
          },
          rules: [
            {
              required: true,
              message: '请选择状态',
              trigger: 'change',
              type: 'number',
            },
          ],
          component: 'Select',
          defaultValue: computed(() => state.statusOptions[0]?.value),
          componentProps: {
            options: computed(() => state.statusOptions),
          },
        },
        {
          field: 'type',
          component: 'TreeSelect',
          label: '需求类型',
          colProps: {
            span: 12,
          },
          required: true,
          componentProps: {
            treeData: computed(() => state.typeTree),
            fieldNames: {
              children: 'children',
              key: 'id',
              value: 'id',
              label: 'name',
            },
            onChange: (val) => {
              initForm(val);
            },
          },
        },
        {
          field: 'remark',
          component: 'InputTextArea',
          label: '需求内容:',
          colProps: {
            span: 24,
          },
          componentProps: {
            rows: 4,
            showCount: true,
            maxlength: 500,
          },
        },
        {
          field: 'source',
          component: 'ApiSelect',
          label: '需求来源:',
          colProps: {
            span: 12,
          },
          componentProps: {
            api: () => getDict('dictd980c757ecc544e79dfeaa2cafe0126f'),
            labelField: 'description',
            valueField: 'value',
          },
          ifShow() {
            return computed(() => state.addMore).value;
          },
        },
        {
          field: 'exhibitorName',
          component: 'Input',
          label: '提出人:',
          colProps: {
            span: 12,
          },
          defaultValue: userInfo?.name,
          componentProps: {
            placeholder: '请选择',
            onClick: () => {
              selectUserOpenModal(true, {
                async onOk(data) {
                  await setFieldsValue({ exhibitorName: data[0].name });
                  state.exhibitor = data[0].id;
                },
              });
            },
            addonAfter: h(
              'span',
              {
                onClick: () => {
                  selectUserOpenModal(true, {
                    async onOk(data) {
                      await setFieldsValue({ exhibitorName: data[0].name });
                      state.exhibitor = data[0].id;
                    },
                  });
                },
              },
              '请选择',
            ),
            async onChange(value) {
              message.info('请选择');
              await setFieldsValue({ exhibitorName: '' });
              state.exhibitor = '';
            },
          },
          ifShow() {
            return computed(() => state.addMore).value;
          },
        },
        {
          field: 'proposedTime',
          component: 'DatePicker',
          label: '提出日期:',
          colProps: {
            span: 12,
          },
          componentProps: {
            style: { width: '100%' },
          },
          ifShow() {
            return computed(() => state.addMore).value;
          },
        },
        {
          field: 'priorityLevel',
          component: 'ApiSelect',
          label: '优先级:',
          colProps: {
            span: 12,
          },
          componentProps: {
            api: () => getDict('dictc56421e19b264d9c91394c48e447e4cb'),
            labelField: 'description',
            valueField: 'value',
          },
          ifShow() {
            return computed(() => state.addMore).value;
          },
        },
        {
          field: 'schedule',
          component: 'InputNumber',
          label: '进度:',
          colProps: {
            span: 12,
          },
          componentProps: {
            max: 100,
            min: 0,
            style: { width: '100%' },
            onChange(value) {
              if (value > 100 || value < 0) {
                message.error('状态值只能取0 ~ 100之间');
              }
            },
            addonAfter: h(
              'span',
              {
              },
              '%',
            ),
          },
          ifShow() {
            return computed(() => state.addMore).value;
          },
        },
      ],
    });
    const cancel = () => {
      closeDrawer();
    };

    const confirm = async () => {
      let formData = await validateFields();
      if (formData.principalName) {
        formData.principalId = state.principalId ?? state.editData?.principalId;
      }
      if (formData.exhibitorName) {
        formData.exhibitor = state.exhibitor ?? state.editData?.exhibitor;
      }
      if (formData.deriveName) {
        formData.deriveIds = state.deriveList.map((item) => item.id);
      }
      if (formData?.predictEndTime) {
        // 2022-10-13T01:57:57.663Z
        formData.predictEndTime = dayjs(formData.predictEndTime).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
      }
      if (formData?.proposedTime) {
        formData.proposedTime = dayjs(formData.proposedTime).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
      }
      let typeAttrValueDTOList = [];
      if (state.fieldList.length > 0) {
        state.fieldList.forEach((item) => {
          typeAttrValueDTOList.push({
            id: item.id,
            name: item.name,
            typeId: item.typeId,
            attributeId: item.number,
            value: Array.isArray(formData[item.number]) ? formData[item.number].join(';') : formData[item.number],
          });
          delete formData[item.number];
        });
      }
      formData.typeAttrValueDTOList = typeAttrValueDTOList;
      if (formData.dirId) {
        formData.dirId = formData.dirId[formData.dirId.length - 1];
      }
      state.loadingBtn = true;
      if (state.formType === 'add') {
        formData = Object.assign(state.drawerData, formData);
        if (!formData.exhibitorName) {
          formData.exhibitor = userInfo.id;
        }
        if (!formData.proposedTime) {
          formData.proposedTime = dayjs(new Date()).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
        }
        formData.number = '';
        new Api('/pas/demand-management/save').fetch(formData, '', 'POST').then((res) => {
          state.loadingBtn = false;
          message.success('新增成功');
          emit('update');
          if (state.checked) {
            resetFields();
            setFieldsValue({ dirId: [formData.dirId] });
            visibleChange(false);
          } else {
            closeDrawer();
          }
        }).catch((err) => {
          state.loadingBtn = false;
        });
      } else {
        formData.id = state.formId;
        new Api('/pas/demand-management/edit').fetch(formData, '', 'PUT').then((res) => {
          message.success('编辑成功');
          state.loadingBtn = false;
          emit('update');
          closeDrawer();
        });
      }
    };
    const visibleChange = (val) => {
      if (!val) {
        if (state.fieldList.length > 0) {
          state.fieldList.forEach((item) => {
            removeSchemaByFiled(item.number);
          });
          state.fieldList = [];
        }
        state.addMore = false;
      }
      // 关闭之前清除插入的字段
      // removeSchemaByFiled
    };
    onMounted(async () => {
      const res = await new Api('/pas/demand-management/getSimpleTree').fetch({}, '', 'POST');
      state.demandTree = [{ ...res }];
      new Api('/pas/demand-type/tree?status=1').fetch('', '', 'GET').then((res) => {
        state.typeTree = res;
      });
      new Api('/pas/demand-management/status/list').fetch({}, '', 'GET').then((res) => {
        res = res.sort((a, b) => a.sort - b.sort);
        state.statusOptions = res.map((item) => ({
          label: item.name,
          value: item.statusValue,
        }));
      });
      if (state.dirIdOptions.length === 0) {
        state.dirIdOptions = await new Api('/pas').fetch('', 'demand-dir/tree', 'GET');
      }
      getStageOptions();
    });
    function getStageOptions() {
      getDict('dict1767456377464340480').then((res) => {
        state.stageOptions = res;
      });
    }
    function initForm(val) {
      if (state.fieldList.length > 0) {
        state.fieldList.forEach((item) => {
          removeSchemaByFiled(item.number);
        });
      }
      if (typeof val === 'undefined' || !val) {
        return;
      }
      nextTick(() => {
        new Api('/pas').fetch({ status: 1 }, `demand-type-to-demand-type-attribute/list/${val}`, 'GET').then((res) => {
          state.fieldList = res;
          appendFrom(state.fieldList, appendSchemaByField, 'type', 'number');
        });
      });
    }
    function appendFrom(fieldList, appendSchemaByField, field, formField = 'id') {
      fieldList.forEach((item, index) => {
        let options = [];
        let fieldItem:any = {
          field: item[formField],
          component: '',
          required: item.require === 1,
          label: item.name,
          colProps: {
            span: 12,
          },
        };
        if (item.type === '1') {
          fieldItem.component = 'Input';
        } else {
          options = item.options.split(';').map((item1) => ({
            label: item1,
            value: item1,
          }));
          let componentProps:any = {
            options,
          };
          let rules = [
            {
              type: 'string',
              required: item.require === 1,
              message: `请选择${item.name}`,
              trigger: 'change',
            },
          ];
          if (item.type === '3') {
            componentProps.mode = 'multiple';
            rules[0].type = 'array';
            delete fieldItem.required;
            componentProps.rules = rules;
          }
          fieldItem.componentProps = componentProps;
          fieldItem.component = 'Select';
        }
        appendSchemaByField(
          fieldItem,
          field,
        );
      });
    }
    function showMore() {
      state.addMore = true;
      nextTick(() => {
        setFieldsValue({ proposedTime: dayjs(dayjs()).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]') });
      });
    }
    function getTableData(id, params) {
      return new Api('/pas').fetch('', `demand-management/getTreeByDirId/${id}`, 'POST');
    }
    function getTreeData() {
      return new Promise((resolve) => resolve(state.dirIdOptions));
    }
    function createNode(keys, rows) {
      state.deriveList = rows;
      setFieldsValue({ deriveName: rows.map((item) => item.name).join(',') });
    }

    return {
      selectUserRegister,
      ...toRefs(state),
      modalRegister,
      registerForm,
      cancel,
      confirm,
      visibleChange,
      tableRef,
      showMore,
      getTableData,
      createNode,
      getTreeData,
      selectListRegister,
    };
  },
});

</script>

<style lang="less" scoped>
.demandManagement-formContent{
  display: flex;
  height: 100%;
  flex-direction: column;
  .formContent_content{
    flex: 1 1 auto;
  }
  .moreMessage{
    color: #5976d6;
    cursor: pointer;
  }
  .actions{
    span{
      color: ~`getPrefixVar('primary-color')`;
      padding:0px 10px;
      cursor: pointer;
    }
    .actions1{
      border-right: 1px solid ~`getPrefixVar('primary-color')`;
    }
  }
}
:deep(.ant-form-item){
  display: block;
}
:deep(.ant-form-item-control){
  width: 100% !important;
}
.action-btn{
  padding: 0 var(--ant-content-padding-left)!important;
}
.addDocumentFooter{
  display: flex;
  align-items: center;
  justify-content: space-between;

  .btnStyle{
    flex: 1;
    text-align: right;
    .ant-btn{
      margin-left:10px;
      border-radius: 4px;
      padding: 4px 12px;
    }
    .canncel{
      background: #5172dc19;
      color: #5172DC;
    }
    .confirm{
      background: #5172dc;
      color: #ffffff;
    }
  }
}
</style>
