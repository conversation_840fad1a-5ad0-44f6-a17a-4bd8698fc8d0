package com.chinasie.orion.management.xxljob;

import cn.hutool.core.util.ObjectUtil;
import com.chinasie.orion.constant.RequirementNodeDict;
import com.chinasie.orion.domain.entity.MarketContract;
import com.chinasie.orion.management.constant.MsgHandlerConstant;
import com.chinasie.orion.management.constant.ProjectOrderStatusEnum;
import com.chinasie.orion.management.domain.dto.ProjectOrderDTO;
import com.chinasie.orion.management.domain.entity.NcfFormPurchOrder;
import com.chinasie.orion.management.domain.entity.ProjectFlow;
import com.chinasie.orion.management.domain.entity.ProjectOrder;
import com.chinasie.orion.management.repository.NcfFormPurchOrderMapper;
import com.chinasie.orion.management.service.ProjectFlowService;
import com.chinasie.orion.management.service.ProjectOrderService;
import com.chinasie.orion.msc.api.MscBuildHandlerManager;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.sdk.domain.vo.business.RoleVO;
import com.chinasie.orion.sdk.helper.RoleRedisHelper;
import com.chinasie.orion.sdk.helper.RoleUserHelper;
import com.chinasie.orion.service.MarketContractService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class MarketOrderDistributeXxlJob {


    @Resource
    MscBuildHandlerManager mscBuildHandlerManager;
    @Autowired
    ProjectOrderService projectOrderService;
    @Autowired
    ProjectFlowService projectFlowService;
    @Autowired
    RoleUserHelper roleUserHelper;
    @Autowired
    RoleRedisHelper roleRedisHelper;
    @Autowired
    MarketContractService marketContractService;

    @XxlJob("marketOrderDistributeJob")
    public void marketOrderDistributeJob() {

        //查询要发送的待确认的数据
        LambdaQueryWrapperX<ProjectFlow> projectFlowServiceLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        projectFlowServiceLambdaQueryWrapperX.eq(ProjectFlow::getOrderStatus, "待确认");
        LocalDate today = LocalDate.now();
        LocalDateTime start = today.atStartOfDay();
        LocalDateTime end = today.atTime(LocalTime.MAX);
        projectFlowServiceLambdaQueryWrapperX.between(ProjectFlow::getCreateTime, start, end);
        List<ProjectFlow> projectFlows = projectFlowService.list(projectFlowServiceLambdaQueryWrapperX);
        List<String> orderNumbers = new ArrayList<>();
        RoleVO role = new RoleVO();
        String orgId = null;
        if (ObjectUtil.isNotEmpty(projectFlows)) {
            projectFlows.forEach(item -> {
                String orderNumber = item.getOrderNumber();
                item.setRemark("待确认");
                orderNumbers.add(orderNumber);
            });
            projectFlowService.updateBatchById(projectFlows);
            orgId = projectFlows.get(0).getOrgId();
            role = roleRedisHelper.getRole("Business_021", orgId);
        }

        //获取商城商务下的所有用户
        if (ObjectUtil.isNotEmpty(role) && ObjectUtil.isNotEmpty(orgId)) {
            List<String> roleUsers = roleUserHelper.getUserIdsOfRoleId(orgId, role.getId());
            if (ObjectUtil.isNotEmpty(orderNumbers)) {
                LambdaQueryWrapperX<ProjectOrder> projectOrderLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
                projectOrderLambdaQueryWrapperX.in(ProjectOrder::getOrderNumber, orderNumbers);
                List<ProjectOrder> projectOrders = projectOrderService.list(projectOrderLambdaQueryWrapperX);
                //发消息给商城商务负责人
                for (ProjectOrder projectOrder : projectOrders) {
                    ProjectOrderDTO projectOrderDTO = new ProjectOrderDTO();
                    BeanCopyUtils.copyProperties(projectOrder, projectOrderDTO);
                    projectOrder.setStatus(ProjectOrderStatusEnum.NEEDCONFIRMED.getStatus());
                    for (String roleUser : roleUsers) {
                        mscBuildHandlerManager.send(projectOrderDTO, RequirementNodeDict.MARKET_SUB_ORDER_DISTRIBUTE, roleUser);
                    }

                }
                projectOrderService.updateBatchById(projectOrders);
            }
        }


        //查询要发送的待确认的数据
        LambdaQueryWrapperX<ProjectFlow> projectFlowServiceLambdaQueryWrapperX2 = new LambdaQueryWrapperX<>();
        List<String> status = Arrays.asList("待发货", "待提供服务","待确认服务","待收货","待验收","待付款","待评价","已完成","已关闭");
        projectFlowServiceLambdaQueryWrapperX2.in(ProjectFlow::getOrderStatus, status);

//        每次商城子订单通过ETL同步后，进行判断：
//
//        1. A为「需求已确认」；
//
//        2. B为「待发货」「待收货」「待提供服务」「『其他状态』」；
        List<ProjectFlow> flowList = projectFlowService.list(projectFlowServiceLambdaQueryWrapperX2);
        List<String> list = new ArrayList<>();
        for (ProjectFlow projectFlow : flowList) {
            String id = projectFlow.getId();
            list.add(id);
        }
        LambdaQueryWrapperX<ProjectOrder> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.in(ProjectOrder::getOrderNumber,list);
        queryWrapper.eq(ProjectOrder::getStatus,ProjectOrderStatusEnum.CONFIRM.getStatus());
        List<ProjectOrder> projectOrderList = projectOrderService.list(queryWrapper);

        List<String> orderNumber2s = new ArrayList<>();
        projectOrderList.forEach(item -> {
            item.setRemark("待发货");
            orderNumber2s.add(item.getOrderNumber());
        });
        projectFlowService.updateBatchById(flowList);
        if (ObjectUtil.isNotEmpty(orderNumber2s)) {
            LambdaQueryWrapperX<ProjectOrder> projectOrderLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            projectOrderLambdaQueryWrapperX.in(ProjectOrder::getOrderNumber, orderNumber2s);
            List<ProjectOrder> projectOrders = projectOrderService.list(projectOrderLambdaQueryWrapperX);
            //发消息给技术接口人
            projectOrders.forEach(item -> {
                item.setStatus(ProjectOrderStatusEnum.ORDERCOMFIRM.getStatus());
                String orderNumber = item.getOrderNumber();
                ProjectOrderDTO projectOrderDTO = new ProjectOrderDTO();
                BeanCopyUtils.copyProperties(item, projectOrderDTO);
                MarketContract marketContract = marketContractService.getById(orderNumber);
                String techRspUser = marketContract.getTechRspUser();
                mscBuildHandlerManager.send(projectOrderDTO, RequirementNodeDict.MARKET_SUB_ORDER_DISTRIBUTE, techRspUser);

            });
            projectOrderService.updateBatchById(projectOrders);
        }

    }

}