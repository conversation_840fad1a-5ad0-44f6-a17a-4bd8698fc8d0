package com.chinasie.orion.msc.handler;

import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.constant.MessageNodeConstant;
import com.chinasie.orion.dict.MessageNodeNumberDict;
import com.chinasie.orion.domain.entity.GoodsServicePlan;
import com.chinasie.orion.domain.entity.quality.QualityItem;
import com.chinasie.orion.msc.api.MscBuildHandler;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class QualityItemBuildHandler implements MscBuildHandler<QualityItem> {

    @Override
    public SendMessageDTO buildMsc(QualityItem qualityItem, Object... params) {
        // $assign$将$name$$action$至$deptName$的$rspUser$
        Map<String, Object> messageMap = new HashMap<>();

        messageMap.put("$projectName$", params[0]);

        SendMessageDTO sendMessageDTO = SendMessageDTO.builder()
                .titleMap(messageMap)
                .messageMap(messageMap)
                .messageUrl(qualityItem.getId())
                .messageUrlName("详情")
                .recipientIdList(Arrays.asList(qualityItem.getAffirm()))
                .senderId(CurrentUserHelper.getCurrentUserId())
                .senderTime(new Date())
                .todoType(TodoTypeDict.TODO_TYPE_TASK)
                .businessId(qualityItem.getId())
                .platformId(qualityItem.getPlatformId())
                .orgId(qualityItem.getOrgId())
                .build();
        log.error("质量控制项确认待办，质控项id：{}",qualityItem.getId());
        return sendMessageDTO;
    }

    @Override
    public String support() {
        return MessageNodeConstant.QUALITY_ITEM_COMPLETE;
    }
}

