<template>
  <a-form
    label-align="left"
    :label-col="{ span: pageType==='page'?5:6 }"
    :wrapper-col="{ span: pageType==='page'?19:18 }"
  >
    <a-row :gutter="20">
      <a-col
        :span="12"
        class="content-box"
      >
        <BasicTitle title="预览" />
      </a-col>
      <a-col
        :span="12"
        class="content-box"
      >
        <BasicTitle title="基本信息">
          <BasicButton
            v-if="isPower('PMS_JFW_container_01_button_01', powerData)"
            type="primay"
            @click="onEdit()"
          >
            编辑
          </BasicButton>
          <a-form-item label="编号">
            {{ father.form.number }}
          </a-form-item>
          <a-form-item label="名称">
            {{ father.form.name }}
          </a-form-item>
          <a-form-item label="所属项目">
            {{ father.form.projectName }}
          </a-form-item>
          <a-form-item label="所属任务">
            {{ father.form.planName }}
          </a-form-item>
          <a-form-item label="状态">
            {{ father.form.statusName }}
          </a-form-item>
          <a-form-item label="版本">
            {{ father.form.revId }}
          </a-form-item>
          <a-form-item label="负责人">
            {{ father.form.principalName }}
          </a-form-item>
          <a-form-item label="计划交付时间">
            {{ father.form.predictDeliverTime }}
          </a-form-item>
          <a-form-item label="实际交付时间">
            {{ father.form.deliveryTime }}
          </a-form-item>
          <a-form-item label="描述">
            {{ father.form.remark }}
          </a-form-item>
          <a-form-item label="修改人">
            {{ father.form.modifyName }}
          </a-form-item>
          <a-form-item label="修改时间">
            {{ father.form.modifyTime }}
          </a-form-item>
          <a-form-item label="创建人">
            {{ father.form.creatorName }}
          </a-form-item>
          <a-form-item label="创建时间">
            {{ father.form.createTime }}
          </a-form-item>
        </BasicTitle>
      </a-col>
    </a-row>
  </a-form>
</template>

<script>
import { Row, Col, Form } from 'ant-design-vue';
import BasicTitle from '/@/views/pms/projectLaborer/componentsList/basicTitle/index.vue';
import { BasicButton, isPower } from 'lyra-component-vue3';

import dayjs from 'dayjs';
import { inject, reactive, toRefs } from 'vue';
export default {
  name: 'View',
  components: {
    ARow: Row,
    ACol: Col,
    BasicTitle,
    AForm: Form,
    AFormItem: Form.Item,
    BasicButton,
  },
  props: {
    data: {
      type: Object,
      default() {
        return {};
      },
    },
    pageType: {
      type: String,
      default: 'page',
    },
    onEdit: {
      type: Function,
      default: null,
    },
  },
  setup(props) {
    const state = reactive({
      father: props.data,
    });
    const powerData = inject('powerData');
    function formatDate(t) {
      return t ? dayjs(t).format('YYYY-MM-DD HH:mm:ss') : '';
    }

    return {
      ...toRefs(state),
      formatDate,
      powerData,
    };
  },
  methods: { isPower },
};
</script>

<style scoped lang="less">
  :deep(.basicTitle) {
    .basicTitle_content {
      padding-left: 40px !important;
    }
    .ant-form-item {
      margin-bottom: 10px;
    }
  }
</style>
