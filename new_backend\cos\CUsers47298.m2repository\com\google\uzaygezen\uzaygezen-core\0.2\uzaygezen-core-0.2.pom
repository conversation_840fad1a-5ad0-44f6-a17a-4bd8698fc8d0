<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.uzaygezen</groupId>
  <artifactId>uzaygezen-core</artifactId>
  <packaging>jar</packaging>
  <version>0.2</version>
  <name>Uzaygezen-core</name>
  <parent>
    <groupId>com.google.uzaygezen</groupId>
    <artifactId>uzaygezen</artifactId>
    <version>0.2</version>
  </parent>
  <dependencies>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.1</version>
    </dependency> 
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>14.0-rc1</version>
    </dependency>
    <dependency>
      <groupId>log4j</groupId>
      <artifactId>log4j</artifactId>
      <version>1.2.17</version>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.11</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.easymock</groupId>
      <artifactId>easymock</artifactId>
      <version>3.1</version>
      <scope>test</scope>
    </dependency> 
    <dependency>
      <groupId>org.apache.hadoop</groupId>
      <artifactId>hadoop-core</artifactId>
      <version>1.1.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.hbase</groupId>
      <artifactId>hbase</artifactId>
      <version>0.94.2</version>
      <scope>test</scope>
    </dependency> 
  </dependencies>

  <scm>
    <connection>scm:svn:https://uzaygezen.googlecode.com/svn/tags/uzaygezen-core-0.2</connection>
    <developerConnection>scm:svn:https://uzaygezen.googlecode.com/svn/tags/uzaygezen-core-0.2</developerConnection>
    <url>https://uzaygezen.googlecode.com/svn/tags/uzaygezen-core-0.2</url>
  </scm>
</project>
