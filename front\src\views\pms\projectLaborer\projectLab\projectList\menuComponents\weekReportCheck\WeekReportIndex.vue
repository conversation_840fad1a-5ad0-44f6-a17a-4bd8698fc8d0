<template>
  <Layout :options="{ body: { scroll: true } }">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      :isTable="state.isTable"
      @selectionChange="selectionChange"
      @smallSearch="smallSearch"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="isPower('PMS_XMXQ_container_21_04_01_button_01',powerData)"
          icon="sie-icon-down"
          type="primary"
          :disabled="!state.rows.length"
          @click="handle('add')"
        >
          批量审核
        </BasicButton>
        <BasicButton
          v-if="isPower('PMS_XMXQ_container_21_04_01_button_02',powerData)"
          :disabled="!state.rows.length"
          icon="sie-icon-daochu"
          @click="downLoad"
        >
          导出
        </BasicButton>
      </template>
      <template #toolbarRight>
        <a-range-picker
          v-show="state.isTable"
          v-model:value="state.searchTime"
          class="mr10"
        />
        <a-radio-group
          v-if="state.isTable"
          v-model:value="state.searchWeekRadio"
          button-style="solid"
          class="mr10"
        >
          <a-radio-button value="ALL">
            全部
          </a-radio-button>
          <a-radio-button value="LAST_MONTH">
            上月
          </a-radio-button>
          <a-radio-button value="THIS_MONTH">
            本月
          </a-radio-button>
          <a-radio-button value="NEXT_MONTH">
            下月
          </a-radio-button>
        </a-radio-group>
        <a-radio-group
          v-if="!state.isTable"
          v-model:value="state.searchCardWeekRadio"
          button-style="solid"
          class="mr10 ml10"
        >
          <a-radio-button value="CARD_LAST_WEEK">
            上周
          </a-radio-button>
          <a-radio-button value="CARD_WEEK">
            本周
          </a-radio-button>
          <a-radio-button value="CARD_MONTH">
            本月
          </a-radio-button>
        </a-radio-group>

        <a-radio-group
          v-model:value="state.tableType"
          button-style="solid"
          class="mr10"
        >
          <a-radio-button value="list">
            <AlignLeftOutlined />
          </a-radio-button>
          <a-radio-button value="content">
            <AppstoreOutlined />
          </a-radio-button>
        </a-radio-group>
      </template>
      <template #otherContent>
        <!--展示卡片-->
        <div
          v-if="!state.isTable"
        >
          <!--月度卡片视图时间-->
          <MonthFormat
            v-if="isWeekly"
            ref="MonthFormatTime"
            @time-change="timeChange"
          />
          <!--月度卡片明细时间-->
          <div
            v-if="!isWeekly"
            style="color: #7e7d7d"
          >
            <BasicButton
              @click="setIsWeekly"
            >
              返回
            </BasicButton>
            <Icon
              icon="fa-angle-double-left"
              size="12"
              class="mr10  cursorPointer"
            />
            <Icon
              icon="fa-angle-left"
              size="12"
              class="mr20  cursorPointer"
            />
            <span class="mr20">
              {{ `${WeeksNumber}周` }}
            </span>
            <Icon
              icon="fa-angle-right"
              size="12"
              class="mr10 cursorPointer "
            />
            <Icon
              icon="fa-angle-double-right"
              size="12"
              class="mr10 cursorPointer "
            />
          </div>
          <!--月度卡片视图-->
          <div
            v-if="isWeekly"
            class="week_cont"
          >
            <div
              v-for="(item,index) in weeksInCurrentMonth.weeks"
              :key="index"
              :class="{'weekly_cont':true,'mark':item.weekNumber==weekIndexNum}"

              @click="goDetails"
            >
              <div class="week_title">
                <div class="week_title_lt">
                  {{ `${item.weekNumber}W` }}
                </div>
              </div>
              <div
                class="week_list"
              >
                <div
                  class="submitted_cont"
                  @click.stop.prevent=""
                >
                  <span
                    class="mr10 action-btn"
                    @click="goDetailedList(item.submitted,item.weekNumber)"
                  >已提交:</span>
                  <span
                    v-if="!item.submittedNumber"
                    class="submitted"
                  > 0</span>
                  <Popover
                    v-else
                    title="已提交列表"
                    trigger="hover"
                    placement="right"
                    :get-popup-container="getPopupContainer"
                  >
                    <template #content>
                      <p
                        v-for="(i,index) in item.submitted"
                        :key="index"
                        class="submitted_list"
                      >
                        <span>
                          {{ i.creatorName }}
                        </span>
                        <span
                          v-if="item.score?1:0"
                        >
                          {{ `${i.score}分` }}
                        </span>
                        <span
                          v-if="i.statusValue!== 120"
                          class=" examine action-btn"
                          @click="check(i.id)"
                        >
                          审核
                        </span>
                      </p>
                    </template>
                    <span class="submitted">{{ item.submittedNumber }}</span>
                  </Popover>
                </div>
                <div
                  class="submitted_cont"
                  @click.stop.prevent=""
                >
                  <span
                    class="mr10 action-btn notSubmitted"
                    @click="goDetailedList(item.noSubmitted,item.weekNumber)"
                  >未提交:</span>
                  <span
                    v-if="!item.noSubmittedNumber"
                    class="notSubmitted"
                  > 0 </span>
                  <Popover
                    v-else
                    title="未提交列表"
                    trigger="hover"
                    placement="right"
                    :get-popup-container="getPopupContainer"
                  >
                    <template #content>
                      <p
                        v-for="(i,index) in item.noSubmitted"
                        :key="index"
                        class="submitted_list"
                      >
                        <span>
                          {{ i.creatorName }}
                        </span>
                        <span
                          class=" examine action-btn"
                          @click="alertPeople(i.id)"
                        >
                          提醒
                        </span>
                      </p>
                    </template>
                    <span class="notSubmitted">{{ item.noSubmittedNumber }}</span>
                  </Popover>
                </div>
              </div>
            </div>
          </div>
          <!--卡片明细-->
          <div
            v-if="!isWeekly"
            class="week_cont"
          >
            <div
              v-for="(item,index) in cardDataArr"
              :key="index"
              class="detail_cont"
            >
              <div class="week_title">
                <div class="week_title_lt">
                  {{ `${WeeksNumber}W` }}
                </div>
                <div class="week_title_rt">
                  <div
                    v-if="item.score?1:0"
                    :class="{operate:true,green:item.score==3,yellow:item.score>3,grey:item.score<3}"
                  >
                    {{ item.score }}
                  </div>
                  <div
                    v-if="item.status=== 120"
                    class="action-btn"
                    @click="check(item.id)"
                  >
                    审核
                  </div>

                  <div
                    v-if="item.status=== 101"
                    class="action-btn"
                    @click="alertPeople(item.id)"
                  >
                    提醒
                  </div>
                </div>
              </div>
              <div
                class="week_list"
                @click="goDetails"
              >
                <div
                  v-for="(j,index) in item?.projectWeeklyStatementContentVOList"
                  :key="index"
                  class="list_item_cont"
                >
                  <h4 class="time">
                    {{ `${j?.taskTime}H` }}
                  </h4>
                  <tooltip
                    placement="top"
                    trigger="hover"
                    mouseEnterDelay="1.5"
                    :title="j?.content"
                    :auto-adjust-overflow="true"
                  >
                    <p
                      class="list_item"
                    >
                      {{ j?.content }}
                    </p>
                  </tooltip>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>

      <template #actions="{record}">
        <BasicTableAction
          :actions="actionList"
          :record="record"
        />
      </template>
    </OrionTable>
    <CheckDrawer
      ref="addOrEditRef"
      @update="updateTable"
    />
  </Layout>
</template>

<script setup lang="ts">
import {
  computed, reactive, watch, ref, onMounted, watchEffect, Ref, inject,
} from 'vue';
import {
  BasicButton, Layout, OrionTable, BasicTableAction, downloadByData as basicDownloadByData, isPower,
} from 'lyra-component-vue3';
import {
  Button, DatePicker, Radio, Calendar, Popover, Modal, message,
} from 'ant-design-vue';
import Api from '/@/api';
import { AlignLeftOutlined, AppstoreOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import { useRoute, useRouter } from 'vue-router';
import { getActionsList, getColumns } from './config/index';
import CheckDrawer from './component/addOrEdit/CheckDrawer.vue';
import PeopleList from './component/cardList/PeopleList.vue';
import Icon from '/@/components/Icon/src/Icon.vue';
import DayFormat from './component/timeFormat/DayFormat.vue';
import MonthFormat from './component/timeFormat/MonthFormat.vue';

const powerData = inject('powerData', []);
const route = useRoute();
const router = useRouter();
const AButton = Button;
const ARadioGroup = Radio.Group;
const ARadioButton = Radio.Button;
const ARangePicker = DatePicker.RangePicker;
const addOrEditRef = ref(null);
const tableRef = ref(null);
const weeksInCurrentMonth = ref();
const currentYear = ref(new Date().getFullYear());
const currentMonth = ref(new Date().getMonth());
const weekIndexNum = ref(); // 当前日期为多少周
const weekIndexNumCopy = ref(); // 当前日期为多少周
const MonthFormatTime = ref(null);
const cont = ref(4);
const isWeekly = ref(true);
const startDateTime = ref();
const endDateTime = ref();
const cardDataArr = ref();
const WeeksNumber = ref();
const state = reactive({
  tableRef,
  addOrEditRef,
  isTable: true,
  showPeopleComponent: false,
  loading: false,
  tableType: 'list',
  searchWeekRadio: 'ALL',
  searchCardWeekRadio: 'CARD_MONTH',
  searchTime: undefined,
  dataSource: [] as any,
  allPeopleCard: [] as any,
  timeEmit: null,
  checkCardData: {} as any,
  rows: [],
  canCheck: false,
  keyword: '',
});
const actionList = getActionsList({
  router,
  state,
});
watch(() => state.tableType, () => {
  state.isTable = state.tableType === 'list';
});
const headAuthList: Ref<any[]> = ref([]);
const tableOptions = {
  showToolButton: false,
  rowSelection: {},
  showSmallSearch: computed(() => state.tableType === 'list'),
  pagination: computed(() => state.tableType === 'list'),
  api: (params) => {
    params.query = getParams();
    params.query.pageType = true;
    params.power = {
      pageCode: 'PMS0004',
      headContainerCode: 'PMS_XMXQ_container_21_04_01',
      containerCode: 'PMS_XMXQ_container_21_04_02',
    };
    return new Api('/pms/projectWeekly/pages').fetch(params, '', 'POST').then((res) => {
      state.showPeopleComponent = false;
      state.isTable = true;
      if (res?.content?.length) {
        state.dataSource = res.content;
      } else {
        state.dataSource = [];
      }
      state.rows = [];
      state.canCheck = false;
      headAuthList.value = res.headAuthList || [];
      return res;
    });
  },
  // isFilter2: true,
  // pagination: computed(() => state.isTable).value,
  // filterConfig,
  columns: getColumns({ router }),
};
onMounted(() => {
  getWeeksInMonth(currentYear.value, currentMonth.value + 1);
  weekIndexNum.value = weeksInCurrentMonth.value.currentWeekNumber;
  weekIndexNumCopy.value = weeksInCurrentMonth.value.currentWeekNumber;
});

// watchEffect(() => {
//   if (state.checkCardData.length) {
//     weeksInCurrentMonth.value.weeks = weeksInCurrentMonth.value.weeks.map((j) => {
//       const matchingData = state.checkCardData.find((i) => i.weekly === j.weekNumber);
//       if (matchingData) {
//         return { ...j, ...matchingData };
//       }
//       return { startDate: j.startDate, endDate: j.endDate, weekNumber: j.weekNumber };
//     });
//   } else {
//     weeksInCurrentMonth.value?.weeks.forEach((j, index) => {
//       weeksInCurrentMonth.value.weeks[index] = { startDate: j.startDate, endDate: j.endDate, weekNumber: j.weekNumber };
//     });
//   }
// });
watch(
  () => state.checkCardData,
  (newCheckCardData) => {
    const weeks = weeksInCurrentMonth.value?.weeks;
    if (weeks && weeks.length) {
      weeksInCurrentMonth.value.weeks = weeks.map((j) => {
        const matchingData = newCheckCardData.find((i) => i.weekly === j.weekNumber);
        if (matchingData) {
          return {
            ...j,
            ...matchingData,
          };
        }
        return {
          startDate: j.startDate,
          endDate: j.endDate,
          weekNumber: j.weekNumber,
        };
      });
    }
  },
  { immediate: true },
);

// 监听卡片视图选择的筛选条件
watch(() => state.searchCardWeekRadio, () => {
  if (state.searchCardWeekRadio === 'CARD_LAST_WEEK') {
    weekIndexNum.value -= 1;
  }
  if (state.searchCardWeekRadio === 'CARD_WEEK') {
    weekIndexNum.value = weekIndexNumCopy.value;
  }
  if (state.searchCardWeekRadio === 'CARD_MONTH') {
    getWeeksInMonth(currentYear.value, currentMonth.value + 1);
    if (!isWeekly.value) {
      MonthFormatTime.value.setTime(dayjs().format('YYYY-MM'));
      state.tableRef.reload({ page: 1 });
      setIsWeekly();
    }
  }
});
watch(() => state.timeEmit, () => {
  if (Number(dayjs(state.timeEmit).format('YYYY')) === currentYear.value
      && Number(dayjs(state.timeEmit).format('MM')) === currentMonth.value + 1) {
    state.searchCardWeekRadio = 'CARD_MONTH';
  } else {
    state.searchCardWeekRadio = '';
  }
});
function getWeeksInMonth(year, month) {
  // 创建一个数组来存储结果
  const weeks = [];
  // 获取指定年月的第一天和最后一天
  const firstDay = new Date(year, month - 1, 1);
  const lastDay = new Date(year, month, 0);
  // 获取第一周的起始和结束日期以及周数
  const getFirstWeek = (date) => {
    const start = new Date(date);
    start.setDate(date.getDate() - date.getDay() + 1);
    const end = new Date(date);
    end.setDate(date.getDate() + (6 - date.getDay()));
    const weekNumber = getWeekNumber(start);
    return {
      startDate: start,
      endDate: end,
      weekNumber,
    };
  };

  // 获取下一周的起始和结束日期以及周数
  const getNextWeek = (date, weekNumber) => {
    const start = new Date(date);
    start.setDate(date.getDate() + 1);
    const end = new Date(date);
    end.setDate(date.getDate() + 7);
    weekNumber++;
    return {
      startDate: start,
      endDate: end,
      weekNumber,
    };
  };

  // 获取指定日期所在周是当年的第几周
  const getWeekNumber = (date) => {
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const daysOffset = firstDayOfYear.getDay() - 1;
    const firstMondayOfYear = new Date(date.getFullYear(), 0, 1 + (daysOffset > 0 ? 7 - daysOffset : 0));
    const diff = date - firstMondayOfYear;
    const weekNumber = Math.ceil(diff / (7 * 24 * 60 * 60 * 1000)); // 修正周数
    return weekNumber;
  };

  // 初始化起始日期和结束日期
  let startDate = getFirstWeek(firstDay).startDate;
  let endDate = getFirstWeek(firstDay).endDate;
  let weekNumber = getFirstWeek(firstDay).weekNumber;

  // 循环直到结束日期大于最后一天
  while (endDate <= lastDay) {
    weeks.push(getNextWeek(startDate, weekNumber));
    startDate.setDate(startDate.getDate() + 7);
    endDate.setDate(endDate.getDate() + 7);
    weekNumber++;
  }

  // 获取当前日期所在周的周数
  const currentWeekNumber = getWeekNumber(new Date()) + 1;
  startDateTime.value = dayjs(weeks[0].startDate).subtract(1, 'day').format('YYYY-MM-DD');
  endDateTime.value = dayjs(weeks[weeks.length - 1].endDate).subtract(1, 'day').format('YYYY-MM-DD');
  weeksInCurrentMonth.value = {
    weeks,
    totalWeeks: weeks.length,
    currentWeekNumber,
  };
}
// 返回卡片视图列表
function setIsWeekly() {
  isWeekly.value = true;
}
function timeChange(time) {
  state.timeEmit = time;
  getWeeksInMonth(Number(dayjs(time).format('YYYY')), Number(dayjs(time).format('MM')));
  getCarData();
}
function getCarData() {
  if (!state.isTable) {
    state.showPeopleComponent = false;
    state.timeEmit = state.timeEmit ?? dayjs().format('YYYY-MM-DD');
    const query = {
      keyword: state.keyword,
      summary: '',
      timeType: state.searchWeekRadio,
      pageType: true,
      projectIds: [route.query.id],
      startTime: state.timeEmit ? startDateTime.value : '',
      endTime: state.timeEmit ? endDateTime.value : '',
    };

    state.loading = true;
    new Api('/pms').fetch(query, 'projectWeekly/checkCard', 'POST').then((res) => {
      if (res) {
        state.checkCardData = Object.keys(res).map((key) => res[key]);
      } else {
        state.checkCardData = [];
      }
    }).finally(() => {
      state.loading = false;
    });
  } else {
    state.timeEmit = undefined;
    state.showPeopleComponent = false;
    state.tableRef.reload({ page: 1 });
  }
}

watch(() => state.timeEmit, () => {
  if (Number(dayjs(state.timeEmit).format('YYYY')) === currentYear.value
      && Number(dayjs(state.timeEmit).format('MM')) === currentMonth.value + 1) {
    state.searchCardWeekRadio = 'CARD_MONTH';
  } else {
    state.searchCardWeekRadio = '';
  }
});

// 获取搜索参数
function getParams() {
  return {
    keyword: state.keyword,
    summary: '',
    timeType: state.searchWeekRadio,
    pageType: true,
    // timeType: 'THIS_MONTH',
    projectId: route.query.id,
    ...getTime(),
  };
}

function getTime() {
  if (state.isTable) {
    return {
      startTime: state.searchTime?.length ? dayjs(dayjs(state.searchTime[0].format('YYYY-MM-DD'))).format('YYYY-MM-DD') : '',
      endTime: state.searchTime?.length ? dayjs(dayjs(state.searchTime[1].format('YYYY-MM-DD'))).format('YYYY-MM-DD') : '',
    };
  }
  return {
    startTime: state.timeEmit ? dayjs(state.timeEmit).startOf('month').format('YYYY-MM-DD') : '',
    endTime: state.timeEmit ? dayjs(state.timeEmit).endOf('month').format('YYYY-MM-DD') : '',
  };
}

function handle(type) {
  switch (type) {
    case 'add':
      state.addOrEditRef.openModal({
        action: 'add',
        info: {
          ids: state.rows.map((item) => item.id),
          type: '2',
        },
      });
      break;
    case 'edit':
      break;
  }
}

watch(() => [state.searchWeekRadio, state.searchTime], () => {
  if (state.isTable) {
    state.tableRef.reload({ page: 1 });
  } else {
    getCarData();
  }
}, { deep: true });
watch(() => [state.isTable], () => {
  // state.timeEmit = null;
  // state.searchTime = null;
  if (state.isTable) {
    state.tableRef.reload({ page: 1 });
  } else {
    getCarData();
  }
}, { deep: true });

// function getCarData() {
//   if (!state.isTable) {
//     state.showPeopleComponent = false;
//     state.timeEmit = state.timeEmit ?? dayjs().format('YYYY-MM-DD');
//     // let query = {
//     //   keyword: '',
//     //   summary: '',
//     //   // timeType: state.searchWeekRadio,
//     //   timeType: 'CUSTOM',
//     //   projectId: route.query.id,
//     //   ...getTime(),
//     // };
//     let query = getParams();
//     state.loading = true;
//     new Api('/pms').fetch(query, 'projectWeekly/checkCard', 'POST').then((res) => {
//       if (res) {
//         state.checkCardData = res;
//       } else {
//         state.checkCardData = {};
//       }
//     }).finally(() => {
//       state.loading = false;
//     });
//   } else {
//     state.timeEmit = undefined;
//     state.showPeopleComponent = false;
//     state.tableRef.reload({ page: 1 });
//   }
// }

// 判断是不是周六周日
function isWeekend(date) {
  const dayOfWeek = dayjs(date).day();
  return dayOfWeek === 0 || dayOfWeek === 6;
}

// 是不是今天
function isToday(someDate) {
  const today = dayjs();
  const inputDate = dayjs(someDate);
  return today.isSame(inputDate, 'day');
}

function goDetails() {
}
function goDetailedList(cardData, weeks) {
  if (cardData?.length) {
    isWeekly.value = false;
    cardDataArr.value = cardData;
    WeeksNumber.value = weeks;
  }
}
const getPopupContainer = (trigger: HTMLElement) => trigger.parentElement;

function updateTable() {
  state.tableRef.reload({ page: 1 });
}

function goCheckPeople(cur) {
  state.showPeopleComponent = true;
  state.allPeopleCard = cur.noSubmitted.length ? cur.noSubmitted.concat(cur.submitted ?? []) : [].concat(cur.submitted ?? []);
}

function check(id) {
  state.addOrEditRef.openModal({
    action: 'add',
    info: {
      record: { id },
      type: '1',
    },
  });
}

function alertPeople(id) {
  // new Api('/pms').fetch({ id, url: 'WeekReportDetails' }, 'projectWeekly/warn', 'GET').then(() => {
  //   message.info('操作成功');
  // });
  new Api('/pms/projectWeekly/warn').fetch({ id }, '', 'GET').then(() => {
    message.info('操作成功');
  });
}

function emitChange(data) {
  if (data?.type === 'check') {
    check(data.id);
  } else {
    alertPeople(data.id);
  }
}

// 列表选择回调
function selectionChange({ rows }) {
  state.rows = JSON.parse(JSON.stringify(rows));
}

async function downLoad() {
  let query = getParams();
  await basicDownloadByData(
    '/api/pms/projectWeekly/export',
    query,
    '',
    'POST',
    false,
    false,
  );
}

watch(() => state.rows, () => {
  if (state.rows?.length) {
    state.rows.forEach((item) => {
      if (!item.audit) {
        state.canCheck = false;
      }
    });
  } else {
    state.canCheck = false;
  }
});

function smallSearch(val) {
  if (state.isTable) {
    state.keyword = val;
    state.tableRef.reload({ page: 1 });
  } else {
    getCarData();
  }
}
</script>

<style scoped lang="less">
.popBox {
  max-height: 250px;
  overflow-y: auto;
}

.monthBoxItem {
  height: 150px;
  margin-bottom: 4px;
  margin-right: 4px;
  border-radius: 3px;
  box-sizing: border-box;
  border: 1px solid #e3e3e3;
  padding: 28px 10px 10px 10px;
  position: relative;

  &:nth-child(7n) {
    margin-right: 0;
  }

  .itemTop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: auto;
    background-color: red;
  }

  .circleCore {
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    text-align: center;
    line-height: 20px;
    right: 5px;
    top: 5px;
  }

  .topRightBtn {
    position: absolute;
    height: 20px;
    text-align: center;
    line-height: 20px;
    right: 5px;
    top: 5px;
  }

  .itemContent {
    text-align: center;
    overflow: auto;
    height: 100%;
    width: 100%;
  }
}

.weekNumber {
  position: absolute;
  height: 20px;
  line-height: 20px;
  left: 5px;
  top: 5px;
  color: #545454;
}

.fourColor {
  background-color: #faa519;
}

.threeColor {
  background-color: rgb(102, 204, 204);
}

.twoColor {
  background-color: #ccc;
}

.bgcCCC {
  background-color: #c0c0c0;
}

.currentDayBgc {
  border-color: ~`getPrefixVar('primary-color')` !important;
  border-width: 2px !important;
}
.week_cont {
  display: flex;
  flex-wrap: wrap;
  padding-right: 20px;
  margin-top: 20px;

  .weekly_cont {
    width: 48%;
    height: 200px;
    margin-right: 1%;
    border: solid 1px #ccc;
    border-radius: 10px;
    margin-bottom: 20px;
    overflow: hidden;

    .week_title {
      height: 40px;
      display: flex;
      padding: 0 10px;

      .week_title_lt {
        width: 20%;
        height: 40px;
        display: flex;
        align-items: center;
      }

      .week_title_rt {
        width: 80%;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: flex-end;

        .operate {
          width: 26px;
          height: 26px;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 50%;
        }

        .green {
          background-color: #2BCFCD;
        }

        .yellow {
          background-color: #FFC900;
        }

        .grey {
          background-color: #CCCCCC;
        }
      }
    }

    .week_list {
      height: 160px;
      padding: 10px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-evenly;
      .submitted_cont{
        width:30%;
        .submitted{
          color:#3AB43B;
          font-weight: bold;
          cursor: pointer
        }
        .notSubmitted{
          color: #FF0000;
          font-weight: bold;
          cursor: pointer
        }
      }

    }
  }
  .detail_cont{
    width: 24%;
    margin-right: 1%;
    height: 160px;
    border: solid 1px #ccc;
    border-radius: 10px;
    margin-bottom: 20px;
    overflow: hidden;
    .week_title {
      height: 28px;
      display: flex;
      padding: 0 10px;
      .week_title_lt {
        width: 20%;
        height: 28px;
        display: flex;
        align-items: center;
      }

      .week_title_rt {
        width: 80%;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: flex-end;

        .operate {
          width: 24px;
          height: 24px;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 50%;
        }

        .green {
          background-color: #2BCFCD;
        }

        .yellow {
          background-color: #FFC900;
        }

        .grey {
          background-color: #CCCCCC;
        }
      }
    }
    .week_list{
      height:132px;
      padding:10px;
      overflow-y: auto;
      .list_item_cont {
        display: flex;
        .time{
          height: 20px;
          margin-right: 10px;
          color:~`getPrefixVar('primary-color')`;
        }
        .list_item {
          height: 20px;
          flex:1;
          line-height: 20px;
          text-overflow: ellipsis; /* 溢出显示省略号 */
          overflow: hidden; /* 溢出隐藏 */
          white-space: nowrap;  /* 强制不换行 */
        }
      }
    }
  }
}
:deep(.ant-popover-inner-content){
  max-height:240px;
  overflow-y: auto;
  cursor: pointer
}
.submitted_list{
  display: flex;
  justify-content: space-between;
  .examine{
    color:~`getPrefixVar('primary-color')`;
  }
}
.mark{
  border:2px solid ~`getPrefixVar('primary-color')` !important;
}
</style>
