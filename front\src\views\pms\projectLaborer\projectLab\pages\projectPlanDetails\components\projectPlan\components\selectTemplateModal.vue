<template>
  <BasicModal
    v-model:visible="$props.visible"
    :width="'800px'"
    title="选择里程碑模板"
    :bodyStyle="{ height: '500px', overflowY: 'hidden' }"
    @register="modalRegister"
    @ok="confirm"
    @cancel="() => handleClosed"
  >
    <div class="add-body">
      <div class="flex flex-ac top-select">
        <span>里程碑模板：</span>
        <a-select
          ref="select"
          v-model:value="templateId"
          style="width: 140px"
          class="table-input"
          placeholder="请选择模板名称"
          :options="menus"
          @change="(value) => onChangeValue(value)"
        />
      </div>
      <div class="table-box">
        <OrionTable
          ref="tableRef"
          :options="tableOptions"
        >
          <template #name="{ record }">
            <div
              class="flex-te"
              :title="record.name"
            >
              <Icon
                class="lichengbei-icon"
                :size="18"
                icon="sie-icon-lichengbei"
              />
              {{ record.nodeName }}
            </div>
          </template>
        </OrionTable>
      </div>
    </div>
  </BasicModal>
</template>
<script lang="ts">
import {
  defineComponent, ref, nextTick,
} from 'vue';
import { Select } from 'ant-design-vue';
import {
  OrionTable,
  BasicModal,
  useModalInner,
  Icon,
} from 'lyra-component-vue3';
import {
  postProjectSchemeMilestoneTemplatelist,
  postProjectSchemeMilestoneNodePages,
} from '/@/views/pms/projectLaborer/projectLab/api';
export default defineComponent({
  name: 'SelectTemplateModal',
  components: {
    OrionTable,
    ASelect: Select,
    BasicModal,
    Icon,
  },
  props: {

    visible: {
      type: Boolean,
      default: false,
    },

  },
  emits: [
    'handleColse',
    'updateForm',
    'confirm',
  ],
  setup(props, { emit }) {
    const tableRef = ref();
    const selectedRowKeys = ref([]);
    const templateId = ref<string>('');
    const menus = ref<any>([]);
    const tableOptions = {
      rowSelection: {},
      showToolButton: false,
      showTableSetting: false,
      immediate: false,
      showSmallSearch: false,
      showIndexColumn: false,
      deleteToolButton: 'add|delete|enable|disable',
      api: (params) => postProjectSchemeMilestoneNodePages({
        ...params,
        query: {
          templateId: templateId.value,
        },
      }),
      columns: [
        {
          title: '里程碑节点名称',
          dataIndex: 'nodeName',
          slots: { customRender: 'name' },
        },
        {
          title: '描述说明',
          dataIndex: 'description',
        },
      ],
    };

    // 获取左侧菜单
    async function getMenus() {
      try {
        menus.value = await postProjectSchemeMilestoneTemplatelist();
        menus.value = menus.value.map((item) => ({
          ...item,
          label: item.templateName,
          value: item.id,
        }));
      } finally {
      }
    }

    const [modalRegister, { closeModal, setModalProps }] = useModalInner(
      (rowData) => {
        getMenus();
      },
    );

    const handleClosed = () => {
      tableRef.value.setTableData([]);
      templateId.value = '';
      closeModal();
      emit('handleColse');
    };
    const onChangeValue = (value) => {
      templateId.value = value;
      updateTable();
    };

    // 刷新表格数据
    function updateTable() {
      nextTick(() => {
        tableRef?.value?.reload();
      });
    }
    function confirm() {
      let selectRow = tableRef.value.getSelectRows();
      tableRef.value.setTableData([]);
      templateId.value = '';
      closeModal();
      emit('confirm', selectRow);
    }

    return {
      tableRef,
      tableOptions,
      onChangeValue,
      selectedRowKeys,
      modalRegister,
      handleClosed,
      menus,
      templateId,
      updateTable,
      confirm,
    };
  },
});
</script>
<style lang="less" scoped>
.add-body {
  :deep(.surely-table-center-container) {
    .surely-table-header-cell:nth-of-type(3),
    .surely-table-header-cell:nth-of-type(5),
    .surely-table-header-cell:nth-of-type(6),
    .surely-table-header-cell:nth-of-type(7),
    .surely-table-header-cell:nth-of-type(8),
    .surely-table-header-cell:nth-of-type(9) {
      .surely-table-header-cell-title-inner .header-column-wrap .flex-f1 {
        &::before {
          display: inline-block;
          margin-right: 4px;
          color: #ff4d4f;
          font-size: 14px;
          font-family: SimSun, sans-serif;
          line-height: 1;
          content: '*';
        }
      }
    }
  }
}
.table-box {
  height:500px;
  overflow: hidden;
}
.top-select {
  padding:20px;
  padding-bottom: 0px;
}
    .lichengbei-icon {
      color:#FFB119;
    }
</style>
