package com.chinasie.orion.domain.dto.file;

import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-20
 */
public class ObjectDTO implements Serializable {

    @ApiModelProperty(value = "id")
    private String id;//
    @ApiModelProperty(value = "类名称")
    private String className;//
    @ApiModelProperty(value = "类名称（中文）")
    private String cnClass;
    @ApiModelProperty(value = "创建人")
    private String creatorId;
    @ApiModelProperty(value = "创建人名字")
    private String creatorName;
    @ApiModelProperty(value = "拥有者")
    private String ownerId;
    @ApiModelProperty(value = "拥有者名字")
    private String ownerName;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "修改人")
    private String modifyId;
    @ApiModelProperty(value = "修改人名字")
    private String modifyName;
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "状态")
    private Integer status;
    /**
     * 名称
     */
    @NotEmpty(message = "名称不能为空")
    @ApiModelProperty(value = "名称")
    private String name;
    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    private String number;

    /**
     * 保密期限
     */
    @ApiModelProperty(value = "保密期限")
    private String securityLimit;

    /**
     * 密级
     */
    @ApiModelProperty(value = "密级")
    private String secretLevel;

    /**
     * 密级名称
     */
    @ApiModelProperty(value = "密级名称")
    private String secretLevelName;

    /**
     * 状态对象
     */
    @ApiModelProperty(value = "状态对象")
    private DataStatusVO dataStatus;

    /**
     * 显示顺序
     */
    @ApiModelProperty(value = "显示顺序")
    private Integer sort;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public String getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifyId() {
        return modifyId;
    }

    public void setModifyId(String modifyId) {
        this.modifyId = modifyId;
    }

    public String getModifyName() {
        return modifyName;
    }

    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getSecurityLimit() {
        return securityLimit;
    }

    public void setSecurityLimit(String securityLimit) {
        this.securityLimit = securityLimit;
    }

    public String getSecretLevel() {
        return secretLevel;
    }

    public void setSecretLevel(String secretLevel) {
        this.secretLevel = secretLevel;
    }

    public String getSecretLevelName() {
        return secretLevelName;
    }

    public void setSecretLevelName(String secretLevelName) {
        this.secretLevelName = secretLevelName;
    }

    public DataStatusVO getDataStatus() {
        return dataStatus;
    }

    public void setDataStatus(DataStatusVO dataStatus) {
        this.dataStatus = dataStatus;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getCnClass() {
        return cnClass;
    }

    public void setCnClass(String cnClass) {
        this.cnClass = cnClass;
    }
}
