<template>
  <Layout :options="{ body: { scroll: true } }">
    <orion-table
      ref="btnsTableRef"
      :options="options"
    />
  </Layout>
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
import { Input, Button } from 'ant-design-vue';
import {
  BasicTable, Layout, useActionsRecord, OrionTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer,
} from 'lyra-component-vue3';
// import Layout from '/@/components/Layout';
// import OrionTable from '/@/components/OrionTable';
import { workflowApi } from '../util/apiConfig';
export default defineComponent({
  name: 'FlowBtns',
  components: {
    Layout,
    OrionTable,
    Input,
    AButton: Button,
  },
  setup() {
    const state = reactive({
      options: {
        rowSelection: {},
        deleteToolButton: 'enable|disable',
        auto: {
          url: `${workflowApi}/act-btn`,
          params: {
            query: {
              search: '',
              code: '',
            },
          },
          form: {
            labelWidth: 120,
            actionColOptions: {
              span: 24,
            },
            schemas: [
              {
                field: 'name',
                component: 'Input',
                label: '名称',
                colProps: { span: 24 },
                defaultValue: '',
                rules: [
                  {
                    type: 'string',
                    required: true,
                  },
                  {
                    max: 10,
                    message: '长度不超过10位',
                  },
                ],
                componentProps: {
                  placeholder: '请输入名称',
                  allowClear: true,
                  maxlength: 10,
                },
              },
              {
                field: 'code',
                component: 'Input',
                label: '编码',
                colProps: { span: 24 },
                defaultValue: '',
                rules: [
                  {
                    type: 'string',
                    required: true,
                  },
                ],
                componentProps: {
                  placeholder: '请输入编码',
                  allowClear: true,
                  maxlength: 10,
                },
              },
              {
                field: 'sort',
                component: 'InputNumber',
                label: '排序',
                colProps: { span: 24 },
                rules: [{ type: 'number' }],
                componentProps: {
                  min: 0,
                },
              },
            ],
          },
        },
        columns: [
          {
            title: '名称',
            dataIndex: 'name',
          },
          {
            title: '编码',
            dataIndex: 'code',
          },
          {
            title: '排序',
            dataIndex: 'sort',
          },
        ],
      },
    });

    return {
      ...toRefs(state),
    };
  },
});
</script>
<style scoped lang="less">
  .btns-header {
    display: flex;
    flex-direction: row;
  }
</style>
