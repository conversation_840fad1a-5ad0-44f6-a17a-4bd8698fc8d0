package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;

/**
 * IdeaForm Entity对象
 *
 * <AUTHOR>
 * @since 2024-01-29 22:08:44
 */
@TableName(value = "pmsx_idea_form")
@ApiModel(value = "IdeaFormEntity对象", description = "意见单")
@Data
public class IdeaForm extends ObjectEntity implements Serializable {

    /**
     * 接口类型
     */
    @ApiModelProperty(value = "单据类型")
    @TableField(value = "form_type")
    private String formType;

    /**
     * 发布部门
     */
    @ApiModelProperty(value = "发布部门")
    @TableField(value = "publish_dept_id")
    private String publishDeptId;

    /**
     * 接受部门
     */
    @ApiModelProperty(value = "接受部门")
    @TableField(value = "review_dept_ids")
    private String reviewDeptIds;

    /**
     * 回复时间
     */
    @ApiModelProperty(value = "回复时间")
    @TableField(value = "reply_time")
    private Date replyTime;

    /**
     * 主办人
     */
    @ApiModelProperty(value = "主办人")
    @TableField(value = "man_user")
    private String manUser;

    /**
     * 第三方检查备案
     */
    @ApiModelProperty(value = "第三方检查备案")
    @TableField(value = "third_verify")
    private String thirdVerify;

    /**
     * 专业代码
     */
    @ApiModelProperty(value = "专业代码")
    @TableField(value = "specialty_code")
    private String specialtyCode;

    /**
     * 回复意见描述
     */
    @ApiModelProperty(value = "回复意见描述")
    @TableField(value = "`desc`")
    private String desc;

    /**
     * 回复意见
     */
    @ApiModelProperty(value = "回复意见")
    @TableField(value = "reply_suggest")
    private String replySuggest;


    @ApiModelProperty(value = "传递单ID/接口ID")
    @TableField(value = "interface_id")
    private String interfaceId;

    /**
     * 流水号
     */
    @ApiModelProperty(value = "流水号")
    @TableField(value = "number")
    private String number;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    private String projectId;

    @ApiModelProperty(value = "数据Id (项目id,产品Id)")
    @TableField(value = "data_id")
    private String dataId;

    @ApiModelProperty(value = "数据类型className")
    @TableField(value = "data_class_name")
    private String dataClassName;
}
