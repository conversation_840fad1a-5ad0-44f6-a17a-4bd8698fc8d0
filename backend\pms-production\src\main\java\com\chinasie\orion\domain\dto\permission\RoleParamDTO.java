package com.chinasie.orion.domain.dto.permission;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/07/31/13:34
 * @description:
 */
@Data
public class RoleParamDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Size(min = 1, message = "角色code不能为空")
    private List<String> roleCodeList;
    @NotEmpty(message = "大修轮次不能为空")
    private String majorRepairTurn;
}
