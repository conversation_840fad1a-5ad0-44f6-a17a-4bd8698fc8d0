package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.bo.CodeBo;
import com.chinasie.orion.constant.ClassNameConstant;
import com.chinasie.orion.conts.BudgetEnum;
import com.chinasie.orion.conts.BudgetStatusEnum;
import com.chinasie.orion.domain.dto.BudgetApplicationFormDTO;
import com.chinasie.orion.domain.dto.BudgetExpendFormDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.BudgetApplicationVO;
import com.chinasie.orion.domain.vo.BudgetExpendFormVO;
import com.chinasie.orion.domain.vo.CostCenterVO;
import com.chinasie.orion.domain.vo.ExpenseSubjectMsgVO;
import com.chinasie.orion.number.api.domain.GenerateNumberRequest;
import com.chinasie.orion.number.api.sdk.NumberApiService;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.BudgetExpendFormMapper;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;




/**
 * <p>
 * BudgetExpendForm 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:07
 */
@Service
@Slf4j
public class BudgetExpendFormServiceImpl extends OrionBaseServiceImpl<BudgetExpendFormMapper, BudgetExpendForm> implements BudgetExpendFormService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private BudgetManagementService budgetManagementService;

    @Autowired
    private BudgetExpendService budgetExpendService;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private CodeBo codeBo;


    @Autowired
    private BudgetRecordService budgetRecordService;

    @Resource
    private CostCenterDataService costCenterService;

    @Resource
    private ProjectService projectService;

    @Autowired
    private NumberApiService numberApiService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public BudgetExpendFormVO detail(String id, String pageCode) throws Exception {
        BudgetExpendForm budgetExpendForm =this.getById(id);
        BudgetExpendFormVO result = BeanCopyUtils.convertTo(budgetExpendForm,BudgetExpendFormVO::new);
        setEveryName(Collections.singletonList(result));
        Project project =  projectService.getById(result.getProjectId());
        result.setProjectName(project.getName());
        result.setName(result.getNumber());
        return result;
    }

    /**
     *  新增
     *
     * * @param budgetExpendFormDTO
     */
    @Override
    public  String create(BudgetExpendFormDTO budgetExpendFormDTO) throws Exception {
        BudgetExpendForm budgetExpendForm =BeanCopyUtils.convertTo(budgetExpendFormDTO,BudgetExpendForm::new);
        GenerateNumberRequest request = new GenerateNumberRequest();
        request.setClazzName(ClassNameConstant.BUDGET_EXPEND_FORM);
        String number =  numberApiService.generate(request);
        budgetExpendForm.setNumber(number);
        budgetExpendForm.setOccupationReceive("2");
        this.save(budgetExpendForm);
        saveBudgetExpend(budgetExpendForm);
        String rsp=budgetExpendForm.getId();
        return rsp;
    }


    public void saveBudgetExpend(BudgetExpendForm budgetExpendForm){
        LambdaQueryWrapperX<BudgetManagement> lambdaQueryWrapperX= new LambdaQueryWrapperX<>(BudgetManagement.class);
        lambdaQueryWrapperX.eq(BudgetManagement::getCostCenterId,budgetExpendForm.getCostCenterId());
        lambdaQueryWrapperX.eq(BudgetManagement::getExpenseSubjectNumber,budgetExpendForm.getExpenseAccountNumber());
        lambdaQueryWrapperX.eq(BudgetManagement::getProjectId,budgetExpendForm.getProjectId());
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy");
        lambdaQueryWrapperX.in(BudgetManagement::getBudgetTime,"entirety",dateFormat.format(budgetExpendForm.getOccurrenceTime()));
        lambdaQueryWrapperX.orderByAsc(BudgetManagement::getCreateTime);
        lambdaQueryWrapperX.eq(BudgetManagement::getProjectId,budgetExpendForm.getProjectId());
        List<BudgetManagement> list = budgetManagementService.list(lambdaQueryWrapperX);
        BigDecimal expendMoney =  budgetExpendForm.getExpendMoney();
        List<BudgetExpend> budgetExpends = new ArrayList<>();
        if(CollUtil.isNotEmpty(list)){
            int count = 1;
            for(BudgetManagement budgetManagement:list){
                BudgetExpend budgetExpend = new BudgetExpend();
                budgetExpend.setFormId(budgetExpendForm.getId());
                budgetExpend.setProjectId(budgetExpendForm.getProjectId());
                budgetExpend.setSort(count);
                budgetExpend.setBudgetId(budgetManagement.getId());
                if(budgetManagement.getResidueMoney().compareTo(expendMoney)>=0){
                    budgetExpend.setExpendMoney(expendMoney);
                    budgetExpend.setResidueMoney(budgetManagement.getResidueMoney().subtract(expendMoney));
                    expendMoney = BigDecimal.ZERO;
                }else{
                    if(list.size() > count){
                        if(budgetManagement.getResidueMoney().compareTo(expendMoney)<0){
                            budgetExpend.setExpendMoney(budgetManagement.getResidueMoney());
                            budgetExpend.setResidueMoney(BigDecimal.ZERO);
                            expendMoney=expendMoney.subtract(budgetManagement.getResidueMoney());
                        }
                    }else {
                        if (budgetManagement.getResidueMoney().compareTo(expendMoney) < 0) {
                            budgetExpend.setExpendMoney(budgetManagement.getResidueMoney());
                            budgetExpend.setResidueMoney(budgetManagement.getResidueMoney().subtract(expendMoney));
                        }
                    }
                }
                count++;
                budgetExpends.add(budgetExpend);
            }
        }
        budgetExpendService.saveBatch(budgetExpends);
    }
    /**
     *  编辑
     *
     * * @param budgetExpendFormDTO
     */
    @Override
    public Boolean edit(BudgetExpendFormDTO budgetExpendFormDTO) throws Exception {
        BudgetExpendForm budgetExpendForm =BeanCopyUtils.convertTo(budgetExpendFormDTO,BudgetExpendForm::new);
        this.updateById(budgetExpendForm);
        budgetExpendService.remove(new LambdaQueryWrapperX<>(BudgetExpend.class).eq(BudgetExpend::getFormId,budgetExpendFormDTO.getId()));
        saveBudgetExpend(budgetExpendForm);
        String rsp=budgetExpendForm.getId();
        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        budgetExpendService.remove(new LambdaQueryWrapperX<>(BudgetExpend.class).in(BudgetExpend::getFormId,ids));
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<BudgetExpendFormVO> pages( Page<BudgetExpendFormDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<BudgetExpendForm> condition = new LambdaQueryWrapperX<>( BudgetExpendForm. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(BudgetExpendForm::getCreateTime);


        Page<BudgetExpendForm> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), BudgetExpendForm::new));
        if(ObjectUtil.isNotEmpty(pageRequest.getQuery())){
            BudgetExpendFormDTO budgetExpendFormDTO = pageRequest.getQuery();
            condition.eqIfPresent(BudgetExpendForm::getProjectId,budgetExpendFormDTO.getProjectId());
        }
        PageResult<BudgetExpendForm> page = this.getBaseMapper().selectPage(realPageRequest, condition);


        Page<BudgetExpendFormVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<BudgetExpendFormVO> vos = BeanCopyUtils.convertListTo(page.getContent(), BudgetExpendFormVO::new);
        if(CollUtil.isEmpty(vos)){
            return pageResult;
        }
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "预算支出表单导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", BudgetExpendFormDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            BudgetExpendFormExcelListener excelReadListener = new BudgetExpendFormExcelListener();
        EasyExcel.read(inputStream,BudgetExpendFormDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<BudgetExpendFormDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("预算支出表单导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<BudgetExpendForm> budgetExpendFormes =BeanCopyUtils.convertListTo(dtoS,BudgetExpendForm::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::BudgetExpendForm-import::id", importId, budgetExpendFormes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<BudgetExpendForm> budgetExpendFormes = (List<BudgetExpendForm>) orionJ2CacheService.get("pmsx::BudgetExpendForm-import::id", importId);
        log.info("预算支出表单导入的入库数据={}", JSONUtil.toJsonStr(budgetExpendFormes));

        this.saveBatch(budgetExpendFormes);
        orionJ2CacheService.delete("pmsx::BudgetExpendForm-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::BudgetExpendForm-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<BudgetExpendForm> condition = new LambdaQueryWrapperX<>( BudgetExpendForm. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(BudgetExpendForm::getCreateTime);
        List<BudgetExpendForm> budgetExpendFormes =   this.list(condition);

        List<BudgetExpendFormDTO> dtos = BeanCopyUtils.convertListTo(budgetExpendFormes, BudgetExpendFormDTO::new);

        String fileName = "预算支出表单数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", BudgetExpendFormDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<BudgetExpendFormVO> vos)throws Exception {
        List<String> costCenterIds = vos.stream().map(BudgetExpendFormVO::getCostCenterId).collect(Collectors.toList());
        List<CostCenterVO> costCenterVOS = costCenterService.getList(costCenterIds);
        Map<String, String> costCenterMap = costCenterVOS.stream().collect(Collectors.toMap(CostCenterVO::getId, CostCenterVO::getName));

        vos.forEach(vo->{
            if(StrUtil.isNotBlank(costCenterMap.get(vo.getCostCenterId()))){
                vo.setCostCenterName(costCenterMap.get(vo.getCostCenterId()));
            }
            if(StrUtil.isNotBlank(vo.getOccurrencePerson())){
               UserVO userVO = userRedisHelper.getUserById(vo.getOccurrencePerson());
               if(ObjectUtil.isNotEmpty(userVO)){
                   vo.setOccurrenceName(userVO.getName());
               }
            }
        });


    }

    @Override
    public void changBudget(String id) throws Exception {
        BudgetExpendForm budgetExpendForm = this.getById(id);
        CurrentUserHelper.setAttributes(budgetExpendForm.getPlatformId(), budgetExpendForm.getOrgId());
        List<BudgetExpend> budgetExpends = budgetExpendService.list(new LambdaQueryWrapperX<>(BudgetExpend.class)
                .eq(BudgetExpend::getFormId, id));
        List<String> ids = budgetExpends.stream().map(BudgetExpend::getBudgetId).collect(Collectors.toList());
        List<BudgetManagement> budgetManagements = budgetManagementService.list(new LambdaQueryWrapper<>(BudgetManagement.class)
                .in(BudgetManagement::getId, ids));

        Map<String, BudgetManagement> budgetMap = budgetManagements.stream()
                .collect(Collectors.toMap(
                        BudgetManagement::getId,
                        Function.identity()
                ));
        List<BudgetManagement> updateList = new ArrayList<>();
        List<BudgetRecord> budgetRecords = new ArrayList<>();
        for (BudgetExpend budgetExpend : budgetExpends) {
            BudgetManagement budgetManagement = budgetMap.get(budgetExpend.getBudgetId());
            if (ObjectUtil.isNotEmpty(budgetManagement)) {
                BudgetRecord budgetRecord = new BudgetRecord();
                budgetRecord.setBudgetId(budgetManagement.getId());
                budgetRecord.setBudgetChangeId(budgetExpendForm.getId());
                budgetRecord.setChangeMoney(budgetExpend.getExpendMoney());
                budgetRecord.setBudgetChangeName(budgetExpendForm.getNumber());
                budgetRecord.setBudgetChangeNumber(budgetExpendForm.getNumber());
                budgetRecord.setBudgetChangeType(BudgetEnum.EXPEND.getCode());
                budgetRecord.setOperationTime(new Date());
                budgetRecord.setOperationPerson(budgetExpend.getCreatorId());
                budgetManagement.setResidueMoney(budgetManagement.getResidueMoney().subtract(budgetExpend.getExpendMoney()));
                updateList.add(budgetManagement);
                budgetRecords.add(budgetRecord);
            }
        }
        budgetManagementService.updateBatchById(updateList);
        budgetRecordService.saveBatch(budgetRecords);
    }


    public static class BudgetExpendFormExcelListener extends AnalysisEventListener<BudgetExpendFormDTO> {

        private final List<BudgetExpendFormDTO> data = new ArrayList<>();

        @Override
        public void invoke(BudgetExpendFormDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }
        public List<BudgetExpendFormDTO> getData() {
            return data;
        }
    }

    public BigDecimal getMaterialsResidueMoney(String projectId) throws Exception {
        List<ExpenseSubjectMsgVO> cl = costCenterService.getExpenseSubjectIds("材料费");
        BigDecimal clBudgetMoney = BigDecimal.ZERO;
        BigDecimal clExpendMoney = BigDecimal.ZERO;

        if (CollectionUtil.isNotEmpty(cl)) {
            List<String> clNumbers = cl.stream().map(ExpenseSubjectMsgVO::getNumber).collect(Collectors.toList());
            BudgetManagement budgetManagement = budgetManagementService.getOne(new LambdaQueryWrapperX<>(BudgetManagement.class)
                    .selectSum(BudgetManagement::getBudgetMoney)
                    .eq(BudgetManagement::getProjectId, projectId)
                    .in(BudgetManagement::getExpenseSubjectNumber, clNumbers)
                    .last("limit 1"));
            if(ObjectUtil.isNotEmpty(budgetManagement)) {
                clBudgetMoney  = budgetManagement.getBudgetMoney();
            }
            BudgetExpendForm budgetExpendForm = this.getOne(new LambdaQueryWrapperX<>(BudgetExpendForm.class)
                    .eq(BudgetExpendForm::getProjectId, projectId)
                    .selectSum(BudgetExpendForm::getExpendMoney)
                    .eq(BudgetExpendForm::getStatus, BudgetStatusEnum.COMPLETED.getCode())
                    .in(BudgetExpendForm::getExpenseAccountNumber, clNumbers)
                    .last("limit 1"));
            if(ObjectUtil.isNotEmpty(budgetExpendForm)) {
                clExpendMoney  = budgetExpendForm.getExpendMoney();
            }
        }
        return clBudgetMoney.subtract(clExpendMoney);
    }

}
