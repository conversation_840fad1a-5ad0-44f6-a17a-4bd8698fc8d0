// ************************************************ //
// ******************费用管理相关请求***************** //
// ************************************************ //

import Api from '/@/api';

/**
 * 获取成本中心分页
 * @param
 *
 */
export async function getCostCenterList(params) {
  return new Api('/pms/costCenter/page').fetch(params, '', 'post');
}
/**
 * 获取费用科目分页
 * @param
 *
 */
export async function getExpenseAccount(params) {
  return new Api('/pms/expenseSubject/tree/page').fetch(params, '', 'post');
}

/**
 * 获取预算编制分页
 * @param
 *
 */
export async function getBudgeting(params) {
  return new Api('/pms/project-budget/getPage').fetch(params, '', 'post');
}

/**
 * 获取成本执行分页
 * @param
 *
 */
export async function getCostExecute(params) {
  return new Api('/pms/project-cost/getPage').fetch(params, '', 'post');
}
/**
 * 获取成本执行发生预算弹窗分页
 * @param
 *
 */
export async function getCostExecuteBudget(params) {
  return new Api('/pms/project-budget/page').fetch(params, '', 'post');
}
/**
 * 获取预算执行发生预算弹窗分页
 * @param
 *
 */
export async function getBudgetExecute(params) {
  return new Api('/pms/project-budget/getPage').fetch(params, '', 'post');
}
/**
 * 获取预算执行详情弹窗
 * @param
 *
 */
export async function getBudgetExecuteDetails(params) {
  return new Api('/pms/project-cost/detail/getPage').fetch(params, '', 'post');
}
