package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.page.SearchCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;

import java.io.Serializable;

import java.math.BigDecimal;
import java.lang.String;
import java.lang.Integer;
import java.util.List;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProjectFullSizeReport DTO对象
 *
 * <AUTHOR>
 * @since 2024-11-18 09:38:50
 */
@ApiModel(value = "ProjectFullSizeReportDTO对象", description = "项目全口径报表")
@Data
@ExcelIgnoreUnannotated
public class ProjectFullSizeReportDTO extends ObjectDTO implements Serializable {

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @ExcelProperty(value = "项目ID ", index = 0)
    private String projectId;

    /**
     * 项目编码
     */
    @ApiModelProperty(value = "项目编码")
    @ExcelProperty(value = "项目编码 ", index = 1)
    private String projectNumber;

    /**
     * 集团内外
     */
    @ApiModelProperty(value = "集团内外")
    @ExcelProperty(value = "集团内外 ", index = 2)
    private String internalExternal;

    /**
     * 核电
     */
    @ApiModelProperty(value = "核电")
    @ExcelProperty(value = "核电 ", index = 3)
    private String nuclearPower;

    /**
     * 基地
     */
    @ApiModelProperty(value = "基地")
    @ExcelProperty(value = "基地 ", index = 4)
    private String base;

    /**
     * 业务分类
     */
    @ApiModelProperty(value = "业务分类")
    @ExcelProperty(value = "业务分类 ", index = 5)
    private String BusinessClassification;

    /**
     * 营业收入
     */
    @ApiModelProperty(value = "营业收入")
    @ExcelProperty(value = "营业收入 ", index = 6)
    private BigDecimal operatingIncome;

    /**
     * 直接采购成本
     */
    @ApiModelProperty(value = "直接采购成本")
    @ExcelProperty(value = "直接采购成本 ", index = 7)
    private BigDecimal directPurchaseCost;

    /**
     * 直接差旅成本
     */
    @ApiModelProperty(value = "直接差旅成本")
    @ExcelProperty(value = "直接差旅成本 ", index = 8)
    private BigDecimal directTravelCost;

    /**
     * 人工成本
     */
    @ApiModelProperty(value = "人工成本")
    @ExcelProperty(value = "人工成本 ", index = 9)
    private BigDecimal laborCost;

    /**
     * 技术配置
     */
    @ApiModelProperty(value = "技术配置")
    @ExcelProperty(value = "技术配置 ", index = 10)
    private BigDecimal technicalConfiguration;

    /**
     * 项目直接成本毛利
     */
    @ApiModelProperty(value = "项目直接成本毛利")
    @ExcelProperty(value = "项目直接成本毛利 ", index = 11)
    private BigDecimal projectDirectCostGross;

    /**
     * 项目直接成本毛利利率
     */
    @ApiModelProperty(value = "项目直接成本毛利利率")
    @ExcelProperty(value = "项目直接成本毛利利率 ", index = 12)
    private BigDecimal projectDirectCostGrossMargin;

    /**
     * 日常行政管理费
     */
    @ApiModelProperty(value = "日常行政管理费")
    @ExcelProperty(value = "日常行政管理费 ", index = 13)
    private BigDecimal dailyAdministrativeExpenses;

    /**
     * 设备/软件使用费
     */
    @ApiModelProperty(value = "设备/软件使用费")
    @ExcelProperty(value = "设备/软件使用费 ", index = 14)
    private BigDecimal softwareUsageFee;

    /**
     * 税金及附加
     */
    @ApiModelProperty(value = "税金及附加")
    @ExcelProperty(value = "税金及附加 ", index = 15)
    private BigDecimal taxeSurcharge;

    /**
     * 项目毛利
     */
    @ApiModelProperty(value = "项目毛利")
    @ExcelProperty(value = "项目毛利 ", index = 16)
    private BigDecimal projectGrossProfit;

    /**
     * 项目毛利率
     */
    @ApiModelProperty(value = "项目毛利率")
    @ExcelProperty(value = "项目毛利率 ", index = 17)
    private BigDecimal projectGrossMargin;

    /**
     * 管理费
     */
    @ApiModelProperty(value = "管理费")
    @ExcelProperty(value = "管理费 ", index = 18)
    private BigDecimal managementFee;

    /**
     * 项目利润
     */
    @ApiModelProperty(value = "项目利润")
    @ExcelProperty(value = "项目利润 ", index = 19)
    private BigDecimal projectProfit;

    /**
     * 项目利润率
     */
    @ApiModelProperty(value = "项目利润率")
    @ExcelProperty(value = "项目利润率 ", index = 20)
    private BigDecimal projectProfitMargin;

    /**
     * 年度
     */
    @ApiModelProperty(value = "年度")
    @ExcelProperty(value = "年度 ", index = 21)
    private Integer year;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    @ExcelProperty(value = "项目名称 ", index = 22)
    private String projectName;


    @ApiModelProperty(value = "WBS所属专业中心")
    private String wbsExpertiseCenter;

    @ApiModelProperty(value = "公司Id")
    private String companyId;

    @ApiModelProperty(value = "项目全口径类型")
    private String type;

    @ApiModelProperty(value = "查询条件")
    List<List<SearchCondition>> searchConditions;


}
