package com.chinasie.orion.domain.vo;

import com.chinasie.orion.domain.entity.ContractMilestone;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.util.List;
/**
 * MilestoneIncomeAllocation VO对象
 *
 * <AUTHOR>
 * @since 2025-01-07 10:50:47
 */
@ApiModel(value = "MilestoneIncomeAllocationVO对象", description = "里程碑收入分配表")
@Data
public class MilestoneIncomeAllocationVO extends  ObjectVO   implements Serializable{

            /**
         * 合同id
         */
        @ApiModelProperty(value = "合同id")
        private String contractId;


        /**
         * 里程碑id
         */
        @ApiModelProperty(value = "里程碑id")
        private String milestoneId;

        @ApiModelProperty(value = "合同附件")
        private List<FileVO> fileList;

        @ApiModelProperty(value = "里程碑")
        private  List<ContractMilestoneVO> contractMilestoneVOS;



    

}
