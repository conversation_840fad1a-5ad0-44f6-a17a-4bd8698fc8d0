package com.chinasie.orion.service.impl.approval;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.bo.ProjectProperties;
import com.chinasie.orion.domain.dto.approval.ProjectApprovalIncomeDTO;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalEstimateExpenseSubject;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalIncome;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalIncomeVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.approval.ProjectApprovalIncomeMapper;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.approval.ProjectApprovalEstimateExpenseSubjectService;
import com.chinasie.orion.service.approval.ProjectApprovalIncomeService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;

import java.lang.String;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;




/**
 * <p>
 * ProjectApprovalIncome 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-14 14:11:17
 */
@Service
@Slf4j
public class ProjectApprovalIncomeServiceImpl extends OrionBaseServiceImpl<ProjectApprovalIncomeMapper, ProjectApprovalIncome> implements ProjectApprovalIncomeService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private ProjectApprovalEstimateExpenseSubjectService projectApprovalEstimateExpenseSubjectService;

    @Autowired
    private DictRedisHelper dictRedisHelper;

    @Autowired
    private ProjectProperties projectProperties;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProjectApprovalIncomeVO detail(String id, String pageCode) throws Exception {
        ProjectApprovalIncome projectApprovalIncome =this.getById(id);
        ProjectApprovalIncomeVO result = BeanCopyUtils.convertTo(projectApprovalIncome,ProjectApprovalIncomeVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param projectApprovalIncomeDTO
     */
    @Override
    public  String create(ProjectApprovalIncomeDTO projectApprovalIncomeDTO) throws Exception {
        if (StrUtil.isBlank(projectApprovalIncomeDTO.getProjectApprovalId())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "项目立项id不能为空");
        }
        ProjectApprovalIncome projectApprovalIncome =BeanCopyUtils.convertTo(projectApprovalIncomeDTO,ProjectApprovalIncome::new);
        this.save(projectApprovalIncome);

        String rsp=projectApprovalIncome.getId();



        return rsp;
    }


    /**
     *  编辑
     *
     * * @param projectApprovalIncomeDTO
     */
    @Override
    public Boolean edit(ProjectApprovalIncomeDTO projectApprovalIncomeDTO) throws Exception {
        ProjectApprovalIncome projectApprovalIncome =BeanCopyUtils.convertTo(projectApprovalIncomeDTO,ProjectApprovalIncome::new);
        this.updateById(projectApprovalIncome);

        String rsp=projectApprovalIncome.getId();


        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectApprovalIncomeVO> pages( Page<ProjectApprovalIncomeDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProjectApprovalIncome> condition = new LambdaQueryWrapperX<>( ProjectApprovalIncome. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        if (ObjectUtil.isNotEmpty(pageRequest.getQuery()) && StrUtil.isNotBlank(pageRequest.getQuery().getProjectApprovalId())) {
            condition.eq(ProjectApprovalIncome::getProjectApprovalId, pageRequest.getQuery().getProjectApprovalId());
        }
        condition.orderByDesc(ProjectApprovalIncome::getCreateTime);


        Page<ProjectApprovalIncome> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectApprovalIncome::new));

        PageResult<ProjectApprovalIncome> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectApprovalIncomeVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectApprovalIncomeVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectApprovalIncomeVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }



    @Override
    public void  setEveryName(List<ProjectApprovalIncomeVO> vos)throws Exception {
        if (CollectionUtil.isNotEmpty(vos)) {
            String projectApprovalId = vos.get(0).getProjectApprovalId();
            List<ProjectApprovalIncome> list = this.list(new LambdaQueryWrapperX<>(ProjectApprovalIncome.class)
                .eq(ProjectApprovalIncome::getProjectApprovalId, projectApprovalId));
            Map<String, Optional<Date>> minExpectedContractYearMap = list.stream()
                    .collect(Collectors.groupingBy(ProjectApprovalIncome::getProductId, Collectors.mapping(ProjectApprovalIncome::getExpectedContractYear, Collectors.minBy(Date::compareTo))));

            BigDecimal materialFee;
            List<ProjectApprovalEstimateExpenseSubject> projectApprovalEstimateExpenseSubjectList = projectApprovalEstimateExpenseSubjectService.list(new LambdaQueryWrapperX<>(ProjectApprovalEstimateExpenseSubject.class)
                    .eq(ProjectApprovalEstimateExpenseSubject::getProjectApprovalId, projectApprovalId));
            //材料费
            Map<String, BigDecimal> amountByNumberMap = projectApprovalEstimateExpenseSubjectList.stream().filter(f -> ObjectUtil.isNotEmpty(f.getAmount()))
                    .collect(Collectors.toMap(ProjectApprovalEstimateExpenseSubject::getNumber, ProjectApprovalEstimateExpenseSubject::getAmount));
            DictValueVO materialFeeInfo = dictRedisHelper.getDictValueInfoByCode(projectProperties.getDictValueMaterialFee());
            if (ObjectUtil.isNotEmpty(materialFeeInfo) && StrUtil.isNotBlank(materialFeeInfo.getValue())) {
                materialFee = Arrays.stream(materialFeeInfo.getValue().split(",")).map(amountByNumberMap::get)
                        .filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add);
            } else {
                materialFee = BigDecimal.ZERO;
            }
            //研发费用
            BigDecimal allAmount = projectApprovalEstimateExpenseSubjectList.stream().filter(f -> "0".equals(f.getParentId()) && ObjectUtil.isNotEmpty(f.getAmount()))
                    .map(ProjectApprovalEstimateExpenseSubject::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            vos.forEach(f -> {
                BigDecimal expectedIncome = f.getExpectedIncome();
                if (expectedIncome.compareTo(BigDecimal.ZERO) != 0) {
                    Optional<Date> date = minExpectedContractYearMap.get(f.getProductId());
                    if (date.isEmpty() || f.getExpectedContractYear().compareTo(date.get()) <= 0) {
                        //首年毛利率
                        BigDecimal marginRate = (expectedIncome.divide(new BigDecimal("1.13"), 4, RoundingMode.HALF_UP).subtract(allAmount).subtract(materialFee.add(f.getFabricateHour().multiply(new BigDecimal(100)))))
                                .divide(expectedIncome, 4,RoundingMode.HALF_UP).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
                        f.setMarginRate(marginRate + "%");
                    } else {
                        //次年毛利率
                        BigDecimal marginRate = (expectedIncome.divide(new BigDecimal("1.13"), 4, RoundingMode.HALF_UP).subtract(materialFee.add(f.getFabricateHour().multiply(new BigDecimal(100)))))
                                .divide(expectedIncome, 4,RoundingMode.HALF_UP).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
                        f.setMarginRate(marginRate + "%");
                    }
                }
                if (f.getExpectedSaleNumber() != 0) {
                    f.setExpectedPrice(expectedIncome.divide(new BigDecimal(f.getExpectedSaleNumber()), 2, RoundingMode.HALF_UP));
                } else {
                    f.setExpectedPrice(BigDecimal.ZERO);
                }

            });
        }
    }



    @Override
    public List<ProjectApprovalIncomeVO> getList( String approvalId) throws Exception {

        LambdaQueryWrapperX<ProjectApprovalIncome> condition = new LambdaQueryWrapperX<>( ProjectApprovalIncome. class);
        condition.eq(ProjectApprovalIncome::getProjectApprovalId,approvalId);
        condition.orderByDesc(ProjectApprovalIncome::getCreateTime);
        List<ProjectApprovalIncome> list = this.list(condition);
        List<ProjectApprovalIncomeVO> vos = BeanCopyUtils.convertListTo(list, ProjectApprovalIncomeVO::new);
        setEveryName(vos);
        return vos;
    }

}
