package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.resourceAllocation.ResourceAllocationDTO;
import com.chinasie.orion.domain.dto.resourceAllocation.UpdateTimeDTO;
import com.chinasie.orion.domain.vo.resourceAllocation.*;

import java.util.List;

/**
 * 人员资源调配服务类
 *
 * <AUTHOR>
 * @since 2024-08-11 17:45:31
 */
public interface PersonResourceAllocationService {

    RepairPlanVO getPersonInfo( ResourceAllocationDTO resourceAllocationDTO );

    RepairPlanVO queryRepairPlanByRepairRound( String repairRound );

    List<RepairPlanVO> queryRepairPlan( String repairRound );

    Integer setPeresonTime(List<UpdateTimeDTO> updateTimeDTOList);

    Integer setAssetTime(List<UpdateTimeDTO> updateTimeDTOList);

}
