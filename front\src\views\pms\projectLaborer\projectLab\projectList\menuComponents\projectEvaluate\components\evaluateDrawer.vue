<template>
  <BasicDrawer
    v-bind="$attrs"
    :width="1000"
    :showFooter="true"
    :title="state.drawerName"
    @register="modalRegister"
    @visibleChange="visibleChange"
    @ok="confirmDrawer"
  >
    <EvaluateDrawerFrom
      v-if="state.visibleStatus"
      ref="formRef"
      :fromData="state.fromData"
    />
  </BasicDrawer>
</template>

<script setup lang="ts">
import {
  ref, defineEmits,
} from 'vue';
import { BasicDrawer, useDrawerInner } from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import EvaluateDrawerFrom from './evaluateDrawerFrom.vue';
import Api from '/@/api';
const emits = defineEmits(['upTableDate']);
const formRef = ref();
const state = ref({
  drawerType: '',
  drawerName: '',
  visibleStatus: false,
  id: '',
  projectId: '',
  fromData: {},
});
const [modalRegister, { closeDrawer, changeOkLoading }] = useDrawerInner(
  (openProps: { type, itemData, projectId}) => {
    state.value.drawerType = openProps.type;
    state.value.id = openProps.itemData?.id;
    state.value.projectId = openProps.projectId;
    state.value.fromData = {
      ...openProps.itemData,
      projectId: openProps.projectId,
      typeSelection: openProps.type === 'edit',
    };
    // 设置为已打开状态
    state.value.visibleStatus = true;
    state.value.drawerName = openProps.type === 'edit' ? '编辑项目评价' : '新增项目评价';
  },
);
function visibleChange(visible: boolean) {
  // 窗口关闭时，设置状态值
  !visible && (state.value.visibleStatus = visible);
}
async function confirmDrawer() {
  const formValues = await formRef.value.validate();
  changeOkLoading(true);
  if (state.value.drawerType === 'edit') {
    const {
      evaluationType, name, evaluationObject, evaluationPersonId, evaluationTime, remark,
    } = formValues;
    const data = {
      projectId: state.value.projectId,
      id: state.value.id,
      evaluationType,
      name,
      evaluationObject,
      evaluationPersonId,
      evaluationTime: dayjs(evaluationTime).format('YYYY-MM-DD'),
      remark,
    };
    await new Api('/pms/evaluation-project').fetch(data, '', 'PUT').then(() => {

    }).finally(() => {
      changeOkLoading(false);
    });
  } else {
    const {
      evaluationType, name, evaluationObject, evaluationPersonId, evaluationTime, remark,
    } = formValues;
    const data = {
      projectId: state.value.projectId,
      evaluationType,
      name,
      evaluationObject,
      evaluationPersonId,
      evaluationTime: dayjs(evaluationTime).format('YYYY-MM-DD'),
      remark,
    };
    await new Api('/pms/evaluation-project').fetch(data, '', 'POST').then(() => {

    }).finally(() => {
      changeOkLoading(false);
    });
  }
  closeDrawer();
  emits('upTableDate');// 更新父组件数据
}

</script>
<style scoped lang="less">
</style>
