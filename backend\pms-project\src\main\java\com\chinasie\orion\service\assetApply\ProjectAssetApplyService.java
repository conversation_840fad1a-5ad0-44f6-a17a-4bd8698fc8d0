package com.chinasie.orion.service.assetApply;

import com.chinasie.orion.domain.dto.assetApply.ProjectAssetApplyDTO;
import com.chinasie.orion.domain.entity.assetApply.ProjectAssetApply;
import com.chinasie.orion.domain.vo.applyAsset.ProjectAssetApplyVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * ProjectAssetApply 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-03 14:14:44
 */
public interface ProjectAssetApplyService  extends  OrionBaseService<ProjectAssetApply>  {


    /**
     *  详情
     *
     * * @param id
     */
    ProjectAssetApplyVO detail(String id, String pageCode)throws Exception;

    /**
     *  新增
     *
     * * @param projectAssetApplyDTO
     * @return
     */
    String create(ProjectAssetApplyDTO projectAssetApplyDTO)throws Exception;

    /**
     *  编辑
     *
     * * @param projectAssetApplyDTO
     */
    Boolean edit(ProjectAssetApplyDTO projectAssetApplyDTO)throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<ProjectAssetApplyVO> pages( Page<ProjectAssetApplyDTO> pageRequest)throws Exception;

    /**
     *  下载模板
     *
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response)throws Exception;

    /**
     *  导入校验
     *
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


    /**
     *  确认导入
     *
     * * @param importId
     */
    Boolean importByExcel(String importId)throws Exception;

    /**
     *  取消导入
     *
     * * @param importId
     */
    Boolean importCancelByExcel(String importId)throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

    /**
     *  设置各种名称
     *
     * * @param searchConditions
     * * @param response
     */
    void  setEveryName(List<ProjectAssetApplyVO> vos)throws Exception;
}
