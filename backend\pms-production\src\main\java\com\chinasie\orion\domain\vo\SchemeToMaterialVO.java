package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.util.List;
/**
 * SchemeToMaterial VO对象
 *
 * <AUTHOR>
 * @since 2024-08-15 20:21:30
 */
@ApiModel(value = "SchemeToMaterialVO对象", description = "计划相关的物资")
@Data
public class SchemeToMaterialVO extends  ObjectVO   implements Serializable{

            /**
         * 项目计划ID
         */
        @ApiModelProperty(value = "项目计划ID")
        private String planSchemeId;


        /**
         * 大修伦次
         */
        @ApiModelProperty(value = "大修伦次")
        private String repairRound;


        /**
         * 物资编码（固定资产编码）
         */
        @ApiModelProperty(value = "物资编码（固定资产编码）")
        private String materialNumber;


        /**
         * 物资ID：物资管理的Id
         */
        @ApiModelProperty(value = "物资ID：物资管理的Id")
        private String materialId;


        /**
         * 物资名称
         */
        @ApiModelProperty(value = "物资名称")
        private String materialName;


        /**
         * 基地编码
         */
        @ApiModelProperty(value = "基地编码")
        private String baseCode;

        /**
         * 入库数量
         */
        @ApiModelProperty(value = "入库数量")
        private Integer demandNum;

        /**
         * 规格型号
         */
        @ApiModelProperty(value = "规格型号")
        private String specificationModel;


        /**
         * 资产类型
         */
        @ApiModelProperty(value = "资产类型")
        private String assetType;

        /**
         * 资产类型
         */
        @ApiModelProperty(value = "资产类型名称")
        private String assetTypeName;


        /**
         * 资产代码
         */
        @ApiModelProperty(value = "资产代码")
        private String assetCode;


        /**
         * 资产名称
         */
        @ApiModelProperty(value = "资产名称")
        private String assetName;

        @ApiModelProperty(value = "成本中心")
        private String costCenter;

        /**
         * 成本中心
         */
        @ApiModelProperty(value = "成本中心")
        private String costCenterName;


        /**
         * 库存数量
         */
        @ApiModelProperty(value = "库存数量")
        private Integer stockNum;

        @ApiModelProperty(value = "物资信息")
        private MaterialManageVO materialManageVO;

        @ApiModelProperty(value = "参与作业数")
        private Integer jobNum;

        @ApiModelProperty(value = "是否可用")
        private Boolean avaliable;
        @ApiModelProperty(value = "产品编码")
        private String productCode;

        /**
         * 工具状态
         */
        @ApiModelProperty(value = "工具状态")
        private String toolStatus;


        /**
         * 检定维护周期
         */
        @ApiModelProperty(value = "检定维护周期")
        private Integer maintenanceCycle;

        /**
         * 工具状态名称
         */
        @ApiModelProperty(value = "工具状态名称")
        private String toolStatusName;

        @ApiModelProperty(value = "项目名称")
        private String projectName;
        @ApiModelProperty(value = "项目计划名称")
        private String planSchemeName;
        @ApiModelProperty(value = "项目id")
        private String projectId;
}
