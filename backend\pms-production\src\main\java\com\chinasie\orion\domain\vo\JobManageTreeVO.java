package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class JobManageTreeVO {

    @ApiModelProperty("大修轮次")
    String repairRound;

    String code;

    @ApiModelProperty("中心层级")
    List<JobManageCenter> centerList;
}

