package com.chinasie.orion.service.impl;
import com.chinasie.orion.domain.entity.JobManage;
import com.chinasie.orion.domain.entity.JobRisk;
import com.chinasie.orion.domain.dto.JobRiskDTO;
import com.chinasie.orion.domain.vo.JobRiskVO;
import com.chinasie.orion.domain.vo.JobSecurityMeasureVO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.file.api.service.FileApiService;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import com.chinasie.orion.service.JobManageService;
import com.chinasie.orion.service.JobRiskService;
import com.chinasie.orion.repository.JobRiskMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.JobSecurityMeasureService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * JobRisk 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-09 15:47:53
 */
@Service
@Slf4j
public class JobRiskServiceImpl extends  OrionBaseServiceImpl<JobRiskMapper, JobRisk>   implements JobRiskService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private FileApiService fileApiService;

    private JobManageService jobManageService;

    private JobSecurityMeasureService jobSecurityMeasureService;
    @Autowired
    public void setJobSecurityMeasureService(JobSecurityMeasureService jobSecurityMeasureService) {
        this.jobSecurityMeasureService = jobSecurityMeasureService;
    }
    @Autowired
    public void setJobManageService(JobManageService jobManageService) {
        this.jobManageService = jobManageService;
    }

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  JobRiskVO detail(String id,String pageCode) throws Exception {
        JobRisk jobRisk =this.getById(id);
        JobRiskVO result = BeanCopyUtils.convertTo(jobRisk,JobRiskVO::new);
        setEveryName(Collections.singletonList(result));

        if(StringUtils.hasText(jobRisk.getJobId())){
            JobManage jobManage = jobManageService.getById(jobRisk.getJobId());
            result.setJobName(null == jobManage?"":jobManage.getName());
            result.setJobNumber(null == jobManage?"":jobManage.getNumber());
        }
        return result;
    }

    /**
     *  新增
     *
     * * @param jobRiskDTO
     */
    @Override
    public  String create(JobRiskDTO jobRiskDTO) throws Exception {
        JobRisk jobRisk =BeanCopyUtils.convertTo(jobRiskDTO,JobRisk::new);
        this.save(jobRisk);

        String rsp=jobRisk.getId();

        return rsp;
    }

    /**
     *  编辑
     *
     * * @param jobRiskDTO
     */
    @Override
    public Boolean edit(JobRiskDTO jobRiskDTO) throws Exception {
        JobRisk jobRisk =BeanCopyUtils.convertTo(jobRiskDTO,JobRisk::new);

        this.updateById(jobRisk);

        String rsp=jobRisk.getId();

        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<JobRiskVO> pages( Page<JobRiskDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<JobRisk> condition = new LambdaQueryWrapperX<>( JobRisk. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(JobRisk::getCreateTime);


        Page<JobRisk> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), JobRisk::new));

        PageResult<JobRisk> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<JobRiskVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<JobRiskVO> vos = BeanCopyUtils.convertListTo(page.getContent(), JobRiskVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "作业与风险关联导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", JobRiskDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            JobRiskExcelListener excelReadListener = new JobRiskExcelListener();
        EasyExcel.read(inputStream,JobRiskDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<JobRiskDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("作业与风险关联导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<JobRisk> jobRiskes =BeanCopyUtils.convertListTo(dtoS,JobRisk::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::JobRisk-import::id", importId, jobRiskes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<JobRisk> jobRiskes = (List<JobRisk>) orionJ2CacheService.get("pmsx::JobRisk-import::id", importId);
        log.info("作业与风险关联导入的入库数据={}", JSONUtil.toJsonStr(jobRiskes));

        this.saveBatch(jobRiskes);
        orionJ2CacheService.delete("pmsx::JobRisk-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::JobRisk-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<JobRisk> condition = new LambdaQueryWrapperX<>( JobRisk. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(JobRisk::getCreateTime);
        List<JobRisk> jobRiskes =   this.list(condition);

        List<JobRiskDTO> dtos = BeanCopyUtils.convertListTo(jobRiskes, JobRiskDTO::new);

        String fileName = "作业与风险关联数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", JobRiskDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<JobRiskVO> vos)throws Exception {
        if(CollectionUtils.isEmpty(vos)){
            return;
        }
        vos.forEach(vo->{
        });


    }

    @Override
    public List<JobRiskVO> getListByJobId(String id) {
        LambdaQueryWrapperX<JobRisk> condition = new LambdaQueryWrapperX<>( JobRisk. class);
        condition.eq(JobRisk::getJobId,id);
//        condition.select(JobRisk::getJobId,JobRisk::getId,JobRisk::getRiskDesc,JobRisk::getRiskNumber
//                ,JobRisk::getRiskText,JobRisk::getRiskType,JobRisk::getFunctionalLocation,JobRiskVO::getEncryKey);
        List<JobRisk> list = this.list(condition);
        if(CollectionUtils.isEmpty(list)){
            return  new ArrayList<>();
        }
        List<JobRiskVO> jobRiskVOList = BeanCopyUtils.convertListTo(list, JobRiskVO::new);
        List<String> encryKeyList = jobRiskVOList.stream().map(JobRiskVO::getEncryKey).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<String,List<JobSecurityMeasureVO>> idToListMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(encryKeyList)){
           idToListMap = jobSecurityMeasureService.getListByKeyList(encryKeyList);
        }

        Map<String, List<JobSecurityMeasureVO>> finalIdToListMap = idToListMap;
        jobRiskVOList.forEach(item->{
            item.setSecurityMeasureVOList(finalIdToListMap.getOrDefault(item.getEncryKey(),new ArrayList<>()));
        });

        return jobRiskVOList;
    }


    public static class JobRiskExcelListener extends AnalysisEventListener<JobRiskDTO> {

        private final List<JobRiskDTO> data = new ArrayList<>();

        @Override
        public void invoke(JobRiskDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<JobRiskDTO> getData() {
            return data;
        }
    }


}
