<template>
  <a-drawer
    v-model:visible="father.visible"
    class="ui-2-0"
    :width="350"
    :title="father.title"
    :body-style="bodyStyle"
    :mask-closable="false"
    @close="handleClose"
  >
    <a-form
      ref="formRef"
      layout="vertical"
      :rules="rules"
      :model="father.form"
    >
      <a-form-item
        label="姓名"
        name="memberId"
      >
        <a-select
          v-model:value="father.form.memberId"
          placeholder="请输入姓名"
          allow-clear
          show-search
          :filter-option="filterOption"
          :options="namesList"
          size="large"
          @change="handlePrincipal(namesList, father.form.memberId)"
        />
      </a-form-item>
      <a-form-item
        label="开始日期"
        name="startTime"
      >
        <a-date-picker
          v-model:value="father.form.startTime"
          show-time
          type="date"
          placeholder="请选择开始日期"
          allow-clear
          value-format="YYYY-MM-DD HH:mm:ss"
          size="large"
          class="w-full"
        />
      </a-form-item>

      <a-form-item label="预估工时">
        <a-input-number
          v-model:value="father.form.manHour"
          :min="0"
          :max="100000"
          placeholder="请输入预估工时"
          disabled
          size="large"
          allow-clear
        />
      </a-form-item>
      <a-form-item label="剩余工时">
        <a-input-number
          v-model:value="father.form.residueManHour"
          :min="0"
          :max="100000"
          placeholder="请输入剩余工时"
          disabled
          size="large"
          allow-clear
        />
      </a-form-item>
      <a-form-item
        label="实际工时"
        name="realityManHour"
      >
        <a-input-number
          v-model:value="father.form.realityManHour"
          :min="0"
          :max="100000"
          :precision="1"
          placeholder="请输入实际工时"
          size="large"
          allow-clear
        />
      </a-form-item>
      <a-form-item label="描述">
        <a-textarea
          v-model:value="father.form.remark"
          placeholder="请输入描述"
          allow-clear
          :maxlength="255"
          size="large"
          :autosize="{ minRows: 3, maxRows: 6 }"
        />
      </a-form-item>
    </a-form>
    <a-checkbox
      v-if="father.type === 'add'"
      v-model:checked="isGo"
      size="large"
    >
      继续创建下一个
    </a-checkbox>
    <div class="drawer-footer">
      <a-row :gutter="20">
        <a-col :span="12">
          <a-button
            size="large"
            block
            @click="handleClose"
          >
            取消
          </a-button>
        </a-col>
        <a-col :span="12">
          <a-button
            size="large"
            type="primary"
            block
            :loading="loading"
            @click="handleSave(father.type, isGo)"
          >
            确认
          </a-button>
        </a-col>
      </a-row>
    </div>
  </a-drawer>
</template>

<script>
import {
  onBeforeMount, reactive, toRefs, ref, inject,
} from 'vue';
import {
  Row,
  Col,
  Drawer,
  Form,
  Checkbox,
  Select,
  DatePicker,
  Input,
  Button,
  message,
  InputNumber,
} from 'ant-design-vue';
import Api from '/@/api';
import { parseURL } from '/@/views/pms/projectLaborer/utils/index';
import { projectIdKey } from '/@/views/pms/projectLaborer/projectLab/types';

export default {
  name: 'Edit',
  components: {
    ARow: Row,
    ACol: Col,
    AInputNumber: InputNumber,
    AButton: Button,
    ATextarea: Input.TextArea,
    ADatePicker: DatePicker,
    ASelect: Select,
    ACheckbox: Checkbox,
    AForm: Form,
    AFormItem: Form.Item,
    ADrawer: Drawer,
  },
  props: {
    data: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  emits: ['submit'],
  setup(props, { emit }) {
    const projectSchemeId = inject('projectSchemeId');
    const projectId = inject('projectId');
    const state = reactive({
      bodyStyle: {
        overflow: 'auto',
        height: 'calc(100vh - 120px)',
      },
      namesList: [], // 负责人
      father: props.data,
      isGo: false,
      loading: false,
      formRef: ref(),
      projectId: projectId.value,
      rules: {
        memberId: [
          {
            required: true,
            message: '请选择姓名',
            trigger: 'change',
          },
        ],
        startTime: [
          {
            required: true,
            message: '请选择开始时间',
            trigger: 'change',
          },
        ],
        realityManHour: [
          {
            required: true,
            message: '请输入实际工时',
          },
        ],
      },
    });
    function getProjectRole() {
      const qury = `/${state.projectId}?name=`;
      new Api('/pms').fetch('', `project-role-user/getListByName${qury}`, 'POST').then((res) => {
        state.namesList = res.map((s) => ({
          label: s.name,
          value: s.id,
        }));
      });
    }
    function filterOption(inputValue, treeNode) {
      return treeNode.props.label.includes(inputValue);
    }

    function handleSave(type, isGo) {
      state.formRef
        .validate()
        .then(() => {
          state.loading = true;
          const love = {
            id: type === 'add' ? '' : state.father.form?.id,
            name: state.father.form?.name,
            className: 'Plan', // 列表中获取也可根据实际情况手动输入
            moduleName: '项目管理-计划管理-项目计划', // 模块名称
            type: type === 'add' ? 'SAVE' : 'UPDATE', // 操作类型
            remark: `${type === 'add' ? '新增' : '编辑'}了【${state.father.form?.name}】`,
          };
          new Api('/pms', love)
            .fetch(state.father.form, 'man-hour', type === 'add' ? 'POST' : 'PUT')
            .then(() => {
              state.loading = false;
              message.success('操作成功');
              if (type === 'add' && isGo) {
                new Api('/pms')
                  .fetch('', `man-hour/list?planId=${projectSchemeId.value}`, 'GET')
                  .then((res) => {
                    state.father.form = {
                      planId: projectSchemeId.value,
                      memberId: undefined,
                      name: undefined,
                      startTime: undefined,
                      manHour: res.manHour,
                      residueManHour: res.residueManHour,
                      realityManHour: undefined,
                      remark: undefined,
                    };
                  });
                emit('submit', true);
              } else {
                emit('submit', true);
              }
            })
            .catch(() => {
              state.loading = false;
            });
        })
        .catch(() => {
          message.warning('请检查必填项');
        });
    }
    function handleClose() {
      emit('submit', false);
    }
    function handlePrincipal(arr, id) {
      if (id) {
        const obj = arr.find((s) => s.value === id);
        state.father.form.name = obj.label;
      } else {
        state.father.form.name = undefined;
      }
    }
    onBeforeMount(() => {
      getProjectRole();
    });
    return {
      ...toRefs(state),
      handleSave,
      handleClose,
      handlePrincipal,
      filterOption,
    };
  },
};
</script>

<style lang="less" scoped>
  .drawer-footer {
    position: absolute;
    bottom: 10px;
    width: 88%;
  }
</style>
