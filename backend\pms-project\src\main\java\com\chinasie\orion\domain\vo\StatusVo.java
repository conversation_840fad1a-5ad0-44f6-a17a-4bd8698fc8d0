package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/2/16 16:27
 * @description:
 */
@ApiModel(value = "StatusVo对象", description = "状态")
@Data
public class StatusVo implements Serializable {
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "值")
    private Integer status;

}
