package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "EvaluationCommonVO对象", description = "项目评价通用详情")
@Data
public class EvaluationCommonVO  extends ObjectVO implements Serializable {

//    /**
//     * 评价内容
//     */
//    @ApiModelProperty(value = "评价内容描述")
//    private String evaluationContent;
//    /**
//     * 评价内容评分
//     */
//    @ApiModelProperty(value = "评价内容评分")
//    private Integer evaluationContentScore;

    /**
     * 项目目标和成果评价
     */
    @ApiModelProperty(value = "项目目标和成果评价描述")
    private String  goalAchieve;
    /**
     * 项目目标和成果评价评分
     */
    @ApiModelProperty(value = "项目目标和成果评价评分")
    private Integer  goalAchieveScore;

    /**
     * 项目进展管理评价
     */
    @ApiModelProperty(value = "项目进展管理评价描述")
    private String progressManage;
    /**
     * 项目进展管理评价评分评分
     */
    @ApiModelProperty(value = "项目进展管理评价评分")
    private Integer progressManageScore;
    /**
     * 项目质量评价
     */
    @ApiModelProperty(value = "项目质量评价描述")
    private String projectQuality;
    /**
     * 项目质量评价评分
     */
    @ApiModelProperty(value = "项目质量评价评分")
    private Integer projectQualityScore;
    /**
     * 项目资源成本与效益评价
     */
    @ApiModelProperty(value = "项目资源成本与效益评价描述")
    private String  costBenefit;
    /**
     * 项目资源成本与效益评价评分
     */
    @ApiModelProperty(value = "项目资源成本与效益评价评分")
    private Integer  costBenefitScore;
    /**
     * 项目风险评价
     */
    @ApiModelProperty(value = "项目风险评价描述")
    private String projectRisk;
    /**
     * 项目风险评价评分
     */
    @ApiModelProperty(value = "项目风险评价评分")
    private Integer projectRiskScore;
    /**
     * 项目沟通和合作评价
     */
    @ApiModelProperty(value = "项目沟通和合作评价描述")
    private String communicateJoin;
    /**
     * 项目沟通和合作评价评分
     */
    @ApiModelProperty(value = "项目沟通和合作评价评分")
    private Integer communicateJoinScore;
    /**
     * 经验与教训总结
     */
    @ApiModelProperty(value = "经验与教训总结描述")
    private String expLesson;
    /**
     * 经验与教训总结评分
     */
    @ApiModelProperty(value = "经验与教训总结评分")
    private Integer expLessonScore;
}
