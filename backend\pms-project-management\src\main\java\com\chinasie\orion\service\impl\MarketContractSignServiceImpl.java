package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.bo.PmsMQProducer;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.MarketContractMilestoneEnum;
import com.chinasie.orion.constant.MarketContractMilestoneStatusEnum;
import com.chinasie.orion.constant.MarketContractStatusEnum;
import com.chinasie.orion.constant.MarketContractTypeEnums;
import com.chinasie.orion.domain.dto.MarketContractSignDTO;
import com.chinasie.orion.domain.entity.ContractMilestone;
import com.chinasie.orion.domain.entity.ContractOurSignedSubject;
import com.chinasie.orion.domain.entity.MarketContract;
import com.chinasie.orion.domain.entity.MarketContractSign;
import com.chinasie.orion.domain.vo.ContractMilestoneApiVO;
import com.chinasie.orion.domain.vo.MarketContractSignFeedbackVO;
import com.chinasie.orion.domain.vo.MarketContractSignVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.file.api.service.FileApiService;
import com.chinasie.orion.management.constant.RequirementMscNodeEnum;
import com.chinasie.orion.management.domain.entity.ProjectInitiation;
import com.chinasie.orion.management.service.ProjectInitiationService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.MarketContractMapper;
import com.chinasie.orion.repository.MarketContractSignMapper;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <p>
 * MarketContractSign 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-30 01:37:02
 */
@Service
@Slf4j
public class MarketContractSignServiceImpl extends OrionBaseServiceImpl<MarketContractSignMapper, MarketContractSign> implements MarketContractSignService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private MarketContractMapper marketContractMapper;
    @Autowired
    private MarketContractService marketContractService;

    @Autowired
    private ContractMilestoneService contractMilestoneService;

    @Autowired
    private FileApiService fileApi;

    @Autowired
    protected PmsMQProducer mqProducer;
    @Autowired
    private ProjectInitiationService projectInitiationService;
    @Autowired
    private ProjectSchemeApi2Service projectSchemeApi2Service;

    @Autowired
    ContractOurSignedSubjectService contractOurSignedSubjectService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public MarketContractSignVO detail(String contractId, String pageCode) throws Exception {
        MarketContractSign marketContractSign = this.baseMapper.selectOne(MarketContractSign::getContractId, contractId);
        MarketContractSignVO result = BeanCopyUtils.convertTo(marketContractSign, MarketContractSignVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param marketContractSignDTO
     */
    @Override
    public String create(MarketContractSignDTO marketContractSignDTO) throws Exception {
        MarketContractSign marketContractSign = BeanCopyUtils.convertTo(marketContractSignDTO, MarketContractSign::new);
        String contractId = marketContractSign.getContractId();
        MarketContract marketContract = marketContractMapper.selectById(contractId);
//        if(marketContract == null){
//            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "未找到合同信息！");
//        }
//        if(MarketContractTypeEnums.SHOPP_ORDER_CONTRACT.getCode().equals(marketContract.getContractType())){
//            if(!MarketContractStatusEnum.AUDITING.getStatus().equals(marketContract.getStatus())){
//                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "不是审核中状态不能签署！");
//            }
//
//        }
//        else{
//            if(!MarketContractStatusEnum.SIGNING.getStatus().equals(marketContract.getStatus())){
//                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "不是待签署状态不能签署！");
//            }
//        }


        List<MarketContractSignFeedbackVO> marketContractSignFeedbackVOs = marketContractSignDTO.getMarketContractSignFeedbackVOs();
        List<ContractOurSignedSubject> contractOurSignedSubjects = BeanCopyUtils.convertListTo(marketContractSignFeedbackVOs, ContractOurSignedSubject::new);

//        List<String> customNamesList = marketContractSignFeedbackVOs.stream()
//                .map(MarketContractSignFeedbackVO::getCustomNames)
//                .collect(Collectors.toList());
//        LambdaQueryWrapperX<ContractOurSignedSubject> condition = new LambdaQueryWrapperX<>( ContractOurSignedSubject. class);
//        condition.eq(ContractOurSignedSubject::getContractNumber, marketContract.getNumber());

        contractOurSignedSubjectService.updateBatchById(contractOurSignedSubjects);


        LambdaUpdateWrapper<ContractMilestone> contractMilestoneLambdaQueryWrapperX = new LambdaUpdateWrapper<>();
        contractMilestoneLambdaQueryWrapperX.eq(ContractMilestone::getContractId, marketContract.getId());
        if (marketContractSign.getSignResult()) {
            marketContract.setStatus(MarketContractStatusEnum.FULFIL.getStatus());
            String contractType = marketContract.getContractType();
            if (ObjectUtil.isNotEmpty(contractType)) {
                //判断是不是子合同或框架子订单 如果是 判断下属子订单类型是不是框架 如果是框架 不执行里程碑状态变更
//                boolean flag = true;
//                if (contractType.equals("sonContract") || contractType.equals("subOrderContract")) {
//                    String subOrderType = marketContract.getSubOrderType();
//                    if (subOrderType.equals("frame")) {
//                        flag = false;
//                    }
//                }
//                if (flag) {
                LambdaQueryWrapperX<ContractMilestone> contractMilestoneLambdaQueryWrapperX2 = new LambdaQueryWrapperX<>();
                contractMilestoneLambdaQueryWrapperX2.eq(ContractMilestone::getContractId, contractId);
                //contractMilestoneLambdaQueryWrapperX2.eq(ContractMilestone::getAmmountType, MarketContractMilestoneEnum.MILESTONEAMT.getCode());
                List<ContractMilestone> contractMilestones2 = contractMilestoneService.list(contractMilestoneLambdaQueryWrapperX2);
                if (ObjectUtil.isNotEmpty(contractMilestones2)) {
                    for (ContractMilestone contractMilestone : contractMilestones2) {
                        Integer milestoneStatus = contractMilestone.getStatus();
                        contractMilestone.setStatus(MarketContractMilestoneStatusEnum.PROGRESS.getStatus());
                        if (ObjectUtil.isNotEmpty(contractMilestone.getPlanAcceptDate())) {
                            contractMilestone.setPlannedAcceptanceDate(contractMilestone.getPlanAcceptDate());
                        } else if (ObjectUtil.isNotEmpty(contractMilestone.getExpectAcceptDate())) {
                            contractMilestone.setPlannedAcceptanceDate(contractMilestone.getExpectAcceptDate());
                        }

                        if (ObjectUtil.isNotEmpty(contractMilestone.getMilestoneAmt()) && !(contractMilestone.getMilestoneAmt().compareTo(BigDecimal.ZERO) == 0)) {
                            contractMilestone.setPlannedAcceptanceAmount(contractMilestone.getMilestoneAmt());
                        } else if (ObjectUtil.isNotEmpty(contractMilestone.getExceptAcceptanceAmt()) && !(contractMilestone.getExceptAcceptanceAmt().compareTo(BigDecimal.ZERO) == 0)) {
                            contractMilestone.setPlannedAcceptanceAmount(contractMilestone.getExceptAcceptanceAmt());
                        }

                        if (milestoneStatus.equals(MarketContractMilestoneStatusEnum.APPROVAL.getStatus()) && StrUtil.equals(contractMilestone.getAmmountType(), MarketContractMilestoneEnum.MILESTONEAMT.getCode())) {
                            contractMilestone.setExceptAcceptanceAmt(contractMilestone.getMilestoneAmt());
                            contractMilestone.setExpectAcceptDate(contractMilestone.getPlanAcceptDate());
                        }
                    }
                    contractMilestoneService.updateBatchById(contractMilestones2);
                }
                //  }
            }
//            contractMilestoneLambdaQueryWrapperX.set(ContractMilestone::getStatus, MarketContractMilestoneStatusEnum.PROGRESS.getStatus());
        } else {
            contractMilestoneLambdaQueryWrapperX.set(ContractMilestone::getStatus, MarketContractMilestoneStatusEnum.CREATED.getStatus());
            marketContract.setStatus(MarketContractStatusEnum.COMPLATED.getStatus());
            contractMilestoneService.update(contractMilestoneLambdaQueryWrapperX);
        }
        marketContractMapper.updateById(marketContract);
        this.save(marketContractSign);


        //里程碑进行中的生成项目计划
        if (marketContractSign.getSignResult()) {
            try {
                contractMilestoneTOChangeProjectScheme(contractId);
            } catch (Exception e) {
                log.error("生成项目计划失败", e);
            }

        }

        List<FileDTO> fileDTOList = marketContractSignDTO.getFileInfoDTOList();
        if (!CollectionUtils.isEmpty(fileDTOList)) {
            fileDTOList.forEach(item -> {
                item.setDataId(marketContractSign.getId());
                item.setDataType("MarketContractSign");
            });
            fileApi.batchSaveFile(fileDTOList);
        }

        String rsp = marketContractSign.getId();
        // 发送消息通知
        if (marketContractSignDTO.getSignResult()) {
            //TODO 里程碑负责人是里程碑的什么人
            sendMessage(rsp,
                    "/pas/contract-mangeDetail?id=" + rsp + "&htNum=" + marketContract.getNumber() + "&query=" + new Date().getTime(),
                    marketContract.getName(),
                    Arrays.asList(marketContract.getCommerceRspUser(), marketContract.getTechRspUser()),
                    marketContract.getPlatformId(),
                    marketContract.getOrgId(),
                    RequirementMscNodeEnum.NODE_CONTRACT_LXZ.getCode()
            );
        }


        return rsp;
    }

    private void contractMilestoneTOChangeProjectScheme(String contractId) {
        LambdaQueryWrapperX<ContractMilestone> lambdaQueryWrapper = new LambdaQueryWrapperX<>();
        lambdaQueryWrapper.select("t.id," + "t.class_name," + "t.creator_id," + "t.modify_time," +
                "t.owner_id," + "t.create_time," + "t.modify_id," + "t.remark," + "t.platform_id," + "t.org_id," +
                "t.`status`," + "t.logic_status," + "t.milestone_name," + "t.milestone_type," + "t.parent_id," +
                "t.tech_rsp_user," + "t.bus_rsp_user," + "t.plan_accept_date," + "t.cost_bus_type," + "t.milestone_amt," +
                "t.undert_dept," + "t.number," + "t.rsp_user," + "t.actual_accept_date," + "t.actual_milestone_amt," +
                "t.total_accept_rate," + "t.contract_id," + "t.tax_rate," + "t.expect_accept_date," + "t.description," +
                "t.cust_person_id," + "t1.number as contractNumber," + "t.is_plan");
        lambdaQueryWrapper.leftJoin(MarketContract.class, MarketContract::getId, ContractMilestone::getContractId);
//        lambdaQueryWrapper.eq(MarketContract::getContractType, "frameContract");
        lambdaQueryWrapper.and(wrapper -> wrapper.eq(ContractMilestone::getIsPlan, 0)
                .or().isNull(ContractMilestone::getIsPlan));
        lambdaQueryWrapper.eq(ContractMilestone::getStatus, 110);
        lambdaQueryWrapper.eq(MarketContract::getLogicStatus, 1);
        lambdaQueryWrapper.eq(ContractMilestone::getContractId, contractId);
        List<ContractMilestone> list = contractMilestoneService.selectJoinList(ContractMilestone.class, lambdaQueryWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            LambdaQueryWrapperX<ContractMilestone> lambdaQueryWrapper1 = new LambdaQueryWrapperX<>();
            lambdaQueryWrapper.select("t.id," + "t.class_name," + "t.creator_id," + "t.modify_time," +
                    "t.owner_id," + "t.create_time," + "t.modify_id," + "t.remark," + "t.platform_id," + "t.org_id," +
                    "t.`status`," + "t.logic_status," + "t.milestone_name," + "t.milestone_type," + "t.parent_id," +
                    "t.tech_rsp_user," + "t.bus_rsp_user," + "t.plan_accept_date," + "t.cost_bus_type," + "t.milestone_amt," +
                    "t.undert_dept," + "t.number," + "t.rsp_user," + "t.actual_accept_date," + "t.actual_milestone_amt," +
                    "t.total_accept_rate," + "t1.frame_contract_id as contractId," + "t.tax_rate," + "t.expect_accept_date," + "t.description," +
                    "t.cust_person_id," + "t1.number as contractNumber," + "t.is_plan");
            lambdaQueryWrapper1.leftJoin(MarketContract.class, MarketContract::getId, ContractMilestone::getContractId);
            lambdaQueryWrapper1.and(wrapper -> wrapper.eq(ContractMilestone::getIsPlan, 0)
                    .or().isNull(ContractMilestone::getIsPlan));
            lambdaQueryWrapper1.eq(ContractMilestone::getStatus, 110);
            lambdaQueryWrapper1.eq(MarketContract::getLogicStatus, 1);
            lambdaQueryWrapper1.eq(MarketContract::getFrameContractId, contractId);
            List<ContractMilestone> childMilestones = contractMilestoneService.selectJoinList(ContractMilestone.class, lambdaQueryWrapper1);
            //替换子合同的合同号
            if (CollectionUtil.isNotEmpty(childMilestones)) {
                Map<String, List<ContractMilestone>> listMap = list.stream().collect(Collectors.groupingBy(ContractMilestone::getContractId));
                for (ContractMilestone childMilestone : childMilestones) {
                    if (CollectionUtil.isNotEmpty(listMap.get(childMilestone.getContractId()))) {
                        childMilestone.setContractNumber(listMap.get(childMilestone.getContractId()).get(0).getContractNumber());
                    }
                }
                list.addAll(childMilestones);
            }

            List<ContractMilestoneApiVO> contractMilestoneVOS = BeanCopyUtils.convertListTo(list, ContractMilestoneApiVO::new);
            //已经生成项目的立项数据
            List<ProjectInitiation> projectInitiations = projectInitiationService.list(new LambdaQueryWrapper<ProjectInitiation>()
                    .in(ProjectInitiation::getContractNumbers, list.get(0).getContractNumber())
                    .isNotNull(ProjectInitiation::getProjectId)
                    .ne(ProjectInitiation::getProjectId, "")
            );

            if (!CollectionUtils.isEmpty(projectInitiations)) {
                List<ContractMilestoneApiVO> result = new ArrayList<>();
                projectInitiations.forEach(projectInitiation -> {
                    for (ContractMilestoneApiVO contractMilestoneVO : contractMilestoneVOS) {
                        //todo 根据业务类型区分 目前没有逻辑
//                            if(contractMilestoneVO.getCostBusType().equalsIgnoreCase(projectInitiation.getco)){}
                        ContractMilestoneApiVO vo = BeanCopyUtils.convertTo(contractMilestoneVO, ContractMilestoneApiVO::new);
                        vo.setProjectId(projectInitiation.getProjectId());
                        result.add(vo);
                    }
                });
                projectSchemeApi2Service.createPlan(result);
                //修改里程碑标记
                List<ContractMilestone> contractMilestoneList = Lists.newArrayList();
                for (ContractMilestoneApiVO vo : result) {
                    ContractMilestone contractMilestone = new ContractMilestone();
                    contractMilestone.setId(vo.getId());
                    contractMilestone.setIsPlan(1);
                    contractMilestoneList.add(contractMilestone);
                }
                contractMilestoneService.updateBatchById(contractMilestoneList);
            }

        }

    }

    /**
     * 发送消息通知
     *
     * @param businessId
     * @param url
     * @param desc
     * @param toUser
     * @param platformId
     * @param orgId
     * @param code
     */
    private void sendMessage(String businessId, String url, String desc, List<String> toUser, String platformId, String orgId, String code) {
        SendMessageDTO sendMsc = SendMessageDTO.builder()
                .businessData(JSON.toJSONString(MapUtil.builder().put("flowType", "意见单审批完成").build()))
                .businessId(businessId)
                .todoStatus(0)
                .todoType(TodoTypeDict.TODO_TYPE_TASK)
                .urgencyLevel(0)
                .businessNodeCode(code)
                .titleMap(MapUtil.builder(new HashMap<String, Object>())
                        .put("$name$", desc)
                        .build())
                .messageUrl(String.format(url, businessId))
                .messageUrlName(desc)
                .recipientIdList(toUser)
                .senderTime(new Date())
                .senderId(CurrentUserHelper.getCurrentUserId())
                .platformId(platformId)
                .orgId(orgId)
                .build();
        mqProducer.sendPmsMessage(sendMsc);
    }

    /**
     * 编辑
     * <p>
     * * @param marketContractSignDTO
     */
    @Override
    public Boolean edit(MarketContractSignDTO marketContractSignDTO) throws Exception {
        MarketContractSign marketContractSign = BeanCopyUtils.convertTo(marketContractSignDTO, MarketContractSign::new);

        this.updateById(marketContractSign);

        String rsp = marketContractSign.getId();

        //编辑附件
        List<FileDTO> fileDTOList = marketContractSignDTO.getFileInfoDTOList();
        List<FileVO> existFileList = fileApi.getFilesByDataId(rsp);
        // 优先移除
        if (Objects.nonNull(existFileList)) {
            List<String> filesIds = existFileList.stream().map(FileVO::getId).collect(Collectors.toList());
            fileApi.removeBatchByIds(filesIds);
        }
        // 批量新增
        if (!CollectionUtils.isEmpty(fileDTOList)) {
            fileDTOList.forEach(item -> {
                item.setDataId(rsp);
                item.setDataType("MarketContractSign");
            });
            fileApi.batchSaveFile(fileDTOList);
        }

        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<MarketContractSignVO> pages(Page<MarketContractSignDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<MarketContractSign> condition = new LambdaQueryWrapperX<>(MarketContractSign.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(MarketContractSign::getCreateTime);


        Page<MarketContractSign> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), MarketContractSign::new));

        PageResult<MarketContractSign> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<MarketContractSignVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<MarketContractSignVO> vos = BeanCopyUtils.convertListTo(page.getContent(), MarketContractSignVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "市场合同签署信息导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", MarketContractSignDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        MarketContractSignExcelListener excelReadListener = new MarketContractSignExcelListener();
        EasyExcel.read(inputStream, MarketContractSignDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<MarketContractSignDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("市场合同签署信息导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<MarketContractSign> marketContractSignes = BeanCopyUtils.convertListTo(dtoS, MarketContractSign::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::MarketContractSign-import::id", importId, marketContractSignes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<MarketContractSign> marketContractSignes = (List<MarketContractSign>) orionJ2CacheService.get("pmsx::MarketContractSign-import::id", importId);
        log.info("市场合同签署信息导入的入库数据={}", JSONUtil.toJsonStr(marketContractSignes));

        this.saveBatch(marketContractSignes);
        orionJ2CacheService.delete("pmsx::MarketContractSign-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::MarketContractSign-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<MarketContractSign> condition = new LambdaQueryWrapperX<>(MarketContractSign.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(MarketContractSign::getCreateTime);
        List<MarketContractSign> marketContractSignes = this.list(condition);

        List<MarketContractSignDTO> dtos = BeanCopyUtils.convertListTo(marketContractSignes, MarketContractSignDTO::new);

        String fileName = "市场合同签署信息数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", MarketContractSignDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<MarketContractSignVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }


    public static class MarketContractSignExcelListener extends AnalysisEventListener<MarketContractSignDTO> {

        private final List<MarketContractSignDTO> data = new ArrayList<>();

        @Override
        public void invoke(MarketContractSignDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<MarketContractSignDTO> getData() {
            return data;
        }
    }


    @Override
    public List<MarketContractSignFeedbackVO> feedbackDetailsById(String contractNumber) throws Exception {
        ArrayList<MarketContractSignFeedbackVO> marketContractSignFeedbackVOS = new ArrayList<>();
        LambdaQueryWrapperX<ContractOurSignedSubject> condition = new LambdaQueryWrapperX<>(ContractOurSignedSubject.class);
        condition.eq(ContractOurSignedSubject::getLogicStatus, 1)
                .eq(ContractOurSignedSubject::getContractNumber, contractNumber)
                .select(ContractOurSignedSubject::getSignedMainName);

        List<String> signedMainNames = contractOurSignedSubjectService.list(condition)
                .stream()
                .map(ContractOurSignedSubject::getSignedMainName)
                .collect(Collectors.toList());

        for (String signedMainName : signedMainNames) {
            MarketContractSignFeedbackVO feedbackVO = new MarketContractSignFeedbackVO();
            feedbackVO.setSignedMainName(signedMainName);
            marketContractSignFeedbackVOS.add(feedbackVO);
        }
        return marketContractSignFeedbackVOS;
    }
}
