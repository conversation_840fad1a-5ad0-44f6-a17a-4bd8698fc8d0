---
description:
globs:
alwaysApply: false
---
# 后端开发阶段 MCP 规则

## 规则目标
完成后端服务开发、单元测试和联调测试，确保系统功能完整和稳定。

## 前置条件
1. 已完成 [003-frontend-dev.mdc](./003-frontend-dev.mdc) 阶段
2. 系统架构设计已完成
3. 数据库设计已完成

## 执行步骤

### 1. 后端项目初始化
```mcp
{
  "name": "后端项目初始化",
  "type": "sequential-thinking",
  "steps": [
    {
      "action": "创建项目结构",
      "path": "./admin/backend",
      "framework": {
        "spring-cloud-alibaba": "latest",
        "spring-boot": "latest",
        "spring-cloud-gateway": "latest"
      }
    },
    {
      "action": "配置依赖管理",
      "dependencies": {
        "mybatis-plus": "latest",
        "mybatis": "latest",
        "xxl-job": "latest",
        "seata": "latest",
        "rabbitmq": "latest",
        "nacos": "latest",
        "redis": "latest",
        "camunda": "latest",
        "kkfileview": "latest",
        "sharding-jdbc": "latest",
        "easyexcel": "latest",
        "aviatorscript": "latest",
        "minio": "latest",
        "mysql-connector": "8.0.41"
      }
    },
    {
      "action": "配置中间件",
      "services": [
        "Nacos配置中心",
        "Redis缓存",
        "RabbitMQ消息队列",
        "MinIO对象存储"
      ]
    }
  ]
}
```

### 2. 核心功能开发
```mcp
{
  "name": "核心功能开发",
  "type": "sequential-thinking",
  "steps": [
    {
      "action": "数据访问层开发",
      "components": [
        "实体类定义",
        "Mapper接口",
        "数据库访问实现"
      ]
    },
    {
      "action": "业务服务层开发",
      "services": [
        "规则管理服务",
        "监控服务",
        "告警服务",
        "报表服务"
      ]
    },
    {
      "action": "控制器层开发",
      "controllers": [
        "规则管理接口",
        "监控接口",
        "告警接口",
        "报表接口"
      ]
    },
    {
      "action": "任务调度开发",
      "tasks": [
        "规则执行任务",
        "数据采集任务",
        "报表生成任务"
      ]
    }
  ]
}
```

### 3. 单元测试
```mcp
{
  "name": "单元测试",
  "type": "sequential-thinking",
  "steps": [
    {
      "action": "测试用例编写",
      "coverage": {
        "service": 80,
        "controller": 80,
        "dao": 90
      }
    },
    {
      "action": "集成测试",
      "scenarios": [
        "数据流转测试",
        "事务一致性测试",
        "并发性能测试"
      ]
    },
    {
      "action": "性能测试",
      "metrics": [
        "响应时间",
        "并发能力",
        "资源占用"
      ]
    }
  ]
}
```

### 4. 前后端联调
```mcp
{
  "name": "前后端联调",
  "type": "sequential-thinking",
  "steps": [
    {
      "action": "接口联调",
      "tasks": [
        "接口参数验证",
        "返回格式验证",
        "错误处理验证"
      ]
    },
    {
      "action": "功能联调",
      "scenarios": [
        "规则配置流程",
        "监控告警流程",
        "报表生成流程"
      ]
    },
    {
      "action": "性能优化",
      "aspects": [
        "接口响应时间",
        "数据库查询优化",
        "缓存策略优化"
      ]
    }
  ]
}
```

## 验收标准
1. 所有接口符合API文档规范
2. 单元测试覆盖率达标
3. 集成测试通过
4. 性能指标满足要求
5. 前后端联调无阻塞性问题

## 输出物
1. ./admin/backend/* 后端代码
2. 单元测试报告
3. 集成测试报告
4. 性能测试报告
5. 接口文档

## 下一阶段
完成本阶段后，进入 [005-deploy.mdc](./005-deploy.mdc) 阶段。
