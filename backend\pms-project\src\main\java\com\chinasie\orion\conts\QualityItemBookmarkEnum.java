package com.chinasie.orion.conts;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @className QualityItemBookmarkEnum
 * @description 质量管控项和书签关系枚举值 枚举值
 * @since 2024/8/24
 */
public enum QualityItemBookmarkEnum {

    // Customer Supervision Requirements
    QUALITY_ACTIVITY_01("quality-activity-01", "顾客监督", "customerSupervision"),

    // Customer Training Requirements
    QUALITY_ACTIVITY_02("quality-activity-02", "顾客培训", "customerTraining"),

    // Monitoring and Measurement Control Requirements
    QUALITY_ACTIVITY_03("quality-activity-03", "监视及测量控制", "monitoringMeasurementControl"),

    // Metrology Requirements
    QUALITY_ACTIVITY_04("quality-activity-04", "计量要求", "metrologicalRequirement"),

    // Personnel Training and Assessment Requirements
    QUALITY_ACTIVITY_05("quality-activity-05", "人员培训与考核", "personnelTraining"),

    // Risk Management and Planning Requirements
    QUALITY_ACTIVITY_06("quality-activity-06", "风险管理与策划", "riskManagementAndPlanning"),

    // Risk Analysis and Evaluation Requirements
    QUALITY_ACTIVITY_07("quality-activity-07", "风险分析与评估", "riskAnalysisAndPlanning"),

    // Risk Response Requirements
    QUALITY_ACTIVITY_08("quality-activity-08", "风险应对", "riskResponse"),

    // Risk Monitoring Requirements
    QUALITY_ACTIVITY_09("quality-activity-09", "风险监控", "riskMonitoring"),

    // Quality Problem Resolution and Lessons Learned Requirements
    QUALITY_ACTIVITY_10("quality-activity-10", "质量问题归零与举一反三", "qualityProblemsToZeroAndDrawInferences"),

    // Digitalization Requirements
    QUALITY_ACTIVITY_11("quality-activity-11", "数字化要求", "digitalRequirement"),

    // Standardization Requirements
    QUALITY_ACTIVITY_12("quality-activity-12", "标准化要求", "standardizationRequirement"),

    // Document and Record Management Requirements
    QUALITY_ACTIVITY_13("quality-activity-13", "文件和记录管理", "documentAndRecordsManagement"),

    // Quality Information Management Requirements
    QUALITY_ACTIVITY_14("quality-activity-14", "质量信息管理", "qualityInformation"),

    // Task Analysis Requirements
    QUALITY_ACTIVITY_15("quality-activity-15", "任务分析", "taskAnalysis"),

    // Design Analysis and Principles Requirements
    QUALITY_ACTIVITY_16("quality-activity-16", "设计分析与原则", "designAnalysisAndPrinciples"),

    // Design Input Requirements
    QUALITY_ACTIVITY_17("quality-activity-17", "设计输入要求", "designInputRequirement"),

    // Reliability Requirements
    QUALITY_ACTIVITY_18("quality-activity-18", "可靠性要求", "feasibilityRequirement"),

    // Maintainability Requirements
    QUALITY_ACTIVITY_19("quality-activity-19", "维修性要求", "maintainabilityRequirement"),

    // Supportability Requirements
    QUALITY_ACTIVITY_20("quality-activity-20", "保障性要求", "safeguardRequirement"),

    // Testability Requirements
    QUALITY_ACTIVITY_21("quality-activity-21", "测试性要求", "testabilityRequirement"),

    // Safety Requirements
    QUALITY_ACTIVITY_22("quality-activity-22", "安全性要求", "safetyRequirement"),

    // Environmental Adaptability Requirements
    QUALITY_ACTIVITY_23("quality-activity-23", "环境适应性要求", "environmentalAdaptabilityRequirement"),

    // Ergonomics Requirements
    QUALITY_ACTIVITY_24("quality-activity-24", "人机工程要求", "ergonomicRequirement"),

    // Electromagnetic Compatibility Requirements
    QUALITY_ACTIVITY_25("quality-activity-25", "电磁兼容性要求", "electromagneticCompatibilityRequirements"),

    // Software Control Requirements
    QUALITY_ACTIVITY_26("quality-activity-26", "软件控制要求", "softwareControlRequirement"),

    // Technical State Control Requirements
    QUALITY_ACTIVITY_27("quality-activity-27", "技术状态控制要求", "technicalStatusControlRequirements"),

    // Critical Item Control Requirements
    QUALITY_ACTIVITY_28("quality-activity-28", "关键项目控制要求", "criticalControlRequirements"),

    // Product Characteristics Classification and Key Item Control Requirements
    QUALITY_ACTIVITY_29("quality-activity-29", "产品特性分类及关重件控制要求", "productCharacteristicsClassifyControlRequirements"),

    // Productivity Analysis Requirements
    QUALITY_ACTIVITY_30("quality-activity-30", "生产性分析要求", "productivityAnalysisRequirement"),

    // Test Coverage Analysis Requirements
    QUALITY_ACTIVITY_31("quality-activity-31", "测试覆盖性分析要求", "testCoverageAnalysisRequirements"),

    // Envelope Analysis Requirements
    QUALITY_ACTIVITY_32("quality-activity-32", "包络分析要求", "envelopeAnalysisRequirements"),

    // Design Review Requirements
    QUALITY_ACTIVITY_33("quality-activity-33", "设计评审要求", "designReviewRequirement"),

    // Design Output Requirements
    QUALITY_ACTIVITY_34("quality-activity-34", "设计输出要求", "designOutputRequirement"),

    // Design Verification Requirements
    QUALITY_ACTIVITY_35("quality-activity-35", "设计验证要求", "designVerificationRequirement"),

    // Qualification and Type Approval Requirements
    QUALITY_ACTIVITY_36("quality-activity-36", "鉴定定型要求", "identificationAndSettingRequirements"),

    // Production Unit Qualification Control Requirements
    QUALITY_ACTIVITY_37("quality-activity-37", "生产单位资质控制", "productionUnitQualificationControl"),

    // Production Readiness Check Requirements
    QUALITY_ACTIVITY_38("quality-activity-38", "生产准备状态检查要求", "productionReadinessCheckRequirements"),

    // Production Environment Control Requirements
    QUALITY_ACTIVITY_39("quality-activity-39", "生产环境控制要求", "productionEnvironmentControlRequirements"),

    // File and Data Control Requirements
    QUALITY_ACTIVITY_40("quality-activity-40", "文件与资料控制要求", "documentAndDataControlRequirements"),

    // First Piece Verification Requirements
    QUALITY_ACTIVITY_41("quality-activity-41", "首件鉴定要求", "firstPieceAppraisalRequirements"),

    // Process Management Requirements
    QUALITY_ACTIVITY_42("quality-activity-42", "工艺管理要求", "processManagementRequirement"),

    // Identification and Traceability Requirements
    QUALITY_ACTIVITY_43("quality-activity-43", "标识和可追溯性要求", "identificationAndTraceabilityRequirements"),

    // Critical Process Control Requirements
    QUALITY_ACTIVITY_44("quality-activity-44", "关键过程控制要求", "criticalProcessControlRequirements"),

    // Special Process Control Requirements
    QUALITY_ACTIVITY_45("quality-activity-45", "特殊过程控制要求", "specialProcessControlRequirements"),

    // Production Process Control Requirements
    QUALITY_ACTIVITY_46("quality-activity-46", "生产过程控制要求", "productionProcessControlRequirements"),

    // Batch Production Quality Requirements
    QUALITY_ACTIVITY_47("quality-activity-47", "批生产质量要求", "qualityRequirementsBatchProduction"),

    // Assembly and Test Control Requirements
    QUALITY_ACTIVITY_48("quality-activity-48", "装配和测试控制要求", "assemblyAndTestControlRequirements"),

    // Inspection Requirements
    QUALITY_ACTIVITY_49("quality-activity-49", "检验要求", "inspectionRequirement"),

    // Nonconforming Product Control Requirements
    QUALITY_ACTIVITY_50("quality-activity-50", "不合格品控制要求", "nonconformingProductControlRequirements"),

    // Emergency and Exception Release Requirements
    QUALITY_ACTIVITY_51("quality-activity-51", "紧急放行与例外放行要求", "emergencyReleaseAndExceptionalReleaseRequirements"),

    // Product Protection Requirements
    QUALITY_ACTIVITY_52("quality-activity-52", "产品防护要求", "productProtectionRequirements"),

    // Quality Acceptance Review Requirements
    QUALITY_ACTIVITY_53("quality-activity-53", "产品质量验收评审", "productQualityAcceptanceReview"),

    // Components and Materials Control Requirements
    QUALITY_ACTIVITY_54("quality-activity-54", "元器件和原材料控制", "componentsAndRawMaterialsControl"),

    // Procurement Quality Control Requirements
    QUALITY_ACTIVITY_55("quality-activity-55", "采购过程质量控制", "qualityControlOfPurchasingProcess"),

    // Outsourced Product Quality Control Requirements
    QUALITY_ACTIVITY_56("quality-activity-56", "外包产品质量控制", "outsourcingProductQualityControl"),

    // Test Planning Requirements
    QUALITY_ACTIVITY_57("quality-activity-57", "试验的策划要求", "testPlanningRequirements"),

    // Test Adequacy/Coverage Analysis, Test Plan and Scheme Review Requirements
    QUALITY_ACTIVITY_58("quality-activity-58", "试验的充分性/覆盖性分析、试验大纲与方案评审要求", "adequacyTestCoverageAnalysisTestOutlineProtocolReviewRequirements"),

    // Test Process Quality Control Requirements
    QUALITY_ACTIVITY_59("quality-activity-59", "试验过程质量控制要求", "testProcessQualityControlRequirements"),

    // Test Result Analysis and Review Requirements
    QUALITY_ACTIVITY_60("quality-activity-60", "试验结果分析与评审要求", "testResultAnalysisAndReviewRequirements"),

    // Field Test Factory Quality Review Requirements
    QUALITY_ACTIVITY_61("quality-activity-61", "外场试验出厂质量评审", "externalTestFactoryQualityReview"),

    // Product and Service Delivery Requirements
    QUALITY_ACTIVITY_62("quality-activity-62", "产品和服务的交装要求", "deliveryRequirementsForProductsAndServices"),

    // After-sales Service Requirements
    QUALITY_ACTIVITY_63("quality-activity-63", "售后服务", "afterSalesService"),

    // Supervision and Evaluation Improvement Requirements
    QUALITY_ACTIVITY_64("quality-activity-64", "监督检查与评价改进", "superviseInspectEvaluateImprovement");

    private final String code;
    private final String description;
    private final String bookmarkKey;

    QualityItemBookmarkEnum(String code, String description, String bookmarkKey) {
        this.code = code;
        this.description = description;
        this.bookmarkKey = bookmarkKey;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public String getBookmarkKey() {
        return bookmarkKey;
    }

    public static Map<String, String> getQualityItemBookmarkMap() {
        return Arrays.stream(QualityItemBookmarkEnum.values())
                .collect(Collectors.toMap(QualityItemBookmarkEnum::getCode, QualityItemBookmarkEnum::getBookmarkKey));
    }
}
