package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.BudgetRecordDTO;
import com.chinasie.orion.domain.vo.BudgetRecordVO;
import com.chinasie.orion.service.BudgetRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;


import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * BudgetRecord 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08 20:08:40
 */
@RestController
@RequestMapping("/budgetRecord")
@Api(tags = "预算改变记录")
public class BudgetRecordController {

    @Autowired
    private BudgetRecordService budgetRecordService;


    /**
     * 获取预算改变记录
     *
     * @param
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取预算记录")
    @RequestMapping(value = "/getList", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】获取预算记录", type = "BudgetRecord", subType = "获取预算记录", bizNo = "{{#budgetId}}")
    public ResponseDTO<List<BudgetRecordVO>> getList(@RequestParam String budgetId, @RequestParam String type) throws Exception {
        List<BudgetRecordVO> rsp = budgetRecordService.getList(budgetId,type);
        return new ResponseDTO<>(rsp);
    }

}
