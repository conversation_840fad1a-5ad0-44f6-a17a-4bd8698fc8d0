package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.KeywordDto;
import com.chinasie.orion.domain.dto.ProjectRoleUserDTO;
import com.chinasie.orion.domain.dto.RoleUserParamDTO;
import com.chinasie.orion.domain.dto.UserQueryDTO;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.domain.vo.SimpleVO;
import com.chinasie.orion.sdk.domain.vo.business.RoleVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectRoleUserService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/06/10:48
 * @description:
 */
@RestController
@RequestMapping("/project-role-user")
@Api(tags = "项目角色用户")
public class ProjectRoleUserController {

    @Resource
    private ProjectRoleUserService projectRoleUserService;

    @ApiOperation("批量新增成员")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectRoleUserDTOList", dataType = "List")
    })
    @PostMapping("/saveBatch")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量新增成员", type = "项目角色用户", subType = "批量新增成员", bizNo = "")
    public ResponseDTO<List<String>> saveBatchProRoleUser(@RequestBody List<ProjectRoleUserDTO> projectRoleUserDTOList) throws Exception {
        return new ResponseDTO<>(projectRoleUserService.saveBatchProRoleUser(projectRoleUserDTOList));
    }


    @ApiOperation("项目立项_批量新增成员")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectRoleUserDTOList", dataType = "List")
    })
    @PostMapping("/approval/saveBatch")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】项目立项_批量新增成员", type = "项目角色用户", subType = "项目立项_批量新增成员", bizNo = "")
    public ResponseDTO<List<String>> saveBatchApprovalProjectRoleUser(@RequestBody List<ProjectRoleUserDTO> projectRoleUserDTOList) throws Exception {
        return new ResponseDTO<>(projectRoleUserService.saveBatchApprovalProjectRoleUser(projectRoleUserDTOList));
    }


    @ApiOperation("名称模糊查询成员列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", dataType = "String"),
            @ApiImplicitParam(name = "name", dataType = "String")
    })
    @PostMapping("/getListByName/{projectId}")
    @LogRecord(success = "【{USER{#logUserId}}】名称模糊查询成员列表", type = "项目角色用户", subType = "名称模糊查询成员列表", bizNo = "{{#projectId}}")
    public ResponseDTO<List<SimpleVo>> getProjectRoleUserList(@PathVariable("projectId") String projectId, @RequestParam(required = false) String name) throws Exception {
        return new ResponseDTO<>(projectRoleUserService.getProjectRoleUserList(projectId, name));
    }

    @ApiOperation("成员分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping("/getPage")
    @LogRecord(success = "【{USER{#logUserId}}】成员分页", type = "项目角色用户", subType = "成员分页", bizNo = "")
    public ResponseDTO<Page<ProjectRoleUserVO>> getProjectRoleUserPage(@RequestBody Page<UserQueryDTO> pageRequest) throws Exception {
        return new ResponseDTO<>(projectRoleUserService.getProjectRoleUserPage(pageRequest));
    }


    @ApiOperation("立项详情_成员分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping("/approval/getPage")
    @LogRecord(success = "【{USER{#logUserId}}】立项详情_成员分页", type = "项目角色用户", subType = "立项详情_成员分页", bizNo = "")
    public ResponseDTO<Page<ProjectRoleUserVO>> getProjectApprovalRoleUserPage(@RequestBody Page<UserQueryDTO> pageRequest) throws Exception {
        return new ResponseDTO<>(projectRoleUserService.getProjectApprovalRoleUserPage(pageRequest));
    }

    @ApiOperation("成员详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", dataType = "String")
    })
    @GetMapping("/detail/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】成员详情", type = "项目角色用户", subType = "成员详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectRoleUserVO> getProjectRoleUserDetail(@PathVariable("id") String id, @RequestParam(value = "pageCode", required = false) String pageCode) throws Exception {
        return new ResponseDTO<>(projectRoleUserService.getProjectRoleUserDetail(id, pageCode));
    }

    @ApiOperation("批量删除成员")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "idList", dataType = "List")
    })
    @DeleteMapping("/removeBatch")
    @LogRecord(success = "【{USER{#logUserId}}】批量删除成员", type = "项目角色用户", subType = "批量删除成员", bizNo = "")
    public ResponseDTO<Boolean> removeProjectRoleUser(@RequestBody List<String> idList) throws Exception {
        return new ResponseDTO<>(projectRoleUserService.removeProjectRoleUser(idList));
    }

    @ApiOperation("搜索项目下的用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "keywordDto", dataType = "KeywordDto")
    })
    @PostMapping("/search")
    @LogRecord(success = "【{USER{#logUserId}}】搜索项目下的用户", type = "项目角色用户", subType = "搜索项目下的用户", bizNo = "")
    public ResponseDTO<List<ProjectRoleUserSearchVO>> searchProjectUser(@RequestBody KeywordDto keywordDto) throws Exception {
        return new ResponseDTO<>(projectRoleUserService.searchProjectUser(keywordDto));
    }

    @ApiOperation(value = "获取当前用户所在项目所处的角色信息")
    @RequestMapping(value = "/current/role/info/list/{projectId}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】获取当前用户所在项目所处的角色信息", type = "项目角色用户", subType = "获取当前用户所在项目所处的角色信息", bizNo = "{{#projectId}}")
    public ResponseDTO<List<SimpleRoleVO>> getCurrentUserRoleByProjectId(@PathVariable("projectId") String projectId) throws Exception {
        return new ResponseDTO<>(projectRoleUserService.getCurrentUserRoleByProjectId(projectId));
    }

    @ApiOperation(value = "获取当前项目成员用户列表")
    @GetMapping(value = "/{projectId}/user/list")
    @LogRecord(success = "【{USER{#logUserId}}】获取当前项目成员用户列表", type = "项目角色用户", subType = "获取当前项目成员用户列表", bizNo = "{{#projectId}}")
    public ResponseDTO<List<ProjectUserInfoVO>> getUserInfoByProjectId(@PathVariable("projectId") String projectId) throws Exception {
        return new ResponseDTO<>(projectRoleUserService.listProjectUser(projectId));
    }

    @ApiOperation(value = "获取当前项目所有成员用户列表")
    @GetMapping(value = "/{projectId}/all/user/list")
    @LogRecord(success = "【{USER{#logUserId}}】获取当前项目所有成员用户列表", type = "项目角色用户", subType = "获取当前项目所有成员用户列表", bizNo = "{{#projectId}}")
    public ResponseDTO<List<ProjectUserInfoVO>> getAllUserInfoByProjectId(@PathVariable("projectId") String projectId) throws Exception {
        return new ResponseDTO<>(projectRoleUserService.listAllProjectUser(projectId));
    }

    @ApiOperation(value = "当前用户是否是项目成员")
    @PostMapping(value = "/current/user/{projectId}/role")
    @LogRecord(success = "【{USER{#logUserId}}】当前用户是否是项目成员", type = "项目角色用户", subType = "当前用户是否是项目成员", bizNo = "{{#projectId}}")
    public ResponseDTO<Boolean> projectExistCurrentUser(@PathVariable("projectId") String projectId) throws Exception {
        return new ResponseDTO<>(projectRoleUserService.projectExistCurrentUser(projectId));
    }


    @ApiOperation(value = "获取项目所有成员")
    @RequestMapping(value = "/{projectId}/allUser", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】获取项目所有成员", type = "项目角色用户", subType = "获取项目所有成员", bizNo = "{{#projectId}}")
    public ResponseDTO<List<SimpleRoleVO>> getAllUserByProjectId(@PathVariable("projectId") String projectId) throws Exception {
        return new ResponseDTO(projectRoleUserService.getAllUserByProjectId(projectId));
    }

    @ApiOperation(value = "获取项目资源中的所有部门列表")
    @RequestMapping(value = "/dept/list", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】获取项目资源中的所有部门列表", type = "项目角色用户", subType = "获取项目资源中的所有部门列表", bizNo = "{{#projectId}}")
    public ResponseDTO<List<SimpleVO>> getAllDeptLis(@RequestParam("projectId") String projectId) throws Exception {
        List<SimpleVO> simpleUserList = projectRoleUserService.getDeptListByProject(projectId);
        return new ResponseDTO(simpleUserList);
    }

    @ApiOperation(value = "获取用户所在项目所处的角色信息")
    @RequestMapping(value = "/user/role/info/list/{projectId}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】获取用户所在项目所处的角色信息", type = "项目角色用户", subType = "获取用户所在项目所处的角色信息", bizNo = "{{#projectId}}")
    public ResponseDTO<List<RoleVO>> getUserRoleByProjectId(@PathVariable("projectId") String projectId, @RequestParam(required = true) String userId) throws Exception {
        return new ResponseDTO<List<RoleVO>>(projectRoleUserService.getRoleByProjectAndUserId(projectId, userId));
    }


    @ApiOperation("获取项目下角色下的人员列表")
    @PostMapping(value = "/user/list")
    @LogRecord(success = "【{USER{#logUserId}}】获取项目下角色下的人员列表", type = "项目角色用户", subType = "获取项目下角色下的人员列表", bizNo = "")
    public ResponseDTO<List<String>> getUserIdToProjectIdsAndRoleIds(@Validated @RequestBody RoleUserParamDTO roleUserParamDTO) throws Exception {
        List<String> b = projectRoleUserService.getUserIdToProjectIdsAndRoleIds(roleUserParamDTO);
        return new ResponseDTO<>(b);
    }

    @ApiOperation(value = "获取项目经理成员")
    @RequestMapping(value = "/pm/allUser", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】获取项目经理成员", type = "项目角色用户", subType = "获取项目经理成员", bizNo = "")
    public ResponseDTO<List<ProjectRoleUserVO>> getUserByProjectIds(@RequestBody List<String> projectIds) throws Exception {
        return new ResponseDTO(projectRoleUserService.getUserByProjectIds(projectIds));
    }

    @ApiOperation(value = "根据项目ID和用户编号获取用户信息")
    @GetMapping(value = "/{projectId}/userInfo/{userCode}")
    @LogRecord(success = "【{USER{#logUserId}}】根据项目ID和用户编号获取用户信息", type = "项目角色用户", subType = "根据项目ID和用户编号获取用户信息", bizNo = "{{#projectId}}")
    public ResponseDTO<List<ProjectUserInfoVO>> getUserInfoByProjectId(@PathVariable("projectId") String projectId, @PathVariable("userCode") String userCode) throws Exception {
        return new ResponseDTO(projectRoleUserService.getUserInfoByProjectId(projectId,userCode));
    }
}
