package com.chinasie.orion.domain.vo;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
import java.lang.String;

import java.util.List;
import java.util.Objects;

/**
 * PersonMange VO对象
 *
 * <AUTHOR>
 * @since 2024-06-03 22:00:57
 */
@ApiModel(value = "PersonMangeVO对象", description = "人员管理")
@Data
public class PersonMangeVO extends ObjectVO implements Serializable {

    /**
     * 员工号
     */
    @ApiModelProperty(value = "员工号")
    private String number;


    /**
     * 人员名称
     */
    @ApiModelProperty(value = "人员名称")
    private String name;


    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String sex;


    /**
     * 身份证号码
     */
    @ApiModelProperty(value = "身份证号码")
    private String idCard;


    /**
     * 政治面貌
     */
    @ApiModelProperty(value = "政治面貌")
    private String politicalAffiliation;


    /**
     * 人员状(进入、离开)
     */
    @ApiModelProperty(value = "人员状(进入、离开)")
    private String personStatus;


    /**
     * 性质
     */
    @ApiModelProperty(value = "性质")
    private String nature;


    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;


    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String deptName;


    /**
     * 研究所
     */
    @ApiModelProperty(value = "研究所")
    private String instituteName;


    /**
     * 现任职务
     */
    @ApiModelProperty(value = "现任职务")
    private String nowPosition;


    /**
     * 基地名称
     */
    @ApiModelProperty(value = "基地名称")
    private String baseName;

    @ApiModelProperty(value = "接口部门")
    private String contactDeptName;
    /**
     * 接口部门
     */
    @ApiModelProperty(value = "接口部门")
    private String contactDept;
    /**
     * 接口部门
     */
    @ApiModelProperty(value = "接口部门编号")
    private String contactDeptCode;

    /**
     * 接口科室
     */
    @ApiModelProperty(value = "接口科室")
    private String contactOffice;

    @ApiModelProperty(value = "接口科室编号")
    private String contactOfficeCode;
    /**
     * 接口科室名称
     */
    @ApiModelProperty(value = "接口科室名称")
    private String contactOfficeName;
    /**
     * 接口人
     */
    @ApiModelProperty(value = "接口人")
    private String contactUser;

    /**
     * 接口人
     */
    @ApiModelProperty(value = "接口人名称")
    private String contactUserName;
    /**
     * 进入形式
     */
    @ApiModelProperty(value = "进入形式")
    private String enterMode;

    /**
     * 进入形式
     */
    @ApiModelProperty(value = "进入形式名称")
    private String enterModeName;

    /**
     * 大修/日常
     */
    @ApiModelProperty(value = "大修/日常")
    private String workType;
    /**
     * 大修/日常
     */
    @ApiModelProperty(value = "大修/日常")
    private String workTypeName;

//    /**
//     * 实际到厂时间
//     */
//    @ApiModelProperty(value = "实际到厂时间")
//    private Date actualEnterDate;
//
//
//    /**
//     * 实际离厂时间
//     */
//    @ApiModelProperty(value = "实际离厂时间")
//    private Date actualLeaveDate;

    /**
     * 离厂原因
     */
    @ApiModelProperty(value = "离厂原因描述")
    private String leaveReasonName;
    /**
     * 离厂原因
     */
    @ApiModelProperty(value = "离厂原因")
    private String leaveReason;


    /**
     * 离厂备注
     */
    @ApiModelProperty(value = "离厂备注")
    private String leaveRemark;


    /**
     * 计划到厂时间
     */
    @ApiModelProperty(value = "计划到厂时间")
    private Date planEnterDate;


    /**
     * 计划离厂时间
     */
    @ApiModelProperty(value = "计划离厂时间")
    private Date planLeaveDate;


    /**
     * 基地编码
     */
    @ApiModelProperty(value = "基地编码")
    private String baseCode;


    @ApiModelProperty(value = "基地内承担的主要项目：项目ID")
    private String basePlaceProject;
    @ApiModelProperty(value = "基地内承担的主要项目：项目ID")
    private String basePlaceProjectName;

    @ApiModelProperty(value = "大修轮次")
    private String repairRound;
    @ApiModelProperty(value = "身高/米")
    private String heightStr;
    @ApiModelProperty(value = "体重")
    private String weightStr;
    @ApiModelProperty(value = "职业禁忌症")
    private String jobTaboos;
    @ApiModelProperty(value = "职业禁忌症名称")
    private String jobTaboosName;


    @ApiModelProperty(value = "涉及控制区作业: 是/否 true/false")
    private Boolean designCtrlZoneOp;

    @ApiModelProperty(value = "化学品/毒物使用或接触作业")
    private Boolean chemicalToxinUseJob;



    @ApiModelProperty(value = "工作负责人")
    private Boolean workResPerson;

    @ApiModelProperty(value = "准备工程师")
    private Boolean preparationEngineer;

    @ApiModelProperty(value = "QC")
    private Boolean qcStr;

    @ApiModelProperty(value = "QC工作年限")
    private String qcWorkYear;

    @ApiModelProperty(value = "专职安全员")
    private Boolean fuTiSafOff;

    @ApiModelProperty(value = "兼职安全员")
    private Boolean paTiSafOff;

    @ApiModelProperty(value = "特种作业持证情况(含无损检测资质")
    private String speTaskCertSit;
    @ApiModelProperty(value = "特种作业持证情况(含无损检测资质) 名称")
    private String speTaskCertSitName;
    @ApiModelProperty(value = "一年内参与过集团内大修、高剂量人员(年个人剂量>8mSv为高剂量人员)")
    private String participateONot;
    @ApiModelProperty(value = "一年内参与过集团内大修、高剂量人员(年个人剂量>8mSv为高剂量人员)名称")
    private String participateONotName;


    @ApiModelProperty(value = "新人")
    private Boolean newcomer;

    @ApiModelProperty(value = "新人类型")
    private String newcomerType;

    @ApiModelProperty(value = "新人对口人")
    private String newcomerMatchPerson;

    @ApiModelProperty(value = "新人对口人编号")
    private String newcomerMatchPersonCode;

    @ApiModelProperty(value = "新人对口人名称")
    private String newcomerMatchPersonCodeName;

    @ApiModelProperty(value = "授权状态")
    private String authorizationStatus;


    @ApiModelProperty(value = "是否基地常驻")
    private Boolean isBasePermanent;

    @ApiModelProperty(value = "附件列表")
    private List<FileVO> fileVOList;


    @ApiModelProperty(value = "一年内参与过集团内大修，码值：是、否")
    private Boolean isJoinYearMajorRepair;

    @ApiModelProperty(value = "高剂量人员（年个人剂量>8mSv为高剂量人员），码值：是、否")
    private Boolean isHeightMeasurePerson;


    @ApiModelProperty(value = "进场倒计时（天）")
    private long inDays;

    @ApiModelProperty(value = "离开场倒计时（天）")
    private long outDays;

    @ApiModelProperty(value = "主工作中心")
    private String mainWorkCenter;

    @ApiModelProperty(value = "是否作业")
    private Boolean isJob;


    @ApiModelProperty(value = "实际入场日期")
    private Date actInDate;

    @ApiModelProperty(value = "实际离场日期")
    private Date actOutDate;

    @ApiModelProperty(value = "计划入场日期")
    private Date inDate;

    @ApiModelProperty(value = "计划离场日期")
    private Date outDate;

    @ApiModelProperty(value = "是否完成离厂交接，离场WBC测量(必要时)")
    private Boolean isFinishOutHandover;

    @ApiModelProperty(value = "是否再次入场")
    private Boolean isAgainIn;
    @ApiModelProperty(value = "入场离场时间")
    private List<Date> inAndOutDateList;

    /**
     * 人员性质
     */
    @ApiModelProperty(value = "人员性质")
    private String personnelNature;

    /**
     * 民族
     */
    @ApiModelProperty(value = "民族")
    private String nation;

    /**
     * 籍贯
     */
    @ApiModelProperty(value = "籍贯")
    private String homeTown;

    /**
     * 出生地
     */
    @ApiModelProperty(value = "出生地")
    private String birthPlace;

    /**
     * 现任职务
     */
    @ApiModelProperty(value = "现任职务")
    private String newPosition;

    /**
     * 职称
     */
    @ApiModelProperty(value = "职称")
    private String jobTitle;

    /**
     * 参加工作时间
     */
    @ApiModelProperty(value = "参加工作时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date joinWorkTime;

    /**
     * 加入ZGH时间
     */
    @ApiModelProperty(value = "加入ZGH时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date addZghTime;

    /**
     * 加入本单位时间
     */
    @ApiModelProperty(value = "加入本单位时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date addUnitTime;
}
