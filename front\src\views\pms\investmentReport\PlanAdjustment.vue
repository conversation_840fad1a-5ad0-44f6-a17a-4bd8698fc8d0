<template>
  <Layout
    :options="{ body: { scroll: true } }"
  >
    <div
      v-loading="loadStatus"
      class="details-content-item"
    >
      <!--      <div class="content-title">-->
      <!--        <div-->
      <!--          class="information-title information-title-flex"-->
      <!--        >-->
      <!--          <span>投资计划调整查询报表</span>-->
      <!--          <span>单位：万元</span>-->
      <!--        </div>-->
      <!--      </div>-->
      <OrionTable
        ref="tableRef"
        class="card-list-table"
        :options="baseTableOption"
        @smallSearch="smallSearch"
        @smallSearchChange="smallSearchChange"
        @selectionChange="selectionChange"
        @filterOpenChange="filterOpenChange"
      >
        <template #toolbarLeft>
          <BasicButton
            icon="sie-icon-daochu"
            type="primary"
            :disabled="selectionKeys.length===0"
            @click="exportTableByIds"
          >
            导出所选
          </BasicButton>
          <BasicButton
            icon="sie-icon-daochu"
            type="primary"
            @click="exportTable"
          >
            导出全部
          </BasicButton>
        </template>
        <template #filter>
          <div class="filter-wrap">
            <div class="padding-bottom-10">
              <TableHeadSearch
                v-show="showSearch"
                ref="searchRef"
                @click-search="clickSearch"
              />
            </div>
          </div>
        </template>

        <template #name="{record ,text}">
          <div class="flex flex-ac w-full">
            <Icon
              v-if="isPower('XM_list_button_04',record?.['rdAuthList']??[]) || isPower('XM_list_button_05',record?.['rdAuthList']??[])"
              :icon="`fa ${record['like']?'fa-star':'fa-star-o'}`"
              :color="record['like']?'orange':''"
              class="collect"
              @click="actionClick('collect',record)"
            />
            <Icon
              v-else
              :icon="`fa ${record['like']?'fa-star':'fa-star-o'}`"
              :color="record['like']?'orange':''"
            />
            <span
              v-if="isPower('XM_list_button_10',record?.['rdAuthList']??[])"
              class="action-btn flex-te"
              @click="actionClick('look',record)"
            >{{ text }}</span>
            <span
              v-else
              class="flex-te"
            >{{ text }}</span>
          </div>
        </template>
        <template
          v-if="!isTable"
          #otherContent="{dataSource}"
        >
          <div
            v-if="dataSource.length"
            ref="cardGrid"
            class="card-grid"
            :style="{'grid-template-columns': `repeat(${gridNum}, 1fr)`}"
          />
          <Empty
            v-else
            class="w-full h-full flex flex-ver flex-ac flex-pc"
            :image="Empty.PRESENTED_IMAGE_SIMPLE"
          />
        </template>
      </OrionTable>
    </div>
    <!--创建项目-->
  </Layout>
</template>

<script setup lang="ts">
import {
  h, onMounted, onUnmounted, Ref, ref, unref, watch, nextTick,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {
  BasicButton,
  BasicButtonGroup,
  DataStatusTag, downloadByData,
  Icon,
  isPower,
  Layout,
  OrionTable,
  useDrawer,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';

import { Empty, message, Modal } from 'ant-design-vue';

import { useUserStore } from '/@/store/modules/user';

import { join } from 'lodash-es';
import {
  deleteNewProject, deleteUserLike, postUserLike, pagechange,
} from './api';
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import TableHeadSearch from './utils/PlanAdjustment.vue';

const [registerCreate, { openDrawer: openDrawerCreate }] = useDrawer();
const router = useRouter();
const route = useRoute();
const isTable: Ref<boolean> = ref(false);
const tableRef: Ref = ref();
const cardGrid: Ref = ref();
const loading: Ref<boolean> = ref(false);
const loadStatus:Ref<boolean> = ref(false);
function formatNumToDecimals(inputValue) {
  let inputNumber = parseFloat(inputValue);
  if (isNaN(inputNumber)) {
    return '';
  }
  return inputNumber.toFixed(2).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

function formatNumToTenThousand(inputValue: number | string, precision: number = 10000, fixedNum:number = 2) {
  let inputNumber = parseFloat(inputValue);
  if (isNaN(inputNumber)) {
    return '';
  }
  const tenThousandValue = (inputNumber / precision).toFixed(fixedNum);
  const parts = tenThousandValue.split('.');
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  return parts.join('.');
}

const columns = [
  {
    title: '投资计划编号',
    dataIndex: 'number',
    fixed: 'left',
  },
  {
    title: '投资计划名称',
    dataIndex: 'name',
    fixed: 'left',
  },
  {
    title: '投资年份（Y）',
    dataIndex: 'yearName',
    customRender({ text }) {
      return text;
    },
    fixed: 'left',
  },
  {
    title: '调整状态',
    fixed: 'left',
    width: 160,
    customRender({ record }) {
      return h(DataStatusTag, {
        statusData: record.dataStatus,
      });
    },
  },
  // {
  //   title: '计划公司',
  //   dataIndex: 'companyName',
  // },
  {
    title: '项目编码',
    dataIndex: 'projectNumber',
  },
  {
    title: '项目名称',
    dataIndex: 'projectName',
  },
  {
    title: '项目状态',
    dataIndex: 'projectStatusName',
  },
  {
    title: '项目处室',
    dataIndex: 'rspDeptName',
  },
  {
    title: '项目负责人',
    dataIndex: 'rspUserName',
  },
  {
    title: '项目概算',
    align: 'right',
    customRender({ text }) {
      return formatNumToDecimals(text);
    },
    dataIndex: 'estimate',
  },
  {
    title: '总体预算',
    align: 'right',
    dataIndex: 'overallBudget',
    customRender({ text }) {
      return h('div', {
        title: text ? formatNumToTenThousand(text, 10000) : '',
      }, text ? formatNumToTenThousand(text, 10000) : '');
    },
    // customRender({ text }) {
    //   return formatNumToDecimals(text);
    // },
  },
  {
    title: '总体实际',
    align: 'right',
    dataIndex: 'overallReality',
    customRender({ text }) {
      return h('div', {
        title: text ? formatNumToTenThousand(text, 10000, 6) : '',
      }, text ? formatNumToTenThousand(text, 10000) : '');
    },
  },
  {
    title: '立项金额',
    align: 'right',
    dataIndex: 'projectAmount',
    customRender({ text }) {
      return h('div', {
        title: text ? formatNumToTenThousand(text, 10000, 6) : '',
      }, text ? formatNumToTenThousand(text, 10000) : '');
    },
  },
  {
    title: '合同金额',
    align: 'right',
    dataIndex: 'contractAmount',
    customRender({ text }) {
      return h('div', {
        title: text ? formatNumToTenThousand(text, 10000, 6) : '',
      }, text ? formatNumToTenThousand(text, 10000) : '');
    },
  },
  {
    title: '累计至（Y-1）年下达投资计划',
    align: 'right',
    customRender({ text }) {
      return formatNumToDecimals(text);
    },
    dataIndex: 'cutOffGiveY_1',
  },
  {
    title: '累计至（Y-1）年投资计划完成',
    align: 'right',
    customRender({ text }) {
      return formatNumToDecimals(text);
    },
    dataIndex: 'cutOffCompleteY_1',
  },
  {
    title: '调整情况说明',
    dataIndex: 'lastYearDoDesc',
  },
  {
    // 年度投资计划
    title: 'Y年投资计划',
    children: [
      {
        title: '调整前',
        align: 'right',
        customRender({ record }) {
          return formatNumToDecimals(record.old.total);
        },
      },
      {
        title: '调整后',
        align: 'right',
        // customRender({ text }) {
        //   return formatNumToDecimals(text);
        // },
        customRender({ record }) {
          return formatNumToDecimals(record.totalChange);
        },
      },
    ],
  },
  {
    title: '建筑工程',
    children: [
      {
        title: '调整前',
        align: 'right',
        customRender({ record }) {
          return formatNumToDecimals(record.old.architecture);
        },
      },
      {
        title: '调整后',
        align: 'right',
        customRender({ text }) {
          return formatNumToDecimals(text);
        },
        dataIndex: 'architecture',
      },
    ],
  },
  {
    title: '安装工程',
    children: [
      {
        title: '调整前',
        align: 'right',
        customRender({ record }) {
          return formatNumToDecimals(record.old.installation);
        },
      },
      {
        title: '调整后',
        align: 'right',
        customRender({ text }) {
          return formatNumToDecimals(text);
        },
        dataIndex: 'installation',
      },
    ],
  },
  {
    title: '设备投资',
    children: [
      {
        title: '调整前',
        align: 'right',
        customRender({ record }) {
          return formatNumToDecimals(record.old.device);
        },
      },
      {
        title: '调整后',
        align: 'right',
        customRender({ text }) {
          return formatNumToDecimals(text);
        },
        dataIndex: 'device',
      },
    ],
  },
  {
    title: '其他费用',
    children: [
      {
        title: '调整前',
        align: 'right',
        customRender({ record }) {
          return formatNumToDecimals(record.old.other);
        },
      },
      {
        title: '调整后',
        align: 'right',
        customRender({ text }) {
          return formatNumToDecimals(text);
        },
        dataIndex: 'other',
      },
    ],

  },
  {
    title: 'Y年形象进度',
    dataIndex: 'yearProcess',
  },
  {
    title: '调整金额',
    align: 'right',
    customRender({ text }) {
      return formatNumToDecimals(text);
    },
    dataIndex: 'totalChange',
  },
  {
    title: '1月投资计划',
    children: [
      {
        title: '调整前',
        align: 'right',

        customRender({ record }) {
          return formatNumToDecimals(record.old.monthInvestmentSchemes.filter((obj) => obj.name === '1月').map((obj) => obj.predicate)[0]);
        },
      },
      {
        title: '调整后',
        align: 'right',

        customRender({ record }) {
          return formatNumToDecimals(record.monthInvestmentSchemes.filter((obj) => obj.name === '1月').map((obj) => obj.predicate)[0]);
        },
      },
    ],
  },
  {
    title: '1-2月投资计划',
    children: [
      {
        title: '调整前',
        align: 'right',

        customRender({ record }) {
          return formatNumToDecimals(record.old.monthInvestmentSchemes.filter((obj) => obj.name === '1-2月').map((obj) => obj.predicate)[0]);
        },
      },
      {
        title: '调整后',
        align: 'right',

        customRender({ record }) {
          return formatNumToDecimals(record.monthInvestmentSchemes.filter((obj) => obj.name === '1-2月').map((obj) => obj.predicate)[0]);
        },
      },
    ],
  },
  {
    title: '1-3月投资计划',
    children: [
      {
        title: '调整前',
        align: 'right',

        customRender({ record }) {
          return formatNumToDecimals(record.old.monthInvestmentSchemes.filter((obj) => obj.name === '1-3月').map((obj) => obj.predicate)[0]);
        },
      },
      {
        title: '调整后',
        align: 'right',

        customRender({ record }) {
          return formatNumToDecimals(record.monthInvestmentSchemes.filter((obj) => obj.name === '1-3月').map((obj) => obj.predicate)[0]);
        },
      },
    ],
  },
  {
    // predicate
    title: '1-4月投资计划',
    children: [
      {
        title: '调整前',
        align: 'right',
        customRender({ record }) {
          return formatNumToDecimals(record.old.monthInvestmentSchemes.filter((obj) => obj.name === '1-4月').map((obj) => obj.predicate)[0]);
        },
      },
      {
        title: '调整后',
        align: 'right',

        customRender({ record }) {
          return formatNumToDecimals(record.monthInvestmentSchemes.filter((obj) => obj.name === '1-4月').map((obj) => obj.predicate)[0]);
        },
      },
    ],
  },
  {
    title: '1-5月投资计划',
    children: [
      {
        title: '调整前',
        align: 'right',
        customRender({ record }) {
          return formatNumToDecimals(record.old.monthInvestmentSchemes.filter((obj) => obj.name === '1-5月').map((obj) => obj.predicate)[0]);
        },
      },
      {
        title: '调整后',
        align: 'right',

        customRender({ record }) {
          return formatNumToDecimals(record.monthInvestmentSchemes.filter((obj) => obj.name === '1-5月').map((obj) => obj.predicate)[0]);
        },
      },
    ],
  },
  {
    title: '1-6月投资计划',
    children: [
      {
        title: '调整前',
        align: 'right',

        customRender({ record }) {
          return formatNumToDecimals(record.old.monthInvestmentSchemes.filter((obj) => obj.name === '1-6月').map((obj) => obj.predicate)[0]);
        },
      },
      {
        title: '调整后',
        align: 'right',
        customRender({ record }) {
          return formatNumToDecimals(record.monthInvestmentSchemes.filter((obj) => obj.name === '1-6月').map((obj) => obj.predicate)[0]);
        },
      },
    ],
  },
  {
    title: '1-7月投资计划',
    children: [
      {
        title: '调整前',
        align: 'right',

        customRender({ record }) {
          return formatNumToDecimals(record.old.monthInvestmentSchemes.filter((obj) => obj.name === '1-7月').map((obj) => obj.predicate)[0]);
        },
      },
      {
        title: '调整后',
        align: 'right',

        customRender({ record }) {
          return formatNumToDecimals(record.monthInvestmentSchemes.filter((obj) => obj.name === '1-7月').map((obj) => obj.predicate)[0]);
        },
      },
    ],
  },
  {
    title: '1-8月投资计划',
    children: [
      {
        title: '调整前',
        align: 'right',

        customRender({ record }) {
          return formatNumToDecimals(record.old.monthInvestmentSchemes.filter((obj) => obj.name === '1-8月').map((obj) => obj.predicate)[0]);
        },
      },
      {
        title: '调整后',
        align: 'right',

        customRender({ record }) {
          return formatNumToDecimals(record.monthInvestmentSchemes.filter((obj) => obj.name === '1-8月').map((obj) => obj.predicate)[0]);
        },
      },
    ],
  },
  {
    title: '1-9月投资计划',
    children: [
      {
        title: '调整前',
        align: 'right',

        customRender({ record }) {
          return formatNumToDecimals(record.old.monthInvestmentSchemes.filter((obj) => obj.name === '1-9月').map((obj) => obj.predicate)[0]);
        },
      },
      {
        title: '调整后',
        align: 'right',

        customRender({ record }) {
          return formatNumToDecimals(record.monthInvestmentSchemes.filter((obj) => obj.name === '1-9月').map((obj) => obj.predicate)[0]);
        },
      },
    ],
  },
  {
    title: '1-10月投资计划',
    children: [
      {
        title: '调整前',
        align: 'right',

        customRender({ record }) {
          return formatNumToDecimals(record.old.monthInvestmentSchemes.filter((obj) => obj.name === '1-10月').map((obj) => obj.predicate)[0]);
        },
      },
      {
        title: '调整后',
        align: 'right',

        customRender({ record }) {
          return formatNumToDecimals(record.monthInvestmentSchemes.filter((obj) => obj.name === '1-10月').map((obj) => obj.predicate)[0]);
        },
      },
    ],
  },
  {
    title: '1-11月投资计划',
    children: [
      {
        title: '调整前',
        align: 'right',

        customRender({ record }) {
          return formatNumToDecimals(record.old.monthInvestmentSchemes.filter((obj) => obj.name === '1-11月').map((obj) => obj.predicate)[0]);
        },
      },
      {
        title: '调整后',
        align: 'right',
        customRender({ record }) {
          return formatNumToDecimals(record.monthInvestmentSchemes.filter((obj) => obj.name === '1-11月').map((obj) => obj.predicate)[0]);
        },
      },
    ],
  },
  {
    title: '1-12月投资计划',
    children: [
      {
        title: '调整前',
        align: 'right',
        customRender({ record }) {
          return formatNumToDecimals(record.old.monthInvestmentSchemes.filter((obj) => obj.name === '1-12月').map((obj) => obj.predicate)[0]);
        },
      },
      {
        title: '调整后',
        align: 'right',
        customRender({ record }) {
          return formatNumToDecimals(record.monthInvestmentSchemes.filter((obj) => obj.name === '1-12月').map((obj) => obj.predicate)[0]);
        },
      },
    ],
  },
  {
    title: 'Y+1年计划投资',
    dataIndex: 'nextOneYear',
    align: 'right',
    customRender({ text }) {
      return formatNumToDecimals(text);
    },
  },
  {
    title: 'Y+2年计划投资',
    dataIndex: 'nextTwoYear',
    align: 'right',
    customRender({ text }) {
      return formatNumToDecimals(text);
    },
  },
  {
    title: 'Y+3年计划投资',
    dataIndex: 'nextThreeYear',
    align: 'right',
    customRender({ text }) {
      return formatNumToDecimals(text);
    },
  },
  {
    title: 'Y+4年计划投资',
    dataIndex: 'nextFourYear',
    align: 'right',
    customRender({ text }) {
      return formatNumToDecimals(text);
    },
  },
  {
    title: 'Y+5年计划投资',
    dataIndex: 'nextFiveYear',
    align: 'right',
    customRender({ text }) {
      return formatNumToDecimals(text);
    },
  },
  {
    title: '备注',
    width: '50px',
    dataIndex: 'remark',
  },
];
async function clickSearch() {
  await nextTick();
  tableRef.value.reload();
}
const searchRef: Ref = ref();
const keyword = ref<string>(route.query?.homePageProject?.toString() ?? '');
const selectionKeys = ref<string[]>([]);
const baseTableOption = {
  rowSelection: {},
  columns,
  isDefaultFilterOpen: true,
  filterOpenDefault: true,
  smallSearchField: ['name', 'number'],
  api: async (params) => {
    const data = await pagechange({
      orders: params.orders,
      pageNum: params.pageNum,
      pageSize: params.pageSize,
      query: {
        name: unref(keyword),
        ...unref(searchRef.value.modal),
        yearName: searchRef.value.modal.yearName ? searchRef.value.modal.yearName.join('-') : '',
        companyName: searchRef.value.modal.companyName ? searchRef.value.modal.companyName.join() : '',
      },
    });
    loading.value = false;
    // setTimeout(() => { showSearch.value = false; }, 300);
    return data;
  },
  showToolButton: false,
  isFilter2: true,
  // immediate: false,
};
const showSearch = ref(true);

onUnmounted(() => {
  window.removeEventListener('resize', onResize);
});

watch(() => isTable.value, (val) => {
  if (val) {
    document.querySelector('.table-other-content')
      .setAttribute('style', 'height:0px;');
  }
});
function filterOpenChange() {
  showSearch.value = !showSearch.value;
}

const gridNum: Ref<number> = ref();

function onResize() {
  gridNum.value = parseInt(window.innerWidth / 350);
}

function exportTable() {
  let isEmpty = tableRef.value.getDataSource().length === 0;
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk: async () => {
      loadStatus.value = true;
      const res = await downloadByData('/pms/investmentscheme-report/exportchange/excel', {
        isEmpty,
        name: unref(keyword),
        ...unref(searchRef.value.modal),
        yearName: searchRef.value.modal.yearName ? searchRef.value.modal.yearName.join('-') : '',
        companyName: searchRef.value.modal.companyName ? searchRef.value.modal.companyName.join() : '',
      }, '', 'POST', true, false, '导出处理完成，现在开始下载');
      loadStatus.value = false;
    },
  });
}
function selectionChange({ rows, keys }) {
  selectionKeys.value = keys;
}

function exportTableByIds() {
  let isEmpty = tableRef.value.getDataSource().length === 0;
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk: async () => {
      loadStatus.value = true;
      const res = await downloadByData('/pms/investmentscheme-report/exportchange/excel', {
        isEmpty,
        ids: selectionKeys.value,
      }, '', 'POST', true, false, '导出处理完成，现在开始下载');
      loadStatus.value = false;
    },
  });
}

// 创建项目
function handleCreatePro() {
  const {
    id,
    name,
    orgId,
  } = {
    name: '',
    orgId: '',
    ...useUserStore().getUserInfo,
  };
  openDrawerCreate(true, {
    resOrg: orgId,
    resPerson: id,
    resPersonName: name,
  });
}

// 操作区点击事件
const actionClick = async (key, record) => {
  switch (key) {
    case 'edit':
      openDrawerCreate(true, record);
      break;
    case 'del':
      Modal.confirm({
        title: '删除确认提示？',
        content: '请确认是否删除该项目，删除后不可恢复？',
        onOk() {
          return new Promise((resolve) => {
            deleteNewProject([record.id])
              .then(() => {
                updateList();
                message.success('删除成功');
                resolve(true);
              })
              .finally(() => {
                resolve('');
              });
          });
        },
        onCancel() {
          Modal.destroyAll();
        },
      });
      break;
    case 'look':
      return router.push({
        name: 'ProDetails',
        params: {
          id: record.id,
        },
      });
      // 收藏
    case 'collect':
      if (record.like) {
        Modal.confirm({
          title: '温馨提示',
          content: '请确认是否取消关注该项目？',
          onOk() {
            return new Promise((resolve) => {
              deleteUserLike([record.like.id])
                .then(() => {
                  updateList();
                  message.success('已取消关注');
                  resolve(true);
                })
                .catch(() => {
                  resolve('');
                });
            });
          },
          onCancel() {
            Modal.destroyAll();
          },
        });
      } else {
        if (loading.value) return;
        loading.value = true;
        await postUserLike({
          projectId: record.id,
        });
        updateList();
        message.success(`${record.name}关注成功！`);
      }
      break;
  }
};

// 模糊搜索
function smallSearch(val) {
  keyword.value = val;
  updateList();
}

function smallSearchChange(val) {
  keyword.value = val;
}
function updateList() {
  tableRef.value?.reload();
}
</script>

<style scoped lang="less">

.padding-bottom-10{
  padding-bottom:  ~`getPrefixVar('content-margin')`;
}
.card-grid {
  display: grid;
  gap: 16px 20px;
}

.hide-card :deep(.table-other-content ) {
  height: 0 !important;
}

.collect {
  cursor: pointer;
}

:deep(.card-list-table) {
  .form-wrap{
    margin-bottom:  ~`getPrefixVar('content-margin')`;
  }
  .ant-btn-group {
    margin-left: auto;

    .ant-btn + .ant-btn {
      margin-left: 0;
    }

    & + .card-list-table {
      width: auto;
      flex: 0;

      .ant-input-search {
        width: 220px;
      }
    }
  }
}
.content-title{
  padding: 20px 20px 0 20px;
  padding: var(--ant-button-margin);
}
:deep(.information-title) {
  font-size: 18px;
  font-weight: 500;
  color: rgba(0,0,0,0.85);
  line-height: 28px;
  position: relative;
  padding-left: 10px;
  border-bottom: 1px solid #cccccc;
  padding-bottom: 5px;
  &:before {
    content: '';
    height: 18px;
    width: 4px;
    background: ~`getPrefixVar('primary-color') `;
    display: inline-block;
    position: absolute;
    top: 5px;
    left: 0px;
  }
}

:deep(.information-title-flex){
  display: flex;
  width: 100%;
  justify-content: space-between;
  span+span{
    font-size: 14px;
    color: #959191;
  }
}
</style>
