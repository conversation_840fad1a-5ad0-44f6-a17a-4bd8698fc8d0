package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;

/**
 * ProjectFinanceIndex Entity对象
 *
 * <AUTHOR>
 * @since 2024-08-15 16:41:01
 */
@TableName(value = "pmsx_project_finance_index")
@ApiModel(value = "ProjectFinanceIndexEntity对象", description = "项目-财务指标")
@Data

public class ProjectFinanceIndex extends  ObjectEntity  implements Serializable{

    /**
     * 指标描述
     */
    @ApiModelProperty(value = "指标描述")
    @TableField(value = "index_name")
    private String indexName;

    /**
     * 指标费用
     */
    @ApiModelProperty(value = "指标费用")
    @TableField(value = "index_cost")
    private BigDecimal indexCost;

    /**
     * 指标计划金额
     */
    @ApiModelProperty(value = "指标计划金额")
    @TableField(value = "index_plan")
    private BigDecimal indexPlan;

    /**
     * 达成率
     */
    @ApiModelProperty(value = "达成率")
    @TableField(value = "achievement_rate")
    private String achievementRate;

    /**
     * 结余费用
     */
    @ApiModelProperty(value = "结余费用")
    @TableField(value = "residue")
    private BigDecimal residue;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    private String projectId;

}
