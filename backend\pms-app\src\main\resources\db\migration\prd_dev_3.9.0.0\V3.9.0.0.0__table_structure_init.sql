

ALTER TABLE `ncf_form_purchase_request`
    ADD COLUMN `applicant_dept_id` varchar(255) NULL COMMENT '申请部门id' AFTER `warranty_level`;


ALTER TABLE `ncf_form_purchase_request`
    ADD COLUMN `applicant_user_id` varchar(255) NULL COMMENT '申请人id' AFTER `applicant_user`;

ALTER TABLE `ncf_form_purchase_request_detail`
    ADD COLUMN `project_code` varchar(64) NULL COMMENT '采购申请号' AFTER `main_table_id`;



CREATE TABLE `pms_ncf_form_purch_order_collect` (
                                                    `order_number` varchar(256) DEFAULT NULL COMMENT '订单编号',
                                                    `po_order_number` varchar(256) DEFAULT NULL COMMENT 'PO订单号',
                                                    `commerce_channel_order_number` varchar(256) DEFAULT NULL COMMENT '电商渠道订单号',
                                                    `enterprise_name` varchar(256) DEFAULT NULL COMMENT '企业名称',
                                                    `pr_company_name` varchar(256) DEFAULT NULL COMMENT 'PR公司名称',
                                                    `department` varchar(256) DEFAULT NULL COMMENT '部门',
                                                    `contract_id` varchar(256) DEFAULT NULL COMMENT '供应商编码',
                                                    `contract_name` varchar(256) DEFAULT NULL COMMENT '供应商名称',
                                                    `order_placer` varchar(256) DEFAULT NULL COMMENT '下单人',
                                                    `order_phone_number` varchar(256) DEFAULT NULL COMMENT '下单人电话',
                                                    `order_time` datetime DEFAULT NULL COMMENT '下单时间',
                                                    `reconciler` varchar(256) DEFAULT NULL COMMENT '对账人',
                                                    `consignee` varchar(256) DEFAULT NULL COMMENT '收货负责人',
                                                    `receipt_reviewer` varchar(256) DEFAULT NULL COMMENT '收货审核人',
                                                    `payment_manager` varchar(256) DEFAULT NULL COMMENT '支付负责人',
                                                    `acceptance_method` varchar(256) DEFAULT NULL COMMENT '验收方式',
                                                    `settlement_method` varchar(256) DEFAULT NULL COMMENT '结算方式',
                                                    `request_delivery_date` datetime DEFAULT NULL COMMENT '要求到货日期',
                                                    `total_order_amount` decimal(10,2) DEFAULT NULL COMMENT '订单总金额',
                                                    `id` varchar(64) NOT NULL COMMENT '主键',
                                                    `class_name` varchar(64) DEFAULT NULL COMMENT '类名',
                                                    `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                                    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                    `owner_id` varchar(64) DEFAULT NULL COMMENT '数据所有者',
                                                    `create_time` datetime NOT NULL COMMENT '创建时间',
                                                    `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
                                                    `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                                    `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                                    `org_id` varchar(64) NOT NULL COMMENT '业务组织ID',
                                                    `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
                                                    `logic_status` int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
                                                    `number` varchar(64) DEFAULT NULL COMMENT '编码',
                                                    `order_pay_day` decimal(10,2) DEFAULT NULL COMMENT '订单待支付',
                                                    `order_confirmation_time` datetime DEFAULT NULL COMMENT '订单确认时间',
                                                    `order_approval_time` datetime DEFAULT NULL COMMENT '订单审批时间',
                                                    `paid_time` datetime DEFAULT NULL COMMENT '支付时间',
                                                    `invoicing_time` datetime DEFAULT NULL COMMENT '开票时间',
                                                    `application_for_invoicing_time` datetime DEFAULT NULL COMMENT '申请开票时间',
                                                    `reconciliation_confirmation_time` datetime DEFAULT NULL COMMENT '对账确认时间',
                                                    `reconciliation_application_time` datetime DEFAULT NULL COMMENT '对账申请时间',
                                                    `used_time` int(4) DEFAULT NULL COMMENT ' 发货耗时',
                                                    `time_of_delivery` datetime DEFAULT NULL COMMENT '订单最后一次交货时间',
                                                    `time_of_last_receipt` datetime DEFAULT NULL COMMENT '订单最后一次确认收货时间',
                                                    `order_state` varchar(256) DEFAULT NULL COMMENT '订单状态',
                                                    `return_amount` decimal(10,2) DEFAULT NULL COMMENT '退货金额',
                                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='采购-商城集采订单（总表）';

CREATE TABLE `pms_ncf_form_purch_order_detail` (
                                                   `contract_number` varchar(256) DEFAULT NULL COMMENT '框架协议号',
                                                   `tax_not_included` decimal(10,2) DEFAULT NULL COMMENT '不含税金额',
                                                   `tax` varchar(256) DEFAULT NULL COMMENT '增值税率',
                                                   `total_price` varchar(256) DEFAULT NULL COMMENT '总价',
                                                   `unit_price` varchar(256) DEFAULT NULL COMMENT '单价',
                                                   `unit` varchar(256) DEFAULT NULL COMMENT '计量单位',
                                                   `purchase_quantity` varchar(256) DEFAULT NULL COMMENT '采购数量',
                                                   `iten_type` varchar(256) DEFAULT NULL COMMENT '商品类型',
                                                   `order_number` varchar(256) DEFAULT NULL COMMENT '订单编号',
                                                   `contract_name` varchar(256) DEFAULT NULL COMMENT '供应商名称',
                                                   `return_amount` decimal(10,2) DEFAULT NULL COMMENT '退货金额',
                                                   `enterprise_name` varchar(256) DEFAULT NULL COMMENT '企业名称',
                                                   `order_placer` varchar(256) DEFAULT NULL COMMENT '下单人',
                                                   `order_time` datetime DEFAULT NULL COMMENT '下单时间',
                                                   `time_of_delivery` datetime DEFAULT NULL COMMENT '订单最后一次交货时间',
                                                   `time_of_last_receipt` datetime DEFAULT NULL COMMENT '订单最后一次确认收货时间',
                                                   `used_time` int(4) DEFAULT NULL COMMENT '发货耗时',
                                                   `reconciliation_application_time` datetime DEFAULT NULL COMMENT '对账申请时间',
                                                   `reconciliation_confirmation_time` datetime DEFAULT NULL COMMENT '对账确认时间',
                                                   `application_for_invoicing_time` datetime DEFAULT NULL COMMENT '申请开票时间',
                                                   `invoicing_time` datetime DEFAULT NULL COMMENT '开票时间',
                                                   `paid_time` datetime DEFAULT NULL COMMENT '支付时间',
                                                   `po_order_number` varchar(256) DEFAULT NULL COMMENT 'PO订单号',
                                                   `department` varchar(256) DEFAULT NULL COMMENT '部门',
                                                   `contract_id` varchar(256) DEFAULT NULL COMMENT '供应商编码',
                                                   `payment_manager` varchar(256) DEFAULT NULL COMMENT '支付负责人',
                                                   `acceptance_method` varchar(256) DEFAULT NULL COMMENT '验收方式',
                                                   `order_total_amount` decimal(10,2) DEFAULT NULL COMMENT '订单总金额（含税含费）',
                                                   `purch_req_doc_code` varchar(256) DEFAULT NULL COMMENT '采购申请号',
                                                   `pr_project_id` varchar(256) DEFAULT NULL COMMENT 'PR行项目',
                                                   `order_state` varchar(256) DEFAULT NULL COMMENT '订单状态',
                                                   `commodity_background_category` varchar(256) DEFAULT NULL COMMENT '商品后台类目',
                                                   `item_coding` varchar(256) DEFAULT NULL COMMENT '单品编码',
                                                   `item_name` varchar(256) DEFAULT NULL COMMENT '单品名称',
                                                   `e_commerce_order_number` varchar(256) DEFAULT NULL COMMENT '电商订单编号',
                                                   `suborder_number` varchar(256) DEFAULT NULL COMMENT '子订单号',
                                                   `order_amount` decimal(10,2) DEFAULT NULL COMMENT '订单金额',
                                                   `amount_payable` decimal(10,2) DEFAULT NULL COMMENT '应付金额',
                                                   `settlement_status` varchar(256) DEFAULT NULL COMMENT '结算状态',
                                                   `order_confirmation_time` datetime DEFAULT NULL COMMENT '订单确认时间',
                                                   `order_approval_time` datetime DEFAULT NULL COMMENT '订单审批时间',
                                                   `pr_company_name` varchar(256) DEFAULT NULL COMMENT 'PR公司名称',
                                                   `commerce_channel_order_number` varchar(256) DEFAULT NULL COMMENT '电商渠道订单号',
                                                   `reconciler` varchar(256) DEFAULT NULL COMMENT '对账人',
                                                   `consignee` varchar(256) DEFAULT NULL COMMENT '收货负责人',
                                                   `receipt_reviewer` varchar(256) DEFAULT NULL COMMENT '收货审核人',
                                                   `settlement_method` varchar(256) DEFAULT NULL COMMENT '结算方式',
                                                   `request_delivery_date` datetime DEFAULT NULL COMMENT '要求到货日期',
                                                   `order_phone_number` varchar(256) DEFAULT NULL COMMENT '下单人电话',
                                                   `id` varchar(64) NOT NULL COMMENT '主键',
                                                   `class_name` varchar(64) DEFAULT NULL COMMENT '类名',
                                                   `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                                   `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                   `owner_id` varchar(64) DEFAULT NULL COMMENT '数据所有者',
                                                   `create_time` datetime NOT NULL COMMENT '创建时间',
                                                   `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人',
                                                   `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                                   `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                                   `org_id` varchar(64) NOT NULL COMMENT '业务组织ID',
                                                   `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
                                                   `logic_status` int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
                                                   `number` varchar(64) DEFAULT NULL COMMENT '编码',
                                                   `order_pay_day` varchar(256) DEFAULT NULL COMMENT '订单待支付',
                                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商城集采订单（明细表）';