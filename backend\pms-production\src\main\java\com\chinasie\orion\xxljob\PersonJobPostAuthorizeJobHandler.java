package com.chinasie.orion.xxljob;

import cn.hutool.core.date.DateUtil;
import com.chinasie.orion.service.MajorRepairPlanService;
import com.chinasie.orion.service.PersonJobPostAuthorizeService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/28/0:17
 * @description:
 */

@Component
public class PersonJobPostAuthorizeJobHandler {

    @Autowired
    private PersonJobPostAuthorizeService personJobPostAuthorizeService;

    @XxlJob("personJobPostAuthorizeVerifyEffect")
    public void personJobPostAuthorizeVerifyEffect() {
        try {
            XxlJobHelper.log("人员岗位培训落地定时验证是否生效，开始时间：{}", DateUtil.date());
            personJobPostAuthorizeService.personJobPostAuthorizeVerifyEffect();
        } catch (Exception e) {
            XxlJobHelper.log("人员岗位培训落地定时验证是否生效，开始时间：{}，执行异常，原因：{}", DateUtil.date(), e.getMessage(), e);
        }
    }
}
