<template>
  <div class="information">
    <div class="information-content">
      <div class="content-title">
        <div
          class="information-title"
        >
          {{ formData.year }}年度投资计划申报表
        </div>
      </div>
      <div class="project-table">
        <a-row
          :gutter="[20, 20]"
          class="information-row"
        >
          <template
            v-for="item in formFieldList"
            :key="item.field"
          >
            <ACol
              :span="item.span"
              class="task-item"
            >
              <div class="item-title">
                {{ item.label }}:
              </div>
              <div
                class="item-value flex-te"
              >
                {{
                  item.formatter
                    ? item.formatter(formData[item.field])
                    :formData[item.field]
                }}
              </div>
            </ACol>
          </template>
        </a-row>
      </div>
    </div>

    <template
      v-for="(item,index) in formContentList"
      :key="index"
    >
      <div class="information-content">
        <div class="content-title">
          <div
            class="information-title"
          >
            {{ item.label }}
          </div>
        </div>
        <div class="form-content-value flex-te3">
          {{ formData[item.field] }}
        </div>
      </div>
    </template>
  </div>
</template>
<script lang="ts">
import {
  defineComponent, inject, reactive, toRefs, watch, ref,
} from 'vue';
import {
  Row, Col,
} from 'ant-design-vue';
import {
  initMonthlyForm,
} from '../index';

export default defineComponent({
  name: 'Information',
  components: {
    ARow: Row,
    ACol: Col,
  },
  props: {
    showApprove: {
      type: Boolean,
      default: false,
    },
  },
  setup() {
    const formData = inject('formData', {});
    let monthNumber = Number(formData.value.month.split('月')[0]);
    const state = reactive({
      formData: formData?.value,
      formFieldList: initMonthlyForm(monthNumber),
      formContentList: [
        {
          label: '本月执行偏差原因及纠偏措施',
          field: 'reason',
        },
        {
          label: '总体进度执行情况',
          field: 'totalProcess',
        },
        {
          label: '项目总体进度滞后情况',
          field: 'delayDesc',
        },
        {
          label: '本月进度执行情况',
          field: 'monthProcess',
        },
        {
          label: '下月进度执行情况',
          field: 'nextProcess',
        },
      ],
    });

    watch(
      () => formData?.value,
      (val) => {
        state.formData = val;
      },
    );
    return {
      ...toRefs(state),
    };
  },
});
</script>
<style lang="less" scoped>
.information {
  min-height: 100%;
  padding-bottom: 20px;
  .content-title{
    padding: 20px 20px 0 20px;
  }
  .information-content{

  }
  .information-row {
    padding: 20px;
    width: 100%;
  }
  .task-item {
    display: flex;
    line-height: 30px;
    min-height: 30px;
    .item-title {
      padding-right: 20px;
      color: #000000a5;
    }
    .item-value {
      flex: 1;
    }
  }
  .form-content-value{
      padding:0 20px;
      line-height: 30px;
      margin-bottom: 20px;
  }
}
</style>
