package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * GoodsServiceStore Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-25 20:59:53
 */
@TableName(value = "pmsx_goods_service_store")
@ApiModel(value = "GoodsServiceStore对象", description = "物资/服务入库表")
@Data
public class GoodsServiceStore extends ObjectEntity implements Serializable{

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number" )
    private String number;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField(value = "name" )
    private String name;


    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @TableField(value = "project_id" )
    private String projectId;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @TableField(value = "description" )
    private String description;

    /**
     * 类型对应的字典编码
     */
    @ApiModelProperty(value = "类型对应的字典编码")
    @TableField(value = "type_code" )
    private String typeCode;

    /**
     * 物资服务编码
     */
    @ApiModelProperty(value = "物资服务编码")
    @TableField(value = "goods_service_number" )
    private String goodsServiceNumber;

    /**
     * 规格型号
     */
    @ApiModelProperty(value = "规格型号")
    @TableField(value = "norms_model" )
    private String normsModel;

    /**
     * 计量单位对应数据字典
     */
    @ApiModelProperty(value = "计量单位对应数据字典")
    @TableField(value = "unit_code" )
    private String unitCode;

    /**
     * 总入库数量
     */
    @ApiModelProperty(value = "总入库数量")
    @TableField(value = "total_store_amount" )
    private BigDecimal totalStoreAmount;

    /**
     * 需求时间
     */
    @ApiModelProperty(value = "需求时间")
    @TableField(value = "demand_time" )
    private Date demandTime;

    /**
     * 入库日期
     */
    @ApiModelProperty(value = "入库日期")
    @TableField(value = "store_time" )
    private Date storeTime;

    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    @TableField(value = "store_amount" )
    private BigDecimal storeAmount;

}
