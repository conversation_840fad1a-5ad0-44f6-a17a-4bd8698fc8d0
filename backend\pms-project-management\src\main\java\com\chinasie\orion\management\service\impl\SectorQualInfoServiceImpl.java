package com.chinasie.orion.management.service.impl;

import com.chinasie.orion.management.domain.dto.SectorQualInfoDTO;
import com.chinasie.orion.management.domain.entity.SectorQualInfo;
import com.chinasie.orion.management.domain.vo.SectorQualInfoVO;
import com.chinasie.orion.management.repository.SectorQualInfoMapper;
import com.chinasie.orion.management.service.SectorQualInfoService;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * SectorQualInfo 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-26 10:59:54
 */
@Service
@Slf4j
public class SectorQualInfoServiceImpl extends  OrionBaseServiceImpl<SectorQualInfoMapper, SectorQualInfo>   implements SectorQualInfoService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public SectorQualInfoVO detail(String id, String pageCode) throws Exception {
        SectorQualInfo sectorQualInfo =this.getById(id);
        SectorQualInfoVO result = BeanCopyUtils.convertTo(sectorQualInfo,SectorQualInfoVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param sectorQualInfoDTO
     */
    @Override
    public  String create(SectorQualInfoDTO sectorQualInfoDTO) throws Exception {
        SectorQualInfo sectorQualInfo =BeanCopyUtils.convertTo(sectorQualInfoDTO,SectorQualInfo::new);
        this.save(sectorQualInfo);

        String rsp=sectorQualInfo.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param sectorQualInfoDTO
     */
    @Override
    public Boolean edit(SectorQualInfoDTO sectorQualInfoDTO) throws Exception {
        SectorQualInfo sectorQualInfo =BeanCopyUtils.convertTo(sectorQualInfoDTO,SectorQualInfo::new);

        this.updateById(sectorQualInfo);

        String rsp=sectorQualInfo.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<SectorQualInfoVO> pages( Page<SectorQualInfoDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<SectorQualInfo> condition = new LambdaQueryWrapperX<>( SectorQualInfo. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(SectorQualInfo::getCreateTime);


        Page<SectorQualInfo> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), SectorQualInfo::new));

        PageResult<SectorQualInfo> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<SectorQualInfoVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<SectorQualInfoVO> vos = BeanCopyUtils.convertListTo(page.getContent(), SectorQualInfoVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public List<SectorQualInfoVO> list(String supplierCode) throws Exception {

        List<SectorQualInfo> sectorQualInfos =  this.getBaseMapper().selectList(SectorQualInfo::getSupplierCode,supplierCode);
        List<SectorQualInfoVO> vos = BeanCopyUtils.convertListTo(sectorQualInfos, SectorQualInfoVO::new);
        return vos;
    }

    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "采购合同板块资审信息导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", SectorQualInfoDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        SectorQualInfoExcelListener excelReadListener = new SectorQualInfoExcelListener();
        EasyExcel.read(inputStream,SectorQualInfoDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<SectorQualInfoDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("采购合同板块资审信息导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<SectorQualInfo> sectorQualInfoes =BeanCopyUtils.convertListTo(dtoS,SectorQualInfo::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::SectorQualInfo-import::id", importId, sectorQualInfoes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<SectorQualInfo> sectorQualInfoes = (List<SectorQualInfo>) orionJ2CacheService.get("pmsx::SectorQualInfo-import::id", importId);
        log.info("采购合同板块资审信息导入的入库数据={}", JSONUtil.toJsonStr(sectorQualInfoes));

        this.saveBatch(sectorQualInfoes);
        orionJ2CacheService.delete("pmsx::SectorQualInfo-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::SectorQualInfo-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<SectorQualInfo> condition = new LambdaQueryWrapperX<>( SectorQualInfo. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(SectorQualInfo::getCreateTime);
        List<SectorQualInfo> sectorQualInfoes =   this.list(condition);

        List<SectorQualInfoDTO> dtos = BeanCopyUtils.convertListTo(sectorQualInfoes, SectorQualInfoDTO::new);

        String fileName = "采购合同板块资审信息数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", SectorQualInfoDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<SectorQualInfoVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class SectorQualInfoExcelListener extends AnalysisEventListener<SectorQualInfoDTO> {

        private final List<SectorQualInfoDTO> data = new ArrayList<>();

        @Override
        public void invoke(SectorQualInfoDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<SectorQualInfoDTO> getData() {
            return data;
        }
    }


}
