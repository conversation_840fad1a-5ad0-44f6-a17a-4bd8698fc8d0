package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * SupplierRestrictedRecord Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@TableName(value = "ncf_form_supplier_restricted_record")
@ApiModel(value = "SupplierRestrictedRecordEntity对象", description = "受限事件记录")
@Data

public class SupplierRestrictedRecord extends ObjectEntity implements Serializable {

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @TableField(value = "supplier_code")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @TableField(value = "supplier_name")
    private String supplierName;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    @TableField(value = "serial_number")
    private String serialNumber;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_number")
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @TableField(value = "contract_name")
    private String contractName;

    /**
     * 申请编号
     */
    @ApiModelProperty(value = "申请编号")
    @TableField(value = "application_number")
    private String applicationNumber;

    /**
     * 受限类型
     */
    @ApiModelProperty(value = "受限类型")
    @TableField(value = "restricted_type")
    private String restrictedType;

    /**
     * 整改状态
     */
    @ApiModelProperty(value = "整改状态")
    @TableField(value = "rectification_status")
    private String rectificationStatus;

    /**
     * 黑名单类型
     */
    @ApiModelProperty(value = "黑名单类型")
    @TableField(value = "blacklist_type")
    private String blacklistType;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    @TableField(value = "project_name")
    private String projectName;

    /**
     * 申报日期
     */
    @ApiModelProperty(value = "申报日期")
    @TableField(value = "declaration_date")
    private Date declarationDate;

    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
    @TableField(value = "applicant")
    private String applicant;

    /**
     * 申报公司
     */
    @ApiModelProperty(value = "申报公司")
    @TableField(value = "declaring_company")
    private String declaringCompany;

    /**
     * 内容描述
     */
    @ApiModelProperty(value = "内容描述")
    @TableField(value = "content_description")
    private String contentDescription;

    /**
     * 审批完成时间
     */
    @ApiModelProperty(value = "审批完成时间")
    @TableField(value = "approval_completion_time")
    private String approvalCompletionTime;

    /**
     * 是否解冻
     */
    @ApiModelProperty(value = "是否解冻")
    @TableField(value = "whether_thawed")
    private String whetherThawed;

    /**
     * 受限范围
     */
    @ApiModelProperty(value = "受限范围")
    @TableField(value = "restricted_scope")
    private String restrictedScope;

    /**
     * 集团发送SAP
     */
    @ApiModelProperty(value = "集团发送SAP")
    @TableField(value = "group_sends_sap")
    private String groupSendsSap;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @TableField(value = "main_table_id")
    private String mainTableId;

}
