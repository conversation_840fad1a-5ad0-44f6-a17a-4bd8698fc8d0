import { Graph } from '@antv/x6';
import {
  nextTick,
  onMounted, ref, Ref, unref,
} from 'vue';
import { fetchData } from '/@/views/pms/overhaulManagement/hooks/data';
import { findNode } from 'lyra-component-vue3';

// 一级节点padding
const rectPadding: number = 20;
// 一级节点间距
const rectXGap: Ref<number> = ref(40);
// 一级节点单位宽度
const rectUnitWidth: Ref<number> = ref(100);
const circleYGap: number = 10;
const circleNodeHeight: number = 30;
const primaryColor: string = '#5F95FF';

enum IndexWidth {
    'NPRN' = 34,
    'ASGN' = 35,
    'INPL' = 27,
    'PLND' = 32,
    'RPLN' = 32,
    'APPV' = 32,
    'SCHD' = 35,
    'RTW' = 28,
    'WIP' = 24,
    'CSR' = 26,
    'CPL' = 24,
    'REJ' = 23,
}

function getRectNode(title: string, text: string[], number: string, width: number) {
  const attrsObj: object = {};
  const attrsCheckObj: object = {};
  const markupArr: any[] = [];

  text.reduce((prev: Record<string, any>, next: string) => {
    const refX: number = prev.refX + Number(IndexWidth[prev.text] || 0) + (prev.text ? 20 : 0);
    attrsObj[next] = {
      ...prev,
      text: next,
      refX,
    };

    attrsCheckObj[`${next}-path`] = {
      d: 'M 3 7 L 6 10 L 12 4',
      refX: refX + Number(IndexWidth[next] || 0),
      refY: '100%',
      refY2: -13,
      fill: 'none',
      display: 'none',
      stroke: '#11D010',
      strokeWidth: 1.5,
      yAlign: 'bottom',
    };
    return {
      ...prev,
      text: next,
      refX,
    };
  }, {
    refX: 50,
    refY: '100%',
    refY2: -8,
    fontSize: 12,
    fill: '#999',
    textAnchor: 'start',
    textVerticalAnchor: 'bottom',
  });

  text.forEach((str) => {
    markupArr.push({
      tagName: 'text',
      selector: str,
    });
    markupArr.push({
      tagName: 'path',
      selector: `${str}-path`,
    });
  });

  return {
    y: 12,
    height: 55,
    width,
    attrs: {
      type: 'node',
      body: {
        stroke: '#ccc',
        strokeWidth: 1,
        fill: '#F9F9F9',
        rx: 4,
        ry: 4,
      },
      circle: {
        r: 15,
        refX: 25,
        refY: '50%',
        fill: '#DDDDDD',
      },
      number: {
        text: number,
        refX: 25,
        refY: '50%',
        refY2: 2,
        fill: '#999',
        fontSize: 18,
      },
      title: {
        text: title,
        refX: 50,
        refY: 10,
        fill: '#999',
        fontSize: 16,
        textAnchor: 'start',
        textVerticalAnchor: 'top',
      },
      ...attrsObj,
      ...attrsCheckObj,
    },
    ports: {
      groups: {
        out: {
          position: {
            name: 'bottom',
          },
          attrs: {
            circle: {
              r: 0,
              refX: 0 - width / 2 + 25,
              magnet: true,
            },
          },
        },
      },
      items: [
        {
          id: 'out-port',
          group: 'out',
        },
      ],
    },
    markup: [
      {
        tagName: 'rect',
        selector: 'body',
      },
      {
        tagName: 'circle',
        selector: 'circle',
      },
      {
        tagName: 'text',
        selector: 'number',
      },
      {
        tagName: 'text',
        selector: 'title',
      },
      {
        tagName: 'path',
        selector: 'path',
      },
      ...markupArr,
    ],
  };
}

// 下方圆形节点
function getCircleNode(text: string, stroke: string = 'transparent') {
  const width: number = text.length * 15 + 30;
  const portX: number = 0 - width / 2 + 15;
  return {
    width,
    height: circleNodeHeight,
    attrs: {
      type: 'subNode',
      selected: false,
      body: {
        stroke,
        strokeWidth: 1,
        rx: 4,
        ry: 4,
      },
      circle: {
        r: 8,
        refX: 15,
        refY: '50%',
        stroke: '#ccc',
        fill: '#F3F3F3',
      },
      path: {
        d: 'M 3 7 L 6 10 L 12 4',
        refX: 8,
        refY: '50%',
        refY2: -3,
        fill: 'none',
        stroke: 'transparent',
        strokeWidth: 1.5,
        yAlign: 'top',
      },
      text: {
        text,
        refX: 30,
        refY: '50%',
        fill: '#999',
        fontSize: 14,
        textAnchor: 'start',
      },
    },
    ports: {
      groups: {
        'in-rect': {
          position: {
            name: 'top',
          },
          attrs: {
            circle: {
              r: 0,
              refX: portX,
              magnet: true,
            },
          },
        },
        'out-rect': {
          position: {
            name: 'bottom',
          },
          attrs: {
            circle: {
              r: 0,
              refX: portX,
              magnet: true,
            },
          },
        },
        'in-circle': {
          position: {
            name: 'top',
          },
          attrs: {
            circle: {
              r: 0,
              refX: portX,
              refY: 7,
              magnet: true,
            },
          },
        },
        'out-circle': {
          position: {
            name: 'bottom',
          },
          attrs: {
            circle: {
              r: 0,
              refX: portX,
              refY: -7,
              magnet: true,
            },
          },
        },
      },
      items: [
        {
          id: 'in-rect-port',
          group: 'in-rect',
        },
        {
          id: 'out-rect-port',
          group: 'out-rect',
        },
        {
          id: 'in-circle-port',
          group: 'in-circle',
        },
        {
          id: 'out-circle-port',
          group: 'out-circle',
        },
      ],
    },
    markup: [
      {
        tagName: 'rect',
        selector: 'body',
      },
      {
        tagName: 'circle',
        selector: 'circle',
      },
      {
        tagName: 'path',
        selector: 'path',
      },
      {
        tagName: 'text',
        selector: 'text',
      },
    ],
  };
}

function getEdgeAttrs(status: string) {
  switch (status) {
    case '1':
      return {
        type: 'edge',
        line: {
          stroke: '#5F95FF',
          strokeDasharray: 0,
          targetMarker: 'block',
        },
      };
    case '2':
      return {
        type: 'edge',
        line: {
          stroke: '#ccc',
          strokeDasharray: 3,
          targetMarker: 'block',
        },
      };
    case '3':
      return {
        type: 'subEdge',
        line: {
          stroke: '#5F95FF',
          strokeDasharray: 0,
          targetMarker: null,
        },
      };
    case '4':
      return {
        type: 'subEdge',
        line: {
          stroke: '#ccc',
          strokeDasharray: 3,
          targetMarker: null,
        },
      };
  }
}

// 通过接口数据获取节点结构
function getNodesByData(fetchData: any[]) {
  const nodes: any[] = [];
  fetchData.forEach((node: any, index: number) => {
    const nowNodeX: number = rectPadding + (index > 0 ? (index - 1) * 3 * rectUnitWidth.value + 5 * rectUnitWidth.value : 0) + rectXGap.value * index;
    const nowNodeWidth: number = index > 0 ? 3 * rectUnitWidth.value : 5 * rectUnitWidth.value;
    nodes.push({
      id: node.code,
      width: nowNodeWidth,
      x: nowNodeX,
      ...getRectNode(node.name, node.phaseList.map((item) => Object.keys(item).join(',')), (index + 1).toString(), nowNodeWidth),
    });
    if (Array.isArray(node.children)) {
      node.children.forEach((subNode, subIndex) => {
        nodes.push({
          id: subNode.code,
          x: nowNodeX + 10,
          y: 70 + subIndex * circleNodeHeight + circleYGap * (subIndex + 1),
          ...getCircleNode(subNode.name),
        });
      });
    }
  });
  return nodes;
}

// 通过接口数据获取边结构
function getEdgesByData(fetchData) {
  const edges: any[] = [];
  fetchData.reduce((prev, next) => {
    if (prev) {
      edges.push({
        source: prev.code,
        target: next.code,
        attrs: getEdgeAttrs('2'),
      });
    }
    return next;
  }, false);
  fetchData.forEach((node: any) => {
    if (Array.isArray(node.children)) {
      node.children.reduce((prev, next) => {
        if (prev) {
          edges.push({
            source: {
              cell: prev.code,
              port: prev.port,
            },
            target: {
              cell: next.code,
              port: 'in-circle-port',
            },
            attrs: getEdgeAttrs('4'),
          });
        }
        return {
          code: next.code,
          port: 'out-circle-port',
        };
      }, {
        code: node.code,
        port: 'out-port',
      });
    }
  });
  return edges;
}

function getNodeScrollTop(nodeId: string): number {
  switch (nodeId) {
    case 'jobDetail':
      return 0;
    case 'personInfoAdd':
    case 'personJoin':
    case 'personOut':
      return 750;
    case 'materialInfoAdd':
    case 'materialJoin':
    case 'materialOut':
      return 1320;
    case 'riskInfo':
      return 1900;
    case 'jobPackageAudit':
      return 2080;
    case 'jobActBeginTimeMaintenance':
    case 'jobActEndTimeMaintenance':
      return 0;
    case 'devlopInfoMaintenance':
      return 2200;
  }
}

export function useGraph(container: Ref<HTMLElement> | HTMLElement, emits: Function): {
    updateStatus: Function
    graph: Ref
} {
  const graph: Ref = ref();

  onMounted(() => {
    graph.value = new Graph({
      container: unref(container),
      autoResize: true,
      // panning: true,
      interacting: {
        nodeMovable: false,
        edgeMovable: false,
      },
    });

    updateSize();

    graph.value.fromJSON({
      nodes: getNodesByData(fetchData),
      edges: getEdgesByData(fetchData),
    });

    graph.value.on('resize', () => {
      graph.value.zoomToFit({
        padding: {
          left: 20,
          right: 20,
          top: 0,
          bottom: 0,
        },
      });
    });

    graph.value.on('node:click', ({
      node,
    }) => {
      if (node.getAttrs().type === 'subNode') {
        emits('updateScroll', getNodeScrollTop(node.id));
        reset();
        selectedNode(node);
        updateEdge(node);
      }
    });
  });

  function updateSize() {
    rectXGap.value = Math.ceil((unref(container).offsetWidth - 2 * rectPadding - 1400) / 3);

    if (rectXGap.value < 40) {
      rectUnitWidth.value = Math.ceil((unref(container).offsetWidth - 2 * rectPadding - 120) / 14);
      rectXGap.value = 40;
    }
  }

  function reset() {
    const nodes = graph.value.getNodes();
    const filterNodes = nodes.filter((node) => node.getAttrs().type === 'subNode');
    const edges = graph.value.getEdges();
    const filterEdges = edges.filter((edge) => edge.getAttrs().type === 'subEdge');

    filterNodes.forEach((node) => {
      node.setAttrs({
        body: {
          stroke: 'transparent',
        },
      });
    });

    filterEdges.forEach((edge) => {
      let target = edge.getTarget();
      let source = edge.getSource();
      if (target.port.includes('in-') || target.port.includes('out-')) {
        edge.setTarget({
          ...target,
          port: target.port.replace('-rect-', '-circle-'),
        });
      }
      if (source.port.includes('in-') || source.port.includes('out-')) {
        edge.setSource({
          ...source,
          port: source.port.replace('-rect-', '-circle-'),
        });
      }
    });
  }

  function selectedNode(node) {
    if (node.getAttrs().type === 'subNode') {
      node.setAttrs({
        selected: true,
        body: {
          stroke: primaryColor,
        },
      });
    }
  }

  // 更新边的链接点
  function updateEdge(node) {
    const edges = graph.value.getEdges().filter((edge) => edge.getTarget().cell === node.id || edge.getSource().cell === node.id);
    edges.forEach((edge, index) => {
      let target = edge.getTarget();
      let source = edge.getSource();
      if (index === 0 && (target.port.includes('in-') || target.port.includes('out-'))) {
        edge.setTarget({
          ...target,
          port: target.port.replace('-circle-', '-rect-'),
        });
      }
      if (index > 0 && (source.port.includes('in-') || source.port.includes('out-'))) {
        edge.setSource({
          ...source,
          port: source.port.replace('-circle-', '-rect-'),
        });
      }
    });
  }

  // 节点、边状态变更
  function updateStatus(newData) {
    if (!Array.isArray(newData) || newData.length === 0) return;
    const nodes = graph.value.getNodes();
    const edges = graph.value.getEdges();
    nodes.forEach((node) => {
      const nodeData = findNode(newData, (item) => item.code === node.id);
      node.setData(nodeData);
      if (node.getAttrs().type === 'node') {
        setNodeStatus(nodeData.isLightUp, node);
      } else if (node.getAttrs().type === 'subNode') {
        setSubNodeStatus(nodeData.isLightUp, node);
      }
    });
    edges.forEach((edge) => {
      const sourceNode = findNode(newData, (item) => item.code === edge.getSource().cell);
      const targetNode = findNode(newData, (item) => item.code === edge.getTarget().cell);
      setEdgeStatus(sourceNode.isLightUp && targetNode.isLightUp, edge);
    });
  }

  // 设置一级节点状态
  function setNodeStatus(status: boolean, node) {
    const phaseList = node.getData().phaseList;
    phaseList.forEach((item) => {
      const key = Object.keys(item)[0];
      const value = item[key];
      node.setAttrs({
        [key]: { fill: value ? '#11D010' : '#999' },
        [`${key}-path`]: { display: value ? 'block' : 'none' },
      });
    });
    if (status) {
      node.setAttrs({
        body: {
          stroke: primaryColor,
          fill: '#E8EFFF',
        },
        circle: {
          fill: primaryColor,
        },
        number: {
          fill: '#fff',
        },
        title: {
          fill: '#333',
        },
      });
    } else {
      node.setAttrs({
        body: {
          stroke: '#CCC',
          fill: '#F9F9F9',
        },
        circle: {
          fill: '#DDD',
        },
        number: {
          fill: '#999',
        },
        title: {
          fill: '#999',
        },
      });
    }
  }

  // 设置子节点状态
  function setSubNodeStatus(status: boolean, node) {
    if (status) {
      node.setAttrs({
        circle: {
          stroke: primaryColor,
          fill: primaryColor,
        },
        path: {
          stroke: '#fff',
        },
        text: {
          fill: primaryColor,
        },
      });
    } else {
      node.setAttrs({
        circle: {
          stroke: '#CCC',
          fill: '#F3F3F3',
        },
        path: {
          stroke: 'transparent',
        },
        text: {
          fill: '#999',
        },
      });
    }
  }

  function setEdgeStatus(status: boolean, edge) {
    switch (edge.getAttrs().type) {
      case 'edge':
        if (status) {
          edge.setAttrs(getEdgeAttrs('1'));
        } else {
          edge.setAttrs(getEdgeAttrs('2'));
        }
        break;
      case 'subEdge':
        if (status) {
          edge.setAttrs(getEdgeAttrs('3'));
        } else {
          edge.setAttrs(getEdgeAttrs('4'));
        }
        break;
    }
  }

  return {
    updateStatus,
    graph,
  };
}
