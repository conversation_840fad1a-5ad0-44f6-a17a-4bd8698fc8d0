package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ProjectMajorDeedDTO;
import com.chinasie.orion.domain.vo.ProjectMajorDeedVO;
import com.chinasie.orion.operatelog.dict.OperateTypeDict;
import com.chinasie.orion.service.ProjectMajorDeedService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.dto.ResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.List;
import java.lang.Exception;

/**
 * <p>
 * ProjectMajorDeed 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-27 19:32:36
 */
@RestController
@RequestMapping("/projectMajorDeed")
@Api(tags = "项目主要事迹")
public class  ProjectMajorDeedController  {

    @Autowired
    private ProjectMajorDeedService projectMajorDeedService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】在【项目管理-项目详情】获取【{{#projectMajorDeedVO.majorDeed}}】详情成功",
            fail = "【{USER{#logUserId}}】在【项目管理-项目详情】获取【{{#projectMajorDeedVO.majorDeed}}】详情失败,失败原因：【{{#_errorMsg}}】",
            type = "项目管理", subType = "项目详情", bizNo = "", extra = OperateTypeDict.GET)
    public ResponseDTO<ProjectMajorDeedVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        ProjectMajorDeedVO rsp = projectMajorDeedService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 保存主要事迹
     * @param projectId
     * @param projectMajorDeedDTOList
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "保存主要事迹")
    @RequestMapping(value = "/saveOrRemove", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】保存主要事迹", type = "项目主要事迹", subType = "保存主要事迹", bizNo = "#{{projectId}}")
    public ResponseDTO<Boolean> saveOrRemove(@RequestParam(value = "projectId") String projectId, @RequestBody List<ProjectMajorDeedDTO> projectMajorDeedDTOList) throws Exception {
        Boolean rsp = projectMajorDeedService.saveOrRemove(projectMajorDeedDTOList, projectId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 获取主要事迹列表
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取主要事迹列表")
    @RequestMapping(value = "/getList", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】获取主要事迹列表", type = "项目主要事迹", subType = "列表", bizNo = "#{{projectId}}")
    public ResponseDTO<List<ProjectMajorDeedVO>> getList(@RequestParam(value = "projectId") String projectId) throws Exception {
        List<ProjectMajorDeedVO> rsp = projectMajorDeedService.getList(projectId);
        return new ResponseDTO<>(rsp);
    }
}
