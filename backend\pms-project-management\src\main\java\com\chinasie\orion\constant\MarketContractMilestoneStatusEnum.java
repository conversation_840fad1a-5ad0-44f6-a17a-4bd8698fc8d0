package com.chinasie.orion.constant;

/**
 * @author: yk
 * @date: 2023/10/25 20:40
 * @description:
 */
public enum MarketContractMilestoneStatusEnum {
    CREATED(101, "未启动"),
    APPROVAL(120,"审批中"),
    PROGRESS(110, "进行中"),
    COMPLATED(130, "已完成"),

    ;


    private Integer status;

    private String desc;


    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    MarketContractMilestoneStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
