<script setup lang="ts">
import {
  defineExpose, defineProps, onMounted, ref,
} from 'vue';
import { Input, Textarea, message } from 'ant-design-vue';
import { BasicTitle1, BasicEditor } from 'lyra-component-vue3';
import { documentDecompositionById } from '/@/views/pms/api/documentDecomposition';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  parentName: {
    type: String,
    default: '',
  },
});

const refBasicEditor = ref();
const loading = ref(false);
const name = ref(undefined);
const remark = ref(undefined);

const handleSubmit = () => new Promise((resolve, reject) => {
  if (!name.value) {
    message.error('名称不能为空');
    reject();
    return;
  }
  if (!name.value.trim()) {
    message.error('名称不能为空');
    reject();
    return;
  }
  resolve({
    name: name.value,
    remark: remark.value,
    content: refBasicEditor.value.getHtml(),
  });
});

onMounted(async () => {
  if (props.id) {
    loading.value = true;
    const data = await documentDecompositionById(props.id);
    loading.value = false;
    name.value = data.name;
    remark.value = data.remark;
    // 设置富文本
    refBasicEditor.value.setHtml(data.content);
  }
});

defineExpose({
  handleSubmit,
});
</script>

<template>
  <div
    v-loading="loading"
    class="p20"
  >
    <BasicTitle1 class="mb10">
      <div class="flex">
        <div
          class="flex-f1"
          style="font-size: 16px;font-weight: bold"
        >
          <span style="color: red">*</span>
          <span>&nbsp;条目标题</span>
        </div>
      </div>
    </BasicTitle1>
    <Input
      v-model:value="name"
      :maxlength="100"
      showCount
      placeholder="请输入条目标题"
    />
    <div v-if="parentName">
      <BasicTitle1 class="mt20 mb10">
        <div class="flex">
          <div
            class="flex-f1"
            style="font-size: 16px;font-weight: bold"
          >
            <span style="color: red">*</span>
            <span>&nbsp;条目父级</span>
          </div>
        </div>
      </BasicTitle1>
      <Input
        :defaultValue="parentName"
        disabled
      />
    </div>
    <BasicTitle1
      class="mt20 mb10"
      title="编写要求"
    />
    <Textarea
      v-model:value="remark"
      :maxlength="512"
      :rows="4"
      showCount
      placeholder="请输入内容"
    />
    <BasicTitle1
      class="mt20 mb10"
      title="条目编制内容"
    />
    <BasicEditor ref="refBasicEditor" />
  </div>
</template>

<style scoped lang="less">

</style>
