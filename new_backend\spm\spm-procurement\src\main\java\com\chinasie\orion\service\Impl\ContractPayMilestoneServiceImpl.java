package com.chinasie.orion.service.Impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.domain.dto.ContractPayMilestoneDTO;
import com.chinasie.orion.domain.entity.ContractPayMilestone;
import com.chinasie.orion.domain.vo.ContractPayMilestoneVO;
import com.chinasie.orion.repository.ContractPayMilestoneMapper;
import com.chinasie.orion.service.ContractPayMilestoneService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <p>
 * ContractPayMilestone 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@Service
@Slf4j
public class ContractPayMilestoneServiceImpl extends OrionBaseServiceImpl<ContractPayMilestoneMapper, ContractPayMilestone> implements ContractPayMilestoneService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ContractPayMilestoneVO detail(String id, String pageCode) throws Exception {
        ContractPayMilestone contractPayMilestone = this.getById(id);
        ContractPayMilestoneVO result = BeanCopyUtils.convertTo(contractPayMilestone, ContractPayMilestoneVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param contractPayMilestoneDTO
     */
    @Override
    public String create(ContractPayMilestoneDTO contractPayMilestoneDTO) throws Exception {
        ContractPayMilestone contractPayMilestone = BeanCopyUtils.convertTo(contractPayMilestoneDTO, ContractPayMilestone::new);
        this.save(contractPayMilestone);

        String rsp = contractPayMilestone.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param contractPayMilestoneDTO
     */
    @Override
    public Boolean edit(ContractPayMilestoneDTO contractPayMilestoneDTO) throws Exception {
        ContractPayMilestone contractPayMilestone = BeanCopyUtils.convertTo(contractPayMilestoneDTO, ContractPayMilestone::new);

        this.updateById(contractPayMilestone);

        String rsp = contractPayMilestone.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ContractPayMilestoneVO> pages(String mainTableId, Page<ContractPayMilestoneDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ContractPayMilestone> condition = new LambdaQueryWrapperX<>(ContractPayMilestone.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ContractPayMilestone::getCreateTime);

        condition.eq(ContractPayMilestone::getMainTableId, mainTableId);

        Page<ContractPayMilestone> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ContractPayMilestone::new));

        PageResult<ContractPayMilestone> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ContractPayMilestoneVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ContractPayMilestoneVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ContractPayMilestoneVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ContractPayMilestoneVO> getByCode(Page<ContractPayMilestoneDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<ContractPayMilestone> condition = new LambdaQueryWrapperX<>(ContractPayMilestone.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.eq(ContractPayMilestone::getContractNumber, pageRequest.getQuery().getContractNumber());
        condition.orderByDesc(ContractPayMilestone::getEstPaymentDate);
        Page<ContractPayMilestone> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ContractPayMilestone::new));

        PageResult<ContractPayMilestone> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ContractPayMilestoneVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ContractPayMilestoneVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ContractPayMilestoneVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "合同支付里程碑（计划）导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractPayMilestoneDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ContractPayMilestoneExcelListener excelReadListener = new ContractPayMilestoneExcelListener();
        EasyExcel.read(inputStream, ContractPayMilestoneDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ContractPayMilestoneDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("合同支付里程碑（计划）导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ContractPayMilestone> contractPayMilestonees = BeanCopyUtils.convertListTo(dtoS, ContractPayMilestone::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("ncf::ContractPayMilestone-import::id", importId, contractPayMilestonees, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ContractPayMilestone> contractPayMilestonees = (List<ContractPayMilestone>) orionJ2CacheService.get("ncf::ContractPayMilestone-import::id", importId);
        log.info("合同支付里程碑（计划）导入的入库数据={}", JSONUtil.toJsonStr(contractPayMilestonees));

        this.saveBatch(contractPayMilestonees);
        orionJ2CacheService.delete("ncf::ContractPayMilestone-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("ncf::ContractPayMilestone-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ContractPayMilestone> condition = new LambdaQueryWrapperX<>(ContractPayMilestone.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ContractPayMilestone::getCreateTime);
        List<ContractPayMilestone> contractPayMilestonees = this.list(condition);

        List<ContractPayMilestoneDTO> dtos = BeanCopyUtils.convertListTo(contractPayMilestonees, ContractPayMilestoneDTO::new);

        String fileName = "合同支付里程碑（计划）数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractPayMilestoneDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<ContractPayMilestoneVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }


    public static class ContractPayMilestoneExcelListener extends AnalysisEventListener<ContractPayMilestoneDTO> {

        private final List<ContractPayMilestoneDTO> data = new ArrayList<>();

        @Override
        public void invoke(ContractPayMilestoneDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ContractPayMilestoneDTO> getData() {
            return data;
        }
    }


}
