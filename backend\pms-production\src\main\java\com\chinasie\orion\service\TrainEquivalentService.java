package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.train.EquivalentParamDTO;
import com.chinasie.orion.domain.entity.TrainEquivalent;
import com.chinasie.orion.domain.dto.TrainEquivalentDTO;
import com.chinasie.orion.domain.vo.TrainEquivalentVO;

import java.lang.String;
import java.util.List;


import com.chinasie.orion.domain.vo.train.PersonTrainEffectVO;
import com.chinasie.orion.domain.vo.train.PersonTrainVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * TrainEquivalent 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 17:09:24
 */
public interface TrainEquivalentService extends OrionBaseService<TrainEquivalent> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    TrainEquivalentVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param trainEquivalentDTO
     */
    String create(TrainEquivalentDTO trainEquivalentDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param trainEquivalentDTO
     */
    Boolean edit(TrainEquivalentDTO trainEquivalentDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<TrainEquivalentVO> pages(Page<TrainEquivalentDTO> pageRequest) throws Exception;
    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<TrainEquivalentVO> vos) throws Exception;


    List<PersonTrainVO> trainCurrentPersonList(EquivalentParamDTO equivalentParamDTO);

    List<PersonTrainEffectVO> currentPersonEffectTrainList(EquivalentParamDTO equivalentParamDTO);

    /**
     *  获取当前人的 等效列表
     * @param equivalentParamDTO
     * @return
     */
    List<TrainEquivalentVO> currentEquivalentList(EquivalentParamDTO equivalentParamDTO) throws Exception;

    Map<String, List<TrainEquivalentVO>> listSimpleByIds(List<String> equivalentIdList) throws Exception;
}
