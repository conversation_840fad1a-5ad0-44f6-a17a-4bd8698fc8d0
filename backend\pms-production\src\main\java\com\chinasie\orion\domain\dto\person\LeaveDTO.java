package com.chinasie.orion.domain.dto.person;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.domain.vo.excel.ExcelYesOrNoConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/05/17:39
 * @description:
 */
@Data
public class LeaveDTO  implements Serializable {

    private String id;

    /**
     * 离厂原因
     */
    @ApiModelProperty(value = "离厂原因")
    private String leaveReason;

    @ExcelProperty(value = "是否完成离厂交接", converter = ExcelYesOrNoConverter.class)
    private Boolean isFinishOutHandover;
    /**
     * 离厂备注
     */
    @ApiModelProperty(value = "离厂备注")
    private String leaveRemark;
    @ApiModelProperty(value = "离厂时间")
    private Date actOutDate;

    @ApiModelProperty(value = "是否再次入场")
    private Boolean isAgainIn;

    @ApiModelProperty(value = "进场出场日期")
    private List<String> userCodeList;
    @ApiModelProperty(value = "进场出场日期")
    private List<Date> inAndOutDateList;

}
