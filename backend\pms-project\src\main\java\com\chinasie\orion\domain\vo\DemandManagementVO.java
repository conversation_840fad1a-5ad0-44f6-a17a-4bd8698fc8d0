package com.chinasie.orion.domain.vo;

import com.chinasie.orion.domain.dto.pas.TypeAttrValueDTO;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/2/18 15:13
 * @description:
 */
@Data
public class DemandManagementVO extends ObjectVO {
    /**
     * 负责人id
     */
    @ApiModelProperty(value = "负责人id")
    private String principalId;
    private String principalName;

    /**
     * 接收人Id
     */
    @ApiModelProperty(value = "接收人Id")
    private String recipientId;
    private String recipientName;

    /**
     * 需求来源
     */
    @ApiModelProperty(value = "需求来源")
    private String source;
    private String sourceName;

    /**
     * 父级ID
     */
    @ApiModelProperty(value = "父级ID")
    private String parentId;

    @ApiModelProperty(value = "所属需求")
    private String parentName;


    /**
     * 期望完成日期
     */
    @ApiModelProperty(value = "期望完成日期")
    private Date predictEndTime;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private String priorityLevel;
    private String priorityLevelName;

    /**
     * 提出时间
     */
    @ApiModelProperty(value = "提出时间")
    private Date proposedTime;

    /**
     * 需求类型
     */
    @ApiModelProperty(value = "需求类型")
    private String type;
    private String typeName;

    /**
     * 提出人
     */
    @ApiModelProperty(value = "提出人")
    private String exhibitor;
    @ApiModelProperty(value = "提出人名称")
    private String exhibitorName;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;
    private String projectName;

    /**
     * 进度
     */
    @ApiModelProperty(value = "进度")
    @TableField(value = "schedule")
    private BigDecimal schedule;
    private String scheduleName;

    @ApiModelProperty("属性值")
    private List<TypeAttrValueDTO> typeAttrValueDTOList;
}
