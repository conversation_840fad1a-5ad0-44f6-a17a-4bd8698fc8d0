package com.chinasie.orion.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinasie.orion.api.code.domain.vo.CodeSegmentVO;
import com.chinasie.orion.bo.CodeBo;
import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.bo.UserBo;
import com.chinasie.orion.constant.ClassNameConstant;
import com.chinasie.orion.constant.DictConstant;
import com.chinasie.orion.constant.TakeEffectEnum;
import com.chinasie.orion.domain.dto.ProjectTaskStatusDTO;
import com.chinasie.orion.domain.dto.TakeEffectDTO;
import com.chinasie.orion.domain.entity.ProjectTaskStatus;
import com.chinasie.orion.domain.vo.ProjectTaskStatusVO;
import com.chinasie.orion.domain.vo.SimpleVo;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.PageRequest;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ProjectTaskStatusRepository;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.helper.StatusRedisHelper;
import com.chinasie.orion.service.ProjectTaskStatusService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/13/10:20
 * @description:
 */
@Service
public class ProjectTaskStatusServiceImpl extends OrionBaseServiceImpl<ProjectTaskStatusRepository, ProjectTaskStatus> implements ProjectTaskStatusService {

    @Resource
    private UserBo userBo;

    @Resource
    private DictBo dictBo;

    @Autowired
    private CodeBo codeBo;

    @Autowired
    private StatusRedisHelper statusRedisHelper;

    @Override
    public String saveProjectTaskStatus(ProjectTaskStatusDTO projectTaskStatusDTO) throws Exception {
        List<ProjectTaskStatus> projectTaskStatusDTOList = this.list(new LambdaQueryWrapper<>(ProjectTaskStatus.class).
                eq(ProjectTaskStatus::getName, projectTaskStatusDTO.getName()).
                eq(ProjectTaskStatus::getProjectId, projectTaskStatusDTO.getProjectId()));
        if (!CollectionUtils.isEmpty(projectTaskStatusDTOList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_NAME_REPEAT);
        }
        List<CodeSegmentVO> codeRuleList = codeBo.getCodeRuleList(ClassNameConstant.PROJECT_TASK_STATUS, ClassNameConstant.NUMBER);
        if (!CollectionUtils.isEmpty(codeRuleList)) {
            String code = codeBo.getCode(codeRuleList);
            projectTaskStatusDTO.setNumber(code);
        }
        projectTaskStatusDTO.setTakeEffect(TakeEffectEnum.UN_EFFECT.getStatus());
//        projectTaskStatusDTO.setNumber(String.format("XMZT%s", IdUtil.objectId()));
        ProjectTaskStatus projectTaskStatus = BeanCopyUtils.convertTo(projectTaskStatusDTO, ProjectTaskStatus::new);
        this.save(projectTaskStatus);
        return projectTaskStatus.getId();
    }

    @Override
    public List<SimpleVo> getProjectTaskStatusList(String projectId) throws Exception {
        List<DataStatusVO> dataStatusList = statusRedisHelper.getStatusInfoListByClassName("Project");
        if (CollectionUtils.isEmpty(dataStatusList)) {
            return new ArrayList<>();
        }
        List<SimpleVo> simpleVos = new ArrayList<>();
        for (DataStatusVO dataStatusVO : dataStatusList) {
            SimpleVo simpleVo = new SimpleVo();
            simpleVo.setName(dataStatusVO.getName());
            simpleVo.setId(dataStatusVO.getStatusValue().toString());
            simpleVos.add(simpleVo);
        }
        return simpleVos;
    }

    @Override
    public Map<String, String> getIdToNameMapByIdList(List<String> projectIds) throws Exception {
        List<ProjectTaskStatus> projectTaskStatusList = this.list(new LambdaQueryWrapper<>(ProjectTaskStatus.class).
                in(ProjectTaskStatus::getProjectId, projectIds.toArray()).
                eq(ProjectTaskStatus::getTakeEffect, TakeEffectEnum.EFFECT.getStatus()));
        if (CollectionUtils.isEmpty(projectTaskStatusList)) {
            return new HashMap<>();
        }
        return projectTaskStatusList.stream().collect(Collectors.toMap(ProjectTaskStatus::getId, ProjectTaskStatus::getName));
    }

    @Override
    public PageResult<ProjectTaskStatusVO> getProjectTaskStatusPage(PageRequest<ProjectTaskStatusDTO> pageRequest) throws Exception {
        ProjectTaskStatusDTO query = pageRequest.getQuery();
        LambdaQueryWrapper<ProjectTaskStatus> condition = new LambdaQueryWrapper<>();
        if (!ObjectUtils.isEmpty(query)) {
            String projectId = query.getProjectId();
            if (StringUtils.hasText(projectId)) {
                condition.eq(ProjectTaskStatus::getProjectId, projectId);
            }
            Integer takeEffect = query.getTakeEffect();
            if (null != takeEffect) {
                condition.eq(ProjectTaskStatus::getTakeEffect, takeEffect);
            }
            String type = query.getType();
            if (StringUtils.hasText(type)) {
                condition.eq(ProjectTaskStatus::getType, type);
            }
        }
        IPage<ProjectTaskStatus> projectTaskStatusIPage = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        IPage<ProjectTaskStatus> pageResult = this.page(projectTaskStatusIPage);
        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(pageResult.getRecords())) {
            return new PageResult<>(new ArrayList<>(), pageRequest.getPageNum(), pageRequest.getPageSize(), 0L);
        }
        List<ProjectTaskStatus> projectTaskStatusList = pageResult.getRecords();
        List<ProjectTaskStatusVO> projectTaskStatusVOList = BeanCopyUtils.convertListTo(projectTaskStatusList, ProjectTaskStatusVO::new);
        List<String> userIdList = projectTaskStatusVOList.stream().map(ProjectTaskStatusVO::getCreatorId).collect(Collectors.toList());
        userIdList.addAll(projectTaskStatusVOList.stream().map(ProjectTaskStatusVO::getModifyId).collect(Collectors.toList()));
        Map<String, String> userIdAndNameMap = userBo.getNameByUserIdMap(userIdList.stream().distinct().collect(Collectors.toList()));
        Map<String, String> taskStatusTypeValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.TASK_STATUS_TYPE);

        projectTaskStatusVOList.forEach(o -> {
            o.setTypeName(taskStatusTypeValueToDesMap.get(o.getType()));
            o.setTakeEffectName(TakeEffectEnum.getNameByStatus(o.getTakeEffect()));
            o.setCreatorName(userIdAndNameMap.get(o.getCreatorId()));
            o.setModifyName(userIdAndNameMap.get(o.getModifyId()));
        });
        return new PageResult<>(projectTaskStatusVOList, pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal());
    }

    @Override
    public ProjectTaskStatusVO getProjectTaskStatusDetail(String id) throws Exception {
        ProjectTaskStatus projectTaskStatusDTO = this.getById(id);
        if (Objects.isNull(projectTaskStatusDTO)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        ProjectTaskStatusVO projectTaskStatusVO = new ProjectTaskStatusVO();
        BeanCopyUtils.copyProperties(projectTaskStatusDTO, projectTaskStatusVO);
        List<String> userIdList = new ArrayList<>();
        userIdList.add(projectTaskStatusVO.getCreatorId());
        userIdList.add(projectTaskStatusVO.getModifyId());
        Map<String, String> userVOIdAndNameMap = userBo.getNameByUserIdMap(userIdList);
        Map<String, String> taskStatusTypeValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.TASK_STATUS_TYPE);
        projectTaskStatusVO.setTypeName(taskStatusTypeValueToDesMap.get(projectTaskStatusVO.getType()));
        projectTaskStatusVO.setTakeEffectName(TakeEffectEnum.getNameByStatus(projectTaskStatusVO.getTakeEffect()));
        projectTaskStatusVO.setCreatorName(userVOIdAndNameMap.get(projectTaskStatusVO.getCreatorId()));
        projectTaskStatusVO.setModifyName(userVOIdAndNameMap.get(projectTaskStatusVO.getModifyId()));
        return projectTaskStatusVO;
    }

    @Override
    public Boolean editProjectTaskStatus(ProjectTaskStatusDTO projectTaskStatusDTO) throws Exception {
        List<ProjectTaskStatus> projectTaskStatusDTOList = this.list(new LambdaQueryWrapper<>(ProjectTaskStatus.class).
                ne(ProjectTaskStatus::getId, projectTaskStatusDTO.getId()).
                eq(ProjectTaskStatus::getName, projectTaskStatusDTO.getName()).
                eq(ProjectTaskStatus::getProjectId, projectTaskStatusDTO.getProjectId()));
        if (!CollectionUtils.isEmpty(projectTaskStatusDTOList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_NAME_REPEAT);
        }
        ProjectTaskStatus projectTaskStatus = BeanCopyUtils.convertTo(projectTaskStatusDTO, ProjectTaskStatus::new);
        this.updateById(projectTaskStatus);
        return true;
    }

    @Override
    public Boolean removeProjectTaskStatus(List<String> ids) throws Exception {
        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }
        List<ProjectTaskStatus> projectTaskStatusDTOList = this.list(new LambdaQueryWrapper<>(ProjectTaskStatus.class).
                in(ProjectTaskStatus::getId, ids.toArray()));
        if (CollectionUtils.isEmpty(projectTaskStatusDTOList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        List<Integer> takeEffectList = projectTaskStatusDTOList.stream().map(ProjectTaskStatus::getTakeEffect).distinct().collect(Collectors.toList());
        if (takeEffectList.contains(TakeEffectEnum.EFFECT.getStatus())) {
            throw new PMSException(PMSErrorCode.KMS_EFFECT_DATA);

        }

        // todo 暂时不处理
//        List<Relation> relationList = relationService.queryRelation(new LambdaQueryWrapper<>(Relation.class).
//                in(Relation::getToId,  ids.toArray()).
//                eq(Relation::getToClass,  RelationClassNameConstant.PROJECT_TASK_STATUS));
//        if (!CollectionUtils.isEmpty(relationList)) {
//            throw new PMSException(PMSErrorCode.KMS_DATA_QUOTE_STATUS);
//        }
        return this.removeBatchByIds(ids);
    }

    @Override
    public Boolean takeEffectProjectTaskStatus(TakeEffectDTO takeEffectDTO) throws Exception {
        //启用禁用逻辑
        List<ProjectTaskStatus> projectTaskStatusDTOList = new ArrayList<>();
        takeEffectDTO.getIdList().forEach(o -> {
            ProjectTaskStatus projectTaskStatusDTO = new ProjectTaskStatus();
            projectTaskStatusDTO.setId(o);
            projectTaskStatusDTO.setTakeEffect(takeEffectDTO.getTakeEffect());
            projectTaskStatusDTOList.add(projectTaskStatusDTO);
        });
        return this.updateBatchById(projectTaskStatusDTOList);
    }
}
