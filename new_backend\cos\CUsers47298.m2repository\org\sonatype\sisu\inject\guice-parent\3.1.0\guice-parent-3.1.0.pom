<?xml version="1.0" encoding="UTF-8"?>

<!--
Copyright (c) 2006 Google, Inc. All rights reserved.

This program is licensed to you under the Apache License Version 2.0,
and you may not use this file except in compliance with the Apache License Version 2.0.
You may obtain a copy of the Apache License Version 2.0 at http://www.apache.org/licenses/LICENSE-2.0.

Unless required by applicable law or agreed to in writing,
software distributed under the Apache License Version 2.0 is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the Apache License Version 2.0 for the specific language governing permissions and limitations there under.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.sonatype.forge</groupId>
    <artifactId>forge-parent</artifactId>
    <version>10</version>
  </parent>

  <packaging>pom</packaging>

  <groupId>org.sonatype.sisu.inject</groupId>
  <artifactId>guice-parent</artifactId>
  <version>3.1.0</version>

  <name>Sisu Guice</name>

  <description>
    Patched build of Guice: a lightweight dependency injection framework for Java 5 and above
  </description>

  <url>http://code.google.com/p/google-guice/</url>
  <inceptionYear>2006</inceptionYear>

  <organization>
    <name>Google, Inc.</name>
    <url>http://www.google.com</url>
  </organization>

  <mailingLists>
    <mailingList>
      <name>Guice Users List</name>
      <archive>http://groups.google.com/group/google-guice/topics</archive>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post>http://groups.google.com/group/google-guice/post</post>
    </mailingList>
    <mailingList>
      <name>Guice Developers List</name>
      <archive>http://groups.google.com/group/google-guice-dev/topics</archive>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post>http://groups.google.com/group/google-guice-dev/post</post>
    </mailingList>
  </mailingLists>

  <scm>
    <connection>scm:git:**************:sonatype/sisu-guice.git</connection>
    <developerConnection>scm:git:**************:sonatype/sisu-guice.git</developerConnection>
    <url>http://github.com/sonatype/sisu-guice</url>
  </scm>

  <issueManagement>
    <system>Google Code</system>
    <url>http://code.google.com/p/google-guice/issues/</url>
  </issueManagement>

  <ciManagement>
    <system>Hudson</system>
    <url>https://builds.sonatype.org/job/sisu-guice/</url>
  </ciManagement>

  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <modules>
    <module>core</module>
    <module>extensions</module>
  </modules>

  <prerequisites>
    <maven>3.0</maven>
  </prerequisites>

  <properties>
    <!--
     | The spec version of the public Guice API
    -->
    <guice.api.version>1.4</guice.api.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <!--
     | Use "-Dguice.with.jarjar=false" to build without jarjar
    -->
    <guice.with.jarjar>true</guice.with.jarjar>
    <!--
     | Use "-Dguice.with.no_aop=false" to skip the no-AOP variant
    -->
    <guice.with.no_aop>true</guice.with.no_aop>
  </properties>

  <dependencies>
    <!--
     | Run tests with TestNG
    -->
    <dependency>
      <groupId>org.testng</groupId>
      <artifactId>testng</artifactId>
      <version>6.2</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <!--
     | Ant-style directories
    -->
    <sourceDirectory>${project.basedir}/src</sourceDirectory>
    <resources>
      <resource>
        <filtering>false</filtering>
        <directory>${project.basedir}/src</directory>
        <excludes>
          <exclude>**/*.java</exclude>
        </excludes>
      </resource>
    </resources>
    <testSourceDirectory>${project.basedir}/test</testSourceDirectory>
    <testResources>
      <testResource>
        <filtering>false</filtering>
        <directory>${project.basedir}/test</directory>
        <excludes>
          <exclude>**/*.java</exclude>
        </excludes>
      </testResource>
    </testResources>
    <pluginManagement>
      <plugins>
        <!--
         | Standard LICENSE and NOTICE files
        -->
        <plugin>
          <artifactId>maven-remote-resources-plugin</artifactId>
          <version>1.2.1</version>
          <executions>
            <execution>
              <goals>
                <goal>process</goal>
              </goals>
              <configuration>
                <resourceBundles>
                  <resourceBundle>org.apache:apache-jar-resource-bundle:1.4</resourceBundle>
                </resourceBundles>
              </configuration>
            </execution>
          </executions>
        </plugin>
        <!--
         | Make sure we only use Java5 methods
        -->
        <plugin>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>2.3.2</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>animal-sniffer-maven-plugin</artifactId>
          <version>1.7</version>
          <configuration>
            <signature>
              <groupId>org.codehaus.mojo.signature</groupId>
              <artifactId>java15</artifactId>
              <version>1.0</version>
            </signature>
          </configuration>
          <executions>
            <execution>
              <id>check-java-1.5-compat</id>
              <phase>process-classes</phase>
              <goals>
                <goal>check</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.9</version>
          <configuration>
            <redirectTestOutputToFile>true</redirectTestOutputToFile>
          </configuration>
        </plugin>
        <!--
         | Shared OSGi manifest configuration
        -->
        <plugin>
          <groupId>org.apache.felix</groupId>
          <artifactId>maven-bundle-plugin</artifactId>
          <version>2.3.5</version>
          <configuration>
            <instructions>
              <Bundle-Name>${project.artifactId}</Bundle-Name>
              <Bundle-SymbolicName>$(maven-symbolicname);singleton:=true</Bundle-SymbolicName>
              <Bundle-Copyright>Copyright (C) 2006 Google Inc.</Bundle-Copyright>
              <Bundle-DocURL>http://code.google.com/p/google-guice/</Bundle-DocURL>
              <Bundle-Vendor>Sonatype, Inc.</Bundle-Vendor>
              <Bundle-RequiredExecutionEnvironment>
                J2SE-1.5,JavaSE-1.6
              </Bundle-RequiredExecutionEnvironment>
              <_exportcontents>!*.internal.*,*;version=${guice.api.version}</_exportcontents>
              <Import-Package>!com.google.*,*</Import-Package>
              <_nouses>true</_nouses>
              <_removeheaders>
                Embed-Dependency,Embed-Transitive,
                Built-By,Tool,Created-By,Build-Jdk,
                Originally-Created-By,Archiver-Version,
                Include-Resource,Private-Package,
                Ignore-Package,Bnd-LastModified
              </_removeheaders>
            </instructions>
            <archive>
              <forced>true</forced><!-- dummy entry to stop bundleplugin from picking up jar config -->
            </archive>
          </configuration>
          <executions>
            <execution>
              <phase>prepare-package</phase>
              <goals>
                <goal>manifest</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <!--
         | Package OSGi manifest in final JAR, also create a JAR of the test classes
        -->
        <plugin>
          <artifactId>maven-jar-plugin</artifactId>
          <version>2.3.2</version>
          <configuration>
            <archive>
              <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
              <addMavenDescriptor>false</addMavenDescriptor><!--  Exclude to mirror ant build -->
            </archive>
          </configuration>
          <executions>
            <execution>
              <phase>package</phase>
              <goals>
                <goal>test-jar</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>2.8</version>
          <configuration>
            <doclet>com.google.doclava.Doclava</doclet>
            <docletPath>
              ${project.basedir}/../lib/doclava.jar:
              ${project.basedir}/../../lib/doclava.jar
            </docletPath>
            <!--
             | bootclasspath required by Sun's JVM 
            -->
            <bootclasspath>${sun.boot.class.path}</bootclasspath>
            <excludePackageNames>*.internal</excludePackageNames>
            <additionalparam>
              -quiet
              -federate JDK http://download.oracle.com/javase/6/docs/api/index.html?
              -federationxml JDK http://doclava.googlecode.com/svn/static/api/openjdk-6.xml
              -hdf project.name "${project.name} (patched build of Google Guice)"
              -d ${project.build.directory}/apidocs
            </additionalparam>
            <useStandardDocletOptions>false</useStandardDocletOptions>
            <!--
             | Apple's JVM sometimes requires more memory
            -->
            <additionalJOption>-J-Xmx1024m</additionalJOption>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-site-plugin</artifactId>
          <version>3.0</version>
        </plugin>
        <plugin>
          <artifactId>maven-source-plugin</artifactId>
          <version>2.1.2</version>
        </plugin>
        <plugin>
          <artifactId>maven-gpg-plugin</artifactId>
          <version>1.4</version>
        </plugin>
        <plugin>
          <artifactId>maven-release-plugin</artifactId>
          <version>2.2.1</version>
          <configuration>
            <autoVersionSubmodules>true</autoVersionSubmodules>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>2.7</version>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>

</project>
