package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.GoodsServiceAddDTO;
import com.chinasie.orion.domain.dto.GoodsServiceStoreDTO;
import com.chinasie.orion.domain.vo.GoodsServiceStoreVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.GoodsServiceStoreService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * GoodsServiceStore 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-25 20:59:53
 */
@RestController
@RequestMapping("/goods-service-store")
@Api(tags = "物资/服务入库")
public class GoodsServiceStoreController {

    @Resource
    private GoodsServiceStoreService goodsServiceStoreService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "detail/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】详情", type = "物资/服务入库", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<GoodsServiceStoreVO> detail(@PathVariable(value = "id") String id) throws Exception {
        GoodsServiceStoreVO rsp = goodsServiceStoreService.detail(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param goodsServiceStoreDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "添加入库")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】添加入库", type = "物资/服务入库", subType = "添加入库", bizNo = "")
    public ResponseDTO<GoodsServiceStoreVO> create(@Valid @RequestBody GoodsServiceStoreDTO goodsServiceStoreDTO) throws Exception {
        GoodsServiceStoreVO rsp = goodsServiceStoreService.create(goodsServiceStoreDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 添加入库
     *
     * @param goodsServiceAddDTO goodsServiceAddDTO
     * @return
     * @throws Exception e
     */
    @ApiOperation(value = "点击右侧添加入库")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】点击右侧添加入库", type = "物资/服务入库", subType = "点击右侧添加入库", bizNo = "")
    public ResponseDTO<GoodsServiceStoreVO> detailCreate(@Valid @RequestBody GoodsServiceAddDTO goodsServiceAddDTO) throws Exception {
        GoodsServiceStoreVO rsp = goodsServiceStoreService.detailCreate(goodsServiceAddDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 编辑
     *
     * @param goodsServiceStoreDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @LogRecord(success = "【{USER{#logUserId}}】编辑", type = "物资/服务入库", subType = "编辑", bizNo = "")
    public ResponseDTO<Boolean> edit(@Valid @RequestBody GoodsServiceStoreDTO goodsServiceStoreDTO) throws Exception {
        Boolean rsp = goodsServiceStoreService.edit(goodsServiceStoreDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除", type = "物资/服务入库", subType = "删除批量", bizNo = "")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = goodsServiceStoreService.remove(ids);
        return new ResponseDTO(rsp);
    }

    @ApiOperation("物资/服务入库列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping("/getPage")
    @LogRecord(success = "【{USER{#logUserId}}】物资/服务入库列表", type = "物资/服务入库", subType = "物资/服务入库列表", bizNo = "")
    public ResponseDTO<PageResult<GoodsServiceStoreVO>> getGoodsServiceStorePage(@Valid @RequestBody Page<GoodsServiceStoreDTO> pageRequest) throws Exception {
        return new ResponseDTO<>(goodsServiceStoreService.getGoodsServiceStorePage(pageRequest));
    }

}
