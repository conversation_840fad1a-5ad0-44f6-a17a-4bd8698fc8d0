package com.chinasie.orion.management.handler.status;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.ChangeStatusMessageDTO;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.amqp.handler.AbstractChangeStatusReceiver;
import com.chinasie.orion.bo.PmsMQProducer;
import com.chinasie.orion.management.constant.RequirementMscNodeEnum;
import com.chinasie.orion.management.domain.entity.QuotationManagement;
import com.chinasie.orion.management.domain.entity.RequirementMangement;
import com.chinasie.orion.management.service.QuotationManagementService;
import com.chinasie.orion.management.service.RequirementMangementService;
import com.chinasie.orion.sdk.domain.vo.business.ClassVO;
import com.chinasie.orion.sdk.domain.vo.business.RoleVO;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.RoleRedisHelper;
import com.chinasie.orion.sdk.helper.RoleUserHelper;
import com.chinasie.orion.sdk.util.IdUtils;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * 客户管理状态变更
 */
@Component
@Slf4j
public class QuotationManagementChangeStatusReceiver extends AbstractChangeStatusReceiver {

    private static final String CURRENT_CLASS = "QuotationManagement";

    @Resource
    private QuotationManagementService quotationManagementService;

    @Resource
    private ClassRedisHelper classRedisHelper;

    @Autowired
    private RequirementMangementService requirementMangementService;

    @Autowired
    protected PmsMQProducer mqProducer;
    @Autowired
    private RoleUserHelper roleUserHelper;
    @Autowired
    private RoleRedisHelper roleRedisHelper;


    @Override
    protected void process(ChangeStatusMessageDTO msg, Channel channel, Message message) {
        log.info("报价管理状态更改消息消费：{}", msg);
        if (ObjectUtil.isNotEmpty(msg)) {
            ClassVO classVO = classRedisHelper.classInfo(IdUtils.getCode(msg.getBusinessId()));
            if (Objects.nonNull(classVO)) {
                if (CURRENT_CLASS.equals(classVO.getClassName())) {
                    msg.setClassName(classVO.getClassName());
                    ThreadUtil.execAsync(() -> {
                        try {
                            consumerCreateMessage(msg);
                        } catch (Exception e) {
                            processError(msg, channel, message, e);
                        }
                    });
                }
            }
        }
    }

    @Override
    protected void processError(ChangeStatusMessageDTO msg, Channel channel, Message message, Exception ex) {
        log.error("报价管理状态更改消息消费异常，【{}】,message，【{}】,", JSONUtil.toJsonStr(msg), message, ex);
    }


    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = CURRENT_CLASS, durable = "true", autoDelete = "false"),
            exchange = @Exchange(value = "${orion.amqp.change-status-v2.exchange}", type = ExchangeTypes.DIRECT),
            key = CURRENT_CLASS
    ))
    @Override
    public void receiver(ChangeStatusMessageDTO msg, Channel channel, Message message) throws IOException {
        super.receiver(msg, channel, message);
    }

    /**
     * 消费消息
     *
     * @param message 消息
     */
    private void consumerCreateMessage(ChangeStatusMessageDTO message) {

        LambdaUpdateWrapper<QuotationManagement> wrapper = new LambdaUpdateWrapper<>(QuotationManagement.class);
        wrapper.eq(QuotationManagement::getId, message.getBusinessId());
        wrapper.set(QuotationManagement::getStatus, message.getStatus());
        boolean result = quotationManagementService.update(wrapper);

        QuotationManagement quotationManagement = quotationManagementService.getById(message.getBusinessId());
        RequirementMangement mangement = requirementMangementService.getById(quotationManagement.getRequirementId());
        // 2024-8-27修改，向 ECP公司商务角色下的用户 发送通知
        RoleVO role = roleRedisHelper.getRole("Business_020", mangement.getOrgId());
        if (null != role) {
            List<String> userIds = roleUserHelper.getUserIdsOfRoleId(mangement.getOrgId(), role.getId());
            if (CollectionUtils.isNotEmpty(userIds)) {
                // 发送消息
                sendMessage(quotationManagement.getId(),
                        "/pas/QuotationManagementDetails/" + quotationManagement.getId() + "?query=" + new Date().getTime(),
                        quotationManagement.getQuotationName() + "，审批完成，等待返回投标情况；",
                        userIds,
                        mangement.getPlatformId(),
                        mangement.getOrgId(),
                        RequirementMscNodeEnum.NODE_REQUIREMENT_KTB.getCode()
                );
            }
        }

        log.info("报价管理状态更改消息消费成功-参数:{}-结果:{}", JSONUtil.toJsonStr(message), result);
    }

    /**
     * 发送消息通知
     */
    private void sendMessage(String businessId, String url, String desc, List<String> toUser, String platformId, String orgId, String code) {
        SendMessageDTO sendMsc = SendMessageDTO.builder()
                .businessData(JSON.toJSONString(MapUtil.builder().put("flowType", "意见单审批完成").build()))
                .businessId(businessId)
                .todoStatus(0)
                .todoType(TodoTypeDict.TODO_TYPE_TASK)
                .urgencyLevel(0)
                .businessNodeCode(code)
                .titleMap(MapUtil.builder(new HashMap<String, Object>())
                        .put("$desc$", desc)
                        .build())
                .messageUrl(String.format(url, businessId))
                .messageUrlName(desc)
                .recipientIdList(toUser)
                .senderTime(new Date())
                .senderId(CurrentUserHelper.getCurrentUserId())
                .platformId(platformId)
                .orgId(orgId)
                .build();
        mqProducer.sendPmsMessage(sendMsc);
    }

}
