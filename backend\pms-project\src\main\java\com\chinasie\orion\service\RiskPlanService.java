package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.RiskPlanDTO;
import com.chinasie.orion.domain.entity.RiskPlan;
import com.chinasie.orion.domain.vo.RiskPlanVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/02/11:22
 * @description:
 */
public interface RiskPlanService extends OrionBaseService<RiskPlan> {

    /**
     * 新增风险预案
     * @param riskPlanDTO
     * @return
     * @throws Exception
     */
    String saveRiskPlan(RiskPlanDTO riskPlanDTO) throws Exception;

    /**
     * 批量新增风险预案
     *  @param projectId
     * @param riskPlanList
     * @return
     * @throws Exception
     */
    Boolean saveBatchRiskPlan(String projectId, List<RiskPlanDTO> riskPlanList) throws Exception;


    /**
     * 同步策划风险
     * @param projectId
     * @return
     * @throws Exception
     */
    Boolean synApprovalRiskPlan(String projectId) throws Exception;


    /**
     * 获取风险预案分页
     * @param pageRequest
     * @return
     * @throws Exception
     */
    Page<RiskPlanVO> getRiskPlanPage(Page<RiskPlanDTO> pageRequest) throws Exception;

    /**
     * 获取风险预案详情
     * @param id
     * @return
     * @throws Exception
     */
    RiskPlanVO getRiskPlanDetail(String id) throws Exception;

    /**
     * 编辑风险预案
     * @param riskPlanDTO
     * @return
     * @throws Exception
     */
    Boolean editRiskPlan(RiskPlanDTO riskPlanDTO) throws Exception;

    /**
     * 批量删除风险预案
     * @param ids
     * @return
     * @throws Exception
     */
    Boolean removeBatchRiskPlan(List<String> ids) throws Exception;
}
