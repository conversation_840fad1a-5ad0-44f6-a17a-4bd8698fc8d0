package com.chinasie.orion.service.approval;

import com.chinasie.orion.domain.entity.approval.ProjectApprovalEstimate;
import com.chinasie.orion.mybatis.service.OrionBaseService;

import java.util.List;

/**
 * <p>
 * ProjectApprovalEstimate 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-07 10:45:22
 */
public interface ProjectApprovalEstimateService  extends OrionBaseService<ProjectApprovalEstimate>{

    /**
     * 通过项目立项id获取概算信息
     *
     * @param projectApprovalId
     * @return
     * @throws Exception
     */
    ProjectApprovalEstimate getEntityByProjectApprovalId(String projectApprovalId) throws Exception;

    /**
     * 通过项目立项获取项目人天信息
     * @param projectApprovalIdList
     * @return
     * @throws Exception
     */
    List<ProjectApprovalEstimate> getPeopleDataByProjectApprovalIds(List<String> projectApprovalIdList) throws Exception;

}
