package com.chinasie.orion.handler.status;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.chinasie.orion.amqp.entity.ChangeStatusMessageDTO;
import com.chinasie.orion.amqp.handler.AbstractChangeStatusReceiver;

import com.chinasie.orion.domain.dto.PersonTrainInfoRecordDTO;
import com.chinasie.orion.domain.entity.TrainManage;
import com.chinasie.orion.enums.StatusEnum;
import com.chinasie.orion.service.PersonTrainInfoRecordService;
import com.chinasie.orion.service.TrainManageService;

import com.chinasie.orion.sdk.domain.vo.business.ClassVO;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.util.IdUtils;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Objects;

/**
 * 培训管理状态变更
 */
@Component
@Slf4j
public class TrainManageChangeStatusReceiver extends AbstractChangeStatusReceiver {


    private static final String CURRENT_CLASS = "TrainManage";

    @Resource
    private TrainManageService trainManageService;

    @Resource
    private ClassRedisHelper classRedisHelper;

    @Resource
    private PersonTrainInfoRecordService personTrainInfoRecordService;



    @Override
    protected void process(ChangeStatusMessageDTO msg, Channel channel, Message message) {
        log.info("培训管理状态更改消息消费：{}", msg);
        if (ObjectUtil.isNotEmpty(msg)) {
            ClassVO classVO = classRedisHelper.classInfo(IdUtils.getCode(msg.getBusinessId()));
            if (Objects.nonNull(classVO)) {
                if (CURRENT_CLASS.equals(classVO.getClassName())) {
                    msg.setClassName(classVO.getClassName());
                    ThreadUtil.execAsync(() -> {
                        try {
                            consumerCreateMessage(msg);
                        } catch (Exception e) {
                            processError(msg, channel, message, e);
                        }
                    });
                }
            }
        }
    }

    @Override
    protected void processError(ChangeStatusMessageDTO msg, Channel channel, Message message, Exception ex) {
        log.error("培训管理状态更改消息消费异常，【{}】,message，【{}】,", JSONUtil.toJsonStr(msg), message, ex);
    }


    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = CURRENT_CLASS, durable = "true", autoDelete = "false"),
            exchange = @Exchange(value = "${orion.amqp.change-status-v2.exchange}", type = ExchangeTypes.DIRECT),
            key = CURRENT_CLASS
    ))
    @Override
    public void receiver(ChangeStatusMessageDTO msg, Channel channel, Message message) throws IOException {
        super.receiver(msg, channel, message);
    }

    /**
     * 消费消息
     *
     * @param message 消息
     */
    private void consumerCreateMessage(ChangeStatusMessageDTO message) throws Exception {

        LambdaUpdateWrapper<TrainManage> wrapper=new LambdaUpdateWrapper<>(TrainManage.class);
        wrapper.eq(TrainManage::getId,message.getBusinessId());
        wrapper.set(TrainManage::getStatus,message.getStatus());
        boolean result = trainManageService.update(wrapper);

        // 当培训 完成后
        if(StatusEnum.ENABLE.getIndex() == message.getStatus()){
            PersonTrainInfoRecordDTO personTrainInfoRecordDTO = new PersonTrainInfoRecordDTO();
            personTrainInfoRecordDTO.setTrainName(personTrainInfoRecordDTO.getTrainName());
            // 培训人员 会修改
            personTrainInfoRecordDTO.setTrainLecturer(personTrainInfoRecordDTO.getTrainLecturer());
            personTrainInfoRecordDTO.setLessonHour(personTrainInfoRecordDTO.getLessonHour());
            personTrainInfoRecordDTO.setTrainNumber(personTrainInfoRecordDTO.getTrainNumber());
            personTrainInfoRecordDTO.setExpireTime(personTrainInfoRecordDTO.getExpireTime());
            personTrainInfoRecordDTO.setContent(personTrainInfoRecordDTO.getContent());
            personTrainInfoRecordDTO.setBaseCode(personTrainInfoRecordDTO.getBaseCode());
            personTrainInfoRecordDTO.setBaseName(personTrainInfoRecordDTO.getBaseName());
            personTrainInfoRecordDTO.setEndDate(personTrainInfoRecordDTO.getEndDate());
            personTrainInfoRecordDTO.setIsEquivalent(Boolean.FALSE);
            personTrainInfoRecordService.create(personTrainInfoRecordDTO);
        };

        log.info("培训管理状态更改消息消费成功-参数:{}-结果:{}", JSONUtil.toJsonStr(message), result);
    }

}
