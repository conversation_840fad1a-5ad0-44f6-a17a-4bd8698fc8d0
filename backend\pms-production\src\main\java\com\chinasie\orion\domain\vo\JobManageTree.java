package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class JobManageTree {

    @ApiModelProperty("状态")
    Integer status;

    @ApiModelProperty("作业名称")
    String name;

    @ApiModelProperty("工单号")
    String number;

    @ApiModelProperty("责任人id")
    String rspUserId;

    @ApiModelProperty("责任人姓名")
    String rspUserName;

    @ApiModelProperty("是否已选择(0未选择   1已选择)")
    Integer selected;

    /**
     * 是否匹配（0未匹配  1匹配）
     */
    @ApiModelProperty(value = "是否匹配（0未匹配  1匹配）")
    private Integer matchUp;

    String code;

    /**
     * 父级唯一值
     */
    String parentCode;

    /**
     * 标识
     */
    String className;

    @ApiModelProperty("大修轮次")
    String repairRound;

    @ApiModelProperty("工作中心")
    String workCenter;

    @ApiModelProperty("工期")
    Integer workDuration;

    @ApiModelProperty("实际开始")
    Date actualBeginTime;

    @ApiModelProperty("实际结束")
    Date actualEndTime;

    @ApiModelProperty("计划开始")
    Date beginTime;

    @ApiModelProperty("计划结束")
    Date endTime;

    @ApiModelProperty("N/O")
    String nOrO;

    @ApiModelProperty("阶段")
    String phase;

}