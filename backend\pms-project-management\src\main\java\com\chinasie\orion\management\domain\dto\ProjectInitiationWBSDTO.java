package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ProjectInitiationWBS DTO对象
 *
 * <AUTHOR>
 * @since 2024-07-16 14:52:10
 */
@ApiModel(value = "ProjectInitiationWBSDTO对象", description = "项目立项WBS预算")
@Data
@ExcelIgnoreUnannotated
public class ProjectInitiationWBSDTO extends ObjectDTO implements Serializable {

    /**
     * 项目层次等级
     */
    @ApiModelProperty(value = "项目层次等级")
    @ExcelProperty(value = "项目层次等级 ", index = 0)
    private String projectLevel;

    /**
     * 工作分解结构元素 (WBS 元素)
     */
    @ApiModelProperty(value = "工作分解结构元素 (WBS 元素)")
    @ExcelProperty(value = "工作分解结构元素 (WBS 元素) ", index = 1)
    private String wbsElement;

    /**
     * PS: 短描述 (第一行文本)
     */
    @ApiModelProperty(value = "PS: 短描述 (第一行文本)")
    @ExcelProperty(value = "PS: 短描述 (第一行文本) ", index = 2)
    private String description;

    /**
     * 功能范围
     */
    @ApiModelProperty(value = "功能范围")
    @ExcelProperty(value = "功能范围 ", index = 3)
    private String functionalScope;

    /**
     * I开头状态
     */
    @ApiModelProperty(value = "I开头状态")
    @ExcelProperty(value = "I开头状态 ", index = 4)
    private String initialStatus;

    /**
     * 利润中心编码
     */
    @ApiModelProperty(value = "利润中心编码")
    @ExcelProperty(value = "利润中心编码 ", index = 5)
    private String profitCenterCode;

    /**
     * 利润中心名称
     */
    @ApiModelProperty(value = "利润中心名称")
    @ExcelProperty(value = "利润中心名称 ", index = 6)
    private String profitCenterName;

    /**
     * 工厂
     */
    @ApiModelProperty(value = "工厂")
    @ExcelProperty(value = "工厂 ", index = 7)
    private String company;

    /**
     * 负责人编号
     */
    @ApiModelProperty(value = "负责人编号")
    @ExcelProperty(value = "负责人编号 ", index = 8)
    private String directorCode;

    /**
     * 负责人姓名（项目管理者）
     */
    @ApiModelProperty(value = "负责人姓名（项目管理者）")
    @ExcelProperty(value = "负责人姓名（项目管理者） ", index = 9)
    private String directorName;

    /**
     * 立项编号
     */
    @ApiModelProperty(value = "立项编号")
    @ExcelProperty(value = "立项编号 ", index = 10)
    private String projectNumber;


    /**
     * 业务分类
     */
    @ApiModelProperty(value = "业务分类")
    @ExcelProperty(value = "业务分类 ", index = 11)
    private String business;

    /**
     * 业务分类名称
     */
    @ApiModelProperty(value = "业务分类名称")
    @ExcelProperty(value = "业务分类名称 ", index = 12)
    private String businessName;



}
