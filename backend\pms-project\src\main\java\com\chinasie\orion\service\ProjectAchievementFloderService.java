package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.ProjectAchievementFloderDTO;
import com.chinasie.orion.domain.entity.ProjectAchievementFloder;
import com.chinasie.orion.domain.vo.ProjectAchievementFloderVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

/**
 * <p>
 * ProjectAchievementFloder 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-07 13:43:27
 */
public interface ProjectAchievementFloderService extends OrionBaseService<ProjectAchievementFloder> {
    /**
     *  详情
     *
     * * @param id
     */
    ProjectAchievementFloderVO detail(String id)  throws Exception;

    /**
     *  新增
     *
     * * @param projectAchievementFloderDTO
     */
    ProjectAchievementFloderVO create(ProjectAchievementFloderDTO projectAchievementFloderDTO)  throws Exception;

    /**
     *  编辑
     *
     * * @param projectAchievementFloderDTO
     */
    Boolean edit(ProjectAchievementFloderDTO projectAchievementFloderDTO) throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     */
    Page<ProjectAchievementFloderVO> pages(Page<ProjectAchievementFloderDTO> pageRequest) throws Exception;


    List<ProjectAchievementFloderVO> lists(String approvalId);
}

