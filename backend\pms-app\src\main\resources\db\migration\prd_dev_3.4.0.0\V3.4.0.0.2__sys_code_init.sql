INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1793485778391891968', '简称', '0', '9hi17f428700879d4564aaabb85233673f74', '', 'fixedValue', '1', 'PMS', '0', 1, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-05-23 11:35:06', '314j1000000000000000000', '2024-05-23 11:35:06', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1793485889511587840', '年', '0', '9hi17f428700879d4564aaabb85233673f74', '', 'DATE_YYYY', '1', '', '0', 2, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-05-23 11:35:32', '314j1000000000000000000', '2024-05-23 11:35:32', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1793486008390746112', '杠', '0', '9hi17f428700879d4564aaabb85233673f74', '', 'fixedValue', '', '-', '0', 3, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-05-23 11:36:00', '314j1000000000000000000', '2024-05-23 11:36:00', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1793486203136475136', '流水', '0', '9hi17f428700879d4564aaabb85233673f74', 'f9g6e500b2bd781b41ab9af0c0fd7e8670b7', 'piPer', '1', '', '4', 5, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-05-23 11:36:47', '314j1000000000000000000', '2024-05-23 11:36:47', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);


UPDATE sys_code_segment SET logic_status=-1 WHERE id IN(
                                                        's3rd101cff6d954c49199521609cb879ba02',
                                                        's3rdb68439d8ba0b4716b212ac0539c73579'
    );

UPDATE sys_code_mapping_relation SET code_rules = '9hi17f428700879d4564aaabb85233673f74' WHERE id='e96m2030be015a5041349b37e2352db87045';