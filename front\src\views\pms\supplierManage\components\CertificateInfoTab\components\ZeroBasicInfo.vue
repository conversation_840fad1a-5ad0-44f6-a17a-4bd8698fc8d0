<script setup lang="ts">
import { BasicCard } from 'lyra-component-vue3';
import {
  inject, reactive, watchEffect,
} from 'vue';
import dayjs from 'dayjs';

const baseInfoProps = reactive({
  list: [
    {
      label: '营业执照注册号/统一社会信用代码',
      field: 'businessLicenseNum',
    },
    {
      label: '营业执照有效期起',
      field: 'businessLicenseStart',
      valueRender: ({ record }) => (record.businessLicenseStart ? dayjs(record.businessLicenseStart).format('YYYY-MM-DD') : '---'),
    },
    {
      label: '营业执照有效期至',
      field: 'businessLicenseEnd',
      valueRender: ({ record }) => (record.businessLicenseEnd ? dayjs(record.businessLicenseEnd).format('YYYY-MM-DD') : '---'),
    },
    {
      label: '经营范围',
      field: 'operationScope',
      gridColumn: '1 / span 2',
      wrap: true,
    },
  ],
  column: 2,
  dataSource: {},
});

const supplierInfo = inject('supplierInfo');
watchEffect(() => {
  baseInfoProps.dataSource = supplierInfo;
});
</script>

<template>
  <BasicCard
    title="证件信息"
    :grid-content-props="baseInfoProps"
    :isBorder="false"
  />
</template>

<style scoped lang="less">

</style>