package com.chinasie.orion.repository;

import com.chinasie.orion.domain.entity.ProvisionalIncomeAccounting;
import com.chinasie.orion.domain.vo.InvoicingRevenueAccountingVO;
import com.chinasie.orion.domain.vo.ProvisionalIncomeAccountingVO;
import org.apache.ibatis.annotations.Mapper;

import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <p>
 * ProvisionalIncomeAccounting Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-19 14:52:56
 */
@Mapper
public interface ProvisionalIncomeAccountingMapper extends  OrionBaseMapper  <ProvisionalIncomeAccounting> {
    ProvisionalIncomeAccountingVO getTotal(@Param("contractId") String contractId);
    List<ProvisionalIncomeAccountingVO> getMilestoneTotal(@Param("milestoneIds") List<String> milestoneIds);


}