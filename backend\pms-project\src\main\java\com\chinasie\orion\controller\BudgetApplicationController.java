package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.BudgetApplicationDTO;
import com.chinasie.orion.domain.dto.BudgetApplicationSaveDTO;
import com.chinasie.orion.domain.vo.BudgetApplicationVO;
import com.chinasie.orion.service.BudgetApplicationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;


import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * BudgetApplication 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:09
 */
@RestController
@RequestMapping("/budgetApplication")
@Api(tags = "预算申请表")
public class BudgetApplicationController {

    @Autowired
    private BudgetApplicationService budgetApplicationService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】详情】", type = "预算申请表", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<BudgetApplicationVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        BudgetApplicationVO rsp = budgetApplicationService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param budgetApplicationDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#budgetApplicationDTO.name}}】", type = "预算申请表", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody BudgetApplicationDTO budgetApplicationDTO) throws Exception {
        String rsp =  budgetApplicationService.create(budgetApplicationDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param budgetApplicationDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#budgetApplicationDTO.name}}】", type = "预算申请表", subType = "编辑", bizNo = "{{#budgetApplicationDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  BudgetApplicationDTO budgetApplicationDTO) throws Exception {
        Boolean rsp = budgetApplicationService.edit(budgetApplicationDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "预算申请表", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = budgetApplicationService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "预算申请表", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = budgetApplicationService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "预算申请表", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<BudgetApplicationVO>> pages(@RequestBody Page<BudgetApplicationDTO> pageRequest) throws Exception {
        Page<BudgetApplicationVO> rsp =  budgetApplicationService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("预算申请表导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "预算申请表", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        budgetApplicationService.downloadExcelTpl(response);
    }

    @ApiOperation("预算申请表导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "预算申请表", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = budgetApplicationService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("预算申请表导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "预算申请表", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  budgetApplicationService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消预算申请表导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "预算申请表", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  budgetApplicationService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("预算申请表导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "预算申请表", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        budgetApplicationService.exportByExcel(searchConditions, response);
    }



    /**
     * 新增
     *
     * @param budgetApplicationDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "保存预算申请")
    @RequestMapping(value = "/saveBatch", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】保存预算申请", type = "预算申请表", subType = "保存预算申请", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> saveBatchBudgetApplication(@RequestBody BudgetApplicationSaveDTO budgetApplicationDTO) throws Exception {
        Boolean rsp =  budgetApplicationService.saveBatchBudgetApplication(budgetApplicationDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }



    /**
     * 查询列表
     *
     * @param
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "列表查询")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "预算申请表", subType = "列表查询", bizNo = "")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public ResponseDTO<List<BudgetApplicationVO>> getList(@RequestParam String formId) throws Exception {
        List<BudgetApplicationVO> rsp =  budgetApplicationService.getList(formId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 查询列表
     *
     * @param
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "引入概算模板数据")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】引入概算模板数据", type = "预算申请表", subType = "引入概算模板数据", bizNo = "")
    @RequestMapping(value = "/getEstimateList", method = RequestMethod.GET)
    public ResponseDTO<List<BudgetApplicationVO>> getEstimateList(@RequestParam String projectId) throws Exception {
        List<BudgetApplicationVO> rsp =  budgetApplicationService.getEstimateList(projectId);
        return new ResponseDTO<>(rsp);
    }
}
