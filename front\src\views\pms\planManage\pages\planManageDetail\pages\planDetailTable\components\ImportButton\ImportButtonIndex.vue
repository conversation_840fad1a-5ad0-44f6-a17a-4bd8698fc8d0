<template>
  <Upload
    ref="upload"
    :before-upload="beforeUpload"
    :showUploadList="false"
    :multiple="false"
    accept=".xlsx"
    class="upload-btn-wrap"
  >
    <AButton
      :disabled="isDisabled"
    >
      计划导入
    </AButton>
  </Upload>
</template>

<script lang="ts" setup>
import { Button as AButton, message, Upload } from 'ant-design-vue';
import Api from '/@/api';
import { computed, unref, watch } from 'vue';

const props = defineProps<{
  selectKeys: string[],
  onSuccess: ()=>void
}>();

const isDisabled = computed(() => !(unref(props.selectKeys).length === 1));

function beforeUpload(res) {
  const formData = new FormData();
  formData.append('file', res);
  new Api('/plan').fetch(formData, `scheme-ie/import/excel/${unref(props.selectKeys)[0]}`, 'POST').then((res) => {
    message.success('操作成功');
    props.onSuccess && props.onSuccess();
  });
  return false;
}
</script>

<style scoped>

</style>
