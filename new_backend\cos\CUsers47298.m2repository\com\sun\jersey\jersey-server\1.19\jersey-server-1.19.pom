<?xml version="1.0" encoding="UTF-8"?>
<!--

    DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS HEADER.

    Copyright (c) 2010-2015 Oracle and/or its affiliates. All rights reserved.

    The contents of this file are subject to the terms of either the GNU
    General Public License Version 2 only ("GPL") or the Common Development
    and Distribution License("CDDL") (collectively, the "License").  You
    may not use this file except in compliance with the License.  You can
    obtain a copy of the License at
    http://glassfish.java.net/public/CDDL+GPL_1_1.html
    or packager/legal/LICENSE.txt.  See the License for the specific
    language governing permissions and limitations under the License.

    When distributing the software, include this License Header Notice in each
    file and include the License file at packager/legal/LICENSE.txt.

    GPL Classpath Exception:
    Oracle designates this particular file as subject to the "Classpath"
    exception as provided by Oracle in the GPL Version 2 section of the License
    file that accompanied this code.

    Modifications:
    If applicable, add the following below the License Header, with the fields
    enclosed by brackets [] replaced by your own identifying information:
    "Portions Copyright [year] [name of copyright owner]"

    Contributor(s):
    If you wish your version of this file to be governed by only the CDDL or
    only the GPL Version 2, indicate your decision by adding "[Contributor]
    elects to include this software in this distribution under the [CDDL or GPL
    Version 2] license."  If you don't indicate a single choice of license, a
    recipient has the option to distribute your version of this file under
    either the CDDL, the GPL Version 2 or to extend the choice of license to
    its licensees as provided above.  However, if you add GPL Version 2 code
    and therefore, elected the GPL Version 2 license, then the option applies
    only if the new code is made subject to such option by the copyright
    holder.

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.sun.jersey</groupId>
        <artifactId>jersey-project</artifactId>
        <version>1.19</version>
    </parent>
    <artifactId>jersey-server</artifactId>
    <packaging>jar</packaging>
    <name>jersey-server</name>
    <dependencies>
        <dependency>
            <groupId>javax.mail</groupId>
            <artifactId>mail</artifactId>
            <version>1.4</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>2.1</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>jsr250-api</artifactId>
            <version>1.0</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.osgi</groupId>
            <artifactId>osgi_R4_core</artifactId>
            <version>1.0</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-io</artifactId>
            <version>1.3.2</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>release</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <configuration>
                            <links>
                                <link>http://jsr311.java.net/nonav/releases/1.1</link>
                            </links>
                            <excludePackageNames>com.sun.jersey.server.impl:com.sun.research.ws.wadl</excludePackageNames>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>cobertura</id>
            <activation>
                <property>
                    <name>cobertura</name>
                </property>
            </activation>
            <dependencies>
                <dependency>
                    <groupId>com.sun.jersey</groupId>
                    <artifactId>jersey-core</artifactId>
                    <version>${project.version}</version>
                    <classifier>cobertura</classifier>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>default</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <dependencies>
                <dependency>
                    <groupId>com.sun.jersey</groupId>
                    <artifactId>jersey-core</artifactId>
                    <version>${project.version}</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>securityOff</id>
            <dependencies>
                <dependency>
                    <groupId>com.sun.jersey</groupId>
                    <artifactId>jersey-core</artifactId>
                    <version>${project.version}</version>
                </dependency>
            </dependencies>
            <properties>
                <surefire.security.argline></surefire.security.argline>
            </properties>
        </profile>
    </profiles>

    <build>
        <resources>
            <resource>
                <directory>${basedir}/src/main/java</directory>
                <includes>
                    <include>META-INF/**/*</include>
                </includes>
            </resource>
            <resource>
                <directory>${basedir}/src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>

        <testResources>
            <testResource>
                <directory>${basedir}/src/test/resources</directory>
                <filtering>true</filtering>
            </testResource>
        </testResources>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>com.sun.tools.xjc.maven2</groupId>
                <artifactId>maven-jaxb-plugin</artifactId>
                <version>1.1.1</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <generatePackage>com.sun.research.ws.wadl</generatePackage>
                    <catalog>${basedir}/etc/catalog.xml</catalog>
                    <schemaDirectory>${basedir}/etc</schemaDirectory>
                    <includeSchemas>
                        <includeSchema>wadl.xsd</includeSchema>
                    </includeSchemas>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <extensions>true</extensions>
                <configuration>
                    <instructions>
                        <Bundle-SymbolicName>com.sun.jersey.jersey-server</Bundle-SymbolicName>
                        <Export-Package>
                            com.sun.jersey.api,
                            com.sun.jersey.api.container.*,
                            com.sun.jersey.api.core,
                            com.sun.jersey.api.model,
                            com.sun.jersey.api.view,
                            com.sun.jersey.api.wadl.*,
                            com.sun.jersey.server.*,
                            com.sun.jersey.spi.container.*,
                            com.sun.jersey.spi.dispatch,
                            com.sun.jersey.spi.monitoring,
                            com.sun.jersey.spi.resource,
                            com.sun.jersey.spi.scanning,
                            com.sun.jersey.spi.template,
                            com.sun.jersey.spi.uri.*,
                            com.sun.research.ws.wadl,
                            jersey.repackaged.org.objectweb.asm.*
                        </Export-Package>
                        <Import-Package>
                            com.sun.jersey.core.osgi;resolution:=optional,
                            com.sun.net.httpserver;resolution:="optional",
                            javax.annotation.*;resolution:=optional,
                            javax.inject.*;resolution:=optional,
                            javax.mail.*;resolution:="optional",
                            javax.xml.bind.*;resolution:="optional",
                            javax.xml.namespace;resolution:="optional",
                            javax.xml.transform.*;resolution:="optional",
                            *
                        </Import-Package>
                        <DynamicImport-Package>*</DynamicImport-Package>
                    </instructions>
                    <unpackBundle>true</unpackBundle>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <phase>package</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <forkCount>1</forkCount>
                    <reuseForks>false</reuseForks>
                    <argLine>${surefire.security.argline}</argLine>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <properties>
        <surefire.security.argline>-Djava.security.manager
            -Djava.security.policy=${project.build.directory}/test-classes/server.policy</surefire.security.argline>
    </properties>

</project>
