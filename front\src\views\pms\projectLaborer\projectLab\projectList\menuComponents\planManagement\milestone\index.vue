<template>
  <div class="milestone-wrap">
    <div>里程碑时间轴 {{ percentage }}%</div>
    <br>
    <div style="overflow-x: auto">
      <a-steps progress-dot>
        <a-step
          v-for="s in dataSource"
          :key="s.id"
        >
          <template #title>
            <div :class="['step-title', s.taskStatusId === '1' ? 'yes' : 'no']">
              {{ s.name }}
            </div>
          </template>
          <template #description>
            <div class="step-sub-title">
              <div>
                状态: <DataStatusTag
                  v-if="s.dataStatus"
                  :status-data="s.dataStatus"
                />
              </div>
              <div>{{ s.planPredictEndTime }}</div>
            </div>
          </template>
        </a-step>
      </a-steps>
    </div>
  </div>
  <BasicTable
    :show-index-column="false"
    :pagination="false"
    :columns="columns"
    :data-source="dataSource"
    :loading="loading"
    row-key="id"
    :resize-height-offset="30"
  >
    <template #status="{ record }">
      <DataStatusTag
        v-if="record.dataStatus"
        :status-data="record.dataStatus"
      />
    </template>
  </BasicTable>
</template>

<script>
import { Steps } from 'ant-design-vue';
import {
  BasicTable, DataStatusTag,
} from 'lyra-component-vue3';
import { reactive, toRefs, onMounted } from 'vue';
import Api from '/@/api';
export default {
  name: 'Milestone',
  components: {
    BasicTable,
    ASteps: Steps,
    AStep: Steps.Step,
    DataStatusTag,
  },
  props: {
    formId: {
      type: String,
      default: '',
    },
    pageType: {
      type: String,
      default: 'page',
    },
  },
  setup(props) {
    const state = reactive({
      percentage: 0,
      columns: [
        {
          title: '名称11',
          dataIndex: 'name',
          align: 'left',
          width: 300,
        },
        {
          title: '结束日期',
          dataIndex: 'planPredictEndTime',
        },
        {
          title: '状态',
          dataIndex: 'status',
          align: 'left',
          width: 150,
          ellipsis: true,
          slots: { customRender: 'status' },
        },
        {
          title: '负责人',
          dataIndex: 'principalName',
        },
        {
          title: '修改日期',
          dataIndex: 'modifyTime',
        },
      ],
      dataSource: [],
      loading: false,
    });
    function getList() {
      state.loading = true;
      const love = {
        id: props.formId,
        className: 'Plan',
        moduleName: '项目管理-计划管理-里程碑', // 模块名称
        type: 'GET', // 操作类型
        remark: `获取/搜索了【${props.formId}】里程碑列表`,
      };
      new Api('/pms', love)
        .fetch('', `milestone/list?projectId=${props.formId}`)
        .then((res) => {
          state.loading = false;
          res = res || [];
          state.dataSource = res;
          if (res.length) {
            const list = res.filter((s) => s.status === 103);
            const num = (list.length / res.length) * 100;
            state.percentage = parseInt(num);
          }
        })
        .catch((_) => {
          state.loading = false;
        });
    }
    onMounted(() => {
      getList();
    });

    return {
      ...toRefs(state),
    };
  },
};
</script>
<style scoped lang="less">
.milestone-wrap {
  padding: ~`getPrefixVar('content-padding-top')` ~`getPrefixVar('content-padding-left')`;
}
  .step-title {
    font-size: 14px;
    border-radius: 5px;
    padding: 3px 5px;
    margin-bottom: 5px;
  }
  .yes {
    color: #20b57e;
    border: 1px solid #20b57e;
  }
  .no {
    color: #f1b63f;
    border: 1px solid #f1b63f;
  }
  .step-sub-title {
    font-size: 12px;
    color: #969eb4;
  }
  :deep(.ant-steps) {
    .ant-steps-icon-dot {
      top: 1px !important;
      background-color: #969eb4 !important;
    }
  }
</style>
