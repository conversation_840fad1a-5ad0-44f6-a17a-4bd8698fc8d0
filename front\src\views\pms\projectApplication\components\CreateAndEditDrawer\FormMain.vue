<script setup lang="ts">
import {
  BasicButton,
  BasicForm, BasicInputSelectModal,
  DataStatusTag,
  FormSchema,
  OrionTable,
  SelectUserModal,
  useForm,
  useModal,
} from 'lyra-component-vue3';
import TableRender from './TableRender.vue';
import dayjs from 'dayjs';
import {
  computed, h, nextTick, onMounted, ref, Ref,
} from 'vue';
import { CheckProjectModal } from '..';
import Api from '/@/api';
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import Wrap from '/@/views/pms/projectLaborer/projectLab/projectList/components/Wrap.vue';
import { Modal } from 'ant-design-vue';
import SourceModal from '/@/views/pms/projectLaborer/projectLab/projectList/components/SourceModal/index.vue';

const props = defineProps<{
  detail: Record<string, any>
}>();

interface UserData {
  id: string,
  name: string
}

const [registerCheckProject, { openModal: openCheckProjectModal }] = useModal();
const [registerSelectUser, { openModal: openSelectUserModal }] = useModal();
const [registerSourceModal, { openModal: openSourceModal }] = useModal();
const tableRef: Ref = ref();
const formRef: Ref = ref();
const fileRef: Ref = ref();
const selectRows = ref([]);
const deptOptions: Ref<any[]> = ref([]);
// 已选择项目id
const selectProjectId: Ref<string> = ref();
const isSelectProject : Ref<boolean> = ref(false);
// 已选择项目负责人
const selectUser: Ref<UserData> = ref();
const disabledBtn = computed(() => selectRows.value.length === 0);
// 申报项目
const projectTimes = ref({}); // 选中的申报项目的起止时间
const projectSubTypeName = ref('');
const projectRecords = ref([]); // 选中的申报项目的数据
const projectOptions = {
  title: '添加立项项目',
  selectType: 'radio',
  smallSearchField: ['name', 'number'],
  async tableApi(params) {
    return await new Api('/pms/projectDeclare/project/page').fetch({
      ...params,
    }, '', 'POST');
  },
  tableColumns: [
    {
      title: '编号',
      dataIndex: 'number',
    },
    {
      title: '名称',
      dataIndex: 'name',
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      slots: { customRender: 'status' },
    },
    {
      title: '项目负责人',
      dataIndex: 'resPersonName',
    },
  ],
  onOk(value) {
    // value为records
    projectRecords.value = value;
    projectTimes.value.startTime = projectRecords.value?.[0]?.projectStartTime;
    projectTimes.value.endTime = projectRecords.value?.[0]?.projectEndTime;
    projectSubTypeName.value = projectRecords.value?.[0]?.projectSubTypeName;
    setFieldsValue({
      projectId: projectRecords.value?.[0]?.id ?? '',
      projectNumber: projectRecords.value?.[0]?.number ?? '',
    });
    return new Promise((resolve, reject) => {
      resolve(value);
    });
  },
};

const schemas: FormSchema[] = [
  // {
  //   field: 'title',
  //   component: 'Input',
  //   label: '',
  //   colProps: {
  //     span: 24,
  //   },
  //   renderColContent() {
  //     return h(DetailsLayout, {
  //       title: '项目关键信息',
  //       isFormItem: true,
  //     });
  //   },
  // },
  // {
  //   field: 'projectNumber',
  //   component: 'InputSearch',
  //   label: '项目编号',
  //   required: true,
  //   componentProps: {
  //     disabled: props.detail?.operationType === 'fixed',
  //     allowClear: false,
  //     onChange() {
  //       selectProjectId.value = undefined;
  //     },
  //     onSearch(value:string, event:Record<string, any>) {
  //       if (event.type === 'click') {
  //         openCheckProjectModal(true, {});
  //       }
  //     },
  //   },
  // },
  // {
  //   field: 'projectName',
  //   component: 'InputSearch',
  //   label: '项目名称',
  //   required: true,
  //   componentProps: {
  //     disabled: props.detail?.operationType === 'fixed',
  //     allowClear: false,
  //     onChange() {
  //       selectProjectId.value = undefined;
  //     },
  //     onSearch(value:string, event:Record<string, any>) {
  //       if (event.type === 'click') {
  //         openCheckProjectModal(true, {});
  //       }
  //     },
  //   },
  // },
  //
  // {
  //   field: 'projectType',
  //   component: 'ApiSelect',
  //   label: '项目类型',
  //   required: true,
  //   componentProps: {
  //     allowClear: false,
  //     api: () => new Api('/pms/dict/code').fetch('', 'pms_project_type', 'GET'),
  //     labelField: 'description',
  //   },
  // },
  // {
  //   field: 'projectSubType',
  //   component: 'ApiSelect',
  //   label: '子类型',
  //   required: true,
  //   ifShow: ({ model }) => model.projectType === 'invest_server',
  //   componentProps: {
  //     allowClear: false,
  //     api: () => new Api('/pms/dict/code').fetch('', 'business_pms_investment', 'GET'),
  //     labelField: 'description',
  //   },
  // },

  // {
  //   field: 'estimateAmt',
  //   component: 'InputNumber',
  //   label: '项目预估金额',
  //   componentProps: {
  //     style: 'width:100%',
  //     addonAfter: '元',
  //     precision: 0,
  //     min: 0,
  //     formatter(value:string) {
  //       return value.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  //     },
  //     maxLength: 15,
  //   },
  // },
  // {
  //   field: 'projectSource',
  //   component: 'ApiSelect',
  //   label: '项目来源',
  //   componentProps: {
  //     api: async () => await getDict('dict1714906542609989632'),
  //     labelField: 'description',
  //   },
  // },
  // {
  //   field: 'projectStartTime',
  //   component: 'DatePicker',
  //   label: '项目开始时间',
  //   required: true,
  //   componentProps: ({ formModel }) => ({
  //     disabledDate: (date:Date) => (formModel.projectEndTime ? dayjs(date).valueOf() >= dayjs(formModel.projectEndTime).valueOf() : false),
  //   }),
  // },
  // {
  //   field: 'projectEndTime',
  //   component: 'DatePicker',
  //   label: '项目结束时间',
  //   required: true,
  //   componentProps: ({ formModel }) => ({
  //     disabledDate: (date:Date) => (formModel.projectStartTime ? dayjs(date).valueOf() <= dayjs(formModel.projectStartTime).valueOf() : false),
  //   }),
  // },
  // {
  //   field: 'resUserName',
  //   component: 'InputSearch',
  //   label: '项目负责人',
  //   required: true,
  //   componentProps: {
  //     placeholder: '请选择',
  //     allowClear: false,
  //     onFocus(e:Record<string, any>) {
  //       e.target.blur();
  //       openSelectUserModal(true);
  //     },
  //   },
  // },
  // {
  //   field: 'rspDeptId',
  //   component: 'Select',
  //   label: '责任部门',
  //   required: true,
  //   componentProps: {
  //     disabled: true,
  //     options: deptOptions,
  //   },
  // },

  {
    field: 'title',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(DetailsLayout, {
        title: '申报信息',
        isFormItem: true,
      });
    },
  },
  {
    field: 'projectId',
    label: '申报项目:',
    colProps: { span: 12 },
    componentProps: {
    },
    render: ({ model, field }) => h(BasicInputSelectModal, {
      placeholder: '请选择',
      ...projectOptions,
      // 基础选择弹窗，回显要传records
      value: projectRecords.value,
    }),
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'projectNumber',
    label: '项目编号:',
    colProps: { span: 12 },
    componentProps: {
      disabled: true,
    },
    component: 'Input',
  },

  {
    field: 'applyReason',
    label: '项目申请理由',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '请输入',
      maxlength: 500,
      showCount: true,
      rows: 4,
    },
    component: 'InputTextArea',
  },
  {
    field: 'projectBackground',
    label: '项目背景摘要',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '请输入',
      maxlength: 500,
      showCount: true,
      rows: 4,
    },
    component: 'InputTextArea',
  },
  {
    field: 'projectTarget',
    label: '项目目标摘要',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '请输入',
      maxlength: 500,
      showCount: true,
      rows: 4,
    },
    component: 'InputTextArea',
  },
  {
    field: 'technologyPlan',
    label: '技术方案摘要',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '请输入',
      maxlength: 500,
      showCount: true,
      rows: 4,
    },
    component: 'InputTextArea',
  },
  {
    field: 'fileInfoDTOList',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    render() {
      return h(TableRender, { ref: fileRef });
    },
  },
];

const [register, { setFieldsValue, validate }] = useForm({
  layout: 'vertical',
  schemas,
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
});

onMounted(() => {
  (props.detail?.id || props.detail?.operationType === 'fixed') && initForm();
  if (props?.detail?.projectId) {
    getBasePlanPage(props?.detail?.projectId);
  }
});

// 初始化表单
async function initForm() {
  await setFieldsValue(props.detail);
  selectProjectId.value = props.detail?.projectId;
  deptOptions.value = [
    {
      label: props.detail?.rspDeptName,
      value: props.detail?.rspDeptId,
    },
  ];
  selectUser.value = {
    id: props.detail?.resUserId,
    name: props.detail?.resUserName,
  };
  projectRecords.value = [
    {
      id: props.detail?.projectId,
      name: props.detail?.projectName,
    },
  ];
  projectSubTypeName.value = props.detail?.projectSubTypeName;
  projectTimes.value.startTime = props.detail?.projectStartTime;
  projectTimes.value.endTime = props.detail?.projectEndTime;
  await nextTick();
  fileRef.value.setData(props.detail?.materialList);
}

// 选择项目回调
function checkProjectCallback(project:Record<string, string>, dept:Array<Record<string, any>>, user:UserData) {
  selectProjectId.value = project.id;
  deptOptions.value = dept;
  selectUser.value = user;
  isSelectProject.value = true;
  dataSource.value = [];
  tableRef.value.setTableData([]);
  setFieldsValue({
    rspDeptId: dept[0]?.value ?? '',
    resUserName: user.name,
    projectName: project.name,
    projectSource: project?.projectSource,
    projectType: project?.projectType,
    projectNumber: project.number,
    projectStartTime: project.projectStartTime,
    projectEndTime: project.projectEndTime,
  });
}

// 选择人员回调
async function selectUserCallback(user:UserData) {
  const result: Record<string, any> = await new Api('/pmi/user/user-profile').fetch('', user[0].id, 'GET');
  deptOptions.value = [
    {
      label: result.orgName,
      value: result.orgId,
    },
  ];
  selectUser.value = {
    id: result.userId,
    name: result.name,
  };
  await setFieldsValue({
    resUserName: result.name,
    rspDeptId: result.orgId,
  });
}
const dataSource = ref([]);
const columns = [
  {
    title: '编号',
    dataIndex: 'number',
    width: 100,
  },
  {
    title: '名称',
    dataIndex: 'name',
  },

  {
    title: '状态',
    dataIndex: 'dataStatus',
    width: 100,
    customRender({ text }) {
      return text ? h(DataStatusTag, {
        statusData: text,
      }) : '';
    },
  },
  {
    title: '责任人',
    width: 100,
    dataIndex: 'rspUserName',
  },
  {
    title: '来源类型',
    width: 100,
    dataIndex: 'type',
  },
];
const tableOptions = {
  rowSelection: {},
  columns,
  dataSource,
  pagination: false,
  showTableSetting: false,
  showToolButton: false,
  showSmallSearch: false,
};

function onSelectionChange({ rows }) {
  selectRows.value = rows;
}

async function getBasePlanPage(id:string) {
  const result = await new Api('/pms/new-project-to-base-plan/page').fetch({
    pageSize: 999,
    pageNum: 1,
    query: {
      projectId: id,
    },
  }, '', 'POST');
  dataSource.value = result.content.map((item:Record<string, any>) => ({
    ...item,
    name: item.sourceName,
    rspUserName: item.resUserName,
    type: item.sourceTypeName,
  }));
  tableRef.value.setTableData(dataSource);
}

function removeData() {
  Modal.confirm({
    title: '移除确认提示',
    content: '请确认是否移除该关联计划信息？',
    onOk(closeFn) {
      dataSource.value = dataSource.value.filter((item) => selectRows.value.every((row) => row.id !== item.id));
      tableRef.value.clearSelectedRowKeys();
      closeFn();
    },
    onCancel() {
      Modal.destroyAll();
    },
  });
}
const openSource = () => {
  openSourceModal(true, {});
};
function pushRows(rows:any[]) {
  const map = new Map();
  dataSource.value = dataSource.value.concat(rows)
    .filter((row) => !map.has(row.id) && map.set(row.id, 1));
}

defineExpose({
  validate,
  selectUser,
  selectProjectId,
  projectRecords,
  projectTimes,
  getTableData: () => fileRef.value.getData(),
  getTablesData: () => dataSource.value,
});

</script>

<template>
  <div class="formContent_content">
    <!--      <Wrap-->
    <!--        title="项目来源"-->
    <!--      >-->
    <!--        <OrionTable-->
    <!--          ref="tableRef"-->
    <!--          :max-height="200"-->
    <!--          :options="tableOptions"-->
    <!--          @selection-change="onSelectionChange"-->
    <!--        >-->
    <!--          <template #toolbarLeft>-->
    <!--            <BasicButton-->
    <!--              type="primary"-->
    <!--              icon="sie-icon-tianjiaxinzeng"-->
    <!--              ghost-->
    <!--              :disabled="isSelectProject"-->
    <!--              @click="openSource()"-->
    <!--            >-->
    <!--              添加来源-->
    <!--            </BasicButton>-->
    <!--            <BasicButton-->
    <!--              icon="sie-icon-del"-->
    <!--              :disabled="disabledBtn&&isSelectProject"-->
    <!--              @click="removeData"-->
    <!--            >-->
    <!--              移除-->
    <!--            </BasicButton>-->
    <!--          </template>-->
    <!--        </OrionTable>-->
    <!--      </Wrap>-->

    <BasicForm
      @register="register"
    />
  </div>

  <!--  <CheckProjectModal-->
  <!--    :onCheckProjectCallback="checkProjectCallback"-->
  <!--    @register="registerCheckProject"-->
  <!--  />-->
  <!--  <SelectUserModal-->
  <!--    selectType="radio"-->
  <!--    @ok="selectUserCallback"-->
  <!--    @register="registerSelectUser"-->
  <!--  />-->
  <!--  选择综合计划-->
  <!--  <SelectProPlanIndex-->
  <!--    :onPushRows="pushRows"-->
  <!--    @register="registerModelPlan"-->
  <!--  />-->

  <SourceModal
    :onPushRows="pushRows"
    @register="registerSourceModal"
  />
</template>

<style scoped lang="less">
.formContent{
  display: flex;
  height: 100%;
  flex-direction: column;
  .formContent_content{
    padding: 0 24px;
    flex: 1 1 auto;
  }
  .moreMessage{
    color: #5976d6;
    cursor: pointer;
  }
  .actions{
    span{
      color: #5172DC;
      padding:0 10px;
      cursor: pointer;
    }
    .actions1{
      border-right: 1px solid #5172DC;
    }
  }
  .addDocumentFooter{
    padding: 15px;
    border-top: 1px solid #e9ecf2;
    width: 100%;
    display: flex;
    justify-content: space-between;
    .addModalFooterNext{
      line-height: 40px !important;
    }
    .btnStyle{
      flex: 1;
      text-align: right;
      .ant-btn{
        margin-left:10px;
        border-radius: 4px;
        padding: 4px 30px;
      }
      .canncel{
        background: #5172dc19;
        color: #5172DC;
      }
      .confirm{
        background: #5172dc;
        color: #ffffff;
      }
    }
  }
}
.ant-form-item{
  display: block;
}
.ant-form-item-control{
  width: 100% !important;
}
</style>
