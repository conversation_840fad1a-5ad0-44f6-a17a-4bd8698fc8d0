package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;

/**
 * PersonTrainEquRecord Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-22 16:20:55
 */
@TableName(value = "pmsx_person_train_equ_record")
@ApiModel(value = "PersonTrainEquRecordEntity对象", description = "人员培训等效信息记录")
@Data

public class PersonTrainEquRecord extends  ObjectEntity  implements Serializable{

    /**
     * 等效基地编号
     */
    @ApiModelProperty(value = "等效基地编号")
    @TableField(value = "equivalent_base_code")
    private String equivalentBaseCode;

    /**
     * 等效基地名称
     */
    @ApiModelProperty(value = "等效基地名称")
    @TableField(value = "equivalent_base_name")
    private String equivalentBaseName;

    /**
     * 等效认定时间
     */
    @ApiModelProperty(value = "等效认定时间")
    @TableField(value = "equivalent_date")
    private Date equivalentDate;

    /**
     * 人员编号
     */
    @ApiModelProperty(value = "人员编号")
    @TableField(value = "user_code")
    private String userCode;

    /**
     * 培训编号
     */
    @ApiModelProperty(value = "培训编号")
    @TableField(value = "train_number")
    private String trainNumber;

    @ApiModelProperty(value = "培训ID - 培训ID")
    @TableField(value = "source_id")
    private String sourceId;
    @ApiModelProperty(value = "培训基地编号")
    @TableField(value = "train_base_code")
    private String trainBaseCode;

    @ApiModelProperty(value = "被等效单key: 映射为 train_key")
    @TableField(value = "form_train_number")
    private String formTrainNumber;
}
