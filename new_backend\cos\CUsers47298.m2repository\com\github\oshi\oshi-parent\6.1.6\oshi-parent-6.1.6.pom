<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.github.oshi</groupId>
    <artifactId>oshi-parent</artifactId>
    <version>6.1.6</version>
    <packaging>pom</packaging>

    <name>Operating System and Hardware Information</name>
    <description>A JNA-based (native) operating system information library for Java that aims to provide a
        cross-platform implementation to retrieve system information, such as version, memory, CPU, disk, battery, etc.</description>
    <url>https://github.com/oshi/oshi</url>
    <inceptionYear>2010</inceptionYear>
    <organization>
        <name>oshi</name>
        <url>https://github.com/oshi/</url>
    </organization>
    <licenses>
        <license>
            <name>SPDX-License-Identifier: MIT</name>
            <url>https://opensource.org/licenses/MIT</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <developers>
        <developer>
            <id>dblock</id>
            <name>Daniel Doubrovkine</name>
            <email><EMAIL></email>
            <organization>dblock.org</organization>
            <organizationUrl>http://code.dblock.org/</organizationUrl>
        </developer>
        <developer>
            <id>dbwiddis</id>
            <name>Daniel Widdis</name>
            <email><EMAIL></email>
            <organization>sometegroup.com</organization>
            <organizationUrl>https://github.com/dbwiddis/</organizationUrl>
        </developer>
    </developers>

    <modules>
        <module>oshi-core</module>
        <module>oshi-demo</module>
    </modules>

    <scm>
        <connection>scm:git:ssh://**************/oshi/oshi.git</connection>
        <developerConnection>scm:git:ssh://**************/oshi/oshi.git</developerConnection>
        <tag>oshi-parent-6.1.6</tag>
        <url>https://github.com/oshi/oshi.git</url>
    </scm>
    <issueManagement>
        <system>Github</system>
        <url>https://github.com/oshi/oshi/issues</url>
    </issueManagement>
    <ciManagement>
        <system>Github</system>
        <url>https://github.com/oshi/oshi/actions</url>
    </ciManagement>
    <distributionManagement>
        <repository>
            <id>ossrh</id>
            <name>Nexus Release Repository</name>
            <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
        </repository>
        <snapshotRepository>
            <id>ossrh-snapshot</id>
            <name>Sonatype Nexus Snapshots</name>
            <url>https://oss.sonatype.org/content/repositories/snapshots</url>
        </snapshotRepository>
        <site>
            <id>gh-pages</id>
            <name>OSHI GitHub Pages</name>
            <url>scm:git:ssh://**************/oshi/oshi.git</url>
        </site>
    </distributionManagement>

    <properties>
        <copyright>2010 - 2022</copyright>
        <maven.build.timestamp.format>yyyy-MM-dd HH:mm:ss</maven.build.timestamp.format>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <maven.compiler.testSource>8</maven.compiler.testSource>
        <maven.compiler.testTarget>8</maven.compiler.testTarget>
        <maven.min-version>3.3.9</maven.min-version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.build.resourceEncoding>UTF-8</project.build.resourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <!-- internal, see https://issues.apache.org/jira/browse/MNG-7038 -->
        <main.basedir>${maven.multiModuleProjectDirectory}</main.basedir>
        <!-- Dependency versions -->
        <!-- Users of the Spring Boot Starter Parent should include this property in their POM -->
        <jna.version>5.11.0</jna.version>
        <slf4j.version>1.7.36</slf4j.version>
        <junit.version>5.8.2</junit.version>
        <hamcrest.version>2.2</hamcrest.version>
        <!-- Compile versions -->
        <maven-clean-plugin.version>3.2.0</maven-clean-plugin.version>
        <maven-compiler-plugin.version>3.10.1</maven-compiler-plugin.version>
        <maven-deploy-plugin.version>3.0.0-M2</maven-deploy-plugin.version>
        <nexus-staging-plugin.version>1.6.12</nexus-staging-plugin.version>
        <maven-enforcer-plugin.version>3.0.0</maven-enforcer-plugin.version>
        <restrict-imports-enforcer-rule.version>2.0.0</restrict-imports-enforcer-rule.version>
        <maven-gpg-plugin.version>3.0.1</maven-gpg-plugin.version>
        <maven-install-plugin.version>3.0.0-M1</maven-install-plugin.version>
        <maven-resources-plugin.version>3.2.0</maven-resources-plugin.version>
        <maven-scm-publish-plugin.version>3.1.0</maven-scm-publish-plugin.version>
        <maven-site-plugin.version>3.11.0</maven-site-plugin.version>
        <maven-surefire-plugin.version>3.0.0-M6</maven-surefire-plugin.version>
        <maven-fluido-skin.version>1.10.0</maven-fluido-skin.version>
        <!-- build plugins -->
        <git-commit-id-plugin.version>4.9.10</git-commit-id-plugin.version>
        <build-helper-maven-plugin.version>3.3.0</build-helper-maven-plugin.version>
        <maven-jar-plugin.version>3.2.2</maven-jar-plugin.version>
        <bnd-maven-plugin.version>6.2.0</bnd-maven-plugin.version>
        <maven-shade-plugin.version>3.2.4</maven-shade-plugin.version>
        <junit-platform-maven-plugin.version>1.1.6</junit-platform-maven-plugin.version>
        <!-- reporting plugins -->
        <maven-checkstyle-plugin.version>3.1.2</maven-checkstyle-plugin.version>
        <checkstyle.config.location>config/checkstyle.xml</checkstyle.config.location>
        <checkstyle.suppressions.location>config/checkstyle-suppressions.xml</checkstyle.suppressions.location>
        <maven-javadoc-plugin.version>3.3.2</maven-javadoc-plugin.version>
        <!-- tools -->
        <maven-antrun-plugin.version>3.0.0</maven-antrun-plugin.version>
        <maven-assembly-plugin.version>3.3.0</maven-assembly-plugin.version>
        <maven-dependency-plugin.version>3.3.0</maven-dependency-plugin.version>
        <maven-release-plugin.version>3.0.0-M5</maven-release-plugin.version>
        <maven-source-plugin.version>3.2.1</maven-source-plugin.version>
        <properties-maven-plugin.version>1.1.0</properties-maven-plugin.version>
        <jacoco-maven-plugin.version>0.8.8</jacoco-maven-plugin.version>
        <coveralls-maven-plugin.version>4.3.0</coveralls-maven-plugin.version>
        <bind-api.version>2.3.3</bind-api.version>
        <license-maven-plugin.version>4.2.rc3</license-maven-plugin.version>
        <sortpom-plugin.version>3.0.1</sortpom-plugin.version>
        <spotless-plugin.version>2.22.1</spotless-plugin.version>
        <!-- report only -->
        <maven-changelog-plugin.version>2.3</maven-changelog-plugin.version>
        <maven-jxr-plugin.version>3.2.0</maven-jxr-plugin.version>
        <maven-project-info-reports-plugin.version>3.2.2</maven-project-info-reports-plugin.version>
        <maven-surefire-report-plugin.version>3.0.0-M6</maven-surefire-report-plugin.version>
        <taglist-maven-plugin.version>3.0.0</taglist-maven-plugin.version>
        <versions-maven-plugin.version>2.10.0</versions-maven-plugin.version>
        <!-- Misc. -->
        <sonar-maven-plugin.version>3.9.1.2184</sonar-maven-plugin.version>
        <animal-sniffer-maven-plugin.version>1.21</animal-sniffer-maven-plugin.version>
        <dependency-check-maven.version>7.0.4</dependency-check-maven.version>
        <puppycrawl.checkstyle.version>10.1</puppycrawl.checkstyle.version>
        <m2e.lifecycle-mapping.version>1.0.0</m2e.lifecycle-mapping.version>
    </properties>

    <build>
        <pluginManagement>
            <plugins>
                <!-- Core plugins -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>${maven-clean-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <!-- Slightly faster builds, see https://issues.apache.org/jira/browse/MCOMPILER-209 -->
                        <useIncrementalCompilation>false</useIncrementalCompilation>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>${maven-deploy-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.sonatype.plugins</groupId>
                    <artifactId>nexus-staging-maven-plugin</artifactId>
                    <version>${nexus-staging-plugin.version}</version>
                    <extensions>true</extensions>
                    <configuration>
                        <serverId>sonatype-nexus-staging</serverId>
                        <nexusUrl>https://oss.sonatype.org/</nexusUrl>
                        <autoReleaseAfterClose>true</autoReleaseAfterClose>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>${maven-enforcer-plugin.version}</version>
                    <dependencies>
                        <dependency>
                            <groupId>de.skuzzle.enforcer</groupId>
                            <artifactId>restrict-imports-enforcer-rule</artifactId>
                            <version>${restrict-imports-enforcer-rule.version}</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-gpg-plugin</artifactId>
                    <version>${maven-gpg-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-install-plugin</artifactId>
                    <version>${maven-install-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>${maven-resources-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-site-plugin</artifactId>
                    <version>${maven-site-plugin.version}</version>
                    <configuration>
                        <!-- don't deploy site with maven-site-plugin (instead use scm publish during release) -->
                        <skipDeploy>true</skipDeploy>
                    </configuration>
                    <dependencies>
                        <dependency>
                            <groupId>org.apache.maven.skins</groupId>
                            <artifactId>maven-fluido-skin</artifactId>
                            <version>${maven-fluido-skin.version}</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-scm-publish-plugin</artifactId>
                    <version>${maven-scm-publish-plugin.version}</version>
                    <configuration>
                        <checkoutDirectory>${user.home}/maven-sites/oshi</checkoutDirectory>
                        <scmBranch>gh-pages</scmBranch>
                        <skipDeletedFiles>true</skipDeletedFiles>
                        <tryUpdate>true</tryUpdate>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                    <configuration>
                        <argLine>-Xmx1024m -XX:MaxPermSize=256m</argLine>
                        <redirectTestOutputToFile>true</redirectTestOutputToFile>
                        <forkCount>4</forkCount>
                        <reuseForks>true</reuseForks>
                        <parallel>all</parallel>
                        <useUnlimitedThreads>true</useUnlimitedThreads>
                        <forkedProcessExitTimeoutInSeconds>120</forkedProcessExitTimeoutInSeconds>
                    </configuration>
                </plugin>
                <!-- Build Plugins -->
                <plugin>
                    <groupId>pl.project13.maven</groupId>
                    <artifactId>git-commit-id-plugin</artifactId>
                    <version>${git-commit-id-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>${build-helper-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>de.sormuras.junit</groupId>
                    <artifactId>junit-platform-maven-plugin</artifactId>
                    <version>${junit-platform-maven-plugin.version}</version>
                    <extensions>true</extensions>
                    <!-- Replace surefire in 'test' phase. -->
                    <configuration>
                        <timeout>3600</timeout>
                        <isolation>NONE</isolation>
                        <parameters>
                            <junit.jupiter.execution.parallel.enabled>true</junit.jupiter.execution.parallel.enabled>
                            <junit.jupiter.execution.parallel.mode.default>concurrent</junit.jupiter.execution.parallel.mode.default>
                        </parameters>
                        <executor>JAVA</executor>
                        <javaOptions>
                            <additionalOptions>
                                <jacoco>${jacoco.java.option}</jacoco>
                            </additionalOptions>
                        </javaOptions>
                    </configuration>
                </plugin>
                <!-- Packaging types / tools -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>${maven-jar-plugin.version}</version>
                    <configuration>
                        <archive>
                            <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                            <manifest>
                                <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                                <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
                            </manifest>
                            <manifestEntries combine.children="append">
                                <Build-Time>${maven.build.timestamp}</Build-Time>
                                <Copyright>${copyright}</Copyright>
                                <Git-Revision>${git.commit.id}</Git-Revision>
                                <Os-Name>${os.name}</Os-Name>
                                <Os-Arch>${os.arch}</Os-Arch>
                                <Os-Version>${os.version}</Os-Version>
                                <X-Compile-Source-JDK>${maven.compiler.source}</X-Compile-Source-JDK>
                                <X-Compile-Target-JDK>${maven.compiler.target}</X-Compile-Target-JDK>
                            </manifestEntries>
                        </archive>
                    </configuration>
                </plugin>
                <!-- OSGi -->
                <plugin>
                    <groupId>biz.aQute.bnd</groupId>
                    <artifactId>bnd-maven-plugin</artifactId>
                    <version>${bnd-maven-plugin.version}</version>
                    <configuration>
                        <bnd><![CDATA[Export-Package: oshi.*;-noimport:=true;-split-package:=merge-first
                        Bundle-SymbolicName: ${project.groupId}.${project.artifactId}
                        -snapshot: SNAPSHOT]]></bnd>
                    </configuration>
                </plugin>
                <!-- Reporting plugins -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-checkstyle-plugin</artifactId>
                    <version>${maven-checkstyle-plugin.version}</version>
                    <configuration>
                        <consoleOutput>true</consoleOutput>
                        <failOnViolation>true</failOnViolation>
                        <includeTestSourceDirectory>true</includeTestSourceDirectory>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>${maven-javadoc-plugin.version}</version>
                    <configuration>
                        <archive>
                            <manifest>
                                <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                                <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
                            </manifest>
                            <manifestEntries>
                                <Build-Time>${maven.build.timestamp}</Build-Time>
                                <Copyright>${copyright}</Copyright>
                                <Git-Revision>${git.commit.id}</Git-Revision>
                                <Os-Name>${os.name}</Os-Name>
                                <Os-Arch>${os.arch}</Os-Arch>
                                <Os-Version>${os.version}</Os-Version>
                                <X-Compile-Source-JDK>${maven.compiler.source}</X-Compile-Source-JDK>
                                <X-Compile-Target-JDK>${maven.compiler.target}</X-Compile-Target-JDK>
                            </manifestEntries>
                        </archive>
                        <source>8</source>
                        <sourcepath>src/main/java</sourcepath>
                        <detectLinks>true</detectLinks>
                        <detectJavaApiLink>false</detectJavaApiLink>
                    </configuration>
                </plugin>
                <!-- Tools -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <version>${maven-antrun-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>${maven-assembly-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>${maven-dependency-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-release-plugin</artifactId>
                    <version>${maven-release-plugin.version}</version>
                    <configuration>
                        <mavenExecutorId>forked-path</mavenExecutorId>
                        <releaseProfiles>release</releaseProfiles>
                        <autoVersionSubmodules>true</autoVersionSubmodules>
                        <!-- override default site-deploy goal -->
                        <goals>deploy</goals>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>${maven-source-plugin.version}</version>
                    <configuration>
                        <archive>
                            <manifest>
                                <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                                <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
                            </manifest>
                            <manifestEntries>
                                <Build-Time>${maven.build.timestamp}</Build-Time>
                                <Copyright>${copyright}</Copyright>
                                <Git-Revision>${git.commit.id}</Git-Revision>
                                <Os-Name>${os.name}</Os-Name>
                                <Os-Arch>${os.arch}</Os-Arch>
                                <Os-Version>${os.version}</Os-Version>
                                <X-Compile-Source-JDK>${maven.compiler.source}</X-Compile-Source-JDK>
                                <X-Compile-Target-JDK>${maven.compiler.target}</X-Compile-Target-JDK>

                                <!-- OSGi source header -->
                                <Bundle-ManifestVersion>2</Bundle-ManifestVersion>
                                <Bundle-Name>${project.name}</Bundle-Name>
                                <Bundle-SymbolicName>${project.groupId}.${project.artifactId}.source</Bundle-SymbolicName>
                                <Bundle-Vendor>${project.organization.name}</Bundle-Vendor>
                                <Bundle-Version>${parsedVersion.osgiVersion}</Bundle-Version>
                                <Eclipse-SourceBundle>${project.groupId}.${project.artifactId};version="${parsedVersion.osgiVersion}";roots:="."</Eclipse-SourceBundle>
                            </manifestEntries>
                        </archive>
                    </configuration>
                    <executions>
                        <!-- Here we override the super-pom attach-sources execution id which calls sources:jar goal. That
                            goals forks the lifecycle, causing the generate-sources phase to be called twice for the install goal. Starting with Maven
                            3.4.0 (https://issues.apache.org/jira/browse/MNG-5940) this is not needed anymore. -->
                        <!-- except that OSSRH fails with no sources with this excluded <execution> <id>attach-sources</id>
                            <phase>DISABLE_FORKED_LIFECYCLE_MSOURCES-13</phase> </execution> -->
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>properties-maven-plugin</artifactId>
                    <version>${properties-maven-plugin.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>read-project-properties</goal>
                            </goals>
                            <phase>initialize</phase>
                            <configuration>
                                <files>
                                    <file>${main.basedir}/config/sonar-project.properties</file>
                                </files>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <!-- External Tools -->
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>${jacoco-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.eluder.coveralls</groupId>
                    <artifactId>coveralls-maven-plugin</artifactId>
                    <version>${coveralls-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>com.mycila</groupId>
                    <artifactId>license-maven-plugin</artifactId>
                    <version>${license-maven-plugin.version}</version>
                    <configuration>
                        <licenseSets>
                            <licenseSet>
                                <header>${main.basedir}/config/MIT.txt</header>
                                <excludes>
                                    <exclude>**/MavenWrapperDownloader.java</exclude>
                                    <exclude>mvnw</exclude>
                                    <exclude>mvnw.cmd</exclude>
                                    <exclude>mvnw11</exclude>
                                    <exclude>.editorconfig</exclude>
                                    <exclude>.gitattributes</exclude>
                                    <exclude>**/module-info.*</exclude>
                                    <exclude>**/*.PNG</exclude>
                                    <exclude>*.xml</exclude>
                                    <exclude>*.yml</exclude>
                                    <exclude>**/*.properties</exclude>
                                    <exclude>**/*.txt</exclude>
                                    <exclude>**/*.xml</exclude>
                                    <exclude>**/*.yml</exclude>
                                    <exclude>**/*.yaml</exclude>
                                    <exclude>**/cov-analysis*/**</exclude>
                                </excludes>
                            </licenseSet>
                        </licenseSets>
                    </configuration>
                    <dependencies>
                        <dependency>
                            <groupId>com.mycila</groupId>
                            <artifactId>license-maven-plugin-git</artifactId>
                            <version>${license-maven-plugin.version}</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>com.diffplug.spotless</groupId>
                    <artifactId>spotless-maven-plugin</artifactId>
                    <version>${spotless-plugin.version}</version>
                    <configuration>
                        <formats>
                            <format>
                                <includes>
                                    <include>**/*.md</include>
                                    <include>**/*.yml</include>
                                    <include>**/*.yaml</include>
                                </includes>
                                <trimTrailingWhitespace />
                                <endWithNewline />
                                <indent>
                                    <spaces>true</spaces>
                                    <spacesPerTab>2</spacesPerTab>
                                </indent>
                            </format>
                            <format>
                                <includes>
                                    <include>**/*.xml</include>
                                </includes>
                                <trimTrailingWhitespace />
                                <endWithNewline />
                                <indent>
                                    <spaces>true</spaces>
                                    <spacesPerTab>4</spacesPerTab>
                                </indent>
                            </format>
                        </formats>
                        <java>
                            <eclipse>
                                <file>${main.basedir}/config/OSHIJavaFormatConventions.xml</file>
                            </eclipse>
                        </java>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>check</goal>
                            </goals>
                            <phase>compile</phase>
                        </execution>
                    </executions>
                </plugin>
                <!-- Report Only -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-changelog-plugin</artifactId>
                    <version>${maven-changelog-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jxr-plugin</artifactId>
                    <version>${maven-jxr-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-project-info-reports-plugin</artifactId>
                    <version>${maven-project-info-reports-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-report-plugin</artifactId>
                    <version>${maven-surefire-report-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>taglist-maven-plugin</artifactId>
                    <version>${taglist-maven-plugin.version}</version>
                    <configuration>
                        <tagListOptions>
                            <tagClasses>
                                <tagClass>
                                    <displayName>FIXME Work</displayName>
                                    <tags>
                                        <tag>
                                            <matchString>fixme</matchString>
                                            <matchType>ignoreCase</matchType>
                                        </tag>
                                        <tag>
                                            <matchString>@fixme</matchString>
                                            <matchType>ignoreCase</matchType>
                                        </tag>
                                    </tags>
                                </tagClass>
                                <tagClass>
                                    <displayName>Todo Work</displayName>
                                    <tags>
                                        <tag>
                                            <matchString>todo</matchString>
                                            <matchType>ignoreCase</matchType>
                                        </tag>
                                        <tag>
                                            <matchString>@todo</matchString>
                                            <matchType>ignoreCase</matchType>
                                        </tag>
                                    </tags>
                                </tagClass>
                                <tagClass>
                                    <displayName>Deprecated Work</displayName>
                                    <tags>
                                        <tag>
                                            <matchString>@deprecated</matchString>
                                            <matchType>ignoreCase</matchType>
                                        </tag>
                                    </tags>
                                </tagClass>
                            </tagClasses>
                        </tagListOptions>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>versions-maven-plugin</artifactId>
                    <version>${versions-maven-plugin.version}</version>
                </plugin>
                <!-- Sonar -->
                <plugin>
                    <groupId>org.sonarsource.scanner.maven</groupId>
                    <artifactId>sonar-maven-plugin</artifactId>
                    <version>${sonar-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.owasp</groupId>
                    <artifactId>dependency-check-maven</artifactId>
                    <version>${dependency-check-maven.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <!-- Build Plugins -->
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
                <configuration>
                    <dotGitDirectory>.git</dotGitDirectory>
                </configuration>
                <executions>
                    <execution>
                        <id>git-commit-id</id>
                        <goals>
                            <goal>revision</goal>
                        </goals>
                        <phase>validate</phase>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>parse-version</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>biz.aQute.bnd</groupId>
                <artifactId>bnd-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>bnd-process</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>de.sormuras.junit</groupId>
                <artifactId>junit-platform-maven-plugin</artifactId>
            </plugin>
            <!-- Reporting Plugins -->
            <!-- Tools -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <configuration>
                    <rules>
                        <requireMavenVersion>
                            <version>${maven.min-version}</version>
                        </requireMavenVersion>
                        <bannedDependencies>
                            <excludes>
                                <exclude>junit:junit</exclude>
                                <exclude>org.hamcrest:hamcrest-core</exclude>
                            </excludes>
                        </bannedDependencies>
                    </rules>
                </configuration>
                <executions>
                    <execution>
                        <id>enforce-maven</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>enforce-clean</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <phase>pre-clean</phase>
                    </execution>
                    <execution>
                        <id>enforce-site</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <phase>pre-site</phase>
                    </execution>
                    <execution>
                        <id>ban-transitive-imports</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <phase>process-sources</phase>
                        <configuration>
                            <rules>
                                <RestrictImports>
                                    <reason>Disallow dependencies not on
                                        module path</reason>
                                    <includeTestCode>true</includeTestCode>
                                    <bannedImports>
                                        <!-- Disallow all imports except explicitly allowed -->
                                        <bannedImport>**</bannedImport>
                                    </bannedImports>
                                    <allowedImports>
                                        <!-- Allow oshi itself :-) -->
                                        <allowedImport>oshi.**</allowedImport>
                                        <!-- Allow core Java usage -->
                                        <allowedImport>java.**</allowedImport>
                                        <!-- Allow known dependencies -->
                                        <allowedImport>com.sun.jna.**</allowedImport>
                                        <allowedImport>org.slf4j.**</allowedImport>
                                        <allowedImport>org.junit.jupiter.api.**</allowedImport>
                                        <allowedImport>static org.hamcrest.**</allowedImport>
                                    </allowedImports>
                                    <!-- No restrictions on oshi-demo -->
                                    <exclusion>oshi.demo.**</exclusion>
                                </RestrictImports>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.github.ekryd.sortpom</groupId>
                <artifactId>sortpom-maven-plugin</artifactId>
                <version>${sortpom-plugin.version}</version>
                <configuration>
                    <createBackupFile>false</createBackupFile>
                    <lineSeparator>\n</lineSeparator>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <sortDependencies>scope</sortDependencies>
                    <nrOfIndentSpace>4</nrOfIndentSpace>
                    <expandEmptyElements>false</expandEmptyElements>
                    <spaceBeforeCloseEmptyElement>true</spaceBeforeCloseEmptyElement>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>sort</goal>
                        </goals>
                        <phase>verify</phase>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <!-- Code Coverage -->
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>**/driver/**</exclude>
                        <exclude>**/common/**</exclude>
                        <exclude>**/Abstract*</exclude>
                        <exclude>**/platform/**</exclude>
                        <exclude>**/linux/**</exclude>
                        <exclude>**/mac/**</exclude>
                        <exclude>**/windows/**</exclude>
                        <exclude>**/unix/**</exclude>
                        <exclude>**/demo/**</exclude>
                    </excludes>
                </configuration>
                <executions>
                    <!-- Prepare execution with Surefire -->
                    <execution>
                        <id>pre-unit-test</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                        <configuration>
                            <propertyName>jacoco.java.option</propertyName>
                        </configuration>
                    </execution>
                    <!-- Generate report after tests are run -->
                    <execution>
                        <id>post-unit-test</id>
                        <goals>
                            <goal>report</goal>
                        </goals>
                        <phase>test</phase>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <argLine>${surefireArgLine}</argLine>
                    <includes>
                        <include>**/*Test.java</include>
                        <include>**/*Tests.java</include>
                    </includes>
                </configuration>
                <executions>
                    <execution>
                        <id>run-unit-tests</id>
                        <goals>
                            <goal>test</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.eluder.coveralls</groupId>
                <artifactId>coveralls-maven-plugin</artifactId>
                <dependencies>
                    <dependency>
                        <groupId>jakarta.xml.bind</groupId>
                        <artifactId>jakarta.xml.bind-api</artifactId>
                        <version>${bind-api.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <dependencies>
                    <dependency>
                        <groupId>com.puppycrawl.tools</groupId>
                        <artifactId>checkstyle</artifactId>
                        <version>${puppycrawl.checkstyle.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>

    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-changelog-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jxr-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-project-info-reports-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-report-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>taglist-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.owasp</groupId>
                <artifactId>dependency-check-maven</artifactId>
                <reportSets>
                    <reportSet>
                        <reports>
                            <report>aggregate</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
        </plugins>
    </reporting>

    <profiles>
        <profile>
            <id>jdk8</id>
            <activation>
                <jdk>1.8</jdk>
            </activation>
            <build>
                <plugins>
                    <!-- Restrict the API to be compatible with Java 8 -->
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>animal-sniffer-maven-plugin</artifactId>
                        <version>${animal-sniffer-maven-plugin.version}</version>
                        <configuration>
                            <signature>
                                <groupId>org.codehaus.mojo.signature</groupId>
                                <artifactId>java18</artifactId>
                                <version>1.0</version>
                            </signature>
                        </configuration>
                        <executions>
                            <execution>
                                <id>test-sniffer</id>
                                <goals>
                                    <goal>check</goal>
                                </goals>
                                <phase>test</phase>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>java11</id>
            <activation>
                <jdk>[11,)</jdk>
            </activation>
            <!-- Only include java11 and dist modules on Java 11+ -->
            <modules>
                <module>oshi-core-java11</module>
                <module>oshi-dist</module>
            </modules>
            <!-- Restrict the API to be compatible with Java 8 -->
            <properties>
                <maven.compiler.release>${maven.compiler.target}</maven.compiler.release>
                <maven.compiler.testRelease>${maven.compiler.testTarget}</maven.compiler.testRelease>
            </properties>
        </profile>
        <profile>
            <id>checks</id>
            <build>
                <plugins>
                    <!-- Reporting Plugins -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-checkstyle-plugin</artifactId>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>check</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <!-- External Tools -->
                    <plugin>
                        <groupId>org.sonarsource.scanner.maven</groupId>
                        <artifactId>sonar-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>sonar</goal>
                                </goals>
                                <phase>verify</phase>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.jacoco</groupId>
                        <artifactId>jacoco-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>check</id>
                                <goals>
                                    <goal>check</goal>
                                </goals>
                                <configuration>
                                    <rules>
                                        <rule>
                                            <element>BUNDLE</element>
                                            <limits>
                                                <limit>
                                                    <counter>COMPLEXITY</counter>
                                                    <value>COVEREDRATIO</value>
                                                    <minimum>${jacoco.minimum.coverage}</minimum>
                                                </limit>
                                            </limits>
                                        </rule>
                                    </rules>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.owasp</groupId>
                        <artifactId>dependency-check-maven</artifactId>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>check</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>sonar</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.jacoco</groupId>
                        <artifactId>jacoco-maven-plugin</artifactId>
                    </plugin>
                    <plugin>
                        <groupId>org.sonarsource.scanner.maven</groupId>
                        <artifactId>sonar-maven-plugin</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>eclipse</id>
            <activation>
                <property>
                    <name>m2e.version</name>
                </property>
            </activation>
            <build>
                <pluginManagement>
                    <plugins>
                        <!--This plugin's configuration is used to store Eclipse m2e settings only. It has no influence on
                            the Maven build itself. -->
                        <plugin>
                            <groupId>org.eclipse.m2e</groupId>
                            <artifactId>lifecycle-mapping</artifactId>
                            <version>${m2e.lifecycle-mapping.version}</version>
                            <configuration>
                                <lifecycleMappingMetadata>
                                    <pluginExecutions>
                                        <pluginExecution>
                                            <pluginExecutionFilter>
                                                <groupId>com.mycila</groupId>
                                                <artifactId>license-maven-plugin</artifactId>
                                                <versionRange>[3.0,)</versionRange>
                                                <goals>
                                                    <goal>format</goal>
                                                </goals>
                                            </pluginExecutionFilter>
                                            <action>
                                                <ignore />
                                            </action>
                                        </pluginExecution>
                                        <pluginExecution>
                                            <pluginExecutionFilter>
                                                <groupId>org.jacoco</groupId>
                                                <artifactId>jacoco-maven-plugin</artifactId>
                                                <versionRange>[0.8.1,)</versionRange>
                                                <goals>
                                                    <goal>prepare-agent</goal>
                                                </goals>
                                            </pluginExecutionFilter>
                                            <action>
                                                <ignore />
                                            </action>
                                        </pluginExecution>
                                        <pluginExecution>
                                            <pluginExecutionFilter>
                                                <groupId>org.apache.maven.plugins</groupId>
                                                <artifactId>maven-enforcer-plugin</artifactId>
                                                <versionRange>[3.0.0-M2,)</versionRange>
                                                <goals>
                                                    <goal>enforce</goal>
                                                </goals>
                                            </pluginExecutionFilter>
                                            <action>
                                                <ignore />
                                            </action>
                                        </pluginExecution>
                                    </pluginExecutions>
                                </lifecycleMappingMetadata>
                            </configuration>
                        </plugin>
                    </plugins>
                </pluginManagement>
            </build>
        </profile>
        <profile>
            <id>release</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-source-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>attach-sources</id>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>attach-javadocs</id>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                            <execution>
                                <id>attach-test-javadocs</id>
                                <goals>
                                    <goal>test-jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                                <phase>verify</phase>
                                <configuration>
                                    <keyname>${gpg.keyname}</keyname>
                                    <passphraseServerId>${gpg.keyname}</passphraseServerId>
                                    <!-- GPG 2.1 requires pinentry-mode to be set to loopback in order to pick up the gpg.passphrase
                                        value defined in Maven settings.xml. -->
                                    <gpgArguments>
                                        <arg>--pinentry-mode</arg>
                                        <arg>loopback</arg>
                                    </gpgArguments>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <!-- Skip license plugin during release as maven release is now shallow cloned -->
                    <plugin>
                        <groupId>com.mycila</groupId>
                        <artifactId>license-maven-plugin</artifactId>
                        <configuration>
                            <skip>true</skip>
                        </configuration>
                    </plugin>
                    <!-- deploy site with maven-scm-publish-plugin -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-scm-publish-plugin</artifactId>
                        <configuration>
                            <!-- no need for site:stage, use target/site or target/checkout/target/site on release -->
                            <content>${project.reporting.outputDirectory}</content>
                        </configuration>
                        <executions>
                            <execution>
                                <id>scm-publish</id>
                                <goals>
                                    <goal>publish-scm</goal>
                                </goals>
                                <phase>site-deploy</phase>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
