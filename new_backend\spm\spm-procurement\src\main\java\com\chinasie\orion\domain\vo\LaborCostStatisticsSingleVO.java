package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "LaborCostStatisticsSingleVO对象", description = "人力成本数据统计")
@Data
public class LaborCostStatisticsSingleVO {

    /**
     * 实际总金额
     */
    @ApiModelProperty(value = "实际总金额")
    private BigDecimal actualTotalAmount = new BigDecimal(0);

    /**
     * 工作量(人/月)
     */
    @ApiModelProperty(value = "工作量(人/月)")
    private BigDecimal workload= new BigDecimal(0);


    /**
     * 岗级成本
     */
    @ApiModelProperty(value = "岗级成本")
    private BigDecimal jobGradeAmt= new BigDecimal(0);


    /**
     * 住宿费
     */
    @ApiModelProperty(value = "住宿费")
    private BigDecimal hotelAmount= new BigDecimal(0);


    /**
     * 换乘费
     */
    @ApiModelProperty(value = "换乘费")
    private BigDecimal transferAmount= new BigDecimal(0);


    /**
     * 基地后勤费
     */
    @ApiModelProperty(value = "基地后勤费")
    private BigDecimal logisticsAmt= new BigDecimal(0);


    /**
     * RP体检费
     */
    @ApiModelProperty(value = "RP体检费")
    private BigDecimal medicalExaminationAmt= new BigDecimal(0);

    /**
     * 劳保用品费
     */
    @ApiModelProperty(value = "劳保用品费")
    private BigDecimal articlesAmt= new BigDecimal(0);

    /**
     * 餐厅管理费
     */
    @ApiModelProperty(value = "餐厅管理费")
    private BigDecimal restaurantAmt= new BigDecimal(0);

    /**
     * 其他费
     */
    @ApiModelProperty(value = "其他费")
    private BigDecimal otherAmt= new BigDecimal(0);
}
