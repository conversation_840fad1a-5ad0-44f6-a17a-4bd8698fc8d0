package com.chinasie.orion;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.CellExtra;
import com.alibaba.excel.read.listener.ReadListener;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.junit.jupiter.api.Test;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;


class DeliverableServiceImplStaticTest {
    Random rand = new Random();
    public static final Map<String, TaskExcel> parents = new HashMap<>();
    public static final Map<String, String> taskTypeMap = new HashMap<>();
    public static final Map<String, String> deptMap = new HashMap<>();
    public static final Map<String, String> projectDeptMap = new HashMap<>();

    public static final Map<String, String> userMap = new HashMap<>();
    public static final Map<String, String> projectUserMap = new HashMap<>();

    public static final Map<String, String> phaseMap = new HashMap<>() {{
        put("1", "K");
        put("2", "F");
        put("3", "C");
        put("4", "M");
        put("5", "J");
    }};

    static {
        for (int i = 1; i <= 4; i++) {
            taskTypeMap.put(i + "", "类型" + i);
        }

        for (int i = 1; i <= 40; i++) {
            deptMap.put(i + "", "部门" + i);
            if (i < 20) {
                projectDeptMap.put(i + "", "部门" + i);
            }
        }

        for (int i = 1; i <= 400; i++) {
            userMap.put(i + "", "用户" + i);
            if (i < 200) {
                projectUserMap.put(i + "", "用户" + i);
            }

        }
    }


    public static class TaskExcelReadListener implements ReadListener<TaskExcel> {
        private List<TaskExcel> taskExcels = new ArrayList<>();

        public List<TaskExcel> getTaskExcels() {
            return taskExcels;
        }

        @Override
        public void invoke(TaskExcel data, AnalysisContext context) {
            taskExcels.add(data);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {

        }

        @Override
        public void extra(CellExtra extra, AnalysisContext context) {
        }
    }


    @Test
    void importExcel() throws Exception {
//        String filePath = "D:\\newchinasie\\pmsx\\src\\test\\resources\\1678258926028.xlsx";
        String filePath = "D:\\newchinasie\\pmsx\\1678259817896.xlsx";
        TaskExcelReadListener taskExcelReadListener = new TaskExcelReadListener();
        EasyExcel.read(filePath, TaskExcel.class, taskExcelReadListener)
                .sheet().headRowNumber(2).doRead();
        List<TaskExcel> taskExcels = taskExcelReadListener.getTaskExcels();

        Map<String, String> orderIdMap = taskExcels.stream().collect(Collectors.toMap(TaskExcel::getOrder, o -> IdUtil.fastSimpleUUID()));
        Map<String, TaskExcel> taskExcelsMap = taskExcels.stream().collect(Collectors.toMap(TaskExcel::getOrder, Function.identity()));


        List<TaskExcelDO> taskExcelDOS = new ArrayList<>();
        List<Map<String, Object>> errors = new ArrayList<>();
        AtomicInteger index = new AtomicInteger(0);
        taskExcels.forEach(t -> {
            /**
             * 导入校验：
             * 1、任务的开始时间和结束时间在父级任务的开始时间和结束时间范围内
             * 2、责任人不能为空且是否在项目资源池中
             * 3、任务名称不能为空
             * 4、任务类型不能为空且在指定范围：课题、任务、专题
             * 5、校验父级任务序号不能是自己且父级序号存在
             * 7、责任部门不能为空且是否在项目资源池中
             * 8、参与部门是否在项目资源池中
             */
            Map<String, Object> errorNoteMap = new HashMap<>();
            List<String> errorNotes = new ArrayList<>();

            if (StrUtil.isBlank(t.getOrder())) {
                errorNoteMap.put("order", "序号为空" + index.get());
                errorNotes.add("序号为空");
            } else {
                errorNoteMap.put("order", t.getOrder());
            }

            if (Objects.nonNull(t.getParentOrder())) {
                TaskExcel parentTaskExcel = taskExcelsMap.get(t.getParentOrder());
                //5、校验父级任务序号不能是自己且父级序号存在
                if (!orderIdMap.containsKey(t.getParentOrder())) {
                    errorNotes.add("父级序号不存在");
                }
                if (StrUtil.equals(t.getOrder(), t.getParentOrder())) {
                    errorNotes.add("父级任务序号与子级序号相同");
                }
                //1、任务的开始时间和结束时间在父级任务的开始时间和结束时间范围内
                if (!betweenOn(DateUtil.parseDate(t.getStartTime()), DateUtil.parseDate(t.getEndTime()), DateUtil.parseDate(parentTaskExcel.getStartTime()), DateUtil.parseDate(parentTaskExcel.getEndTime()))) {
                    errorNotes.add("任务的开始时间和结束时间不在父级任务的开始时间和结束时间范围内");
                }

            }

            //2、责任人是否在项目资源池中
            if (StrUtil.isBlank(t.getResponsibleUserName())) {
                errorNotes.add("责任人为空");
            } else {
                String userId = userMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey)).get(t.getResponsibleUserName());
                if (!projectUserMap.containsKey(userId)) {
                    errorNotes.add("责任人不在项目资源池中");
                }
            }

            //3、任务名称不能为空
            if (StrUtil.isBlank(t.getName())) {
                errorNotes.add("任务名称为空");
            }
            //4、任务类型不能为空且在指定范围：课题、任务、专题
            if (StrUtil.isBlank(t.getTaskTypeName())) {
                errorNotes.add("任务类型为空");
            } else {
                String taskTypeId = phaseMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey)).get(t.getPhase());
                if (!taskTypeMap.containsKey(taskTypeId)) {
                    errorNotes.add("任务类型不在指定范围：课题、任务、专题");
                }
            }
            // 7、责任部门不能为空且是否在项目资源池中
            if (StrUtil.isBlank(t.getResponsibleDepartmentName())) {
                errorNotes.add("责任部门为空");
            } else {
                String responsibleDepartmentId = deptMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey)).get(t.getResponsibleDepartmentName());
                if (!projectDeptMap.containsKey(responsibleDepartmentId)) {
                    errorNotes.add("责任部门不在项目资源池中");
                }

            }
            // 8、参与部门是否在项目资源池中
            if (StrUtil.isNotBlank(t.getInvolvedDepartmentName())) {
                String involvedDepartmentName = deptMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey)).get(t.getInvolvedDepartmentName());
                if (!projectDeptMap.containsKey(involvedDepartmentName)) {
                    errorNotes.add("参与部门不在项目资源池中");
                }
            }
            if (errorNotes.size() == 0) {
                /**
                 * 业务逻辑
                 * 1. 生成编码
                 */
                TaskExcelDO tmp = TaskExcelDO.builder()
                        .id(orderIdMap.get(t.getOrder()))
                        .name(t.getName())
                        .phaseId(phaseMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey)).get(t.getPhase()))
                        .parentId(orderIdMap.get(t.getParentOrder()))
                        .taskTypeId(phaseMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey)).get(t.getPhase()))
                        .responsibleDepartmentId(deptMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey)).get(t.getResponsibleDepartmentName()))
                        .responsibleUserId(userMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey)).get(t.getResponsibleUserName()))
                        .involvedDepartmentId(deptMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey)).get(t.getInvolvedDepartmentName()))
                        .startTime(t.getStartTime())
                        .endTime(t.getEndTime())
                        .description(t.getDescription())
                        .build();
                taskExcelDOS.add(tmp);
            } else {
                errorNoteMap.put("errorNotes", String.join("，", errorNotes));
                errors.add(errorNoteMap);
            }
            index.getAndIncrement();
        });
        System.out.println(taskExcelDOS);
    }


    public static boolean betweenOn(Date date1, Date date2, Date date3, Date date4) {
        if (date1 == null || date2 == null || date3 == null || date4 == null) {
            return false;
        }
        return (date1.getTime() - date3.getTime()) > 0 && ((date4.getTime() - date2.getTime()) > 0);
    }


    @Test
    void exportExcel() throws Exception {
        String filePath = "D:\\newchinasie\\pmsx\\src\\test\\resources\\项目管理_任务导入.xlsx";
        EasyExcel.write().needHead(false).
                withTemplate(filePath).file(System.currentTimeMillis() + ".xlsx").sheet("任务").doWrite(data());

    }

    private List<TaskExcel> data() {
        for (int i = 1; i <= 10000; i++) {
            String parentOrder = null;
            String phase = "K";
            if (i <= 500) {
                phase = new ArrayList<>(phaseMap.values()).get(rand.nextInt(4));
            }
            if (i > 500) {
                parentOrder = rand.nextInt(500 - 1 + 1) + 1 + "";
                phase = parents.get(parentOrder).getPhase();
            }
            TaskExcel taskExcel = TaskExcel.builder()
                    .order(i + "")
                    .name("任务" + i)
                    .phase(phase)
                    .parentOrder(parentOrder)
                    .taskTypeName(taskTypeMap.get(rand.nextInt(4 - 1 + 1) + 1 + ""))
                    .responsibleDepartmentName(deptMap.get(rand.nextInt(40 - 1 + 1) + 1 + ""))
                    .responsibleUserName(userMap.get(rand.nextInt(400 - 1 + 1) + 1 + ""))
                    .involvedDepartmentName(deptMap.get(rand.nextInt(30 - 1 + 1) + 1 + ""))
                    .startTime(DateUtil.formatDate(new Date()))
                    .endTime(DateUtil.formatDate(new Date()))
                    .description("描述" + i)
                    .build();
            parents.put(taskExcel.getOrder(), taskExcel);

        }
        return new ArrayList<>(parents.values());
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TaskExcel {
        @ExcelProperty("任务序号*")
        private String order;
        @ExcelProperty("任务名称*")
        private String name;
        @ExcelProperty("所属阶段*")
        private String phase;
        @ExcelProperty("父级任务序号")
        private String parentOrder;
        @ExcelProperty("任务类型*")
        private String taskTypeName;
        @ExcelProperty("责任部门*")
        private String responsibleDepartmentName;
        @ExcelProperty("责任人*")
        private String responsibleUserName;
        @ExcelProperty("参与部门")
        private String involvedDepartmentName;
        @ExcelProperty("计划开始时间")
        private String startTime;
        @ExcelProperty("计划结束时间")
        private String endTime;
        @ExcelProperty("描述")
        private String description;
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TaskExcelDO {
        /**
         * 主键
         */
        private String id;
        /**
         * 任务名称
         */
        private String name;
        /**
         * 所属阶段
         */
        private String phaseId;
        /**
         * 父级任务
         */
        private String parentId;
        /**
         * 任务类型
         */
        private String taskTypeId;
        /**
         * 责任部门
         */
        private String responsibleDepartmentId;
        /**
         * 责任人
         */
        private String responsibleUserId;
        /**
         * 参与部门
         */
        private String involvedDepartmentId;
        /**
         * 计划开始时间
         */
        private String startTime;
        /**
         * 计划结束时间
         */
        private String endTime;
        /**
         * 描述
         */
        private String description;
    }


    @Test
    public void test_Split() {
        System.out.println("0".split(",").length + "层");
    }

}
