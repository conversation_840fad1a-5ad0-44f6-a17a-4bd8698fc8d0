<template>
  <div class="table-wrap">
    <OrionTable
      ref="tableRef"
      :options="tableOption"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { OrionTable } from 'lyra-component-vue3';

const tableRef = ref(null);

const columns = [
  {
    title: '编号',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '计划名称',
    dataIndex: 'age',
    key: 'age',
  },
  {
    title: '计划层级',
    dataIndex: 'age',
    key: 'age',
  },
  {
    title: '责任部门',
    dataIndex: 'age',
    key: 'age',
  },
  {
    title: '计划负责人',
    dataIndex: 'age',
    key: 'age',
  },
];
const tableOption = {
  rowSelection: {},
  columns,
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
};
</script>

<style scoped lang="less">
.table-wrap{
  height: 400px;
  overflow: hidden;
}
</style>
