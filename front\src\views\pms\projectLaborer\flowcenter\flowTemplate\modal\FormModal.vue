<template>
  <BasicModal
    v-bind="$attrs"
    :title="`${actionType === 'add' ? '新增' : '编辑'}分类`"
    @register="registerModal"
  >
    <BasicForm
      :show-action-button-group="false"
      @register="register"
    />
    <template #footer>
      <a-button
        v-if="actionType === 'add'"
        @click="resetFields()"
      >
        重置
      </a-button>
      <a-button @click="closeModal">
        取消
      </a-button>
      <a-button
        v-if="actionType === 'add'"
        @click="validateForm(true)"
      >
        确定并继续
      </a-button>
      <a-button
        type="primary"
        @click="validateForm(false)"
      >
        确定
      </a-button>
    </template>
  </BasicModal>
</template>
<script lang="ts">
import { defineComponent, toRefs, reactive } from 'vue';
import {
  BasicModal, useModalInner, BasicForm, FormSchema, useForm,
} from 'lyra-component-vue3';
// import { BasicModal, useModalInner } from '/@/components/Modal';
import { Descriptions, Tabs, message } from 'ant-design-vue';
// import { BasicForm, FormSchema, useForm } from '/@/components/Form/index';
import Api from '/@/api/index';
import { workflowApi } from '../../util/apiConfig';
import { isObjectValueEqual } from '/@/views/pms/projectLaborer/utils/isObjectValueEqual';
import { findParents } from '/@/views/pms/projectLaborer/utils/tree/index';
// import { useActionsRecord } from '/@/hooks/actionsRecord/useActionsRecord';

export default defineComponent({
  components: {
    BasicModal,
    aDescriptions: Descriptions,
    aTabs: Tabs,
    BasicForm,
  },
  props: {
    actionType: {
      type: String,
      required: true,
      default: 'add',
    },
    treeData: Object,
  },
  setup(props, { emit }) {
    // 使用 `toRefs` 创建对prop的 `data` property 的响应式引用
    const { treeData, actionType: actionTypeRef }: any = toRefs(props);
    const apiOrg = new Api(`${workflowApi}/act-classify`);
    // 定义表单的字段规则
    const schemas: FormSchema[] = [
      {
        field: 'parentId',
        label: '所属上级',
        colProps: {
          span: 20,
        },
        component: 'Cascader',
        componentProps: {
          displayRender({ labels }: any) {
            // 只显示最后一级文字
            return labels[labels.length - 1];
          },
          fieldNames: {
            label: 'name',
            value: 'id',
          },
          changeOnSelect: true,
          options: treeData,
        },
      },
      {
        field: 'name',
        component: 'Input',
        label: '分类名称',
        colProps: {
          span: 20,
        },
        rules: [
          {
            type: 'string',
            required: true,
          },
          {
            max: 30,
            message: '长度不能超过30字',
          },
        ],
        componentProps: {
          maxlength: 31,
        },
      },
      {
        field: 'sort',
        component: 'InputNumber',
        label: '显示顺序',
        colProps: {
          span: 20,
        },
        rules: [
          {
            type: 'number',
            message: '请输入数字',
          },
        ],
      },
      {
        field: 'description',
        component: 'InputTextArea',
        label: '分类描述',
        colProps: {
          span: 20,
        },
      },
    ];

    // 注册一个表单
    const [
      register,
      {
        resetFields, clearValidate, validateFields, setFieldsValue,
      },
    ] = useForm({
      labelWidth: 120,
      schemas,
      actionColOptions: {
        span: 24,
      },
    });

    // 缓存数据,用于编辑前后对比
    let cacheData: any = reactive({});

    // 弹窗内部的注册函数,可以在内部自己关闭
    const [registerModal, { closeModal, changeLoading }] = useModalInner((data) => {
      cacheData = JSON.parse(JSON.stringify(data)); // 深复制这个对象
      const { parentId, id } = data;
      let ids = [];
      ids = findParents(treeData.value, id, actionTypeRef.value === 'add');

      if (actionTypeRef.value === 'add') {
        data.parentId = ids;
        // 新增的时候,如果选中节点,所属部门要自动选中,其它字段重置为空
        setFieldsValue({
          parentId: data.parentId,
          name: '',
          description: '',
          sort: '',
        });
      } else {
        data.parentId = parentId === '0' ? [] : ids;
        setFieldsValue(data);
      }
      clearValidate();
    });
      /*
       * 验证增加和编辑时候的表单
       * */
    async function validateForm(status: boolean) {
      try {
        const res = await validateFields();
        // 把 parentId 数组转换成最后一个id的字符串
        if (res?.parentId?.length) {
          res.parentId = res.parentId[res.parentId.length - 1];
        } else {
          res.parentId = '0';
        }

        let service: any; // 编辑和增加用不同的接口
        let isEqual = false; // 对比是否相等的判断

        if (actionTypeRef.value === 'add') {
          // 编辑功能
          service = apiOrg.add(res);
        } else {
          // 去对比字段前后是否相等
          if (isObjectValueEqual(cacheData, res, [
            'name',
            'parentId',
            'remark',
          ])) {
            isEqual = true;
          } else {
            res.id = cacheData.id; // 编辑的时候需要部门ID
            service = apiOrg.update(res);
          }
        }
        if (isEqual) {
          // 如果相等,只需要关闭弹窗,什么都不做
          closeModal();
        } else {
          changeLoading(true);
          service
            .then((nodeId: any) => {
              !status && closeModal();
              if (actionTypeRef.value === 'add') {
                res.id = nodeId;
                emit('update-list', res); // 更新之后,需要选中当前已经创建的节点
              } else {
                emit('update-filed', res);
              }
              changeLoading(false);
              message.success(`${actionTypeRef.value === 'add' ? '新增' : '编辑'}成功`);

              const { id, name } = res;
            })
            .catch(() => {
              changeLoading(false);
            });
        }
      } catch (error) {
        changeLoading(false);
      }
    }

    return {
      validateForm,
      registerModal,
      resetFields,
      closeModal,
      register,
    };
  },
});
</script>
