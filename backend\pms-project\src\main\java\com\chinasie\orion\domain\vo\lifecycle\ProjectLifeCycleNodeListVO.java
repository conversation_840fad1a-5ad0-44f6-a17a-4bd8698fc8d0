//package com.chinasie.orion.domain.vo;
//
//import com.chinasie.orion.domain.dto.lifecycle.ProjectLifeCycleNodeDTO;
//import io.swagger.annotations.ApiModel;
//import io.swagger.annotations.ApiModelProperty;
//
//import java.io.Serializable;
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
///**
// * 项目生命周期VO.
// *
// * <AUTHOR>
// */
//@ApiModel(value = "ProjectNodesVO", description = "项目生命周期节点VO")
//public class ProjectLifeCycleNodeListVO implements Serializable {
//    // 前端 enables 控制点
//    @ApiModelProperty(value = "enables控制点")
//    private Map<String, Boolean> enables = new HashMap<>();
//
//    // 项目生命周期节点
//    @ApiModelProperty(value = "项目生命周期节点")
//    private List<ProjectLifeCycleNodeDTO> nodes = new ArrayList<>();
//
//    public Map<String, Boolean> getEnables() {
//        return enables;
//    }
//
//    public void setEnables(Map<String, Boolean> enables) {
//        this.enables = enables;
//    }
//
//    public List<ProjectLifeCycleNodeDTO> getNodes() {
//        return nodes;
//    }
//
//    public void setNodes(List<ProjectLifeCycleNodeDTO> nodes) {
//        this.nodes = nodes;
//    }
//}
