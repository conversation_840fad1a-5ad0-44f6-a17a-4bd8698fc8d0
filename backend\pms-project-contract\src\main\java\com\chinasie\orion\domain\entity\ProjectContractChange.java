package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ProjectContract Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-24 09:56:13
 */
@TableName(value = "pms_project_contract_change")
@ApiModel(value = "ProjectContract对象", description = "项目合同信息(变更)")
@Data
public class ProjectContractChange extends ObjectEntity implements Serializable {


    /**
     * 变更申请单id
     */
    @ApiModelProperty(value = "变更申请单id")
    @TableField(value = "change_id")
    private String changeId;

    /**
     * 变更申请单号
     */
    @ApiModelProperty(value = "变更申请单号")
    @TableField(value = "change_number")
    private String changeNumber;

    /**
     * 字段类型
     */
    @ApiModelProperty(value = "字段类型")
    @TableField(value = "field_type")
    private String fieldType;

    /**
     * 字段名称
     */
    @ApiModelProperty(value = "字段名称")
    @TableField(value = "field_name")
    private String fieldName;

    /**
     * 字段编码
     */
    @ApiModelProperty(value = "字段编码")
    @TableField(value = "field_code")
    private String fieldCode;

    /**
     * 修改前值
     */
    @ApiModelProperty(value = "修改前值")
    @TableField(value = "old_value")
    private Object oldValue;

    /**
     * 修改后值
     */
    @ApiModelProperty(value = "修改后值")
    @TableField(value = "new_value")
    private Object newValue;

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    @TableField(value = "contract_id")
    private String contractId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_number")
    private String contractNumber;

}
