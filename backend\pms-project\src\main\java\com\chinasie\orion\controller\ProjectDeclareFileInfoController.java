package com.chinasie.orion.controller;

import com.chinasie.orion.constant.ProjectDeclareFileTypeEnum;
import com.chinasie.orion.domain.dto.FileInfoDTO;
import com.chinasie.orion.domain.dto.FileInfoQueryDTO;
import com.chinasie.orion.domain.vo.DocumentVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectDeclareFileInfoService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * ProjectDeclareFileInfo 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18 18:02:59
 */
@RestController
@RequestMapping("/projectDeclareFileInfo")
@Api(tags = "项目申报文件信息")
public class ProjectDeclareFileInfoController {

    @Autowired
    private ProjectDeclareFileInfoService projectDeclareFileInfoService;


    @ApiOperation("保存会议记录附件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileInfoDTOList", dataType = "List")
    })
    @PostMapping(value = "/meeting/saveBatch")
    @LogRecord(success = "【{USER{#logUserId}}】保存会议记录附件", type = "项目申报文件信息", subType = "保存会议记录附件", bizNo = "")
    public ResponseDTO<List<String>> saveBatchAdd(@RequestBody List<FileInfoDTO> fileInfoDTOList) throws Exception {
        List<String> res = projectDeclareFileInfoService.saveBatchAdd(ProjectDeclareFileTypeEnum.MEETING.getCode(), fileInfoDTOList);
        return new ResponseDTO<>(res);
    }

    @ApiOperation("保存支持性材料")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileInfoDTOList", dataType = "List")
    })
    @PostMapping(value = "/material/saveBatch")
    @LogRecord(success = "【{USER{#logUserId}}】保存支持性材料", type = "项目申报文件信息", subType = "保存支持性材料", bizNo = "")
    public ResponseDTO<List<String>> materialSaveBatchAdd(@RequestBody List<FileInfoDTO> fileInfoDTOList) throws Exception {
        List<String> res = projectDeclareFileInfoService.saveBatchAdd(ProjectDeclareFileTypeEnum.MATERIAL.getCode(), fileInfoDTOList);
        return new ResponseDTO<>(res);
    }

    @ApiOperation("保存相关附件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileInfoDTOList", dataType = "List")
    })
    @PostMapping(value = "/related/saveBatch")
    @LogRecord(success = "【{USER{#logUserId}}】保存相关附件", type = "项目申报文件信息", subType = "保存相关附件", bizNo = "")
    public ResponseDTO<List<String>> relatedSaveBatchAdd(@RequestBody List<FileInfoDTO> fileInfoDTOList) throws Exception {
        List<String> res = projectDeclareFileInfoService.saveBatchAdd(ProjectDeclareFileTypeEnum.RELATED.getCode(), fileInfoDTOList);
        return new ResponseDTO<>(res);
    }

    @ApiOperation("会议记录附件列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataId", dataType = "String"),
            @ApiImplicitParam(name = "fileInfoQueryDTO", dataType = "FileInfoQueryDTO")
    })
    @PostMapping("/meeting/getList/{dataId}")
    @LogRecord(success = "【{USER{#logUserId}}】会议记录附件列表", type = "项目申报文件信息", subType = "会议记录附件列表", bizNo = "")
    public ResponseDTO<List<DocumentVO>> getMeetingDocumentList(@PathVariable("dataId") String dataId, @RequestBody(required = false) FileInfoQueryDTO fileInfoQueryDTO) throws Exception {
        List<DocumentVO> res = projectDeclareFileInfoService.getDocumentList(ProjectDeclareFileTypeEnum.MEETING.getCode(), dataId, fileInfoQueryDTO);
        return new ResponseDTO<>(res);
    }

    @ApiOperation("相关附件列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataId", dataType = "String"),
            @ApiImplicitParam(name = "fileInfoQueryDTO", dataType = "FileInfoQueryDTO")
    })
    @PostMapping("/related/getList/{dataId}")
    @LogRecord(success = "【{USER{#logUserId}}】相关附件列表", type = "项目申报文件信息", subType = "相关附件列表", bizNo = "")
    public ResponseDTO<List<DocumentVO>> getRelatedDocumentList(@PathVariable("dataId") String dataId, @RequestBody(required = false) FileInfoQueryDTO fileInfoQueryDTO) throws Exception {
        List<DocumentVO> res = projectDeclareFileInfoService.getDocumentList(ProjectDeclareFileTypeEnum.RELATED.getCode(), dataId, fileInfoQueryDTO);
        return new ResponseDTO<>(res);
    }

    @ApiOperation("支持性材料列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataId", dataType = "String"),
            @ApiImplicitParam(name = "fileInfoQueryDTO", dataType = "FileInfoQueryDTO")
    })
    @PostMapping("/material/getList/{dataId}")
    @LogRecord(success = "【{USER{#logUserId}}】支持性材料列表", type = "项目申报文件信息", subType = "支持性材料列表", bizNo = "")
    public ResponseDTO<List<DocumentVO>> getMaterialDocumentList(@PathVariable("dataId") String dataId, @RequestBody(required = false) FileInfoQueryDTO fileInfoQueryDTO) throws Exception {
        List<DocumentVO> res = projectDeclareFileInfoService.getDocumentList(ProjectDeclareFileTypeEnum.MATERIAL.getCode(), dataId, fileInfoQueryDTO);
        return new ResponseDTO<>(res);
    }

    @ApiOperation("会议记录附件分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping("/meeting/getPage")
    @LogRecord(success = "【{USER{#logUserId}}】会议记录附件分页", type = "项目申报文件信息", subType = "会议记录附件分页", bizNo = "")
    public ResponseDTO<PageResult<DocumentVO>> getMeetingDocumentVOPage(@RequestBody Page<FileInfoDTO> pageRequest) throws Exception {
        PageResult<DocumentVO> pageResult = projectDeclareFileInfoService.getDocumentVOPage(ProjectDeclareFileTypeEnum.MEETING.getCode(), pageRequest);
        return new ResponseDTO<>(pageResult);
    }

    @ApiOperation("相关附件分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping("/related/getPage")
    @LogRecord(success = "【{USER{#logUserId}}】相关附件分页", type = "项目申报文件信息", subType = "相关附件分页", bizNo = "")
    public ResponseDTO<PageResult<DocumentVO>> getRelatedDocumentVOPage(@RequestBody Page<FileInfoDTO> pageRequest) throws Exception {
        PageResult<DocumentVO> pageResult = projectDeclareFileInfoService.getDocumentVOPage(ProjectDeclareFileTypeEnum.RELATED.getCode(), pageRequest);
        return new ResponseDTO<>(pageResult);
    }

    @ApiOperation("支持性材料分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping("/material/getPage")
    @LogRecord(success = "【{USER{#logUserId}}】支持性材料分页", type = "项目申报文件信息", subType = "支持性材料分页", bizNo = "")
    public ResponseDTO<PageResult<DocumentVO>> getMaterialDocumentVOPage(@RequestBody Page<FileInfoDTO> pageRequest) throws Exception {
        PageResult<DocumentVO> pageResult = projectDeclareFileInfoService.getDocumentVOPage(ProjectDeclareFileTypeEnum.MATERIAL.getCode(), pageRequest);
        return new ResponseDTO<>(pageResult);
    }

    @ApiOperation("会议记录附件删除文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileIdList", dataType = "List")
    })
    @DeleteMapping("/meeting/removeBatch")
    @LogRecord(success = "【{USER{#logUserId}}】会议记录附件删除文件", type = "项目申报文件信息", subType = "会议记录附件删除文件", bizNo = "")
    public ResponseDTO<Boolean> deleteMeetingBatchFile(@RequestBody List<String> fileIdList) throws Exception {
        Boolean res = projectDeclareFileInfoService.deleteBatchFile(ProjectDeclareFileTypeEnum.MEETING.getCode(), fileIdList);
        return new ResponseDTO<>(res);
    }

    @ApiOperation("相关附件删除文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileIdList", dataType = "List")
    })
    @DeleteMapping("/related/removeBatch")
    @LogRecord(success = "【{USER{#logUserId}}】相关附件删除文件", type = "项目申报文件信息", subType = "相关附件删除文件", bizNo = "")
    public ResponseDTO<Boolean> deleteRelatedBatchFile(@RequestBody List<String> fileIdList) throws Exception {
        Boolean res = projectDeclareFileInfoService.deleteBatchFile(ProjectDeclareFileTypeEnum.RELATED.getCode(), fileIdList);
        return new ResponseDTO<>(res);
    }

    @ApiOperation("支持性材料删除文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileIdList", dataType = "List")
    })
    @DeleteMapping("/material/removeBatch")
    @LogRecord(success = "【{USER{#logUserId}}】支持性材料删除文件", type = "项目申报文件信息", subType = "支持性材料删除文件", bizNo = "")
    public ResponseDTO<Boolean> deleteMaterialBatchFile(@RequestBody List<String> fileIdList) throws Exception {
        Boolean res = projectDeclareFileInfoService.deleteBatchFile(ProjectDeclareFileTypeEnum.MATERIAL.getCode(), fileIdList);
        return new ResponseDTO<>(res);
    }

}
