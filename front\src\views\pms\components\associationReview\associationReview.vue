<template>
  <div class="plan-tab">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      @selectionChange="selectionChange"
    >
      <template
        v-if="['risk','question'].includes(props.pageType)"
        #toolbarLeft
      >
        <div class="button-margin-right">
          <BasicButton
            v-if="isPower(powerCode?.addCode, powerData)"
            type="primary"
            icon="sie-icon-tianjiaxinzeng"
            @click="quoteRisk"
          >
            关联评审
          </BasicButton>
          <BasicButton
            v-if="isPower(powerCode?.removeCode, powerData)"
            icon="sie-icon-shanchu"
            :disabled="pageType==='risk'&&selectedRowKeys.length===0"
            @click="deleteBatch"
          >
            移除
          </BasicButton>
        </div>
      </template>
    </OrionTable>
  </div>
</template>
<script lang="ts" setup>
import {
  BasicButton, OrionTable, DataStatusTag, openDrawer, openModal, isPower,
} from 'lyra-component-vue3';
import {
  inject, ref, h, Ref, computed,
} from 'vue';
import Api from '/@/api';
import { useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import dayjs from 'dayjs';
import AssociationReviewModal from './components/AssociationReviewModal.vue';

const props = withDefaults(defineProps<{
    pageType:string,
    selectType:string,
    getReviewTableDataApi:any,
    deleteReviewBatchApi:any,
    addReviewTableApi:any
    powerCode?:object
}>(), {
  pageType: 'risk',
  selectType: 'check',
  getReviewTableDataApi: null,
  deleteReviewBatchApi: null,
  addReviewTableApi: null,
  powerCode: () => ({}),
});
const tableRef = ref();
const powerData = inject('powerData');
const formData:Record<any, any> = inject('formData', {});
const router = useRouter();
const selectedRowKeys:Ref<string[]> = ref([]);
const getDetails = inject('getDetails');

function selectionChange(data) {
  selectedRowKeys.value = data.keys;
}
function quoteRisk() {
  const quoteRiskPoolRef = ref();
  openModal({
    title: '关联评审单',
    width: 1100,
    height: 700,
    content(h) {
      return h(AssociationReviewModal, {
        ref: quoteRiskPoolRef,
        selectType: props.selectType,
        formId: formData.value.id,
        addReviewTableApi: props.addReviewTableApi,
        projectId: formData.value.projectId,
      });
    },
    async onOk() {
      await quoteRiskPoolRef.value.saveData();
      updateData();
    },
  });
}
function deleteBatch() {
  deleteBatchData(selectedRowKeys.value, 'batch');
}
function deleteBatchData(params, type) {
  let tableData = tableRef.value.getDataSource();
  if (tableData.length === 0) {
    message.warning('暂无数据可移除');
    return;
  }
  Modal.confirm({
    title: '移除提示',
    content: props.pageType === 'question' ? '是否移除该条数据？' : '是否移除所选的数据？',
    async onOk() {
      let keys = props.pageType === 'question' ? [tableData[0].id] : selectedRowKeys.value;
      await props.deleteReviewBatchApi(keys);
      message.success('移除成功');
      updateData();
    },
  });
}
async function updateData() {
  if (props.pageType === 'question') {
    await getDetails();
  }
  tableRef.value.reload();
}
const tableOptions = {
  showIndexColumn: false,
  bordered: false,
  deleteToolButton: 'add|delete|enable|disable',
  rowSelection: props.pageType === 'question' ? false : {},
  pagination: false,
  api(params) {
    if (!formData.value.id) {
      return Promise.resolve([]);
    }
    return props.getReviewTableDataApi(params);
  },
  columns: initColumns(),
  actions: [
    {
      text: '移除',
      isShow: (record) => !record.isCurrentCreate,
      onClick(record) {
        deleteBatchData([record.id], 'one');
      },
    },
  ],
};
function initColumns() {
  let columns:Record<any, any>[] = [
    {
      title: '评审编码',
      dataIndex: 'number',
    },
    {
      title: '评审名称',
      dataIndex: 'name',
      customRender({
        record, text,
      }) {
        return h('span', {
          class: 'action-btn',
          onClick() {
            router.push({
              name: 'CRUorMWXqDetails',
              params: {
                id: record.id,
              },
            });
          },
        }, text);
      },
    },
    {
      title: '评审类型',
      dataIndex: 'reviewTypeName',
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
    },
    {
      title: '任务计划',
      dataIndex: 'planName',
      customRender({
        record, text,
      }) {
        return h('span', {
          class: 'action-btn',
          onClick() {
            router.push({
              name: 'ProPlanDetails',
              params: { id: record.planId },
            });
          },
        }, text);
      },
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      type: 'dateTime',
    },
  ];
  return columns;
}

</script>