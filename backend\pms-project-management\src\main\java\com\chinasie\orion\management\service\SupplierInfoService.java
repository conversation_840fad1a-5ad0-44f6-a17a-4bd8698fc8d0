package com.chinasie.orion.management.service;


import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.dto.SupplierInfoDTO;
import com.chinasie.orion.management.domain.entity.SupplierInfo;
import com.chinasie.orion.management.domain.vo.SupplierInfoVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * SupplierInfo 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
public interface SupplierInfoService extends OrionBaseService<SupplierInfo> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    SupplierInfoVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param supplierInfoDTO
     */
    String create(SupplierInfoDTO supplierInfoDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param supplierInfoDTO
     */
    Boolean edit(SupplierInfoDTO supplierInfoDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<SupplierInfoVO> pages(Page<SupplierInfoDTO> pageRequest) throws Exception;

    /**
     * 查询数量及总数
     * <p>
     * * @param pageRequest
     */
    Map<String, Object> getNum(Page<SupplierInfoDTO> pageRequest);

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(Page<SupplierInfoDTO> pageRequest, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param vos
     * * @param type
     * * @throws Exception
     */
    void setEveryName(List<SupplierInfoVO> vos) throws Exception;

    /**
     * 苏州院合格供应商分页
     * @param pageRequest
     * @return
     */
    Page<SupplierInfoVO> parkPages(Page<SupplierInfoDTO> pageRequest) throws Exception;

    Page<SupplierInfoVO> otherPages(Page<SupplierInfoDTO> pageRequest) throws Exception;

    /**
     * 潜在
     * @param pageRequest
     * @return
     */
    Page<SupplierInfoVO> latentPages(Page<SupplierInfoDTO> pageRequest) throws Exception;


    void parkExportByExcel(Page<SupplierInfoDTO> pageRequest, HttpServletResponse response) throws Exception;
    void otherExportByExcel(Page<SupplierInfoDTO> pageRequest, HttpServletResponse response) throws Exception;
    void latentExportByExcel(Page<SupplierInfoDTO> pageRequest, HttpServletResponse response) throws Exception;

}
