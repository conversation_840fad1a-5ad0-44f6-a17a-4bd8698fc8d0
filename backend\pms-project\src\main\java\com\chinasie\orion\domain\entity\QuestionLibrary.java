package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.Boolean;
import java.lang.String;

/**
 * QuestionLibrary Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-17 13:36:10
 */
@TableName(value = "pms_question_library")
@ApiModel(value = "QuestionLibraryEntity对象", description = "问题库")
@Data
public class QuestionLibrary extends ObjectEntity implements Serializable{

    /**
     * 问题来源
     */
    @ApiModelProperty(value = "问题来源")
    @TableField(value = "question_source")
    private String questionSource;

    /**
     * 严重程度
     */
    @ApiModelProperty(value = "严重程度")
    @TableField(value = "serious_level")
    private String seriousLevel;

    /**
     * 预计完成时间
     */
    @ApiModelProperty(value = "预计完成时间")
    @TableField(value = "predict_end_time")
    private Date predictEndTime;

    /**
     * 问题类型
     */
    @ApiModelProperty(value = "问题类型")
    @TableField(value = "question_type")
    private String questionType;

    /**
     * 提出人
     */
    @ApiModelProperty(value = "提出人")
    @TableField(value = "exhibitor")
    private String exhibitor;

    /**
     * 提出人名称
     */
    @ApiModelProperty(value = "提出人名称")
    @TableField(value = "exhibitor_name")
    private String exhibitorName;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 问题内容
     */
    @ApiModelProperty(value = "问题内容")
    @TableField(value = "content")
    private String content;

    /**
     * 提出时间
     */
    @ApiModelProperty(value = "提出时间")
    @TableField(value = "proposed_time")
    private Date proposedTime;

    /**
     * 进度
     */
    @ApiModelProperty(value = "进度")
    @TableField(value = "schedule")
    private String schedule;

    /**
     * 接收人
     */
    @ApiModelProperty(value = "接收人")
    @TableField(value = "recipient")
    private String recipient;

    /**
     * 接收人名称
     */
    @ApiModelProperty(value = "接收人名称")
    @TableField(value = "recipient_name")
    private String recipientName;

    /**
     * 负责人id
     */
    @ApiModelProperty(value = "负责人id")
    @TableField(value = "principal_id")
    private String principalId;

    /**
     * 负责人名称
     */
    @ApiModelProperty(value = "负责人名称")
    @TableField(value = "principal_name")
    private String principalName;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    @TableField(value = "priority_level")
    private String priorityLevel;

    /**
     * 文档ID
     */
    @ApiModelProperty(value = "文档ID")
    @TableField(value = "document_id")
    private String documentId;

    /**
     * 产品编码
     */
    @ApiModelProperty(value = "产品编码")
    @TableField(value = "product_number")
    private String productNumber;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    @TableField(value = "material_number")
    private String materialNumber;

    /**
     * 生产订单
     */
    @ApiModelProperty(value = "生产订单")
    @TableField(value = "production_orders")
    private String productionOrders;

    /**
     * 产品编号（SN）
     */
    @ApiModelProperty(value = "产品编号（SN）")
    @TableField(value = "product_number_sn")
    private String productNumberSn;

    /**
     * 阶段
     */
    @ApiModelProperty(value = "阶段")
    @TableField(value = "stage")
    private String stage;

    /**
     * 过程环节
     */
    @ApiModelProperty(value = "过程环节")
    @TableField(value = "process_link")
    private String processLink;

    /**
     * 过程分类
     */
    @ApiModelProperty(value = "过程分类")
    @TableField(value = "process_classifi")
    private String processClassifi;

    /**
     * 问题现象一级分类
     */
    @ApiModelProperty(value = "问题现象一级分类")
    @TableField(value = "problem_phenomenon_one")
    private String problemPhenomenonOne;

    /**
     * 问题现象二级分类
     */
    @ApiModelProperty(value = "问题现象二级分类")
    @TableField(value = "problem_phenomenon_two")
    private String problemPhenomenonTwo;

    /**
     * 问题现象三级分类
     */
    @ApiModelProperty(value = "问题现象三级分类")
    @TableField(value = "problem_phenomenon_th")
    private String problemPhenomenonTh;

    /**
     * 问题等级分类
     */
    @ApiModelProperty(value = "问题等级分类")
    @TableField(value = "problem_level")
    private String problemLevel;

    /**
     * 一级原因分类
     */
    @ApiModelProperty(value = "一级原因分类")
    @TableField(value = "reasion_one")
    private String reasionOne;

    /**
     * 二级原因分类
     */
    @ApiModelProperty(value = "二级原因分类")
    @TableField(value = "reasion_two")
    private String reasionTwo;

    /**
     * 三级原因分类
     */
    @ApiModelProperty(value = "三级原因分类")
    @TableField(value = "reasion_three")
    private String reasionThree;

    /**
     * 原因分析描述
     */
    @ApiModelProperty(value = "原因分析描述")
    @TableField(value = "reasion_remark")
    private String reasionRemark;

    /**
     * 纠正分类
     */
    @ApiModelProperty(value = "纠正分类")
    @TableField(value = "correct_classifi")
    private String correctClassifi;

    /**
     * 关联ECN编号
     */
    @ApiModelProperty(value = "关联ECN编号")
    @TableField(value = "ecn_number")
    private String ecnNumber;

    /**
     * 纠正描述
     */
    @ApiModelProperty(value = "纠正描述")
    @TableField(value = "correct_remark")
    private String correctRemark;

    /**
     * 问题升级类型
     */
    @ApiModelProperty(value = "问题升级类型")
    @TableField(value = "question_up_type")
    private String questionUpType;

    /**
     * 是否疑难技术问题
     */
    @ApiModelProperty(value = "是否疑难技术问题")
    @TableField(value = "is_di_technical_issues")
    private Boolean isDiTechnicalIssues;

    /**
     * 是否综合评估不再解决
     */
    @ApiModelProperty(value = "是否综合评估不再解决")
    @TableField(value = "is_assess")
    private Boolean isAssess;

    /**
     * 是否生态问题
     */
    @ApiModelProperty(value = "是否生态问题")
    @TableField(value = "is_ecological_issues")
    private Boolean isEcologicalIssues;

    /**
     * 是否典型质量案例
     */
    @ApiModelProperty(value = "是否典型质量案例")
    @TableField(value = "is_quality_use_cases")
    private Boolean isQualityUseCases;

    /**
     * 举一反三
     */
    @ApiModelProperty(value = "举一反三")
    @TableField(value = "one_case_to_another")
    private String oneCaseToAnother;

    /**
     * 纠正措施描述
     */
    @ApiModelProperty(value = "纠正措施描述")
    @TableField(value = "corre_ac_description")
    private String correAcDescription;

    /**
     * 意见类别
     */
    @ApiModelProperty(value = "意见类别")
    @TableField(value = "opinion_categories")
    private String opinionCategories;

    /**
     * 评审要点
     */
    @ApiModelProperty(value = "评审要点")
    @TableField(value = "review_points")
    private String reviewPoints;

    /**
     * 是否技术问题
     */
    @ApiModelProperty(value = "是否技术问题")
    @TableField(value = "is_technical_issues")
    private Boolean isTechnicalIssues;

    /**
     * 是否典型问题
     */
    @ApiModelProperty(value = "是否典型问题")
    @TableField(value = "is_typical_problems")
    private Boolean isTypicalProblems;

    /**
     * 意见分类
     */
    @ApiModelProperty(value = "意见分类")
    @TableField(value = "op_classification")
    private String opClassification;

    /**
     * 采纳情况
     */
    @ApiModelProperty(value = "采纳情况")
    @TableField(value = "adoption_situation")
    private String adoptionSituation;

    /**
     * 整体改进描述
     */
    @ApiModelProperty(value = "整体改进描述")
    @TableField(value = "overall_description")
    private String overallDescription;

    /**
     * 问题ID
     */
    @ApiModelProperty(value = "问题ID")
    @TableField(value = "question_id")
    private String questionId;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField(value = "name")
    private String name;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    @TableField(value = "number")
    private String number;

}
