package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

import java.util.List;

/**
 * TrainEquivalentRelation VO对象
 *
 * <AUTHOR>
 * @since 2024-06-04 17:09:19
 */
@ApiModel(value = "TrainEquivalentRelationVO对象", description = "员工培训等效关联表")
@Data
@Deprecated
public class TrainEquivalentRelationVO extends ObjectVO implements Serializable {

    /**
     * 等效认证Id
     */
    @ApiModelProperty(value = "等效认证Id")
    private String equivalentId;


    /**
     * 培训人员Id
     */
    @ApiModelProperty(value = "培训人员Id")
    private String trainPersonId;


}
