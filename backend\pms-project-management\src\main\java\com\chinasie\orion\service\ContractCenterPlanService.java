package com.chinasie.orion.service;





import com.chinasie.orion.domain.dto.CenterPlanApprovalDTO;
import com.chinasie.orion.domain.dto.ContractCenterPlanListDTO;
import com.chinasie.orion.domain.dto.TopCenterExportDTO;
import com.chinasie.orion.domain.entity.ContractCenterPlan;
import com.chinasie.orion.domain.dto.ContractCenterPlanDTO;
import com.chinasie.orion.domain.vo.CenterDisPlayPlanVO;
import com.chinasie.orion.domain.vo.ContractCenterPlanVO;

import java.io.IOException;
import java.lang.String;
import java.util.List;


import com.chinasie.orion.domain.vo.CostTypeUnitVO;
import com.chinasie.orion.domain.vo.PlanVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import java.lang.String;
import java.util.Map;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * ContractCenterPlan 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-17 09:32:48
 */
public interface ContractCenterPlanService  extends  OrionBaseService<ContractCenterPlan>  {


        /**
         *  详情
         *
         * * @param id
         */
    ContractCenterPlanVO detail(String id,String pageCode)throws Exception;

        /**
         *  新增
         *
         * * @param contractCenterPlanDTO
         */
        String create(ContractCenterPlanDTO contractCenterPlanDTO)throws Exception;

        /**
         *  编辑
         *
         * * @param contractCenterPlanDTO
         */
        Boolean edit(List<ContractCenterPlanDTO> contractCenterPlanDTOList)throws Exception;


        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;


        /**
         *  分页
         *
         * * @param pageRequest
         * 
         */
        Page<ContractCenterPlanVO> pages( Page<ContractCenterPlanDTO> pageRequest)throws Exception;

        /**
         *  下载模板
         *
         * * @param response
         */
        void downloadExcelTpl(HttpServletResponse response)throws Exception;

        /**
         *  导入校验
         *
         * * @param file
         */
        ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


        /**
           *  确认导入
           *
           * * @param importId
           */
        Boolean importByExcel(String importId)throws Exception;

        /**
       *  取消导入
       *
       * * @param importId
       */
        Boolean importCancelByExcel(String importId)throws Exception;

        /**
            *  导出
            *
            * * @param searchConditions
            * * @param response
            */
        void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

        /**
            *  设置各种名称
            *
            * * @param searchConditions
            * * @param response
            */
        void  setEveryName(List<ContractCenterPlanVO> vos)throws Exception;

        /**
        * 按中心展示中心用人计划
        * @return 结果
        */
        List<CenterDisPlayPlanVO>  planGroupByCenter(Integer year);

        /**
        * 按合同展示合同用人计划
        * @return 结果
        */
        List<CenterDisPlayPlanVO> planGroupByContract(Integer year);

        /**
        * 通过合同编号，用人单位代号，年份
        * @param contractCenterPlanListDTO 查询条件
        * @return 结果
        */
        List<ContractCenterPlanVO> planList(ContractCenterPlanListDTO contractCenterPlanListDTO);

        /**
        * 判断角色是否为技术配置人员或者财务人员
        * @return 结果 结果
        */
        Boolean roleJudge();

        /**
        * 审核计划
        * @param centerPlanApprovalDTO 参数
        */
        void approval(CenterPlanApprovalDTO centerPlanApprovalDTO);

    /**
     * 获取成本类型和单位
     * @return 结果
     */
    List<CostTypeUnitVO> getCostType();

    /**
     * 通过合同编号和年份获取合同下的部门
     * @param contractNumber 合同编号
     * @param year年份
     * @return 结果
     */
    List<Map<String, String>> getCenterHeads(String contractNumber, Integer year);

    /**
     * 导出顶层数据
     * @param exportDTO 参数
     * @param response 参数
     */
    void exportTopByExcel(List<TopCenterExportDTO> exportDTO, HttpServletResponse response) throws Exception;
}
