import Api from '/@/api';
const base = '/pms';
enum zApi {
  /* 项目详情 */
  projectItem = 'project/detail',
  /* 编辑详情 */
  editprojectItem = 'project/edit',
  /* 干系人分页 */
  stakeHolder = 'stakeholder/getPage',
  /* 删除干系人 */
  deletStakeHolder = 'stakeholder/removeBatch',
  /* 新增干系人 */
  addStakeHolder = 'stakeholder/save',
  /* 编辑干系人 */
  editStakeHolder = 'stakeholder/edit',
  /* 干系人联系方式 */
  getContactType = 'stakeholder/contactType',
  /* 获取角色分页 */
  getProjectRole = 'project-role/getPage',
  /* 获取角色 */
  addRole = 'project-role/save',
  /* 获取角色 */
  addAllRole = 'project-role/saveBatch',
  /* 移除角色 */
  deletRole = 'project-role/removeBatch',
  /* 编辑角色 */
  edittRole = 'project-role/edit',
  /* 启用禁用角色 */
  banRole = 'project-role/takeEffectBatch',
  /* 启用禁用角色 */
  getSystemRole = '/project-role/getSystemRole',
  /*
   *项目状态
   */
  /* 项目状态分页 */
  projectStatus = 'project-task-status/getPage',
  /* 新增项目状态 */
  addStatus = 'project-task-status/save',
  /* 删除项目状态 */
  deleteStatus = 'project-task-status/removeBatch',
  /* 编辑项目状态 */
  editStatus = 'project-task-status/edit',
  /* 编辑项目状态 */
  effectStatus = 'project-task-status/takeEffectBatch',
  /* 项目状态类型 */
  getStatus = 'project-task-status/statusType',
  /*
   *项目科目类型
   */
  /* 分页 */
  typePage = 'task-subject/getPage',
  /* 删 */
  deletType = 'task-subject/removeBatch',
  /* + */
  addType = 'task-subject/save',
  /* 编辑 */
  editType = 'task-subject/edit',
  /* 禁用 */
  effectType = 'task-subject/takeEffectBatch',
  /*
   *成员管理
   */
  /* 左侧下拉菜单 */
  peopleManege = 'project-role/getList',
  /* 成员table分页 */
  peoplePage = 'project-role-user/getPage',
  /* 新增成员 */
  addPeople = 'project-role-user/saveBatch',
  /* 删除成员 */
  deletePeople = 'project-role-user/removeBatch',
  /* 获取项目管理相关的角色列表 */
  projectRoleList='project-role/pms/role/list',
}

/**
 * @description: 项目设置
 */
// 获取项目详情
export function projectItemApi(id) {
  return new Api(base).fetch('', `${zApi.projectItem}/${id}/`, 'GET');
}
// 修改项目详情
export function editprojectItemApi(params) {
  return new Api(base).fetch(params, `${zApi.editprojectItem}/`, 'PUT');
}
/**
 * @description: 干系人
 */
// 干系人分页
export function getStakeHolderApi(params) {
  return new Api(base).fetch(params, `${zApi.stakeHolder}/`, 'POST');
}
// 删除干系人
export function deletStakeHolderApi(params) {
  return new Api(base).fetch(params, `${zApi.deletStakeHolder}/`, 'DELETE');
}
// 新增干系人
export function addStakeHolderApi(params) {
  return new Api(base).fetch(params, `${zApi.addStakeHolder}/`, 'POST');
}
// 编辑干系人
export function editStakeHolderApi(params) {
  return new Api(base).fetch(params, `${zApi.editStakeHolder}/`, 'PUT');
}
// 获取联系方式列表
export function getContactTypeApi() {
  return new Api(base).fetch('', `${zApi.getContactType}/`, 'GET');
}
/**
 * @description: 任务角色
 */
// 任务角色分页
export function getProjectRoleApi(params) {
  return new Api(base).fetch(params, `${zApi.getProjectRole}/`, 'POST');
}
// 新增  任务角色
export function addRoleApi(params) {
  const love = {
    name: params?.name,
    className: 'ProjectRole',
    moduleName: '项目管理-项目设置-项目角色',
    type: 'SAVE',
    remark: `新增了【${params?.name}】`,
  };
  return new Api(base).fetch(params, `${zApi.addRole}/`, 'POST');
}
// 批量新增  任务角色
export function addAllRoleApi(params) {
  return new Api(base).fetch(params, `${zApi.addAllRole}/`, 'POST');
}
// 删  任务角色
export function deletRoleApi(params) {
  return new Api(base).fetch(params, `${zApi.deletRole}/`, 'DELETE');
}
// 编辑  任务角色
export function edittRoleApi(params) {
  const love = {
    id: params?.id,
    name: params?.name,
    className: 'ProjectRole',
    moduleName: '项目管理-项目设置-项目角色',
    type: 'UPDATE',
    remark: `编辑了【${params?.id}】`,
  };
  return new Api(base).fetch(params, `${zApi.edittRole}/`, 'PUT');
}
// ban  任务角色
export function banRoleApi(params) {
  return new Api(base).fetch(params, `${zApi.banRole}/`, 'PUT');
}
// modal 通过名称模糊搜索系统角色
export function getSystemRoleApi(params) {
  return new Api(base).fetch('', `${zApi.getSystemRole}?name=${params}`, 'GET');
}
/**
 * @description: 任务状态
 */
// 状态分页
export function projectStatusApi(params) {
  return new Api(base).fetch(params, `${zApi.projectStatus}/`, 'POST');
}
// 新增
export function addStatusApi(params) {
  const love = {
    name: params?.name,
    className: 'ProjectTaskStatus',
    moduleName: '项目管理-项目设置-项目状态',
    type: 'SAVE',
    remark: `新增了【${params?.name}】`,
  };
  return new Api(base).fetch(params, `${zApi.addStatus}/`, 'POST');
}
// 删状态
export function deleteStatusApi(params) {
  return new Api(base).fetch(params, `${zApi.deleteStatus}/`, 'DELETE');
}
// 编辑转态
export function editStatusApi(params) {
  const love = {
    id: params?.id,
    name: params?.name,
    className: 'ProjectTaskStatus',
    moduleName: '项目管理-项目设置-项目状态',
    type: 'UPDATE',
    remark: `编辑了【${params?.id}】`,
  };
  return new Api(base).fetch(params, `${zApi.editStatus}/`, 'PUT');
}
// 状态类型
export function getStatusApi() {
  return new Api(base).fetch('', `${zApi.getStatus}/`, 'GET');
}
// 禁用启用状态
export function effectStatusApi(params) {
  return new Api(base).fetch(params, `${zApi.effectStatus}/`, 'PUT');
}
/**
 * @description: 任务类型
 */
// 状态分页
export function typePageApi(params) {
  return new Api(base).fetch(params, `${zApi.typePage}/`, 'POST');
}
// 删科目类型
export function deletTypeApi(params) {
  return new Api(base).fetch(params, `${zApi.deletType}/`, 'DELETE');
}
// +科目类型
export function addTypeApi(params) {
  return new Api(base).fetch(params, `${zApi.addType}/`, 'POST');
}
// 改类型
export function editTypeApi(params) {
  return new Api(base).fetch(params, `${zApi.editType}/`, 'PUT');
}
// 禁用类型
export function effectTypeApi(params) {
  return new Api(base).fetch(params, `${zApi.effectType}/`, 'PUT');
}
/**
 * @description: 成员管理分页
 */
// 获取菜单-左侧
export function peopleManegeApi(params) {
  return new Api(base).fetch('', `${zApi.peopleManege}/${params}/`, 'GET');
}
// 获取成员分页
export function peoplePageApi(params) {
  return new Api(base).fetch(params, `${zApi.peoplePage}/`, 'POST');
}
// 新增成员
export function addPeopleApi(params) {
  return new Api(base).fetch(params, `${zApi.addPeople}/`, 'POST');
}
// 删除成员
export function deletePeopleApi(params) {
  return new Api(base).fetch(params, `${zApi.deletePeople}/`, 'DELETE');
}
/* 获取项目管理相关的角色列表 */

export function projectRoleListApi(params) {
  return new Api(base).fetch(params, `${zApi.projectRoleList}`, 'GET');
}
