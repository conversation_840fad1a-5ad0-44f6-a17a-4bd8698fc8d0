package com.chinasie.orion.management.schedule;

import com.chinasie.orion.constant.ContractMilestoneNode;
import com.chinasie.orion.domain.entity.ContractMilestone;
import com.chinasie.orion.domain.entity.MarketContract;
import com.chinasie.orion.msc.api.MscBuildHandlerManager;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.repository.ContractMilestoneMapper;
import com.chinasie.orion.repository.MarketContractMapper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class MarketContractMilestoneXxlJob {

    @Autowired
    ContractMilestoneMapper contractMilestoneMapper;

    @Autowired
    MarketContractMapper marketContractMapper;

    @Autowired
    MscBuildHandlerManager mscBuildHandlerManager;

    @XxlJob("milestoneDeadlineXxlJob")
    public void milestoneDeadlineXxlJob() {
        LambdaQueryWrapperX<ContractMilestone> queryWrapper = new LambdaQueryWrapperX<>(ContractMilestone.class);
        queryWrapper.le(ContractMilestone::getPlanAcceptDate, LocalDate.now().plusDays(30).atStartOfDay(ZoneId.systemDefault()));
        List<ContractMilestone> contractMilestones = contractMilestoneMapper.selectList(queryWrapper);
        if (!contractMilestones.isEmpty()){
            Set<String> contractIds = contractMilestones.stream().map(ContractMilestone::getContractId).collect(Collectors.toSet());
            if (!contractIds.isEmpty()){
                List<String> ids = new ArrayList<>(contractIds);
                List<MarketContract> marketContracts = marketContractMapper.selectBatchIds(ids);
                Map<String, MarketContract> collect = marketContracts.stream().collect(Collectors.toMap(MarketContract::getId, contract -> contract));
                contractMilestones.forEach(contractMilestone -> {
                    mscBuildHandlerManager.send(contractMilestone, ContractMilestoneNode.NODE_MILESTONE_NOTIFY, collect.get(contractMilestone.getContractId()));
                });
            }

        }
    }

}
