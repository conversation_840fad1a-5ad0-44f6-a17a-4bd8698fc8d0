const ProjectFilterConfig = [
  {
    field: 'contractName',
    fieldName: '合同名称',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/projectInitiationWBS/getByProjectNumber',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'contractNumber',
    fieldName: '合同编号',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/projectInitiationWBS/getByProjectNumber',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
];

export default ProjectFilterConfig;
