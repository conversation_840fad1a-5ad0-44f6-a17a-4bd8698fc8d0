<template>
  <Layout3
    v-if="tabsOption?.length>0"
    :defaultActionId="actionId"
    :menuData="tabsOption"
    :projectData="projectInfo"
    :type="2"
    :onMenuChange="contentTabsChange2"
  >
    <template #header-title>
      <div class="layoutTtitle">
        <div class="nameStyle flex-te">
          {{ projectInfo.name }}
        </div>
        <div class="numberStyle">
          {{ projectInfo?.number }}
        </div>
      </div>
    </template>

    <template #header-right>
      <BasicTableAction
        :actions="actions"
        type="button"
      />
    </template>
    <template #footer>
      <WorkflowAction
        v-if="projectInfo.id"
        ref="processRef"
        :workflow-props="workflowProps"
      />
    </template>

    <DetailsTab
      v-if="actionId===9991"
      :details="projectInfo"
      @change="change"
    />
    <ContactTabs
      v-if="actionId===9992"
      :id="projectInfo.id"
    />

    <!--关联计划-->
    <ContactPlan
      v-if="actionId===999201"
      :id="projectInfo.id"
    />
    <!--关联问题-->
    <ContactQuestion
      v-if="actionId===999202"
      :id="projectInfo.id"
    />
    <!--关联文档-->
    <RiskContactDoc
      v-if="actionId===999203"
      :id="projectInfo.id"
    />
    <!--关联变更-->
    <ContactAssociated
      v-if="actionId===999204"
      :id="projectInfo.id"
      from="risk"
    />
    <WorkflowView
      v-if="actionId===9993"
      ref="processViewRef"
      :workflow-props="workflowProps"
    />
    <RelatedObjects
      v-if="actionId===9994"
      :relatedType="projectInfo.riskType"
    />

    <!--编辑弹窗-->
    <AddTableNode
      :treeData="treeData"
      @register="register"
    />
  </Layout3>
</template>

<script lang="ts">
import {
  computed,
  ComputedRef,
  defineComponent,
  getCurrentInstance,
  onMounted,
  provide,
  reactive,
  readonly,
  Ref,
  ref,
  toRefs,
  watchEffect,
} from 'vue';
import { WorkflowAction, WorkflowProps, WorkflowView } from 'lyra-workflow-component-vue3';
import {
  BasicTableAction, isPower, ITableActionItem, Layout3, useDrawer,
} from 'lyra-component-vue3';
import { useRoute } from 'vue-router';
import ContactTabs from './contactTabs/index.vue';
import DetailsTab from './DetailsTab/index.vue';
import Api from '/@/api';
import { RelatedObjects } from '../../RelatedObjects';
import AddTableNode from '../model/AddTableNode.vue';
import RiskContactDoc from './contactTabs/riskContactDoc/index.vue';
import ContactPlan from './contactTabs/contactPlan/index.vue';
import ContactQuestion from './contactTabs/contactQuestion/index.vue';
import ContactAssociated from '/@/views/pms/projectLaborer/projectLab/pages/projectPlanDetails/components/associatedChange/contactAssociated.vue';
import { setTitleByRootTabsKey } from '/@/utils';
import { renderNotAuthPage } from '/@/views/pms/utils';

export default defineComponent({
  // name: 'EndDetails',
  components: {
    BasicTableAction,
    AddTableNode,
    Layout3,
    DetailsTab,
    ContactTabs,
    RelatedObjects,
    WorkflowView,
    WorkflowAction,
    RiskContactDoc,
    ContactPlan,
    ContactQuestion,
    ContactAssociated,
  },
  setup() {
    const route = useRoute();
    const [register, { openDrawer }] = useDrawer();
    const processRef: Ref = ref();
    const processViewRef:Ref = ref();
    const powerData:Ref<any[]> = ref([]);
    provide('powerData', powerData);
    const state = reactive({
      tabsIndex: Number(route.query.type),
      id: route.query.itemId,
      projectId: '',
      projectInfo: {}as any,
      className: '',
      actionId: null,
      treeData: [],
      tabsOption: computed(() => [
        {
          name: '概述',
          id: 9991,
          isShow: isPower('PMS_FXGLXQ_container_01', powerData.value),
        },
        {
          name: '关联内容',
          id: 9992,
          isShow: isPower('PMS_FXGLXQ_container_02', powerData.value),
          children: [
            {
              name: '关联计划',
              id: 999201,
              isShow: isPower('PMS_FXGLXQ_container_02_01', powerData.value),
            },
            {
              name: '关联问题',
              id: 999202,
              isShow: isPower('PMS_FXGLXQ_container_02_02', powerData.value),
            },
            {
              name: '关联变更',
              id: 999204,
              isShow: true,
            },
            {
              name: '关联文档',
              id: 999203,
              isShow: isPower('PMS_FXGLXQ_container_02_03', powerData.value),
            },
          ].filter((item) => item.isShow),
        },
        {
          name: '流程',
          id: 9993,
          isShow: isPower('PMS_FXGLXQ_container_03', powerData.value),
        },
      ].filter((item) => item.isShow)),
    });
    const workflowProps:ComputedRef<WorkflowProps> = computed(() => ({
      Api,
      businessData: state.projectInfo,
      afterEvent: () => {
        processViewRef.value?.init();
      },
    }));

    // watchEffect(() => {
    //   if (!state.actionId) {
    //     state.actionId = tabsOption.value[0]?.id;
    //   }
    // });

    const currentInstance = getCurrentInstance();
    async function getDetails() {
      await new Api(`/pms/risk-management/detail/${state.id}`).fetch({
        pageCode: 'PMSRiskManagementDetails',
      }, '', 'GET').then((res:Record<string, any>) => {
        powerData.value = res?.detailAuthList || [];
        renderNotAuthPage({
          vm: currentInstance,
          powerData: powerData.value,
        });
        state.projectInfo = res;
        state.projectId = res.projectId;
        setTitleByRootTabsKey(route?.query?.rootTabsKey as string, res.name);
        processRef.value?.setProps({
          businessData: res,
        });
        if (!state.actionId) {
          state.actionId = 9991;
        }
      });
    }

    provide(
      'formData',
      computed(() => state.projectInfo),
    );
    onMounted(async () => {
      await getDetails();
      state.treeData = await new Api('/pas/risk-dir/tree').fetch('', '', 'GET');
    });
    function contentTabsChange2(index) {
      state.actionId = index.id;
    }
    /* 问题单条qusetionItemId和项目projectId */
    const riskItemId = ref(state.id);
    const projectId = ref(state.projectId);
    provide('riskItemId', readonly(riskItemId));
    provide('projectId', computed(() => state.projectId));
    provide(
      'projectInfo',
      computed(() => state.projectInfo),
    ); // 数据查询
    provide(
      'getForm',
      computed(() => getDetails),
    );
    function getDetailsx() {
      return state.projectInfo;
    }
    provide(
      'formData',
      computed(() => state.projectInfo),
    );
    provide(
      'getFormData',
      computed(() => getDetails),
    );
    provide('getDetails', getDetailsx);
    function change() {
      getDetails();
    }

    const actions: Ref<ITableActionItem<any>[]> = computed(() => [
      {
        text: '添加流程',
        icon: 'sie-icon-tianjiaxinzeng',
        isShow: () => processRef.value?.isAdd && isPower('PMS_FXGLXQ_container_04_button_02', powerData.value),
        onClick() {
          processRef.value?.onAddTemplate({
            messageUrl: route.fullPath,
          });
        },
      },
      {
        text: '编辑',
        isShow: () => isPower('PMS_FXGLXQ_container_04_button_01', powerData.value),
        icon: 'sie-icon-bianji',
        onClick() {
          openDrawer(true, {
            type: 'edit',
            data: { id: state.projectInfo?.id },
          });
        },
      },
    ].filter((item) => item));

    return {
      ...toRefs(state),
      contentTabsChange2,
      change,
      register,
      actions,
      processRef,
      processViewRef,
      workflowProps,
    };
  },
});
</script>
<style lang="less" scoped>
.layoutTtitle{
  width: 350px;
  padding: 5px ~`getPrefixVar('content-padding-left')`;
  .nameStyle{
    font-weight: 400;
    font-style: normal;
    color: #444B5E;
    font-size: 18px;
    height:29px;
    line-height: 29px;
  }
  .numberStyle{
    font-size: 12px;
    color: #969EB4;
  }
}
</style>
