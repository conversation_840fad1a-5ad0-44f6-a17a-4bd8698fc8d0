package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.FileInfoDTO;
import com.chinasie.orion.domain.dto.FileInfoQueryDTO;
import com.chinasie.orion.domain.entity.FileInfo;
import com.chinasie.orion.domain.vo.DocumentVO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.page.PageRequest;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/2/22 18:05
 */
public interface DocumentService {

    /**
     * 批量新增
     * @param fileInfoDTOList
     * @return
     * @throws Exception
     */
    List<String> saveBatchAdd(List<FileInfoDTO> fileInfoDTOList) throws Exception;

    /**
     * 保存文件关联信息
     * @param fileInfoDTOList
     * @return
     * @throws Exception
     */
    public boolean saveBatchDocument(List<FileInfoDTO> fileInfoDTOList) throws Exception;

    /**
     * 获取文件分页
     * @param pageRequest
     * @return
     * @throws Exception
     */
    Page<DocumentVO> getDocumentVOPage(Page<FileInfoDTO> pageRequest) throws Exception;

    /**
     * 文档分页(没有文档类型)
     * @param pageRequest
     * @return
     * @throws Exception
     */
    PageResult<DocumentVO> getDocumentPage(Page<FileInfoDTO> pageRequest) throws Exception;


    /**
     * 获取文件分页
     * @param pageRequest
     * @return
     * @throws Exception
     */
    PageResult<DocumentVO> getDocumentVOPage1(PageRequest<FileInfoDTO> pageRequest) throws Exception;

    /**
     * 通过dataid获取文件列表
     * @param dataId
     * @return
     * @throws Exception
     */
    List<DocumentVO> getDocumentList(String dataId, FileInfoQueryDTO fileInfoQueryDTO) throws Exception;

    /**
     * 批量新增文件
     * @param fileDtoList
     * @return
     * @throws Exception
     */
    Boolean updateBatchDocument(List<FileDTO> fileDtoList) throws Exception;

    /**
     * 新增文件
     * @param fileDto
     * @return
     * @throws Exception
     */
    Boolean updateDocument(FileDTO fileDto) throws Exception;

    /**
     * 批量删除文件
     * @param fileIdList
     * @return
     * @throws Exception
     */
    Boolean deleteBatchFile(List<String> fileIdList, String dataId) throws Exception;

    /**
     * 批量删除文件
     * @param fileInfoList
     * @return
     * @throws Exception
     */
    Boolean deleteBatchFileInfo(List<FileInfo> fileInfoList) throws Exception;

    /**
     * 批量删除文件
     * @param fileIdList
     * @return
     * @throws Exception
     */
    Boolean deleteBatchFile(List<String> fileIdList) throws Exception;


    /**
     * 删除文件关联信息
     * @param fileIdList
     * @return
     * @throws Exception
     */
    public Boolean deleteBatchDocument(List<String> fileIdList) throws Exception;

    List<DocumentVO> getDocumentListByIdList(List<String> idList) throws Exception;

    Boolean deleteBatchFileByDataIds(List<String> dataIds) throws Exception;


    Boolean fileCheckIn(FileDTO fileDto);

    Boolean fileMoveOut(FileDTO fileDto);

    String saveFileInfo(FileInfoDTO fileInfoDTO) throws Exception;

    public List<FileInfoDTO> getFileInfoList(String dataId) throws Exception;

    /**
     * 生成项目档案
     * @param projectId
     * @param templateId
     * @return
     * @throws Exception
     */
    Boolean generateDocument(String projectId, String templateId) throws Exception;
}
