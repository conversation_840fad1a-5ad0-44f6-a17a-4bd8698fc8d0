<template>
  <BasicDrawer
    :width="1000"
    v-bind="$attrs"
    :showFooter="false"
    :mask-closable="false"
    @register="register"
    @visible-change="visibleChange"
  >
    <!--    <SpinMain :loading="state.loading" />-->
    <ViewDrawerMain
      v-if="currentId"
      :id="currentId"
      ref="formRef"
      :record="state.detail"
      @loading-change="loadingChange"
    />
  </BasicDrawer>
</template>

<script setup lang="ts">
import { BasicDrawer, useDrawerInner } from 'lyra-component-vue3';
import {
  inject, reactive, ref, Ref,
} from 'vue';
import { useRoute } from 'vue-router';
import ViewDrawerMain from './viewDrawerMain.vue';
const [register, { closeDrawer, setDrawerProps }] = useDrawerInner(async (openProps) => {
  state.operationType = openProps?.operationType;
  state.record.detail = openProps?.detail;
  currentId.value = openProps.id;
  setDrawerProps({
    title: '查看页面',
  });
  state.visibleStatus = true;
});
const route = useRoute();
const formRef: Ref = ref();
const currentId = ref('');
const state = reactive({
  visibleStatus: false,
  record: {
    id: undefined,
    auditNumber: undefined,
    orderAndNodeParamDTOList: [],
    detail: {},
  },
  detail: {},
  loading: false,
  btnLoading: false,
  operationType: 'detail',
});

function visibleChange(visible: boolean) {
  if (visible === false) {
    currentId.value = null;
    closeDrawer();
  }
  // 窗口关闭时，设置状态值
  !visible && (state.visibleStatus = visible);
}

// 加载状态改变
function loadingChange(loading:boolean) {
  state.loading = loading;
}

// 获取订单确认详情

</script>

<style scoped>

</style>
