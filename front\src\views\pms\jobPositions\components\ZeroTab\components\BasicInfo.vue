<script setup lang="ts">
import { BasicCard } from 'lyra-component-vue3';
import {
  inject, reactive,
} from 'vue';

const detailsData: Record<string, any> = inject('detailsData', reactive({}));
const baseInfoProps = reactive({
  list: [
    {
      label: '岗位编码',
      field: 'number',
    },
    {
      label: '岗位名称',
      field: 'name',
    },
    {
      label: '所属机构',
      field: 'baseName',
    },
    {
      label: '授权时间（月）',
      field: 'authorizationTime',
    },
    {
      label: '授权指引',
      field: 'authorizationGuide',
      wrap: true,
      gridColumn: '1/5',
    },
  ],
  column: 4,
  dataSource: detailsData,
});
</script>

<template>
  <BasicCard
    title="基本信息"
    :grid-content-props="baseInfoProps"
    :isBorder="false"
  />
</template>

<style scoped lang="less">

</style>
