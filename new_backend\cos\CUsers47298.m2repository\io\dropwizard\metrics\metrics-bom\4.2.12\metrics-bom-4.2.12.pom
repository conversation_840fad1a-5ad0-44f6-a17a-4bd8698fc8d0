<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-parent</artifactId>
        <version>4.2.12</version>
    </parent>

    <artifactId>metrics-bom</artifactId>
    <name>Metrics BOM</name>
    <packaging>pom</packaging>
    <description>Bill of Materials for Metrics</description>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-annotation</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-caffeine</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-caffeine3</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-collectd</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-ehcache</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-graphite</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-healthchecks</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-httpclient</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-httpclient5</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-httpasyncclient</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-jakarta-servlet</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-jakarta-servlets</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-jcache</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-jdbi</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-jdbi3</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-jersey2</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-jersey3</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-jetty9</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-jetty10</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-jetty11</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-jmx</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-json</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-jvm</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-log4j2</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-logback</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-servlet</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-servlets</artifactId>
                <version>${project.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>
</project>
