<script setup lang="ts">
import { BasicButton, openModal } from 'lyra-component-vue3';
import {
  DatePicker, InputSearch, message, Modal,
} from 'ant-design-vue';
import {
  nextTick, onMounted, ref, Ref, unref,
} from 'vue';
import dayjs from 'dayjs';
import Api from '/@/api';
import TableView from './TableView.vue';

const props = defineProps<{
  type: 'person' | 'material' | 'personOverlap' | 'materialOverlap'
  repairRound: string
}>();

const config = {
  person: {
    api: 'person',
    vo: 'personSourceVOList',
    save: 'person',
  },
  material: {
    api: 'material',
    vo: 'materialSourceVOList',
    save: 'material',
  },
  personOverlap: {
    api: 'person/overlap',
    vo: 'personSourceVOList',
    save: 'person',
  },
  materialOverlap: {
    api: 'material/overlap',
    vo: 'materialSourceVOList',
    save: 'material',
  },
};

const year: Ref = ref(dayjs().format('YYYY'));

const legend = ref([
  ...(props.type.includes('Overlap') ? [
    {
      label: '重叠天数',
      color: '#FF928A',
      class: 'overlap',
      disabled: false,
    },
  ] : []),
  {
    label: '计划进出基地日期',
    color: '#B5C2DD',
    class: 'in-out',
    disabled: false,
  },
  {
    label: '计划作业开始结束日期',
    color: '#AFE8C5',
    class: 'job',
    disabled: false,
  },
  {
    label: '计划任务开始结束日期',
    color: '#60D38D',
    class: 'task',
    disabled: false,
  },
]);

const keyword: Ref<string> = ref();

// 当前季度索引
const quarterKey: Ref<number> = ref(0);
const quarterData: Ref<Record<string, any>[]> = ref([
  // {
  //   label: '第一季度',
  //   quarterNum: 1,
  // },
  // {
  //   label: '第二季度',
  //   quarterNum: 2,
  // },
  // {
  //   label: '第三季度',
  //   quarterNum: 3,
  // },
  // {
  //   label: '第四季度',
  //   quarterNum: 4,
  // },
]);

onMounted(async () => {
  await getData(quarterKey.value);
  updateQuarter();
});

function updateQuarter() {
  const index = quarterData.value?.findIndex((item) => {
    switch (props.type) {
      case 'person':
        return item.personCount > 0;
      case 'material':
        return item.materialCount > 0;
      case 'personOverlap':
      case 'materialOverlap':
        return item.overlapDayCount > 0;
    }
  });
  if (Math.max(0, index) !== quarterKey.value) {
    getData(Math.max(0, index));
  }
}

const data: Ref<any[]> = ref([]);
const minDate: Ref<number> = ref();
const loading: Ref<boolean> = ref(false);

async function getData(quarter: number) {
  loading.value = true;
  try {
    const result = await new Api(`/pms/resource-allocation/${config[props.type].api}/job/info`).fetch({
      keyword: unref(keyword),
      quarterNum: quarter + 1,
      repairRound: props?.repairRound,
      yearNum: year.value,
    }, '', 'POST');
    minDate.value = result?.minDate;
    quarterData.value = quarterData.value.map((item) => {
      const obj = result?.quarterCountVOList?.find((v) => v.quarterNum === item.quarterNum) || {};
      return {
        ...item,
        ...obj,
      };
    });
    data.value = result?.[config[props.type].vo] || [];
    if (quarterKey.value !== quarter) {
      quarterKey.value = quarter;
    }
  } finally {
    loading.value = false;
  }
}

async function changeDate() {
  if (!isEdit.value) {
    await getData(quarterKey.value);
    updateQuarter();
  } else {
    quarterKey.value = 0;
  }
}

async function handleChange(index: number) {
  if (!isEdit.value && quarterKey.value !== index) {
    await getData(index);
  } else {
    quarterKey.value = index;
    await nextTick();
    tableRef.value.quarterChange();
  }
}

const isEdit: Ref<boolean> = ref(false);

function changeEditMode(flag: boolean) {
  isEdit.value = flag;
}

const tableRef: Ref = ref();

function handleToolButton(type: string) {
  switch (type) {
    case 'edit':
      changeEditMode(true);
      break;
    case 'save':
      const editData = [];
      const data = tableRef.value.getData();
      data.forEach((item) => {
        item.inAndOutDateVOList.filter((v) => v.type === 1 || v.type === 0).forEach((v) => {
          editData.push({
            ...v,
            id: item.id,
          });
        });
      });
      if (editData.some((item) => item.statusText)) {
        return message.info('存在未设置的计划时间！');
      }
      Modal.confirm({
        title: '系统提示',
        content: '是否确认保存信息',
        async onOk() {
          if (editData.length) {
            loading.value = true;
            try {
              await new Api(`/pms/resource-allocation/${config[props.type].save}/job/work/save`).fetch(editData, '', 'POST');
              message.success('操作成功！');
              changeEditMode(false);
              getData(quarterKey.value);
            } finally {
              loading.value = false;
            }
          } else {
            changeEditMode(false);
          }
        },
      });
      break;
    case 'back':
      Modal.confirm({
        title: '系统提示',
        content: '是否确认放弃编辑内容',
        onOk() {
          changeEditMode(false);
          getData(quarterKey.value);
        },
      });
      break;
  }
}

function closeModal() {
  openModal.closeAll();
}
</script>

<template>
  <div
    v-loading="loading"
    class="table-calendar-container"
  >
    <div class="table-calendar-header">
      <div class="flex flex-ac">
        <BasicButton
          v-if="!isEdit && data.length"
          type="primary"
          @click="handleToolButton('edit')"
        >
          任务编辑
        </BasicButton>
        <template v-else-if="isEdit">
          <BasicButton
            type="primary"
            @click="handleToolButton('save')"
          >
            保存
          </BasicButton>
          <BasicButton
            @click="handleToolButton('back')"
          >
            返回
          </BasicButton>
        </template>
        <div class="legend-wrap">
          <div
            v-for="(item,index) in legend"
            :key="index"
            :class="{'disabled-legend':item.disabled}"
            @click="()=>item.disabled=!item.disabled"
          >
            <div
              :class="['color-block',item.class]"
              :style="{backgroundColor:item.color}"
            />
            <div class="label">
              {{ item.label }}
            </div>
          </div>
        </div>
        <DatePicker
          v-model:value="year"
          class="mr20 mla"
          style="width: 100px"
          picker="year"
          :allow-clear="false"
          value-format="YYYY"
          @change="changeDate"
        />
        <InputSearch
          v-model:value="keyword"
          placeholder="请输入关键字"
          style="width: 200px"
          @search="getData(quarterKey)"
        />
      </div>
      <div class="quarter">
        <div
          v-for="(item,index) in quarterData"
          :key="index"
          @click="handleChange(index)"
        >
          <div :class="['content',{active:index===quarterKey}]">
            <span class="label">{{ item.label }}：</span>
            <span
              v-if="type==='person'"
              class="value"
            >参修人员{{ item.personCount || 0 }}人</span>
            <span
              v-else-if="type==='material'"
              class="value"
            >参修物资{{ item.materialCount || 0 }}个</span>
            <span
              v-else-if="type==='personOverlap'"
              class="value"
            >重叠度{{ item.overlapDayCount || 0 }}天 重叠{{ item.overlapPersonCount || 0 }}人</span>
            <span
              v-else-if="type==='materialOverlap'"
              class="value"
            >重叠度{{ item.overlapDayCount || 0 }}天 重叠{{ item.overlapMaterialCount || 0 }}个</span>
          </div>

          <div
            v-if="index===0"
            :style="{transform:`translateX(calc(${100*quarterKey}% - ${2*(quarterKey+1)}px)`}"
            class="active-block"
          />
        </div>
      </div>
    </div>

    <TableView
      ref="tableRef"
      class="mt20"
      :quarter-key="quarterKey"
      :data="data"
      :type="type"
      :year="year"
      :legend="legend"
      :minDate="minDate"
      :repairRound="repairRound"
      :isEdit="isEdit"
      @close="closeModal"
    />
  </div>
</template>

<style scoped lang="less">
.table-calendar-container {
  position: relative;
  height: 100%;
}

.table-calendar-header {
  flex-shrink: 0;
}

.legend-wrap {
  margin-left: 30px;
  display: flex;
  align-items: center;

  > div {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;

    & + div {
      margin-left: 30px;
    }

    .color-block {
      position: relative;
      width: 15px;
      height: 15px;

      &.job::before {
        position: absolute;
        content: '';
        top: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: #60D38D;
      }
    }

    .label {
      margin-left: 10px;
    }

    &.disabled-legend {
      opacity: 0.3;
    }
  }
}

.quarter {
  // margin-top: 20px;
  // position: relative;
  // display: grid;
  // grid-template-columns: repeat(4, minmax(0, 1fr));
  // gap: 0 2px;
  // background-color: #4A7FFA;
  // border: 2px solid #4A7FFA;

  > div {
    position: relative;
    background-color: #fff;
    height: 50px;
    line-height: 50px;
    padding: 0 20px;
    cursor: pointer;

    .content {
      position: relative;
      z-index: 2;
      transition: all .3s;

      &.active {
        color: #fff;
        cursor: default;
      }
    }

    .label {
      font-weight: bold;
    }

    .active-block {
      position: absolute;
      width: 100%;
      height: 100%;
      padding: 0 2px;
      background-color: #4A7FFA;
      z-index: 1;
      top: 0;
      left: 0;
      transition: all .3s;
      box-sizing: content-box;

      &::before {
        position: absolute;
        content: '';
        bottom: 0;
        left: 20px;
        width: 15px;
        height: 10px;
        transform: translateY(100%);
        clip-path: polygon(0 0, 100% 0, 50% 100%);
        background-color: #4A7FFA;
        z-index: 1;
      }
    }
  }
}

.mla {
  margin-left: auto;
}
</style>
