package com.chinasie.orion.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/03/14:00
 * @description:
 */
@Data
@ApiModel(value = "WarningSettingDTO对象", description = "预警设置")
public class WarningSettingDTO extends ObjectDTO {

    /**
     * 频率
     */
    @ApiModelProperty(value = "频率")
    private String frequency;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 提醒种类
     */
    @ApiModelProperty(value = "提醒种类(day:提醒天数;percentage:提醒工期百分比)")
    private String warningCategory;


    /**
     * 提醒时间
     */
    @ApiModelProperty(value = "提醒时间")
    private String time;

    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private Integer takeEffect;

    /**
     * 提醒天数
     */
    @ApiModelProperty(value = "提醒天数")
    private Integer dayNum;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "预警方式")
    private String warningWay;
    @ApiModelProperty(value = "预警方式")
    private List<String> warningWayList;

    @ApiModelProperty(value = "提醒人")
    private List<String> roleList;

    @ApiModelProperty(value = "预警类型")
    private String warningType;

    /**
     * 字典表的值id
     */
    @ApiModelProperty(value = "字典表的值id")
    private String dictValueId;


}
