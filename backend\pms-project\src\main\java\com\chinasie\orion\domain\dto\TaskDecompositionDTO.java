package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.lang.Integer;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * TaskDecomposition DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-01 17:00:34
 */
@ApiModel(value = "TaskDecompositionDTO对象", description = "任务分解")
@Data
@ExcelIgnoreUnannotated
public class TaskDecompositionDTO extends  ObjectDTO   implements Serializable{

    /**
     * 父级id
     */
    @ApiModelProperty(value = "父级id")
    private String parentId;

    /**
     * 责任部门
     */
    @ApiModelProperty(value = "责任部门")
    @ExcelProperty(value = "责任部门 ", index = 0)
    private String rspDept;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    @ExcelProperty(value = "责任人 ", index = 1)
    private String rspUser;

    /**
     * 工期
     */
    @ApiModelProperty(value = "工期")
    @ExcelProperty(value = "工期 ", index = 2)
    private Integer workTime;

    /**
     * 处理实例
     */
    @ApiModelProperty(value = "处理实例")
    @ExcelProperty(value = "处理实例 ", index = 3)
    private String processInstances;

    /**
     * 处理对象
     */
    @ApiModelProperty(value = "处理对象")
    @ExcelProperty(value = "处理对象 ", index = 4)
    private String processObject;

    /**
     *  任务名称
     */
    @ApiModelProperty(value = " 任务名称")
    @ExcelProperty(value = " 任务名称 ", index = 5)
    private String name;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 6)
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @ExcelProperty(value = "主表ID ", index = 7)
    private String mainTableId;


    /**
     *  文档分解ID
     */
    @ApiModelProperty(value = "文档分解ID")
    private String documentDecompositionId;

}
