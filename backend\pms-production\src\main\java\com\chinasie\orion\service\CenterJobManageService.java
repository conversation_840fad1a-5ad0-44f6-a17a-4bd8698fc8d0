package com.chinasie.orion.service;





import com.chinasie.orion.bo.JobManageTreeBO;
import com.chinasie.orion.domain.dto.JobManageSelectDTO;
import com.chinasie.orion.domain.dto.job.NewJobMangeDTO;
import com.chinasie.orion.domain.entity.CenterJobManage;

import java.lang.String;
import java.util.List;


import com.chinasie.orion.domain.vo.JobManageTreeVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * CenterJobManage 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-14 10:35:27
 */
public interface CenterJobManageService  extends OrionBaseService<CenterJobManage> {

    /**
     * 获取工单树结构
     * @return 结果
     */
    List<JobManageTreeVO> treeList(JobManageSelectDTO jobManageSelectDTO);

    List<CenterJobManage> dealDataByNumberList(List<NewJobMangeDTO> jobNumberList,String repairRound);

    /**
     * 获取工单树结构
     * @return 结果
     */
    List<JobManageTreeVO> getTree(List<JobManageTreeBO> jobManageTreeParam);

    /**
     * 获取已选择的作业
     * @return 结果
     */
    List<JobManageTreeBO> usedTreeList(String repairOrgId, String keyword);

    /**
     *  批量修改状态
     * @param jobNumberList
     */
    void updateByNumberList(List<String> jobNumberList);
}
