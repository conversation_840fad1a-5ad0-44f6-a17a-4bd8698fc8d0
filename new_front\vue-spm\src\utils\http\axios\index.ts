// axios配置  可自行根据项目进行更改，只需更改该文件即可，其他文件可以不动
// The axios configuration can be changed according to the project, just change the file, other files can be left unchanged

import type { AxiosResponse } from 'axios';
import type { RequestOptions, Result } from './types';
import type { AxiosTransform, CreateAxiosOptions } from './axiosTransform';

import { VAxios } from './Axios';
import { checkStatus } from './checkStatus';

import { useGlobSetting } from '/@/hooks/setting';
import { useMessage } from '/@/hooks/web/useMessage';

import { ContentTypeEnum, RequestEnum, ResultEnum } from '/@/enums/httpEnum';

import { isArray, isObject, isString } from '/@/utils/is';
import { getToken } from '/@/utils/auth';
import { deepMerge, setObjToUrlParams, trimValues } from '/@/utils';
import { useErrorLogStoreWithOut } from '/@/store/modules/errorLog';

import { errorResult } from './const';
import { useI18n } from '/@/hooks/web/useI18n';
import { createNow, formatRequestDate } from './helper';

import { useUserStore } from '/@/store/modules/user';
import { useAppStoreWidthOut } from '/@/store/modules/app';
import { getQiankunProps } from '/@/utils/qiankun/useQiankun';

const globSetting = useGlobSetting();
const prefix = globSetting.urlPrefix;
const { createMessage, createErrorModal } = useMessage();

/**
 * @description: 数据处理，方便区分多种处理方式
 */
const transform: AxiosTransform = {
  /**
   * @description: 处理请求数据
   */
  transformRequestHook: async (res: AxiosResponse<Result>, options: RequestOptions) => {
    const appStore = useAppStoreWidthOut() as any;
    // tip提示过期时间
    const xOrionTip = res.headers['x-orion-tip'] || res.headers['X-ORION-TIP'] || '';
    appStore.setXOrionTip(decodeURIComponent(xOrionTip));

    const { t } = useI18n();
    const { isTransformRequestResult } = options;
    // 不进行任何处理，直接返回
    // 用于页面代码可能需要直接获取code，data，message这些信息时开启
    if (!isTransformRequestResult) {
      return res.data;
    }
    // 错误的时候返回
    const { data } = res;
    if (!data) {
      // return '[HTTP] Request has no return value';
      return errorResult;
    }

    // 如果是文件流下载，直接返回文件流
    if (res.config?.responseType === 'blob') {
      return res;
    }

    //  这里 code，result，message为 后台统一的字段，需要在 types.ts内修改为项目自己的接口返回格式
    const { code, result, message } = data;

    // 这里逻辑可以根据项目进行修改
    const userStore = useUserStore();
    if (code === ResultEnum.SUCCESS) {
      // 钩子验证token是否快要过期，重新获取
      // await userStore.refreshToken();
      // 系统配置的锁定退出登录
      // await lockLogoutTask();
      // 返回成功的数据
      return result;
    }
    // 登录超时,token过期
    if (code === ResultEnum.TIMEOUT) {
      userStore.logout();
    } else {
      // 其他错误
      if (message) {
        // errorMessageMode=‘modal’的时候会显示modal错误弹窗，而不是消息提示，用于一些比较重要的错误
        if (options.errorMessageMode === 'modal') {
          createErrorModal({
            title: t('sys.api.errorTip'),
            content: message,
          });
        } else if (options.errorMessageMode === 'message') {
          createMessage.destroy();
          createMessage.error(message);
        }
      }
      // 返回错误的原始数据
      return Promise.reject(data);
    }

    return errorResult;
  },

  // 请求之前处理config
  beforeRequestHook: (config, options) => {
    const {
      apiUrl, joinPrefix, joinParamsToUrl, formatDate, joinTime = true,
    } = options;

    if (joinPrefix) {
      config.url = `${prefix}${config.url}`;
    }

    if (apiUrl && isString(apiUrl)) {
      config.url = `${apiUrl}${config.url}`;
    }
    const params = config.params || {};
    if (isObject(params) || isArray(params)) {
      trimValues(params);
    }
    if (config.method?.toUpperCase() === RequestEnum.GET) {
      if (!isString(params)) {
        // 给 get 请求加上时间戳参数，避免从缓存中拿数据。
        config.params = Object.assign(params || {}, createNow(joinTime, false));
      } else {
        // 兼容restful风格
        config.url = `${config.url + params}${createNow(joinTime, true)}`;
        config.params = undefined;
      }
    } else if (!isString(params)) {
      formatDate && formatRequestDate(params);
      config.data = params;
      config.params = undefined;
      if (joinParamsToUrl) {
        config.url = setObjToUrlParams(config.url as string, config.data);
      }
    } else {
      // 兼容restful风格
      config.url += params;
      config.params = undefined;
    }
    return config;
  },

  /**
   * @description: 请求拦截器处理
   */
  requestInterceptors: (config) => {
    config.headers.client = getQiankunProps()?.client || 'orion';
    // 请求之前处理config
    const token = getToken();
    if (token) {
      // jwt token
      config.headers.Authorization = `Bearer ${token}`;
      const userStore = useUserStore();
      const { orgId, pId } = userStore.getOrgIdPId;
      orgId && (config.headers.orgId = orgId);
      pId && (config.headers.pId = pId);
    }
    // 日志传参
    const { operateLog } = config;
    if (operateLog) {
      config.headers.operateLog = encodeURIComponent(operateLog);
    }
    return config;
  },

  /**
   * @description: 响应错误处理
   */
  responseInterceptorsCatch: (error: any) => {
    const { t } = useI18n();
    const errorLogStore = useErrorLogStoreWithOut();
    errorLogStore.addAjaxErrorInfo(error);
    const { response, code, message } = error || {};
    const msg: string = response?.data?.error?.message ?? '';
    const err: string = error?.toString?.() ?? '';
    try {
      if (code === 'ECONNABORTED' && message.indexOf('timeout') !== -1) {
        createMessage.error(t('sys.api.apiTimeoutMessage'));
      }
      if (err?.includes('Network Error')) {
        createErrorModal({
          title: t('sys.api.networkException'),
          content: t('sys.api.networkExceptionMsg'),
        });
      }
    } catch (error) {
      throw new Error(error);
    }
    checkStatus(error?.response?.status, msg);
    return Promise.reject(error);
  },
};

function createAxios(opt?: Partial<CreateAxiosOptions>) {
  return new VAxios(
    deepMerge(
      {
        timeout: 100 * 1000,
        // 基础接口地址
        // baseURL: globSetting.apiUrl,
        // 接口可能会有通用的地址部分，可以统一抽取出来
        prefixUrl: prefix,
        headers: { 'Content-Type': ContentTypeEnum.JSON },
        // 如果是form-data格式
        // headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
        // 数据处理方式
        transform,
        // 配置项，下面的选项都可以在独立的接口请求中覆盖
        requestOptions: {
          // 默认将prefix 添加到url
          joinPrefix: true,
          // 需要对返回数据进行处理
          isTransformRequestResult: true,
          // post请求的时候添加参数到url
          joinParamsToUrl: false,
          // 格式化提交参数时间
          formatDate: true,
          // 消息提示类型
          errorMessageMode: 'message',
          // 接口地址
          apiUrl: globSetting.apiUrl,
          //  是否加入时间戳
          joinTime: true,
          // 忽略重复请求
          ignoreCancelToken: true,
        },
      },
      opt || {},
    ),
  );
}
export let defHttp = createAxios();

export function setHttp(createAxiosFn) {
  defHttp = createAxiosFn?.() ?? createAxios();
}
