package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/01/14:30
 * @description:
 */
@Data
@TableName(value = "pms_plan_to_demand_management")
@ApiModel(value = "DemandToPlan对象", description = "需求和计划的关系类")
public class PlanToDemandManagement implements Serializable {
    /**
     * 副Id  PlanToDemandManagement
     */
    @ApiModelProperty(value = "副Id")
    @TableField(value = "to_id")
    private String toId;

    /**
     * 顺序
     */
    @ApiModelProperty(value = "顺序")
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 修改人ID
     */
    @ApiModelProperty(value = "修改人ID")
    @TableField(value = "modify_id", fill = FieldFill.INSERT_UPDATE)
    private String modifyId;

    /**
     * 主id
     */
    @ApiModelProperty(value = "主id")
    @TableField(value = "from_id")
    private String fromId;

    /**
     * 创建者ID
     */
    @ApiModelProperty(value = "创建者ID")
    @TableField(value = "creator_id", fill = FieldFill.INSERT)
    private String creatorId;

    /**
     * 类名称
     */
    @ApiModelProperty(value = "类名称")
    @TableField(value = "class_name")
    private String className;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @TableId(
            type = IdType.ASSIGN_UUID
    )
    private String id;

    /**
     * 副类名
     */
    @ApiModelProperty(value = "副类名")
    @TableField(value = "to_class")
    private String toClass;

    /**
     * 主类名
     */
    @ApiModelProperty(value = "主类名")
    @TableField(value = "from_class")
    private String fromClass;


    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    @TableField(value = "status", fill = FieldFill.INSERT)
    private Integer status;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @TableField(value = "modify_time", fill = FieldFill.INSERT_UPDATE)
    private Date modifyTime;

}
