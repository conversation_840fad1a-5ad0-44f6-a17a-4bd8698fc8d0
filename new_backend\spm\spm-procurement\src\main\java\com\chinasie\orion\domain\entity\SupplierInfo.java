package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * SupplierInfo Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@TableName(value = "ncf_form_supplier_info")
@ApiModel(value = "SupplierInfoEntity对象", description = "供应商管理")
@Data

public class SupplierInfo extends ObjectEntity implements Serializable {

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @TableField(value = "supplier_number")
    private String supplierNumber;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @TableField(value = "name")
    private String name;

    /**
     * 供应商账号
     */
    @ApiModelProperty(value = "供应商账号")
    @TableField(value = "account")
    private String account;

    /**
     * 找回密码邮箱
     */
    @ApiModelProperty(value = "找回密码邮箱")
    @TableField(value = "find_pass_eamil")
    private String findPassEamil;

    /**
     * 供应商简称
     */
    @ApiModelProperty(value = "供应商简称")
    @TableField(value = "sim_name")
    private String simName;

    /**
     * 供应商英文名称
     */
    @ApiModelProperty(value = "供应商英文名称")
    @TableField(value = "e_name")
    private String eName;

    /**
     * 注册国家和地区
     */
    @ApiModelProperty(value = "注册国家和地区")
    @TableField(value = "reg_country_region")
    private String regCountryRegion;

    /**
     * 省
     */
    @ApiModelProperty(value = "省")
    @TableField(value = "province")
    private String province;

    /**
     * 城市
     */
    @ApiModelProperty(value = "城市")
    @TableField(value = "city")
    private String city;

    /**
     * 区/县
     */
    @ApiModelProperty(value = "区/县")
    @TableField(value = "county")
    private String county;

    /**
     * 注册地址
     */
    @ApiModelProperty(value = "注册地址")
    @TableField(value = "reg_address")
    private String regAddress;

    /**
     * 邮政编码
     */
    @ApiModelProperty(value = "邮政编码")
    @TableField(value = "ems_number")
    private String emsNumber;

    /**
     * 网址
     */
    @ApiModelProperty(value = "网址")
    @TableField(value = "url")
    private String url;

    /**
     * 固定电话
     */
    @ApiModelProperty(value = "固定电话")
    @TableField(value = "landline_phone")
    private String landlinePhone;

    /**
     * 分机
     */
    @ApiModelProperty(value = "分机")
    @TableField(value = "extension")
    private String extension;

    /**
     * 传真
     */
    @ApiModelProperty(value = "传真")
    @TableField(value = "fax")
    private String fax;

    /**
     * 组织类型
     */
    @ApiModelProperty(value = "组织类型")
    @TableField(value = "organization_type")
    private String organizationType;

    /**
     * 法人代表
     */
    @ApiModelProperty(value = "法人代表")
    @TableField(value = "legalrep")
    private String legalrep;

    /**
     * 企业性质
     */
    @ApiModelProperty(value = "企业性质")
    @TableField(value = "company_nature")
    private String companyNature;

    /**
     * 中广核集团参股或控股公司
     */
    @ApiModelProperty(value = "中广核集团参股或控股公司")
    @TableField(value = "zgh_child")
    private String zghChild;

    /**
     * 注册资金币种
     */
    @ApiModelProperty(value = "注册资金币种")
    @TableField(value = "capital_currency")
    private String capitalCurrency;

    /**
     * 注册资本（万）
     */
    @ApiModelProperty(value = "注册资本（万）")
    @TableField(value = "registered_capital")
    private BigDecimal registeredCapital;

    /**
     * 上级主管单位
     */
    @ApiModelProperty(value = "上级主管单位")
    @TableField(value = "parent_org")
    private String parentOrg;

    /**
     * 主要控股公司
     */
    @ApiModelProperty(value = "主要控股公司")
    @TableField(value = "major_shareholder")
    private String majorShareholder;

    /**
     * 可提供产品/服务
     */
    @ApiModelProperty(value = "可提供产品/服务")
    @TableField(value = "products_services")
    private String productsServices;

    /**
     * 可提供产品/服务文字描述
     */
    @ApiModelProperty(value = "可提供产品/服务文字描述")
    @TableField(value = "products_services_desc")
    private String productsServicesDesc;

    /**
     * 公司简介
     */
    @ApiModelProperty(value = "公司简介")
    @TableField(value = "company_overview")
    private String companyOverview;

    /**
     * 营业执照注册号/统一社会信用代码
     */
    @ApiModelProperty(value = "营业执照注册号/统一社会信用代码")
    @TableField(value = "business_license_num")
    private String businessLicenseNum;

    /**
     * 营业执照有效期起
     */
    @ApiModelProperty(value = "营业执照有效期起")
    @TableField(value = "business_license_start")
    private Date businessLicenseStart;

    /**
     * 营业执照有效期至
     */
    @ApiModelProperty(value = "营业执照有效期至")
    @TableField(value = "business_license_end")
    private Date businessLicenseEnd;

    /**
     * 经营范围
     */
    @ApiModelProperty(value = "经营范围")
    @TableField(value = "operation_scope")
    private String operationScope;

    /**
     * 是否推荐供应商
     */
    @ApiModelProperty(value = "是否推荐供应商")
    @TableField(value = "recommend_supplier")
    private String recommendSupplier;

    /**
     * 板块名称
     */
    @ApiModelProperty(value = "板块名称")
    @TableField(value = "sector_name")
    private String sectorName;

    /**
     * 供应商级别
     */
    @ApiModelProperty(value = "供应商级别")
    @TableField(value = "supplier_level")
    private String supplierLevel;

    /**
     * 资审有效期
     */
    @ApiModelProperty(value = "资审有效期")
    @TableField(value = "qual_validity")
    private Date qualValidity;

    /**
     * 采购品类
     */
    @ApiModelProperty(value = "采购品类")
    @TableField(value = "procurement_cat")
    private String procurementCat;

    /**
     * 采购品类编码
     */
    @ApiModelProperty(value = "采购品类编码")
    @TableField(value = "proc_cat_code")
    private String procCatCode;

    /**
     * 供货范围文本描述
     */
    @ApiModelProperty(value = "供货范围文本描述")
    @TableField(value = "delivery_scope_desc")
    private String deliveryScopeDesc;

    /**
     * 供应商分类
     */
    @ApiModelProperty(value = "供应商分类")
    @TableField(value = "supplier_type")
    private String supplierType;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 供应商类别
     */
    @ApiModelProperty(value = "供应商类别")
    @TableField(value = "supplier_category")
    private String supplierCategory;

    /**
     * 供应商缴费有效截止日期
     */
    @ApiModelProperty(value = "供应商缴费有效截止日期")
    @TableField(value = "payment_effective_deadline")
    private Date paymentEffectiveDeadline;

    /**
     * 二级公司编码
     */
    @ApiModelProperty(value = "二级公司编码")
    @TableField(value = "secondary_company_code")
    private String secondaryCompanyCode;

    /**
     * 供应商类别
     */
    @ApiModelProperty(value = "供应商类别")
    @TableField(value = "secondary_company_name")
    private String secondaryCompanyName;

    /**
     * 营业执照注册日期
     */
    @ApiModelProperty(value = "营业执照注册日期")
    @TableField(value = "license_reg_date")
    private Date licenseRegDate;

    /**
     * 营业执照有效日期
     */
    @ApiModelProperty(value = "营业执照有效日期")
    @TableField(value = "license_valid_date")
    private Date licenseValidDate;
}
