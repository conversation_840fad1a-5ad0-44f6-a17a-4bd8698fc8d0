package com.chinasie.orion.service.impl;


import com.chinasie.orion.domain.entity.StatusCount;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.StatusCountRepository;
import com.chinasie.orion.service.StatusCountService;
import org.springframework.stereotype.Service;


/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/23/11:30
 * @description:
 */
@Service
public class StatusCountServiceImpl extends OrionBaseServiceImpl<StatusCountRepository, StatusCount> implements StatusCountService {

//    @Resource
//    private PlanService planService;
//    @Resource
//    private DemandManagementService demandManagementService;
//    @Resource
//    private QuestionManagementService questionManagementService;
//    @Resource
//    private RiskManagementService riskManagementService;
//    @Resource
//    private DeliverableService deliverableService;
//    @Scheduled(cron = "0 0 0 1/1 ? ?")
//    public void saveStatusCount() throws Exception {
//        List<PlanDTO> planDTOList = planService.queryForList();
//        List<>
//    }

}
