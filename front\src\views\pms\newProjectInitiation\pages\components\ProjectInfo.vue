<script setup lang="ts">
import {
  h, inject, onMounted, reactive, ref, unref, watchEffect,
} from 'vue';
import { BasicCard, OrionTable, getDictByNumber } from 'lyra-component-vue3';
import { Select } from 'ant-design-vue';
import tool from 'lodash-es';
import dayjs from 'dayjs';
import { useRoute } from 'vue-router';
import { declarationData } from '../keys';
import { setBasicInfo } from '../../utils';
import Api from '/@/api';
import ProjectInfoFilterConfig from '../ProjectInfoFilterConfig';

const route = useRoute();
// 立项详情数据
const data = inject(declarationData);
const projectCode = ref(route.query.projectCode);
const tableRef = ref(null);
const showTypeOptions = ref([]);
// 立项基本信息
const baseInfoProps = reactive({
  list: setBasicInfo([
    {
      label: '立项编号',
      field: 'projectNumber',
    },
    {
      label: '立项名称',
      field: 'projectName',
    },
    {
      label: '项目标签',
      field: 'projectLabel',
    },
    {
      label: '项目类型',
      field: 'projectType',
    },
    {
      label: '项目立项时间',
      field: 'initiationTime',
      formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD HH:mm:ss') || '' : '--'),
    },
    {
      label: '发起日期',
      field: 'projectInitDate',
      formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD HH:mm:ss') || '' : '--'),
    },
    {
      label: '项目开始日期',
      field: 'projectStartDate',
      formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') || '' : '--'),
    },
    {
      label: '项目结束日期',
      field: 'projectEndDate',
      formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') || '' : '--'),
    },
    {
      label: '项目负责人',
      field: 'projectPerson',
    },
    {
      label: '承担中心',
      field: 'projectAssumeCenter',
    },
    {
      label: '工厂',
      field: 'companyCode',
    },
    {
      label: '项目的公司代码',
      field: 'projectCompanyCode',
    },
    {
      label: '项目公司名称',
      field: 'projectCompanyName',
    },
    {
      label: '委托方代码',
      field: 'clientOne',
    },
    {
      label: '中文客户名称1',
      field: 'clientOneName',
    },
    {
      label: '委托方代码2',
      field: 'clientTwo',
    },
    {
      label: '中文客户名称2',
      field: 'clientTwoName',
    },
    {
      label: '委托方代码3',
      field: 'clientThree',
    },
    {
      label: '中文客户名称3',
      field: 'clientThreeName',
    },
    {
      label: '核电委托方',
      field: 'nuclearClient',
    },
    {
      label: '火电委托方',
      field: 'fireClient',
    },
    {
      label: '风电委托方',
      field: 'windClient',
    },
    {
      label: '其他委托方',
      field: 'otherClient',
    },
    {
      label: '外部合同号',
      field: 'outContractNumber',
    },
  ]),
  column: 4,
  dataSource: {},
});
// WBS预算信息
const tableOptions = {
  showTableSetting: false,
  showToolButton: false,
  showSmallSearch: false,
  isSpacing: false,
  pagination: false,
  isFilter2: false,
  columns: [
    {
      title: '项目层次等级',
      dataIndex: 'projectLevel',
    },
    {
      title: '工作分解结构元素 (WBS 元素)',
      dataIndex: 'wbsElement',
    },
    {
      title: 'PS: 短描述 (第一行文本)',
      dataIndex: 'description',
    },
    {
      title: '业务分类',
      dataIndex: 'business',
      customRender({ record }) {
        const selectRef = ref();
        const filterOptionFn = (input: string, option: any) => option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        return h(Select, {
          style: {
            width: '100%',
          },
          value: record?.business,
          showSearch: true,
          allowClear: true,
          ref: selectRef,
          filterOption: filterOptionFn,
          options: showTypeOptions,
          onChange(v, row) {
            record.businessName = row?.name;
            record.business = v || '';
            onUpdateEdit(row, record);
          },
        });
      },
    },
    {
      title: '业务分类名称',
      dataIndex: 'businessName',
    },
    {
      title: '功能范围',
      dataIndex: 'functionalScope',
    },
    {
      title: '开头状态',
      dataIndex: 'initialStatus',
    },
    {
      title: '利润中心编码',
      dataIndex: 'profitCenterCode',
    },
    {
      title: '利润中心名称',
      dataIndex: 'profitCenterName',
    },
    {
      title: '工厂',
      dataIndex: 'company',
    },
    {
      title: '负责人编号',
      dataIndex: 'directorCode',
    },
    {
      title: '负责人姓名（项目管理者）',
      dataIndex: 'directorName',
    },
  ],
  api: (params) => new Api('/pms/projectInitiationWBS/getByProjectNumber').fetch({
    ...params,
    projectNumber: unref(projectCode),
  }, '', 'POST'),
};

function onUpdateEdit(row:any, record: any) {
  saveApi(row, record);
}

function saveApi(data: any, record: any) {
  let params = {
    id: record.id,
    business: data.value,
    businessName: data.name,
  };
  new Api('/pms/projectInitiationWBS/edit').fetch({
    ...params,
  }, '', 'PUT').then((res) => {
    if (res) {
      updateTable();
    }
  });
}

async function getBaseIncome() {
  try {
    const result = await getDictByNumber('cos_business_type');
    if (result) {
      showTypeOptions.value = result.map((item) => ({
        id: item.id,
        label: item.value,
        value: item.value,
        name: item.name,
      }));
    } else {
      showTypeOptions.value = [];
    }
  } catch (error) {
    showTypeOptions.value = [];
  }
}

function updateTable() {
  tableRef.value?.reload();
}

onMounted(() => {
  getBaseIncome();
});

watchEffect(() => {
  baseInfoProps.dataSource = tool.get(data.value, 'projectInitiation', {});
});
</script>

<template>
  <BasicCard
    title="立项基本信息"
    :grid-content-props="baseInfoProps"
    :isBorder="false"
  />
  <BasicCard
    title="WBS预算信息"
    :isBorder="false"
  >
    <div class="wbs-info-wrapper">
      <OrionTable
        ref="tableRef"
        :options="tableOptions"
      />
    </div>
  </BasicCard>
</template>

<style scoped lang="less">
:deep(.details-container-content){
  padding-bottom: 16px;
}
.wbs-info-wrapper{
  height: 400px;
  overflow: hidden;
}
</style>
