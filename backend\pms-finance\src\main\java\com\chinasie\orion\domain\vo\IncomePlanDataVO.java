package com.chinasie.orion.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.domain.dto.BillingAccountInformationDTO;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * IncomePlanData VO对象
 *
 * <AUTHOR>
 * @since 2024-09-29 19:19:47
 */
@ApiModel(value = "IncomePlanDataVO对象", description = "收入计划填报数据")
@Data
public class IncomePlanDataVO extends ObjectVO implements Serializable {

    /**
     * 合同ID
     */
    @ApiModelProperty(value = "合同ID")
    private String contractId;


    /**
     * 合同编码
     */
    @ApiModelProperty(value = "合同编码")
    private String contractNumber;


    /**
     * 合同里程碑id
     */
    @ApiModelProperty(value = "合同里程碑id")
    private String milestoneId;


    /**
     * 合同里程碑编码
     */
    @ApiModelProperty(value = "合同里程碑编码")
    private String milestoneNumber;


    /**
     * 甲方单位id
     */
    @ApiModelProperty(value = "甲方单位id")
    private String partyADeptId;



    @ApiModelProperty(value = "甲方合同号")
    private String partyAContractNumber;


    /**
     * 收入计划编码
     */
    @ApiModelProperty(value = "收入计划编码")
    private String number;


    /**
     * 锁定状态
     */
    @ApiModelProperty(value = "锁定状态")
    private String lockStatus;


    /**
     * 收入确认类型
     */
    @ApiModelProperty(value = "收入确认类型")
    private String incomeConfirmType;

    @ApiModelProperty(value = "收入确认类型名称")
    private String incomeConfirmTypeName;


    /**
     * 预计开票/暂估日期
     */
    @ApiModelProperty(value = "预计开票/暂估日期")
    private Date estimateInvoiceDate;


    /**
     * 税率
     */
    @ApiModelProperty(value = "税率")
    private String taxRate;


    /**
     * 本次暂估金额（价税合计）
     */
    @ApiModelProperty(value = "本次暂估金额（价税合计）")
    private BigDecimal estimateAmt;


    /**
     * 开票金额（价税合计）
     */
    @ApiModelProperty(value = "开票金额（价税合计）")
    private BigDecimal invAmt;


    /**
     * 作废发票合计（价税合计）
     */
    @ApiModelProperty(value = "作废发票合计（价税合计）")
    private BigDecimal cancelInvAmt;


    /**
     * 里程碑已暂估金额（价税合计）
     */
    @ApiModelProperty(value = "里程碑已暂估金额（价税合计）")
    private BigDecimal milestonEstimateAmt;


    /**
     * 本次冲销暂估金额（价税合计）
     */
    @ApiModelProperty(value = "本次冲销暂估金额（价税合计）")
    private BigDecimal writeOffAmt;


    /**
     * 里程碑已预收款开票金额（价税合计）
     */
    @ApiModelProperty(value = "里程碑已预收款开票金额（价税合计）")
    private BigDecimal milestonePrePaidInvAmt;


    /**
     * 预收款转收入金额（价税合计）
     */
    @ApiModelProperty(value = "预收款转收入金额（价税合计）")
    private BigDecimal advancePayIncomeAmt;


    /**
     * 本次收入计划金额（价税合计）
     */
    @ApiModelProperty(value = "本次收入计划金额（价税合计）")
    private BigDecimal incomePlanAmt;


    /**
     * 专业中心
     */
    @ApiModelProperty(value = "专业中心")
    private String expertiseCenter;

    @ApiModelProperty(value = "专业中心名称")
    private String expertiseCenterName;


    /**
     * 专业所
     */
    @ApiModelProperty(value = "专业所")
    private String expertiseStation;

    @ApiModelProperty(value = "专业所名称")
    private String expertiseStationName;


    /**
     * 项目编码
     */
    @ApiModelProperty(value = "项目编码")
    private String projectNumber;


    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;


    /**
     * 项目负责人
     */
    @ApiModelProperty(value = "项目负责人")
    private String projectRspUserId;

    @ApiModelProperty(value = "项目负责人名称")
    private String projectRspUserName;


    /**
     * 开票/收入确认公司
     */
    @ApiModelProperty(value = "开票/收入确认公司")
    private String billingCompany;


    /**
     * 集团内（基地）/外
     */
    @ApiModelProperty(value = "集团内（基地）/外")
    private String internalExternal;

    @ApiModelProperty(value = "集团内（基地）/外名称")
    private String internalExternalName;


    /**
     * 里程碑金额
     */
    @ApiModelProperty(value = "里程碑金额")
    private BigDecimal milestoneAmt;


    /**
     * 里程碑已开票收入金额（价税合计）
     */
    @ApiModelProperty(value = "里程碑已开票收入金额（价税合计）")
    private BigDecimal milestoneInvAmt;


    /**
     * 里程碑未开票金额（价税合计）
     */
    @ApiModelProperty(value = "里程碑未开票金额（价税合计）")
    private BigDecimal milestoneNoInvAmt;


    /**
     * 本次暂估金额（不含税）
     */
    @ApiModelProperty(value = "本次暂估金额（不含税）")
    private BigDecimal estimateAmtExTax;


    /**
     * 本次开票金额（不含税）
     */
    @ApiModelProperty(value = "本次开票金额（不含税）")
    private BigDecimal invAmtExTax;


    /**
     * 本次作废发票金额（不含税）
     */
    @ApiModelProperty(value = "本次作废发票金额（不含税）")
    private BigDecimal cancelInvAmtExTax;


    /**
     * 里程碑已暂估金额（不含税）
     */
    @ApiModelProperty(value = "里程碑已暂估金额（不含税）")
    private BigDecimal milestoneAmtExTax;


    /**
     * 本次冲销暂估金额（不含税）
     */
    @ApiModelProperty(value = "本次冲销暂估金额（不含税）")
    private BigDecimal writeOffAmtExTax;


    /**
     * 里程碑已预收款开票金额（不含税）
     */
    @ApiModelProperty(value = "里程碑已预收款开票金额（不含税）")
    private BigDecimal milestoneInvAmtExTax;


    /**
     * 预收款转收入金额（不含税）
     */
    @ApiModelProperty(value = "预收款转收入金额（不含税）")
    private BigDecimal advPayIncomeAmtExTax;


    /**
     * 本月是否申报收入计划
     */
    @ApiModelProperty(value = "本月是否申报收入计划")
    private String isRevenue;


    /**
     * 不申报收入计划原因
     */
    @ApiModelProperty(value = "不申报收入计划原因")
    private String noRevenuePlanReason;


    /**
     * 其他说明
     */
    @ApiModelProperty(value = "其他说明")
    private String otherNotes;


    /**
     * 数据版本
     */
    @ApiModelProperty(value = "数据版本")
    private String dataVersion;


    /**
     * 数据来源
     */
    @ApiModelProperty(value = "数据来源")
    private String dataSource;


    /**
     * 收入计划填报Id
     */
    @ApiModelProperty(value = "收入计划填报Id")
    private String incomePlanId;

    @ApiModelProperty(value = "风险状态")
    private String riskType;

    @ApiModelProperty(value = "风险状态名称")
    private String riskTypeName;

    @ApiModelProperty(value = "修改收入计划原因")
    private String changeReason;

    @ApiModelProperty(value = "修改收入计划原因名称")
    private String changeReasonName;

    @ApiModelProperty(value = "风险环节")
    private String riskLink;

    @ApiModelProperty(value = "风险环节名称")
    private String riskLinkName;


    @ApiModelProperty(value = "合同名称")
    private String contractName;

    @ApiModelProperty(value = "合同里程碑名称")
    private String milestoneName;

    @ApiModelProperty(value = "是否调整")
    private String isAdjustment;

    @ApiModelProperty(value = "编制ID")
    private String compileId;

    @ApiModelProperty(value = "税率名称")
    private String taxRateName;

    @ApiModelProperty(value = "甲方单位名称")
    private String partyADeptIdName;

    @ApiModelProperty(value = "是否多项目")
    private String isMultipleProjects;

    @ApiModelProperty(value = "是否修改")
    private String isChange;


    @ApiModelProperty(value = "电厂合同编号")
    private String powerContractCode;

    @ApiModelProperty(value = "开票/收入确认公司名称")
    private String billingCompanyName;

    @ApiModelProperty(value = "锁定状态名称")
    private String lockStatusName;

    @ApiModelProperty(value = "状态名称")
    private String statusName;

    @ApiModelProperty(value = "“开票流程中”、“对账流程中”、“已完成”时是否允许修改")
    private String isUpdate;



    @ApiModelProperty(value = "验收日期")
    private String acceptanceDate;

    @ApiModelProperty(value = "多项目/税率")
    private List<BillingAccountInformationVO> billingAccountInformationVOS;

    @ApiModelProperty(value = "所属行业")
    private String industry;


    @ApiModelProperty(value = "所属行业名称")
    private String industryName;

    @ApiModelProperty(value = "收入wbs")
    private String incomeWbsNumber;
}
