package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;

/**
 * ProjectPlanTypeTypeAttributeValue VO对象
 *
 * <AUTHOR>
 * @since 2024-03-26 10:54:06
 */
@ApiModel(value = "ProjectPlanTypeTypeAttributeValueVO对象", description = "项目类型属性值")
@Data
public class ProjectPlanTypeAttributeValueVO extends ObjectVO implements Serializable{

            /**
         * imageId
         */
        @ApiModelProperty(value = "imageId")
        private String imageId;

        /**
         * 编码
         */
        @ApiModelProperty(value = "编码")
        private String number;

        /**
         * typeId
         */
        @ApiModelProperty(value = "typeId")
        private String typeId;

        /**
         * 名称
         */
        @ApiModelProperty(value = "名称")
        private String name;

        /**
         * 排序
         */
        @ApiModelProperty(value = "排序")
        private Integer sort;

        /**
         * attributeId
         */
        @ApiModelProperty(value = "attributeId")
        private String attributeId;

        /**
         * 属性值
         */
        @ApiModelProperty(value = "属性值")
        private String value;

        /**
         * code
         */
        @ApiModelProperty(value = "code")
        private String code;

    }
