<template>
  <div class="searchModal">
    <BasicDrawer
      v-model:visible="visible"
      :title="title"
      placement="right"
      width="340"
      class="searchModalDrawer pdmRightDrawer"
      :mask-closable="false"
      @close="x"
    >
      <div class="search_title mb15">
        <aInputSearch
          v-model:value="nameValue"
          placeholder="请输入编号或标题"
          size="large"
          @search="searchData"
        />
      </div>
      <basicTitle :title="'筛选属性'">
        <div class="rowItem">
          <div class="rowItem_label">
            优先级:
          </div>
          <a-select
            v-model:value="FormData.priorityLevel"
            size="large"
            placeholder="请选择优先级"
          >
            <a-select-option
              v-for="(item, index) in priorityLevel"
              :key="index"
              :value="item.id"
            >
              {{
                item.name
              }}
            </a-select-option>
          </a-select>
        </div>
        <div class="rowItem">
          <div class="rowItem_label">
            状态:
          </div>
          <a-select
            v-model:value="FormData.status"
            size="large"
            placeholder="请选择状态"
          >
            <a-select-option
              v-for="(item, index) in statusList"
              :key="index"
              :value="item.value"
            >
              {{
                item.label
              }}
            </a-select-option>
          </a-select>
        </div>
        <!-- <div class="rowItem">
          <div class="rowItem_label">状态</div>
          <a-select v-model:value="FormData.status" size="large" placeholder="请选择状态">
            <a-select-option :value="item.id" v-for="(item, index) in status" :key="index">{{
              item.name
            }}</a-select-option>
          </a-select>
        </div> -->
        <div class="rowItem">
          <div class="rowItem_label">
            提出日期:
          </div>
          <RangePicker
            v-model:value="time.proposedTime"
            size="large"
            allow-clear
            :placeholder="['年 / 月 / 日', '年 / 月 / 日']"
            default-value=""
            :show-time="{
              hideDisabledOptions: true,
              defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('23:59:59', 'HH:mm:ss')]
            }"
          />
        </div>
        <div class="rowItem">
          <div class="rowItem_label">
            期望完成日期:
          </div>
          <RangePicker
            v-model:value="time.predictEndTime"
            size="large"
            allow-clear
            :placeholder="['年 / 月 / 日', '年 / 月 / 日']"
            :show-time="{
              hideDisabledOptions: true,
              defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('23:59:59', 'HH:mm:ss')]
            }"
          />
        </div>
        <div class="nodeItemBtn">
          <a-button
            size="large"
            class="cancelBtn"
            @click="close"
          >
            取消
          </a-button>
          <a-button
            size="large"
            class="bgDC"
            type="primary"
            @click="onSubmit"
          >
            确认
          </a-button>
        </div>
      </basicTitle>
    </BasicDrawer>
  </div>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, ref, watch, onMounted,
} from 'vue';
import {
  message, Drawer, Select, Input, DatePicker, Button,
} from 'ant-design-vue';
import basicTitle from '/@/views/pms/projectLaborer/componentsList/basicTitle/index.vue';
import dayjs from 'dayjs';
import { priorityLevelApi, statusDemandApi } from '/@/views/pms/projectLaborer/api/demandManagement';
import moment, { Moment } from 'moment';
// import BasicDrawer from "/@/components/Drawer/src/BasicDrawer.vue";
import { BasicDrawer } from 'lyra-component-vue3';
import Api from '/@/api';
export default defineComponent({
  components: {
    BasicDrawer,
    basicTitle,
    aDrawer: Drawer,
    aSelect: Select,
    ASelectOption: Select.Option,
    aInputSearch: Input.Search,
    RangePicker: DatePicker.RangePicker,
    AButton: Button,
  },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const state = reactive({
      time: <any>{
        /* 开始 */
        proposedTime: [],
        /* 结束 */
        predictEndTime: [],
      },
      FormData: <any>{
        status: undefined,
        priorityLevel: undefined,
      },
      visible: false,
      title: '搜索',
      nameValue: '',
      priorityLevel: [],
      statusList: [],
      // status: []
    });
    watch(
      () => props.data,
      () => {
        state.visible = true;
        priorityLevelApi()
          .then((res) => {
            state.priorityLevel = res;
          })
          .catch((err) => {
            console.log(err);
          });
        new Api('/pmi/data-status/policy?policyId=txf7885e028d303342c28dd6a380b72a7d46').fetch('', '', 'GET').then((res) => {
          if (res?.length > 0) {
            state.statusList = res.map((item) => ({
              label: item.name,
              value: item.statusValue,
            }));
          }
        });
        //   statusDemandApi()
        //     .then((res) => {
        //       state.status = res;
        //     })
        //     .catch((err) => {
        //       console.log(err);
        //     });
      },
    );
    /* x按钮 */
    const x = () => {
      state.FormData = {};
      state.time = {};
      state.nameValue = '';
    };
      /* 取消 */
    const close = () => {
      state.visible = false;
      state.FormData = {};
      state.time = {};
      state.nameValue = '';
    };
    const onSubmit = () => {
      state.visible = false;
      let params = {};
      for (const item in state.FormData) {
        params[item] = state.FormData[item];
      }
      let queryConditionArr = [];
      queryConditionArr = <any>[
        {
          column: 'name',
          type: 'like',
          link: 'or',
          value: state.nameValue,
        },
        {
          column: 'number',
          type: 'like',
          link: 'or',
          value: state.nameValue,
        },
        {
          column: 'proposedTime',
          type: 'bt',
          link: 'and',
          value: [
            state.time.proposedTime[0]?.$d
              ? dayjs(dayjs(state.time.proposedTime[0]?.$d).format('YYYY-MM-DD HH:mm:ss')).unix() * 1000
              : '',
            state.time.proposedTime[1]?.$d
              ? dayjs(dayjs(state.time.proposedTime[1]?.$d).format('YYYY-MM-DD HH:mm:ss')).unix() * 1000
              : '',
          ],
        },
        {
          column: 'predictEndTime',
          type: 'bt',
          link: 'and',
          value: [
            state.time.predictEndTime[0]?.$d
              ? dayjs(dayjs(state.time.predictEndTime[0]?.$d).format('YYYY-MM-DD HH:mm:ss')).unix() * 1000
              : '',
            state.time.predictEndTime[1]?.$d
              ? dayjs(dayjs(state.time.predictEndTime[1]?.$d).format('YYYY-MM-DD HH:mm:ss')).unix() * 1000
              : '',
          ],
        },
      ];
      const queryCondition = queryConditionArr.filter((item, index) => {
        if (item.value === '') {
          delete queryConditionArr[index];
        } else {
          return item.value[0] !== '';
        }
      });

      emit('search', {
        params,
        queryCondition,
      });
      state.FormData = {};
      state.time = {
        /* 开始 */
        proposedTime: [],
        /* 结束 */
        predictEndTime: [],
      };
      state.nameValue = '';
    };
    const searchData = () => {
      onSubmit();
    };

    return {
      ...toRefs(state),
      close,
      onSubmit,
      searchData,
      x,
      dayjs,
      moment,
    };
  },
});
</script>
<style lang="less" scoped>
.nodeForm {
  padding: 10px 10px 80px 10px;
}
.ant-form-item{
  display: block;
}
.nextCheck {
  height: 40px;
  line-height: 40px;
}
.nodeItemBtn {
  position: fixed;
  bottom: 0px;
  padding: 20px 0;
  text-align: center;
  width: 280px;
  height: 80px;
  background: #ffffff;
  margin-bottom: 0px;
}
.cancelBtn {
  color: #5172dc;
  background: #5172dc19;
  width: 110px;
  border-radius: 4px;
}
.bgDC {
  width: 110px;
  margin-left: 15px;
  border-radius: 4px;
}
.rowItem {
  margin-bottom: 10px;
  .rowItem_label {
    padding-left: 5px;
    color: #444b5e;
  }
  .ant-select {
    width: 100%;
  }
}
  //.searchModalDrawer {
  //  .ant-drawer-body {
  //    padding: 60px 0px 80px 0px !important;
  //  }
  //  .search_title {
  //    padding: 10px 0px;
  //    border-bottom: 1px solid #d2d7e1;
  //    text-align: center;
  //    margin-bottom: 10px;
  //    .ant-input-search {
  //      width: 310px;
  //    }
  //  }
  //  .basicTitle {
  //    padding: 0px 15px;
  //  }
  //  .rowItem {
  //    margin-bottom: 10px;
  //    .rowItem_label {
  //      padding-left: 5px;
  //      color: #444b5e;
  //    }
  //    .ant-select {
  //      width: 100%;
  //    }
  //  }
  //
  //  .nodeItemBtn {
  //    position: fixed;
  //    bottom: 0px;
  //    padding: 20px 0px;
  //    text-align: center;
  //    width: 310px;
  //    height: 80px;
  //    background: #ffffff;
  //    margin-bottom: 0px;
  //    text-align: center;
  //  }
  //
  //  .cancelBtn {
  //    color: #5172dc;
  //    background: #5172dc19;
  //    width: 120px;
  //    border-radius: 4px;
  //  }
  //  .bgDC {
  //    width: 120px;
  //    margin-left: 15px;
  //    border-radius: 4px;
  //  }
  //}

</style>
