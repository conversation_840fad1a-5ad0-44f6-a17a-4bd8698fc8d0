#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import logging
import json

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MarketCrawler:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.setup_driver()

    def setup_driver(self):
        """设置Chrome驱动"""
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--allow-running-insecure-content')

        # 使用当前目录下的chromedriver
        service = Service('./chromedriver')
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        self.wait = WebDriverWait(self.driver, 15)

    def login(self, username, password):
        """登录系统"""
        try:
            self.driver.get("http://183.136.206.207:44099/login")
            time.sleep(5)

            # 等待页面完全加载
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))

            # 尝试多种方式查找用户名输入框
            username_selectors = [
                (By.NAME, "username"),
                (By.ID, "username"),
                (By.XPATH, "//input[@placeholder='用户名']"),
                (By.XPATH, "//input[@placeholder='请输入用户名']"),
                (By.XPATH, "//input[contains(@class, 'username')]"),
                (By.XPATH, "//input[@type='text']")
            ]

            username_input = None
            for selector in username_selectors:
                try:
                    username_input = self.driver.find_element(*selector)
                    if username_input.is_displayed():
                        break
                except:
                    continue

            if not username_input:
                logger.error("找不到用户名输入框")
                return False

            username_input.clear()
            username_input.send_keys(username)
            time.sleep(1)

            # 尝试多种方式查找密码输入框
            password_selectors = [
                (By.NAME, "password"),
                (By.ID, "password"),
                (By.XPATH, "//input[@placeholder='密码']"),
                (By.XPATH, "//input[@placeholder='请输入密码']"),
                (By.XPATH, "//input[contains(@class, 'password')]"),
                (By.XPATH, "//input[@type='password']")
            ]

            password_input = None
            for selector in password_selectors:
                try:
                    password_input = self.driver.find_element(*selector)
                    if password_input.is_displayed():
                        break
                except:
                    continue

            if not password_input:
                logger.error("找不到密码输入框")
                return False

            password_input.clear()
            password_input.send_keys(password)
            time.sleep(1)

            # 尝试多种方式查找登录按钮
            login_selectors = [
                (By.XPATH, "//button[contains(text(), '登录')]"),
                (By.XPATH, "//button[contains(text(), '登 录')]"),
                (By.XPATH, "//button[contains(text(), 'Login')]"),
                (By.XPATH, "//input[@type='submit']"),
                (By.XPATH, "//button[@type='submit']"),
                (By.XPATH, "//button[contains(@class, 'login')]")
            ]

            login_button = None
            for selector in login_selectors:
                try:
                    login_button = self.driver.find_element(*selector)
                    if login_button.is_displayed():
                        break
                except:
                    continue

            if not login_button:
                logger.error("找不到登录按钮")
                return False

            login_button.click()

            # 等待登录成功，检查是否跳转到主页
            time.sleep(8)

            # 检查是否登录成功
            current_url = self.driver.current_url
            if "login" not in current_url.lower():
                logger.info("登录成功")
                return True
            else:
                logger.error("登录失败，仍在登录页面")
                return False

        except Exception as e:
            logger.error(f"登录失败: {e}")
            return False

    def take_screenshot(self, filename):
        """截图"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(filename), exist_ok=True)

            # 滚动到页面顶部
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(1)

            self.driver.save_screenshot(filename)
            logger.info(f"截图保存: {filename}")
        except Exception as e:
            logger.error(f"截图失败: {e}")

    def navigate_to_market_module(self):
        """导航到市场经营模块"""
        try:
            # 等待页面加载
            time.sleep(5)

            # 尝试多种方式查找市场经营菜单
            market_selectors = [
                (By.XPATH, "//span[contains(text(), '市场经营')]"),
                (By.XPATH, "//a[contains(text(), '市场经营')]"),
                (By.XPATH, "//div[contains(text(), '市场经营')]"),
                (By.XPATH, "//*[contains(text(), '市场经营')]")
            ]

            market_menu = None
            for selector in market_selectors:
                try:
                    elements = self.driver.find_elements(*selector)
                    for element in elements:
                        if element.is_displayed():
                            market_menu = element
                            break
                    if market_menu:
                        break
                except:
                    continue

            if not market_menu:
                logger.error("找不到市场经营菜单")
                return False

            market_menu.click()
            time.sleep(3)

            logger.info("成功导航到市场经营模块")
            return True

        except Exception as e:
            logger.error(f"导航到市场经营模块失败: {e}")
            return False

    def crawl_market_pages(self):
        """爬取市场经营模块的所有页面"""
        pages_info = []

        # 市场经营模块的页面列表
        market_pages = [
            {"name": "市场经营一览", "xpath": "//span[contains(text(), '市场经营一览')]"},
            {"name": "线索池", "xpath": "//span[contains(text(), '线索池')]"},
            {"name": "线索录入", "xpath": "//span[contains(text(), '线索录入')]"},
            {"name": "需求管理", "xpath": "//span[contains(text(), '需求管理')]"},
            {"name": "报价管理", "xpath": "//span[contains(text(), '报价管理')]"},
            {"name": "合同管理", "xpath": "//span[contains(text(), '合同管理')]"},
            {"name": "客户管理", "xpath": "//span[contains(text(), '客户管理')]"},
            {"name": "里程碑管理", "xpath": "//span[contains(text(), '里程碑管理')]"},
            {"name": "我的草稿", "xpath": "//span[contains(text(), '我的草稿')]"},
            {"name": "帮助中心", "xpath": "//span[contains(text(), '帮助中心')]"},
            {"name": "需求报表", "xpath": "//span[contains(text(), '需求报表')]"},
            {"name": "报价报表", "xpath": "//span[contains(text(), '报价报表')]"},
            {"name": "合同报表", "xpath": "//span[contains(text(), '合同报表')]"},
            {"name": "里程碑报表", "xpath": "//span[contains(text(), '里程碑报表')]"},
        ]

        for page in market_pages:
            try:
                logger.info(f"正在访问页面: {page['name']}")

                # 点击菜单项
                menu_item = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, page['xpath']))
                )
                menu_item.click()
                time.sleep(3)

                # 截图
                screenshot_path = f"./docs/images/front_system/市场经营/市场经营_{page['name']}.png"
                self.take_screenshot(screenshot_path)

                # 获取页面信息
                page_info = self.analyze_page(page['name'])
                pages_info.append(page_info)

                time.sleep(2)

            except Exception as e:
                logger.error(f"访问页面 {page['name']} 失败: {e}")
                continue

        return pages_info

    def analyze_page(self, page_name):
        """分析页面结构和组件"""
        page_info = {
            "name": page_name,
            "url": self.driver.current_url,
            "components": [],
            "buttons": [],
            "forms": [],
            "tables": [],
            "inputs": []
        }

        try:
            # 分析按钮
            buttons = self.driver.find_elements(By.TAG_NAME, "button")
            for button in buttons:
                if button.is_displayed() and button.text.strip():
                    page_info["buttons"].append(button.text.strip())

            # 分析表单
            forms = self.driver.find_elements(By.TAG_NAME, "form")
            page_info["forms"] = [f"表单{i+1}" for i in range(len(forms))]

            # 分析表格
            tables = self.driver.find_elements(By.TAG_NAME, "table")
            page_info["tables"] = [f"表格{i+1}" for i in range(len(tables))]

            # 分析输入框
            inputs = self.driver.find_elements(By.TAG_NAME, "input")
            input_types = []
            for input_elem in inputs:
                if input_elem.is_displayed():
                    input_type = input_elem.get_attribute("type") or "text"
                    placeholder = input_elem.get_attribute("placeholder") or ""
                    if placeholder:
                        input_types.append(f"{input_type}({placeholder})")
                    else:
                        input_types.append(input_type)
            page_info["inputs"] = input_types

        except Exception as e:
            logger.error(f"分析页面 {page_name} 失败: {e}")

        return page_info

    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()

def main():
    crawler = MarketCrawler()

    try:
        # 登录
        if crawler.login("heqiang", "Abc123456789!"):
            # 导航到市场经营模块
            if crawler.navigate_to_market_module():
                # 爬取所有页面
                pages_info = crawler.crawl_market_pages()

                # 保存页面信息到JSON文件
                with open("./docs/market_pages_info.json", "w", encoding="utf-8") as f:
                    json.dump(pages_info, f, ensure_ascii=False, indent=2)

                # 输出页面信息
                for page in pages_info:
                    logger.info(f"页面: {page['name']}")
                    logger.info(f"URL: {page['url']}")
                    logger.info(f"按钮: {page['buttons']}")
                    logger.info(f"表单: {page['forms']}")
                    logger.info(f"表格: {page['tables']}")
                    logger.info(f"输入框: {page['inputs']}")
                    logger.info("-" * 50)

                return pages_info

    finally:
        crawler.close()

if __name__ == "__main__":
    main()
