package com.chinasie.orion.service.projectStatistics;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.projectStatistics.PurchaseStatusStatisticsDTO;
import com.chinasie.orion.domain.entity.projectStatistics.PurchaseStatusStatistics;
import com.chinasie.orion.domain.vo.projectStatistics.PurchaseStatusStatisticsVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

/**
 * <p>
 * PurchaseStatusStatistics 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-21 14:22:48
 */
public interface PurchaseStatusStatisticsService  extends OrionBaseService<PurchaseStatusStatistics>{
    /**
     *  详情
     *
     * * @param id
     */
    PurchaseStatusStatisticsVO detail(String id)  throws Exception;

    /**
     *  新增
     *
     * * @param purchaseStatusStatisticsDTO
     */
    PurchaseStatusStatisticsVO create(PurchaseStatusStatisticsDTO purchaseStatusStatisticsDTO)  throws Exception;

    /**
     *  编辑
     *
     * * @param purchaseStatusStatisticsDTO
     */
    Boolean edit(PurchaseStatusStatisticsDTO purchaseStatusStatisticsDTO) throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     */
    Page<PurchaseStatusStatisticsVO> pages(Page<PurchaseStatusStatisticsDTO> pageRequest) throws Exception;

}
