<template>
  <a-drawer
    v-model:visible="father.visible"
    class="ui-2-0"
    :title="father.title"
    :mask-closable="false"
    :width="650"
  >
    <a-form
      ref="formRef"
      :model="father.form"
      :rules="rules"
      :label-col="{ flex: '100px' }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item
        label="知识名称"
        name="name"
      >
        <a-input
          v-model:value="father.form.name"
          placeholder="请输入知识名称"
          allow-clear
          :maxlength="64"
        />
      </a-form-item>
      <a-form-item
        label="知识摘要"
        name="summary"
      >
        <a-input
          v-model:value="father.form.summary"
          placeholder="请输入知识摘要"
          allow-clear
          :maxlength="128"
        />
      </a-form-item>
      <a-form-item label="知识内容">
        <a-textarea
          v-model:value="father.form.content"
          placeholder="请输入要分享的内容"
          :rows="4"
          :maxlength="500"
          allow-clear
        />
      </a-form-item>
      <a-table
        row-key="type"
        :show-index-column="false"
        :max-height="300"
        :bordered="true"
        :pagination="false"
        :data-source="father.form.permissionJsonDtoList"
        :columns="columnsSetting"
      >
        <template #type="{ text }">
          <span v-if="text === 1">可阅读</span>
          <span v-if="text === 2">可编辑</span>
          <span v-if="text === 3">可下载</span>
        </template>
        <template #projectList="{ record }">
          <span
            v-for="(s, i) in record.projectList"
            :key="i"
          >{{ s.name }}；</span>
        </template>
        <template #organizationList="{ record }">
          <span
            v-for="(s, i) in record.organizationList"
            :key="i"
          >{{ s.name }}；</span>
        </template>
        <template #personList="{ record }">
          <span
            v-for="(s, i) in record.personList"
            :key="i"
          >{{ s.name }}；</span>
        </template>
        <template #isPublic="{ record }">
          <a-checkbox v-model:checked="record.isPublic">
            公开
          </a-checkbox>
          <a-button @click="handleChooseObject(record)">
            选择对象
          </a-button>
        </template>
      </a-table>
      <div>
        <a-button
          type="link"
          @click="isShow = !isShow"
        >
          {{ isShow ? '收起更多' : '展开更多' }}
          <UpOutlined v-if="isShow" />
          <DownOutlined v-else />
        </a-button>
      </div>
      <a-row v-if="isShow">
        <a-col :span="12">
          <a-form-item label="创建者">
            {{ father.form.creatorName }}
          </a-form-item>
          <a-form-item label="所属项目">
            {{ father.form.projectName }}
          </a-form-item>
          <a-form-item label="所属阶段">
            xxx
          </a-form-item>
          <a-form-item label="所属计划">
            xxx
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="作者">
            {{ father.form.creatorName }}
          </a-form-item>
          <a-form-item label="部门">
            {{ father.form.deptName }}
          </a-form-item>
          <a-form-item label="有效期">
            xxx
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <div class="footer">
      <a-button
        style="margin-right: 8px"
        @click="father.visible = false"
      >
        取消
      </a-button>
      <a-button
        type="primary"
        @click="handleOk"
      >
        确定
      </a-button>
    </div>
  </a-drawer>
  <SelectObject
    v-if="selectObject.visible"
    :data="selectObject"
    @submit="submitSelectObject"
  />
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, ref,
} from 'vue';
import {
  Form, Input, message, Row, Col, Button, Checkbox, Table, Drawer,
} from 'ant-design-vue';
import { DownOutlined, UpOutlined } from '@ant-design/icons-vue';
import { edit } from '/@/views/pms/projectLaborer/knowledgeEditData/data';
import SelectObject from '/@/views/pms/projectLaborer/knowledgeEditData/SelectObject.vue';

export default defineComponent({
  name: 'Share',
  components: {
    ADrawer: Drawer,
    SelectObject,
    UpOutlined,
    DownOutlined,
    ATextarea: Input.TextArea,
    ATable: Table,
    ACheckbox: Checkbox,
    ACol: Col,
    AButton: Button,
    ARow: Row,
    AForm: Form,
    AFormItem: Form.Item,
    AInput: Input,
  },
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
  emits: ['submit'],
  setup(props, { emit }) {
    const state = reactive({
      father: props.data,
      columnsSetting: edit.columns1,
      formRef: ref(),
      isShow: false,
      selectObject: {},
      rules: {
        name: [
          {
            required: true,
            message: '知识名称不能为空',
            trigger: 'blur',
          },
        ],
        summary: [
          {
            required: true,
            message: '知识摘要不能为空',
            trigger: 'blur',
          },
        ],
      },
    });

    function handleChooseObject(row) {
      state.selectObject = {
        visible: true,
        form: JSON.parse(JSON.stringify(row)),
      };
    }

    function submitSelectObject(row) {
      state.father.form.permissionJsonDtoList[row.type - 1] = JSON.parse(JSON.stringify(row));
    }

    function handleOk() {
      state.formRef
        .validate()
        .then(() => {
          emit('submit', state.father);
          state.father.visible = false;
        })
        .catch(() => {
          message.warning('请检查必填项');
        });
    }

    return {
      ...toRefs(state),
      handleOk,
      handleChooseObject,
      submitSelectObject,
    };
  },
});
</script>
<style lang="less" scoped>
  .footer {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 100%;
    border-top: 1px solid #e9e9e9;
    padding: 10px 16px;
    background: #fff;
    text-align: right;
    z-index: 1;
  }
</style>
