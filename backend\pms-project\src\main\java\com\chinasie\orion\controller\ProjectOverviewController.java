//package com.chinasie.orion.controller;
//
//import com.chinasie.orion.domain.vo.MilestoneVo;
//import com.chinasie.orion.domain.vo.projectOverview.*;
//import com.chinasie.orion.dto.ResponseDTO;
//import com.chinasie.orion.service.ProjectOverviewService;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//import java.util.List;
//
///**
// * Created with IntelliJ IDEA.
// *
// * @author: wys
// * @date: 2022/03/07/9:18
// * @description:
// */
//@RestController
//@RequestMapping("/project-overview")
//@Api(tags = "项目概览")
//public class ProjectOverviewController {
//
//    @Resource
//    private ProjectOverviewService projectOverviewService;
//
//
//
//    @ApiOperation("获取项目信息")
//    @GetMapping(value = "/info")
//    public ResponseDTO<ProjectOverviewVo> getOverviewProjectInfo(@RequestParam("projectId") String projectId) throws Exception {
//        try {
//            return new ResponseDTO(projectOverviewService.getOverviewProjectInfo(projectId));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//
//    @ApiOperation("获取项目-工时")
//    @GetMapping(value = "/manHour")
//    public ResponseDTO<ProjectPlanManHourVo> getProjectManHourCount(@RequestParam("projectId") String projectId) throws Exception {
//        try {
//            return new ResponseDTO(projectOverviewService.getProjectManHourCount(projectId));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//
//
//    @ApiOperation("获取项目-交付物")
//    @GetMapping(value = "/deliverCount")
//    public ResponseDTO<ProjectDeliverCount> getProjectDeliverCount(@RequestParam("projectId") String projectId) throws Exception {
//        try {
//            return new ResponseDTO(projectOverviewService.getProjectDeliverCount(projectId));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//
//    @ApiOperation("获取-未来两周任务")
//    @GetMapping(value = "/afterWorkCount")
//    public ResponseDTO<List<ProjectDayCountVo>> afterWorkCount(@RequestParam("projectId") String projectId) throws Exception {
//        try {
//            return new ResponseDTO(projectOverviewService.afterWorkCount(projectId));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//    @ApiOperation("获取-工作项类型统计")
//    @GetMapping(value = "/planTypeCount")
//    public ResponseDTO<ProjectViewVo<ProjectPlanTypeCountVo>> planTypeCount(@RequestParam("projectId") String projectId) throws Exception {
//        try {
//            return new ResponseDTO(projectOverviewService.planTypeCount(projectId));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//    @ApiOperation("获取-里程碑统计")
//    @GetMapping(value = "/milestone")
//    public ResponseDTO<ProjectViewVo<MilestoneVo>> milestone(@RequestParam("projectId") String projectId) throws Exception {
//        try {
//            return new ResponseDTO(projectOverviewService.milestone(projectId));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//}
