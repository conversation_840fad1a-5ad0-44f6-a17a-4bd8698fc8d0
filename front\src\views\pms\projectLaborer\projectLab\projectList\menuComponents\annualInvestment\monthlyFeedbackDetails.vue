<template>
  <Layout3
    :projectData="formData"
    :menuData="menuData"
    :type="2"
    :defaultActionId="tabsId"
    :onMenuChange="menuChange"
    class="monthly-feedback-details"
  >
    <template #code>
      <div>{{ `编码：${formData?.number??''}` }}</div>
    </template>

    <template
      #header-right
    >
      <div>
        <BasicTableAction
          :actions="actionsList"
          type="button"
          :record="{data: formData}"
          :showItemNumber="2"
        />
      </div>
    </template>
    <template #footer>
      <WorkflowAction
        v-if="formData?.id"
        ref="workflowActionRef"
        :workflow-props="workflowProps"
      />
    </template>

    <div v-if="tabsId==='applyDetails'">
      <DetailsLayout
        label-width="180px"
        label-align="right"
        :spinning="loading"
        :isContent="true"
        :data-source="formData"
        :list="baseInfo"
      />
      <DetailsLayout
        label-width="180px"
        label-align="right"
        :spinning="loading"
        :isContent="true"
        :data-source="formData"
        :list="planList"
      >
        <template #yearFinished="{record}">
          {{ Number(record?.['mpracticeDo']/record?.['yinvestmentPlan']*100).toFixed(2) }}%
        </template>
        <template #mpracticeDoRate="{record}">
          {{ Number(record?.['mpracticeDo']/record?.['mplanDo']*100).toFixed(2) }}%
        </template>
        <template #monthDoStatus="{record}">
          {{ record===0? '执行情况有偏差' : '执行情况无偏差' }}
        </template>
      </DetailsLayout>
    </div>
    <div
      v-else
      :class="{'process-position':tabsId!=='process'}"
      class="process-dev"
    >
      <WorkflowView
        ref="workflowViewRef"
        :workflow-props="workflowProps"
      />
    </div>
    <AddMonthlyFeedback
      @register="register"
      @update="update"
    />
  </Layout3>
</template>

<script lang="ts">
import {
  computed, defineComponent, getCurrentInstance, onMounted, provide, reactive, Ref, ref, toRefs,
} from 'vue';
import {
  BasicTableAction, BpmnFooterTool, isPower, Layout3, useDrawer,
} from 'lyra-component-vue3';
import { useRoute } from 'vue-router';
import dayjs from 'dayjs';
import Process from '/@/views/pms/projectManage/components/Process.vue';
import AddMonthlyFeedback from './components/AddMonthlyFeedback.vue';
import { getMonthFeedbackDetails } from './index';
import { renderNotAuthPage } from '/@/views/pms/utils';
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import { WorkflowView, WorkflowProps, WorkflowAction } from 'lyra-workflow-component-vue3';
import Api from '/@/api';
import Execution
  from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/annualInvestment/modal/Execution.vue';

export default defineComponent({
  name: 'MonthlyFeedbackDetails',
  components: {
    WorkflowAction,
    WorkflowView,
    DetailsLayout,
    Layout3,
    BasicTableAction,
    AddMonthlyFeedback,
  },
  setup() {
    const route = useRoute();
    const processRef = ref();
    const [register, { openDrawer }] = useDrawer();
    const workflowViewRef: Ref = ref();
    const workflowActionRef: Ref = ref();
    const state = reactive({
      processName: computed(() => `${state.formData?.name}`),
      loading: false,
      formData: {},
      menuData: [],
      tabsId: '',
      showApprove: route.query.type === 'process',
      actionsList: [
        {
          event: 'edit',
          text: '编辑',
          icon: 'sie-icon-bianji',
          onClick() {
            openDrawer(true, {
              type: 'edit',
              id: state.formData.id,
            });
          },
          isShow: computed(() => (state.formData.status === 100 || state.formData.status === 110) && isPower('TZJH_container_button_15', powerData)),
        },
        {
          text: '发起流程',
          icon: 'sie-icon-qidongliucheng',
          isShow: workflowActionRef.value?.isAdd && state.formData?.status === 100,
          onClick() {
            workflowActionRef.value?.onAddTemplate({
              messageUrl: route.fullPath,
            });
          },
        },
      ],
    });

    const currentInstance = getCurrentInstance();
    const powerData = ref([]);

    provide('formData', computed(() => state.formData));
    provide('powerData', powerData);

    onMounted(() => {
      getFormData();
    });
    const workflowProps = computed<WorkflowProps>(() => ({
      Api,
      businessData: state.formData,
      afterEvent() {
        workflowViewRef.value?.init();
        getFormData();
      },
    }));
    function setMenu() {
      state.menuData = [];
      if (isPower('TZJH_container_12', powerData)) {
        state.menuData.push({
          name: '概况详情',
          id: 'applyDetails',
        });
      }

      if (isPower('TZJH_container_13', powerData)) {
        state.menuData.push({
          name: '流程',
          id: 'process',
        });
      }
    }

    function getFormData() {
      state.loading = true;
      getMonthFeedbackDetails(route.params.id, 'PMS1003').then((res) => {
        powerData.value = res?.detailAuthList ?? [];
        renderNotAuthPage({
          vm: currentInstance,
          powerData: powerData.value,
        });
        setMenu();
        if (isPower('TZJH_container_11', powerData)) {
          state.formData = res;
        }

        res.complete = Number(res.yinvestmentPlan) === 0 ? '0.00%' : `${((Number(res.mpracticeDo) / Number(res.yinvestmentPlan)) * 100).toFixed(2)}%`;
        res.implementation = Number(res.mplanDo) === 0 ? '0.00%' : `${((Number(res.mpracticeDo) / Number(res.mplanDo)) * 100).toFixed(2)}%`;
        res.monthDoStatusName = res.monthDoStatus === 0 ? '执行情况有偏差' : '执行情况无偏差';
        if (!state.tabsId) {
          state.tabsId = route.query?.tabsType ? 'process' : 'applyDetails';
        }
        state.loading = false;
      }).catch((err) => {
        state.loading = false;
      });
    }
    provide('getFormData', getFormData);
    provide(
      'formData',
      computed(() => state.formData),
    );
    function update() {
      getFormData();
    }

    const baseInfo:Ref<any[]> = ref([
      {
        label: '流程单号',
        field: '',
      },
      {
        label: '流程名称',
        field: 'procInstName',
      },
      {
        label: '申请人工号',
        field: '',
      },
      {
        label: '申请人',
        field: 'startName',
      },
      {
        label: '状态',
        field: 'statusName',
      },
      {
        label: '修改时间',
        field: 'createTime',
        formatTime: 'YYYY-MM-DD HH:mm:ss',
      },
      {
        label: '提交时间',
        field: 'startTime',
        formatTime: 'YYYY-MM-DD HH:mm:ss',
      },
    ]);

    const planList:Ref<any[]> = ref([
      {
        label: '计划编号',
        field: 'number',
      },
      {
        label: '名称',
        field: 'name',
      },
      {
        label: '年份',
        field: 'year',
      },
      {
        label: '月份',
        field: 'month',
      },
      {
        label: computed(() => `${state.formData.year}年投资计划`),
        field: 'yinvestmentPlan',
        isMoney: true,
        unit: '万元',
      },
      {
        label: '年度完成率',
        field: 'yearFinished',
        slot: true,
        slotName: 'yearFinished',
      },
      {
        label: '上月实际执行',
        field: 'lastPracticeDo',
        gridColumn: '3/5',
        isMoney: true,
        unit: '万元',
      },
      {
        label: computed(() => `1-${state.formData.month}计划`),
        field: 'mplanDo',
        isMoney: true,
        unit: '万元',
      },
      {
        label: computed(() => `1-${state.formData.month}实际执行`),
        field: 'mpracticeDo',
        isMoney: true,
        unit: '万元',
      },
      {
        label: computed(() => `1-${state.formData.month}计划执行率`),
        field: 'mpracticeDoRate',
        gridColumn: '3/5',
        slot: true,
        slotName: 'mpracticeDoRate',
      },
      {
        label: '本月投资计划执行状态',
        field: 'monthDoStatus',
        gridColumn: '1/4',
        wrap: true,
        slot: true,
        slotName: 'monthDoStatus',
      },
      {
        label: '总体进度执行情况',
        field: 'totalProcess',
        gridColumn: '1/4',
        wrap: true,
      },
      {
        label: '项目总体进度滞后情况',
        field: 'delayDesc',
        gridColumn: '1/4',
        wrap: true,
      },
      {
        label: '本月执行情况',
        field: 'monthProcess',
        gridColumn: '1/4',
        wrap: true,
      },
      {
        label: '下月进度计划',
        field: 'nextProcess',
        gridColumn: '1/4',
        wrap: true,
      },
      {
        label: '本月偏差原因及纠错措施',
        field: 'reason',
        gridColumn: '1/4',
        wrap: true,
      },
      {
        label: '备注',
        field: 'remark',
        gridColumn: '1/4',
        wrap: true,
      },
    ]);
    function menuChange(val) {
      state.tabsId = val.id;
    }
    // 修改反馈表
    function handleEditPlan() {
      openDrawer(true, {
        type: 'edit',
        id: state.formData.id,
      });
    }

    return {
      ...toRefs(state),
      processRef,
      update,
      register,
      powerData,
      isPower,
      baseInfo,
      planList,
      handleEditPlan,
      workflowProps,
      workflowViewRef,
      workflowActionRef,
      menuChange,
    };
  },
});

</script>

<style lang="less" scoped>
:deep(.information-title) {
  font-size: 18px;
  font-weight: 500;
  color: rgba(0,0,0,0.85);
  line-height: 28px;
  position: relative;
  padding-left: 10px;
  border-bottom: 1px solid #cccccc;
  padding-bottom: 5px;
    &:before {
        content: '';
        height: 18px;
        width: 4px;
        background: ~`getPrefixVar('primary-color') `;
        display: inline-block;
        position: absolute;
        top: 5px;
        left: 0px;
    }
}

.footer-wrap {
  border-top: 1px solid #e9ecf2;
  margin: 0 ~`getPrefixVar('content-margin-left') `;
}

.process-position{
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
  pointer-events: none;
}
.process-dev{
  height: 100%;
  width: 100%;
}

.plan-list-btn{
  position: absolute;
  top: 0;
  right: 0;
  color: ~`getPrefixVar('primary-color')`;
  z-index: 1;
  cursor: pointer;
}
</style>
