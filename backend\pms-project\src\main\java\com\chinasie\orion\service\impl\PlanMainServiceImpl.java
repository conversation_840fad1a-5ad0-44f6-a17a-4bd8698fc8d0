package com.chinasie.orion.service.impl;

import cn.hutool.core.util.IdUtil;
import com.chinasie.orion.domain.entity.PlanMain;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.PlanMainRepository;
import com.chinasie.orion.service.PlanMainService;
import org.springframework.stereotype.Service;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/18/16:29
 * @description:
 */
@Service
public class PlanMainServiceImpl extends OrionBaseServiceImpl<PlanMainRepository, PlanMain> implements PlanMainService {
    @Override
    public String savePlanMain(String name, String projectId) throws Exception {
        PlanMain planMainDTO = new PlanMain();
        planMainDTO.setName(name);
        planMainDTO.setProjectId(projectId);
        planMainDTO.setNumber("JXZT" + IdUtil.randomUUID());
        this.save(planMainDTO);
        return planMainDTO.getId();
    }


    @Override
    public boolean delPlanMain(String id) throws Exception {
        return this.removeById(id);
    }
}
