<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <artifactId>hadoop-thirdparty</artifactId>
    <groupId>org.apache.hadoop.thirdparty</groupId>
    <version>1.1.1</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>hadoop-shaded-protobuf_3_7</artifactId>
  <name>Apache Hadoop shaded Protobuf 3.7</name>
  <build>
    <resources>
      <resource>
        <targetPath>META-INF</targetPath>
        <directory>${project.basedir}/..</directory>
        <includes>
          <include>licenses-binary/*</include>
          <include>NOTICE.txt</include>
          <include>NOTICE-binary</include>
        </includes>
      </resource>
      <resource>
        <directory>${project.basedir}/src/main/resources</directory>
      </resource>
    </resources>
    <plugins>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <executions>
          <execution>
            <id>shade-protobuf</id>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <artifactSet>
                <includes>
                  <include>com.google.protobuf:protobuf-java</include>
                </includes>
              </artifactSet>
              <filters>
                <filter>
                  <artifact>com.google.protobuf:*</artifact>
                  <includes>
                    <include>**/*</include>
                  </includes>
                </filter>
              </filters>
              <relocations>
                <relocation>
                  <pattern>com/google/protobuf</pattern>
                  <shadedPattern>${protobuf.shade.prefix}</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>google/</pattern>
                  <shadedPattern>${shaded.prefix}.google.</shadedPattern>
                  <includes>
                    <include>**/*.proto</include>
                  </includes>
                </relocation>
              </relocations>
              <transformers>
                <transformer>
                  <resources>
                    <resource>NOTICE</resource>
                    <resource>LICENSE</resource>
                  </resources>
                </transformer>
                <transformer>
                  <resource>META-INF/LICENSE.txt</resource>
                  <file>${basedir}/../LICENSE-binary</file>
                </transformer>
              </transformers>
            </configuration>
          </execution>
        </executions>
        <configuration>
          <createDependencyReducedPom>true</createDependencyReducedPom>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
