package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * TechnicalConfigurationPerson DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-22 14:03:35
 */
@ApiModel(value = "TechnicalConfigurationPersonDTO对象", description = "技术配置人员")
@Data
@ExcelIgnoreUnannotated
public class TechnicalConfigurationPersonDTO extends ObjectDTO implements Serializable {

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @ExcelProperty(value = "姓名 ", index = 0)
    private String personName;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    @ExcelProperty(value = "性别 ", index = 1)
    private String personSex;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    @ExcelProperty(value = "身份证号 ", index = 2)
    private String idCard;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期")
    @ExcelProperty(value = "出生日期 ", index = 3)
    private String birth;

    /**
     * 民族
     */
    @ApiModelProperty(value = "民族")
    @ExcelProperty(value = "民族 ", index = 4)
    private String nation;

    /**
     * 婚姻状况
     */
    @ApiModelProperty(value = "婚姻状况")
    @ExcelProperty(value = "婚姻状况 ", index = 5)
    private String maritalStatus;

    /**
     * 联系方式
     */
    @ApiModelProperty(value = "联系方式")
    @ExcelProperty(value = "联系方式 ", index = 6)
    private String personTel;

    /**
     * 最高学历
     */
    @ApiModelProperty(value = "最高学历")
    @ExcelProperty(value = "最高学历 ", index = 7)
    private String educationLevel;

    /**
     * 所学专业
     */
    @ApiModelProperty(value = "所学专业")
    @ExcelProperty(value = "所学专业 ", index = 8)
    private String major;

    /**
     * 职称
     */
    @ApiModelProperty(value = "职称")
    @ExcelProperty(value = "职称 ", index = 9)
    private String personTitle;

    /**
     * 技术专业证书
     */
    @ApiModelProperty(value = "技术专业证书")
    @ExcelProperty(value = "技术专业证书 ", index = 10)
    private String majorCertificate;

    /**
     * 所在公司
     */
    @ApiModelProperty(value = "所在公司")
    @ExcelProperty(value = "所在公司 ", index = 11)
    private String personCompany;

    /**
     * 所在部门/中心
     */
    @ApiModelProperty(value = "所在部门/中心")
    @ExcelProperty(value = "所在部门/中心 ", index = 12)
    private String personDepartment;

    /**
     * 所在研究所/专业室
     */
    @ApiModelProperty(value = "所在研究所/专业室")
    @ExcelProperty(value = "所在研究所/专业室 ", index = 13)
    private String personInstitute;

    /**
     * 分管项目经理
     */
    @ApiModelProperty(value = "分管项目经理")
    @ExcelProperty(value = "分管项目经理 ", index = 14)
    private String projectManager;

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @ExcelProperty(value = "供应商编码 ", index = 15)
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @ExcelProperty(value = "供应商名称 ", index = 16)
    private String supplierName;

    /**
     * 是否项目制人员
     */
    @ApiModelProperty(value = "是否项目制人员")
    @ExcelProperty(value = "是否项目制人员 ", index = 17)
    private String isProjectBased;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 18)
    private String contractCode;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称 ", index = 19)
    private String contractName;

    /**
     * 合同级别
     */
    @ApiModelProperty(value = "合同级别")
    @ExcelProperty(value = "合同级别 ", index = 20)
    private String contractLevel;

    /**
     * 工作内容
     */
    @ApiModelProperty(value = "工作内容")
    @ExcelProperty(value = "工作内容 ", index = 21)
    private String jobContent;

    /**
     * 常驻服务地点
     */
    @ApiModelProperty(value = "常驻服务地点")
    @ExcelProperty(value = "常驻服务地点 ", index = 22)
    private String serviceLocation;

    /**
     * 是否从事放射性工作
     */
    @ApiModelProperty(value = "是否从事放射性工作")
    @ExcelProperty(value = "是否从事放射性工作 ", index = 23)
    private String isRadioactivityWork;

    /**
     * 是否完成体检
     */
    @ApiModelProperty(value = "是否完成体检")
    @ExcelProperty(value = "是否完成体检 ", index = 24)
    private String isCompletedExam;

    /**
     * 办卡或授权
     */
    @ApiModelProperty(value = "办卡或授权")
    @ExcelProperty(value = "办卡或授权 ", index = 25)
    private String cardOrEmpower;

    /**
     * 是否有亲属在集团内
     */
    @ApiModelProperty(value = "是否有亲属在集团内")
    @ExcelProperty(value = "是否有亲属在集团内 ", index = 26)
    private String haveKinGroup;

    /**
     * 亲属姓名
     */
    @ApiModelProperty(value = "亲属姓名")
    @ExcelProperty(value = "亲属姓名 ", index = 27)
    private String kinName;

    /**
     * 亲属职务
     */
    @ApiModelProperty(value = "亲属职务")
    @ExcelProperty(value = "亲属职务 ", index = 28)
    private String kinPost;

    /**
     * 亲属公司
     */
    @ApiModelProperty(value = "亲属公司")
    @ExcelProperty(value = "亲属公司 ", index = 29)
    private String kinCompany;

    /**
     * 是否技术配置
     */
    @ApiModelProperty(value = "是否技术配置")
    @ExcelProperty(value = "是否技术配置 ", index = 30)
    private String isTechnicaled;

    /**
     * 人员状态（在场/离场）
     */
    @ApiModelProperty(value = "人员状态（在场/离场）")
    @ExcelProperty(value = "人员状态（在场/离场） ", index = 31)
    private String personStatus;

    /**
     * 预计离场时间
     */
    @ApiModelProperty(value = "预计离场时间")
    @ExcelProperty(value = "预计离场时间 ", index = 32)
    private Date leaveTime;

    /**
     * 是否违反相关安全规范
     */
    @ApiModelProperty(value = "是否违反相关安全规范")
    @ExcelProperty(value = "是否违反相关安全规范 ", index = 33)
    private String isSafetied;

    /**
     * 入场时间
     */
    @ApiModelProperty(value = "入场时间")
    @ExcelProperty(value = "入场时间 ", index = 34)
    private Date entryTime;

    /**
     * 离场时间
     */
    @ApiModelProperty(value = "离场时间")
    @ExcelProperty(value = "离场时间 ", index = 35)
    private Date leavingTime;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    @ExcelProperty(value = "操作时间 ", index = 36)
    private Date operationTime;

    /**
     * 操作人id
     */
    @ApiModelProperty(value = "操作人id")
    @ExcelProperty(value = "操作人id ", index = 37)
    private String operationId;

    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人姓名")
    @ExcelProperty(value = "操作人姓名 ", index = 38)
    private String operationName;

    /**
     * 锁定状态
     */
    @ApiModelProperty(value = "锁定状态")
    @ExcelProperty(value = "锁定状态 ", index = 39)
    private String lockedState;

    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    @ExcelProperty(value = "工号 ", index = 40)
    private String jobId;
}
