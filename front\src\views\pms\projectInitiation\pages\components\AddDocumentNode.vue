<template>
  <div
    v-loading="loading"
    class="add-document"
  >
    <BasicForm @register="register">
      <template #basicEditor="{ model,field }">
        <div class="basicEditor">
          <BasicCard
            title="条目编制内容"
            class="basic-card"
          />
          <BasicEditor v-model:value="model[field]" />
        </div>
      </template>
    </BasicForm>
  </div>
</template>
<script lang="ts" setup>
import {
  BasicForm, useForm, BasicEditor, BasicCard,
} from 'lyra-component-vue3';
import { onMounted, ref, Ref } from 'vue';
import Api from '/@/api';
import { message } from 'ant-design-vue';

const props = withDefaults(defineProps<{
    drawerData:object
}>(), {
  drawerData: () => ({}),
});
const loading:Ref<boolean> = ref(false);
const [register, { setFieldsValue, validateFields, getFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas: [
    {
      field: 'name',
      label: '条目标题：',
      rules: [
        {
          required: true,
          message: '请输入条目标题',
        },
      ],
      colProps: { span: 24 },
      componentProps: {
        allowClear: true,
        placeholder: '请输入条目标题',
        maxlength: 40,
        showCount: true,
      },
      component: 'Input',
    },

    {
      field: 'writeRequire',
      label: '编写要求：',
      colProps: { span: 24 },
      componentProps: {
        placeholder: '编写要求',
        maxlength: 1000,
        showCount: true,
        style: {
          height: '150px',
        },
      },
      component: 'InputTextArea',
    },
    {
      component: 'Input',
      field: 'content',
      label: '',
      colProps: {
        span: 24,
      },
      slot: 'basicEditor',
    },
  ],
});
defineExpose({
  async onSubmit() {
    let formData = await validateFields();
    if (props.drawerData.type === 'add') {
      formData.approvalId = props.drawerData.approvalId;
      if (props.drawerData.parentId) {
        formData.parentId = props.drawerData.parentId;
      }
    } else {
      formData.id = props.drawerData.id;
    }
    await new Api('/pms').fetch(formData, props.drawerData.type === 'add' ? 'collaborativeCompilationDocument/add' : 'collaborativeCompilationDocument/edit', props.drawerData.type === 'add' ? 'POST' : 'PUT');
    message.success(props.drawerData.type === 'add' ? '新增条目成功' : '编辑条目成功');
  },
});
onMounted(() => {
  if (props.drawerData.type === 'edit') {
    getDetailsData();
  }
});
function getDetailsData() {
  loading.value = true;
  new Api('/pms').fetch('', `collaborativeCompilationDocument/${props.drawerData.id}`, 'GET').then((res) => {
    setFieldsValue(res);
    loading.value = false;
  });
}
</script>
<style scoped lang="less">
.basic-card{
  margin: 0 !important;
  border: 0 !important;
  :deep(.card-content){
    margin: 10px !important;
  }
}
:deep(.ant-input-affix-wrapper){
  height: 100%;
}
</style>