<script setup lang="ts">
import { computed, ref, Ref } from "vue";
import { Icon } from "lyra-component-vue3";
import { Popover } from "ant-design-vue";

const props = withDefaults(
  defineProps<{
    record: Record<string, any>;
    popoverContainer: any;
    componentValue: any;
    component: string;
    editFlag?: boolean;
  }>(),
  {
    editFlag: true,
  }
);

const emits = defineEmits<{
  (
    e: "submit",
    record: any,
    value: any,
    isWork: number,
    resolve: (value: any) => void
  ): void;
}>();

const isEnter: Ref<boolean> = ref(false);

const timer: Ref = ref();
const isWork: Ref<number> = ref();

const dataSource = ref([
  {
    name: "同级",
    id: 1,
  },
  {
    name: "子级",
    id: 2,
  },
]);
const dataType = ref([
  {
    name: "是（作业）",
    id: 1,
  },
  {
    name: "否（作业）",
    id: 0,
  },
]);
function onMouseEnter() {
  if (!props.editFlag) return;
  clearTimeout(timer.value);
  timer.value = setTimeout(() => {
    isEnter.value = true;
  }, 300);
  isSubmit.value = false;
}

function onMouseLeave() {
  if (!props.editFlag) return;
  clearTimeout(timer.value);
  isEnter.value = false;
}

const isFocus: Ref<boolean> = ref(false);

function onFocus() {
  isFocus.value = true;
}

function onBlur() {
  isFocus.value = false;
}

const options: Ref<any[]> = ref([]);
const isShow = computed(
  () => (isEnter.value || isFocus.value) && !isSubmit.value
);

const isSubmit: Ref<boolean> = ref(false);

async function submit(record: any, data: any, isWork) {
  await new Promise((resolve) => {
    emits("submit", record, data, isWork, resolve);
  });
  isSubmit.value = true;
}

function onClickWork(id: number) {
  isWork.value = id;
}

function handleClick(record, item) {
  submit(record, item.id, isWork.value);
}
const getPopupContainer = () => props.popoverContainer.value;
</script>

<template>
  <div class="pops-mouse-cell">
    <div v-if="props.record?.nodeType !== 'plan'" class="cell-icon">
      <Icon icon="orion-icon-sisternode" size="14" />
      <Popover placement="top" :getPopupContainer="getPopupContainer">
        <template #content>
          注：统计所有子级计划类型为里程碑和任务模块的数量
        </template>
        <span>{{ props.record?.childrenCount || 0 }}</span>
      </Popover>
    </div>
    <Popover placement="rightTop" :getPopupContainer="getPopupContainer">
      <template #content>
        <div class="type-list-box">
          <ul>
            <li
              v-for="item in dataSource"
              :key="item.id"
              @click="handleClick(props.record, item)"
            >
              {{ item.name }}
            </li>
          </ul>
          <ul>
            <li
              v-for="item in dataType"
              :key="item.id"
              :class="isWork === item.id ? 'active' : ''"
              @click="onClickWork(item.id)"
            >
              {{ item.name }}
            </li>
          </ul>
        </div>
      </template>
      <div class="add-icon">
        <Icon icon="sie-icon-tianjiaxinzeng" size="14" />
      </div>
    </Popover>
  </div>
</template>

<style scoped lang="less">
.project-layout-content {
  .type-list-box {
    display: flex;
    flex-direction: row;
    background: #fff;
    margin: -11px -14px;
    border: 1px solid #ddd;
    ul {
      margin: 0;
      padding: 0;
      li {
        width: 90px;
        text-align: center;
        list-style-type: none;
        padding: 2px 10px;
        cursor: pointer;
      }
      li:first-child {
        border-bottom: 1px solid #ddd;
      }
      li:hover {
        background-color: #2a7dc9;
        color: #fff;
      }
      li.active {
        background-color: #2a7dc9;
        color: #fff;
      }
    }
    ul:first-child li {
      text-align: left;
      border-right: 1px solid #ddd;
    }
  }
}
.pops-mouse-cell {
  position: relative;
  display: flex;
  align-items: center;
  margin-left: -10px;
  .cell-icon {
    display: flex;
    align-items: center;
    span {
      display: inline-block;
      margin-left: 5px;
    }
  }
  .add-icon {
    display: none;
    margin-left: 16px;
    cursor: pointer;
  }
}
</style>
