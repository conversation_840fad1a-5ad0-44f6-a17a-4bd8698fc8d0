package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ProjectAchievementFloder Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-07 13:43:27
 */
@TableName(value = "pmsx_project_achievement_floder")
@ApiModel(value = "ProjectAchievementFloder对象", description = "项目成果文件夹表")
@Data
public class ProjectAchievementFloder extends ObjectEntity implements Serializable {

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @TableField(value = "sort")
    private Long sort;

    /**
     * 文件夹名称
     */
    @ApiModelProperty(value = "文件夹名称")
    @TableField(value = "name")
    private String name;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 立项id
     */
    @ApiModelProperty(value = "立项id")
    @TableField(value = "approval_id")
    private String approvalId;

}

