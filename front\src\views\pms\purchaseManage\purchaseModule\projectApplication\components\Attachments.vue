<script setup lang="ts">
import { UploadList, BasicCard } from 'lyra-component-vue3';
import {
  computed, inject, reactive, ref, unref, watchEffect,
} from 'vue';
import Api from '/@/api';
import { useRoute } from 'vue-router';

const route = useRoute();
const powerData = computed(() => inject('powerData'));
const itemId = ref(route.params.id);
const uploadProps = computed(() => ({
  height: 500,
  isSpacing: true,
  maxSize: 10000,
  accept: '.png,.jpg,.jpeg,.tiff,.gif,.mp4,.wav,.mp3,.acc,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.html,.htm,.rtf,.zip,.rar,.gz,.7z',
  buttonText: '上传附件',
}));
async function listApi() {
  const data: any[] = await new Api('/pms/ncfFormpurchaseRequest/getFileList').fetch({
    id: unref(itemId),
  }, '', 'POST');
  return data.map((item) => {
    delete item.children;
    return item;
  });
}

async function deleteApi(record: Record<string, any>) {
  return new Api('/pms/ncfFormpurchaseRequest/deleteFileList').fetch([{ id: record.id }], '', 'DELETE');
}

async function batchDeleteApi({ rows }) {
  const ids = rows.map((item: Record<string, any>) => ({
    id: item.id,
  }));
  return new Api('/pms/ncfFormpurchaseRequest/deleteFileList').fetch(ids, '', 'DELETE');
}

async function saveApi(files: Record<string, any>[]) {
  return new Api('/pms/ncfFormpurchaseRequest/uploadFile').fetch({
    attachments: files,
    id: unref(itemId),
  }, '', 'POST');
}

</script>

<template>
  <UploadList
    :listApi="listApi"
    type="page"
    :powerData="powerData"
    :powerCode="{
      delete:'PMS_CGLXXQXQ_container_02_02_button_02',
      upload:'PMS_CGLXXQXQ_container_02_02_button_01',
      download:'PMS_CGLXXQXQ_container_02_02_button_03',
    }"
    :edit="false"
    :deleteApi="deleteApi"
    :batchDeleteApi="batchDeleteApi"
    :saveApi="saveApi"
    v-bind="uploadProps"
  />
</template>

<style scoped lang="less">

</style>
