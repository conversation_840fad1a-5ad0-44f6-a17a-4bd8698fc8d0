<script lang="ts">
import { defineComponent, inject, provide } from 'vue';
import { CheckCircleFilled } from '@ant-design/icons-vue';
export default defineComponent({
  components: {
    CheckCircleFilled,
  },
  props: {
    actions: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  setup(props) {
    const handleActionClickNode = inject<((action: any) => void)>('handleActionClick');
    const handleClick = (item) => {
      handleActionClickNode(item);
    };
    provide('handleActionClickNode', handleActionClickNode);
    return {
      handleClick,
      handleActionClickNode,
    };
  },
});
</script>

<template>
  <div class="content-box">
    <div
      v-for="item in actions"
      :key="item.id"
      class="cont"
      @click.stop="handleClick(item)"
    >
      <div class="label-name">
        {{ item.name }}
        <div
          v-for="i in item.actions"
          :key="i.id"
          class="action-table"
          @click.stop="handleClick(i)"
        >
          {{ i.name }}
        </div>
      </div>
      <div class="action-table-icon">
        <!--进行中-->
        <div
          v-if="item.isOver"
          class="circle3"
        />
        <!--已完成-->
        <CheckCircleFilled
          v-if="item.isFinish"
          class="circle2"
        />
        <!--未开始-->
        <div
          v-if="item.isNot"
          class="circle1"
        />
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.content-box{
  margin: 0 auto;
  padding:0 10px;
  .cont{
    padding-left: 20px;
    padding-bottom: 2px;
    position: relative;
    width: 100%;
    .label-name{
      padding: 10px 0;
      cursor: pointer;
      .action-table{
        height: 28px;
      }
    }
    .circle1{
      width: 15px;
      height: 15px;
      background: #fff;
      border:1px solid #999;
      border-radius: 50%;
      position: absolute;
      left: -8px;
      top: 16px;
    }
    .circle2{
      width: 15px;
      height: 15px;
      color:#1890ff;
      border-radius: 50%;
      position: absolute;
      left: -8px;
      top: 16px;
      background-color: #fff;
    }
    .circle3{
      width: 15px;
      height: 15px;
      background: #fff;
      border:1px solid #F09509;
      border-radius: 50%;
      position: absolute;
      left: -8px;
      top: 16px;
    }
  }
}

</style>
