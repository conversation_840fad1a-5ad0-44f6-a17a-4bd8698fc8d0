package com.chinasie.orion.management.constant;

/**
 * @author: yk
 * @date: 2023/10/25 20:40
 * @description:
 */
public enum OrderStatusEnum {
    CREATED(101, "编制中"),
    AUDITING(110, "审核中"),
    FULFIL(130, "履行中"),
    COMPLATED(160, "已完结"),
    ;


    private Integer status;

    private String desc;


    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    OrderStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
