package com.chinasie.orion.management.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;

/**
 * ProjectOrderOther Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-31 14:19:24
 */
@TableName(value = "pmsx_project_order_other")
@ApiModel(value = "ProjectOrderOtherEntity对象", description = "其他信息")
@Data

public class ProjectOrderOther extends  ObjectEntity  implements Serializable{

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    @TableField(value = "order_number")
    private String orderNumber;

    /**
     * 要求到货时间
     */
    @ApiModelProperty(value = "要求到货时间")
    @TableField(value = "receive_date")
    private Date receiveDate;

    /**
     * 特殊送货要求
     */
    @ApiModelProperty(value = "特殊送货要求")
    @TableField(value = "receive_demand")
    private String receiveDemand;

    /**
     * 采购组织编码
     */
    @ApiModelProperty(value = "采购组织编码")
    @TableField(value = "buy_org_code")
    private String buyOrgCode;

    /**
     * 采购组织名称
     */
    @ApiModelProperty(value = "采购组织名称")
    @TableField(value = "buy_org_name")
    private String buyOrgName;

    /**
     * 工厂
     */
    @ApiModelProperty(value = "工厂")
    @TableField(value = "shop")
    private String shop;

    /**
     * 确认控制
     */
    @ApiModelProperty(value = "确认控制")
    @TableField(value = "confirm_control")
    private String confirmControl;

    /**
     * 结算方式
     */
    @ApiModelProperty(value = "结算方式")
    @TableField(value = "settlementmethod")
    private String settlementmethod;

    /**
     * 买方留言
     */
    @ApiModelProperty(value = "买方留言")
    @TableField(value = "leave_word")
    private String leaveWord;

}
