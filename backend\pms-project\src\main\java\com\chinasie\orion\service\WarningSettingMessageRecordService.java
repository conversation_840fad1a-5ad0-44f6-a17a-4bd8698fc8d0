package com.chinasie.orion.service;



import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinasie.orion.domain.dto.WarningSettingMessageRecordDTO;
import com.chinasie.orion.domain.entity.WarningSettingMessageRecipient;
import com.chinasie.orion.domain.entity.WarningSettingMessageRecord;
import com.chinasie.orion.domain.vo.WarningSettingMessageRecordVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;

import java.lang.String;
import java.util.List;

/**
 * <p>
 * WarningSettingMessageRecord 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-17 16:49:23
 */
public interface WarningSettingMessageRecordService  extends OrionBaseService<WarningSettingMessageRecord> {
    /**
     *  详情
     *
     * * @param id
     */
    WarningSettingMessageRecordVO detail(String id)  throws Exception;

    /**
     *  新增
     *
     * * @param warningSettingMessageRecordDTO
     */
    WarningSettingMessageRecordVO create(WarningSettingMessageRecordDTO warningSettingMessageRecordDTO)  throws Exception;

    /**
     *  编辑
     *
     * * @param warningSettingMessageRecordDTO
     */
    Boolean edit(WarningSettingMessageRecordDTO warningSettingMessageRecordDTO) throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;



}
