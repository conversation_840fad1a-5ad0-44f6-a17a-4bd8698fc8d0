package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 22 日
 * 安质环事件码表vo对象
 **/
@Data
@ApiModel(value = "AmpereRingEventCodeVO 对象" ,description = "安质环事件码表vo")
public class AmpereRingEventCodeVO extends ObjectVO implements Serializable {
    /**
     * 事件编码
     */
    @ApiModelProperty(value = "事件编码")
    private String eventCode;

    /**
     * 事件等级
     */
    @ApiModelProperty(value = "事件等级")
    private String eventLevel;

    /**
     * 考核分数
     */
    @ApiModelProperty(value = "考核分数")
    private Double score;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Boolean isDelete;

    /**
     * 是否集团考核指标
     */
    @ApiModelProperty(value = "是否集团考核指标")
    private Boolean isGroupKpi;

    /**
     * 是否监控指标：
     */
    @ApiModelProperty(value = "是否监控指标")
    private Boolean isMonitorIndex;

    /**
     * 是否计算天数
     */
    @ApiModelProperty(value = "是否计算天数")
    private Boolean isCalculateDays;

    /**
     * 事件分类
     */
    @ApiModelProperty(value = "事件分类")
    private String parentName;

    /**
     * 分类code
     */
    @ApiModelProperty(value = "分类code")
    private String parentId;
}
