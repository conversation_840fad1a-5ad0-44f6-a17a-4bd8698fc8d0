package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ProjectSetUp Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-08 16:29:57
 */
@TableName(value = "pmsx_project_set_up")
@ApiModel(value = "ProjectSetUp对象", description = "项目设置")
@Data
public class ProjectSetUp extends ObjectEntity implements Serializable {

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField(value = "name")
    private String name;

    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 配置key
     */
    @ApiModelProperty(value = "配置key")
    @TableField(value = "`key`")
    private String key;

    /**
     * 配置value
     */
    @ApiModelProperty(value = "配置value")
    @TableField(value = "`value`")
    private String value;

}
