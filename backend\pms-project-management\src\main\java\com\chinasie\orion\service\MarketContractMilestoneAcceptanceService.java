package com.chinasie.orion.service;





import com.chinasie.orion.domain.dto.MarketContractMilestoneAcceptanceAddDTO;
import com.chinasie.orion.domain.dto.MarketContractMilestoneBatchAcceptanceDTO;
import com.chinasie.orion.domain.entity.MarketContractMilestoneAcceptance;
import com.chinasie.orion.domain.dto.MarketContractMilestoneAcceptanceDTO;
import com.chinasie.orion.domain.vo.MarketContractMilestoneAcceptanceVO;
import java.lang.String;
import java.util.List;


import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import java.lang.String;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * MarketContractMilestoneAcceptance 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-30 01:59:34
 */
public interface MarketContractMilestoneAcceptanceService  extends  OrionBaseService<MarketContractMilestoneAcceptance>  {


    /**
     *  详情
     *
     * * @param id
     */
    MarketContractMilestoneAcceptanceVO detail(String id,String pageCode)throws Exception;

    /**
     *  批量验收
     *
     * * @param marketContractMilestoneAcceptanceDTO
     */
    Boolean batchAdd(MarketContractMilestoneBatchAcceptanceDTO marketContractMilestoneBatchAcceptanceDTO)throws Exception;

    /**
     *  新增
     *
     * * @param marketContractMilestoneAcceptanceDTO
     */
    String create(MarketContractMilestoneAcceptanceDTO marketContractMilestoneAcceptanceDTO)throws Exception;

    /**
     *  编辑
     *
     * * @param marketContractMilestoneAcceptanceDTO
     */
    Boolean edit(MarketContractMilestoneAcceptanceDTO marketContractMilestoneAcceptanceDTO)throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<MarketContractMilestoneAcceptanceVO> pages( Page<MarketContractMilestoneAcceptanceDTO> pageRequest)throws Exception;

    /**
     *  下载模板
     *
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response)throws Exception;

    /**
     *  导入校验
     *
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


    /**
     *  确认导入
     *
     * * @param importId
     */
    Boolean importByExcel(String importId)throws Exception;

    /**
     *  取消导入
     *
     * * @param importId
     */
    Boolean importCancelByExcel(String importId)throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

    /**
     *  设置各种名称
     *
     * * @param searchConditions
     * * @param response
     */
    void  setEveryName(List<MarketContractMilestoneAcceptanceVO> vos)throws Exception;

    /**
     * 里程碑验收新增
     * @param dto
     * @return
     */

    Boolean addAcceptance(MarketContractMilestoneAcceptanceAddDTO dto)throws Exception;
    /**
     * 里程碑验收新增详情
     * @param dto
     * @return
     */
    MarketContractMilestoneAcceptanceAddDTO addAcceptanceDetail(MarketContractMilestoneAcceptanceAddDTO dto) throws Exception;
}
