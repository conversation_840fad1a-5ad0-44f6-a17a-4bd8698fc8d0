<script setup lang="ts">
import { Chart } from 'lyra-component-vue3';
import { computed } from 'vue';

const props = defineProps<{
  data: Record<string, any>
}>();

const placeConfig = [
  {
    label: '未检定数量',
    field: 'notValidTotal',
  },
  {
    label: '故障/封存数量',
    field: 'faultSealingTotal',
  },
];
const placeOption = computed(() => ({
  grid: {
    left: 0,
    right: 0,
    bottom: 0,
    top: 20,
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: placeConfig.map((item) => item.label),
    axisTick: {
      show: false,
    },
    splitLine: {
      show: false,
    },
    axisLabel: {
      interval: 0,
      overflow: 'truncate',
    },
  },
  yAxis: {
    type: 'value',
    minInterval: 1,
    axisTick: {
      show: false,
    },
    splitLine: {
      show: false,
    },
  },
  series: [
    {
      data: placeConfig.map((item) => props?.data?.baseMaterialCountVO?.[item.field] || 0),
      type: 'bar',
      showBackground: true,
      label: {
        show: true,
        position: 'top',
      },
      backgroundStyle: {
        color: '#EBF1FF',
      },
      itemStyle: {
        color: '#6796FB',
      },
      barWidth: 50,
    },
  ],
}));

const overhaulConfig = [
  {
    label: '未检定数量',
    field: 'notValidTotal',
  },
  {
    label: '故障/封存数量',
    field: 'faultSealingTotal',
  },
  {
    label: '实际入场工器具总数',
    field: 'todayInTotal',
  },
  {
    label: '实际离场工器具总数',
    field: 'todayOutTotal',
  },
];
const overhaulOption = computed(() => ({
  grid: {
    left: 0,
    right: 0,
    bottom: 0,
    top: 20,
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: overhaulConfig.map((item) => item.label),
    axisTick: {
      show: false,
    },
    splitLine: {
      show: false,
    },
    axisLabel: {
      interval: 0,
      overflow: 'truncate',
    },
  },
  yAxis: {
    type: 'value',
    minInterval: 1,
    axisTick: {
      show: false,
    },
    splitLine: {
      show: false,
    },
  },
  series: [
    {
      data: overhaulConfig.map((item) => props.data?.majorMaterialCountVO?.[item.field] || 0),
      type: 'bar',
      showBackground: true,
      label: {
        show: true,
        position: 'top',
      },
      backgroundStyle: {
        color: '#F5F3FF',
      },
      itemStyle: {
        color: '#9F92F9',
      },
      barWidth: 50,
    },
  ],
}));
</script>

<template>
  <div class="material-manage">
    <div class="place">
      <div class="title">
        基地物资总览
      </div>
      <div class="legend">
        <span>工器具总数: {{ data?.baseMaterialCountVO?.materialTotal || 0 }}</span>
        <span class="left-block">单位: 个/台</span>
      </div>
      <div class="chart-wrap">
        <Chart :option="placeOption" />
      </div>
    </div>
    <div class="overhaul">
      <div class="title">
        大修物资一览
      </div>
      <div class="legend">
        <span>计划入场工器具数: {{ data?.majorMaterialCountVO?.materialTotal || 0 }}<span class="ml20">到场率: <span
          style="color:#F48B85"
        >{{ Math.ceil((data?.majorMaterialCountVO?.inRate || 0) * 100) }}%</span></span></span>
        <span class="right-block">单位: 人/台</span>
      </div>
      <div class="chart-wrap">
        <Chart :option="overhaulOption" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.material-manage {
  box-shadow: 0 0 5px 0 #eee;
  height: 300px;
  padding: 15px 10px;
  display: grid;
  grid-template-columns: minmax(0, 1fr) minmax(0, 3fr);
  gap: 0 20px;

  > div {
    display: flex;
    flex-direction: column;

    .title {
      font-size: 16px;
      line-height: 1;

      > span {
        font-size: 14px;
        color: #aaa;
      }
    }

    .legend {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 10px;

      > span:nth-child(2) {
        position: relative;

        &::before {
          position: absolute;
          content: '';
          width: 14px;
          height: 14px;
          top: 50%;
          left: -10px;
          transform: translate(-100%, -50%);
        }

        &.left-block::before {
          background-color: #6B97F9;
        }

        &.right-block::before {
          background-color: #9F92FB;
        }

      }
    }

    .chart-wrap {
      flex-grow: 1;
      height: 0;
    }
  }
}
</style>
