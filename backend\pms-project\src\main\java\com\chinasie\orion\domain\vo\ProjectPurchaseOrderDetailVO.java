package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;

/**
 * PojectPurchaseOrderDetail Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-06 09:20:41
 */
@ApiModel(value = "PojectPurchaseOrderDetailVO对象", description = "项目采购订单明细")
@Data
public class ProjectPurchaseOrderDetailVO extends ObjectVO implements Serializable{

    /**
     * 来源单号
     */
    @ApiModelProperty(value = "来源单号")
    private String sourceNumber;

    /**
     * 物资/服务计划编号
     */
    @ApiModelProperty(value = "物资/服务计划编号")
    private String planNumber;

    /**
     * 物资/服务编码
     */
    @ApiModelProperty(value = "物资/服务编码")
    private String goodsServiceNumber;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 规格型号
     */
    @ApiModelProperty(value = "规格型号")
    private String normsModel;

    /**
     * 计量单位
     */
    @ApiModelProperty(value = "计量单位")
    private String unitCode;

    /**
     * 计量单位名称
     */
    @ApiModelProperty(value = "计量单位名称")
    private String unitCodeName;

    /**
     * 采购数量
     */
    @ApiModelProperty(value = "采购数量")
    private BigDecimal purchaseAmount;

    /**
     * 需求日期
     */
    @ApiModelProperty(value = "需求日期")
    private Date demandDate;

    /**
     * 单价（不含税）
     */
    @ApiModelProperty(value = "单价（不含税）")
    private BigDecimal noTaxPrice;

    /**
     * 总金额（不含税）
     */
    @ApiModelProperty(value = "总金额（不含税）")
    private BigDecimal noTaxTotalAmt;

    /**
     * 税率
     */
    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    /**
     * 单价（含税）
     */
    @ApiModelProperty(value = "单价（含税）")
    private BigDecimal haveTaxPrice;

    /**
     * 总金额（含税）
     */
    @ApiModelProperty(value = "总金额（含税）")
    private BigDecimal haveTaxTotalAmt;

    /**
     * 采购订单id
     */
    @ApiModelProperty(value = "采购订单id")
    private String purchaseId;

}
