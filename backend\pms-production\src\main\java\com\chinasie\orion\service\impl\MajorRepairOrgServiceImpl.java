package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.base.api.domain.vo.SimpleUserVO;
import com.chinasie.orion.base.api.service.UserBaseApiService;
import com.chinasie.orion.constant.MajorRepairOrgEnum;
import com.chinasie.orion.domain.dto.MajorRepairOrgJobSimpleDTO;
import com.chinasie.orion.domain.dto.tree.MajorRepairOrgEditDTO;
import com.chinasie.orion.domain.dto.tree.MajorTreeJobParamDTO;
import com.chinasie.orion.domain.dto.JobProgressDTO;
import com.chinasie.orion.domain.dto.tree.MajorTreeParamDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.dto.MajorRepairOrgDTO;
import com.chinasie.orion.domain.vo.*;


import com.chinasie.orion.domain.vo.tree.MajorTreeVO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.MyExceptionCode;
import com.chinasie.orion.repository.*;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.service.*;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.MajorRepairPlanService;
import com.chinasie.orion.service.RelationOrgToJobService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.TreeInfoProcessor;
import com.mzt.logapi.context.LogRecordContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;

import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * MajorRepairOrg 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18 09:32:26
 */
@Service
@Slf4j
public class MajorRepairOrgServiceImpl extends  OrionBaseServiceImpl<MajorRepairOrgMapper, MajorRepairOrg>   implements MajorRepairOrgService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private ClassRedisHelper classRedisHelper;

    @Autowired
    private RelationOrgToJobService relationOrgToJobService;

    @Autowired
    private MajorRepairPlanService majorRepairPlanService;

    @Autowired
    private UserBaseApiService userBaseApiService;

    @Autowired
    private JobManageMapper jobManageMapper;

    @Autowired
    private JobProgressMapper jobProgressMapper;

    @Autowired
    private OrgProgressMapper orgProgressMapper;

    @Autowired
    @Lazy
    private PersonMangeService personMangeService;

    @Autowired
    @Lazy
    private RelationOrgToMaterialService  relationOrgToMaterialService;

    @Autowired
    private MajorRepairOrgMapper majorRepairOrgMapper;

    @Autowired
    private RelationOrgToPersonMapper relationOrgToPersonMapper;

    @Autowired
    private MajorRepairPlanMemberService majorRepairPlanMemberService;
    @Autowired
    private CommonDataAuthRoleService commonDataAuthRoleService;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  MajorRepairOrgVO detail(String id,String pageCode) throws Exception {
        MajorRepairOrg majorRepairOrg =this.getById(id);
        MajorRepairOrgVO result = BeanCopyUtils.convertTo(majorRepairOrg,MajorRepairOrgVO::new);
        //临时处理
        LambdaQueryWrapperX<MajorRepairPlan> wrapperX = new LambdaQueryWrapperX<>(MajorRepairPlan.class);
        wrapperX.eq(MajorRepairPlan::getRepairRound, result.getRepairRound());
        wrapperX.select(MajorRepairPlan::getBaseCode, MajorRepairPlan::getRepairRound);
        List<MajorRepairPlan> list = majorRepairPlanService.list(wrapperX);
        if (!CollectionUtils.isEmpty(list)){
            Map<String, String> roundToBaseCode = list.stream().collect(Collectors.toMap(MajorRepairPlan::getRepairRound, MajorRepairPlan::getBaseCode));
            result.setBaseCode(roundToBaseCode.getOrDefault(result.getRepairRound(),""));
        }
        setEveryName(Collections.singletonList(result));
        LogRecordContext.putVariable("repairRound", result.getRepairRound());
        LogRecordContext.putVariable("orgName", result.getName());
        result.setEditPermissions(geEditPermissions(majorRepairOrg));
        return result;
    }

    public Boolean  geEditPermissions (MajorRepairOrg majorRepairOrg){
        String userId = CurrentUserHelper.getCurrentUserId();
        MajorRepairPlan majorRepairPlan= majorRepairPlanService.getNameByRepairRound(majorRepairOrg.getRepairRound());

        List<MajorRepairOrg> allMajorRepairOrgList = this.getList(majorRepairOrg.getRepairRound(), null);
        Map<String,List<MajorRepairOrg>> majMap = allMajorRepairOrgList.stream().filter(item->StrUtil.equals(item.getLevelType(),MajorRepairOrgEnum.LEVEL_TYPE_MANAGEMENT_ROLE.getCode())).collect(Collectors.groupingBy(MajorRepairOrg::getParentId));
        if(StrUtil.equals(userId,majorRepairPlan.getRepairManager())){
            return true;
        }
        if(StrUtil.equals(majorRepairOrg.getLevelType(),userId)){
            return true;
        }
        List<String> qsIds = new ArrayList<>();
        for(MajorRepairOrg repairOrg: allMajorRepairOrgList){
            if(StrUtil.equals(repairOrg.getLevelType(),MajorRepairOrgEnum.LEVEL_TYPE_REPAIR_ROLE.getCode())
            &&StrUtil.equals(userId,repairOrg.getRspUserId())){
                return true;
            }
           if(majorRepairOrg.getChainPath().contains(repairOrg.getId())){
               if(StrUtil.equals(repairOrg.getLevelType(),MajorRepairOrgEnum.LEVEL_TYPE_EXECUTION_SPECIALTY.getCode())||
                       StrUtil.equals(repairOrg.getLevelType(),MajorRepairOrgEnum.LEVEL_TYPE_EXECUTION_SPECIALTY.getCode())){
                   qsIds.add(repairOrg.getId());
               }
               if(StrUtil.equals(repairOrg.getLevelType(),MajorRepairOrgEnum.LEVEL_TYPE_MANAGEMENT_ROLE.getCode())){
                   List<MajorRepairOrg>  list = majMap.get(repairOrg.getId());
                   if(CollUtil.isNotEmpty(list)){
                       for(MajorRepairOrg majorRepairOrg1 :list  ){
                           if(StrUtil.equals(userId,majorRepairOrg1.getRspUserId())){
                               return true;
                           }
                       }
                   }
                   List<MajorRepairOrg>  parentList = majMap.get(repairOrg.getParentId());
                   if(CollUtil.isNotEmpty(parentList)){
                       for(MajorRepairOrg majorRepairOrg1 :parentList){
                           if(StrUtil.equals(userId,majorRepairOrg1.getRspUserId())){
                               return true;
                           }
                       }
                   }
               }
           }
        }
        if(CollUtil.isNotEmpty(qsIds)) {
            LambdaQueryWrapperX<CommonDataAuthRole> wrapperX = new LambdaQueryWrapperX<>(CommonDataAuthRole.class);
            wrapperX.in(CommonDataAuthRole::getDataId, qsIds);
            wrapperX.eq(CommonDataAuthRole::getAuthObject, "User");
            wrapperX.eq(CommonDataAuthRole::getObjectValue, userId);
            wrapperX.eq(CommonDataAuthRole::getPermissionCode,"write");
            wrapperX.select(CommonDataAuthRole::getDataId, CommonDataAuthRole::getPermissionCode);
            List<CommonDataAuthRole> list = commonDataAuthRoleService.list(wrapperX);
            if(CollUtil.isNotEmpty(list)){
                return true;
            }
        }
        return false;
    }
    /**
     *  新增
     *
     * * @param majorRepairOrgDTO
     */
    @Override
    public  String create(MajorRepairOrgDTO majorRepairOrgDTO) throws Exception {
        MajorRepairOrg majorRepairOrg =BeanCopyUtils.convertTo(majorRepairOrgDTO,MajorRepairOrg::new);
        String repairRound= majorRepairOrg.getRepairRound();
        String code= majorRepairOrg.getCode();
        String name= majorRepairOrg.getName();
        String levelType= majorRepairOrg.getLevelType();
        String parentId= majorRepairOrg.getParentId();
        majorRepairOrg.setId(classRedisHelper.getUUID(MajorRepairOrg.class.getSimpleName()));
        if(StringUtils.hasText(parentId)&& !Objects.equals("0",parentId)){
            MajorRepairOrg parent=this.getById(parentId);
            if(parent==null){
                throw new BaseException(HttpStatus.BAD_REQUEST.value(), "父级组织不存在，请先添加父级组织");
            }
            String chainPath=parent.getChainPath();
            chainPath=chainPath+","+majorRepairOrg.getId();
            majorRepairOrg.setChainPath(chainPath);
            majorRepairOrg.setSort(this.getMaxSort(parentId)+1);
            majorRepairOrg.setLevel(chainPath.split(",").length);
        }else if (Objects.equals("0",parentId)||StrUtil.isEmpty(parentId)){
            String chainPath="0,"+majorRepairOrg.getId();
            majorRepairOrg.setChainPath(chainPath);
            majorRepairOrg.setSort(this.getMaxSort(parentId)+1);
            majorRepairOrg.setLevel(2);
        }
        // 如果是执行专业 那么需要默认构建管理组
        if(Objects.equals(MajorRepairOrgEnum.LEVEL_TYPE_EXECUTION_SPECIALTY.getCode(),levelType)){
            MajorRepairOrg managementGroup = new MajorRepairOrg();
            managementGroup.setId(classRedisHelper.getUUID(MajorRepairOrg.class.getSimpleName()));
            managementGroup.setName("管理组");
            managementGroup.setParentId("0");
            managementGroup.setLevel(3);
            managementGroup.setSort(1);
            managementGroup.setRepairRound(repairRound);
            managementGroup.setLevelType(MajorRepairOrgEnum.LEVEL_TYPE_MANAGEMENT_ROLE.getCode());
            managementGroup.setParentId(majorRepairOrg.getId());
            managementGroup.setCode(code);
            managementGroup.setChainPath(majorRepairOrg.getChainPath()+","+majorRepairOrg.getId());
            this.save(managementGroup);
        }
        if(StrUtil.isEmpty(code) && StringUtils.hasText(name)){
            try {PinyinUtil.getPinyin(name,"");
                majorRepairOrg.setCode(PinyinUtil.getPinyin(name,""));
            }catch (Exception e){
                log.error("生成拼音异常：{}",e);
                majorRepairOrg.setCode(IdUtil.nanoId(8));
            }
        }
        this.existData(name,majorRepairOrg.getLevel(),null,repairRound,code,levelType);
        this.save(majorRepairOrg);
        String rsp=majorRepairOrg.getId();
        return rsp;
    }

    public void existData(String name,Integer level,String id,String repairRound,String code,String levelType ) throws Exception {
        if(Objects.equals(levelType, MajorRepairOrgEnum.LEVEL_TYPE_SPECIALTY_TEAM.getCode())
        || Objects.equals(levelType, MajorRepairOrgEnum.LEVEL_TYPE_EXECUTION_SPECIALTY.getCode())){
            LambdaQueryWrapperX<MajorRepairOrg> existCondition = new LambdaQueryWrapperX<>(MajorRepairOrg.class)
                    .eq(MajorRepairOrg::getRepairRound,repairRound)
                    .eq(MajorRepairOrg::getLevelType,levelType)
                    .eq(MajorRepairOrg::getLevel,level);
            existCondition.and(item->{
                item.eq(MajorRepairOrg::getCode,code);
            });
            if(StringUtils.hasText(id)){
                existCondition.eq(MajorRepairOrg::getId,id);
            }
            existCondition.select(MajorRepairOrg::getId);
            long count=this.count(existCondition);
            if(count >0){
                throw new BaseException(HttpStatus.BAD_REQUEST.value(), "当前层级组织编码已重复，同个组织不能重复添加");
            }
        }
    }
    /**
     *  编辑
     *
     * * @param majorRepairOrgDTO
     */
    @Override
    public Boolean edit(MajorRepairOrgDTO majorRepairOrgDTO) throws Exception {
        String name= majorRepairOrgDTO.getName();
        Integer level =majorRepairOrgDTO.getLevel();
        String levelType= majorRepairOrgDTO.getLevelType();
        this.existData(name,level,majorRepairOrgDTO.getId(),majorRepairOrgDTO.getRepairRound(),majorRepairOrgDTO.getCode(),levelType);
        LambdaUpdateWrapper<MajorRepairOrg> updateWrapper = new LambdaUpdateWrapper<>(MajorRepairOrg.class);
        updateWrapper.set(MajorRepairOrg::getName,name);
        updateWrapper.eq(MajorRepairOrg::getId,majorRepairOrgDTO.getId());
        this.update(updateWrapper);
        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        String repairOrgId= ids.get(0);
        LambdaQueryWrapperX<MajorRepairOrg> wrapper = new LambdaQueryWrapperX<>(MajorRepairOrg.class);
        wrapper.eq(MajorRepairOrg::getParentId,repairOrgId);
        // 管理组-角色
        wrapper.select(MajorRepairOrg::getId);
        long count = this.count(wrapper);
        if(count == 1){
            wrapper.clear();
            wrapper.like(MajorRepairOrg::getChainPath,repairOrgId);
            wrapper.eq(MajorRepairOrg::getLevelType, MajorRepairOrgEnum.LEVEL_TYPE_MANAGEMENT_ROLE.getCode());
            long  count1 = this.count(wrapper);
            if(count1 >1){
                throw new BaseException(HttpStatus.BAD_REQUEST.value(), "存在管理组，请先删除管理组");
            }else{
                this.remove(wrapper);
            }
        }
        if(count> 1){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "存在子级组织，请先删除子级组织");
        }
        long dataCount= relationOrgToJobService.existCountByRepairOrgIds(ids);
        if(dataCount >0){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "存在关联作业数据，请先删除关联数据");
        }
        //  判断人员，物资数据
        if(personMangeService.existData(ids)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "存在人员关联数据，请先删除关联数据");
        };
        if(relationOrgToMaterialService.isExistRelation(ids)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "存在物资关联数据，请先删除关联数据");
        };
        // 移除角色用户的通同事移除角色用户关系移除
        LambdaQueryWrapperX<MajorRepairOrg> wrapperX = new LambdaQueryWrapperX<>(MajorRepairOrg.class);
        wrapperX.in(MajorRepairOrg::getId,ids);
        wrapperX.select(MajorRepairOrg::getName,MajorRepairOrg::getId,MajorRepairOrg::getRspUserId,MajorRepairOrg::getRspUserName,MajorRepairOrg::getRepairRound,MajorRepairOrg::getCode);
        List<MajorRepairOrg> list = this.list(wrapperX);
        // 同步权限
        list.forEach(item->{
            majorRepairPlanMemberService.syncPermission(item.getCode(),null,null,item.getRepairRound(),item.getRspUserId(),item.getRspUserName());
        });

        LogRecordContext.putVariable("orgIds", String.join(",", ids));
        LogRecordContext.putVariable("orgNames",list.stream().map(MajorRepairOrg::getName).collect(Collectors.joining(",")));

        this.removeBatchByIds(ids);
        return true;
    }


    /**get
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<MajorRepairOrgVO> pages( Page<MajorRepairOrgDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<MajorRepairOrg> condition = new LambdaQueryWrapperX<>( MajorRepairOrg. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(MajorRepairOrg::getCreateTime);


        Page<MajorRepairOrg> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), MajorRepairOrg::new));

        PageResult<MajorRepairOrg> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<MajorRepairOrgVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<MajorRepairOrgVO> vos = BeanCopyUtils.convertListTo(page.getContent(), MajorRepairOrgVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }





    @Override
    public void  setEveryName(List<MajorRepairOrgVO> vos)throws Exception {
        Map<String,String> map= MajorRepairOrgEnum.getMap();
        vos.forEach(vo->{
            vo.setLevelTypeName(map.get(vo.getLevelType()));
        });
    }

    @Override
    public List<MajorRepairOrg> getList(String repairRound, String parentId) {
        List<MajorRepairOrgSimpleVO> majorRepairOrgSimpleVOS= majorRepairOrgMapper.listByRepairRoundAndParenId(repairRound,parentId);
        List<MajorRepairOrg> majorRepairOrgs =  BeanCopyUtils.convertListTo(majorRepairOrgSimpleVOS,MajorRepairOrg::new);
        this.packageTopParen(parentId,repairRound,majorRepairOrgs);
        return majorRepairOrgs;
    }


    /**
     *  组装顶层父级-- 大修轮次
     * @param parentId
     * @param repairRound
     * @param majorRepairOrgs
     */
    public void packageTopParen(String parentId,String repairRound,List<MajorRepairOrg> majorRepairOrgs){
        if(StrUtil.isEmpty(parentId)){
            MajorRepairPlan majorRepairPlan= majorRepairPlanService.getNameByRepairRound(repairRound);
            MajorRepairOrg majorRepairOrg = new MajorRepairOrg();
            majorRepairOrg.setRepairRound(repairRound);
            majorRepairOrg.setId("0");
            majorRepairOrg.setName(majorRepairPlan.getName());
            majorRepairOrg.setParentId(null);
            majorRepairOrg.setChainPath("0");
            majorRepairOrg.setLevel(1);
            majorRepairOrg.setSort(1);
            majorRepairOrg.setCode(repairRound);
            if(StringUtils.hasText(majorRepairPlan.getRepairManager())){
                SimpleUserVO simpleUserVO= userBaseApiService.getUserById(majorRepairPlan.getRepairManager());
                if(Objects.nonNull(simpleUserVO)){
                    majorRepairOrg.setRspUserCode(simpleUserVO.getCode());
                    majorRepairOrg.setRspUserId(majorRepairPlan.getRepairManager());
                    majorRepairOrg.setRspUserName(simpleUserVO.getName());
                }
            }
            majorRepairOrgs.add(majorRepairOrg);
        }
    }

    @Override
    public ObjectTreeInfoVO<TreeNodeVO<NodeVO<MajorTreeVO>>> majorTree(MajorTreeParamDTO majorTreeParamDTO) {
        String repairRound=majorTreeParamDTO.getRepairRound();
        LambdaQueryWrapperX<MajorRepairOrg> condition = new LambdaQueryWrapperX<>( MajorRepairOrg. class);
        condition.eq(MajorRepairOrg::getRepairRound,repairRound);
        condition.in(MajorRepairOrg::getLevelType, Arrays.asList(MajorRepairOrgEnum.LEVEL_TYPE_SPECIALTY_TEAM.getCode()
                ,MajorRepairOrgEnum.LEVEL_TYPE_EXECUTION_SPECIALTY.getCode()));
        condition.select(MajorRepairOrg::getId,MajorRepairOrg::getChainPath);
        List<MajorRepairOrg> majorRepairOrgs= this.list(condition);
        return this.packageTree(majorRepairOrgs,condition,repairRound);
    }

    @Override
    public ObjectTreeInfoVO<TreeNodeVO<NodeVO<MajorTreeVO>>> majorTreeByMajorRepairOrgIds(MajorTreeJobParamDTO majorTreeParamDTO) {
        List<String> jobNumberList= majorTreeParamDTO.getJobNumberList();
        if(CollectionUtils.isEmpty(jobNumberList)){
            return  new ObjectTreeInfoVO<>();
        }
        String repairRound= majorTreeParamDTO.getRepairRound();
        LambdaQueryWrapperX<MajorRepairOrg> condition = new LambdaQueryWrapperX<>( MajorRepairOrg. class);
        condition.eq(MajorRepairOrg::getRepairRound,repairRound);
        condition.in(MajorRepairOrg::getLevelType, Arrays.asList(MajorRepairOrgEnum.LEVEL_TYPE_SPECIALTY_TEAM.getCode()
                ,MajorRepairOrgEnum.LEVEL_TYPE_EXECUTION_SPECIALTY.getCode()));
        condition.select(MajorRepairOrg::getId,MajorRepairOrg::getChainPath);
        condition.leftJoin(RelationJobAssistToOrg.class, RelationJobAssistToOrg::getMajorRepairOrgId,MajorRepairOrg::getId);
        condition.in(RelationJobAssistToOrg::getJobNumber,jobNumberList);
        List<MajorRepairOrg> majorRepairOrgs= this.list(condition);

        LogRecordContext.putVariable("repairRound", repairRound);
        return  this.packageTree(majorRepairOrgs,condition,repairRound);

    }

    @Override
    public List<MajorRepairOrg> listByLevel(String speciality,int i, String repairRound) {
        LambdaQueryWrapperX<MajorRepairOrg> condition = new LambdaQueryWrapperX<>( MajorRepairOrg. class);
        condition.eq(MajorRepairOrg::getRepairRound,repairRound);
        condition.eq(MajorRepairOrg::getParentId,speciality);
        condition.eq(MajorRepairOrg::getLevelType, MajorRepairOrgEnum.LEVEL_TYPE_SPECIALTY_TEAM.getCode());
        condition.eq(MajorRepairOrg::getLevel,i);
        condition.select(MajorRepairOrg::getId,MajorRepairOrg::getChainPath);
        return this.list(condition);
    }

    @Override
    public List<String> jobMajorList(MajorTreeJobParamDTO majorTreeParamDTO) {
        String repairRound= majorTreeParamDTO.getRepairRound();
        if(StrUtil.isEmpty(repairRound)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "大修轮次不能为空");
        }
        List<String> jobNumberList= majorTreeParamDTO.getJobNumberList();
        if(CollectionUtils.isEmpty(jobNumberList)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "工单集合不能为空");
        }
        LambdaQueryWrapperX<MajorRepairOrg> condition = new LambdaQueryWrapperX<>( MajorRepairOrg. class);
        condition.eq(MajorRepairOrg::getRepairRound,repairRound);
        condition.select(MajorRepairOrg::getId,MajorRepairOrg::getChainPath);
        condition.leftJoin(RelationJobAssistToOrg.class, RelationJobAssistToOrg::getMajorRepairOrgId,MajorRepairOrg::getId);
        condition.in(RelationJobAssistToOrg::getJobNumber,jobNumberList);
        List<MajorRepairOrg> majorRepairOrgs = this.list(condition);
        if(CollectionUtils.isEmpty(majorRepairOrgs)){
            return List.of();
        }
        LogRecordContext.putVariable("repairRound", repairRound);
        return majorRepairOrgs.stream().map(MajorRepairOrg::getId).filter(Objects::nonNull).distinct().collect(Collectors.toList());

    }

    @Override
    public Boolean editById(MajorRepairOrgEditDTO majorRepairOrgDTO) {

        String name= majorRepairOrgDTO.getName();
        String id =majorRepairOrgDTO.getId();

        MajorRepairOrg majorRepair=  this.getById(id);
        if(Objects.isNull(majorRepair)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "组织不存在");
        }

        String oldRspUserId= majorRepair.getRspUserId();
        String oldRspUserName= majorRepair.getRspUserName();


        boolean b =Boolean.FALSE;
        if(StringUtils.hasText(name)){
            majorRepair.setName(name);
            b =Boolean.TRUE;
        }

        boolean editRspUser = false;

        String rspUserId = majorRepairOrgDTO.getRspUserId();
        if(StringUtils.hasText(rspUserId)){
            majorRepair.setRspUserId(rspUserId);
            b =Boolean.TRUE;
            editRspUser = true;
        }
        String rspUserCode = majorRepairOrgDTO.getRspUserCode();
        if(StringUtils.hasText(rspUserCode)){
            majorRepair.setRspUserCode(rspUserCode);
            b =Boolean.TRUE;
            editRspUser = true;
        }
        String rspUserName = majorRepairOrgDTO.getRspUserName();
        if(StringUtils.hasText(rspUserName)){
            majorRepair.setRspUserName(rspUserName);
            b =Boolean.TRUE;
            editRspUser = true;
        }
        if(editRspUser && Objects.equals(majorRepair.getLevelType(), MajorRepairOrgEnum.LEVEL_TYPE_REPAIR_ROLE.getCode())){
            String code = majorRepair.getCode();
            String repairRound = majorRepair.getRepairRound();
            majorRepairPlanMemberService.syncPermission(code,rspUserId,rspUserName,repairRound,oldRspUserId,oldRspUserName);
        }
        if(b){
            return   this.updateById(majorRepair);
        }
        throw  new BaseException(HttpStatus.BAD_REQUEST.value(),"大修组织责任人或名称不能为空");
    }

    @Override
    public Map<String, Set<String>> getRoleByOrgIdCurrentUser(String repairRound) {
        /**
         *
         * 获取当前人拥有的权限
         * @return
         */
        String userId= CurrentUserHelper.getCurrentUserId();
        LambdaQueryWrapperX<MajorRepairOrg> condition = new LambdaQueryWrapperX<>( MajorRepairOrg. class);
        condition.eq(MajorRepairOrg::getRepairRound,repairRound);
        condition.in(MajorRepairOrg::getLevelType,Arrays.asList(MajorRepairOrgEnum.LEVEL_TYPE_MANAGEMENT_ROLE.getCode()
                ,MajorRepairOrgEnum.LEVEL_TYPE_REPAIR_ROLE.getCode()));
        condition.eq(MajorRepairOrg::getRspUserId,userId);
        condition.select(MajorRepairOrg::getId,MajorRepairOrg::getChainPath);

        List<MajorRepairOrg> majorRepairOrgs= this.list(condition);
        if(CollectionUtils.isEmpty(majorRepairOrgs)){
            return Map.of();
        }

        for (MajorRepairOrg majorRepairOrg : majorRepairOrgs) {
            
        }

        return Map.of();
    }

    @Override
    public void updateByOrgId(String orgId,String repairRound) {

        List<MajorRepairOrgSimpleVO> majorRepairOrgSimpleVOList=  majorRepairOrgMapper.listByRepairRoundAndParenId(repairRound,null);
         if(CollectionUtils.isEmpty(majorRepairOrgSimpleVOList)){
             return;
         }
        Map<String,MajorRepairOrgSimpleVO> idToEntityMap = majorRepairOrgSimpleVOList.stream().collect(Collectors.toMap(MajorRepairOrgSimpleVO::getId, item->item));
         // 父级拥有的子集
        Map<String, List<MajorRepairOrgSimpleVO> > map = majorRepairOrgSimpleVOList.stream().filter(item->StringUtils.hasText(item.getParentId())).collect(Collectors.groupingBy(MajorRepairOrgSimpleVO::getParentId));

        List<MajorRepairOrgJobSimpleDTO> listSimpleByOrgId =   majorRepairOrgMapper.listSimpleByOrgId(orgId);

        List<MajorRepairOrgSimpleVO> list = BeanCopyUtils.convertListTo(listSimpleByOrgId,MajorRepairOrgSimpleVO::new);

        MajorRepairOrgSimpleVO majorRepairOrgSimpleVO = idToEntityMap.get(orgId);
        if(Objects.isNull(majorRepairOrgSimpleVO)){
            return;
        }
        this.setTime(list,majorRepairOrgSimpleVO);
        String chainPath= majorRepairOrgSimpleVO.getChainPath();
        String[] ids = chainPath.split(",");
        idToEntityMap.put(orgId,majorRepairOrgSimpleVO);

        List<MajorRepairOrgSimpleVO>  updateList = new ArrayList<>();
        updateList.add(majorRepairOrgSimpleVO);
        for (String id : ids) {
            List<MajorRepairOrgSimpleVO> children =   map.get(id);
            if(CollectionUtils.isEmpty(children)){
                continue;
            }
            children.forEach(item->{
                if(Objects.equals(item.getId(),id)){
                    MajorRepairOrgSimpleVO majorRepairOrgSimpleVO1 =   idToEntityMap.get(id);
                    if(Objects.nonNull(majorRepairOrgSimpleVO1)) {
                        this.setTime(children, majorRepairOrgSimpleVO1);
                        updateList.add(majorRepairOrgSimpleVO1);
                    }
                }
            });
        }
        if(!CollectionUtils.isEmpty(updateList)){
            this.updateBatchById(BeanCopyUtils.convertListTo(updateList,MajorRepairOrg::new));
        }

        //获取班组相关所有作业
//        List<MajorRepairOrgJobSimpleDTO> majorRepairOrgSimpleList =  jobManageMapper.getMajorRepairOrgSimpleVO(orgId);
//        MajorRepairOrgJobSimpleDTO date = getDate(majorRepairOrgSimpleList);

        //修改关常驻驻人员时间 只能循环修改
        relationOrgToPersonMapper.updatePersonTimeByList(updateList.stream().map(MajorRepairOrgSimpleVO::getId).distinct().collect(Collectors.toList()));
    }

    @Override
    public MajorRepairOrg getSimpleEntityByOrgId(String orgId) {
        LambdaQueryWrapperX<MajorRepairOrg> condition = new LambdaQueryWrapperX<>( MajorRepairOrg.class);
        condition.eq(MajorRepairOrg::getId,orgId);
        condition.select(MajorRepairOrg::getId, MajorRepairOrg::getCode, MajorRepairOrg::getName);
        condition.leftJoin(MajorRepairPlan.class,MajorRepairPlan::getRepairRound,MajorRepairOrg::getRepairRound);
        condition.selectAs(MajorRepairPlan::getBaseCode, MajorRepairOrg::getBaseCode);
        return this.getOne(condition);
    }


    private MajorRepairOrgJobSimpleDTO getDate(List<MajorRepairOrgJobSimpleDTO> param){
        MajorRepairOrgJobSimpleDTO majorRepairOrgJobSimpleDTO = new MajorRepairOrgJobSimpleDTO();
        // 获取时间 最早开始时间和最晚结束时间（实际结束时间如果存在一个为空则取空）
        for (int i = 0; i < param.size(); i++) {
            MajorRepairOrgJobSimpleDTO tmp = param.get(i);
            if (i==0){
                majorRepairOrgJobSimpleDTO.setBeginTime(tmp.getBeginTime());
                majorRepairOrgJobSimpleDTO.setEndTime(tmp.getEndTime());
                majorRepairOrgJobSimpleDTO.setActualBeginTime(tmp.getActualBeginTime());
                majorRepairOrgJobSimpleDTO.setActualEndTime(tmp.getActualEndTime());
                continue;
            }

            if (Objects.nonNull(tmp.getBeginTime())&&
                    (Objects.isNull(majorRepairOrgJobSimpleDTO.getBeginTime())||tmp.getBeginTime().before(majorRepairOrgJobSimpleDTO.getBeginTime()))){
                majorRepairOrgJobSimpleDTO.setBeginTime(tmp.getBeginTime());
            }

            if (Objects.nonNull(tmp.getEndTime())&&
                    (Objects.isNull(majorRepairOrgJobSimpleDTO.getEndTime())||tmp.getEndTime().after(majorRepairOrgJobSimpleDTO.getEndTime()))){
                majorRepairOrgJobSimpleDTO.setEndTime(tmp.getEndTime());
            }

            if (Objects.nonNull(tmp.getActualBeginTime())&&
                    (Objects.isNull(majorRepairOrgJobSimpleDTO.getActualBeginTime())||tmp.getActualBeginTime().before(majorRepairOrgJobSimpleDTO.getActualBeginTime()))){
                majorRepairOrgJobSimpleDTO.setActualBeginTime(tmp.getActualBeginTime());
            }

            if (Objects.isNull(tmp.getActualEndTime()) ||
                    (Objects.nonNull(majorRepairOrgJobSimpleDTO.getActualEndTime())&&tmp.getActualEndTime().after(majorRepairOrgJobSimpleDTO.getActualEndTime()))){
                majorRepairOrgJobSimpleDTO.setActualEndTime(tmp.getActualEndTime());
            }


            }
            return majorRepairOrgJobSimpleDTO;
        }


    public void setTime(List<MajorRepairOrgSimpleVO> listSimpleByOrgId, MajorRepairOrgSimpleVO majorRepairOrgSimpleVO){

        Date minTime = listSimpleByOrgId.stream().filter(Objects::nonNull).filter(item->Objects.nonNull(item.getBeginTime()))
                .map(MajorRepairOrgSimpleVO::getBeginTime)
                .min(Date::compareTo)
                .orElse(null);

        Date maxTime = listSimpleByOrgId.stream().filter(Objects::nonNull).filter(item->Objects.nonNull(item.getEndTime()))
                .map(MajorRepairOrgSimpleVO::getEndTime)
                .max(Date::compareTo)
                .orElse(null);

        Date minActTime = listSimpleByOrgId.stream().filter(Objects::nonNull).filter(item->Objects.nonNull(item.getActualBeginTime()))
                .map(MajorRepairOrgSimpleVO::getActualBeginTime)
                .min(Date::compareTo)
                .orElse(null);

        Date maxActTime = listSimpleByOrgId.stream().filter(Objects::nonNull).filter(item->Objects.nonNull(item.getActualEndTime()))
                .map(MajorRepairOrgSimpleVO::getActualEndTime)
                .max(Date::compareTo)
                .orElse(null);
        majorRepairOrgSimpleVO.setBeginTime(minTime);
        majorRepairOrgSimpleVO.setEndTime(maxTime);
        majorRepairOrgSimpleVO.setActualBeginTime(minActTime);
        majorRepairOrgSimpleVO.setActualEndTime(maxActTime);
    }

    public ObjectTreeInfoVO<TreeNodeVO<NodeVO<MajorTreeVO>>> packageTree(List<MajorRepairOrg> majorRepairOrgs,LambdaQueryWrapperX<MajorRepairOrg> condition,String repairRound){
        Set<String> allSet = new HashSet<>();
        majorRepairOrgs.forEach(item->{
            String chain= item.getChainPath();
            String[] ids = chain.split(",");
            allSet.addAll(Arrays.asList(ids));
        });
        condition.clear();
        if(CollectionUtils.isEmpty(allSet)){
            return new  ObjectTreeInfoVO<>();
        }
        condition.in(MajorRepairOrg::getId,new ArrayList<>(allSet));
        List<MajorRepairOrg> allList= this.list(condition);

        this.packageTopParen(null,repairRound,allList);

        List<NodeVO<MajorTreeVO>>  nodeVOS=new ArrayList<>();
        for (MajorRepairOrg majorRepairOrg : allList) {
            NodeVO<MajorTreeVO> majorTreeVONodeVO = new NodeVO<>();
            majorTreeVONodeVO.setId(majorRepairOrg.getId());
            majorTreeVONodeVO.setName(majorRepairOrg.getName());
            majorTreeVONodeVO.setCode(majorRepairOrg.getCode());
            majorTreeVONodeVO.setRspUserName(majorRepairOrg.getRspUserName());
            majorTreeVONodeVO.setRspUserId(majorRepairOrg.getRspUserId());
            majorTreeVONodeVO.setParentId(majorRepairOrg.getParentId());
            majorTreeVONodeVO.setNodeType(majorRepairOrg.getLevelType());
            nodeVOS.add(majorTreeVONodeVO);
        }

        TreeInfoProcessor<NodeVO<MajorTreeVO>> processor = new TreeInfoProcessor<>(
                nodeVOS,
                NodeVO::getId,
                NodeVO::getParentId,
                NodeVO::getRspUserId,
                CurrentUserHelper.getCurrentUserId(),
                new HashMap<>(),
                false,
                new HashMap<>()
        );
        ObjectTreeInfoVO<TreeNodeVO<NodeVO<MajorTreeVO>>> objectTreeInfoVO = new ObjectTreeInfoVO<>();
        List<TreeNodeVO<NodeVO<MajorTreeVO>>> root = processor.getRootList();
        objectTreeInfoVO.setTreeNodeVOList(root);
        objectTreeInfoVO.setParenIdList(processor.getParenIdList());
        return objectTreeInfoVO;
    }

    @Override
    public void addOrgProgress(JobProgressDTO jobProgressDTO) {
        JobProgress jobProgress = BeanCopyUtils.convertTo(jobProgressDTO, JobProgress::new);
        isExistsSameDate(jobProgressDTO);
        jobProgressMapper.insert(jobProgress);

        //关系插入
        OrgProgress orgProgress = new OrgProgress();
        orgProgress.setProgressId(jobProgress.getId());
        orgProgress.setRepairOrgId(jobProgressDTO.getRepairOrgId());
        orgProgressMapper.insert(orgProgress);

        MajorRepairOrg majorRepairOrg = majorRepairOrgMapper.selectById(jobProgressDTO.getRepairOrgId());

        LogRecordContext.putVariable("repairRound", majorRepairOrg.getRepairRound());
        LogRecordContext.putVariable("orgName", majorRepairOrg.getName());
    }

    @Override
    public Boolean deleteProgress(List<String> ids) {
        MajorRepairOrg majorRepairOrg = majorRepairOrgMapper.selectById(ids.get(0));
        LogRecordContext.putVariable("repairRound", majorRepairOrg.getRepairRound());
        LogRecordContext.putVariable("orgName", majorRepairOrg.getName());
        LogRecordContext.putVariable("ids", StrUtil.join( ",",ids));

        jobProgressMapper.deleteBatchIds(ids);
        return jobManageMapper.deleteProgressByProgressId(ids);
    }

    @Override
    public void editOrgProgress(JobProgressDTO jobProgressDTO) {
        isExistsSameDate(jobProgressDTO);
        JobProgress jobProgress = BeanCopyUtils.convertTo(jobProgressDTO, JobProgress::new);
        jobProgressMapper.updateById(jobProgress);

        MajorRepairOrg majorRepairOrg = majorRepairOrgMapper.selectById(jobProgressDTO.getRepairOrgId());
        LogRecordContext.putVariable("repairRound", majorRepairOrg.getRepairRound());
        LogRecordContext.putVariable("orgName", majorRepairOrg.getName());
        LogRecordContext.putVariable("id", jobProgress.getId());
    }

    private void isExistsSameDate(JobProgressDTO jobProgressDTO){
        String repairOrgId = jobProgressDTO.getRepairOrgId();
        Integer count = 0;
        if (StringUtils.hasText(jobProgressDTO.getId())){
            count = jobManageMapper.selectCountProgressDateJobIdByRepairOrgId(repairOrgId,jobProgressDTO.getWorkDate(),jobProgressDTO.getId());
        }else{
            count = jobManageMapper.selectCountProgressDateByRepairOrgId(repairOrgId,jobProgressDTO.getWorkDate());
        }
        Assert.isTrue(count<=0,()-> new BaseException(MyExceptionCode.ERROR_EXIST_DATA.getErrorCode(),"该日期工作进度已存在!"));
    }

    public int getMaxSort(String parentId){
        Integer count= majorRepairOrgMapper.getMaxSort(parentId);
        return  count== null ? 0 : count;
    }

}
