<script setup lang="ts">
import { BasicCard, BasicEditor } from 'lyra-component-vue3';
import {
  onMounted, reactive, ref,
} from 'vue';
import { documentDecompositionById } from '/@/views/pms/api/documentDecomposition';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});
const loading = ref(false);
const basicGridProps = reactive({
  list: [
    {
      label: '标题',
      field: 'name',
    },
    {
      label: '条目父级',
      field: 'parentName',
    },
    {
      label: '关联任务',
      field: 'taskName',
    },
    {
      label: '创建人',
      field: 'creatorName',
    },
    {
      label: '创建时间',
      field: 'createTime',
      formatTime: 'YYYY-MM-DD HH:mm:ss',
    },
    {
      label: '修改人',
      field: 'modifyName',
    },
    {
      label: '修改时间',
      field: 'modifyTime',
      formatTime: 'YYYY-MM-DD HH:mm:ss',
    },
    {
      label: '编写要求',
      field: 'remark',
      gridColumn: '1/5',
    },
  ],
  dataSource: {},
});
const refBasicEditor = ref();
onMounted(async () => {
  try {
    loading.value = true;
    const result = await documentDecompositionById(props.id);
    basicGridProps.dataSource = result;
    // 设置富文本
    refBasicEditor.value.setHtml(result.content);
    refBasicEditor.value.editor.disable();// 禁用编辑器
  } finally {
    loading.value = false;
  }
});
</script>

<template>
  <div v-loading="loading">
    <BasicCard
      title="基本信息"
      :gridContentProps="basicGridProps"
    />
    <BasicCard title="编制内容">
      <BasicEditor ref="refBasicEditor" />
    </BasicCard>
  </div>
</template>

<style scoped lang="less">

</style>
