package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class CenterDisPlayPlanVO {

    /**
     * 用人单位代号
     */
    @ApiModelProperty("用人单位代号")
    public String centerCode;

    /**
     * 用人单位名称
     */
    @ApiModelProperty("用人单位名称")
    public String centerName;

    @ApiModelProperty("合同名称")
    public String contractName;

    @ApiModelProperty("合同编号")
    public String contractNumber;

    @ApiModelProperty("合同状态")
    public Integer contractStatus;

    @ApiModelProperty("合同状态名称")
    public String contractStatusName;


    /**
     * 人力成本总预算
     */
    @ApiModelProperty("人力成本总预算")
    public BigDecimal personTotalBudget = BigDecimal.ZERO;

    /**
     * 人力成本实际执行
     */
    @ApiModelProperty("人力成本实际执行")
    public BigDecimal personActualTotalMoney = BigDecimal.ZERO;;


    /**
     * 岗级成本总预算
     */
    @ApiModelProperty("岗级成本总预算")
    public BigDecimal positionTotalMoney = BigDecimal.ZERO;;


    /**
     * 岗级成本实际执行
     */
    @ApiModelProperty("岗级成本实际执行")
    public BigDecimal positionActualTotalMoney = BigDecimal.ZERO;;

    /**
     * 岗位计划总人数
     */
    @ApiModelProperty("岗位计划总人数")
    public Integer personTotalCount = 0;;

    /**
     * 岗位实际执行人数
     */
    @ApiModelProperty("岗位实际人数")
    public Integer personActualPerson = 0;

    @ApiModelProperty("PlanList")
    public List<PlanVO> planList;

    @ApiModelProperty("表头数据")
    public Map<String,BigDecimal> sheetHeadsMap;

    @ApiModelProperty("状态")
    public Integer status;

    @ApiModelProperty("状态名称")
    public String statusName;

}
