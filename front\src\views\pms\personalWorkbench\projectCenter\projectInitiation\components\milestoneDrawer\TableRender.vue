<script setup lang="ts">
import {
  BasicButton, BasicUpload, openFile, OrionTable,
} from 'lyra-component-vue3';
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import { onMounted, ref, Ref } from 'vue';
import dayjs from 'dayjs';
import { Modal } from 'ant-design-vue';

const tableRef:Ref = ref();
const uploadRef:Ref = ref();
const selectRows:Ref<any[]> = ref([]);
const tableData:Ref<any[]> = ref([]);
const tableOptions = {
  showToolButton: false,
  showTableSetting: false,
  showSmallSearch: false,
  rowSelection: {},
  pagination: false,
  dataSource: tableData,
  columns: [
    {
      title: '文件名称',
      dataIndex: 'name',
      customRender({ text, record }) {
        return `${text}.${record.filePostfix}`;
      },
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
      customRender({ text }) {
        return text || '--';
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '--';
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 120,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '查看',
      onClick(fileData) {
        openFile(fileData);
      },
    },
    {
      text: '删除',
      modal(record) {
        return new Promise((resolve) => {
          const index = tableData.value.findIndex((item) => item.id === record.id);
          tableData.value.splice(index, 1);
          resolve(true);
        });
      },
    },
  ],
};

// 打开文件上传弹窗
function onUploadFile() {
  uploadRef.value.openModal(true);
}
// 保存回调
function saveChange(successAll) {
  tableData.value = tableData.value.concat(successAll.map((item, index) => {
    if (item?.id) {
      return item.result;
    }
    return {
      ...item.result,
      id: new Date().getTime().toString() + index,
      deleteId: true,
    };
  }));
}
// 表格多选
function selectionChange({ rows }) {
  selectRows.value = rows;
}
// 批量删除
function handleBatchDel() {
  Modal.confirm({
    title: '删除提示',
    content: '确认删除所选择的记录？',
    onOk() {
      tableData.value = tableData.value.filter((item) => !selectRows.value.some((v) => v.id === item.id));
    },
  });
}

defineExpose({
  getData: () => tableData.value.map((item) => {
    if (item.deleteId) {
      item.id = null;
    }
    return item;
  }),
  setData: (data) => tableData.value = data || [],
});
</script>

<template>
  <DetailsLayout
    title="节点支持性材料"
    isFormItem
    showTableHeader
  >
    <div style="height: 300px;overflow: hidden">
      <OrionTable
        ref="tableRef"
        :options="tableOptions"
        :onSelectionChange="selectionChange"
      >
        <template #toolbarLeft>
          <BasicButton
            type="primary"
            icon="orion-icon-upload"
            @click="onUploadFile"
          >
            上传附件
          </BasicButton>
          <BasicButton
            icon="sie-icon-del"
            :disabled="selectRows.length===0"
            @click="handleBatchDel"
          >
            删除
          </BasicButton>
        </template>
      </OrionTable>
    </div>
  </DetailsLayout>
  <BasicUpload
    ref="uploadRef"
    :max-number="100"
    :isClassification="false"
    :isToolRequired="false"
    :isButton="false"
    :onSaveChange="saveChange"
    button-text="上传附件"
  />
</template>

<style scoped lang="less">

</style>
