package com.chinasie.orion.controller;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.domain.dto.NcfFormpurchaseRequestDTO;
import com.chinasie.orion.domain.dto.SupplierRestrictedRecordDTO;
import com.chinasie.orion.domain.vo.SupplierRestrictedRecordVO;
import com.chinasie.orion.service.SupplierRestrictedRecordService;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * SupplierRestrictedRecord 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@RestController
@RequestMapping("/supplierRestrictedRecord")
@Api(tags = "受限事件记录")
public class SupplierRestrictedRecordController {

    @Autowired
    private SupplierRestrictedRecordService supplierRestrictedRecordService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "受限事件记录", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<SupplierRestrictedRecordVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        SupplierRestrictedRecordVO rsp = supplierRestrictedRecordService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 根据采购编号查询受限事件记录
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "根据采购编号查询受限事件记录")
    @RequestMapping(value = "/getRestrictedRecordByCode", method = RequestMethod.POST)
    @LogRecord( success = "【{USER{#logUserId}}】根据采购编号查询受限事件记录", type = "受限事件记录", subType = "根据采购编号查询受限事件记录", bizNo = "")
    public ResponseDTO<Page<SupplierRestrictedRecordVO>> getRestrictedRecordByCode(@RequestBody Page<SupplierRestrictedRecordDTO> pageRequest) throws Exception {
        Page<SupplierRestrictedRecordVO> rsp = supplierRestrictedRecordService.getByCode(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param supplierRestrictedRecordDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#supplierRestrictedRecordDTO.name}}】", type = "受限事件记录", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody SupplierRestrictedRecordDTO supplierRestrictedRecordDTO) throws Exception {
        String rsp = supplierRestrictedRecordService.create(supplierRestrictedRecordDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param supplierRestrictedRecordDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#supplierRestrictedRecordDTO.name}}】", type = "受限事件记录", subType = "编辑", bizNo = "{{#supplierRestrictedRecordDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody SupplierRestrictedRecordDTO supplierRestrictedRecordDTO) throws Exception {
        Boolean rsp = supplierRestrictedRecordService.edit(supplierRestrictedRecordDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "受限事件记录", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = supplierRestrictedRecordService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "受限事件记录", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = supplierRestrictedRecordService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "受限事件记录", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page/{mainTableId}", method = RequestMethod.POST)
    public ResponseDTO<Page<SupplierRestrictedRecordVO>> pages(@PathVariable("mainTableId") String mainTableId, @RequestBody Page<SupplierRestrictedRecordDTO> pageRequest) throws Exception {
        Page<SupplierRestrictedRecordVO> rsp = supplierRestrictedRecordService.pages(mainTableId, pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页查询受限事件供应商（不良供应商）
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页查询受限事件供应商（不良供应商）")
    @LogRecord(success = "【{USER{#logUserId}}】分页查询受限事件供应商（不良供应商）", type = "受限事件记录", subType = "分页查询受限事件供应商（不良供应商）", bizNo = "")
    @RequestMapping(value = "/getRestrictedRecordPages", method = RequestMethod.POST)
    public ResponseDTO<Page<SupplierRestrictedRecordVO>> getRestrictedRecordPages(@RequestBody Page<SupplierRestrictedRecordDTO> pageRequest) throws Exception {
        Page<SupplierRestrictedRecordVO> rsp = supplierRestrictedRecordService.getRestrictedRecordPages(pageRequest);
        return new ResponseDTO<>(rsp);
    }
    /**
     * 查询数量
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "查询数量")
    @LogRecord(success = "【{USER{#logUserId}}】查询数量", type = "受限事件记录", subType = "查询数量", bizNo = "")
    @RequestMapping(value = "/getNum", method = RequestMethod.POST)
    public ResponseDTO<Map<String, Integer>> getNum(@RequestBody Page<SupplierRestrictedRecordDTO> pageRequest) throws Exception {
        Map<String, Integer> rsp = supplierRestrictedRecordService.getNum(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("受限事件记录导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "受限事件记录", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        supplierRestrictedRecordService.downloadExcelTpl(response);
    }

    @ApiOperation("受限事件记录导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "受限事件记录", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = supplierRestrictedRecordService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("受限事件记录导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "受限事件记录", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = supplierRestrictedRecordService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消受限事件记录导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "受限事件记录", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = supplierRestrictedRecordService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("受限事件记录导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "受限事件记录", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody Page<NcfFormpurchaseRequestDTO> pageRequest, HttpServletResponse response) throws Exception {
        supplierRestrictedRecordService.exportByExcel(pageRequest, response);
    }
}
