package com.chinasie.orion.service.impl;

import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.domain.dto.ContractPayNodeConfirmNodeDTO;
import com.chinasie.orion.domain.entity.ContractPayNodeConfirmNode;
import com.chinasie.orion.domain.vo.ContractPayNodeConfirmNodeVO;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ContractPayNodeConfirmNodeRepository;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ContractPayNodeConfirmNodeService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * ContractPayNodeConfirmNode 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26 21:48:50
 */
@Service
public class ContractPayNodeConfirmNodeServiceImpl extends OrionBaseServiceImpl<ContractPayNodeConfirmNodeRepository, ContractPayNodeConfirmNode> implements ContractPayNodeConfirmNodeService {

    @Autowired
    private ContractPayNodeConfirmNodeRepository contractPayNodeConfirmNodeRepository;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public ContractPayNodeConfirmNodeVO detail(String id) throws Exception {
        ContractPayNodeConfirmNode contractPayNodeConfirmNode = contractPayNodeConfirmNodeRepository.selectById(id);
        ContractPayNodeConfirmNodeVO result = BeanCopyUtils.convertTo(contractPayNodeConfirmNode, ContractPayNodeConfirmNodeVO::new);
        return result;
    }

    /**
     * 新增
     * <p>
     * * @param contractPayNodeConfirmNodeDTO
     */
    @Override
    public ContractPayNodeConfirmNodeVO create(ContractPayNodeConfirmNodeDTO contractPayNodeConfirmNodeDTO) throws Exception {
        ContractPayNodeConfirmNode contractPayNodeConfirmNode = BeanCopyUtils.convertTo(contractPayNodeConfirmNodeDTO, ContractPayNodeConfirmNode::new);
        int insert = contractPayNodeConfirmNodeRepository.insert(contractPayNodeConfirmNode);
        ContractPayNodeConfirmNodeVO rsp = BeanCopyUtils.convertTo(contractPayNodeConfirmNode, ContractPayNodeConfirmNodeVO::new);
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param contractPayNodeConfirmNodeDTO
     */
    @Override
    public Boolean edit(ContractPayNodeConfirmNodeDTO contractPayNodeConfirmNodeDTO) throws Exception {
        ContractPayNodeConfirmNode contractPayNodeConfirmNode = BeanCopyUtils.convertTo(contractPayNodeConfirmNodeDTO, ContractPayNodeConfirmNode::new);
        int update = contractPayNodeConfirmNodeRepository.updateById(contractPayNodeConfirmNode);
        return SqlHelper.retBool(update);
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        int delete = contractPayNodeConfirmNodeRepository.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public Page<ContractPayNodeConfirmNodeVO> pages(Page<ContractPayNodeConfirmNodeDTO> pageRequest) throws Exception {
        Page<ContractPayNodeConfirmNode> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ContractPayNodeConfirmNode::new));

        PageResult<ContractPayNodeConfirmNode> page = contractPayNodeConfirmNodeRepository.selectPage(realPageRequest, null);

        Page<ContractPayNodeConfirmNodeVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ContractPayNodeConfirmNodeVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ContractPayNodeConfirmNodeVO::new);
        pageResult.setContent(vos);

        return pageResult;
    }
}
