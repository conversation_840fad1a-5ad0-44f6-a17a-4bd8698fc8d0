<script setup lang="ts">
import {
  BasicForm, FormSchema, useForm,
} from 'lyra-component-vue3';
import {
  onMounted, Ref, ref,
} from 'vue';
import { message } from 'ant-design-vue';
import Api from '/@/api';
import { isValidIDCard, validatePhoneNumber } from '/@/views/pms/utils/utils';

const props = defineProps<{
  record: Record<string, any> | null,
}>();

const validateAllIdCard = async (rule, value) => {
  if (!isValidIDCard(value)) {
    return Promise.reject('身份证号码格式错误');
  }

  return Promise.resolve();
};

const validateAllPhone = async (rule, value) => {
  if (value && !validatePhoneNumber(value)) {
    return Promise.reject('手机号码格式错误');
  }

  return Promise.resolve();
};

const schemas: FormSchema[] = [
  {
    field: 'idCard',
    label: '身份证号',
    rules: [
      {
        required: true,
        trigger: 'change',
        validator: validateAllIdCard,
      },
    ],
    component: 'Input',
    componentProps() {
      return {
        allowClear: false,
        async onChange(e:any) {
          let len = e.target.value;
          if (isValidIDCard(len)) {
            const result = await new Api('/pms/technical-support/detailValidate').fetch({ idCard: len }, '', 'POST');
            if (result) {
              if (result.userStatus === '在职') {
                message.error('该人员在岗，请核实信息后再试');
              } else {
                setFieldsValue({
                  userCode: result?.userCode,
                  fullName: result?.fullName,
                  sex: result?.sex,
                  nation: result?.nation,
                  dateOfBirth: result?.dateOfBirth,
                  politicalAffiliation: result?.politicalAffiliation,
                  birthPlace: result?.birthPlace,
                  userId: result?.userId,
                  id: result?.id,
                });
              }
            }
          }
        },
      };
    },
  },
  {
    field: 'userId',
    label: '',
    component: 'Input',
    show: () => false,
  },
  {
    field: 'id',
    label: '',
    component: 'Input',
    show: () => false,
  },
  {
    field: 'userCode',
    label: '员工号',
    component: 'Input',
    componentProps() {
      return {
        disabled: true,
      };
    },
  },
  {
    field: 'fullName',
    label: '姓名',
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'companyName',
    label: '公司',
    rules: [{ required: true }],
    component: 'Input',
    helpMessage: '承包商单位',
  },
  {
    field: 'sex',
    label: '性别',
    rules: [
      {
        required: true,
        message: '性别不能为空',
      },
    ],
    componentProps: {
      options: [
        {
          label: '男',
          value: 'true',
        },
        {
          label: '女',
          value: 'false',
        },
      ],
    },
    component: 'Select',
  },
  {
    field: 'nation',
    label: '民族',
    rules: [
      {
        required: true,
        message: '民族不能为空',
      },
    ],
    component: 'Input',
  },
  {
    field: 'dateOfBirth',
    label: '出生日期',
    required: true,
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    field: 'politicalAffiliation',
    label: '政治面貌',
    componentProps() {
      return {
        dictNumber: 'politicalAffiliation',
      };
    },
    component: 'SelectDictVal',
  },
  {
    field: 'joinWorkTime',
    label: '参加工作时间',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    field: 'homeTown',
    label: '籍贯',
    component: 'Input',
  },
  {
    field: 'birthPlace',
    label: '出生地',
    component: 'Input',
  },
  {
    field: 'addUnitTime',
    label: '加入本单位时间',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    field: 'deptCode',
    label: '用人部门',
    rules: [
      {
        required: true,
        message: '请选择用人部门',
      },
    ],
    component: 'TreeSelectOrg',
    componentProps() {
      return {
        fieldNames: {
          value: 'deptCode',
        },
        onChange(value: string, label: string) {
          setFieldsValue({
            deptName: label[0],
          });
        },
      };
    },
  },
  {
    field: 'deptName',
    label: '',
    component: 'Input',
    show: () => false,
  },
  {
    field: 'instituteCode',
    label: '研究所',
    component: 'TreeSelectOrg',
    componentProps() {
      return {
        fieldNames: {
          value: 'deptCode',
        },
        onChange(value: string, label: string) {
          setFieldsValue({
            instituteName: label[0],
          });
        },
      };
    },
  },
  {
    field: 'instituteName',
    label: '',
    component: 'Input',
    show: () => false,
  },
  {
    field: 'phone',
    label: '联系电话',
    component: 'Input',
    rules: [
      {
        required: false,
        trigger: 'change',
        validator: validateAllPhone,
      },
    ],
  },
  {
    field: 'addWorkTime',
    label: '到岗时间',
    component: 'DatePicker',
    rules: [
      {
        required: true,
        message: '到岗时间不能为空',
      },
    ],
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
];

const [register, { validate, setFieldsValue, getFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  props?.record?.id && getFormData();
});

const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/technical-support').fetch('', props?.record?.id, 'GET');
    await setFieldsValue({
      ...result,
    });
  } finally {
    loading.value = false;
  }
}

defineExpose({
  async onSubmit() {
    await validate();
    const formValues = getFieldsValue();
    const params = {
      ...formValues,
      id: props?.record?.id || formValues.id,
      sex: formValues.sex ? '男' : '女',
      userCode: props?.record?.userCode,
    };

    return new Promise((resolve, reject) => {
      new Api('/pms/technical-support').fetch(params, props?.record?.id ? 'edit' : 'add', props?.record?.id ? 'PUT' : 'POST').then((res) => {
        if (res) {
          message.success(props?.record?.id ? '编辑成功' : '保存成功');
        }
        resolve('');
      }).catch((err) => {
        reject(err);
      });
    });
  },
});
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">
:deep(.ant-input[disabled]) {
  color: #000 !important;
}

:deep(.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector) {
  color: #000 !important;
}

:deep(.ant-input-number-disabled) {
  color: #000 !important;
}
</style>
