package com.chinasie.orion.controller;

import com.chinasie.orion.domain.vo.PlanCountVo;
import com.chinasie.orion.domain.vo.PrincipalStatisticVo;
import com.chinasie.orion.domain.vo.SimpleVo;
import com.chinasie.orion.domain.vo.StatusCountVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.service.StatisticAnalysisService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/03/23/9:56
 * @description:
 */
@RestController
@RequestMapping("/statistic-analysis")
@Api(tags = "统计分析")
public class StatisticAnalysisController {

    @Resource
    private StatisticAnalysisService statisticAnalysisService;

    @ApiOperation("获取报表类型")
    @GetMapping("/report-forms-type")
    @LogRecord(success = "【{USER{#logUserId}}】获取报表类型", type = "统计分析", subType = "获取报表类型", bizNo = "")
    public ResponseDTO<List<SimpleVo>> getReportFormsTypeList() {
        return new ResponseDTO<>(statisticAnalysisService.getReportFormsTypeList());
    }

    @ApiOperation("获取报表内容列表")
    @GetMapping("/report-forms-content/{typeId}")
    @LogRecord(success = "【{USER{#logUserId}}】获取报表内容列表", type = "统计分析", subType = "获取报表内容列表", bizNo = "{{#typeId}}")
    public ResponseDTO<List<SimpleVo>> getReportFormsList(@PathVariable("typeId") String typeId) {
        return new ResponseDTO<>(statisticAnalysisService.getReportFormsList(typeId));
    }

    @ApiOperation("获取任务状态分布统计")
    @GetMapping("/plan-status/{projectId}")
    @LogRecord(success = "【{USER{#logUserId}}】获取任务状态分布统计", type = "统计分析", subType = "获取任务状态分布统计", bizNo = "{{#projectId}}")
    public ResponseDTO<PlanCountVo> getPlanStatusStatistic(@PathVariable("projectId") String projectId) throws Exception {
        return new ResponseDTO<>(statisticAnalysisService.getPlanStatusStatistic(projectId));
    }

    @ApiOperation("获取任务状态-负责人分布统计")
    @GetMapping("/plan-status-principal/{projectId}")
    @LogRecord(success = "【{USER{#logUserId}}】获取任务状态-负责人分布统计", type = "统计分析", subType = "获取任务状态-负责人分布统计", bizNo = "{{#projectId}}")
    public ResponseDTO<List<PrincipalStatisticVo>> getPlanPrincipalStatistic(@PathVariable("projectId") String projectId) throws Exception {
        return new ResponseDTO<>(statisticAnalysisService.getPlanPrincipalStatistic(projectId));
    }

    @ApiOperation("获取任务每日状态趋势")
    @GetMapping("/plan-everyday-status/{projectId}")
    @LogRecord(success = "【{USER{#logUserId}}】获取任务每日状态趋势", type = "统计分析", subType = "获取任务每日状态趋势", bizNo = "{{#projectId}}")
    public ResponseDTO<List<StatusCountVO>> getPlanEveryStatusStatistic(@PathVariable("projectId") String projectId) throws Exception {
        return new ResponseDTO<>(statisticAnalysisService.getPlanEveryStatusStatistic(projectId));
    }

    @ApiOperation("获取任务每日新增趋势")
    @GetMapping("/plan-everyday-increased/{projectId}")
    @LogRecord(success = "【{USER{#logUserId}}】获取任务每日新增趋势", type = "统计分析", subType = "获取任务每日新增趋势", bizNo = "{{#projectId}}")
    public ResponseDTO<Map<String, Integer>> getPlanEverydayIncreasedStatistic(@PathVariable("projectId") String projectId) throws Exception {
        return new ResponseDTO<>(statisticAnalysisService.getPlanEverydayIncreasedStatistic(projectId));
    }

    @ApiOperation("获取需求状态分布统计")
    @GetMapping("/demand-status/{projectId}")
    @LogRecord(success = "【{USER{#logUserId}}】获取需求状态分布统计", type = "统计分析", subType = "获取需求状态分布统计", bizNo = "{{#projectId}}")
    public ResponseDTO<StatusCountVO> getDemandStatusStatistic(@PathVariable("projectId") String projectId) throws Exception {
        return new ResponseDTO<>(statisticAnalysisService.getDemandStatusStatistic(projectId));
    }

    @ApiOperation("获取需求状态-负责人分布统计")
    @GetMapping("/demand-status-principal/{projectId}")
    @LogRecord(success = "【{USER{#logUserId}}】获取需求状态-负责人分布统计", type = "统计分析", subType = "获取需求状态-负责人分布统计", bizNo = "{{#projectId}}")
    public ResponseDTO<List<PrincipalStatisticVo>> getDemandPrincipalStatistic(@PathVariable("projectId") String projectId) throws Exception {
        return new ResponseDTO<>(statisticAnalysisService.getDemandPrincipalStatistic(projectId));
    }

    @ApiOperation("获取需求每日状态趋势")
    @GetMapping("/demand-everyday-status/{projectId}")
    @LogRecord(success = "【{USER{#logUserId}}】获取需求每日状态趋势", type = "统计分析", subType = "获取需求每日状态趋势", bizNo = "{{#projectId}}")
    public ResponseDTO<List<StatusCountVO>> getDemandEveryStatusStatistic(@PathVariable("projectId") String projectId) throws Exception {
        return new ResponseDTO<>(statisticAnalysisService.getDemandEveryStatusStatistic(projectId));
    }

    @ApiOperation("获取需求每日新增趋势")
    @GetMapping("/demand-everyday-increased/{projectId}")
    @LogRecord(success = "【{USER{#logUserId}}】获取需求每日新增趋势", type = "统计分析", subType = "获取需求每日新增趋势", bizNo = "{{#projectId}}")
    public ResponseDTO<Map<String, Integer>> getDemandEverydayIncreasedStatistic(@PathVariable("projectId") String projectId) throws Exception {
        return new ResponseDTO<>(statisticAnalysisService.getDemandEverydayIncreasedStatistic(projectId));
    }
}
