package com.chinasie.orion.service.impl;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.constant.DictConts;
import com.chinasie.orion.constant.RequirementTypeEnum;
import com.chinasie.orion.constant.RequirementTypeEnum;
import com.chinasie.orion.domain.entity.CertificateInfo;
import com.chinasie.orion.domain.entity.PmsJobPostLibrary;
import com.chinasie.orion.domain.entity.PmsJobPostRequirement;
import com.chinasie.orion.domain.dto.PmsJobPostRequirementDTO;
import com.chinasie.orion.domain.vo.PmsJobPostRequirementVO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.service.CertificateInfoService;
import com.chinasie.orion.service.PmsJobPostRequirementService;
import com.chinasie.orion.repository.PmsJobPostRequirementMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * PmsJobPostRequirement 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 10:19:25
 */
@Service
@Slf4j
public class PmsJobPostRequirementServiceImpl extends  OrionBaseServiceImpl<PmsJobPostRequirementMapper, PmsJobPostRequirement>   implements PmsJobPostRequirementService {
    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private DictRedisHelper dictRedisHelper;

    @Autowired
    private CertificateInfoService certificateInfoService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  PmsJobPostRequirementVO detail(String id,String pageCode) throws Exception {
        PmsJobPostRequirement pmsJobPostRequirement =this.getById(id);
        PmsJobPostRequirementVO result = BeanCopyUtils.convertTo(pmsJobPostRequirement,PmsJobPostRequirementVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param pmsJobPostRequirementDTO
     */
    @Override
    public  String create(PmsJobPostRequirementDTO pmsJobPostRequirementDTO) throws Exception {
        PmsJobPostRequirement pmsJobPostRequirement =BeanCopyUtils.convertTo(pmsJobPostRequirementDTO,PmsJobPostRequirement::new);
        if(Objects.equals(pmsJobPostRequirementDTO.getType(), RequirementTypeEnum.PMS_REQ_CERTIFICATE.getKey())){
            String cerId = pmsJobPostRequirementDTO.getCertificateId();
            if(StrUtil.isEmpty(cerId)){
                throw new BaseException(HttpStatus.BAD_REQUEST.value(), "参数异常：未传入证书唯一ID");
            }
            CertificateInfo certificateInfo = certificateInfoService.getById(cerId);
            pmsJobPostRequirement.setCertificateNumber(certificateInfo.getNumber());
        }
        this.save(pmsJobPostRequirement);
        String rsp=pmsJobPostRequirement.getId();
        return rsp;
    }

    /**
     *  编辑
     *
     * * @param pmsJobPostRequirementDTO
     */
    @Override
    public Boolean edit(PmsJobPostRequirementDTO pmsJobPostRequirementDTO) throws Exception {
        PmsJobPostRequirement pmsJobPostRequirement =BeanCopyUtils.convertTo(pmsJobPostRequirementDTO,PmsJobPostRequirement::new);
        if(Objects.equals(pmsJobPostRequirementDTO.getType(), RequirementTypeEnum.PMS_REQ_CERTIFICATE.getKey())){
            String cerId = pmsJobPostRequirementDTO.getCertificateId();
            if(StrUtil.isEmpty(cerId)){
                throw new BaseException(HttpStatus.BAD_REQUEST.value(), "参数异常：未传入证书唯一ID");
            }
            CertificateInfo certificateInfo = certificateInfoService.getById(cerId);
            pmsJobPostRequirement.setCertificateNumber(certificateInfo.getNumber());
        }
        this.updateById(pmsJobPostRequirement);
        String rsp=pmsJobPostRequirement.getId();
        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<PmsJobPostRequirementVO> pages(String mainTableId, Page<PmsJobPostRequirementDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<PmsJobPostRequirement> condition = new LambdaQueryWrapperX<>( PmsJobPostRequirement. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        if(StringUtils.hasText(mainTableId)){
            condition.eq(PmsJobPostRequirement::getJobPostId,mainTableId);
        }
        condition.orderByDesc(PmsJobPostRequirement::getCreateTime);

        Page<PmsJobPostRequirement> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), PmsJobPostRequirement::new));

        PageResult<PmsJobPostRequirement> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<PmsJobPostRequirementVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<PmsJobPostRequirementVO> vos = BeanCopyUtils.convertListTo(page.getContent(), PmsJobPostRequirementVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "岗位要求导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", PmsJobPostRequirementDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            PmsJobPostRequirementExcelListener excelReadListener = new PmsJobPostRequirementExcelListener();
        EasyExcel.read(inputStream,PmsJobPostRequirementDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<PmsJobPostRequirementDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("岗位要求导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<PmsJobPostRequirement> pmsJobPostRequirementes =BeanCopyUtils.convertListTo(dtoS,PmsJobPostRequirement::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("ncf::PmsJobPostRequirement-import::id", importId, pmsJobPostRequirementes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<PmsJobPostRequirement> pmsJobPostRequirementes = (List<PmsJobPostRequirement>) orionJ2CacheService.get("ncf::PmsJobPostRequirement-import::id", importId);
        log.info("岗位要求导入的入库数据={}", JSONUtil.toJsonStr(pmsJobPostRequirementes));

        this.saveBatch(pmsJobPostRequirementes);
        orionJ2CacheService.delete("ncf::PmsJobPostRequirement-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("ncf::PmsJobPostRequirement-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<PmsJobPostRequirement> condition = new LambdaQueryWrapperX<>( PmsJobPostRequirement. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(PmsJobPostRequirement::getCreateTime);
        List<PmsJobPostRequirement> pmsJobPostRequirementes =   this.list(condition);

        List<PmsJobPostRequirementDTO> dtos = BeanCopyUtils.convertListTo(pmsJobPostRequirementes, PmsJobPostRequirementDTO::new);

        String fileName = "岗位要求数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", PmsJobPostRequirementDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<PmsJobPostRequirementVO> vos)throws Exception {
        if(CollectionUtils.isEmpty(vos)){
            return;
        }
        List<DictValueVO> dictListByCode = dictRedisHelper.getDictListByCode(DictConts.REQUIREMENT_TYPE);

        Map<String,String> numberToName=new HashMap<>();
        for (DictValueVO dictValueVO : dictListByCode) {
            numberToName.put(dictValueVO.getNumber(),dictValueVO.getDescription());
        }
        List<DictValueVO> dictListByCode1 = dictRedisHelper.getDictListByCode(DictConts.TRAIN_TYPE_INFO_DICT);
        for (DictValueVO dictValueVO : dictListByCode1) {
            numberToName.put(dictValueVO.getNumber(),dictValueVO.getDescription());
        }
        List<DictValueVO> trainTypeDict = dictRedisHelper.getDictListByCode(DictConts.TRAIN_TYPE_INFO_DICT);
        for (DictValueVO dictValueVO : dictListByCode1) {
            numberToName.put(dictValueVO.getNumber(),dictValueVO.getDescription());
        }

        Map<String,String> cerNumberToName= certificateInfoService.allMap();
        vos.forEach(vo->{
            vo.setTypeName(numberToName.getOrDefault(vo.getType(),""));
            vo.setTrainName(numberToName.getOrDefault(vo.getTrainNumber(),""));
            vo.setCertificateName(cerNumberToName.getOrDefault(vo.getCertificateId(),""));
//            vo.setTrainName(numberToName.getOrDefault(vo.getCertificateNumber(),""));
        });
    }

    @Override
    public List<PmsJobPostRequirement> getRequirementByJobId(List<String> jobIdList) {
        LambdaQueryWrapperX<PmsJobPostRequirement> condition = new LambdaQueryWrapperX<>( PmsJobPostRequirement. class);
        condition.in(PmsJobPostRequirement::getJobPostId,jobIdList);
        condition.select(PmsJobPostRequirement::getJobPostId,PmsJobPostRequirement::getId,PmsJobPostRequirement::getType
                ,PmsJobPostRequirement::getCertificateNumber,PmsJobPostRequirement::getTrainNumber,PmsJobPostRequirement::getName);
        return this.list(condition);
    }

    @Override
    public List<PmsJobPostRequirement> getRequirementByJobCodeList(List<String> jobCodeList) {
        if(CollectionUtils.isEmpty(jobCodeList)){
            return  new ArrayList<>();
        }
        LambdaQueryWrapperX<PmsJobPostRequirement> condition = new LambdaQueryWrapperX<>( PmsJobPostRequirement. class);
        condition.leftJoin(PmsJobPostLibrary.class,PmsJobPostLibrary::getId,PmsJobPostRequirement::getJobPostId);
        condition.in(PmsJobPostLibrary::getNumber,jobCodeList);
        condition.select(PmsJobPostRequirement::getId, PmsJobPostRequirement::getName, PmsJobPostRequirement::getTrainNumber,PmsJobPostRequirement::getCertificateId
                ,PmsJobPostRequirement::getType,PmsJobPostRequirement::getJobPostId,PmsJobPostRequirement::getCertificateNumber);
        condition.selectAs(PmsJobPostLibrary::getNumber, PmsJobPostRequirement::getNumber);
        return this.list(condition);
    }


    public static class PmsJobPostRequirementExcelListener extends AnalysisEventListener<PmsJobPostRequirementDTO> {

        private final List<PmsJobPostRequirementDTO> data = new ArrayList<>();

        @Override
        public void invoke(PmsJobPostRequirementDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<PmsJobPostRequirementDTO> getData() {
            return data;
        }
    }


}
