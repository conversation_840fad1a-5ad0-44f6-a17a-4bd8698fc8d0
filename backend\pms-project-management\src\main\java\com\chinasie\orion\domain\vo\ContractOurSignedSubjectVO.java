package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;

import java.util.List;
/**
 * ContractOurSignedSubject VO对象
 *
 * <AUTHOR>
 * @since 2024-05-28 21:49:21
 */
@ApiModel(value = "ContractOurSignedSubjectVO对象", description = "甲方签约主体")
@Data
public class ContractOurSignedSubjectVO extends ObjectVO implements Serializable{

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    private String contractId;


    /**
     * 签约主体名称
     */
    @ApiModelProperty(value = "签约主体名称")
    private String signedMainName;

    /**
     * 签约主体客户名
     */
    @ApiModelProperty(value = "签约主体客户名")
    private String custPersonName;

    /**
     * 公司税号
     */
    @ApiModelProperty(value = "公司税号")
    private String companyDutyParagraph;


    /**
     * 主要联系人
     */
    @ApiModelProperty(value = "主要联系人")
    private String mainContactPerson;


    /**
     * 主要联系人电话
     */
    @ApiModelProperty(value = "主要联系人电话")
    private String mainContactPhone;


    /**
     * 技术联系人
     */
    @ApiModelProperty(value = "技术联系人")
    private String techContactPerson;


    /**
     * 技术联系人电话
     */
    @ApiModelProperty(value = "技术联系人电话")
    private String techContactPhone;

    /**
     * 技术联系部门
     */
    @ApiModelProperty(value = "技术联系部门")
    private String techContactDept;

    /**
     * 联系邮箱
     */
    @ApiModelProperty(value = "联系邮箱")
    private String contractEmail;


    /**
     * 联系地址
     */
    @ApiModelProperty(value = "联系地址")
    private String contactAddress;


    /**
     * 排序值
     */
    @ApiModelProperty(value = "排序值")
    private Integer sort;

    /**
     * 占比
     */
    @ApiModelProperty(value = "占比")
    private Double ratio;


    /**
     * 客户合同编号
     */
    @ApiModelProperty(value = "客户合同编号")
    @TableField(value = "cus_contract_number")
    private String cusContractNumber;

    @ApiModelProperty(value = "是否关联人士")
    private String isPerson;


    /**
     * 客户合同编号
     */
    @ApiModelProperty(value = "客户合同编号")
    private String contractNum;


    /**
     * 客户Id
     */
    @ApiModelProperty(value = "客户Id")
    private String customer;
}
