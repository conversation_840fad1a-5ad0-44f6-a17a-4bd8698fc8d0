package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ProjectSchemeContentBatchDTO;
import com.chinasie.orion.domain.dto.ProjectSchemeContentDTO;
import com.chinasie.orion.domain.dto.ProjectSchemeDTO;
import com.chinasie.orion.domain.entity.ObjectEntity;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.request.ListRequest;
import com.chinasie.orion.domain.vo.ProjectSchemeContentVO;
import com.chinasie.orion.domain.vo.ProjectSchemeVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectSchemeContentService;
import com.chinasie.orion.service.ProjectSchemeService;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.mzt.logapi.starter.annotation.LogRecords;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/projectSchemeWXMiniApp")
@Api(tags = "项目计划微信小程序")
public class ProjectSchemeWXMiniAppController {
    @Resource
    private ProjectSchemeService projectSchemeService;
    @Resource
    private UserRedisHelper userRedisHelper;

    @Resource
    private ProjectSchemeContentService schemeContentService;

    @ApiOperation("获取当前用户项目计划列表")
    @PostMapping(value = "/getPage/{type}")
    @LogRecord(success = "【{USER{#logUserId}}】获取当前用户项目计划列表", type = "项目计划微信小程序", subType = "获取当前用户项目计划列表", bizNo = "")

    public ResponseDTO<List<ProjectScheme>> projectSchemeList(@PathVariable String type, @RequestBody Page<ProjectSchemeDTO> schemeDTO) throws Exception {
        return ResponseDTO.success(projectSchemeService.getSchemeByUser(type,schemeDTO));
    }

    @ApiOperation("添加计划记录(批量)")
    @PostMapping(value = "/createBatch")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】添加计划记录(批量)", type = "项目计划微信小程序", subType = "添加计划记录(批量)", bizNo = "")
    public ResponseDTO<List<String>> createBatch(@RequestBody ProjectSchemeContentBatchDTO projectSchemeContentBatchDTO) throws Exception {
        return  ResponseDTO.success(schemeContentService.createBatch(projectSchemeContentBatchDTO));
    }


    @ApiOperation("计划完成确认")
    @PutMapping(value = "/finish")
    @LogRecord(success = "【{USER{#logUserId}}】计划完成确认", type = "项目计划微信小程序", subType = "计划完成确认", bizNo = "")
    public ResponseDTO<Boolean> finish(@RequestBody ProjectSchemeDTO projectSchemeDTO) throws Exception {
        return ResponseDTO.success(projectSchemeService.finish(projectSchemeDTO));
    }

    @ApiOperation("项目计划详情")
    @GetMapping(value = "/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】项目计划详情", type = "项目计划微信小程序", subType = "项目计划详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectSchemeVO> getDetail(@PathVariable("id") String id) throws Exception {
        return ResponseDTO.success(projectSchemeService.getSchemeDetail(id));
    }

    @ApiOperation("获取反馈内容")
    @GetMapping(value = "/getContentList")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】获取反馈内容", type = "项目计划微信小程序", subType = "获取反馈内容", bizNo = "{{#id}}")
    public ResponseDTO<List<ProjectSchemeContentVO>> createBatch(@RequestParam String id) throws Exception {
        return  ResponseDTO.success(schemeContentService.getList(id));
    }

    @ApiOperation("绩效根据审批人id获取计划分页")
    @PostMapping(value = "/getPageByexamineUser")
    @LogRecord(success = "【{USER{#logUserId}}】绩效根据审批人id获取计划分页", type = "项目计划微信小程序", subType = "绩效根据审批人id获取计划分页", bizNo = "")
    ResponseDTO<Page<ProjectSchemeVO>> getPageByProjectId(@RequestBody Page<ProjectSchemeDTO> pageRequest){
        Page<ProjectSchemeVO> page = projectSchemeService.getPageByexamineUser(pageRequest);
        return new ResponseDTO<>(page);
    }

    @ApiOperation("获取人员信息")
    @GetMapping(value = "/getUserVOList")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】获取人员信息", type = "项目计划微信小程序", subType = "获取人员信息", bizNo = "")
    public ResponseDTO<List<UserVO>> getUserVO() throws Exception {
        return  ResponseDTO.success(userRedisHelper.getAllUser(CurrentUserHelper.getOrgId()));
    }




}
