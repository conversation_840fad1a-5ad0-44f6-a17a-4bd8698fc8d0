<template>
  <div>
    <div v-if="status && status !== UploadResultStatus.UPLOADING">
      <div
        v-if="status === UploadResultStatus.UPLOADING"
        class="upload-loading"
      >
        上传中
      </div>
      <div
        v-else-if="status === UploadResultStatus.SUCCESS"
        class="upload-success"
      >
        上传成功
      </div>
      <div
        v-else-if="status === UploadResultStatus.ERROR"
        class="upload-error"
      >
        上传失败
      </div>
    </div>
    <div v-else-if="percent && status === UploadResultStatus.UPLOADING">
      <Progress
        :percent="percent"
        size="small"
        status="active"
      />
    </div>
    <div v-else>
      待上传
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, watch } from 'vue';
import { Progress } from 'ant-design-vue';
import { UploadResultStatus } from './enum';
export default defineComponent({
  name: 'UploadStatus',
  components: {
    Progress,
  },
  props: {
    status: {
      type: String,
      default: undefined,
    },
    percent: {
      type: Number,
      default: 0,
    },
  },
  setup(props) {
    return {
      UploadResultStatus,
    };
  },
});
</script>

<style lang="less" scoped>
  .upload-loading {
    color: ~`getPrefixVar('processing-color')`;
  }

  .upload-success {
    color:~`getPrefixVar('success-color')`;
  }

  .upload-error {
    color:~ `getPrefixVar('error-color')`;
  }
</style>
