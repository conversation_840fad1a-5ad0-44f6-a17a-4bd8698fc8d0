package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * PlanToParam DTO对象
 *
 * <AUTHOR>
 * @since 2024-02-22 21:17:17
 */
@ApiModel(value = "PlanToParamDTO对象", description = "计划和参数的关联关系")
@Data
public class PlanToParamDTO extends ObjectDTO implements Serializable {

    /**
     * 主id
     */
    @ApiModelProperty(value = "主id")
    private String fromId;

    /**
     * 副Id
     */
    @ApiModelProperty(value = "副Id")
    private String toId;


    @ApiModelProperty(value = "模板Id")
    private String modelId;

    /**
     * 参数实列ID
     */
    @ApiModelProperty(value = "参数实列ID")
    private String insId;

    /**
     * 拷贝类型
     */
    @ApiModelProperty(value = "拷贝类型")
    private String copyType;

}
