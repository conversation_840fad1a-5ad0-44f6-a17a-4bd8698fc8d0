<script setup lang="ts">
import { computed } from 'vue';
import StatisticSTable from './StatisticSTable.vue';

const props = defineProps<{
  data: Record<string, any>
}>();

const gridData = computed(() => [
  {
    label: '指标事件',
    field: 'pms_index_event',
  },
  {
    label: 'A类违章',
    field: 'pms_class_a_violation',
  },
  {
    label: 'B类违章',
    field: 'pms_class_b_violation',
  },
  {
    label: 'C类违章',
    field: 'pms_class_c_violation',
  },
  {
    label: 'F1',
    field: 'pms_f1_defect',
  },
  {
    label: 'F2',
    field: 'pms_f2_defect',
  },
].map((item) => ({
  ...item,
  value: props.data?.evenStatisticMap?.[item.field] || 0,
})));

const columns = [
  {
    title: '主题',
    dataIndex: 'eventTopic',
  },
  {
    title: '责任单位',
    dataIndex: 'rspDeptName',
    width: 120,
  },
  {
    title: '违章级别',
    dataIndex: 'assessmentLevelName',
    width: 120,
  },
];
</script>

<template>
  <div class="grid-container">
    <div class="title">
      安质环偏差
    </div>
    <div
      v-for="(item,index) in gridData"
      :key="index"
      :class="{deviation:item.value>0}"
    >
      <span>{{ item.label }}</span>
      <span>({{ item.value }})</span>
    </div>
  </div>
  <StatisticSTable
    :columns="columns"
    :data="data?.safetyQualityEnvVOList||[]"
  />
</template>

<style scoped lang="less">
.grid-container {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  grid-template-rows: 32px 64px 64px;
  gap: 2px;

  > div {
    background-color: #FCA764;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    &.deviation {
      background-color: #FAD56B;
    }
  }

  .title {
    display: flex;
    align-items: center;
    justify-content: center;
    grid-column-start: 1;
    grid-column-end: 4;
    background-color: #EFEFEF;
  }
}
</style>
