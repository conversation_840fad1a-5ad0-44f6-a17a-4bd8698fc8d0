package com.chinasie.orion.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

import java.util.List;

/**
 * PmsJobPostRequirement VO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 10:19:25
 */
@ApiModel(value = "PmsJobPostRequirementVO对象", description = "岗位要求")
@Data
public class PmsJobPostRequirementVO extends ObjectVO implements Serializable {

    /**
     * 岗位授权管理Id
     */
//    @ApiModelProperty(value = "岗位授权管理Id")
//    private String authorizeManageId;
    @ApiModelProperty(value = "岗位Id")
    @ExcelProperty(value = "岗位Id ", index = 0)
    private String jobPostId;
    /**
     * 要求类型
     */
    @ApiModelProperty(value = "要求类型")
    private String type;

    /**
     * 要求类型
     */
    @ApiModelProperty(value = "要求类型名称")
    private String typeName;

    /**
     * 要求名称
     */
    @ApiModelProperty(value = "要求名称")
    private String name;


    /**
     * 应取得证书
     */
    @ApiModelProperty(value = "应取得证书")
    private String certificateNumber;
    @ApiModelProperty(value = "证书名称")
    private String certificateName;


    /**
     * 应通过培训
     */
    @ApiModelProperty(value = "应通过培训")
    private String trainNumber;
    @ApiModelProperty(value = "培训名称")
    private String trainName;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;
    /**
     * 应取得证书
     */
    @ApiModelProperty(value = "应取得证书")
    private String certificateId;


}
