package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * MajorRepairPlanRole Entity对象
 *
 * <AUTHOR>
 * @since 2024-07-30 19:21:00
 */
@TableName(value = "pmsx_major_repair_plan_role")
@ApiModel(value = "MajorRepairPlanRoleEntity对象", description = "大修计划角色")
@Data

public class MajorRepairPlanRole extends  ObjectEntity  implements Serializable{

    /**
     * 角色code
     */
    @ApiModelProperty(value = "角色code")
    @TableField(value = "role_code")
    private String roleCode;

    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    @TableField(value = "major_repair_turn")
    private String majorRepairTurn;

    /**
     * 角色code
     */
    @ApiModelProperty(value = "角色层级")
    @TableField(value = "role_level")
    private String roleLevel;

}
