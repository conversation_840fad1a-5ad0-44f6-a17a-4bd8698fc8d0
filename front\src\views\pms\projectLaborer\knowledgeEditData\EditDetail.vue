<template>
  <a-modal
    v-model:visible="father.visible"
    :title="father.title"
    :mask-closable="false"
    :body-style="{ padding: '20px 20px 0' }"
    :width="1200"
    :footer="null"
  >
    <a-form
      :label-col="{ flex: '120px' }"
      :wrapper-col="{ span: 14 }"
    >
      <a-tabs>
        <a-tab-pane
          key="1"
          tab="基本信息"
        >
          <a-form-item
            label="模板名称"
            required
          >
            <a-input
              v-model:value="father.form.name"
              placeholder="请输入模板名称"
              disabled
              allow-clear
            />
          </a-form-item>
          <a-form-item label="分类名称">
            <a-tree-select
              v-model:value="father.form.classifyIdList"
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              :tree-data="treeSelectData"
              :replace-fields="{ children: 'children', title: 'name', key: 'id', value: 'id' }"
              placeholder="请选择分类名称"
              multiple
              disabled
            />
          </a-form-item>
          <a-row>
            <a-col :span="8">
              <a-form-item
                label="知识密级"
                required
              >
                <a-select
                  v-model:value="father.form.secretLevel"
                  :options="secretLevelList"
                  placeholder="请选择知识密级"
                  allow-clear
                  disabled
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item
                v-if="isSecurityLimit"
                label="密级期限"
                required
              >
                <a-select
                  v-model:value="father.form.securityLimit"
                  placeholder="请选择密级期限"
                  :options="deadlinelList"
                  allow-clear
                  disabled
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-form-item label="模板描述">
            <a-textarea
              v-model:value="father.form.desc"
              :auto-size="{ minRows: 3, maxRows: 5 }"
              placeholder="请输入模板描述"
              allow-clear
              disabled
            />
          </a-form-item>
        </a-tab-pane>
        <a-tab-pane
          key="2"
          tab="属性设置"
        >
          <a-form-item
            label="属性选择"
            required
          >
            <a-button
              type="primary"
              disabled
            >
              <PlusOutlined />
              选择属性
            </a-button>
          </a-form-item>
          <a-table
            class="mb-4"
            row-key="id"
            bordered
            :columns="columns1"
            :data-source="dataSource1"
            :pagination="false"
            :scroll="{ y: 300 }"
          >
            <template #number="{ index }">
              {{ index + 1 }}
            </template>
            <template #type="{ record }">
              <div v-if="record.type === 0">
                字符
              </div>
              <div v-if="record.type === 1">
                数值
              </div>
              <div v-if="record.type === 2">
                时间
              </div>
            </template>
            <template #required="{ text }">
              {{ text === 0 ? '不必填' : '必填' }}
            </template>
            <template #attributeTypeList="{ record }">
              {{ record.attributeTypeList.map((s) => s.name)?.join('；') }}
            </template>
          </a-table>
        </a-tab-pane>
        <a-tab-pane
          key="3"
          tab="标签设置"
        >
          <a-form-item label="标签">
            <a-select
              v-model:value="father.form.labelIdList"
              :options="labelList1"
              mode="multiple"
              placeholder="请选择标签"
              allow-clear
              disabled
            />
            <div class="mt-4">
              <a-tag
                v-for="s in labelList2"
                :key="s.value"
                color="#eaf1fa"
                style="margin-bottom: 5px"
              >
                <span style="color: #2a71ca"> {{ s.label }}</span>
              </a-tag>
            </div>
            <div class="mt-4">
              <a-button
                type="primary"
                disabled
              >
                <PlusOutlined />
                从标签库添加
              </a-button>
            </div>
          </a-form-item>
        </a-tab-pane>
        <a-tab-pane
          key="4"
          tab="文档模板设置"
        >
          <a-form-item label="文档模板">
            <a-select
              v-model:value="father.form.documentModelId"
              :options="docList"
              placeholder="请选择文档模板"
              allow-clear
              disabled
            />
          </a-form-item>
          <div class="flex mt-4 mb-4">
            <div class="doc-box">
              <div
                class="doc-data"
                v-html="docDetail.rtcHtml"
              />
            </div>
            <div class="flex-1 ml-4">
              <h1 class="text-xl">
                {{ docDetail.name }}
              </h1>
              <p
                class="mt-2"
                style="color: #999f"
              >
                {{ docDetail.desc }}
              </p>
            </div>
          </div>
        </a-tab-pane>
        <a-tab-pane
          key="5"
          tab="流程设置"
        >
          <a-button disabled>
            <PlusOutlined /> 选择流程
          </a-button>
          <a-table
            class="mb-4 mt-4"
            row-key="id"
            :show-index-column="false"
            :columns="columnFlow"
            :data-source="dataFlow"
            :max-height="300"
            :bordered="true"
            :pagination="false"
          >
            <template #businessKey="{ record }">
              {{ record.businessKey }}
            </template>
            <template #procInstName="{ record }">
              {{ record.procInstName }}
            </template>
            <template #action="{ record }">
              <a-button
                v-if="record.statusCode === 'NOT_STARTED'"
                type="link"
                disabled
              >
                启动
              </a-button>
              <a-button
                v-if="record.statusCode === 'RUNNING'"
                type="link"
                disabled
              >
                撤回
              </a-button>
              <a-button
                type="link"
                disabled
              >
                删除
              </a-button>
            </template>
          </a-table>
        </a-tab-pane>
        <a-tab-pane
          key="6"
          tab="权限设置"
        >
          <a-table
            row-key="type"
            :show-index-column="false"
            :max-height="300"
            :bordered="true"
            :pagination="false"
            :data-source="father.form.permissionJsonDtoList"
            :columns="columnsSetting"
          >
            <template #type="{ text }">
              <span v-if="text === 1">可阅读</span>
              <span v-if="text === 2">可编辑</span>
              <span v-if="text === 3">可下载</span>
            </template>
            <template #projectList="{ record }">
              <span
                v-for="(s, i) in record.projectList"
                :key="i"
              >{{ s.name }}；</span>
            </template>
            <template #organizationList="{ record }">
              <span
                v-for="(s, i) in record.organizationList"
                :key="i"
              >{{ s.name }}；</span>
            </template>
            <template #personList="{ record }">
              <span
                v-for="(s, i) in record.personList"
                :key="i"
              >{{ s.name }}；</span>
            </template>
            <template #isPublic="{ record }">
              <a-checkbox
                v-model:checked="record.isPublic"
                disabled
              >
                公开
              </a-checkbox>
              <a-button disabled>
                选择对象
              </a-button>
            </template>
          </a-table>
          <br>
        </a-tab-pane>
      </a-tabs>
    </a-form>
    <br>
  </a-modal>
</template>
<script lang="ts">
import {
  computed, defineComponent, reactive, toRefs,
} from 'vue';
import {
  Row,
  Col,
  Modal,
  Button,
  Table,
  Tabs,
  Form,
  Input,
  Select,
  TreeSelect,
  Tag,
  Checkbox,
} from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import { editTemplate } from './data2';
import Api from '/@/api';
import { addValueLabel } from '/@/views/pms/projectLaborer/utils';
import { useUserStore } from '/@/store/modules/user';
import { edit } from '/@/views/pms/projectLaborer/knowledgeEditData/data';

export default defineComponent({
  name: 'EditDetail',
  components: {
    PlusOutlined,
    AModal: Modal,
    ACheckbox: Checkbox,
    ATag: Tag,
    ARow: Row,
    ACol: Col,
    AButton: Button,
    ATable: Table,
    ATabs: Tabs,
    ASelect: Select,
    ATabPane: Tabs.TabPane,
    AInput: Input,
    AForm: Form,
    AFormItem: Form.Item,
    ATreeSelect: TreeSelect,
    ATextarea: Input.TextArea,
  },
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
  emits: ['submit'],
  setup(props) {
    const userStore = useUserStore();
    const state = reactive({
      father: props.data,
      treeSelectData: [],
      deadlinelList: [],
      secretLevelList: [],
      labelList1: [],
      labelList2: [],
      docList: [],
      dataFlow: [],
      attrList: [],
      columnFlow: edit.columns,
      columns1: editTemplate.columns1,
      columnsSetting: edit.columns1,
      dataSource1: computed(() =>
        state.attrList.filter((s) =>
          state.father.form.attributeSetting.attributeIdList.some((v) => v === s.id))),
      docDetail: computed(() => {
        const value = state.father.form.documentModelId;
        if (state.father.form.documentModelId && state.docList.length) {
          return state.docList.find((s) => s.value === value);
        }
        return {
          rtcHtml: '',
          name: '',
          desc: '',
        };
      }),
      isSecurityLimit: computed(() => {
        const secretLevel = state.father.form.secretLevel;
        if (secretLevel && state.secretLevelList.length) {
          return state.secretLevelList.find((s) => s.value === secretLevel).isSecurityLimit;
        }
        return false;
      }),
    });
    async function onUpdateFlow() {
      const query = {
        query: { deliveries: [{ deliveryId: state.father.form.id }] },
        userId: userStore.getUserInfo.id,
        pageNum: 1,
        pageSize: 10,
      };
      const data = await new Api('/workflow').fetch(query, 'act-prearranged/all/page', 'POST');
      state.dataFlow = data.content;
    }

    function init() {
      onUpdateFlow();
      new Api('/kms').fetch({
        show: false,
        type: 2,
      }, 'baseClassify', 'GET').then((res) => {
        state.treeSelectData = res;
      });
      new Api(`/pmi/data-classification/user/${userStore.getUserInfo.id}`)
        .fetch({ status: 1 }, '', 'GET')
        .then((res) => {
          state.secretLevelList = addValueLabel(res);
        });
      new Api('/kms').fetch('', 'kmsAttribute/list', 'POST').then((res) => {
        state.attrList = res;
      });
      new Api('/kms').fetch('', 'kmsLabel/list/all', 'GET').then((res) => {
        state.labelList1 = addValueLabel(res);
      });
      new Api('/kms').fetch('', 'kmsLabel/list', 'GET').then((res) => {
        state.labelList2 = addValueLabel(res);
      });
      new Api('/kms').fetch({}, 'kmsDocumentModel/list', 'POST').then((res) => {
        state.docList = addValueLabel(res);
      });
      new Api('/pmi/dict/dictd85e24b99d23444f8ccf3ae59bcde2d3')
        .fetch({}, '', 'GET')
        .then((res) => {
          res.map((s) => {
            s.value = s.id;
            s.label = s.description;
          });
          state.deadlinelList = res;
        });
    }

    init();
    return {
      ...toRefs(state),
    };
  },
});
</script>
<style lang="less" scoped>
  .doc-box {
    position: relative;
    width: 220px;
    border: 1px solid #ddd;
    height: 270px;
    margin-left: 120px;
    background-color: #dddddd;

    .doc-data {
      font-size: 40px;
      position: absolute;
      transform: scale(0.15, 0.3);
      left: -490px;
      top: -240px;
      width: 1200px;
      height: 751px;
      background-color: #fff;
      padding: 100px;
      overflow: auto;
    }
  }
</style>
