package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * SupplierContact DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@ApiModel(value = "SupplierContactDTO对象", description = "供应商联系人")
@Data
@ExcelIgnoreUnannotated
public class SupplierContactDTO extends ObjectDTO implements Serializable {

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @ExcelProperty(value = "供应商编码 ", index = 0)
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @ExcelProperty(value = "供应商名称 ", index = 1)
    private String supplierName;

    /**
     * 联系人姓
     */
    @ApiModelProperty(value = "联系人姓")
    @ExcelProperty(value = "联系人姓 ", index = 2)
    private String contactLastname;

    /**
     * 联系人名
     */
    @ApiModelProperty(value = "联系人名")
    @ExcelProperty(value = "联系人名 ", index = 3)
    private String contactFirstname;

    /**
     * 部门
     */
    @ApiModelProperty(value = "部门	")
    @ExcelProperty(value = "部门	 ", index = 4)
    private String department;

    /**
     * 职务
     */
    @ApiModelProperty(value = "职务")
    @ExcelProperty(value = "职务 ", index = 5)
    private String position;

    /**
     * 固定电话
     */
    @ApiModelProperty(value = "固定电话")
    @ExcelProperty(value = "固定电话 ", index = 6)
    private String landline;

    /**
     * 手机
     */
    @ApiModelProperty(value = "手机	")
    @ExcelProperty(value = "手机	 ", index = 7)
    private String mobile;

    /**
     * 分机
     */
    @ApiModelProperty(value = "分机")
    @ExcelProperty(value = "分机 ", index = 8)
    private String extension;

    /**
     * 传真
     */
    @ApiModelProperty(value = "传真")
    @ExcelProperty(value = "传真 ", index = 9)
    private String fax;

    /**
     * 默认联系人
     */
    @ApiModelProperty(value = "默认联系人")
    @ExcelProperty(value = "默认联系人 ", index = 10)
    private String defaultContact;

    /**
     * 电子邮箱
     */
    @ApiModelProperty(value = "电子邮箱")
    @ExcelProperty(value = "电子邮箱 ", index = 11)
    private String email;

    /**
     * 身份证号码
     */
    @ApiModelProperty(value = "身份证号码")
    @ExcelProperty(value = "身份证号码 ", index = 12)
    private String idNumber;

    /**
     * 负责区域/专业
     */
    @ApiModelProperty(value = "负责区域/专业")
    @ExcelProperty(value = "负责区域/专业 ", index = 13)
    private String responsibleArea;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 14)
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @ExcelProperty(value = "主表ID ", index = 15)
    private String mainTableId;


}
