package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.*;

import java.util.List;


public interface MajorRepairStatisticService {
    MajorPersonStatisticDTO getPersonStatistic(JobManageDTO jobManageDTO);

    MajorPersonStatisticDTO getMaterialStatistic(JobManageDTO jobManageDTO);

    void getCountToPerson(MajorPersonStatisticDTO majorStatisticDTO, List<String> uniquePersonIds);

    void getCountToMaterial(MajorPersonStatisticDTO majorStatisticDTO, List<String> uniqueMaterialIds);

    MajorRepairPlanBoardStatisticDTO getMajorStatistic(MajorRepairPlanDTO majorRepairPlanDTO) throws Exception;


}
