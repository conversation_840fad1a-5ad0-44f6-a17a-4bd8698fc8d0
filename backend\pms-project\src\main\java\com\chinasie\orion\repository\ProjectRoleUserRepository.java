package com.chinasie.orion.repository;

import com.chinasie.orion.domain.dto.UserProjectRoleDTO;
import com.chinasie.orion.domain.entity.ProjectRoleUser;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/06/10:47
 * @description:
 */
@Mapper
public interface ProjectRoleUserRepository extends OrionBaseMapper<ProjectRoleUser> {


    @Select({
            "<script>",
            "select  pru.user_id from pms_project_role pr " ,
            "join pms_project_role_user pru  on pr.id = pru.project_role_id ",
            "where pr.project_id =#{projectId} and pr.`code`=#{code} AND pru.user_id =#{userId} AND pr.logic_status=1 ",
            "</script>"
    })
    List<String> getUserListByProjectPm(@Param("projectId")String projectId,@Param("code") String code,@Param("userId") String userId);

    @Select({
            "<script>",
            "select  pru.project_id from  pms_project_role_user pru ",
            "where  pru.user_id =#{userId} AND pru.logic_status=1 ",
            "</script>"
    })
    List<String> getProjectIdList(String userId);


    @Select({
            "<script>",
            "select  pru.user_id as userId,pr.project_id as projectId ,ro.`code` as roleCode from pms_project_role pr "
            ," inner join pms_project_role_user pru  on pr.id = pru.project_role_id and pru.user_id = #{userId}  and pru.project_id in ",
            "<foreach collection='projectIdList' item='item' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            " inner join pmi_role  ro on  pr.business_id=ro.id  ",
            "</script>"
    })
    List<UserProjectRoleDTO> getUserCodeToProjectId(@Param("projectIdList") List<String> projectIdList,@Param("userId") String userId);
}
