<template>
  <BasicScrollbar>
    <!--表格-->
    <MonitorTable
      v-if="nodeId"
      :id="nodeId"
      ref="signedMainRef"
      :title="baseInfo?.label"
      :isShow="flag"
      :dataId="dataId"
      :onEdit="onEdit"
      :nodeInfo="baseInfo"
      :projectId="projectId"
      :updateAction="updateAction"
    />
    <!--流程公共区域-->
    <FlowInfo
      v-if="baseInfo"
      :key="flowInfoRefreshKey"
      :baseInfo="baseInfo"
      :projectId="projectId"
    />
  </BasicScrollbar>
</template>
<script setup lang="ts">
import {
  computed, ref, unref,
} from 'vue';
import {
  BasicScrollbar, openDrawer, randomString,
} from 'lyra-component-vue3';
import MonitorTable from './MonitorTable.vue';
import EditInfo from './EditInfo.vue';
import FlowInfo from './FlowInfo.vue';

const props = withDefaults(defineProps<{
  type: string | undefined,
  projectId: string | undefined,
  data:object,
  updateAction: (id:string)=>void,
}>(), {
  projectId: undefined,
  formType: 'batch',
});

// 定义基础信息
const baseInfo = computed(() => props.data);

// 计算当前节点 ID
const nodeId = computed(() => props.data?.id);

// 定义需要排除的节点 ID 数组
const excludedNodeIds = [
  '0-1',
  '0-2',
  '0-3',
  '1-1',
  '1-2',
  '1-3',
  '1-4',
  '1-5',
  '1-6',
  '3-2-3',
  '4-1',
];

// 计算 flag 值
const flag = computed(() => !excludedNodeIds.includes(nodeId.value));
// 计算数据 ID
const dataId = computed(() => props.data?.dataId);
const signedMainRef = ref(null);
const flowInfoRefreshKey = ref(randomString());

function onEdit() {
  const formRef = ref();
  openDrawer({
    title: `修改${unref(baseInfo)?.label ?? ''} `,
    zIndex: 95,
    content(h) {
      return h(EditInfo, {
        ref: formRef,
        baseInfo: unref(baseInfo),
        projectId: unref(props.projectId),
      });
    },
    async onOk() {
      await formRef.value?.save();
      flowInfoRefreshKey.value = randomString();
    },
  });
}
</script>
  <style lang="less" scoped>
  .form-content {
    display: flex;
    height: 100%;
    flex-direction: column;
    .form-content-flex {
      flex: 1 1 auto;
    }
  }
  :deep(.ant-form-item) {
    display: block;
  }
  :deep(.ant-form-item-control) {
    width: 100% !important;
  }
  .action-btn {
    padding: 0 var(--ant-content-padding-left) !important;
  }
  </style>
