package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * IncomePlanDataControl Entity对象
 *
 * <AUTHOR>
 * @since 2024-09-29 18:59:40
 */
@TableName(value = "pmsx_income_plan_data_control")
@ApiModel(value = "IncomePlanDataControlEntity对象", description = "收入计划数据管控")
@Data

public class IncomePlanDataControl extends  ObjectEntity  implements Serializable{

    /**
     * 锁定状态
     */
    @ApiModelProperty(value = "锁定状态")
    @TableField(value = "lock_status")
    private String lockStatus;

    /**
     * 专业中心
     */
    @ApiModelProperty(value = "专业中心")
    @TableField(value = "expertise_center")
    private String expertiseCenter;

    /**
     * 专业中心收入计划金额
     */
    @ApiModelProperty(value = "专业中心收入计划金额")
    @TableField(value = "expertise_center_money")
    private BigDecimal expertiseCenterMoney;

    /**
     * 收入计划填报Id
     */
    @ApiModelProperty(value = "收入计划填报Id")
    @TableField(value = "income_plan_id")
    private String incomePlanId;

}
