package com.chinasie.orion.controller.reporting;

import com.chinasie.orion.domain.dto.reporting.ProjectDailyStatementContentDTO;
import com.chinasie.orion.domain.vo.reporting.ProjectDailyStatementContentVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.reporting.ProjectDailyStatementContentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.dto.ResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.annotation.Resource;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
/**
 * <p>
 * ProjectDailyStatementContent 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-14 15:21:01
 */
@RestController
@RequestMapping("/projectDaily-statementContent")
@Api(tags = "日报内容表-项目的接口已过时请查看通过管理相关接口")
public class ProjectDailyStatementContentController {

    @Resource
    private ProjectDailyStatementContentService projectDailyStatementContentService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Deprecated
    public ResponseDTO<ProjectDailyStatementContentVO> detail(@PathVariable(value = "id") String id) throws Exception {
        ProjectDailyStatementContentVO rsp = projectDailyStatementContentService.detail(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectDailyStatementContentDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @Deprecated
    public ResponseDTO<ProjectDailyStatementContentVO> create(@RequestBody ProjectDailyStatementContentDTO projectDailyStatementContentDTO) throws Exception {
        ProjectDailyStatementContentVO rsp =  projectDailyStatementContentService.create(projectDailyStatementContentDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectDailyStatementContentDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @Deprecated
    public ResponseDTO<Boolean> edit(@RequestBody  ProjectDailyStatementContentDTO projectDailyStatementContentDTO) throws Exception {
        Boolean rsp = projectDailyStatementContentService.edit(projectDailyStatementContentDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @Deprecated
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectDailyStatementContentService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/pages", method = RequestMethod.POST)
    @Deprecated
    public ResponseDTO<Page<ProjectDailyStatementContentVO>> pages(@RequestBody Page<ProjectDailyStatementContentDTO> pageRequest) throws Exception {
        Page<ProjectDailyStatementContentVO> rsp =  projectDailyStatementContentService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }
}
