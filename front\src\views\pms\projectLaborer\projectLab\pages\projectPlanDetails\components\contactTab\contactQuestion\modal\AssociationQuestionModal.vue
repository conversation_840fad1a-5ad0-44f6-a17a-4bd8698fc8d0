<template>
  <div class="table-content">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      @selectionChange="selectionChange"
    />
  </div>
</template>

<script lang="ts" setup>
import { h, ref, Ref } from 'vue';
import {
  BasicButton,
  Layout,
  OrionTable, DataStatusTag,
} from 'lyra-component-vue3';
import { stampDate } from '/@/utils/dateUtil';
import Api from '/@/api';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

const props = withDefaults(defineProps<{
    projectId:string,
    formId:string,
}>(), {
  projectId: '',
  formId: '',
});
const selectRowKeys:Ref<string[]> = ref([]);
function selectionChange(data) {
  selectRowKeys.value = data.keys;
}
const tableRef = ref();
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: {},
  showSmallSearch: true,
  pagination: false,
  smallSearchField: ['name'],
  api: async (params) => {
    params.projectId = props.projectId;
    if (Array.isArray(params.searchConditions) && params.searchConditions.length > 0) {
      params.keyword = params.searchConditions[0][0].values[0];
    }
    let res = await new Api('/pms').fetch(params, 'question-management/search/list', 'POST');
    return res.planSearchVos;
  },
  columns: [
    {
      title: '编号',
      dataIndex: 'number',
      align: 'left',
      key: 'number',

      width: '150px',
      // sorter: true,
      ellipsis: true,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',

      align: 'left',
      // slots: { customRender: 'name' },
      // sorter: true,
      ellipsis: true,
    },

    {
      title: '负责人',
      dataIndex: 'principalName',
      key: 'principalName',
      align: 'left',
      // sorter: true,
      ellipsis: true,
      slots: { customRender: 'principalName' },
      width: '150px',
    },
  ],
  //  beforeFetch,
});
async function saveData() {
  const selectRows = tableRef.value.getSelectRows();
  if (selectRows.length === 0) {
    message.warning('请选择问题');
    return Promise.reject('');
  }
  const params = {
    toId: props.formId,
    fromIds: selectRows.map((item) => item.id),
  };
  await new Api('/pms').fetch(params, 'projectScheme/relation/question', 'POST');
  message.success('关联问题成功');
}
function getSelectData() {
  return tableRef.value.getSelectRows();
}
defineExpose({
  saveData,
});
</script>

<style lang="less" scoped>
.table-content{
  height: 100%;
  overflow: hidden;
}
</style>
