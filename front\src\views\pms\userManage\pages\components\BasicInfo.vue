<script setup lang="ts">
import {
  BasicCard, isPower, openDrawer, openFile, OrionTable,
} from 'lyra-component-vue3';
import {
  h, inject, reactive, Ref,
} from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import { useRouter } from 'vue-router';
import { Popover } from 'ant-design-vue';
import TableDrawer from '/@/views/pms/technicalStaffAllocation/components/TableDrawer.vue';

const router = useRouter();
const powerData: Ref = inject('powerData');
const detailsData: Record<string, any> = inject('detailsData');
const basicInfo = reactive({
  list: [
    {
      label: '员工号',
      field: 'number',
    },
    {
      label: '姓名',
      field: 'name',
    },
    {
      label: '性别',
      field: 'sex',
    },
    {
      label: '人员性质',
      field: 'nature',
    },
    {
      label: '公司',
      field: 'companyName',
    },
    {
      label: '部门',
      field: 'deptName',
    },
    {
      label: '研究所',
      field: 'instituteName',
    },
    {
      label: '民族',
      field: 'nation',
    },
    // {
    //   label: '身份证号',
    //   field: 'idCard',
    // },
    // {
    //   label: '出生日期',
    //   field: 'dateOfBirth',
    //   format: 'YYYY-MM-DD',
    // },
    {
      label: '政治面貌',
      field: 'politicalAffiliation',
    },
    {
      label: '籍贯',
      field: 'homeTown',
    },
    {
      label: '出生地',
      field: 'birthPlace',
    },
    {
      label: '现任职务',
      field: 'nowPosition',
    },
    {
      label: '职称',
      field: 'jobTitle',
    },
    {
      label: '参工时间',
      field: 'joinWorkTime',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '加入中广核时间',
      field: 'addZghTime',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '加入本单位时间',
      field: 'addUnitTime',
      formatTime: 'YYYY-MM-DD',
    },
  ],
  dataSource: detailsData,
});

const trainTableOptions = {
  showToolButton: false,
  showTableSetting: false,
  showSmallSearch: false,
  isSpacing: false,
  pagination: false,
  maxHeight: 300,
  resizeHeightOffset: -35,
  api: () => new Api(`/pms/train-person/person/train/list?userCode=${detailsData?.number}`).fetch('', '', 'POST'),
  columns: [
    {
      title: '培训名称',
      dataIndex: 'name',
    },
    {
      title: '培训基地',
      dataIndex: 'baseName',
      width: 120,
    },
    {
      title: '培训课时',
      dataIndex: 'lessonHour',
      width: 80,
    },
    {
      title: '培训完成日期',
      dataIndex: 'endDate',
      width: 120,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '培训讲师',
      dataIndex: 'trainLecturer',
      width: 100,
    },
    {
      title: '到期日期',
      dataIndex: 'expireTime',
      width: 120,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '是否等效',
      dataIndex: 'isEquivalent',
      width: 100,
      customRender({ text }) {
        return text ? '是' : '否';
      },
    },
    {
      title: '等效认定基地',
      dataIndex: 'equivalentVOList',
      width: 100,
      customRender({ text }) {
        if (isPower('PMS_RXGLXQ_container_01_button_01', powerData.value)) {
          return h('div', {
            class: 'flex-te action-btn',
            onClick() {
              openDrawer({
                title: '岗位等效认定',
                width: 1000,
                content() {
                  return h(TableDrawer, {
                    data: text || [],
                  });
                },
                footer: {
                  isOk: false,
                  // @ts-ignore
                  cancelText: '关闭',
                },
              });
            },
          }, '查看');
        }
        return '';
      },
    },
    {
      title: '培训内容',
      dataIndex: 'content',
    },
    {
      title: '培训记录',
      dataIndex: 'fileVOList',
      width: 100,
      customRender({ text }) {
        if (isPower('PMS_RXGLXQ_container_01_button_02', powerData.value)) {
          return h(Popover, { title: '附件' }, {
            default: () => h('div', { class: 'flex-te action-btn' }, '附件列表'),
            content: () => (text instanceof Array ? text : [])?.map((item: any) => h('p', {
              class: 'action-btn',
              onClick() {
                openFile(item);
              },
            }, item.name)),
          });
        }
        return '';
      },
    },
  ],
};

const certificateTableOptions = {
  showToolButton: false,
  showTableSetting: false,
  showSmallSearch: false,
  resizeHeightOffset: -35,
  isSpacing: false,
  pagination: false,
  maxHeight: 300,
  api: () => new Api(`/pms/basic-user-certificate/user/certificate/list?userCode=${detailsData?.number}`).fetch('', '', 'POST'),
  columns: [
    {
      title: '证书类型',
      dataIndex: 'certificateTypeName',
    },
    {
      title: '证书名称',
      dataIndex: 'certificateName',
      customRender({ record, text }) {
        if (isPower('PMS_RXGLXQ_container_01_button_03', powerData.value)) {
          return h('div', {
            class: 'flex-te action-btn',
            onClick: () => navDetails(record?.certificateId),
          }, text);
        }
        return text;
      },
    },
    {
      title: '证书等级',
      dataIndex: 'certificateLevelName',
      width: 120,
    },
    {
      title: '发证机构',
      dataIndex: 'issuingAuthority',
    },
    {
      title: '获取日期',
      dataIndex: 'obtainDate',
      width: 120,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '复审日期',
      dataIndex: 'reviewDate',
      width: 120,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '证书信息',
      dataIndex: 'fileVOList',
      width: 120,
      customRender({ text }) {
        if (isPower('PMS_RXGLXQ_container_01_button_04', powerData.value)) {
          return h(Popover, { title: '附件' }, {
            default: () => h('div', { class: 'flex-te action-btn' }, '附件列表'),
            content: () => (text instanceof Array ? text : [])?.map((item: any) => h('p', {
              class: 'action-btn',
              onClick() {
                openFile(item);
              },
            }, item.name)),
          });
        }
        return '';
      },
    },
  ],
};

const authTableOptions = {
  showTableSetting: false,
  showSmallSearch: false,
  showToolButton: false,
  pagination: false,
  isSpacing: false,
  maxHeight: 300,
  api: () => new Api('/pms/personJobPostAuthorize/list').fetch({
    userCode: detailsData?.number,
  }, '', 'POST'),
  columns: [
    {
      title: '所属基地',
      dataIndex: 'baseName',
    },
    {
      title: '作业岗位',
      dataIndex: 'jobPostName',
    },
    {
      title: '授权到期日期',
      dataIndex: 'endDate',
      width: 120,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '授权状态',
      dataIndex: 'authorizeStatusName',
      width: 120,
    },
    {
      title: '是否等效',
      dataIndex: 'isEquivalent',
      width: 120,
      customRender({ text }) {
        return text ? '是' : '否';
      },
    },
    {
      title: '等效认定基地',
      dataIndex: 'personJobPostEquList',
      width: 120,
      customRender({ text }) {
        return h('div', {
          class: 'flex-te action-btn',
          onClick() {
            openDrawer({
              title: '岗位等效认定',
              width: 1000,
              content() {
                return h(TableDrawer, {
                  data: text || [],
                });
              },
              footer: {
                isOk: false,
                // @ts-ignore
                cancelText: '关闭',
              },
            });
          },
        }, '查看');
      },
    },
    {
      title: '授权记录',
      dataIndex: 'fileVOList',
      width: 120,
      customRender({ text }) {
        return h(Popover, { title: '附件' }, {
          default: () => h('div', { class: 'flex-te action-btn' }, '附件列表'),
          content: () => (text instanceof Array ? text : [])?.map((item: any) => h('p', {
            class: 'action-btn',
            onClick() {
              openFile(item);
            },
          }, item.name)),
        });
      },
    },
  ],
};

function navDetails(id: string) {
  router.push({
    name: 'PMSCertificateStandardsDetails',
    params: {
      id,
    },
  });
}

const manageInfo = reactive({
  list: [
    // {
    //   label: '接口部门',
    //   field: 'contactDeptName',
    // },
    // {
    //   label: '接口科室',
    //   field: 'contactOfficeName',
    // },
    {
      label: '接口人',
      field: 'contactUserName',
    },
    {
      label: '基地承担项目',
      field: 'basePlaceProjectName',
      gridColumn: '1/3',
    },
    {
      label: '大修/日常',
      field: 'workTypeName',
    },
    {
      label: '大修轮次',
      field: 'repairRound',
    },
    {
      label: '身高/米',
      field: 'heightStr',
    },
    {
      label: '体重/千克',
      field: 'weightStr',
    },
    {
      label: '职业禁忌症',
      field: 'jobTaboosName',
      hidden: detailsData.jobTaboos !== '有',
    },
    {
      label: '控制区作业',
      field: 'designCtrlZoneOp',
      isBoolean: true,
    },
    {
      label: '化毒作业',
      field: 'chemicalToxinUseJob',
      isBoolean: true,
    },
    {
      label: '高剂量人员(年个人剂量>8mSv为高剂量人员)',
      field: 'isHeightMeasurePerson',
      isBoolean: true,
    },
    {
      label: '新人',
      field: 'newcomer',
      isBoolean: true,
    },
    {
      label: '授权状态',
      field: 'authorizationStatus',
    },
    {
      label: '是否常驻基地',
      field: 'isBasePermanent',
      isBoolean: true,
    },
    {
      label: '计划入场时间',
      field: 'inDate',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '计划离场时间',
      field: 'outDate',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '实际入场时间',
      field: 'actInDate',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '实际离场时间',
      field: 'actOutDate',
      formatTime: 'YYYY-MM-DD',
    },
  ],
  dataSource: detailsData,
});

const personIn = reactive({
  list: [
    {
      label: '计划进场日期',
      field: 'inDate',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '实际进场日期',
      field: 'actInDate',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '身高/米',
      field: 'heightStr',
    },
    {
      label: '体重/千克',
      field: 'weightStr',
    },
    {
      label: '化学品/毒物使用或者接触作业',
      field: 'chemicalToxinUseJob',
      isBoolean: true,
    },
    {
      label: '高剂量人员',
      field: 'isHeightMeasurePerson',
      isBoolean: true,
    },
    {
      label: '新人',
      field: 'newcomer',
      isBoolean: true,
    },
    {
      label: '一年内参与过集团内大修',
      field: 'isJoinYearMajorRepair',
      isBoolean: true,
    },
    {
      label: '接口人',
      field: 'contactUserName',
    },
    {
      label: '是否常驻基地',
      field: 'isBasePermanent',
      isBoolean: true,
    },
  ],
  dataSource: detailsData,
});

const personOut = reactive({
  list: [
    {
      label: '计划离场日期',
      field: 'outDate',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '实际离场日期',
      field: 'actOutDate',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '离场原因',
      field: 'leaveReasonName',
    },
    {
      label: '是否再次入场',
      field: 'isAgainIn',
      isBoolean: true,
    },
    {
      label: '是否完成离场工作交接、离场WBC测量（必要时）',
      field: 'isFinishOutHandover',
      isBoolean: true,
      gridColumn: '1/3',
    },
  ],
  dataSource: detailsData,
});
</script>

<template>
  <BasicCard
    title="人员基本信息"
    :grid-content-props="basicInfo"
    :isContentSpacing="false"
  />
  <BasicCard
    title="培训信息"
  >
    <div>
      <OrionTable :options="trainTableOptions" />
    </div>
  </BasicCard>
  <BasicCard
    title="证书信息"
  >
    <div>
      <OrionTable :options="certificateTableOptions" />
    </div>
  </BasicCard>
  <BasicCard
    title="岗位授权信息"
  >
    <div>
      <OrionTable :options="authTableOptions" />
    </div>
  </BasicCard>
  <BasicCard
    title="人员进场信息"
    :isContentSpacing="false"
    :grid-content-props="personIn"
  />
  <BasicCard
    title="人员离场信息"
    :isContentSpacing="false"
    :grid-content-props="personOut"
  />
  <!--  <BasicCard-->
  <!--    title="基地人员管理信息"-->
  <!--    :grid-content-props="manageInfo"-->
  <!--    :isContentSpacing="false"-->
  <!--  />-->
</template>

<style scoped lang="less">

</style>
