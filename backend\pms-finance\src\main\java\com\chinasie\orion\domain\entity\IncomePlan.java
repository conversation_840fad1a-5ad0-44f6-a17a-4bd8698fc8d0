package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.DictDataBind;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * IncomePlan Entity对象
 *
 * <AUTHOR>
 * @since 2024-09-29 19:14:00
 */
@TableName(value = "pmsx_income_plan")
@ApiModel(value = "IncomePlanEntity对象", description = "收入计划填报")
@Data

public class IncomePlan extends  ObjectEntity  implements Serializable{

    /**
     * 工作主题
     */
    @ApiModelProperty(value = "工作主题")
    @TableField(value = "work_topics")
    private String workTopics;

    @ApiModelProperty(value = "工作主题名称")
    @TableField(value = "work_topics_name")
    private String workTopicsName;

    /**
     * 下发人员
     */
    @ApiModelProperty(value = "下发人员")
    @TableField(value = "issue_person")
    private String issuePerson;

    /**
     * 下发时间
     */
    @ApiModelProperty(value = "下发时间")
    @TableField(value = "issue_time")
    private Date issueTime;

    /**
     * 锁定状态
     */
    @ApiModelProperty(value = "锁定状态")
    @TableField(value = "lock_status")
    @FieldBind(dataBind = DictDataBind.class, type = "control_type", target = "lockStatusName")
    private String lockStatus;

    @ApiModelProperty(value = "锁定状态名称")
    @TableField(exist = false)
    private String lockStatusName;


    /**
     * 本月收入计划笔数
     */
    @ApiModelProperty(value = "本月收入计划笔数")
    @TableField(value = "income_plan_count")
    private Integer incomePlanCount;

    /**
     * 本月收入计划金额
     */
    @ApiModelProperty(value = "本月收入计划金额")
    @TableField(value = "income_plan_amt")
    private BigDecimal incomePlanAmt;

    /**
     * 已完成计划数量
     */
    @ApiModelProperty(value = "已完成计划数量")
    @TableField(value = "complete_count")
    private Integer completeCount;

    /**
     * 已完成计划金额
     */
    @ApiModelProperty(value = "已完成计划金额")
    @TableField(value = "complete_amt")
    private BigDecimal completeAmt;

    /**
     * 执行中笔数
     */
    @ApiModelProperty(value = "执行中笔数")
    @TableField(value = "execution_count")
    private Integer executionCount;

    /**
     * 执行中金额
     */
    @ApiModelProperty(value = "执行中金额")
    @TableField(value = "execution_amt")
    private BigDecimal executionAmt;

    /**
     * 未开始笔数
     */
    @ApiModelProperty(value = "未开始笔数")
    @TableField(value = "no_start_count")
    private Integer noStartCount;

    /**
     * 未开始金额
     */
    @ApiModelProperty(value = "未开始金额")
    @TableField(value = "no_start_amt")
    private BigDecimal noStartAmt;

    /**
     * 未挂接里程碑数量
     */
    @ApiModelProperty(value = "未挂接里程碑数量")
    @TableField(value = "no_hook_milestone_count")
    private Integer noHookMilestoneCount;

    /**
     * 未挂接里程碑金额
     */
    @ApiModelProperty(value = "未挂接里程碑金额")
    @TableField(value = "no_hook_milestone_amt")
    private BigDecimal noHookMilestoneAmt;

    /**
     * 编制调整状态
     */
    @ApiModelProperty(value = "编制调整状态")
    @TableField(value = "income_plan_type")
    private String incomePlanType;


    @ApiModelProperty(value = "消息用户id")
    @TableField(exist = false)
    private String userId;


}
