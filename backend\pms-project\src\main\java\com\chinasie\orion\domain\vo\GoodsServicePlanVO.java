package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * GoodsServicePlan Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-25 16:26:18
 */
@ApiModel(value = "GoodsServicePlanVO对象", description = "物资/服务计划表")
@Data
public class GoodsServicePlanVO extends ObjectVO implements Serializable{

        /**
         * 物资服务计划编号
         */
        @ApiModelProperty(value = "物资服务计划编号")
        private String number;

        /**
         * 名称
         */
        @ApiModelProperty(value = "名称")
        private String name;


        /**
         * 项目ID
         */
        @ApiModelProperty(value = "项目ID")
        private String projectId;

        /**
         * 项目ID
         */
        @ApiModelProperty(value = "项目名称")
        private String projectName;

        /**
         * 描述
         */
        @ApiModelProperty(value = "描述")
        private String description;

        /**
         * 类型对应的字典编码
         */
        @ApiModelProperty(value = "类型对应的字典编码")
        private String typeCode;
        /**
         * 类型对应的字典编码
         */
        @ApiModelProperty(value = "类型")
        private String type;

        /**
         * 物资服务编码
         */
        @ApiModelProperty(value = "物资服务编码")
        private String goodsServiceNumber;

        /**
         * 规格型号
         */
        @ApiModelProperty(value = "规格型号")
        private String normsModel;

        /**
         * 服务期限
         */
        @ApiModelProperty(value = "服务期限")
        private Integer serviceTerm;

        /**
         * 计量单位对应数据字典
         */
        @ApiModelProperty(value = "计量单位对应数据字典")
        private String unitCode;
        /**
         * 计量单位对应数据字典
         */
        @ApiModelProperty(value = "计量单位")
        private String unit;

        /**
         * 需求数量
         */
        @ApiModelProperty(value = "需求数量")
        private BigDecimal demandAmount;

        /**
         * 总入库数量
         */
        @ApiModelProperty(value = "总入库数量")
        private BigDecimal totalStoreAmount;

        /**
         * 需求时间
         */
        @ApiModelProperty(value = "需求时间")
        private Date demandTime;

        /**
         * 采购计划编号
         */
        @ApiModelProperty(value = "采购计划编号")
        private String buyPlanId;

        /**
         * 需求人ID
         */
        @ApiModelProperty(value = "需求人ID")
        private String demandPersonId;

        /**
         * 需求人ID
         */
        @ApiModelProperty(value = "需求人名字")
        private String demandPersonName;

        /**
         * 需求人工号
         */
        @ApiModelProperty(value = "需求人工号")
        private String demandPersonJobNumber;

        /**
         * 数据创建者名字
         */
        @ApiModelProperty(value = "数据创建者名字")
        private String dateCreatorName;

    }
