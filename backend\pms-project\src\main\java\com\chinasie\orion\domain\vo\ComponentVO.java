package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/3/10 15:13
 */
@Data
public class ComponentVO implements Serializable {
    /**
     * 分类ID
     */
    @ApiModelProperty(value = "分类ID")
    private String classifyId;

    /**
     * 分类名称
     */
    @ApiModelProperty(value = "分类名称")
    private String classifyName;

    /**
     * 预览图
     */
    @ApiModelProperty(value = "预览图")
    private String imageId;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String modifyId;

    /**
     * 显示顺序
     */
    @ApiModelProperty(value = "显示顺序")
    private Integer sort;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 状态 -1删除 0禁用 1启用
     */
    @ApiModelProperty(value = "状态 -1删除 0禁用 1启用")
    private Integer status;

    /**
     * 创建者ID
     */
    @ApiModelProperty(value = "创建者ID")
    private String creatorId;

    /**
     * 密级期限
     */
    @ApiModelProperty(value = "密级期限")
    private String securityLimit;

    /**
     * 密级
     */
    @ApiModelProperty(value = "密级")
    private String secretLevel;

    /**
     * 密级名称
     */
    @ApiModelProperty(value = "密级名称")
    private String secretLevelName;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    /**
     * 类名称
     */
    @ApiModelProperty(value = "类名称")
    private String className;

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private String id;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    /**
     * 拥有者
     */
    @ApiModelProperty(value = "拥有者")
    private String ownerId;


    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUserName;

    /**
     * 所有者
     */
    @ApiModelProperty(value = "所有者")
    private String ownerUserName;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String modifyUserName;


    /**
     * 产品状态 1未发布 2流程中 3已发布
     */
    @ApiModelProperty(value = "产品状态名称")
    private String statusName;


    /**
     * 产品Id
     */
    @ApiModelProperty(value = "产品Id")
    private String productId;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;

    /**
     * 版本KEY
     */
    @ApiModelProperty(value = "版本KEY")
    private String revKey;

    /**
     * 版本值
     */
    @ApiModelProperty(value = "版本值")
    private String revId;

    /**
     * 发布日期
     */
    @ApiModelProperty(value = "发布日期")
    private Date publishDate;

}
