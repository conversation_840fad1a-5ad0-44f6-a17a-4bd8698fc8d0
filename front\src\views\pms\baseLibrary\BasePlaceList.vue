<script setup lang="ts">
import {
  Layout, OrionTable, BasicTableAction, IOrionTableActionItem, BasicButton, isPower,
} from 'lyra-component-vue3';
import {
  computed, ComputedRef, h, ref, Ref,
} from 'vue';
import { Modal } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { openFormDrawer } from './utils';
import BasePlaceEdit from './BasePlaceEdit.vue';
import Api from '/@/api';
import { usePagePower } from '/@/views/pms/hooks';

const router = useRouter();
const tableRef: Ref = ref();
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const tableOptions = {
  rowSelection: {
    onChange(_keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  showToolButton: false,
  isSpacing: true,
  columns: [
    {
      title: '操作',
      dataIndex: 'actions',
      width: 160,
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
    {
      title: '基地编码',
      dataIndex: 'code',
      customRender({ text, record }) {
        if (isPower('PMS_JDK_container_02_button_02', powerData.value)) {
          return h('span', {
            class: 'flex-te action-btn',
            title: text,
            onClick: () => navDetails(record?.id),
          }, text);
        }
        return h('span', {
          class: 'flex-te',
          title: text,
        }, text);
      },
    },
    {
      title: '基地名称',
      dataIndex: 'name',
    },
    {
      title: '基地所在城市',
      dataIndex: 'city',
    },
  ],
  api: (params: Record<string, any>) => new Api('/pms/base-place').fetch({
    ...params,
  }, 'page', 'POST'),
};

const toolButtons: ComputedRef<Record<string, any>[]> = computed(() => [
  {
    event: 'add',
    text: '新增',
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
    powerCode: 'PMS_JDK_container_01_button_01',
  },
  {
    event: 'batchDelete',
    text: '删除',
    icon: 'sie-icon-shanchu',
    powerCode: 'PMS_JDK_container_01_button_02',
    disabled: selectedRows.value.length === 0,
  },
]);

function toolClick({ event }: Record<string, any>) {
  switch (event) {
    case 'add':
      openFormDrawer(BasePlaceEdit, null, updateTable);
      break;
    case 'batchDelete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除已勾选的数据吗？',
        onOk: () => deleteApi(selectedRows.value.map((item) => item.id)),
      });
      break;
  }
}

const actions: IOrionTableActionItem[] = [
  {
    text: '编辑',
    event: 'edit',
    isShow: () => isPower('PMS_JDK_container_02_button_01', powerData.value),
  },
  {
    text: '查看',
    event: 'view',
    isShow: () => isPower('PMS_JDK_container_02_button_02', powerData.value),
  },
  {
    text: '删除',
    event: 'delete',
    isShow: () => isPower('PMS_JDK_container_02_button_03', powerData.value),
  },
];

function actionClick(actionItem: any, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'edit':
      openFormDrawer(BasePlaceEdit, record, updateTable);
      break;
    case 'view':
      navDetails(record?.id);
      break;
    case 'delete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除这条数据吗？',
        onOk: () => deleteApi([record.id]),
      });
      break;
  }
}

function navDetails(id: string) {
  router.push({
    name: 'PMSBaseLibraryDetails',
    params: {
      id,
    },
  });
}

function updateTable() {
  tableRef.value?.reload();
}

function deleteApi(ids: string[]) {
  return new Promise((resolve) => {
    new Api('/pms/base-place').fetch(ids, 'remove', 'DELETE').then(() => {
      updateTable();
    }).finally(() => {
      resolve('');
    });
  });
}

const { powerData, getPowerDataHandle } = usePagePower();
</script>

<template>
  <Layout
    v-get-power="{pageCode:'PMSBaseLibrary',getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <template
          v-for="button in toolButtons"
          :key="button.event"
        >
          <BasicButton
            v-is-power="[button.powerCode]"
            v-bind="button"
            @click="toolClick(button)"
          >
            {{ button.text }}
          </BasicButton>
        </template>
      </template>
      <template #actions="{record}">
        <BasicTableAction
          :actions="actions"
          :record="record"
          @actionClick="actionClick($event,record)"
        />
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">

</style>
