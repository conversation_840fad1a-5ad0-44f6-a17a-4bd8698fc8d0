package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 21 日
 * 安质环看板维护部门
 **/
@Data
@ApiModel(value = "AmpereRingBoardConfigDept 对象",description = "安质环看板维护部门对象")
@TableName(value = "pms_amperering_config_dept")
public class AmpereRingBoardConfigDept extends ObjectEntity implements Serializable {
    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    @TableField(value = "dept_name")
    private String deptName;

    /**
     * 部门code
     */
    @ApiModelProperty(value = "部门code")
    @TableField(value = "dept_code")
    private String deptCode;

    /**
     * 部门编码
     */
    @ApiModelProperty(value = "部门编码")
    @TableField(value = "dept_number")
    private String deptNumber;

    /**
     * 部门绩效考核是否展示
     */
    @ApiModelProperty(value = "部门绩效考核是否展示")
    @TableField(value = "is_dept_score_show")
    private Boolean isDeptScoreShow;

    /**
     * 隐患排查是否展示
     */
    @ApiModelProperty(value = "隐患排查是否展示")
    @TableField(value = "is_check_problems_show")
    private Boolean isCheckProblemsShow;


    /**
     * 隐患排除的部门序号
     */
    @ApiModelProperty(value = "隐患排查的部门序号")
    @TableField(value = "check_problems_sort")
    private Integer checkProblemsSort;

    /**
     * 标准得分
     */
    @ApiModelProperty(value = "部门标准分")
    @TableField(value = "standard_score")
    private Double standardScore;



}
