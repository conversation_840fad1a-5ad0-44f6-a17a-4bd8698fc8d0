package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;

/**
 * JobAuthorizationinformations Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-05 10:41:49
 */
@TableName(value = "pmsx_job_authorizationinformations")
@ApiModel(value = "JobAuthorizeEntity对象", description = "岗位授权信息")
@Data

public class JobAuthorize extends  ObjectEntity  implements Serializable{

    /**
     * 所属基地
     */
    @ApiModelProperty(value = "所属基地")
    @TableField(value = "base_place_code")
    private String basePlaceCode;

    /**
     * 作业岗位
     */
    @ApiModelProperty(value = "作业岗位")
    @TableField(value = "job_positions")
    private String jobPositions;

    /**
     * 授权到期日期
     */
    @ApiModelProperty(value = "授权到期日期")
    @TableField(value = "end_date")
    private Date endDate;

    /**
     * 授权状态
     */
    @ApiModelProperty(value = "授权状态")
    @TableField(value = "authorize_status")
    private String authorizeStatus;

    /**
     * 是否等效
     */
    @ApiModelProperty(value = "是否等效")
    @TableField(value = "equivalent_ornot")
    private String equivalentOrnot;

    /**
     * 等效认定基地
     */
    @ApiModelProperty(value = "等效认定基地")
    @TableField(value = "equivalent_certification_base")
    private String equivalentCertificationBase;

    /**
     * 授权记录
     */
    @ApiModelProperty(value = "授权记录")
    @TableField(value = "authorization_records")
    private String authorizationRecords;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @TableField(value = "main_table_id")
    private String mainTableId;

}
