<template>
  <div class="milestoneImport">
    <div class="topBtn">
      <BasicButton
        type="primary"
        icon="add"
        @click="handleImport"
      >
        导入模板
      </BasicButton>
    </div>

    <div class="flex content">
      <div class="left-menu">
        <div class="menu-top flex flex-ac flex-pj">
          <span class="templateTitie">计划模板列表</span>
          <div class="menu-top-right">
            <Icon
              :size="18"
              class="addTemplate"
              icon="sie-icon-tianjiaxinzeng"
              @click="setTemplate('add')"
            />
            <Icon
              :size="18"
              class="editTemplate"
              icon="sie-icon-bianji"
              @click="setTemplate('edit')"
            />
            <Icon
              :size="18"
              class="editTemplate ml15"
              icon="sie-icon-del"
              @click="setTemplate('del')"
            />
          </div>
        </div>
        <BasicScrollbar class="menu-content">
          <div>
            <SpinMain v-if="loading" />
            <div
              v-for="(item,index) in menus"
              :key="index"
              class="menuItem"
              :class="item.id===templateId?'active':''"
              @click="getTemplateDetails(item)"
            >
              {{ item.templateName }}
            </div>
          </div>
        </BasicScrollbar>
      </div>
      <div class="right-table">
        <OrionTable
          ref="tableRef"
          :options="tableOption"
        >
          <template #name="{ record }">
            <div
              class="flex-te"
              :title="record.name"
            >
              <Icon
                class="lichengbei-icon"
                :size="18"
                icon="sie-icon-lichengbei"
              />
              {{ record.nodeName }}
            </div>
          </template>

          <template #toolbarLeft>
            <BasicButton
              type="primary"
              icon="edit"
              @click="setContentModal('edit')"
            >
              编辑
            </BasicButton>

            <BasicButton
              type="primary"
              icon="add"
              @click="setContentModal('add')"
            >
              添加
            </BasicButton>
            <BasicButton
              type="primary"
              icon="delete"
              @click="deleteNodes()"
            >
              删除
            </BasicButton>
          </template>
        </OrionTable>
      </div>
    </div>

    <NodeDrawer
      @editSuccess="editSuccess"
      @register="registerNodeDrawer"
      @close="() => setEditPlanModal(false)"
    />

    <TemplateDrawer
      @templateSuccess="templateSuccess"
      @register="registerTemplate"
      @close="() => setEditPlanModal(false)"
    />

    <!-- 导入 -->
    <BasicImport
      :downloadFileObj="downloadFileObj"
      :requestBasicImport="requestBasicImport"
      :requestSuccessImport="requestSuccessImport"
      @register="register"
      @changeImportModalFlag="changeImportModalFlag"
    />
  </div>
</template>

<script setup lang="ts">
import {
  BasicButton, BasicImport, BasicScrollbar, Icon, OrionTable, useDrawer, useModal,
} from 'lyra-component-vue3';
import {
  inject, nextTick, onMounted, Ref, ref, watchEffect,
} from 'vue';
import {
  deleteNewProject,
  deleteProjectSchemeMilestoneNode,
  postProjectSchemeMilestoneNodePages,
  postProjectSchemeMilestoneTemplatelist,
} from './api';
import { message, Modal } from 'ant-design-vue';
import dayjs from 'dayjs';
import { projectIdKey } from './type';
import NodeDrawer from './component/nodeDrawer.vue';
import TemplateDrawer from './component/templateDrawer.vue';
import SpinMain from '/@/views/pms/projectLaborer/projectLab/components/SpinMain.vue';
import Api from '/@/api';

const projectId: string = inject(projectIdKey);
const menus: Ref<any[]> = ref([]);
const tableRef: Ref = ref();
const powerData = inject('powerData');
const loading = ref<boolean>(false);
const templateId = ref<string>('');
const tableData:Ref<any[]> = ref([]);

const downloadFileObj = {
  url: '/pms/projectSchemeMilestoneNode/template/download',
  method: 'POST',
};

const rspDeptOptions = ref([]);// 部门

const [register, { openModal }] = useModal();
const [registerNodeDrawer, { openDrawer: openEditNodeDrawer }] = useDrawer();
const [registerTemplate, { openDrawer: openEditTemplate }] = useDrawer();
const fileTypeList = ref([]);
const tableOption = {
  rowSelection: {},
  showToolButton: false,
  showTableSetting: false,
  immediate: false,
  showSmallSearch: false,
  showIndexColumn: false,
  deleteToolButton: 'add|delete|enable|disable',
  api: async (params) => {
    const result = await postProjectSchemeMilestoneNodePages({
      ...params,
      query: {
        templateId: templateId.value,
      },
    });
    tableData.value = result?.content || [];
    return result;
  },
  columns: [
    {
      title: '计划节点名称',
      dataIndex: 'nodeName',
      slots: { customRender: 'name' },
    },
    {
      title: '描述说明',
      dataIndex: 'description',
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
      key: 'dept',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '修改人',
      dataIndex: 'modifyName',
    },
    {
      title: '修改时间',
      dataIndex: 'modifyTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '排序',
      dataIndex: 'sort',
    },
  ],
};

onMounted(() => {
  getMenus();
});

// 默认选中第一条数据
watchEffect(() => {
  if (menus.value?.length && menus.value?.every((item) => item?.id !== templateId.value)) {
    templateId.value = menus.value[0]?.id;
    updateTable();
  }
});

// 获取左侧菜单
async function getMenus() {
  loading.value = true;
  try {
    menus.value = await postProjectSchemeMilestoneTemplatelist();
    menus.value = menus.value.map((item) => ({
      ...item,
      name: item.roleName,
    }));
  } finally {
    loading.value = false;
  }
}

// 刷新表格数据
function updateTable() {
  nextTick(() => {
    tableRef?.value?.reload();
  });
}

function getTemplateDetails(item) {
  templateId.value = item.id;
  updateTable();
}

function setEditPlanModal(value) {
}
function editSuccess(data) {
  if (data) {
    updateTable();
  }
}
function templateSuccess(data) {
  getMenus();
}

const setTemplate = (type = 'edit') => {
  if (type === 'add') {
    openEditTemplate(true, {
      type,
      templateId: templateId.value,
    });
  } else if (type === 'edit') {
    if (templateId.value) {
      openEditTemplate(true, {
        type,
        id: templateId.value,
        sort: menus.value?.filter((item) => item?.id === templateId?.value)?.[0]?.sort,
        templateName: menus.value?.filter((item) => item?.id === templateId?.value)?.[0]?.templateName,
      });
    } else {
      message.warn('请选择一条计划模板进行编辑！');
    }
  } else if (type === 'del') {
    if (templateId.value) {
      if (tableData.value.length) {
        return message.error('当前计划模板存在计划节点，无法删除！');
      }
      Modal.confirm({
        title: '删除确认提示？',
        content: '请确认是否删除所选计划模板，删除后不可恢复？',
        onOk() {
          return new Promise((resolve) => {
            new Api('/pms/projectSchemeMilestoneTemplate').fetch([templateId.value], '', 'delete')
              .then(() => {
                getMenus();
                message.success('删除成功');
                resolve(true);
              })
              .finally(() => {
                resolve('');
              });
          });
        },
        onCancel() {
          Modal.destroyAll();
        },
      });
    } else {
      message.warn('请选择一条计划模板进行删除！');
    }
  }
};

const setContentModal = (type = 'edit') => {
  if (type === 'add') {
    openEditNodeDrawer(true, {
      type,
      templateId: templateId.value,
    });
  } else {
    let selectRow = tableRef.value.getSelectRows();

    if (selectRow && selectRow.length === 1) {
      openEditNodeDrawer(true, {
        type,
        templateId: templateId.value,
        nodeName: selectRow[0].nodeName,
        description: selectRow[0].description,
        sort: selectRow[0].sort,
        id: selectRow[0].id,
      });
    } else {
      message.warn('请选择一条计划节点编辑！');
    }
  }
};
async function deleteNodes() {
  let selectRow = tableRef.value.getSelectRows();
  let ids = [];
  if (selectRow) {
    for (let i in selectRow) {
      ids.push(selectRow[i].id);
    }
  }
  if (ids && ids.length > 0) {
    Modal.confirm({
      title: '删除确认提示？',
      content: '请确认是否删除所选计划节点，删除后不可恢复？',
      onOk() {
        return new Promise((resolve) => {
          deleteProjectSchemeMilestoneNode(ids).then(() => {
            updateTable();
            resolve(true);
          })
            .finally(() => {
              resolve('');
            });
        });
      },
      onCancel() {
        Modal.destroyAll();
      },
    });
  } else {
    message.warn('请选择一条计划节点删除！');
  }
}

const handleImport = () => {
  openModal(true, {});
};

function changeImportModalFlag() {
  // updateForm();
}

const requestBasicImport = async (formData) =>
  new Promise((resolve) => {
    // new Api('/pms').importFile(
    //   formData[0],
    //   '/api/pms/projectSchemeMilestoneNode/import/excel',
    // )
    //   .then((res) => {
    //     if (res.data?.code === 200) {
    //       resolve({
    //         err: [],
    //         succ: '成功',
    //         code: 200,
    //       });
    //     } else {
    //       resolve({
    //         err: res.data?.result ?? [],
    //         succ: false,
    //         code: 200,
    //       });
    //     }
    //   });
  });

const requestSuccessImport = (successKey) =>
  new Promise((resolve) => {
    setTimeout(() => {
      // 如果有errorMsg就表示失败，没有就表示成功
      resolve({
        result: true,
      });
      getMenus();
      updateTable();
    }, 2000);
  });

</script>

<style scoped lang="less">
.milestoneImport {
  height:100%;
padding:0 16px ;
    background: #fff;
.topBtn {
  background: #fff;
  padding:16px 0  ;
}
.content {
       border: 1px solid rgba(233, 233, 233, 1);
       height: 620px;
}

  .left-menu {
    height: 100%;
    width:300px;
    background: #fff;
    border-right: 1px solid rgba(233, 233, 233, 1);
    .menu-top {
      height:60px;
      line-height: 60px;
      padding: 16px 20px;
    border-bottom: 1px solid rgba(233, 233, 233, 1);      .templateTitie {
        font-size:18px;
        color:#333;
      }
      .addTemplate {
        cursor: pointer;
        margin-right:10px;
      }
            .editTemplate {
        cursor: pointer;
      }
    }

.menu-content {
  height:555px;
  overflow: hidden;

    .menuItem {
  height: 42px;
  line-height: 42px;
  cursor: pointer;
  font-size:14px;
  padding: 0 20px;
  color:#333;
  &:hover {
  color:~`getPrefixVar('primary-color')` !important;
}

}

.active {
  background:~`getPrefixVar('primary-1')`  !important;
  color:~`getPrefixVar('primary-color')` !important;
}
  }

}

  .right-table {
    flex:1;
  padding:10px;
    overflow: hidden;
    .lichengbei-icon {
      color:#FFB119;
    }
  }
}

</style>
