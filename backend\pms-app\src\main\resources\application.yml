spring:
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true

---
easy-trans:
  #启用redis缓存 如果不用redis请设置为false
  is-enable-redis: true
  #启用全局翻译(拦截所有responseBody进行自动翻译)，不推荐，可以在具体的controlelr方法添加注解： @TransMethodResult
  is-enable-global: false
  #启用平铺模式
  is-enable-tile: true
  #字典缓存放到redis 微服务模式请开启
  dict-use-redis: true
  # 以map为返回结果
  is-enable-map-result: true


---
orion:
  web:
    app-api:
      prefix: app
      controller: "*.controlelr.app*"
    admin-api:
      prefix: admin
      controller: "*.controlelr.admin.*"
    admin-ui:
      url: "www.orion.com"