CREATE TABLE `pmsx_relation_org_to_person` (
  `id` varchar(64) NOT NULL COMMENT '主键',
  `creator_id` varchar(64) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `logic_status` int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除字段',
  `person_id` varchar(64) DEFAULT NULL COMMENT '人员id',
  `repair_org_id` varchar(64) DEFAULT NULL COMMENT '大修组织id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='大修组织人员关系表';

CREATE TABLE `pmsx_button_person`
(
    `id`        varchar(64) NOT NULL,
    `person_id` varchar(64) DEFAULT NULL COMMENT '人员id',
    `button_id` varchar(64) DEFAULT NULL COMMENT '按钮id',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='人员按钮权限表';