package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/1/13 15:17
 * @description:
 */
@ApiModel(value = "TaskSubjectVO对象", description = "项目科目")
public class TaskSubjectVO extends ObjectVO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 启用禁用
     */
    @ApiModelProperty(value = "启用禁用")
    private Integer takeEffect;

    private String takeEffectName;

    @Override
    public String getId() {
        return id;
    }

    @Override
    public void setId(String id) {
        this.id = id;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public Integer getTakeEffect() {
        return takeEffect;
    }

    public void setTakeEffect(Integer takeEffect) {
        this.takeEffect = takeEffect;
    }

    public String getTakeEffectName() {
        return takeEffectName;
    }

    public void setTakeEffectName(String takeEffectName) {
        this.takeEffectName = takeEffectName;
    }
}
