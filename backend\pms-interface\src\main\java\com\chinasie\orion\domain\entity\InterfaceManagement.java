package com.chinasie.orion.domain.entity;

import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.UserDataBind;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;

/**
 * InterfaceManagement Entity对象
 *
 * <AUTHOR>
 * @since 2024-01-29 22:08:44
 */
@TableName(value = "pmsx_interface_management")
@ApiModel(value = "InterfaceManagementEntity对象", description = "接口管理")
@Data
public class InterfaceManagement extends ObjectEntity implements Serializable {

    /**
     * 发布单位
     */
    @ApiModelProperty(value = "发布单位")
    @TableField(value = "publish_org_name")
    private String publishOrgName;

    /**
     * 接受单位
     */
    @ApiModelProperty(value = "接受单位")
    @TableField(value = "receive_org_name")
    private String receiveOrgName;

    /**
     * 接口类型
     */
    @ApiModelProperty(value = "接口类型")
    @TableField(value = "`type`")
    private String type;

    /**
     * 流水号
     */
    @ApiModelProperty(value = "流水号")
    @TableField(value = "`number`")
    private String number;

    /**
     * 发布部门
     */
    @ApiModelProperty(value = "发布部门")
    @TableField(value = "publish_dept_id")
    private String publishDeptId;


    /**
     * 接受部门
     */
    @ApiModelProperty(value = "接受部门")
    @TableField(value = "review_dept_ids")
    private String reviewDeptIds;

    /**
     * 回复时间
     */
    @ApiModelProperty(value = "回复时间")
    @TableField(value = "reply_time")
    private Date replyTime;

    /**
     * 主办人
     */
    @ApiModelProperty(value = "主办人")
    @TableField(value = "man_user")
    @FieldBind(
            dataBind = UserDataBind.class,
            target = "manUserName"
    )
    private String manUser;
    @TableField(exist = false)
    private String manUserName;

    /**
     * 协办
     */
    @ApiModelProperty(value = "协办")
    @TableField(value = "cooperation_users")
    private String cooperationUsers;

    /**
     * 第三方检查备案
     */
    @ApiModelProperty(value = "第三方检查备案")
    @TableField(value = "third_verify")
    private String thirdVerify;

    /**
     * 专业代码
     */
    @ApiModelProperty(value = "专业代码")
    @TableField(value = "specialty_code")
    private String specialtyCode;

    /**
     * 接口描述
     */
    @ApiModelProperty(value = "接口描述")
    @TableField(value = "`desc`")
    private String desc;

    @ApiModelProperty(value = "单据类型")
    @TableField(value = "form_type")
    private String formType;

    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    private String projectId;


    @ApiModelProperty(value = "接口状态")
    @TableField(value = "interface_state")
    private String interfaceState;


    @ApiModelProperty(value = "数据Id (项目id,产品Id)")
    @TableField(value = "data_id")
    private String dataId;

    @ApiModelProperty(value = "数据类型className")
    @TableField(value = "data_class_name")
    private String dataClassName;
}
