package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * PurchaseExecuteShcnge DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@ApiModel(value = "PurchaseExecuteShcngeDTO对象", description = "采购执行变更")
@Data
@ExcelIgnoreUnannotated
public class PurchaseExecuteShcngeDTO extends ObjectDTO implements Serializable {

    /**
     * 变更人
     */
    @ApiModelProperty(value = "变更人")
    @ExcelProperty(value = "变更人 ", index = 0)
    private String changer;

    /**
     * 变更日期
     */
    @ApiModelProperty(value = "变更日期")
    @ExcelProperty(value = "变更日期 ", index = 1)
    private Date changeDate;

    /**
     * 变更内容
     */
    @ApiModelProperty(value = "变更内容")
    @ExcelProperty(value = "变更内容 ", index = 2)
    private String changeContent;

    /**
     * 变更前
     */
    @ApiModelProperty(value = "变更前")
    @ExcelProperty(value = "变更前 ", index = 3)
    private String beforeChange;

    /**
     * 变更后
     */
    @ApiModelProperty(value = "变更后")
    @ExcelProperty(value = "变更后 ", index = 4)
    private String afterChange;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 5)
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @ExcelProperty(value = "主表ID ", index = 6)
    private String mainTableId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 7)
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称 ", index = 8)
    private String contractName;
}
