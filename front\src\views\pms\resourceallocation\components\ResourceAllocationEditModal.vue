<script lang="ts" setup>
import { BasicForm, FormSchema, useForm } from 'lyra-component-vue3';
import { Input, Button as AButton, Select,RangePicker } from 'ant-design-vue'
import { disable } from '../../api/documentModelLibrary';
import type { SelectProps } from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';
import axios from 'axios';
import { useUserStore } from '/@/store/modules/user';
import Api from '/@/api';
import { defineComponent, ref, nextTick, reactive, onMounted, computed, h, watch, Ref, watchEffect } from 'vue';

const userStore = useUserStore();
const dateFormat = 'YYYY-MM-DD';
const props = withDefaults(defineProps<{
  number: string,
  userName: string,
  name: string,
  costCenterCode: string,
  repairRound: string,
  markBusinessId: any
}>(), {
  number: '',
  userName: '',
  name: '',
  costCenterCode: '',
  repairRound: '',
  markBusinessId: null
});

// const realEndDate = props.markBusinessId.realEndDate?.props.markBusinessId.realEndDate;
// const realStartDate = props.markBusinessId.realStartDate?.props.markBusinessId.realStartDate;
// function standardizeDateString(dateString) {
//   const parts = dateString.split('-');
//   if (parts.length !== 3) {
//   }
//   const year = parts[0];
//   const month = parts[1].padStart(2, '0'); // 确保月份为两位数
//   const day = parts[2].padStart(2, '0');   // 确保日期为两位数
//   return `${year}-${month}-${day}`;
// }
const markBusinessId__ = ref(props.markBusinessId);

const customValidator=()=>{
  
}
const schemas: FormSchema[] = [
  {
    field: 'userId1',
    component: 'Input',
    label: '调配人员',
    rules: [{ required: true }],
    defaultValue: props.userName,
    colProps: {
      span: 24,
    },
    dynamicDisabled: true,
  },
  {
    field: 'repairRound',
    component: 'Select',
    label: '大修轮次',
    rules: [{ required: true }],
    slot: 'customRepairSelect',
    colProps: {
      span: 24,
    },
    defaultValue:''
  },
  // {
  //   field: 'repairRound',
  //   component: 'ApiSelect',
  //   label: '大修轮次',
  //   rules: [{ required: true }],
  //   componentProps: {
  //     api() {
  //       return new Promise((resolve) => {
  //         setTimeout(() => {
  //           resolve(
  //             [
  //               { label: 'H208', value: 'H208' },
  //               { label: 'H602', value: 'H602' },
  //               { label: 'H945', value: 'H945' },
  //               { label: 'LA120', value: 'LA120' }
  //             ]
  //           )
  //         }, 1000)
  //       })
  //     }
  //   },
  //   defaultValue: 'H208', // 设置默认值
  //   colProps: {
  //     span: 24,
  //   },
  // },
  {
    field: 'data',
    component: 'RangePicker',
    label: '计划进出基地日期',
    rules: [{ required: true }],
    componentProps: {
      defaultValue: props.markBusinessId
      ? [dayjs(props.markBusinessId.realStartDate), dayjs(props.markBusinessId.realEndDate)]
      : null
    },
    colProps: {
      span: 24,
    },
  },
  {
    field: 'costCenterCode',
    component: 'Select',
    label: '班组信息',
    rules: [{ required: true }],
    slot: 'customSelect',
    colProps: {
      span: 24,
    },
    defaultValue:''
  },
];

const [register, { validate }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});


defineExpose({
  getValues,
});

const repairOptions = ref<SelectProps['options']>();

const options = ref<SelectProps['options']>();

const costCenterCode_ = ref(props.costCenterCode);

//待优化 todo方法封装
const basePlaceCode = ref(props.markBusinessId===null?'':props.markBusinessId.basePlaceCode);
const basePlaceName = ref(props.markBusinessId===null?'':props.markBusinessId.basePlaceName);
const repairRoundCode = ref();
const repairRoundName = ref();
const handleRepairChange = (value: string) => {
  const parts = value.split("-");//parts[0]为基地编码，parts[1]为大修轮次
  basePlaceCode.value = parts[0];
  basePlaceName.value = parts[1];
  repairRoundCode.value = parts[2];
  repairRoundName.value = parts[3];
  costCenterCode_.value = value;
  //处理大修轮次
}

const handleChange = (value: string) => {
  const parts = value.split("-");//parts[0]专业编码，parts[1]班组编码
  // costCenterCode_.value = value;
  //处理专业班组
}

async function getValues() {
  const values = await validate();
  let markBusinessId_ = {
    rowId: props.markBusinessId?.rowId,
    realStartDate: dayjs(values.data[0]).format(dateFormat),
    realEndDate: dayjs(values.data[1]).format(dateFormat),
    repairName: values.repairRound,
    basePlaceCode: basePlaceCode.value,
    basePlaceName: basePlaceName.value,
  };

  let formData = {
    number: props.number,
    name: props.name,
    costCenterCode: costCenterCode_.value,
    repairRound: values.repairRound,
    realStartDate: values.data[0].format(dateFormat),
    realEndDate: values.data[1].format(dateFormat),
    markBusinessId: markBusinessId_
  };
  return formData;
}

const loading: Ref<boolean> = ref(false);
onMounted(() => {
  getOptionsMock();
})

// const getOptionsMock = async () => {
//   loading.value = true;
//     try {
//         axios.post('http://127.0.0.1:8700/resource-allocation-of/resourceAllocation/queryRepairPlanAndSpecialtyTeam', { repairRound: '',staffNo:''})
//             .then(function (response) {
//                 console.log(response);
//                 // 处理响应数据
//                 options.value = response.data.result.specialtyAndTeams;
//                 repairOptions.value = response.data.result.repairRoundAnds;
//                 // loading.value = false;
//                 // console.log(response.data);
//             })
//             .catch(function (error) {
//                 // 处理错误情况
//                 console.log(error);
//             });
//     } finally {
//         setTimeout(() => {
//             loading.value = false;
//         }, 2000)
//     }
// }
async function getOptionsMock() {
  loading.value = true;
    try {
      const result = await new Api('/pms/resource-allocation-of/resourceAllocation/queryRepairPlanAndSpecialtyTeam').fetch({ 
        repairRound: '',
        staffNo: userStore.getUserInfo.code,
      }, '', 'POST');
      data.value = result;
    } finally {
      loading.value = false;
    }
}
</script>

<template>
  <div class="form-component-wrap ant-col-24">
    <BasicForm @register="register">
      <template #customRepairSelect="{ model, field }">
        <Select class="ant-col-24" v-model:value="model[field]" :options="repairOptions" style="width: 100%" @change="handleRepairChange"></Select>
      </template>
      <template #customSelect="{ model, field }">
        <Select class="ant-col-24" v-model:value="model[field]" :options="options" style="width: 100%" @change="handleChange"></Select>
      </template>
    </BasicForm>
  </div>
</template>

<style scoped></style>
