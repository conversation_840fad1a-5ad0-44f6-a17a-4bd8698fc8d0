<script setup lang="ts">
import {
  OrionTable, BasicTableAction, IOrionTableActionItem, BasicButton, isPower,
} from 'lyra-component-vue3';
import {
  computed, ComputedRef, ref, Ref, h, inject,
} from 'vue';
import { message, Modal } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { openFormDrawer, formatTableColumns, formatActionsPower } from './utils';
import ExpenseManagementEdit from './ExpenseManagementEdit.vue';
import Api from '/@/api';

const router = useRouter();
const tableRef: Ref = ref();
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const powerData = inject('powerData', []);

const projectId:string = inject('projectId');

const tableOptions = {
  rowSelection: {
    onChange(keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  smallSearchField: undefined,
  columns: [
    {
      title: '成本支出编码',
      dataIndex: 'number',
      customRender({ record, text }) {
        if (isPower('PMS_XMXQ_container_04_YSCB_FYCH_02_buttono_03', record.rdAuthList)) {
          return h('span', {
            onClick: () => {
              router.push({
                name: 'ExpenseManagementDetails',
                query: {
                  id: record.id,
                },
              });
            },
            class: 'action-btn',
          }, text);
        }
        return text;
      },
    },
    {
      title: '成本中心',
      dataIndex: 'costCenterName',
    },
    {
      title: '科目名称',
      dataIndex: 'expenseAccountName',
    },
    {
      title: '科目编码',
      dataIndex: 'expenseAccountNumber',
    },
    {
      title: '摘要',
      dataIndex: 'remark',
    },
    {
      title: '发生时间',
      dataIndex: 'occurrenceTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '--';
      },
    },
    {
      title: '发生人',
      dataIndex: 'occurrenceName',
    },
    {
      title: '金额',
      dataIndex: 'expendMoney',
    },
    {
      title: '状态',
      dataIndex: 'status',
      slots: { customRender: 'status' },
    },
    {
      title: '操作',
      dataIndex: 'actions',
      width: 160,
      fixed: 'right',
      slots: { customRender: 'action' },
    },
  ],

  api: (params:Record<string, any>) => new Api('/pms/budgetExpendForm').fetch({
    ...params,
    power: {
      pageCode: 'PMS0004',
      containerCode: 'PMS_XMXQ_container_04_YSCB_FYCH_02',
    },
    query: {
      projectId,
    },

  }, 'page', 'POST'),
  actions: [
    {
      text: '编辑',
      event: 'edit',
      isShow: (record) => (record.status === 120 && isPower('PMS_XMXQ_container_04_YSCB_FYCH_02_buttono_01', record.rdAuthList)),
      onClick(record) {
        actionClick({ event: 'edit' }, record);
      },

    },
    {
      text: '删除',
      event: 'delete',
      code: 'delete-button-demo111-KcBsejQf',
      isShow: (record) => record.status === 120 && isPower('PMS_XMXQ_container_04_YSCB_FYCH_02_buttono_02', record.rdAuthList),
      onClick(record) {
        actionClick({ event: 'delete' }, record);
      },
    },
  ],
};

const toolButtons: ComputedRef<Record<string, any>[]> = computed(() => [
  {
    event: 'add',
    text: '新增',
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
    code: 'PMS_XMXQ_container_04_YSCB_FYCH_01_button_01',
  },
  {
    event: 'batchDelete',
    text: '删除',
    icon: 'sie-icon-shanchu',
    disabled: (selectedRows.value.length === 0 || selectedRows.value.some((item) => item.status !== 120)),
    code: 'PMS_XMXQ_container_04_YSCB_FYCH_01_button_02',
  },
]);

function toolClick({ event }: Record<string, any>) {
  switch (event) {
    case 'add':
      openFormDrawer(ExpenseManagementEdit, projectId, null, updateTable);
      break;
    case 'batchDelete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除已勾选的数据吗？',
        onOk: () => deleteApi(selectedRows.value.map((item) => item.id)),
      });
      break;
    case 'enable':
      break;
    case 'disable':
      break;
  }
}

const actions = computed(() => [
  {
    text: '编辑',
    event: 'edit',
    code: 'edit-button-demo111-KcBsejQf',
  },
  {
    text: '删除',
    event: 'delete',
    code: 'delete-button-demo111-KcBsejQf',
  },
]);

function actionClick(actionItem: { event: string }, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'edit':
      openFormDrawer(ExpenseManagementEdit, projectId, record, updateTable);
      break;
    case 'delete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除这条数据吗？',
        onOk: () => deleteApi([record.id]),
      });
      break;
  }
}

function updateTable() {
  tableRef.value?.reload();
}

function deleteApi(ids: string[]) {
  return new Promise((resolve) => {
    new Api('/pms/budgetExpendForm').fetch(ids, 'remove', 'DELETE').then(() => {
      updateTable();
    }).finally(() => {
      resolve('');
    });
  });
}

</script>

<template>
  <OrionTable
    ref="tableRef"
    v-get-power="{powerData}"
    :options="tableOptions"
  >
    <template #toolbarLeft>
      <template
        v-for="button in toolButtons"
        :key="button.event"
      >
        <BasicButton
          v-is-power="[button.code]"
          v-bind="button"
          @click="toolClick(button)"
        >
          {{ button.text }}
        </BasicButton>
      </template>
    </template>
    <!--    <template #actions="{record}">-->
    <!--      <BasicTableAction-->
    <!--        :actions="actions"-->
    <!--        :record="record"-->
    <!--        @actionClick="actionClick($event,record)"-->
    <!--      />-->
    <!--    </template>-->
  </OrionTable>
</template>

<style scoped lang="less">

</style>
