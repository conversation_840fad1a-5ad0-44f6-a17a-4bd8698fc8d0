package com.chinasie.orion.controller;

import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.constant.DictConstant;
import com.chinasie.orion.domain.dto.StakeholderDTO;
import com.chinasie.orion.domain.vo.SimpleVo;
import com.chinasie.orion.domain.vo.StakeholderVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.StakeholderService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/06/10:43
 * @description:
 */
@RestController
@RequestMapping("/stakeholder")
@Api(tags = "干系人/不只是人")
public class StakeholderController {

    @Resource
    private StakeholderService stakeholderService;

    @Resource
    private DictBo dictBo;

    @ApiOperation("新增干系人")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stakeholderDTO", dataType = "StakeholderDTO")
    })
    @PostMapping("/save")
    @LogRecord(success = "【{USER{#logUserId}}】新增干系人", type = "干系人/不只是人", subType = "新增干系人", bizNo = "")
    public ResponseDTO<String> saveStakeholder(@RequestBody StakeholderDTO stakeholderDTO) throws Exception {
        return new ResponseDTO<>(stakeholderService.saveStakeholder(stakeholderDTO));
    }

    @ApiOperation("干系人分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping("/getPage")
    @LogRecord(success = "【{USER{#logUserId}}】干系人分页", type = "干系人/不只是人", subType = "干系人分页", bizNo = "")
    public ResponseDTO<Page<StakeholderVO>> getStakeholderPage(@RequestBody Page<StakeholderDTO> pageRequest) throws Exception {
        return new ResponseDTO<>(stakeholderService.getStakeholderPage(pageRequest));
    }

    @ApiOperation("干系人详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", dataType = "String")
    })
    @GetMapping("/detail/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】干系人详情", type = "干系人/不只是人", subType = "干系人详情", bizNo = "{{#id}}")
    public ResponseDTO<StakeholderVO> getStakeholderDetail(@PathVariable("id") String id, @RequestParam(value = "pageCode", required = false) String pageCode) throws Exception {
        return new ResponseDTO<>(stakeholderService.getStakeholderDetail(id, pageCode));
    }

    @ApiOperation("编辑干系人")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stakeholderDTO", dataType = "StakeholderDTO")
    })
    @PutMapping("/edit")
    @LogRecord(success = "【{USER{#logUserId}}】编辑干系人", type = "干系人/不只是人", subType = "编辑干系人", bizNo = "")
    public ResponseDTO<Boolean> editStakeholder(@RequestBody StakeholderDTO stakeholderDTO) throws Exception {
        return new ResponseDTO<>(stakeholderService.editStakeholder(stakeholderDTO));
    }

    @ApiOperation("批量删除干系人")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "idList", dataType = "List")
    })
    @DeleteMapping("/removeBatch")
    @LogRecord(success = "【{USER{#logUserId}}】批量删除干系人", type = "干系人/不只是人", subType = "批量删除干系人", bizNo = "")
    public ResponseDTO<Boolean> removeStakeholder(@RequestBody List<String> idList) throws Exception {
        return new ResponseDTO<>(stakeholderService.removeStakeholder(idList));
    }

    @ApiOperation("导入干系人")
    @PostMapping("/uploadStakeholder")
    @LogRecord(success = "【{USER{#logUserId}}】导入干系人", type = "干系人/不只是人", subType = "导入干系人", bizNo = "")
    public ResponseDTO<Boolean> uploadStakeholder(@RequestParam("file") MultipartFile file) throws Exception {
        return new ResponseDTO<>(stakeholderService.saveStakeholderByExcel(file));
    }

    @ApiOperation("获取干系人联系方式")
    @GetMapping("/contactType")
    @LogRecord(success = "【{USER{#logUserId}}】获取干系人联系方式", type = "干系人/不只是人", subType = "获取干系人联系方式", bizNo = "")
    public ResponseDTO<List<SimpleVo>> getContactType() {
        return new ResponseDTO<>(dictBo.getDictValueAndDesList(DictConstant.STAKEHOLDER_CONTACT_TYPES));
    }

    @ApiOperation("获取干系人列表")
    @PostMapping("/getList")
    @LogRecord(success = "【{USER{#logUserId}}】获取干系人列表", type = "干系人/不只是人", subType = "获取干系人列表", bizNo = "")
    public ResponseDTO<List<StakeholderVO>> getList(@RequestBody StakeholderDTO stakeholderDTO) throws Exception {
        return new ResponseDTO<>(stakeholderService.getStakeholderList(stakeholderDTO));
    }
}
