package com.chinasie.orion.service.impl;

import com.chinasie.orion.domain.entity.ProjectBudget;
import com.chinasie.orion.domain.vo.ProjectBudgetVO;
import com.chinasie.orion.domain.vo.statics.ProjectBudgetStaticesDTO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.repository.ProjectBudgetMapper;
import com.chinasie.orion.service.BudgetMoneyStatisticsService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2023/05/18/22:32
 * @description:
 */
@Service
@AllArgsConstructor
public class BudgetMoneyStatisticsServiceImpl implements BudgetMoneyStatisticsService {



    private ProjectBudgetMapper projectBudgetMapper;


    @Override
    public List<ProjectBudgetStaticesDTO> getBudgetTotal(String projectId) {
        List<ProjectBudgetStaticesDTO> projectBudgetStaticesDTOS = new ArrayList<>();
        List<ProjectBudget> projectBudgets =  projectBudgetMapper.selectList(ProjectBudget::getProjectId, projectId);
        if(!CollectionUtils.isEmpty(projectBudgets)){
            projectBudgets.forEach(item ->{
                ProjectBudgetStaticesDTO projectBudgetStaticesDTO = new ProjectBudgetStaticesDTO();
                projectBudgetStaticesDTO.setBudgetNumber(item.getNumber());
                projectBudgetStaticesDTO.setSumBudget(item.getYearExpense());
                projectBudgetStaticesDTO.setSumPractical(item.getTotalCost());
                projectBudgetStaticesDTOS.add(projectBudgetStaticesDTO);
            });

        }
        return projectBudgetStaticesDTOS;
    }

    @Override
    public ProjectBudgetVO getAnnualStatisticsByProjectIdAndAnnual(String projectId, int year) {
        LambdaQueryWrapperX<ProjectBudget> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(ProjectBudget::getProjectId, projectId);
        lambdaQueryWrapperX.eq(ProjectBudget::getYear, String.valueOf(year));
        List<ProjectBudget> projectBudgets =  projectBudgetMapper.selectList(lambdaQueryWrapperX);
        if(!CollectionUtils.isEmpty(projectBudgets)){
            return BeanCopyUtils.convertTo(projectBudgets.get(0),ProjectBudgetVO::new);
        }
        return null;
    }
}
