package com.chinasie.orion.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.domain.dto.ImOrIfToParamBatchDTO;
import com.chinasie.orion.domain.dto.pdm.*;
import com.chinasie.orion.domain.entity.IdeaForm;
import com.chinasie.orion.domain.entity.IfToParameterToIns;
import com.chinasie.orion.domain.dto.IfToParameterToInsDTO;
import com.chinasie.orion.domain.entity.InterfaceManagement;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.vo.IfToParameterToInsVO;
import com.chinasie.orion.domain.vo.ProductPBSSimpleVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.ComponentFeignService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import com.chinasie.orion.service.IdeaFormService;
import com.chinasie.orion.service.IfToParameterToInsService;
import com.chinasie.orion.repository.IfToParameterToInsMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.service.InterfaceManagementService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.util.CollectionUtils;
import com.chinasie.orion.util.ResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.lang.String;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * IfToParameterToIns 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-31 13:52:16
 */
@Service
public class IfToParameterToInsServiceImpl extends OrionBaseServiceImpl<IfToParameterToInsMapper, IfToParameterToIns> implements IfToParameterToInsService {

    @Autowired
    private IfToParameterToInsMapper ifToParameterToInsMapper;
    @Autowired
    private ComponentFeignService componentFeignService;

    @Autowired
    private ProjectServiceImpl projectService;

    @Autowired
    private IdeaFormService ideaFormService;

    @Autowired
    private InterfaceManagementService interfaceManagementService;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    public  IfToParameterToInsVO detail(String id) throws Exception {
        IfToParameterToIns ifToParameterToIns =ifToParameterToInsMapper.selectById(id);
        IfToParameterToInsVO result = BeanCopyUtils.convertTo(ifToParameterToIns,IfToParameterToInsVO::new);
        return result;
    }

    /**
     *  新增
     *
     * * @param ifToParameterToInsDTO
     */
    @Override
    public  IfToParameterToInsVO create(IfToParameterToInsDTO ifToParameterToInsDTO) throws Exception {
        IfToParameterToIns ifToParameterToIns =BeanCopyUtils.convertTo(ifToParameterToInsDTO,IfToParameterToIns::new);
        int insert = ifToParameterToInsMapper.insert(ifToParameterToIns);
        IfToParameterToInsVO rsp = BeanCopyUtils.convertTo(ifToParameterToIns,IfToParameterToInsVO::new);
        return rsp;
    }

    /**
     *  编辑
     *
     * * @param ifToParameterToInsDTO
     */
    @Override
    public Boolean edit(IfToParameterToInsDTO ifToParameterToInsDTO) throws Exception {
        IfToParameterToIns ifToParameterToIns =BeanCopyUtils.convertTo(ifToParameterToInsDTO,IfToParameterToIns::new);
        int update =  ifToParameterToInsMapper.updateById(ifToParameterToIns);
        return SqlHelper.retBool(update);
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        List<IfToParameterToIns> ifToParameterToIns = this.listByIds(ids);
        if(CollectionUtils.isBlank(ifToParameterToIns)){
            return  Boolean.TRUE;
        }
        boolean b = ifToParameterToIns.stream().anyMatch(i -> StringUtils.hasText(i.getInsId()));
        if(b){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "您选择的数据已经生成了参数实列，不允许移除");
        }
        int delete = ifToParameterToInsMapper.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    public Page<IfToParameterToInsVO> pages(Page<IfToParameterToInsDTO> pageRequest) throws Exception {
        Page<IfToParameterToIns> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), IfToParameterToIns::new));

        PageResult<IfToParameterToIns> page = ifToParameterToInsMapper.selectPage(realPageRequest,null);

        Page<IfToParameterToInsVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<IfToParameterToInsVO> vos = BeanCopyUtils.convertListTo(page.getContent(), IfToParameterToInsVO::new);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public boolean batchCreateOrUpdate(ImOrIfToParamBatchDTO imToParamBatchDTO) {
        String ifId = imToParamBatchDTO.getIfId();
        List<String> paramIdList = imToParamBatchDTO.getParamIdList();
        if(StrUtil.isBlank(ifId) ){
            throw new PMSException(PMSErrorCode.PMS_ERROR_ID_NULL, "意见单ID不能为空");
        }
        if(CollectionUtils.isBlank(paramIdList)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_ID_NULL, "参数ID列表不能为空");
        }
        List<String> haveParamIdList = this.haveList(ifId, paramIdList);
        if(!CollectionUtils.isBlank(haveParamIdList)){
            paramIdList.removeAll(haveParamIdList);
        }
        if(CollectionUtils.isBlank(paramIdList)){
            return Boolean.TRUE;
        }
        List<String> distinctList = paramIdList.stream().distinct().collect(Collectors.toList());
        Map<String, String> paramIdToModelIdMap = imToParamBatchDTO.getParamIdToModelIdMap();
        if(null == paramIdToModelIdMap ){
            paramIdToModelIdMap  = new HashMap<>();
        }
        List<IfToParameterToIns> imList = new ArrayList<>();
        for (String s : distinctList) {
            IfToParameterToIns im = new IfToParameterToIns();
            im.setIfId(ifId);
            im.setModelId(paramIdToModelIdMap.getOrDefault(s,""));
            im.setParameterId(s);
            imList.add(im);
        }
        return this.saveBatch(imList);
    }

    @Override
    public List<IfToParameterToInsVO> detailList(IfToParameterToInsDTO ifToParameterToInsDTO) throws Exception {

        String ifId = ifToParameterToInsDTO.getIfId();
        LambdaQueryWrapperX<IfToParameterToIns> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(IfToParameterToIns::getIfId,ifId);
        lambdaQueryWrapperX.orderByDesc(IfToParameterToIns::getCreateTime);
        List<IfToParameterToIns> list = this.list(lambdaQueryWrapperX);
        if(CollectionUtils.isBlank(list)){
            return  new ArrayList<>();
        }

        List<String> paramIdList = list.stream().map(IfToParameterToIns::getParameterId).distinct().collect(Collectors.toList());
        List<String> insIdList = list.stream().map(IfToParameterToIns::getInsId).distinct().collect(Collectors.toList());
        SearchDTO build = SearchDTO.builder().ids(paramIdList).build();
        Map<String, ParameterPoolVO> idToParameterPoo= new HashMap<>();
        ResponseDTO<List<ParameterPoolVO>> paramListResponse = componentFeignService.parameterPoolLists(build);
        if(ResponseUtils.success(paramListResponse)){
            List<ParameterPoolVO> result = paramListResponse.getResult();
            if(!CollectionUtils.isBlank(result)){
                idToParameterPoo = result.stream().collect(Collectors.toMap(ObjectVO::getId, Function.identity()));
            }
        }
        Map<String, ParameterPoolInsVO> idToPoolModule= new HashMap<>();
        if(!CollectionUtils.isBlank(insIdList)){
            build.setIds(insIdList);
            ResponseDTO<List<ParameterPoolInsVO>> search = componentFeignService.parameterPoolInsSearch(build);
            if(ResponseUtils.success(search)){
                List<ParameterPoolInsVO> result = search.getResult();
                if(!CollectionUtils.isBlank(result)){
                    idToPoolModule = result.stream().collect(Collectors.toMap(ObjectVO::getId, Function.identity()));
                }
            }
        }

        List<IfToParameterToInsVO> imToParameterVOS = BeanCopyUtils.convertListTo(list, IfToParameterToInsVO::new);
        Map<String, ParameterPoolInsVO> finalIdToPoolModule = idToPoolModule;
        Map<String, ParameterPoolVO> finalIdToParameterPoo = idToParameterPoo;
        imToParameterVOS.forEach(i->{
            ParameterPoolInsVO orDefault = finalIdToPoolModule.getOrDefault(i.getInsId(), new ParameterPoolInsVO());
            i.setInsId(orDefault.getId());
            i.setInsName(orDefault.getName());
            i.setInsCreateTime(orDefault.getCreateTime());

            ParameterPoolVO orDefault1 = finalIdToParameterPoo.getOrDefault(i.getParameterId(), new ParameterPoolVO());
            i.setParamName(orDefault1.getName());
            i.setParamNumber(orDefault1.getNumber());
            i.setAliases(orDefault1.getAliases());
        });

        return imToParameterVOS;

    }

    @Override
    public boolean copySourceDataToTarget(String sourceId, String targetId) throws Exception {
        LambdaQueryWrapperX<IfToParameterToIns> im = new LambdaQueryWrapperX<>(IfToParameterToIns.class);
        im.eq(IfToParameterToIns::getIfId,sourceId);
        List<IfToParameterToIns> list = this.list(im);
        if(CollectionUtils.isBlank(list)){
            return  Boolean.TRUE;
        }
        list.forEach(i->{
            i.setId(null);
            i.setCreateTime(null);
            i.setModifyTime(null);
            i.setOwnerId(null);
            i.setCreatorId(null);
            i.setModifyId(null);
            i.setIfId(targetId);
        });
        return this.saveBatch(list);
    }

    @Override
    public Boolean addValue(ParameterPoolInsDTO parameterPoolInsDTO) throws Exception {
        String relationId = parameterPoolInsDTO.getRelationId();
        String id = parameterPoolInsDTO.getId();
        if(StringUtils.hasText(id)){
            //调用 pdm 参数实列修改
            ResponseDTO<Boolean> responseDTO = componentFeignService.edit(parameterPoolInsDTO);
            if(ResponseUtils.success(responseDTO)){
                Boolean result = responseDTO.getResult();
                if(ObjectUtils.isEmpty(result)){
                    throw new BaseException(HttpStatus.BAD_REQUEST.value(), "调用pdm修改参数实列异常："+responseDTO.getMessage());
                }
                if(!result){
                    throw new BaseException(HttpStatus.BAD_REQUEST.value(), "调用pdm修改参数实列失败："+responseDTO.getMessage());
                }
            }
        }else{
            IfToParameterToIns byId2 = this.getById(relationId);
            if(ObjectUtils.isEmpty(byId2)){
                throw new BaseException(HttpStatus.BAD_REQUEST.value(), "当前关系不存在，请刷新后重试");
            }
            IdeaForm byId1 = ideaFormService.getById(parameterPoolInsDTO.getDataId());
            // 调用新增
            if(StringUtils.hasText(parameterPoolInsDTO.getProjectId())){
                Project byId = projectService.getById(parameterPoolInsDTO.getProjectId());
                parameterPoolInsDTO.setProjectName(byId== null?"":byId.getName());
                parameterPoolInsDTO.setDataHref("/pms/oIcmManagementDetailsIndex/"+parameterPoolInsDTO.getDataId());
            }else{
                String interfaceId = byId1.getInterfaceId();
                InterfaceManagement byId = interfaceManagementService.getById(interfaceId);
                ResponseDTO<ProductPBSSimpleVO> responseDTO = componentFeignService.detailPBSSimple(byId.getDataId());
                if(responseDTO.getCode() == HttpServletResponse.SC_OK){
                    ProductPBSSimpleVO result = responseDTO.getResult();
                    parameterPoolInsDTO.setProjectName(result == null ? "":result.getName());
                }
                parameterPoolInsDTO.setProjectId(byId.getDataId());
                parameterPoolInsDTO.setDataHref("/edm/decomposeOpinionInterfaceDetails/"+parameterPoolInsDTO.getDataId());
            }
            parameterPoolInsDTO.setDataName(byId1== null? "":byId1.getDesc());
            ResponseDTO<ParameterPoolInsVO> parameterPoolInsVOResponseDTO = componentFeignService.create(parameterPoolInsDTO);
            if(ResponseUtils.success(parameterPoolInsVOResponseDTO)){
                ParameterPoolInsVO result = parameterPoolInsVOResponseDTO.getResult();
                if(ObjectUtils.isEmpty(result)){
                    throw new BaseException(HttpStatus.BAD_REQUEST.value(), "调用pdm创建参数实列异常："+parameterPoolInsVOResponseDTO.getMessage());
                }
                String moduleId = result.getModuleId();
                byId2.setModelId(moduleId);
                byId2.setInsId(result.getId());
                this.updateById(byId2);
            }else{
                throw new BaseException(HttpStatus.BAD_REQUEST.value(), "调用pdm创建参数实列异常："+parameterPoolInsVOResponseDTO.getMessage());
            }
        }
        return Boolean.TRUE;
    }

    public List<String> haveList(String ifId,List<String> paramIdList){
        LambdaQueryWrapperX<IfToParameterToIns> im = new LambdaQueryWrapperX<>();
        im.select(IfToParameterToIns::getParameterId);
        im.in(IfToParameterToIns::getParameterId,paramIdList);
        im.eq(IfToParameterToIns::getIfId,ifId);
        List<IfToParameterToIns> list = this.list(im);
        if(CollectionUtils.isBlank(list)){
            return  new ArrayList<>();
        }
        return list.stream().map(IfToParameterToIns::getParameterId).distinct().collect(Collectors.toList());
    }
}
