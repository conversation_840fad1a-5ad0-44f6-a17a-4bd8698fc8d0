<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.github.ulisesbocchio</groupId>
        <artifactId>jasypt-spring-boot-parent</artifactId>
        <version>3.0.5</version>
    </parent>

    <artifactId>jasypt-spring-boot</artifactId>
    <packaging>jar</packaging>
    <name>Jasypt Spring Boot</name>
    <description>Jasypt Spring Boot</description>

    <dependencies>
        <dependency>
            <groupId>org.jasypt</groupId>
            <artifactId>jasypt</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>cobertura-maven-plugin</artifactId>
                <version>2.7</version>
                <dependencies>
                    <dependency>
                        <groupId>org.ow2.asm</groupId>
                        <artifactId>asm</artifactId>
                        <version>5.0.3</version>
                    </dependency>
                </dependencies>
                <executions>
                    <execution>
                        <id>clean</id>
                        <phase>pre-site</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>cobertura</id>
                        <phase>site</phase>
                        <goals>
                            <!--                            <goal>instrument</goal>-->
                            <goal>cobertura</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <reporting>
        <outputDirectory>${project.build.directory}/site</outputDirectory>
        <plugins>
            <!-- Maven Project Info Reports Plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-project-info-reports-plugin</artifactId>
                <version>3.4.1</version>
                <configuration>
                    <!--                    <dependencyLocationsEnabled>false</dependencyLocationsEnabled>-->
                </configuration>
            </plugin>
            <!-- Cobertura Code Coverage Plugin -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>cobertura-maven-plugin</artifactId>
                <version>2.7</version>
                <configuration>
                    <instrumentation>
                        <ignoreTrivial>true</ignoreTrivial>
                    </instrumentation>
                    <formats>
                        <format>html</format>
                        <format>xml</format>
                    </formats>
                    <check />
                </configuration>
                <reportSets>
                    <reportSet>
                        <id>cobertura-report</id>
                        <reports>
                            <report>cobertura</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
        </plugins>
    </reporting>
</project>