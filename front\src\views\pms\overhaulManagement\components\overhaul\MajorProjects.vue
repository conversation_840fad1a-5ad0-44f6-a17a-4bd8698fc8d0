<script setup lang="ts">
import { Chart } from 'lyra-component-vue3';
import { computed } from 'vue';

const props = defineProps<{
  data: Record<string, any>
}>();

const dataConfig = [
  {
    label: '重大项目总数',
    field: 'bigTotal',
  },
  {
    label: '重大项目已完成数',
    field: 'bigCompleted',
  },
  {
    label: '实施中作业数',
    field: 'operation',
  },
  {
    label: '已完成作业数',
    field: 'completed',
  },
];

const option = computed(() => ({
  grid: {
    left: 20,
    right: 20,
    bottom: 10,
    top: 30,
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: dataConfig.map((item) => item.label),
    axisTick: {
      show: false,
    },
    axisLabel: {
      interval: 0,
      overflow: 'truncate',
    },
  },
  yAxis: {
    type: 'value',
    minInterval: 1,
    splitLine: {
      show: false,
    },
  },
  series: [
    {
      data: dataConfig.map((item) => props.data?.[item.field]),
      type: 'bar',
      showBackground: true,
      label: {
        show: true,
        position: 'top',
      },
      backgroundStyle: {
        color: '#EEF2FE',
      },
      itemStyle: {
        color: '#6A97FA',
      },
      barWidth: 50,
    },
  ],
}));
</script>

<template>
  <div class="chart-container">
    <div class="legend">
      <div>作业总数：{{ data?.['jobTotal'] }}</div>
      <div class="unit">
        单位：个
      </div>
    </div>
    <div class="chart-wrap">
      <Chart :option="option" />
    </div>
  </div>
</template>

<style scoped lang="less">
.chart-container {
  width: 100%;
  height: 240px;
  box-shadow: 0 0 5px 0 #eee;
  border-radius: 4px;
  display: flex;
  flex-direction: column;

  .legend {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 20px 0;

    .unit {
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: -10px;
        transform: translate(-100%, -50%);
        width: 14px;
        height: 14px;
        background-color: #6B97FA;
      }
    }
  }

  .chart-wrap {
    flex-grow: 1;
    height: 0;
  }
}
</style>
