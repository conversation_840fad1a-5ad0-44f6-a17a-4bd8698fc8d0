<template>
  <Layout3
    v-if="tabsOption?.length>0"
    v-loading="loadingStatus"
    :defaultActionId="actionId"
    :menuData="tabsOption"
    :projectData="projectInfo"
    :type="2"
    :onMenuChange="contentTabsChange2"
  >
    <template #header-title>
      <div class="layoutTtitle">
        <div class="nameStyle flex-te">
          {{ projectInfo?.name }}
        </div>
        <div class="numberStyle">
          {{ projectInfo?.number }}
        </div>
      </div>
    </template>
    <template v-if="projectInfo">
      <!--      &lt;!&ndash;项目生命周期&ndash;&gt;-->
      <!--      <ProjectLifeIndex-->
      <!--        v-if="actionId==='project_lifecycle'"-->
      <!--        :projectId="id"-->
      <!--        :projectStatus="projectInfo.status"-->
      <!--        :projectBudget="projectInfo?.budget"-->
      <!--        :projectType="(!projectInfo?.type || projectInfo?.type === 'empt')?'investmentType': projectInfo?.type"-->
      <!--        @change-tabsId="changeTabsId"-->
      <!--      />-->
      <!--      <ProjectLifeAll-->
      <!--        v-if="actionId==='project_lifecycle_all'"-->
      <!--        :projectId="id"-->
      <!--      />-->
      <!--项目概况-->
      <ProjectOverview v-if="actionId==='project_overview_summary'" />

      <!--项目计划-->
      <ProjectsPlan
        v-if="actionId === 'project_plan'"
        :id="id"
        ref="projectPlanRef"
        :projectData="projectInfo"
        :status="status"
        :circumstance="circumstance"
      />

      <!--交付物-->
      <Deliverable
        v-if="actionId === 'deliverables'"
        :formId="id"
      />
      <!--基线管理 - 计划基线-->
      <BaseLine
        v-if="actionId === 'baseline_management_plan'"
        :formId="id"
      />
      <!--基线管理 - IED基线-->
      <baseLineIed
        v-if="actionId === 'baseline_management_ied'"
        :formId="id"
      />
      <!--  投资计划-->
      <InvestmentPlan
        v-if="actionId === 'investment_plan'"
        :formId="id"
      />

      <!--   技术文件清单   -->
      <TechnicalList
        v-if="actionId==='technical_list'"
        :formId="id"
        @action="updateAction"
      />

      <!--  计划管理  -->
      <PlanManagement v-if="actionId===1111112" />
      <!--  项目评审  -->
      <ProjectReview
        v-if="actionId==='project_review'"
        :projectId="id"
        :projectName="projectInfo?.name"
      />

      <!--甘特图-->
      <PlanGanttChart
        v-if="actionId === 111111202"
        :formId="id"
      />

      <!--里程碑-->
      <Milestone
        v-if="actionId === 111111203"
        :formId="id"
      />

      <!-- 项目费用管理-->
      <!-- 预算-->
      <Budget
        v-if="actionId ==='budget_management'"
        :id="id"
      />
      <!-- 成本执行-->
      <CostExecute
        v-if="actionId ==='expenses'"
        :id="id"
      />
      <!--      项目评价-->
      <ProjectEvaluate
        v-if="actionId ==='project_evaluation'"
        :id="id"
      />
      <!--      物资管理-->
      <ServiceList
        v-if="actionId ==='material_planning' "
        :id="id"
      />
      <!--      物资入库-->
      <WarehousingList
        v-if="actionId ==='material_entry'"
        :id="id"
      />
      <!--物资计划-->
      <MaterialPlan
        v-if="actionId ==='material_plan'"
        :id="id"
      />
      <!--物资追踪-->
      <MaterialTrace
        v-if="actionId ==='material_trace'"
        :id="id"
      />
      <!--  需求管理  -->
      <BusinessDemand
        v-if="actionId==='demand_management'"
        modelName="pms"
      />
      <!-- 问题管理 -->
      <QuestionIndex
        v-if="actionId==='issue_management'"
        modelName="pms"
      />
      <!--风险管理-->
      <BusinessRisk
        v-if="actionId==='risk_management'"
        :id="id"
      />
      <!--风险登记册-->
      <PreRisk
        v-if="actionId==='risk_register'"
        :id="id"
      />
      <!-- 变更管理 -->
      <!-- <ChangeManagement v-if="tabsIndex === 5" :id="id" /> -->

      <!--成员管理-->
      <PeopleManege
        v-if="actionId==='member_management'"
        :id="id"
      />

      <!--干系人管理-->
      <Stakeholder
        v-if="actionId==='stakeholder_management'"
        :id="id"
      />

      <!-- 文档管理 -->
      <DocManagement
        v-if="actionId==='document_repository'"
        :id="id"
      />
      <!-- 结项管理 -->
      <!--      <EndManagement-->
      <!--        v-if="actionId==='project_closure'"-->
      <!--        :id="id"-->
      <!--      />-->
      <ProjectAcceptance
        v-if="actionId==='project_closure'"
        :id="id"
      />
      <!-- 统计分析 -->
      <StaticsticManagement
        v-if="actionId==='project_reports'"
        :id="id"
      />
      <!--基本信息-->
      <ProjectInfo
        v-if="actionId==='project_information'"
        :id="id"
      />

      <!--项目角色-->
      <ProjectRole
        v-if="actionId==='project_roles'"
        :id="id"
      />

      <!--任务科目-->
      <ProjectType
        v-if="actionId===1111111303"
        :id="id"
      />

      <!--预警设置-->
      <PreRiskSet
        v-if="actionId==='warning_settings'"
        :id="id"
      />

      <!--预警设置-->
      <PreFinance
        v-if="actionId==='warning_finance'"
        :id="id"
      />

      <!--    变更管理-->
      <ChangeApply
        v-if="actionId===11111114"
        :formId="id"
        ecrDirName="项目"
        :showBtn="true"
      />

      <!--合同管理-->
      <ContractManage
        v-if="actionId === 'contract_management'"
        :projectId="id"
        :projectName="projectInfo?.name"
      />

      <PushModel />

      <!--应收管理-->
      <Receivable v-if="actionId==='budget_cost'" />
      <!--实收管理-->
      <RealIncome v-if="actionId==='revenue_management'" />
      <!--采购管理-->
      <ProcureManagement v-if="actionId==='procure_management'" />
      <!--质量管控项-->
      <QualityControlItem v-if="actionId==='quality_control_item'" />
      <!--接口管理-->
      <IcmManagementIndex v-if="actionId==='ICM_management'" />
      <!--相关对象-->
      <RelatedObjects
        v-if="actionId==='related_objects'"
        :className="projectInfo.className"
        :dataId="projectInfo.id"
        :treeApi="relatedObjectsTreeApi"
        :router="router"
        :name="projectInfo.name"
        :number="projectInfo.number"
        :showChildren="false"
      />
      <!--工时预估-->
      <WorkingHours v-if="actionId==='project_working_hours'" />
      <!--工时填报-->
      <TimeEntry v-if="actionId==='project_time_entry'" />

      <TimeEntryManage v-if="actionId==='project_time_entry_manage'" />

      <!--        项目规则设置-->
      <ProjectRule v-if="actionId==='project_rule'" />
      <!--      工作日报-->
      <DayReportIndex v-if="actionId==='work_daily_report'" />
      <IncomeManagement v-if="actionId==='income_management'" />
      <!--      工作日报审核-->
      <DayReportCheck v-if="actionId==='daily_report_check'" />

      <!--      工作周报-->
      <WeeklyReportIndex v-if="actionId==='weekly_work_report'" />
      <WeeklyReportCheck v-if="actionId==='weekly_report_check'" />
      <ProjectPerformanceEvaluation v-if="actionId==='project_performance_evaluation'" />
      <!--      费用支出-->
      <ExpenseManagement v-if="actionId==='expense_management'" />
      <!--      预算申请-->
      <BudgetRequest v-if="actionId==='budget_request'" />
      <!--      预算调整-->
      <BudgetAdjustment v-if="actionId==='budget_adjustment'" />

      <!--      预算管理-->
      <BudgetManage v-if="actionId==='budget_manage'" />
      <!--      合同管理-->
      <ContractManagement v-if="actionId==='PmsContractManagement'" />
      <!--      预算成本-->
      <CostBudget v-if="actionId==='cost_budget'" />

      <!--      资产转固-->
      <AssetConsolidation v-if="actionId==='asset_consolidation'" />
    </template>
    <template #header-right>
      <BasicTableAction
        :actions="actionsBtn"
        type="button"
      />

      <!--      <BasicButton-->
      <!--        v-if="projectInfo?.status===101 && isPower('PMS_XMXQ_container_01_button_01',powerData)"-->
      <!--        icon="sie-icon-xiangmushenbao"-->
      <!--        type="primary"-->
      <!--        :loading="fetching"-->
      <!--        @click="handleDeclaration"-->
      <!--      >-->
      <!--        发起申报-->
      <!--      </BasicButton>-->
      <!--      <BasicButton-->
      <!--        v-if="isPower('PMS_XMXQ_container_01_button_02',powerData)"-->
      <!--        type="primary"-->
      <!--        icon="sie-icon-xiangmuyanshou"-->
      <!--        @click="() => handleProjectAcceptance()"-->
      <!--      >-->
      <!--        项目验收-->
      <!--      </BasicButton>-->
    </template>

    <!--创建申报-->
    <CreateAndEditDrawer
      :onConfirmCallback="(result)=>goDeclarationDetail(result.id)"
      @register="registerCreateAndEdit"
    />
  </Layout3>
</template>

<script lang="ts">
import {
  computed, defineComponent, nextTick, onMounted, provide, reactive, readonly, Ref, ref, toRefs,
} from 'vue';
import {
  BasicTableAction,
  isPower,
  ITableActionItem,
  Layout3,
  openModal as openModalNew,
  RelatedObjects,
  useDrawer,
} from 'lyra-component-vue3';
import { Modal } from 'ant-design-vue';
import { Router, useRoute, useRouter } from 'vue-router';
import ProjectOverview from '/@/views/pms/projectLibrary/pages/components/projectOverview/Index.vue';
import PlanManagement from './planManagement/index.vue';
import DocManagement from './docManagement/index.vue';
// import EndManagement from './endManagement/index.vue';
import ProjectAcceptance from './ProjectAcceptance/index.vue';
import StaticsticManagement
  from '/src/views/pms/projectLaborer/projectLab/projectList/menuComponents/staticsticManagement/index.vue';
import PushModel from '/@/views/pms/projectLaborer/pushModel/index.vue';
import Api from '/@/api';
import { listenerRouteChange } from '/@/logics/mitt/routeChange';
import useIndex from '/@/views/pms/projectLaborer/zkhooks/useLocalS.js';
import { statisticType } from '/@/views/pms/projectLaborer/store/modules/statistic';
import { ChangeApply } from './ChangeApply';
// import { BusinessQuestion } from '../components/BusinessQuestion';
import { BusinessDemand } from '../components/BusinessDemand';
// import ProjectsPlan from './planManagement/projectsPlan/index.vue';
import ProjectsPlan from './projectPlan/ProjectPlanIndex.vue';
import Milestone from './planManagement/milestone/index.vue';
import Deliverable from './planManagement/deliverable/index.vue';
import BaseLine from './planManagement/baseLine/index.vue';
import baseLineIed from './planManagement/baseLineIed/index.vue';
import PlanGanttChart from './planManagement/planGanttChart/index.vue';
import PreRisk from './riskManagement/preRisk/index.vue';
import { BusinessRisk } from '../components/BusinessRisk';
import PeopleManege from './sourceManagement/components/peopleManege.vue';
import Stakeholder from './sourceManagement/components/Stakeholder.vue';
import {
  PreFinance, PreRiskSet, ProjectRole, ProjectType,
} from './projectSet';
import { CreateAndEditDrawer } from '/@/views/pms/projectApplication/components';
import CostExecute from './projectCostManage/costExecute/index.vue';
import Budget from './projectCostManage/budget/index.vue';
import ProjectEvaluate from './projectEvaluate/index.vue';
import ServiceList from './materialManagement/serviceList/index.vue';
import WarehousingList from './materialManagement/warehousingList/index.vue';
import ProjectInfo from './projectSet/projectInfo.vue';
import ContractManage from './contractManage/ContractManageIndex.vue';
import IncomeManagement from '/@/views/pms/incomeManagement/index.vue';
import QuestionIndex from './questionManagement/questionIndex.vue';

import { postAcceptance } from '/@/views/pms/api';
import Receivable from '/@/views/pms/projectLibrary/pages/components/receivable/Receivable.vue';
import RealIncome from '/@/views/pms/projectLibrary/pages/components/realIncome/RealIncome.vue';
import ProcureManagement from '/@/views/pms/projectLibrary/pages/components/procureManagement/ProcureManagement.vue';
import ProjectReview from '/@/views/pms/projectLibrary/pages/components/projectReview/index.vue';
import QualityControlItem from '/@/views/pms/projectLibrary/pages/components/qualityControlItem/index.vue';
import { setTitleByRootTabsKey } from '/@/utils';
import WorkingHours from './WorkingHours/index.vue';
import TimeEntry from './TimeEntry/index.vue';

import TimeEntryManage from './TimeEntry/manageIndex.vue';
import IcmManagementIndex
  from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/icmManagement/IcmManagementIndex.vue';
import ProjectRule from './projectRule/ProjectRule.vue';
import DayReportIndex from './dayReport/DayReportIndex.vue';
import DayReportCheck from './dayReportCheck/DayReportIndex.vue';
import WeeklyReportIndex from './weeklyReport/WeeklyReportIndex.vue';
import ProjectPerformanceEvaluation from './projectPerformanceEvaluation/index.vue';

import WeeklyReportCheck from './weekReportCheck/WeekReportIndex.vue';
import InvestmentPlan from './investmentPlan/index.vue';
import TechnicalList from './technicalList/index.vue';
import CostBudget from './CostBudget/index.vue';
import ContractManagement from './pmsContractManagement/PmsContractManagementIndex.vue';
// 费用支出
import ExpenseManagement from './ExpenseManagement/index.vue';

// 预算申请
import BudgetRequest from './BudgetRequest/index.vue';

// 预算调整
import BudgetAdjustment from './BudgetAdjustment/index.vue';
// 预算管理
import BudgetManage from './BudgetManage/index.vue';
import MaterialPlan from './materialPlan/index.vue';
import MaterialTrace from './materialTrace/index.vue';
import { startById } from '/@/views/pms/api/project';
import ProjectPlanDetail from '/@/views/pms/projectLaborer/projectLab/pages/projectPlanDetails/ProPlanDetails.vue';
import { useRouteParamFormUserHome } from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/util';
import AssetConsolidation from './AssetConsolidation/AssetConsolidation.vue';

export default defineComponent({
  name: 'MenuComponents',
  components: {
    AssetConsolidation,
    QualityControlItem,
    BasicTableAction,
    RealIncome,
    Receivable,
    IncomeManagement,
    ContractManagement,
    ProjectOverview,
    PlanManagement,
    Layout3,
    DocManagement,
    // EndManagement,
    ProjectAcceptance,
    StaticsticManagement,
    PushModel,
    ChangeApply,
    BusinessDemand,
    ProjectsPlan,
    Milestone,
    Deliverable,
    BaseLine,
    baseLineIed,
    PlanGanttChart,
    BusinessRisk,
    PreRisk,
    PeopleManege,
    Stakeholder,
    ProjectRole,
    ProjectType,
    PreRiskSet,
    PreFinance,
    // ProjectLifeIndex,
    // ProjectLifeAll,
    CostExecute,
    Budget,
    ProjectEvaluate,
    ServiceList,
    WarehousingList,
    MaterialPlan,
    MaterialTrace,
    CreateAndEditDrawer,
    ProjectInfo,
    ContractManage,
    ProcureManagement,
    ProjectReview,
    WorkingHours,
    TimeEntry,
    ProjectRule,
    DayReportIndex,
    DayReportCheck,
    TimeEntryManage,
    WeeklyReportIndex,
    WeeklyReportCheck,
    ProjectPerformanceEvaluation,
    TechnicalList,
    IcmManagementIndex,
    InvestmentPlan,
    RelatedObjects,
    ExpenseManagement,
    BudgetRequest,
    BudgetAdjustment,
    BudgetManage,
    CostBudget,
    QuestionIndex,
  },
  setup() {
    const statisticTypeStore = statisticType();
    const { removeRouteParamFormUserHome } = useRouteParamFormUserHome();
    const projectPlanRef = ref();
    const [registerCreateAndEdit, { openDrawer: openCreateAndEdit }] = useDrawer();
    const route = useRoute();
    const router: Router = useRouter();
    const state = reactive({
      tabsIndex: route.query.type ? Number(route.query.type) : 1111111,
      loadingStatus: false,
      id: route.query.id as string,
      className: '',
      powerData: [],
      tabsOption: computed(() => [
        {
          id: 'project_overview_summary',
          name: '项目概况',
          isShow: isPower('PMS_XMXQ_container_02', state.powerData),
        },
        {
          id: 'plan_management',
          name: '项目计划',
          isShow: isPower('PMS_XMXQ_container_03', state.powerData),
          children: [
            {
              id: 'project_plan',
              name: '项目计划',
              isShow: isPower('PMS_XMXQ_container_03_01', state.powerData),
            },
            {
              id: 'baseline_management',
              name: '基线管理',
              isShow: isPower('PMS_XMXQ_container_03_03', state.powerData),
              children: [
                {
                  id: 'baseline_management_plan',
                  name: '项目计划基线',
                  isShow: isPower('PMS_XMXQ_container_03_03_01', state.powerData),
                },
              ],
            },
          ].filter((item) => item.isShow),
        },
        {
          id: 'risk_issue',
          name: '质量管理',
          isShow: isPower('PMS_XMXQ_container_07', state.powerData),
          children: [
            {
              id: 'risk_management',
              name: '风险管理',
              isShow: isPower('PMS_XMXQ_container_07_01', state.powerData),
            },
            {
              id: 'issue_management',
              name: '问题管理',
              isShow: isPower('PMS_XMXQ_container_07_02', state.powerData),
            },
            {
              id: 'quality_control_item',
              name: '质量管控项',
              isShow: isPower('PMS_XMXQ_container_17', state.powerData) && state.projectInfo?.projectType !== 'project_type_activity',
            },
          ],
        },
        {
          id: 'project_documents',
          name: '项目文档',
          isShow: isPower('PMS_XMXQ_container_09', state.powerData),
          children: [
            {
              id: 'document_repository',
              name: '项目文档库',
              isShow: isPower('PMS_XMXQ_container_09_01', state.powerData),
            },
            {
              id: 'deliverables',
              name: '任务交付物',
              isShow: isPower('PMS_XMXQ_container_03_02', state.powerData),
            },
          ].filter((item) => item.isShow),
        },
        {
          id: 'resource_management',
          name: '项目人力',
          isShow: isPower('PMS_XMXQ_container_06', state.powerData),
          children: [
            {
              id: 'project_roles',
              name: '项目角色',
              isShow: isPower('PMS_XMXQ_container_12_02', state.powerData),
            },
            {
              name: '成员管理',
              id: 'member_management',
              isShow: isPower('PMS_XMXQ_container_06_01', state.powerData),
            },
            {
              name: '干系人管理',
              isShow: isPower('PMS_XMXQ_container_06_02', state.powerData),
              id: 'stakeholder_management',
            },
          ].filter((item) => item.isShow),
        },
        {
          id: 'acceptance_closure',
          name: '项目验收',
          isShow: isPower('PMS_XMXQ_container_08', state.powerData),
          children: [
            {
              id: 'project_evaluation',
              name: '项目评价',
              isShow: isPower('PMS_XMXQ_container_08_02', state.powerData),
            },
            {
              id: 'project_closure',
              name: '项目验收',
              isShow: isPower('PMS_XMXQ_container_08_01', state.powerData),
            },
            {
              id: 'asset_consolidation',
              name: '资产转固',
              isShow: isPower('PMS_XMXQ_container_08_01', state.powerData),
            },

          ].filter((item) => item.isShow),
        },
        {
          id: 'project_basic_settings',
          name: '项目设置',
          isShow: isPower('PMS_XMXQ_container_12', state.powerData),
          children: [
            {
              id: 'project_information',
              name: '项目信息',
              isShow: isPower('PMS_XMXQ_container_12_01', state.powerData),
            },
            {
              id: 'warning_settings',
              name: '预警设置',
              isShow: isPower('PMS_XMXQ_container_12_03', state.powerData),
            },
            {
              id: 'warning_finance',
              name: '财务指标维护',
              isShow: isPower('PMS_XMXQ_container_12_04', state.powerData),
            },
          ].filter((item) => item.isShow),
        },
      ].filter((item) => item.isShow)),
      projectInfo: null,
    });
    const state2 = reactive({
      actionId: '',
    });
    const zkindex = useIndex('projectIndex');
    const QuestionDetailsLocal = useIndex('QuestionDetailsLocal');
    const riskDetailsIndexLocal = useIndex('riskDetailsIndexLocal');

    const status: Ref<number> = ref(0);

    const circumstance: Ref<number> = ref(0);
    const contentTabsChange2 = (index) => {
      if (index.id === 'project_plan') {
        status.value = index.status || 0;
        circumstance.value = index.circumstance || 0;
      } else {
        removeRouteParamFormUserHome();
        status.value = 0;
        circumstance.value = 0;
      }
      state2.actionId = index.id;
      zkindex.value = index.id;
    };
    if (route.query.tabTarget === 'plan') {
      state2.actionId = 'project_plan';
      contentTabsChange2({ id: 'project_plan' });
    }

    provide('changeTab', contentTabsChange2);
    function updateAction(id) {
      contentTabsChange2({ id });
    }

    // 注入设置菜单的方法
    provide('updateAction', updateAction);

    async function handleProjectAcceptance() {
      const result = await new Api(`/pms/acceptance-form/byProjectId/${route.query.id}`).fetch('', '', 'get');
      if (result) {
        router.push({
          name: 'ProjectAcceptanceDetail',
          query: {
            projectId: result.projectId,
            id: result.id,
          },
        });
      } else {
        Modal.confirm({
          title: '项目验收',
          content: '确认是否要发起项目验收？',
          async onOk() {
            const result = await postAcceptance({
              projectId: route.query.id as string,
              type: 'PROJECT',
            });
            if (result) {
              router.push({
                name: 'ProjectAcceptanceDetail',
                query: {
                  projectId: result.projectId,
                  id: result.id,
                },
              });
            }
          },
        });
      }
    }

    async function jumpProjectAcceptance() {
      const result = await new Api(`/pms/acceptance-form/byProjectId/${route.query.id}`).fetch('', '', 'get');
      if (result) {
        router.push({
          name: 'ProjectAcceptanceDetail',
          query: {
            projectId: route.query.id,
            id: result.id,
          },
        });
      }
    }

    // 查看详情
    function onClickBulletFame(record) {
      const modalRef = ref();
      const modalData = {
        id: record.id,
      };
      openModalNew({
        title: '项目计划详情',
        width: 1200,
        height: 800,
        isFullScreen: false,
        content(h) {
          return h(ProjectPlanDetail, {
            ref: modalRef,
            modalData,
            router,
          });
        },
        footer: null,
      });
    }

    onMounted(async () => {
      QuestionDetailsLocal.value = parseInt(JSON.stringify(0));
      riskDetailsIndexLocal.value = parseInt(JSON.stringify(0));
      const { isEnable, getRouteParamFormUserHome } = useRouteParamFormUserHome();
      if (isEnable()) {
        state2.actionId = 'project_plan';
        await nextTick();
        onClickBulletFame({ id: getRouteParamFormUserHome('sourceId') });
      }
    });
    const provideProjectId = ref(state.id);
    provide('provideProjectId', readonly(provideProjectId));
    provide('projectId', route.query.id);
    provide('projectIdNew', computed(() => route.query.id));
    // 权限分发
    provide(
      'powerData',
      computed(() => state.powerData),
    );
    statisticTypeStore.setprojectid(state.id);

    // 设置项目标题
    async function setTitle() {
      if (route.name !== 'MenuComponents' || !state.id) return;
      state.loadingStatus = true;
      const projectInfo = state.projectInfo
          || (await new Api(`/pms/project/detail/${state.id}`)
            .fetch({ pageCode: 'PMS0004' }, '', 'GET')
            .then((data) => {
              state.projectInfo = data;
              state.powerData = data.detailAuthList ?? [];
              return data;
            }));
      state.loadingStatus = false;
      if (route.name === 'MenuComponents') {
        setTitleByRootTabsKey(route?.query?.rootTabsKey as string, projectInfo?.name);
      }
      if (!state2.actionId) {
        if (route.query.tabType && route.query.tabType === 'project_plan') {
          state2.actionId = 'project_plan';
          return;
        }
        if (route.query.actionId) {
          state2.actionId = actionId;
          return;
        }
        state2.actionId = 'project_overview_summary';
      }
    }

    listenerRouteChange(() => {
      setTitle();
    });
    // 数据
    provide(
      'formData',
      computed(() => state.projectInfo),
    );
    provide(
      'getFormData',
      computed(() => getFormData),
    );

    function getFormData() {
      new Api(`/pms/project/detail/${state.id}`)
        .fetch('', '', 'GET')
        .then((data) => {
          state.projectInfo = data;
        });
    }

    // 发起申报
    const fetching: Ref<boolean> = ref(false);

    async function handleDeclaration() {
      fetching.value = true;
      try {
        const result = await new Api('/pms/projectDeclare/getByProjectId').fetch('', state.projectInfo?.id, 'GET');
        // 判断是否关联申报，有调整申报详情，无则创建关联关系
        if (result?.id) {
          goDeclarationDetail(result.id);
        } else {
          openCreateAndEdit(true, {
            operationType: 'fixed',
            resUserId: state.projectInfo?.resPerson,
            resUserName: state.projectInfo?.resPersonName,
            rspDeptId: state.projectInfo?.resDept,
            rspDeptName: state.projectInfo?.resDeptName,
            projectSource: state.projectInfo?.projectSource,
            projectId: state.projectInfo?.id,
            projectType: state.projectInfo?.projectType,
            projectName: state.projectInfo?.name,
            projectNumber: state.projectInfo?.number,
            projectEndTime: state.projectInfo?.projectEndTime,
            projectStartTime: state.projectInfo?.projectStartTime,
          });
        }
      } finally {
        fetching.value = false;
      }
    }

    // 调整申报详情
    function goDeclarationDetail(id) {
      router.push({
        name: 'ProjectApplicationDetail',
        params: {
          id,
        },
      });
    }

    function changeTabsId(val) {
      let myMap = {
        // 发起项目变更
        startChange: 11111114,
        // 添加采购订单
        addPurchasingOrder: 'procure_management',
        // 管理项目成本
        manageCost: 'revenue_management',
        // 编制物资计划
        prepareMaterialPlan: 'material_planning',
        // 添加物资入库
        materialStorage: 'material_entry',
        // 查看项目交付物
        viewDeliver: 'deliverables',
        // 编制项目预算
        prepareBudget: 'budget_management',
        // 发起项目验收
        startAcceptance: '',
        // 项目工时预估
        workEstimate: 'project_working_hours',
        // 工时填报
        workFill: 'project_time_entry',
        // 发起立项
        startApproval: '',
        // 查看立项
        viewApproval: '',
        // 发起项目评价
        startEvaluation: 'project_evaluation',
        // 维护项目成员
        maintenanceMember: 'member_management',
        // 管理项目营收
        manageRevenue: 'budget_cost',
        // 管理项目文档
        manageDocument: 'document_repository',
        // 发起合同签订
        startContractSigning: 'contract_management',
        // 添加项目合同
        addContract: 'contract_management',
        // "viewDeclare"  "查看申报"

        viewDeclare: 'project_information',
        // // 项目申报
        // PROJECT_DECLARE: 'contract_management',

        // 维护项目风险
        maintenanceRisk: 'risk_management',
        // 维护项目问题
        maintenanceProblem: 'issue_management',

        // 项目计划编制
        startScheme: 'project_plan',
      };
      if (val.key === 'startDeclare' || val.key === 'viewDeclare') {
        handleDeclaration();
      } else if (val.key === 'startAcceptance') {
        handleProjectAcceptance();
      } else if (val.key === 'viewAcceptance') {
        jumpProjectAcceptance();
      } else {
        state2.actionId = myMap[val.key];
      }
    }

    const actionsBtn: ITableActionItem[] = [
      {
        text: '启动项目',
        type: 'primary',
        icon: 'sie-icon-xiangmushenbao',
        isShow: () => state.projectInfo?.status === 101 && isPower('PMS_XMXQ_container_01_button_03', state.powerData),
        onClick(record: any) {
          Modal.confirm({
            title: '请确认是否启动项目',
            content: '启动项目后，项目将进入执行状态',
            async onOk() {
              await startById(state.projectInfo?.id);
              await getFormData();
            },
          });
        },
      },
      // {
      //   text: '关闭项目',
      //   type: 'primary',
      //   icon: 'sie-icon-xiangmushenbao',
      //   isShow: () => isPower('PMS_XMXQ_container_01_button_04', state.powerData),
      //   onClick(record: any) {
      //     Modal.confirm({
      //       title: '请确认是否关闭项目',
      //       content: '关闭项目后，项目将进入关闭状态',
      //       async onOk() {
      //         await closeById(state.projectInfo?.id);
      //         await getFormData();
      //       },
      //     });
      //   },
      // },
      // {
      //   text: '发起申报',
      //   isShow: () => state.projectInfo?.status === 101 && isPower('PMS_XMXQ_container_01_button_01', state.powerData),
      //   type: 'primary',
      //   icon: 'sie-icon-xiangmushenbao',
      //   onClick(record: any) {
      //     handleDeclaration();
      //   },
      // },
      // {
      //   text: '项目验收',
      //   icon: 'sie-icon-xiangmuyanshou',
      //   isShow: () => isPower('PMS_XMXQ_container_01_button_02', state.powerData),
      //   onClick(record: any) {
      //     handleProjectAcceptance();
      //   },
      // },
    ];
    return {
      ...toRefs(state),
      ...toRefs(state2),
      contentTabsChange2,
      isPower,
      handleProjectAcceptance,
      handleDeclaration,
      fetching,
      goDeclarationDetail,
      registerCreateAndEdit,
      changeTabsId,
      actionsBtn,
      updateAction,
      router,
      relatedObjectsTreeApi() {
        return new Api('/union/model/projectSchemeModelList').fetch('', '', 'GET');
      },
      status,
      circumstance,
      projectPlanRef,
    };
  },
});
</script>

<style scoped lang="less">
.layoutTtitle {
  width: 350px;
  padding: 5px ~`getPrefixVar('content-padding-left')`;

  .nameStyle {
    font-weight: 400;
    font-style: normal;
    color: #444B5E;
    font-size: 18px;
    height: 29px;
    line-height: 29px;
  }

  .numberStyle {
    font-size: 12px;
    color: #969EB4;
  }
}
</style>
