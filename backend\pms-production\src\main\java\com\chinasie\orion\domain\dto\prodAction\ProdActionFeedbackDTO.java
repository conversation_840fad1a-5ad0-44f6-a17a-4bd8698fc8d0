package com.chinasie.orion.domain.dto.prodAction;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/10/29/10:06
 * @description:
 */
@Data
public class ProdActionFeedbackDTO implements Serializable {
    @ApiModelProperty("子数据ID")
    @NotEmpty(message = "子数据ID不能为空")
    private String id;
    @ApiModelProperty("反馈信息")
    @NotEmpty(message = "反馈信息不能为空")
    private String feedbackInfo;

}
