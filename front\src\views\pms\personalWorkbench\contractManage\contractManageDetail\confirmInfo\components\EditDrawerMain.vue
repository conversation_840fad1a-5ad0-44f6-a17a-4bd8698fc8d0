<template>
  <BasicForm
    @register="register"
  >
    <template #orderAndNodeParamDTOList>
      <DetailsLayout
        :is-form-item="true"
        title="订单节点选择"
      >
        <div class="flex flex-ver flex-fs">
          <Select
            v-model:value="state.nodeSortDTOList"
            mode="multiple"
            style="width: 100%"
            placeholder="请选择节点"
            option-label-prop="label"
            :options="state.nodeOptions"
            @change="changeNode($event,index)"
          >
            <template #option="{label,value,payDesc}">
              <div
                v-if="value==='all'"
                class="option-item"
              >
                {{ label }}
              </div>
              <div
                v-else
                class="option-item"
              >
                <span
                  class="option-item-content"
                  style="width:50%;"
                >{{ label }}</span>
                <span
                  class="option-item-desc flex-te"
                  style="width:50%;"
                >{{ payDesc }}</span>
              </div>
            </template>
          </Select>
        </div>
      </DetailsLayout>
    </template>
    <template #materialsAuditorName>
      <DetailsLayout
        :is-form-item="true"
        title="节点确认材料审核人"
      >
        <InputSearch
          v-model:value="state.user.materialsAuditorName"
          @search="openModalUser(true)"
          @focus="(e)=>{
            e.target.blur();
            openModalUser(true)
          }"
        />
      </DetailsLayout>
    </template>
    <template #isFinish>
      <DetailsLayout
        :is-form-item="true"
        title="服务确认是否已完成"
      >
        <Select
          v-model:value="state.isFinish"
          :options="state.serverOptions"
          placeholder="请选择服务是否已完成"
        />
      </DetailsLayout>
    </template>
    <template #fileDTOList>
      <DetailsLayout
        :is-form-item="true"
        title="节点确认材料上传"
      >
        <BasicUpload
          :max-number="100"
          :accept="'.rar,.jpg,.zip,.pdf,.docx,.doc,.xls,.xlsx,.png'"
          buttonType="default"
          button-text="上传附件"
          :isClassification="false"
          :isToolRequired="false"
          @save-change="saveChange"
        />
        <div class="file-list">
          <div
            v-for="(item,index) in state.fileDTOList"
            :key="index"
            class="file-item"
          >
            <span class="file-name">{{ item.name }}.{{ item['filePostfix'] }}</span>
            <Icon
              class="remove"
              icon="sie-icon-close"
              size="16"
              @click="removeFile(index)"
            />
          </div>
        </div>
      </DetailsLayout>
    </template>
    <template #notarizeDescription>
      <DetailsLayout
        :is-form-item="true"
        title="节点确认说明"
      >
        <Textarea
          v-model:value="state.notarizeDescription"
          placeholder="请输入节点确认信息"
          :rows="4"
          maxlength="1000"
        />
      </DetailsLayout>
    </template>
  </BasicForm>

  <!--选择审批人-->
  <SelectUserModal
    :on-ok="selectUserChange"
    selectType="radio"
    @register="registerModalUser"
  />
</template>

<script setup lang="ts">
import {
  BasicForm, BasicUpload, getDict, Icon, SelectUserModal, useForm, useModal,
} from 'lyra-component-vue3';
import { InputSearch, Select, Textarea } from 'ant-design-vue';
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import {
  inject, nextTick, onMounted, reactive,
} from 'vue';
import { Rule } from 'ant-design-vue/es/form';
import { useRoute } from 'vue-router';
import { contractDetailKey } from '../../types';
import Api from '/@/api';

const props = defineProps<{
  record: any
}>();

const emit = defineEmits<{
  (e: 'loadingChange', loading: boolean): void
}>();
const route = useRoute();
const state = reactive({
  // 支付状态
  payNodeTypeOptions: [],
  // 合同详情
  contractDetail: inject(contractDetailKey),
  // 采购订单源数据
  orderOptions: [],
  // 订单支付节点选择项
  payOptions: [],
  // 采购订单提交项
  orderList: [],

  nodeOptions: [],
  nodeSortDTOList: [],

  // 审核人提交项
  user: {
    materialsAuditorId: '',
    materialsAuditorName: '',
    // materialsAuditorNo: '',
  },

  // 服务确认选择项
  serverOptions: [
    {
      label: '是',
      value: '1',
    },
    {
      label: '否',
      value: '0',
    },
    {
      label: '不适用',
      value: '2',
    },
  ],
  // 服务确认提交项
  isFinish: '',
  // 节点确认说明
  notarizeDescription: '',
  // 删除的文件id集合
  deleteFileIdList: [],
  // 文件id列表
  fileDTOList: [],

});

// 订单节点选择校验规则
const validateNode = async (_rule: Rule) => {
  if (state.nodeSortDTOList?.some((item) =>
    !item)) {
    return Promise.reject('请选择');
  }
  return Promise.resolve();
};
// 节点确认材料审核人校验规则
const validateAuditor = async (_rule: Rule) => {
  if (!state.user?.materialsAuditorId) {
    return Promise.reject('请选择材料审核人');
  }
  return Promise.resolve();
};
const validateFinish = async (_rule: Rule) => {
  if (!state.isFinish) {
    return Promise.reject('请选择服务是否已完成');
  }
  return Promise.resolve();
};

const [registerModalUser, { openModal: openModalUser }] = useModal();
const [register, formMethods] = useForm({
  actionColOptions: {
    span: 24,
  },
  showActionButtonGroup: false,
  schemas: [
    {
      field: 'orderAndNodeParamDTOList',
      slot: 'orderAndNodeParamDTOList',
      component: 'Input',
      rules: [{ validator: validateNode }],
      colProps: {
        span: 24,
      },
    },
    {
      field: 'materialsAuditorName',
      slot: 'materialsAuditorName',
      component: 'Input',
      rules: [
        {
          validator: validateAuditor,
        },
      ],
      colProps: {
        span: 12,
      },
    },
    {
      field: 'isFinish',
      slot: 'isFinish',
      component: 'Input',
      rules: [
        {
          validator: validateFinish,
          trigger: ['change', 'blur'],
        },
      ],
      colProps: {
        span: 12,
      },
    },
    {
      field: 'fileDTOList',
      slot: 'fileDTOList',
      component: 'Input',
      colProps: {
        span: 24,
      },
    },
    {
      field: 'notarizeDescription',
      slot: 'notarizeDescription',
      component: 'Input',
      colProps: {
        span: 24,
      },
    },
  ],
});

onMounted(() => {
  initFormData();
  getPayNodeOptions();
});
// 获取支付状态的字典
async function getPayNodeOptions() {
  state.payNodeTypeOptions = await getDict('dict1716700830633230336');
}

// 初始化表单数据
async function initFormData() {
  try {
    if (props?.record?.contractPayNodeVOList) {
      state.orderList = await Promise.all(props.record?.contractPayNodeVOList?.map(async (item) => ({
        ...item,
        nodeSortDTOList: props.record?.contractPayNodeVOList?.map((v) => v.id),
        nodeOptions: await getNodeOptions(item.orderNumber),
      })));
    }
    state.nodeOptions = state.orderList[0].nodeOptions;
    state.nodeSortDTOList = state.orderList[0].nodeSortDTOList;
    if (props.record?.contractPayNodeConfirmVO) {
      state.user = {
        materialsAuditorId: props.record.contractPayNodeConfirmVO
          ?.auditUserId,
        materialsAuditorName: props.record.contractPayNodeConfirmVO
          ?.auditUserName,
      };
      state.fileDTOList = props?.record?.documentVOList;
      state.isFinish = props.record.contractPayNodeConfirmVO?.serviceComplete;
      state.notarizeDescription = props.record.contractPayNodeConfirmVO?.confirmDesc;
    } else {
      state.user = {
        materialsAuditorId: state.contractDetail?.contractCreatorId,
        materialsAuditorName: state.contractDetail?.contractCreatorId ? state.contractDetail?.contractCreatorName : '',
      };
    }
  } finally {
    emit('loadingChange', false);
  }
}

// 获取节点下拉数据
async function getNodeOptions(orderNumber: string) {
  const result = await new Api(`/pas/contractPayNodeConfirm/payNode/list/contractId/${route.query.id}`).fetch('', '', 'GET').finally(() => {
  });
  let nodeOptions = result?.map((item, index) => ({
    label: `${index + 1}-${state.payNodeTypeOptions.find((result) => result.value === item.payType)?.description}`,
    value: item.id,
    payDesc: item.payDesc || '',
    key: item.id,
    disabled: !item.isConfirm,
    payDescription: item.payDesc || '',
    serialNumber: item.serialNumber,
  }));

  nodeOptions.unshift({
    label: '全部',
    value: 'all',
    disabled: nodeOptions.every((item) => item.disabled),
  });
  return nodeOptions;
}

function changeNode(val: string[], index: number) {
  if (val.includes('all')) {
    state.nodeSortDTOList = state.orderList.filter((item) => !item.disabled && item.value !== 'all').map((item) =>
      item.value);
  }

  nextTick(() => {
    formMethods.validateFields(['orderAndNodeParamDTOList']);
  });
}

// 选择审批人
function selectUserChange(users: {
  id: string,
  name: string,
  [propName: string]: any;
}[]) {
  state.user = {
    materialsAuditorId: users[0]?.id,
    materialsAuditorName: users[0]?.name,
  };
  formMethods.validateFields(['materialsAuditorName']);
}

// 保存按钮回调
function saveChange(fileList: {
  result: any[],
  [propName: string]: any
}[]) {
  state.fileDTOList = state.fileDTOList.concat(fileList.map((item) => item.result));
}

// 移除文件
function removeFile(index: number) {
  let fileId = state.fileDTOList[index]?.id;
  if (fileId) {
    state.deleteFileIdList.push(fileId);
  }
  state.fileDTOList.splice(index, 1);
}

defineExpose({
  formMethods,
  state,
});

</script>

<style scoped lang="less">
.flex-fs {
  align-items: flex-start;
}

.select-flex {
  width: 100%;
  display: flex;
  margin-top: ~`getPrefixVar('content-margin')`;

  > :nth-child(1) {
    width: 200px;
    flex-shrink: 0;
  }

  > :nth-child(2) {
    flex-grow: 1;
    width: 0;
    margin: 0 0 0 ~`getPrefixVar('content-margin')`;
  }

  > :nth-child(3) {
    width: 16px;
    flex-shrink: 0;
    margin-left: ~`getPrefixVar('content-margin')`;
  }
}

.icon:hover {
  color: ~`getPrefixVar('primary-color')`;
  cursor: pointer;
}

.option-item {
  display: flex;
  align-items: flex-start;

  &-content {
    flex: 1;
  }

  &-desc {
    flex: 2;
    margin-left: 30px;
  }

  //&:hover &-desc{
  //  overflow: auto;
  //  white-space: pre-wrap;
  //}
}

.file-list {
  display: flex;
  flex-direction: column;

  .file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 30px;
    line-height: 30px;
    margin-top: 10px;

    .remove {
      display: none;
      cursor: pointer;
    }

    &:hover .remove {
      display: inline-block;
    }

    .file-name {
      width: 0;
      flex-grow: 1;
      margin-right: ~`getPrefixVar('content-margin')`;
    }
  }
}
</style>
