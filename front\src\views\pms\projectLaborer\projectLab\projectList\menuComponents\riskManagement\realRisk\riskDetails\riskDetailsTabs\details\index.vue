<template>
  <div class="productLibraryDetails5920 layoutPage5920">
    <div
      class="productLibraryDetails_content5920 layoutPage_content5920"
      :style="{ height: contentHeight + 130 + 'px' }"
    >
      <div class="productLibraryDetails_left">
        <basicTitle :title="'预览'">
          <pdmImage
            :img-url="pictureBase + formState.projectImage"
            :show-delete="formType == 'edit'"
            @deleteImgUrl="deleteImgUrl"
          />
        </basicTitle>
      </div>

      <div class="productLibraryDetails_right">
        <basicTitle :title="'基本信息'">
          <div class="productLibraryDetails_right_content">
            <a-form
              ref="formRef"
              :model="formState"
              class="pdmFormClass"
              :rules="formType == 'details' ? {} : rules"
              :label-col="{ span: 4 }"
              :wrapper-col="{ span: 20 }"
              label-align="left"
            >
              <a-form-item
                label="编号"
                name="code"
                :style="{ height: '15px' }"
              >
                <span>{{ formState.number }}</span>
              </a-form-item>
              <a-form-item
                label="名称"
                name="name"
                :style="{ height: '15px' }"
              >
                <a-input
                  v-if="formType == 'edit'"
                  v-model:value="newformdata.name"
                  style="width: 330px"
                  placeholder="请输入名称"
                />
                <span v-else>{{ formState.name }}</span>
              </a-form-item>
              <a-form-item
                label="风险类型"
                name="riskType"
                :style="{ height: '15px' }"
              >
                <a-select
                  v-if="formType == 'edit'"
                  v-model:value="newformdata.riskType"
                  placeholder="请选择风险类型"
                  style="width: 330px"
                >
                  <a-select-option
                    v-for="(item, index) in riskType"
                    :key="index"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
                <span v-else>{{ formState.riskTypeName }}</span>
              </a-form-item>
              <a-form-item
                label="状态"
                name="statusName"
                :style="{ height: '15px' }"
              >
                <span>{{ formState.statusName }}</span>
              </a-form-item>
              <a-form-item
                label="发生概率"
                name="riskProbability"
                :style="{ height: '15px' }"
              >
                <a-select
                  v-if="formType == 'edit'"
                  v-model:value="newformdata.riskProbability"
                  placeholder="请选择发生概率"
                  style="width: 330px"
                >
                  <a-select-option
                    v-for="(item, index) in riskProbability"
                    :key="index"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
                <span v-else>{{ formState.riskProbabilityName }}</span>
              </a-form-item>
              <a-form-item
                label="影响程度"
                name="riskInfluence"
                :style="{ height: '15px' }"
              >
                <a-select
                  v-if="formType == 'edit'"
                  v-model:value="newformdata.riskInfluence"
                  placeholder="请选择影响程度"
                  style="width: 330px"
                >
                  <a-select-option
                    v-for="(item, index) in riskInfluence"
                    :key="index"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
                <span v-else>{{ formState.riskInfluenceName }}</span>
              </a-form-item>
              <!--name solutions copingStrategy principalId remark predictEndTime predictStartTime discernPerson riskInfluence riskProbability riskType-->

              <a-form-item
                label="识别人"
                name="discernPerson"
                :style="{ height: '15px' }"
              >
                <a-select
                  v-if="formType == 'edit'"
                  v-model:value="newformdata.discernPerson"
                  :filter-option="filterHandle"
                  placeholder="请选择识别人"
                  :options="discernPerson"
                  style="width: 330px"
                  show-search
                  @search="handleChange2"
                />
                <span v-else>{{ formState.discernPersonName }}</span>
              </a-form-item>
              <a-form-item
                label="预估发生时间"
                name="predictStartTime"
                :style="{ height: '15px' }"
              >
                <a-select
                  v-if="formType == 'edit'"
                  v-model:value="newformdata.predictStartTime"
                  placeholder="请选择预估发生时间"
                  style="width: 330px"
                >
                  <a-select-option
                    v-for="(item, index) in predictStartTime"
                    :key="index"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
                <span v-else>{{ formState.predictStartTimeName }}</span>
              </a-form-item>
              <!-- YYYY-MM-DD HH:mm:ss -->
              <a-form-item
                label="期望完成时间"
                name="predictEndTime"
                :style="{ height: '15px' }"
              >
                <a-date-picker
                  v-if="formType == 'edit'"
                  v-model:value="newformdata.predictEndTime"
                  style="width: 330px"
                  placeholder="年 / 月 / 日"
                />

                <span v-else>{{
                  formState.predictEndTime
                    ? dayjs(formState.predictEndTime).format('YYYY-MM-DD')
                    : ''
                }}</span>
              </a-form-item>
              <a-form-item label="风险描述">
                <aTextarea
                  v-if="formType == 'edit'"
                  v-model:value="newformdata.remark"
                  style="width: 330px"
                  placeholder="请输入风险描述"
                  show-count
                  :maxlength="255"
                  :rows="4"
                />
                <span
                  v-else
                  class="descriptionStyle"
                >{{ formState.remark }}</span>
              </a-form-item>
              <a-form-item
                label="负责人"
                name="principalId"
                :style="{ height: '15px' }"
              >
                <a-select
                  v-if="formType == 'edit'"
                  v-model:value="newformdata.principalId"
                  :filter-option="filterHandle"
                  placeholder="请输入负责人"
                  :options="roleOption"
                  style="width: 330px"
                  show-search
                  @search="handleChange"
                />
                <span v-else>{{ formState.principalName }}</span>
              </a-form-item>
              <a-form-item
                label="应对策略"
                name="copingStrategy"
                :style="{ height: '15px' }"
              >
                <a-select
                  v-if="formType == 'edit'"
                  v-model:value="newformdata.copingStrategy"
                  placeholder="请选择应对策略"
                  style="width: 330px"
                >
                  <a-select-option
                    v-for="(item, index) in copingStrategy"
                    :key="index"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
                <span v-else>{{ formState.copingStrategyName }}</span>
              </a-form-item>

              <a-form-item label="应对措施">
                <aTextarea
                  v-if="formType == 'edit'"
                  v-model:value="newformdata.solutions"
                  style="width: 330px"
                  placeholder="请输入应对措施"
                  show-count
                  :maxlength="255"
                  :rows="4"
                />
                <span
                  v-else
                  class="descriptionStyle"
                >{{ formState.solutions }}</span>
              </a-form-item>

              <!-- <a-form-item label="预览图" v-if="formType == 'edit'">
              <upload @successChange="successChange" />
            </a-form-item> -->
              <a-form-item
                label="修改人"
                :style="{ height: '15px' }"
              >
                <span>{{ formState.modifyName }}</span>
              </a-form-item>
              <a-form-item
                label="修改时间"
                :style="{ height: '15px' }"
              >
                <span>{{
                  formState.modifyTime
                    ? dayjs(formState.modifyTime).format('YYYY-MM-DD HH:mm:ss')
                    : ''
                }}</span>
              </a-form-item>
              <a-form-item
                label="创建人"
                :style="{ height: '15px' }"
              >
                <span>{{ formState.creatorName }}</span>
              </a-form-item>
              <a-form-item
                label="创建时间"
                :style="{ height: '15px' }"
              >
                <span>{{
                  formState.createTime
                    ? dayjs(formState.createTime).format('YYYY-MM-DD HH:mm:ss')
                    : ''
                }}</span>
              </a-form-item>
            </a-form>
          </div>
        </basicTitle>
      </div>
      <newButtonModal
        :btn-object-data="btnObjectData"
        @clickType="clickType"
      />
    </div>

    <!-- <checkDetails :data="dataRow" /> -->
  </div>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, ref, onMounted, nextTick, inject, computed,
} from 'vue';
import {
  Form, Select, Input, message, Table, DatePicker,
} from 'ant-design-vue';
import basicTitle from '/@/views/pms/projectLaborer/componentsList/basicTitle/index.vue';
import { pictureBase } from '/@/views/pms/projectLaborer/api/picture';
import {
  isPower,
} from 'lyra-component-vue3';
import {
  riskDetailsApi,
  probRiskListApi,
  EffectRiskListApi,
  roleListApi,
  startEffectRiskListApi,
  soluteRiskListApi,
  editRiskApi,
  RistTypeApi,
} from '/@/views/pms/projectLaborer/api/riskManege';

import dayjs from 'dayjs';
import upload from '/@/views/pms/projectLaborer/componentsList/upload/index.vue';
import pdmImage from '/@/views/pms/projectLaborer/componentsList/image/index.vue';
import newButtonModal from '/@/views/pms/projectLaborer/componentsList/buttonModal/newButtonModal.vue';
export default defineComponent({
  name: 'ProjectLabdetail',
  components: {
    aForm: Form,
    aFormItem: Form.Item,
    aSelect: Select,
    aTable: Table,
    aInput: Input,
    aTextarea: Input.TextArea,
    pdmImage,
    basicTitle,
    //   checkDetails,
    upload,
    newButtonModal,
    RangePicker: DatePicker.RangePicker,
    ASelectOption: Select.Option,
    ADatePicker: DatePicker,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    //   const route = useRoute();
    //   const layoutModelStore = layoutModel();
    const formRef = ref();
    const state = reactive({
      formType: 'details',
      formState: <any>{},
      /* 编辑表单 */
      newformdata: <any>{
        name: '',
        solutions: '',
        copingStrategy: '',
        principalId: '',
        remark: '',
        predictEndTime: undefined,
        predictStartTime: undefined,
        discernPerson: '',
        riskInfluence: '',
        riskProbability: '',
        riskType: '',
        /* 识别人和负责人 */
        discernPersonName: '',
        principalName: '',
        id: '',
        projectId: '',
      },
      treeData: [],
      parentId: 1,
      oldFormState: <any>{},
      message: '',
      showVisible: false,
      btnType: '',
      dataRow: {},

      contentHeight: 500,

      // 负责人
      roleOption: <any>[],
      // 识别人
      discernPerson: <any>[],
      // 风险类型
      riskType: <any>[],
      // 发生概率
      riskProbability: [],
      // 影响程度
      riskInfluence: [],
      // 预估发生时间
      predictStartTime: [],
      // 应对策略
      copingStrategy: [],
      powerData: [],

    });
    state.powerData = inject('powerData');
    const state6 = reactive({
      btnObjectData: {
        edit: { show: computed(() => isPower('FX_container_button_02', state.powerData)) },
        determine: { show: false },
        cancel: { show: false },
      },
    });
    let projectId: any = inject('projectId');
    const rules = {
      // name: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
      // ownerName: [{ required: true, message: '请输入项目经理', trigger: 'blur' }],
      // number: [{ required: true, message: '请输入项目编号', trigger: 'blur' }]
      // startTime: [{ required: true, message: '请选择立项时间', trigger: 'blur' }],
      // endTime: [{ required: true, message: '请选择开始时间', trigger: 'blur' }],
      // projectEndTime: [{ required: true, message: '请选择结束时间', trigger: 'blur' }]
    };
    const clickType = (type) => {
      switch (type) {
        case 'edit':
          editFormData();
          break;
        case 'determine':
          okRow();
          break;
        case 'cancel':
          cancel();
          break;
      }
    };
    const editFormData = async () => {
      // console.log('点击了编辑');
      try {
        // 风险类型
        state.riskType = await RistTypeApi();
        //   风险发生概率
        state.riskProbability = await probRiskListApi();
        //   风险影响程度
        state.riskInfluence = await EffectRiskListApi();
        //   风险影响程度
        state.predictStartTime = await startEffectRiskListApi();
        //   风险应对策略
        state.copingStrategy = await soluteRiskListApi();
      } catch (err) {
      }

      /* <!--name solutions copingStrategy principalId remark predictEndTime predictStartTime discernPerson riskInfluence riskProbability riskType--> */
      for (let namex in state.newformdata) {
        if (namex === 'predictEndTime') {
          state.newformdata[namex] = state.formState[namex] ? dayjs(state.formState[namex]) : undefined;
        } else {
          state.newformdata[namex] = state.formState[namex];
        }
      }
      state.newformdata.principalId = state.newformdata.principalName;
      state.newformdata.discernPerson = state.newformdata.discernPersonName;
      /* ---------- */
      state.formType = 'edit';
      state6.btnObjectData = {
        edit: { show: false },
        determine: { show: true },
        cancel: { show: true },
      };
    };
    const okRow = () => {
      formRef.value
        .validate()
        .then(() => {
          if (!state.newformdata.predictEndTime) {
            state.newformdata.predictEndTime = undefined;
          } else {
            state.newformdata.predictEndTime = state.newformdata.predictEndTime
              ? state.newformdata.predictEndTime
              : state.formState.predictEndTime;
          }
          /* 如果值不变,则赋值回旧值 */
          if (state.newformdata.principalId == state.formState.principalName) {
            state.newformdata.principalId = state.formState.principalId;
          }
          if (state.newformdata.discernPerson == state.formState.discernPersonName) {
            state.newformdata.discernPerson = state.formState.discernPerson;
          }
          delete state.newformdata.discernPersonName;
          delete state.newformdata.principalName;
          editRiskApi(state.newformdata)
            .then(async () => {
              message.success('保存成功');
              state6.btnObjectData = {
                edit: { show: computed(() => isPower('FX_container_button_02', state.powerData)) },
                determine: { show: false },
                cancel: { show: false },
              };
              state.formType = 'details';
              state.newformdata = {
                name: '',
                solutions: '',
                copingStrategy: '',
                principalId: '',
                remark: '',
                predictEndTime: undefined,
                predictStartTime: undefined,
                discernPerson: '',
                riskInfluence: '',
                riskProbability: '',
                riskType: '',
                /* 识别人和负责人 */
                discernPersonName: '',
                principalName: '',
                id: '',
                projectId: '',
              };
              await getDetail();
            })
            .catch(() => {});
        })
        .catch((error) => {
          console.log('error', error);
        });
    };

    const cancel = () => {
      state.formType = 'details';
      state.formState.projectImage = '';
      state6.btnObjectData = {
        edit: { show: computed(() => isPower('FX_container_button_02', state.powerData)) },
        determine: { show: false },
        cancel: { show: false },
      };
      getDetail();
    };

    onMounted(async () => {
      state.contentHeight = document.body.clientHeight - 365;
      await getDetail();
    });
    /* 获取详情 */
    const getDetail = () => {
      riskDetailsApi(props.id)
        .then((res) => {
          if (res) {
            console.log('测试🚀 ~ file: index.vue ~ line 55 ~ res', res);
            state.formState = { ...res };
            //   state.oldFormState = JSON.parse(JSON.stringify(res));
          }
        })
        .catch(() => {});
    };
      /* 图片上传回调 */
      //   const successChange = (data) => {
      //     state.formState.projectImage = data.imageId;
      //     state.newformdata.projectImage = data.imageId;
      //   };
    const deleteImgUrl = () => {
      state.formState.projectImage = '';
      state.newformdata.projectImage = '';
    };
    const handleChange = (value) => {
      state.roleOption = [];
      try {
        getRole(value, projectId.value, 'role');
      } catch (err) {
        console.log('测试🚀 ~ file: addProjectModal.vue ~ line 336 ~ err', err);
      }
    };
    const handleChange2 = (value) => {
      state.discernPerson = [];

      try {
        getRole(value, projectId.value, 'role2');
        console.log('测试🚀🚀 ~~~ props.id', props.id);
      } catch (err) {
        console.log('测试🚀 ~ file: addProjectModal.vue ~ line 336 ~ err', err);
      }
    };
    const getRole = async (value, idkey, typeString) => {
      const newvalue = {
        name: value,
      };
      await roleListApi(newvalue, idkey).then((res) => {
        nextTick(() => {
          const qq = res.map((item) => ({
            value: item.id,
            label: item.name,
          }));
          if (typeString === 'role') {
            state.roleOption = qq;
          } else {
            state.discernPerson = qq;
          }
        });
      });
    };
    const filterHandle = (inputValue, option) => option;
    return {
      ...toRefs(state),
      ...toRefs(state6),
      formRef,
      rules,
      cancel,
      okRow,
      editFormData,
      // successChange,
      deleteImgUrl,
      clickType,
      dayjs,
      handleChange,
      handleChange2,
      filterHandle,
      pictureBase,
    };
  },
});
</script>
<style lang="less" scoped>
  /* 去掉冒号 */
  /deep/ .ant-form {
    .ant-form-item {
      .ant-col {
        label {
          &::after {
            content: '';
          }
        }
      }
    }
  }
  .ant-form-item-label {
    label {
      background: red !important;
    }
  }
  @import url('./style/projectLabdetail.less');
</style>
