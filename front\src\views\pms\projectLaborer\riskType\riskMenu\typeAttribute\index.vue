<template>
  <Layout2>
    <OrionTable
      ref="tableRef"
      :options="options"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="isPower('LXSZ_container_button_38',powerData)"
          type="primary"
          icon="sie-icon-tianjiaxinzeng"
          @click="btnClick('add');"
        >
          新增
        </BasicButton>
        <BasicButton
          v-if="isPower('LXSZ_container_button_39',powerData)"
          icon="sie-icon-shanchu"
          @click="btnClick('delete');"
        >
          删除
        </BasicButton>
      </template>

      <template #action="{record}">
        <BasicTableAction :actions="actionsBtn(record)" />
      </template>
    </OrionTable>
    <AEDrawer
      :selectChangeData="selectChangeData"
      @register="AEDrawer"
      @add-success="addSuccess"
    />
  </Layout2>
</template>

<script lang="ts">
import {
  computed,
  defineComponent, inject, onMounted, reactive, toRefs, watch,
} from 'vue';
import {
  Layout, OrionTable, Layout2, DataStatusTag, useDrawer, BasicButton, BasicTableAction, ITableActionItem, isPower,
} from 'lyra-component-vue3';
import { message, Modal } from 'ant-design-vue';
import dayjs from 'dayjs';
import AEDrawer from './AEDrawer.vue';
import Api from '/@/api';

// import { PlusOutlined } from '@ant-design/icons-vue';
export default defineComponent({
  name: 'Index',
  components: {
    BasicTableAction,
    BasicButton,
    Layout2,
    OrionTable,
    AEDrawer,
  },
  props: {
    selectChangeData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  emits: [],
  setup(props) {
    const [AEDrawer, AEDM] = useDrawer();
    const powerData: any = inject('powerData', {});
    const state = reactive({
      tableRef: null,
      options: {
        deleteToolButton: 'add|delete|enable|disable',
        rowSelection: {},
        showSmallSearch: false,
        pagination: false,
        isFilter2: false,
        filterConfigName: 'PAS_BECURRENTMANAGE_TYPESETTINGS_RISKTYPE_ATTRIBUTE',
        smallSearchField: ['name'],
        api: () => new Api(`/pms/projectPlan-type-to-risk-type-attribute/list/${props.selectChangeData.id}`).fetch('', '', 'GET'),
        columns: [
          {
            title: '名称',
            dataIndex: 'name',
            ellipsis: true,
          },
          {
            title: '编码',
            dataIndex: 'number',
          },
          {
            title: '类型',
            dataIndex: 'type',
            customRender({ record }) {
              return record?.type === 1 ? '输入项' : record?.type === 2 ? '单选项' : '复选项';
            },
          },
          {
            title: '选项值',
            dataIndex: 'options',
            // customRender({ record }) {
            //   return record.options?.length > 0 ? record.options.map((item) => item.name).join(';') : '无';
            // }
            customRender({ record }) {
              return record?.options ? record?.options : '无';
            },
          },
          {
            title: '是否必填',
            dataIndex: 'require',
            customRender({ record }) {
              return record?.require === 1 ? '是' : '否';
            },
          },
          {
            title: '所有者',
            dataIndex: 'ownerName',
          },
          {
            title: '修改日期',
            dataIndex: 'modifyTime',
            customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : ''),
          },
          {
            title: '操作',
            dataIndex: 'action',
            width: 150,
            align: 'left',
            fixed: 'right',
            slots: { customRender: 'action' },
          },
        ],
      },
    });
    const state6 = reactive({
      btnList: [
        { type: 'add' },
        {
          type: 'edit',
          powerCode: 'LXSZ_container_button_40',
        },
        {
          type: 'delete',
          powerCode: 'LXSZ_container_button_41',
        },
        // { type: 'search' },
      ],
    });
    watch(() => props.selectChangeData, () => {
      state.tableRef.reload();
    });
    // async function getTabel(){
    //   // console.log("----- state.tableRef -----", state.tableRef)
    //   state.tableRef.setLoading(true)
    //     state.options.dataSource= await new Api(`/pms/projectPlan-type-to-risk-type-attribute/list/${props.selectChangeData.id}`).fetch('','','GET')
    //   state.tableRef.setLoading(false)
    // }
    // onMounted(()=>{
    //   getTabel()
    // })
    function btnClick(type) {
      switch (type) {
        case 'edit':
          if (oddJudge()) {
            AEDM.setDrawerProps({
              title: '编辑分类属性',
            });
            AEDM.openDrawer(true, ['edit', { editData: oddJudge()[0] }]);
          }
          break;
        case 'add':
          AEDM.setDrawerProps({
            title: '新增分类属性',
          });
          AEDM.openDrawer(true, ['add']);
          break;
        case 'delete':
          if (evenJudge()) {
            deleteFn();
          }
          break;
        case 'search':
          break;
      }
    }
    async function deleteFn() {
      let rowList = evenJudge();
      let isDelete = rowList.every((item) => item.currentType);
      if (!isDelete) {
        message.warning('不可删除父级创建的类型属性');
        return;
      }
      Modal.confirm({
        title: '删除提示',
        content: '您确认要删除这些数据吗？',
        async onOk() {
          return await new Api('/pms/projectPlan-type-to-risk-type-attribute/').fetch(evenJudge().map((item) => item.relationId), '', 'DELETE').then(() => {
            message.success('删除成功');
            state.tableRef.reload();
          });
        },
      });
    }
    function oddJudge() {
      const selectColumns:any = state.tableRef && state.tableRef.selectColumns.rows;
      if (selectColumns?.length > 1) {
        message.info('只能选择一条数据进行操作');
        return false;
      }
      if (selectColumns?.length === 0) {
        message.info('请择一条数据进行操作');
        return false;
      }
      return selectColumns;
    }
    function evenJudge() {
      const selectColumns:any = state.tableRef && state.tableRef.selectColumns.rows;
      if (selectColumns?.length === 0) {
        message.info('请至少选择一条数据进行操作');
        return false;
      }
      return selectColumns;
    }
    function addSuccess() {
      state.tableRef.reload();
    }

    const actionsBtn = (record) => {
      const actions:ITableActionItem[] = [
        {
          text: '编辑',

          isShow: computed(() => state6.btnList.some((item) => item.type === 'edit')),

          onClick() {
            AEDM.openDrawer(true, ['edit', { editData: { ...record } }]);
          },
        },
        {
          text: '删除',

          isShow: computed(() => state6.btnList.some((item) => item.type === 'delete')),

          modal() {
            return new Api('/pms/projectPlan-type-to-risk-type-attribute/').fetch([record.relationId], '', 'DELETE').then(() => {
              message.success('删除成功');
              state.tableRef.reload();
            });
          },
        },
      ];
      return actions;
    };

    return {
      ...toRefs(state),
      ...toRefs(state6),
      btnClick,
      AEDrawer,
      addSuccess,
      actionsBtn,
      powerData,
      isPower,
    };
  },
});
</script>

<style scoped lang="less"></style>
