package com.chinasie.orion.service.projectStatistics;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.projectStatistics.GoodsStatusStatisticsDTO;
import com.chinasie.orion.domain.entity.projectStatistics.GoodsStatusStatistics;
import com.chinasie.orion.domain.vo.projectStatistics.GoodsStatusStatisticsVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

/**
 * <p>
 * GoodsStatusStatistics 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-21 14:07:54
 */
public interface GoodsStatusStatisticsService  extends OrionBaseService<GoodsStatusStatistics>{
    /**
     *  详情
     *
     * * @param id
     */
    GoodsStatusStatisticsVO detail(String id)  throws Exception;

    /**
     *  新增
     *
     * * @param goodsStatusStatisticsDTO
     */
    GoodsStatusStatisticsVO create(GoodsStatusStatisticsDTO goodsStatusStatisticsDTO)  throws Exception;

    /**
     *  编辑
     *
     * * @param goodsStatusStatisticsDTO
     */
    Boolean edit(GoodsStatusStatisticsDTO goodsStatusStatisticsDTO) throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     */
    Page<GoodsStatusStatisticsVO> pages(Page<GoodsStatusStatisticsDTO> pageRequest) throws Exception;

}
