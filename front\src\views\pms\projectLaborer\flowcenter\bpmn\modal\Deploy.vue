<template>
  <BasicModal
    v-bind="$attrs"
    title="驳回节点选择"
    @register="registerModal"
  >
    <div>
      <div>
        <a-checkbox
          v-model:checked="checkAll"
          :indeterminate="indeterminate"
          @change="onCheckAllChange"
        >
          全选
        </a-checkbox>
      </div>
      <br>
      <a-checkbox-group
        v-model:value="checkedList"
        :options="plainOptions"
      />
    </div>
    <template #footer>
      <a-button @click="closeModal">
        取消
      </a-button>
      <a-button
        type="primary"
        @click="onConfirm"
      >
        确定
      </a-button>
    </template>
  </BasicModal>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, watch,
} from 'vue';
import {
  BasicTable, Layout, useActionsRecord, OrionTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer,
} from 'lyra-component-vue3';
// import { BasicModal, useModalInner } from '/@/components/Modal';
import { Descriptions, Tabs, Checkbox } from 'ant-design-vue';
import { _joinStr } from '../../util/util';
export default defineComponent({
  components: {
    BasicModal,
    aDescriptions: Descriptions,
    aTabs: Tabs,
    [Checkbox.Group.name]: Checkbox.Group,
  },
  setup(_, { emit }) {
    // 弹窗内部的注册函数,可以在内部自己关闭
    const [registerModal, { closeModal }] = useModalInner((data) => {
      let { flowList, selectIds } = data;
      state.checkedList = selectIds ? selectIds.split(',') : _checkFirst(flowList);
      let exchangeList = flowList.map((item) => ({
        label: item._name,
        value: item._id,
      }));
      state.plainOptions = exchangeList;
    });

    const state = reactive({
      indeterminate: true,
      checkAll: false,
      checkedList: [],
      plainOptions: [],
    });

    // 如果第一个节点是do节点，默认选中
    const _checkFirst = (list) => {
      if (list.length > 0) {
        if (list[0]['_activiti:assignee']) {
          return [list[0]._id];
        }
      }
      return [];
    };

    const onCheckAllChange = (e: any) => {
      let all: any[] = state.plainOptions.map((item: any) => item.value);
      Object.assign(state, {
        checkedList: e.target.checked ? all : [],
        indeterminate: false,
      });
    };
    watch(
      () => state.checkedList,
      (val) => {
        state.indeterminate = !!val.length && val.length < state.plainOptions.length;
        state.checkAll = val.length === state.plainOptions.length;
      },
    );

    function onConfirm() {
      const joinStr = _joinStr(state.checkedList);
      emit('select', joinStr);
      closeModal();
    }

    return {
      registerModal,
      closeModal,
      ...toRefs(state),
      onCheckAllChange,
      onConfirm,
    };
  },
});
</script>
