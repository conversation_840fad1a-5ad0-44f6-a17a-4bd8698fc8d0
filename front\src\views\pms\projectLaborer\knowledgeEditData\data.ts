// 定义数据
export const index = {
  form: {
    classifyId: [],
    endTime: null,
    keyword: null,
    kidList: [],
    organizationIdList: [],
    pageNum: 1,
    pageSize: 10,
    secretLevelIdList: [],
    startTime: null,
  },
  formAdd: {
    attributeDtoList: [],
    attributeParamDtoList: [],
    attributeDtoListId: [],
    author: '',
    classifyId: '',
    content: '',
    createTime: '',
    creatorId: '',
    dept: '',
    deptName: '',
    downloadPermission: {
      detailList: [],
      isPublic: false,
    },
    editPermission: {
      detailList: [],
      isPublic: false,
    },
    id: '',
    isDraft: 0,
    knowledgeIdList: [],
    labelDtoList: [],
    modelId: '',
    modifyId: '',
    modifyTime: '',
    name: '',
    number: '',
    permissionJsonDtoList: [
      {
        type: 1,
        isPublic: false,
        projectList: [],
        organizationList: [],
        personList: [],
      },
      {
        type: 2,
        isPublic: false,
        projectList: [],
        organizationList: [],
        personList: [],
      },
      {
        type: 3,
        isPublic: false,
        projectList: [],
        organizationList: [],
        personList: [],
      },
    ],
    readPermission: {
      detailList: [],
      isPublic: false,
    },
    revision: 'V1.0.0',
    secretLevel: null,
    securityLimit: null,
    summary: '',
    threadId: '',
    thumb: '',
  },
  pagination: {
    content: [],
    pageNum: 1,
    pageSize: 10,
    totalPages: 0,
    totalSize: 0,
  },
  columns: [
    {
      title: '知识名称',
      dataIndex: 'name',
      align: 'left',
      slots: { customRender: 'name' },
      width: 350,
    },
    {
      title: '知识分类名称',
      dataIndex: 'classifyName',
      align: 'left',
      slots: { customRender: 'classifyName' },
    },
    {
      title: '知识密级',
      dataIndex: 'secretLevelName',
      align: 'center',
      slots: { customRender: 'secretLevelName' },
    },
    {
      title: '浏览量',
      dataIndex: 'readCount',
      align: 'center',
      slots: { customRender: 'readCount' },
      width: 100,
    },
    {
      title: '评分',
      dataIndex: 'score',
      align: 'center',
      slots: { customRender: 'score' },
      width: 100,
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
      align: 'center',
      slots: { customRender: 'creatorName' },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      align: 'center',
      slots: { customRender: 'createTime' },
      width: 160,
    },
    {
      title: '作者',
      dataIndex: 'author',
      align: 'center',
      slots: { customRender: 'author' },
    },
    {
      title: '状态',
      dataIndex: 'takeEffect',
      align: 'center',
      slots: { customRender: 'takeEffect' },
    },
    {
      title: '操作',
      width: 160,
      dataIndex: 'action',
      align: 'center',
      slots: { customRender: 'action' },
      fixed: 'right',
    },
  ],
  columns1: [
    {
      title: '附件名称',
      dataIndex: 'fileName',
      align: 'left',
      slots: { customRender: 'fileName' },
      width: 350,
    },
    {
      title: '所属知识名称',
      dataIndex: 'knowledgeName',
      align: 'left',
    },
    {
      title: '知识密级',
      dataIndex: 'secretLevelName',
      align: 'center',
      slots: { customRender: 'secretLevelName' },
    },
    {
      title: '附件类型',
      dataIndex: 'typeName',
      align: 'center',
    },
    {
      title: '附件大小',
      dataIndex: 'fileSizeName',
      align: 'center',
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
      align: 'center',
      slots: { customRender: 'creatorName' },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      align: 'center',
      slots: { customRender: 'createTime' },
    },
    {
      title: '权限状态',
      dataIndex: 'statusName',
      align: 'center',
      slots: { customRender: 'statusName' },
    },
    {
      title: '操作',
      width: 160,
      dataIndex: 'action',
      align: 'center',
      slots: { customRender: 'action' },
      fixed: 'right',
    },
  ],
};
// export const fileType = {
//   '.docx': {
//     color: '#0e6cb7',
//     icon: 'fa-file-word-o'
//   },
//   '.doc': {
//     color: '#0e6cb7',
//     icon: 'fa-file-word-o'
//   },
//   '.pdf': {
//     color: '#ff5722',
//     icon: 'fa-file-pdf-o'
//   },
//   '.zip': {
//     color: '#673ab7',
//     icon: 'fa-file-zip-o'
//   },
//   '.xlsx': {
//     color: '#009688',
//     icon: 'fa-file-excel-o'
//   },
//   '.xls': {
//     color: '#009688',
//     icon: 'fa-file-excel-o'
//   },
//   '.video': {
//     color: '#00bcd4',
//     icon: 'fa-file-video-o'
//   },
//   '.txt': {
//     color: '#607d8b',
//     icon: 'fa-file-text-o'
//   },
//   '.jpg': {
//     color: '#607d8b',
//     icon: 'fa-file-text-o'
//   },
//   '.png': {
//     color: '#607d8b',
//     icon: 'fa-file-text-o'
//   }
// };
export const getFileObj = function (type) {
  switch (type) {
    case type === '.docx' || type === '.doc':
      return {
        color: '#0e6cb7',
        icon: 'fa-file-word-o',
      };
    case type === '.pdf':
      return {
        color: '#ff5722',
        icon: 'fa-file-pdf-o',
      };
    case type === '.zip':
      return {
        color: '#673ab7',
        icon: 'fa-file-zip-o',
      };
    case type === '.xlsx' || type === '.xls':
      return {
        color: '#009688',
        icon: 'fa-file-excel-o',
      };
    case type === '.video':
      return {
        color: '#00bcd4',
        icon: 'fa-file-video-o',
      };
    default:
      return {
        color: '#607d8b',
        icon: 'fa-file-text-o',
      };
  }
};
export const details = {
  columns: [
    {
      title: '序号',
      dataIndex: 'key',
      key: 'key',
      align: 'center',
      width: 80,
    },
    {
      title: '节点名称',
      dataIndex: 'taskName',
      key: 'taskName',
    },
    {
      title: '负责人',
      dataIndex: 'assigneeName',
      key: 'assigneeName',
      align: 'center',
      width: 220,
    },
    {
      title: '操作',
      dataIndex: 'taskStatus',
      key: 'taskStatus',
      align: 'center',
      width: 180,
    },
  ],
  columns1: [
    {
      title: '权限类型',
      dataIndex: 'type',
      slots: { customRender: 'type' },
      align: 'center',
      width: 100,
    },
    {
      title: '项目',
      dataIndex: 'projectList',
      slots: { customRender: 'projectList' },
      align: 'center',
    },
    {
      title: '组织',
      dataIndex: 'organizationList',
      slots: { customRender: 'organizationList' },
      align: 'center',
    },
    {
      title: '员工',
      dataIndex: 'personList',
      slots: { customRender: 'personList' },
      align: 'center',
    },
    {
      title: '选择',
      dataIndex: 'isPublic',
      slots: { customRender: 'isPublic' },
      align: 'center',
      width: 200,
    },
  ],
  columns2: [
    {
      title: '序号',
      dataIndex: 'key',
      key: 'key',
      width: 80,
      align: 'center',
    },
    {
      title: '文档名称',
      dataIndex: 'name1',
      key: 'name1',
      align: 'center',
    },
    {
      title: '操作方式',
      dataIndex: 'name2',
      key: 'name1',
      align: 'center',
    },
    {
      title: '操作人',
      dataIndex: 'name3',
      key: 'name3',
      align: 'center',
    },
    {
      title: '部门',
      dataIndex: 'name4',
      key: 'name4',
      align: 'center',
    },
    {
      title: '操作时间',
      dataIndex: 'name5',
      key: 'name5',
      align: 'center',
    },
  ],
  dataSource2: [
    {
      key: '1',
      name1: '知识文档',
      name2: '阅览',
      name3: '张伟',
      name4: '十一室',
      name5: '2021-05-12 10:11:12',
    },
    {
      key: '2',
      name1: '知识文档',
      name2: '阅览',
      name3: '张伟',
      name4: '十一室',
      name5: '2021-05-12 10:11:12',
    },
    {
      key: '3',
      name1: '知识文档',
      name2: '阅览',
      name3: '张伟',
      name4: '十一室',
      name5: '2021-05-12 10:11:12',
    },
  ],
};
export const edit = {
  columns: [
    {
      title: '流程编号',
      dataIndex: 'businessKey',
      align: 'center',
      slots: { customRender: 'businessKey' },
    },
    {
      title: '流程名称',
      dataIndex: 'procInstName',
      align: 'center',
      slots: { customRender: 'procInstName' },
    },
    {
      title: '流程类型',
      align: 'center',
      dataIndex: 'procDefName',
    },
    {
      title: '流程状态',
      align: 'center',
      dataIndex: 'statusName',
    },
    {
      title: '当前节点',
      align: 'center',
      dataIndex: 'currentTask',
    },
    {
      title: '发起日期',
      align: 'center',
      dataIndex: 'createTime',
    },
    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      slots: { customRender: 'action' },
    },
  ],
  columns1: [
    {
      title: '权限类型',
      dataIndex: 'type',
      slots: { customRender: 'type' },
      align: 'center',
      width: 100,
    },
    {
      title: '项目',
      dataIndex: 'projectList',
      slots: { customRender: 'projectList' },
      align: 'center',
    },
    {
      title: '组织',
      dataIndex: 'organizationList',
      slots: { customRender: 'organizationList' },
      align: 'center',
    },
    {
      title: '员工',
      dataIndex: 'personList',
      slots: { customRender: 'personList' },
      align: 'center',
    },
    {
      title: '选择',
      dataIndex: 'isPublic',
      slots: { customRender: 'isPublic' },
      align: 'center',
      width: 200,
    },
  ],
  columns2: [
    {
      title: '序号',
      dataIndex: 'key',
      key: 'key',
      align: 'center',
      width: 80,
    },
    {
      title: '节点名称',
      dataIndex: 'taskName',
      key: 'taskName',
    },
    {
      title: '负责人',
      dataIndex: 'assigneeName',
      key: 'assigneeName',
      align: 'center',
      width: 220,
    },
    {
      title: '操作',
      dataIndex: 'taskStatus',
      key: 'taskStatus',
      align: 'center',
      width: 180,
    },
  ],
};
export const bulkImport = {
  columns: [
    {
      title: '序号',
      dataIndex: 'number',
      slots: { customRender: 'number' },
      width: 80,
      align: 'center',
    },
    {
      title: '知识名称',
      dataIndex: 'name',
      slots: { customRender: 'name' },
    },
    {
      title: '文件大小',
      dataIndex: 'size',
      width: 100,
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 200,
      align: 'center',
      slots: { customRender: 'action' },
    },
  ],
};
export const uploadAttach = {
  columns: [
    {
      title: '知识名称',
      dataIndex: 'name',
      slots: { customRender: 'name' },
    },
    {
      title: '文件大小',
      dataIndex: 'fileSize',
      width: 100,
      align: 'center',
      slots: { customRender: 'fileSize' },
    },
  ],
};
export const aloneImport = {
  dataSource3: [
    {
      id: '1',
      name1: '中国三代核电技术已研发完成，从核电...',
      name2: '.xxx.pdf',
      name3: 'xxx.pdf',
      name4: '正常',
    },
    {
      id: '2',
      name1: '中国三代核电技术已研发完成，从核电...',
      name2: '.xxx.pdf',
      name3: 'xxx.pdf',
      name4: '正常',
    },
  ],
  columns1: [
    {
      title: '分类路径',
      dataIndex: 'classifyName',
      align: 'left',
      slots: { customRender: 'classifyName' },
    },
    {
      title: '知识名称',
      dataIndex: 'name',
      align: 'left',
      slots: { customRender: 'name' },
    },
    {
      title: '作者',
      dataIndex: 'author',
      align: 'left',
      width: 120,
      slots: { customRender: 'author' },
    },
    {
      title: '摘要',
      dataIndex: 'summary',
      align: 'left',
      slots: { customRender: 'summary' },
    },
    {
      title: '知识密级',
      dataIndex: 'secretLevelName',
      align: 'center',
      width: 120,
    },
    {
      title: '知识密级有效期限/年',
      dataIndex: 'securityLimitName',
      align: 'center',
      width: 180,
    },
    {
      title: '附件名',
      dataIndex: 'fileNames',
      align: 'left',
      slots: { customRender: 'fileNames' },
    },
    {
      title: '附件密级',
      dataIndex: 'fileSecretLevelNames',
      align: 'center',
      width: 120,
    },
    {
      title: '附件密级有效期限/年',
      dataIndex: 'fileSecurityLimitNames',
      align: 'center',
      width: 180,
    },
    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      width: 160,
      slots: { customRender: 'action' },
    },
  ],
  columns2: [
    {
      title: '附件名称',
      dataIndex: 'fileName',
      align: 'left',
    },
    {
      title: '格式',
      dataIndex: 'suffix',
      align: 'center',
    },
    {
      title: '附件大小',
      dataIndex: 'sizeName',
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      slots: { customRender: 'action' },
    },
  ],
  columns3: [
    {
      title: '知识名称',
      dataIndex: 'knowledgeName',
      align: 'left',
    },
    {
      title: '原始附件名',
      dataIndex: 'fileNames',
      align: 'center',
      slots: { customRender: 'fileNames' },
    },
    {
      title: '匹配附件',
      dataIndex: 'newFileNames',
      align: 'center',
    },
    {
      title: '状态',
      dataIndex: 'statusName',
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      slots: { customRender: 'action' },
    },
  ],
};
export const editKnowledge = {
  columns: [
    {
      title: '附件名称',
      dataIndex: 'fileName',
      align: 'center',
    },
    {
      title: '密级',
      dataIndex: 'fileSecretLevelNames',
      align: 'center',
    },
    {
      title: '有效期限/年',
      dataIndex: 'fileSecurityLimitNames',
      align: 'center',
    },
  ],
};
