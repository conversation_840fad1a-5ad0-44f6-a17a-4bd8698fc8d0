<template>
  <BpmnMain
    ref="bpmnMain"
    :deliveryId="deliveryId"
    :menu-instance-list-api="menuInstanceListApi"
    :template-list-api="templateListApi"
    :allTaskPageApi="allTaskPageApi"
    :addEditSavaApi="addEditSavaApi"
    :userId="userId"
    :journalApi="journalApi"
    :taskBtnApi="taskBtnApi"
    :nodeTableDataApi="nodeTableDataApi"
    :approvalListApi="approvalListApi"
    :approvalTableColumns="approvalTableColumns"
    :showBtn="false"
    :showFlowSaveButton="false"
    :userDataApi="userDataApi"
    :start2Api="start2Api"
    @success="successChange"
    @openClick="openClick"
  />
</template>

<script lang="ts">
import {
  defineComponent, onMounted, reactive, toRefs, computed, ref, inject, watch, unref,
} from 'vue';
import { BpmnMain } from 'lyra-component-vue3';
import Api from '/@/api';
import { useUserStore } from '/@/store/modules/user';
import dayjs, { Dayjs } from 'dayjs';
import { useRoute } from 'vue-router';
import { getQiankunProps } from '/@/utils/qiankun/useQiankun';

export default defineComponent({
  name: 'Information',
  components: {
    BpmnMain,
  },
  props: {
    selectedRows: {
      type: Array,
      default: () => [],
    },
    href: {
      type: String,
      default: '',
    },
    processName: {
      type: String,
      default: '',
    },
    bizCatalogName: {
      type: String,
      default: '',
    },
  },
  emits: ['changePage'],
  setup(props, { emit, attrs }) {
    const useWebSocket = getQiankunProps()?.mainStore?.useWebSocketStore();
    const bpmnMain = ref();
    const reLoadTable = inject('reLoadTable');
    const route = useRoute();
    const userStore = useUserStore();
    const state = reactive({
      deliveryId: computed(() => props.selectedRows.map((item) => item.id)),
      dataType: '',
      bizId: '',
      procInstName: '',
      groupId: '',
      userId: userStore.getUserInfo.id,
      templateList: [],
      approvalTableColumns: [
        {
          title: '名称',
          dataIndex: 'name',
        },
        {
          title: '状态',
          dataIndex: 'status',
          slots: { customRender: 'status' },
        },
        {
          title: '所有者',
          dataIndex: 'ownerName',
        },
        {
          title: '修改时间',
          dataIndex: 'modifyTime',
          type: 'dateTime',
        },
      ],
    });
    onMounted(() => {
    });

    function menuInstanceListApi(data) {
      let status = props.selectedRows[0].status;
      let params = {
        pageNum: 0,
        pageSize: 1000,
        query: {
          deliveries: state.deliveryId.map((item) => ({
            deliveryId: item,
          })),
          dataTypeBinds: props.selectedRows.every((item) => item.status === status) ? props.selectedRows.map((item) => ({
            classNameCode: item?.className,
            dataStatusCode: item?.status,
          })) : [],
          // forUserId: 'string',
          // tenantId: 'string',
          userId: userStore.getUserInfo.id,
        },
      };
      return new Api('/workflow').fetch(params, 'process-instance/by_delivery/page', 'POST').then((res) => res);
    }

    async function templateListApi() {
      let params = {
        pageNum: 1,
        pageSize: 10000,
        query: {
          dataTypeBinds: [
            {
              classNameCode: props.selectedRows[0].className,
              dataStatusCode: props.selectedRows[0].status,
            },
          ],
          status: 1,
          userId: state.userId,
        },
      };
      return new Api('/workflow')
        .fetch(
          params,
          'process-template/major/page',
          'POST',
        )
        .then((res) => {
          state.templateList = res.content;
          return res.content.map((item) => ({
            label: item.name,
            value: item.procDefId,
            key: item.procDefId,
            id: item.procDefId,
          }));
        });
    }

    function allTaskPageApi(data) {
      let url = `process-instance/task-definition/page?processDefinitionId=${data.procDefId}&userId=${userStore.getUserInfo.id}`;
      if (data.type === 'edit') {
        url += `&processInstanceId=${data.menuActionItem?.id}`;
      }
      return new Api('/workflow').fetch('', url, 'POST').then((res) => res);
    }

    // 保存
    function addEditSavaApi(data) {
      let templateItem = state.templateList.find((item) => item.procDefId === data.flowInfoId);
      let params: any = {
        userId: state.userId,
        // bizCatalogId: 'string',
        // bizCatalogName: 'string',
        bizId: data.bizId,
        // bizTypeName: 'string',
        // businessKey: 'string',
        deliveries: data.deliveries,
        flowInfoId: templateItem.id,
        // flowKey: 'string',',
        href: `${props.href}/${props.selectedRows[0].id}?tabsType=process`,
        ownerId: userStore.getUserInfo.id,
        prearranges: data.prearranges,
        procDefId: templateItem.procDefId,
        procDefName: data.procDefName,
        procInstName: props.processName,
        bizCatalogName: props.bizCatalogName,
      };
      if (typeof data.id === 'undefined') {
        params.id = data.id;
      }
      return new Api('/workflow').fetch(params, 'process-instance', `${typeof data.id === 'undefined' ? 'POST' : 'PUT'}`).then((res) => res);
    }

    function journalApi(id) {
      let params = {
        procInstId: id,
        userId: userStore.getUserInfo.id,
      };
      return new Api('/workflow').fetch(params, 'act-inst-detail/journal', 'GET').then((res) => res);
    }

    // 获取流程按钮
    function taskBtnApi(data) {
      if (!data.currentTasks) return;

      let currentTasksItem = data.currentTasks.find((item) => {
        let predicate = false;
        if (item.applicant) {
          predicate = (data.startId === userStore.getUserInfo.id);
        } else {
          predicate = item.assigneesUser.some((sItem) => sItem.id === userStore.getUserInfo.id);
        }
        return predicate;
      });

      if (typeof currentTasksItem === 'undefined') return;
      let params = {
        procDefId: data.procDefId,
        userId: userStore.getUserInfo.id,
        taskId: currentTasksItem.id,
      };
      return new Api('/workflow').fetch(params, 'process-instance/task-action', 'GET').then((res) => res);
    }

    const successChange = (type) => {
      reLoadTable();
      useWebSocket?.getMessageTotal();
    };

    // 审批物列表
    function approvalListApi(id) {
      return new Promise((resolve, reject) => {
        resolve([]);
      });
    }

    function nodeTableDataApi(id) {
      let params = {
        procInstId: id,
        userId: userStore.getUserInfo.id,
      };
      return new Api('/workflow').fetch(params, 'act-inst-detail/journal', 'GET').then((res) => res);
    }

    const openClick = (record) => {
      window.open(`/api/document-platform/document/preview?fileId=${record.id}&fileName=${encodeURIComponent(record.name)}${record.filePostfix}&baseHost=${location.host}&fileExt=${record.filePostfix}`);
    };
    function userDataApi({ currentTask }) {
      const {
        post, organization, role, user,
      } = currentTask;

      return new Api('/pmi/user/role-dept-user-job').fetch({
        // 岗位
        jobs: post ? post.split(',') : undefined,
        // 组织id
        orgIds: organization ? organization.split(',') : undefined,
        // 角色
        roleIds: role ? role.split(',') : undefined,
        // 用户
        userIds: user ? user.split(',') : undefined,
      }, '', 'POST');
    }
    function start2Api(data) {
      const comment = data?.comment || undefined;
      return new Api('/workflow/facade/initiate').fetch({
        userId: userStore.getUserInfo.id,
        bizId: '',
        comment,
        deliveries: props.selectedRows.map((item) => ({
          deliveryId: item.id,
        })),
        dataTypeBinds: [
          {
            classNameCode: props.selectedRows[0].className,
            dataStatusCode: props.selectedRows[0].status,
          },
        ],
        href: `${props.href}/${state.deliveryId[0]}?tabsType=process`,
        ownerId: userStore.getUserInfo.id,
        procInstName: props.processName,
        bizCatalogName: props.bizCatalogName,
      }, '', 'post').then((res) => {
        reLoadTable();
        useWebSocket?.getMessageTotal();
      });
    }
    return {
      ...toRefs(state),
      menuInstanceListApi,
      templateListApi,
      allTaskPageApi,
      addEditSavaApi,
      journalApi,
      taskBtnApi,
      successChange,
      nodeTableDataApi,
      approvalListApi,
      openClick,
      bpmnMain,
      userDataApi,
      start2Api,
    };
  },
});
</script>
<style lang="less" scoped>
//.process {
//  height: 100%;
//}
</style>
