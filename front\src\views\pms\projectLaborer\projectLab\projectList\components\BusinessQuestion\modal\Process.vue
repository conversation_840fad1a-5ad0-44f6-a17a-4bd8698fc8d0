<template>
  <div class="process">
    <BpmnMain
      :menu-instance-list-api="menuInstanceListApi"
      :template-list-api="templateListApi"
      :allTaskPageApi="allTaskPageApi"
      :addEditSavaApi="addEditSavaApi"
      :userId="userId"
      :journalApi="journalApi"
      :taskBtnApi="taskBtnApi"
      :approvalListApi="approvalListApi"
      :nodeTableDataApi="nodeTableDataApi"
      :type="2"
      @success="successChange"
      @openClick="openClick"
    />
  </div>
</template>

<script lang="ts">
import {
  defineComponent, onMounted, reactive, toRefs, computed, ref, inject, watch,
} from 'vue';
import { BpmnMain } from 'lyra-component-vue3';
import { useUserStore } from '/@/store/modules/user';
import { stampDate } from '/@/utils/dateUtil';
import Api from '/@/api';
export default defineComponent({
  name: 'Information',
  components: {
    BpmnMain,
  },
  props: {
    pageType: {
      type: String,
      default: 'page',
    },
  },
  emits: ['changePage'],
  setup(props, { emit, attrs }) {
    const formData: any = inject('formData', {});
    const getFormData = inject('getFormData');
    const userStore = useUserStore();
    const state:any = reactive({
      deliveryId: '',
      dataType: '',
      bizId: '',
      procInstName: '',
      groupId: '',
      userId: userStore.getUserInfo.id,
      templateList: [],
    });
    onMounted(() => {
      // if (formData?.value?.files) {
      //   tableRef.value.setTableData(formData?.value?.files);
      // }
    });
    function menuInstanceListApi(data) {
      let params = {
        pageNum: 0,
        pageSize: 1000,
        query: {
          deliveries: [
            {
              deliveryId: formData?.value?.id,
            },
          ],
          // forUserId: 'string',
          // tenantId: 'string',
          userId: userStore.getUserInfo.id,
        },
      };
      return new Api('/workflow').fetch(params, 'process-instance/by_delivery/page', 'POST').then((res) => res);
    }
    // function templateListApi() {
    //   return basicConfig.getProcessList(formData?.value?.matterType).then((res) => {
    //     state.templateList = res;
    //     return res.map((item) => ({
    //       label: item.name,
    //       value: item.procDefId,
    //       key: item.procDefId,
    //       id: item.procDefId,
    //     }));
    //   });
    // }

    function templateListApi() {
      return new Api('/res/data-type/list')
        .fetch(
          {
            dataType: formData?.value?.className,
          },
          '',
          'POST',
        )
        .then(async (data) => {
          if (data && data.length) {
            state.templateList = await new Api('/res/data-type-group/template/list')
              .fetch(
                {
                  dataTypeId: 'zt1ld947099ac3914e8b8336819c0cdb3ba8',
                  groupId: 'bzdj3b0a0a6e70704413b371d1bb738aa1f3',
                  templateType: 'FLOW-TEMPLATE',
                },
                '',
                'POST',
              )
              .then((data) => (
                (data
                          && data.map((item) => ({
                            ...item,
                            label: item.name,
                            key: item.procDefId,
                            value: item.procDefId,
                          })))
                      || []
              ));
            return state.templateList;
          }
          return [];
        });
    }
    function allTaskPageApi(data) {
      let url = `process-instance/task-definition/page?processDefinitionId=${data.procDefId}&userId=${userStore.getUserInfo.id}`;
      return new Api('/workflow').fetch('', url, 'POST').then((res) => res);
    }
    // 保存
    function addEditSavaApi(data) {
      let templateItem = state.templateList.find((item) => item.procDefId === data.flowInfoId);
      data.deliveries.forEach((item) => {
        item.deliveryId = formData?.value?.id;
      });
      let params:any = {
        // bizCatalogId: 'string',
        // bizCatalogName: 'string',
        bizId: data.bizId,
        // bizTypeName: 'string',
        // businessKey: 'string',
        deliveries: data.deliveries,
        flowInfoId: templateItem.id,
        // flowKey: 'string',
        // href: 'string',
        ownerId: userStore.getUserInfo.id,
        prearranges: data.prearranges,
        procDefId: templateItem.procDefId,
        procDefName: data.procDefName,
        procInstName: `${formData?.value?.name}实例${stampDate(Date.parse(new Date()), 'yyyy-MM-dd HH:mm:ss')}`,
      };
      if (typeof data.id === 'undefined') {
        return new Api('/workflow').fetch(params, 'process-instance', 'POST').then((res) => res);
      }
      params.id = data.id;
      return new Api('/workflow').fetch(params, 'process-instance', 'PUT').then((res) => res);
    }
    function journalApi(id) {
      let params = {
        procInstId: id,
        userId: userStore.getUserInfo.id,
      };
      return new Api('/workflow').fetch(params, 'act-inst-detail/journal', 'GET').then((res) => res);
    }
    // 获取流程按钮
    function taskBtnApi(data) {
      if (!data.currentTasks) return;
      let currentTasksItem = data.currentTasks.find((item) => item.assignee === userStore.getUserInfo.id);
      let params = {
        procDefId: data.procDefId,
        userId: userStore.getUserInfo.id,
        taskId: currentTasksItem.id,
      };
      return new Api('/workflow').fetch(params, 'process-instance/task-action', 'GET').then((res) => res);
    }
    const successChange = (type) => {
      getFormData.value(formData?.value?.id);
    };
    // 审批物列表
    function approvalListApi(id) {
      return new Promise((resolve, reject) => {
        resolve([formData?.value]);
      });
    }
    function nodeTableDataApi(id) {
      let params = {
        procInstId: id,
        userId: userStore.getUserInfo.id,
      };
      return new Api('/workflow').fetch(params, 'act-inst-detail/journal', 'GET').then((res) => res);
    }
    const openClick = (record) => {
      emit('changePage', record.id);
    };
    return {
      ...toRefs(state),
      menuInstanceListApi,
      templateListApi,
      allTaskPageApi,
      addEditSavaApi,
      journalApi,
      taskBtnApi,
      successChange,
      nodeTableDataApi,
      approvalListApi,
      openClick,
    };
  },
});
</script>
<style lang="less" scoped>
.process{
  height: 100%;
}
</style>
