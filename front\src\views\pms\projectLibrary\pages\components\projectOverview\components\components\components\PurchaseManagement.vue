<script setup lang="ts">
import { UploadList, BasicCard } from 'lyra-component-vue3';
import {
  defineProps, inject, onMounted, ref, Ref,
} from 'vue';
import Api from '/@/api';

const props = defineProps({
  step: {
    type: Number,
    required: true,
  },
});
const projectId: string = inject('projectId');
const tableRef: Ref = ref();
const knowledgeList: Ref<Record<string, any>[]> = ref([]);
const listApi = async () => {
  const result = await new Api(`/pms/projectOverview/zgh/documentCount/${projectId}`).fetch({ ...props }, '', 'GET');
  knowledgeList.value = result || [];
  return knowledgeList.value;
};
onMounted(() => {
  listApi();
});
</script>

<template>
  <div class="self-define-parent">
    <BasicCard
      v-for="item in knowledgeList"
      :key="item?.groupName"
      :title="item?.groupName"
      class="self-define"
      :isSpacing="false"
    >
      <UploadList
        ref="tableRef"
        :listData="item?.fileVOS"
        :edit="false"
        height="200px"
        :isSpacing="false"
      />
    </BasicCard>
  </div>
</template>

<style scoped lang="less">
.self-define-parent{
  .self-define{
    width: 100%;
    margin-top: 15px;
  }
}
</style>
