package com.chinasie.orion.management.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ContractSupplierRecord VO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@ApiModel(value = "ContractSupplierRecordVO对象", description = "合同供应商记录表")
@Data
public class ContractSupplierRecordVO extends ObjectVO implements Serializable {

    /**
     * 合同号
     */
    @ApiModelProperty(value = "合同号")
    private String contractNumber;


    /**
     * 供应商ID
     */
    @ApiModelProperty(value = "供应商ID")
    private String supplierId;


    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;


    /**
     * 供应商来源
     */
    @ApiModelProperty(value = "供应商来源")
    private String supplierFrom;


    /**
     * 是否询价供应商
     */
    @ApiModelProperty(value = "是否询价供应商")
    private Boolean isInquirySupplier;


    /**
     * 是否中标供应商
     */
    @ApiModelProperty(value = "是否中标供应商")
    private Boolean isWinnerSupplier;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    private String mainTableId;


    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;
}
