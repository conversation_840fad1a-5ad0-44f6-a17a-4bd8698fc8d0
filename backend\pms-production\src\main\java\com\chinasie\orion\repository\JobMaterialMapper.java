package com.chinasie.orion.repository;
import com.chinasie.orion.domain.dto.CountJobDTO;
import com.chinasie.orion.domain.dto.source.MaterialSourceDTO;
import com.chinasie.orion.domain.entity.JobMaterial;
import org.apache.ibatis.annotations.Mapper;

import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;


/**
 * <p>
 * JobMaterial Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-09 15:47:40
 */
@Mapper
public interface JobMaterialMapper extends  OrionBaseMapper  <JobMaterial> {

    @Update({"<script>"," update pmsx_job_material set material_id =#{materialId} where id =#{id}","</script>"})
    void updateMaterialId(@Param("id") String id,@Param("materialId") String materialId);

    List<CountJobDTO> getMaterialNumMap(@Param("materialIdList") List<String> materialIdList);

    List<MaterialSourceDTO> getMaterialJobInfoList(@Param("repairRound") String repairRound,@Param("keyword") String keyword,@Param("year")  int year);

    List<MaterialSourceDTO> materiallapSourceList(@Param("repairRound")String repairRound,@Param("keyword") String keyword,@Param("year") int year
            ,@Param("materialNumberList") List<String> materialNumberList);
}

