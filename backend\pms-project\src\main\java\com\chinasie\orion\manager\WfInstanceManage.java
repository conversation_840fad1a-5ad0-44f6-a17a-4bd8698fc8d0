package com.chinasie.orion.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.vo.workflow.FlowTemplateBusinessVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.enums.StatusEnum;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.feign.WorkflowFeignService;
import com.chinasie.orion.feign.request.*;
import com.chinasie.orion.feign.response.FlowTemplateVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;

import com.chinasie.orion.util.ResponseUtils;
import io.swagger.models.Scheme;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class WfInstanceManage {

    private static final String SCHEME_DETAIL_URL = "/pms/ProPlanDetails/%s";
    @Resource
    private WorkflowFeignService processInstanceService;
    @Resource
    private UserRedisHelper userRedisHelper;

    public void process(List<ProjectScheme> schemes, String name)  {
        String templateId = getTemplateId(name);
        String businessKey = getBusinessKey(schemes, templateId);
    }

    private String getTemplateId(String name) {
        ResponseDTO<List<FlowTemplateVO>> listResponseDTO = null;
        Map<String,Object> paramMap =new HashMap<>();
        paramMap.put("process",name);
        try {
            listResponseDTO = processInstanceService.byDataTypeId("ProjectScheme",paramMap);
        } catch (Exception e) {
            log.error("调用流程服务，获取模板ID异常：", e);
            throw new BaseException(PMSErrorCode.PMS_ERR, "调用流程服务异常");
        }
        List<FlowTemplateVO> result = listResponseDTO.getResult();
        List<FlowTemplateVO> majorTemplate = result.stream().filter(item->item.getIsMajor()).collect(Collectors.toList());
        if (CollUtil.isEmpty(majorTemplate) ) {
            throw new BaseException(PMSErrorCode.PMS_ERR, "流程模板不存在请检查");
        }
        return result.get(0).getId();
    }

    private String getBusinessKey(List<ProjectScheme> schemes, String templateId) {

        FlowTemplateBusinessBatchDTO flowTemplateBusinessBatchDTO = new FlowTemplateBusinessBatchDTO();
        List<FlowTemplateBusinessDetailBatchDTO> flowTemplateBusinessDetailBatchDTOS = new ArrayList<>();
        for(ProjectScheme projectScheme:schemes) {
            FlowTemplateBusinessDetailBatchDTO flowTemplateBusinessDetailBatchDTO = new FlowTemplateBusinessDetailBatchDTO();
            flowTemplateBusinessDetailBatchDTO.setBusinessId(projectScheme.getId());
            flowTemplateBusinessDetailBatchDTO.setBusinessName(buildTitle());
            flowTemplateBusinessDetailBatchDTO.setMessageUrl(String.format(SCHEME_DETAIL_URL, projectScheme.getId()));
            flowTemplateBusinessDetailBatchDTO.setDataTypeCode("ProjectScheme");
            flowTemplateBusinessDetailBatchDTOS.add(flowTemplateBusinessDetailBatchDTO);
        }
        flowTemplateBusinessBatchDTO.setTemplateId(templateId);
        flowTemplateBusinessBatchDTO.setBusinessList(flowTemplateBusinessDetailBatchDTOS);
        flowTemplateBusinessBatchDTO.setOrgId(CurrentUserHelper.getOrgId());
        FlowTemplateBusinessDTO businessDTO = new FlowTemplateBusinessDTO();

        businessDTO.setTemplateId(templateId);
        try {
            log.info("调用流程服务，建立业务与模板管理关系，请求参数：{}", JSON.toJSONString(businessDTO));
            ResponseDTO<FlowTemplateBusinessVO> responseDTO = processInstanceService.batchCreate(flowTemplateBusinessBatchDTO);
            log.info("调用流程服务，建立业务与模板管理关系，响应结果：{}", JSON.toJSONString(responseDTO));
            return responseDTO.getResult().getId();
        } catch (Exception e) {
            log.error("调用流程服务，建立业务与模板管理关系异常：", e);
            throw new BaseException(PMSErrorCode.PMS_ERR,"调用流程服务异常");
        }
    }

    private String buildTitle() {
        SimpleUser user = userRedisHelper.getSimpleUserById(CurrentUserHelper.getCurrentUserId());
        if (Objects.nonNull(user)) {
            return "【" + user.getName() + "】" + "发起计划流程";
        }
        return "";
    }


    public void removeProcess(List<ProjectScheme> schemes) throws Exception {
        List<String> ids = schemes.stream().map(ProjectScheme::getId).collect(Collectors.toList());
        FlowTemplateBusinessDeleteDTO flowTemplateBusinessDeleteDTO = new FlowTemplateBusinessDeleteDTO();
        flowTemplateBusinessDeleteDTO.setBusinessIds(ids);
        processInstanceService.batchDelete(flowTemplateBusinessDeleteDTO);
    }
}
