package com.chinasie.orion.domain.vo.validation;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/08/07/16:13
 * @description:
 */
@Data
@AllArgsConstructor
public class ValidationResult implements Serializable {
    @ApiModelProperty("状态码：200 为全部成功，!200 为有失败的数据")
    private long statusCode;
    @ApiModelProperty("统计消息：总的审核数，通过的数量，未通过的数量")
    private String countMessage;
    @ApiModelProperty("具体未验证通过的详细信息")
    private List<PersonValidationVO> validationVOList;
}
