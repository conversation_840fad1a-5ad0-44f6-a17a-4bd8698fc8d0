模板
<script setup lang="ts">
import { BasicForm, useForm } from 'lyra-component-vue3';
import {
  computed, reactive, Ref, ref,
} from 'vue';

import { Input as AInput } from 'ant-design-vue';
import { simpleProjectTableColumns } from '../../tableColumns.js';

import Api from '/@/api';

import { AddSelectTableModal } from '../AddSelectTableModal/index';

// 定义组件props，接收一个type字符串
const props = defineProps<{
  type: string | undefined
}>();

// 初始化加载状态
const loading: Ref<boolean> = ref(false);

// 项目基本信息响应式对象
const commonProjectInfo = reactive({
  projectName: '',
  projectId: '',
  oldStatus: '',
  oldStatusName: '',
});

// 项目管理员参数响应式对象
const projectManagerPrams = reactive({
  projectName: computed(() => commonProjectInfo.projectName),
  projectId: computed(() => commonProjectInfo.projectId),
  oldManager: '',
  oldManagerName: '',
  newManager: '',
  newManagerName: '',
});

/**
 * 点击选择简单项目时的处理函数
 * 弹出选择项目的模态框
 */
function SelectSimpleProjectClick() {
  AddSelectTableModal({
    title: '项目列表',
    width: '80%',
    selectType: 'radio',
    selectedData: [
      {
        id: commonProjectInfo.projectId,
        name: commonProjectInfo.projectName,
      },
    ],
    columns: simpleProjectTableColumns,
    tableApi(option) {
      const params: Record<string, any> = {
        ...option,
        query: {
        },
      };
      delete params.node;
      delete params.tableMethods;
      delete params.orders;
      // 调用API获取项目列表数据
      return new Api('/pms/project/getSimplePage').fetch(params, '', 'POST');
    },
    async onOk({ tableData }) {
      const obj = tableData[0];
      // 选中项目后，更新commonProjectInfo信息
      commonProjectInfo.projectName = obj.name;
      commonProjectInfo.projectId = obj.id;
      commonProjectInfo.oldStatusName = obj.dataStatus.name;
      commonProjectInfo.oldStatus = obj.dataStatus.statusValue;
      // 更新projectManagerPrams信息
      projectManagerPrams.oldManagerName = obj.pm;
      projectManagerPrams.oldManager = obj.pmId;
      setFieldsValue({
        projectName: obj.name,
        projectId: obj.id,
        oldManagerName: obj.pm,
        oldManager: obj.pmId,
        oldStatusName: obj.dataStatus.name,
        oldStatus: obj.dataStatus.statusValue,
      });
    },
  });
}

// 使用useForm钩子来管理表单
const [
  register,
  {
    validate, updateSchema, setFieldsValue, getFieldsValue,
  },
] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas: [
    {
      field: 'projectName',
      component: 'Input',
      slot: 'SelectSimpleProject',
      label: '选择项目',
      rules: [{ required: true }],
    },
    {
      field: 'oldManagerName',
      component: 'Input',
      label: '原项目经理',
      rules: [{ required: true }],
      componentProps: {
        disabled: true,
      },
    },
    {
      field: 'newManagerList',
      component: 'SelectUser',
      label: '新项目经理',
      rules: [{ required: true }],
      componentProps: {
        selectUserModalProps: {
          selectType: 'radio',
        },
      },
    },
  ],
});

// 定义暴露的接口
defineExpose({
  /**
   * 获取表单数据
   * @returns 如果type为'project_manager'，返回projectManagerPrams，否则返回null
   */
  async getFormData() {
    const formValues = await validate();
    if (formValues && props.type === 'project_manager') {
      projectManagerPrams.newManagerName = formValues.newManagerList[0].name;
      projectManagerPrams.newManager = formValues.newManagerList[0].id;
      return projectManagerPrams;
    }
    return null;
  },
  /**
   * 设置表单数据
   * @param record 设置表单的记录
   * @param detailData 详细数据，默认为null
   */
  async setFormData(record, detailData = null) {
    await setFieldsValue({ ...record });

    // 更新commonProjectInfo信息
    commonProjectInfo.projectName = record.projectName;
    commonProjectInfo.projectId = record.projectId;
    commonProjectInfo.oldStatus = record.oldStatus;
    commonProjectInfo.oldStatusName = record.oldStatusName;
    // 如果type为'project_manager'，更新projectManagerPrams信息
    if (props.type === 'project_manager') {
      projectManagerPrams.oldManager = record.oldManager;
      projectManagerPrams.oldManagerName = record.oldManagerName;
      projectManagerPrams.newManager = record.newManager;
      projectManagerPrams.newManagerName = record.newManagerName;
      await setFieldsValue({
        newManagerList: [
          {
            id: projectManagerPrams.newManager,
            name: projectManagerPrams.newManagerName,
          },
        ],
      });
    }
  },
});

// 表单引用
const tableRef = ref();

</script>

<template>
  <div>
    <BasicForm
      v-if="type!==''"
      :key="type"
      @register="register"
    >
      <template #SelectSimpleProject="{ model, field }">
        <AInput
          v-model:value="model[field]"
          style="width: 100%"
          @click="SelectSimpleProjectClick"
        />
      </template>
    </BasicForm>
  </div>
</template>

<style scoped lang="less">
.min-table{
  min-height: 300px;
}
</style>
