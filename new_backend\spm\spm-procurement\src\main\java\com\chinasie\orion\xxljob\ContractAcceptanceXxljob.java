package com.chinasie.orion.xxljob;

import com.chinasie.orion.constant.MsgHandlerConstant;
import com.chinasie.orion.domain.entity.ContractExtendInfo;
import com.chinasie.orion.repository.ContractInfoMapper;
import com.chinasie.orion.msc.api.MscBuildHandlerManager;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class ContractAcceptanceXxljob {

    @Resource
    ContractInfoMapper contractInfoMapper;

    @Resource
    MscBuildHandlerManager mscBuildHandlerManager;

    @XxlJob(value = "contractAcceptanceJobHandler")
    public void acceptance(){
        List<ContractExtendInfo> contractInfo = contractInfoMapper.getContractInfo();
        contractInfo.forEach(item->{
            mscBuildHandlerManager.send(item, MsgHandlerConstant.NODE_CONTRACT_ACCEPTANCE);
        });

    }


}
