
-- 基础字段
INSERT INTO `dme_class`(`id`, `package_id`, `table_name`, `class_name`, `code`, `parent_id`, `prefix`, `label`, `type`, `icon`, `description`, `is_abstract`, `is_extend`, `class_route`, `table_route`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `sort`, `policy`, `revision`, `platform_id`, `unique_key`, `logic_status`, `owner_id`, `tree_flag`) VALUES ('gtjl1804420576767827968', 'eh6of839dd0486dd4e28a76134550b57834f', 'pmsx_person_train_info_record', 'PersonTrainInfoRecord', 'yqox', NULL, 'pmsx', 'persontraininforecord', 'common', NULL, NULL, NULL, NULL, NULL, NULL, 1, '用户培训信息落地', '314j1000000000000000000', '2024-06-22 15:46:05', '314j1000000000000000000', '2024-06-22 15:46:05', 10, NULL, NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, '314j1000000000000000000', 0);
INSERT INTO `dme_class`(`id`, `package_id`, `table_name`, `class_name`, `code`, `parent_id`, `prefix`, `label`, `type`, `icon`, `description`, `is_abstract`, `is_extend`, `class_route`, `table_route`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `sort`, `policy`, `revision`, `platform_id`, `unique_key`, `logic_status`, `owner_id`, `tree_flag`) VALUES ('gtjl1804421922074058752', 'eh6of839dd0486dd4e28a76134550b57834f', 'pmsx_person_train_equ_record', 'PersonTrainEquRecord', '5boa', NULL, 'pmsx', 'persontrainequrecord', 'common', NULL, NULL, NULL, NULL, NULL, NULL, 1, '人员培训等效信息记录', '314j1000000000000000000', '2024-06-22 15:51:25', '314j1000000000000000000', '2024-06-22 15:51:34', 10, NULL, NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, '314j1000000000000000000', 0);
INSERT INTO `dme_class`(`id`, `package_id`, `table_name`, `class_name`, `code`, `parent_id`, `prefix`, `label`, `type`, `icon`, `description`, `is_abstract`, `is_extend`, `class_route`, `table_route`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `sort`, `policy`, `revision`, `platform_id`, `unique_key`, `logic_status`, `owner_id`, `tree_flag`) VALUES ('gtjl1804423227907694592', 'eh6of839dd0486dd4e28a76134550b57834f', 'pmsx_person_job_post_authorize', 'PersonJobPostAuthorize', 'a9sd', NULL, 'pmsx', 'personjobpostauthorize', 'common', NULL, NULL, NULL, NULL, NULL, NULL, 1, '人员岗位授权记录落地', '314j1000000000000000000', '2024-06-22 15:56:37', '314j1000000000000000000', '2024-06-22 15:56:37', 10, NULL, NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, '314j1000000000000000000', 0);
INSERT INTO `dme_class`(`id`, `package_id`, `table_name`, `class_name`, `code`, `parent_id`, `prefix`, `label`, `type`, `icon`, `description`, `is_abstract`, `is_extend`, `class_route`, `table_route`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `sort`, `policy`, `revision`, `platform_id`, `unique_key`, `logic_status`, `owner_id`, `tree_flag`) VALUES ('gtjl1804423443880796160', 'eh6of839dd0486dd4e28a76134550b57834f', 'pmsx_person_job_post_Equ', 'PersonJobPostEqu', 'xeyj', NULL, 'pmsx', 'personjobpostequ', 'common', NULL, NULL, NULL, NULL, NULL, NULL, 1, '人员岗位等效记录落地', '314j1000000000000000000', '2024-06-22 15:57:28', '314j1000000000000000000', '2024-06-22 15:57:43', 10, NULL, NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, '314j1000000000000000000', 0);



INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804420576847519744', 'gtjl1804420576767827968', 'id', 'Varchar', 64, NULL, 'id', NULL, NULL, NULL, NULL, 1, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:46:05', '314j1000000000000000000', '2024-06-22 15:46:05', '主键', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804420576847519745', 'gtjl1804420576767827968', 'className', 'Varchar', 64, NULL, 'classname', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:46:05', '314j1000000000000000000', '2024-06-22 15:46:05', '创建人', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804420576847519746', 'gtjl1804420576767827968', 'creatorId', 'Varchar', 64, NULL, 'creatorid', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:46:05', '314j1000000000000000000', '2024-06-22 15:46:05', '创建人', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804420576847519747', 'gtjl1804420576767827968', 'modifyTime', 'DateTime', 6, NULL, 'modifytime', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:46:05', '314j1000000000000000000', '2024-06-22 15:46:05', '修改时间', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804420576847519748', 'gtjl1804420576767827968', 'ownerId', 'Varchar', 64, NULL, 'ownerid', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:46:05', '314j1000000000000000000', '2024-06-22 15:46:05', '拥有者', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804420576847519749', 'gtjl1804420576767827968', 'createTime', 'DateTime', 6, NULL, 'createtime', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:46:05', '314j1000000000000000000', '2024-06-22 15:46:05', '创建时间', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804420576847519750', 'gtjl1804420576767827968', 'modifyId', 'Varchar', 64, NULL, 'modifyid', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:46:05', '314j1000000000000000000', '2024-06-22 15:46:05', '修改人', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804420576847519751', 'gtjl1804420576767827968', 'remark', 'Varchar', 1024, NULL, 'remark', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:46:05', '314j1000000000000000000', '2024-06-22 15:46:05', '备注', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804420576847519752', 'gtjl1804420576767827968', 'platformId', 'Varchar', 64, NULL, 'platformid', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:46:05', '314j1000000000000000000', '2024-06-22 15:46:05', '平台ID', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804420576847519753', 'gtjl1804420576767827968', 'orgId', 'Varchar', 64, NULL, 'orgid', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:46:05', '314j1000000000000000000', '2024-06-22 15:46:05', '业务组织Id', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804420576847519754', 'gtjl1804420576767827968', 'status', 'Integer', 1, NULL, 'status', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:46:05', '314j1000000000000000000', '2024-06-22 15:46:05', '状态', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804420576847519755', 'gtjl1804420576767827968', 'logicStatus', 'Integer', 1, NULL, 'logicstatus', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:46:05', '314j1000000000000000000', '2024-06-22 15:46:05', '逻辑删除字段', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804420891932024832', 'gtjl1804420576767827968', 'trainNumber', 'Varchar', 64, NULL, 'trainnumber', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:47:20', '314j1000000000000000000', '2024-06-22 15:47:20', '培训编码', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804420955115020288', 'gtjl1804420576767827968', 'trainName', 'Varchar', 64, NULL, 'trainname', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:47:35', '314j1000000000000000000', '2024-06-22 15:47:35', '培训名称', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804421008919552000', 'gtjl1804420576767827968', 'baseName', 'Varchar', 128, NULL, 'basename', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:47:48', '314j1000000000000000000', '2024-06-22 15:47:48', '培训基地名称', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804421062464036864', 'gtjl1804420576767827968', 'baseCode', 'Varchar', 64, NULL, 'basecode', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:48:00', '314j1000000000000000000', '2024-06-22 15:48:00', '培训基地编码', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804421206546767872', 'gtjl1804420576767827968', 'lessonHour', 'Decimal', 5, 1, 'lessonhour', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:48:35', '314j1000000000000000000', '2024-06-22 15:48:35', '培训课时', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804421297307312128', 'gtjl1804420576767827968', 'endDate', 'DateTime', 0, NULL, 'enddate', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:48:56', '314j1000000000000000000', '2024-06-22 15:48:56', '完成时间', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804421396276109312', 'gtjl1804420576767827968', 'isEquivalent', 'Boolean', 1, NULL, 'isequivalent', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:49:20', '314j1000000000000000000', '2024-06-22 15:49:20', '是否等效', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804421446997827584', 'gtjl1804420576767827968', 'expireTime', 'DateTime', 0, NULL, 'expiretime', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:49:32', '314j1000000000000000000', '2024-06-22 15:49:32', '到期时间时间', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804421577948192768', 'gtjl1804420576767827968', 'trainLecturer', 'Varchar', 128, NULL, 'trainlecturer', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:50:03', '314j1000000000000000000', '2024-06-22 15:50:03', '培训讲师', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804421703806672896', 'gtjl1804420576767827968', 'content', 'Text', 0, NULL, 'content', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:50:33', '314j1000000000000000000', '2024-06-22 15:50:33', '培训内容', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804421922149556224', 'gtjl1804421922074058752', 'id', 'Varchar', 64, NULL, 'id', NULL, NULL, NULL, NULL, 1, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:51:25', '314j1000000000000000000', '2024-06-22 15:51:25', '主键', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804421922149556225', 'gtjl1804421922074058752', 'className', 'Varchar', 64, NULL, 'classname', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:51:25', '314j1000000000000000000', '2024-06-22 15:51:25', '创建人', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804421922149556226', 'gtjl1804421922074058752', 'creatorId', 'Varchar', 64, NULL, 'creatorid', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:51:25', '314j1000000000000000000', '2024-06-22 15:51:25', '创建人', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804421922149556227', 'gtjl1804421922074058752', 'modifyTime', 'DateTime', 6, NULL, 'modifytime', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:51:25', '314j1000000000000000000', '2024-06-22 15:51:25', '修改时间', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804421922149556228', 'gtjl1804421922074058752', 'ownerId', 'Varchar', 64, NULL, 'ownerid', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:51:25', '314j1000000000000000000', '2024-06-22 15:51:25', '拥有者', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804421922149556229', 'gtjl1804421922074058752', 'createTime', 'DateTime', 6, NULL, 'createtime', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:51:25', '314j1000000000000000000', '2024-06-22 15:51:25', '创建时间', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804421922149556230', 'gtjl1804421922074058752', 'modifyId', 'Varchar', 64, NULL, 'modifyid', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:51:25', '314j1000000000000000000', '2024-06-22 15:51:25', '修改人', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804421922149556231', 'gtjl1804421922074058752', 'remark', 'Varchar', 1024, NULL, 'remark', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:51:25', '314j1000000000000000000', '2024-06-22 15:51:25', '备注', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804421922149556232', 'gtjl1804421922074058752', 'platformId', 'Varchar', 64, NULL, 'platformid', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:51:25', '314j1000000000000000000', '2024-06-22 15:51:25', '平台ID', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804421922149556233', 'gtjl1804421922074058752', 'orgId', 'Varchar', 64, NULL, 'orgid', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:51:25', '314j1000000000000000000', '2024-06-22 15:51:25', '业务组织Id', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804421922149556234', 'gtjl1804421922074058752', 'status', 'Integer', 1, NULL, 'status', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:51:25', '314j1000000000000000000', '2024-06-22 15:51:25', '状态', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804421922149556235', 'gtjl1804421922074058752', 'logicStatus', 'Integer', 1, NULL, 'logicstatus', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:51:25', '314j1000000000000000000', '2024-06-22 15:51:25', '逻辑删除字段', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804422265293955072', 'gtjl1804421922074058752', 'equivalentBaseCode', 'Varchar', 64, NULL, 'equivalentbasecode', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:52:47', '314j1000000000000000000', '2024-06-22 15:52:47', '等效基地编号', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804422306209390592', 'gtjl1804421922074058752', 'equivalentBaseName', 'Varchar', 128, NULL, 'equivalentbasename', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:52:57', '314j1000000000000000000', '2024-06-22 15:52:57', '等效基地名称', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804422416892878848', 'gtjl1804421922074058752', 'equivalentDate', 'DateTime', 0, NULL, 'equivalentdate', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:53:23', '314j1000000000000000000', '2024-06-22 15:53:23', '等效认定时间', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804422457300803584', 'gtjl1804421922074058752', 'userCode', 'Varchar', 64, NULL, 'usercode', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:53:33', '314j1000000000000000000', '2024-06-22 15:53:33', '人员编号', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804422549277696000', 'gtjl1804421922074058752', 'trainNumber', 'Varchar', 64, NULL, 'trainnumber', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:53:55', '314j1000000000000000000', '2024-06-22 15:53:55', '培训编号', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804423228083855360', 'gtjl1804423227907694592', 'id', 'Varchar', 64, NULL, 'id', NULL, NULL, NULL, NULL, 1, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:56:37', '314j1000000000000000000', '2024-06-22 15:56:37', '主键', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804423228083855361', 'gtjl1804423227907694592', 'className', 'Varchar', 64, NULL, 'classname', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:56:37', '314j1000000000000000000', '2024-06-22 15:56:37', '创建人', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804423228083855362', 'gtjl1804423227907694592', 'creatorId', 'Varchar', 64, NULL, 'creatorid', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:56:37', '314j1000000000000000000', '2024-06-22 15:56:37', '创建人', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804423228083855363', 'gtjl1804423227907694592', 'modifyTime', 'DateTime', 6, NULL, 'modifytime', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:56:37', '314j1000000000000000000', '2024-06-22 15:56:37', '修改时间', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804423228083855364', 'gtjl1804423227907694592', 'ownerId', 'Varchar', 64, NULL, 'ownerid', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:56:37', '314j1000000000000000000', '2024-06-22 15:56:37', '拥有者', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804423228083855365', 'gtjl1804423227907694592', 'createTime', 'DateTime', 6, NULL, 'createtime', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:56:37', '314j1000000000000000000', '2024-06-22 15:56:37', '创建时间', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804423228083855366', 'gtjl1804423227907694592', 'modifyId', 'Varchar', 64, NULL, 'modifyid', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:56:37', '314j1000000000000000000', '2024-06-22 15:56:37', '修改人', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804423228083855367', 'gtjl1804423227907694592', 'remark', 'Varchar', 1024, NULL, 'remark', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:56:37', '314j1000000000000000000', '2024-06-22 15:56:37', '备注', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804423228083855368', 'gtjl1804423227907694592', 'platformId', 'Varchar', 64, NULL, 'platformid', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:56:37', '314j1000000000000000000', '2024-06-22 15:56:37', '平台ID', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804423228083855369', 'gtjl1804423227907694592', 'orgId', 'Varchar', 64, NULL, 'orgid', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:56:37', '314j1000000000000000000', '2024-06-22 15:56:37', '业务组织Id', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804423228083855370', 'gtjl1804423227907694592', 'status', 'Integer', 1, NULL, 'status', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:56:37', '314j1000000000000000000', '2024-06-22 15:56:37', '状态', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804423228083855371', 'gtjl1804423227907694592', 'logicStatus', 'Integer', 1, NULL, 'logicstatus', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:56:37', '314j1000000000000000000', '2024-06-22 15:56:37', '逻辑删除字段', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804423443943710720', 'gtjl1804423443880796160', 'id', 'Varchar', 64, NULL, 'id', NULL, NULL, NULL, NULL, 1, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:57:28', '314j1000000000000000000', '2024-06-22 15:57:28', '主键', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804423443943710721', 'gtjl1804423443880796160', 'className', 'Varchar', 64, NULL, 'classname', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:57:28', '314j1000000000000000000', '2024-06-22 15:57:28', '创建人', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804423443943710722', 'gtjl1804423443880796160', 'creatorId', 'Varchar', 64, NULL, 'creatorid', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:57:28', '314j1000000000000000000', '2024-06-22 15:57:28', '创建人', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804423443943710723', 'gtjl1804423443880796160', 'modifyTime', 'DateTime', 6, NULL, 'modifytime', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:57:28', '314j1000000000000000000', '2024-06-22 15:57:28', '修改时间', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804423443943710724', 'gtjl1804423443880796160', 'ownerId', 'Varchar', 64, NULL, 'ownerid', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:57:28', '314j1000000000000000000', '2024-06-22 15:57:28', '拥有者', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804423443943710725', 'gtjl1804423443880796160', 'createTime', 'DateTime', 6, NULL, 'createtime', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:57:28', '314j1000000000000000000', '2024-06-22 15:57:28', '创建时间', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804423443943710726', 'gtjl1804423443880796160', 'modifyId', 'Varchar', 64, NULL, 'modifyid', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:57:28', '314j1000000000000000000', '2024-06-22 15:57:28', '修改人', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804423443943710727', 'gtjl1804423443880796160', 'remark', 'Varchar', 1024, NULL, 'remark', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:57:28', '314j1000000000000000000', '2024-06-22 15:57:28', '备注', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804423443943710728', 'gtjl1804423443880796160', 'platformId', 'Varchar', 64, NULL, 'platformid', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:57:28', '314j1000000000000000000', '2024-06-22 15:57:28', '平台ID', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804423443943710729', 'gtjl1804423443880796160', 'orgId', 'Varchar', 64, NULL, 'orgid', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:57:28', '314j1000000000000000000', '2024-06-22 15:57:28', '业务组织Id', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804423443943710730', 'gtjl1804423443880796160', 'status', 'Integer', 1, NULL, 'status', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:57:28', '314j1000000000000000000', '2024-06-22 15:57:28', '状态', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804423443943710731', 'gtjl1804423443880796160', 'logicStatus', 'Integer', 1, NULL, 'logicstatus', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 15:57:28', '314j1000000000000000000', '2024-06-22 15:57:28', '逻辑删除字段', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804425318092955648', 'gtjl1804423227907694592', 'jobPostCode', 'Varchar', 64, NULL, 'jobpostcode', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 16:04:55', '314j1000000000000000000', '2024-06-22 16:04:55', '岗位编号', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804425389333209088', 'gtjl1804423227907694592', 'jobPostName', 'Varchar', 128, NULL, 'jobpostname', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 16:05:12', '314j1000000000000000000', '2024-06-22 16:05:12', '岗位名称', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804425472233627648', 'gtjl1804423227907694592', 'baseCode', 'Varchar', 64, NULL, 'baseplacecode', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 16:05:32', '314j1000000000000000000', '2024-06-22 16:30:12', '基地编码', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804425514608680960', 'gtjl1804423227907694592', 'baseName', 'Varchar', 128, NULL, 'baseplacename', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 16:05:42', '314j1000000000000000000', '2024-06-22 16:30:17', '基地名称', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804425630878982144', 'gtjl1804423227907694592', 'endDate', 'DateTime', 0, NULL, 'enddate', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 16:06:10', '314j1000000000000000000', '2024-06-22 16:06:10', '授权到期日期', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804425785518776320', 'gtjl1804423227907694592', 'authorizeStatus', 'Integer', 3, NULL, 'authorizestatus', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 16:06:47', '314j1000000000000000000', '2024-06-22 16:06:47', '授权状态', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804425929500844032', 'gtjl1804423227907694592', 'authorizeStatusName', 'Varchar', 64, NULL, 'authorizestatusname', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 16:07:21', '314j1000000000000000000', '2024-06-22 16:07:21', '授权状态（100-未授权，111-已授权）', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804425975784988672', 'gtjl1804423227907694592', 'isEquivalent', 'Boolean', 1, NULL, 'isequivalent', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 16:07:32', '314j1000000000000000000', '2024-06-22 16:07:32', '是否等效', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804426137714483200', 'gtjl1804423227907694592', 'jobCode', 'Varchar', 64, NULL, 'jobcode', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 16:08:11', '314j1000000000000000000', '2024-06-22 16:08:11', '作业编号', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804426299304239104', 'gtjl1804423227907694592', 'repairRound', 'Varchar', 64, NULL, 'repairround', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 16:08:49', '314j1000000000000000000', '2024-06-22 16:08:49', '大修轮次', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804426999681703936', 'gtjl1804423443880796160', 'baseCode', 'Varchar', 64, NULL, 'basecode', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 16:11:36', '314j1000000000000000000', '2024-06-22 16:11:36', '基地编码', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804427046582411264', 'gtjl1804423443880796160', 'baseName', 'Varchar', 64, NULL, 'basename', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 16:11:47', '314j1000000000000000000', '2024-06-22 16:11:47', '基地名称', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804427257417490432', 'gtjl1804423443880796160', 'equivalentDate', 'DateTime', 1, NULL, 'equivalentdate', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 16:12:37', '314j1000000000000000000', '2024-06-22 16:12:37', '等效认定时间', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804427697160904704', 'gtjl1804423443880796160', 'userCode', 'Varchar', 64, NULL, 'usercode', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 16:14:22', '314j1000000000000000000', '2024-06-22 16:14:22', '用户编号', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804429051593940992', 'gtjl1804423443880796160', 'jobPostCode', 'Varchar', 64, NULL, 'jobpostcode', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 16:19:45', '314j1000000000000000000', '2024-06-22 16:19:45', '岗位编号', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1804429217587716096', 'gtjl1804423443880796160', 'formBaseCode', 'Varchar', 64, NULL, 'formbasecode', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-06-22 16:20:25', '314j1000000000000000000', '2024-06-22 16:20:25', '被等效基地编号', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
