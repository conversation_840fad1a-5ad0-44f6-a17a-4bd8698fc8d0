package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * SupplierCertInfo DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@ApiModel(value = "SupplierCertInfoDTO对象", description = "资质信息表")
@Data
@ExcelIgnoreUnannotated
public class SupplierCertInfoDTO extends ObjectDTO implements Serializable {

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @ExcelProperty(value = "供应商编码 ", index = 0)
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @ExcelProperty(value = "供应商名称 ", index = 1)
    private String supplierName;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    @ExcelProperty(value = "序号 ", index = 2)
    private String serialNumber;

    /**
     * 资质证书名称
     */
    @ApiModelProperty(value = "资质证书名称")
    @ExcelProperty(value = "资质证书名称 ", index = 3)
    private String certName;

    /**
     * 资质类别
     */
    @ApiModelProperty(value = "资质类别")
    @ExcelProperty(value = "资质类别 ", index = 4)
    private String certCategory;

    /**
     * 资质等级
     */
    @ApiModelProperty(value = "资质等级")
    @ExcelProperty(value = "资质等级 ", index = 5)
    private String certLevel;

    /**
     * 资质分组
     */
    @ApiModelProperty(value = "资质分组")
    @ExcelProperty(value = "资质分组 ", index = 6)
    private String certGroup;

    /**
     * 代理品牌/产品名称
     */
    @ApiModelProperty(value = "代理品牌/产品名称")
    @ExcelProperty(value = "代理品牌/产品名称 ", index = 7)
    private String brandProduct;

    /**
     * 证书有效期截止日期
     */
    @ApiModelProperty(value = "证书有效期截止日期")
    @ExcelProperty(value = "证书有效期截止日期 ", index = 8)
    private String expiryDate;

    /**
     * 证书编码
     */
    @ApiModelProperty(value = "证书编码")
    @ExcelProperty(value = "证书编码 ", index = 9)
    private String certCode;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 10)
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @ExcelProperty(value = "主表ID ", index = 11)
    private String mainTableId;


}
