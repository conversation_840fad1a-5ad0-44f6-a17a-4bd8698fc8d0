<script setup lang="ts">
import {
  onMounted, computed, ref, h, inject, reactive,
} from 'vue';
import {
  BasicForm, FormSchema, InputSelectUser, isPower, useForm,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
import { message } from 'ant-design-vue';
import { deliverableEdit, deliverableQuery } from '/@/views/pms/api/review';
import { useRoute } from 'vue-router';

const route = useRoute();
const dataId = computed(() => route.params?.id);
const detailsData = ref({});
const selectPrincipalUser = ref([]);
const detailsPowerData: any = inject('detailsPowerData', reactive([]));
const schemas: FormSchema[] = [
  {
    field: 'deliverName',
    component: 'Input',
    label: '交付物名称',
    componentProps: {
      disabled: computed(() => !isPower('PMS_XMPSXQ_BUTTON_FILE_EDIT', detailsPowerData)),
      onBlur: (event) => baseEditFun(event.target.value, 'deliverName'),
    },
    rules: [
      {
        required: false,
      },
    ],
  },
  {
    field: 'principalName',
    component: 'InputSearch',
    label: '交付物责任人',
    rules: [
      {
        required: true,
        trigger: 'change',
        type: 'string',
      },
    ],
    render() {
      return h(InputSelectUser, {
        selectUserData: computed(() => selectPrincipalUser.value),
        inputProps: {
          disabled: !isPower('PMS_XMPSXQ_BUTTON_FILE_EDIT', detailsPowerData),
        },
        onChange(users) {
          const userName = users?.[0]?.name;
          selectPrincipalUser.value = users;
          baseEditFun();
          setFieldsValue({
            principalName: userName,
          });
        },
        selectUserModalProps: {
          selectType: 'radio',
        },
      });
    },
  },
  {
    field: 'deliveryTime',
    component: 'DatePicker',
    label: '计划交付时间',
    componentProps: {
      disabled: computed(() => !isPower('PMS_XMPSXQ_BUTTON_FILE_EDIT', detailsPowerData)),
      onChange: (value) => baseEditFun(value, 'deliveryTime'),
    },
    rules: [
      {
        required: false,
        type: 'string',
      },
    ],
  },
  {
    field: 'plmNo',
    component: 'Input',
    label: '评审文件PLM编号',
    componentProps: {
      disabled: computed(() => !isPower('PMS_XMPSXQ_BUTTON_FILE_EDIT', detailsPowerData)),
      onBlur: (event) => baseEditFun(event.target.value, 'plmNo'),
    },
    rules: [
      {
        required: false,
      },
    ],
  },
  {
    field: 'reviewFileNo',
    component: 'Input',
    label: '评审报告文件号',
    componentProps: {
      disabled: computed(() => !isPower('PMS_XMPSXQ_BUTTON_FILE_EDIT', detailsPowerData)),
      onBlur: (event) => baseEditFun(event.target.value, 'reviewFileNo'),
    },
    rules: [
      {
        required: false,
      },
    ],
  },
  {
    field: 'questionFileNo',
    component: 'Input',
    label: '问题整改报告文件号',
    componentProps: {
      disabled: computed(() => !isPower('PMS_XMPSXQ_BUTTON_FILE_EDIT', detailsPowerData)),
      onBlur: (event) => baseEditFun(event.target.value, 'questionFileNo'),
    },
    rules: [
      {
        required: false,
      },
    ],
  },
  {
    field: 'meetingNo',
    component: 'Input',
    label: '会议纪要号',
    componentProps: {
      disabled: computed(() => !isPower('PMS_XMPSXQ_BUTTON_FILE_EDIT', detailsPowerData)),
      onBlur: (event) => baseEditFun(event.target.value, 'meetingNo'),
    },
    rules: [
      {
        required: false,
      },
    ],
  },
];

const [
  register,
  {
    getFieldsValue, setFieldsValue,
  },
] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 8,
  },
  schemas,
});

const baseEditFun = async (value = null, type = null) => {
  const values = getFieldsValue();
  type && (values[type] = value);
  const { deliveryTime } = values;
  values.deliveryTime = deliveryTime ? dayjs(deliveryTime).format('YYYY-MM-DD') : '';
  const principalId = selectPrincipalUser.value[0]?.id;
  const principalName = selectPrincipalUser.value[0]?.name;
  await deliverableEdit({
    ...detailsData.value,
    ...values,
    principalId,
    principalName,
  });
  message.success('操作成功');
};

onMounted(async () => {
  const result = await deliverableQuery(dataId.value);
  detailsData.value = result;
  selectPrincipalUser.value = [
    {
      id: result.principalId,
      name: result.principalName,
    },
  ];
  setFieldsValue({ ...result });
});

</script>

<template>
  <BasicForm @register="register" />
</template>

<style scoped lang="less">

</style>
