<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.locationtech.jts</groupId>
        <artifactId>jts</artifactId>
        <version>1.19.0</version>
    </parent>
    <artifactId>jts-modules</artifactId>
    <name>${project.groupId}:${project.artifactId}</name>
    <packaging>pom</packaging>
    
    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <modules>
        <module>core</module>
        <module>io</module>
    </modules>

    <profiles>
        <profile>
            <id>all</id>
            <activation>
                <property>
                    <name>!release</name>
                </property>
            </activation>

            <modules>
                <module>example</module>
                <module>tests</module>
                <module>app</module>
                <module>lab</module>
            </modules>
        </profile>
    </profiles>

</project>
