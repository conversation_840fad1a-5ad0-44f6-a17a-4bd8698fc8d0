package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.MarketContractMilestoneAcceptanceAddDTO;
import com.chinasie.orion.domain.dto.MarketContractMilestoneBatchAcceptanceDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;


import com.chinasie.orion.domain.entity.MarketContractMilestoneAcceptance;
import com.chinasie.orion.domain.dto.MarketContractMilestoneAcceptanceDTO;
import com.chinasie.orion.domain.vo.MarketContractMilestoneAcceptanceVO;

import com.chinasie.orion.service.MarketContractMilestoneAcceptanceService;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;

import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;

import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * MarketContractMilestoneAcceptance 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-30 01:59:34
 */
@RestController
@RequestMapping("/marketContractMilestoneAcceptance")
@Api(tags = "市场合同里程碑验收信息")
public class MarketContractMilestoneAcceptanceController {

    @Autowired
    private MarketContractMilestoneAcceptanceService marketContractMilestoneAcceptanceService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据【{{#marketContractMilestoneAcceptanceDTO.name}}】", type = "市场合同里程碑验收信息", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<MarketContractMilestoneAcceptanceVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        MarketContractMilestoneAcceptanceVO rsp = marketContractMilestoneAcceptanceService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param marketContractMilestoneAcceptanceDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#marketContractMilestoneAcceptanceDTO.name}}】", type = "市场合同里程碑验收信息", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody MarketContractMilestoneAcceptanceDTO marketContractMilestoneAcceptanceDTO) throws Exception {
        String rsp = marketContractMilestoneAcceptanceService.create(marketContractMilestoneAcceptanceDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 里程碑验收
     *
     * @param
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "里程碑验收新增")
    @RequestMapping(value = "/addAcceptance", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#marketContractMilestoneAcceptanceDTO.name}}】", type = "市场合同里程碑验收信息", subType = "里程碑验收新增", bizNo = "{{#id}}")
    public ResponseDTO addAcceptance(@RequestBody MarketContractMilestoneAcceptanceAddDTO dto) throws Exception {
        marketContractMilestoneAcceptanceService.addAcceptance(dto);
        return new ResponseDTO<>(Boolean.TRUE);
    }


    /**
     * 里程碑验收新增详情
     *
     * @param
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "里程碑验收新增详情")
    @RequestMapping(value = "/addAcceptanceDetail", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#marketContractMilestoneAcceptanceDTO.name}}】", type = "市场合同里程碑验收信息", subType = "里程碑验收新增详情", bizNo = "{{#id}}")
    public ResponseDTO addAcceptanceDetail(@RequestBody MarketContractMilestoneAcceptanceAddDTO dto) throws Exception {
        MarketContractMilestoneAcceptanceAddDTO vo=  marketContractMilestoneAcceptanceService.addAcceptanceDetail(dto);
        return new ResponseDTO<>(vo);
    }


    /**
     * 批量验收
     *
     * @param marketContractMilestoneBatchAcceptanceDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "批量验收")
    @RequestMapping(value = "/batchAdd", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量验收【{{#marketContractMilestoneAcceptanceDTO.name}}】", type = "市场合同里程碑验收信息", subType = "批量验收", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> batchAdd(@RequestBody MarketContractMilestoneBatchAcceptanceDTO marketContractMilestoneBatchAcceptanceDTO) throws Exception {
        Boolean rsp = marketContractMilestoneAcceptanceService.batchAdd(marketContractMilestoneBatchAcceptanceDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 编辑
     *
     * @param marketContractMilestoneAcceptanceDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#marketContractMilestoneAcceptanceDTO.name}}】", type = "市场合同里程碑验收信息", subType = "编辑", bizNo = "{{#marketContractMilestoneAcceptanceDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody MarketContractMilestoneAcceptanceDTO marketContractMilestoneAcceptanceDTO) throws Exception {
        Boolean rsp = marketContractMilestoneAcceptanceService.edit(marketContractMilestoneAcceptanceDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "市场合同里程碑验收信息", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = marketContractMilestoneAcceptanceService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "市场合同里程碑验收信息", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = marketContractMilestoneAcceptanceService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "市场合同里程碑验收信息", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<MarketContractMilestoneAcceptanceVO>> pages(@RequestBody Page<MarketContractMilestoneAcceptanceDTO> pageRequest) throws Exception {
        Page<MarketContractMilestoneAcceptanceVO> rsp = marketContractMilestoneAcceptanceService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("市场合同里程碑验收信息导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "市场合同里程碑验收信息", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        marketContractMilestoneAcceptanceService.downloadExcelTpl(response);
    }

    @ApiOperation("市场合同里程碑验收信息导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "市场合同里程碑验收信息", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = marketContractMilestoneAcceptanceService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("市场合同里程碑验收信息导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "市场合同里程碑验收信息", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = marketContractMilestoneAcceptanceService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消市场合同里程碑验收信息导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "市场合同里程碑验收信息", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = marketContractMilestoneAcceptanceService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("市场合同里程碑验收信息导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "市场合同里程碑验收信息", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        marketContractMilestoneAcceptanceService.exportByExcel(searchConditions, response);
    }
}
