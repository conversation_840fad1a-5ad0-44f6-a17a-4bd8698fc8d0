package com.chinasie.orion.service.impl;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.bo.LyraFileBO;
import com.chinasie.orion.constant.MaterialToolTypeEnum;
import com.chinasie.orion.domain.dto.FixedAssetsDTO;
import com.chinasie.orion.domain.dto.FixedAssetsFileDTO;
import com.chinasie.orion.domain.entity.FixedAssets;
import com.chinasie.orion.domain.entity.JobMaterial;
import com.chinasie.orion.domain.vo.FixedAssetsVO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileTreeVO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.file.api.service.FileApiService;
import com.chinasie.orion.repository.FixedAssetsMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.domain.vo.org.DeptBaseInfoVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.BasePlaceService;
import com.chinasie.orion.service.FixedAssetsService;
import com.chinasie.orion.service.JobMaterialService;
import com.chinasie.orion.tree.TreeNode;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;




/**
 * <p>
 * FixedAssets 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 14:36:24
 */
@Service
@Slf4j
public class FixedAssetsServiceImpl extends OrionBaseServiceImpl<FixedAssetsMapper, FixedAssets> implements FixedAssetsService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    BasePlaceService placeService;

    @Autowired
    private DeptRedisHelper deptRedisHelper;


    private JobMaterialService jobMaterialService;

    @Autowired
    private FileApiService fileApiService;

    @Autowired
    private void setJobMaterialService(JobMaterialService jobMaterialService) {
        this.jobMaterialService = jobMaterialService;
    }

    public static  final  String PMS_FIXED_ASSETS="FixedAssets";

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public FixedAssetsVO detail(String id, String pageCode) throws Exception {
        FixedAssets fixedAssets =this.getById(id);
        if(null == fixedAssets){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "数据不存在或者已删除，请重新刷新");
        }
        FixedAssetsVO result = BeanCopyUtils.convertTo(fixedAssets,FixedAssetsVO::new);
        setEveryName(Collections.singletonList(result));
        //详情封装附件列表
        List<FileTreeVO> filesByDataId = fileApiService.listAllFileByDataId(id);
        result.setFileList(CollectionUtils.isEmpty(filesByDataId)?new ArrayList<>():BeanCopyUtils.convertListTo(filesByDataId,FileVO::new));
        return result;
    }

    /**
     *  新增
     *
     * * @param FixedAssetsDTO
     */
    @Override
    public  String create(FixedAssetsDTO FixedAssetsDTO) throws Exception {
        FixedAssets fixedAssets =BeanCopyUtils.convertTo(FixedAssetsDTO,FixedAssets::new);
        this.save(fixedAssets);

        String rsp=fixedAssets.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param FixedAssetsDTO
     */
    @Override
    public Boolean edit(FixedAssetsDTO FixedAssetsDTO) throws Exception {
        FixedAssets fixedAssets =BeanCopyUtils.convertTo(FixedAssetsDTO,FixedAssets::new);

        this.updateById(fixedAssets);
        String rsp=fixedAssets.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        for (String id : ids) {




        }
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<FixedAssetsVO> pages( Page<FixedAssetsDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<FixedAssets> condition = new LambdaQueryWrapperX<>( FixedAssets. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(FixedAssets::getCreateTime);
        Page<FixedAssets> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), FixedAssets::new));
        FixedAssetsDTO fixedAssetsDTO= pageRequest.getQuery();
        if(Objects.nonNull(fixedAssetsDTO)){
            String jobId =fixedAssetsDTO.getJobId();
            if(StringUtils.hasText(jobId)){
                List<String> numberList= jobMaterialService.getNumberListByJobId(jobId);
                if(!CollectionUtils.isEmpty(numberList)){
                    condition.notIn(FixedAssets::getNumber,numberList);
                }
            }
        }
        PageResult<FixedAssets> page = this.getBaseMapper().selectPage(realPageRequest, condition);
        Page<FixedAssetsVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<FixedAssetsVO> vos = BeanCopyUtils.convertListTo(page.getContent(), FixedAssetsVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "固定资产能力库导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", FixedAssetsDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            FixedAssetsExcelListener excelReadListener = new FixedAssetsExcelListener();
        EasyExcel.read(inputStream,FixedAssetsDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<FixedAssetsDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("固定资产能力库导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<FixedAssets> FixedAssetses =BeanCopyUtils.convertListTo(dtoS,FixedAssets::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("ncf::FixedAssets-import::id", importId, FixedAssetses, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<FixedAssets> FixedAssetses = (List<FixedAssets>) orionJ2CacheService.get("ncf::FixedAssets-import::id", importId);
        log.info("固定资产能力库导入的入库数据={}", JSONUtil.toJsonStr(FixedAssetses));

        this.saveBatch(FixedAssetses);
        orionJ2CacheService.delete("ncf::FixedAssets-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("ncf::FixedAssets-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<FixedAssets> condition = new LambdaQueryWrapperX<>( FixedAssets. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(FixedAssets::getCreateTime);
        List<FixedAssets> FixedAssetses =   this.list(condition);

        List<FixedAssetsDTO> dtos = BeanCopyUtils.convertListTo(FixedAssetses, FixedAssetsDTO::new);

        String fileName = "固定资产能力库数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", FixedAssetsDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<FixedAssetsVO> vos)throws Exception {
        Map<String,String> codeToName = placeService.allMapSimpleList();
        List<String> userCodeList = new ArrayList<>();
        vos.forEach(item->{
            if(StringUtils.hasText(item.getUseUserNumber())){
                userCodeList.add(item.getUseUserNumber());
            }
            if(StringUtils.hasText(item.getRspUserNumber())){
                userCodeList.add(item.getRspUserNumber());
            }
        });
        Map<String,SimpleUser> codeToEntity = new HashMap<>();
        if(!CollectionUtils.isEmpty(userCodeList)){
            List<SimpleUser> simpUserList = userRedisHelper.getSimpleUserByCode(userCodeList.stream().distinct().collect(Collectors.toList()));
            codeToEntity =simpUserList.stream().collect(Collectors.toMap(SimpleUser::getCode,Function.identity(),(k1,k2)->k1));
        }
        String orgId = CurrentUserHelper.getOrgId();


        Map<String, SimpleUser> finalCodeToEntity = codeToEntity;
        vos.forEach(vo->{
            vo.setStorageLocationName(codeToName.getOrDefault(vo.getStorageLocation(),""));

            SimpleUser simpleUser = finalCodeToEntity.getOrDefault(vo.getRspUserNumber(),new SimpleUser());
            vo.setRspUserId(simpleUser.getId());
            vo.setRspUserName(simpleUser.getName());
            vo.setRspUserNumber(vo.getRspUserNumber());

            SimpleUser useEntity = finalCodeToEntity.getOrDefault(vo.getUseUserNumber(),new SimpleUser());
            vo.setUseUserId(useEntity.getId());
            vo.setUseUserName(useEntity.getName());
            vo.setToolStatusName(MaterialToolTypeEnum.getDescByKey(vo.getToolStatus()));
            vo.setUseUserNumber(vo.getUseUserNumber());
            vo.setToolStatusName(MaterialToolTypeEnum.getDescByKey(vo.getToolStatus()));
            if(StringUtils.hasText(vo.getCostCenter())){
                DeptBaseInfoVO deptBaseInfoByDeptCode = deptRedisHelper.getDeptBaseInfoByDeptCode(orgId, vo.getCostCenter());
                vo.setCostCenterName(deptBaseInfoByDeptCode == null?"":deptBaseInfoByDeptCode.getName());
            }
        });


    }

    @Override
    public FixedAssetsVO detailByNumber(String number) {
        LambdaQueryWrapperX<FixedAssets> condition = new LambdaQueryWrapperX<>( FixedAssets. class);
        condition.eq(FixedAssets::getNumber,number);
        List<FixedAssets> list = this.list(condition);
        if(CollectionUtils.isEmpty(list)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "该数据不存在");
        }

        return BeanCopyUtils.convertTo(list.get(0),FixedAssetsVO::new);
    }

    @Override
    public void updateByNumber(String number, String assetCode, Boolean overDue, Boolean isverifivation, Date date) {
        LambdaQueryWrapperX<FixedAssets> condition = new LambdaQueryWrapperX<>( FixedAssets. class);
        condition.select(FixedAssets::getId);
        condition.eq(FixedAssets::getNumber,number);
        List<FixedAssets> list = this.list(condition);
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        FixedAssets fixedAssets =list.get(0);
        LambdaUpdateWrapper<FixedAssets> updateWrapper =new LambdaUpdateWrapper<>();
        updateWrapper.set(FixedAssets::getIsNeedVerification,isverifivation);
        updateWrapper.set(FixedAssets::getNextVerificationTime,date);
        updateWrapper.set(FixedAssets::getIsOverdue,overDue);
        updateWrapper.eq(FixedAssets::getId,fixedAssets.getId());
        this.updateById(fixedAssets);
    }

    @Override
    public void updateByList(Boolean key, List<String> value) {
        LambdaUpdateWrapper<FixedAssets> updateWrapper =new LambdaUpdateWrapper<>();
        updateWrapper.in(FixedAssets::getNumber,value);
        updateWrapper.set(FixedAssets::getIsOverdue,key);
        this.update(updateWrapper);
    }

    @Override
    public Page<FixedAssetsVO> materialPages(Page<FixedAssetsDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<FixedAssets> condition = new LambdaQueryWrapperX<>( FixedAssets. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(FixedAssets::getCreateTime);
        Page<FixedAssets> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        FixedAssetsDTO fixedAssetsDTO =   pageRequest.getQuery();
        if(Objects.nonNull(fixedAssetsDTO)){
            String jobId =fixedAssetsDTO.getJobId();
            if(StringUtils.hasText(jobId)){
                condition.leftJoin(JobMaterial.class,JobMaterial::getNumber,FixedAssets::getNumber);
                condition.eq(JobMaterial::getJobId,jobId);
                condition.isNull(JobMaterial::getNumber);
            }
        }
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), FixedAssets::new));

        PageResult<FixedAssets> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<FixedAssetsVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<FixedAssetsVO> vos = BeanCopyUtils.convertListTo(page.getContent(), FixedAssetsVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);


        return null;
    }
    @Override
    public Boolean uploadFixedAssets(FixedAssetsFileDTO fixedAssetsFileDTO) throws Exception {
        String dataId = fixedAssetsFileDTO.getDataId();
        List<FileDTO> fileDTOList = fixedAssetsFileDTO.getFileDTOList();
        if(!CollectionUtils.isEmpty(fileDTOList)){
            this.saveFile(fileDTOList,dataId,PMS_FIXED_ASSETS);
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean deleteFixedAssetsFile(List<String> fileIds) {
        return fileApiService.removeBatchByIds(fileIds);
    }

    public void saveFile(List<FileDTO> fileDTOList, String dataId, String dataType) throws Exception {
        fileDTOList.forEach(item->{
            item.setDataType(dataType);
            item.setDataId(dataId);
        });
        fileApiService.batchSaveFile(fileDTOList);
    }

    public static class FixedAssetsExcelListener extends AnalysisEventListener<FixedAssetsDTO> {

        private final List<FixedAssetsDTO> data = new ArrayList<>();

        @Override
        public void invoke(FixedAssetsDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<FixedAssetsDTO> getData() {
            return data;
        }
    }


}
