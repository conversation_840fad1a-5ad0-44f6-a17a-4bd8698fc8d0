<template>
  <div class="todo-list-wrap flex flex-ver">
    <div class="tabs-wrap">
      <div class="action">
        待办事项
      </div>
      <div>我发起的</div>
    </div>
    <div class="content flex-f1">
      <TodoListTable />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import TodoListTable from './TodoList/TodoListTable.vue';

export default defineComponent({
  name: 'TodoList',
  components: {
    TodoListTable,
  },
});
</script>

<style scoped lang="less">
  .todo-list-wrap {
    background: #fff;
    margin-top: 16px;
    height: 560px;
    padding: 0 18px 18px;
    box-sizing: border-box;
  }

  .tabs-wrap {
    display: flex;
    font-size: 18px;
    font-weight: bold;
    color: #969eb4;

    > div {
      padding: 20px 0 10px;
      margin-right: 44px;

      &.action {
        color: #444b5e;
      }

      &:not(.action) {
        cursor: pointer;

        &:hover {
          color: ~`getPrefixVar('primary-color')`;
        }
      }
    }
  }
</style>
