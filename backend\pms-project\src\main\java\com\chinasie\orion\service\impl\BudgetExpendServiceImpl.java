package com.chinasie.orion.service.impl;





import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.chinasie.orion.domain.dto.BudgetExpendDTO;
import com.chinasie.orion.domain.entity.BudgetApplication;
import com.chinasie.orion.domain.entity.BudgetExpend;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.vo.BudgetAdjustmentVO;
import com.chinasie.orion.domain.vo.BudgetApplicationVO;
import com.chinasie.orion.domain.vo.BudgetExpendVO;
import com.chinasie.orion.domain.vo.BudgetManagementVO;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.BudgetExpendMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.BudgetExpendService;
import com.chinasie.orion.service.BudgetManagementService;
import com.chinasie.orion.service.ProjectService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;




/**
 * <p>
 * BudgetExpend 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:07
 */
@Service
@Slf4j
public class BudgetExpendServiceImpl extends OrionBaseServiceImpl<BudgetExpendMapper, BudgetExpend> implements BudgetExpendService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private BudgetManagementService budgetManagementService;


    @Autowired
    private ProjectService projectService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public BudgetExpendVO detail(String id, String pageCode) throws Exception {
        BudgetExpend budgetExpend =this.getById(id);
        BudgetExpendVO result = BeanCopyUtils.convertTo(budgetExpend,BudgetExpendVO::new);
        setEveryName(Collections.singletonList(result));
        return result;
    }

    /**
     *  新增
     *
     * * @param budgetExpendDTO
     */
    @Override
    public  String create(BudgetExpendDTO budgetExpendDTO) throws Exception {
        BudgetExpend budgetExpend =BeanCopyUtils.convertTo(budgetExpendDTO,BudgetExpend::new);
        this.save(budgetExpend);
        String rsp=budgetExpend.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param budgetExpendDTO
     */
    @Override
    public Boolean edit(BudgetExpendDTO budgetExpendDTO) throws Exception {
        BudgetExpend budgetExpend =BeanCopyUtils.convertTo(budgetExpendDTO,BudgetExpend::new);

        this.updateById(budgetExpend);

        String rsp=budgetExpend.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        for (String id : ids) {




        }
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<BudgetExpendVO> pages( Page<BudgetExpendDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<BudgetExpend> condition = new LambdaQueryWrapperX<>( BudgetExpend. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        if (pageRequest.getQuery() != null && StringUtils.hasText(pageRequest.getQuery().getBudgetId())) {
            condition.eq(BudgetExpend::getBudgetId, pageRequest.getQuery().getBudgetId());
        }
        condition.orderByDesc(BudgetExpend::getCreateTime);


        Page<BudgetExpend> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), BudgetExpend::new));

        PageResult<BudgetExpend> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<BudgetExpendVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<BudgetExpendVO> vos = BeanCopyUtils.convertListTo(page.getContent(), BudgetExpendVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "项目支出导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", BudgetExpendDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            BudgetExpendExcelListener excelReadListener = new BudgetExpendExcelListener();
        EasyExcel.read(inputStream,BudgetExpendDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<BudgetExpendDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("项目支出导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<BudgetExpend> budgetExpendes =BeanCopyUtils.convertListTo(dtoS,BudgetExpend::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::BudgetExpend-import::id", importId, budgetExpendes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<BudgetExpend> budgetExpendes = (List<BudgetExpend>) orionJ2CacheService.get("pmsx::BudgetExpend-import::id", importId);
        log.info("项目支出导入的入库数据={}", JSONUtil.toJsonStr(budgetExpendes));

        this.saveBatch(budgetExpendes);
        orionJ2CacheService.delete("pmsx::BudgetExpend-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::BudgetExpend-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<BudgetExpend> condition = new LambdaQueryWrapperX<>( BudgetExpend. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(BudgetExpend::getCreateTime);
        List<BudgetExpend> budgetExpendes =   this.list(condition);

        List<BudgetExpendDTO> dtos = BeanCopyUtils.convertListTo(budgetExpendes, BudgetExpendDTO::new);

        String fileName = "项目支出数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", BudgetExpendDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<BudgetExpendVO> vos)throws Exception {
        String projectId = vos.get(0).getProjectId();
        Project project = projectService.getById(projectId);
        List<String> ids = vos.stream().map(BudgetExpendVO::getBudgetId).collect(Collectors.toList());
        List<BudgetManagementVO> managementVOS  = budgetManagementService.getudgetManagementVOList(ids);
        Map<String,BudgetManagementVO>  map = managementVOS.stream().collect(Collectors.toMap(BudgetManagementVO::getId,e->e));
        vos.forEach(vo->{
            if(ObjectUtil.isNotEmpty(map.get(vo.getBudgetId()))){
                BudgetManagementVO budgetManagementVO = map.get(vo.getBudgetId());
                vo.setName(budgetManagementVO.getName());
                vo.setNumber(budgetManagementVO.getNumber());
                vo.setCostCenterId(budgetManagementVO.getCostCenterId());
                vo.setCostCenterName(budgetManagementVO.getCostCenterName());
                vo.setExpenseSubjectName(budgetManagementVO.getExpenseSubjectName());
                vo.setExpenseSubjectNumber(budgetManagementVO.getExpenseSubjectNumber());
                vo.setTimeType(budgetManagementVO.getTimeType());
                vo.setTimeTypeName(budgetManagementVO.getTimeTypeName());
                vo.setCurrency(budgetManagementVO.getCurrency());
                vo.setCurrencyName(budgetManagementVO.getCurrencyName());
                vo.setBudgetTime(budgetManagementVO.getBudgetTime());
                vo.setBudgetObjectId(budgetManagementVO.getBudgetObjectId());
                vo.setBudgetObjectType(budgetManagementVO.getBudgetObjectType());
                vo.setBudgetObjectTypeName(budgetManagementVO.getBudgetObjectTypeName());
                if (ObjectUtil.isNotEmpty(project)) {
                    vo.setBudgetObjectName(project.getName());
                }
            }
        });

    }

    @Override
    public List<BudgetExpendVO> getList(String formId) throws Exception {
        List<BudgetExpend>  budgetApplications = this.list(new LambdaQueryWrapperX<>(BudgetExpend.class)
                .eq(BudgetExpend::getFormId,formId).orderByAsc(BudgetExpend::getSort));
        List<BudgetExpendVO> vos = BeanCopyUtils.convertListTo(budgetApplications, BudgetExpendVO::new);
        if(CollUtil.isEmpty(vos)){
            return vos;
        }
        setEveryName(vos);
        return vos;
    }


    public static class BudgetExpendExcelListener extends AnalysisEventListener<BudgetExpendDTO> {

        private final List<BudgetExpendDTO> data = new ArrayList<>();

        @Override
        public void invoke(BudgetExpendDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<BudgetExpendDTO> getData() {
            return data;
        }
    }


}
