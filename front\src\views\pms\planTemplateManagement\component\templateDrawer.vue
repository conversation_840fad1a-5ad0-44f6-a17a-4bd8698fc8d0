<template>
  <BasicDrawer
    v-bind="$attrs"
    :title="typeName+'模板名称'"
    placement="right"
    :closable="true"
    :width="1000"
    @register="modalRegister"
  >
    <div class="drawar-content">
      <div class="require-title red-star">
        名称：
      </div>
      <Input
        v-model:value="formData.templateName"
        class="content-textarea"
        :maxlength="250"
      />
    </div>
    <div class="drawar-content">
      <div class="require-title">
        排序：
      </div>
      <InputNumber
        v-model:value="formData.sort"
        class="content-textarea"
        :precision="0"
        :controls="false"
        :maxlength="10"
      />
    </div>
    <template #footer>
      <div class="flex-right">
        <BasicButton
          style="margin-right: 8px"
          @click="handleClose"
        >
          取消
        </BasicButton>
        <BasicButton
          type="primary"
          :loading="submitLoading"
          @click="handleSubmit"
        >
          确认
        </BasicButton>
      </div>
    </template>
  </BasicDrawer>
</template>
<script lang="ts" setup>
import { defineEmits, ref } from 'vue';
import { BasicButton, BasicDrawer, useDrawerInner } from 'lyra-component-vue3';
import { Input, message, InputNumber } from 'ant-design-vue';

import { postProjectSchemeMilestoneTemplate, putProjectSchemeMilestoneTemplate } from '../api';

const submitLoading = ref(false);
const [modalRegister, { closeDrawer }] = useDrawerInner(
  (drawerData) => {
    if (drawerData.type === 'add') {
      typeName.value = '添加';
      formData.value = drawerData;
    // templateId.value = drawerData.templateId;
    } else {
      typeName.value = '编辑';
      formData.value = drawerData;
    }
  },
);
const emit = defineEmits(['templateSuccess']);
const drawerName = ref<string>('');
const formData = ref<any>({});
const typeName = ref<string>('添加');
const handleClose = () => {
  closeDrawer();
};
async function handleSubmit() {
  if (!formData.value.templateName) {
    message.error('计划模板名称不能为空');
    return;
  }
  if (formData.value.type === 'add') {
    const result = await postProjectSchemeMilestoneTemplate({
      templateName: formData.value.templateName,
      sort: formData.value.sort,
    });
    if (result) {
      closeDrawer();
      formData.value.type = '';
      formData.value.templateName = '';
      formData.value.sort = '';
      message.success('操作成功');
      emit('templateSuccess', {
        status: true,
        type: 'add',
        name: formData.value.templateName,
      });
    }
  } else {
    const result = await putProjectSchemeMilestoneTemplate(formData.value);
    if (result) {
      closeDrawer();
      message.success('操作成功');
      emit('templateSuccess', {
        status: true,
        type: 'edit',
        name: formData.value.templateName,
      });
      formData.value.type = '';
      formData.value.templateName = '';
      formData.value.sort = '';
    }
  }
}

</script>

<style lang="less" scoped>
.drawar-content {
  padding:~`getPrefixVar('content-margin')`;
  .require-title {
    margin:0 0 ~`getPrefixVar('button-margin')` 0;
    font-weight: 700;
  }
  .red-star::before {
display: inline-block;
 margin-right: 4px
;
 color: #ff4d4f;
 font-size: 14px;
 font-family: SimSun,sans-serif;
 line-height: 1;
 content: "*";

  }
  .content-textarea {
    margin:0 0 ~`getPrefixVar('button-margin')` 0;
  }
  .link-area {
    width:600px;
    margin: ~`getPrefixVar('button-margin')` 0 0 0;
  }

}
.flex-content {
  .flex-box {
    display: flex;
    align-items: center;
    margin-top: 22px;
    > div {
      margin-right: 10px;
    }
    .number-box {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid rgba(217, 217, 217, 1);
      border-radius: 4px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.25);
    }
    .relation-box {
      box-sizing: border-box;
      width: 128px;
      height: 32px;
      box-sizing: border-box;
      border-width: 1px;
      border-style: solid;
      border-color: rgba(217, 217, 217, 1);
      border-radius: 4px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.25);
      height: 32px;
      display: flex;
      align-items: center;
      padding-left: 12px;
    }
    .select-box {
      width: 460px;
    }
  }
}
.flex-right {
  display: flex;
  justify-content: right;
}
</style>
