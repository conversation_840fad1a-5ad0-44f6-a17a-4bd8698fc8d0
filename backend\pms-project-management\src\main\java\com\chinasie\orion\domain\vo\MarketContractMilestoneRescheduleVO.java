package com.chinasie.orion.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;

import java.util.List;
/**
 * MarketContractMilestoneReschedule VO对象
 *
 * <AUTHOR>
 * @since 2024-05-30 02:39:54
 */
@ApiModel(value = "MarketContractMilestoneRescheduleVO对象", description = "市场合同里程碑改期信息")
@Data
public class MarketContractMilestoneRescheduleVO extends  ObjectVO   implements Serializable{

    /**
     * 变更原因
     */
    @ApiModelProperty(value = "变更原因")
    private String changeReason;


    /**
     * 里程碑id
     */
    @ApiModelProperty(value = "里程碑id")
    private String milestoneId;


    /**
     * 原预计验收日期
     */
    @ApiModelProperty(value = "原预计验收日期")
    private Date oldExpectAcceptDate;


    /**
     * 新预计验收日期
     */
    @ApiModelProperty(value = "新预计验收日期")
    private Date newExpectAcceptDate;
    /**
     * 初始预估验收日期
     */
    @ApiModelProperty(value = "初始预估验收日期")
    private Date expectAcceptDate;


    /**
     * 预估验收金额
     */
    @ApiModelProperty(value = "预估验收金额")
    private BigDecimal acceptMoney;


    /**
     * 预计开票日期
     */
    @ApiModelProperty(value = "预计开票日期")
    private Date billingDate;

    /**
     * 原预估验收金额
     */
    @ApiModelProperty(value = "原预估验收金额")
    @ExcelProperty(value = "预计开票日期 ", index = 7)
    private BigDecimal oldAcceptMoney;




}
