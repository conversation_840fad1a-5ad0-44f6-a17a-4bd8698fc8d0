package com.chinasie.orion.constant;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 21 日
 *
 **/
public class AmpereRingBoardConfigConstant {
    /**
     * 安质环考核指标 集团监控指标 最大 6
     */
    public static final int AMPERE_RING_BOARD_CONFIG_BLOC_KPI_MAX_SIZE=6;

    /**
     * 安质环集团考核指标的code
     */
    public static final String AMPERE_RING_BOARD_CONFIG_BLOC_KPI_CODE="group_kpi";

    /**
     * 安质环公司的管控指标 最大 6
     */
    public static final int AMPERE_RING_BOARD_CONFIG_COMPANY_CONTROL_KPI_MAX_SIZE=6;

    /**
     * 安质环公司管控指标的code
     */
    public static final String AMPERE_RING_BOARD_CONFIG_COMPANY_CONTROL_KPI_CODE="company_control_kpi";


    /**
     *安质环公司的监控的指标 最大 12
     */
    public static final int AMPERE_RING_BOARD_CONFIG_COMPANY_MONITORING_KPI_MAX_SIZE=12;

    /**
     * 安质环公司的监控的指标 的code
     */
    public static final String AMPERE_RING_BOARD_CONFIG_COMPANY_MONITORING_KPI_CODE="company_monitoring_kpi";

    /**
     * 上移
     */
    public static final String AMPERE_RING_BOARD_CONFIG_DEPT_OPERATION_UP="up";

    /**
     * 下移
     */
    public static final String AMPERE_RING_BOARD_CONFIG_DEPT_OPERATION_DOWN="down";

    /**
     * 置顶
     */
    public static final String AMPERE_RING_BOARD_CONFIG_DEPT_OPERATION_TOP="top";

    /**
     * 置底
     */
    public static final String AMPERE_RING_BOARD_CONFIG_DEPT_OPERATION_BOTTOM="bottom";

    /**
     * 安质环 隐患排查的维护类型
     */
    public static final String AMPERE_RING_CONFIG_TYPE_CHECK_PROBLEMS = "check_problems";

    /**
     * 安置好 安全生产考核维护类型
     */
    public static final String AMPERE_RING_CONFIG_TYPE_SAFETY_PRODUCTION="safety_production";

    /**
     * 安质环 统计部门绩效
     */
    public static final String AMPERE_RING_DEPT_SCORE_TOTAL_TYPE = "dept";

    /**
     * 安质环 统计项目部绩效
     */
    public static final String AMPERE_RING_PROJECT_SCORE_TOTAL_TYPE = "project";
}
