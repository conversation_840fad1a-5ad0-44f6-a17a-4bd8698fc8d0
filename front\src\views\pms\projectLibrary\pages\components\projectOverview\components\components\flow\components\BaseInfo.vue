<template>
  <div>
    <BasicCard title="流程说明">
      <div>
        <div
          :class="{ content: true, expanded: isExpanded }"
          @click="toggleExpand"
        >
          {{ props.contentText || '暂无数据' }}
        </div>
        <div v-if="props.contentText">
          <p
            v-if="!isExpanded"
            class="more"
            @click="toggleExpand"
          >
            展开
          </p>
          <p
            v-else
            class="more"
            @click="toggleExpand"
          >
            收起
          </p>
        </div>
      </div>
    </BasicCard>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { BasicCard } from 'lyra-component-vue3';
const props = defineProps({
  contentText: String,
});
const isExpanded = ref(false);

const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};
</script>

<style scoped>
.content {
  overflow: hidden;
  max-height: 100px; /* 设定最大高度 */
  transition: max-height 0.3s ease;
}

.expanded {
  max-height: 500px; /* 足够大，可以容纳所有内容 */
}
.more{
  color: #0960bd;
  cursor: pointer;
}
</style>
