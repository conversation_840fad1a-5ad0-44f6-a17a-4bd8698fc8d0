package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.chinasie.orion.conts.ProjectNodeTypeEnum;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.vo.ContractMilestoneApiVO;
import com.chinasie.orion.domain.vo.ProjectSchemeApiVO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.service.ProjectSchemeApi2Service;
import com.chinasie.orion.service.ProjectSchemeService;
import com.chinasie.orion.service.ProjectService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/8/20
 */
@Service
@Slf4j
public class ProjectSchemeApi2ServiceImpl implements ProjectSchemeApi2Service {

    @Autowired
    private ProjectSchemeService projectSchemeService;
    @Autowired
    private ProjectService projectService;
    @Resource
    private UserRedisHelper userRedisHelper;
    @Resource
    private DeptRedisHelper deptRedisHelper;

    @Override
    public void createPlan(List<ContractMilestoneApiVO> contractMilestoneVOS) {
        if(CollectionUtils.isNotEmpty(contractMilestoneVOS)){
            //查找对应项目信息
            List<String> projectIds = contractMilestoneVOS.stream().map(ContractMilestoneApiVO::getProjectId).distinct().collect(Collectors.toList());
            Map<String, List<ContractMilestoneApiVO>> contractMilestoneMap = contractMilestoneVOS.stream().collect(Collectors.groupingBy(ContractMilestoneApiVO::getProjectId));

            List<Project> projectList = projectService.list(new LambdaQueryWrapper<Project>().in(Project::getId, projectIds));
            Map<String, List<Project>> projectMap = projectList.stream().collect(Collectors.groupingBy(Project::getId));

            List<ContractMilestoneApiVO> updateContractMilestoneVOS = Lists.newArrayList();
            for (String key : contractMilestoneMap.keySet()) {
                List<ProjectScheme> projectSchemes = Lists.newArrayList();
                List<ProjectScheme> childrenSchemes = Lists.newArrayList();
                List<ContractMilestoneApiVO> contractMilestoneApiVOS = contractMilestoneMap.get(key);
                if (projectMap.get(key) == null) {
                    continue;
                }
                Project project = projectMap.get(key).get(0);

                LambdaQueryWrapperX<ProjectScheme> lambdaQueryWrapper = new LambdaQueryWrapperX<>();
                lambdaQueryWrapper.select("max(sort) as sort");
                lambdaQueryWrapper.eq(ProjectScheme::getProjectId, project.getId());
                ProjectScheme one = projectSchemeService.getOne(lambdaQueryWrapper);
                Long maxSort = 0L;
                if(Objects.nonNull(one)){
                    maxSort = one.getSort();
                }

                long count = 1;
                for (int i = 0; i < contractMilestoneApiVOS.size(); i++) {
                    ProjectScheme projectScheme = new ProjectScheme();

                    projectScheme.setName(contractMilestoneApiVOS.get(i).getMilestoneName());
                    projectScheme.setProjectId(key);
                    projectScheme.setProjectNumber(project.getNumber());
                    projectScheme.setLevel(1);
                    projectScheme.setParentId("0");
                    projectScheme.setParentChain("0");
                    projectScheme.setNodeType(ProjectNodeTypeEnum.MILESTONE.getCode());
                    projectScheme.setRspUser(contractMilestoneApiVOS.get(i).getTechRspUser());
                    projectScheme.setCircumstance(0);
                    if(StringUtils.isNotEmpty(contractMilestoneApiVOS.get(i).getTechRspUser())){
                        UserVO userVO = userRedisHelper.getUserById(CurrentUserHelper.getOrgId(), contractMilestoneApiVOS.get(i).getTechRspUser());
                        setOrgInfo(projectScheme, userVO.getOrganizations());
                        projectScheme.setRspUserCode(userVO.getCode());
                    }
                    projectScheme.setBeginTime(contractMilestoneApiVOS.get(i).getCreateTime());
                    projectScheme.setEndTime(contractMilestoneApiVOS.get(i).getPlanAcceptDate());
                    projectScheme.setSchemeDesc(contractMilestoneApiVOS.get(i).getDescription());
                    projectScheme.setTopSort(0);
                    projectScheme.setIsWork(0);
                    projectScheme.setClassName("ProjectScheme");
                    projectScheme.setCreatorId(contractMilestoneApiVOS.get(i).getTechRspUser());
                    projectScheme.setOwnerId(contractMilestoneApiVOS.get(i).getTechRspUser());
                    projectScheme.setCreateTime(new Date());
                    projectScheme.setModifyId(contractMilestoneApiVOS.get(i).getTechRspUser());
                    projectScheme.setModifyTime(new Date());
                    projectScheme.setRemark(contractMilestoneApiVOS.get(i).getRemark());
                    projectScheme.setPlatformId(contractMilestoneApiVOS.get(i).getPlatformId());
                    projectScheme.setOrgId(contractMilestoneApiVOS.get(i).getOrgId());
                    projectScheme.setStatus(Status.PENDING.getCode());
                    projectScheme.setNumber(projectScheme.getSchemeNumber());
                    projectScheme.setContractMilestoneId(contractMilestoneApiVOS.get(i).getId());
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(contractMilestoneApiVOS.get(i).getParentId())) {
                        projectScheme.setParentId(contractMilestoneApiVOS.get(i).getParentId());
                        childrenSchemes.add(projectScheme);
                    } else {
                        projectScheme.setSort(maxSort + count);
                        count++;
                        projectScheme.setSchemeNumber(project.getNumber() + "-" + projectScheme.getSort());
                        projectSchemes.add(projectScheme);
                    }
                }
                projectSchemeService.saveBatch(projectSchemes);
                if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(childrenSchemes) && com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(projectSchemes)) {
                    insertChildrenScheme(projectSchemes, childrenSchemes, 2, 1);
                }

                updateContractMilestoneVOS.addAll(contractMilestoneVOS);
            }
        }
    }
    void insertChildrenScheme(List<ProjectScheme> projectSchemes,List<ProjectScheme> childrenSchemes,int level,long sort){
        List<ProjectScheme> resultSchemes = Lists.newArrayList();
        Map<String,List<ProjectScheme>> projectSchemeMap = projectSchemes.stream().collect(Collectors.groupingBy(ProjectScheme::getContractMilestoneId));
        List<ProjectScheme> insertChildSchemes = Lists.newArrayList();
        if(!com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(childrenSchemes)){
            for(ProjectScheme child :childrenSchemes){
                if(!com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(projectSchemeMap.get(child.getParentId()))){
                    String oldParentId = child.getParentId();
                    child.setParentId(projectSchemeMap.get(oldParentId).get(0).getId());
                    child.setParentChain(projectSchemeMap.get(oldParentId).get(0).getParentChain()+","+child.getParentId());
                    child.setLevel(level);
                    child.setSort(sort++);
                    child.setSchemeNumber(projectSchemeMap.get(oldParentId).get(0).getNumber()+"-"+child.getSort());
                    projectSchemeService.save(child);
                    insertChildSchemes.add(child);
                }else{
                    resultSchemes.add(child);
                }
            }
            if(com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(resultSchemes) && com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(insertChildSchemes)){
                insertChildrenScheme(insertChildSchemes,resultSchemes,level+1,1);
            }
        }
    }


    @Override
    public ProjectSchemeApiVO findById(String projectSchemeId) {
        return projectSchemeService.getBySchemeIdApi(projectSchemeId);
    }

    private void setOrgInfo(ProjectScheme p, List<DeptVO> orgs) {
        if (CollUtil.isEmpty(orgs)) {
            return;
        }
        DeptVO deptVO = orgs.get(0);
        if ("20".equals(deptVO.getType())) {
            p.setRspSubDept(deptVO.getId());
        } else if ("30".equals(deptVO.getType())) {
            DeptVO deptOrg = getParentOrgId(deptVO);
            p.setRspSubDept(deptOrg.getId());
        } else if ("40".equals(deptVO.getType())) {
            DeptVO sectionOrg = getParentOrgId(deptVO);
            DeptVO deptOrg = getParentOrgId(sectionOrg);
            p.setRspSubDept(deptOrg.getId());
            p.setRspSectionId(sectionOrg.getId());
        }else{
            p.setRspSubDept(deptVO.getId());
        }

    }
    private DeptVO getParentOrgId(DeptVO org) {
        String parentId = org.getParentId();
        DeptVO organization = deptRedisHelper.getDeptById(parentId);
        return Objects.nonNull(organization) ? organization : new DeptVO();
    }
}
