package com.chinasie.orion.service;

import com.chinasie.orion.domain.vo.ProjectRoleVO;
import com.chinasie.orion.domain.vo.statics.BatchProjectUserVO;
import com.chinasie.orion.sdk.core.conf.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: lsy
 * @date: 2024/6/4
 * @description:
 */
@FeignClient(name = "pms", configuration = FeignConfig.class)
@Lazy
public interface ProjectRoleApiService {

    String API_PREFIX = "/api-pms/projectRole";

    /**
     * 获取项目角色信息
     *
     * @param roleIdList
     * @return
     * @throws Exception
     */
    @PostMapping(value = API_PREFIX + "/list")
    List<ProjectRoleVO> getProjectRoleVO(@RequestBody List<String> roleIdList) throws Exception;

    /**
     * 获取项目成员信息
     *
     * @param projectIds
     * @return
     * @throws Exception
     */
    @PostMapping(value = API_PREFIX + "/list/user")
    List<BatchProjectUserVO> getProjectUserBatch(@RequestBody List<String> projectIds) throws Exception;

}
