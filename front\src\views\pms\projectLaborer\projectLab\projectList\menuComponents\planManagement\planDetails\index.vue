<template>
  <Layout3
    v-if="tabsOption?.length>0"
    :defaultActionId="actionId"
    :menuData="tabsOption"
    :projectData="projectInfo"
    :type="2"
    @menuChange="contentTabsChange"
  >
    <template #header-title>
      <div class="layoutTtitle">
        <div class="nameStyle flex-te">
          {{ projectInfo.name }}
        </div>
        <div class="numberStyle">
          {{ projectInfo?.number }}
        </div>
      </div>
    </template>
    <template #header-info>
      <div class="headerStatus">
        <div class="headerStatus_top">
          <DataStatusTag
            :statusData="projectInfo.dataStatus"
          />
        </div>
        <div class="headerStatus_bottom color-second fz12 lh12">
          {{ projectInfo.resUserName }}
        </div>
      </div>
    </template>
    <div class="content_tabs">
      <Summarize
        v-if="actionId===2222221 && isPower('RWX_container_02', powerData)"
        :formId="formId"
      />
      <FrontPlan
        v-if="actionId===2222222 && isPower('RWX_container_03', powerData)"
        :formId="formId"
        @checkDetails="checkData($event,'plan')"
      />
      <Deliverable
        v-if="actionId===2222223 && isPower('RWX_container_04', powerData)"
        :formId="formId"
        :projectId="projectInfo.projectId"
        @checkDetails="checkData($event,'deliverale')"
      />
      <!--    <Allocating v-if="actionId===2222224 && isPower('RWX_container_05', powerData)" />-->
      <ManHour
        v-if="actionId===2222225 && isPower('RWX_container_06', powerData)"
        :formId="formId"
      />
      <RelatedContent
        v-if="actionId===2222226 && isPower('RWX_container_07', powerData)"
        :formId="formId"
        :projectId="projectInfo.projectId"
      />
      <!--    <Log v-if="tabsIndex === 6" />-->
      <PushModel />

      <Process
        v-if="actionId===2222230"
      />
      <ChildTask v-if="actionId===2222231" />

      <PlanManagementModal @register="registerDetails" />
      <DeliverableModal @register="registerDetailsDeliverable" />
    </div>
  </Layout3>
</template>

<script>
import {
  Layout3, useProjectPower, isPower, useDrawer, DataStatusTag,
} from 'lyra-component-vue3';
import {
  onMounted, reactive, toRefs, getCurrentInstance, provide, computed,
} from 'vue';
import { useRoute } from 'vue-router';
import Summarize from './summarize/index.vue';
import FrontPlan from './frontPlan/index.vue';
import Deliverable from './deliverable/index.vue';
import Allocating from './allocating/index.vue';
import ManHour from './manHour/index.vue';
import RelatedContent from './relatedContent/index.vue';
import PushModel from '/@/views/pms/projectLaborer/pushModel/index.vue';
import PlanManagementModal from '/@/views/pms/projectLaborer/projectLab/projectList/modal/PlanManagementModal.vue';
import DeliverableModal from '/@/views/pms/projectLaborer/projectLab/projectList/modal/DeliverableModal.vue';
import Api from '/@/api';
import Process from './flow/index.vue';
import ChildTask from './childTask/index.vue';
import { setTitleByRootTabsKey } from '/@/utils';

export default {
  name: 'Index',
  components: {
    Layout3,
    PushModel,
    Summarize,
    FrontPlan,
    Deliverable,
    ManHour,
    RelatedContent,
    PlanManagementModal,
    DeliverableModal,
    Process,
    DataStatusTag,
    ChildTask,
  },
  setup() {
    const [registerDetails, { openDrawer: openDetailsDrawer }] = useDrawer();
    const [registerDetailsDeliverable, { openDrawer: openDetailsDeliverable }] = useDrawer();
    const route = useRoute();
    const state = reactive({
      tabsIndex: 0,
      projectInfo: {},
      powerData: [],
      actionId: 2222221,
      formId: route.query.id,
      // tabsOption: [
      //   { name: '概述' }, // 0
      //   { name: '前置计划' }, // 1
      //   { name: '交付物' }, // 2
      //   { name: '分配资源' }, // 3
      //   { name: '工时' }, // 4
      //   { name: '关联内容' }, // 5
      //   // { name: '日志' }, // 6
      // ],
    });
    const state6 = reactive({
      tabsOption: [],
    });
    const internalInstance = getCurrentInstance();
    // 获取权限
    async function getProjectPower() {
      return new Promise((resolve, reject) => {
        useProjectPower(
          { pageCode: 'PMS0027' },
          (powerList) => {
            resolve(powerList || []);
          },
          internalInstance,
        );
      });
    }
    function getForm() {
      new Api('/pms')
        .fetch('', `plan/${state.formId}`, 'GET')
        .then((res) => {
          state.projectInfo = res;
          setTitleByRootTabsKey(route?.query?.rootTabsKey, res.name);
        })
        .catch((_) => {
        });
    }
    provide('formData', computed(() => state.projectInfo));

    provide(
      'getFormData',
      computed(() => getForm),
    );
    onMounted(async () => {
      state.powerData = await getProjectPower();
      isPower('RWX_container_02', state.powerData) && state6.tabsOption.push({
        name: '概述',
        id: 2222221,
      });
      isPower('RWX_container_03', state.powerData) && state6.tabsOption.push({
        name: '前置计划',
        id: 2222222,
      });
      isPower('RWX_container_04', state.powerData) && state6.tabsOption.push({
        name: '交付物',
        id: 2222223,
      });
      // isPower('RWX_container_05', state.powerData) && state6.tabsOption.push({ name: '分配资源', id: 2222224 });
      isPower('RWX_container_06', state.powerData) && state6.tabsOption.push({
        name: '工时',
        id: 2222225,
      });
      isPower('RWX_container_07', state.powerData) && state6.tabsOption.push({
        name: '关联内容',
        id: 2222226,
      });
      state6.tabsOption.push({
        name: '子任务',
        id: 2222231,
      });
      state6.tabsOption.push({
        name: '流程',
        id: 2222230,
      });
      await getForm();
    });
    function contentTabsChange(index) {
      // state.tabsIndex = index.id;
      state.actionId = index.id;
    }
    // 权限分发
    provide(
      'powerData',
      computed(() => state.powerData),
    );
    const checkData = (id, type) => {
      if (type === 'plan') {
        openDetailsDrawer(true, { id });
      } else {
        openDetailsDeliverable(true, { id });
      }
    };
    return {
      ...toRefs(state),
      ...toRefs(state6),
      contentTabsChange,
      isPower,
      checkData,
      registerDetails,
      registerDetailsDeliverable,
    };
  },
};
</script>

<style scoped lang="less">
.content_tabs{
  display: flex;
  height: 100%;
}
.headerStatus{
  padding-right: 10px;
  width: 160px;
  .color-second{
    color: #969eb4;
    margin-top: 5px !important;
    line-height: 1.2em;
  }
}
.layoutTtitle{
  width: 350px;
  padding: 5px ~`getPrefixVar('content-padding-left')`;
  .nameStyle{
    font-weight: 400;
    font-style: normal;
    color: #444B5E;
    font-size: 18px;
    height:29px;
    line-height: 29px;
  }
  .numberStyle{
    font-size: 12px;
    color: #969EB4;
  }
}
</style>
