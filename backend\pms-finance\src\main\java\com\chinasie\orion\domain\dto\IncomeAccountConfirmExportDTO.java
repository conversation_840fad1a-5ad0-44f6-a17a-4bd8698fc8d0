package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * IncomeAccountConfirm DTO对象
 *
 * <AUTHOR>
 * @since 2024-12-18 21:16:26
 */
@ApiModel(value = "IncomeAccountConfirmDTO对象", description = "收入记账明细确认表")
@Data
@ExcelIgnoreUnannotated
public class IncomeAccountConfirmExportDTO extends  ObjectDTO   implements Serializable{

    /**
     * 凭证类型
     */
    @ApiModelProperty(value = "凭证类型")
    @ExcelProperty(value = "凭证类型 ", index = 0)
    private String voucherTypeName;

    /**
     * 是否调账凭证
     */
    @ApiModelProperty(value = "是否调账凭证")
    @ExcelProperty(value = "是否调账凭证 ", index = 1)
    private String adjAccountVoucherName;

    @ApiModelProperty(value = "收入计划编号")
    @ExcelProperty(value = "收入计划编号 ", index = 2)
    private String incomePlanNum;

    @ApiModelProperty(value = "确认状态")
    @ExcelProperty(value = "确认状态 ", index = 3)
    private String confirmStatusName;

    /**
     * 公司代码
     */
    @ApiModelProperty(value = "公司代码")
    @ExcelProperty(value = "公司代码 ", index = 4)
    private String companyCode;

    /**
     * 科目
     */
    @ApiModelProperty(value = "科目")
    @ExcelProperty(value = "科目 ", index = 5)
    private String subject;

    /**
     * 分配编码
     */
    @ApiModelProperty(value = "分配编码")
    @ExcelProperty(value = "分配 ", index = 6)
    private String distributiveCode;


    /**
     * 过账期间
     */
    @ApiModelProperty(value = "过账期间")
    @ExcelProperty(value = "过账期间 ", index = 7)
    private String postingPeriod;



    /**
     * 凭证编号
     */
    @ApiModelProperty(value = "凭证编号")
    @ExcelProperty(value = "凭证编号 ", index = 8)
    private String voucherNum;

    /**
     * 凭证日期
     */
    @ApiModelProperty(value = "凭证日期")
    @ExcelProperty(value = "凭证日期 ", index = 9)
    private Date voucherDate;


    /**
     * 过账期间
     */
    @ApiModelProperty(value = "过账日期")
    @ExcelProperty(value = "过账日期 ", index = 10)
    private Date postingDate;

    /**
     * 本币金额
     */
    @ApiModelProperty(value = "本币金额")
    @ExcelProperty(value = "本币金额 ", index = 11)
    private BigDecimal localCurrencyAmt;

    /**
     * 利润中心
     */
    @ApiModelProperty(value = "利润中心")
    @ExcelProperty(value = "利润中心 ", index = 12)
    private String profitCenter;

    /**
     * 成本中心
     */
    @ApiModelProperty(value = "成本中心")
    @ExcelProperty(value = "成本中心 ", index = 13)
    private String costCenter;

    @ApiModelProperty(value = "订单")
    @ExcelProperty(value = "订单 ", index = 14)
    private String orderForm;

    @ApiModelProperty(value = "WBS要素")
    @ExcelProperty(value = "WBS要素 ", index = 15)
    private String wbsElement;

    /**
     * 文本
     */
    @ApiModelProperty(value = "文本")
    @ExcelProperty(value = "文本 ", index = 16)
    private String text;


    @ApiModelProperty(value = "Tr.prt")
    @ExcelProperty(value = "Tr.prt ", index = 17)
    private String trprt;

    @ApiModelProperty(value = "付款基准日期")
    @ExcelProperty(value = "付款基准日期 ", index = 18)
    private String payReferenceDate;





}
