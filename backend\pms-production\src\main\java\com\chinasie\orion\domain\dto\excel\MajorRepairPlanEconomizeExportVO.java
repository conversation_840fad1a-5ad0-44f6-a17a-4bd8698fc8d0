package com.chinasie.orion.domain.dto.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/09/28/14:14
 * @description:
 */
@Data
public class MajorRepairPlanEconomizeExportVO implements Serializable {

    @ExcelProperty(value = "序号")
    private Integer sort;

    @ExcelProperty(value = "中心名称")
    @ColumnWidth(25)
    private String  jobManageCenter;

    @ExcelProperty(value = "工单号")
    @ColumnWidth(25)
    private String  jobManageNumber;

    @ExcelProperty(value = "工作名称")
    @ColumnWidth(25)
    private String  jobManageName;

    @ExcelProperty(value = "大修轮次")
    @ColumnWidth(25)
    private String majorRepairTurn;

    /**
     * 是否重大项目
     */
    @ExcelProperty(value = "是否重大项目")
    @ColumnWidth(20)
    private String isMajorProject;

    /**
     * 实际结束时间
     */
    @ExcelProperty(value = "实际结束时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @ColumnWidth(20)
    private Date actualEndTime;

    @ExcelProperty(value = "大修状态")
    @ColumnWidth(25)
    private String majorRepairStatusName;

    /**
     * 关键路径是否节约
     */
    @ExcelProperty(value = "关键路径是否节约")
    @ColumnWidth(25)
    private String isEconomize;

    @ExcelProperty(value = "是否可沿用")
    @ColumnWidth(20)
    private String isContinueUse;

    /**
     * 编号
     */
    @ExcelProperty(value = "编号")
    @ColumnWidth(20)
    private String number;

    @ExcelProperty(value = "优化领域")
    @ColumnWidth(20)
    private String optimizeFieldName;

    @ExcelProperty(value = "大修类型")
    @ColumnWidth(20)
    private String majorRepairTypeName;

    @ExcelProperty(value = "应用机组类型")
    @ColumnWidth(25)
    private String applicationCrewName;

    @ExcelProperty(value = "创优技术或工作")
    @ColumnWidth(25)
    private String innTechOrWork;

    @ExcelProperty(value = "计划工期(H)")
    private BigDecimal planDuration;

    @ExcelProperty(value = "实际执行用时(H)")
    private BigDecimal actualExeDuration;
    @ExcelProperty(value = "节约(H)")
    private BigDecimal economizeDuration;
    @ExcelProperty(value = "内容介绍")
    private String content;

    @ExcelProperty(value = "延误原因")
    private String delayReason;

    @ExcelProperty(value = "业务状态")
    private String statusName;

    @ExcelIgnore
    private long timestamp;

    /**
     *  序号、中心名称、工单号、工作名称、大修轮次、是否重大项目
     *  、实际结束时间、大修状态、关键路径是否节约、是否可沿用、编号
     *  、优化领域、大修类型、应用机组类型、创优技术或工作、计划工期
     *  、实际执行用时（H）、节约（H）、内容介绍、延误原因、业务状态
     */
}
