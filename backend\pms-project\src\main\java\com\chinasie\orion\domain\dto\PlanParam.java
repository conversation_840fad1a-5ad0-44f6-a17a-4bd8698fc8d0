package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/18/17:27
 * @description:
 */
@Data
public class PlanParam extends PlanQueryDTO{
    @ApiModelProperty("关键词")
    private String keyWord;
    @ApiModelProperty("计划Id")
    @NotEmpty(message = "所属计划不能为空")
    private String planId;

    @ApiModelProperty("前后置类型 1-前置 2-后置")
    private Integer type;
}
