package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "AnalysisVO对象", description = "影响分析")
public class AnalysisVO implements Serializable {

    /**
     * 关系ID
     */
    @ApiModelProperty(value = "关系ID")
    private String id;
    /**
     * 父级关系ID
     */
    @ApiModelProperty(value = "父级关系ID")
    private String parentId;

    /**
     * 子级
     */
    @ApiModelProperty(value = "子级")
    private List<AnalysisVO> children;

    /**
     * 是否影响
     */
    @ApiModelProperty(value = "是否影响")
    private Integer influenceFlag;


    /**
     * 影响结果
     */
    @ApiModelProperty(value = "影响结果")
    private String influence;


    /**
     * 是否通知
     */
    @ApiModelProperty(value = "是否通知")
    private Integer noteFlag;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;


    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    private String number;


    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private String revId;


    /**
     * 数据类型
     */
    @ApiModelProperty(value = "数据类型")
    private String dataType;


    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    private String ownerId;
    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    private String owner;

    /**
     * 修改日期
     */
    @ApiModelProperty(value = "修改日期")
    private Date modifyTime;


    /**
     * 数据类型名称
     */
    @ApiModelProperty(value = "数据类型名称")
    private String dataTypeName;

    /**
     * 实体ID
     */
    @ApiModelProperty(value = "实体ID")
    private String relationId;


    public AnalysisVO(String id, String parentId, String relationId) {
        this.id = id;
        this.parentId = parentId;
        this.relationId = relationId;
    }
}
