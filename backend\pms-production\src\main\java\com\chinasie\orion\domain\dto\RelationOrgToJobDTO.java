package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * RelationOrgToJob DTO对象
 *
 * <AUTHOR>
 * @since 2024-11-18 09:32:56
 */
@ApiModel(value = "RelationOrgToJobDTO对象", description = "关系-大修组织工单关系")
@Data
@ExcelIgnoreUnannotated
public class RelationOrgToJobDTO extends  ObjectDTO   implements Serializable{

    /**
     * 大修组织ID
     */
    @ApiModelProperty(value = "大修组织ID")
    @ExcelProperty(value = "大修组织ID ", index = 0)
    private String repairOrgId;

    /**
     * 作业工单号
     */
    @ApiModelProperty(value = "作业工单号")
    @ExcelProperty(value = "作业工单号 ", index = 1)
    private String jobNumber;




}
