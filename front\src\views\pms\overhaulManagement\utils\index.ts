import { openDrawer } from 'lyra-component-vue3';
import {
  h, inject, ref, Ref,
} from 'vue';
import Api from '/@/api';
import DrawerForm from '../components/operation/DrawerForm.vue';
import PersonnelInAndOut from '../components/PersonnelInAndOut.vue';
import PersonnelDetails from '../components/PersonnelDetails.vue';
import MaterialAddAndInAndOut from '/@/views/pms/overhaulManagement/components/MaterialAddAndInAndOut.vue';

// 新增编辑大修、日常作业
export function openOverhaulDailyForm(record: any, cb?: () => void) {
  const drawerRef: Ref = ref();
  const isOk = ref<boolean>(false);
  const cancelId = ref<string>('');
  openDrawer({
    title: record?.id ? '编辑作业' : '新增作业',
    width: 1000,
    content() {
      return h(DrawerForm, {
        ref: drawerRef,
        record,
        onUpdateCancelId(value: string) {
          cancelId.value = value;
        },
      });
    },
    async onOk() {
      isOk.value = true;
      await drawerRef.value.submit();
      cb?.();
    },
    async onCloseEvent() {
      if (isOk.value === false) {
        cancelId.value && await new Api(`/pms/job-manage/save/cancel/${cancelId.value}`).fetch('', '', 'PUT');
      }
    },
  });
}

// 人员进场、人员离场
export interface InAndOutSiteProps {
    // 操作类型
    operationType: 'in' | 'out'
    // 人员id
    id: string
    // 作业id
    jobId?: string
    // 员工号
    userCode: string
}

export function openInAndOutSite(record: InAndOutSiteProps, cb?: () => void) {
  const drawerRef: Ref = ref();
  const okDisabled: Ref<boolean> = ref(false);
  openDrawer({
    title: record.operationType === 'in' ? '人员进场' : record.operationType === 'out' ? '人员离场' : '',
    width: 1000,
    content() {
      return h(PersonnelInAndOut, {
        ref: drawerRef,
        record,
        onUpdateButton(value) {
          okDisabled.value = value;
        },
      });
    },
    footer: {
      okDisabled,
    },
    async onOk() {
      await drawerRef.value.submit();
      cb?.();
    },
  });
}

// 人员详情
export function openPersonnelDetailsDrawer(record) {
  const drawerRef: Ref = ref();

  openDrawer({
    title: '人员详情',
    width: 1000,
    content() {
      return h(PersonnelDetails, {
        ref: drawerRef,
        record,
      });
    },
    footer: {
      isOk: false,
      // @ts-ignore
      cancelText: '关闭',
    },
  });
}

// 物资选择、进场、离场
export interface MaterialDrawerRecord {
    operationType: 'pick' | 'in' | 'out' | 'plan-pick'
    jobId?: string
    id?: string

    [propName: string]: any
}

export function openMaterialDrawer(record: MaterialDrawerRecord, cb?: () => void) {
  const drawerRef: Ref = ref();
  let title = '';
  switch (record.operationType) {
    case 'pick':
    case 'plan-pick':
      title = '选择物资';
      break;
    case 'in':
      title = '物资进场';
      break;
    case 'out':
      title = '物资离场';
      break;
  }
  openDrawer({
    title,
    width: 1000,
    content() {
      return h(MaterialAddAndInAndOut, {
        ref: drawerRef,
        record,
      });
    },
    async onOk() {
      await drawerRef.value.submit();
      cb?.();
    },
  });
}
