package com.chinasie.orion.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.domain.dto.pas.TypeAttrValueDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/17/16:29
 * @description:
 */
@Data
@ApiModel(value = "RiskManagementDTO对象", description = "危险管理")
public class RiskManagementDTO extends ObjectDTO {

    /**
     * 预计完成时间
     */
    @ApiModelProperty(value = "预计完成时间")
    private Date predictEndTime;

    /**
     *
     */
    private String id;

    /**
     * 应对策略
     */
    @ApiModelProperty(value = "应对策略")
    private String copingStrategy;

    /**
     * 风险类型
     */
    @ApiModelProperty(value = "风险类型")
    private String riskType;

    /**
     * 项目id
     */
    @NotEmpty(message = "项目id不能为空")
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 应对措施
     */
    @Size(max = 100, message = "应对措施过长，建议控制在100字符以内")
    @ApiModelProperty(value = "应对措施")
    private String solutions;

    /**
     * 影响程度
     */
    @ApiModelProperty(value = "影响程度")
    private String riskInfluence;

    /**
     * 负责人ID
     */
    @ApiModelProperty(value = "负责人ID")
    private String principalId;

    /**
     * 风险概率
     */
    @ApiModelProperty(value = "风险概率")
    private String riskProbability;

    /**
     * 预期发生时间
     */
    @ApiModelProperty(value = "预期发生时间")
    private String predictStartTime;

    /**
     * 识别人
     */
    @ApiModelProperty(value = "识别人")
    private String discernPerson;
    @ApiModelProperty(value = "识别人名称")
    private String discernPersonName;
    @ApiModelProperty("属性值")
    private List<TypeAttrValueDTO> typeAttrValueDTOList;

    @ApiModelProperty(value = "文档ID")
    private String documentId;

    @ApiModelProperty(value = "是否需要审批")
    private Boolean isNeedApproval;

    @ApiModelProperty(value = "是否需要提醒")
    private Boolean isNeedReminder;

    @ApiModelProperty(value = "是否典型风险")
    private Boolean isTypicalRisk;

}
