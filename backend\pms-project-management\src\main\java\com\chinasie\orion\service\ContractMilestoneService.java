package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.ContractMilestoneDTO;
import com.chinasie.orion.domain.dto.ContractMilestoneTreeDTO;
import com.chinasie.orion.domain.dto.MarketContractDTO;
import com.chinasie.orion.domain.entity.ContractMilestone;
import com.chinasie.orion.domain.entity.ContractOurSignedSubject;
import com.chinasie.orion.domain.tree.MileStoneTreeVo;
import com.chinasie.orion.domain.vo.ContractMilestoneTreeTotalVO;
import com.chinasie.orion.domain.vo.ContractMilestoneVO;
import com.chinasie.orion.domain.vo.NodeVO;
import com.chinasie.orion.domain.vo.TreeNodeVO;
import com.chinasie.orion.domain.vo.tree.RelationOrgJobImplVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.entity.CustomerInfo;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.org.SimpleDeptVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * ContractMilestone 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-29 15:01:52
 */
public interface ContractMilestoneService  extends  OrionBaseService<ContractMilestone>  {


    /**
     * 详情
     * <p>
     * * @param id
     */
    ContractMilestoneVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param contractMilestoneDTO
     */
    String create(ContractMilestoneDTO contractMilestoneDTO);

    /**
     *  查询附件
     *
     * * @param dataId
     */
    List<Map<String,Object>> queryFile(String dataId)throws Exception;

    /**
     *  编辑
     *
     * * @param contractMilestoneDTO
     */
    Boolean edit(ContractMilestoneDTO contractMilestoneDTO)throws Exception;


    /**
     *  关闭里程碑
     *
     * * @param id
     */
    Boolean close(String id)throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids);


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<ContractMilestoneVO> pages( Page<ContractMilestoneDTO> pageRequest)throws Exception;

    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<ContractMilestoneVO> relationPages( Page<ContractMilestoneDTO> pageRequest)throws Exception;

    /**
     *  树
     *
     *
     */
    ContractMilestoneTreeTotalVO tree(ContractMilestoneTreeDTO contractMilestoneTreeDTO)throws Exception;

    /**
     *  下载模板
     *
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response)throws Exception;

    /**
     * 导入校验
     * <p>
     *
     * @param file       excel文件
     * @param contractId 导入到哪个合同主数据里
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file, String contractId) throws Exception;

    /**
     * 导入excel
     *
     * @param importId 导入校验后，生成的流水号
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     *  取消导入
     *
     * * @param importId
     */
    Boolean importCancelByExcel(String importId)throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

    /**
     *  设置各种名称
     *
     * * @param searchConditions
     * * @param response
     */
    void  setEveryName(List<ContractMilestoneVO> vos) throws Exception;

    /**
     * 合同内里程碑树形报表导出
     * @param contractMilestoneTreeDTO
     */
    void exportExcelDatatree(ContractMilestoneTreeDTO contractMilestoneTreeDTO,HttpServletResponse response)throws Exception;
    /**
     *  获取合同的里程碑树
     * @param searchVO
     * @return
     */
    List<TreeNodeVO<NodeVO<MileStoneTreeVo>>> queryTree(MileStoneTreeVo searchVO) throws Exception;

    /**
     * 预估里程碑审批
     * @param milestoneDTOS
     * @return
     */
    String approval(List<ContractMilestoneDTO> milestoneDTOS);

    /**
     * 获取子订单的里程碑树
     * @param searchVO
     * @return
     * @throws Exception
     */
    List<TreeNodeVO<NodeVO<MileStoneTreeVo>>> queryOrderTree(MileStoneTreeVo searchVO) throws Exception;

    /**
     * 跟踪确认
     * @param contractMilestoneDTO
     * @return
     * @throws Exception
     */
    String trackConfirmation(ContractMilestoneDTO contractMilestoneDTO) throws Exception;

    /**
     * 里程碑管理列表
     * @param
     * @return
     * @throws Exception
     */
    List<TreeNodeVO<NodeVO<MileStoneTreeVo>>> milestoneManage(MileStoneTreeVo searchVO) throws Exception;

    /**
     * 获取合同下的甲方签约主体
     * @param id
     * @return
     */
    List<CustomerInfo> getOurSignByContractId(String id);


    /**
     * 新建一个空的里程碑
     * @param mileStoneTreeVo
     * @return
     * @throws Exception
     */
    ContractMilestone createNew(MileStoneTreeVo mileStoneTreeVo) throws Exception;

    /**
     * 复制
     * @param mileStoneTreeVo
     * @return
     * @throws Exception
     */
    ContractMilestone copy(MileStoneTreeVo mileStoneTreeVo) throws Exception;

    /**
     * 获取当前合同的所有的所级
     * @param mileStoneTreeVo
     * @return
     * @throws Exception
     */
    List<SimpleDeptVO> getOfficeDept(MileStoneTreeVo mileStoneTreeVo) throws Exception;

    /**
     * 商城子订单新建一个空的里程碑
     * @param mileStoneTreeVo
     * @return
     * @throws Exception
     */
    ContractMilestone createNewProjectOrder(MileStoneTreeVo mileStoneTreeVo) throws Exception;

    ContractMilestone createNewFrame(MileStoneTreeVo mileStoneTreeVo) throws Exception;

    Boolean getContractMilestoneMoney(String contractId) throws Exception;

    Boolean checkMilestoneAmt(String contractId) throws Exception;

    Boolean checkRepeatName(String contractId) throws Exception;

    Boolean checkRepeatNameByContractMilestoneId(String mileStoneId) throws Exception;
}
