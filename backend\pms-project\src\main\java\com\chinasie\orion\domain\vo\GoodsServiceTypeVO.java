package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * EvaluationProject Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-23 17:21:12
 */
@ApiModel(value = "GoodsServiceTypeVO对象", description = "物资服务类型")
@Data
public class GoodsServiceTypeVO implements Serializable {

    /**
     * 物资服务类型编码
     */
    @ApiModelProperty(value = "物资服务类型编码")
    private String typeCode;

    /**
     * 物资服务类型编码名字
     */
    @ApiModelProperty(value = "物资服务类型编码名字")
    private String typeCodeName;
    /**
     * 物资服务类型编码名字
     */
    @ApiModelProperty(value = "物资服务类型编码序号")
    private Integer typeCodeSort;
}
