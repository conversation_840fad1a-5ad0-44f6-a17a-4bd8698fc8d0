package com.chinasie.orion.dict;

public class ProjectEvaluationDict {

    /**
     * 项目评价总编码
     */
    public static final String PROJECT_EVALUATE = "project_evaluate";
    /**
     * 项目自评价编码
     */
    public static final String SELF_EVALUATION = "selfEvaluation";
    /**
     * 项目后评价编码
     */
    public static final String AFTER_EVALUATION = "afterEvaluation";
    /**
     * 项目履约评价编码
     */
    public static final String PERFORMANCE_EVALUATION = "performanceEvaluation";
    /**
     * 项目评价通用详情
     */
    public static final String COMMON_EVALUATION_DETAIL = "common_evaluation";

    /**
     * 项目后评价编码详情
     */
    public static final String AFTER_EVALUATION_DETAIL = "after_evaluation";
    /**
     * 履约评价编码详情
     */
    public static final String PERFORMANCE_EVALUATION_DETAIL = "performance_evaluation";

//    /**
//     * 项目评价内容
//     */
//    public static final String COMMON_EVALUATION_CO NTENT = "evaluationContent";

    /**
     * 项目目标和成果评价
     */
    public static final String COMMON_GOAL_AND_ACHIEVE = "goalAchieve";
    /**
     * 项目进展管理评价
     */
    public static final String COMMON_PROGRESS_AND_MANAGE = "progressManage";
    /**
     * 项目质量评价
     */
    public static final String COMMON_PROJECT_QUALITY = "projectQuality";
    /**
     * 项目资源成本与效益评价
     */
    public static final String COMMON_Resource_COST_AND_BENEFIT = "costBenefit";
    /**
     * 项目风险评价
     */
    public static final String COMMON_PROJECT_RISK = "projectRisk";
    /**
     * 项目沟通和合作评价
     */
    public static final String COMMON_COMMUNICATE_AND_JOIN = "communicateJoin";
    /**
     * 经验与教训总结
     */
    public static final String COMMON_EXP_AND_LESSON = "expLesson";
    /**
     * 客户满意度
     */
    public static final String AFTER_CUSTOMER_SATISFACTION = "customerSatisfaction";
    /**
     * 团队绩效
     */
    public static final String AFTER_TEAM_PERFORMANCE = "teamPerformance";
    /**
     * 反馈和建议
     */
    public static final String AFTER_FEEDBACK_AND_SUGGESTIONS = "feedbackSuggestions";
    /**
     * 可持续性
     */
    public static final String AFTER_SUSTAINABILITY = "sustainability";
    /**
     * 范围管理
     */
    public static final String AFTER_SCOPE_MANAGEMENT = "scopeManage";
    /**
     * 利益相关方满意度
     */
    public static final String AFTER_STAKEHOLDER_SATISFACTION = "stakeholderSatisfaction";
    /**
     * 利益相关方参与度
     */
    public static final String AFTER_STAKEHOLDER_ENGAGEMENT = "stakeholderEngagement";
    /**
     * 创新和改进
     */
    public static final String AFTER_INNOVATION_AND_IMPROVEMENT = "innovationImprove";
    /**
     * 可行性分析
     */
    public static final String AFTER_FEASIBILITY_ANALYSIS = "feasibilityAnalysis";
    /**
     * 治理和监督
     */
    public static final String AFTER_GOVERNANCE_AND_SUPERVISION = "governanceSupervision";
    /**
     * 用户满意度
     */
    public static final String PERFORMANCE_USER_SATISFACTION = "userSatisfaction";
    /**
     * 合同履行
     */
    public static final String PERFORMANCE_OF_CONTRACT = "performanceContract";
    /**
     * 响应能力
     */
    public static final String PERFORMANCE_RESPONSIVENESS = "responsiveness";
    /**
     * 变更管理
     */
    public static final String PERFORMANCE_CHANGE_MANAGEMENT = "changeManage";
    /**
     * 问题解决
     */
    public static final String PERFORMANCE_PROBLEM_SOLVING = "problemSolve";
    /**
     * 信息安全和保密
     */
    public static final String PERFORMANCE_INFORM_SEC_AND_CONFIDENTIALITY = "informSecConfidentiality";
    /**
     * 长期伙伴关系
     */
    public static final String PERFORMANCE_LONG_TERM_PARTNER = "longTermPartner";

}
