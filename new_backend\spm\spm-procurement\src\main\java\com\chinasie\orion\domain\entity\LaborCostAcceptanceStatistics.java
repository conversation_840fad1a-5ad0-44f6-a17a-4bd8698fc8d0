package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * LaborCostAcceptanceStatistics Entity对象
 *
 * <AUTHOR>
 * @since 2024-10-29 14:23:45
 */
@TableName(value = "pmsx_labor_cost_acceptance_statistics")
@ApiModel(value = "LaborCostAcceptanceStatisticsEntity对象", description = "验收人力成本费用统计")
@Data

public class LaborCostAcceptanceStatistics extends  ObjectEntity  implements Serializable{

    /**
     * 验收单id
     */
    @ApiModelProperty(value = "验收单id")
    @TableField(value = "acceptance_id")
    private String acceptanceId;

    /**
     * 人员岗级
     */
    @ApiModelProperty(value = "人员岗级")
    @TableField(value = "job_grade")
    private String jobGrade;

    /**
     * 计划需求人数
     */
    @ApiModelProperty(value = "计划需求人数")
    @TableField(value = "plan_user_count")
    private Integer planUserCount;

    /**
     * 实际人数
     */
    @ApiModelProperty(value = "实际人数")
    @TableField(value = "actual_user_count")
    private Integer actualUserCount;

    /**
     * 工作量(人/月)
     */
    @ApiModelProperty(value = "工作量(人/月)")
    @TableField(value = "workload")
    private BigDecimal workload;

    /**
     * 岗级成本
     */
    @ApiModelProperty(value = "岗级成本")
    @TableField(value = "job_grade_amt")
    private BigDecimal jobGradeAmt;

    /**
     * 岗级总价 (元)
     */
    @ApiModelProperty(value = "岗级总价 (元)")
    @TableField(value = "job_grade_total_amt")
    private BigDecimal jobGradeTotalAmt;

}
