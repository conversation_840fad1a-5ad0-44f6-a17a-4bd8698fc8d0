<script setup lang="ts">
import {
  BasicTableAction, Layout, OrionTable, isPower, BasicButton, downloadByData, useModal, BasicImport,
} from 'lyra-component-vue3';
import {
  computed, nextTick, ref, Ref, watchEffect,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import Api from '/@/api';
import { usePagePower } from '/@/views/pms/hooks';
import {
  Tabs, TabPane, Breadcrumb, BreadcrumbItem, RadioGroup, FormItem, Modal, message,
} from 'ant-design-vue';
import { BarsOutlined } from '@ant-design/icons-vue';
import { typeOptions } from './options';
import { getColumns } from './columns';
import { openFormDrawer, openLeaveForm } from './utils';
import StaffEditor from './components/StaffEditor.vue';
import LeaveEditor from './components/LeaveEditor.vue';
const [register, { openModal }] = useModal();

const route = useRoute();
const router = useRouter();
const size = ref('small');
const loadStatus:Ref<boolean> = ref(false);
const selectedKeys = ref([]);
const selectedRows = ref([]);
const configuration: Ref = ref();
const support: Ref = ref();
const activeKey = ref<string>('configuration');
const { powerData, getPowerDataHandle } = usePagePower();
const randomKeyA = ref(Math.random());
const randomKeyB = ref(Math.random());
const dataType = computed(() => route.query.type);
const dataPositionType = computed(() => route.query.positionType);
const downloadFileObj = {
  url: '/pms/technical-support/template/download',
  method: 'POST',
};

const panes = ref(computed(() => [
  isPower('PMS_JSZCRYK_container_01', powerData.value) ? {
    key: 'configuration',
    name: '技术配置人员',
  } : undefined,
  isPower('PMS_JSZCRYK_container_02', powerData.value) ? {
    key: 'support',
    name: '临时辅助人员',
  } : undefined,
].filter((item) => item)));
const positionType = ref('onDuty'); // onDuty到岗，leave离岗
const actions = computed(() => {
  if (positionType.value === 'onDuty' && activeKey.value === 'support') {
    return [
      {
        text: '编辑',
        isShow: () => isPower('PMS_JSZCRYK_container_02_line_button_01', powerData.value),
        onClick(record: { id: string }) {
          openFormDrawer(StaffEditor, record, updateTable);
        },
      },
      {
        text: '查看',
        isShow: () => isPower('PMS_JSZCRYK_container_02_line_button_02', powerData.value),
        onClick: (record: { id: string }) => navDetails(record?.id),
      },
      {
        text: '离岗',
        isShow: () => isPower('PMS_JSZCRYK_container_02_line_button_03', powerData.value),
        onClick(record) {
          const params = [
            {
              id: record?.id,
              userId: record?.userId,
              userStatus: '离岗',
            },
          ];
          openLeaveForm(LeaveEditor, params, updateTable);
        },
      },
    ];
  }
  return [
    {
      text: '查看',
      isShow: () => isPower('PMS_JSZCRYK_container_01_line_button_01', powerData.value),
      onClick: (record: { id: string }) => navDetails(record?.id),
    },
  ];
});

watchEffect(() => {
  if (dataType.value) {
    activeKey.value = String(dataType.value);
    if (dataType.value === 'support' && dataPositionType.value) {
      positionType.value = String(dataPositionType.value);
    }
    nextTick(() => {
      onTabChange(dataType.value);
    });
  }
});

const rowSelection = computed(() => (activeKey.value === 'configuration' ? false : {
  onChange(keys: string[], rows: any[]) {
    selectedKeys.value = keys || [];
    selectedRows.value = rows || [];
  },
}));

const tableOptions = {
  showToolButton: false,
  rowSelection,
  isSpacing: true,
  smallSearchField: [
    'fullName',
    'userCode',
    'companyName',
    'deptName',
  ],
  api: (params: Record<string, any>) => new Api('/pms/technical-support').fetch({
    ...params,
    power: {
      pageCode: 'PMSTechnicalStaffAllocation',
      containerCode: activeKey.value === 'support' ? 'PMS_JSZCRYK_container_02_line' : 'PMS_JSZCRYK_container_01_line',
    },
    query: {
      userType: activeKey.value === 'support' ? 1 : 0,
      userStatus: positionType.value === 'onDuty' ? '到岗' : '离岗',
    },
  }, 'page', 'POST'),
  actions,
};

function onTabChange(key) {
  if (key === 'configuration') {
    randomKeyA.value = Math.random() + 1;
  } else {
    randomKeyB.value = Math.random() + 1;
  }
}

function actionClick(actionItem: any, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'view':
      navDetails(record?.id);
      break;
  }
}

// 新增
function handleAddPersonnel() {
  openFormDrawer(StaffEditor, null, updateTable);
}

// 删除
function deleteBatch() {
  if (selectedKeys.value.length === 0) {
    message.warning('暂无数据可删除');
    return;
  }
  Modal.confirm({
    title: '删除提示',
    content: '是否确认删除数据？',
    async onOk() {
      let params = selectedKeys.value;
      const res = await new Api('/pms/technical-support').fetch(params, 'remove', 'DELETE');
      if (res) {
        message.success('删除成功');
        updateTable();
      }
    },
  });
}

// 离岗
function handleLeavePost() {
  const params = selectedRows.value && selectedRows.value.map((item) => ({
    id: item?.id,
    userId: item?.userId,
    userStatus: '离岗',
    leaveWorkTime: '',
  }));
  openLeaveForm(LeaveEditor, params, updateTable);
}

// 导入
const handleImport = () => {
  openModal(true, {});
};

// 导出
function exportTable() {
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk: async () => {
      loadStatus.value = true;
      downloadByData('/pms/technical-support/export/excel', {
        ids: selectedKeys.value,
        state: positionType.value === 'onDuty' ? '到岗' : '离岗',
      }, '', 'POST', true, false, '导出处理完成，现在开始下载');
      loadStatus.value = false;
    },
  });
}

function navDetails(id: string) {
  router.push({
    name: 'PMSTechnicalStaffAllocationDetails',
    params: {
      id,
    },
    query: {
      type: activeKey.value,
      positionType: positionType.value,
    },
  });
}
function updateTable() {
  if (activeKey.value === 'configuration') {
    randomKeyA.value = Math.random() + 1;
  } else {
    randomKeyB.value = Math.random() + 1;
  }
}

// 辅助函数：生成唯一的 key
const getUniqueKey = (key, positionType) => {
  if (key === 'configuration') {
    return `${key}${randomKeyA.value}`;
  }
  return `${key}${positionType}${randomKeyB.value}`;
};

const requestBasicImport = async (formData) =>
  new Promise((resolve) => {
    new Api('/pms')
      .importFile(
        formData[0],
        '/api/pms/technical-support/import/excel/check',
      )
      .then((response) => {
        if (response.data?.code === 200) {
          if (response.data?.result?.code === 4000) {
            message.error(response.data?.result?.oom);
            resolve({
              err: response.data?.result ?? [],
              succ: false,
              code: 200,
            });
          } else {
            const importId = response?.data?.result?.succ || '';
            if (importId) {
              const res = new Api(`/pms/technical-support/import/excel/${importId}`).fetch('', '', 'GET');
              if (res) {
                resolve({
                  err: [],
                  succ: '成功',
                  code: 200,
                });
              }
            }
          }
        } else {
          resolve({
            err: response.data?.result ?? [],
            succ: false,
            code: 200,
          });
        }
      });
  });

const requestSuccessImport = (successKey) =>
  new Promise((resolve) => {
    setTimeout(() => {
      // 如果有errorMsg就表示失败，没有就表示成功
      resolve({
        result: true,
      });
      updateTable();
    }, 2000);
  });
</script>

<template>
  <Layout
    v-get-power="{pageCode:'PMSTechnicalStaffAllocation',getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
  >
    <Tabs
      v-model:activeKey="activeKey"
      :animated="false"
      :size="size"
      class="act-end"
      @change="onTabChange"
    >
      <TabPane
        v-for="pane in panes"
        :key="pane.key"
        :tab="pane.name"
      >
        <OrionTable
          :key="getUniqueKey(pane.key, positionType)"
          :ref="pane.key"
          class="radio-button-table"
          :options="tableOptions"
          :columns="getColumns(activeKey, positionType, navDetails)"
        >
          <template #actions="{record}">
            <BasicTableAction
              :actions="actions"
              :record="record"
              @actionClick="actionClick($event,record)"
            />
          </template>
          <template
            v-if="activeKey === 'support'"
            #toolbarLeft
          >
            <FormItem
              class="mb0i"
            >
              <div class="w-auto">
                <BasicButton
                  v-if="isPower('PMS_JSZCRYK_container_02_header_button_01',powerData) && positionType.includes('onDuty')"
                  type="primary"
                  icon="add"
                  @click="handleAddPersonnel"
                >
                  添加人员
                </BasicButton>
                <BasicButton
                  v-if="isPower('PMS_JSZCRYK_container_02_header_button_02',powerData) && positionType.includes('onDuty')"
                  icon="delete"
                  :disabled="selectedKeys.length===0"
                  @click="deleteBatch"
                >
                  删除
                </BasicButton>
                <BasicButton
                  v-if="isPower('PMS_JSZCRYK_container_02_header_button_03',powerData) && positionType.includes('onDuty')"
                  icon="sie-icon-tijiao"
                  :disabled="selectedKeys.length===0"
                  @click="handleLeavePost"
                >
                  离岗
                </BasicButton>
                <BasicButton
                  v-if="isPower('PMS_JSZCRYK_container_02_header_button_04',powerData) && positionType.includes('onDuty')"
                  icon="sie-icon-daochu"
                  @click="handleImport"
                >
                  导入
                </BasicButton>
                <BasicButton
                  v-if="isPower('PMS_JSZCRYK_container_02_header_button_05',powerData)"
                  icon="sie-icon-daochu"
                  @click="exportTable"
                >
                  导出
                </BasicButton>
              </div>
              <div class="status">
                <span>人员状态</span>
                <RadioGroup
                  v-model:value="positionType"
                  class="mla"
                  :options="typeOptions"
                />
              </div>
            </FormItem>
          </template>
        </OrionTable>
      </TabPane>
      <template #tabBarExtraContent>
        <Breadcrumb>
          <BreadcrumbItem>
            <bars-outlined />
            <span>{{ activeKey ==='configuration' ? '技术配置人员' : '临时辅助人员' }}</span>
          </BreadcrumbItem>
        </Breadcrumb>
      </template>
    </Tabs>
    <!-- 导入 -->
    <BasicImport
      :downloadFileObj="downloadFileObj"
      :requestBasicImport="requestBasicImport"
      :requestSuccessImport="requestSuccessImport"
      @register="register"
    />
  </Layout>
</template>

<style scoped lang="less">
:deep(.ant-tabs-nav) {
  padding: 0 25px;
  .ant-tabs-tab{
    padding: 8px;
  }
  .ant-tabs-tab-active .ant-tabs-tab-btn {
    font-size: 14px; /* 设置统一的字体大小 */
  }
}
:deep(.radio-button-table) {
  width: auto;
  flex: 0;

  .ant-input-search {
    width: 215px;
    margin-left: 12px;
  }
}

.mla {
  margin-left: auto;
}

.mb0i {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 0 !important;
  .w-auto{
    width: auto;
  }
}
:deep(.ant-form-item-control-input-content){
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.status{
  display: flex;
  flex-direction: row;
  align-items: center;
  span{
    margin-right: 28px;
  }
}
:deep(.ant-radio-group) {
  .ant-radio-inner:after{
    background-color: #e91e63;
  }
  .ant-radio-checked .ant-radio-inner{
    border: 1px solid #e91e63;
  }
}
:deep(.orion-table-header-wrap){
  .flex{
    justify-content: end;
  }
}
</style>
