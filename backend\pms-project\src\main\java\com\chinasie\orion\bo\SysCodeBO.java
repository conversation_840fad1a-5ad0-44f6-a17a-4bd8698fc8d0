package com.chinasie.orion.bo;

import com.chinasie.orion.api.code.SysCodeApi;
import com.chinasie.orion.api.code.domain.vo.CodeSegmentVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.util.ResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/09/20/9:41
 * @description:
 */
@Component
public class SysCodeBO {

    private SysCodeApi sysCodeApi;

    @Autowired
    public void setSysCodeApi(SysCodeApi sysCodeApi) {
        this.sysCodeApi = sysCodeApi;
    }

    public List<CodeSegmentVO> getCodeRuleList(String className, String number) throws Exception {
        ResponseDTO<List<CodeSegmentVO>> responseDTO = sysCodeApi.rulesAndSegment(className, number);
        if (ResponseUtils.success(responseDTO)) {
            return responseDTO.getResult();
        }
        return new ArrayList<>();
    }

    public String getCode(List<CodeSegmentVO> sysCodeSegmentVOS) throws Exception {
        ResponseDTO responseDTO = sysCodeApi.sysCodeSegmentCode(sysCodeSegmentVOS, null, null);
        if (ResponseUtils.success(responseDTO)) {
            return (String) responseDTO.getResult();
        }
        throw new PMSException(PMSErrorCode.PMS_ERR, "生成编码异常");
    }
}
