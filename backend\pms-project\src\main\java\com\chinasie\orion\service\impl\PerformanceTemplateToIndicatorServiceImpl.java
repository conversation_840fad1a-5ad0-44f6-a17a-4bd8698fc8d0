package com.chinasie.orion.service.impl;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.constant.IndicatorStatusEnum;
import com.chinasie.orion.domain.entity.PerformanceTemplateToIndicator;
import com.chinasie.orion.exception.BaseErrorCode;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.repository.PerformanceTemplateToIndicatorMapper;
import com.chinasie.orion.service.PerformanceTemplateToIndicatorService;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.String;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * PerformanceTemplateToIndicator 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-26 19:59:56
 */
@Service
public class PerformanceTemplateToIndicatorServiceImpl extends OrionBaseServiceImpl<PerformanceTemplateToIndicatorMapper, PerformanceTemplateToIndicator> implements PerformanceTemplateToIndicatorService {

    @Autowired
    private PerformanceTemplateToIndicatorMapper performanceTemplateToIndicatorMapper;

    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        int delete = performanceTemplateToIndicatorMapper.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }

    @Override
    public Boolean enable(List<String> ids) {
        //TODO 6/12 审查 吴锋   边界问题 为空怎么办
        LambdaUpdateWrapper<PerformanceTemplateToIndicator> updateWrapper = new LambdaUpdateWrapper();
        updateWrapper.set(PerformanceTemplateToIndicator::getStatus, IndicatorStatusEnum.ENABLE.getValue());
        updateWrapper.in(PerformanceTemplateToIndicator::getId, ids);
        return this.update(updateWrapper);
    }

    @Override
    public Boolean disEnable(List<String> ids) {
        //TODO 6/12 审查  吴锋  边界问题 为空怎么办
        LambdaUpdateWrapper<PerformanceTemplateToIndicator> updateWrapper = new LambdaUpdateWrapper();
        updateWrapper.set(PerformanceTemplateToIndicator::getStatus, IndicatorStatusEnum.DISENABLE.getValue());
        updateWrapper.in(PerformanceTemplateToIndicator::getId, ids);
        return this.update(updateWrapper);
    }

    @Override
    public Boolean create(String templateId, List<PerformanceTemplateToIndicator> performanceTemplateDTO) {
        if (CollectionUtils.isBlank(performanceTemplateDTO)) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL, "提交的指标数据不能为空！");
        }
        performanceTemplateDTO = performanceTemplateDTO.stream().distinct().collect(Collectors.toList());
        this.remove(new LambdaQueryWrapperX<>(PerformanceTemplateToIndicator.class).eq(PerformanceTemplateToIndicator::getTemplateId, templateId));
        performanceTemplateDTO.forEach(item -> {
            item.setTemplateId(templateId);
            item.setStatus(IndicatorStatusEnum.ENABLE.getValue());
        });
        return this.saveBatch(performanceTemplateDTO);
    }


}
