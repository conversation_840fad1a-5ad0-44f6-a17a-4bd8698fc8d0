package com.chinasie.orion.controller.assetApply;

import com.chinasie.orion.domain.dto.assetApply.ProjectAssetApplyDetailWbsDTO;
import com.chinasie.orion.domain.vo.applyAsset.ProjectAssetApplyDetailWbsVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.assetApply.ProjectAssetApplyDetailWbsService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * ProjectAssetApplyDetailWbs 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-03 17:35:44
 */
@RestController
@RequestMapping("/projectAssetApplyDetailWbs")
@Api(tags = "资产转固申请详情表-WBS")
public class  ProjectAssetApplyDetailWbsController  {

    @Autowired
    private ProjectAssetApplyDetailWbsService projectAssetApplyDetailWbsService;


//    /**
//     * 详情
//     *
//     * @param id
//     * @return
//     * @throws Exception
//     */
//    @ApiOperation(value = "详情")
//    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
//    @Transactional(rollbackFor = Exception.class)
//    public ResponseDTO<ProjectAssetApplyDetailWbsVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
//        ProjectAssetApplyDetailWbsVO rsp = projectAssetApplyDetailWbsService.detail(id,pageCode);
//        return new ResponseDTO<>(rsp);
//    }


    /**
     * 新增WBS
     *
     * @param projectAssetApplyDetailWbsDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【资产转固申请详情表-WBS】数据", type = "ProjectAssetApplyDetailWbs", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody ProjectAssetApplyDetailWbsDTO projectAssetApplyDetailWbsDTO) throws Exception {
        String rsp =  projectAssetApplyDetailWbsService.create(projectAssetApplyDetailWbsDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }
    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【资产转固申请详情表-WBS】数据", type = "ProjectAssetApplyDetailWbs", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ProjectAssetApplyDetailWbsVO>> pages(@RequestBody Page<ProjectAssetApplyDetailWbsDTO> pageRequest) throws Exception {
        Page<ProjectAssetApplyDetailWbsVO> rsp =  projectAssetApplyDetailWbsService.getPages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

//    /**
//     * 编辑
//     *
//     * @param projectAssetApplyDetailWbsDTO
//     * @return
//     * @throws Exception
//     */
//    @ApiOperation(value = "编辑")
//    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
//    @Transactional(rollbackFor = Exception.class)
//    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#projectAssetApplyDetailWbsDTO.name}}】", type = "ProjectAssetApplyDetailWbs", subType = "编辑", bizNo = "{{#projectAssetApplyDetailWbsDTO.id}}")
//    public ResponseDTO<Boolean> edit(@RequestBody  ProjectAssetApplyDetailWbsDTO projectAssetApplyDetailWbsDTO) throws Exception {
//        Boolean rsp = projectAssetApplyDetailWbsService.edit(projectAssetApplyDetailWbsDTO);
//        return new ResponseDTO<>(rsp);
//    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【资产转固申请详情表-WBS】数据", type = "ProjectAssetApplyDetailWbs", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = projectAssetApplyDetailWbsService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【资产转固申请详情表-WBS】数据", type = "ProjectAssetApplyDetailWbs", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectAssetApplyDetailWbsService.remove(ids);
        return new ResponseDTO<>(rsp);
    }



//    @ApiOperation("资产转固申请详情表-WBS导入下载模板(Excel)")
//    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
//    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "ProjectAssetApplyDetailWbs", subType = "导入下载模板", bizNo = "")
//    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
//        projectAssetApplyDetailWbsService.downloadExcelTpl(response);
//    }
//
//    @ApiOperation("资产转固申请详情表-WBS导入校验（Excel）")
//    @PostMapping(value = "/import/excel/check")
//    @Transactional(rollbackFor = Exception.class)
//    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "ProjectAssetApplyDetailWbs", subType = "校验导入", bizNo = "")
//    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
//        ImportExcelCheckResultVO rsp = projectAssetApplyDetailWbsService.importCheckByExcel(file);
//        return new ResponseDTO<>(rsp);
//    }
//
//
//    @ApiOperation("资产转固申请详情表-WBS导入（Excel）")
//    @PostMapping(value = "/import/excel/{importId}")
//    @Transactional(rollbackFor = Exception.class)
//    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "ProjectAssetApplyDetailWbs", subType = "确认导入", bizNo = "")
//    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
//        Boolean rsp =  projectAssetApplyDetailWbsService.importByExcel(importId);
//        return new ResponseDTO<>(rsp);
//    }
//
//    @ApiOperation("取消资产转固申请详情表-WBS导入（Excel）")
//    @PostMapping(value = "/import/excel/cancel/{importId}")
//    @Transactional(rollbackFor = Exception.class)
//    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "ProjectAssetApplyDetailWbs", subType = "取消导入", bizNo = "")
//    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
//        Boolean rsp =  projectAssetApplyDetailWbsService.importCancelByExcel(importId);
//        return new ResponseDTO<>(rsp);
//    }
//
//    @ApiOperation("资产转固申请详情表-WBS导出（Excel）")
//    @PostMapping(value = "/export/excel")
//    @Transactional(rollbackFor = Exception.class)
//    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "ProjectAssetApplyDetailWbs", subType = "导出数据", bizNo = "")
//    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
//        projectAssetApplyDetailWbsService.exportByExcel(searchConditions, response);
//    }
}
