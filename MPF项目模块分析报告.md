# MPF项目模块分析报告

本文档分析了MPF项目中是否存在以下模块的代码：个人中心、战略计划、市场经营、履约执行、采购管理、生产服务、通用管理、任务中心、财务管理、外链分区。

## 模块存在状态及代码分布

| 模块名称 | 存在状态 | 后端代码分布 | 前端代码分布 |
| ------- | ------- | ---------- | ---------- |
| 个人中心 | ✅ 存在 | `pms-project-management/src/main/java/com/chinasie/orion/controller/PersonalCenterController.java`<br>`pms-project/src/main/java/com/chinasie/orion/domain/dto/projectscheme/StatDTO.java`<br>`pms-project/src/main/java/com/chinasie/orion/domain/vo/ProjectCenterStatVO.java`<br>`pms-project/src/main/java/com/chinasie/orion/controller/ProjectSchemeController.java` | 前端代码中有相关引用，但未找到完整模块实现 |
| 战略计划 | ✅ 存在 | `pms-project-management/src/main/java/com/chinasie/orion/constant/PersonInitialEnum.java` | 未找到明确的前端实现 |
| 市场经营 | ✅ 存在 | `pms-project-management/pom.xml`（模块描述）<br>`pms-project-management/src/main/java/com/chinasie/orion/management/controller/ManagementStaticsController.java`<br>`pms-project-management/src/main/java/com/chinasie/orion/management/domain/dto/ManagementStaticsReqDTO.java`<br>`pms-project/src/main/java/com/chinasie/orion/exp/marketManagement/` | `front/src/views/pms/projectLibrary/pages/components/projectOverview/components/components/ProjectKnowledgeDocument.vue` |
| 履约执行 | ✅ 存在 | `pms-project/src/main/java/com/chinasie/orion/domain/vo/projectOverviewZgh/ProjectLifeVO.java` | `front/modules/devModules.ts` |
| 采购管理 | ✅ 存在 | `pms-project/src/main/java/com/chinasie/orion/controller/ProjectPurchaseOrderInfoController.java`<br>`pms-project/src/main/java/com/chinasie/orion/exp/contractInfo/ContractInfoSourcingEngineerExp.java`<br>`pms-project/src/main/java/com/chinasie/orion/constant/ProjectLifeCycleNodeEnum.java`<br>`pms-app/src/main/resources/projectlifecycle.json` | `front/src/views/pms/projectLaborer/projectLab/projectList/menuComponents/MenuComponents.vue`<br>`front/src/views/pms/projectLibrary/pages/components/projectOverview/components/components/flow/data.ts` |
| 生产服务 | ✅ 存在 | `pms-app/src/main/resources/db/prod/migration/prd_dev_3.7.0.0/V3.7.0.0.7__page_init.sql` | `front/src/views/pms/resourceallocation/Resourceallocation.vue` |
| 通用管理 | ✅ 存在 | `pms-project/src/main/java/com/chinasie/orion/service/impl/ProjectServiceImpl.java` | 未找到明确的前端实现 |
| 任务中心 | ❌ 未找到 | 未找到相关代码 | 未找到相关代码 |
| 财务管理 | ✅ 存在 | `pms-finance/src/main/java/com/chinasie/orion/service/impl/IncomePlanDataServiceImpl.java`<br>`pms-finance/src/main/java/com/chinasie/orion/service/impl/IncomePlanExecutionTrackServiceImpl.java`<br>`pms-finance/src/main/java/com/chinasie/orion/service/impl/InTransactionPreReconciliationServiceImpl.java` | `front/modules/devModules.ts` |
| 外链分区 | ❌ 未找到 | 未找到相关代码 | 未找到相关代码 |

## 总结

在MPF项目中，共发现8个模块存在相关代码实现，分别是：个人中心、战略计划、市场经营、履约执行、采购管理、生产服务、通用管理和财务管理。其中，市场经营和采购管理模块的代码实现较为完整，前后端均有对应代码。

未找到任务中心和外链分区模块的相关代码实现。

各模块在代码中的分布情况各不相同，有些模块主要集中在特定的后端模块中，如市场经营主要在pms-project-management模块中实现，财务管理主要在pms-finance模块中实现。前端代码主要分布在views/pms目录下的各个子目录中。