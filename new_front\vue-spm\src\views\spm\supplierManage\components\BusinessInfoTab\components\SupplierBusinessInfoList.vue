<script setup lang="ts">
import {
  Layout, OrionTable,
} from 'lyra-component-vue3';
import {
  ref, Ref, inject,
} from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';

const supplierInfo: Record<string, any> = inject('supplierInfo');

const tableRef: Ref = ref();
const tableOptions = {
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  smallSearchField: undefined,
  showSmallSearch: false,
  columns: [
    {
      title: '合同编号',
      dataIndex: 'contractNo',
    },
    {
      title: '合同名称',
      dataIndex: 'contractName',
    },
    {
      title: '合同生效日期',
      dataIndex: 'effectiveDate',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '甲方公司',
      dataIndex: 'partyACompany',
    },
    {
      title: '负责人',
      dataIndex: 'responsiblePerson',
    },
    {
      title: '货币',
      dataIndex: 'currency',
    },
    {
      title: '合同金额档级',
      dataIndex: 'contractAmountLevel',
    },
    {
      title: '合同状态',
      dataIndex: 'contractStatus',
    },
  ],
  api: (params:Record<string, any>) => new Api('/spm/supplierBusinessInfo/getBusinessInfoByCode').fetch({
    ...params,
    query: {
      supplierCode: supplierInfo.value?.supplierNumber,
    },
  }, '', 'POST'),
};

</script>

<template>
  <Layout :options="{ body: { scroll: true } }">
    <OrionTable
      ref="tableRef"
      class="scroll-table"
      :options="tableOptions"
    />
  </Layout>
</template>

<style scoped lang="less">
.scroll-table {
  ::-webkit-scrollbar {
    height: 15px !important;
    width: 15px !important;
  }
  ::-webkit-scrollbar-thumb {
    border-radius:0;
  }
}
</style>
