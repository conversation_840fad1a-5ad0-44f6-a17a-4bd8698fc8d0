package com.chinasie.orion.domain.dto.job;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/08/07/14:52
 * @description:
 */
@Data
public class StartEndDTO implements Serializable {

    @ApiModelProperty(value = "计划开始结束日期")
    @Size(min = 2, max = 2, message = "计划开始结束日期不能为空")
    private List<Date> startAndEndDateList;

    @ApiModelProperty("id")
    @NotEmpty(message = "数据ID不能为空")
    private String id;
}
