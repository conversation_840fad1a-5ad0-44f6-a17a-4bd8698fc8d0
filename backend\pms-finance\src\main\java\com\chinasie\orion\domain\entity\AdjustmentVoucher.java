package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;

/**
 * AdjustmentVoucher Entity对象
 *
 * <AUTHOR>
 * @since 2024-12-24 10:44:40
 */
@TableName(value = "pmsx_adjustment_voucher")
@ApiModel(value = "AdjustmentVoucherEntity对象", description = "调账凭证数据表")
@Data

public class AdjustmentVoucher extends  ObjectEntity  implements Serializable{

    @ApiModelProperty(value = "关联合同id")
    @TableField(value = "contract_id")
    private String contractId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_num")
    private String contractNum;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @TableField(value = "contract_name")
    private String contractName;


    @ApiModelProperty(value = "合同里程碑")
    @TableField(value = "milestone_name")
    private String milestoneName;

    @ApiModelProperty(value = "合同里程碑id")
    @TableField(value = "milestone_id")
    private String milestoneId;
    /**
     * 公司代码
     */
    @ApiModelProperty(value = "公司代码")
    @TableField(value = "company_code")
    private String companyCode;

    /**
     * 科目
     */
    @ApiModelProperty(value = "科目")
    @TableField(value = "subject")
    private String subject;

    /**
     * 分配
     */
    @ApiModelProperty(value = "分配")
    @TableField(value = "allocation")
    private String allocation;

    /**
     * 过账期间
     */
    @ApiModelProperty(value = "过账期间")
    @TableField(value = "posting_period")
    private String postingPeriod;

    /**
     * 凭证编号
     */
    @ApiModelProperty(value = "凭证编号")
    @TableField(value = "voucher_num")
    private String voucherNum;

    /**
     * 凭证日期
     */
    @ApiModelProperty(value = "凭证日期")
    @TableField(value = "voucher_date")
    private Date voucherDate;

    /**
     * 过账日期
     */
    @ApiModelProperty(value = "过账日期")
    @TableField(value = "posting_date")
    private Date postingDate;

    /**
     * 本币金额
     */
    @ApiModelProperty(value = "本币金额")
    @TableField(value = "local_currency_amount")
    private BigDecimal localCurrencyAmount;

    /**
     * 利润中心
     */
    @ApiModelProperty(value = "利润中心")
    @TableField(value = "profit_center")
    private String profitCenter;

    /**
     * 成本中心
     */
    @ApiModelProperty(value = "成本中心")
    @TableField(value = "cost_center")
    private String costCenter;

    /**
     * 文本文档
     */
    @ApiModelProperty(value = "文本文档")
    @TableField(value = "con_text")
    private String conText;

    /**
     * wbs要素
     */
    @ApiModelProperty(value = "wbs要素")
    @TableField(value = "wbs_element")
    private String wbsElement;

    /**
     * Tr.prt
     */
    @ApiModelProperty(value = "Tr.prt")
    @TableField(value = "trprt")
    private String trprt;

    /**
     * 承诺项目
     */
    @ApiModelProperty(value = "承诺项目")
    @TableField(value = "committed_project")
    private String committedProject;

    /**
     * 基金中心
     */
    @ApiModelProperty(value = "基金中心")
    @TableField(value = "funding_center")
    private String fundingCenter;

    /**
     * 付款基准日期
     */
    @ApiModelProperty(value = "付款基准日期")
    @TableField(value = "pay_reference_date")
    private Date payReferenceDate;

    @ApiModelProperty(value = "挂接状态:0未挂接，1已挂接")
    @TableField(value = "hanging_connect_status")
    private Integer hangingConnectStatus;

}
