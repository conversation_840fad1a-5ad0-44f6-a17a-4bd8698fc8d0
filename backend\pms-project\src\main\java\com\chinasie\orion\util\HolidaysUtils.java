package com.chinasie.orion.util;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.chinasie.orion.domain.entity.ProjectScheme;
import org.springframework.stereotype.Component;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.*;

/**
 * @author: lsy
 * @date: 2024/5/31
 * @description:
 */
@Component
public class HolidaysUtils {

    /**
     * 通过开始日期和工期计算结束日期
     * @param projectScheme
     * @param durationDays
     * @param holidaysList
     * @param maxEndTime
     * @throws Exception
     */
    public void calculationEndDateOfWorkDay(ProjectScheme projectScheme,
                                            Integer durationDays,
                                            List<String> holidaysList,
                                            LocalDate maxEndTime) throws Exception {
        // 日期转换
        LocalDate date = DateUtil.parseLocalDateTime(DateUtil.formatDate(projectScheme.getBeginTime()), DatePattern.NORM_DATE_PATTERN)
                .toLocalDate();
        LocalDate nextWorkday = getNextWorkday(date, holidaysList);
        if (nextWorkday.isAfter(maxEndTime)) {
            projectScheme.setDurationDays(1);
            projectScheme.setEndTime(DateUtil.parseDate(DateUtil.formatLocalDateTime(date.atStartOfDay())));
            return;
        }
        int count = 1;
        while (count < durationDays) {
            nextWorkday = nextWorkday.plusDays(1);
            if (nextWorkday.isAfter(maxEndTime)) {
                projectScheme.setDurationDays(count);
                projectScheme.setEndTime(DateUtil.parseDate(DateUtil.formatLocalDateTime(maxEndTime.atStartOfDay())));
                return;
            }
            if (isWorkday(nextWorkday, holidaysList)) {
                count++;
            }
        }
        projectScheme.setDurationDays(count);
        projectScheme.setEndTime(DateUtil.parseDate(DateUtil.formatLocalDateTime(nextWorkday.atStartOfDay())));
    }

    private static boolean isWorkday(LocalDate localDate, List<String> holidaysList) {
        String dateStr = DateUtil.format(localDate.atStartOfDay(), DatePattern.NORM_DATE_PATTERN);

        /*
         * 节假日中存在日期，判断日期是否是工作日
         * */
        if (holidaysList.contains(dateStr)) {
            return false;
        }

        /*
         * 节假日中不存在日期，通过周六、周日判断日期是否是工作日
         * */
        DayOfWeek dayOfWeek = localDate.getDayOfWeek();
        return dayOfWeek != DayOfWeek.SUNDAY && dayOfWeek != DayOfWeek.SATURDAY;
    }

    /**
     * 获取下一个工作日期
     * @param localDate
     * @param holidaysList
     * @return
     */
    private static LocalDate getNextWorkday(LocalDate localDate, List<String> holidaysList) {
        if (isWorkday(localDate, holidaysList)) {
            return localDate;
        }
        LocalDate nextWorkday = localDate;
        while (!isWorkday(nextWorkday, holidaysList)) {
            nextWorkday = nextWorkday.plusDays(1);
        }

        return nextWorkday;
    }

    /**
     * 通过结束日期和工期计算开始日期
     * @param projectScheme
     * @param durationDays
     * @param holidaysList
     * @param minBeginTime
     * @throws Exception
     */
    public void calculationStartDateOfWorkDay(ProjectScheme projectScheme,
                                              Integer durationDays,
                                              List<String> holidaysList,
                                              LocalDate minBeginTime) throws Exception {
        // 日期转换
        LocalDate date = DateUtil.parseLocalDateTime(DateUtil.formatDate(projectScheme.getEndTime()), DatePattern.NORM_DATE_PATTERN)
                .toLocalDate();
        LocalDate preWorkday = getPreWorkday(date, holidaysList);
        if (preWorkday.isBefore(minBeginTime)) {
            projectScheme.setDurationDays(1);
            projectScheme.setBeginTime(DateUtil.parseDate(DateUtil.formatLocalDateTime(date.atStartOfDay())));
            return;
        }
        int count = 1;
        while (count < durationDays) {
            preWorkday = preWorkday.plusDays(-1);
            if (preWorkday.isBefore(minBeginTime)) {
                projectScheme.setDurationDays(count);
                projectScheme.setBeginTime(DateUtil.parseDate(DateUtil.formatLocalDateTime(minBeginTime.atStartOfDay())));
                return;
            }
            if (isWorkday(preWorkday, holidaysList)) {
                count++;
            }
        }
        projectScheme.setDurationDays(count);
        projectScheme.setBeginTime(DateUtil.parseDate(DateUtil.formatLocalDateTime(preWorkday.atStartOfDay())));
    }

    /**
     * 获取上一个工作日期
     * @param localDate
     * @param holidaysList
     * @return
     */
    private static LocalDate getPreWorkday(LocalDate localDate, List<String> holidaysList) {
        if (isWorkday(localDate, holidaysList)) {
            return localDate;
        }
        LocalDate nextWorkday = localDate;
        while (!isWorkday(nextWorkday, holidaysList)) {
            nextWorkday = nextWorkday.plusDays(-1);
        }

        return nextWorkday;
    }

}
