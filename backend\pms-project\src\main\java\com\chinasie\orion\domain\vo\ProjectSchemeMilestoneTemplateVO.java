package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ProjectSchemeMilestoneTemplate Entity对象
 *
 * <AUTHOR>
 * @since 2023-06-05 10:09:44
 */
@ApiModel(value = "ProjectSchemeMilestoneTemplateVO对象", description = "项目计划里程碑模版")
@Data
public class ProjectSchemeMilestoneTemplateVO extends ObjectVO implements Serializable{

    /**
     * 模版名称
     */
    @ApiModelProperty(value = "模版名称")
    private String templateName;

    /**
     * 模版顺序
     */
    @ApiModelProperty(value = "模版顺序")
    private Long sort;


}
