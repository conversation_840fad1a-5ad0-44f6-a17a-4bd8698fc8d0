package com.chinasie.orion.domain.vo.resource;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/09/05/14:15
 * @description:
 */
@Data
public class PersonSourceAllocationVO implements Serializable {
    @ApiModelProperty("人员资源列表")
    private List<PersonSourceVO> personSourceVOList;
    @ApiModelProperty("季度统计列表")
    private List<PersonQuarterCountVO> quarterCountVOList;
    @ApiModelProperty("当前所处季度最小的数据")
    private PersonSourceVO shortTimePersonData;

    @ApiModelProperty("当前所处季度最小时间")
    private long minDate;
}
