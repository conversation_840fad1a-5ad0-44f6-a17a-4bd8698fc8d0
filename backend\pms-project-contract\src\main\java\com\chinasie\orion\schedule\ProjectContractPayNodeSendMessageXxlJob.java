package com.chinasie.orion.schedule;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.bo.PmsMQProducer;
import com.chinasie.orion.constant.ProjectContractMessageNodeEnums;
import com.chinasie.orion.constant.ProjectContractPayNodeAuditStatusEnum;
import com.chinasie.orion.constant.ProjectContractPayNodeStatusEnum;
import com.chinasie.orion.constant.ProjectStatusEnum;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.repository.ContractPayNodeConfirmRepository;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.service.ContractPayNodeService;
import com.chinasie.orion.service.ProjectContractService;
import com.chinasie.orion.service.ProjectService;
import com.chinasie.orion.util.CollectionUtils;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: yk
 * @date: 2023/10/28 14:42
 * @description: 更改项目合同支付节点状态为待支付
 */
@Component
public class ProjectContractPayNodeSendMessageXxlJob {

    @Autowired
    private ContractPayNodeService contractPayNodeService;

    @Autowired
    private ContractPayNodeConfirmRepository contractPayNodeConfirmRepository;

    @Autowired
    private ProjectContractService projectContractService;

    @Resource
    protected PmsMQProducer mqProducer;

    @Autowired
    private ProjectService projectService;


    @XxlJob("projectContractPayNodeSendMessage")
    public void sendMessage() throws Exception {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        LambdaQueryWrapper<ContractPayNode> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ContractPayNode::getStatus, ProjectContractPayNodeStatusEnum.PAYING.getStatus());
        lambdaQueryWrapper.lt(ContractPayNode::getInitPlanPayDate, calendar.getTime());
        List<ContractPayNode> contractPayNodeList = contractPayNodeService.list(lambdaQueryWrapper);
        if(!CollectionUtils.isBlank(contractPayNodeList)){
            List<String> nodeIdList = contractPayNodeList.stream().map(ContractPayNode :: getContractId).collect(Collectors.toList());
            LambdaQueryWrapperX<ContractPayNodeConfirm> objectLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            objectLambdaQueryWrapperX.selectAll(ContractPayNodeConfirm.class);
            objectLambdaQueryWrapperX.leftJoin(ContractPayNodeConfirmNode.class, ContractPayNodeConfirmNode::getConfirmId ,ContractPayNodeConfirm::getId )
                    .eq(ContractPayNodeConfirm::getStatus, ProjectContractPayNodeAuditStatusEnum.AUDITING.getStatus())
                    .in(ContractPayNodeConfirmNode :: getNodeId,nodeIdList);
            List<ContractPayNodeConfirm> contractPayNodeConfirms = contractPayNodeConfirmRepository.selectJoinList(ContractPayNodeConfirm.class,objectLambdaQueryWrapperX);
            if(!CollectionUtils.isBlank(contractPayNodeConfirms)){
                List<String> contractIdList =  contractPayNodeConfirms.stream().map(ContractPayNodeConfirm :: getContractId).collect(Collectors.toList());
                //获取已暂停或已终止项目
                List<String> projectIdList = projectService.list(new LambdaQueryWrapperX<>(Project.class).select(Project::getId)
                        .in(Project::getStatus, CollUtil.toList(ProjectStatusEnum.PAUSED.getStatus(), ProjectStatusEnum.TERMINATED.getStatus())))
                        .stream().map(Project::getId).collect(Collectors.toList());
                LambdaQueryWrapper<ProjectContract> wrapper = new LambdaQueryWrapper<>();
                wrapper.in(ProjectContract::getId, contractIdList);
                if (CollectionUtil.isNotEmpty(projectIdList)) {
                    wrapper.notIn(ProjectContract::getProjectId, projectIdList);
                }
                List<ProjectContract> projectContractList =  projectContractService.list(wrapper);
                if(!CollectionUtils.isBlank(projectContractList)){
                    Map<String,ProjectContract> projectContractMap = projectContractList.stream().collect(Collectors.toMap(ProjectContract :: getId, Function.identity()));
                    for(ContractPayNodeConfirm contractPayNodeConfirm : contractPayNodeConfirms){
                        ProjectContract projectContract = projectContractMap.get(contractPayNodeConfirm.getContractId());
                        if(projectContract != null){
                            sendMessage(contractPayNodeConfirm.getAuditUserId(),projectContract, contractPayNodeConfirm, ProjectContractMessageNodeEnums.PAY_NODE_CONFIRM_EXPIRE);
                        }
                    }
                }
            }
        }
    }
    private void sendMessage(String auditUserId, ProjectContract projectContract, ContractPayNodeConfirm nodeConfirm, ProjectContractMessageNodeEnums projectContractMessageNodeEnums) {
        if (StringUtils.hasText(auditUserId)) {
            Map<String, Object> messageMap = new HashMap<>();
            messageMap.put("$contractNumber$", projectContract.getNumber());
            messageMap.put("$confirmNumber$", nodeConfirm.getNumber());
            List<String> recipientIdList = new ArrayList<>();
            recipientIdList.add(auditUserId);

            Map<String, Object> businessDataMap = new HashMap<>();
            businessDataMap.put("bizCode", nodeConfirm.getId());
            businessDataMap.put("bizName", nodeConfirm.getNumber());
            businessDataMap.put("bizTypeCode", projectContract.getId());
            businessDataMap.put("bizTypeName", projectContract.getName());
            SendMessageDTO sendMsc = SendMessageDTO.builder()
                    .businessData(JSONObject.toJSONString(businessDataMap))
                    .businessId(nodeConfirm.getId())
                    .todoStatus(0)
                    .todoType(0)
                    .urgencyLevel(0)
                    .messageMap(messageMap)
                    .businessNodeCode(projectContractMessageNodeEnums.getCode())
                    .titleMap(messageMap)
                    //.messageUrl(String.format(JUMP_URL, project.getId()))
                    .messageUrl(projectContractMessageNodeEnums.getMessageUrl())
                    .messageUrlName(projectContractMessageNodeEnums.getMessageUrlName())
                    .recipientIdList(recipientIdList)
                    .senderId(CurrentUserHelper.getCurrentUserId())
                    .senderTime(new Date())
                    .build();
            mqProducer.sendPmsMessage(sendMsc);
        }
    }
}
