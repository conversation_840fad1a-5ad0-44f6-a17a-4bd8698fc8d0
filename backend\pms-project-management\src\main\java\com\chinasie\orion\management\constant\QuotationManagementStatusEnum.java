package com.chinasie.orion.management.constant;


import lombok.Getter;

/**
 * 报价单状态枚举类型
 *
 * @since 2024年8月29日
 */
@Getter
public enum QuotationManagementStatusEnum {

    IN_PREPARATION(120, "编制中"),
    UNDER_REVIEW(110, "审核中"),
    CAN_QUOTATION(150, "可投标"),
    ALREADY_QUOTATION(121, "已投标"),
    SUCCESS_QUOTATION(1, "已中标"),
    FAILED_QUATATION(140, "未中标"),
    RE_QUATATION(160, "重新报价"),
    ABANDON(111, "已作废");

    private final Integer status;

    private final String desc;

    QuotationManagementStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
