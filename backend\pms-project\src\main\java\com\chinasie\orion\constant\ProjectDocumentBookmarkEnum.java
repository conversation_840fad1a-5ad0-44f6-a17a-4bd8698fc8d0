package com.chinasie.orion.constant;

/**
 * @author: lsy
 * @date: 2024/6/21
 * @description:
 */
public enum ProjectDocumentBookmarkEnum {
    ZS_DEPARTMENT("中试部", "zsDepartment"),
    CS_DEPARTMENT("测试验证部", "csDepartment"),
    ZL_DEPARTMENT("质量管理部", "zlDepartment"),
    XM_DEPARTMENT("项目管理办公室", "zmDepartment"),
    KH_DEPARTMENT("客户服务部", "khDepartment"),
    JS_DEPARTMENT("技术管理办公室", "jsDepartment"),
    CG_DEPARTMENT("采购部", "cgDepartment"),
    YY_DEPARTMENT("运营部", "yyDepartment"),
    CW_DEPARTMENT("财务部", "cwDepartment"),
    ZZ_DEPARTMENT("制造部", "zzDepartment"),
    JJ_DEPARTMENT("解决方案部", "jjDepartment"),
    ;

    private String name;

    private String code;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    ProjectDocumentBookmarkEnum(String name, String code) {
        this.name = name;
        this.code = code;
    }
}
