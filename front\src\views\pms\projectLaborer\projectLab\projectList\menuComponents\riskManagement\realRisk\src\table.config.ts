export const columns = [
  {
    title: '编号',
    dataIndex: 'number',
    align: 'left',
    key: 'number',

    width: '120px',
    sorter: true,
    ellipsis: true,
  },
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',

    width: '240px',
    align: 'left',
    slots: { customRender: 'name' },
    sorter: true,
    ellipsis: true,
  },

  {
    title: '风险类型',
    dataIndex: 'riskTypeName',
    key: 'riskType',
    width: '100px',
    margin: '0 20px 0 0',
    align: 'left',
    slots: { customRender: 'riskTypeName' },
    sorter: true,
    ellipsis: true,
  },
  {
    title: '发生概率',
    dataIndex: 'riskProbabilityName',
    key: 'riskProbability',
    width: '90px',
    align: 'left',
    slots: { customRender: 'riskProbabilityName' },
    sorter: true,
    ellipsis: true,
  },
  {
    title: '影响程度',
    dataIndex: 'riskInfluenceName',
    key: 'riskInfluence',

    width: '80px',
    align: 'left',
    slots: { customRender: 'riskInfluenceName' },
    sorter: true,
    ellipsis: true,
  },
  {
    title: '预估发生时间',
    dataIndex: 'predictStartTimeName',
    key: 'predictStartTime',

    width: '130px',
    align: 'left',
    slots: { customRender: 'predictStartTimeName' },
    sorter: true,
    ellipsis: true,
  },
  {
    title: '应对策略',
    dataIndex: 'copingStrategyName',
    key: 'copingStrategy',

    width: '100px',
    align: 'left',
    sorter: true,
    ellipsis: true,
    slots: { customRender: 'copingStrategyName' },
  },
  {
    title: '状态',
    dataIndex: 'statusName',
    key: 'status',

    width: '80px',
    align: 'left',
    sorter: true,
    ellipsis: true,
    slots: { customRender: 'statusName' },
  },
  {
    title: '负责人',
    dataIndex: 'principalName',
    key: 'principalId',

    width: '80px',
    align: 'left',
    sorter: true,
    ellipsis: true,
    slots: { customRender: 'principalName' },
  },
  {
    title: '创建日期',
    dataIndex: 'createTime',
    key: 'createTime',

    width: '150px',
    align: 'left',
    sorter: true,
    ellipsis: true,
    slots: { customRender: 'createTime' },
  },
];
