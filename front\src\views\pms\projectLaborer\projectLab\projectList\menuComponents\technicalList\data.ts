import dayjs from 'dayjs';
import { h } from 'vue';
import { DataStatusTag } from 'lyra-component-vue3';

export const columnsIndex = (bool = true) => {
  let data:any = [
    {
      title: '编号',
      dataIndex: 'number',
    },
    {
      title: '名称',
      dataIndex: 'name',
      slots: { customRender: 'name' },
    },
    {
      title: '计划提交时间',
      dataIndex: 'planSubmitTime',
      customRender: ({ record: { planSubmitTime } }) => getDateTime(planSubmitTime, 'YYYY-MM-DD'),
    },
    {
      title: '编写人',
      dataIndex: 'writerName',
    },
    {
      title: '当前责任方',
      dataIndex: 'resPersonName',
    },
    {
      title: '责任部门',
      dataIndex: 'resDeptName',
    },
    {
      title: '状态',
      dataIndex: 'dataStatus',
      customRender({ record }) {
        return h(DataStatusTag, {
          statusData: record.dataStatus,
        });
      },
    },
    {
      title: '类型',
      dataIndex: 'typeName',
    },
    {
      title: '文件状态',
      dataIndex: 'fileStatus',
    },
    {
      title: '版本',
      dataIndex: 'revId',
    },
    {
      title: '是否挂载技术文件',
      dataIndex: 'existDeliverable',
    },

  ];
  if (bool) {
    data.push({
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 120,
      slots: { customRender: 'action' },
    });
  }
  return data;
};
export const getDateTime = (time, tmp = 'YYYY-MM-DD HH:mm:ss') => {
  if (!time) return '';
  return dayjs(time).format(tmp);
};
