package com.chinasie.orion.domain.entity;

import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.UserDataBind;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;

/**
 * MarketContractMilestoneAcceptance Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-30 01:59:34
 */
@TableName(value = "pms_market_contract_milestone_acceptance")
@ApiModel(value = "MarketContractMilestoneAcceptanceEntity对象", description = "市场合同里程碑验收信息")
@Data

public class MarketContractMilestoneAcceptance extends  ObjectEntity  implements Serializable{

    /**
     * 里程碑id
     */
    @ApiModelProperty(value = "里程碑id")
    @TableField(value = "milestone_id")
    private String milestoneId;

    /**
     * 验收人
     */
    @ApiModelProperty(value = "验收人")
    @TableField(value = "acceptance_user_id")
    @FieldBind(dataBind = UserDataBind.class, target = "acceptanceUserName")
    private String acceptanceUserId;

    /**
     * 验收人名称
     */
    @ApiModelProperty(value = "验收人名称")
    @TableField(exist = false)
    private String acceptanceUserName;

    /**
     * 实际验收日期
     */
    @ApiModelProperty(value = "实际验收日期")
    @TableField(value = "actual_accept_date")
    private Date actualAcceptDate;

    /**
     * 本次验收比例
     */
    @ApiModelProperty(value = "本次验收比例")
    @TableField(value = "acceptance_ratio")
    private BigDecimal acceptanceRatio;

    /**
     * 本次验收金额
     */
    @ApiModelProperty(value = "本次验收金额")
    @TableField(value = "acceptance_amt")
    private BigDecimal acceptanceAmt;

}
