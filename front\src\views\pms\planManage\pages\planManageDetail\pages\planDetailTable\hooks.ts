import { ReturnDrawerMethods } from 'lyra-component-vue3';
import { computed, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { keys } from 'lodash-es';
import { getTableOptions as getTableOptionsFn } from './tableOptions';
import { ToolbarButtonEnums } from './types';
import {
  closePlan, deletePlan, exportPlan, getPlanTree, issuePlan, publishPlan,
} from '/@/views/pms/api';

interface IUseProjectPlan {
  planId: string,
  createPlanDrawerMethods: ReturnDrawerMethods
  relevancePlanDrawerMethods: ReturnDrawerMethods
}

export function useProjectPlan(props: IUseProjectPlan) {
  const { createPlanDrawerMethods, relevancePlanDrawerMethods } = props;
  const router = useRouter();

  const state = reactive({
    tableData: [],
  });

  init();

  function init() {
    updateTableData();
  }

  async function updateTableData() {
    state.tableData = await getPlanTree(props.planId);
  }

  function isSingleSelected(keys: string[]) {
    if (keys.length === 1) {
      return true;
    }
    message.warn('不可操作多条数据');
    return false;
  }

  function createPlanDrawerOpen(selected:{keys: string[], rows: any[]}) {
    if (selected?.keys.length > 1) {
      message.warn('不可选择多条操作');
      return;
    }

    const planId = selected?.keys?.[0] || props.planId;
    createPlanDrawerMethods.openDrawer(
      true,
      {
        type: 'add',
        parentId: planId,
        successChange() {
          updateTableData();
        },
      },
    );
  }

  // 顶部工具按钮点击
  function toolbarClick(type:ToolbarButtonEnums, selected: {keys: string[], rows: any[]}) {
    switch (type) {
      // 创建计划
      case ToolbarButtonEnums.CREATE:
        createPlanDrawerOpen(selected);
        break;
      case ToolbarButtonEnums.PUBLISH:
        // 发布计划
        if (isSingleSelected(selected.keys)) {
          publishPlan(selected.keys[0]).then(() => {
            message.success('操作成功');
            updateTableData();
          });
        }
        break;
      case ToolbarButtonEnums.CLOSE:
        // 关闭计划
        if (isSingleSelected(selected.keys)) {
          closePlan(selected.keys[0]).then(() => {
            message.success('操作成功');
            updateTableData();
          });
        }
        break;
      case ToolbarButtonEnums.ISSUED:
        // 下发计划
        if (isSingleSelected(selected.keys)) {
          issuePlan(selected.keys[0]).then(() => {
            message.success('操作成功');
            updateTableData();
          });
        }
        break;
      case ToolbarButtonEnums.EXPORT:
        // 批量导出
        exportPlan(props.planId).then(() => {
          updateTableData();
        });
        break;
    }
  }

  // 点击操作栏
  function actionClick(key: string, record: any) {
    switch (key) {
      case 'create':
        createPlanDrawerMethods.openDrawer(
          true,
          {
            type: 'add',
            parentId: record?.id,
            successChange() {
              message.success('操作成功');
              updateTableData();
            },
          },
        );
        break;
      case 'relevance':
        // 关联计划
        relevancePlanDrawerMethods.openDrawer(
          true,
          {
            planId: record?.id,
            superId: props.planId,
            successChange() {
              message.success('操作成功');
            },
          },
        );
        break;
      case 'delete':
        deletePlan([record.id]).then(() => {
          message.success('操作成功');
          updateTableData();
        });
        break;
      case 'edit':
        createPlanDrawerMethods.openDrawer(
          true,
          {
            type: 'edit',
            parentId: record?.id,
            record,
            successChange() {
              message.success('操作成功');
              updateTableData();
            },
          },
        );
        break;
    }
  }

  // 点击名称
  function nameClick(record) {
    router.push({
      name: 'ComprehensivePlanDetail',
      query: {
        id: record?.id,
      },
    });
  }

  function importSuccess() {
    updateTableData();
  }

  function getTableOptions() {
    return getTableOptionsFn({
      toolbarClick,
      nameClick,
      actionClick,
      importSuccess,
    });
  }

  return {
    getTableOptions,
    tableData: computed(() => state.tableData),
  };
}
