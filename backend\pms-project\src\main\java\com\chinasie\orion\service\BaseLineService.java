package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.PlanBaseLineDTO;
import com.chinasie.orion.domain.entity.PlanBaseLine;
import com.chinasie.orion.mybatis.service.OrionBaseService;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/06/15:55
 * @description:
 */
public  interface BaseLineService extends OrionBaseService<PlanBaseLine> {
    /**
     *  递归复制计划
     * @param baseLineDTOList
     * @return
     */
    boolean copyPlanRecursion(List<PlanBaseLineDTO> baseLineDTOList) throws Exception;

    /**
     *  通过基线ID获取 基线备份的数据信息
     * @param id
     * @return
     */
    List<PlanBaseLineDTO> getListByBaseId(String id) throws Exception;


    /**
     *  通过基线ID删除数据
     * @param id
     * @return
     * @throws Exception
     */
    boolean delById(String id) throws Exception;
}

