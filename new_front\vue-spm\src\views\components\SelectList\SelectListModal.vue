<template>
  <BasicModal
    width="80%"
    title="选择角色"
    :height="600"
    @register="modalRegister"
  >
    <div class="add-role-user">
      <div class="add-role-user-left">
        <ATree
          v-model:selectedKeys="state.selectedTreeKeys"
          :tree-data="state.treeData"
          class="modalTreeContent"
          :field-names="{title:'name',key:'id'}"
          @select="selectNode"
        />
      </div>
      <div class="add-role-user-content">
        <orion-table
          ref="tableRef"
          :options="options"
          @smallSearch="keywordSearch"
        />
      </div>
      <div class="add-role-user-right">
        <RightList
          :selectedUser="selectTableData"
          @deleteUser="deleteSelectedUser"
        />
      </div>
    </div>
    <template #footer>
      <div class="footer-btn">
        <BasicButton @click="cancel">
          取消
        </BasicButton>
        <BasicButton
          type="primary"
          :disabled="selectedRowKeys.length===0"
          @click="confirm"
        >
          确定
        </BasicButton>
      </div>
    </template>
  </BasicModal>
</template>
<script lang="ts" setup>
import {
  BasicModal, useModalInner, OrionTable, BasicButton,
} from 'lyra-component-vue3';
import {
  Ref, ref, computed, defineEmits, defineProps, reactive, onMounted,
} from 'vue';
import Api from '/@/api';
import { Tree as ATree } from 'ant-design-vue';
import RightList from './RightList.vue';

const props = defineProps({
  getTableData: {
    type: Function,
    default: null,
  },
  getTreeApi: {
    type: Function,
    default: null,
  },
  columns: {
    type: Array,
    default: () => [],
  },
  showLeftTree: {
    type: Boolean,
    default: false,
  },
  isTableTree: {
    type: Boolean,
    default: false,
  },
  selectType: {
    type: String,
    default: 'check',
  },
});

const emit = defineEmits(['confirm']);
const selectTableData:Ref<Record<any, any>[]> = ref([]);
const selectedRowKeys:Ref<string[]> = ref([]);
const [modalRegister, { closeModal, setModalProps }] = useModalInner((modalData) => {
  selectTableData.value = [];
  selectedRowKeys.value = [];
  if (modalData.title) {
    setModalProps({ title: modalData.title });
  }
  if (props.showLeftTree) {
    if (state.selectedTreeKeys.length === 0 && state.treeData.length > 0) {
      state.selectedTreeKeys = [state.treeData[0].id];
      tableRef.value.reload({ page: 1 });
    }
  }
});
const state = reactive({
  treeData: [],
  selectedTreeKeys: [],
});
const keyword:Ref<string> = ref('');
const tableRef = ref();
function keywordSearch(val: string) {
  keyword.value = val;
  tableRef.value.reload();
}
const options = ref({
  deleteToolButton: 'add|enable|disable|delete',
  pagination: !props.isTableTree,
  showIndexColumn: !props.isTableTree,
  rowSelection: {
    type: props.selectType,
    selectedRowKeys: computed(() => selectedRowKeys.value),
    onSelect: (record, selected, selectedRows, nativeEvent) => {
      if (selected) {
        if (props.selectType === 'radio') {
          selectedRowKeys.value = [record.id];
          selectTableData.value = [record];
        } else {
          selectedRowKeys.value.push(record.id);
          selectTableData.value.push(record);
        }
      } else {
        selectTableData.value.splice(selectedRowKeys.value.indexOf(record.id), 1);
        selectedRowKeys.value.splice(selectedRowKeys.value.indexOf(record.id), 1);
      }
    },
    onSelectAll: (selected, selectedRows, changeRows) => {
      let tableData = tableRef.value.getDataSource();
      if (selected) {
        tableData.forEach((item) => {
          if (selectedRowKeys.value.indexOf(item.id) < 0) {
            selectedRowKeys.value.push(item.id);
            selectTableData.value.push(item);
          }
        });
      } else {
        tableData.forEach((item) => {
          selectTableData.value.splice(selectedRowKeys.value.indexOf(item.id), 1);
          selectedRowKeys.value.splice(selectedRowKeys.value.indexOf(item.id), 1);
        });
        // let tableData=tabl
      }
    },
  },
  showTableSetting: false,
  // 数据接口
  api: (params) => {
    if (!props.getTableData) {
      return new Promise((resolve, reject) => resolve([]));
    }
    if (props.showLeftTree) {
      if (state.selectedTreeKeys.length === 0) {
        return new Promise((resolve, reject) => resolve([]));
      }
      return props.getTableData(state.selectedTreeKeys[0], params);
    }
    return props.getTableData(params);
  },
  // 展示的列数据
  columns: props.columns.filter((item) => item.dataIndex !== 'action') || [],
});
function deleteSelectedUser(record) {
  if (record) {
    selectTableData.value.splice(selectedRowKeys.value.indexOf(record.id), 1);
    selectedRowKeys.value.splice(selectedRowKeys.value.indexOf(record.id), 1);
  } else {
    selectTableData.value = [];
    selectedRowKeys.value = [];
  }
}
function cancel() {
  closeModal();
}
function confirm() {
  emit('confirm', selectedRowKeys.value, selectTableData.value);
  closeModal();
}
onMounted(() => {
  if (props.showLeftTree && props.getTreeApi) {
    props.getTreeApi().then((res) => {
      state.treeData = res;
    });
  }
});
const selectNode = (id) => {
  tableRef.value.reload({ page: 1 });
};
</script>
<style lang="less" scoped>
.add-role-user{
  display: flex;
  height: 100%;
  .add-role-user-left{
    width: 250px;
    border-right: 1px solid #dddddd;
    padding: 10px;
    flex: 1;
    overflow: auto;
    :deep(.modalTreeContent){
      .ant-tree-treenode {
        width: 100%;
        padding: 5px 0px;
        position: relative;
        &:hover {
          color: ~`getPrefixVar('primary-color')`;
        }
      }

      .ant-tree-node-content-wrapper {
        width: 100%;
        &:hover {
          background: none;
        }
      }
      .ant-tree-treenode-selected{
        background: ~`getPrefixVar('primary-color-deprecated-f-12')`;
        color: ~`getPrefixVar('primary-color')`;
        .ant-tree-node-selected {
          background: none;
        }
      }
    }
  }
  .add-role-user-content{
    width:calc(~'100% - 470px');
    height: 600px;
    overflow: hidden;
  }
  .add-role-user-right{
    border-left: 1px solid ~`getPrefixVar('border-color-base')`;
    width: 220px;
  }
}
.basic-button{
   margin-left: 0 !important;
   &:last-child{
     margin-right: 0;
   }
 }
</style>