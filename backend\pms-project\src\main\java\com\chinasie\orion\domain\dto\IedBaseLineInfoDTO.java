package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.lang.String;

/**
 * IedBaseLineInfo DTO对象
 *
 * <AUTHOR>
 * @since 2024-01-29 13:50:13
 */
@ApiModel(value = "IedBaseLineInfoDTO对象", description = "ied基线信息表")
@Data
public class IedBaseLineInfoDTO implements Serializable{

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @NotEmpty(message = "所属项目不能为空")
    private String projectId;

    /**
     * 名称
     */
    @NotEmpty(message = "名称不能为空")
    @Size(max = 64, message = "名称过长")
    @ApiModelProperty(value = "名称")
    private String name;


    /**
     * 描述/备注
     */
    @ApiModelProperty(value = "描述/备注")
    @Size(max = 255, message = "名称过长")
    private String remark;

    private String id;

}
