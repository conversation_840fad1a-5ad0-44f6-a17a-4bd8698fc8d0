<template>
  <Layout class="ui-2-0">
    <div class="content-top">
      <a-row class="mb10">
        <a-col :span="12">
          <a-dropdown :trigger="['click']">
            <a
              class="ant-dropdown-link title"
              @click.prevent
            >
              {{ currentName_ }}
              <DownOutlined />
            </a>
            <template #overlay>
              <a-menu v-model:value="currentId_">
                <a-menu-item
                  v-for="s in list"
                  :key="s.id"
                  @click="handleMenu(s)"
                >
                  {{ s.name }}
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
          <br>
          <a-space
            :size="40"
            class="sub-title"
          >
            <span>共 {{ currentRow?.count }} 条数据</span>
            <span>最近一次修改人：{{ currentRow?.modifyName }}</span>
            <span>修改时间：{{ getDateTime(currentRow?.modifyTime) }}</span>
          </a-space>
        </a-col>
        <a-col :span="12">
          <div class="fr">
            <a-space :size="20">
              <BasicButton
                @click="$emit('back')"
              >
                <RollbackOutlined />返回
              </BasicButton>
            </a-space>
          </div>
        </a-col>
      </a-row>
    </div>

    <OrionTable
      ref="tableRef"
      v-loading="loading"
      :options="options"
      :dataSource="dataSource"
    />
  </Layout>
</template>

<script lang="ts">
import {
  Col, Dropdown, Menu, Row, Space,
} from 'ant-design-vue';
import {
  BasicButton, isPower, Layout, OrionTable,
} from 'lyra-component-vue3';
import { DownOutlined, RollbackOutlined } from '@ant-design/icons-vue';
import Api from '/@/api';
import {
  computed, onMounted, reactive, toRefs,
} from 'vue';
import {
  columnsIndex,
  getDateTime,
} from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/technicalList/data';

export default {
  name: 'LineDetails',
  components: {
    OrionTable,
    BasicButton,
    AMenu: Menu,
    AMenuItem: Menu.Item,
    DownOutlined,
    ADropdown: Dropdown,
    ASpace: Space,
    RollbackOutlined,
    ARow: Row,
    ACol: Col,
    Layout,
  },
  props: {
    currentId: String,
    currentName: String,
    formId: String,
  },
  emits: ['back', 'query'],
  setup(props, { emit }) {
    const state = reactive({
      list: [],
      currentId_: props.currentId,
      currentName_: props.currentName,
      dataSource: [],
      loading: false,
      currentRow: computed(() => {
        const list = state.list.filter((item) => item.id === state.currentId_);
        return list[0] || {};
      }),
    });

    const options = {
      deleteToolButton: 'add|delete|enable|disable',
      showSmallSearch: false,
      columns: columnsIndex(false),

    };

    async function getLine(id) {
      try {
        state.loading = true;
        const url = `/pms/ied-base-line-info/ied/${id}`;
        state.dataSource = await new Api(url).fetch('', '', 'GET');
      } finally {
        state.loading = false;
      }
    }
    function handleMenu(item) {
      state.currentId_ = item.id;
      state.currentName_ = item.name;
      getLine(item.id);
    }

    async function getListData() {
      const url = `/pms/ied-base-line-info/list?projectId=${props.formId}`;
      state.list = await new Api(url).fetch('', '', 'GET');
    }

    onMounted(() => {
      getLine(props.currentId);
      getListData();
    });
    return {
      ...toRefs(state),
      handleMenu,
      isPower,
      options,
      getDateTime,
    };
  },
};
</script>

<style scoped lang="less">
  .ui-2-0 {
    width: 100% !important;
  }
  .title {
    font-size: 16px;
    color: #1e1e1e;
  }
  .sub-title {
    font-size: 12px;
    color: #969eb4;
  }

  .content-top {
    margin: ~`getPrefixVar('content-padding-top')` ~`getPrefixVar('content-padding-left')` 0 ~`getPrefixVar('content-padding-left')`;
    border-bottom: 1px solid  ~`getPrefixVar('border-color-base')`
  }
</style>
