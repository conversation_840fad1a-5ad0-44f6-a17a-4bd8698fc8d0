package com.chinasie.orion.service;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.TakeEffectDTO;
import com.chinasie.orion.domain.dto.lifecycle.ProjectLifeCycleTemplateDTO;
import com.chinasie.orion.domain.entity.ProjectLifeCycleTemplate;
import com.chinasie.orion.domain.vo.ProjectLifeCycleTemplateVO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

/**
 * <p>
 * ProjectLifeCycleTemplate 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-22 18:07:01
 */
public interface ProjectLifeCycleTemplateService extends OrionBaseService<ProjectLifeCycleTemplate> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    ProjectLifeCycleTemplateDTO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param projectLifeCycleTemplateDTO
     */
    String create(ProjectLifeCycleTemplateDTO projectLifeCycleTemplateDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param projectLifeCycleTemplateDTO
     */
    Boolean edit(ProjectLifeCycleTemplateDTO projectLifeCycleTemplateDTO) throws Exception;

    void handleFile(String id, List<FileVO> newFileDtoList) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ProjectLifeCycleTemplateVO> pages(Page<ProjectLifeCycleTemplateDTO> pageRequest) throws Exception;

    /**
     * 启用禁用
     *
     * @param takeEffectDTO
     * @return
     * @throws Exception
     */
    Boolean takeEffectBatch(TakeEffectDTO takeEffectDTO) throws Exception;

    /**
     * 复制全生命周期说明模板
     *
     * @param ids
     * @return
     * @throws Exception
     */
    List<String> copy(List<String> ids) throws Exception;
}
