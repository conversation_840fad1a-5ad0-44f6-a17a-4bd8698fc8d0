package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.chinasie.orion.excel.annotations.ExcelSelected;
import com.chinasie.orion.xlsx.RspDeptExcelSelectHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;


/**
 * ProjectSchemeExcelDTO
 *
 * @author: yangFy
 * @date: 2023/4/21 18:33
 * @description:
 * <p>
 * 项目计划导入模版DTO
 * </p>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectSchemeTempleteDownloadExcelDTO implements Serializable {
    @ExcelProperty("计划名称*")
    private String name;
    @ExcelProperty("计划类型*")
    private String schemeType;
    @ExcelProperty("责任处室*")
    private String rspSubDeptName;
    @ExcelProperty("责任人*")
    private String rspUserName;
    @ExcelProperty("开始时间*")
    @DateTimeFormat(value = "yyyy-MM-dd")
    private Date beginTime;
    @ExcelProperty("结束时间*")
    @DateTimeFormat(value = "yyyy-MM-dd")
    private Date endTime;
    @ExcelProperty(value = "实际开始时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    private Date actualEndTime;
    @ExcelProperty(value = "实际结束时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    private String statusName;
    @ExcelProperty(value = "计划下发时间")
    private Date issueTime;
    @ExcelProperty("计划描述说明*")
    private String schemeDesc;

}
