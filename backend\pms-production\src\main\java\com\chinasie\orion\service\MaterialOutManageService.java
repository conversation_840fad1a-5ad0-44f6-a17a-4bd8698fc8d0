package com.chinasie.orion.service;


import com.chinasie.orion.domain.entity.MaterialOutManage;
import com.chinasie.orion.domain.dto.MaterialOutManageDTO;
import com.chinasie.orion.domain.vo.MaterialOutManageVO;
import java.lang.String;
import java.util.List;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import java.util.Map;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * MaterialOutManage 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 17:08:55
 */
public interface MaterialOutManageService extends OrionBaseService<MaterialOutManage> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    MaterialOutManageVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param materialOutManageDTO
     */
    String create(MaterialOutManageDTO materialOutManageDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param materialOutManageDTO
     */
    Boolean edit(MaterialOutManageDTO materialOutManageDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<MaterialOutManageVO> pages(Page<MaterialOutManageDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<MaterialOutManageVO> vos) throws Exception;

    /**
     * 获取物资的出库信息
     * <p>
     * * @param materialIdList
     */
    Map<String, MaterialOutManage> getMateriaIdToLastOutMaterial(List<String> materialIdList);
}
