package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * MajorRepairPlanMember Entity对象
 *
 * <AUTHOR>
 * @since 2024-07-30 19:20:50
 */
@TableName(value = "pmsx_major_repair_plan_member")
@ApiModel(value = "MajorRepairPlanMemberEntity对象", description = "大修计划成员")
@Data

public class MajorRepairPlanMember extends  ObjectEntity  implements Serializable{

    /**
     * 用户编码（工号）
     */
    @ApiModelProperty(value = "用户编码（工号）")
    @TableField(value = "user_code")
    private String userCode;


    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    @TableField(value = "major_repair_turn")
    private String majorRepairTurn;


    /**
     * 角色code
     */
    @ApiModelProperty(value = "角色code")
    @TableField(value = "role_code")
    private String roleCode;


    /**
     * 业务ID--角色业务ID
     */
    @ApiModelProperty(value = "业务ID--角色业务ID")
    @TableField(value = "business_id")
    private String businessId;



    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    @TableField(value = "user_id")
    private String userId;


    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    @TableField(value = "user_name")
    private String userName;

}
