<script setup lang="ts">
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import { h, ref } from 'vue';
import { DataStatusTag } from 'lyra-component-vue3';

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});

const basicInfoList = ref([
  {
    label: '编号',
    field: 'number',
  },
  {
    label: '名称',
    field: 'name',
  },
  {
    label: '计划提交时间',
    field: 'planSubmitTime',
    formatTime: 'YYYY-MM-DD',
  },
  {
    label: '编写人',
    field: 'writerName',
  },
  {
    label: '当前责任方',
    field: 'resPersonName',
  },
  {
    label: '责任部门',
    field: 'resDeptName',
  },
  {
    label: '状态',
    field: 'dataStatus',
    customRender({ record }) {
      return h(DataStatusTag, {
        statusData: record.dataStatus,
      });
    },
  },
  {
    label: '类型',
    field: 'typeName',
  },
  {
    label: '文件状态',
    field: 'fileStatus',
  },
  {
    label: '版本',
    field: 'revId',
  },
  {
    label: '创建时间',
    field: 'createTime',
    formatTime: 'YYYY-MM-DD HH:mm:ss',
  },

]);
</script>

<template>
  <DetailsLayout
    title="基本信息"
    :list="basicInfoList"
    :dataSource="$props?.data"
    :column="4"
  />
</template>

<style scoped lang="less">

</style>
