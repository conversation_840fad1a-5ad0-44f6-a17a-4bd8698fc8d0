package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * UserSatisfactionEvaluation Entity对象
 *
 * <AUTHOR>
 * @since 2024-10-30 14:31:17
 */
@TableName(value = "pmsx_user_satisfaction_evaluation")
@ApiModel(value = "UserSatisfactionEvaluationEntity对象", description = "人员满意度评价")
@Data

public class UserSatisfactionEvaluation extends  ObjectEntity  implements Serializable{


    /**
     * 年
     */
    @ApiModelProperty(value = "年")
    @TableField(value = "data_year")
    private Integer dataYear;


    /**
     * 中心编号
     */
    @ApiModelProperty(value = "中心编号")
    @TableField(value = "org_code")
    private String orgCode;

    /**
     * 中心名称
     */
    @ApiModelProperty(value = "中心名称")
    @TableField(value = "org_name")
    private String orgName;

    /**
     * 部门编号
     */
    @ApiModelProperty(value = "部门编号")
    @TableField(value = "dept_code")
    private String deptCode;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    @TableField(value = "dept_name")
    private String deptName;

    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    @TableField(value = "user_code")
    private String userCode;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    @TableField(value = "user_name")
    private String userName;

    /**
     * 供应商编号
     */
    @ApiModelProperty(value = "供应商编号")
    @TableField(value = "supplier_no")
    private String supplierNo;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @TableField(value = "supplier_name")
    private String supplierName;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_no")
    private String contractNo;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @TableField(value = "contract_name")
    private String contractName;

    /**
     * 岗级
     */
    @ApiModelProperty(value = "岗级")
    @TableField(value = "job_grade")
    private String jobGrade;

    /**
     * 1季度评价
     */
    @ApiModelProperty(value = "1季度评价")
    @TableField(value = "one_quarter")
    private String oneQuarter;

    /**
     * 2季度评分
     */
    @ApiModelProperty(value = "2季度评分")
    @TableField(value = "two_quarter")
    private String twoQuarter;

    /**
     * 3季度评分
     */
    @ApiModelProperty(value = "3季度评分")
    @TableField(value = "three_quarter")
    private String threeQuarter;

    /**
     * 年度评价
     */
    @ApiModelProperty(value = "年度评价")
    @TableField(value = "four_quarter")
    private String fourQuarter;

}
