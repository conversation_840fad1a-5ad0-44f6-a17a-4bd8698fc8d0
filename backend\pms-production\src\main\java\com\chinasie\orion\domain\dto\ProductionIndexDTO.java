package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ProductionIndex DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-18 17:49:18
 */
@ApiModel(value = "ProductionIndexDTO对象", description = "生产看板指标维护")
@Data
@ExcelIgnoreUnannotated
public class ProductionIndexDTO extends ObjectDTO implements Serializable {

    /**
     * 指标编码
     */
    @ApiModelProperty(value = "指标编码")
    @ExcelProperty(value = "指标编码 ", index = 0)
    private String indexNumber;

    /**
     * 考核类型
     */
    @ApiModelProperty(value = "考核类型")
    @ExcelProperty(value = "考核类型 ", index = 1)
    private String indexType;

    /**
     * 目标值
     */
    @ApiModelProperty(value = "目标值")
    @ExcelProperty(value = "目标值 ", index = 2)
    private String indexTarget;

    /**
     * 实际值
     */
    @ApiModelProperty(value = "实际值")
    @ExcelProperty(value = "实际值 ", index = 3)
    private String indexActual;

    /**
     * 状态灯
     */
    @ApiModelProperty(value = "状态灯")
    @ExcelProperty(value = "状态灯 ", index = 4)
    private String indexStatus;


}
