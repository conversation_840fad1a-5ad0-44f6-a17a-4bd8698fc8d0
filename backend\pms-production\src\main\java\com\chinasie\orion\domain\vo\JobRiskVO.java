package com.chinasie.orion.domain.vo;

import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.util.List;
/**
 * JobRisk VO对象
 *
 * <AUTHOR>
 * @since 2024-06-09 15:47:53
 */
@ApiModel(value = "JobRiskVO对象", description = "作业与风险关联")
@Data
public class JobRiskVO extends  ObjectVO   implements Serializable{

            /**
         * 风险ID
         */
        @ApiModelProperty(value = "风险ID")
        private String jobId;
        /**
         * 作业id
         */
        @ApiModelProperty(value = "作业名称")
        private String jobName;

        /**
         * 作业id
         */
        @ApiModelProperty(value = "作业编号")
        private String jobNumber;
        /**
         * 功能位置
         */
        @ApiModelProperty(value = "功能位置")
        private String functionalLocation;


        /**
         * 风险号
         */
        @ApiModelProperty(value = "风险号")
        private String riskNumber;


        /**
         * 风险类型
         */
        @ApiModelProperty(value = "风险类型")
        private String riskType;


        /**
         * 风险长文本
         */
        @ApiModelProperty(value = "风险长文本")
        private String riskText;


        /**
         * 风险描述
         */
        @ApiModelProperty(value = "风险描述")
        private String riskDesc;


        @ApiModelProperty(value = "订单号")
        private String jobCode;
        @ApiModelProperty(value = "风险分析")
        private String riskAnaly;
        @ApiModelProperty(value = "计划工厂")
        private String planFactory;
        @ApiModelProperty(value = "风险代码")
        private String riskCode;
        @ApiModelProperty(value = "md5_value")
        private String encryKey;


        @ApiModelProperty(value = "安措信息")
        private List<JobSecurityMeasureVO> securityMeasureVOList;
}
