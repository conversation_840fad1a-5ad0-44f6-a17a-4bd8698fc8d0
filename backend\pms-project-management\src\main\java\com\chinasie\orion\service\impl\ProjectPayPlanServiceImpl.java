package com.chinasie.orion.service.impl;


import com.chinasie.orion.domain.dto.ProjectPayNormalPurchaseDTO;
import com.chinasie.orion.domain.dto.ProjectPayPlanDTO;
import com.chinasie.orion.domain.entity.ProjectPayActual;
import com.chinasie.orion.domain.entity.ProjectPayPlan;
import com.chinasie.orion.domain.vo.ProjectPayPlanVO;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ProjectPayPlanMapper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectPayPlanService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * ProjectPayPlan 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04 00:48:13
 */
@Service
@Slf4j
public class ProjectPayPlanServiceImpl extends  OrionBaseServiceImpl<ProjectPayPlanMapper, ProjectPayPlan>   implements ProjectPayPlanService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProjectPayPlanVO detail(String id, String pageCode) throws Exception {
        ProjectPayPlan projectPayPlan =this.getById(id);
        ProjectPayPlanVO result = BeanCopyUtils.convertTo(projectPayPlan,ProjectPayPlanVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param projectPayPlanDTO
     */
    @Override
    public  String create(ProjectPayPlanDTO projectPayPlanDTO) throws Exception {
        ProjectPayPlan projectPayPlan =BeanCopyUtils.convertTo(projectPayPlanDTO,ProjectPayPlan::new);
        this.save(projectPayPlan);

        String rsp=projectPayPlan.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param projectPayPlanDTO
     */
    @Override
    public Boolean edit(ProjectPayPlanDTO projectPayPlanDTO) throws Exception {
        ProjectPayPlan projectPayPlan =BeanCopyUtils.convertTo(projectPayPlanDTO,ProjectPayPlan::new);

        this.updateById(projectPayPlan);

        String rsp=projectPayPlan.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectPayPlanVO> pages( Page<ProjectPayPlanDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProjectPayPlan> condition = new LambdaQueryWrapperX<>( ProjectPayPlan. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ProjectPayPlan::getCreateTime);
        ProjectPayPlanDTO projectPayActualDTO =  pageRequest.getQuery();
        if(!Objects.isNull(projectPayActualDTO)){
            if(!Objects.isNull(projectPayActualDTO)){
                if(StringUtils.hasText(projectPayActualDTO.getPsphi())) {
                    condition.eq(ProjectPayPlan::getPsphi,projectPayActualDTO.getPsphi());
                }
                if(StringUtils.hasText(projectPayActualDTO.getKstar())){
                    condition.eq(ProjectPayPlan::getKstar,projectPayActualDTO.getKstar());
                }
            }
        }

        Page<ProjectPayPlan> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectPayPlan::new));

        PageResult<ProjectPayPlan> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectPayPlanVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectPayPlanVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectPayPlanVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "预算金额导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectPayPlanDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            ProjectPayPlanExcelListener excelReadListener = new ProjectPayPlanExcelListener();
        EasyExcel.read(inputStream,ProjectPayPlanDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ProjectPayPlanDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("预算金额导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ProjectPayPlan> projectPayPlanes =BeanCopyUtils.convertListTo(dtoS,ProjectPayPlan::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ProjectPayPlan-import::id", importId, projectPayPlanes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ProjectPayPlan> projectPayPlanes = (List<ProjectPayPlan>) orionJ2CacheService.get("pmsx::ProjectPayPlan-import::id", importId);
        log.info("预算金额导入的入库数据={}", JSONUtil.toJsonStr(projectPayPlanes));

        this.saveBatch(projectPayPlanes);
        orionJ2CacheService.delete("pmsx::ProjectPayPlan-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ProjectPayPlan-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ProjectPayPlan> condition = new LambdaQueryWrapperX<>( ProjectPayPlan. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ProjectPayPlan::getCreateTime);
        List<ProjectPayPlan> projectPayPlanes =   this.list(condition);

        List<ProjectPayPlanDTO> dtos = BeanCopyUtils.convertListTo(projectPayPlanes, ProjectPayPlanDTO::new);

        String fileName = "预算金额数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectPayPlanDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<ProjectPayPlanVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class ProjectPayPlanExcelListener extends AnalysisEventListener<ProjectPayPlanDTO> {

        private final List<ProjectPayPlanDTO> data = new ArrayList<>();

        @Override
        public void invoke(ProjectPayPlanDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ProjectPayPlanDTO> getData() {
            return data;
        }
    }


}
