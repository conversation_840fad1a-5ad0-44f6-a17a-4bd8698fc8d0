INSERT INTO `sys_code_rules`(`id`, `code_number`, `department`, `code_name`, `classification_id`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `rev_key`, `next_rev_id`, `previous_rev_id`, `code`, `rev_id`, `initial_rev_id`, `rev_order`, `share`, `build_in`) VALUES ('9hi11814120694578139136', 'REQ', NULL, '需求新增编码', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', NULL, '314j1000000000000000000', '314j1000000000000000000', '2024-07-19 10:10:53', '314j1000000000000000000', '2024-07-19 10:17:52', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, '58e0cd2a70b74fe38dfd570d99d9d57a', NULL, NULL, NULL, 'A', NULL, 1, b'1', b'1');


INSERT INTO `sys_code_mapping_relation`(`id`, `data_field`, `code_rules`, `data_type`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('e96m1814123011159347200', 'requirementNumber', '9hi11814120694578139136', 'RequirementMangement', 'SysCodeMappingRelation', '', '314j1000000000000000000', '314j1000000000000000000', '2024-07-19 10:20:05', '314j1000000000000000000', '2024-07-19 10:20:05', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');


INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1814121241427300352', '头部', '0', '9hi11814120694578139136', '', 'fixedValue', '1', 'Req', '0', 1, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-07-19 10:13:03', '314j1000000000000000000', '2024-07-19 10:13:03', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1814121412630401024', '年份', '1', '9hi11814120694578139136', '', 'DATE_YY', '1', '', '0', 2, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-07-19 10:13:44', '314j1000000000000000000', '2024-07-19 10:16:08', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1814121653295370240', '月份', '1', '9hi11814120694578139136', '', 'DATE_M', '1', '', '0', 3, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-07-19 10:14:42', '314j1000000000000000000', '2024-07-19 10:15:42', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1814121824041291776', '日', '1', '9hi11814120694578139136', '', 'DATE_D', '1', '', '0', 4, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-07-19 10:15:22', '314j1000000000000000000', '2024-07-19 10:15:22', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1814122196222857216', '流水器', '0', '9hi11814120694578139136', 'f9g6017b6f50410849188667eacdf1a928d8', 'piPer', '1', '', '0', 5, '', '0', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-07-19 10:16:51', '314j1000000000000000000', '2024-07-19 10:16:51', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
