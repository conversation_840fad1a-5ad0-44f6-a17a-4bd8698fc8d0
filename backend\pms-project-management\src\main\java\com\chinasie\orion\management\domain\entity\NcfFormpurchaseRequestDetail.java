package com.chinasie.orion.management.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * NcfFormpurchaseRequestDetail Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-04 10:01:14
 */
@TableName(value = "ncf_form_purchase_request_detail")
@ApiModel(value = "NcfFormpurchaseRequestDetailEntity对象", description = "采购申请行项目表")
@Data
public class NcfFormpurchaseRequestDetail extends ObjectEntity implements Serializable {

    /**
     * 行项目id
     */
    @ApiModelProperty(value = "行项目id")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 内部订单
     */
    @ApiModelProperty(value = "内部订单")
    @TableField(value = "internal_order")
    private String internalOrder;

    /**
     * 物料
     */
    @ApiModelProperty(value = "物料")
    @TableField(value = "item")
    private String item;

    /**
     * 物料组
     */
    @ApiModelProperty(value = "物料组")
    @TableField(value = "item_group")
    private String itemGroup;

    /**
     * 总账科目
     */
    @ApiModelProperty(value = "总账科目")
    @TableField(value = "general_ledger_subject")
    private String generalLedgerSubject;

    /**
     * 资产
     */
    @ApiModelProperty(value = "资产")
    @TableField(value = "asset")
    private String asset;

    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    @TableField(value = "required_quantity")
    private String requiredQuantity;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    @TableField(value = "unit")
    private String unit;

    /**
     * 交货时间
     */
    @ApiModelProperty(value = "交货时间")
    @TableField(value = "delivery_time")
    private Date deliveryTime;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    @TableField(value = "unit_price")
    private String unitPrice;

    /**
     * 总价
     */
    @ApiModelProperty(value = "总价")
    @TableField(value = "total_price")
    private String totalPrice;

    /**
     * 本位币金额
     */
    @ApiModelProperty(value = "本位币金额")
    @TableField(value = "local_currency_amount")
    private BigDecimal localCurrencyAmount;

    /**
     * 成本中心
     */
    @ApiModelProperty(value = "成本中心")
    @TableField(value = "cost_center")
    private String costCenter;

    /**
     * 项目编号/名称
     */
    @ApiModelProperty(value = "项目编号/名称")
    @TableField(value = "project_id_name")
    private String projectIdName;

    /**
     * WBS编号
     */
    @ApiModelProperty(value = "WBS编号")
    @TableField(value = "wbs_id")
    private String wbsId;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @TableField(value = "main_table_id")
    private String mainTableId;

    /**
     * 采购申请号
     */
    @ApiModelProperty(value = "采购申请号")
    @TableField(value = "project_code")
    private String projectCode;

    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    @TableField(value = "project_number")
    private String projectNumber;

}
