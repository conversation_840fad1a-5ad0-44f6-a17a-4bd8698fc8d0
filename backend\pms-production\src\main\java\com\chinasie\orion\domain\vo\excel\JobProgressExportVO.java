package com.chinasie.orion.domain.vo.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/09/27/9:50
 * @description:
 */
@Data
@ExcelIgnoreUnannotated
public class JobProgressExportVO implements Serializable {
    @ExcelProperty(value = "序号 ", index = 0)
    private Integer sort;
    /**
     * 工作日期
     */
    @ApiModelProperty(value = "日期")
    @ExcelProperty(value = "日期 ", index = 1)
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    @ColumnWidth(22)
    private Date workDate;

    /**
     * 总体进展
     */
    @ApiModelProperty(value = "总体进展")
    @ExcelProperty(value = "总体进展 ", index = 2)
    @ColumnWidth(22)
    private String progressSchedule;

    /**
     * 工作进展
     */
    @ApiModelProperty(value = "工作进展")
    @ExcelProperty(value = "工作进展 ", index = 3)
    @ColumnWidth(22)
    private String progressDetail;

    /**
     * 工作进展
     */
    @ApiModelProperty(value = "备注")
    @ExcelProperty(value = "备注 ", index = 4)
    @ColumnWidth(22)
    private String remark;
}
