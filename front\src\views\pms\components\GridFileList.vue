<template>
  <div
    class="files"
    :style="{'grid-template-columns':`repeat(${column}, 1fr)`}"
  >
    <div
      v-for="item in list"
      :key="item.id"
      class="file-item"
    >
      <div class="content">
        <div class="title ">
          <Icon
            class="sie-icon"
            icon="sie-icon-xqgl"
          />
          <span class="file-name flex-te">{{ item.fileName || '' }}</span>
        </div>
        <div class="info">
          <span class="name">{{ item.creatorName || "" }}</span>
          <span class="create-time">{{ dayjs(item?.createTime)?.format('YYYY-MM-DD HH:mm:ss') }}</span>
        </div>
      </div>
      <BasicTableAction
        :actions="actions.value"
        :onActionClick="(action)=>onActionClick(action,item)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { Ref, ref } from 'vue';
import {
  Icon, BasicTableAction, ITableActionItem, downLoadById, openFile,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';

interface Props {
  column?: number,
  list: Array<Record<string, any>>,
  actions?: Ref<ITableActionItem<any>[]>
}

const props = withDefaults(defineProps<Props>(), {
  column: 2,
  actions: () => ref([
    {
      event: 'preview',
      text: '查看',
    },
    {
      event: 'download',
      text: '下载',
    },
  ]),
});

defineEmits<{
  (e: 'actionClick', actionItem: ITableActionItem<any>, record: Record<string, any>): void
}>();

function onActionClick({ event }, record: Record<string, any>) {
  switch (event) {
    case 'preview':
      openFile(record);
      break;
    case 'download':
      downLoadById(record.id);
      break;
  }
}

</script>

<style scoped lang="less">
.files {
  display: grid;
  gap: 16px 60px;
  margin-top: ~`getPrefixVar('content-margin-top')`;
}

.file-item {
  display: flex;
  align-items: center;
  padding-bottom: ~`getPrefixVar('content-padding-top')`;
  color: rgba(0, 0, 0, .65);
  border-bottom: 1px solid ~`getPrefixVar('border-color-base')`;

  .content {
    display: flex;
    flex-direction: column;
    margin-right: 32px;
    flex-grow: 1;

    .title {
      display: flex;
      align-items: center;
      line-height: 20px;

      .sie-icon {
        color: ~`getPrefixVar('primary-color')`;
      }

      .file-name {
        flex-grow: 1;
        width: 0;
        margin-left: ~`getPrefixVar('button-margin')`;
      }
    }

    .info {
      line-height: 20px;
      display: flex;
      align-items: center;
      margin-top: 10px;

      .name {
        margin-right: 32px;
      }

      .create-time {
        margin-right: 8px;
      }
    }
  }
}
</style>
