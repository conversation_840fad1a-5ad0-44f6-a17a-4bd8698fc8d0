package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * IfToParameterToIns Entity对象
 *
 * <AUTHOR>
 * @since 2024-01-31 13:52:16
 */
@TableName(value = "pmsx_if_to_parameter_to_ins")
@ApiModel(value = "IfToParameterToInsEntity对象", description = "意见单和参数和参数实列的关系")
@Data
public class IfToParameterToIns extends ObjectEntity implements Serializable {

    /**
     * 意见单id
     */
    @ApiModelProperty(value = "意见单id")
    @TableField(value = "if_id")
    private String ifId;

    /**
     * 参数ID
     */
    @ApiModelProperty(value = "参数ID")
    @TableField(value = "parameter_id")
    private String parameterId;

    @ApiModelProperty(value = "模板Id")
    @TableField(value = "model_id")
    private String modelId;

    /**
     * 参数实列ID
     */
    @ApiModelProperty(value = "参数实列ID")
    @TableField(value = "ins_id")
    private String insId;

    /**
     * 拷贝类型
     */
    @ApiModelProperty(value = "拷贝类型")
    @TableField(value = "copy_type")
    private String copyType;

}
