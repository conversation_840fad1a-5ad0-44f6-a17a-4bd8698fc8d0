<template>
  <div class="modal-main-wrap">
    <div class="left-wrap">
      <Menu
        v-model:selectedKeys="selectedKeys"
        class="menu-class"
        @click="handleClick"
      >
        <MenuItem
          v-for="item in menuOptions"
          :key="item.id"
          class="pl50"
        >
          {{ item.name }}
        </MenuItem>
      </Menu>
    </div>
    <div class="center-wrap">
      <div
        class="table-wrap"
      >
        <OrionTable
          ref="tableRef"
          :options="tableOptions"
        >
          <template #toolbarLeft>
            <BasicButton
              v-for="button in toolButtons"
              v-bind="button"
              :key="button.event"
              @click="handleToolButton(button.event)"
            >
              {{ button.text }}
            </BasicButton>
          </template>
        </OrionTable>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { OrionTable, BasicButton, openModal } from 'lyra-component-vue3';
import {
  onMounted, ref, Ref, computed, h,
} from 'vue';
import {
  Menu, MenuItem, message, Modal,
} from 'ant-design-vue';
// import { openFormDrawer } from './utils';
import { useRouter } from 'vue-router';
import Api from '/@/api';
import PerformanceModal from './performanceModal/index.vue';
import FormDrawer from '/@/views/pms/projectPerformanceLibrary/components/FormDrawer.vue';
const router = useRouter();
const tableRef: Ref = ref();
const selectedKeys = ref([]);
const selectedMenuId = ref('');
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const menuOptions = ref();

const tableOptions = {
  showToolButton: false,
  smallSearchField: ['name'],
  immediate: false,
  rowSelection: {
    onChange(keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  api: (params: Record<string, any>) => new Api(`/pms/performanceTemplate/${selectedMenuId.value}`).fetch(params, '', 'GET').then((res) => res.performanceTemplateToIndicatorDTOS),
  columns: [
    {
      title: '绩效指标',
      dataIndex: 'name',
    },
    {
      title: '权重',
      dataIndex: 'weight',
    },
    {
      title: '评分标准',
      dataIndex: 'scoreStandard',
    },
    {
      title: '状态',
      dataIndex: 'status',
      slots: { customRender: 'status' },
    },
    {
      title: '修改人',
      dataIndex: 'modifyName',
      width: 80,
    },
    {
      title: '修改时间',
      dataIndex: 'modifyTime',
      slots: { customRender: 'modifyTime' },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 120,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '编辑',
      onClick(record: Record<string, any>) {
        // openFormDrawer('', record, updateTable);
      },
    },
    {
      text: '删除',
      modal: (record: Record<string, any>) => deleteApi([record?.id]),
    },
  ],
};

onMounted(() => {
  getTemplateList();
});
async function getTemplateList() {
  const res = await new Api('/pms/performanceTemplate/list').fetch('', '', 'POST');
  menuOptions.value = res;

  selectedKeys.value = [res[0].id];
  selectedMenuId.value = res[0].id;
  updateTable();
}
function handleClick({ item, key, keyPath }) {
  selectedKeys.value = [key];
  selectedMenuId.value = key;
  updateTable();
}
const toolButtons = computed(() => [
  {
    event: 'add',
    text: '新建',
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
  },
  {
    event: 'start',
    text: '启用',
    icon: 'sie-icon-qiyong',
    disabled: (selectedRows.value.length === 0 || selectedRows.value.some((item) => item.status === 130)),
  },
  {
    event: 'disable',
    text: '禁用',
    icon: 'sie-icon-jinyong',
    disabled: (selectedRows.value.length === 0 || selectedRows.value.some((item) => item.status !== 130)),
  },
  {
    event: 'delete',
    text: '删除',
    icon: 'sie-icon-shanchu',
    disabled: selectedRows.value.length === 0,
  },
]);
function onStart(ids) {
  new Api('/pms/indicatorLibrary/enable').fetch(
    ids,
    '',
    'PUT',
  ).then((res) => {
    if (res) {
      message.success('启用成功');
    }
    updateTable();
  });
}
function onDisable(ids) {
  new Api('/pms/indicatorLibrary/disable').fetch(
    ids,
    '',
    'PUT',
  ).then((res) => {
    if (res) {
      message.success('禁用成功');
    }
    updateTable();
  });
}
function openPerformanceModal(templateId, cb?: () => void, isView: boolean = false) {
  const formRef: Ref = ref();
  openModal({
    title: templateId ? '编辑绩效' : '新建绩效',
    width: 1000,
    content() {
      return h(PerformanceModal, {
        ref: formRef,
        templateId,
      });
    },
    footer: {
      isOk: !isView,
    },
    async onOk(): Promise<void> {
      await formRef.value.onSubmit();
      updateTable();
      cb?.();
    },
  });
}
function handleToolButton(operation: string) {
  switch (operation) {
    case 'add':
      // openFormDrawer('', null, updateTable);
      openPerformanceModal(selectedMenuId.value);
      break;
    case 'start':
      onStart(selectedRows.value.map((item) => item.id));
      break;
    case 'disable':
      onDisable(selectedRows.value.map((item) => item.id));
      break;
    case 'delete':
      Modal.confirm({
        title: '删除提示！',
        content: '确认删除？',
        onOk: () => deleteApi(selectedRows.value.map((item) => item.id)),
      });
      break;
  }
}
function deleteApi(ids: string[]) {
  return new Promise((resolve, reject) => {
    new Api('/pms/projectCollection').fetch(
      ids,
      '',
      'DELETE',
    ).then(() => {
      resolve('');
      updateTable();
    }).catch((err) => {
      reject(err);
    });
  });
}
async function updateTable() {
  await tableRef.value?.reload();
}
</script>
<style scoped lang="less">
.modal-main-wrap {
  width: 100%;
  height: 100%;
  display: flex;

  .left-wrap, .right-wrap {
    width: 300px;
    height: 100%;
    flex-shrink: 0;
  }

  .left-wrap {
    display: flex;
    flex-direction: column;
  }

  .center-wrap {
    flex-grow: 1;
    width: 0;
    border-left: 1px solid #f0f0f0;
    border-right: 1px solid #f0f0f0;
  }
}
.table-wrap {
  height: 100%;
  overflow: hidden;
}

:deep(.ant-input) {
  height: 32px;
  line-height: 32px;
}
</style>
