<template>
  <BasicModal
    v-model:visible="$props.visible"
    :width="'600px'"
    title="计划执行确认"
    :bodyStyle="{ height: '300px', overflowY: 'auto' }"
    zIndex="999"
    @register="modalRegister"
    @ok="handleOk"
    @cancel="handleColsed"
  >
    <div class="body">
      <a-form
        ref="formRef"
        :model="form"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              label="实际开始日期"
              name="actualBeginTime"
              required
            >
              <a-date-picker
                v-model:value="form.actualBeginTime"
                :disabledDate="(current)=>{
                  return current>dayjs(form.actualEndTime)
                }"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="实际结束日期"
              name="actualEndTime"
              required
            >
              <a-date-picker
                v-model:value="form.actualEndTime"
                :disabledDate="(current)=>{
                  return dayjs(current).unix() < dayjs(form.actualBeginTime).unix()
                }"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item
          label="计划执行情况说明"
          name="executeDesc"
          required
        >
          <a-textarea
            v-model:value="form.executeDesc"
            placeholder="请输入情况说明"
          />
        </a-form-item>
        <div>
          <BasicUpload
            :max-number="100"
            :accept="'.rar,.jpg,.zip,.pdf,.docx,.doc,.xls,.xlsx,.png'"
            :isClassification="false"
            :isToolRequired="false"
            zIndex="1001"
            @uploadSuccessChange="uploadSuccessChange"
            @saveChange="saveChange"
          />
          <p>支持的扩展名：.rar，.zip，.doc，.docx，.pdf，.jpg...</p>
        </div>
        <div class="field-list">
          <template
            v-for="(item, index) in fieldList || []"
            :key="item.filePath"
          >
            <div class="field-item">
              <div class="field-item-name">
                {{ item.name + '.' + item.filePostfix }}
              </div>
              <i
                class="sie-icon-close"
                @click="deleteField(index)"
              />
            </div>
          </template>
        </div>
      </a-form>
    </div>
  </BasicModal>
</template>
<script lang="ts">
import {
  defineComponent, ref, reactive, unref,
} from 'vue';
import {
  Modal,
  Row,
  Col,
  Form,
  FormItem,
  DatePicker,
  Textarea,
  message,
} from 'ant-design-vue';
import { BasicUpload, BasicModal, useModalInner } from 'lyra-component-vue3';
import { throttle, cloneDeep } from 'lodash-es';
import Api from '/@/api';
import dayjs from 'dayjs';

export default defineComponent({
  name: 'PlanDone',
  components: {
    BasicModal,
    ARow: Row,
    ACol: Col,
    AForm: Form,
    AFormItem: FormItem,
    ADatePicker: DatePicker,
    ATextarea: Textarea,
    BasicUpload,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    parentIds: {
      type: Array<String>,
      default: () => [],
    },
    handleColse: {
      type: Function,
      default: () => {
      },
    },
    data: {
      type: Object,
      default: () => {
      },
    },
  },
  emits: ['handleColse', 'update'],
  setup(props, { emit }) {
    const data = ref<any>({});
    const type = ref<string>('');
    const form = reactive({
      actualBeginTime: '',
      actualEndTime: '',
      executeDesc: '',
      attachments: [],
    });
    const formRef = ref(null);
    const projectSchemeId = ref<string>('');
    const fieldList = ref([]);
    const from = ref<string>('');
    const fromId = ref<string>('');
    const [modalRegister, { closeModal, setModalProps, changeOkLoading }] = useModalInner(
      (rowData) => {
        projectSchemeId.value = rowData.data?.id;
        data.value = rowData.data;
        if (rowData.from && rowData.fromId) {
          data.value.from = rowData.from;
          data.value.fromId = rowData.fromId;
        }

        form.actualBeginTime = '';
        form.actualEndTime = '';
        form.executeDesc = '';
        form.attachments = [];
      },
    );
    const uploadSuccessChange = () => {
    };
    const saveChange = (val) => {
      fieldList.value = val.map((item) => item.responseData.result);
    };

    const handleOk = async () => {
      const res = await formRef.value.validate();
      if (res) {
        const params = {
          ...data.value,
          ...res,
          projectSchemeId: unref(projectSchemeId),
          actualBeginTime: dayjs(res.actualBeginTime).format('YYYY-MM-DD'),
          actualEndTime: dayjs(res.actualEndTime).format('YYYY-MM-DD'),
          attachments: fieldList.value,
        };
        changeOkLoading(true);
        try {
          const downRes = await new Api('/pms/projectScheme/finish').fetch(
            params,
            '',
            'PUT',
          );
          if (downRes) {
            message.success('完成确认成功');
            formRef.value.resetFields();
            closeModal();
            emit('update');
            emit('handleColse');
          }
        } finally {
          changeOkLoading(false);
        }
      }
    };
    const handleColsed = () => {
      formRef.value.resetFields();
      changeOkLoading(false);
      closeModal();
      emit('handleColse');
    };
    const deleteField = (index) => {
      fieldList.value.splice(index, 1);
    };

    return {
      form,
      uploadSuccessChange,
      saveChange,
      handleOk,
      formRef,
      fieldList,
      modalRegister,
      handleColsed,
      deleteField,
      dayjs,
    };
  },
});
</script>
<style lang="less" scoped>
.body {
  padding: 20px;
  overflow-y: auto;

  .field-list {
    .field-item {
      display: flex;
      height: 30px;
      line-height: 30px;
      margin-top: 10px;

      &:hover {
        .sie-icon-close {
          display: inline-block;
        }
      }

      .field-item-name {
        flex: 1;
      }

      .sie-icon-close {
        display: none;
        cursor: pointer;
      }
    }
  }
}
</style>
