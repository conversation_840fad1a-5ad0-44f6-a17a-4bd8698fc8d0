<template>
  <div>
    <Layout2
      v-if="contentTabs?.length>0"
      v-model:tabsIndex="contentTabsIndex"
      class="layout-tab-center-pms"
      :tabs="contentTabs"
    >
      <ProjectLabdetail
        v-if="contentTabs[contentTabsIndex]?.name === '基本信息'"
        :id="id"
        :pageType="pageType"
        @initForm="initForm"
      />
      <!-- <PeopleManege :id="id" v-if="contentTabsIndex === 1" />
    <Stakeholder :id="id" v-if="contentTabsIndex === 2" /> -->
      <!--    <Permission-->
      <!--      v-if="contentTabsIndex === 1"-->
      <!--      :id="id"-->
      <!--    />-->
      <ProjectRole
        v-if="contentTabs[contentTabsIndex]?.name === '项目角色'"
        :id="id"
        :pageType="pageType"
      />
      <ProjectStatus
        v-if="contentTabs[contentTabsIndex]?.name === '项目状态'"
        :id="id"
        :pageType="pageType"
      />
      <ProjectType
        v-if="contentTabs[contentTabsIndex]?.name === '任务科目'"
        :id="id"
        :pageType="pageType"
      />
      <PreRiskSet
        v-if="contentTabs[contentTabsIndex]?.name === '预警设置'"
        :id="id"
        :pageType="pageType"
      />
    </Layout2>
  </div>
</template>

<script lang="ts">
import {
  defineComponent, onMounted, reactive, toRefs, inject,
} from 'vue';
import {
  Layout2, isPower,
} from 'lyra-component-vue3';
import ProjectLabdetail from './projectLabdetail.vue';
import ProjectRole from './projectRole.vue';
import ProjectStatus from './projectStatus.vue';
import ProjectType from './projectType.vue';
import PreRiskSet from '../riskManagement/preRiskSet/index.vue';
// Layout2Content,
export default defineComponent({
  name: 'ProjectSet',
  components: {
    ProjectRole,
    ProjectStatus,
    ProjectType,
    ProjectLabdetail,
    Layout2,
    PreRiskSet,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
    pageType: {
      type: String,
      default: 'page',
    },
  },

  setup(props) {
    const state = reactive({
      className: '',
      contentTabsIndex: 0,
      powerData: [],
    });
    const state6 = reactive({
      //     [
      //         { name: '基本信息' },
      //   // { name: '权限管理' },
      //   { name: '项目角色' },
      //   { name: '项目状态' },
      //   { name: '任务科目' }
      // ]
      contentTabs: [],
    });
    state.powerData = inject('powerData');
    onMounted(() => {
      if (isPower('XMX_container_13_01', state.powerData) || props.pageType === 'modal') {
        state6.contentTabs.push({ name: '基本信息' });
      }
      if (isPower('XMX_container_13_03', state.powerData) || props.pageType === 'modal') {
        state6.contentTabs.push({ name: '项目角色' });
      }
      // if (isPower('XMX_container_13_04', state.powerData) || props.pageType === 'modal') {
      //   state6.contentTabs.push({ name: '项目状态' });
      // }
      if (isPower('XMX_container_13_05', state.powerData) || props.pageType === 'modal') {
        state6.contentTabs.push({ name: '任务科目' });
      }
      if (isPower('XMX_container_05_03', state.powerData) || props.pageType === 'modal') {
        state6.contentTabs.push({ name: '预警设置' });
      }
    });
    const initForm = (data) => {
      state.className = data.className;
    };

    return {
      ...toRefs(state),
      ...toRefs(state6),
      initForm,
      isPower,
    };
  },
});
</script>

<style   scoped lang="less">
.layout-tab-center-pms{
   :deep(.tabs-main){
    display: flex;
    justify-content: center;
  }
}

</style>
