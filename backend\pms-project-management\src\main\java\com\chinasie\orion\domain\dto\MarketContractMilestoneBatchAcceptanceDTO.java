package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * MarketContractMilestoneAcceptance DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-30 01:59:34
 */
@ApiModel(value = "MarketContractMilestoneAcceptanceDTO对象", description = "市场合同里程碑批量验收信息")
@Data
@ExcelIgnoreUnannotated
public class MarketContractMilestoneBatchAcceptanceDTO  implements Serializable{

    /**
     * 里程碑验收列表
     */
    @ApiModelProperty(value = "里程碑验收列表")
    List<MarketContractMilestoneAcceptanceDTO> milestoneAcceptanceList;

    /**
     * 其他实际验收日期
     */
    @ApiModelProperty(value = "其他实际验收日期")
    private Date otherActualAcceptDate;

    /**
     * 其他本次验收比例
     */
    @ApiModelProperty(value = "其他本次验收比例")
    private BigDecimal otherAcceptanceRatio;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 上传附件
     */
    @ApiModelProperty(value = "上传附件")
    @Valid
    @ExcelIgnore
    private List<FileDTO> fileList;

}
