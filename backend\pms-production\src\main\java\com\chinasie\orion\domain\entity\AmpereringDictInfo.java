package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.Boolean;
import java.lang.String;
import java.lang.Integer;

/**
 * AmpereringDictInfo Entity对象
 *
 * <AUTHOR>
 * @since 2024-09-30 00:02:31
 */
@TableName(value = "pmsx_amperering_dict_info")
@ApiModel(value = "AmpereringDictInfoEntity对象", description = "业务字典表（明细表）")
@Data

public class AmpereringDictInfo extends  ObjectEntity  implements Serializable{

    /**
     * 类型id
     */
    @ApiModelProperty(value = "类型id")
    @TableField(value = "type_id")
    private String typeId;

    /**
     * 类型id
     */
    @ApiModelProperty(value = "类型id")
    @TableField(value = "parent_id")
    private String parentId;

    /**
     * 字典编码
     */
    @ApiModelProperty(value = "字典编码")
    @TableField(value = "code")
    private String code;

    /**
     * 字典名称
     */
    @ApiModelProperty(value = "字典名称")
    @TableField(value = "name")
    private String name;

    /**
     * 是否启用禁用【启用1，禁用0表示】
     */
    @ApiModelProperty(value = "是否启用禁用【启用1，禁用0表示】")
    @TableField(value = "is_enabled")
    private Boolean isEnabled;

    /**
     * 排序,默认0，升序排序
     */
    @ApiModelProperty(value = "排序,默认0，升序排序")
    @TableField(value = "sort")
    private Integer sort;

}
