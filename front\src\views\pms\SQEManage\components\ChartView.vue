<script setup lang="ts">
import {
  Form, FormItem, RadioButton, RadioGroup, RangePicker,
} from 'ant-design-vue';
import {
  Chart, Empty, Icon, isPower, Select,
} from 'lyra-component-vue3';
import {
  computed, inject, onMounted, reactive, Ref, ref,
} from 'vue';
import dayjs, { Dayjs } from 'dayjs';
import Pyramid from './Pyramid.vue';
import { getOption } from './chartOptions';
import Api from '/@/api';

const props = defineProps<{
  type: string
}>();

const emits = defineEmits<{
  (e: 'updateType', value: string): void
}>();

const colors = [
  'rgb(0, 170, 232)',
  'rgb(203, 226, 246)',
  'rgb(231, 241, 251)',
  'rgb(143, 195, 31)',
  'rgb(219, 233, 204)',
  'rgb(238, 245, 231)',
];

const powerData: Ref = inject('powerData');
const dateScope: Ref = ref([dayjs().startOf('year'), dayjs()] as [Dayjs, Dayjs]);
const repairRoundList: Ref<string[]> = ref([]);

onMounted(() => {
  getData();
});

const data = reactive({
  baseScoreVOS: [],
  baseSupervisedBias: {
    headerTitleList: [] as Array<Record<string, any>>,
    rowValues: {} as Record<string, any>,
  },
  centerScoreVOS: [],
  centerSupervisedBias: {
    headerTitleList: [] as Array<Record<string, any>>,
    rowValues: {} as Record<string, any>,
  },
  quality: {},
  safety: {},
});

const baseHead = computed(() => data?.baseSupervisedBias?.headerTitleList || []);
const baseRowValues = computed(() => data?.baseSupervisedBias?.rowValues || {});
const centerHead = computed(() => data?.centerSupervisedBias?.headerTitleList || []);
const centerRowValues = computed(() => data?.centerSupervisedBias?.rowValues || {});

// 获取图表数据
const loading = ref<boolean>(false);

async function getData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/safety-quality-env/pyramid/count/list').fetch({
      dateScope: dateScope.value,
      repairRoundList: repairRoundList.value,
    }, '', 'POST');
    Object.keys(data).forEach((key) => {
      data[key] = result[key];
    });
  } finally {
    loading.value = false;
  }
}

// 获取大修轮次
async function repairRoundApi() {
  const result = await new Api('/pms/safety-quality-env/repairRound/list').fetch('', '', 'GET');
  return result.map((item: string) => ({
    label: item,
    value: item,
  }));
}
</script>

<template>
  <div
    v-loading="loading"
    class="chart-container"
  >
    <Form layout="inline">
      <FormItem
        class="mla"
        label="事发日期选择"
      >
        <RangePicker
          v-model:value="dateScope"
          style="width: 300px"
          @change="getData()"
        />
      </FormItem>
      <FormItem label="大修轮次">
        <Select
          v-model:value="repairRoundList"
          mode="multiple"
          style="width: 220px"
          placeholder="请选择"
          :api="repairRoundApi"
          @change="getData()"
        />
      </FormItem>
      <FormItem>
        <RadioGroup
          v-if="isPower('PMS_AZHGL_container_01',powerData) && isPower('PMS_AZHGL_container_02',powerData)"
          class="mla"
          button-style="solid"
          :value="type"
          @change="(e)=>$emit('updateType',e.target.value)"
        >
          <RadioButton value="list">
            <Icon icon="orion-icon-align-left" />
          </RadioButton>
          <RadioButton value="chart">
            <Icon icon="orion-icon-appstore" />
          </RadioButton>
        </RadioGroup>
      </FormItem>
    </Form>
    <div class="chart-flex">
      <div class="left-content">
        <h2>四个责任分析金字塔</h2>
        <Pyramid
          :data="{
            quality:data.quality,
            safety:data.safety,
          }"
        />
      </div>
      <div class="right-content">
        <h2>项目部安质环状态</h2>
        <div class="right-content-grid">
          <Chart
            v-if="data?.baseScoreVOS?.length"
            :option="getOption(data?.baseScoreVOS||[],colors[0])"
          />
          <div
            v-else
            class="flex flex-pac"
          >
            <Empty />
          </div>
          <table class="table-top">
            <thead>
              <tr>
                <th :colspan="baseHead.length + 1">
                  安质环绩效考核 外部监督偏差数量
                </th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td />
                <td
                  v-for="(item, index) in baseHead"
                  :key="index"
                >
                  {{ item.name }}
                </td>
              </tr>
              <tr
                v-for="(value,key) in baseRowValues"
                :key="key"
              >
                <td>{{ key }}</td>
                <td
                  v-for="(item,index) in value"
                  :key="index"
                  :class="{error:item.count>0}"
                >
                  {{ item.count }}
                </td>
              </tr>
            </tbody>
          </table>
          <Chart
            v-if="data?.centerScoreVOS?.length"
            :option="getOption(data?.centerScoreVOS||[],colors[3])"
          />
          <div
            v-else
            class="flex flex-pac"
          >
            <Empty />
          </div>
          <table class="table-bottom">
            <thead>
              <tr>
                <th />
                <th
                  v-for="(item,index) in centerHead"
                  :key="index"
                >
                  {{ item.name }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="(value,key) in centerRowValues"
                :key="key"
              >
                <td>{{ key }}</td>
                <td
                  v-for="(item,index) in value"
                  :key="index"
                >
                  {{ value.find(v => v.key === centerHead[index]?.key)?.count || 0 }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.chart-container {
  padding: ~`getPrefixVar('content-padding-top')` ~`getPrefixVar('content-padding-left')`;

  .chart-flex {
    display: flex;
    padding-top: 30px;

    > div {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .left-content {
      flex: 1;
      flex-shrink: 0;
    }

    .right-content {
      flex: 3;
      margin-left: 30px;

      .right-content-grid {
        flex-grow: 1;
        width: 100%;
        display: grid;
        grid-template-columns: repeat(2, minmax(100px, 1fr));
        grid-template-rows: repeat(2, minmax(100px, 1fr));
        grid-gap: 12px;
        padding-top: 30px;
      }
    }
  }
}

.mla {
  margin-left: auto;
}

table {
  font-size: 14px;
  font-weight: bold;

  thead {
    color: #fff;
  }

  tbody {
    color: #0a6cd5;
  }

  td {
    text-align: center;
    border: 2px solid #fff;
  }

  &.table-top {

    thead {
      background-color: v-bind('colors[0]');
    }

    td:nth-child(even) {
      background-color: v-bind('colors[1]');
    }

    td:nth-child(odd) {
      background-color: v-bind('colors[2]');
    }
  }

  &.table-bottom {

    thead {
      background-color: v-bind('colors[3]');
    }

    td:nth-child(even) {
      background-color: v-bind('colors[4]');
    }

    td:nth-child(odd) {
      background-color: v-bind('colors[5]');
    }
  }
}

.error {
  color: ~`getPrefixVar('error-color')`;
}
</style>
