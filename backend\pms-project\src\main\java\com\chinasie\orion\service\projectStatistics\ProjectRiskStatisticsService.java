package com.chinasie.orion.service.projectStatistics;

import com.chinasie.orion.domain.dto.projectStatistics.ProjectRiskStatisticsDTO;
import com.chinasie.orion.domain.vo.RiskManagementVO;
import com.chinasie.orion.domain.vo.projectStatistics.ProjectRiskStatisticsVO;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

public interface ProjectRiskStatisticsService {
    ProjectRiskStatisticsVO getProjectRiskStatusStatistics(ProjectRiskStatisticsDTO projectRiskStatisticsDTO);

    List<ProjectRiskStatisticsVO> getProjectRiskRspUserStatistics(ProjectRiskStatisticsDTO projectRiskStatisticsDTO);

    List<ProjectRiskStatisticsVO> getProjectRiskChangeStatusStatistics(ProjectRiskStatisticsDTO projectRiskStatisticsDTO);

    List<ProjectRiskStatisticsVO> getProjectRiskCreateStatistics(ProjectRiskStatisticsDTO projectRiskStatisticsDTO);

    Page<RiskManagementVO> getProjectRiskPages(Page<ProjectRiskStatisticsDTO> pageRequest) throws Exception;
}
