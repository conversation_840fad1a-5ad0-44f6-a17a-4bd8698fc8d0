package com.chinasie.orion.domain.vo.lifecycle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @className ProjectLifeCycleNodeVO
 * @description 生命周期节点VO
 * @since 2023/10/28
 */
@Data
@ApiModel(value = "ProjectLifeCycleNodeVO", description = "生命周期节点VO")
public class ProjectLifeCycleNodeVO implements Serializable {

    @ApiModelProperty(value = "生命周期节点ID")
    private String id;

    @ApiModelProperty(value = "生命周期节点KEY")
    private String nodeKey;

    @ApiModelProperty(value = "节点名称")
    private String name;

    @ApiModelProperty(value = "生命周期内容")
    private String content;

    @ApiModelProperty(value = "是否需要流程模版")
    private Boolean isAttachment;

    @ApiModelProperty(value = "生命周期附件")
    private List<ProjectLifeCycleNodeFileVO> attachments = new ArrayList<>();

    @ApiModelProperty(value = "生命周期操作")
    private List<ProjectLifeCycleNodeActionVO> actions = new ArrayList<>();

    @ApiModelProperty(value = "横坐标")
    private int x;

    @ApiModelProperty(value = "纵坐标")
    private int y;

    @ApiModelProperty(value = "节点类型")
    private String nodeType;

    @ApiModelProperty(value = "是否高亮")
    private Boolean isHighlight;
}
