package com.chinasie.orion.domain.vo;


import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.file.api.domain.vo.FileTreeVO;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * LeadManagementConversion Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-21 18:50:24
 */
@ApiModel(value = "LeadManagementConversionVO对象", description = "线索响应转化")
@Data
public class LeadManagementConversionVO extends ObjectVO implements Serializable {

    /**
     * 线索id
     */
    @ApiModelProperty(value = "线索id")
    @TableField(value = "lead_id")
    private String leadId;


    /**
     * 线索名称
     */
    @ApiModelProperty(value = "线索名称")
    private String leadName;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 转化标准
     */
    @ApiModelProperty(value = "转化标准")
    private String standard;

    /**
     * 预计转化完成日期
     */
    @ApiModelProperty(value = "预计转化完成日期")
    private Date predictCompleteTime;

    /**
     * 协助人员
     */
    @ApiModelProperty(value = "协助人员")
    private String assistUser;


    /**
     * 协助人员
     */
    @ApiModelProperty(value = "协助人员")
    private String assistUserName;

    /**
     * 协助部门
     */
    @ApiModelProperty(value = "协助部门")
    private String assistDept;


    /**
     * 协助部门
     */
    @ApiModelProperty(value = "协助部门")
    private String assistDeptName;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    private List<FileTreeVO> attachments;

}
