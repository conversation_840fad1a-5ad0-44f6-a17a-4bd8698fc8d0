import Api from '/@/api';
import { message, Modal } from 'ant-design-vue';
import AddTableData from './component/AddTableData.vue';
import Formula from './component/Formula.vue';
import SubjectTreeModal from './component/SubjectTreeModal.vue';
import Information from './modal/Information.vue';
import Subject from './modal/Subject.vue';
import { ref } from 'vue';
import { openDrawer } from 'lyra-component-vue3';

function getTableData(params) {
  return new Api('/pms').fetch(params, 'projectApprovalEstimateTemplate/page', 'POST');
}
function addTableApi(params) {
  return Promise.resolve({});
}
function editTableApi(params) {
  return Promise.resolve({});
}
function getDetailsData(id) {
  return new Api('/pms').fetch('', `projectApprovalEstimateTemplate/${id}`, 'GET');
}
function deleteBatchData(params, type, tableRef) {
  Modal.confirm({
    title: '删除提示',
    content: type === 'batch' ? '是否删除选中的数据？' : '是否删除该条数据？',
    onOk() {
      new Api('/pms').fetch(params, 'projectApprovalEstimateTemplate/remove', 'DELETE').then((res) => {
        message.success('删除成功。');
        tableRef.value.reload({ page: 1 });
      });
    },
  });
}
function getTreeApi() {
  return new Api('/pms').fetch('', 'projectApprovalEstimateTemplateClassify/list', 'GET');
}
function deleteTreeApi(params) {
  return new Api('/pms').fetch('', `projectApprovalEstimateTemplateClassify/${params[0]}`, 'DELETE');
}

function addTreeApi(params) {
  return new Api('/pms').fetch(params, 'projectApprovalEstimateTemplateClassify/add', 'POST');
}

function editTreeApi(params) {
  return new Api('/pms').fetch(params, 'projectApprovalEstimateTemplateClassify/edit', 'PUT');
}
interface InitNodeParma{
  drawerData:{
    type:string,
    record?:any
  },
  update:any
  treeData:any,
  selectTreeId?:string
}
function initNode({
  drawerData, update, treeData, selectTreeId = '',
}:InitNodeParma) {
  const addTableDataRef = ref();
  openDrawer({
    title: drawerData.type === 'add' ? '新增模板' : '编辑模板',
    width: 1000,
    content(h) {
      return h(AddTableData, {
        ref: addTableDataRef,
        drawerData,
        treeData,
        selectTreeId,
      });
    },
    async onOk() {
      let formData = await addTableDataRef.value.getModalData();
      let api = drawerData.type === 'add' ? 'projectApprovalEstimateTemplate/add' : 'projectApprovalEstimateTemplate/edit';
      if (drawerData.type === 'edit') {
        formData.id = drawerData.record.id;
      }
      await new Api('/pms').fetch(formData, api, drawerData.type === 'add' ? 'POST' : 'PUT');
      message.success(drawerData.type === 'edit' ? '编辑成功' : '新增成功');
      update();
    },
  });
}
function setFormula(params) {
  return new Api('/pms').fetch(params, 'projectApprovalEstimateTemplateExpenseSubject/setFormula', 'PUT');
}

export {
  getTableData,
  addTableApi,
  editTableApi,
  getDetailsData,
  deleteBatchData,
  getTreeApi,
  deleteTreeApi,
  addTreeApi,
  editTreeApi,
  AddTableData,
  initNode,
  Information,
  Subject,
  Formula,
  SubjectTreeModal,
  setFormula,
};