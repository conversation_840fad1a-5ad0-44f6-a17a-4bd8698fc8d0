package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;

/**
 * OpenCost Entity对象
 *
 * <AUTHOR>
 * @since 2024-10-28 15:17:25
 */
@TableName(value = "pmsx_open_cost")
@ApiModel(value = "OpenCostEntity对象", description = "开口项费用")
@Data

public class OpenCost extends  ObjectEntity  implements Serializable{

    /**
     * 年
     */
    @ApiModelProperty(value = "年")
    @TableField(value = "data_year")
    private String dataYear;

    /**
     * 月
     */
    @ApiModelProperty(value = "月")
    @TableField(value = "data_month")
    private Integer dataMonth;

    /**
     * 季度
     */
    @ApiModelProperty(value = "季度")
    @TableField(value = "data_quarter")
    private Integer dataQuarter;

    /**
     * 中心编号
     */
    @ApiModelProperty(value = "中心编号")
    @TableField(value = "org_code")
    private String orgCode;

    /**
     * 中心名称
     */
    @ApiModelProperty(value = "中心名称")
    @TableField(value = "org_name")
    private String orgName;

    /**
     * 部门编号
     */
    @ApiModelProperty(value = "部门编号")
    @TableField(value = "dept_code")
    private String deptCode;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    @TableField(value = "dept_name")
    private String deptName;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_no")
    private String contractNo;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @TableField(value = "contract_name")
    private String contractName;

    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    @TableField(value = "user_code")
    private String userCode;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @TableField(value = "user_name")
    private String userName;

    /**
     * 供应商编号
     */
    @ApiModelProperty(value = "供应商编号")
    @TableField(value = "supplier_no")
    private String supplierNo;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @TableField(value = "supplier_name")
    private String supplierName;

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    @TableField(value = "pay_date")
    private Date payDate;

    /**
     * 费用类别编号
     */
    @ApiModelProperty(value = "费用类别编号")
    @TableField(value = "pay_type_no")
    private String payTypeNo;

    /**
     * 费用类别名称
     */
    @ApiModelProperty(value = "费用类别名称")
    @TableField(value = "pay_type_name")
    private String payTypeName;

    /**
     * 费用金额
     */
    @ApiModelProperty(value = "费用金额")
    @TableField(value = "pay_amt")
    private BigDecimal payAmt;

}
