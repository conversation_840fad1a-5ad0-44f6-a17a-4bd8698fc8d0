<script lang="ts" setup>
import {
  nextTick,
  ref,
} from 'vue';
import { Input, Tooltip } from 'ant-design-vue';
import { Icon } from 'lyra-component-vue3';
const props = defineProps({
  isShow: {
    type: Boolean,
    default: true,
  },
  name: {
    type: String,
    default: '',
  },
  // 前置任务
  schemePrePostVOList: {
    type: Array,
    default: () => [],
  } as any,
  // 后置任务
  schemePostVOList: {
    type: Array,
    default: () => [],
  } as any,
});
const emit = defineEmits(['blur']);
const isEdit = ref(false);
const name_ = ref('');
const refInput = ref();
// 下拉框展开操作
function handleMouseleave() {
  isEdit.value = false;
}

const handleMouseenter = async () => {
  isEdit.value = true;
  await nextTick();
  refInput.value.focus();
  name_.value = props.name;
};

function handleBlur(event) {
  isEdit.value = false;
  emit('blur', event.target.value);
}

</script>

<template>
  <div class="edit-content">
    <div class="edit-icon">
      <Tooltip>
        <template #title>
          <div class="pre-post-tooltip">
            <template v-if="schemePrePostVOList.length">
              <div>前置任务：</div>
              <div
                v-for="(item,index) in schemePrePostVOList"
                :key="item.id"
              >
                {{ index + 1 }}. {{ item.taskPreOrPostName }}
              </div>
            </template>

            <template v-if="schemePostVOList.length">
              <div>后置任务：</div>
              <div
                v-for="(item,index) in schemePostVOList"
                :key="item.id"
              >
                {{ index + 1 }}. {{ item.taskPreOrPostName }}
              </div>
            </template>
          </div>
        </template>
        <!--前后置计划图标-->
        <Icon
          v-if="schemePrePostVOList.length || schemePostVOList.length"
          color="#D50072"
          icon="fa-sort-amount-asc"
        />
      </Tooltip>
    </div>
    <div class="edit-item flex-te">
      <template v-if="isShow">
        <div
          v-if="!isEdit"
          class="flex-te"
          style="cursor: pointer;"
          :title="name"
          @mouseenter="handleMouseenter"
        >
          {{ name }}&nbsp;
        </div>
        <Input
          v-else
          ref="refInput"
          v-model:value="name_"
          style="width: 250px"
          @mouseleave="handleMouseleave"
          @blur="handleBlur"
        />
      </template>
      <template v-else>
        {{ name }}
      </template>
    </div>
  </div>
</template>

<style scoped lang="less">
.pre-post-tooltip {
  display: flex;
  flex-direction: column;
  font-size: 12px;
  padding: 0 5px;

  span {
    line-height: 1.5;
  }
}
.edit-content{
  width: calc(100% - 50px);
  display: flex;
  align-items: center;
  flex-direction: row;

  .edit-item{
    flex: 1;
  }
}
</style>
