package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionManagementImportDTO {

    @ApiModelProperty(value = "序号")
    @ExcelProperty(value = "序号 ", index = 0)
    private String order;

    @ApiModelProperty(value = "标题")
    @ExcelProperty(value = "标题 ", index = 1)
    private String name;

    @ApiModelProperty(value = "负责人id")
    @ExcelProperty(value = "负责人id ", index = 2)
    private String principalId;

    @ApiModelProperty(value = "提出日期")
    @ExcelProperty(value = "提出日期 ", index = 3)
    private String proposedTimeStr;

    @ApiModelProperty(value = "提出人")
    @ExcelProperty(value = "提出人 ", index = 4)
    private String exhibitor;

    @ApiModelProperty(value = "产品编码")
    @ExcelProperty(value = "产品编码 ", index = 5)
    private String productNumber;

    @ApiModelProperty(value = "物料编码")
    @ExcelProperty(value = "物料编码 ", index = 6)
    private String materialNumber;

    @ApiModelProperty(value = "生产订单")
    @ExcelProperty(value = "生产订单 ", index = 7)
    private String productionOrders;

    @ApiModelProperty(value = "产品编号（SN）")
    @ExcelProperty(value = "产品编号（SN） ", index = 8)
    private String productNumberSn;

    @ApiModelProperty(value = "问题内容")
    @ExcelProperty(value = "问题内容 ", index = 9)
    private String content;

    @ApiModelProperty(value = "阶段")
    @ExcelProperty(value = "阶段 ", index = 10)
    private String stage;

    @ApiModelProperty(value = "过程环节")
    @ExcelProperty(value = "过程环节 ", index = 11)
    private String processLink;

    @ApiModelProperty(value = "过程分类")
    @ExcelProperty(value = "过程分类 ", index = 12)
    private String processClassifi;

    @ApiModelProperty(value = "问题现象一级分类")
    @ExcelProperty(value = "问题现象一级分类 ", index = 13)
    private String problemPhenomenonOne;

    @ApiModelProperty(value = "问题现象二级分类")
    @ExcelProperty(value = "问题现象二级分类 ", index = 14)
    private String problemPhenomenonTwo;

    @ApiModelProperty(value = "问题现象三级分类")
    @ExcelProperty(value = "问题现象三级分类 ", index = 15)
    private String problemPhenomenonTh;

    @ApiModelProperty(value = "问题等级分类")
    @ExcelProperty(value = "问题等级分类 ", index = 16)
    private String problemLevel;

    @ApiModelProperty(value = "优先级")
    @ExcelProperty(value = "优先级 ", index = 17)
    private String priorityLevel;

    @ApiModelProperty(value = "期望完成时间")
    @ExcelProperty(value = "期望完成时间 ", index = 18)
    private String predictEndTimeStr;

    private Date proposedTime;
    private Date predictEndTime;
}
