package com.chinasie.orion.domain.vo.projectOverviewNew;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "ProjectProblemCountVO", description = "项目问题统计")
public class ProjectProblemCountVO {
    @ApiModelProperty(value = "问题总量")
    private Integer total=0;
    @ApiModelProperty(value = "已解决问题数量")
    private Integer solvedCount=0;
    @ApiModelProperty(value = "未解决问题数量")
    private Integer unSolvedCount=0;
}
