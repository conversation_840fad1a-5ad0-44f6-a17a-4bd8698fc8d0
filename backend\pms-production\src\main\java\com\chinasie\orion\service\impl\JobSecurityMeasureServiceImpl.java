package com.chinasie.orion.service.impl;
import com.chinasie.orion.domain.entity.JobSecurityMeasure;
import com.chinasie.orion.domain.dto.JobSecurityMeasureDTO;
import com.chinasie.orion.domain.vo.JobSecurityMeasureVO;
import com.chinasie.orion.service.JobSecurityMeasureService;
import com.chinasie.orion.repository.JobSecurityMeasureMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * JobSecurityMeasure 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-09 15:47:47
 */
@Service
@Slf4j
public class JobSecurityMeasureServiceImpl extends  OrionBaseServiceImpl<JobSecurityMeasureMapper, JobSecurityMeasure>   implements JobSecurityMeasureService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  JobSecurityMeasureVO detail(String id,String pageCode) throws Exception {
        JobSecurityMeasure jobSecurityMeasure =this.getById(id);
        JobSecurityMeasureVO result = BeanCopyUtils.convertTo(jobSecurityMeasure,JobSecurityMeasureVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param jobSecurityMeasureDTO
     */
    @Override
    public  String create(JobSecurityMeasureDTO jobSecurityMeasureDTO) throws Exception {
        JobSecurityMeasure jobSecurityMeasure =BeanCopyUtils.convertTo(jobSecurityMeasureDTO,JobSecurityMeasure::new);
        this.save(jobSecurityMeasure);

        String rsp=jobSecurityMeasure.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param jobSecurityMeasureDTO
     */
    @Override
    public Boolean edit(JobSecurityMeasureDTO jobSecurityMeasureDTO) throws Exception {
        JobSecurityMeasure jobSecurityMeasure =BeanCopyUtils.convertTo(jobSecurityMeasureDTO,JobSecurityMeasure::new);

        this.updateById(jobSecurityMeasure);

        String rsp=jobSecurityMeasure.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<JobSecurityMeasureVO> pages( Page<JobSecurityMeasureDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<JobSecurityMeasure> condition = new LambdaQueryWrapperX<>( JobSecurityMeasure. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(JobSecurityMeasure::getCreateTime);


        Page<JobSecurityMeasure> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), JobSecurityMeasure::new));

        PageResult<JobSecurityMeasure> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<JobSecurityMeasureVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<JobSecurityMeasureVO> vos = BeanCopyUtils.convertListTo(page.getContent(), JobSecurityMeasureVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "作业安措信息导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", JobSecurityMeasureDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            JobSecurityMeasureExcelListener excelReadListener = new JobSecurityMeasureExcelListener();
        EasyExcel.read(inputStream,JobSecurityMeasureDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<JobSecurityMeasureDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("作业安措信息导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<JobSecurityMeasure> jobSecurityMeasurees =BeanCopyUtils.convertListTo(dtoS,JobSecurityMeasure::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::JobSecurityMeasure-import::id", importId, jobSecurityMeasurees, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<JobSecurityMeasure> jobSecurityMeasurees = (List<JobSecurityMeasure>) orionJ2CacheService.get("pmsx::JobSecurityMeasure-import::id", importId);
        log.info("作业安措信息导入的入库数据={}", JSONUtil.toJsonStr(jobSecurityMeasurees));

        this.saveBatch(jobSecurityMeasurees);
        orionJ2CacheService.delete("pmsx::JobSecurityMeasure-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::JobSecurityMeasure-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<JobSecurityMeasure> condition = new LambdaQueryWrapperX<>( JobSecurityMeasure. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(JobSecurityMeasure::getCreateTime);
        List<JobSecurityMeasure> jobSecurityMeasurees =   this.list(condition);

        List<JobSecurityMeasureDTO> dtos = BeanCopyUtils.convertListTo(jobSecurityMeasurees, JobSecurityMeasureDTO::new);

        String fileName = "作业安措信息数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", JobSecurityMeasureDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<JobSecurityMeasureVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }

    @Override
    public List<JobSecurityMeasureVO> getListByJobId(String id) {
        LambdaQueryWrapperX<JobSecurityMeasure> condition = new LambdaQueryWrapperX<>( JobSecurityMeasure. class);
        condition.eq(JobSecurityMeasure::getJobId,id);
        condition.select(JobSecurityMeasure::getJobId,JobSecurityMeasure::getId,JobSecurityMeasure::getMeasureDesc
                ,JobSecurityMeasure::getMeasureCode,JobSecurityMeasure::getMeasureText,JobSecurityMeasure::getMeasureText);
        List<JobSecurityMeasure> list = this.list(condition);
        if(CollectionUtils.isEmpty(list)){
            return  new ArrayList<>();
        }
        return BeanCopyUtils.convertListTo(list,JobSecurityMeasureVO::new);
    }

    @Override
    public Map<String, List<JobSecurityMeasureVO>> getListByKeyList(List<String> encryKeyList) {
        LambdaQueryWrapperX<JobSecurityMeasure> condition = new LambdaQueryWrapperX<>( JobSecurityMeasure. class);
        condition.in(JobSecurityMeasure::getEncryKey,encryKeyList);
//        condition.select(JobSecurityMeasure::getJobId,JobSecurityMeasure::getId,JobSecurityMeasure::getMeasureDesc
//                ,JobSecurityMeasure::getMeasureCode,JobSecurityMeasure::getMeasureText,JobSecurityMeasure::getMeasureText,JobSecurityMeasure::getEncryKey);
        List<JobSecurityMeasure> list = this.list(condition);
        if(CollectionUtils.isEmpty(list)){
            return  new HashMap<>();
        }
        List<JobSecurityMeasureVO> jobSecurityMeasureDTOS = BeanCopyUtils.convertListTo(list,JobSecurityMeasureVO::new);
        return jobSecurityMeasureDTOS.stream().collect(Collectors.groupingBy(JobSecurityMeasureVO::getEncryKey));
    }


    public static class JobSecurityMeasureExcelListener extends AnalysisEventListener<JobSecurityMeasureDTO> {

        private final List<JobSecurityMeasureDTO> data = new ArrayList<>();

        @Override
        public void invoke(JobSecurityMeasureDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<JobSecurityMeasureDTO> getData() {
            return data;
        }
    }


}
