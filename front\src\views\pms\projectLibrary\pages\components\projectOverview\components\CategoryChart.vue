<template>
  <div class="flex-row">
    <div
      ref="chartLeft"
      class="box"
    />
    <div
      ref="chartRight"
      class="box"
    />
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  color: {
    type: String,
    default: () => '#0196fa',
  },
  leftSeries: {
    type: Array,
    default: () => [
      10,
      56,
      53,
      80,
      33,
      76,
      89,
    ],
  },
  rightSeries: {
    type: Array,
    default: () => [
      10,
      56,
      53,
      80,
      33,
      76,
      89,
    ],
  },
});

const chartLeft = ref(null);
const chartRight = ref(null);

onMounted(() => {
  const optionLeft = {
    color: props.color,
    tooltip: {
      show: false, // 设置 tooltip 不显示
    },
    xAxis: {
      type: 'category',
      data: [
        '一月',
        '二月',
        '三月',
        '四月',
        '五月',
        '六月',
        '七月',
      ],
      show: false, // 不显示坐标轴线、坐标轴刻度线和坐标轴上的文字
      axisTick: {
        show: false, // 不显示坐标轴刻度线
      },
      axisLine: {
        show: false, // 不显示坐标轴线
      },
      axisLabel: {
        show: false, // 不显示坐标轴上的文字
      },
      splitLine: {
        show: false, // 不显示网格线
      },
    },
    yAxis: [
      {
        type: 'value',
        axisTick: {
          show: false, // 不显示坐标轴刻度线
        },
        axisLine: {
          show: false, // 不显示坐标轴线
        },
        axisLabel: {
          show: false, // 不显示坐标轴上的文字
        },
        splitLine: {
          show: false, // 不显示网格线
        },
      },
    ],
    series: [
      {
        data: props.leftSeries,
        type: 'bar',
      },
    ],
  };
  const optionRight = {
    color: props.color,
    tooltip: {
      show: false, // 设置 tooltip 不显示
    },
    xAxis: {
      type: 'category',
      data: [
        '一月',
        '二月',
        '三月',
        '四月',
        '五月',
        '六月',
        '七月',
      ],
      show: false, // 不显示坐标轴线、坐标轴刻度线和坐标轴上的文字
      axisTick: {
        show: false, // 不显示坐标轴刻度线
      },
      axisLine: {
        show: false, // 不显示坐标轴线
      },
      axisLabel: {
        show: false, // 不显示坐标轴上的文字
      },
      splitLine: {
        show: false, // 不显示网格线
      },
    },
    yAxis: [
      {
        type: 'value',
        axisTick: {
          show: false, // 不显示坐标轴刻度线
        },
        axisLine: {
          show: false, // 不显示坐标轴线
        },
        axisLabel: {
          show: false, // 不显示坐标轴上的文字
        },
        splitLine: {
          show: false, // 不显示网格线
        },
      },
    ],
    series: [
      {
        data: props.rightSeries,
        type: 'bar',
      },
    ],
  };

  const chartLeftInstance = echarts.init(chartLeft.value);
  chartLeftInstance.setOption(optionLeft);

  const chartRightInstance = echarts.init(chartRight.value);
  chartRightInstance.setOption(optionRight);
});
</script>

<style lang="less" scoped>
  .flex-row{
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    .box{
      width: 100px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .box:nth-child(2) {
      margin-left: -22px;
    }
  }
</style>