CREATE TABLE `pmsx_invoice_information` (
  `id` varchar(64) NOT NULL COMMENT '主键',
  `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
  `creator_id` varchar(64) NOT NULL COMMENT '创建人',
  `modify_time` datetime NOT NULL COMMENT '修改时间',
  `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `modify_id` varchar(64) NOT NULL COMMENT '修改人',
  `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
  `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
  `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
  `status` int(11) NOT NULL COMMENT '状态',
  `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
  `income_plan_num` varchar(100) DEFAULT NULL COMMENT '收入计划编号',
  `contract_id` varchar(64) DEFAULT NULL COMMENT '关联合同id',
  `milestone_id` varchar(64) DEFAULT NULL COMMENT '里程碑id',
  `milestone_name` varchar(200) DEFAULT NULL COMMENT '里程碑名称',
  `invoice_type` varchar(100) DEFAULT NULL COMMENT '发票类型',
  `invoice_num` varchar(100) DEFAULT NULL COMMENT '发票号码',
  `bill_issue_company` varchar(100) DEFAULT NULL COMMENT '开票公司',
  `invoice_purchaser` varchar(100) DEFAULT NULL COMMENT '发票购买方',
  `invoice_date` datetime DEFAULT NULL COMMENT '开票日期',
  `amt_no_tax` decimal(10,0) DEFAULT NULL COMMENT '不含税金额',
  `tax` decimal(10,0) DEFAULT NULL COMMENT '税额',
  `amt_tax` decimal(10,0) DEFAULT NULL COMMENT '含税金额',
  PRIMARY KEY (`id`),
  KEY `pmsx_invoice_information_invoice_num_IDX` (`invoice_num`) USING BTREE,
  KEY `pmsx_invoice_information_contract_id_IDX` (`contract_id`) USING BTREE,
  KEY `pmsx_invoice_information_income_plan_num_IDX` (`income_plan_num`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='发票信息';


CREATE TABLE `pmsx_milestone_income_allocation` (
  `id` varchar(64) NOT NULL COMMENT '主键',
  `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
  `creator_id` varchar(64) NOT NULL COMMENT '创建人',
  `modify_time` datetime NOT NULL COMMENT '修改时间',
  `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `modify_id` varchar(64) NOT NULL COMMENT '修改人',
  `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
  `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
  `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
  `status` int(11) NOT NULL COMMENT '状态',
  `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
  `contract_id` varchar(64) DEFAULT NULL COMMENT '合同id',
  `milestone_id` varchar(64) DEFAULT NULL COMMENT '里程碑id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='里程碑收入分配表';


ALTER TABLE pms_contract_milestone ADD `confirm_allocation_status` varchar(10) DEFAULT NULL COMMENT '确认分配状态 1:待分配,2:已分配'