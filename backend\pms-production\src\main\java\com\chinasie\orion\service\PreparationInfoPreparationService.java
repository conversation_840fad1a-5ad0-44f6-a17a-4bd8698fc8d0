package com.chinasie.orion.service;


import com.chinasie.orion.domain.entity.PreparationInfoPreparation;
import com.chinasie.orion.domain.dto.PreparationInfoPreparationDTO;
import com.chinasie.orion.domain.vo.PreparationInfoPreparationVO;

import java.lang.String;
import java.util.List;


import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * PreparationInfoPreparation 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-17 17:44:46
 */
public interface PreparationInfoPreparationService extends OrionBaseService<PreparationInfoPreparation> {


    /**
     * 新增
     * <p>
     * * @param preparationInfoPreparationDTO
     */
    String create(PreparationInfoPreparationDTO preparationInfoPreparationDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param preparationInfoPreparationDTO
     */
    Boolean edit(PreparationInfoPreparationDTO preparationInfoPreparationDTO) throws Exception;


    PreparationInfoPreparationVO info(String repairRound);
}
