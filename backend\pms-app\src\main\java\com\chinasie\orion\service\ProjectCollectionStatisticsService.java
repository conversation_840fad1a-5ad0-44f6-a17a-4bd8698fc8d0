package com.chinasie.orion.service;

import com.chinasie.orion.domain.vo.WarningSettingMessageRecordVO;
import com.chinasie.orion.domain.vo.projectOverviewNew.*;

import java.util.List;

public interface ProjectCollectionStatisticsService {
    /**
     * 获取风险统计数据
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    ProjectRiskCountVO getRiskCount(String projectId) throws Exception;

    /**
     * 获取问题统计数据
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    ProjectProblemCountVO getProblemCount(String projectId) throws Exception;

    /**
     * 获取需求统计数据
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    ProjectDemandCountVO getDemandCount(String projectId) throws Exception;

    /**
     * 获取项目预警信息
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    List<WarningSettingMessageRecordVO> getwarningSettingMessageRecords(String projectId) throws Exception;

    /**
     * 获取营收统计数据
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    ProjectRevenueTotalVO getProjectRevenueTotalVO(String projectId) throws Exception;

    /**
     * 获取预算统计数据
     *
     * @param projectId
     * @param type
     * @param year
     * @param id
     * @return
     * @throws Exception
     */
    ProjectBudgetTotalVO getBudgetTotalVO(String projectId, String type, String year, String id) throws Exception;

    /**
     * 获取计划统计数据
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    ProjectPlanCountVO getPlanCount(String projectId) throws Exception;

    /**
     * 获取按月统计的计划数据
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    List<ProjectPlanCountVO> getPlanMonth(String projectId) throws Exception;

    /**
     * 获取物资统计数据
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    GoodsServiceCountVO getGoodsServiceCount(String projectId) throws Exception;

    /**
     * 获取项目详情信息
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    ProjectInfoVO getProjectInfo(String projectId) throws Exception;


    /**
     * 获取项目工时信息
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    ProjectWorkHourVO getProjectWorkHourInfo(String projectId) throws Exception;


}
