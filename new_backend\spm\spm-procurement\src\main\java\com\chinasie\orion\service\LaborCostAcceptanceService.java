package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.LaborCostAcceptanceDTO;
import com.chinasie.orion.domain.entity.LaborCostAcceptance;
import com.chinasie.orion.domain.vo.LaborCostAcceptanceVO;
import com.chinasie.orion.domain.vo.LaborCostStatisticsSingleVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * LaborCostAcceptance 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28 19:32:01
 */
public interface LaborCostAcceptanceService  extends  OrionBaseService<LaborCostAcceptance>  {


    /**
     *  详情
     *
     * * @param id
     */
    LaborCostAcceptanceVO detail(String id, String pageCode)throws Exception;

    /**
     *  新增
     *
     * * @param laborCostAcceptanceDTO
     */
    LaborCostAcceptanceVO create(LaborCostAcceptanceDTO laborCostAcceptanceDTO)throws Exception;

    /**
     *  编辑
     *
     * * @param laborCostAcceptanceDTO
     */
    Boolean edit(LaborCostAcceptanceDTO laborCostAcceptanceDTO)throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<LaborCostAcceptanceVO> pages( Page<LaborCostAcceptanceDTO> pageRequest)throws Exception;

    /**
     *  下载模板
     *
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response)throws Exception;

    /**
     *  导入校验
     *
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


    /**
     *  确认导入
     *
     * * @param importId
     */
    Boolean importByExcel(String importId)throws Exception;

    /**
     *  取消导入
     *
     * * @param importId
     */
    Boolean importCancelByExcel(String importId)throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

    /**
     *  设置各种名称
     *
     * * @param searchConditions
     * * @param response
     */
    void  setEveryName(List<LaborCostAcceptanceVO> vos)throws Exception;


    LaborCostStatisticsSingleVO costStatistice(String orgCode, int year, String contractNumber, Integer quarter);
}
