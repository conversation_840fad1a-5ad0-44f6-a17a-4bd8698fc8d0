package com.chinasie.orion.domain.vo.approval;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * ProjectApprovalIncome VO对象
 *
 * <AUTHOR>
 * @since 2024-05-14 14:11:17
 */
@ApiModel(value = "ProjectApprovalIncomeVO对象", description = "收益策划")
@Data
public class ProjectApprovalIncomeVO extends ObjectVO implements Serializable{

    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    private String productId;


    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;

    /**
     * 产品编码
     */
    @ApiModelProperty(value = "产品编码")
    private String productNumber;


    /**
     * 预期合同年份
     */
    @ApiModelProperty(value = "预期合同年份")
    private Date expectedContractYear;


    /**
     * 制造工时
     */
    @ApiModelProperty(value = "制造工时")
    private BigDecimal fabricateHour;


    /**
     * 预期销售数量
     */
    @ApiModelProperty(value = "预期销售数量")
    private Integer expectedSaleNumber;


    /**
     * 预期收益
     */
    @ApiModelProperty(value = "预期收益")
    private BigDecimal expectedIncome;


    /**
     * 项目立项id
     */
    @ApiModelProperty(value = "项目立项id")
    private String projectApprovalId;


    /**
     * 毛利率
     */
    @ApiModelProperty(value = "毛利率")
    private String marginRate;


    /**
     * 预估单价
     */
    @ApiModelProperty(value = "预估单价")
    private BigDecimal expectedPrice;


}
