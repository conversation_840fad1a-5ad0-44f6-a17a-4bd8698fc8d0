package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * CommonFileUploadDTO DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-28 21:47:43
 */
@ApiModel(value = "CommonFileUploadDTO对象", description = "附件")
@Data
@ExcelIgnoreUnannotated
public class CommonFileUploadDTO implements Serializable{

    /**
     * 数据Id
     */
    @ApiModelProperty(value = "数据Id")
    @NotEmpty(message = "数据Id不能为空")
    private String dataId;

    /**
     * 上传附件
     */
    @ApiModelProperty(value = "上传附件")
    @Valid
    @ExcelIgnore
    private List<FileDTO> fileList;

}
