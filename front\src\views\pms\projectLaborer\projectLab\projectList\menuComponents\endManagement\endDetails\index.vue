<template>
  <Layout3
    v-if="tabsOption?.length>0"
    :defaultActionId="actionId"
    :menuData="tabsOption"
    :projectData="projectInfo"
    :type="2"
    @menuChange="contentTabsChange2"
  >
    <template #header-title>
      <div class="layoutTtitle">
        <div class="nameStyle flex-te">
          {{ projectInfo.name }}
        </div>
        <div class="numberStyle">
          {{ projectInfo?.number }}
        </div>
      </div>
    </template>

    <template #header-right>
      <div>
        <BasicTableAction
          v-if="projectInfo?.projectId"
          type="button"
          :actions="actions"
        />
      </div>
    </template>

    <template #tabsRight>
      <RemoveBtn />
    </template>
    <EndDetails
      v-if="actionId===456781 && isPower('JX_container_02', powerData)"
      :id="id"
    />
    <EndDoc
      v-if="actionId===456782 && isPower('JX_container_03', powerData)"
      :id="id"
    />
    <EndProcessTab
      v-if="actionId===456783 && isPower('JX_container_03', powerData)"
      :id="id"
    />

    <AddTableNode
      @register="register"
      @update="getDetail"
    />
  </Layout3>
</template>

<script lang="ts">
import {
  defineComponent, reactive, toRefs, readonly, ref, getCurrentInstance, provide, onMounted, computed,
} from 'vue';
// import { Layout2 } from '/@/components/Layout2.0';
import {
  Layout3, useProjectPower, isPower, useDrawer, ITableActionItem, BasicTableAction,
} from 'lyra-component-vue3';
import { useRoute } from 'vue-router';
import EndDetails from './endProjectComponents/details/index.vue';
import EndDoc from './endProjectComponents/endDoc/index.vue';
import EndProcessTab from './endProjectComponents/endProcessTab/index.vue';
// import EndProcessTab from './endProjectComponents/endProcessTab/indexCopy.vue';
import RemoveBtn from '/@/views/pms/projectLaborer/componentsList/returnBtn/index.vue';
import { getDetailsApi } from '/@/views/pms/projectLaborer/api/endManagement';
import AddTableNode from '../modal/AddTableNode.vue';
import { setTitleByRootTabsKey } from '/@/utils';

export default defineComponent({
  components: {
    Layout3,
    EndDetails,
    EndDoc,
    EndProcessTab,
    RemoveBtn,
    AddTableNode,
    BasicTableAction,
  },
  setup() {
    const [register, { openDrawer }] = useDrawer();
    const route = useRoute();
    const state = reactive({
      tabsIndex: Number(route.query.type),
      id: route.query.id,
      projectId: route.query.projectId,
      projectInfo: {} as any,
      actionId: 456781,
      className: '',
      // tabsOption: [
      //   { name: '概述' }, // 0
      //   { name: '结项文档' }, // 1
      //   // { name: '流程' }, // 2
      // ],
      powerData: [],
    });
    const state6 = reactive({
      tabsOption: [],
    });
    const internalInstance = getCurrentInstance();
    // 获取权限
    async function getProjectPower() {
      return new Promise((resolve, reject) => {
        useProjectPower(
          { pageCode: 'PMS0007' },
          (powerList) => {
            resolve(powerList || []);
          },
          internalInstance,
        );
      });
    }
    /* 获取详情 */
    const getDetail = async () => {
      const love = {
        id: state?.id,
        className: 'PostProject',
        moduleName: '项目管理-结项管理',
        type: 'GET',
        remark: `打开了【${state?.id}】`,
      };
      await getDetailsApi(state.id, love)
        .then((res) => {
          if (res) {
            state.projectInfo = { ...res };
          }
          setTitleByRootTabsKey(route?.query?.rootTabsKey, res.name || '');
        })
        .catch(() => {});
    };
    onMounted(async () => {
      state.powerData = await getProjectPower();
      isPower('JX_container_02', state.powerData) && state6.tabsOption.push({
        name: '概述',
        id: 456781,
      });
      isPower('JX_container_03', state.powerData) && state6.tabsOption.push({
        name: '结项文档',
        id: 456782,
      });
      isPower('JX_container_04', state.powerData) && state6.tabsOption.push({
        name: '流程',
        id: 456783,
      });
      await getDetail();
    });
    // 数据
    provide(
      'formData',
      computed(() => state.projectInfo),
    );
    // 数据查询
    provide(
      'getFormData',
      computed(() => getDetail),
    );
    function contentTabsChange2(index) {
      state.actionId = index.id;
    }
    const endProjectId = ref(state.id);
    provide('endProjectId', readonly(endProjectId));
    const projectId = ref(state.projectId);
    provide('projectId', readonly(projectId));
    // 权限分发
    provide(
      'powerData',
      computed(() => state.powerData),
    );
    provide(
      'projectInfo',
      computed(() => state.projectInfo),
    );
    function getDetails() {
      return state.projectInfo;
    }
    provide('getForm', getDetail);
    provide('getDetails', getDetails);

    const actions: ITableActionItem[] = [
      {
        text: '编辑',
        icon: 'sie-icon-bianji',
        isShow() {
          return isPower('JX_container_button_02', state.powerData);
        },
        onClick() {
          openDrawer(true, {
            type: 'edit',
            id: state.projectInfo?.id,
            projectId: state.projectInfo?.projectId,
          });
        },
      },
    ];

    return {
      ...toRefs(state),
      ...toRefs(state6),
      isPower,
      contentTabsChange2,
      register,
      actions,
      getDetail,
    };
  },
});
</script>
<style lang="less" scoped>
.layoutTtitle{
  width: 350px;
  padding: 5px ~`getPrefixVar('content-padding-left')`;
  .nameStyle{
    font-weight: 400;
    font-style: normal;
    color: #444B5E;
    font-size: 18px;
    height:29px;
    line-height: 29px;
  }
  .numberStyle{
    font-size: 12px;
    color: #969EB4;
  }
}
</style>
