package com.chinasie.orion.service;

import java.lang.String;
import java.util.Date;
import java.util.List;

import com.chinasie.orion.domain.dto.WorkHourFillDTO;
import com.chinasie.orion.domain.dto.WorkHourFillPageDTO;
import com.chinasie.orion.domain.entity.WorkHourFill;
import com.chinasie.orion.domain.vo.WorkHourFillDayVO;
import com.chinasie.orion.domain.vo.WorkHourFillInfoVO;
import com.chinasie.orion.domain.vo.WorkHourFillVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;
/**
 * <p>
 * WorkHourFill 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-15 10:14:51
 */
public interface WorkHourFillService  extends OrionBaseService<WorkHourFill> {
    /**
     *  详情
     *
     * * @param id
     */
    WorkHourFillInfoVO detail(String id)  throws Exception;

    /**
     *  新增
     *
     * * @param workHourFillDTO
     */
    WorkHourFillVO create(WorkHourFillDTO workHourFillDTO)  throws Exception;

    /**
     *  编辑
     *
     * * @param workHourFillDTO
     */
    Boolean edit(WorkHourFillDTO workHourFillDTO) throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     */
    Page<WorkHourFillVO> pages(Page<WorkHourFillPageDTO> pageRequest) throws Exception;

    /**
     *  查询某天的工时填报
     *
     * * @param workDate
     */
    WorkHourFillDayVO byDate(String workDate, String projectId)  throws Exception;

    /**
     *  查询某月的工时填报
     *
     * * @param workDate
     */
    List<WorkHourFillDayVO> byMonth(String month, String projectId)  throws Exception;

}
