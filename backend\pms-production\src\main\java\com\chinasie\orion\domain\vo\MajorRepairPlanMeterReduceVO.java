package com.chinasie.orion.domain.vo;

import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/12/18:19
 * @description:
 */

@ApiModel(value = "MajorRepairPlanMeterReduceVO对象", description = "大修计划集体剂量降低")
@Data
public class MajorRepairPlanMeterReduceVO extends ObjectVO implements Serializable {

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    private String jobManageNumber;


    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    private String majorRepairTurn;


    /**
     * 作业ID
     */
    @ApiModelProperty(value = "作业ID")
    private String jobManageId;


    /**
     * is_reduce
     */
    @ApiModelProperty(value = "是否降低")
    private Boolean isReduce;


    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    private String number;


    /**
     * 领域
     */
    @ApiModelProperty(value = "领域")
    private String belongField;

    @ApiModelProperty(value = "领域名称")
    private String belongFieldName;

    /**
     * 技术应用窗口
     */
    @ApiModelProperty(value = "技术应用窗口")
    private String applicationOccasion;
    @ApiModelProperty(value = "技术应用窗口名称")
    private String applicationOccasionName;


    /**
     * 落地电厂
     */
    @ApiModelProperty(value = "落地电厂")
    private String applicationBase;
    @ApiModelProperty(value = "落地电厂名称")
    private String applicationBaseName;

    /**
     * 应用机组类型
     */
    @ApiModelProperty(value = "应用机组类型")
    private String applicationCrew;
    @ApiModelProperty(value = "应用机组类型名称")
    private String applicationCrewName;


    /**
     * 现场环境计量率
     */
    @ApiModelProperty(value = "现场环境计量率")
    private BigDecimal environmentMeterRate;


    /**
     * 减少工时
     */
    @ApiModelProperty(value = "减少工时")
    private BigDecimal reduceHour;


    /**
     * 节约集体剂量
     */
    @ApiModelProperty(value = "节约集体剂量")
    private BigDecimal conserveMeter;


    /**
     * 创优技术或工作
     */
    @ApiModelProperty(value = "创优技术或工作")
    private String createExcellence;


    /**
     * 内容介绍
     */
    @ApiModelProperty(value = "内容介绍")
    private String content;


    /**
     * 是否可沿用
     */
    @ApiModelProperty(value = "是否可沿用")
    private Boolean isContinueUse;

    @ApiModelProperty(value = "文件列表")
    private List<FileVO> fileVOList;

    @ApiModelProperty(value = "工作抬头")
    private String  workJobTitle;

    @ApiModelProperty(value = "工作中心")
    private String  jobManageCenter;
    @ApiModelProperty(value = "工作名称")
    private String  jobManageName;

    /**
     * 是否重大项目
     */
    @ApiModelProperty(value = "是否重大项目")
    private Boolean isMajorProject;

    @ApiModelProperty(value = "大修状态名称")
    private String majorRepairStatusName;
    /**
     * 实际结束时间
     */
    @ApiModelProperty(value = "实际结束时间")
    private Date actualEndTime;
}
