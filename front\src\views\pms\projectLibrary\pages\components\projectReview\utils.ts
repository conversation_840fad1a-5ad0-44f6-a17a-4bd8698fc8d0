import { isPower, openDrawer } from 'lyra-component-vue3';
import { h, ref, Ref } from 'vue';
import dayjs from 'dayjs';

// 表格组件新增、编辑抽屉
export function openFormDrawer(component: any, record?: Record<string, any>, cb?: () => void): void {
  const drawerRef: Ref = ref();
  openDrawer({
    title: record?.id ? '编辑' : '新增',
    width: 1000,
    content() {
      return h(component, {
        ref: drawerRef,
        formId: record?.id,
        projectId: record?.projectId,
        projectName: record?.projectName,
      });
    },
    async onOk(): Promise<void> {
      await drawerRef.value.onSubmit();
      cb?.();
    },
  });
}

// 获取检验规则
export function getRules(item: Record<string, any>): Record<string, any> {
  const rules: any[] = [];
  if (item.type === 'annexupload' && item.required) {
    rules.push({
      required: true,
      type: 'Array',
      trigger: 'change',
    });
  } else if (item.required) {
    rules.push({
      required: true,
      message: '该内容为必填项',
      trigger: 'change',
    });
  }
  if (item.type === 'input' && item.validateRegular) {
    rules.push({
      validator: async (_rule: any, value: string) => await validateRegular(value, item.validateRegular, item.validateMsg),
    });
  }
  delete item.required;
  return {
    ...item,
    rules,
  };
}

async function validateRegular(value: string, regular: string, msg: string) {
  if (value && !new RegExp(regular).test(value)) {
    return Promise.reject(msg);
  }
  return Promise.resolve();
}

// 表格字段特殊处理
export function formatTableColumns(columns: any[]): any[] {
  return columns.map((item) => {
    switch (item.type) {
      case 'selectuser':
      case 'selectdict':
        const getValue = (record) => record[item.valueField]?.map((v: any) => v.name).join('、');
        item.customRender = ({ record }) => h('span', {
          class: 'flex-te',
          title: getValue(record),
        }, getValue(record));
        break;
      case 'selectdept':
      case 'selectproject':
        item.customRender = ({ record }: {
                    record: Record<string, any>
                }) => h('span', {
          class: 'flex-te',
          title: record[item.valueField],
        }, record[item.valueField]);
        break;
    }
    switch (item.dataIndex) {
      case 'creatorId':
      case 'modifyId':
      case 'ownerId':
        item.customRender = ({ record }: {
                    record: Record<string, any>
                }) => h('span', {
          class: 'flex-te',
          title: record[item.dataIndex.replace('Id', 'Name')],
        }, record[item.dataIndex.replace('Id', 'Name')]);
        break;
      case 'createTime':
      case 'modifyTime':
        item.customRender = ({ text }: {
                    text: string
                }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '');
        break;
    }
    return item;
  });
}

// 编辑页面表单回显
export function setFormFieldsValues({
  forms, selectedData, result,
  setFieldsValue,
}) {
  forms.forEach((field: Record<string, any>) => {
    switch (field.type) {
      case 'selectdept':
      case 'selectproject':
        selectedData[field.field] = result[field.field] ? [
          {
            id: result[field.field],
            name: result[field.valueField],
          },
        ] : [];
        break;
      case 'selectdict':
        selectedData[field.field] = result[field.field] ? result[field.valueField]?.map((item: Record<string, any>) => ({
          value: item.value,
          label: item.description,
        })) : [];
        break;
      case 'selectuser':
        selectedData[field.field] = result[field.field] ? result[field.valueField]?.map((item: Record<string, any>) => ({
          id: item.id,
          name: item.name,
        })) : [];
        break;
    }
    // 解决字段未挂载完成提前校验的问题
    setTimeout(() => {
      setFieldsValue({
        [field.field]: result[field.field],
      });
    });
  });
}

// 详情基本信息回显
export function setBasicInfo(list: any[]): any[] {
  list = list.map((item) => {
    switch (item.type) {
      case 'selectproject':
      case 'selectdept':
        item.valueRender = ({ record }) => record[`${item.field}Name`];
        break;
      case 'selectuser':
      case 'selectdict':
        item.valueRender = ({ record }) => record[`${item.field}Name`]?.map((v) => v.name)?.join('、') || '-';
        break;
    }
    item.wrap = true;
    return item;
  });
  return list;
}

// 格式化表格行权限配置
export function formatActionsPower(actions: any[]): any[] {
  return actions.map((item) => ({
    ...item,
    isShow: (record: Record<string, any>) => isPower(item.code, record.rdAuthList),
  }));
}
// 格式化表格行权限配置-项目评审业务调用
export function formatActionsPowerStates(actions: any[]): any[] {
  return actions.map((item) => ({
    ...item,
    isShow: (record: Record<string, any>) => record.status === 101 && isPower(item.code, record.rdAuthList),
  }));
}
// 格式化表格行权限配置-自定义
export function formatActionsPowerAuth(actions: any[], rdAuthList): any[] {
  return actions.map((item) => ({
    ...item,
    isShow: () => isPower(item.code, rdAuthList),
  }));
}
