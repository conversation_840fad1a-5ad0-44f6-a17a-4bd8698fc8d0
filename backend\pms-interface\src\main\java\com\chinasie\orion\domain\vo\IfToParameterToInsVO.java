package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;
import java.util.Date;
import java.util.List;

/**
 * IfToParameterToIns VO对象
 *
 * <AUTHOR>
 * @since 2024-01-31 13:52:16
 */
@ApiModel(value = "IfToParameterToInsVO对象", description = "意见单和参数和参数实列的关系")
@Data
public class IfToParameterToInsVO extends ObjectVO implements Serializable {

    /**
     * 意见单id
     */
    @ApiModelProperty(value = "意见单id")
    private String ifId;

    /**
     * 参数ID
     */
    @ApiModelProperty(value = "参数ID")
    private String parameterId;

    /**
     * 参数实列ID
     */
    @ApiModelProperty(value = "参数实列ID")
    private String insId;

    @ApiModelProperty(value = "模板Id")
    private String modelId;

    /**
     * 拷贝类型
     */
    @ApiModelProperty(value = "拷贝类型")
    private String copyType;


    /**
     * 名称
     */
    @ApiModelProperty(value = "参数名称")
    private String paramName;

    /**
     * 编码
     */
    @ApiModelProperty(value = "参数编码")
    private String paramNumber;


    /**
     * 别名
     */
    @ApiModelProperty(value = "别名")
    private List<String> aliases;

    @ApiModelProperty(value = "实列值")
    private String insValue;

    @ApiModelProperty(value = "实列名称")
    private String insName;

    @ApiModelProperty(value = "实列创建时间")
    private Date insCreateTime;

}
