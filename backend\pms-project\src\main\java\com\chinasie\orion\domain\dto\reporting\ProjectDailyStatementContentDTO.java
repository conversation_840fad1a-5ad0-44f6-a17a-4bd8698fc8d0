package com.chinasie.orion.domain.dto.reporting;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;
import java.lang.Integer;

/**
 * ProjectDailyStatementContent Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-14 15:21:01
 */
@ApiModel(value = "ProjectDailyStatementContentDTO对象", description = "日报内容表")
@Data
public class ProjectDailyStatementContentDTO extends ObjectDTO implements Serializable {

    /**
     * 工作内容
     */
    @ApiModelProperty(value = "工作内容")
    private String content;

    /**
     * 工时
     */
    @ApiModelProperty(value = "工时")
    private BigDecimal taskTime;

    /**
     * 是否计划内
     */
    @ApiModelProperty(value = "是否计划内")
    private Integer thePlan;

    /**
     * 关联关系
     */
    @ApiModelProperty(value = "关联关系")
    private String relationship;

    /**
     * 关联日报
     */
    @ApiModelProperty(value = "关联日报")
    private String dailyStatementId;


    /**
     * 关联类型
     */
    @ApiModelProperty(value = "关联类型")
    private String relationType;

}
