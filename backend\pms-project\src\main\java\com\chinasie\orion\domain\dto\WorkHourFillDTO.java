package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.lang.String;
import java.util.List;

/**
 * WorkHourFill Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-15 10:14:51
 */
@ApiModel(value = "WorkHourFillDTO对象", description = "工时填报")
@Data
public class WorkHourFillDTO extends ObjectDTO implements Serializable{

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @NotBlank(message = "项目id不能为空")
    private String projectId;

    /**
     * 单据编号
     */
    @ApiModelProperty(value = "单据编号")
    private String number;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String type;

    /**
     * 成员id
     */
    @ApiModelProperty(value = "成员id")
    private String memberId;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 工时类型
     */
    @ApiModelProperty(value = "工时类型")
    private String workHourType;

    /**
     * 填报角色
     */
    @ApiModelProperty(value = "填报角色")
    private String fillRole;

    /**
     * 每天明细
     */
    @ApiModelProperty(value = "每天明细")
    @Valid
    private List<WorkHourFillDayDTO> dayDetailList;

}
