package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * ContractCostType Entity对象
 *
 * <AUTHOR>
 * @since 2024-10-17 09:39:59
 */
@TableName(value = "pmsx_contract_cost_type")
@ApiModel(value = "ContractCostTypeEntity对象", description = "合同计划成本类型")
@Data

public class ContractCostType extends  ObjectEntity  implements Serializable{

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_number")
    private String contractNumber;

    /**
     * 成本类型编号
     */
    @ApiModelProperty(value = "成本类型编号")
    @TableField(value = "cost_type_number")
    private String costTypeNumber;

    /**
     * 成本类型名称
     */
    @ApiModelProperty(value = "成本类型名称")
    @TableField(value = "cost_type_name")
    private String costTypeName;

    /**
     * 成本名称
     */
    @ApiModelProperty(value = "成本名称")
    @TableField(value = "cost_name")
    private String costName;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    @TableField(value = "unit_price")
    private BigDecimal unitPrice;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    @TableField(value = "unit")
    private String unit;

}
