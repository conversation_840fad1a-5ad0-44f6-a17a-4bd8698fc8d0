import { openModal } from 'lyra-component-vue3';
import { h, ref } from 'vue';
import ProgressTableModal from '../ProgressTableModal.vue';

export function useProgressDetails() {
  function openProgressModal(query: object) {
    const contentRef = ref();

    openModal({
      title: '进展详情',
      width: 1200,
      content() {
        return h(ProgressTableModal, {
          ref: contentRef,
          query,
        });
      },
      footer: false,
    });
  }

  return {
    openProgressModal,
  };
}
