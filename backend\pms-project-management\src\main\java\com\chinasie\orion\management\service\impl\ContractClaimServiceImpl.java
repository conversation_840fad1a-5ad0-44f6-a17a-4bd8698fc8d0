package com.chinasie.orion.management.service.impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.management.domain.dto.ContractClaimDTO;
import com.chinasie.orion.management.domain.dto.NcfFormpurchaseRequestDTO;
import com.chinasie.orion.management.domain.entity.ContractChange;
import com.chinasie.orion.management.domain.entity.ContractClaim;
import com.chinasie.orion.management.domain.vo.ContractClaimVO;
import com.chinasie.orion.management.repository.ContractClaimMapper;
import com.chinasie.orion.management.service.ContractClaimService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <p>
 * ContractClaim 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@Service
@Slf4j
public class ContractClaimServiceImpl extends OrionBaseServiceImpl<ContractClaimMapper, ContractClaim> implements ContractClaimService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ContractClaimVO detail(String id, String pageCode) throws Exception {
        ContractClaim contractClaim = this.getById(id);
        ContractClaimVO result = BeanCopyUtils.convertTo(contractClaim, ContractClaimVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param contractClaimDTO
     */
    @Override
    public String create(ContractClaimDTO contractClaimDTO) throws Exception {
        ContractClaim contractClaim = BeanCopyUtils.convertTo(contractClaimDTO, ContractClaim::new);
        this.save(contractClaim);

        String rsp = contractClaim.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param contractClaimDTO
     */
    @Override
    public Boolean edit(ContractClaimDTO contractClaimDTO) throws Exception {
        ContractClaim contractClaim = BeanCopyUtils.convertTo(contractClaimDTO, ContractClaim::new);

        this.updateById(contractClaim);

        String rsp = contractClaim.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ContractClaimVO> pages(Page<ContractClaimDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ContractClaim> condition = new LambdaQueryWrapperX<>(ContractClaim.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }

        if (pageRequest.getQuery() != null) {
            //变更申请日期段
            if (pageRequest.getQuery().getStartDate() != null && pageRequest.getQuery().getEndDate() != null) {
                condition.between(ContractClaim::getClaimRequestTime, pageRequest.getQuery().getStartDate(), pageRequest.getQuery().getEndDate());
            }
        }
        condition.orderByDesc(ContractClaim::getCreateTime);

        Page<ContractClaim> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ContractClaim::new));

        PageResult<ContractClaim> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ContractClaimVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ContractClaimVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ContractClaimVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ContractClaimVO> getByCode(Page<ContractClaimDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<ContractClaim> condition = new LambdaQueryWrapperX<>(ContractClaim.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.eq(ContractClaim::getContractNumber, pageRequest.getQuery().getContractNumber());
        condition.orderByDesc(ContractClaim::getCreateTime);
        Page<ContractClaim> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ContractClaim::new));

        PageResult<ContractClaim> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ContractClaimVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ContractClaimVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ContractClaimVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "合同索赔信息表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractClaimDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ContractClaimExcelListener excelReadListener = new ContractClaimExcelListener();
        EasyExcel.read(inputStream, ContractClaimDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ContractClaimDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("合同索赔信息表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ContractClaim> contractClaimes = BeanCopyUtils.convertListTo(dtoS, ContractClaim::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("ncf::ContractClaim-import::id", importId, contractClaimes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ContractClaim> contractClaimes = (List<ContractClaim>) orionJ2CacheService.get("ncf::ContractClaim-import::id", importId);
        log.info("合同索赔信息表导入的入库数据={}", JSONUtil.toJsonStr(contractClaimes));

        this.saveBatch(contractClaimes);
        orionJ2CacheService.delete("ncf::ContractClaim-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("ncf::ContractClaim-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(Page<NcfFormpurchaseRequestDTO> pageRequest, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ContractClaim> condition = new LambdaQueryWrapperX<>(ContractClaim.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ContractClaim::getCreateTime);
        List<ContractClaim> contractClaimes = this.list(condition);

        List<ContractClaimDTO> dtos = BeanCopyUtils.convertListTo(contractClaimes, ContractClaimDTO::new);

        String fileName = "合同索赔信息表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractClaimDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<ContractClaimVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }


    public static class ContractClaimExcelListener extends AnalysisEventListener<ContractClaimDTO> {

        private final List<ContractClaimDTO> data = new ArrayList<>();

        @Override
        public void invoke(ContractClaimDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ContractClaimDTO> getData() {
            return data;
        }
    }


}
