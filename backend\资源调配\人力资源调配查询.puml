@startuml
actor User
participant "ResourceAllocationOfController" as Controller
participant "ResourceAllocationOfServiceImpl" as ServiceImpl
participant "BasicUserMapper" as BasicUserMapper
participant "ResourceAllocationOfMapper" as ResourceAllocationOfMapper
participant "PersonMangeMapper" as PersonMangeMapper
database "MySQL"

User -> Controller: 调用 persionLlist 方法
Controller -> Controller: 参数校验 (SearchType, RealStartDate, RealEndDate)
alt 如果参数为空
    Controller -> Controller: 设置默认时间范围
end

Controller -> ServiceImpl: getResourceAllocationOfPersonMaterial(param)
ServiceImpl -> BasicUserMapper: 查询员工所在部门 (selectOne)
BasicUserMapper -> MySQL: SELECT * FROM pmsx_basic_user WHERE user_code = #{staffNo}
note right of MySQL: 获取员工信息，包括部门代码

BasicUserMapper --> ServiceImpl: 返回部门代码
ServiceImpl -> ResourceAllocationOfMapper: 查询所有部门 (getDepts)
ResourceAllocationOfMapper -> MySQL: SELECT id, name, parent_id FROM pmi_dept
note right of MySQL: 获取所有部门信息

ResourceAllocationOfMapper --> ServiceImpl: 返回部门列表
ServiceImpl -> ResourceAllocationOfMapper: 根据类型查询人员调配数据 (getResourceAllocationOfPerson 或 getResourceAllocationOfMaterial)
ResourceAllocationOfMapper -> MySQL:
alt 查询人员调配数据
    ResourceAllocationOfMapper -> MySQL: SELECT ... FROM pmsx_basic_user LEFT JOIN pmi_dept ...
    note right of MySQL
       SELECT t_01.id,
              t_01.name,
              t_01.parentId as parentId,
              t_01.dataType as dataType,
              t_02.number   as number,
              t_02.userCode,
              t_02.repairOrgCount,
              t_02.details
       FROM (
           SELECT bu.id        as id,
                  bu.full_name as `name`,
                  bu.user_code AS userCode,
                  dept.id      AS parentId,
                  bu.dept_code AS dept_code,
                  'write'      AS dataType
           FROM pmsx_basic_user bu
           LEFT JOIN pmi_dept dept ON dept.dept_code = bu.dept_code
           WHERE bu.dept_code IN (
               SELECT dept_code
               FROM pmi_dept
               WHERE parent_id = (SELECT id
                                  FROM pmi_dept
                                  WHERE dept_code = (SELECT DISTINCT dept_code
                                                     FROM pmsx_basic_user
                                                     WHERE user_code = #{paramMap.staffNo}))
               OR dept_code = (SELECT DISTINCT dept_code
                               FROM pmsx_basic_user
                               WHERE user_code = #{paramMap.staffNo})
           )
       ) t_01
       LEFT JOIN (
           SELECT user.user_code                          AS userCode,
                  mange.number                            AS `number`,
                  COUNT(DISTINCT to_person.repair_org_id) AS repairOrgCount,
                  CAST(CONCAT('[', GROUP_CONCAT(
                          CONCAT(
                                  '{"realStartDate":"', COALESCE(DATE_FORMAT(IF(mange.is_base_permanent = 1,
                                                                                to_person.plan_begin_time,
                                                                                mange.in_date), '%Y-%m-%d'), ''),
                                  '",',
                                  '"realEndDate":"', COALESCE(DATE_FORMAT(IF(mange.is_base_permanent = 1,
                                                                             to_person.plan_end_time,
                                                                             mange.out_date), '%Y-%m-%d'), ''), '",',
                                  '"basePlaceCode":"', COALESCE(repair_plan.base_code, ''), '",',
                                  '"basePlaceName":"', COALESCE(repair_plan.base_name, ''), '",',
                                  '"rowId":"', COALESCE(mange.id, ''), '",',
                                  '"relationId":"', COALESCE(to_person.id, ''), '",',
                                  '"orgId":"', COALESCE(repair_org.id, ''), '",',
                                  '"staffNo":"', COALESCE(user.user_code, ''), '",',
                                  '"teamCode":"', COALESCE(repair_org.code, ''), '",',
                                  '"teamName":"', COALESCE(repair_org.name, ''), '",',
                                  '"specialtyCode":"', COALESCE(repair_org_zy.code, ''), '",',
                                  '"specialtyName":"', COALESCE(repair_org_zy.name, ''), '",',
                                  '"repairRoundName":"', COALESCE(repair_org.repair_round, ''), '",',
                                  '"repairRoundCode":"', COALESCE(repair_org.repair_round, ''), '"}'
                              )
                          ), ']') AS CHAR)                    AS details
           FROM pmsx_basic_user user
           LEFT JOIN pmsx_person_mange mange ON mange.number = user.user_code
           LEFT JOIN pmsx_relation_org_to_person to_person ON mange.id = to_person.person_id
           LEFT JOIN pmsx_major_repair_org repair_org ON repair_org.id = to_person.repair_org_id AND repair_org.level = '3'
           LEFT JOIN pmsx_major_repair_org repair_org_zy ON repair_org_zy.id = repair_org.parent_id AND repair_org_zy.level = '2'
           LEFT JOIN pmsx_major_repair_plan repair_plan ON repair_plan.repair_round = repair_org.repair_round
           WHERE (mange.in_date IS NOT NULL or mange.out_date IS NOT NULL)
             and (to_person.plan_begin_time IS NOT NULL or to_person.plan_end_time IS NOT NULL)
             AND #{paramMap.realStartDate} <= mange.in_date
             AND mange.out_date <= #{paramMap.realEndDate}
           GROUP BY user.user_code
       ) t_02 ON t_02.userCode = t_01.userCode
    end note

else 查询物资调配数据
    ResourceAllocationOfMapper -> MySQL: SELECT ... FROM pmsx_material_manage LEFT JOIN pmsx_cost_center_master_data ...
    note right of MySQL
       SELECT t1.id,t1.name,t1.parentId,t1.number as `number`,t1.costCenterCode as costCenterCode,t1.userCode,t1.dataType,t2.repairOrgCount,t2.details
       FROM (
           select material_manage.id AS id,material_manage.asset_name AS `name`, dept.dept_code AS userCode,dept.id AS parentId,
                  material_manage.number AS `number`,material_manage.cost_center AS costCenterCode,'write' AS dataType
           FROM pmsx_material_manage material_manage
           LEFT JOIN pmsx_cost_center_master_data center_master ON center_master.kostl = material_manage.number
           LEFT JOIN pmi_dept dept ON dept.dept_code = center_master.dept_no
           WHERE dept.dept_code IN (
               SELECT dept_code
               FROM pmi_dept
               WHERE parent_id = (
                   SELECT id
                   FROM pmi_dept
                   WHERE dept_code = (
                       SELECT DISTINCT dept_code
                       FROM pmsx_basic_user
                       WHERE user_code = #{paramMap.staffNo}
                   )
               )
               OR dept_code = (
                   SELECT DISTINCT dept_code
                   FROM pmsx_basic_user
                   WHERE user_code = #{paramMap.staffNo}
               )
           )
       ) t1
       LEFT JOIN (
           SELECT
               material_manage.id AS `id`,
               material_manage.number AS `number`,
               material_manage.cost_center AS costCenterCode,
               COUNT(DISTINCT org_to_material.repair_org_id) AS repairOrgCount,
               CAST(CONCAT('[', GROUP_CONCAT(
                       CONCAT(
                               '{"realStartDate":"', COALESCE(DATE_FORMAT(material_manage.in_date, '%Y-%m-%d'), ''), '",',
                               '"realEndDate":"', COALESCE(DATE_FORMAT(material_manage.out_date, '%Y-%m-%d'), ''), '",',
                               '"basePlaceCode":"', COALESCE(repair_plan.base_code,''), '",',
                               '"basePlaceName":"', COALESCE(repair_plan.base_name,''), '",',
                               '"rowId":"', COALESCE(material_manage.id,''), '",',
                               '"staffNo":"', COALESCE(material_manage.number,''), '",',
                               '"teamCode":"', COALESCE(repair_org.code,''), '",',
                               '"teamName":"', COALESCE(repair_org.name,''), '",',
                               '"specialtyCode":"', COALESCE(repair_org_zy.code,''), '",',
                               '"specialtyName":"', COALESCE(repair_org_zy.name,''), '",',
                               '"repairRoundName":"', COALESCE(repair_org.repair_round,''), '",',
                               '"repairRoundCode":"', COALESCE(repair_org.repair_round, ''), '"}'
                       )
                   ), ']') AS CHAR )AS details
           FROM pmsx_material_manage material_manage
           LEFT JOIN pmsx_relation_org_to_material org_to_material ON org_to_material.material_id = material_manage.id
           LEFT JOIN pmsx_major_repair_org repair_org ON repair_org.id = org_to_material.repair_org_id AND repair_org.level = '3'
           LEFT JOIN pmsx_major_repair_org repair_org_zy ON repair_org_zy.id = repair_org.parent_id AND repair_org_zy.level = '2'
           LEFT JOIN pmsx_major_repair_plan repair_plan ON repair_plan.repair_round = repair_org.repair_round
           WHERE #{paramMap.realStartDate} <= material_manage.in_date AND material_manage.out_date <= #{paramMap.realEndDate}
           GROUP BY material_manage.id,material_manage.number,material_manage.cost_center
       ) t2 ON t2.id = t1.id

    end note
end
note right of MySQL: 根据参数查询调配数据

ResourceAllocationOfMapper --> ServiceImpl: 返回调配数据列表
ServiceImpl -> PersonMangeMapper: 查询人员管理数据 (可选)
PersonMangeMapper -> MySQL: SELECT ... FROM pmsx_person_mange
note right of MySQL: 查询人员管理信息

PersonMangeMapper --> ServiceImpl: 返回人员管理数据
ServiceImpl --> Controller: 返回调配数据列表
Controller -> Controller: 构建 ResponseDTO
Controller --> User: 返回 ResponseDTO

@enduml