package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 21 日
 **/
@TableName(value = "pms_amperering_config_kpi")
@ApiModel(value = "AmpereRingBoardConfigKpi对象", description = "安质环看板维护考核指标对象")
@Data
public class AmpereRingBoardConfigKpi extends ObjectEntity implements Serializable {
    /**
     * 事件等级code
     */
    @ApiModelProperty(value = "事件等级code")
    @TableField(value = "event_code")
    private String eventCode;

    /**
     * 事件等级
     */
    @ApiModelProperty(value = "事件等级")
    @TableField(value = "event_level")
    @NotEmpty(message = "事件等级不能为空")
    private String eventLevel;

    /**
     * 考核指标
     */
    @ApiModelProperty(value = "考核指标")
    @TableField(value = "kpi_code")
    @NotNull(message = "考核指标不能为空")
    private String kpiCode;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    @TableField(value = "sort")
    private Integer sort;

}
