<script setup lang="ts">
import {
  BasicButton, IDataStatus, isPower, Layout3,
} from 'lyra-component-vue3';
import {
  computed, onMounted, provide, reactive, ref, Ref, watchEffect,
} from 'vue';
import { useRoute } from 'vue-router';
import {
  UseBusinessWorkflowReturn, WorkflowAction, WorkflowProps, WorkflowView,
} from 'lyra-workflow-component-vue3';
import Api from '/@/api';
import EquivalentInfo from './components/EquivalentInfo.vue';

interface DetailsDataType {
  id: string,
  name: string,
  className: string,
  projectCode: string,
  ownerName?: string | undefined,
  status?: string | undefined | number,
  dataStatus?: IDataStatus | undefined,

  [propName: string]: any
}

const route = useRoute();
const actionId: Ref<string | null> = ref('');
const dataId = computed(() => route.params?.id);
const detailsData: DetailsDataType = reactive({
  id: '',
  name: '',
  className: '',
  projectCode: '',
});
provide('detailsData', detailsData);
const projectData = computed(() => ({
  name: detailsData.trainName,
  projectCode: detailsData.equivalentBaseName,
  dataStatus: detailsData.dataStatus,
  ownerName: detailsData.ownerName,
}));

const menuData = computed(() => [
  {
    id: 'info',
    name: '培训等效详情',
    powerCode: 'PMS_PXDXXQ_container_02',
  },
  {
    id: 'workflow',
    name: '审批流程',
    powerCode: 'PMS_PXDXXQ_container_03',
  },
]);

function menuChange({ id }) {
  actionId.value = id;
}

watchEffect(() => {
  if (!actionId.value || (actionId.value && menuData.value.findIndex((item) => item.id === actionId.value) === -1)) {
    actionId.value = menuData.value?.[0]?.id;
  }
});

onMounted(() => {
  getDetails();
});

const powerData: Ref<any[]> = ref(undefined);
provide('powerData', powerData);
const loading: Ref<boolean> = ref(false);

async function getDetails() {
  loading.value = true;
  try {
    const result: Record<string, any> = await new Api('/pms/train-equivalent').fetch({
      pageCode: 'PMSEquivalentDetails',
    }, dataId.value, 'GET');
    Object.keys(result || {}).forEach((key) => {
      detailsData[key] = result[key];
    });
    powerData.value = result?.detailAuthList;
  } finally {
    loading.value = false;
  }
}

const workflowLoading: Ref<boolean> = ref(false);
const showWorkflowAdd = computed(() => workflowActionRef.value?.isAdd && isPower('PMS_PXDXXQ_container_01_button_01', powerData) && !(detailsData?.status === 130 && detailsData?.isEquivalent));

function handleAddWorkflow() {
  workflowActionRef.value?.onAddTemplate({
    messageUrl: route.fullPath,
  }, 'start', workflowLoading);
}

const workflowViewRef: Ref<UseBusinessWorkflowReturn> = ref();
const workflowActionRef: Ref<UseBusinessWorkflowReturn> = ref();
const workflowProps = computed(() => ({
  Api,
  businessData: {
    ...detailsData,
    name: detailsData?.trainName,
  },
  async afterEvent() {
    await getDetails();
    await workflowViewRef.value?.init();
  },
} as WorkflowProps));
</script>

<template>
  <Layout3
    v-loading="loading"
    v-get-power="{powerData}"
    :defaultActionId="actionId"
    :menuData="menuData"
    :projectData="projectData"
    :type="2"
    @menuChange="menuChange"
  >
    <template #header-right>
      <BasicButton
        v-if="showWorkflowAdd"
        type="primary"
        :loading="workflowLoading"
        icon="sie-icon-qidongliucheng"
        @click="handleAddWorkflow"
      >
        发起流程
      </BasicButton>
    </template>
    <template v-if="detailsData?.id">
      <EquivalentInfo v-if="actionId==='info'" />
      <WorkflowView
        v-if="actionId==='workflow'"
        ref="workflowViewRef"
        :workflow-props="workflowProps"
      />
    </template>
    <template
      v-if="detailsData?.id"
      #footer
    >
      <WorkflowAction
        ref="workflowActionRef"
        :workflow-props="workflowProps"
      />
    </template>
  </Layout3>
</template>

<style scoped lang="less">

</style>
