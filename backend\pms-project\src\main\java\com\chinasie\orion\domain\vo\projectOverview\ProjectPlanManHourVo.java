package com.chinasie.orion.domain.vo.projectOverview;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/07/10:12
 * @description:
 */
@Data
@ApiModel(value = "ProjectPlanManHour对象", description = "项目工时")
public class ProjectPlanManHourVo implements Serializable {

    @ApiModelProperty(value = "预计工时")
    private BigDecimal manHour  = BigDecimal.valueOf(0.0);

    @ApiModelProperty(value = "实际工时")
    private BigDecimal realityManHour = BigDecimal.valueOf(0.0);



    @ApiModelProperty(value = "偏差工时")
    private BigDecimal deviationManHour = BigDecimal.valueOf(0.0);

    @ApiModelProperty(value = "剩余工时")
    private BigDecimal residueManHour = BigDecimal.valueOf(0.0);
    @ApiModelProperty(value = "工时进度")
    private BigDecimal manHourSchedule = BigDecimal.valueOf(0.0);

    @ApiModelProperty(value = "工时进度名称")
    private String manHourScheduleName="0.0%";

    @ApiModelProperty(value = "偏差率名称")
    private String deviationScheduleName="0.0%";
    @ApiModelProperty(value = "偏差率")
    private BigDecimal deviationSchedule = BigDecimal.valueOf(0.0);

    @ApiModelProperty(value = "计划")
    private Integer planCount =0 ;


}
