
-- 创建试图
CREATE VIEW quotation_requirement_view AS
SELECT pqm.*,prm.req_ownership req_ownership,prm.tech_res tech_res,prm.business_person
FROM pms_requirement_mangement prm left join pmsx_quotation_management pqm on(prm.id=pqm.requirement_id);

DELETE FROM pmi_data_power_rule
WHERE id='4';
DELETE FROM pmi_data_power_rule
WHERE id='5';
DELETE FROM pmi_data_power_rule
WHERE id='6';
DELETE FROM pmi_data_power_rule
WHERE id='7';

INSERT INTO pmi_data_power_rule
(id, code, name, conf, data_obj_id, data_obj_name, data_obj_table)
VALUES('4', 'NcfFormcontractInfo', '采购工程师合同权限', '{"ops":"OR","children":[{"key":{"value":"business_rsp_user","label":"商务负责人","dataType":"Varchar"},"op":"EQ","value":{"conditionExp":"#{@contractInfoSourcingEngineerExp.apply()}","paramMethodExp":"@contractInfoSourcingEngineerExp.exp(#param)","methodParam":"1","type":"CUSTOM"}},{"key":{"value":"class_name","label":"类名","dataType":"Varchar"},"op":"EQ","value":{"conditionExp":"#{@contractInfoUnSourcingEngineerExp.apply()}","paramMethodExp":"@contractInfoUnSourcingEngineerExp.exp(#param)","methodParam":"1","type":"CUSTOM"}}]}', 'gtjl6dbc3dc82e2a474992c96121dfc7f13a', 'NcfFormcontractInfo', 'ncf_form_contract_info');
INSERT INTO pmi_data_power_rule
(id, code, name, conf, data_obj_id, data_obj_name, data_obj_table)
VALUES('5', 'RequirementMangement', '需求管理的权限', '{"ops":"OR","children":[{"key":{"value":"class_name","label":"类名","dataType":"Varchar"},"op":"EQ","value":{"conditionExp":"#{@requirementCorporateHierarchyExp.apply()}","paramMethodExp":"@requirementCorporateHierarchyExp.exp(#param)","methodParam":"1","type":"CUSTOM"}},{"key":{"value":"req_ownership","label":"承担部门","dataType":"Varchar"},"op":"IN","value":{"conditionExp":"#{@departmentCenterMemberInterfaceRoleExp.apply()}","paramMethodExp":"@departmentCenterMemberInterfaceRoleExp.exp(#param)","methodParam":"1","type":"CUSTOM"}},{"key":{"value":"req_ownership","label":"承担部门","dataType":"Varchar"},"op":"EQ","value":{"conditionExp":"#{@uncommittedDepartmentExp.apply()}","paramMethodExp":"@uncommittedDepartmentExp.exp(#param)","methodParam":"1","type":"CUSTOM"}},{"key":{"value":"tech_res","label":"技术负责人","dataType":"Varchar"},"op":"IN","value":{"conditionExp":"#{@centerBusinessOrTechnicalExp.apply()}","paramMethodExp":"@centerBusinessOrTechnicalExp.exp(#param)","methodParam":"1","type":"CUSTOM"}},{"key":{"value":"business_person","label":"商务负责人","dataType":"Varchar"},"op":"IN","value":{"conditionExp":"#{@centerBusinessOrTechnicalExp.apply()}","paramMethodExp":"@centerBusinessOrTechnicalExp.exp(#param)","methodParam":"1","type":"CUSTOM"}}]}', 'gtjl1795371367655534592', 'RequirementMangement', 'pms_requirement_mangement');
INSERT INTO pmi_data_power_rule
(id, code, name, conf, data_obj_id, data_obj_name, data_obj_table)
VALUES('6', 'MarketContract', '合同管理的权限', '{"ops":"OR","children":[{"key":{"value":"class_name","label":"类名","dataType":"Varchar"},"op":"EQ","value":{"conditionExp":"#{@marketCorporateHierarchyExp.apply()}","paramMethodExp":"@marketCorporateHierarchyExp.exp(#param)","methodParam":"1","type":"CUSTOM"}},{"key":{"value":"tech_rsp_dept","label":"承担部门","dataType":"Varchar"},"op":"IN","value":{"conditionExp":"#{@departmentCenterMemberInterfaceRoleExp.apply()}","paramMethodExp":"@departmentCenterMemberInterfaceRoleExp.exp(#param)","methodParam":"1","type":"CUSTOM"}},{"key":{"value":"tech_rsp_dept","label":"承担部门","dataType":"Varchar"},"op":"EQ","value":{"conditionExp":"#{@uncommittedDepartmentExp.apply()}","paramMethodExp":"@uncommittedDepartmentExp.exp(#param)","methodParam":"1","type":"CUSTOM"}},{"key":{"value":"tech_rsp_user","label":"技术负责人","dataType":"Varchar"},"op":"IN","value":{"conditionExp":"#{@centerBusinessOrTechnicalExp.apply()}","paramMethodExp":"@centerBusinessOrTechnicalExp.exp(#param)","methodParam":"1","type":"CUSTOM"}},{"key":{"value":"commerce_rsp_user","label":"商务负责人","dataType":"Varchar"},"op":"IN","value":{"conditionExp":"#{@centerBusinessOrTechnicalExp.apply()}","paramMethodExp":"@centerBusinessOrTechnicalExp.exp(#param)","methodParam":"1","type":"CUSTOM"}}]}', 'gtjl1795333082197749760', 'MarketContract', 'pms_market_contract');
INSERT INTO pmi_data_power_rule
(id, code, name, conf, data_obj_id, data_obj_name, data_obj_table)
VALUES('7', 'QuotationRequirementView', '报价需求的权限', '{"ops":"OR","children":[{"key":{"value":"class_name","label":"类名","dataType":"Varchar"},"op":"EQ","value":{"conditionExp":"#{@quotationCorporateHierarchyExp.apply()}","paramMethodExp":"@quotationCorporateHierarchyExp.exp(#param)","methodParam":"1","type":"CUSTOM"}},{"key":{"value":"req_ownership","label":"承担部门","dataType":"Varchar"},"op":"IN","value":{"conditionExp":"#{@departmentCenterMemberInterfaceRoleExp.apply()}","paramMethodExp":"@departmentCenterMemberInterfaceRoleExp.exp(#param)","methodParam":"1","type":"CUSTOM"}},{"key":{"value":"req_ownership","label":"承担部门","dataType":"Varchar"},"op":"IN","value":{"conditionExp":"#{@uncommittedDepartmentExp.apply()}","paramMethodExp":"@uncommittedDepartmentExp.exp(#param)","methodParam":"1","type":"CUSTOM"}},{"key":{"value":"tech_res","label":"技术负责人","dataType":"Varchar"},"op":"IN","value":{"conditionExp":"#{@centerBusinessOrTechnicalExp.apply()}","paramMethodExp":"@centerBusinessOrTechnicalExp.exp(#param)","methodParam":"1","type":"CUSTOM"}},{"key":{"value":"business_person","label":"商务负责人","dataType":"Varchar"},"op":"IN","value":{"conditionExp":"#{@centerBusinessOrTechnicalExp.apply()}","paramMethodExp":"@centerBusinessOrTechnicalExp.exp(#param)","methodParam":"1","type":"CUSTOM"}}]}', 'gtjl1795650801921056768', 'QuotationRequirementView', 'quotation_requirement_view');