<template>
  <div
    v-loading="loading"
    class="add-document"
  >
    <BasicForm @register="register" />
  </div>
</template>
<script lang="ts" setup>
import {
  BasicForm, useForm,
} from 'lyra-component-vue3';
import { onMounted, ref, Ref } from 'vue';
import Api from '/@/api';
import { message } from 'ant-design-vue';

const props = withDefaults(defineProps<{
    drawerData:object
}>(), {
  drawerData: () => ({}),
});
const loading:Ref<boolean> = ref(false);
const [register, { setFieldsValue, validateFields, getFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas: [
    {
      field: 'name',
      label: '指标名称：',
      rules: [
        {
          required: true,
          message: '请输入指标名称',
        },
      ],
      colProps: { span: 24 },
      componentProps: {
        allowClear: true,
        placeholder: '请输入指标名称',
        maxlength: 40,
        showCount: true,
      },
      component: 'Input',
    },
    {
      field: 'description',
      label: '技术指标描述：',
      rules: [
        {
          required: true,
          message: '请输入技术指标描述',
        },
      ],
      colProps: { span: 24 },
      componentProps: {
        placeholder: '技术指标描述',
        maxlength: 1000,
        showCount: true,
        style: {
          height: '150px',
        },
      },
      component: 'InputTextArea',
    },
    {
      field: 'remark',
      label: '备注：',
      colProps: { span: 24 },
      componentProps: {
        placeholder: '请输入......',
        maxlength: 1000,
        showCount: true,
        style: {
          height: '150px',
        },
      },
      component: 'InputTextArea',
    },
  ],
});
onMounted(() => {
  if (props.drawerData.type === 'edit') {
    setFieldsValue(props.drawerData.record);
  }
});
defineExpose({
  async onSubmit() {
    let formData = await validateFields();
    if (props.drawerData.type === 'add') {
      formData.projectApprovalId = props.drawerData.projectApprovalId;
    } else {
      formData.id = props.drawerData.record.id;
    }
    await new Api('/pms').fetch(formData, props.drawerData.type === 'add' ? 'productPlan/add' : 'productPlan/edit', props.drawerData.type === 'add' ? 'POST' : 'PUT');
    message.success(props.drawerData.type === 'add' ? '新增产品成功' : '编辑产品成功');
  },
});
</script>
<style scoped lang="less">
.basic-card{
  margin: 0 !important;
  border: 0 !important;
  :deep(.card-content){
    margin: 10px !important;
  }
}
:deep(.ant-input-affix-wrapper){
  height: 100%;
}
</style>