package com.chinasie.orion.schedule;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.constant.ProjectStatusEnum;
import com.chinasie.orion.dict.MessageNodeNumberDict;
import com.chinasie.orion.domain.entity.PlanToRiskManagement;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.entity.RiskManagement;
import com.chinasie.orion.msc.api.MscBuildHandlerManager;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.service.PlanToRiskManagementService;
import com.chinasie.orion.service.ProjectSchemeService;
import com.chinasie.orion.service.ProjectService;
import com.chinasie.orion.service.RiskManagementService;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class ProjectRiskRelationPlanXxlJob {
    @Autowired
    private PlanToRiskManagementService planToRiskManagementService;

    @Autowired
    private ProjectSchemeService projectSchemeService;

    @Autowired
    private RiskManagementService riskManagementService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private MscBuildHandlerManager mscBuildHandlerManager;


    @XxlJob("projectRiskRelationPlan")
    public void riskPlanMessageReminder() throws Exception {
        List<PlanToRiskManagement> list = planToRiskManagementService.list(new LambdaQueryWrapper<>(PlanToRiskManagement.class).select(PlanToRiskManagement::getToId, PlanToRiskManagement::getFromId));
        if (CollectionUtil.isEmpty(list)){
            return;
        }
        List<RiskManagement> allRiskManagementList = riskManagementService.list(new LambdaQueryWrapperX<>(RiskManagement.class).select(RiskManagement::getId, RiskManagement::getProjectId, RiskManagement::getPrincipalId, RiskManagement::getPredictEndTime));
        if (CollectionUtil.isEmpty(allRiskManagementList)){
            return;
        }
        List<String> riskIds = list.stream().map(PlanToRiskManagement::getFromId).collect(Collectors.toList());

        List<String> planIds = list.stream().map(PlanToRiskManagement::getToId).collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(riskIds) && CollectionUtil.isNotEmpty(planIds)){
            List<ProjectScheme> projectSchemes = projectSchemeService.listByIds(planIds);
            List<RiskManagement> riskManagements = riskManagementService.listByIds(riskIds);
            List<String> projectIds = projectSchemes.stream().map(ProjectScheme::getProjectId).collect(Collectors.toList());
            List<Project> projects = projectService.list(new LambdaQueryWrapperX<>(Project.class)
                    .in(Project::getId, projectIds)
                    .notIn(Project::getStatus, CollUtil.toList(ProjectStatusEnum.PAUSED.getStatus(), ProjectStatusEnum.TERMINATED.getStatus())));

            Map<String, ProjectScheme>  projectSchemesMap = new HashMap<>();
            Map<String, RiskManagement>  riskManagementsMap = new HashMap<>();
            Map<String, Project>  projectMap = new HashMap<>();

            if (CollectionUtil.isNotEmpty(projectSchemes)){
                 projectSchemesMap = projectSchemes.stream().collect(Collectors.toMap(ProjectScheme::getId, m -> m));
            }
            if (CollectionUtil.isNotEmpty(riskManagements)){
                riskManagementsMap =  riskManagements.stream().collect(Collectors.toMap(RiskManagement::getId, m -> m));
            }

            if (CollectionUtil.isNotEmpty(projects)){
                projectMap = projects.stream().collect(Collectors.toMap(Project::getId, m -> m));
            }

            for (PlanToRiskManagement planToRiskManagement : list){
                String toId = planToRiskManagement.getToId();
                String fromId = planToRiskManagement.getFromId();
                if (projectSchemesMap.containsKey(toId) && riskManagementsMap.containsKey(fromId)){
                    ProjectScheme projectScheme = projectSchemesMap.get(toId);
                    RiskManagement riskManagement = riskManagementsMap.get(fromId);
                    // 风险负责人、计划责任人、项目经理发送消息通知
                    long day = DateUtil.betweenDay(new Date(), projectScheme.getBeginTime(), true);
                    if (day == 1 || day == 7){
                        if (projectMap.containsKey(projectScheme.getProjectId())) {
                            Project project = projectMap.get(projectScheme.getProjectId());
                            mscBuildHandlerManager.send(riskManagement, MessageNodeNumberDict.NODE_RISK_PLAN_MESSAGE_REMINDER, Arrays.asList(projectScheme.getRspUser(), riskManagement.getPrincipalId(), project.getPm()));
                        }
                    }

                }
            }

            for (RiskManagement riskManagement : allRiskManagementList){
                if (!riskIds.contains(riskManagement.getId())){
                    //风险若未关联计划，则按照期望完成时间进行提醒
                    long day = DateUtil.betweenDay(new Date(), riskManagement.getPredictEndTime(), true);
                    if (day == 1 || day == 7){
                        if (projectMap.containsKey(riskManagement.getProjectId())) {
                            Project project = projectMap.get(riskManagement.getProjectId());
                            mscBuildHandlerManager.send(riskManagement, MessageNodeNumberDict.NODE_RISK_PLAN_MESSAGE_REMINDER, Arrays.asList(riskManagement.getPrincipalId(), project.getPm()));

                        }
                    }
                }
            }

        }
    }
}
