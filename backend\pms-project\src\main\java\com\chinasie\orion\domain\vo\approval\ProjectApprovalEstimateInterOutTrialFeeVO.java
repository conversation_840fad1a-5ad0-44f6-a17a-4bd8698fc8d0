package com.chinasie.orion.domain.vo.approval;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;
import java.lang.Integer;

/**
 * ProjectApprovalEstimateInterOutTrialFees VO对象
 *
 * <AUTHOR>
 * @since 2024-05-07 10:45:22
 */
@ApiModel(value = "ProjectApprovalEstimateInterOutTrialFeesVO对象", description = "概算内外部试验费")
@Data
public class ProjectApprovalEstimateInterOutTrialFeeVO extends ObjectVO implements Serializable{

    /**
     * 类型：内部、外部
     */
    @ApiModelProperty(value = "类型：内部、外部")
    private String type;


    /**
     * 台数
     */
    @ApiModelProperty(value = "台数")
    private Integer num;


    /**
     * 批次
     */
    @ApiModelProperty(value = "批次")
    private Integer batch;


    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    private BigDecimal price;


    /**
     * 设备参数
     */
    @ApiModelProperty(value = "设备参数")
    private String deviceParam;


    /**
     * 试验量
     */
    @ApiModelProperty(value = "试验量")
    private BigDecimal trialNum;


    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;


    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String unitName;


    /**
     * 内外部试验项目基础数据id
     */
    @ApiModelProperty(value = "内外部试验项目基础数据id")
    private String trialBasicDataId;

    /**
     * 项目立项id
     */
    @ApiModelProperty(value = "项目立项id")
    private String projectApprovalId;


    /**
     * 试验费用
     */
    @ApiModelProperty(value = "试验费用")
    private BigDecimal trialFee;


    /**
     * 试验周期天数
     */
    @ApiModelProperty(value = "试验周期天数")
    private BigDecimal trialDay;

    /**
     * 试验项目名称
     */
    @ApiModelProperty(value = "试验项目名称")
    private String name;

}
