package com.chinasie.orion.excel.handler;


import com.chinasie.orion.constant.CostDictNumberConstant;
import com.chinasie.orion.constant.CostTypeToUnit;
import com.chinasie.orion.domain.dto.TopCenterExportDTO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.util.ServletUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class MyExcelUtils extends ExcelUtils {

    public static void writeSheet(List<Map<String, Object>> sheetHeads, List<TopCenterExportDTO> exportDTO, String fileName, String sheet1, HttpServletResponse response) throws Exception {
        Map<String, Object> headMap = new HashMap<>();
        List<String> headList = new ArrayList<>();
        headList.add("序号");
        headList.add("中心名称");
        headList.add("人力成本预算总额");
        headList.add("人力预算执行总额");
        headList.add("岗级计划总人数");
        headList.add("岗级实际总人数");
        for (Map<String, Object> sheetHead : sheetHeads) {
            headMap.putAll(sheetHead);
            headList.addAll(sheetHead.keySet());
        }
        //创建一个新的Excel工作表
        Workbook workbook = new XSSFWorkbook();
        //创建一个工作表
        Sheet sheet = workbook.createSheet(sheet1);
        //表头
        Row headRow = sheet.createRow(0);
        Integer cellIndex = 0;
        CellStyle cellStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        cellStyle.setFont(font);
        //设置第一行的宽度未50个字符
        for (int i = 0; i < 25; i++) {
            sheet.setColumnWidth(i, 18 * 256);
        }
        for (String head : headList) {
            Cell cell = headRow.createCell(cellIndex++);
            cell.setCellValue(head);
            cell.setCellStyle(cellStyle);
        }

        //导入数据
        //行数
        int rowIndex = 1;
        int number = 1;
        for (TopCenterExportDTO topCenterExportDTO : exportDTO) {
            Row row = sheet.createRow(rowIndex++);
            //列数
            //处理外层固定数据
            int dataCellIndex = 0;
            Cell cellOne = row.createCell(dataCellIndex++);
            cellOne.setCellValue(number++);

            Cell cellTwo = row.createCell(dataCellIndex++);
            cellTwo.setCellValue(topCenterExportDTO.getCenterName());

            Cell cellThree = row.createCell(dataCellIndex++);
            cellThree.setCellValue(topCenterExportDTO.getTotalPeopleBudget());

            Cell cellFour = row.createCell(dataCellIndex++);
            cellFour.setCellValue(topCenterExportDTO.getActualPeopleBudget());

            Cell cellFive = row.createCell(dataCellIndex++);
            cellFive.setCellValue(topCenterExportDTO.getTotalPeople());

            Cell cellSix = row.createCell(dataCellIndex++);
            cellSix.setCellValue(topCenterExportDTO.getActualPeople());

//            Cell cellSeven = row.createCell(dataCellIndex++);
//            cellSeven.setCellValue(topCenterExportDTO.getTotalPositionBudget());
//
//            Cell cellEight = row.createCell(dataCellIndex++);
//            cellEight.setCellValue(topCenterExportDTO.getActualPositionBudget());

            //处理内层list
            List<String> data = topCenterExportDTO.getSheetHeads();
            for (String datum : data) {
                row.createCell(dataCellIndex++).setCellValue(datum);
            }
        }
        ServletUtils.writeAttachment(fileName, response);
        ServletOutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        workbook.close();
    }

    public static void createTemplateCustomer(HttpServletResponse response, String fileName, DictRedisHelper dictRedisHelper) throws Exception {
        XSSFWorkbook sheets = new XSSFWorkbook();
        XSSFSheet sheet1 = sheets.createSheet("sheet1");
        XSSFSheet sheet2 = sheets.createSheet("sheet2");
        XSSFSheet sheet3 = sheets.createSheet("sheet3");

        //准备表头数据
        List<String> head1 = Arrays.asList("序号", "合同编号", "合同名称", "成本类型", "成本名称", "单价","备注");
        List<String> head2 = Arrays.asList("序号", "合同编号", "合同名称", "用人单位代号", "用人单位名称");
        List<String> head3 = Arrays.asList("序号", "合同编号", "合同名称", "考核类别", "考核内容", "评分标准");

        //准备下拉框数据
        List<DictValueVO> byDictNumber = dictRedisHelper.getByDictNumber(CostDictNumberConstant.COST_DICT_NUMBER, CurrentUserHelper.getOrgId());
        List<String> costTypeName = byDictNumber.stream().map(DictValueVO::getDescription).collect(Collectors.toList());
        Map<String, String> map = CostTypeToUnit.getMap();
        List<String> selections = new ArrayList<>();
        for (String select : costTypeName) {
            selections.add(select+"("+map.getOrDefault(select,"")+")");
        }

        insertValue(sheets,sheet1, sheet1.createRow(1), head1, true, selections, 3);
        insertValue(sheets,sheet2, sheet2.createRow(1), head2, false, null, 0);
        insertValue(sheets,sheet3, sheet3.createRow(1), head3, false, null, 0);
        ServletUtils.writeAttachment(fileName,response);
        sheets.write(response.getOutputStream());
        sheets.close();
    }

    public static void insertValue(XSSFWorkbook workbook,XSSFSheet sheet, Row row, List<String> headList, Boolean isNeedSelections, List<String> selections, Integer columnIndex) {
        //字体
        CellStyle cellStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        cellStyle.setFont(font);
        //合并第一行单元格
        sheet.addMergedRegion(new CellRangeAddress(0,0,0,headList.size()-1));

        if (isNeedSelections) {
            for (int i = 0; i < headList.size(); i++){
                sheet.setColumnWidth(i, 30 * 256);
                Cell cell = row.createCell(i);
                cell.setCellValue(headList.get(i));
                cell.setCellStyle(cellStyle);
            }
            //设置下拉框

            XSSFDataValidationHelper dvHelper = new XSSFDataValidationHelper(sheet);
            XSSFDataValidationConstraint dvConstraint = (XSSFDataValidationConstraint)dvHelper.createExplicitListConstraint(selections.toArray(new String[0]));
            CellRangeAddressList addressList = new CellRangeAddressList(2, 5000, columnIndex, columnIndex);
            DataValidation validation = dvHelper.createValidation(dvConstraint, addressList);

            //设置单元格中只能是列表中的内容，否则报错
            validation.setSuppressDropDownArrow(true);
            validation.setShowErrorBox(true);
            sheet.addValidationData(validation);

        } else {
            for (int i = 0; i < headList.size(); i++) {
                sheet.setColumnWidth(i, 30 * 256);
                Cell cell = row.createCell(i);
                cell.setCellValue(headList.get(i));
                cell.setCellStyle(cellStyle);
            }
        }


    }


    public static void combineCells(XSSFSheet sheet,Integer rows, Integer cells){
        XSSFRow row = sheet.createRow(rows);



    }


}
