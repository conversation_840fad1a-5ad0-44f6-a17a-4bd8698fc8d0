package com.chinasie.orion.service;

import com.chinasie.orion.domain.entity.ContractOurSignedMain;
import com.chinasie.orion.domain.vo.ContractOurSignedMainVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;

/**
 * <p>
 * ContractOurSignedMain 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24 10:10:42
 */
public interface ContractOurSignedMainService extends OrionBaseService<ContractOurSignedMain> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    ContractOurSignedMainVO detail(String id) throws Exception;

    /**
     * 根据合同id获取详情
     *
     * @param contractId
     * @return
     * @throws Exception
     */
    ContractOurSignedMainVO detailByContractId(String contractId) throws Exception;

}
