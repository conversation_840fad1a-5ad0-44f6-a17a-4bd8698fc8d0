package com.chinasie.orion.service;

import com.chinasie.orion.domain.vo.SimStatusVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.domain.dto.MajorRepairPlanDTO;
import com.chinasie.orion.domain.entity.MajorRepairPlan;
import com.chinasie.orion.domain.vo.MajorRepairPlanVO;
import com.chinasie.orion.sdk.domain.vo.SimpleVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/08/10:41
 * @description:
 */

public interface MajorRepairPlanService extends OrionBaseService<MajorRepairPlan> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    MajorRepairPlanVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param majorRepairPlanDTO
     */
    String create(MajorRepairPlanDTO majorRepairPlanDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param majorRepairPlanDTO
     */
    Boolean edit(MajorRepairPlanDTO majorRepairPlanDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<MajorRepairPlanVO> pages(Page<MajorRepairPlanDTO> pageRequest) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<MajorRepairPlanVO> vos) throws Exception;

    List<SimStatusVO> statusList();

    List<MajorRepairPlanVO> listByNumberList(List<String> majorRepairTurnList);

    /**
     *  获取大修数据列表
     * @param majorRepairPlanDTO
     * @return
     */
    List<MajorRepairPlanVO> listByEntity(MajorRepairPlanDTO majorRepairPlanDTO);


    /**
     *  获取大修轮次列表
     * @return
     */
    List<String>  getRepairRoundList();

    void majorRepairPlanChangeStatusHandler();

    /**
     *  小于等于两年的大修计划接口
     * @return
     */
    List<SimpleVO> leTowlist();

    /**
     *  获取简化版本的数据Map
     * @param relationMajorIdList
     * @return
     */
    Map<String, String> getSipMapList(List<String> relationMajorIdList);


    /**
     *  通过大修轮次获取 简化版本的大修信息
     * @param repairRound
     * @return
     */
    MajorRepairPlan getSimpleByRepairRound(String repairRound);

    List<MajorRepairPlanVO> unfinishList();

    public Boolean isExist(String repairRound, String id);

    MajorRepairPlan getNameByRepairRound(String repairRound);
}

