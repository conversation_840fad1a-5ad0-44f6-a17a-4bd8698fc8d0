package com.chinasie.orion.domain.dto.job;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/10/15/14:43
 * @description:
 */
@Data
public class JobPageProgressDTO implements Serializable {

    @ApiModelProperty(value = "重大项目id")
    private String importProjectId;
    @ApiModelProperty(value = "重大项目名称")
    private String importProjectName;
    @ApiModelProperty(value = "所处日期")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date workDate;
}
