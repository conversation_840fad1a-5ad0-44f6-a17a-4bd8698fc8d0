package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/18/15:00
 * @description:
 */
@Data
@TableName(value = "pms_change_management")
@ApiModel(value = "ChangeManagement对象", description = "变更管理")
public class ChangeManagement extends ObjectEntity {

    /**
     * 原因
     */
    @ApiModelProperty(value = "原因")
    @TableField(value = "reason")
    private String reason;

    /**
     * 预览图
     */
    @ApiModelProperty(value = "预览图")
    @TableField(value = "preview_path")
    private String previewPath;

}
