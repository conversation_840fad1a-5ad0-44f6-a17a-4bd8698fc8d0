package com.chinasie.orion.management.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * QuotationManageCustContact VO对象
 *
 * <AUTHOR>
 * @since 2024-09-06 16:23:59
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "QuotationManageCustContactVO对象", description = "报价管理-客户-联系人")
@Data
public class QuotationManageCustContactVO extends ObjectVO implements Serializable {

    /**
     * 报价单id，pmsx_quotation_management id
     */
    @ApiModelProperty(value = "报价单id，pmsx_quotation_management id")
    private String quotationId;


    /**
     * 客户联系人id，pms_customer_contact id
     */
    @ApiModelProperty(value = "客户联系人id，pms_customer_contact id")
    private String custContactId;


    /**
     * 联系人名称
     */
    @ApiModelProperty(value = "联系人名称")
    private String contactName;


    /**
     * 联系人手机号
     */
    @ApiModelProperty(value = "联系人手机号")
    private String contactPhone;


    /**
     * 联系人类型；business.商务联系人；technology.技术负责人
     */
    @ApiModelProperty(value = "联系人类型；business.商务联系人；technology.技术负责人")
    private String contactType;

    private String contactTypeName;

}
