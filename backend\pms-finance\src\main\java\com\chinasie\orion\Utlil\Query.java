package com.chinasie.orion.Utlil;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD})
public @interface Query {
    String EQ = "eq";
    String LIKE = "like";
    String LIKE_LEFT = "likeLeft";
    String LIKE_RIGHT = "likeRight";

    String type();

    /**
     * 别名，联合查询是表别名
     */
    String alias() default "";
}
