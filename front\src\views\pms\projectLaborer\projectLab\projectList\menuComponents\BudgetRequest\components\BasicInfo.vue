<script setup lang="ts">
import {
  getDictByNumber, BasicButton, BasicCard, OrionTable, InputMoney, isPower, Layout,
} from 'lyra-component-vue3';
import {
  computed,
  ComputedRef,
  h,
  inject, reactive, ref, Ref,
  onMounted,
} from 'vue';
import { openFormDrawer } from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/BudgetRequest/utils';
import BudgetRequestEdit
  from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/BudgetRequest/BudgetRequestEdit.vue';
import {
  Select as ASelect, Modal, DatePicker, TreeSelect as ATreeSelect, message,
} from 'ant-design-vue';
import Api from '/@/api';

import { differenceBy } from 'lodash-es';

const detailsData: Record<string, any> = inject('detailsData', reactive({}));
const baseInfoProps = reactive({
  list: [
    {
      label: '预算申请单编码',
      field: 'number',
    },
    {
      label: '预算申请单名称',
      field: 'name',
    },
    {
      label: '项目编码',
      field: 'projectNumber',
    },
    {
      label: '来源项目名称',
      field: 'projectName',
    },

    {
      label: '概算金额',
      field: 'estimateMoney',
    },
    {
      label: '申请预算金额',
      field: 'budgetMoney',
    },
    {
      label: '状态',
      field: 'dataStatus',
    },
    {
      label: '创建时间',
      field: 'createTime',
      formatTime: 'YYYY-MM-DD HH:mm:ss',
    },
  ],
  column: 4,
  dataSource: detailsData,
});

const dataSource:Ref<Record<any, any>[]> = ref([]);
const selectedRowKeys = ref([]);
const tableOptions = {
  showToolButton: false,
  isSpacing: true,
  pagination: false,
  rowSelection: {
    selectedRowKeys,
    onChange: (keys = []) => {
      selectedRowKeys.value = keys;
    },
  },

  showSmallSearch: false,
  columns: [
    {
      title: '预算申请编码',
      dataIndex: 'number',
    },
    {
      title: '预算名称',
      dataIndex: 'name',
      width: 250,
      slots: { customRender: 'name' },
    },
    {
      title: '成本中心',
      dataIndex: 'costCenterId',
      width: 200,
      slots: { customRender: 'costCenterId' },
    },
    {
      title: '科目名称',
      dataIndex: 'expenseSubjectName',
      width: 250,
      slots: { customRender: 'expenseSubjectName' },
    },
    {
      title: '科目编码',
      dataIndex: 'expenseSubjectNumber',
      slots: { customRender: 'expenseSubjectNumber' },
    },
    {
      title: '期间类型',
      dataIndex: 'timeType',
      width: 100,
      slots: { customRender: 'timeType' },
    },
    {
      title: '预算期间',
      dataIndex: 'budgetTime',
      width: 130,
      slots: { customRender: 'budgetTime' },
    },
    {
      title: '预算对象类型',
      dataIndex: 'budgetObjectType',
      slots: { customRender: 'budgetObjectType' },
    },
    {
      title: '预算对象',
      dataIndex: 'budgetObjectName',
    },
    {
      title: '币种',
      dataIndex: 'currency',
      slots: { customRender: 'currency' },
    },

    {
      title: '预算申请总额（元）',
      dataIndex: 'budgetMoney',
      slots: { customRender: 'budgetMoney' },
    },

    {
      title: '1月',
      dataIndex: 'januaryMoney',
      slots: { customRender: 'januaryMoney' },
    },
    {
      title: '2月',
      dataIndex: 'februaryMoney',
      slots: { customRender: 'februaryMoney' },
    },
    {
      title: '3月',
      dataIndex: 'marchMoney',
      slots: { customRender: 'marchMoney' },
    },
    {
      title: '第一季度',
      dataIndex: 'firstQuarterMoney',
      slots: { customRender: 'firstQuarterMoney' },
    },
    {
      title: '4月',
      dataIndex: 'aprilMoney',
      slots: { customRender: 'aprilMoney' },
    },
    {
      title: '5月',
      dataIndex: 'mayMoney',
      slots: { customRender: 'mayMoney' },
    },
    {
      title: '6月',
      dataIndex: 'juneMoney',
      slots: { customRender: 'juneMoney' },
    },
    {
      title: '第二季度',
      dataIndex: 'secondQuarter',
      slots: { customRender: 'secondQuarter' },
    },
    {
      title: '7月',
      dataIndex: 'julyMoney',
      slots: { customRender: 'julyMoney' },
    },
    {
      title: '8月',
      dataIndex: 'augustMoney',
      slots: { customRender: 'augustMoney' },
    },
    {
      title: '9月',
      dataIndex: 'septemberMoney',
      slots: { customRender: 'septemberMoney' },
    },
    {
      title: '第三季度',
      dataIndex: 'thirdQuarter',
      slots: { customRender: 'thirdQuarter' },
    },
    {
      title: '10月',
      dataIndex: 'octoberMoney',
      slots: { customRender: 'octoberMoney' },
    },
    {
      title: '11月',
      dataIndex: 'novemberMoney',
      slots: { customRender: 'novemberMoney' },
    },
    {
      title: '12月',
      dataIndex: 'decemberMoney',
      slots: { customRender: 'decemberMoney' },
    },
    {
      title: '第四季度',
      dataIndex: 'fourthQuarter',
      slots: { customRender: 'fourthQuarter' },
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 220,
      slots: { customRender: 'action' },
    },
  ],
  actions: [
    {
      text: '复制',
      isShow: () => isPower('PMS_YSSQXQ_container_02_button_05', detailsData.value),
      onClick(record) {
        copyTableDataById(record.id);
      },
    },
    {
      text: '删除',
      isShow: () => isPower('PMS_YSSQXQ_container_02_button_03', detailsData.value),
      modal: (record:any) => fakerDelete(record.id),
    },
  ],
  api: () => new Promise((resolve) => resolve(dataSource.value)),
};
const tableOptionsShow = {
  showToolButton: false,
  isSpacing: true,
  pagination: false,
  rowSelection: {
    selectedRowKeys,
    onChange: (keys = []) => {
      selectedRowKeys.value = keys;
    },
  },

  showSmallSearch: false,
  columns: [
    {
      title: '预算申请编码',
      dataIndex: 'number',
    },
    {
      title: '预算名称',
      dataIndex: 'name',
      width: 250,

    },
    {
      title: '成本中心',
      dataIndex: 'costCenterName',
      width: 200,

    },
    {
      title: '科目名称',
      dataIndex: 'expenseSubjectName',
      width: 250,

    },
    {
      title: '科目编码',
      dataIndex: 'expenseSubjectNumber',

    },
    {
      title: '期间类型',
      dataIndex: 'timeTypeName',
      width: 100,

    },
    {
      title: '预算期间',
      dataIndex: 'budgetTime',
      width: 130,
    },
    {
      title: '预算对象类型',
      dataIndex: 'budgetObjectTypeName',
    },
    {
      title: '预算对象',
      dataIndex: 'budgetObjectName',
    },
    {
      title: '币种',
      dataIndex: 'currencyName',
    },
    {
      title: '预算申请总额（元）',
      dataIndex: 'budgetMoney',

    },

    {
      title: '1月',
      dataIndex: 'januaryMoney',
    },
    {
      title: '2月',
      dataIndex: 'februaryMoney',

    },
    {
      title: '3月',
      dataIndex: 'marchMoney',
    },
    {
      title: '第一季度',
      dataIndex: 'firstQuarterMoney',
    },
    {
      title: '4月',
      dataIndex: 'aprilMoney',

    },
    {
      title: '5月',
      dataIndex: 'mayMoney',

    },
    {
      title: '6月',
      dataIndex: 'juneMoney',

    },
    {
      title: '第二季度',
      dataIndex: 'secondQuarter',

    },
    {
      title: '7月',
      dataIndex: 'julyMoney',

    },
    {
      title: '8月',
      dataIndex: 'augustMoney',

    },
    {
      title: '9月',
      dataIndex: 'septemberMoney',

    },
    {
      title: '第三季度',
      dataIndex: 'thirdQuarter',
    },
    {
      title: '10月',
      dataIndex: 'octoberMoney',
    },
    {
      title: '11月',
      dataIndex: 'novemberMoney',

    },
    {
      title: '12月',
      dataIndex: 'decemberMoney',
    },
    {
      title: '第四季度',
      dataIndex: 'fourthQuarter',
    },
  ],
  actions: [
    {
      text: '复制',
      onClick(record) {
        copyTableDataById(record.id);
      },

    },
    {
      text: '删除',
      modal: (record:any) => fakerDelete(record.id),
    },
  ],
  api: () => new Promise((resolve) => resolve(dataSource.value)),
};
function copyTableDataById(id) {
  let tableData = tableRef.value.getDataSource();
  // 找到与给定 id 匹配的记录索引
  let index = tableData.findIndex((item) => item.id === id);
  if (index !== -1) {
    // 复制找到的记录
    let copyData = { ...tableData[index] };
    copyData.number = '';
    copyData.id = generateRandomString(10);
    // 在目标记录的后面插入复制的数据
    tableData.splice(index + 1, 0, copyData);
    // 更新 dataSource
    dataSource.value = tableData;
    // 重新加载表格
    tableRef.value.reload();
  }
}
function fakerDeleteArr(idToDelete) {
  let tableData = tableRef.value.getDataSource();
  // 使用 Array.prototype.filter 过滤出不包含在 idToDelete 中的项
  tableData = tableData.filter((item) => !idToDelete.includes(item.id));
  dataSource.value = tableData;
  tableRef.value.reload();
}

function fakerDelete(idToDelete) {
  return new Promise((resolve, reject) => {
    let tableData = tableRef.value.getDataSource();
    tableData = tableData.filter((item) => item.id !== idToDelete);
    dataSource.value = tableData;
    tableRef.value.reload();
    resolve(true);
  });
}
function getBudgetTableData() {
  new Api('/pms/budgetApplication').fetch('', `list?formId=${detailsData?.id}`, 'GET').then((res) => {
    dataSource.value = res;
    tableRef.value.reload();
  });
}
const toolButtons: ComputedRef<Record<string, any>[]> = computed(() => [
  {
    event: 'importEstimateList',
    text: '从概算导入',
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
    code: 'PMS_YSSQXQ_container_02_button_01',
  },
  {
    event: 'addBudget',
    text: '新增预算',
    icon: 'sie-icon-tianjiaxinzeng',
    code: 'PMS_YSSQXQ_container_02_button_02',
  },
  {
    event: 'delete',
    text: '删除',
    icon: 'delete',
    disabled: selectedRowKeys.value.length === 0,
    code: 'PMS_YSSQXQ_container_02_button_03',
  },
  {
    event: 'save',
    text: '保存',
    icon: 'sie-icon-baocun',
    code: 'PMS_YSSQXQ_container_02_button_04',
  },
]);
const costCenterOptions = ref();
async function costCenterIdListApi() {
  const res = await new Api('/pms/costCenter/list').fetch('', '', 'POST');
  costCenterOptions.value = res;
}
function getButtonProps(item) {
  return item;
}
const timeTypeOptions = ref([]);
const budgetObjectTypeOptions = ref([]);
const currencyOptions = ref([]);
const expenseSubjectNumberTreeData = ref([]);
async function getDictOptions() {
  new Api(`/pms/expenseSubject/tree?status=${1}`).fetch('', '', 'GET').then((res) => {
    expenseSubjectNumberTreeData.value = res;
  });
  timeTypeOptions.value = await getDictByNumber('budgetPeriodType');
  budgetObjectTypeOptions.value = await getDictByNumber('budgetObjectType');
  currencyOptions.value = await getDictByNumber('budgetCurrency');
}
function changeTimeType(value, record, index) {
  let obj = {
    januaryMoney: null,
    februaryMoney: null,
    marchMoney: null,
    firstQuarterMoney: null,
    aprilMoney: null,
    mayMoney: null,
    juneMoney: null,
    secondQuarter: null,
    julyMoney: null,
    augustMoney: null,
    septemberMoney: null,
    thirdQuarter: null,
    octoberMoney: null,
    novemberMoney: null,
    decemberMoney: null,
    fourthQuarter: null,
    budgetMoney: null,
  };

  for (let key in record) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      record[key] = obj[key];
    }
  }

  return record; // 如果需要返回修改后的record
}

onMounted(() => {
  getDictOptions();
  costCenterIdListApi();
  getBudgetTableData();
});
const tableRef = ref();

function generateRandomString(length) {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let randomString = '';

  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    randomString += characters.charAt(randomIndex);
  }
  return `add-sie-${randomString}`;
}
function addBudgetFun() {
  let tableData = tableRef.value.getDataSource();
  tableData.push(
    {
      id: generateRandomString(10),
      number: '',
      name: '',
      budgetObjectId: detailsData?.projectId,
      projectId: detailsData?.projectId,
      budgetObjectName: detailsData?.projectName,
      costCenterId: '',
      expenseSubjectName: '',
      expenseSubjectNumber: '',
      timeType: '',
      budgetTime: '',
      budgetObjectType: 'budgetProject',
      currency: 'budgetRMB',
      budgetMoney: null,
      januaryMoney: null,
      februaryMoney: null,
      marchMoney: null,
      firstQuarterMoney: null,
      formId: detailsData?.id,
      aprilMoney: null,
      mayMoney: null,
      juneMoney: null,
      secondQuarter: null,
      julyMoney: null,
      augustMoney: null,
      septemberMoney: null,
      thirdQuarter: null,
      octoberMoney: null,
      novemberMoney: null,
      decemberMoney: null,
      fourthQuarter: null,

    },
  );
  dataSource.value = tableData;
  tableRef.value.reload();
}
async function getSaveBatchParams() {
  const originData = await new Api('/pms/budgetApplication/list').fetch('', `?formId=${detailsData?.id}`, 'GET');
  return compareData(originData, dataSource.value);
}

function compareData(originData, tableData) {
  const createBudgetApplicationLists = [];
  const updateBudgetApplicationLists = [];
  const deleteIds = [];

  const diff = differenceBy(originData, tableData, 'id');

  diff.forEach((data) => {
    deleteIds.push(data.id);
  });

  tableData.forEach((data) => {
    const id = data.id;
    const startsWithAddSie = id.startsWith('add-sie');

    if (startsWithAddSie) {
      delete data.id;
      createBudgetApplicationLists.push(data);
    } else {
      updateBudgetApplicationLists.push(data);
    }
  });

  return {
    createBudgetApplicationLists,
    updateBudgetApplicationLists,
    deleteIds,
  };
}

async function saveBatchFun() {
  let params = await getSaveBatchParams();
  const result = await new Api('/pms/budgetApplication/saveBatch').fetch(params, '', 'POST');
  if (result) {
    message.success('保存成功');
    getBudgetTableData();
  }
}
function mergeObjects(obj1, obj2Array) {
  return obj2Array.map((obj2) => {
    const merged = {};

    for (let key in obj1) {
      if (Object.prototype.hasOwnProperty.call(obj1, key)) {
        if (key === 'id') {
          // 对 id 键进行特殊处理
          merged[key] = generateRandomString(10);
        } else if (Object.prototype.hasOwnProperty.call(obj2, key) && (obj2[key] !== null && obj2[key] !== '')) {
          merged[key] = obj2[key];
        } else {
          merged[key] = obj1[key];
        }
      }
    }

    // 处理 obj2 中 obj1 没有的属性
    for (let key in obj2) {
      if (Object.prototype.hasOwnProperty.call(obj2, key) && !Object.prototype.hasOwnProperty.call(obj1, key)) {
        merged[key] = obj2[key];
      }
    }

    return merged;
  });
}

function importEstimateList() {
  new Api('/pms/budgetApplication/getEstimateList').fetch({ projectId: detailsData?.projectId }, '', 'GET').then(((res) => {
    if (res && res.length > 0) {
      let obj1 = {
        id: generateRandomString(10),
        number: '',
        name: '',
        budgetObjectId: detailsData?.projectId,
        projectId: detailsData?.projectId,
        budgetObjectName: detailsData?.projectName,
        costCenterId: '',
        expenseSubjectName: '',
        expenseSubjectNumber: '',
        timeType: '',
        budgetTime: '',
        budgetObjectType: 'budgetProject',
        currency: 'budgetRMB',
        budgetMoney: null,
        januaryMoney: null,
        februaryMoney: null,
        marchMoney: null,
        firstQuarterMoney: null,
        formId: detailsData?.id,
        aprilMoney: null,
        mayMoney: null,
        juneMoney: null,
        secondQuarter: null,
        julyMoney: null,
        augustMoney: null,
        septemberMoney: null,
        thirdQuarter: null,
        octoberMoney: null,
        novemberMoney: null,
        decemberMoney: null,
        fourthQuarter: null,
      };

      let resultArray = mergeObjects(obj1, res);
      dataSource.value.push(...resultArray);
      // 重新加载表格
      tableRef.value.reload();
    }
  }));
}

function toolClick({ event }: Record<string, any>) {
  switch (event) {
    case 'importEstimateList':
      importEstimateList();
      break;
    case 'delete':
      fakerDeleteArr(selectedRowKeys.value);
      break;
    case 'save':
      saveBatchFun();
      break;
    case 'addBudget':
      addBudgetFun();
      break;
  }
}

function calculateQuarterMoney(record, quarterNumber = 0) {
  if (record.timeType === 'budgetMonth') {
    let months;
    let quarter;
    switch (quarterNumber) {
      case 1:
        months = [
          'januaryMoney',
          'februaryMoney',
          'marchMoney',
        ];
        quarter = 'firstQuarterMoney';
        break;
      case 2:
        months = [
          'aprilMoney',
          'mayMoney',
          'juneMoney',
        ];
        quarter = 'secondQuarter';
        break;
      case 3:
        months = [
          'julyMoney',
          'augustMoney',
          'septemberMoney',
        ];
        quarter = 'thirdQuarter';
        break;
      case 4:
        months = [
          'octoberMoney',
          'novemberMoney',
          'decemberMoney',
        ];
        quarter = 'fourthQuarter';
        break;
      default:
        months = [
          'januaryMoney',
          'februaryMoney',
          'marchMoney',
          'aprilMoney',
          'mayMoney',
          'juneMoney',
          'julyMoney',
          'augustMoney',
          'septemberMoney',
          'octoberMoney',
          'novemberMoney',
          'decemberMoney',
        ];
        quarter = 'budgetMoney';
        break;
    }
    // 检查并处理可能的空值
    const totalMoney = months.reduce((total, month) => total + (record[month] ?? 0), 0);
    record[quarter] = totalMoney;
    return totalMoney;
  }
}

function calculateAllQuarterMoney(record) {
  const arr = [
    'firstQuarterMoney',
    'secondQuarter',
    'thirdQuarter',
    'fourthQuarter',
  ];
  const totalMoney = arr.reduce((total, month) => total + (record[month] ?? 0), 0);
  record.budgetMoney = totalMoney;
  return totalMoney;
}

</script>

<template>
  <BasicCard
    title="基本信息"
    :grid-content-props="baseInfoProps"
    :isBorder="false"
  />
  <BasicCard

    title="预算编制"
  >
    <!--    这里下面的插槽太多，使用两个table区分一下-->
    <OrionTable
      v-if="detailsData?.status===120"
      ref="tableRef"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <template
          v-for="button in toolButtons"
          :key="button.event"
        >
          <BasicButton

            v-is-power="[button.code]"
            v-bind="getButtonProps(button)"
            @click="toolClick(button)"
          >
            {{ button.text }}
          </BasicButton>
        </template>
      </template>
      <template #name="{record}">
        <AInput
          v-model:value="record.name"
        />
      </template>
      <template #costCenterId="{record}">
        <ASelect
          v-model:value="record.costCenterId"
          style="width: 180px"
          :options="costCenterOptions"
          :fieldNames="{ label: 'name', value: 'id' }"
          class="select-wrap"
        />
      </template>
      <template #expenseSubjectName="{record}">
        <ATreeSelect
          v-model:value="record.expenseSubjectNumber"
          placeholder="请输入科目名称"
          :tree-data="expenseSubjectNumberTreeData"
          style=" width: 230px"
          tree-node-filter-prop="label"
          :field-names="{ label: 'name', value: 'number' }"
          @change="(selectId,selectLabelArr)=>{
            if(selectLabelArr){
              record.expenseSubjectName=selectLabelArr[0]
            }
          }"
        />
      </template>
      <template #expenseSubjectNumber="{record}">
        <AInput
          v-model:value="record.expenseSubjectNumber"
          :disabled="true"
        />
      </template>

      <template #timeType="{ record, index }">
        <ASelect
          v-model:value="record.timeType"
          style="width: 80px"
          :options="timeTypeOptions"
          class="select-wrap"
          :field-names="{ label: 'description', value: 'number' }"
          @change="(value) => changeTimeType(value,record, index)"
        />
      </template>
      <template #budgetTime="{ record }">
        <DatePicker
          v-model:value="record.budgetTime"
          class="ml15"
          style="width: 110px"
          picker="year"
          valueFormat="YYYY"
        />
      </template>
      <template #budgetObjectType="{ record }">
        <ASelect
          v-model:value="record.budgetObjectType"
          :disabled="true"
          :options="budgetObjectTypeOptions"
          class="select-wrap"
          :field-names="{ label: 'description', value: 'number' }"
        />
      </template>
      <template #currency="{ record }">
        <ASelect
          v-model:value="record.currency"
          :disabled="true"
          :options="currencyOptions"
          class="select-wrap"
          :field-names="{ label: 'description', value: 'number' }"
        />
      </template>
      <template #budgetMoney="{ record }">
        <InputMoney
          v-if="record.timeType==='budgetMonth'"
          :value="calculateQuarterMoney(record,0)"
          :disabled="(record.timeType==='budgetMonth' || record.timeType==='budgetQuarter')"
        />
        <InputMoney
          v-else-if="record.timeType==='budgetQuarter'"
          :value="calculateAllQuarterMoney(record)"
          :disabled="(record.timeType==='budgetMonth' || record.timeType==='budgetQuarter')"
        />
        <InputMoney
          v-else
          v-model:value="record.budgetMoney"
          :disabled="(record.timeType==='budgetMonth' || record.timeType==='budgetQuarter')"
        />
      </template>
      <template #januaryMoney="{ record }">
        <InputMoney
          v-model:value="record.januaryMoney"
          :disabled="record.timeType!=='budgetMonth'"
        />
      </template>
      <template #februaryMoney="{ record }">
        <InputMoney
          v-model:value="record.februaryMoney"
          :disabled="record.timeType!=='budgetMonth'"
        />
      </template>
      <template #marchMoney="{ record }">
        <InputMoney
          v-model:value="record.marchMoney"
          :disabled="record.timeType!=='budgetMonth'"
        />
      </template>
      <template #firstQuarterMoney="{ record }">
        <InputMoney
          v-if="record.timeType==='budgetMonth'"
          :value="calculateQuarterMoney(record,1)"
          :disabled="record.timeType !== 'budgetQuarter'"
        />
        <InputMoney
          v-else
          v-model:value="record.firstQuarterMoney"
          :disabled="record.timeType !== 'budgetQuarter'"
        />
      </template>
      <template #aprilMoney="{ record }">
        <InputMoney
          v-model:value="record.aprilMoney"
          :disabled="record.timeType!=='budgetMonth'"
        />
      </template>
      <template #mayMoney="{ record }">
        <InputMoney
          v-model:value="record.mayMoney"
          :disabled="record.timeType!=='budgetMonth'"
        />
      </template>
      <template #juneMoney="{ record }">
        <InputMoney
          v-model:value="record.juneMoney"
          :disabled="record.timeType!=='budgetMonth'"
        />
      </template>
      <template #secondQuarter="{ record }">
        <InputMoney
          v-if="record.timeType==='budgetMonth'"
          :value="calculateQuarterMoney(record,2)"
          :disabled="record.timeType!=='budgetQuarter'"
        />

        <InputMoney
          v-else
          v-model:value="record.secondQuarter"
          :disabled="record.timeType !== 'budgetQuarter'"
        />
      </template>
      <template #julyMoney="{ record }">
        <InputMoney
          v-model:value="record.julyMoney"
          :disabled="record.timeType!=='budgetMonth'"
        />
      </template>
      <template #augustMoney="{ record }">
        <InputMoney
          v-model:value="record.augustMoney"
          :disabled="record.timeType!=='budgetMonth'"
        />
      </template>
      <template #septemberMoney="{ record }">
        <InputMoney
          v-model:value="record.septemberMoney"
          :disabled="record.timeType!=='budgetMonth'"
        />
      </template>
      <template #thirdQuarter="{ record }">
        <InputMoney
          v-if="record.timeType==='budgetMonth'"
          :value="calculateQuarterMoney(record,3)"
          :disabled="record.timeType!=='budgetQuarter'"
        />

        <InputMoney
          v-else
          v-model:value="record.thirdQuarter"
          :disabled="record.timeType !== 'budgetQuarter'"
        />
      </template>
      <template #octoberMoney="{ record }">
        <InputMoney
          v-model:value="record.octoberMoney"
          :disabled="record.timeType!=='budgetMonth'"
        />
      </template>
      <template #novemberMoney="{ record }">
        <InputMoney
          v-model:value="record.novemberMoney"
          :disabled="record.timeType!=='budgetMonth'"
        />
      </template>
      <template #decemberMoney="{ record }">
        <InputMoney
          v-model:value="record.decemberMoney"
          :disabled="record.timeType!=='budgetMonth'"
        />
      </template>
      <template #fourthQuarter="{ record }">
        <InputMoney
          v-if="record.timeType==='budgetMonth'"
          :value="calculateQuarterMoney(record,4)"
          :disabled="record.timeType!=='budgetQuarter'"
        />
        <InputMoney
          v-else
          v-model:value="record.fourthQuarter"
          :disabled="record.timeType!=='budgetQuarter'"
        />
      </template>
    </OrionTable>
    <OrionTable
      v-else
      ref="tableRef"
      :options="tableOptionsShow"
    />
  </BasicCard>
</template>

<style scoped lang="less">

</style>
