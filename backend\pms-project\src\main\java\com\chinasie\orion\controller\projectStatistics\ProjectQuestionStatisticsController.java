package com.chinasie.orion.controller.projectStatistics;

import com.chinasie.orion.domain.dto.projectStatistics.ProjectQuestionStatisticsDTO;
import com.chinasie.orion.domain.vo.QuestionManagementVO;
import com.chinasie.orion.domain.vo.projectStatistics.ProjectQuestionStatisticsVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.projectStatistics.ProjectQuestionStatisticsService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/projectQuestionStatistics")
@Api(tags = "项目内问题统计")
public class ProjectQuestionStatisticsController {
    @Autowired
    private ProjectQuestionStatisticsService projectQuestionStatisticsService;


    @ApiOperation(value = "问题状态分布统计")
    @RequestMapping(value = "/getProjectQuestionStatusStatistics", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【问题状态分布统计】", type = "项目内报表", bizNo = "")
    public ResponseDTO<ProjectQuestionStatisticsVO> getProjectQuestionStatusStatistics(@RequestBody ProjectQuestionStatisticsDTO projectQuestionStatisticsDTO) throws Exception {
        ProjectQuestionStatisticsVO rsp =  projectQuestionStatisticsService.getProjectQuestionStatusStatistics(projectQuestionStatisticsDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "问题状态负责人统计")
    @RequestMapping(value = "/getProjectQuestionRspUserStatistics", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【问题状态负责人统计】", type = "项目内报表", bizNo = "")
    public ResponseDTO<List<ProjectQuestionStatisticsVO>> getProjectQuestionRspUserStatistics(@RequestBody ProjectQuestionStatisticsDTO projectQuestionStatisticsDTO) throws Exception {
        List<ProjectQuestionStatisticsVO> rsp =  projectQuestionStatisticsService.getProjectQuestionRspUserStatistics(projectQuestionStatisticsDTO);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "问题状态趋势统计")
    @RequestMapping(value = "/getProjectQuestionChangeStatusStatistics", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【问题状态趋势统计】", type = "项目内报表", bizNo = "")
    public ResponseDTO< List<ProjectQuestionStatisticsVO>> getProjectQuestionChangeStatusStatistics( @RequestBody ProjectQuestionStatisticsDTO projectQuestionStatisticsDTO) throws Exception {
        List<ProjectQuestionStatisticsVO> rsp =  projectQuestionStatisticsService.getProjectQuestionChangeStatusStatistics(projectQuestionStatisticsDTO);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "问题新增趋势统计")
    @RequestMapping(value = "/getProjectQuestionCreateStatistics", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【问题新增趋势统计】", type = "项目内报表", bizNo = "")
    public ResponseDTO< List<ProjectQuestionStatisticsVO>> getProjectQuestionCreateStatistics( @RequestBody ProjectQuestionStatisticsDTO projectQuestionStatisticsDTO) throws Exception {
        List<ProjectQuestionStatisticsVO> rsp =  projectQuestionStatisticsService.getProjectQuestionCreateStatistics(projectQuestionStatisticsDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "问题分页查询")
    @RequestMapping(value = "/getProjectQuestionPages", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【问题分页】", type = "项目内报表", bizNo = "")
    public ResponseDTO<Page<QuestionManagementVO>> getProjectQuestionPages(@RequestBody Page<ProjectQuestionStatisticsDTO> pageRequest) throws Exception {
        Page<QuestionManagementVO> rsp =  projectQuestionStatisticsService.getProjectQuestionPages(pageRequest);
        return new ResponseDTO<>(rsp);
    }
}
