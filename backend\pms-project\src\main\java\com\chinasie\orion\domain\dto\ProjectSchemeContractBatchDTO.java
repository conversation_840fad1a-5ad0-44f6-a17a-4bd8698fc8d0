package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * @author: yk
 * @date: 2023/5/25 20:28
 * @description: 批量添加项目计划关联合同DTO
 */
@Data
@ApiModel(value = "ProjectSchemeContractBatchDTO对象", description = "批量添加项目计划关联合同参数")
public class ProjectSchemeContractBatchDTO implements Serializable {
    @ApiModelProperty(value = "合同列表")
    @NotEmpty(message = "所选合同不能为空")
    List<ProjectSchemeContractDTO> schemeContractList;
}
