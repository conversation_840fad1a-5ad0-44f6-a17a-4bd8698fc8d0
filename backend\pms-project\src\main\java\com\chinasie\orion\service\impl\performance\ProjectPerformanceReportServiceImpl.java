package com.chinasie.orion.service.impl.performance;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.constant.PlanActiveEnum;
import com.chinasie.orion.constant.ProjectSchemeNodeTypeEnum;
import com.chinasie.orion.domain.dto.ProjectDTO;
import com.chinasie.orion.domain.entity.BudgetExpendForm;
import com.chinasie.orion.domain.entity.BudgetManagement;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.vo.performance.ProjectPerformanceReportVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ProjectRepository;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.BudgetExpendFormService;
import com.chinasie.orion.service.BudgetManagementService;
import com.chinasie.orion.service.ProjectSchemeService;
import com.chinasie.orion.service.performance.ProjectPerformanceReportService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: lsy
 * @date: 2024/5/21
 * @description:
 */
@Service
public class ProjectPerformanceReportServiceImpl implements ProjectPerformanceReportService {

    @Resource
    private ProjectRepository projectRepository;

    @Autowired
    private ProjectSchemeService projectSchemeService;

    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Autowired
    private BudgetManagementService budgetManagementService;

    @Autowired
    private BudgetExpendFormService budgetExpendFormService;

    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    public Page<ProjectPerformanceReportVO> getPage(Page<ProjectDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<Project> condition = new LambdaQueryWrapperX<>(Project.class);
        if (!CollectionUtil.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(Project::getCreateTime);
        List<Date> statisticalPeriodList = null;
        Date statisticalPeriodDate = null;
        if (ObjectUtil.isNotEmpty(pageRequest.getQuery())) {
            String statisticalPeriod = pageRequest.getQuery().getStatisticalPeriod();
            if (StrUtil.isNotBlank(statisticalPeriod)) {
                String[] split = statisticalPeriod.split("-");
                if (split.length == 1) {
                    statisticalPeriodDate = DateUtil.endOfYear(DateUtil.parse(String.format("%s-01-01", split[0])));
                } else {
                    DateTime date = DateUtil.parse(String.format("%s-%s-01", split[0], Integer.parseInt(split[1]) * 3));
                    statisticalPeriodList = CollUtil.newArrayList(DateUtil.beginOfQuarter(date), DateUtil.endOfQuarter(date));

                }
            }
        }
        Page<Project> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), Project::new));

        PageResult<Project> page = projectRepository.selectPage(realPageRequest, condition);

        Page<ProjectPerformanceReportVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectPerformanceReportVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectPerformanceReportVO::new);
        setEveryName(vos, statisticalPeriodList, statisticalPeriodDate);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public void exportByExcel(Page<ProjectDTO> pageRequest, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<Project> condition = new LambdaQueryWrapperX<>(Project.class);
        if (!CollectionUtil.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(Project::getCreateTime);
        List<Date> statisticalPeriodList = null;
        Date statisticalPeriodDate = null;
        if (ObjectUtil.isNotEmpty(pageRequest.getQuery())) {
            String statisticalPeriod = pageRequest.getQuery().getStatisticalPeriod();
            if (StrUtil.isNotBlank(statisticalPeriod)) {
                String[] split = statisticalPeriod.split("-");
                if (split.length == 1) {
                    statisticalPeriodDate = DateUtil.endOfYear(DateUtil.parse(String.format("%s-01-01", split[0])));
                } else {
                    DateTime date = DateUtil.parse(String.format("%s-%s-01", split[0], Integer.parseInt(split[1]) * 3));
                    statisticalPeriodList = CollUtil.newArrayList(DateUtil.beginOfQuarter(date), DateUtil.endOfQuarter(date));

                }
            }
        }
        List<Project> projectList = projectRepository.selectList(condition);

        List<ProjectPerformanceReportVO> vos = BeanCopyUtils.convertListTo(projectList, ProjectPerformanceReportVO::new);
        setEveryName(vos, statisticalPeriodList, statisticalPeriodDate);
        String fileName = "项目绩效报表导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectPerformanceReportVO.class,vos );

    }

    private void setEveryName(List<ProjectPerformanceReportVO> vos, List<Date> statisticalPeriodList, Date statisticalPeriodDate) throws Exception {
        if (CollectionUtil.isNotEmpty(vos)) {
            List<String> projectIdList = vos.stream().map(ProjectPerformanceReportVO::getId).collect(Collectors.toList());
            Map<String, List<ProjectScheme>> projectSchemeMap = projectSchemeService.list(new LambdaQueryWrapperX<>(ProjectScheme.class)
                    .select(ProjectScheme::getId, ProjectScheme::getActualBeginTime, ProjectScheme::getActualEndTime,
                            ProjectScheme::getNodeType, ProjectScheme::getEndTime, ProjectScheme::getProjectId, ProjectScheme::getPlanActive)
                    .in(ProjectScheme::getProjectId, projectIdList)).stream().collect(Collectors.groupingBy(ProjectScheme::getProjectId));
            int sort = 1;
            LambdaQueryWrapperX<BudgetManagement> budgetManagementWrapperX = new LambdaQueryWrapperX<>(BudgetManagement.class);
            budgetManagementWrapperX.select(BudgetManagement::getProjectId, BudgetManagement::getBudgetMoney)
                    .in(BudgetManagement::getProjectId, projectIdList)
                    .isNotNull(BudgetManagement::getBudgetMoney);
            LambdaQueryWrapperX<BudgetExpendForm> budgetExpendFormWrapperX = new LambdaQueryWrapperX<>(BudgetExpendForm.class);
            budgetExpendFormWrapperX.select(BudgetExpendForm::getProjectId, BudgetExpendForm::getExpendMoney)
                    .in(BudgetExpendForm::getProjectId, projectIdList)
                    .isNotNull(BudgetExpendForm::getExpendMoney);
            if (statisticalPeriodList != null) {
                budgetManagementWrapperX.between(BudgetManagement::getBudgetTime, statisticalPeriodList.get(0), statisticalPeriodList.get(1));
                budgetExpendFormWrapperX.between(BudgetExpendForm::getOccurrenceTime, statisticalPeriodList.get(0), statisticalPeriodList.get(1));
            } else if (statisticalPeriodDate != null) {
                budgetManagementWrapperX.le(BudgetManagement::getBudgetTime, statisticalPeriodDate);
                budgetExpendFormWrapperX.le(BudgetExpendForm::getOccurrenceTime, statisticalPeriodDate);
            }
            Map<String, BigDecimal> budgetManagementMap = budgetManagementService.list(budgetManagementWrapperX)
                    .stream().collect(
                            Collectors.groupingBy(BudgetManagement::getProjectId,
                                    Collectors.mapping(BudgetManagement::getBudgetMoney,
                                            Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
            Map<String, BigDecimal> budgetExpendFormMap = budgetExpendFormService.list(budgetExpendFormWrapperX)
                    .stream().collect(
                            Collectors.groupingBy(BudgetExpendForm::getProjectId,
                                    Collectors.mapping(BudgetExpendForm::getExpendMoney,
                                            Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

            for (ProjectPerformanceReportVO vo : vos) {
                vo.setSort(sort++);
                if (StrUtil.isNotBlank(vo.getResDept())) {
                    DeptVO deptById = deptRedisHelper.getDeptById(vo.getResDept());
                    if (deptById != null) {
                        vo.setDeptName(deptById.getName());
                    }
                }

                DataStatusVO dataStatus = vo.getDataStatus();
                if (dataStatus != null) {
                    vo.setStatusName(dataStatus.getName());
                }

                List<ProjectScheme> projectSchemeList = projectSchemeMap.get(vo.getId());
                if (CollectionUtil.isNotEmpty(projectSchemeList)) {
                    //里程碑
                    List<ProjectScheme> milestoneList;
                    if (statisticalPeriodList != null) {
                        milestoneList = projectSchemeList.stream()
                                .filter(f -> ProjectSchemeNodeTypeEnum.MILESTONE.getValue().equals(f.getNodeType())
                                        && f.getActualEndTime() != null
                                        && statisticalPeriodList.get(0).before(f.getActualEndTime())
                                        && statisticalPeriodList.get(1).after(f.getActualEndTime()))
                                .collect(Collectors.toList());
                    } else if (statisticalPeriodDate != null) {
                        milestoneList = projectSchemeList.stream()
                                .filter(f -> ProjectSchemeNodeTypeEnum.MILESTONE.getValue().equals(f.getNodeType())
                                        && f.getActualEndTime() != null
                                        && statisticalPeriodDate.after(f.getActualEndTime()))
                                .collect(Collectors.toList());
                    } else {
                        milestoneList = projectSchemeList.stream()
                                .filter(f -> ProjectSchemeNodeTypeEnum.MILESTONE.getValue().equals(f.getNodeType()))
                                .collect(Collectors.toList());
                    }
                    if (CollectionUtil.isNotEmpty(milestoneList)) {
                        long completeMilestoneCount = milestoneList.stream()
                                .filter(f -> f.getEndTime() != null
                                        && f.getActualEndTime() != null
                                        && f.getEndTime().compareTo(f.getActualEndTime()) >= 0).count();
                        String milestoneCompleteRate = new BigDecimal(completeMilestoneCount)
                                .divide(new BigDecimal(milestoneList.size()), 4, RoundingMode.HALF_UP)
                                .multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP)
                                .stripTrailingZeros().toPlainString();
                        vo.setMilestoneCompleteRate(milestoneCompleteRate + "%");
                    }

                    //项目成本预算管控率
                    BigDecimal budgetMoney = budgetManagementMap.get(vo.getId());
                    BigDecimal expendMoney = budgetExpendFormMap.get(vo.getId());
                    if (ObjectUtil.isNotEmpty(budgetMoney) && budgetMoney.compareTo(BigDecimal.ZERO) != 0 && ObjectUtil.isNotEmpty(expendMoney)) {
                        vo.setCostBudgetRate(expendMoney.divide(budgetMoney, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP)
                                .stripTrailingZeros().toPlainString() + "%");
                    }
                    //测试检查实际完成时间
                    projectSchemeList.stream().filter(f -> f.getActualEndTime() != null && PlanActiveEnum.TEST_ASSESSMENT.getValue().equals(f.getPlanActive()))
                            .map(ProjectScheme::getActualEndTime).max(Comparator.comparing(Date::getTime))
                            .ifPresent(o -> vo.setTestCheckCompleteTime(DateUtil.format(o, DatePattern.NORM_DATE_FORMATTER)));
                    //测试计划实际开始时间
                    projectSchemeList.stream().filter(f -> f.getActualBeginTime() != null && PlanActiveEnum.TEST_PLAN.getValue().equals(f.getPlanActive()))
                            .map(ProjectScheme::getActualBeginTime).min(Comparator.comparing(Date::getTime))
                            .ifPresent(o -> vo.setTestPlanStartTime(DateUtil.format(o, DatePattern.NORM_DATE_FORMATTER)));
                    //产品转状态天数
                    //todo 产品转状态天数:正样（工工程样机）转阶段及质量评审所关联的计划未知
                }
            }
        }
    }

}
