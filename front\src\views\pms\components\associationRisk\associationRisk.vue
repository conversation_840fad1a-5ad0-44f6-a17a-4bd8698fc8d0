<template>
  <div class="plan-tab">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      @selectionChange="selectionChange"
    >
      <template
        v-if="['risk','question'].includes(props.pageType)"
        #toolbarLeft
      >
        <div class="button-margin-right">
          <BasicButton
            v-if="isPower(powerCode?.addCode, powerData)"
            type="primary"
            icon="sie-icon-tianjiaxinzeng"
            @click="addTableNode"
          >
            新增风险
          </BasicButton>
          <BasicButton
            v-if="isPower(powerCode?.quoteCode, powerData)"
            icon="sie-icon-tianjiaxinzeng"
            @click="quoteRisk"
          >
            关联风险
          </BasicButton>
          <BasicButton
            v-if="isPower(powerCode?.removeCode, powerData)"
            icon="sie-icon-shanchu"
            :disabled="selectedRowKeys.length===0"
            @click="deleteBatch"
          >
            移除
          </BasicButton>
        </div>
      </template>
    </OrionTable>
    <AddTableNode
      :isQuestion="true"
      :question-id="formData.id"
      @register="register"
      @update="updateData"
    />
  </div>
</template>
<script lang="ts" setup>
import {
  BasicButton, OrionTable, DataStatusTag, useDrawer, openModal, isPower,
} from 'lyra-component-vue3';
import {
  inject, ref, h, Ref,
} from 'vue';
import Api from '/@/api';
import { useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import AddTableNode from '/@/views/pms/projectLaborer/projectLab/projectList/components/BusinessRisk/components/AddTableNode.vue';
import dayjs from 'dayjs';
import AssociationRiskModal from './components/AssociationRiskModal.vue';

const props = withDefaults(defineProps<{
    pageType:string,
    getRiskTableDataApi:any,
    powerCode?:object
}>(), {
  pageType: 'risk',
  getRiskTableDataApi: null,
  powerCode: () => ({}),
});
const powerData = inject('powerData', {});
const tableRef = ref();
const formData:Record<any, any> = inject('formData', {});
const router = useRouter();
const selectedRowKeys:Ref<string[]> = ref([]);

const [register, { openDrawer }] = useDrawer();
function selectionChange(data) {
  selectedRowKeys.value = data.keys;
}
function addTableNode() {
  openDrawer(true, {
    type: 'add',
    data: {},
  });
}

function quoteRisk() {
  const quoteRiskPoolRef = ref();
  openModal({
    title: '关联风险',
    width: 1100,
    height: 700,
    content(h) {
      return h(AssociationRiskModal, {
        ref: quoteRiskPoolRef,
        showLeftTree: true,
        questionId: formData.value.id,
      });
    },
    async onOk() {
      await quoteRiskPoolRef.value.saveData();
      updateData();
    },
  });
}
function deleteBatch() {
  deleteBatchData(selectedRowKeys.value, 'batch');
}
function deleteBatchData(params, type) {
  Modal.confirm({
    title: '移除提示',
    content: type === 'batch' ? '是否移除选中的数据？' : '是否移除该条数据？',
    onOk() {
      new Api('/pas').fetch(params, `questionRelationRisk/relationRisk/${formData.value.id}`, 'DELETE')
        .then((res) => {
          message.success('移除成功');
          updateData();
        });
    },
  });
}
function updateData() {
  tableRef.value.reload();
}
const tableOptions = {
  showIndexColumn: false,
  pagination: false,
  bordered: false,
  deleteToolButton: 'add|delete|enable|disable',
  rowSelection: {},
  api(params) {
    if (!formData.value.id) {
      return Promise.resolve([]);
    }
    return props.getRiskTableDataApi(params);
  },
  columns: initColumns(),
  actions: [
    {
      text: '编辑',
      isShow: (record) => record.isCurrentCreate && isPower(props.powerCode?.editCode, powerData),
      onClick(record) {
        openDrawer(true, {
          type: 'edit',
          data: { id: record.id },
        });
      },
    },

    {
      text: '删除',
      isShow: (record) => record.isCurrentCreate && isPower(props.powerCode?.deleteCode, powerData),
      onClick(record) {
        Modal.confirm({
          title: '删除提示',
          content: '是否删除该条数据？',
          onOk() {
            new Api('/pas').fetch({
              riskId: record.id,
            }, `questionRelationRisk/relationRisk/deleteRisk/${formData.value.id}`, 'DELETE').then(() => {
              message.success('删除成功');
              tableRef.value.reload();
            });
          },
        });
      },
    },
    {
      text: '移除',
      isShow: (record) => !record.isCurrentCreate && isPower(props.powerCode?.removeCode, powerData),
      onClick(record) {
        deleteBatchData([record.id], 'one');
      },
    },
  ],
};
function initColumns() {
  let columns:Record<any, any>[] = [
    {
      title: '编号',
      dataIndex: 'number',
      width: '120px',
    },
    {
      title: '名称',
      dataIndex: 'name',
      minWidth: 220,
      customRender({ record, text }) {
        return isPower(props.powerCode?.checkCode, powerData) ? h(
          'span',
          {
            class: 'action-btn',
            title: text,
            onClick(e) {
              router.push({
                name: 'PMSRiskManagementDetails',
                params: {
                  id: record.id,
                },
              });
            },
          },
          text,
        ) : text;
      },
    },
    {
      title: '风险描述',
      dataIndex: 'remark',
      width: '120px',
    },
    {
      title: '状态',
      dataIndex: 'dataStatus',
      width: '120px',
      customRender: ({ record, text }) => h(DataStatusTag, { statusData: record.dataStatus }),
    },

    {
      title: '风险类型',
      dataIndex: 'riskTypeName',
      width: '80px',
    },
    {
      title: '发生概率',
      dataIndex: 'riskProbabilityName',
      width: '100px',
    },
    {
      title: '影响程度',
      dataIndex: 'riskInfluenceName',
      width: '120px',
    },
    {
      title: '预估发生时间',
      dataIndex: 'predictStartTimeName',
      width: '120px',
    },
    {
      title: '负责人',
      dataIndex: 'principalName',
      width: '120px',
    },

    {
      title: '期望完成时间',
      dataIndex: 'predictEndTime',
      width: '170px',
    },

    {
      title: '应对策略',
      dataIndex: 'copingStrategyName',
      width: '120px',
    },
    {
      title: '应对措施',
      dataIndex: 'solutions',
      width: '120px',
    },
  ];
  if (['risk', 'question'].includes(props.pageType)) {
    columns.push({
      title: '操作',
      dataIndex: 'action',
      width: 150,
      fixed: 'right',
      slots: { customRender: 'action' },
    });
  }
  return columns;
}
</script>