<template>
  <div>
    <WorkflowView
      v-if="deliverDetailsInfo?.id"
      ref="processViewRef"
      :workflow-props="workflowProps"
    />
  </div>
</template>

<script>
import { WorkflowView } from 'lyra-workflow-component-vue3';
import { computed, ref } from 'vue';
import Api from '/@/api';
export default {
  components: { WorkflowView },
  props: {
    deliverDetailsInfo: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  setup(props) {
    const processViewRef = ref();
    const workflowProps = computed(() => ({
      Api,
      businessData: props.deliverDetailsInfo,
      afterEvent: (type, props) => {
        processViewRef.value?.init();
      },
    }));

    return {
      processViewRef,
      workflowProps,
    };
  },
};
</script>

<style scoped></style>
