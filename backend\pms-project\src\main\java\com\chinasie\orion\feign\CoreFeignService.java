//package com.chinasie.orion.feign;
//
//import com.chinasie.orion.dto.ResponseDTO;
//import com.chinasie.orion.sdk.core.conf.FeignConfig;
//import com.chinasie.orion.sdk.domain.vo.business.SysCodeSegmentVO;
//import org.springframework.cloud.openfeign.FeignClient;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * @description 数据类型Feign服务接口
// * @since 2021/6/15 21:06
// */
////@FeignClient(name = "core", url = "http://**************:7800", path = "/", configuration = {FeignConfig.class})//测试
//@FeignClient(name = "core", path = "", configuration = FeignConfig.class)
//public interface CoreFeignService {
//
//    /**
//     * /sys-mapping-relation/rules-and-segment
//     * @param dataType
//     * @param dataField
//     * @return
//     * @throws Exception
//     */
//    @GetMapping("/sys-mapping-relation/rules-and-segment")
//    ResponseDTO<List<SysCodeSegmentVO>> rulesAndSegment(
//            @RequestParam(value = "dataType", required = false) String dataType,
//            @RequestParam(value = "dataField", required = false) String dataField
//    ) throws Exception;
//
//
//    /**
//     * /sys-code-segment/code
//     * @return
//     * @throws Exception
//     */
//    @PostMapping("/sys-code-segment/code")
//    ResponseDTO sysCodeSegmentCode(@RequestBody List<SysCodeSegmentVO> code
//    ) throws Exception;
//
//    /**
//     * /sys-mapping-relation/rules-and-segment
//     * @param dataType
//     * @param dataField
//     * @return
//     * @throws Exception
//     */
//    @GetMapping("/sys-mapping-relation/rules-and-segment-create")
//    ResponseDTO<String> rulesAndSegmentCreate(
//            @RequestParam(value = "dataType", required = false) String dataType,
//            @RequestParam(value = "dataField", required = false) String dataField,
//            @RequestParam(value = "isCustom", required = false) Boolean isCustom,
//            @RequestParam(value = "rule", required = false) String rule
//    ) throws Exception;
//
//    /**
//     * 最新版本获取编码
//     * @param dataType 类名
//     * @param dataField 字段类型
//     * @param isCustom 是否自定义
//     * @param rule 自定义规则，按照逗号分割
//     * @return 编码字符串
//     * @throws Exception e
//     */
//    @RequestMapping(value = "/sys-mapping-relation/rules-and-segment-create", method = RequestMethod.GET)
//    ResponseDTO getCreateCodeSegment(@RequestParam("dataType") String dataType,
//                                            @RequestParam("dataField") String dataField,
//                                            @RequestParam("isCustom")Boolean isCustom,
//                                            @RequestParam(value = "rule", required = false)String rule) throws Exception;
//}
