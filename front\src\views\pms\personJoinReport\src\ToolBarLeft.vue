<template>
  <BasicButton
    v-if="showBtn"
    icon="orion-icon-upload"
    @click="changeBtn"
  >
    导出全部
  </BasicButton>
  <div
    v-if="showSelect"
    class="select-box"
  >
    所属项目: <Select
      v-model:value="state.selectValue"
      :placeholder="state.selectValue??'请选择'"
      :options="state.options"
      :allowClear="true"
      class="width-120"
    />
  </div>
</template>

<script setup lang="ts">
import {
  reactive, defineEmits, defineProps, watch, onMounted,
} from 'vue';
import { BasicButton } from 'lyra-component-vue3';
import { Select } from 'ant-design-vue';
import Api from '/@/api';

const emits = defineEmits(['buttonChange', 'change']);
const props = defineProps({
  showBtn: {
    type: Boolean,
    default: true,
  },
  showSelect: {
    type: Boolean,
    default: true,
  },
});

const state = reactive({
  selectValue: undefined,
  options: [
    {
      label: '全部',
      value: 123,
    },
  ],
});
watch(() => state.selectValue, (v) => {
  emits('change', v);
});

function changeBtn() {
  emits('buttonChange', state.selectValue);
}

onMounted(() => {
  new Api('/pms/project/getList').fetch('', '', 'GET').then((res) => {
    if (res?.length > 0) {
      state.options = res.map((item) => ({
        label: item.name,
        value: item.id,
      }));
    } else {
      state.options = [];
    }
  });
});
</script>

<style scoped lang="less">
.width-120 {
  width: 120px;
  margin-left: 10px;
}

.select-box {
  displasy: flex;
  justify-content: center;
  align-items: center;
}
</style>
