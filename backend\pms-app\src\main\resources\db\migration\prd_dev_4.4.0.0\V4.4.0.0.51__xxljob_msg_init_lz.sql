INSERT INTO `msc_business_node`(`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1844629185675055104', '人员证书到期提醒', 'NODE_PERSON_VERIFICATION', 'vub01844627262574088192', NULL, 0, 'SYS,EMAIL', '1', '1', NULL, 'u0rn1770010828494475264', 1, NULL, '314j1000000000000000000', '2024-10-11 14:40:44', '314j1000000000000000000', '2024-10-11 14:42:21', NULL, NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node`(`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1844629553695870976', '人员岗位授权到期提醒', 'NODE_POSITION_AUTHORIZE', 'vub01844627262574088192', NULL, 0, 'SYS,EMAIL', '1', '1', NULL, 'u0rn1770010828494475264', 1, NULL, '314j1000000000000000000', '2024-10-11 14:42:12', '314j1000000000000000000', '2024-10-11 14:42:33', NULL, NULL, NULL, 1, b'1', b'1');

INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01844626861548294144', '人员证书有效期提醒', 'PMS_PERSON_VERIFICATION', '您于【$date$】获取的【$level$】【$name$】有效日期已不足1年，请及时安排续期。', '1', 1, '', '314j1000000000000000000', '2024-10-11 14:31:30', '314j1000000000000000000', '2024-10-11 14:31:38', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01844627262574088192', '人员证书有效期提醒标题', 'PMS_PERSON_VERIFICATION_TITLE', '人员证书有效期提醒', '1', 1, '', '314j1000000000000000000', '2024-10-11 14:33:06', '314j1000000000000000000', '2024-10-11 14:33:12', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01844627871406673920', '人员岗位授权到期提醒', 'PMS_POSITION_AUTHORIZE', '您于【$beginDate$】获取的【$baseName$】【$position$】授权有效日期已不足2个月，请及时安排续期。', '1', 1, '', '314j1000000000000000000', '2024-10-11 14:35:31', '314j1000000000000000000', '2024-10-11 14:35:38', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01844628179121786880', '人员岗位授权到期提醒标题', 'PMS_POSITION_AUTHORIZE_TITLE', '人员岗位授权临期提醒', '1', 1, '', '314j1000000000000000000', '2024-10-11 14:36:44', '314j1000000000000000000', '2024-10-11 14:36:50', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');

INSERT INTO `xxl_job_info`(`id`, `job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (128, 5, '人员证书信息有效期提醒', '2024-10-11 14:00:41', '2024-10-11 14:00:41', 'orion', '', 'CRON', '0 40 8 * * ?', 'DO_NOTHING', 'FIRST', 'personVerificationValidJob', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-10-11 14:00:41', '', 0, 0, 0);
INSERT INTO `xxl_job_info`(`id`, `job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (129, 5, '人员岗位授权到期提醒', '2024-10-11 14:17:16', '2024-10-11 14:17:16', 'orion', '', 'CRON', '0 50 8 * * ?', 'DO_NOTHING', 'FIRST', 'personPositionAuthorizeJob', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-10-11 14:17:16', '', 0, 0, 0);
