<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements. See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership. The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License. You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied. See the License for the
  specific language governing permissions and limitations
  under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.codehaus.mojo</groupId>
    <artifactId>mojo-parent</artifactId>
    <version>70</version>
  </parent>

  <artifactId>flatten-maven-plugin</artifactId>
  <version>1.3.0</version>
  <packaging>maven-plugin</packaging>

  <name>Maven Flatten Plugin</name>

  <description>
    Plugin to generate flattened POM (reduced and resolved information required for consumers of maven repositories)
    and to use (install, sign, deploy) it instead of original pom.xml.
  </description>

  <url>https://www.mojohaus.org/flatten-maven-plugin/</url>

  <inceptionYear>2014</inceptionYear>

  <licenses>
    <license>
      <name>Apache Software Licenese</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <comments />
    </license>
  </licenses>

  <developers>
    <developer>
      <id>hohwille</id>
      <name>Jörg Hohwiller</name>
      <email><EMAIL></email>
      <roles>
        <role>maintainer</role>
        <role>developer</role>
      </roles>
    </developer>
    <developer>
      <id>rfscholte</id>
      <name>Robert Scholte</name>
      <email><EMAIL></email>
      <timezone>Europe/Amsterdam</timezone>
      <roles>
        <role>developer</role>
      </roles>
    </developer>
    <developer>
      <id>khmarbaise</id>
      <name>Karl Heinz Marbaise</name>
      <roles>
        <role>developer</role>
      </roles>
    </developer>
    <developer>
      <id>sjaranowski</id>
      <name>Slawomir Jaranowski</name>
      <email><EMAIL></email>
      <timezone>Europe/Warsaw</timezone>
      <roles>
        <role>developer</role>
      </roles>
    </developer>
  </developers>

  <contributors>
    <contributor>
      <name>Christoph Böhme</name>
    </contributor>
    <contributor>
      <name>Michel Guillet</name>
    </contributor>
    <contributor>
      <name>Mariusz Smykuła</name>
    </contributor>
    <contributor>
      <name>Stephan Leicht Vogt</name>
    </contributor>
    <contributor>
      <name>Erwin Tratar</name>
    </contributor>
    <contributor>
      <name>Guillaume Delafosse</name>
    </contributor>
    <contributor>
      <name>Flabo</name>
    </contributor>
  </contributors>

  <prerequisites>
    <maven>${mavenVersion}</maven>
  </prerequisites>

  <scm>
    <connection>scm:git:https://github.com/mojohaus/flatten-maven-plugin.git</connection>
    <developerConnection>scm:git:ssh://**************/mojohaus/flatten-maven-plugin.git</developerConnection>
    <url>https://github.com/mojohaus/flatten-maven-plugin/tree/master</url>
    <tag>flatten-maven-plugin-1.3.0</tag>
  </scm>

  <properties>
    <mavenVersion>3.2.5</mavenVersion>
    <project.build.outputTimestamp>2022-08-16T21:48:58Z</project.build.outputTimestamp>
  </properties>

  <!-- override versions of some transitive dependencies -->
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>commons-io</groupId>
        <artifactId>commons-io</artifactId>
        <version>2.11.0</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <dependencies>
    <!-- plugin tools -->
    <dependency>
      <groupId>org.apache.maven.plugin-tools</groupId>
      <artifactId>maven-plugin-annotations</artifactId>
      <scope>provided</scope>
    </dependency>

    <!-- Maven Core -->
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-artifact</artifactId>
      <version>${mavenVersion}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-core</artifactId>
      <version>${mavenVersion}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-model</artifactId>
      <version>${mavenVersion}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-plugin-api</artifactId>
      <version>${mavenVersion}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-model-builder</artifactId>
      <version>${mavenVersion}</version>
      <scope>provided</scope>
    </dependency>

    <!-- other -->
    <dependency>
      <groupId>org.apache.maven.shared</groupId>
      <artifactId>maven-dependency-tree</artifactId>
      <version>3.1.1</version>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-utils</artifactId>
      <version>3.4.2</version>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.shared</groupId>
      <artifactId>maven-artifact-transfer</artifactId>
      <version>0.13.1</version>
    </dependency>

    <!-- testing -->
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <version>3.23.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.plugin-testing</groupId>
      <artifactId>maven-plugin-testing-harness</artifactId>
      <version>3.3.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-compat</artifactId>
      <version>${mavenVersion}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
      </plugin>
    </plugins>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <configuration>
          <links combine.self="override">
            <link>https://junit.org/junit4/javadoc/latest</link>
            <link>https://maven.apache.org/ref/${mavenVersion}/apidocs/</link>
          </links>
        </configuration>
      </plugin>
    </plugins>
  </reporting>

  <profiles>
    <profile>
      <id>run-its</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-invoker-plugin</artifactId>
            <executions>
              <execution>
                <goals>
                  <goal>install</goal>
                  <goal>integration-test</goal>
                  <goal>verify</goal>
                </goals>
              </execution>
            </executions>
            <configuration>
              <debug>true</debug>
              <cloneProjectsTo>${project.build.directory}/it</cloneProjectsTo>
              <localRepositoryPath>${project.build.directory}/local-repo</localRepositoryPath>
              <settingsFile>src/it/mrm/settings.xml</settingsFile>
              <filterProperties>
                <repository.proxy.url>${repository.proxy.url}</repository.proxy.url>
              </filterProperties>
              <properties>
                <settingsFile>${project.build.directory}/it/interpolated-settings.xml</settingsFile>
              </properties>
              <goals>
                <goal>${project.groupId}:${project.artifactId}:${project.version}:flatten -Drevision=*******</goal>
              </goals>
              <projectsDirectory>src/it/projects</projectsDirectory>
              <postBuildHookScript>verify</postBuildHookScript>
              <preBuildHookScript>setup</preBuildHookScript>
            </configuration>
          </plugin>

          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>mrm-maven-plugin</artifactId>
            <executions>
              <execution>
                <goals>
                  <goal>start</goal>
                  <goal>stop</goal>
                </goals>
              </execution>
            </executions>
            <configuration>
              <propertyName>repository.proxy.url</propertyName>
              <repositories>
                <mockRepo>
                  <source>src/it/mrm/repository</source>
                </mockRepo>
                <localRepo>
                  <source>${project.build.directory}/local-repo</source>
                </localRepo>
                <proxyRepo />
              </repositories>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

</project>
