package com.chinasie.orion.domain.vo.resourceAllocation;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class RepairPlanVO {

    @ApiModelProperty(value = "大修轮次")
    private String repairRound;

    @ApiModelProperty(value = "大修名称")
    private String repairName;

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "展示开始时间")
    private Date beginTime;

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "展示结束时间")
    private Date endTime;

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "计划开始时间")
    private Date planBeginTime;

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "计划结束时间")
    private Date planEndTime;

    @ApiModelProperty(value = "基地code")
    private String baseCode;

    @ApiModelProperty(value = "基地名称")
    private String baseName;

    @ApiModelProperty(value = "重叠天数")
    private Integer overNumbers;

    @ApiModelProperty(value = "人员/物资信息")
    private List<ResourceAllocationSpecialtyVO> resourceAllocationSpecialtyList;

}
