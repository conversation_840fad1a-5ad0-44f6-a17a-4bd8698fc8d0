package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.tree.MajorRepairOrgEditDTO;
import com.chinasie.orion.domain.dto.tree.MajorTreeJobParamDTO;
import com.chinasie.orion.domain.dto.JobProgressDTO;
import com.chinasie.orion.domain.dto.tree.MajorTreeParamDTO;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.domain.vo.tree.MajorTreeVO;
import com.chinasie.orion.service.ImportantProjectService;
import com.chinasie.orion.tree.TreeNode;
import com.chinasie.orion.xxljob.JobManageProcessXxlJob;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;




import com.chinasie.orion.domain.entity.MajorRepairOrg;
import com.chinasie.orion.domain.dto.MajorRepairOrgDTO;

import com.chinasie.orion.service.MajorRepairOrgService;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * MajorRepairOrg 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18 09:32:26
 */
@RestController
@RequestMapping("/majorRepairOrg")
@Api(tags = "大修组织")
public class  MajorRepairOrgController  {

    @Autowired
    private MajorRepairOrgService majorRepairOrgService;

    @Autowired
    private ImportantProjectService importantProjectService;

    @Autowired
    private JobManageProcessXxlJob jobManageProcessXxlJob;
    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看大修【{{#repairRound}}】组织【{{#orgName}}】详情", type = "MajorRepairOrg", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<MajorRepairOrgVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        MajorRepairOrgVO rsp = majorRepairOrgService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param majorRepairOrgDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】在大修【{{#repairRound}}】下新增组织【{{#majorRepairOrgDTO.name}}】数据", type = "MajorRepairOrg", subType = "新增", bizNo = "")
    public ResponseDTO<String> create(@RequestBody MajorRepairOrgDTO majorRepairOrgDTO) throws Exception {
        String rsp =  majorRepairOrgService.create(majorRepairOrgDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑-名称/责任人
     * @param majorRepairOrgDTO 编辑对象
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑-名称/责任人")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】在大修【{{#repairRound}}】下编辑组织【{{#majorRepairOrgDTO.name}}】数据", type = "MajorRepairOrg", subType = "编辑", bizNo = "{{#majorRepairOrgDTO.id}}")
    public ResponseDTO<Boolean> edit(@Validated @RequestBody MajorRepairOrgEditDTO majorRepairOrgDTO) throws Exception {
        Boolean rsp = majorRepairOrgService.editById(majorRepairOrgDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除大修组织【{{#orgNames}}】 id【{{#orgIds}}】数据", type = "MajorRepairOrg", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = majorRepairOrgService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除大修组织【{{#orgNames}}】 id【{{#orgIds}}】数据", type = "MajorRepairOrg", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = majorRepairOrgService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @LogRecord(success = "【{USER{#logUserId}}】查询大修组织数据", type = "MajorRepairOrg", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<MajorRepairOrgVO>> pages(@RequestBody Page<MajorRepairOrgDTO> pageRequest) throws Exception {
        Page<MajorRepairOrgVO> rsp =  majorRepairOrgService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 分页
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取专业树")
    @LogRecord(success = "【{USER{#logUserId}}】查询获取大修轮次【{{#majorTreeParamDTO.repairRound}}】专业树", type = "MajorRepairOrg", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/major/tree", method = RequestMethod.POST)
    public ResponseDTO<ObjectTreeInfoVO<TreeNodeVO<NodeVO<MajorTreeVO>>>> majorTree(@RequestBody MajorTreeParamDTO majorTreeParamDTO) throws Exception {
        return new ResponseDTO<>(majorRepairOrgService.majorTree(majorTreeParamDTO));
    }


    @ApiOperation(value = "添加大修组织进展")
    @PostMapping(value = "/add/progress")
    @LogRecord(success = "【{USER{#logUserId}}】添加大修【{{#repairRound}}】组织【{{#orgName}}】进展", type = "JobProgress", subType = "新增", bizNo = "")
    public ResponseDTO<Boolean> addOrgProgress(@RequestBody JobProgressDTO jobProgressDTO){
        majorRepairOrgService.addOrgProgress(jobProgressDTO);
        return new ResponseDTO<>(Boolean.TRUE);
    }

    @ApiOperation(value = "修改大修组织进展")
    @PutMapping(value = "/edit/progress")
    @LogRecord(success = "【{USER{#logUserId}}】编辑大修【{{#repairRound}}】组织【{{#orgName}}】进展 id【{{#id}}】", type = "JobProgress", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> editOrgProgress(@RequestBody JobProgressDTO jobProgressDTO){
        majorRepairOrgService.editOrgProgress(jobProgressDTO);
        return new ResponseDTO<>(Boolean.TRUE);
    }

    @ApiOperation("删除大修组织进展")
    @Transactional(rollbackFor = Exception.class)
    @DeleteMapping("/delete/progress")
    @LogRecord(success = "【{USER{#logUserId}}】删除大修【{{#repairRound}}】组织【{{#orgName}}】下进行 ids【{{#ids}}】", type = "JobProgress", subType = "新增", bizNo = "{{#ids}}")
    public ResponseDTO<Boolean> deleteProgress(@RequestBody List<String> ids){
        return new ResponseDTO<>(majorRepairOrgService.deleteProgress(ids));
    }

    @ApiOperation("获取大修组织相关进度")
    @PostMapping("/getRepairOrgProgress")
    @LogRecord(success = "【{USER{#logUserId}}】获取大修【{{#repairRound}}】组织【{{#orgName}}】组织相关进度分页", type = "JobProgress", subType = "分页查询", bizNo = "")
    public ResponseDTO<Page<JobProgressVO>> getJobProgressByProjectId(@RequestBody Page<JobProgressDTO> pageRequest) throws Exception {
        Page<JobProgressVO> rsp = importantProjectService.getJobProgressByProjectId(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 分页
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取当前业务数据对应的专业树")
    @LogRecord(success = "【{USER{#logUserId}}】获取大修系【{{#repairRound}}】工单对应的专业树", type = "MajorRepairOrg", subType = "树查询", bizNo = "")
    @RequestMapping(value = "/job/major/tree", method = RequestMethod.POST)
    public ResponseDTO<ObjectTreeInfoVO<TreeNodeVO<NodeVO<MajorTreeVO>>>> jobMajorTree(@RequestBody MajorTreeJobParamDTO majorTreeParamDTO) throws Exception {
        return new ResponseDTO<>(majorRepairOrgService.majorTreeByMajorRepairOrgIds(majorTreeParamDTO));
    }


    /**
     * ID列表
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取当前业务数据对应的ID列表")
    @LogRecord(success = "【{USER{#logUserId}}】获取大修系【{{#repairRound}}】工单对应的专业树id列表", type = "MajorRepairOrg", subType = "id列表查询", bizNo = "")
    @RequestMapping(value = "/job/major/list", method = RequestMethod.POST)
    public ResponseDTO<List<String>> jobMajorList(@RequestBody MajorTreeJobParamDTO majorTreeParamDTO) throws Exception {
        return new ResponseDTO<>(majorRepairOrgService.jobMajorList(majorTreeParamDTO));
    }

    /**
     *  状态变更
     * @throws Exception
     */

    @ApiOperation(value = "状态变更")
    @LogRecord(success = "【{USER{#logUserId}}】获取状态变更列表", type = "MajorRepairOrg", subType = "id列表查询", bizNo = "")
    @RequestMapping(value = "/matchUpJobManage", method = RequestMethod.POST)
    public void matchUpJobManage() throws Exception {
        jobManageProcessXxlJob.matchUpJobManage();
    }
}
