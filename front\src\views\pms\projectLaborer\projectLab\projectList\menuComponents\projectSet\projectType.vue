<template>
  <div>
    <div>
      <OrionTable
        ref="tableRef"
        :options="tableOptions"
        @selection-change="selectionChange"
      >
        <template #toolbarLeft>
          <BasicButton
            v-if=" isPower('XMSZ_container_button_07', powerData) "
            class="mr10"
            icon="orion-icon-info-circle"
            @click="checkData"
          >
            <span class="labelSpan">属性</span>
          </BasicButton>
          <BasicButton
            v-if=" isPower('XMSZ_container_button_08', powerData) "
            class="mr10"
            icon="add"
            @click="addNode"
          >
            <span class="labelSpan">添加</span>
          </BasicButton>
          <BasicButton
            v-if=" isPower('XMSZ_container_button_09', powerData) "
            class="mr10"
            icon="sie-icon-edit"
            @click="editNode"
          >
            <span class="labelSpan">编辑</span>
          </BasicButton>

          <BasicButton
            v-if=" isPower('XMSZ_container_button_10', powerData) "
            class="mr10"
            icon="sie-icon-jinyong"
            @click="banState"
          >
            <span class="labelSpan">禁用</span>
          </BasicButton>
          <BasicButton
            v-if=" isPower('XMSZ_container_button_11', powerData) "
            class="mr10"
            icon="sie-icon-qiyong"
            @click="useState"
          >
            <span class="labelSpan">启用</span>
          </BasicButton>
          <BasicButton
            v-if=" isPower('XMSZ_container_button_12', powerData) "
            class="mr10"
            icon="delete"
            @click="multiDelete"
          >
            <span class="labelSpan">删除</span>
          </BasicButton>
        </template>
      </OrionTable>
    </div>

    <!-- 查看详情弹窗 -->
    <checkDetails
      v-if="pageType==='page'"
      :data="nodeData"
      @close="closeCheck"
    />
    <!-- 简易弹窗提醒 -->
    <messageModal
      :title="'确认提示'"
      :show-visible="showVisible"
      @cancel="showVisible = false"
      @confirm="confirm"
    >
      <div class="messageVal">
        <InfoCircleOutlined />
        <span>{{ message }}</span>
      </div>
    </messageModal>
    <!-- 高级搜索抽屉 -->
    <AddTypeNode
      v-if="pageType==='page'"
      @register="register"
      @update="upDateData"
    />
  </div>
</template>
<script lang="ts">
import {
  defineComponent, ref, reactive, toRefs, computed, onMounted, inject, h,
} from 'vue';
import {
  isPower, useDrawer, BasicButton, OrionTable,
} from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import checkDetails from './TypeModal/checkmodal.vue';
import searchModal from './TypeModal/searchModal.vue';
import messageModal from '/@/views/pms/projectLaborer/components/MessageModal/messageModal.vue';

import {
  typePageApi,
  deletTypeApi,
  effectTypeApi,
} from '/@/views/pms/projectLaborer/api/projectList';
import AddTypeNode from './components/AddTypeNode.vue';

export default defineComponent({
  name: 'ProjectType',
  components: {
    BasicButton,
    /* 表格 */
    //   提示图标
    InfoCircleOutlined,
    //   addNodeModal,
    messageModal,
    checkDetails,
    /* 高级搜索 */
    AddTypeNode,
    OrionTable,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
    pageType: {
      type: String,
      default: 'page',
    },
  },
  setup(props) {
    const [register, { openDrawer }] = useDrawer();
    const tableRef = ref(null);
    function getListParams(params) {
      if (params.searchConditions) {
        let queryCondition = params.searchConditions.map((item) => ({
          column: item?.[0]?.field,
          type: 'like',
          link: 'or',
          value: item?.[0]?.values?.[0],
        }));
        queryCondition.push({
          column: 'projectId',
          type: 'eq',
          link: 'and',
          value: props.id,
        });
        return {
          ...params,
          queryCondition,
        };
      }
      return params;
    }
    const tableOptions = {
      deleteToolButton: 'add|enable|disable|delete',
      rowSelection: {},
      columns: [
        {
          title: '编号',
          dataIndex: 'number',
          key: 'number',
          width: '290px',
          align: 'left',
          slots: { customRender: 'number' },
        },
        {
          title: '名称',
          dataIndex: 'name',
          key: 'name',
          customRender({ record, text }) {
            return h(
              'span',
              {
                class: computed(() => (isPower('XMX_container_button_108', state.powerData) ? 'action-btn' : '')).value,
                title: text,
                onClick(e) {
                  if (isPower('XMX_container_button_108', state.powerData)) {
                    checkData2(record);
                  }
                  e.stopPropagation();
                },
              },
              text,
            );
          },

          align: 'left',
          width: '280px',
        },
        {
          title: '状态',
          dataIndex: 'takeEffectName',
          key: 'takeEffectName',
          width: '100px',
          margin: '0 20px 0 0',
          align: 'left',
          slots: { customRender: 'takeEffectName' },

        },
        {
          title: '修改人',
          dataIndex: 'modifyName',
          key: 'modifyName',
          width: '100px',
          align: 'left',
          slots: { customRender: 'modifyName' },

        },
        {
          title: '修改时间',
          dataIndex: 'modifyTime',
          key: 'modifyTime',
          width: '300px',
          align: 'left',
          slots: { customRender: 'modifyTime' },
        },
      ],
      api(params) {
        return typePageApi(getListParams({
          ...params,
          query: {
            projectId: props.id,
          },
          orders: [
            {
              asc: false,
              column: '',
            },
          ],
          queryCondition: [],
        }));
      },

    };

    const state = reactive({
      contentHeight: 600,
      /* 多选 */
      selectedRowKeys: [],
      /* 选择行id */
      selectedRows: [],
      showVisible: false,
      /* 简易弹窗提醒消息 */
      message: '',
      nodeData: {},
      /* 高度 */
      tableHeight: 400,
      powerData: [],
    });
    state.powerData = inject('powerData');

    const banState = async () => {
      if (multiLengthCheckHandle()) return;
      takeEffect(0);
    };
      /* 启用 */
    const useState = async () => {
      if (multiLengthCheckHandle()) return;
      takeEffect(1);
    };
      /* 启用禁用handle */
    const takeEffect = async (ban) => {
      const banparams = {
        idList: state.selectedRowKeys,
        takeEffect: ban,
      };
      const love = {
        className: 'TaskSubject',
        moduleName: '项目管理-项目设置-任务科目',
        type: 'UPDATE',
        remark: `${ban === 1 ? '启用' : '禁用'}了【${state.selectedRowKeys}】`,
      };
      await effectTypeApi(banparams, love);
      await getFormData();
      state.selectedRows = [];
      state.selectedRowKeys = [];
    };
      /* 编辑 */
    const editNode = () => {
      if (lengthCheckHandle()) return;
      // 编辑只有一条
      let row = state.selectedRows[0];
      openDrawer(true, {
        type: 'edit',
        ...row,
      });
    };
      /* 删除 */
      /* 简易弹窗的确定cb */
    const confirm = () => {
      // 删除操作
      deletrow();
      state.selectedRowKeys = [];
      state.selectedRows = [];
    };
    onMounted(() => {
      /* 高度变化 */
      state.tableHeight = document.body.clientHeight - 460;
      getFormData();
    });
    /* 删除操作 */
    const deletrow = () => {
      deletTypeApi(state.selectedRowKeys)
        .then((res) => {
          message.success('删除成功');
          state.showVisible = false;
          getFormData();
        })
        .catch(() => {
          state.showVisible = false;
        });
    };
    const getFormData = async () => {
      tableRef.value?.reload();
    };
      /* 查看详情 */
    const checkData = () => {
      if (lengthCheckHandle()) return;
      state.nodeData = state.selectedRows[0];
    };
      /* 查看详情 */
    const checkData2 = (data) => {
      state.nodeData = {
        ...[JSON.parse(JSON.stringify(data))],
      };
    };
      /* 检查选择条数fn */
    const lengthCheckHandle = () => {
      if (state.selectedRows.length > 1) {
        message.warning('请选择一条数据进行操作');
        return true;
      }
      if (state.selectedRows.length === 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };
      /* 批量检查选择条数fn */
    const multiLengthCheckHandle = () => {
      if (state.selectedRows.length === 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };

    /* 新建项目 */
    const addNode = () => {
      openDrawer(true, {
        type: 'add',
        projectId: props.id,
      });
    };
      /* 批量删除 */
    const multiDelete = () => {
      if (multiLengthCheckHandle()) return;
      state.message = '请确认是否对当前选中数据进行删除？';
      state.showVisible = true;
    };
      /* 搜索右上 */
      /* 新建项目成功回调 */
    const successSave = () => {
      getFormData();
      state.selectedRowKeys = [];
      state.selectedRows = [];
    };
      /* 查看关闭 */
    const closeCheck = () => {
      state.selectedRowKeys = [];
      state.selectedRows = [];
    };
    function upDateData() {
      getFormData();
    }

    const selectionChange = ({ keys, rows }) => {
      state.selectedRowKeys = keys;
      state.selectedRows = rows;
    };
    return {
      ...toRefs(state),
      isPower,
      /* 简易弹窗cb */
      confirm,
      /* 新增按钮 */
      addNode,
      dayjs,
      /* 批量删除 */
      multiDelete,
      /* 搜索右上角 */
      successSave,
      closeCheck,
      register,
      upDateData,
      tableOptions,
      checkData,
      banState,
      useState,
      editNode,
      selectionChange,
      tableRef,
    };
  },
});
</script>
