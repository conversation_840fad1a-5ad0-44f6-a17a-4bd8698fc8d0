package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.Boolean;
import java.lang.String;

/**
 * AuthorizeJobPostRequirement Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-08 20:25:47
 */
@TableName(value = "pmsx_authorize_job_post_requirement")
@ApiModel(value = "AuthorizeJobPostRequirementEntity对象", description = "岗位授权要求检验表")
@Data

public class AuthorizeJobPostRequirement extends  ObjectEntity  implements Serializable{

    /**
     * 岗位授权管理Id
     */
    @ApiModelProperty(value = "岗位授权管理Id")
    @TableField(value = "authorize_manage_id")
    private String authorizeManageId;

    /**
     * 岗位id
     */
    @ApiModelProperty(value = "岗位id")
    @TableField(value = "job_post_id")
    private String jobPostId;

    /**
     * 要求类型(取得证书、通过培训)（冗余）
     */
    @ApiModelProperty(value = "要求类型(取得证书、通过培训)（冗余）")
    @TableField(value = "type")
    private String type;

    /**
     * 要求名称（冗余）
     */
    @ApiModelProperty(value = "要求名称（冗余）")
    @TableField(value = "name")
    private String name;

    /**
     * 应取得证书number（冗余）
     */
    @ApiModelProperty(value = "应取得证书number（冗余）")
    @TableField(value = "certificate_number")
    private String certificateNumber;

    /**
     * 应通过培训number
     */
    @ApiModelProperty(value = "应通过培训number")
    @TableField(value = "train_number")
    private String trainNumber;

    /**
     * 是否满足要求
     */
    @ApiModelProperty(value = "是否满足要求")
    @TableField(value = "is_satisfy")
    private Boolean isSatisfy;

    /**
     * 证书编号
     */
    @ApiModelProperty(value = "证书库ID")
    @TableField(value = "certificate_id")
    private String certificateId;

}
