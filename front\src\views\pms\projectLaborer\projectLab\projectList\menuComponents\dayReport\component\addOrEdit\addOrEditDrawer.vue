<template>
  <BasicDrawer
    v-model:is-continue="state.isContinue"
    destroyOnClose
    showFooter
    :width="1200"
    :title="state.title"
    :show-continue="state.showContinue"
    @register="modalRegister"
    @visible-change="visibleChange"
    @ok="confirm"
  >
    <div v-loading="state.loading">
      <DrawerForm
        ref="formModalRef"
        :action="state.action"
        @timeChange="timeChange"
      />
      <AsyncForm
        ref="asyncFormRef"
        :isRequire="true"
      />
      <div class="ml20 mr20">
        汇报总结
      </div>
      <div class="ml20 mr20">
        <InputTextArea
          v-model:value="state.summary"
          row="4"
        />
      </div>
      <AsyncForm
        ref="asyncNexFormRef"
        :isRequire="false"
      />
      <div class="ml20 mr20">
        <UploadList
          ref="tableRef"
          :listData="state.allFileList"
          type="modal"
          :on-change="onChange"
        />

        <!--        <BasicButton-->
        <!--          type="primary"-->
        <!--          icon="orion-icon-upload"-->
        <!--          @click="uploadRef.openModal(true)"-->
        <!--        >-->
        <!--          上传附件-->
        <!--        </BasicButton>-->
        <!--        <FormFileList-->
        <!--          :fileList="state.allFileList"-->
        <!--          @delete="deleteFile"-->
        <!--        />-->
      </div>
    </div>
    <template #appendFooter>
      <BasicButton
        v-if="state.originData?.commit"
        v-loading="state.checkLoading"
        type="primary"
        @Click="approve"
      >
        提交
      </BasicButton>
    </template>
  </BasicDrawer>
  <BasicUpload
    ref="uploadRef"
    :max-number="100"
    :isClassification="false"
    :isToolRequired="false"
    :onSaveChange="fileDataChange"
    :isButton="false"
  />
</template>

<script setup lang="ts">
import {
  defineProps, reactive, defineExpose, defineEmits, ref,
} from 'vue';
import {
  BasicDrawer, useDrawer, BasicButton, BasicUpload, UploadList,
} from 'lyra-component-vue3';
import { message, Input } from 'ant-design-vue';
import dayjs from 'dayjs';
import { useRoute } from 'vue-router';
import DrawerForm from './DrawerForm.vue';
import Api from '/@/api';
import AsyncForm from './AsyncForm.vue';
import FormFileList from './FormFileList.vue';

const route = useRoute();
const InputTextArea = Input.TextArea;
const [modalRegister, modalMethods] = useDrawer();
const props = defineProps({
  // details: {
  //   type: Object,
  //   default: () => {},
  // },
});
const emit = defineEmits(['update']);
const uploadRef = ref(null);
const asyncFormRef = ref(null);
const asyncNexFormRef = ref(null);

function initData() {
  return {
    action: 'add',
    title: '',
    summary: '', // 汇报总结
    isContinue: false,
    loading: false,
    showContinue: true,
    originData: {},
    allFileList: [],
    cacheData: {},
    nextTimePlanList: [],
    checkLoading: false,
  };
}

const state = reactive(initData());
const formModalRef = ref();

function visibleChange(show) {
  if (!show) {
    Object.assign(state, initData());
    modalMethods.setDrawerProps({ confirmLoading: false });
  }
}

interface openModalTypes {
    action: String;// add  edit 等
    info?: any
}

function openDrawer(data: openModalTypes) {
  modalMethods.openDrawer(true);
  data && usualHandle(data);
  if (data.action === 'add') {
    // 如果是新增,并传了时间,则要设置时间,并获取明日计划内容
    if (data.info?.addThisDay) {
      setTimeout(() => {
        formModalRef.value.formMethods.setFieldsValue({ daily: data.info?.addThisDay });
        timeChange(data.info?.addThisDay);
      }, 100);
    }
  }
  if (data.action === 'edit') {
    state.showContinue = false;
    state.loading = true;
    state.title = '编辑日报';
    new Api('/pms').fetch('', `projectDaily-statement/${data?.info?.id}`, 'GET').then((res) => {
      state.cacheData = JSON.parse(JSON.stringify(res));
      if (res?.edit) {
        modalMethods.setDrawerProps({ showOkBtn: res?.edit ?? false });
      }
      const {
        daily,
        status,
        reviewedBy,
        carbonCopyByList,
        summary,
        projectDailyStatementContentVOList,
        nexDayVOList,
        documentVOList,
      } = res;
      state.allFileList = documentVOList;
      setTimeout(() => {
        formModalRef.value.formMethods.setFieldsValue({
          daily,
          status,
          reviewedBy,
          carbonCopyByList,
        });
        state.summary = summary;
        asyncFormRef.value.showThisAsyncItem(projectDailyStatementContentVOList);
        asyncNexFormRef.value.showThisAsyncItem(nexDayVOList);
      }, 100);
    }).finally(() => {
      state.loading = false;
    });
  }
}

function usualHandle(data) {
  data?.action && (state.action = data?.action);
  data?.action === 'add' && (state.title = '新增日报');
  data?.action === 'edit' && (state.title = '编辑日报');
  if (data?.info) {
    state.originData = JSON.parse(JSON.stringify(data.info));
  }
}

// 确定
async function confirm() {
  formModalRef.value && await formModalRef.value.formMethods.validate();
  asyncFormRef.value && await asyncFormRef.value.validate();
  asyncNexFormRef.value && await asyncNexFormRef.value.validate();
  modalMethods.setDrawerProps({ confirmLoading: true });
  try {
    await goFetch().then(() => {
      message.success('操作成功');
      state.allFileList = [];
      emit('update');
      if (!state?.isContinue) {
        modalMethods.openDrawer(false);
      } else {
        // formModalRef.value && formModalRef.value.formMethods.resetFields();
        // asyncFormRef.value.resetFields;
        // asyncNexFormRef.value.resetFields;
        // state.summary = null;
      }
    });
  } catch (_) {
  } finally {
    modalMethods.setDrawerProps({ confirmLoading: false });
    isGoApprove.value = false;
  }
}

// 请求操作
async function goFetch() {
  const formData = formModalRef.value && formModalRef.value.formMethods.getFieldsValue();
  const params = JSON.parse(JSON.stringify(formData));
  if (params?.daily) {
    params.daily = dayjs(params.daily).format('YYYY-MM-DD');
  }
  const asyncFormData = asyncFormRef.value.getFieldsValue();
  const asyncNexFormData = asyncNexFormRef.value.getFieldsValue();
  let contentVOList = formatData(asyncFormData);

  let arr = state.allFileList.map((item) => ({
    ...item,
    modifyTime: item.modifyTime ? dayjs(item.modifyTime).format('YYYY-MM-DDTHH:mm:ss') : '',
    createTime: item.modifyTime ? dayjs(item.modifyTime).format('YYYY-MM-DDTHH:mm:ss') : '',
  }));

  let obj = {
    // contentVOList: formatData(asyncFormData)[0].relationship = 'fyrt1725122164153274368',
    fileDTOList: arr,
    contentVOList,
    nexDayVOList: asyncNexFormData ? formatData(asyncNexFormData) : [],
    summary: state.summary,
    ...params,
  };
  if (state.action === 'add') {
    obj.projectId = route.query.id;
    return await new Api('/pms').fetch(obj, 'projectDaily-statement/add', 'POST').then((res) => {
      if (isGoApprove.value) {
        state.checkLoading = true;
        return new Api('/pms').fetch('', `projectDaily-statement/submit/${res.id}`, 'PUT').finally(() => {
          state.checkLoading = false;
        });
      }
    });
  }
  if (state.action === 'edit') {
    obj.id = state.cacheData.id;
    return await new Api('/pms').fetch(obj, 'projectDaily-statement', 'PUT').then((res) => {
      if (isGoApprove.value) {
        state.checkLoading = true;
        return new Api('/pms').fetch('', `projectDaily-statement/submit/${state.cacheData.id}`, 'PUT').finally(() => {
          state.checkLoading = false;
        });
      }
    });
  }
}

function fileDataChange(successAll) {
  const files = successAll.map((item) => item.result);
  state.allFileList = state.allFileList.concat(files ?? []);
}

function formatData(data) {
  const result = Object.keys(data)
    .reduce((acc, key) => {
      const [prefix, index] = key.split('_');
      const currentIndex = parseInt(index, 10);

      if (!acc[currentIndex]) {
        acc[currentIndex] = {};
      }

      acc[currentIndex][prefix] = data[key];

      return acc;
    }, []).filter((item) => Object.values(item).every((value) => value !== null));

  return result;
}

const isGoApprove = ref(false);

function approve() {
  isGoApprove.value = true;
  confirm();
}

// 时间变化回调
function timeChange(time) {
  state.loading = true;
  new Api('/pms').fetch({
    day: time,
    projectId: route.query.id,
  }, 'projectDaily-statement/today/info', 'GET').then((res) => {
    if (res) {
      // state.nextTimePlanList = res;
      asyncFormRef.value.showThisAsyncItem(res.projectDailyStatementContentVOList ?? []);
    } else {
      asyncFormRef.value.showThisAsyncItem([]);
    }
  }).finally(() => {
    state.loading = false;
  });
}

// 文件删除
function deleteFile(index) {
  state.allFileList.splice(index, 1);
}
function onChange(listData) {
  state.allFileList = listData;
}
defineExpose({
  modalMethods,
  openDrawer,
});
</script>

<style scoped lang="less">

</style>
