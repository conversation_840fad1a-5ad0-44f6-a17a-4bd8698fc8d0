<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.opentest4j</groupId>
  <artifactId>opentest4j</artifactId>
  <version>1.2.0</version>
  <name>org.opentest4j:opentest4j</name>
  <description>Open Test Alliance for the JVM</description>
  <url>https://github.com/ota4j-team/opentest4j</url>
  <licenses>
    <license>
      <name>The Apache License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>bechte</id>
      <name><PERSON></name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>jlink</id>
      <name>Johannes Link</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>marcphilipp</id>
      <name>Marc Philipp</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>mmerdes</id>
      <name>Matthias Merdes</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>sbrannen</id>
      <name>Sam Brannen</name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:git://github.com/ota4j-team/opentest4j.git</connection>
    <developerConnection>scm:git:git://github.com/ota4j-team/opentest4j.git</developerConnection>
    <url>https://github.com/ota4j-team/opentest4j</url>
  </scm>
</project>
