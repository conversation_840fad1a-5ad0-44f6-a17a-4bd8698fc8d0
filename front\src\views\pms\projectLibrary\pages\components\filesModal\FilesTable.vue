<script setup lang="ts">
import {
  BasicButton, downLoadById, OrionTable, UploadList,
} from 'lyra-component-vue3';
import {
  h, onMounted, ref, Ref, unref,
} from 'vue';
import dayjs from 'dayjs';
import Api from '/@/api';

const props = defineProps<{
  dataId:string
}>();

const selectKeys:Ref<string[]> = ref([]);
const tableRef:Ref = ref();
const tableOptions = {
  showTableSetting: false,
  showSmallSearch: false,
  showToolButton: false,
  rowSelection: {},
  pagination: false,
  api: async () => {
    const result = await new Api('/res/manage/file/new').fetch('', props.dataId, 'GET');
    return result.map((item) => {
      delete item.children;
      return {
        ...item,
        fileName: `${item.name}.${item.filePostfix}`,
      };
    });
  },
  columns: [
    {
      title: '名称',
      dataIndex: 'fileName',
      customRender({ text }) {
        return h('div', { class: 'action-btn' }, text);
      },
    },
    {
      title: '文件大小',
      dataIndex: 'fileSize',
    },
    {
      title: '上传时间',
      dataIndex: 'createTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '';
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 80,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '下载',
      onClick: (record) => handleBatchDownload([record.id]),
    },
  ],
};

// 批量下载
function handleBatchDownload(ids) {
  ids.map((item) => {
    downLoadById(item);
  });
}

function selectionChange({ keys }) {
  selectKeys.value = keys;
}

async function listApi(params) {
  return new Api('/res/manage/file/new').fetch('', props.dataId, 'GET');
}

</script>

<template>
  <div style="height: 400px;overflow: hidden">
    <UploadList
      :listApi="listApi"
      :edit="false"
    />

    <!--    <OrionTable-->
    <!--      ref="tableRef"-->
    <!--      :options="tableOptions"-->
    <!--      :onSelectionChange="selectionChange"-->
    <!--    >-->
    <!--      <BasicButton-->
    <!--        type="primary"-->
    <!--        :disabled="selectKeys.length===0"-->
    <!--        icon="orion-icon-download"-->
    <!--        @click="handleBatchDownload(selectKeys)"-->
    <!--      >-->
    <!--        批量下载-->
    <!--      </BasicButton>-->
    <!--    </OrionTable>-->
  </div>
</template>

<style scoped lang="less">

</style>
