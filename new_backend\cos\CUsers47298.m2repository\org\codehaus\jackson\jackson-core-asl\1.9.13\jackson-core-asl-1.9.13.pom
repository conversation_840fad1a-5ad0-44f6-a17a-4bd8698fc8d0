<?xml version="1.0" encoding="UTF-8"?>
<project>

 <!-- General information -->

  <modelVersion>4.0.0</modelVersion>
  <groupId>org.codehaus.jackson</groupId>
  <artifactId>jackson-core-asl</artifactId>
  <packaging>jar</packaging>
  <name><PERSON></name>
  <version>1.9.13</version>
  <description><PERSON> is a high-performance JSON processor (parser, generator)
</description>

 <!-- Contact information -->

  <url>http://jackson.codehaus.org</url>
  <scm>
    <url>http://xircles.codehaus.org/projects/jackson/repos/primary/repo</url>
  </scm>
  <issueManagement>
    <url>http://jira.codehaus.org/browse/JACKSON</url>
  </issueManagement>
  <developers>
    <developer>
      <id>cowtowncoder</id>
      <name>Tatu Sal<PERSON>ta</name>
      <email><EMAIL></email>
    </developer>
  </developers>

 <!-- Dependency information -->
 
  <dependencies>
    <!-- no dependencies, for now -->
  </dependencies>

  <!-- Licensing (joy!) -->
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <organization>
    <name>FasterXML</name>
    <url>http://fasterxml.com</url>
  </organization>

</project>
