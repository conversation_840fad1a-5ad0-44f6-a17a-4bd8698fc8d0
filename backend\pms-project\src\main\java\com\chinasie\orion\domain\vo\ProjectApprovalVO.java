package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;
import java.util.List;

/**
 * ProjectApproval Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-06 10:36:23
 */
@ApiModel(value = "ProjectApprovalVO对象", description = "项目立项表")
@Data
public class ProjectApprovalVO extends ObjectVO implements Serializable{

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    private String projectNumber;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 项目类型
     */
    @ApiModelProperty(value = "项目类型")
    private String projectType;

    /**
     * 项目类型
     */
    @ApiModelProperty(value = "项目类型名称")
    private String projectTypeName;

    /**
     * 项目类型
     */
    @ApiModelProperty(value = "项目子类型")
    private String projectSubType;

    /**
     * 项目类型
     */
    @ApiModelProperty(value = "项目子类型名称")
    private String projectSubTypeName;

    /**
     * 发起时间
     */
    @ApiModelProperty(value = "发起时间")
    private Date startTime;

    /**
     * 项目来源
     */
    @ApiModelProperty(value = "项目来源")
    private String projectSource;

    /**
     * 项目来源
     */
    @ApiModelProperty(value = "项目来源名称")
    private String projectSourceName;

    /**
     * 项目开始时间
     */
    @ApiModelProperty(value = "项目开始时间")
    private Date projectStartTime;

    /**
     * 项目开始时间
     */
    @ApiModelProperty(value = "项目开始时间")
    private Date projectEndTime;

    /**
     * 项目负责人
     */
    @ApiModelProperty(value = "项目负责人")
    private String resPerson;

    /**
     * 项目负责人
     */
    @ApiModelProperty(value = "项目负责人名称")
    private String resPersonName;

    /**
     * 责任部门
     */
    @ApiModelProperty(value = "责任部门")
    private String resDept;

    /**
     * 责任部门
     */
    @ApiModelProperty(value = "责任部门名称")
    private String resDeptName;

    /**
     * 立项理由
     */
    @ApiModelProperty(value = "立项理由")
    private String approvalReason;

    /**
     * 预估金额
     */
    @ApiModelProperty(value = "预估金额")
    private BigDecimal estimateAmt;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 立项编码
     */
    @ApiModelProperty(value = "立项编码")
    private String number;
    /**
     * 立项名称
     */
    @ApiModelProperty(value = "立项名称")
    private String name;
    /**
     * 立项类型
     */
    @ApiModelProperty(value = "立项类型")
    private String type;
    @ApiModelProperty(value = "立项类型名称")
    private String typeName;
    /**
     * 来源
     */
    @ApiModelProperty(value = "来源")
    private String source;

    @ApiModelProperty(value = "来源名称")
    private String sourceName;

    /**
     * 预估项目开始时间
     */
    @ApiModelProperty(value = "预估项目开始时间")
    private Date estimateStartTime;

    /**
     * 预估项目结束时间
     */
    @ApiModelProperty(value = "预估项目结束时间")
    private Date estimateEndTime;

    /**
     * 需求评审单
     */
    @ApiModelProperty(value = "需求评审单")
    private String requireReviewId;

    @ApiModelProperty(value = "需求评审单")
    private String requireReviewName;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private String rspUser;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人名称")
    private String rspUserName;


    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人部门")
    private String rspDeptName;

    @ApiModelProperty(value = "立项支持性材料")
    private List<FileDTO> attachments;



}
