package com.chinasie.orion.domain.vo;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

import java.util.List;

/**
 * NcfFormZftvVEfTF VO对象
 *
 * <AUTHOR>
 * @since 2024-06-04 14:36:09
 */
@ApiModel(value = "NonFixedAssetsVO对象", description = "非固定资产标准库")
@Data
public class NonFixedAssetsVO extends ObjectVO implements Serializable {

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 资产条码
     */
    @ApiModelProperty(value = "资产条码")
    private String barcode;


    /**
     * 资产名称
     */
    @ApiModelProperty(value = "资产名称")
    private String name;


    /**
     * 规格型号
     */
    @ApiModelProperty(value = "规格型号")
    private String spModel;


    /**
     * 是否需要检定
     */
    @ApiModelProperty(value = "是否需要检定")
    private Boolean isNeedVerification;


}
