package com.chinasie.orion.management.service.impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.management.domain.dto.SupplierCertInfoDTO;
import com.chinasie.orion.management.domain.entity.SupplierCertInfo;
import com.chinasie.orion.management.domain.vo.SupplierCertInfoVO;
import com.chinasie.orion.management.repository.SupplierCertInfoMapper;
import com.chinasie.orion.management.service.SupplierCertInfoService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <p>
 * SupplierCertInfo 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@Service
@Slf4j
public class SupplierCertInfoServiceImpl extends OrionBaseServiceImpl<SupplierCertInfoMapper, SupplierCertInfo> implements SupplierCertInfoService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public SupplierCertInfoVO detail(String id, String pageCode) throws Exception {
        SupplierCertInfo supplierCertInfo = this.getById(id);
        SupplierCertInfoVO result = BeanCopyUtils.convertTo(supplierCertInfo, SupplierCertInfoVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param supplierCertInfoDTO
     */
    @Override
    public String create(SupplierCertInfoDTO supplierCertInfoDTO) throws Exception {
        SupplierCertInfo supplierCertInfo = BeanCopyUtils.convertTo(supplierCertInfoDTO, SupplierCertInfo::new);
        this.save(supplierCertInfo);

        String rsp = supplierCertInfo.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param supplierCertInfoDTO
     */
    @Override
    public Boolean edit(SupplierCertInfoDTO supplierCertInfoDTO) throws Exception {
        SupplierCertInfo supplierCertInfo = BeanCopyUtils.convertTo(supplierCertInfoDTO, SupplierCertInfo::new);

        this.updateById(supplierCertInfo);

        String rsp = supplierCertInfo.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<SupplierCertInfoVO> pages(String mainTableId, Page<SupplierCertInfoDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<SupplierCertInfo> condition = new LambdaQueryWrapperX<>(SupplierCertInfo.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(SupplierCertInfo::getCreateTime);

        condition.eq(SupplierCertInfo::getMainTableId, mainTableId);

        Page<SupplierCertInfo> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), SupplierCertInfo::new));

        PageResult<SupplierCertInfo> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<SupplierCertInfoVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<SupplierCertInfoVO> vos = BeanCopyUtils.convertListTo(page.getContent(), SupplierCertInfoVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "资质信息表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", SupplierCertInfoDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        SupplierCertInfoExcelListener excelReadListener = new SupplierCertInfoExcelListener();
        EasyExcel.read(inputStream, SupplierCertInfoDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<SupplierCertInfoDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("资质信息表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<SupplierCertInfo> supplierCertInfoes = BeanCopyUtils.convertListTo(dtoS, SupplierCertInfo::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("ncf::SupplierCertInfo-import::id", importId, supplierCertInfoes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<SupplierCertInfo> supplierCertInfoes = (List<SupplierCertInfo>) orionJ2CacheService.get("ncf::SupplierCertInfo-import::id", importId);
        log.info("资质信息表导入的入库数据={}", JSONUtil.toJsonStr(supplierCertInfoes));

        this.saveBatch(supplierCertInfoes);
        orionJ2CacheService.delete("ncf::SupplierCertInfo-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("ncf::SupplierCertInfo-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(Page<SupplierCertInfoDTO> pageRequest, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<SupplierCertInfo> condition = new LambdaQueryWrapperX<>(SupplierCertInfo.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(SupplierCertInfo::getCreateTime);
        List<SupplierCertInfo> supplierCertInfoes = this.list(condition);

        List<SupplierCertInfoDTO> dtos = BeanCopyUtils.convertListTo(supplierCertInfoes, SupplierCertInfoDTO::new);

        String fileName = "资质信息表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", SupplierCertInfoDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<SupplierCertInfoVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }

    @Override
//    @OperationPower(operationType = OperationPowerType.DETAIL)
    public Page<SupplierCertInfoVO> getByCode(Page<SupplierCertInfoDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<SupplierCertInfo> condition = new LambdaQueryWrapperX<>(SupplierCertInfo.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(SupplierCertInfo::getCreateTime);
        if (pageRequest.getQuery() == null || pageRequest.getQuery().getSupplierCode() == null) {
            throw new Exception("供应商号为空，请输入");
        }
        condition.eq(SupplierCertInfo::getSupplierCode, pageRequest.getQuery().getSupplierCode());

        Page<SupplierCertInfo> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), SupplierCertInfo::new));

        PageResult<SupplierCertInfo> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<SupplierCertInfoVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<SupplierCertInfoVO> vos = BeanCopyUtils.convertListTo(page.getContent(), SupplierCertInfoVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    public static class SupplierCertInfoExcelListener extends AnalysisEventListener<SupplierCertInfoDTO> {

        private final List<SupplierCertInfoDTO> data = new ArrayList<>();

        @Override
        public void invoke(SupplierCertInfoDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<SupplierCertInfoDTO> getData() {
            return data;
        }
    }


}
