<script setup lang="ts">
import { Empty, randomString } from 'lyra-component-vue3';
import {
  inject, onMounted, ref, Ref, watch, watchEffect,
} from 'vue';
import Api from '/@/api';
import { find as _find, get as _get } from 'lodash-es';
import { useProgressDetails } from '/@/views/pms/majorRepairsSecond/pages/components/hooks/useProgressDetails';

const props = defineProps<{
  setProgressStatisticsData: Function
}>();

const updateProgressKey = inject('updateProgressKey');
const detailsData: Record<string, any> = inject('detailsData');
const tableHeads: Array<{
  key: string,
  name: string,
  class?: string
}> = [
  {
    key: 'NPLN',
    name: '待准备',
    class: 'work-prepare2',
  },
  {
    key: 'ASGN',
    name: '已分配',
    class: 'work-prepare2',
  },
  {
    key: 'INPL',
    name: '准备中',
    class: 'work-prepare2',
  },
  {
    key: 'PLND',
    name: '已准备',
    class: 'work-prepare2',
  },
  {
    key: 'RPLN',
    name: '重新准备',
    class: 'work-prepare2',
  },
  {
    key: 'PRWD',
    name: '已校核',
    class: 'work-prepare2',
  },
  {
    key: 'APPV',
    name: '已批准',
    class: 'prepare-finish2',
  },
  {
    key: 'SCHD',
    name: '已计划',
    class: 'prepare-finish2',
  },
  {
    key: 'RTW',
    name: '已下达',
    class: 'work-out2',
  },
  {
    key: 'WIP',
    name: '执行中',
    class: 'work-out2',
  },
  {
    key: 'CSR',
    name: '工作完成',
    class: 'work-closed2',
  },
  {
    key: 'CPL',
    name: '关闭',
    class: 'work-closed2',
  },
  {
    key: 'REJ',
    name: '拒绝',
    class: 'work-closed2',
  },
];

const list: Ref<any[]> = ref([]);
const loading = ref<boolean>(false);

async function getData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/job-manage/develop/list').fetch({
      repairRound: detailsData?.repairRound,
    }, '', 'GET');
    list.value = _get(result, 'jobDevelopVOS', []);
    props?.setProgressStatisticsData(_get(result, 'statisticMap', {}));
  } finally {
    loading.value = false;
  }
}

function parseDecimalToPre(num) {
  if (!num || isNaN(Number(num))) {
    return num;
  }
  let [integer, decimal = ''] = String(num).split('.');
  if (`${decimal}`.length > 2) {
    return `${num.toFixed(2)}%`;
  }
  return `${num}%`;
}

watch(() => updateProgressKey.value, () => {
  getData();
});
onMounted(() => {
  if (detailsData?.repairRound) {
    getData();
  }
});

const { openProgressModal } = useProgressDetails();

function handleCellData(item: {
  rspDeptName: string
  rspDeptId: string
}, table?: { key: string }) {
  if (item.rspDeptName === '合计') {
    openProgressModal({
      norO: 'O',
      phase: table?.key || undefined,
      repairRound: detailsData?.repairRound,
    });
    return;
  }
  openProgressModal({
    norO: 'O',
    phase: table?.key || undefined,
    repairRound: detailsData?.repairRound,
    rspDept: item.rspDeptId,
  });
}

defineExpose({
  update: getData,
});

</script>

<template>
  <div
    v-loading="loading"
    class="table-container"
  >
    <table>
      <thead>
        <tr>
          <th
            scope="col"
            class="center-name"
            rowspan="3"
          >
            中心名称
          </th>
          <th
            scope="col"
            class="work-prepare"
            colspan="6"
          >
            作业准备
          </th>
          <th
            scope="col"
            colspan="2"
            class="prepare-finish"
          >
            准备完成
          </th>
          <th
            scope="col"
            colspan="2"
            class="work-out"
          >
            作业实施
          </th>
          <th
            scope="col"
            class="work-closed"
            colspan="3"
          >
            作业关闭
          </th>
          <th
            scope="col"
            class="th-green"
            rowspan="3"
          >
            合计
          </th>
          <th
            scope="col"
            class="th-green"
            rowspan="3"
          >
            准备率
          </th>
          <th
            scope="col"
            class="th-green"
            rowspan="3"
          >
            实施率
          </th>
          <th
            scope="col"
            class="th-green"
            rowspan="3"
          >
            完成率
          </th>
        </tr>

        <tr>
          <th
            v-for="item in tableHeads"
            :key="item.key"
            scope="col"
            :class="item.class"
          >
            {{ item.key }}
          </th>
        </tr>
        <tr>
          <th
            v-for="item in tableHeads"
            :key="item.key"
            scope="col"
            :class="item.class"
          >
            {{ item.name }}
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          v-for="item in list"
          :key="item.toString()"
        >
          <td>{{ item.rspDeptName }}</td>

          <td
            v-for="table in tableHeads"
            :key="table.key"
            :class="[table.class,'action-btn']"
            @click="handleCellData(item,table)"
          >
            {{ item?.jobStatusCountList?.find(v => v.jobStatusKey === table.key)?.count }}
          </td>
          <td
            class="th-green action-btn"
            @click="handleCellData(item)"
          >
            {{ _get(_find(item?.jobStatusCountList, ['jobStatusKey', 'SUM']), 'count', '-') }}
          </td>
          <td class="th-green">
            {{ parseDecimalToPre(_get(_find(item?.jobStatusCountList, ['jobStatusKey', 'RESRAT']), 'count', '-')) }}
          </td>
          <td class="th-green">
            {{ parseDecimalToPre(_get(_find(item?.jobStatusCountList, ['jobStatusKey', 'JICRAT']), 'count', '-')) }}
          </td>
          <td class="th-green">
            {{ parseDecimalToPre(_get(_find(item?.jobStatusCountList, ['jobStatusKey', 'COMRAT']), 'count', '-')) }}
          </td>
        </tr>
      </tbody>
    </table>
    <div
      v-if="list?.length===0"
      class="empty-wrap"
    >
      <Empty />
    </div>
  </div>
</template>

<style scoped lang="less">
.table-container {

  table {
    width: 100%;
    border-collapse: collapse;
    border: none;
    font-size: 14px;
    letter-spacing: 1px;

    thead {
      th {
        border: 2px solid #fff;
        background-color: #eee;
        padding: 10px 0;

        &.th-green {
          background-color: #7caa65;
          color: #eef4ea
        }

        &.center-name {
          background-color: #eeeeee;
          color: #424242
        }

        &.work-prepare {
          background-color: #f0c78a;
          color: #fefdfb
        }

        &.work-prepare2 {
          background-color: #faecd8;
          color: #3e3d3c
        }

        &.prepare-finish {
          background-color: #e9ac50;
          color: #fefdfa
        }

        &.prepare-finish2 {
          background-color: #f5dab1;
          color: #474440
        }

        &.work-out {
          background-color: #6c99ff;
          color: #d7e2fe
        }

        &.work-out2 {
          background-color: #b7ccfd;
          color: #44474d
        }

        &.work-closed {
          background-color: #77c94e;
          color: #fff
        }

        &.work-closed2 {
          background-color: #c2e7b0;
          color: #64715e
        }
      }
    }

    tbody {
      td {
        text-align: center;
        padding: 10px 0;
        background-color: #eee;
        border: 2px solid #fff;

        &.th-green {
          background-color: #b6d8a6;
        }

        &.work-prepare2 {
          background-color: #fdf6ec;
        }

        &.prepare-finish2 {
          background-color: #f8e4c5;
        }

        &.work-out2 {
          background-color: #c9d9fe;
        }

        &.work-closed2 {
          background-color: #d2edc4;
        }
      }
    }
  }
}

.bg1 {
  background-color: rgb(214, 255, 235) !important;
}

.bg2 {
  background-color: rgb(235, 235, 255) !important;
}

.bg3 {
  background-color: rgb(255, 192, 192) !important;
}

.empty-wrap {
  width: 100%;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
