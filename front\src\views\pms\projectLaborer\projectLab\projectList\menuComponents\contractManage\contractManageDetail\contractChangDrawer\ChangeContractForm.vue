<template>
  <div class="h-full flex flex-ver">
    <BasicTabs
      v-model:tabsIndex="state.tabsIndex"
      :tabs="tabs"
    >
      <template #tabsItem="{item}">
        <div class="tabs-item">
          <span v-if="item.key==='1'">*</span>{{ item.name }}
        </div>
      </template>
    </BasicTabs>
    <div class="flex-f1">
      <BasicScrollbar>
        <!--原因-->
        <ChangeCause
          v-show="tabs[state.tabsIndex]?.key === '1'"
          ref="changeCauseRef"
        />
        <!--合同支付节点信息-->
        <PayNode
          v-show="tabs[state.tabsIndex]?.key === '4'"
          ref="payNodeRef"
          type="change"
          :baseMethods="baseMethods"
        />

        <!--合同附件信息-->
        <ContractFiles
          v-show="tabs[state.tabsIndex]?.key === '5'"
          ref="contractFilesRef"
        />
      </BasicScrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import { BasicTabs, BasicScrollbar } from 'lyra-component-vue3';
import { onMounted, reactive, ref } from 'vue';
import { Button as AButton, message } from 'ant-design-vue';
import { ChangeCause, PayNode, ContractFiles } from './components';
import Api from '/@/api';

const props = defineProps<{
  allMoney: number,
  editData?: any
}>();

onMounted(() => {
  init();
});

async function init() {
  const data = await new Api(`/pas/projectContract/all/${props.editData.id}`).fetch('', '', 'get');
  setValues(data);
}

const state = reactive({
  tabsIndex: 0,
});

// 合同基本信息
const changeCauseRef = ref();
// 合同支付节点信息
const payNodeRef = ref();
// 合同附件信息
const contractFilesRef = ref();

// 基础方法的传递
const baseMethods = {
  // 获取合同金额,主要用到合同支付节点里面的判断
  getAllMoney() {
    return props.allMoney ?? 0;
  },
};

async function getFormData() {
  // 合同变更原因
  const { changeReason } = await changeCauseRef.value?.getValues();
  const projectContractChangeApplyDTO = {
    changeReason,
  };

  // 合同支付节点信息
  const contractPayNodeDTOList = await payNodeRef.value?.getValues();

  // 合同附件信息
  const fileInfoDTOList = contractFilesRef.value?.getValues();

  return {
    projectContractChangeApplyDTO,
    contractPayNodeDTOList,
    fileInfoDTOList,
  };
}

async function setValues(values) {
  const {
    projectContractChangeApplyVO, contractOurSignedMainVO, contractSupplierSignedMainVO, contractPayNodeVOList, documentVOList,
  } = values;

  // 合同变更原因
  changeCauseRef.value?.setValues(projectContractChangeApplyVO);

  // 合同支付节点信息
  payNodeRef.value?.setValues(contractPayNodeVOList);
  // 合同附件信息
  contractFilesRef.value?.setValues(documentVOList);
}

const tabs = [
  {
    key: '1',
    name: '合同变更原因',
  },
  {
    key: '2',
    name: '合同变更内容',
  },
  // {
  //   key: '3',
  //   name: '采购订单信息',
  // },
  {
    key: '4',
    name: '合同支付节点信息',
  },
  {
    key: '5',
    name: '合同附件信息',
  },
];

defineExpose({
  getFormData,
  setValues,
});
</script>

<style scoped lang="less">
.tabs-item {
  >span {
    color: ~`getPrefixVar('error-color')`;
  }
}
</style>
