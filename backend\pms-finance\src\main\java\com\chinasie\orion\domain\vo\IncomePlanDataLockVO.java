package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * IncomePlanDataLock VO对象
 *
 * <AUTHOR>
 * @since 2024-09-29 18:50:39
 */
@ApiModel(value = "IncomePlanDataLockVO对象", description = "收入几乎是数据锁定表")
@Data
public class IncomePlanDataLockVO extends  ObjectVO   implements Serializable{

        /**
         * 专业中心
         */
        @ApiModelProperty(value = "专业中心")
        private String expertiseCenter;

        /**
         * 专业中心名称
         */
        @ApiModelProperty(value = "专业中心")
        private String expertiseCenterTitle;


        /**
         * 专业所
         */
        @ApiModelProperty(value = "专业所")
        private String expertiseStation;

        /**
         * 专业所名称
         */
        @ApiModelProperty(value = "专业所")
        private String expertiseStationTitle;


        /**
         * 锁状态
         */
        @ApiModelProperty(value = "锁状态")
        private String lockType;


        /**
         * 收入计划填报Id
         */
        @ApiModelProperty(value = "收入计划填报Id")
        private String incomePlanId;


        /**
         * 锁状态
         */
        @ApiModelProperty(value = "锁状态")
        private String lockStatus;

        /**
         * 锁状态名称
         */
        @ApiModelProperty(value = "锁状态名称")
        private String lockStatusName;


        /**
         * 数据操作权限 1:表示为true,0:表示为false
         */
        @ApiModelProperty(value = "数据操作权限")
        private String isFlag;
}
