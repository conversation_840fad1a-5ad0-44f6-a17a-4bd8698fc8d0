import Api from '/@/api';

// 6项目计划计划模版

/**
 * 新增
 * @param params
 */
export async function projectSchemeMilestoneTemplate(params: string[]) {
  return new Api('/pms/projectSchemeMilestoneTemplate').fetch(params, '', 'POST');
}

/**
 * 删除项目
 * @param params
 */
export async function deleteNewProject(params: string[]) {
  return new Api('/pms/project').fetch(params, '', 'delete');
}

/**
 * 项目计划计划节点--删除
 * @param  params
 */
export async function deleteProjectSchemeMilestoneNode(params) {
  return new Api('/pms/projectSchemeMilestoneNode').fetch(params, '', 'delete');
}

/**
 * 项目计划计划节点--分页
 * @param  params
 */
export async function postProjectSchemeMilestoneNodePages(params) {
  return new Api('/pms/projectSchemeMilestoneNode/pages').fetch(params, '', 'post');
}

/**
 * 项目计划计划节点--树列表
 * @param  params
 */
export async function postProjectSchemeMilestoneNodeTree(tplId) {
  return new Api(`/pms/projectSchemeMilestoneNode/tree/${tplId}`).fetch('', '', 'post');
}

/**
 * 项目计划计划模版--分页
 * @param  params
 */
export async function postProjectSchemeMilestoneTemplatelist() {
  return new Api('/pms/projectSchemeMilestoneTemplate/list').fetch('', '', 'post');
}

/**
 * 项目计划计划节点--新增
 * @param  params
 */
export async function postProjectSchemeMilestoneNode(params) {
  return new Api('/pms/projectSchemeMilestoneNode').fetch(params, '', 'post');
}

/**
 * 项目计划计划节点--编辑
 * @param  params
 */
export async function putProjectSchemeMilestoneNode(params) {
  return new Api('/pms/projectSchemeMilestoneNode').fetch(params, '', 'put');
}

/**
 * 项目计划计划模版--新增
 * @param  params
 */
export async function postProjectSchemeMilestoneTemplate(params) {
  return new Api('/pms/projectSchemeMilestoneTemplate').fetch(params, '', 'post');
}

/**
 * 项目计划计划模版--编辑
 * @param  params
 */
export async function putProjectSchemeMilestoneTemplate(params) {
  return new Api('/pms/projectSchemeMilestoneTemplate').fetch(params, '', 'put');
}
