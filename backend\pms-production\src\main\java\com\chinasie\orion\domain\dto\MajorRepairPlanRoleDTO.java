package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * MajorRepairPlanRole DTO对象
 *
 * <AUTHOR>
 * @since 2024-07-30 19:21:00
 */
@ApiModel(value = "MajorRepairPlanRoleDTO对象", description = "大修计划角色")
@Data
@ExcelIgnoreUnannotated
public class MajorRepairPlanRoleDTO extends  ObjectDTO   implements Serializable{

    /**
     * 角色code
     */
    @ApiModelProperty(value = "角色code")
    @NotEmpty(message = "角色code不能为空")
    private String roleCode;

    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    @NotEmpty(message = "所属大修伦次不能为空")
    private String majorRepairTurn;

    @ApiModelProperty(value = "角色名称")
    private String roleName;

    @ApiModelProperty(value = "角色层级")
    @ExcelProperty(value = "角色层级 ", index = 1)
    @NotNull(message = "所属层级不能为空")
    private String roleLevel;

}
