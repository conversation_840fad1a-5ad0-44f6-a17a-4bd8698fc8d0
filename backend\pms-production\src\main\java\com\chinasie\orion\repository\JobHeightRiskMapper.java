package com.chinasie.orion.repository;
import com.chinasie.orion.domain.entity.JobHeightRisk;
import com.chinasie.orion.page.PageResult;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;

import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;

import java.util.List;
import java.util.Map;


/**
 * <p>
 * JobHeightRisk Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-07 11:41:22
 */
@Mapper
public interface JobHeightRiskMapper extends  OrionBaseMapper  <JobHeightRisk> {


    @Delete(" delete from pmsx_job_height_risk  where 1=1")
    void delAllList();

}

