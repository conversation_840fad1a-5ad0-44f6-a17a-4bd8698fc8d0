package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.chinasie.orion.service.DataUpdateMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class DataUpdateMessageServiceImpl implements DataUpdateMessageService {
    public static final String CACHE_KEY_DATA_UPDATED = "pms:data-updated:%s";

    @Autowired
    private RedisTemplate redisTemplate;

    @Override
    public void sendDataUpdatedMessage(String dataType, String dataId) {
        redisTemplate.opsForSet().add(String.format(CACHE_KEY_DATA_UPDATED, dataType), dataId);
    }

    @Override
    public List<String> popUpdatedDataIds(String dataType, Integer size) {
        String redisKey = String.format(CACHE_KEY_DATA_UPDATED, dataType);

        List<String> dataIds = redisTemplate.opsForSet().pop(redisKey, size);
        if (CollectionUtil.isEmpty(dataIds)) {
            return null;
        }

        return dataIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }
}
