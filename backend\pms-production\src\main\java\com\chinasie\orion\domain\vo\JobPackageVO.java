package com.chinasie.orion.domain.vo;

import com.chinasie.orion.file.api.domain.vo.FileTreeVO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

import java.util.List;

/**
 * JobPackage VO对象
 *
 * <AUTHOR>
 * @since 2024-06-09 15:47:59
 */
@ApiModel(value = "JobPackageVO对象", description = "作业工作包信息")
@Data
public class JobPackageVO extends ObjectVO implements Serializable {

    /**
     * 作业id
     */
    @ApiModelProperty(value = "作业id")
    private String jobId;


    /**
     * 功能位置
     */
    @ApiModelProperty(value = "功能位置")
    private String functionalLocation;


    /**
     * 设备/系统
     */
    @ApiModelProperty(value = "设备/系统")
    private String equipmentSystem;

    @ApiModelProperty(value = "作业信息对象")
    private JobManageVO jobManageVO;


    @ApiModelProperty(value = "风险信息")
    private List<JobRiskVO> riskVOList;

    @ApiModelProperty(value = "安措信息")
    private List<JobSecurityMeasureVO> securityMeasureVOList;
    @ApiModelProperty(value = "证明文件")
    private List<FileTreeVO> fileVOList;
    @ApiModelProperty(value = "作业名称")
    private String name;
    @ApiModelProperty(value = "大修伦次")
    private String majorRepairTurn;

}
