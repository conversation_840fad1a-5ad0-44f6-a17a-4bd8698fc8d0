<template>
  <div class="figureWorkTree">
    <div class="figureWorkTree_title">
      <div class="left">
        <FolderFilled class="folderTitle" />
        <span class="figureWorkTree_title_span">{{ titleName }}</span>
      </div>
    </div>
    <AInputSearch
      v-model:value="searchValue"
      style="margin-bottom: 8px"
      placeholder="请输入名称或编码"
      @search="searchChange"
    />
    <div class="treeContent">
      <ATree
        v-model:selectedKeys="selectedKeys"
        :tree-data="treeData"
        class="figureWorkTreeContent"
        :field-names="fieldNames"
        :expanded-keys="expandedKeys"
        @select="selectNode"
        @expand="expandNode"
      >
        <template #title="treeNode">
          <div
            class="treeNode "
          >
            <template
              v-if="$slots.nameIcon"
            >
              <slot name="nameIcon" />
            </template>
            <FolderFilled
              v-else
              class="folderTitle"
            />
            <span
              class="titleSpan flex-te"
              :title=" treeNode.name"
            >{{ treeNode.name }}</span>
          </div>
        </template>
      </ATree>
    </div>
    <template v-if="$slots.footer">
      <slot name="footer" />
    </template>
  </div>
</template>
<script lang="ts">
import {
  defineComponent, inject, onMounted, reactive, toRefs, watch,
} from 'vue';
import {
  PlusOutlined, MoreOutlined, FolderFilled,
} from '@ant-design/icons-vue';
import {
  Tree, Dropdown, Menu, Input, Modal, message,
} from 'ant-design-vue';
import { useDrawer, isPower } from 'lyra-component-vue3';
import Api from '/@/api';
import { em } from 'windicss/types/plugin/typography/utils';
export default defineComponent({
  name: 'BusinessTree',
  components: {
    ATree: Tree,
    AInputSearch: Input.Search,
    FolderFilled,
  },
  props: {
    initForm: {
      type: Boolean,
      default: true,
    },
    showBtn: {
      type: Boolean,
      default: false,
    },
    noId: {
      type: Boolean,
      default: false,
    },
    titleName: {
      type: String,
      default: '',
    },
    getTreeApi: {
      type: Function,
      default: () => null,
    },
    deleteTreeApi: {
      type: Function,
      default: () => null,
    },
    showDrawer: {
      type: Boolean,
      default: true,
    },
    fieldNames: {
      type: Object,
      default: () => ({
        name: 'name',
        id: 'id',
      }),
    },
    powerCode: {
      type: Object,
      default: () => ({}),
    },
    // treeData:{
    //   type:Array,
    //   default:[],
    // }
  },
  emits: [
    'selectNode',
    'initData',
    'projectChange',
    'isShowTable',
  ],
  setup(props, { emit }) {
    const powerData: any = inject('powerData', {});
    const state:any = reactive({
      searchValue: '',
      selectedKeys: [],
      autoExpandParent: false,
      dataList: [],
      treeData: [],
      nodeData: {},
      expandedKeys: [],
    });
    const [register, { openDrawer }] = useDrawer();
    const searchChange = (val) => {
      // console.log(state.treeData);
    };
    const selectNode = (selectedKeys, { selected, node }) => {
      if (selected) {
        if (selectedKeys[0] === '0-0') {
          // 发起全部请求
          emit('projectChange', state.treeData[0].children.map((item) => item.id));
        } else {
          emit('projectChange', [node.dataRef.id]);
        }
        // node.dataRef.id 项目id
      }
    };
    onMounted(() => {
      getFormData();
    });
    async function getFormData() {
      let res = await props.getTreeApi({ searchText: state.searchValue });
      state.treeData = res;
      emit('isShowTable', [state.treeData[0]?.id]);
      if (state.treeData.length > 0) {
        if (!props.initForm) return;
        state.selectedKeys = [state.treeData[0]?.id];
        state.nodeData = state.treeData[0];
      } else {
        state.selectedKeys = [];
      }
    }
    async function initFormData() {
      let res = await props.getTreeApi({ searchText: state.searchValue });
      state.treeData = res;
    }
    function findParent(id, data) {
      for (let i = 0; i < data.length; i++) {
        let item = data[i];
        if (Array.isArray(item.children) && item.children.some((item1) => item1.id === id)) {
          return item;
        }
        if (Array.isArray(item.children) && item.children.length > 0) {
          let itemData = findParent(id, item.children);
          if (itemData) {
            return itemData;
          }
        }
      }
    }
    watch(
      () => state.selectedKeys,
      (val) => {
        emit('selectNode', val[0]);
      },
    );
    watch(
      () => state.treeData,
      (val) => {
        if (state.searchValue) return;
        emit('initData', val);
      },
    );
    const updateData = (data) => {
      if (data.type === 'add') {
        if (JSON.stringify(state.nodeData) === '{}') {
          state.treeData.push(data.data);
        } else if (Array.isArray(state.nodeData.children)) {
          state.nodeData.children.push(data.data);
        } else {
          state.nodeData.children = [data.data];
        }
        if (JSON.stringify(state.nodeData) !== '{}') {
          state.expandedKeys.push(state.nodeData.id);
        }
        if (!data.checked) {
          state.selectedKeys = [data.data.id];
          state.nodeData = data.data;
        }
      } else {
        state.nodeData.name = data.data.name;
        state.nodeData.description = data.data.description;
        state.nodeData = data.data;
      }
      state.treeData = [...state.treeData];
    };
    const expandNode = (expandedKeys, { expanded: bool, node }) => {
      state.expandedKeys = expandedKeys;
    };
    return {
      ...toRefs(state),
      searchChange,
      selectNode,
      register,
      updateData,
      expandNode,
      isPower,
      powerData,
    };
  },
});
</script>
<style lang="less" scoped>
.figureWorkTree{
  height: 100%;
  box-sizing: border-box;
  padding:0px ~`getPrefixVar('content-margin-left')` ~`getPrefixVar('content-margin-top')` ~`getPrefixVar('content-margin-left')`;
  .treeNode {
    display: flex;
    align-items: center;
  }
  :deep(.ant-tree-list-holder-inner) {
    display: block!important;
  }
  .figureWorkTree_title{
    padding: 15px 0px;
    display: flex;
    justify-content: space-between;
    .left{
      .folderTitle{
        color: ~`getPrefixVar('primary-4')`;
        vertical-align: bottom !important;
        font-size: 30px;
        padding-right: 4px;
      }
      .figureWorkTree_title_span{
        font-weight: 500;
        font-style: normal;
        font-size: 16px;
        color: #444B5E;
        vertical-align: middle !important;
      }
    }

    .rightClick{
      font-size: 20px;
      margin-top: 3px;
      color: #444B5E;
      cursor: pointer;
    }
  }
  :deep(.ant-tree-treenode){
    //width:100%;
    padding: 5px 0px;
    position: relative;
    display: flex;
    .ant-tree-switcher{
      height:30px;
      line-height:30px;
    }
    .ant-tree-node-content-wrapper{
      flex: 1;
      width: 1px;
      &:hover{
        background: none;
      }
    }
    &:hover{
      .operation{
        display: block;
      }
      .treeNode{
        .titleSpan{
          color:~`getPrefixVar('primary-color')`;
        }
      }
    }
  }
  :deep(.ant-tree-treenode-selected){
    background:~`getPrefixVar('primary-color-deprecated-f-12')`;
    .ant-tree-node-selected{
      background: none;
    }
    .operation{
      display: block;
    }
    .treeNode{
      .titleSpan{
        color:~`getPrefixVar('primary-color')`;
      }
    }
  }
  .treeNode{
    .folderTitle{
      color: #ffca29;
      font-size: 18px;
      padding-right: 5px;
      vertical-align: middle !important;
    }
    .titleSpan{
      color:#444B5E;
      font-size: 14px;
      height:30px;
      line-height:30px;
      vertical-align: middle !important;
      width: 250px;
      display: inline-block;
    }
  }
  .operation{
    display: none;
    position: absolute;
    top: 5px;
    right: 5px;
    font-size: 18px;
    color: #444B5E;
    z-index: 1000;
    .anticon{
      margin-left: 5px;
      cursor: pointer;
    }

  }
  .nodeType{
    color: red;
  }
  .treeContent{
    height: calc(100% - 100px);
    overflow-y: auto;
  }
  .recycle{
    border-top: 1px solid #dddddd;
    padding: 10px 0px;
    .recycleLabel{
      font-size: 0px;
      cursor: pointer;
      padding: 5px 10px;
      .frecycleLabelTitle{
        color: #85bcff;
        vertical-align: middle !important;
        font-size: 30px;
        padding-right: 4px;
      }
      .recycleLabel_span{
        font-weight: 500;
        font-style: normal;
        font-size: 14px;
        color: #444B5E;
        vertical-align: middle !important;
      }
      &:hover{
        background:~`getPrefixVar('primary-color-deprecated-f-12')`;
        .recycleLabel_span{
          color:~`getPrefixVar('primary-color')`;
        }
      }
    }
  }
}

.treeDropDown{
  .aMenuItem{
    display: block;
    width: 80px;
    color: #444B5E;
  }
  .anticon{
    font-size: 16px;
    padding-right: 5px;
  }
}
</style>
