package com.chinasie.orion.controller;

import com.chinasie.orion.domain.vo.ProjectContractChangeFormVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.service.ProjectContractChangeFormService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * ProjectContractChangeForm 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26 15:37:42
 */
@RestController
@RequestMapping("/projectContractChangeForm")
@Api(tags = "项目合同变更表单")
public class ProjectContractChangeFormController {

    @Autowired
    private ProjectContractChangeFormService projectContractChangeFormService;

    /**
     * 列表
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "列表")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @LogRecord(
            success = "【{USER{#logUserId}}】获取项目合同变更表单列表",
            type = "ProjectContractChangeForm",
            subType = "列表查询",
            bizNo = ""  // 列表查询无具体业务实体编号
    )
    public ResponseDTO<List<ProjectContractChangeFormVO>> allList() throws Exception {
        List<ProjectContractChangeFormVO> rsp = projectContractChangeFormService.allList();
        return new ResponseDTO<>(rsp);
    }
}
