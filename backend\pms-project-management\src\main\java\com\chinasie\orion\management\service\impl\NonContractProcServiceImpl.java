package com.chinasie.orion.management.service.impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.management.domain.dto.NonContractProcDTO;
import com.chinasie.orion.management.domain.entity.NonContractProc;
import com.chinasie.orion.management.domain.vo.NonContractProcVO;
import com.chinasie.orion.management.repository.NonContractProcMapper;
import com.chinasie.orion.management.service.NonContractProcService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * NonContractProc 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12 11:04:24
 */
@Service
@Slf4j
public class NonContractProcServiceImpl extends OrionBaseServiceImpl<NonContractProcMapper, NonContractProc> implements NonContractProcService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public NonContractProcVO detail(String id, String pageCode) throws Exception {
        NonContractProc nonContractProc = this.getById(id);
        NonContractProcVO result = BeanCopyUtils.convertTo(nonContractProc, NonContractProcVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param nonContractProcDTO
     */
    @Override
    public String create(NonContractProcDTO nonContractProcDTO) throws Exception {
        NonContractProc nonContractProc = BeanCopyUtils.convertTo(nonContractProcDTO, NonContractProc::new);
        this.save(nonContractProc);

        String rsp = nonContractProc.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param nonContractProcDTO
     */
    @Override
    public Boolean edit(NonContractProcDTO nonContractProcDTO) throws Exception {
        NonContractProc nonContractProc = BeanCopyUtils.convertTo(nonContractProcDTO, NonContractProc::new);

        this.updateById(nonContractProc);

        String rsp = nonContractProc.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<NonContractProcVO> pages(Page<NonContractProcDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<NonContractProc> condition = new LambdaQueryWrapperX<>(NonContractProc.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(NonContractProc::getCreateTime);


        Page<NonContractProc> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), NonContractProc::new));

        PageResult<NonContractProc> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<NonContractProcVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<NonContractProcVO> vos = BeanCopyUtils.convertListTo(page.getContent(), NonContractProcVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "无合同采购表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", NonContractProcDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        NonContractProcExcelListener excelReadListener = new NonContractProcExcelListener();
        EasyExcel.read(inputStream, NonContractProcDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<NonContractProcDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("无合同采购表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<NonContractProc> nonContractProces = BeanCopyUtils.convertListTo(dtoS, NonContractProc::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pms::NonContractProc-import::id", importId, nonContractProces, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<NonContractProc> nonContractProces = (List<NonContractProc>) orionJ2CacheService.get("pms::NonContractProc-import::id", importId);
        log.info("无合同采购表导入的入库数据={}", JSONUtil.toJsonStr(nonContractProces));

        this.saveBatch(nonContractProces);
        orionJ2CacheService.delete("pms::NonContractProc-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pms::NonContractProc-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(Page<NonContractProcDTO> pageRequest, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<NonContractProc> condition = new LambdaQueryWrapperX<>(NonContractProc.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(NonContractProc::getCreateTime);
        List<NonContractProc> nonContractProces = this.list(condition);

        List<NonContractProcDTO> dtos = BeanCopyUtils.convertListTo(nonContractProces, NonContractProcDTO::new);
        //是否内部交易转化
        for (NonContractProcDTO dto : dtos) {
            Boolean isInternalTx = dto.getIsInternalTx();
            if (isInternalTx.booleanValue()){
                dto.setIsInternalTxName("是");
            }else {
                dto.setIsInternalTxName("否");
            }
            if("3".equals(dto.getProcessStatus())){
                dto.setProcessStatus("流程审批完成");
            }
        }

        String fileName = "无合同采购表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", NonContractProcDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<NonContractProcVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }

    @Override
    public Map<String, Object> getNumMoney(Page<NonContractProcDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<NonContractProc> condition = new LambdaQueryWrapperX<>(NonContractProc.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(NonContractProc::getCreateTime);

        String sql = " count(*) as total," +
                "sum(reimbursed_amount) as allMoney";
        condition.select(sql);
        Map map = this.getMap(condition);
        return map;
    }


    public static class NonContractProcExcelListener extends AnalysisEventListener<NonContractProcDTO> {

        private final List<NonContractProcDTO> data = new ArrayList<>();

        @Override
        public void invoke(NonContractProcDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<NonContractProcDTO> getData() {
            return data;
        }
    }


}
