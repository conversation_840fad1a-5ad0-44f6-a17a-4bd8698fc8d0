import { roleListApi } from '/@/views/pms/projectLaborer/api/riskManege';
import {
  demandSimplePageApi,
  demandSourceApi,
  demandTypeApi,
  // 优先级
  priorityLevelApi,
  addDemandApi,
  editDemandApi,
  itemDetailsApi,
  getDemandStatusApi,
} from '/@/views/pms/projectLaborer/api/demandManagement';
export const addotherApi = {
  zkEdit: editDemandApi,
  zkAdd: addDemandApi,
  zkPeopel: roleListApi,
  zkItemDetails: itemDetailsApi,
  //   zkForType: demandSimplePageApi
};
export const addform = [
  {
    type: 'treeSelect',
    label: '所属需求',
    field: 'parentId',
    optionsValue: [],
    getOptionFn: demandSimplePageApi,
    rules: [
      {
        required: true,
        message: '请选择所属需求',
        trigger: 'blur',
      },
    ],
    apiConfig: {
      size: 'large',
      placeholder: '请选择所属需求',
    },
  },
  {
    type: 'input',
    label: '标题',
    field: 'name',
    rules: [
      {
        required: true,
        message: '请输入标题',
        trigger: 'blur',
      },
    ],
    apiConfig: {
      size: 'large',
      placeholder: '请输入标题',
    },
  },
  {
    type: 'selectSearchPeople',
    label: '负责人',
    field: 'principalId',
    radioOptions: [],
    rules: [
      {
        required: true,
        message: '请选择负责人',
        trigger: 'blur',
      },
    ],
    apiConfig: {
      placeholder: '请选择负责人',

      size: 'large',
    },
  },
  {
    type: 'textarea',
    label: '内容',
    field: 'remark',
    apiConfig: {
      placeholder: '请输入内容',
      maxlength: 255,
      rows: 4,
      size: 'large',
    },
  },

  {
    type: 'select',
    label: '需求来源',
    field: 'source',
    options: [],
    getOptionFn: demandSourceApi,
    apiConfig: {
      placeholder: '请选择需求来源',
      size: 'large',
    },
  },
  {
    type: 'select',
    label: '需求类型',
    field: 'type',

    getOptionFn: demandTypeApi,
    options: [],
    apiConfig: {
      size: 'large',
      placeholder: '请选择需求类型',
    },
  },
  {
    type: 'input',
    label: '提出人',
    field: 'exhibitor',
    apiConfig: {
      size: 'large',
      placeholder: '请输入提出人',
    },
  },
  {
    type: 'dataPicker',
    label: '提出时间',
    field: 'proposedTime',
    format: true,

    apiConfig: {
      placeholder: '请选择提出时间',

      size: 'large',
    },
  },
  {
    type: 'dataPicker',
    label: '期望完成时间',
    field: 'predictEndTime',

    format: true,
    apiConfig: {
      placeholder: '请选择期望完成时间',

      size: 'large',
    },
  },
  {
    type: 'selectSearchPeople',
    label: '接收人',
    field: 'recipientId',

    radioOptions: [],
    apiConfig: {
      placeholder: '请选择接收人',

      size: 'large',
    },
  },

  {
    type: 'select',
    label: '优先级',
    field: 'priorityLevel',
    getOptionFn: priorityLevelApi,
    options: [],

    apiConfig: {
      placeholder: '请选择优先级',

      size: 'large',
    },
  },
  {
    type: 'inputIcon',
    label: '进度',
    field: 'schedule',

    apiConfig: {
      placeholder: '请输入进度',
      size: 'large',
      suffix: '%',
    },
  },
];
