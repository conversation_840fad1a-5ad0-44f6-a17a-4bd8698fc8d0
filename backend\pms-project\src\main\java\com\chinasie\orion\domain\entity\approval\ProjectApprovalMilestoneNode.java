package com.chinasie.orion.domain.entity.approval;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.tree.OrionTreeNode;


import lombok.Data;

import java.io.Serializable;
import java.lang.Boolean;
import java.lang.String;
import java.lang.Integer;

/**
 * ProjectApprovalMilestoneNode Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-27 17:39:59
 */
@TableName(value = "pmsx_project_approval_milestone_node")
@ApiModel(value = "ProjectApprovalMilestoneNodeEntity对象", description = "项目立项里程碑节点")
@Data

public class ProjectApprovalMilestoneNode extends  OrionTreeNode  implements Serializable{

    /**
     * 模板id
     */
    @ApiModelProperty(value = "模板id")
    @TableField(value = "template_id")
    private String templateId;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @TableField(value = "description")
    private String description;

    /**
     * 父级链
     */
    @ApiModelProperty(value = "父级链")
    @TableField(value = "node_chain")
    private String nodeChain;

    /**
     * 节点类型
     */
    @ApiModelProperty(value = "节点类型")
    @TableField(value = "node_type")
    private String nodeType;

    /**
     * 责任部门id
     */
    @ApiModelProperty(value = "责任部门id")
    @TableField(value = "rsp_dept_id")
    private String rspDeptId;

    /**
     * 计划工期
     */
    @ApiModelProperty(value = "计划工期")
    @TableField(value = "duration_days")
    private Integer durationDays;

    /**
     * 项目启动后n天开始
     */
    @ApiModelProperty(value = "项目启动后n天开始")
    @TableField(value = "delay_days")
    private Integer delayDays;

    /**
     * 是否关联流程
     */
    @ApiModelProperty(value = "是否关联流程")
    @TableField(value = "process_flag")
    private Boolean processFlag;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

}
