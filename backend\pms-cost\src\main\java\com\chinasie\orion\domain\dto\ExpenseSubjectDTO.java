package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;

/**
 * ExpenseSubject Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-16 15:15:30
 */
@ApiModel(value = "ExpenseSubjectDTO对象", description = "费用科目类")
@Data
public class ExpenseSubjectDTO extends ObjectDTO implements Serializable {

    /**
     * 费用科目名称
     */
    @ApiModelProperty(value = "费用科目名称")
    private String name;

    /**
     * 父级目录
     */
    @ApiModelProperty(value = "父级目录")
    private String parentId;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    /**
     * 排序值
     */
    @ApiModelProperty(value = "排序值")
    private Long sort;

}
