package com.chinasie.orion.management.service.impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.management.domain.dto.NcfFormSupplierReviewDTO;
import com.chinasie.orion.management.domain.dto.NcfFormpurchaseRequestDTO;
import com.chinasie.orion.management.domain.entity.NcfFormSupplierReview;
import com.chinasie.orion.management.domain.entity.SupplierInfo;
import com.chinasie.orion.management.domain.vo.NcfFormSupplierReviewVO;
import com.chinasie.orion.management.repository.NcfFormSupplierReviewMapper;
import com.chinasie.orion.management.service.NcfFormSupplierReviewService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <p>
 * NcfFormSupplierReview 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 14:26:38
 */
@Service
@Slf4j
public class NcfFormSupplierReviewServiceImpl extends OrionBaseServiceImpl<NcfFormSupplierReviewMapper, NcfFormSupplierReview> implements NcfFormSupplierReviewService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public NcfFormSupplierReviewVO detail(String id, String pageCode) throws Exception {
        NcfFormSupplierReview ncfFormSupplierReview = this.getById(id);
        NcfFormSupplierReviewVO result = BeanCopyUtils.convertTo(ncfFormSupplierReview, NcfFormSupplierReviewVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param ncfFormSupplierReviewDTO
     */
    @Override
    public String create(NcfFormSupplierReviewDTO ncfFormSupplierReviewDTO) throws Exception {
        NcfFormSupplierReview ncfFormSupplierReview = BeanCopyUtils.convertTo(ncfFormSupplierReviewDTO, NcfFormSupplierReview::new);
        this.save(ncfFormSupplierReview);

        String rsp = ncfFormSupplierReview.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param ncfFormSupplierReviewDTO
     */
    @Override
    public Boolean edit(NcfFormSupplierReviewDTO ncfFormSupplierReviewDTO) throws Exception {
        NcfFormSupplierReview ncfFormSupplierReview = BeanCopyUtils.convertTo(ncfFormSupplierReviewDTO, NcfFormSupplierReview::new);

        this.updateById(ncfFormSupplierReview);

        String rsp = ncfFormSupplierReview.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }

    @Override
    public void updateStatus(List<NcfFormSupplierReviewDTO> dtos) {

        Collection<String> ids = dtos.stream().map(NcfFormSupplierReviewDTO::getId).collect(Collectors.toList());;
        List<NcfFormSupplierReview> list = this.listByIds(ids);
        list.forEach(item -> item.setStatus(dtos.get(0).getStatus()));
        this.updateBatchById(list);
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<NcfFormSupplierReviewVO> pages(Page<NcfFormSupplierReviewDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<NcfFormSupplierReview> condition = new LambdaQueryWrapperX<>(NcfFormSupplierReview.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(NcfFormSupplierReview::getCreateTime);


        Page<NcfFormSupplierReview> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), NcfFormSupplierReview::new));

        PageResult<NcfFormSupplierReview> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<NcfFormSupplierReviewVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<NcfFormSupplierReviewVO> vos = BeanCopyUtils.convertListTo(page.getContent(), NcfFormSupplierReviewVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "资审供应商信息表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", NcfFormSupplierReviewDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        NcfFormSupplierReviewExcelListener excelReadListener = new NcfFormSupplierReviewExcelListener();
        EasyExcel.read(inputStream, NcfFormSupplierReviewDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<NcfFormSupplierReviewDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("资审供应商信息表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<NcfFormSupplierReview> ncfFormSupplierReviewes = BeanCopyUtils.convertListTo(dtoS, NcfFormSupplierReview::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("ncf::NcfFormSupplierReview-import::id", importId, ncfFormSupplierReviewes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<NcfFormSupplierReview> ncfFormSupplierReviewes = (List<NcfFormSupplierReview>) orionJ2CacheService.get("ncf::NcfFormSupplierReview-import::id", importId);
        log.info("资审供应商信息表导入的入库数据={}", JSONUtil.toJsonStr(ncfFormSupplierReviewes));

        this.saveBatch(ncfFormSupplierReviewes);
        orionJ2CacheService.delete("ncf::NcfFormSupplierReview-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("ncf::NcfFormSupplierReview-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(Page<NcfFormpurchaseRequestDTO> pageRequest, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<NcfFormSupplierReview> condition = new LambdaQueryWrapperX<>(NcfFormSupplierReview.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(NcfFormSupplierReview::getCreateTime);
        List<NcfFormSupplierReview> ncfFormSupplierReviewes = this.list(condition);

        List<NcfFormSupplierReviewDTO> dtos = BeanCopyUtils.convertListTo(ncfFormSupplierReviewes, NcfFormSupplierReviewDTO::new);

        String fileName = "资审供应商信息表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", NcfFormSupplierReviewDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<NcfFormSupplierReviewVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }

    @Override
    public Map getListByIds(List<String> ids) {
        LambdaQueryWrapperX<NcfFormSupplierReview> condition = new LambdaQueryWrapperX<>(NcfFormSupplierReview.class);

        condition.like(NcfFormSupplierReview::getApprovalCompletionTime, LocalDate.now().getYear());
        condition.in(NcfFormSupplierReview::getContractId,ids);
        condition.orderByDesc(NcfFormSupplierReview::getCreateTime);
        String sql = " count(distinct (contract_id)) as yearNum";
        condition.select(sql);
        Map map = this.getMap(condition);
        return map;
    }


    public static class NcfFormSupplierReviewExcelListener extends AnalysisEventListener<NcfFormSupplierReviewDTO> {

        private final List<NcfFormSupplierReviewDTO> data = new ArrayList<>();

        @Override
        public void invoke(NcfFormSupplierReviewDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<NcfFormSupplierReviewDTO> getData() {
            return data;
        }
    }


}
