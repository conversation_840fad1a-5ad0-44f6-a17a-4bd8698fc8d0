package com.chinasie.orion.feign.vo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PageVO<T> implements Serializable {

    /**
     * 查询数据列表
     */
    protected List<T> content = Collections.emptyList();

    @ApiModelProperty("页数")
    protected long  pageNo=0;
    @ApiModelProperty("页数")
    protected long  current=0;
    @ApiModelProperty("每页条数")
    protected long  pageSize;
    @ApiModelProperty("页数")
    protected long pageNum;
    @ApiModelProperty("总数")
    protected long totalSize;
    @ApiModelProperty("总页数")
    protected long totalPages;

}
