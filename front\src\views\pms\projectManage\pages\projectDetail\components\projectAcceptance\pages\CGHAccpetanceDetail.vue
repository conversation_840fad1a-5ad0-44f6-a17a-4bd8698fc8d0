<template>
  <Layout3
    :projectData="dataSource"
    :type="2"
    :menuData="menuData"
    :defaultActionId="tabsId"
    :onMenuChange="menuChange"
  >
    <template #header-info>
      <div class="head-info flex">
        <span class="names">
          {{ dataSource['creatorName'] }}
        </span>
        <DataStatusTag :statusData="dataSource?.dataStatus" />
      </div>
    </template>
    <template #code>
      <div class="sub-text">
        编码：{{ dataSource?.number }}
      </div>
    </template>
    <template #name>
      <div class="name">
        {{ prefix === 'CGYS' ?'采购计划验收单' :prefix==='XMYS'? '项目验收单':'' }}
      </div>
    </template>
    <template
      #header-right
    >
      <BasicButton
        v-if="enables && isPower(`${prefix}_container_button_01`, powerData)"
        type="primary"
        icon="sie-icon-chakantuisong"
        @click="() => completeAccept(id)"
      >
        验收完成
      </BasicButton>
    </template>

    <Layout3Content>
      <!--预算管理-->
      <AcceptanceInformation
        v-if="tabsId === 'acceptanceInformation' && isPower(`${prefix}_container_02`, powerData)"
        :id="id"
        :projectId="projectId"
        :dataSource="dataSource"
      />
      <!--采购计划-->
      <AcceptancePlan
        v-if="tabsId === 'acceptancePlan' && isPower(`${prefix}_container_03`, powerData)"
        :id="id"
        :projectData="dataSource"
        :from="from"
        :projectId="projectId"
      />
      <!--采购立项-->
      <AcceptanceFiLes
        v-if="tabsId === 'acceptanceFiLes' && isPower(`${prefix}_container_04`, powerData)"
        :id="id"
        :projectId="projectId"
      />
    </Layout3Content>
  </Layout3>
</template>

<script setup lang="ts">
import {
  BasicButton, DataStatusTag, isPower, Layout3, Layout3Content,
} from 'lyra-component-vue3';
import {
  getCurrentInstance, onMounted, provide, ref, Ref,
} from 'vue';
import { useRoute } from 'vue-router';
import { Modal } from 'ant-design-vue';
import { getAcceptanceForm, putAcceptanceStatus } from '/@/views/pms/api';
import dayjs from 'dayjs';
import AcceptanceInformation from '../components/AcceptanceInformation.vue';
import AcceptanceFiLes from '../components/AcceptanceFiLes.vue';
import AcceptancePlan from '../components/AcceptancePlan.vue';
import { renderNotAuthPage } from '/@/views/pms/utils';
import { setTitleByRootTabsKey } from '/@/utils';
const route = useRoute();
const rootTabsKey = route.query?.rootTabsKey as string;
const from = ref<string>('accpet');
const tabsId: Ref<string> = ref(route.query?.tabsId?.toString() ?? 'acceptanceInformation');
const menuData = ref([]);
const projectId: string = route.query?.projectId?.toString() ?? '';
// 验收单ID
const id: string = route.query?.id?.toString() ?? '';
const prefix = route.name === 'CGHAccpetanceDetail' ? 'CGYS' : route.name === 'ProjectAccpetanceDetail' ? 'XMYS' : '';
provide('prefix', prefix);
const enables = ref<boolean>(false);

onMounted(() => {
  getData();
});
// 获取当前菜单id
function menuChange({ id }) {
  tabsId.value = id;
}

// 验收完成
async function completeAccept(id) {
  Modal.confirm({
    title: '验收完成确认',
    content: '请确认验收是否已完成？',
    onOk() {
      handlePutAcceptanceStatus(id);
    },
  });
}

async function handlePutAcceptanceStatus(id) {
  try {
    const result = await putAcceptanceStatus(id);
    if (result == null) {
      await getData();
    } else {
    }
  } catch (error) {}
}
const dataSource = ref<any>({});

const pageInstance = getCurrentInstance();
const powerData = ref([]);
provide('powerData', powerData);
// 获取验收详情
async function getData() {
  dataSource.value = await getAcceptanceForm(
    id,
    {
      pageCode: prefix === 'CGYS' ? 'PMS-cghAccpetanceDetail' : prefix === 'XMYS' ? 'PMS-ProjectAccpetanceDetail' : '',
    },
  );
  setTitleByRootTabsKey(rootTabsKey, prefix === 'CGYS' ? '采购计划验收单' : prefix === 'XMYS' ? '项目验收单' : '');
  powerData.value = dataSource.value?.detailAuthList ?? [];
  renderNotAuthPage({
    vm: pageInstance,
    powerData: powerData.value,
  });
  menuData.value = [
    (isPower(`${prefix}_container_02`, powerData.value) ? {
      name: '验收信息',
      id: 'acceptanceInformation',
    } : undefined),
    (isPower(`${prefix}_container_03`, powerData.value) ? {
      name: '验收计划',
      id: 'acceptancePlan',
    } : undefined),
    (isPower(`${prefix}_container_04`, powerData.value) ? {
      name: '验收文件',
      id: 'acceptanceFiLes',
    } : undefined),
  ].filter((item) => item);
  if (dataSource.value != null) {
    dataSource.value.projectBeginTime = dataSource.value.projectBeginTime ? dayjs(dataSource.value.projectBeginTime).format('YYYY-MM-DD') : '';
    dataSource.value.projectEndTime = dataSource.value.projectEndTime ? dayjs(dataSource.value.projectEndTime).format('YYYY-MM-DD') : '';
    dataSource.value.createTime = dataSource.value.createTime ? dayjs(dataSource.value.createTime).format('YYYY-MM-DD') : '';
    enables.value = dataSource.value.enables.acceptForm;
  } else {

  }
}
</script>

<style scoped lang="less">
:deep(.project-title)  {
  width: 360px !important;
}
.header-wrap {
  line-height: 1;
  height: 80px;
  .name {
    font-size: 18px;
    color: ~`getPrefixVar('sider-dark-bg-color') `;
    font-weight:700;
  }

  .sub-text {
    font-size: 14px;
    color: ~`getPrefixVar('sider-dark-bg-color') `;
  }
  .head-info {
    font-size: 18px;
    color: ~`getPrefixVar('sider-dark-bg-color') `;
        font-weight:700;
        .names {
          margin-right:15px;
        }
  }
}
</style>
