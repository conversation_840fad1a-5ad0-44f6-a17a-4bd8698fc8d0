package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;

/**
 * ProjectInternalAssociation Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-16 14:25:49
 */
@TableName(value = "pmsx_project_internal_association")
@ApiModel(value = "ProjectInternalAssociation对象", description = "项目内部关联表")
@Data
public class ProjectInternalAssociation extends ObjectEntity implements Serializable{

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField(value = "name" )
    private String name;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    @TableField(value = "number" )
    private String number;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @TableField(value = "sort" )
    private Long sort;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id" )
    private String projectId;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    @TableField(value = "project_name" )
    private String projectName;

    /**
     * 内部类型
     */
    @ApiModelProperty(value = "内部类型")
    @TableField(value = "type" )
    private String type;

    /**
     * 内部数据id
     */
    @ApiModelProperty(value = "内部数据id")
    @TableField(value = "inner_id" )
    private String innerId;

    /**
     * 内部名称
     */
    @ApiModelProperty(value = "内部名称")
    @TableField(value = "inner_name" )
    private String innerName;

}
