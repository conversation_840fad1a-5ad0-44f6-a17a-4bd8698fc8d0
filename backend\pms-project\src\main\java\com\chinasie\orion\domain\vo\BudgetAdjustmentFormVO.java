package com.chinasie.orion.domain.vo;

import com.chinasie.orion.domain.dto.BudgetAdjustmentDTO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;
import java.lang.String;
import java.lang.Integer;

import java.util.List;

/**
 * BudgetAdjustmentFrom VO对象
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:07
 */
@ApiModel(value = "BudgetAdjustmentFromVO对象", description = "预算调整表")
@Data
public class BudgetAdjustmentFormVO extends ObjectVO implements Serializable {


    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    private String projectId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目名称")
    private String projectNumber;


    /**
     * 预算调整名称
     */
    @ApiModelProperty(value = "预算调整名称")
    private String name;


    /**
     * 预算调整编码
     */
    @ApiModelProperty(value = "预算调整编码")
    private String number;


    /**
     * 调整金额
     */
    @ApiModelProperty(value = "调整金额")
    private BigDecimal adjustmentMoney;


    /**
     * 调整预算条目
     */
    @ApiModelProperty(value = "调整预算条目")
    private Integer adjustmentNum;

    @ApiModelProperty(value = "调整预算数据")
    private List<BudgetAdjustmentVO> budgetAdjustmentVOS;

}
