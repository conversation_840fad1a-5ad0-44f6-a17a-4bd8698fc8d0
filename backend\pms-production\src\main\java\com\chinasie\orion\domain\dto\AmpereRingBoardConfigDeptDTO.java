package com.chinasie.orion.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NonNull;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 21 日
 **/
@ApiModel(value = "AmpereRingBoardConfigDeptDto对象", description = "安质环部门维护dto对象")
@Data
public class AmpereRingBoardConfigDeptDTO extends ObjectDTO implements Serializable {
    /**
     * 操作的ids
     */
    @ApiModelProperty(value = "操作的ids")
    private List<String> ids;

    /**
     * 移动隐患排查的部门展示的顺序
     */
    @ApiModelProperty(value = "移动隐患排查的部门序号的：上移 up;下移 down;置顶 top ;置底 bottom")
    @NotEmpty(message = "operationType 不能为空")
    private String operationType;

    /**
     * 部门绩效考核是否展示
     */
    @ApiModelProperty(value = "部门绩效考核是否展示")
    private Boolean isDeptScoreShow;

    /**
     * 隐患排查是否展示
     */
    @ApiModelProperty(value = "隐患排查是否展示")
    private Boolean isCheckProblemsShow;

    /**
     * kpi得分
     */
    @ApiModelProperty(value = "kpi得分")
    private String kpiScore;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /**
     * 部门code
     */
    @ApiModelProperty(value = "部门code")
    private String deptCode;

    /**
     * 部门编码
     */
    @ApiModelProperty(value = "部门编码")
    private String deptNumber;





    /**
     * 维护类型
     */
    @ApiModelProperty(value = "维护类型")
    private String configType;

    /**
     * 隐患排除的部门序号
     */
    @ApiModelProperty(value = "隐患排查的部门序号")
    private Integer checkProblemsSort;

    /**
     * 标准得分
     */
    @ApiModelProperty(value = "部门标准分")
    private Double standardScore;

    /**
     * 事件分类
     */
    @ApiModelProperty(value = "事件分类")
    private String parentName;

    /**
     * 分类code
     */
    @ApiModelProperty(value = "分类code")
    private String parentId;


}
