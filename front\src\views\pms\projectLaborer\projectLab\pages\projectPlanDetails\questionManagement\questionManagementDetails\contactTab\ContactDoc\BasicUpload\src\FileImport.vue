<template>
  <div class="flex flex-ac">
    <div
      v-if="fileList.length"
      class="mr10"
    >
      {{ fileList[0].name }}
    </div>
    <AButtonGroup>
      <Upload
        v-bind="$attrs"
        :before-upload="beforeUpload"
        :showUploadList="false"
        :multiple="multiple"
        :accept="accept"
        :disabled="loadingStatus"
        class="upload-btn-wrap"
      >
        <AButton
          :type="buttonType"
          :loading="loadingStatus"
        >
          <Icon
            v-if="!loadingStatus"
            icon="fa-upload"
            :color="buttonType !== 'default' ? '#fff' : ''"
          />
          <span>
            {{ fileList.length ? (loadingStatus ? '上传中' : '重新选择') : '选择文件' }}
          </span>
        </AButton>

        <div
          v-if="fileList.length && loadingStatus"
          class="load-progress"
          :style="{ width: (uploadProgress || 0) + '%' }"
        />
      </Upload>
      <AButton
        v-if="!loadingStatus && fileList.length"
        @click="startUpload"
      >
        <Icon icon="fa-play-circle-o" />
        <span> 开始导入 </span>
      </AButton>
      <AButton
        v-if="loadingStatus"
        type="primary"
        @click="allCancel"
      >
        取消
      </AButton>
    </AButtonGroup>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
import {
  Button, message, Upload, Modal,
} from 'ant-design-vue';
import Icon from '/@/components/Icon';
import { UploadResultStatus } from './enum';
import { defHttp } from '/@/utils/http/axios';
import Axios from 'axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

export default defineComponent({
  name: 'FileInput',
  components: {
    AButton: Button,
    AButtonGroup: Button.Group,
    Upload,
    Icon,
  },
  props: {
    buttonType: {
      type: String,
      default: 'primary',
    },
    action: {
      type: String,
      default: '',
    },
    name: {
      type: String,
      default: 'file',
    },
    data: {
      type: Object,
      default: () => {},
    },
    isBeforeRequest: {
      type: Boolean,
      default: true,
    },
  },
  emits: ['fileImportChange'],
  setup(props, { emit }) {
    const state = reactive({
      loadingStatus: false,
      fileList: [],
      uploadProgress: 0,
      source: null,
      uploadStatus: undefined,
    });

    function startUpload() {
      const file = state.fileList[0] || null;
      if (!file) {
        return;
      }
      let cancelToken = Axios.CancelToken;
      state.source = cancelToken.source();
      state.loadingStatus = true;
      defHttp
        .fileInput(
          {
            url: (props.isBeforeRequest ? import.meta.env?.VITE_GLOB_API_URL : '') + props.action,
            timeout: 1000 * 60 * 60,
            cancelToken: state.source.token,
            onUploadProgress(progressEvent: any) {
              state.uploadProgress = ((progressEvent.loaded / progressEvent.total) * 100).toFixed(2) - 0;
            },
          },
          file,
          {
            data: props.data,
            fileName: props.name,
          },
        )
        .then((res) => {
          const { data } = res;
          if (data?.code === 200) {
            message.success('导入成功');
            emit('fileImportChange', {
              type: 'success',
              data,
            });
            state.uploadStatus = 'success';
          } else {
            emit('fileImportChange', { type: 'error' });
            message.error(data?.message);
          }
        })
        .catch(() => {
          state.uploadStatus = 'error';
          emit('fileImportChange', { type: 'error' });
        })
        .finally(() => {
          state.loadingStatus = false;
        });
    }

    return {
      ...toRefs(state),
      beforeUpload(file) {
        state.uploadStatus = undefined;
        state.fileList = [file];
        return false;
      },
      startUpload,
      allCancel() {
        state.source && state.source.cancel();
      },
    };
  },
});
</script>

<style scoped lang="less">
  .upload-btn-wrap {
    position: relative;

    .load-progress {
      position: absolute;
      height: 100%;
      left: 0;
      width: 20%;
      background-color: rgba(255, 255, 255, 0.2);
      top: 0;
    }
  }
</style>
