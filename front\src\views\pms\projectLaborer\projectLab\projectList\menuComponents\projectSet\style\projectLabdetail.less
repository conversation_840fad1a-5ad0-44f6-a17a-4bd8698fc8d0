// .productLibraryDetails {
/* 去掉冒号 */
:deep(.ant-form) {
  .ant-form-item {
    .ant-col {
      label {
        &::after {
          content: '';
        }
      }
    }
  }
}
.productLibraryDetails_content5920 {
  display: flex;
  justify-content: space-between;
  background: #ffffff;
  padding-left: 16px;
  .productLibraryDetails_left,
  .productLibraryDetails_right {
    padding-top: 10px;
    width: calc(~'50% - 10px');
  }
  .detailsImg {
    height: 400px;
    width: 100%;
    img {
      width: 100%;
      height: 80%;
      padding-bottom: 10px;
    }
  }
  .productLibraryDetails_right {
    padding-left: 16px;
  }
  .productLibraryDetails_left_title,
  .productLibraryDetails_right_title {
    height: 40px;
    background-color: #f0f2f5;
    line-height: 40px;
    color: #444b5e;
    padding-left: 16px;
    margin-bottom: 10px;
    .anticon-caret-down {
      color: #969eb4;
      cursor: pointer;
    }
    span {
      padding-left: 6px;
    }
  }
  .ant-form-item-label {
    text-align: left;
  }
  .coverImg {
    width: 100px;
    height: 50px;
    border-radius: 5px;
  }
}
.productLibraryDetails_btn {
  background: #ffffff;
}
.descriptionStyle {
  word-break: break-all;
}
