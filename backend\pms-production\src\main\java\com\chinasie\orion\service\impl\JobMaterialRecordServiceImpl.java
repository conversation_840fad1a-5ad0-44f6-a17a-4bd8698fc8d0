package com.chinasie.orion.service.impl;





import com.chinasie.orion.domain.entity.JobMaterialRecord;
import com.chinasie.orion.domain.dto.JobMaterialRecordDTO;
import com.chinasie.orion.domain.entity.JobPersonRecord;
import com.chinasie.orion.domain.vo.JobMaterialRecordVO;



import com.chinasie.orion.service.JobMaterialRecordService;
import com.chinasie.orion.repository.JobMaterialRecordMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * JobMaterialRecord 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-11 17:45:37
 */
@Service
@Slf4j
public class JobMaterialRecordServiceImpl extends  OrionBaseServiceImpl<JobMaterialRecordMapper, JobMaterialRecord>   implements JobMaterialRecordService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  JobMaterialRecordVO detail(String id,String pageCode) throws Exception {
        JobMaterialRecord jobMaterialRecord =this.getById(id);
        JobMaterialRecordVO result = BeanCopyUtils.convertTo(jobMaterialRecord,JobMaterialRecordVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param jobMaterialRecordDTO
     */
    @Override
    public  String create(JobMaterialRecordDTO jobMaterialRecordDTO) throws Exception {
        JobMaterialRecord jobMaterialRecord =BeanCopyUtils.convertTo(jobMaterialRecordDTO,JobMaterialRecord::new);
        this.save(jobMaterialRecord);

        String rsp=jobMaterialRecord.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param jobMaterialRecordDTO
     */
    @Override
    public Boolean edit(JobMaterialRecordDTO jobMaterialRecordDTO) throws Exception {
        JobMaterialRecord jobMaterialRecord =BeanCopyUtils.convertTo(jobMaterialRecordDTO,JobMaterialRecord::new);

        this.updateById(jobMaterialRecord);

        String rsp=jobMaterialRecord.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<JobMaterialRecordVO> pages( Page<JobMaterialRecordDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<JobMaterialRecord> condition = new LambdaQueryWrapperX<>( JobMaterialRecord. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(JobMaterialRecord::getCreateTime);


        Page<JobMaterialRecord> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), JobMaterialRecord::new));

        PageResult<JobMaterialRecord> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<JobMaterialRecordVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<JobMaterialRecordVO> vos = BeanCopyUtils.convertListTo(page.getContent(), JobMaterialRecordVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "作业物资记录表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", JobMaterialRecordDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            JobMaterialRecordExcelListener excelReadListener = new JobMaterialRecordExcelListener();
        EasyExcel.read(inputStream,JobMaterialRecordDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<JobMaterialRecordDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("作业物资记录表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<JobMaterialRecord> jobMaterialRecordes =BeanCopyUtils.convertListTo(dtoS,JobMaterialRecord::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::JobMaterialRecord-import::id", importId, jobMaterialRecordes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<JobMaterialRecord> jobMaterialRecordes = (List<JobMaterialRecord>) orionJ2CacheService.get("pmsx::JobMaterialRecord-import::id", importId);
        log.info("作业物资记录表导入的入库数据={}", JSONUtil.toJsonStr(jobMaterialRecordes));

        this.saveBatch(jobMaterialRecordes);
        orionJ2CacheService.delete("pmsx::JobMaterialRecord-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::JobMaterialRecord-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<JobMaterialRecord> condition = new LambdaQueryWrapperX<>( JobMaterialRecord. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(JobMaterialRecord::getCreateTime);
        List<JobMaterialRecord> jobMaterialRecordes =   this.list(condition);

        List<JobMaterialRecordDTO> dtos = BeanCopyUtils.convertListTo(jobMaterialRecordes, JobMaterialRecordDTO::new);

        String fileName = "作业物资记录表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", JobMaterialRecordDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<JobMaterialRecordVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }

    @Override
    public void addOrUpdate(String jobId, String number, String jobMaterialId) {
        LambdaQueryWrapperX<JobMaterialRecord> wrapperX = new LambdaQueryWrapperX<>(JobMaterialRecord.class);
        wrapperX.eq(JobMaterialRecord::getJobId, jobId)
                .eq(JobMaterialRecord::getMateriaCode, number)
                .eq(JobMaterialRecord::getMateriaManageId, jobMaterialId);
        if (this.count(wrapperX) > 0) {
            return;
        }
        JobMaterialRecord jobPersonRecord = new JobMaterialRecord();
        jobPersonRecord.setJobId(jobId);
        jobPersonRecord.setMateriaCode(number);
        jobPersonRecord.setMateriaManageId(jobMaterialId);
        this.save(jobPersonRecord);
    }

    @Override
    public void addRelation(String jobId, String materialId, String number) {
        LambdaQueryWrapperX<JobMaterialRecord> wrapperX = new LambdaQueryWrapperX<>(JobMaterialRecord.class);
        wrapperX.eq(JobMaterialRecord::getJobId, jobId)
                .eq(JobMaterialRecord::getMateriaCode, number)
                .eq(JobMaterialRecord::getMateriaManageId, materialId);
        if (this.count(wrapperX) > 0) {
            return;
        }
        JobMaterialRecord jobPersonRecord = new JobMaterialRecord();
        jobPersonRecord.setJobId(jobId);
        jobPersonRecord.setMateriaCode(number);
        jobPersonRecord.setMateriaManageId(materialId);
        this.save(jobPersonRecord);
    }


    public static class JobMaterialRecordExcelListener extends AnalysisEventListener<JobMaterialRecordDTO> {

        private final List<JobMaterialRecordDTO> data = new ArrayList<>();

        @Override
        public void invoke(JobMaterialRecordDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<JobMaterialRecordDTO> getData() {
            return data;
        }
    }


}
