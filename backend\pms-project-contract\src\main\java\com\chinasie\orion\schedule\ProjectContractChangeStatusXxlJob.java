package com.chinasie.orion.schedule;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.constant.ProjectContractStatusEnum;
import com.chinasie.orion.domain.entity.ProjectContract;
import com.chinasie.orion.service.ProjectContractService;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;

/**
 * @author: yk
 * @date: 2023/10/28 14:42
 * @description: 更改项目合同状态为合同履行
 */
@Component
public class ProjectContractChangeStatusXxlJob {

    @Autowired
    private ProjectContractService projectContractService;


    @XxlJob("projectContractChangeStatusJobHandler")
    public void changeStatus() throws Exception {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        LambdaUpdateWrapper<ProjectContract> lambdaQueryWrapper = new LambdaUpdateWrapper<>();
        lambdaQueryWrapper.eq(ProjectContract::getStatus, ProjectContractStatusEnum.AUDITED.getStatus());
        lambdaQueryWrapper.lt(ProjectContract::getStartDate, calendar.getTime());
        lambdaQueryWrapper.set(ProjectContract::getStatus, ProjectContractStatusEnum.PERFORM.getStatus());
        projectContractService.update(lambdaQueryWrapper);
    }
}
