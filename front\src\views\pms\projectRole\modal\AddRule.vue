<template>
  <BasicModal
    v-bind="$attrs"
    :width="500"
    :min-height="260"
    :use-wrapper="false"
    title="添加规则"
    @register="modalRegister"
    @visibleChange="visibleChange"
  >
    <AddRuleComponent
      v-model:value="checkValues"
      @change="selectChange"
    />

    <!--footer-->
    <template #footer>
      <ModalFooterButtons
        :is-continue="false"
        :button-loading="buttonLoading"
        :ok-disabled="!checkValues.length"
        @reset="reset"
        @cancel="cancel"
        @ok="ok"
      />
    </template>
  </BasicModal>
</template>

<script lang="ts">
import {
  defineComponent, reactive, ref, toRefs,
} from 'vue';
import { BasicModal, useModalInner, ModalFooterButtons } from 'lyra-component-vue3';
import AddRuleComponent from './component/AddRuleComponent.vue';

export default defineComponent({
  name: 'AddRule',
  components: {
    BasicModal,
    ModalFooterButtons,
    AddRuleComponent,
  },
  emits: ['change'],
  setup(props, { emit }) {
    const buttonLoading = ref(false);

    const state = reactive({
      checkValues: [],
      selectItems: [],
    });

    // useModal
    const [modalRegister, { closeModal }] = useModalInner((data) => {});

    function selectChange(selectItems) {
      state.selectItems = selectItems;
    }

    return {
      ...toRefs(state),
      selectChange,
      modalRegister,
      buttonLoading,
      visibleChange(visible) {},
      cancel() {
        closeModal();
      },
      reset() {},
      ok() {
        emit('change', state.selectItems);
        closeModal();
        state.checkValues = [];
      },
    };
  },
});
</script>

<style scoped lang="less"></style>
