<script setup lang="ts">
import { BasicCard } from 'lyra-component-vue3';
import { inject, reactive, Ref } from 'vue';

const detailsData: Ref<Record<string, any>> = inject('detailsData');

function getQuarterDates(year, quarter) {
  const startMonth = 3 * (quarter - 1) + 1;
  const endMonth = startMonth + 2;
  const startDate = new Date(year, startMonth - 1, 1);
  const endDate = new Date(year, endMonth, 0);

  return {
    startDate: `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}-${String(startDate.getDate()).padStart(2, '0')}`,
    endDate: `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}-${String(endDate.getDate()).padStart(2, '0')}`,
  };
}

const gridContentProps = reactive({
  gap: '20px 10px',
  list: [
    {
      label: '编号',
      field: 'number',
    },
    {
      label: '验收名称',
      field: 'name',
    },
    {
      label: '验收单位',
      field: 'employDeptName',
    },
    {
      label: '验收时间段',
      field: '',
      valueRender({ record }) {
        return Object.values(getQuarterDates(record.acceptanceYear, record.acceptanceQuarter)).join(' ~ ');
      },
    },
    {
      label: '合同编号',
      field: 'contractNo',
    },
    {
      label: '合同名称',
      field: 'contractName',
    },
    {
      label: '预计开始日期',
      field: 'estimatedStartTime',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '预计结束日期',
      field: 'estimatedEndTime',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '合同执行状态',
      field: 'contractStatus',
    },
    {
      label: '合同类型',
      field: 'contractType',
    },
    {
      label: '供应商',
      field: 'supplierName',
    },
    // {
    //   label: '供应商联系人',
    //   field: '',
    // },
    // {
    //   label: '供应商联系电话',
    //   field: '',
    // },
    {
      label: '项目组审核人',
      field: 'projectReviewerName',
    },
    {
      label: '研究所审核人',
      field: 'deptReviewerName',
    },
    {
      label: '中心/部门审核人',
      field: 'orgReviewerName',
    },
    {
      label: '归口部门审核人',
      field: 'belongDeptReviewerName',
      gridColumn: '1/5',
    },
    {
      label: '备注',
      field: 'remark',
      gridColumn: '1/5',
    },
  ],
  dataSource: detailsData,
});
</script>

<template>
  <BasicCard
    title="基本信息"
    :grid-content-props="gridContentProps"
  />
</template>

<style scoped lang="less">

</style>
