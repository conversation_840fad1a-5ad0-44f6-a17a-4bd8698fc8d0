<script setup lang="ts">
import {
  BasicCard,
  BasicTableAction,
  DataStatusTag,
  IOrionTableActionItem,
  Layout,
  OrionTable,
  randomString,
  BasicButton,
  isPower,
} from 'lyra-component-vue3';
import { Modal, Step, Steps } from 'ant-design-vue';
import {
  computed, ComputedRef, h, inject, onMounted, ref, Ref,
} from 'vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { openWorkPackageForm, openWorkProgressForm } from '../utils';
import Api from '/@/api';

const router = useRouter();
const powerCodePrefix: Ref = inject('powerCodePrefix');
const detailsData: Record<string, any> = inject('detailsData');
const powerData: Ref = inject('powerData');

const steps: Ref<any[]> = ref([]);
const currentStep: ComputedRef<number> = computed(() => steps.value.findIndex((item: {
  status: number
}) => item.status === detailsData.status));

const tableRef: Ref = ref();
const tableOptions = computed(() => ({
  showToolButton: false,
  showTableSetting: false,
  isSpacing: false,
  showSmallSearch: false,
  pagination: currentStep.value === 1,
  api: async (params: any) => {
    switch (currentStep.value) {
      case 0:
        const result = await new Api(`/pms/job-manage/package/info/${detailsData?.id}`).fetch('', '', 'GET');
        return result ? [
          {
            ...result,
            id: randomString(),
          },
        ] : [];
      case 1:
        return new Api('/pms/job-progress/page').fetch({
          ...params,
          query: {
            jobId: detailsData?.id,
          },
        }, '', 'POST');
      case 2:
        return [detailsData];
    }
  },
}));

const actions: IOrionTableActionItem[] = [
  {
    text: '查看',
    event: 'view',
    isShow: () => currentStep.value === 0 && isPower(`${powerCodePrefix.value}_container_05_01_button_01`, powerData.value),
  },
  {
    text: '编辑',
    event: 'edit',
    isShow: () => (currentStep.value === 0 && isPower(`${powerCodePrefix.value}_container_05_01_button_02`, powerData.value)) || (currentStep.value === 1 && isPower(`${powerCodePrefix.value}_container_05_03_button_01`, powerData.value)),
  },
  {
    text: '删除',
    event: 'delete',
    isShow: () => currentStep.value === 1 && isPower(`${powerCodePrefix.value}_container_05_03_button_02`, powerData.value),
  },
];

function actionClick(actionItem: any, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'view':
      navDetails();
      break;
    case 'edit':
      switch (currentStep.value) {
        case 0:
          openWorkPackageForm({ id: detailsData?.id }, updateTable);
          break;
        case 1:
          openWorkProgressForm(record, updateTable);
          break;
      }
      break;
    case 'delete':
      Modal.confirm({
        title: '删除提示？',
        content: '确认删除该数据？',
        onOk: () => deleteProgressApi([record?.id]),
      });
      break;
  }
}

function deleteProgressApi(ids: string[]) {
  return new Promise((resolve, reject) => {
    new Api('/pms/job-progress/remove').fetch(ids, '', 'DELETE').then(() => {
      updateTable();
      resolve('');
    }).catch((e) => {
      reject(e);
    });
  });
}

function navDetails() {
  router.push({
    name: 'PMSDailyWorkPackageDetails',
    params: {
      id: detailsData?.id,
    },
  });
}

function updateTable() {
  tableRef.value?.reload();
}

const loading: Ref<boolean> = ref(false);

async function getStatusList() {
  loading.value = true;
  try {
    const result = await new Api('/pms/job-manage/status/list').fetch('', '', 'POST');
    steps.value = result || [];
  } finally {
    loading.value = false;
  }
}

onMounted(() => {
  getStatusList();
});

const columns = [
  [
    {
      title: '操作',
      dataIndex: 'actions',
      width: 120,
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
    {
      title: '工单号',
      dataIndex: ['jobManageVO', 'number'],
    },
    {
      title: '工作抬头',
      dataIndex: ['jobManageVO', 'workJobTitle'],
    },
    {
      title: '工作中心',
      dataIndex: ['jobManageVO', 'workCenter'],
    },
    {
      title: '工作名称',
      dataIndex: ['jobManageVO', 'name'],
    },
    {
      title: '工作负责人',
      dataIndex: ['jobManageVO', 'rspUserName'],
    },
    {
      title: '大修轮次',
      dataIndex: ['jobManageVO', 'repairRound'],
    },
    {
      title: '是否重点项目',
      dataIndex: ['jobManageVO', 'isMajorProject'],
      customRender({ text }) {
        return text ? '是' : '否';
      },
    },
    {
      title: '功能位置',
      dataIndex: 'functionalLocation',
    },
    {
      title: '设备/系统',
      dataIndex: 'equipmentSystem',
    },
    {
      title: '研读审查状态',
      dataIndex: ['jobManageVO', 'studyExamineStatusName'],
    },
  ],
  [
    {
      title: '日期',
      dataIndex: 'workDate',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '总体进展',
      dataIndex: 'progressSchedule',
      customRender({ text }) {
        return text ? `${text}%` : '0%';
      },
    },
    {
      title: '工作进展',
      dataIndex: 'progressDetail',
    },
    {
      title: '备注',
      dataIndex: 'remark',
    },
    {
      title: '操作',
      dataIndex: 'actions',
      width: 120,
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
  ],
  [
    {
      title: '工单号',
      dataIndex: 'number',
    },
    {
      title: '工作抬头',
      dataIndex: 'workJobTitle',
    },
    {
      title: '工作名称',
      dataIndex: 'name',
    },
    {
      title: '大修轮次',
      dataIndex: 'repairRound',
    },
    {
      title: '是否重大项目',
      dataIndex: 'isMajorProject',
      customRender({ text }) {
        return text ? '是' : '否';
      },
    },
    {
      title: '作业状态',
      dataIndex: 'dataStatus',
      customRender({ text }) {
        return text ? h(DataStatusTag, { statusData: text }) : '';
      },
    },
    {
      title: '关闭时间',
      dataIndex: 'closeDate',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
  ],
];
</script>

<template>
  <Layout
    v-get-power="{powerData}"
    :options="{ body: { scroll: true } }"
  >
    <div class="steps-wrap">
      <Steps
        :current="currentStep"
        size="small"
      >
        <Step
          v-for="(item,index) in steps"
          :key="index"
          :title="item.desc"
        />
      </Steps>
    </div>
    <BasicCard
      v-if="currentStep!==-1"
      :title="currentStep===0?'工作包信息':currentStep===1?'作业进展信息':'作业信息'"
      :is-border="false"
    >
      <OrionTable
        ref="tableRef"
        :key="currentStep.toString()"
        :options="tableOptions"
        :columns="columns[currentStep]"
      >
        <template
          v-if="currentStep===1"
          #toolbarLeft
        >
          <BasicButton
            v-is-power="[`${powerCodePrefix}_container_05_02_button_01`]"
            type="primary"
            icon="sie-icon-tianjiaxinzeng"
            @click="openWorkProgressForm({jobId:detailsData?.id},updateTable)"
          >
            添加进展
          </BasicButton>
        </template>
        <template #actions="{record}">
          <BasicTableAction
            :actions="actions"
            :record="record"
            @actionClick="actionClick($event,record)"
          />
        </template>
      </OrionTable>
    </BasicCard>
  </Layout>
</template>

<style scoped lang="less">
.steps-wrap {
  width: 80%;
  margin: 0 auto;
  padding: ~`getPrefixVar('content-padding-top')` ~`getPrefixVar('content-padding-left')` 0;
}
</style>
