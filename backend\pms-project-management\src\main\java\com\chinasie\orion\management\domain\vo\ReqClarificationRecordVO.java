package com.chinasie.orion.management.domain.vo;

import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * ReqClarificationRecord VO对象
 *
 * <AUTHOR>
 * @since 2024-06-27 09:31:59
 */
@ApiModel(value = "ReqClarificationRecordVO对象", description = "需求澄清记录")
@Data
public class ReqClarificationRecordVO extends ObjectVO implements Serializable {

    /**
     * 澄清主题
     */
    @ApiModelProperty(value = "澄清主题")
    private String clarificationTheme;


    /**
     * 澄清类型
     */
    @ApiModelProperty(value = "澄清类型")
    private String clarificationType;


    /**
     * 澄清批次
     */
    @ApiModelProperty(value = "澄清批次")
    private String clarificationLot;


    /**
     * 澄清内容
     */
    @ApiModelProperty(value = "澄清内容")
    private String clarificationContext;


    /**
     * 是否更新报价时间
     */
    @ApiModelProperty(value = "是否更新报价时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String IsUpdateTime;


    /**
     * 需求编号
     */
    @ApiModelProperty(value = "需求编号")
    private String requirementId;


    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date ReleaseTime;


    /**
     * 查看时间
     */
    @ApiModelProperty(value = "查看时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;


    /**
     * 查看状态
     */
    @ApiModelProperty(value = "查看状态")
    private String checkStatus;


    /**
     * 原报价开始时间
     */
    @ApiModelProperty(value = "原报价开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date originQuoteStartTime;


    /**
     * 原报价截止时间
     */
    @ApiModelProperty(value = "原报价截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date originQuoteDlTime;


    /**
     * 原开启报价时间
     */
    @ApiModelProperty(value = "原开启报价时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date originQuoteOpenTime;


    /**
     * 新开启报价时间
     */
    @ApiModelProperty(value = "新开启报价时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date newQuoteOpenTime;


    /**
     * 新报价截止时间
     */
    @ApiModelProperty(value = "新报价截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date newQuoteDlTime;


    /**
     * 新报价开始时间
     */
    @ApiModelProperty(value = "新报价开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date newQuoteStartTime;

    /**
     * 澄清阶段
     */
    @ApiModelProperty(value = "澄清阶段")
    private String clarificationStage;

    /**
     * 附件列表
     */
    @ApiModelProperty(value = "附件列表")
    private List<FileVO> fileList;

}
