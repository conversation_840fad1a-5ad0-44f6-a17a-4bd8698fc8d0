<script setup lang="ts">
import {
  BasicForm, FormSchema, getDict, SelectUserModal, useForm, useModal,
} from 'lyra-component-vue3';
import TableRender from './TableRender.vue';
import dayjs from 'dayjs';
import {
  h, nextTick, onMounted, ref, Ref,
} from 'vue';
import { CheckProjectModal } from '..';
import Api from '/@/api';
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';

const props = defineProps<{
  detail: Record<string, any>
}>();

interface UserData {
  id: string,
  name: string
}

const [registerCheckProject, { openModal: openCheckProjectModal }] = useModal();
const [registerSelectUser, { openModal: openSelectUserModal }] = useModal();

const tableRef: Ref = ref();
const deptOptions: Ref<any[]> = ref([]);
// 已选择项目id
const selectProjectId: Ref<string> = ref();
// 已选择项目负责人
const selectUser: Ref<UserData> = ref();
const schemas: FormSchema[] = [
  {
    field: 'title',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(DetailsLayout, {
        title: '项目关键信息',
        isFormItem: true,
      });
    },
  },
  {
    field: 'projectNumber',
    component: 'InputSearch',
    label: '项目编号',
    required: true,
    componentProps: {
      disabled: props.detail?.operationType === 'fixed',
      allowClear: false,
      onChange() {
        selectProjectId.value = undefined;
      },
      onSearch(value:string, event:Record<string, any>) {
        if (event.type === 'click') {
          openCheckProjectModal(true, {});
        }
      },
    },
  },
  {
    field: 'projectName',
    component: 'InputSearch',
    label: '项目名称',
    required: true,
    componentProps: {
      disabled: props.detail?.operationType === 'fixed',
      allowClear: false,
      onChange() {
        selectProjectId.value = undefined;
      },
      onSearch(value:string, event:Record<string, any>) {
        if (event.type === 'click') {
          openCheckProjectModal(true, {});
        }
      },
    },
  },
  {
    field: 'estimateAmt',
    component: 'InputNumber',
    label: '项目预估金额',
    componentProps: {
      style: 'width:100%',
      addonAfter: '元',
      precision: 0,
      min: 0,
      formatter(value:string) {
        return value.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      },
      maxLength: 15,
    },
  },
  {
    field: 'projectSource',
    component: 'ApiSelect',
    label: '项目来源',
    componentProps: {
      api: async () => await getDict('dict1714906542609989632'),
      labelField: 'description',
    },
  },
  {
    field: 'projectStartTime',
    component: 'DatePicker',
    label: '项目开始时间',
    required: true,
    componentProps: ({ formModel }) => ({
      disabledDate: (date:Date) => (formModel.projectEndTime ? dayjs(date).valueOf() >= dayjs(formModel.projectEndTime).valueOf() : false),
    }),
  },
  {
    field: 'projectEndTime',
    component: 'DatePicker',
    label: '项目结束时间',
    required: true,
    componentProps: ({ formModel }) => ({
      disabledDate: (date:Date) => (formModel.projectStartTime ? dayjs(date).valueOf() <= dayjs(formModel.projectStartTime).valueOf() : false),
    }),
  },
  {
    field: 'resUserName',
    component: 'InputSearch',
    label: '项目负责人',
    required: true,
    componentProps: {
      placeholder: '请选择',
      allowClear: false,
      onFocus(e:Record<string, any>) {
        e.target.blur();
        openSelectUserModal(true);
      },
    },
  },
  {
    field: 'rspDeptId',
    component: 'Select',
    label: '责任部门',
    required: true,
    componentProps: {
      disabled: true,
      options: deptOptions,
    },
  },
  {
    field: 'projectType',
    component: 'ApiSelect',
    label: '项目类型',
    required: true,
    componentProps: {
      allowClear: false,
      api: () => new Api('/pms/dict/code').fetch('', 'pms_project_type', 'GET'),
      labelField: 'description',
    },
  },
  {
    field: 'projectSubType',
    component: 'ApiSelect',
    label: '子类型',
    required: true,
    ifShow: ({ model }) => model.projectType === 'invest_server',
    componentProps: {
      allowClear: false,
      api: () => new Api('/pms/dict/code').fetch('', 'business_pms_investment', 'GET'),
      labelField: 'description',
    },
  },
  {
    field: 'applyReason',
    component: 'Input',
    label: '项目申请理由',
    colProps: {
      span: 24,
    },
  },
  {
    field: 'fileInfoDTOList',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    render() {
      return h(TableRender, { ref: tableRef });
    },
  },
  {
    field: 'projectBackground',
    component: 'Input',
    label: '项目背景摘要',
    colProps: {
      span: 24,
    },
  },
  {
    field: 'projectTarget',
    component: 'Input',
    label: '项目目标摘要',
    colProps: {
      span: 24,
    },
  },
  {
    field: 'technologyPlan',
    component: 'Input',
    label: '技术方案摘要',
    colProps: {
      span: 24,
    },
  },
];

const [register, { setFieldsValue, validate }] = useForm({
  layout: 'vertical',
  schemas,
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
});

onMounted(() => {
  (props.detail?.id || props.detail?.operationType === 'fixed') && initForm();
});

// 初始化表单
async function initForm() {
  await setFieldsValue(props.detail);
  selectProjectId.value = props.detail?.projectId;
  deptOptions.value = [
    {
      label: props.detail?.rspDeptName,
      value: props.detail?.rspDeptId,
    },
  ];
  selectUser.value = {
    id: props.detail?.resUserId,
    name: props.detail?.resUserName,
  };
  await nextTick();
  tableRef.value.setData(props.detail?.materialList);
}

// 选择项目回调
function checkProjectCallback(project:Record<string, string>, dept:Array<Record<string, any>>, user:UserData) {
  selectProjectId.value = project.id;
  deptOptions.value = dept;
  selectUser.value = user;

  setFieldsValue({
    rspDeptId: dept[0]?.value ?? '',
    resUserName: user.name,
    projectName: project.name,
    projectSource: project?.projectSource,
    projectType: project?.projectType,
    projectNumber: project.number,
    projectStartTime: project.projectStartTime,
    projectEndTime: project.projectEndTime,
  });
}

// 选择人员回调
async function selectUserCallback(user:UserData) {
  const result: Record<string, any> = await new Api('/pmi/user/user-profile').fetch('', user[0].id, 'GET');
  deptOptions.value = [
    {
      label: result.orgName,
      value: result.orgId,
    },
  ];
  selectUser.value = {
    id: result.userId,
    name: result.name,
  };
  await setFieldsValue({
    resUserName: result.name,
    rspDeptId: result.orgId,
  });
}

defineExpose({
  validate,
  selectUser,
  selectProjectId,
  getTableData: () => tableRef.value.getData(),
});
</script>

<template>
  <BasicForm @register="register" />
  <CheckProjectModal
    :onCheckProjectCallback="checkProjectCallback"
    @register="registerCheckProject"
  />
  <SelectUserModal
    selectType="radio"
    @ok="selectUserCallback"
    @register="registerSelectUser"
  />
</template>

<style scoped lang="less">

</style>
