package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.util.Date;
import java.lang.String;
import java.lang.Integer;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * CollaborativeCompilationTask DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-04 17:04:08
 */
@ApiModel(value = "CollaborativeCompilationTaskDTO对象", description = "协同编制任务表")
@Data
@ExcelIgnoreUnannotated
public class CollaborativeCompilationTaskDTO extends  ObjectDTO   implements Serializable{

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @ExcelProperty(value = "排序 ", index = 0)
    private Integer sort;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @ExcelProperty(value = "名称 ", index = 1)
    private String name;

    /**
     * 立项Id
     */
    @ApiModelProperty(value = "立项Id")
    @ExcelProperty(value = "立项Id ", index = 2)
    private String approvalId;

    /**
     * 层级
     */
    @ApiModelProperty(value = "层级")
    @ExcelProperty(value = "层级 ", index = 3)
    private Integer level;

    /**
     * 父id
     */
    @ApiModelProperty(value = "父id")
    @ExcelProperty(value = "父id ", index = 4)
    private String parentId;

    /**
     * 责任部门
     */
    @ApiModelProperty(value = "责任部门")
    @ExcelProperty(value = "责任部门 ", index = 5)
    private String rspDept;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    @ExcelProperty(value = "责任人 ", index = 6)
    private String rspUser;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    @ExcelProperty(value = "计划开始时间 ", index = 7)
    private Date beginTime;

    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    @ExcelProperty(value = "计划结束时间 ", index = 8)
    private Date endTime;

    /**
     * 计划情况
     */
    @ApiModelProperty(value = "计划情况")
    @ExcelProperty(value = "计划情况 ", index = 9)
    private Integer circumstance;

    /**
     * 实际结束时间
     */
    @ApiModelProperty(value = "实际结束时间")
    @ExcelProperty(value = "实际结束时间 ", index = 10)
    private Date actualEndTime;

    /**
     * 实际开始时间
     */
    @ApiModelProperty(value = "实际开始时间")
    @ExcelProperty(value = "实际开始时间 ", index = 11)
    private Date actualBeginTime;

    /**
     * 父级链
     */
    @ApiModelProperty(value = "父级链")
    @ExcelProperty(value = "父级链 ", index = 12)
    private String parentChain;

    /**
     * 任务描述
     */
    @ApiModelProperty(value = "任务描述")
    @ExcelProperty(value = "任务描述 ", index = 13)
    private String taskDesc;

    /**
     * 置顶序号（0：取消置顶）
     */
    @ApiModelProperty(value = "置顶序号（0：取消置顶）")
    @ExcelProperty(value = "置顶序号（0：取消置顶） ", index = 14)
    private Integer topSort;

    /**
     * 执行情况说明
     */
    @ApiModelProperty(value = "执行情况说明")
    @ExcelProperty(value = "执行情况说明 ", index = 15)
    private String executeDesc;

    /**
     * 下发计划时间
     */
    @ApiModelProperty(value = "下发计划时间")
    @ExcelProperty(value = "下发计划时间 ", index = 16)
    private Date issueTime;

    /**
     * 计划工期
     */
    @ApiModelProperty(value = "计划工期")
    @ExcelProperty(value = "计划工期 ", index = 17)
    private Integer durationDays;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 18)
    private String number;

    /**
     * 下达人
     */
    @ApiModelProperty(value = "下达人")
    @ExcelProperty(value = "下达人 ", index = 19)
    private String issuedUser;

    /**
     * 确认理由
     */
    @ApiModelProperty(value = "确认理由")
    @ExcelProperty(value = "确认理由 ", index = 20)
    private String reasonConfirmation;

    /**
     * 上一个状态
     */
    @ApiModelProperty(value = "上一个状态")
    @ExcelProperty(value = "上一个状态 ", index = 21)
    private Integer lastStatus;

    /**
     * 任务内容
     */
    @ApiModelProperty(value = "任务内容")
    @ExcelProperty(value = "任务内容 ", index = 22)
    private String content;

    /**
     * 处理实例
     */
    @ApiModelProperty(value = "处理实例")
    @ExcelProperty(value = "处理实例 ", index = 23)
    private String processInstances;

    /**
     * 处理对象
     */
    @ApiModelProperty(value = "处理对象")
    @ExcelProperty(value = "处理对象 ", index = 24)
    private String processObject;


    @ApiModelProperty(value = "附件文档")
    private List<FileDTO> attachments;

    @ApiModelProperty(value = "同意驳回")
    private String confirmResult;

    @ApiModelProperty(value = "转办理由")
    private String  reasonTransfer;


}
