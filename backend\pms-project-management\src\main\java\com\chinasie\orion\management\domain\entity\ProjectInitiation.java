package com.chinasie.orion.management.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * ProjectInitiation Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-29 17:48:53
 */
@TableName(value = "pmsx_project_initiation")
@ApiModel(value = "ProjectInitiationEntity对象", description = "项目立项")
@Data

public class ProjectInitiation extends ObjectEntity implements Serializable {

    /**
     * 立项编号
     */
    @ApiModelProperty(value = "立项编号")
    @TableField(value = "project_number")
    private String projectNumber;

    /**
     * 立项名称
     */
    @ApiModelProperty(value = "立项名称")
    @TableField(value = "project_name")
    private String projectName;

    /**
     * 立项标签
     */
    @ApiModelProperty(value = "立项标签")
    @TableField(value = "project_label")
    private String projectLabel;

    /**
     * 立项状态
     */
    @ApiModelProperty(value = "立项状态")
    @TableField(value = "project_status")
    private String projectStatus;

    /**
     * 立项类型
     */
    @ApiModelProperty(value = "立项类型")
    @TableField(value = "project_type")
    private String projectType;

    /**
     * 项目发起日期
     */
    @ApiModelProperty(value = "项目发起日期")
    @TableField(value = "project_init_date")
    private Date projectInitDate;

    /**
     * 项目开始日期
     */
    @ApiModelProperty(value = "项目开始日期")
    @TableField(value = "project_start_date")
    private Date projectStartDate;

    /**
     * 项目结束日期
     */
    @ApiModelProperty(value = "项目结束日期")
    @TableField(value = "project_end_date")
    private Date projectEndDate;

    /**
     * 项目责任人
     */
    @ApiModelProperty(value = "项目责任人")
    @TableField(value = "project_person")
    private String projectPerson;

    /**
     * 项目责任人id
     */
    @ApiModelProperty(value = "项目责任人ID")
    @TableField(value = "project_person_id")
    private String projectPersonId;

    /**
     * 承担中心id
     */
    @ApiModelProperty(value = "承担中心id")
    @TableField(value = "project_assume_center_id")
    private String projectAssumeCenterId;

    /**
     * 承担中心
     */
    @ApiModelProperty(value = "承担中心")
    @TableField(value = "project_assume_center")
    private String projectAssumeCenter;

    /**
     * 立项理由
     */
    @ApiModelProperty(value = "立项理由")
    @TableField(value = "project_reson")
    private String projectReson;

    /**
     * 合同编号拼接
     */
    @ApiModelProperty(value = "合同编号拼接")
    @TableField(value = "contract_numbers")
    private String contractNumbers;

    /**
     * 线索编号拼接
     */
    @ApiModelProperty(value = "线索编号拼接")
    @TableField(value = "clue_numbers")
    private String clueNumbers;

    /**
     * 工厂
     */
    @ApiModelProperty(value = "工厂")
    @TableField(value = "company_code")
    private String companyCode;

    /**
     * 项目的公司代码
     */
    @ApiModelProperty(value = "项目的公司代码")
    @TableField(value = "project_company_code")
    private String projectCompanyCode;

    /**
     * 项目公司名称
     */
    @ApiModelProperty(value = "项目公司名称")
    @TableField(value = "project_company_name")
    private String projectCompanyName;

    /**
     * 委托方代码1
     */
    @ApiModelProperty(value = "委托方代码1")
    @TableField(value = "client_one")
    private String clientOne;

    /**
     * 中文客户名称1
     */
    @ApiModelProperty(value = "中文客户名称1")
    @TableField(value = "client_one_name")
    private String clientOneName;

    /**
     * 委托方代码2
     */
    @ApiModelProperty(value = "委托方代码2")
    @TableField(value = "client_two")
    private String clientTwo;

    /**
     * 中文客户名称2
     */
    @ApiModelProperty(value = "中文客户名称2")
    @TableField(value = "client_two_name")
    private String clientTwoName;

    /**
     * 委托方代码3
     */
    @ApiModelProperty(value = "委托方代码3")
    @TableField(value = "client_three")
    private String clientThree;

    /**
     * 中文客户名称3
     */
    @ApiModelProperty(value = "中文客户名称3")
    @TableField(value = "client_three_name")
    private String clientThreeName;

    /**
     * 核电委托方
     */
    @ApiModelProperty(value = "核电委托方")
    @TableField(value = "nuclear_client")
    private String nuclearClient;

    /**
     * 火电委托方
     */
    @ApiModelProperty(value = "火电委托方")
    @TableField(value = "fire_client")
    private String fireClient;

    /**
     * 风电委托方
     */
    @ApiModelProperty(value = "风电委托方")
    @TableField(value = "wind_client")
    private String windClient;

    /**
     * 其他委托方
     */
    @ApiModelProperty(value = "其他委托方")
    @TableField(value = "other_client")
    private String otherClient;

    /**
     * 外部合同号
     */
    @ApiModelProperty(value = "外部合同号")
    @TableField(value = "out_contract_number")
    private String outContractNumber;

//    @ApiModelProperty(value = "冗余字段：部门ID")
//    @TableField(value = "dept_id")
//    private String deptId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    private String projectId;

    @ApiModelProperty(value = "工作分解结构元素 (WBS 元素)")
    @TableField(exist = false)
    private String wbsElement;


    @ApiModelProperty(value = "立项时间")
    @TableField(value = "initiation_time")
    private Date initiationTime;

    /**
     * 销售合同号（原始）
     */
    @ApiModelProperty(value = "销售合同号（原始）")
    @TableField(value = "original_number")
    private String originalNumber;
}
