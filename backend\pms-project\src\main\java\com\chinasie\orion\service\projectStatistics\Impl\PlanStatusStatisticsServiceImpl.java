package com.chinasie.orion.service.projectStatistics.Impl;

import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.domain.dto.projectStatistics.PlanStatusStatisticsDTO;
import com.chinasie.orion.domain.entity.projectStatistics.PlanStatusStatistics;
import com.chinasie.orion.domain.vo.projectStatistics.PlanStatusStatisticsVO;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.projectStatistics.PlanStatusStatisticsMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.projectStatistics.PlanStatusStatisticsService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * PlanStatusStatistics 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-21 11:28:29
 */
@Service
public class PlanStatusStatisticsServiceImpl extends OrionBaseServiceImpl<PlanStatusStatisticsMapper, PlanStatusStatistics> implements PlanStatusStatisticsService {

    @Autowired
    private PlanStatusStatisticsMapper planStatusStatisticsMapper;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    public PlanStatusStatisticsVO detail(String id) throws Exception {
        PlanStatusStatistics planStatusStatistics =planStatusStatisticsMapper.selectById(id);
        PlanStatusStatisticsVO result = BeanCopyUtils.convertTo(planStatusStatistics,PlanStatusStatisticsVO::new);
        return result;
    }

    /**
     *  新增
     *
     * * @param planStatusStatisticsDTO
     */
    @Override
    public  PlanStatusStatisticsVO create(PlanStatusStatisticsDTO planStatusStatisticsDTO) throws Exception {
        PlanStatusStatistics planStatusStatistics =BeanCopyUtils.convertTo(planStatusStatisticsDTO,PlanStatusStatistics::new);
        int insert = planStatusStatisticsMapper.insert(planStatusStatistics);
        PlanStatusStatisticsVO rsp = BeanCopyUtils.convertTo(planStatusStatistics,PlanStatusStatisticsVO::new);
        return rsp;
    }

    /**
     *  编辑
     *
     * * @param planStatusStatisticsDTO
     */
    @Override
    public Boolean edit(PlanStatusStatisticsDTO planStatusStatisticsDTO) throws Exception {
        PlanStatusStatistics planStatusStatistics =BeanCopyUtils.convertTo(planStatusStatisticsDTO,PlanStatusStatistics::new);
        int update =  planStatusStatisticsMapper.updateById(planStatusStatistics);
        return SqlHelper.retBool(update);
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        int delete = planStatusStatisticsMapper.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    public Page<PlanStatusStatisticsVO> pages(Page<PlanStatusStatisticsDTO> pageRequest) throws Exception {
        Page<PlanStatusStatistics> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), PlanStatusStatistics::new));

        PageResult<PlanStatusStatistics> page = planStatusStatisticsMapper.selectPage(realPageRequest,null);

        Page<PlanStatusStatisticsVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<PlanStatusStatisticsVO> vos = BeanCopyUtils.convertListTo(page.getContent(), PlanStatusStatisticsVO::new);
        pageResult.setContent(vos);

        return pageResult;
    }
}
