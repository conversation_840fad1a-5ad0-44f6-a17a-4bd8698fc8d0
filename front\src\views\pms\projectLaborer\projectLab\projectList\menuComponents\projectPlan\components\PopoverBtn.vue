<script setup lang="ts">
import { computed, ref, Ref } from "vue";
import { Icon, isPower } from "lyra-component-vue3";
import { Popover } from "ant-design-vue";

const props = withDefaults(
  defineProps<{
    popoverContainer: any;
    userInfo: any;
    record: any;
    indexData: any;
    num: number;
    editFlag?: boolean;
    onInitActions: Function;
  }>(),
  {
    editFlag: true,
  }
);

const emits = defineEmits<{
  (e: "submit", type: string, record: any, resolve: (value: any) => void): void;
}>();

const isEnter: Ref<boolean> = ref(false);

const timer: Ref = ref();
const options: Ref<any[]> = ref([]);

const dataType = ref([
  {
    name: "是（作业）",
    id: 1,
  },
  {
    name: "否（作业）",
    id: 0,
  },
]);

function onMouseEnter() {
  if (!props.editFlag) return;
  clearTimeout(timer.value);
  timer.value = setTimeout(() => {
    isEnter.value = true;
  }, 300);
}

function onMouseLeave() {
  if (!props.editFlag) return;
  clearTimeout(timer.value);
  isEnter.value = false;
}

const isFocus: Ref<boolean> = ref(false);

function onFocus() {
  isFocus.value = true;
}

function onBlur() {
  isFocus.value = false;
}

async function submit(type: string, record: any) {
  await new Promise((resolve) => {
    emits("submit", type, record, resolve);
  });
}

function onClickType(type, record) {
  submit(type, record);
}

const isShow = computed(() => isEnter.value || isFocus.value);

const getPopupContainer = () => props.popoverContainer.value;

function isCreator(userInfo, record) {
  return (
    record.isManager ||
    userInfo.id === record.creatorId ||
    userInfo.id === record.issuedUser
  );
}

function isRspUser(record, userInfo) {
  return record.rspUser === userInfo.id;
}
</script>

<template>
  <div class="btns">
    <div
      v-show="
        isPower('PMS_XMXQ_container_03_01_02_button_25', record?.rdAuthList)
      "
      class="more-mouse-cell-icon"
    >
      <Popover placement="top" :getPopupContainer="getPopupContainer">
        <template #content> 查看 </template>
        <div class="fw-icon">
          <Icon
            icon="sie-icon-chakandaima"
            size="16"
            @click="onClickType('check', record)"
          />
        </div>
      </Popover>
    </div>
    <div
      v-show="
        record.status === 130 &&
        isRspUser(record, userInfo) &&
        isPower('PMS_XMXQ_container_03_01_02_button_16', record.rdAuthList)
      "
      class="more-mouse-cell-icon-st"
    >
      <Popover placement="top" :getPopupContainer="getPopupContainer">
        <template #content> 开始执行 </template>
        <div class="st-icon">
          <Icon
            icon="orion-icon-play-square"
            size="18"
            @click="onClickType('startExecution', record)"
          />
        </div>
      </Popover>
    </div>
    <div
      v-show="
        record.status === 140 &&
        isRspUser(record, userInfo) &&
        isPower('PMS_XMXQ_container_03_01_02_button_06', record?.rdAuthList) &&
        ![0, 1].includes(record.approveStatus)
      "
      class="more-mouse-cell-icon-st"
    >
      <Popover placement="top" :getPopupContainer="getPopupContainer">
        <template #content> 执行完成 </template>
        <div class="st-icon">
          <Icon
            icon="orion-icon-check-circle"
            size="18"
            @click="onClickType('executionComplete', record)"
          />
        </div>
      </Popover>
    </div>
    <div
      v-show="
        record.status === 121 &&
        isPower('PMS_XMXQ_container_03_01_02_button_10', record?.rdAuthList) &&
        ![0, 1].includes(record.approveStatus) &&
        isCreator(userInfo, record)
      "
      class="more-mouse-cell-icon-st"
    >
      <Popover placement="top" :getPopupContainer="getPopupContainer">
        <template #content> 完成确认 </template>
        <div class="st-icon">
          <Icon
            icon="sie-icon-tijiao"
            size="18"
            @click="onClickType('completePlanRow', record)"
          />
        </div>
      </Popover>
    </div>
    <div v-if="props.onInitActions().length > 0" class="more-mouse-cell">
      <Popover placement="rightTop" :getPopupContainer="getPopupContainer">
        <template #content>
          <div class="type-list">
            <ul>
              <li
                v-for="(item, index) in props.onInitActions()"
                :key="index"
                :class="item.isShow(record) ? 'block' : 'none'"
                @click="item.onClick(record)"
              >
                <Icon :icon="item.icon" size="16" />
                <span>
                  {{ item.text }}
                </span>
              </li>
            </ul>
          </div>
        </template>
        <span class="fw">...</span>
      </Popover>
    </div>
  </div>
</template>

<style scoped lang="less">
.btns {
  display: flex;
  align-items: center;
}
.more-mouse-cell {
  .fw {
    display: none;
    cursor: pointer;
    font-size: 24px;
    color: #0960bd;
    margin-bottom: 11px;
    margin-left: 2px;
  }
}
.more-mouse-cell-icon {
  .fw-icon {
    display: none;
    cursor: pointer;
    font-size: 16px;
    color: #0960bd;
  }
}
.more-mouse-cell-icon-st {
  margin: 0 1.5px;
  .st-icon {
    display: none;
    cursor: pointer;
    font-size: 16px;
    color: #0960bd;
    margin-top: 2px;
  }
}
.project-layout-content {
  .type-list {
    display: flex;
    flex-direction: row;
    background: #fff;
    margin: 0 -12px;
    ul {
      margin: 0;
      padding: 0;
      .block {
        display: block;
      }
      .none {
        display: none;
      }
      li {
        width: 110px;
        display: flex;
        flex-direction: row;
        align-items: center;
        text-align: left;
        list-style-type: none;
        padding: 5px 3px;
        cursor: pointer;
        border-bottom: 1px solid #ddd;
        span {
          margin-left: 7px;
        }
      }
      li:last-child {
        border-bottom: none;
      }
    }
  }
}
</style>
