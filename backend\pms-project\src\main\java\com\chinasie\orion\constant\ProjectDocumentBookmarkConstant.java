package com.chinasie.orion.constant;

/**
 * 由类名和字段名组成
 * @author: lsy
 * @date: 2024/5/28
 * @description:
 */
public class ProjectDocumentBookmarkConstant {

    /**
     * 产品编码及名称
     */
    public static final String PRODUCT_NUMBER_NAME = "productNumberName";
    /**
     * 承研部门
     */
    public static final String RESEARCH_DEPARTMENT = "researchDepartment";
    /**
     * 产品经理
     */
    public static final String PRODUCT_MANAGER = "productManager";
    /**
     * 销售部门
     */
    public static final String SALE_DEPARTMENT = "saleDepartment";
    /**
     * 销售经理
     */
    public static final String SALE_MANAGER = "saleManager";
    /**
     * 建档时间
     */
    public static final String DOCUMENT_TIME = "documentTime";
    /**
     * 预计产出
     */
    public static final String EXPECT_OUTPUT = "expectedOutputs";
    /**
     * 签单情况
     */
    public static final String SIGNING_STATUS = "signingStatus";
    /**
     * 达成情况
     */
    public static final String ACHIEVEMENT = "achievement";
    /**
     * 预算
     */
    public static final String BUDGET = "budget";
    /**
     * 实际投入
     */
    public static final String ACTUAL_INPUT = "actualInput";
    /**
     * 项目组成员
     */
    public static final String PROJECT_ROLE_USER = "projectRoleUser";
    /**
     * 使用占比
     */
    public static final String USE_RATE = "useRate";
    /**
     * 奖励次数
     */
    public static final String REWARD_NUM = "rewardNum";
    /**
     * 奖励情况
     */
    public static final String REWARD_SITUATION = "rewardSituation";
    /**
     * 惩罚次数
     */
    public static final String PUNISHMENT_NUM = "punishmentNum";
    /**
     * 惩罚原因
     */
    public static final String PUNISHMENT_REASON = "punishmentReason";
    /**
     * 项目id
     */
    public static final String ID = "id";
}
