package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ProjectInternalAssociationDTO;
import com.chinasie.orion.domain.vo.ProjectInternalAssociationRedisVO;
import com.chinasie.orion.domain.vo.ProjectInternalAssociationVO;
import com.chinasie.orion.service.ProjectInternalAssociationService;
import com.mzt.logapi.starter.annotation.LogRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
/**
 * <p>
 * ProjectInternalAssociation 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-16 14:25:49
 */
@RestController
@RequestMapping("/projectInternalAssociation")
@Api(tags = "项目内部关联表")
public class ProjectInternalAssociationController {

    @Autowired
    private ProjectInternalAssociationService projectInternalAssociationService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查看了数据", type = "项目内部关联表", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectInternalAssociationVO> detail(@PathVariable(value = "id") String id) throws Exception {
        ProjectInternalAssociationVO rsp = projectInternalAssociationService.detail(id);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 列表
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "列表")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查看了数据", type = "项目内部关联表", subType = "列表", bizNo = "")
    public ResponseDTO<List<ProjectInternalAssociationRedisVO>> list(@RequestBody List<String> ids) throws Exception {
        List<ProjectInternalAssociationRedisVO> rsp = projectInternalAssociationService.list(ids);
        return new ResponseDTO<>(rsp);
    }



    /**
     * 新增
     *
     * @param projectInternalAssociationDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增了数据【{{#projectInternalAssociationDTO.name}}】", type = "项目内部关联表", subType = "新增", bizNo = "{{#projectInternalAssociationDTO.id}}")
    public ResponseDTO<ProjectInternalAssociationVO> create(@RequestBody ProjectInternalAssociationDTO projectInternalAssociationDTO) throws Exception {
        ProjectInternalAssociationVO rsp =  projectInternalAssociationService.create(projectInternalAssociationDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectInternalAssociationDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#projectInternalAssociationDTO.name}}】", type = "项目内部关联表", subType = "编辑", bizNo = "{{#projectInternalAssociationDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody ProjectInternalAssociationDTO projectInternalAssociationDTO) throws Exception {
        Boolean rsp = projectInternalAssociationService.edit(projectInternalAssociationDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "项目内部关联表", subType = "删除", bizNo = "{{#ids}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectInternalAssociationService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/pages", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "项目内部关联表", subType = "分页查询", bizNo = "")
    public ResponseDTO<Page<ProjectInternalAssociationVO>> pages(@RequestBody Page<ProjectInternalAssociationDTO> pageRequest) throws Exception {
        Page<ProjectInternalAssociationVO> rsp =  projectInternalAssociationService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }
}
