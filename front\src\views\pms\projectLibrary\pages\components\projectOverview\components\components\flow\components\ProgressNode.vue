<template>
  <div
    ref="wrapper"
    class="wrapper"
  >
    <div class="hover-box">
      <button
        class="current-button"
        :title="data?.data?.label"
        :style="{width: data?.data?.width + 'px', borderColor: data?.data?.fill, color: data?.data?.fill}"
      >
        <div
          class="icon-box"
          :style="{borderColor: data?.data?.fill}"
        >
          <Icon
            :icon="data?.id === '1' ? 'sie-icon-chuangjianjixian' : (data?.id === '2' ? 'sie-icon-dakai' : (data?.id === '3' ? 'sie-icon-gyzykgl' : (data?.id === '4' ? 'fa-check-square' : (data?.id === '5' ? 'sie-icon-gysz' : 'sie-icon-zsjsgl'))))"
            size="16"
            class="i-r"
            :color="data?.data?.full"
          />
        </div>
        {{ data?.data?.label }}
      </button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  inject, onMounted, ref,
} from 'vue';
import {
  Icon,
} from 'lyra-component-vue3';

const data:any = ref({});
const getNode:any = inject('getNode');
const wrapper = ref();
const visible = ref(false);
onMounted(() => {
  data.value = getNode();
});

</script>
<style lang="less" scoped>
:deep(.current-button){
  width: 100%;
  height: 50px;
  display: flex;
  flex-direction: row;
  align-items: center;
  background: inherit;
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-radius: 10px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-weight: 700;
  font-style: normal;
  font-size: 18px;
  color: #1890FF;
  text-align: left;
  cursor: pointer;
  padding: 0 10px;
    .icon-box{
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      height: 30px;
      width: 30px;
      border: 1px solid rgb(204, 204, 204);
      border-radius: 100%;
      margin-right: 10px;
    }
}
.hover-box:hover :deep(.current-button) {
  border: 1px solid #1890FF;
  color: #1890FF;
}
</style>
