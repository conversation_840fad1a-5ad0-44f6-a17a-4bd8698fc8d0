<script setup lang="ts">
import {
  BasicButton, BasicCard, Empty, Icon, openDrawer,
} from 'lyra-component-vue3';
import { TabPane, Tabs } from 'ant-design-vue';
import {
  h, provide, reactive, ref, Ref, watchEffect,
} from 'vue';
import dayjs from 'dayjs';
import Device<PERSON>erson from './DevicePerson.vue';
import PreparationInfo from './PreparationInfo.vue';
import JobManage from './JobManage.vue';
import UserManage from './UserManage.vue';
import MaterialManage from './MaterialManage.vue';
import Api from '/@/api';
import CheckRepairRound from './CheckRepairRound.vue';
import PreparationEdit from '/@/views/pms/overhaulManagement/components/overhaul/PreparationEdit.vue';
import axios from 'axios';
interface TabPaneItem {
  key: string
  tab: string
  isWarning: boolean
}

const tabKey: Ref<string> = ref('');
provide('repairRound', tabKey);
const tabPanes: Ref<TabPaneItem[]> = ref([]);

// 获取关注的准备中大修
const majorLoading: Ref<boolean> = ref(false);

async function getMajorInfo() {
  majorLoading.value = true;
  try {
    const result = await new Api('/pms/majorUserLike/major/info?statusEnum=PREPARE').fetch('', '', 'POST');
    tabPanes.value = result?.map((item) => ({
      key: item.repairRound,
      tab: item.repairRound,
      isWarning: item.isWarning,
    })) || [];
    tabKey.value = tabPanes.value?.[0]?.key;
  } finally {
    majorLoading.value = false;
  }
}

watchEffect(() => {
  getMajorInfo();
});

const repairRoundList: Ref<any[]> = ref([]);

async function getMajorList() {
  const result = await new Api('/pms/majorUserLike/major/list?statusEnum=PREPARE').fetch({}, '', 'POST');
  repairRoundList.value = result?.map((item) => item?.repairRound) || [];
}

watchEffect(() => {
  getMajorList();
});

// 大修准备信息相关
const containerRef: Ref = ref();

function handleEdit() {
  const drawerRef: Ref = ref();
  openDrawer({
    title: '大修准备',
    width: 1000,
    content() {
      return h(PreparationEdit, {
        ref: drawerRef,
        repairRound: tabKey.value,
      });
    },
    async onOk() {
      await drawerRef.value?.submit();
      await getInfo();
    },
  });
}

const infoData: Record<string, any> = reactive({});
const infoLoading: Ref<boolean> = ref(false);

async function getInfo() {
  if (!tabKey.value) return;
  infoLoading.value = true;
  try {
    const result = await new Api('/pms/preparationInfoPreparation/info').fetch({
      repairRound: tabKey.value,
    }, '', 'GET');
    Object.assign(infoData, result);
  } finally {
    infoLoading.value = false;
  }
}

watchEffect(() => {
  getInfo();
});

// 作业管理相关
const jobData: Record<string, any> = reactive({});
const jobLoading: Ref<boolean> = ref(false);

async function getJobCount() {
  if (!tabKey.value) return;
  jobLoading.value = true;
  try {
    const result = await new Api('/pms/major-dashboard/job/dashboard/count').fetch({
      repairRound: tabKey.value,
    }, '', 'GET');
    Object.assign(jobData, result);
  } finally {
    jobLoading.value = false;
  }
}

watchEffect(() => {
  getJobCount();
});

// 人员管理相关
const personData: Record<string, any> = reactive({});
const personLoading: Ref<boolean> = ref(false);

async function getPersonCount() {
  if (!tabKey.value) return;
  personLoading.value = true;
  try {
    const result = await new Api('/pms/major-dashboard/person/dashboard/count').fetch({
      repairRound: tabKey.value,
    }, '', 'GET');
    Object.assign(personData, result);
  } finally {
    personLoading.value = false;
  }
}

watchEffect(() => {
  getPersonCount();
});

// 物资管理相关
const materialData: Record<string, any> = reactive({});
const materialLoading: Ref<boolean> = ref(false);

async function getMaterialCount() {
  if (!tabKey.value) return;
  materialLoading.value = true;
  try {
    const result = await new Api('/pms/major-dashboard/material/dashboard/count').fetch({
      repairRound: tabKey.value,
    }, '', 'GET');
    Object.assign(materialData, result);
  } finally {
    materialLoading.value = false;
  }
}

watchEffect(() => {
  getMaterialCount();
});

// 人员与设备相关
const personNum=ref(0)
const materialNum=ref(0)
const userMaterialLoading: Ref<boolean> = ref(false);
const personMaterialData = reactive({
  personOverlap: null,
  person: null,
  materialOverlap: null,
  material: null,
});

async function getUserAndMaterial() {
  if (!tabKey.value) return;
  userMaterialLoading.value = true;
  try {
    const userApi = new Api('/pms/majorRepairStatistic/getPerson').fetch({
      repairRound: tabKey.value,
    }, '', 'POST');
    const userOverlapApi = new Api('/pms/resource-allocation/person/overlap/days').fetch({
      repairRound: tabKey.value,
      yearNum: dayjs().year(),
    }, '', 'POST');
    const materialApi = new Api('/pms/majorRepairStatistic/getMaterial').fetch({
      repairRound: tabKey.value,
    }, '', 'POST');
    const materialOverlapApi = new Api('/pms/resource-allocation/material/overlap/days').fetch({
      repairRound: tabKey.value,
      yearNum: dayjs().year(),
    }, '', 'POST');
    const results: any[] = await Promise.allSettled([
      userOverlapApi,
      userApi,
      materialOverlapApi,
      materialApi,
    ]);
    Object.keys(personMaterialData).forEach((key, index) => {
      personMaterialData[key] = results[index].status === 'fulfilled' ? results[index].value : 0;
    });
    personMaterialData.personOverlap=Number(personNum.value);
    personMaterialData.materialOverlap=Number(materialNum.value);
  } finally {
    userMaterialLoading.value = false;
  }
}


async function getpersonNum(){
  // axios.post('http://192.168.0.106:8700/personAllocation/getInfo',{ repairRound: tabKey.value,
  //   queryType:0,
  //   status:0,
  //   })
  //       .then(function (response) {
  //         personNum.value=response.data?.result?.overNumbers;
  //       })
  try {
    const obj = new Api('/pms/personAllocation/getInfo').fetch({
      repairRound: tabKey.value,
      queryType:0,
     status:0,
    }, '', 'POST').then((res)=>{
        personNum.value=res?.overNumbers
    })
    
  }
  catch{}
}
async function getmaterialNum(){
  // axios.post('http://192.168.0.106:8700/personAllocation/getInfo',{ repairRound: tabKey.value,
  //   queryType:1,
  //   status:0,
  //   })
  //       .then(function (response) {
  //         materialNum.value=response.data?.result?.overNumbers
  //       })
  try {
    const obj2 = new Api('/pms/personAllocation/getInfo').fetch({
      repairRound: tabKey.value,
      queryType:1,
     status:0,
    }, '', 'POST').then((res)=>{
        materialNum.value=res?.overNumbers
    })
    
  }
  catch{}
}

watchEffect(() => {
  getUserAndMaterial();
  getpersonNum();
  getmaterialNum();
});
</script>

<template>
  <div ref="containerRef" />
  <Tabs
    v-model:activeKey="tabKey"
    type="card"
  >
    <TabPane
      v-for="item in tabPanes"
      :key="item.key"
    >
      <template #tab>
        <div>
          <span>{{ item.tab }}</span>
          <Icon
            v-if="item.isWarning"
            class="ml5"
            style="color:red"
            icon="orion-icon-alert"
          />
        </div>
      </template>
    </TabPane>
    <template #rightExtra>
      <CheckRepairRound
        statusEnum="PREPARE"
        :options="repairRoundList"
        :getPopupContainer="()=>containerRef"
        :selectedKeys="tabPanes.map(item=>item.tab)"
        @update="getMajorInfo()"
      />
    </template>
  </Tabs>

  <div
    v-if="tabKey"
    :key="tabKey"
    class="grid-card"
  >
    <BasicCard
      v-loading="infoLoading"
      style="position: relative"
      :title="`大修准备信息（大修准备率 ${infoData.majorPrepareRate||0}%）`"
      :isSpacing="false"
      :isBorder="false"
    >
      <template #titleRight>
        <BasicButton
          v-is-power="['PMS_DXGLNEW_container_01_button_02']"
          type="primary"
          @click="handleEdit"
        >
          准备信息维护
        </BasicButton>
      </template>
      <PreparationInfo :data="infoData" />
    </BasicCard>
    <BasicCard
      v-loading="userMaterialLoading"
      style="position: relative"
      title="人员与设备"
      :isSpacing="false"
      :isBorder="false"
    >
      <DevicePerson
        class="mt15"
        height="200px"
        :data="personMaterialData"
           :personData="personNum"
        :materialData="materialNum"
      />
    </BasicCard>
    <BasicCard
      v-loading="jobLoading"
      style="position: relative"
      title="作业管理"
      :isSpacing="false"
      :isBorder="false"
    >
      <JobManage :data="jobData" />
    </BasicCard>
    <BasicCard
      v-loading="personLoading"
      title="人员管理"
      style="position: relative"
      :isSpacing="false"
      :isBorder="false"
    >
      <UserManage :data="personData" />
    </BasicCard>
    <BasicCard
      v-loading="materialLoading"
      style="grid-column: 1/3;position: relative"
      title="物资管理"
      :isSpacing="false"
      :isBorder="false"
    >
      <MaterialManage 
      :data="materialData" />
    </BasicCard>
  </div>
  <div
    v-else
    v-loading="majorLoading"
    class="empty-wrap"
  >
    <Empty description="暂无关注的轮次信息" />
  </div>
</template>

<style scoped lang="less">
:deep(.ant-tabs-tab-active) {
  font-weight: inherit !important;
  font-size: inherit !important;
}

.empty-wrap {
  position: relative;
  padding: 1px;
}

.grid-card {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  gap: 0 20px;
}
</style>
