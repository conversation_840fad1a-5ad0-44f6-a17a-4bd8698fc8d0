package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.poi.hpsf.Decimal;

import java.io.Serializable;

/**
 * KeySafetyVO对象
 *
 * <AUTHOR>
 * @since 2024-06-18 17:49:18
 */
@ApiModel(value = "KeySafetyVO对象", description = "安质环关键指标")
@Data
public class KeySafetyVO extends ObjectVO implements Serializable {
    /**
     * 类别
     */
    @ApiModelProperty(value = "类别")
    private String label;

    /**
     * 外部数量
     */
    @ApiModelProperty(value = "外部数量")
    private int outNum;

    /**
     * 内部数量
     */
    @ApiModelProperty(value = "内部数量")
    private int inNum;

    /**
     * 去年数量
     */
    @ApiModelProperty(value = "去年数量")
    private int lastNum;

    /**
     * 今年数量
     */
    @ApiModelProperty(value = "去年数量")
    private int num;

}
