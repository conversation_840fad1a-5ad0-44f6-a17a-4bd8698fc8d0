package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.bo.CodeBo;
import com.chinasie.orion.constant.ClassNameConstant;
import com.chinasie.orion.conts.BudgetEnum;
import com.chinasie.orion.dict.BudgetDict;
import com.chinasie.orion.domain.dto.BudgetAdjustmentDTO;
import com.chinasie.orion.domain.dto.BudgetAdjustmentFormDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.BudgetAdjustmentFormVO;
import com.chinasie.orion.domain.vo.BudgetAdjustmentVO;
import com.chinasie.orion.domain.vo.BudgetAdjustmentFormVO;
import com.chinasie.orion.number.api.domain.GenerateNumberRequest;
import com.chinasie.orion.number.api.sdk.NumberApiService;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.BudgetAdjustmentFromMapper;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.ehcache.core.util.CollectionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.math.BigDecimal;
import java.util.*;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;

import java.io.InputStream;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;


/**
 * <p>
 * BudgetAdjustmentFrom 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:07
 */
@Service
@Slf4j
public class BudgetAdjustmentFormServiceImpl extends OrionBaseServiceImpl<BudgetAdjustmentFromMapper, BudgetAdjustmentForm> implements BudgetAdjustmentFormService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;
    @Autowired
    private BudgetManagementService budgetManagementService;
    @Autowired
    private BudgetAdjustmentService budgetAdjustmentService;
    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private BudgetRecordService budgetRecordService;

    @Resource
    private ProjectService projectService;

    @Autowired
    private NumberApiService numberApiService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public BudgetAdjustmentFormVO detail(String id, String pageCode) throws Exception {
        BudgetAdjustmentForm budgetAdjustmentForm = this.getById(id);
        BudgetAdjustmentFormVO result = BeanCopyUtils.convertTo(budgetAdjustmentForm, BudgetAdjustmentFormVO::new);
        setEveryName(Collections.singletonList(result));
        List<BudgetAdjustmentVO> budgetAdjustmentVOS = budgetAdjustmentService.getDetailList(id);
        if(ObjectUtil.isNotEmpty(budgetAdjustmentVOS)) {
            result.setBudgetAdjustmentVOS(budgetAdjustmentVOS);
        }
        Project project =  projectService.getById(result.getProjectId());
        result.setProjectName(project.getName());
        result.setProjectNumber(project.getNumber());
        return result;
    }

    /**
     * 新增
     * <p>
     * * @param budgetAdjustmentFromDTO
     */
    @Override
    public String create(BudgetAdjustmentFormDTO budgetAdjustmentFormDTO) throws Exception {
        BudgetAdjustmentForm budgetAdjustmentForm = BeanCopyUtils.convertTo(budgetAdjustmentFormDTO, BudgetAdjustmentForm::new);
//        String code = codeBo.createCode(ClassNameConstant.BUDGET_ADJUSTMENT_FORM, ClassNameConstant.NUMBER, false, null);
//        budgetAdjustmentForm.setNumber(code);
        GenerateNumberRequest request = new GenerateNumberRequest();
        request.setClazzName(ClassNameConstant.BUDGET_ADJUSTMENT_FORM);
        String number =  numberApiService.generate(request);
        budgetAdjustmentForm.setNumber(number);
        this.save(budgetAdjustmentForm);
        if(!CollectionUtils.isEmpty(budgetAdjustmentFormDTO.getBudgetAdjustmentDTOS())){
            List<BudgetAdjustmentDTO>  budgetAdjustmentDTOS = budgetAdjustmentFormDTO.getBudgetAdjustmentDTOS();
            List<String> ids = budgetAdjustmentDTOS.stream().map(BudgetAdjustmentDTO::getBudgetId).collect(Collectors.toList());
            List<BudgetManagement> budgetManagements = budgetManagementService.listByIds(ids);
            List<BudgetAdjustment> budgetAdjustments = BeanCopyUtils.convertListTo(budgetManagements,BudgetAdjustment::new);
            List<BudgetAdjustment> changeAdjustment = new ArrayList<>();
            budgetAdjustments.forEach(item->{
                item.setBudgetId(item.getId());
                item.setId(null);
                item.setIsChange("0");
                item.setFormId(budgetAdjustmentForm.getId());
                BudgetAdjustment budgetAdjustment = new BudgetAdjustment();
                budgetAdjustment.setIsChange("1");
                budgetAdjustment.setBudgetId(item.getBudgetId());
                budgetAdjustment.setFormId(item.getFormId());
                budgetAdjustment.setProjectId(item.getProjectId());
                changeAdjustment.add(budgetAdjustment);
            });
            budgetAdjustments.addAll(changeAdjustment);
            budgetAdjustmentService.saveBatch(budgetAdjustments);
        }

        String rsp = budgetAdjustmentForm.getId();
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param budgetAdjustmentFromDTO
     */
    @Override
    public Boolean edit(BudgetAdjustmentFormDTO budgetAdjustmentFormDTO) throws Exception {
        BudgetAdjustmentForm budgetAdjustmentForm = BeanCopyUtils.convertTo(budgetAdjustmentFormDTO, BudgetAdjustmentForm::new);
        this.updateById(budgetAdjustmentForm);
        if(!CollUtil.isEmpty(budgetAdjustmentFormDTO.getBudgetAdjustmentDTOS())){
            List<BudgetAdjustmentDTO> budgetAdjustmentDTOS = budgetAdjustmentFormDTO.getBudgetAdjustmentDTOS();
            List<BudgetAdjustmentDTO> createBudget = budgetAdjustmentDTOS.stream().filter(item-> StrUtil.isBlank(item.getId())).collect(Collectors.toList());
            List<BudgetAdjustmentDTO> updateBudget = budgetAdjustmentDTOS.stream().filter(item-> StrUtil.isNotBlank(item.getId())).collect(Collectors.toList());
            if(CollUtil.isNotEmpty(updateBudget)){
                List<String> ids = updateBudget.stream().map(BudgetAdjustmentDTO::getBudgetId).collect(Collectors.toList());
                budgetAdjustmentService.remove(new LambdaQueryWrapperX<>(BudgetAdjustment.class)
                        .eq(BudgetAdjustment::getFormId,budgetAdjustmentFormDTO.getId())
                        .notIn(BudgetAdjustment::getBudgetId,ids));
            } else{
                budgetAdjustmentService.remove(new LambdaQueryWrapperX<>(BudgetAdjustment.class).eq(BudgetAdjustment::getFormId,budgetAdjustmentFormDTO.getId()));
            }
            if(CollUtil.isNotEmpty(createBudget)) {
                List<String> ids = createBudget.stream().map(BudgetAdjustmentDTO::getBudgetId).collect(Collectors.toList());
                List<BudgetManagement> budgetManagements = budgetManagementService.listByIds(ids);
                List<BudgetAdjustment> budgetAdjustments = BeanCopyUtils.convertListTo(budgetManagements, BudgetAdjustment::new);
                List<BudgetAdjustment> changeAdjustment = new ArrayList<>();
                budgetAdjustments.forEach(item -> {
                    item.setBudgetId(item.getId());
                    item.setId(null);
                    item.setIsChange("0");
                    item.setFormId(budgetAdjustmentForm.getId());
                    BudgetAdjustment budgetAdjustment = new BudgetAdjustment();
                    budgetAdjustment.setIsChange("1");
                    budgetAdjustment.setBudgetId(item.getBudgetId());
                    budgetAdjustment.setFormId(item.getFormId());
                    budgetAdjustment.setProjectId(item.getProjectId());
                    changeAdjustment.add(budgetAdjustment);

                });
                budgetAdjustments.addAll(changeAdjustment);
                budgetAdjustmentService.saveBatch(budgetAdjustments);
            }
        }else{
            budgetAdjustmentService.remove(new LambdaQueryWrapperX<>(BudgetAdjustment.class).eq(BudgetAdjustment::getFormId,budgetAdjustmentFormDTO.getId()));
        }
        String rsp = budgetAdjustmentForm.getId();
        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        budgetAdjustmentService.remove(new LambdaQueryWrapperX<>(BudgetAdjustment.class).in(BudgetAdjustment::getFormId,ids));
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<BudgetAdjustmentFormVO> pages(Page<BudgetAdjustmentFormDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<BudgetAdjustmentForm> condition = new LambdaQueryWrapperX<>(BudgetAdjustmentForm.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(BudgetAdjustmentForm::getCreateTime);


        Page<BudgetAdjustmentForm> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), BudgetAdjustmentForm::new));

        if(ObjectUtil.isNotEmpty(pageRequest.getQuery())){
            BudgetAdjustmentFormDTO budgetAdjustmentFormDTO = pageRequest.getQuery();
            condition.eq(BudgetAdjustmentForm::getProjectId,budgetAdjustmentFormDTO.getProjectId());
        }
        PageResult<BudgetAdjustmentForm> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<BudgetAdjustmentFormVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<BudgetAdjustmentFormVO> vos = BeanCopyUtils.convertListTo(page.getContent(), BudgetAdjustmentFormVO::new);
        if(CollUtil.isEmpty(vos)){
            return pageResult;
        }
        setEveryName(vos);
        Map<String, BudgetAdjustmentFormVO> map = vos.stream().collect(Collectors.toMap(BudgetAdjustmentFormVO::getId, e->e));
        List<String> ids = vos.stream().map(BudgetAdjustmentFormVO::getId).collect(Collectors.toList());
        LambdaQueryWrapperX<BudgetAdjustment> budgetAdjustmentLambdaQueryWrapperX = new LambdaQueryWrapperX<>(BudgetAdjustment.class);
        //TODO 6/12 审查 魏宇航 后续调整为函数
        budgetAdjustmentLambdaQueryWrapperX.select("form_id as formId , count(id) as budgetCount , sum(budget_money) as budgetMoney");
        budgetAdjustmentLambdaQueryWrapperX.in(BudgetAdjustment::getFormId,ids);
        budgetAdjustmentLambdaQueryWrapperX.in(BudgetAdjustment::getIsChange,1);
        budgetAdjustmentLambdaQueryWrapperX.groupBy(BudgetAdjustment::getFormId);
        List<Map<String,Object>> result = budgetAdjustmentService.listMaps(budgetAdjustmentLambdaQueryWrapperX);
        for(Map resultMap:result){
            if(ObjectUtil.isNotEmpty(map.get(resultMap.get("formId")))){
                BudgetAdjustmentFormVO budgetAdjustmentFormVO =map.get(resultMap.get("formId"));
                budgetAdjustmentFormVO.setAdjustmentNum(Integer.parseInt(resultMap.get("budgetCount").toString()));
                budgetAdjustmentFormVO.setAdjustmentMoney((BigDecimal) resultMap.get("budgetMoney"));
            }
        }
        pageResult.setContent(vos);
        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "预算支出表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", BudgetAdjustmentFormDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        BudgetAdjustmentFromExcelListener excelReadListener = new BudgetAdjustmentFromExcelListener();
        EasyExcel.read(inputStream, BudgetAdjustmentFormDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<BudgetAdjustmentFormDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("预算调整表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<BudgetAdjustmentForm> budgetAdjustmentFromes = BeanCopyUtils.convertListTo(dtoS, BudgetAdjustmentForm::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::BudgetAdjustmentFrom-import::id", importId, budgetAdjustmentFromes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<BudgetAdjustmentForm> budgetAdjustmentFromes = (List<BudgetAdjustmentForm>) orionJ2CacheService.get("pmsx::BudgetAdjustmentFrom-import::id", importId);
        log.info("预算调整表导入的入库数据={}", JSONUtil.toJsonStr(budgetAdjustmentFromes));

        this.saveBatch(budgetAdjustmentFromes);
        orionJ2CacheService.delete("pmsx::BudgetAdjustmentFrom-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::BudgetAdjustmentFrom-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<BudgetAdjustmentForm> condition = new LambdaQueryWrapperX<>(BudgetAdjustmentForm.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(BudgetAdjustmentForm::getCreateTime);
        List<BudgetAdjustmentForm> budgetAdjustmentFromes = this.list(condition);

        List<BudgetAdjustmentFormDTO> dtos = BeanCopyUtils.convertListTo(budgetAdjustmentFromes, BudgetAdjustmentFormDTO::new);

        String fileName = "预算调整表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", BudgetAdjustmentFormDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<BudgetAdjustmentFormVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }

    @Override
    public void changBudget(String id) throws Exception {
        BudgetAdjustmentForm budgetAdjustmentForm = this.getById(id);
        List<BudgetAdjustment> budgetAdjustments = budgetAdjustmentService.list(new LambdaQueryWrapperX<>(BudgetAdjustment.class)
                .eq(BudgetAdjustment::getFormId,id)
                .eq(BudgetAdjustment::getIsChange,1));

        List<String> ids = budgetAdjustments.stream().map(BudgetAdjustment::getBudgetId).collect(Collectors.toList());

        List<BudgetManagement> budgetManagements = budgetManagementService.list(new LambdaQueryWrapper<>(BudgetManagement.class)
                .in(BudgetManagement::getId, ids));
        Map<String, BudgetManagement> budgetMap = budgetManagements.stream()
                .collect(Collectors.toMap(
                        BudgetManagement::getId,
                        Function.identity()
                ));
        List<BudgetManagement> updateList = new ArrayList<>();
        List<BudgetRecord> budgetRecords = new ArrayList<>();
        for(BudgetAdjustment budgetAdjustment:budgetAdjustments){
            BudgetManagement budgetManagement = budgetMap.get(budgetAdjustment.getBudgetId());
            if (ObjectUtil.isNotEmpty(budgetManagement)) {
                budgetManagement.setBudgetMoney(budgetManagement.getBudgetMoney().add(budgetAdjustment.getBudgetMoney()));
                budgetManagement.setResidueMoney(budgetManagement.getResidueMoney().add(budgetAdjustment.getBudgetMoney()));
                BudgetRecord budgetRecord = new BudgetRecord();
                budgetRecord.setBudgetId(budgetManagement.getId());
                budgetRecord.setBudgetChangeId(budgetAdjustmentForm.getId());
                budgetRecord.setChangeMoney(budgetAdjustment.getBudgetMoney());
                budgetRecord.setAfterChangeMoney(budgetManagement.getBudgetMoney());
                budgetRecord.setBudgetChangeName(budgetAdjustmentForm.getName());
                budgetRecord.setBudgetChangeNumber(budgetAdjustmentForm.getNumber());
                budgetRecord.setOperationTime(new Date());
                budgetRecord.setBudgetChangeType(BudgetEnum.ADJUSTMENT.getCode());
                budgetRecord.setOperationPerson(budgetAdjustment.getCreatorId());
                budgetRecords.add(budgetRecord);
                if (budgetManagement.getTimeType().equals("budgetMonth")) {
                    if(ObjectUtil.isNotEmpty(budgetAdjustment.getJanuaryMoney())){
                        budgetManagement.setJanuaryMoney(((budgetManagement.getJanuaryMoney() != null) ?budgetManagement.getJanuaryMoney() : BigDecimal.ZERO).add(budgetAdjustment.getJanuaryMoney()));
                    }
                    if(ObjectUtil.isNotEmpty( budgetManagement.getFebruaryMoney())) {
                        budgetManagement.setFebruaryMoney(((budgetManagement.getFebruaryMoney() != null) ?budgetManagement.getFebruaryMoney() : BigDecimal.ZERO).add(budgetAdjustment.getFebruaryMoney()));
                    }
                    if(ObjectUtil.isNotEmpty( budgetManagement.getMarchMoney())) {
                        budgetManagement.setMarchMoney(((budgetManagement.getMarchMoney() != null) ?budgetManagement.getMarchMoney() : BigDecimal.ZERO).add(budgetAdjustment.getMarchMoney()));
                    }
                    if(ObjectUtil.isNotEmpty( budgetManagement.getAprilMoney())) {
                        budgetManagement.setAprilMoney(((budgetManagement.getAprilMoney() != null) ?budgetManagement.getAprilMoney() : BigDecimal.ZERO).add(budgetAdjustment.getAprilMoney()));
                    }

                    if(ObjectUtil.isNotEmpty( budgetManagement.getMayMoney())) {
                        budgetManagement.setMayMoney(((budgetManagement.getMayMoney() != null) ?budgetManagement.getMayMoney() : BigDecimal.ZERO).add(budgetAdjustment.getMayMoney()));
                    }
                    if(ObjectUtil.isNotEmpty( budgetManagement.getJuneMoney())) {
                        budgetManagement.setJuneMoney(((budgetManagement.getJuneMoney() != null) ?budgetManagement.getJuneMoney() : BigDecimal.ZERO).add(budgetAdjustment.getJuneMoney()));
                    }

                    if(ObjectUtil.isNotEmpty( budgetManagement.getJulyMoney())) {
                        budgetManagement.setJulyMoney(((budgetManagement.getJulyMoney() != null) ?budgetManagement.getJulyMoney() : BigDecimal.ZERO).add(budgetAdjustment.getJulyMoney()));
                    }

                    if(ObjectUtil.isNotEmpty( budgetManagement.getAugustMoney())) {
                        budgetManagement.setAugustMoney(((budgetManagement.getAugustMoney() != null) ?budgetManagement.getAugustMoney() : BigDecimal.ZERO).add(budgetAdjustment.getAugustMoney()));
                    }
                    if(ObjectUtil.isNotEmpty( budgetManagement.getSeptemberMoney())) {
                        budgetManagement.setSeptemberMoney(((budgetManagement.getSeptemberMoney() != null) ?budgetManagement.getSeptemberMoney() : BigDecimal.ZERO).add(budgetAdjustment.getSeptemberMoney()));
                    }
                    if(ObjectUtil.isNotEmpty( budgetManagement.getOctoberMoney())) {
                        budgetManagement.setOctoberMoney(((budgetManagement.getOctoberMoney() != null) ?budgetManagement.getOctoberMoney() : BigDecimal.ZERO).add(budgetAdjustment.getOctoberMoney()));
                    }
                    if(ObjectUtil.isNotEmpty( budgetManagement.getNovemberMoney())) {
                        budgetManagement.setNovemberMoney(((budgetManagement.getNovemberMoney() != null) ?budgetManagement.getNovemberMoney() : BigDecimal.ZERO).add(budgetAdjustment.getNovemberMoney()));
                    }
                    if(ObjectUtil.isNotEmpty( budgetManagement.getDecemberMoney())) {
                        budgetManagement.setDecemberMoney(((budgetManagement.getDecemberMoney() != null) ?budgetManagement.getDecemberMoney() : BigDecimal.ZERO).add(budgetAdjustment.getDecemberMoney()));
                    }
                }
                if(budgetManagement.getTimeType().equals("budgetQuarter")){
                    if(ObjectUtil.isNotEmpty( budgetManagement.getFirstQuarterMoney())) {
                        budgetManagement.setFirstQuarterMoney(((budgetManagement.getFirstQuarterMoney() != null) ?budgetManagement.getFirstQuarterMoney() : BigDecimal.ZERO).add(budgetAdjustment.getFirstQuarterMoney()));
                    }
                    if(ObjectUtil.isNotEmpty( budgetManagement.getSecondQuarter())) {
                        budgetManagement.setSecondQuarter(((budgetManagement.getSecondQuarter() != null) ?budgetManagement.getOctoberMoney() : BigDecimal.ZERO).add(budgetAdjustment.getSecondQuarter()));
                    }
                    if(ObjectUtil.isNotEmpty( budgetManagement.getThirdQuarter())) {
                        budgetManagement.setThirdQuarter(((budgetManagement.getThirdQuarter() != null) ?budgetManagement.getThirdQuarter() : BigDecimal.ZERO).add(budgetAdjustment.getThirdQuarter()));
                    }
                    if(ObjectUtil.isNotEmpty( budgetManagement.getFourthQuarter())) {
                        budgetManagement.setFourthQuarter(((budgetManagement.getFourthQuarter() != null) ?budgetManagement.getFourthQuarter() : BigDecimal.ZERO).add(budgetAdjustment.getFourthQuarter()));
                    }
                }
                updateList.add(budgetManagement);
            }
        }
        budgetManagementService.updateBatchById(updateList);
        budgetRecordService.saveBatch(budgetRecords);
    }


    public static class BudgetAdjustmentFromExcelListener extends AnalysisEventListener<BudgetAdjustmentFormDTO> {

        private final List<BudgetAdjustmentFormDTO> data = new ArrayList<>();

        @Override
        public void invoke(BudgetAdjustmentFormDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }
        public List<BudgetAdjustmentFormDTO> getData() {
            return data;
        }
    }


}
