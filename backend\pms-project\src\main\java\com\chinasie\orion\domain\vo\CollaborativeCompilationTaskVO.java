package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

import java.util.List;

/**
 * CollaborativeCompilationTask VO对象
 *
 * <AUTHOR>
 * @since 2024-06-04 17:04:08
 */
@ApiModel(value = "CollaborativeCompilationTaskVO对象", description = "协同编制任务表")
@Data
public class CollaborativeCompilationTaskVO extends ObjectVO implements Serializable {

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;


    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;


    /**
     * 立项Id
     */
    @ApiModelProperty(value = "立项Id")
    private String approvalId;


    /**
     * 层级
     */
    @ApiModelProperty(value = "层级")
    private Integer level;


    /**
     * 父id
     */
    @ApiModelProperty(value = "父id")
    private String parentId;


    /**
     * 父id
     */
    @ApiModelProperty(value = "父id")
    private String parentName;


    /**
     * 责任部门
     */
    @ApiModelProperty(value = "责任部门")
    private String rspDept;

    @ApiModelProperty(value = "责任部门名称")
    private String rspDeptName;


    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    private String rspUser;

    @ApiModelProperty(value = "责任人名称")
    private String rspUserName;


    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    private Date beginTime;


    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    private Date endTime;


    /**
     * 计划情况
     */
    @ApiModelProperty(value = "计划情况")
    private Integer circumstance;

    @ApiModelProperty(value = "计划情况名称")
    private String circumstanceName;

    /**
     * 实际结束时间
     */
    @ApiModelProperty(value = "实际结束时间")
    private Date actualEndTime;


    /**
     * 实际开始时间
     */
    @ApiModelProperty(value = "实际开始时间")
    private Date actualBeginTime;


    /**
     * 父级链
     */
    @ApiModelProperty(value = "父级链")
    private String parentChain;


    /**
     * 任务描述
     */
    @ApiModelProperty(value = "任务描述")
    private String taskDesc;


    /**
     * 置顶序号（0：取消置顶）
     */
    @ApiModelProperty(value = "置顶序号（0：取消置顶）")
    private Integer topSort;


    /**
     * 执行情况说明
     */
    @ApiModelProperty(value = "执行情况说明")
    private String executeDesc;


    /**
     * 下发计划时间
     */
    @ApiModelProperty(value = "下发计划时间")
    private Date issueTime;


    /**
     * 计划工期
     */
    @ApiModelProperty(value = "计划工期")
    private Integer durationDays;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 下达人
     */
    @ApiModelProperty(value = "下达人")
    private String issuedUser;

    /**
     * 下达人
     */
    @ApiModelProperty(value = "下达人")
    private String IssuedUserName;


    /**
     * 确认理由
     */
    @ApiModelProperty(value = "确认理由")
    private String reasonConfirmation;


    /**
     * 上一个状态
     */
    @ApiModelProperty(value = "上一个状态")
    private Integer lastStatus;


    /**
     * 任务内容
     */
    @ApiModelProperty(value = "任务内容")
    private String content;


    /**
     * 处理实例
     */
    @ApiModelProperty(value = "处理实例")
    private String processInstances;

    @ApiModelProperty(value = "处理实例")
    private String processInstancesName;


    /**
     * 处理对象
     */
    @ApiModelProperty(value = "处理对象")
    private String processObject;

    @ApiModelProperty(value = "处理名称")
    private String processObjectName;

    @ApiModelProperty(value = "是否前置关系")
    private Boolean isPrePost;


    @ApiModelProperty(value = "子级计划")
    private List<CollaborativeCompilationTaskVO> children;

    @ApiModelProperty(value = "项目计划前置关系")
    private List<ApprovalTaskPrePostVO> taskPreVOList = new ArrayList<>();

    @ApiModelProperty(value = "项目计划后置关系")
    private List<ApprovalTaskPrePostVO> taskPostVOList = new ArrayList<>();


    @ApiModelProperty(value = "项目计划前后置关系")
    private List<ApprovalTaskPrePostVO>  taskPrePostVOS = new ArrayList<>();

    @ApiModelProperty(value = "转办理由")
    private String  reasonTransfer;
}
