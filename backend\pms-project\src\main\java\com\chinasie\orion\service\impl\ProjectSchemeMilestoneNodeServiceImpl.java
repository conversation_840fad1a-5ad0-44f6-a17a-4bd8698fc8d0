package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.CellExtra;
import com.alibaba.excel.read.listener.ReadListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.ProjectSchemeNodeTypeEnum;
import com.chinasie.orion.dict.SchemeDict;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.dto.ProjectSchemeMilestoneNodeDTO;
import com.chinasie.orion.domain.dto.ProjectSchemeMilestoneNodeExcelDTO;
import com.chinasie.orion.domain.entity.ProjectSchemeMilestoneNode;
import com.chinasie.orion.domain.entity.ProjectSchemeMilestoneNodePrePost;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.exception.BaseErrorCode;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.ProjectSchemeMilestoneNodeRepository;
import com.chinasie.orion.sdk.domain.vo.business.ClassVO;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.org.SimpleDeptVO;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectSchemeMilestoneNodePrePostService;
import com.chinasie.orion.service.ProjectSchemeMilestoneNodeService;
import com.chinasie.orion.service.ProjectSchemeMilestoneTemplateService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.TreeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * ProjectSchemeMilestoneNode 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-05 10:30:49
 */
@Service
@Slf4j
public class ProjectSchemeMilestoneNodeServiceImpl extends OrionBaseServiceImpl<ProjectSchemeMilestoneNodeRepository, ProjectSchemeMilestoneNode> implements ProjectSchemeMilestoneNodeService {

    @Autowired
    private ProjectSchemeMilestoneTemplateService projectSchemeMilestoneTemplateService;

    @Autowired
    private ClassRedisHelper classRedisHelper;

    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Autowired
    private ProjectSchemeMilestoneNodePrePostService projectSchemeMilestoneNodePrePostService;

    @Autowired
    private DictRedisHelper dictRedisHelper;


    public static final Map<String, String> PLAN_TYPE_NAME_MAP = new HashMap<>() {{
        //TODO 整改 其他地方使用一些魔法值，这些地方也存在，需要提供到公共位置
        put("里程碑", "milestone");
        put("计划", "plan");
        put("任务模块", "taskModule");
    }};
    public static final Map<String, String> PLAN_TYPE_MAP = new HashMap<>() {{
        put("milestone", "里程碑");
        put("plan", "计划");
        put("taskModule", "任务模块");
    }};


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public ProjectSchemeMilestoneNodeVO detail(String id) throws Exception {
        ProjectSchemeMilestoneNode projectSchemeMilestoneNode = this.getById(id);
        ProjectSchemeMilestoneNodeVO result = BeanCopyUtils.convertTo(projectSchemeMilestoneNode, ProjectSchemeMilestoneNodeVO::new);
        //获取前置计划
        //设置前后置计划
        List<ProjectSchemeMilestoneNodePrePost> projectSchemeMilestoneNodePrePosts = projectSchemeMilestoneNodePrePostService
                .list(new LambdaQueryWrapper<>(ProjectSchemeMilestoneNodePrePost.class).eq(ProjectSchemeMilestoneNodePrePost::getProjectSchemeId, id));

        List<ProjectSchemeMilestoneNodePrePostVO> projectSchemeMilestoneNodePrePostVOS = BeanCopyUtils.convertListTo(projectSchemeMilestoneNodePrePosts, ProjectSchemeMilestoneNodePrePostVO::new);
        projectSchemeMilestoneNodePrePostVOS.forEach(o -> {
            o.setProjectSchemeName(projectSchemeMilestoneNode.getName());
        });

        Map<Integer, List<ProjectSchemeMilestoneNodePrePostVO>> prePostMapVOMap = projectSchemeMilestoneNodePrePostVOS.stream().collect(Collectors.groupingBy(ProjectSchemeMilestoneNodePrePostVO::getType));

        result.setIsPrePost(prePostMapVOMap.containsKey(Status.SCHEME_PRE.getCode()));
        result.setSchemePrePostVOList(prePostMapVOMap.getOrDefault(Status.SCHEME_PRE.getCode(), new ArrayList<>()));
        result.setSchemePostVOList(prePostMapVOMap.getOrDefault(Status.SCHEME_POST.getCode(), new ArrayList<>()));
        result.setProjectSchemePrePostVOS(prePostMapVOMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList()));

        return result;
    }


    /**
     * 是否前置计划
     *
     * @param templateId 项目id
     * @param schemeIds  计划id列表
     * @return
     * @throws Exception
     */
    private Map<String, List<ProjectSchemeMilestoneNodePrePostVO>> isPrePost(String templateId, List<String> schemeIds) throws Exception {
        Map<String, List<ProjectSchemeMilestoneNodePrePostVO>> isPrePostMap = new HashMap<>();
        if (CollectionUtils.isEmpty(schemeIds)) {
            return isPrePostMap;
        }

        //获取前置计划
        LambdaQueryWrapper<ProjectSchemeMilestoneNodePrePost> prePostWrapper = new LambdaQueryWrapper<>(ProjectSchemeMilestoneNodePrePost.class)
                .eq(ProjectSchemeMilestoneNodePrePost::getTemplateId, templateId)
                .eq(ProjectSchemeMilestoneNodePrePost::getType, Status.SCHEME_PRE.getCode())
                .in(ProjectSchemeMilestoneNodePrePost::getProjectSchemeId, schemeIds);
        List<ProjectSchemeMilestoneNodePrePost> prePostList = projectSchemeMilestoneNodePrePostService.list(prePostWrapper);
        if (CollectionUtils.isEmpty(prePostList)) {
            return isPrePostMap;
        }
        List<String> prePostSchemeIds = prePostList.stream().map(ProjectSchemeMilestoneNodePrePost::getPreSchemeId).collect(Collectors.toList());

        List<ProjectSchemeMilestoneNodeVO> prePostSchemeVOS = BeanCopyUtils.convertListTo(this.listByIds(prePostSchemeIds), ProjectSchemeMilestoneNodeVO::new);


        Map<String, ProjectSchemeMilestoneNodeVO> prePostSchemeMap = prePostSchemeVOS.stream().peek(p -> p.setTypeName(ProjectSchemeMilestoneNodeServiceImpl.PLAN_TYPE_MAP.get(p.getNodeType()))).collect(Collectors.toMap(ProjectSchemeMilestoneNodeVO::getId, Function.identity()));

        List<ProjectSchemeMilestoneNodePrePostVO> result = BeanCopyUtils.convertListTo(prePostList, ProjectSchemeMilestoneNodePrePostVO::new);
        isPrePostMap = result.stream().peek(prePost -> {
            ProjectSchemeMilestoneNodeVO vo = prePostSchemeMap.get(prePost.getPreSchemeId());
            if (null == vo) {
                return;
            }
        }).collect(Collectors.groupingBy(ProjectSchemeMilestoneNodePrePostVO::getProjectSchemeId, Collectors.toList()));
        return isPrePostMap;
    }


    /**
     * 新增
     * <p>
     * * @param projectSchemeMilestoneNodeDTO
     */
    @Override
    public ProjectSchemeMilestoneNodeVO create(ProjectSchemeMilestoneNodeDTO projectSchemeMilestoneNodeDTO) {
        ProjectSchemeMilestoneNode projectSchemeMilestoneNode = BeanCopyUtils.convertTo(projectSchemeMilestoneNodeDTO, ProjectSchemeMilestoneNode::new);
        if (StrUtil.isBlank(projectSchemeMilestoneNode.getParentId()) || StrUtil.equals("0", projectSchemeMilestoneNode.getParentId())) {
            projectSchemeMilestoneNode.setParentId("0");
            projectSchemeMilestoneNode.setNodeChain("0");
        } else {
            ProjectSchemeMilestoneNode parent = this.getById(projectSchemeMilestoneNode.getParentId());
            projectSchemeMilestoneNode.setNodeChain(parent.getNodeChain() + "," + parent.getId());
        }
        this.save(projectSchemeMilestoneNode);
        ProjectSchemeMilestoneNodeVO rsp = BeanCopyUtils.convertTo(projectSchemeMilestoneNode, ProjectSchemeMilestoneNodeVO::new);
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param projectSchemeMilestoneNodeDTO
     */
    @Override
    public Boolean edit(ProjectSchemeMilestoneNodeDTO projectSchemeMilestoneNodeDTO) throws Exception {
        ProjectSchemeMilestoneNode projectSchemeMilestoneNode = BeanCopyUtils.convertTo(projectSchemeMilestoneNodeDTO, ProjectSchemeMilestoneNode::new);
        // nodeNameRepeatHandle(projectSchemeMilestoneNode, CollUtil.toList());
        Boolean result = this.updateById(projectSchemeMilestoneNode);
        return result;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        Boolean result = this.removeBatchByIds(ids);

        projectSchemeMilestoneNodePrePostService.remove(new LambdaQueryWrapperX<>(ProjectSchemeMilestoneNodePrePost.class)
                .and(sub -> sub.in(ProjectSchemeMilestoneNodePrePost::getProjectSchemeId, ids).or().in(ProjectSchemeMilestoneNodePrePost::getPreSchemeId, ids))
        );
        return result;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public Page<ProjectSchemeMilestoneNodeVO> pages(Page<ProjectSchemeMilestoneNodeDTO> pageRequest) throws Exception {
        IPage<ProjectSchemeMilestoneNode> realPageRequest = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());

        LambdaQueryWrapperX<ProjectSchemeMilestoneNode> wrapper = new LambdaQueryWrapperX<>();
        wrapper.selectAll(ProjectSchemeMilestoneNode.class);
        ProjectSchemeMilestoneNodeDTO query = pageRequest.getQuery();
        if (query != null && StringUtils.hasText(query.getTemplateId())) {
            wrapper.eq(ProjectSchemeMilestoneNode::getTemplateId, query.getTemplateId());
            wrapper.orderByAsc(ProjectSchemeMilestoneNode::getSort);
        }
        IPage<ProjectSchemeMilestoneNode> page = this.page(realPageRequest, wrapper);

        Page<ProjectSchemeMilestoneNodeVO> pageResult = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        List<ProjectSchemeMilestoneNodeVO> vos = BeanCopyUtils.convertListTo(page.getRecords(), ProjectSchemeMilestoneNodeVO::new);
        Map<String, DictValueVO> planActiveMap = dictRedisHelper.getDictMapByCode(SchemeDict.PLAN_ACTIVE);
        Map<String, Map<String, String>> dictMap = new HashMap<>();
        planActiveMap.forEach((key, value) -> {
            Map<String, String> map = new HashMap<>();
            map.put("name", value.getDescription());
            map.put("value", key);
            dictMap.put(key, map);
        });
        vos.forEach(item->{
            if (StrUtil.isNotBlank(item.getPlanActive())) {
                List<String> names = List.of(item.getPlanActive().split(","));
                List<Map<String, String>> mapList = new ArrayList<>();
                for (String name : names) {
                    if (ObjectUtil.isNotEmpty(dictMap.get(name))) {
                        mapList.add(dictMap.get(name));
                    }
                }
                item.setPlanActiveList(mapList);
            }
        });

        pageResult.setContent(vos);

        return pageResult;
    }


    /**
     * 重复名字 给名字添加数字
     *
     * @param milestoneExcelDTOS
     * @throws Exception
     */
    private void nodeNameHandle(List<ProjectSchemeMilestoneNode> milestoneExcelDTOS) {
        List<String> imNodeNames = CollUtil.toList();
        milestoneExcelDTOS.forEach(item -> nodeNameRepeatHandle(item, imNodeNames));
    }

    private void nodeNameRepeatHandle(ProjectSchemeMilestoneNode node, List<String> imNodeNames) {
        List<ProjectSchemeMilestoneNode> projectSchemeMilestoneNodes = CollUtil.toList();
        try {
            projectSchemeMilestoneNodes = this.list(new LambdaQueryWrapper<>(ProjectSchemeMilestoneNode.class)
                    .eq(ProjectSchemeMilestoneNode::getTemplateId, node.getTemplateId()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        int size = projectSchemeMilestoneNodes.stream().filter(Objects::isNull)
                .filter(item -> item.getNodeName().equals(node.getNodeName())).collect(Collectors.toList())
                .size();
        if (0 == size) {
            return;
        }
        int num = 0;
        List<ProjectSchemeMilestoneNode> repeatNodeNames = projectSchemeMilestoneNodes.stream().filter(Objects::nonNull)
                .filter(item -> {
                    if (StrUtil.isNotBlank(item.getNodeName()) && StrUtil.contains(item.getNodeName(), "(")) {
                        return item.getNodeName().substring(0, item.getNodeName().lastIndexOf("(")).equals(node.getNodeName());
                    }
                    return node.getNodeName().equals(item.getNodeName());
                }).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(repeatNodeNames)) {
            num += repeatNodeNames.size();
        }
        if (imNodeNames.contains(node.getNodeName())) {
            num += 1;
        } else {
            imNodeNames.add(node.getNodeName());
        }
        if (num != 0) {
            node.setNodeName(node.getNodeName() + "(" + num + ")");
        }
    }


    @Override
    public List<ProjectSchemeMilestoneNodeVO> tree(String tplId) {
        LambdaQueryWrapperX<ProjectSchemeMilestoneNode> wrapper = new LambdaQueryWrapperX<>();
        wrapper.selectAll(ProjectSchemeMilestoneNode.class);
        wrapper.eq(ProjectSchemeMilestoneNode::getTemplateId, tplId);
        wrapper.orderByAsc(ProjectSchemeMilestoneNode::getSort);
        List<ProjectSchemeMilestoneNode> projectSchemeMilestoneNodes = this.list(wrapper);

        List<ProjectSchemeMilestoneNodeVO> projectSchemeMilestoneNodeVOS = BeanCopyUtils.convertListTo(projectSchemeMilestoneNodes, ProjectSchemeMilestoneNodeVO::new);

        projectSchemeMilestoneNodeVOS.forEach(vo -> {
            vo.setNodeChainName(vo.getNodeChain().split(",").length + "层");
            if (StrUtil.isNotBlank(vo.getRspDeptId())) {
                DeptVO organization = deptRedisHelper.getDeptById(vo.getRspDeptId());
                if (Objects.nonNull(organization)) {
                    vo.setRspDeptName(organization.getName());
                }

            }

        });

        Map<String, String> nameMap = projectSchemeMilestoneNodeVOS.stream().collect(Collectors.toMap(ProjectSchemeMilestoneNodeVO::getId, ProjectSchemeMilestoneNodeVO::getName));


        //设置前后置计划
        List<ProjectSchemeMilestoneNodePrePost> projectSchemeMilestoneNodePrePosts = projectSchemeMilestoneNodePrePostService.list(new LambdaQueryWrapper<>(ProjectSchemeMilestoneNodePrePost.class).eq(ProjectSchemeMilestoneNodePrePost::getTemplateId, tplId));

        Map<String, List<ProjectSchemeMilestoneNodePrePost>> prePostMap = projectSchemeMilestoneNodePrePosts.stream().collect(Collectors.groupingBy(ProjectSchemeMilestoneNodePrePost::getProjectSchemeId));
        Map<String, Map<Integer, List<ProjectSchemeMilestoneNodePrePostVO>>> prePostMapVOMap = new HashMap<>();
        prePostMap.forEach((k, v) -> {
            List<ProjectSchemeMilestoneNodePrePostVO> projectSchemeMilestoneNodePrePostVOS = BeanCopyUtils.convertListTo(v, ProjectSchemeMilestoneNodePrePostVO::new);
            projectSchemeMilestoneNodePrePostVOS.forEach(o -> {
                if (StrUtil.isNotBlank(o.getPreSchemeId())) {
                    o.setProjectSchemeName(nameMap.get(o.getPreSchemeId()));
                } else {
                    o.setProjectSchemeName(nameMap.get(o.getPostSchemeId()));
                }

            });

            prePostMapVOMap.put(k, projectSchemeMilestoneNodePrePostVOS.stream().collect(Collectors.groupingBy(ProjectSchemeMilestoneNodePrePostVO::getType)));
        });

//        Map<String, List<ProjectSchemeMilestoneNodePrePost>> postMap = projectSchemeMilestoneNodePrePosts.stream().filter(f -> StrUtil.isNotBlank(f.getPostSchemeId())).collect(Collectors.groupingBy(ProjectSchemeMilestoneNodePrePost::getPostSchemeId));
//        Map<String, List<ProjectSchemeMilestoneNodePrePostVO>> postVOMap = new HashMap<>();
//        postMap.forEach((k, v) -> {
//            List<ProjectSchemeMilestoneNodePrePostVO> projectSchemeMilestoneNodePrePostVOS = BeanCopyUtils.convertListTo(v, ProjectSchemeMilestoneNodePrePostVO::new);
//
//            projectSchemeMilestoneNodePrePostVOS.forEach(o -> {
//                o.setProjectSchemeName(nameMap.get(o.getProjectSchemeId()));
//            });
//
//            postVOMap.put(k, projectSchemeMilestoneNodePrePostVOS);
//        });


        projectSchemeMilestoneNodeVOS.forEach(o -> {
            if (prePostMapVOMap.containsKey(o.getId())) {
                o.setSchemePrePostVOList(prePostMapVOMap.get(o.getId()).getOrDefault(Status.SCHEME_PRE.getCode(), new ArrayList<>()));
                o.setSchemePostVOList(prePostMapVOMap.get(o.getId()).getOrDefault(Status.SCHEME_POST.getCode(), new ArrayList<>()));
            }
        });

        List<ProjectSchemeMilestoneNodeVO> rsp = TreeUtil.listToTree(projectSchemeMilestoneNodeVOS,
                ProjectSchemeMilestoneNodeVO::getId,
                ProjectSchemeMilestoneNodeVO::getParentId,
                ProjectSchemeMilestoneNodeVO::getChildren,
                ProjectSchemeMilestoneNodeVO::setChildren
        );

        return rsp;
    }


    @Override
    public List<String> createBatch(List<ProjectSchemeMilestoneNodeDTO> projectSchemeMilestoneNodeDTOS) {
        List<String> rsp = new ArrayList<>();
        projectSchemeMilestoneNodeDTOS.forEach(dto -> {
            ProjectSchemeMilestoneNodeVO projectSchemeMilestoneNodeVO = create(dto);
            rsp.add(projectSchemeMilestoneNodeVO.getId());
        });
        return rsp;
    }


    public static class MilestoneExcelReadListener implements ReadListener<ProjectSchemeMilestoneNodeExcelDTO> {
        private List<ProjectSchemeMilestoneNodeExcelDTO> milestoneExcelDTOS = new ArrayList<>();

        private Map<Integer, List<String>> errorMessageMap = new HashMap<>();

        public Map<Integer, List<String>> getErrorMessageMap() {
            return errorMessageMap;
        }


        public List<ProjectSchemeMilestoneNodeExcelDTO> getMilestoneExcelDTOS() {
            return milestoneExcelDTOS;
        }

        @Override
        public void invoke(ProjectSchemeMilestoneNodeExcelDTO data, AnalysisContext context) {
            Integer rowIndex = context.readRowHolder().getRowIndex();//行号
            List<String> errorMessages = new ArrayList<>();
            if (StrUtil.isBlank(data.getParentName())) {
                errorMessages.add("父级计划名称必填");
            }
            if (StrUtil.isBlank(data.getName())) {
                errorMessages.add("计划名称必填");
            }
            if (StrUtil.isBlank(data.getPlanType())) {
                errorMessages.add("计划类别必填");
            }
            if (StrUtil.isBlank(data.getProcessFlagName())) {
                errorMessages.add("是否关联流程必填");
            }
            if (!CollectionUtils.isEmpty(errorMessages)) {
                errorMessageMap.put(rowIndex, errorMessages);
            }
            data.setRowIndex(rowIndex);
            milestoneExcelDTOS.add(data);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {

        }

        @Override
        public void extra(CellExtra extra, AnalysisContext context) {
        }
    }


    @Override
    public ImportExcelCheckResultVO importByExcelCheck(MultipartFile file, String tplId) throws Exception {
        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();

        if (StrUtil.isBlank(tplId)) {
            String fileName = file.getOriginalFilename();
            String templateName = fileName.substring(0, fileName.lastIndexOf("."));
            ProjectSchemeMilestoneTemplateVO projectSchemeMilestoneTemplateVO = projectSchemeMilestoneTemplateService.saveNoExist(templateName);
            tplId = projectSchemeMilestoneTemplateVO.getId();
        }
        InputStream inputStream = file.getInputStream();
        MilestoneExcelReadListener milestoneExcelReadListener = new MilestoneExcelReadListener();

        EasyExcel.read(inputStream, ProjectSchemeMilestoneNodeExcelDTO.class, milestoneExcelReadListener).sheet().headRowNumber(1).doRead();
        List<ProjectSchemeMilestoneNodeExcelDTO> milestoneExcelDTOS = milestoneExcelReadListener.getMilestoneExcelDTOS();
        log.info("计划导入的数据={}", JSONUtil.toJsonStr(milestoneExcelDTOS));
        if (CollectionUtils.isEmpty(milestoneExcelDTOS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (milestoneExcelDTOS.size() > 100) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }

        Map<Integer, List<String>> errorMessageMap = milestoneExcelReadListener.getErrorMessageMap();

        List<ProjectSchemeMilestoneNode> projectSchemeMilestoneNodes = new ArrayList<>();

        List<ProjectSchemeMilestoneNode> dbProjectSchemeMilestoneNodes = this.list(new LambdaQueryWrapper<>(ProjectSchemeMilestoneNode.class).eq(ProjectSchemeMilestoneNode::getTemplateId, tplId));

        Map<String, ProjectSchemeMilestoneNode> dbProjectSchemeMilestoneNodesNameMap = dbProjectSchemeMilestoneNodes.stream().collect(Collectors.toMap(ProjectSchemeMilestoneNode::getName, Function.identity(), (v1, v2) -> v2));

        ClassVO classVO = classRedisHelper.getClassByClassName(ProjectSchemeMilestoneNode.class.getSimpleName());

        String finalTplId = tplId;
        milestoneExcelDTOS.stream().sorted(Comparator.comparing(ProjectSchemeMilestoneNodeExcelDTO::getRowIndex)).forEach(excel -> {
            ProjectSchemeMilestoneNode tmp = new ProjectSchemeMilestoneNode();
            String id = String.format("%s%s", Objects.isNull(classVO) ? "1gvz" : classVO.getCode(), IdUtil.getSnowflakeNextIdStr());
            tmp.setId(id);
            tmp.setName(excel.getName());
            tmp.setDescription(excel.getRemark());
            tmp.setRemark(excel.getRemark());
            if (excel.getDurationDays().matches("^[0-9]*[1-9][0-9]*$")) {
                tmp.setDurationDays(Integer.parseInt(excel.getDurationDays()));
            } else {
                List<String> errorMessages = errorMessageMap.getOrDefault(excel.getRowIndex(), new ArrayList<>());
                errorMessages.add("计划工期（天）未填写正整数");
                errorMessageMap.put(excel.getRowIndex(), errorMessages);
            }

            tmp.setProcessFlag(StrUtil.equals("是", excel.getProcessFlagName()));
            tmp.setTemplateId(finalTplId);
            tmp.setNodeType(PLAN_TYPE_NAME_MAP.get(excel.getPlanType()));

            List<SimpleDeptVO> depts = deptRedisHelper.getAllSimpleDept().stream().filter(o -> StrUtil.equals(o.getName(), excel.getRspDeptName())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(depts)) {
                List<String> errorMessages = errorMessageMap.getOrDefault(excel.getRowIndex(), new ArrayList<>());
                errorMessages.add("责任部门不存在");
                errorMessageMap.put(excel.getRowIndex(), errorMessages);
            } else {
                tmp.setRspDeptId(depts.get(0).getId());
            }

            if (StrUtil.equals("无", excel.getParentName())) {
                tmp.setNodeChain("0");
                tmp.setParentId("0");
                dbProjectSchemeMilestoneNodesNameMap.put(excel.getName(), tmp);
            } else {
                if (dbProjectSchemeMilestoneNodesNameMap.containsKey(excel.getParentName())) {
                    ProjectSchemeMilestoneNode projectSchemeMilestoneNode = dbProjectSchemeMilestoneNodesNameMap.get(excel.getParentName());
                    tmp.setNodeChain(projectSchemeMilestoneNode.getNodeChain() + "," + projectSchemeMilestoneNode.getId());
                    tmp.setParentId(projectSchemeMilestoneNode.getId());
                } else {
                    List<String> errorMessages = errorMessageMap.getOrDefault(excel.getRowIndex(), new ArrayList<>());
                    errorMessages.add("父级计划不存在");
                    errorMessageMap.put(excel.getRowIndex(), errorMessages);
                }
            }
            projectSchemeMilestoneNodes.add(tmp);
        });

        if (!CollectionUtils.isEmpty(errorMessageMap)) {
            result.setCode(200);
            List<ImportExcelErrorNoteVO> errors = new ArrayList<>();
            errorMessageMap.forEach((k, v) -> {
                ImportExcelErrorNoteVO tmp = new ImportExcelErrorNoteVO();
                tmp.setOrder(String.valueOf(k));
                tmp.setErrorNotes(v);
                errors.add(tmp);
            });
            result.setErr(errors);
        } else {
            String importId = IdUtil.fastSimpleUUID();
            orionJ2CacheService.set("pms::projectSchemeMilestoneNode-import::importKey", importId, projectSchemeMilestoneNodes, 24 * 60 * 60);
            result.setCode(200);
            result.setSucc(importId);
        }
        return result;
    }


    @Override
    public void importByExcelDo(String importKey) {
        List<ProjectSchemeMilestoneNode> projectSchemeMilestoneNodes = (List<ProjectSchemeMilestoneNode>) orionJ2CacheService.get("pms::projectSchemeMilestoneNode-import::importKey", importKey);
        this.saveBatch(projectSchemeMilestoneNodes);
        //TODO 整改 提取到公共常量里面去或者配置
        orionJ2CacheService.delete("pms::projectSchemeMilestoneNode-import::importKey", importKey);
    }

    @Override
    public void importByExcelCancel(String importKey) {
        //TODO 整改 提取到公共常量里面去或者配置
        orionJ2CacheService.delete("pms::projectSchemeMilestoneNode-import::importKey", importKey);
    }


    /**
     * @param templateId
     */
    @Override
    public List<ProjectSchemeMilestoneNodeVO> getSchemeList(String templateId) throws Exception {
        LambdaQueryWrapper<ProjectSchemeMilestoneNode> schemeLambdaQueryWrapper = new LambdaQueryWrapper<>(ProjectSchemeMilestoneNode.class);

        schemeLambdaQueryWrapper.eq(ProjectSchemeMilestoneNode::getTemplateId, templateId);
        List<ProjectSchemeMilestoneNode> projectSchemes = this.list(schemeLambdaQueryWrapper);
        List<ProjectSchemeMilestoneNodeVO> schemeVOS = BeanCopyUtils.convertListTo(projectSchemes, ProjectSchemeMilestoneNodeVO::new);
        if (schemeVOS == null) {
            return new ArrayList<>();
        }
        return schemeVOS;

    }

    @Override
    public List<ProjectSchemeMilestoneNodeVO> getProjectSchemeMilestoneList(String templateId) throws Exception {

        if (StrUtil.isBlank(templateId)) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERR, "模版ID不能为空");
        }

        LambdaQueryWrapper<ProjectSchemeMilestoneNode> schemeLambdaQueryWrapper = new LambdaQueryWrapper<>(ProjectSchemeMilestoneNode.class);
        schemeLambdaQueryWrapper.eq(ProjectSchemeMilestoneNode::getTemplateId, templateId);
        schemeLambdaQueryWrapper.eq(ProjectSchemeMilestoneNode::getNodeType, PLAN_TYPE_NAME_MAP.get("里程碑"));

        List<ProjectSchemeMilestoneNode> projectSchemes = this.list(schemeLambdaQueryWrapper);
        if (projectSchemes == null) {
            return new ArrayList<>();
        }

        List<ProjectSchemeMilestoneNodeVO> projectSchemeMilestoneNodeVOS = BeanCopyUtils.convertListTo(projectSchemes, ProjectSchemeMilestoneNodeVO::new);

        return projectSchemeMilestoneNodeVOS;
    }

    @Override
    public ProjectSchemeMilestoneNodeVO createSchemeMilestone(ProjectSchemeMilestoneNodeDTO projectSchemeMilestoneNodeDTO) {
        ProjectSchemeMilestoneNode projectSchemeMilestoneNode = BeanCopyUtils.convertTo(projectSchemeMilestoneNodeDTO, ProjectSchemeMilestoneNode::new);
        if (StrUtil.isBlank(projectSchemeMilestoneNode.getParentId()) || StrUtil.equals("0", projectSchemeMilestoneNode.getParentId())) {
            projectSchemeMilestoneNode.setParentId("0");
            projectSchemeMilestoneNode.setNodeChain("0");
        } else {
            ProjectSchemeMilestoneNode parent = this.getById(projectSchemeMilestoneNode.getParentId());
            projectSchemeMilestoneNode.setNodeChain(parent.getNodeChain() + "," + parent.getId());
        }
        projectSchemeMilestoneNode.setName("新增计划");
        projectSchemeMilestoneNode.setDurationDays(1);
        projectSchemeMilestoneNode.setNodeType(ProjectSchemeNodeTypeEnum.PLAN.getValue());
        projectSchemeMilestoneNode.setProcessFlag(false);
        this.save(projectSchemeMilestoneNode);
        ProjectSchemeMilestoneNodeVO rsp = BeanCopyUtils.convertTo(projectSchemeMilestoneNode, ProjectSchemeMilestoneNodeVO::new);
        return rsp;
    }
}
