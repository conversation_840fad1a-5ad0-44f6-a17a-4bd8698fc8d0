package com.chinasie.orion.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.amqp.entity.ChangeStatusMessageDTO;
import com.chinasie.orion.constant.InvestmentSchemeStatus;
import com.chinasie.orion.constant.MonthInvestmentSchemeFeedbackStatus;
import com.chinasie.orion.constant.YearInvestmentSchemeStatus;
import com.chinasie.orion.domain.dto.MonthInvestmentSchemeDTO;
import com.chinasie.orion.domain.dto.YearInvestmentSchemeDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.YearInvestmentSchemeRepository;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;

import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.PmsAuthUtil;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * YearInvestmentScheme 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16 14:21:48
 */
@Service
public class YearInvestmentSchemeServiceImpl extends OrionBaseServiceImpl<YearInvestmentSchemeRepository,YearInvestmentScheme> implements YearInvestmentSchemeService {

    @Resource
    private InvestmentSchemeService investmentSchemeService;

    @Resource
    private InvestmentSchemeEstimateService investmentSchemeEstimateService;

    @Resource
    private MonthInvestmentSchemeService monthInvestmentSchemeService;

    @Resource
    private YearInvestmentSchemeMonthFeedbackService yearInvestmentSchemeMonthFeedbackService;
    @Resource
    private UserRedisHelper userRedisHelper;

    @Autowired
    private PmsAuthUtil pmsAuthUtil;

    @Autowired
    @Lazy
    private MoneyStatisticsService moneyStatisticsService;

    @Autowired
    private ProjectService projectService;


    @Override
    public YearInvestmentSchemeVO initValue(String investmentSchemeId, String currentYear, String yearId) throws Exception {
        InvestmentSchemeVO investmentSchemeVO = investmentSchemeService.detail(investmentSchemeId, null);
        YearInvestmentSchemeVO result = BeanCopyUtils.convertTo(investmentSchemeVO, YearInvestmentSchemeVO::new);
        if (Objects.isNull(currentYear)) {
            currentYear = DateUtil.format(new Date(), "yyyy");
            //生成计划名称
            int y = Integer.parseInt(currentYear) + 1;
            result.setNumber(investmentSchemeVO.getNumber() + "-" + y);
            result.setName(result.getProjectName() + y + "年投资计划");
            //获取概算
            List<InvestmentSchemeEstimate> investmentSchemeEstimates = investmentSchemeEstimateService.list(new LambdaQueryWrapperX<>(InvestmentSchemeEstimate.class).eq(InvestmentSchemeEstimate::getInvestmentSchemeId, investmentSchemeId));
            if (CollectionUtils.isEmpty(investmentSchemeEstimates)) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "请先添加概算");
            }
            //新增获取最大层级概算
            //{ label: '项目建议书', value: '1' },
            //{ label: '可行性研究报告', value: '2' },
            //{ label: '初步设计', value: '3' },
            InvestmentSchemeEstimate use = null;
            Map<String, InvestmentSchemeEstimate> sourceEstimateMap = investmentSchemeEstimates.stream().collect(Collectors.toMap(InvestmentSchemeEstimate::getSource, Function.identity(), (v1, v2) -> v2));
            if (sourceEstimateMap.containsKey("1")) {
                use = sourceEstimateMap.get("1");
            }
            if (sourceEstimateMap.containsKey("2")) {
                use = sourceEstimateMap.get("2");
            }
            if (sourceEstimateMap.containsKey("3")) {
                use = sourceEstimateMap.get("3");
            }

            String estimateStr = NumberUtil.roundStr((use.getArchitecture().add(use.getDevice()).add(use.getInstallation()).add(use.getOther())).toString(), 2);
            result.setEstimate(estimateStr);
            result.setEstimateId(use.getId());

            List<YearInvestmentScheme> yearInvestmentSchemes = this.list(new LambdaQueryWrapperX<>(YearInvestmentScheme.class).eq(YearInvestmentScheme::getInvestmentId, investmentSchemeId));
            if (!CollectionUtils.isEmpty(yearInvestmentSchemes)) {
                //截止Y-2年下达投资计划
                Map<String, List<YearInvestmentScheme>> groupByYearNameMapY_2 = yearInvestmentSchemes.stream().filter(o -> Integer.parseInt(o.getYearName()) <= (y - 2)).collect(Collectors.groupingBy(YearInvestmentScheme::getYearName));
                List<YearInvestmentScheme> y_2s = new ArrayList<>();
                groupByYearNameMapY_2.forEach((k, v) -> {
                    if (v.size() == 2) {
                        y_2s.addAll(v.stream().filter(o -> StrUtil.isNotBlank(o.getOldId())).collect(Collectors.toList()));
                    } else {
                        y_2s.addAll(v);
                    }

                });
                BigDecimal cutOffGiveY_2 = y_2s.stream().map(o -> o.getArchitecture().add(o.getDevice()).add(o.getOther()).add(o.getInstallation())).reduce(BigDecimal.ZERO, BigDecimal::add);
                result.setCutOffGiveY_2(NumberUtil.roundStr(cutOffGiveY_2.toString(), 2));

                //截止Y-2年投资计划完成
                BigDecimal completeY_2 = y_2s.stream().map(YearInvestmentScheme::getTotalDo).reduce(BigDecimal.ZERO, BigDecimal::add);
                result.setCutOffCompleteY_2(NumberUtil.roundStr(completeY_2.toString(), 2));

                //Y-1年投资计划
                List<YearInvestmentScheme> y_1 = yearInvestmentSchemes.stream().filter(o -> Objects.equals(Integer.parseInt(o.getYearName()), (y - 1))).collect(Collectors.toList());
                if (y_1.size() == 1) {
                    BigDecimal reduce = y_1.stream().map(o -> o.getArchitecture().add(o.getDevice()).add(o.getOther()).add(o.getInstallation())).reduce(BigDecimal.ZERO, BigDecimal::add);
                    result.setLastYear(NumberUtil.roundStr(reduce.toString(), 2));
                } else {
                    BigDecimal reduce = y_1.stream().filter(o -> StrUtil.isNotBlank(o.getOldId())).map(o -> o.getArchitecture().add(o.getDevice()).add(o.getOther()).add(o.getInstallation())).reduce(BigDecimal.ZERO, BigDecimal::add);
                    result.setLastYear(NumberUtil.roundStr(reduce.toString(), 2));
                }


                //截止Y-1年下达投资计划
                Map<String, List<YearInvestmentScheme>> groupByYearNameMapY_1 = yearInvestmentSchemes.stream().filter(o -> Integer.parseInt(o.getYearName()) <= (y - 1)).collect(Collectors.groupingBy(YearInvestmentScheme::getYearName));
                List<YearInvestmentScheme> y_1s = new ArrayList<>();
                groupByYearNameMapY_1.forEach((k, v) -> {
                    if (v.size() == 2) {
                        y_1s.addAll(v.stream().filter(o -> StrUtil.isNotBlank(o.getOldId())).collect(Collectors.toList()));
                    } else {
                        y_1s.addAll(v);
                    }

                });
                BigDecimal cutOffGiveY_1 = y_1s.stream().map(o -> o.getArchitecture().add(o.getDevice()).add(o.getOther()).add(o.getInstallation())).reduce(BigDecimal.ZERO, BigDecimal::add);
                result.setCutOffGiveY_1(NumberUtil.roundStr(cutOffGiveY_1.toString(), 2));

                //截止Y-1年投资计划完成
                BigDecimal completeY_1 = y_1s.stream().map(YearInvestmentScheme::getTotalDo).reduce(BigDecimal.ZERO, BigDecimal::add);
                result.setCutOffCompleteY_1(NumberUtil.roundStr(completeY_1.toString(), 2));


                //截止Y年下达投资计划
                Map<String, List<YearInvestmentScheme>> groupByYearNameMapY = yearInvestmentSchemes.stream().filter(o -> Integer.parseInt(o.getYearName()) <= (y)).collect(Collectors.groupingBy(YearInvestmentScheme::getYearName));
                List<YearInvestmentScheme> ys = new ArrayList<>();
                groupByYearNameMapY.forEach((k, v) -> {
                    if (v.size() == 2) {
                        ys.addAll(v.stream().filter(o -> StrUtil.isNotBlank(o.getOldId())).collect(Collectors.toList()));
                    } else {
                        ys.addAll(v);
                    }

                });
                BigDecimal cutOffGiveY = ys.stream().map(o -> o.getArchitecture().add(o.getDevice()).add(o.getOther()).add(o.getInstallation())).reduce(BigDecimal.ZERO, BigDecimal::add);
                result.setCutOffGiveY(NumberUtil.roundStr(cutOffGiveY.toString(), 2));

                //截止Y年投资计划完成
                BigDecimal completeY = ys.stream().map(YearInvestmentScheme::getTotalDo).reduce(BigDecimal.ZERO, BigDecimal::add);
                result.setCutOffCompleteY(NumberUtil.roundStr(completeY.toString(), 2));

            } else {
                //截止Y-2年下达投资计划
                result.setCutOffGiveY_2("0");
                //截止Y-2年投资计划完成
                result.setCutOffCompleteY_2("0");
                //Y-1年投资计划
                result.setLastYear("0");

                //截止Y-1年下达投资计划
                result.setCutOffGiveY_1("0");
                //截止Y-1年投资计划完成
                result.setCutOffCompleteY_1("0");

                //截止Y年下达投资计划
                result.setCutOffGiveY("0");
                //截止Y年投资计划完成
                result.setCutOffCompleteY("0");
            }
            result.setYearName(String.valueOf(y));
        } else {
            int y = Integer.parseInt(currentYear);
            YearInvestmentScheme yearInvestmentScheme = this.getById(yearId);
            InvestmentSchemeEstimate use = investmentSchemeEstimateService.getById(yearInvestmentScheme.getEstimateId());
            String estimateStr = NumberUtil.roundStr((use.getArchitecture().add(use.getDevice()).add(use.getInstallation()).add(use.getOther())).toString(), 2);
            result.setEstimate(estimateStr);
            result.setEstimateId(use.getId());

            List<YearInvestmentScheme> yearInvestmentSchemes = this.list(new LambdaQueryWrapperX<>(YearInvestmentScheme.class).eq(YearInvestmentScheme::getInvestmentId, investmentSchemeId));
            if (!CollectionUtils.isEmpty(yearInvestmentSchemes)) {
                //截止Y-2年下达投资计划
                Map<String, List<YearInvestmentScheme>> groupByYearNameMapY_2 = yearInvestmentSchemes.stream().filter(o -> Integer.parseInt(o.getYearName()) <= (y - 2)).collect(Collectors.groupingBy(YearInvestmentScheme::getYearName));
                List<YearInvestmentScheme> y_2s = new ArrayList<>();
                groupByYearNameMapY_2.forEach((k, v) -> {
                    if (v.size() == 2) {
                        y_2s.addAll(v.stream().filter(o -> StrUtil.isNotBlank(o.getOldId())).collect(Collectors.toList()));
                    } else {
                        y_2s.addAll(v);
                    }

                });
                BigDecimal cutOffGiveY_2 = y_2s.stream().map(o -> o.getArchitecture().add(o.getDevice()).add(o.getOther()).add(o.getInstallation())).reduce(BigDecimal.ZERO, BigDecimal::add);
                result.setCutOffGiveY_2(NumberUtil.roundStr(cutOffGiveY_2.toString(), 2));

                //截止Y-2年投资计划完成
                BigDecimal completeY_2 = y_2s.stream().map(YearInvestmentScheme::getTotalDo).reduce(BigDecimal.ZERO, BigDecimal::add);
                result.setCutOffCompleteY_2(NumberUtil.roundStr(completeY_2.toString(), 2));
                //Y-1年投资计划
                List<YearInvestmentScheme> y_1 = yearInvestmentSchemes.stream().filter(o -> Objects.equals(Integer.parseInt(o.getYearName()), (y - 1))).collect(Collectors.toList());
                if (y_1.size() == 1) {
                    BigDecimal reduce = y_1.stream().map(o -> o.getArchitecture().add(o.getDevice()).add(o.getOther()).add(o.getInstallation())).reduce(BigDecimal.ZERO, BigDecimal::add);
                    result.setLastYear(NumberUtil.roundStr(reduce.toString(), 2));
                } else {
                    BigDecimal reduce = y_1.stream().filter(o -> StrUtil.isNotBlank(o.getOldId())).map(o -> o.getArchitecture().add(o.getDevice()).add(o.getOther()).add(o.getInstallation())).reduce(BigDecimal.ZERO, BigDecimal::add);
                    result.setLastYear(NumberUtil.roundStr(reduce.toString(), 2));
                }


                //截止Y-1年下达投资计划
                Map<String, List<YearInvestmentScheme>> groupByYearNameMapY_1 = yearInvestmentSchemes.stream().filter(o -> Integer.parseInt(o.getYearName()) <= (y - 1)).collect(Collectors.groupingBy(YearInvestmentScheme::getYearName));
                List<YearInvestmentScheme> y_1s = new ArrayList<>();
                groupByYearNameMapY_1.forEach((k, v) -> {
                    if (v.size() == 2) {
                        y_1s.addAll(v.stream().filter(o -> StrUtil.isNotBlank(o.getOldId())).collect(Collectors.toList()));
                    } else {
                        y_1s.addAll(v);
                    }

                });
                BigDecimal cutOffGiveY_1 = y_1s.stream().map(o -> o.getArchitecture().add(o.getDevice()).add(o.getOther()).add(o.getInstallation())).reduce(BigDecimal.ZERO, BigDecimal::add);
                result.setCutOffGiveY_1(NumberUtil.roundStr(cutOffGiveY_1.toString(), 2));

                //截止Y-1年投资计划完成
                BigDecimal completeY_1 = y_1s.stream().map(YearInvestmentScheme::getTotalDo).reduce(BigDecimal.ZERO, BigDecimal::add);
                result.setCutOffCompleteY_1(NumberUtil.roundStr(completeY_1.toString(), 2));


                //截止Y年下达投资计划
                Map<String, List<YearInvestmentScheme>> groupByYearNameMapY = yearInvestmentSchemes.stream().filter(o -> Integer.parseInt(o.getYearName()) <= (y)).collect(Collectors.groupingBy(YearInvestmentScheme::getYearName));
                List<YearInvestmentScheme> ys = new ArrayList<>();
                groupByYearNameMapY.forEach((k, v) -> {
                    if (v.size() == 2) {
                        ys.addAll(v.stream().filter(o -> StrUtil.isNotBlank(o.getOldId())).collect(Collectors.toList()));
                    } else {
                        ys.addAll(v);
                    }

                });
                BigDecimal cutOffGiveY = ys.stream().map(o -> o.getArchitecture().add(o.getDevice()).add(o.getOther()).add(o.getInstallation())).reduce(BigDecimal.ZERO, BigDecimal::add);
                result.setCutOffGiveY(NumberUtil.roundStr(cutOffGiveY.toString(), 2));

                //截止Y年投资计划完成
                BigDecimal completeY = ys.stream().map(YearInvestmentScheme::getTotalDo).reduce(BigDecimal.ZERO, BigDecimal::add);
                result.setCutOffCompleteY(NumberUtil.roundStr(completeY.toString(), 2));

            } else {
                //截止Y-2年下达投资计划
                result.setCutOffGiveY_2("0");
                //截止Y-2年投资计划完成
                result.setCutOffCompleteY_2("0");
                //Y-1年投资计划
                result.setLastYear("0");

                //截止Y-1年下达投资计划
                result.setCutOffGiveY_1("0");
                //截止Y-1年投资计划完成
                result.setCutOffCompleteY_1("0");

                //截止Y年下达投资计划
                result.setCutOffGiveY("0");
                //截止Y年投资计划完成
                result.setCutOffCompleteY("0");
            }
            result.setYearName(String.valueOf(y));
        }
        return result;
    }

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public YearInvestmentSchemeVO detail(String id, String pageCode) throws Exception {
        YearInvestmentScheme yearInvestmentScheme = this.getById(id);

        YearInvestmentSchemeVO yearInvestmentSchemeVO = initValue(yearInvestmentScheme.getInvestmentId(), yearInvestmentScheme.getYearName(), id);

        YearInvestmentSchemeVO result = BeanCopyUtils.convertTo(yearInvestmentScheme, YearInvestmentSchemeVO::new);
        result.setEstimate(yearInvestmentSchemeVO.getEstimate());
        result.setCutOffGiveY_2(yearInvestmentSchemeVO.getCutOffGiveY_2());
        result.setCutOffCompleteY_2(yearInvestmentSchemeVO.getCutOffCompleteY_2());
        result.setLastYear(yearInvestmentSchemeVO.getLastYear());
        result.setCompanyName(yearInvestmentSchemeVO.getCompanyName());

        result.setCutOffGiveY_1(yearInvestmentSchemeVO.getCutOffGiveY_1());
        result.setCutOffCompleteY_1(yearInvestmentSchemeVO.getCutOffCompleteY_1());

        result.setCutOffGiveY(yearInvestmentSchemeVO.getCutOffGiveY());
        result.setCutOffCompleteY(yearInvestmentSchemeVO.getCutOffCompleteY());


        result.setProjectId(yearInvestmentSchemeVO.getProjectId());
        result.setProjectName(yearInvestmentSchemeVO.getProjectName());
        result.setProjectNumber(yearInvestmentSchemeVO.getProjectNumber());
        result.setProjectStatusName(yearInvestmentSchemeVO.getProjectStatusName());
        result.setRspDeptName(yearInvestmentSchemeVO.getRspDeptName());
        result.setRspUserName(yearInvestmentSchemeVO.getRspUserName());
        /**
         * 总体预算
         */
        result.setOverallBudget(yearInvestmentSchemeVO.getOverallBudget());
        /**
         * 总体实际
         */
        result.setOverallReality(yearInvestmentSchemeVO.getOverallReality());
        /**
         * 立项金额
         */
        result.setProjectAmount(yearInvestmentSchemeVO.getProjectAmount());
        /**
         * 合同金额
         */
        result.setContractAmount(yearInvestmentSchemeVO.getContractAmount());

        List<MonthInvestmentScheme> monthInvestmentSchemes = monthInvestmentSchemeService.list(new LambdaQueryWrapperX<>(MonthInvestmentScheme.class).eq(MonthInvestmentScheme::getYearId, id));
        List<MonthInvestmentSchemeVO> monthInvestmentSchemeVOS = BeanCopyUtils.convertListTo(monthInvestmentSchemes, MonthInvestmentSchemeVO::new);

        monthInvestmentSchemeVOS.sort((o1, o2) -> {
            // 拆分月份字符串并转换为数字
            String newo1 = "";
            if (o1.getName().contains("-")) {
                newo1 = o1.getName().split("-")[1].replace("月", "");
            } else {
                newo1 = o1.getName().replace("月", "");
            }

            String newo2 = "";
            if (o2.getName().contains("-")) {
                newo2 = o2.getName().split("-")[1].replace("月", "");
            } else {
                newo2 = o2.getName().replace("月", "");
            }
            return Integer.parseInt(newo1) - Integer.parseInt(newo2);
        });
        result.setMonthInvestmentSchemes(monthInvestmentSchemeVOS);

        result.setTotalDo(NumberUtil.roundStr(yearInvestmentScheme.getTotalDo().toString(), 2));


        if (StrUtil.isNotBlank(yearInvestmentScheme.getOldId())) {
            BigDecimal totalChange = result.getArchitecture().add(result.getInstallation()).add(result.getDevice()).add(result.getOther());
            result.setTotalChange(NumberUtil.roundStr(totalChange.toString(), 2));

            YearInvestmentScheme oldYearInvest = this.getById(yearInvestmentScheme.getOldId());
            BigDecimal total = oldYearInvest.getArchitecture().add(oldYearInvest.getInstallation()).add(oldYearInvest.getDevice()).add(oldYearInvest.getOther());
            result.setTotal(NumberUtil.roundStr(total.toString(), 2));
        } else {
            List<YearInvestmentScheme> yearInvestmentSchemes = this.list(new LambdaQueryWrapperX<>(YearInvestmentScheme.class).eq(YearInvestmentScheme::getOldId, id));
            if (!CollectionUtils.isEmpty(yearInvestmentSchemes)) {
                YearInvestmentScheme change = yearInvestmentSchemes.get(0);
                BigDecimal totalChange = change.getArchitecture().add(change.getInstallation()).add(change.getDevice()).add(change.getOther());
                result.setTotalChange(NumberUtil.roundStr(totalChange.toString(), 2));

                result.setTotalDo(NumberUtil.roundStr(change.getTotalDo().toString(), 2));

            } else {
                result.setTotalChange("0");
            }
            BigDecimal total = result.getArchitecture().add(result.getInstallation()).add(result.getDevice()).add(result.getOther());
            result.setTotal(NumberUtil.roundStr(total.toString(), 2));
        }


        if (Objects.nonNull(result.getDataStatus()) && StrUtil.isNotBlank(result.getOldId())) {
            DataStatusVO dataStatus = result.getDataStatus();
            String name = dataStatus.getName();
            if (!name.contains("调整-")) {
                DataStatusVO dataStatusVO = BeanCopyUtils.convertTo(dataStatus, DataStatusVO::new);
                dataStatusVO.setName("调整-" + dataStatusVO.getName());
                result.setDataStatus(dataStatusVO);
            }
        }
        List<YearInvestmentSchemeMonthFeedback> yearInvestmentSchemeMonthFeedbacks = yearInvestmentSchemeMonthFeedbackService.list(new LambdaQueryWrapperX<>(YearInvestmentSchemeMonthFeedback.class).eq(YearInvestmentSchemeMonthFeedback::getYearInvestmentId, id).eq(YearInvestmentSchemeMonthFeedback::getStatus, MonthInvestmentSchemeFeedbackStatus.CHANGE_WRITE.getCode()));
        if (!CollectionUtils.isEmpty(yearInvestmentSchemeMonthFeedbacks)) {
            yearInvestmentSchemeMonthFeedbacks.sort(Comparator.comparing(o -> Integer.parseInt(o.getMonth().replace("月", ""))));
            YearInvestmentSchemeMonthFeedback yearInvestmentSchemeMonthFeedback = yearInvestmentSchemeMonthFeedbacks.get(yearInvestmentSchemeMonthFeedbacks.size() - 1);
            result.setTotalProcess(yearInvestmentSchemeMonthFeedback.getTotalProcess());
        }
        ProjectBudgetVO annualStatistics = moneyStatisticsService.getAnnualStatisticsByProjectId(result.getProjectId());
        if(annualStatistics != null){
            /**
             * 本年预算
             */
            result.setCurrentYearBudget(NumberUtil.roundStr(annualStatistics.getYearExpense().toString(), 2));

            /**
             *   本年预算执行
             */
            result.setCurrentYearBudgetDo(NumberUtil.roundStr(annualStatistics.getTotalCost().toString(), 2));
        }



        List<String> roleCodeList = pmsAuthUtil.getRoleCodeList(result.getProjectId(), CurrentUserHelper.getCurrentUserId());

        pmsAuthUtil.setDetailAuths(result, CurrentUserHelper.getCurrentUserId(), pageCode, result.getDataStatus(), YearInvestmentSchemeVO::setDetailAuthList, result.getCreatorId(), result.getModifyId(), result.getOwnerId(), roleCodeList);

        return result;
    }

    /**
     * 新增
     * <p>
     * * @param yearInvestmentSchemeDTO
     */
    @Override
    public YearInvestmentSchemeVO create(YearInvestmentSchemeDTO yearInvestmentSchemeDTO) throws Exception {
        YearInvestmentScheme yearInvestmentScheme = BeanCopyUtils.convertTo(yearInvestmentSchemeDTO, YearInvestmentScheme::new);


        //新增时判断当年的投资计划是否已经存在
        String yearName = yearInvestmentScheme.getYearName();
        List<YearInvestmentScheme> yearInvestmentSchemes = this.list(new LambdaQueryWrapperX<>(YearInvestmentScheme.class).eq(YearInvestmentScheme::getInvestmentId, yearInvestmentSchemeDTO.getInvestmentId()).eq(YearInvestmentScheme::getYearName, yearName));
        if (!CollectionUtils.isEmpty(yearInvestmentSchemes)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "该年度投资计划已存在");
        }

        InvestmentScheme investmentScheme = investmentSchemeService.getById(yearInvestmentSchemeDTO.getInvestmentId());

        //新增时判断投资计划是否已关闭
//        if (investmentScheme.getCloseFlag()) {
//            throw new PMSException(PMSErrorCode.PMS_ERR, "该项目投资计划已调整为关闭");
//        }

        yearInvestmentScheme.setProjectId(investmentScheme.getProjectId());
        if (Objects.isNull(yearInvestmentScheme.getCloseFlag())) {
            yearInvestmentScheme.setCloseFlag(false);
        }
        if (Objects.isNull(yearInvestmentScheme.getArchitecture())) {
            yearInvestmentScheme.setArchitecture(BigDecimal.ZERO);
        }
        if (Objects.isNull(yearInvestmentScheme.getInstallation())) {
            yearInvestmentScheme.setInstallation(BigDecimal.ZERO);
        }
        if (Objects.isNull(yearInvestmentScheme.getDevice())) {
            yearInvestmentScheme.setDevice(BigDecimal.ZERO);
        }
        if (Objects.isNull(yearInvestmentScheme.getOther())) {
            yearInvestmentScheme.setOther(BigDecimal.ZERO);
        }
        if (Objects.isNull(yearInvestmentScheme.getLastYearComplete())) {
            yearInvestmentScheme.setLastYearComplete(BigDecimal.ZERO);
        }
        if (Objects.isNull(yearInvestmentScheme.getNextFiveYear())) {
            yearInvestmentScheme.setNextFiveYear(BigDecimal.ZERO);
        }
        if (Objects.isNull(yearInvestmentScheme.getNextFourYear())) {
            yearInvestmentScheme.setNextFourYear(BigDecimal.ZERO);
        }
        if (Objects.isNull(yearInvestmentScheme.getNextThreeYear())) {
            yearInvestmentScheme.setNextThreeYear(BigDecimal.ZERO);
        }
        if (Objects.isNull(yearInvestmentScheme.getNextTwoYear())) {
            yearInvestmentScheme.setNextTwoYear(BigDecimal.ZERO);
        }
        if (Objects.isNull(yearInvestmentScheme.getNextOneYear())) {
            yearInvestmentScheme.setNextOneYear(BigDecimal.ZERO);
        }
        yearInvestmentScheme.setTotalDo(BigDecimal.ZERO);

        // 新增 项目编号和项目编号对应的序号 方便排序过滤 冗余字段
        String projectId = investmentScheme.getProjectId();
        Project project = projectService.getById(projectId);
        String projectNumber = project.getNumber();
        String[] s = projectNumber.split("-");
        if(s[s.length-1].matches("^[0-9]+$")){
            int i = Integer.parseInt(s[s.length-1]);
            yearInvestmentScheme.setSerialNumber(i);
        }

        yearInvestmentScheme.setProjectNumber(projectNumber);


        this.save(yearInvestmentScheme);
        List<MonthInvestmentSchemeDTO> monthInvestmentSchemeDTOS = yearInvestmentSchemeDTO.getMonthInvestmentSchemes();

        List<MonthInvestmentScheme> monthInvestmentSchemes = BeanCopyUtils.convertListTo(monthInvestmentSchemeDTOS, MonthInvestmentScheme::new);
        monthInvestmentSchemes.forEach(m -> {
            m.setYearId(yearInvestmentScheme.getId());
            m.setProjectId(investmentScheme.getProjectId());
            if (Objects.isNull(m.getActual())) {
                m.setActual(BigDecimal.ZERO);
            }
            if (Objects.isNull(m.getPredicate())) {
                m.setPredicate(BigDecimal.ZERO);
            }
            m.setHasDo(0);
        });
        monthInvestmentSchemeService.saveBatch(monthInvestmentSchemes);
        YearInvestmentSchemeVO rsp = BeanCopyUtils.convertTo(yearInvestmentScheme, YearInvestmentSchemeVO::new);
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param yearInvestmentSchemeDTO
     */
    @Override
    public YearInvestmentSchemeVO edit(YearInvestmentSchemeDTO yearInvestmentSchemeDTO) throws Exception {
        YearInvestmentScheme yearInvestmentScheme = BeanCopyUtils.convertTo(yearInvestmentSchemeDTO, YearInvestmentScheme::new);

        if (Objects.isNull(yearInvestmentScheme.getCloseFlag())) {
            yearInvestmentScheme.setCloseFlag(false);
        }
        if (Objects.isNull(yearInvestmentScheme.getArchitecture())) {
            yearInvestmentScheme.setArchitecture(BigDecimal.ZERO);
        }
        if (Objects.isNull(yearInvestmentScheme.getInstallation())) {
            yearInvestmentScheme.setInstallation(BigDecimal.ZERO);
        }
        if (Objects.isNull(yearInvestmentScheme.getDevice())) {
            yearInvestmentScheme.setDevice(BigDecimal.ZERO);
        }
        if (Objects.isNull(yearInvestmentScheme.getOther())) {
            yearInvestmentScheme.setOther(BigDecimal.ZERO);
        }
        if (Objects.isNull(yearInvestmentScheme.getLastYearComplete())) {
            yearInvestmentScheme.setLastYearComplete(BigDecimal.ZERO);
        }
        if (Objects.isNull(yearInvestmentScheme.getNextFiveYear())) {
            yearInvestmentScheme.setNextFiveYear(BigDecimal.ZERO);
        }
        if (Objects.isNull(yearInvestmentScheme.getNextFourYear())) {
            yearInvestmentScheme.setNextFourYear(BigDecimal.ZERO);
        }
        if (Objects.isNull(yearInvestmentScheme.getNextThreeYear())) {
            yearInvestmentScheme.setNextThreeYear(BigDecimal.ZERO);
        }
        if (Objects.isNull(yearInvestmentScheme.getNextTwoYear())) {
            yearInvestmentScheme.setNextTwoYear(BigDecimal.ZERO);
        }
        if (Objects.isNull(yearInvestmentScheme.getNextOneYear())) {
            yearInvestmentScheme.setNextOneYear(BigDecimal.ZERO);
        }

        this.updateById(yearInvestmentScheme);



        List<MonthInvestmentSchemeDTO> monthInvestmentSchemeDTOS = yearInvestmentSchemeDTO.getMonthInvestmentSchemes();
        List<MonthInvestmentScheme> monthInvestmentSchemes = BeanCopyUtils.convertListTo(monthInvestmentSchemeDTOS, MonthInvestmentScheme::new);
        monthInvestmentSchemes.forEach(m -> {
            m.setYearId(yearInvestmentScheme.getId());
            if (Objects.isNull(m.getActual())) {
                m.setActual(BigDecimal.ZERO);
            }
            if (Objects.isNull(m.getPredicate())) {
                m.setPredicate(BigDecimal.ZERO);
            }
            m.setHasDo(0);
        });
        List<MonthInvestmentScheme> updateMonthInvestmentSchemes = monthInvestmentSchemes.stream().filter(p->StringUtils.hasText(p.getId())).collect(Collectors.toList());
        List<String> updateIds =  updateMonthInvestmentSchemes.stream().map(MonthInvestmentScheme :: getId).collect(Collectors.toList());

       LambdaQueryWrapperX<MonthInvestmentScheme> deleteMonthInvestmentSchemes =  new LambdaQueryWrapperX<>(MonthInvestmentScheme.class);
        deleteMonthInvestmentSchemes.eq(MonthInvestmentScheme::getYearId, yearInvestmentSchemeDTO.getId());
        if(!CollectionUtils.isEmpty(updateIds)){
            deleteMonthInvestmentSchemes.notIn(MonthInvestmentScheme :: getId,updateIds);
        }
        monthInvestmentSchemeService.remove(deleteMonthInvestmentSchemes);



        List<MonthInvestmentScheme> insertMonthInvestmentSchemes = monthInvestmentSchemes.stream().filter(p->!StringUtils.hasText(p.getId())).collect(Collectors.toList());

        if(!CollectionUtils.isEmpty(updateMonthInvestmentSchemes)){
            monthInvestmentSchemeService.updateBatchById(monthInvestmentSchemes);
        }
        if(!CollectionUtils.isEmpty(insertMonthInvestmentSchemes)){
            monthInvestmentSchemeService.saveBatch(monthInvestmentSchemes);
        }

        YearInvestmentSchemeVO detail = this.detail(yearInvestmentScheme.getId(), null);
        return detail;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        List<YearInvestmentSchemeMonthFeedback> yearInvestmentSchemeMonthFeedbacks = yearInvestmentSchemeMonthFeedbackService.list(new LambdaQueryWrapperX<>(YearInvestmentSchemeMonthFeedback.class).in(YearInvestmentSchemeMonthFeedback::getYearInvestmentId, ids));
        if (!CollectionUtils.isEmpty(yearInvestmentSchemeMonthFeedbacks)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "该年度投资计划已产生计划反馈");
        }
        Boolean result = this.removeBatchByIds(ids);
        monthInvestmentSchemeService.remove(new LambdaQueryWrapperX<>(MonthInvestmentScheme.class).in(MonthInvestmentScheme::getYearId, ids));
        return result;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public List<YearInvestmentSchemeVO> list(String investmentSchemeId, String pageCode, String containerCode) throws Exception {
        List<YearInvestmentSchemeVO> result = new ArrayList<>();
        // 获取全部的
        List<YearInvestmentScheme> yearInvestmentSchemes = this.list(new LambdaQueryWrapperX<>(YearInvestmentScheme.class)
                .eq(YearInvestmentScheme::getInvestmentId, investmentSchemeId)
        );
        if(CollectionUtils.isEmpty(yearInvestmentSchemes)){
            return result;
        }
        //根据年分组
        Map<String, List<YearInvestmentScheme>> yearInvestmentSchemesGroupingByYearMap = yearInvestmentSchemes.stream().collect(Collectors.groupingBy(YearInvestmentScheme::getYearName));
        //每一组里面处理
        yearInvestmentSchemesGroupingByYearMap.forEach((k, v) -> {
            if (v.size() == 2) {
                Map<Boolean, List<YearInvestmentScheme>> changedMap = v.stream().collect(Collectors.groupingBy(o -> StrUtil.isNotBlank(o.getOldId())));
                YearInvestmentScheme oldData = changedMap.get(false).get(0);
                YearInvestmentScheme newData = changedMap.get(true).get(0);

                YearInvestmentSchemeVO yearInvestmentSchemeVO = BeanCopyUtils.convertTo(newData, YearInvestmentSchemeVO::new);
                yearInvestmentSchemeVO.setTotal(NumberUtil.roundStr(oldData.getArchitecture().add(oldData.getInstallation()).add(oldData.getDevice()).add(oldData.getOther()).toString(), 2));
                yearInvestmentSchemeVO.setTotalChange(NumberUtil.roundStr(newData.getArchitecture().add(newData.getInstallation()).add(newData.getDevice()).add(newData.getOther()).toString(), 2));
                yearInvestmentSchemeVO.setTotalDo(NumberUtil.roundStr(newData.getTotalDo().toString(), 2));
                result.add(yearInvestmentSchemeVO);
            } else {
                if (v.size() == 1) {
                    YearInvestmentScheme yearInvestmentScheme = v.get(0);
                    YearInvestmentSchemeVO yearInvestmentSchemeVO = BeanCopyUtils.convertTo(yearInvestmentScheme, YearInvestmentSchemeVO::new);
                    yearInvestmentSchemeVO.setTotalDo(NumberUtil.roundStr(yearInvestmentScheme.getTotalDo().toString(), 2));
                    result.add(yearInvestmentSchemeVO);

                }
            }
        });

        List<String> yearInvestmentSchemeVOIds = result.stream().map(YearInvestmentSchemeVO::getId).collect(Collectors.toList());

        List<MonthInvestmentScheme> monthInvestmentSchemeAll = monthInvestmentSchemeService.list(new LambdaQueryWrapperX<>(MonthInvestmentScheme.class).in(MonthInvestmentScheme::getYearId, yearInvestmentSchemeVOIds));

        Map<String, List<MonthInvestmentScheme>> monthInvestmentSchemeGroupingByMap = monthInvestmentSchemeAll.stream().collect(Collectors.groupingBy(MonthInvestmentScheme::getYearId));

        for (YearInvestmentSchemeVO r : result) {
            YearInvestmentSchemeVO yearInvestmentSchemeVO = initValue(investmentSchemeId, r.getYearName(), r.getId());
            r.setEstimate(yearInvestmentSchemeVO.getEstimate());
            r.setCutOffGiveY_2(yearInvestmentSchemeVO.getCutOffGiveY_2());
            r.setCutOffCompleteY_2(yearInvestmentSchemeVO.getCutOffCompleteY_2());
            r.setLastYear(yearInvestmentSchemeVO.getLastYear());
            List<MonthInvestmentScheme> monthInvestmentSchemes = monthInvestmentSchemeGroupingByMap.get(r.getId());
            List<MonthInvestmentSchemeVO> monthInvestmentSchemeVOS = BeanCopyUtils.convertListTo(monthInvestmentSchemes, MonthInvestmentSchemeVO::new);
            if(monthInvestmentSchemeVOS == null){
                monthInvestmentSchemeVOS = new ArrayList<>();
            }
           if(!CollectionUtils.isEmpty(monthInvestmentSchemeVOS)){
               monthInvestmentSchemeVOS.sort((o1, o2) -> {
                   // 拆分月份字符串并转换为数字
                   String newo1 = "";
                   if (o1.getName().contains("-")) {
                       newo1 = o1.getName().split("-")[1].replace("月", "");
                   } else {
                       newo1 = o1.getName().replace("月", "");
                   }

                   String newo2 = "";
                   if (o2.getName().contains("-")) {
                       newo2 = o2.getName().split("-")[1].replace("月", "");
                   } else {
                       newo2 = o2.getName().replace("月", "");
                   }
                   return Integer.parseInt(newo1) - Integer.parseInt(newo2);
               });
           }
            r.setMonthInvestmentSchemes(monthInvestmentSchemeVOS);

            BigDecimal total = r.getArchitecture().add(r.getInstallation()).add(r.getDevice()).add(r.getOther());
            if (StrUtil.isBlank(r.getTotal())) {
                r.setTotal(NumberUtil.roundStr(total.toString(), 2));
            }

            DataStatusVO dataStatus = r.getDataStatus();
            if (Objects.nonNull(dataStatus) && StrUtil.isNotBlank(r.getOldId())) {
                String name = dataStatus.getName();
                if (!name.contains("调整-")) {
                    DataStatusVO dataStatusVO = BeanCopyUtils.convertTo(dataStatus, DataStatusVO::new);
                    dataStatusVO.setName("调整-" + dataStatusVO.getName());
                    r.setDataStatus(dataStatusVO);
                }
            }

        }
        result.sort(Comparator.comparing(YearInvestmentSchemeVO::getYearName).reversed());
        return result;
    }

    @Override
    public void exportV2(List<String> yearIds, HttpServletResponse response) throws Exception {
        List<YearInvestmentSchemeVO> yearInvestmentSchemeVOS = new ArrayList<>();
        for (String yid : yearIds) {
            List<YearInvestmentSchemeVO> tmpYearInvestmentSchemeVOS = this.changeList(yid);
            if (CollectionUtils.isEmpty(tmpYearInvestmentSchemeVOS)) {
                YearInvestmentSchemeVO yearInvestmentSchemeVO = this.detail(yid, null);
                yearInvestmentSchemeVOS.add(yearInvestmentSchemeVO);
            } else {
                yearInvestmentSchemeVOS.addAll(tmpYearInvestmentSchemeVOS);
            }
        }
        yearInvestmentSchemeVOS.sort(Comparator.comparing(o -> Integer.parseInt(o.getYearName())));
        List<YearInvestmentSchemeMonthFeedbackVO> yearInvestmentSchemeMonthFeedbackVOS = new ArrayList<>();
        for (YearInvestmentSchemeVO o : yearInvestmentSchemeVOS) {
            List<YearInvestmentSchemeMonthFeedbackVO> list = yearInvestmentSchemeMonthFeedbackService.list(o.getId(), null, null);
            yearInvestmentSchemeMonthFeedbackVOS.addAll(list);
        }

        Map<String, List<YearInvestmentSchemeMonthFeedbackVO>> monthFeedbackMap = yearInvestmentSchemeMonthFeedbackVOS.stream().collect(Collectors.groupingBy(YearInvestmentSchemeMonthFeedbackVO::getYearInvestmentId));


        String[] sheet1Header = new String[]{"计划编号", "计划名称", "流程状态", "项目编码", "项目名称", "年度", "项目状态", "项目处室", "项目负责人", "类型", "概算",
                "立项金额", "合同金额", "累计至（Y-2）年下达投资计划", "累计至（Y-2）年完成投资", "（Y-1）年下达投资计划",
                "（Y-1）年全年预计完成投资", "（Y-1）年投资计划执行说明", "累计至（Y-1）年下达投资计划", "累计至（Y-1）年预计完成投资",
                "Y年投资计划", "Y年投资计划执行", "建筑工程", "安装工程", "设备投资", "其它费用", "1月", "1-2月", "1-3月", "1-4月",
                "1-5月", "1-6月", "1-7月", "1-8月", "1-9月", "1-10月", "1-11月", "1-12月",
                "Y年形象进度（需与分月预算匹配）", "Y+1年计划投资", "Y+2年计划投资", "Y+3年计划投资", "Y+4年计划投资", "Y+5年计划投资", "备注"};


        String[] sheet2Header = new String[]{"计划编号", "计划名称", "流程状态", "项目编码", "项目名称", "年度", "月份", "项目处室", "项目负责人",
                "Y年度形象进度", "总体进度执行情况", "项目总体进度滞后情况", "本月进度执行情况", "本月投资计划执行状态",
                "本月执行偏差原因及纠偏措施", "下月进度计划", "今年投资计划", "年度投资计划完成率", "1至（M-1）上月实际执行",
                "（1-M）月计划", "（1-M）月实际执行", "（1-M）月执行率", "备注"};


        List<Map<String, String>> items = new ArrayList<>();
        List<Map<String, String>> items2 = new ArrayList<>();

        for (Map.Entry<String, List<YearInvestmentSchemeVO>> entry : yearInvestmentSchemeVOS.stream().collect(Collectors.groupingBy(YearInvestmentSchemeVO::getYearName)).entrySet()) {
            String k = entry.getKey();
            List<YearInvestmentSchemeVO> v = entry.getValue();
            v.forEach(e -> {
                Map<String, String> item = new HashMap<>();
                item.put("计划编号", e.getNumber());
                item.put("计划名称", e.getName());
                item.put("流程状态", e.getDataStatus().getName());
                item.put("项目编码", e.getProjectNumber());
                item.put("项目名称", e.getProjectName());
                item.put("年度", e.getYearName());
                item.put("项目状态", e.getProjectStatusName());
                item.put("项目处室", e.getRspDeptName());
                item.put("项目负责人", e.getRspUserName());
                item.put("类型", StrUtil.isNotBlank(e.getOldId()) ? "调整" : "申请");
                item.put("概算", e.getEstimate());
                item.put("立项金额", e.getProjectAmount());
                item.put("合同金额", e.getContractAmount());
                item.put("累计至（Y-2）年下达投资计划", e.getCutOffGiveY_2());
                item.put("累计至（Y-2）年完成投资", e.getCutOffCompleteY_2());
                item.put("（Y-1）年下达投资计划", e.getLastYear());
                item.put("（Y-1）年全年预计完成投资", NumberUtil.roundStr(String.valueOf(e.getLastYearComplete()), 2));
                item.put("（Y-1）年投资计划执行说明", e.getLastYearDoDesc());
                item.put("累计至（Y-1）年下达投资计划", NumberUtil.roundStr(String.valueOf(e.getCutOffGiveY_1()), 2));
                item.put("累计至（Y-1）年预计完成投资", NumberUtil.roundStr(String.valueOf(e.getCutOffCompleteY_1()), 2));
                item.put("Y年投资计划", NumberUtil.roundStr(String.valueOf(e.getArchitecture().add(e.getDevice()).add(e.getInstallation()).add(e.getOther())), 2));
                item.put("Y年投资计划执行", NumberUtil.roundStr(String.valueOf(e.getTotalDo()), 2));
                item.put("建筑工程", NumberUtil.roundStr(String.valueOf(e.getArchitecture()), 2));
                item.put("安装工程", NumberUtil.roundStr(String.valueOf(e.getInstallation()), 2));
                item.put("设备投资", NumberUtil.roundStr(String.valueOf(e.getDevice()), 2));
                item.put("其它费用", NumberUtil.roundStr(String.valueOf(e.getOther()), 2));

                List<MonthInvestmentSchemeVO> monthInvestmentSchemes = e.getMonthInvestmentSchemes();
                Map<String, BigDecimal> monthMap = monthInvestmentSchemes.stream().collect(Collectors.toMap(MonthInvestmentSchemeVO::getName, MonthInvestmentSchemeVO::getPredicate));


                item.put("1月", NumberUtil.roundStr(String.valueOf(monthMap.get("1月")), 2));
                item.put("1-2月", NumberUtil.roundStr(String.valueOf(monthMap.get("1-2月")), 2));
                item.put("1-3月", NumberUtil.roundStr(String.valueOf(monthMap.get("1-3月")), 2));
                item.put("1-4月", NumberUtil.roundStr(String.valueOf(monthMap.get("1-4月")), 2));
                item.put("1-5月", NumberUtil.roundStr(String.valueOf(monthMap.get("1-5月")), 2));
                item.put("1-6月", NumberUtil.roundStr(String.valueOf(monthMap.get("1-6月")), 2));
                item.put("1-7月", NumberUtil.roundStr(String.valueOf(monthMap.get("1-7月")), 2));
                item.put("1-8月", NumberUtil.roundStr(String.valueOf(monthMap.get("1-8月")), 2));
                item.put("1-9月", NumberUtil.roundStr(String.valueOf(monthMap.get("1-9月")), 2));
                item.put("1-10月", NumberUtil.roundStr(String.valueOf(monthMap.get("1-10月")), 2));
                item.put("1-11月", NumberUtil.roundStr(String.valueOf(monthMap.get("1-11月")), 2));
                item.put("1-12月", NumberUtil.roundStr(String.valueOf(monthMap.get("1-12月")), 2));
                item.put("Y年形象进度（需与分月预算匹配）", e.getYearProcess());
                item.put("Y+1年计划投资", NumberUtil.roundStr(String.valueOf(e.getNextOneYear()), 2));
                item.put("Y+2年计划投资", NumberUtil.roundStr(String.valueOf(e.getNextTwoYear()), 2));
                item.put("Y+3年计划投资", NumberUtil.roundStr(String.valueOf(e.getNextThreeYear()), 2));
                item.put("Y+4年计划投资", NumberUtil.roundStr(String.valueOf(e.getNextFourYear()), 2));
                item.put("Y+5年计划投资", NumberUtil.roundStr(String.valueOf(e.getNextFiveYear()), 2));
                item.put("备注", e.getRemark());
                items.add(item);
            });


            YearInvestmentSchemeVO yearInvestmentSchemeVO = v.get(0);
            Map<String, String> doStatusMap = new HashMap<>() {{
                put("0", "有偏差");
                put("1", "无偏差");
            }};

            YearInvestmentSchemeVO yearInvestmentSchemeVO1 = null;
            if (v.size() == 2) {
                List<YearInvestmentSchemeVO> changeds = v.stream().filter(o -> StrUtil.isNotBlank(o.getOldId())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(changeds)) {
                    yearInvestmentSchemeVO1 = changeds.get(0);
                }
            } else {
                yearInvestmentSchemeVO1 = v.get(0);
            }

            List<YearInvestmentSchemeMonthFeedbackVO> tmpYearInvestmentSchemeMonthFeedbackVOS = monthFeedbackMap.getOrDefault(yearInvestmentSchemeVO1.getId(), new ArrayList<>());

            tmpYearInvestmentSchemeMonthFeedbackVOS.forEach(e -> {
                Map<String, String> item = new HashMap<>();

                item.put("计划编号", e.getNumber());
                item.put("计划名称", e.getName());
                item.put("流程状态", e.getDataStatus().getName());
                item.put("项目编码", yearInvestmentSchemeVO.getProjectNumber());
                item.put("项目名称", yearInvestmentSchemeVO.getProjectName());
                item.put("年度", e.getYear());
                item.put("月份", e.getMonth());
                item.put("项目处室", yearInvestmentSchemeVO.getRspDeptName());
                item.put("项目负责人", yearInvestmentSchemeVO.getRspUserName());
                item.put("Y年度形象进度", e.getYearProcess());
                item.put("总体进度执行情况", e.getTotalProcess());
                item.put("项目总体进度滞后情况", e.getDelayDesc());
                item.put("本月进度执行情况", e.getMonthProcess());
                item.put("本月投资计划执行状态", doStatusMap.get(String.valueOf(e.getMonthDoStatus())));
                item.put("本月执行偏差原因及纠偏措施", e.getReason());
                item.put("下月进度计划", e.getNextProcess());
                item.put("今年投资计划", NumberUtil.roundStr(String.valueOf(e.getYinvestmentPlan()), 2));

                DecimalFormat df = new DecimalFormat("0.00%");//设置百分比格式，保留两位小数

                if (Objects.equals(e.getYinvestmentPlan().compareTo(BigDecimal.ZERO), 0)) {
                    if (!Objects.equals(e.getMpracticeDo().compareTo(BigDecimal.ZERO), 0)) {
                        item.put("年度投资计划完成率", "100%");
                    } else {
                        item.put("年度投资计划完成率", "0.00%");
                    }
                } else {
                    BigDecimal yearRate = e.getMpracticeDo().divide(e.getYinvestmentPlan(), 4, RoundingMode.HALF_UP);// 除法运算并设置保留小数点后四位
                    item.put("年度投资计划完成率", df.format(yearRate.doubleValue()));
                }

                item.put("1至（M-1）上月实际执行", NumberUtil.roundStr(String.valueOf(e.getLastPracticeDo()), 2));
                item.put("（1-M）月计划", NumberUtil.roundStr(String.valueOf(e.getMplanDo()), 2));
                item.put("（1-M）月实际执行", NumberUtil.roundStr(String.valueOf(e.getMpracticeDo()), 2));
                if (Objects.equals(e.getMplanDo().compareTo(BigDecimal.ZERO), 0)) {
                    if (!Objects.equals(e.getMpracticeDo().compareTo(BigDecimal.ZERO), 0)) {
                        item.put("（1-M）月执行率", "100%");
                    } else {
                        item.put("（1-M）月执行率", "0.00%");
                    }
                } else {
                    BigDecimal monthRate = e.getMpracticeDo().divide(e.getMplanDo(), 4, RoundingMode.HALF_UP);// 除法运算并设置保留小数点后四位
                    item.put("（1-M）月执行率", df.format(monthRate.doubleValue()));
                }
                item.put("备注", e.getRemark());
                items2.add(item);
            });
        }

        // 创建一个新工作簿
        Workbook workbook = new XSSFWorkbook();
        // 年度投资计划
        Sheet sheet = workbook.createSheet("投资计划");

        Row row = sheet.createRow(0);
        for (int j = 0; j < sheet1Header.length; j++) {
            Cell cell = row.createCell(j);
            cell.setCellValue(sheet1Header[j]);
        }

        for (int i = 0; i < items.size(); i++) {
            Row dataRow = sheet.createRow(i + 1);
            Map<String, String> item = items.get(i);
            for (int j = 0; j < sheet1Header.length; j++) {
                Cell cell = dataRow.createCell(j);
                cell.setCellValue(item.get(sheet1Header[j]));
            }
        }

        Row row2 = sheet.createRow(items.size() + 2);
        for (int j = 0; j < sheet2Header.length; j++) {
            Cell cell = row2.createCell(j);
            cell.setCellValue(sheet2Header[j]);
        }

        for (int i = 0; i < items2.size(); i++) {
            Row dataRow = sheet.createRow(i + items.size() + 2 + 1);
            Map<String, String> item = items2.get(i);
            for (int j = 0; j < sheet2Header.length; j++) {
                Cell cell = dataRow.createCell(j);
                cell.setCellValue(item.get(sheet2Header[j]));
            }
        }


        //下载
        response.reset();
        response.setContentType("application/octet-stream; charset=utf-8");//以流的形式对文件进行下载
        String fileName = String.format("%s.xlsx", yearInvestmentSchemeVOS.get(0).getName());
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));//对文件名编码,防止文件名乱码
        workbook.write(response.getOutputStream());
        response.getOutputStream().close();
    }

    @Override
    public String canChange(String investId, String yearId) throws Exception {
        if (StrUtil.isBlank(yearId)) {
            String currentYear = DateUtil.format(new Date(), "yyyy");
            //判断今年是否已经有了调整
            List<YearInvestmentScheme> currentYearInvestmentSchemes = this.list(new LambdaQueryWrapperX<>(YearInvestmentScheme.class)
                    .eq(YearInvestmentScheme::getInvestmentId, investId)
                    .eq(YearInvestmentScheme::getYearName, currentYear)
            );
            if (CollectionUtils.isEmpty(currentYearInvestmentSchemes)) {

                InvestmentSchemeVO investmentSchemeVO = investmentSchemeService.detail(investId, null);
                YearInvestmentSchemeVO result = BeanCopyUtils.convertTo(investmentSchemeVO, YearInvestmentSchemeVO::new);

                //生成计划名称
                int y = Integer.parseInt(currentYear);
                result.setNumber(investmentSchemeVO.getNumber() + "-" + y);
                result.setName(result.getProjectName() + y + "年投资计划");
                //获取概算
                List<InvestmentSchemeEstimate> investmentSchemeEstimates = investmentSchemeEstimateService.list(new LambdaQueryWrapperX<>(InvestmentSchemeEstimate.class).eq(InvestmentSchemeEstimate::getInvestmentSchemeId, investId));
                if (CollectionUtils.isEmpty(investmentSchemeEstimates)) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "请先添加概算");
                }
                //新增获取最大层级概算
                //{ label: '项目建议书', value: '1' },
                //{ label: '可行性研究报告', value: '2' },
                //{ label: '初步设计', value: '3' },
                InvestmentSchemeEstimate use = null;
                Map<String, InvestmentSchemeEstimate> sourceEstimateMap = investmentSchemeEstimates.stream().collect(Collectors.toMap(InvestmentSchemeEstimate::getSource, Function.identity(), (v1, v2) -> v2));
                if (sourceEstimateMap.containsKey("1")) {
                    use = sourceEstimateMap.get("1");
                }
                if (sourceEstimateMap.containsKey("2")) {
                    use = sourceEstimateMap.get("2");
                }
                if (sourceEstimateMap.containsKey("3")) {
                    use = sourceEstimateMap.get("3");
                }

                String estimateStr = NumberUtil.roundStr((use.getArchitecture().add(use.getDevice()).add(use.getInstallation()).add(use.getOther())).toString(), 2);
                result.setEstimate(estimateStr);
                result.setEstimateId(use.getId());

                List<YearInvestmentScheme> yearInvestmentSchemes = this.list(new LambdaQueryWrapperX<>(YearInvestmentScheme.class).eq(YearInvestmentScheme::getInvestmentId, investId));
                if (!CollectionUtils.isEmpty(yearInvestmentSchemes)) {
                    //截止Y-2年下达投资计划
                    Map<String, List<YearInvestmentScheme>> groupByYearNameMap = yearInvestmentSchemes.stream().filter(o -> Integer.parseInt(o.getYearName()) <= (y - 2)).collect(Collectors.groupingBy(YearInvestmentScheme::getYearName));
                    List<YearInvestmentScheme> y_2 = new ArrayList<>();
                    groupByYearNameMap.forEach((k, v) -> {
                        if (v.size() == 2) {
                            y_2.addAll(v.stream().filter(o -> StrUtil.isNotBlank(o.getOldId())).collect(Collectors.toList()));
                        } else {
                            y_2.addAll(v);
                        }

                    });
                    BigDecimal cutOffGiveY_2 = y_2.stream().map(o -> o.getArchitecture().add(o.getDevice()).add(o.getOther()).add(o.getInstallation())).reduce(BigDecimal.ZERO, BigDecimal::add);
                    result.setCutOffGiveY_2(NumberUtil.roundStr(cutOffGiveY_2.toString(), 2));

                    //截止Y-2年投资计划完成
                    BigDecimal completeY_2 = y_2.stream().map(YearInvestmentScheme::getTotalDo).reduce(BigDecimal.ZERO, BigDecimal::add);
                    result.setCutOffCompleteY_2(NumberUtil.roundStr(completeY_2.toString(), 2));
                    //Y-1年投资计划
                    List<YearInvestmentScheme> y_1 = yearInvestmentSchemes.stream().filter(o -> Objects.equals(Integer.parseInt(o.getYearName()), (y - 1))).collect(Collectors.toList());
                    if (y_1.size() == 1) {
                        BigDecimal reduce = y_1.stream().map(o -> o.getArchitecture().add(o.getDevice()).add(o.getOther()).add(o.getInstallation())).reduce(BigDecimal.ZERO, BigDecimal::add);
                        result.setLastYear(NumberUtil.roundStr(reduce.toString(), 2));
                    } else {
                        BigDecimal reduce = y_1.stream().filter(o -> StrUtil.isNotBlank(o.getOldId())).map(o -> o.getArchitecture().add(o.getDevice()).add(o.getOther()).add(o.getInstallation())).reduce(BigDecimal.ZERO, BigDecimal::add);
                        result.setLastYear(NumberUtil.roundStr(reduce.toString(), 2));
                    }
                } else {
                    //截止Y-2年下达投资计划
                    result.setCutOffGiveY_2("0");
                    //截止Y-2年投资计划完成
                    result.setCutOffCompleteY_2("0");
                    //Y-1年投资计划
                    result.setLastYear("0");
                }
                result.setYearName(String.valueOf(y));
                UserVO currentUser = userRedisHelper.getUserById(CurrentUserHelper.getCurrentUserId());
                if (Objects.nonNull(currentUser)) {
                    result.setRspUserName(currentUser.getName());
                }
                YearInvestmentSchemeDTO yearInvestmentSchemeDTO = BeanCopyUtils.convertTo(result, YearInvestmentSchemeDTO::new);
                yearInvestmentSchemeDTO.setStatus(YearInvestmentSchemeStatus.GIVE.getCode());
                yearInvestmentSchemeDTO.setInvestmentId(investId);


                List<MonthInvestmentSchemeDTO> monthInvestmentSchemes = new ArrayList<>();
                for (int i = 1; i <= 12; i++) {
                    MonthInvestmentSchemeDTO monthInvestmentSchemeDTO = new MonthInvestmentSchemeDTO();
                    if (i == 1) {
                        monthInvestmentSchemeDTO.setName(i + "月");
                    } else {
                        monthInvestmentSchemeDTO.setName("1-" + i + "月");
                    }
                    monthInvestmentSchemeDTO.setActual(BigDecimal.ZERO);
                    monthInvestmentSchemeDTO.setPredicate(BigDecimal.ZERO);
                    monthInvestmentSchemes.add(monthInvestmentSchemeDTO);
                }
                yearInvestmentSchemeDTO.setMonthInvestmentSchemes(monthInvestmentSchemes);
                yearInvestmentSchemeDTO.setId(null);
                yearInvestmentSchemeDTO.setClassName(null);
                YearInvestmentSchemeVO yearInvestmentSchemeVO = create(yearInvestmentSchemeDTO);
                return yearInvestmentSchemeVO.getId();
            } else {
                if (currentYearInvestmentSchemes.size() == 2) {
                    throw new PMSException(PMSErrorCode.PMS_ERR, "本年度投资计划调整已申请");
                } else {
                    YearInvestmentScheme yearInvestmentScheme = currentYearInvestmentSchemes.get(0);
                    if (!Objects.equals(YearInvestmentSchemeStatus.GIVE.getCode(), yearInvestmentScheme.getStatus())) {
                        throw new PMSException(PMSErrorCode.PMS_ERR, "本年度投资计划暂未下达");
                    } else {
                        return yearInvestmentScheme.getId();
                    }
                }
            }
        } else {

            YearInvestmentScheme oldYearInvestmentScheme = this.getById(yearId);
            if (StrUtil.isNotBlank(oldYearInvestmentScheme.getOldId())) {
                throw new PMSException(PMSErrorCode.PMS_ERR, "只能申请一次年度投资计划调整");
            }
            if (!Objects.equals(YearInvestmentSchemeStatus.GIVE.getCode(), oldYearInvestmentScheme.getStatus())) {
                throw new PMSException(PMSErrorCode.PMS_ERR, "只能调整已下达的年度投资计划");
            }

            if (!Objects.equals(DateUtil.format(new Date(), "yyyy"), oldYearInvestmentScheme.getYearName())) {
                throw new PMSException(PMSErrorCode.PMS_ERR, "只能调整当前年度投资计划");
            }
            return yearId;
        }
    }

    @Override
    public YearInvestmentSchemeVO change(String yearId, YearInvestmentSchemeDTO yearInvestmentSchemeDTO) throws Exception {
        YearInvestmentScheme oldYearInvestmentScheme = this.getById(yearId);

        if (StrUtil.isNotBlank(oldYearInvestmentScheme.getOldId())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "只能申请一次年度投资计划调整");
        }


        if (!Objects.equals(YearInvestmentSchemeStatus.GIVE.getCode(), oldYearInvestmentScheme.getStatus())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "只能调整已下达的年度投资计划");
        }

        if (!Objects.equals(DateUtil.format(new Date(), "yyyy"), oldYearInvestmentScheme.getYearName())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "只能调整当前年度投资计划");
        }

        YearInvestmentScheme yearInvestmentScheme = BeanCopyUtils.convertTo(yearInvestmentSchemeDTO, YearInvestmentScheme::new);
        yearInvestmentScheme.setId(null);
        yearInvestmentScheme.setStatus(YearInvestmentSchemeStatus.WRITE.getCode());
        yearInvestmentScheme.setOldId(yearId);
        yearInvestmentScheme.setEstimateId(oldYearInvestmentScheme.getEstimateId());
        yearInvestmentScheme.setProjectId(oldYearInvestmentScheme.getProjectId());
        yearInvestmentScheme.setTotalDo(oldYearInvestmentScheme.getTotalDo());
        yearInvestmentScheme.setCloseFlag(true);


        // 新增 项目编号和项目编号对应的序号 方便排序过滤 冗余字段
        String projectId = oldYearInvestmentScheme.getProjectId();
        Project project = projectService.getById(projectId);
        if(project != null){
            String projectNumber = project.getNumber();
            String[] s = projectNumber.split("-");
            if(s[s.length-1].matches("^[0-9]+$")){
                int i = Integer.parseInt(s[s.length-1]);
                yearInvestmentScheme.setSerialNumber(i);
            }

            yearInvestmentScheme.setProjectNumber(projectNumber);
        }

        this.save(yearInvestmentScheme);


        List<MonthInvestmentScheme> dbMonthInvestmentSchemes = monthInvestmentSchemeService.list(new LambdaQueryWrapperX<>(MonthInvestmentScheme.class).eq(MonthInvestmentScheme::getYearId, yearId).eq(MonthInvestmentScheme::getHasDo, 1));
        Map<String, MonthInvestmentScheme> monthInvestmentSchemeMap = dbMonthInvestmentSchemes.stream().collect(Collectors.toMap(o -> {
            if (StrUtil.contains(o.getName(), "-")) {
                String[] split = o.getName().split("-");
                return split[1].replace("月", "");
            } else {
                return o.getName().replace("月", "");
            }
        }, Function.identity()));

        Integer max = monthInvestmentSchemeMap.keySet().stream().map(Integer::parseInt).max(Integer::compareTo).orElse(0);
        List<MonthInvestmentSchemeDTO> monthInvestmentSchemeDTOS = yearInvestmentSchemeDTO.getMonthInvestmentSchemes();
        List<MonthInvestmentScheme> monthInvestmentSchemes = BeanCopyUtils.convertListTo(monthInvestmentSchemeDTOS, MonthInvestmentScheme::new);
        monthInvestmentSchemes.forEach(m -> {
            m.setYearId(yearInvestmentScheme.getId());
            String monthName;
            if (StrUtil.contains(m.getName(), "-")) {
                String[] split = m.getName().split("-");
                monthName = split[1].replace("月", "");
            } else {
                monthName = m.getName().replace("月", "");
            }
            if (Integer.parseInt(monthName) <= max) {
                m.setHasDo(1);
                MonthInvestmentScheme oldMonth = monthInvestmentSchemeMap.get(monthName);
                if (Objects.nonNull(oldMonth)) {
                    m.setActual(oldMonth.getActual());
                    m.setPredicate(oldMonth.getPredicate());
                } else {
                    m.setActual(BigDecimal.ZERO);
                    m.setPredicate(BigDecimal.ZERO);
                }
            } else {
                m.setHasDo(0);
                m.setActual(BigDecimal.ZERO);
            }
        });
        monthInvestmentSchemeService.saveBatch(monthInvestmentSchemes);

        return this.detail(yearInvestmentScheme.getId(), null);
    }

    @Override
    public List<YearInvestmentSchemeVO> changeList(String yearId) throws Exception {
        List<YearInvestmentSchemeVO> result = new ArrayList<>();
        YearInvestmentSchemeVO detail = this.detail(yearId, null);
        if (StrUtil.isNotBlank(detail.getOldId())) {
            YearInvestmentSchemeVO oldDetail = this.detail(detail.getOldId(), null);
            result.add(detail);
            result.add(oldDetail);
            return result;
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public Boolean updateStatus(ChangeStatusMessageDTO message) throws Exception {
        YearInvestmentScheme yearInvestmentScheme = this.getById(message.getBusinessId());
        yearInvestmentScheme.setStatus(message.getStatus());
        Boolean result = this.updateById(yearInvestmentScheme);
        if (Objects.equals(message.getStatus(), YearInvestmentSchemeStatus.GIVE.getCode())) {
            boolean isUpdate = false;
            InvestmentScheme investmentScheme = investmentSchemeService.getById(yearInvestmentScheme.getInvestmentId());
            if (Objects.equals(investmentScheme.getStatus(), InvestmentSchemeStatus.WRITE.getCode())) {
                investmentScheme.setStatus(InvestmentSchemeStatus.PROCESS.getCode());
                isUpdate = true;
            }
            if (yearInvestmentScheme.getCloseFlag()) {
                investmentScheme.setCloseFlag(true);
                isUpdate = true;
            }
            if (isUpdate) {
                investmentSchemeService.updateById(investmentScheme);
            }

        }
        return result;
    }

    @Override
    public Boolean updateEntityProjectNumber() throws Exception {
        List<YearInvestmentScheme> yearInvestmentSchemes = this.list();
        if (CollectionUtils.isEmpty(yearInvestmentSchemes)) {
            return false;
        }

        List<String> projectIdList = yearInvestmentSchemes.stream().map(YearInvestmentScheme::getProjectId).distinct().collect(Collectors.toList());
        Map<String, String> idToNumber = projectService.getIdToNumberByProjectIdList(projectIdList);

        Map<String, Integer> numberToSerialNumber = new HashMap<>();
        for (YearInvestmentScheme yearInvestmentScheme : yearInvestmentSchemes) {
            String projectId = yearInvestmentScheme.getProjectId();

            String projectNumber = idToNumber.get(projectId);
            if (StringUtils.isEmpty(projectNumber)) {
                continue;
            }
            yearInvestmentScheme.setProjectNumber(projectNumber);
            Integer serialNumber = numberToSerialNumber.get(projectNumber);
            if (StringUtils.isEmpty(serialNumber)) {
                String[] s = projectNumber.split("-");
                int i = Integer.parseInt(s[1]);
                serialNumber = i;
            }
            yearInvestmentScheme.setSerialNumber(serialNumber);
            numberToSerialNumber.put(projectNumber, serialNumber);
        }
        this.updateBatchById(yearInvestmentSchemes);
        return true;
    }
}
