package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * JobPostAuthorize Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-08 20:33:32
 */
@TableName(value = "pmsx_job_post_authorize")
@ApiModel(value = "JobPostAuthorizeEntity对象", description = "作业授权信息")
@Data

public class    JobPostAuthorize extends  ObjectEntity  implements Serializable{

    /**
     * 人员ID
     */
    @ApiModelProperty(value = "人员ID")
    @TableField(value = "person_id")
    private String personId;

    /**
     * 人员编号/工号
     */
    @ApiModelProperty(value = "人员编号/工号")
    @TableField(value = "person_code")
    private String userCode;

    /**
     * 所属作业ID
     */
    @ApiModelProperty(value = "所属作业ID")
    @TableField(value = "job_id")
    private String jobId;

    /**
     * 所属大修轮次
     */
    @ApiModelProperty(value = "所属大修轮次")
    @TableField(value = "repair_round")
    private String repairRound;

    /**
     * 所属计划ID
     */
    @ApiModelProperty(value = "所属计划ID")
    @TableField(value = "plan_scheme_id")
    private String planSchemeId;

    /**
     * 作业岗位编码
     */
    @ApiModelProperty(value = "作业岗位编码")
    @TableField(value = "job_post_code")
    private String jobPostCode;

    /**
     * 是否满足授权*（0-待确认，1-不满足，2-满足）
     */
    @ApiModelProperty(value = "是否满足授权*（0-待确认，1-不满足，2-满足）")
    @TableField(value = "is_authorization")
    private Integer isAuthorization;

    /**
     * 授权到期日期
     */
    @ApiModelProperty(value = "授权到期日期")
    @TableField(value = "end_date")
    private Date endDate;

    /**
     * 人员所在基地
     */
    @ApiModelProperty(value = "人员所在基地")
    @TableField(value = "base_place_code")
    private String basePlaceCode;

    /**
     * 人员所在基地名称
     */
    @ApiModelProperty(value = "人员所在基地名称")
    @TableField(value = "base_place_name")
    private String basePlaceName;

    /**
     * 进入基地时间
     */
    @ApiModelProperty(value = "进入基地时间")
    @TableField(value = "enter_base_date")
    private Date enterBaseDate;

    /**
     * 授权状态（101-未授权，130-已授权）
     */
    @ApiModelProperty(value = "授权状态（101-未授权，130-已授权）")
    @TableField(value = "authorize_status")
    private Integer authorizeStatus;


    /**
     * 是否申请岗位等效
     */
    @ApiModelProperty(value = "是否申请岗位等效")
    @TableField(value = "is_apply_job_equ")
    private Boolean isApplyJobEqu;

    /**
     * 岗位授权指引（冗余）
     */
    @ApiModelProperty(value = "岗位授权指引（冗余）")
    @TableField(value = "authorization_guide")
    private String authorizationGuide;

    /**
     * 授权起始日期
     */
    @ApiModelProperty(value = "授权起始日期")
    @TableField(value = "authorize_start_date")
    private Date authorizeStartDate;

    // 为了处理数据 冗余ID
    @ApiModelProperty(value = "人员管理ID")
    @TableField(value = "person_manage_id")
    private String personManageId;

    @ApiModelProperty(value = "人员台账ID")
    @TableField(value = "person_ledger_id")
    private String personLedgerId;

    @ApiModelProperty(value = "计划起始时间")
    @TableField(value = "plan_begin_date")
    private Date planBeginDate;
    @ApiModelProperty(value = "计划结束时间")
    @TableField(value = "plan_end_date")
    private Date planEndDate;

    @ApiModelProperty(value = "实际起始时间")
    @TableField(value = "act_begin_date")
    private Date actBeginDate;
    @ApiModelProperty(value = "实际结束时间")
    @TableField(value = "act_end_date")
    private Date actEndDate;

    @TableField(
            exist = false
    )
    private String userName;

    @TableField(
            exist = false
    )
    private String userId;
}
