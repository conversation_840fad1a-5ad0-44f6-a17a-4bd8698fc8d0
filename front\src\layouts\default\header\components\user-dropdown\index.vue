<template>
  <Dropdown
    placement="bottomLeft"
    :overlay-class-name="`${prefixCls}-dropdown-overlay`"
  >
    <span
      :class="[prefixCls, `${prefixCls}--${theme}`]"
      class="flex"
    >
      <!--      <img-->
      <!--        :class="`${prefixCls}__header`"-->
      <!--        :src="getUserInfo.avatar"-->
      <!--      >-->
      <img
        :class="`${prefixCls}__header`"
        src="/images/header.jpg"
      >
      <span :class="`${prefixCls}__info hidden md:block`">
        <span
          :class="`${prefixCls}__name  `"
          class="truncate"
        >
          {{ getUserInfo.realName }}
        </span>
      </span>
    </span>

    <template #overlay>
      <Menu @click="handleMenuClick">
        <MenuItem
          :text="t('layout.header.dropdownItemLoginOut')"
          icon="orion-icon-poweroff"
          type="logout"
        />
      </Menu>
    </template>
  </Dropdown>
</template>
<script lang="ts">
// components
import { Dropdown, Menu } from 'ant-design-vue';
import type { MenuInfo } from 'ant-design-vue/lib/menu/src/interface';

import { defineComponent, computed } from 'vue';

import { DOC_URL } from '/@/settings/siteSetting';

import { useUserStore } from '/@/store/modules/user';
import { useHeaderSetting } from '/@/hooks/setting/useHeaderSetting';
import { useI18n } from '/@/hooks/web/useI18n';
import { useDesign } from '/@/hooks/web/useDesign';
import { useModal } from '/@/components/Modal';

// @ts-ignore
// import headerImg from '/@/assets/images/header.jpg';
import { propTypes } from '/@/utils/propTypes';
import { openWindow } from '/@/utils';

import { createAsyncComponent } from '/@/utils/factory/createAsyncComponent';

  type MenuEvent = 'logout' | 'doc' | 'lock';

export default defineComponent({
  name: 'UserDropdown',
  components: {
    Dropdown,
    Menu,
    MenuItem: createAsyncComponent(() => import('./DropMenuItem.vue')),
    MenuDivider: Menu.Divider,
  },
  props: {
    theme: propTypes.oneOf(['dark', 'light']),
  },
  setup() {
    const { prefixCls } = useDesign('header-user-dropdown');
    const { t } = useI18n();
    const { getShowDoc, getUseLockPage } = useHeaderSetting();
    const userStore = useUserStore();

    const getUserInfo = computed(() => {
      const { realName = '', avatar, desc } = userStore.getUserInfo || {};
      return {
        realName,
        avatar: avatar || '',
      };
    });

    const [register, { openModal }] = useModal();

    function handleLock() {
      openModal(true);
    }

    //  login out
    function handleLoginOut() {
      userStore.confirmLoginOut();
    }

    // open doc
    function openDoc() {
      openWindow(DOC_URL);
    }

    function handleMenuClick(e: MenuInfo) {
      switch (e.key as MenuEvent) {
        case 'logout':
          handleLoginOut();
          break;
        case 'doc':
          openDoc();
          break;
        case 'lock':
          handleLock();
          break;
      }
    }

    return {
      prefixCls,
      t,
      getUserInfo,
      handleMenuClick,
      getShowDoc,
      register,
      getUseLockPage,
    };
  },
});
</script>
<style lang="less">
  @prefix-cls: ~'@{namespace}-header-user-dropdown';

  .@{prefix-cls} {
    height: ~`getPrefixVar('header-height')`;
    padding: 0 0 0 10px;
    padding-right: 10px;
    overflow: hidden;
    font-size: 12px;
    cursor: pointer;
    align-items: center;

    img {
      width: 24px;
      height: 24px;
      margin-right: 12px;
    }

    &__header {
      border-radius: 50%;
    }

    &__name {
      font-size: 14px;
    }

    &--dark {
      &:hover {
        background-color: ~`getPrefixVar('primary-color-hover')`;
      }
    }

    &--light {
      &:hover {
        background-color: #f6f6f6;
      }

      .@{prefix-cls}__name {
        color: #c9d1d9;
      }

      .@{prefix-cls}__desc {
        color: #7c8087;
      }
    }

    &-dropdown-overlay {
      .ant-dropdown-menu-item {
        min-width: 160px;
      }
    }
  }
</style>
