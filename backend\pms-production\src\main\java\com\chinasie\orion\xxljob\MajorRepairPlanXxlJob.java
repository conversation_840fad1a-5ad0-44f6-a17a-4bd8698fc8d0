package com.chinasie.orion.xxljob;

import com.chinasie.orion.constant.JobManageStatusEnum;
import com.chinasie.orion.constant.MessageNodeDict;
import com.chinasie.orion.domain.entity.JobManage;
import com.chinasie.orion.msc.api.MscBuildHandlerManager;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.repository.JobManageMapper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class MajorRepairPlanXxlJob {

    @Autowired
    JobManageMapper jobManageMapper;

    @Autowired
    MscBuildHandlerManager mscBuildHandlerManager;

    @XxlJob(value = "majorRepairPlanProcessHandler")
    public void majorRepairPlanProcessHandler() {
        LambdaQueryWrapperX<JobManage> wrapper = new LambdaQueryWrapperX<>(JobManage.class);
        wrapper.eq(JobManage::getIsMajorProject,true);
        wrapper.eq(JobManage::getBusStatus, JobManageStatusEnum.IMPL.getStatus());
        List<JobManage> jobManages = jobManageMapper.selectList(wrapper);

        jobManages.forEach(item->{
            mscBuildHandlerManager.send(item, MessageNodeDict.NODE_MAJOR_PROCESS);
        });
    }


}
