package com.chinasie.orion.domain.vo;

import com.chinasie.orion.domain.dto.pas.TypeAttrValueDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/1/18 13:37
 */
@Data
@ApiModel(value = "QuestionManagementVO对象", description = "风险管理")
public class QuestionManagementVO extends ObjectVO {
    /**
     * 问题来源
     */
    @ApiModelProperty(value = "问题来源ID")
    private String questionSource;
    @ApiModelProperty(value = "问题来源名称")
    private String questionSourceName;

    /**
     * 验证程度
     */
    @ApiModelProperty(value = "严重程度ID")
    private String seriousLevel;
    @ApiModelProperty(value = "严重程度名称")
    private String seriousLevelName;

    /**
     * 预计完成时间
     */
    @ApiModelProperty(value = "预计完成时间")
    private Date predictEndTime;

    /**
     * 问题类型
     */
    @ApiModelProperty(value = "问题类型ID")
    private String questionType;
    @ApiModelProperty(value = "问题类型名称")
    private String questionTypeName;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 提出人Id
     */
    @ApiModelProperty(value = "提出人")
    private String exhibitor;
    private String exhibitorName;

    @ApiModelProperty(value = "提出人部门")
    private String exhibitorDept;

    @ApiModelProperty(value = "提出人部门名称")
    private String exhibitorDeptName;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 问题内容
     */
    @ApiModelProperty(value = "问题内容")
    private String content;

    /**
     * 提出时间
     */
    @ApiModelProperty(value = "提出时间")
    private Date proposedTime;


    /**
     * 进度
     */
    @ApiModelProperty(value = "进度")
    private BigDecimal schedule;
    private String scheduleName;

    /**
     * 接收人
     */
    @ApiModelProperty(value = "接收人id")
    private String recipient;
    @ApiModelProperty(value = "接收人名称")
    private String recipientName;



    /**
     * 负责人id
     */
    @ApiModelProperty(value = "负责人id")
    private String principalId;
    @ApiModelProperty(value = "负责人名称")
    private String principalName;



    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级id")
    private String priorityLevel;
    @ApiModelProperty(value = "优先级名称")
    private String priorityLevelName;

    @ApiModelProperty("属性值")
    private List<TypeAttrValueDTO> typeAttrValueDTOList;


    /**
     * 产品编码
     */
    @ApiModelProperty(value = "产品编码")
    private String productNumber;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String materialNumber;

    /**
     * 生产订单
     */
    @ApiModelProperty(value = "生产订单")
    private String productionOrders;

    /**
     * 产品编号（SN）
     */
    @ApiModelProperty(value = "产品编号（SN）")
    private String productNumberSn;

    /**
     * 阶段
     */
    @ApiModelProperty(value = "阶段")
    private String stage;
    @ApiModelProperty(value = "阶段名称")
    private String stageName;


    /**
     * 过程环节
     */
    @ApiModelProperty(value = "过程环节")
    private String processLink;
    @ApiModelProperty(value = "过程环节名称")
    private String processLinkName;

    /**
     * 过程分类
     */
    @ApiModelProperty(value = "过程分类")
    private String processClassifi;
    @ApiModelProperty(value = "过程分类名称")
    private String processClassifiName;

    /**
     * 问题现象一级分类
     */
    @ApiModelProperty(value = "问题现象一级分类")
    private String problemPhenomenonOne;
    @ApiModelProperty(value = "问题现象一级分类名称")
    private String problemPhenomenonOneName;

    /**
     * 问题现象二级分类
     */
    @ApiModelProperty(value = "问题现象二级分类")
    private String problemPhenomenonTwo;
    @ApiModelProperty(value = "问题现象二级分类名称")
    private String problemPhenomenonTwoName;

    /**
     * 问题现象三级分类
     */
    @ApiModelProperty(value = "问题现象三级分类")
    private String problemPhenomenonTh;
    @ApiModelProperty(value = "问题现象三级分类名称")
    private String problemPhenomenonThName;

    /**
     * 问题等级分类
     */
    @ApiModelProperty(value = "问题等级分类")
    private String problemLevel;
    @ApiModelProperty(value = "问题等级分类名称")
    private String problemLevelName;

    /**
     * 一级原因分类
     */
    @ApiModelProperty(value = "一级原因分类")
    private String reasionOne;
    @ApiModelProperty(value = "一级原因分类名称")
    private String reasionOneName;

    /**
     * 二级原因分类
     */
    @ApiModelProperty(value = "二级原因分类")
    private String reasionTwo;
    @ApiModelProperty(value = "二级原因分类名称")
    private String reasionTwoName;

    /**
     * 三级原因分类
     */
    @ApiModelProperty(value = "三级原因分类")
    private String reasionThree;
    @ApiModelProperty(value = "三级原因分类名称")
    private String reasionThreeName;

    /**
     * 原因分析描述
     */
    @ApiModelProperty(value = "原因分析描述")
    private String reasionRemark;

    /**
     * 纠正分类
     */
    @ApiModelProperty(value = "纠正分类")
    private String correctClassifi;
    @ApiModelProperty(value = "纠正分类名称")
    private String correctClassifiName;

    /**
     * 关联ECN编号
     */
    @ApiModelProperty(value = "关联ECN编号")
    private String ecnNumber;

    /**
     * 纠正描述
     */
    @ApiModelProperty(value = "纠正描述")
    private String correctRemark;

    /**
     * 问题升级类型
     */
    @ApiModelProperty(value = "问题升级类型")
    private String questionUpType;

    /**
     * 是否疑难技术问题
     */
    @ApiModelProperty(value = "是否疑难技术问题")
    private Boolean isDiTechnicalIssues;

    /**
     * 是否综合评估不再解决
     */
    @ApiModelProperty(value = "是否综合评估不再解决")
    private Boolean isAssess;

    /**
     * 是否生态问题
     */
    @ApiModelProperty(value = "是否生态问题")
    private Boolean isEcologicalIssues;

    /**
     * 是否典型质量案例
     */
    @ApiModelProperty(value = "是否典型质量案例")
    private Boolean isQualityUseCases;

    /**
     * 举一反三
     */
    @ApiModelProperty(value = "举一反三")
    private String oneCaseToAnother;

    /**
     * 纠正措施描述
     */
    @ApiModelProperty(value = "纠正措施描述")
    private String correAcDescription;

    /**
     * 意见类别
     */
    @ApiModelProperty(value = "意见类别")
    private String opinionCategories;
    @ApiModelProperty(value = "意见类别名称")
    private String opinionCategoriesName;

    /**
     * 评审要点
     */
    @ApiModelProperty(value = "评审要点")
    private String reviewPoints;
    @ApiModelProperty(value = "评审要点名称")
    private String reviewPointsName;

    /**
     * 是否技术问题
     */
    @ApiModelProperty(value = "是否技术问题")
    private Boolean isTechnicalIssues;

    /**
     * 是否典型问题
     */
    @ApiModelProperty(value = "是否典型问题")
    private Boolean isTypicalProblems;

    /**
     * 意见分类
     */
    @ApiModelProperty(value = "意见分类")
    private String opClassification;
    @ApiModelProperty(value = "意见分类名称")
    private String opClassificationName;

    /**
     * 采纳情况
     */
    @ApiModelProperty(value = "采纳情况")
    private String adoptionSituation;
    @ApiModelProperty(value = "采纳情况名称")
    private String adoptionSituationName;

    /**
     * 整体改进描述
     */
    @ApiModelProperty(value = "整体改进描述")
    private String overallDescription;

    /**
     * 是否主动关联
     */
    @ApiModelProperty(value = "是否主动关联")
    private Boolean isActiveRelation;

    @ApiModelProperty
    private Boolean isPlan;

    @ApiModelProperty(value = "关联风险Id")
    private String riskId;
    @ApiModelProperty(value = "关联风险名称")
    private String riskName;
}
