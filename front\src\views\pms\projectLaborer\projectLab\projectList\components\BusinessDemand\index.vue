<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
  >
    <template
      v-if="pageType==='page'"
      #toolbarLeft
    >
      <div
        class="businessDemandMainTable_add"
      >
        <BasicButton
          type="primary"
          icon="add"
          @click="addTableNode"
        >
          新增需求
        </BasicButton>
      </div>
    </template>
    <!--    <template #toolbarRight>-->
    <!--      <AInputSearch-->
    <!--        v-model:value="tableSearchVal"-->
    <!--        style="width: 200px"-->
    <!--        placeholder="请输入名称或者编号"-->
    <!--        @search="searchForm"-->
    <!--      />-->
    <!--    </template>-->
    <template
      v-if="pageType==='page'"
      #name="{ record }"
    >
      <div
        class="action-btn flex-te"
        @click="openDetails(record)"
      >
        {{ record.name }}
      </div>
    </template>

    <template #action="{record}">
      <BasicTableAction
        :actions="actionsBtn"
        :record="record"
      />
    </template>
  </OrionTable>
  <AddTableNode
    v-if="pageType==='page'"
    :columns="tableOptions.columns"
    @register="register"
    @update="updateData"
  />
  <SearchDrawer
    @register="SearchDrawerRegister"
    @search="searchTable"
  />
</template>
<script lang="ts">
import {
  computed, defineComponent, inject, reactive, ref, toRefs,
} from 'vue';
import {
  BasicButton, BasicTableAction, ITableActionItem, OrionTable, useDrawer,
} from 'lyra-component-vue3';
import { Input, message } from 'ant-design-vue';
import { stampDate } from '/@/utils/dateUtil';
import { useQiankun } from '/@/utils/qiankun/useQiankun';
import Api from '/@/api';
import { useRouter } from 'vue-router';
import AddTableNode from './components/AddTableNode.vue';
import SearchDrawer from './components/SearchDrawer/index.vue';

export default defineComponent({
  name: 'BusinessDemand',
  components: {
    OrionTable,
    // AInputSearch: Input.Search,/
    AddTableNode,
    SearchDrawer,
    BasicButton,
    BasicTableAction,
  },
  props: {
    pageType: {
      type: String,
      default: 'page',
    },
    modelName: {
      type: String,
      default: 'pdm',
    },
  },
  setup(props) {
    const [SearchDrawerRegister, { openDrawer: searchDrawerModal }] = useDrawer();
    const { mainRouter } = useQiankun();
    const formData: any = inject('formData', {});
    const powerData: any = inject('powerData', {});
    const state:any = reactive({
      formId: formData?.value?.id,
      tableSearchVal: '',
      searchParams: {},
      powerData: [],
    });

    const [register, { openDrawer }] = useDrawer();
    const tableRef = ref(null);
    const tableOptions = ref({
      deleteToolButton: 'add|enable|disable|delete',
      rowSelection: props.pageType === 'page' ? {} : false,
      showSmallSearch: true,
      showIndexColumn: false,
      isFilter2: true,
      filterConfigName: 'PMS_PROJECTMANAGE_DEMANDMANAGE',
      api: computed(() => getTableList()),
      batchDeleteApi({ ids }) {
        return new Api('/pas').fetch(ids, 'demand-management/removeBatch', 'DELETE').then((res) => {
          message.success('删除数据成功');
          tableRef.value.reload();
        });
      },
      columns: [
        {
          title: '名称',
          dataIndex: 'name',
          ellipsis: true,
          slots: { customRender: 'name' },
          align: 'left',
          minWidth: 200,
        },
        {
          title: '编号',
          dataIndex: 'number',
          width: 150,
        },
        {
          title: '提出人',
          dataIndex: 'exhibitorName',
          width: 150,
        },
        {
          title: '提出日期',
          dataIndex: 'proposedTime',
          customRender: ({
            text, record, index, column,
          }) => (record.proposedTime && record.proposedTime.length > 0 ? stampDate(record.proposedTime, 'yyyy-MM-dd') : ''),
          width: 150,
        },
        {
          title: '期望完成日期',
          dataIndex: 'predictEndTime',
          customRender: ({
            text, record, index, column,
          }) => (record.predictEndTime && record.predictEndTime.length > 0 ? stampDate(record.predictEndTime, 'yyyy-MM-dd') : ''),
          width: 150,
        },
        {
          title: '优先级',
          dataIndex: 'priorityLevelName',
          width: 100,
        },
        {
          title: '进度',
          dataIndex: 'scheduleName',
          width: 100,
        },
        {
          title: '状态',
          dataIndex: 'status',
          align: 'left',
          width: 150,
          slots: { customRender: 'status' },
        },

        {
          title: '需求类型',
          dataIndex: 'typeName',
          align: 'left',
          width: 150,
        },

        {
          title: '负责人',
          dataIndex: 'principalName',
          width: 100,
        },
        {
          title: '修改日期',
          ellipsis: true,
          dataIndex: 'modifyTime',
          customRender: ({
            text, record, index, column,
          }) => (record.modifyTime && record.modifyTime.length > 0 ? stampDate(record.modifyTime, 'yyyy-MM-dd') : ''),
          width: 150,
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 150,
          align: 'left',
          fixed: 'right',
          slots: { customRender: 'action' },
        },
      ],
      beforeFetch,
    });
    function getTableList() {
      return (tableParams) => new Api('/pas').fetch(tableParams, 'demand-management/getPage', 'POST');
    }

    function beforeFetch(T) {
      return {
        ...T,
        query: {
          ...state.searchParams,
          projectId: formData?.value?.id,
        },
        queryCondition: [
          {
            column: 'name',
            type: 'like',
            link: 'or',
            value: state.tableSearchVal,
          },
          {
            column: 'number',
            type: 'like',
            link: 'or',
            value: state.tableSearchVal,
          },
        ],
      };
    }
    const addTableNode = () => {
      let drawerData :any = {
        projectId: formData?.value?.id,
        fromObjName: formData?.value?.name,
        modelName: props.modelName,
      };
      let dataSource = tableRef.value.getDataSource();
      if (dataSource.length > 0) {
        drawerData.dirId = dataSource[0].dirId;
      }
      openDrawer(true, {
        type: 'add',
        data: drawerData,
      });
    };

    const clickType = (type) => {
      let selectRow = tableRef.value.getSelectRows();
      if (selectRow.length === 0 && type !== 'search') {
        message.warning('请选择数据');
        return;
      }
      switch (type) {
        case 'search':
          searchDrawerModal(true);
          break;
      }
    };
    const router = useRouter();
    const openDetails = (record) => {
      router.push({
        // name: 'PMSDemandManagementDetails', pms需求管理详情
        name: 'DemandManagementDetails',
        query: {
          itemId: record.id,
        },
      });
    };
    const updateData = () => {
      tableRef.value.reload();
    };
    const searchTable = (params) => {
      state.searchParams = params;
      if (params.name) {
        state.tableSearchVal = params.name;
      }
      tableRef.value.reload();
    };
    const searchForm = (val) => {
      tableRef.value.reload();
    };

    const actionsBtn:ITableActionItem[] = [
      {
        text: '编辑',

        isShow: true,

        onClick(record:any) {
          openDrawer(true, {
            type: 'edit',
            data: record,
          });
        },
      },
      {
        text: '删除',
        isShow: true,
        modal(record:any) {
          return new Api('/pas').fetch([record.id], 'demand-management/removeBatch', 'DELETE').then((res) => {
            message.success('删除数据成功');
            tableRef.value.reload();
          });
        },
      },
    ];

    return {
      ...toRefs(state),
      tableOptions,
      SearchDrawerRegister,
      tableRef,
      addTableNode,
      clickType,
      register,
      openDetails,
      updateData,
      searchForm,
      searchTable,
      actionsBtn,
    };
  },
});
</script>
<style lang="less" scoped>

</style>
