<script setup lang="ts">
import {
  DataStatusTag,
  OrionTable,
} from 'lyra-component-vue3';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';
import dayjs from 'dayjs';
import { useRouter } from 'vue-router';
import ActiveTagList from './ActiveTagList.vue';
const router = useRouter();
const SituationColorEnum = {
  100015: '#52c41a', // 正常完成
  100016: '#ff0000', // 逾期完成
  100017: '#52c41a', // 待责任人处理
  100018: '#52c41a', // 调整申请中
  100019: '#52c41a', // 待完成确认
  100020: '#52c41a', // 下发审批中
  100021: '#52c41a', // 完成审批中
  100022: '#52c41a', // 暂停审批中
  100023: '#52c41a', // 终止审批中
  100024: '#52c41a', // 启动审批中
  100025: '#52c41a', // 执行完成审批中
};
const columns = [
  // 计划名称
  {
    title: '计划名称',
    dataIndex: 'name',
    minWidth: 300,
    fixed: 'left',
    customRender({
      record, text,
    }) {
      return h('span', {
        class: 'action-btn',
        onClick() {
          router.push({
            name: 'ProPlanDetails',
            params: { id: record.id },
          });
        },
      }, text);
    },
  },
  // 计划层级
  {
    title: '计划层级',
    dataIndex: 'level',
    width: 100,
    customRender: ({ text }) => `${text}级`,
  },
  // 计划类型
  {
    title: '计划类型',
    dataIndex: 'nodeType',
    width: 120,
    customRender: ({ text }) => (text === 'milestone' ? '里程碑节点' : '计划'),
  },
  // 计划属性
  {
    title: '计划属性',
    dataIndex: 'planActiveList',
    width: 200,
    customRender: ({ record }) => h(ActiveTagList, { data: record.planActiveList }),
  },
  // 计划状态
  {
    title: '计划状态',
    dataIndex: 'dataStatus',
    width: 100,
    customRender: ({ record }) => h(DataStatusTag, { statusData: record.dataStatus }),
  },
  // 完成情况
  {
    title: '完成情况',
    dataIndex: 'circumstance',
    width: 120,
    customRender({ record }) {
      return h(
        Tag,
        { color: SituationColorEnum[record.circumstance] },
        record.circumstanceName || '--',
      );
    },
  },
  // 责任部门
  {
    title: '责任部门',
    dataIndex: 'rspSubDeptName',
    width: 120,
  },
  // 责任人
  {
    title: '责任人',
    dataIndex: 'rspUserName',
    width: 120,
  },
  // 计划开始日期
  {
    title: '计划开始日期',
    dataIndex: 'beginTime',
    width: 120,
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD') : '';
    },
  },
  // 计划结束日期
  {
    title: '计划结束日期',
    dataIndex: 'endTime',
    width: 120,
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD') : '';
    },
  },
  // 实际开始日期
  {
    title: '实际开始日期',
    dataIndex: 'actualBeginTime',
    width: 120,
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD') : '';
    },
  },
  // 实际结束日期
  {
    title: '实际结束日期',
    dataIndex: 'actualEndTime',
    width: 120,
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD') : '';
    },
  },
];

const options = {
  // 是否显示工具栏默认按钮
  showToolButton: false,
  // 是否显示工具栏上的搜索
  showSmallSearch: false,
  // 是否显示表格设置工具
  showTableSetting: false,
  isSpacing: false,
  columns,
};

defineProps({
  dataSource: {
    type: Array,
    default: () => [],
  },
});
</script>

<template>
  <div style="height: 300px;overflow: hidden;">
    <OrionTable
      :options="options"
      :dataSource="dataSource"
    />
  </div>
</template>

<style scoped lang="less">

</style>
