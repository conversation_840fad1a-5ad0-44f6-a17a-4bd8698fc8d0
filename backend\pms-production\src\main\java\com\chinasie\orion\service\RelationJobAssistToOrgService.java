package com.chinasie.orion.service;





import java.lang.String;
import java.util.List;
import java.util.Map;


import com.chinasie.orion.domain.dto.RelationJobAssistToOrgDTO;
import com.chinasie.orion.domain.entity.RelationJobAssistToOrg;
import com.chinasie.orion.domain.vo.RelationJobAssistToOrgVO;
import com.chinasie.orion.sdk.metadata.page.Page;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * RelationJobAssistToOrg 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-20 19:05:01
 */
public interface RelationJobAssistToOrgService  extends  OrionBaseService<RelationJobAssistToOrg>  {


        /**
         *  详情
         *
         * * @param id
         */
    RelationJobAssistToOrgVO detail(String id, String pageCode)throws Exception;

        /**
         *  新增
         *
         * * @param relationJobAssistToOrgDTO
         */
        String create(RelationJobAssistToOrgDTO relationJobAssistToOrgDTO)throws Exception;

        /**
         *  编辑
         *
         * * @param relationJobAssistToOrgDTO
         */
        Boolean edit(RelationJobAssistToOrgDTO relationJobAssistToOrgDTO)throws Exception;


        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;


        /**
         *  分页
         *
         * * @param pageRequest
         * 
         */
        Page<RelationJobAssistToOrgVO> pages( Page<RelationJobAssistToOrgDTO> pageRequest)throws Exception;


        /**
            *  设置各种名称
            *
            * * @param searchConditions
            * * @param response
            */
        void  setEveryName(List<RelationJobAssistToOrgVO> vos)throws Exception;

    /**
     *  获取 组织ID对应的协助数据
     * @param idList
     * @return
     */
    List<RelationJobAssistToOrg> getListByRepairOrgIds(List<String> idList);
}
