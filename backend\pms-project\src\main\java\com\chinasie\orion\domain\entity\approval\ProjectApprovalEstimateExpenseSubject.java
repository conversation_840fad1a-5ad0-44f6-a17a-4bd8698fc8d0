package com.chinasie.orion.domain.entity.approval;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.Boolean;
import java.lang.String;

/**
 * ProjectApprovalEstimateExpenseSubject Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-06 10:05:25
 */
@TableName(value = "pms_project_approval_estimate_expense_subject")
@ApiModel(value = "ProjectApprovalEstimateExpenseSubjectEntity对象", description = "概算科目")
@Data
public class ProjectApprovalEstimateExpenseSubject extends ObjectEntity implements Serializable{

    /**
     * 科目名称
     */
    @ApiModelProperty(value = "科目名称")
    @TableField(value = "name")
    private String name;

    /**
     * 项目立项id
     */
    @ApiModelProperty(value = "项目立项id")
    @TableField(value = "project_approval_id")
    private String projectApprovalId;

    /**
     * 概算金额
     */
    @ApiModelProperty(value = "概算金额")
    @TableField(value = "amount", updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal amount;

    /**
     * 父级
     */
    @ApiModelProperty(value = "父级")
    @TableField(value = "parent_id")
    private String parentId;

//    /**
//     * 是否必填
//     */
//    @ApiModelProperty(value = "是否必填")
//    @TableField(value = "required")
//    private Boolean required;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    @TableField(value = "number")
    private String number;

    /**
     * 公式
     */
    @ApiModelProperty(value = "公式")
    @TableField(value = "formula")
    private String formula;

}
