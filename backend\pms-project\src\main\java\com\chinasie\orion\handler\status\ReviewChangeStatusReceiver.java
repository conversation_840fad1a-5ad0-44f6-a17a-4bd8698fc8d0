package com.chinasie.orion.handler.status;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.chinasie.orion.amqp.entity.ChangeStatusMessageDTO;
import com.chinasie.orion.amqp.handler.AbstractChangeStatusReceiver;


import com.chinasie.orion.constant.review.OpinionTypeEnum;
import com.chinasie.orion.constant.review.ReviewStatusEnum;
import com.chinasie.orion.domain.dto.QuestionManagementDTO;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.entity.QuestionManagement;
import com.chinasie.orion.domain.entity.review.Review;
import com.chinasie.orion.domain.entity.review.ReviewOpinion;
import com.chinasie.orion.domain.vo.QuestionManagementVO;
import com.chinasie.orion.manager.WfInstanceQuestionManage;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.sdk.domain.vo.business.ClassVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.util.IdUtils;
import com.chinasie.orion.service.ProjectService;
import com.chinasie.orion.service.impl.QuestionManagementServiceImpl;
import com.chinasie.orion.service.review.ReviewLibraryService;
import com.chinasie.orion.service.review.ReviewOpinionService;
import com.chinasie.orion.service.review.ReviewService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 项目评审状态变更
 */
@Component
@Slf4j
public class ReviewChangeStatusReceiver extends AbstractChangeStatusReceiver {


    private static final String CURRENT_CLASS = "Review";

    @Resource
    private ReviewService reviewService;

    @Resource
    private ClassRedisHelper classRedisHelper;
    @Resource
    private ReviewOpinionService reviewOpinionService;
    @Autowired
    private QuestionManagementServiceImpl questionManagementService;
    @Autowired
    private ProjectService projectService;
    @Resource
    private UserRedisHelper userRedisHelper;
    @Resource
    private WfInstanceQuestionManage wfInstanceQuestionManage;


    @Override
    protected void process(ChangeStatusMessageDTO msg, Channel channel, Message message) {
        log.info("项目评审状态更改消息消费：{}", msg);
        if (ObjectUtil.isNotEmpty(msg)) {
            ClassVO classVO = classRedisHelper.classInfo(IdUtils.getCode(msg.getBusinessId()));
            if (Objects.nonNull(classVO)) {
                if (CURRENT_CLASS.equals(classVO.getClassName())) {
                    msg.setClassName(classVO.getClassName());
                    ThreadUtil.execAsync(() -> {
                        try {
                            consumerCreateMessage(msg);
                        } catch (Exception e) {
                            processError(msg, channel, message, e);
                        }
                    });
                }
            }
        }
    }

    @Override
    protected void processError(ChangeStatusMessageDTO msg, Channel channel, Message message, Exception ex) {
        log.error("项目评审状态更改消息消费异常，【{}】,message，【{}】,", JSONUtil.toJsonStr(msg), message, ex);
    }


    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = CURRENT_CLASS, durable = "true", autoDelete = "false"),
            exchange = @Exchange(value = "${orion.amqp.change-status-v2.exchange}", type = ExchangeTypes.DIRECT),
            key = CURRENT_CLASS
    ))
    @Override
    public void receiver(ChangeStatusMessageDTO msg, Channel channel, Message message) throws IOException {
        super.receiver(msg, channel, message);
    }

    /**
     * 消费消息
     *
     * @param message 消息
     */
    private void consumerCreateMessage(ChangeStatusMessageDTO message) throws Exception {
        boolean result = true;
        Review review = reviewService.getById(message.getBusinessId());
        CurrentUserHelper.setUserId(message.getPusherId());
        CurrentUserHelper.setAttributes(review.getPlatformId(),review.getOrgId());
        if (ReviewStatusEnum.ADD_LOTUS.getCode().equals(message.getStatus())){
            createLotus(review);
        } else if (ReviewStatusEnum.ADD_APPRAISAL.getCode().equals(message.getStatus())){
            createAppraisal(review);
        } else {
            LambdaUpdateWrapper<Review> wrapper=new LambdaUpdateWrapper<>(Review.class);
            wrapper.eq(Review::getId,message.getBusinessId());
            wrapper.set(Review::getStatus,message.getStatus());
            result = reviewService.update(wrapper);

        }

        log.info("项目评审状态更改消息消费成功-参数:{}-结果:{}", JSONUtil.toJsonStr(message), result);
    }

    private void createLotus(Review review) throws Exception {
        if (ObjectUtil.isNull(review)){
            return;
        }
        String reviewId = review.getId();
        LambdaQueryWrapperX<ReviewOpinion> wrapperX = new LambdaQueryWrapperX<>(ReviewOpinion.class);
        wrapperX.eq(ReviewOpinion::getMainTableId,reviewId)
                .eq(ReviewOpinion::getType, OpinionTypeEnum.LOTUS.getValue())
                .isNull(ReviewOpinion::getQuestionId);
        List<ReviewOpinion> reviewOpinions = reviewOpinionService.list(wrapperX);
        log.info("lotus data log {}", JSONUtil.toJsonStr(reviewOpinions));
        if (CollectionUtil.isEmpty(reviewOpinions)){
            return;
        }
        List<QuestionManagementDTO> questionManagementDTOList = new ArrayList<>();
        for (ReviewOpinion reviewOpinion : reviewOpinions) {
            QuestionManagementDTO dto = new QuestionManagementDTO();
            dto.setOrgId(review.getOrgId());
            dto.setExhibitor(reviewOpinion.getCreatorId());
            dto.setReviewPoints(reviewOpinion.getReviewEssentialsId());
            dto.setContent(reviewOpinion.getDescription());
            // todo 意见类别
            dto.setName(String.format("%s - %s",review.getName(),reviewOpinion.getEssentials()));
            dto.setReviewPoints(reviewOpinion.getReviewEssentialsId());
            dto.setProposedTime(reviewOpinion.getCreateTime());
            questionManagementDTOList.add(dto);
        }
        List<QuestionManagementVO> batch = questionManagementService.createBatch(questionManagementDTOList);
        log.info("Batch saved question {}", JSONUtil.toJsonStr(batch));
        Map<String, String> map = batch.stream().collect(Collectors.toMap(QuestionManagementVO::getQuestionSource, QuestionManagementVO::getId));
        reviewOpinions.forEach(reviewOpinion -> {
            String id = reviewOpinion.getId();
            String s = map.get(id);
            reviewOpinion.setQuestionId(s);
        });
        wfInstanceQuestionManage.process(batch, review.getOrgId());
        reviewOpinionService.updateBatchById(reviewOpinions);
    }

    private void createAppraisal(Review review) throws Exception {
        if (ObjectUtil.isNull(review)){
            return;
        }
        String reviewId = review.getId();
        LambdaQueryWrapperX<ReviewOpinion> wrapperX = new LambdaQueryWrapperX<>(ReviewOpinion.class);
        wrapperX.eq(ReviewOpinion::getMainTableId,reviewId)
                .eq(ReviewOpinion::getType, OpinionTypeEnum.APPRAISAL.getValue())
                .isNull(ReviewOpinion::getQuestionId);
        List<ReviewOpinion> reviewOpinions = reviewOpinionService.list(wrapperX);
        log.info("appraisal data log {}", JSONUtil.toJsonStr(reviewOpinions));
        if (CollectionUtil.isEmpty(reviewOpinions)){
            return;
        }
        List<QuestionManagementDTO> questionManagementDTOList = new ArrayList<>();
        String projectId = review.getProjectId();
        Project project = projectService.getById(projectId);

        List<String> userIds = reviewOpinions.stream().map(ReviewOpinion::getPresentedUser).distinct().collect(Collectors.toList());
        Map<String, UserVO> userMap = userRedisHelper.getUserMapByUserIds(userIds);

        for (ReviewOpinion reviewOpinion : reviewOpinions) {
            UserVO userVO = userMap.get(reviewOpinion.getPresentedUser());
            QuestionManagementDTO dto = new QuestionManagementDTO();
            dto.setOrgId(review.getOrgId());
            dto.setExhibitor(reviewOpinion.getPresentedUser());
            dto.setReviewPoints(reviewOpinion.getEssentials());
            dto.setContent(reviewOpinion.getOpinion());
            // todo 意见类别
            dto.setPrincipalId(project.getPm());
            if (ObjectUtil.isNotNull(userVO)){
                dto.setName(String.format("%s %s的评审意见",review.getName(),userVO.getName()));
            } else {
                dto.setName(String.format("%s 的评审意见",review.getName()));
            }
            dto.setProposedTime(reviewOpinion.getCreateTime());
            questionManagementDTOList.add(dto);
        }
        List<QuestionManagementVO> batch = questionManagementService.createBatch(questionManagementDTOList);
        log.info("Batch saved question {}", JSONUtil.toJsonStr(batch));
        Map<String, String> map = batch.stream().collect(Collectors.toMap(QuestionManagementVO::getQuestionSource, QuestionManagementVO::getId));
        reviewOpinions.forEach(reviewOpinion -> {
            String id = reviewOpinion.getId();
            String s = map.get(id);
            reviewOpinion.setQuestionId(s);
        });
        wfInstanceQuestionManage.process(batch, review.getOrgId());
        reviewOpinionService.updateBatchById(reviewOpinions);
    }
}
