package com.chinasie.orion.exp.marketManagement;

import com.chinasie.orion.permission.core.core.type.ValueExpType;
import com.chinasie.orion.permission.core.exp.IExp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * Keafmd
 *
 * @ClassName: UncommittedDepartmentExp
 * @Description: 无承担部门的机构的值的时候。所有成员都能够查看
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/8/12 11:49
 * @Blog: https://keafmd.blog.csdn.net/
 */
@Component
@Slf4j
public class UncommittedDepartmentExp implements IExp {
    @Override
    public ValueExpType group() {
        return ValueExpType.CUSTOM;
    }

    @Override
    public String expName() {
        return "无承担部门";
    }

    @Override
    public List<String> exp(String s) {
        return List.of("");
    }

    @Override
    public Boolean apply() {
        return true;
    }
}
