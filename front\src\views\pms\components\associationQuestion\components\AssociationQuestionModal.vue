<template>
  <div class="table-content">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      @selectionChange="selectionChange"
    />
  </div>
</template>

<script lang="ts" setup>
import { h, ref, Ref } from 'vue';
import {
  BasicButton,
  Layout,
  OrionTable, DataStatusTag,
} from 'lyra-component-vue3';
import { stampDate } from '/@/utils/dateUtil';
import Api from '/@/api';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

const props = withDefaults(defineProps<{
    questionId:string
}>(), {
  questionId: '',
});
const selectRowKeys:Ref<string[]> = ref([]);
function selectionChange(data) {
  selectRowKeys.value = data.keys;
}
const tableRef = ref();
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: {},
  showSmallSearch: true,
  smallSearchField: ['name'],
  api: (params) => {
    if (typeof params.searchConditions === 'undefined') {
      params.searchConditions = [
        [
          {
            field: 'status',
            fieldType: 'Integer',
            values: ['130'],
            queryType: 'eq',
          },
        ],
        [
          {
            field: 'status',
            fieldType: 'Integer',
            values: ['160'],
            queryType: 'eq',
          },
        ],
      ];
    } else {
      params.searchConditions = params.searchConditions.concat([
        [
          {
            field: 'status',
            fieldType: 'Integer',
            values: ['130'],
            queryType: 'eq',
          },
        ],
        [
          {
            field: 'status',
            fieldType: 'Integer',
            values: ['160'],
            queryType: 'eq',
          },
        ],
      ]);
    }
    params.query = {
      riskId: props.questionId,
    };
    return new Api('/pms/question-management/getPage').fetch(params, '', 'POST');
  },
  columns: [
    {
      title: '编号',
      dataIndex: 'number',
      align: 'left',
      key: 'number',

      width: '120px',
      // sorter: true,
      ellipsis: true,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',

      align: 'left',
      // slots: { customRender: 'name' },
      // sorter: true,
      ellipsis: true,
    },
    {
      title: '问题内容',
      dataIndex: 'content',
      key: 'content',

      align: 'left',
      // sorter: true,
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'dataStatus',
      key: 'dataStatus',
      width: '80px',
      align: 'left',
      // sorter: true,
      ellipsis: true,
      customRender: ({ record, text }) => h(DataStatusTag, { statusData: record.dataStatus }),
    },
    {
      title: '严重程度',
      dataIndex: 'seriousLevelName',
      key: 'seriousLevelName',

      align: 'left',
      // sorter: true,
      ellipsis: true,
    },
    {
      title: '问题来源',
      dataIndex: 'questionSourceName',
      key: 'questionSourceName',

      align: 'left',
      // sorter: true,
      ellipsis: true,
    },
    {
      title: '问题类型',
      dataIndex: 'questionTypeName',
      key: 'questionTypeName',

      align: 'left',
      // sorter: true,
      ellipsis: true,
    },

    {
      title: '负责人',
      dataIndex: 'principalName',
      key: 'principalName',
      align: 'left',
      // sorter: true,
      ellipsis: true,
      slots: { customRender: 'principalName' },
    },
    {
      title: '期望完成日期',
      dataIndex: 'questionTypeName',
      key: 'questionTypeName',
      align: 'left',
      // sorter: true,
      ellipsis: true,
    },

    {
      title: '提出人',
      dataIndex: 'exhibitorName',
      key: 'exhibitorName',
      width: '70px',
      align: 'left',
      // sorter: true,
      ellipsis: true,
    },
    {
      title: '提出日期',
      dataIndex: 'proposedTime',
      key: 'proposedTime',
      customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : ''),
      width: '100px',
      align: 'left',
      // sorter: true,
      ellipsis: true,
      // slots: { customRender: 'proposedTime' },
    },
  ],
  //  beforeFetch,
});
async function saveData() {
  const selectRows = tableRef.value.getSelectRows();
  if (selectRows.length === 0) {
    message.warning('请选择问题');
    return Promise.reject('');
  }
  await new Api('/pas').fetch(
    selectRows.map((item) => item.id),
    `riskRelationQuestion/relationQuestion/${props.questionId}`,
    'POST',
  );

  message.success('关联问题成功');
}
function getSelectData() {
  return tableRef.value.getSelectRows();
}
defineExpose({
  saveData,
});
</script>

<style lang="less" scoped>
.table-content{
  height: 100%;
  overflow: hidden;
}
</style>
