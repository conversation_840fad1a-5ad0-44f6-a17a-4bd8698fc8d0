<template>
  <Layout3
    :defaultActionId="tabsIndex"
    :projectData="projectData.headerData"
    :menuData="menuData"
    :type="2"
    :onMenuChange="contentTabsChange"
  >
    <template #header-right>
      <BasicTableAction
        v-if="Number(projectInfo?.dataStatus?.statusValue) === 120"
        :actions="rightBtnActions"
        type="button"
      />
    </template>
    <template #footer>
      <WorkflowAction
        v-if="projectInfo?.id"
        ref="processRef"
        :workflow-props="workflowProps"
      />
    </template>
    <MaterialServices
      v-if="tabsIndex==='WZFWXX_01'"
      :detailsData="projectData.detailsData"
    />
    <WorkflowView
      v-if="tabsIndex==='process'"
      ref="processViewRef"
      :workflow-props="workflowProps"
    />
    <ServicePlanDrawer
      @upTableDate="upTableDate"
      @register="modalServicePlanRegister"
    />
  </Layout3>
</template>

<script setup lang="ts">
import {
  computed, ref, onMounted, Ref, ComputedRef, watchEffect,
} from 'vue';
import {
  Layout3, ITableActionItem, useDrawer, BasicTableAction,
} from 'lyra-component-vue3';
import { WorkflowAction, WorkflowProps, WorkflowView } from 'lyra-workflow-component-vue3';
import Api from '/@/api';
import { useRoute } from 'vue-router';
import { getMaterialServicesDetails } from '/@/views/pms/api';
import MaterialServices from './components/materialServices.vue';
import ServicePlanDrawer from '../components/servicePlanDrawer.vue';
import { setTitleByRootTabsKey } from '/@/utils';

const [modalServicePlanRegister, { openDrawer: openServicePlanDrawer }] = useDrawer();
const route = useRoute();
const tabsIndex = ref('WZFWXX_01'); // 选中的项index
const menuData = ref([]);// 右边菜单选项
const projectData = ref({
  headerData: {},
  detailsData: {},
});
const projectInfo = ref({} as any);
const processRef: Ref = ref();
const processViewRef:Ref = ref();
// const projectData = computed(() => ({ name: '显示全部物资信息/服务描述', projectCode: `计划编号:${route.query.number}` }));
function getDetailsData() {
  getMaterialServicesDetails(route.query?.id).then((res) => {
    projectInfo.value = res;
    processRef.value?.setProps({
      businessData: res,
    });
    projectData.value.headerData = {
      name: res.description,
      projectCode: `计划编号:${res.number}`,
      ownerName: res.ownerName,
      dataStatus: res.dataStatus,
    };
    setTitleByRootTabsKey(route?.query?.rootTabsKey, res.description);
    projectData.value.detailsData = res;
  });
}
onMounted(() => {
  menuData.value.push({
    name: '物资服务信息',
    id: 'WZFWXX_01',
  }, {
    name: '流程',
    id: 'process',
  });
  getMaterialServicesDetails(route.query?.id).then((res) => {
    projectInfo.value = res;
    processRef.value?.setProps({
      businessData: res,
    });
    projectData.value.headerData = {
      name: res.description,
      projectCode: `计划编号:${res.number ? res.number : '--'}`,
      ownerName: res.ownerName,
      dataStatus: res.dataStatus,
    };
    projectData.value.detailsData = res;
  });
});
const workflowProps:ComputedRef<WorkflowProps> = computed(() => ({
  Api,
  businessData: {
    ...projectData.value.detailsData,
    name: projectData.value.detailsData.description,
  },
  afterEvent: (type, props) => {
    processViewRef.value?.init();
  },
}));

const rightBtnActions:Ref<ITableActionItem[]> = computed(() => [
  {
    text: '编辑',
    // isShow: computed(() => Number(projectInfo.value?.dataStatus?.statusValue) === 120),
    onClick() {
      openServicePlanDrawer(true, {
        type: 'edit',
        itemData: projectData.value.detailsData,
        projectId: route.query?.projectId,
      });
    },
  },
  (processRef.value?.isAdd ? {
    text: '添加流程',
    icon: 'sie-icon-tianjiaxinzeng',
    // isShow: computed(() => Number(projectInfo.value?.dataStatus?.statusValue) === 120),
    onClick() {
      processRef.value?.onAddTemplate({
        messageUrl: route.fullPath,
      });
    },
  } : undefined),

].filter((item) => item));
function upTableDate() {
  getDetailsData();
}
const contentTabsChange = (index) => {
  tabsIndex.value = index.id;
};
</script>
<style scoped lang="less">
</style>
