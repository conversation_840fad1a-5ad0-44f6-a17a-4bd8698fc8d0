package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;
import java.lang.Integer;

/**
 * ProjectCondition Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-05 16:43:24
 */
@TableName(value = "pmsx_project_condition")
@ApiModel(value = "ProjectConditionEntity对象", description = "项目状态")
@Data

public class ProjectCondition extends  ObjectEntity  implements Serializable{

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    @TableField(value = "project_name")
    private String projectName;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "project_number")
    private String projectNumber;

    /**
     * 承接中心
     */
    @ApiModelProperty(value = "承接中心")
    @TableField(value = "undertaking_center")
    private String undertakingCenter;

    /**
     * 客户（电厂）
     */
    @ApiModelProperty(value = "客户（电厂）")
    @TableField(value = "cust_company")
    private String custCompany;

    /**
     * 详细描述
     */
    @ApiModelProperty(value = "详细描述")
    @TableField(value = "detailed_description")
    private String detailedDescription;

    /**
     * CCM设备/隐患消除(个)
     */
    @ApiModelProperty(value = "CCM设备/隐患消除(个)")
    @TableField(value = "CC_mnumber")
    private Integer CCMnumber;

    /**
     * 大修工期节约(H)
     */
    @ApiModelProperty(value = "大修工期节约(H)")
    @TableField(value = "save_time")
    private Integer saveTime;

    /**
     * 集体剂量降低(man.mSv)
     */
    @ApiModelProperty(value = "集体剂量降低(man.mSv)")
    @TableField(value = "m_sv_reduce")
    private Integer mSvReduce;

    /**
     * 项目成本(万)
     */
    @ApiModelProperty(value = "项目成本(万)")
    @TableField(value = "project_cost")
    private BigDecimal projectCost;

    /**
     * 项目营收
     */
    @ApiModelProperty(value = "项目营收")
    @TableField(value = "project_revenue")
    private BigDecimal projectRevenue;

}
