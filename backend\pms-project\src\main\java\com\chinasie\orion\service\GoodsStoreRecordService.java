package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.GoodsStoreRecordDTO;
import com.chinasie.orion.domain.vo.GoodsStoreRecordVO;
import com.chinasie.orion.page.PageRequest;
import com.chinasie.orion.page.PageResult;

import java.lang.String;
import java.util.List;

/**
 * <p>
 * GoodsStoreRecord 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26 10:06:03
 */
public interface GoodsStoreRecordService  {
        /**
         *  详情
         *
         * * @param id
         */
        GoodsStoreRecordVO detail(String id)  throws Exception;

        /**
         *  新增
         *
         * * @param goodsStoreRecordDTO
         */
        GoodsStoreRecordVO create(GoodsStoreRecordDTO goodsStoreRecordDTO)  throws Exception;

        /**
         *  编辑
         *
         * * @param goodsStoreRecordDTO
         */
        Boolean edit(GoodsStoreRecordDTO goodsStoreRecordDTO) throws Exception;


        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;


        PageResult<GoodsStoreRecordVO> getGoodsStoreRecordPage(PageRequest<GoodsStoreRecordVO> pageRequest) throws Exception;
}
