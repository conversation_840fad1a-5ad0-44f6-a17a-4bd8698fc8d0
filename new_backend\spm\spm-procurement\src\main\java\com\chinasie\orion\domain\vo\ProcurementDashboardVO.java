package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ProcurementDashboard VO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@ApiModel(value = "ProcurementDashboardVO对象", description = "采购供应商指标看板")
@Data
public class ProcurementDashboardVO extends ObjectVO implements Serializable {

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;


    /**
     * 当前值
     */
    @ApiModelProperty(value = "当前值")
    private String present;

    /**
     * 同比
     */
    @ApiModelProperty(value = "同比")
    private String yoy;

    /**
     * 环比
     */
    @ApiModelProperty(value = "环比")
    private String qoq;
}
