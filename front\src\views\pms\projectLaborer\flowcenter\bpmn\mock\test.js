export const testXML = `<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="Process_1" isExecutable="false">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_09dyrc9</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Activity_0igip2e" name="第一名">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="selfName">少年的你</camunda:inputParameter>
          <camunda:inputParameter name="nodeDesc">
            <camunda:map>
              <camunda:entry key="class">一年一班</camunda:entry>
              <camunda:entry key="age">8</camunda:entry>
            </camunda:map>
          </camunda:inputParameter>
          <camunda:inputParameter name="interestFood">
            <camunda:list>
              <camunda:value>苹果</camunda:value>
              <camunda:value>香蕉</camunda:value>
              <camunda:value>西瓜</camunda:value>
            </camunda:list>
          </camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_09dyrc9</bpmn:incoming>
      <bpmn:outgoing>Flow_03cqlix</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="Event_1lh7sje">
      <bpmn:incoming>Flow_03cqlix</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_09dyrc9" sourceRef="StartEvent_1" targetRef="Activity_0igip2e" />
    <bpmn:sequenceFlow id="Flow_03cqlix" sourceRef="Activity_0igip2e" targetRef="Event_1lh7sje" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="173" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0igip2e_di" bpmnElement="Activity_0igip2e">
        <dc:Bounds x="310" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1lh7sje_di" bpmnElement="Event_1lh7sje">
        <dc:Bounds x="502" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_09dyrc9_di" bpmnElement="Flow_09dyrc9">
        <di:waypoint x="209" y="120" />
        <di:waypoint x="310" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03cqlix_di" bpmnElement="Flow_03cqlix">
        <di:waypoint x="410" y="120" />
        <di:waypoint x="502" y="120" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>`;
