package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.PlanCountDTO;
import com.chinasie.orion.domain.dto.PlanCountParamDto;
import com.chinasie.orion.domain.entity.PlanCount;
import com.chinasie.orion.mybatis.service.OrionBaseService;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/16/19:53
 * @description:
 */
public interface PlanCountService extends OrionBaseService<PlanCount> {

    /**
     * 加
     * @return
     */
    void plusCount(PlanCountDTO planCountDTO) throws Exception;


    /**
     * 减
     * @param planCountDTO
     * @return
     */
    void minusCount(PlanCountDTO planCountDTO) throws Exception;


    void  updateCount(Integer oldStatus, Integer newStatus,String uk,String type) throws Exception;

    void minusCount(Integer status, String planType,String uk) throws Exception;


    void minusCountList(List<PlanCountParamDto>planCountParamDtos) throws Exception;
}
