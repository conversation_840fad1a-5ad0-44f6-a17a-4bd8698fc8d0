package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ContractPayNode Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-24 10:41:37
 */
@TableName(value = "pms_contract_pay_node")
@ApiModel(value = "ContractPayNode对象", description = "合同支付节点信息")
@Data
public class ContractPayNode extends ObjectEntity implements Serializable {

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_number")
    private String contractNumber;

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    @TableField(value = "contract_id")
    private String contractId;

    /**
     * 结算类型
     */
    @ApiModelProperty(value = "结算类型")
    @TableField(value = "settlement_type")
    private String settlementType;

    /**
     * 支付类型
     */
    @ApiModelProperty(value = "支付类型")
    @TableField(value = "pay_type")
    private String payType;

    /**
     * 初始计划支付时间
     */
    @ApiModelProperty(value = "初始计划支付时间")
    @TableField(value = "init_plan_pay_date")
    private Date initPlanPayDate;

    /**
     * 初始计划支付金额
     */
    @ApiModelProperty(value = "初始计划支付金额")
    @TableField(value = "init_plan_pay_amt")
    private BigDecimal initPlanPayAmt;

    /**
     * 支付百分比
     */
    @ApiModelProperty(value = "支付百分比")
    @TableField(value = "pay_percentage")
    private BigDecimal payPercentage;

    /**
     * 支付说明
     */
    @ApiModelProperty(value = "支付说明")
    @TableField(value = "pay_desc")
    private String payDesc;

    /**
     * 支付日期
     */
    @ApiModelProperty(value = "支付日期")
    @TableField(value = "pay_date")
    private Date payDate;

}
