<template>
  <div
    v-loading="spinning"
    class="deliverbleContent"
  >
    <div class="top-wrap">
      <a-row>
        <a-col :span="20">
          <div class="text-base">
            交付物
          </div>
          <div class="text-xs">
            已完成 {{ dataSource.finishCount }} 项，共 {{ dataSource.totalSize }} 项
          </div>
        </a-col>
        <a-col :span="4">
          <a-input-search
            v-if=" isPower('XMX_container_button_17', powerData) "
            v-model:value="searchValue"
            placeholder="请输入名称或编号"
            @search="searchTableData"
          />
        </a-col>
      </a-row>
    </div>
    <BasicTable
      v-if="isShowTable"
      ref="tableRef"
      :show-index-column="false"
      :columns="columns"
      :data-source="dataSource.content"
      :pagination="false"
      :resize-height-offset="40"
      :row-selection="pageType==='page'?{ type: 'checkbox' }:null"
      row-key="id"
    />
    <div class="page-wrap">
      <Pagination
        v-model:current="form.pageNum"
        v-model:pageSize="form.pageSize"
        class="align-right"
        :total="dataSource.totalSize"
        @query="getPage"
      />
    </div>

    <ViewDetails
      v-if="viewDetails.visible"
      :data="viewDetails"
    />
    <Deliver
      v-if="deliver.visible"
      :data="deliver"
    />
    <SearchModal
      v-if="pageType==='page'"
      :onSearchEmit="searchEmit"
      @register="searchRegister"
    />
  </div>
</template>

<script>
import {
  BasicTable, isPower, OrionTable, useDrawer,
} from 'lyra-component-vue3';
import Pagination from '/@/views/pms/projectLaborer/knowledgeEditData/Pagination.vue';
import {
  Col, Input, message, Row,
} from 'ant-design-vue';
import {
  computed, h, inject, nextTick, onMounted, reactive, ref, toRefs,
} from 'vue';
import Api from '/@/api';
import ViewDetails
  from '../planDetails/deliverable/components/ViewDetails.vue';
import Deliver from '../deliverDetails/index.vue';
import router from '/@/router';
import SearchModal from './SearchModal.vue';

export default {
  name: 'Index',
  components: {
    Deliver,
    ViewDetails,
    Pagination,
    BasicTable,
    ARow: Row,
    ACol: Col,
    AInputSearch: Input.Search,
    SearchModal,
  },
  props: {
    formId: {
      type: String,
      default: '',
    },
    pageType: {
      type: String,
      default: 'page',
    },
  },
  emits: ['checkDetails'],
  setup(props, { emit }) {
    const [searchRegister, { openDrawer: openSearchDrawer }] = useDrawer();
    const state = reactive({
      tableRef: ref(),
      deliver: {},
      // btnConfig: {
      //   check: { show: true },
      //   open: { show: true },
      // },
      powerData: [],
      viewDetails: {},
      spinning: false,
      isShowTable: true,
      columns: [
        {
          title: '编号',
          dataIndex: 'number',
          align: 'left',
          minWidth: 200,
        },
        {
          title: '交付物名称',
          dataIndex: 'name',
          align: 'left',
          minWidth: 200,
          customRender({ record, text }) {
            return h(
              'span',
              {
                class: computed(() => (isPower('XMX_container_button_16', state.powerData) ? 'action-btn' : '')).value,
                title: text,
                onClick(e) {
                  if (isPower('XMX_container_button_16', state.powerData)) {
                    router.push({
                      name: 'DeliverDetails',
                      query: {
                        projectId: props.formId,
                        id: record.id,
                      },
                    });
                  }
                  e.stopPropagation();
                },
              },
              text,
            );
          },

        },
        {
          title: '计划交付物时间',
          dataIndex: 'predictDeliverTime',
          width: 150,
        },
        {
          title: '实际交付物时间',
          dataIndex: 'deliveryTime',
          width: 150,
        },
        {
          title: '所属任务',
          dataIndex: 'planName',
          width: 200,
        },
        {
          title: '状态',
          dataIndex: 'statusName',
          width: 100,
        },
        {
          title: '版本',
          dataIndex: 'revId',
          width: 80,
        },
        {
          title: '负责人',
          dataIndex: 'principalName',
          width: 150,
        },
        {
          title: '修改时间',
          dataIndex: 'modifyTime',
          width: 150,
        },
      ],
      searchValue: undefined,
      dataSource: {
        content: [],
        finishCount: 1,
        pageNum: 1,
        pageSize: 10,
        totalPages: 0,
        totalSize: 0,
      },
      form: {
        pageNum: 1,
        pageSize: 10,
        query: {
          projectId: props.formId,
          keyword: '',
        },
        queryCondition: [],
      },
    });
    state.powerData = inject('powerData');
    const state2 = reactive({
      btnConfig: {
        check: { show: computed(() => isPower('XMX_container_button_15', state.powerData)) },
        open: { show: computed(() => isPower('XMX_container_button_16', state.powerData)) },
        search: { show: computed(() => isPower('XMX_container_button_17', state.powerData)) },
      },
    });

    function getPage() {
      state.spinning = true;
      const love = {
        className: 'Plan',
        moduleName: '项目管理-计划管理-项目计划-交付物', // 模块名称
        type: 'GET', // 操作类型
        remark: '获取/搜索了【交付物列表】',
      };
      state.form.queryCondition.push({
        column: 'projectId',
        type: 'eq',
        link: 'and',
        value: props.formId,
      });
      new Api('/pms', love)
        .fetch(state.form, 'deliverable/page', 'POST')
        .then((res) => {
          state.isShowTable = false;
          state.dataSource = res;
          nextTick(() => {
            state.isShowTable = true;
          });
          state.tableRef.clearSelectedRowKeys();
          state.form.queryCondition = [];
        }).finally(() => {
          state.spinning = false;
        });
    }

    function searchTableData() {
      let queryCondition = [];
      if (state.searchValue) {
        queryCondition = [
          {
            column: 'name',
            type: 'like',
            link: 'or',
            value: state.searchValue,
          },
          {
            column: 'number',
            type: 'like',
            link: 'or',
            value: state.searchValue,
          },
        ];
      }
      state.form.query.keyword = state.searchValue;
      state.form.queryCondition = queryCondition;
      state.form.pageNum = 1;
      state.form.pageSize = 10;
      getPage();
    }

    function handleCheck(id) {
      const love = {
        id,
        className: 'Plan',
        moduleName: '项目管理-计划管理-交付物', // 模块名称
        type: 'GET', // 操作类型
        remark: `查看了【${id}】`,
      };
      emit('checkDetails', { id });
    }

    function isSelectCheck() {
      const selectedRowKeys = state.tableRef.getSelectRowKeys();
      if (selectedRowKeys.length === 0) {
        message.warning('请选择数据进行操作');
        return false;
      }
      if (selectedRowKeys.length === 1) {
        return selectedRowKeys[0];
      }
      if (selectedRowKeys.length > 1) {
        message.warning('请选择一条数据进行操作');
        return false;
      }
    }

    function clickType(type) {
      if (type === 'search') {
        openSearchModal(true);
        return;
      }
      const id = isSelectCheck();
      if (id && type === 'check') handleCheck(id); // 查看
      if (id && type === 'open') {
        // if (id) {
        //   const projectId = props.formId;
        //   state.deliver = {
        //     visible: true,
        //     projectId,
        //     id,
        //   };
        // }
        if (id) {
          router.push({
            name: 'DeliverDetails',
            query: {
              projectId: props.formId,
              id,
            },
          });
        }
      }
    }

    function openSearchModal() {
      openSearchDrawer(true);
    }

    onMounted(() => {
      getPage();
    });

    function searchEmit(data) {
      // console.log("----- data -----", data)
      let queryCondition = [];
      if (data.keyword) {
        queryCondition = [
          {
            column: 'name',
            type: 'like',
            link: 'or',
            value: data.keyword,
          },
          {
            column: 'number',
            type: 'like',
            link: 'or',
            value: data.keyword,
          },
        ];
      } else {
        for (const item in data) {
          // console.log("----- item,data[item] -----", item,data[item])
          if (item === 'predictDeliverTime') {
            queryCondition.push({
              column: item,
              type: 'bt',
              link: 'and',
              value: data[item],
            });
          } else if (item === 'deliveryTime') {
            queryCondition.push({
              column: item,
              type: 'bt',
              link: 'and',
              value: data[item],
            });
          } else {
            queryCondition.push({
              column: item,
              type: 'eq',
              link: 'and',
              value: data[item],
            });
          }
        }
      }

      state.form.queryCondition = queryCondition;
      state.form.pageNum = 1;
      state.form.pageSize = 10;
      getPage();
    }
    return {
      ...toRefs(state),
      ...toRefs(state2),
      clickType,
      searchTableData,
      getPage,
      isPower,
      searchRegister,
      searchEmit,
    };
  },
};
</script>

<style lang="less" scoped>
.top-wrap {
  padding: ~`getPrefixVar('content-padding-top')` ~`getPrefixVar('content-padding-left')` 0 ~`getPrefixVar('content-padding-left')`;
}

.page-wrap {
  padding: 0 ~`getPrefixVar('content-padding-left')`
}

</style>
