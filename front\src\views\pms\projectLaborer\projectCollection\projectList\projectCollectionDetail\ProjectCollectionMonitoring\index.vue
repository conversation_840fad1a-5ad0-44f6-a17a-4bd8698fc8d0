<script setup lang="ts">
import { OrionTable, BasicButton } from 'lyra-component-vue3';
import {
  onMounted, ref, Ref, computed, inject,
} from 'vue';
// import { openFormDrawer } from './utils';
import { useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import Api from '/@/api';
import dayjs from 'dayjs';
const router = useRouter();
const tableRef: Ref = ref();
function assignId(node, path) {
  node.originId = node.id;
  node.id = path.join('-'); // 将节点的路径作为demoId

  // 递归处理子节点
  if (node.children && node.children.length > 0) {
    for (let i = 0; i < node.children.length; i++) {
      assignId(node.children[i], path.concat(i + 1)); // 在路径中添加当前节点的索引
    }
  }
  return node; // 返回当前节点
}
// 比较两个日期字符串，返回更早的一个
function getMinDate(date1, date2) {
  const date1Obj = new Date(date1);
  const date2Obj = new Date(date2);
  return date1Obj < date2Obj ? date1 : date2;
}

// 比较两个日期字符串，返回更晚的一个
function getMaxDate(date1, date2) {
  const date1Obj = new Date(date1);
  const date2Obj = new Date(date2);
  return date1Obj > date2Obj ? date1 : date2;
}

// 递归地更新每个节点的projectStartTime和projectEndTime
function updateProjectTimes(node) {
  // 如果项目开始时间和结束时间都存在，不需要更新
  if (node.projectStartTime && node.projectEndTime) {
    return {
      ...node,
      children: node.children ? node.children.map(updateProjectTimes) : null,
    };
  }

  // 初始化最小开始时间和最大结束时间
  let minStartTime = node.projectStartTime || null;
  let maxEndTime = node.projectEndTime || null;

  const updatedChildren = node.children?.map(updateProjectTimes);
  // 如果有子节点，递归更新它们，并更新当前节点的时间
  if (node.children && node.children.length > 0) {
    updatedChildren.forEach((child) => {
      if (child.projectStartTime) {
        minStartTime = minStartTime ? getMinDate(minStartTime, child.projectStartTime) : child.projectStartTime;
      }
      if (child.projectEndTime) {
        maxEndTime = maxEndTime ? getMaxDate(maxEndTime, child.projectEndTime) : child.projectEndTime;
      }
    });
  }

  // 更新当前节点的时间
  return {
    ...node,
    projectStartTime: minStartTime,
    projectEndTime: maxEndTime,
    children: node.children ? updatedChildren : null,
  };
}
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const projectCollectionId = inject('projectId');
const keyword: Ref = ref('');
const tableOptions = {
  showToolButton: false,
  showIndexColumn: false,
  pagination: false,
  smallSearchField: ['name'],
  api: (params: Record<string, any>) => {
    params.name = keyword.value;
    return new Api(`/pms/projectCollection/treeStatistics/${projectCollectionId}`).fetch(params, '', 'POST').then((res) => {
      if (res && res.length > 0) {
        for (let i = 0; i < res.length; i++) {
          assignId(res[i], [i + 1]); // 初始路径为节点的索引
          res[i] = updateProjectTimes(res[i]); // 计算时间
        }
        return res;
      }
      return [];
    });
  },
  columns: [
    {
      title: '序号',
      dataIndex: 'id',
    },
    {
      title: '项目组合/子项目组合名称',
      dataIndex: 'name',
    },
    {
      title: '项目组合/子项目组合负责人',
      dataIndex: 'resPersonName',
    },
    {
      title: '状态',
      dataIndex: 'status',
      slots: { customRender: 'status' },
    },
    {
      title: '项目开始时间',
      dataIndex: 'projectStartTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '项目结束时间',
      dataIndex: 'projectEndTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '实际进度',
      dataIndex: 'schedule',
      customRender({ record }) {
        return record.schedule ? `${record.schedule}%` : '0%';
      },
    },
    {
      title: '未完成/总里程碑',
      customRender({ record }) {
        return `${record.noFinishedCount}/${record.totalCount}`;
      },
    },
    {
      title: '未关闭/总问题',
      customRender({ record }) {
        return `${record.questionNOFinishedCount}/${record.questionTotalCount}`;
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 120,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '移除',
      modal(record) {
        return new Api('/pms/projectCollection/remove/toProject').fetch({
          id: record?.originId,
          parentId: record.parentId,
        }, '', 'GET').then(() => {
          message.success('移除成功');
          updateTable();
        }).catch((err) => {

        });
      },
    },
  ],
};
function smallSearchChange(val) {
  keyword.value = val;
}
onMounted(() => {

});

function deleteApi(params) {
  return new Promise((resolve, reject) => {
    new Api('/pms/projectCollection/remove/toProject').fetch(params, '', 'GET').then(() => {
      resolve('');
      updateTable();
    }).catch((err) => {
      reject(err);
    });
  });
}
async function updateTable() {
  await tableRef.value?.reload();
}
function convertToFormat(recordIndexes) {
  if (!recordIndexes) {
    return '';
  }
  if (recordIndexes && recordIndexes.length === 0) {
    return '';
  }

  const incrementedIndexes = recordIndexes.map((item, index) => {
    if (index === 0) {
      return parseInt(item) + 1;
    }
    return parseInt(item) + 1;
  });
  const formattedIndexes = incrementedIndexes.join('-');
  return formattedIndexes;
}
</script>

<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
    @smallSearchChange="smallSearchChange"
  />
</template>

<style scoped lang="less">

</style>
