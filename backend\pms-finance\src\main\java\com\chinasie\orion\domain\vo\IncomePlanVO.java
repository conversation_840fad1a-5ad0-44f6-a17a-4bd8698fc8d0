package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * IncomePlan VO对象
 *
 * <AUTHOR>
 * @since 2024-09-29 19:14:00
 */
@ApiModel(value = "IncomePlanVO对象", description = "收入计划填报")
@Data
public class IncomePlanVO extends  ObjectVO   implements Serializable{

            /**
         * 工作主题
         */
        @ApiModelProperty(value = "工作主题")
        private String workTopics;

        @ApiModelProperty(value = "工作主题名称")
        private String workTopicsName;


        /**
         * 下发人员
         */
        @ApiModelProperty(value = "下发人员")
        private String issuePerson;

        @ApiModelProperty(value = "下发人员名称")
        private String issuePersonName;


        /**
         * 下发时间
         */
        @ApiModelProperty(value = "下发时间")
        private Date issueTime;


        /**
         * 锁定状态
         */
        @ApiModelProperty(value = "锁定状态")
        private String lockStatus;

        @ApiModelProperty(value = "锁定状态名称")
        private String lockStatusName;


        /**
         * 本月收入计划笔数
         */
        @ApiModelProperty(value = "本月收入计划笔数")
        private Integer incomePlanCount = 0;


        /**
         * 本月收入计划金额
         */
        @ApiModelProperty(value = "本月收入计划金额")
        private BigDecimal incomePlanAmt = BigDecimal.ZERO;;


        /**
         * 已完成计划数量
         */
        @ApiModelProperty(value = "已完成计划数量")
        private Integer completeCount = 0;


        /**
         * 已完成计划金额
         */
        @ApiModelProperty(value = "已完成计划金额")
        private BigDecimal completeAmt = BigDecimal.ZERO;


        /**
         * 执行中笔数
         */
        @ApiModelProperty(value = "执行中笔数")
        private Integer executionCount = 0;


        /**
         * 执行中金额
         */
        @ApiModelProperty(value = "执行中金额")
        private BigDecimal executionAmt = BigDecimal.ZERO;


        /**
         * 未开始笔数
         */
        @ApiModelProperty(value = "未开始笔数")
        private Integer noStartCount = 0;


        /**
         * 未开始金额
         */
        @ApiModelProperty(value = "未开始金额")
        private BigDecimal noStartAmt = BigDecimal.ZERO;


        /**
         * 未挂接里程碑数量
         */
        @ApiModelProperty(value = "未挂接里程碑数量")
        private Integer noHookMilestoneCount = 0;


        /**
         * 未挂接里程碑金额
         */
        @ApiModelProperty(value = "未挂接里程碑金额")
        private BigDecimal noHookMilestoneAmt = BigDecimal.ZERO;


        /**
         * 编制调整状态
         */
        @ApiModelProperty(value = "编制调整状态")
        private String incomePlanType;

        @ApiModelProperty(value = "是否拥有编辑调整操作权限")
        private String isAuthority;




    

}
