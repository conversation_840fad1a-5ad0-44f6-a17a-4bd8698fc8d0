package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/03/10/17:34
 * @description:
 */
@Data
public class ProductPBSSimpleVO extends ObjectVO {

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("编码")
    private String number;

    /**
     * 产品类型
     */
    @ApiModelProperty(value = "产品类型")
    private String productType;

    /**
     * 图号
     */
    @ApiModelProperty(value = "图号")
    private String drawNo;

    /**
     * 规格
     */
    @ApiModelProperty(value = "规格")
    private String specification;

    /**
     * 缩略图
     */
    @ApiModelProperty(value = "缩略图")
    private String avatar;


    @ApiModelProperty(value = "上一个状态")
    private Integer lastStatus;

    @ApiModelProperty(value = "父级id")
    private String parentId;


    @ApiModelProperty(value = "tcUrl")
    private String tcUrl;

    /**
     * 版本KEY
     */
    @ApiModelProperty(value = "版本KEY")
    private String revKey;
    /**
     * 版本值
     */
    @ApiModelProperty(value = "版本值")
    private String revId;
}
