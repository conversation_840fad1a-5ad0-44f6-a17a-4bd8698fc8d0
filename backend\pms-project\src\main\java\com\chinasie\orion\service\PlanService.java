//package com.chinasie.orion.service;
//
//import com.chinasie.orion.domain.dto.*;
//import com.chinasie.orion.domain.dto.plan.PlanStatusVo;
//import com.chinasie.orion.domain.dto.plan.PlanTreeDto;
//import com.chinasie.orion.domain.entity.Plan;
//import com.chinasie.orion.domain.vo.*;
//import com.chinasie.orion.domain.vo.projectOverview.ProjectPlanManHourVo;
//import com.chinasie.orion.domain.vo.projectOverview.ProjectPlanTypeCountVo;
//import com.chinasie.orion.domain.vo.projectOverview.ProjectViewVo;
//import com.chinasie.orion.mybatis.service.OrionBaseService;
//import com.chinasie.orion.page.PageRequest;
//import com.chinasie.orion.page.PageResult;
//import org.springframework.web.multipart.MultipartFile;
//
//import javax.servlet.http.HttpServletResponse;
//import java.util.List;
//import java.util.Map;
//
///**
// * Created with IntelliJ IDEA.
// *
// * @author: wys
// * @date: 2022/01/06/15:52
// * @description:
// */
//public interface PlanService extends OrionBaseService<Plan> {
//    /**
//     * 新增计划
//     *
//     * @param planDTO
//     * @return
//     */
//    String savePlan(PlanDTO planDTO) throws Exception;
//
//    /**
//     * 修改计划
//     *
//     * @param planDTO
//     * @return
//     * @throws Exception
//     */
//    boolean updatePlan(PlanDTO planDTO) throws Exception;
//
//    /**
//     * 通过数据ID删除数据
//     *
//     * @param uid
//     * @return
//     */
//    boolean delPlanById(String uid) throws Exception;
//
//    /**
//     * 通过项目id 获取项目计划列表
//     *
//     * @param planTreeDto
//     * @return
//     * @throws Exception
//     */
//    List<PlanTreeVo> getTreeList(PlanTreeDto planTreeDto) throws Exception;
//
//    /**
//     * 获取详情数据
//     *
//     * @param uid
//     * @return
//     */
//    PlanDetailVo detailById(String uid) throws Exception;
//
//    /**
//     * 批量删除数据 通过数据ID列表
//     *
//     * @param uidList
//     * @return
//     */
//    Boolean delPlanByIdList(List<String> uidList) throws Exception;
//
//    /**
//     * 获取计划列表
//     *
//     * @param projectId
//     * @return
//     */
//    List<SimpleVo> getListSimPle(String projectId) throws Exception;
//
//    /**
//     * 获取列表
//     *
//     * @param projectId
//     * @param planQueryDTO
//     * @return
//     * @throws Exception
//     */
//    List<PlanDetailVo> getListByIdList(String projectId, PlanQueryDTO planQueryDTO) throws Exception;
//
//
//    /**
//     * 获取计划树列表
//     *
//     * @param planTreeDto
//     * @return
//     */
//    List<PlanTreeVo> getTreeSimpleList(PlanTreeDto planTreeDto) throws Exception;
//
//    /**
//     * 获取项目的计划的所有负责人信息
//     *
//     * @param projectId
//     * @return
//     * @throws Exception
//     */
//    List<SimpleVo> listPlanPrincipalUser(String projectId) throws Exception;
//
//    /**
//     * 获取优先级字典
//     *
//     * @return
//     */
//    List<SimpleVo> getPriorityList();
//
//    boolean addAll() throws Exception;
//
//    /**
//     * 获取不存在的数据列表 在当前Id 列表中
//     *
//     * @param ids
//     * @return
//     * @throws Exception
//     */
//    List<PlanDetailVo> getListNotInIdList(List<String> ids, String name, String planId) throws Exception;
//
//    /**
//     * 新增里程碑
//     *
//     * @param milestoneDto
//     * @return
//     */
//    String saveMilestone(MilestoneDto milestoneDto) throws Exception;
//
//    /**
//     * 修改里程碑
//     *
//     * @param milestoneDto
//     * @return
//     * @throws Exception
//     */
//    boolean updateMilestone(MilestoneDto milestoneDto) throws Exception;
//
//    /**
//     * 获取里程碑列表 通过项目ID
//     *
//     * @param projectId
//     * @return
//     */
//    List<MilestoneVo> getListByProjectId(String projectId) throws Exception;
//
//    /**
//     * 通过计划ID列表获取 ID对应的计划名称map
//     *
//     * @param idList
//     * @return
//     */
//    Map<String, String> getIdToNameMapByIdList(List<String> idList) throws Exception;
//
//    /**
//     * 搜索获取数据列表
//     *
//     * @param keywordDto
//     * @return
//     */
//    PlanSearchDataVo searchList(KeywordDto keywordDto) throws Exception;
//
//    /**
//     * 获取项目下非里程碑数据的计划 状态统计
//     *
//     * @param projectId
//     * @return
//     */
//    PlanCountVo getPlanCount(String projectId) throws Exception;
//
//    /**
//     * 获取项目工时
//     *
//     * @return
//     * @throws Exception
//     */
//    ProjectPlanManHourVo getProjectManHour(String projectId) throws Exception;
//
//
//    /**
//     * 获取计划分组通过 计划类型ID
//     *
//     * @return
//     * @throws Exception
//     */
//    ProjectViewVo<ProjectPlanTypeCountVo> getPlanGroupByType(String projectId) throws Exception;
//
//    /**
//     * 获取 时间范围 统计每天数量
//     *
//     * @param endTimeStr
//     * @param nowDay
//     * @param projectId
//     * @return
//     */
//    Map<String, Integer> getDayCount(String endTimeStr, String nowDay, String projectId) throws Exception;
//
//    /**
//     * 通过ID列表获取 计划详情类表
//     *
//     * @param ids
//     * @return
//     */
//    List<PlanDetailVo> relationPlanList(List<String> ids) throws Exception;
//
//    /**
//     * 通过数据ID 修改状态
//     *
//     * @param id
//     * @return
//     */
//    boolean updateStatusById(PlanStatusVo id) throws Exception;
//
//    /**
//     * 搜索该项目下的零组件
//     *
//     * @param keywordDto
//     * @return
//     * @throws Exception
//     */
//    List<ComponentSimpleVo> searchComponent(KeywordDto keywordDto) throws Exception;
//
//    /**
//     * 关联零组件
//     *
//     * @param relationCommonDTO
//     * @return
//     * @throws Exception
//     */
//    Boolean relationToComponent(RelationCommonDTO relationCommonDTO) throws Exception;
//
//    /**
//     * 批量删除关联零组件
//     *
//     * @param relationCommonDTO
//     * @return
//     * @throws Exception
//     */
//    Boolean removeRelationToComponent(RelationCommonDTO relationCommonDTO) throws Exception;
//
//    /**
//     * 通过任务获取关联零组件列表
//     *
//     * @param planId
//     * @return
//     * @throws Exception
//     */
//    List<ComponentVO> getComponentListByPlan(String planId) throws Exception;
//
//    /**
//     * 通过计划ID获取 相关联的零组件文档类表
//     *
//     * @param planId
//     * @return
//     */
//    List<PlanAndComponentDocumentVo> getComponentDocumentListByPlan(String planId) throws Exception;
//
//    /**
//     * 获取文档对应的文件列表
//     *
//     * @param documentId
//     * @return
//     */
//    List<PlanAndComponentDocumentVo> getComponentDocumentFileListById(String documentId) throws Exception;
//
////    /**
////     *  修改缓存对应的前置计划信息
////     * @param fromIdList
////     * @return
////     */
////    void updateCacheBeforePlan(List<String> fromIdList,String toId) throws Exception;
//
//    /**
//     * 获取里程碑状态列表
//     *
//     * @return
//     */
//    List<StatusEntityVo> getMilestoneList();
//
//    /**
//     * 计划分页
//     *
//     * @param pageRequest
//     * @return
//     */
//    PageResult<PlanDetailVo> pages(PageRequest<PlanDTO> pageRequest) throws Exception;
//
//    /**
//     * 搜索
//     *
//     * @param searchDTO
//     * @return
//     */
//    List<PlanDetailVo> search(SearchDTO searchDTO) throws Exception;
//
//    /**
//     * 变更影响分析
//     *
//     * @param id
//     * @return
//     */
//    List<AnalysisVO> analysis(String id) throws Exception;
//
//    /**
//     * 计划导入（Excel）
//     *
//     * @param excel
//     * @param id
//     * @return
//     */
//    List<ImportExcelErrorNoteVO> importByExcel(MultipartFile excel, String id) throws Exception;
//
//    /**
//     * 个人工作台 -计划分页列表
//     *
//     * @param pageRequest
//     * @return
//     * @throws Exception
//     */
//    PageResult<PlanDetailVo> personPlanPage(PageRequest<PlanDTO> pageRequest) throws Exception;
//
//    /**
//     * 提交审核
//     *
//     * @param ids
//     * @return
//     */
//    Boolean commitAudit(List<String> ids) throws Exception;
//
//    /**
//     * 关闭计划
//     *
//     * @param ids
//     * @return
//     */
//    Boolean closePlan(List<String> ids) throws Exception;
//
//    /**
//     * 递归更新子级状态
//     *
//     * @param planDTOS
//     * @param status
//     * @throws Exception
//     */
//    void recursivelyChildStateUpdate(List<Plan> planDTOS, int status) throws Exception;
//
//    /**
//     * 计划导出（Excel）
//     *
//     * @param id
//     * @param response
//     */
//    void exportByExcel(String id, HttpServletResponse response) throws Exception;
//
//    /**
//     * 任务下发
//     *
//     * @param ids
//     * @return
//     */
//    Boolean dispatch(List<String> ids) throws Exception;
//
//    /**
//     * 计划分解
//     *
//     * @param ids
//     * @return
//     */
//    Boolean resolve(List<String> ids) throws Exception;
//
//    /**
//     * 审核通过
//     *
//     * @param ids
//     * @return
//     */
//    Boolean commitAuditPass(List<String> ids) throws Exception;
//
//    /**
//     * 获取子计划树
//     *
//     * @param planTreeDto
//     * @return
//     */
//    List<PlanTreeVo> getChildrenTreeList(String planId,PlanTreeDto planTreeDto) throws Exception;
//}
