<template>
  <BasicDrawer
    v-bind="$attrs"
    title="同意流程"
    width="340"
    :mask-closable="false"
    :show-footer="true"
    :after-visible-change="afterVisibleChange"
    @register="drawerRegister"
  >
    <BasicForm
      v-if="visible"
      @register="formRegister"
    />

    <div
      v-if="dataSource.length"
      class="table-wrap"
    >
      <div class="pb10">
        指定负责人:
      </div>
      <OrionTable :options="tableOptions">
        <template #action="{ record }">
          <div
            v-if="!record.applicant"
            class="action-btn"
            @click="addUser(record)"
          >
            指定
          </div>
        </template>
      </OrionTable>
    </div>
    <!--选人-->
    <SelectUserModal
      :on-ok="selectUserChange"
      @register="selectUserRegister"
    />

    <template #footer>
      <div class="flex flex-pac">
        <div class="mr10">
          <DrawerBasicButton type="cancel">
            取消
          </DrawerBasicButton>
        </div>
        <div>
          <DrawerBasicButton
            :loading="submitLoading"
            @click="submitHandle"
          >
            确认提交
          </DrawerBasicButton>
        </div>
      </div>
    </template>
  </BasicDrawer>
</template>

<script lang="ts">
import {
  computed, defineComponent, reactive, toRefs, nextTick, inject, unref,
} from 'vue';
// import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
import { useForm, BasicForm } from '/@/components/Form';
import {
  OrionTable, useModal, useDrawerInner, SelectUserModal, BasicDrawer, DrawerBasicButton,
} from 'lyra-component-vue3';
// import { OrionTable } from '/@/components/OrionTable';
// import { DrawerBasicButton } from '/@/components/BasicButton';
// import { SelectUserModal } from '/@/components/SelectUser';
// import { useModal } from '/@/components/Modal';
import { message } from 'ant-design-vue';
import Api from '/@/api';
// import { workflowApi } from '/@/views/flowcenter/util/apiConfig';
import { BUTTON_TYPE } from '../../../enums/btnType';

export default defineComponent({
  name: 'AgreeDrawer',
  components: {
    BasicDrawer,
    BasicForm,
    OrionTable,
    DrawerBasicButton,
    SelectUserModal,
  },
  props: {
    onOk: {
      type: Function,
      default: () => null,
    },
  },
  setup(props) {
    const bpmnModuleData = inject('bpmnModuleData');
    const state = reactive({
      visible: false,
      dataSource: [],
      selectOptions: [],
      // 保存已选中的节点项
      selectedItem: null,
      // 正在指定人员的行
      currentTableItem: null,
      submitLoading: false,
    });
    const [drawerRegister, { closeDrawer }] = useDrawerInner((data) => {
      state.visible = true;
      nextTick(() => {
        init(data);
      });
    });
    const [selectUserRegister, { openModal: selectUserOpenModal }] = useModal();

    /**
       * 初始化
       */
    async function init(data) {
      const { list, flowList } = data;
      // 删除指定节点
      if (list && list.length <= 1) {
        await removeSchemaByFiled('taskDefinitionKey');
        state.selectedItem = list[0] || null;
      }
      state.selectOptions = list;
      state.dataSource = flowList || [];
    }

    // 指定人员
    function addUser(record) {
      state.currentTableItem = record;
      selectUserOpenModal();
    }

    // 选人完成
    function selectUserChange(data) {
      if (data.length > 1) {
        message.error('请选择人员');
        return Promise.reject();
      }
      const currentItem = state.currentTableItem;
      currentItem.reviewer = data[0].name;
      currentItem.ids = [data[0].id];

      //* ****这一步到低是干嘛的？*****
      const {
        menuActionItem: { id },
        userId,
      } = unref(bpmnModuleData);
      const { taskDefinitionKey } = currentItem;
      const params = {
        prearrangeId: id,
        taskDefinitionKey,
        ...(currentItem.ids.join(',').length ? { assignees: currentItem.ids.join(',') } : {}),
        // assignees: currentItem.ids.join(','),
        userId,
      };
      new Api('/workflow')
        .fetch(params, 'act-prearranged/prearranged/set-assignee', 'GET')
        .then(() => {});
    }

    // 提交
    function submitHandle() {
      const formValues = getFieldsValue();
      const { comment } = formValues;
      const taskDefinitionKey = state.selectedItem?.taskDefinitionKey;
      if (!taskDefinitionKey) {
        message.error('请选择节点');
        return;
      }
      const submitParams = {
        code: BUTTON_TYPE.agree,
        taskDefinitionKey,
        comment,
      };
      if (props.onOk) {
        state.submitLoading = true;
        props
          .onOk(submitParams)
          .then(() => {
            closeDrawer();
          })
          .finally(() => {
            state.submitLoading = false;
          });
      }
    }

    // 下拉表单项
    const [formRegister, { setFieldsValue, removeSchemaByFiled, getFieldsValue }] = useForm({
      actionColOptions: {
        span: 24,
      },
      showActionButtonGroup: false,
      schemas: [
        {
          field: 'comment',
          component: 'InputTextArea',
          label: '处理意见:',
          colProps: {
            span: 28,
          },
          componentProps: {
            maxlength: 200,
            showCount: true,
          },
        },
        {
          field: 'taskDefinitionKey',
          component: 'Select',
          label: '指定节点:',
          colProps: {
            span: 28,
          },
          componentProps: {
            options: computed(() => state.selectOptions.map((item) => ({
              key: item.taskDefinitionKey,
              value: item.taskDefinitionKey,
              label: item.taskName,
            }))),
            onChange: (taskDefinitionKey) => {
              state.selectedItem = state.selectOptions.find(
                (item) => item.taskDefinitionKey === taskDefinitionKey,
              );
            },
          },
        },
      ],
    });
    const tableOptions = {
      deleteToolButton: 'add|delete|enable|disable',
      showIndexColumn: false,
      showSmallSearch: false,
      pagination: false,
      resizeHeightOffset: 60,
      dataSource: computed(() => state.dataSource),
      columns: [
        {
          title: '节点名称',
          align: 'left',
          width: 80,
          dataIndex: 'taskName',
        },
        {
          title: '审批人',
          dataIndex: 'reviewer',
          customRender({ record }) {
            let users = [];
            if (record.assigneesUser && record.assigneesUser.length) {
              users = record.assigneesUser.map((item) => item.name);
            }
            return users.join(',');
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 60,
          slots: { customRender: 'action' },
        },
      ],
    };

    // 弹框打开隐藏事件
    function afterVisibleChange(visible) {
      state.visible = visible;
    }

    return {
      ...toRefs(state),
      drawerRegister,
      formRegister,
      selectUserRegister,
      tableOptions,
      addUser,
      afterVisibleChange,
      selectUserChange,
      submitHandle,
    };
  },
});
</script>

<style lang="less" scoped>
  .table-wrap {
    :deep(.ant-table-wrapper) {
      padding: 0;

      .ant-table-title {
        display: none;
      }
    }
  }
</style>
