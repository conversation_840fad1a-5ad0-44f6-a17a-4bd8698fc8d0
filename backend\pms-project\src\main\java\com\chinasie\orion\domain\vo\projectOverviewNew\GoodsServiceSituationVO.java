package com.chinasie.orion.domain.vo.projectOverviewNew;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "GoodsServiceSituationVO", description = "项目物资情况统计")
public class GoodsServiceSituationVO {

    @ApiModelProperty(value = "物资服务总数")
    private BigDecimal goodsServiceTotal=BigDecimal.ZERO;

    @ApiModelProperty(value = "物资服务总数（本年度）")
    private BigDecimal yearGoodsServiceTotal=BigDecimal.ZERO;

    @ApiModelProperty(value = "入库物资总数")
    private BigDecimal goodsStorageTotal=BigDecimal.ZERO;

    @ApiModelProperty(value = "超期未入库数")
    private BigDecimal goodsOutStorageTotal=BigDecimal.ZERO;

    @ApiModelProperty(value = "未审核")
    private BigDecimal goodsUncheckTotal=BigDecimal.ZERO;
    @ApiModelProperty(value = "未审核百分比")
    private String goodsUncheckTotalPercent;

    @ApiModelProperty(value = "审核中")
    private BigDecimal goodsCheckingTotal=BigDecimal.ZERO;
    @ApiModelProperty(value = "审核中百分比")
    private String goodsCheckingTotalPercent;

    @ApiModelProperty(value = "已审核")
    private BigDecimal goodsCheckedTotal=BigDecimal.ZERO;
    @ApiModelProperty(value = "已审核百分比")
    private String goodsCheckedTotalPercent;

}
