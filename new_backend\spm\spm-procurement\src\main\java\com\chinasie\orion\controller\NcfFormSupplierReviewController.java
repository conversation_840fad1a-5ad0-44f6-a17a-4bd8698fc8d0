package com.chinasie.orion.controller;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.domain.dto.NcfFormSupplierReviewDTO;
import com.chinasie.orion.domain.dto.NcfFormpurchaseRequestDTO;
import com.chinasie.orion.domain.vo.NcfFormSupplierReviewVO;
import com.chinasie.orion.service.NcfFormSupplierReviewService;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * NcfFormSupplierReview 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 14:26:38
 */
@RestController
@RequestMapping("/ncfFormSupplierReview")
@Api(tags = "资审供应商信息表")
public class NcfFormSupplierReviewController {

    @Autowired
    private NcfFormSupplierReviewService ncfFormSupplierReviewService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据【{{#id}}】", type = "资审供应商信息表", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<NcfFormSupplierReviewVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        NcfFormSupplierReviewVO rsp = ncfFormSupplierReviewService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param ncfFormSupplierReviewDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#ncfFormSupplierReviewDTO.name}}】", type = "资审供应商信息表", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody NcfFormSupplierReviewDTO ncfFormSupplierReviewDTO) throws Exception {
        String rsp = ncfFormSupplierReviewService.create(ncfFormSupplierReviewDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param ncfFormSupplierReviewDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#ncfFormSupplierReviewDTO.name}}】", type = "资审供应商信息表", subType = "编辑", bizNo = "{{#ncfFormSupplierReviewDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody NcfFormSupplierReviewDTO ncfFormSupplierReviewDTO) throws Exception {
        Boolean rsp = ncfFormSupplierReviewService.edit(ncfFormSupplierReviewDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "资审供应商信息表", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = ncfFormSupplierReviewService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "资审供应商信息表", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = ncfFormSupplierReviewService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 修改（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "修改（批量）")
    @RequestMapping(value = "/updateStatus", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】批量修改了数据", type = "资审供应商信息表", subType = "批量修改", bizNo = "{{#ids.toString()}}")
    public ResponseDTO updateStatus(@RequestBody List<NcfFormSupplierReviewDTO> dtos) throws Exception {
        ncfFormSupplierReviewService.updateStatus(dtos);
        return new ResponseDTO();
    }

    /**
     * 分页查询资审中供应商
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页查询资审中供应商")
    @LogRecord(success = "【{USER{#logUserId}}】分页查询资审中供应商", type = "资审供应商信息表", subType = "分页查询资审中供应商", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<NcfFormSupplierReviewVO>> pages(@RequestBody Page<NcfFormSupplierReviewDTO> pageRequest) throws Exception {
        Page<NcfFormSupplierReviewVO> rsp = ncfFormSupplierReviewService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("资审供应商信息表导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "资审供应商信息表", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        ncfFormSupplierReviewService.downloadExcelTpl(response);
    }

    @ApiOperation("资审供应商信息表导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "资审供应商信息表", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = ncfFormSupplierReviewService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("资审供应商信息表导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "资审供应商信息表", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = ncfFormSupplierReviewService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消资审供应商信息表导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "资审供应商信息表", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = ncfFormSupplierReviewService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("资审供应商信息表导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "资审供应商信息表", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody Page<NcfFormpurchaseRequestDTO> pageRequest, HttpServletResponse response) throws Exception {
        ncfFormSupplierReviewService.exportByExcel(pageRequest, response);
    }
}
