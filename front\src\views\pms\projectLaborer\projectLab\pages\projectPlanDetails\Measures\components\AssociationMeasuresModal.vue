<template>
  <div class="table-content">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      @selectionChange="selectionChange"
    />
  </div>
</template>

<script lang="ts" setup>
import { h, ref, Ref } from 'vue';
import {
  BasicButton,
  Layout,
  OrionTable, DataStatusTag,
} from 'lyra-component-vue3';
import { stampDate } from '/@/utils/dateUtil';
import Api from '/@/api';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

const props = withDefaults(defineProps<{
    projectId:string,
    formId:string,
}>(), {
  projectId: '',
  formId: '',
});
const searchValue = ref(null);
const selectRowKeys:Ref<string[]> = ref([]);
function selectionChange(data) {
  selectRowKeys.value = data.keys;
}
const tableRef = ref();
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: {},
  showSmallSearch: true,
  smallSearchField: ['point'],
  onSmallSearchChange: (val) => {
    searchValue.value = val;
  },
  api: (params) => {
    params.searchConditions = [
      [
        {
          field: 'status',
          fieldType: 'Integer',
          values: ['130'],
          queryType: 'eq',
        },
      ],
    ];
    params.query = {
      projectId: props.projectId,
      point: searchValue.value ?? null,
      isModel: false,
    };
    return new Api('/pas').fetch(params, 'qualityStep/page', 'POST');
  },
  columns: [

    {
      title: '质控点编码',
      dataIndex: 'number',
    },
    {
      title: '质控点',
      dataIndex: 'point',
    },
    {
      title: '控制方案',
      dataIndex: 'scheme',
      minWidth: 260,
    },
    {
      title: '状态',
      dataIndex: 'dataStatus',
      customRender: ({ record }) => h(DataStatusTag, { statusData: record.dataStatus }),
    },
    {
      title: '质控措施类型',
      dataIndex: 'typeName',
    },
    {
      title: '质控阶段',
      dataIndex: 'stage',
    },
    {
      title: '过程',
      dataIndex: 'processName',
    },
    {
      title: '质控活动',
      dataIndex: 'activityName',
    },
    {
      title: '负责人',
      dataIndex: 'resPersonName',
    },
  ],
  //  beforeFetch,
});
async function saveData() {
  const selectRows = tableRef.value.getSelectRows();
  if (selectRows.length === 0) {
    message.warning('请选择质控措施');
    return Promise.reject('');
  }
  await new Api('/pms').fetch(
    {
      toId: props.formId,
      fromIds: selectRows.map((item) => item.id),
    },
    'projectScheme/relation/qualityItem',
    'POST',
  );

  message.success('关联质控措施成功');
}
function getSelectData() {
  return tableRef.value.getSelectRows();
}
defineExpose({
  saveData,
});
</script>

<style lang="less" scoped>
.table-content{
  height: 100%;
  overflow: hidden;
}
</style>
