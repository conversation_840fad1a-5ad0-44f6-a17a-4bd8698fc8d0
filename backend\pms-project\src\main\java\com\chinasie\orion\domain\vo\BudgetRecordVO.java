package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;

import java.util.List;

/**
 * BudgetRecord VO对象
 *
 * <AUTHOR>
 * @since 2024-05-08 20:08:40
 */
@ApiModel(value = "BudgetRecordVO对象", description = "预算改变记录")
@Data
public class BudgetRecordVO extends ObjectVO implements Serializable {

    /**
     * 预算修改类型
     */
    @ApiModelProperty(value = "预算修改类型")
    private String budgetChangeType;


    /**
     * 预算Id
     */
    @ApiModelProperty(value = "预算Id")
    private String budgetId;


    /**
     * 预算修改单Id
     */
    @ApiModelProperty(value = "预算修改单Id")
    private String budgetChangeId;


    /**
     * 预算修改编码
     */
    @ApiModelProperty(value = "预算修改编码")
    private String budgetChangeNumber;


    /**
     * 预算修改表单名称
     */
    @ApiModelProperty(value = "预算修改表单名称")
    private String budgetChangeName;


    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    private Date operationTime;


    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private String operationPerson;

    @ApiModelProperty(value = "操作人")
    private String operationPersonName;


    /**
     * 改变金额
     */
    @ApiModelProperty(value = "改变金额")
    private BigDecimal changeMoney;


    /**
     * 改变后金额
     */
    @ApiModelProperty(value = "改变后金额")
    private BigDecimal afterChangeMoney;


}
