<script setup lang="ts">
import {
  computed, ComputedRef, inject, onMounted, ref, Ref, watch,
} from 'vue';
import SpinView from '/@/views/pms/components/SpinView.vue';
import { useChart } from './useChart';
import Api from '/@/api';

const projectId:string = inject('projectId');
const loading:Ref<boolean> = ref(false);
const demandInfo:Ref<Record<string, any>> = ref({});
const dataOptions: ComputedRef<any[]> = computed(() => [
  {
    name: '需求总数',
    value: demandInfo.value.total || 0,
  },
  {
    color: '#4ECAC8',
    name: '已响应需求',
    value: demandInfo.value.respondedCount || 0,
  },
  {
    color: '#20B57E',
    name: '未响应需求',
    value: demandInfo.value.noRespondedCount || 0,
  },
]);
const legendOptions = computed(() => dataOptions.value.filter((item) => item.color));
const chartOption = computed(() => ({
  tooltip: {
    trigger: 'item',
  },
  legend: {
    show: false,
  },
  color: legendOptions.value.map((item) => item.color),
  series: [
    {
      name: '需求总数',
      type: 'pie',
      radius: [0, '90%'],
      center: ['50%', '50%'],
      label: {
        show: false,
      },
      data: legendOptions.value,
    },
  ],
}));

const chartRef: Ref = ref();
const chartInstance = useChart(chartRef, loading);

watch(() => chartOption.value, (value) => {
  chartInstance.value.setOption(value);
  chartInstance.value.hideLoading();
});

onMounted(() => {
  getDemandInfo();
});

async function getDemandInfo() {
  loading.value = true;
  try {
    const result = await new Api('/pms/projectOverviewNew/demandCount').fetch({
      projectId,
    }, '', 'GET');
    demandInfo.value = result || {};
  } finally {
    loading.value = false;
  }
}
</script>

<template>
  <div class="container-risk">
    <div>
      <div
        v-for="(item,index) in dataOptions"
        :key="index"
        class="custom-legend-title"
      >
        <span>{{ item.name }}：</span>
        <span class="value">{{ item.value || 0 }}个</span>
      </div>
    </div>
    <spin-view
      v-if="loading"
      class="chart-demand"
    />
    <div
      v-show="!loading"
      ref="chartRef"
      class="chart-demand"
    />
    <div>
      <div
        v-for="(item,index) in legendOptions"
        :key="index"
        class="custom-legend-item"
      >
        <span :style="{backgroundColor:item.color}" />
        <span>{{ item.name }}<span />
        </span>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.container-risk {
  display: flex;
  align-items: center;
}

.custom-legend-title + .custom-legend-title {
  margin-top: 10px;
}

.chart-demand {
  width: 0;
  flex-grow: 1;
  height: 150px;
  margin: 0 12px;
}
</style>
