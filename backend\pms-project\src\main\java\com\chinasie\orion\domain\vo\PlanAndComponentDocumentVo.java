package com.chinasie.orion.domain.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/14/9:27
 * @description:
 */
@Data
public class PlanAndComponentDocumentVo implements Serializable {


    /**
     * checkId : string
     * className : string
     * createTime : 2022-03-14T02:12:04.894Z
     * createUserName : string
     * creatorId : string
     * description : string
     * id : string
     * imageId : string
     * initialRevId : string
     * modifyId : string
     * modifyTime : 2022-03-14T02:12:04.894Z
     * modifyUserName : string
     * name : string
     * nextRevId : string
     * number : string
     * openId : string
     * ownerId : string
     * ownerUserName : string
     * planeId : string
     * planeName : string
     * previousRevId : string
     * productId : string
     * productName : string
     * projectId : string
     * projectName : string
     * publishDate : 2022-03-14T02:12:04.894Z
     * revId : string
     * revKey : string
     * revOrder : 0
     * secretLevel : string
     * secretLevelName : string
     * securityLimit : string
     * sort : 0
     * status : 0
     * statusName : string
     * type : string
     * typeName : string
     */

    private String checkId;
    private String className;
    private String createTime;
    private String createUserName;
    private String creatorId;
    private String description;
    private String id;
    private String imageId;
    private String initialRevId;
    private String modifyId;
    private String modifyTime;
    private String modifyUserName;
    private String name;
    private String nextRevId;
    private String number;
    private String openId;
    private String ownerId;
    private String ownerUserName;
    private String planeId;
    private String planeName;
    private String previousRevId;
    private String productId;
    private String productName;
    private String projectId;
    private String projectName;
    private String publishDate;
    private String revId;
    private String revKey;
    private int revOrder;
    private String secretLevel;
    private String secretLevelName;
    private String securityLimit;
    private int sort;
    private int status;
    private String statusName;
    private String type;
    private String typeName;

    public String getCheckId() {
        return checkId;
    }

    public void setCheckId(String checkId) {
        this.checkId = checkId;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getImageId() {
        return imageId;
    }

    public void setImageId(String imageId) {
        this.imageId = imageId;
    }

    public String getInitialRevId() {
        return initialRevId;
    }

    public void setInitialRevId(String initialRevId) {
        this.initialRevId = initialRevId;
    }

    public String getModifyId() {
        return modifyId;
    }

    public void setModifyId(String modifyId) {
        this.modifyId = modifyId;
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getModifyUserName() {
        return modifyUserName;
    }

    public void setModifyUserName(String modifyUserName) {
        this.modifyUserName = modifyUserName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNextRevId() {
        return nextRevId;
    }

    public void setNextRevId(String nextRevId) {
        this.nextRevId = nextRevId;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public String getOwnerUserName() {
        return ownerUserName;
    }

    public void setOwnerUserName(String ownerUserName) {
        this.ownerUserName = ownerUserName;
    }

    public String getPlaneId() {
        return planeId;
    }

    public void setPlaneId(String planeId) {
        this.planeId = planeId;
    }

    public String getPlaneName() {
        return planeName;
    }

    public void setPlaneName(String planeName) {
        this.planeName = planeName;
    }

    public String getPreviousRevId() {
        return previousRevId;
    }

    public void setPreviousRevId(String previousRevId) {
        this.previousRevId = previousRevId;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getPublishDate() {
        return publishDate;
    }

    public void setPublishDate(String publishDate) {
        this.publishDate = publishDate;
    }

    public String getRevId() {
        return revId;
    }

    public void setRevId(String revId) {
        this.revId = revId;
    }

    public String getRevKey() {
        return revKey;
    }

    public void setRevKey(String revKey) {
        this.revKey = revKey;
    }

    public int getRevOrder() {
        return revOrder;
    }

    public void setRevOrder(int revOrder) {
        this.revOrder = revOrder;
    }

    public String getSecretLevel() {
        return secretLevel;
    }

    public void setSecretLevel(String secretLevel) {
        this.secretLevel = secretLevel;
    }

    public String getSecretLevelName() {
        return secretLevelName;
    }

    public void setSecretLevelName(String secretLevelName) {
        this.secretLevelName = secretLevelName;
    }

    public String getSecurityLimit() {
        return securityLimit;
    }

    public void setSecurityLimit(String securityLimit) {
        this.securityLimit = securityLimit;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }
}
