<template>
  <Layout3
    v-loading="state.loadingStatus"
    :defaultActionId="state.actionId"
    :menuData="state.tabsOption"
    :type="2"
    :projectData="state.basicData"
    @menuChange="onMenuChange"
  >
    <template v-if="state.basicData">
      <!--合同详情-->
      <ContractBasicInfo
        v-if="state.actionId===1"
        :id="id"
      />
      <!--合同节点-->
      <NodeInfo
        v-if="state.actionId===2"
        :id="id"
      />
      <!--采购订单-->
      <OrderInfo
        v-if="state.actionId===3"
        :id="id"
      />

      <!--节点确认记录-->
      <ConfirmInfo
        v-if="state.actionId===4"
        :id="id"
      />

      <!--合同附件信息-->
      <AppurtenantMaterialInfo
        v-if="state.actionId===5"
        :id="id"
      />
    </template>
  </Layout3>
</template>

<script lang="ts" setup>
import {
  computed, onMounted, provide, reactive,
} from 'vue';
import { Layout3 } from 'lyra-component-vue3';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import ContractBasicInfo from './contractChangeRequest/DetailIndex.vue';
import NodeInfo from '../../nodeInfo/NodeInfoIndex.vue';
import ConfirmInfo from '../../confirmInfo/ConfirmInfoIndex.vue';
import AppurtenantMaterialInfo from '../../appurtenantMaterial/AppurtenantMaterialIndex.vue';
import OrderInfo from '../../orderInfo/OrderInfoIndex.vue';

import Api from '/@/api';

const id = useRoute()?.query?.id as string;
const state = reactive({
  actionId: 1,
  tabsOption: [
    {
      name: '合同内容信息',
      id: 1,
    },
    {
      name: '支付节点信息',
      id: 2,
    },
    {
      name: '采购订单信息',
      id: 3,
    },
    {
      name: '节点确认记录',
      id: 4,
    },
    {
      name: '合同附件信息',
      id: 5,
    },
  ],
  basicData: null,
  allData: null,
  loadingStatus: false,
});

// 下发所有合同数据
provide(
  'allData',
  computed(() => state.allData),
);

onMounted(() => {
  init();
});

async function init() {
  await getData();
}

async function getData() {
  if (!id) {
    message.error('获取合同id出错');
    return;
  }
  state.loadingStatus = true;
  const allData = await new Api(`/pas/projectContractChangeApply/list/${id}`).fetch('', '', 'GET').finally(() => {
    state.loadingStatus = false;
  });
  state.allData = allData;
  state.basicData = allData?.projectContractVO;
}

function onMenuChange(menuItem) {
  state.actionId = menuItem.id;
}
</script>
