package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ProjectSchemeContentBatchDTO;
import com.chinasie.orion.domain.dto.ProjectSchemeContentDTO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.service.ProjectSchemeContentService;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.mzt.logapi.starter.annotation.LogRecords;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 *ProjectSchemeContentController
 *
 *
 * @author: yangFy
 * @date: 2023/4/20 15:00
 * @description
 * <p>
 * 项目计划记录内容
 * </p>
 */
@RestController
@RequestMapping("/schemeContent")
@Api(tags = "项目计划记录内容")
public class ProjectSchemeContentController {

    @Resource
    private ProjectSchemeContentService schemeContentService;

    @ApiOperation("添加计划记录(批量)")
    @PostMapping(value = "/createBatch")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】添加计划记录(批量)", type = "项目计划记录内容", subType = "添加计划记录(批量)", bizNo = "")
    public ResponseDTO<List<String>> createBatch(@RequestBody ProjectSchemeContentBatchDTO projectSchemeContentBatchDTO) throws Exception {
        return  ResponseDTO.success(schemeContentService.createBatch(projectSchemeContentBatchDTO));
    }


    @ApiOperation("删除(批量)")
    @DeleteMapping(value = "")
    @LogRecord(success = "【{USER{#logUserId}}】删除(批量)", type = "项目计划记录内容", subType = "删除(批量)", bizNo = "")
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<Boolean> edit(@RequestBody List<String> ids) throws Exception {
        return  ResponseDTO.success(schemeContentService.deleteByIds(ids));
    }
}
