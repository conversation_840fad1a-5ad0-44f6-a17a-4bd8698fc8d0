package com.chinasie.orion.constant;

public enum RequirementStatusEnum {
    DISTRIBUTION(120, "分发"),
    CONFIRMED(170, "确认"),
    REDISTRIBUTION(121, "重新分发"),
    UNCONFIRMED(140, "不响应"),
    ;


    private Integer status;

    private String desc;


    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    RequirementStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
