<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.IncomePlanMapper">
    <select id="getDataTotal" resultType="com.chinasie.orion.domain.entity.IncomePlan">
SELECT
  p.id,
  count(*) incomePlanCount,
	ROUND(sum(d.income_plan_amt) / 10000, 2) incomePlanAmt
FROM
	pmsx_income_plan p
	LEFT JOIN pmsx_income_plan_data d ON p.id = d.income_plan_id
WHERE
	 p.logic_status = 1 and d.logic_status = 1 and p.income_plan_type = d.data_version
		<if test="ids != null">
			AND p.id in
			<foreach item="id" index="index" collection="ids"
					 open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>
	group by p.id
    </select>
</mapper>