package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;
import java.lang.String;

/**
 * BudgetManagement Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:08
 */
@TableName(value = "pmsx_budget_management")
@ApiModel(value = "BudgetManagementEntity对象", description = "预算管理表")
@Data
public class BudgetManagement extends ObjectEntity implements Serializable {


    /**
     * 成本中心Id
     */
    @ApiModelProperty(value = "成本中心Id")
    @TableField(value = "cost_center_id")
    private String costCenterId;

    /**
     * 科目编码
     */
    @ApiModelProperty(value = "科目编码")
    @TableField(value = "expense_subject_number")
    private String expenseSubjectNumber;

    /**
     * 科目名称
     */
    @ApiModelProperty(value = "科目名称")
    @TableField(value = "expense_subject_name")
    private String expenseSubjectName;

    /**
     * 期间类型
     */
    @ApiModelProperty(value = "期间类型")
    @TableField(value = "time_type")
    private String timeType;

    /**
     * 预算期间
     */
    @ApiModelProperty(value = "预算期间")
    @TableField(value = "budget_time")
    private String budgetTime;

    /**
     * 预算对象类型
     */
    @ApiModelProperty(value = "预算对象类型")
    @TableField(value = "budget_object_type")
    private String budgetObjectType;

    /**
     * 预算对象Id
     */
    @ApiModelProperty(value = "预算对象Id")
    @TableField(value = "budget_object_id")
    private String budgetObjectId;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    @TableField(value = "currency")
    private String currency;

    /**
     * 预算申请总金额
     */
    @ApiModelProperty(value = "预算总金额")
    @TableField(value = "budget_money")
    private BigDecimal budgetMoney;

    /**
     * 1月预算
     */
    @ApiModelProperty(value = "1月预算")
    @TableField(value = "january_money")
    private BigDecimal januaryMoney;

    /**
     * 2月预算
     */
    @ApiModelProperty(value = "2月预算")
    @TableField(value = "february_money")
    private BigDecimal februaryMoney;

    /**
     * 3月预算
     */
    @ApiModelProperty(value = "3月预算")
    @TableField(value = "march_money")
    private BigDecimal marchMoney;

    /**
     * 4月预算
     */
    @ApiModelProperty(value = "4月预算")
    @TableField(value = "april_money")
    private BigDecimal aprilMoney;

    /**
     * 5月预算
     */
    @ApiModelProperty(value = "5月预算")
    @TableField(value = "may_money")
    private BigDecimal mayMoney;

    /**
     * 6月预算
     */
    @ApiModelProperty(value = "6月预算")
    @TableField(value = "june_money")
    private BigDecimal juneMoney;

    /**
     * 7月预算
     */
    @ApiModelProperty(value = "7月预算")
    @TableField(value = "july_money")
    private BigDecimal julyMoney;

    /**
     * 8月预算
     */
    @ApiModelProperty(value = "8月预算")
    @TableField(value = "august_money")
    private BigDecimal augustMoney;

    /**
     * 9月预算
     */
    @ApiModelProperty(value = "9月预算")
    @TableField(value = "september_money")
    private BigDecimal septemberMoney;

    /**
     * 10月预算
     */
    @ApiModelProperty(value = "10月预算")
    @TableField(value = "october_money")
    private BigDecimal octoberMoney;

    /**
     * 11月预算
     */
    @ApiModelProperty(value = "11月预算")
    @TableField(value = "november_money")
    private BigDecimal novemberMoney;

    /**
     * 12月预算
     */
    @ApiModelProperty(value = "12月预算")
    @TableField(value = "december_money")
    private BigDecimal decemberMoney;

    /**
     * 第一季度预算
     */
    @ApiModelProperty(value = "第一季度预算")
    @TableField(value = "first_quarter_money")
    private BigDecimal firstQuarterMoney;

    /**
     * 第二季度预算
     */
    @ApiModelProperty(value = "第二季度预算")
    @TableField(value = "second_quarter")
    private BigDecimal secondQuarter;

    /**
     * 第三季度预算
     */
    @ApiModelProperty(value = "第三季度预算")
    @TableField(value = "third_quarter")
    private BigDecimal thirdQuarter;

    /**
     * 第四季度预算
     */
    @ApiModelProperty(value = "第四季度预算")
    @TableField(value = "fourth_quarter")
    private BigDecimal fourthQuarter;

    /**
     * 预算名称
     */
    @ApiModelProperty(value = "预算名称")
    @TableField(value = "name")
    private String name;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 预算剩余金额
     */
    @ApiModelProperty(value = "预算剩余金额")
    @TableField(value = "residue_money")
    private BigDecimal residueMoney;

    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    @TableField(value = "project_id")
    private String projectId;

}
