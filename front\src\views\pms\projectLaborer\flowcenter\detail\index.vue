<template>
  <ProjectLayout
    :project-data="projectData"
    :menu-data="menuData"
    :default-action-id="defaultActionId"
    :is-header="true"
    @menu-change="onMenuChange"
  >
    <template #header-info>
      <div
        v-if="projectData"
        class="info"
      >
        <div>
          状态：<span class="status-wrap">
            <i>{{ showEdit ? projectData.currentTaskStatus : projectData.statusName }}</i>
          </span>
        </div>
        <div class="date-time">
          修改时间：{{
            showEdit ? projectData.currentTaskCreateTime : projectData.modifyTime
          }}
        </div>
        <div>所有者：{{ projectData.creatorName }}</div>
      </div>
    </template>
    <!-- <template #header-right>
      <div>
        <AButton
          style="margin-right: 10px"
          type="default"
          @click="onBack"
        >关闭返回</AButton>
        <AButton
          @click="onStart"
          type="primary"
          v-if="!showEdit"
        >启动流程</AButton>
      </div>
    </template> -->
    <ProjectContentLayout :content-title="menuTitle">
      <div
        v-show="menuIndex === 0"
        class="pd-20"
      >
        <div v-if="filterByPowerBasic()">
          <a-descriptions
            v-if="projectData"
            bordered
            :column="2"
          >
            <a-descriptions-item label="流程名称：">
              {{
                projectData.procInstName
              }}
            </a-descriptions-item>
            <a-descriptions-item label="流程编号：">
              {{ projectData.businessKey }}
            </a-descriptions-item>
            <a-descriptions-item label="所有者：">
              {{
                projectData.creatorName
              }}
            </a-descriptions-item>
            <a-descriptions-item label="所属部门：">
              {{
                projectData.organizationsName
              }}
            </a-descriptions-item>
            <a-descriptions-item label="状态：">
              {{ projectData.statusName }}
            </a-descriptions-item>
            <a-descriptions-item label="创建时间：">
              {{
                projectData.createTime
              }}
            </a-descriptions-item>
            <a-descriptions-item label="发起时间：">
              {{
                projectData.startTime
              }}
            </a-descriptions-item>
          </a-descriptions>
        </div>
        <div v-else>
          <a-empty />
        </div>
      </div>
      <div
        v-show="menuIndex === 1"
        class="pd-20"
      >
        <div v-if="filterByPowerPreset()">
          <orion-table
            ref="presetTableRef"
            :options="presetOptions"
            row-key="taskName"
            :pagination="false"
          >
            <template #assignees="{ record }">
              <span>{{
                record.assignees ? record.assignees : _joinStr(record.assigneesUser, 'name')
              }}</span>
            </template>
            <template #action="{ record, index }">
              <a
                v-if="!record.applicant"
                @click="onOpenModal(record, index)"
              >指定人员</a>
            </template>
          </orion-table>
          <TransferModal
            ref="presetTransferModalRef"
            :data-source="dataSource"
            :default-expand-all="true"
            row-key="id"
            z-index="1003"
            :target-keys="selectIds"
            show-search
            module-title="人员"
            @submit="onSubmitPresetReviewer"
          />
          <!--  会签弹窗  -->
          <BasicModal
            title="会签处理"
            @register="registerJointlySignModal"
            @ok="JointlySignOk"
            @cancel="JointlySignCancel"
          >
            <BasicTable
              ref="JointlySignTableRef"
              :columns="JointlySignColumns"
              :data-source="JointlySignData"
              :pagination="false"
            >
              <template #assignees="{ record, index }">
                <div
                  class="pointer bd"
                  @click="JointlySignModal(index, record)"
                >
                  {{ record.countersignUserName ? record.countersignUserName : '请选择' }}
                </div>
              </template>
            </BasicTable>
          </BasicModal>
          <!--  会签弹窗  -->
        </div>
        <div v-else>
          <a-empty />
        </div>
      </div>
      <div
        v-show="menuIndex === 2"
        class="pd-20"
      >
        <div v-if="filterByPowerReview()">
          <div style="height: 50vh; overflow: scroll">
            <orion-table
              ref="approveTableRef"
              :options="approveOptions"
              row-key="id"
              :pagination="false"
            >
              <template #name="{ record, text }">
                <!-- <router-link
                  :title="text"
                  :to="{
                    name: 'Document',
                    params: { id: record.id },
                    query: { type: record.className }
                  }"
                  >{{ text }}</router-link
                > -->
                <a @click="goto(record)">{{ text }}</a>
              </template>
              <template #modifyTime="{ record }">
                <span>{{ stampDate(record.modifyTime) }}</span>
              </template>
            </orion-table>
          </div>
          <div
            v-if="showEdit"
            class="approve-panel"
          >
            <a-textarea
              v-model:value="comment"
              placeholder="请输入"
              :rows="4"
            />
            <div class="btn-line">
              <a-button
                v-if="!showBtn"
                type="primary"
                class="button-margin"
                @click="onShowBtn"
              >
                立即处理
              </a-button>
              <div
                v-if="showBtn"
                class="btn-list"
              >
                <div
                  v-for="item in btnList"
                  :key="item.name"
                  class="btn-item"
                >
                  <a-button
                    v-if="filterByPower(item)"
                    type="primary"
                    class="button-margin"
                    @click="onSubmit(item.code)"
                  >
                    {{ item.name }}
                  </a-button>
                </div>
                <div class="no-handle">
                  <a-button
                    type="primary"
                    class="button-margin"
                    @click="onCloseBtn"
                  >
                    暂不处理
                  </a-button>
                </div>
              </div>
            </div>
            <agree-modal
              :width="600"
              @register="registerForm"
              @submit="onConfirmSubmit"
            />
            <TransferModal
              ref="transferModalRef"
              :data-source="dataSource"
              :default-expand-all="true"
              row-key="id"
              :target-keys="exchangeSelectIds"
              show-search
              module-title="人员"
              @submit="onSubmitSelectReviewer"
            />
          </div>
        </div>
        <div v-else>
          <a-empty />
        </div>
      </div>
      <div
        v-show="menuIndex === 3"
        class=""
      >
        <div
          v-if="filterByPowerPicture()"
          class="flow-container"
        >
          <div class="bpmn-containers">
            <div
              id="flow-canvas"
              ref="canvas"
              class="canvas"
            />
          </div>
          <div class="right-nodes">
            <h5 class="title">
              流程节点
            </h5>
            <div class="node-content">
              <a-timeline>
                <a-timeline-item
                  v-for="(item, index) in allFlowList"
                  :key="index"
                >
                  <p>{{ item[0].taskName || item[0].activityName }}</p>
                  <div class="panel-list">
                    <div
                      v-for="(subItem, index) in item"
                      :key="index"
                    >
                      <simple-panel
                        :color="renderColorByStatus(subItem.taskStatusCode)"
                        :name="subItem.assigneeName || _joinStr(subItem.assigneesUser, 'name')"
                        :status="subItem.taskStatus"
                        :comment="subItem.comment"
                        :end-time="subItem.endTime"
                        :status-code="subItem.taskStatusCode"
                      />
                    </div>
                  </div>
                </a-timeline-item>
              </a-timeline>
            </div>
          </div>
        </div>
        <div v-else>
          <a-empty />
        </div>
      </div>
      <div
        v-show="menuIndex === 4"
        class="pd-20"
      >
        <div
          v-if="filterByPowerJournal()"
          class="flow-container"
        >
          <a-table
            :columns="columns"
            :data-source="tableData"
            row-key="activityId"
            :pagination="false"
          />
        </div>
        <div v-else>
          <a-empty />
        </div>
      </div>
    </ProjectContentLayout>
    <EditDetail
      v-if="editDetail.visible"
      :data="editDetail"
    />
  </ProjectLayout>
</template>
<script lang="ts">
import {
  defineComponent, ref, reactive, toRefs, inject, watchEffect,
} from 'vue';
import {
  BasicTable, Layout, useActionsRecord, OrionTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer, isPower, Icon, TransferModal,
} from 'lyra-component-vue3';
// import { isPower } from '/@/hooks/power/useBusinessPrivilege';
// import Layout from '/@/components/Layout';
// import Icon from '/@/components/Icon';
import { useRoute, useRouter } from 'vue-router';
import { useUserStore } from '/@/store/modules/user';
// import TransferModal from '/@/components/TransferModal';
// import { BasicModal, useModal } from '/@/components/Modal';
import {
  Timeline, Collapse, Input, Table, message, Select, Empty,
} from 'ant-design-vue';
import { ProjectLayout, ProjectContentLayout } from '/@/views/pms/projectLaborer/components/ProjectLayout';
// import { BasicTable } from '/@/components/Table';
import { stampDate } from '/@/views/pms/projectLaborer/utils/dateUtil';
// import OrionTable from '/@/components/OrionTable';
import { useTabs } from '/@/hooks/web/useTabs';
import AgreeModal from './modal/Agree.vue';
import { btnType } from '../util/btnType';
import { _joinStr } from '../util/util';
import { workflowApi } from '../util/apiConfig';
import EditDetail from '/@/views/pms/projectLaborer/knowledgeEditData//EditDetail.vue';

import Viewer from 'bpmn-js/lib/Viewer';

import DetailPanel from './component/Panel.vue';
import SimplePanel from './component/SimplePanel.vue';
import CustomRenderer from './extra/index';
import MoveCanvasModule from 'diagram-js/lib/navigation/movecanvas';
import { unionBy } from 'lodash-es';
import Api from '/@/api/index';
// import { useActionsRecord } from '/@/hooks/actionsRecord/useActionsRecord';
import { handleDetailController } from '/@/views/pms/projectLaborer/utils';

const { Panel } = Collapse;

export default defineComponent({
  name: 'Flowdetail',
  components: {
    EditDetail,
    Layout,
    [Timeline.name]: Timeline,
    [Timeline.Item.name]: Timeline.Item,
    aCollapse: Collapse,
    aCollapsePanel: Panel,
    [Input.TextArea.name]: Input.TextArea,
    DetailPanel,
    SimplePanel,
    ATable: Table,
    AgreeModal,
    Icon,
    [Select.name]: Select,
    SelectOption: Select.Option,
    TransferModal,
    ProjectLayout,
    ProjectContentLayout,
    OrionTable,
    BasicModal,
    BasicTable,
    AEmpty: Empty,
  },
  setup() {
    const powerData = inject('powerData', {});
    const userStore: any = useUserStore();
    const transferModalRef: any = ref(null);
    const JointlySignTableRef: any = ref(null);
    const presetTransferModalRef: any = ref(null);
    const presetTableRef = ref<Nullable<any>>(null); // table的ref
    const { closeCurrent } = useTabs();
    const route = useRoute();
    const router = useRouter();

    const state: any = reactive({
      editDetail: {},
      /* 会签内容 */
      JointlySignInput: '',
      JointlySignColumns: [
        {
          title: '会签内容',
          dataIndex: 'content',
        },
        {
          title: '流程负责人',
          dataIndex: 'assignees',
          slots: { customRender: 'assignees' },
        },
      ],
      JointlySignData: [],
      // 存储所选数据索引
      JointlySignTableIndex: null,
      /* 会签内容 */
      exchangeSelectIds: [],
      dataSource: [],
      showBtn: false,
      hasSet: false,
      projectData: null,
      currentItem: null,
      selectIds: [],
      isSingle: false,
      hasRender: false,
      menuIndex: route.query.from ? 2 : 0,
      defaultActionId: route.query.from ? 3 : 1,
      currentIndex: 0,
      menuTitle: route.query.from ? '审批信息' : '基本信息',
      comment: '',
      btnList: [],
      flowList: [],
      allFlowList: [],
      tableData: [],
      showEdit: false,
      menuData: [
        {
          name: '基本信息',
          id: 1,
        },
        {
          name: '流程预设',
          id: 2,
        },
        {
          name: '审批信息',
          id: 3,
        },
        {
          name: '流程图',
          id: 4,
        },
        {
          name: '流程日志',
          id: 5,
        },
      ],
      user: '',
      _organization: [],
      _role: [],
      presetOptions: {
        deleteToolButton: 'add|delete|enable|disable',
        showSmallSearch: false,
        auto: {
          url: `${workflowApi}/act-prearranged/all-tasks`,
          params: {
            query: {
              userId: userStore.getUserInfo.id,
              procDefId: route.query.processDefinitionId,
              procInstId: route.query.processInstanceId,
              prearrangeId: route.query.id,
            },
          },
        },
        columns: [
          {
            title: '节点名称',
            dataIndex: 'taskName',
          },
          {
            title: '流程负责人',
            dataIndex: 'assignees',
            slots: { customRender: 'assignees' },
          },
          {
            title: '操作',
            dataIndex: 'action',
            slots: { customRender: 'action' },
          },
        ],
      },
      approveOptions: {
        deleteToolButton: 'add|delete|enable|disable',
        showSmallSearch: false,
        auto: {
          url: `${workflowApi}/act-prearranged/delivery`,
          params: {
            query: {
              userId: userStore.getUserInfo.id,
              ids: [route.query.id],
            },
          },
        },
        columns: [
          {
            title: '业务编号',
            dataIndex: 'number',
          },
          {
            title: '名称',
            dataIndex: 'name',
            slots: { customRender: 'name' },
          },
          {
            title: '密级',
            dataIndex: 'secretLevelName',
          },
          {
            title: '所有者',
            dataIndex: 'ownerName',
          },
          {
            title: '修改时间',
            dataIndex: 'modifyTime',
            slots: { customRender: 'modifyTime' },
          },
        ],
      },
    });

    if (route.query.from || route.query.isTodo) {
      state.showEdit = true;
    }

    if (route.query.processInstanceId) {
      new Api(workflowApi)
        .fetch(
          {
            userId: userStore.getUserInfo.id,
            procInstId: route.query.processInstanceId,
          },
          'act-inst-detail/infos',
          'GET',
        )
        .then((res) => {
          state.flowList = res;
        });
    }

    function _judgeFlow() {
      if (state.hasSet) {
        return;
      }
      let dataList = presetTableRef.value.getDataSource();
      dataList.forEach((item) => {
        const flag = state.tableData.find((flow) => flow.activityId === item.id && flow.complete === true);
        item.checked = flag;
      });
      presetTableRef.value.setTableData(dataList);
      state.hasSet = true;
    }

    new Api(workflowApi)
      .fetch({}, `act-prearranged/load?id=${route.query.id}`, 'GET')
      .then((res) => {
        res.name = route.query.from || route.query.isTodo ? res.currentTask : res.procInstName;
        res.projectCode = res.businessKey;
        state.projectData = res;

        const { id, procInstName } = res;
        watchEffect((data) => {
        });
      });

    function _getJournal() {
      return new Api(workflowApi).fetch(
        {
          userId: userStore.getUserInfo.id,
          procInstId: route.query.processInstanceId,
        },
        'act-inst-detail/journal',
        'GET',
      );
    }

    if (route.query.processInstanceId) {
      _getJournal().then((data) => {
        state.tableData = data;
      });
    }

    new Api(workflowApi)
      .fetch(
        // {
        //   userId: userStore.getUserInfo.id,
        //   processInstanceId: route.query.processInstanceId,
        //   prearrangeId: route.query.id
        // },
        // 'act-inst-detail/whole_process',
        // 'GET'
        {
          userId: userStore.getUserInfo.id,
          procInstId: route.query.processInstanceId,
        },
        'act-inst-detail/task/record',
        'GET',
      )
      .then((res) => {
        state.allFlowList = res;
      });

    // if (route.query.from || route.query.isTodo) {
    //   new Api(workflowApi)
    //     .fetch(
    //       {
    //         procDefId: route.query.processDefinitionId,
    //         userId: userStore.getUserInfo.id,
    //         taskId: route.query.taskId
    //       },
    //       'act-inst-detail/task-act',
    //       'GET'
    //     )
    //     .then((res) => {
    //       state.btnList = res;
    //     });
    // }

    function getBtnList() {
      return new Api(workflowApi).fetch(
        {
          procDefId: route.query.processDefinitionId,
          userId: userStore.getUserInfo.id,
          taskId: route.query.taskId,
        },
        'act-inst-detail/task-act',
        'GET',
      );
    }

    function _renderFlow() {
      if (state.hasRender) {
        return;
      }
      new Api(workflowApi)
        .fetch(
          {
            userId: userStore.getUserInfo.id,
            procDefId: route.query.processDefinitionId,
          },
          'act-flow/bpmn-by-proc-def-id',
          'GET',
        )
        .then((res) => {
          renderFlow(state, res);
          state.hasRender = true;
        });
    }

    // 根据userId查用户
    function _getListByUserId(ids) {
      return new Api('/pmi').fetch(ids.split(','), 'user/ids', 'POST');
    }

    // 根据roleId查用户
    function _getListByRoleId(ids) {
      return new Api('/pmi').fetch(ids.split(','), 'user/role/ids', 'POST');
    }

    // 根据orgId查用户
    function _getListByOrgId(ids) {
      return new Api('/pmi').fetch(ids.split(','), 'user/org/ids', 'POST');
    }

    // 根据projectId查用户
    function _getListByProId(ids) {
      return new Api('/pms').fetch(
        {
          projectId: state.projectData.bizId,
          roleIds: ids.split(','),
        },
        'project/user/listAll',
        'POST',
      );
    }

    // 根据资质过滤用户
    function _getListByQua(value, paramsUser) {
      return new Api('/pmi').fetch(
        paramsUser,
        `gradeTemporarySyn/get-grade-list?gradeValue=${value}`,
        'POST',
      );
    }

    // 密级过滤
    function _filterBySecret(userList) {
      const userIds: string[] = [];
      userList.forEach((item) => {
        userIds.push(item.id);
      });
      return new Api('/pmi').fetch(
        {
          classificationId: state.projectData.secretLevels[0],
          userIds,
        },
        'data-classification/user-filter',
        'POST',
      );
    }

    // 项目资源池过滤
    function _filterBySource(userList) {
      const userIds: string[] = [];
      userList.forEach((item) => {
        userIds.push(item.id);
      });
      new Api('/pms')
        .fetch(
          {
            projectId: state.projectData.bizId,
            userIds,
          },
          'project/resources/list',
          'POST',
        )
        .then((res) => {
          state.dataSource = res;
        });
    }

    // 获取用户
    async function _getUsers(data) {
      state.dataSource = [];
      let usersList: any[] = [];
      if (
        !data.user
          && !data.role
          && !data.organization
          && !data.projectRole
          && !data.credentials
      ) {
        message.warn('没有人员可选择');
        return;
      }
      if (data.user) {
        const ulist = await _getListByUserId(data.user);
        usersList = usersList.concat(ulist);
      }
      if (data.role) {
        const rlist = await _getListByRoleId(data.role);
        usersList = usersList.concat(rlist);
      }
      if (data.organization) {
        const olist = await _getListByOrgId(data.organization);
        usersList = usersList.concat(olist);
      }
      if (data.projectRole) {
        const plist = await _getListByProId(data.projectRole);
        usersList = usersList.concat(plist);
      }
      if (data.credentials) {
        const paramsUser: any[] = [];
        usersList.forEach((item) => {
          paramsUser.push({
            id: item.id,
          });
        });
        const qlist = await _getListByQua(data.credentials, paramsUser);
        _filterBySecret(qlist).then((resList) => {
          _filterBySource(resList);
        });
      } else {
        _filterBySecret(usersList).then((resList) => {
          _filterBySource(resList);
        });
      }
    }

    const { registerForm, openModalForm, setModalFormProps } = handleModal();

    /* 会签内容 */
    // 会签弹窗
    const [registerJointlySignModal, { openModal: openModalJointlySign }] = useModal();
    const JointlySignOk = (): void => {
      let users: string[] = [];
      let ids: string[] = [];
      JointlySignTableRef.value.getDataSource().forEach((item) => {
        if (item.countersignUserName) {
          users.push(item.countersignUserName);
          ids.push(item.countersignUserId);
        }
      });
      presetTableRef.value.getDataSource()[state.currentIndex].assignees = unionBy(users).join(',');
      presetTableRef.value.getDataSource()[state.currentIndex].selectIds = unionBy(ids).join(',');
      presetTableRef.value.setProps(presetTableRef.value.getDataSource());
      setReviewers(ids);
      openModalJointlySign(false);
    };
    const JointlySignCancel = (): void => {};

    // 会签内容table
    const JointlySignInfo = async (): Promise<void> => {
      const res = await new Api('/pms').fetch(
        { desId: state.projectData.deliveries[0].deliveryId },
        'countersign/list',
        'POST',
      );
      state.JointlySignData = res;
      openModalJointlySign(true);
    };

    const JointlySignModal = (index, record): void => {
      _getUsers(state.currentItem);
      state.JointlySignTableIndex = index;
      state.selectIds = record.countersignUserId ? record.countersignUserId.split(',') : [];
      presetTransferModalRef.value.openModal();
    };
      // 設置表格参数
    const setJointlySignTableData = (addData): void => {
      JointlySignTableRef.value.getDataSource()[state.JointlySignTableIndex].countersignUserId = addData?.id || '';
      JointlySignTableRef.value.getDataSource()[state.JointlySignTableIndex].countersignUserName = addData?.name || '';
      putJointlySignTableData(
        JointlySignTableRef.value.getDataSource()[state.JointlySignTableIndex],
      );
      presetTransferModalRef.value.openModal(false);
    };
      // 调用接口修改数据
    const putJointlySignTableData = (data): void => {
      data.desId = state.projectData.deliveries[0].deliveryId;
      new Api('/pms').fetch(data, 'countersign', 'PUT').then(() => {
        JointlySignInfo();
      });
    };
      /* 会签内容 */

    function onSubmit(code) {
      const params: any = {
        procDefId: route.query.processDefinitionId,
        taskDefinitionKey: route.query.taskDefinitionKey,
        userId: userStore.getUserInfo.id,
        procInstId: route.query.processInstanceId,
      };
        // 同意
      if (code === btnType.agree) {
        new Api(workflowApi).fetch(params, 'act-inst-detail/next-tasks', 'GET').then((res) => {
          new Api(workflowApi)
            .fetch(
              {
                prearrangeId: route.query.id,
                taskDefinitionKey: route.query.taskDefinitionKey,
                userId: userStore.getUserInfo.id,
              },
              'act-prearranged/other/prearranged/assignee',
              'GET',
            )
            .then((resp) => {
              // 看是否有节点需要指定人员
              if (res.length > 1 || resp.length > 0) {
                const paramsData = {
                  code,
                  list: res,
                  flowList: resp,
                  procInstId: route.query.processInstanceId,
                  taskDefinitionKey: route.query.taskDefinitionKey,
                  secretLevels: state.projectData.secretLevels[0],
                  projectId: state.projectData.bizId,
                  deliveryId: state.projectData.deliveries[0].deliveryId,
                };
                openModalForm(true, paramsData);
              } else {
                // 如果不是多个节点，直接同意或者驳回
                let id = res.length > 0 ? res[0].taskDefinitionKey : '';
                submitFlow(code, id);
              }
            });
        });
      }
      // 驳回
      if (code === btnType.reject) {
        new Api(workflowApi)
          .fetch(params, 'act-inst-detail/next-reject-tasks', 'GET')
          .then((res) => {
            // 看是否多个节点
            if (res.length > 1) {
              const paramsData = {
                code,
                list: res,
              };
              openModalForm(true, paramsData);
            } else {
              // 如果不是多个节点，直接同意或者驳回
              submitFlow(code, res[0].taskDefinitionKey);
            }
          });
      }
      // 转办
      if (code === btnType.turn) {
        new Api(workflowApi)
          .fetch(
            {
              prearrangeId: route.query.id,
              taskDefinitionKey: route.query.taskDefinitionKey,
              userId: userStore.getUserInfo.id,
            },
            'act-prearranged/prearranged/assignee',
            'GET',
          )
          .then((res) => {
            state.exchangeSelectIds = _scrapeIds(res.assigneesUser);
            _getUsers(res);
            state.user = _scrapeIds(res.assigneesUser);
            transferModalRef.value.openModal();
          });
      }
    }
    function onConfirmSubmit(data) {
      submitFlow(data.code, data.taskDefinitionKey);
    }
    function submitFlow(code, taskDefinitionKey) {
      if (code === btnType.reject) {
        // if (!state.comment) {
        //   message.warn('请填写驳回原因');
        //   return;
        // }
        new Api(workflowApi)
          .fetch(
            {
              comment: state.comment,
              targetTaskDefinitionKey: taskDefinitionKey,
              userId: userStore.getUserInfo.id,
              taskId: route.query.taskId,
              variables: {},
            },
            'act-task/reject',
            'PUT',
          )
          .then(() => {
            closeCurrent();
            message.success('操作成功');
          });
      }
      if (code === btnType.agree) {
        new Api(workflowApi)
          .fetch(
            {
              comment: state.comment,
              targetTaskDefinitionKey: taskDefinitionKey,
              userId: userStore.getUserInfo.id,
              taskId: route.query.taskId,
              variables: {},
            },
            'act-task/agree',
            'PUT',
          )
          .then(() => {
            closeCurrent();
            message.success('操作成功');
          });
      }
    }

    // 拼凑人员ids
    function _scrapeIds(list) {
      const res: string[] = [];
      list.forEach((item) => {
        res.push(item.id);
      });
      return res;
    }

    // 设置审批人
    function setReviewers(ids) {
      new Api(workflowApi)
        .fetch(
          {
            // procInstId: route.query.processInstanceId,
            taskDefinitionKey: state.currentItem.taskDefinitionKey,
            assignees: ids.join(','),
            prearrangeId: route.query.id,
            userId: userStore.getUserInfo.id,
          },
          'act-prearranged/prearranged/set-assignee',
          'GET',
        )
        .then(() => {
          presetTableRef.value.reload();
        });
    }
    async function goto(record) {
      if (record.className === 'KnowledgeInfoDocument') {
        handleDetailController(record.id);
      }
      if (record.className === 'KmDocument') {
        const url = `/knowledgeModel/detail/${record.id}`;
        const form = await new Api('/kms').fetch({}, url, 'GET');
        const obj = {
          ...form,
          labelIdList: form.labelIdList === null ? [] : form.labelIdList,
        };
        state.editDetail = {
          visible: true,
          title: '模板详情',
          form: obj,
        };
      }
    }

    return {
      powerData,
      isPower,
      registerJointlySignModal,
      JointlySignOk,
      JointlySignCancel,
      JointlySignModal,
      JointlySignTableRef,
      goto,
      onBack() {
        closeCurrent();
      },
      onShowBtn() {
        getBtnList().then((list) => {
          state.btnList = list;
          new Api(workflowApi)
            .fetch(
              {
                userId: userStore.getUserInfo.id,
                taskId: route.query.taskId,
              },
              'act-task/lock',
              'POST',
            )
            .then((res) => {
              if (res) {
                state.showBtn = true;
              }
            });
        });
      },
      onCloseBtn() {
        new Api(workflowApi)
          .fetch(
            {
              userId: userStore.getUserInfo.id,
              taskId: route.query.taskId,
            },
            'act-task/unlock',
            'POST',
          )
          .then(() => {
            state.showBtn = false;
          });
      },
      onSubmitSelectReviewer({ addItems }) {
        let ids = _joinStr(addItems, 'id');
        new Api(workflowApi)
          .fetch(
            {
              taskId: route.query.taskId,
              assigneeId: ids,
              userId: userStore.getUserInfo.id,
            },
            'act-task/turn',
            'PUT',
          )
          .then(() => {
            message.success('操作成功');
            transferModalRef.value.openModal(false);
          });
      },
      rowKey: 'id',
      registerForm,
      openModalForm,
      stampDate,
      setModalFormProps,
      activeKey: ref('1'),
      onSubmit,
      onConfirmSubmit,
      transferModalRef,
      ...toRefs(state),
      columns: [
        {
          title: '审批环节',
          dataIndex: 'activityName',
          key: 'activityName',
        },
        {
          title: '审批人',
          dataIndex: 'assigneeName',
          key: 'assigneeName',
        },
        {
          title: '接收时间',
          dataIndex: 'startTime',
          key: 'startTime',
        },
        {
          title: '审批时间',
          key: 'endTime',
          dataIndex: 'endTime',
        },
        {
          title: '审批状态',
          key: 'commentStatus',
          dataIndex: 'commentStatus',
        },
        {
          title: '审批意见',
          key: 'comment',
          dataIndex: 'comment',
        },
      ],
      onMenuChange(e) {
        state.menuIndex = e.index;
        state.menuTitle = e.item.name;
        if (e.index === 3) {
          _renderFlow();
        }
        if (e.index === 1) {
          _judgeFlow();
        }
      },
      _joinStr,
      renderColorByStatus(statusCode) {
        switch (statusCode) {
          case 'NOT_STARTED':
            return '#ACB2BF';
          case 'CANCELLED':
            return '#EC8200';
          case 'COMPLETED':
            return '#46BA7B';
          default:
            return '#ACB2BF';
        }
      },
      presetTransferModalRef,
      presetTableRef,
      onOpenModal(row, index) {
        state.currentItem = row;
        state.currentIndex = index;
        if (row.tags && row.tags.indexOf('JOINTLY_SIGN') !== -1) {
          JointlySignInfo();
        } else {
          _getUsers(row);
          state.selectIds = row.assigneesUser.length > 0
            ? _scrapeIds(row.assigneesUser)
            : row?.selectIds
              ? row?.selectIds.split(',')
              : [];
          state.isSingle = !row.multiInst; // 看是否多人审批
          presetTransferModalRef.value.openModal();
        }
      },
      onSubmitPresetReviewer(add) {
        if (state.isSingle && add.targetItems.length > 1) {
          message.warn('此节点为单人审批，不能选择多个审批人');
          return;
        }
        if (add.targetItems.length > 0) {
          if (state.currentItem && state.currentItem.tags.indexOf('JOINTLY_SIGN') !== -1) {
            if (add.targetItems.length > 1) {
              message.warn('只能选择一个人');
              return;
            }
            setJointlySignTableData(add.targetItems[0]);
          } else {
            const users: string[] = [];
            const ids: string[] = [];
            add.targetItems.forEach((item) => {
              users.push(item.name);
              ids.push(item.id);
            });
            presetTableRef.value.getDataSource()[state.currentIndex].assignees = users.join(',');
            presetTableRef.value.getDataSource()[state.currentIndex].selectIds = ids.join(',');
            presetTableRef.value.setProps(presetTableRef.value.getDataSource());
            setReviewers(ids);
          }
        }
        presetTransferModalRef.value.openModal(false);
      },
      filterByPower(item) {
        if (item.code === btnType.agree && isPower('RWJD_container_button_02', powerData, true)) {
          return true;
        }
        if (
          item.code === btnType.reject
            && isPower('RWJD_container_button_03', powerData, true)
        ) {
          return true;
        }
        if (item.code === btnType.turn && isPower('RWJD_container_button_04', powerData, true)) {
          return true;
        }
        return false;
      },
      filterByPowerBasic() {
        if (!state.showEdit && isPower('LCSL_container_01', powerData, true)) {
          return true;
        }
        if (state.showEdit && isPower('RWJD_container_01', powerData, true)) {
          return true;
        }
        return false;
      },
      filterByPowerPreset() {
        if (!state.showEdit && isPower('LCSL_container_02', powerData, true)) {
          return true;
        }
        if (state.showEdit && isPower('RWJD_container_02', powerData, true)) {
          return true;
        }
        return false;
      },
      filterByPowerReview() {
        if (!state.showEdit && isPower('LCSL_container_03', powerData, true)) {
          return true;
        }
        if (state.showEdit && isPower('RWJD_container_03', powerData, true)) {
          return true;
        }
        return false;
      },
      filterByPowerPicture() {
        if (!state.showEdit && isPower('LCSL_container_04', powerData, true)) {
          return true;
        }
        if (state.showEdit && isPower('RWJD_container_04', powerData, true)) {
          return true;
        }
        return false;
      },
      filterByPowerJournal() {
        if (!state.showEdit && isPower('LCSL_container_05', powerData, true)) {
          return true;
        }
        if (state.showEdit && isPower('RWJD_container_05', powerData, true)) {
          return true;
        }
        return false;
      },
    };
  },
});

function handleModal() {
  const [registerForm, { openModal: openModalForm, setModalProps: setModalFormProps }] = useModal();
  return {
    registerForm,
    openModalForm,
    setModalFormProps,
  };
}

export function renderFlow(state, xml) {
  const bpmnModeler = new Viewer({
    container: '#flow-canvas',
    additionalModules: [CustomRenderer, MoveCanvasModule],
  });
  let completeActivity = {};

  state.tableData.forEach((item) => {
    if (item.complete) {
      completeActivity[item.activityId] = true;
    }
  });
  let customRenderer = bpmnModeler.get('customRenderer');
  customRenderer.setComplete(completeActivity);

  bpmnModeler.importXML(xml, (err) => {
    if (err) {
      console.error(err);
    } else {
      bpmnModeler.get('canvas').zoom('fit-viewport', 'auto'); // 自动调整流程在画布的位置
    }
  });
}
</script>
<style lang="less" scoped>
  .flow-container {
    display: flex;
    flex-direction: row;
  }
  :deep(.bpmn-containers) {
    width: 70%;
    height: 70vh;

    #flow-canvas {
      height: 100%;
      overflow: scroll;
    }

    img {
      display: none;
    }
  }

  :deep(.right-nodes) {
    width: 40%;
    .title {
      background: #f4f5f8;
      width: 100%;
      padding: 10px;
      margin-bottom: 20px;
    }
    .node-content {
      padding-left: 10px;
    }
  }
  .btn-line {
    display: flex;
    flex-direction: row;
    margin-top: 10px;

    .btn-item {
      margin-right: 10px;
    }
  }
  .top-tabs {
    .panel-list {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
    }
  }
  .info {
    width: 420px;
    display: flex;
    flex-wrap: wrap;

    > div {
      width: 50%;
    }
    .date-time {
      white-space: nowrap;
    }
    .status-wrap {
      > i {
        min-width: 62px;
        text-align: center;
        display: inline-block;
        height: 24px;
        line-height: 24px;
        border-radius: ~`getPrefixVar('border-radius-base')`;
        padding: 0 6px;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        background: rgb(234, 241, 250);
        color: rgb(42, 113, 202);
      }
    }
  }
  .pd-20 {
    padding: 20px;
  }
  .btn-list {
    display: flex;
    flex-direction: row;
  }
  .approve-panel {
    position: absolute;
    bottom: 10px;
    width: 90%;
  }
  :deep(.ant-table-body) {
    height: 40vh !important;
  }
  .bd {
    border: 1px solid #ccc;
  }
</style>
