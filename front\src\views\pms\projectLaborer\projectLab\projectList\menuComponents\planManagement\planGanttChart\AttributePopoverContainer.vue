<template>
  <div class="attribute-popover-container-wrap-pms">
    <Spin :spinning="state.loadingStatus">
      <div v-if="state.taskDetail">
        <div class="title">
          {{ state.taskDetail?.name }}
        </div>
        <div class="content">
          <div class="flex">
            <div class="s-title">
              责任部门:
            </div>
            <div class="flex-f1">
              {{ state.taskDetail?.resDeptName }}
            </div>
          </div>
          <div class="flex">
            <div class="s-title">
              责任人:
            </div>
            <div class="flex-f1">
              {{ state.taskDetail?.resUserName }}
            </div>
          </div>
          <div class="flex">
            <div class="s-title">
              责任单位:
            </div>
            <div class="flex-f1">
              {{ state.taskDetail?.resOrgName }}
            </div>
          </div>
          <!--        <div class="flex">-->
          <!--          <div class="s-title">密级:</div>-->
          <!--          <div class="flex-f1">非密</div>-->
          <!--        </div>-->
          <div class="flex">
            <div class="s-title">
              截止时间:
            </div>
            <div class="flex-f1">
              {{ state.taskDetail?.planPredictEndTime ? formatDate(state.taskDetail?.planPredictEndTime):'' }}
            </div>
          </div>
        </div>
      </div>
      <div
        v-else
        class="empty"
      />
    </Spin>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive } from 'vue';
import { Spin } from 'ant-design-vue';
import dayjs from 'dayjs';

const props = defineProps<{
  taskItem?: any,
  getTaskDetailApi?:(task)=> Promise<any>
}>();

const state = reactive({
  loadingStatus: false,
  taskDetail: null,
});

onMounted(() => {
  init();
});

function init() {
  getTaskDetail();
}

async function getTaskDetail() {
  if (!props.getTaskDetailApi) return;
  state.loadingStatus = true;
  state.taskDetail = await props.getTaskDetailApi(props.taskItem);
  state.loadingStatus = false;
}

function formatDate(t) {
  return t ? dayjs(t).format('YYYY-MM-DD') : '';
}

</script>

<style  lang="less">
.attribute-popover-container-wrap-pms {
  width: 220px;
  height: 185px;
  padding: 15px;
  .empty {
    height: 130px
  }
  .title {
    font-size: 14px;
    font-weight: bold;

  }
  .content {
    margin-top: 5px;
    color: ~`getPrefixVar('text-color-second')`;

    >div {
      padding: 2px 0;
    }

    .s-title {
      padding-right: 4px;
    }
  }
}
</style>
