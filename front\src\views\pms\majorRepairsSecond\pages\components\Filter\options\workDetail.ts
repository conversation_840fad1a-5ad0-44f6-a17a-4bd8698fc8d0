import Api from '/@/api';
import { repeat } from 'lodash-es';

export const getFilterOptions = (params: object): any[] => [
  {
    title: '基本信息',
    forms: [
      {
        label: '项目名称',
        component: 'Input',
        field: 't.project_name',
        condition: 'like',
      },
      {
        label: '项目计划名称',
        component: 'Input',
        field: 't.plan_scheme_name',
        condition: 'like',
      },
      {
        label: '作业名称',
        component: 'Input',
        field: 't.name',
        condition: 'like',
      },
      {
        label: '工单号',
        component: 'Input',
        field: 't.number',
        condition: 'like',
      },
      {
        label: '作业负责人',
        component: 'Select',
        field: 'u.id',
        condition: '=',
        componentProps: {
          api: async () => {
            const result = await new Api('/pms/job-manage/person/simple/list').fetch(params, '', 'POST');
            return result?.map((item) => ({
              label: `${item.name} (${item.code})`,
              value: item.id,
            }));
          },
        },
      },
      {
        label: '负责人所在中心',
        component: 'Select',
        field: 'd.id',
        condition: '=',
        componentProps: {
          fieldNames: {
            label: 'name',
            value: 'id',
          },
          api: () => new Api('/pmi/organization/dept/list').fetch('', '20', 'GET'),
        },
      },
    ],
  },
  {
    title: '属性信息',
    forms: [
      {
        label: '是否重大项目',
        component: 'Select',
        field: 't.is_major_project',
        condition: '=',
        componentProps: {
          options: [
            {
              label: '是',
              value: true,
            },
            {
              label: '否',
              value: false,
            },
          ],
        },
      },
      {
        label: '是否高风险',
        component: 'Select',
        field: 't.is_high_risk',
        condition: '=',
        componentProps: {
          options: [
            {
              label: '是',
              value: true,
            },
            {
              label: '否',
              value: false,
            },
          ],
        },
      },
      {
        label: '高风险等级',
        component: 'Select',
        field: 'jh.risk_level',
        condition: '=',
        componentProps: {
          options: [
            {
              label: '一级',
              value: '一级',
            },
            {
              label: '二级',
              value: '二级',
            },
            {
              label: '三级',
              value: '三级',
            },
          ],
        },
      },
      {
        label: '作业状态',
        component: 'Select',
        field: 't.phase',
        condition: '=',
        componentProps: {
          api: async () => {
            const result = await new Api('/pms/job-manage/phase/list').fetch('', '', 'POST');
            return result.map((item) => ({
              label: item,
              value: item,
            }));
          },
        },
      },
      {
        label: '工作包审查状态',
        component: 'Select',
        field: 't.work_package_status',
        condition: '=',
        componentProps: {
          fieldNames: {
            label: 'statusName',
            value: 'status',
          },
          api: () => new Api('/pms/job-manage/work/package/list').fetch('', '', 'POST'),
        },
      },
      {
        label: '首次执行',
        component: 'SelectDictVal',
        field: 't.first_execute',
        condition: '=',
        componentProps: {
          dictNumber: 'pms_first_execute',
        },
      },
      {
        label: '新人参与',
        component: 'Select',
        field: 't.new_participants',
        condition: '=',
        componentProps: {
          options: [
            {
              label: '是',
              value: true,
            },
            {
              label: '否',
              value: false,
            },
          ],
        },
      },
    ],
  },
  {
    title: '日期信息',
    column: 2,
    forms: [
      {
        label: '计划开始日期',
        component: 'RangePicker',
        field: 't.begin_time',
      },
      {
        label: '计划结束日期',
        component: 'RangePicker',
        field: 't.end_time',
      },
      {
        label: '实际开始日期',
        component: 'RangePicker',
        field: 't.actual_begin_time',
        style: {
          'grid-column-start': '1',
        },
      },
      {
        label: '实际结束日期',
        component: 'RangePicker',
        field: 't.actual_end_time',
      },
    ],
  },
];
