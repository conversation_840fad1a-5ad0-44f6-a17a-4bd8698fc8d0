package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/08/10:40
 * @description:
 */

@ApiModel(value = "MajorRepairPlanDTO对象", description = "大修计划")
@Data
@ExcelIgnoreUnannotated
public class MajorRepairPlanDTO extends ObjectDTO implements Serializable {

    /**
     * 大修轮次（全局唯一）
     */
    @ApiModelProperty(value = "大修轮次（全局唯一）")
    @ExcelProperty(value = "大修轮次（全局唯一） ", index = 0)
    private String repairRound;

    /**
     * 大修名称
     */
    @ApiModelProperty(value = "大修名称")
    @ExcelProperty(value = "大修名称 ", index = 1)
    private String name;

    /**
     * 大修类别
     */
    @ApiModelProperty(value = "大修类别")
    @ExcelProperty(value = "大修类别 ", index = 2)
    private String type;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    @ExcelProperty(value = "计划开始时间 ", index = 3)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date beginTime;

    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    @ExcelProperty(value = "计划结束时间 ", index = 4)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 实际开始时间
     */
    @ApiModelProperty(value = "实际开始时间")
    @ExcelProperty(value = "实际开始时间 ", index = 5)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date actualBeginTime;

    /**
     * 实际结束时间
     */
    @ApiModelProperty(value = "实际结束时间")
    @ExcelProperty(value = "实际结束时间 ", index = 6)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date actualEndTime;

    /**
     * 工期（天数）
     */
    @ApiModelProperty(value = "工期（天数）")
    @ExcelProperty(value = "工期（天数） ", index = 7)
    private Integer workDuration;

    /**
     * 大修经理
     */
    @ApiModelProperty(value = "大修经理")
    @ExcelProperty(value = "大修经理 ", index = 8)
    private String repairManager;

    @ApiModelProperty(value = "基地编号")
    private String baseCode;

    @ApiModelProperty(value = "基地名称")
    private String baseName;

    @ApiModelProperty(value = "是否办结")
    private Boolean isFinish;

    /**
     * 统计开始时间
     */
    @ApiModelProperty(value = "统计开始时间")
    @ExcelProperty(value = "统计开始时间 ", index = 3)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date statisticBeginTime;

    /**
     * 统计结束时间
     */
    @ApiModelProperty(value = "统计结束时间")
    @ExcelProperty(value = "统计结束时间 ", index = 4)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date statisticEndTime;
}
