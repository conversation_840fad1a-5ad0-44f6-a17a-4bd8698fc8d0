package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.util.Date;
import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProjectOrderOther DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-31 14:19:24
 */
@ApiModel(value = "ProjectOrderOtherDTO对象", description = "其他信息")
@Data
@ExcelIgnoreUnannotated
public class ProjectOrderOtherDTO extends  ObjectDTO   implements Serializable{

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    @ExcelProperty(value = "订单编号 ", index = 0)
    private String orderNumber;

    /**
     * 要求到货时间
     */
    @ApiModelProperty(value = "要求到货时间")
    @ExcelProperty(value = "要求到货时间 ", index = 1)
    private Date receiveDate;

    /**
     * 特殊送货要求
     */
    @ApiModelProperty(value = "特殊送货要求")
    @ExcelProperty(value = "特殊送货要求 ", index = 2)
    private String receiveDemand;

    /**
     * 采购组织编码
     */
    @ApiModelProperty(value = "采购组织编码")
    @ExcelProperty(value = "采购组织编码 ", index = 3)
    private String buyOrgCode;

    /**
     * 采购组织名称
     */
    @ApiModelProperty(value = "采购组织名称")
    @ExcelProperty(value = "采购组织名称 ", index = 4)
    private String buyOrgName;

    /**
     * 工厂
     */
    @ApiModelProperty(value = "工厂")
    @ExcelProperty(value = "工厂 ", index = 5)
    private String shop;

    /**
     * 确认控制
     */
    @ApiModelProperty(value = "确认控制")
    @ExcelProperty(value = "确认控制 ", index = 6)
    private String confirmControl;

    /**
     * 结算方式
     */
    @ApiModelProperty(value = "结算方式")
    @ExcelProperty(value = "结算方式 ", index = 7)
    private String settlementmethod;

    /**
     * 买方留言
     */
    @ApiModelProperty(value = "买方留言")
    @ExcelProperty(value = "买方留言 ", index = 8)
    private String leaveWord;




}
