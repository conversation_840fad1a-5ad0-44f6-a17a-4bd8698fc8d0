package com.chinasie.orion.service.impl;

import com.chinasie.orion.domain.dto.AttendanceSignDTO;
import com.chinasie.orion.domain.entity.AttendanceSign;
import com.chinasie.orion.domain.entity.AttendanceSignQuarterStatistics;
import com.chinasie.orion.domain.entity.ContractCenterPlanStatistics;
import com.chinasie.orion.domain.entity.Deliverable;
import com.chinasie.orion.domain.vo.AttendanceResultSignVO;
import com.chinasie.orion.domain.vo.AttendanceSignListVO;
import com.chinasie.orion.domain.vo.AttendanceSignVO;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.AttendanceSignMapper;
import com.chinasie.orion.repository.ContractCenterPlanMapper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.AttendanceSignService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.ArrayStack;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * AttendanceSign 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28 14:00:09
 */
@Service
@Slf4j
public class AttendanceSignServiceImpl extends  OrionBaseServiceImpl<AttendanceSignMapper, AttendanceSign>   implements AttendanceSignService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    @Autowired
    private ContractCenterPlanMapper contractCenterPlanMapper;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public AttendanceSignVO detail(String id, String pageCode) throws Exception {
        AttendanceSign attendanceSign =this.getById(id);
        AttendanceSignVO result = BeanCopyUtils.convertTo(attendanceSign,AttendanceSignVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param attendanceSignDTO
     */
    @Override
    public  String create(AttendanceSignDTO attendanceSignDTO) throws Exception {
        AttendanceSign attendanceSign =BeanCopyUtils.convertTo(attendanceSignDTO,AttendanceSign::new);
        this.save(attendanceSign);

        String rsp=attendanceSign.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param attendanceSignDTO
     */
    @Override
    public Boolean edit(AttendanceSignDTO attendanceSignDTO) throws Exception {
        AttendanceSign attendanceSign =BeanCopyUtils.convertTo(attendanceSignDTO,AttendanceSign::new);

        this.updateById(attendanceSign);

        String rsp=attendanceSign.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<AttendanceSignVO> pages( Page<AttendanceSignDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<AttendanceSign> condition = new LambdaQueryWrapperX<>( AttendanceSign. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(AttendanceSign::getCreateTime);


        Page<AttendanceSign> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), AttendanceSign::new));

        PageResult<AttendanceSign> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<AttendanceSignVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<AttendanceSignVO> vos = BeanCopyUtils.convertListTo(page.getContent(), AttendanceSignVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public AttendanceResultSignVO list(AttendanceSignDTO attendanceSignDTO) throws Exception {
        AttendanceResultSignVO attendanceResultSignVO = new AttendanceResultSignVO();
        List<AttendanceSignQuarterStatistics> quarterStatistics =  this.baseMapper.attendanceSignStatistics(attendanceSignDTO.getAttandanceYear(),attendanceSignDTO.getContractNo(),attendanceSignDTO.getOrgCode(),attendanceSignDTO.getAttandanceQuarter());
        if(CollectionUtils.isEmpty(quarterStatistics)){
            return null;
        }
        Integer quarter =attendanceSignDTO.getAttandanceQuarter();
        int start  = 1;
        int end  = 1;
        if(quarter == 1){
            start = 1;
            end = 3;
        }
        else if(quarter == 2){
            start = 4;
            end = 6;
        }
        else if(quarter == 3){
            start = 7;
            end = 9;
        }
        else if(quarter == 4){
            start = 10;
            end = 12;
        }
        final  int startMonth = start;
        final  int endMonth = end;
        List<ContractCenterPlanStatistics>  centerPlanStatistics = contractCenterPlanMapper.contractCenterPlanStatistics(attendanceSignDTO.getAttandanceYear(),attendanceSignDTO.getContractNo(),attendanceSignDTO.getOrgCode(),"positionCost");
        if(!CollectionUtils.isEmpty(centerPlanStatistics)){
            centerPlanStatistics.stream().filter(item -> item.getDataMonth() >= startMonth && item.getDataMonth() <= endMonth).collect(Collectors.toList());
        }
        Map<String,List<ContractCenterPlanStatistics>>  contractCenterPlanStatisticsMap = centerPlanStatistics.stream().collect(Collectors.groupingBy(ContractCenterPlanStatistics::getCostName));
       List<AttendanceSignListVO> list = new ArrayList<>();
        attendanceResultSignVO.setList(list);
        quarterStatistics.forEach(item ->{
            AttendanceSignListVO attendanceSignListVO = new AttendanceSignListVO();

            list.add(attendanceSignListVO);
            String jobGrade = item.getJobGrade();
            attendanceSignListVO.setJobGrade(jobGrade);
            attendanceSignListVO.setActualUserCount(item.getUserCount());
            attendanceSignListVO.setWorkload(item.getAttandanceRate() == null ? new BigDecimal(0):item.getAttandanceRate());
            List<ContractCenterPlanStatistics> contractCenterPlanStatistics = contractCenterPlanStatisticsMap.get(jobGrade);
            if(!CollectionUtils.isEmpty(contractCenterPlanStatistics)){
                int userCount = contractCenterPlanStatistics.stream()
                        .mapToInt(ContractCenterPlanStatistics::getNum)
                        .sum();
                attendanceSignListVO.setPlanUserCount(userCount);
                ContractCenterPlanStatistics planStatistics = contractCenterPlanStatistics.get(0);
                BigDecimal unit_price = planStatistics.getUnit_price() == null?new BigDecimal(0) : planStatistics.getUnit_price();
                attendanceSignListVO.setJobGradeAmt(unit_price);
                if(unit_price != null && item.getUserCount() != null){
                    attendanceSignListVO.setJobGradeTotalAmt(unit_price.multiply(new BigDecimal(item.getUserCount())));
                }
            }
        });

        BigDecimal totalAmount = list.stream()
                .map(AttendanceSignListVO::getJobGradeTotalAmt)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        attendanceResultSignVO.setJobGradeTotalAmt(totalAmount);

        BigDecimal workloadTotalAmt = list.stream()
                .map(AttendanceSignListVO::getWorkload)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        attendanceResultSignVO.setWorkloadTotalAmt(workloadTotalAmt);
        return attendanceResultSignVO;
    }

    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "出勤签到导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", AttendanceSignDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        AttendanceSignExcelListener excelReadListener = new AttendanceSignExcelListener();
        EasyExcel.read(inputStream,AttendanceSignDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<AttendanceSignDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("出勤签到导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<AttendanceSign> attendanceSignes =BeanCopyUtils.convertListTo(dtoS,AttendanceSign::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::AttendanceSign-import::id", importId, attendanceSignes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<AttendanceSign> attendanceSignes = (List<AttendanceSign>) orionJ2CacheService.get("pmsx::AttendanceSign-import::id", importId);
        log.info("出勤签到导入的入库数据={}", JSONUtil.toJsonStr(attendanceSignes));

        this.saveBatch(attendanceSignes);
        orionJ2CacheService.delete("pmsx::AttendanceSign-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::AttendanceSign-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<AttendanceSign> condition = new LambdaQueryWrapperX<>( AttendanceSign. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(AttendanceSign::getCreateTime);
        List<AttendanceSign> attendanceSignes =   this.list(condition);

        List<AttendanceSignDTO> dtos = BeanCopyUtils.convertListTo(attendanceSignes, AttendanceSignDTO::new);

        String fileName = "出勤签到数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", AttendanceSignDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<AttendanceSignVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class AttendanceSignExcelListener extends AnalysisEventListener<AttendanceSignDTO> {

        private final List<AttendanceSignDTO> data = new ArrayList<>();

        @Override
        public void invoke(AttendanceSignDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<AttendanceSignDTO> getData() {
            return data;
        }
    }


}
