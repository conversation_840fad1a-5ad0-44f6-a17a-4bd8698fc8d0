package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

@Data
@ApiModel(value = "ProjectEvaluationDetailDTO对象", description = "项目评价详情")
public class EvaluationProjectDetailDTO extends ObjectDTO implements Serializable {


    /**
     * 评价类型
     */
    @ApiModelProperty(value = "项目评价ID")
    @NotEmpty(message = "项目评价ID不能为空")
    private String evaluationProjectId;

    /**
     * 评价类型
     */
    @ApiModelProperty(value = "评价类型")
    @NotEmpty(message = "评价类型不能为空")
    private String evaluationType;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @NotEmpty(message = "项目ID不能为空")
    private String projectId;
}
