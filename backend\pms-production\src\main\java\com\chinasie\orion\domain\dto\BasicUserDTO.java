package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.util.Date;
import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * BasicUser DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-05 09:17:18
 */
@ApiModel(value = "BasicUserDTO对象", description = "员工能力库人员信息基础表")
@Data
@ExcelIgnoreUnannotated
public class BasicUserDTO extends  ObjectDTO   implements Serializable{

    /**
     * 人员类型
     */
    @ApiModelProperty(value = "人员类型")
    @ExcelProperty(value = "人员类型 ", index = 0)
    private String personType;

    /**
     * 研究所编号
     */
    @ApiModelProperty(value = "研究所编号")
    @ExcelProperty(value = "研究所编号 ", index = 1)
    private String instituteCode;

    /**
     * 部门编号
     */
    @ApiModelProperty(value = "部门编号")
    @ExcelProperty(value = "部门编号 ", index = 2)
    private String deptCode;

    /**
     * 公司编号
     */
    @ApiModelProperty(value = "公司编号")
    @ExcelProperty(value = "公司编号 ", index = 3)
    private String companyCode;

    /**
     * 员工号
     */
    @ApiModelProperty(value = "员工号")
    @ExcelProperty(value = "员工号 ", index = 4)
    private String userCode;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @ExcelProperty(value = "姓名 ", index = 5)
    private String fullName;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    @ExcelProperty(value = "性别 ", index = 6)
    private String sex;

    /**
     * 人员性质
     */
    @ApiModelProperty(value = "人员性质")
    @ExcelProperty(value = "人员性质 ", index = 7)
    private String personnelNature;

    /**
     * 公司
     */
    @ApiModelProperty(value = "公司")
    @ExcelProperty(value = "公司 ", index = 8)
    private String companyName;

    /**
     * 部门
     */
    @ApiModelProperty(value = "部门")
    @ExcelProperty(value = "部门 ", index = 9)
    private String deptName;

    /**
     * 研究所
     */
    @ApiModelProperty(value = "研究所")
    @ExcelProperty(value = "研究所 ", index = 10)
    private String instituteName;

    /**
     * 民族
     */
    @ApiModelProperty(value = "民族")
    @ExcelProperty(value = "民族 ", index = 11)
    private String nation;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    @ExcelProperty(value = "身份证号 ", index = 12)
    private String idCard;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期")
    @ExcelProperty(value = "出生日期 ", index = 13)
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date dateOfBirth;

    /**
     * 政治面貌
     */
    @ApiModelProperty(value = "政治面貌")
    @ExcelProperty(value = "政治面貌 ", index = 14)
    private String politicalAffiliation;

    /**
     * 籍贯
     */
    @ApiModelProperty(value = "籍贯")
    @ExcelProperty(value = "籍贯 ", index = 15)
    private String homeTown;

    /**
     * 职级
     */
    @ApiModelProperty(value = "职级")
    @ExcelProperty(value = "职级 ", index = 17)
    private String jobLevel;

    /**
     * 现任职务
     */
    @ApiModelProperty(value = "现任职务")
    @ExcelProperty(value = "现任职务 ", index = 18)
    private String newPosition;

    /**
     * 职称
     */
    @ApiModelProperty(value = "职称")
    @ExcelProperty(value = "职称 ", index = 19)
    private String jobTitle;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 23)
    private String number;


    /**
     * 出生地
     */
    @ApiModelProperty(value = "出生地")
    @ExcelProperty(value = "出生地 ", index = 25)
    private String birthPlace;

    /**
     * 参加工作时间
     */
    @ApiModelProperty(value = "参加工作时间")
    @ExcelProperty(value = "参加工作时间 ", index = 26)
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date joinWorkTime;

    /**
     * 加入ZGH时间
     */
    @ApiModelProperty(value = "加入ZGH时间")
    @ExcelProperty(value = "加入ZGH时间 ", index = 27)
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date addZghTime;

    /**
     * 加入本单位时间
     */
    @ApiModelProperty(value = "加入本单位时间")
    @ExcelProperty(value = "加入本单位时间 ", index = 28)
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date addUnitTime;

    /**
     * 婚姻状况
     */
    @ApiModelProperty(value = "婚姻状况")
    @ExcelProperty(value = "婚姻状况 ", index = 29)
    private String maritalStatus;

    /**
     * 人员状态
     */
    @ApiModelProperty(value = "人员状态")
    @ExcelProperty(value = "人员状态 ", index = 30)
    private String userStatus;

    /**
     * 人员类型 0：正式成员 1：临时辅助人员
     */
    @ApiModelProperty(value = "人员类型 0：正式成员 1：临时辅助人员")
    private Integer userType;

    @ApiModelProperty(value = "到岗时间")
    private Date addWorkTime;

    @ApiModelProperty(value = "联系电话")
    private String phone;

    @ApiModelProperty(value = "基础用户id")
    private String userId;

    @ApiModelProperty(value = "离岗时间")
    private Date leaveWorkTime;

    @ApiModelProperty(value = "是否是常驻")
    private Integer permanent;

    @ApiModelProperty(value = "常驻基地编码")
    private String permanentBasicCode;

    @ApiModelProperty(value = "常驻基地名称")
    private String permanentBasicName;
}
