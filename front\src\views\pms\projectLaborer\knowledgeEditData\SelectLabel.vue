<template>
  <a-select
    v-model:value="value"
    mode="tags"
    style="width: 100%"
    :open="open"
    placeholder="请输入标签，搜索"
    :options="options"
    :filter-option="filterOption"
    @change="handleChange"
    @blur="blur"
    @click="focus"
  />
</template>
<script>
import {
  defineComponent, reactive, toRefs, ref, computed,
} from 'vue';
import { message, Select } from 'ant-design-vue';

export default defineComponent({
  name: 'SelectLabel',
  components: { ASelect: Select },
  props: {
    options: {
      type: Array,
      default() {
        return [];
      },
    },
    values: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  emits: ['update:values'],
  setup(props, { emit }) {
    const state = reactive({
      value: [],
      open: false,
      select: ref(),
      selectItems: computed({
        get() {
          return props.values;
        },
        set(val) {
          emit('update:values', val);
        },
      }),
    });

    const handleChange = (value, row) => {
      if (row[0].hasOwnProperty('label')) {
        const label = row[0].label;
        if (state.selectItems.includes(label)) {
          message.error('标签已存在');
        } else {
          state.selectItems.unshift(label);
        }
      } else {
        const label = value[0];
        if (state.selectItems.includes(label)) {
          message.error('标签已存在');
        } else {
          state.selectItems.unshift(label);
        }
      }
      state.value = [];
      state.open = false;
    };

    function focus() {
      state.open = true;
    }

    function blur() {
      state.open = false;
    }

    const filterOption = (input, option) => option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;

    return {
      ...toRefs(state),
      handleChange,
      blur,
      focus,
      filterOption,
    };
  },
});
</script>
