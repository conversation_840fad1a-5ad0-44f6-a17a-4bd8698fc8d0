package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/07/14:55
 * @description:
 */

@TableName(value = "pms_amperering_event_check_data_info")
@ApiModel(value = "SafetyQualityEnvEntity对象", description = "安质环")
@Data

public class SafetyQualityEnv extends ObjectEntity implements Serializable {

    /**
     * 事件主题
     */
    @ApiModelProperty(value = "事件主题")
    @TableField(value = "check_subject")
    private String eventTopic;

    /**
     * 事件等级
     */
    @ApiModelProperty(value = "事件等级")
    @TableField(value = "event_level")
    private String eventLevel;

    /**
     * 事件地点
     */
    @ApiModelProperty(value = "事件地点")
    @TableField(value = "event_address")
    private String eventLocation;


    /**
     * 事件位置
     */
    @ApiModelProperty(value = "事件位置")
    @TableField(value = "event_position")
    private String eventPosition;

    /**
     * 分类类型
     */
    @ApiModelProperty(value = "分类类型")
    @TableField(value = "classification_type")
    private String classificationType;

    /**
     * 隐患类型
     */
    @ApiModelProperty(value = "隐患类型")
    @TableField(value = "hidden_danger_type")
    private String hiddenDangerType;

    /**
     * 事发日期
     */
    @ApiModelProperty(value = "事发日期")
    @TableField(value = "event_date")
    private Date occurrenceDate;

//    /**
//     * 责任中心
//     */
//    @ApiModelProperty(value = "责任中心")
//    @TableField(value = "rsp_center")
//    private String rspCenter;

    /**
     * 是否大修
     */
    @ApiModelProperty(value = "是否大修")
    @TableField(value = "is_major_repair")
    private Boolean isMajorRepair;


    /**
     * 隐患/事件领域
     */
    @ApiModelProperty(value = "隐患/事件领域")
    @TableField(value = "hidden_event")
    private String hiddenEvent;

    /**
     * 事件类型
     */
    @ApiModelProperty(value = "事件类型")
    @TableField(value = "event_type")
    private String eventType;

    /**
     * 是否已关闭
     */
    @ApiModelProperty(value = "是否已关闭")
    @TableField(value = "is_closed")
    private Boolean isClosed;

    /**
     * 当前流程
     */
    @ApiModelProperty(value = "当前流程")
    @TableField(value = "current_process")
    private String currentProcess;

    /**
     * 金字塔类别
     */
    @ApiModelProperty(value = "金字塔类别")
    @TableField(value = "pyramid_category")
    private String pyramidCategory;

    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    @TableField(value = "major_repair_turn",updateStrategy = FieldStrategy.IGNORED)
    private String majorRepairTurn;

    /**
     * 是否考核
     */
    @ApiModelProperty(value = "是否考核")
    @TableField(value = "is_assessed")
    private Boolean isAssessed;

    /**
     * 考核级别
     */
    @ApiModelProperty(value = "考核级别")
    @TableField(value = "assessment_level",updateStrategy = FieldStrategy.ALWAYS)
    private String assessmentLevel;

    /**
     * 隐患编号
     */
    @ApiModelProperty(value = "隐患编号")
    @TableField(value = "check_number")
    private String hiddenDangerCode;

    @ApiModelProperty(value = "检查人编号")
    @TableField(value = "reviewer_number")
    private String reviewerNumber;
    @ApiModelProperty(value = "检查人名称")
    @TableField(value = "reviewer_name")
    private String reviewerName;

    @ApiModelProperty(value = "检查人所在部门")
    @TableField(value = "dept_name")
    private String deptName;

    @ApiModelProperty(value = "检查人所在部门编号")
    @TableField(value = "dept_code")
    private String deptCode;


    @ApiModelProperty(value = "直接责任部门名称")
    @TableField(value = "rsp_dept_name")
    private String rspDeptName;

    @ApiModelProperty(value = "直接责任部门code")
    @TableField(value = "rsp_dept_code")
    private String rspDeptCode;
    /**
     * 事件类型
     */
    @ApiModelProperty(value = "事件描述")
    @TableField(value = "event_desc")
    private String eventDesc;

    /**
     * 事件地点
     */
    @ApiModelProperty(value = "事件地点编码")
    @TableField(value = "event_address_code")
    private String eventLocationCode;


    /**
     * 地点编码
     */
    @ApiModelProperty(value = "地点编码")
    @TableField(value = "base_code")
    private String baseCode;

}
