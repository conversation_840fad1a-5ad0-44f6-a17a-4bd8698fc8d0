package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * AdvancePaymentInformation DTO对象
 *
 * <AUTHOR>
 * @since 2024-09-29 17:25:21
 */
@ApiModel(value = "AdvancePaymentInformationDTO对象", description = "预收款信息")
@Data
@ExcelIgnoreUnannotated
public class AdvancePaymentInformationDTO extends  ObjectDTO   implements Serializable{

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @ExcelProperty(value = "年份 ", index = 0)
    private String year;

    /**
     * 凭证号
     */
    @ApiModelProperty(value = "凭证号")
    @ExcelProperty(value = "凭证号 ", index = 1)
    private String voucherNumber;

    /**
     * 挂账金额
     */
    @ApiModelProperty(value = "挂账金额")
    @ExcelProperty(value = "挂账金额 ", index = 2)
    private BigDecimal accruedAmt;

    /**
     * 已清账金额
     */
    @ApiModelProperty(value = "已清账金额")
    @ExcelProperty(value = "已清账金额 ", index = 3)
    private BigDecimal clearedAmt;

    /**
     * 未结清预收款金额
     */
    @ApiModelProperty(value = "未结清预收款金额")
    @ExcelProperty(value = "未结清预收款金额 ", index = 4)
    private BigDecimal unAdvReceivableAmt;

    /**
     * 本次清账金额
     */
    @ApiModelProperty(value = "本次清账金额")
    @ExcelProperty(value = "本次清账金额 ", index = 5)
    private BigDecimal currentClearAmt;

    /**
     * 本次清账金额（不含税）
     */
    @ApiModelProperty(value = "本次清账金额（不含税）")
    @ExcelProperty(value = "本次清账金额（不含税） ", index = 6)
    private BigDecimal currentClearAmtExTax;

    /**
     * 不清账原因
     */
    @ApiModelProperty(value = "不清账原因")
    @ExcelProperty(value = "不清账原因 ", index = 7)
    private String noClearReason;

    /**
     * 收入计划填报ID
     */
    @ApiModelProperty(value = "收入计划填报ID")
    @ExcelProperty(value = "收入计划填报ID ", index = 8)
    private String incomePlanId;

    /**
     * 收入计划填报数据Id
     */
    @ApiModelProperty(value = "收入计划填报数据Id")
    @ExcelProperty(value = "收入计划填报数据Id ", index = 9)
    private String incomePlanDataId;




}
