package com.chinasie.orion.repository;

import com.chinasie.orion.domain.entity.TravelCost;
import com.chinasie.orion.domain.entity.TravelCostUserStatistics;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <p>
 * TravelCost Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28 14:45:17
 */
@Mapper
public interface TravelCostMapper extends  OrionBaseMapper  <TravelCost> {

    List<TravelCostUserStatistics> travelCostUserStatistics(@Param("year") Integer year, @Param("contractNo") String contractNo,
                                                            @Param("orgCode") String orgCode, @Param("quarter") Integer quarter);

}

