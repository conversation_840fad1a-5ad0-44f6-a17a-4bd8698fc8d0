<script setup lang="ts">
import {
  OrionTable,
} from 'lyra-component-vue3';
import {
  h, inject, ref, Ref,
} from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';

const powerCodePrefix: string = inject('powerCodePrefix');
const powerData: Ref = inject('powerData');
const detailsData: Record<string, any> = inject('detailsData');
const tableRef: Ref = ref();
const tableOptions = {
  height: 300,
  showTableSetting: false,
  showSmallSearch: false,
  showToolButton: false,
  columns: [
    {
      title: '姓名',
      dataIndex: 'userName',
    },
    {
      title: '公司',
      dataIndex: 'companyName',
    },
    {
      title: '用人部门',
      dataIndex: 'departmentName',
    },
    {
      title: '研究所',
      dataIndex: 'instituteName',
    },
    {
      title: '加入本单位时间',
      dataIndex: 'addUnittime',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '到岗时间',
      dataIndex: 'addWorkTime',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '修改时间',
      dataIndex: 'modifyTime',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
  ],
  api: (params: Record<string, any>) => new Api('/pms/basicUserLedger').fetch({
    ...params,
    query: {
      basicUserId: detailsData?.id,
    },
  }, 'page', 'POST'),
};
</script>

<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
  />
</template>

<style scoped lang="less">

</style>
