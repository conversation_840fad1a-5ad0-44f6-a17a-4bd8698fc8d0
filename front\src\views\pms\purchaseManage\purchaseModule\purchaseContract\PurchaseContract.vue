<script setup lang="ts">
import {
  BasicTableAction, IOrionTableActionItem, Layout, OrionTable, isPower, BasicButton, downloadByData,
} from 'lyra-component-vue3';
import {
  h, reactive, ref, unref, watchEffect,
} from 'vue';
import Api from '/@/api';
import { Modal, RangePicker, Tag } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import {
  cloneDeep, findIndex, get, get as loadGet, isBoolean, set,
} from 'lodash-es';
import dayjs from 'dayjs';
import MoneyRow from '../components/MoneyRow.vue';
import { openFormDrawer, parseBooleanToRender, parsePriceByNumber } from '../utils';
import PurchaseContractForm from './components/PurchaseContractForm.vue';
import {
  useWaringMoneyByProp, useWaringDay, useWaringMoney, useWaringDayByProp,
} from '../tokens/warningDayOrMony';

const tableRef = ref();
const router = useRouter();
const rowMoney = reactive([
  {
    key: 'total',
    title: '合同数量',
    value: '',
    suffix: '个',
  },
  {
    key: 'allMoney',
    title: '合同总金额',
    value: '',
    suffix: '元',
  },
  {
    key: 'saveMoney',
    title: '较立项节约总金额',
    value: '',
    suffix: '元',
  },
]);
const pageSearchConditions = ref(null);
const pageOriginSearchConditions = ref(null);

const shouldShowWarning = () => pageOriginSearchConditions.value?.length > 0
    && findIndex(pageOriginSearchConditions.value, ['type', '框架协议']) === -1;

const tableOptions = {
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  smallSearchField: [
    't.contract_name',
    't.contract_number',
    'factoryName',
  ],
  filterConfig: {
    fields: [
      {
        field: 't.contract_name',
        fieldName: '合同名称',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 't.contract_number',
        fieldName: '合同编号',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'factoryName',
        fieldName: '工厂',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'endProcurementWay',
        fieldName: '采购方式',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'type',
        fieldName: '合同类型',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Select',
        hidden: false,
        searchFieldName: null,
        constValue: JSON.stringify([
          {
            label: '标准订单',
            name: '标准订单',
            value: '标准订单',
          },
          {
            label: '框架协议',
            name: '框架协议',
            value: '框架协议',
          },
        ]),
      },
      {
        field: 'supplierName',
        fieldName: '供应商',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 't.business_rsp_user',
        fieldName: '商务负责人',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'customUpmDatePick',
        fieldName: 'UPM审批完成时间',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterface: null,
        referenceInterfaceMethod: null,
        referenceInterfaceParams: null,
        component: 'Select',
        hidden: false,
        constValue: null,
        fieldNames: null,
        searchFieldName: null,
        optionsCustomRender({ filterItem, filterMethods, groupRelation }) {
          return h(RangePicker, {
            style: {
              width: '100%',
            },
            onChange(date: any) {
              filterMethods.setFieldValue(filterItem.field, date, groupRelation);
            },
            valueFormat: 'YYYY-MM-DD',
          });
        },
      },
      {
        field: 'warningMoney',
        fieldName: '合同预警金额',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Select',
        hidden: false,
        searchFieldName: null,
        constValue: JSON.stringify(useWaringMoney()),
      },
      {
        field: 'warningDay',
        fieldName: '合同预警日期',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Select',
        hidden: false,
        searchFieldName: null,
        constValue: JSON.stringify(useWaringDay()),
      },
      {
        field: 't1.procurement_group_name',
        fieldName: '采购组',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 't1.technical_rsp_user',
        fieldName: '技术负责人',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
    ],
  },
  columns: [
    {
      title: '操作',
      dataIndex: 'actions',
      width: 120,
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
    {
      title: '合同编号',
      dataIndex: 'contractNumber',
      width: 280,
    },
    {
      title: '合同名称',
      dataIndex: 'contractName',
      width: 480,
      minWidth: 480,
    },
    {
      title: '最终价格（RMB）',
      dataIndex: 'approvedPrice', //       formatter: parsePriceByNumber,
      width: 150,
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '最终价格（原币）',
      dataIndex: 'finalPrice', //       formatter: parsePriceByNumber,
      width: 150,
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '币种',
      dataIndex: 'currency',
      width: 150,
    },
    {
      title: '供应商',
      dataIndex: 'supplierName',
      width: 220,
    },
    {
      title: '合同推荐审批完成时间',
      dataIndex: 'recommendEndTime',
      width: 220,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '预计合同开始时间',
      dataIndex: 'estimatedStartTime',
      width: 150,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '预计合同结束时间',
      dataIndex: 'estimatedEndTime',
      width: 150,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '供应商来源',
      dataIndex: 'supplierFrom',
      width: 130,
    },
    {
      title: '是否非技术推荐供应商参与',
      dataIndex: 'isTechSupplier',
      width: 130,
    },
    {
      title: '工厂',
      dataIndex: 'factoryName',
      width: 130,
    },
    {
      title: '合同执行状态',
      dataIndex: 'statusName',
      width: 130,
    },
    {
      title: '合同预警金额',
      dataIndex: 'warningMoney',
      width: 130,
      customRender({ text }) {
        return shouldShowWarning() ? '' : useWaringDayByProp(text);
      },
    },
    {
      title: '合同预警日期',
      dataIndex: 'warningDay',
      width: 130,
      customRender({ text }) {
        return shouldShowWarning() ? '' : useWaringDayByProp(text);
      },
    },
    {
      title: '框架合同剩余金额',
      dataIndex: 'freamResidueAmount',
      width: 130,
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '合同类型',
      dataIndex: 'type',
      width: 100,
    },
    {
      title: '采购方式',
      dataIndex: 'endProcurementWay',
      width: 100,
    },
    {
      title: '采购订单号',
      dataIndex: 'procurementOrderNumber',
      width: 150,
    },
    {
      title: '采购计划号',
      dataIndex: 'purchasePlanCode',
      width: 150,
    },
    {
      title: '采购申请单编码',
      dataIndex: 'purchaseRequestCode',
      width: 150,
    },
    {
      title: '申请单名称',
      dataIndex: 'applicationName',
      width: 150,
    },
    {
      title: '采购立项金额',
      dataIndex: 'procurementAmount',
      width: 100,
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '采购立项完成时间',
      dataIndex: 'projectEndTime',
      width: 150,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '归口管理',
      dataIndex: 'bkManage',
      width: 150,
    },
    {
      title: '商务负责人',
      dataIndex: 'businessRspUser',
      width: 150,
    },
    {
      title: '技术负责人',
      dataIndex: 'technicalRspUser',
      width: 150,
    },
    {
      title: '采购组',
      dataIndex: 'procurementGroupName',
      width: 150,
    },
    {
      title: '变更金额',
      dataIndex: 'changeMoney',
      width: 100,
    },
    {
      title: '变更比例',
      dataIndex: 'changePercent',
      width: 100,
    },
    {
      title: '变更后合同承诺总价（总目标值）',
      dataIndex: 'contactAmountAfterChange',
      width: 230,
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '累计索赔金额(含本次)',
      dataIndex: 'cumulativeClaimAmount',
      width: 170,
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '总累计索赔占原合同价%',
      dataIndex: 'totalClaimPctOfOrigPrice',
      width: 200,
    },
    {
      title: '支付金额',
      dataIndex: 'payMoney',
      width: 100,
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '支付比例',
      dataIndex: 'payPercent',
      width: 100,
    },
    {
      title: '是否合同终止',
      dataIndex: 'isContractTerminate',
      width: 100,
      customRender({ text }) {
        return parseBooleanToRender(text);
      },
    },
  ],
  api: (params: Record<string, any>) => {
    // 这里用来存储高级搜索的条件来判断：当合同类型为框架协议不置空合同预警金额、合同预警日期
    pageOriginSearchConditions.value = loadGet(cloneDeep(params), 'searchConditions', []).map((col) => {
      const obj = {};
      set(obj, get(col, '0.field'), get(col, '0.values.0'));
      return obj;
    });
    console.log('pageOriginSearchConditions.value', pageOriginSearchConditions.value);
    const originSearchConditions = loadGet(cloneDeep(params), 'searchConditions', []);
    let targetItem = null;
    const searchConditions = originSearchConditions.reduce((prev, cur) => {
      const innerItem = cur.find((item) => item.field === 'customUpmDatePick');
      const resetCur = cur.filter((item) => item.field !== 'customUpmDatePick');
      if (innerItem) {
        targetItem = innerItem;
        return resetCur.length ? [...prev, resetCur] : prev;
      }
      return [...prev, cur];
    }, []);
    const newSearchConditions = {
      searchConditions: searchConditions.length ? searchConditions : [],
      query: (targetItem ? {
        startDate: loadGet(targetItem.values, 0, ''),
        endDate: `${loadGet(targetItem.values, 1, '')} 23:59:59`,
      } : undefined),
    };
    pageSearchConditions.value = params.searchConditions ? newSearchConditions : null;
    return new Api('/pms/contractInfo').fetch({
      ...params,
      power: {
        pageCode: 'purchaseContract',
        containerCode: 'PMS_HTZLB_container_01',
      },
      ...newSearchConditions,
    }, 'page', 'POST');
  },
};
const actions: IOrionTableActionItem[] = [
  {
    text: '编辑',
    event: 'edit',
    isShow: (record) => isPower('PMS_HTZLB_container_01_button_03', record.rdAuthList),
  },
  {
    text: '查看',
    event: 'view',
    isShow: (record) => isPower('PMS_HTZLB_container_01_button_02', record.rdAuthList),
  },
];

const getMoney = async (params) => {
  try {
    const res = await new Api('/pms/contractInfo/getNumMoney').fetch({
      ...(params || {}),
    }, '', 'POST');
    rowMoney.forEach((item) => {
      item.value = res[item.key];
    });
  } catch (e) {
  }
};
const processRouterByType = (record) => {
  let routerName = 'PurchaseContractInfo';
  let routerQuery = 'total';
  if (record.type === '标准订单') {
    routerName = 'LumpSumContractInfo';
    routerQuery = 'lumpSumContract';
  } else if (record.type === '框架协议') {
    routerName = 'FrameworkContractInfo';
    routerQuery = 'frameworkContract';
  }
  router.push({
    name: routerName,
    params: {
      id: record.id,
    },
    query: {
      source: routerQuery,
    },
  });
};
function actionClick(actionItem: { event: string }, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'edit':
      openFormDrawer(PurchaseContractForm, record, updateTable);
      break;
    case 'view':
      processRouterByType(record);
      break;
    case 'delete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除这条数据吗？',
        onOk: () => deleteApi([record.id]),
      });
      break;
  }
}
function getPowerDataHandle(data) {
}
function updateTable() {
  tableRef.value?.reload();
}

function deleteApi(ids: string[]) {
  return new Promise((resolve) => {
    new Api('/pms/contractInfo').fetch(ids, 'remove', 'DELETE').then(() => {
      updateTable();
    }).finally(() => {
      resolve('');
    });
  });
}
watchEffect(async () => {
  await getMoney(pageSearchConditions.value);
});

async function handleExportApi() {
  const params = pageSearchConditions.value;
  Modal.confirm({
    title: '系统提示！',
    content: '确认导出所选数据？',
    onOk() {
      return new Promise((resolve) => {
        downloadByData('/pms/contractInfo/export/excel', { ...params }).then(() => {
          resolve('');
        }).catch((e) => {
          resolve(e);
        });
      });
    },
  });
}
</script>

<template>
  <Layout
    v-get-power="{pageCode: 'purchaseContract',getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      class="scroll-table"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <MoneyRow :data="rowMoney" />
        <BasicButton
          icon="sie-icon-daochu"
          class="mr-btn"
          @click="handleExportApi()"
        >
          导出
        </BasicButton>
      </template>
      <template #actions="{record}">
        <BasicTableAction
          :actions="actions"
          :record="record"
          @actionClick="actionClick($event,record)"
        />
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">
.scroll-table {
  ::-webkit-scrollbar {
    height: 15px !important;
    width: 15px !important;
  }
  ::-webkit-scrollbar-thumb {
    border-radius:0;
  }
}
.mr-btn {
  margin-left: 10px;
}
</style>
