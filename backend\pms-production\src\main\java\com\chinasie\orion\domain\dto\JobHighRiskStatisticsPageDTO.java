package com.chinasie.orion.domain.dto;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;
import java.util.Map;

@ApiModel(value = "JobHighRiskStatisticsPageDTO对象", description = "高风险作业统计")
@Data
@ExcelIgnoreUnannotated
public class JobHighRiskStatisticsPageDTO {
    List<JobHighRiskStatisticDTO> jobHighRiskStatisticDTOList;
    Map<String,Integer> map;
}
