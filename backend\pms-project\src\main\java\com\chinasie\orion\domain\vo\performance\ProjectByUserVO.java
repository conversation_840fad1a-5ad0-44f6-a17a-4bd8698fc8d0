package com.chinasie.orion.domain.vo.performance;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @author: lsy
 * @date: 2024/5/23
 * @description:
 */
@Data
@ExcelIgnoreUnannotated
public class ProjectByUserVO {

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    @ExcelProperty(value = "序号", index = 0)
    private Integer sort;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String id;


    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    @ExcelProperty(value = "项目编号", index = 1)
    private String number;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    @ExcelProperty(value = "项目名称", index = 2)
    private String name;

    /**
     * 状态对象
     */
    @ApiModelProperty(value = "状态对象")
    private DataStatusVO dataStatus;

    /**
     * 项目状态
     */
    @ApiModelProperty(value = "项目状态")
    @ExcelProperty(value = "项目状态", index = 3)
    private String statusName;

    @ApiModelProperty(value = "项目类型")
    private String projectType;

    @ApiModelProperty(value = "项目类型名称")
    @ExcelProperty(value = "项目类型", index = 4)
    private String projectTypeName;

    @ApiModelProperty(value = "项目级别")
    private String level;

    @ApiModelProperty(value = "项目级别名称")
    @ExcelProperty(value = "项目级别", index = 5)
    private String levelName;

    /**
     * 项目经理
     */
    @ApiModelProperty(value = "项目经理")
    @ExcelProperty(value = "项目经理", index = 6)
    private String pm;


    /**
     * 项目开始时间
     */
    @ApiModelProperty(value = "项目开始时间")
    @ExcelProperty(value = "项目开始时间", index = 7)
    private Date projectStartTime;

    /**
     * 项目结束时间
     */
    @ApiModelProperty(value = "项目结束时间")
    @ExcelProperty(value = "项目结束时间", index = 8)
    private Date projectEndTime;


}
