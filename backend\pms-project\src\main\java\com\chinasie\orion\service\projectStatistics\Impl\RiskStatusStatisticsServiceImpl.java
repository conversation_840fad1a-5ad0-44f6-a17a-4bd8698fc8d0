package com.chinasie.orion.service.projectStatistics.Impl;

import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.domain.dto.projectStatistics.RiskStatusStatisticsDTO;
import com.chinasie.orion.domain.entity.projectStatistics.RiskStatusStatistics;
import com.chinasie.orion.domain.vo.projectStatistics.RiskStatusStatisticsVO;
import com.chinasie.orion.repository.projectStatistics.RiskStatusStatisticsMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.service.projectStatistics.RiskStatusStatisticsService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.String;
import java.util.List;

/**
 * <p>
 * RiskStatusStatistics 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-21 13:45:41
 */
@Service
public class RiskStatusStatisticsServiceImpl extends OrionBaseServiceImpl<RiskStatusStatisticsMapper, RiskStatusStatistics> implements RiskStatusStatisticsService {

    @Autowired
    private RiskStatusStatisticsMapper riskStatusStatisticsMapper;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    public RiskStatusStatisticsVO detail(String id) throws Exception {
        RiskStatusStatistics riskStatusStatistics =riskStatusStatisticsMapper.selectById(id);
        RiskStatusStatisticsVO result = BeanCopyUtils.convertTo(riskStatusStatistics,RiskStatusStatisticsVO::new);
        return result;
    }

    /**
     *  新增
     *
     * * @param riskStatusStatisticsDTO
     */
    @Override
    public  RiskStatusStatisticsVO create(RiskStatusStatisticsDTO riskStatusStatisticsDTO) throws Exception {
        RiskStatusStatistics riskStatusStatistics =BeanCopyUtils.convertTo(riskStatusStatisticsDTO,RiskStatusStatistics::new);
        int insert = riskStatusStatisticsMapper.insert(riskStatusStatistics);
        RiskStatusStatisticsVO rsp = BeanCopyUtils.convertTo(riskStatusStatistics,RiskStatusStatisticsVO::new);
        return rsp;
    }

    /**
     *  编辑
     *
     * * @param riskStatusStatisticsDTO
     */
    @Override
    public Boolean edit(RiskStatusStatisticsDTO riskStatusStatisticsDTO) throws Exception {
        RiskStatusStatistics riskStatusStatistics =BeanCopyUtils.convertTo(riskStatusStatisticsDTO,RiskStatusStatistics::new);
        int update =  riskStatusStatisticsMapper.updateById(riskStatusStatistics);
        return SqlHelper.retBool(update);
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        int delete = riskStatusStatisticsMapper.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    public Page<RiskStatusStatisticsVO> pages(Page<RiskStatusStatisticsDTO> pageRequest) throws Exception {
        Page<RiskStatusStatistics> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), RiskStatusStatistics::new));

        PageResult<RiskStatusStatistics> page = riskStatusStatisticsMapper.selectPage(realPageRequest,null);

        Page<RiskStatusStatisticsVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<RiskStatusStatisticsVO> vos = BeanCopyUtils.convertListTo(page.getContent(), RiskStatusStatisticsVO::new);
        pageResult.setContent(vos);

        return pageResult;
    }
}
