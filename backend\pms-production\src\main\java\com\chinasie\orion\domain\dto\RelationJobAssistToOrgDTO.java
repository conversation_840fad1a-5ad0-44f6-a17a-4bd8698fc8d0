package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * RelationJobAssistToOrg DTO对象
 *
 * <AUTHOR>
 * @since 2024-11-20 19:05:01
 */
@ApiModel(value = "RelationJobAssistToOrgDTO对象", description = "作业专业协助关系表")
@Data
@ExcelIgnoreUnannotated
public class RelationJobAssistToOrgDTO extends  ObjectDTO   implements Serializable{

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    @ExcelProperty(value = "工单号 ", index = 0)
    private String jobNumber;

    /**
     * 大修组织ID：基本是执行班组
     */
    @ApiModelProperty(value = "大修组织ID：基本是执行班组")
    @ExcelProperty(value = "大修组织ID：基本是执行班组 ", index = 1)
    private String majorRepairOrgId;




}
