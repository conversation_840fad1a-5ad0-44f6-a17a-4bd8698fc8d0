<script setup lang="ts">
import { BasicCard } from 'lyra-component-vue3';
import {
  inject, reactive,
} from 'vue';
import { setBasicInfo } from '../utils';

const detailsData: Record<string, any> = inject('formData', reactive({}));
const baseProjectProps = reactive({
  list: setBasicInfo([
    {
      label: '项目编号',
      field: 'projectNumber',
    },
    {
      label: '项目名称',
      field: 'projectName',
    },
    {
      label: '项目经理',
      field: 'pm',
    },
    {
      label: '项目类型',
      field: 'projectTypeName',
      type: 'select',
    },
    {
      label: '项目子类型',
      field: 'projectSubTypeName',
      type: 'input',
    },
  ]),
  column: 4,
  dataSource: detailsData,
});
const baseQuestionProps = reactive({
  list: setBasicInfo([
    {
      label: '产品编号',
      field: 'productNumber',
      type: 'or',
    },
    {
      label: '产品名称',
      field: 'productName',
      type: 'input',
    },
    {
      label: '物料类别',
      field: 'snCode',
      type: 'input',
    },
    {
      label: '产品组',
      field: 'productGroupName',
    },
    {
      label: '产品二级分类',
      field: 'productSecondClassifyName',
    },
    {
      label: '产品/物料编码',
      field: 'materialNumber',
    },
  ]),
  column: 4,
  dataSource: detailsData,
});
</script>

<template>
  <div class="basic-info-content">
    <BasicCard
      title="关联项目信息"
      :grid-content-props="baseProjectProps"
      :isBorder="false"
    />
    <BasicCard
      v-if="detailsData.questionType==='questionType_1'"
      title="关联产品信息"
      :grid-content-props="baseQuestionProps"
      :isBorder="false"
    />
  </div>
</template>

<style scoped lang="less">
.basic-info-content{
  height: 100%;
}
</style>
