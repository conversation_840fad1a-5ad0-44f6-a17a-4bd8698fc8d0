<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>org.springdoc</groupId>
	<artifactId>springdoc-openapi</artifactId>
	<version>1.7.0</version>
	<packaging>pom</packaging>
	<name>Spring openapi documentation</name>
	<description>Spring openapi documentation</description>
	<url>https://springdoc.org/</url>

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.7.10</version>
	</parent>

	<licenses>
		<license>
			<name>The Apache License, Version 2.0</name>
			<url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
		</license>
	</licenses>

	<developers>
		<developer>
			<name>Badr NASS LAHSEN</name>
			<email><EMAIL></email>
			<organization>springdoc</organization>
			<organizationUrl>https://springdoc.github.io/springdoc-openapi/
			</organizationUrl>
		</developer>
	</developers>

	<scm>
		<url>**************:springdoc/springdoc-openapi.git</url>
		<connection>scm:git:**************:springdoc/springdoc-openapi.git</connection>
		<developerConnection>scm:git:**************:springdoc/springdoc-openapi.git
		</developerConnection>
		<tag>v1.7.0</tag>
	</scm>
	<distributionManagement>
		<snapshotRepository>
			<id>ossrh</id>
			<url>https://s01.oss.sonatype.org/content/repositories/snapshots</url>
		</snapshotRepository>
		<repository>
			<id>ossrh</id>
			<url>https://s01.oss.sonatype.org/service/local/staging/deploy/maven2/</url>
		</repository>
	</distributionManagement>

	<modules>
		<module>springdoc-openapi-common</module>
		<module>springdoc-openapi-webmvc-core</module>
		<module>springdoc-openapi-webflux-core</module>
		<module>springdoc-openapi-kotlin</module>
		<module>springdoc-openapi-hateoas</module>
		<module>springdoc-openapi-data-rest</module>
		<module>springdoc-openapi-security</module>
		<module>springdoc-openapi-groovy</module>
		<module>springdoc-openapi-ui</module>
		<module>springdoc-openapi-webflux-ui</module>
		<module>springdoc-openapi-javadoc</module>
	</modules>

	<properties>
		<maven-gpg-plugin.version>1.6</maven-gpg-plugin.version>
		<maven-release-plugin.version>2.5.3</maven-release-plugin.version>
		<nexus-staging-maven-plugin>1.6.8</nexus-staging-maven-plugin>
		<swagger-api.version>2.2.9</swagger-api.version>
		<swagger-ui.version>4.18.2</swagger-ui.version>
		<spring-security-oauth2.version>2.5.2.RELEASE</spring-security-oauth2.version>
		<gmavenplus-plugin.version>1.8.1</gmavenplus-plugin.version>
		<jaxb-impl.version>2.1</jaxb-impl.version>
		<javax.jws-api.version>1.1</javax.jws-api.version>
		<jjwt.version>0.9.1</jjwt.version>
		<therapi-runtime-javadoc.version>0.15.0</therapi-runtime-javadoc.version>
		<spring-cloud-function.version>3.2.4</spring-cloud-function.version>
		<dependency-check.version>8.2.1</dependency-check.version>
		<spring-security-oauth2-authorization-server.version>0.4.1</spring-security-oauth2-authorization-server.version>
	</properties>

	<dependencyManagement>
		<dependencies>
			<!-- swagger dependencies -->
			<dependency>
				<groupId>io.swagger.core.v3</groupId>
				<artifactId>swagger-core</artifactId>
				<version>${swagger-api.version}</version>
			</dependency>
			<!-- swagger ui -->
			<dependency>
				<groupId>org.webjars</groupId>
				<artifactId>swagger-ui</artifactId>
				<version>${swagger-ui.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.security.oauth</groupId>
				<artifactId>spring-security-oauth2</artifactId>
				<version>${spring-security-oauth2.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.security</groupId>
				<artifactId>spring-security-oauth2-authorization-server</artifactId>
				<version>${spring-security-oauth2-authorization-server.version}</version>
			</dependency>
			<dependency>
				<groupId>javax.xml</groupId>
				<artifactId>jaxb-impl</artifactId>
				<version>${jaxb-impl.version}</version>
			</dependency>
			<dependency>
				<groupId>javax.jws</groupId>
				<artifactId>javax.jws-api</artifactId>
				<version>${javax.jws-api.version}</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>io.jsonwebtoken</groupId>
				<artifactId>jjwt</artifactId>
				<version>${jjwt.version}</version>
				<scope>test</scope>
			</dependency>
			<!-- spring Cloud -->
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-function-web</artifactId>
				<version>${spring-cloud-function.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-starter-function-web</artifactId>
				<version>${spring-cloud-function.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-starter-function-webflux</artifactId>
				<version>${spring-cloud-function.version}</version>
			</dependency>
			<!-- SpringDoc -->
			<dependency>
				<groupId>org.springdoc</groupId>
				<artifactId>springdoc-openapi-common</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springdoc</groupId>
				<artifactId>springdoc-openapi-webmvc-core</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springdoc</groupId>
				<artifactId>springdoc-openapi-webflux-core</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springdoc</groupId>
				<artifactId>springdoc-openapi-kotlin</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springdoc</groupId>
				<artifactId>springdoc-openapi-hateoas</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springdoc</groupId>
				<artifactId>springdoc-openapi-data-rest</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springdoc</groupId>
				<artifactId>springdoc-openapi-security</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springdoc</groupId>
				<artifactId>springdoc-openapi-groovy</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springdoc</groupId>
				<artifactId>springdoc-openapi-ui</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springdoc</groupId>
				<artifactId>springdoc-openapi-webflux-ui</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springdoc</groupId>
				<artifactId>springdoc-openapi-javadoc</artifactId>
				<version>${project.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>
	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>org.junit.vintage</groupId>
					<artifactId>junit-vintage-engine</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
	</dependencies>
	<build>
		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.codehaus.gmavenplus</groupId>
					<artifactId>gmavenplus-plugin</artifactId>
					<version>${gmavenplus-plugin.version}</version>
				</plugin>
			</plugins>
		</pluginManagement>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-resources</id>
						<phase>validate</phase>
						<goals>
							<goal>copy-resources</goal>
						</goals>
						<configuration>
							<outputDirectory>${basedir}/target/classes/META-INF</outputDirectory>
							<resources>
								<resource>
									<directory>${basedir}/..</directory>
									<includes>
										<include>LICENSE</include>
										<include>COPYRIGHT</include>
									</includes>
								</resource>
							</resources>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.owasp</groupId>
				<artifactId>dependency-check-maven</artifactId>
				<version>${dependency-check.version}</version>
				<configuration>
					<skipSystemScope>true</skipSystemScope>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>check</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

	<profiles>
		<profile>
			<id>gpg</id>
			<build>
				<plugins>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-source-plugin</artifactId>
						<executions>
							<execution>
								<id>attach-sources</id>
								<goals>
									<goal>jar</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-javadoc-plugin</artifactId>
						<executions>
							<execution>
								<id>attach-javadocs</id>
								<goals>
									<goal>jar</goal>
								</goals>
							</execution>
						</executions>
						<configuration>
							<source>8</source>
						</configuration>
					</plugin>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-gpg-plugin</artifactId>
						<version>${maven-gpg-plugin.version}</version>
						<executions>
							<execution>
								<id>sign-artifacts</id>
								<phase>verify</phase>
								<goals>
									<goal>sign</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
					<plugin>
						<groupId>org.sonatype.plugins</groupId>
						<artifactId>nexus-staging-maven-plugin</artifactId>
						<version>${nexus-staging-maven-plugin}</version>
						<extensions>true</extensions>
						<configuration>
							<serverId>ossrh</serverId>
							<nexusUrl>https://s01.oss.sonatype.org/</nexusUrl>
							<autoReleaseAfterClose>true</autoReleaseAfterClose>
						</configuration>
						<dependencies>
							<dependency>
								<groupId>com.thoughtworks.xstream</groupId>
								<artifactId>xstream</artifactId>
								<version>1.4.15</version>
							</dependency>
						</dependencies>
					</plugin>
					<plugin>
						<artifactId>maven-release-plugin</artifactId>
						<version>${maven-release-plugin.version}</version>
						<configuration>
							<tagNameFormat>v@{project.version}</tagNameFormat>
							<autoVersionSubmodules>true</autoVersionSubmodules>
							<useReleaseProfile>false</useReleaseProfile>
							<releaseProfiles>gpg</releaseProfiles>
							<goals>deploy</goals>
						</configuration>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>
	<repositories>
		<repository>
			<id>central</id>
			<url>https://repo.maven.apache.org/maven2</url>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>
		<repository>
			<id>spring-release</id>
			<name>Spring release</name>
			<url>https://repo.spring.io/release</url>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>
		<repository>
			<id>spring-snapshot</id>
			<name>Spring Snapshots</name>
			<url>https://repo.spring.io/snapshot</url>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
		<repository>
			<id>spring-milestone</id>
			<name>Spring Milestone</name>
			<url>https://repo.spring.io/milestone</url>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>
	</repositories>
	<pluginRepositories>
		<pluginRepository>
			<id>spring-releases</id>
			<name>Spring Releases</name>
			<url>https://repo.spring.io/release</url>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</pluginRepository>
	</pluginRepositories>
</project>
