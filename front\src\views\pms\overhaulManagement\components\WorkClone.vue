<script setup lang="ts">
import { BasicCard, OrionTable } from 'lyra-component-vue3';
import {
  h, reactive, ref, Ref,
} from 'vue';
import Api from '/@/api';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

const props = defineProps<{
  jobDetails: Record<string, any>
}>();

const cloneInfo = reactive({
  list: [
    {
      label: '工单号',
      field: 'number',
    },
    {
      label: '作业名称',
      field: 'name',
    },
    {
      label: '是否高风险',
      field: 'isHighRisk',
      isBoolean: true,
    },
    {
      label: '作业负责人',
      field: 'rspUserName',
    },
    {
      label: '监督人员',
      field: 'supervisoryStaffName',
    },
    {
      label: '管理人员',
      field: 'managePersonName',
    },
    {
      label: '责任中心',
      field: 'rspDeptName',
    },
    {
      label: '作业部门',
      field: 'jobDeptName',
    },
    {
      label: '作业基地',
      field: 'jobBaseName',
    },
    {
      label: '工作地址',
      field: 'workPlace',
    },
    {
      label: '计划开工时间',
      field: 'beginTime',
      formatTime: 'YYYY-MM-DD',
    },
  ],
  dataSource: props.jobDetails,
});

const tableRef: Ref = ref();
const selectedKeys: Ref<string[]> = ref([]);
const tableOptions = {
  showToolButton: false,
  showTableSetting: false,
  isSpacing: false,
  resizeHeightOffset: 70,
  smallSearchField: ['name', 'number'],
  rowSelection: {
    onChange(keys: string[]) {
      selectedKeys.value = keys || [];
    },
  },
  api: (params: object) => new Api('/pms/job-manage/target/page').fetch({
    ...params,
    query: {
      repairRound: props.jobDetails?.repairRound,
      sourceId: props.jobDetails?.id,
    },
  }, '', 'POST'),
  columns: [
    {
      title: '作业名称',
      dataIndex: 'name',
    },
    {
      title: '工单号',
      dataIndex: 'number',
      width: 120,
    },
    {
      title: '作业负责人',
      dataIndex: 'rspUserName',
      width: 90,
    },
    {
      title: '负责人所在中心',
      dataIndex: 'rspDeptName',
      width: 120,
    },
    {
      title: '是否重大项目',
      dataIndex: 'isMajorProject',
      width: 100,
      customRender({ text }) {
        return text ? '是' : text === false ? '否' : '';
      },
    },
    {
      title: '计划开始日期',
      dataIndex: 'beginTime',
      width: 100,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '计划结束日期',
      dataIndex: 'endTime',
      width: 100,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '计划工期',
      dataIndex: 'workDuration',
      width: 80,
    },
  ],
};

defineExpose({
  async submit() {
    return new Promise((resolve, reject) => {
      if (selectedKeys.value.length === 0) {
        message.info('请勾选克隆目标数据！');
        return reject();
      }
      new Api('/pms/job-manage/copy').fetch({
        sourceId: props?.jobDetails?.id,
        targetIdList: selectedKeys.value,
      }, '', 'POST').then(() => {
        message.success('操作成功');
        resolve('');
      }).catch((e) => {
        reject(e);
      });
    });
  },
});
</script>

<template>
  <div
    style="padding: 1px"
  >
    <BasicCard
      title="克隆源"
      :isBorder="false"
      :isContentSpacing="false"
      :grid-content-props="cloneInfo"
    />
    <BasicCard
      title="克隆目标"
      :isBorder="false"
      :isContentSpacing="false"
    >
      <OrionTable
        ref="tableRef"
        :options="tableOptions"
      />
    </BasicCard>
  </div>
</template>

<style scoped lang="less">

</style>
