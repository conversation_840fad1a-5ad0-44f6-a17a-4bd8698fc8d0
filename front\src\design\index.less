@import 'function.less';
//@import 'common.less';
@import 'var/index.less';
@import 'transition/index.less';
@import 'public.less';
@import 'component.less';


input:-webkit-autofill {
  box-shadow: 0 0 0 1000px white inset !important;
}

:-webkit-autofill {
  transition: background-color 5000s ease-in-out 0s !important;
}

html {
  overflow: hidden;
  text-size-adjust: 100%;
}

html,
body {
  width: 100%;
  height: 100%;
  overflow: hidden;

  &.color-weak {
    filter: invert(80%);
  }

  &.gray-mode {
    filter: grayscale(100%);
    filter: progid:dximagetransform.microsoft.basicimage(grayscale=1);
  }
}


a:focus,
a:active,
button,
div,
svg,
span {
  outline: none;
}
