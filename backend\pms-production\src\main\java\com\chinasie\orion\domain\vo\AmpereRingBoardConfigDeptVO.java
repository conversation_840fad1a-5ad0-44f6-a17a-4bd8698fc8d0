package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 21 日
 * 安质环看板部门维护对象
 **/
@Data
@ApiModel(value = "AmpereRingBoardConfigDeptVO 对象",description = "安质环看板部门维护对象")
public class AmpereRingBoardConfigDeptVO extends ObjectVO implements Serializable {
    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /**
     * 部门code
     */
    @ApiModelProperty(value = "部门code")
    private String deptCode;

    /**
     * 部门编码
     */
    @ApiModelProperty(value = "部门编码")
    private String deptNumber;

    /**
     * 部门绩效考核是否展示
     */
    @ApiModelProperty(value = "部门绩效考核是否展示")
    @TableField(value = "is_dept_score_show")
    private Boolean isDeptScoreShow;

    /**
     * 隐患排查是否展示
     */
    @ApiModelProperty(value = "隐患排查是否展示")
    @TableField(value = "is_check_problems_show")
    private Boolean isCheckProblemsShow;


    /**
     * 维护类型
     */
    @ApiModelProperty(value = "维护类型")
    @NotEmpty(message = "维护类型不能为空")
    private String configType;

    /**
     * 隐患排除的部门序号
     */
    @ApiModelProperty(value = "隐患排查的部门序号")
    private Integer checkProblemsSort;

    /**
     * 标准得分
     */
    @ApiModelProperty(value = "部门标准分")
    private Double standardScore;


}
