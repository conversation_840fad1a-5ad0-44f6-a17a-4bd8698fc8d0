<script setup lang="ts">
import {
  computed, ComputedRef, inject, onMounted, ref, Ref,
} from 'vue';
import { OrionTable } from 'lyra-component-vue3';
import { getColumns } from './tableColumns';
import Api from '/@/api';
const props = defineProps<{
  node: Record<string, any>
}>();

const emits = defineEmits<{(e: 'selectTable', selectedRows: Record<string, any>[]): void,
  (e:'update'):void
}>();

const attrs: Record<string, any> = inject('attrs', {});
const tableRef: Ref = ref();
const treeData: Ref<any[]> = inject('treeData', ref([]));
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const selectedKeys: ComputedRef<string[]> = computed(() => selectedRows.value.map((item) => item.id));
const { selectType } = attrs;
const tableOptions: Ref = ref({
  rowSelection: {
    type: selectType,
    selectedRowKeys: selectedKeys,
    onSelect: selectRow,
    onSelectAll(selected:boolean, rows:any[], changeRows:any[]) {
      if (selected) {
        selectedRows.value = selectedRows.value.concat(changeRows);
      } else {
        selectedRows.value = selectedRows.value.filter((item) => changeRows.every((v) => v.id !== item.id));
      }
      emits('selectTable', selectedRows.value);
    },
  },
  indexColumnProps: {
    width: 80,
  },
  showTableSetting: false,
  showToolButton: false,
  immediate: false,
  api: (params: Record<string, any>) => defaultTableApi(params),
  columns: attrs.columns || getColumns(),
});

onMounted(() => {
  emits('update');
});

function defaultTableApi(params: any) {
  if (!attrs.tableApi) {
    delete params.selectedKeys;
    return new Api(props.node?.address).fetch(params, '', 'POST');
  }
  return attrs.tableApi({
    ...params,
    node: props.node,
    tableMethods: tableRef.value,
  });
}

function updateTable() {
  tableRef.value.reload();
}

function customRow(record: Record<string, any>) {
  return {
    onClick: () => {
      selectRow(record);
    },
  };
}

// 自定义选择逻辑
function selectRow(record: Record<string, any>) {
  const rows = [...selectedRows.value];
  const index = rows.map((item: Record<string, any>) => item.id).indexOf(record.id);
  if (index >= 0) {
    rows.splice(index, 1);
  } else if (selectType === 'radio') {
    rows.splice(0, 1, record);
  } else {
    rows.push(record);
  }
  selectedRows.value = rows;
  emits('selectTable', rows);
}
// 设置选中项
function setSelectedRows(data: Record<string, any>[]) {
  selectedRows.value = data;
}

defineExpose({
  updateTable,
  setSelectedRows,
});
</script>

<template>
  <div class="table-wrap">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      :customRow="customRow"
    />
  </div>
</template>

<style scoped lang="less">
.table-wrap {
  height: 100%;
  overflow: hidden;
}

:deep(.ant-input) {
  height: 32px;
  line-height: 32px;
}
</style>
