package com.chinasie.orion.conts;

public enum ProjectLifeCycleEnum {
    START_END_NODE("START_END_NODE", "开始结束"),
    NORMAL_NODE("NORMAL_NODE", "常用节点"),
    FINISHED("FINISHED", "已完成"),
    UNDERWAY("UNDERWAY", "进行中"),
    NOT_START("NOT_START", "未开始"),
    ;

    private String code;

    private String description;

    ProjectLifeCycleEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
