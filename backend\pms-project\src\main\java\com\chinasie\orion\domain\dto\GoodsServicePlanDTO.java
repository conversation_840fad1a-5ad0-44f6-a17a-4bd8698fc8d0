package com.chinasie.orion.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * GoodsServicePlan Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-25 16:26:18
 */
@ApiModel(value = "GoodsServicePlanDTO对象", description = "物资/服务计划表")
@Data
public class GoodsServicePlanDTO extends ObjectDTO implements Serializable{

    /**
     * 物资服务计划编号
     */
    @ApiModelProperty(value = "物资服务计划编号")
    private String number;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;


    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @NotEmpty(message = "项目ID不能为空")
    private String projectId;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @NotEmpty(message = "描述不能为空")
    private String description;

    /**
     * 类型对应的字典编码
     */
    @ApiModelProperty(value = "类型对应的字典编码")
    @NotEmpty(message = "类型不能为空")
    private String typeCode;

    /**
     * 物资服务编码
     */
    @ApiModelProperty(value = "物资服务编码")
    @NotEmpty(message = "物资服务编码不能为空")
    private String goodsServiceNumber;

    /**
     * 规格型号
     */
    @ApiModelProperty(value = "规格型号")
    private String normsModel;

    /**
     * 服务期限
     */
    @ApiModelProperty(value = "服务期限")
    private Integer serviceTerm;

    /**
     * 计量单位对应数据字典
     */
    @ApiModelProperty(value = "计量单位对应数据字典")
    @NotEmpty(message = "计量单位不能为空")
    private String unitCode;

    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    private BigDecimal demandAmount;

    /**
     * 总入库数量
     */
    @ApiModelProperty(value = "总入库数量")
    private BigDecimal totalStoreAmount;

    /**
     * 需求时间
     */
    @ApiModelProperty(value = "需求时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date demandTime;

    /**
     * 采购计划编号
     */
    @ApiModelProperty(value = "采购计划编号")
    private String buyPlanId;

    /**
     * 需求人ID
     */
    @ApiModelProperty(value = "需求人ID")
    private String demandPersonId;

}
