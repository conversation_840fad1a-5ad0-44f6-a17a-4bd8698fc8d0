<template>
  <BasicDrawer
    v-model:visible="visibleShow"
    :title="titleMap[editTypes]"
    placement="right"
    :maskClosable="false"
    :closable="!isCreate"
    :width="1000"
    :show-footer="true"
    @register="modalRegister"
    @visibleChange="visibleChange"
    @ok="handleSubmit"
    @cancel="handleClose"
    @close="handleCloseAll"
  >
    <Spin
      :delay="300"
      :spinning="spinning"
    >
      <BasicForm
        @register="registerForm"
      />
    </Spin>
  </BasicDrawer>
</template>
<script lang="ts" setup>
import {
  computed, defineEmits, inject, onMounted, reactive, ref, unref,
} from 'vue';
import {
  BasicDrawer, BasicForm, useDrawerInner, useForm,
} from 'lyra-component-vue3';
import { message, Spin } from 'ant-design-vue';
import { map as _map, get as _get } from 'lodash-es';
import dayjs from 'dayjs';
import Api from '/@/api';
import { getProjectUserList, getUserInfoByCode } from '/@/views/pms/projectLaborer/projectLab/api';
import { Ref } from 'vue/dist/vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
  editType: {
    type: String,
    default: 'edit',
  },
});
const emit = defineEmits([
  'close',
  'update',
  'closeUpdate',
]);
const dataObj = ref<any>();
const isCreate = ref(false);
const editTypes = ref();
const relationList = ref(['']);
const values = ref([]);
const formRef = ref(null);
const deptList = ref([]);
const projectId = inject('projectId') as string;
const spinning: Ref<boolean> = ref(false);
const visibleShow = ref(props.visible);

const titleMap = {
  edit: '编辑计划',
  apply: '计划调整申请',
};

const userList = ref([]);

function visibleChange(val) {
  if (!val) {
    changeOkLoading(false);
  }
}

const [modalRegister, { closeDrawer, changeOkLoading }] = useDrawerInner(
  (drawerData) => {
    spinning.value = true;
    setTimeout(() => {
      isCreate.value = drawerData.isCreate;
      dataObj.value = drawerData.data;
      editTypes.value = drawerData.editType;
      setFieldsValue(drawerData.data);
      reqProjectUserList();
      // getUserList(drawerData.data.id);
      getDeptList();
      getParentData(drawerData.id || drawerData.data.id, drawerData.isWork);
    }, 5);
  },
);

// 获取项目成员列表
const projectUserList: Ref = ref([]);

async function reqProjectUserList() {
  const result = await getProjectUserList(unref(projectId));
  projectUserList.value = result && result.map((item) => ({
    ...item,
    label: item.name,
    value: item.id,
  }));
}

const handleClose = () => {
  emit('close');
  closeDrawer();
};

const handleCloseAll = async () => {
  if (isCreate.value) {
    emit('closeUpdate', dataObj.value);
  }
};

const [
  registerForm,
  {
    setFieldsValue, validate, getFieldsValue, updateSchema, clearValidate,
  },
] = useForm({
  actionColOptions: {
    span: 24,
  },
  layout: 'vertical',
  showActionButtonGroup: false,
  schemas: [
    {
      field: 'parent',
      component: 'Input',
      label: '计划父级',
      colProps: {
        span: 12,
      },
      componentProps: {
        placeholder: '请输入计划父级',
        maxlength: 100,
        disabled: true,
      },
    },
    {
      field: 'name',
      component: 'Input',
      label: '计划名称',
      colProps: {
        span: 12,
      },
      componentProps: {
        placeholder: '请输入名称',
        maxlength: 100,
      },
      required: true,
    },
    {
      field: 'rspUser',
      component: 'Select',
      label: '责任人',
      colProps: {
        span: 12,
      },
      componentProps: {
        showSearch: true,
        onSelect(value, option) {
          dataObj.value = {
            ...dataObj.value,
            rspSectionId: option.sectionId || '',
            rspSubDept: option.deptId || '',
          };
          setFieldsValue({
            rspSubDept: option.deptId || '',
            rspSubDeptName: option.deptName || '',
            rspSectionId: option.sectionId || '',
            rspSectionName: option.sectionName || '',
            rspUserCode: option.code || '',
          });
        },
        options: computed(() => projectUserList.value),
        filterOption: (input: string, option: any) => option.label.toLowerCase().indexOf(input.toLowerCase()) !== -1,
      },
    },
    {
      field: 'rspSubDeptName',
      component: 'Input',
      label: '责任部门',
      required: true,
      colProps: {
        span: 12,
      },
      componentProps: {
        disabled: true,
      },
    },
    {
      field: 'rspSectionName',
      component: 'Input',
      label: '责任科室',
      colProps: {
        span: 12,
      },
      componentProps: {
        disabled: true,
      },
    },
    {
      field: 'rspUserCode',
      component: 'Input',
      label: '员工编号',
      colProps: {
        span: 12,
      },
      required: true,
      componentProps: {
        async onBlur(e) {
          if (!e.target.value) return;
          const result = await getUserInfoByCode(unref(projectId), e.target.value);
          let option = result || { code: e.target.value };
          dataObj.value = {
            ...dataObj.value,
            rspSectionId: option.sectionId || '',
            rspSubDept: option.deptId || '',
          };
          await setFieldsValue({
            rspUser: option.id || '',
            rspSubDept: option.deptId || '',
            rspSubDeptName: option.deptName || '',
            rspSectionId: option.sectionId || '',
            rspSectionName: option.sectionName || '',
            rspUserCode: option.code || '',
          });
        },
      },
    },
    {
      field: 'participantUserList',
      component: 'Select',
      label: '参与人',
      colProps: {
        span: 12,
      },
      componentProps: {
        showSearch: true,
        mode: 'multiple',
        options: computed(() => projectUserList.value),
        filterOption: (input: string, option: any) => option.label.toLowerCase().indexOf(input.toLowerCase()) !== -1,
      },
    },
    {
      field: 'isWork',
      component: 'Select',
      label: '是否作业',
      colProps: {
        span: 12,
      },
      rules: [
        {
          type: 'boolean',
          required: true,
        },
      ],
      componentProps({ formModel }) {
        return {
          options: [
            {
              label: '是',
              value: true,
            },
            {
              label: '否',
              value: false,
            },
          ],
          onChange(val) {
            if (val === 0) {
              formModel.enforceType = null;
              formModel.enforceBasePlace = null;
              formModel.enforceBasePlaceName = null;
              formModel.enforceScope = null;
              formModel.workContent = null;
              formModel.repairRound = null;
              // formModel.isCarryMaterials = null;
            }

            const updateProps = [
              'enforceType',
              'enforceBasePlace',
              'enforceScope',
              'workContent',
            ];
            updateSchema(_map(updateProps, (propKey) => ({
              field: propKey,
              ...({
                required: val,
              }),
            })));
            clearValidate(updateProps);
          },
        };
      },
    },
    {
      field: 'enforceType',
      component: 'SelectDictVal',
      label: '实施类型',
      colProps: {
        span: 12,
      },
      componentProps({ formModel }) {
        return {
          dictNumber: 'pms_enforce_Type',
          disabled: formModel?.isWork === false,
          onChange(value: string) {
            let updateProps = {};
            if (value === 'pms_not_dc_job' || value === 'pms_overseas_job') {
              formModel.enforceScope = null;
              formModel.workContent = null;
              formModel.repairRound = null;
              // formModel.isCarryMaterials = null;
              if (value === 'pms_overseas_job') {
                formModel.enforceBasePlace = null;
              }
            }
            if (value === 'pms_overseas_job') {
              updateProps = {
                enforceType: true,
                enforceBasePlace: false,
                enforceScope: false,
                workContent: false,
                repairRound: false,
                // isCarryMaterials: false,
              };
            } else if (value === 'pms_not_dc_job') {
              updateProps = {
                enforceType: true,
                enforceBasePlace: true,
                enforceScope: false,
                workContent: false,
                repairRound: false,
                // isCarryMaterials: false,
              };
            } else if (value === 'pms_dc_job' && formModel.workContent === 'pms_major_repair_con') {
              updateProps = {
                enforceType: true,
                enforceBasePlace: true,
                enforceScope: true,
                workContent: true,
                repairRound: false,
                isCarryMaterials: false,
              };
            } else {
              updateProps = {
                enforceType: true,
                enforceBasePlace: true,
                enforceScope: true,
                workContent: true,
                repairRound: false,
                isCarryMaterials: false,
              };
            }
            updateSchema(_map(Object.keys(updateProps), (propKey) => ({
              field: propKey,
              ...({
                required: _get(updateProps, propKey),
              }),
            })));
          },
        };
      },
    },
    {
      field: 'enforceBasePlace',
      component: 'ApiSelect',
      label: '实施地点',
      colProps: {
        span: 12,
      },
      componentProps({ formModel }) {
        return {
          disabled: formModel?.enforceType === 'pms_overseas_job' || formModel?.isWork === false,
          api: () => new Api('/pms/base-place/list').fetch('', '', 'POST'),
          labelField: formModel?.enforceType === 'pms_not_dc_job' ? 'city' : 'name',
          valueField: 'code',
          onChange(value: string, option: { label: string }) {
            formModel.enforceBasePlaceName = option?.label;
            formModel.enforceBasePlace = value;
            formModel.repairRound = null;
          },
        };
      },
    },
    {
      field: 'enforceScope',
      component: 'SelectDictVal',
      label: '实施区域',
      colProps: {
        span: 12,
      },
      componentProps({ formModel }) {
        return {
          disabled: formModel?.enforceType === 'pms_overseas_job' || formModel?.enforceType === 'pms_not_dc_job' || formModel?.isWork === false,
          dictNumber: 'pms_enforce_scope',
        };
      },
    },
    {
      field: 'workContent',
      component: 'SelectDictVal',
      label: '工作内容',
      colProps: {
        span: 12,
      },
      componentProps({ formModel }) {
        return {
          disabled: formModel?.enforceType === 'pms_overseas_job' || formModel?.enforceType === 'pms_not_dc_job' || formModel?.isWork === false,
          dictNumber: 'pms_work_content',
          onChange(value: string) {
            if (value !== 'pms_major_repair_con') {
              formModel.repairRound = null;
            }
            updateSchema({
              field: 'repairRound',
              required: value === 'pms_major_repair_con',
            });
          },
        };
      },
    },
    {
      field: 'repairRound',
      component: 'ApiSelect',
      label: '大修轮次',
      colProps: {
        span: 12,
      },
      componentProps({ formModel }) {
        return {
          disabled: formModel?.workContent !== 'pms_major_repair_con' || formModel?.enforceType === 'pms_overseas_job' || formModel?.enforceType === 'pms_not_dc_job' || formModel?.isWork === false,
          fieldNames: {
            label: 'repairRound',
            value: 'repairRound',
          },
          api: () => new Api('/pms/major-repair-plan/list').fetch({
            baseCode: formModel?.enforceBasePlace,
            isFinish: false,
          }, '', 'POST'),
        };
      },
    },
    {
      field: 'beginTime',
      component: 'DatePicker',
      label: '计划开始时间',
      required: true,
      colProps: {
        span: 12,
      },
      componentProps: {
        valueFormat: 'YYYY-MM-DD',
        placeholder: '请选择计划开始时间',
        // disabledDate: (current) =>
        // let endTime = getFieldsValue().endTime || '';
        // if (endTime) {
        //   let startTime = dayjs(current).format('YYYY-MM-DD');
        //   let limitEndTime = dayjs(endTime).format('YYYY-MM-DD');
        //   return !(Date.parse(limitEndTime) >= Date.parse(startTime));
        // }
        //   false,
        style: { width: '100%' },
        onChange(value: string) {
          setFieldsValue({
            endTime: value,
          });
        },
      },
    },
    {
      field: 'endTime',
      component: 'DatePicker',
      label: '计划完成时间',
      required: true,
      colProps: {
        span: 12,
      },
      componentProps: {
        placeholder: '请选择计划完成时间',
        style: { width: '100%' },
        valueFormat: 'YYYY-MM-DD',
        disabledDate: (current) => {
          let beginTime = getFieldsValue().beginTime || '';
          if (beginTime) {
            let startTime = dayjs(beginTime).format('YYYY-MM-DD');
            let endTime = dayjs(current).format('YYYY-MM-DD');
            return !(Date.parse(endTime) >= Date.parse(startTime));
          }
          return false;
        },
      },
    },
    // {
    //   field: 'processFlag',
    //   component: 'Select',
    //   label: '是否关联流程:',
    //   colProps: { span: 12 },
    //   componentProps: {
    //     options: [
    //       {
    //         label: '是',
    //         value: true,
    //       },
    //       {
    //         label: '否',
    //         value: false,
    //       },
    //     ],
    //   },
    // },
    {
      field: 'remark',
      component: 'InputTextArea',
      label: '描述',
      colProps: {
        span: 24,
      },
      componentProps: {
        placeholder: '请输入内容',
        maxlength: 500,
        style: { height: '130px' },
      },
    },
  ],
});

const form = reactive({
  parent: dataObj.value?.parentName,
  name: dataObj.value?.name,
  rspSubDept: dataObj.value?.rspSubDept,
  rspUser: dataObj.value?.rspUser,
  beginTime: dataObj.value?.beginTime,
  endTime: dataObj.value?.endTime,
  schemeDesc: dataObj.value?.schemeDesc,
});

// 获取信息
async function getParentData(id: string, isWork: boolean) {
  spinning.value = true;

  try {
    const res = await new Api(`/pms/projectScheme/${id}`).fetch('', '', 'GET');
    if (!res) throw new Error('未收到有效响应');

    const parentName = res.level === 1 ? '' : res.parentName || res.name;
    dataObj.value = res;
    form.parent = parentName;
    form.name = res.name;
    form.rspSubDept = res.rspSubDept;
    form.rspUser = res.rspUser;
    form.beginTime = res.beginTime;
    form.endTime = res.endTime;
    form.schemeDesc = res.schemeDesc;
    await setFieldsValue({
      parent: parentName,
      name: res.name,
      isWork: isWork ? true : (res.isWork === 1),
    });

    const updateProps = [
      'enforceType',
      'enforceBasePlace',
      'enforceScope',
      'workContent',
    ];
    updateSchema(_map(updateProps, (propKey) => ({
      field: propKey,
      required: isWork ? true : (res.isWork === 1),
    })));

    updateSchema({
      field: 'repairRound',
      required: res.workContent === 'pms_major_repair_con',
    });

    clearValidate(updateProps);
    spinning.value = false;
  } catch (error) {
    console.error('获取父级数据失败:', error);
    spinning.value = false;
  }
}

// 获取部门列表
async function getDeptList() {
  const res = await new Api(
    `/pms/project-role-user/dept/list?projectId=${unref(projectId)}`,
  ).fetch({ projectId: unref(projectId) }, '', 'post');
  if (res && res.length > 0) {
    deptList.value = res.map((item) => ({
      value: item.id,
      label: item.name,
    }));
  } else {
    deptList.value = [];
  }
}

// 获取负责人列表
async function getUserList(id) {
  const res = await new Api('/pms/project-role-user/dept/user/list').fetch(
    {
      projectId: unref(projectId),
      deptId: id,
    },
    '',
    'get',
  );
  if (res) {
    userList.value = res.map((item) => ({
      value: item.id,
      label: item.name,
    }));
  } else {
    userList.value = [];
  }
}

const handleSubmit = async () => {
  const res = await validate();
  if (res) {
    changeOkLoading(true);
    const params = {
      id: dataObj.value?.id,
      rspSubDept: dataObj.value?.rspSubDept,
      ...res,
      isWork: res.isWork ? 1 : 0,
      beginTime: dayjs(res.beginTime).format('YYYY-MM-DD'),
      endTime: dayjs(res.endTime).format('YYYY-MM-DD'),
    };
    try {
      // modify 变更接口
      // edit  编辑接口
      const editRes = await new Api('/pms/projectScheme/edit').fetch(params, '', 'put');
      if (editRes) {
        emit('update');
        if (editRes.message) {
          message.success(editRes.message);
        } else {
          message.success('编辑成功');
        }
      } else {
        message.error('编辑失败');
      }
    } finally {
      handleClose();
      changeOkLoading(false);
    }
  }
};

</script>

<style lang="less" scoped>

</style>
