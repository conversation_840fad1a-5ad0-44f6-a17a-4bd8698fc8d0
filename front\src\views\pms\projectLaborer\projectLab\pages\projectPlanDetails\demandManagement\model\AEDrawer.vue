<template>
  <BasicDrawer
    v-bind="$attrs"
    :width="500"
    showFooter
    :showCancelBtn="false"
    :showOkBtn="false"
    wrap-class-name="addTreeNode"
    @register="register"
    @close="cancelClick('close')"
  >
    <BasicForm
      class="oobbj"
      :layout="'vertical'"
      @register="registerForm"
    />
    <template #footer>
      <DrawerFooterButtons
        v-model:checked="checked"
        :isContinue="cloneData[0]==='add'"
        :loading="loading"
        @cancelClick="cancelClick('close')"
        @okClick="okClick"
      />
    </template>
  </BasicDrawer>
</template>
<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
} from 'vue';
import {
  BasicDrawer, useDrawerInner, BasicForm, FormSchema, useForm,
} from 'lyra-component-vue3';
import { Checkbox, Button, message } from 'ant-design-vue';
import Api from '/@/api';
import DrawerFooterButtons from '/@/views/pms/components/DrawerFooterButtons.vue';
export default defineComponent({
  name: '',
  components: {
    BasicDrawer,
    BasicForm,
    ACheckBox: Checkbox,
    AButton: Button,
    DrawerFooterButtons,
  },
  emits: ['addSuccess'],
  setup(_, { emit }) {
    const state: any = reactive({
      cloneData: [],
      checked: undefined,
      loading: false,
      parentId: '',
    });
    function resetData() {
      state.cloneData = [];
      state.checked = undefined;
      state.loading = false;
    }
    const schemas: FormSchema[] = [
      {
        field: 'name',
        component: 'Input',
        label: '名称:',
        colProps: {
          span: 24,
        },
        rules: [
          {
            required: true,
            trigger: 'blur',
          },
          {
            min: 1,
            max: 64,
            message: '名称1-64位',
            trigger: 'blur',
          },
        ],
        componentProps: {
          maxlength: 64,
        },
      },
      {
        field: 'remark',
        component: 'InputTextArea',
        label: '描述:',
        colProps: {
          span: 24,
        },
        componentProps: {
          rows: 4,
          showCount: true,
          maxlength: 500,
        },
      },
    ];
    const [registerForm, F1] = useForm({
      schemas,
      showSubmitButton: false,
      showResetButton: false,
    });
    const [register, DrawerM] = useDrawerInner((data) => {
      state.cloneData = JSON.parse(JSON.stringify(data));
      if (state.cloneData[0] === 'edit') {
        // if (state.cloneData[1].fileToolList?.length > 0) {
        //   state.cloneData[1].fileToolList = state.cloneData[1].fileToolList.map((item) => item.id);
        // }
        F1.setFieldsValue(state.cloneData[1].data);
      }
    });

    /**
     * @description: 提交确定
     * */
    async function okClick() {
      await F1.validate();
      state.loading = true;
      const fd = F1.getFieldsValue();
      console.log('----- fd -----', fd);
      // 如果格式不对,此处可以对请求参数进行拷贝并处理
      const params:any = JSON.parse(JSON.stringify(fd));
      if (state.cloneData[0] === 'add') {
        if (state.cloneData[1].head) {
          params.parentId = 0;
        } else {
          params.parentId = state.cloneData[1].data.id;
        }
      }
      if (state.cloneData[0] === 'add') {
        new Api('/pas/demand-dir').fetch(params, '', 'POST').then(() => {
          message.success('操作成功');
          cancelClick('ok');
          emit('addSuccess');
        }).catch(() => {
          state.loading = false;
        });
      } else {
        // 如果格式不对或需要添加某些id,此处可以对请求参数进行处理
        params.id = state.cloneData[1].data.id;
        new Api('/pas/demand-dir').fetch(params, '', 'PUT').then(() => {
          message.success('操作成功');
          cancelClick('ok');
          emit('addSuccess');
        }).catch(() => {
          state.loading = false;
        });
      }
    }

    /**
     * @description: 取消
     * */
    function cancelClick(type) {
      state.loading = false;
      if (type === 'close') {
        resetData();
        F1.resetFields();
        DrawerM.closeDrawer();
      } else if (!state.checked) {
        F1.resetFields();
        DrawerM.closeDrawer();
      } else {
        F1.resetFields();
      }
    }
    return {
      ...toRefs(state),
      register,
      registerForm,
      okClick,
      cancelClick,
    };
  },
});
</script>
<style lang="less" scoped></style>
