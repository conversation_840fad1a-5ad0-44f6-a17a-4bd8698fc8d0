<template>
  <div class="sidebar">
    <div
      v-if="isShow"
      class="content"
    >
      <a-tooltip
        placement="left"
        color="#108ee9"
        title="推送知识"
      >
        <a-button
          size="large"
          class="button"
          @click="$emit('push')"
        >
          <template #icon>
            <CommentOutlined class="icon" />
          </template>
        </a-button>
      </a-tooltip>
      <a-tooltip
        placement="left"
        color="#108ee9"
        title="搜索知识"
      >
        <a-button
          size="large"
          class="button"
          @click="$emit('search')"
        >
          <template #icon>
            <SearchOutlined class="icon" />
          </template>
        </a-button>
      </a-tooltip>
      <a-tooltip
        placement="left"
        color="#108ee9"
        title="分享知识"
      >
        <a-button
          size="large"
          class="button"
          @click="$emit('share')"
        >
          <template #icon>
            <ShareAltOutlined class="icon" />
          </template>
        </a-button>
      </a-tooltip>
    </div>
    <div
      :class="['icon-box', isShow ? 'show' : 'off']"
      @click="isShow = !isShow"
    >
      <CaretLeftOutlined
        v-show="!isShow"
        class="icon"
      />
      <CaretRightOutlined
        v-show="isShow"
        class="icon"
      />
    </div>
  </div>
</template>

<script>
import { reactive, toRefs } from 'vue';
import { Button, Tooltip } from 'ant-design-vue';
import {
  CommentOutlined,
  SearchOutlined,
  ShareAltOutlined,
  CaretLeftOutlined,
  CaretRightOutlined,
} from '@ant-design/icons-vue';
export default {
  name: 'FloatBox',
  components: {
    AButton: Button,
    ATooltip: Tooltip,
    CommentOutlined,
    SearchOutlined,
    CaretLeftOutlined,
    CaretRightOutlined,
    ShareAltOutlined,
  },
  emits: [
    'push',
    'search',
    'share',
  ],
  setup() {
    const state = reactive({
      isShow: false,
    });
    return {
      ...toRefs(state),
    };
  },
};
</script>

<style scoped lang="less">
  .ant-btn > .anticon {
    line-height: 0;
  }
  .content {
    width: 60px;
    background-color: #ffffff;
    border-radius: 60px;
    border: 1px solid #ddd;
    position: fixed;
    bottom: 50%;
    right: 10px;
    z-index: 10;
    transition: .3s;

    .button {
      display: block;
      margin: 10px auto;
      border: 0 !important;

      .icon {
        font-size: 24px !important;
      }
    }
  }

  .icon-box {
    position: fixed;
    bottom: 56%;
    right: 42px;
    z-index: 9;
    width: 45px;
    height: 45px;
    text-align: center;
    border-radius: 45px;
    cursor: pointer;
    color: #969eb4;
    background-color: #f3f3f3;
    transition: .3s;
    &:hover {
      color: #fff;
      background-color: ~`getPrefixVar('primary-color')` ;
    }

    .icon {
      position: absolute;
      left: 2px;
      top: 15px;
    }
  }
  .show {
    right: 42px;
  }
  .off {
    right: -27px;
  }
</style>
