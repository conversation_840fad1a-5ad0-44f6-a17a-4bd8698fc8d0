package com.chinasie.orion.domain.vo.resource;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/09/05/14:20
 * @description:
 */
@Data
public class PersonSourceVO implements Serializable {

    @ApiModelProperty(value = "数据id")
    private String id;
    @ApiModelProperty(value = "人员code")
    private String userCode;
    @ApiModelProperty(value = "姓名")
    private String fullName;
    @ApiModelProperty(value = "大修轮次")
    private String repairRound;
    @ApiModelProperty(value = "作业名称")
    private String jobName;

    @ApiModelProperty(value = "作业Id")
    private String jobId;

    @ApiModelProperty(value = "重叠天数")
    private int overlapCount;
    @ApiModelProperty(value = "数据类型: 1-人，2-作业，3.任务")
    private String dataType;

    @ApiModelProperty(value = "进出场时间")
    private List<InAndOutDateVO> inAndOutDateVOList;

    @ApiModelProperty(value = "作业计划开始时间")
    private Date jobBeginDate;
    @ApiModelProperty(value = "作业计划结束时间")
    private Date jobEndDate;

    @ApiModelProperty(value = "唯一Id")
    private String uniqueId;

    @ApiModelProperty(value = "人员管理ID")
    private String personId;

    private Date taskEndDate;

    private Date taskBeginDate;

    @ApiModelProperty(value = "排序: 4-任务 3-作业 2-人员进出 1-啥也没有")
    private Integer sort;
    @ApiModelProperty(value = "所属基地")
    private String baseCode;
    @ApiModelProperty(value = "最小时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    private Date  minDate;


    @ApiModelProperty(value = "对比时间：参照数据为任务类型的数据的时间")
    private List<InAndOutDateVO> targetDateVOList;
}
