package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.Boolean;
import java.lang.String;

/**
 * TrainPerson Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-04 17:09:14
 */
@TableName(value = "pmsx_train_person")
@ApiModel(value = "TrainPersonEntity对象", description = "参培人员")
@Data

public class TrainPerson extends  ObjectEntity  implements Serializable{

    /**
     * 培训id
     */
    @ApiModelProperty(value = "培训id")
    @TableField(value = "train_id")
    private String trainId;

    /**
     * 培训编码
     */
    @ApiModelProperty(value = "培训编码")
    @TableField(value = "train_number")
    private String trainNumber;

    /**
     * 参培中心关联表Id
     */
    @ApiModelProperty(value = "参培中心关联表Id")
    @TableField(value = "train_center_id")
    private String trainCenterId;



    /**
     * 员工号
     */
    @ApiModelProperty(value = "员工号")
    @TableField(value = "user_code")
    private String userCode;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @TableField(value = "full_name")
    private String fullName;

    /**
     * 公司编号
     */
    @ApiModelProperty(value = "公司编号")
    @TableField(value = "company_code")
    private String companyCode;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    @TableField(value = "company_name")
    private String companyName;

    /**
     * 部门编号
     */
    @ApiModelProperty(value = "部门编号")
    @TableField(value = "dept_code")
    private String deptCode;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    @TableField(value = "dept_name")
    private String deptName;

    /**
     * 研究所编号
     */
    @ApiModelProperty(value = "研究所编号")
    @TableField(value = "institute_code")
    private String instituteCode;

    /**
     * 研究所名称
     */
    @ApiModelProperty(value = "研究所名称")
    @TableField(value = "institute_name")
    private String instituteName;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    @TableField(value = "sex")
    private String sex;

    /**
     * 到期日期
     */
    @ApiModelProperty(value = "到期日期")
    @TableField(value = "end_date")
    private Date endDate;

    /**
     * 是否等效
     */
    @ApiModelProperty(value = "是否等效")
    @TableField(value = "is_equivalent")
    private Boolean isEquivalent;

    /**
     * 现任职务
     */
    @ApiModelProperty(value = "现任职务")
    @TableField(value = "now_position")
    private String nowPosition;


    @ApiModelProperty(value = "成绩")
    @TableField(value = "score")
    private BigDecimal score;

    @ApiModelProperty(value = "是否合格")
    @TableField(value = "is_ok")
    private Boolean isOK;

}
