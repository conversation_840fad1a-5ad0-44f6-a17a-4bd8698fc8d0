package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.TakeEffectDTO;
import com.chinasie.orion.domain.dto.TaskSubjectDTO;
import com.chinasie.orion.domain.vo.SimpleVo;
import com.chinasie.orion.domain.vo.TaskSubjectVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.page.PageRequest;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.service.TaskSubjectService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/13/10:18
 * @description:
 */
@RestController
@RequestMapping("/task-subject")
@Api(tags = "任务科目")
public class TaskSubjectController {

    @Resource
    private TaskSubjectService taskSubjectService;

    @ApiOperation("新增任务科目")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "taskSubjectDTO", dataType = "TaskSubjectDTO")
    })
    @PostMapping(value = "/save")
    @LogRecord(success = "【{USER{#logUserId}}】新增任务科目", type = "任务科目", subType = "新增任务科目", bizNo = "")
    public ResponseDTO<String> saveTaskSubject(@RequestBody TaskSubjectDTO taskSubjectDTO) throws Exception {
        return new ResponseDTO<>(taskSubjectService.saveTaskSubject(taskSubjectDTO));
    }

    @ApiOperation("获取启用任务科目列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", dataType = "projectId")
    })
    @GetMapping(value = "/getList/{projectId}")
    @LogRecord(success = "【{USER{#logUserId}}】获取启用任务科目列表", type = "任务科目", subType = "获取启用任务科目列表", bizNo = "{{#projectId}}")
    public ResponseDTO<List<SimpleVo>> getTaskSubjectListByTakeEffect(@PathVariable("projectId") String projectId) throws Exception {
        return new ResponseDTO<>(taskSubjectService.getTaskSubjectListByTakeEffect(projectId));
    }

    @ApiOperation("获取任务科目分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping(value = "/getPage")
    @LogRecord(success = "【{USER{#logUserId}}】获取任务科目分页", type = "任务科目", subType = "获取任务科目分页", bizNo = "")
    public ResponseDTO<PageResult<TaskSubjectVO>> getProjectTaskStatusPage(@RequestBody PageRequest<TaskSubjectDTO> pageRequest) throws Exception {
        return new ResponseDTO<>(taskSubjectService.getTaskSubjectPage(pageRequest));
    }

    @ApiOperation("获取任务科目详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", dataType = "String")
    })
    @GetMapping(value = "/detail/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】获取任务科目详情", type = "任务科目", subType = "获取任务科目详情", bizNo = "{{#id}}")
    public ResponseDTO<TaskSubjectVO> getTaskSubjectDetail(@PathVariable("id") String id) throws Exception {
        return new ResponseDTO<>(taskSubjectService.getTaskSubjectDetail(id));
    }

    @ApiOperation("编辑任务科目")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "taskSubjectDTO", dataType = "TaskSubjectDTO")
    })
    @PutMapping("/edit")
    @LogRecord(success = "【{USER{#logUserId}}】编辑任务科目", type = "任务科目", subType = "编辑任务科目", bizNo = "}")
    public ResponseDTO<Boolean> editTaskSubject(@RequestBody TaskSubjectDTO taskSubjectDTO) throws Exception {
        return new ResponseDTO<>(taskSubjectService.editTaskSubject(taskSubjectDTO));
    }

    @ApiOperation("批量删除任务科目")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", dataType = "List")
    })
    @DeleteMapping(value = "/removeBatch")
    @LogRecord(success = "【{USER{#logUserId}}】批量删除任务科目", type = "任务科目", subType = "批量删除任务科目", bizNo = "")
    public ResponseDTO<Boolean> removeTaskSubject(@RequestBody List<String> ids) throws Exception {
        return new ResponseDTO<>(taskSubjectService.removeTaskSubject(ids));
    }

    @ApiOperation("批量启用禁用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "takeEffectDTO", dataType = "TakeEffectDTO")
    })
    @PutMapping(value = "/takeEffectBatch")
    @LogRecord(success = "【{USER{#logUserId}}】批量启用禁用", type = "任务科目", subType = "批量启用禁用", bizNo = "")
    public ResponseDTO<Boolean> takeEffectTaskSubject(@RequestBody TakeEffectDTO takeEffectDTO) throws Exception {
        return new ResponseDTO<>(taskSubjectService.takeEffectTaskSubject(takeEffectDTO));
    }
}
