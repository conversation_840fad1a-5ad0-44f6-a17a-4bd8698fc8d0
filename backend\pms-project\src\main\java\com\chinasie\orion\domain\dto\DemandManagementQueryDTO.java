package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/2/18 15:23
 * @description:
 */
@Data
@ApiModel(value = "DemandManagementQueryDTO对象", description = "搜索")
public class DemandManagementQueryDTO  implements Serializable {

    @ApiModelProperty(value = "项目id")
    @NotEmpty(message = "项目id不能为空")
    private String projectId;

    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyword;

    @ApiModelProperty(value = "优先级")
    private String priorityLevel;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "提出时间")
    private List<Long> proposedTime;

    @ApiModelProperty(value = "期望完成日期")
    private List<Long> predictEndTime;
}
