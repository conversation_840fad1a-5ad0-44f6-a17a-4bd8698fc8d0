package com.chinasie.orion.repository;

import com.chinasie.orion.domain.entity.acceptance.AcceptanceForm;
import com.chinasie.orion.search.common.domain.IndexData;
import org.apache.ibatis.annotations.Mapper;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;


/**
 * 验收单 Repository.
 *
 * <AUTHOR>
 */
@Mapper
public interface AcceptanceFormRepository extends OrionBaseMapper<AcceptanceForm> {
    /**
     * 获取自上次索引依赖新增的可索引的EDM数据.
     *
     * @param lastIndexTime 上次索引时间
     * @param limitSize
     * @return
     */
    List<IndexData> fetchIndexData(@Param("lastIndexTime") Date lastIndexTime, @Param("limitSize") Integer limitSize);

    /**
     * 查找项目id
     *
     * @param acceptanceFormId
     * @return
     */
    String findProjectIdByFormId(@Param("acceptanceFormId") String acceptanceFormId);
}
