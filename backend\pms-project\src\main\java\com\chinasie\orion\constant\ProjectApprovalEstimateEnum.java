package com.chinasie.orion.constant;

/**
 * @author: lsy
 * @date: 2024/5/8
 * @description:
 */
public enum ProjectApprovalEstimateEnum {
    INTER("inter","内部"),
    OUT("out","外部"),
    PEOPLE_NUM("<PERSON><PERSON><PERSON>LE_NUM", "人天"),
    ;

    private String key;

    private String desc;


    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }
    ProjectApprovalEstimateEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

}
