<script lang="ts" setup>
import {
  defineComponent, onMounted, Ref, ref, watch,
} from 'vue';
import { Select as ASelect, Tag as ATag } from 'ant-design-vue';
import { planActiveColor } from '/@/views/pms/projectLaborer/projectLab/enums';

const props = withDefaults(defineProps<{
    record:object,
    planActiveOptions:any
}>(), {
  record: () => ({}),
  planActiveOptions: () => [],
});
const emit = defineEmits(['change']);
const isEdit:Ref<boolean> = ref(false);
const dropdownVisible:Ref<boolean> = ref(false);
const planActive:Ref<''> = ref('');
watch(() => props.record, (newVal) => {
  planActive.value = Array.isArray(newVal.planActiveList) ? newVal.planActiveList.map((item) => item.value) : [];
});
onMounted(() => {
  planActive.value = Array.isArray(props.record.planActiveList) ? props.record.planActiveList.map((item) => item.value) : [];
});
// 下拉框展开操作
function dropdownVisibleChange(bool) {
  dropdownVisible.value = bool;
  if (!bool) {
    isEdit.value = false;
  }
}
function handleMouseover() {
  if (props.record.status === 101) {
    isEdit.value = true;
  }
}
function handleMouseleave() {
  if (!dropdownVisible.value) {
    isEdit.value = false;
  }
}
let saveTimeOut = null;
function handleChange(value: []) {
  if (saveTimeOut) {
    clearTimeout(saveTimeOut);
  }
  saveTimeOut = setTimeout(() => {
    const planActiveList:any = props.planActiveOptions.filter((item) => value.includes(item.value)).map((item) => ({
      name: item.name,
      value: item.value,
    }));
    emit('change', planActiveList);
  }, 1000);
}
</script>

<template>
  <div
    style="width: 100%;min-height: 30px;"
    @mouseover="handleMouseover"
    @mouseleave="handleMouseleave"
  >
    <ASelect
      v-if="isEdit"
      v-model:value="planActive"
      placeholder="请选择"
      style="width: 100%"
      :options="planActiveOptions"
      :fieldNames="{label:'name',value:'value'}"
      mode="multiple"
      @dropdownVisibleChange="dropdownVisibleChange"
      @change="handleChange"
    />
    <div
      v-else
      class="plan-active-name"
    >
      <template v-if="Array.isArray(record.planActiveList)&&record.planActiveList.length>0">
        <template
          v-for="(item,index) in record.planActiveList"
          :key="index"
        >
          <ATag
            :color="planActiveColor[item.value]"
            style="margin-right: 5px"
          >
            {{ item.name }}
          </ATag>
        </template>
      </template>
    </div>
  </div>
</template>

<style scoped lang="less">
.plan-active-name{
  height: 32px;
  line-height: 32px;
}
</style>
