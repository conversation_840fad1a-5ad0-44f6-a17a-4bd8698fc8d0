<template>
  <div class="planGanttChart">
    <JQueryGantt
      :taskData="taskData"
      :attributePopoverContainerRender="attributePopoverContainerRender"
    />
  </div>
</template>

<script  lang='ts'>
import {
  defineComponent, onMounted, ref, Ref, h,
} from 'vue';
import { JQueryGantt, IGanttOptions, Layout } from 'lyra-component-vue3';
import Api from '/@/api';
import AttributePopoverContainer from './AttributePopoverContainer.vue';

export default defineComponent({
  name: 'PlanGanttChart',
  components: {
    JQueryGantt,
  },
  props: {
    formId: {
      type: String,
      default: '',
    },
    pageType: {
      type: String,
      default: 'page',
    },
  },
  setup(props) {
    const taskData: Ref<IGanttOptions> = ref({
      tasks: [],
    });
    onMounted(() => {
      new Api('/pms').fetch({}, `project/gantt/${props.formId}`, 'GET').then((res) => {
        taskData.value.tasks = res;
      });
    });
    return {
      taskData,
      attributePopoverContainerRender(task) {
        return h(AttributePopoverContainer, {
          getTaskDetailApi: () => new Api('/pms').fetch({}, `/plan/${task.planId}`, 'GET'),
        });
      },
    };
  },

});
</script>
<style lang="less" scoped>
.planGanttChart{
  height: 100%;
  width: 100%;
  padding: ~`getPrefixVar('content-padding-top')` ~`getPrefixVar('content-padding-left')`;
}
</style>
