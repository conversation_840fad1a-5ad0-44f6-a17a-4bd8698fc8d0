package com.chinasie.orion.service.impl;


import com.chinasie.orion.domain.entity.PlanToDemandManagement;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.PlanToDemandManagementRepository;
import com.chinasie.orion.service.PlanToDemandManagementService;
import org.springframework.stereotype.Service;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/01/14:31
 * @description:
 */
@Service
public class PlanToDemandManagementServiceImpl extends OrionBaseServiceImpl<PlanToDemandManagementRepository, PlanToDemandManagement> implements PlanToDemandManagementService {

}
