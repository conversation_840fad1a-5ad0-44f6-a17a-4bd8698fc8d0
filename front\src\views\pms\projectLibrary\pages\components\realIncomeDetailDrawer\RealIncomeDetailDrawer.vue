<script setup lang="ts">
import { BasicDrawer, useDrawerInner } from 'lyra-component-vue3';
import { ref, Ref } from 'vue';
import RealIncomeDetailTable from './RealIncomeDetailTable.vue';

const [register] = useDrawerInner((openProps) => {
  receivableId.value = openProps.id;
  visibleTable.value = true;
});

const visibleTable:Ref<boolean> = ref(false);
const receivableId:Ref<string> = ref();
function visibleChange(visible: boolean) {
  !visible && (visibleTable.value = visible);
}
</script>

<template>
  <BasicDrawer
    v-bind="$attrs"
    title="实收明细"
    width="1000px"
    :height="450"
    :onVisibleChange="visibleChange"
    @register="register"
  >
    <RealIncomeDetailTable
      v-if="visibleTable"
      :receivableId="receivableId"
    />
  </BasicDrawer>
</template>

<style scoped lang="less">

</style>
