<template>
  <div class="searchModal">
    <a-drawer
      v-model:visible="visible"
      :title="title"
      placement="right"
      width="340"
      class="searchModalDrawer pdmRightDrawer"
      :mask-closable="false"
      @close="x"
    >
      <div class="search_title mb15">
        <aInputSearch
          v-model:value="nameValue"
          placeholder="请输入内容"
          size="large"
          @search="searchData"
        />
      </div>
      <basicTitle :title="'筛选属性'">
        <div class="rowItem">
          <div class="rowItem_label">
            问题类型:
          </div>
          <a-select
            v-model:value="FormData.questionType"
            size="large"
            placeholder="请选择问题类型"
            allow-clear
          >
            <a-select-option
              v-for="(item, index) in questionType"
              :key="index"
              :value="item.id"
            >
              {{
                item.name
              }}
            </a-select-option>
          </a-select>
        </div>
        <div class="rowItem">
          <div class="rowItem_label">
            问题来源:
          </div>
          <a-select
            v-model:value="FormData.questionSource"
            size="large"
            placeholder="请选择问题来源"
            allow-clear
          >
            <a-select-option
              v-for="(item, index) in questionSource"
              :key="index"
              :value="item.id"
            >
              {{ item.name }}
            </a-select-option>
          </a-select>
        </div>
        <div class="rowItem">
          <div class="rowItem_label">
            严重程度:
          </div>
          <a-select
            v-model:value="FormData.seriousLevel"
            size="large"
            placeholder="请选择严重程度"
            allow-clear
          >
            <a-select-option
              v-for="(item, index) in seriousLevel"
              :key="index"
              :value="item.id"
            >
              {{
                item.name
              }}
            </a-select-option>
          </a-select>
        </div>
        <div class="rowItem">
          <div class="rowItem_label">
            状态类型:
          </div>
          <a-select
            v-model:value="FormData.status"
            size="large"
            placeholder="请选择状态类型"
            allow-clear
          >
            <a-select-option
              v-for="(item, index) in status"
              :key="index"
              :value="item.status"
            >
              {{
                item.name
              }}
            </a-select-option>
          </a-select>
        </div>

        <div class="nodeItemBtn">
          <a-button
            size="large"
            class="cancelBtn"
            @click="close"
          >
            取消
          </a-button>
          <a-button
            size="large"
            class="bgDC"
            type="primary"
            @click="onSubmit"
          >
            确认
          </a-button>
        </div>
      </basicTitle>
    </a-drawer>
  </div>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, ref, watch, onMounted,
} from 'vue';
import {
  message, Drawer, Select, Input, DatePicker, TreeSelect, Button,
} from 'ant-design-vue';
import basicTitle from '/@/views/pms/projectLaborer/componentsList/basicTitle/index.vue';
import dayjs from 'dayjs';
import {
  questionSourceApi,
  questionTypeApi,
  questionLevelApi,
  // soluteRiskListApi
} from '/@/views/pms/projectLaborer/api/questionManage';
import { statusListApi } from '/@/views/pms/projectLaborer/api/riskManege';
export default defineComponent({
  components: {
    basicTitle,
    aDrawer: Drawer,
    aSelect: Select,
    aInputSearch: Input.Search,
    RangePicker: DatePicker.RangePicker,
    ATreeSelect: TreeSelect,
    ASelectOption: Select.Option,
    AButton: Button,
  },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
    Projectid: {
      type: String,
      default: '',
    },
  },
  setup(props, { emit }) {
    const state = reactive({
      FormData: <any>{
        /* 状态 */
        status: '',
        // 类型
        questionType: '',
        // 来源
        questionSource: '',
        // 严重程度
        seriousLevel: '',
      },
      visible: false,
      title: '搜索',
      // 状态
      status: <any>[],
      // 类型
      questionType: <any>[],
      // 问题来源
      questionSource: <any>[],
      // 严重等级
      seriousLevel: <any>[],
      nameValue: '',
    });
    watch(
      () => props.data,
      async () => {
        state.visible = true;
        try {
          const res2 = await questionSourceApi();
          state.questionSource = res2;
          const res3 = await questionTypeApi();
          state.questionType = res3;
          const res4 = await questionLevelApi();
          state.seriousLevel = res4;
          const res5 = await statusListApi();
          state.status = res5;
        } catch (err) {
          console.log('测试🚀 ~ file: addProjectModal.vue ~ line 336 ~ err', err);
        }
      },
    );
    /* x按钮 */
    const x = () => {
      state.FormData = {};
      state.nameValue = '';
    };
      /* 取消 */
    const close = () => {
      state.visible = false;
      state.FormData = {};
      state.nameValue = '';
    };
    const onSubmit = () => {
      state.visible = false;
      let params = {};
      // console.log('测试🚀 ~ file: searchModal.vue ~ line 172 ~ time', state.time);

      for (const item in state.FormData) {
        params[item] = state.FormData[item];
      }
      let queryCondition = [];
      queryCondition = <any>[
        {
          column: 'name',
          type: 'like',
          link: 'or',
          value: state.nameValue,
        },
        {
          column: 'number',
          type: 'like',
          link: 'or',
          value: state.nameValue,
        },
      ];

      // console.log('测试🚀 ~ file: searchModal.vue ~ line 164 ~ params', params);

      emit('search', {
        params,
        queryCondition,
      });
      state.FormData = {};
      state.nameValue = '';
    };
    const searchData = () => {
      onSubmit();
    };
    return {
      ...toRefs(state),
      close,
      onSubmit,
      searchData,
      x,
      dayjs,
    };
  },
});
</script>
<style lang="less" scoped>
  .searchModalDrawer {
    .ant-drawer-body {
      padding: 60px 0px 80px 0px !important;
    }
    .search_title {
      padding: 10px 0px;
      border-bottom: 1px solid #d2d7e1;
      text-align: center;
      margin-bottom: 10px;
      .ant-input-search {
        width: 310px;
      }
    }
    .basicTitle {
      padding: 0px 15px;
    }
    .rowItem {
      margin-bottom: 10px;
      .rowItem_label {
        padding-left: 5px;
        color: #444b5e;
      }
      .ant-select {
        width: 100%;
      }
    }
    .nodeItemBtn {
      position: fixed;
      bottom: 0px;
      padding: 20px 0;
      text-align: center;
      width: 280px;
      height: 80px;
      background: #ffffff;
      margin-bottom: 0px;
    }
    .cancelBtn {
      color: #5172dc;
      background: #5172dc19;
      width: 110px;
      border-radius: 4px;
    }
    .bgDC {
      width: 110px;
      margin-left: 15px;
      border-radius: 4px;
    }
  }
</style>
