package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.util.Date;
import java.lang.String;
import java.lang.Integer;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * TrainCenter DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-04 17:09:07
 */
@ApiModel(value = "TrainCenterDTO对象", description = "培训中心管理")
@Data
@ExcelIgnoreUnannotated
public class TrainCenterDTO extends  ObjectDTO   implements Serializable{

    /**
     * 培训Id
     */
    @ApiModelProperty(value = "培训Id")
    @ExcelProperty(value = "培训Id ", index = 0)
    private String trainId;

    /**
     * 培训编码
     */
    @ApiModelProperty(value = "培训编码")
    @ExcelProperty(value = "培训编码 ", index = 1)
    private String trainNumber;

    /**
     * 参培中心编号
     */
    @ApiModelProperty(value = "参培中心编号")
    @ExcelProperty(value = "参培中心编号 ", index = 2)
    private String attendCenter;

    /**
     * 参培中心名称
     */
    @ApiModelProperty(value = "参培中心名称")
    @ExcelProperty(value = "参培中心名称 ", index = 3)
    private String attendCenterName;

    /**
     * 培训地点
     */
    @ApiModelProperty(value = "培训地点")
    @ExcelProperty(value = "培训地点 ", index = 4)
    private String trainAddress;

    /**
     * 培训讲师
     */
    @ApiModelProperty(value = "培训讲师")
    @ExcelProperty(value = "培训讲师 ", index = 5)
    private String trainLecturer;

    /**
     * 办结时间
     */
    @ApiModelProperty(value = "办结时间")
    @ExcelProperty(value = "办结时间 ", index = 6)
    private Date endDate;


    /**
     * 中心培训联络人
     *
     */
    @ApiModelProperty(value = "中心培训联络人")
    @ExcelProperty(value = "中心培训联络人 ", index = 7)
    private String contactPersonIds;

    /**
     * 中心培训联络人
     * contact_person_names
     */
    @ApiModelProperty(value = "中心培训联络人")
    @ExcelProperty(value = "参培人数 ", index = 8)
    private String contactPersonNames;

    /**
     * 参培人数
     */
    @ApiModelProperty(value = "参培人数")
    @ExcelProperty(value = "参培人数 ", index = 9)
    private Integer trainNum;


    @ApiModelProperty(value = "附件")
    private List<FileDTO> fileDTOList;

}
