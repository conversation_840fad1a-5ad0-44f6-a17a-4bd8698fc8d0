<script setup lang="ts">
import {
  h,
  ref,
  Ref,
} from 'vue';
import {
  BasicTableAction,
  IOrionTableActionItem,
  Layout,
  OrionTable,
  isPower,
  BasicButton,
  downloadByData,
  BasicImport,
  useModal,
} from 'lyra-component-vue3';
import { Modal, message } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import Api from '/@/api';
import { RoleFilterConfig } from '../filterIndex';
import { openFormDrawer } from '../utils';
import StaffEditor from './components/RoleEditor.vue';
import { DropdownMenu } from '/@/views/pms/financialManage/components';

const router = useRouter();
const visible: Ref<boolean> = ref(true);
const tableRef: Ref = ref();
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const selectionKeys: Ref<string[]> = ref([]);
const loadStatus: Ref<boolean> = ref(false);
const importId = ref();
const powerData: Ref<any[]> = ref([]);
const [register, { openModal }] = useModal();
const tableOptions = {
  rowSelection: {
    onChange(_keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
      selectionKeys.value = _keys;
    },
  },
  showToolButton: false,
  isSpacing: true,
  isFilter2: true,
  smallSearchField: [
    't1.name',
    't2.name',
    't4.name',
    't5.name',
    't6.name',
  ],
  filterConfig: {
    fields: RoleFilterConfig,
  },
  api: (params: Record<string, any>) => new Api('/pms/personRoleMaintenance').fetch({
    ...params,
    power: {
      pageCode: 'RYJSWH_001',
    },
  }, 'page', 'POST'),
  columns: [
    {
      title: '操作',
      dataIndex: 'actions',
      width: 160,
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
    {
      title: '专业所',
      width: 200,
      dataIndex: 'expertiseStationTitle',
    },
    {
      title: '专业所审核人员',
      dataIndex: 'expertiseStationName',
    },
    {
      title: '专业中心',
      width: 200,
      dataIndex: 'expertiseCenterTitle',
    },
    {
      title: '专业中心审核人员',
      dataIndex: 'expertiseCenterName',
    },
    {
      title: '财务人员',
      dataIndex: 'financialStaffName',
    },
    {
      title: '最后一次更新时间',
      width: 180,
      dataIndex: 'changeTime',
    },
    {
      title: '更新人',
      width: 100,
      dataIndex: 'changePersonName',
    },
  ],
};

const actions: IOrionTableActionItem[] = [
  {
    text: '查看',
    event: 'view',
    isShow: () => isPower('PMS_RYJSWH_001_container_01_button_01', powerData.value),
  },
  {
    text: '编辑',
    event: 'edit',
    isShow: () => isPower('PMS_RYJSWH_001_container_01_button_02', powerData.value),
  },
  {
    text: '删除',
    event: 'del',
    isShow: () => isPower('PMS_RYJSWH_001_container_01_button_03', powerData.value),
  },
];

// 行操作（查看，编制，调整，删除）
function actionClick(actionItem: any, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'view':
      navDetails(record.id);
      break;
    case 'edit':
      openFormDrawer(StaffEditor, record, updateTable);
      break;
    case 'del':
      handleDelete([record.id]);
      break;
  }
}

// 删除
function handleDelete(ids) {
  Modal.confirm({
    title: '删除操作！',
    content: '确定要删除数据吗？',
    onOk: () => deleteBatchApi(ids),
  });
}

// 批量删除
async function deleteBatchApi(ids) {
  // 否则，调用接口进行删除
  try {
    await new Api('/pms/personRoleMaintenance/remove').fetch(ids, '', 'DELETE').then((res) => {
      if (res) {
        message.success('删除成功');
        updateTable();
      }
    });
  } catch (error) {
    message.error('删除失败，请稍后再试');
  }
}

// 导出
async function handleExport() {
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk: async () => {
      loadStatus.value = true;
      await downloadByData('/pms/personRoleMaintenance/export/excel', selectionKeys.value, '', 'POST', true, false, '导出处理完成，现在开始下载');
      loadStatus.value = false;
    },
  });
}

const handleOperation = (type: any) => {
  switch (type) {
    case 'add':
      openFormDrawer(StaffEditor, null, updateTable);
      break;
    case 'del':
      const ids = selectedRows.value.map((item: any) => item.id);
      handleDelete(ids);
      break;
  }
};

// 调整详情
function navDetails(id: string) {
  router.push({
    name: 'RoleDetails',
    query: {
      id,
    },
  });
}

// 下载url
const downloadFileObj = {
  url: '/pms/personRoleMaintenance/download/excel/tpl',
  method: 'GET',
};

// 导入模版
function handleImport() {
  openModal(true, {});
}

const requestBasicImport = async (formData) =>
  new Promise((resolve) => {
    new Api('/pms')
      .importFile(
        formData[0],
        '/api/pms/personRoleMaintenance/import/excel/check',
      )
      .then((res) => {
        const {
          code,
          message,
        } = res.data;
        if (code === 200) {
          // 转换oom   ---> message
          let newResultData = res.data.result;
          if (res.data.result.oom) {
            newResultData.message = res.data.result.oom;
          }
          resolve(newResultData);
        } else {
          const msg = message || '模版格式不对';
          resolve({
            code: 4000,
            msg,
          });
        }
      });
  });

// 取消导入
function changeImportModalFlag({ succ, successImportFlag }) {
  if (!successImportFlag && succ) {
    return new Api(`/pms/personRoleMaintenance/import/excel/cancel/${succ}`).fetch('', '', 'POST');
  }
}

const requestSuccessImport = (importId) =>
  new Promise((resolve) => {
    new Api(`/pms/personRoleMaintenance/import/excel/${importId}`).fetch('', '', 'POST')
      .then(() => {
        updateTable();
        resolve({
          result: true,
        });
      });
  });

function updateTable() {
  tableRef.value?.reload();
}

// 获取权限数据
function getPowerDataHandle(power: any) {
  powerData.value = power;
}

</script>

<template>
  <Layout
    v-get-power="{pageCode:'RYJSWH_001',getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="isPower('PMS_RYJSWH_001_container_02_button_01', powerData)"
          type="primary"
          icon="add"
          @click="handleOperation('add')"
        >
          新增数据
        </BasicButton>
        <!-- 导入导出下拉菜单 -->
        <DropdownMenu
          v-if="isPower('PMS_RYJSWH_001_container_02_button_02', powerData)"
          title="导入导出"
          :options="[
            { text: '导入', action: () => handleImport() },
            { text: '导出', action: () => handleExport() }
          ]"
        />
        <BasicButton
          v-if="isPower('PMS_RYJSWH_001_container_02_button_04', powerData)"
          type="primary"
          ghost
          icon="sie-icon-del"
          :disabled="!selectedRows.length"
          @click="handleOperation('del')"
        >
          删除
        </BasicButton>
      </template>
      <template #actions="{record}">
        <BasicTableAction
          :actions="actions"
          :record="record"
          @actionClick="actionClick($event,record)"
        />
      </template>
    </OrionTable>
    <!-- 导入 -->
    <BasicImport
      :downloadFileObj="downloadFileObj"
      :requestBasicImport="requestBasicImport"
      :requestSuccessImport="requestSuccessImport"
      @register="register"
      @changeImportModalFlag="changeImportModalFlag"
    />
  </Layout>
</template>

<style scoped lang="less">

</style>
