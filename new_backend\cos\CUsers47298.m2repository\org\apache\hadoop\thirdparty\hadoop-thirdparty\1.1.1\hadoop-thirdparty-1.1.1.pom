<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements.  See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership.  The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied.  See the License for the
  specific language governing permissions and limitations
  under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.apache.hadoop.thirdparty</groupId>
  <artifactId>hadoop-thirdparty</artifactId>
  <version>1.1.1</version>
  <parent>
    <groupId>org.apache</groupId>
    <artifactId>apache</artifactId>
    <version>21</version>
    <relativePath/>
    <!-- no parent resolution -->
  </parent>
  <name>Apache Hadoop Third-party Libs</name>
  <packaging>pom</packaging>
  <description>
    Packaging of relocated (renamed, shaded) third-party libraries used by Hadoop.
  </description>

  <distributionManagement>
    <repository>
      <id>${distMgmtStagingId}</id>
      <name>${distMgmtStagingName}</name>
      <url>${distMgmtStagingUrl}</url>
    </repository>
    <snapshotRepository>
      <id>${distMgmtSnapshotsId}</id>
      <name>${distMgmtSnapshotsName}</name>
      <url>${distMgmtSnapshotsUrl}</url>
    </snapshotRepository>
    <site>
      <id>apache.website</id>
      <url>scpexe://people.apache.org/www/hadoop.apache.org/docs/rthirdparty-${project.version}</url>
    </site>
  </distributionManagement>

  <repositories>
    <repository>
      <id>${distMgmtSnapshotsId}</id>
      <name>${distMgmtSnapshotsName}</name>
      <url>${distMgmtSnapshotsUrl}</url>
    </repository>
    <repository>
      <id>repository.jboss.org</id>
      <url>https://repository.jboss.org/nexus/content/groups/public/</url>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
    </repository>
  </repositories>

  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>

  <properties>
    <!-- define the Java language version used by the compiler -->
    <javac.version>1.8</javac.version>

    <distMgmtSnapshotsId>apache.snapshots.https</distMgmtSnapshotsId>
    <distMgmtSnapshotsName>Apache Development Snapshot Repository</distMgmtSnapshotsName>
    <distMgmtSnapshotsUrl>https://repository.apache.org/content/repositories/snapshots</distMgmtSnapshotsUrl>
    <distMgmtStagingId>apache.staging.https</distMgmtStagingId>
    <distMgmtStagingName>Apache Release Distribution Repository</distMgmtStagingName>
    <distMgmtStagingUrl>https://repository.apache.org/service/local/staging/deploy/maven2</distMgmtStagingUrl>

    <!-- platform encoding override -->
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

    <!--thirdparty dependency versions-->
    <shaded.prefix>org.apache.hadoop.thirdparty</shaded.prefix>
    <protobuf.shade.prefix>${shaded.prefix}.protobuf</protobuf.shade.prefix>
    <protobuf_3_7.version>3.7.1</protobuf_3_7.version>
    <guava.version>30.1.1-jre</guava.version>

    <!-- maven plugin versions -->
    <maven-deploy-plugin.version>2.8.1</maven-deploy-plugin.version>
    <maven-site-plugin.version>3.6</maven-site-plugin.version>
    <maven-stylus-skin.version>1.5</maven-stylus-skin.version>
    <maven-antrun-plugin.version>1.7</maven-antrun-plugin.version>
    <maven-assembly-plugin.version>2.5</maven-assembly-plugin.version>
    <maven-dependency-plugin.version>3.0.2</maven-dependency-plugin.version>
    <maven-enforcer-plugin.version>3.0.0-M1</maven-enforcer-plugin.version>
    <maven-javadoc-plugin.version>3.0.1</maven-javadoc-plugin.version>
    <maven-gpg-plugin.version>1.5</maven-gpg-plugin.version>
    <apache-rat-plugin.version>0.12</apache-rat-plugin.version>
    <wagon-ssh.version>2.4</wagon-ssh.version>
    <maven-bundle-plugin.version>2.5.0</maven-bundle-plugin.version>
    <maven-checkstyle-plugin.version>3.0.0</maven-checkstyle-plugin.version>
    <checkstyle.version>8.19</checkstyle.version>
    <dependency-check-maven.version>6.1.5</dependency-check-maven.version>
    <exec-maven-plugin.version>1.3.1</exec-maven-plugin.version>
  </properties>

  <organization>
    <name>Apache Software Foundation</name>
    <url>https://www.apache.org</url>
  </organization>

  <modules>
    <module>hadoop-shaded-protobuf_3_7</module>
    <module>hadoop-shaded-guava</module>
  </modules>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>${maven-dependency-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-enforcer-plugin</artifactId>
          <version>${maven-enforcer-plugin.version}</version>
          <configuration>
            <rules>
              <requireMavenVersion>
                <version>[3.0.2,)</version>
              </requireMavenVersion>
              <requireJavaVersion>
                <version>[1.8,)</version>
              </requireJavaVersion>
            </rules>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>${maven-assembly-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>${maven-deploy-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.rat</groupId>
          <artifactId>apache-rat-plugin</artifactId>
          <version>${apache-rat-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-antrun-plugin</artifactId>
          <version>${maven-antrun-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>exec-maven-plugin</artifactId>
          <version>${exec-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <version>${maven-site-plugin.version}</version>
          <dependencies>
            <dependency><!-- add support for ssh/scp -->
              <groupId>org.apache.maven.wagon</groupId>
              <artifactId>wagon-ssh</artifactId>
              <version>${wagon-ssh.version}</version>
            </dependency>
            <dependency>
              <groupId>org.apache.maven.doxia</groupId>
              <artifactId>doxia-module-markdown</artifactId>
              <version>1.8</version>
            </dependency>
          </dependencies>
        </plugin>
        <plugin>
          <groupId>org.apache.felix</groupId>
          <artifactId>maven-bundle-plugin</artifactId>
          <version>${maven-bundle-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.owasp</groupId>
          <artifactId>dependency-check-maven</artifactId>
          <version>${dependency-check-maven.version}</version>
        </plugin>
      </plugins>
    </pluginManagement>

    <plugins>
      <plugin>
        <artifactId>maven-clean-plugin</artifactId>
        <configuration>
          <filesets>
            <fileset>
              <directory>${basedir}</directory>
              <includes>
                <include>dependency-reduced-pom.xml</include>
              </includes>
            </fileset>
          </filesets>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <inherited>false</inherited>
        <executions>
          <execution>
            <id>clean</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <phase>pre-clean</phase>
          </execution>
          <execution>
            <id>default</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <phase>validate</phase>
          </execution>
          <execution>
            <id>site</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <phase>pre-site</phase>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
        <configuration>
          <excludes>
            <exclude>.gitattributes</exclude>
            <exclude>.gitignore</exclude>
            <exclude>.git/**</exclude>
            <exclude>.github/pull_request_template.md</exclude>
            <exclude>.idea/**</exclude>
            <exclude>README.md</exclude>
            <exclude>**/build/**</exclude>
            <exclude>**/patchprocess/**</exclude>
            <exclude>**/*.js</exclude>
            <exclude>licenses/**</exclude>
            <exclude>licenses-binary/**</exclude>
            <exclude>**/dependency-reduced-pom.xml</exclude>
            <exclude>NOTICE-binary</exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-site-plugin</artifactId>
        <executions>
          <execution>
            <id>attach-descriptor</id>
            <goals>
              <goal>attach-descriptor</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <inherited>true</inherited>
        <extensions>true</extensions>
      </plugin>
      <plugin>
        <!-- OWASP's dependency-check plugin will scan the third party
             dependencies of this project for known CVEs (security
             vulnerabilities against them). It will produce a report
             in target/dependency-check-report.html. To invoke, run
             'mvn dependency-check:aggregate'. Note that this plugin
             requires maven 3.1.1 or greater.
        -->
        <groupId>org.owasp</groupId>
        <artifactId>dependency-check-maven</artifactId>
        <version>${dependency-check-maven.version}</version>
      </plugin>
    </plugins>
  </build>

  <reporting>
    <excludeDefaults>true</excludeDefaults>
  </reporting>

  <profiles>
    <profile>
      <id>src</id>
      <activation>
        <activeByDefault>false</activeByDefault>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-assembly-plugin</artifactId>
            <inherited>false</inherited>
            <executions>
              <execution>
                <id>src-dist</id>
                <phase>package</phase>
                <goals>
                  <goal>single</goal>
                </goals>
                <configuration>
                  <tarLongFileMode>posix</tarLongFileMode>
                  <appendAssemblyId>false</appendAssemblyId>
                  <attach>false</attach>
                  <finalName>hadoop-thirdparty-${project.version}-src</finalName>
                  <outputDirectory>target</outputDirectory>
                  <descriptors>
                    <descriptor>src/main/resources/assemblies/hadoop-thirdparty-src.xml</descriptor>
                  </descriptors>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-antrun-plugin</artifactId>
            <inherited>false</inherited>
            <executions>
              <execution>
                <id>src-dist-msg</id>
                <phase>package</phase>
                <goals>
                  <goal>run</goal>
                </goals>
                <configuration>
                  <target>
                    <echo/>
                    <echo>Hadoop Thirdparty source tar available at: ${basedir}/target/hadoop-thirdparty-${project.version}-src.tar.gz</echo>
                    <echo/>
                  </target>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>sign</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>${maven-gpg-plugin.version}</version>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>releasedocs</id>
      <activation>
        <activeByDefault>false</activeByDefault>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>exec-maven-plugin</artifactId>
            <inherited>false</inherited>
            <executions>
              <execution>
                <id>releasedocs</id>
                <phase>pre-site</phase>
                <goals>
                  <goal>exec</goal>
                </goals>
                <configuration>
                  <executable>${basedir}/dev-support/bin/releasedocmaker</executable>
                  <arguments>
                    <argument>--index</argument>
                    <argument>--license</argument>
                    <argument>--outputdir</argument>
                    <argument>${basedir}/src/site/markdown/release</argument>
                    <argument>--project</argument>
                    <argument>HADOOP</argument>
                    <argument>--projecttitle</argument>
                    <argument>Apache Hadoop Third-party Libs</argument>
                    <argument>--usetoday</argument>
                    <argument>--fileversions</argument>
                    <argument>--dirversions</argument>
                    <argument>--version</argument>
                    <argument>thirdparty-${project.version}</argument>
                  </arguments>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-clean-plugin</artifactId>
            <configuration>
              <filesets>
                <fileset>
                  <directory>src/site/markdown/release</directory>
                  <includes>
                    <include>${project.version}/</include>
                    <include>index.md</include>
                  </includes>
                  <followSymlinks>false</followSymlinks>
                </fileset>
              </filesets>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

</project>
