package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * GoodsServicePlan Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-25 16:26:18
 */
@TableName(value = "pmsx_goods_service_plan")
@ApiModel(value = "GoodsServicePlan对象", description = "物资/服务计划表")
@Data
public class GoodsServicePlan extends ObjectEntity implements Serializable{

    /**
     * 物资服务计划编号
     */
    @ApiModelProperty(value = "物资服务计划编号")
    @TableField(value = "number" )
    private String number;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField(value = "name" )
    private String name;


    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @TableField(value = "project_id" )
    private String projectId;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @TableField(value = "description" )
    private String description;

    /**
     * 类型对应的字典编码
     */
    @ApiModelProperty(value = "类型对应的字典编码")
    @TableField(value = "type_code" )
    private String typeCode;

    /**
     * 物资服务编码
     */
    @ApiModelProperty(value = "物资服务编码")
    @TableField(value = "goods_service_number" )
    private String goodsServiceNumber;

    /**
     * 规格型号
     */
    @ApiModelProperty(value = "规格型号")
    @TableField(value = "norms_model" )
    private String normsModel;

    /**
     * 服务期限
     */
    @ApiModelProperty(value = "服务期限")
    @TableField(value = "service_term" )
    private Integer serviceTerm;

    /**
     * 计量单位对应数据字典
     */
    @ApiModelProperty(value = "计量单位对应数据字典")
    @TableField(value = "unit_code" )
    private String unitCode;

    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    @TableField(value = "demand_amount" )
    private BigDecimal demandAmount;

    /**
     * 总入库数量
     */
    @ApiModelProperty(value = "总入库数量")
    @TableField(value = "total_store_amount" )
    private BigDecimal totalStoreAmount;

    /**
     * 需求时间
     */
    @ApiModelProperty(value = "需求时间")
    @TableField(value = "demand_time" )
    private Date demandTime;

    /**
     * 采购计划编号
     */
    @ApiModelProperty(value = "采购计划编号")
    @TableField(value = "buy_plan_id" )
    private String buyPlanId;

    /**
     * 需求人ID
     */
    @ApiModelProperty(value = "需求人ID")
    @TableField(value = "demand_person_id" )
    private String demandPersonId;

}
