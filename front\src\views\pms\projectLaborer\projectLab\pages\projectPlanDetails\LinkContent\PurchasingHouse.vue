<script setup lang="ts">
import { Modal, Space } from 'ant-design-vue';
import {
  h, ref,
} from 'vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import Api from '/@/api';
import { BasicButton, OrionTable } from 'lyra-component-vue3';
import { get } from 'lodash-es';
import {
  usePurchasingHouseModal,
} from './hooks/usePurchasingHouseModal';
import { parsePriceByNumber } from '/@/views/pms/purchaseManage/purchaseModule/utils';

const props = withDefaults(defineProps<{
  parentData:Record<string, any>
}>(), {
  parentData: () => ({}),
});

const tableRef = ref();
const selectKeys = ref([]);
const tableOptions = {
  showToolButton: false,
  isSpacing: true,
  showSmallSearch: false,
  showTableSetting: false,
  rowSelection: {
    onChange(val) {
      selectKeys.value = val;
    },
  },
  columns: [
    {
      title: '采购申请行号',
      dataIndex: 'projectCode',
      width: 120,
      fixed: 'left',
    },
    {
      title: '总账科目',
      dataIndex: 'generalLedgerSubject',
      width: 250,
    },
    {
      title: '资产名称',
      dataIndex: 'projectIdName',
      width: 380,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      width: 80,
    },
    {
      title: '数量',
      dataIndex: 'requiredQuantity',
      width: 80,
    },
    {
      title: '交货时间',
      dataIndex: 'deliveryTime',
      width: 120,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '单价',
      dataIndex: 'unitPrice',
      width: 120,
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '总价',
      dataIndex: 'totalPrice',
      width: 120,
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '本位币金额',
      dataIndex: 'localCurrencyAmount',
      width: 100,
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '成本中心',
      dataIndex: 'costCenter',
      width: 230,
    },
    {
      title: '物料',
      dataIndex: 'item',
      width: 100,
    },
    {
      title: '物料组',
      dataIndex: 'itemGroup',
      width: 100,
    },
    {
      title: '内部订单',
      dataIndex: 'internalOrder',
      width: 100,
    },
    {
      title: '关联预算',
      dataIndex: 'localCurrencyAmount',
      width: 100,
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 100,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '移除',
      onClick(record) {
        Modal.confirm({
          title: '移除警告',
          icon: h(ExclamationCircleOutlined),
          content: '确定移除这条数据',
          async onOk() {
            try {
              const targetKey = [record.id];
              return await handleRemoveRequest(targetKey);
            } catch {
            }
          },
          onCancel() {},
        });
      },
    },
  ],
  api: (params) => new Api('/pms/projectScheme/relation/ncFormpurchase/lists')
    .fetch(params, get(props.parentData, 'id'), 'POST'),
};

function updateTable() {
  tableRef.value?.reload?.();
}
function handleAddPurchasing() {
  usePurchasingHouseModal({
    ...(get(props, 'parentData') ?? {}),
    title: '添加采购行',
  }, updateTable);
}
function handleDeletePurchasingRows() {
  Modal.confirm({
    title: '移除警告',
    icon: h(ExclamationCircleOutlined),
    content: '确定移除选中的数据',
    async onOk() {
      try {
        const targetKeys = tableRef.value?.getSelectRowKeys();
        return await handleRemoveRequest(targetKeys);
      } catch {
      }
    },
    onCancel() {},
  });
}
function handleRemoveRequest(idxs) {
  return new Promise((resolve) => {
    new Api('/pms/projectScheme/relation/ncFormpurchase/remove')
      .fetch({
        toId: get(props, 'parentData.id'),
        fromIds: idxs,
      }, '', 'DELETE')
      .then((res) => {
        updateTable();
        resolve(res);
      });
  });
}

</script>

<template>
  <div class="purchasing-house">
    <OrionTable
      ref="tableRef"
      class="scroll-table"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <Space
          :size="12"
          align="center"
        >
          <BasicButton
            type="primary"
            icon="sie-icon-tianjiaxinzeng"
            @click="handleAddPurchasing"
          >
            添加
          </BasicButton>
          <BasicButton
            :disabled="!selectKeys.length"
            icon="sie-icon-shanchu"
            @click="handleDeletePurchasingRows"
          >
            移除
          </BasicButton>
        </Space>
      </template>
    </OrionTable>
  </div>
</template>

<style scoped lang="less">

</style>