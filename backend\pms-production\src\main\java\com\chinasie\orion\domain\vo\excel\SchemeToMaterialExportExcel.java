package com.chinasie.orion.domain.vo.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 大修管理 - 物资入场离场信息导出 excel
 *
 * <AUTHOR>
 * @since 2024年9月27日
 */
@ApiModel(value = "SchemeToMaterialExportExcel", description = "大修管理 - 物资入场离场信息导出 excel")
@Data
@ExcelIgnoreUnannotated
public class SchemeToMaterialExportExcel implements Serializable {

    @ApiModelProperty(value = "物资名称")
    private String materialName;

    @ApiModelProperty(value = "materialManage id")
    private String materialId;

    @ApiModelProperty(value = "基地编码")
    private String baseCode;

    @ApiModelProperty(value = "大修伦次")
    private String repairRound;

    @ApiModelProperty(value = "资产类型")
    private String assetType;

    @ExcelProperty("资产类型")
    @ApiModelProperty(value = "资产类型名称")
    private String assetTypeName;

    @ExcelProperty(value = "资产编码/条码")
    @ApiModelProperty(value = "物资编码（固定资产编码）")
    private String materialNumber;

    @ApiModelProperty(value = "产品编码（预留字段，暂时还没上）")
    private String productCode;

    @ExcelProperty(value = "资产名称")
    @ApiModelProperty(value = "资产名称")
    private String assetName;

    @ExcelProperty(value = "规格型号")
    @ApiModelProperty(value = "规格型号")
    private String specificationModel;

    @ExcelProperty(value = "入场数量")
    @ApiModelProperty(value = "入场数量")
    private Integer demandNum;

    @ExcelProperty(value = "成本中心名称")
    @ApiModelProperty(value = "成本中心名称")
    private String costCenterName;

    @ExcelProperty(value = "库存数量")
    @ApiModelProperty(value = "库存数量")
    private Integer stockNum;

    @ExcelProperty(value = "需求数量")
    @ApiModelProperty(value = "需求数量")
    private Integer requiredNum;

    @ColumnWidth(35)
    @ExcelProperty(value = "计划入场日期")
    @ApiModelProperty(value = "计划入场日期")
    private Date inDate;

    @ColumnWidth(35)
    @ExcelProperty(value = "计划离场日期")
    @ApiModelProperty(value = "计划离场日期")
    private Date outDate;

    @ColumnWidth(35)
    @ExcelProperty(value = "实际入场日期")
    @ApiModelProperty(value = "实际入场日期")
    private Date actInDate;

    @ColumnWidth(35)
    @ExcelProperty(value = "实际离场日期")
    @ApiModelProperty(value = "实际离场日期")
    private Date actOutDate;

    @ExcelProperty(value = "是否合格", converter = ExcelYesOrNoConverter.class)
    @ApiModelProperty(value = "是否合格")
    private Boolean isPass;

    @ExcelProperty(value = "进场倒计时（天）")
    @ApiModelProperty(value = "进场倒计时（天）")
    private long inDays;

    @ExcelProperty(value = "物资状态", converter = MaterialStatusConverter.class)
    @ApiModelProperty(value = "物资状态")
    private Integer status;

    @ExcelProperty(value = "参与作业数")
    @ApiModelProperty(value = "参与作业数")
    private Integer jobNum;

    @ExcelProperty(value = "是否需要检定", converter = ExcelYesOrNoConverter.class)
    @ApiModelProperty(value = "是否需要检定")
    private Boolean isVerification;

    @ColumnWidth(35)
    @ExcelProperty(value = "下次检定日期")
    @ApiModelProperty(value = "下次检定日期")
    private Date nextVerificationDate;

    @ExcelProperty(value = "物资所在地")
    @ApiModelProperty(value = "物资所在地")
    private String baseName;

    @ExcelProperty(value = "是否计量器具", converter = ExcelYesOrNoConverter.class)
    @ApiModelProperty(value = "是否计量器具")
    private Boolean isMetering;

    @ExcelProperty(value = "实际入场数量")
    @ApiModelProperty(value = "实际入场数量")
    private Integer actDemandNum;

    @ExcelProperty(value = "责任人工号")
    @ApiModelProperty(value = "责任人工号")
    private String rspUserNo;

    @ExcelProperty(value = "责任人姓名")
    @ApiModelProperty(value = "责任人姓名")
    private String rspUserName;

    @ExcelProperty(value = "是否向电厂报备", converter = ExcelYesOrNoConverter.class)
    @ApiModelProperty(value = "是否向电厂报备")
    private Boolean isReport;

    @ExcelProperty(value = "检定是否超期", converter = ExcelYesOrNoConverter.class)
    @ApiModelProperty(value = "检定是否超期")
    private Boolean isOverdue;

    @ExcelProperty(value = "实际离场数量")
    @ApiModelProperty(value = "实际离场数量")
    private Integer actOutNum;

    @ApiModelProperty(value = "出库原因")
    private String outReason;

    @ExcelProperty(value = "出库原因")
    @ApiModelProperty(value = "出库原因")
    private String outReasonName;

    @ExcelProperty(value = "物资去向")
    @ApiModelProperty(value = "物资去向")
    private String materialDestination;

    @ExcelProperty(value = "是否再次入场", converter = ExcelYesOrNoConverter.class)
    @ApiModelProperty(value = "是否再次入场")
    private Boolean isAgainIn;
}
