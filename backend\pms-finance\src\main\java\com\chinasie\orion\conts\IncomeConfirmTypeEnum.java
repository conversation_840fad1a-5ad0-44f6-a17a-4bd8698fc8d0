package com.chinasie.orion.conts;

public enum IncomeConfirmTypeEnum {

    ADVANCE_PAYMENT_INVOICE("advance_payment_invoice","预收款开票"),
    PROGRESS_PAYMENT_INVOICE ("progress_payment_invoice", "进度款开票"),
    CANCEL_REOPEN ("cancel_reopen", "作废重开"),
    INVALIDATION_INVOICE("invalidation_invoice", "发票作废"),
    PROVISIONAL_INCOME("provisional_income", "暂估收入"),
    OTHER_INCOME_CONFIRM("other_income_confirm", "其他");
    private String value;

    private String description;

    IncomeConfirmTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public String getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }
}
