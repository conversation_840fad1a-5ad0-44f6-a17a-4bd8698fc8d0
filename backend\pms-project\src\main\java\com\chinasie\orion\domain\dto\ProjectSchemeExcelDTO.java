package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


/**
 * ProjectSchemeExcelDTO
 *
 * @author: yangFy
 * @date: 2023/4/21 18:33
 * @description:
 * <p>
 * 项目计划文档映射entity 对象DTO
 * </p>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectSchemeExcelDTO {
    @ExcelIgnore
    private String id;
    @ExcelIgnore
    private String parentId;
    @ExcelIgnore
    private Long order;
    @ExcelIgnore
    private String projectNumber;

    @ExcelProperty("序号*")
    private String sort;
    @ExcelProperty("计划编号*")
    @ColumnWidth(20)
    private String schemeNumber;
    @ExcelProperty("计划名称*")
    @ColumnWidth(20)
    private String name;
    @ExcelProperty(value = "层级名称")
    @ColumnWidth(20)
    private String levelName;
    @ExcelIgnore
    private String schemeType;
    @ExcelProperty("责任处室*")
    @ColumnWidth(20)
    private String rspSubDeptName;
    @ExcelProperty("责任人*")
    @ColumnWidth(20)
    private String rspUserName;
    @ExcelProperty("开始时间*")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @ColumnWidth(20)
    private Date beginTime;
    @ExcelProperty("结束时间*")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @ColumnWidth(20)
    private Date endTime;
    @ExcelProperty(value = "计划状态")
    @ColumnWidth(20)
    private String statusName;
    @ExcelProperty(value = "计划情况名称" )
    @ColumnWidth(20)
    private String circumstanceName;
    @ExcelProperty(value = "实际开始时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @ColumnWidth(20)
    private Date actualBeginTime;
    @ExcelProperty(value = "实际结束时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @ColumnWidth(20)
    private Date actualEndTime;
    @ExcelProperty(value = "计划下发时间")
    @ColumnWidth(20)
    private Date issueTime;
    @ExcelProperty("计划描述说明*")
    @ColumnWidth(20)
    private String schemeDesc;

    @ExcelProperty("项目计划记录")
    @ColumnWidth(20)
    private String schemeProgressRecord;

    @ExcelProperty("参与人")
    @ColumnWidth(15)
    private String participantNames;
}
