package com.chinasie.orion.domain.vo.lifecycle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @className ProjectLifeCycleNodeActionVO
 * @description 生命周期节点操作VO
 * @since 2023/10/28
 */
@Data
@ApiModel(value = "ProjectLifeCycleNodeActionVO", description = "生命周期节点操作VO")
public class ProjectLifeCycleNodeActionVO {
    @ApiModelProperty
    private String key;

    @ApiModelProperty(value = "操作名")
    private String label;

    @ApiModelProperty(value = "操作定义")
    private String href;

    @ApiModelProperty(value = "是否有操作权限")
    private Boolean hasAuth;
}
