package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.entity.ProjectPlace;
import com.chinasie.orion.domain.vo.BaseAreaVO;
import com.chinasie.orion.domain.vo.PlaceVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.PMIService;
import com.chinasie.orion.repository.ProjectRepository;
import com.chinasie.orion.service.ProjectBaseInformationService;
import com.chinasie.orion.service.ProjectPlaceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("projectBaseInformationService")
public class ProjectBaseInformationServiceImpl implements ProjectBaseInformationService {

    @Resource
    private ProjectRepository projectRepository;
    @Autowired
    private PMIService pmiService;

    @Autowired
    private ProjectPlaceService projectPlaceService;
    @Override
    public Boolean applyPLM(String id) {
        Project project = projectRepository.selectById(id);
        if (ObjectUtil.isNull(project)){
            throw new BaseException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST,"项目不存在请刷新页面！");
        }
        // todo 需要调用PLM接口获取编码
        return true;
    }

    @Override
    public Boolean syncApproval(String id){
        Project project = projectRepository.selectById(id);
        if (ObjectUtil.isNull(project)){
            throw new BaseException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST,"项目不存在请刷新页面！");
        }
        // todo 需要调用PLM接口获取编码
        return true;
    }

    @Override
    public List<PlaceVO> getPlace(String id){
        List<ProjectPlace> places = projectPlaceService.getPlaceByProjectId(id);
        if (CollectionUtil.isEmpty(places)){
            return new ArrayList<>();
        }
        List<String> areaIds = places.stream().map(ProjectPlace::getAreaId).distinct().collect(Collectors.toList());
        ResponseDTO<List<BaseAreaVO>> byAreaIds;
        try {
            byAreaIds = pmiService.getByAreaIds(areaIds);
        } catch (Exception e) {
            throw new PMSException(PMSErrorCode.PMS_ERR,"获取base数据失败！");
        }
        List<BaseAreaVO> result = byAreaIds.getResult();
        if (CollectionUtil.isEmpty(result)){
            return new ArrayList<>();
        }
        Map<String, BaseAreaVO> areaVOMap = result.stream().collect(Collectors.toMap(BaseAreaVO::getAreaId, b -> b));
        List<PlaceVO> placeVOS = new ArrayList<>();
        for (ProjectPlace place : places) {
            String areaId = place.getAreaId();
            BaseAreaVO baseAreaVO = areaVOMap.get(areaId);
            if (!ObjectUtil.isNull(baseAreaVO)){
                PlaceVO placeVO = new PlaceVO();
                placeVO.setAreaId(areaId);
                placeVO.setArea(baseAreaVO.getArea());
                placeVOS.add(placeVO);
            }
        }
        return placeVOS;
    }
}
