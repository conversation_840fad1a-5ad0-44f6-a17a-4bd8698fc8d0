package com.chinasie.orion.bo;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.base.api.domain.entity.DeptDO;
import com.chinasie.orion.base.api.domain.entity.DeptUserDO;
import com.chinasie.orion.base.api.repository.DeptDOMapper;
import com.chinasie.orion.base.api.repository.DeptUserDOMapper;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.user.DeptUserRelationVO;
import com.chinasie.orion.sdk.domain.vo.user.UserPartTimeVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.DeptUserHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Component
public class UserDeptBo {

    @Autowired
    private DeptUserHelper deptUserHelper;
    @Autowired
    private DeptRedisHelper deptRedisHelper;
    @Resource
    private DeptDOMapper deptDOMapper;
    @Resource
    private DeptUserDOMapper deptUserDOMapper;

    public UserPartTimeVO getUserDeptInfo(String userId) {
        List<DeptUserRelationVO> deptUserRelationList = this.deptUserHelper.getDeptUserRelationByUserId(CurrentUserHelper.getOrgId(), userId);
        if (CollectionUtil.isEmpty(deptUserRelationList)) {
            return null;
        }
        UserPartTimeVO userPartTimeVO = new UserPartTimeVO();
        Optional<String> first = deptUserRelationList.stream().filter(
                (relation) -> relation.getPartTime() != null && !relation.getPartTime()).map(DeptUserRelationVO::getOrgId).distinct().filter(CharSequenceUtil::isNotBlank).findFirst();
        if (first.isEmpty()) {
            return null;
        } else {
            String deptId = first.get(); // 获取到当前人的部门id
            DeptVO deptVO = deptRedisHelper.getDeptById(deptId);
            if (Objects.nonNull(deptVO)) {
                String type = deptVO.getType();
                String parentId = deptVO.getParentId();
                if ("30".equals(type)) {// 说明是所级部门
                    userPartTimeVO.setDeptId(deptVO.getId());
                    userPartTimeVO.setDeptCode(deptVO.getDeptCode());
                    userPartTimeVO.setDeptName(deptVO.getName());
                    DeptVO parent = deptRedisHelper.getDeptById(parentId);
                    if (Objects.nonNull(parent)) {
                        userPartTimeVO.setOrgId(parent.getId());
                        userPartTimeVO.setOrgCode(parent.getDeptCode());
                        userPartTimeVO.setOrgName(parent.getName());
                    }
                } else {
                    userPartTimeVO.setDeptId(deptVO.getId());
                    userPartTimeVO.setDeptCode(deptVO.getDeptCode());
                    userPartTimeVO.setDeptName(deptVO.getName());
                    userPartTimeVO.setOrgId(deptVO.getId());
                    userPartTimeVO.setOrgCode(deptVO.getDeptCode());
                    userPartTimeVO.setOrgName(deptVO.getName());
                }

            }
            return userPartTimeVO;
        }
    }


//    public UserPartTimeVO getUserDeptInfos(List<String> userIds) {
//        LambdaQueryWrapperX<DeptUserDO> deptUserRelationVOLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
//        deptUserRelationVOLambdaQueryWrapperX.in(DeptUserDO::getUserId, userIds);
//        List<DeptUserDO> deptUserRelationList = deptUserDOMapper.selectList(deptUserRelationVOLambdaQueryWrapperX);
//
//        //   List<DeptUserRelationVO> deptUserRelationList = this.deptUserHelper.getde(CurrentUserHelper.getOrgId(), userIds);
//        if (CollectionUtil.isEmpty(deptUserRelationList)) {
//            return null;
//        }
//        List<UserPartTimeVO> userPartTimeVO = new UserPartTimeVO();
//        Optional<String> first = deptUserRelationList.stream().filter(
//                (relation) -> relation.getPartTime() != null && !relation.getPartTime()).map(DeptUserDO::getDeptId).distinct().filter(CharSequenceUtil::isNotBlank).findFirst();
//        if (first.isEmpty()) {
//            return null;
//        } else {
//            String deptId = first.get(); // 获取到当前人的部门id
//            DeptVO deptVO = deptRedisHelper.getDeptById(deptId);
//            if (Objects.nonNull(deptVO)) {
//                String type = deptVO.getType();
//                String parentId = deptVO.getParentId();
//                if ("30".equals(type)) {// 说明是所级部门
//                    userPartTimeVO.setDeptId(deptVO.getId());
//                    userPartTimeVO.setDeptCode(deptVO.getDeptCode());
//                    userPartTimeVO.setDeptName(deptVO.getName());
//                    DeptVO parent = deptRedisHelper.getDeptById(parentId);
//                    if (Objects.nonNull(parent)) {
//                        userPartTimeVO.setOrgId(parent.getId());
//                        userPartTimeVO.setOrgCode(parent.getDeptCode());
//                        userPartTimeVO.setOrgName(parent.getName());
//                    }
//                } else {
//                    userPartTimeVO.setDeptId(deptVO.getId());
//                    userPartTimeVO.setDeptCode(deptVO.getDeptCode());
//                    userPartTimeVO.setDeptName(deptVO.getName());
//                    userPartTimeVO.setOrgId(deptVO.getId());
//                    userPartTimeVO.setOrgCode(deptVO.getDeptCode());
//                    userPartTimeVO.setOrgName(deptVO.getName());
//                }
//
//            }
//            return userPartTimeVO;
//        }
//    }

    /**
     * 根据用户id查询该用户所属20级别部门id
     */
    public String get20DeptId(String userId) {
        if (StrUtil.isNotBlank(userId)) {
            //根据部门id查询子级部门
            LambdaQueryWrapperX<DeptDO> sql = new LambdaQueryWrapperX<>(DeptDO.class);
            sql.select(DeptDO::getId, DeptDO::getType, DeptDO::getChain);
            sql.leftJoin(DeptUserDO.class, DeptUserDO::getDeptId, DeptDO::getId);
            sql.eq(DeptUserDO::getUserId, userId);
            sql.last("LIMIT 1");
            DeptDO dept = deptDOMapper.selectOne(sql);
            if (ObjectUtil.isNotNull(dept)) {
                String type = dept.getType();
                if (StrUtil.isNotBlank(type)) {
                    if ((StrUtil.equals(type, "10")) || (StrUtil.equals(type, "20"))) {
                        return dept.getId();
                    } else {
                        LambdaQueryWrapperX<DeptDO> sqlx = new LambdaQueryWrapperX<>(DeptDO.class);
                        sqlx.select(DeptDO::getId);
                        sqlx.eq(DeptDO::getType, "20");
                        sqlx.apply("FIND_IN_SET(id,'" + dept.getChain() + "' ) > 0 ");
                        sqlx.last("LIMIT 1");
                        DeptDO deptDO = deptDOMapper.selectOne(sqlx);
                        if (ObjectUtil.isNotNull(deptDO)) {
                            return deptDO.getId();
                        }
                    }
                }
            }
        }
        return null;
    }
}
