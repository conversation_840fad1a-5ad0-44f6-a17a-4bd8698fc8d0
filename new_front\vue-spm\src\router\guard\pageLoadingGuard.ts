import type { Router } from 'vue-router';
import { useAppStoreWidthOut } from '/@/store/modules/app';
import { useUserStoreWidthOut } from '/@/store/modules/user';
import { useTransitionSetting } from '/@/hooks/setting/useTransitionSetting';
import { unref } from 'vue';
import { guardQueue } from '/@/router';

export function createPageLoadingGuard(router: Router) {
  const userStore = useUserStoreWidthOut();
  const appStore = useAppStoreWidthOut();
  const { getOpenPageLoading } = useTransitionSetting();
  const beforeGuard = router.beforeEach(async (to) => {
    if (!userStore.getToken) {
      return true;
    }
    if (to.meta.loaded) {
      return true;
    }

    if (unref(getOpenPageLoading)) {
      appStore.setPageLoadingAction(true);
      return true;
    }

    return true;
  });
  const afterGuard = router.afterEach(async () => {
    if (unref(getOpenPageLoading)) {
      setTimeout(() => {
        appStore.setPageLoading(false);
      }, 220);
    }
    return true;
  });

  guardQueue.push(beforeGuard);
  guardQueue.push(afterGuard);
}
