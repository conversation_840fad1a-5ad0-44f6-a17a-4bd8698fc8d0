package com.chinasie.orion.domain.dto;

import com.chinasie.orion.file.api.domain.dto.FileDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;
import java.util.List;

/**
 * ProjectApprovalMilestone Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-07 16:23:23
 */
@ApiModel(value = "ProjectApprovalMilestoneDTO对象", description = "项目立项里程碑")
@Data
public class ProjectApprovalMilestoneDTO extends ObjectDTO implements Serializable{

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 任务层级
     */
    @ApiModelProperty(value = "任务层级")
    private String taskLevel;

    /**
     * 密级
     */
    @ApiModelProperty(value = "密级")
    private String secrecyLevel;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    private Date beginTime;

    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    private Date endTime;

    /**
     * 计划状况
     */
    @ApiModelProperty(value = "计划状况")
    private Integer circumstance;

    /**
     * 计划状态
     */
    @ApiModelProperty(value = "计划状态")
    private Integer schemeStatus;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private String resPerson;

    /**
     * 责任科室
     */
    @ApiModelProperty(value = "责任科室")
    private String resOffice;

    /**
     * 责任部门
     */
    @ApiModelProperty(value = "责任部门")
    private String resDept;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 项目立项id
     */
    @ApiModelProperty(value = "项目立项id")
    private String approvalId;

    /**
     * 项目里程碑id
     */
    @ApiModelProperty(value = "项目里程碑id")
    private String milestoneId;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 层级
     */
    @ApiModelProperty(value = "层级")
    private Integer level;


    /**
     * 计划类型
     */
    @ApiModelProperty(value = "计划类型")
    private String schemeType;


    /**
     * 计划活动项
     */
    @ApiModelProperty(value = "计划活动项")
    private String schemeActivity;
    @ApiModelProperty(value = "计划活动项集合")
    private List<String> schemeActivityList;


    /**
     * 工期
     */
    @ApiModelProperty(value = "工期")
    private Integer durationDays;


    /**
     * 责任科室
     */
    @ApiModelProperty(value = "责任科室（责任处室）")
    private String rspSectionId;


    /**
     * 参与人
     */
    @ApiModelProperty(value = "参与人")
    private String participantUsers;
    @ApiModelProperty(value = "参与人集合")
    private List<String> participantUsersList;


    /**
     * 计划描述
     */
    @ApiModelProperty(value = "计划描述")
    private String schemeDesc;

    /**
     * 模板id
     */
    @ApiModelProperty(value = "模板id")
    private String templateId;

    /**
     * 支撑性材料
     */
    @ApiModelProperty(value = "文件")
    private List<FileDTO> attachments;

}

