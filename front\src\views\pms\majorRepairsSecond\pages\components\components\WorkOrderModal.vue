<script setup lang="ts">
import { message, Table, Transfer } from 'ant-design-vue';
import { CheckCircleFilled, ExclamationCircleOutlined } from '@ant-design/icons-vue';
import {
  computed, h, nextTick, onMounted, reactive, ref,
} from 'vue';
import Api from '/@/api';
import {
  BasicButton,
  Icon,
  IOpenBasicSelectModalProps,
  openBasicSelectModal,
  randomString,
  treeMap,
  treeToList,
} from 'lyra-component-vue3';
import { filter } from '/@/utils/helper/treeHelper';
import { cloneDeep } from 'lodash-es';
import { useWorkOrderImport } from '/@/views/pms/majorRepairsSecond/hooks/useImportAndExport';

const props = defineProps<{
  record: Record<string, any>
  repairRound: string
}>();

const leftDataSource = ref<any[]>([]);
const rightHasList = ref<any[]>([]);
const expandedRowKeys = ref<string[]>([]);
const addSelectList = ref([]);
const loading = ref<boolean>(false);
const { importApi, OrderImportRender } = useWorkOrderImport();
const rowsList = ref([]);

const rightDataSource = computed(() => {
  const leftToRightList = treeToList(leftDataSource.value).filter((node) => node.rightSelected && node.number);
  const allList = rightHasList.value.filter((item) => !item?.deleted).concat(leftToRightList).concat(addSelectList.value);
  const set = new Set() as any;
  return filter(allList, (node) => !set.has(node.key) && set.add(node.key));
});

const rightOrderList = computed(() => treeToList(rightDataSource.value).filter((item) => item.number));

async function getTree() {
  loading.value = true;
  try {
    await getHasList();
    const result = await new Api('/pms/centerJobManage/treeList').fetch({
      repairRound: props?.repairRound,
    }, '', 'POST');
    leftDataSource.value = result?.map((item) => {
      const itemObj = cloneDeep(item);
      itemObj.key = randomString();
      expandedRowKeys.value.push(itemObj.key);
      delete itemObj?.centerList;
      return {
        ...itemObj,
        children: item?.centerList?.map((c) => {
          const cObj = cloneDeep(c);
          cObj.key = randomString();
          expandedRowKeys.value.push(cObj.key);
          delete cObj?.jobList;
          return {
            ...cObj,
            children: c?.jobList?.map((j) => ({
              ...j,
              key: j.number,
              rightSelected: rightHasList.value.map((v) => v.key).includes(j.number),
            })),
          };
        }),
      };
    }) || [];
  } finally {
    loading.value = false;
  }
}

async function getHasList() {
  const result = await new Api('/pms/centerJobManage/used/treeList').fetch({
    repairOrgId: props?.record?.data?.id,
    repairRound: props?.repairRound,
  }, '', 'POST');
  rightHasList.value = result?.map((item) => ({
    ...item,
    key: item.number,
  })) || [];
}

function importCb(list: any[]) {
  list.forEach((item) => {
    addSelectList.value.push({
      ...item,
      key: item.number,
      isAdd: true,
    });
  });
}

onMounted(() => {
  getTree();
});

const columns = [
  {
    title: '组织架构',
    dataIndex: 'code',
    customCell(record) {
      if (record.number && record.name && !record.isAdd) {
        return {
          style: {
            display: 'flex',
          },
        };
      }
    },
    customRender({ text, record }) {
      if ('number' in record) {
        if (!('isAdd' in record) && 'name' in record) {
          return h('div', {
            style: {
              display: 'inline-flex',
              flexDirection: 'column',
              flexGrow: '1',
              width: '0',
            },
          }, [
            h('div', { class: 'flex-te' }, record.name),
            h('div', {
              class: 'flex-te',
              style: {
                fontSize: '12px',
                color: '#ccc',
              },
            }, `工单号：${record.number}`),
          ]);
        }
        return `工单号：${record.number}`;
      }
      return text;
    },
  },
  {
    title: '状态',
    dataIndex: 'matchUp',
    width: 50,
    customRender({ text, record }) {
      if (!('matchUp' in record)) return;
      if (text) {
        return h(CheckCircleFilled, {
          style: {
            color: '#52c41a',
            fontSize: '24px',
          },
        });
      }
      return h(ExclamationCircleOutlined, {
        style: {
          color: '#ff4d4f',
          fontSize: '24px',
        },
      });
    },
  },
];

function expandIcon({ expanded, record, onExpand }) {
  if (!record?.children?.length) {
    return h('span', {
      style: {
        display: 'inline-block',
        marginRight: '8px',
        marginTop: '2px',
        width: '16px',
      },
    });
  }
  if (expanded) {
    return h(Icon, {
      onClick(e) {
        onExpand(record, e);
      },
      style: {
        cursor: 'pointer',
        width: '16px',
        height: '16px',
        marginRight: '8px',
        marginTop: '2px',
      },
      icon: 'fa-angle-down',
      size: 20,
    });
  }
  return h(Icon, {
    onClick(e) {
      onExpand(record, e);
    },
    style: {
      cursor: 'pointer',
      width: '16px',
      height: '16px',
      marginRight: '8px',
      marginTop: '2px',
    },
    icon: 'fa-angle-right',
    size: 20,
  });
}

const selectedKeysAll = reactive({
  left: [],
  right: [],
});

const getRowSelection = ({
  direction,
  onItemSelect,
}: Record<string, any>) => ({
  hideSelectAll: direction === 'left',
  checkStrictly: direction === 'right',
  getCheckboxProps: (item: any) => ({
    disabled: direction === 'left' && ((('selected' in item) && item.selected === 1) || item?.rightSelected || item?.children?.every((c) => c?.rightSelected || !c?.number || c?.selected === 1)),
  }),
  onSelect({ key }: any, selected: boolean, selectedNodes: any[]) {
    selectedKeysAll[direction] = selectedNodes.map((item) => item?.key);
    onItemSelect(key, selected);
  },
  onSelectAll(selected: boolean, selectedNodes: any[]) {
    selectedKeysAll[direction] = selectedNodes.map((item) => item?.key);
    selectedKeysAll[direction].forEach((key) => {
      onItemSelect(key, selected);
    });
  },
  selectedRowKeys: selectedKeysAll[direction],
});

function handleChange() {
  treeMap(leftDataSource.value, {
    conversion: (node) => {
      if (selectedKeysAll.left.includes(node?.key) && node?.number) {
        node.rightSelected = true;
      }
      return node;
    },
  });
  nextTick(() => {
    selectedKeysAll.left = [];
  });
}

const leftKeyword = ref('');
const rightKeyword = ref('');

function handleSearch(direction: string, keyword: string) {
  if (direction === 'left') {
    leftKeyword.value = keyword;
  } else {
    rightKeyword.value = keyword;
  }
}

function format(direction: string) {
  switch (direction) {
    case 'left':
      return filter(leftDataSource.value, (item) => filterOption(leftKeyword.value, item));
    case 'right':
      return filter(rightDataSource.value, (item) => filterOption(rightKeyword.value, item));
  }
}

function handleBatchDelete() {
  if (selectedKeysAll.right.length === 0) {
    return message.info('请选择需要删除的数据');
  }
  treeMap(leftDataSource.value, {
    conversion: (node) => {
      if (selectedKeysAll.right.includes(node.key)) {
        delete node.rightSelected;
      }
      return node;
    },
  });
  addSelectList.value = addSelectList.value.filter((node) => !selectedKeysAll.right.includes(node.key));
  rightHasList.value = rightHasList.value.map((node) => {
    if (selectedKeysAll.right.includes(node.key)) {
      node.deleted = true;
    }
    return node;
  });
  selectedKeysAll.right = [];
}

function filterOption(inputValue, option) {
  return option?.number?.indexOf(inputValue) > -1;
}

// 添加工单
function handleOpenAddModal() {
  const options: IOpenBasicSelectModalProps = {
    title: '选择作业',
    selectType: 'checkbox',
    isSearchBlur: true,
    searchLength: 8,
    tableColumns: [
      {
        title: '工单号',
        dataIndex: 'number',
        width: 130,
      },
      {
        title: '作业名称',
        dataIndex: 'name',
      },
      {
        title: '系统条件',
        dataIndex: 'phase',
        width: 80,
      },
      {
        title: '大修轮次',
        dataIndex: 'repairRound',
        width: 80,
      },
    ],
    async tableApi(params) {
      const keyword = params.searchConditions?.[0]?.[0]?.values?.[0] || '';
      const result = await new Api('/icm/job/manage/page').fetch({
        keyword,
        pageNum: params?.pageNum,
        pageSize: params?.pageSize,
        repairRound: props?.repairRound,
      }, '', 'POST');
      rowsList.value = result?.content;
      return {
        ...result,
        content: result?.content.map((item) => ({
          ...item,
          id: item.number,
          key: item.number,
        })) || [],
      };
    },
    tableSlots: {
      // @ts-ignore
      toolbarRight(props) {
        return h(BasicButton, {
          type: 'primary',
          style: {
            marginRight: '0',
            marginLeft: '10px',
          },
          disabled: rowsList.value.length > 0,
          onClick() {
            props.customAddRow({
              id: randomString(),
              number: props.keyword,
              name: props.keyword,
              key: props.keyword,
              matchUp: 0,
              isAdd: true,
            });
          },
        }, '仍然添加');
      },
    },
    onOk(value) {
      addSelectList.value = value;
    },
  };
  openBasicSelectModal(options);
}

defineExpose({
  confirm() {
    return new Promise((resolve, reject) => {
      const addList = rightDataSource.value.filter((item) => !rightHasList.value.map((c) => c.key).includes(item?.key) && item.number && !item.deleted);
      const delNumberList = rightHasList.value.filter((item) => item.deleted).map((item) => item.number);
      if (addList.length === 0 && delNumberList.length === 0) {
        message.info('暂无需要保存的数据');
        return reject();
      }
      const params = {
        addList,
        delNumberList,
        repairOrgId: props?.record?.data?.id,
        repairRound: props?.repairRound,
      };
      new Api('/pms/relationOrgToJob/add/batch').fetch(params, '', 'POST').then(() => {
        resolve('');
      }).catch((e) => {
        reject(e);
      });
    });
  },
});
</script>

<template>
  <div
    class="transfer-wrapper"
  >
    <div class="custom-header left">
      待选择作业
    </div>
    <div class="custom-header right">
      已选择作业（{{ rightOrderList.length }}）
    </div>
    <div class="right-buttons">
      <BasicButton
        type="link"
        @click="handleBatchDelete"
      >
        批量删除
      </BasicButton>
      <BasicButton
        ghost
        type="primary"
        icon="sie-icon-tianjiaxinzeng"
        @click="handleOpenAddModal"
      >
        添加
      </BasicButton>
      <BasicButton
        ghost
        type="primary"
        icon="sie-icon-daoru"
        @click="importApi"
      >
        导入
      </BasicButton>
    </div>

    <Transfer
      :data-source="[]"
      :show-search="true"
      :oneWay="true"
      :titles="[' 待选择作业',' 已选择作业']"
      :filter-option="filterOption"
      :show-select-all="false"
      @change="handleChange"
      @search="handleSearch"
    >
      <template
        #children="{
          direction,
          onItemSelect,
        }"
      >
        <Table
          v-model:expandedRowKeys="expandedRowKeys"
          :row-selection="getRowSelection({
            direction,
            onItemSelect,
          })"
          bordered
          :loading="loading"
          :scroll="{y:380}"
          :pagination="false"
          :expandIcon="expandIcon"
          :columns="columns"
          :data-source="format(direction)"
          size="small"
        />
      </template>
    </Transfer>
  </div>
  <OrderImportRender
    :repairRound="repairRound"
    :cb="importCb"
  />
</template>

<style scoped lang="less">
.transfer-wrapper {
  position: relative;
  padding: 12px 20px;

  .custom-header {
    position: absolute;
    top: 20px;
    left: 32px;
    z-index: 1;

    &.right {
      left: calc(50% + 32px);
    }
  }

  .right-buttons {
    position: absolute;
    top: 16px;
    right: 20px;
    z-index: 1;
  }
}

:deep(.ant-transfer-customize-list .ant-transfer-list) {
  width: 0;
}

:deep(.ant-transfer-list-header-selected) {
  display: none;
}
</style>
