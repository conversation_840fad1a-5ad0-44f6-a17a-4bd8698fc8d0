package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;

/**
 * BasicUserLedger Entity对象
 *
 * <AUTHOR>
 * @since 2024-09-11 09:54:35
 */
@TableName(value = "pmsx_basic_user_ledger")
@ApiModel(value = "BasicUserLedgerEntity对象", description = "技术支持人员台账记录")
@Data

public class BasicUserLedger extends  ObjectEntity  implements Serializable{

    /**
     * 技术支持人员id
     */
    @ApiModelProperty(value = "技术支持人员id")
    @TableField(value = "basic_user_id")
    private String basicUserId;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @TableField(value = "user_name")
    private String userName;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    @TableField(value = "company_name")
    private String companyName;

    /**
     * 用人部门
     */
    @ApiModelProperty(value = "用人部门")
    @TableField(value = "department_name")
    private String departmentName;

    /**
     * 研究所
     */
    @ApiModelProperty(value = "研究所")
    @TableField(value = "institute_name")
    private String instituteName;

    /**
     * 加入本单位时间
     */
    @ApiModelProperty(value = "加入本单位时间")
    @TableField(value = "add_unittime")
    private Date addUnittime;

    /**
     * 到岗时间
     */
    @ApiModelProperty(value = "到岗时间")
    @TableField(value = "add_work_time")
    private Date addWorkTime;

}
