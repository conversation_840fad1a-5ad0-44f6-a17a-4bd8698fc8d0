<script setup lang="ts">
import {
  BasicDrawer, use<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, UploadList,
} from 'lyra-component-vue3';
import { Spin } from 'ant-design-vue';
import { inject, ref, Ref } from 'vue';
import dayjs from 'dayjs';
import RealIncomeForm from './RealIncomeForm.vue';
import Api from '/@/api';

const emits = defineEmits<{
    (e: 'confirm')
}>();

const [register, { closeDrawer, setDrawerProps }] = useDrawerInner(async (openProps) => {
  formData.value = openProps;
  if (openProps.id) {
    await getFormData();
    await getFiles();
    setDrawerProps({
      title: '编辑实收',
    });
    setTimeout(() => {
      formRef.value.setC(formData.value.collectionPoint);
    });
  } else {
    setDrawerProps({
      title: '新增实收',
    });
  }
  visibleForm.value = true;
});

const visibleForm: Ref<boolean> = ref(false);
const projectId = inject('projectId');
const formData: Ref = ref({});
const formRef: Ref = ref();
const attachments: Ref = ref([]);
const loading: Ref<boolean> = ref(false);
const isContinue: Ref<boolean> = ref(false);

// 获取表单数据
async function getFormData() {
  try {
    const result = await new Api('/pms/projectFundsReceived').fetch('', formData.value.id, 'GET');
    formData.value = result || {};
  } finally {
  }
}

// 获取附件列表
async function getFiles() {
  try {
    const result = await new Api('/res/manage/file/new').fetch('', formData.value.id, 'GET');
    attachments.value = result || [];
  } finally {

  }
}

function visibleChange(visible: boolean) {
  if (!visible) {
    visibleForm.value = visible;
    loading.value = false;
  }
}

function onChange(listData) {
  attachments.value = listData;
}

async function onOk() {
  const { receivableDate, saleSate, ...form } = await formRef.value.validate();
  const params = {
    ...form,
    projectId,
    id: formData.value.id,

    attachments: attachments.value,
    saleSate: saleSate ? dayjs(saleSate).format('YYYY-MM-DD') : undefined,
    receivableDate: receivableDate ? dayjs(receivableDate).format('YYYY-MM-DD') : undefined,
  };
  params.collectionPoint = formRef.value.getC();
  loading.value = true;
  try {
    await new Api('/pms/projectFundsReceived').fetch(params, '', params.id ? 'PUT' : 'POST');
    emits('confirm');
    closeDrawer();
  } finally {
    loading.value = false;
  }
}
</script>

<template>
  <BasicDrawer
    v-bind="$attrs"
    width="800px"
    title="编辑实收"
    :onVisibleChange="visibleChange"
    @register="register"
  >
    <template #footer>
      <DrawerFooter
        v-model:isContinue="isContinue"
        :showFooter="true"
        :showContinue="!formData.id"
        :confirm-loading="loading"
        @ok="onOk"
        @close="closeDrawer()"
      />
    </template>
    <RealIncomeForm
      v-if="visibleForm"
      ref="formRef"
      :form-data="formData"
    />

    <UploadList
      v-if="visibleForm"
      class="ml20 mr20"
      type="modal"
      :listData="attachments"
      :onChange="onChange"
    />

    <div
      v-else
      class="w-full h-full flex flex-ac flex-pc"
    >
      <Spin />
    </div>
  </BasicDrawer>
</template>

<style scoped lang="less">

</style>
