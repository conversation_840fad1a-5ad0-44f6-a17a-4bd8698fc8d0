package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * RequirementManageCustContact DTO对象
 *
 * <AUTHOR>
 * @since 2024-09-06 11:00:57
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "RequirementManageCustContactDTO对象", description = "需求管理-客户-联系人")
@Data
@ExcelIgnoreUnannotated
public class RequirementManageCustContactDTO extends ObjectDTO implements Serializable {

    /**
     * 需求管理id，pms_requirement_mangement id
     */
    @ApiModelProperty(value = "需求管理id，pms_requirement_mangement id")
    @ExcelProperty(value = "需求管理id，pms_requirement_mangement id ", index = 0)
    private String requirementId;

    /**
     * 客户联系人id，pms_customer_contact id
     */
    @ApiModelProperty(value = "客户联系人id，pms_customer_contact id")
    @ExcelProperty(value = "客户联系人id，pms_customer_contact id ", index = 1)
    private String custContactId;

    /**
     * 联系人名称
     */
    @ApiModelProperty(value = "联系人名称")
    @ExcelProperty(value = "联系人名称 ", index = 2)
    private String contactName;

    /**
     * 联系人手机号
     */
    @ApiModelProperty(value = "联系人手机号")
    @ExcelProperty(value = "联系人手机号 ", index = 3)
    private String contactPhone;

    /**
     * 联系人类型；business.商务联系人；technology.技术负责人
     */
    @ApiModelProperty(value = "联系人类型；business.商务联系人；technology.技术负责人")
    @ExcelProperty(value = "联系人类型；business.商务联系人；technology.技术负责人 ", index = 4)
    private String contactType;


}
