<template>
  <ConfigProvider :locale="getAntdLocale">
    <AppProvider>
      <RouterView />
    </AppProvider>
  </ConfigProvider>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import { ConfigProvider } from 'ant-design-vue';
import { AppProvider } from '/@/components/Application';
import { useLocale } from '/@/locales/useLocale';
import { useSetTheme } from '/@/utils';

const { getAntdLocale } = useLocale();
dayjs.locale('zh-cn');

useSetTheme();

</script>

<style lang="less">

</style>
