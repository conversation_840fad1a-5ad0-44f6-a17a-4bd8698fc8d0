package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ProjectContractAllInfoDTO;
import com.chinasie.orion.domain.dto.ProjectContractDTO;
import com.chinasie.orion.domain.vo.ProjectContractAllInfoVO;
import com.chinasie.orion.domain.vo.ProjectContractMainInfoVO;
import com.chinasie.orion.domain.vo.ProjectContractVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectContractService;
import com.chinasie.orion.util.CollectionUtils;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * ProjectContract 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24 09:56:13
 */
@RestController
@RequestMapping("/projectContract")
@Api(tags = "项目合同信息")
public class ProjectContractController {

    @Autowired
    private ProjectContractService projectContractService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(
            success = "【{USER{#logUserId}}】查看项目合同详情，业务编号：{#id}",
            type = "ProjectContract",
            subType = "详情",
            bizNo = "{#id}"
    )
    public ResponseDTO<ProjectContractVO> detail(@PathVariable(value = "id") String id) throws Exception {
        ProjectContractVO rsp = projectContractService.detail(id);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 主要详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "主要详情")
    @RequestMapping(value = "/main/{id}", method = RequestMethod.GET)
    @LogRecord(
            success = "【{USER{#logUserId}}】查看项目合同主要详情，业务编号：{#id}",
            type = "ProjectContract",
            subType = "主要详情",
            bizNo = "{#id}"
    )
    public ResponseDTO<ProjectContractMainInfoVO> mainInfo(@PathVariable(value = "id") String id) throws Exception {
        ProjectContractMainInfoVO rsp = projectContractService.mainInfo(id);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 所有信息
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "所有信息")
    @RequestMapping(value = "/all/{id}", method = RequestMethod.GET)
    @LogRecord(
            success = "【{USER{#logUserId}}】查看项目合同所有信息，业务编号：{#id}",
            type = "ProjectContract",
            subType = "所有信息",
            bizNo = "{#id}"
    )
    public ResponseDTO<ProjectContractAllInfoVO> allInfo(@PathVariable(value = "id") String id) throws Exception {
        ProjectContractAllInfoVO rsp = projectContractService.allInfo(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectContractAllInfoDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】新增项目合同",
            type = "ProjectContract",
            subType = "新增",
            bizNo = ""
    )
    public ResponseDTO<ProjectContractAllInfoVO> create(@RequestBody @Validated ProjectContractAllInfoDTO projectContractAllInfoDTO) throws Exception {
        ProjectContractDTO projectContractDTO = projectContractAllInfoDTO.getProjectContractDTO();
        if (projectContractDTO.getIsGuaranteePeriod() && projectContractDTO.getGuaranteeEndDate() == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "预计质保期到期日期不能为空!");
        }
        if (projectContractDTO.getIsGuaranteeMoney() && (projectContractDTO.getGuaranteeAmt() == null || projectContractDTO.getGuaranteeAmt().compareTo(new BigDecimal(0)) < 1)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "质保金额必须大于0!");
        }
        ProjectContractAllInfoVO rsp = projectContractService.create(projectContractAllInfoDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectContractAllInfoDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】编辑项目合同，业务编号：{#projectContractDTO.id}",
            type = "ProjectContract",
            subType = "编辑",
            bizNo = "{#projectContractDTO.id}"
    )
    public ResponseDTO<Boolean> edit(@RequestBody @Validated ProjectContractAllInfoDTO projectContractAllInfoDTO) throws Exception {
        ProjectContractDTO projectContractDTO = projectContractAllInfoDTO.getProjectContractDTO();
        if (projectContractDTO.getIsGuaranteePeriod() && projectContractDTO.getGuaranteeEndDate() == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "预计质保期到期日期不能为空!");
        }
        if (projectContractDTO.getIsGuaranteeMoney() && (projectContractDTO.getGuaranteeAmt() == null || projectContractDTO.getGuaranteeAmt().compareTo(new BigDecimal(0)) < 1)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "质保金额必须大于0!");
        }
        if (!StringUtils.hasText(projectContractDTO.getId())) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "合同id不能为空!");
        }
        Boolean rsp = projectContractService.edit(projectContractAllInfoDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】删除项目合同，业务编号：{ID_LIST{#ids}}",
            type = "ProjectContract",
            subType = "删除",
            bizNo = "{ID_LIST{#ids}}"
    )
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        if (CollectionUtils.isBlank(ids)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "id不能为空!");
        }
        Boolean rsp = projectContractService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @LogRecord(
            success = "【{USER{#logUserId}}】执行项目合同分页查询",
            type = "ProjectContract",
            subType = "分页查询",
            bizNo = ""  // 分页无具体业务实体编号
    )
    public ResponseDTO<Page<ProjectContractVO>> pages(@RequestBody Page<ProjectContractDTO> pageRequest) throws Exception {
        Page<ProjectContractVO> rsp = projectContractService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 我的合同分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "我的合同分页")
    @RequestMapping(value = "/userPage", method = RequestMethod.POST)
    @LogRecord(
            success = "【{USER{#logUserId}}】执行我的项目合同分页查询",
            type = "ProjectContract",
            subType = "我的合同分页查询",
            bizNo = ""  // 分页无具体业务实体编号
    )
    public ResponseDTO<Page<ProjectContractVO>> userPage(@RequestBody Page<ProjectContractDTO> pageRequest) throws Exception {
        Page<ProjectContractVO> rsp = projectContractService.userPage(pageRequest);
        return new ResponseDTO<>(rsp);
    }
}
