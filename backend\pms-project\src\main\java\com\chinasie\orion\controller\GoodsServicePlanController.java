package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.GoodsServicePlanDTO;
import com.chinasie.orion.domain.vo.GoodsServicePlanVO;
import com.chinasie.orion.domain.vo.GoodsServiceTypeVO;
import com.chinasie.orion.domain.vo.GoodsServiceUnitCodeVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.GoodsServicePlanService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * GoodsServicePlan 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-25 16:26:18
 */
@RestController
@RequestMapping("/goods-service-plan")
@Api(tags = "物资/服务计划表")
public class GoodsServicePlanController {

    @Resource
    private GoodsServicePlanService goodsServicePlanService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "物资/服务计划详情")
    @RequestMapping(value = "detail/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】详情", type = "物资/服务计划表", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<GoodsServicePlanVO> detail(@PathVariable(value = "id") String id) throws Exception {
        GoodsServicePlanVO rsp = goodsServicePlanService.detail(id);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 新增
     *
     * @param goodsServicePlanDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "创建计划")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】创建计划", type = "物资/服务计划表", subType = "创建计划", bizNo = "")
    public ResponseDTO<GoodsServicePlanVO> create(@Valid @RequestBody GoodsServicePlanDTO goodsServicePlanDTO) throws Exception {
        GoodsServicePlanVO rsp = goodsServicePlanService.create(goodsServicePlanDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param goodsServicePlanDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @LogRecord(success = "【{USER{#logUserId}}】编辑", type = "物资/服务计划表", subType = "编辑", bizNo = "")
    public ResponseDTO<Boolean> edit(@Valid @RequestBody GoodsServicePlanDTO goodsServicePlanDTO) throws Exception {
        Boolean rsp = goodsServicePlanService.edit(goodsServicePlanDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除", type = "物资/服务计划表", subType = "删除批量", bizNo = "")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = goodsServicePlanService.remove(ids);
        return new ResponseDTO(rsp);
    }

    @ApiOperation("获取物资服务类型")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @GetMapping("/getGoodsServiceType/{projectId}")
    @LogRecord(success = "【{USER{#logUserId}}】获取物资服务类型", type = "物资/服务计划表", subType = "获取物资服务类型", bizNo = "")
    public ResponseDTO<List<GoodsServiceTypeVO>> getGoodsServiceType(@PathVariable(value = "projectId") String projectId) throws Exception {
        return new ResponseDTO<>(goodsServicePlanService.getGoodsServiceType(projectId));
    }


    @ApiOperation("通过物资服务类型获取计量单位")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @GetMapping("/getUnitCode/{typeCode}")
    @LogRecord(success = "【{USER{#logUserId}}】获取计量单位", type = "物资/服务计划表", subType = "获取计量单位", bizNo = "")
    public ResponseDTO<List<GoodsServiceUnitCodeVO>> getUnitCode(@PathVariable("typeCode") String typeCode) throws Exception {
        return new ResponseDTO<>(goodsServicePlanService.getUnitCode(typeCode));
    }

    @ApiOperation("物资/服务列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping("/getPage")
    @LogRecord(success = "【{USER{#logUserId}}】物资/服务列表", type = "物资/服务计划表", subType = "物资/服务列表", bizNo = "")
    public ResponseDTO<PageResult<GoodsServicePlanVO>> getGoodsServicePlanPage(@Valid @RequestBody Page<GoodsServicePlanDTO> pageRequest) throws Exception {
        return new ResponseDTO<>(goodsServicePlanService.getGoodsServicePlanPage(pageRequest));
    }

    /**
     * 发送预警消息
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "发送预警消息")
    @RequestMapping(value = "/send/msg", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】发送预警消息", type = "物资/服务计划表", subType = "发送预警消息", bizNo = "")
    public ResponseDTO<Boolean> sendExpireMsg() throws Exception {
        Boolean rsp = goodsServicePlanService.sendExpireMsg();
        return new ResponseDTO(rsp);
    }


    @ApiOperation("我创建的物资/服务列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping("/getUserPage")
    @LogRecord(success = "【{USER{#logUserId}}】我创建的物资/服务列表", type = "物资/服务计划表", subType = "我创建的物资/服务列表", bizNo = "")
    public ResponseDTO<PageResult<GoodsServicePlanVO>> getGoodsServicePlanUserPage(@Valid @RequestBody Page<GoodsServicePlanDTO> pageRequest) throws Exception {
        return new ResponseDTO<>(goodsServicePlanService.getGoodsServicePlanUserPage(pageRequest));
    }

}
