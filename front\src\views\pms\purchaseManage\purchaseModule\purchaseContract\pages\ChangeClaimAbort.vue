<script setup lang="ts">

import {
  OrionTable, Layout2, Layout3, isPower,
} from 'lyra-component-vue3';
import {
  onMounted, reactive, ref, computed, ComputedRef, h,
} from 'vue';
import Api from '/src/api';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { RangePicker } from 'ant-design-vue';
import {
  cloneDeep, get as loadGet, isBoolean, isEmpty,
} from 'lodash-es';
import MoneyRow from '../../components/MoneyRow.vue';
import { parseBooleanToRender, parsePriceByNumber } from '../../utils';

const tabsIndex = ref(0);
const rowChangeMoney = reactive([
  {
    key: 'total',
    title: '合同数量',
    value: '',
    suffix: '个',
  },
]);
const rowClaimMoney = reactive([
  {
    key: 'total',
    title: '合同数量',
    value: '',
    suffix: '个',
  },
]);
const rowAbortMoney = reactive([
  {
    key: 'total',
    title: '合同数量',
    value: '',
    suffix: '个',
  },
]);
const tableChangeOptions = {
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  smallSearchField: ['contractName', 'contractNumber'],
  filterConfig: {
    fields: [
      {
        field: 'contractName',
        fieldName: '合同名称',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'contractNumber',
        fieldName: '合同编号',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'changeRequestDate',
        fieldName: '变更申请日期',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterface: null,
        referenceInterfaceMethod: null,
        referenceInterfaceParams: null,
        component: 'Select',
        hidden: false,
        constValue: null,
        fieldNames: null,
        searchFieldName: null,
        optionsCustomRender({ filterItem, filterMethods, groupRelation }) {
          return h(RangePicker, {
            style: {
              width: '100%',
            },
            onChange(date: any) {
              filterMethods.setFieldValue(filterItem.field, date, groupRelation);
            },
            valueFormat: 'YYYY-MM-DD',
          });
        },
      },
      {
        field: 'businessRspUserName',
        fieldName: '商务负责人',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
    ],
  },
  columns: [
    {
      title: '合同编号',
      dataIndex: 'contractNumber',
    },
    {
      title: '合同名称',
      dataIndex: 'contractName',
    },
    {
      title: '采购组',
      dataIndex: 'procurementGroupName',
      width: 150,
    },
    {
      title: '商务负责人',
      dataIndex: 'businessRspUserName',
      width: 150,
    },
    {
      title: '变更编号',
      dataIndex: 'changeId',
      width: 150,
    },
    {
      title: '变更标题',
      dataIndex: 'changeTitle',
      width: 220,
    },
    {
      title: '变更类型',
      dataIndex: 'changeType',
      width: 130,
    },
    {
      title: '变更申请日期',
      dataIndex: 'changeRequestDate',
      width: 150,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '本次变更金额',
      dataIndex: 'thisChangeAmount',
      width: 130,
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
  ],
  api: (params: Record<string, any>) => {
    const originSearchConditions = loadGet(cloneDeep(params), 'searchConditions', []);
    const query = {};
    const searchConditions = originSearchConditions.reduce((prev, cur) => {
      for (let i = 0; i < cur.length; i++) {
        const single = cur[i];
        const filedProp = loadGet(single, 'field', '');
        if (['changeRequestDate'].includes(filedProp)) {
          const [first, second] = loadGet(single, 'values', []);
          if (filedProp === 'changeRequestDate') {
            Object.assign(query, {
              startDate: first,
              endDate: second,
            });
          }
          cur.splice(i, 1, undefined);
        }
      }
      const lastCur = cur.filter(Boolean);
      if (lastCur.length) {
        return [...prev, lastCur];
      }
      return prev;
    }, []);
    const newSearchConditions = {
      searchConditions: searchConditions.length ? searchConditions : [],
      query: isEmpty(query) ? undefined : query,
    };
    return new Api('/pms/contractChange').fetch({
      ...params,
      ...newSearchConditions,
    }, 'page', 'POST').then((res) => {
      rowChangeMoney[0].value = res.totalSize;
      return res;
    });
  },
};
const tableClaimOptions = {
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  smallSearchField: ['contractName', 'contractNumber'],
  filterConfig: {
    fields: [
      {
        field: 'contractName',
        fieldName: '合同名称',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'contractNumber',
        fieldName: '合同编号',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'claimRequestTime',
        fieldName: '索赔申请时间',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterface: null,
        referenceInterfaceMethod: null,
        referenceInterfaceParams: null,
        component: 'Select',
        hidden: false,
        constValue: null,
        fieldNames: null,
        searchFieldName: null,
        optionsCustomRender({ filterItem, filterMethods, groupRelation }) {
          return h(RangePicker, {
            style: {
              width: '100%',
            },
            onChange(date: any) {
              filterMethods.setFieldValue(filterItem.field, date, groupRelation);
            },
            valueFormat: 'YYYY-MM-DD',
          });
        },
      },
      {
        field: 'businessRspUserName',
        fieldName: '商务负责人',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
    ],
  },
  columns: [
    {
      title: '合同编号',
      dataIndex: 'contractNumber',
    },
    {
      title: '合同名称',
      dataIndex: 'contractName',
    },
    {
      title: '采购组',
      dataIndex: 'procurementGroupName',
      width: 150,
    },
    {
      title: '商务负责人',
      dataIndex: 'businessRspUserName',
      width: 150,
    },
    {
      title: '索赔编号',
      dataIndex: 'claimId',
      width: 150,
    },
    {
      title: '索赔标题',
      dataIndex: 'claimTitle',
      width: 200,
    },
    {
      title: '索赔状态',
      dataIndex: 'claimStatus',
      width: 100,
    },
    {
      title: '索赔方向',
      dataIndex: 'claimDirection',
      width: 100,
    },
    {
      title: '索赔处理时间',
      dataIndex: 'claimProcessTime',
      width: 150,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '索赔申请时间',
      dataIndex: 'claimRequestTime',
      width: 150,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '累计索赔金额(含本次)',
      dataIndex: 'cumulativeClaimAmount',
      width: 170,
    },
    {
      title: '总累计索赔占原合同价%',
      dataIndex: 'totalClaimPctOfOrigPrice',
      width: 200,
    },
  ],
  api: (params: Record<string, any>) => {
    const originSearchConditions = loadGet(cloneDeep(params), 'searchConditions', []);
    const query = {};
    const searchConditions = originSearchConditions.reduce((prev, cur) => {
      for (let i = 0; i < cur.length; i++) {
        const single = cur[i];
        const filedProp = loadGet(single, 'field', '');
        if (['claimRequestTime'].includes(filedProp)) {
          const [first, second] = loadGet(single, 'values', []);
          if (filedProp === 'claimRequestTime') {
            Object.assign(query, {
              startDate: first,
              endDate: second,
            });
          }
          cur.splice(i, 1, undefined);
        }
      }
      const lastCur = cur.filter(Boolean);
      if (lastCur.length) {
        return [...prev, lastCur];
      }
      return prev;
    }, []);
    const newSearchConditions = {
      searchConditions: searchConditions.length ? searchConditions : [],
      query: isEmpty(query) ? undefined : query,
    };
    return new Api('/pms/contractClaim').fetch({
      ...params,
      ...newSearchConditions,
    }, 'page', 'POST').then((res) => {
      rowClaimMoney[0].value = res.totalSize;
      return res;
    });
  },
};
const tableAbortOptions = {
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  smallSearchField: ['contractName', 'contractNumber'],
  filterConfig: {
    fields: [
      {
        field: 'contractName',
        fieldName: '合同名称',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'contractNumber',
        fieldName: '合同编号',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'terminationRequestDate',
        fieldName: '终止申请日期',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterface: null,
        referenceInterfaceMethod: null,
        referenceInterfaceParams: null,
        component: 'Select',
        hidden: false,
        constValue: null,
        fieldNames: null,
        searchFieldName: null,
        optionsCustomRender({ filterItem, filterMethods, groupRelation }) {
          return h(RangePicker, {
            style: {
              width: '100%',
            },
            onChange(date: any) {
              filterMethods.setFieldValue(filterItem.field, date, groupRelation);
            },
            valueFormat: 'YYYY-MM-DD',
          });
        },
      },
      {
        field: 'businessRspUserName',
        fieldName: '商务负责人',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
    ],
  },
  columns: [
    {
      title: '合同编号',
      dataIndex: 'contractNumber',
    },
    {
      title: '合同名称',
      dataIndex: 'contractName',
    },
    {
      title: '采购组',
      dataIndex: 'procurementGroupName',
      width: 150,
    },
    {
      title: '商务负责人',
      dataIndex: 'businessRspUserName',
      width: 150,
    },
    {
      title: '是否签约前终止',
      dataIndex: 'isPreSignTermination',
      width: 150,
      customRender({ text }) {
        return parseBooleanToRender(text);
      },
    },
    {
      title: '终止申请日期',
      dataIndex: 'terminationRequestDate',
      width: 130,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '合同终止金额',
      dataIndex: 'contractTerminationAmount',
      width: 130,
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
  ],
  api: (params: Record<string, any>) => {
    const originSearchConditions = loadGet(cloneDeep(params), 'searchConditions', []);
    const query = {};
    const searchConditions = originSearchConditions.reduce((prev, cur) => {
      for (let i = 0; i < cur.length; i++) {
        const single = cur[i];
        const filedProp = loadGet(single, 'field', '');
        if (['terminationRequestDate'].includes(filedProp)) {
          const [first, second] = loadGet(single, 'values', []);
          if (filedProp === 'terminationRequestDate') {
            Object.assign(query, {
              startDate: first,
              endDate: second,
            });
          }
          cur.splice(i, 1, undefined);
        }
      }
      const lastCur = cur.filter(Boolean);
      if (lastCur.length) {
        return [...prev, lastCur];
      }
      return prev;
    }, []);
    const newSearchConditions = {
      searchConditions: searchConditions.length ? searchConditions : [],
      query: isEmpty(query) ? undefined : query,
    };
    return new Api('/pms/contractTermination').fetch({
      ...params,
      ...newSearchConditions,
    }, 'page', 'POST').then((res) => {
      rowAbortMoney[0].value = res.totalSize;
      return res;
    });
  },
};
const pageMenu = computed(() => ([
  isPower('PMS_BGSPZZ_container_01', powerData.value)
    ? {
      name: '合同变更',
      id: 'change',
    } : null,
  isPower('PMS_BGSPZZ_container_02', powerData.value)
    ? {
      name: '合同索赔',
      id: 'claim',
    } : null,
  isPower('PMS_BGSPZZ_container_03', powerData.value)
    ? {
      name: '合同终止',
      id: 'abort',
    } : null,
].filter((item) => item)));
const powerData = ref([]);
function getPowerDataHandle(data) {
  powerData.value = data;
}
const handleTabsChang = (index) => {
  tabsIndex.value = index;
};
</script>

<template>
  <Layout2
    v-model:tabsIndex="tabsIndex"
    v-get-power="{pageCode: 'changeClaimAbort',getPowerDataHandle}"
    :tabs="pageMenu"
    :options="{ body: { scroll: true } }"
    @tabsChange="handleTabsChang"
  >
    <!--    变更-->
    <template v-if="tabsIndex===0">
      <OrionTable
        ref="tableRef"
        class="scroll-table"
        :options="tableChangeOptions"
      >
        <template #toolbarLeft>
          <MoneyRow :data="rowChangeMoney" />
        </template>
      </OrionTable>
    </template>
    <!--    索赔-->
    <template v-if="tabsIndex===1">
      <OrionTable
        ref="tableRef"
        class="scroll-table"
        :options="tableClaimOptions"
      >
        <template #toolbarLeft>
          <MoneyRow :data="rowClaimMoney" />
        </template>
      </OrionTable>
    </template>
    <!--    终止-->
    <template v-if="tabsIndex===2">
      <OrionTable
        ref="tableRef"
        class="scroll-table"
        :options="tableAbortOptions"
      >
        <MoneyRow :data="rowAbortMoney" />
      </OrionTable>
    </template>
  </Layout2>
</template>

<style scoped lang="less">
.scroll-table {
  ::-webkit-scrollbar {
    height: 15px !important;
    width: 15px !important;
  }
  ::-webkit-scrollbar-thumb {
    border-radius:0;
  }
}
</style>