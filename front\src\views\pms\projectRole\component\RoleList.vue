<template>
  <div class="h-full flex flex-ver">
    <div class="title flex flex-ac">
      <h3 class="flex-f1">
        角色列表
      </h3>
      <div
        class="p10 flex flex-pac search-icon"
        @click="isSearch = !isSearch"
      >
        <Icon icon="fa-search" />
      </div>
    </div>
    <div
      v-if="isSearch"
      class="search-wrap"
    >
      <InputSearch
        v-model:value="searchValue"
        placeholder="请输入关键字"
      />
    </div>
    <div
      v-loading="loadStatus"
      class="role-list flex-f1"
    >
      <Menu
        v-if="menuData"
        v-bind="$attrs"
        :show-header="false"
        :menu-data="menuDataCom"
        size="small"
        @menuChange="menuChange"
      />
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed, defineComponent, reactive, toRefs,
} from 'vue';
import { Icon, BasicMenu as Menu } from 'lyra-component-vue3';
import { Input } from 'ant-design-vue';
import Api from '/@/api';

export default defineComponent({
  name: 'RoleList',
  components: {
    Icon,
    InputSearch: Input.Search,
    Menu,
  },
  props: {
    getRoleList: {
      type: Function,
      default: null,
    },
  },
  emits: ['menuChange'],
  setup(props, { emit }) {
    const state = reactive({
      loadStatus: false,
      isSearch: false,
      menuData: null,
      searchValue: '',
    });

    init();

    function init() {
      loadRoleList();
    }

    async function loadRoleList() {
      state.loadStatus = true;
      try {
        const roleList = [
          {
            id: 'projectRole',
            name: '项目管理角色',
            children: [],
          },
          {
            id: 'sysRole',
            name: '系统管理角色',
            children: [],
          },
        ];
        roleList[0].children = await loadProjectRoleList();
        roleList[1].children = await loadSysRoleList();
        props.getRoleList && props.getRoleList(roleList);
        state.menuData = roleList;
        state.loadStatus = false;
      } catch (e) {
        state.loadStatus = false;
      }
    }

    function loadProjectRoleList() {
      return new Api('/pms/project-role')
        .getList({
          status: 1,
        })
        .then((data) => data.map((item) => ({
          ...item,
          roleType: 0,
        })));
    }

    function loadSysRoleList() {
      return new Api('/pmi/role/list').fetch({}, '', 'POST').then((data) => data.map((item) => ({
        ...item,
        roleType: 1,
      })));
    }

    return {
      ...toRefs(state),
      menuDataCom: computed(() => {
        if (!state.menuData) [];
        if (state.searchValue) {
          return state.menuData.filter((item) => item.name.includes(state.searchValue));
        }
        return state.menuData;
      }),
      menuChange(menuItem) {
        const excludeMenuItem = ['projectRole', 'sysRole'];
        if (excludeMenuItem.indexOf(menuItem.id) > -1) {
          return;
        }
        emit('menuChange', menuItem.item);
      },
    };
  },
});
</script>

<style scoped lang="less">
  .title {
    height: 40px;
    line-height: 40px;
    background: rgba(248, 249, 252, 1);
    padding-left: 10px;
  }

  .search-icon {
    font-size: 16px;
    color: #959899;
    width: 40px;
    height: 40px;
    cursor: pointer;

    &:hover {
      background-color: rgb(243, 244, 246);
    }
  }

  .search-wrap {
    padding: 10px;
    border-bottom: 1px solid ~`getPrefixVar('border-color-base')`;
  }

  .role-list {
    overflow-y: auto;
    //> div {
    //  height: 40px;
    //  line-height: 40px;
    //  padding: 0 14px;
    //  cursor: pointer;
    //  &:hover {
    //    background-color: rgba(42, 113, 202, 0.1);
    //  }
    //
    //  &.action {
    //    background-color: rgba(42, 113, 202, 0.1);
    //  }
    //}
  }
</style>
