<template>
  <div
    v-loading="loading"
    class="wrap"
  >
    <div class="rule-toolbar">
      <div class="title">
        项目列表页面
      </div>
      <div>
        以下规则会限制访问当前页面: <span
          class="action-btn"
          @click="addRule"
        >添加规则</span><span>|</span><span
          class="action-btn"
          @click="deleteRule"
        >移除规则</span>
      </div>
    </div>
    <div class="plr20">
      <BasicTabs
        :tabs="tabs"
        :tabs-index="tabsIndex"
        @tabsChange="tabsChange"
      >
        <template #tabsItem="{ item }">
          <div class="tabs-item flex-te">
            <Checkbox
              v-if="item.id !== 'default'"
              :key="item.id"
              v-model:checked="tabSelected[item.id]"
              :disabled="item.id === ruleId"
              @click.stop
              @change="onCheckChange($event, item.id)"
            /><span class="plr10">{{ item.name }}</span>
          </div>
        </template>
      </BasicTabs>
      <TipsBox>当前条件为：空<span>（默认规则在添加新规则后，会失效！）</span></TipsBox>

      <CheckPermissionComponent
        :key="ruleId"
        :role-type="roleType"
        :role-id="roleId"
        :page-id="pageId"
        :rule-id="ruleId === 'default' ? '' : ruleId"
      />
    </div>

    <!--添加规则-->
    <AddRule
      @register="addRuleRegister"
      @change="addRuleChange"
    />
  </div>
</template>

<script lang="ts">
import {
  defineComponent, onMounted, reactive, toRefs,
} from 'vue';
import {
  BasicTabs, TipsBox, useModal,
} from 'lyra-component-vue3';
import { Checkbox, message } from 'ant-design-vue';
import AddRule from '../modal/AddRule.vue';
import CheckPermissionComponent from './CheckPermissionComponent.vue';
import Api from '/@/api';
import { getSubjectType } from '../utils';

export default defineComponent({
  name: 'FunctionalEmpowerment',
  components: {
    BasicTabs,
    TipsBox,
    Checkbox,
    AddRule,
    CheckPermissionComponent,
  },
  props: {
    // 角色ID
    roleId: {
      type: String,
      default: '',
    },
    // 页面ID
    pageId: {
      type: String,
      default: '',
    },
    // 角色类型
    roleType: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const state = reactive({
      loading: false,
      tabsIndex: 0,
      ruleId: 'default',
      tabs: [
        {
          name: '默认规则',
          id: 'default',
        },
      ],
      tabSelected: {},
      pageContainer: [],
    });

    onMounted(() => {
      init();
    });

    function init() {
      getRule();
    }

    // 获取已添加的规则
    function getRule() {
      new Api('/pmi/privilege-subject/privilege-subject')
        .fetch(
          {
            subjectId: props.roleId,
            pageId: props.pageId,
            subjectType: getSubjectType(props.roleType),
          },
          '',
          'GET',
        )
        .then((data) => {
          state.tabs = [
            ...state.tabs,
            ...data
              .filter((item) => item.ruleId)
              .map((item) => ({
                id: item.ruleId,
                name: item.ruleName,
              })),
          ];
        });
    }

    // 添加规则
    function addRule() {
      openModal();
    }

    function tabsChange(index, item) {
      state.tabsIndex = index;
      state.ruleId = item.id;
      if (state.tabSelected[state.ruleId]) {
        delete state.tabSelected[state.ruleId];
      }
    }

    // 移除规则
    function deleteRule() {
      const selectedKeys = Object.keys(state.tabSelected);
      if (!selectedKeys.length) {
        message.error('请勾选规则项');
        return;
      }

      new Api('/pmi/privilege-subject/delete-subject-privilege')
        .fetch(
          {
            pageId: props.pageId,
            subjectId: props.roleId,
            ruleIds: selectedKeys,
          },
          '',
          'DELETE',
        )
        .then(() => {
          message.success('移除成功');
          state.tabs = state.tabs.filter((item) => !selectedKeys.includes(item.id));
          state.tabsIndex = state.tabs.findIndex((item) => item.id === state.ruleId);
          state.tabSelected = {};
        });
    }

    // 添加规则弹窗
    const [addRuleRegister, { openModal }] = useModal();

    // 添加规则确认
    async function addRuleChange(selectItems) {
      const filterSelectItem = selectItems.filter((item) => {
        const tabsIds = state.tabs.map((tabsItem) => tabsItem.id);

        return !tabsIds.includes(item.id);
      });

      state.loading = true;

      for (let item of filterSelectItem) {
        await subAddRule(item.id);
        state.tabs = [...state.tabs, item];
      }

      state.loading = false;
    }

    // 提交新规则
    function subAddRule(ruleId) {
      return new Api('/pmi/privilege-subject/setting-subject-privilege').fetch(
        {
          subjectId: props.roleId,
          pageId: props.pageId,
          ruleId,
          subjectType: getSubjectType(props.roleType),
        },
        '',
        'POST',
      );
    }

    // 勾选tabs上的checkbox
    function onCheckChange(event, id) {
      const { checked } = event.target;
      if (checked) {
        state.tabSelected[id] = true;
      } else if (state.tabSelected[id]) {
        delete state.tabSelected[id];
      }
    }

    return {
      ...toRefs(state),
      addRule,
      deleteRule,
      addRuleRegister,
      addRuleChange,
      onCheckChange,
      tabsChange,
    };
  },
});
</script>

<style scoped lang="less">
  .rule-toolbar {
    background: rgba(9, 96, 189, 0.06);
    padding: 20px;
    border-top: 1px solid #d9dde8;

    > .title {
      font-weight: bold;
      font-size: 16px;
      padding-bottom: 5px;
    }

    span:not(.action-btn) {
      color: #ccc;
      position: relative;
      top: -2px;
      padding: 0 10px;
    }
  }

  .tabs-item {
    max-width: 200px;
  }
</style>
