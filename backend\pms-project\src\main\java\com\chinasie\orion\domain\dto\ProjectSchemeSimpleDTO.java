package com.chinasie.orion.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.UserDataBind;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Time;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/07/23/20:40
 * @description:
 */
@Data
public class ProjectSchemeSimpleDTO implements Serializable {

    @ApiModelProperty("ID")
    @TableField(value = "id")
    private String id;

    @ApiModelProperty("类名称")
    @TableField(value = "class_name")
    private String className;

    @ApiModelProperty("创建人")
    @TableField(value = "creator_id")
    private String creatorId;

    @ApiModelProperty("创建人名字")
    @TableField(exist = false)
    private String creatorName;

    @ApiModelProperty("拥有者")
    @TableField(value = "owner_id")
    private String ownerId;

    @ApiModelProperty("拥有者名字")
    @TableField(exist = false)
    private String ownerName;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time")
    private Date createTime;

    @ApiModelProperty("修改人")
    @TableField(value = "modify_id")
    private String modifyId;

    @ApiModelProperty("修改人名字")
    @TableField(exist = false)
    private String modifyName;

    @ApiModelProperty("修改时间")
    @TableField(value = "modify_time")
    private Date modifyTime;

    @ApiModelProperty("备注")
    @TableField(value = "remark")
    private String remark;

    @ApiModelProperty("平台ID")
    @TableField(value = "platform_id")
    private String platformId;

    @ApiModelProperty("业务组织Id")
    @TableField(value = "org_id")
    private String orgId;

    @ApiModelProperty("业务组织名称")
    @TableField(exist = false)
    private String businessOrgName;

    @ApiModelProperty("状态")
    @TableField(value = "status")
    private Integer status;

    @ApiModelProperty("状态对象")
    @TableField(exist = false)
    private DataStatusVO dataStatus;

    @ApiModelProperty("logicStatus 逻辑删除字段")
    @TableLogic
    private Integer logicStatus;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;


    /**
     * 计划编号
     */
    @ApiModelProperty(value = "计划编号")
    @TableField(value = "scheme_number")
    private String schemeNumber;
    /**
     *
     */
    @ApiModelProperty(value = "排序")
    @TableField(value = "sort")
    private Long sort;
    /**
     *
     */
    @ApiModelProperty(value = "计划名称")
    @TableField(value = "name")
    private String name;
    /**
     *
     */
    @ApiModelProperty(value = "icon")
    @TableField(value = "icon")
    private String icon;
    /**
     *
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    private String projectId;
    /**
     *
     */
    @ApiModelProperty(value = "项目编号")
    @TableField(value = "project_number")
    private String projectNumber;
    /**
     *
     */
    @ApiModelProperty(value = "层级")
    @TableField(value = "level")
    private Integer level;
    /**
     *
     */
    @ApiModelProperty(value = "父id")
    @TableField(value = "parent_id")
    private String parentId;
    /**
     *
     */
    @ApiModelProperty(value = "父级链")
    @TableField(value = "parent_chain")
    private String parentChain;
//    /**
//     *
//     */
//    @ApiModelProperty(value = "计划类型")
//    @TableField(value = "type")
//    private Integer type;


    /**
     *
     */
    @ApiModelProperty(value = "计划类型")
    @TableField(value = "node_type")
    private String nodeType;
    /**
     *
     */
    @ApiModelProperty(value = "责任处室")
    @TableField(value = "rsp_sub_dept")
    private String rspSubDept;

    /**
     *
     */
    @ApiModelProperty(value = "责任科室")
    @TableField(value = "rsp_section_id")
    private String rspSectionId;
    /**
     *
     */
    @ApiModelProperty(value = "责任人")
    @TableField(value = "rsp_user")
    private String rspUser;

    @ApiModelProperty(value = "责任人编号")
    @TableField(value = "rsp_user_code")
    private String rspUserCode;
    /**
     *
     */
    @ApiModelProperty(value = "计划开始时间")
    @TableField(value = "begin_time")
    private Date beginTime;
    /**
     *
     */
    @ApiModelProperty(value = "计划结束时间")
    @TableField(value = "end_time")
    private Date endTime;
    /**
     *
     */
    @ApiModelProperty(value = "计划情况")
    @TableField(value = "circumstance")
    private Integer circumstance;
    /**
     *
     */
    @ApiModelProperty(value = "实际结束时间")
    @TableField(value = "actual_end_time")
    private Date actualEndTime;
    /**
     *
     */
    @ApiModelProperty(value = "实际开始时间")
    @TableField(value = "actual_begin_time")
    private Date actualBeginTime;

    @ApiModelProperty(value = "项目计划描述")
    @TableField(value = "scheme_desc")
    private String schemeDesc;

    @ApiModelProperty(value = "置顶序号（0：取消置顶，其他根据序号排列置顶计划）")
    @TableField(value = "top_sort")
    private Integer topSort;

    @ApiModelProperty(value = "执行情况说明")
    @TableField(value = "execute_desc")
    private String executeDesc;

    @ApiModelProperty(value = "计划下发时间")
    @TableField(value = "issue_time")
    private Date issueTime;


    @ApiModelProperty(value = "是否超时完成 1 是，0 否")
    @TableField(value = "delay_end_Flag")
    private Boolean delayEndFlag;

    @ApiModelProperty(value = "超时原因")
    @TableField(value = "delay_end_reason")
    private String delayEndReason;


    @ApiModelProperty(value = "是否关联流程 1 是，0 否")
    @TableField(value = "process_flag")
    private Boolean processFlag;

    @ApiModelProperty(value = "关联流程实例Id")
    @TableField(value = "process_id")
    private String processId;


    @ApiModelProperty(value = "关联流程实例Id")
    @TableField(value = "form_code_id")
    private String formCodeId;


    /**
     * 计划工期
     */
    @ApiModelProperty(value = "计划工期")
    @TableField(value = "duration_days")
    private Integer durationDays;


    @ApiModelProperty(value = "下达人")
    @TableField(value = "issued_user")
    private String issuedUser;
    @ApiModelProperty(value = "参与人")
    @TableField(value = "participant_users")
    private String participantUsers;

    @ApiModelProperty(value = "审批人")
    @TableField(value = "examine_user")
    private String examineUser;

    @ApiModelProperty(value = "评分状态")
    @TableField(value = "examine_type")
    private String examineType;

    @ApiModelProperty(value = "下发提醒间隔")
    @TableField(value = "issue_remind_interval")
    private Integer issueRemindInterval;

    @ApiModelProperty(value = "下发提醒间隔单位")
    @TableField(value = "issue_remind_interval_unit")
    private String issueRemindIntervalUnit;

    @ApiModelProperty(value = "责任人名称")
    @TableField(exist = false)
    private String rspUserName;

    @ApiModelProperty(value = "下发提醒间隔时间")
    @TableField(value = "issue_remind_time")
    private Time issueRemindTime;


    @ApiModelProperty(value = "计划活动项")
    @TableField(value = "plan_active")
    private String planActive;

    @ApiModelProperty(value = "选择基线")
    @TableField(value = "base_line")
    private String baseLine;
    @ApiModelProperty(value = "设计任务编码")
    @TableField(value = "design_task_number")
    private String designTaskNumber;

    @ApiModelProperty(value = "标识")
    @TableField(value = "identification")
    private String identification;

    @ApiModelProperty(value = "确认理由")
    @TableField(value = "reason_confirmation")
    private String reasonConfirmation;


    @ApiModelProperty(value = "暂停时间")
    @TableField(value = "suspend_time")
    private Date suspendTime;

    @ApiModelProperty(value = "终止时间")
    @TableField(value = "terminate_time")
    private Date terminateTime;

    @ApiModelProperty(value = "开始时间")
    @TableField(value = "start_time")
    private Date startTime;

    @ApiModelProperty(value = "暂停理由")
    @TableField(value = "suspend_reason")
    private String suspendReason;

    @ApiModelProperty(value = "终止理由")
    @TableField(value = "terminate_reason")
    private String terminateReason;

    @ApiModelProperty(value = "启动理由")
    @TableField(value = "start_reason")
    private String startReason;

    @ApiModelProperty(value = "上个状态")
    @TableField(value = "last_status")
    private Integer lastStatus;
    @ApiModelProperty(value = "是否作业")
    @TableField(value = "is_work_job")
    private Boolean isWorkJob;

    @ApiModelProperty(value = "是否作业 -1：否 1：是")
    @TableField(value = "is_work")
    private Integer isWork;

    @ApiModelProperty(value = "实施类型")
    @TableField(value = "enforce_type")
    private String enforceType;

    @ApiModelProperty(value = "实施地点")
    @TableField(value = "enforce_base_place")
    private String enforceBasePlace;
    @ApiModelProperty(value = "实施地点名称")
    @TableField(value = "enforce_base_place_name")
    private String enforceBasePlaceName;

    @ApiModelProperty(value = "实施区域/范围")
    @TableField(value = "enforce_scope")
    private String enforceScope;

    @ApiModelProperty(value = "工作内容")
    @TableField(value = "work_content")
    private String workContent;

    @ApiModelProperty(value = "大修轮次")
    @TableField(value = "repair_round")
    private String repairRound;

    @ApiModelProperty(value = "是否携带物资")
    @TableField(value = "is_carry_materials")
    private Boolean isCarryMaterials;

    @ApiModelProperty(value = "转办理由")
    @TableField(value = "reason_transfer")
    private String  reasonTransfer;

    @ApiModelProperty(value = "转办理由")
    @TableField(value = "product_id")
    private String  productId;


    /**
     * 项目暂停周期
     */
    @ApiModelProperty(value = "项目暂停周期")
    @TableField(value = "paused_cycle")
    private Integer pausedCycle;

    @ApiModelProperty(value = "紧急程度")
    @TableField(value = "urgency")
    private String urgency;

    @ApiModelProperty(value = "合同里程碑id")
    @TableField(value = "contract_milestone_id")
    private String contractMilestoneId;
}
