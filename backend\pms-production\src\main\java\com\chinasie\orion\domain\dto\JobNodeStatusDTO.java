package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * JobNodeStatus DTO对象
 *
 * <AUTHOR>
 * @since 2024-08-08 14:45:20
 */
@ApiModel(value = "JobNodeStatusDTO对象", description = "作业节点执行状态表")
@Data
@ExcelIgnoreUnannotated
public class JobNodeStatusDTO extends  ObjectDTO   implements Serializable{

    /**
     * 作业ID
     */
    @ApiModelProperty(value = "作业ID")
    @ExcelProperty(value = "作业ID ", index = 0)
    private String jobId;

    /**
     * 存放所有节点key,拼接（拥有就表示点亮）
     */
    @ApiModelProperty(value = "存放所有节点key,拼接（拥有就表示点亮）")
    @ExcelProperty(value = "存放所有节点key,拼接（拥有就表示点亮） ", index = 1)
    private String nodeKeyJson;




}
