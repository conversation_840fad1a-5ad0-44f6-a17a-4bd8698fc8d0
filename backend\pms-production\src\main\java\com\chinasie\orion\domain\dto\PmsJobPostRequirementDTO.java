package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * PmsJobPostRequirement DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 10:19:25
 */
@ApiModel(value = "PmsJobPostRequirementDTO对象", description = "岗位要求")
@Data
@ExcelIgnoreUnannotated
public class PmsJobPostRequirementDTO extends  ObjectDTO   implements Serializable{

    /**
     * 岗位授权管理Id
     */
//    @ApiModelProperty(value = "岗位授权管理Id")
//    @ExcelProperty(value = "岗位授权管理Id ", index = 0)
//    private String authorizeManageId;
    @ApiModelProperty(value = "岗位Id")
    @ExcelProperty(value = "岗位Id ", index = 0)
    private String jobPostId;
    /**
     * 要求类型
     */
    @ApiModelProperty(value = "要求类型")
    @ExcelProperty(value = "要求类型 ", index = 1)
    private String type;

    /**
     * 要求名称
     */
    @ApiModelProperty(value = "要求名称")
    @ExcelProperty(value = "要求名称 ", index = 2)
    private String name;

    /**
     * 应取得证书
     */
    @ApiModelProperty(value = "应取得证书")
    @ExcelProperty(value = "应取得证书 ", index = 3)
    private String certificateNumber;

    /**
     * 应取得证书
     */
    @ApiModelProperty(value = "应取得证书id")
    private String certificateId;

    /**
     * 应通过培训
     */
    @ApiModelProperty(value = "应通过培训")
    @ExcelProperty(value = "应通过培训 ", index = 4)
    private String trainNumber;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 5)
    private String number;

//    /**
//     * 主表ID
//     */
//    @ApiModelProperty(value = "主表ID")
//    @ExcelProperty(value = "主表ID ", index = 6)
//    private String mainTableId;

}
