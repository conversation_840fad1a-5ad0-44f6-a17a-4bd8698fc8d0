package com.chinasie.orion.controller;

import com.chinasie.orion.amqp.entity.ChangeStatusMessageDTO;
import com.chinasie.orion.domain.dto.train.EquivalentParamDTO;
import com.chinasie.orion.domain.vo.train.PersonTrainEffectVO;
import com.chinasie.orion.domain.vo.train.PersonTrainVO;
import com.chinasie.orion.handler.status.TrainEquivalentChangeStatusReceiver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.domain.entity.TrainEquivalent;
import com.chinasie.orion.domain.dto.TrainEquivalentDTO;
import com.chinasie.orion.domain.vo.TrainEquivalentVO;
import com.chinasie.orion.service.TrainEquivalentService;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * TrainEquivalent 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 17:09:24
 */
@RestController
@RequestMapping("/train-equivalent")
@Api(tags = "培训等效")
public class  TrainEquivalentController  {

    @Autowired
    private TrainEquivalentService trainEquivalentService;

    @Autowired
    private TrainEquivalentChangeStatusReceiver trainEquivalentChangeStatusReceiver;

    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看【培训等效】的信息", type = "TrainCenter", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<TrainEquivalentVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        TrainEquivalentVO rsp = trainEquivalentService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param trainEquivalentDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【培训等效】数据", type = "TrainEquivalent", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody TrainEquivalentDTO trainEquivalentDTO) throws Exception {
        String rsp =  trainEquivalentService.create(trainEquivalentDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param trainEquivalentDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【培训等效】数据", type = "TrainEquivalent", subType = "编辑", bizNo = "{{#trainEquivalentDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  TrainEquivalentDTO trainEquivalentDTO) throws Exception {
        Boolean rsp = trainEquivalentService.edit(trainEquivalentDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【培训等效】数据", type = "TrainEquivalent", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = trainEquivalentService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【培训等效】数据", type = "TrainEquivalent", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = trainEquivalentService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【培训等效】数据", type = "TrainEquivalent", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<TrainEquivalentVO>> pages(@RequestBody Page<TrainEquivalentDTO> pageRequest) throws Exception {
        Page<TrainEquivalentVO> rsp =  trainEquivalentService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 分页
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "列表")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【培训等效】数据", type = "TrainEquivalent", subType = "查询", bizNo = "")
    @RequestMapping(value = "/current/equivalent/list", method = RequestMethod.POST)
    public ResponseDTO<List<TrainEquivalentVO>> currentEquivalentList(@RequestBody EquivalentParamDTO equivalentParamDTO) throws Exception {
        List<TrainEquivalentVO> rsp =  trainEquivalentService.currentEquivalentList( equivalentParamDTO);
        return new ResponseDTO<>(rsp);
    }
    /**
     * 分页
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取当前人能够等效的培训列表")
    @LogRecord(success = "【{USER{#logUserId}}】查询【培训等效】有效培训数据", type = "PersonTrainEffect", subType = "列表查询", bizNo = "")
    @RequestMapping(value = "/current/person/effect/train/list", method = RequestMethod.POST)
    public ResponseDTO<List<PersonTrainEffectVO>> currentPersonEffectTrainList(@RequestBody EquivalentParamDTO equivalentParamDTO) throws Exception {
        List<PersonTrainEffectVO> rsp =  trainEquivalentService.currentPersonEffectTrainList(equivalentParamDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * @param changeStatusDto
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "培训等效状态变更")
    @RequestMapping(value = "/change/status", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】【培训等效】培训等效状态变更", type = "TrainEquivalent", subType = "", bizNo = "")
    public ResponseDTO<Boolean> changeStatus(@RequestBody ChangeStatusMessageDTO changeStatusDto) throws Exception {
        trainEquivalentChangeStatusReceiver.consumerCreateMessage(changeStatusDto);
        return new ResponseDTO<>(Boolean.TRUE);
    }
}
