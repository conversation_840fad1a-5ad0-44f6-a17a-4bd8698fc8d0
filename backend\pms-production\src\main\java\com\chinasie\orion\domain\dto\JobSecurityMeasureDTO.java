package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * JobSecurityMeasure DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-09 15:47:47
 */
@ApiModel(value = "JobSecurityMeasureDTO对象", description = "作业安措信息")
@Data
@ExcelIgnoreUnannotated
public class JobSecurityMeasureDTO extends  ObjectDTO   implements Serializable{

    /**
     * 作业id
     */
    @ApiModelProperty(value = "作业id")
    @ExcelProperty(value = "作业id ", index = 0)
    private String jobId;

    /**
     * 安全措施编码
     */
    @ApiModelProperty(value = "安全措施编码")
    @ExcelProperty(value = "安全措施编码 ", index = 1)
    private String measureCode;

    /**
     * 安全措施描述
     */
    @ApiModelProperty(value = "安全措施描述")
    @ExcelProperty(value = "安全措施描述 ", index = 2)
    private String measureDesc;

    /**
     * 措施类型
     */
    @ApiModelProperty(value = "措施类型")
    @ExcelProperty(value = "措施类型 ", index = 3)
    private String measureType;

    /**
     * 措施长文本
     */
    @ApiModelProperty(value = "措施长文本")
    @ExcelProperty(value = "措施长文本 ", index = 4)
    private String measureText;


    @ApiModelProperty(value = "订单号")
    private String jobCode;
    @ApiModelProperty(value = "风险分析")
    private String riskAnaly;
    @ApiModelProperty(value = "计划工厂")
    private String planFactory;
    @ApiModelProperty(value = "风险代码")
    private String riskCode;
    @ApiModelProperty(value = "md5_value")
    private String encryKey;

}
