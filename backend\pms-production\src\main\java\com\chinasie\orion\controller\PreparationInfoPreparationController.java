package com.chinasie.orion.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;




import com.chinasie.orion.domain.dto.PreparationInfoPreparationDTO;
import com.chinasie.orion.domain.vo.PreparationInfoPreparationVO;

import com.chinasie.orion.service.PreparationInfoPreparationService;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * PreparationInfoPreparation 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-17 17:44:46
 */
@RestController
@RequestMapping("/preparationInfoPreparation")
@Api(tags = "准备信息维护")
public class  PreparationInfoPreparationController  {

    @Autowired
    private PreparationInfoPreparationService preparationInfoPreparationService;


    /**
     * 详情
     *
     * @param repairRound
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取大修轮次对应的配置")
    @RequestMapping(value = "/info", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看【准备信息维护】大修【{{#repairRound}}】", type = "PreparationInfoPreparation", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<PreparationInfoPreparationVO> info(@RequestParam(value = "repairRound") String repairRound) throws Exception {
        PreparationInfoPreparationVO rsp = preparationInfoPreparationService.info(repairRound);
        return new ResponseDTO<>(rsp);
    }



    /**
     * 编辑
     *
     * @param preparationInfoPreparationDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增/编辑")
    @RequestMapping(value = "add/or/update", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增/编辑【准备信息维护】数据【{{#preparationInfoPreparationDTO.repairRound}}】", type = "PreparationInfoPreparation", subType = "编辑", bizNo = "{{#preparationInfoPreparationDTO.id}}")
    public ResponseDTO<Boolean> addOrUpdate(@Validated  @RequestBody  PreparationInfoPreparationDTO preparationInfoPreparationDTO) throws Exception {
        Boolean rsp = preparationInfoPreparationService.edit(preparationInfoPreparationDTO);
        return new ResponseDTO<>(rsp);
    }


}
