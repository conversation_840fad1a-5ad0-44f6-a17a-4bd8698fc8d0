html {
  // header
  --header-bg-color: #394664;
  --header-bg-hover-color: #273352;
  --header-active-menu-bg-color: #273352;

  // sider
  --sider-dark-bg-color: #273352;
  --sider-dark-darken-bg-color: #273352;
  --sider-dark-lighten-bg-color: #273352;
}


// :export {
//   name: "less";
//   mainColor: @mainColor;
//   fontSize: @fontSize;
// }
@iconify-bg-color: #5551;

// =================================
// ==============border-color=======
// =================================

// Dark-dark
@border-color-dark: #b6b7b9;

// Dark-light
@border-color-shallow-dark: #cececd;

// Light-dark
@border-color-light: ~`getPrefixVar('border-color-base')`;

// =================================
// ==============message==============
// =================================

// success-bg-color
@success-background-color: #f1f9ec;
// info-bg-color
@info-background-color: #e8eff8;
// warn-bg-color
@warning-background-color: #fdf6ed;
// danger-bg-color
@danger-background-color: #fef0f0;

// =================================
// ==============Header=============
// =================================

@header-dark-bg-color: var(--header-bg-color);
@header-dark-bg-hover-color: var(--header-bg-hover-color);
@header-light-bg-hover-color: #f6f6f6;
@header-light-desc-color: #7c8087;
@header-light-bottom-border-color: #eee;
// top-menu
@top-menu-active-bg-color: var(--header-active-menu-bg-color);

// =================================
// ==============Menu============
// =================================

// let -menu
@sider-dark-darken-bg-color: var(--sider-dark-darken-bg-color);
@sider-dark-lighten-bg-color: var(--sider-dark-lighten-bg-color);

// trigger
@trigger-dark-hover-bg-color: rgba(255, 255, 255, 0.2);
@trigger-dark-bg-color: rgba(255, 255, 255, 0.1);

// =================================
// ==============tree============
// =================================
// tree item hover background
@tree-hover-background-color: #f5f7fa;
// tree item hover font color
@tree-hover-font-color: #f5f7fa;

// =================================
// ==============link============
// =================================
@link-hover-color: ~`getPrefixVar('primary-color')`;
@link-active-color: ~`getPrefixVar('primary-color-active')`;

// =================================
// ==============Text color-=============
// =================================


// Label color
@text-color-call-out: #606266;

// Auxiliary information color-dark
@text-color-help-dark: #909399;

// =================================
// ==============button=============
// =================================

@button-primary-color: ~`getPrefixVar('primary-color')`;
@button-primary-hover-color: ~`getPrefixVar('primary-color-hover')`;
@button-primary-active-color: ~`getPrefixVar('primary-color-active')`;

@button-ghost-color: ~`getPrefixVar('white')`;
@button-ghost-hover-color: ~`getPrefixVar('white')`;
@button-ghost-hover-bg-color: #e1ebf6;
@button-ghost-active-color: ~`getPrefixVar('white')`;

@button-success-color:~`getPrefixVar('success-color')`;
@button-success-hover-color: ~`getPrefixVar('success-color-hover')`;
@button-success-active-color: ~`getPrefixVar('success-color-active')`;

@button-warn-color: ~`getPrefixVar('warning-color')`;
@button-warn-hover-color: ~`getPrefixVar('warning-color-hover')`;
@button-warn-active-color: ~`getPrefixVar('warning-color-active')`;

@button-error-color: ~`getPrefixVar('error-color')`;
@button-error-hover-color: ~`getPrefixVar('error-color-hover')`;
@button-error-active-color: ~`getPrefixVar('error-color-active')`;

@button-cancel-color: @text-color-call-out;
@button-cancel-bg-color: ~`getPrefixVar('white')`;
@button-cancel-border-color: @border-color-shallow-dark;

// Mouse over
@button-cancel-hover-color: ~`getPrefixVar('primary-color')`;
@button-cancel-hover-bg-color: ~`getPrefixVar('white')`;
@button-cancel-hover-border-color: ~`getPrefixVar('primary-color')`;
