package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

import javax.validation.Valid;

/**
 * MarketContractMilestoneAcceptance DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-30 01:59:34
 */
@ApiModel(value = "MarketContractMilestoneAcceptanceDTO对象", description = "市场合同里程碑验收信息")
@Data
@ExcelIgnoreUnannotated
public class MarketContractMilestoneAcceptanceDTO extends  ObjectDTO   implements Serializable{

    /**
     * 里程碑id
     */
    @ApiModelProperty(value = "里程碑id")
    @ExcelProperty(value = "里程碑id ", index = 0)
    private String milestoneId;

    /**
     * 验收人
     */
    @ApiModelProperty(value = "验收人")
    @ExcelProperty(value = "验收人 ", index = 1)
    private String acceptanceUserId;

    /**
     * 实际验收日期
     */
    @ApiModelProperty(value = "实际验收日期")
    @ExcelProperty(value = "实际验收日期 ", index = 2)
    private Date actualAcceptDate;

    /**
     * 本次验收比例
     */
    @ApiModelProperty(value = "本次验收比例")
    @ExcelProperty(value = "本次验收比例 ", index = 3)
    private BigDecimal acceptanceRatio;

    /**
     * 上传附件
     */
    @ApiModelProperty(value = "上传附件")
    @Valid
    @ExcelIgnore
    private List<FileDTO> fileList;


}
