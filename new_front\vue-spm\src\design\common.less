@charset "UTF-8";

body {
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans,
    sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', Segoe UI Symbol, 'Noto Color Emoji' !important;
}
//===========-常见样式-===========
.db {
  display: block !important;
}

.di {
  display: inline !important;
}

.dib {
  display: inline-block !important;
}

.w-full {
  display: block;
  width: 100%;
}

.h-full {
  min-height: 100%;
  position: relative;
}

//fix
//元素采用弹性BOX布局
.flex {
  display: flex;
}

//子元素占用区域份数
.flex-f1 {
  position: relative;
  flex: 1;
  width: 1px;
}

.flex-f2 {
  position: relative;
  flex: 2;
  width: 1px;
}

.flex-f3 {
  position: relative;
  flex: 3;
  width: 1px;
}

.flex-f4 {
  position: relative;
  flex: 4;
  width: 1px;
}

//占父盒子的二分之一
.flex-1f2 {
  flex: 0 0 50%;
  max-width: 50%;
}

//占父盒子的三分之一
.flex-1f3 {
  flex: 0 0 33.333%;
  max-width: 33.333%;
  width: 1px;
}

//占父盒子的四分之一
.flex-1f4 {
  flex: 0 0 25%;
  max-width: 25%;
  width: 1px;
}

//============对齐方式==============
//---子元素水平对齐----
//居中对齐
.flex-pc {
  justify-content: center;
}

//结尾对齐
.flex-pe {
  justify-content: flex-end;
}

//两端对齐
.flex-pj {
  justify-content: space-between;
}

//---子元素垂直对齐----
//子元素垂直居中
.flex-ac {
  align-items: center;
}

//子元素垂直结尾对齐
.flex-ae {
  align-items: flex-end;
}

//子元素水平垂直居中
.flex-pac {
  justify-content: center;
  align-items: center;
}

//=============================
//子元素竖向排列
.flex-ver {
  flex-direction: column;

  > .flex-f1,
  > .flex-f2,
  > .flex-f3,
  > .flex-f4,
  > .flex-1f2,
  > .flex-1f3,
  > .flex-1f4 {
    width: initial;
    height: 1px;
  }

  > .flex-auto {
    flex: auto;
    height: 1px
  }
}

//子元素折行
.flex-bm {
  flex-wrap: wrap;
}

//垂直居中
.di-vm {
  display: inline-block;
  vertical-align: middle;
}

//-------浮动 begin-------
.fl {
  float: left;
}

.fr {
  float: right;
}

.clear {
  zoom: 1;
}

.clear::after {
  content: '';
  display: block;
  clear: both;
}

//-------浮动 end--------
.cell {
  display: table-cell;
  vertical-align: middle;
}

//-------常见字号 begin-------
.fz0 {
  font-size: 0;
  overflow: hidden;
}

.fz12 {
  font-size: 12px !important;
}

.fz14 {
  font-size: 14px !important;
}

.fz16 {
  font-size: 16px !important;
}

.fz18 {
  font-size: 18px !important;
}

.fz20 {
  font-size: 20px !important;
}

.fz22 {
  font-size: 22px !important;
}

.fz24 {
  font-size: 24px !important;
}

.fz26 {
  font-size: 26px !important;
}

.fz28 {
  font-size: 28px !important;
}

.fz30 {
  font-size: 30px !important;
}

.fz32 {
  font-size: 32px !important;
}

.fz34 {
  font-size: 34px !important;
}

.fz36 {
  font-size: 36px !important;
}

.fz38 {
  font-size: 38px !important;
}

.fz40 {
  font-size: 40px !important;
}

.fz42 {
  font-size: 42px !important;
}

.fz44 {
  font-size: 44px !important;
}

.fz46 {
  font-size: 46px !important;
}

.fz48 {
  font-size: 48px !important;
}

.fz50 {
  font-size: 50px !important;
}

.fw-b {
  font-weight: bold;
}

.fw-d {
  font-weight: 600;
}

.fw-l {
  font-weight: lighter;
}

//-------常见字号 end-------

//-------变量间距 begin-------
.m-b {
  margin: ~`getPrefixVar('content-margin')`!important;
}

.m-b-t {
  margin-top: ~`getPrefixVar('content-margin')`!important;
}

.m-b-r {
  margin-right: ~`getPrefixVar('content-margin')`!important;
}

.m-b-b {
  margin-bottom: ~`getPrefixVar('content-margin')`!important;
}

.m-b-l {
  margin-left: ~`getPrefixVar('content-margin')`!important;
}

.m-b-lr {
  margin-left: ~`getPrefixVar('content-margin')`!important;
  margin-right: ~`getPrefixVar('content-margin')`!important;
}

.m-b-tb {
  margin-top: ~`getPrefixVar('content-margin')`!important;
  margin-bottom: ~`getPrefixVar('content-margin')`!important;
}

.p-b {
  padding: ~`getPrefixVar('content-padding')`!important;
}

.p-b-t {
  padding-top: ~`getPrefixVar('content-padding')`!important;
}

.p-b-r {
  padding-right: ~`getPrefixVar('content-padding')`!important;
}

.p-b-b {
  padding-bottom: ~`getPrefixVar('content-padding')`!important;
}

.p-b-l {
  padding-left: ~`getPrefixVar('content-padding')`!important;
}

.p-b-lr {
  padding-left: ~`getPrefixVar('content-padding')`!important;
  padding-right: ~`getPrefixVar('content-padding')`!important;
}

.p-b-tb {
  padding-top: ~`getPrefixVar('content-padding')`!important;
  padding-bottom: ~`getPrefixVar('content-padding')`!important;
}
//-------变量间距 end---------

//-------常见外间距 begin-------
.ma {
  margin: 0 auto !important;
}

.m0 {
  margin: 0 !important;
}

.m5 {
  margin: 5px !important;
}

.m10 {
  margin: 10px !important;
}

.m15 {
  margin: 15px !important;
}

.m20 {
  margin: 20px !important;
}

.m25 {
  margin: 25px !important;
}

.m30 {
  margin: 30px !important;
}

.m35 {
  margin: 35px !important;
}

.m40 {
  margin: 40px !important;
}

.m50 {
  margin: 50px !important;
}

.m60 {
  margin: 60px !important;
}

.mt0 {
  margin-top: 0;
}

.mt5 {
  margin-top: 5px !important;
}

.mt10 {
  margin-top: 10px !important;
}

.mt15 {
  margin-top: 15px !important;
}

.mt20 {
  margin-top: 20px !important;
}

.mt25 {
  margin-top: 25px !important;
}

.mt30 {
  margin-top: 30px !important;
}

.mt35 {
  margin-top: 35px !important;
}

.mt40 {
  margin-top: 40px !important;
}

.mt50 {
  margin-top: 50px !important;
}

.mt60 {
  margin-top: 60px !important;
}

.mr0 {
  margin-right: 0;
}

.mr5 {
  margin-right: 5px !important;
}

.mr10 {
  margin-right: 10px !important;
}

.mr15 {
  margin-right: 15px !important;
}

.mr20 {
  margin-right: 20px !important;
}

.mr25 {
  margin-right: 25px !important;
}

.mr30 {
  margin-right: 30px !important;
}

.mr35 {
  margin-right: 35px !important;
}

.mr40 {
  margin-right: 40px !important;
}

.mr50 {
  margin-right: 50px !important;
}

.mr60 {
  margin-right: 60px !important;
}

.mb0 {
  margin-bottom: 0;
}

.mb5 {
  margin-bottom: 5px !important;
}

.mb10 {
  margin-bottom: 10px !important;
}

.mb15 {
  margin-bottom: 15px !important;
}

.mb20 {
  margin-bottom: 20px !important;
}

.mb25 {
  margin-bottom: 25px !important;
}

.mb30 {
  margin-bottom: 30px !important;
}

.mb35 {
  margin-bottom: 35px !important;
}

.mb40 {
  margin-bottom: 40px !important;
}

.mb50 {
  margin-bottom: 50px !important;
}

.mb60 {
  margin-bottom: 60px !important;
}

.ml0 {
  margin-left: 0;
}

.ml5 {
  margin-left: 5px !important;
}

.ml10 {
  margin-left: 10px !important;
}

.ml15 {
  margin-left: 15px !important;
}

.ml20 {
  margin-left: 20px !important;
}

.ml25 {
  margin-left: 25px !important;
}

.ml30 {
  margin-left: 30px !important;
}

.ml35 {
  margin-left: 35px !important;
}

.ml40 {
  margin-left: 40px !important;
}

.ml50 {
  margin-left: 50px !important;
}

.ml60 {
  margin-left: 60px !important;
}

.mlr5 {
  margin-left: 5px !important;
  margin-right: 5px !important;
}

.mlr10 {
  margin-left: 10px !important;
  margin-right: 10px !important;
}

.mlr15 {
  margin-left: 15px !important;
  margin-right: 15px !important;
}

.mlr20 {
  margin-left: 20px !important;
  margin-right: 20px !important;
}

.mlr25 {
  margin-left: 25px !important;
  margin-right: 25px !important;
}

.mlr30 {
  margin-left: 30px !important;
  margin-right: 30px !important;
}

.mlr35 {
  margin-left: 35px !important;
  margin-right: 35px !important;
}

.mlr40 {
  margin-left: 40px !important;
  margin-right: 40px !important;
}

.mlr50 {
  margin-left: 50px !important;
  margin-right: 50px !important;
}

.mlr60 {
  margin-left: 60px !important;
  margin-right: 60px !important;
}

.mtb5 {
  margin-left: 5px !important;
  margin-right: 5px !important;
}

.mtb10 {
  margin-left: 10px !important;
  margin-right: 10px !important;
}

.mtb15 {
  margin-left: 15px !important;
  margin-right: 15px !important;
}

.mtb20 {
  margin-left: 20px !important;
  margin-right: 20px !important;
}

.mtb25 {
  margin-left: 25px !important;
  margin-right: 25px !important;
}

.mtb30 {
  margin-left: 30px !important;
  margin-right: 30px !important;
}

.mtb35 {
  margin-left: 35px !important;
  margin-right: 35px !important;
}

.mtb40 {
  margin-left: 40px !important;
  margin-right: 40px !important;
}

.mtb50 {
  margin-left: 50px !important;
  margin-right: 50px !important;
}

.mtb60 {
  margin-left: 60px !important;
  margin-right: 60px !important;
}

.mt-5 {
  margin-top: -5px !important;
}

.mt-10 {
  margin-top: -10px !important;
}

.mt-15 {
  margin-top: -15px !important;
}

.mt-20 {
  margin-top: -20px !important;
}

.mt-25 {
  margin-top: -25px !important;
}

.mt-30 {
  margin-top: -30px !important;
}

.mt-35 {
  margin-top: -35px !important;
}

.mt-40 {
  margin-top: -40px !important;
}

.mt-50 {
  margin-top: -50px !important;
}

.mt-60 {
  margin-top: -60px !important;
}

.mr-5 {
  margin-right: -5px !important;
}

.mr-10 {
  margin-right: -10px !important;
}

.mr-15 {
  margin-right: -15px !important;
}

.mr-20 {
  margin-right: -20px !important;
}

.mr-25 {
  margin-right: -25px !important;
}

.mr-30 {
  margin-right: -30px !important;
}

.mr-35 {
  margin-right: -35px !important;
}

.mr-40 {
  margin-right: -40px !important;
}

.mr-50 {
  margin-right: -50px !important;
}

.mr-60 {
  margin-right: -60px !important;
}

.mb-5 {
  margin-bottom: -5px !important;
}

.mb-10 {
  margin-bottom: -10px !important;
}

.mb-15 {
  margin-bottom: -15px !important;
}

.mb-20 {
  margin-bottom: -20px !important;
}

.mb-25 {
  margin-bottom: -25px !important;
}

.mb-30 {
  margin-bottom: -30px !important;
}

.mb-35 {
  margin-bottom: -35px !important;
}

.mb-40 {
  margin-bottom: -40px !important;
}

.mb-50 {
  margin-bottom: -50px !important;
}

.mb-60 {
  margin-bottom: -60px !important;
}

.ml-5 {
  margin-left: -5px !important;
}

.ml-10 {
  margin-left: -10px !important;
}

.ml-15 {
  margin-left: -15px !important;
}

.ml-20 {
  margin-left: -20px !important;
}

.ml-25 {
  margin-left: -25px !important;
}

.ml-30 {
  margin-left: -30px !important;
}

.ml-35 {
  margin-left: -35px !important;
}

.ml-40 {
  margin-left: -40px !important;
}

.ml-50 {
  margin-left: -50px !important;
}

.ml-60 {
  margin-left: -60px !important;
}

//-------常见内间距 begin-------
.p0 {
  padding: 0;
}

.p5 {
  padding: 5px !important;
}

.p10 {
  padding: 10px !important;
}

.p15 {
  padding: 15px !important;
}

.p20 {
  padding: 20px !important;
}

.p25 {
  padding: 25px !important;
}

.p30 {
  padding: 30px !important;
}

.p35 {
  padding: 35px !important;
}

.p40 {
  padding: 40px !important;
}

.p50 {
  padding: 50px !important;
}

.p60 {
  padding: 60px !important;
}

.pt0 {
  padding-top: 0;
}

.pt5 {
  padding-top: 5px !important;
}

.pt10 {
  padding-top: 10px !important;
}

.pt15 {
  padding-top: 15px !important;
}

.pt20 {
  padding-top: 20px !important;
}

.pt25 {
  padding-top: 25px !important;
}

.pt30 {
  padding-top: 30px !important;
}

.pt35 {
  padding-top: 35px !important;
}

.pt40 {
  padding-top: 40px !important;
}

.pt50 {
  padding-top: 50px !important;
}

.pt60 {
  padding-top: 60px !important;
}

.pr0 {
  padding-right: 0;
}

.pr5 {
  padding-right: 5px !important;
}

.pr10 {
  padding-right: 10px !important;
}

.pr15 {
  padding-right: 15px !important;
}

.pr20 {
  padding-right: 20px !important;
}

.pr25 {
  padding-right: 25px !important;
}

.pr30 {
  padding-right: 30px !important;
}

.pr35 {
  padding-right: 35px !important;
}

.pr40 {
  padding-right: 40px !important;
}

.pr50 {
  padding-right: 50px !important;
}

.pr60 {
  padding-right: 60px !important;
}

.pb0 {
  padding-bottom: 0;
}

.pb5 {
  padding-bottom: 5px !important;
}

.pb10 {
  padding-bottom: 10px !important;
}

.pb15 {
  padding-bottom: 15px !important;
}

.pb20 {
  padding-bottom: 20px !important;
}

.pb25 {
  padding-bottom: 25px !important;
}

.pb30 {
  padding-bottom: 30px !important;
}

.pb35 {
  padding-bottom: 35px !important;
}

.pb40 {
  padding-bottom: 40px !important;
}

.pb50 {
  padding-bottom: 50px !important;
}

.pb60 {
  padding-bottom: 60px !important;
}

.pl0 {
  padding-left: 0;
}

.pl5 {
  padding-left: 5px !important;
}

.pl10 {
  padding-left: 10px !important;
}

.pl15 {
  padding-left: 15px !important;
}

.pl20 {
  padding-left: 20px !important;
}

.pl25 {
  padding-left: 25px !important;
}

.pl30 {
  padding-left: 30px !important;
}

.pl35 {
  padding-left: 35px !important;
}

.pl40 {
  padding-left: 40px !important;
}

.pl50 {
  padding-left: 50px !important;
}

.pl60 {
  padding-left: 60px !important;
}

.plr5 {
  padding-left: 5px !important;
  padding-right: 5px !important;
}

.plr10 {
  padding-left: 10px !important;
  padding-right: 10px !important;
}

.plr15 {
  padding-left: 15px !important;
  padding-right: 15px !important;
}

.plr20 {
  padding-left: 20px !important;
  padding-right: 20px !important;
}

.plr25 {
  padding-left: 25px !important;
  padding-right: 25px !important;
}

.plr30 {
  padding-left: 30px !important;
  padding-right: 30px !important;
}

.plr35 {
  padding-left: 35px !important;
  padding-right: 35px !important;
}

.plr40 {
  padding-left: 40px !important;
  padding-right: 40px !important;
}

.plr50 {
  padding-left: 50px !important;
  padding-right: 50px !important;
}

.plr60 {
  padding-left: 60px !important;
  padding-right: 60px !important;
}

.ptb5 {
  padding-top: 5px !important;
  padding-bottom: 5px !important;
}

.ptb10 {
  padding-top: 10px !important;
  padding-bottom: 10px !important;
}

.ptb15 {
  padding-top: 15px !important;
  padding-bottom: 15px !important;
}

.ptb20 {
  padding-top: 20px !important;
  padding-bottom: 20px !important;
}

.ptb25 {
  padding-top: 25px !important;
  padding-bottom: 25px !important;
}

.ptb30 {
  padding-top: 30px !important;
  padding-bottom: 30px !important;
}

.ptb35 {
  padding-top: 35px !important;
  padding-bottom: 35px !important;
}

.ptb40 {
  padding-top: 40px !important;
  padding-bottom: 40px !important;
}

.ptb50 {
  padding-top: 50px !important;
  padding-bottom: 50px !important;
}

.ptb60 {
  padding-top: 60px !important;
  padding-bottom: 60px !important;
}

//-------常见内间距 end-------

//-----文字对齐方式 begin------
.tx-c {
  text-align: center;
}

.tx-l {
  text-align: left;
}

.tx-r {
  text-align: right;
}

//-----文字对齐方式 end--------

//-----文字对齐方式 begin------
.tx-lc {
  text-transform: lowercase; //英文始终小写。
}

.tx-uc {
  text-transform: uppercase; //英文始终大写。
}

.tx-cl {
  text-transform: capitalize; //英文首字母大写。
}

//-----文字对齐方式 begin------

//行距
.lh1 {
  line-height: 1;
}

.lh12 {
  line-height: 1.2em;
}

.lh14 {
  line-height: 1.4em;
}

.lh16 {
  line-height: 1.6em;
}

.lh18 {
  line-height: 1.8em;
}

//单行省略号
.flex-te {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

//2行省略号
.flex-te2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

//3行省略号
.flex-te3 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

//-----文字排版 end--------

//-----常见背景 begin-------
.bgn {
  background: none;
}

.bgff {
  background: #fff;
}

.bgfb {
  background: #fbfbfb;
}

.bgf5 {
  background: #f5f5f5;
}

.bgf0 {
  background: #f0f0f0;
}

//-----常见背景 end-------
//-----常见颜色 begin-------
.cff {
  color: #fff;
}

.c00 {
  color: #000;
}

.c33 {
  color: #333;
}

.c66 {
  color: #666;
}

.c99 {
  color: #999;
}

//手型
.pointer {
  cursor: pointer;
}

//-----常见颜色 end-------
///*正圆*/
.round {
  border-radius: 1000px !important;
  overflow: hidden;
}

//----常见尺寸 begin-------
//小
.size-s {
  width: 80px !important;
  height: 80px !important;
  line-height: 80px !important;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

//中
.size-m {
  width: 120px !important;
  height: 120px !important;
  line-height: 120px !important;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

//默认
.size {
  width: 160px !important;
  height: 160px !important;
  line-height: 160px !important;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

//大
.size-b {
  width: 200px !important;
  height: 200px !important;
  line-height: 200px !important;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

//----常见尺寸 begin-------

//图片盒子
.img-wrap {
  overflow: hidden;
  position: relative;
  display: block;

  img {
    width: 100%;
    height: auto;
    display: block;
    z-index: 1;
  }

  p {
    position: absolute;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 2;
    display: block;
    color: #fff;
    background: rgba(0, 0, 0, 0.5);
    padding-top: 10px !important;
    padding-bottom: 10px !important;
  }
}

// table右侧按钮组样式

//.action-btn {
//  color: #000;
//}

.action-btn {
  //display: inline-block;
  color: ~`getPrefixVar('primary-color')`;
}

.action-btn:hover {
  transition: all 0.2s ease-in;
  cursor: pointer;
  color: @link-active-color !important;
}

//删除弹窗样式
.deleted-node-modal {
  .ant-modal-body {
    padding: 24px !important;
  }

  p {
    margin: 0;

    .error {
      color: #f00;
    }
  }
}

//关键字高亮显示
.text-highlight {
  color: #f00;
}

//数字输入框样式
.ant-input-number {
  width: 100% !important;
}

//tab切换样式

.ant-descriptions-bordered .ant-descriptions-item-label {
  background-color: #f3f3f3;
}

.primary-color {
  color: ~`getPrefixVar('primary-color')`;
}

.success-color {
  color:~`getPrefixVar('success-color')`;
}

.info-color {
  color: ~`getPrefixVar('info-color')`;
}

.warning-color {
  color: ~`getPrefixVar('warning-color')`;
}

.error-color {
  color:~ `getPrefixVar('error-color')`;
}

//覆盖搜索框样式
.ant-input-search {
  width: unset;
}

//表格操作按钮统一样式
.operation-btn-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;

  .divider {
    padding: 0 10px !important;
  }
}

.user-wrapper {
  width: 100%;
  float: left;
  overflow: hidden;
  padding-left: 10px !important;

  .main-title {
    padding: 0 8px !important;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.red {
  color: red !important;
}

.green {
  color: #46ba7b !important;
}

.tf_blue {
  font-size: 16px;
  color: #19243b;
  margin: 0;
  padding-left: 15px;
  font-weight: normal;
  position: relative;
  flex: 1;
}

.tf_cc {
  font-size: 16px;
  color: #19243b;
  margin: 0;
  padding-left: 15px;
  font-weight: normal;
  position: relative;
  flex: 1;
}

.tf_blue::after {
  content: '';
  position: absolute;
  left: 0;
  height: 16px;
  width: 4px;
  top: 50%;
  margin-top: -8px;
  background: #0960bd;
}

.tf_cc::after {
  content: '';
  position: absolute;
  left: 0;
  height: 16px;
  width: 4px;
  top: 50%;
  margin-top: -8px;
  background: #ccc;
}

// 边框线
.bor-t {
  border-top: 1px solid #d5d5d5;
}

.bor-b {
  border-bottom: 1px solid #d5d5d5;
}

.bor-r {
  border-right: 1px solid #d5d5d5;
}

.bor-l {
  border-left: 1px solid #d5d5d5;
}

// 项目概况步骤条
.stepsFlexUnset {
  flex: unset !important;

  .ant-steps-item-icon {
    position: absolute;
    top: 30%;
    left: 15%;
  }

  .ant-steps-item-content {
    margin-top: -50px !important;

    .ant-steps-item-title {
      position: absolute;
      top: 30%;
      right: 65%;
      padding-right: 0;

      .title {
        position: absolute;
        top: -70%;
      }
    }

    .ant-steps-item-description {
      position: absolute;
      top: 60%;
      left: 2%;
    }
  }
}

// 项目概况工时总览进度条样式调整
.SurveyHourOverviewProgressClass {
  .ant-progress-inner {
    border-radius: 2px !important;
  }

  .ant-progress-bg {
    border-radius: 2px !important;
    border-top-right-radius: unset;
    border-bottom-left-radius: unset;
  }

  .ant-progress-success-bg {
    background-color: #28d094;
    border-radius: 2px !important;
  }
}

.SurveyHourOverviewProgressEstimateClass {
  .ant-progress-inner {
    border-radius: 2px !important;
    height: 22px;
  }

  .ant-progress-bg {
    border-radius: 2px !important;
  }

  .ant-progress-success-bg {
    border-radius: 2px !important;
  }
}

.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
