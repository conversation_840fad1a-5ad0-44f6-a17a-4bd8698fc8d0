<template>
  <div
    class="base-info-wrap"
  >
    <Ztitle :title="'风险信息'" />
    <a-descriptions :column="4">
      <a-descriptions-item
        label="编号"
        span="1"
      >
        {{ details?.number }}
      </a-descriptions-item>
      <a-descriptions-item
        label="标题"
        span="1"
      >
        {{ details?.name }}
        <!--      {{ details?.createTime?dayjs(details.createTime).format('YYYY-MM-DD HH:mm:ss'):'' }}-->
      </a-descriptions-item>
      <a-descriptions-item
        label="风险类型"
        span="1"
      >
        {{ details?.riskTypeName }}
      </a-descriptions-item>
      <a-descriptions-item
        label="状态"
        span="1"
      >
        <DataStatusTag
          v-if="details?.dataStatus?.name"
          :status-data="details?.dataStatus"
        />
        <span v-else>-</span>
      </a-descriptions-item>
      <a-descriptions-item
        label="发生概率"
        span="1"
      >
        {{ details?.riskProbabilityName }}
      </a-descriptions-item>
      <a-descriptions-item
        label="识别人"
        span="1"
      >
        {{ details?.discernPersonName }}
      </a-descriptions-item>
      <a-descriptions-item
        label="期望完成时间"
        span="1"
      >
        {{ details?.predictEndTime?dayjs(details.predictEndTime).format('YYYY-MM-DD'):'' }}
      </a-descriptions-item>
      <a-descriptions-item
        label="影响程度"
        span="1"
      >
        {{ details?.riskInfluenceName }}
      </a-descriptions-item>
      <a-descriptions-item
        label="预估发生时间"
        span="1"
      >
        {{ details?.predictStartTimeName }}
      </a-descriptions-item>
      <a-descriptions-item
        label="风险描述"
        span="4"
      >
        {{ details?.remark }}
      </a-descriptions-item>
      <a-descriptions-item
        label="负责人"
        span="1"
      >
        {{ details?.principalName }}
      </a-descriptions-item>
      <a-descriptions-item
        label="应对策略"
        span="3"
      >
        {{ details?.copingStrategyName }}
      </a-descriptions-item>

      <a-descriptions-item
        label="应对措施"
        span="4"
      >
        {{ details?.solutions }}
      </a-descriptions-item>
    </a-descriptions>
    <div class="mt15" />
    <Ztitle :title="'类型属性'" />
    <a-descriptions
      v-if="details?.typeAttrValueDTOList?.length>0"
      :column="4"
    >
      <a-descriptions-item
        v-for="(item,index) of details?.typeAttrValueDTOList"
        :key="index"
        :label="item?.name"
        span="1"
      >
        {{ item?.value }}
      </a-descriptions-item>
    </a-descriptions>
    <span
      v-else
      class="ml15"
    >无</span>
    <div class="mt15" />
    <Ztitle :title="'基础信息'" />
    <a-descriptions :column="4">
      <a-descriptions-item
        label="创建人"
        span="1"
      >
        {{ details?.creatorName }}
      </a-descriptions-item>
      <a-descriptions-item
        label="创建时间"
        span="1"
      >
        {{ details?.createTime?dayjs(details.createTime).format('YYYY-MM-DD HH:mm:ss'):'' }}
      </a-descriptions-item>
      <a-descriptions-item
        label="修改人"
        span="1"
      >
        {{ details?.modifyName }}
      </a-descriptions-item>
      <a-descriptions-item
        label="修改时间"
        span="1"
      >
        {{ details?.modifyTime?dayjs(details.modifyTime).format('YYYY-MM-DD HH:mm:ss'):'' }}
      </a-descriptions-item>
    </a-descriptions>
  </div>
</template>

<script lang="ts">
import {
  DataStatusTag, Layout2, useDrawer,
} from 'lyra-component-vue3';
import {
  defineComponent, reactive, toRefs, onMounted,
} from 'vue';
import { Empty, Descriptions } from 'ant-design-vue';
// import { PlusOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import { useRoute } from 'vue-router';
import Ztitle from '../../../components/Ztitle.vue';
import Api from '/@/api';
import AddTableNode from '../../model/AddTableNode.vue';
export default defineComponent({
  name: 'BasicInformation',
  components: {
    DataStatusTag,
    ADescriptions: Descriptions,
    ADescriptionsItem: Descriptions.Item,
    Ztitle,
  },
  props: {
    details: {},
  },
  emits: ['change'],
  setup(props, { emit }) {
    const route = useRoute();
    const state = reactive({
      selectChangeData: { id: route.query.folderId },
    });
    const state6 = reactive({
      btnList: [{ type: 'edit' }],
    });
    function addSuccess() {
      emit('change');
    }
    return {
      ...toRefs(state),
      ...toRefs(state6),
      dayjs,
      addSuccess,
    };
  },
});
</script>

<style lang="less" scoped>
.base-info-wrap {
  padding: ~`getPrefixVar('content-padding-top')` ~`getPrefixVar('content-padding-left')`;
}
</style>
