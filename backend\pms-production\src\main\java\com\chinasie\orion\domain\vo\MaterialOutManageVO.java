package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

import java.util.List;

/**
 * MaterialOutManage VO对象
 *
 * <AUTHOR>
 * @since 2024-06-04 17:08:55
 */
@ApiModel(value = "MaterialOutManageVO对象", description = "物质出库管理")
@Data
public class MaterialOutManageVO extends ObjectVO implements Serializable {

    /**
     * 资产类型
     */
    @ApiModelProperty(value = "资产类型")
    private String assetType;
    @ApiModelProperty(value = "资产类型名称")
    private String assetTypeName;

    /**
     * 资产代码
     */
    @ApiModelProperty(value = "资产代码")
    private String assetCode;


    /**
     * 资产编码
     */
    @ApiModelProperty(value = "资产编码")
    private String number;


    /**
     * 资产名称
     */
    @ApiModelProperty(value = "资产名称")
    private String assetName;


    /**
     * 成本中心
     */
    @ApiModelProperty(value = "成本中心")
    private String costCenter;
    /**
     * 成本中心
     */
    @ApiModelProperty(value = "成本中心名称")
    private String costCenterName;


    /**
     * 规格型号
     */
    @ApiModelProperty(value = "规格型号")
    private String specificationModel;


    /**
     * 物质去向
     */
    @ApiModelProperty(value = "物质去向")
    private String materialDestination;


    /**
     * 出库原因
     */
    @ApiModelProperty(value = "出库原因")
    private String outReason;


//    /**
//     * 出库时间
//     */
//    @ApiModelProperty(value = "出库时间")
//    private Date outDate;


    /**
     * 出库数量
     */
    @ApiModelProperty(value = "出库数量")
    private Integer outNum;


    /**
     * 库存数量
     */
    @ApiModelProperty(value = "库存数量")
    private Integer stockNum;



    /**
     * 下次检定日期
     */
    @ApiModelProperty(value = "下次检定日期")
    private Date nextVerificationDate;

    /**
     * 是否需要检定
     */
    @ApiModelProperty(value = "是否需要检定")
    private Boolean isVerification;

    /**
     * 物质所在基地
     */
    @ApiModelProperty(value = "物质所在基地")
    private String baseId;
    @ApiModelProperty(value = "物质所在基地编号")
    private String baseCode;
    @ApiModelProperty(value = "物质所在基地名称")
    private String baseName;
    /**
     * 责任人工号
     */
    @ApiModelProperty(value = "责任人工号")
    private String rspUserNo;

    /**
     * 责任人名称
     */
    @ApiModelProperty(value = "责任人名称")
    private String rspUserName;

    /**
     * 使用人工号
     */
    @ApiModelProperty(value = "使用人工号")
    private String useUserNo;

    /**
     * 使用人名称
     */
    @ApiModelProperty(value = "使用人名称")
    private String useUserName;

    /**
     * 资产入库日期
     */
    @ApiModelProperty(value = "资产入库日期")
    private Date enterDate;

    /**
     * 是否计量器具
     */
    @ApiModelProperty(value = "是否计量器具")
    private Boolean isMetering;

    /**
     * 是否向电厂报备
     */
    @ApiModelProperty(value = "是否向电厂报备")
    private Boolean isReport;

    /**
     * 检定是否超期
     */
    @ApiModelProperty(value = "检定是否超期")
    private Boolean isOverdue;

    /**
     * 物质应用作业(工单号)
     */
    @ApiModelProperty(value = "物质应用作业(工单号)")
    private String jobNo;

    /**
     * 作业名称
     */
    @ApiModelProperty(value = "作业名称")
    private String jobName;

    @ApiModelProperty(value = "台账类型")
    private String type;

    @ApiModelProperty(value = "台账类型名称")
    private String typeName;

    @ApiModelProperty(value = "实际入场日期")
    private Date actInDate;

    @ApiModelProperty(value = "实际离场日期")
    private Date actOutDate;

    @ApiModelProperty(value = "计划入场日期")
    private Date inDate;

    @ApiModelProperty(value = "计划离场日期")
    private Date outDate;

    @ApiModelProperty(value = "物资来源ID：指 物资管理的ID 方便溯源对于作业管理")
    private String sourceId;

    @ApiModelProperty(value = "是否合格")
    private Boolean isPass;

    @ApiModelProperty(value = "是否可用")
    private Boolean isAvailable;
    @ApiModelProperty(value = "进场倒计时（天）")
    private long inDays;

    @ApiModelProperty(value = "数量")
    private Integer inputStockNum;

    @ApiModelProperty(value = "是否再次入场")
    private Boolean isAgainIn;

    @ApiModelProperty(value = "产品编码")
    private String productCode;

    /**
     * 工具状态
     */
    @ApiModelProperty(value = "工具状态")
    private String toolStatus;


    /**
     * 检定维护周期
     */
    @ApiModelProperty(value = "检定维护周期")
    private Integer maintenanceCycle;

    /**
     * 工具状态名称
     */
    @ApiModelProperty(value = "工具状态名称")
    private String toolStatusName;

    @ApiModelProperty(value = "固定资产附件")
    private List<FileVO> fileVOList;
}
