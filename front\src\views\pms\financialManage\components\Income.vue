<script setup lang="ts">
import {
  BasicCard, OrionTable,
} from 'lyra-component-vue3';
import {
  ref, reactive,
} from 'vue';
import { STableSummaryRow, STableSummaryCell } from '@surely-vue/table';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});

const dataSource = ref([]);

const tableOptions = {
  rowSelection: {},
  showToolButton: false,
  isSpacing: false,
  isFilter2: false,
  api: null,
  showSmallSearch: false,
  showIndexColumn: false,
  pagination: false,
  showTableSetting: false,
  columns: [
    {
      title: '年份',
      dataIndex: 'year',
      width: 100,
    },
    {
      title: '凭证号',
      dataIndex: 'voucherNumber',
      width: 100,
    },
    {
      title: '挂账金额',
      dataIndex: 'accruedAmt',
      isMoney: true,
    },
    {
      title: '已清账金额',
      dataIndex: 'clearedAmt',
      isMoney: true,
    },
    {
      title: '未结清预收款金额',
      dataIndex: 'unAdvReceivableAmt',
      isMoney: true,
    },
    {
      title: '本次清账金额',
      dataIndex: 'currentClearAmt',
      isMoney: true,
    },
    {
      title: '本次清账金额（不含税）',
      dataIndex: 'currentClearAmtExTax',
      isMoney: true,
      width: 200,
    },
    {
      title: '不冲销原因',
      dataIndex: 'noAmortizationReason',
    },
  ],
};

const incomeTableRef = ref();
const state = reactive({
  accruedAmt: 0,
  clearedAmt: 0,
  unAdvReceivableAmt: 0,
  currentClearAmt: 0,
  currentClearAmtExTax: 0,
});

function reload() {
  incomeTableRef.value?.reload();
}

function setFnTotal(data) {
  let accruedAmt = 0;
  let clearedAmt = 0;
  let unAdvReceivableAmt = 0;
  let currentClearAmt = 0;
  let currentClearAmtExTax = 0;

  data.forEach((item) => {
    // 计算总额
    accruedAmt += Number(item.accruedAmt || 0); // 确保转换为数字
    clearedAmt += Number(item.clearedAmt || 0);
    unAdvReceivableAmt += Number(item.unAdvReceivableAmt || 0);
    currentClearAmt += Number(item.currentClearAmt || 0);
    currentClearAmtExTax += Number(item.currentClearAmtExTax || 0);
  });

  state.accruedAmt = accruedAmt;
  state.clearedAmt = clearedAmt;
  state.unAdvReceivableAmt = unAdvReceivableAmt;
  state.currentClearAmt = currentClearAmt;
  state.currentClearAmtExTax = currentClearAmtExTax;
}

function setValues(data) {
  if (data.length > 0) {
    setFnTotal(data);
    dataSource.value = data;
  }
}

defineExpose({
  setValues,
});
</script>

<template>
  <BasicCard title="预收款信息：">
    <OrionTable
      ref="incomeTableRef"
      :dataSource="dataSource"
      :options="tableOptions"
      summary-fixed
    >
      <template #summary>
        <div  class="fix-summary">
          <div>
            <span class="red">*</span> 合计栏：
          </div>
          <div
            v-for="(value, key, index) in state"
            :key="index"
            :index="index + 3"
            :class="'rq' + (index + 1)"
          >
            {{ value ? value.toFixed(2) : '0.00' }}
          </div>
        </div>
      </template>
    </OrionTable>
  </BasicCard>
</template>
<style scoped lang="less">
.fix-summary{
  display: flex;
  justify-content: flex-start;
}
.rq1{
  position: absolute;
  left: 259px;
}
.rq2{
  position: absolute;
  left: 420px;
}
.rq3{
  position: absolute;
  left: 580px;
}
.rq4{
  position: absolute;
  left: 740px;
}
.rq5{
  position: absolute;
  left: 900px;
}
</style>
