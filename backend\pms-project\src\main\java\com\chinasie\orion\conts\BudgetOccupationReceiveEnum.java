package com.chinasie.orion.conts;

public enum BudgetOccupationReceiveEnum {
    OCCUPATION("1", "占用"),
    RECEIVE("2", "领用"),
            ;

    private String code;

    private String description;

    BudgetOccupationReceiveEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static String getBudgetOccupationReceive(String code){
        if (OCCUPATION.getCode().equals(code)) {
            return OCCUPATION.getDescription();
        }
        if (RECEIVE.getCode().equals(code)) {
            return RECEIVE.getDescription();
        }
        return "";
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
