package com.chinasie.orion.domain.vo.projectOverviewZgh;


import com.chinasie.orion.file.api.domain.vo.FileVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 项目毛利
 */
@Data
@ApiModel(value = "ProjectDocumentCountVO", description = "项目知识文档库")
public class ProjectDocumentCountVO {

    /**
     * 分组名称
     */
    @ApiModelProperty("分组名称")
    private String groupName;

    /**
     * 文件列表
     */
    @ApiModelProperty("文件列表")
    private List<FileVO> fileVOS=new ArrayList<>();

}
