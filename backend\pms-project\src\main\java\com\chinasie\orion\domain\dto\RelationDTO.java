package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/06/10:49
 * @description:
 */
@ApiModel(value = "RelationDTO对象", description = "基类关系")
public class RelationDTO implements Serializable {

    /**
     * 副Id
     */
    @ApiModelProperty(value = "副Id")
    private String toId;

    /**
     * 顺序
     */
    @ApiModelProperty(value = "顺序")
    private Integer sort;

    /**
     * 修改人ID
     */
    @ApiModelProperty(value = "修改人ID")
    private String modifyId;

    /**
     * 主id
     */
    @ApiModelProperty(value = "主id")
    private String fromId;

    /**
     * 创建者ID
     */
    @ApiModelProperty(value = "创建者ID")
    private String creatorId;

    /**
     * 类名称
     */
    @ApiModelProperty(value = "类名称")
    private String className;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 副类名
     */
    @ApiModelProperty(value = "副类名")
    private String toClass;

    /**
     * 主类名
     */
    @ApiModelProperty(value = "主类名")
    private String fromClass;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    public String getToId(){
        return toId;
    }

    public void setToId(String toId) {
        this.toId = toId;
    }

    public Integer getSort(){
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getModifyId(){
        return modifyId;
    }

    public void setModifyId(String modifyId) {
        this.modifyId = modifyId;
    }

    public String getFromId(){
        return fromId;
    }

    public void setFromId(String fromId) {
        this.fromId = fromId;
    }

    public String getCreatorId(){
        return creatorId;
    }

    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    public String getClassName(){
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public String getId(){
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getToClass(){
        return toClass;
    }

    public void setToClass(String toClass) {
        this.toClass = toClass;
    }

    public String getFromClass(){
        return fromClass;
    }

    public void setFromClass(String fromClass) {
        this.fromClass = fromClass;
    }

    public Date getCreateTime(){
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getStatus(){
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getModifyTime(){
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

}
