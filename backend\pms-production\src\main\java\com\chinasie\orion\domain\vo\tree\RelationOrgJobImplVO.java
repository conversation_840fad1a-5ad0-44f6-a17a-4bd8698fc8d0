package com.chinasie.orion.domain.vo.tree;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.chinasie.orion.constant.StatisticField;
import com.chinasie.orion.constant.StatisticType;
import com.chinasie.orion.domain.vo.job.BeforeAndAfterFourDay;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/11/18/16:32
 * @description:
 */
@Data
public class RelationOrgJobImplVO implements Serializable {
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "大修组织ID")
    private String repairOrgId;
    @ApiModelProperty(value = "工单名称")
    private String  jobName;
    @ApiModelProperty(value = "关系ID")
    private String  relationId;
    @ApiModelProperty(value = "工单id")
    private String  jobId;
    @ApiModelProperty(value = "工单号")
    private String  jobNumber;
    @ApiModelProperty(value = "大修伦次")
    private String  repairRound;
    @ApiModelProperty(value = "阶段")
    private String  phase;
    @ApiModelProperty(value = "责任人id")
    private String  rspUserId;
    @ApiModelProperty(value = "责任人名称")
    private String  rspUserName;
    @ApiModelProperty(value = "责任人工号")
    private String  rspUserCode;

    @ApiModelProperty(value = "实际开始时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date actualBeginTime;

    /**
     *  已取票数量：统计有实际开工时间的作业总数；
     * 开工数量：按昨天、今天、明天、后天四个时间段，与实际开工时间进行比对，并统计总数据；
     * 已还票数量：统计有实际完工时间的作业总数；
     * 进展：已完工总数/作业总数
     */
    @ApiModelProperty(value = "已取票数量：统计有实际开工时间的作业总数；")
    @StatisticField(value = "haveActBeginDateCount", type = StatisticType.SUM)
    private  Integer  haveActBeginDateCount=0;

    @ApiModelProperty(value = "已还票数量：统计有实际完工时间的作业总数；")
    @StatisticField(value = "haveActFinishDateCount", type = StatisticType.SUM)
    private  Integer  haveActFinishDateCount=0;

    @ApiModelProperty(value = "作业总数")
    @StatisticField(value = "jobCount", type = StatisticType.SUM)
    private  Integer  jobCount=0;
    @ApiModelProperty(value = "已完工总数")
    @StatisticField(value = "finishedCount", type = StatisticType.SUM)
    private  Integer  finishedCount=0;
    @ApiModelProperty(value = "进展（%）")
    @StatisticField(value = "requiredCount", type = StatisticType.PERCENTAGE, fields = {"finishedCount","jobCount"})
    private double requiredCount=0;


    @ApiModelProperty(value = "昨天统计")
    @StatisticField(value = "yesterdayCount", type = StatisticType.SUM)
    private Integer  yesterdayCount=0;

    @ApiModelProperty(value = "当天统计")
    @StatisticField(value = "todayCount", type = StatisticType.SUM)
    private Integer  todayCount=0;
    @ApiModelProperty(value = "明天统计")
    @StatisticField(value = "tomorrowCount", type = StatisticType.SUM)
    private Integer  tomorrowCount=0;
    @ApiModelProperty(value = "后天统计")
    @StatisticField(value = "dayAfterTomorrowCount", type = StatisticType.SUM)
    private Integer  dayAfterTomorrowCount=0;


    @ApiModelProperty(value = "前后四天")
    public Map<String, BeforeAndAfterFourDay> beforeAndAfterFourDayMap;


    @ApiModelProperty(value = "实际结束时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date actualEndTime;


    @ApiModelProperty(value = "协助")
    private Boolean isCollaboration;
    @ApiModelProperty(value = "协同专业名称拼接")
    private String collaborationNames;
    @ApiModelProperty(value = "协同专业ID拼接")
    private String collaborationIds;
}
