package org.jeecg.modules.demo.dkm.util;

/**
 * 字符串检查工具类
 *
 * <AUTHOR>
 * @date 2025-4-16
 */
public class StringCheckUtil {

    /**
     * 判断字符串是否为空
     *
     * @param str 待检查的字符串
     * @return 如果为null或空字符串（包括只包含空格的字符串）返回true，否则返回false
     */
    public static boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }

    /**
     * 判断字符串是否不为空
     *
     * @param str 待检查的字符串
     * @return 如果不为null且不是空字符串（包括只包含空格的字符串）返回true，否则返回false
     */
    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    /**
     * 获取非空字符串，如果原字符串为空则返回默认值
     *
     * @param str 原字符串
     * @param defaultValue 默认值
     * @return 如果原字符串不为空，返回原字符串；否则返回默认值
     */
    public static String defaultIfBlank(String str, String defaultValue) {
        return isEmpty(str) ? defaultValue : str;
    }
} 