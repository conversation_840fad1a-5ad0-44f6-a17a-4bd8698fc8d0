package com.chinasie.orion.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.base.api.domain.vo.SimpleUserVO;
import com.chinasie.orion.base.api.service.UserBaseApiService;
import com.chinasie.orion.bo.SysParamConfBO;
import com.chinasie.orion.conts.SchemeListTypeEnum;
import com.chinasie.orion.dict.SchemeDict;
import com.chinasie.orion.domain.entity.ObjectEntity;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.entity.ProjectSchemeApplyApproval;
import com.chinasie.orion.domain.entity.ProjectSchemePrePost;
import com.chinasie.orion.domain.request.ListRequest;
import com.chinasie.orion.domain.vo.ProjectSchemeVO;
import com.chinasie.orion.manager.ProjectSchemeAuthManager;
import com.chinasie.orion.manager.SchemeCommonManager;
import com.chinasie.orion.manager.SchemeStatusProcessor;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.page.OrderItem;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.StatusRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.service.ProjectRoleService;
import com.chinasie.orion.service.ProjectRoleUserService;
import com.chinasie.orion.service.ProjectSchemeApplyApprovalService;
import com.chinasie.orion.service.ProjectSchemeListService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service("projectSchemeList")
@AllArgsConstructor
public class ProjectSchemeListServiceImpl implements ProjectSchemeListService {

    private final SchemeCommonManager commonManager;
    private final ProjectSchemeApplyApprovalService applyApprovalService;
    private final ProjectSchemeAuthManager projectSchemeAuthManager;
    private final SchemeStatusProcessor statusProcessor;
    private final UserRedisHelper userRedisHelper;
    private SysParamConfBO sysParamConfBO;
    private final ProjectRoleService projectRoleService;
    private final ProjectRoleUserService projectRoleUserService;
    private final DictRedisHelper dictRedisHelper;

    private final StatusRedisHelper statusRedisHelper;

    private final UserBaseApiService userBaseApiService;


    @Override
    public List<ProjectSchemeVO> schemeList(ListRequest request) throws Exception {
        long startTime = System.currentTimeMillis();
        String processType = sysParamConfBO.getProcessType();

        List<OrderItem> orderItems = request.getOrders();
        List<ProjectScheme> projectSchemeList = commonManager.listByProjectId(request.getProjectId(),request.getType());
        if(CollectionUtils.isEmpty(projectSchemeList)){
            return new ArrayList<>();
        }
        //过滤人员数据
        List<ProjectScheme> projectSchemes = commonManager.filterateByUser(projectSchemeList,request.getType());
        long endTime00 = System.currentTimeMillis();
        log.debug("start00: 00阶段查询任务执行耗时: {} 毫秒", (endTime00 - startTime));
        statusProcessor.statusHandle(projectSchemes);
        long endTime0 = System.currentTimeMillis();
        log.debug("start0: 0阶段查询任务执行耗时: {} 毫秒", (endTime0 - endTime00));
        Boolean isManager = projectRoleUserService.isPmRoleUser(request.getProjectId());
        long endTime1 = System.currentTimeMillis();
        log.debug("start1: 一阶段查询任务执行耗时: {} 毫秒", (endTime1 - endTime0));
        List<ProjectSchemeVO> resultContent = CollUtil.toList();
        List<ProjectScheme> schemeList11 = commonManager.listProjectSchemeByProjectId(request.getProjectId(), request.getSearchConditions(), isManager, orderItems);
        long endTime2 = System.currentTimeMillis();
        log.debug("start2: 二阶段查询任务执行耗时: {} 毫秒%n", (endTime2 - endTime1));
        List<List<SearchCondition>> searchConditions = request.getSearchConditions();

        if (CollUtil.isEmpty(schemeList11)) {
            return resultContent;
        }
        Map<String,ProjectScheme> projectSchemeMap = projectSchemes.stream().collect(Collectors.toMap(ObjectEntity::getId, Function.identity()));
        List<ProjectScheme> schemeList =new ArrayList<>();
        schemeList11.forEach(item->{
            ProjectScheme projectScheme =  projectSchemeMap.get(item.getId());
            if(!Objects.isNull(projectScheme)){
                schemeList.add(projectScheme);
            }
        });
        if(CollectionUtils.isEmpty(schemeList)){
            return new ArrayList<>();
        }
        //过滤需要展示的项目计划
        List<ProjectScheme> viewList = new ArrayList<>();
        if (request.getTypeEnum() == SchemeListTypeEnum.PROJECT_SCHEME) {
            List<ProjectScheme> list = new ArrayList<>();
            if (!CollectionUtils.isEmpty(searchConditions)) {
                viewList = commonManager.filterView(schemeList, request.getTypeEnum());
                if(!CollectionUtils.isEmpty(viewList)){
                    list.addAll(viewList);
                }
            }else{
                list.addAll(schemeList);
            }
            if(!CollectionUtils.isEmpty(list)){
                viewList = commonManager.getList(list, projectSchemes);
            }
        } else {
            viewList = commonManager.filterView(schemeList, request.getTypeEnum());
        }
        if (CollUtil.isEmpty(viewList)) {
            return resultContent;
        }
        long endTime3 = System.currentTimeMillis();
        log.debug("start3: 三阶段查询任务执行耗时: {} 毫秒%n", (endTime3 - endTime2));
        long endTime4 = System.currentTimeMillis();
        log.debug("start4: 四阶段查询任务执行耗时: {} 毫秒%n", (endTime4 - endTime3));
        List<ProjectSchemeVO> schemeVOS = BeanCopyUtils.convertListTo(viewList, ProjectSchemeVO::new);
        //查询
        long endTime5 = System.currentTimeMillis();
        log.debug("start5: 五阶段查询任务执行耗时: {} 毫秒%n", (endTime5 - endTime4));

        Map<String, DictValueVO> planActiveMap = dictRedisHelper.getDictMapByCode(SchemeDict.PLAN_ACTIVE);
        Map<String, Map<String, String>> dictMap = new HashMap<>();
        planActiveMap.forEach((key, value) -> {
            Map<String, String> map = new HashMap<>();
            map.put("name", value.getDescription());
            map.put("value", key);
            dictMap.put(key, map);
        });

        long endTime6 = System.currentTimeMillis();
        log.debug("start6: 六阶段查询任务执行耗时: {} 毫秒%n", (endTime6 - endTime5));
//        Map<String, DictValueVO> copeDict = dictRedisHelper.getDictMapByCode(SchemeDict.ENFORECE_SCORE);
//        Map<String, DictValueVO> typeDict = dictRedisHelper.getDictMapByCode(SchemeDict.ENFORECE_TYPE);
//        Map<String, DictValueVO> contentDict = dictRedisHelper.getDictMapByCode(SchemeDict.WORK_CONTENT);
//            o.setWorkContentName(contentDict.getOrDefault(o.getWorkContent(),new DictValueVO()).getDescription());
//            o.setEnforceScopeName(copeDict.getOrDefault(o.getEnforceScope(),new DictValueVO()).getDescription());
//            o.setEnforceTypeName(typeDict.getOrDefault(o.getEnforceType(),new DictValueVO()).getDescription());
        schemeVOS.forEach(o->{
            o.setIsManager(isManager);
            if (StrUtil.isNotBlank(o.getPlanActive())) {
                List<String> names = List.of(o.getPlanActive().split(","));
                List<Map<String, String>> mapList = new ArrayList<>();
                for (String name : names) {
                    if (ObjectUtil.isNotEmpty(dictMap.get(name))) {
                        mapList.add(dictMap.get(name));
                    }
                }
                o.setPlanActiveList(mapList);
            }
            o.setProcessType(processType);
            if (Objects.nonNull(o.getEndTime()) && DateUtil.compare(o.getEndTime(), new Date()) < 0) {
                o.setOverdueDays(DateUtil.betweenDay(o.getEndTime(), new Date(), false));
            }
        });
        long endTime7 = System.currentTimeMillis();
        log.debug("start7: 七阶段查询任务执行耗时: {} 毫秒%n", (endTime7 - endTime6));

        //权限处理
        projectSchemeAuthManager.auth(schemeVOS, request.getPower(), request.getProjectId());
        long endTime8 = System.currentTimeMillis();
        log.debug("start8: 八阶段查询任务执行耗时: {} 毫秒%n", (endTime8 - endTime7));

        //设置展示属性
        setAttribute(schemeVOS);
        long endTime9 = System.currentTimeMillis();
        log.debug("start9: 九阶段查询任务执行耗时: {} 毫秒%n", (endTime9 - endTime8));

        //获取置顶项目计划
//        List<ProjectSchemeVO> topList = topSchemeList(schemeVOS);
        long endTime10 = System.currentTimeMillis();
        log.debug("start10: 十阶段查询任务执行耗时: {} 毫秒%n", (endTime10 - endTime9));

        //计划排序
        resultContent = sortContent(schemeVOS);
        long endTime11 = System.currentTimeMillis();
        log.debug("start11: 十一阶段查询任务执行耗时:{} 毫秒%n", (endTime11 - endTime10));
        //合并
//        List<ProjectSchemeVO> result = BeanCopyUtils.convertListTo(topList, ProjectSchemeVO::new);
//        result.addAll(resultContent);

        log.debug("start12: 十二阶段查询任务执行总耗时:{} 毫秒%n", (endTime11 - startTime));
        return resultContent;
    }

    private List<ProjectSchemeVO> topSchemeList(List<ProjectSchemeVO> schemes) {
        List<ProjectSchemeVO> collect = schemes.stream().filter(item -> item.getTopSort() > 0)
                .sorted(Comparator.comparing(ProjectSchemeVO::getTopSort).reversed()
                        .thenComparing(Comparator.comparing(ProjectSchemeVO::getSort).reversed())
                        .thenComparing(Comparator.comparing(ProjectSchemeVO::getSchemeNumber).reversed()))
                .collect(Collectors.toList());
        //深copy
        List<ProjectSchemeVO> result = BeanUtil.copyToList(collect, ProjectSchemeVO.class);
        result.forEach(item -> item.setChildren(CollUtil.toList()));
        return result;
    }

    private List<ProjectSchemeVO> sortContent(List<ProjectSchemeVO> schemeVOS) {
        for(ProjectSchemeVO schemeVO:schemeVOS){
            if(!CollectionUtils.isEmpty(schemeVO.getChildren())){
                List<ProjectSchemeVO> collect = schemeVO.getChildren().stream().sorted(Comparator.comparing(ProjectSchemeVO::getTopSort).reversed()
                        .thenComparing(ProjectSchemeVO::getLevel)
                        .thenComparing(ProjectSchemeVO::getSort)
                )
                        .collect(Collectors.toList());
                schemeVO.setChildren(collect);
            }
        }
        return schemeVOS.stream()
                .filter(item -> Objects.equals(item.getParentId(),"0") || StrUtil.isEmpty(item.getParentId()))
                .sorted(Comparator.comparing(ProjectSchemeVO::getTopSort).reversed()
                        .thenComparing(ProjectSchemeVO::getLevel)
                        .thenComparing(ProjectSchemeVO::getSort)
                )
                .collect(Collectors.toList());
    }


    private void setAttribute(List<ProjectSchemeVO> content) throws Exception {
//        Map<String, List<ProjectSchemePrePost>> preMap = commonManager.initPreMap(content.stream()
//                .map(ProjectSchemeVO::getId)
//                .collect(Collectors.toList()));

//        List<String> approveList = approveListByProjectId(content.get(0).getProjectId());
        long startTime = System.currentTimeMillis();
        Map<String, List<ProjectSchemeVO>> childMap = initChildMap(content);

        List<String> schemeIds = content.stream().map(ProjectSchemeVO::getId).distinct().collect(Collectors.toList());
        Map<String, Integer> approvalStatusMap = applyApprovalService.getApprovalStatus(schemeIds);
        long currentTime = System.currentTimeMillis();
        log.debug("start9: 九阶段查询任务执行耗时第一阶段: {} 毫秒%n", (currentTime - startTime));
        commonManager.codeMapping1(content);
        long currentTime1 = System.currentTimeMillis();
        log.debug("start9: 九阶段查询任务执行耗时第二阶段: {} 毫秒%n", (currentTime1 - currentTime));
        //先根据层级排序
        content.sort(Comparator.comparing(ProjectSchemeVO::getLevel));
        //已经计数的数据
        List<ProjectSchemeVO> countSchemeList = Lists.newArrayList();
        for(ProjectSchemeVO item:content){
            //            item.setSchemePrePostVOList(commonManager.getPreScheme(item.getProjectId(), item.getId()));
//            item.setSchemePostVOList(commonManager.getPostScheme(item.getProjectId(), item.getId()));
//            item.setIsPrePost(!CollectionUtils.isEmpty(preMap.get(item.getId())));
//            item.setSchemeApprove(approveList.contains(item.getId()));
            item.setApproveStatus(approvalStatusMap.get(item.getId()));
            List<ProjectSchemeVO> childList = childMap.getOrDefault(item.getId(), CollUtil.toList())
                    .stream()
                    .sorted(Comparator.comparing(ProjectSchemeVO::getSort))
                    .collect(Collectors.toList());

            item.setChildren(childList);
            //先顶层计数
            if("milestone,taskModule".contains(item.getNodeType()) && item.getLevel() == 1){
                item.setChildrenCount(findChildrenCount(item,content));
                countSchemeList.add(item);
                continue;
            }
            if(CollectionUtil.isNotEmpty(countSchemeList)){
                Map<String, ProjectSchemeVO> map = countSchemeList.stream().collect(Collectors.toMap(ProjectSchemeVO::getId, Function.identity()));
                if(map.get(item.getParentId()) != null){
                    item.setChildrenCount(findChildrenCount(item,content));
                }
                //GC回收
                map.clear();
            }
        }
        //GC回收
        countSchemeList.clear();
    }

    private Integer findChildrenCount(ProjectSchemeVO item, List<ProjectSchemeVO> content) {
        int count = 0;
        for(ProjectSchemeVO vo:content){
            if(vo.getParentChain().contains(item.getId())){
                count++;
            }
        }
        return count;
    }

    private Map<String, List<ProjectSchemeVO>> initChildMap(List<ProjectSchemeVO> schemeVOS) {
        Map<String, List<ProjectSchemeVO>> childMap = new HashMap<>();
        for (ProjectSchemeVO item : schemeVOS) {
//            commonManager.codeMapping(item);
            if (!childMap.containsKey(item.getParentId())) {
                childMap.put(item.getParentId(), CollUtil.toList());
            }
            childMap.get(item.getParentId()).add(item);
        }

        for (String s : childMap.keySet()) {
            List<ProjectSchemeVO> childList = childMap.getOrDefault(s, CollUtil.toList());
            List<ProjectSchemeVO> sortChildList = childList.stream().sorted(Comparator.comparing(ProjectSchemeVO::getSort).reversed()
                    .thenComparing(Comparator.comparing(ProjectSchemeVO::getSchemeNumber).reversed())).collect(Collectors.toList());
            childMap.put(s, sortChildList);
        }

        schemeVOS.forEach(item -> item.setChildren(childMap.getOrDefault(item.getParentId(), CollUtil.toList())));
        return childMap;
    }

    /**
     * 申请调整的项目计划集合
     *
     * @param projectId
     * @return
     */
    private List<String> approveListByProjectId(String projectId) throws Exception {
        List<ProjectSchemeApplyApproval> approvalList = applyApprovalService.list(new LambdaQueryWrapper<>(ProjectSchemeApplyApproval.class)
                .eq(ProjectSchemeApplyApproval::getProjectId, projectId));
        if (CollUtil.isEmpty(approvalList)) {
            return CollUtil.toList();
        }
        return approvalList.stream()
                .filter(Objects::nonNull)
                .filter(item -> Objects.isNull(item.getAgreement()))
                .map(ProjectSchemeApplyApproval::getProjectSchemeId)
                .collect(Collectors.toList());
    }

}
