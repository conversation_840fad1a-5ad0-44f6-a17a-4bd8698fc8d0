package com.chinasie.orion.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/13/9:54
 * @description:
 */
public enum AssessmentLevelEnum {
    pms_index_event("pms_index_event",20,"指标事件"),

    pms_index_event_fo("pms_index_event_fo",20,"指标事件-异物"),

    pms_index_event_is("pms_index_event_is",20,"指标事件-工业安全"),

    pms_index_event_other("pms_index_event_other",20,"指标事件-其他"),

    pms_index_event_rp("pms_index_event_rp",24,"指标事件—辐射安全"),
    pms_class_a_violation("pms_class_a_violation",10,"A类违章"),
    pms_class_b_violation("pms_class_b_violation",5,"B类违章"),
    pms_class_c_violation("pms_class_c_violation",2,"C类违章"),
    pms_f1_defect("pms_f1_defect",10,"F1缺陷"),
    pms_f2_defect("pms_f2_defect",2,"F2缺陷"),

//    pms_little_defect("pms_little_defect",0,""),
    ;

    public void setKey(String key) {
        this.key = key;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public Integer getScore() {
        return score;
    }


    AssessmentLevelEnum(String key, Integer score, String desc) {
        this.key = key;
        this.score = score;
        this.desc = desc;
    }

    private String key;

    private Integer score;

    private String desc;

    public static Map<String,Integer> keyScore(){
        Map<String,Integer> keyToScoreMap = new HashMap<>();
        AssessmentLevelEnum[] values = AssessmentLevelEnum.values();
        for (AssessmentLevelEnum value : values) {
            keyToScoreMap.put(value.getKey(),value.getScore());
        }

        return  keyToScoreMap;
    }


    public static Map<String,String> oldTOKeyDesc(){
        Map<String,String> keyToScoreMap = new HashMap<>();
        keyToScoreMap.put(pms_index_event.getKey(),pms_index_event.getDesc());
        keyToScoreMap.put(pms_class_a_violation.getKey(),pms_class_a_violation.getDesc());
        keyToScoreMap.put(pms_class_b_violation.getKey(),pms_class_b_violation.getDesc());
        keyToScoreMap.put(pms_class_c_violation.getKey(),pms_class_c_violation.getDesc());
        keyToScoreMap.put(pms_f1_defect.getKey(),pms_f1_defect.getDesc());
        keyToScoreMap.put(pms_f2_defect.getKey(),pms_f2_defect.getDesc());
//        keyToScoreMap.put(pms_f1_defect.getKey(),pms_f1_defect.getDesc());
        return  keyToScoreMap;
    }

    public static Map<String,String> keyDesc(){
        Map<String,String> keyToDescMap = new HashMap<>();
        AssessmentLevelEnum[] values = AssessmentLevelEnum.values();
        for (AssessmentLevelEnum value : values) {
            keyToDescMap.put(value.getKey(),value.getDesc());
        }
        return  keyToDescMap;
    }

    //考核结果（结果指标）：指标事件 20分 ，A类违章 ： 10分 ，F1：10分，B类违章：5分
    public static Map<String,Integer> resultType(){
        Map<String,Integer> keyToScoreMap = new HashMap<>();
        keyToScoreMap.put(pms_index_event.getKey(),pms_index_event.getScore());
        keyToScoreMap.put(pms_index_event_fo.getKey(),pms_index_event_fo.getScore());
        keyToScoreMap.put(pms_index_event_is.getKey(),pms_index_event_is.getScore());
        keyToScoreMap.put(pms_index_event_other.getKey(),pms_index_event_other.getScore());
        keyToScoreMap.put(pms_index_event_rp.getKey(),pms_index_event_rp.getScore());
        keyToScoreMap.put(pms_class_a_violation.getKey(),pms_class_a_violation.getScore());
        keyToScoreMap.put(pms_class_b_violation.getKey(),pms_class_b_violation.getScore());
        keyToScoreMap.put(pms_f1_defect.getKey(),pms_f1_defect.getScore());
        return  keyToScoreMap;
    }

    //过程评价（过程指标）：C类违章 2分，F2 ： 2分
    public static Map<String,Integer> processType(){
        Map<String,Integer> keyToScoreMap = new HashMap<>();
        keyToScoreMap.put(pms_class_c_violation.getKey(),pms_class_c_violation.getScore());
        keyToScoreMap.put(pms_f2_defect.getKey(),pms_f2_defect.getScore());
//        keyToScoreMap.put(pms_little_defect.getKey(),pms_little_defect.getScore());

        return  keyToScoreMap;
    }
}
