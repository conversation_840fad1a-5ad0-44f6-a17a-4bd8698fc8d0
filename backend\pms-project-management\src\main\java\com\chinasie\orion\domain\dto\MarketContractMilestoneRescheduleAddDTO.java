package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


@Data
public class MarketContractMilestoneRescheduleAddDTO extends  ObjectDTO   implements Serializable{

    /**
     * 变更原因
     */
    @ApiModelProperty(value = "调整说明")
    private String changeReason;

    /**
     * 里程碑id
     */
    @ApiModelProperty(value = "里程碑id")
    private String milestoneId;

    @ApiModelProperty(value = "跟踪确认单")
    private List<MarketContractMilestoneRescheduleAddMessageDTO> messageList;

    /**
     * 是否需要暂估
     */
    @ApiModelProperty(value = "是否需要暂估")
    private Boolean isProvisionalEstimate;



    /**
     * 计划暂估日期
     */
    @ApiModelProperty(value = "计划暂估日期")
    private Date plannedEstimatedDate;


    /**
     * 上传附件
     */
    @ApiModelProperty(value = "上传附件")
    private List<FileDTO> fileList;




}
