package com.chinasie.orion.domain.vo;

import com.chinasie.orion.util.TreeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;
import java.util.List;

/**
 * ExpenseSubject Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-16 15:15:30
 */
@ApiModel(value = "ExpenseSubjectVO对象", description = "费用科目类")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExpenseSubjectVO extends ObjectVO implements TreeUtils.TreeNode<String, ExpenseSubjectVO> {

    /**
     * 费用科目名称
     */
    @ApiModelProperty(value = "费用科目名称")
    private String name;

    /**
     * 父级目录
     */
    @ApiModelProperty(value = "父级目录")
    private String parentId;

    /**
     * 父级目录
     */
    @ApiModelProperty(value = "父级目录")
    private String parentName;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    /**
     * 排序值
     */
    @ApiModelProperty(value = "排序值")
    private Long sort;

    /**
     * 子项
     */
    @ApiModelProperty(value = "子项")
    private List<ExpenseSubjectVO> children;

}
