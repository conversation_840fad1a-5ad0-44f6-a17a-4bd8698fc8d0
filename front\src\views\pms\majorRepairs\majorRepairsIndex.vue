<script setup lang="ts">
import {
  BasicButton, BasicTableAction, Icon, IOrionTableActionItem, Layout, OrionTable, isPower,
} from 'lyra-component-vue3';
import {
  computed, ComputedRef, h, ref, Ref, watch, provide,
} from 'vue';
import { Modal, RadioButton, RadioGroup } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { openMajorRepairsForm } from './utils';
import Api from '/@/api';
import MajorRepairsTable from './components/MajorRepairsTable.vue';
import { usePagePower } from '/@/views/pms/hooks';

const { powerData, getPowerDataHandle } = usePagePower();
provide('powerData', powerData);
const type: Ref<'list' | 'chart'> = ref();

watch(() => powerData.value, (value) => {
  if (!type.value) {
    if (isPower('PMS_DXGL_container_01', value)) {
      type.value = 'list';
    } else if (isPower('PMS_DXGL_container_02', value)) {
      type.value = 'chart';
    }
  } else if (isPower('PMS_DXGL_container_01', value)) {
    type.value = 'list';
  } else if (isPower('PMS_DXGL_container_02', value)) {
    type.value = 'chart';
  }
}, {
  immediate: true,
  deep: true,
});

const router = useRouter();
const tableRef: Ref = ref();
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const tableOptions = {
  rowSelection: {
    onChange(_keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  showToolButton: false,
  isSpacing: true,
  columns: [
    {
      title: '操作',
      dataIndex: 'actions',
      width: 140,
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
    {
      title: '大修轮次',
      dataIndex: 'repairRound',
      width: 110,
    },
    {
      title: '大修名称',
      dataIndex: 'name',
      minWidth: 240,
      customRender({ text, record }) {
        if (isPower('PMS_DXGL_container_01_02_button_02', record?.rdAuthList)) {
          return h('span', {
            class: 'flex-te action-btn',
            title: text,
            onClick: () => navDetails(record?.id),
          }, text);
        }
        return h('span', {
          class: 'flex-te',
          title: text,
        }, text);
      },
    },
    {
      title: '大修所属基地',
      dataIndex: 'baseName',
      width: 110,
    },
    {
      title: '大修类型',
      dataIndex: 'typeName',
      width: 110,
    },
    {
      title: '计划开始日期',
      dataIndex: 'beginTime',
      width: 110,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '计划结束日期',
      dataIndex: 'endTime',
      width: 110,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '实际开始日期',
      dataIndex: 'actualBeginTime',
      width: 110,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '实际结束日期',
      dataIndex: 'actualEndTime',
      width: 110,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '大修经理',
      dataIndex: 'repairManagerName',
      width: 110,
    },
  ],
  api: (params: Record<string, any>) => new Api('/pms/major-repair-plan').fetch({
    ...params,
    power: {
      containerCode: 'PMS_DXGL_container_01_02',
      pageCode: 'PMSMajorRepairs',
    },
  }, 'page', 'POST'),
};

const toolButtons: ComputedRef<Record<string, any>[]> = computed(() => [
  {
    event: 'add',
    text: '添加计划',
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
    powerCode: 'PMS_DXGL_container_01_01_button_01',
  },
  {
    event: 'batchDelete',
    text: '删除',
    icon: 'sie-icon-shanchu',
    disabled: selectedRows.value.length === 0,
    powerCode: 'PMS_DXGL_container_01_01_button_02',
  },
]);

function toolClick({ event }: Record<string, any>) {
  switch (event) {
    case 'add':
      openMajorRepairsForm(null, updateTable);
      break;
    case 'batchDelete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除已勾选的数据吗？',
        onOk: () => deleteApi(selectedRows.value.map((item) => item.id)),
      });
      break;
  }
}

const actions: IOrionTableActionItem[] = [
  {
    text: '编辑',
    event: 'edit',
    isShow: (record) => isPower('PMS_DXGL_container_01_02_button_01', record?.rdAuthList),
  },
  {
    text: '查看',
    event: 'view',
    isShow: (record) => isPower('PMS_DXGL_container_01_02_button_02', record?.rdAuthList),
  },
  {
    text: '删除',
    event: 'delete',
    isShow: (record) => isPower('PMS_DXGL_container_01_02_button_03', record?.rdAuthList),
  },
];

function actionClick(actionItem: any, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'edit':
      openMajorRepairsForm(record, updateTable);
      break;
    case 'view':
      navDetails(record.id);
      break;
    case 'delete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除这条数据吗？',
        onOk: () => deleteApi([record.id]),
      });
      break;
  }
}

function navDetails(id: string) {
  router.push({
    name: 'MajorRepairsSecondDetail',
    params: {
      id,
    },
  });
}

function updateTable() {
  tableRef.value?.reload();
}

function deleteApi(ids: string[]) {
  return new Promise((resolve) => {
    new Api('/pms/major-repair-plan').fetch(ids, 'remove', 'DELETE').then(() => {
      updateTable();
    }).finally(() => {
      resolve('');
    });
  });
}

</script>

<template>
  <Layout
    v-get-power="{pageCode:'PMSMajorRepairs', getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      v-show="type==='list'"
      ref="tableRef"
      class="radio-button-table"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <template
          v-for="button in toolButtons"
          :key="button.event"
        >
          <BasicButton
            v-is-power="[button.powerCode]"
            v-bind="button"
            @click="toolClick(button)"
          >
            {{ button.text }}
          </BasicButton>
        </template>
        <RadioGroup
          v-if="isPower('PMS_DXGL_container_01',powerData) && isPower('PMS_DXGL_container_02',powerData)"
          v-model:value="type"
          class="mla"
          button-style="solid"
        >
          <RadioButton
            value="list"
          >
            <Icon icon="orion-icon-align-left" />
          </RadioButton>
          <RadioButton
            value="chart"
          >
            <Icon icon="orion-icon-appstore" />
          </RadioButton>
        </RadioGroup>
      </template>
      <template #actions="{record}">
        <BasicTableAction
          :actions="actions"
          :record="record"
          @actionClick="actionClick($event,record)"
        />
      </template>
    </OrionTable>
    <MajorRepairsTable
      v-if="type==='chart'"
      v-model:type="type"
    />
  </Layout>
</template>

<style scoped lang="less">
:deep(.radio-button-table) {
  width: auto;
  flex: 0;

  .ant-input-search {
    width: 215px;
    margin-left: 12px;
  }
}

.mla {
  margin-left: auto;
}
</style>
