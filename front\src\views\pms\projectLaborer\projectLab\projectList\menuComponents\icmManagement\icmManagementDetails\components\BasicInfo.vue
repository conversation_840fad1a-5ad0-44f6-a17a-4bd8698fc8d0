<template>
  <div class="pt10 pl40 pr40">
    <BasicButton
      v-if="state.details?.status===101"
      icon="sie-icon-bianji"
      @click="goEdit"
    >
      编辑
    </BasicButton>
  </div>

  <BasicTitle1
    title="基础信息"
    class="pt10 pl40 pr40"
  />

  <div
    class=" pl50 pr50 pt10"
  >
    <Description
      :bordered="false"
      :colums="3"
      @register="appraisalDesRegister"
    />
  </div>
  <AddOrEditDrawer
    ref="editRef"
    @update="getDetailData()"
  />
</template>
<script lang="ts" setup>
// 接口基础信息
import {
  reactive, computed, ref, inject, watch, onMounted,
} from 'vue';
import {
  Description, useDescription, BasicTitle1, BasicButton,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
import AddOrEditDrawer
  from '../../component/AddOrEditDrawerTable/Drawer.vue';

const editRef = ref(null);
const detailsInfo: any = inject('detailsInfo', {});
const getDetailData: any = inject('getDetailData');
watch(() => detailsInfo.value, () => {
  state.details = detailsInfo.value;
});
const state = reactive({
  loading: false,
  details: {},
});
onMounted(() => {
  state.details = detailsInfo.value;
});
const appraisalSchema = [
  {
    field: 'publishDeptName',
    label: '发布方',
    span: 1,
    render: (data) => data,
  },
  {
    field: 'reviewDeptNames',
    label: '接收方',
    span: 1,
  },
  {
    field: 'typeName',
    label: '接口类型',
    span: 1,
    render: (curVal, data) =>
    // 当前值 curVal  data:所有值
      curVal,
  },
  {
    field: 'interfaceState',
    label: '接口状态',
    span: 1,
  },
  {
    field: 'number',
    label: '编码',
    span: 1,
  },
  {
    field: 'dataStatus',
    label: '最新状态',
    span: 1,
    render: (curVal, data) => data?.dataStatus?.name ?? '未定义',
  },
  {
    field: 'manUserName',
    label: '主办人',
    span: 1,
  },
  {
    field: 'cooperationUserNames',
    label: '协办',
    span: 1,
  },
  {
    field: 'creatorName',
    label: '创建人',
    span: 1,
  },
  {
    field: 'createTime',
    label: '创建时间',
    span: 1,
    render: (curVal, data) => (curVal ? dayjs(curVal).format('YYYY-MM-DD') : '无'),
  },
  {
    field: 'thirdVerifyName',
    label: '第三方审查',
    span: 1,
  },
  {
    field: 'nowRspDeptName',
    label: '当前责任方',
    span: 1,
  },
  {
    field: 'specialtyCode',
    label: '专业代码',
    span: 6,
  },
  {
    field: 'desc',
    label: '接口详细描述',
    span: 6,
  },
  {
    field: 'remark',
    label: '备注',
    span: 6,
  },
];
const [appraisalDesRegister] = useDescription(
  {
    schema: appraisalSchema,
    data: computed(() => state.details),
  },
);

function goEdit() {
  editRef.value.openDrawer({
    action: 'edit',
    info: {
      record: state.details,
      projectId: state.details?.projectId ?? '',
    },
  });
}
</script>
<style lang="less" scoped></style>
