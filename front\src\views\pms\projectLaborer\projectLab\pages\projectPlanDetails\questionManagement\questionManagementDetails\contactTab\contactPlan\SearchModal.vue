<template>
  <BasicDrawer
    v-bind="$attrs"
    title="搜索"
    width="340"
    @register="register"
    @close="cancelClick"
  >
    <div class="mb15">
      <aInputSearch
        v-model:value="keyword"
        placeholder="请输入名称或编号"
        size="large"
        @search.enter="onSubmit"
      />
    </div>
    <basicTitle :title="'筛选属性'">
      <BasicForm
        class="bbj"
        @register="registerForm"
      />
    </basicTitle>
    <div class="buttonGG">
      <a-button
        size="large"
        class="cancelButton"
        @click="cancelClick"
      >
        取消
      </a-button>
      <a-button
        size="large"
        class="okButton"
        type="primary"
        @click="onSubmit"
      >
        确认
      </a-button>
    </div>
  </BasicDrawer>
</template>
<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  inject,
} from 'vue';
import {
  BasicDrawer, useDrawerInner, BasicForm, FormSchema, useForm,
} from 'lyra-component-vue3';
import basicTitle from '/@/views/pms/projectLaborer/componentsList/basicTitle/index.vue';
import { Button, Input } from 'ant-design-vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import { parseURL } from '/@/views/pms/projectLaborer/utils/index';
export default defineComponent({
  name: '',
  components: {
    BasicDrawer,
    BasicForm,
    basicTitle,
    AButton: Button,
    aInputSearch: Input.Search,
  },
  props: {},
  emits: ['searchEmit'],
  setup(_, { emit }) {
    const state: any = reactive({
      keyword: undefined,
      projectId: inject('projectId'),
    });
    function resetData() {
      state.keyword = undefined;
    }
    const schemas: FormSchema[] = [
      {
        field: 'planType',
        component: 'ApiSelect',
        label: '计划类型:',
        componentProps: {
          api: () => {
            const url = `task-subject/getList/${state.projectId}`;
            return new Api('/pms').fetch('', url, 'GET');
          },
          labelField: 'name',
          valueField: 'id',
        },
      },
      {
        field: 'priorityLevel',
        component: 'ApiSelect',
        label: '优先级:',
        componentProps: {
          api: () => new Api('/pms').fetch('', 'plan/priority/list', 'POST'),
          labelField: 'name',
          valueField: 'id',
        },
      },
      {
        field: 'status',
        component: 'ApiSelect',
        label: '状态:',
        componentProps: {
          api: () => new Api('/pmi/data-status/policy?policyId=txf7708f528061104c50a007ca1070c6aeb4').fetch('', '', 'GET'),
          labelField: 'name',
          valueField: 'statusValue',
        },
      },
      {
        field: 'planStartTime',
        component: 'RangePicker',
        label: '开始时间:',
        componentProps: {
          onChange(params) {
            if (params.length === 0) return;
            const data1 = dayjs(params[0].$d).format('YYYY-MM-DD 00:00:00');
            const data2 = dayjs(params[1].$d).format('YYYY-MM-DD 23:59:59');
            F1.setFieldsValue({
              planStartTime: [data1, data2],
            });
          },
        },
      },
      {
        field: 'planEndTime',
        component: 'RangePicker',
        label: '结束时间:',
        componentProps: {
          onChange(params) {
            if (params.length === 0) return;
            const data1 = dayjs(params[0].$d).format('YYYY-MM-DD 00:00:00');
            const data2 = dayjs(params[1].$d).format('YYYY-MM-DD 23:59:59');
            F1.setFieldsValue({
              planEndTime: [data1, data2],
            });
          },
        },
      },
    ];
    const [registerForm, F1] = useForm({
      // layout: 'vertical',
      baseColProps: {
        span: 24,
      },
      size: 'large',
      schemas,
      showSubmitButton: false,
      showResetButton: false,
    });
    const [register, DrawerM] = useDrawerInner((data) => {

    });
    // onMounted(() => {
    // const fd = F1.getFieldsValue();
    // })
    function onSubmit() {
      const fd = F1.getFieldsValue();
      // console.log('----- fd -----', fd);
      const clone = JSON.parse(JSON.stringify(fd));
      if (fd?.planStartTime) {
        clone.planStartTime = fd.planStartTime.map((item) => dayjs(item).unix() * 1000);
      }
      if (fd?.planEndTime) {
        clone.planEndTime = fd.planEndTime.map((item) => dayjs(item).unix() * 1000);
      }
      if (state.keyword) {
        clone.keyword = state.keyword;
      }
      // console.log('----- clone -----', clone);
      emit('searchEmit', clone);
      cancelClick();
    }
    function cancelClick() {
      F1.resetFields();
      resetData();
      DrawerM.closeDrawer();
    }
    return {
      ...toRefs(state),
      register,
      registerForm,
      onSubmit,
      cancelClick,
    };
  },
});
</script>
<style lang="less" scoped>
.buttonGG {
  position: fixed;
  bottom: 0px;
  padding: 20px 0;
  text-align: center;
  width: 280px;
  height: 80px;
  background: #ffffff;
  margin-bottom: 0px;
}
.cancelButton {
  color: ~`getPrefixVar('primary-color')`;
  background: #5172dc19;
  width: 110px;
  border-radius: 4px;
}
.okButton {
  width: 110px;
  margin-left: 15px;
  border-radius: 4px;
}

  :deep(.bbj){
    &>.ant-row{
      .ant-col{
        .ant-form-item{
            display: block !important;
        }
      }
    }

}
</style>
