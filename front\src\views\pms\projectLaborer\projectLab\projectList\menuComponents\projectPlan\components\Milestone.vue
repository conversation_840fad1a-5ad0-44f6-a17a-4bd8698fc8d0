<template>
  <div
    class="milestone-content"
    :style="{paddingTop: milestoneData.length>0 ? 138+'px' : ''}"
  >
    <a-steps
      :current="0"
      :progressDot="true"
    >
      <a-step
        v-for="val in milestoneData"
        :key="val.title"
      >
        <template #title>
          <div class="title-step">
            <a-tag
              :color="statusMap[val.dataStatus.statusValue]?.type?statusMap[val.dataStatus.statusValue]?.type:''"
              :title="val.name"
            >
              <a-tooltip>
                <template #title>
                  {{ val.name }}
                </template>
                {{ processedName(val.name) }}
              </a-tooltip>
            </a-tag>
          </div>
        </template>
        <template #description>
          <div class="des-step">
            <div>状态：{{ val.dataStatus.name }}</div>
            <div>{{ val.endTime }}</div>
          </div>
        </template>
      </a-step>
    </a-steps>
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      :dataSource="tableSource"
      :expandIconColumnIndex="0"
    >
      <template #name="{ record }">
        <div
          class="action-btn flex-te"
          :title="record.name"
          @click="handleToDetail(record)"
        >
          {{ record.name }}
        </div>
      </template>
      <template #level="{ record }">
        {{ record['level'] }}级
      </template>
    </OrionTable>
  </div>
</template>
<script lang="ts">
import {
  defineComponent, ref, h, onMounted,
} from 'vue';
import {
  Steps, Step, Popover, Tag,
  Tooltip,
} from 'ant-design-vue';
import { OrionTable, DataStatusTag } from 'lyra-component-vue3';
import dayjs from 'dayjs';
import { useRoute, useRouter } from 'vue-router';
import { postProjectPlanPages } from '/@/views/pms/projectLaborer/projectLab/api';
import Api from '/@/api';
import { statusColor } from '/@/views/pms/projectLaborer/projectLab/enums';

export default defineComponent({
  name: 'Milestone',
  components: {
    ASteps: Steps,
    AStep: Step,
    // APopover: Popover,
    ATag: Tag,
    OrionTable,
  },
  props: {
    projectId: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const route = useRoute();
    const router = useRouter();
    const statusMap = {
      111: {
        label: '已完成',
        type: 'success',
      },
      130: {
        label: '已发布',
        type: 'processing',
      },
      101: {
        label: '未发布',
        type: 'warning',
      },
    };
    function processedName(name) {
      return name.length > 20 ? `${name.slice(0, 20)}...` : name;
    }
    const typeMap = {
      100001: '计划',
      100002: '里程碑',
    };
    const situationMap = {
      100011: '正常',
      100012: '已临期',
      100013: '已逾期',
    };
    const levelMap = {
      100021: '1级',
      100022: '2级',
      100023: '3级',
      100024: '4级',
      100025: '5级',
      100026: '6级',
      100027: '7级',
      100028: '8级',
      100029: '9级',
      100030: '10级',
    };

    const milestoneData = ref([]);
    const selectedRowKeys = ref([]);

    const tableSource = ref([]);
    const tableOptions = {
      rowSelection: {
        selectedRowKeys,

        onChange: (keys = [], selectedRows) => {
          selectedRowKeys.value = keys;
        },
      },
      isTreeTable: false,
      showSmallSearch: false,
      showIndexColumn: false,
      showToolButton: false,
      pagination: false,
      columns: [
        {
          title: '计划编号',
          dataIndex: 'schemeNumber',
          key: 'schemeNumber',
          width: 100,
        },
        {
          title: '计划名称',
          dataIndex: 'name',
          align: 'left',
          minWidth: 300,
          slots: { customRender: 'name' },
        },
        {
          title: '层级',
          dataIndex: 'level',
          width: 100,
          slots: { customRender: 'level' },
        },

        {
          title: '计划类型',
          align: 'left',
          dataIndex: 'nodeTypeName',
          width: 120,
          customRender({ text }) {
            return text === 'milestone' ? '里程碑节点' : '计划';
          },
        },
        {
          title: '责任处室',
          align: 'left',
          dataIndex: 'rspSubDeptName',
          width: 120,
        },
        {
          title: '责任人',
          align: 'left',
          dataIndex: 'rspUserName',
          width: 120,
        },
        {
          title: '计划状态',
          dataIndex: 'dataStatus',
          align: 'left',
          width: 100,
          customRender({ record }) {
            return record.dataStatus
              ? h(DataStatusTag, {
                statusData: record.dataStatus,
              })
              : '';
          },
        },
        {
          title: '计划开始日期',
          align: 'left',
          dataIndex: 'beginTime',
          width: 120,
          customRender({ text }) {
            return text ? dayjs(text).format('YYYY-MM-DD') : '';
          },
        },
        {
          title: '计划结束日期',
          align: 'left',
          dataIndex: 'endTime',
          width: 120,
          customRender({ text }) {
            return text ? dayjs(text).format('YYYY-MM-DD') : '';
          },
        },
        {
          title: '情况',
          align: 'left',
          dataIndex: 'circumstance',
          width: 120,
          customRender({ record }) {
            return h(Tag, { color: statusColor[record.circumstance] }, `${record?.approveStatus === 0 ? '调整申请中' : record?.approveStatus === 1 ? '变更申请中' : (record?.circumstanceName ?? '')}`);
          },
        },

        {
          title: '实际开始日期',
          align: 'left',
          dataIndex: 'actualBeginTime',
          width: 120,
          customRender({ text }) {
            return text ? dayjs(text).format('YYYY-MM-DD') : '';
          },
        },
        {
          title: '实际结束日期',
          align: 'left',
          dataIndex: 'actualEndTime',
          width: 120,
          customRender({ text }) {
            return text ? dayjs(text).format('YYYY-MM-DD') : '';
          },
        },
        {
          title: '创建人',
          align: 'left',
          dataIndex: 'creatorName',
          width: 120,
        },
        {
          title: '计划下发时间',
          dataIndex: 'issueTime',
          width: 120,
          customRender({ text }) {
            // debugger;
            return text ? dayjs(text).format('YYYY-MM-DD') : '';
          },
        },
      ],
    };

    const getPlanList = async () => {
      const res = await new Api(
        `/pms/projectScheme/${props.projectId}/milestone`,
      ).fetch('', '', 'POST');
      if (res) {
        // debugger;
        tableSource.value = res;
        milestoneData.value = res;
      }
    };
    const handleToDetail = (row) => {
      router.push({
        name: 'ProPlanDetails',
        params: { id: row.id },
      });
    };
    onMounted(() => {
      getPlanList();
    });
    return {
      milestoneData,
      statusMap,
      tableSource,
      tableOptions,
      getPlanList,
      handleToDetail,
      processedName,
    };
  },
});
</script>
<style lang="less" scoped>
.milestone-content {
  padding-top: 70px;
  height: calc(100vh - 150px);
  width: 100%;
  .title-step {
    position: relative;
    .ant-tag {
      max-width: 140px;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-right: 0;
      position: absolute;
      left: 0;
      top: -80px;
      transform: translate(-50%, 0);
      padding: 4px 8px;
      border-radius: 15px;
    }
  }
  .des-step {
    margin-top: 25px;
  }
}
.milestone-content.pd-top{
  padding-top: 170px;
}
.table {
  height: 400px;
  overflow-y: auto;
  overflow-x: auto;
}
</style>
