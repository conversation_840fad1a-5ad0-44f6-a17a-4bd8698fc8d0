package com.chinasie.orion.service.impl;





import com.chinasie.orion.domain.entity.RelatedTransactionForm;
import com.chinasie.orion.domain.dto.RelatedTransactionFormDTO;
import com.chinasie.orion.domain.vo.RelatedTransactionFormVO;



import com.chinasie.orion.service.RelatedTransactionFormService;
import com.chinasie.orion.repository.RelatedTransactionFormMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;




/**
 * <p>
 * RelatedTransactionForm 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-31 14:07:31
 */
@Service
@Slf4j
public class RelatedTransactionFormServiceImpl extends OrionBaseServiceImpl<RelatedTransactionFormMapper, RelatedTransactionForm> implements RelatedTransactionFormService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  RelatedTransactionFormVO detail(String id,String pageCode) throws Exception {
        RelatedTransactionForm relatedTransactionForm =this.getById(id);
        RelatedTransactionFormVO result = BeanCopyUtils.convertTo(relatedTransactionForm,RelatedTransactionFormVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param relatedTransactionFormDTO
     */
    @Override
    public  String create(RelatedTransactionFormDTO relatedTransactionFormDTO) throws Exception {
        RelatedTransactionForm relatedTransactionForm =BeanCopyUtils.convertTo(relatedTransactionFormDTO,RelatedTransactionForm::new);
        this.save(relatedTransactionForm);

        String rsp=relatedTransactionForm.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param relatedTransactionFormDTO
     */
    @Override
    public Boolean edit(RelatedTransactionFormDTO relatedTransactionFormDTO) throws Exception {
        RelatedTransactionForm relatedTransactionForm =BeanCopyUtils.convertTo(relatedTransactionFormDTO,RelatedTransactionForm::new);

        this.updateById(relatedTransactionForm);

        String rsp=relatedTransactionForm.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        for (String id : ids) {




        }
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<RelatedTransactionFormVO> pages( Page<RelatedTransactionFormDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<RelatedTransactionForm> condition = new LambdaQueryWrapperX<>( RelatedTransactionForm. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(RelatedTransactionForm::getCreateTime);


        Page<RelatedTransactionForm> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), RelatedTransactionForm::new));

        PageResult<RelatedTransactionForm> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<RelatedTransactionFormVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<RelatedTransactionFormVO> vos = BeanCopyUtils.convertListTo(page.getContent(), RelatedTransactionFormVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "关联交易表单导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", RelatedTransactionFormDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        RelatedTransactionFormExcelListener excelReadListener = new RelatedTransactionFormExcelListener();
        EasyExcel.read(inputStream,RelatedTransactionFormDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<RelatedTransactionFormDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("关联交易表单导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<RelatedTransactionForm> relatedTransactionFormes =BeanCopyUtils.convertListTo(dtoS,RelatedTransactionForm::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::RelatedTransactionForm-import::id", importId, relatedTransactionFormes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<RelatedTransactionForm> relatedTransactionFormes = (List<RelatedTransactionForm>) orionJ2CacheService.get("pmsx::RelatedTransactionForm-import::id", importId);
        log.info("关联交易表单导入的入库数据={}", JSONUtil.toJsonStr(relatedTransactionFormes));

        this.saveBatch(relatedTransactionFormes);
        orionJ2CacheService.delete("pmsx::RelatedTransactionForm-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::RelatedTransactionForm-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<RelatedTransactionForm> condition = new LambdaQueryWrapperX<>( RelatedTransactionForm. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(RelatedTransactionForm::getCreateTime);
        List<RelatedTransactionForm> relatedTransactionFormes =   this.list(condition);

        List<RelatedTransactionFormDTO> dtos = BeanCopyUtils.convertListTo(relatedTransactionFormes, RelatedTransactionFormDTO::new);

        String fileName = "关联交易表单数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", RelatedTransactionFormDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<RelatedTransactionFormVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class RelatedTransactionFormExcelListener extends AnalysisEventListener<RelatedTransactionFormDTO> {

        private final List<RelatedTransactionFormDTO> data = new ArrayList<>();

        @Override
        public void invoke(RelatedTransactionFormDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<RelatedTransactionFormDTO> getData() {
            return data;
        }
    }


}
