<template>
  <BasicForm
    @register="register"
  >
    <template #add="{ field }">
      <Button
        class="mr10"
        @click="add"
      >
        +
      </Button>
      <Button
        :disabled="field==0"
        @click="del(field)"
      >
        -
      </Button>
    </template>
  </BasicForm>
</template>

<script setup lang="ts">
import {
  defineExpose, h, reactive, ref,
} from 'vue';
import { Button, Select } from 'ant-design-vue';
import { BasicForm, openTreeSelectModal, useForm } from 'lyra-component-vue3';

const AButton = Button;
const props = defineProps({
  isRequire: {
    type: Boolean,
    default: false,
  },
});

const state = reactive({
  optionsList: {
    relationship_0: [
      {
        label: '',
        value: '',
      },
    ],
  },
});
const currentClickN = ref('0');
const [
  register,
  {
    appendSchemaByField, removeSchemaByFiled, validate, getFieldsValue, setFieldsValue, resetFields,
  },
] = useForm({
  schemas: [
    // {
    //   field: '汇报总结',
    //   component: 'InputTextArea',
    //   label: '汇报总结',
    //   colProps: {
    //     span: 24,
    //   },
    //   ifShow: !props.isRequire,
    //   componentProps: {
    //     row: 4,
    //   },
    // },
    {
      field: 'content_0',
      component: 'InputTextArea',
      label: props.isRequire ? '工作内容' : '明日计划',
      colProps: {
        span: 9,
      },
      required: props.isRequire,
      componentProps: () => ({
        style: { height: '32px' },
        maxlength: 200,
      }),
    },
    {
      field: 'taskTime_0',
      component: 'InputNumber',
      label: '工时',
      componentProps: {
        max: 24,
        min: 0,
      },
      colProps: {
        span: 3,
      },
      required: props.isRequire,
    },
    {
      field: 'thePlan_0',
      component: 'Select',
      label: '是否计划内',
      colProps: {
        span: 3,
      },
      required: props.isRequire,
      componentProps: () => ({
        options:
                    [
                      {
                        label: '计划内',
                        value: '0',
                      },
                      {
                        label: '计划外',
                        value: '-1',
                      },
                    ],
      }),
    },
    {
      field: 'relationship_0',
      component: 'Input',
      label: '关联对象',
      colProps: {
        span: 6,
      },
      required: props.isRequire,
      componentProps: () => ({
        open: false,
        showArrow: false,
      }),
      render: ({ model, field }) => h('div', {
        style: {
          width: '100%',
          height: '32px',
        },
      }, [
        h(Select, {
          style: {
            display: 'inline-block',
            width: '70%',
          },
          options: state.optionsList[field],
          value: state.optionsList[field][0].value,
          open: false,
          showArrow: false,
        }),
        h(Button, {
          style: { display: 'inline-block' },
          onClick: () => {
            currentClickN.value = field;
            openTreeSelectModal({
              onOk: selectedOk,
            });
          },
        }, '选择'),
      ]),
    },
    {
      field: '0',
      component: 'Input',
      label: ' ',
      colProps: {
        span: 3,
      },
      slot: 'add',
    },
  ],
  // labelWidth: 100,
  layout: 'vertical',
  actionColOptions: { span: 24 },
});

// 动态表单当前的行数
const n = ref(1);

// 动态表单追加一项
function add() {
  state.optionsList[`relationship_${n.value}`] = [
    {
      label: '',
      value: '',
    },
  ];
  appendSchemaByField(
    {
      field: `content_${n.value}`,
      component: 'InputTextArea',
      label: '',
      colProps: {
        span: 9,
      },
      required: props.isRequire,
      componentProps: () => ({
        style: { height: '32px' },
        maxlength: 200,

      }),
    },
    '',
  );
  appendSchemaByField(
    {
      field: `taskTime_${n.value}`,
      component: 'InputNumber',
      label: '',
      colProps: {
        span: 3,
      },
      required: props.isRequire,
      componentProps: {
        max: 24,
        min: 0,
      },
    },
    '',
  );
  appendSchemaByField(
    {
      field: `thePlan_${n.value}`,
      component: 'Select',
      label: '',
      colProps: {
        span: 3,
      },
      required: props.isRequire,
      componentProps: () => ({
        options:
                    [
                      {
                        label: '计划内',
                        value: '0',
                      },
                      {
                        label: '计划外',
                        value: '-1',
                      },
                    ],
      }),
    },

    '',
  );
  appendSchemaByField(
    {
      field: `relationship_${n.value}`,
      component: 'Input',
      label: '',
      colProps: {
        span: 6,
      },
      required: props.isRequire,
      componentProps: () => ({
        open: false,
        showArrow: false,
      }),
      render: ({ field }) => h('div', {
        style: {
          width: '100%',
          height: '32px',
        },
      }, [
        h(Select, {
          style: {
            display: 'inline-block',
            width: '70%',
          },
          options: state.optionsList[`${field}`],
          value: state.optionsList[`${field}`]?.length ? state.optionsList[`${field}`][0].value : null,
          open: false,
          showArrow: false,
        }),
        h(Button, {
          style: { display: 'inline-block' },
          onClick: () => {
            currentClickN.value = field;
            openTreeSelectModal({
              onOk: selectedOk,
            });
          },
        }, '选择'),
      ]),
    },
    '',
  );

  appendSchemaByField(
    {
      field: `${n.value}`,
      component: 'Input',
      label: '',
      colProps: {
        span: 3,
      },
      slot: 'add',
    },
    '',
  );
  n.value++;
}

// 删除一项
function del(field) {
  if (field !== '0') {
    removeSchemaByFiled([
      `content_${field}`,
      `taskTime_${field}`,
      `thePlan_${field}`,
      `relationship_${field}`,
      `${field}`,
    ]);
    delete state.optionsList[`relationship_${field}`];
    n.value--;
  }
}

// 关联对象回调哦
function selectedOk(data) {
  if (data.tableData?.length) {
    const { innerName, id } = data.associatedData;
    state.optionsList[currentClickN.value][0].label = innerName;
    state.optionsList[currentClickN.value][0].value = id;
    let obj = {};
    obj[currentClickN.value] = id;
    setFieldsValue(obj);
  }
}

// 回显动态表单,只有需要在回显的时候调用...
function showThisAsyncItem(dataList) {
  if (dataList?.length) {
    dataList.forEach((item, index) => {
      if (index === 0) {
        state.optionsList.relationship_0[0].label = item.relationshipName;
        state.optionsList.relationship_0[0].value = item.relationship;
        setFieldsValue({
          content_0: item.content,
          taskTime_0: item.taskTime,
          thePlan_0: `${item.thePlan}`,
          relationship_0: item.relationship,
        });
      } else {
        add();
        let obj = {};
        obj[`content_${n.value - 1}`] = item.content;
        obj[`taskTime_${n.value - 1}`] = item.taskTime;
        obj[`thePlan_${n.value - 1}`] = `${item.thePlan}`;
        obj[`relationship_${n.value - 1}`] = item.relationship;
        state.optionsList[`relationship_${n.value - 1}`] = [
          {
            label: item.relationshipName,
            value: item.relationship,
          },
        ];
        setFieldsValue(obj);
      }
    });
  }
}

defineExpose({
  getFieldsValue,
  validate,
  state,
  resetFields,
  showThisAsyncItem,
});
</script>

<style scoped lang="less">
:deep(.ant-basic-form) {
  padding: 0 !important;

}

</style>
