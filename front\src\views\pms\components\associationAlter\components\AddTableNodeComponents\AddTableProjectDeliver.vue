<script setup lang="ts">
import {
  BasicButton, BasicForm, OrionTable, useForm,
} from 'lyra-component-vue3';
import {
  computed, reactive, Ref, ref,
} from 'vue';

import { Input as AInput, Modal } from 'ant-design-vue';
import { deliverIdsTableColumns, simpleProjectTableColumns } from '../../tableColumns.js';

import Api from '/@/api';

import { AddSelectTableModal } from '../AddSelectTableModal/index';

const props = defineProps<{
  type: string | undefined
}>();

const loading: Ref<boolean> = ref(false);

const deliverIdsTableRef = ref();
const deliverIdsTableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  showSmallSearch: false,
  pagination: false,
  columns: [
    ...deliverIdsTableColumns,
    {
      title: '操作',
      dataIndex: 'action',
      width: 150,
      align: 'left',
      fixed: 'right',
      slots: { customRender: 'action' },
    },
  ],
  actions: [
    {
      text: '移除',
      onClick(record) {
        Modal.confirm({
          title: '是否移除当前数据？',
          onOk() {
            removeTableDataByRef(deliverIdsTableRef, record);
          },
        });
      },
    },
  ],
});
function removeTableDataByRef(tableRef, record) {
  let tableData = tableRef?.value?.getDataSource();
  tableData = tableData.filter((item) => item.id !== record.id);
  tableRef.value.setTableData(tableData);
}
const commonProjectInfo = reactive({
  projectName: '',
  projectId: '',
  oldStatus: '',
  oldStatusName: '',

});

const projectDeliverPrams = reactive({
  projectName: computed(() => commonProjectInfo.projectName),
  projectId: computed(() => commonProjectInfo.projectId),
  oldStatus: computed(() => commonProjectInfo.oldStatus),
  oldStatusName: computed(() => commonProjectInfo.oldStatusName),
  deliverIds: [],
});

function SelectSimpleProjectClick() {
  AddSelectTableModal({
    title: '项目列表',
    width: '80%',
    selectType: 'radio',
    selectedData: [
      {
        id: commonProjectInfo.projectId,
        name: commonProjectInfo.projectName,
      },
    ],
    columns: simpleProjectTableColumns,
    tableApi(option) {
      const params: Record<string, any> = {
        ...option,
        query: {

        },
      };
      delete params.node;
      delete params.tableMethods;
      delete params.orders;
      return new Api('/pms/project/getSimplePage').fetch(params, '', 'POST');
    },
    async onOk({ tableData }) {
      const obj = tableData[0];
      if (commonProjectInfo.projectId && obj.id !== commonProjectInfo.projectId) {
        deliverIdsTableRef.value.setTableData([]);
      }
      commonProjectInfo.projectName = obj.name;
      commonProjectInfo.projectId = obj.id;
      commonProjectInfo.oldStatusName = obj.dataStatus.name;
      commonProjectInfo.oldStatus = obj.dataStatus.statusValue;

      setFieldsValue({
        projectName: obj.name,
        projectId: obj.id,
        oldManagerName: obj.pm,
        oldManager: obj.pmId,
        oldStatusName: obj.dataStatus.name,
        oldStatus: obj.dataStatus.statusValue,
      });
    },
  });
}
function deliverIdsAdd() {
  AddSelectTableModal(
    {
      title: '选择交付物',
      width: '80%',
      selectType: 'checkbox',
      selectedData: deliverIdsTableRef.value.getDataSource(),
      columns: deliverIdsTableColumns,
      tableApi(option) {
        const params: Record<string, any> = {
          ...option,
          query: {
            projectId: commonProjectInfo.projectId,
          },
        };
        delete params.tableMethods;
        delete params.node;
        return new Api('/pms/deliverable/page').fetch(params, '', 'POST');
      },
      onOk({ tableData }) {
        projectDeliverPrams.deliverIds = tableData.map((obj) => (obj.id));
        deliverIdsTableRef.value.setTableData(tableData);
      },

    },
  );
}
// 生成一个排序算法

const [
  register,
  {
    validate, updateSchema, setFieldsValue, getFieldsValue,
  },
] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas: [
    {
      field: 'projectName',
      component: 'Input',
      slot: 'SelectSimpleProject',
      label: '选择项目',
      rules: [{ required: true }],
    },
    {
      field: 'deliverIds',
      slot: 'deliverIds',
      component: 'Input',
      label: '原交付物',
      colProps: {
        span: 24,
      },
    },

  ],
});
defineExpose({
  async getFormData() {
    const formValues = await validate();
    if (formValues && props.type === 'project_deliver') {
      return projectDeliverPrams;
    }
    return null;
  },
  async setFormData(record, detailData = null) {
    await setFieldsValue({ ...record });
    commonProjectInfo.projectName = record.projectName;
    commonProjectInfo.projectId = record.projectId;
    commonProjectInfo.oldStatus = record.oldStatus;
    commonProjectInfo.oldStatusName = record.oldStatusName;
    if (props.type === 'project_deliver') {
      deliverIdsTableRef.value.setTableData(detailData.deliverableVOS);
    }
  },
});
const tableRef = ref();

</script>

<template>
  <div>
    <BasicForm
      v-if="type!==''"
      :key="type"
      @register="register"
    >
      <template #SelectSimpleProject="{ model, field }">
        <AInput
          v-model:value="model[field]"
          style="width: 100%"
          @click="SelectSimpleProjectClick"
        />
      </template>

      <template #deliverIds>
        <OrionTable
          ref="deliverIdsTableRef"
          class="min-table"
          :options="deliverIdsTableOptions"
        >
          <template #toolbarLeft>
            <BasicButton
              type="primary"
              icon="sie-icon-tianjiaxinzeng"
              @click="deliverIdsAdd"
            >
              添加
            </BasicButton>
            <!--                <BasicButton-->
            <!--                  icon="sie-icon-shanchu"-->
            <!--                  @click="btnClick('delete');"-->
            <!--                >-->
            <!--                  移除-->
            <!--                </BasicButton>-->
          </template>
        </OrionTable>
      </template>
    </BasicForm>
  </div>
</template>

<style scoped lang="less">
.edit-btn{
  color: red;
}
.min-table{
  min-height: 300px;
}
</style>
