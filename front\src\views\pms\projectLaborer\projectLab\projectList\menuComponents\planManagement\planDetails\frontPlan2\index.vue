<template>
  <Layout class="ui-2-0">
    <BasicTable
      ref="tableRef"
      :columns="columns"
      :data-source="dataSource"
      :show-index-column="false"
      :pagination="false"
      row-key="id"
      :resize-height-offset="10"
      :loading="loading"
      :row-selection="pageType==='page'?{ type: 'checkbox' }:false"
    />
  </Layout>
  <NewButtonModal
    v-if="pageType==='page'"
    :btn-object-data="btnConfig"
    @clickType="clickType"
  />
  <AddMsg
    v-if="addMsg.visible"
    :data="addMsg"
    @submit="getPage"
  />
  <ViewDetails
    v-if="viewDetails.visible"
    :data="viewDetails"
  />
  <SearchAll
    v-if="searchAll.visible"
    :data="searchAll"
    @submit="submitSearch"
  />
  <SearchModal
    v-if="pageType==='page'"
    @register="searchRegister"
    @searchEmit="searchEmit"
  />
</template>

<script>
import {
  Layout, BasicTable, isPower, useDrawer,
} from 'lyra-component-vue3';
import { message, Modal } from 'ant-design-vue';
import NewButtonModal from '/@/views/pms/projectLaborer/componentsList/buttonModal/newButtonModal.vue';
import {
  onMounted, reactive, toRefs, ref, inject, computed, h,
} from 'vue';
import Api from '/@/api';
import AddMsg from './components/AddMsg.vue';
import ViewDetails from './components/ViewDetails.vue';
import SearchAll from './components/SearchAll.vue';
import SearchModal from './components/SearchModal.vue';
export default {
  name: 'Index',
  components: {
    SearchAll,
    NewButtonModal,
    Layout,
    BasicTable,
    AddMsg,
    ViewDetails,
    SearchModal,
  },
  props: {
    pageType: {
      type: String,
      default: 'page',
    },
    formId: {
      type: String,
      default: '',
    },
  },
  emits: ['checkDetails'],
  setup(props, { emit }) {
    const [searchRegister, { openDrawer: openSearchDrawer }] = useDrawer();
    const state = reactive({
      tableRef: ref(),
      formDel: {
        idList: [],
        sourceId: props.formId,
        type: 1,
      },
      addMsg: {},
      searchAll: {},
      viewDetails: {},
      columns: [
        {
          title: '姓名',
          dataIndex: 'name',
          align: 'left',
          minWidth: 200,
          customRender({ record, text }) {
            return h(
              'span',
              {
                class: computed(() => (isPower('LCB_container_button_03', state.powerData) ? 'action-btn' : '')).value,
                title: text,
                onClick(e) {
                  if (isPower('XMX_list_button_03', state.powerData)) {
                    handleCheck(record.id);
                  }
                  e.stopPropagation();
                },
              },
              text,
            );
          },

        },
        {
          title: '编号',
          dataIndex: 'number',
          align: 'left',
          minWidth: 200,
        },
        {
          title: '依赖关系',
          dataIndex: 'typeName',
          width: 150,
        },
        {
          title: '计划类型',
          dataIndex: 'planTypeName',
          width: 150,
        },
        {
          title: '负责人',
          dataIndex: 'principalName',
          width: 150,
        },
        {
          title: '计划进度',
          dataIndex: 'scheduleName',
          width: 100,
        },
        {
          title: '优先级',
          dataIndex: 'priorityLevelName',
          width: 150,
        },
        {
          title: '状态',
          dataIndex: 'statusName',
          width: 100,
        },
        {
          title: '开始日期',
          dataIndex: 'planPredictStartTime',
          width: 150,
        },
        {
          title: '结束日期',
          dataIndex: 'planPredictEndTime',
          width: 150,
        },
      ],
      dataSource: [],
      loading: false,
      form: {
        keyWord: undefined,
        planId: props.formId,
        type: 1,
      },

      powerData: [],
    });
    state.powerData = inject('powerData');
    const state6 = reactive({
      btnConfig: {
        check: { show: computed(() => isPower('LCB_container_button_03', state.powerData)) },
        open: { show: computed(() => isPower('LCB_container_button_04', state.powerData)) },
        add: { show: computed(() => isPower('LCB_container_button_05', state.powerData)) },
        remove: { show: computed(() => isPower('LCB_container_button_06', state.powerData)) },
        search: { show: computed(() => isPower('LCB_container_button_07', state.powerData)) },
      },
    });
    function getPage() {
      state.loading = true;
      const love = {
        className: 'Plan',
        moduleName: '项目管理-计划管理-项目计划-前置计划', // 模块名称
        type: 'GET', // 操作类型
        remark: '获取/搜索了【前置计划列表】',
      };
      new Api('/pms', love)
        .fetch(state.form, 'before-after-to-plan/list', 'POST')
        .then((res) => {
          state.dataSource = res;
          state.loading = false;
          state.tableRef.clearSelectedRowKeys();
          state.form = {
            planId: props.formId,
            type: 1,
          };
        })
        .catch(() => {
          state.loading = false;
          state.form = {
            planId: props.formId,
            type: 1,
          };
        });
    }

    function isSelectCheck(type) {
      const selectedRowKeys = state.tableRef.getSelectRowKeys();
      if (selectedRowKeys.length === 0) {
        message.warning('请选择数据进行操作');
        return false;
      }
      if (selectedRowKeys.length === 1) {
        return selectedRowKeys[0];
      }
      if (selectedRowKeys.length > 1) {
        if (type === 'remove') {
          return true;
        }
        message.warning('请选择一条数据进行操作');
        return false;
      }
    }
    function handleCheck(id) {
      emit('checkDetails', id);
      return;
      const love = {
        id,
        className: 'Plan',
        moduleName: '项目管理-计划管理-项目计划-前置计划', // 模块名称
        type: 'GET', // 操作类型
        remark: `查看了【${id}】`,
      };
      new Api('/pms', love).fetch('', `plan/${id}`, 'GET').then((res) => {
        state.viewDetails = {
          visible: true,
          title: '查看信息',
          form: res,
        };
      });
    }
    function clickType(type) {
      if (type === 'open') {
        const id = isSelectCheck();
        if (id) {
          const projectId = props.formId;
          const url = `pms/planManagement/planDetails?id=${id}&projectId=${projectId}`;
          window.open(`/${url}`);
        }
      }
      if (type === 'search') {
        openSearchDrawer(true);
        // state.searchAll = {
        //   visible: true,
        //   form: {
        //     keyWord: state.form.keyWord,
        //   },
        // };
      }
      if (type === 'check') {
        const id = isSelectCheck();
        id && handleCheck(id);
      }
      if (type === 'remove') {
        const id = isSelectCheck(type);
        if (id) {
          Modal.confirm({
            title: '确认提示',
            content: '请确认是否移除此选中任务？',
            onOk() {
              isConfirm();
            },
          });
        }
      }
      if (type === 'add') {
        state.addMsg = {
          visible: true,
          form: {
            fromIdList: [],
            toId: props.formId,
            type: undefined,
          },
        };
      }
    }
    function searchEmit(data) {
      // console.log("----- data -----", data)
      state.form = {
        ...state.form,
        ...data,
      };
      getPage();
    }
    function isConfirm() {
      const love = {
        className: 'Plan',
        moduleName: '项目管理-计划管理-项目计划-前置计划',
        type: 'DELETE',
        remark: `删除了【${state.tableRef.getSelectRowKeys()}】`,
      };
      new Api('/pms', love)
        .fetch(
          {
            ...state.formDel,
            idList: state.tableRef.getSelectRowKeys(),
          },
          'before-after-to-plan/batch',
          'DELETE',
        )
        .then(() => {
          message.success('操作成功');
          getPage();
        });
    }
    function submitSearch(val) {
      state.form.keyWord = val;
      getPage();
    }

    onMounted(() => {
      getPage();
    });
    return {
      ...toRefs(state),
      ...toRefs(state6),
      clickType,
      getPage,
      submitSearch,
      isPower,
      searchRegister,
      searchEmit,
    };
  },
};
</script>

<style scoped>
  .ui-2-0 {
    width: calc(100% - 60px);
    flex: 1;
  }
</style>
