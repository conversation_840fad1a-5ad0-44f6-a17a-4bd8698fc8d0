<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.alibaba.cloud</groupId>
    <artifactId>spring-cloud-alibaba-starters</artifactId>
    <version>2021.0.6.2</version>
  </parent>
  <groupId>com.alibaba.cloud</groupId>
  <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
  <version>2021.0.6.2</version>
  <name>Spring Cloud Starter Alibaba Nacos Config</name>
  <description>Spring Cloud Alibaba Starters</description>
  <url>https://github.com/alibaba/spring-cloud-alibaba/spring-cloud-alibaba-starters/spring-cloud-starter-alibaba-nacos-config</url>
  <organization>
    <name>Pivotal Software, Inc.</name>
    <url>https://www.spring.io</url>
  </organization>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <name>xiaojing</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <name>Jim Fang</name>
      <email><EMAIL></email>
      <url>https://github.com/fangjian0423</url>
      <organization>Alibaba</organization>
    </developer>
    <developer>
      <name>xiaolongzuo</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <name>hengyunabc</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>mercyblitz</id>
      <name>Mercy Ma</name>
      <email><EMAIL></email>
      <url>https://github.com/mercyblitz</url>
      <organization>Alibaba</organization>
    </developer>
    <developer>
      <name>yunzheng</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>theonefx</id>
      <name>theonefx</name>
      <email><EMAIL></email>
      <url>https://github.com/theonefx</url>
      <organization>Alibaba</organization>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:git://github.com/alibaba/spring-cloud-alibaba.git/spring-cloud-alibaba-starters/spring-cloud-starter-alibaba-nacos-config</connection>
    <developerConnection>scm:git:ssh://**************/alibaba/spring-cloud-alibaba.git/spring-cloud-alibaba-starters/spring-cloud-starter-alibaba-nacos-config</developerConnection>
    <url>https://github.com/alibaba/spring-cloud-alibaba/spring-cloud-alibaba-starters/spring-cloud-starter-alibaba-nacos-config</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>com.alibaba.cloud</groupId>
      <artifactId>spring-cloud-alibaba-commons</artifactId>
      <version>2021.0.6.2</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-actuator-autoconfigure</artifactId>
      <version>2.6.13</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-configuration-processor</artifactId>
      <version>2.6.13</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>com.alibaba.spring</groupId>
      <artifactId>spring-context-support</artifactId>
      <version>1.0.11</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.alibaba.nacos</groupId>
      <artifactId>nacos-client</artifactId>
      <version>2.2.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-commons</artifactId>
      <version>3.1.5</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-context</artifactId>
      <version>3.1.5</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.7.36</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>jakarta.annotation</groupId>
      <artifactId>jakarta.annotation-api</artifactId>
      <version>1.3.5</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
</project>
