<script setup lang="ts">

import {
  BasicButton, BasicCard, FormSchema, OrionTable, useForm, BasicForm, useModal, SelectUserModal, openModal,
} from 'lyra-component-vue3';
import { computed, ref, unref } from 'vue';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import BasicCommodity from './BasicCommodity.vue';

const route = useRoute();
const [registerSelectUser, { openModal: openSelectUserModal }] = useModal();
const [registerSelectUser2, { openModal: openSelectUserModal2 }] = useModal();

const orderTableRef = ref();
const tableContractRef = ref();
const tableConcatOptions = {
  rowSelection: {},
  deleteToolButton: 'add|enable|disable',
  showSmallSearch: false,
  showTableSetting: false,
  rowKey: 'number',
  pagination: false,
  scroll: {
    y: 100,
  },
  batchDeleteApi: async (params) => {
    const { ids = [] } = params ?? {};
    const oldList = selectConcatRows.value.slice();
    selectConcatRows.value = [...oldList.filter((item) => !ids.includes(item.id))];
    orderTableRef.value.reload();
  },
  dataSource: computed(() => selectConcatRows.value),
  columns: [
    {
      title: '框架合同名称',
      dataIndex: 'name',
    },
    {
      title: '合同编号',
      dataIndex: 'number',
    },
  ],
  actions: [
    {
      text: '查看',
      onClick: () => {
      },
    },
  ],
};
const schemas: FormSchema[] = [
  {
    field: 'businessPersonName',
    component: 'InputSearch',
    label: '商务接口人',
    required: true,
    colProps: {
      span: 12,
    },
    componentProps: {
      placeholder: '请选择商务接口人',
      allowClear: false,
      onFocus(e) {
        e.target.blur();
        openSelectUserModal(true);
      },
    },
  },
  {
    field: 'technicalPersonName',
    component: 'InputSearch',
    label: '技术接口人',
    required: true,
    colProps: {
      span: 12,
    },
    componentProps: {
      placeholder: '请选择技术接口人',
      allowClear: false,
      onFocus(e) {
        e.target.blur();
        openSelectUserModal2(true);
      },
    },
  },
  {
    field: 'bearOrgName',
    component: 'Input',
    label: '输入框（必填）',
    rules: [
      {
        required: true,
        trigger: 'change',
        type: 'string',
      },
    ],
  },
];
const [register, { setFieldsValue, validate }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});
const formOtherConf = ref({});

const selectConcatRows = ref([]);

async function getValues() {
  const values = await validate();
  return values;
}

const selectUserCallback = (users) => {
  formOtherConf.value = {
    ...formOtherConf.value,
    businessPersonId: users[0].id,
  };
  setFieldsValue({
    businessPersonName: users[0].name,
  });
};
const selectUserCallback2 = (users) => {
  const [dept = {}] = users[0]?.organizations ?? [];
  setFieldsValue({
    technicalPersonName: users[0].name,
    bearOrgName: dept.name,
  });
  formOtherConf.value = {
    ...formOtherConf.value,
    technicalPersonId: users[0].id,
    bearOrgId: dept.id,
  };
};
const handleAssociationExample = () => {
  const modal = openModal({
    title: '请选择关联框架合同',
    width: 1000,
    content(h) {
      return h(BasicCommodity, {
        ref: tableContractRef,
        selectRows: selectConcatRows.value.map((item) => item.number),
      });
    },
    onOk: async () => {
      const getTableRef = tableContractRef.value.getTableRef();
      const params = getTableRef.getSelectRowKeys();
      const selectRows = getTableRef.getSelectRows();
      selectConcatRows.value = [...selectRows];
      if (!params.length) await Promise.reject(false);
      modal.close();
    },
  });
};

const resolveValidatedForm = async () => {
  if (!selectConcatRows.value.length) {
    message.error('请关联示例！');
    return Promise.reject(false);
  }
  const baseForm = await getValues();
  const result = {
    ...baseForm,
    ...formOtherConf.value,
    contractNumbers: selectConcatRows.value.map((item) => item.number).join(','),
  };
  return result;
};

defineExpose({
  resolveValidatedForm,
});
</script>

<template>
  <BasicCard title="商城订单信息">
    <OrionTable
      ref="orderTableRef"
      :options="tableConcatOptions"
    >
      <template #toolbarLeft>
        <BasicButton
          type="primary"
          icon="orion-icon-reload"
          @click="handleAssociationExample"
        >
          关联示例
        </BasicButton>
      </template>
    </OrionTable>
    <BasicForm @register="register" />
  </BasicCard>
  <SelectUserModal
    @ok="selectUserCallback"
    @register="registerSelectUser"
  />
  <SelectUserModal
    selectType="radio"
    @ok="selectUserCallback2"
    @register="registerSelectUser2"
  />
</template>

<style scoped lang="less">

</style>