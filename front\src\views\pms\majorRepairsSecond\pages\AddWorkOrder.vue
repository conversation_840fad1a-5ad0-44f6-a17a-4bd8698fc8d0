<template>

  <div class="drawerbox">
    <ul class="drawerul">
      <li>
        <p class="title">待选择作业</p>
        <a-input-search v-model:value="searchValue" placeholder="请输入查询内容" style="width: 100%;margin-bottom: 10px;"
          @search="onSearch" />
          <Spin :spinning="spinning" style="width: 100%; height: 500px;">
        <a-table  :columns="columns" :data-source="ltreeLists[0]" :childrenColumnName="'children'" bordered
          :row-selection="rowSelection" sticky :pagination="false" class="atable" :defaultExpandAllRows="true">
          <template #bodyCell="{ column, record }" :defaultExpandAllRows="true">
            <template v-if="column.key === 'name'">
              {{ record.name }}
              <p v-if="record.className == 'jobManage'" style="font-size: 12px;color: #ccc;margin-left: 16px">
                工单号:{{ record.number }}
                <a-popconfirm title="是否确认删除?" ok-text="确定" cancel-text="取消" @confirm="delconfirm(record)"
                  @cancel="cancel">
                  <img v-if="record.matchUp === 0" style="display: inline-block; width: 14px;height: 14px;"
                    src="../../../../assets/imgs/zujian/Frame.svg" alt="">
                </a-popconfirm>
              </p>
            </template>
            <template v-if="column.key === 'matchUp'">
              <img v-if="record.matchUp === 1" style="width: 20px;height: 20px; margin: auto;"
                src="../../../../assets/imgs/checkmark.svg" alt="">
              <img v-else-if="record.matchUp === 0" style="width: 23px;height: 24px; margin: auto;"
                src="../../../../assets/imgs/zujian/Frame(1).svg" alt="">
              <span v-else></span>
            </template>
          </template>
        </a-table>
      </Spin>
      </li>
      <li class="conetnt">
        <img @click="addbtn" src="../../../../assets/imgs/zujian/Button1.svg" alt="">
        <img src="../../../../assets/imgs/zujian/Button2.svg" alt="">
      </li>
      <li>
        <div class="navbtn">
          <p class="title">已选择作业({{ selectNum }})</p>
          <div class="navbtn-right">
            <div @click="confirm">   
                批量删除
            </div>
            <div class="btn" @click="openSelectModal">
              <img src="../../../../assets/imgs/zujian/tips_plus.svg" alt="">
              添加
            </div>
            <div class="btn" @click="openModal(true)">
              <img src="../../../../assets/imgs/zujian/log-out.svg" alt="">
              导入
            </div>
          </div>

        </div>
        <a-input-search v-model:value="searchValue2" placeholder="请输入查询内容" style="width: 100%;margin-bottom: 10px;"
          @search="ronSearch" />
          <Spin :spinning="spinningrigth" style="width: 100%; height: 500px;">
        <a-table :columns="columns" :data-source="rtreeLists[0]" bordered :row-selection="rowSelection2"
          :pagination="false" class="atable">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              {{ record.name }}
              <p v-if="record.className == 'jobManage'" style="font-size: 12px;color: #ccc;margin-left: 16px">
                工单号:{{ record.number }}</p>
            </template>
            <template v-if="column.key === 'matchUp'">
              <img v-if="record.matchUp === 1" style="width: 20px;height: 20px; margin: auto;"
                src="../../../../assets/imgs/checkmark.svg" alt="">
              <img v-else-if="record.matchUp === 0" style="width: 23px;height: 24px; margin: auto;"
                src="../../../../assets/imgs/zujian/Frame(1).svg" alt="">
              <span v-else></span>
            </template>
          </template>
        </a-table>
      </Spin>
      </li>
    </ul>
  </div>
  <BasicImport @register="register" :requestBasicImport="requestBasicImport"
    :requestSuccessImport="requestSuccessImport" :downloadFileObj="downloadFileObj"
    @change-import-modal-flag="changeImportModalFlag" />






</template>
<script setup lang="ts">
import { nextTick } from 'vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { defineComponent, ref, reactive, createVNode, onMounted } from 'vue';
import { Button as AButton,
   Drawer as ADrawer,
    Modal as AModal, 
    Table as ATable, 
    Popconfirm as APopconfirm, 
    InputSearch as AInputSearch, 
  } from 'ant-design-vue';
  import { Spin } from 'ant-design-vue';
  import { message } from 'ant-design-vue';
import { BasicImport, useModal, BasicButton } from 'lyra-component-vue3';
import { openBasicSelectModal, BasicInputSelectModal, type IOpenBasicSelectModalProps } from 'lyra-component-vue3'
import Api from '/@/api';
import { Item } from 'ant-design-vue/lib/menu';
import { rules } from '../../projectLaborer/flowcenter/flowTemplate/formDate';

const searchValue = ref('');
const searchValue2 = ref('');
const addsearchValue = ref('');
const onSearch = searchValue => {
  console.log('use value', searchValue);
  lorderlist()
};
const ronSearch = searchValue2 => {
  console.log('use value', searchValue2);
  rorderlist()
};

const spinning=ref(true)
const spinningrigth=ref(true)

const leftAddlidt=ref([])
const topAddlidt=ref([])


const columns = [
  {
    title: '组织架构',
    dataIndex: 'name',
    key: 'name',
    width: '70%',
    ellipsis: true,
  },
  // {
  //   title: '责任人',
  //   dataIndex: 'rspUserName',
  //   key: 'rspUserName',
  //   width: '20%',
  // },
  {
    title: '状态',
    dataIndex: 'matchUp',
    width: '20%',
    key: 'matchUp',
  },
];


const selectDatas = ref([])
const rowSelection = ref({
  checkStrictly: false,
  onChange: (selectedRowKeys, selectedRows) => {
    console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
  },
  onSelect: (record, selected, selectedRows) => {
    selectDatas.value = selectedRows
  },
  onSelectAll: (selected, selectedRows, changeRows) => {
  },
});

const selectDatas2 = ref([])
const selectNum = ref(0)
const rowSelection2 = ref({
  onChange: (selectedRowKeys, selectedRows) => {
    console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
    selectDatas2.value = null
    selectDatas2.value = selectedRows
    selectNum.value=selectedRows.length;
  },
  onSelect: (record, selected, selectedRows) => {
    console.log(record, selected, selectedRows);
    selectDatas2.value = null,
      selectDatas2.value = selectedRows;
      selectNum.value=selectDatas2.value.length;
  },
  onSelectAll: (selected, selectedRows, changeRows) => {
    console.log(selected, selectedRows, changeRows);
  },
});
function uniqueByKey(arr, key) {
  const seen = {};
  const result = [];

  for (let i = 0; i < arr.length; i++) {
    const item = arr[i];
    const k = item[key];
    if (!seen[k]) {
      seen[k] = true;
      result.push(item);
    }
  }

  return result;
}

const addbtn = () => {
  selectDatas.value.forEach((av, index) => {
    if (av.children) {
      selectDatas.value.splice(index, 1)
      av.children.forEach((item: any) => {
        rtreeLists.value[0].push(item),
          leftAddlidt.value.push(item)
      })
      rtreeLists.value[0] = uniqueByKey(rtreeLists.value[0], 'key');
      leftAddlidt.value= uniqueByKey(leftAddlidt, 'key');
      
    } else {
      rtreeLists.value[0].push(av)
      leftAddlidt.value.push(av)
      rtreeLists.value[0] = uniqueByKey(rtreeLists.value[0], 'key');
      leftAddlidt.value= uniqueByKey(leftAddlidt, 'key');
    }
    selectDatas.value = []
  })

}




const delList=ref([])
const confirm = () => {
  const list = reactive([])
  list.length = 0;
  selectDatas2.value.forEach(el => {
      if (el != undefined) {
        list.push(el)
        delList.value.push(el.id)
      }
    })
  if(selectDatas2.value.length==0 || rtreeLists.value[0].length==0){
    message.error('暂无可删除数据！');
  }else{
    rtreeLists.value[0] = rtreeLists.value[0].filter(item2 => {
      return !list.some(item1 => item1.key === item2.key);
    });
    leftAddlidt.value = leftAddlidt.value.filter(item2 => {
      return !list.some(item1 => item1.key === item2.key);
    });
    topAddlidt.value= topAddlidt.value.filter(item2 => {
      return !list.some(item1 => item1.key === item2.key);
    });
  }
}

const delconfirm = (value) => {
  ltreeLists[0] = deleteNodeByKey(ltreeLists[0], value.key);
}

function deleteNodeByKey(treeArray, keyToDelete) {
    let newTreeArray = [];
    for (let rootNode of treeArray) {
        let updatedRootNode = deleteNodeRecursively(rootNode, keyToDelete);
        if (updatedRootNode !== null) {
            newTreeArray.push(updatedRootNode);
        }
    }
    return newTreeArray;
}
 
function deleteNodeRecursively(node, keyToDelete) {
    if (node.key === keyToDelete) {
        return null; 
    }
    let newChildren = [];
    for (let child of node.children || []) {
        let updatedChild = deleteNodeRecursively(child, keyToDelete);
        if (updatedChild !== null) {
            newChildren.push(updatedChild);
        }
    }
 
    let newNode = { ...node }; 
    newNode.children = newChildren; 
    return newNode;
}
 


//工单添加
const loading = ref(false);
const visible = ref(false);






const [register, { openModal }] = useModal();
function requestBasicImport(files: any[]) {
  const formData = new FormData();
  formData.append('file', files[0]);
  // return new Api('/import/excelNew/H208/{importId}').fetch(formData, '', 'POST');
  return new Api('/pms/job-manage/import/excel/checkNew').fetch(formData, '', 'POST');
}


const requestSuccessImport = (successKey) => {
  return new Promise(async (resolve) => {
    setTimeout(async () => {
      const result = { result: true };
      if (result.result) {
        await importfile(successKey);
      }
      resolve(result);
    }, 2000);
  });
};

async function importfile(successKey) {
  try {
    const result = await new Api(`/pms/job-manage/import/excelNew/H208/${successKey}`).fetch('', '', 'POST');
  } catch (e) {
    console.error('Error importing file:', e);
  }
}


const downloadFileObj = {
  url: '/pms/job-manage/download/excel/tpl',
  method: 'GET',
};
const changeImportModalFlag = (flag) => {
  if (flag.successImportFlag == true) {
    visible.value = true;
  }
};
function handleClickDownloadTemplate() {
  console.log('点击下载模板');
}


const options: IOpenBasicSelectModalProps = {
  title: '选择作业',
  selectType: 'checkbox',
  tableColumns: [
    {
      title: '工单号',
      dataIndex: 'number',
    },
    {
      title: '作业名称',
      dataIndex: 'name',
    },
    {
      title: '系统条件',
      dataIndex: 'phase',
    },
    {
      title: '大修轮次',
      dataIndex: 'repairRound',
    },
  ],
  async tableApi(params) {
    addsearchValue.value = params.searchConditions?.[0]?.[0]?.values?.[0] || '';
    await addbusiness();
    return addlist[0];
  },
  onOk(value) {
    return new Promise((resolve, reject) => {
      setTimeout(() => { resolve() }, 1000)
      value.forEach((item) => {
        rtreeLists.value[0].push(item)
        topAddlidt.value.push(item)
      })
    })

  },
};

function openSelectModal() {
  openBasicSelectModal(options);
}


//左侧列表
const ltreeLists = reactive([])
const rtreeLists = ref([])
async function lorderlist() {
  try {
    const result = await new Api('/pms/centerJobManage/treeList').fetch({
      keyword: searchValue.value,
      number: "",
      repairRound: 'H208',
    }, '', 'POST');
    if(result){
      spinning.value=false;
    
    ltreeLists.length = 0;
    let num = 666;
    let num1 = 1;
    let num2 = 2;
    const lists = result.map((item) => {
      if (item.selected != null && item.selected !== 0) {
        item.selected = true;
      } else {
        item.selected = false;
      }
      return ({
        children: item.centerList.map((el) => {
          if (item.selected != null && item.selected !== 0) {
            item.selected = true;
          } else {
            item.selected = false;
          }
          return ({
            children: el.jobList.map((ele) => {
              if (ele.selected != null && ele.selected !== 0) {
                ele.selected = true;
              } else {
                ele.selected = false;
              }
              return ({
                key: ele.number,
                name: ele.name,
                selected: ele.selected,
                rspUserName: ele.rspUserName,
                status: ele.status,
                matchUp: ele.matchUp,
                code: ele.parentCode,
                disabled: ele.selected,
                className: ele.className,
                number: ele.number
              })
            }),
            name: el.workCenter,
            number: el.number,
            rspUserId: el.rspUserId,
            selected: el.selected,
            rspUserName: el.rspUserName,
            status: el.status,
            key: el.workCenter,
            code: el.code,
          })
        }),
        name: item.repairRound,
        key: num1++,
        selected: item.selected,
        rspUserName: item.rspUserName,
        status: item.status,
        code: item.code,
      })
    })
    ltreeLists.push(lists)
  }
  } catch (e) {
  }
}

async function rorderlist() {
  try {
    const result = await new Api(`/pms/centerJobManage/used/treeList`).fetch({
      keyword: searchValue2.value,
      number: "",
      repairOrgId: '1111',
    }, '', 'POST');
    rtreeLists.value.length = 0;
    let num1 = 1;
    if(result){
      spinningrigth.value=false;
      const rlists = result.map((item) => {
        if (item.selected != null && item.selected !== 0) {
          item.selected = true;
        } else {
          item.selected = false;
        }
        return ({
          name: item.name,
          key: num1++,
          id:item.id,
          selected: item.selected,
          rspUserName: item.rspUserName,
          status: item.status,
          code: item.code,
          jobBaseName: item.jobBaseName,
          workCenter: item.workCenter,
          rspUserId: item.rspUserId,
          matchUp: item.matchUp,
          norOitem: item.norO,
          phase: item.phase
        })
      })
      rtreeLists.value.push(rlists)
    }
  } catch (e) {
  }
}


const addlist = reactive([])
async function addbusiness() {
  try {
    const result = await new Api('/icm/job/manage/page').fetch({
      jobNumberList: [],
      keyword: addsearchValue.value,
      pageNum: 0,
      pageSize: 0,
      repairRound: "H208"
    }, '', 'POST');
    let num1 = 999;
    let lists = result.content.map((item) => {
      return ({
        actualBeginTime: item.actualBeginTime,
        actualEndTime: item.actualEndTime,
        endTime: item.endTime,
        beginTime: item.beginTime,
        name: item.name,
        norO: item.norO,
        number: item.number,
        key: num1++,
        id: item.number,
        phase: item.phase,
        repairRound: item.repairRound,
        workCenter: item.workCenter,
        workDuration: item.workDuration,
        matchUp: item.matchUp,
      })
    })
    addlist.push(lists)
  } catch (e) {
  }
}



onMounted(async () => {
  await lorderlist();
  await rorderlist();
  await addbusiness();
})



</script>
<style lang="less" scoped>
.drawerbox {
  width: 95%;
  height: 100%;
}

.drawerul {
  width: 105%;
  height: 99%;
  color: #000000;
  list-style-type: none;
  display: flex;
  justify-content: space-between;
  padding: 20px 20px;

  li {
    width: 47%;
    height: 101%;
    border: 1px solid #CDD1D5;
    border-radius: 2px;
    padding: 5px 10px;
    overflow: hidden;

    .navbtn {
      width: 100%;
      height: 30px;
      // min-width: ;
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;

      .navbtn-right {
        width: 50%;
        min-width: 238px;
        height: 100%;
        display: flex;
        justify-content: space-between;
        cursor: pointer;

        div {
          display: flex;
          padding: 6px 10px;
          font-size: 14px;
          color: #0960BD;
          line-height: 15px;

          img {
            display: block;
            width: 15px;
            height: 15px;
            margin-right: 3px;
          }
        }

        .btn {
          color: #0960BD;
          border: 1.5px solid #0960BD;
          border-radius: 2px;
          background-color: rgba(9, 96, 189, 0.1);
        }
      }
    }

    /deep/.ant-table-thead>th {
      background-color: #f0f0f0;
    }

    .title {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: bold;
      font-size: 14px;
      color: #4E5969;
      line-height: 22px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    /deep/.ant-input-search-button {
      height: 31px;
    }
  }



  .conetnt {
    width: 50px;
    height: 96%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border: none;

    img {
      display: block;
      width: 24px;
      height: 24px;
      margin: 5px 0px;
    }

  }
}

.fotterbox {
  width: 100%;
  height: 35px;
  border-top: 1px solid #ccc;
  position: relative;

  /deep/.subbtn {
    background-color: #0960BD;
  }
}

// .drawer-footer{
//   width: 95%;
// }
#listTableRef {
  margin-top: 15px;
}

/deep/.ant-table-thead {
  position: sticky !important;
  top: 0px;
  z-index: 1;
}

.ant-btn-primary {
  background-color: #0960BD !important;
}

.atable {
  max-height: 500px;
  overflow-y: auto;
}

.atable::-webkit-scrollbar {
  width: 8px;
}

.atable::-webkit-scrollbar-thumb {
  background-color: #888;
  border-radius: 4px;
}

.atable::-webkit-scrollbar-thumb:hover {
  background-color: #555;
}

.atable::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.add-content {
  width: 100%;
  display: flex;
  height: 486px;
  background-color: springgreen;

  .cont-left {
    width: 230px;
    height: 100%;
    // background-color: pink;
    padding: 0px 12px;
    border-right: 1px solid #f0f0f0;

    .ant-input-group-wrapper {
      margin: 11px 0px;
    }

    /deep/.ant-input-search-button {
      height: 31px;
      border-left: none;
    }

    .ant-tree.ant-tree-directory .ant-tree-treenode-selected::before {
      background: #afcae4;
    }
  }

  .cont-cont {
    width: 630px;
    height: 100%;
    border-right: 1px solid #f0f0f0;
    background-color: red;

    /deep/.ant-input-search-button {
      height: 31px;
      border-left: none;
    }

    // .ant-btn-primary{
    //   margin-top: 10px;
    // }
    .inp {
      margin: 10px;
      margin-left: 45%;
    }

    .a-table {
      width: 96%;
      margin: 0 auto;
    }

    .pagin {
      width: 99%;
      height: 30px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      margin-top: 10px;
      line-height: 30px;
    }

    :deep(.ant-pagination-item) {
      min-width: 21px;
      margin-inline-end: 0px;

    }

    :deep(.ant-pagination .ant-pagination-options-quick-jumper input) {
      width: 27px;
    }

    :deep(.ant-pagination .ant-pagination-options-quick-jumper) {
      margin-inline-start: 2px;
    }

    :deep(:where(.css-dev-only-do-not-override-18hkbno).ant-pagination) {
      text-align: left;
    }

    :deep(.ant-pagination .ant-pagination-jump-next .ant-pagination-jump-prev .ant-pagination-prev) {
      min-width: 15px;
      margin-inline-end: 3px;
    }

    /deep/.ant-pagination-item {
      min-width: 21px;
      margin-inline-end: 0px;
    }

    .atable {
      max-height: 380px;
      overflow-y: auto;
    }

    .atable::-webkit-scrollbar {
      width: 5px;
    }

    .atable::-webkit-scrollbar-thumb {
      background-color: #888;
      border-radius: 4px;
    }

    .atable::-webkit-scrollbar-thumb:hover {
      background-color: #555;
    }

    .atable::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

  }

  .cont-right {
    width: 230px;
    height: 100%;

    .right-nav {
      width: 91%;
      margin: 10px;
      display: flex;
      justify-content: space-between;

      span {
        color: #0960BD;
        cursor: pointer;
      }
    }

    ul {
      width: 99%;
      height: 435px;
      list-style-type: none;
      padding-left: 10px;
      margin-top: 15px;

      li {
        span {
          margin-right: 6px;
          line-height: 25px;
        }

        img {
          width: 20px;
          height: 12px;
        }
      }
    }

    ul::-webkit-scrollbar {
      display: none;
      /* Chrome Safari */
    }

    ul {
      scrollbar-width: none;
      /* firefox */
      -ms-overflow-style: none;
      /* IE 10+ */
      overflow-x: hidden;
      overflow-y: auto;
    }
  }
}
// .ant-spin .ant-spin-spinning{
// position: relative;
// top: 50%;
// left: 50%;
// }
</style>