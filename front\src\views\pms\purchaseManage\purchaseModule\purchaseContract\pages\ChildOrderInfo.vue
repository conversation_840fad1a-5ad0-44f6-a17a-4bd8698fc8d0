<script setup lang="ts">
import { Spin, Tag as ATag, Tooltip } from 'ant-design-vue';
import {
  Layout3, Layout3Content, BasicButton, isPower,
} from 'lyra-component-vue3';
import {
  computed, onMounted, provide, reactive, ref, Ref, unref,
} from 'vue';
import { useRoute } from 'vue-router';
import PurchaseProjectApproval
  from '../components/PurchaseProjectApproval/PurchaseProjectApproval.vue';
import Api from '/@/api';
import { BasicInjectionsKey } from '../../tokens/basicKeys';
import CarryOut from '../components/CarryOut/CarryOut.vue';
import PurchaseContractForm from '../components/PurchaseContractForm.vue';
import BasicHtInfo from '../components/BasicInfo/BasicInfo.vue';
import { openFormDrawer } from '/@/views/pms/purchaseManage/purchaseModule/utils';

interface MenuItem {
  id: string,
  name: string,
  show?: boolean,
  children?: MenuItem[]
}

const loading: Ref<boolean> = ref(false);
const layoutData = computed(() => ({}));
const defaultActionId: Ref<string> = ref('jBXX');
const route = useRoute();
const projectId: Ref<string> = ref(route.params.id as string);
const basicInfo = reactive({
  data: {},
});
const powerData = ref();
const showEditBtn = computed(() => isPower('PMS_KJHTZDDXQ_container_04_button_01', powerData));

const menuData = computed(() => [
  {
    id: 'jBXX',
    name: '基本信息',
    code: 'PMS_KJHTZDDXQ_container_01',
  },
  {
    id: 'cGLX',
    name: '采购立项',
    code: 'PMS_KJHTZDDXQ_container_02',
  },
  {
    id: 'hTZX',
    name: '合同执行',
    code: 'PMS_KJHTZDDXQ_container_03',
  },
].filter((item) => isPower(item.code, powerData.value)));

provide(BasicInjectionsKey, basicInfo);

function menuChange(option: { id: string, index: number, item: MenuItem }): void {
  defaultActionId.value = option.id;
}

const getInfo = async () => {
  try {
    const result = await new Api('/pms/contractInfo').fetch({
      pageCode: 'lumpSumContract',
    }, unref(projectId), 'GET');
    basicInfo.data = result || {};
    const basicArr = [
      {
        id: 'jBXX',
        name: '基本信息',
        code: 'PMS_KJHTZDDXQ_container_01',
      },
      {
        id: 'cGLX',
        name: '采购立项',
        code: 'PMS_KJHTZDDXQ_container_02',
      },
      {
        id: 'hTZX',
        name: '合同执行',
        code: 'PMS_KJHTZDDXQ_container_03',
      },
    ];
    menuData.value = basicArr;
  } catch (e) {

  }
};
const getPowerDataHandle = async (data: any) => {
  powerData.value = data;
};

onMounted(async () => {
  await getInfo();
});
const handleUpdateForm = () => {
  openFormDrawer(PurchaseContractForm, basicInfo.data, () => {
    getInfo();
  });
};
</script>

<template>
  <Layout3
    v-get-power="{pageCode: 'childOrderInfo001',getPowerDataHandle}"
    :projectData="layoutData"
    :menuData="menuData"
    :defaultActionId="defaultActionId"
    :type="2"
    :onMenuChange="menuChange"
    class="purchase-manage-layout"
  >
    <template #code>
      <h2
        class="custom-page-title"
        :title="basicInfo.data?.contractName"
      >
        {{ basicInfo.data?.contractName || basicInfo.data?.techRespons || "--" }}
      </h2>
      <div class="page-subtitle">
        <span>合同编号：{{ basicInfo.data?.contractNumber }}</span>
        <template v-if="basicInfo.data?.purchaseApplicant">
          <Tooltip placement="top">
            <template #title>
              <span class="tooltip">{{ basicInfo.data?.purchaseApplicant }}</span>
            </template>
            <span>采购申请号：{{ basicInfo.data?.purchaseApplicant }}</span>
          </Tooltip>
        </template>
        <span v-else>采购申请号：-</span>
      </div>
    </template>
    <template #header-info>
      <div class="type-map">
        <a-tag
          style="margin-left: 20px;"
          color="purple"
        >
          已审核
        </a-tag>
        <a-tag color="green">
          履行中
        </a-tag>
      </div>
    </template>
    <template #header-right>
      <BasicButton
        v-if="showEditBtn"
        type="primary"
        ghost
        icon="sie-icon-chakantuisong"
        @click="handleUpdateForm"
      >
        编辑
      </BasicButton>
    </template>
    <div
      v-if="loading"
      class="w-full h-full flex flex-pc flex-ac"
    >
      <Spin />
    </div>
    <Layout3Content v-else>
      <BasicHtInfo v-if="defaultActionId==='jBXX'" />
      <PurchaseProjectApproval v-if="defaultActionId==='cGLX'" />
      <CarryOut v-if="defaultActionId==='hTZX'" />
    </Layout3Content>
  </Layout3>
</template>

<style scoped lang="less">
:deep(.header-wrap) {
  min-height: 60px;
  height: auto;
  .header-main{
    min-height: 33px;
    height: auto !important;
  }
  .project-title {
    width: 400px !important;
    .flex-te{
      word-wrap: break-word;
      overflow-wrap: break-word;
      white-space: normal;
      line-height: 26px;
      padding-top: 10px;
    }
  }
  .layout-menu-warp {
    display: flex;
    align-items: center;

    .ant-menu {
      height: 60px !important;
    }
  }
}

.custom-page-title {
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
  line-height: 26px;
  padding-top: 10px;
  font-size: 18px;
  color: #000000D9;
}

.page-subtitle {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  font-size: 14px;
  color: rgb(150, 158, 180);
  font-weight: 40;
  padding-bottom: 6px;
  span {
    margin-right: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100%;
  }
}

.type-map {
  display: flex;
  align-items: center;
  .type-wrapper{
    display: flex;
    flex-direction: column;
  }

  .type-h2-enum {
    font-size: 14px;
    color: #000000D9;
    margin-bottom: 3px;
  }

  .type-p-enum {
    font-size: 12px;
    color: #000000D9;
    margin-bottom: 0;
  }
}
.purchase-manage-layout{
  :deep(.ant-menu-overflow){
    min-width: 300px;
  }
}
</style>
