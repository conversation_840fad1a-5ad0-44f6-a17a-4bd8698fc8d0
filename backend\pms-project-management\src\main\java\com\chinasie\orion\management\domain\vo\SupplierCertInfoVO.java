package com.chinasie.orion.management.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * SupplierCertInfo VO对象
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@ApiModel(value = "SupplierCertInfoVO对象", description = "资质信息表")
@Data
public class SupplierCertInfoVO extends ObjectVO implements Serializable {

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;


    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;


    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    private String serialNumber;


    /**
     * 资质证书名称
     */
    @ApiModelProperty(value = "资质证书名称")
    private String certName;


    /**
     * 资质类别
     */
    @ApiModelProperty(value = "资质类别")
    private String certCategory;


    /**
     * 资质等级
     */
    @ApiModelProperty(value = "资质等级")
    private String certLevel;


    /**
     * 资质分组
     */
    @ApiModelProperty(value = "资质分组")
    private String certGroup;


    /**
     * 代理品牌/产品名称
     */
    @ApiModelProperty(value = "代理品牌/产品名称")
    private String brandProduct;


    /**
     * 证书有效期截止日期
     */
    @ApiModelProperty(value = "证书有效期截止日期")
    private String expiryDate;


    /**
     * 证书编码
     */
    @ApiModelProperty(value = "证书编码")
    private String certCode;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    private String mainTableId;


}
