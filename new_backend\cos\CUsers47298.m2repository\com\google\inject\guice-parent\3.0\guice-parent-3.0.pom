<?xml version="1.0" encoding="UTF-8"?>

<!--
Copyright (c) 2006 Google, Inc. All rights reserved.

This program is licensed to you under the Apache License Version 2.0,
and you may not use this file except in compliance with the Apache License Version 2.0.
You may obtain a copy of the Apache License Version 2.0 at http://www.apache.org/licenses/LICENSE-2.0.

Unless required by applicable law or agreed to in writing,
software distributed under the Apache License Version 2.0 is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the Apache License Version 2.0 for the specific language governing permissions and limitations there under.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.google</groupId>
    <artifactId>google</artifactId>
    <version>5</version>
  </parent>

  <packaging>pom</packaging>

  <groupId>com.google.inject</groupId>
  <artifactId>guice-parent</artifactId>
  <version>3.0</version>

  <name>Google Guice</name>

  <description>
    Guice is a lightweight dependency injection framework for Java 5 and above
  </description>

  <url>http://code.google.com/p/google-guice/</url>
  <inceptionYear>2006</inceptionYear>

  <organization>
    <name>Google, Inc.</name>
    <url>http://www.google.com</url>
  </organization>

  <mailingLists>
    <mailingList>
      <name>Guice Users List</name>
      <archive>http://groups.google.com/group/google-guice/topics</archive>
      <subscribe>http://groups.google.com/group/google-guice/subscribe</subscribe>
      <unsubscribe>http://groups.google.com/group/google-guice/subscribe</unsubscribe>
      <post>http://groups.google.com/group/google-guice/post</post>
    </mailingList>
    <mailingList>
      <name>Guice Developers List</name>
      <archive>http://groups.google.com/group/google-guice-dev/topics</archive>
      <subscribe>http://groups.google.com/group/google-guice-dev/subscribe</subscribe>
      <unsubscribe>http://groups.google.com/group/google-guice-dev/subscribe</unsubscribe>
      <post>http://groups.google.com/group/google-guice-dev/post</post>
    </mailingList>
  </mailingLists>

  <scm>
    <connection>scm:svn:http://google-guice.googlecode.com/svn/trunk</connection>
    <developerConnection>scm:svn:https://google-guice.googlecode.com/svn/trunk</developerConnection>
    <url>http://code.google.com/p/google-guice/source/browse</url>
  </scm>

  <issueManagement>
    <system>Google Code</system>
    <url>http://code.google.com/p/google-guice/issues/</url>
  </issueManagement>

  <ciManagement>
    <system>Hudson</system>
    <url>https://grid.sonatype.org/ci/job/Google-Guice/</url>
  </ciManagement>

  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <modules>
    <module>core</module>
    <module>extensions</module>
  </modules>

  <prerequisites>
    <maven>3.0</maven>
  </prerequisites>

  <properties>
    <!--
     | The spec version of the public Guice API
    -->
    <guice.api.version>1.3</guice.api.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <!--
     | Use "-Dguice.with.jarjar=false" to build without jarjar
    -->
    <guice.with.jarjar>true</guice.with.jarjar>
    <!--
     | Use "-Dguice.with.no_aop=false" to skip the no-AOP variant
    -->
    <guice.with.no_aop>true</guice.with.no_aop>
  </properties>

  <dependencies>
    <!--
     | Run tests with TestNG
    -->
    <dependency>
      <groupId>org.testng</groupId>
      <artifactId>testng</artifactId>
      <version>5.11</version>
      <classifier>jdk15</classifier>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <!--
     | Ant-style directories
    -->
    <sourceDirectory>${project.basedir}/src</sourceDirectory>
    <resources>
      <resource>
        <filtering>false</filtering>
        <directory>${project.basedir}/src</directory>
        <excludes>
          <exclude>**/*.java</exclude>
        </excludes>
      </resource>
    </resources>
    <testSourceDirectory>${project.basedir}/test</testSourceDirectory>
    <testResources>
      <testResource>
        <filtering>false</filtering>
        <directory>${project.basedir}/test</directory>
        <excludes>
          <exclude>**/*.java</exclude>
        </excludes>
      </testResource>
    </testResources>
    <pluginManagement>
      <plugins>
        <!--
         | Standard LICENSE and NOTICE files
        -->
        <plugin>
          <artifactId>maven-remote-resources-plugin</artifactId>
          <version>1.1</version>
          <executions>
            <execution>
              <goals>
                <goal>process</goal>
              </goals>
              <configuration>
                <resourceBundles>
                  <resourceBundle>org.apache:apache-jar-resource-bundle:1.4</resourceBundle>
                </resourceBundles>
              </configuration>
            </execution>
          </executions>
        </plugin>
        <!--
         | Make sure we only use Java5 methods
        -->
        <plugin>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>2.3.2</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>animal-sniffer-maven-plugin</artifactId>
          <version>1.6</version>
          <configuration>
            <signature>
              <groupId>org.codehaus.mojo.signature</groupId>
              <artifactId>java15</artifactId>
              <version>1.0</version>
            </signature>
          </configuration>
          <executions>
            <execution>
              <id>check-java-1.5-compat</id>
              <phase>process-classes</phase>
              <goals>
                <goal>check</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <!--
         | Shared JarJar configuration
        -->
        <plugin>
          <groupId>org.sonatype.plugins</groupId>
          <artifactId>jarjar-maven-plugin</artifactId>
          <version>1.3</version>
          <configuration>
            <rules>
              <rule>
                <pattern>net.sf.cglib.*</pattern>
                <result>com.google.inject.internal.cglib.$@1</result>
              </rule>
              <rule>
                <pattern>net.sf.cglib.**.*</pattern>
                <result>com.google.inject.internal.cglib.@1.$@2</result>
              </rule>
              <rule>
                <pattern>org.objectweb.asm.*</pattern>
                <result>com.google.inject.internal.asm.$@1</result>
              </rule>
              <rule>
                <pattern>org.objectweb.asm.**.*</pattern>
                <result>com.google.inject.internal.asm.@1.$@2</result>
              </rule>
              <rule>
                <pattern>com.google.inject.internal.util.*</pattern>
                <result>com.google.inject.internal.util.$@1</result>
              </rule>
              <rule>
                <pattern>com.google.inject.internal.util.**.*</pattern>
                <result>com.google.inject.internal.util.@1.$@2</result>
              </rule>
              <keep>
                <pattern>com.google.inject.**</pattern>
              </keep>
              <keep>
                <pattern>com.googlecode.**</pattern>
              </keep>
            </rules>
          </configuration>
          <!--
           | JarJar all classes before running tests
          -->
          <executions>
            <execution>
              <id>jarjar-classes</id>
              <phase>process-test-classes</phase>
              <goals>
                <goal>jarjar</goal>
              </goals>
              <configuration>
                <input>{classes}</input>
              </configuration>
            </execution>
            <execution>
              <id>jarjar-test-classes</id>
              <phase>process-test-classes</phase>
              <goals>
                <goal>jarjar</goal>
              </goals>
              <configuration>
                <input>{test-classes}</input>
              </configuration>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.5</version>
          <configuration>
            <redirectTestOutputToFile>true</redirectTestOutputToFile>
          </configuration>
        </plugin>
        <!--
         | Shared OSGi manifest configuration
        -->
        <plugin>
          <groupId>org.apache.felix</groupId>
          <artifactId>maven-bundle-plugin</artifactId>
          <version>2.1.0</version>
          <configuration>
            <instructions>
              <module>com.google.inject</module>
              <_include>-${project.basedir}/build.properties</_include>
              <Bundle-Copyright>Copyright (C) 2006 Google Inc.</Bundle-Copyright>
              <Bundle-DocURL>http://code.google.com/p/google-guice/</Bundle-DocURL>
              <Bundle-Name>${project.artifactId}</Bundle-Name>
              <Bundle-SymbolicName>$(module)</Bundle-SymbolicName>
              <Bundle-RequiredExecutionEnvironment>
                J2SE-1.5,JavaSE-1.6
              </Bundle-RequiredExecutionEnvironment>
              <Import-Package>!com.google.inject.*,*</Import-Package>
              <_exportcontents>!*.internal.*,$(module).*;version=${guice.api.version}</_exportcontents>
              <_versionpolicy>[$(version;==;$(@)),$(version;+;$(@)))</_versionpolicy>
              <_nouses>true</_nouses>
              <_removeheaders>
                Embed-Dependency,Embed-Transitive,
                Built-By,Tool,Created-By,Build-Jdk,
                Originally-Created-By,Archiver-Version,
                Include-Resource,Private-Package,
                Ignore-Package,Bnd-LastModified
              </_removeheaders>
            </instructions>
          </configuration>
          <executions>
            <execution>
              <phase>prepare-package</phase>
              <goals>
                <goal>manifest</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <!--
         | Package OSGi manifest in final JAR, also create a JAR of the test classes
        -->
        <plugin>
          <artifactId>maven-jar-plugin</artifactId>
          <version>2.3.1</version>
          <configuration>
            <archive>
              <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
              <!--  Exclude to mirror ant build -->
     		  <addMavenDescriptor>false</addMavenDescriptor>
            </archive>
          </configuration>
          <executions>
            <execution>
              <phase>package</phase>
              <goals>
                <goal>test-jar</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>2.7</version>
        </plugin>
        <plugin>
          <artifactId>maven-source-plugin</artifactId>
          <version>2.1.2</version>
        </plugin>
        <plugin>
          <artifactId>maven-gpg-plugin</artifactId>
          <version>1.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-release-plugin</artifactId>
          <version>2.1</version>
          <configuration>
            <autoVersionSubmodules>true</autoVersionSubmodules>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>2.5</version>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>

  <profiles>
    <profile>
      <!--
       | Deployment profile for the Sonatype Grid
      -->
      <id>sonatype-grid</id>
      <properties>
        <forgeReleaseId>forge-releases</forgeReleaseId>
        <forgeReleaseUrl>http://repository.sonatype.org:8081/service/local/staging/deploy/maven2</forgeReleaseUrl>
        <forgeSnapshotId>forge-snapshots</forgeSnapshotId>
        <forgeSnapshotUrl>http://repository.sonatype.org/content/repositories/snapshots</forgeSnapshotUrl>
      </properties>
      <distributionManagement>
        <repository>
          <id>${forgeReleaseId}</id>
          <url>${forgeReleaseUrl}</url>
        </repository>
        <snapshotRepository>
          <id>${forgeSnapshotId}</id>
          <url>${forgeSnapshotUrl}</url>
        </snapshotRepository>
      </distributionManagement>
    </profile>
  </profiles>

</project>
