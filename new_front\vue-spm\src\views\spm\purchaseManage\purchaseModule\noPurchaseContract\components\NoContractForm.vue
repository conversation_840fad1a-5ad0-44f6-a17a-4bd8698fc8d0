<script setup lang="ts">
import { BasicForm, useForm } from 'lyra-component-vue3';
import { onMounted, Ref, ref } from 'vue';
import Api from '/@/api';

const props = defineProps<{
  record: Record<string, any> | null,
}>();

const schemas = [
  {
    field: 'initiator',
    label: '发起人',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'applyCompanyName',
    label: '申请公司名称',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'currency',
    label: '币种',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'applyDept',
    label: '申请部门',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'expenseCompanyCode',
    label: '费用归属公司编码',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'xpenseCompanyName',
    label: '费用归属公司名称',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'applyReason',
    label: '申请原因',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'expenseInfo',
    label: '费用信息',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'supplierInfo',
    label: '供应商信息',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'internalTxNumber',
    label: '内部交易号',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'paymentWay',
    label: '支付方式',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'applyNumber',
    label: '申请笔数',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'paymentAmount',
    label: '支付金额',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'transactionAmount',
    label: '交易金额',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'actualPaymentAmount',
    label: '实际支付金额',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'reimbursementAmount',
    label: '报销金额',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
];

const [register, { validate, setFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  props?.record?.id && getFormData();
});

const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  try {
    await setFieldsValue(props.record);
  } finally {
    loading.value = false;
  }
}

defineExpose({
  async onSubmit() {
    const formValues = await validate();
    const params = {
      id: props?.record?.id,
      ...formValues,

    };

    return new Promise((resolve, reject) => {
      new Api('/spm/nonContractProc').fetch(params, props?.record?.id ? 'edit' : 'add', props?.record?.id ? 'PUT' : 'POST').then(() => {
        resolve('');
      }).catch((err) => {
        reject(err);
      });
    });
  },
});
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">

</style>
