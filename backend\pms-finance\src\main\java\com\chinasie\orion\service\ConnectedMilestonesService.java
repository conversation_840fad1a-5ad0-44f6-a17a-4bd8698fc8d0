package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.ConnectedMilestonesDTO;
import com.chinasie.orion.domain.dto.HangingConnectDTO;
import com.chinasie.orion.domain.entity.ConnectedMilestones;
import com.chinasie.orion.domain.entity.IncomePlanData;
import com.chinasie.orion.domain.vo.ConnectedMilestonesVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * ConnectedMilestones 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-20 16:31:37
 */
public interface ConnectedMilestonesService extends OrionBaseService<ConnectedMilestones> {

    /**
     * 新增
     * <p>
     * * @param dto
     */
    String create(ConnectedMilestonesDTO dto) throws Exception;

    /**
     * 收入计划分页数据
     * <p>
     * * @param pageRequest
     */
    Page<ConnectedMilestonesVO> incomePlanPage(Page<ConnectedMilestonesDTO> pageRequest) throws Exception;

    /**
     * 调账凭证分页数据
     * <p>
     * * @param pageRequest
     */
    Page<ConnectedMilestonesVO> adjustmentVoucherPage(Page<ConnectedMilestonesDTO> pageRequest);

    /**
     * 收入计划列表数据
     * <p>
     * * @param dto
     */
    List<ConnectedMilestonesVO> incomePlanList(ConnectedMilestonesDTO dto) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(ConnectedMilestonesDTO dto, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<ConnectedMilestonesVO> vos) throws Exception;

    /**
     * 挂接确认
     *
     * @param
     * @return
     */
    Boolean hangingConnect(List<String> ids);


    String edit(ConnectedMilestonesDTO dto) throws Exception;

}
