package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.lifecycle.ProjectLifeCycleNodeDTO;
import com.chinasie.orion.domain.vo.lifecycle.ProjectLifeCycleVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.operatelog.dict.OperateTypeDict;
import com.chinasie.orion.operatelog.dict.VariableDict;
import com.chinasie.orion.service.ProjectLifeCycleNodeService;
import com.chinasie.orion.service.ProjectService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 项目全生命周期Controller.
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/project-life-cycle")
@Api(tags = "项目全生命周期")
public class ProjectLifeCycleNodeController {
    private final Logger logger = LoggerFactory.getLogger(ProjectLifeCycleNodeController.class);

    @Autowired
    private ProjectLifeCycleNodeService projectLifeCycleNodeService;

    /**
     * 修改节点的内容、附件等属性.
     *
     * @param lifeCycleNode 生命周期节点
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "修改节点的内容、附件等属性.")
    @PutMapping("/node")
    @LogRecord(success = "【{USER{#logUserId}}】修改了节点【{{#projectLifeCycleNode.id}}】的内容、附件等属性", type = "项目全生命周期", subType = "修改节点", bizNo = "{{#projectLifeCycleNode.id}}")
    public ResponseDTO<String> updateProjectLifeCycleNode(@RequestBody ProjectLifeCycleNodeDTO lifeCycleNode) throws Exception {
        projectLifeCycleNodeService.updateProjectLifeCycleNode(lifeCycleNode);
        return new ResponseDTO<>();
    }

    /**
     * 列出所有类型的生命周期流程中的所有 Activity 节点.
     *
     * @param projectId 项目ID
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取项目的全生命周期节点")
    @GetMapping("/{projectId}")
    @LogRecord(
            success = "【{USER{#logUserId}}】在【项目管理-项目详情】获取项目【{FILED_VALUE{#projectId}}】【项目生命周期】节点列表成功",
            fail = "【{USER{#logUserId}}】在【项目管理-项目详情】获取项目【{FILED_VALUE{#projectId}}】【项目生命周期】节点列表失败,失败原因：【{{#_errorMsg}}】",
            type = "项目管理", subType = "项目详情", bizNo = "", extra = OperateTypeDict.GET)
    public ResponseDTO<ProjectLifeCycleVO> getProjectLifeCycleByProjectId(@PathVariable("projectId") String projectId) throws Exception {
        LogRecordContext.putVariable(VariableDict.SERVER_NAME, ProjectService.class);
        return new ResponseDTO<>(projectLifeCycleNodeService.getProjectLifeCycleByProjectId(projectId), "项目全生命周期");
    }

    //
//    /**
//     * 根据nodeKey获取节点信息.
//     *
//     * @param projectId
//     * @param nodeKey
//     * @return
//     * @throws Exception
//     */
//    @ApiOperation(value = "根据nodeKey获取节点信息")
//    @GetMapping("/{projectId}/{nodeKey}")
//    public ResponseDTO<ProjectLifeCycleNodeDTO> findNodeByKey(@PathVariable("projectId") String projectId, @PathVariable(value = "nodeKey") String nodeKey) throws Exception {
//        return new ResponseDTO<>(projectLifeCycleService.queryNodeInfo(nodeKey));
//    }
//
//    /**
//     * 修改节点的内容、附件等属性.
//     *
//     * @param projectId
//     * @param nodeKey
//     * @return
//     * @throws Exception
//     */
//    @ApiOperation(value = "修改节点的内容、附件等属性.")
//    @PutMapping("/{projectId}/{nodekKey}")
//    public ResponseDTO<ProjectLifeCycleNodeDTO> updateNode(@PathVariable("projectId") String projectId, @PathVariable("nodekKey") String nodeKey,
//                                         @RequestBody ProjectLifeCycleNodeDTO projectNodeUpdateDTO) throws Exception {
//        projectNodeUpdateDTO.setNodeKey(nodeKey);
//        Boolean ret = projectLifeCycleService.updateNode(nodeKey, projectNodeUpdateDTO);
//        if (Objects.equals(Boolean.TRUE, ret)) {
//            return new ResponseDTO<>(projectLifeCycleService.queryNodeInfo(nodeKey));
//        } else {
//            return new ResponseDTO<>(PMSErrorCode.KMS_NOT_DATA_ERROR.getErrorCode(), "更新失败");
//        }
//    }
//
//    /**
//     * 创建项目生命周期流程节点信息.
//     *
//     * @param projectNodeUpdateDTO
//     * @return
//     * @throws Exception
//     */
//    @ApiOperation(value = "创建项目生命周期流程节点信息")
//    @PostMapping("")
//    public ResponseDTO<String> createNode(@RequestBody ProjectLifeCycleNodeDTO projectNodeUpdateDTO) throws Exception {
//        projectLifeCycleService.createNode(projectNodeUpdateDTO);
//        return new ResponseDTO<>("SUCCESS");
//    }
//
//    @ApiOperation(value = "刷新项目生命周期流程节点缓存数据")
//    @PutMapping("/refresh")
//    public ResponseDTO<String> refreshCache() throws Exception {
//        projectLifeCycleService.refreshCache();
//        return new ResponseDTO<>("SUCCESS");
//    }
}
