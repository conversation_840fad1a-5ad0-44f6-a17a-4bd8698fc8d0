package com.chinasie.orion.controller;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.domain.dto.SupplierInfoDTO;
import com.chinasie.orion.domain.vo.SupplierInfoVO;
import com.chinasie.orion.service.SupplierInfoService;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * SupplierInfo 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@RestController
@RequestMapping("/supplierInfo")
@Api(tags = "供应商管理")
public class SupplierInfoController {

    @Autowired
    private SupplierInfoService supplierInfoService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据",type = "供应商管理", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<SupplierInfoVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        SupplierInfoVO rsp = supplierInfoService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param supplierInfoDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#supplierInfoDTO.name}}】",type = "供应商管理", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody SupplierInfoDTO supplierInfoDTO) throws Exception {
        String rsp = supplierInfoService.create(supplierInfoDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param supplierInfoDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#supplierInfoDTO.name}}】",type = "供应商管理", subType = "编辑", bizNo = "{{#supplierInfoDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody SupplierInfoDTO supplierInfoDTO) throws Exception {
        Boolean rsp = supplierInfoService.edit(supplierInfoDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据",type = "供应商管理", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = supplierInfoService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据",type = "供应商管理", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = supplierInfoService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据",type = "供应商管理", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<SupplierInfoVO>> pages(@RequestBody Page<SupplierInfoDTO> pageRequest) throws Exception {
        Page<SupplierInfoVO> rsp = supplierInfoService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 查询总数及年度数量
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "查询总数及年度数量")
    @LogRecord(success = "【{USER{#logUserId}}】查询总数及年度数量",type = "供应商管理", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/getNum", method = RequestMethod.POST)
    public ResponseDTO<Map<String,Object>> getNum(@RequestBody Page<SupplierInfoDTO> pageRequest) throws Exception {
        Map<String,Object> map = supplierInfoService.getNum(pageRequest);
        return new ResponseDTO<>(map);
    }

    @ApiOperation("供应商管理导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板",type = "供应商管理", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        supplierInfoService.downloadExcelTpl(response);
    }

    @ApiOperation("供应商管理导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入",type = "供应商管理", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = supplierInfoService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("供应商管理导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入",type = "供应商管理", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = supplierInfoService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消供应商管理导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入",type = "供应商管理", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = supplierInfoService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("供应商管理导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据",type = "供应商管理", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody Page<SupplierInfoDTO> pageRequest, HttpServletResponse response) throws Exception {
        supplierInfoService.exportByExcel(pageRequest, response);
    }


    /**
     * 苏州院合格供应商分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "苏州院合格供应商分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】苏州院合格供应商分页",type = "供应商管理", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/park/page", method = RequestMethod.POST)
    public ResponseDTO<Page<SupplierInfoVO>> parkPages(@RequestBody Page<SupplierInfoDTO> pageRequest) throws Exception {
        Page<SupplierInfoVO> rsp = supplierInfoService.parkPages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("苏州院合格供应商导出（Excel）")
    @PostMapping(value = "/park/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "SupplierInfo", subType = "导出数据", bizNo = "")
    public void parkExportByExcel(@RequestBody Page<SupplierInfoDTO> pageRequest, HttpServletResponse response) throws Exception {
        supplierInfoService.parkExportByExcel(pageRequest, response);
    }


    /**
     * 其他合格供应商分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "其他合格供应商")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】其他合格供应商",type = "供应商管理", subType = "其他合格供应商", bizNo = "")
    @RequestMapping(value = "/other/page", method = RequestMethod.POST)
    public ResponseDTO<Page<SupplierInfoVO>> otherPages(@RequestBody Page<SupplierInfoDTO> pageRequest) throws Exception {
        Page<SupplierInfoVO> rsp = supplierInfoService.otherPages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("其他合格供应商导出（Excel）")
    @PostMapping(value = "/other/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "SupplierInfo", subType = "导出数据", bizNo = "")
    public void otherExportByExcel(@RequestBody Page<SupplierInfoDTO> pageRequest, HttpServletResponse response) throws Exception {
        supplierInfoService.otherExportByExcel(pageRequest, response);
    }




    /**
     * 潜在合格供应商分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "潜在合格供应商")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】潜在合格供应商",type = "供应商管理", subType = "潜在合格供应商", bizNo = "")
    @RequestMapping(value = "/latent/page", method = RequestMethod.POST)
    public ResponseDTO<Page<SupplierInfoVO>> latentPages(@RequestBody Page<SupplierInfoDTO> pageRequest) throws Exception {
        Page<SupplierInfoVO> rsp = supplierInfoService.latentPages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("潜在合格供应商导出（Excel）")
    @PostMapping(value = "/latent/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "SupplierInfo", subType = "导出数据", bizNo = "")
    public void latentExportByExcel(@RequestBody Page<SupplierInfoDTO> pageRequest, HttpServletResponse response) throws Exception {
        supplierInfoService.latentExportByExcel(pageRequest, response);
    }
}
