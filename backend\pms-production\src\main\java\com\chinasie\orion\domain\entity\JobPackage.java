package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * JobPackage Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-09 15:47:59
 */
@TableName(value = "pmsx_job_package")
@ApiModel(value = "JobPackageEntity对象", description = "作业工作包信息")
@Data

public class JobPackage extends  ObjectEntity  implements Serializable{

    /**
     * 作业id
     */
    @ApiModelProperty(value = "作业id")
    @TableField(value = "job_id")
    private String jobId;

    /**
     * 功能位置
     */
    @ApiModelProperty(value = "功能位置")
    @TableField(value = "functional_location")
    private String functionalLocation;

    /**
     * 设备/系统
     */
    @ApiModelProperty(value = "设备/系统")
    @TableField(value = "equipment_system")
    private String equipmentSystem;

}
