package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@ApiModel(value = "ProjectOrderStatusDTO对象", description = "商城子订单订单状态数量")
@Data
@ExcelIgnoreUnannotated
public class ProjectOrderStatusDTO {

    @ApiModelProperty(value = "名称")
    private String label;

    @ApiModelProperty(value = "值")
    private int value;

    @ApiModelProperty(value = "数量")
    private int count;


}



