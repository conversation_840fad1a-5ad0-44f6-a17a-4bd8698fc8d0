package com.chinasie.orion.service;

import com.chinasie.orion.domain.entity.PlanToType;
import com.chinasie.orion.mybatis.service.OrionBaseService;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/17/17:39
 * @description:
 */
public interface PlanToTypeService extends OrionBaseService<PlanToType> {
    /**
     * 通过参数新增
     * @param id
     * @param planTypeId
     * @return
     * @throws Exception
     */
    PlanToType saveParam(String id, String planTypeId) throws Exception;
}
