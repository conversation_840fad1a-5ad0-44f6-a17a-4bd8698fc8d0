package com.chinasie.orion.service;

import java.lang.String;
import java.util.List;


import com.chinasie.orion.domain.dto.LaborCostAcceptanceAssessmentStandardDTO;
import com.chinasie.orion.domain.entity.LaborCostAcceptanceAssessmentStandard;
import com.chinasie.orion.domain.vo.ContractAssessmentStandardVO;
import com.chinasie.orion.domain.vo.LaborCostAcceptanceAssessmentStandardVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;
import java.lang.String;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * LaborCostAcceptanceAssessmentStandard 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-29 19:13:30
 */
public interface LaborCostAcceptanceAssessmentStandardService  extends  OrionBaseService<LaborCostAcceptanceAssessmentStandard>  {


    /**
     *  详情
     *
     * * @param id
     */
    LaborCostAcceptanceAssessmentStandardVO detail(String id, String pageCode)throws Exception;

    /**
     *  详情
     *
     * * @param id
     */
    List<ContractAssessmentStandardVO> byAcceptanceId(String acceptanceId)throws Exception;

    /**
     *  新增
     *
     * * @param laborCostAcceptanceAssessmentStandardDTO
     */
    String create(LaborCostAcceptanceAssessmentStandardDTO laborCostAcceptanceAssessmentStandardDTO)throws Exception;

    boolean insertByAcceptanceId(List<String> ids, String acceptanceId) throws Exception;

    boolean removeByAcceptanceId(List<String> ids, String acceptanceId) throws Exception;

    /**
     *  编辑
     *
     * * @param laborCostAcceptanceAssessmentStandardDTO
     */
    Boolean edit(LaborCostAcceptanceAssessmentStandardDTO laborCostAcceptanceAssessmentStandardDTO)throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<LaborCostAcceptanceAssessmentStandardVO> pages( Page<LaborCostAcceptanceAssessmentStandardDTO> pageRequest)throws Exception;

    /**
     *  下载模板
     *
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response)throws Exception;

    /**
     *  导入校验
     *
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


    /**
     *  确认导入
     *
     * * @param importId
     */
    Boolean importByExcel(String importId)throws Exception;

    /**
     *  取消导入
     *
     * * @param importId
     */
    Boolean importCancelByExcel(String importId)throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

    /**
     *  设置各种名称
     *
     * * @param searchConditions
     * * @param response
     */
    void  setEveryName(List<LaborCostAcceptanceAssessmentStandardVO> vos)throws Exception;
}
