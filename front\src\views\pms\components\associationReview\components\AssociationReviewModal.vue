<template>
  <div class="table-content">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      @selectionChange="selectionChange"
    />
  </div>
</template>

<script lang="ts" setup>
import { h, ref, Ref } from 'vue';
import {
  BasicButton,
  Layout,
  OrionTable, DataStatusTag, isPower,
} from 'lyra-component-vue3';
import { stampDate } from '/@/utils/dateUtil';
import Api from '/@/api';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

const props = withDefaults(defineProps<{
    formId:string,
    selectType:string,
    addReviewTableApi:any
    projectId:string
}>(), {
  formId: '',
  projectId: '',
  selectType: 'check',
  addReviewTableApi: null,
});
const selectRowKeys:Ref<string[]> = ref([]);
function selectionChange(data) {
  selectRowKeys.value = data.keys;
}
const tableRef = ref();
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: {
    type: props.selectType,
  },
  showSmallSearch: true,
  smallSearchField: ['name'],
  api: (params) => {
    params.query = {
      projectId: props.projectId,
    };
    return new Api('/pms').fetch(params, 'review/page', 'POST');
  },
  columns: [
    {
      title: '评审编码',
      dataIndex: 'number',
    },
    {
      title: '评审名称',
      dataIndex: 'name',
    },
    {
      title: '评审状态',
      dataIndex: 'status',
      slots: { customRender: 'status' },
    },
    {
      title: '评审类型',
      dataIndex: 'reviewTypeName',
    },
    {
      title: '任务计划',
      dataIndex: 'planName',
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      type: 'dateTime',
    },
  ],
  //  beforeFetch,
});
async function saveData() {
  const selectRows = tableRef.value.getSelectRows();
  if (selectRows.length === 0) {
    message.warning('请选评审单');
    return Promise.reject('');
  }
  await props.addReviewTableApi(selectRows);

  message.success('关联问题成功');
}
function getSelectData() {
  return tableRef.value.getSelectRows();
}
defineExpose({
  saveData,
});
</script>

<style lang="less" scoped>
.table-content{
  height: 100%;
  overflow: hidden;
}
</style>
