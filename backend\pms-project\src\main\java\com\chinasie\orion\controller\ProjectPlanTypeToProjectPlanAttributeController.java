package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ProjectPlanTypeAttributeDTO;
import com.chinasie.orion.domain.vo.ProjectPlanTypeAttributeVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.operatelog.dict.OperateTypeDict;
import com.chinasie.orion.service.ProjectPlanTypeAttributeService;
import com.chinasie.orion.service.ProjectPlanTypeToProjectPlanAttributeService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * RiskTypeToRiskTypeAttribute 前端控制器
 * </p>
 *
 * <AUTHOR> sie
 * @since 2022-10-09
 */
@RestController
@RequestMapping("/projectPlan-type-to-risk-type-attribute")
@Api(tags = "项目计划类型与项目计划类型属性的关系")
public class ProjectPlanTypeToProjectPlanAttributeController {

    @Resource
    private ProjectPlanTypeToProjectPlanAttributeService projectPlanTypeToProjectPlanAttributeService;

    @Resource
    private ProjectPlanTypeAttributeService projectPlanTypeAttributeService;

    /**
     * 类型属性列表
     * @param questionTypeId
     * @return
     * @throws Exception
     */
    @ApiOperation("类型属性列表")
    @GetMapping(value = "/list/{questionTypeId}")
    @LogRecord(
            success = "【{USER{#logUserId}}】在【项目计划类型-类型属性】获取项目计划类型【{FILED_VALUE{#questionTypeId}}】相关的类型属性列表",
            fail = "【{USER{#logUserId}}】在【项目计划类型-类型属性】获取项目计划类型【{FILED_VALUE{#questionTypeId}}】相关的类型属性列表，失败原因：「{{#_errorMsg}}」",
            type = "项目计划类型与项目计划类型属性的关系", subType = "类型属性列表", bizNo = "{{#questionTypeId}}",extra = OperateTypeDict.GET)
    public ResponseDTO<List<ProjectPlanTypeAttributeVO>> list(@PathVariable("questionTypeId") String questionTypeId) throws Exception {

        return new ResponseDTO<>(projectPlanTypeToProjectPlanAttributeService.list(questionTypeId));
    }

    /**
     * 添加类型属性
     * @param
     * @return
     * @throws Exception
     */
    @ApiOperation("添加类型属性")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "typeAttributeDTO", dataType = "TypeAttributeDTO")
    })
    @PostMapping(value = "")
    @LogRecord(
            success = "【{USER{#logUserId}}】在【项目计划类型-类型属性】对项目计划类型【{FILED_VALUE{#typeAttributeDTO.typeId}}】添加类型属性【{{#typeAttributeDTO.name}}】",
            fail = "【{USER{#logUserId}}】在【项目计划类型-类型属性】对项目计划类型【{FILED_VALUE{#typeAttributeDTO.typeId}}】添加类型属性【{{#typeAttributeDTO.name}}】，失败原因：「{{#_errorMsg}}」",
            type = "项目计划类型与项目计划类型属性的关系", subType = "添加类型属性", bizNo = "",extra = OperateTypeDict.SAVE)
    public ResponseDTO<String> add(@RequestBody ProjectPlanTypeAttributeDTO projectPlanTypeAttributeDTO) throws Exception {
        return new ResponseDTO<>(projectPlanTypeToProjectPlanAttributeService.add(projectPlanTypeAttributeDTO));
    }

    /**
     * 编辑类型属性
     * @param
     * @return
     * @throws Exception
     */
    @ApiOperation("编辑类型属性")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "typeAttributeDTO", dataType = "TypeAttributeDTO")
    })
    @PutMapping(value = "")
    @LogRecord(
            success = "【{USER{#logUserId}}】在【项目计划类型-类型属性】编辑类型属性【{FILED_VALUE{#typeAttributeDTO.id}}】",
            fail = "【{USER{#logUserId}}】在【项目计划类型-类型属性】编辑类型属性【{FILED_VALUE{#typeAttributeDTO.id}}】，失败原因：「{{#_errorMsg}}」",
            type = "项目计划类型与项目计划类型属性的关系", subType = "编辑类型属性", bizNo = "{{#typeAttributeDTO.id}}",extra = OperateTypeDict.UPDATE)
    public ResponseDTO<Boolean> edit(@RequestBody  ProjectPlanTypeAttributeDTO projectPlanTypeAttributeDTO) throws Exception {
        return new ResponseDTO<>(projectPlanTypeAttributeService.edit(projectPlanTypeAttributeDTO));
    }

    /**
     * 移除类型属性
     * @param relationIds
     * @return
     * @throws Exception
     */
    @ApiOperation("移除类型属性")
    @DeleteMapping(value = "")
    @LogRecord(
            success = "【{USER{#logUserId}}】在【项目计划类型-类型属性】批量移除类型属性",
            fail = "【{USER{#logUserId}}】在【项目计划类型-类型属性】批量移除类型属性，失败原因：「{{#_errorMsg}}」",
            type = "项目计划类型与项目计划类型属性的关系", subType = "移除类型属性", bizNo = "{{#typeAttributeDTO.id}}",extra = OperateTypeDict.UPDATE)
    public ResponseDTO<Boolean> remove(@RequestBody List<String> relationIds) throws Exception {
        return new ResponseDTO<>(projectPlanTypeToProjectPlanAttributeService.remove(relationIds));
    }
}
