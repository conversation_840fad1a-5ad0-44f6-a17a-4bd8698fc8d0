package com.chinasie.orion.domain.vo.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 大修管理 - 人员入场离场信息导出 excel
 *
 * <AUTHOR>
 * @since 2024年9月27日
 */
@ApiModel(value = "SchemeToPersonExportExcel", description = "大修管理 - 人员入场离场信息导出 excel")
@Data
@ExcelIgnoreUnannotated
public class SchemeToPersonExportExcel implements Serializable {

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "人员ID： 人员管理ID")
    private String personId;

    @ApiModelProperty(value = "用户工号")
    @ExcelProperty("员工号")
    private String userCode;

    @ApiModelProperty(value = "用户名称")
    @ExcelProperty("姓名")
    private String userName;

    @ApiModelProperty(value = "基地编码")
    private String baseCode;

    @ApiModelProperty(value = "人员性别")
    @ExcelProperty("性别")
    private String sex;

    @ExcelProperty("人员性质")
    @ApiModelProperty(value = "人员性质")
    private String personnelNature;

    @ExcelProperty(value = "是否常驻", converter = ExcelYesOrNoConverter.class)
    @ApiModelProperty(value = "是否基地常驻")
    private Boolean isBasePermanent;

    @ExcelProperty(value = "参修类型", converter = HaveProjectConverter.class)
    @ApiModelProperty(value = "有项目、无项目")
    private Boolean isHaveProject;

    @ApiModelProperty(value = "公司编号")
    private String companyCode;

    @ExcelProperty("公司")
    @ApiModelProperty(value = "公司")
    private String companyName;

    @ApiModelProperty(value = "部门编号")
    private String deptCode;

    @ExcelProperty("部门")
    @ApiModelProperty(value = "部门")
    private String deptName;

    @ApiModelProperty(value = "研究所编号")
    private String instituteCode;

    @ExcelProperty("研究所")
    @ApiModelProperty(value = "研究所")
    private String instituteName;

    @ExcelProperty("现任职务")
    @ApiModelProperty(value = "现任职务")
    private String nowPosition;

    @ExcelProperty("政治面貌")
    @ApiModelProperty(value = "政治面貌")
    private String politicalAffiliation;

    @DateTimeFormat("yyyy-MM-dd")
    @ColumnWidth(15)
    @ExcelProperty("计划入场日期")
    @ApiModelProperty(value = "计划入场日期")
    private Date inDate;

    @DateTimeFormat("yyyy-MM-dd")
    @ColumnWidth(15)
    @ExcelProperty("计划离场日期")
    @ApiModelProperty(value = "计划离场日期")
    private Date outDate;

    @DateTimeFormat("yyyy-MM-dd")
    @ColumnWidth(15)
    @ExcelProperty("实际入场日期")
    @ApiModelProperty(value = "实际入场日期")
    private Date actInDate;

    @DateTimeFormat("yyyy-MM-dd")
    @ColumnWidth(15)
    @ExcelProperty("实际离场日期")
    @ApiModelProperty(value = "实际离场日期")
    private Date actOutDate;

    @ExcelProperty(value = "是否新人", converter = ExcelYesOrNoConverter.class)
    @ApiModelProperty(value = "新人")
    private Boolean newcomer;

    @ExcelProperty("接口人")
    @ApiModelProperty(value = "接口人名称")
    private String contactUserName;

    @ExcelProperty(value = "是否入场", converter = ExcelYesOrNoConverter.class)
    @ApiModelProperty(value = "是否入场,根据是否有实际入场时间判断")
    private Boolean hadStationed;

    @ExcelProperty("进场倒计时（天）")
    @ApiModelProperty(value = "进场倒计时（天）")
    private long inDays;

    @ExcelProperty("离开场倒计时（天）")
    @ApiModelProperty(value = "离开场倒计时（天）")
    private long outDays;

    @ExcelProperty("参与作业数")
    @ApiModelProperty(value = "参与作业数")
    private Integer jobNum;

    @ExcelProperty(value = "涉及控制区作业", converter = ExcelYesOrNoConverter.class)
    @ApiModelProperty(value = "涉及控制区作业: 是/否 true/false")
    private Boolean designCtrlZoneOp;

    @ExcelProperty("身高/厘米")
    @ApiModelProperty(value = "身高/厘米")
    private String heightStr;

    @ExcelProperty("体重/千克")
    @ApiModelProperty(value = "体重/千克")
    private String weightStr;

    @ExcelProperty(value = "化学品/毒物使用或接触作业", converter = ExcelYesOrNoConverter.class)
    @ApiModelProperty(value = "化学品/毒物使用或接触作业：是/否 true/false")
    private Boolean chemicalToxinUseJob;

    @ExcelProperty(value = "一年内参与过集团内大修", converter = ExcelYesOrNoConverter.class)
    @ApiModelProperty(value = "一年内参与过集团内大修，码值：是、否")
    private Boolean isJoinYearMajorRepair;

    @ExcelProperty(value = "高剂量人员", converter = ExcelYesOrNoConverter.class)
    @ApiModelProperty(value = "高剂量人员（年个人剂量>8mSv为高剂量人员），码值：是、否")
    private Boolean isHeightMeasurePerson;

    @ApiModelProperty(value = "离厂原因")
    private String leaveReason;

    @ExcelProperty("离厂原因")
    @ApiModelProperty(value = "离厂原因描述")
    private String leaveReasonName;

    @ExcelProperty(value = "是否完成离厂交接", converter = ExcelYesOrNoConverter.class)
    @ApiModelProperty(value = "是否完成离厂交接，离场WBC测量(必要时)")
    private Boolean isFinishOutHandover;

    @ExcelProperty(value = "是否再次入场", converter = ExcelYesOrNoConverter.class)
    @ApiModelProperty(value = "是否再次入场")
    private Boolean isAgainIn;

    @ApiModelProperty(value = "进场状态")
    private Integer status;

}
