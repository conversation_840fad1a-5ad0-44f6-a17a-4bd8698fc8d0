package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.domain.dto.ProjectContributeDTO;
import com.chinasie.orion.domain.entity.ProjectContribute;
import com.chinasie.orion.domain.vo.ProjectContributeVO;
import com.chinasie.orion.repository.ProjectContributeMapper;
import com.chinasie.orion.service.ProjectContributeService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import java.util.*;
import java.util.stream.Collectors;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * ProjectContribute 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-27 19:32:36
 */
@Service
@Slf4j
public class ProjectContributeServiceImpl extends  OrionBaseServiceImpl<ProjectContributeMapper, ProjectContribute>   implements ProjectContributeService {


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  ProjectContributeVO detail(String id,String pageCode) throws Exception {
        ProjectContribute projectContribute =this.getById(id);
        ProjectContributeVO result = BeanCopyUtils.convertTo(projectContribute,ProjectContributeVO::new);

        return result;
    }


    /**
     * 新增编辑删除项目贡献情况
     * @param projectContributeList
     * @param projectId
     * @return
     * @throws Exception
     */
    @Override
    public Boolean saveOrRemove(List<ProjectContributeDTO> projectContributeList, String projectId) throws Exception {
        List<String> existIdList = this.list(new LambdaQueryWrapperX<>(ProjectContribute.class)
                .select(ProjectContribute::getId)
                .eq(ProjectContribute::getProjectId, projectId))
                .stream().map(ProjectContribute::getId).collect(Collectors.toList());
        List<ProjectContribute> saveList = projectContributeList.stream()
                .filter(f -> StrUtil.isBlank(f.getId()))
                .map(m -> {
                    m.setProjectId(projectId);
                    return BeanCopyUtils.convertTo(m, ProjectContribute::new);
                }).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(saveList)) {
            this.saveBatch(saveList);
        }
        List<ProjectContribute> updateList = projectContributeList.stream()
                .filter(f -> StrUtil.isNotBlank(f.getId()))
                .map(m -> BeanCopyUtils.convertTo(m, ProjectContribute::new)).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(updateList)) {
            this.updateBatchById(updateList);
        }
        List<String> updateIdList = projectContributeList.stream()
                .filter(f -> StrUtil.isNotBlank(f.getId()))
                .map(ProjectContributeDTO::getId).collect(Collectors.toList());;
        List<String> deleteIdList = existIdList.stream().filter(f -> !updateIdList.contains(f)).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(deleteIdList)) {
            this.removeByIds(deleteIdList);
        }
        return true;
    }

    /**
     * 获取项目贡献情况列表
     * @param projectId
     * @return
     * @throws Exception
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public List<ProjectContributeVO> getList(String projectId) throws Exception {
        List<ProjectContribute> list = this.list(new LambdaQueryWrapperX<>(ProjectContribute.class)
                .eq(ProjectContribute::getProjectId, projectId)
                .orderByDesc(ProjectContribute::getCreateTime));
        return BeanCopyUtils.convertListTo(list, ProjectContributeVO::new);
    }

}
