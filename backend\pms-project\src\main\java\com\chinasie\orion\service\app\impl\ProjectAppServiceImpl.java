package com.chinasie.orion.service.app.impl;

import cn.hutool.core.collection.CollUtil;
import com.chinasie.orion.domain.dto.ProjectDTO;
import com.chinasie.orion.domain.dto.ProjectRoleDTO;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.entity.ProjectRoleUser;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectRoleService;
import com.chinasie.orion.service.ProjectRoleUserService;
import com.chinasie.orion.service.ProjectSchemeService;
import com.chinasie.orion.service.ProjectService;
import com.chinasie.orion.service.app.ProjectAppService;
import com.mzt.logapi.context.LogRecordContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProjectAppServiceImpl implements ProjectAppService {
    @Resource
    private ProjectService projectService;
    @Resource
    private ProjectRoleService projectRoleService;
    @Resource
    private ProjectRoleUserService projectRoleUserService;
    @Resource
    private ProjectSchemeService projectSchemeService;

    @Override
    public List<ProjectRoleDTO> listProjectRole(String projectId) throws Exception {
        Project po= projectService.getById(projectId);
        if(Objects.nonNull(po)){
            LogRecordContext.putVariable("projectNumber", po.getNumber());
        }
        List<ProjectRoleDTO> projectRoleList = projectRoleService.getProjectRoleList(projectId);
        if (CollUtil.isEmpty(projectRoleList)) {
            return projectRoleList;
        }
        List<ProjectRoleUser> list = projectRoleUserService.list(new LambdaQueryWrapperX<>(ProjectRoleUser.class)
                .eq(ProjectRoleUser::getProjectId, projectId));
        Map<String, List<ProjectRoleUser>> collect = list.stream()
                .filter(item -> item.getProjectRoleId() != null)
                .collect(Collectors.groupingBy(ProjectRoleUser::getProjectRoleId));
        projectRoleList.forEach(projectRoleDTO -> {
            projectRoleDTO.setUserVOList(collect.getOrDefault(projectRoleDTO.getId(), null));
        });

        return projectRoleList;
    }

    @Override
    public Page<NewProjectHomePageVO> getProjectPage(Page<ProjectDTO> pageRequest) throws Exception {
        Page<NewProjectHomePageVO> projectPage = projectService.getProjectPage(pageRequest);
        List<NewProjectHomePageVO> content = projectPage.getContent();
        if (CollUtil.isEmpty(content)) {
            return projectPage;
        }
        for (NewProjectHomePageVO vo : content) {
            List<ProjectSchemeVO> schemeVOList = projectSchemeService.getAllMilestoneByProjectId(vo.getId());
            vo.setSchemeVOS(schemeVOList);
        }
        projectPage.setContent(content);
        return projectPage;
    }

    @Override
    public NewProjectVO detail(String id) throws Exception {
        NewProjectVO singleDetail = projectService.getSingleDetail(id, null);
        if (singleDetail != null) {
            List<ProjectSchemeVO> scheme = projectSchemeService.getAllMilestoneByProjectId(id);
            singleDetail.setSchemeVOS(scheme);
        }
        return singleDetail;
    }
}
