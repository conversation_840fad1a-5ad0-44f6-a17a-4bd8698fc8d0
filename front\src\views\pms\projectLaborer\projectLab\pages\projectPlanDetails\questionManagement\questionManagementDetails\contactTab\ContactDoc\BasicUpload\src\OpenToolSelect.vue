<template>
  <ASelect
    class="select-main"
    placeholder="请选择打开工具"
    v-bind="$attrs"
  >
    <ASelectOption
      v-for="(item, index) in OpenTool"
      :key="index"
      :value="item.fileToolId"
    >
      {{
        item.fileToolValue
      }}
    </ASelectOption>
  </ASelect>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
// import { OpenTool } from './enum';
import { Select } from 'ant-design-vue';
import Api from '/@/api';
export default defineComponent({
  name: 'OpenToolSelect',
  components: {
    ASelect: Select,
    ASelectOption: Select.Option,
  },
  props: {
    accept: {
      type: String,
      default: '',
    },
  },
  setup(props, context) {
    const { accept } = props;
    const OpenTool = ref([]);

    if (accept) {
      new Api('/res/file-postfix/file-suffix-tool').fetch({}, accept, 'GET').then((data) => {
        OpenTool.value = data;
        data && data[0]?.fileToolId && context.emit('update:value', data[0].fileToolId);
      });
    }
    return {
      OpenTool,
    };
  },
});
</script>

<style scoped lang="less">
  .select-main {
    width: 150px;
  }
</style>
