package com.chinasie.orion.controller;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.service.QuestionToRiskService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2023/03/01/15:03
 * @description:
 */
@RestController
@RequestMapping("/question-to-risk")
@Api(tags = "问题和风险关系")
public class QuestionToRiskController {

    @Resource
    private QuestionToRiskService questionToRiskService;

    @ApiOperation("通过问题ID获取关联的分析ID列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "questionId", dataType = "String")
    })
    @PostMapping(value = "/listByQuestionId/{questionId}")
    @LogRecord(success = "【{USER{#logUserId}}】通过问题ID获取关联的分析ID列表", type = "问题和风险关系", subType = "通过问题ID获取关联的分析ID列表", bizNo = "{{#questionId}}")
    public ResponseDTO<List<String>> getListByQuestionId(@PathVariable("questionId") String questionId) throws Exception {
        return new ResponseDTO<>(questionToRiskService.getListByQuestionId(questionId));
    }

    @ApiOperation("通过风险ID获取关联的问题ID列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "riskId", dataType = "String")
    })
    @PostMapping(value = "/listByRiskId/{riskId}")
    @LogRecord(success = "【{USER{#logUserId}}】通过风险ID获取关联的问题ID列表", type = "问题和风险关系", subType = "通过风险ID获取关联的问题ID列表", bizNo = "{{#riskId}}")
    public ResponseDTO<List<String>> getListByRiskId(@PathVariable("riskId") String riskId) throws Exception {
        return new ResponseDTO<>(questionToRiskService.getListByRiskId(riskId));
    }
}
