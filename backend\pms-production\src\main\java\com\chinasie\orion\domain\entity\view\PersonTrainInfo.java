package com.chinasie.orion.domain.entity.view;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/09/18:50
 * @description:
 */
@Data
@TableName(value = "pms_person_train_info")
public class PersonTrainInfo implements Serializable {


    /**
     * `pp`.`user_code` AS `user_code`,
     * 	`pp`.`id` AS `id`,
     * 	`pp`.`full_name` AS `full_name`,
     * 	`pp`.`is_equivalent` AS `is_equivalent`,
     * 	`pp`.`train_center_id` AS `train_center_id`,
     * 	`pc`.`attend_center_name` AS `attend_center_name`,
     * 	`pc`.`end_date` AS `end_date`,
     * 	`pc`.`expire_time` AS `expire_time`,
     * 	`pc`.`expiration_month` AS `expiration_month`,
     * 	`pm`.`base_code` AS `base_code`,
     * 	`pm`.`base_name` AS `base_name`,
     * 	`pm`.`train_number` AS `train_number`,
     * 	`pm`.`content` AS `content`,
     * 	`pm`.`is_check` AS `is_check`,
     * 	`pm`.`status` AS `status`
     */

    /**
     * 员工号
     */
    @ApiModelProperty(value = "员工号")
    @TableField(value = "user_code")
    private String userCode;

    @TableField(value = "id")
    private String id;
    @ApiModelProperty(value = "姓名")
    @TableField(value = "full_name")
    private String fullName;

    @ApiModelProperty(value = "是否等效")
    @TableField(value = "is_equivalent")
    private Boolean isEquivalent;

    @ApiModelProperty(value = "参培中心关联表Id")
    @TableField(value = "train_center_id")
    private String trainCenterId;


    @ApiModelProperty(value = "参培中心名称")
    @TableField(value = "attend_center_name")
    private String attendCenterName;

    /**
     * 授权到期日期
     */
    @ApiModelProperty(value = "授权到期日期")
    @TableField(value = "end_date")
    private Date endDate;

    @ApiModelProperty(value = "到期时间")
    @TableField(value = "expire_time")
    private Date expireTime;

    @ApiModelProperty(value = "有效期限（月）")
    @TableField(value = "expiration_month")
    private Integer expirationMonth;

    /**
     * 物质所在基地编码
     */
    @ApiModelProperty(value = "物质所在基地编码")
    @TableField(value = "base_code")
    private String baseCode;

    /**
     * 物质所在基地名称
     */
    @ApiModelProperty(value = "物质所在基地名称")
    @TableField(value = "base_name")
    private String baseName;

    /**
     * 应通过培训number
     */
    @ApiModelProperty(value = "应通过培训number")
    @TableField(value = "train_number")
    private String trainNumber;
    /**
     * 应通过培训number
     */
    @ApiModelProperty(value = "应通过培训名称")
    @TableField(value = "train_name")
    private String trainName;

    /**
     * 培训内容
     */
    @ApiModelProperty(value = "培训内容")
    @TableField(value = "content")
    private String content;


    /**
     * 是否考核
     */
    @ApiModelProperty(value = "是否考核")
    @TableField(value = "is_check")
    private Boolean isCheck;

    @ApiModelProperty(value = "状态")
    @TableField(value = "status")
    private Integer status;


    @ApiModelProperty(value = "状态")
    @TableField(value = "lesson_hour")
    private BigDecimal lessonHour;


    /**
     * 授权到期日期
     */
    @ApiModelProperty(value = "创建日期")
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 培训讲师
     */
    @ApiModelProperty(value = "培训讲师")
    @TableField(value = "train_lecturer")
    private String trainLecturer;

    /**
     * 培训讲师
     */
    @ApiModelProperty(value = "类型")
    @TableField(value = "class_name")
    private String className;
}
