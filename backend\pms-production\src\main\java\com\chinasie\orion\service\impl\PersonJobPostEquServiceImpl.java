package com.chinasie.orion.service.impl;





import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.domain.entity.PersonJobPostAuthorize;
import com.chinasie.orion.domain.entity.PersonJobPostEqu;
import com.chinasie.orion.domain.dto.PersonJobPostEquDTO;
import com.chinasie.orion.domain.entity.PersonTrainEquRecord;
import com.chinasie.orion.domain.vo.PersonJobPostEquVO;


import com.chinasie.orion.file.api.domain.vo.FileTreeVO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.file.api.service.FileApiService;
import com.chinasie.orion.service.PersonJobPostEquService;
import com.chinasie.orion.repository.PersonJobPostEquMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * PersonJobPostEqu 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-22 16:20:42
 */
@Service
@Slf4j
public class PersonJobPostEquServiceImpl extends  OrionBaseServiceImpl<PersonJobPostEquMapper, PersonJobPostEqu>   implements PersonJobPostEquService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    @Autowired
    private FileApiService fileApiService;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  PersonJobPostEquVO detail(String id,String pageCode) throws Exception {
        PersonJobPostEqu personJobPostEqu =this.getById(id);
        PersonJobPostEquVO result = BeanCopyUtils.convertTo(personJobPostEqu,PersonJobPostEquVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param personJobPostEquDTO
     */
    @Override
    public  String create(PersonJobPostEquDTO personJobPostEquDTO) throws Exception {
        PersonJobPostEqu personJobPostEqu =BeanCopyUtils.convertTo(personJobPostEquDTO,PersonJobPostEqu::new);
        this.save(personJobPostEqu);

        String rsp=personJobPostEqu.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param personJobPostEquDTO
     */
    @Override
    public Boolean edit(PersonJobPostEquDTO personJobPostEquDTO) throws Exception {
        PersonJobPostEqu personJobPostEqu =BeanCopyUtils.convertTo(personJobPostEquDTO,PersonJobPostEqu::new);

        this.updateById(personJobPostEqu);

        String rsp=personJobPostEqu.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<PersonJobPostEquVO> pages( Page<PersonJobPostEquDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<PersonJobPostEqu> condition = new LambdaQueryWrapperX<>( PersonJobPostEqu. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(PersonJobPostEqu::getCreateTime);


        Page<PersonJobPostEqu> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), PersonJobPostEqu::new));

        PageResult<PersonJobPostEqu> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<PersonJobPostEquVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<PersonJobPostEquVO> vos = BeanCopyUtils.convertListTo(page.getContent(), PersonJobPostEquVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "人员岗位等效记录落地导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", PersonJobPostEquDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            PersonJobPostEquExcelListener excelReadListener = new PersonJobPostEquExcelListener();
        EasyExcel.read(inputStream,PersonJobPostEquDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<PersonJobPostEquDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("人员岗位等效记录落地导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<PersonJobPostEqu> personJobPostEques =BeanCopyUtils.convertListTo(dtoS,PersonJobPostEqu::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::PersonJobPostEqu-import::id", importId, personJobPostEques, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<PersonJobPostEqu> personJobPostEques = (List<PersonJobPostEqu>) orionJ2CacheService.get("pmsx::PersonJobPostEqu-import::id", importId);
        log.info("人员岗位等效记录落地导入的入库数据={}", JSONUtil.toJsonStr(personJobPostEques));

        this.saveBatch(personJobPostEques);
        orionJ2CacheService.delete("pmsx::PersonJobPostEqu-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::PersonJobPostEqu-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<PersonJobPostEqu> condition = new LambdaQueryWrapperX<>( PersonJobPostEqu. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(PersonJobPostEqu::getCreateTime);
        List<PersonJobPostEqu> personJobPostEques =   this.list(condition);

        List<PersonJobPostEquDTO> dtos = BeanCopyUtils.convertListTo(personJobPostEques, PersonJobPostEquDTO::new);

        String fileName = "人员岗位等效记录落地数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", PersonJobPostEquDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<PersonJobPostEquVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }

    @Override
    public List<PersonJobPostEquVO> listByUserCode(String userCode) throws Exception {
        if(StrUtil.isEmpty(userCode)){
            return  new ArrayList<>();
        }
        LambdaQueryWrapperX<PersonJobPostEqu> condition = new LambdaQueryWrapperX<>( PersonJobPostEqu. class);
        condition.eq(PersonJobPostEqu::getUserCode,userCode);
        condition.select(PersonJobPostEqu::getUserCode,PersonJobPostEqu::getJobPostCode
                ,PersonJobPostEqu::getBaseCode,PersonJobPostEqu::getFormRecordId,PersonJobPostEqu::getBaseName
                ,PersonJobPostEqu::getEquivalentDate,PersonJobPostEqu::getId, PersonJobPostEqu::getSourceId);
        List<PersonJobPostEqu> list = this.list(condition);
        if(CollectionUtils.isEmpty(list)){
            return  new ArrayList<>();
        }
        List<PersonJobPostEquVO> postEquVOS = BeanCopyUtils.convertListTo(list,PersonJobPostEquVO::new);
        List<String> dataIdList= postEquVOS.stream().map(PersonJobPostEquVO::getSourceId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<String,List<FileVO>>  map = new HashMap<>();
        if(!CollectionUtils.isEmpty(dataIdList)){
            List<FileVO> filesByDataIds = fileApiService.listMaxFileByDataIds(dataIdList);
            if(!CollectionUtils.isEmpty(filesByDataIds)){
                map = filesByDataIds.stream().collect(Collectors.groupingBy(FileVO::getDataId));
            }
        }
        Map<String, List<FileVO>> finalMap = map;
        postEquVOS.forEach(item->{

            item.setFileVOList(finalMap.getOrDefault(item.getSourceId(),new ArrayList<>()));
        });
        return  postEquVOS;
    }

    @Override
    public List<PersonJobPostEquVO> listByUserCodeList(List<String> userCodeList, String baseCode) {
        LambdaQueryWrapperX<PersonJobPostEqu> condition = new LambdaQueryWrapperX<>( PersonJobPostEqu. class);
        condition.in(PersonJobPostEqu::getUserCode,userCodeList);
        condition.eq(PersonJobPostEqu::getBaseCode,baseCode);
        condition.leftJoin(PersonJobPostAuthorize.class,PersonJobPostAuthorize::getId,PersonJobPostEqu::getFormRecordId);
        condition.selectAs(PersonJobPostAuthorize::getEndDate,PersonJobPostEquVO::getEquivalentDate);
        condition.selectAs(PersonJobPostAuthorize::getBaseCode,PersonJobPostEquVO::getBaseCode);
        List<PersonJobPostEqu>  list =  this.list(condition);
        if(CollectionUtils.isEmpty(list)){
            return  new ArrayList<>();
        }
        return BeanCopyUtils.convertListTo(list,PersonJobPostEquVO::new);
    }


    public static class PersonJobPostEquExcelListener extends AnalysisEventListener<PersonJobPostEquDTO> {

        private final List<PersonJobPostEquDTO> data = new ArrayList<>();

        @Override
        public void invoke(PersonJobPostEquDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<PersonJobPostEquDTO> getData() {
            return data;
        }
    }


}
