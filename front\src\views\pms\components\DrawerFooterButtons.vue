<template>
  <div class="addModalFooter123 flex-ac">
    <div class="flex-f1">
      <ACheckbox
        v-if="isContinue"
        v-model:checked="checked"
        class="addModalFooterNext123"
      >
        继续创建下一个
      </ACheckbox>
    </div>

    <div class="btnStyle123">
      <a-button
        v-if="onClearClick"
        class="canncel123"
        size="large"
        @click="clearClick"
      >
        清空
      </a-button>
      <a-button
        class="canncel123"
        size="large"
        @click="cancelClick('close')"
      >
        取消
      </a-button>
      <a-button
        size="large"
        type="primary"
        :loading="loading"
        @click="okClick"
      >
        确认
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, unref, watch } from 'vue';
import { Checkbox as ACheckbox, Button as AButton } from 'ant-design-vue';
const props = defineProps<{
  isContinue: boolean;
  loading: boolean;
  checked: boolean;
  onClearClick?: ()=>void;
}>();

const emit = defineEmits<{
  (e:'cancelClick', type:string):void,
  (e:'okClick'):void,
  (e:'clearClick'):void,
  (e:'update:checked', status: boolean):void
}>();

const checked = ref(props.checked);
watch(() => unref(checked), (checked) => {
  emit('update:checked', checked);
});

function cancelClick(type:string) {
  emit('cancelClick', type);
}

function okClick() {
  emit('okClick');
}
function clearClick() {
  props.onClearClick && props.onClearClick();
  // emit('clearClick');
}
</script>

<style scoped lang="less">
.addModalFooter123{
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  .addModalFooterNext123{
    line-height: 40px !important;
  }
  .btnStyle123{
    .ant-btn{
      margin-left: ~`getPrefixVar('button-margin')`;
      border-radius: 4px;
      padding: 4px 30px;
    }
    .canncel123{
      color: ~`getPrefixVar('primary-color')`;
    }
  }
}
</style>
