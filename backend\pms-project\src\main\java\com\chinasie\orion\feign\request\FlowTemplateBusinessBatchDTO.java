package com.chinasie.orion.feign.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * FlowTemplateBusiness Entity对象
 *
 * <AUTHOR>
 * @since 2023-09-26 17:01:52
 */
@ApiModel(value = "FlowTemplateBusinessDTO对象", description = "流程模版与业务关联表")
@Data
public class FlowTemplateBusinessBatchDTO implements Serializable {


    /**
     * 流程模版id
     */
    @ApiModelProperty(value = "流程模版id")
    @NotBlank(message = "流程模版id不能为空")
    private String templateId;

    /**
     * 流程模版id
     */
    @ApiModelProperty(value = "租户di")
    private String orgId;

    /**
     * 业务信息
     */
    @ApiModelProperty(value = "业务信息")
    @NotEmpty(message = "业务信息不能为空")
   private List<FlowTemplateBusinessDetailBatchDTO> businessList;

}
