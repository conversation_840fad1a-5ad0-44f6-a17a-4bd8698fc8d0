package com.chinasie.orion.domain.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
/**
 * PmsProjectSchemeContent
 *
 * @author: yangFy
 * @date: 2023/4/17 17:36
 * @description: 
 * <p>
 * 项目计划记录内容entity
 * </p>
 */
@Data
@TableName(value = "pms_project_scheme_content")
@ApiModel(value = "PmsProjectSchemeContent对象", description = "项目计划记录内容")
public class ProjectSchemeContent  extends ObjectEntity implements Serializable {

  /**
   *
   */
  @ApiModelProperty(value = "项目id")
  @TableField(value = "project_id")
  private String projectId;
  /**
   *
   */
  @ApiModelProperty(value = "项目计划id")
  @TableField(value = "project_scheme_id")
  private String projectSchemeId;

  @ApiModelProperty(value = "进度")
  @TableField(value = "schedule")
  private Double schedule;
  /**
   *
   */
  @ApiModelProperty(value = "内容")
  @TableField(value = "content")
  private String content;
}
