<script setup lang="ts">
import {
  ref, Ref,
} from 'vue';
import { message } from 'ant-design-vue';
import {
  OrionTable,
  IOrionTableOptions, BasicButton,
  openModal,
} from 'lyra-component-vue3';
import { useRouter } from 'vue-router';
import Api from '/@/api';
import { DataControlFilterConfig } from '../filterIndex';

const props = defineProps<{
  record: any,
  cb?: () => void
}>();

const router = useRouter();
const loadingUnlock: Ref<boolean> = ref(false);
const loadingLock: Ref<boolean> = ref(false);
const tableRef: Ref = ref();
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const tableOptions:IOrionTableOptions = {
  rowSelection: {
    onChange(_keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  showToolButton: false,
  isSpacing: true,
  isFilter2: true,
  pagination: false,
  filterConfig: {
    fields: DataControlFilterConfig,
  },
  smallSearchField: ['t1.name'],
  api: (params: Record<string, any>) => {
    let newParams = getListParams({
      ...params,
    });
    const searchParams = newParams.searchConditions;
    return new Api('/pms').fetch(searchParams || [], `incomePlanDataControl/getList/${props?.record?.id}`, 'POST');
  },
  // 是否开启行添加，如果不开启，请自行在表单创建好后，调用表格的添加行数据方法
  isRowAdd: false,
  columns: [
    {
      dataIndex: 'lockStatusName',
      title: '锁定状态',
    },
    {
      dataIndex: 'expertiseCenterName',
      title: '专业中心',
    },
    {
      dataIndex: 'expertiseCenterMoney',
      title: '专业中心收入计划总额',
      edit: true,
      editComponent: 'Input',
      editRule: true,
      isMoney: true,
    },
  ],
  deleteToolButton: 'enable|disable',
  canResize: false,
};

// 生成 queryCondition
function getListParams(params) {
  if (params.searchConditions) {
    return {
      ...params,
      queryCondition: params.searchConditions.map((item) => ({
        column: item?.[0]?.field,
        type: 'like',
        link: 'or',
        value: item?.[0]?.values?.[0],
      })),
    };
  }
  return params;
}

function updateTable() {
  tableRef.value?.reload();
}

// 解锁参数
function paramsUnRows() {
  const selectedRowsSource = syncSelectedRows();
  return selectedRowsSource.map((item) => ({
    id: item.id,
    lockStatus: 'unlock',
    expertiseCenterName: item.expertiseCenterName,
    expertiseCenter: item.expertiseCenter,
    expertiseCenterMoney: item.expertiseCenterMoney,
    incomePlanId: item.incomePlanId,
  }));
}

// 锁定参数
function paramsRows() {
  const selectedRowsSource = syncSelectedRows();
  return selectedRowsSource.map((item) => ({
    id: item.id,
    lockStatus: 'lock',
    expertiseCenterName: item.expertiseCenterName,
    expertiseCenter: item.expertiseCenter,
    expertiseCenterMoney: item.expertiseCenterMoney,
    incomePlanId: item.incomePlanId,
  }));
}

function syncSelectedRows(): any[] {
  const arrAll = tableRef.value.getDataSource();
  const rows = selectedRows.value;

  // 创建一个映射表，以 ID 作为键
  const allById = new Map(arrAll.map((item: any) => [item.id, item]));

  // 使用 map 方法同步数据
  const syncedRows = rows.map((row: any) => {
    const updatedRow = allById.get(row.id);
    if (updatedRow && typeof updatedRow === 'object') {
      return { ...updatedRow };
    }
    return row; // 如果找不到对应的项，保持原样
  });

  return syncedRows;
}

function verification() {
  const selectedRowsSource = selectedRows.value;

  // 判断数据的 lockStatus 字段是否同时存在 unlock 和 lock 状态
  const hasUnlockAndLock = selectedRowsSource.some((item) => item.lockStatus === 'unlock')
      && selectedRowsSource.some((item) => item.lockStatus === 'lock');
  return hasUnlockAndLock;
}

// 定义 handleLocking 函数
async function handleLocking() {
  const isVerification = verification();
  if (isVerification) {
    return message.error('请选择未锁定的数据');
  }
  loadingLock.value = true;
  const params = paramsRows();
  try {
    const result = await new Api('/pms/incomePlanDataControl').fetch(params, 'lock', 'POST');
    if (result) {
      loadingLock.value = false;
      message.success('锁定数据成功');
      closeAll();
      props.cb?.();
    }
  } catch (error) {
    loadingLock.value = false;
  }
}

// 定义 handleUnlocking 函数
async function handleUnlocking() {
  const isVerification = verification();
  if (isVerification) {
    return message.error('请选择已锁定的数据');
  }
  loadingUnlock.value = true;
  const params = paramsUnRows();
  try {
    const result = await new Api('/pms/incomePlanDataControl').fetch(params, 'unLock', 'POST');
    if (result) {
      loadingUnlock.value = false;
      message.success('取消锁定成功');
      closeAll();
      props.cb?.();
    }
  } catch (error) {
    loadingUnlock.value = false;
  }
}

// 定义 handleCancel 函数
function handleCancel() {
  closeAll();
}

// 定义 closeAll 函数
function closeAll() {
  openModal.closeAll();
}

</script>

<template>
  <div class="manage-box">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    />
    <div class="document-footer">
      <div class="btnStyle">
        <BasicButton @click="handleCancel">
          取消
        </BasicButton>
        <BasicButton
          :disabled="!selectedRows.length || selectedRows && selectedRows?.[0]?.lockStatus === 'unlock'"
          type="primary"
          :loading="loadingUnlock"
          @click="handleUnlocking"
        >
          取消锁定
        </BasicButton>
        <BasicButton
          :disabled="!selectedRows.length || selectedRows && selectedRows?.[0]?.lockStatus === 'lock'"
          type="primary"
          :loading="loadingLock"
          @click="handleLocking"
        >
          锁定数据
        </BasicButton>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.manage-box{
  padding-bottom: 35px;
}
.document-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 9999;
  height: 44px;
  background: #fff;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
</style>
