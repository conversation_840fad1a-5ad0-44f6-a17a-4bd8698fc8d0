package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.BudgetExpendFormDTO;
import com.chinasie.orion.domain.vo.BudgetExpendDetailVO;
import com.chinasie.orion.domain.vo.BudgetExpendStatisticsVO;
import com.chinasie.orion.domain.vo.BudgetStatisticsVO;
import com.chinasie.orion.page.PageRequest;
import com.chinasie.orion.sdk.metadata.page.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface BudgetExpendStatisticsService {
    List<BudgetExpendStatisticsVO> getBudgetExpendStatistics(String projectId) throws Exception;

    Page<BudgetExpendDetailVO> getPage(PageRequest<BudgetExpendFormDTO> pageRequest) throws Exception;

    BudgetStatisticsVO getTotalList(String projectId) throws Exception;

    void export(String expenseSubjectId,String projectId, HttpServletResponse response) throws Exception;

    void exportAll(String projectId,HttpServletResponse response) throws Exception;
}
