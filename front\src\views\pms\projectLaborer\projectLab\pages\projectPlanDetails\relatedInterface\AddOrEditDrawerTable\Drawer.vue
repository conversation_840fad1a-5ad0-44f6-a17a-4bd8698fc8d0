<template>
  <BasicModal
    destroyOnClose
    showFooter
    :width="1000"
    :title="state.title"
    @register="modalRegister"
    @visible-change="visibleChange"
    @ok="confirm"
  >
    <ModalTable
      ref="formModalRef"
      :projectId="projectId"
    />
  </BasicModal>
</template>

<script setup lang="ts">
import {
  defineProps, reactive, defineExpose, defineEmits, ref, inject,
} from 'vue';
import { BasicModal, useModal } from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import { useRouter } from 'vue-router';
import ModalTable from './ModalTable.vue';
import Api from '/@/api';
import { useUserStore } from '/@/store/modules/user';

const props = withDefaults(defineProps<{
    formId:string,
    projectId:string,
}>(), {
  projectId: '',
  formId: '',
});
const userStore = useUserStore();
const [modalRegister, modalMethods] = useModal();
const emit = defineEmits(['update']);

function initData() {
  return {
    action: 'add',
    title: '',
    originData: {},
  };
}

const state = reactive(initData());
const formModalRef = ref(null);

function visibleChange(show) {
  !show && Object.assign(state, initData());
}

interface openModalTypes {
    action: String;// add  edit 等
    info?: any
}

function openDrawer(data: openModalTypes) {
  modalMethods.openModal(true);
  data && usualHandle(data);
  if (data.action === 'add') {
  }
}

function usualHandle(data) {
  data?.action && (state.action = data?.action);
  data?.action === 'add' && (state.title = '新增');
  data?.action === 'edit' && (state.title = '编辑');
  if (data?.info) {
    state.originData = JSON.parse(JSON.stringify(data.info));
  }
}

const router = useRouter();

async function confirm() {
  formModalRef.value && formModalRef.value.getSelectRow().length;
  modalMethods.setModalProps({ confirmLoading: true });
  try {
    const res = await goFetch();
    message.success('操作成功');
    emit('update');
    modalMethods.openModal(false);
  } catch (_) {
  } finally {
    modalMethods.setModalProps({ confirmLoading: false });
  }
}

async function goFetch() {
  const formData = formModalRef.value.getSelectRow().map((item) => item.id);
  const params = JSON.parse(JSON.stringify(formData));
  if (state.action === 'add') {
    return await new Api(`/pms/plan-to-im?planId=${props.formId}`).fetch(params, '', 'POST');
  }
}

defineExpose({
  modalMethods,
  openDrawer,
});
</script>

<style scoped lang="less">

</style>
