<template>
  <BasicDrawer
    v-model:visible="visible"
    :title="title"
    placement="right"
    width="340"
    class="searchModalDrawer pdmRightDrawer"
    :mask-closable="false"
    @close="x"
  >
    <div class="search_title mb15">
      <aInputSearch
        v-model:value="nameValue"
        placeholder="请输入内容"
        size="large"
        @search="searchData"
      />
    </div>
    <div>
      <div class="rowItem">
        <div :style="{ padding: '0 0 0 12px', color: '#666', fontSize: '12px' }">
          共搜索出{{ systemRole.size || 0 }}项结果
        </div>
        <a-menu
          v-model:selectedKeys="selectedKeys"
          :style="{
            width: '96%',
            margin: '10px',
            borderRightWidth: 0,
            overflowX: 'auto',
            height: contentHeight - 520 + 'px'
          }"
          mode="vertical"
          multiple="true"
        >
          <a-menu-item
            v-for="item in systemRole.planSearchVos"
            :key="item.id"
            :item-value="item"
            :style="{ height: '100px', borderBottom: '1px dotted #ccc', margin: 0 }"
          >
            <div
              class="avatar"
              :style="{ height: '100px' }"
            >
              <div class="left">
                <a-avatar
                  v-if="false"
                  :style="{ width: '65px', height: '65px' }"
                  shape="square"
                />
                <a-avatar
                  v-else
                  shape="square"
                  style="
                      width: 65px;
                      height: 65px;
                      text-align: center;
                      line-height: 60px;
                      font-size: 20px;
                    "
                >
                  需求
                </a-avatar>
              </div>

              <div class="right">
                <h3 class="rightitem">
                  {{ item.name }}
                </h3>
                <div class="rightitem rightitem2">
                  编号:{{ item.number }}
                </div>
                <div class="rightitem rightitem2">
                  负责人:{{ item.principalName }}
                </div>
              </div>
            </div>
          </a-menu-item>
        </a-menu>
      </div>
      <div class="nodeItemBtn">
        <a-button
          size="large"
          class="cancelBtn"
          @click="close"
        >
          取消
        </a-button>
        <a-button
          size="large"
          class="bgDC"
          type="primary"
          :loading="loading"
          @click="onSubmit"
        >
          确认
        </a-button>
      </div>
    </div>
  </BasicDrawer>
</template>
<script lang="ts">

import Api from '/@/api';
import {
  defineComponent, reactive, toRefs, ref, watch, onMounted, inject,
} from 'vue';
import {
  message, Drawer, Input, Menu, Button,
} from 'ant-design-vue';
import basicTitle from '/@/views/pms/projectLaborer/componentsList/basicTitle/index.vue';
import { planSearchListApi, addDemandContactitemApi } from '/@/views/pms/projectLaborer/api/planList';
import {
  BasicDrawer,
} from 'lyra-component-vue3';
export default defineComponent({
  components: {
    aInputSearch: Input.Search,
    AMenu: Menu,
    AMenuItem: Menu.Item,
    AButton: Button,
    BasicDrawer,
  },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
    formId: {
      type: String,
      default: '',
    },
  },
  setup(props, { emit }) {
    // projectId

    let projectId: any = inject('projectId');
    //   console.log('测试🚀🚀 ~~~ projectId', projectId.value);
    //   let provideProjectId: any = inject('provideProjectId');
    const state = reactive({
      visible: false,
      title: '添加信息',
      nameValue: '',
      selectedKeys: [],
      openKeys: [],
      systemRole: <any>{},
      loading: false,
    });
    watch(
      () => props.data,
      () => (state.visible = true),
    );
    /* 重置数据 */
    const resetData = () => {
      state.nameValue = '';
      state.selectedKeys = [];
      state.systemRole = {};
    };
      /* x按钮 */
    const x = () => resetData();
    /* 取消 */
    const close = () => {
      state.visible = false;
      resetData();
    };
      /* 模糊搜索系统角色 */
    const searchData = async (e) => {
      const params = {
        keyword: e,
        projectId: projectId.value,
      };
      await new Api('/pms/demand-management/search/list/').fetch(params, '', 'post').then((res) => {
        state.systemRole = res;
      });
    };
    const onSubmit = () => {
      // console.log(state.selectedKeys);
      if (state.selectedKeys.length === 0) {
        state.visible = false;
        return;
      }
      const systemRoleApiValue = [];
      state.selectedKeys.forEach((item) => {
        const iditem = state.systemRole.planSearchVos.filter((element) => item === element.id);
        systemRoleApiValue.push(iditem);
      });
      const keyArr = systemRoleApiValue.map((value) => value[0].id);
      const newSelectArr = JSON.parse(JSON.stringify(keyArr));
      const addAllRole = {
        toId: props.formId,
        fromIds: newSelectArr,
      };
      state.loading = true;
      new Api('/pms').fetch(addAllRole, 'projectScheme/relation/demand', 'POST').then(() => {
        state.visible = false;

        resetData();
        emit('success');
        message.success('添加成功');
        state.loading = false;
      })
        .catch(() => {
          state.loading = false;
        });
    };
    const contentHeight = ref(0);
    onMounted(() => {
      contentHeight.value = document.body.clientHeight + 250;
    });

    return {
      ...toRefs(state),
      close,
      onSubmit,
      searchData,
      x,
      contentHeight,
    };
  },
});
</script>
<style lang="less" scoped>
  @import url('/@/views/pms/projectLaborer/statics/style/addListModal.less');
</style>
