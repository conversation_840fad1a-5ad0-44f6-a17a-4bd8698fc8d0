package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;

/**
 * ProjectLifeCycle Entity对象
 *
 * <AUTHOR>
 * @since 2024-04-22 18:07:01
 */
@TableName(value = "pms_project_life_cycle_phase")
@ApiModel(value = "ProjectLifeCycleEntity对象", description = "全生命周期")
@Data
public class ProjectLifeCyclePhase extends ObjectEntity implements Serializable{

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 项目计划id（里程碑id）
     */
    @ApiModelProperty(value = "项目计划id（里程碑id）")
    @TableField(value = "project_scheme_id")
    private String projectSchemeId;

    /**
     * 全生命周期模板id
     */
    @ApiModelProperty(value = "全生命周期模板id")
    @TableField(value = "template_id")
    private String templateId;

    /**
     * 阶段描述
     */
    @ApiModelProperty(value = "阶段描述")
    @TableField(value = "phase_description")
    private String phaseDescription;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @TableField(value = "sort")
    private Integer sort;

}
