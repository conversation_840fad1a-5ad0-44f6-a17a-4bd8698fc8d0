package org.jeecg.modules.demo.dkm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.config.shiro.IgnoreAuth;
import org.jeecg.modules.demo.dkm.service.DtcPersonnelsCheckService;
import org.jeecg.modules.demo.dkm.service.DtcTecPersonnelsCheckService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description: DKM数据校验
 * @Author: tancheng
 * @Date: 2025-4-16 15:30:44
 * @Version: V1.0
 */
@Api(tags = "DKM适配器")
@RestController
@RequestMapping("/dkm")
@Slf4j
public class DkmCheckController {

    @Autowired
    private DtcPersonnelsCheckService dtcPersonnelsService;

    @Autowired
    private DtcTecPersonnelsCheckService dtcTecPersonnelsService;

    /**
     * 检查dtc_personnels表数据完整性
     * 检查员工号、员工姓名、身份证号、部门编码和部门名称是否缺失
     * @return 标准Markdown表格格式的字符串，多个表格间用空行分隔
     * 
     */
    @ApiOperation(value = "检查dtc_personnels表数据完整性", notes = "检查员工号、员工姓名、身份证号、部门编码和部门名称是否缺失，返回标准Markdown表格")
    @PostMapping("/check-personnels")
    @IgnoreAuth
    public Result<String> checkPersonnelsDataIntegrity() {
        log.info("开始检查dtc_personnels表数据完整性");
        String markdownResult = dtcPersonnelsService.checkPersonnelsDataIntegrity();
        log.info("完成dtc_personnels表数据完整性检查");
        return Result.OK(markdownResult);
    }

    /**
     * 检查dtc_tec_personnels表数据完整性
     * 检查员工号、员工姓名、身份证号、部门编码和部门名称是否缺失，检查员工号是否重复
     * @return 标准Markdown表格格式的字符串，多个表格间用空行分隔
     *
     */
    @ApiOperation(value = "检查dtc_tec_personnels表数据完整性", notes = "检查员工号、员工姓名、身份证号、部门编码和部门名称是否缺失，检查员工号是否重复，返回标准Markdown表格")
    @PostMapping("/check-tecpersonnels")
    @IgnoreAuth
    public Result<String> checkTecPersonnelsDataIntegrity() {
        log.info("开始检查dtc_tec_personnels表数据完整性");
        String markdownResult = dtcTecPersonnelsService.checkTecPersonnelsDataIntegrity();
        log.info("完成dtc_tec_personnels表数据完整性检查");
        return Result.OK(markdownResult);
    }
} 