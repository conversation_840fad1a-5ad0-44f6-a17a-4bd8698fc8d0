package com.chinasie.orion.service;





import com.chinasie.orion.domain.entity.PersonJobPostAuthorize;
import com.chinasie.orion.domain.dto.PersonJobPostAuthorizeDTO;
import com.chinasie.orion.domain.vo.PersonJobPostAuthorizeVO;
import java.lang.String;
import java.util.List;


import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import java.lang.String;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * PersonJobPostAuthorize 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-22 16:30:22
 */
public interface PersonJobPostAuthorizeService  extends  OrionBaseService<PersonJobPostAuthorize>  {


        /**
         *  详情
         *
         * * @param id
         */
    PersonJobPostAuthorizeVO detail(String id,String pageCode)throws Exception;

        /**
         *  新增
         *
         * * @param personJobPostAuthorizeDTO
         */
        String create(PersonJobPostAuthorizeDTO personJobPostAuthorizeDTO)throws Exception;

        /**
         *  编辑
         *
         * * @param personJobPostAuthorizeDTO
         */
        Boolean edit(PersonJobPostAuthorizeDTO personJobPostAuthorizeDTO)throws Exception;


        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;


        /**
         *  分页
         *
         * * @param pageRequest
         * 
         */
        Page<PersonJobPostAuthorizeVO> pages( Page<PersonJobPostAuthorizeDTO> pageRequest)throws Exception;

        /**
         *  下载模板
         *
         * * @param response
         */
        void downloadExcelTpl(HttpServletResponse response)throws Exception;

        /**
         *  导入校验
         *
         * * @param file
         */
        ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


        /**
           *  确认导入
           *
           * * @param importId
           */
        Boolean importByExcel(String importId)throws Exception;

        /**
       *  取消导入
       *
       * * @param importId
       */
        Boolean importCancelByExcel(String importId)throws Exception;

        /**
            *  导出
            *
            * * @param searchConditions
            * * @param response
            */
        void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

        /**
            *  设置各种名称
            *
            * * @param searchConditions
            * * @param response
            */
        void  setEveryName(List<PersonJobPostAuthorizeVO> vos)throws Exception;

    List<PersonJobPostAuthorize> listByUserCode(String userCode);

    List<PersonJobPostAuthorize> listByUserCodeList(List<String> userCodeList,String baseCode);

    /**
     *  人员岗位授权落地验证是否生效
     */
    void personJobPostAuthorizeVerifyEffect();

    /**
     *  人员 岗位授权落地信息列表
     * @param postAuthorizeDTO
     * @return
     */
    List<PersonJobPostAuthorizeVO> listByEntity(PersonJobPostAuthorizeDTO postAuthorizeDTO) throws Exception;
}
