package com.chinasie.orion.management.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * TechnicalConfigurationPerson Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-22 14:03:35
 */
@TableName(value = "pmsx_technical_configuration_person")
@ApiModel(value = "TechnicalConfigurationPersonEntity对象", description = "技术配置人员")
@Data

public class TechnicalConfigurationPerson extends ObjectEntity implements Serializable {

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @TableField(value = "person_name")
    private String personName;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    @TableField(value = "person_sex")
    private String personSex;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    @TableField(value = "id_card")
    private String idCard;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期")
    @TableField(value = "birth")
    private String birth;

    /**
     * 民族
     */
    @ApiModelProperty(value = "民族")
    @TableField(value = "nation")
    private String nation;

    /**
     * 婚姻状况
     */
    @ApiModelProperty(value = "婚姻状况")
    @TableField(value = "marital_status")
    private String maritalStatus;

    /**
     * 联系方式
     */
    @ApiModelProperty(value = "联系方式")
    @TableField(value = "person_tel")
    private String personTel;

    /**
     * 最高学历
     */
    @ApiModelProperty(value = "最高学历")
    @TableField(value = "education_level")
    private String educationLevel;

    /**
     * 所学专业
     */
    @ApiModelProperty(value = "所学专业")
    @TableField(value = "major")
    private String major;

    /**
     * 职称
     */
    @ApiModelProperty(value = "职称")
    @TableField(value = "person_title")
    private String personTitle;

    /**
     * 技术专业证书
     */
    @ApiModelProperty(value = "技术专业证书")
    @TableField(value = "major_certificate")
    private String majorCertificate;

    /**
     * 所在公司
     */
    @ApiModelProperty(value = "所在公司")
    @TableField(value = "person_company")
    private String personCompany;

    /**
     * 所在部门/中心
     */
    @ApiModelProperty(value = "所在部门/中心")
    @TableField(value = "person_department")
    private String personDepartment;

    /**
     * 所在研究所/专业室
     */
    @ApiModelProperty(value = "所在研究所/专业室")
    @TableField(value = "person_institute")
    private String personInstitute;

    /**
     * 分管项目经理
     */
    @ApiModelProperty(value = "分管项目经理")
    @TableField(value = "project_manager")
    private String projectManager;

    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    @TableField(value = "job_id")
    private String jobId;

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @TableField(value = "supplier_code")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @TableField(value = "supplier_name")
    private String supplierName;

    /**
     * 是否项目制人员
     */
    @ApiModelProperty(value = "是否项目制人员")
    @TableField(value = "is_project_based")
    private String isProjectBased;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_code")
    private String contractCode;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @TableField(value = "contract_name")
    private String contractName;

    /**
     * 合同级别
     */
    @ApiModelProperty(value = "合同级别")
    @TableField(value = "contract_level")
    private String contractLevel;

    /**
     * 工作内容
     */
    @ApiModelProperty(value = "工作内容")
    @TableField(value = "job_content")
    private String jobContent;

    /**
     * 常驻服务地点
     */
    @ApiModelProperty(value = "常驻服务地点")
    @TableField(value = "service_location")
    private String serviceLocation;

    /**
     * 是否从事放射性工作
     */
    @ApiModelProperty(value = "是否从事放射性工作")
    @TableField(value = "is_radioactivity_work")
    private String isRadioactivityWork;

    /**
     * 是否完成体检
     */
    @ApiModelProperty(value = "是否完成体检")
    @TableField(value = "is_completed_exam")
    private String isCompletedExam;

    /**
     * 办卡或授权
     */
    @ApiModelProperty(value = "办卡或授权")
    @TableField(value = "card_or_empower")
    private String cardOrEmpower;

    /**
     * 是否有亲属在集团内
     */
    @ApiModelProperty(value = "是否有亲属在集团内")
    @TableField(value = "have_kin_group")
    private String haveKinGroup;

    /**
     * 亲属姓名
     */
    @ApiModelProperty(value = "亲属姓名")
    @TableField(value = "kin_name")
    private String kinName;

    /**
     * 亲属职务
     */
    @ApiModelProperty(value = "亲属职务")
    @TableField(value = "kin_post")
    private String kinPost;

    /**
     * 亲属公司
     */
    @ApiModelProperty(value = "亲属公司")
    @TableField(value = "kin_company")
    private String kinCompany;

    /**
     * 是否技术配置
     */
    @ApiModelProperty(value = "是否技术配置")
    @TableField(value = "is_technicaled")
    private String isTechnicaled;

    /**
     * 人员状态（在场/离场）
     */
    @ApiModelProperty(value = "人员状态（在场/离场）")
    @TableField(value = "person_status")
    private String personStatus;

    /**
     * 预计离场时间
     */
    @ApiModelProperty(value = "预计离场时间")
    @TableField(value = "leave_time")
    private Date leaveTime;

    /**
     * 是否违反相关安全规范
     */
    @ApiModelProperty(value = "是否违反相关安全规范")
    @TableField(value = "is_safetied")
    private String isSafetied;

    /**
     * 入场时间
     */
    @ApiModelProperty(value = "入场时间")
    @TableField(value = "entry_time")
    private Date entryTime;

    /**
     * 离场时间
     */
    @ApiModelProperty(value = "离场时间")
    @TableField(value = "leaving_time")
    private Date leavingTime;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    @TableField(value = "operation_time")
    private Date operationTime;

    /**
     * 操作人id
     */
    @ApiModelProperty(value = "操作人id")
    @TableField(value = "operation_id")
    private String operationId;

    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人姓名")
    @TableField(value = "operation_name")
    private String operationName;

    /**
     * 锁定状态
     */
    @ApiModelProperty(value = "锁定状态")
    @TableField(value = "locked_state")
    private String lockedState;

}
