package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ExponseDetail Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-16 15:22:28
 */
@TableName(value = "pmsx_bud_exponse_detail")
@ApiModel(value = "ExponseDetail对象", description = "支出详情表")
@Data
public class ExponseDetail extends ObjectEntity implements Serializable{


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number" )
    private String number;

    /**
     * 成本中心ID
     */
    @ApiModelProperty(value = "成本中心ID")
    @TableField(value = "cost_center_id" )
    private String costCenterId;

    /**
     * 成本中心名字
     */
    @ApiModelProperty(value = "成本中心名字")
    @TableField(value = "cost_center_name" )
    private String costCenterName;

    /**
     * 费用科目ID
     */
    @ApiModelProperty(value = "费用科目ID")
    @TableField(value = "expense_account_id" )
    private String expenseAccountId;

    /**
     * 费用科目名字
     */
    @ApiModelProperty(value = "费用科目名字")
    @TableField(value = "expense_account_name" )
    private String expenseAccountName;

    /**
     * 支出人ID
     */
    @ApiModelProperty(value = "支出人ID")
    @TableField(value = "out_person_id" )
    private String outPersonId;

    /**
     * 支出人姓名
     */
    @ApiModelProperty(value = "支出人姓名")
    @TableField(value = "out_person_name" )
    private String outPersonName;

    /**
     * 支出金额
     */
    @ApiModelProperty(value = "支出金额")
    @TableField(value = "out_money" )
    private BigDecimal outMoney;

    /**
     * 支出时间
     */
    @ApiModelProperty(value = "支出时间")
    @TableField(value = "out_time" )
    private Date outTime;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @TableField(value = "description" )
    private String description;

    /**
     * 附件URL
     */
    @ApiModelProperty(value = "附件URL")
    @TableField(value = "tag" )
    private String tag;

    /**
     * 预算ID
     */
    @ApiModelProperty(value = "预算ID")
    @TableField(value = "budget_project_id" )
    private String budgetProjectId;

    /**
     * 预算名称
     */
    @ApiModelProperty(value = "预算名称")
    @TableField(value = "budget_project_name" )
    private String budgetProjectName;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id" )
    private String projectId;


}
