package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * SupplierProducts DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@ApiModel(value = "SupplierProductsDTO对象", description = "可提供产品")
@Data
@ExcelIgnoreUnannotated
public class SupplierProductsDTO extends ObjectDTO implements Serializable {

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @ExcelProperty(value = "供应商编码 ", index = 0)
    private String supplierCode;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 1)
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @ExcelProperty(value = "主表ID ", index = 2)
    private String mainTableId;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @ExcelProperty(value = "供应商名称 ", index = 3)
    private String supplierName;

    /**
     * 可提供产品一级
     */
    @ApiModelProperty(value = "可提供产品一级")
    @ExcelProperty(value = "可提供产品一级 ", index = 4)
    private String productLevelOne;

    /**
     * 可提供产品二级
     */
    @ApiModelProperty(value = "可提供产品二级")
    @ExcelProperty(value = "可提供产品二级 ", index = 5)
    private String productLevelTwo;

    /**
     * 可提供产品三级
     */
    @ApiModelProperty(value = "可提供产品三级")
    @ExcelProperty(value = "可提供产品三级 ", index = 6)
    private String productLevelThree;

    /**
     * 可提供产品四级
     */
    @ApiModelProperty(value = "可提供产品四级")
    @ExcelProperty(value = "可提供产品四级 ", index = 7)
    private String productLevelFour;


}
