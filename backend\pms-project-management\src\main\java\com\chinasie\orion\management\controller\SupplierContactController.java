package com.chinasie.orion.management.controller;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.dto.SupplierContactDTO;
import com.chinasie.orion.management.domain.dto.SupplierInfoDTO;
import com.chinasie.orion.management.domain.vo.SupplierContactVO;
import com.chinasie.orion.management.domain.vo.SupplierProductsVO;
import com.chinasie.orion.management.service.SupplierContactService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * SupplierContact 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@RestController
@RequestMapping("/supplierContact")
@Api(tags = "供应商联系人")
public class SupplierContactController {

    @Autowired
    private SupplierContactService supplierContactService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "供应商联系人", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<SupplierContactVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        SupplierContactVO rsp = supplierContactService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 根据采购编号查询联系人信息
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "根据采购编号查询联系人信息")
    @RequestMapping(value = "/getContactByCode", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】根据采购编号查询联系人信息", type = "供应商联系人", subType = "根据采购编号查询联系人信息", bizNo = "{{#pageRequest.params.code}}")
    public ResponseDTO<Page<SupplierContactVO>> getContactByCode(@RequestBody Page<SupplierContactDTO> pageRequest) throws Exception {
        Page<SupplierContactVO> rsp = supplierContactService.getByCode(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param supplierContactDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#supplierContactDTO.name}}】", type = "供应商联系人", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody SupplierContactDTO supplierContactDTO) throws Exception {
        String rsp = supplierContactService.create(supplierContactDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param supplierContactDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#supplierContactDTO.name}}】", type = "供应商联系人", subType = "编辑", bizNo = "{{#supplierContactDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody SupplierContactDTO supplierContactDTO) throws Exception {
        Boolean rsp = supplierContactService.edit(supplierContactDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "供应商联系人", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = supplierContactService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "供应商联系人", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = supplierContactService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "供应商联系人", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page/{mainTableId}", method = RequestMethod.POST)
    public ResponseDTO<Page<SupplierContactVO>> pages(@PathVariable("mainTableId") String mainTableId, @RequestBody Page<SupplierContactDTO> pageRequest) throws Exception {
        Page<SupplierContactVO> rsp = supplierContactService.pages(mainTableId, pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("供应商联系人导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "供应商联系人", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        supplierContactService.downloadExcelTpl(response);
    }

    @ApiOperation("供应商联系人导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "供应商联系人", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = supplierContactService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("供应商联系人导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "供应商联系人", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = supplierContactService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消供应商联系人导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "供应商联系人", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = supplierContactService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("供应商联系人导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "供应商联系人", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        supplierContactService.exportByExcel(searchConditions, response);
    }
}
