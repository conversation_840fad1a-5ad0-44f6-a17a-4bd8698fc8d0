package com.chinasie.orion.repository.reporting;

import com.chinasie.orion.domain.entity.reporting.ProjectDailyStatement;

import com.chinasie.orion.search.common.domain.IndexData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * ProjectDailyStatement Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-14 15:21:01
 */
@Mapper
public interface ProjectDailyStatementMapper extends OrionBaseMapper<ProjectDailyStatement> {

    /**
     * 获取自上次索引依赖新增的可索引的EDM数据.
     *
     * @param lastIndexTime 上次索引时间
     * @param limitSize
     * @return
     */
    List<IndexData> fetchIndexData(@Param("lastIndexTime") Date lastIndexTime, @Param("limitSize") Integer limitSize);


}

