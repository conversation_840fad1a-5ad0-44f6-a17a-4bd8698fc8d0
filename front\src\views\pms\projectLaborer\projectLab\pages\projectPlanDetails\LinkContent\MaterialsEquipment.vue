<script setup lang="ts">
import { Modal, Space } from 'ant-design-vue';
import {
  h, ref,
} from 'vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import Api from '/@/api';
import { BasicButton, OrionTable } from 'lyra-component-vue3';
import { get } from 'lodash-es';
import {
  useMaterialsEquipmentModal,
} from './hooks/useMaterialsEquipmentModal';

const props = withDefaults(defineProps<{
  parentData:Record<string, any>
}>(), {
  parentData: () => ({}),
});

const tableRef = ref();
const selectKeys = ref([]);
const tableOptions = {
  showToolButton: false,
  isSpacing: true,
  showSmallSearch: false,
  showTableSetting: false,
  rowSelection: {
    onChange(val) {
      selectKeys.value = val;
    },
  },
  columns: [
    {
      title: '资产代码',
      width: 100,
      dataIndex: 'code',
    },
    {
      title: '资产编码/条码',
      width: 190,
      dataIndex: 'number',
    },
    {
      title: '产品编码',
      dataIndex: 'productCode',
    },
    {
      title: '工具状态',
      dataIndex: 'toolStatusName',
    },
    {
      title: '检定维护周期',
      dataIndex: 'maintenanceCycle',
    },
    {
      title: '资产名称',
      width: 370,
      dataIndex: 'name',
    },
    {
      title: '数量',
      width: 100,
      dataIndex: 'numCount',
    },
    {
      title: '成本中心名称',
      width: 115,
      dataIndex: 'costCenterName',
    },
    {
      title: '规格型号',
      width: 222,
      dataIndex: 'spModel',
    },
    {
      title: '是否需要检定',
      width: 115,
      dataIndex: 'isNeedVerification',
      customRender({ text }) {
        return text ? '是' : '否';
      },
    },
    {
      title: '下次检定日期',
      width: 115,
      dataIndex: 'nextVerificationTime',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('span', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '责任人工号',
      width: 115,
      dataIndex: 'rspUserNumber',
    },
    {
      title: '责任人姓名',
      width: 110,
      dataIndex: 'rspUserName',
    },
    {
      title: '使用人工号',
      width: 110,
      dataIndex: 'useUserNumber',
    },
    {
      title: '使用人姓名',
      width: 100,
      dataIndex: 'useUserName',
    },
    {
      title: '资产存放地',
      width: 100,
      dataIndex: 'storageLocationName',
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 100,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '移除',
      onClick(record) {
        Modal.confirm({
          title: '移除警告',
          icon: h(ExclamationCircleOutlined),
          content: '确定移除这条数据',
          async onOk() {
            try {
              const targetKey = [record.id];
              return await handleRemoveRequest(targetKey);
            } catch {
            }
          },
          onCancel() {},
        });
      },
    },
  ],
  api: (params) => new Api('/pms/projectScheme/relation/fixedAsset/lists')
    .fetch(params, get(props.parentData, 'id'), 'POST'),
};

function updateTable() {
  tableRef.value?.reload?.();
}
function handleAddMaterialsEquipment() {
  useMaterialsEquipmentModal({
    ...(get(props, 'parentData') ?? {}),
    title: '添加物资设备',
  }, updateTable);
}
function handleDeleteMaterialsEquipmentRows() {
  Modal.confirm({
    title: '移除警告',
    icon: h(ExclamationCircleOutlined),
    content: '确定移除选中的数据',
    async onOk() {
      try {
        const targetKeys = tableRef.value?.getSelectRowKeys();
        return await handleRemoveRequest(targetKeys);
      } catch {
      }
    },
    onCancel() {},
  });
}
function handleRemoveRequest(idxs) {
  return new Promise((resolve) => {
    new Api('/pms/projectScheme/relation/fixedAsset/remove')
      .fetch({
        toId: get(props, 'parentData.id'),
        fromIds: idxs,
      }, '', 'DELETE')
      .then((res) => {
        updateTable();
        resolve(res);
      });
  });
}

</script>

<template>
  <div class="materials-equipment">
    <OrionTable
      ref="tableRef"
      class="scroll-table"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <Space
          :size="12"
          align="center"
        >
          <BasicButton
            type="primary"
            icon="sie-icon-tianjiaxinzeng"
            @click="handleAddMaterialsEquipment"
          >
            添加
          </BasicButton>
          <BasicButton
            :disabled="!selectKeys.length"
            icon="sie-icon-shanchu"
            @click="handleDeleteMaterialsEquipmentRows"
          >
            移除
          </BasicButton>
        </Space>
      </template>
    </OrionTable>
  </div>
</template>

<style scoped lang="less">

</style>