package com.chinasie.orion.domain.vo;


import com.chinasie.orion.sdk.domain.vo.PageContainerAuthorityVO;
import com.chinasie.orion.sdk.domain.vo.RowDataOperationAuthorityVO;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/1/6 15:07
 * @description:
 */
@Data
public class ObjectVO implements Serializable {

    /**
     * 拥有者ID
     */
    @ApiModelProperty(value = "拥有者ID")
    private String ownerId;

    /**
     * 拥有者名称
     */
    @ApiModelProperty(value = "拥有者名称")
    private String ownerName;

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private String id;

    @ApiModelProperty(value = "排序字段")
    private Long sort;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 描述/备注
     */
    @ApiModelProperty(value = "描述/备注")
    private String remark;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String statusName;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * logicStatus 逻辑删除字段
     */
    @ApiModelProperty(value = "logicStatus 逻辑删除字段")
    private Integer logicStatus;

    /**
     * 修改者ID
     */
    @ApiModelProperty(value = "修改者ID")
    private String modifyId;

    /**
     * 修改者名称
     */
    @ApiModelProperty(value = "修改者名称")
    private String modifyName;

    /**
     * 创建者id
     */
    @ApiModelProperty(value = "创建者id")
    private String creatorId;

    /**
     * 创建者名称
     */
    @ApiModelProperty(value = "创建者名称")
    private String creatorName;

    /**
     * 类名称
     */
    @ApiModelProperty(value = "类名称")
    private String className;

    /**
     * 状态对象
     */
    @ApiModelProperty(value = "状态对象")
    private DataStatusVO dataStatus;

    @ApiModelProperty("行数据权限")
    private List<RowDataOperationAuthorityVO> rdAuthList;

    @ApiModelProperty("数据详情权限（页面权限）")
    private List<PageContainerAuthorityVO> detailAuthList;

    private String orgId;

    @ApiModelProperty("平台ID")
    private String platformId;

}
