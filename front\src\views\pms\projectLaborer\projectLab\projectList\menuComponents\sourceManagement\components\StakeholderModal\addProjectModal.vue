<template>
  <div class="addNodeModal">
    <BasicDrawer
      v-model:visible="visible"
      :title="title"
      :width="500"
      :mask-closable="false"
      :showFooter="true"
      :showContinue="true"
      @register="registerDrawer"
      @ok="onSubmit"
      @cancel="cancel"
    >
      <BasicForm
        ref="formRef"
        @register="register"
      />
    </BasicDrawer>
  </div>
</template>
<script lang="ts">
import {
  computed, defineComponent, inject, onMounted, reactive, ref, toRefs,
} from 'vue';
import {
  Button, Checkbox, Form, Input, Select,
} from 'ant-design-vue';
import messageModal from '/@/views/pms/projectLaborer/components/MessageModal/messageModal.vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
import { addStakeHolderApi, editStakeHolderApi, getContactTypeApi } from '/@/views/pms/projectLaborer/api/projectList';
import {
  BasicDrawer, BasicForm, useDrawerInner, useForm,
} from 'lyra-component-vue3';

export default defineComponent({
  components: {
    BasicForm,
    BasicDrawer,
  },
  emits: ['success'],
  setup(props, { emit }) {
    let provideProjectId: any = inject('provideProjectId');
    const state = reactive({
      concactTypeOption: [],
      formType: '',
      checkedValue: [],
      addVisible: false,
      selectValue: '',
      visible: false,
      title: '',
      nextCheck: false,
      loading: false,
      // 表单是否发生变化
      flag: false,
      formState: {

      },
    });
    const [registerDrawer, { closeDrawer, setDrawerProps }] = useDrawerInner(async (openProps) => {
      state.visible = true;
      state.nextCheck = false;
      state.formState = openProps;
      if (openProps.type === 'edit') {
        state.title = '修改信息';
        state.formType = 'edit';
        setFieldsValue(openProps);
      } else {
        state.title = '新增信息';
        state.formType = 'add';
      }
    });
    const [register, { setFieldsValue, getFieldsValue }] = useForm({
      actionColOptions: {
        span: 24,
      },
      showActionButtonGroup: false,
      schemas: [
        {
          field: 'name',
          component: 'Input',
          label: '名称:',
          rules: [
            {
              required: true,
              trigger: 'blur',
              type: 'string',
            },
          ],
          componentProps: {
            placeholder: '请输入名称',
            maxlength: 25,
          },
          colProps: {
            span: 12,
          },
        },
        {
          field: 'contactType',
          component: 'Select',
          componentProps: {
            placeholder: '请输入名称',
            options: computed(() => state.concactTypeOption.map((item) => ({
              ...item,
              label: item.label,
              value: item.value,
            }))),
          },
          label: '联系方式:',
          colProps: {
            span: 12,
          },
        },
        {
          field: 'contactInfo',
          componentProps: {
            placeholder: '请输入联系信息',
          },
          component: 'Input',
          label: '联系信息:',
          colProps: {
            span: 24,
          },
        },
        {
          field: 'address',
          component: 'InputTextArea',
          componentProps: {
            placeholder: '请输入地址',
            max: 255,
            rows: 4,
            showCount: true,
            maxlength: 255,
          },
          label: '地址:',
          colProps: {
            span: 24,
          },
        },
        {
          field: 'remark',
          component: 'InputTextArea',
          componentProps: {
            placeholder: '请输入描述',
            rows: 4,
            showCount: true,
            maxlength: 255,
          },
          label: '描述:',
          colProps: {
            span: 24,
          },
        },
      ],
    });
    const formRef = ref();
    onMounted(async () => {
      const res = await getContactTypeApi();
      state.concactTypeOption = res.map((item) => ({
        value: item.id,
        label: item.name,
      }));
    });
    /* 表单取消按钮 */
    const cancel = () => {
      formRef.value.resetFields();
      closeDrawer();
      state.visible = false;
    };
    const confirm = () => {
      formRef.value.resetFields();
    };
    /* 提交按钮 */
    const onSubmit = () => {
      zhttp(getFieldsValue());
    };
    const zhttp = (httpValue) => {
      formRef.value
        .validate()
        .then(() => {
          state.loading = true;
          if (state.formType === 'edit') {
            /* 编辑 */
            let params = {
              ...httpValue,
              id: state.formState?.id,
              projectId: provideProjectId.value,
            };
            editStakeHolderApi(params)
              .then(() => {
                emit('success', false);
                cancel();
              })
              .catch(() => {
                state.loading = false;
              });
          } else {
            /* 走新增系列 */
            let params = {
              ...httpValue,
              projectId: provideProjectId.value,
            };
            addStakeHolderApi(params)
              .then(() => {
                emit('success', false);
                cancel();
              })
              .catch(() => {
                state.loading = false;
              });
          }
        })
        .catch(() => {
          state.loading = false;
        });
    };

    return {
      ...toRefs(state),
      formRef,
      cancel,
      confirm,
      onSubmit,
      register,
      setFieldsValue,
      registerDrawer,
      closeDrawer,
    };
  },
});
</script>
<style lang="less" scoped>

.nodeForm {
  padding: 10px 10px 80px 10px;
}
.ant-form-item{
  display: block;
}
.nextCheck {
  height: 40px;
  line-height: 40px;
}
.nodeItemBtn {
  position: fixed;
  bottom: 0px;
  padding: 20px 0;
  text-align: center;
  width: 280px;
  height: 80px;
  background: #ffffff;
  margin-bottom: 0px;
}
.cancelBtn {
  color: #5172dc;
  background: #5172dc19;
  width: 110px;
  border-radius: 4px;
}
.bgDC {
  width: 110px;
  margin-left: 15px;
  border-radius: 4px;
}
  //.addNodeModalDrawer {
  //  .ant-checkbox-wrapper,
  //  .ant-form-item-label > label {
  //    color: #444b5e;
  //  }
  //  .nodeForm {
  //    padding: 10px 10px 80px 10px;
  //  }
  //  .ant-form-item-label {
  //    text-align: left;
  //    color: #444b5e;
  //  }
  //  .cancelBtn {
  //    color: #5172dc;
  //    background: #5172dc19;
  //    width: 120px;
  //    border-radius: 4px;
  //  }
  //  .bgDC {
  //    width: 120px;
  //    margin-left: 15px;
  //    border-radius: 4px;
  //  }
  //  .nextCheck {
  //    height: 40px;
  //    line-height: 40px;
  //  }
  //  .nodeItemBtn {
  //    position: fixed;
  //    bottom: 0px;
  //    padding: 20px 0px;
  //    text-align: center;
  //    width: 320px;
  //    height: 80px;
  //    background: #ffffff;
  //    margin-bottom: 0px;
  //  }
  //}
</style>
