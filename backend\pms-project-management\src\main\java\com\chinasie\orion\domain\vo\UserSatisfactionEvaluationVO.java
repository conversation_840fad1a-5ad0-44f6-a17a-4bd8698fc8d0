package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.util.List;
/**
 * UserSatisfactionEvaluation VO对象
 *
 * <AUTHOR>
 * @since 2024-10-30 14:31:17
 */
@ApiModel(value = "UserSatisfactionEvaluationVO对象", description = "人员满意度评价")
@Data
public class UserSatisfactionEvaluationVO extends  ObjectVO   implements Serializable{


    /**
     * 年
     */
    @ApiModelProperty(value = "年")
    private Integer dataYear;


    /**
     * 中心编号
     */
    @ApiModelProperty(value = "中心编号")
    private String orgCode;


    /**
     * 中心名称
     */
    @ApiModelProperty(value = "中心名称")
    private String orgName;


    /**
     * 部门编号
     */
    @ApiModelProperty(value = "部门编号")
    private String deptCode;


    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String deptName;


    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    private String userCode;


    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;


    /**
     * 供应商编号
     */
    @ApiModelProperty(value = "供应商编号")
    private String supplierNo;


    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;


    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNo;


    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;


    /**
     * 岗级
     */
    @ApiModelProperty(value = "岗级")
    private String jobGrade;


    /**
     * 1季度评价
     */
    @ApiModelProperty(value = "1季度评价")
    private String oneQuarter;


    /**
     * 2季度评分
     */
    @ApiModelProperty(value = "2季度评分")
    private String twoQuarter;


    /**
     * 3季度评分
     */
    @ApiModelProperty(value = "3季度评分")
    private String threeQuarter;


    /**
     * 年度评价
     */
    @ApiModelProperty(value = "年度评价")
    private String fourQuarter;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String type;


    /**
     * parentId
     */
    @ApiModelProperty(value = "父id")
    private String parentId;


    /**
     * 儿子节点
     */
    @ApiModelProperty(value = "儿子节点")
    private List<UserSatisfactionEvaluationVO> children;
}
