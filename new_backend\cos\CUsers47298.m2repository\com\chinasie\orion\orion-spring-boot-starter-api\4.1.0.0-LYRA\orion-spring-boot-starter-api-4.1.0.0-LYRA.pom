<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.chinasie.orion</groupId>
    <artifactId>orion-framework</artifactId>
    <version>4.1.0.0-LYRA</version>
  </parent>
  <groupId>com.chinasie.orion</groupId>
  <artifactId>orion-spring-boot-starter-api</artifactId>
  <version>4.1.0.0-LYRA</version>
  <packaging>pom</packaging>
  <modules>
    <module>orion-spring-boot-starter-number-api</module>
    <module>orion-spring-boot-starter-msc-api</module>
    <module>orion-spring-boot-starter-file-api</module>
    <module>orion-spring-boot-starter-base-api</module>
    <module>orion-spring-boot-starter-common-api</module>
    <module>orion-spring-boot-starter-third-api</module>
  </modules>
  <properties>
    <maven.compiler.target>11</maven.compiler.target>
    <maven.compiler.source>11</maven.compiler.source>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.chinasie.orion</groupId>
        <artifactId>orion-spring-boot-starter-number-api</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>com.chinasie.orion</groupId>
        <artifactId>orion-spring-boot-starter-base-api</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>com.chinasie.orion</groupId>
        <artifactId>orion-spring-boot-starter-common-api</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>com.chinasie.orion</groupId>
        <artifactId>orion-spring-boot-starter-file-api</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>com.chinasie.orion</groupId>
        <artifactId>orion-spring-boot-starter-msc-api</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>com.chinasie.orion</groupId>
        <artifactId>orion-spring-boot-starter-workflow-api</artifactId>
        <version>${revision}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
