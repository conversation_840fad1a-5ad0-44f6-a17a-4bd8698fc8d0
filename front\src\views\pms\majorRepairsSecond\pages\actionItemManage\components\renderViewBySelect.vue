<script setup lang="ts">
import {
  BasicCard, UploadList, BasicForm, useForm, BasicEditor2, BasicButton, randomString,
} from 'lyra-component-vue3';
import {
  inject, provide, Ref, ref, watchEffect, h, watch, computed, reactive, onMounted, nextTick,
} from 'vue';
import { cloneDeep, get, hasIn } from 'lodash-es';
import { message } from 'ant-design-vue';
import { WorkflowAction, WorkflowView } from 'lyra-workflow-component-vue3';
import { Rule } from 'ant-design-vue/es/form';
import { useResizeObserver } from '@vueuse/core';
import ScrollableRowBar from './ScrollableRowBar.vue';
import TdCol from './TdCol.vue';
import Api from '/@/api';

const detailsData = inject('detailsData') ?? {};
const emits = defineEmits(['updateWorkCfg']);
const updateRefreshCfgKey = inject('updateRefreshCfgKey');

const personInCharge = ref([]);
const viewContainer = ref();
const workActionContainerStyle = ref({});
const childPersonData = ref({});
const workflowActionRef: Ref = ref();
const workflowViewRef: Ref = ref();
const fileListData = ref([]);
const loading = ref(false);
const currIdx = ref(0);
const workflowProps = computed(() => ({
  Api,
  businessData: {
    ...childPersonData.value,
    variables: {
      name: '大修行动项',
    },
  },
  beforeEvent(type) {
    if (type === 'pass') {
      const feedbackInfo = get(childPersonData.value, 'feedbackInfo');
      const feedback = get(childPersonData.value, 'feedback');
      if ([!feedbackInfo || feedbackInfo === '<p><br></p>', !feedback || feedback === '<p><br></p>'].every(Boolean)) {
        message.error('行动项反馈信息不能为空！');
        return Promise.reject();
      }

      return Promise.resolve();
    }
  },
  afterEvent() {
    workflowViewRef.value?.init();
    updateRefreshCfgKey.value = randomString(50);
  },
}));
const [
  register,
  {
    setFieldsValue, validate, validateFields, getFieldsValue, updateSchema, clearValidate,
  },
] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas: [
    {
      field: 'feedbackInfo',
      component: 'InputTextArea',
      label: '行动项反馈信息',
      colProps: {
        span: 24,
      },
      rules: [
        {
          required: true,
          message: '请输入行动项反馈信息',
          validator(_rule: Rule, value: string) {
            if (!value || value === '<p><br></p>') {
              return Promise.reject('请输入行动项反馈信息');
            }
            return Promise.resolve();
          },
        },
      ],
      slot: 'feedbackInfo',
    },
    // {
    //   field: 'actionAttachments',
    //   component: 'InputTextArea',
    //   label: '行动项附件',
    //   colProps: {
    //     span: 24,
    //   },
    //   slot: 'actionAttachments',
    // },
  ],
});

useResizeObserver(viewContainer, (entries) => {
  const entry = entries[0];
  const { width } = entry.contentRect;
  workActionContainerStyle.value = {
    width: `${width}px`,
  };
});

provide('childPersonData', childPersonData);

async function handleSave() {
  const formValue = await validateFields(['feedbackInfo']);
  try {
    await new Api('/pms/prodActionItem/add/feedback').fetch({
      feedbackInfo: formValue.feedbackInfo,
      id: childPersonData.value?.id,
    }, '', 'POST').then((res) => {
      childPersonData.value = {
        ...childPersonData.value,
        feedbackInfo: formValue.feedbackInfo || '',
      };
      message.success('保存成功');
    });
  } catch (e) {
  }
}

function parseChild() {
  let childList = cloneDeep(detailsData?.childList) ?? [];
  personInCharge.value = childList;
}

function handleSelect(treeItem: any, idx = 0) {
  childPersonData.value = treeItem;
  if (!hasIn(childPersonData.value, 'feedbackInfo')) {
    childPersonData.value = {
      ...childPersonData.value,
      feedbackInfo: '',
    };
  }
  currIdx.value = idx;
}

async function saveApi(files) {
  let fieldList = files.map((item) => {
    item.dataId = get(childPersonData.value, 'id');
    return item;
  });
  return new Api('').fetch(fieldList, 'pms/document/saveBatch', 'POST').then((res) => {
    listApi();
    return res;
  });
}

async function listApi() {
  try {
    loading.value = true;
    const result = await new Api('').fetch({
      dataId: get(childPersonData.value, 'id'),
    }, 'res/manage/file/new/getfilelist', 'GET');
    fileListData.value = result;
  } catch (e) {
  } finally {
    loading.value = false;
  }
}

async function getWorkflowInstance() {
  try {
    const instanceRes = await new Api('/wf/flowTemplateBusiness/new').fetch({}, childPersonData.value?.id, 'GET');
    if (instanceRes) {
      await nextTick();
      await workflowActionRef.value.setCurrentInstance(instanceRes);
      await workflowViewRef.value.setCurrentInstance(instanceRes);
      await workflowActionRef.value.init();
    } else {
    }
  } catch (e) {
  }
}

function handleEditorChange(val?: any) {
  updateSchema({
    field: 'feedbackInfo',
    label: childPersonData.value?.editFeedback
      ? h('div', { class: 'save-row' }, [
        h('span', '行动项反馈信息'),
        h(BasicButton, {
          class: 'save-editor',
          type: 'primary',
          onClick() {
            handleSave();
          },
        }, '保存'),
      ])
      : '行动项反馈信息',
  });
  if (val) {
    validateFields(['feedbackInfo']);
  }
}

watchEffect(() => {
  parseChild();
});
onMounted(() => {
  handleSelect(get(detailsData, 'childList.0', {}));
});
watch(() => childPersonData.value, (val) => {
  if (val?.id) {
    handleEditorChange();
    setFieldsValue({
      ...(get(childPersonData.value, 'feedback')
        ? { feedbackInfo: get(childPersonData.value, 'feedback') ?? '' }
        : {}),
    });
    validateFields(['feedbackInfo']);
    listApi();
    workflowViewRef.value?.setProps({
      businessData: {
        ...val,
      },
    });
    workflowActionRef.value?.setProps({
      businessData: {
        ...val,
      },
    });
    getWorkflowInstance();
  }
});

defineExpose({
  setFirstFlowCtx() {
    handleSelect(get(detailsData, 'childList.0', {}));
  },
});
</script>

<template>
  <div
    v-if="personInCharge.length>0"
    ref="viewContainer"
    v-loading="loading"
    class="user-custom-info"
    :class="[
      childPersonData?.editFeedback?'':'readonly-user-info',
    ]"
  >
    <div class="form-card">
      <BasicCard
        title="行动项反馈信息"
      >
        <ScrollableRowBar>
          <TdCol
            v-for="(col,idx) in personInCharge"
            :key="idx"
            :class="currIdx===idx?'tr-col-active':''"
            :idx="idx"
            :curr-idx="idx"
            :col-cfg="col"
            @onSelect="handleSelect(col,idx)"
          />
        </ScrollableRowBar>
        <BasicForm
          @register="register"
        >
          <template #feedbackInfo="{ model, field }">
            <div
              v-if="childPersonData.editFeedback"
              class="feedback-info"
            >
              <BasicEditor2
                v-model:value="model[field]"
                :config="{
                  placeholder: '请输入反馈信息'
                }"
                @change="handleEditorChange"
              />
            </div>
            <div
              v-else
              class="improvement-measure"
            >
              <div
                class="im-inner"
                v-html="childPersonData?.feedback"
              />
            </div>
          </template>
          <!-- <template #actionAttachments>
            <div class="actionAttachments">
              <UploadList
                :key="childPersonData.id"
                :listData="fileListData"
                :edit="childPersonData.editFeedback"
                :saveApi="saveApi"
                :isFileDownload="false"
                :isFileEdit="false"
                type="page"
              />
            </div>
          </template> -->
        </BasicForm>
      </BasicCard>
    </div>
    <div
      v-if="childPersonData.id"
      class="work-flow-wrap"
    >
      <WorkflowView
        ref="workflowViewRef"
        :workflow-props="workflowProps"
      />
      <div
        class="action-flow"
        :style="workActionContainerStyle"
      >
        <WorkflowAction
          ref="workflowActionRef"
          :workflow-props="workflowProps"
        />
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.user-custom-info {
  position: relative;
  padding-bottom: 25px;

  .ant-basic-form {
    &.spacing {
      padding: 12px 0 !important;
    }
  }

  :deep(.ant-basic-table) {
    &.default-spacing {
      padding: 0 !important;
    }
  }

  .process-record {
    height: 500px;
    overflow: hidden;
  }

  :deep(.ant-form-item) {
    label[for='form_item_feedbackInfo'] {
      width: 100%;
    }
  }

  :deep(.save-row) {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

}

.form-card {
  :deep(.basic-card-wrap) {
    margin-bottom: 0 !important;
  }

  :deep(.ant-form-item) {
    margin-bottom: 0 !important;
  }
}

.improvement-measure {
  height: 200px;
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  box-shadow: none;
  color: #333;
  padding: 12px;
  overflow-y: auto;
  line-height: 24px;
}

.feedback-info {
  padding-bottom: 16px;
}

.readonly-user-info {
  :deep(.ant-form-item-explain-error) {
    display: none;
  }

  :deep(.save-row) {
    .ant-btn {
      display: none;
    }
  }
}

.actionAttachments {
  :deep(.surely-table-body) {
    min-height: 120px !important;
    height: auto !important;
    max-height: 500px !important;
    overflow-y: auto !important;

    .ant-row {
      margin-bottom: 0 !important;
    }
  }
}

.work-flow-wrap {
  height: 545px;
  position: relative;

  .action-flow {
    position: fixed;
    background: #fff;
    right: 8px;
    bottom: 8px;
    z-index: 1000;
  }
}

</style>