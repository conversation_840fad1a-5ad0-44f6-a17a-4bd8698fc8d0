<template>
  <div class="project-layout-wrap">
    <div class="title">
      <div
        v-if="$attrs.pageTitle"
        class="content-title"
      >
        <slot name="title">
          <h3>{{ $attrs.pageTitle }}</h3>
          <div>
            <slot name="titleRight" />
          </div>
        </slot>
      </div>
      <BasicTabs
        v-if="$attrs.tabs"
        v-bind="$attrs"
      />
    </div>
    <ProjectLayoutHeader
      v-if="isHeader"
      class="header"
      v-bind="$attrs"
    >
      <template
        v-for="item in Object.keys($slots)"
        #[item]="data"
      >
        <slot
          v-bind="data"
          :name="item"
        />
      </template>
    </ProjectLayoutHeader>
    <div class="body">
      <Menu
        v-if="$attrs.menuData && isMenu"
        v-bind="$attrs"
        class="project-layout-menu"
        :show-header="false"
      >
        <template
          v-for="item in Object.keys($slots)"
          #[item]="data"
        >
          <template v-if="item !== 'default'">
            <slot
              v-bind="data"
              :name="item"
            />
          </template>
        </template>
      </Menu>
      <div class="project-layout-content">
        <slot />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import {
  BasicTable, Layout, useActionsRecord, OrionTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer, BasicTabs,
} from 'lyra-component-vue3';
import ProjectLayoutHeader from './components/Header.vue';
import Menu from '/@/views/pms/projectLaborer/components/BasicMenu';
// import BasicTabs from '/@/components/BasicTabs/';
export default defineComponent({
  name: 'ProjectLayout',
  components: {
    ProjectLayoutHeader,
    Menu,
    BasicTabs,
  },
  props: {
    isHeader: {
      type: Boolean,
      default: true,
    },
    isMenu: {
      type: Boolean,
      default: true,
    },
  },
});
</script>

<style lang="less" scoped>
  .project-layout-wrap {
    display: flex;
    flex-direction: column;
    margin: 12px;
    background: #fff;
    border-radius: 2px;

    > .body {
      flex: 1;
      display: flex;
      height: 1px;
      overflow: auto;

      > .project-layout-content {
        flex: 1;
        position: relative;
        overflow: auto;
      }
    }
  }

  .project-layout-menu {
    width: 230px;
    border-right: 1px solid #e9ecf2;
  }

  .content-title {
    border-bottom: 1px solid #e9ecf2;
    line-height: 50px;
    padding: 0 10px;
    display: flex;
    align-items: center;

    > h3 {
      font-size: 16px;
      color: #19243b;
      margin: 0;
      padding-left: 15px;
      font-weight: normal;
      position: relative;
      flex: 1;

      &::after {
        content: '';
        position: absolute;
        left: 0;
        height: 16px;
        width: 4px;
        top: 50%;
        margin-top: -8px;
        background: blue;
      }
    }
  }

  .title {
    :deep(.tabs-wrap) {
      margin-top: 0;
    }
  }
</style>
