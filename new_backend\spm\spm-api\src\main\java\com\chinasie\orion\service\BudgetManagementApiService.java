package com.chinasie.orion.service;

import com.chinasie.orion.domain.vo.BudgetManagementVO;
import com.chinasie.orion.sdk.core.conf.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: lsy
 * @date: 2024/6/6
 * @description:
 */
@FeignClient(name = "pms", configuration = FeignConfig.class)
@Lazy
public interface BudgetManagementApiService {

    String API_PREFIX = "/api-pms/budget";

    /**
     * 获取预算详情列表
     * @param budgetIdList
     * @return
     * @throws Exception
     */
    @PostMapping(value = API_PREFIX + "/byIds/list")
    List<BudgetManagementVO> getBudgetVOByIds(@RequestBody List<String> budgetIdList) throws Exception;
}
