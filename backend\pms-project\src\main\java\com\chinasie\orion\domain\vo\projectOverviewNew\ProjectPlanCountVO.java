package com.chinasie.orion.domain.vo.projectOverviewNew;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "ProjectPlanCountVO", description = "项目计划统计")
public class ProjectPlanCountVO {
    @ApiModelProperty(value = "时间")
    private String time;
    @ApiModelProperty(value = "计划总数")
    private Integer planTotal=0;
    @ApiModelProperty(value = "计划未开始数量")
    private Integer noStartCount=0;
    @ApiModelProperty(value = "计划进行中数量")
    private Integer underwayCount=0;
    @ApiModelProperty(value = "计划已完成数量")
    private Integer completeCount=0;
    @ApiModelProperty(value = "计划异常总数")
    private Integer planAbnormalTotal=0;
    @ApiModelProperty(value = "计划逾期数量")
    private Integer overdueCount=0;
    @ApiModelProperty(value = "计划临期数量")
    private Integer nearExpiredCount=0;
    @ApiModelProperty(value = "计划逾期完成数量")
    private Integer overdueCompleteCount=0;
}
