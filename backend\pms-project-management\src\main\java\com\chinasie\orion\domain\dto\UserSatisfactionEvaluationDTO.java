package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * UserSatisfactionEvaluation DTO对象
 *
 * <AUTHOR>
 * @since 2024-10-30 14:31:17
 */
@ApiModel(value = "UserSatisfactionEvaluationDTO对象", description = "人员满意度评价")
@Data
@ExcelIgnoreUnannotated
public class UserSatisfactionEvaluationDTO extends  ObjectDTO   implements Serializable{

    /**
     * 年
     */
    @ApiModelProperty(value = "年")
    private Integer dataYear;

    /**
     * 中心编号
     */
    @ApiModelProperty(value = "中心编号")
    @ExcelProperty(value = "中心编号 ", index = 0)
    private String orgCode;

    /**
     * 中心名称
     */
    @ApiModelProperty(value = "中心名称")
    @ExcelProperty(value = "中心名称 ", index = 1)
    private String orgName;

    /**
     * 部门编号
     */
    @ApiModelProperty(value = "部门编号")
    @ExcelProperty(value = "部门编号 ", index = 2)
    private String deptCode;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    @ExcelProperty(value = "部门名称 ", index = 3)
    private String deptName;

    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    @ExcelProperty(value = "工号 ", index = 4)
    private String userCode;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    @ExcelProperty(value = "用户名称 ", index = 5)
    private String userName;

    /**
     * 供应商编号
     */
    @ApiModelProperty(value = "供应商编号")
    @ExcelProperty(value = "供应商编号 ", index = 6)
    private String supplierNo;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @ExcelProperty(value = "供应商名称 ", index = 7)
    private String supplierName;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 8)
    private String contractNo;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称 ", index = 9)
    private String contractName;

    /**
     * 岗级
     */
    @ApiModelProperty(value = "岗级")
    @ExcelProperty(value = "岗级 ", index = 10)
    private String jobGrade;

    /**
     * 1季度评价
     */
    @ApiModelProperty(value = "1季度评价")
    @ExcelProperty(value = "1季度评价 ", index = 11)
    private String oneQuarter;

    /**
     * 2季度评分
     */
    @ApiModelProperty(value = "2季度评分")
    @ExcelProperty(value = "2季度评分 ", index = 12)
    private String twoQuarter;

    /**
     * 3季度评分
     */
    @ApiModelProperty(value = "3季度评分")
    @ExcelProperty(value = "3季度评分 ", index = 13)
    private String threeQuarter;

    /**
     * 年度评价
     */
    @ApiModelProperty(value = "年度评价")
    @ExcelProperty(value = "年度评价 ", index = 14)
    private String fourQuarter;




}
