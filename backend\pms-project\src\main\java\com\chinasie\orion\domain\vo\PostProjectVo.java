package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/2/24 9:34
 * @description:
 */
@Data
@ApiModel(value = "ProjectRoleVO对象", description = "项目角色")
public class PostProjectVo extends ObjectVO {

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "结项类型")
    private String type;
    private String typeName;

    @ApiModelProperty(value = "理由")
    private String reason;


    @ApiModelProperty(value = "ID")
    private String id;


    @ApiModelProperty(value = "结束时间")
    private Date endTime;


    @ApiModelProperty(value = "开始时间")
    private Date startTime;


    @ApiModelProperty(value = "负责人Id")
    private String principalId;

    @ApiModelProperty(value = "负责人名称")
    private String principalName;
}
