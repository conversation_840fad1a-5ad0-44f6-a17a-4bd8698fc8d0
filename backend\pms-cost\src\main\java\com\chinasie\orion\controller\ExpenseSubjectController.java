package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ExpenseSubjectDTO;
import com.chinasie.orion.domain.vo.ExpenseSubjectVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ExpenseSubjectService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.dto.ResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;
import java.lang.Exception;
import java.lang.String;

/**
 * <p>
 * ExpenseSubject 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16 15:15:30
 */
@RestController
@RequestMapping("/expenseSubject")
@Api(tags = "费用科目类")
public class ExpenseSubjectController {

    @Autowired
    private ExpenseSubjectService expenseSubjectService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查看【费用科目】 【【{{#name}}】详情", type = "CostCenter", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ExpenseSubjectVO> detail(@PathVariable(value = "id") String id) throws Exception {
        ExpenseSubjectVO rsp = expenseSubjectService.detail(id);
        LogRecordContext.putVariable("name", rsp.getName());
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param expenseSubjectDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】新增【费用科目】 【【{{#name}}】数据", type = "CostCenter", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<ExpenseSubjectVO> create(@RequestBody ExpenseSubjectDTO expenseSubjectDTO) throws Exception {
        ExpenseSubjectVO rsp = expenseSubjectService.create(expenseSubjectDTO);
        LogRecordContext.putVariable("name", rsp.getName());
        LogRecordContext.putVariable("id", rsp.getId());
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param expenseSubjectDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【费用科目】 【{{#name}}】数据", type = "CostCenter", subType = "编辑", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> edit(@RequestBody ExpenseSubjectDTO expenseSubjectDTO) throws Exception {
        Boolean rsp = expenseSubjectService.edit(expenseSubjectDTO);
        LogRecordContext.putVariable("name", expenseSubjectDTO.getName());
        LogRecordContext.putVariable("id", expenseSubjectDTO.getId());
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "{id}", method = RequestMethod.DELETE)
    @LogRecord(success = "【{USER{#logUserId}}】删除【费用科目】数据", type = "CostCenter", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable(value = "id") String id) throws Exception {
        Boolean rsp = expenseSubjectService.removeById(id);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【费用科目】分页数据", type = "CostCenter", subType = "分页查询", bizNo = "")
    public ResponseDTO<Page<ExpenseSubjectVO>> pages(@RequestBody Page<ExpenseSubjectDTO> pageRequest) throws Exception {
        Page<ExpenseSubjectVO> rsp = expenseSubjectService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "启用")
    @RequestMapping(value = "/use/{id}", method = RequestMethod.PUT)
    @LogRecord(success = "【{USER{#logUserId}}】启用【费用科目】【{{#name}}】数据", type = "CostCenter", subType = "启用", bizNo = "")
    public ResponseDTO<Boolean> use(@PathVariable("id") String id) throws Exception {
        Boolean rsp = expenseSubjectService.use(id);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "禁用")
    @RequestMapping(value = "/ban/{id}", method = RequestMethod.PUT)
    @LogRecord(success = "【{USER{#logUserId}}】禁用【费用科目】【{{#name}}】数据", type = "CostCenter", subType = "禁用", bizNo = "")
    public ResponseDTO<Boolean> ban(@PathVariable("id") String id) throws Exception {
        Boolean rsp = expenseSubjectService.ban(id);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "关系树")
    @RequestMapping(value = "/tree", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查看【费用科目】关系树", type = "CostCenter", subType = "关系树", bizNo = "")
    public ResponseDTO<List<ExpenseSubjectVO>> pages(@RequestParam(value = "searchText", required = false) String searchText
            , @RequestParam(value = "status", required = false) Integer status) throws Exception {
        List<ExpenseSubjectVO> rsp = expenseSubjectService.tree(searchText, status);
        return new ResponseDTO<List<ExpenseSubjectVO>>(rsp);
    }

    @ApiOperation(value = "首层费用科目")
    @LogRecord(success = "【{USER{#logUserId}}】查看【费用科目】首层费用科目", type = "CostCenter", subType = "查询", bizNo = "")
    @RequestMapping(value = "/getFirstFloor", method = RequestMethod.GET)
    public ResponseDTO<List<ExpenseSubjectVO>> getFirstFloor() throws Exception {
        List<ExpenseSubjectVO> rsp = expenseSubjectService.getFirstExpenseSubject();
        return new ResponseDTO<>(rsp);
    }


    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页-树型")
    @RequestMapping(value = "/tree/page", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查看【费用科目】分页树", type = "CostCenter", subType = "树型", bizNo = "")
    public ResponseDTO<Page<ExpenseSubjectVO>> treePages(@RequestBody Page<ExpenseSubjectDTO> pageRequest) throws Exception {
        Page<ExpenseSubjectVO> rsp = expenseSubjectService.treePages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

}
