<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>de.daniel<PERSON><PERSON>ler</groupId>
  <artifactId>java-object-diff</artifactId>
  <version>0.95</version>
  <dependencies>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.7.22</version>
      <scope>runtime</scope>
    </dependency>
  </dependencies>
  <description>Library to diff and merge Java objects with ease</description>
  <name>java-object-diff</name>
  <url>https://github.com/SQiShER/java-object-diff</url>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>sqisher</id>
      <name>Daniel Bechler</name>
      <url>https://github.com/SQiShER</url>
    </developer>
  </developers>
  <scm>
    <url>https://github.com/SQiShER/java-object-diff</url>
  </scm>
</project>
