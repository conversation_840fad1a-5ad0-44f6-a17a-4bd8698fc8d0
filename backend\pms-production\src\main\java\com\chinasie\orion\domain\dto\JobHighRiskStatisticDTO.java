package com.chinasie.orion.domain.dto;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "JobHighRiskStatisticDTO对象", description = "高风险作业统计")
@Data
@ExcelIgnoreUnannotated
public class JobHighRiskStatisticDTO extends ObjectDTO implements Serializable {

    @ApiModelProperty(value = "作业名称")
    private String jobName;

    @ApiModelProperty(value = "类别")
    private String riskLevel;

    @ApiModelProperty(value = "风险名称")
    private String riskName;

    @ApiModelProperty(value = "作业部门id")
    private String rspDeptId;

    @ApiModelProperty(value = "作业部门名称")
    private String jobDeptName;

    @ApiModelProperty(value = "项目负责人")
    private String rspUserId;

    @ApiModelProperty(value = "项目负责人名称")
    private String rspUserName;

    @ApiModelProperty(value = "管理人员名称")
    private String managePersonName;

    @ApiModelProperty(value = "监管人员名称")
    private String monitorPersonName;

    @ApiModelProperty(value = "实际结束时间")
    private String actualEndTime;

}
