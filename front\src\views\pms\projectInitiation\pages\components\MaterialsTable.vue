<template>
  <BasicCard
    title="材料费编制"
  >
    <div class="materials-table">
      <OrionTable
        ref="tableRef"
        :options="tableObjOptions"
        @selection-change="selectionChange"
        @initData="initData"
      >
        <template
          v-if="props.status===101"
          #toolbarLeft
        >
          <BasicButton
            v-is-power="['XMLXXQ_container_01_button_08']"
            type="primary"
            icon="add"
            :disabled="selectRowKeys.length>1"
            @click="addTableNode('add')"
          >
            选择产品
          </BasicButton>
          <BasicButton
            v-is-power="['XMLXXQ_container_01_button_09']"
            icon="delete"
            :disabled="selectRowKeys.length!==1"
            @click="addTableNode('change')"
          >
            改配
          </BasicButton>
          <BasicButton
            v-is-power="['XMLXXQ_container_01_button_010']"
            type="primary"
            icon="add"
            :disabled="selectRowKeys.length>1"
            @click="addTableNode('add')"
          >
            同步价格
          </BasicButton>
          <BasicButton
            v-is-power="['XMLXXQ_container_01_button_011']"
            icon="delete"
            :disabled="selectRowKeys.length===0"
            @click="deleteBatch"
          >
            移除
          </BasicButton>
          <BasicButton
            v-is-power="['XMLXXQ_container_01_button_023']"
            type="primary"
            @click="saveTableData"
          >
            保存
          </BasicButton>
        </template>
        <template
          #requiredNum="{record}"
        >
          <AInputNumber
            v-if="!Array.isArray(record.children)||record.children.length===0"
            v-model:value="record.requiredNum"
            :disabled="props.status!==101"
            @change="changeValue($event,record)"
          />
          <!--          <span>{{ formatMoney(record.amount) }}</span>-->
        </template>
        <template #footer>
          <div
            class="footer"
          >
            <span class="footer-label">汇总</span>
            <div class="footer-sum">
              <span class="footer-sum-span">材料费概算（元）：</span>
              <span class="footer-sum-value">{{ formatMoney(totalSum) }}</span>
            </div>
          </div>
        </template>
      </OrionTable>
    </div>
  </BasicCard>
</template>
<script setup lang="ts">
import { SelectListTable } from '/@/views/pms/components';
import { ref, Ref } from 'vue';
import {
  BasicCard, BasicButton, OrionTable, openModal,
} from 'lyra-component-vue3';
import { formatMoney } from '../../index';
import Api from '/@/api';
import { message, Modal, InputNumber as AInputNumber } from 'ant-design-vue';
const props = withDefaults(defineProps<{
    formId:string,
    status:number
}>(), {
  formId: '',
  status: 101,
});
const selectRowKeys:Ref<string[]> = ref([]);

function selectionChange({ keys }) {
  selectRowKeys.value = keys;
}
const totalSum:Ref<number> = ref(0);
const tableRef = ref();
const tableObjOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: {},
  showIndexColumn: false,
  expandIconColumnIndex: 2,
  showTableSetting: false,
  showSmallSearch: false,
  pagination: false,
  api: async () => {
    let params = {
      projectApprovalId: props.formId,
    };
    let tableData = await new Api('/pms').fetch(params, 'projectApprovalEstimate/material/list', 'GET');
    totalSum.value = tableData.materialFee;
    return tableData.projectApprovalEstimateMaterialVOList;
  },
  columns: [
    {
      title: '物料结构名称',
      dataIndex: 'name',
      slots: { customRender: 'name' },
      minWidth: 200,
    },
    {
      title: '物料价格',
      dataIndex: 'materialPrice',
      ellipsis: true,
      width: 100,
      customRender: ({ text, record }) => {
        if (Array.isArray(record.children)) {
          return '';
        }
        return text;
      },
    },
    {
      title: '结构包含物料数量',
      dataIndex: 'materialAmount',
      width: 150,
      customRender: ({ text, record }) => {
        if (Array.isArray(record.children)) {
          return '';
        }
        return text;
      },
    },
    {
      title: '需求数量',
      dataIndex: 'requiredNum',
      slots: { customRender: 'requiredNum' },
      width: 150,
      customRender: ({ text, record }) => {
        if (Array.isArray(record.children)) {
          return '';
        }
      },
    },
    {
      title: '材料概算（元）',
      dataIndex: 'amount',
      width: 150,
      customRender: ({ text, record }) => {
        if (Array.isArray(record.children)) {
          return `总计：${formatMoney(record.amount)}`;
        }
        return formatMoney(record.amount);
      },
    },
  ],
});

let initDataTimeOut = null;
function changeValue(val, record:any) {
  if (initDataTimeOut) {
    clearTimeout(initDataTimeOut);
  }
  initDataTimeOut = setTimeout(() => {
    record.amount = record.materialPrice * record.requiredNum;
    let dataSource = tableRef.value.getDataSource();
    initSumData(dataSource);
    totalSum.value = dataSource.reduce((acc, item1) => {
      let amount = !isNaN(item1.amount) ? item1.amount : 0;
      return acc + amount;
    }, 0);
    tableRef.value.setTableData(dataSource);
  }, 300);
}
function initSumData(dataSource) {
  dataSource.forEach((item) => {
    if (Array.isArray(item.children)) {
      initSumData(item.children);
      item.amount = item.children.reduce((acc, item1) => {
        let amount = !isNaN(item1.amount) ? item1.amount : 0;
        return acc + amount;
      }, 0);
      item.requiredNum = item.children.reduce((acc, item1) => {
        let amount = !isNaN(item1.requiredNum) ? item1.requiredNum : 0;
        return acc + amount;
      }, 0);
    }
  });
}

function initData(data:any[], indexColumns = '') {
  data.forEach((item, index) => {
    if (indexColumns) {
      item.index = `${indexColumns}.${index + 1}`;
    } else {
      item.index = index + 1;
    }
    if (Array.isArray(item.children)) {
      initData(item.children, item.index);
    }
  });
}
function addTableNode(type) {
  const selectListTableRef = ref();
  openModal({
    title: '添加物料',
    width: 1100,
    height: 700,
    content(h) {
      return h(SelectListTable, {
        ref: selectListTableRef,
        getTableData,
        columns: [
          {
            title: '物料编号',
            dataIndex: 'number',
            width: 180,
          },
          {
            title: '物料名称',
            dataIndex: 'name',
            width: 200,
          },
          {
            title: '总计值',
            dataIndex: 'materialPrice',
            customRender: ({ text, record }) => formatMoney(text),
          },
          {
            title: '数量',
            dataIndex: 'materialAmount',

          },
          {
            title: '归属项目',
            dataIndex: 'projectName1',
          },
          {
            title: '项目负责人',
            dataIndex: 'modifyTime1',
          },
        ],
        showLeftTree: false,
        selectType: 'check',
        isTableTree: true,
      });
    },
    async onOk() {
      let selectTable = await selectListTableRef.value.getFormData();
      if (selectTable.selectedRowKeys.length === 0) {
        message.warning('请选择物料');
        return Promise.reject('');
      }
      let params = {};
      let api = '';
      if (type === 'add') {
        params = {
          projectApprovalId: props.formId,
          parentId: selectRowKeys.value.length === 0 ? '0' : selectRowKeys.value[0],
          projectApprovalEstimateMaterialDTOList: selectTable.selectTableData.map((item) => ({
            name: item.name,
            id: item.id,
            materialPrice: item.materialPrice,
            materialAmount: item.materialAmount,
          })),
        };
        api = 'projectApprovalEstimate/material/add/batch';
      } else {
        params = {
          id: selectRowKeys.value[0],
          projectApprovalEstimateMaterialVOList: selectTable.selectTableData.map((item) => ({
            name: item.name,
            id: item.id,
            materialPrice: item.materialPrice,
            materialAmount: item.materialAmount,
          })),
        };
        api = 'projectApprovalEstimate/material/change';
      }
      new Api('/pms').fetch(params, api, type === 'add' ? 'POST' : 'PUT').then((res) => {
        message.success(type === 'add' ? '添加物料成功' : '改配物料成功');
        tableRef.value.reload({ page: 1 });
      });
    },
  });
}
function saveTableData() {
  let params = initTreeTable(tableRef.value.getDataSource(), props.formId);
  new Api('/pms').fetch(params, 'projectApprovalEstimate/material/editAmount', 'PUT').then((res) => {
    message.success('保存物料成功');
    tableRef.value.reload({ page: 1 });
  });
}
function initTreeTable(tableData, id) {
  return tableData.map((item) => {
    let newItem:any = {
      id: item.id,
      requiredNum: item.requiredNum,
      amount: item.amount,
      projectApprovalId: id,
      parentId: item.parentId,
    };
    if (Array.isArray(item.children)) {
      newItem.children = initTreeTable(item.children, id);
    }
    return newItem;
  });
}
function deleteBatch() {
  Modal.confirm({
    title: '移除提示',
    content: '是否移除选中的物料',
    onOk() {
      new Api('/pms').fetch(selectRowKeys.value, 'projectApprovalEstimate/material/remove', 'DELETE').then((res) => {
        message.success('移除成功。');
        tableRef.value.reload({ page: 1 });
      });
    },
  });
}

function getTableData(params) {
  return new Api('/pms').fetch(params, 'projectApprovalEstimate/getProductEstimateMaterialPage', 'POST');
}
</script>
<style lang="less" scoped>
:deep(.basic-card-wrap){
  border-bottom: 0;
}
.materials-table{
  height: 450px;
  overflow: hidden;
}
:deep(.ant-basic-table){
  padding-right: 0 !important;
  padding-left: 0 !important;
}
:deep(.surely-table-body){
  height: 286px !important;
}
</style>