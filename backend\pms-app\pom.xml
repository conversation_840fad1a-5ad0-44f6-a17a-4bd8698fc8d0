<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>pms</artifactId>
        <groupId>com.chinasie.orion</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>pms-app</artifactId>
    <version>${revision}</version>

    <dependencies>
        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>pms-project</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>pms-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>pms-cost</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>pms-project-contract</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>pms-production</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>pms-interface</artifactId>
        </dependency>
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-core</artifactId>
            <version>7.15.0</version>
        </dependency>
        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>pms-finance</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>orion-spring-boot-starter-msc-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>pms-project-management</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>pms-common</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!--方式二 只能将依赖放入平级目录-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.2.0</version>
                <configuration>
                    <archive>
                        <manifest>
                            <!-- 配置加入依赖包 -->
                            <addClasspath>true</addClasspath>
                            <classpathPrefix>lib/</classpathPrefix>
                            <useUniqueVersions>false</useUniqueVersions>
                            <!-- Spring Boot 启动类(自行修改) -->
                            <mainClass>com.chinasie.orion.PMSApplication</mainClass>
                        </manifest>
                    </archive>
                    <!-- jar 输出目录 -->
                    <outputDirectory>${project.build.directory}/pms/</outputDirectory>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <!-- 复制依赖 -->
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <!-- 依赖包 输出目录 -->
                            <outputDirectory>${project.build.directory}/pms/lib</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>


</project>
