package com.chinasie.orion.constant;

import java.util.HashMap;
import java.util.Map;

public enum JobPrepDownEnum {
    jobCount("jobCount"," "),
    requiredCount("requiredCount"," "),
    majorProjectCount("majorProjectCount"," "),
    majorAuditProjectCount("majorAuditProjectCount"," "),
    majorProjectAuditP("majorProjectAuditP"," "),
    highRiskCount("highRiskCount"," "),
    highRiskNotTellCount("highRiskNotTellCount"," "),
    highRiskOneCount("highRiskOneCount"," "),
    highRiskTwoCount("highRiskTwoCount"," "),
    highRiskThreeCount("highRiskThreeCount"," "),
    antiForfeignLevelOneCount("antiForfeignLevelOneCount"," "),
    antiForfeignLevelTwoCount("antiForfeignLevelTwoCount"," "),
    antiForfeignLevelThreeCount("antiForfeignLevelThreeCount"," "),
    antiForfeignLevelNaCount("antiForfeignLevelNaCount"," ");

    private String code;

    private String sqlDesc;

    public String getCode() {
        return code;
    }
    public String getSqlDesc() {
        return sqlDesc;
    }
    private JobPrepDownEnum(String code, String sqlDesc) {
        this.code = code;
        this.sqlDesc = sqlDesc;
    }

    public static Map<String,String> getMap() {
        Map<String,String> map = new HashMap<>();
        for (JobPrepDownEnum jobPrepDownEnum : JobPrepDownEnum.values()) {
            map.put(jobPrepDownEnum.getCode(), jobPrepDownEnum.getSqlDesc());
        }
        return new HashMap<>();
    }

}
