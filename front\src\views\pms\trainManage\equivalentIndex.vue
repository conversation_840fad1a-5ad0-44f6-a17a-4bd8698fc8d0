<script setup lang="ts">
import {
  BasicButton, isPower, Layout, OrionTable,
} from 'lyra-component-vue3';
import {
  h, nextTick, ref, Ref,
} from 'vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { openEquivalentForm } from './utils';
import Api from '/@/api';
import { usePagePower } from '/@/views/pms/hooks';

const router = useRouter();
const { powerData, getPowerDataHandle } = usePagePower();
const tableRef: Ref = ref();
const tableOptions = {
  showToolButton: false,
  smallSearchField: ['userCode'],
  api: (params: any) => new Api('/pms/train-equivalent').fetch({
    ...params,
    power: {
      pageCode: 'PMSEquivalent',
      containerCode: 'PMS_PXDX_container_02',
    },
  }, 'page', 'POST'),
  columns: [
    {
      title: '员工号',
      dataIndex: 'userCode',
      width: 110,
      customRender({ text, record }) {
        if (isPower('PMS_PXDX_container_02_button_01', record?.rdAuthList)) {
          return h('span', {
            class: 'flex-te action-btn',
            title: text,
            onClick: () => navDetails(record?.id),
          }, text);
        }
        return h('span', {
          class: 'flex-te',
          title: text,
        }, text);
      },
    },
    {
      title: '姓名',
      dataIndex: 'fullName',
      width: 110,
    },
    {
      title: '现任职务',
      dataIndex: 'nowPosition',
      width: 110,
    },
    {
      title: '培训名称',
      dataIndex: 'trainName',
    },
    {
      title: '培训基地',
      dataIndex: 'baseName',
      width: 110,
    },
    {
      title: '培训等效申请基地',
      dataIndex: 'equivalentBaseName',
      width: 130,
    },
    {
      title: '是否等效',
      dataIndex: 'isEquivalent',
      width: 80,
      customRender({ text }) {
        return text ? '是' : text === false ? '否' : '';
      },
    },
    {
      title: '培训课时',
      dataIndex: 'lessonHour',
      width: 80,
    },
    {
      title: '到期日期',
      dataIndex: 'expireTime',
      width: 110,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 140,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '查看',
      isShow: (record) => isPower('PMS_PXDX_container_02_button_01', record?.rdAuthList),
      onClick: (record: { id: string }) => navDetails(record?.id),
    },
    {
      text: '管理',
      isShow: (record) => isPower('PMS_PXDX_container_02_button_02', record?.rdAuthList) && !(record?.status === 130 && record?.isEquivalent),
      onClick(record: { id: string }) {
        openEquivalentForm(record, updateTable);
      },
    },
    {
      text: '移除',
      isShow: (record) => isPower('PMS_PXDX_container_02_button_03', record?.rdAuthList) && !(record?.status === 130 && record?.isEquivalent),
      modal: (record: { id: string }) => removeApi([record?.id]),
    },
  ],
};

async function updateTable() {
  await nextTick();
  tableRef.value?.reload();
}

function navDetails(id: string) {
  router.push({
    name: 'PMSEquivalentDetails',
    params: {
      id,
    },
  });
}

function removeApi(ids: string[]) {
  return new Promise((resolve, reject) => {
    new Api('/pms/train-equivalent/remove').fetch(ids, '', 'DELETE').then(() => {
      updateTable();
      resolve('');
    }).catch(() => {
      reject('');
    });
  });
}
</script>

<template>
  <Layout
    v-get-power="{pageCode:'PMSEquivalent', getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <BasicButton
          v-is-power="['PMS_PXDX_container_01_button_01']"
          type="primary"
          icon="sie-icon-tianjiaxinzeng"
          @click="openEquivalentForm(null,updateTable)"
        >
          添加等效
        </BasicButton>
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">

</style>
