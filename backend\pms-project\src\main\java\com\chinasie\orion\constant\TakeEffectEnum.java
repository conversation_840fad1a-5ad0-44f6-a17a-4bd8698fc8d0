package com.chinasie.orion.constant;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/1/13 11:26
 */
public enum TakeEffectEnum {
    EFFECT(1, "启用"),
    UN_EFFECT(0, "禁用");
    private Integer status;
    private String name;

    public Integer getStatus() {
        return status;
    }

    public String getName() {
        return name;
    }

    TakeEffectEnum(Integer status, String name) {
        this.status = status;
        this.name = name;
    }

    public static String getNameByStatus(Integer status){
        if (EFFECT.getStatus().equals(status)) {
            return EFFECT.getName();
        }
        if (UN_EFFECT.getStatus().equals(status)) {
            return UN_EFFECT.getName();
        }
        return "";
    }
}
