package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;

/**
 * AssessmentLog Entity对象
 *
 * <AUTHOR>
 * @since 2024-10-17 09:28:12
 */
@TableName(value = "pmsx_assessment_log")
@ApiModel(value = "AssessmentLogEntity对象", description = "审核记录表")
@Data

public class AssessmentLog extends  ObjectEntity  implements Serializable{

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_number")
    private String contractNumber;

    /**
     * 中心名称
     */
    @ApiModelProperty(value = "中心名称")
    @TableField(value = "center_name")
    private String centerName;

    /**
     * 审批意见
     */
    @ApiModelProperty(value = "审批意见")
    @TableField(value = "assessment_advice")
    private String assessmentAdvice;

    /**
     * 审批人id
     */
    @ApiModelProperty(value = "审批人id")
    @TableField(value = "person_id")
    private String personId;

    /**
     * 审批人姓名
     */
    @ApiModelProperty(value = "审批人姓名")
    @TableField(value = "person_name")
    private String personName;

    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间")
    @TableField(value = "submit_time")
    private Date submitTime;

    /**
     * 审批时间
     */
    @ApiModelProperty(value = "审批时间")
    @TableField(value = "assessment_time")
    private Date assessmentTime;

    @ApiModelProperty(value = "审批类型")
    @TableField(value = "type")
    private String type;

    @ApiModelProperty(value = "年份")
    @TableField(value = "year")
    private Integer year;
}
