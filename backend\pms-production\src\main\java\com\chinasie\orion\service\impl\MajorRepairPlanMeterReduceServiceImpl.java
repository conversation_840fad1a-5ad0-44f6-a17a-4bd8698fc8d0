package com.chinasie.orion.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.bo.DataStatusNBO;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.DictConts;
import com.chinasie.orion.domain.dto.MajorRepairPlanEconomizeDTO;
import com.chinasie.orion.domain.dto.MajorRepairPlanMeterReduceDTO;
import com.chinasie.orion.domain.dto.excel.MajorRepairExportDTO;
import com.chinasie.orion.domain.dto.excel.MajorRepairPlanEconomizeExportVO;
import com.chinasie.orion.domain.dto.excel.MajorRepairPlanMeterReduceExportVO;
import com.chinasie.orion.domain.entity.JobManage;
import com.chinasie.orion.domain.entity.MajorRepairPlan;
import com.chinasie.orion.domain.entity.MajorRepairPlanEconomize;
import com.chinasie.orion.domain.entity.MajorRepairPlanMeterReduce;
import com.chinasie.orion.domain.vo.JobManageVO;
import com.chinasie.orion.domain.vo.MajorRepairPlanEconomizeVO;
import com.chinasie.orion.domain.vo.MajorRepairPlanMeterReduceVO;
import com.chinasie.orion.domain.vo.MajorRepairPlanVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.file.api.service.FileApiService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.MajorRepairPlanMeterReduceMapper;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.BasePlaceService;
import com.chinasie.orion.service.JobManageService;
import com.chinasie.orion.service.MajorRepairPlanMeterReduceService;
import com.chinasie.orion.service.MajorRepairPlanService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/12/18:18
 * @description:
 */

@Service
@Slf4j
public class MajorRepairPlanMeterReduceServiceImpl extends OrionBaseServiceImpl<MajorRepairPlanMeterReduceMapper, MajorRepairPlanMeterReduce> implements MajorRepairPlanMeterReduceService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;
    @Autowired
    private FileApiService fileApiService;


    @Autowired
    private BasePlaceService basePlaceService;

    @Autowired
    private DictRedisHelper dictRedisHelper;

    @Autowired
    private DataStatusNBO dataStatusNBO;

    private JobManageService jobManageService;

    private MajorRepairPlanService majorRepairPlanService;
    @Autowired
    public void setJobManageService(JobManageService jobManageService) {
        this.jobManageService = jobManageService;
    }
    @Autowired
    public void setMajorRepairPlanService(MajorRepairPlanService majorRepairPlanService) {
        this.majorRepairPlanService = majorRepairPlanService;
    }
    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public MajorRepairPlanMeterReduceVO detail(String id, String pageCode) throws Exception {
        MajorRepairPlanMeterReduce majorRepairPlanMeterReduce =this.getById(id);
        MajorRepairPlanMeterReduceVO result = BeanCopyUtils.convertTo(majorRepairPlanMeterReduce,MajorRepairPlanMeterReduceVO::new);
        setEveryName(Collections.singletonList(result));

        List<FileVO> fileVOList = fileApiService.getFilesByDataId(id);
        result.setFileVOList(fileVOList);
        return result;
    }

    /**
     *  新增
     *
     * * @param majorRepairPlanMeterReduceDTO
     */
    @Override
    public  String create(MajorRepairPlanMeterReduceDTO majorRepairPlanMeterReduceDTO) throws Exception {
        MajorRepairPlanMeterReduce majorRepairPlanMeterReduce =BeanCopyUtils.convertTo(majorRepairPlanMeterReduceDTO,MajorRepairPlanMeterReduce::new);
        this.save(majorRepairPlanMeterReduce);

        String rsp=majorRepairPlanMeterReduce.getId();


        List<FileDTO> fileDTOList = majorRepairPlanMeterReduceDTO.getFileDTOList();
        if(!CollectionUtils.isEmpty(fileDTOList)){
            fileDTOList.forEach(item->{
                item.setDataId(rsp);
                item.setDataType("MajorRepairPlanMeterReduce");
            });
            fileApiService.batchSaveFile(fileDTOList);
        }

        return rsp;
    }

    /**
     *  编辑
     *
     * * @param majorRepairPlanMeterReduceDTO
     */
    @Override
    public Boolean edit(MajorRepairPlanMeterReduceDTO majorRepairPlanMeterReduceDTO) throws Exception {
        MajorRepairPlanMeterReduce majorRepairPlanMeterReduce =BeanCopyUtils.convertTo(majorRepairPlanMeterReduceDTO,MajorRepairPlanMeterReduce::new);

        this.updateById(majorRepairPlanMeterReduce);

        String rsp=majorRepairPlanMeterReduce.getId();

        List<FileDTO> fileDTOList = majorRepairPlanMeterReduceDTO.getFileDTOList();
        if(!CollectionUtils.isEmpty(fileDTOList)){
            List<FileVO> fileVOList = fileApiService.getFilesByDataId(rsp);
            if(!CollectionUtils.isEmpty(fileVOList)){
                fileApiService.removeByIds(fileVOList.stream().map(FileVO::getId).distinct().collect(Collectors.toList()));
            }

            fileDTOList.forEach(item->{
                item.setId(null);
                item.setDataId(rsp);
                item.setDataType("MajorRepairPlanMeterReduce");
            });
            fileApiService.batchSaveFile(fileDTOList);
        }

        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<MajorRepairPlanMeterReduceVO> pages( Page<MajorRepairPlanMeterReduceDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<MajorRepairPlanMeterReduce> condition = new LambdaQueryWrapperX<>( MajorRepairPlanMeterReduce. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(MajorRepairPlanMeterReduce::getCreateTime);


        Page<MajorRepairPlanMeterReduce> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), MajorRepairPlanMeterReduce::new));
        MajorRepairPlanMeterReduceDTO repairPlanEconomizeDTO = pageRequest.getQuery();
        condition.leftJoin(JobManage.class, JobManage::getId,MajorRepairPlanMeterReduce::getJobManageId);
        if(Objects.nonNull(repairPlanEconomizeDTO)){
            String keyword= repairPlanEconomizeDTO.getKeyword();
            String majorRepairTurn= repairPlanEconomizeDTO.getMajorRepairTurn();
            if(StringUtils.hasText(majorRepairTurn)){
                condition.eq(MajorRepairPlanMeterReduce::getMajorRepairTurn,majorRepairTurn);
            }
            if(StringUtils.hasText(keyword)){
                condition.and(item->{
                    item.like(MajorRepairPlanMeterReduce::getJobManageNumber,keyword)
                            .or().like(JobManage::getName,keyword);
                });
            }
        }
        PageResult<MajorRepairPlanMeterReduce> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<MajorRepairPlanMeterReduceVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<MajorRepairPlanMeterReduceVO> vos = BeanCopyUtils.convertListTo(page.getContent(), MajorRepairPlanMeterReduceVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "大修计划集体剂量降低导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", MajorRepairPlanMeterReduceDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        MajorRepairPlanMeterReduceExcelListener excelReadListener = new MajorRepairPlanMeterReduceExcelListener();
        EasyExcel.read(inputStream,MajorRepairPlanMeterReduceDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<MajorRepairPlanMeterReduceDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("大修计划集体剂量降低导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<MajorRepairPlanMeterReduce> majorRepairPlanMeterReducees =BeanCopyUtils.convertListTo(dtoS,MajorRepairPlanMeterReduce::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::MajorRepairPlanMeterReduce-import::id", importId, majorRepairPlanMeterReducees, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<MajorRepairPlanMeterReduce> majorRepairPlanMeterReducees = (List<MajorRepairPlanMeterReduce>) orionJ2CacheService.get("pmsx::MajorRepairPlanMeterReduce-import::id", importId);
        log.info("大修计划集体剂量降低导入的入库数据={}", JSONUtil.toJsonStr(majorRepairPlanMeterReducees));

        this.saveBatch(majorRepairPlanMeterReducees);
        orionJ2CacheService.delete("pmsx::MajorRepairPlanMeterReduce-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::MajorRepairPlanMeterReduce-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<MajorRepairPlanMeterReduce> condition = new LambdaQueryWrapperX<>( MajorRepairPlanMeterReduce. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(MajorRepairPlanMeterReduce::getCreateTime);
        List<MajorRepairPlanMeterReduce> majorRepairPlanMeterReducees =   this.list(condition);

        List<MajorRepairPlanMeterReduceDTO> dtos = BeanCopyUtils.convertListTo(majorRepairPlanMeterReducees, MajorRepairPlanMeterReduceDTO::new);

        String fileName = "大修计划集体剂量降低数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", MajorRepairPlanMeterReduceDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<MajorRepairPlanMeterReduceVO> vos)throws Exception {
        if(CollectionUtils.isEmpty(vos)){
            return;
        }
        Map<String, String> stringStringMap = basePlaceService.allMapSimpleList();
        List<DictValueVO> metologyFiled = dictRedisHelper.getDictListByCode(DictConts.METROLOGY_FIELD);
        Map<String, String> metologyFiled_numberToName = metologyFiled.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (k1, k2) -> k1));
        List<DictValueVO> appUnitType = dictRedisHelper.getDictListByCode(DictConts.APPLICATION_UNIT_TYPE);
        Map<String, String> appUnitType_numberToName = appUnitType.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (k1, k2) -> k1));
        List<DictValueVO> majorReapairType = dictRedisHelper.getDictListByCode(DictConts.TECHNICAL_APPLICATION);
        Map<String, String> majorReapairType_numberToName = majorReapairType.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (k1, k2) -> k1));

        List<DataStatusVO> dataStatusVOList = dataStatusNBO.getDataStatusListByClassName(MajorRepairPlanMeterReduce.class.getSimpleName());
        if (CollectionUtils.isEmpty(dataStatusVOList)){
            dataStatusVOList = new ArrayList<>();
        }
        final Map<Integer, DataStatusVO> statusToVo = dataStatusVOList.stream().collect(Collectors.toMap(DataStatusVO::getStatusValue, x -> x));

        List<String> jobNumberList = new ArrayList<>();
        List<String> majorRepairTurnList = new ArrayList<>();
        for (MajorRepairPlanMeterReduceVO vo : vos) {
            vo.setDataStatus(statusToVo.getOrDefault(vo.getStatus(), new DataStatusVO()));
            String jobManageNumber = vo.getJobManageNumber();
            if(StringUtils.hasText(jobManageNumber)){
                jobNumberList.add(jobManageNumber);
            }
            String majorRepairTurn = vo.getMajorRepairTurn();
            if(StringUtils.hasText(majorRepairTurn)){
                majorRepairTurnList.add(majorRepairTurn);
            }
        }
        List<JobManageVO> jobManageVOS = jobManageService.listByJobNumberList(jobNumberList);
        Map<String,JobManageVO> map = new HashMap<>();
        if(!CollectionUtils.isEmpty(jobManageVOS)){
            map = jobManageVOS.stream().collect(Collectors.toMap(JobManageVO::getNumber, Function.identity(),(k1, k2)->k1));
        }
        List<MajorRepairPlanVO> majorRepairPlanVOS = majorRepairPlanService.listByNumberList(majorRepairTurnList);
        Map<String, MajorRepairPlanVO> idToEntity =new HashMap<>();
        if (!CollectionUtils.isEmpty(majorRepairPlanVOS)){
            idToEntity = majorRepairPlanVOS.stream().collect(Collectors.toMap(MajorRepairPlanVO::getRepairRound, Function.identity(), (k1, k2) -> k1));

        }

        Map<String, JobManageVO> finalMap = map;
        Map<String, MajorRepairPlanVO> finalIdToEntity = idToEntity;
        vos.forEach(vo->{
            vo.setBelongFieldName(metologyFiled_numberToName.getOrDefault(vo.getBelongField(),""));
            vo.setApplicationOccasionName(majorReapairType_numberToName.getOrDefault(vo.getApplicationOccasion(),""));
            vo.setApplicationBaseName(majorReapairType_numberToName.getOrDefault(vo.getApplicationBase(),""));
            vo.setApplicationCrewName(appUnitType_numberToName.getOrDefault(vo.getApplicationCrew(),""));

            // applicationUnittype
            JobManageVO jobManageVO = finalMap.get(vo.getJobManageNumber());
            if(null != jobManageVO){
                vo.setWorkJobTitle(jobManageVO.getWorkJobTitle());
                vo.setJobManageCenter(jobManageVO.getRspDeptName());
                vo.setJobManageName(jobManageVO.getName());
                vo.setIsMajorProject(jobManageVO.getIsMajorProject());
                vo.setActualEndTime(jobManageVO.getActualEndTime());
            }
            MajorRepairPlanVO orDefault = finalIdToEntity.getOrDefault(vo.getMajorRepairTurn(), new MajorRepairPlanVO());
            if (Objects.nonNull(orDefault)){
                try {
                    Map<Integer, DataStatusVO> repairPlanStatusMap = dataStatusNBO.getDataStatusMapByClassName(MajorRepairPlan.class.getSimpleName());
                    orDefault.setDataStatus(repairPlanStatusMap.getOrDefault(orDefault.getStatus(),new DataStatusVO()));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            vo.setMajorRepairStatusName(
                    orDefault.getDataStatus()==null?"":orDefault.getDataStatus().getName()
            );
        });

    }

    @Override
    public List<String> listByRepairRound(String repairRound) {
        LambdaQueryWrapperX<MajorRepairPlanMeterReduce> condition = new LambdaQueryWrapperX<>( MajorRepairPlanMeterReduce. class);
        if(StringUtils.hasText(repairRound)){
            condition.eq(MajorRepairPlanMeterReduce::getMajorRepairTurn,repairRound);
        }
        condition.select(MajorRepairPlanEconomize::getJobManageNumber);
        List<MajorRepairPlanMeterReduce> majorRepairPlanEconomizeList = this.list(condition);
        if(!CollectionUtils.isEmpty(majorRepairPlanEconomizeList)){
            return  majorRepairPlanEconomizeList.stream().map(MajorRepairPlanMeterReduce::getJobManageNumber).distinct().collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    @Override
    public void export(MajorRepairExportDTO majorRepairExportDTO, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<MajorRepairPlanMeterReduce> condition = new LambdaQueryWrapperX<>( MajorRepairPlanMeterReduce. class);
        if(StringUtils.hasText(majorRepairExportDTO.getRepairRound())){
            condition.eq(MajorRepairPlanMeterReduce::getMajorRepairTurn,majorRepairExportDTO.getRepairRound());
        }
        List<MajorRepairPlanMeterReduce> meterReduces = this.list(condition);
        List<MajorRepairPlanMeterReduceExportVO> reduceExportVOList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(meterReduces)){
            List<MajorRepairPlanMeterReduceVO> vos = BeanCopyUtils.convertListTo(meterReduces, MajorRepairPlanMeterReduceVO::new);
            setEveryName(vos);
            vos.forEach(item->{
                MajorRepairPlanMeterReduceExportVO exportVO=BeanCopyUtils.convertTo(item, MajorRepairPlanMeterReduceExportVO::new);
                exportVO.setIsReduce(Objects.isNull(item.getIsReduce())?"":Objects.equals(item.getIsReduce(),true)?"是":"否");
                exportVO.setIsContinueUse(Objects.isNull(item.getIsContinueUse())?"":Objects.equals(item.getIsContinueUse(),true)?"是":"否");
                exportVO.setIsMajorProject(Objects.isNull(item.getIsMajorProject())?"":Objects.equals(item.getIsMajorProject(),true)?"是":"否");
                exportVO.setApplicationBaseName(item.getApplicationBase());
                DataStatusVO dataStatusVO= item.getDataStatus();
                if(Objects.nonNull(dataStatusVO)){
                    exportVO.setStatusName(dataStatusVO.getName());
                }
                exportVO.setTimestamp(Objects.isNull(item.getActualEndTime())?0L:item.getActualEndTime().getTime());
                exportVO.setActualEndTime(item.getActualEndTime());
                reduceExportVOList.add(exportVO);
            });
            reduceExportVOList.sort(Comparator.comparing(
                            MajorRepairPlanMeterReduceExportVO::getTimestamp).reversed()
            );
            AtomicInteger i = new AtomicInteger(1);
            reduceExportVOList.forEach(item->{
                item.setSort(i.get());
                i.getAndIncrement();
            });

        }
        String fileName = "集体剂量降低.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", MajorRepairPlanMeterReduceExportVO.class,reduceExportVOList );


    }


    public static class MajorRepairPlanMeterReduceExcelListener extends AnalysisEventListener<MajorRepairPlanMeterReduceDTO> {

        private final List<MajorRepairPlanMeterReduceDTO> data = new ArrayList<>();

        @Override
        public void invoke(MajorRepairPlanMeterReduceDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<MajorRepairPlanMeterReduceDTO> getData() {
            return data;
        }
    }


}
