package com.chinasie.orion.handler.status;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.chinasie.orion.amqp.entity.ChangeStatusMessageDTO;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.amqp.handler.AbstractChangeStatusReceiver;
import com.chinasie.orion.bo.PmsMQProducer;
import com.chinasie.orion.constant.WorkHourFillStatusEnum;
import com.chinasie.orion.domain.entity.ProjectDeclare;
import com.chinasie.orion.domain.entity.WorkHourFill;
import com.chinasie.orion.sdk.domain.vo.business.ClassVO;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.util.IdUtils;
import com.chinasie.orion.service.ProjectDeclareService;
import com.chinasie.orion.service.WorkHourFillService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

/**
 * 工时填报状态变更
 */
@Component
public class WorkHourChangeStatusReceiver extends AbstractChangeStatusReceiver {

    private final Logger logger = LoggerFactory.getLogger(WorkHourChangeStatusReceiver.class);


    private static final String CURRENT_CLASS = "WorkHourFill";

    @Autowired
    private WorkHourFillService workHourFillService;

    @Resource
    private ClassRedisHelper classRedisHelper;

    @Resource
    protected PmsMQProducer mqProducer;


    @Override
    protected void process(ChangeStatusMessageDTO msg, Channel channel, Message message) {
        logger.info("工时填报状态更改消息消费：{}", msg);
        if (ObjectUtil.isNotEmpty(msg)) {
            ClassVO classVO = classRedisHelper.classInfo(IdUtils.getCode(msg.getBusinessId()));
            if (Objects.nonNull(classVO)) {
                if (CURRENT_CLASS.equals(classVO.getClassName())) {
                    msg.setClassName(classVO.getClassName());
                    ThreadUtil.execAsync(() -> {
                        try {
                            consumerCreateMessage(msg);
                        } catch (Exception e) {
                            processError(msg, channel, message, e);
                        }
                    });
                }
            }
        }
    }

    @Override
    protected void processError(ChangeStatusMessageDTO msg, Channel channel, Message message, Exception ex) {
        logger.error("项目填报状态更改消息消费异常，【{}】,message，【{}】,", JSONUtil.toJsonStr(msg), message, ex);
    }


    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = CURRENT_CLASS, durable = "true", autoDelete = "false"),
            exchange = @Exchange(value = "${orion.amqp.change-status-v2.exchange}", type = ExchangeTypes.DIRECT),
            key = CURRENT_CLASS
    ))
    @Override
    public void receiver(ChangeStatusMessageDTO msg, Channel channel, Message message) throws IOException {
        super.receiver(msg, channel, message);
    }

    /**
     * 消费消息
     *
     * @param message 消息
     */
    private void consumerCreateMessage(ChangeStatusMessageDTO message) throws Exception {
        //项目申报 状态变更
        Integer status = message.getStatus();
        String businessId = message.getBusinessId();
        WorkHourFill workHourFill = workHourFillService.getById(businessId);
        workHourFill.setStatus(status);
        boolean result = workHourFillService.updateById(workHourFill);
        if(WorkHourFillStatusEnum.AUDITED.getStatus().equals(status)){
            sendMessage(workHourFill);
        }
        logger.info("工时填报模块状态更改消息消费成功-参数:{}-结果:{}", JSONUtil.toJsonStr(message), result);
    }

    private void sendMessage(WorkHourFill workHourFill){
            Map<String, Object> messageMap = new HashMap<>();
            messageMap.put("$startDate$", workHourFill.getStartDate());
            messageMap.put("$endDate$", workHourFill.getEndDate());
            List<String> recipientIdList = new ArrayList<>();
            recipientIdList.add(workHourFill.getMemberId());
            SendMessageDTO sendMsc = SendMessageDTO.builder()
                    .businessData("")
                    .businessId(workHourFill.getId())
                    .todoStatus(0)
                    .todoType(0)
                    .urgencyLevel(0)
                    .messageMap(messageMap)
                    .businessNodeCode("Node_Work_Hour_Andit_Pass")
                    .titleMap(messageMap)
                    //.messageUrl(String.format(JUMP_URL, project.getId()))
                    .messageUrl("")
                    .messageUrlName("工时填报审核通过")
                    .recipientIdList(recipientIdList)
                    .senderId(CurrentUserHelper.getCurrentUserId())
                    .senderTime(new Date())
                    .build();
            mqProducer.sendPmsMessage(sendMsc);
    }
}
