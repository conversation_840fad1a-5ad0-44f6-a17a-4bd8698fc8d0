package com.chinasie.orion.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.domain.entity.ProjectPlanTypeAttributeValue;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.ProjectPlanTypeAttributeValueRepository;
import com.chinasie.orion.service.ProjectPlanTypeAttributeValueService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/10/11 13:48
 */
@Service
public class ProjectPlanTypeAttributeValueServiceImpl extends OrionBaseServiceImpl<ProjectPlanTypeAttributeValueRepository, ProjectPlanTypeAttributeValue> implements ProjectPlanTypeAttributeValueService {

    @Override
    public Boolean deleteByRiskIds(List<String> riskIds) throws Exception {
        return this.remove(new LambdaQueryWrapper<>(ProjectPlanTypeAttributeValue.class)
                .in(ProjectPlanTypeAttributeValue::getPlanId, riskIds));
    }
}
