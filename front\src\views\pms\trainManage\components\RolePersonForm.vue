<script setup lang="ts">
import { BasicCard, BasicForm, useForm } from 'lyra-component-vue3';
import {
  computed, h, onMounted, ref, Ref,
} from 'vue';
import Api from '/@/api';

const props = defineProps<{
  params: Record<string, any> | null
}>();

const mangeType: Ref<string> = ref('');
const schemas: any[] = [
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '角色人员信息',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  {
    field: 'contactType',
    component: 'ApiSelect',
    label: '角色',
    required: true,
    componentProps() {
      return {
        api: () => new Api('/pmi/role/list/module/PMS').fetch({}, '', 'GET'),
        labelField: 'name',
        valueField: 'code',
      };
    },
  },
  {
    field: 'manageType',
    component: 'SelectDictVal',
    label: '管理属性',
    required: true,
    componentProps({ formModel }) {
      return {
        dictNumber: 'manage_properties',
        fieldNames: { value: 'number' },
        onChange(value: string) {
          mangeType.value = value;
          switch (value) {
            case 'base_manage':
              formModel.deptId = '';
              formModel.deptName = '';
              break;
            case 'center_manage':
              formModel.baseCode = '';
              formModel.baseName = '';
              break;
          }
        },
      };
    },
  },
  {
    field: 'baseCode',
    component: 'ApiSelect',
    label: '所属基地',
    required: computed(() => mangeType.value !== 'center_manage'),
    componentProps({ formModel }) {
      return {
        disabled: formModel.manageType === 'center_manage',
        api: () => new Api('/pms/train-manage/base-place/list').fetch('', '', 'POST'),
        labelField: 'name',
        valueField: 'code',
        onChange(code: string, option: { label: string }) {
          formModel.baseName = option.label;
        },
      };
    },
  },
  {
    field: 'deptId',
    component: 'TreeSelectOrg',
    label: '所属中心',
    required: computed(() => mangeType.value !== 'base_manage'),
    componentProps({ formModel }) {
      return {
        showSearch: true,
        disabled: formModel.manageType === 'base_manage',
        onChange(_id: string, names: string[]) {
          formModel.deptName = names.join(',');
          formModel.contactPersonIdList = [];
        },
      };
    },
  },
  {
    field: 'contactPersonIdList',
    component: 'SelectUser',
    label: '人员姓名',
    rules: [
      {
        required: true,
        type: 'array',
      },
    ],
    componentProps({ formModel }) {
      return {
        selectUserModalProps: {
          treeDataApi: () => new Api('/pmi/organization/current/org/tree').fetch(
            formModel?.deptId ? [formModel.deptId] : [],
            '',
            'POST',
          ),
        },
      };
    },
  },
];

const [
  register,
  {
    validate, setFieldsValue, getFieldsValue, clearValidate,
  },
] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  if (props?.params?.id) {
    getDetails();
  }
});

const loading: Ref<boolean> = ref(false);

async function getDetails() {
  loading.value = true;
  try {
    const result: Record<string, any> = await new Api('/pms/train-contact').fetch('', props?.params?.id, 'GET');
    mangeType.value = result?.manageType;
    await setFieldsValue({
      ...result,
      contactPersonIdList: (result?.contactPersonIds && result?.contactPersonNames) ? result?.contactPersonIds?.split(',')?.map((id, index) => ({
        id,
        name: result?.contactPersonNames?.split(',')[index],
      })) : [],
    });
    await clearValidate();
  } finally {
    loading.value = false;
  }
}

defineExpose({
  async submit() {
    await validate();
    const formValues = getFieldsValue();
    return new Promise((resolve, reject) => {
      new Api('/pms/train-contact').fetch({
        ...formValues,
        contactPersonIdList: formValues.contactPersonIdList.map((item) => item.id),
        contactPersonNameList: formValues.contactPersonIdList.map((item) => item.name),
        id: props?.params?.id,
      }, props?.params?.id ? 'edit' : 'add', props?.params?.id ? 'PUT' : 'POST').then(() => {
        resolve('');
      }).catch((e) => {
        reject(e);
      });
    });
  },
});

</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">

</style>
