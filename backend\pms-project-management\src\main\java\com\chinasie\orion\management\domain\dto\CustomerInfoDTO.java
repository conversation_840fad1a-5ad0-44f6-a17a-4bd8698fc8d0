package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import java.io.Serializable;

import java.lang.String;
import java.util.Date;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * customerInfo DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-30 16:25:24
 */
@ApiModel(value = "customerInfoDTO对象", description = "客户管理")
@Data
@ExcelIgnoreUnannotated
public class CustomerInfoDTO extends ObjectDTO implements Serializable {

    /**
     * 冗余字段
     */
    @ApiModelProperty(value = "冗余字段")
    private String comnumber;

    @ApiModelProperty(value = "客户编码")
    @ExcelProperty(value = "客户编码", index = 0)
    private String id;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    @ExcelProperty(value = "客户名称 ", index = 1)
    private String cusName;

    /**
     * 业务收入类型
     */
    @ApiModelProperty(value = "业务收入类型")
    @ExcelProperty(value = "业务收入类型 ", index = 2)
    private String ywsrlx;

    /**
     * 所属行业
     */
    @ApiModelProperty(value = "所属行业")
    @ExcelProperty(value = "所属行业 ", index = 3)
    private String industry;

    /**
     * 客户关系(集团内外)
     */
    @ApiModelProperty(value = "客户关系(集团内外)")
    @ExcelProperty(value = "客户关系", index = 4)
    private String groupInOut;

    /**
     * 客户状态
     */
    @ApiModelProperty(value = "客户状态")
    @ExcelProperty(value = "客户状态 ", index = 5)
    private String cusStatus;

    /**
     * 客户范围
     */
    @ApiModelProperty(value = "客户范围")
    @ExcelProperty(value = "客户范围 ", index = 6)
    private String busScope;

    /**
     * 所属集团
     */
    @ApiModelProperty(value = "所属集团")
    @ExcelProperty(value = "所属集团 ", index = 7)
    private String groupInfo;

    /**
     * 国标行业门类
     */
    @ApiModelProperty(value = "国标行业门类")
    @ExcelProperty(value = "国标行业门类 ", index = 8)
    private String category;


    /**
     * 国标行业大类
     */
    @ApiModelProperty(value = "国标行业大类")
    @ExcelProperty(value = "国标行业大类 ", index = 9)
    private String largeCategory;

    /**
     * 国标行业中类
     */
    @ApiModelProperty(value = "国标行业中类")
    @ExcelProperty(value = "国标行业中类 ", index = 10)
    private String middleCategory;

    /**
     * 客户级别
     */
    @ApiModelProperty(value = "客户级别")
    @ExcelProperty(value = "客户级别 ", index = 11)
    private String cusLevel;

    /**
     * 国家
     */
    @ApiModelProperty(value = "国家")
    @ExcelProperty(value = "国家 ", index = 12)
    private String country;

    /**
     * 所属省份
     */
    @ApiModelProperty(value = "所属省份")
    @ExcelProperty(value = "所属省份 ", index = 13)
    private String province;

    /**
     * 所属城市
     */
    @ApiModelProperty(value = "所属城市")
    @ExcelProperty(value = "所属城市 ", index = 14)
    private String city;


    /**
     * 所属区县
     */
    @ApiModelProperty(value = "所属区县")
    @ExcelProperty(value = "所属区县 ", index = 15)
    private String county;

    /**
     * 客户联系地址
     */
    @ApiModelProperty(value = "客户联系地址")
    @ExcelProperty(value = "客户联系地址 ", index = 16)
    private String cusAddress;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    @ExcelProperty(value = "联系电话 ", index = 17)
    private String tel;


    /**
     * 其他电话
     */
    @ApiModelProperty(value = "其他电话")
    @ExcelProperty(value = "其他电话 ", index = 18)
    private String otherTel;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    @ExcelProperty(value = "邮箱 ", index = 19)
    private String email;

    /**
     * 企业全称
     */
    @ApiModelProperty(value = "企业全称")
    @ExcelProperty(value = "企业全称 ", index = 20)
    private String cusFullName;

    /**
     * 英文名
     */
    @ApiModelProperty(value = "英文名")
    @ExcelProperty(value = "英文名 ", index = 21)
    private String englishName;

    /**
     * 法定代表人
     */
    @ApiModelProperty(value = "法定代表人")
    @ExcelProperty(value = "法定代表人 ", index = 22)
    private String legalRepr;

    /**
     * 工商注册号
     */
    @ApiModelProperty(value = "工商注册号")
    @ExcelProperty(value = "工商注册号 ", index = 23)
    private String busRegisterCode;

    /**
     * 纳税人识别号
     */
    @ApiModelProperty(value = "纳税人识别号")
    @ExcelProperty(value = "纳税人识别号 ", index = 24)
    private String taxIdCode;



    /**
     * 组织机构代码
     */
    @ApiModelProperty(value = "组织机构代码")
    @ExcelProperty(value = "组织机构代码 ", index = 25)
    private String organizatioinCode;

    /**
     * 税务登记证号
     */
    @ApiModelProperty(value = "税务登记证号")
    @ExcelProperty(value = "税务登记证号 ", index = 26)
    private String comtaxnumber;


    /**
     * 核准日期
     */
    @ApiModelProperty(value = "核准日期")
    @ExcelProperty(value = "核准日期 ", index = 27 )
    private Date approvedDate;

    /**
     * 资质信息
     */
    @ApiModelProperty(value = "资质信息")
    @ExcelProperty(value = "税务登记证号 ", index = 28)
    private String zzxx;



    /**
     * 公司类型
     */
    @ApiModelProperty(value = "公司类型")
    @ExcelProperty(value = "公司类型 ", index = 29)
    private String comtpye;

    /**
     * 登记状态
     */
    @ApiModelProperty(value = "登记状态")
    @ExcelProperty(value = "登记状态 ", index = 30)
    private String registStatus;

    /**
     * 注册资本
     */
    @ApiModelProperty(value = "注册资本")
    @ExcelProperty(value = "注册资本 ", index = 31)
    private String registeredCapital;

    /**
     * 实缴资本
     */
    @ApiModelProperty(value = "实缴资本")
    @ExcelProperty(value = "实缴资本 ", index = 32)
    private String paidInCapital;

    /**
     * 营业期限
     */
    @ApiModelProperty(value = "营业期限")
    @ExcelProperty(value = "营业期限 ", index = 33)
    private String bizPeriod;

    /**
     * 注册地址
     */
    @ApiModelProperty(value = "注册地址")
    @ExcelProperty(value = "注册地址 ", index = 34)
    private String registeredAddress;


    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    @ExcelProperty(value = "统一社会信用代码 ", index = 35)
    private String uniformCreditCode;

    /**
     * 企业类型
     */
    @ApiModelProperty(value = "企业类型")
    @ExcelProperty(value = "企业类型 ", index = 36)
    private String cusCategory;

    /**
     * 成立日期
     */
    @ApiModelProperty(value = "成立日期")
    @ExcelProperty(value = "成立日期 ", index = 37)
    private Date registrationTime;


    /**
     * 经营范围
     */
    @ApiModelProperty(value = "经营范围")
    @ExcelProperty(value = "经营范围 ", index = 38)
    private String businessScope;

    /**
     * 企业规模
     */
    @ApiModelProperty(value = "企业规模")
    @ExcelProperty(value = "企业规模 ", index = 39)
    private String cusNumCount;



    /**
     * 公众号信息
     */
    @ApiModelProperty(value = "公众号信息")
    @ExcelProperty(value = "公众号信息 ", index = 40)
    private String publicAccountInfo;

    /**
     * 是否被使用
     */
    @ApiModelProperty(value = "是否被使用0-没有被使用,1-被使用")
    @ExcelProperty(value = "公众号信息 ", index = 41)
    private String isUsed;

    /**
     * 是否关联人士
     */
    @ApiModelProperty(value = "是否关联人士")
    private String isPerson;

    /**
     * 客户编码
     */
    @ApiModelProperty(value = "客户编码")
    private String cusNumber;

    /**
     * 所属基地
     */
    @ApiModelProperty(value = "所属基地")
    @ExcelProperty(value = "所属基地 ", index = 42)
    private String homeBase;

    /**
     * 所属基地
     */
    @ApiModelProperty(value = "所属基地名称")
    private String homeBaseName;

    /**
     * 销售业务分类
     */
    @ApiModelProperty(value = "销售业务分类")
    @ExcelProperty(value = "销售业务分类 ", index = 43)
    private String salesClass;

}
