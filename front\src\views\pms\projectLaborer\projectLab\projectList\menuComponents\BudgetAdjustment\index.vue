<script setup lang="ts">
import {
  BasicButton, BasicTableAction, IOrionTableActionItem, isPower, OrionTable,
} from 'lyra-component-vue3';
import {
  computed, ComputedRef, h, inject, ref, Ref,
} from 'vue';
import { Modal } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { openFormDrawer } from './utils';
import BudgetAdjustmentEdit from './BudgetAdjustmentEdit.vue';
import Api from '/@/api';

const router = useRouter();
const tableRef: Ref = ref();
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const projectId:string = inject('projectId');
const powerData = inject('powerData', []);
const tableOptions = {
  rowSelection: {
    onChange(keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  smallSearchField: undefined,
  columns: [
    {
      title: '调整单编号',
      dataIndex: 'number',
      customRender({ record, text }) {
        if (isPower('PMS_XMXQ_container_04_YSCB_YSTZ_02_button_03', record.rdAuthList)) {
          return h('span', {
            onClick: () => {
              router.push({
                name: 'BudgetAdjustmentDetails',
                query: {
                  id: record.id,
                },
              });
            },
            class: 'action-btn',
          }, text);
        }
        return text;
      },
    },
    {
      title: '调整标题',
      dataIndex: 'name',
    },
    {
      title: '调整说明',
      dataIndex: 'remark',
    },
    {
      title: '调整预算金额',
      dataIndex: 'adjustmentMoney',
      customRender({ record, text }) {
        // 添加类名的逻辑
        const className = record.adjustmentMoney < 0 ? 'negative-number' : '';
        return h('span', {
          class: className,
        }, text);
      },
    },
    {
      title: '包含调整预算条目',
      dataIndex: 'adjustmentNum',

    },
    {
      title: '状态',
      dataIndex: 'status',
      slots: {
        customRender: 'status',
      },
    },
    {
      title: '操作',
      dataIndex: 'actions',
      width: 160,
      fixed: 'right',
      slots: {
        customRender: 'actions',
      },
    },
  ],
  api: (params:Record<string, any>) => new Api('/pms/budgetAdjustmentFrom').fetch({
    ...params,
    power: {
      pageCode: 'PMS0004',
      containerCode: 'PMS_XMXQ_container_04_YSCB_YSTZ_02',
    },
    query: {
      projectId,
    },
    // power: {
    //   containerCode: 'table-list-container-demo111-KcBsejQf',
    //   pageCode: 'list-container-demo111',
    // },
  }, 'page', 'POST'),

};

const toolButtons: ComputedRef<Record<string, any>[]> = computed(() => [
  {
    event: 'add',
    text: '创建调整单',
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
    code: 'PMS_XMXQ_container_04_YSCB_YSTZ_01_button_01',
  },
  {
    event: 'batchDelete',
    text: '删除',
    icon: 'sie-icon-shanchu',
    disabled: (selectedRows.value.length === 0 || selectedRows.value.some((item) => item.status !== 120)),
    code: 'PMS_XMXQ_container_04_YSCB_YSTZ_01_button_02',
  },
]);

function toolClick({ event }: Record<string, any>) {
  switch (event) {
    case 'add':
      openFormDrawer(BudgetAdjustmentEdit, projectId, null, updateTable);
      break;
    case 'batchDelete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除已勾选的数据吗？',
        onOk: () => deleteApi(selectedRows.value.map((item) => item.id)),
      });
      break;
    case 'enable':
      break;
    case 'disable':
      break;
  }
}

const actions: IOrionTableActionItem[] = [
  {
    text: '编辑',
    event: 'edit',
    isShow: (record) => record.status === 120 && isPower('PMS_XMXQ_container_04_YSCB_YSTZ_02_button_01', record.rdAuthList),
  },
  {
    text: '删除',
    event: 'delete',
    isShow: (record) => record.status === 120 && isPower('PMS_XMXQ_container_04_YSCB_YSTZ_02_button_02', record.rdAuthList),
  },
];

function actionClick(actionItem: { event: string }, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'edit':
      openFormDrawer(BudgetAdjustmentEdit, projectId, record, updateTable);
      break;
    case 'view':
      router.push({
        name: 'BudgetAdjustmentDetails',
        params: {
          id: record.id,
        },
      });
      break;
    case 'delete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除这条数据吗？',
        onOk: () => deleteApi([record.id]),
      });
      break;
  }
}

function updateTable() {
  tableRef.value?.reload();
}

function deleteApi(ids: string[]) {
  return new Promise((resolve) => {
    new Api('/pms/budgetAdjustmentFrom').fetch(ids, 'remove', 'DELETE').then(() => {
      updateTable();
    }).finally(() => {
      resolve('');
    });
  });
}

</script>

<template>
  <OrionTable
    ref="tableRef"
    v-get-power="{powerData}"
    :options="tableOptions"
  >
    <template #toolbarLeft>
      <template
        v-for="button in toolButtons"
        :key="button.event"
      >
        <BasicButton
          v-is-power="[button.code]"
          v-bind="(button)"
          @click="toolClick(button)"
        >
          {{ button.text }}
        </BasicButton>
      </template>
    </template>
    <template #actions="{record}">
      <BasicTableAction
        :actions="actions"
        :record="record"
        @actionClick="actionClick($event,record)"
      />
    </template>
  </OrionTable>
</template>

<style scoped lang="less">

</style>
