<script setup lang="ts">
import { downLoadById } from 'lyra-component-vue3';

withDefaults(defineProps<{
  showDelete?: boolean,
  showDownload?: boolean,
  fileList: Array<{
    name: string,
    filePostfix: string
  }>
}>(), {
  showDownload: false,
  showDelete: true,
});
const emits = defineEmits<{
  (e: 'delete', index: number): void
}>();

</script>

<template>
  <div
    v-if="fileList?.length"
    class="file-list"
  >
    <div
      v-for="(item,index) in fileList"
      :key="index"
      class="file-item"
    >
      <span
        class="file-name action-btn"
      >{{ item?.name }}.{{ item?.filePostfix }}</span>
      <!--      <span-->
      <!--        v-if="showDownload"-->
      <!--        class="action-btn"-->
      <!--        @click="downLoadById(item['id'])"-->
      <!--      >下载</span>-->
      <span />
      <span
        v-if="showDelete"
        class="action-btn"
        @click="()=>$emit('delete',index)"
      >删除</span>
    </div>
  </div>
</template>

<style scoped lang="less">
.file-list {
  display: flex;
  flex-direction: column;
  padding: 12px 0;

  .file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 30px;
    line-height: 30px;
    border-radius: 15px;
    padding: 0 20px;

    & + .file-item {
      margin-top: 10px;
    }

    &:hover {
      background-color: #f4f4f4;
    }

    .file-name {
      width: 0;
      flex-grow: 1;
      margin-right: ~`getPrefixVar('content-margin')`;
      text-decoration: underline;
    }
  }
}

.action-btn + .action-btn {
  margin-left: ~`getPrefixVar('button-margin')`;
}
</style>
