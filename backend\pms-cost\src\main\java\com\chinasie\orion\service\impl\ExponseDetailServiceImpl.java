package com.chinasie.orion.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.bo.CodeBo;
import com.chinasie.orion.bo.LyraFileBO;
import com.chinasie.orion.constant.ClassNameConstant;
import com.chinasie.orion.constant.FileConstant;
import com.chinasie.orion.domain.dto.ExponseDetailDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.ExponseDetailVO;
import com.chinasie.orion.domain.vo.ProjectBudgetVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.*;
import com.chinasie.orion.repository.BudgetMonthMapper;
import com.chinasie.orion.repository.ExponseDetailMapper;
import com.chinasie.orion.repository.ProjectBudgetMapper;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.CostCenterService;
import com.chinasie.orion.service.ExpenseSubjectService;
import com.chinasie.orion.service.ExponseDetailService;
import com.chinasie.orion.service.ProjectBudgetService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.PmsAuthUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * ExponseDetail 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16 15:22:28
 */
@Service
public class ExponseDetailServiceImpl extends OrionBaseServiceImpl<ExponseDetailMapper, ExponseDetail> implements ExponseDetailService {

    @Resource
    private ExponseDetailMapper exponseDetailMapper;

    @Resource
    private ProjectBudgetMapper projectBudgetMapper;

    @Resource
    private BudgetMonthMapper budgetMonthMapper;

    @Resource
    private CostCenterService costCenterService;

    @Resource
    private ExpenseSubjectService expenseSubjectService;

    @Resource
    private ProjectBudgetService projectBudgetService;
    @Resource
    private LyraFileBO fileBo;

    @Resource
    private UserRedisHelper userRedisHelper;
    @Autowired
    private PmsAuthUtil pmsAuthUtil;


    @Resource
    private CodeBo codeBo;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public ExponseDetailVO detail(String id, String pageCode) throws Exception {
        ExponseDetail exponseDetail = exponseDetailMapper.selectById(id);
        ExponseDetailVO result = BeanCopyUtils.convertTo(exponseDetail, ExponseDetailVO::new);
        // 权限设置
        if (StrUtil.isNotBlank(pageCode)) {
            String currentUserId = CurrentUserHelper.getCurrentUserId();
            List<String> roleCodeList = pmsAuthUtil.getRoleCodeList(result.getProjectId(), currentUserId);
            pmsAuthUtil.setDetailAuths(result, currentUserId, pageCode, result.getDataStatus(), ExponseDetailVO::setDetailAuthList, result.getCreatorId(), result.getModifyId(), result.getOwnerId(), roleCodeList);
        }
        return result;
    }

    /**
     * 新增
     * <p>
     * * @param exponseDetailDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ExponseDetailVO create(ExponseDetailDTO exponseDetailDTO) throws Exception {
        BigDecimal money = exponseDetailDTO.getOutMoney();
        if (money.compareTo(BigDecimal.ZERO) < 0) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "支出金额不能为负数");
        }
        String budgetProjectId = exponseDetailDTO.getBudgetProjectId();
        List<ProjectBudget> projectBudgets = projectBudgetMapper.selectList(new LambdaQueryWrapperX<ProjectBudget>()
                .select(ProjectBudget::getProjectId)
                .eq(ProjectBudget::getId, budgetProjectId));
        if (CollectionUtil.isEmpty(projectBudgets) || !projectBudgets.get(0).getProjectId().equals(exponseDetailDTO.getProjectId())) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "当前预算信息不存在，请仔细检查");
        }
//        List<SysCodeSegmentVO> codeRuleList = codeBo.getCodeRuleList(ClassNameConstant.EXPONSE_DETAIL,ClassNameConstant.NUMBER);
//        if (!CollectionUtils.isEmpty(codeRuleList)) {
//            String code = codeBo.getCode(codeRuleList);
//            exponseDetailDTO.setNumber(code);
//        }

        String code = codeBo.createCode(ClassNameConstant.EXPONSE_DETAIL, ClassNameConstant.NUMBER, false, null);
        if (BeanUtil.isNotEmpty(code)) {
            exponseDetailDTO.setNumber(code);
        }
        ExponseDetail exponseDetail = BeanCopyUtils.convertTo(exponseDetailDTO, ExponseDetail::new);
        int insert = exponseDetailMapper.insert(exponseDetail);
        BigDecimal outMoney = exponseDetail.getOutMoney();
        ProjectBudget projectBudget = projectBudgetMapper.selectOne(new LambdaQueryWrapperX<ProjectBudget>()
                .eq(ProjectBudget::getId, exponseDetail.getBudgetProjectId()));
        BigDecimal totalCost = projectBudget.getTotalCost();
        if (BeanUtil.isNotEmpty(totalCost)) {
            projectBudget.setTotalCost(totalCost.add(outMoney));
        } else {
            projectBudget.setTotalCost(outMoney);
        }
        // 计算差异
        BigDecimal yearExpense = projectBudget.getYearExpense();
        BigDecimal subtract = yearExpense.subtract(projectBudget.getTotalCost());
        projectBudget.setPriceDifference(subtract.abs());
        int status = subtract.compareTo(BigDecimal.ZERO);
        if (status > 0) {
            projectBudget.setIsOut(0);
        } else {
            projectBudget.setIsOut(1);
        }
        projectBudgetMapper.updateById(projectBudget);
        BudgetMonth budgetMonth = budgetMonthMapper.selectOne(new LambdaQueryWrapperX<BudgetMonth>()
                .eq(BudgetMonth::getBudgetProjectId, exponseDetail.getBudgetProjectId()));
        BigDecimal yearFactExpense = budgetMonth.getYearFactExpense();
        if (BeanUtil.isNotEmpty(yearFactExpense)) {
            budgetMonth.setYearFactExpense(totalCost.add(outMoney));
        } else {
            budgetMonth.setYearFactExpense(outMoney);
        }
        budgetMonthMapper.updateById(budgetMonth);
        ExponseDetailVO rsp = BeanCopyUtils.convertTo(exponseDetail, ExponseDetailVO::new);

        String id = exponseDetail.getId();

        // 调用res服务保存附件信息
        List<FileVO> attachments = Optional.ofNullable(exponseDetailDTO.getAttachments()).orElse(new ArrayList<>());
        attachments.forEach(f -> {
            f.setDataId(id);
            f.setDataType(FileConstant.FILETYPE_EXPONSEDETAIL_FILE);
        });
        if (CollectionUtil.isNotEmpty(attachments)) {
            List<FileDTO> fileDTOS = BeanCopyUtils.convertListTo(attachments, FileDTO::new);
            fileBo.addBatch(fileDTOS);
        }

        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param exponseDetailDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean edit(ExponseDetailDTO exponseDetailDTO) throws Exception {
        String budgetProjectId1 = exponseDetailDTO.getBudgetProjectId();
        List<ProjectBudget> projectBudgets = projectBudgetMapper.selectList(new LambdaQueryWrapperX<ProjectBudget>()
                .select(ProjectBudget::getProjectId)
                .eq(ProjectBudget::getId, budgetProjectId1));
        if (CollectionUtil.isEmpty(projectBudgets) || !projectBudgets.get(0).getProjectId().equals(exponseDetailDTO.getProjectId())) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "当前预算信息不存在，请仔细检查");
        }

        ExponseDetail exponseDetail1 = getById(exponseDetailDTO.getId());
        BigDecimal newOutMoney = exponseDetailDTO.getOutMoney();
        if (exponseDetailDTO.getBudgetProjectId().equals(exponseDetail1.getBudgetProjectId()) && !newOutMoney.equals(exponseDetail1.getOutMoney())) {
            String budgetProjectId = exponseDetailDTO.getBudgetProjectId();
            ProjectBudget projectBudget = projectBudgetMapper.selectOne(new LambdaQueryWrapperX<ProjectBudget>().eq(ProjectBudget::getId, budgetProjectId));
            BudgetMonth budgetMonth = budgetMonthMapper.selectOne(new LambdaQueryWrapperX<BudgetMonth>().eq(BudgetMonth::getBudgetProjectId, budgetProjectId));
            // 修改项目预算的总成本、差异值、是否超预算
            BigDecimal oldOutMoney = exponseDetail1.getOutMoney();
            BigDecimal totalCost = projectBudget.getTotalCost();
            BigDecimal totalMoney = totalCost.subtract(oldOutMoney);
            totalMoney = totalMoney.add(newOutMoney);
            projectBudget.setTotalCost(totalMoney);
            BigDecimal difference = projectBudget.getYearExpense().subtract(totalMoney);
            projectBudget.setPriceDifference(difference.abs());
            if (difference.compareTo(BigDecimal.ZERO) < 0) {
                projectBudget.setIsOut(1);
            } else {
                projectBudget.setIsOut(0);
            }
            budgetMonth.setYearFactExpense(totalMoney);
            projectBudgetMapper.updateById(projectBudget);
            budgetMonthMapper.updateById(budgetMonth);
        }
        ExponseDetail exponseDetail = BeanCopyUtils.convertTo(exponseDetailDTO, ExponseDetail::new);
        int update = exponseDetailMapper.updateById(exponseDetail);
        List<FileVO> files = exponseDetailDTO.getAttachments();
        List<FileVO> oldFilesResult = fileBo.getFilesByDataId(exponseDetail.getId());
        if (CollectionUtil.isNotEmpty(oldFilesResult)) {
//            List<FileVO> oldFilesResult = oldFiles.getResult();
            List<String> oldFileIds = oldFilesResult.stream()
                    .map(FileVO::getId)
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(oldFileIds)) {
                fileBo.deleteFileByIds(oldFileIds);
            }
        }
        files.forEach(f -> {
            f.setDataId(exponseDetail.getId());
            f.setId(null);
            f.setDataType(FileConstant.FILETYPE_EXPONSEDETAIL_FILE);
        });
        if (CollectionUtil.isNotEmpty(files)) {
            List<FileDTO> fileDTOS = BeanCopyUtils.convertListTo(files, FileDTO::new);
            fileBo.addBatch(fileDTOS);
        }
        return SqlHelper.retBool(update);
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(List<String> ids) throws Exception {
        List<ExponseDetail> exponseDetails = list(new LambdaQueryWrapperX<ExponseDetail>()
                .in(ExponseDetail::getId, ids)
                .select(ExponseDetail::getBudgetProjectId, ExponseDetail::getOutMoney));
        Map<String, BigDecimal> monthMap = new HashMap<>();
        Map<String, List<ExponseDetail>> exponseMap = exponseDetails.stream().collect(Collectors.groupingBy(ExponseDetail::getBudgetProjectId));
        exponseMap.forEach((key, value) -> {
            BigDecimal outMoney = value.stream().map(ExponseDetail::getOutMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
            monthMap.put(key, outMoney);
        });
        // 修改总成本、差异、是否超预算
        List<String> projectBudgets = exponseDetails.stream().map(ExponseDetail::getBudgetProjectId).collect(Collectors.toList());
        List<ProjectBudget> projectBudgetList = projectBudgetMapper.selectList(new LambdaQueryWrapperX<ProjectBudget>().in(ProjectBudget::getId, projectBudgets));
        List<BudgetMonth> budgetMonths = budgetMonthMapper.selectList(new LambdaQueryWrapperX<BudgetMonth>().in(BudgetMonth::getBudgetProjectId, projectBudgets));
        projectBudgetList.forEach(e -> {
            BigDecimal money = monthMap.get(e.getId());
            BigDecimal add = e.getTotalCost().subtract(money);
            // 总成本
            e.setTotalCost(add);
            // 差异
            BigDecimal subtract = e.getYearExpense().subtract(add);
            e.setPriceDifference(subtract.abs());
            // 是否超预算
            if (subtract.compareTo(BigDecimal.ZERO) < 0) {
                e.setIsOut(1);
            } else {
                e.setIsOut(0);
            }
        });
        budgetMonths.forEach(e -> {
            BigDecimal money = monthMap.get(e.getBudgetProjectId());
            BigDecimal add = e.getYearFactExpense().subtract(money);
            e.setYearFactExpense(add);
        });
        budgetMonthMapper.updateBatch(budgetMonths, budgetMonths.size());
        projectBudgetMapper.updateBatch(projectBudgetList, projectBudgetList.size());
        int delete = exponseDetailMapper.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }

    @Override
    public Page<ExponseDetailVO> getExponseDetailVOPage(Page<ExponseDetailDTO> pageRequest) throws Exception {
        Page<ExponseDetailVO> resultPage = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), 0L, new ArrayList<>());
        pmsAuthUtil.setHeaderAuths(resultPage, CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), Page::setHeadAuthList, new ArrayList<>());

        ExponseDetailDTO query = pageRequest.getQuery();
        String budgetProjectId = query.getBudgetProjectId();
        LambdaQueryWrapperX<ExponseDetail> pageCondition = new LambdaQueryWrapperX<>(ExponseDetail.class);
        String projectId = query.getProjectId();
        if (StringUtils.hasText(projectId)) {
            pageCondition.eq(ExponseDetail::getProjectId, projectId);
        }
        pageCondition.eq(ExponseDetail::getBudgetProjectId, budgetProjectId);
        pageCondition.orderByDesc(ExponseDetail::getOutTime);
//        List<ConditionItem> conditionItems = pageRequest.getQueryCondition();
//        if (CollectionUtils.isEmpty(conditionItems)) {
//            conditionItems = new ArrayList<>();
//        }
//        Map<String, ConditionItem> conditionItemMap = QueryConditionUtil.conditionListTurnToMap(conditionItems);
//        ConditionItem nameOfConditionItem = conditionItemMap.getOrDefault("name", new ConditionItem());
//        Object value = nameOfConditionItem.getValue();
//        if (Objects.nonNull(value)) {
//            pageCondition.and(sub -> sub.like(ExponseDetail::getName, value).or().like(ExponseDetail::getNumber, value));
//        }
        List<List<SearchCondition>> searchConditions = pageRequest.getSearchConditions();
        if (CollectionUtil.isNotEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, pageCondition);
        }
        Page<ExponseDetail> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        PageResult<ExponseDetail> pageResult = exponseDetailMapper.selectPage(realPageRequest, pageCondition);
        List<ExponseDetail> records = pageResult.getContent();

        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(records)) {
            return resultPage;
        }
        List<String> costCenterIds = records.stream().map(ExponseDetail::getCostCenterId).distinct().collect(Collectors.toList());
        List<String> expenseSubjectIds = records.stream().map(ExponseDetail::getExpenseAccountId).distinct().collect(Collectors.toList());
        List<String> budgetProjectIds = records.stream().map(ExponseDetail::getBudgetProjectId).distinct().collect(Collectors.toList());
        Map<String, CostCenter> cosCenterMap = costCenterService.getCosCenterMap(costCenterIds);
        Map<String, ExpenseSubject> expenseSubjectMap = expenseSubjectService.getExpenseSubjectMap(expenseSubjectIds);
        Map<String, ProjectBudget> projectBudgetMap = projectBudgetService.getProjectBudgetMap(budgetProjectIds);
        Map<String, UserVO> userNameMap = this.getUserName();
        records.forEach(e -> {
            CostCenter costCenter = cosCenterMap.getOrDefault(e.getCostCenterId(), new CostCenter());
            ExpenseSubject expenseSubject = expenseSubjectMap.getOrDefault(e.getExpenseAccountId(), new ExpenseSubject());
            ProjectBudget budget = projectBudgetMap.getOrDefault(e.getBudgetProjectId(), new ProjectBudget());
            e.setCostCenterName(costCenter.getName());
            e.setExpenseAccountName(expenseSubject.getName());
            e.setBudgetProjectName(budget.getName());
            e.setOutPersonName(userNameMap.getOrDefault(e.getOutPersonId(), new UserVO()).getName());
        });
        List<ExponseDetailVO> exponseDetailVOS = BeanCopyUtils.convertListTo(records, ExponseDetailVO::new);
        exponseDetailVOS.forEach(e -> {
            List<FileVO> responseDTOByGetFiles = fileBo.getFilesByDataId(e.getId());
            if (!CollectionUtils.isEmpty(responseDTOByGetFiles)) {
                e.setAttachments(responseDTOByGetFiles);
            } else {
                e.setAttachments(Collections.EMPTY_LIST);
            }
        });
        //权限设置
        Map<String, List<String>> dataRoleMap = getDataRoleMap(exponseDetailVOS);
        pmsAuthUtil.setRowAuths(CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), exponseDetailVOS, ExponseDetailVO::getId, ExponseDetailVO::getDataStatus, ExponseDetailVO::setRdAuthList,
                ExponseDetailVO::getCreatorId,
                ExponseDetailVO::getModifyId,
                ExponseDetailVO::getOwnerId,
                dataRoleMap);
        resultPage.setTotalSize(pageResult.getTotalSize());
        resultPage.setContent(exponseDetailVOS);
        return resultPage;
    }

    public Map<String, List<String>> getDataRoleMap(List<ExponseDetailVO> vos) throws Exception {
        Map<String, List<String>> dataRoleCodeMap = new HashMap<>();
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        for (ExponseDetailVO v : vos) {
            List<String> roles = pmsAuthUtil.getRoleCodeList(v.getProjectId(), currentUserId);
            dataRoleCodeMap.put(v.getId(), roles);
        }
        return dataRoleCodeMap;
    }

    @Override
    public Page<ExponseDetailVO> getExponseDetailVOPage1(Page<ExponseDetailDTO> pageRequest) throws Exception {
        Page<ExponseDetailVO> resultPage = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), 0L, new ArrayList<>());
        pmsAuthUtil.setHeaderAuths(resultPage, CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), Page::setHeadAuthList, new ArrayList<>());


        LambdaQueryWrapperX<ExponseDetail> pageCondition = new LambdaQueryWrapperX<>();
        ExponseDetailDTO query = pageRequest.getQuery();
        String projectId = query.getProjectId();
        if (StringUtils.hasText(projectId)) {
            pageCondition.eq(ExponseDetail::getProjectId, projectId);
        }
        pageCondition.selectAll(ExponseDetail.class);
        pageCondition.orderByDesc(ExponseDetail::getOutTime);
        pageCondition.leftJoin(ProjectBudget.class, ProjectBudget::getId, ExponseDetail::getBudgetProjectId);
        List<List<SearchCondition>> searchConditions = pageRequest.getSearchConditions();
        if (CollectionUtil.isNotEmpty(searchConditions)) {
            pageCondition.and(wapper -> {
                for (List<SearchCondition> list : searchConditions) {
                    if (CollectionUtil.isNotEmpty(list)) {
                        wapper.or((w) -> {
                            for (SearchCondition s : list) {
                                if (s.getField().equals("name")) {
                                    w.apply("t1.name like '%" + s.getValues().get(0).toString() + "%'");
                                }
                                if (s.getField().equals("number")) {
                                    String m = s.getValues().get(0).toString();
                                    w.like(ExponseDetail::getNumber, s.getValues().get(0).toString());
                                }
                            }
                        });
                    }

                }
            });
            // SearchConditionUtils.parseSearchConditionsWrapper(searchConditions,pageCondition);
        }
        Page<ExponseDetail> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        PageResult<ExponseDetail> pageResult = exponseDetailMapper.selectPage(realPageRequest, pageCondition);
        List<ExponseDetail> records = pageResult.getContent();

        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(records)) {
            return resultPage;
        }
        List<String> costCenterIds = records.stream().map(ExponseDetail::getCostCenterId).distinct().collect(Collectors.toList());
        List<String> expenseSubjectIds = records.stream().map(ExponseDetail::getExpenseAccountId).distinct().collect(Collectors.toList());
        List<String> budgetProjectIds = records.stream().map(ExponseDetail::getBudgetProjectId).distinct().collect(Collectors.toList());
        Map<String, CostCenter> cosCenterMap = costCenterService.getCosCenterMap(costCenterIds);
        Map<String, ExpenseSubject> expenseSubjectMap = expenseSubjectService.getExpenseSubjectMap(expenseSubjectIds);
        Map<String, ProjectBudget> projectBudgetMap = projectBudgetService.getProjectBudgetMap(budgetProjectIds);
        Map<String, UserVO> userNameMap = this.getUserName();
        records.forEach(e -> {
            CostCenter costCenter = cosCenterMap.getOrDefault(e.getCostCenterId(), new CostCenter());
            ExpenseSubject expenseSubject = expenseSubjectMap.getOrDefault(e.getExpenseAccountId(), new ExpenseSubject());
            ProjectBudget budget = projectBudgetMap.getOrDefault(e.getBudgetProjectId(), new ProjectBudget());
            e.setCostCenterName(costCenter.getName());
            e.setExpenseAccountName(expenseSubject.getName());
            e.setBudgetProjectName(budget.getName());
            e.setOutPersonName(userNameMap.getOrDefault(e.getOutPersonId(), new UserVO()).getName());
        });

        List<ExponseDetailVO> exponseDetailVOS = BeanCopyUtils.convertListTo(records, ExponseDetailVO::new);
        exponseDetailVOS.forEach(e -> {
            ProjectBudget budget = projectBudgetMap.getOrDefault(e.getBudgetProjectId(), new ProjectBudget());
            CostCenter costCenter = cosCenterMap.getOrDefault(e.getCostCenterId(), new CostCenter());
            ExpenseSubject expenseSubject = expenseSubjectMap.getOrDefault(e.getExpenseAccountId(), new ExpenseSubject());
            budget.setCostCenterName(costCenter.getName());
            budget.setExpenseAccountName(expenseSubject.getName());
            ProjectBudgetVO projectBudgetVO = BeanCopyUtils.convertTo(budget, ProjectBudgetVO::new);
            e.setProjectBudgetVO(projectBudgetVO);
            List<FileVO> responseDTOByGetFiles = fileBo.getFilesByDataId(e.getId());
            if (!CollectionUtils.isEmpty(responseDTOByGetFiles)) {
                e.setAttachments(responseDTOByGetFiles);
            } else {
                e.setAttachments(Collections.EMPTY_LIST);
            }

        });
        //权限设置
        Map<String, List<String>> dataRoleMap = getDataRoleMap(exponseDetailVOS);
        pmsAuthUtil.setRowAuths(CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), exponseDetailVOS, ExponseDetailVO::getId, ExponseDetailVO::getDataStatus, ExponseDetailVO::setRdAuthList,
                ExponseDetailVO::getCreatorId,
                ExponseDetailVO::getModifyId,
                ExponseDetailVO::getOwnerId,
                dataRoleMap);
        resultPage.setTotalSize(pageResult.getTotalSize());
        resultPage.setContent(exponseDetailVOS);
        return resultPage;
    }

    @Override
    public List<String> importFiles(String id, List<FileDTO> files) throws Exception {
        List<FileVO> oldFilesResult = fileBo.getFilesByDataId(id);
        if (CollectionUtil.isNotEmpty(oldFilesResult)) {
//            List<FileVO> oldFilesResult = oldFiles.getResult();
            List<String> oldFileIds = oldFilesResult.stream()
                    .map(FileVO::getId)
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(oldFileIds)) {
                fileBo.deleteFileByIds(oldFileIds);
            }
        }
//        List<String> result = new ArrayList<>();
        files.forEach(f -> {
            f.setDataId(id);
            f.setDataType(FileConstant.FILETYPE_EXPONSEDETAIL_FILE);
            f.setId(null);
//            ResponseDTO<String> responseDTO = fileBo.addFile(f);
//            if (StrUtil.isNotBlank(responseDTO.getResult())) {
//                result.add(responseDTO.getResult());
//            }
        });
        List<String> result = fileBo.addBatch(files);
        return result;
    }

    /**
     * 获取用户信息Map
     *
     * @return 用户信息
     */
    private HashMap<String, UserVO> getUserName() {
        List<UserVO> users = userRedisHelper.getAllUser();
        HashMap<String, UserVO> userMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(users)) {
            Map<String, UserVO> collect = users.stream().collect(Collectors.toMap(UserVO::getId, e -> e));
            userMap.putAll(collect);
        }
        return userMap;
    }

}
