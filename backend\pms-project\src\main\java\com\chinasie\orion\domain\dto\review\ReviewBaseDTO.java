package com.chinasie.orion.domain.dto.review;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.domain.dto.FileInfoDTO;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Review DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:03
 */
@ApiModel(value = "ReviewBaseDTO对象", description = "项目评审详情基本信息")
@Data
public class ReviewBaseDTO implements Serializable {

    /**
     * 项目评审id
     */
    @ApiModelProperty(value = "项目评审id")
    @NotBlank(message = "项目评审id不能为空!")
    private String id;

    /**
     * 会议地点
     */
    @ApiModelProperty(value = "会议地点")
    private String reviewAddress;

    /**
     * 会议召开时间
     */
    @ApiModelProperty(value = "会议召开时间")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date reviewTime;

    /**
     * 文档齐套检查
     */
    @ApiModelProperty(value = "文档齐套检查")
    private Integer examineState;

}
