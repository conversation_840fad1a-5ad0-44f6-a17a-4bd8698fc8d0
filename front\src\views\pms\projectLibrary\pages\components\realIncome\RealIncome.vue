<script setup lang="ts">
import {
  BasicButton, isPower, OrionTable, useDrawer, useModal,
} from 'lyra-component-vue3';
import {
  computed,
  h, inject, onMounted, ref, Ref,
} from 'vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { Modal } from 'ant-design-vue';
import RealIncomeDrawer from '../realIncomeFormDrawer/RealIncomeDrawer.vue';
import ReceivableDrawer from '../receivableFormDrawer/ReceivableDrawer.vue';
import Api from '/@/api';
import { formatMoney } from '/@/views/pms/utils/utils';
import FilesModal from '/@/views/pms/projectLibrary/pages/components/filesModal/FilesModal.vue';

const [registerRealIncomeDrawer, { openDrawer: openRealIncomeDrawer }] = useDrawer();
const [registerReceivableDrawer, { openDrawer: openReceivableDrawer }] = useDrawer();
const [registerFilesModal, { openModal: openFilesModal }] = useModal();

const router = useRouter();
const keyword:Ref<string> = ref();
const projectId = inject('projectId');
const powerData = inject('powerData');
const selectKeys: Ref<string[]> = ref([]);
const tableRef: Ref = ref();
const tableData: Ref<any[]> = ref([]);
const tableOptions = {
  showToolButton: false,
  rowSelection: {},
  resizeHeightOffset: 54,
  smallSearchField: ['name', 'number'],
  api: (params:Record<string, any>) => new Api('/pms/projectFundsReceived/pages').fetch({
    ...params,
    query: {
      projectId,
      name: keyword.value || undefined,
    },
    power: {
      pageCode: 'PMS0004',
      containerCode: 'PMS_XMXQ_container_04_04',
    },
  }, '', 'POST'),
  afterFetch(data:any[]) {
    tableData.value = data;
  },
  columns: [
    {
      title: '收款编码',
      dataIndex: 'number',
      customRender({ record, text }) {
        if (isPower('PMS_XMXQ_container_04_04_button_06', record.rdAuthList)) {
          return h('div', {
            class: 'action-btn',
            onClick: () => handleDetail(record.id),
          }, text);
        }
        return text;
      },
    },
    {
      title: '客户名称',
      dataIndex: 'stakeholderName',
    },
    {
      title: '合同收款节点',
      dataIndex: 'collectionPoint',
    },
    {
      title: '实收日期',
      dataIndex: 'fundsReceivedDate',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '实收金额（元）',
      dataIndex: 'fundsReceived',
      customRender({ text }) {
        return text ? formatMoney(text) : '0';
      },
    },
    {
      title: '关联应收编码',
      dataIndex: 'receivableNumber',
      customRender({ record, text }) {
        if (isPower('PMS_XMXQ_container_04_04_button_07', record.rdAuthList)) {
          return h('div', {
            class: 'action-btn',
            onClick: () => openReceivableDetail(record),
          }, text);
        }
        return text;
      },
    },
    {
      title: '发票金额',
      dataIndex: 'invoiceMoney',
      customRender({ text }) {
        return text ? formatMoney(text) : '0';
      },
    },
    {
      title: '描述',
      dataIndex: 'remark',
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 140,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '编辑',
      isShow: (record: Record<string, any>) => isPower('PMS_XMXQ_container_04_04_button_03', record.rdAuthList),
      onClick(record:Record<string, any>) {
        openRealIncomeDrawer(true, { id: record.id });
      },
    },
    {
      text: '删除',
      isShow: (record: Record<string, any>) => isPower('PMS_XMXQ_container_04_04_button_04', record.rdAuthList),
      modal: (record:Record<string, any>) => requestBatchDel([record.id]),
    },
    {
      text: '附件',
      isShow: (record: Record<string, any>) => isPower('PMS_XMXQ_container_04_04_button_05', record.rdAuthList),
      onClick(record:Record<string, any>) {
        openFilesModal(true, { id: record.id });
      },
    },
  ],
};

const totalInvoiceMoney = computed(() => tableData.value.reduce((prev, next:Record<string, any>) => prev + Number(next.invoiceMoney || 0), 0));
const totalFundsReceived = computed(() => tableData.value.reduce((prev, next:Record<string, any>) => prev + Number(next.fundsReceived || 0), 0));

const totalRef: Ref = ref();
const cellLeft: Ref<number> = ref();
const left: Ref<number> = ref();
onMounted(() => {
  setTimeout(() => {
    cellLeft.value = (document.querySelector("div[colstart='6'].surely-table-header-cell").getBoundingClientRect().left);
    left.value = (totalRef.value.getBoundingClientRect().left);
  });
});

// 表格更新
function updateTable() {
  tableRef.value.reload();
}

function handleDetail(id:string) {
  router.push({
    name: 'RealIncomeDetail',
    params: {
      id,
    },
  });
}

// 打开应收款详情
function openReceivableDetail(record:Record<string, any>) {
  openReceivableDrawer(true, {
    operationType: 'show',
    id: record.receivableId,
  });
}

function selectionChange({ keys }) {
  selectKeys.value = keys;
}

// 批量删除
function handleBatchDel(ids:string[]) {
  Modal.confirm({
    title: '删除提示',
    content: `确认要删除已选择的${ids.length}条记录？`,
    onOk: () => requestBatchDel(ids),
  });
}

// 批量删除请求
function requestBatchDel(ids:string[]) {
  return new Promise((resolve, reject) => {
    new Api('/pms/projectFundsReceived').fetch(ids, '', 'DELETE')
      .then(() => {
        resolve('');
        updateTable();
      })
      .catch((err) => {
        reject(err);
      });
  });
}

function searchChange(value:string) {
  keyword.value = value;
}
</script>

<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
    :onSelectionChange="selectionChange"
    @smallSearchChange="searchChange"
  >
    <template #footer>
      <div class="footer-content">
        <span>汇总</span>
        <span
          ref="totalRef"
          :style="{paddingLeft:(cellLeft-left)+'px'}"
        >{{ formatMoney(totalFundsReceived) }}</span>
        <span>{{ formatMoney(totalInvoiceMoney) }}</span>
      </div>
    </template>
    <template #toolbarLeft>
      <BasicButton
        v-if="isPower('PMS_XMXQ_container_04_04_button_01',powerData)"
        type="primary"
        icon="sie-icon-tianjiaxinzeng"
        @click="openRealIncomeDrawer(true,{})"
      >
        新增收款
      </BasicButton>
      <BasicButton
        v-if="isPower('PMS_XMXQ_container_04_04_button_02',powerData)"
        icon="sie-icon-del"
        :disabled="selectKeys.length===0"
        @click="handleBatchDel(selectKeys)"
      >
        删除
      </BasicButton>
    </template>
  </OrionTable>
  <!--新增、编辑收款-->
  <RealIncomeDrawer
    @register="registerRealIncomeDrawer"
    @confirm="updateTable()"
  />
  <!--应收款详情-->
  <ReceivableDrawer @register="registerReceivableDrawer" />
  <!--附件抽屉-->
  <FilesModal @register="registerFilesModal" />
</template>

<style scoped lang="less">
.table-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: bold;
  padding: 0 100px;
}

.footer-content {
  position: relative;
  display: flex;
  align-items: center;

  span {
    font-weight: bold;
  }

  :last-child {
    margin-left: 290px;
  }
}
</style>
