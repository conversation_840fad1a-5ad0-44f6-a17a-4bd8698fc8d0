package com.chinasie.orion.service.Impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.domain.dto.NcfFormPurchOrderDTO;
import com.chinasie.orion.domain.entity.NcfFormPurchOrder;
import com.chinasie.orion.domain.vo.NcfFormPurchOrderVO;
import com.chinasie.orion.repository.NcfFormPurchOrderMapper;
import com.chinasie.orion.service.NcfFormPurchOrderService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;


/**
 * <p>
 * NcfFormPurchOrder 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-07 16:28:00
 */
@Service
@Slf4j
public class NcfFormPurchOrderServiceImpl extends OrionBaseServiceImpl<NcfFormPurchOrderMapper, NcfFormPurchOrder> implements NcfFormPurchOrderService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;
    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public NcfFormPurchOrderVO detail(String id, String pageCode) throws Exception {
        NcfFormPurchOrder ncfFormPurchOrder = this.getById(id);
        NcfFormPurchOrderVO result = BeanCopyUtils.convertTo(ncfFormPurchOrder, NcfFormPurchOrderVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param ncfFormPurchOrderDTO
     */
    @Override
    public String create(NcfFormPurchOrderDTO ncfFormPurchOrderDTO) throws Exception {
        NcfFormPurchOrder ncfFormPurchOrder = BeanCopyUtils.convertTo(ncfFormPurchOrderDTO, NcfFormPurchOrder::new);
        this.save(ncfFormPurchOrder);

        String rsp = ncfFormPurchOrder.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param ncfFormPurchOrderDTO
     */
    @Override
    public Boolean edit(NcfFormPurchOrderDTO ncfFormPurchOrderDTO) throws Exception {
        NcfFormPurchOrder ncfFormPurchOrder = BeanCopyUtils.convertTo(ncfFormPurchOrderDTO, NcfFormPurchOrder::new);

        this.updateById(ncfFormPurchOrder);

        String rsp = ncfFormPurchOrder.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<NcfFormPurchOrderVO> pages(Page<NcfFormPurchOrderDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<NcfFormPurchOrder> condition = new LambdaQueryWrapperX<>(NcfFormPurchOrder.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(NcfFormPurchOrder::getCreateTime);


        Page<NcfFormPurchOrder> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), NcfFormPurchOrder::new));

        PageResult<NcfFormPurchOrder> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<NcfFormPurchOrderVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<NcfFormPurchOrderVO> vos = BeanCopyUtils.convertListTo(page.getContent(), NcfFormPurchOrderVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public Page<NcfFormPurchOrderVO> getTotalPage(Page<NcfFormPurchOrderDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<NcfFormPurchOrder> condition = new LambdaQueryWrapperX<>(NcfFormPurchOrder.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        //根据需求只查询总订单字段
        condition.select("DISTINCT order_number,contract_name,return_amount," +
                "enterprise_name,order_placer,order_time,time_of_delivery,time_of_last_receipt," +
                "used_time,reconciliation_application_time,reconciliation_confirmation_time," +
                "application_for_invoicing_time,invoicing_time,paid_time,po_order_number," +
                "department,contract_id,payment_manager,acceptance_method,order_total_amount," +
                "purch_req_doc_code,pr_project_id,order_state,commodity_background_category");

        Page<NcfFormPurchOrder> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), NcfFormPurchOrder::new));

        PageResult<NcfFormPurchOrder> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<NcfFormPurchOrderVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<NcfFormPurchOrderVO> vos = BeanCopyUtils.convertListTo(page.getContent(), NcfFormPurchOrderVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public Map<String, Object> getNumMoney(Page<NcfFormPurchOrderDTO> pageRequest) {
        LambdaQueryWrapperX<NcfFormPurchOrder> condition = new LambdaQueryWrapperX<>(NcfFormPurchOrder.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.select("DISTINCT order_number,order_total_amount");
        List<NcfFormPurchOrder> list = this.getBaseMapper().selectList(condition);

        Map<String,Object> map = new HashMap<>();
        map.put("num",list.size());
        map.put("money",list.stream().map(NcfFormPurchOrder::getOrderTotalAmount).reduce(BigDecimal.ZERO,BigDecimal::add));

        return map;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "商城集采订单导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", NcfFormPurchOrderDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        NcfFormPurchOrderExcelListener excelReadListener = new NcfFormPurchOrderExcelListener();
        EasyExcel.read(inputStream, NcfFormPurchOrderDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<NcfFormPurchOrderDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("商城集采订单导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<NcfFormPurchOrder> ncfFormPurchOrderes = BeanCopyUtils.convertListTo(dtoS, NcfFormPurchOrder::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pms::NcfFormPurchOrder-import::id", importId, ncfFormPurchOrderes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<NcfFormPurchOrder> ncfFormPurchOrderes = (List<NcfFormPurchOrder>) orionJ2CacheService.get("pms::NcfFormPurchOrder-import::id", importId);
        log.info("商城集采订单导入的入库数据={}", JSONUtil.toJsonStr(ncfFormPurchOrderes));

        this.saveBatch(ncfFormPurchOrderes);
        orionJ2CacheService.delete("pms::NcfFormPurchOrder-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pms::NcfFormPurchOrder-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<NcfFormPurchOrder> condition = new LambdaQueryWrapperX<>(NcfFormPurchOrder.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(NcfFormPurchOrder::getCreateTime);
        List<NcfFormPurchOrder> ncfFormPurchOrderes = this.list(condition);

        List<NcfFormPurchOrderDTO> dtos = BeanCopyUtils.convertListTo(ncfFormPurchOrderes, NcfFormPurchOrderDTO::new);

        String fileName = "商城集采订单数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", NcfFormPurchOrderDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<NcfFormPurchOrderVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }

    @Override
    public Map<String,Object> getPurchList(LocalDate start, LocalDate end) {
        LocalDateTime localDateTime = end.atTime(23,59,59);
        String sql = "select sum(total_order_amount) as order_amount from pms_ncf_form_purch_order_collect where logic_status = '1' " +
                "and order_approval_time <= '" + LocalDate.now() + "' and order_approval_time between '" + start + "' and '" + localDateTime + "'";
        Map<String,Object> map = this.namedParameterJdbcTemplate.queryForMap(sql, new HashMap<>());
        map.putIfAbsent("order_amount", 0);
        return map;
    }


    public static class NcfFormPurchOrderExcelListener extends AnalysisEventListener<NcfFormPurchOrderDTO> {

        private final List<NcfFormPurchOrderDTO> data = new ArrayList<>();

        @Override
        public void invoke(NcfFormPurchOrderDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<NcfFormPurchOrderDTO> getData() {
            return data;
        }
    }


}
