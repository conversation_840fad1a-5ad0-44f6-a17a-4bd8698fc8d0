package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;

/**
 * ProdActionItem Entity对象
 *
 * <AUTHOR>
 * @since 2024-10-24 10:28:59
 */
@TableName(value = "pmsx_prod_action_item")
@ApiModel(value = "ProdActionItemEntity对象", description = "生产大修行动项")
@Data

public class ProdActionItem extends  ObjectEntity  implements Serializable{

    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    @TableField(value = "repair_round")
    private String repairRound;

    /**
     * 责任人ID拼接
     */
    @ApiModelProperty(value = "责任人ID拼接")
    @TableField(value = "rsp_user_ids")
    private String rspUserIds;

    /**
     * 责任人名称拼接
     */
    @ApiModelProperty(value = "责任人名称拼接")
    @TableField(value = "rsp_user_names")
    private String rspUserNames;

    /**
     * 完成时限
     */
    @ApiModelProperty(value = "完成时限")
    @TableField(value = "finish_deadline")
    private Date finishDeadline;

    /**
     * 维度字典
     */
    @ApiModelProperty(value = "维度字典")
    @TableField(value = "dimension_dict")
    private String dimensionDict;

    /**
     * 验证人ID
     */
    @ApiModelProperty(value = "验证人ID")
    @TableField(value = "verifier_id")
    private String verifierId;

    /**
     * 验证人名称
     */
    @ApiModelProperty(value = "验证人名称")
    @TableField(value = "verifier_name")
    private String verifierName;

    /**
     * 问题描述
     */
    @ApiModelProperty(value = "问题描述")
    @TableField(value = "problem_desc")
    private String problemDesc;

    @ApiModelProperty(value = "父级ID")
    @TableField(value = "parent_id")
    private String parentId;



    /**
     * 责任人ID拼接
     */
    @ApiModelProperty(value = "责任部门ID拼接")
    @TableField(value = "rsp_dept_ids")
    private String rspDeptIds;
//
    /**
     * 责任人名称拼接
     */
    @ApiModelProperty(value = "责任部门名称拼接")
    @TableField(value = "rsp_dept_names")
    private String rspDeptNames;


}
