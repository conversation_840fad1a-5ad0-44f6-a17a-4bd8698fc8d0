package com.chinasie.orion.controller;

import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.bo.StatusBo;
import com.chinasie.orion.constant.DictConstant;
import com.chinasie.orion.domain.dto.ProjectTaskStatusDTO;
import com.chinasie.orion.domain.dto.TakeEffectDTO;
import com.chinasie.orion.domain.vo.ProjectTaskStatusVO;
import com.chinasie.orion.domain.vo.SimpleVo;
import com.chinasie.orion.domain.vo.StatusEntityVo;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.page.PageRequest;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.service.ProjectTaskStatusService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/13/10:21
 * @description:
 */
@RestController
@RequestMapping("/project-task-status")
@Api(tags = "项目状态")
public class ProjectTaskStatusController {
    @Resource
    private ProjectTaskStatusService projectTaskStatusService;

    @Resource
    private DictBo dictBo;

    @Resource
    private StatusBo statusBo;

    @ApiOperation("新增项目任务状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectTaskStatusDTO", dataType = "ProjectTaskStatusDTO")
    })
    @PostMapping(value = "/save")
    @LogRecord(success = "【{USER{#logUserId}}】新增项目任务状态", type = "项目状态", subType = "新增项目任务状态", bizNo = "")
    public ResponseDTO<String> saveProjectTaskStatus(@RequestBody ProjectTaskStatusDTO projectTaskStatusDTO) throws Exception {
        try {
            return new ResponseDTO<>(projectTaskStatusService.saveProjectTaskStatus(projectTaskStatusDTO));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @ApiOperation("项目任务状态启用列表 简单版")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", dataType = "String")
    })
    @GetMapping(value = "/list/{projectId}")
    @LogRecord(success = "【{USER{#logUserId}}】项目任务状态启用列表 简单版", type = "项目状态", subType = "项目任务状态启用列表 简单版", bizNo = "{{#projectId}}")
    public ResponseDTO<List<SimpleVo>> getProjectTaskStatusList(@PathVariable("projectId") String projectId) throws Exception {
        try {
            return new ResponseDTO<>(projectTaskStatusService.getProjectTaskStatusList(projectId));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @ApiOperation("项目任务状态分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping(value = "/getPage")
    @LogRecord(success = "【{USER{#logUserId}}】项目任务状态分页", type = "项目状态", subType = "项目任务状态分页", bizNo = "")
    public ResponseDTO<PageResult<ProjectTaskStatusVO>> getProjectTaskStatusPage(@RequestBody PageRequest<ProjectTaskStatusDTO> pageRequest) throws Exception {
        try {
            return new ResponseDTO<>(projectTaskStatusService.getProjectTaskStatusPage(pageRequest));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @ApiOperation("项目任务状态详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", dataType = "String")
    })
    @GetMapping(value = "/detail/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】项目任务状态详情", type = "项目状态", subType = "项目任务状态详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectTaskStatusVO> getProjectTaskStatusDetail(@PathVariable("id") String id) throws Exception {
        try {
            return new ResponseDTO<>(projectTaskStatusService.getProjectTaskStatusDetail(id));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @ApiOperation("编辑项目任务状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectTaskStatusDTO", dataType = "ProjectTaskStatusDTO")
    })
    @PutMapping("/edit")
    @LogRecord(success = "【{USER{#logUserId}}】编辑项目任务状态", type = "项目状态", subType = "编辑项目任务状态", bizNo = "")
    public ResponseDTO<Boolean> editProjectTaskStatus(@RequestBody ProjectTaskStatusDTO projectTaskStatusDTO) throws Exception {
        try {
            return new ResponseDTO<>(projectTaskStatusService.editProjectTaskStatus(projectTaskStatusDTO));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @ApiOperation("批量删除项目任务状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", dataType = "List")
    })
    @DeleteMapping(value = "/removeBatch")
    @LogRecord(success = "【{USER{#logUserId}}】批量删除项目任务状态", type = "项目状态", subType = "批量删除项目任务状态", bizNo = "")
    public ResponseDTO<Boolean> removeProjectTaskStatus(@RequestBody List<String> ids) throws Exception {
        try {
            return new ResponseDTO<>(projectTaskStatusService.removeProjectTaskStatus(ids));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @ApiOperation("批量启用禁用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "takeEffectDTO", dataType = "TakeEffectDTO")
    })
    @PutMapping(value = "/takeEffectBatch")
    @LogRecord(success = "【{USER{#logUserId}}】批量启用禁用", type = "项目状态", subType = "批量启用禁用", bizNo = "")
    public ResponseDTO<Boolean> takeEffectProjectRole(@RequestBody TakeEffectDTO takeEffectDTO) throws Exception {
        try {
            return new ResponseDTO<>(projectTaskStatusService.takeEffectProjectTaskStatus(takeEffectDTO));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @ApiOperation("获取项目任务状态类型")
    @GetMapping("/statusType")
    @LogRecord(success = "【{USER{#logUserId}}】获取项目任务状态类型", type = "项目状态", subType = "获取项目任务状态类型", bizNo = "")
    public ResponseDTO<List<SimpleVo>> getProjectTaskStatusStatuses() {
        try {
            return new ResponseDTO<>(dictBo.getDictValueAndDesList(DictConstant.TASK_STATUS_TYPE));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }


    @ApiOperation("通过数据ID获取已绑定的状态列表")
    @GetMapping("/policy/status/list/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】通过数据ID获取已绑定的状态列表", type = "项目状态", subType = "通过数据ID获取已绑定的状态列表", bizNo = "{{#id}}")
    public ResponseDTO<List<StatusEntityVo>> policyStatusListById(@PathVariable("id") String id) {
        try {
            return new ResponseDTO(statusBo.getPolicyId(id));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }


    @ApiOperation("项目状态")
    @GetMapping("/policy/status/list/project")
    @LogRecord(success = "【{USER{#logUserId}}】项目状态", type = "项目状态", subType = "项目状态", bizNo = "")
    public ResponseDTO<List<StatusEntityVo>> projectPolicyStatusList() {
        List<StatusEntityVo> rsp = statusBo.projectPolicyStatusList();
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("所有项目任务状态启用列表 简单版")
    @GetMapping(value = "/list")
    @LogRecord(success = "【{USER{#logUserId}}】所有项目任务状态启用列表 简单版", type = "项目状态", subType = "所有项目任务状态启用列表 简单版", bizNo = "")
    public ResponseDTO<List<SimpleVo>> getProjectTaskStatusList() throws Exception {
        try {
            return new ResponseDTO<>(projectTaskStatusService.getProjectTaskStatusList(null));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }
}
