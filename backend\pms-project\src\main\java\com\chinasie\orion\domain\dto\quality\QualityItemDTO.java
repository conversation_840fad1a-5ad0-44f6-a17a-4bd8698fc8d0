package com.chinasie.orion.domain.dto.quality;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import java.io.Serializable;

import java.lang.String;
import java.lang.Integer;
import java.util.List;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * QualityItem DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-08 14:13:46
 */
@ApiModel(value = "QualityItemDTO对象", description = "质量管控项")
@Data
@ExcelIgnoreUnannotated
public class QualityItemDTO extends ObjectDTO implements Serializable {

    /**
     * 质控点
     */
    @ApiModelProperty(value = "质控点")
    @ExcelProperty(value = "质控点 ", index = 0)
    @NotBlank(message = "质控点不可为空")
    @Length(max = 100, message = "质控点长度不能超过100")
    private String point;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 1)
    private String number;

    /**
     * 方案
     */
    @ApiModelProperty(value = "控制方案")
    @ExcelProperty(value = "控制方案 ", index = 2)
    @NotBlank(message = "控制方案不可为空")
    @Length(max = 1000, message = "方案长度不能超过1000")
    private String scheme;

    /**
     * 类型
     */
    @ApiModelProperty(value = "质控措施类型")
    @ExcelProperty(value = "质控措施类型 ", index = 3)
    @NotBlank(message = "质控措施类型不可为空")
    private String type;

    /**
     * 阶段
     */
    @ApiModelProperty(value = "质控阶段")
    @ExcelProperty(value = "质控阶段 ", index = 4)
    @Length(max = 50, message = "阶段长度不能超过50")
    private String stage;

    /**
     * 过程
     */
    @ApiModelProperty(value = "过程")
    @ExcelProperty(value = "过程 ", index = 5)
    @NotBlank(message = "过程不可为空")
    private String process;

    /**
     * 活动
     */
    @ApiModelProperty(value = "质控活动")
    @ExcelProperty(value = "质控活动 ", index = 6)
    @NotBlank(message = "质控活动不可为空")
    private String activity;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @ExcelProperty(value = "项目id ", index = 7)
    private String projectId;

    /**
     * 确认人
     */
    @ApiModelProperty(value = "完成确认人")
    @ExcelProperty(value = "完成确认人 ", index = 8)
    private String affirm;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    @ExcelProperty(value = "责任人 ", index = 9)
    private String resPerson;

    /**
     * 是否关联计划
     */
    @ApiModelProperty(value = "是否关联计划")
    @ExcelProperty(value = "是否关联计划 ", index = 10)
    private Integer relevanceScheme;

    /**
     * 执行情况
     */
    @ApiModelProperty(value = "执行情况")
    @ExcelProperty(value = "执行情况 ", index = 11)
    private String execute;

    /**
     * 交付文件名称
     */
    @ApiModelProperty(value = "交付文件名称")
    @ExcelProperty(value = "交付文件名称 ", index = 6)
    @Length(max = 50, message = "交付文件名称长度不能超过50")
    private String deliveryFileName;

    /**
     * 消息id
     */
    @ApiModelProperty(value = "消息id")
    private String messageId;

    @ApiModelProperty(value = "完成情况说明")
    private String completionStatement;
}
