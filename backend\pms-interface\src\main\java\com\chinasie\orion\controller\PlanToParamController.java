package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.CopyEntityDTO;
import com.chinasie.orion.domain.dto.pdm.ParameterPoolInsDTO;
import com.chinasie.orion.domain.vo.RelationParamInsVO;
import com.chinasie.orion.domain.vo.RelationParamVO;
import com.mzt.logapi.starter.annotation.LogRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;


import com.chinasie.orion.service.PlanToParamService;

import com.chinasie.orion.dto.ResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
/**
 * <p>
 * PlanToParam 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-22 21:17:17
 */
@RestController
@RequestMapping("/plan-to-param")
@Api(tags = "计划和参数的关联关系")
public class PlanToParamController {

    @Autowired
    private PlanToParamService planToParamService;


    /**
     * 新增关联接口
     * @param
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增关联参数")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】新增关联参数，计划编号：{#planId}，参数编号列表：{ID_LIST{#imIdList}}",
            type = "PlanToParam",
            subType = "新增关联",
            bizNo = "{#planId}"
    )
    public ResponseDTO<Boolean> relationToParam(@RequestParam("planId") String planId, @RequestBody List<String> imIdList) throws Exception {
        return new ResponseDTO<>(planToParamService.relationToParam(planId,imIdList));
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）关联参数")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】删除关联参数，计划编号：{#planId}，参数编号列表：{ID_LIST{#paramIdList}}",
            type = "PlanToParam",
            subType = "删除关联",
            bizNo = "{#planId}"
    )
    public ResponseDTO<Boolean> removeRelation(@RequestParam("planId") String planId, @RequestBody List<String> paramIdList) throws Exception {
        Boolean rsp = planToParamService.removeRelation(planId,paramIdList);
        return new ResponseDTO(rsp);
    }


    @ApiOperation(value = "关联的参数列表")
    @RequestMapping(value = "/relation/list/{planId}", method = RequestMethod.POST)
    @LogRecord(
            success = "【{USER{#logUserId}}】查看计划【{#planId}】的关联参数列表",
            type = "PlanToParam",
            subType = "关联参数列表",
            bizNo = "{#planId}"
    )
    public ResponseDTO<List<RelationParamVO>> relationList(@PathVariable("planId") String planId) throws Exception {
        List<RelationParamVO> rsp =  planToParamService.relationList(planId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 选择模板
     * @param planId
     * @param paramId
     * @param modelId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "选择模板")
    @RequestMapping(value = "/setting/model", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】选择模板",
            type = "PlanToParam",
            subType = "选择模板",
            bizNo = "{#planId}"
    )
    public ResponseDTO<Boolean> settingModel(@RequestParam("planId") String planId,@RequestParam("paramId") String paramId,@RequestParam("modelId") String modelId) throws Exception {
        Boolean rsp = planToParamService.settingModel(planId,paramId,modelId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 编辑
     *
     * @param parameterPoolInsDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "填值")
    @RequestMapping(value = "/add/value", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】填值，业务编号：{#parameterPoolInsDTO.id}，参数记录编号：{#parameterPoolInsDTO.paramInsId}",
            type = "ParameterPoolIns",
            subType = "填值",
            bizNo = "{#parameterPoolInsDTO.id}"
    )
    public ResponseDTO<Boolean> addValue(@Validated @RequestBody ParameterPoolInsDTO parameterPoolInsDTO) throws Exception {
        Boolean rsp = planToParamService.addValue(parameterPoolInsDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 复制
     * @param sourceId
     * @param targetId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "复制")
    @RequestMapping(value = "/copy/{sourceId}/to/{targetId}", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】复制计划计划和参数的关联关系",
            type = "PlanToParam",
            subType = "复制",
            bizNo = "{#sourceId}"
    )
    public ResponseDTO<Boolean> copySourceIdToTargetId(@PathVariable("sourceId") String sourceId,@PathVariable("targetId") String targetId,@RequestBody CopyEntityDTO copyEntityDTO) throws Exception {
        Boolean rsp = planToParamService.copySourceIdToTargetId(sourceId,targetId,copyEntityDTO);
        return new ResponseDTO<>(rsp);
    }



    @ApiOperation(value = "关联的参数列表-通过fromIdList")
    @RequestMapping(value = "/param/relation/list", method = RequestMethod.POST)
    @LogRecord(
            success = "【{USER{#logUserId}}】通过fromIdList查询关联参数列表",
            type = "PlanToParam",
            subType = "关联参数列表查询",
            bizNo = ""  // 无具体业务编号
    )
    public ResponseDTO<List<RelationParamInsVO>> relationListByToIdList(@RequestBody List<String> fromIdList) throws Exception {
        List<RelationParamInsVO> rsp =  planToParamService.relationListByToIdList(fromIdList);
        return new ResponseDTO<>(rsp);
    }
}
