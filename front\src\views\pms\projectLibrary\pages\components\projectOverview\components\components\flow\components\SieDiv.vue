<script lang="ts">
import {
  defineComponent, inject, onMounted, ref,
} from 'vue';
import { CheckCircleFilled } from '@ant-design/icons-vue';
export default defineComponent({
  components: {
    CheckCircleFilled,
  },
  props: {
    actions: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  setup() {
    const data:any = ref({});
    const getNode:any = inject('getNode');
    onMounted(() => {
      data.value = getNode();
    });
    return {
      data,
    };
  },
});

</script>

<template>
  <div
    v-if="data?.data"
    class="cell-service"
  >
    <!--进行中-->
    <div
      v-if="data?.data.isOver"
      class="circle3"
    />
    <!--已完成-->
    <CheckCircleFilled
      v-if="data?.data.isFinish"
      class="circle2"
    />
    <!--未开始-->
    <div
      v-if="data?.data.isNot"
      class="circle1"
    />
    <div>售后服务</div>
  </div>
</template>

<style scoped lang="less">
.cell-service{
  display: flex;
  align-items: center;
    .circle1{
      width: 15px;
      height: 15px;
      background: #fff;
      border:1px solid #999;
      border-radius: 50%;
      margin-right: 10px;
    }

    .circle2{
      width: 15px;
      height: 15px;
      color:#1890ff;
      border-radius: 50%;
      margin-right: 10px;
    }

    .circle3{
      width: 15px;
      height: 15px;
      background: #fff;
      border:1px solid #F09509;
      margin-right: 10px;
      border-radius: 50%;
    }
}
</style>