<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <artifactId>netty-parent</artifactId>
    <groupId>io.netty</groupId>
    <version>4.1.101.Final</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>netty-common</artifactId>
  <name>Netty/Common</name>
  <build>
    <plugins>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <artifactSet>
                <includes>
                  <include>org.jctools</include>
                </includes>
              </artifactSet>
              <relocations>
                <relocation>
                  <pattern>org.jctools.</pattern>
                  <shadedPattern>io.netty.util.internal.shaded.org.jctools.</shadedPattern>
                </relocation>
              </relocations>
              <minimizeJar>true</minimizeJar>
              <createSourcesJar>true</createSourcesJar>
              <shadeSourcesContent>true</shadeSourcesContent>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>build-helper-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>add-source</id>
            <phase>generate-sources</phase>
            <goals>
              <goal>add-source</goal>
            </goals>
            <configuration>
              <sources>
                <source>${collection.src.dir}</source>
              </sources>
            </configuration>
          </execution>
          <execution>
            <id>add-test-source</id>
            <phase>generate-test-sources</phase>
            <goals>
              <goal>add-test-source</goal>
            </goals>
            <configuration>
              <sources>
                <source>${collection.testsrc.dir}</source>
              </sources>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.gmaven</groupId>
        <artifactId>groovy-maven-plugin</artifactId>
        <version>2.1.1</version>
        <executions>
          <execution>
            <id>generate-collections</id>
            <phase>generate-sources</phase>
            <goals>
              <goal>execute</goal>
            </goals>
            <configuration>
              <source>${project.basedir}/src/main/script/codegen.groovy</source>
            </configuration>
          </execution>
        </executions>
        <dependencies>
          <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy</artifactId>
            <version>3.0.9</version>
          </dependency>
          <dependency>
            <groupId>ant</groupId>
            <artifactId>ant-optional</artifactId>
            <version>1.5.3-1</version>
          </dependency>
        </dependencies>
      </plugin>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <executions>
          <execution>
            <id>generate-manifest</id>
            <phase>process-classes</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
            <configuration>
              <instructions>
                <DynamicImport-Package>*</DynamicImport-Package>
                <Import-Package>!org.jctools.*;sun.misc;resolution:=optional;*</Import-Package>
              </instructions>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <dependencies>
    <dependency>
      <groupId>org.graalvm.nativeimage</groupId>
      <artifactId>svm</artifactId>
      <version>19.3.6</version>
      <scope>provided</scope>
      <exclusions>
        <exclusion>
          <artifactId>svm-hosted-native-linux-amd64</artifactId>
          <groupId>org.graalvm.nativeimage</groupId>
        </exclusion>
        <exclusion>
          <artifactId>svm-hosted-native-darwin-amd64</artifactId>
          <groupId>org.graalvm.nativeimage</groupId>
        </exclusion>
        <exclusion>
          <artifactId>svm-hosted-native-windows-amd64</artifactId>
          <groupId>org.graalvm.nativeimage</groupId>
        </exclusion>
        <exclusion>
          <artifactId>graal-sdk</artifactId>
          <groupId>org.graalvm.sdk</groupId>
        </exclusion>
        <exclusion>
          <artifactId>objectfile</artifactId>
          <groupId>org.graalvm.nativeimage</groupId>
        </exclusion>
        <exclusion>
          <artifactId>pointsto</artifactId>
          <groupId>org.graalvm.nativeimage</groupId>
        </exclusion>
        <exclusion>
          <artifactId>truffle-nfi</artifactId>
          <groupId>org.graalvm.truffle</groupId>
        </exclusion>
        <exclusion>
          <artifactId>compiler</artifactId>
          <groupId>org.graalvm.compiler</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.jetbrains</groupId>
      <artifactId>annotations-java5</artifactId>
      <version>23.0.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.7.30</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>commons-logging</groupId>
      <artifactId>commons-logging</artifactId>
      <version>1.2</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-1.2-api</artifactId>
      <version>2.17.2</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>mail</artifactId>
          <groupId>javax.mail</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jms</artifactId>
          <groupId>javax.jms</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jmxtools</artifactId>
          <groupId>com.sun.jdmk</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jmxri</artifactId>
          <groupId>com.sun.jmx</groupId>
        </exclusion>
      </exclusions>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-api</artifactId>
      <version>2.17.2</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-core</artifactId>
      <version>2.17.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>io.projectreactor.tools</groupId>
      <artifactId>blockhound</artifactId>
      <version>1.0.6.RELEASE</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>2.18.3</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>byte-buddy</artifactId>
          <groupId>net.bytebuddy</groupId>
        </exclusion>
        <exclusion>
          <artifactId>byte-buddy-agent</artifactId>
          <groupId>net.bytebuddy</groupId>
        </exclusion>
        <exclusion>
          <artifactId>objenesis</artifactId>
          <groupId>org.objenesis</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-dev-tools</artifactId>
      <version>4.1.101.Final</version>
      <scope>test</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <version>5.9.0</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>opentest4j</artifactId>
          <groupId>org.opentest4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>junit-platform-commons</artifactId>
          <groupId>org.junit.platform</groupId>
        </exclusion>
        <exclusion>
          <artifactId>apiguardian-api</artifactId>
          <groupId>org.apiguardian</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <version>5.9.0</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>junit-platform-engine</artifactId>
          <groupId>org.junit.platform</groupId>
        </exclusion>
        <exclusion>
          <artifactId>apiguardian-api</artifactId>
          <groupId>org.apiguardian</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-params</artifactId>
      <version>5.9.0</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>apiguardian-api</artifactId>
          <groupId>org.apiguardian</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-build-common</artifactId>
      <version>31</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>checkstyle</artifactId>
          <groupId>com.puppycrawl.tools</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.hamcrest</groupId>
      <artifactId>hamcrest-library</artifactId>
      <version>1.3</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>hamcrest-core</artifactId>
          <groupId>org.hamcrest</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <version>3.18.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
      <version>1.2.3</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>logback-core</artifactId>
          <groupId>ch.qos.logback</groupId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>
  <properties>
    <collection.template.test.dir>${project.basedir}/src/test/templates</collection.template.test.dir>
    <collection.testsrc.dir>${project.build.directory}/generated-test-sources/collections/java</collection.testsrc.dir>
    <javaModuleName>io.netty.common</javaModuleName>
    <collection.template.dir>${project.basedir}/src/main/templates</collection.template.dir>
    <collection.src.dir>${project.build.directory}/generated-sources/collections/java</collection.src.dir>
  </properties>
</project>
