package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ContractMilestoneAcceptanceDTO DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-29 15:01:52
 */
@ApiModel(value = "ContractMilestoneAcceptanceDTO对象", description = "合同里程碑")
@Data
@ExcelIgnoreUnannotated
public class ContractMilestoneAcceptanceDTO extends  ObjectDTO   implements Serializable{





}
