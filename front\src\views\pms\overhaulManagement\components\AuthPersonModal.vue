<script setup lang="ts">
import { OrionTable, randomString } from 'lyra-component-vue3';
import { Alert } from 'ant-design-vue';
import { computed, ref, Ref } from 'vue';

const props = defineProps<{
  list: any[],
  message: string
}>();

const data = computed(() => props.list?.map((item) => {
  item.id = randomString();
  return item;
}));

const tableRef: Ref = ref();
const tableOptions = {
  showToolButton: false,
  showTableSetting: false,
  showSmallSearch: false,
  pagination: false,
  isSpacing: false,
  maxHeight: 300,
  api: () => data.value,
  columns: [
    {
      title: '员工号',
      dataIndex: 'userCode',
    },
    {
      title: '姓名',
      dataIndex: 'userName',
    },
    {
      title: '未验证通过原因',
      dataIndex: 'reason',
    },
  ],
};
</script>

<template>
  <div
    class="p20"
  >
    <Alert
      :message="message"
      type="info"
    />
    <div>
      <OrionTable
        ref="tableRef"
        :options="tableOptions"
      />
    </div>
  </div>
</template>

<style scoped lang="less">

</style>
