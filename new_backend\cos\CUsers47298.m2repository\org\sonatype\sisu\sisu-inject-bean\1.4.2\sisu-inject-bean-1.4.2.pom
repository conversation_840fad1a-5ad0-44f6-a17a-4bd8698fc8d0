<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.sonatype.sisu.inject</groupId>
    <artifactId>guice-bean</artifactId>
    <version>1.4.2</version>
  </parent>

  <packaging>bundle</packaging>

  <groupId>org.sonatype.sisu</groupId>
  <artifactId>sisu-inject-bean</artifactId>

  <name>Sisu - Inject (JSR330 bean support)</name>

  <dependencies>
    <dependency>
      <groupId>org.sonatype.sisu</groupId>
      <artifactId>sisu-guice</artifactId>
      <classifier>noaop</classifier>
      <exclusions>
        <exclusion>
          <groupId>javax.inject</groupId>
          <artifactId>javax.inject</artifactId>
        </exclusion>
        <exclusion>
          <groupId>aopalliance</groupId>
          <artifactId>aopalliance</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.sonatype.sisu.inject</groupId>
      <artifactId>guice-bean-containers</artifactId>
      <optional>true</optional>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <extensions>true</extensions>
        <configuration>
          <instructions>
            <Bundle-SymbolicName>
              org.sonatype.inject
            </Bundle-SymbolicName>
            <Bundle-Activator>
              org.sonatype.guice.bean.containers.Activator
            </Bundle-Activator>
            <DynamicImport-Package>
              org.slf4j,junit.framework
            </DynamicImport-Package>
            <Export-Package>
              org.sonatype.inject;-noimport:=true;-split-package:=merge-first;version=${project.version},
              javax.*|org.aopalliance.*;version=1
            </Export-Package>
            <Private-Package>
              org.sonatype.guice.*,org.objectweb.asm
            </Private-Package>
          </instructions>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <artifactSet>
                <includes>
                  <include>${project.groupId}:${project.artifactId}</include>
                </includes>
              </artifactSet>
              <relocations>
                <relocation>
                  <pattern>org.objectweb</pattern>
                  <shadedPattern>org.sonatype.guice</shadedPattern>
                </relocation>
              </relocations>
              <filters>
                <filter>
                  <artifact>*:*</artifact>
                  <excludes>
                    <exclude>org/objectweb/asm/*Adapter*</exclude>
                    <exclude>org/objectweb/asm/*Writer*</exclude>
                  </excludes>
                </filter>
              </filters>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive>
            <manifest>
              <mainClass>org.sonatype.guice.bean.containers.Main</mainClass>
            </manifest>
          </archive>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>release</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-dependency-plugin</artifactId>
            <executions>
              <execution>
                <id>unpack-source</id>
                <phase>prepare-package</phase>
                <goals>
                  <goal>unpack-dependencies</goal>
                </goals>
                <configuration>
                  <classifier>sources</classifier>
                  <failOnMissingClassifierArtifact>false</failOnMissingClassifierArtifact>
                  <outputDirectory>${project.build.directory}/sources</outputDirectory>
                  <includes>
                    javax/**,org/aopalliance/**,org/sonatype/inject/**,org/sonatype/guice/bean/**
                  </includes>
                  <silent>true</silent>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>build-helper-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>add-source</id>
                <phase>prepare-package</phase>
                <goals>
                  <goal>add-source</goal>
                </goals>
                <configuration>
                  <sources>
                    <source>${project.build.directory}/sources</source>
                  </sources>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

</project>
