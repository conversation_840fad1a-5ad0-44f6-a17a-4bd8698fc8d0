package com.chinasie.orion.domain.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * PersonRoleMaintenance Entity对象
 *
 * <AUTHOR>
 * @since 2024-10-08 17:28:59
 */
@TableName(value = "pmsx_person_role_maintenance")
@ApiModel(value = "PersonRoleMaintenanceEntity对象", description = "人员角色维护表")
@Data

public class PersonRoleMaintenance extends  ObjectEntity  implements Serializable{



    /**
     * 专业中心
     */
    @ApiModelProperty(value = "专业中心")
    @TableField(value = "expertise_center")
    private String expertiseCenter;

    @ApiModelProperty(value = "专业中心名称")
    @TableField(exist = false)
    private String expertiseCenterTitle;

    /**
     * 专业所
     */
    @ApiModelProperty(value = "专业所")
    @TableField(value = "expertise_station")
    private String expertiseStation;


    @ApiModelProperty(value = "专业所")
    @TableField(exist = false)
    private String expertiseStationTitle;

    /**
     * 专业所
     */
    @ApiModelProperty(value = "财务人员")
    @TableField(value = "financial_staff")
    private String financialStaff;

    /**
     * 变更原因
     */
    @ApiModelProperty(value = "变更原因")
    @TableField(value = "change_reason")
    private String changeReason;
}
