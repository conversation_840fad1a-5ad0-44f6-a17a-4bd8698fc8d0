package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;
import java.util.List;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProjectApprovalProduct DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-23 15:11:39
 */
@ApiModel(value = "ProjectApprovalProductDTO对象", description = "项目立项产品")
@Data
@ExcelIgnoreUnannotated
public class ProjectApprovalProductDTO extends ObjectDTO implements Serializable {

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    @ExcelProperty(value = "产品名称 ", index = 0)
    private String name;

    /**
     * 产品编码
     */
    @ApiModelProperty(value = "产品编码")
    @ExcelProperty(value = "产品编码 ", index = 1)
    private String number;

    /**
     * 产品分类
     */
    @ApiModelProperty(value = "产品分类")
    @ExcelProperty(value = "产品分类 ", index = 2)
    private String productClassify;

    /**
     * 产品/物料类型
     */
    @ApiModelProperty(value = "产品/物料类型")
    @ExcelProperty(value = "产品/物料类型 ", index = 3)
    private String productMaterialType;

    /**
     * 物料类别
     */
    @ApiModelProperty(value = "物料类别")
    @ExcelProperty(value = "物料类别 ", index = 4)
    private String materialType;

    /**
     * 军/民品分类
     */
    @ApiModelProperty(value = "军/民品分类")
    @ExcelProperty(value = "军/民品分类 ", index = 5)
    private String militaryCivilian;

    /**
     * 物料数量
     */
    @ApiModelProperty(value = "物料数量")
    @ExcelProperty(value = "物料数量 ", index = 6)
    private Integer materialAmount;

    /**
     * 产品组
     */
    @ApiModelProperty(value = "产品组")
    @ExcelProperty(value = "产品组 ", index = 7)
    private String productGroup;

    /**
     * 产品型号
     */
    @ApiModelProperty(value = "产品型号")
    @ExcelProperty(value = "产品型号 ", index = 8)
    private String productModelNumber;

    /**
     * 父级id
     */
    @ApiModelProperty(value = "父级id")
    @ExcelProperty(value = "父级id ", index = 9)
    private String parentId;

    /**
     * 物料价格
     */
    @ApiModelProperty(value = "物料价格")
    @ExcelProperty(value = "物料价格 ", index = 10)
    private BigDecimal materialPrice;

    /**
     * oa编码
     */
    @ApiModelProperty(value = "oa编码")
    @ExcelProperty(value = "oa编码 ", index = 11)
    private String oaNumber;

    /**
     * 物料等级
     */
    @ApiModelProperty(value = "物料等级")
    @ExcelProperty(value = "物料等级 ", index = 12)
    private String materialLevel;

    /**
     * CS状态批准时间
     */
    @ApiModelProperty(value = "CS状态批准时间")
    @ExcelProperty(value = "CS状态批准时间 ", index = 13)
    private Date csDate;

    /**
     * WS状态批准时间
     */
    @ApiModelProperty(value = "WS状态批准时间")
    @ExcelProperty(value = "WS状态批准时间 ", index = 14)
    private Date wsDate;

    /**
     * 高质量等级
     */
    @ApiModelProperty(value = "高质量等级")
    @ExcelProperty(value = "高质量等级 ", index = 15)
    private String highQualityLevel;


    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "台套数")
    private String requiredUnitNum;

    /**
     * 国产化管控
     */
    @ApiModelProperty(value = "国产化管控")
    @ExcelProperty(value = "国产化管控 ", index = 16)
    private String localizedControl;


    @ApiModelProperty(value = "立项id ")
    private String approvalId;
    @ApiModelProperty(value = "产品二级分类 ")
    private String productSecondClassify;

}
