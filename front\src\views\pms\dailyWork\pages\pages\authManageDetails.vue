<script setup lang="ts">
import {
  BasicButton, BasicTableAction, IDataStatus, isPower, Layout3,
} from 'lyra-component-vue3';
import {
  computed, onMounted, provide, reactive, ref, Ref, watchEffect,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import Api from '/@/api';
import {
  UseBusinessWorkflowReturn, WorkflowAction, WorkflowProps, WorkflowView,
} from 'lyra-workflow-component-vue3';
import BasicInfo from './components/BasicInfo.vue';
import JobAuthorization from './components/JobAuthorization.vue';
import CertificateList from './components/CertificateList.vue';
import { Modal } from 'ant-design-vue';
import { authApi } from '/@/views/pms/dailyWork/pages/utils';
import { useLayoutKey } from '/@/views/pms/hooks';
import { useAuthDetailsCode } from '/@/views/pms/dailyWork/pages/hooks';

interface DetailsDataType {
  id: string,
  name: string,
  className: string,
  projectCode: string,
  ownerName?: string | undefined,
  status?: string | undefined | number,
  dataStatus?: IDataStatus | undefined,

  [propName: string]: any
}

const route = useRoute();
const router = useRouter();
const actionId: Ref<string | null> = ref('');
const { powerCodePrefix } = useAuthDetailsCode(route.name as string);
provide('powerCodePrefix', powerCodePrefix);
const dataId = computed(() => route.params?.id);
const detailsData: DetailsDataType = reactive({
  id: '',
  name: '',
  className: '',
  projectCode: '',
});
provide('detailsData', detailsData);
const projectData = computed(() => ({
  name: detailsData.fullName,
  projectCode: detailsData.userCode,
  dataStatus: detailsData?.dataStatus,
}));

const menuData = computed(() => [
  {
    id: 'basicInfo',
    name: '基本信息',
    powerCode: `${powerCodePrefix.value}_container_02`,
  },
  {
    id: 'certificate',
    name: '证书管理',
    powerCode: `${powerCodePrefix.value}_container_03`,
  },
  {
    id: 'jobAuthorization',
    name: '岗位授权管理',
    powerCode: `${powerCodePrefix.value}_container_04`,
  },
  {
    id: 'workflow',
    name: '审批流程',
    powerCode: `${powerCodePrefix.value}_container_05`,
  },
]);

function menuChange({ id }) {
  actionId.value = id;
}

watchEffect(() => {
  if (!actionId.value || (actionId.value && menuData.value.findIndex((item) => item.id === actionId.value) === -1)) {
    actionId.value = menuData.value?.[0]?.id;
  }
});

onMounted(() => {
  getDetails();
});

const powerData: Ref<any[]> = ref(undefined);
provide('powerData', powerData);
const loading: Ref<boolean> = ref(false);

async function getDetails() {
  loading.value = true;
  try {
    const result: Record<string, any> = await new Api('/pms/job-post-authorize').fetch({
      pageCode: route.name,
    }, dataId.value, 'GET');
    Object.keys(result).forEach((key) => {
      detailsData[key] = result[key];
    });
    powerData.value = result?.detailAuthList;
    updateKey();
  } finally {
    loading.value = false;
  }
}

const workflowActionRef: Ref<UseBusinessWorkflowReturn> = ref();
const workflowViewRef: Ref<UseBusinessWorkflowReturn> = ref();

const workflowProps = computed(() => ({
  Api,
  businessData: {
    ...detailsData,
    name: detailsData.fullName,
  },
  async afterEvent() {
    await getDetails();
    await workflowViewRef.value?.init();
  },
} as WorkflowProps));

const actions = computed(() => [
  {
    text: '发起流程',
    icon: 'sie-icon-qidongliucheng',
    isShow: () => !!workflowActionRef.value?.isAdd && isPower(`${powerCodePrefix.value}_container_01_button_01`, powerData.value) && detailsData.status !== 130,
    onClick() {
      workflowActionRef.value?.onAddTemplate({
        messageUrl: route.fullPath,
      });
    },
  },
]);

const authRef: Ref = ref();

const loadingBtn: Ref<boolean> = ref(false);

function handleSave() {
  loadingBtn.value = true;
  authRef.value?.submit().then(() => {
    getDetails();
  }).finally(() => {
    loadingBtn.value = false;
  });
}

function handleCancel() {
  switch (route.name) {
    case 'PMSDailyWorkAuthDetails':
      router.push({
        name: 'PMSDailyWorkDetails',
        params: {
          id: detailsData?.jobId,
        },
      });
      break;
    case 'PMSMajorRepairsAuthDetails':
      router.push({
        name: 'PMSMajorRepairsWorkDetails',
        params: {
          id: detailsData?.jobId,
        },
      });
      break;
  }
}

function handleValidate() {
  Modal.confirm({
    title: '核验提示！',
    content: '确认核验已选数据?',
    onOk: () => authApi(detailsData?.jobId, [detailsData?.id], getDetails),
  });
}

const { layoutKey, updateKey } = useLayoutKey();

</script>

<template>
  <Layout3
    :key="layoutKey"
    v-loading="loading"
    v-get-power="{powerData}"
    :defaultActionId="actionId"
    :menuData="menuData"
    :projectData="projectData"
    :type="2"
    @menuChange="menuChange"
  >
    <template #header-right>
      <BasicTableAction
        :actions="actions"
        type="button"
      />
    </template>
    <template
      v-if="detailsData?.id"
    >
      <BasicInfo v-if="actionId==='basicInfo'" />
      <CertificateList v-if="actionId==='certificate'" />
      <JobAuthorization
        v-if="actionId==='jobAuthorization'"
        ref="authRef"
      />
      <WorkflowView
        v-if="actionId==='workflow'"
        ref="workflowViewRef"
        :workflow-props="workflowProps"
      />
    </template>
    <template
      v-if="detailsData?.id"
      #footer
    >
      <WorkflowAction
        v-if="actionId==='workflow'"
        ref="workflowActionRef"
        :workflow-props="workflowProps"
      />
      <div
        v-else
        class="p20 flex flex-pe"
      >
        <BasicButton
          @click="handleCancel"
        >
          取消
        </BasicButton>
        <BasicButton
          v-if="actionId==='basicInfo'&&detailsData.status!==130"
          v-is-power="[`${powerCodePrefix}_container_02_button_02`]"
          class="button-margin-left"
          type="primary"
          @click="handleValidate"
        >
          核验
        </BasicButton>
        <BasicButton
          v-if="actionId==='jobAuthorization'&&detailsData.status!==130"
          v-is-power="[`${powerCodePrefix}_container_04_button_02`]"
          class="button-margin-left"
          type="primary"
          :loading="loadingBtn"
          @click="handleSave"
        >
          保存
        </BasicButton>
      </div>
    </template>
  </Layout3>
</template>

<style scoped lang="less">

</style>
