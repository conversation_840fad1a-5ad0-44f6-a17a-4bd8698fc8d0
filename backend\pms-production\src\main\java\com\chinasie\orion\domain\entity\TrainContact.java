package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/07/29/11:05
 * @description:
 */

@TableName(value = "pmsx_train_contact")
@ApiModel(value = "TrainContractEntity对象", description = "培训联络人信息表")
@Data

public class TrainContact extends ObjectEntity implements Serializable {
    @ApiModelProperty(value = "联络人类型")
    @TableField(value = "contact_type")
    private String contactType;

    /**
     * 中心ID或部门ID
     */
    @ApiModelProperty(value = "中心ID或部门ID")
    @TableField(value = "dept_id")
    private String deptId;
    /**
     * 中心ID或部门ID
     */
    @ApiModelProperty(value = "中心code或部门code")
    @TableField(value = "dept_code")
    private String deptCode;
    /**
     * 联系人id拼接
     */
    @ApiModelProperty(value = "联系人id拼接")
    @TableField(value = "contact_person_ids")
    private String contactPersonIds;

    /**
     * 中心名称或者部门名称
     */
    @ApiModelProperty(value = "中心名称或者部门名称")
    @TableField(value = "dept_name")
    private String deptName;

    /**
     * 联系人名称拼接
     */
    @ApiModelProperty(value = "联系人名称拼接")
    @TableField(value = "contact_person_names")
    private String contactPersonNames;

    /**
     * 基地编码
     */
    @ApiModelProperty(value = "基地编码")
    @TableField(value = "base_code")
    private String baseCode;

    /**
     * 基地名称
     */
    @ApiModelProperty(value = "基地名称")
    @TableField(value = "base_name")
    private String baseName;

    /**
     * 管理属性
     */
    @ApiModelProperty(value = "管理属性")
    @TableField(value = "manage_type")
    private String manageType;

}

