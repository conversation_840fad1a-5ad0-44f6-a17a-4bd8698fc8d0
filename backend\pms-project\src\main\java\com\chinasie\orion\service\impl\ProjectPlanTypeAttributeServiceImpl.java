package com.chinasie.orion.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinasie.orion.api.code.domain.vo.CodeSegmentVO;
import com.chinasie.orion.bo.CodeBo;
import com.chinasie.orion.constant.ClassNameConstant;
import com.chinasie.orion.domain.dto.ProjectPlanTypeAttributeDTO;
import com.chinasie.orion.domain.entity.ProjectPlanTypeAttribute;
import com.chinasie.orion.domain.vo.ProjectPlanTypeAttributeVO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.ProjectPlanTypeAttributeRepository;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectPlanTypeAttributeService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/10/9 17:37
 */
@Service
public class ProjectPlanTypeAttributeServiceImpl extends OrionBaseServiceImpl<ProjectPlanTypeAttributeRepository, ProjectPlanTypeAttribute> implements ProjectPlanTypeAttributeService {

    @Resource
    private CodeBo codeBo;

    @Override
    public String add(ProjectPlanTypeAttributeDTO typeAttributeDTO) throws Exception {
        List<ProjectPlanTypeAttribute> planTypeAttributes = this.list(new LambdaQueryWrapper<>(ProjectPlanTypeAttribute.class)
                .eq(ProjectPlanTypeAttribute::getName, typeAttributeDTO.getName()));
        if (!CollectionUtils.isEmpty(planTypeAttributes)) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_CODE_REPEAT.getErrorCode(), "该类型属性已存在，请引用该属性");
        }
        List<CodeSegmentVO> codeRuleList = codeBo.getCodeRuleList(ProjectPlanTypeAttribute.class.getSimpleName(), ClassNameConstant.NUMBER);
        if (!CollectionUtils.isEmpty(codeRuleList)) {
            String code = codeBo.getCode(codeRuleList);
            typeAttributeDTO.setNumber(code);
        }
        ProjectPlanTypeAttribute typeAttribute = BeanCopyUtils.convertTo(typeAttributeDTO, ProjectPlanTypeAttribute::new);
        this.save(typeAttribute);
        return typeAttribute.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean edit(ProjectPlanTypeAttributeDTO typeAttributeDTO) throws Exception {
        List<ProjectPlanTypeAttribute> riskTypeAttributeList = this.list(new LambdaQueryWrapper<>(ProjectPlanTypeAttribute.class)
                .eq(ProjectPlanTypeAttribute::getName, typeAttributeDTO.getName())
                .ne(ProjectPlanTypeAttribute::getId, typeAttributeDTO.getId()));
        if (!CollectionUtils.isEmpty(riskTypeAttributeList)) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_CODE_REPEAT.getErrorCode(), "该类型属性已存在，请引用该属性");
        }
        ProjectPlanTypeAttribute projectPlanTypeAttribute = new ProjectPlanTypeAttribute();
        projectPlanTypeAttribute.setId(typeAttributeDTO.getId());
        projectPlanTypeAttribute.setName(typeAttributeDTO.getName());
        projectPlanTypeAttribute.setOptions(typeAttributeDTO.getOptions());
        projectPlanTypeAttribute.setRequire(typeAttributeDTO.getRequire());
        projectPlanTypeAttribute.setRemark(typeAttributeDTO.getRemark());
        return this.updateById(projectPlanTypeAttribute);
    }

    @Override
    public Page<ProjectPlanTypeAttributeVO> getPage(Page<ProjectPlanTypeAttribute> pageRequest) throws Exception {
        LambdaQueryWrapper<ProjectPlanTypeAttribute> wrapper = new LambdaQueryWrapper<>(ProjectPlanTypeAttribute.class);
        ProjectPlanTypeAttribute query = pageRequest.getQuery();
        if (Objects.nonNull(query)) {
            String name = query.getName();
            if (StringUtils.hasText(name)) {
                wrapper.and(a -> a.like(ProjectPlanTypeAttribute::getName, name).or().like(ProjectPlanTypeAttribute::getNumber, name));
            }
        }
        IPage<ProjectPlanTypeAttribute> realPageRequest = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        IPage<ProjectPlanTypeAttribute> page = this.page(realPageRequest, wrapper);
        return new Page<>(page.getCurrent(), page.getSize(), page.getTotal(), BeanCopyUtils.convertListTo(page.getRecords(), ProjectPlanTypeAttributeVO::new));
    }
}
