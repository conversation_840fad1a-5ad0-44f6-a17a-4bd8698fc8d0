package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.api.holidays.domain.dto.CalculationWorkdayNumDTO;
import com.chinasie.orion.api.holidays.domain.vo.CalculationWorkdayNumVO;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.DictConstant;
import com.chinasie.orion.constant.ProjectSchemeEnum;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.dto.ProjectApprovalMilestoneDTO;
import com.chinasie.orion.domain.dto.approval.excel.ProjectApprovalMilestoneExportDTO;
import com.chinasie.orion.domain.dto.approval.excel.ProjectApprovalMilestoneTplDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.ProjectApprovalMilestoneVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.export.ImportExcelErrorNoteVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.PMIService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.ProjectApprovalMilestoneMapper;
import com.chinasie.orion.sdk.domain.vo.business.*;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * ProjectApprovalMilestone 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-07 16:23:23
 */
@Service
@Slf4j
public class ProjectApprovalMilestoneServiceImpl extends OrionBaseServiceImpl<ProjectApprovalMilestoneMapper,ProjectApprovalMilestone> implements ProjectApprovalMilestoneService {

    @Autowired
    private ProjectApprovalMilestoneMapper projectApprovalMilestoneMapper;

    @Resource
    private DictRedisHelper dictRedisHelper;

    @Resource
    private ProjectService projectService;
    @Resource
    private ProjectSchemeService projectSchemeService;

    @Resource
    private UserRedisHelper userRedisHelper;

    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Resource
    private ClassRedisHelper classRedisHelper;

    @Resource
    private ProjectApprovalService projectApprovalService;
    @Autowired
    private ProjectAchievementService projectAchievementService;
    @Autowired
    private OrionJ2CacheService orionJ2CacheService;
    @Autowired
    private PMIService pmiService;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    public ProjectApprovalMilestoneVO detail(String id) throws Exception {
        ProjectApprovalMilestone projectApprovalMilestone =projectApprovalMilestoneMapper.selectById(id);
        ProjectApprovalMilestoneVO result = BeanCopyUtils.convertTo(projectApprovalMilestone,ProjectApprovalMilestoneVO::new);
        if(!StrUtil.isEmpty(result.getMilestoneId())){
            ProjectScheme projectScheme=projectSchemeService.getById(result.getMilestoneId());
            result.setResDept(projectScheme.getRspSubDept());
            result.setResPerson(projectScheme.getRspUser());
            result.setBeginTime(projectScheme.getBeginTime());
            result.setEndTime(projectScheme.getEndTime());
        }
        String schemeActivity = result.getSchemeActivity();
        if (StrUtil.isNotBlank(schemeActivity)){
            result.setSchemeActivityList(Arrays.asList(schemeActivity.split(",")));
        }
        UserVO userVO = userRedisHelper.getUserById(result.getResPerson()==null?"0":result.getResPerson());
        DeptVO deptVO = deptRedisHelper.getDeptById(result.getResDept()==null?"0":result.getResDept());
        if(ObjectUtil.isNotEmpty(userVO)){
            result.setResPersonName(userVO.getName());
        }
        if(ObjectUtil.isNotEmpty(deptVO)){
            result.setResDeptName(deptVO.getName());
        }
        return result;
    }

    /**
     *  新增
     *
     * * @param projectApprovalMilestoneDTO
     */
    @Override
    @Transactional
    //TODO 6/12 审查 李思睿  事务异常捕获？
    public  Boolean create(List<ProjectApprovalMilestoneDTO> projectApprovalMilestoneDTOs) throws Exception {

        List<ProjectApprovalMilestone> milestones = new ArrayList<>();

        projectApprovalMilestoneDTOs.forEach(projectApprovalMilestoneDTO -> {
            ProjectApprovalMilestone milestone = BeanCopyUtils.convertTo(projectApprovalMilestoneDTO, ProjectApprovalMilestone::new);
            List<String> schemeActivityList = projectApprovalMilestoneDTO.getSchemeActivityList();
            if (CollectionUtil.isNotEmpty(schemeActivityList)){
                String join = String.join(",", schemeActivityList);
                milestone.setSchemeActivity(join);
            }
            List<String> users = projectApprovalMilestoneDTO.getParticipantUsersList();
//            if (CollectionUtil.isNotEmpty(users)){
//                String join = String.join(",", users);
//                milestone.setParticipantUsers(join);
//            }
            if (ObjectUtil.isNull(projectApprovalMilestoneDTO.getEndTime()) || ObjectUtil.isNull(projectApprovalMilestoneDTO.getBeginTime())){
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS,"开始时间或结束时间不能为空！");
            }
            if (projectApprovalMilestoneDTO.getEndTime().before(projectApprovalMilestoneDTO.getBeginTime())){
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS,"结束时间不能小于开始时间！");
            }
            milestone.setLevel(1);
            milestone.setSchemeStatus(Integer.valueOf(ProjectSchemeEnum.RESEALED.getValue()));
            milestones.add(milestone);
        });
        //保存附件
//        List<FileDTO> attachments = projectApprovalMilestoneDTO.getAttachments();
//        if (!CollectionUtils.isEmpty(attachments)) {
//            attachments.forEach(a -> {
//                a.setDataId(projectApprovalMilestone.getId());
//                a.setDataType("ProjectApprovalMileston");
//            });
//
//            try {
//                fileBo.addBatch(attachments);
//            } catch (Exception ex) {
//                log.error("项目立项里程碑文件保存异常", ex);
//            }
//
//        }
        this.saveBatch(milestones);
        return true;
    }

    /**
     *  编辑
     *
     * * @param projectApprovalMilestoneDTO
     */
    @Override
    public Boolean edit(ProjectApprovalMilestoneDTO projectApprovalMilestoneDTO) throws Exception {
        ProjectApprovalMilestone milestone =BeanCopyUtils.convertTo(projectApprovalMilestoneDTO,ProjectApprovalMilestone::new);
        List<String> schemeActivityList = projectApprovalMilestoneDTO.getSchemeActivityList();
        if (ObjectUtil.isNull(projectApprovalMilestoneDTO.getEndTime()) || ObjectUtil.isNull(projectApprovalMilestoneDTO.getBeginTime())){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS,"开始时间或结束时间不能为空！");
        }
        if (CollectionUtil.isNotEmpty(schemeActivityList)){
            String join = String.join(",", schemeActivityList);
            milestone.setSchemeActivity(join);
        }
        List<String> users = projectApprovalMilestoneDTO.getParticipantUsersList();
//        if (CollectionUtil.isNotEmpty(users)){
//            String join = String.join(",", users);
//            milestone.setParticipantUsers(join);
//        }
        int update =  projectApprovalMilestoneMapper.updateById(milestone);

        //编辑附件
//        List<FileDTO> attachments = projectApprovalMilestoneDTO.getAttachments();
//        List<FileVO> getFilesByDataIdResponse = fileBo.getFilesByDataId(projectApprovalMilestoneDTO.getId());
//        if (Objects.nonNull(getFilesByDataIdResponse) && !CollectionUtils.isEmpty(getFilesByDataIdResponse)) {
//            List<String> filesIds = getFilesByDataIdResponse.stream().map(FileVO::getId).collect(Collectors.toList());
//            filesIds.forEach(fid -> {
//                try {
//                    fileBo.deleteFileById(fid);
//                } catch (Exception e) {
//                    log.error("删除项目立项里程碑文件保存异常", e);
//                }
//            });
//        }
//        if (!CollectionUtils.isEmpty(attachments)) {
//            attachments.forEach(a -> {
//                a.setId(null);
//                a.setDataId(projectApprovalMilestoneDTO.getId());
//                a.setDataType("ProjectApprovalMileston");
//            });
//
//            try {
//                fileBo.addBatch(attachments);
//            } catch (Exception ex) {
//                log.error("项目项目立项里程碑文件保存异常", ex);
//            }
//        }
        return SqlHelper.retBool(update);
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        LambdaQueryWrapperX<ProjectAchievement> lambdaQueryWrapperX=new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.in(ProjectAchievement::getMilestoneId,ids);
        List<ProjectAchievement> projectAchievements =   projectAchievementService.list(lambdaQueryWrapperX);
        if(CollectionUtil.isNotEmpty(projectAchievements)){
            throw new PMSException(PMSErrorCode.KMS_DATA_QUOTE_STATUS,"成果有删除的里程碑");
        }
        int delete = projectApprovalMilestoneMapper.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    public Page<ProjectApprovalMilestoneVO> pages(Page<ProjectApprovalMilestoneDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<ProjectApprovalMilestone> condition = new LambdaQueryWrapperX<>( ProjectApprovalMilestone. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        if (ObjectUtil.isNull(pageRequest.getQuery()) || StrUtil.isBlank(pageRequest.getQuery().getApprovalId())){
            throw new PMSException(PMSErrorCode.PMS_ERROR_ID_NULL,"立项id不能为空！");
        }
        condition.eq(ProjectApprovalMilestone::getApprovalId,pageRequest.getQuery().getApprovalId());
        condition.orderByDesc(ProjectApprovalMilestone::getCreateTime);


        Page<ProjectApprovalMilestone> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectApprovalMilestone::new));

        PageResult<ProjectApprovalMilestone> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectApprovalMilestoneVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectApprovalMilestoneVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectApprovalMilestoneVO::new);
        if (CollectionUtil.isNotEmpty(vos)){
            setEveryName(vos);
        }
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void syncProjectScheme(String id) throws Exception {
        LambdaQueryWrapperX<ProjectApprovalMilestone> projectApprovalMilestoneLambdaQueryWrapperX=new LambdaQueryWrapperX<>();
        projectApprovalMilestoneLambdaQueryWrapperX.eq(ProjectApprovalMilestone::getApprovalId,id);
        List<ProjectApprovalMilestone> list=this.list(projectApprovalMilestoneLambdaQueryWrapperX);
//        if(CollectionUtil.isNotEmpty(list)){
//            saveProjectSchemes(list);
//        }


    }

//    public void saveProjectSchemes(List<ProjectApprovalMilestone> projectApprovalMilestones){
//        String   parentId = "0";
//        Project project = projectService.getById(projectApprovalMilestones.get(0).getProjectId());
//        List<ProjectScheme> projectSchemes = CollUtil.toList();
//        ClassVO classVO = classRedisHelper.getClassByClassName(ProjectScheme.class.getSimpleName());
//        List<ProjectScheme> projectSchemeList = projectSchemeService.list(new LambdaQueryWrapper<>(ProjectScheme.class)
//                .eq(ProjectScheme::getProjectId, project.getId()));
//        Long maxSort = projectSchemeList.stream().map(ProjectScheme::getSort).max(Comparator.comparing(Long::longValue)).orElse(0L);
//        AtomicLong sort = new AtomicLong(maxSort + 1);
//        String finalParentId = parentId;
//        String id = String.format("%s%s", Objects.isNull(classVO) ? "" : classVO.getCode(), IdUtil.getSnowflakeNextIdStr());
//        for(ProjectApprovalMilestone projectApprovalMilestone:projectApprovalMilestones) {
//            ProjectScheme projectScheme = new ProjectScheme();
//            projectScheme.setLevel(1);
//            projectScheme.setName(projectApprovalMilestone.getName());
//            projectScheme.setBeginTime(projectApprovalMilestone.getBeginTime());
//            projectScheme.setEndTime(projectApprovalMilestone.getEndTime());
//            projectScheme.setNodeType("milestone");
//            Calendar calendar = Calendar.getInstance();
//            calendar.setTime(projectApprovalMilestone.getBeginTime());
//            LocalDate date1=LocalDate.of(calendar.get(Calendar.YEAR),calendar.get(Calendar.MONTH) + 1, calendar.get(Calendar.DAY_OF_MONTH));
//            calendar.setTime(projectApprovalMilestone.getEndTime());
//            LocalDate date2=LocalDate.of(calendar.get(Calendar.YEAR),calendar.get(Calendar.MONTH) + 1, calendar.get(Calendar.DAY_OF_MONTH));
//            long daysBetween = ChronoUnit.DAYS.between(date1, date2);
//            projectScheme.setDurationDays((int) daysBetween);
//            projectScheme.setStatus(101);
////            projectScheme.setRspSubDept(projectApprovalMilestone.getResDept());
////            projectScheme.setRspUser(projectApprovalMilestone.getResPerson());
////            SimpleUser resSimpleUser = userRedisHelper.getSimpleUserById(projectApprovalMilestone.getResPerson());
////            projectScheme.setRspUserCode(resSimpleUser.getCode());
////            projectScheme.setRspSectionId(projectApprovalMilestone.getResOffice());
//            projectScheme.setId(id);
//            projectScheme.setParentId(parentId);
//            projectScheme.setProjectId(project.getId());
//            projectScheme.setTopSort(0);
//            projectScheme.setProjectNumber(project.getNumber());
//            projectScheme.setParentChain("0");
//            projectScheme.setSchemeNumber(project.getNumber() + "-" + sort);
//            projectScheme.setNumber(projectScheme.getSchemeNumber());
//            projectScheme.setSort(sort.getAndIncrement());
//            projectScheme.setLevel(projectScheme.getParentChain().split(",").length);
//            projectScheme.setCircumstance(0);
//            projectScheme.setStatus(Status.PENDING.getCode());
////            projectScheme.setCreatorId(projectApprovalMilestone.getResPerson());
//            projectScheme.setCreateTime(new Date());
////            projectScheme.setModifyId(projectApprovalMilestone.getResPerson());
//            projectScheme.setModifyTime(new Date());
//            projectSchemes.add(projectScheme);
//            projectApprovalMilestone.setMilestoneId(id);
//        }
//        projectSchemeService.saveBatch(projectSchemes);
//        this.updateBatchById(projectApprovalMilestones);
//    }

    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    public Page<ProjectApprovalMilestoneVO> getPages(Page<ProjectApprovalMilestoneDTO> pageRequest) throws Exception {
        Page<ProjectApprovalMilestone> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectApprovalMilestone::new));
        realPageRequest.setSearchConditions(pageRequest.getSearchConditions());
        LambdaQueryWrapperX<ProjectApprovalMilestone> objectLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        ProjectApprovalMilestoneDTO approvalMilestoneDTO=pageRequest.getQuery();
        objectLambdaQueryWrapperX.eqIfPresent(ProjectApprovalMilestone::getApprovalId,approvalMilestoneDTO.getApprovalId());
        objectLambdaQueryWrapperX.likeIfPresent(ProjectApprovalMilestone::getName,approvalMilestoneDTO.getName());
        objectLambdaQueryWrapperX = SearchConditionUtils.parseSearchConditionsWrapper(realPageRequest.getSearchConditions(),objectLambdaQueryWrapperX);
        PageResult<ProjectApprovalMilestone> page = projectApprovalMilestoneMapper.selectPage(realPageRequest,objectLambdaQueryWrapperX);
        Page<ProjectApprovalMilestoneVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectApprovalMilestoneVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectApprovalMilestoneVO::new);
        if(!CollectionUtils.isEmpty(vos)){
            ProjectApproval approval=projectApprovalService.getById(vos.get(0).getApprovalId());
            List<String> userIds=new ArrayList<>();
            List<String> deptIds=new ArrayList<>();
            Map<String,ProjectScheme> projectSchemeMap=new HashMap<>();
            if(approval.getStatus().equals(130)){
                List<String> stringList=vos.stream().map(ProjectApprovalMilestoneVO::getMilestoneId).collect(Collectors.toList());
                List<ProjectScheme> list=projectSchemeService.listByIds(stringList);
                projectSchemeMap=list.stream().collect(Collectors.toMap(ProjectScheme::getId, Function.identity()));
                userIds=list.stream().map(ProjectScheme::getRspUser).collect(Collectors.toList());
                deptIds=list.stream().map(ProjectScheme::getRspSubDept).collect(Collectors.toList());
            }else{
                userIds=vos.stream().map(ProjectApprovalMilestoneVO::getResPerson).collect(Collectors.toList());
                deptIds=vos.stream().map(ProjectApprovalMilestoneVO::getResDept).collect(Collectors.toList());
            }
            Map<String,String> organizationMap = new HashMap<>();
            if(!com.chinasie.orion.util.CollectionUtils.isBlank(deptIds)){
                List<DeptVO> deptVOList = deptRedisHelper.getDeptByIds(deptIds);
                if(!com.chinasie.orion.util.CollectionUtils.isBlank(deptVOList)){
                    organizationMap = deptVOList.stream().collect(Collectors.toMap(DeptVO :: getId,DeptVO :: getName));
                }
            }
            Map<String,String> userMap = new HashMap<>();
            if(!CollectionUtils.isEmpty(userIds)){
                List<UserVO> userVOS = userRedisHelper.getUserByIds(userIds);
                if(!CollectionUtils.isEmpty(userVOS)){
                    userMap = userVOS.stream().collect(Collectors.toMap(UserVO :: getId,UserVO :: getName));
                }
            }
            //TODO 整改 提取到公共常量里面去或者配置
            Map<String, DictValueVO> approvalSecrecyLevelMap = dictRedisHelper.getDictMapByCode("project_approval_secrecy_level");
            Map<String, DictValueVO> approvalTaskLevelMap = dictRedisHelper.getDictMapByCode("project_approval_task_level");
            for(ProjectApprovalMilestoneVO projectApprovalMilestoneVO:vos){
                if(ObjectUtil.isNotEmpty(approvalSecrecyLevelMap.get(projectApprovalMilestoneVO.getSecrecyLevel()))){
                    if(StrUtil.isNotBlank(projectApprovalMilestoneVO.getSecrecyLevel())){
                        projectApprovalMilestoneVO.setSecrecyLevelName(approvalSecrecyLevelMap.get(projectApprovalMilestoneVO.getSecrecyLevel()).getDescription());
                    }
                    if(StrUtil.isNotBlank(projectApprovalMilestoneVO.getTaskLevel())){
                        projectApprovalMilestoneVO.setTaskLevelName(approvalTaskLevelMap.get(projectApprovalMilestoneVO.getTaskLevel()).getDescription());
                    }
                    if(approval.getStatus().equals(130)){
                        ProjectScheme projectScheme= projectSchemeMap.get(projectApprovalMilestoneVO.getMilestoneId());
                        if(ObjectUtil.isNotEmpty(projectScheme)){
                            projectApprovalMilestoneVO.setResPersonName(userMap.get(projectScheme.getRspUser()));
                            projectApprovalMilestoneVO.setResDeptName(organizationMap.get(projectScheme.getRspSubDept()));
                            projectApprovalMilestoneVO.setSchemeStatus(projectScheme.getStatus());
                            projectApprovalMilestoneVO.setCircumstance(projectScheme.getCircumstance());
                            projectApprovalMilestoneVO.setResDept(projectScheme.getRspSubDept());
                            projectApprovalMilestoneVO.setResPerson(projectScheme.getRspUser());
                            projectApprovalMilestoneVO.setBeginTime(projectScheme.getBeginTime());
                            projectApprovalMilestoneVO.setEndTime(projectScheme.getEndTime());
                        }
                    }else{
                        projectApprovalMilestoneVO.setResPersonName(userMap.get(projectApprovalMilestoneVO.getResPerson()));
                        projectApprovalMilestoneVO.setResDeptName(organizationMap.get(projectApprovalMilestoneVO.getResDept()));
                    }
                }
            }
        }
        pageResult.setContent(vos);
        return pageResult;
    }



    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "立项论证-里程碑模板.xlsx";
        response.reset();
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("立项论证-里程碑模板.xlsx", StandardCharsets.UTF_8));

        ExcelUtils.writeTemplate(response, ProjectApprovalMilestoneTplDTO.class, fileName, "立项论证-里程碑模板", "项目立项里程碑导入模板");

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel, String approvalId) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ProjectApprovalMilestoneExcelListener excelReadListener = new ProjectApprovalMilestoneExcelListener();
        EasyExcel.read(inputStream,ProjectApprovalMilestoneTplDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ProjectApprovalMilestoneTplDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("项目立项里程碑导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ProjectApprovalMilestone> projectApprovalMilestones = new ArrayList<>();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        List<ImportExcelErrorNoteVO> errorNoteVOList = new ArrayList<>();
        for (int i = 0; i < dtoS.size(); i++) {
            ImportExcelErrorNoteVO importExcelErrorNoteVO = new ImportExcelErrorNoteVO();
            importExcelErrorNoteVO.setOrder(String.valueOf(i+1));
            ProjectApprovalMilestoneTplDTO dto = dtoS.get(i);
            String endTime = dto.getEndTime();
            String beginTime = dto.getBeginTime();
            if (StrUtil.isBlank(endTime)){
                result.setCode(400);
                result.setOom(String.format("序号%s 结束时间不存在！", i));
                return result;
            }
            if (StrUtil.isBlank(beginTime)){
                result.setCode(400);
                result.setOom(String.format("序号%s 开始时间不存在！", i));
                return result;
            }
            Date end = format.parse(endTime);
            Date begin = format.parse(beginTime);
            if (end.before(begin)){
                result.setCode(400);
                result.setOom(String.format("序号%s 结束时间小于开始时间！", i));
                return result;
            }
            ProjectApprovalMilestone milestone = BeanCopyUtils.convertTo(dto, ProjectApprovalMilestone::new);
            milestone.setBeginTime(begin);
            milestone.setEndTime(end);
            projectApprovalMilestones.add(milestone);
        }
        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ProjectApprovalMilestone-import::id", importId, projectApprovalMilestones, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);
        result.setErr(errorNoteVOList);
        return result;
    }


    @Override
    public Boolean importByExcel(String importId, String approvalId) throws Exception {
        List<ProjectApprovalMilestone> projectApprovalMilestones = (List<ProjectApprovalMilestone>) orionJ2CacheService.get("pmsx::ProjectApprovalMilestone-import::id", importId);
        log.info("项目立项里程碑导入的入库数据={}", JSONUtil.toJsonStr(projectApprovalMilestones));
        projectApprovalMilestones.forEach(projectApprovalMilestone -> {
            projectApprovalMilestone.setApprovalId(approvalId);
            projectApprovalMilestone.setLevel(1);
            projectApprovalMilestone.setSchemeStatus(Integer.valueOf(ProjectSchemeEnum.RESEALED.getValue()));
            projectApprovalMilestone.setStatus(Integer.valueOf(ProjectSchemeEnum.RESEALED.getValue()));
        });
        this.saveBatch(projectApprovalMilestones);

        List<CalculationWorkdayNumDTO> workdayNumDTOS = new ArrayList<>();
        projectApprovalMilestones.forEach(projectApprovalMilestone -> {
            CalculationWorkdayNumDTO dto = new CalculationWorkdayNumDTO();
            dto.setEndDate(projectApprovalMilestone.getEndTime());
            dto.setStartDate(projectApprovalMilestone.getBeginTime());
            dto.setBusinessId(projectApprovalMilestone.getId());
            workdayNumDTOS.add(dto);
        });
        ResponseDTO<List<CalculationWorkdayNumVO>> listResponseDTO;
        try {
            listResponseDTO = pmiService.calculationIntervalDaysBatch(workdayNumDTOS);
        } catch (Exception e) {
            throw new PMSException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR, "获取工期时间出错！");
        }
        List<CalculationWorkdayNumVO> result = listResponseDTO.getResult();
        if (CollectionUtil.isEmpty(result)){
            throw new PMSException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR, "获取工期时间出错！");
        }
        Map<String, Long> numMap = result.stream().collect(Collectors.toMap(CalculationWorkdayNumVO::getBusinessId, CalculationWorkdayNumVO::getWorkdayNum));
        projectApprovalMilestones.forEach(projectApprovalMilestone -> projectApprovalMilestone.setDurationDays(Math.toIntExact(numMap.get(projectApprovalMilestone.getId()))));
        this.updateBatchById(projectApprovalMilestones);
        orionJ2CacheService.delete("pmsx::ProjectApprovalMilestone-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ProjectApprovalMilestone-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response, String approvalId) throws Exception {
        if (StrUtil.isBlank(approvalId)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_ID_NULL,"立项id不能为空");
        }
        LambdaQueryWrapperX<ProjectApprovalMilestone> condition = new LambdaQueryWrapperX<>( ProjectApprovalMilestone. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.eq(ProjectApprovalMilestone::getApprovalId,approvalId);
        condition.orderByDesc(ProjectApprovalMilestone::getCreateTime);
        List<ProjectApprovalMilestone> projectApprovalMilestones =   this.list(condition);

        List<ProjectApprovalMilestoneExportDTO> dtos = setExcelPortName(projectApprovalMilestones);

        String fileName = "项目立项里程碑数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectApprovalMilestoneExportDTO.class,dtos );

    }

    private List<ProjectApprovalMilestoneExportDTO> setExcelPortName(List<ProjectApprovalMilestone> milestones) {
        if (CollectionUtil.isEmpty(milestones)){
            return new ArrayList<>();
        }
        List<UserVO> allUser = userRedisHelper.getAllUser();
        Map<String, String> userMap = allUser.stream().collect(Collectors.toMap(UserVO::getId, UserVO::getName));
//        Map<String, SimpleDeptVO> deptMap = deptRedisHelper.getAllSimpleDeptMap();
        Map<String, DictValueVO> dictMap = dictRedisHelper.getDictMapByCode(DictConstant.SCHEME_ACTIVITY);
        List<ProjectApprovalMilestoneExportDTO> exportDTOList = new ArrayList<>();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        for (ProjectApprovalMilestone milestone : milestones) {
            ProjectApprovalMilestoneExportDTO exportDTO = BeanCopyUtils.convertTo(milestone, ProjectApprovalMilestoneExportDTO::new);
            DataStatusVO dataStatus = milestone.getDataStatus();
            if (ObjectUtil.isNotNull(dataStatus)){
                exportDTO.setSchemeStatus(dataStatus.getName());
            }
            String schemeActivity = milestone.getSchemeActivity();
            if (StrUtil.isNotBlank(schemeActivity)){
                List<String> activity = Arrays.asList(schemeActivity.split(","));
                List<String> join = new ArrayList<>();
                activity.forEach(a -> {
                    if (StrUtil.isNotBlank(dictMap.get(a).getName())){
                        join.add(dictMap.get(a).getName());
                    }
                });
                if (CollectionUtil.isNotEmpty(join)){
                    exportDTO.setSchemeActivity(String.join(",",join));
                }
            }
            exportDTO.setEndTime(format.format(milestone.getEndTime()));
            exportDTO.setBeginTime(format.format(milestone.getBeginTime()));
            exportDTO.setSchemeType("里程碑");
            exportDTOList.add(exportDTO);
        }
        return exportDTOList;
    }


    public static class ProjectApprovalMilestoneExcelListener extends AnalysisEventListener<ProjectApprovalMilestoneTplDTO> {

        private final List<ProjectApprovalMilestoneTplDTO> data = new ArrayList<>();

        @Override
        public void invoke(ProjectApprovalMilestoneTplDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ProjectApprovalMilestoneTplDTO> getData() {
            return data;
        }
    }




    public void  setEveryName(List<ProjectApprovalMilestoneVO> vos)throws Exception {
        vos.forEach(vo->{
            String participantUsers = vo.getParticipantUsers();
            if (StrUtil.isNotBlank(participantUsers)){
                vo.setParticipantUsersList(Arrays.asList(participantUsers.split(",")));
            }
            String schemeActivity = vo.getSchemeActivity();
            if (StrUtil.isNotBlank(schemeActivity)){
                vo.setSchemeActivityList(Arrays.asList(schemeActivity.split(",")));
            }
        });


    }

    @Override
    public List<ProjectApprovalMilestoneVO> getList(String approvalId) throws Exception {
        LambdaQueryWrapperX<ProjectApprovalMilestone> condition = new LambdaQueryWrapperX<>( ProjectApprovalMilestone. class);
        condition.eq(ProjectApprovalMilestone::getApprovalId,approvalId);
        condition.orderByDesc(ProjectApprovalMilestone::getCreateTime);
        List<ProjectApprovalMilestone> list = this.list( condition);
        List<ProjectApprovalMilestoneVO> vos = BeanCopyUtils.convertListTo(list, ProjectApprovalMilestoneVO::new);
        return vos;
    }

}
