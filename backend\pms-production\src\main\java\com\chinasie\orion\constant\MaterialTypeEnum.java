package com.chinasie.orion.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/13/16:53
 * @description:
 */
public enum MaterialTypeEnum {

    OUT("out","出库"),
    EDIT_INFO("edit","信息变更"),
    INPUT("input","入库");

    private String key;

    private String desc;

    MaterialTypeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }


    public static Map<String,String> keyToDesc(){
        Map<String,String> map = new HashMap<>();
        MaterialTypeEnum[] values = MaterialTypeEnum.values();
        for (MaterialTypeEnum value : values) {
            map.put(value.getKey(),value.getDesc());
        }
        return  map;
    }
}
