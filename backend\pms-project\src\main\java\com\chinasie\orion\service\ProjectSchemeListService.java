package com.chinasie.orion.service;

import cn.hutool.core.collection.CollUtil;
import com.chinasie.orion.domain.dto.GantDTO;
import com.chinasie.orion.domain.dto.ProjectSchemeDTO;
import com.chinasie.orion.domain.dto.ProjectSchemeDragDTO;
import com.chinasie.orion.domain.request.ListRequest;
import com.chinasie.orion.domain.vo.ProjectSchemeVO;
import com.chinasie.orion.domain.vo.projectscheme.SchemeGanttHeadVO;
import com.chinasie.orion.domain.vo.projectscheme.SchemeGanttVO;

import java.util.Date;
import java.util.List;

public interface ProjectSchemeListService {

    /**
     * 项目计划列表
     *
     * @param request
     * @return
     */
    default List<ProjectSchemeVO> schemeList(ListRequest request) throws Exception {
        return CollUtil.toList();
    }

    /**
     * 甘特图
     * @param projectId
     * @param gantDTO
     * @return
     * @throws Exception
     */
    default List<SchemeGanttVO> ganttPic(String projectId, GantDTO gantDTO) throws Exception {
        return CollUtil.toList();
    }

}
