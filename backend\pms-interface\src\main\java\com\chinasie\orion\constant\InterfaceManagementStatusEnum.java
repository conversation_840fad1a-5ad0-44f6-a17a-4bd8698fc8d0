package com.chinasie.orion.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/01/30/10:10
 * @description:
 */
public enum InterfaceManagementStatusEnum {

    IM_APPLY_FOR(101, "已创建"),
    IM_APPROVALING(110, "流程中"),
    IM_APPROVAL_FINISH(130, "已审批"),
    IM_BACK(120, "已驳回"),
    IM_CLOSE(111, "已关闭");


    private Integer status;

    private String desc;



    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    InterfaceManagementStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
