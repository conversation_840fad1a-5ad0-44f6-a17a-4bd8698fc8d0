package com.chinasie.orion.constant;

import java.util.HashMap;
import java.util.Map;

public enum MarketContractBusinessTypeEnums {
    DOMESTIC_MAIN_BUSINESS("domestic_main_business", "境内主营业务"),
    OVERSEAS_MAIN_BUSINESS("overseas_main_business", "境外主营业务"),
    WASTE_MATERIALS_DISPOSAL("waste_materials_disposal", "闲废物资处理"),
    OTHER_SALES_CONTRACTS("other_sales_contracts", "其他销售合同"),
    ;

    private String key;

    private String desc;

    MarketContractBusinessTypeEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }


    public static Map<String,String> keyToDesc(){
        Map<String,String> map = new HashMap<>();
        MarketContractBusinessTypeEnums[] values = MarketContractBusinessTypeEnums.values();
        for (MarketContractBusinessTypeEnums value : values) {
            map.put(value.getKey(),value.getDesc());
        }
        return  map;
    }
}
