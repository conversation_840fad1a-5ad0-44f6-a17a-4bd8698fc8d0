<template>
  <div class="bpmn-wrap">
    <template v-if="menuActionItem">
      <slot
        name="flowInfo"
        :flow-data="menuActionItem"
      >
        <div class="info">
          <div>流程名称：{{ menuActionItem?.procInstName }}</div>
          <div>流程编号：{{ menuActionItem?.businessKey }}</div>
          <div>发起人：{{ menuActionItem?.creatorName }}</div>
          <div>状态：{{ menuActionItem?.statusName }}</div>
        </div>
      </slot>
    </template>

    <BasicTabs
      v-model:tabsIndex="tabsIndex"
      :tabs="tabs"
      @tabsChange="tabsChange"
    />
    <div
      v-if="tabsIndex === 0"
      class="content-main"
    >
      <ApprovalObject v-bind="$attrs" />
    </div>
    <div
      v-if="tabsIndex === 1"
      class="content-main"
    >
      <FlowInfo />
    </div>
    <div
      v-if="tabsIndex === 2"
      class="content-main"
    >
      <Record />
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent, inject, reactive, toRefs,
} from 'vue';
import { BasicTabs } from 'lyra-component-vue3';
import ApprovalObject from './ApprovalObject/index.vue';
import FlowInfo from './FlowInf/index.vue';
import Record from './Record/index.vue';

export default defineComponent({
  components: {
    BasicTabs,
    ApprovalObject,
    FlowInfo,
    Record,
  },
  props: {
    menuActionItem: {
      type: Object,
      default: () => null,
    },
  },
  setup() {
    const bpmnModuleData = inject('bpmnModuleData');

    const state: any = reactive({
      tabs: [
        {
          name: '审批对象',
        },
        {
          name: '流程信息',
        },
        {
          name: '历史记录',
        },
      ],
      tabsIndex: 0,
    });
    return {
      ...toRefs(state),
      tabsChange(index) {
        state.tabsIndex = index;
      },
    };
  },
});
</script>

<style scoped lang="less">
  .bpmn-wrap {
    display: flex;
    flex-direction: column;
    padding: 15px;
    height: 100%;

    > .info {
      > div {
        display: inline-block;
        margin-right: 40px;
        line-height: 30px;
      }
    }

    > .content-main {
      flex: 1;
      height: 1px;
      overflow-y: auto;
    }
  }
</style>
