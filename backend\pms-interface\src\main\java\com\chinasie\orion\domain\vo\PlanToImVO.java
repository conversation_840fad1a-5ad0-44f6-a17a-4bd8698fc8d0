package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * PlanToIm VO对象
 *
 * <AUTHOR>
 * @since 2024-02-22 21:17:17
 */
@ApiModel(value = "PlanToImVO对象", description = "计划和接口的关联关系")
@Data
public class PlanToImVO extends ObjectVO implements Serializable {

    /**
     * 副Id
     */
    @ApiModelProperty(value = "副Id")
    private String toId;

    /**
     * 主id
     */
    @ApiModelProperty(value = "主id")
    private String fromId;

}
