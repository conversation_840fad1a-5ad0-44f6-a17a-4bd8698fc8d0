<script setup lang="ts">
import {
  isPower, OrionTable, BasicTableAction, IOrionTableActionItem, BasicButton, downloadByData as basicDownloadByData,
} from 'lyra-component-vue3';
import {
  computed, ComputedRef, ref, Ref, h,
} from 'vue';
import { Modal } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { openFormDrawer, formatTableColumns, formatActionsPower } from './utils';
import BusinessAssessmentEdit from './BusinessAssessmentEdit.vue';
import Api from '/@/api';

const router = useRouter();
const tableRef: Ref = ref();
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const columns = [
  {
    title: '操作',
    dataIndex: 'actions',
    width: 160,
    fixed: 'right',
    slots: { customRender: 'actions' },
  },
  {
    title: '指标编码',
    dataIndex: 'indexNumber',
  },
  {
    title: '考核类型',
    dataIndex: 'indexType',
  },
  {
    title: '目标值',
    dataIndex: 'indexTarget',
  },
  {
    title: '实际值',
    dataIndex: 'indexActual',
  },
  {
    title: '状态灯',
    dataIndex: 'indexStatus',
    customRender({ text }) {
      return text === 'red' ? h('span', { style: 'color:red' }, '红色') : text === 'green' ? h('span', { style: 'color:green' }, '绿色') : h('span', { style: 'color:yellow' }, '黄色');
    },
  },

];

const tableOptions = {
  rowSelection: {
    onChange(keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  smallSearchField: ['indexNumber'],
  columns: formatTableColumns(columns),
  api: (params: Record<string, any>) => new Api('/pms/productionIndex').fetch({
    ...params,
    power: {
      // containerCode: 'PAS_KHGL_container_01',
      // pageCode: 'KHGL_001',
    },
  }, 'page', 'POST'),
};

const toolButtons: ComputedRef<Record<string, any>[]> = computed(() => [
  {
    event: 'add',
    text: '新增',
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
    // code: 'PAS_KHGL_container_01_button_01',
  },

]);
// 详情
function onDetails(record: Record<string, any>) {
  router.push({
    name: 'CustomerInfoDetails',
    params: {
      id: record.id,
    },
  });
}
function toolClick({ event }: Record<string, any>) {
  switch (event) {
    case 'add':
      openFormDrawer(BusinessAssessmentEdit, null, updateTable);
      break;
    case 'batchDelete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除已勾选的数据吗？',
        onOk: () => deleteApi(selectedRows.value.map((item) => item.id)),
      });
      break;
    case 'enable':
      break;
    case 'disable':
      break;
    case 'batchExport':
      Modal.confirm({
        title: '导出操作！',
        content: '确定要批量导出数据吗？',
        onOk: () => downLoad(),
      });
      break;
  }
}
async function downLoad() {
  const tableIds = selectedRows.value.map((item) => item.id);
  // let query = getParams();
  await basicDownloadByData(
    '/api/pms/customerInfo/export/excel',
    tableIds,
    '',
    'POST',
    false,
    false,
  );
}

const actions: IOrionTableActionItem[] = formatActionsPower([
  {
    text: '编辑',
    event: 'edit',
    //  // code: 'edit-button-042a7f-customerInfo-SGMe48we',
    // code: 'PAS_KHGL_container_02_button_01',
  },
  // {
  //   text: '查看',
  //   event: 'view',
  //    // code: 'PAS_KHGL_container_02_button_02',
  // //  // code: 'view-button-042a7f-customerInfo-SGMe48we',
  // },
  // {
  //   text: '删除',
  //   event: 'delete',
  //    // code: 'PAS_KHGL_container_02_button_03',
  // //  // code: 'delete-button-042a7f-customerInfo-SGMe48we',
  // },
]);

function actionClick(actionItem: { event: string }, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'edit':
      openFormDrawer(BusinessAssessmentEdit, record, updateTable);
      break;
    case 'view':
      router.push({
        name: 'CustomerInfoDetails',
        params: {
          id: record.id,
        },
      });
      break;
    case 'delete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除这条数据吗？',
        onOk: () => deleteApi([record.id]),
      });
      break;
  }
}

function updateTable() {
  tableRef.value?.reload();
}

function deleteApi(ids: string[]) {
  return new Promise((resolve) => {
    new Api('/pms/customerInfo').fetch(ids, 'remove', 'DELETE').then(() => {
      updateTable();
    }).finally(() => {
      resolve('');
    });
  });
}

function getButtonProps(item) {
  if (item.event !== 'add' && item.event !== 'batchExport') {
    item.disabled = !selectedRows.value.length;
  }
  return item;
}
</script>

<template>
  <Layout
    v-get-power="{pageCode:'KHGL_001'}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <template
          v-for="button in toolButtons"
          :key="button.event"
        >
          <BasicButton
            v-is-power="[button.code]"
            v-bind="getButtonProps(button)"
            @click="toolClick(button)"
          >
            {{ button.text }}
          </BasicButton>
        </template>
      </template>
      <template #actions="{ record }">
        <BasicTableAction
          :actions="actions"
          :record="record"
          @actionClick="actionClick($event, record)"
        />
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less"></style>
