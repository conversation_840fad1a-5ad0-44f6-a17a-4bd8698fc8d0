<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.chinasie.orion</groupId>
        <artifactId>spm</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>spm-app</artifactId>
    <dependencies>
        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>spm-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>spm-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>spm-procurement</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
        </dependency>
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-core</artifactId>
            <version>7.15.0</version>
        </dependency>
        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>orion-spring-boot-starter-msc-api</artifactId>
        </dependency>
    </dependencies>

    <properties>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <build>
        <plugins>
            <!--方式二 只能将依赖放入平级目录-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.2.0</version>
                <configuration>
                    <archive>
                        <manifest>
                            <!-- 配置加入依赖包 -->
                            <addClasspath>true</addClasspath>
                            <classpathPrefix>lib/</classpathPrefix>
                            <useUniqueVersions>false</useUniqueVersions>
                            <!-- Spring Boot 启动类(自行修改) -->
                            <mainClass>com.chinasie.orion.SPMApplication</mainClass>
                        </manifest>
                    </archive>
                    <!-- jar 输出目录 -->
                    <outputDirectory>${project.build.directory}/spm/</outputDirectory>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <!-- 复制依赖 -->
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <!-- 依赖包 输出目录 -->
                            <outputDirectory>${project.build.directory}/spm/lib</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>