package com.chinasie.orion.management.service.impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.management.domain.dto.ProjectInitiationWBSDTO;
import com.chinasie.orion.management.domain.entity.ProjectInitiationWBS;
import com.chinasie.orion.management.domain.vo.ProjectInitiationWBSVO;
import com.chinasie.orion.management.repository.ProjectInitiationWBSMapper;
import com.chinasie.orion.management.service.ProjectInitiationWBSService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;


/**
 * <p>
 * ProjectInitiationWBS 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-16 14:52:10
 */
@Service
@Slf4j
public class ProjectInitiationWBSServiceImpl extends OrionBaseServiceImpl<ProjectInitiationWBSMapper, ProjectInitiationWBS> implements ProjectInitiationWBSService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProjectInitiationWBSVO detail(String id, String pageCode) throws Exception {
        ProjectInitiationWBS projectInitiationWBS = this.getById(id);
        ProjectInitiationWBSVO result = BeanCopyUtils.convertTo(projectInitiationWBS, ProjectInitiationWBSVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    @Override
    public List<ProjectInitiationWBSVO> getByProjectNumber(String projectNumber) throws Exception {
        LambdaQueryWrapperX<ProjectInitiationWBS> condition = new LambdaQueryWrapperX<>(ProjectInitiationWBS.class);
        condition.eq(ProjectInitiationWBS::getProjectNumber, projectNumber);
        List<ProjectInitiationWBS> projects = this.list(condition);
        if (!CollectionUtils.isEmpty(projects)) {
            List<ProjectInitiationWBSVO> vos = BeanCopyUtils.convertListTo(projects, ProjectInitiationWBSVO::new);
            setEveryName(vos);
            return vos;
        }
        return new ArrayList<>();
    }

    /**
     * 新增
     * <p>
     * * @param projectInitiationWBSDTO
     */
    @Override
    public String create(ProjectInitiationWBSDTO projectInitiationWBSDTO) throws Exception {
        ProjectInitiationWBS projectInitiationWBS = BeanCopyUtils.convertTo(projectInitiationWBSDTO, ProjectInitiationWBS::new);
        this.save(projectInitiationWBS);

        String rsp = projectInitiationWBS.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param projectInitiationWBSDTO
     */
    @Override
    public Boolean edit(ProjectInitiationWBSDTO projectInitiationWBSDTO) throws Exception {
        ProjectInitiationWBS projectInitiationWBS = BeanCopyUtils.convertTo(projectInitiationWBSDTO, ProjectInitiationWBS::new);

        this.updateById(projectInitiationWBS);

        String rsp = projectInitiationWBS.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectInitiationWBSVO> pages(Page<ProjectInitiationWBSDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProjectInitiationWBS> condition = new LambdaQueryWrapperX<>(ProjectInitiationWBS.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ProjectInitiationWBS::getCreateTime);

        ProjectInitiationWBSDTO query = pageRequest.getQuery();
        if(Objects.nonNull(query)){
            if(StringUtils.hasText(query.getProjectNumber())){
                condition.eq(ProjectInitiationWBS::getProjectNumber, query.getProjectNumber());
            }
        }
        Page<ProjectInitiationWBS> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectInitiationWBS::new));

        PageResult<ProjectInitiationWBS> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectInitiationWBSVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectInitiationWBSVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectInitiationWBSVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "项目立项WBS预算导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectInitiationWBSDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ProjectInitiationWBSExcelListener excelReadListener = new ProjectInitiationWBSExcelListener();
        EasyExcel.read(inputStream, ProjectInitiationWBSDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ProjectInitiationWBSDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("项目立项WBS预算导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ProjectInitiationWBS> projectInitiationWBSes = BeanCopyUtils.convertListTo(dtoS, ProjectInitiationWBS::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ProjectInitiationWBS-import::id", importId, projectInitiationWBSes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ProjectInitiationWBS> projectInitiationWBSes = (List<ProjectInitiationWBS>) orionJ2CacheService.get("pmsx::ProjectInitiationWBS-import::id", importId);
        log.info("项目立项WBS预算导入的入库数据={}", JSONUtil.toJsonStr(projectInitiationWBSes));

        this.saveBatch(projectInitiationWBSes);
        orionJ2CacheService.delete("pmsx::ProjectInitiationWBS-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ProjectInitiationWBS-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ProjectInitiationWBS> condition = new LambdaQueryWrapperX<>(ProjectInitiationWBS.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ProjectInitiationWBS::getCreateTime);
        List<ProjectInitiationWBS> projectInitiationWBSes = this.list(condition);

        List<ProjectInitiationWBSDTO> dtos = BeanCopyUtils.convertListTo(projectInitiationWBSes, ProjectInitiationWBSDTO::new);

        String fileName = "项目立项WBS预算数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectInitiationWBSDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<ProjectInitiationWBSVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }


    public static class ProjectInitiationWBSExcelListener extends AnalysisEventListener<ProjectInitiationWBSDTO> {

        private final List<ProjectInitiationWBSDTO> data = new ArrayList<>();

        @Override
        public void invoke(ProjectInitiationWBSDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ProjectInitiationWBSDTO> getData() {
            return data;
        }
    }


}
