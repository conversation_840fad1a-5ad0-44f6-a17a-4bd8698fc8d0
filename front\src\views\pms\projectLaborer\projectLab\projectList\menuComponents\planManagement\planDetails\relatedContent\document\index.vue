<template>
  <Layout class="ui-2-0">
    <BasicUpload
      v-if=" isPower('LCB_container_button_27', powerData) "
      class="mb10"
      :max-number="100"
      :multiple="true"
      button-text="上传附件"
      button-type="primary"
      accept="*"
      :is-classification="true"
      @saveChange="handleSaveFile"
    />
    <BasicTitle title="计划关联文档">
      <BasicTable
        ref="tableRef"
        :loading="loading"
        :show-index-column="false"
        :columns="columns"
        :data-source="dataSource"
        :pagination="false"
        :max-height="260"
        :row-selection="pageType==='page'?{ type: 'checkbox' }:false"
        row-key="id"
      />
    </BasicTitle>
    <BasicTitle title="零组件关联文档">
      <BasicTable
        :loading="loading2"
        :show-index-column="false"
        :columns="columns2"
        :data-source="dataSource2"
        :pagination="false"
        :max-height="260"
        row-key="id"
        @expand="expandRow"
      />
    </BasicTitle>
  </Layout>
  <NewButtonModal
    v-if="pageType==='page'"
    :btn-object-data="btnConfig"
    @clickType="clickType"
  />
  <Edit
    v-if="edit.visible"
    :data="edit"
    @submit="submitEdit"
  />
  <SearchModal
    v-if="pageType==='page'"
    @register="searchRegister"
    @searchEmit="searchEmit"
  />
</template>

<script>
import {
  BasicTable, Layout, BasicUpload, isPower, useDrawer,
} from 'lyra-component-vue3';
import NewButtonModal from '/@/views/pms/projectLaborer/componentsList/buttonModal/newButtonModal.vue';
import {
  reactive, toRefs, ref, onMounted, inject, computed,
} from 'vue';
import Api from '/@/api';
import BasicTitle from '/@/views/pms/projectLaborer/componentsList/basicTitle/index.vue';

import { message, Modal } from 'ant-design-vue';
import { downLoadById } from '/@/views/pms/projectLaborer/utils/file/download';
import dayjs from 'dayjs';
import Edit from './Edit.vue';
import SearchModal from './SearchModal.vue';
export default {
  name: 'Index',
  components: {
    Edit,
    BasicTable,
    BasicTitle,
    BasicUpload,
    Layout,
    NewButtonModal,
    SearchModal,
  },
  props: {
    pageType: {
      type: String,
      default: 'page',
    },
    formId: {
      type: String,
      default: '',
    },
    projectId: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const [searchRegister, { openDrawer: openSearchDrawer }] = useDrawer();
    const state = reactive({
      edit: {},
      tableRef: ref(),
      loading: false,
      dataSource: [],
      columns: [
        {
          title: '名称',
          dataIndex: 'fullName',
          align: 'left',
          minWidth: 200,
        },
        {
          title: '版本',
          dataIndex: 'revId',
        },
        {
          title: '状态',
          dataIndex: 'statusName',
        },
        {
          title: '所有者',
          dataIndex: 'ownerName',
        },
        {
          title: '修改时间',
          dataIndex: 'modifyTime',
        },
      ],
      loading2: false,
      dataSource2: [],
      columns2: [
        {
          title: '名称',
          dataIndex: 'name',
          align: 'left',
          minWidth: 200,
        },
        {
          title: '编号',
          dataIndex: 'number',
          align: 'left',
        },
        {
          title: '版本',
          dataIndex: 'revId',
        },
        {
          title: '状态',
          dataIndex: 'statusName',
        },
        {
          title: '所有者',
          dataIndex: 'ownerUserName',
        },
        {
          title: '修改时间',
          dataIndex: 'modifyTime',
          customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'),
        },
      ],
      powerData: [],
      fileId: undefined,
    });
    state.powerData = inject('powerData');
    const state6 = reactive({
      btnConfig: {
        // open: { show: true },
        open: { show: computed(() => isPower('LCB_container_button_26', state.powerData)) },
        rename: { show: computed(() => isPower('LCB_container_button_28', state.powerData)) },
        delete: { show: computed(() => isPower('LCB_container_button_29', state.powerData)) },
        download: { show: computed(() => isPower('LCB_container_button_32', state.powerData)) },
        search: { show: computed(() => isPower('LCB_container_button_100', state.powerData)) },
      },
    });
    function clickType(type) {
      if (type === 'search') {
        openSearchDrawer(true);
      }
      if (type === 'open') {
        if (state.tableRef.getSelectRows()?.length !== 1) {
          message.info('请选择一条数据进行操作');
          return;
        }
        let record = state.tableRef.getSelectRows()[0];
        window.open(`/api/document-platform/document/preview?fileId=${record.id}&fileName=${encodeURIComponent(record.name)}${record.filePostfix}&baseHost=${location.host}&fileExt=${record.filePostfix}`);
      }
      const bool = isSelectCheck(type);
      if (type === 'download' && bool) {
        const selectedRowKeys = state.tableRef.getSelectRowKeys();
        selectedRowKeys.map((id) => {
          downLoadById(id);
        });
      }
      if (type === 'delete' && bool) {
        Modal.confirm({
          title: '确认提示',
          content: '请确认是否删除选中任务？',
          onOk() {
            delPage();
          },
        });
      }
      if (type === 'rename' && bool) {
        const selectedRowKeys = state.tableRef.getSelectRows();
        state.edit = {
          visible: true,
          form: {
            id: selectedRowKeys[0].id,
            name: selectedRowKeys[0].name,
          },
        };
      }
    }

    function delPage() {
      const love = {
        className: 'Plan',
        moduleName: '项目管理-计划管理-项目计划-关联内容-关联文档',
        type: 'DELETE',
        remark: `删除了【${state.tableRef.getSelectRowKeys()}】`,
      };
      new Api('/pms', love)
        .fetch(state.tableRef.getSelectRowKeys(), 'document/removeBatch', 'DELETE')
        .then(() => {
          message.success('操作成功');
          getPage();
        });
    }
    function isSelectCheck(type) {
      const selectedRowKeys = state.tableRef.getSelectRowKeys();
      if (selectedRowKeys.length === 0) {
        message.warning('请选择数据进行操作');
        return false;
      }
      if (selectedRowKeys.length === 1 || type === 'delete' || type === 'download') {
        return true;
      }
      if (selectedRowKeys.length > 1) {
        message.warning('请选择一条数据进行操作');
        return false;
      }
    }
    function getPage(obj = {}) {
      state.loading = true;
      const love = {
        id: props.formId,
        className: 'Plan',
        moduleName: '项目管理-计划管理-项目计划-关联内容-关联文档', // 模块名称
        type: 'GET', // 操作类型
        remark: `获取/搜索了【${props.formId}】计划关联文档列表`,
      };
      const url = `document/getList/${props.formId}`;
      new Api('/pms', love)
        .fetch(obj, url, 'POST')
        .then((res) => {
          state.loading = false;
          state.dataSource = res;
          state.tableRef.clearSelectedRowKeys();
        })
        .catch((_) => {
          state.loading = false;
        });
    }
    function searchEmit(data) {
      getPage(data);
    }
    function getPage2() {
      state.loading2 = true;
      const love = {
        id: props.formId,
        className: 'Plan',
        moduleName: '项目管理-计划管理-项目计划-关联内容-关联文档', // 模块名称
        type: 'GET', // 操作类型
        remark: `获取/搜索了【${props.formId}】零组件关联文档`,
      };
      const url = `plan/relation/component/document/${props.formId}`;
      new Api('/pms', love)
        .fetch('', url, 'GET')
        .then((res) => {
          state.loading2 = false;
          state.dataSource2 = res.map((item) => {
            item.children = [];
            return item;
          });
        })
        .catch((_) => {
          state.loading2 = false;
        });
    }
    function handleSaveFile(successAll, cb) {
      const files = successAll.map((item) => {
        const { classification, securityLimit } = item;
        const {
          filePath, filePostfix, fileSize, name,
        } = item.result;
        return {
          projectId: props.projectId,
          dataId: props.formId,
          fileTool: item.openTool,
          filePath,
          filePostfix,
          fileSize,
          name,
          secretLevel: classification,
          securityLimit,
        };
      });
      const love = {
        className: 'Plan',
        moduleName: '项目管理-计划管理-项目计划-关联内容-关联文档', // 模块名称
        type: 'UPLOAD', // 操作类型
        remark: `上传了【${files.map((item) => item?.name)}】`,
      };
      const api = new Api('/pms', love).fetch(files, 'document/saveBatch', 'POST');
      cb(api).then(() => {
        getPage();
      });
    }
    function submitEdit() {
      getPage();
    }

    function expandRow(expanded, record) {
      if (expanded) {
        state.loading2 = true;
        const url = `plan/component/document/file/${record.id}`;
        new Api('/pms')
          .fetch('', url, 'GET')
          .then((res) => {
            state.loading2 = false;
            record.children = res;
          })
          .catch((_) => {
            state.loading2 = false;
          });
      } else {
        record.children = [];
      }
    }
    onMounted(() => {
      getPage();
      getPage2();
    });
    return {
      ...toRefs(state),
      ...toRefs(state6),
      clickType,
      handleSaveFile,
      submitEdit,
      expandRow,
      isPower,
      searchRegister,
      searchEmit,
    };
  },
};
</script>

<style scoped lang="less">
  .ui-2-0 {
    width: calc(100% - 60px);
    flex: 1;
    height: calc(100vh - 260px);
    overflow: auto;
  }
</style>
