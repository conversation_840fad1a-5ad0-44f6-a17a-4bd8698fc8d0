package com.chinasie.orion.management.controller;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.dto.ContractChangeDTO;
import com.chinasie.orion.management.domain.dto.NcfFormpurchaseRequestDTO;
import com.chinasie.orion.management.domain.vo.ContractChangeVO;
import com.chinasie.orion.management.service.ContractChangeService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * ContractChange 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@RestController
@RequestMapping("/contractChange")
@Api(tags = "合同变更信息表")
public class ContractChangeController {

    @Autowired
    private ContractChangeService contractChangeService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据",type = "合同变更信息表", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ContractChangeVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        ContractChangeVO rsp = contractChangeService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param contractChangeDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#contractChangeDTO.name}}】",type = "合同变更信息表", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody ContractChangeDTO contractChangeDTO) throws Exception {
        String rsp = contractChangeService.create(contractChangeDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param contractChangeDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#contractChangeDTO.name}}】",type = "合同变更信息表", subType = "编辑", bizNo = "{{#contractChangeDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody ContractChangeDTO contractChangeDTO) throws Exception {
        Boolean rsp = contractChangeService.edit(contractChangeDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据",type = "合同变更信息表", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = contractChangeService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据",type = "合同变更信息表", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = contractChangeService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据",type = "合同变更信息表", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ContractChangeVO>> pages(@RequestBody Page<ContractChangeDTO> pageRequest) throws Exception {
        Page<ContractChangeVO> rsp = contractChangeService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 根据合同编号查询合同变更信息
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "根据合同编号查询合同变更信息")
    @LogRecord(success = "【{USER{#logUserId}}】根据合同编号查询合同变更信息",type = "合同变更信息表", subType = "根据合同编号查询合同变更信息", bizNo = "")
    @RequestMapping(value = "/getByCode", method = RequestMethod.POST)
    public ResponseDTO<Page<ContractChangeVO>> getByCode(@RequestBody Page<ContractChangeDTO> pageRequest) throws Exception {
        Page<ContractChangeVO> rsp = contractChangeService.getByCode(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("合同变更信息表导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板",type = "合同变更信息表", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        contractChangeService.downloadExcelTpl(response);
    }

    @ApiOperation("合同变更信息表导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入",type = "合同变更信息表", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = contractChangeService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("合同变更信息表导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入",type = "合同变更信息表", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = contractChangeService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消合同变更信息表导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入",type = "合同变更信息表", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = contractChangeService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("合同变更信息表导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据",type = "合同变更信息表", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody Page<NcfFormpurchaseRequestDTO> pageRequest, HttpServletResponse response) throws Exception {
        contractChangeService.exportByExcel(pageRequest, response);
    }
}
