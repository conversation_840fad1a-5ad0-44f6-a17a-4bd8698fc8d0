<script setup lang="ts">
import { BasicCard } from 'lyra-component-vue3';
import { ref, Ref } from 'vue';
import { get, map } from 'lodash-es';
import ContractBasicInfo
  from '../common/ContractBasicInfo.vue';
import { useContractPlanDetail } from '../../hooks/useContractPlanDetail';
import ContractEmploymentPlan
  from '../common/ContractEmploymentPlan.vue';
import Api from '/@/api';

const props = defineProps({
  // eslint-disable-next-line vue/require-default-prop,vue/require-prop-types
  record: {},
});
const tableRef = ref();
const { basicContractEmployerPlan } = useContractPlanDetail(get(props, 'record.contractNumber'));

defineExpose({
  onSubmit() {
    const contractEmploymentPlanData = tableRef.value?.exportTableData();
    const bodyParams = map(contractEmploymentPlanData, (item) => ({
      id: item.id,
      status: item.status,
      year: new Date(`${get(basicContractEmployerPlan, 'year')}-01-01`),
      num: item.num,
      contractNumber: item.contractNumber,
      centerName: get(props, 'record.centerName'),
    }));
    return new Promise((resolve, reject) => {
      new Api('/spm/contractCenterPlan/edit').fetch(bodyParams, '', 'PUT').then(() => {
        resolve('');
      }).catch((err) => {
        reject(err);
      });
    });
  },
});
</script>

<template>
  <ContractBasicInfo />
  <BasicCard
    v-if="basicContractEmployerPlan?.contractNumber"
    title="人工成本信息"
    :isBorder="false"
  >
    <ContractEmploymentPlan
      ref="tableRef"
      :show-summary-row="true"
      :is-editable-num="true"
    />
  </BasicCard>
</template>

<style scoped lang="less">
.contract-employment-plan{
  overflow: auto;
  min-height: 120px;
  max-height: 500px;
  :deep(.ant-basic-table){
    &.default-spacing{
      padding: 0;
    }
  }
}
</style>