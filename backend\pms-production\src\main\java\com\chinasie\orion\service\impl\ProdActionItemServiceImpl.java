package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.bo.DataStatusNBO;
import com.chinasie.orion.constant.ActionItemBusStatusEnum;
import com.chinasie.orion.constant.MessageNodeDict;
import com.chinasie.orion.constant.ProdActionItemEnum;
import com.chinasie.orion.domain.dto.prodAction.ProdActionFeedbackDTO;
import com.chinasie.orion.domain.dto.prodAction.ProdActionTreeParamDTO;
import com.chinasie.orion.domain.entity.MajorRepairPlan;
import com.chinasie.orion.domain.entity.ProdActionItem;
import com.chinasie.orion.domain.dto.ProdActionItemDTO;
import com.chinasie.orion.domain.entity.ProdActionRichText;
import com.chinasie.orion.domain.vo.ProdActionItemVO;
import com.chinasie.orion.domain.vo.prodAction.ActionItemCountVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.MyExceptionCode;
import com.chinasie.orion.feign.PDWorkflowFeignService;
import com.chinasie.orion.feign.dto.FlowBusinessDTO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.entity.OrionFile;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.file.api.service.FileApiService;
import com.chinasie.orion.msc.api.MscBuildHandlerManager;
import com.chinasie.orion.mybatis.domain.entity.LyraEntity;
import com.chinasie.orion.repository.MajorRepairPlanMapper;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.service.MajorRepairPlanMemberService;
import com.chinasie.orion.service.ProdActionItemService;
import com.chinasie.orion.repository.ProdActionItemMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProdActionRichTextService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.spring.SpringApplicationUtils;
import com.mzt.logapi.context.LogRecordContext;
import feign.RetryableException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;

import org.springframework.util.StringUtils;

import java.lang.String;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;

import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;

import static com.chinasie.orion.constant.DictConts.DIMENSION_DICT;


/**
 * <p>
 * ProdActionItem 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 10:28:59
 */
@Service
@Slf4j
public class ProdActionItemServiceImpl extends OrionBaseServiceImpl<ProdActionItemMapper, ProdActionItem> implements ProdActionItemService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private ProdActionRichTextService prodActionRichTextService;


    @Autowired
    private DictRedisHelper dictRedisHelper;
    @Autowired
    private MscBuildHandlerManager mscBuildHandlerManager;

    @Autowired
    private PDWorkflowFeignService workflowFeignService;

    @Autowired
    private MajorRepairPlanMemberService majorRepairPlanMemberService;

    @Autowired
    private UserRedisHelper userRedisHelper;
    @Autowired
    private MajorRepairPlanMapper majorRepairPlanMapper;

    @Autowired
    private FileApiService fileApiService;

    @Autowired
    private DataStatusNBO dataStatusNBO;

//    private ThreadPoolExecutor threadPoolExecutor =null;




    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProdActionItemVO detail(String id, String pageCode) throws Exception {
        ProdActionItem prodActionItem = this.getById(id);
        if (Objects.isNull(prodActionItem)) {
            throw new BaseException(MyExceptionCode.ERROR_NOT_FOUNT_JOB.getErrorCode(), "数据不存在，请刷新后重试");
        }
        // 顶级层级-查看所有
        String parentId = prodActionItem.getParentId();
        List<ProdActionItem> allList = new ArrayList<>();
        allList.add(prodActionItem);
        if (Objects.equals(parentId, "0")) {
            LambdaQueryWrapperX<ProdActionItem> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(ProdActionItem.class);
            lambdaQueryWrapperX.
                    // 获取当前数据下 所有子集
                            eq(ProdActionItem::getParentId, id);
            //所有 子集 行动项
            List<ProdActionItem> prodActionItemList = this.list(lambdaQueryWrapperX);


            if (!CollectionUtils.isEmpty(prodActionItemList)) {
                allList.addAll(prodActionItemList);
            }
            // 获取所有子集ID
            List<String> idList = allList.stream().map(LyraEntity::getId).distinct().collect(Collectors.toList());
            // 新增父级ID
            return this.packageVO(idList, allList, id);

        } else {
            ProdActionItem parentItem = this.getById(parentId);
            if(Objects.isNull(parentItem)){
                throw new BaseException(MyExceptionCode.ERROR_NOT_FOUNT_JOB.getErrorCode(), "父级数据不存在，请刷新后重试");
            }
            allList.add(parentItem);
            List<String> idList = new ArrayList<>();
            idList.add(id);
            return this.packageVO(idList, allList, parentId);
        }
    }

    public ProdActionItemVO packageVO(List<String> childIdList, List<ProdActionItem> prodActionItemList, String parentId) throws Exception {
        childIdList.add(parentId);
        Map<String, List<ProdActionRichText>> idToList = prodActionRichTextService.getRichMapList(childIdList);
        Map<String, ProdActionItemVO> prodActionItemVOMap = new HashMap<>();
        for (ProdActionItem actionItem : prodActionItemList) {
            ProdActionItemVO prodActionItemVO = new ProdActionItemVO();
            BeanCopyUtils.copyProperties(actionItem, prodActionItemVO);
            List<ProdActionRichText> richTexts = idToList.get(actionItem.getId());
            if (CollectionUtils.isEmpty(richTexts)) {
                prodActionItemVOMap.put(actionItem.getId(), prodActionItemVO);
                continue;
            }
            Map<String, String> dataTypeToRichText = richTexts.stream().collect(Collectors.toMap(ProdActionRichText::getDataType, ProdActionRichText::getRichText,(k1,k2)->k1));
            prodActionItemVO.setUpdateSolutions(dataTypeToRichText.getOrDefault(ProdActionItemEnum.UPDATE_SOLUTIONS.getType(), ""));
            prodActionItemVO.setFeedback(dataTypeToRichText.getOrDefault(ProdActionItemEnum.FEEDBACK_INFORMATION.getType(), ""));
            prodActionItemVOMap.put(actionItem.getId(), prodActionItemVO);
        }
        ProdActionItemVO prodActionItemVO1 = prodActionItemVOMap.get(parentId);
        List<ProdActionItemVO> prodActionItemVOList = new ArrayList<>();
        childIdList = childIdList.stream().distinct().collect(Collectors.toList());
        childIdList.remove(parentId);
        String userId= CurrentUserHelper.getCurrentUserId();
        for (String s : childIdList) {
            ProdActionItemVO prodActionItemVO2 = prodActionItemVOMap.get(s);
            if (Objects.nonNull(prodActionItemVO2)) {
                prodActionItemVO2.setEditFeedback(Objects.equals(userId,prodActionItemVO2.getRspUserIds())
                        && Objects.equals(prodActionItemVO2.getStatus(),ActionItemBusStatusEnum.WAIT_FEEDBACK.getStatus()));
                prodActionItemVOList.add(prodActionItemVO2);
            }
        }
        List<DictValueVO> dictValueVOList =  dictRedisHelper.getDictListByCode(DIMENSION_DICT);
        Map<String,String> numberToDesc= dictValueVOList.stream().collect(Collectors.toMap(DictValueVO::getNumber,DictValueVO::getDescription,(k1,k2)->k1));
        prodActionItemVO1.setDimensionDictName(numberToDesc.getOrDefault(prodActionItemVO1.getDimensionDict(),""));

        boolean isUpdate= majorRepairPlanMemberService.isMajorRole(prodActionItemVO1.getRepairRound())?Objects.equals(prodActionItemVO1.getStatus()
                ,ActionItemBusStatusEnum.WAIT_PUSH.getStatus()):Boolean.FALSE;
        prodActionItemVO1.setEditUpdateFlow(isUpdate);
        prodActionItemVO1.setChildList(prodActionItemVOList);
        prodActionItemVO1.setFileList(fileApiService.getFilesByDataId(parentId));
        return prodActionItemVO1;
    }


    /**
     * 新增
     * <p>
     * * @param prodActionItemDTO
     */
    @Override
    public String create(ProdActionItemDTO prodActionItemDTO) throws Exception {
        ProdActionItem prodActionItem = BeanCopyUtils.convertTo(prodActionItemDTO, ProdActionItem::new);
        //获取改进措施
        String richText = prodActionItemDTO.getUpdateSolutions();
        prodActionItem.setParentId("0");
        this.save(prodActionItem);
        if (StringUtils.hasText(richText)) {
            // 新增或更新 当前行动项的 改进措施富文本信息
            prodActionRichTextService.saveOrUpdateEntity(prodActionItem.getId(), richText, ProdActionItemEnum.UPDATE_SOLUTIONS.getType());
        }
        // 附件上传
        List<FileDTO> fileList = prodActionItemDTO.getFileList();
        if (CollUtil.isNotEmpty(fileList)) {
            fileList.forEach(x -> x.setDataId(prodActionItem.getId()));
            fileApiService.batchSaveFile(fileList);
        }
        return prodActionItem.getId();
    }

    /**
     * 编辑
     * <p>
     * * @param prodActionItemDTO
     */
    @Override
    public Boolean edit(ProdActionItemDTO prodActionItemDTO) throws Exception {
        ProdActionItem prodActionItem = BeanCopyUtils.convertTo(prodActionItemDTO, ProdActionItem::new);
        String richText = prodActionItemDTO.getUpdateSolutions();

        String id = prodActionItemDTO.getId();
        ProdActionItem item =this.getById(id);
        if(Objects.isNull(item)){
            throw new BaseException(MyExceptionCode.ERROR_NOT_FOUNT_JOB.getErrorCode(), "数据不存在，请刷新后重试");
        }

        if(!Objects.equals(item.getParentId(),"0")){
            throw new BaseException(MyExceptionCode.ERROR_NOT_FOUNT_JOB.getErrorCode(), "只有顶层数据才能编辑");
        }
        if(!Objects.equals(item.getStatus(),ActionItemBusStatusEnum.WAIT_PUSH.getStatus())){
            throw new BaseException(MyExceptionCode.ERROR_NOT_FOUNT_JOB.getErrorCode(), "只有待发布的数据才能编辑");
        }
        prodActionItem.setParentId("0");
        if (StringUtils.hasText(richText)) {
            // 新增或更新 当前行动项的 改进措施富文本信息
            prodActionRichTextService.saveOrUpdateEntity(prodActionItem.getId(), richText, ProdActionItemEnum.UPDATE_SOLUTIONS.getType());
        }
        this.updateById(prodActionItem);
        String rsp = prodActionItem.getId();

        //编辑附件
        List<FileDTO> fileList = prodActionItemDTO.getFileList();
        List<FileVO> existFileList = fileApiService.getFilesByDataId(id);
        // 优先移除
        if (Objects.nonNull(existFileList)) {
            // existFileList 中不包含 fileDTOList的删除
            List<String> filesIds = existFileList.stream().map(FileVO::getId).filter(x -> !fileList.stream()
                            .map(FileDTO::getId).filter(Objects::nonNull).collect(Collectors.toList()).contains(x))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(filesIds)) {
                fileApiService.removeBatchByIds(filesIds);
            }
        }
        // 批量新增
        if (!CollectionUtils.isEmpty(fileList)) {
            fileList.forEach(x -> {
                x.setDataId(id);
                x.setDataType("MarketContract");
            });
            fileApiService.batchSaveFile(fileList);
        }
        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        long count = this.count(new LambdaQueryWrapper<>(ProdActionItem.class).in(LyraEntity::getId, ids).select(LyraEntity::getId)
                .in(LyraEntity::getStatus,ActionItemBusStatusEnum.getNotEditAndNotDel()));
        if (count > 0) {
            throw new RuntimeException("存在已经处于正在走流程/或者已经结束的数据，不能删除");
        }
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProdActionItemVO> pages(Page<ProdActionItemDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProdActionItem> condition = new LambdaQueryWrapperX<>(ProdActionItem.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ProdActionItem::getCreateTime);


        Page<ProdActionItem> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProdActionItem::new));

        PageResult<ProdActionItem> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProdActionItemVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProdActionItemVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProdActionItemVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public void setEveryName(List<ProdActionItemVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }

    @Override
    public Boolean updateFeedback(ProdActionFeedbackDTO prodActionFeedbackDTO) {
        String id = prodActionFeedbackDTO.getId();
        ProdActionItem actionItem = this.getById(id);
        if (Objects.isNull(actionItem)) {
            throw new RuntimeException("子行动项数据不存在，请刷新后重试");
        }
        prodActionRichTextService.saveOrUpdateEntity(actionItem.getId(), prodActionFeedbackDTO.getFeedbackInfo(),
                ProdActionItemEnum.FEEDBACK_INFORMATION.getType());
        LogRecordContext.putVariable("actionDesc", actionItem.getProblemDesc());
        return Boolean.TRUE;
    }

    @Override
    public List<ProdActionItemVO> treeList(ProdActionTreeParamDTO paramDto) throws Exception {
        String repairRound = paramDto.getRepairRound();
        String keyword= paramDto.getKeyword();
        List<ProdActionItem> prodActionItemList = this.list(new LambdaQueryWrapperX<>(ProdActionItem.class)
                .eqIfPresent(ProdActionItem::getRepairRound, repairRound)
                .orderByDesc(ProdActionItem::getCreateTime));

        List<ProdActionItemVO> actionItemVOList = new ArrayList<>();
        // 过滤基本信息
        List<DictValueVO> dictValueVOList =  dictRedisHelper.getDictListByCode(DIMENSION_DICT);


        List<DataStatusVO> dataStatusVOList = dataStatusNBO.getDataStatusListByClassName(ProdActionItem.class.getSimpleName());
        if (CollectionUtils.isEmpty(dataStatusVOList)){
            dataStatusVOList = new ArrayList<>();
        }
        Map<Integer, DataStatusVO> statusToVo = dataStatusVOList.stream().collect(Collectors.toMap(DataStatusVO::getStatusValue, x -> x));

        for (ProdActionItem prodActionItem : prodActionItemList) {
            prodActionItem.setDataStatus(statusToVo.getOrDefault(prodActionItem.getStatus(),new DataStatusVO()));
        }

        if(StringUtils.hasText(keyword) && !CollectionUtils.isEmpty(prodActionItemList)){
            List<String> dictNumberList = dictValueVOList.stream().filter(item-> item.getDescription().contains(keyword))
                    .map(DictValueVO::getNumber).collect(Collectors.toList());
            List<ProdActionItem> searchList =prodActionItemList.stream().filter(item-> item.getProblemDesc().contains(keyword)
                    || item.getRspUserNames().contains(keyword)
                    || item.getVerifierName().contains(keyword)
                    || dictNumberList.contains(item.getDimensionDict())).collect(Collectors.toList() );
            if(!CollectionUtils.isEmpty(searchList)){
                actionItemVOList = this.packgeList(searchList);
            }
        }else{
            actionItemVOList = this.packgeList(prodActionItemList);
        }
        Map<String,String> numberToDesc= dictValueVOList.stream().collect(Collectors.toMap(DictValueVO::getNumber,DictValueVO::getDescription,(k1,k2)->k1));
        actionItemVOList.forEach(item->{
            item.setDimensionDictName(numberToDesc.getOrDefault(item.getDimensionDict(),""));
        });
        LogRecordContext.putVariable("repairRound", repairRound);
        return actionItemVOList;
    }

    @Override
    public List<ProdActionItemVO> allList() {
        List<ProdActionItem> prodActionItemList = this.list(new LambdaQueryWrapperX<>(ProdActionItem.class)
                        .eq(ProdActionItem::getParentId, "0")
                .orderByDesc(ProdActionItem::getRepairRound).orderByDesc(ProdActionItem::getCreateTime));
        if(CollectionUtils.isEmpty(prodActionItemList)){
            return  new ArrayList<>();
        }
        List<ProdActionItemVO> actionItemVOList = this.packgeList(prodActionItemList);
       actionItemVOList.forEach(item->{
            if(!Objects.equals(item.getStatus(),ActionItemBusStatusEnum.CLOSE.getStatus())){
                item.setStatusName("在办");
            }else{
                item.setStatusName("已关闭");
            }
        });
        List<String> repairRoundList= prodActionItemList.stream().map(ProdActionItem::getRepairRound).distinct().collect(Collectors.toList());
        LambdaQueryWrapperX<MajorRepairPlan> queryWrapperX = new LambdaQueryWrapperX<>(MajorRepairPlan.class);
        queryWrapperX.in(MajorRepairPlan::getRepairRound, repairRoundList);
        queryWrapperX.select(MajorRepairPlan::getRepairRound,MajorRepairPlan::getId);
        List<MajorRepairPlan>  majorRepairPlans = majorRepairPlanMapper.selectList(queryWrapperX);
        //
        Map<String, String> repairRountToIdMap =new HashMap<>();
        for (MajorRepairPlan majorRepairPlan : majorRepairPlans) {
            repairRountToIdMap.put(majorRepairPlan.getRepairRound(),majorRepairPlan.getId());
        }
        actionItemVOList.forEach(item->{
            item.setRepairId(repairRountToIdMap.get(item.getRepairRound()));
        });
        return actionItemVOList;
    }

    @Override
    public Boolean startWorkflow(FlowBusinessDTO flowBusinessDTO,String repairId) throws Exception {
        // 通过ID 获取行动项信息
        String id= flowBusinessDTO.getBusinessId();

        ProdActionItem actionItem=  this.getById(id);
        if(Objects.isNull(actionItem)){
            throw new BaseException(MyExceptionCode.ERROR_NOT_FOUNT_JOB.getErrorCode(), "数据不存在，请刷新后重试");
        }
        if(actionItem.getStatus() != ActionItemBusStatusEnum.WAIT_PUSH.getStatus()){
            throw new BaseException(MyExceptionCode.ERROR_NOT_FOUNT_JOB.getErrorCode(), "只有待发布的数据才能启动流程");
        }
        String repairRound = actionItem.getRepairRound();
        // 更新状态-父级
        actionItem.setStatus(ActionItemBusStatusEnum.RUN_EXECUTE.getStatus());
        // 复制父级数据将数据根据 责任人拆分
        String rspUserIds= actionItem.getRspUserIds();
        List<String> rspUserIdList = Arrays.asList(rspUserIds.split(","));
        String rspUserNames= actionItem.getRspUserNames();
        List<String> rspUserNameList = Arrays.asList(rspUserNames.split(","));
        String rspDeptIds= actionItem.getRspDeptIds();
        List<String> rspDeptIdList = Arrays.asList(rspDeptIds.split(","));
        String rspDeptNames= actionItem.getRspDeptNames();
        List<String> rspDeptNameList = Arrays.asList(rspDeptNames.split(","));
        // 获取父级对应的 措施富文本
//        ProdActionRichText richText=  prodActionRichTextService.getByActionId(id, ProdActionItemEnum.UPDATE_SOLUTIONS.getType());
        List<ProdActionItem> childList = new ArrayList<>();

        Map<String,SimpleUser> idToSimpleUserMap= userRedisHelper.getSimpleUserMapByUserIds(rspUserIdList);
        for (int i = 0; i < rspUserIdList.size(); i++) {
            ProdActionItem prodActionItem = BeanCopyUtils.convertTo(actionItem,ProdActionItem::new);
            prodActionItem.setRspUserIds(rspUserIdList.get(i));

            SimpleUser simpleUser=idToSimpleUserMap.get(rspUserIdList.get(i));
            if(Objects.nonNull(simpleUser)){
                prodActionItem.setRspDeptNames(simpleUser.getOrgName());
                prodActionItem.setRspDeptIds(simpleUser.getOrgId());
            }
            prodActionItem.setId(null);
            prodActionItem.setParentId(id);
            prodActionItem.setRspUserNames(rspUserNameList.get(i));
            prodActionItem.setStatus(ActionItemBusStatusEnum.WAIT_FEEDBACK.getStatus());
            childList.add(prodActionItem);
        }
        // 插入数据
        childList.add(actionItem);
        this.saveOrUpdateBatch(childList);
        childList.remove(actionItem);
        // 组装流程启动相关的实体对象  然后 插入流程
        String  buseinessName =String.format("【%s大修行动项】",repairRound);
        String  messageUrl = flowBusinessDTO.getMessageUrl();
        List<FlowBusinessDTO> flowBusinessDTOList =new ArrayList<>();
        for (ProdActionItem prodActionItem : childList) {
            FlowBusinessDTO childDto = new FlowBusinessDTO();
            childDto.setBusinessId(prodActionItem.getId());
            childDto.setDataTypeCode(this.getEntityClass().getSimpleName());
            childDto.setBusinessName(buseinessName);
            childDto.setMessageUrl(String.format("%s%s?repairId=%s&query=%s",messageUrl,prodActionItem.getId(),repairId,System.currentTimeMillis()));
            childDto.setCreatorId(prodActionItem.getCreatorId());
            childDto.setPlatformId(prodActionItem.getPlatformId());
            childDto.setFormData(JSON.parseObject(JSON.toJSONString(prodActionItem)));
            childDto.setOrgId(prodActionItem.getOrgId());
            flowBusinessDTOList.add(childDto);
        }
        // 调用并启动流程  后续考虑加为 子线程
        log.info("调用多数据启动流程参数：【{}】 ; 对象：【{}】",JSONUtil.toJsonStr(flowBusinessDTOList),this.getEntityClass().getSimpleName());
//        workflowFeignService.createList(flowBusinessDTOList,this.entityClass.getSimpleName());

//        CompletableFuture.runAsync(() -> {
            // 调用第三方接口的代码
        List<FlowBusinessDTO> flowBusinessDTOList1 =new ArrayList<>();
        FlowBusinessDTO flowBusinessDTO1= flowBusinessDTOList.get(0);
        try {
            flowBusinessDTOList1.add(flowBusinessDTO1);
            flowBusinessDTO1.setDataTypeCode(this.getEntityClass().getSimpleName());
            log.error("开始同步调用流程启动信息");
            ResponseDTO<Boolean> responseDTO= workflowFeignService.oneStart(flowBusinessDTO1);
            if(null != responseDTO && !Objects.equals(responseDTO.getCode(),200)){
                throw new BaseException(MyExceptionCode.ERROR_NOT_FOUNT_JOB.getErrorCode(), responseDTO.getMessage());
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("同步调用流程启动信息：【{}】 ",e.getMessage());
            if(e instanceof  BaseException){
                throw new BaseException(MyExceptionCode.ERROR_NOT_FOUNT_JOB.getErrorCode(), "同步调用流程启动信息异常："+((BaseException) e).getErrorDesc());
            }else  if(e.getCause() instanceof  java.net.SocketTimeoutException  && e instanceof RetryableException){
//                throw new BaseException(MyExceptionCode.ERROR_NOT_FOUNT_JOB.getErrorCode(), "同步调用流程启动信息异常："+e.getMessage());
            }
            log.error("同步调用流程启动信息：【{}】 ",e.getMessage());
//            throw new BaseException(MyExceptionCode.ERROR_NOT_FOUNT_JOB.getErrorCode(), "同步调用流程启动信息异常："+e.getMessage());
        }
        if(flowBusinessDTOList.size() >1){
            flowBusinessDTOList.remove(0);
            ThreadPoolTaskExecutor threadPoolExecutor = (ThreadPoolTaskExecutor) SpringApplicationUtils.getBean("projectPool");
            log.error("调用流程的参数信息:{}", JSONObject.toJSONString(flowBusinessDTOList));
            for (FlowBusinessDTO businessDTO : flowBusinessDTOList) {
                threadPoolExecutor.submit(() -> {
                    try {
                        List<FlowBusinessDTO> flowBusinessDTOList2 =new ArrayList<>();
                        flowBusinessDTOList2.add(businessDTO);
                        flowBusinessDTO1.setDataTypeCode(this.getEntityClass().getSimpleName());
                        log.error("开始异步调用流程启动信息");
                        ResponseDTO<Boolean> responseDTO=  workflowFeignService.oneStart(businessDTO);
                        if(null != responseDTO && !Objects.equals(responseDTO.getCode(),200)){
                            throw new BaseException(MyExceptionCode.ERROR_NOT_FOUNT_JOB.getErrorCode(), responseDTO.getMessage());
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        log.error("异步调用流程启动信息：【{}】 ",e);
                        if(e instanceof  BaseException){
                            throw new BaseException(MyExceptionCode.ERROR_NOT_FOUNT_JOB.getErrorCode(), "同步调用流程启动信息异常："+((BaseException) e).getErrorDesc());
                        }else  if(e.getCause() instanceof  java.net.SocketTimeoutException  && e instanceof RetryableException){
                        }else{
                            throw new BaseException(MyExceptionCode.ERROR_NOT_FOUNT_JOB.getErrorCode(), "异步调用流程启动信息："+e.getMessage());
                        }
                    }
                });
            }

        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean superviseById(String id,String repairId) {
        ProdActionItem actionItem=  this.getById(id);
        if(Objects.isNull(actionItem)){
            throw new BaseException(MyExceptionCode.ERROR_NOT_FOUNT_JOB.getErrorCode(), "数据不存在，请刷新后重试");
        }
        if(Objects.equals(actionItem.getParentId(),"0") && !actionItem.getStatus().equals(ActionItemBusStatusEnum.RUN_EXECUTE.getStatus())){
            throw new BaseException(MyExceptionCode.ERROR_NOT_FOUNT_JOB.getErrorCode(), "顶层数据状态为执行中，才能进行督办");
        }
        List<ProdActionItem> all = new ArrayList<>();
        if(Objects.equals(actionItem.getParentId(),"0")){
            LambdaQueryWrapperX<ProdActionItem> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(ProdActionItem.class);
            lambdaQueryWrapperX.eq(ProdActionItem::getParentId, id);  // 获取当前数据下 所有子集
            //所有 子集 行动项
            lambdaQueryWrapperX.eq(ProdActionItem::getStatus, ActionItemBusStatusEnum.WAIT_FEEDBACK.getStatus());
            List<ProdActionItem> prodActionItemList = this.list(lambdaQueryWrapperX);
            if(!CollectionUtils.isEmpty(prodActionItemList)){
                all.addAll(prodActionItemList);
            }
        }else{
            all.add(actionItem);
        }
        if(CollectionUtils.isEmpty(all)){
            return Boolean.TRUE;
        }
        // 获取到 所有状态为 待反馈的
        List<ProdActionItem>  filterList = all.stream().filter(ite-> !ite.getStatus()
                .equals(ActionItemBusStatusEnum.WAIT_PUSH.getStatus())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(filterList)){
            return Boolean.TRUE;
        }
//        List<String>  rspUserIdList= filterList.stream().map(ProdActionItem::getRspUserIds).collect(Collectors.toList());
        for (ProdActionItem prodActionItem : filterList) {
            mscBuildHandlerManager.send(prodActionItem, MessageNodeDict.NODE_ACTION_ITEM_SUPERVISE, Collections.singletonList(prodActionItem.getRspUserIds()),repairId);
        }
        LogRecordContext.putVariable("name", actionItem.getRepairRound());
        LogRecordContext.putVariable("userNames", filterList.stream().map(ProdActionItem::getRspUserNames).collect(Collectors.joining(",")));
        return Boolean.TRUE;
    }


    @Override
    public void drawingNear(String param) {
        // 获取所有状态为 待验证的数据列表
        LambdaQueryWrapperX<ProdActionItem> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(ProdActionItem.class);
        lambdaQueryWrapperX.eq(ProdActionItem::getStatus, ActionItemBusStatusEnum.WAIT_FEEDBACK.getStatus());
        List<ProdActionItem> actionItemList=  this.list(lambdaQueryWrapperX);
        if(CollectionUtils.isEmpty(actionItemList)){
            return;
        }
        // 当前日期
        LocalDate today = LocalDate.now();
        // 目标日期：当前日期后14天
        if(StrUtil.isEmpty(param)){
            param ="14";
        }
        List<LocalDate> allTargetDate = new ArrayList<>();
        for (String s : param.split(",")) {
            LocalDate targetDate = today.plusDays(Integer.parseInt(s));
            allTargetDate.add(targetDate);
        }
        List<ProdActionItem> filterList = new ArrayList<>();
        log.info("数据进入对比：{}",JSONUtil.toJsonStr(allTargetDate));
        actionItemList.forEach(item->{

            Date finishDeadline =  item.getFinishDeadline();
            LocalDate localDate = finishDeadline.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

            for (LocalDate date : allTargetDate) {
                if(ChronoUnit.DAYS.between(localDate, date) == 0){
                    filterList.add(item);
                }
            }
        });
        if(CollectionUtils.isEmpty(filterList)){
            return;
        }

        // 获取大修伦茨列表
        List<String> repairRoundList= filterList.stream().map(ProdActionItem::getRepairRound).distinct().collect(Collectors.toList());
        // 通过大修伦茨获取大修idMap
        LambdaQueryWrapperX<MajorRepairPlan> queryWrapperX = new LambdaQueryWrapperX<>(MajorRepairPlan.class);
        queryWrapperX.in(MajorRepairPlan::getRepairRound, repairRoundList);
        queryWrapperX.select(MajorRepairPlan::getRepairRound,MajorRepairPlan::getId);
        List<MajorRepairPlan>  majorRepairPlans = majorRepairPlanMapper.selectList(queryWrapperX);
        //
        Map<String, String> repairRountToIdMap =new HashMap<>();
        for (MajorRepairPlan majorRepairPlan : majorRepairPlans) {
            repairRountToIdMap.put(majorRepairPlan.getRepairRound(),majorRepairPlan.getId());
        }
        log.info("临近15天预警开始");
        Map<String,List<ProdActionItem>> repairRoundToRspUsersMap =
                filterList.stream().filter(item-> StringUtils.hasText(item.getRspUserIds())).collect(Collectors.groupingBy(ProdActionItem::getRepairRound));
        for (Map.Entry<String, List<ProdActionItem>> stringSetEntry : repairRoundToRspUsersMap.entrySet()) {
            List<ProdActionItem> itemList = stringSetEntry.getValue();
            String repairRound= stringSetEntry.getKey();
            String id = repairRountToIdMap.get(repairRound);
            mscBuildHandlerManager.send(itemList.get(0), MessageNodeDict.NODE_ACTION_ITEM_REMIND,
                    itemList.stream().map(ProdActionItem::getRspUserIds).distinct().collect(Collectors.toList()),id);
        }
        return;
    }

    @Override
    public ActionItemCountVO actionItemCount(String majorRepairTurn) {
        LambdaQueryWrapperX<ProdActionItem> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(ProdActionItem.class);
        lambdaQueryWrapperX.eq(ProdActionItem::getParentId, "0");
        if(StringUtils.hasText(majorRepairTurn)){
            lambdaQueryWrapperX.eq(ProdActionItem::getRepairRound, majorRepairTurn);
        }
        lambdaQueryWrapperX.select(ProdActionItem::getId,ProdActionItem::getStatus);
        List<ProdActionItem> actionItemList= this.list(lambdaQueryWrapperX);
        if(CollectionUtils.isEmpty(actionItemList)){
            return  new ActionItemCountVO();
        }
        ActionItemCountVO actionItemCountVO = new ActionItemCountVO();
        actionItemCountVO.setTotalCount(actionItemList.size());
        int closedCount0 = 0;
        int unClosedCount0 = 0;
        for (ProdActionItem actionItem : actionItemList) {
            if(actionItem.getStatus().equals(ActionItemBusStatusEnum.CLOSE.getStatus())){
                closedCount0++;
            }else{
                unClosedCount0++;
            }
        }
        actionItemCountVO.setClosedCount(closedCount0);
        actionItemCountVO.setUnClosedCount(unClosedCount0);
        return actionItemCountVO;
    }

    public List<ProdActionItemVO> packgeList(List<ProdActionItem> filterList){
        if(CollectionUtils.isEmpty(filterList)){
            return  new ArrayList<>();
        }
        List<ProdActionItemVO>  parentList  = new ArrayList<>();
        Map<String, List<ProdActionItemVO>> parentKeyToListMap = new HashMap<>();
        for (ProdActionItem actionItem : filterList) {
            ProdActionItemVO prodActionItemVO = new ProdActionItemVO();
            BeanCopyUtils.copyProperties(actionItem,prodActionItemVO);
            if(Objects.equals(actionItem.getParentId(),"0")){
                parentList.add(prodActionItemVO);
            }else{
                List<ProdActionItemVO> childList =  parentKeyToListMap.getOrDefault(actionItem.getParentId(),new ArrayList<>());
                childList.add(prodActionItemVO);
                parentKeyToListMap.put(actionItem.getParentId(),childList);
            }
        }
        List<DictValueVO> dictValueVOList =  dictRedisHelper.getDictListByCode(DIMENSION_DICT);
        Map<String,String> numberToDesc= dictValueVOList.stream().collect(Collectors.toMap(DictValueVO::getNumber,DictValueVO::getDescription,(k1,k2)->k1));
        parentList.forEach(item->{
            item.setDimensionDictName(numberToDesc.getOrDefault(item.getDimensionDict(),""));
            item.setChildList(parentKeyToListMap.getOrDefault(item.getId(),new ArrayList<>()));
        });
        return parentList;

    }

}
