package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.api.code.SysCodeApi;
import com.chinasie.orion.bo.DataStatusNBO;
import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.constant.*;
import com.chinasie.orion.domain.dto.FileInfoDTO;
import com.chinasie.orion.domain.dto.ProjectDTO;
import com.chinasie.orion.domain.dto.ProjectDeclareDTO;
import com.chinasie.orion.domain.dto.RelevancyPlanDTO;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.entity.ProjectApproval;
import com.chinasie.orion.domain.entity.ProjectDeclare;
import com.chinasie.orion.domain.entity.ProjectDeclareFileInfo;
import com.chinasie.orion.domain.vo.DocumentVO;
import com.chinasie.orion.domain.vo.ProjectDeclareVO;
import com.chinasie.orion.domain.vo.ProjectVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.BaseErrorCode;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ProjectDeclareFileInfoRepository;
import com.chinasie.orion.repository.ProjectDeclareRepository;
import com.chinasie.orion.repository.ProjectRepository;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectDeclareFileInfoService;
import com.chinasie.orion.service.ProjectDeclareService;
import com.chinasie.orion.service.ProjectService;
import com.chinasie.orion.service.ProjectToBasePlanService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.CollectionUtils;
import com.chinasie.orion.util.PmsAuthUtil;
import com.chinasie.orion.util.ResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * ProjectDeclare 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18 10:38:38
 */
@Service
public class ProjectDeclareServiceImpl extends OrionBaseServiceImpl<ProjectDeclareRepository, ProjectDeclare> implements ProjectDeclareService {

    @Autowired
    private ProjectDeclareRepository projectDeclareRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private SysCodeApi sysCodeApi;

    @Resource
    private DictBo dictBo;

    @Autowired
    private ProjectDeclareFileInfoService projectDeclareFileInfoService;

    @Autowired
    private ProjectDeclareFileInfoRepository projectDeclareFileInfoRepository;

    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Resource
    private CurrentUserHelper currentUserHelper;

    @Autowired
    private ProjectToBasePlanService newProjectToBasePlanService;

    @Autowired
    private PmsAuthUtil pmsAuthUtil;
    @Autowired
    private DataStatusNBO dataStatusNBO;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProjectDeclareVO detail(String id, String pageCode) throws Exception {
        ProjectDeclare projectDeclare = projectDeclareRepository.selectById(id);
        ProjectDeclareVO result = BeanCopyUtils.convertTo(projectDeclare, ProjectDeclareVO::new);
        if (result != null) {
            packageProjectDeclareDetail(result);
        }

        return result;
    }


    @Override
    public ProjectDeclareVO getByProjectId(String projectId) throws Exception {
        ProjectDeclare projectDeclare = projectDeclareRepository.selectOne(ProjectDeclare::getProjectId, projectId);
        ProjectDeclareVO result = BeanCopyUtils.convertTo(projectDeclare, ProjectDeclareVO::new);
        if (result != null) {
            packageProjectDeclareDetail(result);
        }
        return result;
    }

    private void packageProjectDeclareDetail(ProjectDeclareVO result) throws Exception {
        String projectId = result.getProjectId();
        Project project = projectService.getById(projectId);
        result.setProjectSource(project.getProjectSource());
        result.setResUserId(project.getResPerson());
        result.setRspDeptId(project.getResDept());
        if (StringUtils.hasText(result.getRspDeptId())) {
            DeptVO dept = deptRedisHelper.getDeptById(result.getRspDeptId());
            result.setRspDeptName(null == dept ? "" : dept.getName());
        }
        if (StringUtils.hasText(result.getResUserId())) {
            UserVO rspUser = userRedisHelper.getUserById(result.getResUserId());
            result.setResUserName(null == rspUser ? "" : rspUser.getName());
        }


        if (project != null) {
            result.setProjectName(project.getName());
            result.setProjectStartTime(project.getProjectStartTime());
            result.setProjectEndTime(project.getProjectEndTime());
            result.setProjectType(project.getProjectType());
            result.setProjectSubType(project.getProjectSubType());
        }

        if (StringUtils.hasText(result.getProjectSource())) {
            Map<String, String> projectSourceDict = dictBo.getDictValueToDesMap(DictConstant.Project_Source);
            result.setProjectSourceName(projectSourceDict.get(result.getProjectSource()));
        }
        if (StringUtils.hasText(result.getProjectType())) {
            Map<String, String> projectTypeDict = dictBo.getDictNumberToDesMap(DictConstant.Project_Type);
            result.setProjectTypeName(projectTypeDict.get(result.getProjectType()));
        }
        if (StringUtils.hasText(result.getProjectSubType())) {
            Map<String, String> projectSubTypeDict = dictBo.getDictValueToDesMap(DictConstant.Project_Type_InvestmentType);
            result.setProjectSubTypeName(projectSubTypeDict.get(result.getProjectSubType()));
        }

        List<DocumentVO> materialList = projectDeclareFileInfoService.getDocumentList(ProjectDeclareFileTypeEnum.MATERIAL.getCode(), result.getId(), null);
        result.setMaterialList(materialList);
    }

    /**
     * 新增
     * <p>
     * * @param projectDeclareDTO
     */
    @Override
    @Transactional
    //TODO 6/12 审查 袁坤  事务异常捕获
    public ProjectDeclareVO create(ProjectDeclareDTO projectDeclareDTO) throws Exception {
        String projectId = projectDeclareDTO.getProjectId();
        Project project = null;
        if (StringUtils.hasText(projectId)) {
            project = projectRepository.selectById(projectId);
            if (project == null) {
                throw new BaseException(BaseErrorCode.SYSTEM_ERROR_QUERY_ENTITY_NULL, projectId + "对应项目未找到！");
            }
        } else {
            project = projectRepository.selectOne(Project::getNumber, projectDeclareDTO.getProjectNumber());
        }

        ResponseDTO<String> responseDTO = sysCodeApi.rulesAndSegmentCreate("ProjectDeclare", "number", false, "");
        if (ResponseUtils.fail(responseDTO)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, responseDTO.getMessage());
        }
        String number = responseDTO.getResult();
        if (!StringUtils.hasText(number)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "生成项目申报编号失败");
        }
        String projectNumber = "";
        if (project != null) {
            projectNumber = project.getNumber();
//            if (project.getIsDeclare() != null && !project.getIsDeclare()) {
//                throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST, "当前项目不需要申报！");
//            }
            ProjectDeclare projectDeclare = projectDeclareRepository.selectOne(ProjectDeclare::getProjectId, projectId);
            if (projectDeclare != null) {
                throw new PMSException(PMSErrorCode.KMS_DATA_QUOTE_STATUS, project.getName() + "项目已经发起过项目申报，不能重复发起！");
            }
            projectDeclareDTO.setProjectNumber(project.getNumber());
            projectDeclareDTO.setProjectId(project.getId());

            if (project.getProjectStartTime() != projectDeclareDTO.getProjectStartTime()
                    || project.getProjectEndTime() != projectDeclareDTO.getProjectEndTime()) {
                project.setProjectStartTime(projectDeclareDTO.getProjectStartTime());
                project.setProjectEndTime(projectDeclareDTO.getProjectEndTime());
                project.setResPerson(projectDeclareDTO.getResUserId());
                project.setResAdministrativeOffice(projectDeclareDTO.getRspDeptId());
                String resUser = project.getResPerson();
                if (StringUtils.hasText(resUser)) {
                    SimpleUser resSimpleUser = userRedisHelper.getSimpleUserById(resUser);
                    if (resSimpleUser != null) {
                        if (StringUtils.hasText(projectDeclareDTO.getRspDeptId())) {
                            project.setResAdministrativeOffice(resSimpleUser.getDeptId());
                        }
                        project.setResTeamGroup(resSimpleUser.getClassId());
                        project.setResDept(resSimpleUser.getOrgId());
                    }
                }
                project.setProjectType(projectDeclareDTO.getProjectType());
                project.setProjectSubType(projectDeclareDTO.getProjectSubType());
                project.setProjectSource(projectDeclareDTO.getProjectSource());
                projectRepository.updateById(project);
            }

        } else {
            ProjectDTO projectDTO = new ProjectDTO();
            projectDTO.setNumber(projectDeclareDTO.getProjectNumber());
            projectDTO.setName(projectDeclareDTO.getProjectName());
            projectDTO.setProjectStartTime(projectDeclareDTO.getProjectStartTime());
            projectDTO.setProjectEndTime(projectDeclareDTO.getProjectEndTime());
            projectDTO.setProjectType(projectDeclareDTO.getProjectType());
            projectDTO.setProjectSubType(projectDeclareDTO.getProjectSubType());
            projectDTO.setProjectSource(projectDeclareDTO.getProjectSource());
            projectDTO.setResPerson(projectDeclareDTO.getResUserId());
            String resUser = projectDTO.getResPerson();
            if (StringUtils.hasText(resUser)) {
                SimpleUser resSimpleUser = userRedisHelper.getSimpleUserById(resUser);
                if (resSimpleUser != null) {
                    projectDTO.setResDept(resSimpleUser.getOrgId());
                    if (StringUtils.hasText(projectDeclareDTO.getRspDeptId())) {
                        projectDTO.setResDept(projectDeclareDTO.getRspDeptId());
                    }
                    projectDTO.setResAdministrativeOffice(resSimpleUser.getDeptId());
                    projectDTO.setResTeamGroup(resSimpleUser.getClassId());

                }
            }
            projectDTO.setIsDeclare(true);
            String newProjectId = projectService.saveProject(projectDTO);
            projectNumber = projectDTO.getNumber();
            projectDeclareDTO.setProjectId(newProjectId);
            // 初始化绑定综合计划
            List<RelevancyPlanDTO> basePlanList = projectDeclareDTO.getPlanList();
            newProjectToBasePlanService.packageEntityListAndSave(newProjectId, projectNumber, basePlanList);

        }


        ProjectDeclare projectDeclare = BeanCopyUtils.convertTo(projectDeclareDTO, ProjectDeclare::new);
        projectDeclare.setStatus(ProjectDeclareStatusEnum.CREATED.getStatus());
        this.save(projectDeclare);

        List<FileInfoDTO> fileInfoDTOList = projectDeclareDTO.getFileInfoDTOList();
        List<String> fileDataIds = new ArrayList<>();
        if (!CollectionUtils.isBlank(fileInfoDTOList)) {
            fileInfoDTOList.forEach(p -> p.setDataId(projectDeclare.getId()));
            fileDataIds = projectDeclareFileInfoService.saveBatchAdd(ProjectDeclareFileTypeEnum.MATERIAL.getCode(), fileInfoDTOList);
        }


        ProjectDeclareVO rsp = BeanCopyUtils.convertTo(projectDeclare, ProjectDeclareVO::new);
        rsp.setFileDataIds(fileDataIds);
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param projectDeclareDTO
     */
    @Override
    @Transactional
    //TODO 6/12 审查 袁坤  事务异常捕获
    public ProjectDeclareVO edit(ProjectDeclareDTO projectDeclareDTO) throws Exception {
        String projectId = projectDeclareDTO.getProjectId();
        Project project = null;
        if (StringUtils.hasText(projectId)) {
            project = projectRepository.selectById(projectId);
            if (project == null) {
                throw new BaseException(BaseErrorCode.SYSTEM_ERROR_QUERY_ENTITY_NULL, projectId + "对应项目未找到！");
            }
        } else {
            project = projectRepository.selectOne(Project::getNumber, projectDeclareDTO.getProjectNumber());
        }
        String projectNumber = "";
        if (project != null) {
            projectNumber = project.getNumber();
//            if (!project.getIsDeclare()) {
//                throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST, "当前项目不需要申报！");
//            }
            projectDeclareDTO.setProjectNumber(project.getNumber());
            if (project.getProjectStartTime() != projectDeclareDTO.getProjectStartTime()
                    || project.getProjectEndTime() != projectDeclareDTO.getProjectEndTime()) {
                project.setProjectStartTime(projectDeclareDTO.getProjectStartTime());
                project.setProjectEndTime(projectDeclareDTO.getProjectEndTime());
                project.setResPerson(projectDeclareDTO.getResUserId());
                project.setResAdministrativeOffice(projectDeclareDTO.getRspDeptId());
                String resUser = project.getResPerson();
                if (StringUtils.hasText(resUser)) {
                    SimpleUser resSimpleUser = userRedisHelper.getSimpleUserById(resUser);
                    if (resSimpleUser != null) {
                        project.setResDept(resSimpleUser.getOrgId());
                        if (StringUtils.hasText(projectDeclareDTO.getRspDeptId())) {
                            project.setResDept(projectDeclareDTO.getRspDeptId());
                        }
                        project.setResAdministrativeOffice(resSimpleUser.getDeptId());
                        project.setResTeamGroup(resSimpleUser.getClassId());

                    }
                }
                project.setProjectType(projectDeclareDTO.getProjectType());
                project.setProjectSubType(projectDeclareDTO.getProjectSubType());
                project.setProjectSource(projectDeclareDTO.getProjectSource());
                projectRepository.updateById(project);
            }
        } else {
            projectNumber = projectDeclareDTO.getProjectNumber();
            ProjectDTO projectDTO = new ProjectDTO();
            projectDTO.setNumber(projectDeclareDTO.getProjectNumber());
            projectDTO.setName(projectDeclareDTO.getProjectName());
            projectDTO.setProjectStartTime(projectDeclareDTO.getProjectStartTime());
            projectDTO.setProjectEndTime(projectDeclareDTO.getProjectEndTime());
            projectDTO.setProjectType(projectDeclareDTO.getProjectType());
            projectDTO.setProjectSubType(projectDeclareDTO.getProjectSubType());
            projectDTO.setProjectSource(projectDeclareDTO.getProjectSource());
            projectDTO.setResPerson(projectDeclareDTO.getResUserId());
            projectDTO.setResAdministrativeOffice(projectDeclareDTO.getRspDeptId());
            String resUser = projectDTO.getResPerson();
            if (StringUtils.hasText(resUser)) {
                SimpleUser resSimpleUser = userRedisHelper.getSimpleUserById(resUser);
                if (resSimpleUser != null) {
                    if (StringUtils.hasText(projectDeclareDTO.getRspDeptId())) {
                        projectDTO.setResAdministrativeOffice(resSimpleUser.getDeptId());
                    }
                    projectDTO.setResTeamGroup(resSimpleUser.getClassId());
                    projectDTO.setResDept(resSimpleUser.getOrgId());
                }
            }
            projectDTO.setIsDeclare(true);
            String newProjectId = projectService.saveProject(projectDTO);
            projectDeclareDTO.setProjectId(newProjectId);
        }

        LambdaQueryWrapper<ProjectDeclareFileInfo> projectOrionWrapper = new LambdaQueryWrapper<>(ProjectDeclareFileInfo.class);
        projectOrionWrapper.eq(ProjectDeclareFileInfo::getType, ProjectDeclareFileTypeEnum.MATERIAL.getCode());
        projectOrionWrapper.eq(ProjectDeclareFileInfo::getProjectDeclareId, projectDeclareDTO.getId());
        List<ProjectDeclareFileInfo> projectDeclareFileInfos = projectDeclareFileInfoRepository.selectList(projectOrionWrapper);
        List<String> fileDataIds = new ArrayList<>();
        List<String> resultFileDataIds = new ArrayList<>();
        if (!CollectionUtils.isBlank(projectDeclareFileInfos)) {
            fileDataIds.addAll(projectDeclareFileInfos.stream().map(ProjectDeclareFileInfo::getFileDataId).collect(Collectors.toList()));
        }
        List<FileInfoDTO> fileInfoDTOList = projectDeclareDTO.getFileInfoDTOList();
        if (!CollectionUtils.isBlank(fileDataIds) || !CollectionUtils.isBlank(fileInfoDTOList)) {
            if (CollectionUtils.isBlank(fileDataIds)) {
                fileInfoDTOList.forEach(p -> p.setDataId(projectDeclareDTO.getId()));
                resultFileDataIds = projectDeclareFileInfoService.saveBatchAdd(ProjectDeclareFileTypeEnum.MATERIAL.getCode(), fileInfoDTOList);
            } else if (CollectionUtils.isBlank(fileInfoDTOList)) {
                projectDeclareFileInfoService.deleteBatchFile(ProjectDeclareFileTypeEnum.MATERIAL.getCode(), fileDataIds);
            } else {
                resultFileDataIds.addAll(fileDataIds);
                List<FileInfoDTO> insertFileList = new ArrayList<>();
                for (FileInfoDTO fileInfoDTO : fileInfoDTOList) {
                    if (!StringUtils.hasText(fileInfoDTO.getId())) {
                        insertFileList.add(fileInfoDTO);
                        continue;
                    }
                    if (!fileDataIds.contains(fileInfoDTO.getId())) {
                        insertFileList.add(fileInfoDTO);
                    } else {
                        fileDataIds.remove(fileInfoDTO.getId());
                    }
                }
                if (!CollectionUtils.isBlank(fileDataIds)) {
                    resultFileDataIds.removeAll(fileDataIds);
                    projectDeclareFileInfoService.deleteBatchFile(ProjectDeclareFileTypeEnum.MATERIAL.getCode(), fileDataIds);
                }
                if (!CollectionUtils.isBlank(insertFileList)) {
                    insertFileList.forEach(p -> p.setDataId(projectDeclareDTO.getId()));
                    List<String> insertFileIds = projectDeclareFileInfoService.saveBatchAdd(ProjectDeclareFileTypeEnum.MATERIAL.getCode(), insertFileList);
                    resultFileDataIds.addAll(insertFileIds);
                }
            }
        }
        ProjectDeclare projectDeclare = BeanCopyUtils.convertTo(projectDeclareDTO, ProjectDeclare::new);
        int update = projectDeclareRepository.updateById(projectDeclare);
        // 初始化绑定综合计划
        List<RelevancyPlanDTO> basePlanList = projectDeclareDTO.getPlanList();
        newProjectToBasePlanService.packageEntityListAndSave(projectDeclareDTO.getProjectId(), projectNumber, basePlanList);
        ProjectDeclareVO rsp = BeanCopyUtils.convertTo(projectDeclare, ProjectDeclareVO::new);
        rsp.setFileDataIds(resultFileDataIds);
        return rsp;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        List<ProjectDeclare> projectDeclareList = projectDeclareRepository.selectList(ProjectDeclare::getId, ids);
        if (CollectionUtils.isBlank(projectDeclareList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "项目申报记录不存在或被删除!");
        }
        if (projectDeclareList.stream().filter(item -> !item.getStatus().equals(ProjectDeclareStatusEnum.CREATED.getStatus())).findAny().isPresent()) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "项目申报记录当前状态不能删除!");
        }

        int delete = projectDeclareRepository.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }

    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectDeclareVO> pages(Page<ProjectDeclareDTO> pageRequest) throws Exception {
        Page<ProjectDeclare> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
//        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectDeclare::new));
//        realPageRequest.setSearchConditions(pageRequest.getSearchConditions());
        LambdaQueryWrapperX<ProjectDeclare> objectLambdaQueryWrapperX = new LambdaQueryWrapperX<>(ProjectDeclare.class);
        objectLambdaQueryWrapperX.selectAll(ProjectDeclare.class);
        objectLambdaQueryWrapperX.selectAs(Project::getResDept,ProjectDeclare::getRspDept);
        objectLambdaQueryWrapperX.leftJoin(Project.class, Project::getId, ProjectDeclare::getProjectId);
//        objectLambdaQueryWrapperX = SearchConditionUtils.parseSearchConditionsWrapper(realPageRequest.getSearchConditions(), objectLambdaQueryWrapperX);
        if (ObjectUtil.isNotNull(pageRequest.getQuery()) && ObjectUtil.isNotNull(pageRequest.getQuery().getProjectName())) {
            objectLambdaQueryWrapperX.like(Project::getName, pageRequest.getQuery().getProjectName());
        }
        objectLambdaQueryWrapperX.orderByDesc(ProjectDeclare::getCreateTime);
        PageResult<ProjectDeclare> page = projectDeclareRepository.selectPage(realPageRequest, objectLambdaQueryWrapperX);

        Page<ProjectDeclareVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectDeclareVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectDeclareVO::new);
        if (!CollectionUtils.isBlank(vos)) {
            List<String> projectIds = vos.stream().map(ProjectDeclareVO::getProjectId).collect(Collectors.toList());
            List<Project> projectList = projectRepository.selectList(Project::getId, projectIds);
            Map<String, Project> projectMap = projectList.stream().collect(Collectors.toMap(Project::getId, Function.identity()));
            Map<String, String> projectTypeDict = dictBo.getDictNumberToDesMap(DictConstant.Project_Type);
            Map<String, String> projectTypeInvestmentTypeDict = dictBo.getDictValueToDesMap(DictConstant.Project_Type_InvestmentType);
            List<String> rspDeptIds = projectList.stream().map(Project::getResDept).filter(StringUtils::hasText).collect(Collectors.toList());
            Map<String, String> organizationMap = new HashMap<>();
            if (!CollectionUtils.isBlank(rspDeptIds)) {
                List<DeptVO> deptVOList = deptRedisHelper.getDeptByIds(rspDeptIds);
                if (!CollectionUtils.isBlank(deptVOList)) {
                    organizationMap = deptVOList.stream().collect(Collectors.toMap(DeptVO::getId, DeptVO::getName));
                }
            }

            List<String> rspUserIds = projectList.stream().filter(p -> StringUtils.hasText(p.getResPerson())).map(Project::getResPerson).collect(Collectors.toList());
            Map<String, String> userMap = new HashMap<>();
            if (!CollectionUtils.isBlank(rspUserIds)) {
                List<UserVO> userVOS = userRedisHelper.getUserByIds(rspUserIds);
                if (!CollectionUtils.isBlank(userVOS)) {
                    userMap = userVOS.stream().collect(Collectors.toMap(UserVO::getId, UserVO::getName));
                }
            }

            // 状态策略
            Map<Integer, DataStatusVO> statusMap = dataStatusNBO.getDataStatusMapByClassName(ProjectDeclare.class.getSimpleName());

            for (ProjectDeclareVO projectDeclareVO : vos) {
                Project project = projectMap.get(projectDeclareVO.getProjectId());
                if (project != null) {
                    projectDeclareVO.setProjectName(project.getName());
                    projectDeclareVO.setProjectType(project.getProjectType());
                    projectDeclareVO.setProjectSubType(project.getProjectSubType());
                    projectDeclareVO.setRspDeptId(project.getResAdministrativeOffice());
                    projectDeclareVO.setResUserId(project.getResPerson());
                }

                String projectType = projectDeclareVO.getProjectType();
                if (StringUtils.hasText(projectType)) {
                    projectDeclareVO.setProjectTypeName(projectTypeDict.get(projectType));
                }

                String projectSubType = projectDeclareVO.getProjectSubType();
                if (StringUtils.hasText(projectSubType)) {
                    projectDeclareVO.setProjectSubTypeName(projectTypeInvestmentTypeDict.get(projectSubType));
                }

                if (StringUtils.hasText(projectDeclareVO.getRspDept())) {
                    String dept = organizationMap.get(projectDeclareVO.getRspDept());
                    projectDeclareVO.setRspDeptName(null == dept ? "" : dept);
                }
                if (StringUtils.hasText(projectDeclareVO.getResUserId())) {
                    String userName = userMap.get(projectDeclareVO.getResUserId());
                    projectDeclareVO.setResUserName(null == userName ? "" : userName);
                }

                projectDeclareVO.setDataStatus(statusMap.getOrDefault(projectDeclareVO.getStatus(), new DataStatusVO()));
            }
        }
        //权限设置
        Map<String, List<String>> dataRoleMap = new HashMap<>();
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        for (ProjectDeclareVO v : vos) {
            List<String> roles = pmsAuthUtil.getRoleCodeList(v.getProjectId(), currentUserId);
            dataRoleMap.put(v.getId(), roles);
        }
        pmsAuthUtil.setRowAuths(CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), vos,
                ProjectDeclareVO::getId, ProjectDeclareVO::getDataStatus, ProjectDeclareVO::setRdAuthList,
                ProjectDeclareVO::getCreatorId,
                ProjectDeclareVO::getModifyId,
                ProjectDeclareVO::getOwnerId,
                dataRoleMap);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public Page<ProjectVO> projectPages(Page<ProjectDTO> pageRequest) throws Exception {
        // LambdaQueryWrapper<ProjectDeclare> projectDeclareOrionWrapper = new LambdaQueryWrapper<>(ProjectDeclare.class);
        List<ProjectDeclare> projectDeclareList = projectDeclareRepository.selectList();
        List<String> projectIds = projectDeclareList.stream().map(ProjectDeclare::getProjectId).collect(Collectors.toList());

        LambdaQueryWrapperX<Project> projectOrionWrapper = new LambdaQueryWrapperX<>(Project.class);
        projectOrionWrapper.eq(Project::getStatus, NewProjectStatusEnum.PROJECT_CREATED.getStatus());
//        projectOrionWrapper.eq(Project::getIsDeclare, true);
        if (!CollectionUtils.isBlank(projectIds)) {
            projectOrionWrapper.notIn(Project::getId, projectIds);
        }
        Page<Project> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setSearchConditions(pageRequest.getSearchConditions());
        if (CollectionUtil.isNotEmpty(pageRequest.getSearchConditions())) {
            projectOrionWrapper = SearchConditionUtils.parseSearchConditionsWrapper(realPageRequest.getSearchConditions(), projectOrionWrapper);
        }
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), Project::new));
        realPageRequest.setSearchConditions(pageRequest.getSearchConditions());
        PageResult<Project> page = projectRepository.selectPage(realPageRequest, projectOrionWrapper);

        Page<ProjectVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectVO::new);
        Map<String, String> projectTypeDict = dictBo.getDictNumberToDesMap(DictConstant.Project_Type);
        Map<String, String> projectTypeInvestmentTypeDict = dictBo.getDictValueToDesMap(DictConstant.Project_Type_InvestmentType);
        Map<String, String> researchChildren = dictBo.getDictValueToDesMap(DictConstant.PROJECT_TYPE_RESEARCH_CHILDREN);
        for(ProjectVO projectVO:vos){
            String projectType = projectVO.getProjectType();
            if (StringUtils.hasText(projectType)) {
                projectVO.setProjectTypeName(projectTypeDict.get(projectType));
            }

            String projectSubType = projectVO.getProjectSubType();
            if(StringUtils.hasText(projectSubType) && ProjectTypeEnum.PROJECT_TYPE_INVEST_SERVER.getValue().equals(projectType)){
                projectVO.setProjectSubTypeName(projectTypeInvestmentTypeDict.get(projectSubType));
            }
            if(StringUtils.hasText(projectSubType) && ProjectTypeEnum.PROJECT_TYPE_RESEARCH.getValue().equals(projectType)){
                projectVO.setProjectSubTypeName(researchChildren.get(projectSubType));
            }
        }

        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public Page<ProjectDeclareVO> userPages(Page<ProjectDeclareDTO> pageRequest) throws Exception {
        String userId = currentUserHelper.getUserId();
        Page<ProjectDeclare> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectDeclare::new));
        realPageRequest.setSearchConditions(pageRequest.getSearchConditions());
        LambdaQueryWrapperX<ProjectDeclare> objectLambdaQueryWrapperX = new LambdaQueryWrapperX<>(ProjectDeclare.class);
        objectLambdaQueryWrapperX.selectAll(ProjectDeclare.class);
        //TODO 6/12 审查 魏宇航  只返回ID
        List<Project> projects = projectService.list();
        List<String> projectIdList = projects.stream().map(Project::getId).collect(Collectors.toList());
        objectLambdaQueryWrapperX.inIfPresent(ProjectDeclare::getProjectId, projectIdList);
        objectLambdaQueryWrapperX.eq(ProjectApproval::getCreatorId, userId);
        if (ObjectUtil.isNotEmpty(pageRequest.getQuery())) {
            ProjectDeclareDTO projectDeclareDTO = pageRequest.getQuery();
            if (StrUtil.isNotBlank(projectDeclareDTO.getName())) {
                LambdaQueryWrapperX<Project> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(Project.class);
                lambdaQueryWrapperX.like(Project::getName, projectDeclareDTO.getName()).or()
                        .like(Project::getNumber, projectDeclareDTO.getName());
                List<Project> list = projectService.list(lambdaQueryWrapperX);
                if (CollectionUtil.isEmpty(list)) {
                    return new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), pageRequest.getTotalSize());
                }
                List<String> projectIds = list.stream().map(Project::getId).collect(Collectors.toList());
                objectLambdaQueryWrapperX.in(ProjectDeclare::getProjectId, projectIds);
            }
        }
        if (CollectionUtil.isNotEmpty(realPageRequest.getSearchConditions())) {
            objectLambdaQueryWrapperX = SearchConditionUtils.parseSearchConditionsWrapper(realPageRequest.getSearchConditions(), objectLambdaQueryWrapperX);
        }
        PageResult<ProjectDeclare> page = projectDeclareRepository.selectPage(realPageRequest, objectLambdaQueryWrapperX);
        Page<ProjectDeclareVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectDeclareVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectDeclareVO::new);
        if (!CollectionUtils.isBlank(vos)) {
            List<String> projectIds = vos.stream().map(ProjectDeclareVO::getProjectId).collect(Collectors.toList());
            List<Project> projectList = projectRepository.selectList(Project::getId, projectIds);
            Map<String, Project> projectMap = projectList.stream().collect(Collectors.toMap(Project::getId, Function.identity()));
            Map<String, String> projectTypeDict = dictBo.getDictNumberToDesMap(DictConstant.Project_Type);
            Map<String, String> projectTypeInvestmentTypeDict = dictBo.getDictValueToDesMap(DictConstant.Project_Type_InvestmentType);
            List<String> rspDeptIds = projectList.stream().filter(p -> StringUtils.hasText(p.getResDept())).map(Project::getResDept).collect(Collectors.toList());
            Map<String, String> organizationMap = new HashMap<>();
            if (!CollectionUtils.isBlank(rspDeptIds)) {
                List<DeptVO> deptVOList = deptRedisHelper.getDeptByIds(rspDeptIds);
                if (!CollectionUtils.isBlank(deptVOList)) {
                    organizationMap = deptVOList.stream().collect(Collectors.toMap(DeptVO::getId, DeptVO::getName));
                }
            }
            List<String> rspUserIds = projectList.stream().filter(p -> StringUtils.hasText(p.getResPerson())).map(Project::getResPerson).collect(Collectors.toList());
            Map<String, String> userMap = new HashMap<>();
            if (!CollectionUtils.isBlank(rspUserIds)) {
                List<UserVO> userVOS = userRedisHelper.getUserByIds(rspUserIds);
                if (!CollectionUtils.isBlank(userVOS)) {
                    userMap = userVOS.stream().collect(Collectors.toMap(UserVO::getId, UserVO::getName));
                }
            }

            for (ProjectDeclareVO projectDeclareVO : vos) {
                Project project = projectMap.get(projectDeclareVO.getProjectId());
                if (project != null) {
                    projectDeclareVO.setProjectName(project.getName());
                    projectDeclareVO.setProjectType(project.getProjectType());
                    projectDeclareVO.setProjectSubType(project.getProjectSubType());
                    projectDeclareVO.setRspDeptId(project.getResDept());
                    projectDeclareVO.setResUserId(project.getResPerson());
                }

                String projectType = projectDeclareVO.getProjectType();
                if (StringUtils.hasText(projectType)) {
                    projectDeclareVO.setProjectTypeName(projectTypeDict.get(projectType));
                }

                String projectSubType = projectDeclareVO.getProjectSubType();
                if (StringUtils.hasText(projectSubType)) {
                    projectDeclareVO.setProjectSubTypeName(projectTypeInvestmentTypeDict.get(projectSubType));
                }

                if (StringUtils.hasText(projectDeclareVO.getRspDeptId())) {
                    String dept = organizationMap.get(projectDeclareVO.getRspDeptId());
                    projectDeclareVO.setRspDeptName(null == dept ? "" : dept);
                }
                if (StringUtils.hasText(projectDeclareVO.getResUserId())) {
                    String userName = userMap.get(projectDeclareVO.getResUserId());
                    projectDeclareVO.setResUserName(null == userName ? "" : userName);
                }
            }
        }
        pageResult.setContent(vos);
        return pageResult;
    }
}
