package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * ContractPayNodeConfirmDetailVO Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-26 21:44:48
 */
@ApiModel(value = "ContractPayNodeConfirmDetailVO对象", description = "项目合同支付节点确认详情")
@Data
public class ContractPayNodeConfirmDetailVO extends ObjectVO implements Serializable {
    /**
     * 合同支付节点列表
     */
    @ApiModelProperty(value = "合同支付节点列表")
    private List<ContractPayNodeVO> contractPayNodeVOList;

    /**
     * 合同支付节点信息
     */
    @ApiModelProperty(value = "合同支付节点列表")
    private ContractPayNodeVO contractPayNodeVO;

    /**
     * 合同支付节点确认信息
     */
    @ApiModelProperty(value = "合同支付节点确认信息")
    private ContractPayNodeConfirmVO contractPayNodeConfirmVO;

    /**
     * 节点确认材料上传
     */
    @ApiModelProperty(value = "节点确认材料上传")
    private List<DocumentVO> documentVOList;
}
