package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.MarketContractDTO;
import com.chinasie.orion.domain.dto.MarketContractDivisionLeaderDTO;
import com.chinasie.orion.domain.dto.MarketContractFileDTO;
import com.chinasie.orion.domain.entity.MarketContract;
import com.chinasie.orion.domain.vo.MarketContractApiVO;
import com.chinasie.orion.domain.vo.MarketContractDetailMoneyVo;
import com.chinasie.orion.domain.vo.MarketContractVO;
import com.chinasie.orion.domain.vo.SubOrderContractTotalAmt;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.vo.QuotationManagementVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * MarketContract 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28 21:47:43
 */
public interface MarketContractService extends OrionBaseService<MarketContract> {
    /**
     *  详情
     *
     * * @param id
     */
    MarketContractVO detail(String id, String pageCode) throws Exception;

    /**
     * 投入分析
     * @param id
     * @return
     * @throws Exception
     */
    QuotationManagementVO inputAnalysis(String id) throws Exception;

    /**
     *  新增
     *
     * * @param marketContractDTO
     */
    String create(MarketContractDTO marketContractDTO) throws Exception;

    /**
     *  新增框架下子订单
     *
     * * @param marketContractDTO
     */
    String createOrder(MarketContractDTO marketContractDTO) throws Exception;

    /**
     *  新增附件
     *
     * * @param marketContractDTO
     */
    Boolean createFile(MarketContractFileDTO marketContractFileDTO) throws Exception;

    /**
     *  查询附件
     *
     * * @param dataId
     */
    List<Map<String, Object>> queryFile(String dataId) throws Exception;


    /**
     *  删除附件
     *
     * * @param fileId
     */
    Boolean deleteFile(List<String> fileIds) throws Exception;

    /**
     *  编辑
     *
     * * @param marketContractDTO
     */
    Boolean edit(MarketContractDTO marketContractDTO) throws Exception;

    /**
     *  关闭
     *
     * * @param marketContractDTO
     */
    Boolean close(String id) throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<MarketContractVO> pages(Page<MarketContractDTO> pageRequest) throws Exception;


    /**
     *
     * @param numbers
     * @return
     * @throws Exception
     */
    List<MarketContractVO> listByNumber(List<String> numbers) throws Exception;


    /**
     *  子订单分页
     *
     * * @param pageRequest
     *
     */
    Page<MarketContractVO> subOrderPages(Page<MarketContractDTO> pageRequest) throws Exception;

    /**
     * 查询子订单总金额
     */
    SubOrderContractTotalAmt subOrderTotalAmt(String id);

    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<MarketContractVO> complateFramePages(Page<MarketContractDTO> pageRequest);

    /**
     *  导入校验
     *
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     *  导入校验
     *
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     *  确认导入
     *
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     *  取消导入
     *
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<MarketContractVO> vos) throws Exception;

    /**
     *  根据编号查询
     *
     * * @param searchConditions
     * * @param response
     */
    MarketContract getByNumber(String number);

    /**
     *  根据编号list查询
     *
     * * @param searchConditions
     * * @param response
     */
    List<MarketContract> getByNumberList(List<MarketContractDTO> dtos);

    /**
     * 发起流程前判断组合类型里程碑是否有子项
     */
    List<String> judgeMilestone(String marketContractId);

    /**
     * 返回合同的状态 字典
     *
     * @return list
     */
    List<DataStatusVO> listDataStatus();

    /**
     * 生成合同编码
     *
     * @param marketContract 市场合同
     * @param signedMainName 签约主体名称
     * @return 编码
     */
    String generateApplyNo(MarketContract marketContract, String signedMainName);

    /**
     * 查询合同信息
     * @param projectNumbers
     * @return
     */
    List<MarketContractApiVO> findByProjectNumber(List<String> projectNumbers);

    /**
     * 查询所级领导
     * @return
     */
    MarketContractDivisionLeaderDTO getDivisionLeaders(MarketContractDivisionLeaderDTO marketContractDivisionLeaderDTO);

    MarketContractDetailMoneyVo getMoney(String contractId);

    /**
     * 收入计划合同信息分页查询
     * @param pageRequest
     * @param type 0：查询未签订合同；1-不查询未签订合同
     * @return
     * @throws Exception
     */
    Page<MarketContractVO> getIncomeMarketContractPage(Page<MarketContractDTO> pageRequest, int type) throws Exception;


    /**
     * 合同报表
     * @param pageRequest
     * @param response
     */
    void exportExcelData(Page<MarketContractDTO> pageRequest, HttpServletResponse response) throws Exception;

    /**
     * 判断当前合同的业务类型是否和他的需求保持一致
     * @param marketContract
     * @return
     * @throws Exception
     */
    Boolean businessType(MarketContract marketContract) throws Exception;

    Boolean returnAmtUpdate(String marketContractId) throws Exception;

    /**
     * 子订单和子合同的编辑
     */
    Boolean editSub(MarketContractDTO marketContractDTO) throws Exception;

    void exportExcelDataNew(Page<MarketContractDTO> pageRequest, HttpServletResponse response) throws Exception;

    Page<MarketContractVO> pagesMenu(Page<MarketContractDTO> pageRequest) throws Exception;
}
