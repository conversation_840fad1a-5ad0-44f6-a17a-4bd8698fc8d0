<script setup lang="ts">
import {
  computed, inject, ref, Ref,
} from 'vue';
import { fieldConfig } from './data';

const props = defineProps<{
  data: Record<string, any>
}>();

const repairRound: Ref<string> = inject('repairRound');
const legend: Ref<any[]> = ref([
  {
    label: '正常',
    value: 'pms_normal_type',
    bgColor: '#F5F5F5',
  },
  {
    label: '需要关注',
    value: 'pms_need_like_type',
    bgColor: '#FD897B',
  },
  {
    label: '预警',
    value: 'pms_warning_type',
    bgColor: '#F8CF2F',
  },
  {
    label: '已完成',
    value: 'pms_finished_type',
    bgColor: '#73CC93',
  },
]);

const renderData = computed(() => fieldConfig.map((item) => ({
  ...item,
  value: props.data[item.field],
})));

function getColor(value: string) {
  return {
    bgColor: legend.value.find((item) => item.value === value)?.bgColor || '#F5F5F5',
    color: value && value !== 'pms_normal_type' ? '#fff' : 'rgba(0,0,0,0.85)',
  };
}
</script>

<template>
  <div class="preparation-info">
    <div class="legend">
      <div
        v-for="(item,index) in legend"
        :key="index"
        class="legend-item"
      >
        <span
          :style="{backgroundColor:item.bgColor}"
          class="block"
        />
        <span>{{ item.label }}</span>
      </div>
    </div>
    <div class="grid-container">
      <div
        v-for="(item,index) in renderData"
        :key="index"
        :style="{backgroundColor:getColor(item.value).bgColor,color:getColor(item.value).color}"
      >
        {{ item.label }}
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.preparation-info {
  height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.legend {
  display: flex;
  align-items: center;
  justify-items: center;

  .legend-item {
    display: flex;
    align-items: center;

    & + .legend-item {
      margin-left: 20px;
    }

    .block {
      width: 14px;
      height: 14px;
      margin-right: 8px;
      border-radius: 2px;
    }
  }
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(4, minmax(0, 1fr));
  grid-template-rows: repeat(3, 48px);
  gap: 2px;

  > div {
    text-align: center;
    line-height: 48px;
    font-size: 16px;
  }
}
</style>
