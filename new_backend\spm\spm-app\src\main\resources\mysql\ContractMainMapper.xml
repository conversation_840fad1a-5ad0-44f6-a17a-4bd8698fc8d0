<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.ContractMainMapper">


    <select id="copyCenterPlan" resultType="com.chinasie.orion.domain.entity.ContractCenterPlan">
        SELECT
        pcm.contract_number AS 'contractNumber',
        pcm.contract_name AS 'contractName',
        pcc.center_code AS 'centerCode',
        pcc.center_name AS 'centerName',
        pcct.cost_type_number AS 'costTypeNumber',
        pcct.id AS 'costTypeId'
        FROM
        pmsx_contract_main pcm
        INNER JOIN pmsx_contract_center pcc ON pcm.contract_number = pcc.contract_number
        INNER JOIN pmsx_contract_cost_type pcct ON pcm.contract_number = pcct.contract_number
        WHERE
        pcm.logic_status = 1
        AND pcm.id IN
        <foreach item='id' collection='unIssuedIds' open='(' separator=',' close=')'>
            #{id}
        </foreach>
        AND pcc.center_code is not NULL
        AND pcc.logic_status = 1
        AND pcct.logic_status = 1
        AND YEAR(pcm.year) = YEAR(CURDATE())
    </select>

    <select id="selectAttendanceSign" resultType="com.chinasie.orion.domain.entity.AttendanceSignStatistics">
        SELECT
        attandance_quarter as dataQuarter,
        org_code,
        org_name,
        SUM(attandance_rate ) attandance_rate,
        SUM( attandance_rate * b.unit_price ) unit_price
        FROM
        pmsx_attendance_sign a
        LEFT JOIN pmsx_contract_cost_type b ON a.contract_no = b.contract_number
        AND b.cost_type_number = 'positionCost'
        AND a.job_grade = b.cost_name
        WHERE
        attandance_year = #{year}
        and contract_no = #{contractNumber}
        <if test=" null != quarter  and quarter !=''">
            AND attandance_quarter = #{quarter}
        </if>
        AND a.org_code IN
        <foreach item='id' collection='orgCodes' open='(' separator=',' close=')'>
            #{id}
        </foreach>
        GROUP BY
        attandance_quarter,
        org_code,
        org_name
    </select>

    <select id="selectTravelCost" resultType="com.chinasie.orion.domain.entity.TravelCostStatistics">
        SELECT
        data_quarter as dataQuarter,
        org_code,
        org_name,
        SUM( hotel_amount ) hotel_amount,
        SUM( transfer_amount ) transfer_amount,
        SUM( traffic_amount ) traffic_amount
        FROM
        pmsx_travel_cost
        WHERE
        data_year = #{year}
        and contract_code = #{contractNumber}
        <if test=" null != quarter  and quarter !=''">
            AND data_quarter = #{quarter}
        </if>
        AND org_code IN
        <foreach item='id' collection='orgCodes' open='(' separator=',' close=')'>
            #{id}
        </foreach>
        GROUP BY
        data_quarter,
        org_code,
        org_name
    </select>

    <select id="selectPlaneCost" resultType="com.chinasie.orion.domain.entity.PlaneCostStatistics">
        SELECT
        data_quarter as dataQuarter,
        org_code,
        org_name,
        SUM( discount_price ) discount_price
        FROM
        pmsx_plane_cost
        WHERE
        data_year = #{year}
        and contract_no = #{contractNumber}
        <if test=" null != quarter  and quarter !=''">
            AND data_quarter = #{quarter}
        </if>
        AND org_code IN
        <foreach item='id' collection='orgCodes' open='(' separator=',' close=')'>
            #{id}
        </foreach>
        GROUP BY
        data_quarter,
        org_code,
        org_name
    </select>

    <select id="selectOpenCost" resultType="com.chinasie.orion.domain.entity.OpenCostStatistics">
        SELECT
        data_quarter as dataQuarter,
        org_code,
        org_name,
        SUM(IF(pay_type_no = 'logisticsFee',pay_amt,0) ) logisticsAmt,
        SUM(IF(pay_type_no = 'physicalExaminationFee',pay_amt,0) ) physicalExaminationAmt,
        SUM(IF(pay_type_no = 'laborFee',pay_amt,0) ) laborAmt,
        SUM(IF(pay_type_no = 'restaurantManagementFee',pay_amt,0) ) restaurantManagementAmt,
        SUM(IF(pay_type_no = 'otherFee',pay_amt,0) ) otherAmt
        FROM pmsx_open_cost a
        WHERE
        data_year = #{year}
        and contract_no = #{contractNumber}
        <if test=" null != quarter  and quarter !=''">
            AND data_quarter = #{quarter}
        </if>
        AND org_code IN
        <foreach item='id' collection='orgCodes' open='(' separator=',' close=')'>
            #{id}
        </foreach>
        GROUP BY
        data_quarter,
        org_code,
        org_name
    </select>
</mapper>
