package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;

/**
 * BudgetRecord Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-08 20:08:40
 */
@TableName(value = "pmsx_budget_record")
@ApiModel(value = "BudgetRecordEntity对象", description = "预算改变记录")
@Data
public class BudgetRecord extends ObjectEntity implements Serializable {

    /**
     * 预算修改类型
     */
    @ApiModelProperty(value = "预算修改类型")
    @TableField(value = "budget_change_type")
    private String budgetChangeType;

    /**
     * 预算Id
     */
    @ApiModelProperty(value = "预算Id")
    @TableField(value = "budget_id")
    private String budgetId;

    /**
     * 预算修改单Id
     */
    @ApiModelProperty(value = "预算修改单Id")
    @TableField(value = "budget_change_id")
    private String budgetChangeId;

    /**
     * 预算修改编码
     */
    @ApiModelProperty(value = "预算修改编码")
    @TableField(value = "budget_change_number")
    private String budgetChangeNumber;

    /**
     * 预算修改表单名称
     */
    @ApiModelProperty(value = "预算修改表单名称")
    @TableField(value = "budget_change_name")
    private String budgetChangeName;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    @TableField(value = "operation_time")
    private Date operationTime;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    @TableField(value = "operation_person")
    private String operationPerson;

    /**
     * 改变金额
     */
    @ApiModelProperty(value = "改变金额")
    @TableField(value = "change_money")
    private BigDecimal changeMoney;

    /**
     * 改变后金额
     */
    @ApiModelProperty(value = "改变后金额")
    @TableField(value = "after_change_money")
    private BigDecimal afterChangeMoney;

}
