package com.chinasie.orion.domain.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * YearInvestmentSchemeMonthFeedback Entity对象
 *
 * <AUTHOR>
 * @since 2023-05-15 19:21:49
 */
@TableName(value = "pms_year_investment_scheme_month_feedback")
@ApiModel(value = "YearInvestmentSchemeMonthFeedback对象", description = "月度投资计划反馈")
@Data
public class YearInvestmentSchemeMonthFeedback extends ObjectEntity implements Serializable {

    /**
     * 计划编号
     */
    @ApiModelProperty(value = "计划编号")
    @TableField(value = "number")
    private String number;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField(value = "name")
    private String name;

    /**
     * 下月进度计划
     */
    @ApiModelProperty(value = "下月进度计划")
    @TableField(value = "next_process")
    private String nextProcess;

    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @TableField(value = "year")
    private String year;

    /**
     * 项目总体进度滞后情况
     */
    @ApiModelProperty(value = "项目总体进度滞后情况")
    @TableField(value = "delay_desc")
    private String delayDesc;

    /**
     * 年度投资计划Id
     */
    @ApiModelProperty(value = "年度投资计划Id")
    @TableField(value = "year_investment_id")
    private String yearInvestmentId;

    /**
     * 本月进度执行情况说明
     */
    @ApiModelProperty(value = "本月进度执行情况说明")
    @TableField(value = "month_process")
    private String monthProcess;

    /**
     * 本月投资计划执行状态
     */
    @ApiModelProperty(value = "本月投资计划执行状态")
    @TableField(value = "month_do_status")
    private Integer monthDoStatus;

    /**
     * 本月执行偏差原因及纠偏措施
     */
    @ApiModelProperty(value = "本月执行偏差原因及纠偏措施")
    @TableField(value = "reason")
    private String reason;

    /**
     * 月度
     */
    @ApiModelProperty(value = "月度")
    @TableField(value = "month")
    private String month;

    /**
     * 1-M月实际执行
     */
    @ApiModelProperty(value = "1-M月实际执行")
    @TableField(value = "m_practice_do")
    private BigDecimal mpracticeDo = new BigDecimal("0");

    /**
     * 总体进度执行情况
     */
    @ApiModelProperty(value = "总体进度执行情况")
    @TableField(value = "total_process")
    private String totalProcess;

}
