<script setup lang="ts">
import { BasicCard } from 'lyra-component-vue3';
import { inject, reactive } from 'vue';

const pageType = inject('pageType');
const detailsData: Record<string, any> = inject('detailsData');
const trainInfo = reactive({
  list: [
    {
      label: '培训类型',
      field: 'typeName',
      hidden: pageType !== 'special',
    },
    {
      label: '培训名称',
      field: 'name',
    },
    {
      label: '是否考核',
      field: 'isCheck',
      isBoolean: true,
    },
    {
      label: '培训基地',
      field: 'baseName',
    },
    {
      label: '拟完成时间',
      field: 'completeDate',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '实际完成时间',
      field: 'endDate',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '培训有效期（月）',
      field: 'expirationMonth',
    },
    {
      label: '培训到期时间',
      field: 'expireTime',
      formatTime: 'YYYY-MM-DD',
      hidden: !detailsData.isCheck,
    },
    {
      label: '培训课时',
      field: 'lessonHour',
    },
    {
      label: '培训地点',
      field: 'trainAddress',
      hidden: !detailsData.isCheck,
    },
    {
      label: '培训讲师',
      field: 'trainLecturer',
      hidden: !detailsData.isCheck,
    },
    {
      label: '业务状态',
      field: 'dataStatus',
    },
    {
      label: '培训内容',
      field: 'content',
      gridColumn: '1/5',
      wrap: true,
    },
  ],
  dataSource: detailsData,
});
</script>

<template>
  <BasicCard
    title="培训信息"
    :is-border="false"
    :grid-content-props="trainInfo"
  />
</template>

<style scoped lang="less">

</style>
