package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * ImToParameter Entity对象
 *
 * <AUTHOR>
 * @since 2024-01-31 13:52:16
 */
@TableName(value = "pmsx_im_to_parameter")
@ApiModel(value = "ImToParameterEntity对象", description = "接口和参数的关系")
@Data
public class ImToParameter extends ObjectEntity implements Serializable{

/**
 * 接口ID
 */
@ApiModelProperty(value = "接口ID")
@TableField(value = "im_id")
private String imId;

/**
 * 参数ID
 */
@ApiModelProperty(value = "参数ID")
@TableField(value = "param_id")
private String paramId;

/**
 * 模板ID
 */
@ApiModelProperty(value = "模板ID")
@TableField(value = "model_id")
private String modelId;

}
