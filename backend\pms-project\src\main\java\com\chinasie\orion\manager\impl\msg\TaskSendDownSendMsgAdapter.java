package com.chinasie.orion.manager.impl.msg;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.bo.PmsMQProducer;
import com.chinasie.orion.constant.TaskMessageNode;
import com.chinasie.orion.conts.MsgBusinessTypeEnum;
import com.chinasie.orion.domain.dto.TaskMsgDTO;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.entity.CollaborativeCompilationTask;
import com.chinasie.orion.domain.entity.ProjectApproval;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.feign.MessageCenterApi;
import com.chinasie.orion.manager.SendMessageCommonAdapter;
import com.chinasie.orion.manager.TaskSendMessageCommonAdapter;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.service.ProjectApprovalService;
import com.chinasie.orion.service.ProjectService;
import com.chinasie.orion.tenant.core.context.TenantContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 计划下发时，消息推送处理
 * <p>
 * 计划完成时消除待办：
 *
 * @see com.chinasie.orion.service.impl.CollaborativeCompilationTaskServiceImpl#finish(com.chinasie.orion.domain.dto.CollaborativeCompilationTaskDTO)
 */
@Slf4j
@Component("taskSendDownSendMsgAdapter")
public class TaskSendDownSendMsgAdapter   extends TaskSendMessageCommonAdapter {

    /**
     * 项目计划详情页
     */
    private final String JUMP_URL = "/pms/establishmentTaskDetails/%s";

    @Resource
    private ProjectApprovalService projectApprovalService;

    @Resource
    protected CurrentUserHelper userHelper;



    @Override
    public <T> List<SendMessageDTO> buildMscMessageDTO(TaskMsgDTO taskMsgDTO) throws Exception {
        List<SendMessageDTO> messageDTOList = CollUtil.toList();
        //抄送
        if (CollUtil.isNotEmpty(taskMsgDTO.getRecipientIds())) {
            messageDTOList.addAll(buildSendCopyMessageDTO(taskMsgDTO.getCollaborativeCompilationTasks(), taskMsgDTO.getRecipientIds()));
        }
        messageDTOList.addAll(buildMscMessageDTO(taskMsgDTO.getCollaborativeCompilationTasks()));
        return messageDTOList;
    }

    /**
     * 抄送对象
     *
     * @param collaborativeCompilationTask
     * @param recipientIdList
     * @return
     */
    private <T> List<? extends SendMessageDTO> buildSendCopyMessageDTO(List<CollaborativeCompilationTask> collaborativeCompilationTask, List<String> recipientIdList) throws Exception {
        CollaborativeCompilationTask task = collaborativeCompilationTask.stream().findFirst().orElse(new CollaborativeCompilationTask());
        ProjectApproval projectApproval = Optional.ofNullable(projectApprovalService.getById(task.getApprovalId())).orElse(new ProjectApproval());
        List<SendMessageDTO> messageDTOList = CollUtil.toList();
        collaborativeCompilationTask.forEach(item -> {
            SendMessageDTO backMessageDTO = SendMessageDTO.builder()
                    .businessData(JSON.toJSONString(MapUtil.builder().put("flowType", "任务下发").build()))
                    .titleMap(MapUtil.builder(new HashMap<String, Object>())
                            .put("$approvalName$", projectApproval.getName())
                            .put("$taskName$", item.getName())
                            .build())
                    .messageMap(MapUtil.builder(new HashMap<String, Object>())
                            .put("$approvalName$", projectApproval.getName())
                            .put("$taskName$", item.getName())
                            .build())
                    .businessNodeCode(TaskMessageNode.NODE_TASK_SEND_COPY_RSP_USER)
                    .businessTypeCode("ProjectScheme")
                    .businessTypeName("项目计划")
                    .todoType(TodoTypeDict.TODO_TYPE_TASK)
                    .messageUrl(String.format(JUMP_URL, item.getId()))
                    .messageUrlName("详情")
                    .recipientIdList(recipientIdList)
                    .senderId(userHelper.getUserId())
                    .senderTime(new Date())
                    .businessId(packageBusinessId(MsgBusinessTypeEnum.TASK_SEND_DOWN, item.getId()))
                    .platformId(CurrentUserHelper.getPId())
                    .orgId(TenantContextHolder.getTenantId())
                    .build();
            messageDTOList.add(backMessageDTO);
        });
        return messageDTOList;
    }


    /**
     * 下发计划 对应对象
     *
     * @param collaborativeCompilationTask
     * @return
     */
    private <T> List<? extends SendMessageDTO> buildMscMessageDTO(List<CollaborativeCompilationTask> collaborativeCompilationTask) {
        List<SendMessageDTO> messageDTOList = CollUtil.toList();
        collaborativeCompilationTask.forEach(item -> {
            List<String> recipientIds = new ArrayList<>();
            recipientIds.add(item.getRspUser());
            log.info("plan-name:{},recipientIds:{}",item.getName(),JSON.toJSONString(recipientIds));

            SendMessageDTO backMessageDTO = SendMessageDTO.builder()
                    .businessData(JSON.toJSONString(MapUtil.builder().put("flowType", "任务下发").build()))
                    .titleMap(MapUtil.builder(new HashMap<String, Object>())
                            .put("$taskName$", item.getName())
                            .build())
                    .messageMap(MapUtil.builder(new HashMap<String, Object>())
                            .put("$taskName$", item.getName())
                            .build())
                    .businessNodeCode(TaskMessageNode.NODE_TASK_SEND_RSP_USER)
                    .businessTypeCode("ProjectScheme")
                    .businessTypeName("项目计划")
                    .todoType(TodoTypeDict.TODO_TYPE_TASK)
                    .messageUrl(String.format(JUMP_URL, item.getId()))
                    .messageUrlName("详情")
                    .recipientIdList(recipientIds.stream().distinct().collect(Collectors.toList()))
                    .senderId(userHelper.getUserId())
                    .senderTime(new Date())
                    .businessId(packageBusinessId(MsgBusinessTypeEnum.TASK_SEND_DOWN, item.getId()))
                    .platformId(CurrentUserHelper.getPId())
                    .orgId(TenantContextHolder.getTenantId())
                    .build();
            messageDTOList.add(backMessageDTO);
        });
        return messageDTOList;
    }


}
