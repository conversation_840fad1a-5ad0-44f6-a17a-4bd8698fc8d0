package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * IncomePlanDataLock Entity对象
 *
 * <AUTHOR>
 * @since 2024-09-29 18:50:39
 */
@TableName(value = "pmsx_income_plan_data_lock")
@ApiModel(value = "IncomePlanDataLockEntity对象", description = "收入数据锁定表")
@Data

public class IncomePlanDataLock extends  ObjectEntity  implements Serializable{

    /**
     * 专业中心
     */
    @ApiModelProperty(value = "专业中心")
    @TableField(value = "expertise_center")
    private String expertiseCenter;

    /**
     * 专业所
     */
    @ApiModelProperty(value = "专业所")
    @TableField(value = "expertise_station")
    private String expertiseStation;

    /**
     * 锁类型
     */
    @ApiModelProperty(value = "锁类型")
    @TableField(value = "lock_type")
    private String lockType;

    /**
     * 收入计划填报Id
     */
    @ApiModelProperty(value = "收入计划填报Id")
    @TableField(value = "income_plan_id")
    private String incomePlanId;

    /**
     * 锁状态
     */
    @ApiModelProperty(value = "锁状态")
    @TableField(value = "lock_status")
    private String lockStatus;

}
