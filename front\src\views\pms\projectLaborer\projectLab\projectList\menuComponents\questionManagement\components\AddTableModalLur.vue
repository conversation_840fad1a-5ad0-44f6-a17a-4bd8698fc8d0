<template>
  <div class="drawer-body">
    <BasicButton
      class="add-btn"
      type="primary"
      icon="add"
      @click="() => addNewData()"
    >
      添加记录
    </BasicButton>
    <a-form
      ref="formRef"
      :model="form"
      layout="vertical"
    >
      <a-form-item
        v-for="(val, index) in form.record"
        :key="val.key"
        label="执行记录:"
        :name="['record', index, 'value']"
        required
        :rule="recordRule"
      >
        <div class="flex">
          <a-textarea
            v-model:value="val.value"
            :disabled="val.id?true:false"
            placeholder="请输入记录"
          />
          <span
            v-if="form.record.length > 1"
            class="action-btn"
            @click="() => deleteItem(index)"
          >删除</span>
        </div>
      </a-form-item>
    </a-form>
  </div>
</template>
<script lang="ts">
import {
  Form, FormItem, message, Modal,
  Textarea,
} from 'ant-design-vue';
import dayjs from 'dayjs';
import { BasicButton, useDrawerInner } from 'lyra-component-vue3';
import {
  defineComponent, reactive, ref, onMounted,
} from 'vue';
import Api from '/@/api';
import { useUserStore } from '/@/store/modules/user';

export default defineComponent({
  components: {
    AForm: Form,
    AFormItem: FormItem,
    ATextarea: Textarea,
    BasicButton,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  emits: ['close', 'update'],
  setup(props, context) {
    onMounted(() => {
      getPlanDetail();
    });

    const getPlanDetail = async () => {
      const res = await new Api('/pms/questionReplyManagement/getListByNumber').fetch(
        { questionNumber: props.data.number },
        '',
        'POST',
      );
      form.record = res.map((item) => ({
        value: item.reply,
        key: item.id,
        id: item.id,
      }));
    };
    const submitLoading = ref(false);
    const data = ref({
      projectId: '',
      id: '',
    });
    const relationList = ref(['']);
    const values = ref([]);
    const detailData = ref({});

    const form = reactive({
      record: [
        {
          value: '',
          key: Date.now(),
        },
      ],
    });
    const recordRule = {
      required: true,
      message: '执行记录不能为空',
      trigger: 'change',
    };

    const formRef = ref(null);

    const [modalRegister, { closeDrawer }] = useDrawerInner(
      (drawerData) => {
        data.value = drawerData.data;
        detailData.value = {
          ...drawerData.data,
        };
        form.record = [
          {
            value: '',
            key: Date.now(),
          },
        ];

        submitLoading.value = false;
        getPlanDetail();
      },
    );

    const addNewData = () => {
      form.record.push({
        value: '',
        key: Date.now(),
      });
    };

    const deleteItem = (index) => {
      if (form.record[index].id) {
        Modal.confirm({
          title: '删除确认提示？',
          content: '请确认是否删除该记录，删除后不可恢复？',
          onOk() {
            return new Promise((resolve) => {
              new Api(`/pms/questionReplyManagement/${form.record[index].id}`).fetch('', '', 'DELETE').then(() => {
                form.record.splice(index, 1);
                resolve(true);
              }).catch(() => {
                resolve('');
              });
            });
          },
        });
      } else {
        form.record.splice(index, 1);
      }
    };

    const handleSubmit = async () => {
      if (submitLoading.value) return;

      const res = await formRef.value.validate();
      if (res) {
        submitLoading.value = true;

        let params = [];
        form.record.map((item) => {
          if (!item.id) {
            params.push({
              reply: item.value,
              questionNumber: props.data.number,
            });
          }
        });
        // questionNumber
        const addRes = await new Api('/pms/questionReplyManagement/addList').fetch(
          params,
          '',
          'POST',
        );
        if (addRes) {
          message.success('添加成功');
          context.emit('update');
          handleClose();
        }
        submitLoading.value = false;
      }
    };

    const handleClose = () => {
      relationList.value = [];
      values.value = [];
      formRef.value.resetFields();
      closeDrawer();
      context.emit('close');
    };

    // 删除记录
    async function handleDelRecord(val) {
      Modal.confirm({
        title: '删除确认提示？',
        content: '请确认是否删除该项目，删除后不可恢复？',
        onOk() {
          return new Promise((resolve) => {
            new Api('/pms/schemeContent').fetch([val.id], '', 'delete').then(() => {
              getPlanDetail();
              resolve(true);
            }).catch(() => {
              resolve('');
            });
          });
        },
      });
    }

    return {
      relationList,
      values,
      addNewData,
      deleteItem,
      handleSubmit,
      handleClose,
      detailData,
      form,
      formRef,
      recordRule,
      dayjs,
      modalRegister,
      submitLoading,
      handleDelRecord,
      useUserStore,
    };
  },
});
</script>

<style lang="less" scoped>
.drawer-body {
  padding: 22px;
}

.title {
  font-size: 14px;
  font-weight: bold;
}

.add-btn {
  margin-top: 35px;
  bottom: 10px;
}

.col {
  display: flex;
  align-items: center;
  height: 40px;
}

.label {
  min-width: 100px;
}

.label,
.value {
  color: ~`getPrefixVar('primary-10') `;
}

.record-content {
  margin-bottom: 10px;
  display: flex;

  .time {
    flex-shrink: 0;
    width: 160px;
    color: ~`getPrefixVar('text-color-second') `;
  }

  .content {
    color: ~`getPrefixVar('text-color') `;
    line-height: 20px;
    flex-grow: 1;
    width: 0;
  }

  .del-btn {
    margin-left: 30px;
    flex-shrink: 0;
  }

}

.flex {
  display: flex;
  align-items: center;

  .ant-input {
    flex: 1;
  }

  .action-btn {
    width: 50px;
    margin-left: 6px;
    text-align: center;
  }
}

.flex-right {
  display: flex;
  justify-content: right;
}
</style>
