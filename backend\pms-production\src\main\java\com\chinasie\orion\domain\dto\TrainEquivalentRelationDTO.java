package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * TrainEquivalentRelation DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-04 17:09:19
 */
@ApiModel(value = "TrainEquivalentRelationDTO对象", description = "员工培训等效关联表")
@Data
@Deprecated
public class TrainEquivalentRelationDTO extends  ObjectDTO   implements Serializable{

    /**
     * 等效认证Id
     */
    @ApiModelProperty(value = "等效认证Id")
    @ExcelProperty(value = "等效认证Id ", index = 0)
    private String equivalentId;

    /**
     * 培训人员Id
     */
    @ApiModelProperty(value = "培训人员Id")
    @ExcelProperty(value = "培训人员Id ", index = 1)
    private String trainPersonId;




}
