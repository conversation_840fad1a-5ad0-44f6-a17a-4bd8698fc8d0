package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.MajorRepairPlanMeterReduceDTO;
import com.chinasie.orion.domain.dto.excel.MajorRepairExportDTO;
import com.chinasie.orion.domain.vo.MajorRepairPlanMeterReduceVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.MajorRepairPlanMeterReduceService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/12/18:19
 * @description:
 */

@RestController
@RequestMapping("/major-repair-plan-meter-reduce")
@Api(tags = "大修计划集体剂量降低")
public class  MajorRepairPlanMeterReduceController  {

    @Autowired
    private MajorRepairPlanMeterReduceService majorRepairPlanMeterReduceService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看大修【大修计划集体剂量降低】详情", type = "MajorRepairPlanMeterReduce", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<MajorRepairPlanMeterReduceVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        MajorRepairPlanMeterReduceVO rsp = majorRepairPlanMeterReduceService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param majorRepairPlanMeterReduceDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【大修计划集体剂量降低】数据", type = "MajorRepairPlanMeterReduce", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody MajorRepairPlanMeterReduceDTO majorRepairPlanMeterReduceDTO) throws Exception {
        String rsp =  majorRepairPlanMeterReduceService.create(majorRepairPlanMeterReduceDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param majorRepairPlanMeterReduceDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【大修计划集体剂量降低】数据", type = "MajorRepairPlanMeterReduce", subType = "编辑", bizNo = "{{#majorRepairPlanMeterReduceDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  MajorRepairPlanMeterReduceDTO majorRepairPlanMeterReduceDTO) throws Exception {
        Boolean rsp = majorRepairPlanMeterReduceService.edit(majorRepairPlanMeterReduceDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【大修计划集体剂量降低】数据", type = "MajorRepairPlanMeterReduce", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = majorRepairPlanMeterReduceService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【大修计划集体剂量降低】数据", type = "MajorRepairPlanMeterReduce", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = majorRepairPlanMeterReduceService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【大修计划集体剂量降低】分页数据", type = "MajorRepairPlanMeterReduce", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<MajorRepairPlanMeterReduceVO>> pages(@RequestBody Page<MajorRepairPlanMeterReduceDTO> pageRequest) throws Exception {
        Page<MajorRepairPlanMeterReduceVO> rsp =  majorRepairPlanMeterReduceService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "导出")
    @RequestMapping(value = "/export", method = RequestMethod.POST)
    public void export(@RequestBody MajorRepairExportDTO majorRepairExportDTO, HttpServletResponse response) throws Exception {
        majorRepairPlanMeterReduceService.export(majorRepairExportDTO, response);
    }
}
