package com.chinasie.orion.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.constant.DictConstant;
import com.chinasie.orion.constant.RiskTakeEffectEnum;
import com.chinasie.orion.constant.WarningRoleConstant;
import com.chinasie.orion.domain.dto.ProjectRoleDTO;
import com.chinasie.orion.domain.dto.TakeEffectDTO;
import com.chinasie.orion.domain.dto.WarningSettingDTO;
import com.chinasie.orion.domain.dto.WarningSettingQueryDTO;
import com.chinasie.orion.domain.entity.ProjectRole;
import com.chinasie.orion.domain.entity.WarningSetting;
import com.chinasie.orion.domain.entity.WarningToRole;
import com.chinasie.orion.domain.vo.SimpleVo;
import com.chinasie.orion.domain.vo.WarningSettingVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.WarningSettingRepository;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.service.ProjectRoleService;
import com.chinasie.orion.service.WarningSettingService;
import com.chinasie.orion.service.WarningToRoleService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.chinasie.orion.constant.WarningRoleConstant.CREATOR;
import static com.chinasie.orion.constant.WarningRoleConstant.PRINCIPAL;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/03/14:01
 * @description:
 */
@Service
public class WarningSettingServiceImpl extends OrionBaseServiceImpl<WarningSettingRepository, WarningSetting> implements WarningSettingService {

    @Resource
    private DictBo dictBo;

    @Resource
    private WarningToRoleService warningToRoleService;

    @Resource
    private ProjectRoleService projectRoleService;

    @Override
    public List<SimpleVo> getWarningTypeList() {
        List<DictValueVO> dictValueVOList = dictBo.getDictList(DictConstant.Warning_Type);
        List<SimpleVo> simpleVoList = new ArrayList<>();
        SimpleVo topSimpleVo = new SimpleVo();
        topSimpleVo.setId("0");
        topSimpleVo.setName("全部提醒");
        simpleVoList.add(topSimpleVo);
        dictValueVOList.forEach(o -> {
            SimpleVo simpleVo = new SimpleVo();
            simpleVo.setId(o.getSubDictId());
            simpleVo.setName(o.getDescription());
            simpleVoList.add(simpleVo);
        });
        return simpleVoList;
    }

    @Override
    public List<WarningSettingVO> getWarningSettingList(WarningSettingQueryDTO warningSettingQueryDTO) throws Exception {
        LambdaQueryWrapper<WarningSetting> lambdaQueryWrapper = new LambdaQueryWrapper<>(WarningSetting.class);
        lambdaQueryWrapper.eq(WarningSetting::getProjectId, warningSettingQueryDTO.getProjectId());
        if (!Objects.equals("0", warningSettingQueryDTO.getWarningType())) {
            lambdaQueryWrapper.eq(WarningSetting::getWarningType, warningSettingQueryDTO.getWarningType());
        }
        lambdaQueryWrapper.orderByAsc(WarningSetting::getSort);
        List<WarningSetting> warningSettingList = this.list(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(warningSettingList)) {
            return new ArrayList<>();
        }
        List<WarningSettingVO> warningSettingVOList = BeanCopyUtils.convertListTo(warningSettingList, WarningSettingVO::new);
        Map<String, String> frequencyValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.Warning_Frequency);
        warningSettingVOList.forEach(d -> {
            d.setFrequencyName(frequencyValueToDesMap.get(d.getFrequency()));
            d.setTakeEffectName(RiskTakeEffectEnum.getNameByStatus(d.getTakeEffect()));
            try {
                setContent(d);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        return warningSettingVOList;
    }

    @Override
    public WarningSettingVO getWarningSettingDetail(String id) throws Exception {
        WarningSetting warningSettingDTO = this.getById(id);
        if (Objects.isNull(warningSettingDTO)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        WarningSettingVO warningSettingVO = new WarningSettingVO();
        BeanCopyUtils.copyProperties(warningSettingDTO, warningSettingVO);
        Map<String, String> frequencyValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.Warning_Frequency);
        warningSettingVO.setFrequencyName(frequencyValueToDesMap.get(warningSettingVO.getFrequency()));
        warningSettingVO.setTakeEffectName(RiskTakeEffectEnum.getNameByStatus(warningSettingVO.getTakeEffect()));
        setContent(warningSettingVO);
        return warningSettingVO;

    }

    @Override
    public Boolean editWarningSetting(WarningSettingDTO warningSettingDTO) throws Exception {
        List<String> warningWayList = warningSettingDTO.getWarningWayList();
        if (!CollectionUtils.isEmpty(warningWayList)) {
            StringBuilder warningWay = new StringBuilder();
            for (String str : warningWayList) {
                warningWay.append(str);
                warningWay.append(",");
            }
            warningWay.substring(0, warningWay.length() - 2);
            warningSettingDTO.setWarningWay(warningWay.toString());
        } else {
            warningSettingDTO.setWarningWay("");
        }
        List<String> roleList = warningSettingDTO.getRoleList();
        String id = warningSettingDTO.getId();
        List<WarningToRole> warningToRoleList = warningToRoleService.list(new LambdaQueryWrapper<>(WarningToRole.class).
                eq(WarningToRole::getFromId, id));
        if (!CollectionUtils.isEmpty(warningToRoleList)) {
            warningToRoleService.remove(new LambdaQueryWrapper<>(WarningToRole.class).eq(WarningToRole::getFromId, id));
        }
        if (!CollectionUtils.isEmpty(roleList)) {
            warningToRoleService.saveParamList(id, roleList);
        }
        WarningSetting warningSetting = BeanCopyUtils.convertTo(warningSettingDTO, WarningSetting::new);
        return this.updateById(warningSetting);
    }

    @Override
    public Boolean takeEffectWarningSetting(TakeEffectDTO takeEffectDTO) throws Exception {
        if (CollectionUtils.isEmpty(takeEffectDTO.getIdList()) || (!takeEffectDTO.getTakeEffect().equals(RiskTakeEffectEnum.EFFECT.getStatus()) &&
                !takeEffectDTO.getTakeEffect().equals(RiskTakeEffectEnum.UN_EFFECT.getStatus()))) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS);
        }
        List<WarningSetting> warningSettingDTOList = new ArrayList<>();
        takeEffectDTO.getIdList().forEach(o -> {
            WarningSetting warningSettingDTO = new WarningSetting();
            warningSettingDTO.setId(o);
            warningSettingDTO.setTakeEffect(takeEffectDTO.getTakeEffect());
            warningSettingDTOList.add(warningSettingDTO);
        });
        return this.updateBatchById(warningSettingDTOList);
    }

    private void setContent(WarningSettingVO warningSettingVO) throws Exception {
        Map<String, String> warningWayValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.Warning_Way);
        if (StringUtils.hasText(warningSettingVO.getWarningWay())) {
            String[] strArr = warningSettingVO.getWarningWay().split(",");
            StringBuilder warningWayName = new StringBuilder();
            for (String str : strArr) {
                warningWayName.append(warningWayValueToDesMap.get(str));
                warningWayName.append("、");
            }
            warningSettingVO.setWarningWayName(warningWayName.substring(0, warningWayName.length() - 1));
            warningSettingVO.setWarningWayList(Arrays.asList(strArr));
        }
        List<WarningToRole> warningToRoleList = warningToRoleService.list(new LambdaQueryWrapper<>(WarningToRole.class).
                eq(WarningToRole::getFromId, warningSettingVO.getId()));
        if (!CollectionUtils.isEmpty(warningToRoleList)) {
            List<String> roleList = warningToRoleList.stream().map(WarningToRole::getToId).collect(Collectors.toList());
            List<ProjectRole> projectRoleDTOList = projectRoleService.list(new LambdaQueryWrapper<>(ProjectRole.class)
                    .in(ProjectRole::getId, roleList.toArray()));
            StringBuilder role = new StringBuilder();
            for (ProjectRole projectRoleDTO : projectRoleDTOList) {
                role.append(projectRoleDTO.getName());
                role.append("、");
            }
            if (roleList.contains(CREATOR)) {
                role.append("创建人");
                role.append("、");
            }
            if (roleList.contains(PRINCIPAL)) {
                role.append("负责人");
                role.append("、");
            }
            warningSettingVO.setRoleName(role.substring(0, role.length() - 1));
            warningSettingVO.setRoleList(roleList);
        }

    }


    @Override
    public List<SimpleVo> getProjectRoleList(String projectId) throws Exception {
        List<SimpleVo> simpleVoList = new ArrayList<>();
        SimpleVo simpleVo1 = new SimpleVo();
        simpleVo1.setId(WarningRoleConstant.CREATOR);
        simpleVo1.setName("创建人");
        SimpleVo simpleVo2 = new SimpleVo();
        simpleVo2.setId(WarningRoleConstant.PRINCIPAL);
        simpleVo2.setName("负责人");
        simpleVoList.add(simpleVo1);
        simpleVoList.add(simpleVo2);

        List<ProjectRoleDTO> projectRoleList = projectRoleService.getProjectRoleList(projectId);
        List<SimpleVo> simpleVos = BeanCopyUtils.convertListTo(projectRoleList, SimpleVo::new);
        if (!CollectionUtils.isEmpty(simpleVos)) {
            simpleVoList.addAll(simpleVos);
        }

        return simpleVoList;
    }

    @Override
    public Boolean removeByProjectIds(List<String> projectIds) throws Exception {
        LambdaQueryWrapper<WarningSetting> wrapper = new LambdaQueryWrapper<>(WarningSetting.class);
        wrapper.in(WarningSetting::getProjectId, projectIds.toArray());
        return this.remove(wrapper);
    }
}
