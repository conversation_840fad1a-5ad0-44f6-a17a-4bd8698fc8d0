<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
    @selectionChange="tableSelectionChange"
  >
    <template #toolbarLeft>
      <BasicButton
        type="primary"
        icon="sie-icon-tianjiaxinzeng"
        @click="goAdd"
      >
        新增版本
      </BasicButton>
    </template>
  </OrionTable>
  <!--  <AddOrEditDrawerTable-->
  <!--    ref="addDrawerRef"-->
  <!--    @update="reloadTable()"-->
  <!--  />-->
</template>

<script setup lang="ts">
import { h, reactive, ref } from 'vue';
import {
  OrionTable, BasicButton, DataStatusTag,
} from 'lyra-component-vue3';
import { Modal } from 'ant-design-vue';
import Api from '/@/api';
import { useRouter } from 'vue-router';
// import AddOrEditDrawerTable
//   from './component/AddOrEditDrawerTable/Drawer.vue';

const router = useRouter();
const emits = defineEmits([]);
const props = defineProps({
  selectedKeys: {
    type: Array,
    default: () => [],
  },
});

const state = reactive({
  selectRows: [],
});
const addDrawerRef = ref(null);
const tableRef = ref(null);
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: {},
  smallSearchField: ['name', 'number'],
  showIndexColumn: true,
  showTableSetting: false,
  showSmallSearch: false,
  rowKey: 'id',
  api: async (params) => await new Promise((resolve) => {
    setTimeout(() => {
      resolve([
        {
          name: '测试',
          id: '123',
        },
      ]);
    }, 500);
  }).finally(() => {
    state.selectRows = [];
  }),
  columns: [
    {
      title: '版本名称',
      dataIndex: 'name',
      align: 'left',
      minWidth: 200,
      customRender: ({ record, text }) => h('span', {
        class: 'action-btn',
        onClick: async () => {
          await router.push({
            name: 'icmManagementDetailsIndex',
            query: {
              id: record?.id,
            },
            params: {
              id: record?.id,
            },
          });
        },
      }, text),
    },
    {
      title: '版本',
      align: 'left',
      dataIndex: 'number',
      slots: { customRender: 'workHourType' },
      width: 200,
      sorter: {},
    },
    {
      title: '编号',
      align: 'left',
      dataIndex: 'number',
      slots: { customRender: 'workHourType' },
      width: 200,
      sorter: {},
    },
    {
      title: '版本',
      align: 'left',
      dataIndex: 'number',
      slots: { customRender: 'workHourType' },
      width: 200,
      sorter: {},
    },
    {
      title: '状态',
      align: 'left',
      dataIndex: 'number',
      slots: { customRender: 'workHourType' },
      width: 200,
      sorter: {},
    },
    {
      title: '修改人',
      align: 'left',
      dataIndex: 'number',
      slots: { customRender: 'workHourType' },
      width: 200,
      sorter: {},
    },
    {
      title: '修改日期',
      align: 'left',
      dataIndex: 'number',
      slots: { customRender: 'workHourType' },
      width: 200,
      sorter: {},
    },
    {
      title: '操作',
      align: 'left',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 200,
      sorter: {},
    },
  ],
  actions: [
    {
      text: '设为默认',
      isShow: (record: Record<string, any>) => true,
      modal(record: Record<string, any>) {
        return new Api('/pms').fetch([record.id], '', 'DELETE').then(() => {
          reloadTable();
        });
      },
    },
    {
      text: '删除',
      isShow: (record: Record<string, any>) => true,
      modal(record: Record<string, any>) {
        return new Api('/pms').fetch([record.id], '', 'DELETE').then(() => {
          reloadTable();
        });
      },
    },
  ],
});

// 删除
function goDelete() {
  Modal.confirm({
    title: '删除确认提示',
    content: '请确认是否删除这些信息？',
    onOk() {
      return new Api('').fetch('', '', 'DELETE').then(() => {
        reloadTable();
      });
    },
    onCancel() {
      Modal.destroyAll();
    },
  });
}

function reloadTable() {
  tableRef.value && tableRef.value.reload({ page: 1 });
}

function tableSelectionChange({ rows }) {
  state.selectRows = rows;
}

// 新增
function goAdd() {
  addDrawerRef.value.openDrawer({
    action: 'add',
    info: {},
  });
}
</script>

<style scoped lang="less"></style>
