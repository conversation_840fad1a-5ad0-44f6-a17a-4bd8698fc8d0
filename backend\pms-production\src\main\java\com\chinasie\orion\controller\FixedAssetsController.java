package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.FixedAssetsFileDTO;
import com.chinasie.orion.domain.vo.FixedAssetsVO;
import com.chinasie.orion.service.FixedAssetsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.domain.entity.FixedAssets;
import com.chinasie.orion.domain.dto.FixedAssetsDTO;
import com.chinasie.orion.domain.vo.FixedAssetsVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * FixedAssets 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 14:36:24
 */
@RestController
@RequestMapping("/fixed-assets")
@Api(tags = "固定资产能力库")
public class FixedAssetsController {

    @Autowired
    private FixedAssetsService fixedAssetsService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【固定资产】详情", type = "JobManageTree", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<FixedAssetsVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        FixedAssetsVO rsp = fixedAssetsService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 详情
     * @param number
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "通过编码获取固定资产")
    @RequestMapping(value = "/detail/number/{number}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【固定资产】-【{{#number}}】详情", type = "JobManageTree", subType = "详情", bizNo = "{{#number}}")
    public ResponseDTO<FixedAssetsVO> detailByNumber(@PathVariable(value = "number") String number) throws Exception {
        FixedAssetsVO rsp = fixedAssetsService.detailByNumber(number);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 新增
     *
     * @param fixedAssetsDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【固定资产】数据【{{#fixedAssetsDTO.number}}】-【{{#fixedAssetsDTO.name}}】", type = "FixedAssets", subType = "新增", bizNo = "")
    public ResponseDTO<String> create(@RequestBody FixedAssetsDTO fixedAssetsDTO) throws Exception {
        String rsp =  fixedAssetsService.create(fixedAssetsDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param fixedAssetsDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【固定资产】数据【{{#fixedAssetsDTO.number}}】-【{{#fixedAssetsDTO.name}}】", type = "FixedAssets", subType = "编辑", bizNo = "{{#fixedAssetsDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  FixedAssetsDTO fixedAssetsDTO) throws Exception {
        Boolean rsp = fixedAssetsService.edit(fixedAssetsDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【固定资产】数据", type = "FixedAssets", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = fixedAssetsService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【固定资产】数据", type = "FixedAssets", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = fixedAssetsService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【固定资产】分页数据", type = "FixedAssets", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<FixedAssetsVO>> pages(@RequestBody Page<FixedAssetsDTO> pageRequest) throws Exception {
        Page<FixedAssetsVO> rsp =  fixedAssetsService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 列表修改产品编码
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "列表修改产品编码")
    @RequestMapping(value = "/editById", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【固定资产】数据修改产品编码【{{#fixedAssetsDTO.number}}】-【{{#fixedAssetsDTO.name}}】", type = "FixedAssets", subType = "列表修改产品编码", bizNo = "{{#fixedAssetsDTO.id}}")
    public ResponseDTO<Boolean> editById(@RequestBody  FixedAssetsDTO fixedAssetsDTO) throws Exception {
        Boolean rsp = fixedAssetsService.edit(fixedAssetsDTO);
        return new ResponseDTO<>(rsp);
    }



    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页-过滤作业下的物资-固定资产")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【固定资产】过滤作业下的物资数据", type = "FixedAssets", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/material/page", method = RequestMethod.POST)
    public ResponseDTO<Page<FixedAssetsVO>> materialPages(@RequestBody Page<FixedAssetsDTO> pageRequest) throws Exception {
        Page<FixedAssetsVO> rsp =  fixedAssetsService.materialPages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("固定资产能力库导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载【固定资产】导入模板", type = "FixedAssets", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        fixedAssetsService.downloadExcelTpl(response);
    }

    @ApiOperation("固定资产能力库导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验【固定资产】导入", type = "FixedAssets", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = fixedAssetsService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("固定资产能力库导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认【固定资产】导入", type = "FixedAssets", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  fixedAssetsService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消固定资产能力库导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消【固定资产】导入", type = "FixedAssets", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  fixedAssetsService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("固定资产能力库导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出【固定资产】数据", type = "FixedAssets", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        fixedAssetsService.exportByExcel(searchConditions, response);
    }

    @ApiOperation("固定资产上传附件")
    @PutMapping(value = "/upload/fixedAssets")
    @LogRecord(success = "【{USER{#logUserId}}】上传【固定资产】附件", type = "FixedAssets", subType = "上传附件", bizNo = "")
    public ResponseDTO<Boolean> uploadFixedAssets(@RequestBody FixedAssetsFileDTO fixedAssetsFileDTO) throws Exception {
        Boolean rsp = fixedAssetsService.uploadFixedAssets(fixedAssetsFileDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("固定资产附件删除")
    @DeleteMapping(value = "")
    @LogRecord(success = "【{USER{#logUserId}}】删除【固定资产】附件{{#fileIds}}", type = "FixedAssets", subType = "删除附件", bizNo = "{{#fileIds}}")
    public ResponseDTO<Boolean> deleteFixedAssetsFile(@RequestBody List<String> fileIds) throws Exception {
        Boolean rsp = fixedAssetsService.deleteFixedAssetsFile(fileIds);
        return new ResponseDTO<>(rsp);
    }
}
