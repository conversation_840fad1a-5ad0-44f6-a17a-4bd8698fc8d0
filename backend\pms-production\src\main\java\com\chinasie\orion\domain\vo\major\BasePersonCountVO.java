package com.chinasie.orion.domain.vo.major;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/08/19/10:44
 * @description:
 */
@Data
public class BasePersonCountVO implements Serializable {

    @ApiModelProperty(value = "人员总数")
    private Integer personTotal=0;
    @ApiModelProperty(value = "年龄大于50的总数")
    private Integer ageCompareFifthTotal=0;
    @ApiModelProperty(value = "当天入场人数")
    private Integer todayInTotal=0;
    @ApiModelProperty(value = "当天离场人数")
    private Integer todayOutTotal=0;
}
