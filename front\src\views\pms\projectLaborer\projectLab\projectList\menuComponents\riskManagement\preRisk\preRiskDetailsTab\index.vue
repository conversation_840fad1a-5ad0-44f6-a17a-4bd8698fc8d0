<template>
  <Layout3
    v-if="tabsOption?.length>0"
    :defaultActionId="actionId"
    :menuData="tabsOption"
    :projectData="projectInfo"
    :type="2"
    @menuChange="contentTabsChange2"
  >
    <template #header-title>
      <div class="layoutTtitle">
        <div class="nameStyle flex-te">
          {{ projectInfo.name }}
        </div>
        <div class="numberStyle">
          {{ projectInfo?.number }}
        </div>
      </div>
    </template>
    <template #header-info>
      <div class="layout-right-title">
        {{ projectInfo.ownerName }}
      </div>
    </template>
    <template #tabsRight>
      <RemoveBtn />
    </template>
    <div
      v-if="actionId===22331 && isPower('YAFX_container_02', powerData)"
      class="contentTabs"
    >
      <!--  概述  -->
      <PreRiskDetailsTabs
        :id="id"
      />
    </div>
    <!-- 关联内容 -->
    <ContactDoc
      v-if="actionId===22332 && isPower('YAFX_container_03', powerData)"
      :id="id"
    />
  </Layout3>
</template>

<script lang="ts">
import {
  defineComponent, reactive, toRefs, provide, readonly, ref, inject, onMounted, getCurrentInstance, computed,
} from 'vue';
import {
  Layout3, isPower, useProjectPower,
} from 'lyra-component-vue3';
// import { Layout2 } from '/@/components/Layout2.0';
import PreRiskDetailsTabs from './details/index.vue';
import ContactDoc from './contactDoc/index.vue';
import { useRoute } from 'vue-router';
import RemoveBtn from '/@/views/pms/projectLaborer/componentsList/returnBtn/index.vue';
import { getPreRiskItemDetailsApi } from '/@/views/pms/projectLaborer/api/riskManege';
import { setTitleByRootTabsKey } from '/@/utils';

export default defineComponent({
  components: {
    Layout3,
    ContactDoc,
    PreRiskDetailsTabs,
    RemoveBtn,
  },
  setup() {
    const route = useRoute();
    const state = reactive({
      tabsIndex: Number(route.query.type),
      id: route.query.id,
      actionId: 22331,
      projectInfo: {},
      projectId: route.query.projectId,
      className: '',
      // tabsOption: [
      //   { name: '概述' }, // 0
      //   { name: '关联文件' }, // 1
      //   // { name: '日志' }, // 2
      // ],
      powerData: [],
    });
    const state6 = reactive({
      tabsOption: [],
    });
    const internalInstance = getCurrentInstance();
    // 获取权限
    async function getProjectPower() {
      return new Promise((resolve, reject) => {
        useProjectPower(
          { pageCode: 'PMS0023' },
          (powerList) => {
            resolve(powerList || []);
          },
          internalInstance,
        );
      });
    }
    /* 获取详情 */
    const getDetail = () => {
      const love = {
        id: state?.id,
        className: 'RiskPlan',
        moduleName: '项目管理-风险管理-风险预案',
        type: 'GET',
        remark: `打开了【${state?.id}】风险预案详情`,
      };
      getPreRiskItemDetailsApi(state.id, love)
        .then((res) => {
          if (res) {
            state.projectInfo = { ...res };
            setTitleByRootTabsKey(route?.query?.rootTabsKey, res.name);
          }
        })
        .catch(() => {});
    };
    onMounted(async () => {
      state.powerData = await getProjectPower();
      isPower('YAFX_container_02', state.powerData) && state6.tabsOption.push({
        name: '概述',
        id: 22331,
      });
      isPower('YAFX_container_03', state.powerData) && state6.tabsOption.push({
        name: '关联文件',
        id: 22332,
      });
      await getDetail();
    });
    function contentTabsChange2(index) {
      state.actionId = index.id;
    }
    const preRiskItemId = ref(state.id);
    const projectId = ref(state.projectId);
    provide('preRiskItemId', readonly(preRiskItemId));
    provide('projectId', readonly(projectId));
    // 权限分发
    provide(
      'powerData',
      computed(() => state.powerData),
    );
    // 数据
    provide(
      'formData',
      computed(() => state.projectInfo),
    );
    // 数据查询
    provide(
      'getFormData',
      computed(() => getDetail),
    );
    return {
      ...toRefs(state),
      ...toRefs(state6),
      isPower,
      contentTabsChange2,
    };
  },
});
</script>
<style lang="less">
.contentTabs{
  display: flex;
  height: 100%;
  width: 100%;
}
.layoutTtitle{
  width: 350px;
  padding: 5px ~`getPrefixVar('content-padding-left')`;
  .nameStyle{
    font-weight: 400;
    font-style: normal;
    color: #444B5E;
    font-size: 18px;
    height:29px;
    line-height: 29px;
  }
  .numberStyle{
    font-size: 12px;
    color: #969EB4;
  }
}
.layout-right-title{
  font-size: 14px;
  font-weight: 700;
}
</style>
