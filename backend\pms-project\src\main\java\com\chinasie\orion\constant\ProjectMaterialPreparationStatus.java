package com.chinasie.orion.constant;


public enum ProjectMaterialPreparationStatus {
    CREATE(100, "已创建"),
    FLOWING(111, "流程中"),
    AUDITED(107, "已生效");
    private final Integer code;
    private final String value;

    ProjectMaterialPreparationStatus(Integer code, String value) {
        this.value = value;
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
