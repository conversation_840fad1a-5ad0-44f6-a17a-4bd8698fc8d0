package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.util.Date;
import java.lang.Boolean;
import java.lang.String;
import java.lang.Integer;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * MaterialManage DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-04 17:08:48
 */
@ApiModel(value = "MaterialManageDTO对象", description = "物资库")
@Data
@ExcelIgnoreUnannotated
public class MaterialManageDTO extends  ObjectDTO   implements Serializable{

    /**
     * 资产类型
     */
    @ApiModelProperty(value = "资产类型")
    @ExcelProperty(value = "资产类型 ", index = 0)
    private String assetType;

    /**
     * 资产代码
     */
    @ApiModelProperty(value = "资产代码")
    @ExcelProperty(value = "资产代码 ", index = 1)
    private String assetCode;

    /**
     * 资产编码
     */
    @ApiModelProperty(value = "资产编码")
    @ExcelProperty(value = "资产编码 ", index = 2)
    private String number;

    /**
     * 资产名称
     */
    @ApiModelProperty(value = "资产名称")
    @ExcelProperty(value = "资产名称 ", index = 3)
    private String assetName;

    /**
     * 成本中心
     */
    @ApiModelProperty(value = "成本中心")
    @ExcelProperty(value = "成本中心 ", index = 4)
    private String costCenterName;

    /**
     * 规格型号
     */
    @ApiModelProperty(value = "规格型号")
    @ExcelProperty(value = "规格型号 ", index = 5)
    private String specificationModel;

    /**
     * 库存数量
     */
    @ApiModelProperty(value = "库存数量")
    @ExcelProperty(value = "库存数量 ", index = 6)
    private Integer stockNum;

    /**
     * 下次检定日期
     */
    @ApiModelProperty(value = "下次检定日期")
    @ExcelProperty(value = "下次检定日期 ", index = 7)
    private Date nextVerificationDate;

    /**
     * 是否需要检定
     */
    @ApiModelProperty(value = "是否需要检定")
    @ExcelProperty(value = "是否需要检定 ", index = 8)
    private Boolean isVerification;

    /**
     * 物质所在基地
     */
    @ApiModelProperty(value = "物质所在基地")
    @ExcelProperty(value = "物质所在基地 ", index = 9)
    private String baseId;
    @ApiModelProperty(value = "物质所在基地编号")
    private String baseCode;

    /**
     * 责任人工号
     */
    @ApiModelProperty(value = "责任人工号")
    @ExcelProperty(value = "责任人工号 ", index = 10)
    private String rspUserNo;

    /**
     * 责任人名称
     */
    @ApiModelProperty(value = "责任人名称")
    @ExcelProperty(value = "责任人名称 ", index = 11)
    private String rspUserName;

    /**
     * 使用人工号
     */
    @ApiModelProperty(value = "使用人工号")
    @ExcelProperty(value = "使用人工号 ", index = 12)
    private String useUserNo;

    /**
     * 使用人名称
     */
    @ApiModelProperty(value = "使用人名称")
    @ExcelProperty(value = "使用人名称 ", index = 13)
    private String useUserName;

    /**
     * 资产入库日期
     */
    @ApiModelProperty(value = "资产入库日期")
    @ExcelProperty(value = "资产入库日期 ", index = 14)
    @DateTimeFormat(value = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date enterDate;

    /**
     * 是否计量器具
     */
    @ApiModelProperty(value = "是否计量器具")
    @ExcelProperty(value = "是否计量器具 ", index = 15)
    private Boolean isMetering;

    /**
     * 是否向电厂报备
     */
    @ApiModelProperty(value = "是否向电厂报备")
    @ExcelProperty(value = "是否向电厂报备 ", index = 16)
    private Boolean isReport;

    /**
     * 检定是否超期
     */
    @ApiModelProperty(value = "检定是否超期")
    @ExcelProperty(value = "检定是否超期 ", index = 17)
    private Boolean isOverdue;

    /**
     * 物质应用作业(工单号)
     */
    @ApiModelProperty(value = "物质应用作业(工单号)")
    @ExcelProperty(value = "物质应用作业(工单号) ", index = 18)
    private String jobNo;

    /**
     * 作业名称
     */
    @ApiModelProperty(value = "作业名称")
    @ExcelProperty(value = "作业名称 ", index = 19)
    private String jobName;

    @ApiModelProperty(value = "物资应用作业ID")
    private String jobId;


    @ApiModelProperty(value = "成本中心")
    private String costCenter;

    @ApiModelProperty(value = "入库数量")
    private Integer inputStockNum;

    @ApiModelProperty(value = "实际入场日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actInDate;

    @ApiModelProperty(value = "实际离场日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actOutDate;

    @ApiModelProperty(value = "计划入场日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date inDate;

    @ApiModelProperty(value = "计划离场日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date outDate;


    @ApiModelProperty(value = "进场倒计时（天）")
    private long inDays;

    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    private Integer demandNum;

    @ApiModelProperty(value = "产品编码")
    private String productCode;

    /**
     * 工具状态
     */
    @ApiModelProperty(value = "工具状态")
    private String toolStatus;

    /**
     * 检定维护周期
     */
    @ApiModelProperty(value = "检定维护周期")
    private Integer maintenanceCycle;

    @ApiModelProperty(value = "资产所在地")
    private String storagePlaceName;
}
