package com.chinasie.orion.msc.commonhandler;

import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.domain.entity.JobManage;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;

import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>  任务消息构建公共方法
 */
public class JobCommonHandler {

    public static SendMessageDTO buildMsc(JobManage jobManage, Object... objects) {
        Map<String,Object> messageMap = new HashMap<>();
        messageMap.put("$workName$",jobManage.getProjectName());
        messageMap.put("$workTitle$",jobManage.getName());

        SendMessageDTO sendMsc = SendMessageDTO.builder()
                .businessId(jobManage.getId())
                .todoStatus(0)
                .messageUrl("/pms/majorRepairsWork/"+jobManage.getId())
                .messageUrlName("作业详情")
                .todoType(TodoTypeDict.TODO_TYPE_TASK)
                .recipientIdList(Collections.singletonList(jobManage.getRspUserId()))
                .messageMap(messageMap)
                .titleMap(messageMap)
                .senderTime(new Date())
                .senderId(jobManage.getCreatorId())
                .platformId(jobManage.getPlatformId())
                .orgId(jobManage.getOrgId())
                .build();

        return sendMsc;
    }


}
