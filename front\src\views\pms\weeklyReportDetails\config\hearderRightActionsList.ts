import Api from '/@/api';
import { reactive } from 'vue';

const isShowbtn = reactive({
  isEdit: '',
  isCommit: '',
  isAudit: '',
});

export function getHeaderRightActionsList() {
  return [
    {
      event: 'edit',
      text: '编辑',
      icon: 'sie-icon-bianji',
      isShow: (data) => data?.edit,
    },
    {
      event: 'approve',
      text: '审核',
      icon: '',
      isShow: (data) => data?.audit,
    },
    {
      event: 'commit',
      text: '提交',
      icon: '',
      isShow: (data) => data?.commit,
    },
  ];
}
