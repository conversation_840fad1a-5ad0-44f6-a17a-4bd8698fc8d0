package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.CostCenterDTO;
import com.chinasie.orion.domain.vo.CostCenterVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.CostCenterService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.lang.Exception;
import java.lang.String;

/**
 * <p>
 * CostCenter 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16 13:29:31
 */
@RestController
@RequestMapping("/costCenter")
@Api(tags = "成本中心类")
public class CostCenterController {

    @Autowired
    private CostCenterService costCenterService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查看【成本中心类】 【{{#number}}】-【{{#name}}】详情", type = "CostCenter", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<CostCenterVO> detail(@PathVariable(value = "id") String id) throws Exception {
        CostCenterVO rsp = costCenterService.detail(id);
        LogRecordContext.putVariable("number", rsp.getNumber());
        LogRecordContext.putVariable("name", rsp.getName());
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param costCenterDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】新增【成本中心类】 【{{#number}}】-【{{#name}}】数据", type = "CostCenter", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<CostCenterVO> create(@RequestBody CostCenterDTO costCenterDTO) throws Exception {
        CostCenterVO rsp = costCenterService.create(costCenterDTO);
        LogRecordContext.putVariable("number", costCenterDTO.getNumber());
        LogRecordContext.putVariable("name", costCenterDTO.getName());
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param costCenterDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【成本中心类】 【{{#number}}】-【{{#name}}】数据", type = "CostCenter", subType = "编辑", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> edit(@RequestBody CostCenterDTO costCenterDTO) throws Exception {
        Boolean rsp = costCenterService.edit(costCenterDTO);
        LogRecordContext.putVariable("number", costCenterDTO.getNumber());
        LogRecordContext.putVariable("name", costCenterDTO.getName());
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【成本中心类】 【{{#number}}】-【{{#name}}】数据", type = "CostCenter", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable(value = "id") String id) throws Exception {
        Boolean rsp = costCenterService.removeById(id);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【成本中心类】 分页数据", type = "CostCenter", subType = "分页查询", bizNo = "")
    public ResponseDTO<Page<CostCenterVO>> pages(@RequestBody Page<CostCenterDTO> pageRequest) throws Exception {
        Page<CostCenterVO> rsp = costCenterService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "启用")
    @RequestMapping(value = "/use/{id}", method = RequestMethod.PUT)
    @LogRecord(success = "【{USER{#logUserId}}】启用【成本中心类】 【{{#number}}】-【{{#name}}】数据", type = "CostCenter", subType = "启用", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> use(@PathVariable("id") String id) throws Exception {
        Boolean rsp = costCenterService.use(id);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "禁用")
    @RequestMapping(value = "/ban/{id}", method = RequestMethod.PUT)
    public ResponseDTO<Boolean> ban(@PathVariable("id") String id) throws Exception {
        Boolean rsp = costCenterService.ban(id);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "列表")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【成本中心类】 列表数据", type = "CostCenter", subType = "列表", bizNo = "")
    public ResponseDTO<List<CostCenterVO>> detail(@RequestBody CostCenterDTO costCenterDTO) throws Exception {
        List<CostCenterVO> rsp = costCenterService.getCostCenterList(costCenterDTO);
        return new ResponseDTO<List<CostCenterVO>>(rsp);
    }
}

