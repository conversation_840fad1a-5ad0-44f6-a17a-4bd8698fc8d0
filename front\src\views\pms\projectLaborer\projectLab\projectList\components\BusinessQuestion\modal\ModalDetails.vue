<template>
  <BasicDrawer
    :width="1000"
    wrap-class-name="modalMaterialDetails checkDrawer"
    title="查看基本信息"
    @register="modalRegister"
  >
    <div
      v-loading="loading"
      class="modalDetails_main"
    >
      <div class="modalDetails_content">
        <div class="viewTitle">
          <div class="rowItem titleLabel">
            {{ formData.name }}
          </div>
          <div class="rowItem">
            <div class="rowCell">
              <div class="rowCell_icon icon_user">
                <i class="orion-icon-user" />
              </div>
              <div class="rowCell_val">
                <div class="val_top">
                  {{ formData.creatorName }}
                </div>
                <div class="val_bot">
                  创建人
                </div>
              </div>
            </div>
            <div class="rowCell">
              <div class="rowCell_icon icon_calendar">
                <i class="orion-icon-calendar" />
              </div>
              <div class="rowCell_val">
                <div class="val_top">
                  {{ stampDate(formData.modifyTime) }}
                </div>
                <div class="val_bot">
                  提出时间
                </div>
              </div>
            </div>
            <div class="rowCell">
              <div class="rowCell_icon icon_calendar">
                <i class="orion-icon-hourglass" />
              </div>
              <div class="rowCell_val">
                <div class="val_top">
                  <DataStatusTag :status-data="formData.dataStatus" />
                </div>
                <div class="val_bot">
                  状态
                </div>
              </div>
            </div>

            <div class="rowCell">
              <div class="rowCell_icon icon_calendar">
                <i class="orion-icon-branches" />
              </div>
              <div class="rowCell_val">
                <div class="val_top">
                  {{ formData.seriousLevelName }}
                </div>
                <div class="val_bot">
                  严重程度
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <BasicTabs
        v-model:tabsIndex="tabsIndex"
        :tabs="tabs"
        @tabsChange="tabsChange"
      />
      <div class="modalDetailsPage">
        <Information
          v-if="tabKey==='information'"
          page-type="modal"
        />
        <RelatedObjects
          v-if="tabKey==='relatedObjects'"
          page-type="modal"
          :relatedType="formData.questionType"
        />
        <Process
          v-if="tabKey==='process'"
          page-type="modal"
        />
      </div>
    </div>
  </BasicDrawer>
</template>
<script lang="ts">
import {
  defineComponent, onMounted, reactive, toRefs, computed, nextTick, provide,
} from 'vue';
import {
  BasicDrawer, useDrawerInner, BasicTabs, DataStatusTag,
} from 'lyra-component-vue3';
import { stampDate } from '/@/utils/dateUtil';
import Api from '/@/api';
import Information from './Information.vue';
import { RelatedObjects } from '/@/views/pms/projectLaborer/projectLab/projectList/components/RelatedObjects';
import Process from './Process.vue';
export default defineComponent({
  name: 'ModalDetails',
  components: {
    BasicDrawer,
    BasicTabs,
    DataStatusTag,
    Information,
    RelatedObjects,
    Process,
  },
  setup(_, { emit }) {
    const state:any = reactive({
      formData: {},
      formId: '',
      showTabs: true,
      loading: false,
      tabsIndex: 0,
      tabKey: 'information',
      tabs: [
        {
          name: '基本信息',
          key: 'information',
        },
        {
          name: '相关对象',
          key: 'relatedObjects',
        },
        {
          name: '流程',
          key: 'process',
        },
      ],
    });
    const [modalRegister, { closeDrawer, setDrawerProps, changeLoading }] = useDrawerInner((drawerData) => {
      getFormData(drawerData.id);// 46r09a5d21f551f54003b301a5130d643381
    });
    function getFormData(id) {
      state.loading = true;
      new Api(`/pas/question-management/detail/${id}`).fetch('', '', 'GET').then((res) => {
        state.loading = false;
        state.tabsIndex = 0;
        state.tabKey = 'information';
        if (Array.isArray(res.typeAttrValueDTOList)) {
          res.typeAttrValueDTOList.forEach((item) => {
            res[item.attributeId] = item.value;
          });
        }
        state.formData = res;
      }).catch((err) => {
        state.loading = false;
      });
    }
    // 权限分发
    provide(
      'formData',
      computed(() => state.formData),
    );
    const tabsChange = (index, item) => {
      state.tabKey = item.key;
    };

    return {
      ...toRefs(state),
      modalRegister,
      stampDate,
      tabsChange,
    };
  },
});

</script>
<style lang="less">
.modalMaterialDetails{
  .ant-drawer-body{
    padding:0px;
    .scrollbar__view{
      height: 100%;
    }
  }
  .modalDetails_main{
    display: flex;
    height: 100%;
    flex-direction: column;
    .modalDetails_content{
      padding: 10px;
      .fa {
        font-family: 'FontAwesome';
      }
      .viewTitle {
        * {
          font-family: 'MicrosoftYaHei-Bold', '微软雅黑 Bold', '微软雅黑';
        }
        padding-bottom: 20px;
        border-bottom: 1px dashed #e4e4e7;
        .titleLabel {
          font-weight: 700;
          font-style: normal;
          font-size: 20px;
          color: #000000;
          height: 60px;
          line-height: 60px;
        }
        .rowItem {
          display: flex;
          .rowCell {
            display: flex;
            width: 250px;
            .rowCell_icon {
              height: 40px;
              width: 40px;
              line-height: 40px;
              border-radius: 20px;
              text-align: center;
              font-size: 20px;
              font-weight: bold;
              margin-right: 5px;
            }
            .icon_user {
              background: #6e72fb;
              color: #ffffff;
            }
            .icon_calendar {
              background: #f1f4fd;
              color: #5678dd;
            }
            .rowCell_val {
              .val_top {
                font-weight: 500;
                font-style: normal;
                font-size: 16px;
                height: 25px;
              }
              .val_bot {
                font-weight: 400;
                font-style: normal;
                font-size: 12px;
                color: #686f8b;
              }
            }
          }
        }
      }
    }

    .modalDetailsPage{
      flex: 1;
    }
  }
  .tabs-main{
    padding: 0px 10px;
  }
}

</style>
