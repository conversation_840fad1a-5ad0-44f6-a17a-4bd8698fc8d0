package com.chinasie.orion.domain.entity.approval;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;
import java.lang.Integer;

/**
 * ProjectApprovalEstimate Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-07 10:45:22
 */
@TableName(value = "pms_project_approval_estimate")
@ApiModel(value = "ProjectApprovalEstimateEntity对象", description = "概算")
@Data
public class ProjectApprovalEstimate extends ObjectEntity implements Serializable{

    /**
     * 项目立项id
     */
    @ApiModelProperty(value = "项目立项id")
    @TableField(value = "project_approval_id")
    private String projectApprovalId;

    /**
     * 材料费概算
     */
    @ApiModelProperty(value = "材料费概算")
    @TableField(value = "material_fee")
    private BigDecimal materialFee;

    /**
     * 人天数
     */
    @ApiModelProperty(value = "人天数")
    @TableField(value = "people_num")
    private BigDecimal peopleNum;

    /**
     * 工资及劳务费
     */
    @ApiModelProperty(value = "工资及劳务费")
    @TableField(value = "labor_fee")
    private BigDecimal laborFee;

    /**
     * 内部试验费
     */
    @ApiModelProperty(value = "内部试验费")
    @TableField(value = "inter_trial_fee")
    private BigDecimal interTrialFee;

    /**
     * 外部试验费
     */
    @ApiModelProperty(value = "外部试验费")
    @TableField(value = "out_trial_fee")
    private BigDecimal outTrialFee;

}
