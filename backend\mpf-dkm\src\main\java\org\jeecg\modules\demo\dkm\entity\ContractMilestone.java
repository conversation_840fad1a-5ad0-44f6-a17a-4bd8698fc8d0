package org.jeecg.modules.demo.dkm.entity;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

/**
 * @Description: 合同里程碑实体类
 * @Author: tancheng
 * @Date: 2025-5-15
 * @Version: V1.0
 */
@Data
@TableName("pms_contract_milestone")
public class ContractMilestone {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    
    /**
     * 里程碑名称
     */
    private String milestoneName;
    
    /**
     * 里程碑编号
     */
    private String number;
    
    /**
     * 合同ID(外键)
     */
    private String contractId;
    
    /**
     * 合同名称(非数据库字段，用于显示)
     */
    @TableField(exist = false)
    private String contractName;
} 