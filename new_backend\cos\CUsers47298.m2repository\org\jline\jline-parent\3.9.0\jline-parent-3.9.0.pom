<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright (c) 2002-2018, the original author or authors.

    This software is distributable under the BSD license. See the terms of the
    BSD license in the documentation provided with this software.

    http://www.opensource.org/licenses/bsd-license.php

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.sonatype.oss</groupId>
        <artifactId>oss-parent</artifactId>
        <version>9</version>
    </parent>

    <groupId>org.jline</groupId>
    <artifactId>jline-parent</artifactId>
    <name>JLine Parent</name>
    <description>JLine</description>
    <version>3.9.0</version>
    <packaging>pom</packaging>

    <licenses>
        <license>
            <name>The BSD License</name>
            <url>http://www.opensource.org/licenses/bsd-license.php</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <scm>
        <connection>scm:git:git://github.com/jline/jline3.git</connection>
        <developerConnection>scm:git:ssh://**************/jline/jline3.git</developerConnection>
        <url>http://github.com/jline/jline3</url>
    <tag>jline-parent-3.9.0</tag>
  </scm>

    <issueManagement>
        <system>github</system>
        <url>https://github.com/jline/jline3/issues</url>
    </issueManagement>

    <ciManagement>
        <system>travis</system>
        <url>https://travis-ci.org/jline/jline3</url>
    </ciManagement>

    <mailingLists>
        <mailingList>
            <name>jline-users</name>
            <post>mailto:<EMAIL></post>
            <unsubscribe>mailto:<EMAIL></unsubscribe>
            <subscribe>https://groups.google.com/group/jline-users</subscribe>
            <archive>https://groups.google.com/group/jline-users</archive>
        </mailingList>
    </mailingLists>

    <developers>
        <developer>
            <id>gnodet</id>
            <name>Guillaume Nodet</name>
            <email><EMAIL></email>
            <roles>
                <role>Developer</role>
            </roles>
        </developer>
    </developers>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>

        <jna.version>4.2.2</jna.version>
        <jansi.version>1.17.1</jansi.version>
        <juniversalchardet.version>1.0.3</juniversalchardet.version>
        <sshd.version>1.4.0</sshd.version>
        <easymock.version>3.3.1</easymock.version>
        <junit.version>4.12</junit.version>
        <gogo.runtime.version>1.1.0</gogo.runtime.version>
        <gogo.jline.version>1.1.0</gogo.jline.version>
        <slf4j.version>1.7.21</slf4j.version>
        <findbugs.version>3.0.2</findbugs.version>

        <surefire.argLine />
    </properties>

    <prerequisites>
        <maven>3.5.0</maven>
    </prerequisites>

    <repositories>
        <repository>
            <id>apache-snapshots</id>
            <name>Apache Snapshots</name>
            <url>https://repository.apache.org/content/groups/snapshots</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.jline</groupId>
                <artifactId>jline-terminal</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jline</groupId>
                <artifactId>jline-terminal-jansi</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jline</groupId>
                <artifactId>jline-terminal-jna</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jline</groupId>
                <artifactId>jline-reader</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jline</groupId>
                <artifactId>jline-builtins</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jline</groupId>
                <artifactId>jline-remote-ssh</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jline</groupId>
                <artifactId>jline-remote-telnet</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jline</groupId>
                <artifactId>jline</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jline</groupId>
                <artifactId>jline-style</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>org.fusesource.jansi</groupId>
                <artifactId>jansi</artifactId>
                <version>${jansi.version}</version>
            </dependency>

            <dependency>
                <groupId>net.java.dev.jna</groupId>
                <artifactId>jna</artifactId>
                <version>${jna.version}</version>
            </dependency>

            <dependency>
                <groupId>com.googlecode.juniversalchardet</groupId>
                <artifactId>juniversalchardet</artifactId>
                <version>${juniversalchardet.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.sshd</groupId>
                <artifactId>sshd-core</artifactId>
                <version>${sshd.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.felix</groupId>
                <artifactId>org.apache.felix.gogo.runtime</artifactId>
                <version>${gogo.runtime.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.felix</groupId>
                <artifactId>org.apache.felix.gogo.jline</artifactId>
                <version>${gogo.jline.version}</version>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-jdk14</artifactId>
                <version>${slf4j.version}</version>
            </dependency>

            <dependency>
                <groupId>org.easymock</groupId>
                <artifactId>easymock</artifactId>
                <version>${easymock.version}</version>
            </dependency>

            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.code.findbugs</groupId>
                <artifactId>jsr305</artifactId>
                <version>${findbugs.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <defaultGoal>install</defaultGoal>

        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.7.0</version>
                    <configuration>
                        <showWarnings>true</showWarnings>
                        <compilerArgs>
                            <arg>-Xlint:all,-options</arg>
                            <arg>-Werror</arg>
                            <arg>-profile</arg>
                            <arg>compact1</arg>
                        </compilerArgs>
                        <fork>true</fork>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>2.8.2</version>
                    <configuration>
                        <deployAtEnd>true</deployAtEnd>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>com.mycila</groupId>
                    <artifactId>license-maven-plugin</artifactId>
                    <version>3.0</version>
                    <configuration>
                        <encoding>UTF-8</encoding>
                        <strictCheck>true</strictCheck>
                        <useDefaultExcludes>false</useDefaultExcludes>
                        <header>${project.basedir}/header.txt</header>
                        <aggregate>true</aggregate>
                        <includes>
                            <include>**/pom.xml</include>
                            <include>**/*.xml</include>
                            <include>**/*.xsd</include>
                            <include>**/*.xjb</include>
                            <include>**/*.properties</include>
                            <include>**/*.ini</include>
                            <include>**/*.java</include>
                            <include>**/*.groovy</include>
                            <include>**/*.scala</include>
                            <include>**/*.aj</include>
                            <include>**/*.js</include>
                            <include>**/*.css</include>
                            <include>**/*.help</include>
                            <include>**/*.proto</include>
                            <include>**/*.sm</include>
                            <include>**/*.bat</include>
                            <include>**/*.xsl</include>
                            <include>**/*.html</include>
                            <include>**/*.vm</include>
                            <include>**/*.md</include>
                            <include>**/*.sh</include>
                            <include>**/*.bash</include>
                            <include>**/*.rb</include>
                        </includes>
                        <excludes>
                            <exclude>**/target/**</exclude>
                            <exclude>**/.*/**</exclude>
                            <exclude>**/dependency-reduced-pom.xml</exclude>
                            <exclude>**/nbactions*.xml</exclude>
                            <exclude>**/nb-configuration.xml</exclude>
                            <exclude>**/atlassian-ide-plugin.xml</exclude>
                            <exclude>**/release.properties</exclude>
                            <exclude>**/META-INF/services/**</exclude>
                        </excludes>
                        <mapping>
                            <aj>JAVADOC_STYLE</aj>
                            <groovy>SLASHSTAR_STYLE</groovy>
                            <help>SCRIPT_STYLE</help>
                            <ini>SCRIPT_STYLE</ini>
                            <java>SLASHSTAR_STYLE</java>
                            <md>XML_STYLE</md>
                            <proto>DOUBLESLASH_STYLE</proto>
                            <scala>SLASHSTAR_STYLE</scala>
                            <sm>DOUBLESLASH_STYLE</sm>
                            <vm>VELOCITY_STYLE</vm>
                            <xjb>XML_STYLE</xjb>
                        </mapping>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>3.5.0</version>
                <configuration>
                    <instructions>
                        <Export-Package>*;-noimport:=true</Export-Package>
                    </instructions>
                </configuration>
                <executions>
                    <execution>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>manifest</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.7.0</version>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <archive>
                        <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                    </archive>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.21.0</version>
                <configuration>
                    <redirectTestOutputToFile>true</redirectTestOutputToFile>
                    <argLine>${surefire.argLine}</argLine>
                    <failIfNoTests>false</failIfNoTests>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.1</version>
                <configuration>
                    <archive>
                        <manifestEntries> 
                            <Bundle-Name>JLine Sources Bundle</Bundle-Name>
                            <Bundle-SymbolicName>${project.groupId}.source</Bundle-SymbolicName>
                            <Bundle-Version>${project.version}</Bundle-Version>
                            <Eclipse-SourceBundle>${project.groupId};version="${project.version}"</Eclipse-SourceBundle>
                        </manifestEntries>
                    </archive>
                </configuration>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>3.0.0</version>
                <configuration>
                    <additionalparam>-Xdoclint:none </additionalparam>
                    <additionalOptions>${javadocOptions}</additionalOptions>
                    <failOnError>false</failOnError>
                </configuration>
                <executions>
                    <execution>
                        <id>javadoc</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>org.apache.commons</groupId>
                        <artifactId>commons-lang3</artifactId>
                        <version>3.7</version>
                    </dependency>
                </dependencies>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>2.5.3</version>
                <configuration>
                    <autoVersionSubmodules>true</autoVersionSubmodules>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>java9</id>
            <properties>
                <surefire.argLine>--add-opens java.base/java.io=ALL-UNNAMED</surefire.argLine>
                <javadocOptions>-html4</javadocOptions>
            </properties>
            <activation>
                <jdk>[9,)</jdk>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-compiler-plugin</artifactId>
                        <version>3.6.0</version>
                        <configuration>
                            <release>8</release>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>license-check</id>
            <build>
                <defaultGoal>com.mycila:license-maven-plugin:check</defaultGoal>
            </build>
        </profile>

        <profile>
            <id>license-format</id>
            <build>
                <defaultGoal>com.mycila:license-maven-plugin:format</defaultGoal>
            </build>
        </profile>
    </profiles>

    <modules>
        <module>terminal</module>
        <module>terminal-jna</module>
        <module>terminal-jansi</module>
        <module>reader</module>
        <module>builtins</module>
        <module>remote-ssh</module>
        <module>remote-telnet</module>
        <module>style</module>
        <module>jline</module>
        <module>demo</module>
    </modules>

</project>
