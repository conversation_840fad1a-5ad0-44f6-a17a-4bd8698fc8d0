package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ProjectSimpleBasicVO implements Serializable {

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目编码")
    private String projectNumber;

    @ApiModelProperty(value = "合同编码")
    private String contractNumber;

    @ApiModelProperty(value = "合同名称")
    private String contractName;
}
