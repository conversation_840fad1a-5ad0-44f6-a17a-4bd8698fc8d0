package com.chinasie.orion.service.impl;

import com.chinasie.orion.domain.dto.DocumentModelLibraryDirDTO;
import com.chinasie.orion.domain.entity.DocumentModelLibraryDir;
import com.chinasie.orion.domain.vo.DocumentModelLibraryDirVO;
import com.chinasie.orion.service.DocumentModelLibraryDirService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.service.impl.OrionTreeNodeServiceImpl;




/**
 * <p>
 * DocumentModelLibraryDir 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-01 16:35:39
 */
@Service
@Slf4j
public class DocumentModelLibraryDirServiceImpl extends  OrionTreeNodeServiceImpl<DocumentModelLibraryDir, DocumentModelLibraryDirDTO, DocumentModelLibraryDirVO>   implements DocumentModelLibraryDirService {

}
