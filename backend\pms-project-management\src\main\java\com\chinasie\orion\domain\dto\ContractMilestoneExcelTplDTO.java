package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 合同里程碑导出excel模板工具类
 *
 * <AUTHOR>
 * @since 2024-05-29 15:01:52
 */
@ApiModel(value = "ContractMilestoneExcelTplDTO", description = "合同里程碑导出excel模板工具类")
@Data
@ExcelIgnoreUnannotated
public class ContractMilestoneExcelTplDTO {

    @ApiModelProperty(value = "里程碑名称")
    @ExcelProperty(value = "里程碑名称")
    private String milestoneName;

    @ApiModelProperty(value = "里程碑类型名称")
    @ExcelProperty(value = "里程碑类型名称")
    private String milestoneTypeName;

    @ExcelProperty(value = "里程碑详情")
    @ApiModelProperty(value = "描述")
    private String description;

    @ExcelProperty(value = "父级里程碑名称")
    private String parentName;

    @ApiModelProperty(value = "技术负责人工号")
    @ExcelProperty(value = "技术接口人工号")
    private String techRspUserNo;

    @ApiModelProperty(value = "技术接口人名称")
    @ExcelProperty(value = "技术接口人名称")
    private String techRspUserName;

    @ApiModelProperty(value = "商务负责人工号")
    @ExcelProperty(value = "商务接口人工号")
    private String busRspUserNo;

    @ApiModelProperty(value = "商务负责人名称")
    @ExcelProperty(value = "商务接口人名称")
    private String busRspUserName;

    @ApiModelProperty(value = "计划验收日期")
    @ExcelProperty(value = "计划验收日期")
    @NotNull(message = "计划验收日期不能为空")
    private Date planAcceptDate;

    @ExcelProperty(value = "业务类型名称")
    private String costBusTypeName;

    @ApiModelProperty(value = "里程碑金额")
    @ExcelProperty(value = "里程碑金额")
    @NotNull(message = "里程碑金额不能为空")
    private BigDecimal milestoneAmt;

    @ApiModelProperty(value = "承接部门名称")
    @ExcelProperty(value = "承接部门名称")
    private String undertDeptName;

    @ApiModelProperty(value = "税率")
    @ExcelProperty(value = "税率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "客户名称")
    @ExcelProperty(value = "客户名称")
    private String cusPersonName;

    @ApiModelProperty(value = "客户编号")
    @ExcelProperty(value = "客户编号")
    private String cusPersonNo;

    @ApiModelProperty(value = "创建人")
    @ExcelProperty(value = "创建人")
    private String creatorName;

    @ApiModelProperty(value = "创建人工号")
    @ExcelProperty(value = "创建人工号")
    private String creatorNo;

    @ApiModelProperty(value = "创建时间")
    @ExcelProperty(value = "创建时间")
    private String createTime;

}
