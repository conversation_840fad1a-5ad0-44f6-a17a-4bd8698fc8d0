<template>
  <Layout2Content
    v-if="contentTabs?.length>0"
    v-model:contentTabsIndex="contentTabsIndex"
    :contentTabs="contentTabs"
    class="riskManagement"
    @contentTabsChange="contentTabsChange2"
  >
    <BusinessRisk
      v-if="contentTabs[contentTabsIndex]?.name === '实际风险' && isPower('FXGL_container_01',powerData)"
      modelName="pms"
      :pageType="pageType"
    />
    <PreRisk
      v-if="contentTabs[contentTabsIndex]?.name === '风险预案' && isPower('FXGL_container_02',powerData)"
      :id="id"
      :pageType="pageType"
    />
  </Layout2Content>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  provide,
  readonly,
  ref,
  onMounted,
  nextTick,
  inject,
} from 'vue';
import {
  Layout2Content, isPower,
} from 'lyra-component-vue3';
import PreRisk from './preRisk/index.vue';
import useIndex from '/@/views/pms/projectLaborer/zkhooks/useLocalS.js';
import { BusinessRisk } from '../../components/BusinessRisk';

export default defineComponent({
  // name: 'ProjectSet',
  components: {
    PreRisk,
    Layout2Content,
    BusinessRisk,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
    pageType: {
      type: String,
      default: 'page',
    },
  },
  setup(props) {
    const state = reactive({
      className: '',
      contentTabsIndex: 0,
      powerData: [],
      contentTabs: [],
    });
    state.powerData = inject('powerData');
    const riskIndex = useIndex('riskIndex');
    function contentTabsChange2(index) {
      // state.contentTabsIndex = index;
      riskIndex.value = index;
    }
    onMounted(() => {
      if (riskIndex.value !== 0) {
        state.contentTabsIndex = riskIndex.value;
      }
      if (!state.contentTabsIndex) {
        state.contentTabsIndex = 0;
      }
      if (isPower('XMX_container_05_01', state.powerData) || props.pageType === 'modal') {
        state.contentTabs.push({ name: '实际风险' });
      }
      if (isPower('XMX_container_05_02', state.powerData) || props.pageType === 'modal') {
        state.contentTabs.push({ name: '风险预案' });
      }
    });
    const initForm = (data) => {
      state.className = data.className;
    };
    const projectId = ref(props.id);
    provide('projectId', readonly(projectId));
    return {
      ...toRefs(state),
      initForm,
      contentTabsChange2,
      isPower,
    };
  },
});
</script>

<style lang="less" scoped>

.riskManagement{
  height:100%
}
</style>
