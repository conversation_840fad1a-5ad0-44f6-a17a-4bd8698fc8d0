<script setup lang="ts">
import {
  BasicForm, useForm, UploadList, BasicCard, openModal, getDictByNumber,
} from 'lyra-component-vue3';
import {
  h, onMounted, Ref, ref, computed,
} from 'vue';
import Api from '/@/api';
import { message } from 'ant-design-vue';
import ProjectPlan from './ProjectPlan.vue';
import { add, edit, reviewManageUser } from '/@/views/pms/api/review';

const props = withDefaults(defineProps<{
    formId: string | undefined,
    projectId: string | undefined,
    projectName: string | undefined,
    isPlan:boolean,
    planData:object,
}>(), {
  projectId: undefined,
  formId: undefined,
  projectName: undefined,
  isPlan: false,
  planData: () => ({}),
});
const planId = ref('');
const manageUserId = ref('');
const schemas = [
  {
    component: 'Input',
    colProps: {
      span: 24,
      style: 'padding-right: 15px;',
    },
    slot: 'basicInfo',
  },
  {
    field: 'reviewType',
    component: 'ApiSelect',
    label: '评审类型',
    required: true,
    componentProps: {
      placeholder: '请选择评审类型',
      api: () => getDictByNumber('pms_review_type'),
      labelField: 'description',
      valueField: 'number',
      onChange: (_, { label }) => {
        setFieldsValue({ name: `${props.projectName}_${label}` });
      },
    },
  },
  {
    field: 'planName',
    component: 'Input',
    label: '关联任务计划',
    colProps: {
      span: 12,
    },
    required: true,
    componentProps: {
      readonly: true,
      disabled: computed(() => props.isPlan),
      placeholder: '请选择关联任务计划',
      onClick: () => !props.isPlan && handleClickTaskPlan(),
      addonAfter: h(
        'span',
        { onClick: () => !props.isPlan && handleClickTaskPlan() },
        '请选择',
      ),
      onChange: () => handleChangeTaskPlan(),
    },
  },
  {
    field: 'name',
    label: '评审名称',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '选择评审类型自动生成(项目名称_评审类型)',
    },
    component: 'Input',
  },
  {
    field: 'manageUserName',
    label: '项目管理专员',
    colProps: { span: 12 },
    required: true,
    componentProps: {
      readonly: true,
    },
    component: 'Input',
  },

  {
    field: 'requestState',
    label: '研制过程文档签审完成并齐套，满足产品开发流程的要求',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请选择',
      options: [
        {
          label: '是',
          value: 1,
        },
        {
          label: '否',
          value: 0,
        },
      ],
    },
    rules: [
      {
        required: true,
        type: 'number',
      },
    ],
    component: 'Select',
  },
  {
    field: 'phaseLegacy',
    label: '阶段遗留问题已关闭',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请选择',
      options: [
        {
          label: '已关闭',
          value: 0,
        },
        {
          label: '未关闭',
          value: 1,
        },
      ],
    },
    rules: [
      {
        required: true,
        type: 'number',
      },
    ],
    component: 'Select',
  },
  {
    field: 'remark',
    label: '备注',
    colProps: { span: 24 },
    componentProps: {
      maxlength: 1000,
      placeholder: '请输入',
    },
    component: 'InputTextArea',
  },
  {
    component: 'Input',
    colProps: {
      span: 24,
    },
    slot: 'scoreInfo',
  },
  {
    field: 'deliverName',
    label: '评审交付物名称',
    colProps: { span: 12 },
    componentProps: {
      maxlength: 100,
      allowClear: true,
      placeholder: '请输入评审交付物名称',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'plmNo',
    label: '评审文件PLM编号',
    colProps: { span: 12 },
    componentProps: {
      maxlength: 100,
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: false }],
    component: 'Input',
  },
  {
    field: 'files',
    label: '附件上传',
    colProps: { span: 24 },
    component: 'Select',
    colSlot: '_attachment_',
  },
];

const [register, { validate, setFieldsValue, getFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/review').fetch('', props.formId, 'GET');
    planId.value = result.planId;
    manageUserId.value = result.manageUserId;
    setFieldsValue(result);
  } finally {
    loading.value = false;
  }
}

// 附件列表改变处理方法
function onChange(files: any[], field: string) {
  setFieldsValue({
    [field]: files,
  });
}

function handleClickTaskPlan() {
  const refModal = ref();
  openModal({
    title: '关联任务计划',
    width: 1100,
    content(h) {
      return h(ProjectPlan, {
        ref: refModal,
        projectId: props.projectId,
        type: 'radio',
      });
    },
    async onOk() {
      const { isSelectedAndGetData } = refModal.value;
      const values = await isSelectedAndGetData();
      await setFieldsValue({ planName: values[0]?.name });
      planId.value = values[0]?.id;
    },
  });
}
async function handleChangeTaskPlan() {
  message.info('请选择');
  await setFieldsValue({ planName: '' });
  planId.value = '';
}
onMounted(async () => {
  // 新增-设置项目管理专员
  if (!props.formId) {
    const user = await reviewManageUser();
    if (user) {
      await setFieldsValue({ manageUserName: user?.name || '' });
      manageUserId.value = user?.id;
    }
    if (props.isPlan) {
      await setFieldsValue({ planName: props.planData.name });
    }
  } else {
    getFormData();
  }
});

defineExpose({
  async onSubmit() {
    await validate();
    const formValues = getFieldsValue();
    const params = {
      ...formValues,
      projectId: props.projectId,
      planId: props.isPlan ? props.planData.id : planId.value,
      manageUser: manageUserId.value,
    };

    if (!props.formId) {
      await add(params);
    } else {
      await edit({
        ...params,
        id: props.formId,
      });
    }
  },
});
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  >
    <template #basicInfo>
      <BasicCard
        title="评审基本信息"
      />
    </template>
    <template #scoreInfo>
      <BasicCard title="评审文件信息" />
    </template>
    <template #_attachment_="{model,field}">
      <UploadList
        :is-spacing="false"
        :listData="model[field]"
        @change="(files)=>onChange(files,field)"
      />
    </template>
  </BasicForm>
</template>

<style scoped lang="less">
.basic-card-wrap.spacing {
  margin: 0;
}
.basic-card-wrap.border {
  border: none;
}
</style>
