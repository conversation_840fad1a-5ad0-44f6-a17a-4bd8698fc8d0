package com.chinasie.orion.domain.dto.job;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/11/14/16:38
 * @description:
 */
@Data
public class NewJobMangeDTO extends ObjectDTO implements Serializable {


    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    private String number;

    /**
     * 作业名
     */
    @ApiModelProperty(value = "作业名称")
    private String name;

    /**
     * N/O
     */
    @ApiModelProperty(value = "N/O")
    private String nOrO;

    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    private String repairRound;


    /**
     * 工作中心
     */
    @ApiModelProperty(value = "工作中心")
    private String workCenter;

    /**
     * 责任中心
     */
    @ApiModelProperty(value = "责任中心")
    private String rspDept;

    /**
     * 作业基地
     */
    @ApiModelProperty(value = "作业基地")
    private String jobBaseName;

    /**
     * 计划开工时间
     */
    @ApiModelProperty(value = "计划开工时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date beginTime;

    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date endTime;

    /**
     * 计划工期
     */
    @ApiModelProperty(value = "计划工期")
    private Integer workDuration;

    /**
     * 实际开工时间
     */
    @ApiModelProperty(value = "实际开工时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date actualBeginTime;

    /**
     * 实际完工时间
     */
    @ApiModelProperty(value = "实际完工时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date actualEndTime;

    /**
     * 作业阶段：作业状态
     */
    @ApiModelProperty(value = "作业阶段：作业状态")
    private String phase;

    /**
     * 是否匹配（0未匹配  1匹配）
     */
    @ApiModelProperty(value = "是否匹配（0未匹配  1匹配）")
    private Integer match;

    @ApiModelProperty(value = "责任人工号")
    private String rspUserCode;
    @ApiModelProperty(value = "责任人名称")
    private String rspUserName;
    @ApiModelProperty(value = "责任人ID")
    private String rspUserId;


    /**
     * 防异物等级
     */
    @ApiModelProperty(value = "防异物等级")
    @ExcelProperty(value = "防异物等级 ", index = 2)
    private String antiForfeignLevel;

    /**
     * 首次执行
     */
    @ApiModelProperty(value = "首次执行")
    @ExcelProperty(value = "首次执行 ", index = 3)
    private String firstExecute;

    /**
     * 是否高风险
     */
    @ApiModelProperty(value = "是否高风险")
    private Boolean isHighRisk;

}


