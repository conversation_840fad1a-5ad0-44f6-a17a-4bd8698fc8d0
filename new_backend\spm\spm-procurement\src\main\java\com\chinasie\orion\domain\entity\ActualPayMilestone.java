package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ActualPayMilestone Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@TableName(value = "ncf_form_actual_pay_milestone")
@ApiModel(value = "ActualPayMilestoneEntity对象", description = "合同支付里程碑（实际）")
@Data

public class ActualPayMilestone extends ObjectEntity implements Serializable {

    /**
     * 支付编号
     */
    @ApiModelProperty(value = "支付编号")
    @TableField(value = "pay_number")
    private String payNumber;

    /**
     * 支付申请人
     */
    @ApiModelProperty(value = "支付申请人")
    @TableField(value = "pay_req_user")
    private String payReqUser;

     /**
     * 支付申请人名称
     */
    @ApiModelProperty(value = "支付申请人名称")
    @TableField(value = "pay_req_user_name")
    private String payReqUserName;


    /**
     * 支付申请发起时间
     */
    @ApiModelProperty(value = "支付申请发起时间")
    @TableField(value = "pay_req_start_time")
    private Date payReqStartTime;

    /**
     * 支付类型
     */
    @ApiModelProperty(value = "支付类型")
    @TableField(value = "pay_type")
    private String payType;

    /**
     * 预计付款时间
     */
    @ApiModelProperty(value = "预计付款时间")
    @TableField(value = "estimated_pay_time")
    private Date estimatedPayTime;

    /**
     * 里程碑业务描述
     */
    @ApiModelProperty(value = "里程碑业务描述")
    @TableField(value = "milestone_desc")
    private String milestoneDesc;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @TableField(value = "supplier_name")
    private String supplierName;

    /**
     * 供应商编号
     */
    @ApiModelProperty(value = "供应商名称")
    @TableField(value = "supplier_code")
    private String supplierCode;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    @TableField(value = "currency")
    private String currency;

    /**
     * 本次支付汇总金额
     */
    @ApiModelProperty(value = "本次支付汇总金额")
    @TableField(value = "current_pay_total_amount")
    private BigDecimal currentPayTotalAmount;

    /**
     * 支付比例
     */
    @ApiModelProperty(value = "支付比例")
    @TableField(value = "pay_ratio")
    private String payRatio;

    /**
     * 已支付金额
     */
    @ApiModelProperty(value = "已支付金额")
    @TableField(value = "paid_amount")
    private BigDecimal paidAmount;

    /**
     * 合同预计验收时间
     */
    @ApiModelProperty(value = "合同预计验收时间")
    @TableField(value = "estimated_acceptance_time")
    private Date estimatedAcceptanceTime;

    /**
     * 合同验收时间
     */
    @ApiModelProperty(value = "合同验收时间")
    @TableField(value = "acceptance_time")
    private Date acceptanceTime;

    /**
     * 是否有质保金
     */
    @ApiModelProperty(value = "是否有质保金")
    @TableField(value = "is_have_quality_amount")
    private Boolean isHaveQualityAmount;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @TableField(value = "main_table_id")
    private String mainTableId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_number")
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @TableField(value = "contract_name")
    private String contractName;

    /**
     * 实际支付时间
     */
    @ApiModelProperty(value = "实际交付时间")
    @TableField(value = "actual_delivery_time")
    private Date actualDeliveryTime;

    /**
     * 原因
     */
    @ApiModelProperty(value = "原因")
    @TableField(value = "reason")
    private String reason;

    /**
     * 实际验收时间
     */
    @ApiModelProperty(value = "实际验收时间")
    @TableField(value = "actual_acceptance_time")
    private Date actualAcceptanceTime;

    /**
     * 是否一次验收
     */
    @ApiModelProperty(value = "是否一次验收")
    @TableField(value = "is_acceptance_qualified")
    private Boolean isAcceptanceQualified;

    /**
     * 是否一次验收合格
     */
    @ApiModelProperty(value = "是否一次验收合格")
    @TableField(value = "is_onetime_acceptance")
    private Boolean isOnetimeAcceptance;

    /**
     * 是否按时交付
     */
    @ApiModelProperty(value = "是否按时交付")
    @TableField(value = "is_deliver_on_time")
    private Boolean isDeliverOnTime;

    /**
     * 未按时交付验收原因
     */
    @ApiModelProperty(value = "未按时交付验收原因")
    @TableField(value = "reason_of_undeliver")
    private String reasonOfUndeliver;

    /**
     * 实际支付时间
     */
    @ApiModelProperty(value = "实际支付时间")
    @TableField(value = "actual_pay_time")
    private Date actualPayTime;
}
