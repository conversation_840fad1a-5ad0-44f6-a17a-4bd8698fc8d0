package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.ProjectPurchaseOrderAllInfoDTO;
import com.chinasie.orion.domain.dto.ProjectPurchaseOrderInfoDTO;
import com.chinasie.orion.domain.entity.ProjectPurchaseOrderInfo;
import com.chinasie.orion.domain.vo.ProjectPurchaseOrderAllInfoVO;
import com.chinasie.orion.domain.vo.ProjectPurchaseOrderInfoVO;
import com.chinasie.orion.domain.vo.ProjectPurchaseOrderListInfoVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;
/**
 * <p>
 * ProjectPurchaseOrderInfo 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-06 08:42:57
 */
public interface ProjectPurchaseOrderInfoService  extends OrionBaseService<ProjectPurchaseOrderInfo> {
    /**
     *  详情
     *
     * * @param id
     */
    ProjectPurchaseOrderAllInfoVO detail(String id)  throws Exception;

    /**
     *  新增
     *
     * * @param projectPurchaseOrderAllInfoDTO
     */
    ProjectPurchaseOrderInfoVO create(ProjectPurchaseOrderAllInfoDTO projectPurchaseOrderAllInfoDTO)  throws Exception;

    /**
     *  编辑
     *
     * * @param projectPurchaseOrderInfoDTO
     */
    Boolean edit(ProjectPurchaseOrderAllInfoDTO projectPurchaseOrderAllInfoDTO) throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;

    /**
     * 订单关闭（批量）
     *
     * * @param ids
     */
    Boolean close(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     */
    Page<ProjectPurchaseOrderListInfoVO> pages(Page<ProjectPurchaseOrderInfoDTO> pageRequest) throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     */
    Page<ProjectPurchaseOrderListInfoVO> userPage(Page<ProjectPurchaseOrderInfoDTO> pageRequest) throws Exception;


    List<ProjectPurchaseOrderListInfoVO> getProjectPurchaseOrderList(String contractId) throws Exception;
}
