export const columns = [
  {
    title: '编号',
    dataIndex: 'number',
    align: 'left',
    key: 'number',

    width: '120px',
    // sorter: true,
    ellipsis: true,
  },
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',

    width: '240px',
    align: 'left',
    slots: { customRender: 'name' },
    // sorter: true,
    ellipsis: true,
  },

  {
    title: '计划类型',
    dataIndex: 'planTypeName',
    key: 'planType',
    width: '100px',
    margin: '0 20px 0 0',
    align: 'left',
    slots: { customRender: 'planTypeName' },
    // sorter: true,
    ellipsis: true,
  },
  {
    title: '负责人',
    dataIndex: 'principalName',
    key: 'principalId',

    width: '80px',
    align: 'left',
    // sorter: true,
    ellipsis: true,
    slots: { customRender: 'principalName' },
  },
  {
    title: '计划进度',
    dataIndex: 'scheduleName',
    key: 'schedule',
    width: '90px',
    align: 'left',
    slots: { customRender: 'scheduleName' },
    // sorter: true,
    ellipsis: true,
  },
  {
    title: '优先级',
    dataIndex: 'priorityLevelName',
    key: 'priorityLevel',

    width: '80px',
    align: 'left',
    slots: { customRender: 'priorityLevelName' },
    // sorter: true,
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'statusName',
    key: 'status',

    width: '80px',
    align: 'left',
    sorter: true,
    ellipsis: true,
    slots: { customRender: 'statusName' },
  },
  {
    title: '开始日期',
    dataIndex: 'planPredictStartTime',
    key: 'planPredictStartTime',

    width: '130px',
    align: 'left',
    slots: { customRender: 'planPredictStartTime' },
    sorter: true,
    ellipsis: true,
  },

  {
    title: '结束日期',
    dataIndex: 'planPredictEndTime',
    key: 'planPredictEndTime',

    width: '150px',
    align: 'left',
    sorter: true,
    ellipsis: true,
    slots: { customRender: 'planPredictEndTime' },
  },
];
