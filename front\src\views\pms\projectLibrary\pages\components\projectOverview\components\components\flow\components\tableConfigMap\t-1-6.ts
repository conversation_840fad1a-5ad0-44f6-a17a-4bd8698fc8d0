import { DataStatusTag, IOrionTableOptions } from 'lyra-component-vue3';
import { IGetConfigProps } from '.';
import Api from '/@/api';

export default (props: IGetConfigProps): IOrionTableOptions => ({
  api() {
    return new Api('/pms/projectOverview/zgh/projectLife/purchaseRequestContract').fetch({}, props.projectId, 'GET');
  },
  columns: [
    {
      title: '订单编号',
      dataIndex: 'milestoneName',
    },
    {
      title: '合同编号',
      dataIndex: 'milestoneName',
    },
    {
      title: '供应商名称',
      dataIndex: 'milestoneName',
    },
    {
      title: '支付金额',
      dataIndex: 'milestoneName',
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 180,
      fixed: 'right',
      slots: { customRender: 'action' },
    },
  ],
  actions: [
    {
      text: '查看',
      onClick() {

      },
    },
  ],
});
