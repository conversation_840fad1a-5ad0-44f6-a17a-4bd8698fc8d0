package com.chinasie.orion.service.impl.assetApply;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.domain.dto.assetApply.AssetSyncDTO;
import com.chinasie.orion.domain.dto.assetApply.ProjectAssetApplyDetailAssetsDTO;
import com.chinasie.orion.domain.entity.assetApply.ProjectAssetApplyDetailAssets;
import com.chinasie.orion.domain.vo.NcfFormpurchaseRequestDetailAPIAsset;
import com.chinasie.orion.domain.vo.applyAsset.AssetSyncVO;
import com.chinasie.orion.domain.vo.applyAsset.ProjectAssetApplyDetailAssetsVO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.assetApply.ProjectAssetApplyDetailAssetsMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectInitiationApiService;
import com.chinasie.orion.service.assetApply.ProjectAssetApplyDetailAssetsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;




/**
 * <p>
 * ProjectAssetApplyDetailAssets 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-03 14:25:17
 */
@Service
@Slf4j
public class ProjectAssetApplyDetailAssetsServiceImpl extends OrionBaseServiceImpl<ProjectAssetApplyDetailAssetsMapper, ProjectAssetApplyDetailAssets> implements ProjectAssetApplyDetailAssetsService {

    @Autowired
    private ProjectInitiationApiService projectInitiationApiService;


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


//    /**
//     *  详情
//     *
//     * * @param id
//     */
//    @Override
//    @OperationPower(operationType = OperationPowerType.DETAIL)
//    public ProjectAssetApplyDetailAssetsVO detail(String id, String pageCode) throws Exception {
//        ProjectAssetApplyDetailAssets projectAssetApplyDetailAssets =this.getById(id);
//        ProjectAssetApplyDetailAssetsVO result = BeanCopyUtils.convertTo(projectAssetApplyDetailAssets,ProjectAssetApplyDetailAssetsVO::new);
//        setEveryName(Collections.singletonList(result));
//
//
//        return result;
//    }

    /**
     *  新增
     *
     * * @param projectAssetApplyDetailAssetsDTO
     */
    @Override
    public  String create(ProjectAssetApplyDetailAssetsDTO projectAssetApplyDetailAssetsDTO) throws Exception {
        List<String> assetSyncIds = projectAssetApplyDetailAssetsDTO.getAssetSyncIds();
        LambdaQueryWrapperX<ProjectAssetApplyDetailAssets> wrapperX = new LambdaQueryWrapperX<>(ProjectAssetApplyDetailAssets.class);
        wrapperX.eq(ProjectAssetApplyDetailAssets::getAssetApplyId, projectAssetApplyDetailAssetsDTO.getAssetApplyId());
        wrapperX.in(ProjectAssetApplyDetailAssets::getAssetSyncId, projectAssetApplyDetailAssetsDTO.getAssetSyncIds());
        List<ProjectAssetApplyDetailAssets> oldList = this.list(wrapperX);
        if(!CollectionUtil.isEmpty(oldList)){
            throw new BaseException(PMSErrorCode.PMS_ERROR_DATA_EXIST);
        }
        List<ProjectAssetApplyDetailAssets> list = new ArrayList<>();
        for (String assetSyncId : assetSyncIds) {
            ProjectAssetApplyDetailAssets projectAssetApplyDetailAssets = new ProjectAssetApplyDetailAssets();
            projectAssetApplyDetailAssets.setAssetApplyId(projectAssetApplyDetailAssetsDTO.getAssetApplyId());
            projectAssetApplyDetailAssets.setAssetSyncId(assetSyncId);
            list.add(projectAssetApplyDetailAssets);
        }
        this.saveBatch(list);



        return "操作成功";
    }

//    /**
//     *  编辑
//     *
//     * * @param projectAssetApplyDetailAssetsDTO
//     */
//    @Override
//    public Boolean edit(ProjectAssetApplyDetailAssetsDTO projectAssetApplyDetailAssetsDTO) throws Exception {
//        ProjectAssetApplyDetailAssets projectAssetApplyDetailAssets =BeanCopyUtils.convertTo(projectAssetApplyDetailAssetsDTO,ProjectAssetApplyDetailAssets::new);
//
//        this.updateById(projectAssetApplyDetailAssets);
//
//        String rsp=projectAssetApplyDetailAssets.getId();
//
//
//
//        return true;
//    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<AssetSyncVO> pages(Page<AssetSyncDTO> pageRequest) throws Exception {
        Page<AssetSyncVO> pageResult = new Page<>();
        //资产信息还是从采购行里面取
        Page<NcfFormpurchaseRequestDetailAPIAsset> pages = projectInitiationApiService.getNcfFormpurchaseRequestDetailAPIAssetByProjectId(pageRequest.getQuery().getProjectId(),pageRequest.getPageNum(),pageRequest.getPageSize());
        if(pages == null){
            return null;
        }
        BeanUtils.copyProperties(pages,pageResult);
        return pageResult;
    }


//    @Override
//    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
//
//        String fileName = "资产转固申请详情表-Asset导入模板.xlsx";
//        ExcelUtils.write(response, fileName, "sheet1", ProjectAssetApplyDetailAssetsDTO.class, new ArrayList<>());
//
//    }
//
//
//    @Override
//    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {
//
//        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
//        InputStream inputStream = excel.getInputStream();
//        ProjectAssetApplyDetailAssetsExcelListener excelReadListener = new ProjectAssetApplyDetailAssetsExcelListener();
//        EasyExcel.read(inputStream,ProjectAssetApplyDetailAssetsDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
//        List<ProjectAssetApplyDetailAssetsDTO> dtoS = excelReadListener.getData();
//        if (CollectionUtils.isEmpty(dtoS)) {
//            result.setCode(400);
//            result.setOom("数据不存在或导入模板表头解析错误，请检查");
//            return result;
//        }
//        if (dtoS.size() > 1000) {
//            result.setCode(400);
//            result.setOom("数据量超限");
//            return result;
//        }
//        log.info("资产转固申请详情表-Asset导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
//        List<ProjectAssetApplyDetailAssets> projectAssetApplyDetailAssetses =BeanCopyUtils.convertListTo(dtoS,ProjectAssetApplyDetailAssets::new);
//
//        String importId = IdUtil.fastSimpleUUID();
//        orionJ2CacheService.set("pmsx::ProjectAssetApplyDetailAssets-import::id", importId, projectAssetApplyDetailAssetses, 24 * 60 * 60);
//        result.setCode(200);
//        result.setSucc(importId);
//
//        return result;
//    }
//
//
//    @Override
//    public Boolean importByExcel(String importId) throws Exception {
//        List<ProjectAssetApplyDetailAssets> projectAssetApplyDetailAssetses = (List<ProjectAssetApplyDetailAssets>) orionJ2CacheService.get("pmsx::ProjectAssetApplyDetailAssets-import::id", importId);
//        log.info("资产转固申请详情表-Asset导入的入库数据={}", JSONUtil.toJsonStr(projectAssetApplyDetailAssetses));
//
//        this.saveBatch(projectAssetApplyDetailAssetses);
//        orionJ2CacheService.delete("pmsx::ProjectAssetApplyDetailAssets-import::id", importId);
//        return true;
//    }
//
//
//    @Override
//    public Boolean importCancelByExcel(String importId) throws Exception {
//        orionJ2CacheService.delete("pmsx::ProjectAssetApplyDetailAssets-import::id", importId);
//        return true;
//    }
//
//
//    @Override
//    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
//        LambdaQueryWrapperX<ProjectAssetApplyDetailAssets> condition = new LambdaQueryWrapperX<>( ProjectAssetApplyDetailAssets. class);
//        if (!CollectionUtils.isEmpty(searchConditions)) {
//            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
//        }
//        condition.orderByDesc(ProjectAssetApplyDetailAssets::getCreateTime);
//        List<ProjectAssetApplyDetailAssets> projectAssetApplyDetailAssetses =   this.list(condition);
//
//        List<ProjectAssetApplyDetailAssetsDTO> dtos = BeanCopyUtils.convertListTo(projectAssetApplyDetailAssetses, ProjectAssetApplyDetailAssetsDTO::new);
//
//        String fileName = "资产转固申请详情表-Asset数据导出.xlsx";
//        ExcelUtils.write(response, fileName, "sheet1", ProjectAssetApplyDetailAssetsDTO.class,dtos );
//
//    }

    @Override
    public void  setEveryName(List<ProjectAssetApplyDetailAssetsVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class ProjectAssetApplyDetailAssetsExcelListener extends AnalysisEventListener<ProjectAssetApplyDetailAssetsDTO> {

        private final List<ProjectAssetApplyDetailAssetsDTO> data = new ArrayList<>();

        @Override
        public void invoke(ProjectAssetApplyDetailAssetsDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ProjectAssetApplyDetailAssetsDTO> getData() {
            return data;
        }
    }


}
