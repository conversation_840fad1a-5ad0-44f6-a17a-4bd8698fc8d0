package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * SupplierBank DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@ApiModel(value = "SupplierBankDTO对象", description = "银行信息")
@Data
@ExcelIgnoreUnannotated
public class SupplierBankDTO extends ObjectDTO implements Serializable {

    /**
     * 银行代码
     */
    @ApiModelProperty(value = "银行代码")
    @ExcelProperty(value = "银行代码 ", index = 0)
    private String bankCode;

    /**
     * 银行网点
     */
    @ApiModelProperty(value = "银行网点")
    @ExcelProperty(value = "银行网点 ", index = 1)
    private String bankBranch;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    @ExcelProperty(value = "币种 ", index = 2)
    private String currency;

    /**
     * 默认银行账号
     */
    @ApiModelProperty(value = "默认银行账号")
    @ExcelProperty(value = "默认银行账号 ", index = 3)
    private String defaultAccount;

    /**
     * 账号名称（受益人）
     */
    @ApiModelProperty(value = "账号名称（受益人）")
    @ExcelProperty(value = "账号名称（受益人） ", index = 4)
    private String accountHolder;

    /**
     * 银行账号
     */
    @ApiModelProperty(value = "银行账号")
    @ExcelProperty(value = "银行账号 ", index = 5)
    private String bankAccount;

    /**
     * 国际银行代码（SWIFT)
     */
    @ApiModelProperty(value = "国际银行代码（SWIFT)")
    @ExcelProperty(value = "国际银行代码（SWIFT) ", index = 6)
    private String swiftCode;

    /**
     * 国际银行账户号码（IBAN)
     */
    @ApiModelProperty(value = "国际银行账户号码（IBAN)	")
    @ExcelProperty(value = "国际银行账户号码（IBAN)	 ", index = 7)
    private String iban;

    /**
     * 分理处/营业点
     */
    @ApiModelProperty(value = "分理处/营业点")
    @ExcelProperty(value = "分理处/营业点 ", index = 8)
    private String tellerOffice;

    /**
     * 支行
     */
    @ApiModelProperty(value = "支行")
    @ExcelProperty(value = "支行 ", index = 9)
    private String subBranch;

    /**
     * 分行
     */
    @ApiModelProperty(value = "分行")
    @ExcelProperty(value = "分行 ", index = 10)
    private String branch;

    /**
     * 银行名称
     */
    @ApiModelProperty(value = "银行名称")
    @ExcelProperty(value = "银行名称 ", index = 11)
    private String bankName;

    /**
     * 开户行地区
     */
    @ApiModelProperty(value = "开户行地区")
    @ExcelProperty(value = "开户行地区 ", index = 12)
    private String bankDistrict;

    /**
     * 开户行城市
     */
    @ApiModelProperty(value = "开户行城市")
    @ExcelProperty(value = "开户行城市 ", index = 13)
    private String bankCity;

    /**
     * 开户行省份
     */
    @ApiModelProperty(value = "开户行省份")
    @ExcelProperty(value = "开户行省份 ", index = 14)
    private String bankProvince;

    /**
     * 开户行国家和地区
     */
    @ApiModelProperty(value = "开户行国家和地区")
    @ExcelProperty(value = "开户行国家和地区 ", index = 15)
    private String bankCountryArea;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    @ExcelProperty(value = "序号 ", index = 16)
    private String serialNumber;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @ExcelProperty(value = "供应商名称 ", index = 17)
    private String supplierName;

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @ExcelProperty(value = "供应商编码 ", index = 18)
    private String supplierCode;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 19)
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @ExcelProperty(value = "主表ID ", index = 20)
    private String mainTableId;


}
