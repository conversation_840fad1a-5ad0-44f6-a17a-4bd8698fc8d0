<script setup lang="ts">
import { Layout, randomString } from 'lyra-component-vue3';
import {
  Breadcrumb, BreadcrumbItem, Collapse, CollapsePanel,
} from 'ant-design-vue';
import { BarsOutlined, CaretRightOutlined } from '@ant-design/icons-vue';
import {
  CSSProperties, onActivated, provide, ref, Ref,
} from 'vue';
import CalendarOverview from './components/overhaul/CalendarOverview.vue';
import InOperation from './components/overhaul/InOperation.vue';
import InPreparation from './components/overhaul/InPreparation.vue';
import FocusIssues from './components/overhaul/FocusIssues.vue';
import { usePagePower } from '/@/views/pms/hooks';
import ActionItemFeedback from './components/actionItemFeedback/ActionItemFeedback.vue';

const collapseKeys: Ref<string[]> = ref([
  '1',
  '2',
  '3',
  '4',
]);

const customStyle: CSSProperties = {
  padding: '4px 14px',
  border: 'none',
};

const { powerData, getPowerDataHandle } = usePagePower();
provide('powerData', powerData);

const key: Ref<string> = ref();
const isKeep: Ref<boolean> = ref(false);
onActivated(() => {
  if (!isKeep.value) return isKeep.value = true;
  key.value = randomString();
});
</script>

<template>
  <Layout
    v-get-power="{pageCode:'PMSOverhaul',getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
  >
    <div class="layout-container">
      <div class="header">
        <h3>大修管理</h3>
        <Breadcrumb>
          <BreadcrumbItem>
            <bars-outlined />
            <span>大修管理</span>
          </BreadcrumbItem>
        </Breadcrumb>
      </div>
      <CalendarOverview :key="key" />
    </div>
    <Collapse
      :key="key"
      v-model:activeKey="collapseKeys"
      ghost
      :bordered="false"
    >
      <template #expandIcon="{ isActive }">
        <caret-right-outlined :rotate="isActive ? 90 : 0" />
      </template>
      <CollapsePanel
        key="1"
        header="关注问题"
        :style="customStyle"
      >
        <FocusIssues />
      </CollapsePanel>
      <CollapsePanel
        key="2"
        header="实施中大修"
        :style="customStyle"
      >
        <InOperation />
      </CollapsePanel>
      <CollapsePanel
        key="3"
        header="准备中大修"
        :style="customStyle"
      >
        <InPreparation />
      </CollapsePanel>
      <CollapsePanel
        key="4"
        header="大修总结"
        :style="customStyle"
      >
        <ActionItemFeedback />
      </CollapsePanel>
    </Collapse>
  </Layout>
</template>

<style scoped lang="less">
:deep(.ant-collapse-header) {
  display: inline-flex;
}

.layout-container {
  padding: ~`getPrefixVar('content-padding-top')` ~`getPrefixVar('content-padding-left')` 0;

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: ~`getPrefixVar('content-padding-top')`;
    border-bottom: 1px solid #e1e1e1;

    h3 {
      margin: 0;
      font-weight: bold;
    }
  }
}
</style>
