package com.chinasie.orion.domain.vo.projectOverviewZgh;

import com.chinasie.orion.domain.vo.ProjectSchemeVO;
import com.chinasie.orion.domain.vo.QuestionManagementVO;
import com.chinasie.orion.domain.vo.RiskManagementVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 异常计划明细信息
 */
@Data
@ApiModel(value = "ProjectPlanOverdueVO", description = "异常计划明细信息")
public class ProjectPlanOverdueCountVO {
    @ApiModelProperty("已逾期")
    private Integer overdue= 0;

    @ApiModelProperty("临期")
    private Integer doing= 0;

    @ApiModelProperty("逾期完成")
    private Integer overdueDone= 0;

    @ApiModelProperty("风险")
    private Integer riskCount = 0;

    @ApiModelProperty("问题")
    private Integer questionCount = 0;

    @ApiModelProperty("计划列表")
    private List<ProjectSchemeVO> projectSchemeVOS=new ArrayList<>();

    @ApiModelProperty("风险列表")
    private List<RiskManagementVO> riskManagementVos = new ArrayList<>();

    @ApiModelProperty("问题列表")
    List<QuestionManagementVO> questionManagementVOs = new ArrayList<>();
}
