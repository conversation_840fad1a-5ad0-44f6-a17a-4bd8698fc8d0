package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * NcfFormPurchOrderDetail Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-21 14:56:15
 */
@TableName(value = "pms_ncf_form_purch_order_detail")
@ApiModel(value = "NcfFormPurchOrderDetailEntity对象", description = "商城集采订单（明细表）")
@Data

public class NcfFormPurchOrderDetail extends ObjectEntity implements Serializable {

    /**
     * 框架协议号
     */
    @ApiModelProperty(value = "框架协议号")
    @TableField(value = "contract_number")
    private String contractNumber;

    /**
     * 不含税金额
     */
    @ApiModelProperty(value = "不含税金额")
    @TableField(value = "tax_not_included")
    private BigDecimal taxNotIncluded;

    /**
     * 增值税率
     */
    @ApiModelProperty(value = "增值税率")
    @TableField(value = "tax")
    private String tax;

    /**
     * 总价
     */
    @ApiModelProperty(value = "总价")
    @TableField(value = "total_price")
    private String totalPrice;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    @TableField(value = "unit_price")
    private String unitPrice;

    /**
     * 计量单位
     */
    @ApiModelProperty(value = "计量单位")
    @TableField(value = "unit")
    private String unit;

    /**
     * 采购数量
     */
    @ApiModelProperty(value = "采购数量")
    @TableField(value = "purchase_quantity")
    private String purchaseQuantity;

    /**
     * 商品类型
     */
    @ApiModelProperty(value = "商品类型")
    @TableField(value = "iten_type")
    private String itenType;

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    @TableField(value = "order_number")
    private String orderNumber;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @TableField(value = "contract_name")
    private String contractName;

    /**
     * 退货金额
     */
    @ApiModelProperty(value = "退货金额")
    @TableField(value = "return_amount")
    private BigDecimal returnAmount;

    /**
     * 企业名称
     */
    @ApiModelProperty(value = "企业名称")
    @TableField(value = "enterprise_name")
    private String enterpriseName;

    /**
     * 下单人
     */
    @ApiModelProperty(value = "下单人")
    @TableField(value = "order_placer")
    private String orderPlacer;

    /**
     * 下单时间
     */
    @ApiModelProperty(value = "下单时间")
    @TableField(value = "order_time")
    private Date orderTime;

    /**
     * 订单最后一次交货时间
     */
    @ApiModelProperty(value = "订单最后一次交货时间")
    @TableField(value = "time_of_delivery")
    private Date timeOfDelivery;

    /**
     * 订单最后一次确认收货时间
     */
    @ApiModelProperty(value = "订单最后一次确认收货时间")
    @TableField(value = "time_of_last_receipt")
    private Date timeOfLastReceipt;

    /**
     * 发货耗时
     */
    @ApiModelProperty(value = "发货耗时")
    @TableField(value = "used_time")
    private Integer usedTime;

    /**
     * 对账申请时间
     */
    @ApiModelProperty(value = "对账申请时间")
    @TableField(value = "reconciliation_application_time")
    private Date reconciliationApplicationTime;

    /**
     * 对账确认时间
     */
    @ApiModelProperty(value = "对账确认时间")
    @TableField(value = "reconciliation_confirmation_time")
    private Date reconciliationConfirmationTime;

    /**
     * 申请开票时间
     */
    @ApiModelProperty(value = "申请开票时间")
    @TableField(value = "application_for_invoicing_time")
    private Date applicationForInvoicingTime;

    /**
     * 开票时间
     */
    @ApiModelProperty(value = "开票时间")
    @TableField(value = "invoicing_time")
    private Date invoicingTime;

    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间")
    @TableField(value = "paid_time")
    private Date paidTime;

    /**
     * PO订单号
     */
    @ApiModelProperty(value = "PO订单号")
    @TableField(value = "po_order_number")
    private String poOrderNumber;

    /**
     * 部门
     */
    @ApiModelProperty(value = "部门")
    @TableField(value = "department")
    private String department;

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @TableField(value = "contract_id")
    private String contractId;

    /**
     * 支付负责人
     */
    @ApiModelProperty(value = "支付负责人")
    @TableField(value = "payment_manager")
    private String paymentManager;

    /**
     * 验收方式
     */
    @ApiModelProperty(value = "验收方式")
    @TableField(value = "acceptance_method")
    private String acceptanceMethod;

    /**
     * 订单总金额（含税含费）
     */
    @ApiModelProperty(value = "订单总金额（含税含费）")
    @TableField(value = "order_total_amount")
    private BigDecimal orderTotalAmount;

    /**
     * 采购申请号
     */
    @ApiModelProperty(value = "采购申请号")
    @TableField(value = "purch_req_doc_code")
    private String purchReqDocCode;

    /**
     * PR行项目
     */
    @ApiModelProperty(value = "PR行项目")
    @TableField(value = "pr_project_id")
    private String prProjectId;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态")
    @TableField(value = "order_state")
    private String orderState;

    /**
     * 商品后台类目
     */
    @ApiModelProperty(value = "商品后台类目")
    @TableField(value = "commodity_background_category")
    private String commodityBackgroundCategory;

    /**
     * 单品编码
     */
    @ApiModelProperty(value = "单品编码")
    @TableField(value = "item_coding")
    private String itemCoding;

    /**
     * 单品名称
     */
    @ApiModelProperty(value = "单品名称")
    @TableField(value = "item_name")
    private String itemName;

    /**
     * 电商订单编号
     */
    @ApiModelProperty(value = "电商订单编号")
    @TableField(value = "e_commerce_order_number")
    private String eCommerceOrderNumber;

    /**
     * 子订单号
     */
    @ApiModelProperty(value = "子订单号")
    @TableField(value = "suborder_number")
    private String suborderNumber;

    /**
     * 订单金额
     */
    @ApiModelProperty(value = "订单金额")
    @TableField(value = "order_amount")
    private BigDecimal orderAmount;

    /**
     * 应付金额
     */
    @ApiModelProperty(value = "应付金额")
    @TableField(value = "amount_payable")
    private BigDecimal amountPayable;

    /**
     * 结算状态
     */
    @ApiModelProperty(value = "结算状态")
    @TableField(value = "settlement_status")
    private String settlementStatus;

    /**
     * 订单确认时间
     */
    @ApiModelProperty(value = "订单确认时间")
    @TableField(value = "order_confirmation_time")
    private Date orderConfirmationTime;

    /**
     * 订单审批时间
     */
    @ApiModelProperty(value = "订单审批时间")
    @TableField(value = "order_approval_time")
    private Date orderApprovalTime;

    /**
     * PR公司名称
     */
    @ApiModelProperty(value = "PR公司名称")
    @TableField(value = "pr_company_name")
    private String prCompanyName;

    /**
     * 电商渠道订单号
     */
    @ApiModelProperty(value = "电商渠道订单号")
    @TableField(value = "commerce_channel_order_number")
    private String commerceChannelOrderNumber;

    /**
     * 对账人
     */
    @ApiModelProperty(value = "对账人")
    @TableField(value = "reconciler")
    private String reconciler;

    /**
     * 收货负责人
     */
    @ApiModelProperty(value = "收货负责人")
    @TableField(value = "consignee")
    private String consignee;

    /**
     * 收货审核人
     */
    @ApiModelProperty(value = "收货审核人")
    @TableField(value = "receipt_reviewer")
    private String receiptReviewer;

    /**
     * 结算方式
     */
    @ApiModelProperty(value = "结算方式")
    @TableField(value = "settlement_method")
    private String settlementMethod;

    /**
     * 要求到货日期
     */
    @ApiModelProperty(value = "要求到货日期")
    @TableField(value = "request_delivery_date")
    private Date requestDeliveryDate;

    /**
     * 下单人电话
     */
    @ApiModelProperty(value = "下单人电话")
    @TableField(value = "order_phone_number")
    private String orderPhoneNumber;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 订单待支付
     */
    @ApiModelProperty(value = "订单待支付")
    @TableField(value = "order_pay_day")
    private String orderPayDay;

}
