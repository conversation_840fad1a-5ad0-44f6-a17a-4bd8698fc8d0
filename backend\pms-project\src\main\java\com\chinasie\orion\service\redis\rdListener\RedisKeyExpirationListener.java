package com.chinasie.orion.service.redis.rdListener;

import com.alibaba.fastjson.JSON;
import com.chinasie.orion.service.redis.StateChangeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.stereotype.Component;

import static com.chinasie.orion.constant.ExpirationKeysConstant.EXPIRATION_KEYS_KEY;

@RequiredArgsConstructor
@Component
@Slf4j
public class RedisKeyExpirationListener implements MessageListener {

    private final StateChangeService stateChangeService;

    @Override
    public void onMessage(Message message, byte[] pattern) {
        String expiredKey = new String(message.getBody());
        if (expiredKey.startsWith(EXPIRATION_KEYS_KEY)) {
            log.info("【redis缓存到期监听】开始:【{}】 " ,expiredKey);
            String[] keyParts = expiredKey.split(":");
            if(keyParts.length >1){
                log.info("【redis缓存到期监听】进入:【{}】 【{}】" , JSON.toJSONString(keyParts),stateChangeService);
                stateChangeService.processExpiredKey(keyParts[1]);
            }
        }
    }


}