<script setup lang="ts">
import {
  Icon, isPower, Layout, OrionTable,
} from 'lyra-component-vue3';
import {
  h, nextTick, provide, ref, Ref, watch,
} from 'vue';
import { RadioButton, RadioGroup } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { openSQEManageForm } from '/@/views/pms/SQEManage/utils';
import dayjs from 'dayjs';
import ChartView from './components/ChartView.vue';
import Api from '/@/api';
import { usePagePower } from '/@/views/pms/hooks';

const router = useRouter();
const { powerData, getPowerDataHandle } = usePagePower();
provide('powerData', powerData);
const type: Ref<'list' | 'chart'> = ref();

watch(() => powerData.value, (value) => {
  if (!type.value) {
    if (isPower('PMS_AZHGL_container_01', value)) {
      type.value = 'list';
    } else if (isPower('PMS_AZHGL_container_02', value)) {
      type.value = 'chart';
    }
  } else if (isPower('PMS_AZHGL_container_01', value)) {
    type.value = 'list';
  } else if (isPower('PMS_AZHGL_container_02', value)) {
    type.value = 'chart';
  }
}, {
  immediate: true,
  deep: true,
});

const tableRef: Ref = ref();
const selectedKeys: Ref<string[]> = ref([]);
const selectedRows: Ref<any[]> = ref([]);
const tableOptions = {
  rowSelection: {
    onChange(keys: string[], rows: any[]) {
      selectedKeys.value = keys || [];
      selectedRows.value = rows || [];
    },
  },
  showTableSetting: false,
  smallSearchField: [
    'eventTopic',
    'hiddenDangerCode',
    'eventDesc',
    'rspDeptCode',
    'rspDeptName',
  ],
  api: (params: any) => new Api('/pms/safety-quality-env/page').fetch({
    ...params,
    power: {
      pageCode: 'PMSSQEManage',
      containerCode: 'PMS_AZHGL_container_01',
    },
  }, '', 'POST'),
  showToolButton: false,
  columns: [
    {
      title: '隐患编号',
      width: 135,
      dataIndex: 'hiddenDangerCode',
    },
    {
      title: '事件主题',
      width: 330,
      dataIndex: 'eventTopic',
    },
    {
      title: '事件等级',
      width: 90,
      dataIndex: 'eventLevel',
    },
    {
      title: '事件地点',
      width: 106,
      dataIndex: 'eventLocation',
    },
    {
      title: '检查人',
      width: 90,
      dataIndex: 'reviewerName',
    },
    {
      title: '检查人所在部门',
      width: 180,
      dataIndex: 'deptName',
    },
    {
      title: '事件描述',
      width: 850,
      dataIndex: 'eventDesc',
    },
    {
      title: '事件类型',
      width: 310,
      dataIndex: 'eventType',
    },
    {
      title: '事发日期',
      dataIndex: 'occurrenceDate',
      width: 100,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '是否大修',
      width: 85,
      dataIndex: 'isMajorRepair',
      customRender({ text }) {
        return text ? '是' : '否';
      },
    },
    {
      title: '隐患/事件领域',
      width: 130,
      dataIndex: 'hiddenEvent',
    },
    {
      title: '是否已关闭',
      width: 110,
      dataIndex: 'isClosed',
      customRender({ text }) {
        return text ? '是' : '否';
      },
    },
    {
      title: '当前流程',
      width: 110,
      dataIndex: 'currentProcess',
    },
    {
      title: '直接责任部门',
      width: 180,
      dataIndex: 'rspDeptName',
    },
    {
      title: '金字塔类别',
      width: 140,
      dataIndex: 'pyramidCategoryName',
    },
    {
      title: '大修轮次',
      width: 90,
      dataIndex: 'majorRepairTurn',
    },
    {
      title: '是否考核',
      width: 90,
      dataIndex: 'isAssessed',
      customRender({ text }) {
        return text ? '是' : '否';
      },
    },
    {
      title: '考核级别',
      width: 120,
      dataIndex: 'assessmentLevelName',
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 120,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '查看',
      isShow: (record) => isPower('PMS_AZHGL_container_01_button_01', record?.rdAuthList),
      onClick: (record: { id: string }) => navDetails(record?.id),
    },
    {
      text: '编辑',
      isShow: (record) => isPower('PMS_AZHGL_container_01_button_02', record?.rdAuthList) && record?.edit,
      onClick(record: { id: string }) {
        openSQEManageForm(record, updateTable);
      },
    },
  ],
};

async function updateTable() {
  await nextTick();
  tableRef.value.reload();
}

function navDetails(id: string) {
  router.push({
    name: 'PMSSQEManageDetails',
    params: {
      id,
    },
  });
}

</script>

<template>
  <Layout
    v-get-power="{pageCode:'PMSSQEManage', getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      v-show="type==='list'"
      ref="tableRef"
      class="radio-button-table"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <RadioGroup
          v-if="isPower('PMS_AZHGL_container_01',powerData) && isPower('PMS_AZHGL_container_02',powerData)"
          v-model:value="type"
          class="mla"
          button-style="solid"
        >
          <RadioButton value="list">
            <Icon icon="orion-icon-align-left" />
          </RadioButton>
          <RadioButton value="chart">
            <Icon icon="orion-icon-appstore" />
          </RadioButton>
        </RadioGroup>
      </template>
    </OrionTable>
    <ChartView
      v-if="type==='chart'"
      :type="type"
      @updateType="(value:'chart'|'list')=>{
        type=value
      }"
    />
  </Layout>
</template>

<style scoped lang="less">
:deep(.radio-button-table) {
  width: auto;
  flex: 0;

  .ant-input-search {
    width: 215px;
    margin-left: 12px;
  }
}

.mla {
  margin-left: auto;
}
</style>
