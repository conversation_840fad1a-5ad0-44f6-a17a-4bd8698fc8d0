package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "项目下用户信息")
public class ProjectUserInfoVO extends ObjectVO implements Serializable {

    @ApiModelProperty("ID")
    private String id;
    @ApiModelProperty("编号")
    private String code;
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("部门id")
    private String deptId;
    @ApiModelProperty("部门名称")
    private String deptName;
    @ApiModelProperty("科室id")
    private String sectionId;
    @ApiModelProperty("科室名称")
    private String sectionName;
    @ApiModelProperty("班组id")
    private String teamsId;
    @ApiModelProperty("班组名称")
    private String teamsName;

}
