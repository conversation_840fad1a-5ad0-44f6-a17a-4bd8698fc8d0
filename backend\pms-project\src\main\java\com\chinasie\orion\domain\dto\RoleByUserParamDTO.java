package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2023/04/28/9:54
 * @description:
 */
@Data
@ApiModel(value = "RoleByUserParamDTO对象", description = "角色通过用户参数对象")
public class RoleByUserParamDTO {
    @ApiModelProperty(value = "用户ID")
    @NotEmpty(message = "用户ID不能为空")
    private String userId;
    @ApiModelProperty(value = "项目ID")
    @NotEmpty(message = "项目ID不能为空")
    private String projectId;

    public RoleByUserParamDTO() {
    }

    public RoleByUserParamDTO(String projectId, String userId) {
        this.projectId = projectId;
        this.userId = userId;
    }
}
