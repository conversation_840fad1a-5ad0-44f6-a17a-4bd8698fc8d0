package com.chinasie.orion.service;

import java.util.Date;
import java.util.List;
import com.chinasie.orion.domain.dto.ProjectMaterialPreparationInfoDTO;
import com.chinasie.orion.domain.entity.ProjectMaterialPreparationInfo;
import com.chinasie.orion.domain.vo.ProjectMaterialPreparationInfoImportVO;
import com.chinasie.orion.domain.vo.ProjectMaterialPreparationInfoVO;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import java.lang.String;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * ProjectMaterialPreparationInfo 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-25 15:53:32
 */
public interface ProjectMaterialPreparationInfoService  extends  OrionBaseService<ProjectMaterialPreparationInfo>  {


    /**
     *  新增
     *
     * * @param projectMaterialPreparationInfoDTO
     */
    Boolean create(List<ProjectMaterialPreparationInfoDTO> list, Date requireCompleteTime, String preparationId) throws Exception;

    /**
     *  编辑
     *
     * * @param projectMaterialPreparationInfoDTO
     */
    Boolean edit(List<ProjectMaterialPreparationInfoDTO> list, Date requireCompleteTime, String preparationId) throws Exception;

    /**
     * 通过备料id获取备料信息
     * @param preparationId
     * @return
     * @throws Exception
     */
    List<ProjectMaterialPreparationInfoVO>  getList(String preparationId) throws Exception;

    /**
     * 导入
     * @param excel
     * @return
     * @throws Exception
     */
    ProjectMaterialPreparationInfoImportVO importCheckByExcel(MultipartFile excel) throws Exception;

    /**
     *  下载模板
     *
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response)throws Exception;

    /**
     * 升版
     * @param preparationInfoList
     * @param requireCompleteTime
     * @param oldPreparationId
     * @param newPreparationId
     * @throws Exception
     */
    void upgrade(List<ProjectMaterialPreparationInfoDTO> preparationInfoList,
                 Date requireCompleteTime,
                 String oldPreparationId,
                 String newPreparationId) throws Exception;
}
