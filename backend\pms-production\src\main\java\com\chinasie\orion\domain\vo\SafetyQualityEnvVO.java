package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/07/15:01
 * @description:
 */

@ApiModel(value = "SafetyQualityEnvVO对象", description = "安质环")
@Data
public class SafetyQualityEnvVO extends ObjectVO implements Serializable {

    /**
     * 事件主题
     */
    @ApiModelProperty(value = "事件主题")
    private String eventTopic;


    /**
     * 事件等级
     */
    @ApiModelProperty(value = "事件等级")
    private String eventLevel;


    /**
     * 事件地点
     */
    @ApiModelProperty(value = "事件地点")
    private String eventLocation;


    /**
     * 事件位置
     */
    @ApiModelProperty(value = "事件位置")
    private String eventPosition;


    /**
     * 分类类型
     */
    @ApiModelProperty(value = "分类类型")
    private String classificationType;


    /**
     * 隐患类型
     */
    @ApiModelProperty(value = "隐患类型")
    private String hiddenDangerType;


    /**
     * 事发日期
     */
    @ApiModelProperty(value = "事发日期")
    private Date occurrenceDate;


    /**
     * 责任中心
     */
    @ApiModelProperty(value = "责任中心")
    private String rspCenter;


    /**
     * 是否大修
     */
    @ApiModelProperty(value = "是否大修")
    private Boolean isMajorRepair;


    /**
     * 隐患/事件领域
     */
    @ApiModelProperty(value = "隐患/事件领域")
    private String hiddenEvent;


    /**
     * 事件类型
     */
    @ApiModelProperty(value = "事件类型")
    private String eventType;


    /**
     * 是否已关闭
     */
    @ApiModelProperty(value = "是否已关闭")
    private Boolean isClosed;


    /**
     * 当前流程
     */
    @ApiModelProperty(value = "当前流程")
    private String currentProcess;


    /**
     * 金字塔类别
     */
    @ApiModelProperty(value = "金字塔类别")
    private String pyramidCategory;
    @ApiModelProperty(value = "金字塔类别名称")
    private String pyramidCategoryName;

    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    private String majorRepairTurn;


    /**
     * 是否考核
     */
    @ApiModelProperty(value = "是否考核")
    private Boolean isAssessed;
    /**
     * 考核级别
     */
    @ApiModelProperty(value = "考核级别")
    private String assessmentLevel;

    @ApiModelProperty(value = "考核级别名称")
    private String assessmentLevelName;

    /**
     * 隐患编号
     */
    @ApiModelProperty(value = "隐患编号")
    private String hiddenDangerCode;

    @ApiModelProperty(value = "检查人ID")
    private String reviewerId;


    @ApiModelProperty(value = "检查人编号")
    private String reviewerNumber;

    @ApiModelProperty(value = "检查人名称")
    private String reviewerName;

    @ApiModelProperty(value = "检查人所在部门Id")
    private String deptId;

    @ApiModelProperty(value = "检查人所在部门code")
    private String deptCode;

    @ApiModelProperty(value = "检查人所在部门")
    private String deptName;


    @ApiModelProperty(value = "直接责任部门Id")
    private String rspDeptId;

    @ApiModelProperty(value = "直接责任部门名称")
    private String rspDeptName;
    @ApiModelProperty(value = "直接责任部门Id")
    private String rspDeptCode;

    /**
     * 事件类型
     */
    @ApiModelProperty(value = "事件描述")
    private String eventDesc;

    /**
     * 事件地点
     */
    @ApiModelProperty(value = "事件地点编码")
    private String eventLocationCode;
    /**
     * 事件地点
     */
    @ApiModelProperty(value = "事件地点编码名称")
    private String eventLocationCodeName;

    /**
     * 地点编码
     */
    @ApiModelProperty(value = "地点编码")
    private String baseCode;

    /**
     * 责任中心
     */
    @ApiModelProperty(value = "责任中心id")
    private String rspCenterId;
    @ApiModelProperty(value = "是否有编辑权限")
    private boolean edit;
//    @ApiModelProperty(value = "是否可阅读")
//    private boolean read;
//@ApiModelProperty(value = "责任中心id")
//private String checkPerson;
//    @ApiModelProperty(value = "责任中心id")
//    private String checkPersonDept;
}

