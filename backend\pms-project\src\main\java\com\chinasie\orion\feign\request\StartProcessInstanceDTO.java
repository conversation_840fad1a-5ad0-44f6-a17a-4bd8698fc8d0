package com.chinasie.orion.feign.request;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

/**
 * @author: yk
 * @date: 2023/9/18 11:01
 * @description:
 */
@Data
@ApiModel("启动流程实例需要传入的参数")
public class StartProcessInstanceDTO {
    @ApiModelProperty("流程定义id")
    private String processDefinitionId;
    @ApiModelProperty("业务key")
    @NotBlank(message = "业务key")
    private String businessKey;
    @ApiModelProperty("表单数据")
    private JSONObject formData;
    @ApiModelProperty("变量")
    private Map<String, Object> variables;
    @ApiModelProperty("下一步处理人")
    private List<String> nextAssigneeList;
}
