package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: yk
 * @date: 2023/10/24 11:34
 * @description:
 */
@ApiModel(value = "ProjectContractMainInfoVO对象", description = "项目合同主要信息")
@Data
public class ProjectContractChangeApplyMainInfoVO {
    /**
     * 合同基本信息
     */
    @ApiModelProperty(value = "合同基本信息")
    private ProjectContractVO projectContractVO;

    /**
     * 合同变更申请信息
     */
    @ApiModelProperty(value = "合同变更申请信息")
    private ProjectContractChangeApplyVO projectContractChangeApplyVO;


    /**
     * 合同变更内容
     */
    @ApiModelProperty(value = "合同变更内容")
    private List<ProjectContractChangeVO> projectContractChangeVOList;
}
