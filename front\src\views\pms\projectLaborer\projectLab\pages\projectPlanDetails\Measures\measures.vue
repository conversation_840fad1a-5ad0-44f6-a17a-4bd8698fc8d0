<template>
  <div class="plan-tab">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      @selectionChange="selectionChange"
    >
      <template
        #toolbarLeft
      >
        <div class="button-margin-right">
          <BasicButton
            v-if="isPower('PMS_OJJHXQ_container_02_07_button_01',powerData)"
            type="primary"
            icon="sie-icon-tianjiaxinzeng"
            @click="quoteRisk"
          >
            关联质控措施
          </BasicButton>
          <BasicButton
            v-if="isPower('PMS_OJJHXQ_container_02_07_button_02',powerData)"
            icon="sie-icon-shanchu"
            :disabled="selectedRowKeys.length===0"
            @click="deleteBatch"
          >
            移除
          </BasicButton>
        </div>
      </template>
    </OrionTable>
  </div>
</template>
<script lang="ts" setup>
import {
  BasicButton, OrionTable, DataStatusTag, useDrawer, openModal, isPower,
} from 'lyra-component-vue3';
import {
  inject, ref, h, Ref,
} from 'vue';
import Api from '/@/api';
import router from '/@/router';
import { message, Modal, Tag } from 'ant-design-vue';
import { getExecuteItem } from '/@/views/pms/projectLibrary/pages/components/qualityControlItem/utill';
import AssociationMeasuresModal from './components/AssociationMeasuresModal.vue';

const tableRef = ref();
const formData:Record<any, any> = inject('formData', {});
const selectedRowKeys:Ref<string[]> = ref([]);
const powerData = inject('powerData', {});
function selectionChange(data) {
  selectedRowKeys.value = data.keys;
}

function quoteRisk() {
  const quoteRiskPoolRef = ref();
  openModal({
    title: '关联质控措施',
    width: 1100,
    height: 700,
    content(h) {
      return h(AssociationMeasuresModal, {
        ref: quoteRiskPoolRef,
        formId: formData.value.id,
        projectId: formData.value.projectId,
      });
    },
    async onOk() {
      await quoteRiskPoolRef.value.saveData();
      updateData();
    },
  });
}
function deleteBatch() {
  deleteBatchData(selectedRowKeys.value, 'batch');
}
function deleteBatchData(params, type) {
  Modal.confirm({
    title: '移除提示',
    content: type === 'batch' ? '是否移除选中的数据？' : '是否移除该条数据？',
    onOk() {
      new Api('/pms').fetch({
        toId: formData.value.id,
        fromIds: params,
      }, 'projectScheme/relation/qualityItem/remove', 'DELETE')
        .then((res) => {
          message.success('移除成功');
          updateData();
        });
    },
  });
}
function updateData() {
  tableRef.value.reload();
}
const tableOptions = {
  showIndexColumn: false,
  pagination: false,
  bordered: false,
  deleteToolButton: 'add|delete|enable|disable',
  rowSelection: {},
  smallSearchField: ['point'],
  api(params) {
    if (!formData.value.id) {
      return Promise.resolve([]);
    }
    return new Api('/pms').fetch('', `projectScheme/relation/qualityItem/lists/${formData.value.id}`, 'POST');
  },
  columns: initColumns(),
  actions: [
    {
      text: '删除',
      isShow: (record) => record.isCurrentCreate && isPower('PMS_OJJHXQ_container_02_07_button_02', powerData),
      onClick(record) {
        Modal.confirm({
          title: '删除提示',
          content: '是否删除该条数据？',
          onOk() {
            new Api('/pas').fetch({
              riskId: record.id,
            }, `questionRelationRisk/relationRisk/deleteRisk/${formData.value.id}`, 'DELETE').then(() => {
              message.success('删除成功');
              tableRef.value.reload();
            });
          },
        });
      },
    },
    {
      text: '移除',
      isShow: (record) => !record.isCurrentCreate && isPower('PMS_OJJHXQ_container_02_07_button_02', powerData),
      onClick(record) {
        deleteBatchData([record.id], 'one');
      },
    },
  ],
};
function initColumns() {
  let columns:Record<any, any>[] = [
    {
      title: '质控点编码',
      dataIndex: 'number',
    },
    {
      title: '质控点',
      dataIndex: 'point',
      customRender({
        record, text,
      }) {
        return h('span', {
          class: 'action-btn',
          onClick() {
            openModal.closeAll();
            router.push({
              name: 'QualityControlItemDetail',
              params: {
                id: record.id,
              },
            });
          },
        }, text);
      },
    },
    {
      title: '控制方案',
      dataIndex: 'scheme',
      minWidth: 260,
    },
    {
      title: '状态',
      dataIndex: 'dataStatus',
      customRender: ({ record }) => h(DataStatusTag, { statusData: record.dataStatus }),
    },
    {
      title: '执行情况',
      dataIndex: 'execute',
      customRender: ({ text }) => {
        const { color, child } = getExecuteItem(text);
        return h(Tag, { color }, child);
      },
    },
    {
      title: '是否关联项目计划',
      dataIndex: 'relevanceScheme',
      customRender: ({ text }) => (text ? '关联' : '未关联'),
    },
    {
      title: '质控措施类型',
      dataIndex: 'typeName',
    },
    {
      title: '质控阶段',
      dataIndex: 'stage',
    },
    {
      title: '过程',
      dataIndex: 'processName',
    },
    {
      title: '质控活动',
      dataIndex: 'activityName',
    },
    {
      title: '负责人',
      dataIndex: 'resPersonName',
    },
    {
      title: '完成确认人',
      dataIndex: 'affirmName',
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      type: 'dateTime',
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      slots: { customRender: 'action' },
    },
  ];
  return columns;
}
</script>
