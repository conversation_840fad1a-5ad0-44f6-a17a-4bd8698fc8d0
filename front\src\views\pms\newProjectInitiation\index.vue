<script setup lang="ts">
import {
  h, onMounted, Ref, ref,
} from 'vue';
import { useRouter } from 'vue-router';
import { Modal, RangePicker } from 'ant-design-vue';
import {
  get as loadGet, isEmpty, cloneDeep,
} from 'lodash-es';
import {
  Layout, OrionTable, isPower,
} from 'lyra-component-vue3';
import Api from '/@/api';

const router = useRouter();
const tableRef: Ref = ref();
const selectRows: Ref = ref([]);
const tableOptions = {
  showToolButton: false,
  rowSelection: {},
  isFilter2: true,
  smallSearchField: ['projectName', 'projectNumber'],
  api: (params) => new Api('/pms/projectInitiation/page').fetch(setSearch(params), '', 'POST'),
  columns: [
    {
      title: '立项编号',
      dataIndex: 'projectNumber',
      width: 140,
      slots: { customRender: 'name' },
    },
    {
      title: '立项名称',
      dataIndex: 'projectName',
    },
    {
      title: '立项标签',
      dataIndex: 'projectLabel',
      maxWidth: 120,
    },
    {
      title: '项目类型',
      dataIndex: 'projectType',
      width: 120,
    },
    {
      title: '承担中心',
      dataIndex: 'projectAssumeCenter',
    },
    {
      title: '项目负责人',
      dataIndex: 'projectPerson',
      width: 100,
    },
    {
      title: '状态',
      dataIndex: 'projectStatus',
      width: 100,
    },
    // {
    //   title: '发起日期',
    //   dataIndex: 'projectInitDate',
    //   customRender({ text }) {
    //     // return text ? dayjs(text).format('YYYY-MM-DD') : '';
    //     return text ? dayjs(text).format('YYYY-MM-DD') : '';
    //   },
    //   width: 160,
    // },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 100,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '查看',
      onClick: handleDetail,
      isShow: (record) => isPower('PMS_ZGHXMLX_container_01_button_01', record.rdAuthList),
    },
  ],
  filterConfig: {
    fields: [
      {
        field: 'projectLabel',
        fieldName: '标签',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Select',
        hidden: false,
        searchFieldName: null,
        constValue: JSON.stringify([
          {
            label: '项目立项',
            name: '项目立项',
            value: 100,
          },
          {
            label: '项目预立项',
            name: '项目预立项',
            value: 101,
          },
        ]),
      },
      {
        field: 'projectInitDate',
        fieldName: '申请日期',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterface: null,
        referenceInterfaceMethod: null,
        referenceInterfaceParams: null,
        component: 'Select',
        hidden: false,
        constValue: null,
        fieldNames: null,
        searchFieldName: null,
        optionsCustomRender({ filterItem, filterMethods, groupRelation }) {
          return h(RangePicker, {
            style: {
              width: '100%',
            },
            onChange(date: any) {
              filterMethods.setFieldValue(filterItem.field, date, groupRelation);
            },
            valueFormat: 'YYYY-MM-DD',
          });
        },
      },
    ],
  },
};

function setSearch(params) {
  const originSearchConditions = loadGet(params, 'searchConditions', []);
  const query = {};
  let i = 0;
  const searchConditions = cloneDeep(originSearchConditions).reduce((prev, cur) => {
    for (; i < cur.length; i++) {
      const single = cur[i];
      const filedProp = loadGet(single, 'field', '');
      if (['projectLabel', 'projectInitDate'].includes(filedProp)) {
        const [first, second] = loadGet(single, 'values', []);
        if (filedProp === 'projectLabel') {
          Object.assign(query, {
            projectLabel: first,
          });
        }
        if (filedProp === 'projectInitDate') {
          Object.assign(query, {
            startDate: first,
            endDate: second,
          });
        }
        cur.splice(i, 1, undefined);
      }
    }
    const lastCur = cur.filter(Boolean);
    if (lastCur.length) {
      return [...prev, lastCur];
    }
    return prev;
  }, []);
  const newSearchConditions = {
    searchConditions: searchConditions.length ? searchConditions : [],
    query: isEmpty(query) ? undefined : query,
  };
  return {
    ...params,
    ...newSearchConditions,
    power: {
      pageCode: 'ZghProjectInitiationpms001',
      containerCode: 'PMS_ZGHXMLX_container_01',
    },
  };
}

function handleDetail(record) {
  router.push({
    name: 'ZghProjectInitiationDetail',
    params: {
      id: record.id,
    },
    query: {
      projectId: record.id,
      projectCode: record.projectNumber,
    },
  });
}

function updateTable() {
  tableRef.value?.reload();
}

// 批量删除
function batchDelete(ids) {
  return new Promise((resolve, reject) => {
    new Api('/pms/projectApproval').fetch(ids, '', 'DELETE')
      .then(() => {
        updateTable();
        resolve(true);
      })
      .catch(() => {
        reject();
      });
  });
}

// 批量删除
function handleBatchDel() {
  Modal.confirm({
    title: '删除提示',
    content: '确认要删除已选择的记录？',
    onOk: () => batchDelete(selectRows.value.map((item) => item.id)),
  });
}

// 表格勾选回调
function selectionChange({ rows }) {
  selectRows.value = rows;
}
function getPowerDataHandle(data) {
}
</script>

<template>
  <Layout
    v-get-power="{pageCode: 'ZghProjectInitiationpms001',getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      :onSelectionChange="selectionChange"
    >
      <template #name="{text,record}">
        <div
          class="action-btn"
          @click="handleDetail(record)"
        >
          {{ text }}
        </div>
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">

</style>
