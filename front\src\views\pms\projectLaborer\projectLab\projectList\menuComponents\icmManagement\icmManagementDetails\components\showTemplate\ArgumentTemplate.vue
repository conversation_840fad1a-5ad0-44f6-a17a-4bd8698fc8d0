<template>
  <div class="argument-template">
    <div
      v-if="modalType==='edit'"
      class="template-top"
    >
      <template
        v-for="(item,index) in state.templateTypeList"
        :key="index"
      >
        <div
          class="top-item"
          :class="{'top-item-active':state.templateData.type===item.value}"
          @click="changeType(item)"
        >
          <span>{{ item.label }}</span>
        </div>
      </template>
    </div>
    <div class="template-content">
      <div
        v-if="state.templateData.type===1"
        class="content-item"
      >
        <div
          v-if="props.modalType==='edit'"
          class="content-item-label"
        >
          文本型参数：
        </div>
        <div class="content-item-value">
          <AInputTextarea
            v-model:value="state.templateData.txtDesc"
            placeholder="请输入文本参数"
            :disabled="props.modalType==='check'"
          />
        </div>
      </div>
      <div
        v-else-if="state.templateData.type===2"
        class="content-item"
      >
        <div
          v-if="props.modalType==='edit'"
          class="content-item-label"
        >
          数值型参数：
        </div>
        <div class="content-item-number">
          <ASelect
            v-model:value="state.templateData.numValueType"
            :options="state.numberOptions"
            class="input-val"
            :disabled="props.modalType==='check'"
          />
          <AInputNumber
            v-model:value="state.templateData.numValue"
            :formatter="formatter"
            :parser="formatter"
            class="input-val"
            :disabled="props.modalType==='check'"
          />
          <AInput
            v-model:value="state.templateData.numValueUnit"
            class="input-val"
            :disabled="props.modalType==='check'"
          />
        </div>
      </div>
      <div
        v-else-if="state.templateData.type===3"
        class="content-item"
      >
        <div
          v-if="props.modalType==='edit'"
          class="content-item-label content-item-label1"
        >
          <span>表格型参数：</span>
          <div class="">
            <input
              v-show="false"
              id="upload"
              ref="inputRef"
              type="file"
              accept=".xlsx, .xls"
              @change="parseExcel"
            >
            <BasicButton
              icon="sie-icon-daoru"
              @click="uploadTable"
            >
              从Excel导入
            </BasicButton>
          </div>
        </div>
        <div class="content-item-table">
          <div
            class="content-item-left"
            :class="{'content-item-check':props.modalType==='check'}"
            @paste="handlePaste"
          >
            <ATable
              ref="tableRef"
              :data-source="dataSource"
              :columns="columns"
              bordered
              :scroll="{ x: '100%',y:300 }"
              :pagination="false"
            >
              <template #headerCell="{ column }">
                <div
                  v-if="column.dataIndex !== 'actions'"
                  class="header-cell"
                >
                  <span>{{ column.title }}</span>
                  <div class="header-cell-icon">
                    <Icon
                      v-if="props.modalType==='edit'"
                      icon="sie-icon-bianji"
                      @click="editColumn(column)"
                    />
                    <Icon
                      v-if="props.modalType==='edit'"
                      icon="sie-icon-shanchu"
                      @click="deleteColumns(column)"
                    />
                  </div>
                </div>
              </template>
              <template #bodyCell="{ column,record }">
                <template v-if="column.dataIndex !== 'actions'">
                  <AInput
                    v-if="deleteListKey.includes(record.id)"
                    v-model:value="record[column.dataIndex]"
                  />
                  <span v-else>{{ record[column.dataIndex] }}</span>
                </template>
                <template v-if="column.dataIndex === 'actions'">
                  <div class="action-item">
                    <template v-if="!deleteListKey.includes(record.id)">
                      <span
                        class="action-btn"
                        @click="editRow(record)"
                      >编辑</span>
                      <ADivider type="vertical" />
                      <span
                        class="action-btn"
                        @click="deleteRow(record)"
                      >删除</span>
                    </template>
                    <template v-else>
                      <span
                        class="action-btn"
                        @click="saveRow(record)"
                      >确认</span>
                    </template>
                  </div>
                </template>
              </template>
            </ATable>
            <span
              v-if="props.modalType==='edit'"
              class="add-row"
              @click="addRow"
            >添加行</span>
          </div>
          <div
            v-if="props.modalType==='edit'"
            class="content-item-right"

            @click="addColumns"
          >
            <span>添加列</span>
          </div>
        </div>
      </div>
      <div
        v-else-if="state.templateData.type===4"
        class="content-item"
      >
        <div
          v-if="props.modalType==='edit'"
          class="content-item-label-img"
        >
          <span> 图片型参数：</span>
          <BasicUpload
            class="allUpload"
            :multiple="true"
            button-text="上传图片"
            button-type="default"
            :accept="'.jpg,.png'"
            :is-classification="false"
            @saveChange="fileSave"
          />
        </div>
        <div class="content-item-img">
          <template
            v-for="(item,index) in state.templateData.images"
            :key="item.filePath"
          >
            <div class="item-img">
              <ViewFile :file="item" />
              <Icon
                v-if="props.modalType==='edit'"
                icon="sie-icon-close"
                @click="deleteImgs(index)"
              />
            </div>
          </template>
        </div>
      </div>
      <div
        v-else-if="state.templateData.type===5"
        class="content-item"
      >
        <div
          v-if="props.modalType==='edit'"
          class="content-item-label"
        >
          公式型参数：
        </div>
        <div class="latex-content">
          <div
            v-if="modalType==='edit'"
            class="latex-edit"
          >
            <AInputTextarea
              v-model:value="state.templateData.equation"
              placeholder="请输入公式参数"
            />
          </div>
          <!--            :expression="'\\frac{a_i}{1+x}'"-->

          <!--            :expression="'\\'+state.templateData.equation"-->
          <div class="latex-details">
            <vue-latex
              :expression="state.templateData.equation"
              display-mode
              :fontsize="40"
            />
          </div>
        </div>
      </div>
      <div
        v-else-if="state.templateData.type===6"
        class="content-item"
      >
        <div
          v-if="props.modalType==='edit'"
          class="content-item-label"
        >
          链接内容：
        </div>
        <div class="content-item-href">
          <AInput
            v-model:value="state.templateData.href"
            placeholder="请输入链接"
            :disabled="props.modalType==='check'"
          />
        </div>
      </div>
      <div
        v-if="state.templateData.type===7"
        class="content-item"
      >
        <div
          v-if="props.modalType==='edit'"
          class="content-item-label"
        >
          附件型参数：
        </div>
        <div class="content-item-upload">
          <UploadList
            :edit="props.modalType==='edit'"
            :type="props.modalType==='edit'?'modal':'page'"
            :listData="state.templateData.attachments"
          />
        </div>
      </div>
    </div>
    <AddColumn
      @register="registerModal"
      @confirmColumn="confirmColumn"
    />
  </div>
</template>
<script lang="ts" setup>
import {
  UploadList, BasicUpload, ViewFile, Icon, BasicButton, useModal,
} from 'lyra-component-vue3';
import {
  ref, defineProps, defineExpose, reactive, watch, onMounted, computed, Ref, unref,
} from 'vue';
import dayjs from 'dayjs';
import {
  Select as ASelect,
  Input as AInput,
  InputNumber as AInputNumber,
  Textarea as AInputTextarea,
  Table as ATable,
  message,
  Divider as ADivider,
} from 'ant-design-vue';
import * as XLSX from 'xlsx';
import AddColumn from './AddColumn.vue';

const props = defineProps({
  data: {
    type: Object,
    default: () => ({
      type: '',
      txtDesc: '',
    }),
  },
  modalType: {
    type: String,
    default: 'check',
  },
});
const state = reactive({
  canChange: true,
  type: 5,
  templateData: {},
  templateTypeList: [
    {
      label: '文本',
      value: 1,
    },
    {
      label: '数值',
      value: 2,
    },
    {
      label: '表格',
      value: 3,
    },
    {
      label: '图片',
      value: 4,
    },
    {
      label: '公式',
      value: 5,
    },
    {
      label: '链接',
      value: 6,
    },
    {
      label: '附件',
      value: 7,
    },
  ],
  numberOptions: [
    {
      label: '整点型',
      value: 1,
    },
    {
      label: '浮点型',
      value: 2,
    },
  ],
  actionsColumn: [
    {
      title: '操作',
      dataIndex: 'actions',
      fixed: 'right',
      width: 130,
    },
  ],
  tableColumns: [],
});

const deleteListKey: Ref<string[]> = ref([]);
const [registerModal, { openModal }] = useModal();
const columns = computed(() => {
  if (props.modalType === 'edit') {
    return [...state.tableColumns, ...state.actionsColumn];
  }
  return [...state.tableColumns];
});
const dataSource = ref([]);
const oldRecord = ref({});
onMounted(() => {
  state.templateData = props.data;
  if (state.templateData.type === 3) {
    let tableData = JSON.parse(state.templateData.tableFormat);
    dataSource.value = tableData?.dataSource;
    state.tableColumns = tableData?.columns;
  }
});

watch(
  () => props.data,
  (val) => {
    deleteListKey.value = [];
    state.templateData = val;
    if (state.templateData.type === 3) {
      let tableData = JSON.parse(state.templateData.tableFormat);
      dataSource.value = tableData?.dataSource;
      state.tableColumns = tableData?.columns;
    }
  },
);

function changeType(data) {
  if (!state.canChange) return;
  if (state.templateData.type === data.value) return;
  if (data.value === 1) {
    state.templateData.txtDesc = '';
  }
  if (data.value === 2) {
    state.templateData.numValueType = '';
    state.templateData.numValue = '';
    state.templateData.numValueUnit = '';
  }
  if (data.value === 3) {
    state.tableColumns = [];
    dataSource.value = [];
  }
  if (data.value === 4) {
    state.templateData.images = [];
  }
  if (data.value === 5) {
    state.templateData.equation = '';
  }
  if (data.value === 6) {
    state.templateData.href = '';
  }
  if (data.value === 7) {
    state.templateData.attachments = [];
  }

  state.templateData.type = data.value;
}

function formatter(val) {
  if (state.templateData.numValueType === 2) {
    return val;
  }
  return parseInt(val);
}

function fileSave(fields) {
  let image = fields.map((item) => item.result);
  state.templateData.images = state.templateData.images.concat(image);
}

function deleteImgs(index) {
  state.templateData.images.splice(index, 1);
}

function initData() {

}

function getData() {
  return {
    columns: state.tableColumns,
    dataSource: dataSource.value,
  };
}

function addRow() {
  if (state.tableColumns.length === 0) {
    message.warning('请先添加列');
    return;
  }
  let record = {
    id: generateRandomCode(),
  };
  dataSource.value.push(record);
  deleteListKey.value.push(record.id);
}

function generateRandomCode() {
  let characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let code = '';
  for (let i = 0; i < 6; i++) {
    let randomIndex = Math.floor(Math.random() * characters.length);
    code += characters.charAt(randomIndex);
  }
  return code;
}

function addColumns() {
  let randomCode = generateRandomCode();
  let tableColumnItem = {
    title: '',
    dataIndex: randomCode,
    width: 200,
  };
  openModal(true, {
    type: 'add',
    data: tableColumnItem,
  });
}

function editColumn(tableColumnItem) {
  openModal(true, {
    type: 'edit',
    data: tableColumnItem,
  });
}

function confirmColumn(type, data) {
  if (type === 'add') {
    state.tableColumns.push(data);
  } else {
    let columnItem = state.tableColumns.find((item) => item.dataIndex === data.dataIndex);
    columnItem.title = data.title;
  }
}

function editRow(record) {
  oldRecord.value = JSON.parse(JSON.stringify(record));
  deleteListKey.value.push(record.id);
}

function saveRow(record) {
  deleteListKey.value = deleteListKey.value.filter((item) => item !== record.id);
}

function deleteColumns(columns) {
  state.tableColumns = state.tableColumns.filter((item) => item.dataIndex !== columns.dataIndex);
}

function deleteRow(record) {
  dataSource.value = dataSource.value.filter((item) => item.id !== record.id);
}

const inputRef = ref();

function uploadTable() {
  const inputRefDom = unref(inputRef);
  inputRefDom && inputRefDom.click();
}

async function parseExcel(event) {
  const file = event.target.files[0];
  const jsonData = await readerField(file);
  if (jsonData.length > 0) {
    let tableColumns = [];
    let tableRows = [];
    let tableColumnsKey = {};
    let row = jsonData[0];
    for (let key in row) {
      let randomCode = generateRandomCode();
      tableColumns.push({
        title: key,
        dataIndex: randomCode,
        width: 200,
      });
      tableColumnsKey[key] = randomCode;
    }
    for (let i = 0; i < jsonData.length; i++) {
      let item = jsonData[i];
      let itemRow = {
        id: generateRandomCode(),
      };
      for (let key in item) {
        itemRow[tableColumnsKey[key]] = item[key];
      }
      tableRows.push(itemRow);
    }
    state.tableColumns = tableColumns;
    dataSource.value = tableRows;
  }
}

function readerField(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const data = new Uint8Array(e.target.result);
      const workbook = XLSX.read(data, { type: 'array' });
      const firstSheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet);
      resolve(jsonData);
    };
    reader.readAsArrayBuffer(file);
  });
}

function handlePaste(event) {
  if (props.modalType !== 'edit') return;
  // 获取剪切板中的数据
  const clipboardData = event.clipboardData || window.clipboardData;
  let pastedData = clipboardData.getData('text');
  if (!pastedData.includes('\r\n') || !pastedData.includes('\t')) {
    message.warning('请复制Excel表格内容,至少一行两列');
    return;
  }
  let dataKey = [];
  let tableColumns = [];
  let tableRows = [];
  // 拆成多行
  let rowStrArray = pastedData.split('\r\n');
  // 拆成多列
  for (let i = 0; i < rowStrArray.length; i++) {
    let cellData = rowStrArray[i].split('\t');
    if (i === rowStrArray.length - 1) {
      if (cellData.join('').length === 0) {
        return;
      }
    }
    if (i === 0) {
      cellData.forEach((item1) => {
        let randomCode = generateRandomCode();
        tableColumns.push({
          title: item1,
          dataIndex: randomCode,
          width: 200,
        });
        dataKey.push(randomCode);
      });
    } else {
      let itemRow = {
        id: generateRandomCode(),
      };
      cellData.forEach((item1, index1) => {
        itemRow[dataKey[index1]] = item1;
      });
      tableRows.push(itemRow);
    }
    state.tableColumns = tableColumns;
    dataSource.value = tableRows;
  }
  state.tableColumns = tableColumns;
  dataSource.value = tableRows;
}

function canChangeType() {
  state.canChange = false;
}

defineExpose({
  initData,
  getData,
  changeType,
  canChangeType,
});

</script>
<style lang="less" scoped>
.template-top {
  display: flex;
  margin-bottom: 15px;

  .top-item {
    width: 60px;
    height: 60px;
    text-align: center;
    line-height: 60px;
    cursor: pointer;
    margin-right: 10px;

    &:hover {
      background-color: rgba(88, 171, 248, 0.298);
      color: var(--ant-primary-color);
    }
  }

  .top-item-active {
    background-color: rgba(88, 171, 248, 0.298);
    color: var(--ant-primary-color);
  }
}

.content-item {
  .header-cell {
    display: flex;
    justify-content: space-between;

    .header-cell-icon {
      .icon-main-wrap + .icon-main-wrap {
        margin-left: 10px;
      }
    }
  }

  .content-item-label {
    margin-bottom: 10px;
  }

  .content-item-label1 {
    display: flex;
    justify-content: space-between;
  }

  .content-item-label-img {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
  }

  .content-item-img {
    display: flex;
    margin-bottom: 10px;

    .item-img {
      width: 200px;
      height: 200px;
      position: relative;
      margin-right: 10px;

      &:last-child {
        margin-right: 0px;
      }

      .icon-main-wrap {
        position: absolute;
        top: 0px;
        right: 0px;
      }
    }
  }

  .content-item-value {
    height: 170px;

    .ant-input {
      height: 100%;
    }
  }

  .latex-top {

  }

  .latex-content {
    display: flex;

    .latex-edit, .latex-details {
      flex: 1;
    }

    .latex-edit {
      .ant-input {
        height: 100%;
      }
    }
  }

  .content-item-number {
    display: flex;

    .input-val {
      width: 300px !important;
      margin-right: 10px;

      &:last-child {
        margin-right: 0px;
      }

    }
  }

  .content-item-upload {
    height: 300px;
    overflow: hidden;
  }

  .content-item-table {
    display: flex;
    width: 100%;

    .content-item-left {
      margin-right: 20px;
      width: calc(~ '100% - 60px');

      .add-row {
        display: block;
        text-align: center;
        border: 1px dashed #cccccc;
        cursor: pointer;
        margin-top: 10px;
        padding: 6px 0;
        border-radius: 4px;
      }
    }

    .content-item-check {
      width: 100%;
      margin-right: 0;
    }

    .content-item-right {
      width: 40px;
      position: relative;
      cursor: pointer;
      border: 1px dashed #cccccc;
      text-align: center;
      border-radius: 4px;

      span {
        left: 10px;
        writing-mode: vertical-rl;
        text-orientation: upright;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }
}

.template-remark {
  margin-top: 20px;

  .template-remark-label {
    margin-bottom: 10px;

    .ant-input {
      height: 200px;
    }
  }
}
</style>
