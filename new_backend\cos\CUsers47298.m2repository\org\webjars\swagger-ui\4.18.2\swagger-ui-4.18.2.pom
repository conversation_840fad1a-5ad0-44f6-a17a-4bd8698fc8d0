<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.sonatype.oss</groupId>
        <artifactId>oss-parent</artifactId>
        <version>9</version>
    </parent>
    
    <packaging>jar</packaging>
    <groupId>org.webjars</groupId>
    <artifactId>swagger-ui</artifactId>
    <version>4.18.2</version>
    <name>Swagger UI</name>
    <description>WebJar for Swagger UI</description>
    <url>http://webjars.org</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.build.outputTimestamp>1680273830</project.build.outputTimestamp>
        <upstream.url>https://github.com/swagger-api/swagger-ui/archive/v${version.unsnapshot}.zip</upstream.url>
        <destDir>${project.build.outputDirectory}/META-INF/resources/webjars/${project.artifactId}/${project.version}</destDir>
        <requirejs>
            {
                "paths": { "swagger-ui": "swagger-ui" }
            }
        </requirejs>
    </properties>

    <developers>
        <developer>
            <id>jamesward</id>
            <name>James Ward</name>
            <email><EMAIL></email>
        </developer>
    </developers>

    <licenses>
        <license>
            <name>Apache 2.0</name>
            <url>https://github.com/swagger-api/swagger-ui</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <scm>
        <url>http://github.com/webjars/swagger-ui</url>
        <connection>scm:git:https://github.com/webjars/swagger-ui.git</connection>
        <developerConnection>scm:git:https://github.com/webjars/swagger-ui.git</developerConnection>
        <tag>swagger-ui-4.18.2</tag>
    </scm>
    
    <build>
        <plugins>
            <plugin>
                <groupId>com.jamesward</groupId>
                <artifactId>unsnapshot-maven-plugin</artifactId>
                <version>0.2</version>
                <executions>
                    <execution>
                        <phase>initialize</phase>
                        <goals>
                            <goal>unsnapshot</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.2.2</version>
                <configuration>
                    <archive>  
                        <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                    </archive> 
                </configuration>
            </plugin>  

            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>5.1.4</version>
                <extensions>true</extensions>
                <executions>
                    <execution>
                        <id>bundle-manifest</id>
                        <phase>process-classes</phase>
                        <goals>    
                            <goal>manifest</goal>
                        </goals>   
                    </execution>
                </executions>                
            </plugin>

            <plugin>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>1.8</version>
                <executions>
                    <execution>
                        <phase>process-resources</phase>
                        <goals><goal>run</goal></goals>
                        <configuration>
                            <target>
                                <echo message="download archive" />
                                <get src="${upstream.url}" dest="${project.build.directory}/${project.artifactId}.zip" />
                                <echo message="unzip archive" />
                                <unzip src="${project.build.directory}/${project.artifactId}.zip" dest="${project.build.directory}" />
                                <echo message="moving resources" />
                                <move todir="${destDir}">
                                    <fileset dir="${project.build.directory}/swagger-ui-${version.unsnapshot}/dist" />
                                </move>
                            </target>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>3.0.0-M7</version>
            </plugin>
            <plugin>
                <groupId>com.googlecode.todomap</groupId>
                <artifactId>maven-jettygzip-plugin</artifactId>
                <version>0.0.4</version>
                <configuration>
                    <webappDirectory>target/classes</webappDirectory>
                    <outputDirectory>target/classes</outputDirectory>
                </configuration>
                <executions>
                    <execution>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>process</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.sonatype.plugins</groupId>
                <artifactId>nexus-staging-maven-plugin</artifactId>
                <version>1.6.13</version>
                <extensions>true</extensions>
                <configuration>
                    <serverId>sonatype-nexus-staging</serverId>
                    <nexusUrl>https://oss.sonatype.org/</nexusUrl>
                    <autoReleaseAfterClose>true</autoReleaseAfterClose>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
