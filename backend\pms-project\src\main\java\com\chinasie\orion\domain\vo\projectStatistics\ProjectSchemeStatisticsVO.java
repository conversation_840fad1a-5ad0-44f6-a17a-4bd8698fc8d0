package com.chinasie.orion.domain.vo.projectStatistics;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "ProjectSchemeStatisticsVO对象", description = "计划统计表")
public class ProjectSchemeStatisticsVO {
    @ApiModelProperty(value = "项目id")
    private String id;
    @ApiModelProperty(value = "负责人名称")
    private String rspuserName;
    @ApiModelProperty(value = "负责人")
    private String rspuser;
    @ApiModelProperty(value = "展示时间")
    private String showTime;
    @ApiModelProperty(value = "时间")
    private Date timeValue;
    @ApiModelProperty(value = "待发布数量")
    private Integer waitReleaseCount=0;
    @ApiModelProperty(value = "已发布数量")
    private Integer releaseCount=0;
    @ApiModelProperty(value = "已完成数量")
    private Integer completeCount=0;
}
