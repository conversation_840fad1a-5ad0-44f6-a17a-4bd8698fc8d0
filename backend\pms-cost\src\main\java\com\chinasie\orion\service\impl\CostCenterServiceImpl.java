package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.bo.CodeBo;
import com.chinasie.orion.constant.ClassNameConstant;
import com.chinasie.orion.domain.dto.CostCenterDTO;
import com.chinasie.orion.domain.entity.CostCenter;
import com.chinasie.orion.domain.entity.ExponseDetail;
import com.chinasie.orion.domain.entity.ProjectBudget;
import com.chinasie.orion.domain.vo.CostCenterVO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.CostCenterMapper;
import com.chinasie.orion.repository.ExponseDetailMapper;
import com.chinasie.orion.repository.ProjectBudgetMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.CostCenterService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.mzt.logapi.context.LogRecordContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * CostCenter 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16 13:29:31
 */
@Service
public class CostCenterServiceImpl extends OrionBaseServiceImpl<CostCenterMapper, CostCenter> implements CostCenterService {

    @Autowired
    private CostCenterMapper costCenterMapper;
    @Autowired
    private CodeBo codeBo;
    @Autowired
    private ProjectBudgetMapper projectBudgetMapper;
    @Autowired
    private ExponseDetailMapper exponseDetailMapper;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public CostCenterVO detail(String id) throws Exception {
        CostCenter costCenter = this.getById(id);
        CostCenterVO result = BeanCopyUtils.convertTo(costCenter, CostCenterVO::new);
        return result;
    }

    /**
     * 新增
     * <p>
     * * @param costCenterDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CostCenterVO create(CostCenterDTO costCenterDTO) throws Exception {
        List<CostCenter> costCenters = this.list(new LambdaQueryWrapperX<CostCenter>().eq(CostCenter::getName, costCenterDTO.getName()));
        if (!CollectionUtils.isEmpty(costCenters)) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "成本中心名称不能重复");
        }
        // CostCenter  costCenter= BeanCopyUtils.convertTo(costCenterDTO, CostCenter::new);
//        //生成编码
//        List<SysCodeSegmentVO> codeRuleList = codeBo.getCodeRuleList(ClassNameConstant.COST_CENTER,ClassNameConstant.NUMBER);
//        if (!CollectionUtils.isEmpty(codeRuleList)) {
//            String code = codeBo.getCode(codeRuleList);
//            costCenterDTO.setNumber(code);
//        }
        String code = "";
        if (StrUtil.isNotBlank(costCenterDTO.getNumber())) {
            code = codeBo.createCode(ClassNameConstant.COST_CENTER, ClassNameConstant.NUMBER, true, costCenterDTO.getNumber());
        } else {
            code = codeBo.createCode(ClassNameConstant.COST_CENTER, ClassNameConstant.NUMBER, false, null);
        }
        costCenterDTO.setNumber(code);
        CostCenter costCenter = BeanCopyUtils.convertTo(costCenterDTO, CostCenter::new);
        this.save(costCenter);
        CostCenterVO rsp = BeanCopyUtils.convertTo(costCenter, CostCenterVO::new);
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param costCenterDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean edit(CostCenterDTO costCenterDTO) throws Exception {

        List<CostCenter> costCenters = this.list(new LambdaQueryWrapperX<CostCenter>().eq(CostCenter::getName, costCenterDTO.getName())
                .ne(CostCenter::getId, costCenterDTO.getId()));
        if (!CollectionUtils.isEmpty(costCenters)) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "成本中心名称不能重复");
        }
        CostCenter costCenter = this.getById(costCenterDTO.getId());
        if (costCenter.getStatus() != costCenterDTO.getStatus() && costCenterDTO.getStatus() == 0) {
            List<ProjectBudget> projectBudgetList = projectBudgetMapper.selectList(new LambdaQueryWrapperX<ProjectBudget>()
                    .eq(ProjectBudget::getCostCenterId, costCenterDTO.getId()));
            if (!CollectionUtils.isEmpty(projectBudgetList)) {
                throw new BaseException(PMSErrorCode.PMS_DATA_QUOTE_STATUS);
            }
            List<ExponseDetail> exponseDetails = exponseDetailMapper.selectList(new LambdaQueryWrapperX<ExponseDetail>()
                    .eq(ExponseDetail::getCostCenterId, costCenterDTO.getId()));
            if (!CollectionUtils.isEmpty(exponseDetails)) {
                throw new BaseException(PMSErrorCode.PMS_DATA_QUOTE_STATUS);
            }
        }
        CostCenter cost = BeanCopyUtils.convertTo(costCenterDTO, CostCenter::new);
        int update = costCenterMapper.updateById(cost);
        return SqlHelper.retBool(update);
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    @Transactional
    public Boolean removeById(String id) throws Exception {
        //TODO 删除判断待处理
        List<ProjectBudget> projectBudgetList = projectBudgetMapper.selectList(new LambdaQueryWrapperX<ProjectBudget>()
                .eq(ProjectBudget::getCostCenterId, id));
        if (!CollectionUtils.isEmpty(projectBudgetList)) {
            throw new BaseException(PMSErrorCode.PMS_DATA_QUOTE_STATUS);
        }
        List<ExponseDetail> exponseDetails = exponseDetailMapper.selectList(new LambdaQueryWrapperX<ExponseDetail>()
                .eq(ExponseDetail::getCostCenterId, id));
        if (!CollectionUtils.isEmpty(exponseDetails)) {
            throw new BaseException(PMSErrorCode.PMS_DATA_QUOTE_STATUS);
        }
        CostCenter costCenter = this.getById(id);
        if (null == costCenter){
            return true;
        }
        LogRecordContext.putVariable("number", costCenter.getNumber());
        LogRecordContext.putVariable("name", costCenter.getName());

        int delete = costCenterMapper.deleteById(id);
        return SqlHelper.retBool(delete);
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public Page<CostCenterVO> pages(Page<CostCenterDTO> pageRequest) throws Exception {
        Page<CostCenter> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), CostCenter::new));
        CostCenterDTO query = pageRequest.getQuery();
        LambdaQueryWrapperX<CostCenter> lambdaQueryWrapperX = new LambdaQueryWrapperX();
        if (!ObjectUtils.isEmpty(query)&&StrUtil.isNotBlank(query.getName())) {
            lambdaQueryWrapperX.and(m -> m.like(CostCenter::getName, query.getName()).or().like(CostCenter::getNumber, query.getName()));
        }
        List<List<SearchCondition>> searchConditions = pageRequest.getSearchConditions();
        if (CollectionUtil.isNotEmpty(searchConditions)){
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions,lambdaQueryWrapperX);
        }
        PageResult<CostCenter> page = costCenterMapper.selectPage(realPageRequest, lambdaQueryWrapperX);
        Page<CostCenterVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<CostCenterVO> vos = BeanCopyUtils.convertListTo(page.getContent(), CostCenterVO::new);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public Boolean ban(String id) throws Exception {
        //TODO 禁用判断待处理
//        List<ProjectBudget> projectBudgetList = projectBudgetMapper.selectList(new LambdaQueryWrapperX<ProjectBudget>()
//                .eq(ProjectBudget::getCostCenterId, id));
//        if (!CollectionUtils.isEmpty(projectBudgetList)) {
//            throw new BaseException(PMSErrorCode.PMS_DATA_QUOTE_STATUS);
//        }
//        List<ExponseDetail> exponseDetails = exponseDetailMapper.selectList(new LambdaQueryWrapperX<ExponseDetail>()
//                .eq(ExponseDetail::getCostCenterId, id));
//        if (!CollectionUtils.isEmpty(exponseDetails)) {
//            throw new BaseException(PMSErrorCode.PMS_DATA_QUOTE_STATUS);
//        }
        CostCenter costCenter = this.getById(id);
        if (null == costCenter){
            return true;
        }
        LogRecordContext.putVariable("number", costCenter.getNumber());
        LogRecordContext.putVariable("name", costCenter.getName());
        costCenter.setStatus(0);
        this.updateById(costCenter);
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public Boolean use(String id) throws Exception {
        CostCenter costCenter = this.getById(id);
        costCenter.setStatus(1);
        this.updateById(costCenter);
        return Boolean.TRUE;
    }

    @Override
    public List<CostCenterVO> getCostCenterList(CostCenterDTO costCenterDTO) throws Exception {
        LambdaQueryWrapperX<CostCenter> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(CostCenter::getStatus, 1);
        lambdaQueryWrapperX.likeIfPresent(CostCenter::getName, costCenterDTO.getName());
        List<CostCenter> costCenters = this.list(lambdaQueryWrapperX);
        List<CostCenterVO> list = BeanCopyUtils.convertListTo(costCenters, CostCenterVO::new);
        return list;
    }

    @Override
    public Map<String, CostCenter> getCosCenterMap(List<String> costCenterIds) throws Exception {
        if (!CollectionUtils.isEmpty(costCenterIds)) {
            List<CostCenter> costCenters = list(new LambdaQueryWrapperX<CostCenter>().in(CostCenter::getId, costCenterIds));
            if (CollectionUtils.isEmpty(costCenters)) {
                return Collections.EMPTY_MAP;
            }
            return costCenters.stream().collect(Collectors.toMap(CostCenter::getId, e -> e));
        }
        return Collections.EMPTY_MAP;
    }
}

