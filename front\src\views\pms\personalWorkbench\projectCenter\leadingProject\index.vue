<template>
  <Layout
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      :isTable="isTable"
      class="card-list-table"
    >
      <!--      <template #toolbarLeft>-->
      <!--        <BasicButton-->
      <!--          v-if=" isPower('XMX_list_button_01', powerData) "-->
      <!--          type="primary"-->
      <!--          icon="add"-->
      <!--          @click="addNode"-->
      <!--        >-->
      <!--          创建项目-->
      <!--        </BasicButton>-->
      <!--        <BasicButtonGroup>-->
      <!--          <BasicButton-->
      <!--            :type="isTable?'':'primary'"-->
      <!--            :ghost="!isTable"-->
      <!--            @click="isTable=false"-->
      <!--          >-->
      <!--            卡片视图-->
      <!--          </BasicButton>-->
      <!--          <BasicButton-->
      <!--            :type="isTable?'primary':''"-->
      <!--            :ghost="isTable"-->
      <!--            @click="isTable=true"-->
      <!--          >-->
      <!--            列表视图-->
      <!--          </BasicButton>-->
      <!--        </BasicButtonGroup>-->
      <!--      </template>-->
      <template #projectApproveTime="{ text }">
        {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
      </template>
      <template #projectStartTime="{ text }">
        {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
      </template>
      <template #projectEndTime="{ text }">
        {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
      </template>

      <template #schedule="{ text }">
        <Progress
          v-if="text < 100"
          :percent="text"
          size="small"
          style="padding-right: 40px"
        />
        <Progress
          v-else-if="(text = 100)"
          size="small"
          style="padding-right: 40px"
          :stroke-color="{
            from: '#67af64',
            to: '#63c3c2'
          }"
          :percent="text"
          status="active"
        />
      </template>

      <template #statusIdName="{ record }">
        <DataStatusTag :status-data="record?.dataStatus" />
      </template>

      <template #action="{record}">
        <BasicTableAction
          :actions="actionsBtn"
          :record="record"
        />
      </template>

      <template
        v-if="!isTable"
        #otherContent="{dataSource}"
      >
        <div
          v-if="dataSource.length"
          ref="cardGrid"
          class="card-grid"
          :style="{'grid-template-columns': `repeat(${gridNum}, minmax(340px,1fr))`}"
        >
          <CardItem
            v-for="item in dataSource"
            :key="item.id"
            :record="item"
            :onActionClick="actionClick"
          />
        </div>
        <Empty
          v-else
          class="w-full h-full flex flex-ver flex-ac flex-pc"
          :image="Empty.PRESENTED_IMAGE_SIMPLE"
        />
      </template>
    </OrionTable>
    <!--    <checkDetails :data="nodeData" />-->
    <!--    <searchModal-->
    <!--      v-model:visible="searchModalVisible"-->
    <!--      :data="searchData"-->
    <!--      @search="searchTable"-->
    <!--    />-->
    <!--    <PushModel />-->
    <!--    <PojectLabModal-->
    <!--      @register="registerModal"-->
    <!--    />-->
    <AddTableNode
      @update="successSave"
      @register="registerAdd"
    />
    <!--发起申报-->
    <CreateAndEditDrawer
      :onConfirmCallback="reloadTable"
      @register="registerCreateAndEdit"
    />
    <!--发起立项-->
    <InitiationEditDrawer
      :onConfirmCallback="reloadTable"
      @register="registerCreateInitiationEdit"
    />
    <!--发起评价-->
    <EvaluateDrawer
      @upTableDate="reloadTable"
      @register="modalEvaluateRegister"
    />
    <!--    合同签订-->
    <AddContractDrawer @register="addContractRegister" />
  </Layout>
</template>
<script lang="ts">
import {
  computed,
  defineComponent,
  getCurrentInstance,
  h,
  onMounted,
  onUnmounted,
  reactive,
  ref,
  Ref,
  toRefs,
  unref, watch,
} from 'vue';
import {
  BasicButton,
  BasicButtonGroup,
  BasicTableAction, BasicTitle1,
  DataStatusTag,
  isPower,
  ITableActionItem,
  Layout,
  OrionTable,
  useDrawer,
  useProjectPower,
} from 'lyra-component-vue3';
import {
  Empty, message, Progress, Modal,
} from 'ant-design-vue';
import AddTableNode from './components/AddTableNode.vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import Api from '/@/api';
import { CreateAndEditDrawer } from '/@/views/pms/projectApplication/components';// 申报抽屉
import InitiationEditDrawer from '/@/views/pms/projectInitiation/components/CreateAndEditDrawer/Index.vue';
import { postAcceptance } from '/@/views/pms/api';
import EvaluateDrawer from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/projectEvaluate/components/evaluateDrawer.vue';
import { AddContractDrawer } from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/contractManage/components'; // 立项抽屉
const [registerCreateAndEdit, { openDrawer: openCreateAndEdit }] = useDrawer(); // 申报抽屉
const [registerCreateInitiationEdit, { openDrawer: openCreateInitiationEdit }] = useDrawer(); // 立项抽屉
const [modalEvaluateRegister, { openDrawer: openEvaluateDrawer }] = useDrawer();// 项目评价
const [addContractRegister, { openDrawer: openContractDrawer }] = useDrawer();// 合同签订

const tableRef = ref(null);
export default defineComponent({
  name: 'ProductLibraryIndex',
  components: {
    AddContractDrawer,
    EvaluateDrawer,
    CreateAndEditDrawer,
    InitiationEditDrawer,
    // BasicTitle1,
    Layout,
    AddTableNode,
    Progress,
    DataStatusTag,
    OrionTable,
    // BasicButtonGroup,
    // BasicButton,
    BasicTableAction,
    Empty,
  },
  setup() {
    let ids: string[] = [];
    try {
      ids = JSON.parse(sessionStorage.getItem('ids'));
    } finally {
      sessionStorage.removeItem('ids');
    }

    const [registerAdd, { openDrawer: openDrawerAdd }] = useDrawer();
    const [registerModal, { openDrawer: openDrawerModal }] = useDrawer();
    const isTable: Ref<boolean> = ref(true);
    const state = reactive({
      // searchvlaue: '',
      showVisible: false,
      message: '',
      powerData: [],
      nodeData: [],
      // 搜索弹窗
      searchModalVisible: false,
      searchData: {},
      params: {},
      queryCondition: [],
      searchStatus: '',
    });

    function getTableAction() {
      const tableAction = unref(tableRef);
      if (!tableAction) {
        throw new Error('内部错误');
      }
      return tableAction;
    }

    const internalInstance = getCurrentInstance();

    // 获取权限
    async function getProjectPower() {
      return new Promise((resolve, reject) => {
        useProjectPower(
          { pageCode: 'PMS0003' },
          (powerList) => {
            resolve(powerList || []);
          },
          internalInstance,
        );
      });
    }

    onMounted(async () => {
      state.powerData = await getProjectPower();
      onResize();
      window.addEventListener('resize', onResize);
    });
    const router = useRouter();
    const zkKeys = () => getTableAction().getSelectRowKeys();
    const searchTable = (params) => {
      state.searchStatus = 'eveSearch';
      state.queryCondition = params.queryCondition;
      state.params = params.params;
      successSave();
    };
    // 跳转详情
    const toDetails = (data) => {
      router.push({
        name: 'MenuComponents',
        query: {
          id: data.id,
        },
      });
    };
    const addNode = () => {
      openDrawerAdd(true, { type: 'add' });
    };
    const onSearch = () => {
      state.searchStatus = 'oddSearch';
      successSave();
    };
    const successSave = () => {
      getTableAction().reload({
        page: 1,
      });
      getTableAction().clearSelectedRowKeys();
      state.searchStatus = '';
    };

    function getListParams(params) {
      if (ids?.length) {
        params.query = {
          ids,
          jumpFlag: true,
        };
      }
      if (params.searchConditions) {
        return {
          ...params,
          queryCondition: params.searchConditions.map((item) => ({
            column: item?.[0]?.field,
            type: 'like',
            link: 'or',
            value: item?.[0]?.values?.[0],
          })),
        };
      }
      return params;
    }

    function reloadTable() {
      tableRef.value?.reload();
    }

    function deleteRecords(ids: string[]) {
      return new Api('/pms')
        .fetch(ids, 'project/removeBatch/', 'DELETE')
        .then(() => {
          message.success('删除成功');
        });
    }

    const tableOptions = {
      rowSelection: {},
      // smallSearchField: ['name', 'number'],
      deleteToolButton: 'add|delete|enable|disable',
      isFilter2: true,
      filterConfigName: 'PMS_PERSONALWORKBENCH_PROJECTCENTER_LEADINGPROJECT_INDEX',
      api: (params) => new Api('/pms/project/workBenchPage/1').fetch(getListParams(params), '', 'POST'),
      batchDeleteApi({ ids }) {
        return deleteRecords(ids);
      },
      columns: [
        // {
        //   title: '项目编号',
        //   dataIndex: 'number',
        //   width: '100px',
        //   slots: { customRender: 'number' },
        // },
        {
          title: '项目名称',
          dataIndex: 'name',
          minWidth: 220,
          customRender({ record, text }) {
            return h(
              'span',
              {
                class: computed(() => (isPower('XMX_list_button_03', state.powerData) ? 'action-btn' : '')).value,
                title: text,
                onClick(e) {
                  if (isPower('XMX_list_button_03', state.powerData)) {
                    toDetails(record);
                  }
                  e.stopPropagation();
                },
              },
              text,
            );
          },
        },
        {
          title: '项目类型',
          dataIndex: 'projectTypeName',
          width: '120px',
        },
        {
          title: '是否需要申报',
          dataIndex: 'isDeclare',
          width: '100px',
          customRender({ text }) {
            return text ? '是' : text === false ? '否' : '--';
          },
        },
        {
          title: '项目进度',
          dataIndex: 'schedule',
          width: '170px',
          slots: { customRender: 'schedule' },
        },
        {
          title: '状态',
          dataIndex: 'statusIdName',
          width: '100px',
          slots: { customRender: 'statusIdName' },
        },
        {
          title: '项目经理',
          dataIndex: 'pm',
          width: '120px',
          slots: { customRender: 'pm' },
        },
        {
          title: '我的项目角色',
          dataIndex: 'roleName',
          width: '170px',
          slots: { customRender: 'roleName' },
        },
        {
          title: '立项日期',
          dataIndex: 'projectApproveTime',
          width: '120px',
          slots: { customRender: 'projectApproveTime' },
        },
        {
          title: '开始日期',
          dataIndex: 'projectStartTime',
          width: '120px',
          slots: { customRender: 'projectStartTime' },
        },
        {
          title: '结束日期',
          dataIndex: 'projectEndTime',
          width: '120px',
          slots: { customRender: 'projectEndTime' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 200,
          align: 'left',
          fixed: 'right',
          slots: { customRender: 'action' },
        },
      ],
    };

    const actionsBtn: ITableActionItem[] = [
      {
        text: '发起申报',
        isShow: (record:any) => record.dataStatus.statusValue === 101 && record.isDeclare,
        onClick(record: any) {
          openCreateAndEdit(true, {});
        },
      },
      {
        text: '发起立项',
        isShow: (record:any) => record.dataStatus.statusValue === 110,
        onClick(record: any) {
          openCreateInitiationEdit(true, {});
        },
      },
      {
        text: '项目验收',
        isShow: (record:any) => record.dataStatus.statusValue === 120,
        onClick(record:any) {
          handleProjectAcceptance(record);
        },
      },
      {
        text: '项目评价',
        isShow: (record:any) => record.dataStatus.statusValue === 130,
        onClick(record: any) {
          openEvaluateDrawer(true, { projectId: record.id });
        },
      },
      {
        text: '合同签订',
        isShow: (record:any) => record.dataStatus.statusValue === 101 && !record.isDeclare,
        onClick(record: any) {
          openContractDrawer(true, {
            type: 'add',
            projectId: record.id,
            projectName: record.name,
          });
        },
      },
      {
        text: '编辑',
        isShow: (record:any) => record.dataStatus.statusValue === 101,
        onClick(record: any) {
          openDrawerAdd(true, {
            type: 'edit',
            id: record.id,
            projectType: record.projectType || '',
          });
        },
      },
      {
        text: '删除',
        isShow: (record:any) => record.dataStatus.statusValue === 101,
        modal(record: any) {
          return deleteRecords([record.id]).then(() => {
            reloadTable();
          });
        },
      },
    ];

    onUnmounted(() => {
      window.removeEventListener('resize', onResize);
    });

    const gridNum: Ref<number> = ref();

    watch(() => tableRef.value, () => {
      onResize();
    });

    function onResize() {
      if (!tableRef.value) {
        return;
      }
      const tableWidth = tableRef.value.$el.clientWidth - 60;
      let num = parseInt(tableWidth / 340);
      gridNum.value = parseInt(tableWidth / (340 + Math.ceil((num - 1 < 0 ? 0 : num - 1) * 20) / num));
    }

    /**
     * 用户收藏项目
     * @param params
     */
    async function postUserLike(params: { projectId: string }) {
      return new Api('/pms/user-like-project').fetch(params, '', 'post');
    }

    /**
     * 用户取消收藏项目
     * @param params
     */
    async function deleteUserLike(params: Array<string>) {
      return new Api('/pms/user-like-project').fetch(params, '', 'delete');
    }

    // 操作区点击事件
    const actionClick = async (key, record) => {
      switch (key) {
        case 'edit':
          openDrawerAdd(true, {
            type: 'edit',
            id: record.id,
            projectType: record.projectType || '',
          });
          break;
        case 'del':
          Modal.confirm({
            title: '删除确认提示？',
            content: '请确认是否删除该项目，删除后不可恢复？',
            onOk() {
              return deleteRecords([record.id]).then(() => {
                reloadTable();
              });
            },
          });
          break;
        case 'look':
          return toDetails(record);
          // 收藏
        case 'collect':
          if (record.like) {
            Modal.confirm({
              title: '温馨提示',
              content: '请确认是否取消关注该项目？',
              onOk() {
                return new Promise((resolve) => {
                  deleteUserLike([record.like.id])
                    .then(() => {
                      getTableAction().reload({
                        page: 1,
                      });
                      message.success('已取消关注');
                      resolve(true);
                    })
                    .catch(() => {
                      resolve('');
                    });
                });
              },
              onCancel() {
                Modal.destroyAll();
              },
            });
          } else {
            await postUserLike({
              projectId: record.id,
            });
            getTableAction().reload({
              page: 1,
            });
            message.success(`${record.name}关注成功！`);
          }
          break;
      }
    };
    // 操作区点击项目验收
    async function handleProjectAcceptance(record) {
      Modal.confirm({
        title: '项目验收',
        content: '确认是否要发起项目验收？',
        async onOk() {
          const result = await postAcceptance({
            projectId: record.id,
            type: 'PROJECT',
          });
          if (result) {
            router.push({
              name: 'ProjectAcceptanceDetail',
              query: {
                projectId: result.projectId,
                id: result.id,

              },
            });
          }
        },
      });
    }

    return {
      ...toRefs(state),
      isTable,
      addNode,
      dayjs,
      onSearch,
      successSave,
      searchTable,
      tableRef,
      isPower,
      registerModal,
      registerAdd,
      tableOptions,
      actionsBtn,
      Empty,
      onResize,
      gridNum,
      actionClick,
      reloadTable,
      registerCreateAndEdit,
      registerCreateInitiationEdit,
      modalEvaluateRegister,
      addContractRegister,
    };
  },
});
</script>
<style lang="less" scoped>
.title{
  margin-left: 20px;
}
.card-grid {
  display: grid;
  gap: 16px 20px;
}

:deep(.card-list-table) {
  .ant-btn-group {
    margin-left: auto;

    .ant-btn + .ant-btn {
      margin-left: 0;
    }

    & + .card-list-table {
      width: auto;
      flex: 0;

      .ant-input-search {
        width: 220px;
      }
    }
  }
}
</style>
