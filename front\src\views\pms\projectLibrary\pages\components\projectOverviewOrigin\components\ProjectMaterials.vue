<script setup lang="ts">
import {
  inject,
  onMounted, provide, readonly, ref, Ref,
} from 'vue';
import MaterialType from '../charts/MaterialType.vue';
import MaterialDemand from '../charts/MaterialDemand.vue';
import MaterialReceived from '../charts/MaterialReceived.vue';
import Api from '/@/api';

const projectId:string = inject('projectId');
const loading:Ref<boolean> = ref(false);
provide('loading', readonly(loading));
const materialInfo:Ref<Record<string, any>> = ref({});
provide('materialInfo', materialInfo);

onMounted(() => {
  getMaterialInfo();
});

// 获取物资信息
async function getMaterialInfo() {
  loading.value = true;
  try {
    const result = await new Api('/pms/projectOverviewNew/getGoodsServiceCount').fetch({ projectId }, '', 'GET');
    materialInfo.value = result || {};
  } finally {
    loading.value = false;
  }
}
</script>

<template>
  <div class="grid">
    <MaterialType />
    <MaterialDemand />
    <MaterialReceived />
  </div>
</template>

<style scoped lang="less">
.grid {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  gap: 0 15px;
}
</style>
