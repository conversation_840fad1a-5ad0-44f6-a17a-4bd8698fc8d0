<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.springframework.cloud</groupId>
    <artifactId>spring-cloud-dependencies-parent</artifactId>
    <version>3.1.9</version>
    <relativePath></relativePath>
  </parent>
  <groupId>org.springframework.cloud</groupId>
  <artifactId>spring-cloud-dependencies</artifactId>
  <version>2021.0.9</version>
  <packaging>pom</packaging>
  <name>spring-cloud-dependencies</name>
  <description>Spring Cloud Dependencies</description>
  <url>https://projects.spring.io/spring-cloud/spring-cloud-dependencies/</url>
  <organization>
    <name>Pivotal Software, Inc.</name>
    <url>https://www.spring.io</url>
  </organization>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
      <comments>Copyright 2014-2021 the original author or authors.

				Licensed under the Apache License, Version 2.0 (the "License");
				you may not use this file except in compliance with the License.
				You may obtain a copy of the License at

				https://www.apache.org/licenses/LICENSE-2.0

				Unless required by applicable law or agreed to in writing, software
				distributed under the License is distributed on an "AS IS" BASIS,
				WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
				implied.

				See the License for the specific language governing permissions and
				limitations under the License.</comments>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>dsyer</id>
      <name>Dave Syer</name>
      <email>dsyer at pivotal.io</email>
      <organization>Pivotal Software, Inc.</organization>
      <organizationUrl>https://www.spring.io</organizationUrl>
      <roles>
        <role>Project lead</role>
      </roles>
    </developer>
    <developer>
      <id>sgibb</id>
      <name>Spencer Gibb</name>
      <email>sgibb at pivotal.io</email>
      <organization>Pivotal Software, Inc.</organization>
      <organizationUrl>https://www.spring.io</organizationUrl>
      <roles>
        <role>Project lead</role>
      </roles>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:git://github.com/spring-cloud/spring-cloud-build.git/spring-cloud-dependencies</connection>
    <developerConnection>scm:git:ssh://**************/spring-cloud/spring-cloud-build.git/spring-cloud-dependencies</developerConnection>
    <url>https://github.com/spring-cloud/spring-cloud-build/spring-cloud-dependencies</url>
  </scm>
  <distributionManagement>
    <repository>
      <id>repo.spring.io</id>
      <name>Spring Release Repository</name>
      <url>https://repo.spring.io/libs-release-local</url>
    </repository>
    <snapshotRepository>
      <id>repo.spring.io</id>
      <name>Spring Snapshot Repository</name>
      <url>https://repo.spring.io/libs-snapshot-local</url>
    </snapshotRepository>
    <site>
      <id>spring-docs</id>
      <url>https:/docs.spring.io/spring-cloud-dependencies-parent/docs/3.1.9/reference/html/spring-cloud-dependencies/</url>
    </site>
    <downloadUrl>https://github.com/spring-cloud</downloadUrl>
  </distributionManagement>
  <properties>
    <spring-cloud-circuitbreaker.version>2.1.8</spring-cloud-circuitbreaker.version>
    <spring-cloud-bus.version>3.1.3</spring-cloud-bus.version>
    <spring-cloud-consul.version>3.1.5</spring-cloud-consul.version>
    <spring-cloud-stream.version>3.2.10</spring-cloud-stream.version>
    <spring-cloud-contract.version>3.1.9</spring-cloud-contract.version>
    <spring-cloud-openfeign.version>3.1.9</spring-cloud-openfeign.version>
    <spring-cloud-cli.version>3.1.1</spring-cloud-cli.version>
    <spring-cloud-config.version>3.1.9</spring-cloud-config.version>
    <spring-cloud-gateway.version>3.1.9</spring-cloud-gateway.version>
    <spring-cloud-task.version>2.4.6</spring-cloud-task.version>
    <spring-cloud-sleuth.version>3.1.10</spring-cloud-sleuth.version>
    <spring-cloud-vault.version>3.1.4</spring-cloud-vault.version>
    <main.basedir>${basedir}/../..</main.basedir>
    <spring-cloud-netflix.version>3.1.8</spring-cloud-netflix.version>
    <spring-cloud-cloudfoundry.version>3.1.4</spring-cloud-cloudfoundry.version>
    <spring-cloud-function.version>3.2.12</spring-cloud-function.version>
    <spring-cloud-build.version>3.1.9</spring-cloud-build.version>
    <spring-cloud-commons.version>3.1.8</spring-cloud-commons.version>
    <spring-cloud-kubernetes.version>2.1.9</spring-cloud-kubernetes.version>
    <spring-cloud-zookeeper.version>3.1.5</spring-cloud-zookeeper.version>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-commons-dependencies</artifactId>
        <version>${spring-cloud-commons.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-netflix-dependencies</artifactId>
        <version>${spring-cloud-netflix.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-stream-dependencies</artifactId>
        <version>${spring-cloud-stream.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-task-dependencies</artifactId>
        <version>${spring-cloud-task.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-circuitbreaker-dependencies</artifactId>
        <version>${spring-cloud-circuitbreaker.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-config-dependencies</artifactId>
        <version>${spring-cloud-config.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-function-dependencies</artifactId>
        <version>${spring-cloud-function.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gateway-dependencies</artifactId>
        <version>${spring-cloud-gateway.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-consul-dependencies</artifactId>
        <version>${spring-cloud-consul.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-sleuth-dependencies</artifactId>
        <version>${spring-cloud-sleuth.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-vault-dependencies</artifactId>
        <version>${spring-cloud-vault.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-zookeeper-dependencies</artifactId>
        <version>${spring-cloud-zookeeper.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-cloudfoundry-dependencies</artifactId>
        <version>${spring-cloud-cloudfoundry.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-bus-dependencies</artifactId>
        <version>${spring-cloud-bus.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-contract-dependencies</artifactId>
        <version>${spring-cloud-contract.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-openfeign-dependencies</artifactId>
        <version>${spring-cloud-openfeign.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-kubernetes-dependencies</artifactId>
        <version>${spring-cloud-kubernetes.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <profiles>
    <profile>
      <id>spring</id>
      <repositories>
        <repository>
          <snapshots>
            <enabled>true</enabled>
          </snapshots>
          <id>spring-snapshots</id>
          <name>Spring Snapshots</name>
          <url>https://repo.spring.io/snapshot</url>
        </repository>
        <repository>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
          <id>spring-milestones</id>
          <name>Spring Milestones</name>
          <url>https://repo.spring.io/milestone</url>
        </repository>
        <repository>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
          <id>spring-releases</id>
          <name>Spring Releases</name>
          <url>https://repo.spring.io/release</url>
        </repository>
      </repositories>
      <pluginRepositories>
        <pluginRepository>
          <snapshots>
            <enabled>true</enabled>
          </snapshots>
          <id>spring-snapshots</id>
          <name>Spring Snapshots</name>
          <url>https://repo.spring.io/snapshot</url>
        </pluginRepository>
        <pluginRepository>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
          <id>spring-milestones</id>
          <name>Spring Milestones</name>
          <url>https://repo.spring.io/milestone</url>
        </pluginRepository>
      </pluginRepositories>
    </profile>
  </profiles>
</project>
