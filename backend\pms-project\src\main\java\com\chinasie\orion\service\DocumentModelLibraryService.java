package com.chinasie.orion.service;

import java.lang.String;
import java.util.List;
import com.chinasie.orion.domain.dto.DocumentModelLibraryDTO;
import com.chinasie.orion.domain.entity.DocumentModelLibrary;
import com.chinasie.orion.domain.vo.DocumentModelLibraryVO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.sdk.metadata.page.Page;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * DocumentModelLibrary 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-01 16:35:39
 */
public interface DocumentModelLibraryService  extends  OrionBaseService<DocumentModelLibrary>  {


        /**
         *  详情
         *
         * * @param id
         */
        DocumentModelLibraryVO detail(String id, String pageCode)throws Exception;

        /**
         *  新增
         *
         * * @param documentModelLibraryDTO
         */
        String create(DocumentModelLibraryDTO documentModelLibraryDTO)throws Exception;

        /**
         *  编辑
         *
         * * @param documentModelLibraryDTO
         */
        Boolean edit(DocumentModelLibraryDTO documentModelLibraryDTO)throws Exception;


        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;


        /**
         *  分页
         *
         * * @param pageRequest
         * @param  mainTableId 
         */
        Page<DocumentModelLibraryVO> pages(String mainTableId, Page<DocumentModelLibraryDTO> pageRequest)throws Exception;


        /**
            *  设置各种名称
            *
            * * @param searchConditions
            * * @param response
            */
        void  setEveryName(List<DocumentModelLibraryVO> vos)throws Exception;


        /**
         * 升版
         *
         * @param id
         * @return
         * @throws Exception
         */
        Boolean upVersion (String id) throws Exception;

        /**
         * 获取版本记录
         *
         * @return
         * @throws Exception
         */
        List<DocumentModelLibraryVO> getVersionRecords (String id) throws Exception;


        /**
         *  启用
         *
         * * @param ids
         */
        Boolean enable(List<String> ids)  throws Exception;
        /**
         *  禁用
         *
         * * @param ids
         */
        Boolean disEnable(List<String> ids)  throws Exception;

        /**
         * 新增或删除文件
         *
         * @param id
         * @param newFileDtoList
         * @throws Exception
         */
        void handleFile(String id, List<FileVO> newFileDtoList) throws Exception;

        Page<DocumentModelLibraryVO> getModelPages(Page<DocumentModelLibraryDTO> pageRequest) throws Exception;
}
