<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
    @selectionChange="tableSelectionChange"
  >
    <template #toolbarLeft>
      <BasicButton
        type="primary"
        icon="sie-icon-tianjiaxinzeng"
        @click="goAdd"
      >
        选择参数
      </BasicButton>
      <BasicButton
        icon="delete"
        :disabled="state.selectRows?.length===0"
        @click="goDelete"
      >
        移除
      </BasicButton>
    </template>
  </OrionTable>
  <ModalHandle
    ref="addDrawerRef"
    @update="reloadTable()"
  />
  <SelectModel
    ref="selectModelRef"
    @update="reloadTable()"
  />
  <FillValue
    ref="fillModelRef"
    @update="reloadTable()"
  />
</template>

<script setup lang="ts">
import {
  inject, reactive, ref, h,
} from 'vue';
import {
  OrionTable, BasicButton, DataStatusTag, openModal,
} from 'lyra-component-vue3';
import { message, Modal } from 'ant-design-vue';
import Api from '/@/api';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import ModalHandle
  from './relatedParams/ModalHandle.vue';
import SelectModel
  from './selectModel/Drawer.vue';
import FillValue from './fillValue/Drawer.vue';
import { SelectListTable } from '/@/views/pms/components';

const router = useRouter();
const emits = defineEmits([]);
const props = withDefaults(defineProps<{
    formId:string,
    projectId:string,
}>(), {
  projectId: '',
  formId: '',
});

const state = reactive({
  selectRows: [],
  details: {},
});
const addDrawerRef = ref(null);
const selectModelRef = ref(null);
const fillModelRef = ref(null);
const tableRef = ref(null);
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: {},
  smallSearchField: ['name', 'number'],
  showIndexColumn: true,
  showSmallSearch: false,
  showTableSetting: false,
  rowKey: 'id',
  api: async (params) =>
    await new Api(`/pms/plan-to-param/relation/list/${props.formId}`).fetch(params, '', 'POST').finally(() => {
      state.selectRows = [];
    }),
  columns: [
    {
      title: '参数编号',
      dataIndex: 'paramNumber',
      align: 'left',
      minWidth: 110,
      // customRender: ({ record, text }) => h('span', {
      //   class: 'action-btn',
      //   onClick: async () => {
      //     await router.push({
      //       name: 'icmManagementDetailsIndex',
      //       query: {
      //         id: record?.id,
      //       },
      //       params: {
      //         id: record?.id,
      //       },
      //     });
      //   },
      // }, text),
    },
    {
      title: '参数名称',
      align: 'left',
      minWidth: 150,
      dataIndex: 'paramName',
    },
    {
      title: '模版名称',
      align: 'left',
      dataIndex: 'modelName',
    },
    {
      title: '提供部门',
      align: 'left',
      dataIndex: 'paramProviderDeptNames',
    },
    {
      title: '参数实例',
      align: 'left',
      dataIndex: 'insName',
      customRender: ({ record }) => h('span', {
        class: record.insName ? 'action-btn' : '',
        onClick: () => {
          sessionStorage.setItem('insTab', 'true');
          router.push({
            name: 'EDMArgumentPoolDetails',
            params: {
              id: record?.paramId,
            },
          });
        },
      }, record.insName),
    },
    {
      title: '实例创建时间',
      align: 'left',
      dataIndex: 'insCreateTime',
      customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '操作',
      align: 'left',
      dataIndex: 'action',
      slots: { customRender: 'action' },
    },
  ],
  actions: [
    {
      text: '选择模板',
      isShow: (record: Record<string, any>) => !record.modelId,
      onClick: (record) => {
        selectModelRef.value.openDrawer({
          action: record?.modelName ? 'edit' : 'add',
          info: { record },
        });
      },
    },
    {
      text: '填值',
      isShow: (record: Record<string, any>) => record.modelId,
      onClick: (record) => {
        fillModelRef.value.openDrawer({
          action: record?.insId ? 'edit' : 'add',
          info: { record },
        });
      },
    },
    {
      text: '移除',
      modal(record: Record<string, any>) {
        return new Api(`/pms/plan-to-param?planId=${props.formId}`).fetch([record?.paramId], '', 'DELETE').then(() => {
          reloadTable();
        });
      },
    },
  ],
});

// 删除
function goDelete() {
  Modal.confirm({
    title: '删除确认提示',
    content: '请确认是否删除这些信息？',
    onOk() {
      return new Api(`/pms/plan-to-param?planId=${props.formId}`).fetch(state.selectRows.map((item) => item.paramId), '', 'DELETE').then(() => {
        reloadTable();
      });
    },
    onCancel() {
      Modal.destroyAll();
    },
  });
}

function reloadTable() {
  tableRef.value && tableRef.value.reload({ page: 1 });
}

function tableSelectionChange({ rows }) {
  state.selectRows = rows;
}

// 新增
function goAdd() {
  const selectListTableRef = ref();
  openModal({
    title: '选择参数',
    width: 1100,
    height: 700,
    content(h) {
      return h(SelectListTable, {
        ref: selectListTableRef,
        showLeftTree: true,
        getTreeApi,
        getTableData,
        columns: [
          {
            title: '编号',
            dataIndex: 'number',
            align: 'left',
            width: 120,
          },
          {
            title: '参数名称',
            align: 'left',
            dataIndex: 'name',
            minWidth: 150,
          },
          {
            title: '提供部门',
            align: 'left',
            dataIndex: 'providerDeptNames',
            minWidth: 100,
          },
        ],
        selectType: 'check',
        isTableTree: false,
        showTreeSearch: false,
      });
    },
    async onOk() {
      let formData = await selectListTableRef.value.getFormData();
      if (formData.selectTableData.length === 0) {
        message.warning('请选择参数');
        return Promise.reject('');
      }
      const ids = formData.selectTableData.map((item) => item.id);
      new Api(`/pms/plan-to-param?planId=${props.formId}`).fetch(ids, '', 'POST').then((res) => {
        message.success('添加参数成功');
        reloadTable();
      });
      // console.log(formData);
    },
  });
}
function getTreeApi() {
  return new Api('/pdm').fetch('', 'parameterPoolDir/tree', 'GET');
}
function getTableData(id, params) {
  return new Api('/pdm').fetch(params, `parameterPool/usepages/${id}`, 'POST');
}
</script>
