package com.chinasie.orion.domain.dto;

import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * ProjectSchemeMilestoneTemplate Entity对象
 *
 * <AUTHOR>
 * @since 2023-06-05 10:09:44
 */
@ApiModel(value = "ProjectSchemeMilestoneTemplateDTO对象", description = "项目计划里程碑模版")
@Data
public class ProjectSchemeMilestoneTemplateDTO extends ObjectDTO implements Serializable{

    /**
     * 模版名称
     */
    @ApiModelProperty(value = "模版名称")
    @NotBlank(message = "模版名称不能为空")
    private String templateName;

    /**
     * 模版顺序
     */
    @ApiModelProperty(value = "模版顺序")
    @NotNull(message = "模版顺序不能为空")
    private Long sort;
}
