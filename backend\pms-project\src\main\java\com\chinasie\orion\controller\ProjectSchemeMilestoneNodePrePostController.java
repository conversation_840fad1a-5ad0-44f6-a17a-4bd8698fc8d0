package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ProjectSchemeMilestoneNodePrePostDTO;
import com.chinasie.orion.domain.dto.TemplatePreSchemeDTO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.service.ProjectSchemeMilestoneNodePrePostService;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.mzt.logapi.starter.annotation.LogRecords;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ProjectSchemePrePostController
 *
 * @author: yangFy
 * @date: 2023/4/20 14:28
 * @description
 * <p>
 * 项目计划前后置关系
 * </p>
 */

@RestController
@RequestMapping("/projectSchemeMilestoneNodePrePost")
@Api(tags = "项目计划模板前后置关系")
public class ProjectSchemeMilestoneNodePrePostController {

    @Autowired
    private ProjectSchemeMilestoneNodePrePostService projectSchemeMilestoneNodePrePostService;

    @ApiOperation("添加前后置关系(批量)")
    @PostMapping(value = "/createBatch")
    @LogRecord(success = "【{USER{#logUserId}}】添加前后置关系(批量)", type = "项目计划模板前后置关系", subType = "添加前后置关系(批量)", bizNo = "")
    public ResponseDTO<List<String>> createBatch(@RequestBody TemplatePreSchemeDTO preSchemeDTO) throws Exception {
        return ResponseDTO.success(projectSchemeMilestoneNodePrePostService.createBatch(preSchemeDTO));
    }

    @ApiOperation("删除(批量)")
    @DeleteMapping(value = "")
    @LogRecord(success = "【{USER{#logUserId}}】删除(批量)", type = "项目计划模板前后置关系", subType = "删除(批量)", bizNo = "")
    public ResponseDTO<Boolean> deleteByIds(@RequestBody List<String> ids) throws Exception {
        return ResponseDTO.success(projectSchemeMilestoneNodePrePostService.deleteByIds(ids));
    }


    @ApiOperation("变更前置计划(批量)")
    @PutMapping(value = "/modify/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】变更前置计划(批量)", type = "项目计划模板前后置关系", subType = "变更前置计划(批量)", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> modify(@PathVariable("id") String id, List<ProjectSchemeMilestoneNodePrePostDTO> prePostDTOS) throws Exception {
        return ResponseDTO.success(projectSchemeMilestoneNodePrePostService.modify(id, prePostDTOS));
    }
}
