<template>
  <div class="modal-main-wrap">
    <div class="center-wrap">
      <div
        class="table-wrap"
      >
        <OrionTable

          ref="tableRef"
          :options="tableOption"
        />
      </div>
    </div>
    <div class="right-wrap">
      <SelectedList

        ref="selectedRef"
        @updateTableSelect="updateTableSelect"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ref, reactive, unref, h, Ref, computed,
} from 'vue';
import {
  BasicModal, DataStatusTag, Icon, useModalInner, OrionTable,
} from 'lyra-component-vue3';

import { Menu, MenuItem, message } from 'ant-design-vue';
import dayjs from 'dayjs';
import SelectedList from './SelectedList.vue';
import Api from '/@/api';
import { postProjectSchemeMilestoneNodePages } from '/@/views/pms/planTemplateManagement/api';
const props = defineProps<{
  id: string,
  templateId:string,
}>();

const selectedKeys = ref(['plan']);
const tableRef = ref(null);
const selectedRef = ref(null);
const isShowTable = ref(false);

const columns = ref([
  {
    title: '编号',
    dataIndex: 'number',
    width: 100,
  },
  {
    title: '名称',
    dataIndex: 'name',
  },

  {
    title: '状态',
    dataIndex: 'dataStatus',
    width: 100,
    customRender({ text }) {
      return text ? h(DataStatusTag, {
        statusData: text,
      }) : '';
    },
  },
  {
    title: '责任人',
    width: 100,
    dataIndex: 'rspUserName',
  },
]);
const dataSource = ref([]);
const selectRows: Ref<string[]> = ref([]);
const onSelectionChange = (selectedRowKeys, selectedRows) => {
  state.selectedRowKeys = selectedRowKeys;

  selectRows.value = selectedRows;
  selectedRef.value.setData(selectedRows);
};
const tableOption = ref({
  api: (params) => new Api(`/pms/performanceTemplate/filterIndicatorLibrary/templateId/${props.templateId}`).fetch(params, '', 'POST'),

  rowSelection: {
    selectedRowKeys: computed(() => state.selectedRowKeys),
    onChange: onSelectionChange,
  },
  showSmallSearch: false,
  showToolButton: false,
  columns: [
    {
      title: '绩效指标',
      dataIndex: 'name',
    },
    {
      title: '权重',
      dataIndex: 'weight',
    },
    {
      title: '评分标准',
      dataIndex: 'scoreStandard',
    },
    {
      title: '状态',
      dataIndex: 'status',
      slots: { customRender: 'status' },
    },
    {
      title: '修改人',
      dataIndex: 'modifyName',
      width: 80,
    },
    {
      title: '修改时间',
      dataIndex: 'modifyTime',
      slots: { customRender: 'modifyTime' },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 120,
      fixed: 'right',
    },
  ],
  showTableSetting: false,
});

const state = reactive({
  visible: false,
  tableRef: null,
  selectedRowKeys: [],
});

// 获取表格方法
function initTable(ref) {
  state.tableRef = ref;
}

function cancelHandle() {
  state.visible = false;
}

function updateTableSelect(rows: Record<string, any>[] = []) {
  tableRef.value?.setSelectedRowKeys(rows.map((item) => item?.id));
}
// 定义一个函数来选择你想要的字段
function selectFields(item, templateId) {
  return {
    indicatorId: item.indicatorId,
    remark: item.remark,
    scoreStandard: item.scoreStandard,
    indicatorName: item.indicatorName,
    templateId,
    weight: item.weight,
  };
}
defineExpose({
  async onSubmit() {
    const params = selectRows.value.map((item) => ({
      indicatorId: item.id,
      remark: item.remark,
      scoreStandard: item.scoreStandard,
      indicatorName: item.name,
      templateId: props.templateId,
      weight: item.weight,
    }));
    await new Api(`/pms/performanceTemplateToIndicator/saveTemplateId/${props.templateId}`).fetch(params, '', 'POST');
  },
});
</script>
<style scoped lang="less">
.modal-main-wrap {
  width: 100%;
  height: 100%;
  display: flex;

  .left-wrap, .right-wrap {
    width: 220px;
    flex-shrink: 0;
  }

  .left-wrap {
    display: flex;
    flex-direction: column;
  }

  .center-wrap {
    flex-grow: 1;
    width: 0;
    border-left: 1px solid #f0f0f0;
    border-right: 1px solid #f0f0f0;
  }
}
.table-wrap {
  height: 100%;
  overflow: hidden;
}

:deep(.ant-input) {
  height: 32px;
  line-height: 32px;
}
</style>
