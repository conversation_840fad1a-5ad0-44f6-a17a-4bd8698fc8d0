import {
  DataStatusTag,
  Icon,
  SelectDictVal,
  formatMoney,
  randomString,
} from 'lyra-component-vue3';
import { h, ref } from 'vue';
import { InputSearch, Select } from 'ant-design-vue';
import dayjs from 'dayjs';
import {
  openContractTableSelect,
  openPartyTableSelect,
  openProjectTableSelect,
  openTaxModel,
} from './utils';
import {
  ContractList,
  TaxRateEdit,
  ProjectList,
  PartyContractList,
} from './components';

// 编制和调整公共的计算和业务方法

// 数据已锁定/当数据状态为“开票流程中”、“对账流程中”、“已完成”时，专业中心、专业所管理员不允许修改本条收入计划，财务人员可以修改
const isLocked = (record: any) =>
  record?.lockStatus === 'lock_down' || record?.isUpdate === '0';

// 多项目 '1'单项目'0'
const isMultipleProjects = (record: any) => {
  const hasMultipleNames = (record.billingAccountInformationVOS || []).filter(
    (item) => item.name != null && item.name !== '',
  ).length > 1;
  const isMultipleProjectsFlag = record.isMultipleProjects != null && record.isMultipleProjects === '1';

  return isMultipleProjectsFlag || hasMultipleNames;
};

// 系统推送的数据
const isPushData = (record: any) => record?.dataSource === 'contract_milestone';

// 暂估收入类型
const isEstimateAmt = (record: any) => {
  const incomeConfirmType = isEstimateType(record);

  if (Array.isArray(incomeConfirmType)) {
    return incomeConfirmType[0]?.id === 'provisional_income';
  }

  return incomeConfirmType === 'provisional_income';
};

// 获取收入确认类型的值
const isEstimateType = (record: any) => {
  const incomeConfirmType = record?.incomeConfirmType;

  if (Array.isArray(incomeConfirmType)) {
    // 如果是数组，取第一个元素的 id 进行比较
    return incomeConfirmType[0]?.id;
  }
  if (typeof incomeConfirmType === 'string') {
    // 如果是字符串，直接进行比较
    return incomeConfirmType;
  }
};

// 本次暂估金额（价税合计）
const isEditableEstimateAmt = (record: any, isEdit: boolean) => {
  if (!isEdit) return false;
  if (
    isEstimateAmt(record)
    && (isMultipleProjects(record) || record?.taxRate?.length > 1)
  ) {
    return false; // 多项目/多税率时，只读
  }
  if (
    isEstimateAmt(record)
    && (!isMultipleProjects(record) || record?.taxRate?.length <= 1)
  ) {
    return true; // 单项目/单税率时，可编辑且必填
  }
  // 暂估收入类型为暂估收入时，可编辑,必填
  if (isEstimateAmt(record)) {
    return true;
  }
};

// 本次开票金额（价税合计）
const isInvAmt = (record: any, isEdit: boolean) => {
  if (!isEdit) return false;
  const incomeConfirmType = isEstimateType(record);
  const isMultiple = isMultipleProjects(record);
  const hasSingleTaxRate = record.taxRate && record.taxRate.length <= 1;
  // 检查是否为进度款发票、预付款发票、取消重开或其他收入确认类型，且为多项目或多税率时，不可编辑
  if (
    [
      'progress_payment_invoice',
      'advance_payment_invoice',
      'cancel_reopen',
      'other_income_confirm',
    ].includes(incomeConfirmType)
    && isMultiple
    && !hasSingleTaxRate
  ) {
    return false;
  }

  // 检查是否为进度款发票，且为单项目/单税率时，可编辑
  if (
    incomeConfirmType === 'progress_payment_invoice'
    && !isMultiple
    && hasSingleTaxRate
  ) {
    return true;
  }

  // 检查是否为暂估收入或取消重开时，不可编辑
  return !(
    incomeConfirmType === 'provisional_income'
    || incomeConfirmType === 'invalidation_invoice'
  );
};

// 本次作废发票金额（价税合计）
const isInvalidationInvoice = (record: any, isEdit: boolean) => {
  if (!isEdit) return false;
  const incomeConfirmType = isEstimateType(record);
  return (
    incomeConfirmType === 'invalidation_invoice'
    || incomeConfirmType === 'cancel_reopen'
  );
};
// 本次冲销暂估金额（价税合计）-预收款转收入金额（价税合计)
const isWriteOffAmt = (record: any, isEdit: boolean) => {
  if (!isEdit) return false;
  const incomeConfirmType = isEstimateType(record);
  return (
    incomeConfirmType === 'progress_payment_invoice'
    || incomeConfirmType === 'other_income_confirm'
  );
};

// 是否推送的数据
const isPush = (record: any, isEdit: boolean) => {
  if (isPushData(record)) {
    return false;
  }
  return isEdit;
};

// 单税率时插入一条数据到多税率弹框 tax税率
const addTaxRate = (record: any) => [
  {
    id: `ADD${randomString(6)}`,
    name: record.name,
    projectId: record.projectId,
    projectNumber: record.projectNumber,
    incomeWbsNumber: record.incomeWbsNumber,
    actualAcceptanceAmt: record.actualAcceptanceAmt,
    taxRate: record.taxRate,
    totalAmtTax: record.totalAmtTax,
    vatAmt: '',
    amtExTax: '',
  },
];

// 单税率计算不含税金额
function calculateExTax(amount: number, taxRate: number): number {
  const result = amount / (1 + taxRate * 0.01);
  return parseFloat(result.toFixed(2));
}

// 创建了一个 createOption 函数，用于生成对象数组，减少了代码重复。
function createOption(
  id: string | undefined,
  name: string | undefined,
): { id: string; name: string }[] {
  return [
    {
      id: id ?? '',
      name: name ?? '',
    },
  ];
}

// 切换收入确认类型封装方法
function updateRowValue(params: any, val: string) {
  const record = params?.record ?? {};
  // 单一税率
  const singleTax = record?.taxRate && record?.taxRate.length === 1;
  // 多税率
  const isMultipleTax = record?.taxRate && record?.taxRate.length > 1;
  // 单一税率的值
  const singleTaxRate = singleTax ? params?.record?.taxRate && Number(params?.record?.taxRate[0]) : 0;

  const amtExTax = record.estimateAmt ?? 0; // 本次暂估金额（价税合计）
  const invAmt = record.invAmt ?? 0; // 开票金额（价税合计）
  const cancelInvAmt = val !== 'cancel_reopen' && val !== 'invalidation_invoice'
    ? 0
    : record.cancelInvAmt ?? 0; // 本次作废开票金额（价税合计）
  const writeOffAmt = record.writeOffAmt ?? 0; // 本次冲销暂估金额（价税合计）
  const advancePayIncomeAmt = record.advancePayIncomeAmt ?? 0; // 预收款转收入金额（价税合计）

  const amountExcludingTax = record?.amountExcludingTax ?? 0; // 不含税总计值

  // 计算不含税金额
  const estimateAmtExTaxNum = val === 'provisional_income'
    ? isMultipleTax
      ? amountExcludingTax
      : singleTaxRate
        ? calculateExTax(amtExTax, singleTaxRate)
        : 0
    : 0; // 本次暂估金额（不含税）
  const invAmtExTaxNum = val !== 'provisional_income' && val !== 'invalidation_invoice'
    ? isMultipleTax
      ? amountExcludingTax
      : singleTaxRate
        ? calculateExTax(invAmt, singleTaxRate)
        : 0
    : 0; // 本次开票金额（不含税）
  const cancelInvAmtExTaxNum = isMultipleTax
    ? cancelInvAmt
    : calculateExTax(cancelInvAmt, singleTaxRate); // 本次作废发票金额（不含税）
  const milestoneAmtExTaxNum = isMultipleTax
    ? writeOffAmt
    : calculateExTax(writeOffAmt, singleTaxRate); // 本次冲销暂估金额（不含税)
  const writeOffAmtExTaxNum = isMultipleTax
    ? advancePayIncomeAmt
    : calculateExTax(advancePayIncomeAmt, singleTaxRate); // 预收款转收入金额（价税合计）

  const incomePlanAmtTotal = val === 'advance_payment_invoice'
    ? 0
    : Number(
      estimateAmtExTaxNum
            + invAmtExTaxNum
            + cancelInvAmtExTaxNum
            + milestoneAmtExTaxNum
            + writeOffAmtExTaxNum,
    ).toFixed(2);

  params.setRowValue({
    incomePlanAmt: incomePlanAmtTotal || 0,
    estimateAmtExTax: estimateAmtExTaxNum,
    invAmtExTax: invAmtExTaxNum,
    estimateAmt: val === 'provisional_income' ? amtExTax : '', // 本次暂估金额（价税合计）
    invAmt:
      val === 'progress_payment_invoice'
      || val === 'advance_payment_invoice'
      || val === 'cancel_reopen'
      || val === 'other_income_confirm'
        ? invAmt
        : '', // 本次开票金额（价税合计）
    cancelInvAmt:
      val !== 'cancel_reopen' && val !== 'invalidation_invoice'
        ? ''
        : params?.record?.cancelInvAmt,
  });
}

// 选择税率
function updateRowDataTax(value: number[], methods: any) {
  const result: number = value?.[0];
  const incomeConfirmType = isEstimateType(methods?.record);

  // 单一税率
  const singleTax = value?.length === 1;
  // 多税率
  const isMultipleTax = value?.length > 1;
  // 单一税率的值
  const singleTaxRate = Number(result);
  // 类型等于暂估收入或者进度款开票
  const isAddTAx = incomeConfirmType === 'provisional_income'
    || incomeConfirmType === 'progress_payment_invoice';
  // 本次暂估金额-本次开票金额
  const totalAmount = incomeConfirmType === 'provisional_income'
    ? methods?.record.estimateAmt
    : incomeConfirmType === 'progress_payment_invoice'
      ? methods?.record.invAmt
      : '';
  const paramsData = {
    name: methods?.record?.projectName || '', // 项目名称
    projectId: methods?.record?.projectId || '', // 项目id
    projectNumber: methods?.record?.projectNumber || '', // 项目编号
    incomeWbsNumber: methods?.record?.incomeWbsNumber || 0, // WBS金额
    actualAcceptanceAmt: methods?.record?.actualAcceptanceAmt || 0, // 实际验收金额
    taxRate: value || [], // 税率
    totalAmtTax: totalAmount || '', // 本次暂估金额（价税合计）/本次开票金额
  };
  // 单一税率-新增一条数据
  const addTaxRateList = singleTax ? addTaxRate(paramsData) : [];

  // 提取并检查记录中的值，提供默认值
  const amtExTax = methods?.record?.estimateAmt || 0; // 多税率弹窗】中【不含税金额】合计值
  const invAmt = methods?.record?.invAmt || 0; // 多税率弹窗】中【不含税金额】合计值
  const cancelInvAmt = methods?.record?.cancelInvAmt || 0; // 本次作废发票金额（价税合计）
  const writeOffAmt = methods?.record?.writeOffAmt || 0; // 本次冲销暂估金额（价税合计)
  const advancePayIncomeAmt = methods?.record?.advancePayIncomeAmt || 0; // 预收款转收入金额（价税合计）

  // 计算不含税金额
  const estimateAmtExTaxNum = incomeConfirmType === 'provisional_income'
    ? isMultipleTax
      ? 0
      : singleTaxRate
        ? calculateExTax(amtExTax, singleTaxRate)
        : 0
    : 0; // 本次暂估金额（不含税）
  const invAmtExTaxNum = incomeConfirmType !== 'provisional_income'
    && incomeConfirmType !== 'invalidation_invoice'
    ? isMultipleTax
      ? 0
      : singleTaxRate
        ? calculateExTax(invAmt, singleTaxRate)
        : 0
    : 0; // 本次开票金额（不含税）
  const cancelInvAmtExTaxNum = isMultipleTax
    ? cancelInvAmt
    : calculateExTax(cancelInvAmt, singleTaxRate); // 本次作废发票金额（不含税）
  const milestoneAmtExTaxNum = isMultipleTax
    ? writeOffAmt
    : calculateExTax(writeOffAmt, singleTaxRate); // 本次冲销暂估金额（不含税)
  const writeOffAmtExTaxNum = isMultipleTax
    ? advancePayIncomeAmt
    : calculateExTax(advancePayIncomeAmt, singleTaxRate); // 预收款转收入金额（价税合计）

  // 计算总收入计划金额
  const incomePlanAmtTotal = incomeConfirmType === 'advance_payment_invoice'
    ? 0
    : Number(
      estimateAmtExTaxNum
            + invAmtExTaxNum
            + cancelInvAmtExTaxNum
            + milestoneAmtExTaxNum
            + writeOffAmtExTaxNum,
    ).toFixed(2);
  // 更新行数据
  methods.setRowValue({
    billingAccountInformationVOS: singleTax && isAddTAx ? addTaxRateList : [],
    incomePlanAmt: value.length === 1 ? incomePlanAmtTotal : '',
    projectNumber: value.length > 1 ? '多项目' : methods.record.projectNumber,
    projectName: value.length > 1 ? '多项目' : methods.record.projectName,
    projectRspUserId:
      value.length > 1
        ? '多项目'
        : [
          {
            id: methods.record.resPerson,
            name: methods.record.resPersonName,
          },
        ],
    projectRspUserName:
      value.length > 1 ? '多项目' : methods.record.projectRspUserName,
    cancelInvAmtExTax: singleTax ? cancelInvAmtExTaxNum : cancelInvAmt,
    writeOffAmtExTax: singleTax ? milestoneAmtExTaxNum : writeOffAmt,
    advPayIncomeAmtExTax: singleTax ? writeOffAmtExTaxNum : advancePayIncomeAmt,
    estimateAmtExTax: singleTax ? estimateAmtExTaxNum : '',
    invAmtExTax: singleTax ? invAmtExTaxNum : '',
    estimateAmt:
      incomeConfirmType === 'provisional_income' && isMultipleTax
        ? totalAmount
        : methods?.record?.estimateAmt, // 本次暂估金额（价税合计）
    invAmt:
      (incomeConfirmType === 'progress_payment_invoice'
        || incomeConfirmType === 'advance_payment_invoice'
        || incomeConfirmType === 'cancel_reopen'
        || incomeConfirmType === 'other_income_confirm')
      && isMultipleTax
        ? totalAmount
        : methods?.record?.invAmt, // 本次开票金额（价税合计）
  });
}

// 弹框税率的回调处理方法
function updateRowData(data: any, methods: any) {
  try {
    const incomeConfirmType = isEstimateType(methods?.record);
    // 单一税率
    const singleTax = data?.advanceData?.length === 1;
    // 多税率
    const isMultipleTax = data?.advanceData?.length > 1;
    // 单一税率的值
    const singleTaxRate = singleTax ? Number(data?.taxRate[0]) : 0;
    // 提取并检查记录中的值，提供默认值
    const amtExTax = data?.amountExcludingTax || 0; // 本次暂估金额（价税合计）
    const invAmt = data?.amountExcludingTax || 0; // 本次开票金额（价税合计）
    const cancelInvAmt = methods?.record?.cancelInvAmt || 0; // 本次作废开票金额（价税合计）
    const writeOffAmt = methods?.record?.writeOffAmt || 0; // 本次冲销暂估金额（价税合计）
    const advancePayIncomeAmt = methods?.record?.advancePayIncomeAmt || 0; // 预收款转收入金额（价税合计）

    // 计算不含税金额
    const estimateAmtExTaxNum = incomeConfirmType === 'provisional_income'
      ? isMultipleTax
        ? amtExTax
        : singleTaxRate
          ? calculateExTax(data?.totalAmountIncludingTax, singleTaxRate)
          : 0
      : 0; // 本次暂估金额（不含税）
    const invAmtExTaxNum = incomeConfirmType !== 'provisional_income'
      && incomeConfirmType !== 'invalidation_invoice'
      ? isMultipleTax
        ? invAmt
        : singleTaxRate
          ? calculateExTax(data?.totalAmountIncludingTax, singleTaxRate)
          : 0
      : 0; // 本次开票金额（不含税）
    const cancelInvAmtExTaxNum = isMultipleTax
      ? cancelInvAmt
      : calculateExTax(cancelInvAmt, singleTaxRate); // 本次作废发票金额（不含税）
    const milestoneAmtExTaxNum = isMultipleTax
      ? writeOffAmt
      : calculateExTax(writeOffAmt, singleTaxRate); // 本次冲销暂估金额（不含税）
    const writeOffAmtExTaxNum = isMultipleTax
      ? advancePayIncomeAmt
      : calculateExTax(advancePayIncomeAmt, singleTaxRate); // 预收款转收入金额（价税合计）

    // 计算总收入计划金额
    const incomePlanAmtTotal = incomeConfirmType === 'advance_payment_invoice'
      ? 0
      : Number(
        estimateAmtExTaxNum
              + invAmtExTaxNum
              + cancelInvAmtExTaxNum
              + milestoneAmtExTaxNum
              + writeOffAmtExTaxNum,
      ).toFixed(2);

    // 更新行数据
    methods.setRowValue({
      totalAmount: data?.totalAmountIncludingTax || 0,
      billingAccountInformationVOS: data.advanceData,
      taxRateName: data?.taxRateName,
      estimateAmt:
        incomeConfirmType === 'provisional_income'
          ? data.totalAmountIncludingTax
          : '', // 本次暂估金额（价税合计）
      invAmt:
        incomeConfirmType === 'progress_payment_invoice'
        || incomeConfirmType === 'advance_payment_invoice'
        || incomeConfirmType === 'cancel_reopen'
        || incomeConfirmType === 'other_income_confirm'
          ? data.totalAmountIncludingTax
          : '', // 本次开票金额（价税合计）
      projectNumber:
        data?.advanceData.length > 1
          ? '多项目'
          : data?.advanceData?.[0].projectNumber,
      projectName:
        data?.advanceData.length > 1 ? '多项目' : data?.advanceData[0].name,
      projectId:
        data?.advanceData.length > 1 ? '' : data?.advanceData[0].projectId, // 项目id
      projectRspUserId:
        data?.advanceData.length > 1
          ? '多项目'
          : [
            {
              id: data?.advanceData[0].resPerson,
              name: data?.advanceData[0].resPersonName,
            },
          ],
      projectRspUserName:
        data?.advanceData.length > 1
          ? '多项目'
          : data?.advanceData[0].resPersonName,
      incomePlanAmt: incomePlanAmtTotal || 0,
      cancelInvAmtExTax: singleTax ? cancelInvAmtExTaxNum : cancelInvAmt,
      writeOffAmtExTax: singleTax ? milestoneAmtExTaxNum : writeOffAmt,
      advPayIncomeAmtExTax: singleTax
        ? writeOffAmtExTaxNum
        : advancePayIncomeAmt,
      estimateAmtExTax: estimateAmtExTaxNum,
      invAmtExTax: invAmtExTaxNum,
      amountExcludingTax: data?.amountExcludingTax, // 不含税总计
      totalAmountIncludingTax: data?.totalAmountIncludingTax, // 含税总计
    });

    methods.edit(data?.taxRate?.split('、') ?? []);
  } catch (error) {
    // console.error("更新行数据时发生错误:", error);
  }
}

// 暂估输入金额处理业务方法
function updateRowZgData(value: any, params: any) {
  // 收入类型
  const incomeConfirmType = isEstimateType(params?.record);
  // 税率为空
  const isEmpty = params?.record.taxRate?.length === 0;
  // 单一税率
  const singleTax = params?.record.taxRate?.length === 1;
  // 多税率
  const isMultipleTax = params?.record.taxRate?.length > 1;
  // 单一税率的值
  const singleTaxRate = singleTax ? Number(params?.record?.taxRate[0]) : 0;
  // 类型等于暂估收入或者进度款开票
  const isAddTAx = incomeConfirmType === 'provisional_income'
    || incomeConfirmType === 'progress_payment_invoice';
  // 多项目回显后的数据
  const isMultipleProjectsList = params?.record?.billingAccountInformationVOS?.length > 0
    ? params?.record?.billingAccountInformationVOS
    : [];
  const paramsData = {
    name: params?.record?.projectName, // 项目名称
    projectNumber: params?.record?.projectNumber, // 项目编号
    projectId: params?.record?.projectId || '', // 项目id
    incomeWbsNumber: params?.record?.incomeWbsNumber, // WBS金额
    actualAcceptanceAmt: params?.record?.actualAcceptanceAmt, // 实际验收金额
    taxRate: params?.record.taxRate, // 税率
    totalAmtTax: value, // 本次暂估金额
  };
  // 单一税率-新增一条数据
  const addTaxRateList = singleTax ? addTaxRate(paramsData) : [];

  // 获取本次暂估金额（价税合计）的不含税金额
  const isNoTax = calculateExTax(value, singleTaxRate);

  // 提取并检查记录中的值，提供默认值
  const amtExTax = params?.record?.amountExcludingTax || 0; // 本次暂估金额(价税合计)
  const invAmt = params?.record.invAmtExTax; // 本次开票金额(价税合计)
  const cancelInvAmt = params?.record?.cancelInvAmt || 0; // 本次作废开票金额(价税合计)
  const writeOffAmt = params?.record?.writeOffAmt || 0; //  本次冲销暂估金额（价税合计）
  const advancePayIncomeAmt = params?.record?.advancePayIncomeAmt || 0; // 预收款转收入金额（价税合计）

  // 计算不含税金额
  const estimateAmtExTaxNum = incomeConfirmType === 'provisional_income' && singleTax
    ? isNoTax
    : amtExTax; // 本次暂估金额（不含税）
  const invAmtExTaxNum = incomeConfirmType !== 'provisional_income'
    && incomeConfirmType !== 'invalidation_invoice'
    ? invAmt
    : 0; // 本次开票金额（不含税）
  const cancelInvAmtExTaxNum = isMultipleTax
    ? cancelInvAmt
    : calculateExTax(cancelInvAmt, singleTaxRate); // 本次作废开票金额（不含税）
  const milestoneAmtExTaxNum = isMultipleTax
    ? writeOffAmt
    : calculateExTax(writeOffAmt, singleTaxRate); // 本次冲销暂估金额（不含税）
  const writeOffAmtExTaxNum = isMultipleTax
    ? advancePayIncomeAmt
    : calculateExTax(advancePayIncomeAmt, singleTaxRate); // 预收款转收入金额（不含税）

  // 计算总收入计划金额
  const incomePlanAmtTotal = incomeConfirmType === 'advance_payment_invoice'
    ? 0
    : Number(
      estimateAmtExTaxNum
            + invAmtExTaxNum
            + cancelInvAmtExTaxNum
            + milestoneAmtExTaxNum
            + writeOffAmtExTaxNum,
    ).toFixed(2);

  // 更新行数据
  params.setRowValue({
    billingAccountInformationVOS: isEmpty
      ? []
      : singleTax && isAddTAx
        ? addTaxRateList
        : isMultipleProjectsList,
    incomePlanAmt: incomePlanAmtTotal || 0,
    cancelInvAmtExTax: cancelInvAmtExTaxNum,
    writeOffAmtExTax: milestoneAmtExTaxNum,
    advPayIncomeAmtExTax: writeOffAmtExTaxNum,
    estimateAmtExTax: estimateAmtExTaxNum,
    invAmtExTax: invAmtExTaxNum,
  });
  params.edit(value);
}

// 开票金额出来业务方法
function updateRowKpData(value: any, params: any) {
  // 收入类型
  const incomeConfirmType = isEstimateType(params?.record);
  // 税率为空
  const isEmpty = params?.record.taxRate?.length === 0;
  // 单一税率
  const singleTax = params?.record.taxRate?.length === 1;
  // 多税率
  const isMultipleTax = params?.record.taxRate?.length > 1;
  // 单一税率的值
  const singleTaxRate = singleTax ? Number(params?.record?.taxRate[0]) : 0;
  // 类型等于暂估收入或者进度款开票
  const isAddTAx = incomeConfirmType === 'provisional_income'
    || incomeConfirmType === 'progress_payment_invoice';
  // 多项目回显后的数据
  const isMultipleProjectsList = params?.record?.billingAccountInformationVOS?.length > 0
    ? params?.record?.billingAccountInformationVOS
    : [];
  const paramsData = {
    name: params?.record?.projectName, // 项目名称
    projectId: params?.record?.projectId || '', // 项目id
    projectNumber: params?.record?.projectNumber, // 项目编号
    incomeWbsNumber: params?.record?.incomeWbsNumber, // WBS金额
    actualAcceptanceAmt: params?.record?.actualAcceptanceAmt, // 实际验收金额
    taxRate: params?.record.taxRate, // 税率
    totalAmtTax: value, // 本次开票金额
  };
  // 单一税率-新增一条数据
  const addTaxRateList = singleTax ? addTaxRate(paramsData) : [];

  // 获取本次开票金额（价税合计）的不含税金额
  const isNoTax = calculateExTax(value, singleTaxRate);

  // 提取并检查记录中的值，提供默认值
  const amtExTax = params?.recrod?.estimateAmtExTax; // 本次暂估金额(价税合计)
  const invAmt = params?.record?.amountExcludingTax || 0; // 本次开票金额(价税合计)
  const cancelInvAmt = params?.record?.cancelInvAmt || 0; // 本次作废开票金额(价税合计)
  const writeOffAmt = params?.record?.writeOffAmt || 0; //  本次冲销暂估金额（价税合计）
  const advancePayIncomeAmt = params?.record?.advancePayIncomeAmt || 0; // 预收款转收入金额（价税合计）
  // 计算不含税金额
  const estimateAmtExTaxNum = incomeConfirmType === 'provisional_income' ? amtExTax : 0; // 本次暂估金额(不含税)
  const invAmtExTaxNum = incomeConfirmType !== 'provisional_income'
    && incomeConfirmType !== 'invalidation_invoice'
    && singleTax
    ? isNoTax
    : invAmt; // 本次开票金额（不含税）
  const cancelInvAmtExTaxNum = isMultipleTax
    ? cancelInvAmt
    : calculateExTax(cancelInvAmt, singleTaxRate); // 本次作废开票金额（不含税）
  const milestoneAmtExTaxNum = isMultipleTax
    ? writeOffAmt
    : calculateExTax(writeOffAmt, singleTaxRate); // 本次冲销暂估金额（不含税）
  const writeOffAmtExTaxNum = isMultipleTax
    ? advancePayIncomeAmt
    : calculateExTax(advancePayIncomeAmt, singleTaxRate); // 预收款转收入金额（不含税）

  // 计算总收入计划金额
  const incomePlanAmtTotal = incomeConfirmType === 'advance_payment_invoice'
    ? 0
    : Number(
      estimateAmtExTaxNum
            + invAmtExTaxNum
            + cancelInvAmtExTaxNum
            + milestoneAmtExTaxNum
            + writeOffAmtExTaxNum,
    ).toFixed(2);
  // 更新行数据
  params.setRowValue({
    billingAccountInformationVOS: isEmpty
      ? []
      : singleTax && isAddTAx
        ? addTaxRateList
        : isMultipleProjectsList,
    incomePlanAmt: incomePlanAmtTotal || 0,
    cancelInvAmtExTax: cancelInvAmtExTaxNum,
    writeOffAmtExTax: milestoneAmtExTaxNum,
    advPayIncomeAmtExTax: writeOffAmtExTaxNum,
    estimateAmtExTax: estimateAmtExTaxNum,
    invAmtExTax: invAmtExTaxNum,
  });
  params.edit(value);
}

// 本次作废开票金额业务方法
function updateRowZfData(value: any, params: any) {
  // 收入类型
  const incomeConfirmType = isEstimateType(params?.record);
  // 单一税率
  const singleTax = params?.record.taxRate?.length === 1;
  // 多税率
  const isMultipleTax = params?.record.taxRate?.length > 1;
  // 单一税率的值
  const singleTaxRate = singleTax ? Number(params?.record?.taxRate[0]) : 0;

  // 提取并检查记录中的值，提供默认值
  const amtExTax = params?.record?.estimateAmtExTax || 0; // 本次暂估金额(不含税)
  const invAmt = params?.record?.invAmtExTax || 0; // 本次开票金额(不含税)
  const cancelInvAmt = params?.record?.cancelInvAmt || 0; // 本次作废开票金额(价税合计)
  const writeOffAmt = params?.record?.writeOffAmt || 0; // 本次冲销暂估金额（价税合计）
  const advancePayIncomeAmt = params?.record?.advancePayIncomeAmt || 0; // 本次预收款转收入金额（价税合计）

  // 本次作废发票金额（价税合计）的不含税金额
  const isNoTax = calculateExTax(value, singleTaxRate);

  // 计算不含税金额
  const estimateAmtExTaxNum = incomeConfirmType === 'provisional_income' ? amtExTax : 0; // 本次暂估金额（不含税）
  const invAmtExTaxNum = incomeConfirmType !== 'provisional_income'
    && incomeConfirmType !== 'invalidation_invoice'
    ? invAmt
    : 0; // 本次开票金额（不含税）
  const cancelInvAmtExTaxNum = isMultipleTax ? cancelInvAmt : isNoTax; // 本次作废开票金额(不含税)
  const milestoneAmtExTaxNum = isMultipleTax
    ? writeOffAmt
    : calculateExTax(writeOffAmt, singleTaxRate); // 本次冲销暂估金额（不含税）
  const writeOffAmtExTaxNum = isMultipleTax
    ? advancePayIncomeAmt
    : calculateExTax(advancePayIncomeAmt, singleTaxRate); // 预收款转收入金额（不含税）

  // 计算总收入计划金额
  const incomePlanAmtTotal = incomeConfirmType === 'advance_payment_invoice'
    ? 0
    : Number(
      estimateAmtExTaxNum
            + invAmtExTaxNum
            + cancelInvAmtExTaxNum
            + milestoneAmtExTaxNum
            + writeOffAmtExTaxNum,
    ).toFixed(2);

  // 更新行数据
  params.setRowValue({
    incomePlanAmt: incomePlanAmtTotal || 0,
    cancelInvAmtExTax: cancelInvAmtExTaxNum,
    writeOffAmtExTax: milestoneAmtExTaxNum,
    advPayIncomeAmtExTax: writeOffAmtExTaxNum,
    estimateAmtExTax: estimateAmtExTaxNum,
    invAmtExTax: invAmtExTaxNum,
  });
  params.edit(value);
}

// 冲销暂估金额业务方法
function updateRowCwData(value: any, params: any) {
  // 收入类型
  const incomeConfirmType = isEstimateType(params?.record);
  // 单一税率
  const singleTax = params?.record.taxRate?.length === 1;
  // 多税率
  const isMultipleTax = params?.record.taxRate?.length > 1;
  // 单一税率的值
  const singleTaxRate = singleTax ? Number(params?.record?.taxRate[0]) : 0;

  // 提取并检查记录中的值，提供默认值
  const amtExTax = params?.record?.estimateAmtExTax || 0; // 本次暂估金额(不含税)
  const invAmt = params?.record?.invAmtExTax || 0; // 本次开票金额(不含税)
  const cancelInvAmt = params?.record?.cancelInvAmt || 0; // 本次作废发票金额（不含税）
  const writeOffAmt = params?.record?.writeOffAmt || 0; // 本次冲销暂估金额（不含税）
  const advancePayIncomeAmt = params?.record?.advancePayIncomeAmt || 0; // 预收款转收入金额（不含税）

  // 本次预收款转收入金额（价税合计）的不含税金额
  const isNoTax = calculateExTax(value, singleTaxRate);

  // 计算不含税金额
  const estimateAmtExTaxNum = incomeConfirmType === 'provisional_income' ? amtExTax : 0; // 暂估金额
  const invAmtExTaxNum = incomeConfirmType !== 'provisional_income'
    && incomeConfirmType !== 'invalidation_invoice'
    ? invAmt
    : 0; // 开票金额
  const cancelInvAmtExTaxNum = isMultipleTax
    ? cancelInvAmt
    : calculateExTax(cancelInvAmt, singleTaxRate); // 作废开票金额
  const milestoneAmtExTaxNum = isMultipleTax ? writeOffAmt : isNoTax; // 本次冲销暂估金额（不含税）
  const writeOffAmtExTaxNum = isMultipleTax
    ? advancePayIncomeAmt
    : calculateExTax(advancePayIncomeAmt, singleTaxRate); // 预收款转收入金额（不含税）

  // 计算总收入计划金额
  const incomePlanAmtTotal = incomeConfirmType === 'advance_payment_invoice'
    ? 0
    : Number(
      estimateAmtExTaxNum
            + invAmtExTaxNum
            + cancelInvAmtExTaxNum
            + milestoneAmtExTaxNum
            + writeOffAmtExTaxNum,
    ).toFixed(2);

  // 更新行数据
  params.setRowValue({
    incomePlanAmt: incomePlanAmtTotal || 0,
    cancelInvAmtExTax: cancelInvAmtExTaxNum,
    writeOffAmtExTax: milestoneAmtExTaxNum,
    advPayIncomeAmtExTax: writeOffAmtExTaxNum,
    estimateAmtExTax: estimateAmtExTaxNum,
    invAmtExTax: invAmtExTaxNum,
  });
  params.edit(value);
}

// 预收款转收入金额业务方法
function updateRowYsData(value: any, params: any) {
  // 收入类型
  const incomeConfirmType = isEstimateType(params?.record);
  // 单一税率
  const singleTax = params?.record.taxRate?.length === 1;
  // 多税率
  const isMultipleTax = params?.record.taxRate?.length > 1;
  // 单一税率的值
  const singleTaxRate = singleTax ? Number(params?.record?.taxRate[0]) : 0;

  // 提取并检查记录中的值，提供默认值
  const amtExTax = params?.record?.estimateAmtExTax || 0; // 本次暂估金额(不含税)
  const invAmt = params?.record?.invAmtExTax || 0; // 本次开票金额(不含税)
  const cancelInvAmt = params?.record?.cancelInvAmt || 0; // 本次作废发票金额（不含税）
  const writeOffAmt = params?.record?.writeOffAmt || 0; // 本次冲销暂估金额（不含税）
  const advancePayIncomeAmt = params?.record?.advancePayIncomeAmt || 0; // 预收款转收入金额（不含税）

  // 本次预收款转收入金额（价税合计）的不含税金额
  const isNoTax = calculateExTax(value, singleTaxRate);

  // 计算不含税金额
  const estimateAmtExTaxNum = incomeConfirmType === 'provisional_income' ? amtExTax : 0; // 暂估金额（不含税）
  const invAmtExTaxNum = incomeConfirmType !== 'provisional_income'
    && incomeConfirmType !== 'invalidation_invoice'
    ? invAmt
    : 0; // 开票金额（不含税）
  const cancelInvAmtExTaxNum = isMultipleTax
    ? cancelInvAmt
    : calculateExTax(cancelInvAmt, singleTaxRate); // 作废开票金额（不含税）
  const milestoneAmtExTaxNum = isMultipleTax
    ? writeOffAmt
    : calculateExTax(writeOffAmt, singleTaxRate); // 本次冲销暂估金额（不含税）
  const writeOffAmtExTaxNum = isMultipleTax ? advancePayIncomeAmt : isNoTax; // 预收款转收入金额（不含税）

  // 计算总收入计划金额
  const incomePlanAmtTotal = incomeConfirmType === 'advance_payment_invoice'
    ? 0
    : Number(
      estimateAmtExTaxNum
            + invAmtExTaxNum
            + cancelInvAmtExTaxNum
            + milestoneAmtExTaxNum
            + writeOffAmtExTaxNum,
    ).toFixed(2);

  // 更新行数据
  params.setRowValue({
    incomePlanAmt: incomePlanAmtTotal || 0,
    cancelInvAmtExTax: cancelInvAmtExTaxNum,
    writeOffAmtExTax: milestoneAmtExTaxNum,
    advPayIncomeAmtExTax: writeOffAmtExTaxNum,
    estimateAmtExTax: estimateAmtExTaxNum,
    invAmtExTax: invAmtExTaxNum,
  });
  params.edit(value);
}

// 项目编号业务方法
function updateProjectCode(data: any, methods: any) {
  // 收入类型
  const incomeConfirmType = isEstimateType(methods?.record);
  // 税率为空
  const isEmpty = methods?.record.taxRate?.length === 0;
  // 单一税率
  const singleTax = methods?.record.taxRate?.length === 1;
  // 本次暂估金额-本次开票金额
  const totalAmount = incomeConfirmType === 'provisional_income'
    ? methods?.record.estimateAmt
    : incomeConfirmType === 'progress_payment_invoice'
      ? methods?.record.invAmt
      : '';
  // 类型等于暂估收入或者进度款开票
  const isAddTAx = incomeConfirmType === 'provisional_income'
    || incomeConfirmType === 'progress_payment_invoice';
  // 多项目回显后的数据
  const isMultipleProjectsList = methods?.record?.billingAccountInformationVOS?.length > 0
    ? methods?.record?.billingAccountInformationVOS
    : [];
  const paramsData = {
    name: data?.name, // 项目名称
    projectNumber: data?.number, // 项目编号
    projectId: methods?.record?.projectId || data?.id, // 项目id
    incomeWbsNumber: data?.incomeWbsNumber, // WBS金额
    actualAcceptanceAmt: data?.actualAcceptanceAmt, // 实际验收金额
    taxRate: methods?.record.taxRate, // 税率
    totalAmtTax: totalAmount, // 本次开票金额/本次暂估金额
  };
  // 单一税率-新增一条数据
  const addTaxRateList = singleTax ? addTaxRate(paramsData) : [];
  methods.setRowValue({
    billingAccountInformationVOS: isEmpty
      ? []
      : singleTax && isAddTAx
        ? addTaxRateList
        : isMultipleProjectsList,
    projectName: data?.name || data?.projectName,
    projectId: data?.id,
    incomeWbsNumber: data?.incomeWbsNumber,
    actualAcceptanceAmt: data?.actualAcceptanceAmt,
    projectRspUserId: data?.resPerson
      ? [
        {
          id: data?.resPerson || '',
          name: data?.resPersonName || '',
        },
      ]
      : [],
    projectRspUserName: data?.resPersonName,
  });
  methods.edit(data?.number);
}

// 数据管理
export function getColumns(type: string) {
  const columns: any[] = [
    {
      title: '专业所',
      show: ['management'],
      dataIndex: 'expertiseStationTitle',
    },
    {
      title: '专业中心',
      dataIndex: 'expertiseCenterTitle',
    },
    {
      title: '专业中心数据状态',
      dataIndex: 'lockStatusName',
      width: 200,
      show: ['center'],
    },
    {
      title: '专业所数据状态',
      dataIndex: 'lockStatusName',
      width: 200,
      show: ['management'],
    },
  ];
  return columns.filter(
    (item) => item.show === undefined || item.show.includes(type),
  );
}

// 编制表格
export function getCompilationTableColumns(
  isEdit: boolean,
  navDetails: (type: string, record: Record<string, any>) => void,
  fn: () => void,
  fnStation: (id: string) => void,
  handleType: (val: string, record: Record<string, any>) => void,
) {
  const actions = isEdit
    ? [
      {
        title: '操作',
        dataIndex: 'actions',
        width: 60,
        fixed: 'right',
        slots: { customRender: 'actions' },
      },
    ]
    : [];
  const columns: any[] = [
    {
      dataIndex: 'contractNumber',
      title: '合同编码',
      edit: (text, record) =>
        (isLocked(record) ? false : isPush(record, isEdit)),
      fixed: 'left',
      width: 90,
      editComponent(methods) {
        const selectRef = ref();
        return h(InputSearch, {
          class: 'w-full',
          ref: selectRef,
          value: methods.getComponentValue(),
          onSearch() {
            openContractTableSelect(ContractList, {
              cb: (data) => {
                if (!data) {
                  return;
                }
                const dataTaxRate = data?.taxRate
                  ? (data?.taxRate ?? '').split('、')
                  : [];
                const dataProjectRspUserId = createOption(
                  data?.projectRspUserId,
                  data?.projectRspUserName,
                );

                const rowValue = {
                  ...data,
                  id: methods?.record.id
                    ? methods?.record.id
                    : `ADD${randomString(6)}`,
                  taxRate: dataTaxRate,
                  projectRspUserId: dataProjectRspUserId,
                };
                const contract_number = data.contractNumber || data.contractName;
                methods.setRowValue(rowValue);
                methods.edit(contract_number ?? data.name);
              },
            });
          },
        });
      },
      editRule: true,
    },
    {
      title: '合同名称',
      width: 90,
      fixed: 'left',
      dataIndex: 'contractName',
      edit: (text, record) =>
        (isLocked(record)
          ? false
          : isPush(record, isEdit)
            && !!(
              record.contractNumber
              && record.contractNumber.includes('未签订合同')
            )),
      editRule: () => [
        {
          type: 'string',
          required: true,
        },
      ],
      format(params, record): any {
        return record?.contractName;
      },
      editComponent: 'Input',
      customRender({ text, record }) {
        return record.contractId
          ? h(
            'div',
            {
              class: 'box-big',
              onClick: () => {
                navDetails('2', record);
              },
            },
            record.contractName,
          )
          : h(
            'div',
            {
              class: 'box-big-normal',
            },
            text
                || (record.contractNumber
                && record.contractNumber.includes('未签订合同')
                  ? '未签订合同'
                  : ''),
          );
      },
    },
    {
      title: '里程碑名称',
      width: 90,
      fixed: 'left',
      dataIndex: 'milestoneName',
      edit: (text, record) =>
        (isLocked(record)
          ? false
          : isPush(record, isEdit)
            && !!(
              record.contractNumber
              && record.contractNumber.includes('未签订合同')
            )),
      editRule: false,
      format(params, record): any {
        return record?.milestoneName;
      },
      editComponent: 'Input',
      customRender({ text, record }) {
        return record.milestoneId
          ? h(
            'div',
            {
              class: 'box-big',
              onClick: () => navDetails('1', record),
            },
            text
                || (record.contractNumber
                && record.contractNumber.includes('未签订合同')
                  ? '无'
                  : ''),
          )
          : h(
            'div',
            {
              class: 'box-big-normal',
            },
            text
                || (record.contractNumber
                && record.contractNumber.includes('未签订合同')
                  ? '无'
                  : ''),
          );
      },
    },
    {
      title: '合同编码（备注）',
      width: 130,
      dataIndex: 'remark',
      //  开启编辑
      edit: (text, record) => (isLocked(record) ? false : isEdit),
      // 表单组件
      editComponent: 'Input',
    },
    {
      title: '甲方合同号',
      width: 100,
      dataIndex: 'partyAContractNumber',
      //  开启编辑
      edit: (text, record) =>
        (isLocked(record)
          ? false
          : isPush(record, isEdit)
            && !!(
              record.contractNumber
              && record.contractNumber.includes('未签订合同')
            )),
      // 表单组件
      editComponent: 'Input',
    },
    {
      title: '甲方单位名称',
      width: 130,
      dataIndex: 'partyADeptIdName',
      format(params, record): any {
        return record?.partyADeptIdName;
      },
      edit: (text, record) =>
        (isLocked(record)
          ? false
          : isPush(record, isEdit)
            && !!(
              record.contractNumber
              && record.contractNumber.includes('未签订合同')
            )),
      editRule: false,
      editComponent(methods) {
        const selectRef = ref();
        return h(InputSearch, {
          class: 'w-full',
          ref: selectRef,
          value: methods.getComponentValue(),
          onSearch() {
            openPartyTableSelect(PartyContractList, {
              cb: (data) => {
                if (!data) {
                  return;
                }
                const name_ = data?.id === '0'
                  ? data?.temporary || data?.cusName
                  : data?.cusName;
                const rowValue = {
                  id: methods?.record.id
                    ? methods?.record.id
                    : `ADD${randomString(6)}`,
                  partyADeptId:
                    data?.id === '0'
                      ? data?.temporary || data?.cusName
                      : data?.id,
                  partyADeptIdName: name_,
                  internalExternal: data?.groupInOut,
                  internalExternalName: data?.groupInOutName,
                  industry: data?.industry,
                  industryName: data?.industryName,
                };

                methods.setRowValue(rowValue);
                methods.edit(name_);
              },
            });
          },
        });
      },
    },
    {
      title: '集团内/外',
      width: 100,
      dataIndex: 'internalExternal',
      edit: (text, record) =>
        (isLocked(record)
        || (record?.partyADeptIdName !== '临时客户' && record?.partyADeptIdName)
          ? false
          : !isPushData(record)
            && record.contractNumber
            && record.contractNumber.includes('未签订合同')
            && isEdit),
      format(params, record): any {
        return record.internalExternalName;
      },
      // 基础校验，可使用与antd一样的表单校验规则。支持数组、方法...
      editComponent(methods) {
        return h(
          'div',
          {
            class: 'select-row',
          },
          [
            h(SelectDictVal, {
              class: 'w-full',
              value: methods.getComponentValue(),
              dictNumber: 'customer_relationship',
              onChange(value) {
                methods.edit(value);
              },
            }),
          ],
        );
      },
    },
    {
      title: '所属行业',
      width: 100,
      dataIndex: 'industry',
      edit: (text, record) =>
        (isLocked(record)
        || (record?.partyADeptIdName !== '临时客户' && record?.partyADeptIdName)
        || !record.industryName
          ? false
          : !isPushData(record)
            && record.contractNumber
            && record.contractNumber.includes('未签订合同')
            && isEdit),
      format(params, record): any {
        return record.industryName;
      },
      // 基础校验，可使用与antd一样的表单校验规则。支持数组、方法...
      editComponent(methods) {
        return h(
          'div',
          {
            class: 'select-row',
          },
          [
            h(SelectDictVal, {
              class: 'w-full',
              value: methods.getComponentValue(),
              dictNumber: 'customer_industry',
              onChange(value) {
                methods.edit(value);
              },
            }),
          ],
        );
      },
    },
    {
      title: '收入计划编号',
      width: 100,
      dataIndex: 'number',
    },
    {
      dataIndex: 'lockStatus',
      title: '锁定状态',
      width: 80,
      customRender({ record }) {
        const colorBg = record.lockStatus === 'lock_down' ? 'green-s' : 'warn-s';
        const name = record.lockStatusName
          ? record.lockStatusName
          : record.lockStatus === 'lock_down'
            ? '已锁定'
            : '未锁定';
        return h(
          'div',
          {
            class: 'common-center',
          },
          [
            h(
              'div',
              {
                class: ['common-s', colorBg],
              },
              [
                h(
                  'span',
                  {
                    class: 'status-show',
                  },
                  name,
                ),
              ],
            ),
          ],
        );
      },
    },
    {
      dataIndex: 'dataStatus',
      title: '数据状态',
      width: 100,
      align: 'center',
      customRender({ record }) {
        const isStart = {
          color: '5',
          isInitialValue: true,
          name: '未开始',
          statusValue: 101,
        };
        return record.dataStatus
          ? h(DataStatusTag, {
            statusData: (record as { dataStatus: string })?.dataStatus,
          })
          : h(DataStatusTag, {
            statusData: isStart,
          });
      },
    },
    {
      title: '收入确认类型',
      width: 120,
      dataIndex: 'incomeConfirmType',
      edit: (text, record) =>
        (isLocked(record)
          ? false
          : record.number
            ? false
            : isPush(record, isEdit)),
      editComponent: 'SelectDict',
      editRule: true,
      format(params, record) {
        const incomeName = Array.isArray(record.incomeConfirmType)
          ? record.incomeConfirmType?.[0]?.name // 数组时取第一个元素的name
          : record?.incomeConfirmTypeName; // 非数组时直接取字段值
        return incomeName;
      },
      editComponentProps(params) {
        return {
          dictNumber: 'income_confirm_type',
          onChange: (val, record) => {
            params.setRowValue({
              incomeConfirmTypeName: record?.name,
            });
            updateRowValue(params, val);
            handleType(val, params?.record);
          },
        };
      },
    },
    {
      title: '预计暂估/开票日期',
      width: 160,
      dataIndex: 'estimateInvoiceDate',
      edit: (text, record) => (isLocked(record) ? false : isEdit),
      format(params, record): any {
        return record?.estimateInvoiceDate
          ? dayjs(record?.estimateInvoiceDate).format('YYYY-MM-DD')
          : '';
      },
      editComponent: 'DatePicker',
      editRule: true,
      editComponentProps: {
        placeholder: '预计暂估/开票日期',
        valueFormat: 'YYYY-MM-DD',
        style: {
          width: '100%',
        },
      },
    },
    {
      title: '税率（%）',
      width: 180,
      dataIndex: 'taxRate',
      edit: (text, record) => (isLocked(record) ? false : isEdit),
      format(params, record): any {
        return record.taxRateName;
      },
      // 基础校验，可使用与antd一样的表单校验规则。支持数组、方法...
      editComponent(methods) {
        const isInvalidation = isEstimateType(methods?.record) === 'invalidation_invoice';
        return h(
          'div',
          {
            class: isInvalidation ? 'select-row dis-btn' : 'select-row',
          },
          [
            h(SelectDictVal, {
              class: 'w-full',
              value: methods.getComponentValue(),
              dictNumber: 'tax_rate',
              mode: 'multiple',
              onChange(value) {
                updateRowDataTax(value, methods);
                methods.edit(value);
              },
            }),
            h(
              'div',
              {
                class: 'box-big space',
                onClick: () =>
                  (isEdit && !isInvalidation
                    ? openTaxModel(TaxRateEdit, {
                      record: methods.record,
                      cb: (data) => {
                        updateRowData(data, methods);
                      },
                    })
                    : null),
              },
              [
                h(Icon, {
                  icon: 'sie-icon-bianji',
                  size: 18,
                  color: isInvalidation ? '#ddd' : '#1890FF',
                }),
              ],
            ),
          ],
        );
      },
      editRule: [
        {
          type: 'array',
          required: true,
        },
      ],
    },
    {
      title: '本次暂估金额（价税合计）',
      width: 200,
      dataIndex: 'estimateAmt',
      isMoney: true,
      align: 'right',
      edit: (text, record) =>
        (isLocked(record) ? false : isEditableEstimateAmt(record, isEdit)),
      editComponent: 'InputMoney',
      editRule: (text, record) => [
        {
          type: 'number',
          required: isEditableEstimateAmt(record, isEdit),
        },
      ],
      format(text) {
        // 移除千分号
        const cleanedText = typeof text === 'string' ? text.replace(/,/g, '') : text;
        return cleanedText ? formatMoney(Number(cleanedText) ?? 0) : undefined;
      },
      editComponentProps(params) {
        return {
          onChange: (value) => {
            updateRowZgData(value, params);
          },
        };
      },
    },
    {
      title: '本次开票金额（价税合计）',
      width: 200,
      dataIndex: 'invAmt',
      isMoney: true,
      edit: (text, record) =>
        (isLocked(record) ? false : isInvAmt(record, isEdit)),
      editComponent: 'InputMoney',
      align: 'right',
      editRule: (text, record) => [
        {
          type: 'number',
          required: isInvAmt(record, isEdit),
        },
      ],
      format(text) {
        // 移除千分号
        const cleanedText = typeof text === 'string' ? text.replace(/,/g, '') : text;
        return cleanedText ? formatMoney(Number(cleanedText) ?? 0) : undefined;
      },
      editComponentProps(params) {
        return {
          onChange: (value) => {
            updateRowKpData(value, params);
          },
        };
      },
    },
    {
      title: '本次作废发票金额（价税合计）',
      width: 230,
      dataIndex: 'cancelInvAmt',
      align: 'right',
      isMoney: true,
      //  开启编辑
      edit: (text, record) =>
        (isLocked(record) ? false : isInvalidationInvoice(record, isEdit)),
      // 表单组件
      editComponent: 'InputNumber',
      editRule: (text, record) => [
        {
          type: 'number',
          required: isInvalidationInvoice(record, isEdit),
        },
      ],
      format(text) {
        return text ? formatMoney(text ?? 0) : undefined;
      },
      editComponentProps(params) {
        return {
          onChange: (value) => {
            const negativeValue = -Math.abs(value); // 确保 value 总是负数
            updateRowZfData(negativeValue, params);
          },
          onBlur: () => {
            const originalValue = params?.record?.cancelInvAmt;
            if (
              typeof originalValue === 'number'
              && !isNaN(originalValue)
              && originalValue !== 0
            ) {
              const negativeValue = -Math.abs(originalValue); // 确保 value 总是负数
              params.setRowValue({
                cancelInvAmt: negativeValue,
              });
            } else {
              params.setRowValue({
                cancelInvAmt: '', // 或者你可以设置为其他默认值
              });
            }
          },
        };
      },
    },
    {
      title: '里程碑已暂估金额（价税合计）',
      width: 240,
      dataIndex: 'milestonEstimateAmt',
      align: 'right',
      sorter: true,
      customRender({ text }) {
        // 添加数值有效性检查
        const numericValue = Number(text);
        return h(
          'div',
          {},
          !isNaN(numericValue) ? formatMoney(numericValue) : '',
        );
      },
    },
    {
      title: '本次冲销暂估金额（价税合计）',
      width: 220,
      dataIndex: 'writeOffAmt',
      align: 'right',
      isMoney: true,
      //  开启编辑
      edit: (text, record) =>
        (isLocked(record) ? false : isWriteOffAmt(record, isEdit)),
      format(text) {
        return text ? formatMoney(text ?? 0) : undefined;
      },
      // 表单组件
      editComponent: 'InputNumber',
      editComponentProps(params) {
        return {
          onChange: (value) => {
            const negativeValue = -Math.abs(value); // 确保 value 总是负数
            updateRowCwData(negativeValue, params);
          },
          onBlur: () => {
            const originalValue = params?.record?.writeOffAmt;
            if (
              typeof originalValue === 'number'
              && !isNaN(originalValue)
              && originalValue !== 0
            ) {
              const negativeValue = -Math.abs(originalValue); // 确保 value 总是负数
              params.setRowValue({
                writeOffAmt: negativeValue,
              });
            } else {
              params.setRowValue({
                writeOffAmt: '', // 或者你可以设置为其他默认值
              });
            }
          },
        };
      },
    },
    {
      title: '里程碑已预收款开票金额（价税合计）',
      width: 280,
      dataIndex: 'milestonePrePaidInvAmt',
      align: 'right',
      customRender({ text }) {
        // 添加数值有效性检查
        const numericValue = Number(text);
        return h(
          'div',
          {},
          !isNaN(numericValue) ? formatMoney(numericValue) : '',
        );
      },
    },
    {
      title: '预收款转收入金额（价税合计）',
      width: 220,
      dataIndex: 'advancePayIncomeAmt',
      isMoney: true,
      align: 'right',
      //  开启编辑
      edit: (text, record) =>
        (isLocked(record) ? false : isWriteOffAmt(record, isEdit)),
      format(text) {
        return text ? formatMoney(text ?? 0) : undefined;
      },
      // 表单组件
      editComponent: 'InputMoney',
      editComponentProps(params) {
        return {
          onChange: (value) => {
            updateRowYsData(value, params);
          },
        };
      },
    },
    {
      title: '本次收入计划金额（不含税）',
      width: 200,
      dataIndex: 'incomePlanAmt',
      align: 'right',
      customRender({ text }) {
        // 添加数值有效性检查
        const numericValue = Number(text);
        return h(
          'div',
          {},
          !isNaN(numericValue) ? formatMoney(numericValue) : '',
        );
      },
    },
    {
      title: '专业中心',
      width: 100,
      dataIndex: 'expertiseCenter',
      edit: (text, record) =>
        (isLocked(record) ? false : isPush(record, isEdit)),
      editComponent: 'ApiSelect',
      fixed: 'right',
      format(params, record): any {
        return record?.expertiseCenterName;
      },
      editComponentProps(params) {
        return {
          api: fn,
          onChange: (value, record) => {
            if (params.record.expertiseStation) {
              params.setRowValue({
                expertiseStationName: '',
                expertiseStation: '',
              });
            }
            // 更新“专业所”的选项
            const stationOptions = fnStation(value);
            params.setRowValue({
              expertiseCenterName: record?.label,
              expertiseStationOptions: stationOptions,
            });
          },
        };
      },
      editRule: true,
    },
    {
      title: '专业所',
      width: 100,
      dataIndex: 'expertiseStation',
      edit: (text, record) =>
        (isLocked(record)
          ? false
          : !!record.expertiseStationOptions
            || (!!record.expertiseCenterName && isPush(record, isEdit))),
      editComponent: 'ApiSelect',
      fixed: 'right',
      format(params, record): any {
        return record?.expertiseStationName;
      },
      editComponentProps(params) {
        return {
          api: () =>
            params.record.expertiseStationOptions
            || fnStation(params.record?.expertiseCenter),
          onChange: (value, record) => {
            params.setRowValue({
              expertiseStationName: record.label,
            });
          },
        };
      },
    },
    {
      title: '收入wbs',
      width: 100,
      dataIndex: 'projectNumber',
      edit: (text, record) =>
        (isLocked(record)
          ? false
          : record.taxRate?.length > 1
            ? false
            : !isPushData(record)
            && record.contractNumber
            && record.contractNumber.includes('未签订合同')
            && isEdit),
      format(params, record) {
        const isProjects = isMultipleProjects(record);
        return isProjects ? '多项目' : record.projectNumber;
      },
      editComponent(methods) {
        const selectRef = ref();
        return methods?.record.taxRate?.length > 1
          ? h('div', {}, methods?.record.projectNumber)
          : h(InputSearch, {
            class: 'w-full',
            ref: selectRef,
            value: methods.getComponentValue(),
            onSearch() {
              openProjectTableSelect(ProjectList, {
                cb: (data) => {
                  updateProjectCode(data, methods);
                },
              });
            },
          });
      },
    },
    {
      title: '项目名称',
      width: 100,
      dataIndex: 'projectName',
      format(params, record) {
        const isProjects = isMultipleProjects(record);
        return isProjects ? '多项目' : record.projectName;
      },
    },
    {
      title: '项目负责人',
      width: 100,
      dataIndex: 'projectRspUserId',
      edit: (text, record) =>
        (isLocked(record)
          ? false
          : record.taxRate?.length > 1
            ? false
            : !isPushData(record)
            && record.contractNumber
            && record.contractNumber.includes('未签订合同')
            && isEdit),
      format(params, record) {
        const isProjects = isMultipleProjects(record);
        return isProjects ? '多项目' : record.projectRspUserName;
      },
      editComponent: 'SelectUser',
      editComponentProps(params) {
        return {
          selectUserModalProps: {
            selectType: 'radio',
          },
          onChange(record) {
            const paramsData = [
              {
                id: record[0].id,
                name: record[0].name,
              },
            ];
            params.setRowValue({
              projectRspUserId: paramsData,
              projectRspUserName: record[0].name,
            });
            params.edit(paramsData);
          },
        };
      },
    },
    {
      title: '开票/收入确认公司',
      width: 180,
      dataIndex: 'billingCompany',
      edit: (text, record) =>
        (isLocked(record)
          ? false
          : !isPushData(record)
            && record.contractNumber
            && record.contractNumber.includes('未签订合同')
            && isEdit),
      format(params, record): any {
        return record.billingCompanyName;
      },
      // 基础校验，可使用与antd一样的表单校验规则。支持数组、方法...
      editComponent(methods) {
        return h(
          'div',
          {
            class: 'select-row',
          },
          [
            h(SelectDictVal, {
              class: 'w-full',
              value: methods.getComponentValue(),
              dictNumber: 'billing_recognition_company',
              onChange(value) {
                methods.edit(value);
              },
            }),
          ],
        );
      },
    },
    {
      title: '里程碑金额（价税合计）',
      width: 180,
      dataIndex: 'milestoneAmt',
      align: 'right',
      customRender({ text }) {
        // 添加数值有效性检查
        const numericValue = Number(text);
        return h(
          'div',
          {},
          !isNaN(numericValue) ? formatMoney(numericValue) : '',
        );
      },
    },
    {
      title: '里程碑已开票收入金额（价税合计）',
      width: 250,
      dataIndex: 'milestoneInvAmt',
      align: 'right',
      customRender({ text }) {
        // 添加数值有效性检查
        const numericValue = Number(text);
        return h(
          'div',
          {},
          !isNaN(numericValue) ? formatMoney(numericValue) : '',
        );
      },
    },
    {
      title: '里程碑未开票收入金额（价税合计）',
      width: 250,
      dataIndex: 'milestoneNoInvAmt',
      align: 'right',
      customRender({ text }) {
        // 添加数值有效性检查
        const numericValue = Number(text);
        return h(
          'div',
          {},
          !isNaN(numericValue) ? formatMoney(numericValue) : '',
        );
      },
    },
    {
      title: '本次暂估金额（不含税）',
      width: 180,
      dataIndex: 'estimateAmtExTax',
      align: 'right',
      customRender({ text }) {
        // 添加数值有效性检查
        const numericValue = Number(text);
        return h(
          'div',
          {},
          !isNaN(numericValue) ? formatMoney(numericValue) : '',
        );
      },
    },
    {
      title: '本次开票金额（不含税）',
      width: 180,
      dataIndex: 'invAmtExTax',
      align: 'right',
      customRender({ text }) {
        // 添加数值有效性检查
        const numericValue = Number(text);
        return h(
          'div',
          {},
          !isNaN(numericValue) ? formatMoney(numericValue) : '',
        );
      },
    },
    {
      title: '本次作废发票金额（不含税）',
      width: 200,
      dataIndex: 'cancelInvAmtExTax',
      align: 'right',
      isMoney: true,
      //  开启编辑
      edit: (text, record) =>
        (isLocked(record) ? false : record?.taxRate?.length > 1),
      // 表单组件
      editComponent: 'InputNumber',
      format(params, record): any {
        return record?.cancelInvAmtExTax || '';
      },
    },
    {
      title: '里程碑已暂估金额（不含税）',
      width: 200,
      dataIndex: 'milestoneAmtExTax',
      align: 'right',
      customRender({ text }) {
        // 添加数值有效性检查
        const numericValue = Number(text);
        return h(
          'div',
          {},
          !isNaN(numericValue) ? formatMoney(numericValue) : '',
        );
      },
    },
    {
      title: '本次冲销暂估金额（不含税）',
      width: 200,
      dataIndex: 'writeOffAmtExTax',
      isMoney: true,
      //  开启编辑
      edit: (text, record) =>
        (isLocked(record) ? false : isPushData(record) || record.writeOffAmt),
      // 表单组件
      editComponent: 'InputNumber',
      align: 'right',
      editRule: (text, record) => [
        {
          type: 'number',
          required: record.writeOffAmt,
        },
      ],
      format(params, record): any {
        return record?.writeOffAmtExTax || '';
      },
    },
    {
      title: '里程碑已预收款开票金额（不含税）',
      width: 280,
      dataIndex: 'milestoneInvAmtExTax',
      align: 'right',
      customRender({ text }) {
        // 添加数值有效性检查
        const numericValue = Number(text);
        return h(
          'div',
          {},
          !isNaN(numericValue) ? formatMoney(numericValue) : '',
        );
      },
    },
    {
      title: '预收款转收入金额（不含税）',
      width: 230,
      dataIndex: 'advPayIncomeAmtExTax',
      align: 'right',
      isMoney: true,
      //  开启编辑
      edit: (text, record) =>
        (isLocked(record)
          ? false
          : isPushData(record) || record.advancePayIncomeAmt),
      // 表单组件
      editComponent: 'InputNumber',
      editRule: (text, record) => [
        {
          type: 'number',
          required: record.advancePayIncomeAmt,
        },
      ],
    },
    {
      title: '本月是否申报收入计划',
      width: 180,
      dataIndex: 'isRevenue',
      edit: (text, record) => (isLocked(record) ? false : isEdit),
      editRule: false,
      format(params) {
        if (params === '1') {
          return '是';
        }
        if (params === '0') {
          return '否';
        }
        return '';
      },
      editComponent(methods) {
        const selectRef = ref();
        return h(Select, {
          class: 'w-full',
          ref: selectRef,
          value: methods.getComponentValue(),
          options: [
            {
              label: '是',
              value: '1',
            },
            {
              label: '否',
              value: '0',
            },
          ],
          // 选中
          onSelect(e) {
            // 数据保存
            methods.edit(e);
          },
        });
      },
    },
    {
      title: '不申报收入计划原因',
      width: 160,
      dataIndex: 'noRevenuePlanReason',
      //  开启编辑
      edit: (text, record) => (isLocked(record) ? false : isEdit),
      // 表单组件
      editComponent: 'Input',
      editRule: (text, record) => record.isRevenue && record.isRevenue === 0,
    },
    {
      title: '其他说明',
      width: 130,
      dataIndex: 'otherNotes',
      //  开启编辑
      edit: (text, record) => (isLocked(record) ? false : isEdit),
      // 表单组件
      editComponent: 'Input',
    },
    {
      title: '验收日期',
      width: 150,
      dataIndex: 'acceptanceDate',
      edit: (text, record) => (isLocked(record) ? false : isEdit),
      format(params, record): any {
        return record?.acceptanceDate
          ? dayjs(record?.acceptanceDate).format('YYYY-MM-DD')
          : '';
      },
      editComponent: 'DatePicker',
      editRule: false,
      editComponentProps: {
        placeholder: '请选择验收日期',
        valueFormat: 'YYYY-MM-DD',
        style: {
          width: '100%',
        },
      },
    },
    ...actions,
  ];
  return columns.filter((item) => item.show === undefined);
}

// 调整
export function getAdjustmentTableColumns(
  isEdit: boolean,
  navDetails: (type: string, record: Record<string, any>) => void,
  fn: () => void,
  fnStation: (id: string) => void,
  handleType: (val: string, record: Record<string, any>) => void,
) {
  const actions = isEdit
    ? [
      {
        title: '操作',
        dataIndex: 'actions',
        width: 60,
        fixed: 'right',
        slots: { customRender: 'actions' },
      },
    ]
    : [];

  const columns: any[] = [
    {
      dataIndex: 'contractNumber',
      title: '合同编码',
      edit: (text, record) =>
        (isLocked(record) ? false : isPush(record, isEdit)),
      fixed: 'left',
      editComponent(methods) {
        const selectRef = ref();
        return h(InputSearch, {
          class: 'w-full',
          ref: selectRef,
          value: methods.getComponentValue(),
          onSearch() {
            openContractTableSelect(ContractList, {
              cb: (data) => {
                if (!data) {
                  return;
                }
                const dataTaxRate = data?.taxRate
                  ? (data?.taxRate ?? '').split('、')
                  : [];
                const dataProjectRspUserId = createOption(
                  data?.projectRspUserId,
                  data?.projectRspUserName,
                );

                const rowValue = {
                  ...data,
                  id: methods?.record.id
                    ? methods?.record.id
                    : `ADD${randomString(6)}`,
                  taxRate: dataTaxRate,
                  projectRspUserId: dataProjectRspUserId,
                };
                const contract_number = data.contractNumber || data.contractName;
                methods.setRowValue(rowValue);
                methods.edit(contract_number ?? data.name);
              },
            });
          },
        });
      },
      editRule: true,
    },
    {
      title: '合同名称',
      width: 160,
      fixed: 'left',
      dataIndex: 'contractName',
      edit: (text, record) =>
        (isLocked(record)
          ? false
          : isPush(record, isEdit)
            && !!(
              record.contractNumber
              && record.contractNumber.includes('未签订合同')
            )),
      editRule: (text, record) => [
        {
          type: 'string',
          required: true,
        },
      ],
      format(params, record): any {
        return record?.contractName;
      },
      editComponent: 'Input',
      customRender({ text, record }) {
        return record.contractId
          ? h(
            'div',
            {
              class: 'box-big',
              onClick: () => {
                navDetails('2', record);
              },
            },
            record.contractName,
          )
          : h(
            'div',
            {
              class: 'box-big-normal',
            },
            text
                || (record.contractNumber
                && record.contractNumber.includes('未签订合同')
                  ? '未签订合同'
                  : ''),
          );
      },
    },
    {
      title: '里程碑名称',
      width: 150,
      fixed: 'left',
      dataIndex: 'milestoneName',
      edit: (text, record) =>
        (isLocked(record)
          ? false
          : isPush(record, isEdit)
            && !!(
              record.contractNumber
              && record.contractNumber.includes('未签订合同')
            )),
      editRule: false,
      format(params, record): any {
        return record?.milestoneName;
      },
      editComponent: 'Input',
      customRender({ text, record }) {
        return record.milestoneId
          ? h(
            'div',
            {
              class: 'box-big',
              onClick: () => navDetails('1', record),
            },
            text
                || (record.contractNumber
                && record.contractNumber.includes('未签订合同')
                  ? '无'
                  : ''),
          )
          : h(
            'div',
            {
              class: 'box-big-normal',
            },
            text
                || (record.contractNumber
                && record.contractNumber.includes('未签订合同')
                  ? '无'
                  : ''),
          );
      },
    },
    {
      title: '合同编码（备注）',
      width: 160,
      dataIndex: 'remark',
      //  开启编辑
      edit: (text, record) => (isLocked(record) ? false : isEdit),
      // 表单组件
      editComponent: 'Input',
    },
    {
      title: '甲方合同号',
      width: 160,
      dataIndex: 'partyAContractNumber',
      //  开启编辑
      edit: (text, record) =>
        (isLocked(record)
          ? false
          : isPush(record, isEdit)
            && !!(
              record.contractNumber
              && record.contractNumber.includes('未签订合同')
            )),
      // 表单组件
      editComponent: 'Input',
    },
    {
      title: '甲方单位名称',
      width: 130,
      dataIndex: 'partyADeptIdName',
      format(params, record): any {
        return record?.partyADeptIdName;
      },
      edit: (text, record) =>
        (isLocked(record)
          ? false
          : isPush(record, isEdit)
            && !!(
              record.contractNumber
              && record.contractNumber.includes('未签订合同')
            )),
      editRule: false,
      editComponent(methods) {
        const selectRef = ref();
        return h(InputSearch, {
          class: 'w-full',
          ref: selectRef,
          value: methods.getComponentValue(),
          onSearch() {
            openPartyTableSelect(PartyContractList, {
              cb: (data) => {
                if (!data) {
                  return;
                }
                const name_ = data?.id === '0'
                  ? data?.temporary || data?.cusName
                  : data?.cusName;
                const rowValue = {
                  id: methods?.record.id
                    ? methods?.record.id
                    : `ADD${randomString(6)}`,
                  partyADeptId:
                    data?.id === '0'
                      ? data?.temporary || data?.cusName
                      : data?.id,
                  partyADeptIdName: name_,
                  internalExternal: data?.groupInOut,
                  internalExternalName: data?.groupInOutName,
                  industry: data?.industry,
                  industryName: data?.industryName,
                };

                methods.setRowValue(rowValue);
                methods.edit(name_);
              },
            });
          },
        });
      },
    },
    {
      title: '集团内/外',
      width: 100,
      dataIndex: 'internalExternal',
      edit: (text, record) =>
        (isLocked(record)
        || (record?.partyADeptIdName !== '临时客户' && record?.partyADeptIdName)
          ? false
          : !isPushData(record)
            && record.contractNumber
            && record.contractNumber.includes('未签订合同')
            && isEdit),
      format(params, record): any {
        return record.internalExternalName;
      },
      // 基础校验，可使用与antd一样的表单校验规则。支持数组、方法...
      editComponent(methods) {
        return h(
          'div',
          {
            class: 'select-row',
          },
          [
            h(SelectDictVal, {
              class: 'w-full',
              value: methods.getComponentValue(),
              dictNumber: 'customer_relationship',
              onChange(value) {
                methods.edit(value);
              },
            }),
          ],
        );
      },
    },
    {
      title: '所属行业',
      width: 100,
      dataIndex: 'industry',
      edit: (text, record) =>
        (isLocked(record)
        || (record?.partyADeptIdName !== '临时客户' && record?.partyADeptIdName)
        || !record.industryName
          ? false
          : !isPushData(record)
            && record.contractNumber
            && record.contractNumber.includes('未签订合同')
            && isEdit),
      format(params, record): any {
        return record.industryName;
      },
      // 基础校验，可使用与antd一样的表单校验规则。支持数组、方法...
      editComponent(methods) {
        return h(
          'div',
          {
            class: 'select-row',
          },
          [
            h(SelectDictVal, {
              class: 'w-full',
              value: methods.getComponentValue(),
              dictNumber: 'customer_industry',
              onChange(value) {
                methods.edit(value);
              },
            }),
          ],
        );
      },
    },
    {
      title: '收入计划编号',
      width: 100,
      dataIndex: 'number',
    },
    {
      dataIndex: 'lockStatus',
      title: '锁定状态',
      width: 100,
      customRender({ record }) {
        const colorBg = record.lockStatus === 'lock_down' ? 'green-s' : 'warn-s';
        const name = record.lockStatusName
          ? record.lockStatusName
          : record.lockStatus === 'lock_down'
            ? '已锁定'
            : '未锁定';
        return h(
          'div',
          {
            class: 'common-center',
          },
          [
            h(
              'div',
              {
                class: ['common-s', colorBg],
              },
              [
                h(
                  'span',
                  {
                    class: 'status-show',
                  },
                  name,
                ),
              ],
            ),
          ],
        );
      },
    },
    {
      dataIndex: 'dataStatus',
      title: '数据状态',
      width: 130,
      align: 'center',
      customRender({ record }) {
        const isStart = {
          color: '5',
          isInitialValue: true,
          name: '未开始',
          statusValue: 101,
        };
        return record.dataStatus
          ? h(DataStatusTag, {
            statusData: (record as { dataStatus: string })?.dataStatus,
          })
          : h(DataStatusTag, {
            statusData: isStart,
          });
      },
    },
    {
      title: '收入确认类型',
      width: 150,
      dataIndex: 'incomeConfirmType',
      edit: (text, record) =>
        (isLocked(record)
          ? false
          : record.number
            ? false
            : isPush(record, isEdit)),
      editComponent: 'SelectDict',
      editRule: true,
      format(params, record) {
        const incomeName = Array.isArray(record.incomeConfirmType)
          ? record.incomeConfirmType?.[0]?.name // 数组时取第一个元素的name
          : record?.incomeConfirmTypeName; // 非数组时直接取字段值
        return incomeName;
      },
      editComponentProps(params) {
        return {
          dictNumber: 'income_confirm_type',
          onChange: (val, record) => {
            updateRowValue(params, val);
            handleType(val, params?.record);
            params.setRowValue({
              incomeConfirmTypeName: record?.name,
            });
            params.edit(val);
          },
        };
      },
    },
    {
      title: '预计暂估/开票日期',
      width: 210,
      dataIndex: 'estimateInvoiceDate',
      edit: (text, record) => (isLocked(record) ? false : isEdit),
      format(params, record): any {
        return record?.estimateInvoiceDate
          ? dayjs(record?.estimateInvoiceDate).format('YYYY-MM-DD')
          : '';
      },
      editComponent: 'DatePicker',
      editRule: true,
      editComponentProps: {
        placeholder: '预计暂估/开票日期',
        valueFormat: 'YYYY-MM-DD',
        style: {
          width: '100%',
        },
      },
    },
    {
      title: '税率（%）',
      width: 180,
      dataIndex: 'taxRate',
      edit: (text, record) => (isLocked(record) ? false : isEdit),
      format(params, record): any {
        return record.taxRateName;
      },
      // 基础校验，可使用与antd一样的表单校验规则。支持数组、方法...
      editComponent(methods) {
        const isInvalidation = isEstimateType(methods?.record) === 'invalidation_invoice';
        return h(
          'div',
          {
            class: isInvalidation ? 'select-row dis-btn' : 'select-row',
          },
          [
            h(SelectDictVal, {
              class: 'w-full',
              value: methods.getComponentValue(),
              dictNumber: 'tax_rate',
              mode: 'multiple',
              onChange(value) {
                updateRowDataTax(value, methods);
                methods.edit(value);
              },
            }),
            h(
              'div',
              {
                class: 'box-big space',
                onClick: () =>
                  (isEdit && !isInvalidation
                    ? openTaxModel(TaxRateEdit, {
                      record: methods.record,
                      cb: (data) => {
                        updateRowData(data, methods);
                      },
                    })
                    : null),
              },
              [
                h(Icon, {
                  icon: 'sie-icon-bianji',
                  size: 18,
                  color: isInvalidation ? '#ddd' : '#1890FF',
                }),
              ],
            ),
          ],
        );
      },
      editRule: [
        {
          type: 'array',
          required: true,
        },
      ],
    },
    {
      title: '本次暂估金额（价税合计）',
      width: 200,
      dataIndex: 'estimateAmt',
      isMoney: true,
      align: 'right',
      edit: (text, record) =>
        (isLocked(record) ? false : isEditableEstimateAmt(record, isEdit)),
      editComponent: 'InputMoney',
      editRule: (text, record) => [
        {
          type: 'number',
          required: isEditableEstimateAmt(record, isEdit),
        },
      ],
      format(text) {
        // 移除千分号
        const cleanedText = typeof text === 'string' ? text.replace(/,/g, '') : text;
        return cleanedText ? formatMoney(Number(cleanedText) ?? 0) : undefined;
      },
      editComponentProps(params) {
        return {
          onChange: (value) => {
            updateRowZgData(value, params);
          },
        };
      },
    },
    {
      title: '本次开票金额（价税合计）',
      width: 200,
      dataIndex: 'invAmt',
      isMoney: true,
      edit: (text, record) =>
        (isLocked(record) ? false : isInvAmt(record, isEdit)),
      editComponent: 'InputMoney',
      align: 'right',
      editRule: (text, record) => [
        {
          type: 'number',
          required: isInvAmt(record, isEdit),
        },
      ],
      format(text) {
        // 移除千分号
        const cleanedText = typeof text === 'string' ? text.replace(/,/g, '') : text;
        return cleanedText ? formatMoney(Number(cleanedText) ?? 0) : undefined;
      },
      editComponentProps(params) {
        return {
          onChange: (value) => {
            updateRowKpData(value, params);
          },
        };
      },
    },
    {
      title: '本次作废发票金额（价税合计）',
      width: 230,
      dataIndex: 'cancelInvAmt',
      align: 'right',
      isMoney: true,
      //  开启编辑
      edit: (text, record) =>
        (isLocked(record) ? false : isInvalidationInvoice(record, isEdit)),
      // 表单组件
      editComponent: 'InputNumber',
      editRule: (text, record) => [
        {
          type: 'number',
          required: isInvalidationInvoice(record, isEdit),
        },
      ],
      format(text) {
        return text ? formatMoney(text ?? 0) : undefined;
      },
      editComponentProps(params) {
        return {
          onChange: (value) => {
            const negativeValue = -Math.abs(value); // 确保 value 总是负数
            updateRowZfData(negativeValue, params);
          },
          onBlur: () => {
            const originalValue = params?.record?.cancelInvAmt;
            if (
              typeof originalValue === 'number'
              && !isNaN(originalValue)
              && originalValue !== 0
            ) {
              const negativeValue = -Math.abs(originalValue); // 确保 value 总是负数
              params.setRowValue({
                cancelInvAmt: negativeValue,
              });
            } else {
              params.setRowValue({
                cancelInvAmt: '', // 或者你可以设置为其他默认值
              });
            }
          },
        };
      },
    },
    {
      title: '里程碑已暂估金额（价税合计）',
      width: 220,
      dataIndex: 'milestonEstimateAmt',
      align: 'right',
      customRender({ text }) {
        // 添加数值有效性检查
        const numericValue = Number(text);
        return h(
          'div',
          {},
          !isNaN(numericValue) ? formatMoney(numericValue) : '',
        );
      },
    },
    {
      title: '本次冲销暂估金额（价税合计）',
      width: 220,
      dataIndex: 'writeOffAmt',
      align: 'right',
      isMoney: true,
      //  开启编辑
      edit: (text, record) =>
        (isLocked(record) ? false : isWriteOffAmt(record, isEdit)),
      format(text) {
        return text ? formatMoney(text ?? 0) : undefined;
      },
      // 表单组件
      editComponent: 'InputNumber',
      editComponentProps(params) {
        return {
          onChange: (value) => {
            const negativeValue = -Math.abs(value); // 确保 value 总是负数
            updateRowCwData(negativeValue, params);
          },
          onBlur: () => {
            const originalValue = params?.record?.writeOffAmt;
            if (
              typeof originalValue === 'number'
              && !isNaN(originalValue)
              && originalValue !== 0
            ) {
              const negativeValue = -Math.abs(originalValue); // 确保 value 总是负数
              params.setRowValue({
                writeOffAmt: negativeValue,
              });
            } else {
              params.setRowValue({
                writeOffAmt: '', // 或者你可以设置为其他默认值
              });
            }
          },
        };
      },
    },
    {
      title: '里程碑已预收款开票金额（价税合计）',
      width: 280,
      dataIndex: 'milestonePrePaidInvAmt',
      align: 'right',
      customRender({ text }) {
        // 添加数值有效性检查
        const numericValue = Number(text);
        return h(
          'div',
          {},
          !isNaN(numericValue) ? formatMoney(numericValue) : '',
        );
      },
    },
    {
      title: '预收款转收入金额（价税合计）',
      width: 220,
      dataIndex: 'advancePayIncomeAmt',
      isMoney: true,
      align: 'right',
      //  开启编辑
      edit: (text, record) =>
        (isLocked(record) ? false : isWriteOffAmt(record, isEdit)),
      format(text) {
        return text ? formatMoney(text ?? 0) : undefined;
      },
      // 表单组件
      editComponent: 'InputMoney',
      editComponentProps(params) {
        return {
          onChange: (value) => {
            updateRowYsData(value, params);
          },
        };
      },
    },
    {
      title: '本次收入计划金额（不含税）',
      width: 200,
      dataIndex: 'incomePlanAmt',
      align: 'right',
      customRender({ text }) {
        // 添加数值有效性检查
        const numericValue = Number(text);
        return h(
          'div',
          {},
          !isNaN(numericValue) ? formatMoney(numericValue) : '',
        );
      },
      stomRender({ text }) {
        return h('div', {}, text ? formatMoney(text ?? 0) : '');
      },
    },
    {
      title: '专业中心',
      width: 100,
      dataIndex: 'expertiseCenter',
      edit: (text, record) =>
        (isLocked(record) ? false : isPush(record, isEdit)),
      editComponent: 'ApiSelect',
      fixed: 'right',
      format(params, record): any {
        return record?.expertiseCenterName;
      },
      editComponentProps(params) {
        return {
          api: fn,
          onChange: (value, record) => {
            if (params.record.expertiseStation) {
              params.setRowValue({
                expertiseStationName: '',
                expertiseStation: '',
              });
            }
            // 更新“专业所”的选项
            const stationOptions = fnStation(value);
            params.setRowValue({
              expertiseCenterName: record?.label,
              expertiseStationOptions: stationOptions,
            });
          },
        };
      },
      editRule: true,
    },
    {
      title: '专业所',
      width: 100,
      dataIndex: 'expertiseStation',
      fixed: 'right',
      edit: (text, record) =>
        (isLocked(record)
          ? false
          : !!record.expertiseStationOptions
            || (!!record.expertiseCenterName && isPush(record, isEdit))),
      editComponent: 'ApiSelect',
      format(params, record): any {
        return record?.expertiseStationName;
      },
      editComponentProps(params) {
        return {
          api: () =>
            params.record.expertiseStationOptions
            || fnStation(params.record?.expertiseCenter),
          onChange: (value, record) => {
            params.setRowValue({
              expertiseStationName: record.label,
            });
          },
        };
      },
    },
    {
      title: '收入wbs',
      width: 130,
      dataIndex: 'projectNumber',
      edit: (text, record) =>
        (isLocked(record)
          ? false
          : record.taxRate?.length > 1
            ? false
            : !isPushData(record)
            && record.contractNumber
            && record.contractNumber.includes('未签订合同')
            && isEdit),
      format(params, record) {
        const isProjects = isMultipleProjects(record);
        return isProjects ? '多项目' : record.projectNumber;
      },
      editComponent(methods) {
        const selectRef = ref();
        return methods?.record.taxRate?.length > 1
          ? h('div', {}, methods?.record.projectNumber)
          : h(InputSearch, {
            class: 'w-full',
            ref: selectRef,
            value: methods.getComponentValue(),
            onSearch() {
              openProjectTableSelect(ProjectList, {
                cb: (data) => {
                  updateProjectCode(data, methods);
                },
              });
            },
          });
      },
    },
    {
      title: '项目名称',
      width: 150,
      dataIndex: 'projectName',
      format(params, record) {
        const isProjects = isMultipleProjects(record);
        return isProjects ? '多项目' : record.projectName;
      },
    },
    {
      title: '项目负责人',
      width: 150,
      dataIndex: 'projectRspUserId',
      edit: (text, record) =>
        (isLocked(record)
          ? false
          : record.taxRate?.length > 1
            ? false
            : !isPushData(record)
            && record.contractNumber
            && record.contractNumber.includes('未签订合同')
            && isEdit),
      format(params, record) {
        const isProjects = isMultipleProjects(record);
        return isProjects ? '多项目' : record.projectRspUserName;
      },
      editComponent: 'SelectUser',
      editComponentProps(params) {
        return {
          selectUserModalProps: {
            selectType: 'radio',
          },
          onChange(record) {
            const paramsData = [
              {
                id: record[0].id,
                name: record[0].name,
              },
            ];
            params.setRowValue({
              projectRspUserId: paramsData,
              projectRspUserName: record[0].name,
            });
            params.edit(paramsData);
          },
        };
      },
    },
    {
      title: '开票/收入确认公司',
      width: 180,
      dataIndex: 'billingCompany',
      edit: (text, record) =>
        (isLocked(record)
          ? false
          : !isPushData(record)
            && record.contractNumber
            && record.contractNumber.includes('未签订合同')
            && isEdit),
      format(params, record): any {
        return record.billingCompanyName;
      },
      // 基础校验，可使用与antd一样的表单校验规则。支持数组、方法...
      editComponent(methods) {
        return h(
          'div',
          {
            class: 'select-row',
          },
          [
            h(SelectDictVal, {
              class: 'w-full',
              value: methods.getComponentValue(),
              dictNumber: 'billing_recognition_company',
              onChange(value, record) {
                methods.setRowValue({
                  billingCompanyName: record.name,
                });
                methods.edit(value);
              },
            }),
          ],
        );
      },
    },
    {
      title: '里程碑金额（价税合计）',
      width: 180,
      dataIndex: 'milestoneAmt',
      align: 'right',
      customRender({ text }) {
        // 添加数值有效性检查
        const numericValue = Number(text);
        return h(
          'div',
          {},
          !isNaN(numericValue) ? formatMoney(numericValue) : '',
        );
      },
    },
    {
      title: '里程碑已开票收入金额（价税合计）',
      width: 250,
      dataIndex: 'milestoneInvAmt',
      align: 'right',
      customRender({ text }) {
        // 添加数值有效性检查
        const numericValue = Number(text);
        return h(
          'div',
          {},
          !isNaN(numericValue) ? formatMoney(numericValue) : '',
        );
      },
    },
    {
      title: '里程碑未开票收入金额（价税合计）',
      width: 250,
      dataIndex: 'milestoneNoInvAmt',
      align: 'right',
      customRender({ text }) {
        // 添加数值有效性检查
        const numericValue = Number(text);
        return h(
          'div',
          {},
          !isNaN(numericValue) ? formatMoney(numericValue) : '',
        );
      },
    },
    {
      title: '本次暂估金额（不含税）',
      width: 180,
      dataIndex: 'estimateAmtExTax',
      align: 'right',
      customRender({ text }) {
        // 添加数值有效性检查
        const numericValue = Number(text);
        return h(
          'div',
          {},
          !isNaN(numericValue) ? formatMoney(numericValue) : '',
        );
      },
    },
    {
      title: '本次开票金额（不含税）',
      width: 180,
      dataIndex: 'invAmtExTax',
      align: 'right',
      customRender({ text }) {
        // 添加数值有效性检查
        const numericValue = Number(text);
        return h(
          'div',
          {},
          !isNaN(numericValue) ? formatMoney(numericValue) : '',
        );
      },
    },
    {
      title: '本次作废发票金额（不含税）',
      width: 200,
      dataIndex: 'cancelInvAmtExTax',
      align: 'right',
      isMoney: true,
      //  开启编辑
      edit: (text, record) => record?.taxRate?.length > 1,
      // 表单组件
      editComponent: 'InputNumber',
      format(params, record): any {
        return record?.cancelInvAmtExTax || '';
      },
    },
    {
      title: '里程碑已暂估金额（不含税）',
      width: 200,
      dataIndex: 'milestoneAmtExTax',
      align: 'right',
      customRender({ text }) {
        // 添加数值有效性检查
        const numericValue = Number(text);
        return h(
          'div',
          {},
          !isNaN(numericValue) ? formatMoney(numericValue) : '',
        );
      },
    },
    {
      title: '本次冲销暂估金额（不含税）',
      width: 200,
      dataIndex: 'writeOffAmtExTax',
      isMoney: true,
      //  开启编辑
      edit: (text, record) =>
        (isLocked(record) ? false : isPushData(record) || record.writeOffAmt),
      // 表单组件
      editComponent: 'InputNumber',
      align: 'right',
      editRule: (text, record) => [
        {
          type: 'number',
          required: record.writeOffAmt,
        },
      ],
      format(params, record): any {
        return record?.writeOffAmtExTax || '';
      },
    },
    {
      title: '里程碑已预收款开票金额（不含税）',
      width: 280,
      dataIndex: 'milestoneInvAmtExTax',
      align: 'right',
      customRender({ text }) {
        // 添加数值有效性检查
        const numericValue = Number(text);
        return h(
          'div',
          {},
          !isNaN(numericValue) ? formatMoney(numericValue) : '',
        );
      },
    },
    {
      title: '预收款转收入金额（不含税）',
      width: 230,
      dataIndex: 'advPayIncomeAmtExTax',
      align: 'right',
      isMoney: true,
      //  开启编辑
      edit: (text, record) =>
        (isLocked(record)
          ? false
          : isPushData(record) || record.advancePayIncomeAmt),
      // 表单组件
      editComponent: 'InputNumber',
      editRule: (text, record) => [
        {
          type: 'number',
          required: record.advancePayIncomeAmt,
        },
      ],
    },
    {
      title: '修改收入计划原因',
      width: 180,
      dataIndex: 'changeReason',
      edit: (text, record) => (isLocked(record) ? false : isEdit),
      editRule: false,
      format(params, record): any {
        return record.changeReasonName;
      },
      editComponent: 'SelectDict',
      editComponentProps(params) {
        return {
          dictNumber: 'revise_income_plan_reason',
          onChange(value, record) {
            params.setRowValue({
              changeReasonName: record.name,
            });
            params.edit(value);
          },
        };
      },
    },
    {
      title: '风险状态',
      width: 180,
      dataIndex: 'riskType',
      edit: (text, record) => (isLocked(record) ? false : isEdit),
      editRule: false,
      format(params, record): any {
        return record.riskTypeName;
      },
      editComponent: 'SelectDict',
      editComponentProps(params) {
        return {
          dictNumber: 'risk_type',
          onChange(value, record) {
            params.setRowValue({
              riskTypeName: record.name,
            });
            params.edit(value);
          },
        };
      },
    },
    {
      title: '风险环节',
      width: 160,
      dataIndex: 'riskLink',
      edit: (text, record) => (isLocked(record) ? false : isEdit),
      format(params, record): any {
        return record.riskLinkName;
      },
      editComponent: 'SelectDict',
      editComponentProps(params) {
        return {
          dictNumber: 'risk_link',
          onChange(value, record) {
            params.setRowValue({
              riskLinkName: record.name,
            });
            params.edit(value);
          },
        };
      },
    },
    {
      title: '其他说明',
      width: 130,
      dataIndex: 'otherNotes',
      edit: (text, record) => (isLocked(record) ? false : isEdit),
      editComponent: 'Input',
    },
    {
      title: '验收日期',
      width: 150,
      dataIndex: 'acceptanceDate',
      edit: (text, record) => (isLocked(record) ? false : isEdit),
      format(params, record): any {
        return record?.acceptanceDate
          ? dayjs(record?.acceptanceDate).format('YYYY-MM-DD')
          : '';
      },
      editComponent: 'DatePicker',
      editRule: false,
      editComponentProps: {
        placeholder: '请选择验收日期',
        valueFormat: 'YYYY-MM-DD',
        style: {
          width: '100%',
        },
      },
    },
    ...actions,
  ];
  return columns.filter((item) => item.show === undefined);
}

// 差异
export function getDiffTableColumns(
  navDetails: (type: string, record: Record<string, any>) => void,
) {
  const columns: any[] = [
    {
      dataIndex: 'contractName',
      title: '合同名称',
      fixed: 'left',
      customRender({ text, record }) {
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text,
          )
          : text;
      },
    },
    {
      title: '合同编码',
      width: 160,
      fixed: 'left',
      dataIndex: 'contractNumber',
      customRender({ text, record }) {
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text,
          )
          : text;
      },
    },
    {
      title: '里程碑名称',
      width: 150,
      dataIndex: 'milestoneName',
      fixed: 'left',
      customRender({ text, record }) {
        return h(
          'div',
          {
            class: 'box-big',
            onClick: () => navDetails('1', record),
          },
          text,
        );
      },
    },
    {
      title: '合同编码（备注）',
      width: 160,
      dataIndex: 'remark',
      customRender({ text, record }) {
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text,
          )
          : text;
      },
    },
    {
      title: '甲方合同号',
      width: 160,
      dataIndex: 'partyAContractNumber',
      customRender({ text, record }) {
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text,
          )
          : text;
      },
    },
    {
      title: '甲方单位名称',
      width: 100,
      dataIndex: 'partyADeptIdName',
      customRender({ text, record }) {
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text,
          )
          : text;
      },
    },
    {
      title: '收入计划编号',
      width: 100,
      dataIndex: 'number',
      customRender({ text, record }) {
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text,
          )
          : text;
      },
    },
    {
      dataIndex: 'lockStatus',
      title: '锁定状态',
      width: 100,
      customRender({ record }) {
        const colorBg = record.lockStatus === 'lock_down' ? 'green-s' : 'warn-s';
        const name = record.lockStatusName
          ? record.lockStatusName
          : record.lockStatus === 'lock_down'
            ? '已锁定'
            : '未锁定';
        return h(
          'div',
          {
            class: 'common-center',
          },
          [
            h(
              'div',
              {
                class: ['common-s', colorBg],
              },
              [
                h(
                  'span',
                  {
                    class: 'status-show',
                  },
                  name,
                ),
              ],
            ),
          ],
        );
      },
    },
    {
      dataIndex: 'dataStatus',
      title: '数据状态',
      width: 130,
      align: 'center',
      customRender({ record }) {
        return record.dataStatus
          ? h(DataStatusTag, {
            statusData: (record as { dataStatus: string })?.dataStatus,
          })
          : '';
      },
    },
    {
      title: '收入确认类型',
      width: 150,
      dataIndex: 'incomeConfirmTypeName',
      customRender({ text, record }) {
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text,
          )
          : text;
      },
    },
    {
      title: '预计暂估/开票日期',
      width: 150,
      dataIndex: 'estimateInvoiceDate',
      customRender({ text, record }) {
        const date = dayjs(text).format('YYYY-MM-DD');
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            date,
          )
          : date;
      },
    },
    {
      title: '税率（%）',
      width: 100,
      dataIndex: 'taxRate',
      customRender({ text, record }) {
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text,
          )
          : text;
      },
    },
    {
      title: '本次暂估金额（价税合计）',
      width: 200,
      dataIndex: 'estimateAmt',
      isMoney: true,
      customRender({ text, record }) {
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text ? formatMoney(text ?? 0) : '',
          )
          : text
            ? formatMoney(text ?? 0)
            : '';
      },
    },
    {
      title: '本次开票金额（价税合计）',
      width: 200,
      dataIndex: 'invAmt',
      isMoney: true,
      customRender({ text, record }) {
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text ? formatMoney(text ?? 0) : '',
          )
          : text
            ? formatMoney(text ?? 0)
            : '';
      },
    },
    {
      title: '本次作废发票金额（价税合计）',
      width: 220,
      dataIndex: 'cancelInvAmt',
      isMoney: true,
      customRender({ text, record }) {
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text ? formatMoney(text ?? 0) : '',
          )
          : text
            ? formatMoney(text ?? 0)
            : '';
      },
    },
    {
      title: '里程碑已暂估金额（价税合计）',
      width: 220,
      dataIndex: 'milestonEstimateAmt',
      customRender({ text, record }) {
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text ? formatMoney(text ?? 0) : '',
          )
          : text
            ? formatMoney(text ?? 0)
            : '';
      },
    },
    {
      title: '本次冲销暂估金额（价税合计）',
      width: 220,
      dataIndex: 'writeOffAmt',
      customRender({ text, record }) {
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text ? formatMoney(text ?? 0) : '',
          )
          : text
            ? formatMoney(text ?? 0)
            : '';
      },
    },
    {
      title: '里程碑已预收款开票金额（价税合计）',
      width: 280,
      dataIndex: 'milestonePrePaidInvAmt',
      isMoney: true,
      customRender({ text, record }) {
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text ? formatMoney(text ?? 0) : '',
          )
          : text
            ? formatMoney(text ?? 0)
            : '';
      },
    },
    {
      title: '预收款转收入金额（价税合计）',
      width: 220,
      dataIndex: 'advancePayIncomeAmt',
      isMoney: true,
      customRender({ text, record }) {
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text ? formatMoney(text ?? 0) : '',
          )
          : text
            ? formatMoney(text ?? 0)
            : '';
      },
    },
    {
      title: '本次收入计划金额（不含税）',
      width: 200,
      dataIndex: 'incomePlanAmt',
      customRender({ text, record }) {
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text ? formatMoney(text ?? 0) : '',
          )
          : text
            ? formatMoney(text ?? 0)
            : '';
      },
    },
    {
      title: '专业中心',
      width: 100,
      dataIndex: 'expertiseCenterName',
      customRender({ text, record }) {
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text,
          )
          : text;
      },
    },
    {
      title: '专业所',
      width: 100,
      dataIndex: 'expertiseStationName',
      customRender({ text, record }) {
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text,
          )
          : text;
      },
    },
    {
      title: '收入wbs',
      width: 130,
      dataIndex: 'projectNumber',
      customRender({ text, record }) {
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text,
          )
          : text;
      },
    },
    {
      title: '项目名称',
      width: 150,
      dataIndex: 'projectName',
      customRender({ text, record }) {
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text,
          )
          : text;
      },
    },
    {
      title: '项目负责人',
      width: 100,
      dataIndex: 'projectRspUserName',
      customRender({ text, record }) {
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text,
          )
          : text;
      },
    },
    {
      title: '开票/收入确认公司',
      width: 180,
      dataIndex: 'billingCompany',
      customRender({ text, record }) {
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text,
          )
          : text;
      },
    },
    {
      title: '集团内/外',
      width: 180,
      dataIndex: 'internalExternalName',
      customRender({ text, record }) {
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text,
          )
          : text;
      },
    },
    {
      title: '里程碑金额（价税合计）',
      width: 180,
      dataIndex: 'milestoneAmt',
      customRender({ text, record }) {
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text ? formatMoney(text ?? 0) : '',
          )
          : text
            ? formatMoney(text ?? 0)
            : '';
      },
    },
    {
      title: '里程碑已开票收入金额（价税合计）',
      width: 250,
      dataIndex: 'milestoneInvAmt',
      customRender({ text, record }) {
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text ? formatMoney(text ?? 0) : '',
          )
          : text
            ? formatMoney(text ?? 0)
            : '';
      },
    },
    {
      title: '里程碑未开票收入金额（价税合计）',
      width: 250,
      dataIndex: 'milestoneNoInvAmt',
      customRender({ text, record }) {
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text ? formatMoney(text ?? 0) : '',
          )
          : text
            ? formatMoney(text ?? 0)
            : '';
      },
    },
    {
      title: '本次暂估金额（不含税）',
      width: 180,
      dataIndex: 'estimateAmtExTax',
      customRender({ text, record }) {
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text ? formatMoney(text ?? 0) : '',
          )
          : text
            ? formatMoney(text ?? 0)
            : '';
      },
    },
    {
      title: '本次开票金额（不含税）',
      width: 180,
      dataIndex: 'invAmtExTax',
      customRender({ text, record }) {
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text ? formatMoney(text ?? 0) : '',
          )
          : text
            ? formatMoney(text ?? 0)
            : '';
      },
    },
    {
      title: '本次作废发票金额（不含税）',
      width: 200,
      dataIndex: 'cancelInvAmtExTax',
      customRender({ text, record }) {
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text ? formatMoney(text ?? 0) : '',
          )
          : text
            ? formatMoney(text ?? 0)
            : '';
      },
    },
    {
      title: '里程碑已暂估金额（不含税）',
      width: 200,
      dataIndex: 'milestoneAmtExTax',
      customRender({ text, record }) {
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text ? formatMoney(text ?? 0) : '',
          )
          : text
            ? formatMoney(text ?? 0)
            : '';
      },
    },
    {
      title: '本次冲销暂估金额（不含税）',
      width: 200,
      dataIndex: 'writeOffAmtExTax',
      customRender({ text, record }) {
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text ? formatMoney(text ?? 0) : '',
          )
          : text
            ? formatMoney(text ?? 0)
            : '';
      },
    },
    {
      title: '里程碑已预收款开票金额（不含税）',
      width: 280,
      dataIndex: 'milestoneInvAmtExTax',
      customRender({ text, record }) {
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text ? formatMoney(text ?? 0) : '',
          )
          : text
            ? formatMoney(text ?? 0)
            : '';
      },
    },
    {
      title: '预收款转收入金额（不含税）',
      width: 230,
      dataIndex: 'advPayIncomeAmtExTax',
      customRender({ text, record }) {
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text ? formatMoney(text ?? 0) : '',
          )
          : text
            ? formatMoney(text ?? 0)
            : '';
      },
    },
    {
      title: '验收日期',
      width: 150,
      dataIndex: 'acceptanceDate',
      customRender({ text, record }) {
        const date = record?.acceptanceDate
          ? dayjs(record?.acceptanceDate).format('YYYY-MM-DD')
          : '';
        return record.isChange === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            date,
          )
          : date;
      },
    },
  ];
  return columns.filter((item) => item.show === undefined);
}
// 季度数据查看
export function getQuarterTableColumns(
  dataType: boolean,
  navDetails: (type: string, record: Record<string, any>) => void,
) {
  const isActions = dataType
    ? [
      {
        title: '操作',
        dataIndex: 'actions',
        width: 200,
        fixed: 'right',
        slots: { customRender: 'actions' },
      },
    ]
    : [];
  const columns: any[] = [
    ...isActions,
    {
      dataIndex: 'contractName',
      title: '合同名称',
      width: 200,
      fixed: 'left',
      customRender({ text, record }) {
        return h(
          'div',
          {
            class: 'box-big',
            onClick: () => navDetails('2', record),
          },
          text,
        );
      },
    },
    {
      title: '里程碑名称',
      width: 150,
      dataIndex: 'milestoneName',
      fixed: 'left',
      customRender({ text, record }) {
        return h(
          'div',
          {
            class: 'box-big',
            onClick: () => navDetails('1', record),
          },
          text,
        );
      },
    },
    {
      title: '甲方单位名称',
      width: 100,
      dataIndex: 'partyADeptIdName',
    },
    {
      title: '收入计划编号',
      width: 100,
      dataIndex: 'number',
    },
    {
      title: '收入确认类型',
      width: 150,
      dataIndex: 'incomeConfirmTypeName',
    },
    {
      title: '预计暂估/开票日期',
      width: 150,
      dataIndex: 'estimateInvoiceDate',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '税率（%）',
      width: 100,
      dataIndex: 'taxRate',
      customRender({ text, record }) {
        return h(
          'div',
          {
            class: 'box-big',
          },
          text,
        );
      },
    },
    {
      title: '本次暂估金额（价税合计）',
      width: 200,
      dataIndex: 'estimateAmt',
      isMoney: true,
    },
    {
      title: '本次开票金额（价税合计）',
      width: 200,
      dataIndex: 'invAmt',
      isMoney: true,
    },
    {
      title: '本次作废发票金额（价税合计）',
      width: 220,
      dataIndex: 'cancelInvAmt',
      isMoney: true,
    },
    {
      title: '里程碑已暂估金额（价税合计）',
      width: 220,
      dataIndex: 'milestonEstimateAmt',
    },
    {
      title: '本次冲销暂估金额（价税合计）',
      width: 220,
      dataIndex: 'writeOffAmt',
    },
    {
      title: '里程碑已预收款开票金额（价税合计）',
      width: 250,
      dataIndex: 'milestonePrePaidInvAmt',
      isMoney: true,
    },
    {
      title: '预收款转收入金额（价税合计）',
      width: 220,
      dataIndex: 'advancePayIncomeAmt',
      isMoney: true,
    },
    {
      title: '本次收入计划金额（不含税）',
      width: 200,
      dataIndex: 'incomePlanAmt',
    },
    {
      title: '专业中心',
      width: 100,
      dataIndex: 'expertiseCenterName',
    },
    {
      title: '专业所',
      width: 100,
      dataIndex: 'expertiseStationName',
    },
  ];
  return columns.filter((item) => item.show === undefined);
}

// 编制-调整-差异--查看详情
export function getTableViewColumns(
  navDetails: (type: string, record: Record<string, any>) => void,
) {
  const columns: any[] = [
    {
      title: '',
      children: [
        {
          title: '收入计划编号',
          dataIndex: 'contractName',
          fixed: 'left',
        },
        {
          title: '专业中心',
          width: 100,
          dataIndex: 'expertiseCenterName',
          fixed: 'left',
        },
        {
          title: '专业所',
          width: 100,
          dataIndex: 'partyADeptIdName',
          fixed: 'left',
        },
        {
          title: '数据状态',
          width: 100,
          dataIndex: 'number',
          fixed: 'left',
        },
      ],
    },
    {
      title: '合同里程碑信息',
      children: [
        {
          title: '合同编号',
          dataIndex: 'lockStatus',
          width: 100,
          customRender({ text, record }) {
            return h('div', {
              class: 'box-big',
              onClick: () => navDetails('1', record),
            });
          },
        },
        {
          dataIndex: 'dataStatus',
          title: '合同名称',
          width: 100,
          customRender({ text, record }) {
            return h('div', {
              class: 'box-big',
              onClick: () => navDetails('1', record),
            });
          },
        },
        {
          title: '里程碑名称',
          width: 150,
          dataIndex: 'incomeConfirmType',
        },
        {
          title: '里程碑金额（含税）',
          width: 150,
          dataIndex: 'estimateInvoiceDate',
          isMoney: true,
        },
      ],
    },
    {
      title: '对账/开票信息',
      children: [
        {
          title: '开票主体',
          width: 200,
          dataIndex: 'estimateAmt',
        },
        {
          title: '甲方单位名称',
          width: 200,
          dataIndex: 'partyADeptIdName',
          customRender({ text, record }) {
            return h('div', {
              class: 'box-big',
              onClick: () => navDetails('1', record),
            });
          },
        },
        {
          title: '集团内/外',
          width: 220,
          dataIndex: 'cancelInvAmt',
        },
      ],
    },
    {
      title: '收入计划信息',
      children: [
        {
          title: '收入确认类型',
          width: 220,
          dataIndex: 'milestonEstimateAmt',
        },
        {
          title: '税率',
          width: 220,
          dataIndex: 'writeOffAmt',
        },
        {
          title: '进度款/完工款开票金额',
          width: 250,
          dataIndex: 'milestonePrePaidInvAmt',
          isMoney: true,
        },
        {
          title: '预收款开票金额',
          width: 220,
          dataIndex: 'advancePayIncomeAmt',
          isMoney: true,
        },
        {
          title: '暂估收入金额',
          width: 200,
          dataIndex: 'incomePlanAmt',
          isMoney: true,
        },
        {
          title: '本月收入计划金额',
          width: 100,
          dataIndex: 'expertiseCenterName',
          isMoney: true,
        },
        {
          title: '本次预计开票金额（不含税）',
          width: 100,
          dataIndex: 'expertiseStationName',
          isMoney: true,
        },
        {
          title: '本次预计税额',
          width: 130,
          dataIndex: 'projectNumber',
        },
        {
          title: '预计开票/暂估时间',
          width: 150,
          dataIndex: 'projectName',
        },
        {
          title: '发票红冲金额',
          width: 100,
          dataIndex: 'projectRspUserId',
        },
        {
          title: '里程碑已暂估金额',
          width: 180,
          dataIndex: 'billingCompany',
        },
        {
          title: '本次冲销暂估金额',
          width: 180,
          dataIndex: 'internalExternal',
        },
        {
          title: '里程碑已预收款转收入金额（不含税）',
          width: 180,
          dataIndex: 'milestoneAmt',
        },
        {
          title: '预收转收入金额',
          width: 250,
          dataIndex: 'milestoneInvAmt',
        },
        {
          title: '本次进度款开票金额',
          width: 250,
          dataIndex: 'milestoneNoInvAmt',
        },
      ],
    },
    {
      title: '收入确认信息',
      children: [
        {
          title: '已确认收入金额',
          width: 180,
          dataIndex: 'estimateAmt',
        },
        {
          title: '暂估收入金额（含冲销暂估金额）',
          width: 180,
          dataIndex: 'invAmtExTax',
        },
        {
          title: '剩余未确认金额',
          width: 200,
          dataIndex: 'cancelInvAmtExTax',
        },
        {
          title: '已计入预收款开票金额',
          width: 200,
          dataIndex: 'milestoneAmtExTax',
        },
      ],
    },
    {
      title: '接口人员信息',
      children: [
        {
          title: '项目负责人',
          width: 200,
          dataIndex: 'projectRspUserName',
        },
      ],
    },
    {
      title: '其他信息',
      children: [
        {
          title: '修改收入计划原因',
          width: 250,
          dataIndex: 'milestoneInvAmtExTax',
        },
        {
          title: '是否本月计划不执行',
          width: 200,
          dataIndex: 'advPayIncomeAmtExTax',
        },
        {
          title: '备注说明',
          width: 130,
          dataIndex: 'remark',
        },
      ],
    },
  ];
  return columns.filter((item) => item.show === undefined);
}
