package com.chinasie.orion.domain.dto;

import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ProjectContractChangeForm Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-26 15:37:42
 */
@ApiModel(value = "ProjectContractChangeFormDTO对象", description = "项目合同变更表单")
@Data
public class ProjectContractChangeFormDTO extends ObjectDTO implements Serializable {

    /**
     * 字段编码
     */
    @ApiModelProperty(value = "字段编码")
    private String fieldCode;

    /**
     * 字段名称
     */
    @ApiModelProperty(value = "字段名称")
    private String fieldName;

    /**
     * 字段类型
     */
    @ApiModelProperty(value = "字段类型")
    private String fieldType;

    /**
     * keyValue
     */
    @ApiModelProperty(value = "keyValue")
    private String keyValue;
    /**
     * 是否必填
     */
    @ApiModelProperty(value = "是否必填")
    private Boolean isNull;

}
