package com.chinasie.orion.management.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * NcfFormpurchaseRequest VO对象
 *
 * <AUTHOR>
 * @since 2024-06-04 10:01:14
 */
@ApiModel(value = "NcfFormpurchaseRequestVO对象", description = "采购申请主表")
@Data
public class NcfFormpurchaseRequestVO extends ObjectVO implements Serializable {

    /**
     * 采购申请单编码
     */
    @ApiModelProperty(value = "采购申请单编码")
    private String code;


    /**
     * 申请单名称
     */
    @ApiModelProperty(value = "申请单名称")
    private String name;


    /**
     * 采购立项完成时间
     */
    @ApiModelProperty(value = "采购立项完成时间")
    private Date projectEndTime;


    /**
     * 采购立项号
     */
    @ApiModelProperty(value = "采购立项号")
    private String projectCode;


    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称")
    private String fileName;


    /**
     * 申请单状态
     */
    @ApiModelProperty(value = "申请单状态")
    private String state;


    /**
     * 申请单类型
     */
    @ApiModelProperty(value = "申请单类型")
    private String type;


    /**
     * 申请单来源
     */
    @ApiModelProperty(value = "申请单来源")
    private String source;


    /**
     * 采购申请金额（元）
     */
    @ApiModelProperty(value = "采购申请金额（元）")
    private BigDecimal money;


    /**
     * 汇率
     */
    @ApiModelProperty(value = "汇率")
    private BigDecimal rate;


    /**
     * 预计开工时间
     */
    @ApiModelProperty(value = "预计开工时间")
    private Date estimatedBeginTime;


    /**
     * 质保等级
     */
    @ApiModelProperty(value = "质保等级")
    private String warrantyLevel;

    /**
     * 申请部门id
     */
    @ApiModelProperty(value = "申请部门id")
    private String applicantDeptId;
    /**
     * 申请部门
     */
    @ApiModelProperty(value = "申请部门")
    private String applicantDept;

    /**
     * 申请人id
     */
    @ApiModelProperty(value = "申请人id")
    private String applicantUserid;
    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
    private String applicantUser;


    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    private String currency;


    /**
     * 与现场安全相关
     */
    @ApiModelProperty(value = "与现场安全相关")
    private String withSafety;


    /**
     * 采购计划号
     */
    @ApiModelProperty(value = "采购计划号")
    private String purchasePlanCode;


    /**
     * 归口部门
     */
    @ApiModelProperty(value = "归口部门")
    private String bkDept;


    /**
     * 归口管理
     */
    @ApiModelProperty(value = "归口管理")
    private String bkManage;


    /**
     * 建议采购方式
     */
    @ApiModelProperty(value = "建议采购方式")
    private String suggestPurchaseWay;


    /**
     * 采购内容
     */
    @ApiModelProperty(value = "采购内容")
    private String purchaseContent;


    /**
     * 推荐供应商名单
     */
    @ApiModelProperty(value = "推荐供应商名单")
    private String recSupList;


    /**
     * 推荐潜在供应商名单
     */
    @ApiModelProperty(value = "推荐潜在供应商名单")
    private String recPtSupList;


    /**
     * 是否有匹配的框架合同
     */
    @ApiModelProperty(value = "是否有匹配的框架合同")
    private String isFrameContrac;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    /**
     * 框架合同编码
     */
    @ApiModelProperty(value = "框架合同编码")
    private String contractId;

    @ApiModelProperty(value = "商务负责人")
    private String businessLeader;


    @ApiModelProperty(value = "采购申请发起时间")
    private Date purchaseRequestInitTime;

    /**
     * 采购立项审批完成时间
     */
    @ApiModelProperty(value = "采购立项审批完成时间")
    private String projectApprovalEndTime;

    @ApiModelProperty(value = "附件文档")
    private List<FileDTO> attachments;
}
