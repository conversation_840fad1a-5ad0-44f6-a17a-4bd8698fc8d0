<script setup lang="ts">
import {
  BasicButton,
  BasicImport,
  BasicTableAction,
  downloadByData,
  getDictByNumber,
  IOrionTableActionItem,
  isPower,
  OrionTable,
  useModal,
} from 'lyra-component-vue3';
import {
  computed, ComputedRef, inject, ref, Ref, onMounted, nextTick,
} from 'vue';
import { message, Modal } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { WorkflowAction } from 'lyra-workflow-component-vue3';
import { formatTableColumns, openFormDrawer } from './utils';
import AddTableModal from './components/AddTableModal.vue';
import AddModal from './components/AddPlan.vue';
import Api from '/@/api';

const router = useRouter();
const tableRef: Ref = ref();
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const formData: any = inject('formData', {});
const powerData = inject('powerData', {});
const workflowActionRef = ref();
const [registerModal, { openModal }] = useModal();
const [registerAdd, { openModal: setAddPlan }] = useModal();
const planActiveOptions: Ref<Record<any, any>[]> = ref([]);
const fileTypeList = [
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-excel.sheet.macroEnabled.12',
];
const actions: IOrionTableActionItem[] = [
  {
    text: '编辑',
    event: 'edit',
    isShow: (record) => isPower('PMS_XMXQ_container_07_02_02_button_01', record.rdAuthList),
  },
  {
    text: '删除',
    isShow: (record) => isPower('PMS_XMXQ_container_07_02_02_button_02', record.rdAuthList),
    event: 'delete',
  },
  // {
  //   text: '启动流程',
  //   isShow: (record) => isPower('PMS_XMXQ_container_07_02_02_button_04', record.rdAuthList),
  //   event: 'start',
  // },
  {
    text: '激活',
    isShow: (record: Record<string, any>) => (isPower('PMS_XMXQ_container_07_02_02_button_05', record.rdAuthList) && record.status === 160),
    event: 'activate',
  },

  {
    text: '关闭',
    isShow: (record: Record<string, any>) => (isPower('PMS_XMXQ_container_07_02_02_button_06', record.rdAuthList) && record.status === 130),
    event: 'close',
  },
  {
    text: '转计划',
    isShow: (record: Record<string, any>) => isPower('PMS_XMXQ_container_07_02_02_button_07', record.rdAuthList),
    event: 'plan',
  },
];
const tableOptions = {
  rowSelection: {
    onChange(keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  smallSearchField: undefined,
  columns: formatTableColumns(router, isPower),
  isFilter2: true,
  // filterConfigName: 'PMS_PROJECTMANAGE_PROBLEMMANAGE',
  filterConfig: {
    fields: [
      {
        field: 'name',
        fieldName: '名称',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'number',
        fieldName: '编号',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'priorityLevel',
        fieldName: '优先级',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterface: '/pms/dict/code/pms_priority',
        referenceInterfaceMethod: 'GET',
        referenceInterfaceParams: null,
        component: 'Select',
        hidden: false,
        constValue: null,
        fieldNames: '{"name":"name","value":"number"} ',
        searchFieldName: null,
      },
      {
        field: 'principalName',
        fieldName: '问题负责人',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },

    ],
  },
  api: async (params) => {
    const result: Record<string, any> = await new Api('/pms').fetch({
      ...params,
      query: {
        projectId: formData?.value?.id,
      },
      power: {
        pageCode: 'PMS0004',
        containerCode: 'PMS_XMXQ_container_07_02_02',
      },
    }, 'question-management/getPage', 'POST');
    return result;
  },
};

const toolButtons: ComputedRef<Record<string, any>[]> = computed(() => [
  {
    event: 'add',
    text: '新增',
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
    code: 'PMS_XMXQ_container_07_02_01',
  },
  {
    event: 'import',
    text: '导入',
    icon: 'sie-icon-daoru',
    code: 'PMS_XMXQ_container_07_02_01_button_03',
  },
  {
    event: 'export',
    text: '导出',
    icon: 'sie-icon-daochu',
    code: 'PMS_XMXQ_container_07_02_01_button_04',
  },
  {
    event: 'batchDelete',
    text: '删除',
    icon: 'sie-icon-shanchu',
    code: 'PMS_XMXQ_container_07_02_01_button_02',
  },
]);

function toolClick({ event }: Record<string, any>) {
  switch (event) {
    case 'add':
      openFormDrawer(AddTableModal, {
        projectId: formData?.value?.id,
        fromObjName: formData?.value?.name,
        modelName: 'pms',
      }, updateTable);
      break;
    case 'batchDelete':
      deleteBatchData(selectedRows.value.map((item) => item.id), 'all');
      break;
    case 'import':
      openModal(true, {});
      break;
    case 'export':
      Modal.confirm({
        title: '确认导出',
        content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
        onOk() {
          downloadByData('/pms/question-management/export/excel', [], '', 'POST', true, false, '导出处理完成，现在开始下载');
        },
      });
      break;
  }
}

function deleteBatchData(params, type = 'all') {
  Modal.confirm({
    title: '删除提示',
    content: type === 'all' ? '是否删除选中的数据？' : '是否删除当前的数据？',
    onOk() {
      new Api('/pms').fetch(params, 'question-management/removeBatch', 'DELETE').then((res) => {
        message.success('删除成功。');
        updateTable();
      });
    },
  });
}

function actionClick(actionItem: { event: string }, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'edit':
      openFormDrawer(AddTableModal, record, updateTable);
      break;
    case 'delete':
      deleteBatchData([record.id], 'one');
      break;
    case 'start':
      workflowActionRef.value.setProps({ businessData: record });
      workflowActionRef.value.init().then((res) => {
        workflowActionRef.value?.onAddTemplate({
          messageUrl: `/pms/question-management-details/${record.id}?tabsType=process`,
        });
      });
      break;
    case 'close':
      Modal.confirm({
        title: '关闭提示',
        content: '是否关闭当前的数据',
        onOk() {
          new Api('/pms').fetch([record.id], 'question-management/close', 'PUT').then((res) => {
            message.success('关闭成功。');
            updateTable();
          });
        },
      });
      break;

    case 'activate':
      Modal.confirm({
        title: '激活提示',
        content: '是否激活当前的数据',
        onOk() {
          new Api('/pms').fetch([record.id], 'question-management/open', 'PUT').then((res) => {
            message.success('激活成功。');
            updateTable();
          });
        },
      });
      break;
    case 'plan':
      addModalVisibleChange(true, record);
      break;
  }
}

function addModalVisibleChange(value: boolean, record: any) {
  if (value) {
    setAddPlan(true, {
      parentIds: record.id,
      parentData: [record],
      projectData: record,
      from: '',
      isPlan: true,
    });
  }
  if (!value) {
    updateForm();
  }
}

function updateForm() {
  nextTick(() => {
    tableRef.value.reload();
  });
}

const workflowProps = computed(() => ({
  Api,
  businessData: {},
  afterEvent() {
    updateTable();
  },
}));

function updateTable() {
  tableRef.value?.reload();
}

function getButtonProps(item) {
  if (item.event === 'batchDelete') {
    item.disabled = !selectedRows.value.length;
  }
  return item;
}

function requestBasicImport(data) {
  let params = new FormData();
  params.append('file', data[0]);

  return new Api('/pms').fetch(params, `question-management/import/excel/check?id=${formData.value.id}`, 'POST');
}

const downloadFileObj = {
  url: '/pms/question-management/download/excel/tpl',
  method: 'GET',
};
const requestSuccessImport = (successKey) => new Api('/pms').fetch('', `question-management/import/excel/${successKey}`, 'POST');

function changeImportModalFlag(data) {
  if (!data.visible && data.successImportFlag) {
    // 导入成功刷新数据
    tableRef.value.reload();
  } else if (!data.visible && !data.successImportFlag && data.succ) {
    new Api('/pms').fetch('', `question-management/import/excel/cancel/${data.succ}`, 'POST').then((res) => {
      message.success('取消导入成功');
    });
  }
}

onMounted(async () => {
  planActiveOptions.value = await getDictByNumber('planActive');
});

</script>

<template>
  <!--  v-get-power="{pageCode:'list-container-5e5296-RStTRGxJJ'}"-->
  <div class="question-table">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <template
          v-for="button in toolButtons"
          :key="button.event"
        >
          <BasicButton
            v-if="isPower(button.code,powerData)"
            v-bind="getButtonProps(button)"
            @click="toolClick(button)"
          >
            {{ button.text }}
          </BasicButton>
        </template>
      </template>
      <template #actions="{record}">
        <BasicTableAction
          :actions="actions"
          :record="record"
          @actionClick="actionClick($event,record)"
        />
      </template>
    </OrionTable>

    <WorkflowAction
      v-show="false"
      ref="workflowActionRef"
      :workflow-props="workflowProps"
    />
    <BasicImport
      :requestBasicImport="requestBasicImport"
      :requestSuccessImport="requestSuccessImport"
      :downloadFileObj="downloadFileObj"
      :fileTypeList="fileTypeList"
      @register="registerModal"
      @change-import-modal-flag="changeImportModalFlag"
    />
    <AddModal
      :planActiveOptions="planActiveOptions"
      @register="registerAdd"
      @handleColse="() => addModalVisibleChange(false, {})"
    />
  </div>
</template>

<style scoped lang="less">

</style>
