package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "AttendanceSignResultVO对象", description = "验收人力成本费用")
@Data
public class AttendanceSignResultVO {
    /**
     * 验收人力成本费用明细
     */
    @ApiModelProperty(value = "验收人力成本费用明细")
    private AttendanceResultSignVO attendanceResultCostVO;

    /**
     * 验收人力成本费用统计
     */
    @ApiModelProperty(value = "验收人力成本费用统计")
    private LaborCostStatisticsSingleVO laborCostStatisticsSingleVO;

}
