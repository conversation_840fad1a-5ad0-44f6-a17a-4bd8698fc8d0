<template>
  <div class="h-full flex flex-ver">
    <div
      v-if="isTitle"
      class="title flex flex-ac"
    >
      <h3
        v-if="userInfoData"
        class="flex-f1"
      >
        {{ userInfoData.name }}的权限
      </h3>
      <div>
        <SearchInput
          v-model:value="searchValue"
          placeholder="请输入姓名"
          :loading="searchLoadStatus"
          @search="userSearch"
        />
      </div>
    </div>
    <div class="main flex flex-f1 pt10">
      <template v-if="userId">
        <div
          v-loading="loadRoleStatus"
          class="menu"
        >
          <Menu
            v-if="roleList.length"
            :show-header="false"
            :menu-data="roleListData"
            :default-action-id="roleDefaultAction"
            size="small"
            @menuChange="menuChange"
          />
          <div
            v-else
            class="h-full flex flex-pac"
          >
            <Empty description="该用户未分配角色" />
          </div>
        </div>
        <div class="table-wrap flex-f1">
          <RoleTable
            v-if="roleDefaultAction"
            :key="userId + roleDefaultAction"
            :role-id="roleDefaultAction"
          />
          <div
            v-else
            class="h-full flex flex-pac"
          >
            <Empty description="请选择角色" />
          </div>
        </div>
      </template>
      <div
        v-else
        class="h-full w-full flex flex-pac"
      >
        <Empty description="请搜索用户" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed, defineComponent, reactive, toRefs, watch,
} from 'vue';
import { Input, message, Empty } from 'ant-design-vue';
import { BasicMenu as Menu } from 'lyra-component-vue3';
import RoleTable from './RoleTable.vue';
import Api from '/@/api';

export default defineComponent({
  name: 'ViewRoleMain',
  components: {
    SearchInput: Input.Search,
    Menu,
    RoleTable,
    Empty,
  },
  props: {
    type: {
      type: String,
      default: 'search', // search | user
    },
    userInfo: {
      type: Object,
      default: null,
    },
    isTitle: {
      type: Boolean,
      default: true,
    },
  },
  setup(props) {
    const state = reactive({
      userId: props?.userInfo?.id || '',
      roleDefaultAction: '',
      roleList: [],
      loadRoleStatus: false,
      searchValue: '',
      searchLoadStatus: false,
      userInfoData: props.userInfo,
    });

    const roleListData = computed(() => [
      // {
      //   name: '所有权限',
      //   id: 'all'
      // },
      ...state.roleList,
    ]);

    watch(
      () => state.userId,
      (userId) => {
        userId && init();
      },
    );

    init();

    function init() {
      if (state.userId) {
        loadRoleLest(state.userId);
      }
    }

    /**
       * 加载用户角色列表
       * @param userId 用户ID
       */
    function loadRoleLest(userId) {
      state.loadRoleStatus = true;
      new Api('/pmi/user/user-query-role')
        .fetch(
          {
            id: userId,
          },
          '',
          'POST',
        )
        .then((data) => {
          state.roleList = data;
          if (data.length) {
            state.roleDefaultAction = data[0].id;
          } else {
            state.roleDefaultAction = '';
          }
        })
        .finally(() => {
          state.loadRoleStatus = false;
        });
    }

    return {
      ...toRefs(state),
      roleListData,
      menuChange({ id, index, item }) {
        state.roleDefaultAction = id;
      },
      // 获取用户信息
      userSearch() {
        if (!state.searchValue) return;
        state.searchLoadStatus = true;
        new Api('/pmi/user/page')
          .fetch(
            {
              pageNum: 1,
              pageSize: 10,
              query: {},
              queryCondition: [
                {
                  column: 'name',
                  link: 'OR',
                  type: 'eq',
                  value: state.searchValue,
                },
              ],
            },
            '',
            'POST',
          )
          .then((data) => {
            const { content } = data;
            if (content && content.length) {
              state.userId = content[0].id;
              state.userInfoData = content[0];
            } else {
              message.error('未查询到该用户');
            }
          })
          .finally(() => {
            state.searchLoadStatus = false;
          });
      },
    };
  },
});
</script>

<style scoped lang="less">
  .title {
    border-bottom: 1px solid ~`getPrefixVar('border-color-base')`;
    position: relative;
    padding: 0 0 10px 15px;

    h3 {
      font-size: 16px;
      margin: 0;
    }

    &::after {
      content: '';
      position: absolute;
      left: 0;
      width: 3px;
      height: 16px;
      background-color:~`getPrefixVar('primary-color')`;
      top: 50%;
      margin-top: -13px;
    }
  }

  .menu {
    position: relative;
    width: 200px;
    overflow-y: auto;
    border-right: 1px solid ~`getPrefixVar('border-color-base')`;
  }
</style>
