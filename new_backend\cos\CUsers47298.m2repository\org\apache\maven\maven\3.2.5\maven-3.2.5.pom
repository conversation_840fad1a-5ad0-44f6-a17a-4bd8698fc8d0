<?xml version="1.0" encoding="UTF-8"?>

  <!--
    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements. See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to you under the Apache License, Version
    2.0 (the "License"); you may not use this file except in compliance
    with the License. You may obtain a copy of the License at
    http://www.apache.org/licenses/LICENSE-2.0 Unless required by
    applicable law or agreed to in writing, software distributed under
    the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES
    OR CONDITIONS OF ANY KIND, either express or implied. See the
    License for the specific language governing permissions and
    limitations under the License.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache.maven</groupId>
    <artifactId>maven-parent</artifactId>
    <version>25</version>
    <relativePath>../pom/maven/pom.xml</relativePath>
  </parent>

  <artifactId>maven</artifactId>
  <version>3.2.5</version>
  <packaging>pom</packaging>

  <name>Apache Maven</name>
  <description>Maven is a software build management and
    comprehension tool. Based on the concept of a project object model:
    builds, dependency management, documentation creation, site
    publication, and distribution publication are all controlled from
    the declarative file. Maven can be extended by plugins to utilise a
    number of other development tools for reporting or the build
    process.
  </description>
  <url>http://maven.apache.org/ref/${project.version}</url>
  <inceptionYear>2001</inceptionYear>

  <properties>
    <maven.compiler.source>1.6</maven.compiler.source>
    <maven.compiler.target>1.6</maven.compiler.target>
    <classWorldsVersion>2.5.2</classWorldsVersion>
    <commonsCliVersion>1.2</commonsCliVersion>
    <junitVersion>4.11</junitVersion>
    <plexusVersion>1.5.5</plexusVersion>
    <plexusInterpolationVersion>1.21</plexusInterpolationVersion>
    <plexusUtilsVersion>3.0.20</plexusUtilsVersion>
    <!-- Latest version of Guava that works with Sisu -->
    <guavaVersion>18.0</guavaVersion>
    <guiceVersion>3.2.3</guiceVersion>
    <sisuInjectVersion>0.3.0.M1</sisuInjectVersion>
    <wagonVersion>2.8</wagonVersion>
    <securityDispatcherVersion>1.3</securityDispatcherVersion>
    <cipherVersion>1.7</cipherVersion>
    <modelloVersion>1.8.1</modelloVersion>
    <jxpathVersion>1.3</jxpathVersion>
    <aetherVersion>1.0.0.v20140518</aetherVersion>
    <slf4jVersion>1.7.5</slf4jVersion>
    <maven.test.redirectTestOutputToFile>true</maven.test.redirectTestOutputToFile>
    <!-- Control the name of the distribution and information output by mvn -->
    <distributionId>apache-maven</distributionId>
    <distributionShortName>Maven</distributionShortName>
    <distributionName>Apache Maven</distributionName>
    <maven.site.path>ref/3-LATEST</maven.site.path>
    <checkstyle.violation.ignore>RedundantThrows,NewlineAtEndOfFile,ParameterNumber,MethodLength,FileLength,JavadocType,MagicNumber,InnerAssignment,MethodName</checkstyle.violation.ignore>
    <checkstyle.excludes>**/package-info.java</checkstyle.excludes>
  </properties>

  <modules>
    <module>maven-plugin-api</module>
    <module>maven-model</module>
    <module>maven-model-builder</module>
    <module>maven-core</module>
    <module>maven-settings</module>
    <module>maven-settings-builder</module>
    <module>maven-artifact</module>
    <module>maven-aether-provider</module>
    <module>maven-repository-metadata</module>
    <module>maven-embedder</module>
    <module>maven-compat</module>
    <module>apache-maven</module>
  </modules>

  <scm>
    <connection>scm:git:https://git-wip-us.apache.org/repos/asf/maven.git</connection>
    <developerConnection>scm:git:https://git-wip-us.apache.org/repos/asf/maven.git</developerConnection>
    <url>https://github.com/apache/maven/tree/${project.scm.tag}</url>
    <tag>maven-3.2.5</tag>
  </scm>
  <issueManagement>
    <system>jira</system>
    <url>http://jira.codehaus.org/browse/MNG</url>
  </issueManagement>
  <ciManagement>
    <system>Jenkins</system>
    <url>https://builds.apache.org/job/maven-3.x/</url>
  </ciManagement>
  <distributionManagement>
    <downloadUrl>http://maven.apache.org/download.html</downloadUrl>
    <site>
      <id>apache.website</id>
      <url>scm:svn:https://svn.apache.org/repos/infra/websites/production/maven/content/${maven.site.path}</url>
    </site>
  </distributionManagement>

  <contributors>
    <contributor>
      <name>Stuart McCulloch</name>
    </contributor>
    <contributor>
      <name>Christian Schulte (MNG-2199)</name>
    </contributor>
    <contributor>
      <name>Christopher Tubbs (MNG-4226)</name>
    </contributor>
    <contributor>
      <name>Konstantin Perikov (MNG-4565)</name>
    </contributor>
    <contributor>
      <name>Sébastian Le Merdy (MNG-5613)</name>
    </contributor>
    <contributor>
      <name>Mark Ingram (MNG-5639)</name>
    </contributor>
    <contributor>
      <name>Phil Pratt-Szeliga (MNG-5645)</name>
    </contributor>
  </contributors>

  <prerequisites>
    <maven>2.2.1</maven>
  </prerequisites>

  <!--bootstrap-start-comment-->
  <dependencyManagement>
    <!--bootstrap-end-comment-->
    <dependencies>
      <!--  Maven Modules -->
      <!--bootstrap-start-comment-->
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-model</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-settings</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-settings-builder</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-plugin-api</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-embedder</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-core</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-model-builder</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-compat</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-artifact</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-aether-provider</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-repository-metadata</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!--bootstrap-end-comment-->
      <!--  Plexus -->
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-utils</artifactId>
        <version>${plexusUtilsVersion}</version>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>${guavaVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.sonatype.sisu</groupId>
        <artifactId>sisu-guice</artifactId>
        <version>${guiceVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.sonatype.sisu</groupId>
        <artifactId>sisu-guice</artifactId>
        <version>${guiceVersion}</version>
        <classifier>no_aop</classifier>
      </dependency>
      <dependency>
        <groupId>org.eclipse.sisu</groupId>
        <artifactId>org.eclipse.sisu.plexus</artifactId>
        <version>${sisuInjectVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-component-annotations</artifactId>
        <version>${plexusVersion}</version>
        <exclusions>
          <exclusion>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-classworlds</artifactId>
        <version>${classWorldsVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-interpolation</artifactId>
        <version>${plexusInterpolationVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>${slf4jVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-simple</artifactId>
        <version>${slf4jVersion}</version>
        <optional>true</optional>
      </dependency>
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-classic</artifactId>
        <version>1.0.7</version>
        <optional>true</optional>
      </dependency>
      <!--  Wagon -->
      <dependency>
        <groupId>org.apache.maven.wagon</groupId>
        <artifactId>wagon-provider-api</artifactId>
        <version>${wagonVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.wagon</groupId>
        <artifactId>wagon-file</artifactId>
        <version>${wagonVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.wagon</groupId>
        <artifactId>wagon-http</artifactId>
        <version>${wagonVersion}</version>
        <classifier>shaded</classifier>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <!--  Repository -->
      <dependency>
        <groupId>org.eclipse.aether</groupId>
        <artifactId>aether-api</artifactId>
        <version>${aetherVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.aether</groupId>
        <artifactId>aether-spi</artifactId>
        <version>${aetherVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.aether</groupId>
        <artifactId>aether-impl</artifactId>
        <version>${aetherVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.aether</groupId>
        <artifactId>aether-util</artifactId>
        <version>${aetherVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.aether</groupId>
        <artifactId>aether-connector-basic</artifactId>
        <version>${aetherVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.aether</groupId>
        <artifactId>aether-transport-wagon</artifactId>
        <version>${aetherVersion}</version>
      </dependency>
      <!--  Commons -->
      <dependency>
        <groupId>commons-cli</groupId>
        <artifactId>commons-cli</artifactId>
        <version>${commonsCliVersion}</version>
        <exclusions>
          <exclusion>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
          </exclusion>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>commons-jxpath</groupId>
        <artifactId>commons-jxpath</artifactId>
        <version>${jxpathVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.sonatype.plexus</groupId>
        <artifactId>plexus-sec-dispatcher</artifactId>
        <version>${securityDispatcherVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.sonatype.plexus</groupId>
        <artifactId>plexus-cipher</artifactId>
        <version>${cipherVersion}</version>
      </dependency>
    </dependencies>
    <!--bootstrap-start-comment-->
  </dependencyManagement>
  <!--bootstrap-end-comment-->
  <!--bootstrap-start-comment-->
  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>${junitVersion}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <!--bootstrap-end-comment-->

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.codehaus.plexus</groupId>
          <artifactId>plexus-component-metadata</artifactId>
          <version>${plexusVersion}</version>
          <executions>
            <execution>
              <goals>
                <goal>generate-metadata</goal>
                <goal>generate-test-metadata</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <configuration>
            <autoVersionSubmodules>true</autoVersionSubmodules>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <configuration>
            <argLine>-Xmx256m</argLine>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.codehaus.modello</groupId>
          <artifactId>modello-maven-plugin</artifactId>
          <version>${modelloVersion}</version>
          <executions>
            <execution>
              <id>site-docs</id>
              <phase>pre-site</phase>
              <goals>
                <goal>xdoc</goal>
                <goal>xsd</goal>
              </goals>
            </execution>
            <execution>
              <id>standard</id>
              <goals>
                <goal>java</goal>
                <goal>xpp3-reader</goal>
                <goal>xpp3-writer</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.felix</groupId>
          <artifactId>maven-bundle-plugin</artifactId>
          <version>1.0.0</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>buildnumber-maven-plugin</artifactId>
          <version>1.2</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <configuration>
            <topSiteURL>scm:svn:https://svn.apache.org/repos/infra/websites/production/maven/content/${maven.site.path}</topSiteURL>
          </configuration>
          <dependencies>
            <dependency>
              <groupId>org.apache.maven.doxia</groupId>
              <artifactId>doxia-module-markdown</artifactId>
              <version>1.5</version>
            </dependency>
          </dependencies>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-scm-publish-plugin</artifactId>
          <version>1.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.rat</groupId>
          <artifactId>apache-rat-plugin</artifactId>
          <configuration>
            <excludes>
              <exclude>src/test/resources*/**</exclude>
              <exclude>src/test/projects/**</exclude>
              <exclude>src/test/remote-repo/**</exclude>
              <exclude>**/*.odg</exclude>
            </excludes>
          </configuration>
        </plugin>
        <!--This plugin's configuration is used to store Eclipse m2e settings only. It has no influence on the Maven build itself.-->
        <plugin>
          <groupId>org.eclipse.m2e</groupId>
          <artifactId>lifecycle-mapping</artifactId>
          <version>1.0.0</version>
          <configuration>
            <lifecycleMappingMetadata>
              <pluginExecutions>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.apache.rat</groupId>
                    <artifactId>apache-rat-plugin</artifactId>
                    <versionRange>[0.10,)</versionRange>
                    <goals>
                      <goal>check</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore />
                  </action>
                </pluginExecution>
              </pluginExecutions>
            </lifecycleMappingMetadata>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>animal-sniffer-maven-plugin</artifactId>
        <version>1.10</version>
        <configuration>
          <signature>
            <groupId>org.codehaus.mojo.signature</groupId>
            <artifactId>java16</artifactId>
            <version>1.1</version>
          </signature>
        </configuration>
        <executions>
          <execution>
            <id>check-java-1.6-compat</id>
            <phase>process-classes</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-doap-plugin</artifactId>
        <version>1.1</version>
        <configuration>
          <asfExtOptions>
            <charter>The mission of the Apache Maven project is to create and maintain software
            libraries that provide a widely-used project build tool, targeting mainly Java
            development. Apache Maven promotes the use of dependencies via a
            standardized coordinate system, binary plugins, and a standard build
            lifecycle.</charter>
          </asfExtOptions>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
        <configuration>
          <excludes combine.children="append">
            <exclude>bootstrap/**</exclude>
            <exclude>README.bootstrap.txt</exclude>
            <exclude>.repository/**</exclude> <!-- jenkins with local maven repository -->
            <exclude>.maven/spy.log</exclude> <!-- hudson maven3 integration log -->
          </excludes>
          <!-- maven-parent:24 sets ignore errors, but core is ahead -->
          <ignoreErrors>false</ignoreErrors>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>apache-release</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-assembly-plugin</artifactId>
            <executions>
              <execution>
                <id>source-release-assembly</id>
                <configuration>
                  <!-- we have a dedicated distribution module -->
                  <skipAssembly>true</skipAssembly>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>reporting</id>
      <reporting>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <configuration>
              <linksource>true</linksource>
              <notimestamp>true</notimestamp>
              <quiet>true</quiet>
              <links combine.children="append">
                <link>http://download.eclipse.org/aether/aether-core/${aetherVersion}/apidocs/</link>
                <link>http://plexus.codehaus.org/plexus-containers/plexus-container-default/apidocs/</link>
              </links>
            </configuration>
            <reportSets>
              <reportSet>
                <id>aggregate</id>
                <inherited>false</inherited>
                <reports>
                  <report>aggregate</report>
                </reports>
              </reportSet>
            </reportSets>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-jxr-plugin</artifactId>
            <reportSets>
              <reportSet>
                <id>aggregate</id>
                <inherited>false</inherited>
                <reports>
                  <report>aggregate</report>
                </reports>
              </reportSet>
            </reportSets>
          </plugin>
        </plugins>
      </reporting>
    </profile>
    <profile>
      <id>maven-repo-local</id>
      <activation>
        <property>
          <name>maven.repo.local</name>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <systemProperties combine.children="append">
                <property>
                  <!-- Pass this through to the tests (if set!) to have them pick the right repository -->
                  <name>maven.repo.local</name>
                  <value>${maven.repo.local}</value>
                </property>
              </systemProperties>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
