package com.chinasie.orion.service;

import java.util.List;

/**
 * 数据更新消息服务.
 *
 * <AUTHOR>
 */
public interface DataUpdateMessageService {
    /**
     * 发送数据更新通知.
     *
     * @param dataType
     * @param dataId
     * @throws Exception
     */
    void sendDataUpdatedMessage(String dataType, String dataId);

    /**
     * 从队列弹出最近更新的数据Id.
     *
     * @param dataType
     * @param size pop数量
     * @return
     */
    List<String> popUpdatedDataIds(String dataType, Integer size);
}
