<script setup lang="ts">
import {
  BasicForm, useForm, getDictByNumber, InputSelectUser,
} from 'lyra-component-vue3';
import {
  h, onMounted, reactive, Ref, ref,
} from 'vue';
import { qualityItemGet } from '/@/views/pms/api/qualityItem';
interface UserItem {
  id: string,
  name: string
}
const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});
const loading = ref(false);
const dataDetail:any = ref({});

const selectUserData:any = ref([]);

const [register, formMethods] = useForm({
  schemas: [
    {
      field: 'number',
      component: 'Input',
      label: '质控点编号',
      required: true,
      componentProps: {
        disabled: true,
      },
    },
    {
      field: 'type',
      component: 'ApiSelect',
      label: '质控措施类型',
      required: true,
      componentProps: {
        disabled: true,
        api: () => getDictByNumber('quality_type'),
        labelField: 'description',
        valueField: 'number',
      },
    },
    {
      field: 'point',
      component: 'Input',
      label: '质控点',
      required: true,
      colProps: {
        span: 24,
      },
    },
    {
      field: 'process',
      component: 'ApiSelect',
      label: '过程',
      required: true,
      componentProps: {
        disabled: true,
        api: () => getDictByNumber('quality_process'),
        labelField: 'description',
        valueField: 'number',
      },
    },
    {
      field: 'stage',
      component: 'Input',
      label: '质控阶段',
      componentProps: {
        disabled: true,
      },
    },
    {
      field: 'activity',
      component: 'ApiSelect',
      label: '质控活动',
      required: true,
      componentProps: {
        disabled: true,
        api: () => getDictByNumber('quality_activity'),
        labelField: 'description',
        valueField: 'number',
      },
    },
    {
      field: 'deliveryFileName',
      component: 'Input',
      label: '交付文件名称',
      componentProps: {
        disabled: true,
      },
    },

    {
      field: 'scheme',
      component: 'InputTextArea',
      label: '控制方案',
      required: true,
      colProps: {
        span: 24,
      },
      componentProps: {
        rows: 4,
        showCount: true,
        maxlength: 1000,
      },
    },
    {
      field: 'affirm',
      component: 'Input',
      label: '确认人',
      colProps: {
        span: 12,
      },
      componentProps: {},
      rules: [{ required: true }],
      render: (model) => h(InputSelectUser, {
        selectUserData,
        onChange: inputSelectUserChange,
        selectUserModalProps: { selectType: 'radio' },
      }),
    },
  ],
  layout: 'vertical',
  baseColProps: {
    span: 12,
  },
});

// 选人表单组件回调
function inputSelectUserChange(users: UserItem[]) {
  selectUserData.value = users;
  formMethods.setFieldsValue({
    affirm: users[0].id,
  });
}

const getDataDetail = () => dataDetail.value;

onMounted(async () => {
  if (props.id) {
    loading.value = true;
    dataDetail.value = await qualityItemGet(props.id);
    loading.value = false;
    formMethods.setFieldsValue(dataDetail.value);
    selectUserData.value = [
      {
        id: dataDetail.value.affirm,
        name: dataDetail.value.affirmName,
      },
    ];
  }
});

defineExpose({
  formMethods,
  getDataDetail,
});
</script>

<template>
  <BasicForm @register="register" />
</template>

<style scoped lang="less">

</style>
