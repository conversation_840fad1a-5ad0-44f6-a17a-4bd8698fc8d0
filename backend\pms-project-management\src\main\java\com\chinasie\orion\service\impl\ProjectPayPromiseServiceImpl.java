package com.chinasie.orion.service.impl;


import com.chinasie.orion.domain.dto.ProjectPayPlanDTO;
import com.chinasie.orion.domain.dto.ProjectPayPromiseDTO;
import com.chinasie.orion.domain.entity.ProjectPayPlan;
import com.chinasie.orion.domain.entity.ProjectPayPromise;
import com.chinasie.orion.domain.vo.ProjectPayPromiseVO;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ProjectPayPromiseMapper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectPayPromiseService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * ProjectPayPromise 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04 00:48:05
 */
@Service
@Slf4j
public class ProjectPayPromiseServiceImpl extends  OrionBaseServiceImpl<ProjectPayPromiseMapper, ProjectPayPromise>   implements ProjectPayPromiseService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProjectPayPromiseVO detail(String id, String pageCode) throws Exception {
        ProjectPayPromise projectPayPromise =this.getById(id);
        ProjectPayPromiseVO result = BeanCopyUtils.convertTo(projectPayPromise,ProjectPayPromiseVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param projectPayPromiseDTO
     */
    @Override
    public  String create(ProjectPayPromiseDTO projectPayPromiseDTO) throws Exception {
        ProjectPayPromise projectPayPromise =BeanCopyUtils.convertTo(projectPayPromiseDTO,ProjectPayPromise::new);
        this.save(projectPayPromise);

        String rsp=projectPayPromise.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param projectPayPromiseDTO
     */
    @Override
    public Boolean edit(ProjectPayPromiseDTO projectPayPromiseDTO) throws Exception {
        ProjectPayPromise projectPayPromise =BeanCopyUtils.convertTo(projectPayPromiseDTO,ProjectPayPromise::new);

        this.updateById(projectPayPromise);

        String rsp=projectPayPromise.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectPayPromiseVO> pages( Page<ProjectPayPromiseDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProjectPayPromise> condition = new LambdaQueryWrapperX<>( ProjectPayPromise. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ProjectPayPromise::getCreateTime);
        ProjectPayPromiseDTO projectPayActualDTO =  pageRequest.getQuery();
        if(!Objects.isNull(projectPayActualDTO)){

            if(StringUtils.hasText(projectPayActualDTO.getPspid())) {
                condition.eq(ProjectPayPromise::getPspid,projectPayActualDTO.getPspid());
            }
            if(StringUtils.hasText(projectPayActualDTO.getKstar())){
                condition.eq(ProjectPayPromise::getKstar,projectPayActualDTO.getKstar());
            }

        }

        Page<ProjectPayPromise> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectPayPromise::new));

        PageResult<ProjectPayPromise> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectPayPromiseVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectPayPromiseVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectPayPromiseVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "立项金额导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectPayPromiseDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            ProjectPayPromiseExcelListener excelReadListener = new ProjectPayPromiseExcelListener();
        EasyExcel.read(inputStream,ProjectPayPromiseDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ProjectPayPromiseDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("立项金额导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ProjectPayPromise> projectPayPromisees =BeanCopyUtils.convertListTo(dtoS,ProjectPayPromise::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ProjectPayPromise-import::id", importId, projectPayPromisees, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ProjectPayPromise> projectPayPromisees = (List<ProjectPayPromise>) orionJ2CacheService.get("pmsx::ProjectPayPromise-import::id", importId);
        log.info("立项金额导入的入库数据={}", JSONUtil.toJsonStr(projectPayPromisees));

        this.saveBatch(projectPayPromisees);
        orionJ2CacheService.delete("pmsx::ProjectPayPromise-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ProjectPayPromise-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ProjectPayPromise> condition = new LambdaQueryWrapperX<>( ProjectPayPromise. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ProjectPayPromise::getCreateTime);
        List<ProjectPayPromise> projectPayPromisees =   this.list(condition);

        List<ProjectPayPromiseDTO> dtos = BeanCopyUtils.convertListTo(projectPayPromisees, ProjectPayPromiseDTO::new);

        String fileName = "立项金额数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectPayPromiseDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<ProjectPayPromiseVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class ProjectPayPromiseExcelListener extends AnalysisEventListener<ProjectPayPromiseDTO> {

        private final List<ProjectPayPromiseDTO> data = new ArrayList<>();

        @Override
        public void invoke(ProjectPayPromiseDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ProjectPayPromiseDTO> getData() {
            return data;
        }
    }


}
