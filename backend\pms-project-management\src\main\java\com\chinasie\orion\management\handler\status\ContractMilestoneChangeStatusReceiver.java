package com.chinasie.orion.management.handler.status;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.chinasie.orion.amqp.entity.ChangeStatusMessageDTO;
import com.chinasie.orion.amqp.handler.AbstractChangeStatusReceiver;


import com.chinasie.orion.constant.MarketContractMilestoneStatusEnum;
import com.chinasie.orion.constant.MarketContractStatusEnum;
import com.chinasie.orion.domain.entity.ContractMilestone;
import com.chinasie.orion.domain.entity.MarketContract;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.sdk.domain.vo.business.ClassVO;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.util.IdUtils;
import com.chinasie.orion.service.ContractMilestoneService;
import com.chinasie.orion.service.MarketContractService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

/**
 * 合同里程碑状态变更
 */
@Component
@Slf4j
public class ContractMilestoneChangeStatusReceiver extends AbstractChangeStatusReceiver {


    private static final String CURRENT_CLASS = "ContractMilestone";

    @Resource
    private ContractMilestoneService contractMilestoneService;
    @Autowired
    private MarketContractService marketContractService;

    @Resource
    private ClassRedisHelper classRedisHelper;


    @Override
    protected void process(ChangeStatusMessageDTO msg, Channel channel, Message message) {
        log.info("合同里程碑状态更改消息消费：{}", msg);
        if (ObjectUtil.isNotEmpty(msg)) {
            ClassVO classVO = classRedisHelper.classInfo(IdUtils.getCode(msg.getBusinessId()));
            if (Objects.nonNull(classVO)) {
                if (CURRENT_CLASS.equals(classVO.getClassName())) {
                    msg.setClassName(classVO.getClassName());
                    ThreadUtil.execAsync(() -> {
                        try {
                            consumerCreateMessage(msg);
                        } catch (Exception e) {
                            processError(msg, channel, message, e);
                        }
                    });
                }
            }
        }
    }

    @Override
    protected void processError(ChangeStatusMessageDTO msg, Channel channel, Message message, Exception ex) {
        log.error("合同里程碑状态更改消息消费异常，【{}】,message，【{}】,", JSONUtil.toJsonStr(msg), message, ex);
    }


    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = CURRENT_CLASS, durable = "true", autoDelete = "false"),
            exchange = @Exchange(value = "${orion.amqp.change-status-v2.exchange}", type = ExchangeTypes.DIRECT),
            key = CURRENT_CLASS
    ))
    @Override
    public void receiver(ChangeStatusMessageDTO msg, Channel channel, Message message) throws IOException {
        super.receiver(msg, channel, message);
    }

    /**
     * 消费消息
     *
     * @param message 消息
     */
    private void consumerCreateMessage(ChangeStatusMessageDTO message) throws Exception {

        LambdaUpdateWrapper<ContractMilestone> wrapper = new LambdaUpdateWrapper<>(ContractMilestone.class);
        wrapper.eq(ContractMilestone::getId, message.getBusinessId());
        wrapper.set(ContractMilestone::getStatus, message.getStatus());
        boolean result = contractMilestoneService.update(wrapper);

        if (Objects.equals(MarketContractMilestoneStatusEnum.PROGRESS.getStatus(), message.getStatus())) {
            String businessId = message.getBusinessId();
            ContractMilestone contractMilestone = contractMilestoneService.getById(businessId);
            contractMilestone.setExpectAcceptDate(contractMilestone.getPlanAcceptDate());
            LambdaQueryWrapperX<ContractMilestone> contractMilestoneLambdaQueryWrapperX1 = new LambdaQueryWrapperX<>();
            contractMilestoneLambdaQueryWrapperX1.eq(ContractMilestone::getParentId,businessId);
            List<ContractMilestone> contractMilestones = contractMilestoneService.list(contractMilestoneLambdaQueryWrapperX1);//子里程碑
            for (ContractMilestone milestone : contractMilestones) {
                milestone.setExpectAcceptDate(milestone.getPlanAcceptDate());
            }
            if (ObjectUtil.isNotEmpty(contractMilestone.getPlanAcceptDate())) {
                contractMilestone.setPlannedAcceptanceDate(contractMilestone.getPlanAcceptDate());
            } else if (ObjectUtil.isNotEmpty(contractMilestone.getExpectAcceptDate())) {
                contractMilestone.setPlannedAcceptanceDate(contractMilestone.getExpectAcceptDate());
            }
            if (ObjectUtil.isNotEmpty(contractMilestone.getMilestoneAmt()) && !(contractMilestone.getMilestoneAmt().compareTo(BigDecimal.ZERO) == 0)) {
                contractMilestone.setPlannedAcceptanceAmount(contractMilestone.getMilestoneAmt());
            } else if (ObjectUtil.isNotEmpty(contractMilestone.getExceptAcceptanceAmt()) && !(contractMilestone.getExceptAcceptanceAmt().compareTo(BigDecimal.ZERO) == 0)) {
                contractMilestone.setPlannedAcceptanceAmount(contractMilestone.getExceptAcceptanceAmt());
            }
        }
        if (Objects.equals(MarketContractMilestoneStatusEnum.APPROVAL.getStatus(), message.getStatus())) {
            String businessId = message.getBusinessId();
            ContractMilestone contractMilestone = contractMilestoneService.getById(businessId);
            String parentId = contractMilestone.getParentId();
            String contractId = contractMilestone.getContractId();
            MarketContract marketContract = marketContractService.getById(contractId);
            LambdaQueryWrapperX<ContractMilestone> contractMilestoneAllLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            contractMilestoneAllLambdaQueryWrapperX.eq(ContractMilestone::getContractId, contractId);
//            List<ContractMilestone> contractMilestoneAllList = contractMilestoneService.list(contractMilestoneAllLambdaQueryWrapperX);
//            HashSet<String> nameSet = new HashSet<>();
//            //检查里程碑是否有重名
//            if (NameDuplicateChecker(contractMilestoneAllList, nameSet)) {
//                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "该合同下存在里程碑重名，请修改后再发起流程。");
//            }

            LambdaQueryWrapperX<ContractMilestone> contractMilestoneLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            contractMilestoneLambdaQueryWrapperX.eq(ContractMilestone::getContractId, contractId);
            contractMilestoneLambdaQueryWrapperX.eq(ContractMilestone::getMilestoneType,"1");
            List<ContractMilestone> contractMilestoneList = contractMilestoneService.list(contractMilestoneLambdaQueryWrapperX);
            Integer max = findMax(contractMilestoneList);
            if (ObjectUtil.isEmpty(max)) {
                contractMilestone.setSequenceNumber(1);
            } else {
                contractMilestone.setSequenceNumber(max + 1);
            }

            //设置里程碑的编码
            StringBuffer milestoneNumber = new StringBuffer();
            String number = marketContract.getNumber();
            if (ObjectUtil.isNotEmpty(number)) {
                String substring = number.substring(9);
                milestoneNumber.append(substring).append("-").append(contractMilestone.getSequenceNumber());
                String milestoneNumberStr = milestoneNumber.toString();
                contractMilestone.setNumber(milestoneNumberStr);
            }
            contractMilestoneService.updateById(contractMilestone);
            //设置下属子里程碑的编码
            LambdaQueryWrapperX<ContractMilestone> contractMilestoneLambdaQueryWrapperX1 = new LambdaQueryWrapperX<>();
            contractMilestoneLambdaQueryWrapperX1.eq(ContractMilestone::getParentId,businessId);
            List<ContractMilestone> contractMilestones = contractMilestoneService.list(contractMilestoneLambdaQueryWrapperX1);//子里程碑
            contractMilestones.sort(Comparator.comparing(ContractMilestone::getCreateTime));//把子里程碑按照创建时间排序
            Integer maxSon = findMax(contractMilestones);
            for (ContractMilestone milestone : contractMilestones) {
               if (ObjectUtil.isEmpty(maxSon)){
                   ContractMilestone contractMilestone1 = contractMilestones.get(0);
                   contractMilestone1.setSequenceNumber(1);
               } else {
                   maxSon = findMax(contractMilestones);
                   milestone.setSequenceNumber(maxSon + 1);
               }
            }

            for (ContractMilestone milestone : contractMilestones) {
                StringBuffer stringBuffer = new StringBuffer();
                stringBuffer.append(contractMilestone.getNumber()).append("-").append(milestone.getSequenceNumber());
                String string = stringBuffer.toString();
                milestone.setNumber(string);
            }
            contractMilestoneService.updateBatchById(contractMilestones);

        }
        log.info("合同里程碑状态更改消息消费成功-参数:{}-结果:{}", JSONUtil.toJsonStr(message), result);
    }

    private Integer findMax(List<ContractMilestone> contractMilestones) {
        Optional<Integer> maxOpt = contractMilestones.stream().map(ContractMilestone::getSequenceNumber).filter(Objects::nonNull).max(Integer::compareTo);
        return maxOpt.orElse(null);
    }

    /**
     * 判断里程碑名字有没有重复的
     * @param contractMilestones
     * @param nameSet
     * @return
     */
    private boolean NameDuplicateChecker(List<ContractMilestone> contractMilestones, HashSet<String> nameSet) {
        for (ContractMilestone contractMilestone : contractMilestones) {
            if (!nameSet.add(contractMilestone.getMilestoneName())){
                return true;
            }
        }
        return false;
    }

}
