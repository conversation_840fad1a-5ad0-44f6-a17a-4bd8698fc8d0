package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.PersonRoleMaintenanceDTO;
import com.chinasie.orion.domain.vo.PersonRoleMaintenanceVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.PersonRoleMaintenanceService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * PersonRoleMaintenance 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08 17:28:59
 */
@RestController
@RequestMapping("/personRoleMaintenance")
@Api(tags = "人员角色维护表")
public class  PersonRoleMaintenanceController  {

    @Autowired
    private PersonRoleMaintenanceService personRoleMaintenanceService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "人员角色维护表", subType = "详情查询", bizNo = "{{#id}}")
    public ResponseDTO<PersonRoleMaintenanceVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        PersonRoleMaintenanceVO rsp = personRoleMaintenanceService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param personRoleMaintenanceDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#personRoleMaintenanceDTO.name}}】", type = "人员角色维护表", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody PersonRoleMaintenanceDTO personRoleMaintenanceDTO) throws Exception {
        String rsp =  personRoleMaintenanceService.create(personRoleMaintenanceDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param personRoleMaintenanceDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#personRoleMaintenanceDTO.name}}】", type = "人员角色维护表", subType = "编辑", bizNo = "{{#personRoleMaintenanceDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  PersonRoleMaintenanceDTO personRoleMaintenanceDTO) throws Exception {
        Boolean rsp = personRoleMaintenanceService.edit(personRoleMaintenanceDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "人员角色维护表", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = personRoleMaintenanceService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "人员角色维护表", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = personRoleMaintenanceService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "人员角色维护表", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<PersonRoleMaintenanceVO>> pages(@RequestBody Page<PersonRoleMaintenanceDTO> pageRequest) throws Exception {
        Page<PersonRoleMaintenanceVO> rsp =  personRoleMaintenanceService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("人员角色维护表导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "人员角色维护表", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        personRoleMaintenanceService.downloadExcelTpl(response);
    }

    @ApiOperation("人员角色维护表导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "人员角色维护表", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = personRoleMaintenanceService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("人员角色维护表导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "人员角色维护表", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  personRoleMaintenanceService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消人员角色维护表导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "人员角色维护表", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  personRoleMaintenanceService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("人员角色维护表导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "人员角色维护表", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<String> searchConditions, HttpServletResponse response) throws Exception {
        personRoleMaintenanceService.exportByExcel(searchConditions, response);
    }

    /*@ApiOperation("人员角色维护搜索")
    @PostMapping(value = "/personnelMaintenanceSearch/{serchValue}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "人员角色维护表", subType = "导出数据", bizNo = "")
    public List<PersonRoleMaintenanceVO> personnelMaintenanceSearch(@PathVariable("serchValue") String serchValue) throws Exception {
        List<PersonRoleMaintenanceVO> personRoleMaintenanceVOList = personRoleMaintenanceService.personnelMaintenanceSearch(serchValue);
        return personRoleMaintenanceVOList;
    }*/

    @ApiOperation("专业所查询")
    @PostMapping(value = "/searchStationName/{id}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询数据", type = "人员角色维护表", subType = "专业所查询", bizNo = "")
    public ResponseDTO<List> searchStationName(@PathVariable("id") String id){
        List<PersonRoleMaintenanceVO> rsp = personRoleMaintenanceService.searchStationName(id);
        return new ResponseDTO<>(rsp);
    }
}
