package com.chinasie.orion.exp.marketManagement;

import com.chinasie.orion.base.api.domain.entity.DeptDO;
import com.chinasie.orion.base.api.domain.entity.DeptLeaderDO;
import com.chinasie.orion.base.api.domain.entity.RoleDO;
import com.chinasie.orion.base.api.domain.entity.RoleUserDO;
import com.chinasie.orion.base.api.repository.DeptDOMapper;
import com.chinasie.orion.base.api.repository.DeptLeaderDORepository;
import com.chinasie.orion.base.api.repository.RoleDOMapper;
import com.chinasie.orion.base.api.repository.RoleUserDOMapper;
import com.chinasie.orion.constant.RoleColeConstant;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.permission.core.core.type.ValueExpType;
import com.chinasie.orion.permission.core.exp.IExp;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * Keafmd
 *
 * @ClassName: CorporateHierarchyExp
 * @Description: 公司层级的的数据过滤规则：1.计划经营部（组织代码：PBD）的「领导组」成员；
 * 2.角色「公司级商务接口人（Business_03)」下的用户；
 * @author: zhangqianyang
 * @date: 2024/8/10 16:43
 * @Blog: https://keafmd.blog.csdn.net/
 */
@Component
@Slf4j
public class RequirementCorporateHierarchyExp implements IExp {

    @Autowired
    private DeptLeaderDORepository deptLeaderDORepository;

    @Autowired
    private DeptDOMapper deptDOMapper;

    @Autowired
    private RoleDOMapper roleDOMapper;

    @Autowired
    private RoleUserDOMapper roleUserDOMapper;

    @Override
    public ValueExpType group() {
        return ValueExpType.CUSTOM;
    }

    @Override
    public String expName() {
        return "市场经营的公司层级的数据分层过滤";
    }

    /**
     * 1.计划经营部（组织代码：PBD）的「领导组」成员；
     * 2.角色「公司级商务接口人
     *
     * @param s
     * @return
     */
    @Override
    public List<String> exp(String s) {
        log.info("开始判断用户是否是市场经营的公司层级的领导");
        String currentUserId = CurrentUserHelper.getCurrentUserId();

        //获取计划经营部门
        LambdaQueryWrapperX<DeptDO> deptQueryWrapperx = new LambdaQueryWrapperX(DeptDO.class);
        deptQueryWrapperx.eq(DeptDO::getDeptCode, "PBD");
        DeptDO deptDO = deptDOMapper.selectOne(deptQueryWrapperx);
        if (Objects.isNull(deptDO)) {
            log.info("计划经营部门不存在");
            //查看是否是角色「公司级商务接口人
            RoleDO roleDO = roleDOMapper.selectOne(RoleDO::getCode, RoleColeConstant.COMPANY_BUSINESS_INTERFACE_ROLE_CODE);
            if (Objects.isNull(roleDO)) {
                log.info("公司级商务接口人的角色不存在");
                return Arrays.asList("");
            }

            LambdaQueryWrapperX<RoleUserDO> roleQueryWrapper = new LambdaQueryWrapperX<>();
            roleQueryWrapper.eq(RoleUserDO::getUserId, currentUserId);
            roleQueryWrapper.eq(RoleUserDO::getRoleId, roleDO.getId());
            if (roleUserDOMapper.exists(roleQueryWrapper)) {
                log.info("是公司级商务接口人角色");
                return Arrays.asList("RequirementMangement");
            }
        }else{
            //判断是否是计划经营部的领导组成员
            LambdaQueryWrapperX<DeptLeaderDO> queryWrapperX = new LambdaQueryWrapperX(DeptLeaderDO.class);
            queryWrapperX.eq(DeptLeaderDO::getType, "group");
            queryWrapperX.eq(DeptLeaderDO::getDeptId, deptDO.getId());
            queryWrapperX.eq(DeptLeaderDO::getUserId, currentUserId);

            DeptLeaderDO deptLeaderDO = deptLeaderDORepository.selectOne(queryWrapperX);
            if (Objects.nonNull(deptLeaderDO)) {
                log.info("是计划经营部的领导组的成员");
                return Arrays.asList("RequirementMangement");
            }
        }
        return Arrays.asList("");
    }

    @Override
    public Boolean apply() {
        return true;
    }
}
