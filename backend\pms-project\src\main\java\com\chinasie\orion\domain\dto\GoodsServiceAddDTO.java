package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "FromIdsRelationToIdDTO对象", description = "FromIdsRelationToIdDTO对象")
public class GoodsServiceAddDTO {

    @ApiModelProperty(value = "id")
    @NotNull(message = "id不能为空")
    private String id;

    /**
     * 需求数量
     */
    @ApiModelProperty(value = "入库数量")
    @NotNull(message = "入库数量不能为空")
    private BigDecimal storeAmount;

    /**
     * 描述/备注
     */
    @ApiModelProperty(value = "描述/备注")
    private String remark;

    /**
     * 入库日期
     */
    @ApiModelProperty(value = "入库日期")
    @NotNull(message = "入库数量不能为空")
    private Date storeTime;
}
