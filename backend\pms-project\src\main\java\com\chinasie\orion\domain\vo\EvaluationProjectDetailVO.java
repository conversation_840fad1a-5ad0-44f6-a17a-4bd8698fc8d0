package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "EvaluationProjectDetailVO对象", description = "项目评价详情")
@Data
public class EvaluationProjectDetailVO extends ObjectVO implements Serializable {

    /**
     * 通用评价详情
     */
    @ApiModelProperty(value = "通用评价详情")
    private EvaluationCommonVO evaluationCommonVO;
    /**
     * 项目后评价详情
     */
    @ApiModelProperty(value = "项目后评价详情")
    private EvaluationAfterVO evaluationAfterVO;
    /**
     * 项目履约评价详情
     */
    @ApiModelProperty(value = "项目履约评价详情")
    private EvaluationPerformanceVO evaluationPerformanceVO;
}
