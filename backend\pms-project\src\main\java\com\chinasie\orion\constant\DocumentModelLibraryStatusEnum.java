package com.chinasie.orion.constant;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/10/16:05
 * @description:
 */
public enum DocumentModelLibraryStatusEnum {

    EFFECT(130, "已启用"),
    UN_EFFECT(101, "未启用");
    private Integer status;
    private String desc;


    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    DocumentModelLibraryStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
