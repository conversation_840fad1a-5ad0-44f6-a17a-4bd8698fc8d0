package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.Boolean;

/**
 * ProjectIncome VO对象
 *
 * <AUTHOR>
 * @since 2024-05-15 09:24:02
 */
@ApiModel(value = "ProjectIncomeVO对象", description = "收益管理")
@Data
public class ProjectIncomeVO extends ObjectVO implements Serializable{

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;


    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    private String productId;


    /**
     * 产品编码
     */
    @ApiModelProperty(value = "产品编码")
    private String productNumber;

    @ApiModelProperty(value = "需求评审产品编码")
    private String oaNumber;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;



    /**
     * 销售是否结束
     */
    @ApiModelProperty(value = "销售是否结束")
    private Boolean saleOver;


    /**
     * 是否已签单
     */
    @ApiModelProperty(value = "是否已签单")
    private Boolean signBill;


    /**
     * 预期合同年份
     */
    @ApiModelProperty(value = "预期合同年份")
    private Date expectedContractYear;


    /**
     * 原预期产品单价
     */
    @ApiModelProperty(value = "原预期产品单价")
    private BigDecimal expectedProductPrice;


    /**
     * 原预期总产出
     */
    @ApiModelProperty(value = "原预期总产出")
    private BigDecimal origExpectedOutcome;


    /**
     * 现预期总产出
     */
    @ApiModelProperty(value = "现预期总产出")
    private BigDecimal expectedOutcomes;


    /**
     * 已签订合同金额
     */
    @ApiModelProperty(value = "已签订合同金额")
    private BigDecimal contractAmount;


    /**
     * 预期差异比
     */
    @ApiModelProperty(value = "预期差异比")
    private String expectedDiffRate = "0%";


    /**
     * 完成百分比
     */
    @ApiModelProperty(value = "完成百分比")
    private String completeRate = "0%";


    /**
     * 收益策划id
     */
    @ApiModelProperty(value = "收益策划id")
    private String approvalIncomeId;

    @ApiModelProperty(value = "产品型号")
    private String productModelNumber;

    private Boolean edit;


    @ApiModelProperty(value = "军/民品分类名称")
    private String militaryCivilianName;

    @ApiModelProperty(value = "产品二级分类名称 ")
    private String productSecondClassifyName;

}
