package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ApiModel(value = "CostShareDTO对象", description = "成本分摊")
@Data
@ExcelIgnoreUnannotated
public class ImportExcelTempleteDTO  implements Serializable {
    @ApiModelProperty("excel 序号")
    @ExcelProperty(value = "excel序号", index = 0)
    private String order;

    @ApiModelProperty("校验错误提示")
    @ExcelProperty(value = "校验错误提示", index = 1)
    private String errorNotes;

}
