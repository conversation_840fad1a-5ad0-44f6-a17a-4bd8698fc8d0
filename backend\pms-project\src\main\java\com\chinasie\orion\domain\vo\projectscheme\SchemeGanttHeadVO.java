package com.chinasie.orion.domain.vo.projectscheme;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/8/13
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "SchemeGanttHeadVO", description = "甘特图VO")
@Data
public class SchemeGanttHeadVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 未开始
     */
    private Integer noBegin;
    /**
     * 进行中
     */
    private Integer beginning;
    /**
     * 已完成
     */
    private Integer complete;
    /**
     * 已逾期
     */
    private Integer overdue;
    /**
     * 甘特图详情数据
     */
    private List<SchemeGanttVO> schemeGanttVOList;
}
