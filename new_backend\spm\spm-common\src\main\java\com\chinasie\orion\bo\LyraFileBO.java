package com.chinasie.orion.bo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileTreeVO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.file.api.service.LyraFileApiService;
import com.chinasie.orion.page.PageRequest;
import com.chinasie.orion.page.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/2/22 16:01
 * @description:
 */
@Slf4j
@Component
public class LyraFileBO {

    @Resource
    private LyraFileApiService lyraFileApiService;


    public List<FileVO> getFileByIds(List<String> fileIdList){
        try {
            List<FileVO> result = lyraFileApiService.getFileByIds(fileIdList);
            return result;
        } catch (Exception e) {
            log.error("获取文件列表失败", e);
            return new ArrayList<>();
        }
    }

    public List<FileVO> getFileDtoListByDataIds(List<String> dataIdList) {
        try {
            List<FileVO> result = lyraFileApiService.listMaxFileByDataIds(dataIdList);
            return result;
        } catch (Exception e) {
            log.error("获取文件列表失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 复制文件 將某個對象的文件复制到新的對象中
     *
     * @param newId
     * @param oldId
     * @return
     */
    public Boolean copyFileByOldIdToNewId(String newId, String oldId) {
        try {
            Boolean isSuccess = lyraFileApiService.copyFileByOldIdToNewId(newId, oldId);
            return isSuccess;
        } catch (Exception e) {
            log.error("复制文件失败", e);
            return false;
        }
    }

    /**
     * 返回列表
     *
     * @param dataId
     * @return
     * @throws Exception
     */
    public List<FileVO> getFilesByDataId(String dataId) {
        try {
            List<FileVO> result = lyraFileApiService.getFilesByDataId(dataId);
            return result;
        } catch (Exception e) {
            log.error("获取文件列表失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 返回树形结构
     *
     * @param dataId
     * @return
     * @throws Exception
     */
    public List<FileTreeVO> getFilesTreeByDataId(String dataId) throws Exception {
        List<FileTreeVO> result = lyraFileApiService.getFilesTreeByDataId(dataId);
        if (CollectionUtils.isEmpty(result)) {
            return new ArrayList<>();
        }
        return result;
    }

    public String addFile(FileDTO fileDto) {
        try {
            String result = lyraFileApiService.addFile(fileDto);
            return result;
        } catch (Exception e) {
            log.error("添加文件失败", e);
            return null;
        }
    }

    public List<String> addBatch(List<FileDTO> fileDtoList) {
        if (CollectionUtils.isEmpty(fileDtoList)) {
            return new ArrayList<>();
        }
        try {
            List<String> result = lyraFileApiService.addBatch(fileDtoList);
            return result;
        } catch (Exception e) {
            log.error("批量添加文件失败", e);
            return new ArrayList<>();
        }
    }

    public Boolean updateBatchFile(List<FileDTO> fileDtoList) throws Exception {
        Boolean isSuccess = lyraFileApiService.updateBatch(fileDtoList);
        return isSuccess;
    }

    public Boolean updateFile(FileDTO fileDto) throws Exception {
        Boolean responseDTO = lyraFileApiService.update(fileDto);
        return responseDTO;
    }

    @Deprecated
    public PageResult<FileDTO> filePage(PageRequest<FileDTO> pageRequest) throws Exception {
        // 不知道这段代码是否有用,RES已经没有这个接口了先返回NULL
//        ResponseDTO<PageResult<FileDTO>> responseDTO = fileService.filePage(pageRequest);
//        if (ResponseUtils.fail(responseDTO)) {
//            throw new SPMException(SPMErrorCode.KMS_USER_OTHER_SERVER_ERROR);
//        }
//        PageResult<FileDTO> result = responseDTO.getResult();
//        if (Objects.isNull(result)) {
        return new PageResult<>();
//        }
//        return result;
    }

    public boolean deleteFileByIds(List<String> fileIdList) {
        try {
            Boolean isSuccess = lyraFileApiService.deleteFileByIds(fileIdList);
            return isSuccess;
        } catch (Exception e) {
            log.error("删除文件失败", e);
            return false;
        }
    }

    public boolean deleteFileByIds(List<String> fileIdList,List<FileDTO> attachments) {
        try {
            List<String> saveIds = attachments.stream().filter(item-> StrUtil.isNotBlank(item.getId())).map(FileDTO::getId).collect(Collectors.toList());
            if(CollUtil.isEmpty(saveIds)){
                Boolean isSuccess =  deleteFileByIds(fileIdList);
                return isSuccess;
            }
                // 将列表转换为集合
                Set<String> set1 = new HashSet<>(fileIdList);
                Set<String> set2 = new HashSet<>(saveIds);

                // 找出只存在于list1中的字符串
                Set<String> uniqueToList1 = new HashSet<>(set1);
                uniqueToList1.removeAll(set2);
                Boolean isSuccess = true;
                if (CollUtil.isNotEmpty(uniqueToList1)) {
                    List<String> ids = new ArrayList<>(uniqueToList1);
                    isSuccess = lyraFileApiService.deleteFileByIds(ids);
                }
                return isSuccess;

        } catch (Exception e) {
            log.error("删除文件失败", e);
            return false;
        }
    }

    public boolean deleteFileById(String fileId) {
        try {
            Boolean isSuccess = lyraFileApiService.deleteFileById(fileId);
            return isSuccess;
        } catch (Exception e) {
            log.error("删除文件失败", e);
            return false;
        }
    }

    public List<FileDTO> listMaxNewFile(String dataId, String dataType) {
        List<FileDTO> fileDTOS = lyraFileApiService.listMaxNewFile(dataId, dataType);
        return fileDTOS;
    }

}
