package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * ProjectSchemeMilestoneNode Entity对象
 *
 * <AUTHOR>
 * @since 2023-06-05 10:30:49
 */
@ApiModel(value = "ProjectSchemeMilestoneNodeDTO对象", description = "项目计划里程碑节点")
@Data
public class ProjectSchemeMilestoneNodeDTO extends ObjectDTO implements Serializable {

    /**
     * 父级Id
     */
    @ApiModelProperty(value = "父级Id")
    private String parentId;


    /**
     * 模版ID
     */
    @ApiModelProperty(value = "模版ID")
    @NotBlank(message = "模版ID不能为空")
    private String templateId;

    /**
     * 节点名称
     */
    @ApiModelProperty(value = "节点名称")
    //@NotBlank(message = "节点名称不能为空")
    @ExcelProperty(value = "里程碑节点名称*")
    private String nodeName;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @ExcelProperty(value = "描述*")
    private String description;

    /**
     * 节点顺序
     */
    @ApiModelProperty(value = "节点顺序")
    @ExcelProperty(value = "序号*")
    private Long sort;

    @ApiModelProperty(value = "计划属性")
    private String planActive;


    /**
     * 层级
     */
    @ApiModelProperty(value = "层级")
    private String nodeChain;


    /**
     * 计划类型
     */
    @ApiModelProperty(value = "计划类型: 里程碑：milestone，计划：plan")
    private String nodeType;

    /**
     * 责任部门
     */
    @ApiModelProperty(value = "责任部门")
    private String rspDeptId;

    /**
     * 计划工期
     */
    @ApiModelProperty(value = "计划工期")
    private Integer durationDays;

    /**
     * 项目启动后n天开始
     */
    @ApiModelProperty(value = "项目启动后n天开始")
    private Integer delayDays;


    /**
     * 是否关联流程
     */
    @ApiModelProperty(value = "是否关联流程")
    private Boolean processFlag;

}
