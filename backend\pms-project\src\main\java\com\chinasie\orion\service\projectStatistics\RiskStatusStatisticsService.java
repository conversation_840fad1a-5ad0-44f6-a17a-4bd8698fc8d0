package com.chinasie.orion.service.projectStatistics;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.projectStatistics.RiskStatusStatisticsDTO;
import com.chinasie.orion.domain.entity.projectStatistics.RiskStatusStatistics;
import com.chinasie.orion.domain.vo.projectStatistics.RiskStatusStatisticsVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

/**
 * <p>
 * RiskStatusStatistics 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-21 13:45:41
 */
public interface RiskStatusStatisticsService  extends OrionBaseService<RiskStatusStatistics>{
    /**
     *  详情
     *
     * * @param id
     */
    RiskStatusStatisticsVO detail(String id)  throws Exception;

    /**
     *  新增
     *
     * * @param riskStatusStatisticsDTO
     */
    RiskStatusStatisticsVO create(RiskStatusStatisticsDTO riskStatusStatisticsDTO)  throws Exception;

    /**
     *  编辑
     *
     * * @param riskStatusStatisticsDTO
     */
    Boolean edit(RiskStatusStatisticsDTO riskStatusStatisticsDTO) throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     */
    Page<RiskStatusStatisticsVO> pages(Page<RiskStatusStatisticsDTO> pageRequest) throws Exception;

}

