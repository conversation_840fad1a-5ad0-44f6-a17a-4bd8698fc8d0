package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chinasie.orion.domain.entity.ContractMilestone;
import com.chinasie.orion.domain.entity.MarketContract;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.vo.ContractMilestoneApiVO;
import com.chinasie.orion.management.domain.entity.ProjectInitiation;
import com.chinasie.orion.management.domain.entity.ProjectInitiationWBS;
import com.chinasie.orion.management.service.ProjectInitiationService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.service.ContractMilestoneService;
import com.chinasie.orion.service.MarketContractService;
import com.chinasie.orion.service.ProjectContractMilestoneService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/8/19
 */
@Service
@Slf4j
@AllArgsConstructor
public class ProjectContractMilestoneServiceImpl implements ProjectContractMilestoneService {

    @Autowired
    private ContractMilestoneService contractMilestoneService;
    @Autowired
    private ProjectInitiationService projectInitiationService;
    @Autowired
    private MarketContractService marketContractService;
    @Autowired
    private DictRedisHelper dictRedisHelper;

    @Override
    public List<ContractMilestoneApiVO> findnoChangePlanData() {
        List<ContractMilestoneApiVO> result = Lists.newArrayList();
        LambdaQueryWrapperX<ContractMilestone> lambdaQueryWrapper = new LambdaQueryWrapperX<>();
        lambdaQueryWrapper.select("t.id," + "t.class_name," + "t.creator_id," + "t.modify_time," +
                "t.owner_id," + "t.create_time," + "t.modify_id," + "t.remark," + "t.platform_id," + "t.org_id," +
                "t.`status`," + "t.logic_status," + "t.milestone_name," + "t.milestone_type," + "t.parent_id," +
                "t.tech_rsp_user," + "t.bus_rsp_user," + "t.plan_accept_date," + "t.cost_bus_type," + "t.milestone_amt," +
                "t.undert_dept," + "t.number," + "t.rsp_user," + "t.actual_accept_date," + "t.actual_milestone_amt," +
                "t.total_accept_rate," + "t.contract_id," + "t.tax_rate," + "t.expect_accept_date," + "t.description," +
                "t.cust_person_id," + "t1.number as contractNumber,"+ "t1.frame_contract_id as frameContractId," + "t.is_plan");
        lambdaQueryWrapper.leftJoin(MarketContract.class, MarketContract::getId, ContractMilestone::getContractId);
//        lambdaQueryWrapper.eq(MarketContract::getContractType, "frameContract");
        lambdaQueryWrapper.and(wrapper -> wrapper.eq(ContractMilestone::getIsPlan, 0)
                .or().isNull(ContractMilestone::getIsPlan));
        lambdaQueryWrapper.eq(ContractMilestone::getStatus, 110);
//        lambdaQueryWrapper.and(wrapper -> wrapper.eq(MarketContract::getFrameContractId, "")
//                .or().isNull(MarketContract::getFrameContractId));
        lambdaQueryWrapper.eq(MarketContract::getLogicStatus, 1);
        List<ContractMilestone> list = contractMilestoneService.selectJoinList(ContractMilestone.class, lambdaQueryWrapper);
        log.error("--------------合同里程碑生成计划,里程碑数据{}------------------", list.size());
        if (CollectionUtil.isNotEmpty(list)) {
            List<String> frameContractIds = list.stream().map(ContractMilestone::getFrameContractId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            //主合同
            List<MarketContract> marketContracts = marketContractService.list(Wrappers.<MarketContract>lambdaQuery()
                    .in(MarketContract::getId, frameContractIds));

            //替换子合同的合同号
            if(CollectionUtil.isNotEmpty(marketContracts)){
                Map<String,List<MarketContract>> listMap = marketContracts.stream().collect(Collectors.groupingBy(MarketContract::getId));
                for(ContractMilestone childMilestone:list){
                    if(StringUtils.isNotBlank(childMilestone.getFrameContractId())){
                        childMilestone.setContractNumber(listMap.get(childMilestone.getFrameContractId()).get(0).getNumber());
                    }
                }
            }
            List<ContractMilestoneApiVO> contractMilestoneVOS = BeanCopyUtils.convertListTo(list, ContractMilestoneApiVO::new);
            List<String> contractNumbers = contractMilestoneVOS.stream().map(ContractMilestoneApiVO::getContractNumber)
                    .distinct().collect(Collectors.toList());
            Map<String, List<ContractMilestoneApiVO>> map = contractMilestoneVOS.stream().collect(
                    Collectors.groupingBy(ContractMilestoneApiVO::getContractNumber));

            //已经生成项目的立项数据
            LambdaQueryWrapperX<ProjectInitiation> queryWrapper =  new LambdaQueryWrapperX();
            queryWrapper.select("t.*,GROUP_CONCAT( t1.wbs_element ) as wbsElement");
            queryWrapper.leftJoin(ProjectInitiationWBS.class,ProjectInitiationWBS::getProjectNumber,ProjectInitiation::getProjectNumber);
            queryWrapper.groupBy(ProjectInitiation::getProjectNumber);
            queryWrapper.in(ProjectInitiation::getContractNumbers, contractNumbers);
            queryWrapper.isNotNull(ProjectInitiation::getProjectId);
            queryWrapper.ne(ProjectInitiation::getProjectId, "");
            List<ProjectInitiation> projectInitiations = projectInitiationService.list(queryWrapper
            );
            if(CollectionUtil.isNotEmpty(projectInitiations)){
                List<DictValueVO> dictValues = dictRedisHelper.getDictListByCode("cos_business_type");
                List<String> dictValueList = dictValues.stream().map(DictValueVO::getNumber).collect(Collectors.toList());
                projectInitiations.forEach(projectInitiation -> {
                    if(StringUtils.isNotEmpty(projectInitiation.getWbsElement())){
                        String[] wbsElements = projectInitiation.getWbsElement().split(",");
                        Map<String,String> wbsElementMap = Maps.newHashMap();
                        for(String wbsElement :wbsElements){
                            String[] wbsElementArray = wbsElement.split("\\.");
                            if(wbsElementArray.length > 1){
                                for(String dict:dictValueList){
                                    if(dict.toLowerCase().startsWith(wbsElementArray[1].toLowerCase())){
                                        wbsElementMap.put(dict, dict);
                                        break;
                                    }
                                }
                                wbsElementMap.put(wbsElementArray[1], wbsElementArray[1]);
                            }
                        }
                        List<ContractMilestoneApiVO> contractMilestoneVoS1 = map.get(projectInitiation.getContractNumbers());

                        if(CollectionUtil.isNotEmpty(contractMilestoneVoS1)){
                            for(ContractMilestoneApiVO contractMilestoneVO:contractMilestoneVoS1){
                                //todo 根据业务类型区分
                                if(wbsElementMap.containsKey(contractMilestoneVO.getCostBusType())){
                                    ContractMilestoneApiVO vo = BeanCopyUtils.convertTo(contractMilestoneVO, ContractMilestoneApiVO::new);
                                    vo.setProjectId(projectInitiation.getProjectId());
                                    result.add(vo);
                                }
                            }
                        }
                    }

                });
            }
        }
        return result;
    }

    @Override
    public void updateContractMilestoneStatus(List<ContractMilestoneApiVO> updateContractMilestoneVOS) {
        List<ContractMilestone> list = Lists.newArrayList();
        for(ContractMilestoneApiVO updateContractMilestoneVO:updateContractMilestoneVOS){
            ContractMilestone consentMilestone = new ContractMilestone();
            consentMilestone.setId(updateContractMilestoneVO.getId());
            consentMilestone.setIsPlan(1);
            list.add(consentMilestone);
        }
        contractMilestoneService.updateBatchById(list);
    }

    @Override
    public Integer findPendingCount(String year, Integer type) {
        LambdaQueryWrapper<ContractMilestone> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.apply("YEAR(create_time) = {0}", year);
        queryWrapper.eq(ContractMilestone::getIsPlan, 0);
        if(type == 1){
            queryWrapper.eq(ContractMilestone::getTechRspUser, CurrentUserHelper.getCurrentUserId());
        }
        List<Integer> statusList = Lists.newArrayList();
        statusList.add(110);
        statusList.add(130);
        queryWrapper.in(ContractMilestone::getStatus, statusList);
        return (int)contractMilestoneService.count(queryWrapper);
    }

    @Override
    public JSONObject findCompletionRate(String year, Integer type) {
        LambdaQueryWrapper<ContractMilestone> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.apply("YEAR(create_time) = {0}", year);
        queryWrapper.eq(ContractMilestone::getIsPlan, 1);
        if(type == 1){
            queryWrapper.eq(ContractMilestone::getTechRspUser, CurrentUserHelper.getCurrentUserId());
        }
        List<ContractMilestone> list = contractMilestoneService.list(queryWrapper);
        //完成个数
        BigDecimal completedCount = BigDecimal.ZERO;

        JSONObject data = new JSONObject();

        if(CollectionUtil.isNotEmpty(list)){
            for(ContractMilestone contractMilestone:list){
               if(contractMilestone.getMilestoneAmt() != null && contractMilestone.getActualMilestoneAmt() != null
                       && contractMilestone.getMilestoneAmt().compareTo(contractMilestone.getActualMilestoneAmt()) == 0){
                   completedCount = completedCount.add(BigDecimal.ONE);
               }
            }
            //计划总金额
            BigDecimal planTotalAmount = list.stream().map(ContractMilestone::getMilestoneAmt).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            //实际总金额
            BigDecimal actualTotalAmount = list.stream().map(ContractMilestone::getActualMilestoneAmt).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            data.put("moneyCompeteRate", actualTotalAmount.divide(planTotalAmount,2, RoundingMode.HALF_UP));
            data.put("countCompeteRate",completedCount.divide(BigDecimal.valueOf(list.size()),2, RoundingMode.HALF_UP));
        }else{
            data.put("moneyCompeteRate",100);
            data.put("countCompeteRate",100);
        }

        return data;
    }
}
