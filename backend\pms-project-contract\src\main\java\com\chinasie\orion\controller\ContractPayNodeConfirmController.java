package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ContractPayNodeConfirmAuditDTO;
import com.chinasie.orion.domain.dto.ContractPayNodeConfirmDTO;
import com.chinasie.orion.domain.vo.ContractPayNodeConfirmListVO;
import com.chinasie.orion.domain.vo.ContractPayNodeConfirmSelectVO;
import com.chinasie.orion.domain.vo.ContractPayNodeConfirmVO;
import com.chinasie.orion.domain.vo.ContractPayNodeDetailVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ContractPayNodeConfirmService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * ContractPayNodeConfirm 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26 21:44:48
 */
@RestController
@RequestMapping("/contractPayNodeConfirm")
@Api(tags = "项目合同支付节点确认")
public class ContractPayNodeConfirmController {

    @Autowired
    private ContractPayNodeConfirmService contractPayNodeConfirmService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(
            success = "【{USER{#logUserId}}】查看合同支付节点确认详情，业务编号：{#id}",
            type = "ContractPayNodeConfirm",
            subType = "详情",
            bizNo = "{#id}"
    )
    public ResponseDTO<ContractPayNodeDetailVO> detail(@PathVariable(value = "id") String id) throws Exception {
        ContractPayNodeDetailVO rsp = contractPayNodeConfirmService.detail(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param contractPayNodeConfirmDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】新增合同支付节点确认",
            type = "ContractPayNodeConfirm",
            subType = "新增",
            bizNo = ""
    )
    public ResponseDTO<ContractPayNodeConfirmVO> create(@RequestBody @Validated ContractPayNodeConfirmDTO contractPayNodeConfirmDTO) throws Exception {
        ContractPayNodeConfirmVO rsp = contractPayNodeConfirmService.create(contractPayNodeConfirmDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 提交
     *
     * @param contractPayNodeConfirmDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "提交")
    @RequestMapping(value = "/submit", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】提交合同支付节点确认，业务编号：{#contractPayNodeConfirmDTO.id}",
            type = "ContractPayNodeConfirm",
            subType = "提交",
            bizNo = "{#contractPayNodeConfirmDTO.id}"
    )
    public ResponseDTO<Boolean> submit(@RequestBody @Validated ContractPayNodeConfirmDTO contractPayNodeConfirmDTO) throws Exception {
        Boolean rsp = contractPayNodeConfirmService.submit(contractPayNodeConfirmDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 编辑
     *
     * @param contractPayNodeConfirmDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】编辑合同支付节点确认，业务编号：{#contractPayNodeConfirmDTO.id}",
            type = "ContractPayNodeConfirm",
            subType = "编辑",
            bizNo = "{#contractPayNodeConfirmDTO.id}"
    )
    public ResponseDTO<Boolean> edit(@RequestBody @Validated ContractPayNodeConfirmDTO contractPayNodeConfirmDTO) throws Exception {
        Boolean rsp = contractPayNodeConfirmService.edit(contractPayNodeConfirmDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】删除合同支付节点确认，业务编号：{ID_LIST{#ids}}",
            type = "ContractPayNodeConfirm",
            subType = "删除",
            bizNo = "{ID_LIST{#ids}}"
    )
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = contractPayNodeConfirmService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @LogRecord(
            success = "【{USER{#logUserId}}】执行合同支付节点确认分页查询",
            type = "ContractPayNodeConfirm",
            subType = "分页查询",
            bizNo = ""  // 分页无具体业务实体编号
    )
    public ResponseDTO<Page<ContractPayNodeConfirmListVO>> pages(@RequestBody Page<ContractPayNodeConfirmDTO> pageRequest) throws Exception {
        Page<ContractPayNodeConfirmListVO> rsp = contractPayNodeConfirmService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 选择支付节点列表
     *
     * @param contractId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "选择支付节点列表(新增)")
    @RequestMapping(value = "/payNode/list/contractId/{contractId}", method = RequestMethod.GET)
    @LogRecord(
            success = "【{USER{#logUserId}}】获取合同编号为{#contractId}的支付节点列表",
            type = "ContractPayNodeConfirm",
            subType = "支付节点列表(新增)",
            bizNo = "{#contractId}"
    )
    public ResponseDTO<List<ContractPayNodeConfirmSelectVO>> payNodeListByContractId(@PathVariable(value = "contractId") String contractId) throws Exception {
        List<ContractPayNodeConfirmSelectVO> rsp = contractPayNodeConfirmService.payNodeListByContractId(contractId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 选择支付状态确认节点列表
     *
     * @param contractId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "选择支付状态确认节点列表")
    @RequestMapping(value = "/payNodes/statusConfirm/list/contractId/{contractId}", method = RequestMethod.GET)
    @LogRecord(
            success = "【{USER{#logUserId}}】查看合同编号为{#contractId}的支付状态确认节点列表",
            type = "ContractPayNodeConfirm",
            subType = "支付状态确认节点列表",
            bizNo = "{#contractId}"
    )
    public ResponseDTO<List<ContractPayNodeConfirmSelectVO>> payNodeStatusConfirmListByContractId(@PathVariable(value = "contractId") String contractId) throws Exception {
        List<ContractPayNodeConfirmSelectVO> rsp = contractPayNodeConfirmService.payNodeStatusConfirmListByContractId(contractId);
        return new ResponseDTO<>(rsp);
    }
    /**
     * 选择支付节点列表
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "选择支付节点列表(编辑)")
    @RequestMapping(value = "/payNodes/list/id/{id}", method = RequestMethod.GET)
    @LogRecord(
            success = "【{USER{#logUserId}}】根据业务编号{#id}查看支付节点列表",
            type = "ContractPayNodeConfirm",
            subType = "支付节点列表(编辑)",
            bizNo = "{#id}"
    )
    public ResponseDTO<List<ContractPayNodeConfirmSelectVO>> payNodeListById(@PathVariable(value = "id") String id) throws Exception {
        List<ContractPayNodeConfirmSelectVO> rsp = contractPayNodeConfirmService.payNodeListById(id);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("同意")
    @PostMapping("/agree")
    @LogRecord(
            success = "【{USER{#logUserId}}】同意合同支付节点确认，业务编号：{#confirmAuditDTO.id}",
            type = "ContractPayNodeConfirm",
            subType = "同意",
            bizNo = "{#confirmAuditDTO.id}"
    )
    public ResponseDTO<Boolean> agree(@RequestBody @Validated ContractPayNodeConfirmAuditDTO confirmAuditDTO) throws Exception {
        String id = confirmAuditDTO.getId();
        if (!StringUtils.hasText(id)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "id不能为空!");
        }
        Boolean rsp = contractPayNodeConfirmService.agree(confirmAuditDTO);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("驳回")
    @PostMapping("/reject")
    @LogRecord(
            success = "【{USER{#logUserId}}】驳回合同支付节点确认，业务编号：{#confirmAuditDTO.id}",
            type = "ContractPayNodeConfirm",
            subType = "驳回",
            bizNo = "{#confirmAuditDTO.id}"
    )
    public ResponseDTO<Boolean> reject(@RequestBody @Validated ContractPayNodeConfirmAuditDTO confirmAuditDTO) throws Exception {
        String id = confirmAuditDTO.getId();
        if (!StringUtils.hasText(id)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "id不能为空!");
        }
        Boolean rsp = contractPayNodeConfirmService.reject(confirmAuditDTO);
        return new ResponseDTO<>(rsp);
    }
}
