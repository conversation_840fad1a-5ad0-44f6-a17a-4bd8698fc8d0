package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ProductionIndex VO对象
 *
 * <AUTHOR>
 * @since 2024-06-18 17:49:18
 */
@ApiModel(value = "ProductionIndexVO对象", description = "生产看板指标维护")
@Data
public class ProductionIndexVO extends ObjectVO implements Serializable {

    /**
     * 指标编码
     */
    @ApiModelProperty(value = "指标编码")
    private String indexNumber;


    /**
     * 考核类型
     */
    @ApiModelProperty(value = "考核类型")
    private String indexType;


    /**
     * 目标值
     */
    @ApiModelProperty(value = "目标值")
    private String indexTarget;


    /**
     * 实际值
     */
    @ApiModelProperty(value = "实际值")
    private String indexActual;


    /**
     * 状态灯
     */
    @ApiModelProperty(value = "状态灯")
    private String indexStatus;


}
