import Api from '/@/api';
/**
 * 新增
 * @param params 参数
 */
export const add = (params) => new Api('/pms/reviewLibrary/add').fetch(params, '', 'POST');
/**
 * 编辑
 * @param params 参数
 */
export const edit = (params) => new Api('/pms/reviewLibrary/edit').fetch(params, '', 'PUT');
/**
 * 分页查询
 * @param params 参数
 */
export const page = (params) => new Api('/pms/reviewLibrary/page').fetch(params, '', 'POST');
/**
 * 详情
 * @param id 参数
 * @param pageCode 权限编码
 */
export const reviewLibraryGet = (id, pageCode = '') => new Api(`/pms/reviewLibrary/${id}?pageCode=${pageCode}`).fetch('', '', 'GET');
/**
 * 删除（批量）
 * @param params 参数
 */
export const remove = (params) => new Api('/pms/reviewLibrary/remove').fetch(params, '', 'DELETE');
/**
 * 删除（单个）
 * @param id 参数
 */
export const reviewLibraryDelete = (id) => new Api(`/pms/reviewLibrary/${id}`).fetch('', '', 'DELETE');
/**
 * LIST
 * @param id 参数
 */
export const getList = () => new Api('/pms/reviewLibrary/getList').fetch('', '', 'GET');
