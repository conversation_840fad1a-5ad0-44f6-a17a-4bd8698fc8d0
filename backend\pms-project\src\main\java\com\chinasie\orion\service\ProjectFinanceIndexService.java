package com.chinasie.orion.service;





import com.chinasie.orion.domain.entity.ProjectFinanceIndex;
import com.chinasie.orion.domain.dto.ProjectFinanceIndexDTO;
import com.chinasie.orion.domain.vo.ProjectFinanceIndexVO;
import java.lang.String;
import java.util.List;


import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import java.lang.String;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * ProjectFinanceIndex 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-15 16:41:01
 */
public interface ProjectFinanceIndexService  extends  OrionBaseService<ProjectFinanceIndex>  {


    /**
     *  详情
     *
     * * @param id
     */
    ProjectFinanceIndexVO detail(String id,String pageCode)throws Exception;

    /**
     *  新增
     *
     * * @param projectFinanceIndexDTO
     */
    String create(ProjectFinanceIndexDTO projectFinanceIndexDTO)throws Exception;

    /**
     *  编辑
     *
     * * @param projectFinanceIndexDTO
     */
    Boolean edit(ProjectFinanceIndexDTO projectFinanceIndexDTO)throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<ProjectFinanceIndexVO> pages( Page<ProjectFinanceIndexDTO> pageRequest)throws Exception;

    /**
     *  下载模板
     *
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response)throws Exception;

    /**
     *  导入校验
     *
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


    /**
     *  确认导入
     *
     * * @param importId
     */
    Boolean importByExcel(String importId)throws Exception;

    /**
     *  取消导入
     *
     * * @param importId
     */
    Boolean importCancelByExcel(String importId)throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

    /**
     *  设置各种名称
     *
     * * @param searchConditions
     * * @param response
     */
    void  setEveryName(List<ProjectFinanceIndexVO> vos)throws Exception;

    /**
     * 查找list数据
     * @param projectFinanceIndexDTO
     * @return
     */
    List<ProjectFinanceIndexVO> listByInfo(ProjectFinanceIndexDTO projectFinanceIndexDTO);
}
