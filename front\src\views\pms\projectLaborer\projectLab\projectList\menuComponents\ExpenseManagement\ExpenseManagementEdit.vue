<script setup lang="ts">
import { BasicForm, useForm, InputSelectUser } from 'lyra-component-vue3';
import {
  computed, reactive, onMounted, Ref, ref, h,
} from 'vue';
import { getRules, setFormFieldsValues } from './utils';
import Api from '/@/api';
import { getPlanDict } from '/@/views/pms/api';

const props = defineProps<{
    formId: string | undefined,
    projectId: string | undefined,
}>();
const occurrencePerson = ref();
const costCenterOptions = ref();

// 对于自定义规则进行处理
const schemas = [
  {
    field: 'number',
    label: '成本支出编码',
    component: 'Input',
    colProps: {
      span: 12,
    },
    type: 'input',
    componentProps: {
      readOnly: true,
      disabled: true,
      placeholder: '请输入',
    },
    helpMessage: '新增完成时自动生成',
    rules: [],
  },
  {
    field: 'expenseAccountNumber',
    label: '费用科目',
    component: 'ApiTreeSelect',
    colProps: {
      span: 12,
    },
    rules: [
      {
        required: true,
        trigger: 'change',
      },
    ],
    componentProps: {
      // immediate: true,
      api: () => new Api(`/pms/expenseSubject/tree?status=${1}`).fetch('', '', 'GET').then((res) => (res)),
      labelField: 'name',
      valueField: 'number',
      onChange(selectedId, selectedItems, selectObject) {
        if (selectObject?.triggerNode?.props) {
          const selectedItem = selectObject?.triggerNode?.props;
          const expenseAccountName = selectedItem?.name || '';
          const expenseAccountId = selectedItem?.id || '';
          setFieldsValue({
            expenseAccountName,
            expenseAccountId,
          });
        }
      },
    },
  },
  {
    field: 'expenseAccountName',
    component: 'Input',
    label: '费用科目名称',
    show: false,
  },

  {
    field: 'expenseAccountId',
    label: '费用科目ID',
    component: 'Input',
    show: false,
  },
  {
    field: 'costCenterId',
    label: '成本中心',
    component: 'Select',
    colProps: {
      span: 12,
    },
    rules: [
      {
        required: true,
        trigger: 'change',
      },
    ],
    componentProps: {
      fieldNames: {
        key: 'id',
        value: 'id',
        label: 'name',
      },
      options: computed(() => costCenterOptions.value),
      onChange(selectedId, selectedItem) {
        if (selectedItem) {
          const costCenterName = selectedItem?.name || '';
          const costCenterNumber = selectedItem?.number || '';
          setFieldsValue({
            costCenterName,
            costCenterNumber,
          });
        }
      },
    },
  },
  {
    field: 'costCenterName',
    component: 'Input',
    label: '成本中心名称',
    show: false,
  },
  {
    field: 'occurrenceName',
    component: 'Input',
    label: '发生人',
    show: false,
  },
  {
    field: 'costCenterNumber',
    label: '成本中心编码',
    component: 'Input',
    show: false,
  },
  {
    field: 'occurrenceTime',
    label: '发生时间',
    component: 'DatePicker',
    colProps: {
      span: 12,
    },
    type: 'date',
    componentProps: {
      allowClear: true,
      placeholder: '请选择',
    },
    rules: [
      {
        required: true,
        message: '该内容为必填项',
        trigger: 'change',
      },
    ],
  },
  {
    field: 'occurrencePerson',
    label: '发生人',
    component: 'InputSearch',
    colProps: {
      span: 12,
    },
    type: 'input',
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [
      {
        required: true,
        message: '该内容为必填项',
        trigger: 'change',
      },
    ],
    render({ model, field }) {
      return h(InputSelectUser, {
        selectUserData: computed(() => occurrencePerson.value),
        onChange(users) {
          occurrencePerson.value = users;
          const userId = users?.[0]?.id;
          const userName = users?.[0]?.name;
          model[field] = userId ?? '';
          model.occurrenceName = userName ?? '';
          validateFields(['occurrencePerson']);
        },
        selectUserModalProps: {
          selectType: 'radio',
        },
      });
    },
  },
  {
    field: 'expendMoney',
    label: '支出金额',
    component: 'InputMoney',
    colProps: {
      span: 12,
    },
    type: 'number',
    componentProps: {
      min: 0,
    },
    rules: [],
  },
  {
    field: 'remark',
    label: '摘要',
    component: 'InputTextArea',
    colProps: {
      span: 24,
    },
    type: 'textarea',
    componentProps: {
      showCount: true,
      maxlength: 200,
      placeholder: '请输入',
    },
    rules: [
      {
        required: true,
        message: '该内容为必填项',
        trigger: 'change',
      },
    ],
  },
];
const [register, { validate, setFieldsValue, validateFields }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  getSelection();
  props.formId && getFormData();
});

function getSelection() {
  new Api('/pms/costCenter/list').fetch('', '', 'POST').then((res) => {
    costCenterOptions.value = res;
  });
}

const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/budgetExpendForm').fetch('', props.formId, 'GET');
    occurrencePerson.value = [
      {
        id: result.occurrencePerson,
        name: result.occurrenceName,
      },
    ];
    setFieldsValue({ ...result });
  } finally {
    loading.value = false;
  }
}

defineExpose({
  async onSubmit() {
    const formValues = await validate();
    const params = {
      id: props?.formId,
      ...formValues,
      mainTableId: undefined,
      projectId: props.projectId,
    };

    return new Promise((resolve, reject) => {
      new Api('/pms/budgetExpendForm').fetch(params, props.formId ? 'edit' : 'add', props.formId ? 'PUT' : 'POST').then(() => {
        resolve('');
      }).catch((err) => {
        reject(err);
      });
    });
  },
});
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">

</style>
