package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;
import java.lang.String;
import java.util.List;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * BudgetExpendForm DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:07
 */
@ApiModel(value = "BudgetExpendFormDTO对象", description = "预算支出表单")
@Data
@ExcelIgnoreUnannotated
public class BudgetExpendFormDTO extends ObjectDTO implements Serializable {

    /**
     * 成本支出编码
     */
    @ApiModelProperty(value = "成本支出编码")
    @ExcelProperty(value = "成本支出编码 ", index = 1)
    private String number;

    /**
     * 科目名称
     */
    @ApiModelProperty(value = "科目名称")
    @ExcelProperty(value = "科目名称 ", index = 2)
    private String expenseAccountName;

    /**
     * 科目编码
     */
    @ApiModelProperty(value = "科目编码")
    @ExcelProperty(value = "科目编码 ", index = 3)
    private String expenseAccountNumber;

    /**
     * 科目Id
     */
    @ApiModelProperty(value = "科目Id")
    @ExcelProperty(value = "科目Id ", index = 4)
    private String expenseAccountId;

    /**
     * 成本中心id
     */
    @ApiModelProperty(value = "成本中心id")
    @ExcelProperty(value = "成本中心id ", index = 5)
    private String costCenterId;

    /**
     * 发生时间
     */
    @ApiModelProperty(value = "发生时间")
    @ExcelProperty(value = "发生时间 ", index = 6)
    private Date occurrenceTime;

    /**
     * 发生人
     */
    @ApiModelProperty(value = "发生人")
    @ExcelProperty(value = "发生人 ", index = 7)
    private String occurrencePerson;

    /**
     * 支出金额
     */
    @ApiModelProperty(value = "支出金额")
    @ExcelProperty(value = "支出金额 ", index = 8)
    private BigDecimal expendMoney;

    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    @ExcelProperty(value = "项目Id ", index = 9)
    private String projectId;


}
