package com.chinasie.orion.constant.quality;

import java.util.Objects;

public enum RelevanceEnum {
    OK(1, "已关联"),
    //直管领导审核中
    NO(0, "未关联"),
    ;

    private Integer code;
    private String name;


    RelevanceEnum(Integer code, String name) {
        this.name = name;
        this.code = code;
    }


    public String getName() {
        return name;
    }

    public Integer getCode() {
        return code;
    }


    private static RelevanceEnum getEnumByCode(Integer code) {
        for (RelevanceEnum performanceAppraiseStatusEnum : RelevanceEnum.values()) {
            if (Objects.equals(performanceAppraiseStatusEnum.getCode(), code)) {
                return performanceAppraiseStatusEnum;
            }
        }
        return null;
    }
}
