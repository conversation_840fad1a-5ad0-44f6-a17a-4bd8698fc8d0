package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.Boolean;
import java.lang.String;

/**
 * ProjectSchemeFeedback Entity对象
 *
 * <AUTHOR>
 * @since 2024-04-19 18:29:03
 */
@TableName(value = "pms_project_scheme_feedback")
@ApiModel(value = "ProjectSchemeFeedbackEntity对象", description = "项目计划反馈日志")
@Data
public class ProjectSchemeFeedback implements Serializable{

    @ApiModelProperty("ID")
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
    * 项目计划id
    */
    @ApiModelProperty(value = "项目计划id")
    @TableField(value = "project_scheme_id")
    private String projectSchemeId;

    /**
    * 是否执行
    */
    @ApiModelProperty(value = "是否执行")
    @TableField(value = "is_execute")
    private Boolean isExecute;

    /**
    * 下一次执行时间
    */
    @ApiModelProperty(value = "下一次执行时间")
    @TableField(value = "next_execute_time")
    private Date nextExecuteTime;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT, value = "create_time")
    private Date createTime;

    @ApiModelProperty("修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE, value = "modify_time")
    private Date modifyTime;

    @ApiModelProperty("logicStatus 逻辑删除字段")
    @TableField(fill = FieldFill.INSERT, value = "logic_status")
    @TableLogic
    private Integer logicStatus;
}
