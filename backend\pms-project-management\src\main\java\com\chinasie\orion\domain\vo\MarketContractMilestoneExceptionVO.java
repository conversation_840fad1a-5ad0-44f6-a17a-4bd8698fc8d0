package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.util.List;
/**
 * MarketContractMilestoneException VO对象
 *
 * <AUTHOR>
 * @since 2024-05-30 02:55:28
 */
@ApiModel(value = "MarketContractMilestoneExceptionVO对象", description = "市场合同里程碑异常信息")
@Data
public class MarketContractMilestoneExceptionVO extends  ObjectVO   implements Serializable{

    /**
     * 里程碑id
     */
    @ApiModelProperty(value = "里程碑id")
    private String milestoneId;


    /**
     * 异常说明
     */
    @ApiModelProperty(value = "异常说明")
    private String exceptionDesc;




}
