package com.chinasie.orion.management.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;

import java.util.List;

/**
 * NcfFormPurchOrderCollect VO对象
 *
 * <AUTHOR>
 * @since 2024-06-21 14:55:52
 */
@ApiModel(value = "NcfFormPurchOrderCollectVO对象", description = "采购-商城集采订单（总表）")
@Data
public class NcfFormPurchOrderCollectVO extends ObjectVO implements Serializable {

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderNumber;


    /**
     * PO订单号
     */
    @ApiModelProperty(value = "PO订单号")
    private String poOrderNumber;


    /**
     * 电商渠道订单号
     */
    @ApiModelProperty(value = "电商渠道订单号")
    private String commerceChannelOrderNumber;


    /**
     * 企业名称
     */
    @ApiModelProperty(value = "企业名称")
    private String enterpriseName;


    /**
     * PR公司名称
     */
    @ApiModelProperty(value = "PR公司名称")
    private String prCompanyName;


    /**
     * 部门
     */
    @ApiModelProperty(value = "部门")
    private String department;


    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    private String contractId;


    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String contractName;


    /**
     * 下单人
     */
    @ApiModelProperty(value = "下单人")
    private String orderPlacer;


    /**
     * 下单人电话
     */
    @ApiModelProperty(value = "下单人电话")
    private String orderPhoneNumber;


    /**
     * 下单时间
     */
    @ApiModelProperty(value = "下单时间")
    private Date orderTime;


    /**
     * 对账人
     */
    @ApiModelProperty(value = "对账人")
    private String reconciler;


    /**
     * 收货负责人
     */
    @ApiModelProperty(value = "收货负责人")
    private String consignee;


    /**
     * 收货审核人
     */
    @ApiModelProperty(value = "收货审核人")
    private String receiptReviewer;


    /**
     * 支付负责人
     */
    @ApiModelProperty(value = "支付负责人")
    private String paymentManager;


    /**
     * 验收方式
     */
    @ApiModelProperty(value = "验收方式")
    private String acceptanceMethod;


    /**
     * 结算方式
     */
    @ApiModelProperty(value = "结算方式")
    private String settlementMethod;


    /**
     * 要求到货日期
     */
    @ApiModelProperty(value = "要求到货日期")
    private Date requestDeliveryDate;


    /**
     * 订单总金额
     */
    @ApiModelProperty(value = "订单总金额")
    private BigDecimal totalOrderAmount;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 订单待支付
     */
    @ApiModelProperty(value = "订单待支付")
    private String orderPayDay;


    /**
     * 订单确认时间
     */
    @ApiModelProperty(value = "订单确认时间")
    private Date orderConfirmationTime;


    /**
     * 订单审批时间
     */
    @ApiModelProperty(value = "订单审批时间")
    private Date orderApprovalTime;


    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间")
    private Date paidTime;


    /**
     * 开票时间
     */
    @ApiModelProperty(value = "开票时间")
    private Date invoicingTime;


    /**
     * 申请开票时间
     */
    @ApiModelProperty(value = "申请开票时间")
    private Date applicationForInvoicingTime;


    /**
     * 对账确认时间
     */
    @ApiModelProperty(value = "对账确认时间")
    private Date reconciliationConfirmationTime;


    /**
     * 对账申请时间
     */
    @ApiModelProperty(value = "对账申请时间")
    private Date reconciliationApplicationTime;


    /**
     * 发货耗时
     */
    @ApiModelProperty(value = " 发货耗时")
    private Integer usedTime;


    /**
     * 订单最后一次交货时间
     */
    @ApiModelProperty(value = "订单最后一次交货时间")
    private Date timeOfDelivery;


    /**
     * 订单最后一次确认收货时间
     */
    @ApiModelProperty(value = "订单最后一次确认收货时间")
    private Date timeOfLastReceipt;


    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态")
    private String orderState;


    /**
     * 退货金额
     */
    @ApiModelProperty(value = "退货金额")
    private BigDecimal returnAmount;


}
