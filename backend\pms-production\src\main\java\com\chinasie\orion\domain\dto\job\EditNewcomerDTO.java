package com.chinasie.orion.domain.dto.job;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/08/10/17:48
 * @description:
 */
@Data
public class EditNewcomerDTO implements Serializable {
    @ApiModelProperty("人员管理ID")
    private String id;
    @ApiModelProperty("是否新人")
    private Boolean isNewcomer;


    /**
     * 接口人
     */
    @ApiModelProperty(value = "接口人")
    @TableField(value = "contact_user")
    private String contactUser;
    /**
     * 接口人
     */
    @ApiModelProperty(value = "接口人编号")
    @TableField(value = "contact_user_code")
    private String contactUserCode;
    @ApiModelProperty(value = "接口人名称")
    @TableField(value = "contact_user_name")
    private String contactUserName;
}
