package com.chinasie.orion.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.util.Date;
import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * TrainEquivalent DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-04 17:09:24
 */
@ApiModel(value = "TrainEquivalentDTO对象", description = "培训等效")
@Data
@ExcelIgnoreUnannotated
public class TrainEquivalentDTO extends  ObjectDTO   implements Serializable{

    /**
     * 等效基地编号
     */
    @ApiModelProperty(value = "等效基地编号")
    private String equivalentBaseCode;

    /**
     * 等效基地名称
     */
    @ApiModelProperty(value = "等效基地名称")
    private String equivalentBaseName;

    /**
     * 等效认定时间
     */
    @ApiModelProperty(value = "等效认定时间")
    private Date equivalentDate;


    /**
     */
    @ApiModelProperty(value = "人员编号")
    private String personNumber;


    /**
     * 是否等效
     */
    @ApiModelProperty(value = "是否等效")
    private Boolean isEquivalent;


    @ApiModelProperty(value = "培训编号")
    private String trainNumber;

    @ApiModelProperty(value = "培训名称")
    private String trainName;

    @ApiModelProperty(value = "培训所在基地")
    private String baseCode;

    @ApiModelProperty(value = "培训所在基地名称")
    private String baseName;

    @ApiModelProperty(value = "参培中心关联表Id")
    private String trainCenterId;

    @ApiModelProperty(value = "参培中心名称")
    private String trainCenterName;

    @ApiModelProperty(value = "唯一落地Id")
    private String  ukKey;

    @ApiModelProperty(value = "附件列表")
    private List<FileDTO> fileDTOList;
}
