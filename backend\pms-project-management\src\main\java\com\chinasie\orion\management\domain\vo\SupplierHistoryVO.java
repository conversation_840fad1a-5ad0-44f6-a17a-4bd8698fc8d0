package com.chinasie.orion.management.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * SupplierHistory VO对象
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@ApiModel(value = "SupplierHistoryVO对象", description = "历史资审记录")
@Data
public class SupplierHistoryVO extends ObjectVO implements Serializable {

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;


    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;


    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    private String serialNumber;


    /**
     * 申请编号
     */
    @ApiModelProperty(value = "申请编号")
    private String applicationId;


    /**
     * 申请类型
     */
    @ApiModelProperty(value = "申请类型")
    private String applicationType;


    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
    private String applicant;


    /**
     * 申请公司
     */
    @ApiModelProperty(value = "申请公司")
    private String applyingCompany;


    /**
     * 评审公司
     */
    @ApiModelProperty(value = "评审公司")
    private String reviewingCompany;


    /**
     * 安全专家评分
     */
    @ApiModelProperty(value = "安全专家评分")
    private String safetyExpertScore;


    /**
     * 技术专家评分
     */
    @ApiModelProperty(value = "技术专家评分")
    private String techExpertScore;


    /**
     * 商务专家评分
     */
    @ApiModelProperty(value = "商务专家评分")
    private String businessExpertScore;


    /**
     * 质保专家评分
     */
    @ApiModelProperty(value = "质保专家评分")
    private String qualityAssuranceExpertScore;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    private String mainTableId;


}
