package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ContractCenterPlanListDTO;
import com.chinasie.orion.domain.dto.ContractMainDTO;
import com.chinasie.orion.domain.dto.EstablishmentContractPlanDTO;
import com.chinasie.orion.domain.dto.IssuedDTO;
import com.chinasie.orion.domain.vo.ContractCenterPlanVO;
import com.chinasie.orion.domain.vo.ContractInfoVO;
import com.chinasie.orion.domain.vo.ContractMainVO;
import com.chinasie.orion.domain.vo.LaborCostStatisticsVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ContractMainService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * ContractMain 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-17 09:42:35
 */
@RestController
@RequestMapping("/contractMain")
@Api(tags = "合同计划主表")
public class  ContractMainController  {

    @Autowired
    private ContractMainService contractMainService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据【{{#id}}】", type = "合同计划主表", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ContractMainVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        ContractMainVO rsp = contractMainService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param contractMainDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#contractMainDTO.name}}】", type = "合同计划主表", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody ContractMainDTO contractMainDTO) throws Exception {
        String rsp =  contractMainService.create(contractMainDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param contractMainDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#contractMainDTO.name}}】", type = "合同计划主表", subType = "编辑", bizNo = "{{#contractMainDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  ContractMainDTO contractMainDTO) throws Exception {
        Boolean rsp = contractMainService.edit(contractMainDTO);
        return new ResponseDTO<>(rsp);
    }


//    /**
//     * 删除
//     *
//     * @return
//     * @throws Exception
//     */
//    @ApiOperation(value = "删除")
//    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
//    @Transactional(rollbackFor = Exception.class)
//    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "合同计划主表", subType = "删除", bizNo = "{{#id}}")
//    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
//        Boolean rsp = contractMainService.remove(Collections.singletonList(id));
//        return new ResponseDTO<>(rsp);
//    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "合同计划主表", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<IssuedDTO> params) throws Exception {
        Boolean rsp = contractMainService.remove(params);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "合同计划主表", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ContractMainVO>> pages(@RequestBody Page<ContractMainDTO> pageRequest) throws Exception {
        Page<ContractMainVO> rsp =  contractMainService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("合同计划主表导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "合同计划主表", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        contractMainService.downloadExcelTpl(response);
    }

    @ApiOperation("合同计划主表导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "合同计划主表", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = contractMainService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("合同计划主表导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "合同计划主表", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  contractMainService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消合同计划主表导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "合同计划主表", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  contractMainService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("合同计划主表导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "合同计划主表", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        contractMainService.exportByExcel(searchConditions, response);
    }


    @ApiOperation("合同计划下发")
    @PostMapping("/issued")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】合同计划下发", type = "合同计划主表", subType = "合同计划下发", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> issued (@RequestBody List<IssuedDTO> params){
        contractMainService.issuedByIds(params);
        return new ResponseDTO<>(true);
    }

    @ApiOperation("判断部门和中心")
    @GetMapping("/judge")
    @LogRecord(success = "【{USER{#logUserId}}】判断部门和中心", type = "合同计划主表", subType = "判断部门和中心", bizNo = "{{#deptCode}}")
    public ResponseDTO<String> judge(String deptCode){
        return new ResponseDTO<>(contractMainService.judge(deptCode));
    }

    @ApiOperation("编制合同计划")
    @PutMapping("/establishment")
    @LogRecord(success = "【{USER{#logUserId}}】编制合同计划", type = "合同计划主表", subType = "编制合同计划", bizNo = "{{#deptCode}}")
    public ResponseDTO<Boolean> establishment(@RequestBody EstablishmentContractPlanDTO establishmentContractPlanDTO){
        contractMainService.establishment(establishmentContractPlanDTO);
        return new ResponseDTO<>(true);
    }

    @ApiOperation("获取合同信息")
    @PostMapping("/contractInfo/{year}/{contractNumber}")
    @LogRecord(success = "【{USER{#logUserId}}】获取合同信息", type = "合同计划主表", subType = "获取合同信息", bizNo = "{{#year}}")
    public ResponseDTO<ContractInfoVO> contractInfoDetail(@PathVariable("year")Integer year,@PathVariable("contractNumber")String contractNumber){
        ContractInfoVO res = contractMainService.contractInfoDetail(year,contractNumber);
        return new ResponseDTO<>(res);
    }

    @ApiOperation("获取用人单位计划（用人计划分组）")
    @PostMapping("/group/centerPlan")
    @LogRecord(success = "【{USER{#logUserId}}】获取用人单位计划（用人计划分组）", type = "合同计划主表", subType = "获取用人单位计划（用人计划分组）", bizNo = "")
    public ResponseDTO<Map<String, List<ContractCenterPlanVO>>> getCenterPlan(@RequestBody ContractCenterPlanListDTO contractCenterPlanListDTO){
        Map<String, List<ContractCenterPlanVO>> res = contractMainService.groupCenterPlan(contractCenterPlanListDTO);
        return new ResponseDTO<>(res);
    }


    @ApiOperation("开启下年录入")
    @PostMapping("/action/next")
    @LogRecord(success = "【{USER{#logUserId}}】开启下年录入", type = "合同计划主表", subType = "开启下年录入", bizNo = "{{#contractMainIds.toString()}}")
    public ResponseDTO<Boolean> beginNextYear(@RequestBody List<String> contractMainIds){
        contractMainService.beginNextYear(contractMainIds);
        return new ResponseDTO<>(true);
    }

    @ApiOperation("开启调整")
    @PostMapping("/begin/edit")
    @LogRecord(success = "【{USER{#logUserId}}】开启调整", type = "合同计划主表", subType = "开启调整", bizNo = "{{#contractMainIds.toString()}}")
    public ResponseDTO<Boolean> beginEdit(@RequestBody List<String> contractMainIds){
        contractMainService.beginEdit(contractMainIds);
        return new ResponseDTO<>(true);
    }

    @ApiOperation("获取表头")
    @GetMapping("/sheet/heads")
    @LogRecord(success = "【{USER{#logUserId}}】获取表头", type = "合同计划主表", subType = "获取表头", bizNo = "")
    public ResponseDTO<List<Map<String,Object>>> getSheetHeads(){
        List<Map<String,Object>>  heads = contractMainService.getSheetHeads();
        return new ResponseDTO<>(heads);
    }


    @ApiOperation("根据中心code和合同编号和年份获取计划详情")
    @GetMapping("/listByCode")
    @LogRecord(success = "【{USER{#logUserId}}】根据中心code和合同编号和年份获取计划详情", type = "合同计划主表", subType = "根据中心code和合同编号和年份获取计划详情", bizNo = "")
    public ResponseDTO<List<ContractCenterPlanVO>> listByCode(String code,String contractNumber,Integer year){
        List<ContractCenterPlanVO> res = contractMainService.listByCode(code,contractNumber,year);
        return new ResponseDTO<>(res);
    }

    @ApiOperation("人力成本数据统计")
    @GetMapping("/laborCost/statistics")
    @LogRecord(success = "【{USER{#logUserId}}】人力成本数据统计", type = "合同计划主表", subType = "人力成本数据统计", bizNo = "")
    public ResponseDTO<LaborCostStatisticsVO> laborCostStatistics(String contractNumber,Integer year){
        LaborCostStatisticsVO res = contractMainService.laborCostStatistics(contractNumber,year);
        return new ResponseDTO<>(res);
    }



}
