export const NORMAL_NODE = {
  FINISHED: '#8598b1',
  UNDERWAY: '#f09509',
  NOT_START: '#cccccc',
};
export const START_END_NODE = {
  FINISHED: '#D60072',
  NOT_START: '#cccccc',
};

/**
 * 自定义圆节点注册
 * @param Node 节点对象
 */
export const registerNodeSieCircle = (Node) => {
  if (!Node.registry.get('sie-circle')) {
    // 定义新节点
    Node.registry.register('sie-circle', {
      inherit: 'circle',
      width: 70,
      height: 70,
      markup: [
        {
          tagName: 'circle',
          selector: 'body',
        },
        {
          tagName: 'text',
          selector: 'label',
        },
      ],
      attrs: {
        body: {
          fill: '#D60072',
          stroke: '#D60072',
          r: 35,
        },
        label: {
          fill: '#fff',
          fontSize: 18,
          fontWeight: 'bold',
        },
      },
    });
  }
};
