package com.chinasie.orion.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.api.code.SysCodeApi;
import com.chinasie.orion.bo.UserBo;
import com.chinasie.orion.constant.WorkHourFillStatusEnum;
import com.chinasie.orion.constant.WorkHourFillTypeEnum;
import com.chinasie.orion.constant.WorkHourTypeEnum;
import com.chinasie.orion.domain.dto.WorkHourFillDTO;
import com.chinasie.orion.domain.dto.WorkHourFillDayDTO;
import com.chinasie.orion.domain.dto.WorkHourFillDetailDTO;
import com.chinasie.orion.domain.dto.WorkHourFillPageDTO;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.entity.WorkHourFill;
import com.chinasie.orion.domain.entity.WorkHourFillDay;
import com.chinasie.orion.domain.entity.WorkHourFillDetail;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.WorkflowFeignService;
import com.chinasie.orion.helper.InternalAssociationRedisHelper;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ProjectRepository;
import com.chinasie.orion.repository.WorkHourFillDayRepository;
import com.chinasie.orion.repository.WorkHourFillDetailRepository;
import com.chinasie.orion.repository.WorkHourFillRepository;
import com.chinasie.orion.sdk.domain.vo.business.RoleVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectRoleUserService;
import com.chinasie.orion.service.WorkHourFillService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.CollectionUtils;
import com.chinasie.orion.util.ResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * WorkHourFill 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-15 10:14:51
 */
@Service
public class WorkHourFillServiceImpl extends OrionBaseServiceImpl<WorkHourFillRepository, WorkHourFill> implements WorkHourFillService {

    @Autowired
    private WorkHourFillRepository workHourFillRepository;

    @Autowired
    private WorkHourFillDayRepository workHourFillDayRepository;

    @Autowired
    private WorkHourFillDetailRepository workHourFillDetailRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private SysCodeApi sysCodeApi;

    @Resource
    private UserRedisHelper userRedisHelper;

    @Resource
    private WorkflowFeignService workflowFeignService;

    @Resource
    private InternalAssociationRedisHelper internalAssociationRedisHelper;

    @Autowired
    private UserBo userBo;

    @Autowired
    private ProjectRoleUserService projectRoleUserService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    public WorkHourFillInfoVO detail(String id) throws Exception {
        WorkHourFill workHourFill = workHourFillRepository.selectById(id);
        if(workHourFill == null){
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "填报信息不存在!");
        }

        String projectId = workHourFill.getProjectId();

        Project project = projectRepository.selectById(projectId);
        String entry = "";
        if (project != null) {
            entry = project.getName() +"的工时填报";
        }
        List<WorkHourFillDayInfoVO> workHourFillDayInfoVOList = new ArrayList<>();
       List<WorkHourFillDay> workHourFillDays =  workHourFillDayRepository.selectList(WorkHourFillDay::getFillId, id);
        if(!CollectionUtils.isBlank(workHourFillDays)){
            List<String> dayIds = workHourFillDays.stream().map(WorkHourFillDay :: getId).collect(Collectors.toList());
            List<WorkHourFillDetail> workHourFillDetails = workHourFillDetailRepository.selectList(WorkHourFillDetail :: getFillDayId, dayIds);
            Map<String,List<WorkHourFillDetail>> detailMap = workHourFillDetails.stream().collect(Collectors.groupingBy(WorkHourFillDetail :: getFillDayId));

            for(WorkHourFillDay workHourFillDay : workHourFillDays){
                List<WorkHourFillDetail> detailList = detailMap.get(workHourFillDay.getId());
                if(!CollectionUtils.isBlank(detailList)){
                    List<String> relateObjects = detailList.stream().map(WorkHourFillDetail :: getRelateObject).collect(Collectors.toList());
                    List<ProjectInternalAssociationRedisVO> internalAssociationRedisVOList = internalAssociationRedisHelper.queryForEntityByIds(relateObjects);
                    Map<String,ProjectInternalAssociationRedisVO> internalAssociationRedisVOMap =  internalAssociationRedisVOList.stream().collect(Collectors.toMap(ProjectInternalAssociationRedisVO :: getId,Function.identity()));
                    for(WorkHourFillDetail workHourFillDetail : detailList){
                        WorkHourFillDayInfoVO workHourFillDayInfoVO = new WorkHourFillDayInfoVO();
                        workHourFillDayInfoVO.setEntry(entry);
                        workHourFillDayInfoVO.setId(workHourFillDetail.getId());
                        workHourFillDayInfoVO.setWorkDate(workHourFillDay.getWorkDate());
                        workHourFillDayInfoVO.setType(workHourFill.getType());
                        workHourFillDayInfoVO.setWorkHour(workHourFillDay.getWorkHour());
                        workHourFillDayInfoVO.setProjectPlace(workHourFillDetail.getProjectPlace());
                        workHourFillDayInfoVO.setTaskContent(workHourFillDetail.getTaskContent());
                        workHourFillDayInfoVO.setRelateObject(workHourFillDetail.getRelateObject());
                        if(StringUtils.hasText(workHourFillDetail.getRelateObject())){
                            ProjectInternalAssociationRedisVO internalAssociationRedisVO = internalAssociationRedisVOMap.get(workHourFillDetail.getRelateObject());
                            if(internalAssociationRedisVO != null){
                                workHourFillDayInfoVO.setRelateObjectName((StringUtils.hasText(internalAssociationRedisVO.getName()) ? "【"+internalAssociationRedisVO.getName()+"】":"")+internalAssociationRedisVO.getInnerName());
                            }
                        }
                        workHourFillDayInfoVOList.add(workHourFillDayInfoVO);
                    }
                }
                else{
                    WorkHourFillDayInfoVO workHourFillDayInfoVO = new WorkHourFillDayInfoVO();
                    workHourFillDayInfoVO.setEntry(workHourFillDay.getId());
                    workHourFillDayInfoVO.setWorkDate(workHourFillDay.getWorkDate());
                    workHourFillDayInfoVO.setType(workHourFill.getType());
                    workHourFillDayInfoVO.setWorkHour(workHourFillDay.getWorkHour());
                    workHourFillDayInfoVOList.add(workHourFillDayInfoVO);
                }

            }
        }
        WorkHourFillInfoVO result = BeanCopyUtils.convertTo(workHourFill,WorkHourFillInfoVO::new);

        List<String> memberRoleNames = new ArrayList<>();
        if(StringUtils.hasText(result.getMemberId())){
            UserVO userVO = userBo.getUserById(result.getMemberId());
            if(userVO != null){
                result.setMemberName(userVO.getName());
            }
            List<RoleVO> roleVOS = projectRoleUserService.getRoleByProjectAndUserId(result.getProjectId(),result.getMemberId());
            if(!CollectionUtils.isBlank(roleVOS)){
                memberRoleNames = roleVOS.stream().map(RoleVO :: getName).collect(Collectors.toList());
            }
            String memberRoleName = memberRoleNames.stream().collect(Collectors.joining(","));
            result.setMemberRoleName(memberRoleName);
        }

        result.setDayList(workHourFillDayInfoVOList);
        return result;
    }

    @Override
    public WorkHourFillDayVO byDate(String workDate, String projectId) throws Exception {

        WorkHourFillDayVO workHourFillDayVO = new WorkHourFillDayVO();

        Project project = projectRepository.selectById(projectId);
        if (project == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "项目不存在!");
        }

        workHourFillDayVO.setProjectName(project.getName());
        LambdaQueryWrapperX<WorkHourFillDay> lambdaQueryWrapperX = new LambdaQueryWrapperX();
        lambdaQueryWrapperX.eq(WorkHourFillDay :: getWorkDate, workDate);
        lambdaQueryWrapperX.eq(WorkHourFillDay :: getProjectId, projectId);
        lambdaQueryWrapperX.eq(WorkHourFillDay :: getMemberId, CurrentUserHelper.getCurrentUserId());
        WorkHourFillDay workHourFillDay = workHourFillDayRepository.selectOne(lambdaQueryWrapperX);
        if(workHourFillDay == null){
            workHourFillDayVO.setWorkHourCount(0);
            return workHourFillDayVO;
        }
        BeanCopyUtils.copyProperties(workHourFillDay,workHourFillDayVO);

        String filleId = workHourFillDay.getFillId();
        WorkHourFill workHourFill = workHourFillRepository.selectById(filleId);
        if(workHourFill != null){
            workHourFillDayVO.setStatus(workHourFill.getStatus());
            workHourFillDayVO.setDataStatus(workHourFill.getDataStatus());
        }

        List<WorkHourFillDetail> detailList = workHourFillDetailRepository.selectList(WorkHourFillDetail :: getFillDayId, workHourFillDay.getId());
        List<WorkHourFillDetailVO> detailVOList = BeanCopyUtils.convertListTo(detailList, WorkHourFillDetailVO::new);
        if(!CollectionUtils.isBlank(detailVOList)){
            workHourFillDayVO.setDetaiList(detailVOList);
            workHourFillDayVO.setWorkHourCount(detailVOList.size());
        }
        return workHourFillDayVO;
    }

    @Override
    public List<WorkHourFillDayVO> byMonth(String month, String projectId) throws Exception {
        SimpleDateFormat sf = new SimpleDateFormat("yyyy年MM月");
        SimpleDateFormat sf1 = new SimpleDateFormat("yyyy-MM-dd");
        Date monthDate = sf.parse(month);
        Date beginDate = DateUtil.beginOfMonth(monthDate);
        Date endDate = DateUtil.endOfMonth(monthDate);
        LambdaQueryWrapperX<WorkHourFillDay> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(WorkHourFillDay :: getProjectId,projectId);
        lambdaQueryWrapperX.eq(WorkHourFillDay :: getMemberId,CurrentUserHelper.getCurrentUserId());
        lambdaQueryWrapperX.le(WorkHourFillDay :: getWorkDate,sf1.format(endDate));
        lambdaQueryWrapperX.ge(WorkHourFillDay :: getWorkDate,sf1.format(beginDate));
        lambdaQueryWrapperX.orderByAsc(WorkHourFillDay :: getWorkDate);
        List<WorkHourFillDay> workHourFillDays =  workHourFillDayRepository.selectList(lambdaQueryWrapperX);
        if(!CollectionUtils.isBlank(workHourFillDays)){
            List<String> fillListIds = workHourFillDays.stream().map(WorkHourFillDay :: getFillId).collect(Collectors.toList());
            List<WorkHourFill> workHourFillList = workHourFillRepository.selectList(WorkHourFill :: getId, fillListIds);
            Map<String,WorkHourFill> workHourFillMap = workHourFillList.stream().collect(Collectors.toMap(WorkHourFill :: getId, Function.identity()));
            workHourFillDays.forEach(p ->{
                WorkHourFill workHourFill = workHourFillMap.get(p.getFillId());
                if(workHourFill != null){
                    p.setStatus(workHourFill.getStatus());
                    p.setDataStatus(workHourFill.getDataStatus());
                }
            });
        }
        List<WorkHourFillDayVO> vos = BeanCopyUtils.convertListTo(workHourFillDays, WorkHourFillDayVO::new);

        return vos;
    }

    /**
     *  新增
     *
     * * @param workHourFillDTO
     */
    @Override
    public  WorkHourFillVO create(WorkHourFillDTO workHourFillDTO) throws Exception {
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        List<WorkHourFillDayDTO> workHourFillDayDTOList = workHourFillDTO.getDayDetailList();
        if(CollectionUtils.isBlank(workHourFillDayDTOList)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "工时填报明细不能为空!");
        }
        workHourFillDayDTOList = workHourFillDayDTOList.stream().sorted(Comparator.comparing(WorkHourFillDayDTO :: getWorkDate)).collect(Collectors.toList());
        WorkHourFillDayDTO workHourFillDayDTO = workHourFillDayDTOList.get(0);
        String firstWorkDate = workHourFillDayDTO.getWorkDate();
        int week = DateUtil.dayOfWeek(sf.parse(firstWorkDate));
        if((week -1) != 1){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "工时填报只能整周填写!");
        }
        if(workHourFillDayDTOList.size() != 7){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "工时填报只能整周七天填写!");
        }
        Date firstDate  = sf.parse(firstWorkDate);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(firstDate);
        for(WorkHourFillDayDTO item : workHourFillDayDTOList){
            int weekday = calendar.get(Calendar.DAY_OF_WEEK);
            if (!DateUtil.isSameDay(calendar.getTime(), sf.parse(item.getWorkDate()))) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS,sf.format(calendar.getTime()) +"工时未填!");
            }
            if (weekday != 7 && weekday != 1 && item.getWorkHour() == null) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, sf.format(calendar.getTime()) +"工时未填!");
            }
            calendar.add(Calendar.DAY_OF_MONTH,1);
        }


        boolean isFill = false;
        for(int i = 0; i< workHourFillDayDTOList.size(); i++){
            WorkHourFillDayDTO item =  workHourFillDayDTOList.get(0);
            String workDate = item.getWorkDate();
            List<WorkHourFillDetailDTO> detaiList = item.getDetaiList();
            if(!CollectionUtils.isBlank(detaiList)){
                if(item.getWorkHour() == null){
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS,  sf.format(workDate) +"工时计算错误!");
                }
                int workHourTotal = detaiList.stream().mapToInt(WorkHourFillDetailDTO:: getWorkHour).sum();
                if(workHourTotal != item.getWorkHour()){
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS,  sf.format(workDate) +"工时计算错误!");
                }
                isFill = true;
            }
            if(isFill && CollectionUtils.isBlank(detaiList)){

                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, sf.format(workDate)+"未填报工时!");
            }

            if(i % 7 == 6){
                isFill = false;
            }
        }

        String projectId = workHourFillDTO.getProjectId();
        Project project = projectRepository.selectById(projectId);
        if (project == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "项目不存在!");
        }

        LambdaQueryWrapperX<WorkHourFill>  lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(WorkHourFill :: getMemberId, CurrentUserHelper.getCurrentUserId());
        lambdaQueryWrapperX.eq(WorkHourFill :: getProjectId, workHourFillDTO.getProjectId());
        lambdaQueryWrapperX.eq(WorkHourFill :: getStartDate,firstWorkDate);
        List<WorkHourFill> workHourFillList = workHourFillRepository.selectList(lambdaQueryWrapperX);
        if(!CollectionUtils.isBlank(workHourFillList)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST, "这周的工时已经填报，不能重复填！");
        }


        ResponseDTO<String> responseDTO = sysCodeApi.rulesAndSegmentCreate("WorkHourFill", "number", false, "");
        if (ResponseUtils.fail(responseDTO)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, responseDTO.getMessage());
        }
        String number = responseDTO.getResult();
        if (!StringUtils.hasText(number)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "生成项目合同编号失败");
        }
        workHourFillDTO.setNumber(number);

        WorkHourFillDayDTO startWorkHourFillDay = workHourFillDayDTOList.get(0);
        WorkHourFillDayDTO endWorkHourFillDay = workHourFillDayDTOList.get(workHourFillDayDTOList.size() -1);

        String currentUserId = CurrentUserHelper.getCurrentUserId();
        SimpleUser simpleUser = userRedisHelper.getSimpleUserById(currentUserId);
        if(simpleUser != null){
            String title = simpleUser.getName() + startWorkHourFillDay.getWorkDate() +"至" + endWorkHourFillDay.getWorkDate() + "的工作填报";
            workHourFillDTO.setTitle(title);
        }
        WorkHourFill workHourFill = BeanCopyUtils.convertTo(workHourFillDTO,WorkHourFill::new);
        int workHourTotal = workHourFillDayDTOList.stream().filter(p -> p.getWorkHour() != null).mapToInt(WorkHourFillDayDTO:: getWorkHour).sum();
        workHourFill.setStartDate(startWorkHourFillDay.getWorkDate());
        workHourFill.setEndDate(endWorkHourFillDay.getWorkDate());
        workHourFill.setWorkHour(workHourTotal);
        workHourFill.setFillRole("0");
        workHourFill.setMemberId(CurrentUserHelper.getCurrentUserId());
        workHourFill.setType(WorkHourFillTypeEnum.WORK_HOUR.getCode());
        workHourFill.setWorkHourType(WorkHourTypeEnum.PROJECT.getCode());
        int insert = workHourFillRepository.insert(workHourFill);
        workHourFillDayDTOList.forEach(p ->{
            p.setFillId(workHourFill.getId());
            p.setMemberId(CurrentUserHelper.getCurrentUserId());
            p.setProjectId(workHourFill.getProjectId());
            List<WorkHourFillDetailDTO> detailDTOS = p.getDetaiList();
            if(!CollectionUtils.isBlank(detailDTOS)){
                detailDTOS.forEach(item ->{
                    item.setWorkDate(p.getWorkDate());
                });
            }

        });

        Map<String,WorkHourFillDayDTO> dateListMap = workHourFillDayDTOList.stream().collect(Collectors.toMap(WorkHourFillDayDTO :: getWorkDate, Function.identity()));
        List<WorkHourFillDay> workHourFillDayList =  BeanCopyUtils.convertListTo(workHourFillDayDTOList, WorkHourFillDay::new);

        workHourFillDayRepository.insertBatch(workHourFillDayList);

        List<WorkHourFillDetail>  workHourFillDetailList = new ArrayList<>();
        workHourFillDayList.forEach(p ->{
            WorkHourFillDayDTO workHourFillDayDTO1 = dateListMap.get(p.getWorkDate());
            List<WorkHourFillDetailDTO> detaiList = workHourFillDayDTO1.getDetaiList();
            if(!CollectionUtils.isBlank(detaiList)){
                detaiList.forEach(item ->{
                    item.setFillDayId(p.getId());
                });
                List<WorkHourFillDetail> workHourFillDetails =  BeanCopyUtils.convertListTo(detaiList, WorkHourFillDetail::new);
                workHourFillDetailList.addAll(workHourFillDetails);
            }
        });
        if(!CollectionUtils.isBlank(workHourFillDetailList)){
            workHourFillDetailRepository.insertBatch(workHourFillDetailList);
        }
        WorkHourFillVO rsp = BeanCopyUtils.convertTo(workHourFill,WorkHourFillVO::new);
        return rsp;
    }

    /**
     *  编辑
     *
     * * @param workHourFillDTO
     */
    @Override
    public Boolean edit(WorkHourFillDTO workHourFillDTO) throws Exception {
        String id = workHourFillDTO.getId();
        if(!StringUtils.hasText(id)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "id不能为空");
        }
        WorkHourFill oldWorkHourFill = this.getById(id);
        if(oldWorkHourFill == null){
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "工时填报信息未找到或已删除!");
        }
        if(!oldWorkHourFill.getMemberId().equals(CurrentUserHelper.getCurrentUserId())){
            throw new PMSException(PMSErrorCode.PMS_ERROR_NOT_ROLE, "只能修改自己的工时填报信息!");
        }
        if(!WorkHourFillStatusEnum.CREATED.getStatus().equals(oldWorkHourFill.getStatus())){
            throw new PMSException(PMSErrorCode.KMS_EFFECT_DATA, "只能修改已创建状态的工时填报信息!");
        }
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        List<WorkHourFillDayDTO> workHourFillDayDTOList = workHourFillDTO.getDayDetailList();
        if(CollectionUtils.isBlank(workHourFillDayDTOList)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "工时填报明细不能为空!");
        }

        WorkHourFillDayDTO startWorkHourFillDay = workHourFillDayDTOList.get(0);
        WorkHourFillDayDTO endWorkHourFillDay = workHourFillDayDTOList.get(workHourFillDayDTOList.size() -1);

        workHourFillDayDTOList = workHourFillDayDTOList.stream().sorted(Comparator.comparing(WorkHourFillDayDTO :: getWorkDate)).collect(Collectors.toList());
        WorkHourFillDayDTO workHourFillDayDTO = workHourFillDayDTOList.get(0);
        String firstWorkDate = workHourFillDayDTO.getWorkDate();
        int week = DateUtil.dayOfWeek(sf.parse(firstWorkDate));
        if((week -1) != 1){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "工时填报只能按周填写!");
        }
        if(workHourFillDayDTOList.size() != 7){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "工时填报只能整周七天填写!");
        }
        Date firstDate  = sf.parse(firstWorkDate);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(firstDate);
        for(WorkHourFillDayDTO item : workHourFillDayDTOList){
            int weekday = calendar.get(Calendar.DAY_OF_WEEK);
            if (!DateUtil.isSameDay(calendar.getTime(), sf.parse(item.getWorkDate()))) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS,sf.format(calendar.getTime()) +"工时未填!");
            }
            if (weekday != 7 && weekday != 1 && item.getWorkHour() == null) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, sf.format(calendar.getTime()) +"工时未填!");
            }
            calendar.add(Calendar.DAY_OF_MONTH,1);
        }

        boolean isFill = false;
        for(int i = 0; i< workHourFillDayDTOList.size(); i++){
            WorkHourFillDayDTO item =  workHourFillDayDTOList.get(0);
            String workDate = item.getWorkDate();
            List<WorkHourFillDetailDTO> detaiList = item.getDetaiList();
            if(!CollectionUtils.isBlank(detaiList)){
                if(item.getWorkHour() == null){
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS,  workDate +"工时计算错误!");
                }
                int workHourTotal = detaiList.stream().mapToInt(WorkHourFillDetailDTO:: getWorkHour).sum();
                if(workHourTotal != item.getWorkHour()){
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS,  workDate +"工时计算错误!");
                }
            }
        }


        String projectId = workHourFillDTO.getProjectId();
        Project project = projectRepository.selectById(projectId);
        if (project == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "项目不存在!");
        }

        LambdaQueryWrapperX<WorkHourFill>  lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(WorkHourFill :: getMemberId, CurrentUserHelper.getCurrentUserId());
        lambdaQueryWrapperX.eq(WorkHourFill :: getProjectId, workHourFillDTO.getProjectId());
        lambdaQueryWrapperX.eq(WorkHourFill :: getStartDate,firstWorkDate);
        lambdaQueryWrapperX.ne(WorkHourFill :: getId,id);
        List<WorkHourFill> workHourFillList = workHourFillRepository.selectList(lambdaQueryWrapperX);
        if(!CollectionUtils.isBlank(workHourFillList)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST, "这周的工时已经填报，不能重复填！");
        }



        WorkHourFill workHourFill = BeanCopyUtils.convertTo(workHourFillDTO,WorkHourFill::new);

        workHourFillDayDTOList.forEach(p ->{
            p.setFillId(workHourFill.getId());
            p.setMemberId(CurrentUserHelper.getCurrentUserId());
            p.setProjectId(projectId);
            List<WorkHourFillDetailDTO> detailDTOS = p.getDetaiList();
            if(!CollectionUtils.isBlank(detailDTOS)){
                detailDTOS.forEach(item ->{
                    item.setWorkDate(p.getWorkDate());
                });
            }

        });

        List<String> newWorkDate = workHourFillDayDTOList.stream().map(WorkHourFillDayDTO :: getWorkDate).collect(Collectors.toList());

        List<WorkHourFillDay> oldWorkHourFillDayList =  workHourFillDayRepository.selectList(WorkHourFillDay :: getFillId, workHourFill.getId());
        Map<String,WorkHourFillDay> oldWorkHourFillDayMap = oldWorkHourFillDayList.stream().collect(Collectors.toMap(WorkHourFillDay :: getWorkDate, Function.identity()));
        List<WorkHourFillDayDTO> insertWorkHourFillDayDTOList = new ArrayList<>();
        List<WorkHourFillDayDTO> updateWorkHourFillDayDTOList = new ArrayList<>();
        workHourFillDayDTOList.forEach(p ->{
            WorkHourFillDay workHourFillDay = oldWorkHourFillDayMap.get(p.getWorkDate());
            if(workHourFillDay == null){
                insertWorkHourFillDayDTOList.add(p);
            }
            else{
                p.setId(workHourFillDay.getId());
                updateWorkHourFillDayDTOList.add(p);
            }
        });
        List<WorkHourFillDay> deleteWorkHourFillDayList = oldWorkHourFillDayList.stream().filter(p -> !newWorkDate.contains(p.getWorkDate())).collect(Collectors.toList());


        Map<String,WorkHourFillDayDTO> dateListMap = workHourFillDayDTOList.stream().collect(Collectors.toMap(WorkHourFillDayDTO :: getWorkDate, Function.identity()));




        List<WorkHourFillDetailDTO>  allWorkHourFillDetailList = new ArrayList<>();
       if(!CollectionUtils.isBlank(insertWorkHourFillDayDTOList)){
           List<WorkHourFillDay> insertWorkHourFillDayList =  BeanCopyUtils.convertListTo(insertWorkHourFillDayDTOList, WorkHourFillDay::new);
           workHourFillDayRepository.insertBatch(insertWorkHourFillDayList);
           insertWorkHourFillDayList.forEach(p ->{
               WorkHourFillDayDTO workHourFillDayDTO1 = dateListMap.get(p.getWorkDate());
               List<WorkHourFillDetailDTO> detaiList = workHourFillDayDTO1.getDetaiList();
               if(!CollectionUtils.isBlank(detaiList)){
                   detaiList.forEach(item ->{
                       item.setFillDayId(p.getId());
                   });
                   allWorkHourFillDetailList.addAll(detaiList);
               }
           });

       }

        if(!CollectionUtils.isBlank(updateWorkHourFillDayDTOList)){
            updateWorkHourFillDayDTOList.forEach(p ->{
                List<WorkHourFillDetailDTO> detaiList = p.getDetaiList();
                if(!CollectionUtils.isBlank(detaiList)){
                    detaiList.forEach(item ->{
                        item.setFillDayId(p.getId());
                    });
                    allWorkHourFillDetailList.addAll(detaiList);
                }
            });
        }
        LambdaQueryWrapperX<WorkHourFillDetail> detailDeleteWrapper = new LambdaQueryWrapperX<>();
        List<String> deleteDetailIds = oldWorkHourFillDayList.stream().map(WorkHourFillDay :: getId).collect(Collectors.toList());
        detailDeleteWrapper.in(WorkHourFillDetail :: getFillDayId,deleteDetailIds);

        if(!CollectionUtils.isBlank(allWorkHourFillDetailList)){
            List<WorkHourFillDetail> allFillDetailList =  BeanCopyUtils.convertListTo(allWorkHourFillDetailList, WorkHourFillDetail::new);
           List<WorkHourFillDetail> updateFillDetail =  allFillDetailList.stream().filter(p -> StringUtils.hasText(p.getId())).collect(Collectors.toList());
            List<WorkHourFillDetail> insertFillDetail =  allFillDetailList.stream().filter(p -> !StringUtils.hasText(p.getId())).collect(Collectors.toList());
            if(!CollectionUtils.isBlank(updateFillDetail)){
                List<String> updateIds =  updateFillDetail.stream().map(WorkHourFillDetail :: getId).collect(Collectors.toList());
                detailDeleteWrapper.notIn(WorkHourFillDetail :: getId,updateIds);
                workHourFillDetailRepository.delete(detailDeleteWrapper);
                workHourFillDetailRepository.updateBatch(updateFillDetail,updateFillDetail.size());
            }
            else{
                workHourFillDetailRepository.delete(detailDeleteWrapper);
            }
            if(!CollectionUtils.isBlank(insertFillDetail)){
                workHourFillDetailRepository.insertBatch(insertFillDetail);
            }
        }
        else{
            workHourFillDetailRepository.delete(detailDeleteWrapper);
        }

        if(!CollectionUtils.isBlank(insertWorkHourFillDayDTOList)){
            List<WorkHourFillDay> insertWorkHourFillDayList =  BeanCopyUtils.convertListTo(insertWorkHourFillDayDTOList, WorkHourFillDay::new);
            workHourFillDayRepository.insertBatch(insertWorkHourFillDayList);
        }

        if(!CollectionUtils.isBlank(updateWorkHourFillDayDTOList)){
            List<WorkHourFillDay> updateWorkHourFillDayList =  BeanCopyUtils.convertListTo(updateWorkHourFillDayDTOList, WorkHourFillDay::new);
            workHourFillDayRepository.updateBatch(updateWorkHourFillDayList, updateWorkHourFillDayList.size());
        }

        if(!CollectionUtils.isBlank(deleteWorkHourFillDayList)){
            workHourFillDayRepository.deleteBatchIds(deleteWorkHourFillDayList.stream().map(WorkHourFillDay :: getId).collect(Collectors.toList()));
        }
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        SimpleUser simpleUser = userRedisHelper.getSimpleUserById(currentUserId);
        if(simpleUser != null){
            String title = simpleUser.getName() + startWorkHourFillDay.getWorkDate() +"至" + endWorkHourFillDay.getWorkDate() + "的工作填报";
            workHourFillDTO.setTitle(title);
        }
        int workHourTotal = workHourFillDayDTOList.stream().filter(p -> p.getWorkHour() != null).mapToInt(WorkHourFillDayDTO:: getWorkHour).sum();
        workHourFill.setStartDate(startWorkHourFillDay.getWorkDate());
        workHourFill.setEndDate(endWorkHourFillDay.getWorkDate());
        workHourFill.setWorkHour(workHourTotal);
        workHourFill.setMemberId(CurrentUserHelper.getCurrentUserId());
        workHourFill.setType(WorkHourFillTypeEnum.WORK_HOUR.getCode());
        workHourFill.setWorkHourType(WorkHourTypeEnum.PROJECT.getCode());
        int update =  workHourFillRepository.updateById(workHourFill);
        return SqlHelper.retBool(update);
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        List<WorkHourFill> workHourFillList = workHourFillRepository.selectList(WorkHourFill::getId, ids);
        if (CollectionUtils.isBlank(workHourFillList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "工时填报记录不存在或被删除!");
        }
        if (workHourFillList.stream().filter(item -> !item.getStatus().equals(WorkHourFillStatusEnum.CREATED.getStatus())).findAny().isPresent()) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "工时填报记录当前状态不能删除!");
        }

        List<WorkHourFillDay> workHourFillDayList = workHourFillDayRepository.selectList(WorkHourFillDay::getFillId, ids);
        List<String> filleDays = workHourFillDayList.stream().map(WorkHourFillDay :: getId).collect(Collectors.toList());

        if(!CollectionUtils.isBlank(filleDays)){
            LambdaQueryWrapperX<WorkHourFillDetail> detailLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            detailLambdaQueryWrapperX.in(WorkHourFillDetail :: getFillDayId,filleDays);
            workHourFillDetailRepository.delete(detailLambdaQueryWrapperX);
        }

        if(!CollectionUtils.isBlank(ids)){
            LambdaQueryWrapperX<WorkHourFillDay> fillDayLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            fillDayLambdaQueryWrapperX.in(WorkHourFillDay :: getFillId,ids);
            workHourFillDayRepository.delete(fillDayLambdaQueryWrapperX);
        }


        int delete = workHourFillRepository.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    public Page<WorkHourFillVO> pages(Page<WorkHourFillPageDTO> pageRequest) throws Exception {
        Page<WorkHourFill> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), WorkHourFill::new));

        WorkHourFill workHourFill = realPageRequest.getQuery();
        if(workHourFill == null){
            workHourFill = new WorkHourFill();
        }
        if(!StringUtils.hasText(workHourFill.getProjectId())){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "项目id不能为空!");
        }

        LambdaQueryWrapperX<WorkHourFill> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(WorkHourFill :: getProjectId,workHourFill.getProjectId());
        lambdaQueryWrapperX.eq(WorkHourFill :: getMemberId,CurrentUserHelper.getCurrentUserId());
        SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(),lambdaQueryWrapperX);
        PageResult<WorkHourFill> page = workHourFillRepository.selectPage(realPageRequest,lambdaQueryWrapperX);

        Page<WorkHourFillVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<WorkHourFillVO> vos = BeanCopyUtils.convertListTo(page.getContent(), WorkHourFillVO::new);
        if(!CollectionUtils.isBlank(vos)){
            List<String> businessIds = vos.stream().map(WorkHourFillVO :: getId).collect(Collectors.toList());
            ResponseDTO<List<ProcessInstanceAssigneeListVO>> responseDTO = workflowFeignService.assigneeList(businessIds);
            if(ResponseUtils.fail(responseDTO)){
                throw new PMSException(PMSErrorCode.PMS_ERR, responseDTO.getMessage());
            }
            List<ProcessInstanceAssigneeListVO> processInstanceAssigneeListVOS = responseDTO.getResult();
            if(!CollectionUtils.isBlank(processInstanceAssigneeListVOS)){
                Map<String,ProcessInstanceAssigneeListVO> assigneeListVOMap = processInstanceAssigneeListVOS.stream()
                        .collect(Collectors.toMap(ProcessInstanceAssigneeListVO :: getBusinessId,Function.identity(),(k1,k2)->k2));
                for(WorkHourFillVO workHourFillVO : vos){
                    ProcessInstanceAssigneeListVO processInstanceAssigneeListVO = assigneeListVOMap.get(workHourFillVO.getId());
                    if(processInstanceAssigneeListVO != null){
                       List<String> assignessIds = processInstanceAssigneeListVO.getAllAssigneeIds();
                        if(!CollectionUtils.isBlank(assignessIds)){
                            List<UserVO> userVOList = userRedisHelper.getUserByIds(assignessIds);
                            if(!CollectionUtils.isBlank(userVOList)){
                                String assigneeName = String.join(",", userVOList.stream().map(UserVO :: getName).collect(Collectors.toList()));
                                workHourFillVO.setAssigneeName(assigneeName);
                            }
                        }
                    }
                }
            }
        }

        pageResult.setContent(vos);

        return pageResult;
    }
}
