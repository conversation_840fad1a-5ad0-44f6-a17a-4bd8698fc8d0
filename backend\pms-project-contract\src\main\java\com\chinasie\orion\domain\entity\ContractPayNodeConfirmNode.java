package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ContractPayNodeConfirmNode Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-26 21:48:50
 */
@TableName(value = "pms_contract_pay_node_confirm_node")
@ApiModel(value = "ContractPayNodeConfirmNode对象", description = "项目合同支付节点确认节点信息")
@Data
public class ContractPayNodeConfirmNode extends ObjectEntity implements Serializable {

    /**
     * 确认id
     */
    @ApiModelProperty(value = "确认id")
    @TableField(value = "confirm_id")
    private String confirmId;

    /**
     * 节点id
     */
    @ApiModelProperty(value = "节点id")
    @TableField(value = "node_id")
    private String nodeId;

}
