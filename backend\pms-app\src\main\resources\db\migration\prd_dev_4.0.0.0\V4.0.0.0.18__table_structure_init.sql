ALTER TABLE `pms_requirement_mangement`
    ADD COLUMN `is_published` varchar(255) NULL COMMENT '是否发布公示' AFTER `undertake_dept`;

ALTER TABLE `pms_requirement_mangement`
    ADD COLUMN `ecp_group` varchar(255) NULL COMMENT '来源ecp组织' AFTER `is_published`,
ADD COLUMN `ecp_group_name` varchar(255) NULL COMMENT '来源ecp组织名称' AFTER `ecp_group`;

ALTER TABLE `pmsx_req_clarification_record`
    ADD COLUMN `clarification_stage` varchar(255) NULL COMMENT '澄清阶段' AFTER `new_quote_start_time`;

CREATE TABLE `pmsx_requirement_pay_mangement` (
                                                  `id` varchar(64) NOT NULL  COMMENT '主键',
                                                  `class_name` varchar(64)   COMMENT '创建人',
                                                  `creator_id` varchar(64) NOT NULL  COMMENT '创建人',
                                                  `modify_time` datetime NOT NULL  COMMENT '修改时间',
                                                  `owner_id` varchar(64)   COMMENT '拥有者',
                                                  `create_time` datetime NOT NULL  COMMENT '创建时间',
                                                  `modify_id` varchar(64) NOT NULL  COMMENT '修改人',
                                                  `remark` varchar(1024)   COMMENT '备注',
                                                  `platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
                                                  `org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
                                                  `status` int NOT NULL  COMMENT '状态',
                                                  `logic_status` int NOT NULL  COMMENT '逻辑删除字段',
                                                  `bond` decimal   COMMENT '保证金',
                                                  `tenders_end_time` datetime   COMMENT '截标时间',
                                                  `pay_way` varchar(64)   COMMENT '支付方式',
                                                  `apply_time` datetime   COMMENT '申请时间',
                                                  `payer` varchar(64)   COMMENT '付款人姓名',
                                                  `bid_validity` datetime  COMMENT '投标有效期',
                                                  `requirement_number` varchar(64)   COMMENT '需求编号',
                                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='需求支付信息';

