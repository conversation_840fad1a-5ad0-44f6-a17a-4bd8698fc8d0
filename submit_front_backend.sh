#!/bin/bash

# 主项目路径
PROJECT_DIR="/mnt/c/workspace/mpf"

# 前端和后端仓库信息
FRONTEND_DIR="$PROJECT_DIR/front"
BACKEND_DIR="$PROJECT_DIR/backend"

FRONTEND_REMOTE="**********************************************/gh/vue-pms.git"
BACKEND_REMOTE="************************************************/gh/pmsx.git"

# 设置全局安全目录（避免 dubious ownership 错误）
git config --global --add safe.directory "$PROJECT_DIR"
git config --global --add safe.directory "$FRONTEND_DIR"
git config --global --add safe.directory "$BACKEND_DIR"

cd "$PROJECT_DIR" || { echo "无法进入项目目录"; exit 1; }

echo "✅ 步骤 1：清理已有的 .git 文件..."
rm -rf "$FRONTEND_DIR/.git"
rm -rf "$BACKEND_DIR/.git"

echo "✅ 步骤 2：提交 front 到前端仓库..."
cd "$FRONTEND_DIR" || { echo "无法进入 $FRONTEND_DIR"; exit 1; }
git init
git remote add origin "$FRONTEND_REMOTE"
git add .
git commit -m "Initial commit from mpf project"
git push -f origin master

echo "✅ 步骤 3：提交 backend 到后端仓库..."
cd "$BACKEND_DIR" || { echo "无法进入 $BACKEND_DIR"; exit 1; }
git init
git remote add origin "$BACKEND_REMOTE"
git add .
git commit -m "Initial commit from mpf project"
git push -f origin master

echo "✅ 步骤 4：提交整个项目到主仓库（可选）..."
cd "$PROJECT_DIR" || { echo "无法进入主项目目录"; exit 1; }
rm -rf .git
git init
git checkout -b dev
git remote add origin http://milado1/snpi/jy_mpf.git || git remote set-url origin http://milado1/snpi/jy_mpf.git
git submodule add "$FRONTEND_REMOTE" front
git submodule add "$BACKEND_REMOTE" backend
git add .
git commit -m "Submodules: front and backend added"
git push -u origin dev

echo "🎉 所有操作已完成！"
echo "👉 front 已提交到: $FRONTEND_REMOTE"
echo "👉 backend 已提交到: $BACKEND_REMOTE"
echo "👉 整个项目已作为含子模块的形式提交到: http://milado1/snpi/jy_mpf.git (dev 分支)"
