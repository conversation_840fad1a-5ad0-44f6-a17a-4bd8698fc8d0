<template>
  <div class="card-wrap flex flex-ver">
    <div class="title">
      {{ contentTitle }}
      <div
        v-if="isMore"
        class="more flex flex-ac"
        @click="moreClick"
      >
        <span>更多</span>
        <Icon
          icon="fa-chevron-right"
          size="12"
        />
      </div>
    </div>
    <div class="flex-f1 card-content">
      <slot name="default" />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import Icon from '/@/components/Icon';

export default defineComponent({
  name: 'Card',
  components: {
    Icon,
  },
  props: {
    contentTitle: {
      type: String,
      default: '',
    },
    isMore: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['moreClick'],
  setup(_, { emit }) {
    return {
      moreClick() {
        emit('moreClick');
      },
    };
  },
});
</script>

<style scoped lang="less">
  .card-wrap {
    background: #fff;
    padding: 16px;
    border-radius: 3px;

    > .title {
      font-size: 18px;
      font-weight: bold;
      color: #19243b;
      padding-bottom: 10px;
      position: relative;

      > .more {
        font-size: 14px;
        font-weight: normal;
        color: #969eb4;
        position: absolute;
        right: 0;
        top: 0;
        cursor: pointer;

        &:hover {
          color: ~`getPrefixVar('primary-color')`;
        }
      }
    }

    > .card-content {
      overflow: auto;
    }
  }
</style>
