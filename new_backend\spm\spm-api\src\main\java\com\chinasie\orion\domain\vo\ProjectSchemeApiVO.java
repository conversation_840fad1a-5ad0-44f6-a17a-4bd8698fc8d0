package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/8/21
 */
@Data
@ApiModel(value = "ProjectSchemeVO对象", description = "项目计划")
public class ProjectSchemeApiVO extends ObjectVO implements Serializable {
    /**
     * 计划编号
     */
    @ApiModelProperty(value = "计划编号")
    private String schemeNumber;

    /**
     *
     */
    @ApiModelProperty(value = "排序")
    private Long sort;
    /**
     *
     */
    @ApiModelProperty(value = "计划名称")
    private String name;
    /**
     *
     */
    @ApiModelProperty(value = "icon")
    private String icon;
    /**
     *
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;
    /**
     *
     */
    @ApiModelProperty(value = "项目编号")
    private String projectNumber;

    @ApiModelProperty(value = "项目名称")
    private String projectName;
    /**
     *
     */
    @ApiModelProperty(value = "层级")
    private Integer level;

    @ApiModelProperty(value = "层级名称")
    private String levelName;
    /**
     *
     */
    @ApiModelProperty(value = "父id")
    private String parentId;
    @ApiModelProperty(value = "父名称")
    private String parentName;
    /**
     *
     */
    @ApiModelProperty(value = "父级链")
    private String parentChain;
    /**
     *
     */
    @ApiModelProperty(value = "计划类型")
    private Integer type;

    @ApiModelProperty(value = "计划类型名称(计划，里程碑)")
    private String typeName;
    /**
     *
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "状态")
    private String statusName;
    /**
     *
     */
    @ApiModelProperty(value = "责任科室")
    private String rspSectionId;
    /**
     *
     */
    @ApiModelProperty(value = "责任科室姓名")
    private String rspSectionName;
    /**
     *
     */
    @ApiModelProperty(value = "责任处室")
    private String rspSubDept;

    @ApiModelProperty(value = "责任处室名称")
    private String rspSubDeptName;
    @ApiModelProperty(value = "责任处室编码")
    private String rspSubDeptCode;
    /**
     *
     */
    @ApiModelProperty(value = "责任人")
    private String rspUser;

    @ApiModelProperty(value = "责任人编号")
    private String rspUserCode;

    @ApiModelProperty(value = "责任人名称")
    private String rspUserName;
    /**
     *
     */
    @ApiModelProperty(value = "计划开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date beginTime;
    /**
     *
     */
    @ApiModelProperty(value = "计划结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;
    /**
     *
     */
    @ApiModelProperty(value = "计划情况")
    private Integer circumstance;

    @ApiModelProperty(value = "计划情况名称")
    private String circumstanceName;
    /**
     *
     */
    @ApiModelProperty(value = "实际结束时间")
    private Date actualEndTime;
    /**
     *
     */
    @ApiModelProperty(value = "实际开始时间")
    private Date actualBeginTime;

    @ApiModelProperty(value = "项目计划描述")
    private String schemeDesc;

    @ApiModelProperty(value = "置顶序号（0：取消置顶，其他根据序号排列置顶计划）")
    private Integer topSort;

    @ApiModelProperty(value = "子级数量")
    private Integer childrenCount;

    /**
     * 计划进度
     */
    @ApiModelProperty(value = "计划进度")
    private BigDecimal schedule;

    /**
     * 是否变更审批
     */
    @ApiModelProperty(value = "是否变更审批")
    private Boolean schemeApprove;

    /**
     * 审核状态
     */
    private Integer approveStatus;
    /**
     * 是否前置关系
     */
    @ApiModelProperty(value = "是否前置关系")
    private Boolean isPrePost;

    @ApiModelProperty(value = "计划下发时间")
    private Date issueTime;

    @ApiModelProperty(value = "是否超时完成 1 是，0 否")
    private Boolean delayEndFlag;

    @ApiModelProperty(value = "超时原因")
    private String delayEndReason;


    @ApiModelProperty(value = "是否关联流程 1 是，0 否")
    private Boolean processFlag;

    @ApiModelProperty(value = "关联流程实例Id")
    private String processId;


    /**
     * 计划工期
     */
    @ApiModelProperty(value = "计划工期")
    private Integer durationDays;

    /**
     *
     */
    @ApiModelProperty(value = "计划类型")
    private String nodeType;

    @ApiModelProperty(value = "关联流程实例Id")
    private String formCodeId;


    @ApiModelProperty(value = "下达人组织")
    private String issuedDeptId;

    @ApiModelProperty(value = "下达人组织名称")
    private String issuedDeptName;

    @ApiModelProperty(value = "下达人")
    private String issuedUser;

    @ApiModelProperty(value = "下达人名称")
    private String issuedUserName;

    @ApiModelProperty(value = "参与人（拼接）")
    private String participantUsers;
    @ApiModelProperty(value = "参与人集合")
    private List<String> participantUserList = new ArrayList<>();

    @ApiModelProperty(value = "参与人名称（拼接）")
    private String participantUserNames;

    @ApiModelProperty(value = "审批人")
    private String examineUser;

    @ApiModelProperty(value = "审批人名称")
    private String examineUserName;
    @ApiModelProperty(value = "评分状态")
    private String examineType;

    @ApiModelProperty(value = "计划活动项")
    private String planActive;

    @ApiModelProperty(value = "计划活动项集合")
    private List<Map<String, String>> planActiveList;


    @ApiModelProperty(value = "计划活动项名称")
    private String planActiveName;

    @ApiModelProperty(value = "选择基线")
    private String baseLine;

    @ApiModelProperty(value = "选择基线名称")
    private String baseLineName;

    @ApiModelProperty(value = "转办理由")
    private String reasonTransfer;

    @ApiModelProperty(value = "产品id")
    private String productId;

    @ApiModelProperty(value = "设计任务编码")
    private String designTaskNumber;


    @ApiModelProperty(value = "标识")
    private String identification;


    @ApiModelProperty(value = "确认理由")
    private String reasonConfirmation;
    @ApiModelProperty(value = "确认结果")
    private String confirmResult;

    @ApiModelProperty(value = "暂停时间")
    private Date suspendTime;

    @ApiModelProperty(value = "终止时间")
    private Date terminateTime;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "暂停理由")
    private String suspendReason;

    @ApiModelProperty(value = "终止理由")
    private String terminateReason;

    @ApiModelProperty(value = "启动理由")
    private String startReason;

    @ApiModelProperty(value = "上个状态")
    private Integer lastStatus;


    @ApiModelProperty(value = "流程类型")
    private String processType;

    @ApiModelProperty(value = "是否是项目经理")
    private Boolean isManager;


    @ApiModelProperty(value = "累计填报天数")
    private String reportingDays;


    @ApiModelProperty(value = "是否作业")
    private Boolean isWorkJob;

    @ApiModelProperty(value = "实施类型")
    private String enforceType;

    @ApiModelProperty(value = "实施类型名称")
    private String enforceTypeName;
    @ApiModelProperty(value = "实施地点")
    private String enforceBasePlace;
    @ApiModelProperty(value = "实施地点名称")
    private String enforceBasePlaceName;

    @ApiModelProperty(value = "实施区域/范围")
    private String enforceScope;

    @ApiModelProperty(value = "实施区域名称")
    private String enforceScopeName;

    @ApiModelProperty(value = "工作内容")
    private String workContent;
    @ApiModelProperty(value = "工作内容名称")
    private String workContentName;

    @ApiModelProperty(value = "大修轮次")
    private String repairRound;

    @ApiModelProperty(value = "是否携带物资")
    private Boolean isCarryMaterials;

    @ApiModelProperty(value = "逾期天数")
    private Long overdueDays;

    @ApiModelProperty(value = "紧急程度")
    private String  urgency;

    @ApiModelProperty(value = "是否作业 -1：否 1：是")
    private Integer isWork;

    @ApiModelProperty(value = "合同里程碑id")
    private String contractMilestoneId;

}
