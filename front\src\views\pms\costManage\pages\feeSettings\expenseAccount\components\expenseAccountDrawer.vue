<template>
  <BasicDrawer
    width="50%"
    :title="state.drawerName"
    :showFooter="true"
    @ok="confirmDrawer"
    @register="modalRegister"
    @visibleChange="visibleChange"
  >
    <BasicForm
      v-if="state.showForm"
      ref="formRef"
      @register="registerForm"
    />
  </BasicDrawer>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from 'vue';
import {
  BasicDrawer, useDrawerInner, BasicForm, FormSchema, useForm,
} from 'lyra-component-vue3';
import Api from '/@/api';
import { message } from 'ant-design-vue';

const emit = defineEmits(['updatePage']);
const formRef = ref();
const state = reactive({
  actionType: '',
  drawerName: '',
  formData: {},
  visibleStatus: false,
  showForm: true,
  id: '',
});
const [modalRegister, { closeDrawer, changeOkLoading }] = useDrawerInner(
  (openProps: { type, formData }) => {
    state.actionType = openProps.type;
    if (openProps.type === 'edit') {
      state.drawerName = '编辑费用';
      // state.id = openProps.formData.parentId === '0' ? '' : openProps.formData.id;
      state.id = openProps.formData.id;
      formRef.value.setFieldsValue({
        number: openProps.formData.number,
        parentId: openProps.formData.parentId === '0' ? undefined : openProps.formData.parentId,
        name: openProps.formData.name,
        status: openProps.formData.status,
        sort: openProps.formData.sort,
      });
    } else {
      state.drawerName = '新增费用';
    }
    // 设置为已打开状态
    state.visibleStatus = true;
  },
);

function visibleChange(visible: boolean) {
  // 窗口关闭时，设置状态值
  !visible && (state.visibleStatus = visible);
  if (!visible) {
    resetForm();
  }
}

// 远程菜单树处理
/** 全部菜单数据处理 */
function parseTreeSelect(menuList) {
  const res = [];
  for (let i in menuList) {
    let tmp = {
      label: menuList[i]?.name,
      value: menuList[i]?.id,
    };
    if (menuList[i].children) {
      tmp.children = parseTreeSelect(menuList[i].children);
    }
    res.push(tmp);
  }
  return res;
}

const schemas: FormSchema[] = [
  {
    field: 'number',
    component: 'Input',
    label: '费用科目编码',
    required: true,
    dynamicDisabled: () => state.drawerName === '编辑费用',
  },
  {
    field: 'parentId',
    component: 'ApiTreeSelect',
    label: '父级费用科目',
    componentProps: {
      placeholder: '默认为空，即默认创建顶层数据',
      // immediate: true,
      api: () => new Api('/pms/expenseSubject/tree').fetch('', '', 'GET').then((res) => parseTreeSelect(res)),
      labelField: 'label',
      valueField: 'value',
    },

  },
  {
    field: 'name',
    component: 'Input',
    label: '费用科目名称',
    rules: [
      {
        required: true,
        trigger: 'change',
        type: 'string',
      },
    ],
  },
  {
    field: 'status',
    component: 'Select',
    label: '状态',
    defaultValue: 1,
    rules: [
      {
        required: true,
        type: 'number',
      },
    ],
    componentProps: {
      options: [
        {
          label: '启用',
          value: 1,
        },
        {
          label: '禁用',
          value: 0,
        },
      ],
    },
  },
  {
    field: 'sort',
    component: 'Input',
    label: '排序值',
    componentProps: {
      placeholder: '设置0-999任意值，进行同级别排序',
    },
  },
];

const [
  registerForm,
  {
    validate, resetFields, getFieldsValue, setFieldsValue,
  },
] = useForm({
  layout: 'vertical',
  schemas,
  baseColProps: {
    span: 24,
  },
  expandIconColumnIndex: '1',
  expandIconAsCell: 'false',
});

function resetForm() {
  formRef.value.resetFields();
}

async function confirmDrawer() {
  await formRef.value.validate();
  const values = await formRef.value.getFieldsValue();
  changeOkLoading(true);
  if (state.drawerName === '编辑费用') {
    const data = {
      id: state.id,
      parentId: values.parentId ? values.parentId : '0',
      name: values.name,
      status: values.status,
      number: values.number,
      sort: values.sort,
    };
    await new Api('/pms/expenseSubject').fetch(data, '', 'PUT').then(() => {

    }).finally(() => {
      changeOkLoading(false);
    });
  } else {
    const data = {
      parentId: values.parentId ? values.parentId : '0',
      name: values.name,
      status: values.status,
      number: values.number,
      sort: values.sort,
    };
    await new Api('/pms/expenseSubject').fetch(data, '', 'POST').then(async () => {

    }).finally(() => {
      changeOkLoading(false);
    });
  }
  closeDrawer();
  emit('updatePage');// 更新父组件数据
}

</script>
