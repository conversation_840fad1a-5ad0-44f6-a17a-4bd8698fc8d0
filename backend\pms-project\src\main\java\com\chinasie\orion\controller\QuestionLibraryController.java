package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.QuestionLibraryDTO;
import com.chinasie.orion.domain.dto.QuestionLibraryPushDTO;
import com.chinasie.orion.domain.vo.QuestionLibraryVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.QuestionLibraryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.dto.ResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * QuestionLibrary 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-17 13:36:10
 */
@RestController
@RequestMapping("/questionLibrary")
@Api(tags = "问题库")
public class QuestionLibraryController {

    @Autowired
    private QuestionLibraryService questionLibraryService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据", type = "问题库", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<QuestionLibraryVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        QuestionLibraryVO rsp = questionLibraryService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param questionLibraryDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#questionLibraryDTO.name}}】", type = "问题库", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody QuestionLibraryDTO questionLibraryDTO) throws Exception {
        String rsp =  questionLibraryService.create(questionLibraryDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 批量新增
     *
     * @param ids
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "批量新增")
    @RequestMapping(value = "/add/batch", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#riskLibraryDTO.name}}】", type = "问题库", subType = "批量新增", bizNo = "{{#id}}")
    public ResponseDTO createBatch(@RequestBody List<String> ids) throws Exception {
        return new ResponseDTO<>(questionLibraryService.createBatch(ids));
    }

    /**
     * 编辑
     *
     * @param questionLibraryDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#questionLibraryDTO.name}}】", type = "问题库", subType = "编辑", bizNo = "{{#questionLibraryDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  QuestionLibraryDTO questionLibraryDTO) throws Exception {
        Boolean rsp = questionLibraryService.edit(questionLibraryDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "问题库", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = questionLibraryService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "问题库", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = questionLibraryService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "问题库", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<QuestionLibraryVO>> pages(@RequestBody Page<QuestionLibraryDTO> pageRequest) throws Exception {
        Page<QuestionLibraryVO> rsp =  questionLibraryService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }




    @ApiOperation("问题库导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "问题库", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        questionLibraryService.exportByExcel(searchConditions, response);
    }


    /**
     * 禁用
     *
     * @param ids
     * @return
     */
    @ApiOperation("批量禁用")
    @RequestMapping(value = "/ban/batch", method = {RequestMethod.PUT})
    @LogRecord(success = "【{USER{#logUserId}}】禁用了数据", type = "问题库", subType = "批量禁用", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> ban(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = questionLibraryService.banBatch(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 启用
     *
     * @param ids
     * @return
     */
    @ApiOperation("批量启用")
    @RequestMapping(value = "/use/batch", method = {RequestMethod.PUT})
    @LogRecord(success = "【{USER{#logUserId}}】启用了数据", type = "问题库", subType = "批量启用", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> use(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = questionLibraryService.useBatch(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 问题推送
     *
     * @param questionLibraryPushDTO
     * @return
     */
    @ApiOperation("问题推送")
    @RequestMapping(value = "/push", method = {RequestMethod.POST})
    @LogRecord(success = "【{USER{#logUserId}}】推送了数据", type = "问题库", subType = "推送", bizNo = "")
    public ResponseDTO<Boolean> questionPush(@RequestBody QuestionLibraryPushDTO questionLibraryPushDTO) throws Exception {
        Boolean rsp = questionLibraryService.questionLibraryPush(questionLibraryPushDTO);
        return new ResponseDTO<>(rsp);
    }

}
