<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
   <modelVersion>4.0.0</modelVersion>
   <groupId>org.jboss.weld</groupId>
   <artifactId>weld-parent</artifactId>
   <packaging>pom</packaging>
   <version>6</version>

   <name>Weld Parent</name>

   <!-- Full metadata -->

   <url>http://www.seamframework.org/Weld</url>

   <description>
      The parent POM for Weld, specifying the build parameters
   </description>

   <licenses>
      <license>
         <name>Apache License, Version 2.0</name>
         <url>http://www.apache.org/licenses/LICENSE-2.0</url>
      </license>
   </licenses>

   <developers>
      <developer>
         <name>Weld committers</name>
      </developer>
   </developers>

   <organization>
      <name>Seam Framework</name>
      <url>http://seamframework.org</url>
   </organization>

   <pluginRepositories>
      <pluginRepository>
         <id>twdata-m2-repository.googlecode.com</id>
         <url>http://twdata-m2-repository.googlecode.com/svn</url>
         <releases>
            <enabled>true</enabled>
         </releases>
         <snapshots>
            <enabled>false</enabled>
         </snapshots>
      </pluginRepository>
      <pluginRepository>
         <id>repository.jboss.org</id>
         <url>http://repository.jboss.org/maven2</url>
         <releases>
            <enabled>true</enabled>
         </releases>
         <snapshots>
            <enabled>false</enabled>
         </snapshots>
      </pluginRepository>
   </pluginRepositories>

   <repositories>
      <repository>
         <id>oss.sonatype.org/jboss-snapshots</id>
         <name>JBoss (Nexus) Snapshots Repository</name>
         <url>http://oss.sonatype.org/content/repositories/jboss-snapshots</url>
         <releases>
            <enabled>false</enabled>
         </releases>
         <snapshots>
            <enabled>true</enabled>
            <updatePolicy>never</updatePolicy>
         </snapshots>
      </repository>
   </repositories>

   <properties>
      <!-- Force encoding to UTF-8 (bug on OS X) -->
      <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
      <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
      <weld.docbook.version>1.1.1-Beta3</weld.docbook.version>
      <seam.docbook.version>1.1.0.GA</seam.docbook.version>
      <jbossorg.docbook.version>1.1.0</jbossorg.docbook.version>
   </properties>

   <!-- Configure the build -->
   <build>
      <plugins>
         <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <executions>
               <execution>
                  <id>attach-sources</id>
                  <goals>
                     <goal>jar</goal>
                  </goals>
               </execution>
            </executions>
         </plugin>
         <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-enforcer-plugin</artifactId>
         </plugin>
         <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>build-helper-maven-plugin</artifactId>
         </plugin>
         <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>buildnumber-maven-plugin</artifactId>
         </plugin>
      </plugins>
      <defaultGoal>package</defaultGoal>

      <!-- Configure all plugins, including versions to use in the build -->
      <pluginManagement>
         <plugins>
            <plugin>
               <groupId>ch.qos.cal10n.plugins</groupId>
               <artifactId>maven-cal10n-plugin</artifactId>
               <version>0.7</version>
            </plugin>
            <plugin>
               <groupId>org.jboss.maven.plugins</groupId>
               <artifactId>maven-jdocbook-plugin</artifactId>
               <version>2.2.0</version>
               <extensions>true</extensions>
               <dependencies>
                  <dependency>
                     <groupId>org.jboss.weld</groupId>
                     <artifactId>weld-docbook-xslt</artifactId>
                     <version>${weld.docbook.version}</version>
                  </dependency>
                  <dependency>
                     <groupId>org.jboss.seam</groupId>
                     <artifactId>seam-docbook-xslt</artifactId>
                     <version>${seam.docbook.version}</version>
                  </dependency>
                  <dependency>
                     <groupId>org.jboss.seam</groupId>
                     <artifactId>seam-jdocbook-style</artifactId>
                     <version>${seam.docbook.version}</version>
                     <type>jdocbook-style</type>
                  </dependency>
                  <dependency>
                     <groupId>org.jboss</groupId>
                     <artifactId>jbossorg-jdocbook-style</artifactId>
                     <version>${jbossorg.docbook.version}</version>
                     <type>jdocbook-style</type>
                  </dependency>
               </dependencies>
               <configuration>
                  <sourceDirectory>${pom.basedir}</sourceDirectory>
                  <sourceDocumentName>master.xml</sourceDocumentName>
                  <masterTranslation>en-US</masterTranslation>
                  <imageResource>
                     <directory>${pom.basedir}/en-US</directory>
                     <includes>
                        <include>images/*.png</include>
                     </includes>
                  </imageResource>
                  <formats>
                     <format>
                        <formatName>pdf</formatName>
                        <stylesheetResource>classpath:/xslt/org/jboss/weld/pdf.xsl</stylesheetResource>
                        <finalName>${pdf.name}</finalName>
                     </format>
                     <format>
                        <formatName>html</formatName>
                        <stylesheetResource>classpath:/xslt/org/jboss/weld/xhtml.xsl</stylesheetResource>
                        <finalName>index.html</finalName>
                     </format>
                     <format>
                        <formatName>html_single</formatName>
                        <stylesheetResource>classpath:/xslt/org/jboss/weld/xhtml-single.xsl</stylesheetResource>
                        <finalName>index.html</finalName>
                     </format>
                  </formats>
                  <options>
                     <xincludeSupported>true</xincludeSupported>
                     <xmlTransformerType>saxon</xmlTransformerType>
                     <docbookVersion>1.72.0</docbookVersion>
                     <localeSeparator>-</localeSeparator>
                  </options>
               </configuration>
            </plugin>
            <plugin>
               <groupId>org.apache.maven.plugins</groupId>
               <artifactId>maven-antrun-plugin</artifactId>
               <version>1.3</version>
            </plugin>
            <plugin>
               <groupId>org.apache.maven.plugins</groupId>
               <artifactId>maven-release-plugin</artifactId>
               <configuration>
                  <arguments>-Drelease </arguments>
                  <autoVersionSubmodules>true</autoVersionSubmodules>
               </configuration>
               <version>2.0-beta-9</version>
            </plugin>
            <plugin>
               <groupId>org.apache.maven.plugins</groupId>
               <artifactId>maven-surefire-report-plugin</artifactId>
               <version>2.4.3</version>
            </plugin>
            <plugin>
               <groupId>org.apache.maven.plugins</groupId>
               <artifactId>maven-surefire-plugin</artifactId>
               <version>2.4.3</version>
            </plugin>
            <plugin>
               <groupId>org.apache.maven.plugins</groupId>
               <artifactId>maven-clean-plugin</artifactId>
               <version>2.3</version>
            </plugin>
            <plugin>
               <groupId>org.apache.maven.plugins</groupId>
               <artifactId>maven-deploy-plugin</artifactId>
               <version>2.4</version>
            </plugin>
            <plugin>
               <groupId>org.apache.maven.plugins</groupId>
               <artifactId>maven-assembly-plugin</artifactId>
               <version>2.2-beta-4</version>
            </plugin>
            <plugin>
               <groupId>org.apache.maven.plugins</groupId>
               <artifactId>maven-install-plugin</artifactId>
               <version>2.3</version>
            </plugin>
            <plugin>
               <groupId>org.apache.maven.plugins</groupId>
               <artifactId>maven-site-plugin</artifactId>
               <version>2.0.1</version>
            </plugin>
            <plugin>
               <groupId>org.apache.maven.plugins</groupId>
               <artifactId>maven-source-plugin</artifactId>
               <version>2.0.4</version>
            </plugin>
            <plugin>
               <groupId>org.apache.maven.plugins</groupId>
               <artifactId>maven-resources-plugin</artifactId>
               <version>2.4.1</version>
            </plugin>
            <plugin>
               <groupId>org.apache.maven.plugins</groupId>
               <artifactId>maven-dependency-plugin</artifactId>
               <version>2.1</version>
            </plugin>
            <plugin>
               <groupId>org.apache.maven.plugins</groupId>
               <artifactId>maven-javadoc-plugin</artifactId>
               <version>2.4</version>
               <configuration>
                  <keywords>true</keywords>
               </configuration>
            </plugin>
            <plugin>
               <groupId>org.apache.maven.plugins</groupId>
               <artifactId>maven-gpg-plugin</artifactId>
               <version>1.0-alpha-4</version>
            </plugin>
            <plugin>
               <groupId>org.apache.felix</groupId>
               <artifactId>maven-bundle-plugin</artifactId>
               <version>2.0.0</version>
            </plugin>
            <plugin>
               <groupId>org.codehaus.mojo</groupId>
               <artifactId>exec-maven-plugin</artifactId>
               <version>1.1.1</version>
            </plugin>
            <plugin>
               <groupId>org.apache.maven.plugins</groupId>
               <artifactId>maven-enforcer-plugin</artifactId>
               <version>1.0-beta-1</version>
               <executions>
                  <execution>
                     <id>enforce</id>
                     <goals>
                        <goal>enforce</goal>
                     </goals>
                     <configuration>
                        <rules>
                           <requireMavenVersion>
                              <version>2.0.10</version>
                           </requireMavenVersion>
                           <requirePluginVersions>
                              <unCheckedPlugins>
                                 <unCheckedPlugin>org.apache.maven.plugins:maven-eclipse-plugin</unCheckedPlugin>
                              </unCheckedPlugins>
                           </requirePluginVersions>
                        </rules>
                     </configuration>
                  </execution>
               </executions>
            </plugin>
            <plugin>
               <groupId>org.apache.maven.plugins</groupId>
               <artifactId>maven-shade-plugin</artifactId>
               <version>1.2.1</version>
            </plugin>
            <plugin>
               <groupId>org.apache.maven.plugins</groupId>
               <artifactId>maven-compiler-plugin</artifactId>
               <version>2.0.2</version>
               <configuration>
                  <source>1.5</source>
                  <target>1.5</target>
               </configuration>
            </plugin>
            <plugin>
               <groupId>org.apache.maven.plugins</groupId>
               <artifactId>maven-jar-plugin</artifactId>
               <version>2.2</version>
               <configuration>
                  <archive>
                     <manifestEntries>
                        <Implementation-URL>${pom.url}</Implementation-URL>
                        <Implementation-Title>${pom.name}</Implementation-Title>
                        <Implementation-Version>${parsedVersion.osgiVersion}</Implementation-Version>
                        <Implementation-Vendor>${pom.organization.name}</Implementation-Vendor>
                        <Specification-Title>${pom.name}</Specification-Title>
                        <Specification-Version>${parsedVersion.osgiVersion}</Specification-Version>
                        <Specification-Vendor>${pom.organization.name}</Specification-Vendor>
                     </manifestEntries>
                     <manifestSections>
                        <manifestSection>
                           <name>Build-Information</name>
                           <manifestEntries>
                              <Maven-Version>${maven.version}</Maven-Version>
                              <Java-Version>${java.version}</Java-Version>
                              <Java-Vendor>${java.vendor}</Java-Vendor>
                              <Os-Name>${os.name}</Os-Name>
                              <Os-Arch>${os.arch}</Os-Arch>
                              <Os-Version>${os.version}</Os-Version>
                              <SCM>r${buildNumber}</SCM>
                              <Build-Time>${timestamp}</Build-Time>
                           </manifestEntries>
                        </manifestSection>
                     </manifestSections>
                  </archive>
               </configuration>
            </plugin>
            <plugin>
               <groupId>org.codehaus.mojo</groupId>
               <artifactId>build-helper-maven-plugin</artifactId>
               <version>1.4</version>
               <executions>
                  <execution>
                     <phase>validate</phase>
                     <goals>
                        <goal>maven-version</goal>
                        <goal>parse-version</goal>
                     </goals>
                  </execution>
               </executions>
            </plugin>
            <plugin>
               <groupId>org.apache.maven.plugins</groupId>
               <artifactId>maven-eclipse-plugin</artifactId>
               <configuration>
                  <classpathContainers>
                     <classpathContainer>org.eclipse.jdt.launching.JRE_CONTAINER</classpathContainer>
                  </classpathContainers>
               </configuration>
            </plugin>
            <plugin>
               <groupId>org.codehaus.mojo</groupId>
               <artifactId>buildnumber-maven-plugin</artifactId>
               <version>1.0-beta-3</version>
               <executions>
                  <execution>
                     <id>set-build-properties</id>
                     <goals>
                        <goal>create</goal>
                     </goals>
                     <phase>validate</phase>
                     <configuration>
                        <getRevisionOnlyOnce>true</getRevisionOnlyOnce>
                        <revisionOnScmFailure>unavailable</revisionOnScmFailure>
                        <timestampFormat>{0, date, long} {0, time, long}</timestampFormat>
                     </configuration>
                  </execution>
               </executions>
               <inherited>true</inherited>
            </plugin>
            <plugin>
               <groupId>org.twdata.maven</groupId>
               <artifactId>maven-cli-plugin</artifactId>
               <version>0.6.9</version>
            </plugin>
            <plugin>
               <groupId>org.codehaus.mojo</groupId>
               <artifactId>tomcat-maven-plugin</artifactId>
               <version>1.0-beta-1</version>
            </plugin>
            <plugin>
               <groupId>org.apache.maven.plugins</groupId>
               <artifactId>maven-war-plugin</artifactId>
               <version>2.1-beta-1</version>
            </plugin>
            <plugin>
               <groupId>org.apache.maven.plugins</groupId>
               <artifactId>maven-ejb-plugin</artifactId>
               <version>2.2</version>
            </plugin>
            <plugin>
               <groupId>org.apache.maven.plugins</groupId>
               <artifactId>maven-ear-plugin</artifactId>
               <version>2.3.2</version>
            </plugin>
            <plugin>
               <groupId>org.mortbay.jetty</groupId>
               <artifactId>maven-jetty-plugin</artifactId>
               <version>6.1.21</version>
            </plugin>
            <plugin>
               <groupId>com.pyx4j</groupId>
               <artifactId>maven-junction-plugin</artifactId>
               <version>1.0.3</version>
            </plugin>
            <plugin>
               <groupId>org.sonatype.plugins</groupId>
               <artifactId>nexus-maven-plugin</artifactId>
               <version>1.3.2</version>
               <configuration>
                  <nexusUrl>http://oss.sonatype.org</nexusUrl>
                  <serverAuthId>oss.sonatype.org/jboss-staging</serverAuthId>
               </configuration>
            </plugin>
         </plugins>
      </pluginManagement>
   </build>

   <profiles>
      <!-- The release profile -->
      <profile>
         <id>release</id>
         <activation>
            <property>
               <name>release</name>
            </property>
         </activation>
         <build>
            <plugins>
               <plugin>
                  <groupId>org.apache.maven.plugins</groupId>
                  <artifactId>maven-gpg-plugin</artifactId>
                  <configuration>
                     <passphrase>${gpg.passphrase}</passphrase>
                     <useAgent>${gpg.useAgent}</useAgent>
                  </configuration>
                  <executions>
                     <execution>
                        <id>sign-artifacts</id>
                        <phase>verify</phase>
                        <goals>
                           <goal>sign</goal>
                        </goals>
                     </execution>
                  </executions>
               </plugin>
               <plugin>
                  <groupId>org.sonatype.plugins</groupId>
                  <artifactId>nexus-maven-plugin</artifactId>
                  <configuration>
                     <auto>true</auto>
                     <description>[nexus-maven-plugin] closing repository after release:perform</description>
                  </configuration>
               </plugin>
               <plugin>
                  <groupId>org.codehaus.mojo</groupId>
                  <artifactId>buildnumber-maven-plugin</artifactId>
                  <executions>
                     <execution>
                        <id>validate-scm</id>
                        <goals>
                           <goal>create</goal>
                        </goals>
                        <phase>validate</phase>
                        <configuration>
                           <doUpdate>true</doUpdate>
                           <doCheck>true</doCheck>
                        </configuration>
                     </execution>
                  </executions>
                  <inherited>true</inherited>
               </plugin>
            </plugins>
         </build>
      </profile>
   </profiles>

   <!-- SCM and Distribution Management -->

   <scm>
      <connection>scm:svn:http://anonsvn.jboss.org/repos/weld/build/tags/weld-parent-6</connection>
      <developerConnection>scm:svn:https://svn.jboss.org/repos/weld/build/tags/weld-parent-6</developerConnection>
      <url>http://fisheye.jboss.org/browse/Weld/build/tags/weld-parent-6</url>
   </scm>

   <distributionManagement>
      <repository>
         <id>oss.sonatype.org/jboss-staging</id>
         <name>Sonatype Nexus Maven Repository</name>
         <url>http://oss.sonatype.org/service/local/staging/deploy/maven2</url>
      </repository>
      <snapshotRepository>
         <id>oss.sonatype.org/jboss-snapshots</id>
         <name>Sonatype Nexus Snapshot Repository</name>
         <url>http://oss.sonatype.org/content/repositories/jboss-snapshots</url>
      </snapshotRepository>
   </distributionManagement>

</project>
