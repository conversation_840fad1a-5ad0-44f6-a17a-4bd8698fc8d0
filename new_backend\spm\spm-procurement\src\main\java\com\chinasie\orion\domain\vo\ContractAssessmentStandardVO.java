package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ContractAssessmentStandard VO对象
 *
 * <AUTHOR>
 * @since 2024-10-17 09:29:14
 */
@ApiModel(value = "ContractAssessmentStandardVO对象", description = "审核标准表")
@Data
public class ContractAssessmentStandardVO extends  ObjectVO   implements Serializable{

            /**
         * 合同编号
         */
        @ApiModelProperty(value = "合同编号")
        private String contractNumber;


        /**
         * 合同名称
         */
        @ApiModelProperty(value = "合同名称")
        private String contractName;


        /**
         * 审核类别
         */
        @ApiModelProperty(value = "审核类别")
        private String assessmentType;


        /**
         * 考核内容
         */
        @ApiModelProperty(value = "考核内容")
        private String assessmentContent;


        /**
         * 考核标准
         */
        @ApiModelProperty(value = "考核标准")
        private String standard;


    

}
