package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * SupplierCertInfo Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@TableName(value = "ncf_form_supplier_cert_info")
@ApiModel(value = "SupplierCertInfoEntity对象", description = "资质信息表")
@Data

public class SupplierCertInfo extends ObjectEntity implements Serializable {

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @TableField(value = "supplier_code")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @TableField(value = "supplier_name")
    private String supplierName;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    @TableField(value = "serial_number")
    private String serialNumber;

    /**
     * 资质证书名称
     */
    @ApiModelProperty(value = "资质证书名称")
    @TableField(value = "cert_name")
    private String certName;

    /**
     * 资质类别
     */
    @ApiModelProperty(value = "资质类别")
    @TableField(value = "cert_category")
    private String certCategory;

    /**
     * 资质等级
     */
    @ApiModelProperty(value = "资质等级")
    @TableField(value = "cert_level")
    private String certLevel;

    /**
     * 资质分组
     */
    @ApiModelProperty(value = "资质分组")
    @TableField(value = "cert_group")
    private String certGroup;

    /**
     * 代理品牌/产品名称
     */
    @ApiModelProperty(value = "代理品牌/产品名称")
    @TableField(value = "brand_product")
    private String brandProduct;

    /**
     * 证书有效期截止日期
     */
    @ApiModelProperty(value = "证书有效期截止日期")
    @TableField(value = "expiry_date")
    private String expiryDate;

    /**
     * 证书编码
     */
    @ApiModelProperty(value = "证书编码")
    @TableField(value = "cert_code")
    private String certCode;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @TableField(value = "main_table_id")
    private String mainTableId;

}
