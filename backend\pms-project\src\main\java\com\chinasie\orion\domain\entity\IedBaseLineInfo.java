package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * IedBaseLineInfo Entity对象
 *
 * <AUTHOR>
 * @since 2024-01-29 13:50:13
 */
@TableName(value = "pms_ied_base_line_info")
@ApiModel(value = "IedBaseLineInfoEntity对象", description = "ied基线信息表")
@Data
public class IedBaseLineInfo extends ObjectEntity implements Serializable{

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    @ApiModelProperty(value = "复制的IED数量")
    @TableField(value = "sum_number")
    private Long sumNumber;

}
