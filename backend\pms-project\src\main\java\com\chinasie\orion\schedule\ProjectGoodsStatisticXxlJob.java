package com.chinasie.orion.schedule;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.domain.entity.GoodsServicePlan;
import com.chinasie.orion.domain.entity.projectStatistics.GoodsStatusStatistics;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.repository.GoodsServicePlanMapper;
import com.chinasie.orion.service.projectStatistics.GoodsStatusStatisticsService;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Component
public class ProjectGoodsStatisticXxlJob {
    @Resource
    private GoodsServicePlanMapper goodsServicePlanMapper;

    @Autowired
    private GoodsStatusStatisticsService goodsStatusStatisticsService;

    @XxlJob("projectGoodsStatisticDailyCount")
    public void projectGoodsStatisticDailyCount() throws Exception {
        String nowDate =  DateUtil.format(new Date(),"yyyy-MM-dd");
        LambdaQueryWrapper<GoodsStatusStatistics> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(GoodsStatusStatistics :: getDateStr,nowDate);
        goodsStatusStatisticsService.remove(lambdaQueryWrapper);

        LambdaQueryWrapperX<GoodsServicePlan> schemeLambdaQueryWrapper = new LambdaQueryWrapperX<>();
        schemeLambdaQueryWrapper.isNotNull(GoodsServicePlan :: getProjectId);
        schemeLambdaQueryWrapper.isNotNull(GoodsServicePlan :: getStatus);
        schemeLambdaQueryWrapper.groupBy(GoodsServicePlan :: getProjectId, GoodsServicePlan :: getTypeCode);
        schemeLambdaQueryWrapper.select(" project_id projectId, type_code typeCode," +
                "IFNULL( sum( CASE  WHEN `status`=120 THEN 1 ELSE 0 END ), 0 ) noAuditCount ," +
                "IFNULL( sum( CASE  WHEN `status`=110 THEN 1 ELSE 0 END ), 0 ) as underReviewCount," +
                "IFNULL( sum( CASE  WHEN `status`=130 THEN 1 ELSE 0 END ), 0 ) as reviewedCount, " +
                "IFNULL( sum( CASE  WHEN `status`=160 THEN 1 ELSE 0 END ), 0 ) as storeCount");
        List<Map<String, Object>> maps = goodsServicePlanMapper.selectMaps(schemeLambdaQueryWrapper);
        if(CollectionUtils.isEmpty(maps)){
            return;
        }
        List<GoodsStatusStatistics> goodsStatusStatisticsList = new ArrayList<>();

        maps.forEach(p ->{
            String projectId = String.valueOf(p.get("projectId"));
            String type = String.valueOf(p.get("typeCode"));
            GoodsStatusStatistics goodsStatusStatistics =new GoodsStatusStatistics();
            goodsStatusStatistics.setNowDay(new Date());
            goodsStatusStatistics.setDateStr(nowDate);
            goodsStatusStatistics.setProjectId(projectId);
            goodsStatusStatistics.setUk(nowDate+":"+type+":"+projectId);
            goodsStatusStatistics.setTypeId(type);
            goodsStatusStatistics.setNoAuditCount(Integer.parseInt(p.get("noAuditCount").toString()));
            goodsStatusStatistics.setUnderReviewCount(Integer.parseInt(p.get("underReviewCount").toString()));
            goodsStatusStatistics.setReviewedCount(Integer.parseInt(p.get("reviewedCount").toString()));
            goodsStatusStatistics.setStoreCount(Integer.parseInt(p.get("storeCount").toString()));
            goodsStatusStatisticsList.add(goodsStatusStatistics);
        });
        goodsStatusStatisticsService.saveBatch(goodsStatusStatisticsList);
    }
}
