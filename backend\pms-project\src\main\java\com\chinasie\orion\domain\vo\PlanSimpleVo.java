package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/16/10:33
 * @description:
 */
@Data
public class PlanSimpleVo implements Serializable {
    @ApiModelProperty(value = "计划ID")
    private String id;
    @ApiModelProperty(value = "计划名称")
    private String name;
    @ApiModelProperty(value = "计划编号")
    private String number;

    @ApiModelProperty(value = "负责人ID")
    private String principalId;

    @ApiModelProperty(value = "负责人名称")
    private String principalName;
}
