package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * ContractMilestone DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-29 15:01:52
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ContractMilestoneDTO对象", description = "合同里程碑")
@Data
@ExcelIgnoreUnannotated
public class ContractMilestoneDTO extends ObjectDTO implements Serializable {

    /**
     * 里程碑名称
     */
    @ApiModelProperty(value = "里程碑名称")
    @ExcelProperty(value = "里程碑名称")
    @NotEmpty(message = "里程碑名称不能为空")
    private String milestoneName;

    /**
     * 里程碑类型
     */
    @ApiModelProperty(value = "里程碑类型")
    private String milestoneType;

    @ExcelProperty(value = "里程碑类型名称")
    private String milestoneTypeName;

    /**
     * 描述
     */
    @ExcelProperty(value = "里程碑详情")
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 关联里程碑Id
     */
    @ApiModelProperty(value = "关联里程碑Id")
    private String parentId;

    @ExcelProperty(value = "父级里程碑名称")
    private String parentName;

    /**
     * 技术负责人
     */
    @ApiModelProperty(value = "技术负责人")
    private String techRspUser;

    @ApiModelProperty(value = "技术负责人工号")
    @ExcelProperty(value = "技术接口人工号")
    private String techRspUserNo;

    @ApiModelProperty(value = "商务负责人")
    @NotEmpty(message = "商务负责人不能为空")
    private String busRspUser;

    @ApiModelProperty(value = "商务负责人工号")
    @ExcelProperty(value = "商务接口人工号")
    private String busRspUserNo;

    /**
     * 计划验收日期
     */
    @ApiModelProperty(value = "合同约定验收日期")
    @ExcelProperty(value = "合同约定验收日期")
    @NotNull(message = "合同约定验收日期不能为空")
    private Date planAcceptDate;

    @ApiModelProperty(value = "修改前计划验收日期")
    private Date originPlanAcceptDate;

    /**
     * 成本业务分类
     */
    @ApiModelProperty(value = "业务类型")
    @NotEmpty(message = "业务类型不能为空")
    private String costBusType;

    @ExcelProperty(value = "业务类型名称")
    private String costBusTypeName;

    /**
     * 里程碑金额
     */
    @ApiModelProperty(value = "合同约定验收金额")
    @ExcelProperty(value = "合同约定验收金额")
    @NotNull(message = "合同约定验收金额不能为空")
    private BigDecimal milestoneAmt;

    /**
     * 承接部门
     */
    @ApiModelProperty(value = "承接部门")
    private String undertDept;

    @ApiModelProperty(value = "承接部门名称")
    @ExcelProperty(value = "承接部门名称")
    private String undertDeptName;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    @NotEmpty(message = "合同id不能为空")
    private String contractId;


    /**
     * 实际验收日期
     */
    @ApiModelProperty(value = "实际验收日期")
    private Date actualAcceptDate;

    /**
     * 实际验收金额
     */
    @ApiModelProperty(value = "实际验收金额")
    private BigDecimal actualMilestoneAmt;

    /**
     * 税率
     */
    @ApiModelProperty(value = "税率")
    @ExcelProperty(value = "税率")
    private BigDecimal taxRate;

    /**
     * 累计验收比例
     */
    @ApiModelProperty(value = "累计验收比例")
    private BigDecimal totalAcceptRate;
    /**
     * 初始预估验收日期
     */
    @ApiModelProperty(value = "初始预估验收日期")
    private Date expectAcceptDate;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private String cusPersonId;

    @ApiModelProperty(value = "客户名称")
    @ExcelProperty(value = "客户名称")
    private String cusPersonName;

    /**
     * 是否创建计划数据 0：否 1：是
     */
    @ApiModelProperty(value = "是否创建计划数据 0：否 1：是")
    private Integer isPlan;

    @ApiModelProperty(value = "所级部门，pmi_dept id")
    private String officeDept;

    @ApiModelProperty(value = "所级负责人")
    private String officeLeader;

    /**
     * 技术接口人--所级
     */
    @ApiModelProperty(value = "技术接口人--所级")
    private String departmental;


    /**
     * 收入确认类型
     */
    @ApiModelProperty(value = "收入确认类型")
    private String incomeType;

    /**
     * 是否暂估
     */
    @ApiModelProperty(value = "是否暂估")
    private Integer isProvisionalEstimate;

    /**
     * 初始预估验收金额
     */
    @ApiModelProperty(value = "初始预估验收金额")
    private BigDecimal exceptAcceptanceAmt;

    /**
     * 里程碑已暂估金额
     */
    @ApiModelProperty(value = "里程碑已暂估金额")
    private BigDecimal milestoneProvisionalEstimateAmt;

    /**
     * 里程碑已预收款开票金额（价税合计）
     */
    @ApiModelProperty(value = "里程碑已预收款开票金额（价税合计）")
    private BigDecimal milestoneAdvanceAmt;

    /**
     * 开票主体名称
     */
    @ApiModelProperty(value = "开票主体名称")
    private BigDecimal signedMainName;

    /**
     * 预计开票日期
     */
    @ApiModelProperty(value = "预计开票日期")
    private Date exceptInvoiceDate;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    private String currency;

    /**
     * 含税金额
     */
    @ApiModelProperty(value = "含税金额")
    private BigDecimal amtTax;

    /**
     * 不含税金额
     */
    @ApiModelProperty(value = "不含税金额")
    private BigDecimal amtNoTax;

    /**
     * 收入计划编号
     */
    @ApiModelProperty(value = "收入计划编号")
    private String incomePlanCode;

   /**
     * 项目 编号
     */
    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    /**
     * 里程碑收入类型
     */
    @ApiModelProperty(value = "里程碑收入类型")
    private String mileIncomeType;

    /**
     * wbs编号
     */
    @ApiModelProperty(value = "wbs编号")
    private String wbsCode;

    /**
     * 金额类型
     */
    @ApiModelProperty(value = "金额类型")
    @ExcelProperty(value = "金额类型")
    private String ammountType;

    /**
     * 日期类型
     */
    @ApiModelProperty(value = "日期类型")
    @ExcelProperty(value = "日期类型")
    private String dateType;
    /**
     * 凭证号
     */
    @ApiModelProperty(value = "凭证号")
    @ExcelProperty(value = "凭证号 ", index = 14)
    private String voucherNum;

    /**
     * 过帐日期
     */
    @ApiModelProperty(value = "过帐日期")
    @ExcelProperty(value = "过帐日期 ", index = 15)
    private Date passAccountDate;


    /**
     * 确认收入金额-暂估收入
     */
    @ApiModelProperty(value = "确认收入金额-暂估收入")
    @ExcelProperty(value = "确认收入金额-暂估收入 ", index = 16)
    private BigDecimal confirmIncomeProvisionalEstimate;

    /**
     * 确认收入金额-开票收入
     */
    @ApiModelProperty(value = "确认收入金额-开票收入")
    @ExcelProperty(value = "确认收入金额-开票收入 ", index = 17)
    private BigDecimal confirmIncomeInvoicing;

    /**
     * 确认收入金额-合计值
     */
    @ApiModelProperty(value = "确认收入金额-合计值")
    @ExcelProperty(value = "确认收入金额-合计值 ", index = 18)
    private BigDecimal confirmIncomeSum;

    /**
     * 是否被跟踪确认
     */
    @ApiModelProperty(value = "是否被跟踪确认")
    @ExcelProperty(value = "是否被跟踪确认 ", index = 20)
    private Date isTrackConfirm;


    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    private BigDecimal amt;

    /**
     * 验收日期
     */
    @ApiModelProperty(value = "验收日期")
    private Date checkDate;


    /**
     * 业务收入类型
     */
    @ApiModelProperty(value = "业务收入类型")
    @ExcelProperty(value = "业务收入类型 ", index = 21)
    private String businessIncomeType;

    /**
     * 里程碑序号
     */
    @ApiModelProperty(value = "里程碑序号")
    @ExcelProperty(value = "里程碑序号 ", index = 22)
    private Integer sequenceNumber;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "里程碑编号")
    private String number;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    @ExcelProperty(value = "项目名称 ", index = 23)
    private String projectName;

    /**
     * 立项时间
     */
    @ApiModelProperty(value = "立项时间")
    @ExcelProperty(value = "立项时间 ", index = 24)
    private Date initiationTime;

    /**
     * 计划验收金额
     */
    @ApiModelProperty(value = "计划验收金额")
    @ExcelProperty(value = "计划验收金额 ", index = 14)
    private BigDecimal plannedAcceptanceAmount;

    /**
     * 计划验收日期
     */
    @ApiModelProperty(value = "计划验收日期")
    @ExcelProperty(value = "计划验收日期 ", index = 15)
    private Date plannedAcceptanceDate;
}
