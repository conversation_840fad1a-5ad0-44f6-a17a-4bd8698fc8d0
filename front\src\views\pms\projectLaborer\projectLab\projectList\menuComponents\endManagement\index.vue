<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
    class="pdmBasicTable"
  >
    <template #toolbarLeft>
      <BasicButton
        v-if=" isPower('JXGL_container_button_01', powerData) "
        type="primary"
        icon="add"
        @click="addNode"
      >
        申请结项
      </BasicButton>
    </template>
    <template #startTime="{ text }">
      {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
    </template>
    <template #endTime="{ text }">
      {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
    </template>
    <template #modifyTime="{ text }">
      {{ text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '' }}
    </template>

    <template #action="{record}">
      <BasicTableAction
        :actions="actionsBtn"
        :record="record"
      />
    </template>
  </OrionTable>
  <AddTableNode
    v-if="pageType==='page'"
    @register="register"
    @update="updateData"
  />
</template>
<script lang="ts">
import {
  computed, defineComponent, h, inject, reactive, ref, toRefs,
} from 'vue';
import {
  BasicButton, BasicTableAction, isPower, ITableActionItem, OrionTable, useDrawer,
} from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { getPageApi, getProjectApi, removeApi } from '/@/views/pms/projectLaborer/api/endManagement';

import AddTableNode from './modal/AddTableNode.vue';

export default defineComponent({
  name: 'ProductLibraryIndex',
  components: {
    BasicTableAction,
    OrionTable,
    BasicButton,
    AddTableNode,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
    pageType: {
      type: String,
      default: 'page',
    },
  },

  setup(props) {
    const [register, { openDrawer }] = useDrawer();
    const [registerDetails, { openDrawer: openDrawerDetails }] = useDrawer();

    const tableRef = ref(null);
    const state = reactive({
      powerData: [],
      showVisible: false,
    });
    state.powerData = inject('powerData');

    const router = useRouter();
    const confirm = () => {
    };

    const getFormData = async () => {
      tableRef.value?.reload();
    };

    const openDetails = (data) => {
      router.push({
        name: 'EndDetails',
        query: {
          id: data.id,
          projectId: props.id,
          type: 0,
        },
      });
    };
    const addNode = () => {
      getProjectApi(props.id)
        .then((res) => {
          openDrawer(true, {
            type: 'add',
            projectId: res.projectId,
            data: res,
          });
        })
        .catch(() => {
          message.warning('获取项目信息失败!');
        });
    };

    function updateData() {
      getFormData();
    }

    // 生成 queryCondition
    function getListParams(params) {
      if (params.searchConditions) {
        return {
          ...params,
          queryCondition: params.searchConditions.map((item) => ({
            column: item?.[0]?.field,
            type: 'like',
            link: 'or',
            value: item?.[0]?.values?.[0],
          })),
        };
      }
      return params;
    }

    const tableOptions = {
      deleteToolButton: isPower('XMPJ_container_button_03', state.powerData) ? 'add|enable|disable|delete' : 'add|enable|disable',
      rowSelection: {},
      isFilter2: true,
      filterConfigName: 'PMS_PROJECTMANAGE_CLOSINGMANAGE',
      api(params) {
        return getPageApi(getListParams({
          ...params,
          query: {
            projectId: props.id,
          },
          orders: [
            {
              asc: false,
              column: '',
            },
          ],
          queryCondition: [],
        }));
      },
      batchDeleteApi({ ids }) {
        return removeApi(ids).then(() => {
          message.success('删除成功');
          getFormData();
        });
      },
      columns: [
        {
          title: '编号',
          dataIndex: 'number',
          width: '120px',
        },
        {
          title: '名称',
          dataIndex: 'name',
          customRender({ record, text }) {
            return h(
              'span',
              {
                class: computed(() => (isPower('XMX_container_button_85', state.powerData) ? 'action-btn' : '')).value,
                title: text,
                onClick(e) {
                  if (isPower('XMX_container_button_85', state.powerData)) {
                    openDetails(record);
                  }
                  e.stopPropagation();
                },
              },
              text,
            );
          },
          minWidth: 200,
        },

        {
          title: '结项类型',
          dataIndex: 'typeName',
          width: 100,
          slots: { customRender: 'type' },
        },
        {
          title: '开始日期',
          dataIndex: 'startTime',
          width: 150,
          slots: { customRender: 'startTime' },
        },
        {
          title: '实际结束日期',
          dataIndex: 'endTime',
          width: 150,
          slots: { customRender: 'endTime' },
        },
        {
          title: '状态',
          dataIndex: 'statusName',
          width: 100,
          slots: { customRender: 'status' },
        },
        {
          title: '负责人',
          dataIndex: 'principalName',
          width: 100,
          slots: { customRender: 'principalName' },
        },
        {
          title: '修改日期',
          dataIndex: 'modifyTime',
          width: 150,
          slots: { customRender: 'modifyTime' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 120,
          fixed: 'right',
          slots: { customRender: 'action' },
        },
      ],
    };

    const actionsBtn:ITableActionItem[] = [
      {
        text: '编辑',
        isShow: computed(() => isPower('JXGL_container_button_03', state.powerData)),
        onClick(record:any) {
          openDrawer(true, {
            type: 'edit',
            id: record.id,
            projectId: record.projectId,
          });
        },
      },
      {
        text: '删除',
        isShow: computed(() => isPower('JXGL_container_button_04', state.powerData)),
        modal(record:any) {
          return removeApi([record.id]).then(() => {
            message.success('删除成功');
            getFormData();
          });
        },
      },
    ];

    return {
      tableRef,
      ...toRefs(state),
      confirm,
      addNode,
      dayjs,
      isPower,
      register,
      updateData,
      registerDetails,
      tableOptions,
      actionsBtn,
    };
  },
});
</script>
<style lang="less" scoped>
</style>
