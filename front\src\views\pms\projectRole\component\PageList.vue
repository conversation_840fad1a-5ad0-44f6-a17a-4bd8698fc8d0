<template>
  <div
    v-loading="loadListStatus"
    class="h-full"
  >
    <template v-if="Array.isArray(menuData) && menuData.length">
      <Menu
        v-bind="$attrs"
        :show-header="false"
        size="small"
        name="角色"
        :menu-data="menuData"
      >
        <template #menuItem="{ item }">
          <div class="flex flex-ac menu-item">
            <div class="flex-f1">
              {{ item.name }}
            </div>
            <Icon
              v-if="useRolePermissionsStore().isSuper"
              icon="fa-close"
              class="icon"
              @click.stop="deletePage(item)"
            />
          </div>
        </template>
      </Menu>
    </template>

    <div
      v-else-if="useRolePermissionsStore().isSuper"
      class="tx-c ptb30 c99"
    >
      暂无页面，请点击 <span
        class="action-btn"
        @click="openAddPageModal"
      >添加</span> 页面
    </div>
    <AddPageModal
      @register="addPageModalRegister"
      @reload="reloadPageList"
    />
  </div>
</template>

<script lang="ts">
import {
  defineComponent, ref, nextTick, reactive, toRefs, h,
} from 'vue';
import {
  BasicMenu as Menu, useModal, Icon,
} from 'lyra-component-vue3';
import Api from '/@/api';
import AddPageModal from '../modal/AddPageModal.vue';
import { message, Modal } from 'ant-design-vue';
import { useRolePermissionsStore } from '/@/store/modules/rolePermissions';

export default defineComponent({
  name: 'BusinessObject',
  components: {
    Menu,
    AddPageModal,
    Icon,
  },
  props: {
    roleId: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const state = reactive({
      loadListStatus: false,
      menuData: [],
    });

    const [addPageModalRegister, { openModal }] = useModal();

    async function init() {
      await reloadPageList();
    }
    nextTick(() => {
      init();
    });

    async function reloadPageList() {
      state.menuData = await loadPageList();
    }

    function loadPageList() {
      state.loadListStatus = true;
      return new Api('/pmi/business-privilege/subject-policy/page-list')
        .fetch(
          {
            subjectId: props.roleId,
            subjectType: '10',
          },
          '',
          'POST',
        )
        .then((data) => data.map((item) => ({
          ...item,
          name: item.pageName,
          id: item.pageId,
        })))
        .catch(() => [])
        .finally(() => {
          state.loadListStatus = false;
        });
    }

    return {
      ...toRefs(state),
      useRolePermissionsStore,
      addPageModalRegister,
      closeChange(item) {},
      // 打开添加页面
      openAddPageModal() {
        openModal(true, {
          addedPageList: state.menuData,
          roleId: props.roleId,
        });
      },
      // 删除页面
      deletePage(item) {
        Modal.confirm({
          title: '确认提示',
          content: h('span', {}, [
            '请确认是否移除',
            h(
              'span',
              {
                style: {
                  color: 'red',
                },
                class: {
                  plr5: true,
                },
              },
              item.name,
            ),
            '配置数据？',
          ]),
          onOk() {
            return new Api('/pmi/business-privilege/subject-privilege')
              .fetch({}, item.id, 'DELETE')
              .then(async () => {
                message.success('删除成功');
                // if (item.id === businessState.actionPageId) {
                //   Object.assign(businessState, {
                //     actionPageId: '',
                //     actionPage: null
                //   });
                // }
              })
              .finally(() => {
                reloadPageList();
              });
          },
        });
      },
      reloadPageList,
    };
  },
});
</script>

<style scoped lang="less">
  .menu-wrap {
    height: 100%;
  }

  .menu-item {
    > .icon {
      opacity: 0;
      transition: 0.2s;
      margin-right: 10px;
    }

    &:hover {
      > .icon {
        opacity: 0.6;

        &:hover {
          background: ~`getPrefixVar('primary-color-deprecated-f-12')`;
        }
      }
    }
  }
</style>
