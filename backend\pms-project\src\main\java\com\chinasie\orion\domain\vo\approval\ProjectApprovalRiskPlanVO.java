package com.chinasie.orion.domain.vo.approval;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.util.List;
/**
 * ProjectApprovalRiskPlan VO对象
 *
 * <AUTHOR>
 * @since 2024-05-27 16:10:46
 */
@ApiModel(value = "ProjectApprovalRiskPlanVO对象", description = "项目立项风险策划")
@Data
public class ProjectApprovalRiskPlanVO extends  ObjectVO   implements Serializable{

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    private String number;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;


    /**
     * 立项ID
     */
    @ApiModelProperty(value = "立项ID")
    private String approveId;


    /**
     * 风险ID
     */
    @ApiModelProperty(value = "风险ID")
    private String riskId;


    /**
     * 风险发生概率
     */
    @ApiModelProperty(value = "风险发生概率")
    private String riskProbability;
    @ApiModelProperty(value = "风险发生概率名称")
    private String riskProbabilityName;


    /**
     * 影响程度
     */
    @ApiModelProperty(value = "影响程度")
    private String riskInfluence;
    @ApiModelProperty(value = "影响程度名称")
    private String riskInfluenceName;


    /**
     * 风险类型
     */
    @ApiModelProperty(value = "风险类型")
    private String riskType;
    @ApiModelProperty(value = "风险类型名称")
    private String riskTypeName;


    /**
     * 应对措施
     */
    @ApiModelProperty(value = "应对措施")
    private String solutions;


    /**
     * 文档ID
     */
    @ApiModelProperty(value = "文档ID")
    private String documentId;


}
