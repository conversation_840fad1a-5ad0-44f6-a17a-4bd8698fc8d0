package com.chinasie.orion.bo;

import cn.hutool.core.map.MapUtil;
import cn.zhxu.bs.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.chinasie.orion.constant.MajorRepairOrgEnum;
import com.chinasie.orion.constant.Permission;
import com.chinasie.orion.domain.entity.MajorRepairOrg;
import com.chinasie.orion.domain.vo.DataPermissionVO;
import com.chinasie.orion.mybatis.domain.entity.LyraEntity;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.service.CommonDataAuthRoleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class CommonRoleBo {

    @Autowired
    private CommonDataAuthRoleService commonDataAuthRoleService;


    public Map<String, Set<String>> currentUserRoles(List<String> idList, List<MajorRepairOrg> majorRepairOrgList) throws Exception{
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        Map<String,Set<String>> map = new HashMap<>();
        getLevelRole(map,majorRepairOrgList,currentUserId);
        mergeRoleList(map,idList);
        return map;
    }

    public void getLevelRole(Map<String,Set<String>> map,List<MajorRepairOrg> majorRepairOrgList,String currentUserId){
        List<String> towLevelIdList = new ArrayList<>();
        Map<String,List<String>> majorIdToTeamIdList=new HashMap<>();
        Map<String,MajorRepairOrg> idToEntity = new HashMap<>();
        for (MajorRepairOrg majorRepairOrg : majorRepairOrgList) {
            // 取出所以后的 二级菜单 如果是大修指挥部 拥有所有二级节点的编辑权限
            if(!Objects.equals(MajorRepairOrgEnum.LEVEL_TYPE_REPAIR_ROLE.getCode(),majorRepairOrg.getLevelType()) && Objects.equals(2,majorRepairOrg.getLevel())){
                towLevelIdList.add(majorRepairOrg.getId());
            }
            // 获取专业班组 每个专业下的所有班组
            if(Objects.equals(MajorRepairOrgEnum.LEVEL_TYPE_SPECIALTY_TEAM.getCode(),majorRepairOrg.getLevelType())){
                List<String> teamIdList = majorIdToTeamIdList.getOrDefault(majorRepairOrg.getParentId(),new ArrayList<>());
                teamIdList.add(majorRepairOrg.getId());
                majorIdToTeamIdList.put(majorRepairOrg.getParentId(),teamIdList);
            }
            idToEntity.put(majorRepairOrg.getId(),majorRepairOrg);
        }
        boolean isMajorRole=Boolean.FALSE;
        List<String> specialtyIdList = new ArrayList<>();
        for (MajorRepairOrg majorRepairOrg : majorRepairOrgList) {
            // 如果是同一个负责人
            if(Objects.equals(majorRepairOrg.getRspUserId(),currentUserId)){
                // 如果是大修指挥部 那么拥有其他节点的权限
                if(Objects.equals(MajorRepairOrgEnum.LEVEL_TYPE_REPAIR_ROLE.getCode(),majorRepairOrg.getLevelType())){
                    log.info("权限设置--数据：【{}】", JSONObject.toJSONString(majorRepairOrg));
                    isMajorRole = Boolean.TRUE;
                }
                // 如果是specialtyManagementRole 专业下的管理组下的角色 那么可以操作当前专业下的 班组节点的信息
                if(Objects.equals(MajorRepairOrgEnum.LEVEL_TYPE_MANAGEMENT_ROLE.getCode(),majorRepairOrg.getLevelType())  ){
                    // 如果是专业班组 那么拥有其他班组的权限
                    if(Objects.equals(4,majorRepairOrg.getLevel())){
                        MajorRepairOrg majorRepairOrg1=   idToEntity.get(majorRepairOrg.getParentId());
                        if(Objects.nonNull(majorRepairOrg1)){
                            // 插入那些所有的专业ID
                            specialtyIdList.add(majorRepairOrg1.getParentId());
                        }
                    }else if (Objects.equals(3,majorRepairOrg.getLevel())){
                        specialtyIdList.add(majorRepairOrg.getParentId());
                    }
                }
            }
        }

        Set<String> role = new HashSet<>();
        role.add(Permission.WRITE.name());
        if(isMajorRole){
            map.put("0",role);
            for (String s : towLevelIdList) {
                map.put(s,role);
            }
        }else if(!CollectionUtils.isEmpty(specialtyIdList)){
            for (String s : specialtyIdList) {
                List<String> teamIdList = majorIdToTeamIdList.get(s);
                if(!CollectionUtils.isEmpty(teamIdList)){
                    for (String string : teamIdList) {
                        map.put(string,role);
                    }
                }
                map.put(s,role);
            }
            log.info("权限设置：【{}】 权限【{}】",specialtyIdList,map);
        }
    }

    public void mergeRoleList( Map<String, Set<String>> map, List<String> idList){
        Map<String, Set<String>> userRole =   commonDataAuthRoleService.currentUserRoles(idList);
        if(MapUtil.isNotEmpty(userRole)){
            log.info("权限设置--权限表：【{}】", JSONObject.toJSONString(userRole));
            for (Map.Entry<String, Set<String>> stringSetEntry : userRole.entrySet()) {
                Set<String> set =  map.get(stringSetEntry.getKey());
                if(null == set){
                    set = new HashSet<>();
                }
                set.addAll(stringSetEntry.getValue());
                map.put(stringSetEntry.getKey(),set);
            }
        }
    }

    public <T extends DataPermissionVO> Map<String, Set<String>>  currentUserRolesList(List<T> daList,List<MajorRepairOrg> majorRepairOrgList) throws Exception {

        String currentUserId = CurrentUserHelper.getCurrentUserId();
        List<String> idList=  daList.stream().map(DataPermissionVO::getId).distinct().collect(Collectors.toList());
        // 生产自带的权限
        idList.addAll(majorRepairOrgList.stream().map(LyraEntity::getId).collect(Collectors.toList()));
        Map<String, Set<String>>  businessMap = this.currentUserRoles(idList,majorRepairOrgList);
        List<String> majorRepairOrgIdList= new ArrayList<>();
        Map<String, MajorRepairOrg> idToEntity = new HashMap<>();
        for (MajorRepairOrg majorRepairOrg : majorRepairOrgList) {
            majorRepairOrgIdList.add(majorRepairOrg.getId());
            idToEntity.put(majorRepairOrg.getId(),majorRepairOrg);
        }
        // 获取节点上的权限
        Map<String, Set<String>> map = new HashMap<>();
        Set<String> role = new HashSet<>();
        role.add(Permission.WRITE.name());

        daList.forEach(dataPermissionVO -> {
            String rspUser=  dataPermissionVO.getRspUserId();
            if(StringUtils.isNotBlank(rspUser) && Objects.equals(rspUser,currentUserId)){
                this.setRole(map,dataPermissionVO,role);
            }else {
                String chaninPath = dataPermissionVO.getChainPath();
                if(org.springframework.util.StringUtils.hasText(chaninPath)){
                    for (Map.Entry<String, MajorRepairOrg> stringMajorRepairOrgEntry : idToEntity.entrySet()) {
                        if(chaninPath.contains(stringMajorRepairOrgEntry.getKey())){
                            MajorRepairOrg majorRepairOrg =   stringMajorRepairOrgEntry.getValue();
                            if( Objects.equals(majorRepairOrg.getRspUserId(), currentUserId)){
                                this.setRole(map,dataPermissionVO,role);
                            }
                        }
                    }
                    // 获取 数据拥有的权限信息
//                    Set<String>  roleSet =   map.get(dataPermissionVO.getId());
                    for (Map.Entry<String, Set<String>> stringMajorRepairOrgEntry : businessMap.entrySet()) {
                        String majorRepairOrgId = stringMajorRepairOrgEntry.getKey();
                        if(chaninPath.contains(majorRepairOrgId)){
                            this.setRole(map,dataPermissionVO,stringMajorRepairOrgEntry.getValue());
                        }
                    }
                }
                if(majorRepairOrgIdList.contains(dataPermissionVO.getId())){
                    this.setRole(map,dataPermissionVO,role);
                }
            }
        });
        return  map;
    }

    public <T extends DataPermissionVO>  void setRole(Map<String, Set<String>>  map,T dataPermissionVO,Set<String> role){
        Set<String> set = map.get(dataPermissionVO.getId());
        if(CollectionUtils.isEmpty(set)){
            set = new HashSet<>();
        }
        set.addAll(role);
        map.put(dataPermissionVO.getId(),set);
    }
}
