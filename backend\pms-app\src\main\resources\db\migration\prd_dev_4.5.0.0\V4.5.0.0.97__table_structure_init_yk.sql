CREATE INDEX index_type ON pmi_dept(`type`);
CREATE INDEX index_dept_id ON pmi_dept_leader(`dept_id`);
CREATE INDEX index_user_id ON pmi_dept_leader(`user_id`);
CREATE INDEX index_business_type ON pmsx_common_data_auth_role(`business_type`);

alter table pmsx_common_data_auth_role add COLUMN  `business_type` varchar(64)   COMMENT '业务类型';
ALTER TABLE pmsx_common_data_auth_role change COLUMN role_code permission_code  varchar(64) DEFAULT NULL COMMENT '权限code：read,edit';