package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * NcfFormpurchaseRequestAttachment Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-11 10:49:58
 */
@TableName(value = "ncf_form_purchase_request_attachment")
@ApiModel(value = "NcfFormpurchaseRequestAttachmentEntity对象", description = "采购申请附件")
@Data

public class NcfFormpurchaseRequestAttachment extends ObjectEntity implements Serializable {

    /**
     * 采购申请编号
     */
    @ApiModelProperty(value = "采购申请编号")
    @TableField(value = "code")
    private String code;

    /**
     * 节点
     */
    @ApiModelProperty(value = "节点")
    @TableField(value = "node")
    private String node;

    /**
     * 文件名
     */
    @ApiModelProperty(value = "文件名")
    @TableField(value = "attachment_name")
    private String attachmentName;

    /**
     * 文件密级
     */
    @ApiModelProperty(value = "文件密级")
    @TableField(value = "classification_level")
    private String classificationLevel;

    /**
     * 保密期限
     */
    @ApiModelProperty(value = "保密期限")
    @TableField(value = "secrecy_term")
    private String secrecyTerm;

    /**
     * 文档类型
     */
    @ApiModelProperty(value = "文档类型")
    @TableField(value = "document_type")
    private String documentType;

}
