package com.chinasie.orion.controller;

import com.chinasie.orion.domain.entity.ProjectPlanTypeAttributeValue;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.service.ProjectPlanTypeAttributeValueService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * RiskTypeAttributeValue 前端控制器
 * </p>
 *
 * <AUTHOR> sie
 * @since 2022-10-13
 */
@RestController
@RequestMapping("/projectPlan-type-attribute-value")
@Api(tags = "项目计划类型属性值")
public class ProjectPlanTypeAttributeValueController {
    @Autowired
    private ProjectPlanTypeAttributeValueService projectPlanTypeAttributeValueService;

    /**
     * 添加
     * @param projectPlanTypeAttributeValues
     * @return
     * @throws Exception
     */
    @ApiOperation("添加")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectPlanTypeAttributeValues", dataType = "List")
    })
    @PostMapping(value = "")
    @LogRecord(success = "【{USER{#logUserId}}】添加", type = "项目计划类型属性值", subType = "添加", bizNo = "")
    public ResponseDTO<List<ProjectPlanTypeAttributeValue>> add(@RequestBody List<ProjectPlanTypeAttributeValue> projectPlanTypeAttributeValues) throws Exception {
        projectPlanTypeAttributeValueService.saveBatch(projectPlanTypeAttributeValues);
        return new ResponseDTO<>(projectPlanTypeAttributeValues);
    }

    /**
     * 删除属性值
     * @param riskIds
     * @return
     * @throws Exception
     */
    @ApiOperation("删除属性值")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "riskId", dataType = "List")
    })
        @DeleteMapping(value = "/byRiskIds")
    @LogRecord(success = "【{USER{#logUserId}}】删除属性值", type = "项目计划类型属性值", subType = "删除属性值", bizNo = "")
    public ResponseDTO<Boolean> deleteByRiskIds(@RequestBody List<String> riskIds) throws Exception {
        return new ResponseDTO<>(projectPlanTypeAttributeValueService.deleteByRiskIds(riskIds));
    }
}
