<template>
  <BasicModal
    v-bind="$attrs"
    title="添加关联计划"
    :width="1100"
    :min-height="600"
    wrap-class-name="addPlanTreeModal"
    :footer="null"
    @register="registerModal"
  >
    <div class="addPlanTreeModalContent">
      <div class="contentList">
        <AInputSearch
          v-model:value="searchTreeValue"
          style="margin-bottom: 8px"
          placeholder="请输入名称或者编号"
          @search="searchChange"
        />

        <div class="treeContent">
          <template
            v-for="item in leftList"
            :key="item.id"
          >
            <div
              class="treeContentName"
              :class="{'treeContentName_active':selectLeftKey===item.id}"
              @click="changeLeft(item)"
            >
              {{ item.name }}
            </div>
          </template>
        </div>
      </div>
      <div class="contentMiddle">
        <OrionTable
          v-if="selectLeftKey"
          :key="selectLeftKey"
          ref="tableRef"
          :options="tableOptions"
        />
      </div>
      <div class="contentTight">
        <div class="contentTight_title">
          <span class="listLength">显示已选择（{{ rightData.length }}）</span>
          <span
            class="clearList"
            @click="clearAll"
          >清空</span>
        </div>
        <div class="listContent">
          <template
            v-for="item in rightData"
            :key="item.id"
          >
            <div class="listContent_item">
              <span class="itemStyle flex-te">{{ item.name }}</span>
              <span
                class="removeItem"
                @click="clearItem(item)"
              >移除</span>
            </div>
          </template>
        </div>
        <AButton
          type="primary"
          @click="addRelease"
        >
          确认添加
        </AButton>
      </div>
    </div>
  </BasicModal>
</template>
<script lang="ts">
import {
  defineComponent, onMounted, reactive, toRefs, computed, ref, inject, h,
} from 'vue';
import {
  BasicModal, useModalInner,
  OrionTable, DataStatusTag,
} from 'lyra-component-vue3';
import {
  Tree, Input, Button, message, Select,
} from 'ant-design-vue';
import Api from '/@/api';
import dayjs from 'dayjs';
export default defineComponent({
  name: 'AddPlanTreeModal',
  components: {
    BasicModal,
    OrionTable,
    AInputSearch: Input.Search,
    AButton: Button,
  },
  props: {
    modalTitle: {
      type: String,
      default: '',
    },
    relatedType: {
      type: String,
      default: 'page',
    },
  },
  emits: ['update', 'initData'],
  setup(props, { emit }) {
    const formData: any = inject('formData', {});
    const state :any = reactive({
      rightData: [],
      showTable: false,
      leftList: [],
      selectLeftKey: '',
      selectedRowKeys: [],
    });
    let onOk = null;
    const [registerModal, { closeModal, setModalProps }] = useModalInner(async (data) => {
      state.rightData = [];
      state.selectedRowKeys = [];
      if (state.leftList.length === 0) {
        getLeftList();
      }
      if (data.onOk) {
        onOk = data.onOk;
      } else {
        onOk = null;
      }
    });
    function getLeftList(parmas = {}) {
      new Api('/plan').fetch({}, 'scheme/scheme-shell/list', 'POST').then((res) => {
        console.log(res);
        state.leftList = res.map((item) => ({
          name: item.name,
          id: item.id,
        }));
      });
    }
    const tableRef = ref();
    const tableOptions = ref({
      deleteToolButton: 'add|enable|disable|delete',

      rowSelection: {
        selectedRowKeys: computed(() => state.selectedRowKeys),
        onSelect: (record, selected, selectedRows, nativeEvent) => {
          if (selected) {
            state.selectedRowKeys.push(record.id);
            state.rightData.push(record);
          } else {
            state.rightData.splice(state.selectedRowKeys.indexOf(record.id), 1);
            state.selectedRowKeys.splice(state.selectedRowKeys.indexOf(record.id), 1);
          }
        },
        onSelectAll: (selected, selectedRows, changeRows) => {
          let tableData = [];
          initData(tableRef.value.getDataSource(), tableData);
          if (selected) {
            tableData.forEach((item) => {
              if (state.selectedRowKeys.indexOf(item.id) < 0) {
                state.selectedRowKeys.push(item.id);
                state.rightData.push(item);
              }
            });
          } else {
            tableData.forEach((item) => {
              state.rightData.splice(state.selectedRowKeys.indexOf(item.id), 1);
              state.selectedRowKeys.splice(state.selectedRowKeys.indexOf(item.id), 1);
            });
          }
        },
      },
      showSmallSearch: false,
      // dataSource: [],
      api: (P) => new Api(`/plan/scheme/tree/${state.selectLeftKey}`).fetch(P, '', 'POST'),
      columns: [
        {
          title: '编号',
          dataIndex: 'number',
          width: 200,
        },
        {
          title: '计划名称',
          dataIndex: 'name',
          minWidth: 400,
        },
        {
          title: '计划类型',
          dataIndex: 'planTypeName',
          width: 100,
        },
        {
          title: '状态',
          dataIndex: 'data4',
          width: 100,
          customRender({ record }) {
            return h(DataStatusTag, {
              statusData: record.dataStatus,
            });
          },
        },
        {
          title: '进度状态',
          dataIndex: 'speedStatus',
          width: 100,
        },
        {
          title: '责任单位',
          dataIndex: 'resOrgName',
          width: 100,
        },
        {
          title: '责任科室',
          dataIndex: 'resDeptName',
          width: 100,
        },
        {
          title: '责任人',
          dataIndex: 'resPersonName',
          width: 100,
        },
        {
          title: '计划开始日期',
          dataIndex: 'planStartTime',
          width: 120,
          resizable: true,
          customRender({ text }) {
            return text ? dayjs(text).format('YYYY-MM-DD') : '';
          },
        },
        {
          title: '计划结束日期',
          dataIndex: 'planEndTime',
          width: 120,
          resizable: true,
          customRender({ text }) {
            return text ? dayjs(text).format('YYYY-MM-DD') : '';
          },
        },
      ],
      beforeFetch,
    });
    function initData(data, tableData) {
      data.forEach((item) => {
        tableData.push(item);
        if (item.children && item.children.length > 0) {
          initData(item.children, tableData);
        }
      });
    }
    function beforeFetch(t) {
      return t;
    }

    const selectNode = (id) => {
      state.searchTableValue = '';
      state.params = {
        query: {
          status: 1,
        },
      };
    };
    const searchTableChange = () => {
      state.params = {
        query: {
          name: state.searchTableValue,
          status: 1,
        },
      };
    };
    const searchChange = () => {
      //   getTreeData();
    };
    onMounted(() => {
    });
    const clearItem = (item) => {
      state.selectedRowKeys.splice(state.selectedRowKeys.indexOf(item.id), 1);
      let itemIndex = 0;
      for (let i = 0; i < state.rightData.length; i++) {
        if (state.rightData[i].id === item.id) {
          itemIndex = i;
          break;
        }
      }
      state.rightData.splice(itemIndex, 1);
    };
    const clearAll = () => {
      state.selectedRowKeys = [];
      state.rightData = [];
    };
    const addRelease = async () => {
      if (onOk) {
        try {
          await onOk(state.rightData);
          closeModal();
        } catch (e) {
          console.error(e);
        }
      } else {
        emit('update', state.rightData);
      }
      closeModal();
    };
    function changeLeft(data) {
      state.selectLeftKey = data.id;
    }

    return {
      ...toRefs(state),
      registerModal,
      tableOptions,
      selectNode,
      searchChange,
      searchTableChange,
      clearItem,
      clearAll,
      addRelease,
      tableRef,
      changeLeft,
    };
  },
});

</script>
<style lang="less" scoped>
.addPlanTreeModal{
  .scrollbar{
    padding: 0px !important;
    padding-bottom: 10px !important;
  }
  .scrollbar__wrap{
    margin-bottom: 0px !important;
  }
  .ant-modal-header{
    border-color:#dddddd
  }
  .addPlanTreeModalContent{
    display: flex;
    height: 100%;
    border-bottom: 1px solid #dddddd;
    margin-bottom: 10px;
  }
  .contentList{
    width: 250px;
    border-right: 1px solid #dddddd;
    padding: 10px;
    .treeContent{
      height: calc(~'100% - 50px');
      overflow: auto;
      .treeContentName{
        line-height: 32px;
        height: 32px;
        cursor: pointer;
        &:hover{
          color: #1890ff;
        }
      }
      .treeContentName_active{
        background: #1890ff1f;
        color: #1890ff;
      }
    }
  }
  .contentMiddle{
    width:calc(~'100% - 450px');
    padding:10px;
  }
  .contentTight{
    width: 200px;
    border-left: 1px solid #dddddd;
    padding:10px;
    display: flex;
    flex-direction: column;
    .contentTight_title{
      padding-bottom: 10px;
      .listLength{
        color: #333333;
        display: inline-block;
        width: 150px;
      }
      .clearList{
        color: ~`getPrefixVar('primary-color')`;
        cursor: pointer;
      }
    }
    .listContent{
      flex: 1;
      overflow: auto;
      .listContent_item{
        position: relative;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        .itemStyle{
          color: #8A92A5;
          height: 30px;
          line-height: 30px;
          display: inline-block;
          width: 120px;
          vertical-align: middle;
        }
        .removeItem{
          display: none;
          cursor: pointer;
          color: ~`getPrefixVar('primary-color')`;
          height: 30px;
          line-height: 30px;
          font-size: 12px;
          padding: 0px 10px;
          border-radius: 5px;
          &:hover{
            background: #edf1fc;
          }
        }
        &:hover{
          .itemStyle{
            color: ~`getPrefixVar('primary-color')`;
          }
          .removeItem{
            display: inline-block;
          }
        }
      }

    }
  }

}
</style>
