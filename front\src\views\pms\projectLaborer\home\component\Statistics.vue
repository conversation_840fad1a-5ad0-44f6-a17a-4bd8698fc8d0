<template>
  <div class="statistics-wrap">
    <div class="main flex">
      <div class="list-main">
        <div class="wrap item01">
          <div class="title">
            我的任务
          </div>
          <div class="number">
            26
          </div>
        </div>
      </div>
      <div class="list-main">
        <div class="wrap item02">
          <div class="title">
            我的流程
          </div>
          <div class="number">
            26
          </div>
        </div>
      </div>
      <div class="list-main">
        <div class="wrap item03">
          <div class="title">
            我的项目
          </div>
          <div class="number">
            26
          </div>
        </div>
      </div>
      <div class="list-main">
        <div class="wrap item04">
          <div class="title">
            我的数据
          </div>
          <div class="number">
            26
          </div>
        </div>
      </div>
      <div class="list-main">
        <div class="wrap item05">
          <div class="title">
            我的知识
          </div>
          <div class="number">
            26
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import Api from '/@/api';
export default defineComponent({
  setup() {
    function onClickFn() {
      // defHttp.request(
      //   {
      //     url: '/pmi/user/get/panshi/url',
      //     method: 'GET'
      //   },
      //   {
      //     isTransformRequestResult: false
      //   }
      // );
      // return;
      new Api('')
        .config({
          url: '/pmi/user/get/panshi/url',
          method: 'GET',
        })
        .then((res) => {
          console.log(res);
        });
    }

    // setTimeout(() => {
    //   onClickFn();
    // }, 4000);

    return {
      onClickFn,
    };
  },
});
</script>

<style scoped lang="less">
  .statistics-wrap {
    width: 100%;
    overflow: hidden;

    > .main {
      width: calc(100% + 16px);
      display: flex;
      margin-left: -8px;

      > div {
        flex: 1;
        width: 1px;
        padding: 0 9px;

        > .wrap {
          background-color: #fff;
          padding: 10px 16px;
          height: 120px;
          background-repeat: no-repeat;
          position: relative;

          > .title {
            font-size: 14px;
            font-weight: 400;
            height: 30px;
            color: #666666;
          }

          > .number {
            font-size: 32px;
            color: #444b5e;
            font-weight: bold;
            line-height: 1;
          }

          &.item01 {
            &:after {
              content: '';
              position: absolute;
              height: 40px;
              bottom: 14px;
              right: 22px;
              left: 16px;
              background: url('../img/img01.png') no-repeat left bottom;
              background-size: contain;
            }
          }

          &.item02 {
            &:after {
              content: '';
              position: absolute;
              right: 40px;
              height: 40px;
              bottom: 14px;
              left: 16px;
              background: url('../img/img02.png') no-repeat left bottom;
              background-size: contain;
            }
          }

          &.item03 {
            &:after {
              content: '';
              position: absolute;
              right: 40px;
              height: 40px;
              bottom: 14px;
              left: 16px;
              background: url('../img/img03.png') no-repeat left bottom;
              background-size: contain;
            }
          }

          &.item04 {
            &:after {
              content: '';
              position: absolute;
              left: 70%;
              height: 60px;
              right: 16px;
              bottom: 14px;
              background: url('../img/img04.png') no-repeat right bottom;
              background-size: contain;
            }
          }

          &.item05 {
            &:after {
              content: '';
              position: absolute;
              left: 65%;
              height: 70px;
              right: 16px;
              bottom: 14px;
              background: url('../img/img05.png') no-repeat right bottom;
              background-size: contain;
            }
          }
        }
      }
    }
  }
</style>
