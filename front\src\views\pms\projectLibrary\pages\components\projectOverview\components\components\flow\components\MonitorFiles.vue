<template>
  <div class="files-wrap">
    <BasicCard title="相关模板文件">
      <OrionTable
        :options="tableOptions"
        :dataSource="dataSource"
        class="pay-node-table"
      />
    </BasicCard>
  </div>
</template>
<script setup lang="ts">
import {
  BasicCard, downLoadByFilePath, OrionTable, randomString,
} from 'lyra-component-vue3';
import { ref, unref } from 'vue';
import { useUserStore } from '/@/store/modules/user';
import dayjs from 'dayjs';
import Api from '/@/api';

const userStore = useUserStore();
const dataSource = ref([]);

const tableOptions = {
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: false,
  pagination: false,
  showSmallSearch: false,
  showTableSetting: false,
  actions: [
    {
      text: '下载',
      onClick(record) {
        downLoadByFilePath({
          filePath: record.filePath,
          filePostfix: record.filePostfix,
          name: record.name,
        });
      },
    },
  ],
  columns: [
    {
      title: '文件名称',
      dataIndex: 'name',
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      type: 'dateTime',
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 120,
      slots: { customRender: 'action' },
    },
  ],
};

function getValues() {
  let params = unref(dataSource);
  params = JSON.parse(JSON.stringify(params ?? []));

  params.forEach((item) => {
    if (item.id.indexOf('add') >= 0) {
      delete item.id;
    }
  });
  return params;
}

function setValues(values) {
  dataSource.value = values;
}

defineExpose({
  getValues,
  setValues,
});
</script>

<style scoped lang="less">

.files-wrap {
  height: 300px;
  position: relative;
  overflow: hidden;
}
</style>
