package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.lang.Integer;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ContractOurSignedSubject DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-28 21:49:21
 */
@ApiModel(value = "ContractOurSignedSubjectDTO对象", description = "甲方签约主体")
@Data
@ExcelIgnoreUnannotated
public class ContractOurSignedSubjectDTO extends ObjectDTO implements Serializable{

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    private String contractId;

    /**
     * 签约主体名称
     */
    @ApiModelProperty(value = "签约主体名称")
    @ExcelProperty(value = "签约主体名称 ", index = 1)
    private String signedMainName;

    /**
     * 公司税号
     */
    @ApiModelProperty(value = "公司税号")
    @ExcelProperty(value = "公司税号 ", index = 2)
    private String companyDutyParagraph;

    /**
     * 主要联系人
     */
    @ApiModelProperty(value = "主要联系人")
    @ExcelProperty(value = "主要联系人 ", index = 3)
    private String mainContactPerson;

    /**
     * 主要联系人电话
     */
    @ApiModelProperty(value = "主要联系人电话")
    @ExcelProperty(value = "主要联系人电话 ", index = 4)
    private String mainContactPhone;

    /**
     * 技术联系人
     */
    @ApiModelProperty(value = "技术联系人")
    @ExcelProperty(value = "技术联系人 ", index = 5)
    private String techContactPerson;

    /**
     * 技术联系人电话
     */
    @ApiModelProperty(value = "技术联系人电话")
    @ExcelProperty(value = "技术联系人电话 ", index = 6)
    private String techContactPhone;

    /**
     * 技术联系部门
     */
    @ApiModelProperty(value = "技术联系部门")
    private String techContactDept;

    /**
     * 联系邮箱
     */
    @ApiModelProperty(value = "联系邮箱")
    @ExcelProperty(value = "联系邮箱 ", index = 7)
    private String contractEmail;

    /**
     * 联系地址
     */
    @ApiModelProperty(value = "联系地址")
    @ExcelProperty(value = "联系地址 ", index = 8)
    private String contactAddress;

    /**
     * 排序值
     */
    @ApiModelProperty(value = "排序值")
    @ExcelProperty(value = "排序值 ", index = 9)
    private Integer sort;


    /**
     * 占比
     */
    @ApiModelProperty(value = "占比")
    @ExcelProperty(value = "排序值 ", index = 10)
    private double ratio;

    /**
     * 客户合同编号
     */
    @ApiModelProperty(value = "客户合同编号")
    @ExcelProperty(value = "排序值 ", index = 11)
    private String cusContractNumber;

    /**
     * 客户Id
     */
    @ApiModelProperty(value = "客户Id")
    @ExcelProperty(value = "客户Id ", index = 11)
    private String customer;

}
