package com.chinasie.orion.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.DictConts;
import com.chinasie.orion.domain.dto.JobStudyReviewDTO;
import com.chinasie.orion.domain.entity.JobManage;
import com.chinasie.orion.domain.entity.JobStudyReview;
import com.chinasie.orion.domain.vo.JobStudyReviewVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.file.api.service.FileApiService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.JobStudyReviewMapper;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.JobManageService;
import com.chinasie.orion.service.JobStudyReviewService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/13/18:29
 * @description:
 */

@Service
@Slf4j
public class JobStudyReviewServiceImpl extends OrionBaseServiceImpl<JobStudyReviewMapper, JobStudyReview> implements JobStudyReviewService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private FileApiService fileApiService;

    @Autowired
    private DictRedisHelper dictRedisHelper;

    private JobManageService jobManageService;
    @Autowired
    public void setJobManageService(JobManageService jobManageService) {
        this.jobManageService = jobManageService;
    }

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public JobStudyReviewVO detail(String id, String pageCode) throws Exception {
        JobStudyReview jobStudyReview =this.getById(id);
        JobStudyReviewVO result = BeanCopyUtils.convertTo(jobStudyReview,JobStudyReviewVO::new);
        setEveryName(Collections.singletonList(result));

        List<FileVO> fileVOList = fileApiService.getFilesByDataId(id);
        result.setFileVOList(fileVOList);

        if(StringUtils.hasText(jobStudyReview.getJobId())){
            JobManage jobManage = jobManageService.getById(jobStudyReview.getJobId());
            result.setJobName(null == jobManage?"":jobManage.getName());
            result.setJobNumber(null == jobManage?"":jobManage.getNumber());
        }
        return result;
    }

    /**
     *  新增
     *
     * * @param jobStudyReviewDTO
     */
    @Override
    public  String create(JobStudyReviewDTO jobStudyReviewDTO) throws Exception {
        JobStudyReview jobStudyReview =BeanCopyUtils.convertTo(jobStudyReviewDTO,JobStudyReview::new);
        this.save(jobStudyReview);

        String rsp=jobStudyReview.getId();

        List<FileDTO> fileDTOList = jobStudyReviewDTO.getFileDTOList();
        if(!CollectionUtils.isEmpty(fileDTOList)){
            fileDTOList.forEach(item->{
                item.setDataType("JobStudyReview");
                item.setDataId(rsp);
            });
            fileApiService.batchSaveFile(fileDTOList);
        }
        return rsp;
    }

    /**
     *  编辑
     *
     * * @param jobStudyReviewDTO
     */
    @Override
    public Boolean edit(JobStudyReviewDTO jobStudyReviewDTO) throws Exception {
        JobStudyReview jobStudyReview =BeanCopyUtils.convertTo(jobStudyReviewDTO,JobStudyReview::new);

        this.updateById(jobStudyReview);

        String rsp=jobStudyReview.getId();

        List<FileDTO> fileDTOList = jobStudyReviewDTO.getFileDTOList();

        if(!CollectionUtils.isEmpty(fileDTOList)){
            List<FileVO> fileVOList = fileApiService.getFilesByDataId(rsp);
            if(!CollectionUtils.isEmpty(fileVOList)){
                fileApiService.removeByIds(fileVOList.stream().map(FileVO::getId).distinct().collect(Collectors.toList()));
            }
            fileDTOList.forEach(item->{
                item.setId(null);
                item.setDataType("JobStudyReview");
                item.setDataId(rsp);
            });
            fileApiService.batchSaveFile(fileDTOList);
        }
        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<JobStudyReviewVO> pages( Page<JobStudyReviewDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<JobStudyReview> condition = new LambdaQueryWrapperX<>( JobStudyReview. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(JobStudyReview::getCreateTime);

        JobStudyReviewDTO jobStudyReviewDTO =   pageRequest.getQuery();
        if(Objects.isNull(jobStudyReviewDTO)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "参数错误");
        }
        if(StringUtils.hasText(jobStudyReviewDTO.getJobId())){
            condition.eq(JobStudyReview::getJobId,jobStudyReviewDTO.getJobId());
        }
        Page<JobStudyReview> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), JobStudyReview::new));

        PageResult<JobStudyReview> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<JobStudyReviewVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<JobStudyReviewVO> vos = BeanCopyUtils.convertListTo(page.getContent(), JobStudyReviewVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void  setEveryName(List<JobStudyReviewVO> vos)throws Exception {
        if(CollectionUtils.isEmpty(vos)){
            return;
        }
        List<String> dataIdList = vos.stream().map(ObjectVO::getId).distinct().collect(Collectors.toList());
        List<FileVO> fileVOList=  fileApiService.listMaxFileByDataIds(dataIdList);               ;

        List<DictValueVO> dictListByCode = dictRedisHelper.getDictListByCode(DictConts.STUDY_EXAMINE);
        Map<String,String> numberTodesc = new HashMap<>();
        if(!CollectionUtils.isEmpty(dictListByCode)){
            for (DictValueVO dictValueVO : dictListByCode) {
                numberTodesc.put(dictValueVO.getNumber(),dictValueVO.getDescription());
            }
        }
        Map<String,List<FileVO>> idToList = new HashMap<>();
        if(!CollectionUtils.isEmpty(fileVOList)){
            idToList = fileVOList.stream().collect(Collectors.groupingBy(FileVO::getDataId));
        }
        Map<String, List<FileVO>> finalIdToList = idToList;
        vos.forEach(vo->{
            vo.setFileVOList(finalIdToList.getOrDefault(vo.getId(),new ArrayList<>()));
            vo.setReviewConclusionName(numberTodesc.getOrDefault(vo.getReviewConclusion(),""));
        });
    }

    @Override
    public void saveDefault(String rsp) {
        JobStudyReview jobStudyReview = new JobStudyReview();
        jobStudyReview.setJobId(rsp);
        this.save(jobStudyReview);
    }

}

