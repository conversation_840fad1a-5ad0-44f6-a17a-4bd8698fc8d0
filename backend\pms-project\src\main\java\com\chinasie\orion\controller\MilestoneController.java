//package com.chinasie.orion.controller;
//
//import com.chinasie.orion.domain.dto.MilestoneDto;
//import com.chinasie.orion.domain.vo.MilestoneVo;
//import com.chinasie.orion.domain.vo.StatusEntityVo;
//import com.chinasie.orion.dto.ResponseDTO;
//import io.swagger.annotations.*;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//
///**
// * Created with IntelliJ IDEA.
// *
// * @author: wys
// * @date: 2022/02/22/14:54
// * @description:
// */
//@RestController
//@RequestMapping("/milestone")
//@Api(tags = "里程碑")
//public class MilestoneController {
//    @Autowired
//    private PlanService planService;
//
//    @ApiOperation("新增里程碑")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "milestoneDto", dataType = "MilestoneDto")
//    })
//    @PostMapping(value = "")
//    public ResponseDTO<String> savePlan( @RequestBody MilestoneDto milestoneDto) throws Exception {
//        try {
//            return new ResponseDTO(planService.saveMilestone(milestoneDto));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//    @ApiOperation("修改里程碑")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "milestoneDto", dataType = "MilestoneDto")
//    })
//    @PutMapping(value = "")
//    public ResponseDTO<String> updateById( @RequestBody MilestoneDto milestoneDto) throws Exception {
//        try {
//            return new ResponseDTO(planService.updateMilestone(milestoneDto));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//    @ApiOperation("获取里程碑状态列表")
//    @GetMapping(value = "/status/list")
//    public ResponseDTO<List<StatusEntityVo>> getMilestoneList() throws Exception {
//        try {
//            return new ResponseDTO(planService.getMilestoneList());
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//    @ApiOperation("获取当前项目的里程碑列表")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "projectId", dataType = "String")
//    })
//    @GetMapping(value = "/list")
//    public ResponseDTO<List<MilestoneVo>> updateById(@ApiParam("项目ID") @RequestParam("projectId") String projectId) throws Exception {
//        try {
//            return new ResponseDTO(planService.getListByProjectId(projectId));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//}
