package com.chinasie.orion.service.impl;


import com.chinasie.orion.domain.entity.ObjectEntity;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.ObjectEntityRepository;
import com.chinasie.orion.service.ObjectEntityService;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/06/10:40
 * @description:
 */
@Service
public class ObjectEntityServiceImpl extends OrionBaseServiceImpl<ObjectEntityRepository, ObjectEntity> implements ObjectEntityService {


    @Override
    public String getName(String id) throws Exception {
        ObjectEntity objectDTO = this.getById(id);
        if (ObjectUtils.isEmpty(objectDTO)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        return objectDTO.getName();
    }
}
