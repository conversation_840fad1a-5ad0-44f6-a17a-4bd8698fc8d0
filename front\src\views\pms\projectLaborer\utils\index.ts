import { nextTick, unref } from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import { useUserStore } from '/@/store/modules/user';
import { message } from 'ant-design-vue';
export const parseURL = () => {
  let qs: any = location.href.split('?');
  qs = qs[1] ? qs[1] : '';
  let obj = {};
  if (typeof qs !== 'string' || qs.length === 0) {
    return obj;
  }

  let key: any = [];
  let decode = decodeURIComponent;
  qs = qs.split('&');
  let qsLen = qs.length;
  for (let i = 0; i < qsLen; ++i) {
    let x = qs[i];
    let idx = x.indexOf('=');
    let k: any;
    let v;
    if (idx >= 0) {
      k = decode(x.substring(0, idx));
      v = decode(x.substring(idx + 1));
    } else {
      k = x;
      v = '';
    }
    if (key.indexOf(k) === -1) {
      obj[k] = v;
      key.push(k);
    } else if (obj[k] instanceof Array) {
      obj[k].push(v);
    } else {
      obj[k] = [obj[k], v];
    }
  }

  return obj;
};
export const removeTip = (document) => {
  nextTick(() => {
    const title = 'div[style="width: 100%; height: 100% !important; position: absolute !important; font-size: 100px !important; opacity: 0.1 !important; text-align: center !important; top: 0px !important; left: 0px !important; pointer-events: none; display: block !important; color: rgb(0, 0, 0) !important; margin: 0px !important; padding: 0px !important; transform: unset !important;"]';
    const sub_title = 'div[style="min-width: 100px !important; min-height: 40px !important; position: absolute !important; font-size: 20px !important; opacity: 0.8 !important; bottom: 40px !important; right: 40px !important; pointer-events: none; display: block !important; z-index: 999999 !important; color: rgb(0, 0, 0) !important; margin: 0px !important; padding: 0px !important; transform: unset !important;"]';
    const nodesTitle = document.querySelectorAll(title);
    const nodesSubTitle = document.querySelectorAll(sub_title);
    for (const s of nodesTitle) {
      s.innerText = '';
    }
    for (const s of nodesSubTitle) {
      s.innerText = '';
    }
  });
};

const userStore = useUserStore();
export const validateName = async (value, name, len) => {
  if (!value) {
    return Promise.reject(`${name}不能为空`);
  }

  if (value.length > len) {
    return Promise.reject(`${name}不能超过${len}个字`);
  }
  return Promise.resolve();
};

export function preview(filePath, filePostfix) {
  let getTime = function () {
    let fillZero = function (str) {
      let realNum;
      if (str < 10) {
        realNum = `0${str}`;
      } else {
        realNum = str;
      }
      return realNum;
    };

    let date = new Date();
    let year = date.getFullYear(); // 获取完整的年份(4位,1970-????)
    let month = date.getMonth() + 1; // 获取当前月份(0-11,0代表1月)
    let today = date.getDate(); // 获取当前日(1-31)
    let day = date.getDay(); // 获取当前星期X(0-6,0代表星期天)
    // let hour = date.getHours(); //获取当前小时数(0-23)
    let minute = date.getMinutes(); // 获取当前分钟数(0-59)
    let second = date.getSeconds(); // 获取当前秒数(0-59)
    let week = [
      '星期日',
      '星期一',
      '星期二',
      '星期三',
      '星期四',
      '星期五',
      '星期六',
    ];
    let nowTime;

    // nowTime = year + '年' + fillZero(month) + '月' + fillZero(today) + '日' + '  ' + fillZero(hour) + ':' +
    `${fillZero(minute)}:${fillZero(second)}  ${week[day]}  `;
    nowTime = `${year}-${fillZero(month)}-${fillZero(today)}`;
    return nowTime;
  };

  let fileView = 'http://192.168.17.188:8012/onlinePreview';
  let fileServer = 'http://192.168.17.211/hdfs/';
  let url = fileServer + filePath + filePostfix;

  let userName = userStore.getUserInfo.name;
  window.open(
    `${fileView
    }?url=${
      encodeURIComponent(btoa(url))
    }&wt=${
      encodeURIComponent(btoa(escape(`${userName}  ${getTime()}`)))}`,
  );
}

// 2184 【所有页面】所有跳转知识详情或者问答详情 都需要调用获取权限接口
// type: read download edit
export async function roleController(id, type = 'read') {
  const response = await new Api('/kms').fetch(`/role/${id}`);
  if (response.includes(type)) {
    return true;
  }
  message.error('您无权限操作');
  return false;
}

export async function handleDetailController(id, path = 'knowledge/detailNew') {
  const authority = await roleController(id, 'read');
  if (authority) {
    window.open('/' + `${path}?id=${id}`);
  }
}

export function formatDate(time, format = 'YYYY-MM-DD HH:mm:ss') {
  return time ? dayjs(time).format(format) : '';
}

export function stringIntercept(name, len = 4) {
  if (name) {
    return name.length > len ? `${name.slice(0, len)}...` : name;
  }
  return name;
}
export const addValueLabel = (arr, value = 'id', label = 'name') => arr.map((s) => ({
  ...s,
  value: s[value],
  label: s[label],
}));
