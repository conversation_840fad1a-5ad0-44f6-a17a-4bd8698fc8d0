<?xml version="1.0" encoding="UTF-8"?>

<!--
Copyright The Codehaus Foundation.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>org.codehaus.plexus</groupId>
  <artifactId>plexus</artifactId>
  <packaging>pom</packaging>
  <version>2.0.7</version>

  <name>Plexus</name>
  <description>The Plexus project provides a full software stack for creating and executing software projects.</description>
  <url>http://plexus.codehaus.org/</url>
  <inceptionYear>2001</inceptionYear>
  <organization>
    <name>Codehaus</name>
    <url>http://www.codehaus.org/</url>
  </organization>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <developers>
    <developer>
      <id>jvanzyl</id>
      <name>Jason van Zyl</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
        <role>Release Manager</role>
      </roles>
    </developer>
    <developer>
      <id>kaz</id>
      <name>Pete Kazmier</name>
      <email />
      <organization />
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>jtaylor</id>
      <name>James Taylor</name>
      <email><EMAIL></email>
      <organization />
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>dandiep</id>
      <name>Dan Diephouse</name>
      <email><EMAIL></email>
      <organization>Envoi solutions</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>kasper</id>
      <name>Kasper Nielsen</name>
      <email><EMAIL></email>
      <organization />
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>bwalding</id>
      <name>Ben Walding</name>
      <email><EMAIL></email>
      <organization>Walding Consulting Services</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>mhw</id>
      <name>Mark Wilkinson</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>michal</id>
      <name>Michal Maczka</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>evenisse</id>
      <name>Emmanuel Venisse</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Trygve Laugstøl</name>
      <id>trygvis</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Kenney Westerhof</name>
      <id>kenney</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Carlos Sanchez</name>
      <id>carlos</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Brett Porter</name>
      <id>brett</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>John Casey</name>
      <id>jdcasey</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Andrew Williams</name>
      <id>handyande</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Rahul Thakur</name>
      <id>rahul</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Joakim Erdfelt</name>
      <id>joakime</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Olivier Lamy</name>
      <id>olamy</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Hervé Boutemy</name>
      <id>hboutemy</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Oleg Gusakov</name>
      <id>oleg</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Vincent Siveton</name>
      <id>vsiveton</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
  </developers>

  <mailingLists>
    <mailingList>
      <name>Plexus User List</name>
      <subscribe>http://xircles.codehaus.org/manage_email/user%40plexus.codehaus.org</subscribe>
      <unsubscribe>http://xircles.codehaus.org/manage_email/user%40plexus.codehaus.org</unsubscribe>
      <archive>http://archive.plexus.codehaus.org/user</archive>
      <post><EMAIL></post>
    </mailingList>
    <mailingList>
      <name>Plexus Developer List</name>
      <subscribe>http://xircles.codehaus.org/manage_email/dev%40plexus.codehaus.org</subscribe>
      <unsubscribe>http://xircles.codehaus.org/manage_email/dev%40plexus.codehaus.org</unsubscribe>
      <archive>http://archive.plexus.codehaus.org/dev</archive>
      <post><EMAIL></post>
    </mailingList>
    <mailingList>
      <name>Plexus Announce List</name>
      <subscribe>http://xircles.codehaus.org/manage_email/announce%40plexus.codehaus.org</subscribe>
      <unsubscribe>http://xircles.codehaus.org/manage_email/announce%40plexus.codehaus.org</unsubscribe>
      <archive>http://archive.plexus.codehaus.org/announce</archive>
    </mailingList>
    <mailingList>
      <name>Plexus Commit List</name>
      <subscribe>http://xircles.codehaus.org/manage_email/scm%40plexus.codehaus.org</subscribe>
      <unsubscribe>http://xircles.codehaus.org/manage_email/scm%40plexus.codehaus.org</unsubscribe>
      <archive>http://archive.plexus.codehaus.org/scm</archive>
    </mailingList>
  </mailingLists>

  <scm>
    <connection>scm:svn:http://svn.codehaus.org/plexus/pom/tags/plexus-2.0.7</connection>
    <developerConnection>scm:svn:https://svn.codehaus.org/plexus/pom/tags/plexus-2.0.7</developerConnection>
    <url>http://fisheye.codehaus.org/browse/plexus/pom/tags/plexus-2.0.7</url>
  </scm>
  <issueManagement>
    <system>JIRA</system>
    <url>http://jira.codehaus.org/browse/PLX</url>
  </issueManagement>
  <ciManagement>
    <notifiers>
      <notifier>
        <type>mail</type>
        <configuration>
          <address><EMAIL></address>
        </configuration>
      </notifier>
    </notifiers>
  </ciManagement>
  <distributionManagement>
    <repository>
      <id>plexus-releases</id>
      <name>Plexus Release Repository</name>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
    <snapshotRepository>
      <id>plexus-snapshots</id>
      <name>Plexus Snapshot Repository</name>
      <url>${plexusDistMgmtSnapshotsUrl}</url>
    </snapshotRepository>
    <site>
      <id>codehaus.org</id>
      <url>dav:https://dav.codehaus.org/plexus</url>
    </site>
  </distributionManagement>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <plexusDistMgmtSnapshotsUrl>https://oss.sonatype.org/content/repositories/plexus-snapshots</plexusDistMgmtSnapshotsUrl>
  </properties>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>3.8.2</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <pluginManagement>
      <plugins>
        <!-- set versions of common plugins for reproducibility, ordered alphabetically -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-clean-plugin</artifactId>
          <version>2.4.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>2.3.1</version>
          <configuration>
            <source>1.4</source>
            <target>1.4</target>
            <encoding>${project.build.sourceEncoding}</encoding>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>2.5</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-gpg-plugin</artifactId>
          <version>1.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-install-plugin</artifactId>
          <version>2.3.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>2.3.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>2.5</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-plugin-plugin</artifactId>
          <version>2.6</version>
        </plugin>
        <plugin>
          <artifactId>maven-release-plugin</artifactId>
          <version>2.0</version>
          <configuration>
            <goals>deploy</goals>
            <useReleaseProfile>false</useReleaseProfile>
            <arguments>-Pplexus-release</arguments>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-resources-plugin</artifactId>
          <version>2.4.3</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <version>2.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <version>2.1.2</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.5</version>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <artifactId>maven-project-info-reports-plugin</artifactId>
        <version>2.1.2</version>
      </plugin>
    </plugins>
  </reporting>

  <profiles>
    <profile>
      <id>reporting</id>
      <reporting>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-project-info-reports-plugin</artifactId>
            <version>2.1.2</version>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-report-plugin</artifactId>
            <version>2.4.3</version>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-checkstyle-plugin</artifactId>
            <version>2.2</version>
            <configuration>
              <configLocation>http://svn.apache.org/repos/asf/maven/plugins/tags/maven-checkstyle-plugin-2.2/src/main/resources/config/maven_checks.xml</configLocation>
              <headerLocation>http://svn.codehaus.org/plexus/pom/trunk/src/main/resources/config/plexus-header.txt</headerLocation>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-pmd-plugin</artifactId>
            <version>2.4</version>
            <configuration>
              <rulesets>
                <ruleset>http://svn.apache.org/repos/asf/maven/plugins/trunk/maven-pmd-plugin/src/main/resources/rulesets/maven.xml</ruleset>
              </rulesets>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>taglist-maven-plugin</artifactId>
            <version>2.4</version>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-jxr-plugin</artifactId>
            <version>2.1</version>
            <configuration>
              <inputEncoding>${project.build.sourceEncoding}</inputEncoding>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>2.5</version>
            <configuration>
              <links>
                <link>http://java.sun.com/j2ee/1.4/docs/api</link>
                <link>http://junit.sourceforge.net/javadoc/</link>
              </links>
            </configuration>
            <reportSets>
              <reportSet>
                <reports>
                  <report>javadoc</report>
                  <report>test-javadoc</report>
                </reports>
              </reportSet>
            </reportSets>
          </plugin>
        </plugins>
      </reporting>
    </profile>
    <profile>
      <id>plexus-release</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <configuration>
              <passphrase>${gpg.passphrase}</passphrase>
            </configuration>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-sources</id>
                <goals>
                  <goal>jar-no-fork</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>maven-3</id>
      <activation>
        <file>
          <!-- This employs that the basedir expression is only recognized by Maven 3.x (see MNG-2363) -->
          <exists>${basedir}</exists>
        </file>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-site-plugin</artifactId>
            <version>2.1</version>
            <inherited>false</inherited>
            <executions>
              <execution>
                <id>attach-descriptor</id>
                <goals>
                  <goal>attach-descriptor</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>

    </profile>

  </profiles>
</project>
