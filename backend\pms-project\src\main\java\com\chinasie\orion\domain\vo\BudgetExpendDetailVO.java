package com.chinasie.orion.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ExcelIgnoreUnannotated
public class BudgetExpendDetailVO {

    /**
     * 成本支出编码
     */
    @ApiModelProperty(value = "成本支出编码")
    @ExcelProperty(value = "成本支出编码 ", index = 1)
    private String expendNumber;


    @ApiModelProperty(value = "成本中心名称")
    @ExcelProperty(value = "成本中心名称 ", index = 2)
    private String costCenterName;
    /**
     * 科目名称
     */
    @ApiModelProperty(value = "科目名称")
    @ExcelProperty(value = "科目 ", index = 3)
    private String expenseSubjectName;

    @ApiModelProperty(value = "发生人名称")
    @ExcelProperty(value = "发生人 ", index = 4)
    private String occurrencePersonName;


    @ApiModelProperty(value = "发生时间")
    @ExcelProperty(value = "发生时间 ", index = 5)
    private Date occurrenceTime;

    @ApiModelProperty(value = "预算编码")
    @ExcelProperty(value = "预算编码 ", index = 6)
    private String number;

    @ApiModelProperty(value = "预算名称")
    @ExcelProperty(value = "预算名称 ", index = 7)
    private String name;

    @ApiModelProperty(value = "币种")
    @ExcelProperty(value = "币种名称 ", index = 8)
    private String currencyName;

    @ApiModelProperty(value = "支出金额")
    @ExcelProperty(value = "支出金额 ", index = 9)
    private BigDecimal expendMoney;


    @ApiModelProperty(value = "占/领用")
    @ExcelProperty(value = "占/领用 ", index = 10)
    private String occupationReceive;

    /**
     * 币种
     */
    @ApiModelProperty(value = "描述")
    @ExcelProperty(value = "描述 ", index = 11)
    private String remark;

    /**
     * 科目编码
     */
    @ApiModelProperty(value = "科目编码")
    private String expenseSubjectId;
    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    private String currency;
    /**
     * 发生人
     */
    @ApiModelProperty(value = "发生人")
    private String occurrencePerson;


    /**
     * 成本中心Id
     */
    @ApiModelProperty(value = "成本中心Id")
    private String costCenterId;

    @ApiModelProperty(value = "成本Id")
    private String id;





}
