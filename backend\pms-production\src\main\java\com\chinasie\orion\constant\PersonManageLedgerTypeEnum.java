package com.chinasie.orion.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/23/20:11
 * @description:
 */
public enum PersonManageLedgerTypeEnum {

    INPUT("input","进厂"),
    DATA_EDIT("edit","信息变更"),
    OUT("out","离厂"),

    ;

    public void setKey(String key) {
        this.key = key;
    }


    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }


    PersonManageLedgerTypeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    private String key;


    private String desc;

    public static Map<String,String> keyDesc(){
        Map<String,String> keyToDescMap = new HashMap<>();
        PersonManageLedgerTypeEnum[] values = PersonManageLedgerTypeEnum.values();
        for (PersonManageLedgerTypeEnum value : values) {
            keyToDescMap.put(value.getKey(),value.getDesc());
        }

        return  keyToDescMap;
    }
}
