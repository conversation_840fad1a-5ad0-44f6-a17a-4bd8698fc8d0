<script setup lang="ts">
import { OrionTable } from 'lyra-component-vue3';
import Api from '/@/api';
import { ref, Ref } from 'vue';

const props = defineProps<{
  projectId:string
}>();

const getTableRef = () => tableRef.value;
const tableRef:Ref = ref();
const tableOptions = {
  deleteToolButton: 'add|delete|enable|disable',
  showIndexColumn: true,
  rowSelection: {},
  searchInfo: {
    query: { projectId: props.projectId },
  },
  api(params) {
    const url = '/pms/deliverable/page';
    return new Api(url).fetch(params, '', 'POST');
  },
  columns: [
    {
      title: '交付物编号',
      dataIndex: 'number',
    },
    {
      title: '交付物名称',
      dataIndex: 'name',
    },
    {
      title: '状态',
      dataIndex: 'statusName',
    },
    {
      title: '计划交付物时间',
      dataIndex: 'predictDeliverTime',
    },
    {
      title: '实际交付物时间',
      dataIndex: 'deliveryTime',
    },
    {
      title: '所属任务',
      dataIndex: 'planName',
    },
    {
      title: '交付物负责人',
      dataIndex: 'principalName',
    },
  ],
};
defineExpose({
  getTableRef,
});
</script>

<template>
  <div class="content-box-table">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    />
  </div>
</template>

<style scoped lang="less">
.content-box-table{
  overflow: hidden;
  height: 100%;
}
</style>
