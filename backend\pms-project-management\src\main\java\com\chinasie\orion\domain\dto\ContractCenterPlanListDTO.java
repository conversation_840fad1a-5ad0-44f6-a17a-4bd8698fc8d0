package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class ContractCenterPlanListDTO implements Serializable {

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同主表id")
    private String id;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 用人单位代号
     */
    @ApiModelProperty(value = "用人单位代号")
    private String centerCode;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    private Integer year;



}
