<template>
  <div class="home-wrap">
    <div class="home-layout flex">
      <div class="left">
        <Statistics />
        <TodoListIndex />
      </div>
      <div class="right">
        <Notice />
        <IconNav />
        <CommonTemplates />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import Statistics from './component/Statistics.vue';
import TodoListIndex from './component/TodoListIndex.vue';
import Notice from './component/Notice.vue';
import IconNav from './component/IconNav.vue';
import CommonTemplates from './component/CommonTemplates.vue';

export default defineComponent({
  name: 'PMSXHome',
  components: {
    Statistics,
    TodoListIndex,
    Notice,
    IconNav,
    CommonTemplates,
  },
});
</script>

<style scoped lang="less">
  .home-wrap {
    width: 100%;
    height: 100%;
    overflow: auto;
    display: block;
  }

  .home-layout {
    min-width: 1200px;
    padding: 16px;
    margin-bottom: 30px;

    > .left {
      flex: 0 0 70%;
      width: 1px;
    }

    > .right {
      //flex: 0 0 30%;
      min-width: 430px;
      flex: 1;
      //width: 1px;
      margin-left: 16px;
    }
  }
</style>
