package com.chinasie.orion.domain.dto.assetApply;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * ProjectAssetApplyDetailAssets DTO对象
 *
 * <AUTHOR>
 * @since 2024-12-03 14:25:17
 */
@ApiModel(value = "ProjectAssetApplyDetailAssetsDTO对象", description = "资产转固申请详情表-Asset")
@Data
@ExcelIgnoreUnannotated
public class ProjectAssetApplyDetailAssetsDTO extends ObjectDTO implements Serializable {

    /**
     * 资产转固申请主表id
     */
    @ApiModelProperty(value = "资产转固申请主表id")
    @ExcelProperty(value = "资产转固申请主表id ", index = 0)
    private String assetApplyId;

    /**
     * 资产转固申请主表id
     */
    @ApiModelProperty(value = "资产信息表id")
    private List<String> assetSyncIds;

//    /**
//     * 采购申请行号
//     */
//    @ApiModelProperty(value = "采购申请行号")
//    @ExcelProperty(value = "采购申请行号 ", index = 1)
//    private String procurementApplicantLineNumber;
//
//    /**
//     * 项目编号/名称
//     */
//    @ApiModelProperty(value = "项目编号/名称")
//    @ExcelProperty(value = "项目编号/名称 ", index = 2)
//    private String projectIdName;
//
//    /**
//     * 总账科目
//     */
//    @ApiModelProperty(value = "总账科目")
//    @ExcelProperty(value = "总账科目 ", index = 3)
//    private String generalLedgerSubject;
//
//    /**
//     * 资产
//     */
//    @ApiModelProperty(value = "资产")
//    @ExcelProperty(value = "资产 ", index = 4)
//    private String asset;
//
//    /**
//     * 需求数量
//     */
//    @ApiModelProperty(value = "需求数量")
//    @ExcelProperty(value = "需求数量 ", index = 5)
//    private String requiredQuantity;
//
//    /**
//     * 单位
//     */
//    @ApiModelProperty(value = "单位")
//    @ExcelProperty(value = "单位 ", index = 6)
//    private String unit;
//
//    /**
//     * 交货时间
//     */
//    @ApiModelProperty(value = "交货时间")
//    @ExcelProperty(value = "交货时间 ", index = 7)
//    private Date deliveryTime;
//
//    /**
//     * 单价
//     */
//    @ApiModelProperty(value = "单价")
//    @ExcelProperty(value = "单价 ", index = 8)
//    private String unitPrice;
//
//    /**
//     * 总价
//     */
//    @ApiModelProperty(value = "总价")
//    @ExcelProperty(value = "总价 ", index = 9)
//    private String totalPrice;
//
//    /**
//     * 本位币金额
//     */
//    @ApiModelProperty(value = "本位币金额")
//    @ExcelProperty(value = "本位币金额 ", index = 10)
//    private BigDecimal localCurrencyAmount;
//
//    /**
//     * 成本中心
//     */
//    @ApiModelProperty(value = "成本中心")
//    @ExcelProperty(value = "成本中心 ", index = 11)
//    private String costCenter;
//
//    /**
//     * 物料
//     */
//    @ApiModelProperty(value = "物料")
//    @ExcelProperty(value = "物料 ", index = 12)
//    private String item;
//
//    /**
//     * 物料组
//     */
//    @ApiModelProperty(value = "物料组")
//    @ExcelProperty(value = "物料组 ", index = 13)
//    private String itemGroup;
//
//    /**
//     * 内部订单
//     */
//    @ApiModelProperty(value = "内部订单")
//    @ExcelProperty(value = "内部订单 ", index = 14)
//    private String internalOrder;
//
//    /**
//     * WBS编号
//     */
//    @ApiModelProperty(value = "WBS编号")
//    @ExcelProperty(value = "WBS编号 ", index = 15)
//    private String wbsId;


}
