<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
    @selectionChange="selectionChange"
  >
    <template
      v-if="['risk','question'].includes(props.pageType)"
      #toolbarLeft
    >
      <div class="button-margin-right">
        <BasicButton
          v-if="isPower(powerCode?.addCode, powerData)"
          type="primary"
          icon="sie-icon-tianjiaxinzeng"
          @click="addTableNode"
        >
          新增变更
        </BasicButton>

        <BasicButton
          v-if="isPower(powerCode?.deleteCode, powerData)"
          icon="sie-icon-shanchu"
          :disabled="selectRowKeys.length===0"
          @click="deleteBatch"
        >
          删除
        </BasicButton>
      </div>
    </template>
  </OrionTable>
</template>
<script setup lang="ts">
import {
  BasicButton, isPower, OrionTable, openDrawer, DataStatusTag,
} from 'lyra-component-vue3';
import {
  h, inject, Ref, ref, onMounted,
} from 'vue';
import { message, Modal } from 'ant-design-vue';
import { useRoute, useRouter } from 'vue-router';
import { stampDate } from '/@/utils/dateUtil';
import Api from '/@/api';
import dayjs from 'dayjs';
import AddAssociationAlterNode from './components/AddAssociationAlterNode.vue';
import AddTableNode from './components/AddTableNode.vue';
const props = withDefaults(defineProps<{
    pageType:string,
    getAlterTableDataApi:any,
    addAlterTableApi:any,
    alterType:string
    powerCode?:object
}>(), {
  pageType: 'risk',
  getAlterTableDataApi: null,
  addAlterTableApi: null,
  alterType: '',
  powerCode: () => ({}),
});
const tableRef = ref(null);
const powerData = inject('powerData');
const formData:any = inject('formData', {});
const router = useRouter();
const selectRowKeys:Ref<string[]> = ref([]);

const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: {},
  showSmallSearch: true,
  showIndexColumn: false,
  filterConfigName: 'PAS_BECURRENTMANAGE_CHANGEMANAGE_CHANGEREQUESTMANAGE',
  api(params) {
    if (!formData.value.id) {
      return Promise.resolve([]);
    }
    return props.getAlterTableDataApi(params);
  },
  columns: initColumns(),
  actions: [
    {
      text: '编辑',
      isShow: (record) => isPower(props.powerCode?.editCode, powerData),
      onClick(record) {
        let drawerData:any = {};
        if (props.alterType === 'plan') {
          drawerData = {
            type: 'edit',
            data: {
              type: 'project_scheme',
              id: record.id,
            },
          };
        } else {
          drawerData = {
            type: 'edit',
            id: record.id,
          };
        }
        openFormDrawer(drawerData);
      },
    },
    {
      text: '删除',
      isShow: (record) => isPower(props.powerCode?.deleteCode, powerData),
      onClick(record) {
        deleteBatchData([record.id], 'one');
      },
    },
    {
      text: '关闭',
      isShow: (record) => isPower(props.powerCode?.closeCode, powerData),
      modal(record) {
        return new Api('/pas').fetch('', `ecr/close/${record.id}`, 'PUT').then((res) => {
          message.success('关闭成功');
          tableRef.value.reload();
        });
      },
    },
  ],
});

const addTableNode = () => {
  let drawerData:any = {};
  if (props.alterType === 'plan') {
    drawerData = {
      type: 'add',
      sourceId: formData.value.id,
      data: {
        type: 'project_scheme',
      },
      schemeData: {
        projectId: formData.value.projectId,
        projectName: formData.value.projectName,
        dataSource: [formData.value],
      },
    };
    // openFormPlanDrawer(
    //   {
    //     type: 'add',
    //     sourceId: formData.value.id,
    //     data: {
    //       type: 'project_scheme',
    //     },
    //     schemeData: {
    //       projectId: formData.value.projectId,
    //       projectName: formData.value.projectName,
    //       dataSource: [formData.value],
    //     },
    //   },
    // );
  } else {
    drawerData = {
      type: 'add',
      isRisk: true,
      sourceId: formData.value.id,
    };
  }

  openFormDrawer(drawerData);
};

function openFormDrawer(drawerData) {
  const drawerRef: Ref = ref();
  const component = props.alterType === 'plan' ? AddTableNode : AddAssociationAlterNode;
  openDrawer({
    title: drawerData.type === 'add' ? '新增变更' : '编辑变更',
    width: 1000,
    content() {
      return h(component, {
        ref: drawerRef,
        drawerData,
        addAlterTableApi: props.addAlterTableApi,
      });
    },
    async onOk(): Promise<void> {
      await drawerRef.value.saveData();
      tableRef.value.reload({ page: 1 });
    },
  });
}
function deleteBatch() {
  deleteBatchData(selectRowKeys.value, 'batch');
}
function deleteBatchData(params, type) {
  Modal.confirm({
    title: '删除提示',
    content: type === 'batch' ? '是否删除选中的数据？' : '是否删除该条数据？',
    onOk() {
      new Api('/pas').fetch(params, 'ecr/remove', 'DELETE').then((res) => {
        message.success('删除成功。');
        tableRef.value.reload({ page: 1 });
      });
    },
  });
}
const openDetails = (record) => {
  router.push({
    name: 'PASChangeApplyDetails',
    params: {
      id: record.id,
    },
  });
};

function initColumns() {
  let columns:Record<any, any>[] = [
    {
      title: '标题',
      dataIndex: 'name',
      minWidth: 200,
      customRender({ record, text }) {
        return isPower(props.powerCode?.checkCode, powerData) ? h('span', {
          class: 'action-btn',
          onClick: () => openDetails(record),
        }, text) : text;
      },
    },
    {
      title: '编号',
      dataIndex: 'number',
      slots: { customRender: 'messageName' },
      width: 200,
    },
    {
      title: '变更类型',
      dataIndex: 'changeWay',
      width: 100,
      customRender: ({
        text, record, index, column,
      }) =>
        (record.changeWay === 1 ? '快速变更' : '工程变更'),
    },
    {
      title: '所属类型',
      dataIndex: 'ecrTypeName',
      width: 100,
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 150,
      slots: { customRender: 'status' },
    },
    {
      title: '负责人',
      dataIndex: 'responsiblerName',
      width: 150,
    },
    {
      title: '申请时间',
      dataIndex: 'applyTime',
      type: 'dateTime',
      customRender: ({
        text, record, index, column,
      }) =>
        (record.applyTime && record.applyTime.length > 0
          ? stampDate(record.applyTime, 'yyyy-MM-dd HH:mm:ss')
          : ''),
    },
  ];
  if (['risk', 'question'].includes(props.pageType)) {
    columns.push({
      title: '操作',
      dataIndex: 'action',
      width: 150,
      fixed: 'right',
      slots: { customRender: 'action' },
    });
  }
  return columns;
}
function selectionChange(data) {
  selectRowKeys.value = data.keys;
}
const updateData = () => {
  tableRef.value.reload();
};
</script>
