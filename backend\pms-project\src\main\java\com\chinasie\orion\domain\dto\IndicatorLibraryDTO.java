package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;

/**
 * IndicatorLibrary DTO对象
 *
 * <AUTHOR>
 * @since 2024-03-25 15:13:04
 */
@ApiModel(value = "IndicatorLibraryDTO对象", description = "绩效管理指标库")
public class IndicatorLibraryDTO extends ObjectDTO implements Serializable {

    /**
     * 绩效评分标准
     */
    @ApiModelProperty(value = "绩效评分标准")
    private String scoreStandard;

    /**
     * 该指标默认权重
     */
    @ApiModelProperty(value = "该指标默认权重")
    private BigDecimal weight;

    /**
     * 绩效指标名称
     */
    @ApiModelProperty(value = "绩效指标名称")
    private String name;

    public String getScoreStandard() {
        return scoreStandard;
    }

    public void setScoreStandard(String scoreStandard) {
        this.scoreStandard = scoreStandard;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
