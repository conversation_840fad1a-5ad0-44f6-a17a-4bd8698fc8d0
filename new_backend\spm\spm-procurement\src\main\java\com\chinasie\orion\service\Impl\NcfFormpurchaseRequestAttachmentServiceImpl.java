package com.chinasie.orion.service.Impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.domain.dto.NcfFormpurchaseRequestAttachmentDTO;
import com.chinasie.orion.domain.entity.NcfFormpurchaseRequestAttachment;
import com.chinasie.orion.domain.vo.NcfFormpurchaseRequestAttachmentVO;
import com.chinasie.orion.repository.NcfFormpurchaseRequestAttachmentMapper;
import com.chinasie.orion.service.NcfFormpurchaseRequestAttachmentService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <p>
 * NcfFormpurchaseRequestAttachment 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-11 10:49:58
 */
@Service
@Slf4j
public class NcfFormpurchaseRequestAttachmentServiceImpl extends OrionBaseServiceImpl<NcfFormpurchaseRequestAttachmentMapper, NcfFormpurchaseRequestAttachment> implements NcfFormpurchaseRequestAttachmentService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public NcfFormpurchaseRequestAttachmentVO detail(String id, String pageCode) throws Exception {
        NcfFormpurchaseRequestAttachment ncfFormpurchaseRequestAttachment = this.getById(id);
        NcfFormpurchaseRequestAttachmentVO result = BeanCopyUtils.convertTo(ncfFormpurchaseRequestAttachment, NcfFormpurchaseRequestAttachmentVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param ncfFormpurchaseRequestAttachmentDTO
     */
    @Override
    public boolean create(List<NcfFormpurchaseRequestAttachmentDTO> ncfFormpurchaseRequestAttachmentDTOs) throws Exception {
        List<NcfFormpurchaseRequestAttachment> ncfFormpurchaseRequestAttachments = BeanCopyUtils.convertListTo(ncfFormpurchaseRequestAttachmentDTOs, NcfFormpurchaseRequestAttachment::new);
        boolean rsp = this.saveBatch(ncfFormpurchaseRequestAttachments);

        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param ncfFormpurchaseRequestAttachmentDTO
     */
    @Override
    public Boolean edit(NcfFormpurchaseRequestAttachmentDTO ncfFormpurchaseRequestAttachmentDTO) throws Exception {
        NcfFormpurchaseRequestAttachment ncfFormpurchaseRequestAttachment = BeanCopyUtils.convertTo(ncfFormpurchaseRequestAttachmentDTO, NcfFormpurchaseRequestAttachment::new);

        this.updateById(ncfFormpurchaseRequestAttachment);

        String rsp = ncfFormpurchaseRequestAttachment.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<NcfFormpurchaseRequestAttachmentVO> pages(Page<NcfFormpurchaseRequestAttachmentDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<NcfFormpurchaseRequestAttachment> condition = new LambdaQueryWrapperX<>(NcfFormpurchaseRequestAttachment.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(NcfFormpurchaseRequestAttachment::getCreateTime);


        Page<NcfFormpurchaseRequestAttachment> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), NcfFormpurchaseRequestAttachment::new));

        PageResult<NcfFormpurchaseRequestAttachment> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<NcfFormpurchaseRequestAttachmentVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<NcfFormpurchaseRequestAttachmentVO> vos = BeanCopyUtils.convertListTo(page.getContent(), NcfFormpurchaseRequestAttachmentVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public List<NcfFormpurchaseRequestAttachmentVO> getAttachmentsByCode(String code) {
        List<NcfFormpurchaseRequestAttachment> ments = this.getBaseMapper().selectList(new LambdaQueryWrapperX<NcfFormpurchaseRequestAttachment>().eq(NcfFormpurchaseRequestAttachment::getCode, code));
        return ments.stream().map(contact -> BeanCopyUtils.convertTo(contact, NcfFormpurchaseRequestAttachmentVO::new)).collect(Collectors.toList());
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "采购申请附件导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", NcfFormpurchaseRequestAttachmentDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        NcfFormpurchaseRequestAttachmentExcelListener excelReadListener = new NcfFormpurchaseRequestAttachmentExcelListener();
        EasyExcel.read(inputStream, NcfFormpurchaseRequestAttachmentDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<NcfFormpurchaseRequestAttachmentDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("采购申请附件导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<NcfFormpurchaseRequestAttachment> ncfFormpurchaseRequestAttachmentes = BeanCopyUtils.convertListTo(dtoS, NcfFormpurchaseRequestAttachment::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::NcfFormpurchaseRequestAttachment-import::id", importId, ncfFormpurchaseRequestAttachmentes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<NcfFormpurchaseRequestAttachment> ncfFormpurchaseRequestAttachmentes = (List<NcfFormpurchaseRequestAttachment>) orionJ2CacheService.get("pmsx::NcfFormpurchaseRequestAttachment-import::id", importId);
        log.info("采购申请附件导入的入库数据={}", JSONUtil.toJsonStr(ncfFormpurchaseRequestAttachmentes));

        this.saveBatch(ncfFormpurchaseRequestAttachmentes);
        orionJ2CacheService.delete("pmsx::NcfFormpurchaseRequestAttachment-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::NcfFormpurchaseRequestAttachment-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<NcfFormpurchaseRequestAttachment> condition = new LambdaQueryWrapperX<>(NcfFormpurchaseRequestAttachment.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(NcfFormpurchaseRequestAttachment::getCreateTime);
        List<NcfFormpurchaseRequestAttachment> ncfFormpurchaseRequestAttachmentes = this.list(condition);

        List<NcfFormpurchaseRequestAttachmentDTO> dtos = BeanCopyUtils.convertListTo(ncfFormpurchaseRequestAttachmentes, NcfFormpurchaseRequestAttachmentDTO::new);

        String fileName = "采购申请附件数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", NcfFormpurchaseRequestAttachmentDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<NcfFormpurchaseRequestAttachmentVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }


    public static class NcfFormpurchaseRequestAttachmentExcelListener extends AnalysisEventListener<NcfFormpurchaseRequestAttachmentDTO> {

        private final List<NcfFormpurchaseRequestAttachmentDTO> data = new ArrayList<>();

        @Override
        public void invoke(NcfFormpurchaseRequestAttachmentDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<NcfFormpurchaseRequestAttachmentDTO> getData() {
            return data;
        }
    }


}
