<script setup lang="ts">
import { BasicCard, OrionTable, UploadList } from 'lyra-component-vue3';
import {
  inject, reactive, ref, Ref, watch,
} from 'vue';

const detailsData: Record<string, any> = inject('detailsData');

const orderInfo = reactive({
  list: [
    {
      label: '工单号',
      field: ['jobManageVO', 'number'],
    },
    {
      label: '工作抬头',
      field: ['jobManageVO', 'workJobTitle'],
    },
    {
      label: '工作中心',
      field: ['jobManageVO', 'workCenter'],
    },
    {
      label: '工作名称',
      field: ['jobManageVO', 'name'],
    },
    {
      label: '工作负责人',
      field: ['jobManageVO', 'rspUserName'],
    },
    {
      label: '大修轮次',
      field: ['jobManageVO', 'repairRound'],
    },
    {
      label: '是否重点项目',
      field: ['jobManageVO', 'isMajorProject'],
      isBoolean: true,
    },
    {
      label: '功能位置',
      field: 'functionalLocation',
    },
    {
      label: '研读审查状态',
      field: ['jobManageVO', 'studyExamineStatusName'],
    },
  ],
  dataSource: detailsData,
});

const tableOptions = {
  showSmallSearch: false,
  isSpacing: false,
  showTableSetting: false,
  showToolButton: false,
  pagination: false,
  maxHeight: 300,
};

const riskColumns = [
  {
    title: '风险号',
    dataIndex: 'riskNumber',
  },
  {
    title: '风险描述',
    dataIndex: 'riskDesc',
  },
  {
    title: '风险类型',
    dataIndex: 'riskType',
  },
  {
    title: '风险长文本',
    dataIndex: 'riskText',
  },
];

const securityColumns = [
  {
    title: '安全措施',
    dataIndex: 'measureCode',
  },
  {
    title: '安全措施描述',
    dataIndex: 'measureDesc',
  },
  {
    title: '措施类型',
    dataIndex: 'measureType',
  },
  {
    title: '措施长文本',
    dataIndex: 'measureText',
  },
];

const securityMeasureVOList: Ref<any[]> = ref([]);
const customRow = (record: Record<string, any>) => ({
  onClick() {
    securityMeasureVOList.value = record?.securityMeasureVOList || [];
  },
});

watch(() => detailsData?.riskVOList, (value: Record<string, any>) => {
  securityMeasureVOList.value = value?.[0]?.securityMeasureVOList || [];
}, {
  immediate: true,
  deep: true,
});
</script>

<template>
  <BasicCard
    :is-border="false"
    title="工单信息"
    :grid-content-props="orderInfo"
  />
  <BasicCard
    :is-border="false"
    title="风险信息"
  >
    <div>
      <OrionTable
        :options="tableOptions"
        :dataSource="detailsData?.riskVOList||[]"
        :columns="riskColumns"
        :customRow="customRow"
      />
    </div>
  </BasicCard>
  <BasicCard
    :is-border="false"
    title="安措信息"
  >
    <div>
      <OrionTable
        :options="tableOptions"
        :dataSource="securityMeasureVOList||[]"
        :columns="securityColumns"
      />
    </div>
  </BasicCard>
  <BasicCard
    :is-border="false"
    title="工作包信息"
  >
    <UploadList
      :edit="false"
      :height="300"
      :is-spacing="false"
      :list-data="detailsData?.fileVOList||[]"
    />
  </BasicCard>
</template>

<style scoped lang="less">

</style>
