package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * DocumentDecomposition DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-01 17:00:34
 */
@ApiModel(value = "DocumentDecompositionDTO对象", description = "文档分解")
@Data
@ExcelIgnoreUnannotated
public class DocumentDecompositionDTO extends  ObjectDTO   implements Serializable{

    /**
     * 父级id
     */
    @ApiModelProperty(value = "父级id")
    @ExcelProperty(value = "父级id ", index = 0)
    @NotBlank(message = "父级ID不可为空")
    private String parentId;

    /**
     * 关联任务
     */
    @ApiModelProperty(value = "关联任务")
    @ExcelProperty(value = "关联任务 ", index = 1)
    private String taskId;

    /**
     * 条目标题
     */
    @ApiModelProperty(value = "条目标题")
    @ExcelProperty(value = "条目标题 ", index = 2)
    @NotBlank(message = "条目标题不可为空")
    @Length(max = 200, message = "条目标题长度不能超过200")
    private String name;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 3)
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @ExcelProperty(value = "主表ID ", index = 4)
    @NotBlank(message = "文档模板ID不可为空")
    private String mainTableId;

    /**
     * 条目编制内容
     */
    @ApiModelProperty(value = "条目编制内容")
    @ExcelProperty(value = "条目编制内容 ", index = 5)
    private String content;


}
