package com.chinasie.orion.management.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;

/**
 * ReqClarificationRecord Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-27 09:31:59
 */
@TableName(value = "pmsx_req_clarification_record")
@ApiModel(value = "ReqClarificationRecordEntity对象", description = "需求澄清记录")
@Data

public class ReqClarificationRecord extends  ObjectEntity  implements Serializable{

    /**
     * 澄清主题
     */
    @ApiModelProperty(value = "澄清主题")
    @TableField(value = "clarification_theme")
    private String clarificationTheme;

    /**
     * 澄清类型
     */
    @ApiModelProperty(value = "澄清类型")
    @TableField(value = "clarification_type")
    private String clarificationType;

    /**
     * 澄清批次
     */
    @ApiModelProperty(value = "澄清批次")
    @TableField(value = "clarification_lot")
    private String clarificationLot;

    /**
     * 澄清内容
     */
    @ApiModelProperty(value = "澄清内容")
    @TableField(value = "clarification_context")
    private String clarificationContext;

    /**
     * 是否更新报价时间
     */
    @ApiModelProperty(value = "是否更新报价时间")
    @TableField(value = "is_update_time")
    private String IsUpdateTime;

    /**
     * 需求编号
     */
    @ApiModelProperty(value = "需求编号")
    @TableField(value = "requirement_id")
    private String requirementId;

    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    @TableField(value = "release_time")
    private Date ReleaseTime;

    /**
     * 查看时间
     */
    @ApiModelProperty(value = "查看时间")
    @TableField(value = "check_time")
    private Date checkTime;

    /**
     * 查看状态
     */
    @ApiModelProperty(value = "查看状态")
    @TableField(value = "check_status")
    private String checkStatus;

    /**
     * 原报价开始时间
     */
    @ApiModelProperty(value = "原报价开始时间")
    @TableField(value = "origin_quote_start_time")
    private Date originQuoteStartTime;

    /**
     * 原报价截止时间
     */
    @ApiModelProperty(value = "原报价截止时间")
    @TableField(value = "origin_quote_dl_time")
    private Date originQuoteDlTime;

    /**
     * 原开启报价时间
     */
    @ApiModelProperty(value = "原开启报价时间")
    @TableField(value = "origin_quote_open_time")
    private Date originQuoteOpenTime;

    /**
     * 新开启报价时间
     */
    @ApiModelProperty(value = "新开启报价时间")
    @TableField(value = "new_quote_open_time")
    private Date newQuoteOpenTime;

    /**
     * 新报价截止时间
     */
    @ApiModelProperty(value = "新报价截止时间")
    @TableField(value = "new_quote_dl_time")
    private Date newQuoteDlTime;

    /**
     * 新报价开始时间
     */
    @ApiModelProperty(value = "新报价开始时间")
    @TableField(value = "new_quote_start_time")
    private Date newQuoteStartTime;

    /**
     * 澄清阶段
     */
    @ApiModelProperty(value = "澄清阶段")
    @TableField(value = "clarification_stage")
    private String clarificationStage;

}
