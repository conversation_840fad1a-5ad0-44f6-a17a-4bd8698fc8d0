package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ActualPayMilestone VO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@ApiModel(value = "ActualPayMilestoneVO对象", description = "合同支付里程碑（实际）")
@Data
public class ActualPayMilestoneVO extends ObjectVO implements Serializable {

    /**
     * 支付编号
     */
    @ApiModelProperty(value = "支付编号")
    private String payNumber;


    /**
     * 支付申请人
     */
    @ApiModelProperty(value = "支付申请人")
    private String payReqUser;


    /**
     * 支付申请发起时间
     */
    @ApiModelProperty(value = "支付申请发起时间")
    private Date payReqStartTime;


    /**
     * 支付类型
     */
    @ApiModelProperty(value = "支付类型")
    private String payType;


    /**
     * 预计付款时间
     */
    @ApiModelProperty(value = "预计付款时间")
    private Date estimatedPayTime;


    /**
     * 里程碑业务描述
     */
    @ApiModelProperty(value = "里程碑业务描述")
    private String milestoneDesc;


    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;


    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    private String currency;


    /**
     * 本次支付汇总金额
     */
    @ApiModelProperty(value = "本次支付汇总金额")
    private BigDecimal currentPayTotalAmount;


    /**
     * 支付比例
     */
    @ApiModelProperty(value = "支付比例")
    private String payRatio;

    /**
     * 已支付金额
     */
    @ApiModelProperty(value = "已支付金额")
    private BigDecimal paidAmount;


    /**
     * 合同预计验收时间
     */
    @ApiModelProperty(value = "合同预计验收时间")
    private Date estimatedAcceptanceTime;


    /**
     * 合同验收时间
     */
    @ApiModelProperty(value = "合同验收时间")
    private Date acceptanceTime;


    /**
     * 是否有质保金
     */
    @ApiModelProperty(value = "是否有质保金")
    private Boolean isHaveQualityAmount;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    private String mainTableId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;

    /**
     * 实际支付时间
     */
    @ApiModelProperty(value = "实际支付时间")
    private Date actualPayTime;

    /**
     * 原因
     */
    @ApiModelProperty(value = "原因")
    private String reason;

    /**
     * 实际验收时间
     */
    @ApiModelProperty(value = "实际验收时间")
    private Date actualAcceptanceTime;

    /**
     * 是否一次验收
     */
    @ApiModelProperty(value = "是否一次验收")
    private String isAcceptacneQualified;

    /**
     * 是否一次验收合格
     */
    @ApiModelProperty(value = "是否一次验收合格")
    private String isOnetimeAcceptance;

    /**
     * 是否按时交付
     */
    @ApiModelProperty(value = "是否按时交付")
    private String isDeliverOnTime;

    /**
     * 未按时交付验收原因
     */
    @ApiModelProperty(value = "未按时交付验收原因")
    private String reasonOfUndeliver;

    /**
     * 实际交付时间
     */
    @ApiModelProperty(value = "实际交付时间")
    private Date actualDeliveryTime;

    /**
     * 支付申请人名称
     */
    @ApiModelProperty(value = "支付申请人名称")
    private String payReqUserName;

    /**
     * 供应商编号
     */
    @ApiModelProperty(value = "供应商名称")
    private String supplierCode;

}
