package com.chinasie.orion.domain.vo.material;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class MaterialInVO implements Serializable {
    /**
     * 责任人id
     */
    @ApiModelProperty(value = "责任人id")
    private String rspUserId;

    /**
     * 责任人名称
     */
    @ApiModelProperty(value = "责任人名称")
    private String rspUserName;

    /**
     * 资产名称
     */
    @ApiModelProperty(value = "资产名称")
    private String assetName;

    /**
     * 资产代码
     */
    @ApiModelProperty(value = "资产代码")
    private String assetCode;

    @ApiModelProperty(value = "资产类型")
    private String assetType;

    @ApiModelProperty(value = "资产类型名称")
    private String assetTypeName;

    @ApiModelProperty(value = "资产编码")
    private String number;

    @ApiModelProperty(value = "计划进场日期")
    private Date inDate;

    @ApiModelProperty(value = "计划离场日期")
    private Date outDate;

    @ApiModelProperty(value = "实际进场日期")
    private Date actInDate;


    @ApiModelProperty(value = "是否向电厂报备")
    private Boolean isReport;

    @ApiModelProperty(value = "规格")
    private String specificationModel;

    @ApiModelProperty(value = "物质所在基地名称")
    private String baseName;

    @ApiModelProperty(value = "是否计量工具")
    private Boolean isMetering;

    @ApiModelProperty(value = "是否需要检定")
    private Boolean isVerification;

    @ApiModelProperty(value = "下次检定日期")
    private Date nextVerificationDate;

    @ApiModelProperty(value = "物资库id")
    private String materialManageId;

    @ApiModelProperty(value = "固定资产能力库id")
    private String fixedAssetsId;

    @ApiModelProperty(value = "资产所在地")
    private String storagePlaceName;


    @ApiModelProperty(value = "入库数量")
    private Integer inputStockNum;
}
