:deep(.scroll) {
  .margin {
    .padding {
      padding: 0 !important;
      margin: 1 !important;
    }
  }
}

.demo-header {
  padding: 15px;

  > div {
    font-size: 20px;
  }

  > span {
    font-size: 14px;
  }
}
.productLibraryIndex1 {
  //   min-width: 1280px;
  // margin: 0;  注意数据中心加载productLibraryIndex,导致margin:16px
  height: calc(~'100%');
  width: calc(~'100%');
  //   background: #ffffff;
  border-radius: 4px;
  display: flex;
  .productLibraryIndex_content {
    width: calc(~'100%');
    padding: 0px;
  }
  .tableName {
    color: #5871d5;
    cursor: pointer;
  }
  .productLibraryIndex_title {
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 13px 8px 8px 13px;
    .searchcenter {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .blueFont {
      vertical-align: center !important;
      &:hover {
        border: 1px solid #5871d5;
        color: #5871d5;
        box-sizing: border-box;
      }
    }
    .productLibraryIndex_btn {
      display: inline-block;
      width: 121px;
      height: 40px;
      line-height: 38px;
      text-align: center;
      border: 1px solid;
      border-radius: 4px;
      color: #444b5e;
      font-size: 16px;
      cursor: pointer;
      .labelSpan {
        padding-left: 10px;
      }
      .anticon {
        font-size: 15px;
        line-height: 30px;
      }
    }
    .productLibraryIndex_btn + .productLibraryIndex_btn {
      margin-left: 10px;
    }
    .addModel {
      background: #5871d5;
      color: #ffffff;
    }
  }
  .productLibraryIndex_table {
    padding: 0 8px;
  }
}
