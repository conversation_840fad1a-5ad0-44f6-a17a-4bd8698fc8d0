package com.chinasie.orion.domain.dto.importantProject;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/09/26/14:37
 * @description:
 */
@Data
public class ImportParamDTO implements Serializable {
    @ApiModelProperty("重大项目管理ID")
    private String importantId;
    @ApiModelProperty("关键词")
    private String keyword;
    @ApiModelProperty("大修轮次")
    private String repairRound;

}
