package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;

/**
 * WorkHourFillDetail Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-15 15:15:20
 */
@TableName(value = "pms_work_hour_fill_detail")
@ApiModel(value = "WorkHourFillDetail对象", description = "工时填报明细")
@Data
public class WorkHourFillDetail extends ObjectEntity implements Serializable{

    /**
     * 工时填报天id
     */
    @ApiModelProperty(value = "工时填报天id")
    @TableField(value = "fill_day_id" )
    private String fillDayId;

    /**
     * 工时日期
     */
    @ApiModelProperty(value = "工时日期")
    @TableField(value = "work_date" )
    private String workDate;

    /**
     * 工时
     */
    @ApiModelProperty(value = "工时")
    @TableField(value = "work_hour" )
    private Integer workHour;

    /**
     * 项目地点
     */
    @ApiModelProperty(value = "项目地点")
    @TableField(value = "project_place" )
    private String projectPlace;

    /**
     * 关联对象
     */
    @ApiModelProperty(value = "关联对象")
    @TableField(value = "relate_object" )
    private String relateObject;

    /**
     * 任务内容
     */
    @ApiModelProperty(value = "任务内容")
    @TableField(value = "task_content" )
    private String taskContent;

}
