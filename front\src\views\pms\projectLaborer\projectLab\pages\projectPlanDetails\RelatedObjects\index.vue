<template>
  <div class="changeApply">
    <div
      class="changeApplyMainTable"
    >
      <OrionTable
        v-if="showTable"
        ref="tableRef"
        :options="tableOptions"
      >
        <template
          v-if="pageType==='page'"
          #toolbarLeft
        >
          <div
            class="changeApplyMainTable_add"
          >
            <AButton
              type="primary"
              @click="addTableNode"
            >
              <template #icon>
                <PlusOutlined />
              </template>
              添加
            </AButton>
          </div>
        </template>
        <template
          #name="{ text,record }"
        >
          <div
            class="tableIndexName flex-te"
            @click="checkDetails(record)"
          >
            {{ record.name }}
          </div>
        </template>
      </OrionTable>
    </div>
    <RightTool
      v-if="pageType==='page'"
      :btn-list="btnList"
      @clickType="clickType"
    />
    <AddObjectNode
      :relatedType="relatedType"
      @initData="initData"
      @register="register"
      @update="updateData"
    />
  </div>
</template>
<script lang="ts">
import {
  defineComponent, onMounted, reactive, toRefs, ref, computed, nextTick, provide, getCurrentInstance, inject,
} from 'vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import {
  useModal, OrionTable, RightTool, isPower,
} from 'lyra-component-vue3';
import {
  Empty, Button, message, Modal, Input,
} from 'ant-design-vue';
import { stampDate } from '/@/utils/dateUtil';
import { useQiankun } from '/@/utils/qiankun/useQiankun';
import AddObjectNode from './components/AddObjectNode.vue';
import Api from '/@/api';

export default defineComponent({
  name: 'ChangeApply',
  components: {
    OrionTable,
    RightTool,
    PlusOutlined,
    AButton: Button,
    AddObjectNode,
  },
  props: {
    pageType: {
      type: String,
      default: 'page',
    },
    relatedType: {
      type: String,
      default: 'page',
    },
  },
  setup() {
    const { mainRouter } = useQiankun();
    const formData: any = inject('formData', {});
    const powerData: any = inject('powerData', {});
    const state:any = reactive({
      formId: formData?.value?.id,
      tableSearchVal: '',
      btnList: [
        'open',
        'remove',
        'search',
      ],
      params: {
        query: {
          objIds: [formData?.value?.id],
        },
      },
      powerData: [],
      listData: [],
      showTable: false,
    });
    function initData(data) {
      state.listData = data;
      state.showTable = true;
    }

    const [register, { openModal }] = useModal();
    const tableRef = ref(null);
    const tableOptions = ref({
      deleteToolButton: 'add|enable|disable|delete',
      rowSelection: {},
      showSmallSearch: false,
      showIndexColumn: false,
      api: computed(() => getTableList(formData?.value?.id)),
      columns: [
        {
          title: '名称',
          dataIndex: 'name',
          slots: { customRender: 'name' },
          align: 'left',
          minWidth: 200,
        },
        {
          title: '编号',
          align: 'left',
          dataIndex: 'number',
          slots: { customRender: 'messageName' },
          width: 200,
        },
        {
          title: '数据类型',
          dataIndex: 'className',
          align: 'left',
          width: 100,
          customRender: ({
            text, record, index, column,
          }) => {
            let itemData = state.listData.find((item) => item.id === text);
            return itemData?.name;
          },
        },
        {
          title: '版本',
          dataIndex: 'revId',
          align: 'left',
          width: 100,
        },
        {
          title: '状态',
          dataIndex: 'status',
          align: 'left',
          width: 150,
          slots: { customRender: 'status' },
        },
        {
          title: '创建人',
          dataIndex: 'creatorName',
          width: 150,
          align: 'left',
        },
        {
          title: '修改日期',
          dataIndex: 'modifyTime',
          type: 'dateTime',
          align: 'left',
          customRender: ({
            text, record, index, column,
          }) => (record.modifyTime.length > 0 ? stampDate(record.modifyTime, 'yyyy-MM-dd HH:mm:ss') : ''),
        },
      ],
    });
    function getTableList(id) {
      return (tableParams) => new Api('/union').fetch({ ...tableParams }, `common/pages/${id}`, 'POST');
    }
    const searchForm = (val) => {
      tableRef.value.reload();
    };
    const addTableNode = () => {
      openModal(true, {
        type: 'add',
        data: { ecnDir: state.formId },
      });
    };

    const clickType = (type) => {
      let selectRow = tableRef.value.getSelectRows();
      if (selectRow.length === 0 && type !== 'search') {
        message.warning('请选择数据');
        return;
      }
      switch (type) {
        case 'remove':
          Modal.confirm({
            title: '操作提示',
            content: '是否移除所选的数据？',
            onOk() {
              let params = selectRow.map((item) => item.relationId);
              new Api('/union').fetch(params, 'common/remove', 'POST').then((res) => {
                message.success('移除数据成功');
                tableRef.value.reload();
              });
              // getchangeApplyTableDelete(params)
            },
          });
          break;
        case 'search':
          // openDrawerSearch(true);
          break;
        case 'open':
          if (selectRow.length > 1) {
            message.warning('只能选择一条数据打开');
            return;
          }
          checkDetails(selectRow[0]);
          break;
      }
    };
    const checkDetails = (record) => {
      if (!record.routerName) return;
      let routerData :any = {
        name: record.routerName,
      };
      if (record.className === 'Plan' && record.type === '0') {
        // 当为里程碑的时候，应当跳转去里程碑的接口
        routerData.name = 'MilestoneDetails';
      }
      if ([
        'Project',
        'Plan',
        'Deliverable',
        'PostProject',
      ].indexOf(record.className) >= 0) {
        routerData.query = {
          id: record.id,
          projectId: record?.projectId,
        };
      }
      if ([
        'EdmDocument',
        'Product',
        'Matter',
        'CappWorkRoute',
        'CappPbom',
        'CappWorkOrder',
        'CappWorkStep',
        'CappEquipment',
        'CappAssembly',
        'CappCenter',
        'Ecr',
        'Ecn',
      ].indexOf(record.className) >= 0) {
        routerData.params = {
          id: record.id,
        };
      }
      if ([
        'DemandManagement',
        'QuestionManagement',
        'RiskManagement',
      ].indexOf(record.className) >= 0) {
        routerData.query = {
          folderId: record?.dirId,
          itemId: record.id,
          dirName: record?.dirName,
        };
      }
      try {
        mainRouter.push(routerData);
      } catch (e) {
        message.warning('无权查看该数据');
      }
    };
    onMounted(async () => {

      // state.powerData = await getProjectPower(pageCode);
    });
    const updateData = (data) => {
      let params = [];
      data.forEach((item) => {
        params.push({
          fromClass: formData?.value?.calssName,
          toClass: item.className,
          fromId: formData?.value?.id,
          toId: item.id,
        });
      });
      new Api('/union').fetch(params, 'common/add', 'POST').then((res) => {
        message.success('添加数据成功');
        tableRef.value.reload();
      });
    };

    return {
      ...toRefs(state),
      tableOptions,
      tableRef,
      addTableNode,
      clickType,
      register,
      checkDetails,
      updateData,
      searchForm,
      initData,
    };
  },
});
</script>
<style lang="less" scoped>
.changeApply{
  height: 100%;
  display: flex;

  .tableIndexName{
    color: ~`getPrefixVar('primary-color')`;
    cursor: pointer;
  }
  .changeApplyMainTable{
    width: calc(~'100% - 60px');
    display: flex;
    flex-direction: column;
    flex: 1;
    .addBtn{
      background: ~`getPrefixVar('primary-color')`;
    }
  }
}
</style>
