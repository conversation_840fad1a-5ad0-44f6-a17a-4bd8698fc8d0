<template>
  <BasicModal
    width="50%"
    :height="400"
    v-bind="$attrs"
    title="请选择预算"
    @register="modalRegisterList"
    @visibleChange="visibleChange"
    @ok="okCostExecuteBudget"
  >
    <div style="height: 400px;overflow: hidden">
      <OrionTable
        ref="tableRef"
        :options="baseTableOption"
        @selectionChange="selectionChange"
      />
    </div>
  </BasicModal>
</template>

<script setup lang="ts">
import {
  reactive, ref, onMounted,
} from 'vue';
import {
  BasicModal, useModalInner, OrionTable, BasicDrawer, useModal,
} from 'lyra-component-vue3';
import { getCostExecuteBudget } from '/@/views/pms/api';
const emits = defineEmits(['selectedBudgetTab']);
const tableRef = ref();
const state = reactive({
  visibleStatus: false,
  projectId: '',
  tabData: {},
});
const columns = [
  {
    title: '预算编码',
    dataIndex: 'number',

  },
  {
    title: '预算名称',
    dataIndex: 'name',

  },
  {
    title: '成本中心',
    dataIndex: 'costCenterName',

  },
  {
    title: '科目',
    dataIndex: 'expenseAccountName',

  },
  {
    title: '年度',
    dataIndex: 'year',

  },
];
const baseTableOption = {
  showToolButton: false,
  rowSelection: {
    type: 'radio',
  },
  columns,
  api: (params) => getCostExecuteBudget({
    ...params,
    query: { projectId: state.projectId },
  }),
  showSmallSearch: false,
  showTableSetting: false,

};
function selectionChange({ keys, rows }) {
  if (rows.length > 0) {
    state.tabData = rows.map((s) => ({
      id: s.id,
      number: s.number,
      name: s.name,
      costCenterName: s.costCenterName,
      expenseAccountName: s.expenseAccountName,
      year: s.year,
      costCenterId: s.costCenterId,
      expenseAccountId: s.expenseAccountId,
    }));
  } else {
    state.tabData = {
      id: '',
      number: '',
      name: '',
      costCenterName: '',
      expenseAccountName: '',
      year: '',
      costCenterId: '',
      expenseAccountId: '',
    };
  }
}
function okCostExecuteBudget() {
  emits('selectedBudgetTab', state.tabData);
  closeModal();
}

const [modalRegisterList, { closeModal }] = useModalInner((openProps:{projectId: string}) => {
  // 接收，通过useModal的openModal方法打开时，传输过来的参数
  state.projectId = openProps.projectId;
  // 设置为已打开状态
  state.visibleStatus = true;
});

function visibleChange(visible: boolean) {
  // 窗口关闭时，设置状态值
  !visible && (state.visibleStatus = visible);
}
</script>
