package com.chinasie.orion.management.domain.dto;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "RequirementRatioDTO对象", description = "需求响应比率")
@Data
@ExcelIgnoreUnannotated
public class RequirementRatioDTO {

    @ApiModelProperty(value = "有核接收")
    private long nuclearReception;

    @ApiModelProperty(value = "有核响应")
    private Double nuclearResponse;

    @ApiModelProperty(value = "有核响应比")
    private Double  nuclearRatio;

    @ApiModelProperty(value = "无核接收")
    private long nonNuclearReception;

    @ApiModelProperty(value = "无核响应")
    private Double nonNuclearResponse;

    @ApiModelProperty(value = "无核响应比")
    private Double  nonNuclearRatio;


}
