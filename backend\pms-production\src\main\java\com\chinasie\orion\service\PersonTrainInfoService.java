package com.chinasie.orion.service;

import com.chinasie.orion.domain.entity.view.PersonTrainInfo;
import com.chinasie.orion.domain.vo.train.PersonTrainVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/09/19:00
 * @description:
 */
public interface PersonTrainInfoService extends OrionBaseService<PersonTrainInfo> {
    /**
     *  获取用户拥有的 培训
     * @param userCodeList
     * @return
     */
    List<PersonTrainInfo> listByUserCodeList(List<String> userCodeList);


    Page<PersonTrainVO> pagesList(Page<PersonTrainInfo> pageRequest) throws Exception;

    /**
     *  获取人员下的有效培训/和等效培训
     * @param userCode
     * @return
     */
    List<PersonTrainVO> personTrainList(String userCode) throws Exception;
}
