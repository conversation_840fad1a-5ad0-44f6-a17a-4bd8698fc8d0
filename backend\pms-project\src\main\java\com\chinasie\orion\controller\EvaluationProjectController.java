package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.EvaluationProjectDTO;
import com.chinasie.orion.domain.vo.EvaluationProjectVO;
import com.chinasie.orion.domain.vo.EvaluationTypeVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.EvaluationProjectService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * EvaluationProject 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-23 17:21:12
 */
@RestController
@RequestMapping("/evaluation-project")
@Api(tags = "项目评价")
public class EvaluationProjectController {

    @Resource
    private EvaluationProjectService evaluationProjectService;

    @ApiOperation("获取本条基本信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", dataType = "String")
    })
    @GetMapping(value = "/detail/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】获取本条基本信息", type = "项目评价", subType = "获取本条基本信息", bizNo = "{{#id}}")
    public ResponseDTO<EvaluationProjectVO> getById(@PathVariable("id") String id) throws Exception {
        EvaluationProjectVO evaluationProjectVO = evaluationProjectService.getByDataId(id);
        return new ResponseDTO<>(evaluationProjectVO);
    }

    /**
     * 新增
     *
     * @param evaluationProjectDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】新增", type = "项目评价", subType = "新增", bizNo = "")
    public ResponseDTO<EvaluationProjectVO> create(@Valid @RequestBody EvaluationProjectDTO evaluationProjectDTO) throws Exception {
        EvaluationProjectVO rsp = evaluationProjectService.create(evaluationProjectDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param evaluationProjectDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @LogRecord(success = "【{USER{#logUserId}}】编辑", type = "项目评价", subType = "编辑", bizNo = "")
    public ResponseDTO<Boolean> edit(@Valid @RequestBody EvaluationProjectDTO evaluationProjectDTO) throws Exception {
        Boolean rsp = evaluationProjectService.edit(evaluationProjectDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @LogRecord(success = "【{USER{#logUserId}}】删除（批量）", type = "项目评价", subType = "删除（批量）", bizNo = "")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = evaluationProjectService.remove(ids);
        return new ResponseDTO(rsp);
    }

    @ApiOperation("获取评价类型")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @GetMapping("/getEvaluationType/{projectId}")
    @LogRecord(success = "【{USER{#logUserId}}】获取评价类型", type = "项目评价", subType = "获取评价类型", bizNo = "")
    public ResponseDTO<List<EvaluationTypeVO>> getEvaluationType(@PathVariable(value = "projectId") String projectId) throws Exception {
        return new ResponseDTO<>(evaluationProjectService.getEvaluationType(projectId));
    }


    @ApiOperation("项目评价分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping("/getPage")
    @LogRecord(success = "【{USER{#logUserId}}】项目评价分页", type = "项目评价", subType = "项目评价分页", bizNo = "")
    public ResponseDTO<PageResult<EvaluationProjectVO>> getProjectEvaluationPage(@Valid @RequestBody Page<EvaluationProjectDTO> pageRequest) throws Exception {
        return new ResponseDTO<>(evaluationProjectService.getProjectEvaluationPage(pageRequest));
    }

}
