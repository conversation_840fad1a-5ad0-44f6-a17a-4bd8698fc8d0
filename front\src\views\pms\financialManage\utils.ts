import { openDrawer, openModal } from 'lyra-component-vue3';
import { h, ref, Ref } from 'vue';

// 数据管控
export function openDataControlForm(component: any, record?: Record<string, any>, cb?: () => void): void {
  const drawerRef: Ref = ref();
  openModal({
    title: record?.workTopicsName,
    width: 1000,
    content() {
      return h(component, {
        ref: drawerRef,
        record,
        cb,
      });
    },
    footer: null,
  });
}

// 收入计划填报-选择合同信息
export function openContractTableSelect(component: any, options: {
  record?: any,
  type?: string,
  cb?: Function,
}) {
  const drawerRef: Ref = ref();
  openModal({
    title: '收入计划填报-选择合同信息',
    width: 1200,
    height: 700,
    content() {
      return h(component, {
        ref: drawerRef,
        record: options?.record,
        type: options.type,
      });
    },
    async onOk() {
      const data = await drawerRef.value.onSubmit();
      if (data) {
        options?.cb?.(data);
      }
    },
  });
}

// 数据锁定
export function openLockModel(component: any, options: {
    isControl?: boolean,
    inComePlanId?:string,
  }, updateTable: () => void) {
  const drawerRef: Ref = ref();
  openModal({
    title: '',
    width: 730,
    height: 530,
    content() {
      return h(component, {
        ref: drawerRef,
        cb: updateTable,
        isControl: options.isControl,
        inComePlanId: options.inComePlanId,
      });
    },
  });
}

// 多项目/税率维护
export function openTaxModel(component: any, options: {
  record?: Record<string, any>,
  cb?: Function,
}) {
  const drawerRef: Ref = ref();
  openModal({
    title: '多项目/税率维护',
    width: 1300,
    height: 700,
    content() {
      return h(component, {
        ref: drawerRef,
        record: options.record,
      });
    },
    async onOk(): Promise<void> {
      const data = await drawerRef.value.onSubmit();
      if (data) {
        options?.cb?.(data);
      }
    },
  });
}

// 收入计划填报-选择项目信息
export function openProjectTableSelect(component: any, options: {
  cb?: Function,
}) {
  const drawerRef: Ref = ref();
  openModal({
    title: '申请开票-选择项目信息',
    width: 1200,
    height: 700,
    content() {
      return h(component, {
        ref: drawerRef,
      });
    },
    async onOk() {
      const data = await drawerRef.value.onSubmit();
      options?.cb?.(data);
    },
  });
}

// 人员角色维护新增
export function openFormDrawer(component: any, record?: Record<string, any>, cb?: () => void): void {
  const drawerRef: Ref = ref();
  openDrawer({
    title: record?.id ? '编辑人员角色维护' : '新增人员角色维护',
    width: 1000,
    content() {
      return h(component, {
        ref: drawerRef,
        record,
      });
    },
    async onOk(): Promise<void> {
      await drawerRef.value.onSubmit();
      cb?.();
    },
  });
}

// 项目全口径明细行数据
export function openCostDrawer(component: any, record?: Record<string, any>, costType?: string, date?: string, powerData?: any, cb?: () => void): void {
  const drawerRef: Ref = ref();
  const dynamicWidth = `calc(100% - ${210}px)`;
  openDrawer({
    title: '项目全口径明细行数据',
    width: dynamicWidth,
    content() {
      return h(component, {
        ref: drawerRef,
        record,
        costType,
        powerData,
        date,
      });
    },
    footer: null,
    async onOk(): Promise<void> {
      await drawerRef.value.onSubmit();
      cb?.();
    },
  });
}

// 收入计划填报-选择甲方单位名称
export function openPartyTableSelect(component: any, options: {
  cb?: Function,
}) {
  const drawerRef: Ref = ref();
  openModal({
    title: '收入计划填报-选择甲方单位名称',
    width: 1200,
    height: 700,
    content() {
      return h(component, {
        ref: drawerRef,
      });
    },
    async onOk() {
      const data = await drawerRef.value.onSubmit();
      options?.cb?.(data);
    },
  });
}
