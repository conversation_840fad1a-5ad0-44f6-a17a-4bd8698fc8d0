<script setup lang="ts">
import {
  computed, inject, ref, Ref, watch,
} from 'vue';
import { useChart } from './useChart';
import SpinView from '/@/views/pms/components/SpinView.vue';

const loading: Ref<boolean> = inject('loading');
const schemeInfo: Ref<Record<string, any>> = inject('schemeInfo');
const dataOptions = computed(() => [
  {
    color: '#F9D25D',
    name: '逾期未完成',
    value: schemeInfo.value.overdueCount || 0,
    circumstance: 100013,
    status: 130,
  },
  {
    color: '#E7A67C',
    name: '临期',
    value: schemeInfo.value.nearExpiredCount || 0,
    circumstance: 100012,
    status: 130,
  },
  {
    color: '#AEDD8B',
    name: '逾期完成',
    value: schemeInfo.value.overdueCompleteCount || 0,
    circumstance: 100013,
    status: 111,
  },
]);
const chartOption = computed(() => ({
  tooltip: {
    trigger: 'item',
  },
  legend: {
    show: false,
  },
  color: dataOptions.value.map((item) => item.color),
  series: [
    {
      name: '异常总数',
      type: 'pie',
      radius: [0, '90%'],
      center: ['50%', '50%'],
      label: {
        show: false,
      },
      data: dataOptions.value,
    },
  ],
}));

const chartRef: Ref = ref();
const chartInstance = useChart(chartRef);
const changeTab: (params: { id: string, circumstance: number, status: number }) => void = inject('changeTab');

watch(() => chartOption.value, (value) => {
  chartInstance.value.setOption(value);
}, {
  deep: true,
});
</script>

<template>
  <div class="flex flex-ac mr10">
    <spin-view
      v-if="loading"
      class="project-scheme-chart"
    />
    <div
      v-show="!loading"
      ref="chartRef"
      class="project-scheme-chart"
    />
    <div
      style="width: 140px"
      class=" ml10"
    >
      <div class="custom-legend-title">
        <div class="label">
          异常总数：
        </div>
        <div class="value">
          {{ schemeInfo.planAbnormalTotal || 0 }}个
        </div>
      </div>
      <div class="mt15">
        <div
          v-for="(item,index) in dataOptions"
          :key="index"
          class="custom-legend-item"
          @click="changeTab({id:'project_plan',circumstance:item.circumstance,status:item.status})"
        >
          <span
            :style="{backgroundColor:item.color}"
          />
          <span>{{ item.name }}（{{ item.value }}）</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">

</style>
