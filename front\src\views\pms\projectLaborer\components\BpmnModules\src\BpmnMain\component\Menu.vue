<template>
  <div class="menu-wrap flex flex-ver">
    <div class="title">
      流程实例列表
    </div>
    <div
      v-if="menuList.length"
      class="list flex-f1"
    >
      <div
        v-for="(item, index) in menuList"
        :key="index"
        :class="{ action: menuIsAction(index) }"
        @click="onHandle(item, index)"
      >
        {{ item.procInstName }}
      </div>
    </div>
    <div
      v-else
      class="flex-f1 flex flex-pac"
    >
      <Empty description="请添加流程实例" />
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent, onMounted, reactive, toRefs, computed,
} from 'vue';
import { Empty } from 'ant-design-vue';
import Api from '/@/api';

export default defineComponent({
  name: 'Menu',
  components: {
    Empty,
  },
  props: {
    deliveryId: {
      type: String,
      default: '',
    },
    userId: {
      type: String,
      default: '',
    },
  },
  emits: ['change', 'init'],
  setup(props, { emit }) {
    const state = reactive({
      menuAction: 0,
      menuList: [],
    });
    onMounted(() => {
      init();
    });

    // 初始化
    async function init() {
      await load();
      emitInit();
    }

    // 设置默认菜单第一项选中
    function setDefaultActionMenu() {
      if (state.menuList.length) {
        const index = state.menuAction || 0;
        onHandle(state.menuList[index], index);
      } else {
        onHandle(null);
      }
    }

    // 加载菜单
    function load() {
      return new Api('/workflow/act-prearranged/all/page')
        .fetch(
          {
            pageNum: 1,
            pageSize: 10000,
            query: { deliveries: [{ deliveryId: props.deliveryId }] },
            userId: props.userId,
            queryCondition: [],
          },
          '',
          'POST',
        )
        .then((data) => {
          data.content && (state.menuList = data.content);
          setDefaultActionMenu();
        });
    }

    // 选中菜单项
    function onHandle(item, index = 0) {
      state.menuAction = index;
      emit(
        'change',
        item
          ? {
            item,
            index,
          }
          : null,
      );
    }

    // 计算选中项
    const menuIsAction = computed(() => (index) => index === state.menuAction);

    const menuMethod = {
      load,
    };

    function emitInit() {
      emit('init', menuMethod);
    }

    return {
      ...toRefs(state),
      onHandle,
      menuIsAction,
    };
  },
});
</script>

<style lang="less" scoped>
  .menu-wrap {
    padding: 15px;
    overflow-y: auto;
    max-height: 100%;
    min-height: 100%;

    > .title {
      height: 40px;
      line-height: 40px;
      padding: 0 15px 0 30px;
      box-sizing: border-box;
      background: #f0f2f5;
      position: relative;

      &::after {
        content: '';
        width: 0;
        height: 0;
        border-width: 6px;
        border-style: solid;
        border-color: #969eb4 transparent transparent transparent;
        position: absolute;
        left: 15px;
        top: 18px;
      }
    }

    > .list {
      > div {
        cursor: pointer;
        height: 40px;
        line-height: 40px;
        padding: 0 10px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;

        &:hover {
          color: #0960bd;
        }

        &.action {
          background: #e1e7f97f;
          color: #0960bd;
        }
      }
    }
  }
</style>
