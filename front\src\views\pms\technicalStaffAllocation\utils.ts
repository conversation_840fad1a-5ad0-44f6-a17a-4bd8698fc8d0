import { openDrawer, openModal } from 'lyra-component-vue3';
import { h, ref, Ref } from 'vue';

// 表格组件新增、编辑抽屉
export function openFormDrawer(component: any, record?: Record<string, any>, cb?: () => void): void {
  const drawerRef: Ref = ref();
  openDrawer({
    title: record?.id ? '编辑' : '新增',
    width: 1000,
    content() {
      return h(component, {
        ref: drawerRef,
        record,
      });
    },
    async onOk(): Promise<void> {
      await drawerRef.value.onSubmit();
      cb?.();
    },
  });
}

// 离岗
export function openLeaveForm(component: any, ids?: any[], cb?: () => void): void {
  const drawerRef: Ref = ref();
  openModal({
    title: '离岗',
    width: 380,
    height: 220,
    isFullScreen: false,
    content() {
      return h(component, {
        ref: drawerRef,
        ids,
      });
    },
    async onOk(): Promise<void> {
      await drawerRef.value.onSubmit();
      cb?.();
    },
  });
}

// 证书名称
export function openCertificateTableSelect(component: any, options: {
    selectedList?: any[],
    cb?: Function,
    certificateType?: string
  }) {
  const drawerRef: Ref = ref();
  openModal({
    title: '证书标准选择',
    width: 1200,
    content() {
      return h(component, {
        ref: drawerRef,
        selectedList: options?.selectedList,
        certificateType: options?.certificateType || '',
      });
    },
    async onOk() {
      const data = await drawerRef.value.confirm();
      options?.cb?.(data);
    },
  });
}
