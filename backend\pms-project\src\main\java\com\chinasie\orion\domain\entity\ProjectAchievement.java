package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * ProjectAchievement Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-07 14:51:38
 */
@TableName(value = "pmsx_project_achievement")
@ApiModel(value = "ProjectAchievement对象", description = "项目成果表")
@Data
public class ProjectAchievement extends ObjectEntity implements Serializable {

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @TableField(value = "sort")
    private Long sort;

    /**
     * 里程碑Id
     */
    @ApiModelProperty(value = "里程碑Id")
    @TableField(value = "milestone_id")
    private String milestoneId;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField(value = "name")
    private String name;

    /**
     * 密级
     */
    @ApiModelProperty(value = "密级")
    @TableField(value = "secrecy_level")
    private String secrecyLevel;

    /**
     * 层级
     */
    @ApiModelProperty(value = "层级")
    @TableField(value = "hierarchy_level")
    private String hierarchyLevel;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    @TableField(value = "res_person")
    private String resPerson;

    /**
     * 责任部门
     */
    @ApiModelProperty(value = "责任部门")
    @TableField(value = "res_dept")
    private String resDept;

    /**
     * 责任科室
     */
    @ApiModelProperty(value = "责任科室")
    @TableField(value = "res_office")
    private String resOffice;

    /**
     * 计划提交时间
     */
    @ApiModelProperty(value = "计划提交时间")
    @TableField(value = "plan_commit_time")
    private Date planCommitTime;


    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @TableField(value = "project_id")
    private String projectId;


    /**
     * 成果类型ID
     */
    @ApiModelProperty(value = "成果类型ID")
    @TableField(value = "floder_id")
    private String floderId;

}
