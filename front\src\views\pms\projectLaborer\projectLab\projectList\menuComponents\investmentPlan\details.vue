<template>
  <Layout3
    :projectData="formData"
    :menuData="menuData"
    :type="2"
    :defaultActionId="tabsId"
    :onMenuChange="menuChange"
  >
    <template #code>
      <div>{{ `编码：${formData?.['number']??""}` }}</div>
    </template>
    <div
      class="details-content"
    >
      <div
        v-if="isPower('TZJH_container_02', powerData)"
        class="details-content-item"
      >
        <div class="content-title">
          <div
            class="information-title"
          >
            基础信息
          </div>
        </div>
        <Information />
      </div>

      <div
        v-if="isPower('TZJH_container_01', powerData)"
        class="details-content-item"
      >
        <div class="content-title">
          <div
            class="information-title information-title-flex"
          >
            <span>年度投资计划</span>
            <span>单位：万元</span>
          </div>
        </div>
        <InvestmentPlan v-if="formData?.id" />
      </div>

      <div
        v-if="isPower('TZJH_container_03', powerData)"
        class="details-content-item"
      >
        <div class="content-title">
          <div
            class="information-title information-title-flex"
          >
            <span>项目概算</span>
            <span>单位：万元</span>
          </div>
        </div>
        <Estimate
          v-if="formData?.id"
          ref="estimateRef"
        />
      </div>
    </div>
  </Layout3>
</template>

<script lang="ts">
import {
  computed, defineComponent, onMounted, provide, reactive, toRefs, getCurrentInstance, ref,
} from 'vue';
import { Layout3, isPower } from 'lyra-component-vue3';
import { useRoute } from 'vue-router';
import Information from './modal/Information.vue';
import InvestmentPlan from './modal/InvestmentPlan.vue';
import Estimate from './modal/Estimate.vue';
import { getPlanDetails } from './index';
import { renderNotAuthPage } from '/@/views/pms/utils';

export default defineComponent({
  name: 'ProjectLifeDetails',
  components: {
    Layout3,
    Information,
    InvestmentPlan,
    Estimate,
  },
  setup() {
    const estimateRef = ref();
    const route = useRoute();
    const state = reactive({
      formData: {},
      menuData: [],
      tabsId: '',
    });

    const powerData = ref([]);

    function menuChange(val) {
      state.tabsId = val.id;
    }
    onMounted(() => {
      getFormData();
    });

    const currentInstance = getCurrentInstance();

    function getFormData(type = 'update') {
      getPlanDetails(route.params.id, 'PMS1001').then(async (res) => {
        powerData.value = res?.detailAuthList ?? [];
        renderNotAuthPage({
          vm: currentInstance,
          powerData: powerData.value,
        });
        let formatterFields = [
          'overallBudget',
          'overallReality',
          'projectAmount',
          'contractAmount',
        ];
        formatterFields.forEach((item) => {
          if (res[item] && Number(res[item])) {
            res[item] = Number((Number(res[item]) / 10000).toFixed(6));
          }
        });
        state.formData = res;
        if (type === 'updateE') {
          estimateRef.value.updateInit();
        }
        if (!state.tabsId) {
          state.tabsId = 'annualInvestment';
        }
      });
    }
    provide('formData', computed(() => state.formData));
    provide('getFormData', getFormData);
    provide('powerData', powerData);
    return {
      ...toRefs(state),
      menuChange,
      powerData,
      isPower,
      estimateRef,
    };
  },
});

</script>

<style lang="less" scoped>
.details-content{

  :deep(.information-title) {
      font-size: 18px;
      font-weight: 500;
      color: rgba(0,0,0,0.85);
      line-height: 28px;
      position: relative;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
      padding-bottom: 5px;
      &:before {
          content: '';
          height: 18px;
          width: 4px;
          background: ~`getPrefixVar('primary-color') `;
          display: inline-block;
          position: absolute;
          top: 5px;
          left: 0;
      }
  }
  .information-title-flex{
    display: flex;
    width: 100%;
    justify-content: space-between;
    span+span{
      font-size: 14px;
      color: #959191;
    }
  }

  .content-title{
    padding: 20px 20px 0 20px;
  }
}
</style>
