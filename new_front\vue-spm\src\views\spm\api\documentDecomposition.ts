import Api from '/@/api';

/**
 * 分页
 * @param mainTableId 参数
 * @param params 参数
 */
export const page = (mainTableId, params) => new Api(`/spm/documentDecomposition/page/${mainTableId}`).fetch(params, '', 'POST');
/**
 * 列表树
 * @param mainTableId 参数
 */
export const listTree = (mainTableId) => new Api(`/spm/documentDecomposition/list/tree/${mainTableId}`).fetch('', '', 'POST');
/**
 * 列表
 * @param mainTableId 参数
 */
export const list = (mainTableId) => new Api(`/spm/documentDecomposition/list/${mainTableId}`).fetch('', '', 'POST');

/**
 * 新增
 * @param params 参数
 */
export const add = (params) => new Api('/spm/documentDecomposition/add').fetch(params, '', 'POST');
/**
 * 编辑
 * @param params 参数
 */
export const edit = (params) => new Api('/spm/documentDecomposition/edit').fetch(params, '', 'PUT');
/**
 * 详情
 * @param id 参数
 */
export const documentDecompositionById = (id) => new Api(`/spm/documentDecomposition/${id}`).fetch('', '', 'GET');
/**
 * 删除
 * @param params 参数
 */
export const remove = (params) => new Api('/spm/documentDecomposition/remove').fetch(params, '', 'DELETE');
/**
 * 删除
 * @param id 参数
 */
export const removeById = (id) => new Api(`/spm/documentDecomposition/${id}`).fetch('', '', 'DELETE');
/**
 * 生成任务
 * @param mainTableId 参数
 */
export const generateTask = (mainTableId) => new Api(`/spm/documentDecomposition/generateTask/${mainTableId}`).fetch('', '', 'POST');
