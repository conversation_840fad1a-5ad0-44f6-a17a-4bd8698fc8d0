<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements.  See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership.  The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
                      http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.apache</groupId>
    <artifactId>apache</artifactId>
    <version>17</version>
    <relativePath/>
    <!-- no parent resolution -->
  </parent>
  <groupId>org.apache.yetus</groupId>
  <artifactId>yetus-project</artifactId>
  <version>0.5.0</version>
  <description>Project-wide definitions for Apache Yetus</description>
  <name>Apache Yetus Project</name>
  <packaging>pom</packaging>

  <url>https://yetus.apache.org</url>
  <inceptionYear>2015</inceptionYear>
  <mailingLists>
    <mailingList>
      <name>Apache Yetus Dev List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>https://mail-archives.apache.org/mod_mbox/yetus-dev/</archive>
    </mailingList>
  </mailingLists>

  <properties>
    <maven.min.version>3.2.0</maven.min.version>
    <java.min.version>1.7</java.min.version>
    <maven.compiler.source>1.7</maven.compiler.source>
    <maven.compiler.target>1.7</maven.compiler.target>
    <extra.enforcer.version>1.0-beta-3</extra.enforcer.version>
  </properties>

  <scm>
    <connection>scm:git:git://git.apache.org/yetus.git</connection>
    <developerConnection>scm:git:https://git-wip-us.apache.org/repos/asf/yetus.git</developerConnection>
    <url>https://git-wip-us.apache.org/repos/asf?p=yetus.git</url>
  </scm>
  <issueManagement>
    <system>JIRA</system>
    <url>http://issues.apache.org/jira/browse/YETUS</url>
  </issueManagement>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>extra-enforcer-rules</artifactId>
        <version>${extra.enforcer.version}</version>
      </dependency>
      <dependency>
        <groupId>jdk.tools</groupId>
        <artifactId>jdk.tools</artifactId>
        <version>1.7</version>
        <scope>system</scope>
        <systemPath>${java.home}/../lib/tools.jar</systemPath>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <dependencies>
          <dependency>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>extra-enforcer-rules</artifactId>
            <version>${extra.enforcer.version}</version>
          </dependency>
        </dependencies>
        <!-- version set by parent -->
        <executions>
          <execution>
            <id>enforce</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <!-- The earliest maven version we verify builds for via ASF Jenkins -->
                <!-- Might be obviated by move to gradle -->
                <requireMavenVersion>
                  <version>[${maven.min.version},)</version>
                  <message>Maven is out of date.
  Yetus requires at least version ${maven.min.version} of Maven to properly build from source.
  You appear to be using an older version. You can use either "mvn -version" or
  "mvn enforcer:display-info" to verify what version is active.
  See the contributor guide on building for more information: ${project.url}/contribute/
                  </message>
                </requireMavenVersion>
                <!-- The earliest JVM version we verify builds for via ASF Jenkins -->
                <requireJavaVersion>
                  <version>[${java.min.version},)</version>
                  <message>Java is out of date.
  Yetus requires at least version ${java.min.version} of the JDK to properly build from source.
  You appear to be using an older version. You can use either "mvn -version" or
  "mvn enforcer:display-info" to verify what version is active.
  See the contributor guide on building for more information: ${project.url}/contribute/
                  </message>
                </requireJavaVersion>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>jdk1.7</id>
      <activation>
        <jdk>1.7</jdk>
      </activation>
      <dependencyManagement>
        <dependencies>
          <dependency>
            <groupId>jdk.tools</groupId>
            <artifactId>jdk.tools</artifactId>
            <version>1.7</version>
            <scope>system</scope>
            <systemPath>${java.home}/../lib/tools.jar</systemPath>
          </dependency>
        </dependencies>
      </dependencyManagement>
    </profile>
    <profile>
      <id>jdk1.8</id>
      <activation>
        <jdk>1.8</jdk>
      </activation>
      <dependencyManagement>
        <dependencies>
          <dependency>
            <groupId>jdk.tools</groupId>
            <artifactId>jdk.tools</artifactId>
            <version>1.8</version>
            <scope>system</scope>
            <systemPath>${java.home}/../lib/tools.jar</systemPath>
          </dependency>
        </dependencies>
      </dependencyManagement>
    </profile>
    <profile>
      <id>jdk1.9</id>
      <activation>
        <jdk>1.9</jdk>
      </activation>
      <dependencyManagement>
        <dependencies>
          <dependency>
            <groupId>jdk.tools</groupId>
            <artifactId>jdk.tools</artifactId>
            <version>1.9</version>
            <scope>system</scope>
            <systemPath>${java.home}/../lib/tools.jar</systemPath>
          </dependency>
        </dependencies>
      </dependencyManagement>
    </profile>
  </profiles>

</project>
