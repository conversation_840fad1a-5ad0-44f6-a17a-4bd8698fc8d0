package com.chinasie.orion.management.domain.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.DeptDataBind;
import com.chinasie.orion.sdk.core.data.bind.UserDataBind;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * RequirementMangement Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-28 15:55:19
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "pms_requirement_mangement")
@ApiModel(value = "RequirementMangementEntity对象", description = "主表")
@Data
public class RequirementMangement extends ObjectEntity implements Serializable {

    /**
     * 需求编号
     */
    @ApiModelProperty(value = "需求编号")
    @TableField(value = "requirement_number")
    private String requirementNumber;

    /**
     * 需求标题
     */
    @ApiModelProperty(value = "需求标题")
    @TableField(value = "requirement_name")
    private String requirementName;

    /**
     * 需求来源
     */
    @ApiModelProperty(value = "需求来源")
    @TableField(value = "res_source")
    private String resSource;

    /**
     * 开标时间
     */
    @ApiModelProperty(value = "开标时间")
    @TableField(value = "bid_opening_tm")
    private Date bidOpeningTm;

    /**
     * 报名开始日期
     */
    @ApiModelProperty(value = "报名开始日期")
    @TableField(value = "sign_start_time")
    private Date signStartTime;

    /**
     * 报名结束日期
     */
    @ApiModelProperty(value = "报名结束日期")
    @TableField(value = "sign_end_time")
    private Date signEndTime;

    /**
     * 报价截止时间
     */
    @ApiModelProperty(value = "报价截止时间")
    @TableField(value = "sign_deadln_time")
    private Date signDeadlnTime;

    /**
     * 客户
     */
    @ApiModelProperty(value = "客户")
    @TableField(value = "cust_person")
    private String custPerson;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    @TableField(value = "cust_person_name")
    private String custPersonName;

    /**
     * 客户范围
     */
    @ApiModelProperty(value = "客户范围")
    @TableField(value = "cust_scope")
    private String custScope;

    /**
     * 客户主要联系人
     */
    @ApiModelProperty(value = "客户主要联系人")
    @TableField(value = "cust_con_person")
    private String custConPerson;

    @ApiModelProperty(value = "客户主要联系人名称")
    @TableField(value = "cust_con_person_name")
    private String custConPersonName;

    /**
     * 客户主要联系人电话
     */
    @ApiModelProperty(value = "客户主要联系人电话")
    @TableField(value = "cust_contact_ph")
    private String custContactPh;

    /**
     * 客户商务接口人
     */
    @ApiModelProperty(value = "客户商务接口人")
    @TableField(value = "cust_bs_person")
    //@FieldBind(dataBind = UserDataBind.class, target = "custBsPersonName")
    private String custBsPerson;

    /**
     * 客户商务接口人名称
     */
    @ApiModelProperty(value = "客户商务接口人")
    @TableField(exist = false)
    private String custBsPersonName;

    /**
     * 客户技术接口人
     */
    @ApiModelProperty(value = "客户技术接口人")
    @TableField(value = "cust_tec_person")
    //@FieldBind(dataBind = UserDataBind.class, target = "custTecPersonName")
    private String custTecPerson;

    /**
     * 客户技术接口人名称
     */
    @ApiModelProperty(value = "客户技术接口人名称")
    @TableField(exist = false)
    private String custTecPersonName;

    /**
     * 商务接口人
     */
    @ApiModelProperty(value = "商务接口人")
    //@FieldBind(dataBind = UserDataBind.class, target = "businessPersonName")
    @TableField(value = "business_person")
    private String businessPerson;

    /**
     * 商务接口人名称
     */
    @ApiModelProperty(value = "商务接口人名称")
    @TableField(value = "business_person_name")
    private String businessPersonName;
    /**
     * 技术接口人(技术负责人)
     */
    @ApiModelProperty(value = "技术接口人(技术负责人)")
    //@FieldBind(dataBind = UserDataBind.class, target = "techResName")
    @TableField(value = "tech_res")
    private String techRes;

    /**
     * 技术接口人名称(技术负责人名称)
     */
    @ApiModelProperty(value = "技术接口人名称(技术负责人名称)")
    @TableField(value = "tech_res_name")
    private String techResName;
    /**
     * 需求归属中心
     */
    @ApiModelProperty(value = "需求归属中心")
    @TableField(value = "req_ownership")
    private String reqOwnership;

    /**
     * 配合部门接口人
     */
    @ApiModelProperty(value = "配合部门接口人")
    @TableField(value = "cooperate_person")
    private String cooperatePerson;

    /**
     * 配合部门
     */
    @ApiModelProperty(value = "配合部门")
    //@FieldBind(dataBind = DeptDataBind.class, target = "cooperateDptName")
    @TableField(value = "cooperate_dpt")
    private String cooperateDpt;

    /**
     * 配合部门
     */
    @ApiModelProperty(value = "配合部门名称")
    @TableField(exist = false)
    private String cooperateDptName;

    /**
     * 需求状态
     */
    @ApiModelProperty(value = "需求状态")
    @TableField(value = "project_status")
    private String projectStatus;

    /**
     * 响应状态
     */
    @ApiModelProperty(value = "响应状态")
    @TableField(value = "response_status")
    private String responseStatus;

    /**
     * 需求确认备注
     */
    @ApiModelProperty(value = "需求确认备注")
    @TableField(value = "confirm_remark")
    private String confirmRemark;

    /**
     * 标段名称
     */
    @ApiModelProperty(value = "标段名称")
    @TableField(value = "section_name")
    private String sectionName;

    /**
     * 客户部门
     */
    @ApiModelProperty(value = "客户部门")
    @TableField(value = "cust_dpt_name")
    private String custDptName;

    /**
     * ECP状态
     */
    @ApiModelProperty(value = "ECP状态")
    @TableField(value = "ecp_status")
    private String ecpStatus;

    /**
     * ECP上次更新时间
     */
    @ApiModelProperty(value = "ECP上次更新时间")
    @TableField(value = "ecp_update_time")
    private Date ecpUpdateTime;

    /**
     * 是否发布公示
     */
    @ApiModelProperty(value = "是否发布公示")
    @TableField(value = "is_published")
    private String isPublished;

    @ApiModelProperty(value = "来源ecp组织")
    @TableField(value = "ecp_group")
    private String ecpGroup;

    @ApiModelProperty(value = "来源ecp组织名称")
    @TableField(value = "ecp_group_name")
    private String ecpGroupName;

    @ApiModelProperty(value = "业务类型")
    @TableField(value = "business_type")
    private String businessType;

    @ApiModelProperty(value = "已报价 1.是 0.否")
    @TableField(value = "had_quotation")
    private Integer hadQuotation;

    @ApiModelProperty(value = "分发时间")
    @TableField(value = "distribute_time")
    private Date distributeTime;

    /**
     * 报名申请人
     */
    @ApiModelProperty(value = "报名申请人")
    @TableField(value = "applicant_user",updateStrategy = FieldStrategy.IGNORED)
    //@FieldBind(dataBind = UserDataBind.class, target = "applicantUserName")
    private String applicantUser;

    /**
     * 报名申请人名称
     */
    @ApiModelProperty(value = "报名申请人名称")
    @TableField(exist = false)
    private String applicantUserName;

    /**
     * 报名部门
     */
    @ApiModelProperty(value = "报名部门")
    @TableField(value = "applicant_dept",updateStrategy = FieldStrategy.IGNORED)
    //@FieldBind(dataBind = DeptDataBind.class, target = "applicantDeptName")
    private String applicantDept;

    /**
     * 报名部门名称
     */
    @ApiModelProperty(value = "报名部门名称")
    @TableField(exist = false)
    private String applicantDeptName;

    /**
     * 报名时间
     */
    @ApiModelProperty(value = "报名时间")
    @TableField(value = "applicant_time",updateStrategy = FieldStrategy.IGNORED)
    private Date applicantTime;

    /**
     * 优先级1低2中3高
     */
    @ApiModelProperty(value = "优先级1低2中3高")
    @TableField(value = "priority")
    private String priority;

    /**
     * ECP系统删除标识
     */
    @ApiModelProperty(value = "ECP系统删除标识")
    @TableField(value = "delete_flag")
    private String deleteFlag;

    /**
     * 关闭原因
     */
    @ApiModelProperty(value = "关闭原因")
    @TableField(value = "close_reason")
    private String closeReason;

    /**
     * 所级负责人
     */
    @ApiModelProperty(value = "所级负责人")
    @TableField(value = "office_leader")
    private String officeLeader;

    /**
     * 关联交易表单Id
     */
    @ApiModelProperty(value = "关联交易表单Id")
    @TableField(value = "trans_appr_id")
    private String transApprId;

    /**
     * 是否关联交易表单
     */
    @ApiModelProperty(value = "是否关联交易表单")
    @TableField(value = "rel_trans_appr")
    private Boolean relTransAppr;

    /**
     * 不关联原因
     */
    @ApiModelProperty(value = "不关联原因")
    @TableField(value = "unrelated_reason")
    private String unrelatedReason;

    /**
     * 关联交易表单编号
     */
    @ApiModelProperty(value = "关联交易表单编号")
    @TableField(value = "trans_appr_number")
    private String transApprNumber;


    @ApiModelProperty("需求确认时间")
    @TableField(exist = false)
    private Date feedbackTime;



}
