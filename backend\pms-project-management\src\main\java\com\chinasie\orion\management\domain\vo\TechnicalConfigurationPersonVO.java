package com.chinasie.orion.management.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * TechnicalConfigurationPerson VO对象
 *
 * <AUTHOR>
 * @since 2024-06-22 14:03:35
 */
@ApiModel(value = "TechnicalConfigurationPersonVO对象", description = "技术配置人员")
@Data
public class TechnicalConfigurationPersonVO extends ObjectVO implements Serializable {

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String personName;


    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String personSex;


    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    private String idCard;


    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期")
    private String birth;


    /**
     * 民族
     */
    @ApiModelProperty(value = "民族")
    private String nation;


    /**
     * 婚姻状况
     */
    @ApiModelProperty(value = "婚姻状况")
    private String maritalStatus;


    /**
     * 联系方式
     */
    @ApiModelProperty(value = "联系方式")
    private String personTel;


    /**
     * 最高学历
     */
    @ApiModelProperty(value = "最高学历")
    private String educationLevel;


    /**
     * 所学专业
     */
    @ApiModelProperty(value = "所学专业")
    private String major;


    /**
     * 职称
     */
    @ApiModelProperty(value = "职称")
    private String personTitle;


    /**
     * 技术专业证书
     */
    @ApiModelProperty(value = "技术专业证书")
    private String majorCertificate;


    /**
     * 所在公司
     */
    @ApiModelProperty(value = "所在公司")
    private String personCompany;


    /**
     * 所在部门/中心
     */
    @ApiModelProperty(value = "所在部门/中心")
    private String personDepartment;


    /**
     * 所在研究所/专业室
     */
    @ApiModelProperty(value = "所在研究所/专业室")
    private String personInstitute;


    /**
     * 分管项目经理
     */
    @ApiModelProperty(value = "分管项目经理")
    private String projectManager;

    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    private String jobId;


    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;


    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;


    /**
     * 是否项目制人员
     */
    @ApiModelProperty(value = "是否项目制人员")
    private String isProjectBased;


    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractCode;


    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;


    /**
     * 合同级别
     */
    @ApiModelProperty(value = "合同级别")
    private String contractLevel;


    /**
     * 工作内容
     */
    @ApiModelProperty(value = "工作内容")
    private String jobContent;


    /**
     * 常驻服务地点
     */
    @ApiModelProperty(value = "常驻服务地点")
    private String serviceLocation;


    /**
     * 是否从事放射性工作
     */
    @ApiModelProperty(value = "是否从事放射性工作")
    private String isRadioactivityWork;


    /**
     * 是否完成体检
     */
    @ApiModelProperty(value = "是否完成体检")
    private String isCompletedExam;


    /**
     * 办卡或授权
     */
    @ApiModelProperty(value = "办卡或授权")
    private String cardOrEmpower;


    /**
     * 是否有亲属在集团内
     */
    @ApiModelProperty(value = "是否有亲属在集团内")
    private String haveKinGroup;


    /**
     * 亲属姓名
     */
    @ApiModelProperty(value = "亲属姓名")
    private String kinName;


    /**
     * 亲属职务
     */
    @ApiModelProperty(value = "亲属职务")
    private String kinPost;


    /**
     * 亲属公司
     */
    @ApiModelProperty(value = "亲属公司")
    private String kinCompany;


    /**
     * 是否技术配置
     */
    @ApiModelProperty(value = "是否技术配置")
    private String isTechnicaled;


    /**
     * 人员状态（在场/离场）
     */
    @ApiModelProperty(value = "人员状态（在场/离场）")
    private String personStatus;


    /**
     * 预计离场时间
     */
    @ApiModelProperty(value = "预计离场时间")
    private Date leaveTime;


    /**
     * 是否违反相关安全规范
     */
    @ApiModelProperty(value = "是否违反相关安全规范")
    private String isSafetied;


    /**
     * 入场时间
     */
    @ApiModelProperty(value = "入场时间")
    private Date entryTime;


    /**
     * 离场时间
     */
    @ApiModelProperty(value = "离场时间")
    private Date leavingTime;


    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    private Date operationTime;


    /**
     * 操作人id
     */
    @ApiModelProperty(value = "操作人id")
    private String operationId;


    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人姓名")
    private String operationName;


    /**
     * 锁定状态
     */
    @ApiModelProperty(value = "锁定状态")
    private String lockedState;


}
