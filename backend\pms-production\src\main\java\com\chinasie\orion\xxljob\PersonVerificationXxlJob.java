package com.chinasie.orion.xxljob;

import com.chinasie.orion.constant.MessageNodeDict;
import com.chinasie.orion.domain.entity.BasicUserCertificate;
import com.chinasie.orion.domain.vo.BasicUserCertificateVO;
import com.chinasie.orion.domain.vo.BasicUserVO;
import com.chinasie.orion.msc.api.MscBuildHandlerManager;
import com.chinasie.orion.repository.BasicUserCertificateMapper;
import com.chinasie.orion.service.BasicUserCertificateService;
import com.chinasie.orion.service.BasicUserService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class PersonVerificationXxlJob {

    @Autowired
    BasicUserCertificateMapper basicUserCertificateMapper;

    @Autowired
    BasicUserCertificateService basicUserCertificateService;

    @Autowired
    BasicUserService basicUserService;

    @Autowired
    MscBuildHandlerManager mscBuildHandlerManager;

    @XxlJob("personVerificationValidJob")
    public void personVerificationValidJob() throws Exception {
       List<BasicUserCertificate> res = basicUserCertificateMapper.getNotifyList();
       if (CollectionUtils.isEmpty(res)){
           return;
       }
        List<BasicUserCertificateVO> vos = BeanCopyUtils.convertListTo(res, BasicUserCertificateVO::new);
        basicUserCertificateService.setEveryName(vos);
        BasicUserVO basicUserVO = basicUserService.detailUserCode(res.get(0).getUserCode());
        vos.forEach(item->{
           //发送消息
            mscBuildHandlerManager.send(item, MessageNodeDict.NODE_PERSON_VERIFICATION, Objects.isNull(basicUserVO)?"":basicUserVO.getId());
       });
    }

}
