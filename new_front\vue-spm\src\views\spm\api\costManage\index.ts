// ************************************************ //
// ******************费用管理相关请求***************** //
// ************************************************ //

import Api from '/@/api';

/**
 * 获取成本中心分页
 * @param
 *
 */
export async function getCostCenterList(params) {
  return new Api('/spm/costCenter/page').fetch(params, '', 'post');
}
/**
 * 获取费用科目分页
 * @param
 *
 */
export async function getExpenseAccount(params) {
  return new Api('/spm/expenseSubject/tree/page').fetch(params, '', 'post');
}

/**
 * 获取预算编制分页
 * @param
 *
 */
export async function getBudgeting(params) {
  return new Api('/spm/project-budget/getPage').fetch(params, '', 'post');
}

/**
 * 获取成本执行分页
 * @param
 *
 */
export async function getCostExecute(params) {
  return new Api('/spm/project-cost/getPage').fetch(params, '', 'post');
}
/**
 * 获取成本执行发生预算弹窗分页
 * @param
 *
 */
export async function getCostExecuteBudget(params) {
  return new Api('/spm/project-budget/page').fetch(params, '', 'post');
}
/**
 * 获取预算执行发生预算弹窗分页
 * @param
 *
 */
export async function getBudgetExecute(params) {
  return new Api('/spm/project-budget/getPage').fetch(params, '', 'post');
}
/**
 * 获取预算执行详情弹窗
 * @param
 *
 */
export async function getBudgetExecuteDetails(params) {
  return new Api('/spm/project-cost/detail/getPage').fetch(params, '', 'post');
}
