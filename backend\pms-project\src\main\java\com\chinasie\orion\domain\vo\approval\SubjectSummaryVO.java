package com.chinasie.orion.domain.vo.approval;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: lsy
 * @date: 2024/5/6
 * @description:
 */
@Data
public class SubjectSummaryVO {

    @ApiModelProperty(value = "概算总金额")
    private BigDecimal allAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "直接费占比")
    private String directFeeRate = "0%";

    @ApiModelProperty(value = "间接费占比")
    private String indirectFeeRate = "0%";

    @ApiModelProperty(value = "材料费")
    private BigDecimal materialFee = BigDecimal.ZERO;

    @ApiModelProperty(value = "材料费占比")
    private String materialFeeRate = "0%";

    @ApiModelProperty(value = "工资及劳务费")
    private BigDecimal laborFee = BigDecimal.ZERO;

    @ApiModelProperty(value = "人天数")
    private BigDecimal peopleNum = BigDecimal.ZERO;

    @ApiModelProperty(value = "专用费")
    private BigDecimal dedicatedFee = BigDecimal.ZERO;

    @ApiModelProperty(value = "专用费占比")
    private String dedicatedFeeRate = "0%";


    @ApiModelProperty(value = "间接费")
    private BigDecimal indirectFee = BigDecimal.ZERO;

    @ApiModelProperty(value = "间接费占比详情")
    private Map<String, String> indirectFeeRateMap = new HashMap<>();


}
