package com.chinasie.orion.service.impl;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.chinasie.orion.constant.AmpereRingBoardConfigConstant;
import com.chinasie.orion.domain.dto.AmpereRingBoardConfigKpiDTO;
import com.chinasie.orion.domain.entity.AmpereRingBoardConfigDept;
import com.chinasie.orion.domain.entity.AmpereRingBoardConfigKpi;
import com.chinasie.orion.domain.vo.AmpereRingBoardConfigKpiVO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.AmpereRingBoardConfigKpiMapper;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.service.AmpereRingBoardConfigKpiService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 21 日
 **/
@Service
@Slf4j
public class AmpereRingBoardConfigKpiServiceImpl extends OrionBaseServiceImpl<AmpereRingBoardConfigKpiMapper, AmpereRingBoardConfigKpi> implements AmpereRingBoardConfigKpiService {


    /**
     * 查看考核指标
     *
     * @param kpiVo
     * @return
     */
    @Override
    public List<AmpereRingBoardConfigKpiVO> queryKpi(AmpereRingBoardConfigKpiVO kpiVo) {
        if(!StringUtils.hasText(kpiVo.getKpiCode())){
            throw new BaseException(HttpStatus.METHOD_NOT_ALLOWED.value(), "考核指标为空");
        }
        LambdaQueryWrapperX<AmpereRingBoardConfigKpi> lambdaQueryWrapperX=new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(AmpereRingBoardConfigKpi::getKpiCode, kpiVo.getKpiCode());
        lambdaQueryWrapperX.orderByAsc(AmpereRingBoardConfigKpi::getSort);
        List<AmpereRingBoardConfigKpi> configKpiList = this.baseMapper.selectList(lambdaQueryWrapperX);

        List<AmpereRingBoardConfigKpiVO> kpiVOS = BeanCopyUtils.convertListTo(configKpiList, AmpereRingBoardConfigKpiVO::new);

        return kpiVOS;
    }

    /**
     * 考核指标的新增
     *
     * @param ampereRingBoardConfigKpiDTO
     * @return
     */
    @Override
    public Boolean kpiAdd(AmpereRingBoardConfigKpiDTO ampereRingBoardConfigKpiDTO) {
        LambdaQueryWrapperX<AmpereRingBoardConfigKpi> countQuery=new LambdaQueryWrapperX<>();
        countQuery.eq(AmpereRingBoardConfigKpi::getKpiCode, ampereRingBoardConfigKpiDTO.getKpiCode());
        countQuery.eq(AmpereRingBoardConfigKpi::getLogicStatus,1);
        Long kpiCodeCount = this.baseMapper.selectCount(countQuery);
        if(!CollectionUtils.isEmpty(ampereRingBoardConfigKpiDTO.getKpiList())){
            kpiCodeCount+=ampereRingBoardConfigKpiDTO.getKpiList().size();
        }
        if(AmpereRingBoardConfigConstant.AMPERE_RING_BOARD_CONFIG_BLOC_KPI_CODE.equals(ampereRingBoardConfigKpiDTO.getKpiCode())){
            if(isExistsEventLevel(ampereRingBoardConfigKpiDTO.getKpiCode(),ampereRingBoardConfigKpiDTO.getKpiList())){
                throw  new BaseException(HttpStatus.BAD_REQUEST.value(),"当前统计指标已经存在");
            }
            //集团考核指标
            if(kpiCodeCount>AmpereRingBoardConfigConstant.AMPERE_RING_BOARD_CONFIG_BLOC_KPI_MAX_SIZE){
                throw new BaseException(HttpStatus.BAD_REQUEST.value(),"集团的考核指标最多6个");
            }
        }
        if(AmpereRingBoardConfigConstant.AMPERE_RING_BOARD_CONFIG_COMPANY_MONITORING_KPI_CODE.equals(ampereRingBoardConfigKpiDTO.getKpiCode())){
            if(isExistsEventLevel(ampereRingBoardConfigKpiDTO.getKpiCode(),ampereRingBoardConfigKpiDTO.getKpiList())){
                throw  new BaseException(HttpStatus.BAD_REQUEST.value(),"当前统计指标已经存在");
            }
            //公司监控指标 12
            if(kpiCodeCount>AmpereRingBoardConfigConstant.AMPERE_RING_BOARD_CONFIG_COMPANY_MONITORING_KPI_MAX_SIZE){
                throw new BaseException(HttpStatus.BAD_REQUEST.value(),"集团的考核指标最多12个");
            }
        }
        if(AmpereRingBoardConfigConstant.AMPERE_RING_BOARD_CONFIG_COMPANY_CONTROL_KPI_CODE.equals(ampereRingBoardConfigKpiDTO.getKpiCode())){
            if(isExistsEventLevel(ampereRingBoardConfigKpiDTO.getKpiCode(),ampereRingBoardConfigKpiDTO.getKpiList())){
                throw  new BaseException(HttpStatus.BAD_REQUEST.value(),"当前统计指标已经存在");
            }
            //公司管控指标 6
            if(kpiCodeCount>AmpereRingBoardConfigConstant.AMPERE_RING_BOARD_CONFIG_COMPANY_CONTROL_KPI_MAX_SIZE){
                throw new BaseException(HttpStatus.BAD_REQUEST.value(),"集团的考核指标最多6个");
            }
        }
        countQuery.orderByDesc(AmpereRingBoardConfigKpi::getSort);
        countQuery.last("limit 1");
        AmpereRingBoardConfigKpi countMax=this.baseMapper.selectOne(countQuery);
        final int[] sort = {Integer.parseInt(Objects.isNull(countMax) ? "1" : ((Objects.isNull(countMax.getSort())?0:countMax.getSort()) + 1) + "")};
        List<AmpereRingBoardConfigKpi> configKpis = ampereRingBoardConfigKpiDTO.getKpiList().stream().map(o -> {
            o.setKpiCode(ampereRingBoardConfigKpiDTO.getKpiCode());
            o.setCreatorId(CurrentUserHelper.getCurrentUserId());
            o.setCreateTime(DateTime.now());
            o.setSort(sort[0]);
            sort[0]++;
            return o;
        }).collect(Collectors.toList());


        boolean save = this.saveBatch(configKpis);

        return true;
    }

    /**
     * 判断指标事件是否已经重复
     * @param kpiCode
     * @param kpiList
     * @return
     */
    private boolean isExistsEventLevel(String kpiCode, List<AmpereRingBoardConfigKpi> kpiList) {
        LambdaQueryWrapperX<AmpereRingBoardConfigKpi> queryLamWraperX=new LambdaQueryWrapperX<>();
        queryLamWraperX.in(AmpereRingBoardConfigKpi::getEventLevel,kpiList.stream().map(o->o.getEventLevel()).collect(Collectors.toList()));
        queryLamWraperX.eq(AmpereRingBoardConfigKpi::getKpiCode,kpiCode);
        return this.baseMapper.exists(queryLamWraperX);
    }

    /**
     * 批量移除 考核指标
     *
     * @param ids
     * @return
     */
    @Override
    public Boolean removeIds(List<String> ids) {
        LambdaQueryWrapperX<AmpereRingBoardConfigKpi> remove=new LambdaQueryWrapperX<>();
        remove.in(AmpereRingBoardConfigKpi::getId,ids);
        this.remove(remove);
        return true;
    }

    /**
     * 考核指标维护移动
     *
     * @param boardConfigKpiDTO
     * @return
     */
    @Override
    public Boolean kpiMove(AmpereRingBoardConfigKpiDTO boardConfigKpiDTO) {
        if(!StringUtils.hasText(boardConfigKpiDTO.getOperationType())){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(),"operationType 操作类型不能为空");
        }

        if(!StringUtils.hasText(boardConfigKpiDTO.getId())){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(),"id 不能为空");
        }
        if(Objects.isNull(boardConfigKpiDTO.getKpiCode())){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(),"kpiCode 考核指标类型不能为空");
        }

        //获取当前指标的信息
        AmpereRingBoardConfigKpi moveKpi = this.baseMapper.selectOne(AmpereRingBoardConfigKpi::getId, boardConfigKpiDTO.getId());
        if(Objects.isNull(moveKpi)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(),"当前考核指标不存在");
        }
        LambdaQueryWrapperX<AmpereRingBoardConfigKpi> queryWrapperX=new LambdaQueryWrapperX<>();
        queryWrapperX.eq(AmpereRingBoardConfigKpi::getKpiCode,boardConfigKpiDTO.getKpiCode());
        queryWrapperX.eq(AmpereRingBoardConfigKpi::getLogicStatus,1);
        //下移 上移 替换
        if(AmpereRingBoardConfigConstant.AMPERE_RING_BOARD_CONFIG_DEPT_OPERATION_UP.equals(boardConfigKpiDTO.getOperationType())
        || AmpereRingBoardConfigConstant.AMPERE_RING_BOARD_CONFIG_DEPT_OPERATION_DOWN.equals(boardConfigKpiDTO.getOperationType())){
            int moveKpiSort=moveKpi.getSort();
            List<AmpereRingBoardConfigKpi> updateVOs= Lists.newArrayList();
            updateVOs.add(moveKpi);
            //下移
            if(AmpereRingBoardConfigConstant.AMPERE_RING_BOARD_CONFIG_DEPT_OPERATION_DOWN.equals(boardConfigKpiDTO.getOperationType())){
                queryWrapperX.gt(AmpereRingBoardConfigKpi::getSort,moveKpiSort);
                queryWrapperX.orderByAsc(AmpereRingBoardConfigKpi::getSort);
            }
            //上移
            if(AmpereRingBoardConfigConstant.AMPERE_RING_BOARD_CONFIG_DEPT_OPERATION_UP.equals(boardConfigKpiDTO.getOperationType())){
                queryWrapperX.lt(AmpereRingBoardConfigKpi::getSort,moveKpiSort);
                queryWrapperX.orderByDesc(AmpereRingBoardConfigKpi::getSort);
            }
            queryWrapperX.last("limit 1");
            AmpereRingBoardConfigKpi reKpi = this.baseMapper.selectOne(queryWrapperX);
            if(Objects.nonNull(reKpi)){
                int reKpiSort=reKpi.getSort();
                moveKpi.setSort(reKpiSort);
                reKpi.setSort(moveKpiSort);
                updateVOs.add(reKpi);
                this.updateBatchById(updateVOs);
            }

            return true;
        }
        // 置底 最大sort +1;
        if(AmpereRingBoardConfigConstant.AMPERE_RING_BOARD_CONFIG_DEPT_OPERATION_BOTTOM.equals(boardConfigKpiDTO.getOperationType())){
            queryWrapperX.select(AmpereRingBoardConfigKpi::getSort)
                    .orderByDesc(AmpereRingBoardConfigKpi::getSort)
                    .last("limit 1");
            AmpereRingBoardConfigKpi boardConfigKpi = this.baseMapper.selectOne(queryWrapperX);
            moveKpi.setSort(boardConfigKpi.getSort()+1);
            this.baseMapper.updateById(moveKpi);
            return true;
        }
        //置顶 最小sort -1;
        if(AmpereRingBoardConfigConstant.AMPERE_RING_BOARD_CONFIG_DEPT_OPERATION_TOP.equals(boardConfigKpiDTO.getOperationType())){
            queryWrapperX.select(AmpereRingBoardConfigKpi::getSort)
                    .orderByAsc(AmpereRingBoardConfigKpi::getSort)
                    .last("limit 1");
            AmpereRingBoardConfigKpi topKpi = this.baseMapper.selectOne(queryWrapperX);
            moveKpi.setSort(topKpi.getSort()-1);
            this.baseMapper.updateById(moveKpi);
            return true;
        }
        return false;
    }
}
