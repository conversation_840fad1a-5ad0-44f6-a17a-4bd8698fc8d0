package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.TakeEffectDTO;
import com.chinasie.orion.domain.dto.TaskSubjectDTO;
import com.chinasie.orion.domain.entity.TaskSubject;
import com.chinasie.orion.domain.vo.SimpleVo;
import com.chinasie.orion.domain.vo.TaskSubjectVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.PageRequest;
import com.chinasie.orion.page.PageResult;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/13/10:17
 * @description:
 */
public interface TaskSubjectService extends OrionBaseService<TaskSubject> {

    /**
     * 新增任务科目
     * @param taskSubjectDTO
     * @return
     * @throws Exception
     */
    String saveTaskSubject(TaskSubjectDTO taskSubjectDTO) throws Exception;

    /**
     * 获取任务科目列表
     * @param projectId
     * @return
     * @throws Exception
     */
    List<SimpleVo> getTaskSubjectListByTakeEffect(String projectId) throws Exception;
    /**
     * 获取任务科目分页
     * @param pageRequest
     * @return
     * @throws Exception
     */
    PageResult<TaskSubjectVO> getTaskSubjectPage(PageRequest<TaskSubjectDTO> pageRequest) throws Exception;

    /**
     * 获取任务科目详情
     * @param id
     * @return
     * @throws Exception
     */
    TaskSubjectVO getTaskSubjectDetail(String id) throws Exception;

    /**
     * 编辑任务科目
     * @param taskSubjectDTO
     * @return
     * @throws Exception
     */
    Boolean editTaskSubject(TaskSubjectDTO taskSubjectDTO) throws Exception;

    /**
     * 批量删除任务科目
     * @param ids
     * @return
     * @throws Exception
     */
    Boolean removeTaskSubject(List<String> ids) throws Exception;

    /**
     * 批量启用禁用
     * @param takeEffectDTO
     * @return
     * @throws Exception
     */
    Boolean takeEffectTaskSubject(TakeEffectDTO takeEffectDTO) throws Exception;
}
