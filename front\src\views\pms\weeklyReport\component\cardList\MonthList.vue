<template>
  <div class="MonthBox">
    <div
      v-for="item of 2"
      :key="item"
      class="monthBoxItem"
    >
      <div class="weekNumber">
        {{ 44 }}w
      </div>
      <div class="circleCore">
        {{ 4 }}
      </div>
      <div>
        <div
          v-for="item of 7"
          :key="item"
        >
          <span class="mr20">3h</span>
          <span>完成计划管理开发方案</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, PropType } from 'vue';
import { Button } from 'ant-design-vue';
// import { PlusOutlined } from '@ant-design/icons-vue';
const AButton = Button;
const emits = defineEmits<{
    (e: 'update:title', hah: string): void;
}>();
const props = defineProps({
  trigger: {
    type: [Array] as PropType<('contextmenu' | 'click' | 'hover')[]>,
    default: () => ['contextmenu'],
  },
  selectedKeys: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
});

const state = reactive({
  one: 666666,
});
</script>

<style scoped lang="less">
.MonthBox {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-content: flex-start;

  .monthBoxItem {
    width: 49%;
    height: 300px;
    margin-bottom: 2%;
    border-radius: 3px;
    box-sizing: border-box;
    border: 1px solid #ccc;
    padding: 40px;
    position: relative;

    .weekNumber {
      position: absolute;
      width: 30px;
      height: 30px;
      text-align: center;
      line-height: 30px;
      left: 5px;
      top: 5px;
    }

    .circleCore {
      position: absolute;
      width: 30px;
      height: 30px;
      border-radius: 50%;
      background-color: red;
      text-align: center;
      line-height: 30px;
      right: 5px;
      top: 5px;
    }
  }
}
</style>
