package com.chinasie.orion.domain.vo.performance;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @author: lsy
 * @date: 2024/5/23
 * @description:
 */
@Data
@ExcelIgnoreUnannotated
public class ProjectSchemePerformanceDurationVO {

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    @ExcelProperty(value = "序号", index = 0)
    private Integer sort;

    /**
     * 计划ID
     */
    @ApiModelProperty(value = "计划ID")
    private String id;

    /**
     * 计划名称
     */
    @ApiModelProperty(value = "计划名称")
    @ExcelProperty(value = "计划名称", index = 1)
    private String name;

    /**
     * 状态对象
     */
    @ApiModelProperty(value = "状态对象")
    private DataStatusVO dataStatus;

    /**
     * 计划状态
     */
    @ApiModelProperty(value = "计划状态")
    @ExcelProperty(value = "计划状态", index = 2)
    private String statusName;


    private String projectId;

    @ExcelProperty(value = "所属项目", index = 3)
    private String projectName;

    /**
     * 计划层级
     */
    @ApiModelProperty(value = "计划层级")
    @ExcelProperty(value = "计划层级", index = 4)
    private Integer level;

    /**
     * 计划类型
     */
    @ApiModelProperty(value = "计划类型")
    private String nodeType;

    /**
     * 计划类型
     */
    @ApiModelProperty(value = "计划类型")
    @ExcelProperty(value = "计划类型", index = 5)
    private String nodeTypeName;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    private String rspUser;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    @ExcelProperty(value = "责任人", index = 6)
    private String rspUserName;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    @ExcelProperty(value = "计划开始时间", index = 7)
    private Date beginTime;

    /**
     * 工期
     */
    @ApiModelProperty(value = "工期")
    @ExcelProperty(value = "工期", index = 8)
    private Integer durationDays;

    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    @ExcelProperty(value = "计划结束时间", index = 9)
    private Date endTime;

    /**
     * 实际开始时间
     */
    @ApiModelProperty(value = "实际开始时间")
    @ExcelProperty(value = "实际开始时间", index = 10)
    private Date actualBeginTime;

    /**
     * 实际结束时间
     */
    @ApiModelProperty(value = "实际结束时间")
    @ExcelProperty(value = "实际结束时间", index = 11)
    private Date actualEndTime;
}
