<template>
  <ProjectLayout
    :project-data="projectData"
    :menu-data="menuData"
    :default-action-id="defaultActionId"
    :is-header="true"
    @menu-change="onMenuChange"
  >
    <template #header-info>
      <div
        v-if="projectData"
        class="info"
      >
        <div>
          状态：<span class="status-wrap">
            <i>{{ projectData.status ? '已发布' : '草稿' }}</i>
          </span>
        </div>
        <div>所有者：{{ projectData.creatorName }}</div>
        <div class="date-time">
          修改时间：{{ projectData.modifyTime }}
        </div>
      </div>
    </template>
    <template #header-right>
      <div>
        <AButton
          v-if="showBtn"
          style="margin-right: 10px"
          type="default"
          @click="onBack"
        >
          关闭返回
        </AButton>
        <AButton
          v-if="status === '0'"
          style="margin-right: 10px"
          type="primary"
          @click="onTask"
        >
          流程设计
        </AButton>
      </div>
    </template>
    <ProjectContentLayout :content-title="menuTitle">
      <div
        v-show="menuIndex === 0"
        class="pd-20"
      >
        <div v-if="isPower('LCMB_container_01', powerData, true)">
          <a-descriptions
            v-if="projectData"
            bordered
            :column="2"
          >
            <a-descriptions-item label="模版名称：">
              {{ projectData.name }}
            </a-descriptions-item>
            <a-descriptions-item label="编号：">
              {{ projectData.flowKey }}
            </a-descriptions-item>
            <a-descriptions-item label="所有者：">
              {{
                projectData.creatorName
              }}
            </a-descriptions-item>
            <a-descriptions-item label="所属部门：">
              {{
                projectData.organizationsName
              }}
            </a-descriptions-item>
            <a-descriptions-item label="版本号：">
              {{ projectData.version }}
            </a-descriptions-item>
            <a-descriptions-item label="是否为主体版本：">
              {{
                projectData.major ? '是' : '否'
              }}
            </a-descriptions-item>
            <a-descriptions-item label="状态：">
              {{
                projectData.status ? '已发布' : '草稿'
              }}
            </a-descriptions-item>
            <a-descriptions-item label="所属分类：">
              {{
                projectData.classifyName
              }}
            </a-descriptions-item>
            <a-descriptions-item label="创建人：">
              {{
                projectData.creatorName
              }}
            </a-descriptions-item>
            <a-descriptions-item label="创建时间：">
              {{
                projectData.createTime
              }}
            </a-descriptions-item>
            <a-descriptions-item label="修改人：">
              {{ projectData.modifyName }}
            </a-descriptions-item>
            <a-descriptions-item label="修改时间：">
              {{
                projectData.modifyTime
              }}
            </a-descriptions-item>
            <a-descriptions-item label="描述：">
              {{ projectData.description }}
            </a-descriptions-item>
          </a-descriptions>
        </div>
        <div v-else>
          <a-empty />
        </div>
      </div>
      <div
        v-show="menuIndex === 1"
        class="pd-20"
      >
        <div v-if="isPower('LCMB_container_02', powerData, true)">
          <orion-table
            ref="presetTableRef"
            :options="presetOptions"
            row-key="id"
          >
            <template #name="{ record }">
              <a @click="onChangeVersion(record)">{{ record.name }}</a>
            </template>
            <template #status="{ record }">
              <span v-if="record.status === 1">启用</span>
              <span
                v-if="record.status === 0"
                style="color: #9ea3b5"
              >草稿</span>
              <span
                v-if="record.status === -1"
                style="color: red"
              >禁用</span>
            </template>
            <template #action="{ record }">
              <div class="action-line">
                <a
                  v-if="record.status === 1"
                  @click="onStop(record)"
                >停用</a>
                <a
                  v-if="record.status === -1"
                  @click="onStart(record)"
                >启用</a>
                <a
                  v-if="record.status === 0"
                  @click="onEdit(record)"
                >编辑</a>
                <a
                  v-if="record.status === 0 && isPower('LCMB_container_button_02', powerData, true)"
                  @click="onDelete(record)"
                >删除</a>
                <a
                  v-if="record.status === 0"
                  @click="onPublish(record)"
                >发布</a>
                <a
                  v-if="
                    record.status === 1 &&
                      !record.major &&
                      isPower('LCMB_container_button_01', powerData, true)
                  "
                  @click="onMajor(record)"
                >设为主版本</a>
                <a
                  v-if="record.status !== 0"
                  @click="onCopy(record)"
                >复制</a>
              </div>
            </template>
            <template #major="{ record }">
              <span>{{ record.major ? '是' : '否' }}</span>
            </template>
          </orion-table>
          <a-modal
            v-model:visible="copyVisible"
            :centered="true"
            title="复制流程"
            @ok="handleCopy"
          >
            <div class="copy-modal">
              <span>模版名称:</span>
              <a-input
                v-model:value="copyValue"
                placeholder="请输入模版名称"
              />
            </div>
          </a-modal>
          <a-modal
            v-model:visible="addVisible"
            :centered="true"
            title="新增版本"
            @ok="handleAddversion"
          >
            <div class="copy-modal">
              <span>版本名称:</span>
              <a-input
                v-model:value="addValue"
                placeholder="请输入版本名称"
              />
            </div>
          </a-modal>
        </div>
        <div v-else>
          <a-empty />
        </div>
      </div>
      <div
        v-show="menuIndex === 2"
        class="pd-20"
      >
        <div
          v-if="isPower('LCMB_container_03', powerData, true)"
          class="flow-container"
        >
          <div class="bpmn-containers">
            <div
              id="flow-canvas"
              ref="canvas"
              class="canvas"
            />
          </div>
        </div>
        <div v-else>
          <a-empty />
        </div>
      </div>
      <div
        v-show="menuIndex === 3"
        class=""
      >
        <div v-if="isPower('LCMB_container_04', powerData, true)">
          <orion-table
            ref="instanceTableRef"
            :options="instanceOptions"
            row-key="id"
          >
            <template #deploymentName="{ record }">
              <a @click="onDetail(record)">{{ record.deploymentName }}</a>
            </template>
            <template #action>
              <div>
                <a>停用</a>
              </div>
            </template>
            <template #major="{ record }">
              <span>{{ record.major ? '是' : '否' }}</span>
            </template>
          </orion-table>
        </div>
        <div v-else>
          <a-empty />
        </div>
      </div>
      <div
        v-show="menuIndex === 4"
        class="pd-20"
      >
        <div v-if="isPower('LCMB_container_05', powerData, true)">
          <!-- <a-table
            :columns="columns"
            :data-source="tableData"
            rowKey="activityId"
            :pagination="false"
          /> -->
          <orion-table
            ref="journalTableRef"
            :options="journalOptions"
          >
            <template #operateType="{ record }">
              <span>{{ changeType(record.operateType) }}</span>
            </template>
            <template #createTime="{ record }">
              <span>{{ stampDate(record.createTime) }}</span>
            </template>
          </orion-table>
        </div>
        <div v-else>
          <a-empty />
        </div>
      </div>
    </ProjectContentLayout>
  </ProjectLayout>
</template>
<script lang="ts">
import {
  defineComponent, ref, reactive, toRefs, inject, watchEffect,
} from 'vue';
import {
  BasicTable, Layout, useActionsRecord, OrionTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer, isPower, Icon,
} from 'lyra-component-vue3';
// import { isPower } from '/@/hooks/power/useBusinessPrivilege';
// import Layout from '/@/components/Layout';
// import Icon from '/@/components/Icon';
import { useRoute, useRouter } from 'vue-router';
import { useUserStore } from '/@/store/modules/user';
import {
  Collapse, Input, Table, Select, message, Empty,
} from 'ant-design-vue';
import { ProjectLayout, ProjectContentLayout } from '/@/views/pms/projectLaborer/components/ProjectLayout';
// import { useModal } from '/@/components/Modal';
// import OrionTable from '/@/components/OrionTable';
import { stampDate } from '/@/views/pms/projectLaborer/utils/dateUtil';
import { useTabs } from '/@/hooks/web/useTabs';
import { workflowApi } from '../util/apiConfig';

import Viewer from 'bpmn-js/lib/Viewer';

import CustomRenderer from '../detail/extra/index';
import MoveCanvasModule from 'diagram-js/lib/navigation/movecanvas';
import Api from '/@/api/index';
// import { useActionsRecord } from '/@/hooks/actionsRecord/useActionsRecord';

const { Panel } = Collapse;

export default defineComponent({
  name: 'Flowdetail',
  components: {
    Layout,
    aCollapse: Collapse,
    aCollapsePanel: Panel,
    [Input.TextArea.name]: Input.TextArea,
    ATable: Table,
    Icon,
    [Select.name]: Select,
    SelectOption: Select.Option,
    ProjectLayout,
    ProjectContentLayout,
    OrionTable,
    AEmpty: Empty,
  },
  setup() {
    const userStore: any = useUserStore();
    const powerData = inject('powerData', {});
    const transferModalRef: any = ref(null);
    const presetTableRef = ref<Nullable<any>>(null); // table的ref
    const { closeCurrent } = useTabs();
    const route = useRoute();
    const router = useRouter();
    const state: any = reactive({
      showBtn: !!route.query.showBtn,
      status: route.query.status,
      copyVisible: false,
      addVisible: false,
      copyValue: '',
      addValue: '',
      currentId: '',
      hasSet: false,
      projectData: null,
      hasRender: false,
      menuIndex: route.query.from ? 2 : 0,
      defaultActionId: route.query.from ? 3 : 1,
      currentIndex: 0,
      menuTitle: route.query.from ? '审批信息' : '基本信息',
      showEdit: !!route.query.from,
      menuData: [
        {
          name: '基本信息',
          id: 1,
        },
        {
          name: '流程版本',
          id: 2,
        },
        {
          name: '流程图',
          id: 3,
        },
        {
          name: '关联实例',
          id: 4,
        },
        {
          name: '操作日志',
          id: 5,
        },
      ],
      presetOptions: {
        deleteToolButton: 'add|delete|enable|disable',
        showSmallSearch: false,
        auto: {
          url: `${workflowApi}/act-flow/version`,
          params: {
            query: {
              flowKey: route.query.flowKey,
            },
          },
        },
        tool: [
          {
            type: 'button',
            position: 'before',
            buttonGroup: [
              [
                {
                  icon: 'fa fa-plus',
                  name: '新增',
                  enable: true,
                  cb: () => {
                    state.addVisible = true;
                  },
                },
              ],
            ],
          },
        ],
        columns: [
          {
            title: '名称',
            dataIndex: 'name',
            slots: { customRender: 'name' },
          },
          {
            title: '版本号',
            dataIndex: 'version',
          },
          {
            title: '是否主版本',
            dataIndex: 'major',
            slots: { customRender: 'major' },
          },
          {
            title: '所属分类',
            dataIndex: 'classifyName',
          },
          {
            title: '状态',
            dataIndex: 'status',
            slots: { customRender: 'status' },
          },
          {
            title: '修改人',
            dataIndex: 'modifyTime',
          },
          {
            title: '修改时间',
            dataIndex: 'modifyTime',
          },
          {
            title: '操作',
            dataIndex: 'action',
            width: 220,
            slots: { customRender: 'action' },
          },
        ],
      },
      instanceOptions: {
        deleteToolButton: 'add|delete|enable|disable',
        showSmallSearch: false,
        auto: {
          url: `${workflowApi}/act-prearranged/associated`,
          params: {
            query: route.query.flowKey,
          },
        },
        columns: [
          {
            title: '流程编号',
            dataIndex: 'businessKey',
          },
          {
            title: '流程名称',
            dataIndex: 'procInstName',
            slots: { customRender: 'procInstName' },
          },
          {
            title: '流程类型',
            dataIndex: 'procDefName',
            slots: { customRender: 'procDefName' },
          },
          {
            title: '版本',
            dataIndex: 'procDefVer',
          },
          {
            title: '流程状态',
            dataIndex: 'statusName',
          },
          {
            title: '发起人',
            dataIndex: 'creatorName',
          },
          {
            title: '发起时间',
            dataIndex: 'createTime',
          },
        ],
      },
      journalOptions: {
        deleteToolButton: 'add|delete|enable|disable',
        showSmallSearch: false,
        auto: {
          url: '/log/log',
          params: {
            query: {
              objectId: route.query.id,
            },
          },
        },
        columns: [
          {
            title: '操作',
            dataIndex: 'operateType',
            slots: { customRender: 'operateType' },
          },
          {
            title: '操作人',
            dataIndex: 'creatorName',
          },
          {
            title: '操作时间',
            dataIndex: 'createTime',
            slots: { customRender: 'createTime' },
          },
          {
            title: '记录',
            dataIndex: 'remark',
          },
        ],
      },
    });

    new Api(workflowApi)
      .fetch(
        {
          ids: [route.query.id],
        },
        'act-flow/info',
        'POST',
      )
      .then((res) => {
        res[0].projectCode = res[0].flowKey;
        console.log('detail', res);
        state.projectData = res[0];

        const { id, name } = state.projectData;
        watchEffect((data) => {
        });
      });

    // function _getJournal() {
    //   return new Api(workflowApi).fetch(
    //     {
    //       userId: userStore.getUserInfo.id,
    //       procInstId: route.query.processInstanceId
    //     },
    //     'act-inst-detail/journal',
    //     'GET'
    //   );
    // }

    // if (route.query.processInstanceId) {
    //   _getJournal().then((data) => {
    //     state.tableData = data;
    //   });
    // }

    function _renderFlow() {
      if (state.hasRender) {
        return;
      }
      new Api(workflowApi)
        .fetch(
          {
            userId: userStore.getUserInfo.id,
            uid: route.query.id,
          },
          'act-flow/bpmn',
          'GET',
        )
        .then((res) => {
          renderFlow(state, res);
          state.hasRender = true;
        });
    }

    const { registerForm, openModalForm, setModalFormProps } = handleModal();
    return {
      stampDate,
      isPower,
      powerData,
      registerForm,
      openModalForm,
      setModalFormProps,
      activeKey: ref('1'),
      transferModalRef,
      ...toRefs(state),
      onMenuChange(e) {
        state.menuIndex = e.index;
        state.menuTitle = e.item.name;
        if (e.index === 2) {
          _renderFlow();
        }
      },
      presetTableRef,
      onChangeVersion(row) {
        closeCurrent();
        setTimeout(() => {
          router.push({
            name: 'FlowDetail',
            query: {
              id: row.id,
              flowKey: row.flowKey,
              procdefId: row.procDefId,
              status: row.status,
            },
          });
        }, 500);
      },
      onBack() {
        closeCurrent();
        router.push({
          name: 'FlowTemplate',
          query: {
            status: route.query.status,
          },
        });
      },
      onTask() {
        router.push({
          name: 'TaskFlow',
          query: {
            id: route.query.id,
            classifyId: route.query.classifyId,
            name: route.query.name,
            status: route.query.status,
            createTime: route.query.createTime,
            modifyTime: route.query.modifyTime,
            classifyName: route.query.classifyName,
            description: route.query.description,
            modifyName: route.query.modifyName,
            creatorName: route.query.creatorName,
            routerName: 'FlowDetail',
          },
        });
      },
      onDetail(row) {
        if (!isPower('LCMB_container_button_04', powerData, true)) {
          return;
        }
        router.push({
          path: '/flowcenter/detail',
          query: {
            id: row.id,
            processInstanceId: row.processInstanceId,
            processDefinitionId: row.processDefinitionId,
          },
        });
      },
      onStop(row) {
        new Api(workflowApi)
          .fetch(
            {
              id: row.id,
              userId: userStore.getUserInfo.id,
            },
            'act-flow/disable',
            'GET',
          )
          .then(() => {
            message.success('操作成功');
            presetTableRef.value.reload();

            const { id, name } = row;
          });
      },
      onStart(row) {
        new Api(workflowApi)
          .fetch(
            {
              id: row.id,
              userId: userStore.getUserInfo.id,
            },
            'act-flow/publish',
            'GET',
          )
          .then(() => {
            message.success('操作成功');
            presetTableRef.value.reload();
          });
      },
      onCopy(row) {
        state.copyVisible = true;
        state.copyValue = `${row.name}_副本`;
        state.currentId = row.id;
      },
      handleCopy() {
        new Api(workflowApi)
          .fetch(
            {
              id: state.currentId,
              name: state.copyValue,
              continueVersion: true,
              userId: userStore.getUserInfo.id,
            },
            'act-flow/copy',
            'GET',
          )
          .then((result) => {
            state.copyVisible = false;
            message.success('复制成功');
            presetTableRef.value.reload();
          });
      },
      handleAddversion() {
        new Api(workflowApi)
          .fetch(
            {
              id: route.query.id,
              name: state.addValue,
              continueVersion: true,
              clearBpmn: true,
              userId: userStore.getUserInfo.id,
            },
            'act-flow/copy',
            'GET',
          )
          .then((result) => {
            state.addVisible = false;
            message.success('新增成功');
            presetTableRef.value.reload();
          });
      },
      onMajor(row) {
        new Api(workflowApi)
          .fetch(
            {
              version: row.version,
              flowKey: row.flowKey,
            },
            'act-flow/major',
            'PUT',
          )
          .then(() => {
            message.success('操作成功');
            presetTableRef.value.reload();
          });
      },
      onPublish(row) {
        new Api(workflowApi)
          .fetch(
            {
              id: row.id,
              userId: userStore.getUserInfo.id,
            },
            'act-flow/publish',
            'GET',
          )
          .then(() => {
            message.success('操作成功');
            presetTableRef.value.reload();
          });
      },
      onDelete(row) {
        new Api(workflowApi)
          .fetch({}, `act-flow/delete?id=${row.id}&userId=${userStore.getUserInfo.id}`, 'DELETE')
          .then(() => {
            message.success('删除成功');
            presetTableRef.value.reload();
          });
      },
      onEdit(row) {
        router.push({
          name: 'TaskFlow',
          query: {
            id: row.id,
            classifyId: row.classifyId,
            name: row.name,
            status: row.status,
            createTime: row.createTime,
            modifyTime: row.modifyTime,
            classifyName: row.classifyName,
            description: row.description,
            modifyName: row.modifyName,
            creatorName: row.creatorName,
            routerName: 'FlowDetail',
          },
        });
      },
      changeType(type) {
        if (type === 'SAVE') {
          return '创建';
        }
        if (type === 'UPDATE') {
          return '编辑';
        }
        if (type === 'MAJOR') {
          return '设为主版本';
        }
        if (type === 'ENABLE') {
          return '启用';
        }
        if (type === 'DISABLE') {
          return '停用';
        }
        if (type === 'PUBLISH') {
          return '发布';
        }
        if (type === 'DELETE') {
          return '删除';
        }
        if (type === 'COPY') {
          return '复制';
        }
        return '';
      },
    };
  },
});

function handleModal() {
  const [registerForm, { openModal: openModalForm, setModalProps: setModalFormProps }] = useModal();
  return {
    registerForm,
    openModalForm,
    setModalFormProps,
  };
}

export function renderFlow(state, xml) {
  const bpmnModeler = new Viewer({
    container: '#flow-canvas',
    additionalModules: [CustomRenderer, MoveCanvasModule],
  });
  let completeActivity = {};

  console.log(state);
  // state.tableData.forEach((item) => {
  //   if (item.complete) {
  //     completeActivity[item.activityId] = true;
  //   }
  // });
  let customRenderer = bpmnModeler.get('customRenderer');
  customRenderer.setComplete(completeActivity);

  bpmnModeler.importXML(xml, (err) => {
    if (err) {
      console.error(err);
    } else {
      bpmnModeler.get('canvas').zoom('fit-viewport', 'auto'); // 自动调整流程在画布的位置
    }
  });
}
</script>
<style lang="less" scoped>
  .flow-container {
    display: flex;
    flex-direction: row;
  }
  :deep(.bpmn-containers) {
    width: 100%;
    height: 70vh;

    #flow-canvas {
      height: 100%;
      overflow: scroll;
    }

    img {
      display: none;
    }
  }
  .btn-line {
    display: flex;
    flex-direction: row;
    margin-top: 10px;

    .btn-item {
      margin-right: 10px;
    }
  }
  .top-tabs {
    .panel-list {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
    }
  }
  .info {
    width: 420px;
    display: flex;
    flex-wrap: wrap;

    > div {
      width: 50%;
    }
    .date-time {
      white-space: nowrap;
    }
    .status-wrap {
      > i {
        min-width: 62px;
        text-align: center;
        display: inline-block;
        height: 24px;
        line-height: 24px;
        border-radius: ~`getPrefixVar('border-radius-base')`;
        padding: 0 6px;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        background: rgb(234, 241, 250);
        color: rgb(42, 113, 202);
      }
    }
  }
  .pd-20 {
    padding: 20px;
  }
  .action-line {
    display: flex;
    flex-direction: row;
    justify-content: center;
    a {
      white-space: nowrap;
      margin-right: 6px;
    }
    span {
      margin: 0 6px;
      color: #eee;
    }
  }
  .copy-modal {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 10px;
    span {
      white-space: nowrap;
      margin-right: 10px;
    }
  }
</style>
