package com.chinasie.orion.strategy.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

public enum PersonDeepEnum {

    requiredRepairFinishCount("requiredRepairFinishCount",
            "  ( ((rop.newcomer is null or rop.act_in_date is null) and rop.is_base_permanent is true) or  (rop.is_base_permanent is not  true and (rop.plan_begin_time is null or rop.plan_end_time is null or rop.newcomer is null or (rop.act_in_date is null and now() < rop.plan_begin_time)))) " ),
    requiredPrepImplCount("requiredCount",
            "  rop.act_out_date is null  "),

    /**
     * 人员数量
     */
    personCount("personCount", ""),

    /**
     * 新人数量
     */
    newPersonCount("newPersonCount", " rop.newcomer is true"),

    /**
     * 计划入场时间未报备
     */
    noPlanIn("noPlanIn", " rop.plan_begin_time is null"),

    /**
     * 入场时间未报备
     */
    noActIn("noActIn", " (rop.act_in_date is null and  DATE(rop.plan_begin_time) < CURDATE()) "),

    /**
     * 实际入场人数
     */
    actInCount("actInCount", "rop.status = 1"),

    /**
     * 实际离场人数
     */
    actOutCount("actOutCount", "rop.status = 2"),

    /**
     * 实际离场未报备人数
     */
    actOutNotReportCount("actOutNotReportCount", " ( rop.plan_end_time is not null and  rop.act_out_date is null  and  DATE(rop.plan_end_time) < CURDATE()) ");

    private final String statisticFieldName;
    private final String description;

    PersonDeepEnum(String statisticFieldName, String description) {
        this.statisticFieldName = statisticFieldName;
        this.description = description;
    }

    public String getStatisticFieldName() {
        return statisticFieldName;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 获取枚举Map
     *
     * @return 字段名
     */
    public static Map<String, String> getFieldMap() {
        Map<String, String> fieldMap = new HashMap<>();
        for (PersonDeepEnum field : PersonDeepEnum.values()) {
            fieldMap.put(field.getStatisticFieldName(), field.getDescription());
        }
        return fieldMap;
    }
}
