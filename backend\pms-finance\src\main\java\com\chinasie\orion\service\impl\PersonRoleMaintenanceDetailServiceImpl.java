package com.chinasie.orion.service.impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.domain.dto.PersonRoleMaintenanceDetailDTO;
import com.chinasie.orion.domain.entity.PersonRoleMaintenanceDetail;
import com.chinasie.orion.domain.vo.PersonRoleMaintenanceDetailVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.PersonRoleMaintenanceDetailMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.PersonRoleMaintenanceDetailService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;




/**
 * <p>
 * PersonRoleMaintenanceDetail 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-09 20:19:13
 */
@Service
@Slf4j
public class PersonRoleMaintenanceDetailServiceImpl extends  OrionBaseServiceImpl<PersonRoleMaintenanceDetailMapper, PersonRoleMaintenanceDetail>   implements PersonRoleMaintenanceDetailService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public PersonRoleMaintenanceDetailVO detail(String id, String pageCode) throws Exception {
        PersonRoleMaintenanceDetail personRoleMaintenanceDetail =this.getById(id);
        PersonRoleMaintenanceDetailVO result = BeanCopyUtils.convertTo(personRoleMaintenanceDetail,PersonRoleMaintenanceDetailVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param personRoleMaintenanceDetailDTO
     */
    @Override
    public  String create(PersonRoleMaintenanceDetailDTO personRoleMaintenanceDetailDTO) throws Exception {
        PersonRoleMaintenanceDetail personRoleMaintenanceDetail =BeanCopyUtils.convertTo(personRoleMaintenanceDetailDTO,PersonRoleMaintenanceDetail::new);
        this.save(personRoleMaintenanceDetail);

        String rsp=personRoleMaintenanceDetail.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param personRoleMaintenanceDetailDTO
     */
    @Override
    public Boolean edit(PersonRoleMaintenanceDetailDTO personRoleMaintenanceDetailDTO) throws Exception {
        PersonRoleMaintenanceDetail personRoleMaintenanceDetail =BeanCopyUtils.convertTo(personRoleMaintenanceDetailDTO,PersonRoleMaintenanceDetail::new);

        this.updateById(personRoleMaintenanceDetail);

        String rsp=personRoleMaintenanceDetail.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<PersonRoleMaintenanceDetailVO> pages( Page<PersonRoleMaintenanceDetailDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<PersonRoleMaintenanceDetail> condition = new LambdaQueryWrapperX<>( PersonRoleMaintenanceDetail. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(PersonRoleMaintenanceDetail::getCreateTime);


        Page<PersonRoleMaintenanceDetail> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), PersonRoleMaintenanceDetail::new));

        PageResult<PersonRoleMaintenanceDetail> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<PersonRoleMaintenanceDetailVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<PersonRoleMaintenanceDetailVO> vos = BeanCopyUtils.convertListTo(page.getContent(), PersonRoleMaintenanceDetailVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "人员角色维护表人员明细导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", PersonRoleMaintenanceDetailDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        PersonRoleMaintenanceDetailExcelListener excelReadListener = new PersonRoleMaintenanceDetailExcelListener();
        EasyExcel.read(inputStream,PersonRoleMaintenanceDetailDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<PersonRoleMaintenanceDetailDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("人员角色维护表人员明细导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<PersonRoleMaintenanceDetail> personRoleMaintenanceDetailes =BeanCopyUtils.convertListTo(dtoS,PersonRoleMaintenanceDetail::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::PersonRoleMaintenanceDetail-import::id", importId, personRoleMaintenanceDetailes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<PersonRoleMaintenanceDetail> personRoleMaintenanceDetailes = (List<PersonRoleMaintenanceDetail>) orionJ2CacheService.get("pmsx::PersonRoleMaintenanceDetail-import::id", importId);
        log.info("人员角色维护表人员明细导入的入库数据={}", JSONUtil.toJsonStr(personRoleMaintenanceDetailes));

        this.saveBatch(personRoleMaintenanceDetailes);
        orionJ2CacheService.delete("pmsx::PersonRoleMaintenanceDetail-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::PersonRoleMaintenanceDetail-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<PersonRoleMaintenanceDetail> condition = new LambdaQueryWrapperX<>( PersonRoleMaintenanceDetail. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(PersonRoleMaintenanceDetail::getCreateTime);
        List<PersonRoleMaintenanceDetail> personRoleMaintenanceDetailes =   this.list(condition);

        List<PersonRoleMaintenanceDetailDTO> dtos = BeanCopyUtils.convertListTo(personRoleMaintenanceDetailes, PersonRoleMaintenanceDetailDTO::new);

        String fileName = "人员角色维护表人员明细数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", PersonRoleMaintenanceDetailDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<PersonRoleMaintenanceDetailVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class PersonRoleMaintenanceDetailExcelListener extends AnalysisEventListener<PersonRoleMaintenanceDetailDTO> {

        private final List<PersonRoleMaintenanceDetailDTO> data = new ArrayList<>();

        @Override
        public void invoke(PersonRoleMaintenanceDetailDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<PersonRoleMaintenanceDetailDTO> getData() {
            return data;
        }
    }


}
