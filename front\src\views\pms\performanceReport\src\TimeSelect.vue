<template>
  <Dropdown
    :trigger="['click']"
    :getPopupContainer="getMircoAppContainer"
  >
    <Select
      ref="selectRef"
      v-model:value="selectTime"
      :allowClear="true"
      :options="selectOptions"
      class="w1000"
      :open="false"
      placeholder="请选择季度"
      @change="selectChange"
    >
      <template #suffixIcon>
        <Icon icon="orion-icon-Field-time" />
      </template>
    </Select>
    <template #overlay>
      <div
        class="box"
        @click.stop=""
      >
        <div class="time-top">
          <Icon
            class="ml10 time-pointer"
            icon="orion-icon-doubleleft"
            @click.stop="getAdjacentYear(showTime,false)"
          />
          <div class="item-year">
            {{ showTime }}
          </div>
          <Icon
            class="mr10 time-pointer"
            icon="orion-icon-doubleright"
            @click.stop="getAdjacentYear(showTime,true)"
          />
        </div>
        <div class="time-center">
          <div class="flex mt10 time-jbc">
            <div class="ml10 time-pointer">
              <span
                :class="{'time-hover':true,'select-center':(selectTime===showTime+'-1'),'color-white':(selectTime===showTime+'-1')}"
                @click="open(true,'一')"
              >一季度</span>
            </div>
            <div class="mr10 time-pointer">
              <span
                :class="{'time-hover':true,'select-center':(selectTime===showTime+'-2'),'color-white':(selectTime===showTime+'-2')}"
                @click="open(true,'二')"
              >二季度</span>
            </div>
          </div>
          <div class="flex mt10 time-jbc">
            <div class="ml10 time-pointer">
              <span
                :class="{'time-hover':true,'select-center':(selectTime===showTime+'-3'),'color-white':(selectTime===showTime+'-3')}"
                @click="open(true,'三')"
              >三季度</span>
            </div>
            <div class="mr10 time-pointer">
              <span
                :class="{'time-hover':true,'select-center':(selectTime===showTime+'-4'),'color-white':(selectTime===showTime+'-4')}"
                @click="open(true,'四')"
              >四季度</span>
            </div>
          </div>
        </div>
        <div class=" footer-style">
          <div class="right-button">
            <div @click="open(true,'all')">
              全部
            </div>
          </div>
        </div>
      </div>
    </template>
  </Dropdown>
</template>

<script lang="ts" setup>
import { reactive, ref, watch } from 'vue';
import { Button, Select, Dropdown } from 'ant-design-vue';
import { getMircoAppContainer } from 'lyra-component-vue3';

const emits = defineEmits(['update:value']);
const props = defineProps({
  value: {
    type: String,
    default: '',
  },
});
const selectRef = ref();
const selectTime = ref();
const showTime = ref();
const selectOptions: any = ref([]);
showTime.value = new Date().getFullYear();
const state = reactive({
  close: true,
});
watch(() => props.value, () => {
  selectTime.value = props.value;
});

function open(boolean, n) {
  if (boolean) {
    if (n === 'all') {
      selectOptions.value.push({
        label: '全部',
        value: showTime.value,
      });
      selectTime.value = showTime.value;
    } else {
      selectOptions.value.push({
        label: `${showTime.value}年${n}季度`,
        value: `${showTime.value}-${getN(n)}`,
      });
      selectTime.value = `${showTime.value}-${getN(n)}`;
    }
  }
  emits('update:value', selectTime.value);
}

function getN(n: any) {
  return n === '一' ? 1 : n === '二' ? 2 : n === '三' ? 3 : n === '四' ? 4 : '';
}

/**
 * 获取某个年份的上一年或下一年
 * @param {number} year - 当前年份
 * @param {boolean} next - 如果为 true，获取下一年；如果为 false，获取上一年
 * @return {number} 返回计算后的年份
 */
function getAdjacentYear(year, next) {
  if (next) {
    showTime.value = year + 1;
  } else {
    showTime.value = year - 1;
  }
}

function selectChange(e) {
  if (!e) {
    emits('update:value', undefined);
  } else {
    emits('update:value', e);
  }
}
</script>

<style lang="less" scoped>
.w1000 {
  width: 100%;
  margin: 0 auto;
}

.box {
  position: relative;
  background-color: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.footer-style {
  position: absolute;
  bottom: 0;
  height: 30px;
  width: 100%;
}

.right-button {
  width: 100%;
  height: 30px;
  line-height: 30px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #e8e8e8;
  align-items: center;
  cursor: pointer;
  color: black
}

.time-top {
  border-bottom: 1px solid #e8e8e8;
  height: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.time-center {
  height: 95px;
}

.item-year {
  font-size: 14px;
  font-weight: 600;
  color: black;
}

.time-jbc {
  justify-content: space-around;
  color: black;
  align-items: center;

}

.time-hover {
  display: inline-block;
  border-radius: 4px;
  height: 25px;
  width: 50px;
  text-align: center;
  line-height: 25px;

  &:hover {
    background: #e7e6e6;
    color: black !important;
  }
}

.time-pointer {
  cursor: pointer;
}

.select-center {
  background: ~`getPrefixVar('primary-color')`;
}

.color-white {
  color: white !important;
}
</style>
