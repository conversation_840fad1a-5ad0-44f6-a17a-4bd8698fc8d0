package com.chinasie.orion.util;


import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.chinasie.orion.api.authority.AuthorityServiceApiV2;
import com.chinasie.orion.api.authority.domain.dto.DataDetailAuthorityParamsDTO;
import com.chinasie.orion.api.authority.domain.dto.HeadAuthorityParamsDTO;
import com.chinasie.orion.api.authority.domain.dto.RowDataAuthorityBizParamsDTO;
import com.chinasie.orion.api.authority.domain.dto.RowDataAuthorityParamsDTO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.domain.dto.PowerParams;
import com.chinasie.orion.sdk.domain.vo.PageButtonAuthorityVO;
import com.chinasie.orion.sdk.domain.vo.PageContainerAuthorityVO;
import com.chinasie.orion.sdk.domain.vo.RowDataOperationAuthorityVO;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class PlanAuthUtil {


    @Autowired
    private AuthorityServiceApiV2 authorityServiceApi;


    //设置列表权限
    public <T> void setRowAuths(String userId, PowerParams power, List<T> entities, Function<T, String> getId, Function<T, DataStatusVO> getDataStatusVO, BiConsumer<T, List<RowDataOperationAuthorityVO>> setRowAuth, Map<String, List<String>> dataRoleCodeMap) throws Exception {
        if (Objects.isNull(power)) {
            return;
        }

        if (StrUtil.isBlank(power.getContainerCode())) {
            return;
        }

        if (StrUtil.isBlank(power.getPageCode())) {
            return;
        }
        StopWatch watch = new StopWatch();

        watch.start();
        RowDataAuthorityParamsDTO rowDataAuthorityParamsDTO = setRowAuths(userId, power.getPageCode(), power.getContainerCode(), entities, getId, getDataStatusVO, dataRoleCodeMap);
        watch.stop();
        log.info("===耗时==[setRowAuths]耗时 {}=====", watch.getLastTaskTimeMillis());

        watch.start();
        ResponseDTO<Map<String, List<RowDataOperationAuthorityVO>>> rowDataAuthority = authorityServiceApi.getRowDataAuthority(rowDataAuthorityParamsDTO);
        watch.stop();
        log.info("===耗时==[authorityServiceApi.getRowDataAuthority]耗时 {}=====", watch.getLastTaskTimeMillis());

        log.info("行数据权限集成，用户={}，入参={}，出参={}", userId, JSONUtil.toJsonStr(rowDataAuthorityParamsDTO), JSONUtil.toJsonStr(rowDataAuthority));
        watch.start();
        Map<String, List<RowDataOperationAuthorityVO>> authMap = rowDataAuthority.getResult();
        entities.forEach(vo -> {
            List<RowDataOperationAuthorityVO> rowDataOperationAuthorityVOS = authMap.getOrDefault(getId.apply(vo), new ArrayList<>());
            setRowAuth.accept(vo, rowDataOperationAuthorityVOS);
        });
        watch.stop();
        log.info("===耗时==[list forEach]耗时 {}=====", watch.getLastTaskTimeMillis());
    }


    private <T> RowDataAuthorityParamsDTO setRowAuths(String userId, String pageCode, String containerCode, List<T> datas, Function<T, String> getId, Function<T, DataStatusVO> getDataStatusVO, Map<String, List<String>> dataRoleCodeMap) {
        RowDataAuthorityParamsDTO rowDataAuthorityParamsDTO = new RowDataAuthorityParamsDTO();
        rowDataAuthorityParamsDTO.setUserId(userId);
        rowDataAuthorityParamsDTO.setPageCode(pageCode);
        rowDataAuthorityParamsDTO.setContainerCode(containerCode);
        List<RowDataAuthorityBizParamsDTO> rowAuths = datas.stream().map(data -> {
            RowDataAuthorityBizParamsDTO entity = new RowDataAuthorityBizParamsDTO();
            entity.setDataId(getId.apply(data));
            entity.setDataStatus(getDataStatusVO.apply(data));
            entity.setRoleCodeList(dataRoleCodeMap.getOrDefault(getId.apply(data), new ArrayList<>()));

            //判断这个人的身份？
            //督办领导 supervisetheleadership_dbld
            //科室负责人 Headofdepartment_ksfzr
            //计划负责人（计划） Projectleader_jhfzr
            //签批领导 Endorsementlesder_qpld
            //部门负责人 Departmenthead_bmfzr
            //计划录入人 Planentryperson_jhlrr
            //班组负责人 Teamleader_bzfzr
            //领域主管 Fieldsupervisor_lyzg
            return entity;
        }).collect(Collectors.toList());
        rowDataAuthorityParamsDTO.setBizDataList(rowAuths);

        return rowDataAuthorityParamsDTO;
    }


    /**
     * 设置详情权限
     *
     * @param entity
     * @param userId
     * @param pageCode
     * @param dataStatusVO
     * @param setRowAuth
     * @param <T>
     * @throws Exception
     */
    public <T> void setDetailAuths(T entity, String userId, String pageCode, DataStatusVO dataStatusVO, BiConsumer<T, List<PageContainerAuthorityVO>> setRowAuth, List<String> roleCodeList) throws Exception {
        if (StrUtil.isBlank(pageCode)) {
            return;
        }
        DataDetailAuthorityParamsDTO dataDetailAuthorityParamsDTO = setDetailAuths(userId, pageCode, dataStatusVO, roleCodeList);
        ResponseDTO<List<PageContainerAuthorityVO>> dataDetailAuthority = authorityServiceApi.getDataDetailAuthority(dataDetailAuthorityParamsDTO);
        log.info("详情权限集成，用户={}，入参={}，出参={}", userId, JSONUtil.toJsonStr(dataDetailAuthorityParamsDTO), JSONUtil.toJsonStr(dataDetailAuthority));
        setRowAuth.accept(entity, dataDetailAuthority.getResult());
    }


    /**
     * 设置详情权限
     *
     * @param entity
     * @param userId
     * @param power
     * @param setHeaderAuth
     * @param <T>
     * @throws Exception
     */
    public <T> void setHeaderAuths(T entity, String userId, PowerParams power, BiConsumer<T, List<PageButtonAuthorityVO>> setHeaderAuth, List<String> roleCodeList) throws Exception {

        if (Objects.isNull(power)) {
            return;
        }
        if (StrUtil.isBlank(power.getHeadContainerCode())) {
            return;
        }

        if (StrUtil.isBlank(power.getPageCode())) {
            return;
        }

        HeadAuthorityParamsDTO headAuthorityParamsDTO = new HeadAuthorityParamsDTO();

        headAuthorityParamsDTO.setHeadContainerCode(power.getHeadContainerCode());
        headAuthorityParamsDTO.setPageCode(power.getPageCode());
        headAuthorityParamsDTO.setRoleCodeList(roleCodeList);
        headAuthorityParamsDTO.setUserId(userId);

        ResponseDTO<List<PageButtonAuthorityVO>> headAuthority = authorityServiceApi.getHeadAuthority(headAuthorityParamsDTO);

        log.info("详情权限集成，用户={}，入参={}，出参={}", userId, JSONUtil.toJsonStr(headAuthorityParamsDTO), JSONUtil.toJsonStr(headAuthority));
        setHeaderAuth.accept(entity, headAuthority.getResult());
    }


    private DataDetailAuthorityParamsDTO setDetailAuths(String userId, String pageCode, DataStatusVO dataStatusVO, List<String> roleCodeList) {
        DataDetailAuthorityParamsDTO dataDetailAuthorityParamsDTO = new DataDetailAuthorityParamsDTO();
        dataDetailAuthorityParamsDTO.setUserId(userId);
        dataDetailAuthorityParamsDTO.setPageCode(pageCode);
        dataDetailAuthorityParamsDTO.setDataStatus(dataStatusVO);
        dataDetailAuthorityParamsDTO.setRoleCodeList(roleCodeList);
        return dataDetailAuthorityParamsDTO;
    }


}
