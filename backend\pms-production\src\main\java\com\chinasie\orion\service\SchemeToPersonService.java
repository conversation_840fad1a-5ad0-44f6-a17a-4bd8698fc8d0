package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.SchemeToPersonDTO;
import com.chinasie.orion.domain.dto.scheme.RemoveRelationDTO;
import com.chinasie.orion.domain.dto.scheme.SchemeAuthorizeUserParamDTO;
import com.chinasie.orion.domain.dto.scheme.SchemePersonParamDTO;
import com.chinasie.orion.domain.entity.SchemeToPerson;
import com.chinasie.orion.domain.vo.ResultVO;
import com.chinasie.orion.domain.vo.SchemeToPersonVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;


/**
 * <p>
 * SchemeToPerson 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-15 20:21:36
 */
public interface SchemeToPersonService extends OrionBaseService<SchemeToPerson> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    SchemeToPersonVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param schemeToPersonDTO
     */
    String create(SchemeToPersonDTO schemeToPersonDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param schemeToPersonDTO
     */
    Boolean edit(SchemeToPersonDTO schemeToPersonDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<SchemeToPersonVO> pages(Page<SchemeToPersonDTO> pageRequest) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<SchemeToPersonVO> vos) throws Exception;

    /**
     *  获取大修下的人员列表
     * @param pageRequest
     * @return
     */
    Page<SchemeToPersonVO> personPages(Page<SchemeToPersonDTO> pageRequest);

    /**
     *  保存大修人员列表
     * @param schemeToPersonList
     * @param schemeId
     * @param repairRound
     */
    void saveListEntity(List<SchemeToPerson> schemeToPersonList, String schemeId, String repairRound);

    /**
     *  批量新增 人员列表
     * @param schemeAuthorizeUserParamDTO
     * @return ResultVO
     */
    ResultVO addPersonList(SchemeAuthorizeUserParamDTO schemeAuthorizeUserParamDTO) throws Exception;


    /**
     *  批量移除数据
     * @param personParamDTO
     * @return
     */
    Boolean removeBatchNew(SchemePersonParamDTO personParamDTO);

    Boolean removeBatchNewV2(RemoveRelationDTO removeRelationDTO);

    /**
     * 导出人员入场离场信息
     *
     * @param query    查询
     * @param response 导出到浏览器
     */
    void exportSchemePersonExcel(SchemeToPersonDTO query, HttpServletResponse response);


    /**
     *  人员计划导入 相关
     * @param response
     */
    void downloadExcelTplToPlan(String repairRound,HttpServletResponse response) throws Exception;

    ImportExcelCheckResultVO importCheckByExcelToPlan(String repairRound,MultipartFile file) throws IOException;

    Boolean importByExcelToPlan(String importId);

    /**
     * 查找入场人员
     * @param schemeToPersonDTO
     * @return
     */
    List<SchemeToPersonVO> getAdmissionList(SchemeToPersonDTO schemeToPersonDTO);
}
