package com.chinasie.orion.service;




import java.lang.String;
import java.util.List;


import com.chinasie.orion.domain.dto.CollaborativeCompilationDocumentDTO;
import com.chinasie.orion.domain.entity.CollaborativeCompilationDocument;
import com.chinasie.orion.domain.vo.CollaborativeCompilationDocumentVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import java.lang.String;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * CollaborativeCompilationDocument 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 17:10:25
 */
public interface CollaborativeCompilationDocumentService  extends  OrionBaseService<CollaborativeCompilationDocument>  {


        /**
         *  详情
         *
         * * @param id
         */
    CollaborativeCompilationDocumentVO detail(String id, String pageCode)throws Exception;

        /**
         *  新增
         *
         * * @param collaborativeCompilationDocumentDTO
         */
        String create(CollaborativeCompilationDocumentDTO collaborativeCompilationDocumentDTO)throws Exception;

        /**
         *  编辑
         *
         * * @param collaborativeCompilationDocumentDTO
         */
        Boolean edit(CollaborativeCompilationDocumentDTO collaborativeCompilationDocumentDTO)throws Exception;


        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;


        /**
         *  分页
         *
         * * @param pageRequest
         * 
         */
        Page<CollaborativeCompilationDocumentVO> pages( Page<CollaborativeCompilationDocumentDTO> pageRequest)throws Exception;

        List<CollaborativeCompilationDocumentVO> getTree( CollaborativeCompilationDocumentDTO collaborativeCompilationDocumentDTO)throws Exception;

        Boolean setTask(String id);
        /**
            *  设置各种名称
            *
            * * @param searchConditions
            * * @param response
            */
        void  setEveryName(List<CollaborativeCompilationDocumentVO> vos)throws Exception;
}
