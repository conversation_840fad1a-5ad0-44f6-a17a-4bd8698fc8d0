<script setup lang="ts">
import {
  BasicCard, BasicForm, useForm, InputSelectUser, BasicEditor, BasicTitle1,
} from 'lyra-component-vue3';
import {
  ref, Ref, h, inject, watchEffect,
} from 'vue';
import { get, map } from 'lodash-es';

const detailsData = inject('detailsData');
const personInCharge: Ref<{ id: string, name: string }[]> = ref([]);
const actionVerifier: Ref<{ id: string, name: string }[]> = ref([]);
const [
  register,
  {
    setFieldsValue, validate, validateFields, getFieldsValue,
  },
] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas: [
    {
      field: 'BasicTitle11',
      component: 'Input',
      label: '',
      colProps: {
        span: 24,
      },
      render() {
        return h(BasicTitle1, { title: '行动项信息' });
      },
    },
    {
      field: 'repairRound',
      component: 'Input',
      label: '大修轮次',
      required: true,
      colProps: {
        span: 6,
      },
      componentProps: {
        disabled: true,
        placeholder: '请选择大修轮次',
      },
    },
    {
      field: 'dimensionDict',
      component: 'Select',
      label: '维度',
      rules: [
        {
          required: true,
        },
      ],
      colProps: {
        span: 6,
      },
      componentProps: {
        placeholder: '请选择维度',
        dictNumber: 'pms_action_dimension',
        disabled: true,
      },
    },
    {
      field: 'rspUserNames',
      component: 'Input',
      label: '责任人（可多选）',
      colProps: {
        span: 12,
      },
      rules: [
        {
          required: true,
        },
      ],
      componentProps: {
        disabled: true,
        placeholder: '',
      },
    },
    {
      field: 'rspDeptNames',
      component: 'Input',
      label: '责任部门',
      colProps: {
        span: 12,
      },
      componentProps: {
        disabled: true,
        placeholder: '',
      },
    },
    {
      field: 'finishDeadline',
      component: 'DatePicker',
      label: '完成时限（截止日期）',
      colProps: {
        span: 6,
      },
      required: true,
      componentProps: {
        disabled: true,
        placeholder: '请选择完成时限',
        valueFormat: 'YYYY-MM-DD',
        style: {
          width: '100%',
        },
      },
    },
    {
      field: 'verifierId',
      component: 'Select',
      label: '行动验证人',
      colProps: {
        span: 6,
      },
      rules: [
        {
          required: true,
        },
      ],
      componentProps: {
        disabled: true,
        placeholder: '',
      },
    },
    {
      field: 'problemDesc',
      component: 'InputTextArea',
      label: '问题描述',
      colProps: {
        span: 24,
      },
      rules: [
        {
          required: true,
        },
      ],
      componentProps: {
        disabled: true,
        placeholder: '请填写问题描述',
        rows: 4,
        showCount: true,
        maxlength: 1000,
      },
    },
    {
      field: 'conferenceSummary',
      component: 'InputTextArea',
      label: '改进措施：',
      rules: [
        {
          required: true,
        },
      ],
      colProps: {
        span: 24,
      },
      slot: 'improvementMeasure',
    },
  ],
});

watchEffect(() => {
  setFieldsValue({
    repairRound: get(detailsData, 'repairRound'),
    dimensionDict: get(detailsData, 'dimensionDictName'),
    problemDesc: get(detailsData, 'problemDesc'),
    rspUserNames: get(detailsData, 'rspUserNames'),
    rspDeptNames: get(detailsData, 'rspDeptNames'),
    updateSolutions: get(detailsData, 'updateSolutions'),
    finishDeadline: get(detailsData, 'finishDeadline'),
    verifierId: get(detailsData, 'verifierName'),
  });
  personInCharge.value = map(get(detailsData, 'rspUserIds'), (user, idx) => {
    const nameList = get(detailsData, 'rspUserNames', '').split(',');
    return {
      id: user,
      name: get(nameList, idx, ''),
    };
  });
  actionVerifier.value = [
    {
      name: get(detailsData, 'verifierName'),
      id: get(detailsData, 'verifierId'),
    },
  ];
});
</script>

<template>
  <BasicForm @register="register">
    <template #improvementMeasure>
      <div
        class="improvement-measure"
      >
        <div
          class="im-inner"
          v-html="detailsData?.updateSolutions"
        />
      </div>
    </template>
  </BasicForm>
</template>

<style scoped lang="less">
 :deep(.ant-form-item){
   margin-bottom: 6px !important;
 }
 .improvement-measure{
   height: 200px;
   background-color: #f5f5f5;
   border:1px solid #d9d9d9;
   box-shadow: none;
   color: #333;
   padding: 12px;
   overflow-y: auto;
   line-height: 24px;
 }
</style>