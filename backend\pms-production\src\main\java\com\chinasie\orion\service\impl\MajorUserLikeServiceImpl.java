package com.chinasie.orion.service.impl;





import com.chinasie.orion.constant.JobManageStatusEnum;
import com.chinasie.orion.domain.dto.job.MajorUserLikeParamDTO;
import com.chinasie.orion.domain.entity.MajorRepairPlan;
import com.chinasie.orion.domain.entity.MajorUserLike;
import com.chinasie.orion.domain.dto.MajorUserLikeDTO;
import com.chinasie.orion.domain.vo.MajorRepairPlanVO;
import com.chinasie.orion.domain.vo.MajorUserLikeVO;


import com.chinasie.orion.mybatis.domain.entity.LyraEntity;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.service.MajorRepairPlanService;
import com.chinasie.orion.service.MajorUserLikeService;
import com.chinasie.orion.repository.MajorUserLikeMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * MajorUserLike 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-17 16:07:04
 */
@Service
@Slf4j
public class MajorUserLikeServiceImpl extends  OrionBaseServiceImpl<MajorUserLikeMapper, MajorUserLike>   implements MajorUserLikeService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private MajorRepairPlanService majorRepairPlanService;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  MajorUserLikeVO detail(String id,String pageCode) throws Exception {
        MajorUserLike majorUserLike =this.getById(id);
        MajorUserLikeVO result = BeanCopyUtils.convertTo(majorUserLike,MajorUserLikeVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param majorUserLikeDTO
     */
    @Override
    public  String create(MajorUserLikeDTO majorUserLikeDTO) throws Exception {
        MajorUserLike majorUserLike =BeanCopyUtils.convertTo(majorUserLikeDTO,MajorUserLike::new);
        this.save(majorUserLike);

        String rsp=majorUserLike.getId();



        return rsp;
    }


    /**
     *  编辑
     *
     * * @param majorUserLikeDTO
     */
    @Override
    public Boolean edit(MajorUserLikeDTO majorUserLikeDTO) throws Exception {
        MajorUserLike majorUserLike =BeanCopyUtils.convertTo(majorUserLikeDTO,MajorUserLike::new);
        this.updateById(majorUserLike);
        String rsp=majorUserLike.getId();


        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<MajorUserLikeVO> pages( Page<MajorUserLikeDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<MajorUserLike> condition = new LambdaQueryWrapperX<>( MajorUserLike. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(MajorUserLike::getCreateTime);


        Page<MajorUserLike> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), MajorUserLike::new));

        PageResult<MajorUserLike> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<MajorUserLikeVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<MajorUserLikeVO> vos = BeanCopyUtils.convertListTo(page.getContent(), MajorUserLikeVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public void  setEveryName(List<MajorUserLikeVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }

    @Override
    public List<MajorUserLikeVO> majorInfoList(JobManageStatusEnum statusEnum) {
        String userId = CurrentUserHelper.getCurrentUserId();
        // 搜寻自己关注的数据 状态过滤
        LambdaQueryWrapperX<MajorUserLike> wrapperX = new LambdaQueryWrapperX<>(MajorUserLike.class);
        wrapperX.eq(MajorRepairPlan::getStatus,statusEnum.getStatus());
        wrapperX.eq(MajorUserLike::getUserId,userId);
        wrapperX.leftJoin(MajorRepairPlan.class, MajorRepairPlan::getRepairRound, MajorUserLike::getRepairRound);
        wrapperX.select(MajorUserLike::getId,MajorUserLike::getRepairRound,MajorUserLike::getUserId,MajorUserLike::getSort);
        List<MajorUserLike> majorUserLikes = this.getBaseMapper().selectList(wrapperX);
        if(CollectionUtils.isEmpty(majorUserLikes)){
            // 获取大修数据前三的
            List<MajorUserLikeVO> list  =new ArrayList<>();
            LambdaQueryWrapperX<MajorRepairPlan> lambdaQueryWrapperX =new LambdaQueryWrapperX<>(MajorRepairPlan.class);
            lambdaQueryWrapperX.orderByDesc(MajorRepairPlan::getCreateTime);
            lambdaQueryWrapperX.eq(MajorRepairPlan::getStatus,statusEnum.getStatus());
            lambdaQueryWrapperX.select(MajorRepairPlan::getRepairRound);
            lambdaQueryWrapperX.last(" limit 3");
            List<MajorRepairPlan> majorRepairPlanList= majorRepairPlanService.list(lambdaQueryWrapperX);
            if(!CollectionUtils.isEmpty(majorRepairPlanList)){
                List<MajorUserLikeVO> majorUserLikeVOList =new ArrayList<>();
                for (MajorRepairPlan majorRepairPlan : majorRepairPlanList) {
                    MajorUserLikeVO majorUserLikeVO = new MajorUserLikeVO();
                    majorUserLikeVO.setRepairRound(majorRepairPlan.getRepairRound());
                    majorUserLikeVOList.add(majorUserLikeVO);
                }
                return majorUserLikeVOList;
            }
            return  new ArrayList<>();
        }
        List<MajorUserLikeVO> majorUserLikeVOList= BeanCopyUtils.convertListTo(majorUserLikes,MajorUserLikeVO::new);
        majorUserLikeVOList.sort(Comparator.comparing(item->{
            if(Objects.isNull(item.getSort())){
                return  0;
            }
            return  item.getSort();
        }));
        return majorUserLikeVOList;
    }

    @Override
    public Boolean addBatch(List<MajorUserLikeParamDTO> majorUserLikeDTOList, JobManageStatusEnum statusEnum) {
        List<String> repairRoundList = majorUserLikeDTOList.stream().map(MajorUserLikeParamDTO::getRepairRound).distinct().collect(Collectors.toList());
        String userId = CurrentUserHelper.getCurrentUserId();
        LambdaQueryWrapperX<MajorUserLike> wrapperX = new LambdaQueryWrapperX<>(MajorUserLike.class);
        wrapperX.eq(MajorRepairPlan::getStatus,statusEnum.getStatus());
        wrapperX.eq(MajorUserLike::getUserId,userId);
        wrapperX.leftJoin(MajorRepairPlan.class, MajorRepairPlan::getRepairRound, MajorUserLike::getRepairRound);
        wrapperX.select(MajorUserLike::getId,MajorUserLike::getRepairRound,MajorUserLike::getUserId);
        List<MajorUserLike> majorUserLikes= this.list(wrapperX);
        if(CollectionUtils.isEmpty(repairRoundList)){
            if(CollectionUtils.isEmpty(majorUserLikes)){
                return Boolean.TRUE;
            }
            List<String> idList=   majorUserLikes.stream().map(LyraEntity::getId).distinct().collect(Collectors.toList());
            return  this.removeBatchByIds(idList);
        }
        if(CollectionUtils.isEmpty(majorUserLikes)){
            this.saveBatchList(userId,majorUserLikeDTOList);
        }else{
            this.removeBatchByIds(majorUserLikes.stream().map(LyraEntity::getId).distinct().collect(Collectors.toList()));
            this.saveBatchList(userId,majorUserLikeDTOList);
        }
        return Boolean.TRUE;
    }

    @Override
    public List<MajorRepairPlanVO> majorList(JobManageStatusEnum statusEnum) {
        LambdaQueryWrapperX<MajorRepairPlan> lambdaQueryWrapperX =new LambdaQueryWrapperX<>(MajorRepairPlan.class);
        lambdaQueryWrapperX.orderByDesc(MajorRepairPlan::getCreateTime);
        lambdaQueryWrapperX.eq(MajorRepairPlan::getStatus,statusEnum.getStatus());
        lambdaQueryWrapperX.select(MajorRepairPlan::getRepairRound,MajorRepairPlan::getName);
        List<MajorRepairPlan> majorRepairPlanList= majorRepairPlanService.list(lambdaQueryWrapperX);
        if(CollectionUtils.isEmpty(majorRepairPlanList)){
            return  new ArrayList<>();
        }
        return BeanCopyUtils.convertListTo(majorRepairPlanList,MajorRepairPlanVO::new);
    }

    public void saveBatchList(String userId,List<MajorUserLikeParamDTO> majorUserLikeParamDTOS){
        List<MajorUserLike> majorUserLikeList = new ArrayList<>();
        for (MajorUserLikeParamDTO entity : majorUserLikeParamDTOS) {
            MajorUserLike majorUserLike = new MajorUserLike();
            majorUserLike.setUserId(userId);
            majorUserLike.setRepairRound(entity.getRepairRound());
            majorUserLike.setSort(entity.getSort());
            majorUserLikeList.add(majorUserLike);
        }
        this.saveBatch(majorUserLikeList);
    }
}
