package com.chinasie.orion.domain.vo;

import com.chinasie.orion.domain.dto.pas.TypeAttrValueDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/1/18 9:33
 */
@Data
@ApiModel(value = "RiskManagementVO对象", description = "风险管理")
public class RiskManagementVO extends ObjectVO {
    /**
     * 预计完成时间
     */
    @ApiModelProperty(value = "预计完成时间")
    private Date predictEndTime;

    /**
     *
     */
    private String id;

    /**
     * 应对策略
     */
    @ApiModelProperty(value = "应对策略ID")
    private String copingStrategy;
    @ApiModelProperty(value = "应对策略名称")
    private String copingStrategyName;

    /**
     * 风险类型
     */
    @ApiModelProperty(value = "风险类型ID")
    private String riskType;
    @ApiModelProperty(value = "风险类型名称")
    private String riskTypeName;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 应对措施
     */
    @ApiModelProperty(value = "应对措施")
    private String solutions;

    /**
     * 风险影响
     */
    @ApiModelProperty(value = "风险影响ID")
    private String riskInfluence;
    @ApiModelProperty(value = "风险影响名称")
    private String riskInfluenceName;

    /**
     * 负责人ID
     */
    @ApiModelProperty(value = "负责人ID")
    private String principalId;
    @ApiModelProperty(value = "负责人名称")
    private String principalName;

    /**
     * 风险概率
     */
    @ApiModelProperty(value = "风险概率ID")
    private String riskProbability;
    @ApiModelProperty(value = "风险概率名称")
    private String riskProbabilityName;

    /**
     * 预期发生时间
     */
    @ApiModelProperty(value = "预期发生时间ID")
    private String predictStartTime;
    @ApiModelProperty(value = "预期发生时间名称")
    private String predictStartTimeName;

    /**
     * 识别人
     */
    @ApiModelProperty(value = "识别人ID")
    private String discernPerson;
    @ApiModelProperty(value = "识别人名称")
    private String discernPersonName;


    @ApiModelProperty("属性值")
    private List<TypeAttrValueDTO> typeAttrValueDTOList;

    @ApiModelProperty(value = "是否需要审批")
    private Boolean isNeedApproval;

    @ApiModelProperty(value = "是否需要提醒")
    private Boolean isNeedReminder;

    @ApiModelProperty(value = "是否典型风险")
    private Boolean isTypicalRisk;

    @ApiModelProperty(value = "是否转问题")
    private Boolean isToQuestion;

}
