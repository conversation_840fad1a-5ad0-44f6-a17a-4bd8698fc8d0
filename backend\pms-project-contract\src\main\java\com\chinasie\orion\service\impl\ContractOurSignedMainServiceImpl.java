package com.chinasie.orion.service.impl;

import com.chinasie.orion.domain.entity.ContractOurSignedMain;
import com.chinasie.orion.domain.vo.ContractOurSignedMainVO;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.ContractOurSignedMainRepository;
import com.chinasie.orion.service.ContractOurSignedMainService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * ContractOurSignedMain 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24 10:10:42
 */
@Service
public class ContractOurSignedMainServiceImpl extends OrionBaseServiceImpl<ContractOurSignedMainRepository, ContractOurSignedMain> implements ContractOurSignedMainService {

    @Autowired
    private ContractOurSignedMainRepository contractOurSignedMainRepository;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public ContractOurSignedMainVO detail(String id) throws Exception {
        ContractOurSignedMain contractOurSignedMain = contractOurSignedMainRepository.selectById(id);
        ContractOurSignedMainVO result = BeanCopyUtils.convertTo(contractOurSignedMain, ContractOurSignedMainVO::new);
        return result;
    }

    /**
     * 根据合同id获取详情
     *
     * @param contractId
     * @return
     * @throws Exception
     */
    @Override
    public ContractOurSignedMainVO detailByContractId(String contractId) throws Exception {
        ContractOurSignedMain contractOurSignedMain = contractOurSignedMainRepository.selectOne(ContractOurSignedMain::getContractId, contractId);
        ContractOurSignedMainVO result = BeanCopyUtils.convertTo(contractOurSignedMain, ContractOurSignedMainVO::new);
        return result;
    }

}
