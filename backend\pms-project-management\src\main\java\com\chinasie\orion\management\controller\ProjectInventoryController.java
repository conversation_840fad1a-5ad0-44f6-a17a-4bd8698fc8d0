package com.chinasie.orion.management.controller;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.management.domain.dto.ProjectInventoryDTO;
import com.chinasie.orion.management.domain.dto.ProjectOrderDTO;
import com.chinasie.orion.management.domain.vo.ProjectInventoryVO;
import com.chinasie.orion.management.domain.vo.ProjectOrderVO;
import com.chinasie.orion.management.service.ProjectInventoryService;
import com.chinasie.orion.management.service.ProjectOrderService;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * ProjectInventory 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-31 14:19:58
 */
@RestController
@RequestMapping("/projectInventory")
@Api(tags = "商品清单")
public class ProjectInventoryController {

    @Autowired
    private ProjectInventoryService projectInventoryService;

    @Autowired
    private ProjectOrderService projectOrderService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "项目立项商品清单", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectInventoryVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        ProjectInventoryVO rsp = projectInventoryService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 根据编号查询详情
     *
     * @param projectOrderDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "根据编号查询商品清单")
    @RequestMapping(value = "/getList", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】根据编号查询商品清单", type = "项目立项商品清单", subType = "根据编号查询商品清单", bizNo = "{{#projectOrderDTO.orderNumber}}")
    public ResponseDTO<Map<String, Object>> getList(@RequestBody ProjectOrderDTO projectOrderDTO) throws Exception {
        Map<String, Object> map = new HashMap<>();
        List<ProjectInventoryVO> inventoryVOS = projectInventoryService.getList(projectOrderDTO.getOrderNumber());
        double total = inventoryVOS.stream().filter(vo -> vo.getAmount() != null && vo.getQuantity() != null)
                .mapToDouble(vo -> vo.getAmount().doubleValue() * vo.getQuantity().doubleValue())
                .sum();
        ProjectOrderVO orderVO = projectOrderService.getByNumber(projectOrderDTO.getOrderNumber(), projectOrderDTO.getPageCode());
        map.put("list", inventoryVOS);
        map.put("total", total);
        map.put("surcharge", orderVO.getOrderSurcharge() == null ? BigDecimal.ZERO : orderVO.getOrderSurcharge());
        map.put("allTotal", (total + (orderVO.getOrderSurcharge() == null ? 0 : orderVO.getOrderSurcharge().doubleValue())));

        return new ResponseDTO<>(map);
    }


    /**
     * 新增
     *
     * @param projectInventoryDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#projectInventoryDTO.name}}】", type = "项目立项商品清单", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody ProjectInventoryDTO projectInventoryDTO) throws Exception {
        String rsp = projectInventoryService.create(projectInventoryDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectInventoryDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#projectInventoryDTO.name}}】", type = "项目立项商品清单", subType = "编辑", bizNo = "{{#projectInventoryDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody ProjectInventoryDTO projectInventoryDTO) throws Exception {
        Boolean rsp = projectInventoryService.edit(projectInventoryDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "项目立项商品清单", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = projectInventoryService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "项目立项商品清单", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectInventoryService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "项目立项商品清单", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ProjectInventoryVO>> pages(@RequestBody Page<ProjectInventoryDTO> pageRequest) throws Exception {
        Page<ProjectInventoryVO> rsp = projectInventoryService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

}
