package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.IncomeAccountConfirmDTO;
import com.chinasie.orion.domain.vo.IncomeAccountConfirmVO;
import com.chinasie.orion.service.IncomeAccountConfirmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;


import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * IncomeAccountConfirm 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18 21:16:26
 */
@RestController
@RequestMapping("/incomeAccountConfirm")
@Api(tags = "收入记账明细确认表")
public class  IncomeAccountConfirmController  {

    @Autowired
    private IncomeAccountConfirmService incomeAccountConfirmService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据【{{#id}}】", type = "收入记账明细确认表", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<IncomeAccountConfirmVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        IncomeAccountConfirmVO rsp = incomeAccountConfirmService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param incomeAccountConfirmDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#incomeAccountConfirmDTO.name}}】", type = "收入记账明细确认表", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody IncomeAccountConfirmDTO incomeAccountConfirmDTO) throws Exception {
        String rsp =  incomeAccountConfirmService.create(incomeAccountConfirmDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param incomeAccountConfirmDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#incomeAccountConfirmDTO.name}}】", type = "收入记账明细确认表", subType = "编辑", bizNo = "{{#incomeAccountConfirmDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody IncomeAccountConfirmDTO incomeAccountConfirmDTO) throws Exception {
        Boolean rsp = incomeAccountConfirmService.edit(incomeAccountConfirmDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "收入记账明细确认表", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = incomeAccountConfirmService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "收入记账明细确认表", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = incomeAccountConfirmService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "收入记账明细确认表", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<IncomeAccountConfirmVO>> pages(@RequestBody Page<IncomeAccountConfirmDTO> pageRequest) throws Exception {
        Page<IncomeAccountConfirmVO> rsp =  incomeAccountConfirmService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("收入记账明细确认表导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "收入记账明细确认表", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        incomeAccountConfirmService.downloadExcelTpl(response);
    }

    @ApiOperation("收入记账明细确认表导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "收入记账明细确认表", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = incomeAccountConfirmService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("收入记账明细确认表导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "收入记账明细确认表", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  incomeAccountConfirmService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消收入记账明细确认表导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "收入记账明细确认表", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  incomeAccountConfirmService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("收入记账明细确认表导出（Excel）")
    @PostMapping(value = "/export/excel")
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "收入记账明细确认表", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody IncomeAccountConfirmDTO incomeAccountConfirmDTO, HttpServletResponse response) throws Exception {
        incomeAccountConfirmService.exportByExcel(incomeAccountConfirmDTO, response);
    }

    @ApiOperation(value = "数据确认")
    @RequestMapping(value = "/dataConfirm", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】数据确认【{{#incomeAccountConfirmDTO.name}}】", type = "收入记账明细确认表", subType = "数据确认", bizNo = "")
    public ResponseDTO<Boolean> dataConfirm(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = incomeAccountConfirmService.dataConfirm(ids);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "批量修改")
    @RequestMapping(value = "/editBatch", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量修改数据【{{#incomeAccountConfirmDTO.name}}】", type = "收入记账明细确认表", subType = "批量修改", bizNo = "")
    public ResponseDTO<Boolean> editBatch(@RequestBody IncomeAccountConfirmDTO incomeAccountConfirmDTO) throws Exception {
        Boolean rsp = incomeAccountConfirmService.editBatch(incomeAccountConfirmDTO);
        return new ResponseDTO<>(rsp);
    }

}
