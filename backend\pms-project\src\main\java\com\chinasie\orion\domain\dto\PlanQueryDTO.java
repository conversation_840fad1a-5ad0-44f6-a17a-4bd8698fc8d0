package com.chinasie.orion.domain.dto;

import com.chinasie.orion.page.SearchCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/9/22 10:03
 */
@Data
public class PlanQueryDTO implements Serializable {

    private List<List<SearchCondition>> searchConditions;
    
    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyword;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private String priorityLevel;

    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    private List<Long> planEndTime;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    private List<Long> planStartTime;

    /**
     * 计划类型Id
     */
    @ApiModelProperty(value = "计划类型Id")
    private String planType;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 计划id
     */
    @ApiModelProperty(value = "计划id")
    List<String> ids;

    @ApiModelProperty(value = "负责人ID")
    private String principalId;

}
