package com.chinasie.orion.constant;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/10/16:05
 * @description:
 */
public enum ProjectRoleEnum {

    ROLE_PL("XM_LEADER", "XM_LEADER", "项目领导"),
    ROLE_XMJL("PM", "XMJL001", "项目经理"),
    ROLE_PD("P162", "33381", "项目总监"),
    PRODUCT_MANGER("product_manger", "", "产品经理"),
    SALE_PRODUCT("sale_manger", "", "销售经理"),
    ;

    private String code;
    private String number;
    private String name;

    ProjectRoleEnum(String code, String number, String name) {
        this.code = code;
        this.number = number;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getNumber() {
        return number;
    }

    public String getName() {
        return name;
    }

}