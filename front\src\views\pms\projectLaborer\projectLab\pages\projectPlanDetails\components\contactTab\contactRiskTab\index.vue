<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
    @buttonClick="tableButtonClick"
  >
    <template #toolbarLeft>
      <BasicButton
        v-if="isPower('PMS_OJJHXQ_container_02_02_button_01',powerData)"
        type="primary"
        icon="add"
        @click="() => addSystemRoleHandle('add')"
      >
        添加
      </BasicButton>
    </template>
    <template #createTime="{ text }">
      {{ text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '' }}
    </template>
    <template #schedule="{ text }">
      <Progress
        :percent="text"
        size="small"
        style="padding-right: 40px"
      />
    </template>

    <template #statusName="{ record }">
      <span
        :style="{
          color: record.statusName == '未完成' ? 'red' : ''
        }"
      >
        {{ record.statusName }}
      </span>
    </template>
  </OrionTable>
</template>
<script lang="ts">
import {
  defineComponent, onMounted, inject, h, ref,
} from 'vue';
import {
  Layout, OrionTable, BasicButton, DataStatusTag, isPower, openModal,
} from 'lyra-component-vue3';
import { Modal } from 'ant-design-vue';
/* 格式化时间 */
import { formatterTime } from '/@/views/pms/projectLaborer/utils/time';
import router from '/@/router';
import dayjs from 'dayjs';
import Api from '/@/api';
import AssociationRiskModal from './modal/AssociationRiskModal.vue';
export default defineComponent({
  name: 'ProductLibraryIndex',
  components: {
    BasicButton,
    OrionTable,
  },
  props: {
    formId: {
      type: String,
      default: '',
    },
  },

  setup(props) {
    const powerData = inject('powerData', []);
    const tableOptions = {
      deleteToolButton: `'add|enable|disable'${isPower('PMS_OJJHXQ_container_02_02_button_02', powerData) ? '' : '|delete'}`,
      rowSelection: {},
      pagination: false,
      showSmallSearch: false,
      resizeHeightOffset: 60,
      api() {
        return new Api(`/pms/projectScheme/relation/risk/lists/${props.formId}`).fetch('', '', 'POST');
      },
      columns: [
        {
          title: '编号',
          dataIndex: 'number',
          ellipsis: true,
          width: 200,
        },
        {
          title: '名称',
          dataIndex: 'name',
          align: 'left',
          minWidth: 220,
          customRender({ record, text }) {
            return h('span', {
              onClick: () => {
                openModal.closeAll();
                router.push({
                  name: 'PMSRiskManagementDetails',
                  params: {
                    id: record.id,
                  },
                });
              },
              class: 'action-btn',
            }, text);
          },
        },
        {
          title: '风险类型',
          dataIndex: 'riskTypeName',
          key: 'riskType',
          width: '80px',
          margin: '0 20px 0 0',
          align: 'left',
          slots: { customRender: 'riskTypeName' },
          ellipsis: true,
        },
        {
          title: '发生概率',
          dataIndex: 'riskProbabilityName',
          key: 'riskProbabilityName',
          width: '100px',
          align: 'left',
          slots: { customRender: 'riskProbabilityName' },
          ellipsis: true,
        },
        {
          title: '影响程度',
          dataIndex: 'riskInfluenceName',
          key: 'riskInfluenceName',

          width: '120px',
          align: 'left',
          slots: { customRender: 'riskInfluenceName' },
          ellipsis: true,
        },
        {
          title: '预估发生时间',
          dataIndex: 'predictStartTimeName',
          key: 'predictStartTimeName',
          width: '120px',
          align: 'left',
          slots: { customRender: 'predictStartTimeName' },
          // sorter: true,
          ellipsis: true,
        },
        {
          title: '应对策略',
          dataIndex: 'copingStrategyName',
          key: 'copingStrategyName',

          width: '120px',
          align: 'left',
          ellipsis: true,
          slots: { customRender: 'copingStrategyName' },
        },
        {
          title: '状态',
          dataIndex: 'dataStatus',
          key: 'dataStatus',

          width: '120px',
          align: 'left',
          // sorter: true,
          ellipsis: true,
          customRender: ({ record, text }) => h(DataStatusTag, { statusData: record.dataStatus }),
        },
        {
          title: '负责人',
          dataIndex: 'principalName',
          key: 'principalName',

          width: '120px',
          align: 'left',
          // sorter: true,
          ellipsis: true,
          slots: { customRender: 'principalName' },
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          key: 'createTime',

          width: '170px',
          align: 'left',
          // sorter: true,
          ellipsis: true,
          slots: { customRender: 'createTime' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          slots: { customRender: 'action' },
          width: 120,
          fixed: 'right',
        },
      ],
      actions: [
        {
          event: 'delete',
          text: '删除',
          isShow: isPower('PMS_OJJHXQ_container_02_02_button_02', powerData),
          modal(record) {
            const deleteParams = {
              toId: props.formId,
              fromIds: [record.id],
            };
            const love = {
            };
            return new Api('/pms').fetch(deleteParams, 'projectScheme/relation/risk/remove', 'DELETE');
          },
        },
      ],
    };
    const tableRef = ref(null);
    let projectId: any = inject('projectId');
    const addSystemRoleHandle = () => {
      const quoteRiskPoolRef = ref();
      openModal({
        title: '关联风险',
        width: 1100,
        height: 700,
        content(h) {
          return h(AssociationRiskModal, {
            ref: quoteRiskPoolRef,
            formId: props.formId,
            projectId: projectId.value,
          });
        },
        async onOk() {
          await quoteRiskPoolRef.value.saveData();
          tableRef.value.reload();
        },
      });
    };
    onMounted(() => {
      //  getFormData();
    });
    /* 批量删除 */
    const multiDelete = (keys) => {
      Modal.confirm({
        title: '删除确认提示？',
        content: '请确认是否对当前选中数据进行删除？',
        onOk() {
          const deleteParams = {
            toId: props.formId,
            fromIds: keys,
          };
          return new Api('/pms').fetch(deleteParams, 'projectScheme/relation/risk/remove', 'DELETE').then(() => {
            tableRef.value.reload();
          });
        },
        onCancel() {
          Modal.destroyAll();
        },
      });
    };
    function tableButtonClick(params) {
      if (params && params.type === 'delete') {
        multiDelete(params.selectColumns.keys);
      }
    }
    return {
      formatterTime,
      dayjs,
      addSystemRoleHandle,
      tableOptions,
      tableRef,
      tableButtonClick,
      powerData,
      isPower,
    };
  },

  // mounted() {}
});
</script>
<style scoped lang="less">
</style>
