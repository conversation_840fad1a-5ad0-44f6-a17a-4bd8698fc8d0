package com.chinasie.orion.service.review;


import com.chinasie.orion.domain.dto.review.ReviewLibraryDTO;
import com.chinasie.orion.domain.entity.review.ReviewLibrary;
import com.chinasie.orion.domain.vo.review.ReviewLibraryVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

/**
 * <p>
 * ReviewLibrary 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:03
 */
public interface ReviewLibraryService extends OrionBaseService<ReviewLibrary> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    ReviewLibraryVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param reviewLibraryDTO
     */
    String create(ReviewLibraryDTO reviewLibraryDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param reviewLibraryDTO
     */
    Boolean edit(ReviewLibraryDTO reviewLibraryDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ReviewLibraryVO> pages(Page<ReviewLibraryDTO> pageRequest) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<ReviewLibraryVO> vos) throws Exception;

    List<ReviewLibraryVO> getList();

}
