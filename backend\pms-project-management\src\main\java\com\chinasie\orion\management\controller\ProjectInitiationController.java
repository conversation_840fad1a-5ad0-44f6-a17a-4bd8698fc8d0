package com.chinasie.orion.management.controller;

import com.chinasie.orion.domain.request.quality.QualityStepVO;
import com.chinasie.orion.domain.vo.MarketContractVO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.feign.PasFeignService;
import com.chinasie.orion.management.domain.dto.ProjectInitiationDTO;
import com.chinasie.orion.management.domain.vo.ProjectInitiationVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.management.service.ProjectInitiationService;
import com.chinasie.orion.service.MarketContractService;
import com.chinasie.orion.util.CollectionUtils;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * <p>
 * ProjectInitiation 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-29 17:48:53
 */
@RestController
@RequestMapping("/projectInitiation")
@Api(tags = "项目立项")
public class ProjectInitiationController {

    @Autowired
    private ProjectInitiationService projectInitiationService;
    @Autowired
    private MarketContractService marketContractService;

    @Autowired
    private PasFeignService feignService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据【{{#projectInitiationDTO.name}}】", type = "项目立项", subType = "详情", bizNo = "{{#projectInitiationDTO.id}}")
    public ResponseDTO<Map<String, Object>> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        Map<String, Object> map = new HashMap<>();
        ProjectInitiationVO rsp = projectInitiationService.detail(id, pageCode);
        //根据合同编号(修订)查询合同
        String contractNumbers = rsp.getContractNumbers();//修订的
        String originalNumber = rsp.getOriginalNumber();
        List<MarketContractVO> marketContractVOS = new ArrayList<>();
        if (contractNumbers != null && !contractNumbers.isEmpty()) {
            List<String> contractNumberList = Arrays.asList(contractNumbers.split(","));
            //调用合同接口
            marketContractVOS = marketContractService.listByNumber(contractNumberList);
            //如果marketContractVOS为空，则将原合同号展示
            if (marketContractVOS.isEmpty()) {
                MarketContractVO marketContractVO = new MarketContractVO();
                marketContractVO.setOriginalNumber(originalNumber);
                marketContractVO.setNumber(contractNumbers);
                marketContractVOS.add(marketContractVO);
            } else {
                for (MarketContractVO marketContractVO : marketContractVOS) {
                    marketContractVO.setOriginalNumber(originalNumber);
                    marketContractVO.setContractNumbers(contractNumbers);
                }
            }

        }

        //根据线索编号查线索
        String clueNumbers = rsp.getClueNumbers();
        ResponseDTO<List<Map<String, Object>>> clues = new ResponseDTO<>();
        if (clueNumbers != null && !clueNumbers.isEmpty()) {
            List<String> clueList = Arrays.asList(clueNumbers.split(","));
            //feign调用pas接口
            clues = feignService.listByClueIds(clueList);
        }
        map.put("projectInitiation", rsp);
        map.put("contract", marketContractVOS);
        map.put("clue", clues);
        return new ResponseDTO<>(map);
    }

    /**
     * 编辑
     *
     * @param projectInitiationDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#projectInitiationDTO.name}}】", type = "项目立项", subType = "编辑", bizNo = "{{#projectInitiationDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody ProjectInitiationDTO projectInitiationDTO) throws Exception {
        Boolean rsp = projectInitiationService.edit(projectInitiationDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页预测线索查询")
    @RequestMapping(value = "/getPredictedClues", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】分页预测线索查询", type = "项目立项", subType = "分页预测线索查询", bizNo = "")
    public ResponseDTO<Page<Map<String, Object>>> getPredictedClues(@RequestBody Page<Map<String, Object>> pageRequest) throws Exception {
        ResponseDTO<Page<Map<String, Object>>> rsp;
        try {
            rsp = feignService.getCluePredictedPages(pageRequest);
            rsp.getResult().getContent().stream().filter(p -> p.get("status").toString().equals("101")).forEach(x -> x.put("status", "编制中"));
            rsp.getResult().getContent().stream().filter(p -> p.get("status").toString().equals("110")).forEach(x -> x.put("status", "流程中"));
            rsp.getResult().getContent().stream().filter(p -> p.get("status").toString().equals("111")).forEach(x -> x.put("status", "已审批"));
        } catch (Exception e) {
            throw new BaseException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR, "线索数据失败");
        }
        return rsp;
    }


    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "项目立项", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ProjectInitiationVO>> pages(@RequestBody Page<ProjectInitiationDTO> pageRequest) throws Exception {
        Page<ProjectInitiationVO> rsp = projectInitiationService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "项目立项", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/project/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ProjectInitiationVO>> projectPages(@RequestBody Page<ProjectInitiationDTO> pageRequest) throws Exception {
        Page<ProjectInitiationVO> rsp = projectInitiationService.pagePages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "保存项目关联关系")
    @LogRecord(success = "【{USER{#logUserId}}】保存项目关联关系", type = "项目立项", subType = "保存项目关联关系", bizNo = "")
    @RequestMapping(value = "/project/save", method = RequestMethod.POST)
    public ResponseDTO<Boolean> projectSave(@RequestBody List<ProjectInitiationDTO> projectInitiationDTOs) throws Exception {
        Boolean rsp = projectInitiationService.projectSave(projectInitiationDTOs);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页查询线索")
    @RequestMapping(value = "/getClues", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】分页查询线索", type = "项目立项", subType = "分页查询线索", bizNo = "")
    public ResponseDTO<Page<Map<String, Object>>> getClues(@RequestBody Page<Map<String, Object>> pageRequest) throws Exception {
        ResponseDTO<Page<Map<String, Object>>> rsp;
        try {
            rsp = feignService.getCluePages(pageRequest);
            rsp.getResult().getContent().stream().filter(p -> p.get("status").toString().equals("101")).forEach(x -> x.put("status", "编制中"));
            rsp.getResult().getContent().stream().filter(p -> p.get("status").toString().equals("110")).forEach(x -> x.put("status", "流程中"));
            rsp.getResult().getContent().stream().filter(p -> p.get("status").toString().equals("111")).forEach(x -> x.put("status", "已审批"));
        } catch (Exception e) {
            throw new BaseException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR, "线索数据失败");
        }
        return rsp;
    }


    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "权限-分页")
    @LogRecord(success = "【{USER{#logUserId}}】权限-分页", type = "项目立项", subType = "权限-分页", bizNo = "")
    @RequestMapping(value = "/permission/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ProjectInitiationVO>> permissionPages(@RequestBody Page<ProjectInitiationDTO> pageRequest) throws Exception {
        Page<ProjectInitiationVO> rsp = projectInitiationService.permissionPages(pageRequest);
        return new ResponseDTO<>(rsp);
    }
}
