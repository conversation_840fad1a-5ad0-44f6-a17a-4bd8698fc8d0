<template>
  <OrionTable
    ref="tableRef"
    class="pdmBasicTable"
    :options="tableOptions"
  >
    <template #toolbarLeft>
      <BasicButton
        type="primary"
        icon="add"
        @click="clickType('add')"
      >
        新增问题
      </BasicButton>
      <BasicButton
        type="primary"
        icon="add"
        @click="openModalAssociated(true,{})"
      >
        关联问题
      </BasicButton>
      <BasicButton
        icon="delete"
        @click="clickType('delete')"
      >
        移除
      </BasicButton>
    </template>
    <template #modifyTime="{ text }">
      {{ text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '' }}
    </template>
    <template #proposedTime="{ text }">
      {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
    </template>
    <template #predictEndTime="{ text }">
      {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
    </template>
    <template #seriousLevelName="{ text }">
      <span :style="{ color: text === '严重' ? 'red' : '' }">{{ text }}</span>
    </template>
    <template #priorityLevelName="{ text }">
      <span :style="{ color: text === '最高' ? 'red' : '' }">{{ text }}</span>
    </template>
    <template #statusName="{ text }">
      <span
        :style="{ color: text === '未完成' ? '#ccc' : text == '已处理' ? 'green' : 'blue' }"
      >{{ text }}</span>
    </template>

    <template #action="{record}">
      <BasicTableAction :actions="actionsBtn(record)" />
    </template>
  </OrionTable>
  <!-- 查看详情弹窗 -->
  <checkDetails :data="nodeData" />

  <!--新增问题-->
  <AddTableNode
    :isRisk="true"
    @register="register"
    @update="addSuccess"
  />
  <ContactAssociatedModal
    @register="registerAssociated"
    @check-project-callback="checkProjectCallback"
  />
  <!-- 新建/编辑抽屉 -->
  <addProjectModal
    :data="addNodeModalData"
    :list-data="editdataSource"
    :projectid="id"
    @success="successSave"
  />

  <ResolvedDrawer @register="ResolvedDrawerRegister" />
  <!-- 从系统添加 -->
  <AddSystemRole
    :id="id"
    :data="addSystemModalData"
    @success="successSave"
  />
  <SearchModal
    @register="searchRegister"
    @searchEmit="searchEmit"
  />
</template>
<script lang="ts">
import {
  computed, defineComponent, h, inject, onMounted, reactive, Ref, toRefs,
} from 'vue';
import {
  BasicButton,
  BasicTableAction,
  DataStatusTag,
  isPower,
  ITableActionItem,
  OrionTable,
  useDrawer,
  useModal,
  useTable,
} from 'lyra-component-vue3';
import { message, Modal } from 'ant-design-vue';
import { formatterTime } from '/@/views/pms/projectLaborer/utils/time';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import addProjectModal from './modal/addProjectModal.vue';
import AddSystemRole from './modal/addSystemRole.vue';
import checkDetails from './modal/checkmodal.vue';
import {
  StatusEnum,
} from '/@/views/pms/projectLaborer/projectLab/pages/projectPlanDetails/questionManagement/questionManagementDetails/DetailsTab/enum';
import Api from '/@/api';
import ContactAssociatedModal from './modal/ContactAssociatedModal.vue';
import AddTableNode
  from '/@/views/pms/projectLaborer/projectLab/pages/projectPlanDetails/questionManagement/model/AddTableNode.vue';
import {
  ResolvedDrawer,
} from '/@/views/pms/projectLaborer/projectLab/pages/projectPlanDetails/questionManagement/questionManagementDetails/DetailsTab/drawer';

const [registerTable, { setLoading }] = useTable();
export default defineComponent({
  name: 'ProductLibraryIndex',
  components: {
    ResolvedDrawer,
    BasicTableAction,
    BasicButton,
    //   提示图标
    //   addNodeModal,
    checkDetails,
    /* 新建项目抽屉 */
    addProjectModal,
    AddSystemRole,
    OrionTable,
    ContactAssociatedModal,
    AddTableNode,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
  },

  setup(props) {
    const formData = inject('formData', {});
    const router = useRouter();
    const [register, { openDrawer }] = useDrawer();
    const [searchRegister, { openDrawer: openSearchDrawer }] = useDrawer();
    const [registerAssociated, { openModal: openModalAssociated }] = useModal();
    const [ResolvedDrawerRegister, { openDrawer: resolvedDrawerMethods }] = useDrawer();
    const state = reactive({
      tableRef: null,
      /* 搜索框value */
      searchvlaue: '',
      /* 编辑send */
      editdataSource: {},
      /* 多选 */
      selectedRowKeys: [],
      /* 列 */
      dataSource: [],
      tablehttp: {
        orders: [
          {
            asc: false,
            column: '',
          },
        ],

        query: {
          projectId: '',
        },
        // 条数
        pageSize: 10,
        /* 页数 */
        pageNum: 1,
        /* 总数 */
        total: 0,
        queryCondition: [],
      },
      // 条数
      pageSize: 10,
      /* 页数 */
      current: 1,
      /* 总数 */
      total: 20,
      addNodeModalData: {},
      /* 选择行id */
      selectedRows: [],
      addSystemModalData: {},

      showVisible: false,
      /* 简易弹窗提醒消息 */
      message: '',
      nodeData: {},
      searchData: {},
      params: {},
      /* 高度 */
      tableHeight: 400,
    });
    const powerData:Ref = inject('powerData');

    /* 多选cb */
    const onSelectChange = (selectedRowKeys, selectedRows) => {
      state.selectedRowKeys = selectedRowKeys;
      state.selectedRows = selectedRows;
      // console.log('测试🚀🚀 ~~~ state.selectedRows', state.selectedRows);
    };
    /* 页数变化cb */
    const handleChange = (pag, filters, sorter: any) => {
      // 如果是多选触发,则不更新页面
      if (typeof pag.current === 'undefined') return;
      state.tablehttp.pageNum = pag.current;
      state.tablehttp.pageSize = pag.pageSize;
      state.tablehttp.orders[0].asc = sorter.order === 'ascend';
      state.tablehttp.orders[0].column = sorter.columnKey;

      state.tableRef.reload();
    };
    /* 右按钮 */
    const clickType = (type) => {
      switch (type) {
        case 'edit':
          // editNode();
          break;
        case 'check':
          checkData();
          break;
        case 'add':
          openDrawer(true, {
            type: 'add',

            data: {
              fromObjName: formData.value.projectName,
            },
          });
          break;
        case 'open':
          // openDetail();
          openQuestion();
          break;
        case 'delete':
          // deleteNode();
          multiDelete();
          break;
        case 'search':
          // state.searchData = {};
          openSearchDrawer(true);
          break;
      }
    };
    let projectId: any = inject('projectId');
    const openQuestion = () => {
      if (lengthCheckHandle()) return;

      router.push({
        name: 'QuestionManagementDetails',
        query: {
          itemId: state.selectedRowKeys[0],
          projectId: projectId.value,
          type: 0,
        },
      });
    };
    const addSystemRoleHandle = () => {
      // console.log('从系统创建角色');
      state.addSystemModalData = { formType: 'add' };
    };
    /* 编辑 */
    const editNode = () => {
      if (lengthCheckHandle()) return;
      // state.selectedRows = [];
      state.addNodeModalData = { formType: 'edit' };
      state.editdataSource = [...state.selectedRows];
    };
    /* 删除 */
    /* 简易弹窗的确定cb */
    const confirm = () => {
      // 删除操作
      deletrow();
    };
    onMounted(() => {
      /* 高度变化 */
      state.tableHeight = document.body.clientHeight - 440;

      state.tableRef.reload();
    });
    let riskItemId: any = inject('riskItemId');

    /* 删除操作 */
    const deletrow = () => {
      const newArr = {
        fromId: riskItemId.value,
        toIdList: state.selectedRowKeys,
      };
      new Api('/pas/risk-management/relation/question/batch').fetch(newArr, '', 'DELETE')
        .then((res) => {
          message.success('删除成功');
          state.tableRef.reload();
        })
        .catch(() => {
        });
    };

    function searchEmit(data) {
      state.tableRef.reload();
    }

    /* 查看详情 */
    const checkData = () => {
      if (lengthCheckHandle()) return;
      state.nodeData = {
        ...state.dataSource.filter((item) => item.id === state.selectedRowKeys[0]),
      };
    };

    /* 检查选择条数fn */
    const lengthCheckHandle = () => {
      if (state.selectedRows.length > 1) {
        message.warning('请选择一条数据进行操作');
        return true;
      }
      if (state.selectedRows.length === 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };
    /* 批量检查选择条数fn */
    const multiLengthCheckHandle = () => {
      if (state.selectedRows.length === 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };
    const searchTable = (params) => {
      state.tablehttp.query = params.params;
      state.tablehttp.queryCondition = params.queryCondition;

      state.tableRef.reload();
      state.searchvlaue = '';
    };

    /* 新建项目 */
    const addNode = () => {
      state.addNodeModalData = {
        formType: 'add',
      };
    };
    /* 批量删除 */
    const multiDelete = () => {
      if (multiLengthCheckHandle()) return;
      Modal.confirm({
        title: '请确认是否对当前选中数据进行移除?',
        onOk() {
          return new Api(`/pas/riskRelationQuestion/relationQuestion/${riskItemId.value}`).fetch(state.selectedRowKeys, '', 'DELETE')
            .then((res) => {
              message.success('移除成功');
              state.tableRef.reload();
            });
        },
      });
    };
    /* 搜索右上 */
    const onSearch = () => {
      /* gettable */
      state.tablehttp.queryCondition = <any>[
        {
          column: 'name',
          type: 'like',
          link: 'or',
          value: state.searchvlaue,
        },
        {
          column: 'number',
          type: 'like',
          link: 'or',
          value: state.searchvlaue,
        },
      ];
      state.tablehttp.query = { projectId: '' };
      state.tableRef.reload();
    };
    /* 新建项目成功回调 */
    const successSave = () => {
      state.tablehttp.pageNum = 1;
      state.selectedRowKeys = [];
      state.selectedRows = [];

      state.tableRef.reload();
      state.searchvlaue = '';
      onSearch();
    };
    const clickRow = (record, index) => {
      const num = state.selectedRowKeys.findIndex((item) => item === record.id);
      num === -1 ? state.selectedRowKeys.push(record.id) : state.selectedRowKeys.splice(num, 1);
      const row = state.selectedRows.findIndex((item) => item.id === record.id);
      row === -1 ? state.selectedRows.push(record) : state.selectedRows.splice(row, 1);
    };

    const tableOptions = {
      showIndexColumn: false,
      pagination: true,
      bordered: false,
      deleteToolButton: 'add|delete|enable|disable',
      rowClick: clickRow,
      rowSelection: {
        selectedRowKeys: computed(() => state.selectedRowKeys),
        onChange: onSelectChange,
      },
      api(params) {
        return new Api('/pas/riskRelationQuestion/relationQuestion/page').fetch({
          ...params,
          query: {
            riskId: riskItemId.value,
          },
        }, '', 'POST');
      },
      columns: [
        {
          title: '编号',
          dataIndex: 'number',
          align: 'left',
          key: 'number',

          width: '120px',
          // sorter: true,
          ellipsis: true,
        },
        {
          title: '名称',
          dataIndex: 'name',
          key: 'name',
          customRender({ record, text }) {
            return h(
              'span',
              {
                class: 'action-btn',
                // class: computed(()=>isPower('FX_container_button_08', state.powerData)) ?'action-btn':'',
                title: text,
                onClick(e) {
                  router.push({
                    name: 'QuestionManagementDetails',
                    query: {
                      itemId: record.id,
                    },
                  });
                },
              },
              text,
            );
          },

          align: 'left',
          // slots: { customRender: 'name' },
          // sorter: true,
          ellipsis: true,
        },
        {
          title: '问题内容',
          dataIndex: 'content',
          key: 'content',

          align: 'left',
          // sorter: true,
          ellipsis: true,
        },
        {
          title: '状态',
          dataIndex: 'dataStatus',
          key: 'dataStatus',
          width: '80px',
          align: 'left',
          // sorter: true,
          ellipsis: true,
          customRender: ({ record, text }) => h(DataStatusTag, { statusData: record.dataStatus }),
        },
        {
          title: '严重程度',
          dataIndex: 'seriousLevelName',
          key: 'seriousLevelName',
          align: 'left',
          // sorter: true,
          ellipsis: true,
        },
        {
          title: '问题来源',
          dataIndex: 'questionSourceName',
          key: 'questionSourceName',
          align: 'left',
          // sorter: true,
          ellipsis: true,
        },
        {
          title: '问题类型',
          dataIndex: 'questionTypeName',
          key: 'questionTypeName',
          align: 'left',
          // sorter: true,
          ellipsis: true,
        },

        {
          title: '负责人',
          dataIndex: 'principalName',
          key: 'principalName',
          align: 'left',
          // sorter: true,
          ellipsis: true,
          slots: { customRender: 'principalName' },
        },
        {
          title: '期望完成日期',
          dataIndex: 'predictEndTime',
          key: 'predictEndTime',
          align: 'left',
          customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : ''),
          ellipsis: true,
        },

        {
          title: '提出人',
          dataIndex: 'exhibitorName',
          key: 'exhibitorName',
          width: '70px',
          align: 'left',
          // sorter: true,
          ellipsis: true,
        },
        {
          title: '提出日期',
          dataIndex: 'proposedTime',
          key: 'proposedTime',

          width: '100px',
          align: 'left',
          // sorter: true,
          ellipsis: true,
          slots: { customRender: 'proposedTime' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 150,
          align: 'left',
          fixed: 'right',
          slots: { customRender: 'action' },
        },
      ],
    };
    const state6 = reactive({
      btnList: [
        // { type: 'add' },
        { type: 'check' },
        { type: 'open' },
        {
          type: 'edit',
          powerCode: 'WTGL_container_button_02',
        },
        {
          type: 'delete',
          powerCode: 'WTGL_container_button_03',
        },
        {
          type: 'agree',
          name: '解决',
          powerCode: 'WTGL_container_button_05',
        },
        {
          type: 'cancel',
          name: '关闭',
          powerCode: 'WTGL_container_button_06',
        },
        {
          type: 'start',
          name: '激活',
          powerCode: 'WTGL_container_button_04',
        },
        // { type: 'oddTask' },
        // { type: 'evenTask' },
        { type: 'search' },
      ],
    });
    const actionsBtn = (record) => {
      const actions: ITableActionItem<any>[] = [
        {

          isShow: record.isCurrentCreate && computed(() => state6.btnList.some((item) => item.type === 'edit')),
          text: '编辑',
          onClick() {
            openDrawer(true, {
              type: 'edit',
              data: { id: record.id },
            });
          },
        },
        {
          text: '关闭',
          isShow() {
            return record.isCurrentCreate && record?.status === 130 && computed(() => state6.btnList.some((item) => item.type === 'cancel'));
          },
          onClick() {
            resolvedDrawerMethods(true, {
              questionId: record.id,
              editStatus: StatusEnum.CLOSE.status,
              title: '关闭',
              onSuccess() {
                state.tableRef.reload({ page: 1 });
              },
            });
          },
        },
        {
          text: '解决',
          isShow() {
            return record.isCurrentCreate && record?.status === 101 && computed(() => state6.btnList.some((item) => item.type === 'agree'));
          },
          onClick() {
            resolvedDrawerMethods(true, {
              questionId: record.id,
              editStatus: StatusEnum.COMPLETED.status,
              title: '解决',
              onSuccess() {
                state.tableRef.reload({ page: 1 });
              },
            });
          },
        },
        {
          text: '删除',
          isShow: () => record.isCurrentCreate && isPower('PAS_FXGLXQ_container_02_02_button_03', powerData.value),
          modal() {
            return new Api(`/pas/riskRelationQuestion/relationQuestion/deleteQuestion/${riskItemId.value}`).fetch({
              questionId: record.id,
            }, '', 'DELETE').then(() => {
              message.success('删除成功');
              state.tableRef.reload();
            });
          },
        },
        {
          text: '激活',
          isShow() {
            return record.isCurrentCreate && (record?.status === 102 || record?.status === 103) && computed(() => state6.btnList.some((item) => item.type === 'start'));
          },
          onClick() {
            resolvedDrawerMethods(true, {
              questionId: record.id,
              editStatus: StatusEnum.INCOMPLETE.status,
              title: '激活',
              onSuccess() {
                state.tableRef.reload();
              },
            });
          },
        },
        {
          text: '移除',
          modal() {
            return new Api(`/pas/riskRelationQuestion/relationQuestion/${riskItemId.value}`).fetch([record.id], '', 'DELETE')
              .then((res) => {
                message.success('移除成功');
                state.tableRef.reload();
              });
          },
        },
      ];
      return actions;
    };
    function addSuccess() {
      let pageSize = state.tableRef.getPaginationRef().pageSize;
      state.tableRef.setPagination({
        current: 1,
        pageSize,
      });
      state.tableRef.reload();
    }
    const checkProjectCallback = (data) => {
      new Api(`/pas/riskRelationQuestion/relationQuestion/${riskItemId.value}`).fetch(
        data.map((item) => item.id),
        '',
        'POST',
      )
        .then((res) => {
          message.success('关联成功');
          state.tableRef.reload();
        });
    };
    return {
      ...toRefs(state),
      clickRow,
      clickType,
      onSelectChange,
      handleChange,
      formatterTime,
      confirm,
      addNode,
      dayjs,
      multiDelete,
      onSearch,
      successSave,
      searchTable,
      addSystemRoleHandle,
      registerTable,
      setLoading,
      searchRegister,
      searchEmit,
      tableOptions,
      actionsBtn,
      powerData,
      registerAssociated,
      ResolvedDrawerRegister,
      ...toRefs(state6),
      openModalAssociated,
      register,
      openDrawer,
      addSuccess,
      checkProjectCallback,
      resolvedDrawerMethods,
    };
  },
  methods: { isPower },
});
</script>
<style lang="less" scoped>
</style>
