package com.chinasie.orion.domain.vo.approval;

import com.chinasie.orion.util.TreeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.math.BigDecimal;
import java.lang.String;
import java.lang.Integer;

import java.util.List;

/**
 * ProjectApprovalEstimateMaterial VO对象
 *
 * <AUTHOR>
 * @since 2024-05-07 10:45:22
 */
@ApiModel(value = "ProjectApprovalEstimateMaterialVO对象", description = "概算物料")
@Data
public class ProjectApprovalEstimateMaterialVO extends ObjectVO implements TreeUtils.TreeNode<String, ProjectApprovalEstimateMaterialVO> {
        /**
         * 物料数量
         */
        @ApiModelProperty(value = "物料数量")
        private Integer materialAmount;


        /**
         * 物料价格
         */
        @ApiModelProperty(value = "物料价格")
        private BigDecimal materialPrice;


        /**
         * 父级id
         */
        @ApiModelProperty(value = "父级id")
        private String parentId;


        /**
         * 需求数量
         */
        @ApiModelProperty(value = "需求数量")
        private Integer requiredNum;


        /**
         * 材料概算
         */
        @ApiModelProperty(value = "材料概算")
        private BigDecimal amount;

        /**
         * 物料id
         */
        @ApiModelProperty(value = "物料id")
        private String materialId;

        /**
         * 项目立项id
         */
        @ApiModelProperty(value = "项目立项id")
        private String projectApprovalId;

        /**
         * 物料结构名称
         */
        @ApiModelProperty(value = "物料结构名称")
        private String name;

        /**
         * 编号
         */
        @ApiModelProperty(value = "编号")
        private String number;


        /**
         * 移动平均价格
         */
        @ApiModelProperty(value = "移动平均价格")
        private BigDecimal averagePrice;


        /**
         * 价格单位
         */
        @ApiModelProperty(value = "价格单位")
        private String priceUnit;


        /**
         * 子项
         */
        @ApiModelProperty(value = "子项")
        private List<ProjectApprovalEstimateMaterialVO> children;
}
