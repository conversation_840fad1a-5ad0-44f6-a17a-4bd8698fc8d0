<script setup lang="ts">
import { BasicButton, Icon } from 'lyra-component-vue3';
import {
  Checkbox, CheckboxGroup, message, Popover,
} from 'ant-design-vue';
import {
  computed, ref, Ref, watch,
} from 'vue';
import Api from '/@/api';
import { cloneDeep } from 'lodash-es';
import draggable from 'vuedraggable';

const props = defineProps<{
  statusEnum: 'IMPL' | 'PREPARE'
  selectedKeys: any[]
  options: string[]
  getPopupContainer: Function
}>();

const emits = defineEmits<{
  (e: 'update'): void
}>();

const loading: Ref<boolean> = ref(false);
const visible: Ref<boolean> = ref(false);
const checkedList: Ref<string[]> = ref([]);
const unCheckedList = computed(() => props.options.filter((item) => !checkedList.value.includes(item)));

watch(() => props.selectedKeys, (value) => {
  checkedList.value = value;
});

async function handleSubmit() {
  loading.value = true;
  const params = cloneDeep(checkedList.value)?.map((item, index) => ({
    sort: index,
    repairRound: item,
  }));
  try {
    await new Api(`/pms/majorUserLike/add/batch?statusEnum=${props.statusEnum}`).fetch(params, '', 'POST');
    message.success('操作成功');
    emits('update');
  } finally {
    loading.value = false;
    visible.value = false;
  }
}

function handleSetting() {
  visible.value = !visible.value;
}
</script>

<template>
  <Popover
    v-model:visible="visible"
    trigger="click"
    :getPopupContainer="getPopupContainer"
    placement="bottomRight"
  >
    <template #content>
      <CheckboxGroup
        v-model:value="checkedList"
      >
        <draggable
          :list="checkedList"
          :force-fallback="true"
          animation="300"
        >
          <template #item="{ element }">
            <div class="move-item">
              <Icon
                class="mr10"
                icon="orion-icon-drag"
                size="16"
              />
              <Checkbox
                :value="element"
              >
                {{ element }}
              </Checkbox>
            </div>
          </template>
        </draggable>
        <div
          v-for="item in unCheckedList"
          :key="item"
          class="unchecked-item"
        >
          <Checkbox
            :value="item"
          >
            {{ item }}
          </Checkbox>
        </div>
      </CheckboxGroup>
      <div
        v-if="options.length"
        class="flex flex-pac mt10"
      >
        <BasicButton
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          保存
        </BasicButton>
      </div>
    </template>
    <div
      class="icon-button"
      @click="handleSetting"
    >
      <Icon icon="orion-icon-setting" />
    </div>
  </Popover>
</template>

<style scoped lang="less">
.icon-button {
  background-color: #EEEEEE;
  width: 32px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  cursor: pointer;
}

.grid-wrap {
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  gap: 5px 0;
}

.ant-checkbox-wrapper + .ant-checkbox-wrapper {
  margin-left: 0;
}

.move-item {
  user-select: none;
  cursor: move;
  height: 30px;
  display: flex;
  align-items: center;
}

.unchecked-item {
  height: 30px;
  display: flex;
  align-items: center;
}
</style>
