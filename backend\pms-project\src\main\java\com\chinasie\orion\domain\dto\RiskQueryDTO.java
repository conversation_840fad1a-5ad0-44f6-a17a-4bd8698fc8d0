package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/9/22 10:48
 */
@Data
public class RiskQueryDTO implements Serializable {

    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyword;

    /**
     * 风险类型
     */
    @ApiModelProperty(value = "风险类型")
    private String riskType;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 风险概率
     */
    @ApiModelProperty(value = "风险概率")
    private String riskProbability;

    /**
     * 影响程度
     */
    @ApiModelProperty(value = "影响程度")
    private String riskInfluence;

    /**
     * 预期发生时间
     */
    @ApiModelProperty(value = "预期发生时间")
    private String predictStartTime;

    /**
     * 应对策略
     */
    @ApiModelProperty(value = "应对策略")
    private String copingStrategy;

}
