package com.chinasie.orion.service.impl;





import com.chinasie.orion.domain.entity.ProjectSchemeFallback;
import com.chinasie.orion.repository.ProjectSchemeFallbackRepository;
import com.chinasie.orion.service.ProjectSchemeFallbackService;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;




/**
 * <p>
 * SchemeFallback 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16 13:50:11
 */
@Service
@Slf4j
public class ProjectSchemeFallbackServiceImpl extends OrionBaseServiceImpl<ProjectSchemeFallbackRepository, ProjectSchemeFallback> implements ProjectSchemeFallbackService {


}
