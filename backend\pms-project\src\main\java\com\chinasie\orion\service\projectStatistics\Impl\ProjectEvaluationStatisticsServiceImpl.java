package com.chinasie.orion.service.projectStatistics.Impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.chinasie.orion.api.holidays.HolidaysApi;
import com.chinasie.orion.constant.ProjectSchemeNodeTypeEnum;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.pas.api.service.WorkHoursReportDetailForUserService;
import com.chinasie.orion.service.*;
import cn.hutool.core.collection.CollectionUtil;
import com.chinasie.orion.api.holidays.domain.dto.CalculationWorkdayNumDTO;
import com.chinasie.orion.api.holidays.domain.vo.CalculationWorkdayNumVO;
import com.chinasie.orion.constant.question.QuestionDictConstant;
import com.chinasie.orion.domain.entity.QuestionManagement;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.service.projectStatistics.ProjectEvaluationStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;
import java.util.Objects;

@Service
public class ProjectEvaluationStatisticsServiceImpl implements ProjectEvaluationStatisticsService {


    @Autowired
    private BudgetExpendFormService budgetExpendFormService;

    @Autowired
    private BudgetManagementService budgetManagementService;

    @Autowired
    private ProjectSchemeService projectSchemeService;

    @Autowired
    private WorkHoursReportDetailForUserService workHoursReportDetailForUserService;

    @Autowired
    private WorkHourEstimateService workHourEstimateService;

    @Autowired
    private QuestionManagementService questionManagementService;

    @Autowired
    private HolidaysApi holidaysApi;

    @Override
    public float getMilestoneScore(String projectId) throws Exception {
        List<ProjectScheme> projectSchemeList = projectSchemeService.list(new LambdaQueryWrapperX<>(ProjectScheme.class)
                .select(ProjectScheme::getId, ProjectScheme::getCircumstance, ProjectScheme::getActualEndTime, ProjectScheme::getEndTime)
                .eq(ProjectScheme::getProjectId, projectId)
                .eq(ProjectScheme::getNodeType, ProjectSchemeNodeTypeEnum.MILESTONE.getValue()));
        BigDecimal allNum = new BigDecimal(projectSchemeList.size());
        if (allNum.compareTo(BigDecimal.ZERO) == 0) {
            return 0;
        }
        List<ProjectScheme> completeNormalProjectSchemeList = projectSchemeList.stream()
                .filter(f -> Status.CIRCUMSTANCE_COMPLETE_NORMAL.getCode().equals(f.getCircumstance())).collect(Collectors.toList());
        BigDecimal completeNormalNum = new BigDecimal(completeNormalProjectSchemeList.size());
        long completeBeforeNum = completeNormalProjectSchemeList.stream().filter(f -> {
            if (ObjectUtil.isNotEmpty(f.getActualEndTime()) && ObjectUtil.isNotEmpty(f.getEndTime())) {
                long between = DateUtil.between(f.getActualBeginTime(), f.getEndTime(), DateUnit.DAY, false);
                if (between > 7) {
                    return true;
                }
            }
            return false;
        }).count();
        BigDecimal result = completeNormalNum.divide(allNum, 2, RoundingMode.HALF_UP).multiply(new BigDecimal("130"))
                .add((new BigDecimal("10").multiply(new BigDecimal(completeBeforeNum)))).setScale(2, RoundingMode.HALF_UP);
        if (result.compareTo(new BigDecimal("180")) > 0) {
            return 180;
        }
        return result.floatValue();
    }

    @Override
    public float getPlanScore(String projectId) throws Exception {
        List<ProjectScheme> projectSchemeList = projectSchemeService.list(new LambdaQueryWrapperX<>(ProjectScheme.class)
                .select(ProjectScheme::getId, ProjectScheme::getCircumstance)
                .eq(ProjectScheme::getProjectId, projectId)
                .eq(ProjectScheme::getNodeType, ProjectSchemeNodeTypeEnum.PLAN.getValue()));
        BigDecimal allNum = new BigDecimal(projectSchemeList.size());
        if (allNum.compareTo(BigDecimal.ZERO) == 0) {
            return 0;
        }
        List<ProjectScheme> completeNormalProjectSchemeList = projectSchemeList.stream()
                .filter(f -> Status.CIRCUMSTANCE_COMPLETE_NORMAL.getCode().equals(f.getCircumstance())).collect(Collectors.toList());
        BigDecimal completeNormalNum = new BigDecimal(completeNormalProjectSchemeList.size());
        return completeNormalNum.divide(allNum, 2, RoundingMode.HALF_UP).multiply(new BigDecimal("60"))
                .setScale(2, RoundingMode.HALF_UP).floatValue();
    }

    @Override
    public float getBudgetScore(String projectId) throws Exception {
        //预算
        BigDecimal budgetMoney = budgetManagementService.list(new LambdaQueryWrapperX<>(BudgetManagement.class)
                .eq(BudgetManagement::getProjectId, projectId)
                .isNotNull(BudgetManagement::getBudgetMoney))
                .stream().map(BudgetManagement::getBudgetMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (budgetMoney.compareTo(BigDecimal.ZERO) == 0) {
            return 0;
        }
        //支出
        BigDecimal expendMoney = budgetExpendFormService.list(new LambdaQueryWrapperX<>(BudgetExpendForm.class)
                .eq(BudgetExpendForm::getProjectId, projectId)
                .isNotNull(BudgetExpendForm::getExpendMoney))
                .stream().map(BudgetExpendForm::getExpendMoney).reduce(BigDecimal.ZERO, BigDecimal::add);

        //130-【10*（预算使用率-1）】 = 140-10*预算使用率
        BigDecimal result = new BigDecimal("140").subtract(expendMoney.divide(budgetMoney, 2, RoundingMode.HALF_UP).multiply(new BigDecimal("10"))).setScale(2, RoundingMode.HALF_UP);
        if (result.compareTo(BigDecimal.ZERO) < 0) {
            return 0;
        } else if (result.compareTo(new BigDecimal("130")) > 0) {
            return 130;
        } else {
            return result.floatValue();
        }
    }

    @Override
    public int getWorkHoursScore(String projectId) throws Exception {
        int estimateWorkHours = workHourEstimateService.list(new LambdaQueryWrapperX<>(WorkHourEstimate.class)
                .select(WorkHourEstimate::getId, WorkHourEstimate::getWorkHour)
                .eq(WorkHourEstimate::getProjectId, projectId))
                .stream().filter(f -> ObjectUtil.isNotEmpty(f.getWorkHour())).mapToInt(WorkHourEstimate::getWorkHour).sum();
        if (estimateWorkHours <= 0) {
            return 0;
        }
        Double useWorkHours = workHoursReportDetailForUserService.getProjectUserWorkHours(projectId);
        BigDecimal useRate = new BigDecimal(useWorkHours).divide(new BigDecimal(estimateWorkHours), 2, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
        BigDecimal bigDecimal = new BigDecimal("100").subtract(useRate);
        if (bigDecimal.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal result = bigDecimal.divide(new BigDecimal("3"), 0, RoundingMode.DOWN).multiply(new BigDecimal("10")).add(new BigDecimal("100"));
            if (result.compareTo(new BigDecimal("150")) > 0) {
                return 150;
            } else {
                return result.intValue();
            }
        } else {
            BigDecimal result = bigDecimal.divide(new BigDecimal("5"), 0, RoundingMode.DOWN).multiply(new BigDecimal("10")).add(new BigDecimal("100"));
            if (result.compareTo(new BigDecimal("0")) < 0) {
                return 0;
            } else {
                return result.intValue();
            }
        }
    }

    @Override
    public int getMaterialFeeScore(String projectId) throws Exception {
        //预算
        BigDecimal budgetMoney = budgetManagementService.list(new LambdaQueryWrapperX<>(BudgetManagement.class)
                .eq(BudgetManagement::getProjectId, projectId)
                .eq(BudgetManagement::getExpenseSubjectName, "材料成本")
                .isNotNull(BudgetManagement::getBudgetMoney))
                .stream().map(BudgetManagement::getBudgetMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (budgetMoney.compareTo(BigDecimal.ZERO) == 0) {
            return 0;
        }
        //支出
        BigDecimal expendMoney = budgetExpendFormService.list(new LambdaQueryWrapperX<>(BudgetExpendForm.class)
                .eq(BudgetExpendForm::getProjectId, projectId)
                .eq(BudgetExpendForm::getExpenseAccountName, "材料成本")
                .isNotNull(BudgetExpendForm::getExpendMoney))
                .stream().map(BudgetExpendForm::getExpendMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal bigDecimal = expendMoney.divide(budgetMoney, 2, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).subtract(new BigDecimal("100"));
        if (bigDecimal.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal result = new BigDecimal("50").subtract(bigDecimal.multiply(new BigDecimal("10")));
            if (result.compareTo(BigDecimal.ZERO) <= 0) {
                return 0;
            }
            return result.intValue();
        }
        return 50;
    }

    @Override
    public int getDedicatedFeeScore(String projectId) throws Exception {
        return getFeeScore(projectId, "专用费");
    }

    @Override
    public int getOutSourceFeeScore(String projectId) throws Exception {
        return getFeeScore(projectId, "外协费");
    }

    @Override
    public int getAffairsFeeScore(String projectId) throws Exception {
        return getFeeScore(projectId, "事务费");
    }

    private int getFeeScore(String projectId, String feeName) throws Exception {
        BigDecimal budgetMoney = budgetManagementService.list(new LambdaQueryWrapperX<>(BudgetManagement.class)
                .eq(BudgetManagement::getProjectId, projectId)
                .eq(BudgetManagement::getExpenseSubjectName, feeName)
                .isNotNull(BudgetManagement::getBudgetMoney))
                .stream().map(BudgetManagement::getBudgetMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (budgetMoney.compareTo(BigDecimal.ZERO) == 0) {
            return 0;
        }
        //支出
        BigDecimal expendMoney = budgetExpendFormService.list(new LambdaQueryWrapperX<>(BudgetExpendForm.class)
                .eq(BudgetExpendForm::getProjectId, projectId)
                .eq(BudgetExpendForm::getExpenseAccountName, feeName)
                .isNotNull(BudgetExpendForm::getExpendMoney))
                .stream().map(BudgetExpendForm::getExpendMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal bigDecimal = expendMoney.divide(budgetMoney, 2, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).subtract(new BigDecimal("100"));
        if (bigDecimal.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal result = new BigDecimal("50").subtract(bigDecimal.divide(new BigDecimal("5"), 0, RoundingMode.DOWN).multiply(new BigDecimal("10")));
            if (result.compareTo(BigDecimal.ZERO) <= 0) {
                return 0;
            }
            return result.intValue();
        }
        return 50;
    }


    @Override
    public int getQuestionForValidate(String projectId) throws Exception {
        return getWorkDay(projectId, QuestionDictConstant.PROCESS_LINK_VALIDATE);
    }


    @Override
    public int getQuestionForTest(String projectId) throws Exception {
        return getWorkDay(projectId, QuestionDictConstant.PROCESS_LINK_TEST);
    }

    @Override
    public int getQuestionAllIsEcologicalIssues(String projectId) throws Exception {
        long count = questionManagementService.count(new LambdaQueryWrapperX<>(QuestionManagement.class)
                .eq(QuestionManagement::getProjectId, projectId)
                .eq(QuestionManagement::getIsEcologicalIssues, true));
        if (count == 0){
            return 0;
        }
        long countResult = count * 2;
        return countResult >=10 ? 10 :  (int) countResult;
    }

    @NotNull
    private int getWorkDay(String projectId, String processLinkType) throws Exception {
        List<QuestionManagement> list = questionManagementService.list(new LambdaQueryWrapperX<>(QuestionManagement.class)
                .eq(QuestionManagement::getProjectId, projectId)
                .eq(QuestionManagement::getQuestionType, QuestionDictConstant.QUESTION_TYPE_PRODUCT_DEVELOPMENT)
                .eq(QuestionManagement::getProcessLink, processLinkType));
        if (CollectionUtil.isEmpty(list)){
            return 0;
        }
        List<CalculationWorkdayNumDTO> calculationWorkdayNumDTOList = list.stream().map(m -> {
            if (ObjectUtil.isNotNull(m.getCloseTime())){
                CalculationWorkdayNumDTO calculationWorkdayNumDTO = new CalculationWorkdayNumDTO();
                calculationWorkdayNumDTO.setBusinessId(m.getId());
                calculationWorkdayNumDTO.setStartDate(m.getCreateTime());
                calculationWorkdayNumDTO.setEndDate(m.getCloseTime());
                return calculationWorkdayNumDTO;
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        if (CollectionUtil.isEmpty(calculationWorkdayNumDTOList)){
            return 0;
        }

        ResponseDTO<List<CalculationWorkdayNumVO>> listResponseDTO = holidaysApi.calculationIntervalDaysBatch(calculationWorkdayNumDTOList);
        List<CalculationWorkdayNumVO> result = listResponseDTO.getResult();
        if (CollectionUtil.isEmpty(result)){
            return 0;
        }
        List<CalculationWorkdayNumVO> calculationWorkdayNumList = result.stream().filter(f -> f.getWorkdayNum() > 15).collect(Collectors.toList());
        return CollectionUtil.isEmpty(calculationWorkdayNumList) ? 5 : 0;
    }


}
