package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * TaskDecompositionPrePost Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-01 18:01:27
 */
@TableName(value = "pms_task_decomposition_pre_post")
@ApiModel(value = "TaskDecompositionPrePostEntity对象", description = "任务分解前后置关系")
@Data

public class TaskDecompositionPrePost extends  ObjectEntity  implements Serializable{

    /**
     * 前置计划Id
     */
    @ApiModelProperty(value = "前置计划Id")
    @TableField(value = "pre_task_id")
    private String preTaskId;

    /**
     * 后置计划Id
     */
    @ApiModelProperty(value = "后置计划Id")
    @TableField(value = "post_task_id")
    private String postTaskId;

    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    @TableField(value = "task_decomposition_id")
    private String taskDecompositionId;

    /**
     * 前后置类型
     */
    @ApiModelProperty(value = "前后置类型")
    @TableField(value = "type")
    private Integer type;

    /**
     * 模板id
     */
    @ApiModelProperty(value = "模板id")
    @TableField(value = "template_id")
    private String templateId;

}
