package com.chinasie.orion.domain.dto.job;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/18/23:06
 * @description:
 */
@Data
public class PersonJobPostDTO implements Serializable {

    /**
     *  数据ID
     */
    @ApiModelProperty("主数据ID")
    @NotEmpty(message = "岗位授权ID不能为空")
    private String id;
    /**
     *  作业岗位code
     */
    @ApiModelProperty("作业岗位编号")
    @NotEmpty(message = "岗位编号不能为空")
    private String jobPostCode;

    /**
     *  作业岗位code
     */
    @ApiModelProperty("作业岗位id")
    @NotEmpty(message = "作业岗位id不能为空")
    private String jobPostId;
}
