package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.MaterialOutManageDTO;
import com.chinasie.orion.domain.dto.material.InAndOutDTO;
import com.chinasie.orion.domain.dto.material.OutBoundDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.domain.entity.MaterialManage;
import com.chinasie.orion.domain.dto.MaterialManageDTO;
import com.chinasie.orion.domain.vo.MaterialManageVO;
import com.chinasie.orion.service.MaterialManageService;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * MaterialManage 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 17:08:48
 */
@RestController
@RequestMapping("/material-manage")
@Api(tags = "物资库")
public class  MaterialManageController  {

    @Autowired
    private MaterialManageService materialManageService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看【物资管理】详情【{{#materialNumber}}】", type = "MaterialManage", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<MaterialManageVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        MaterialManageVO rsp = materialManageService.detail(id,pageCode);
        LogRecordContext.putVariable("materialNumber", rsp.getId());
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param materialManageDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【物资管理】数据【{{#materialManageDTO.number}}】", type = "MaterialManage", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody MaterialManageDTO materialManageDTO) throws Exception {
        String rsp =  materialManageService.create(materialManageDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param materialManageDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】【物资管理】操作物资【{{#materialManageDTO.name}}】进场基地【{{#baseCode}}】", type = "MaterialManage", subType = "编辑", bizNo = "{{#materialManageDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  MaterialManageDTO materialManageDTO) throws Exception {
        Boolean rsp = materialManageService.edit(materialManageDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【物资管理】数据", type = "MaterialManage", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = materialManageService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【物资管理】数据", type = "MaterialManage", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = materialManageService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【物资管理】数据", type = "MaterialManage", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<MaterialManageVO>> pages(@RequestBody Page<MaterialManageDTO> pageRequest) throws Exception {
        Page<MaterialManageVO> rsp =  materialManageService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * @param materialOutManageDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "单个物资出库")
    @RequestMapping(value = "/{id}/out/bound", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】【物资管理】将物资【{{#name}}-{{#number}}】从基地【{{#baseCode}}】离厂了【{{#id}}】", type = "MaterialManage", subType = "出库", bizNo = "{{#materialManageDTO.id}}")
    public ResponseDTO<Boolean> outBound(@PathVariable("id") String id ,@RequestBody MaterialOutManageDTO materialOutManageDTO) throws Exception {
        Boolean rsp = materialManageService.outBound(id,materialOutManageDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * @param outBoundDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "批量物资出库")
    @RequestMapping(value = "/out/bound/batch", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】【物资管理】批量出库物质【{{#materialIdList}}】", type = "MaterialManage", subType = "出库", bizNo = "{{#materialManageDTO.id}}")
    public ResponseDTO<Boolean> outBoundBatch(@RequestBody OutBoundDTO outBoundDTO) throws Exception {
        Boolean rsp = materialManageService.outBoundBatch(outBoundDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param inAndOutDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑进场离场时间")
    @RequestMapping(value = "/edit/date", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】【物资管理】编辑【{{#jobMaterialDTO.assetName}}】的进场离场时间", type = "JobMaterial", subType = "编辑", bizNo = "{{#jobMaterialDTO.id}}")
    public ResponseDTO<Boolean> editDate(@Validated @RequestBody InAndOutDTO inAndOutDTO) throws Exception {
        Boolean rsp = materialManageService.editDate(inAndOutDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * @param
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "定时器测试入口")
    @RequestMapping(value = "/xxl/test", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    public void outBound() throws Exception {
        materialManageService.materialManageDateVerifyHandler();
    }
}
