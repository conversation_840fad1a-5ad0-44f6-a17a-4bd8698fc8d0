package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * MileStoneLog Entity对象
 *
 * <AUTHOR>
 * @since 2024-11-28 15:51:40
 */
@TableName(value = "pmsx_mile_stone_log")
@ApiModel(value = "MileStoneLogEntity对象", description = "里程碑执行记录")
@Data

public class MileStoneLog extends  ObjectEntity  implements Serializable{

    /**
     * 里程碑id
     */
    @ApiModelProperty(value = "里程碑id")
    @TableField(value = "milestone_id")
    private String milestoneId;

    /**
     * 执行操作
     */
    @ApiModelProperty(value = "执行操作")
    @TableField(value = "edit_desc")
    private String editDesc;

    /**
     * 执行说明
     */
    @ApiModelProperty(value = "执行说明")
    @TableField(value = "edit_message")
    private String editMessage;

    /**
     * 附件数
     */
    @ApiModelProperty(value = "附件数")
    @TableField(value = "file_count")
    private Integer fileCount;

    /**
     * 执行人
     */
    @ApiModelProperty(value = "执行人")
    @TableField(value = "edit_person")
    private String editPerson;

    /**
     * 执行时间
     */
    @ApiModelProperty(value = "执行时间")
    @TableField(value = "edit_time")
    private Date editTime;

    /**
     * 是否需要暂估
     */
    @ApiModelProperty(value = "是否需要暂估")
    @TableField(value = "is_provisional_estimate")
    private Boolean isProvisionalEstimate;



    /**
     * 计划暂估日期
     */
    @ApiModelProperty(value = "计划暂估日期")
    @TableField(value = "planned_estimated_date")
    private Date plannedEstimatedDate;


}
