package com.chinasie.orion.msc;

import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.constant.MessageNodeDict;
import com.chinasie.orion.domain.entity.FixedAssets;
import com.chinasie.orion.msc.api.MscBuildHandler;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class FixedAssetsMsgHandler implements MscBuildHandler<FixedAssets> {

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Override
    public SendMessageDTO buildMsc(FixedAssets fixedAssets, Object... objects) {
        Map<String,Object> messageMap = new HashMap<>();
        SimpleUser simpleUserByCode = userRedisHelper.getSimpleUserByCode(fixedAssets.getRspUserNumber());
        messageMap.put("$number$",fixedAssets.getNumber());

        SendMessageDTO sendMsc = SendMessageDTO.builder()
                .businessId(fixedAssets.getId())
                .todoStatus(0)
                .messageUrl("/pms/fixedAssetCapacity/"+fixedAssets.getId())
                .messageUrlName("固定资产详情")
                .todoType(TodoTypeDict.TODO_TYPE_TASK)
                .recipientIdList(Collections.singletonList(Objects.isNull(simpleUserByCode)? "" : simpleUserByCode.getId()))
                .messageMap(messageMap)
                .titleMap(messageMap)
                .senderTime(new Date())
                .senderId(fixedAssets.getCreatorId())
                .platformId(fixedAssets.getPlatformId())
                .orgId(fixedAssets.getOrgId())
                .build();

        return sendMsc;
    }

    @Override
    public String support() {
        return MessageNodeDict.NODE_FIXED_NOTIFY;
    }
}
