package com.chinasie.orion.manager.impl.msg;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.bo.ProjectProperties;
import com.chinasie.orion.conts.MsgBusinessTypeEnum;
import com.chinasie.orion.domain.dto.SchemeMsgDTO;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.manager.SendMessageCommonAdapter;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.service.ProjectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 计划下发时，消息推送处理
 * <p>
 * 计划完成时消除待办：
 *
 * @see com.chinasie.orion.service.impl.ProjectSchemeServiceImpl#finish(com.chinasie.orion.domain.dto.ProjectSchemeDTO)
 */
@Slf4j
@Component("sendDownSendMsgAdapter")
public class SendDownSendMsgAdapter extends SendMessageCommonAdapter {

    /**
     * 项目计划详情页
     */
    private final String JUMP_URL = "/pms/menuComponents?id=%s";

    @Resource
    private ProjectService projectService;

    @Resource
    private ProjectProperties projectProperties;

    @Resource
    private UserRedisHelper userHelper;


//    String url= projectProperties.getBaseUrl();

    @Override
    protected <T> List<SendMessageDTO> buildMscMessageDTO(SchemeMsgDTO schemeMsgDTO) throws Exception {
        List<SendMessageDTO> messageDTOList = CollUtil.toList();
        //抄送
        if (CollUtil.isNotEmpty(schemeMsgDTO.getRecipientIds())) {
            messageDTOList.addAll(buildSendCopyMessageDTO(schemeMsgDTO.getProjectSchemeList(), schemeMsgDTO.getRecipientIds()));
        }
        messageDTOList.addAll(buildMscMessageDTO(schemeMsgDTO.getProjectSchemeList()));
        return messageDTOList;
    }

    /**
     * 抄送对象
     *
     * @param schemeList
     * @param recipientIdList
     * @return
     */
    private <T> List<? extends SendMessageDTO> buildSendCopyMessageDTO(List<ProjectScheme> schemeList, List<String> recipientIdList) throws Exception {
        ProjectScheme projectScheme = schemeList.stream().findFirst().orElse(new ProjectScheme());
        Project project = Optional.ofNullable(projectService.getById(projectScheme.getProjectId())).orElse(new Project());
        List<SendMessageDTO> messageDTOList = CollUtil.toList();
        schemeList.forEach(item -> {
            String parentId = "";
            //获取最父级id
            if(item.getLevel() != 1){
                String[] ids = item.getParentChain().split(",");
                if(ids.length > 1){
                    parentId = ids[1];
                }
            }else{
                parentId = item.getId();
            }
            SendMessageDTO backMessageDTO = SendMessageDTO.builder()
                    .businessData(JSON.toJSONString(MapUtil.builder()
                            .put("flowType", TYPE_FLOW_TYPE_MAP.get(MsgBusinessTypeEnum.SEND_DOWN_COPY))
                            .put("parentId",parentId)
                            .put("id",item.getId())
                            .put("type", "plan")
                            .put("projectId", item.getProjectId())
                            .build()))
                    .titleMap(MapUtil.builder(new HashMap<String, Object>())
                            .put("$projectName$", project.getName())
                            .put("$projectSchemeName$", item.getName())
                            .build())
                    .messageMap(MapUtil.builder(new HashMap<String, Object>())
                            .put("$projectName$", project.getName())
                            .put("$projectSchemeName$", item.getName())
                            .build())
                    .businessNodeCode(TYPE_CODE_MAP.get(MsgBusinessTypeEnum.SEND_DOWN_COPY))
                    .businessTypeCode("ProjectScheme")
                    .businessTypeName("项目计划")
                    .todoType(TodoTypeDict.TODO_TYPE_TASK)
                    .messageUrl(String.format(JUMP_URL, item.getProjectId()) +"&query="+System.currentTimeMillis())
                    .messageUrlName("详情")
                    .recipientIdList(recipientIdList)
                    .senderId(CurrentUserHelper.getCurrentUserId())
                    .senderTime(new Date())
                    .businessId(packageBusinessId(MsgBusinessTypeEnum.SEND_DOWN, item.getId()))
                    .platformId(item.getPlatformId())
                    .orgId(item.getOrgId())
                    .build();
            messageDTOList.add(backMessageDTO);
        });
        return messageDTOList;
    }


    /**
     * 下发计划 对应对象
     *
     * @param schemeList
     * @return
     */
    private <T> List<? extends SendMessageDTO> buildMscMessageDTO(List<ProjectScheme> schemeList) {
        List<SendMessageDTO> messageDTOList = CollUtil.toList();
        String userId= CurrentUserHelper.getCurrentUserId();
        schemeList.forEach(item -> {
            String parentId = "";
            //获取最父级id
            if(item.getLevel() != 1){
                String[] ids = item.getParentChain().split(",");
                if(ids.length > 1){
                    parentId = ids[1];
                }
            }else{
                parentId = item.getId();
            }
            List<String> recipientIds = new ArrayList<>();
            String participantUsers = item.getParticipantUsers();
            if(StringUtils.hasText(participantUsers)){
                List<String> split = ListUtil.of(participantUsers.split(","));
                recipientIds.addAll(split);
            }
            recipientIds.add(item.getRspUser());
            String baseUrl= projectProperties.getBaseUrl();
            log.info("plan-name:{},recipientIds:{}",item.getName(),JSON.toJSONString(recipientIds));
            String url =baseUrl+"/pms/menuComponents?id="+item.getProjectId();
            Map<String,Object> titleMap =   MapUtil.builder(new HashMap<String, Object>())
                    .put("$recipientName$", item.getRspUserName())
                    .put("$workName$", item.getName())
                    .put("$creatorName$", item.getCreatorName())
                    .put("$rspUserName$", item.getRspUserName())
                    .put("$taskName$", item.getName())
                    .put("$beginDate$", DateUtil.format(item.getBeginTime(), "yyyy-MM-dd"))
                    .put("$endDate$",DateUtil.format(item.getEndTime(), "yyyy-MM-dd"))
                    .put("$taskTypeName$", "计划")
                    .put("$levelName$", item.getUrgency())
                    .put("$todoUrl$", url)
                    .put("$projectSchemeName$", item.getName())
                    .build();

            SendMessageDTO backMessageDTO = SendMessageDTO.builder()
                    .businessData(JSON.toJSONString(MapUtil.builder()
                            .put("flowType", TYPE_FLOW_TYPE_MAP.get(MsgBusinessTypeEnum.SEND_DOWN))
                            .put("parentId",parentId)
                             .put("id",item.getId())
                             .put("type", "plan")
                            .put("projectId", item.getProjectId())
                            .build()))
                    .titleMap(titleMap)
                    .messageMap(titleMap)
                    .businessNodeCode(TYPE_CODE_MAP.get(MsgBusinessTypeEnum.SEND_DOWN))
                    .businessTypeCode("ProjectScheme")
                    .businessTypeName("项目计划")
                    .todoType(TodoTypeDict.TODO_TYPE_TASK)
                    .messageUrl(String.format(JUMP_URL, item.getProjectId())+"&query="+System.currentTimeMillis())
                    .messageUrlName("详情")
                    .recipientIdList(recipientIds.stream().distinct().collect(Collectors.toList()))
                    .senderId(userId)
                    .senderTime(new Date())
                    .businessId(packageBusinessId(MsgBusinessTypeEnum.SEND_DOWN, item.getId()))
                    .platformId(item.getPlatformId())
                    .orgId(item.getOrgId())
                    .build();
            messageDTOList.add(backMessageDTO);
        });
        return messageDTOList;
    }


}
