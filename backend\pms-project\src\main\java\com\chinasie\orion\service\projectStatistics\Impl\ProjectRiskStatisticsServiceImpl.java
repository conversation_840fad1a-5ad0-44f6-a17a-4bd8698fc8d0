package com.chinasie.orion.service.projectStatistics.Impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.domain.dto.projectStatistics.ProjectRiskStatisticsDTO;
import com.chinasie.orion.domain.entity.RiskManagement;
import com.chinasie.orion.domain.entity.projectStatistics.RiskStatusStatistics;
import com.chinasie.orion.domain.vo.RiskManagementVO;
import com.chinasie.orion.domain.vo.projectStatistics.ProjectRiskStatisticsVO;
import com.chinasie.orion.feign.PasFeignService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.RiskManagementRepository;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.RiskManagementService;
import com.chinasie.orion.service.projectStatistics.ProjectRiskStatisticsService;
import com.chinasie.orion.service.projectStatistics.RiskStatusStatisticsService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ProjectRiskStatisticsServiceImpl implements ProjectRiskStatisticsService {

    @Autowired
    private RiskManagementService riskManagementService;

    @Autowired
    private RiskStatusStatisticsService riskStatusStatisticsService;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Resource
    private RiskManagementRepository riskManagementRepository;

    @Resource
    private PasFeignService pasFeignService;

    @Resource
    private DictBo dictBo;

    @Override
    public ProjectRiskStatisticsVO getProjectRiskStatusStatistics(ProjectRiskStatisticsDTO projectRiskStatisticsDTO) {
        ProjectRiskStatisticsVO projectRiskStatisticsVO = new ProjectRiskStatisticsVO();
        LambdaQueryWrapperX<RiskManagement> projectRiskLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        projectRiskLambdaQueryWrapperX.select("status,count(id) as count");
        projectRiskLambdaQueryWrapperX.eq(RiskManagement::getProjectId, projectRiskStatisticsDTO.getProjectId());
        projectRiskLambdaQueryWrapperX.eqIfPresent(RiskManagement::getRiskType, projectRiskStatisticsDTO.getRiskType());
        projectRiskLambdaQueryWrapperX.groupBy(RiskManagement::getStatus);
        List<Map<String, Object>> list = riskManagementService.listMaps(projectRiskLambdaQueryWrapperX);
        for (Map<String, Object> map : list) {
            if ("101".equals(map.get("status").toString())) {
                projectRiskStatisticsVO.setUnFinishedCount(Integer.parseInt(map.get("count").toString()));
            }
            if ("110".equals(map.get("status").toString())) {
                projectRiskStatisticsVO.setProcessCount(Integer.parseInt(map.get("count").toString()));
            }
            if ("130".equals(map.get("status").toString())) {
                projectRiskStatisticsVO.setFinishedCount(Integer.parseInt(map.get("count").toString()));
            }
        }
        return projectRiskStatisticsVO;
    }

    @Override
    public List<ProjectRiskStatisticsVO> getProjectRiskRspUserStatistics(ProjectRiskStatisticsDTO projectRiskStatisticsDTO) {
        List<ProjectRiskStatisticsVO> projectRiskStatisticsVOs = new ArrayList<>();
        LambdaQueryWrapperX<RiskManagement> projectRiskLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        projectRiskLambdaQueryWrapperX.select("principal_id as rspUser,IFNULL( sum( CASE  WHEN `status`=101 THEN 1 ELSE 0 END ), 0 ) unFinishedCount ," +
                "IFNULL( sum( CASE  WHEN `status`=110 THEN 1 ELSE 0 END ), 0 ) as processCount," +
                "IFNULL( sum( CASE  WHEN `status`=130 THEN 1 ELSE 0 END ), 0 ) as finishedCount");
        projectRiskLambdaQueryWrapperX.eq(RiskManagement::getProjectId, projectRiskStatisticsDTO.getProjectId());
        projectRiskLambdaQueryWrapperX.eqIfPresent(RiskManagement::getRiskType, projectRiskStatisticsDTO.getRiskType());
        projectRiskLambdaQueryWrapperX.groupBy(RiskManagement::getPrincipalId);
        List<Map<String, Object>> list = riskManagementService.listMaps(projectRiskLambdaQueryWrapperX);
        for (Map<String, Object> map : list) {
            if (ObjectUtil.isEmpty(map.get("rspUser"))) {
                continue;
            }
            ProjectRiskStatisticsVO projectRiskStatisticsVO = new ProjectRiskStatisticsVO();
            projectRiskStatisticsVO.setRspuser(map.get("rspUser").toString());
            UserVO userVO = userRedisHelper.getUserById(map.get("rspUser").toString());
            if (ObjectUtil.isNotEmpty(userVO)) {
                projectRiskStatisticsVO.setRspuserName(userVO.getName());
            }
            projectRiskStatisticsVO.setUnFinishedCount(Integer.parseInt(map.get("unFinishedCount").toString()));
            projectRiskStatisticsVO.setFinishedCount(Integer.parseInt(map.get("finishedCount").toString()));
            projectRiskStatisticsVO.setProcessCount(Integer.parseInt(map.get("processCount").toString()));
            projectRiskStatisticsVOs.add(projectRiskStatisticsVO);
        }
        return projectRiskStatisticsVOs;
    }

    @Override
    public List<ProjectRiskStatisticsVO> getProjectRiskChangeStatusStatistics(ProjectRiskStatisticsDTO projectRiskStatisticsDTO) {
        List<ProjectRiskStatisticsVO> projectRiskStatisticsVOs = new ArrayList<>();
        LambdaQueryWrapperX<RiskStatusStatistics> projectRiskLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        String sql = "";
        Map<String, String> timeMap = new HashMap<>();
        Map<String, Date> timeValue = new HashMap<>();
        for (int i = 0; i < 10; i++) {
            Calendar calendar = Calendar.getInstance();
            Date startDate = new Date();
            Date endDate = new Date();
            if (projectRiskStatisticsDTO.getTimeType().equals("DAY")) {
                calendar.add(Calendar.DAY_OF_YEAR, -i);
                startDate = DateUtil.beginOfDay(calendar.getTime());
                endDate = DateUtil.endOfDay(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1) +"-"+ calendar.get(Calendar.DAY_OF_MONTH));
            }
            if (projectRiskStatisticsDTO.getTimeType().equals("MONTH")) {
                calendar.add(Calendar.MONTH, -i);
                startDate = DateUtil.beginOfMonth(calendar.getTime());
                endDate = DateUtil.endOfMonth(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1));
            }
            if (projectRiskStatisticsDTO.getTimeType().equals("QUARTER")) {
                calendar.add(Calendar.MONTH, -3 * i);
                startDate = DateUtil.beginOfQuarter(calendar.getTime());
                endDate = DateUtil.endOfQuarter(calendar.getTime());
                int quarter = (calendar.get(Calendar.MONTH)+1) < 3 ? 1 : (int)Math.ceil((double)(calendar.get(Calendar.MONTH)+1) / 3);
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "年第" + quarter + "季度");
            }
            if (projectRiskStatisticsDTO.getTimeType().equals("WEEK")) {
                calendar.add(Calendar.WEEK_OF_YEAR, -i);
                startDate = DateUtil.beginOfWeek(calendar.getTime());
                endDate = DateUtil.endOfWeek(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1) + "-第" + calendar.get(Calendar.WEEK_OF_MONTH) + "周");
            }
            if (projectRiskStatisticsDTO.getTimeType().equals("YEAR")) {
                calendar.add(Calendar.YEAR, -i);
                startDate = DateUtil.beginOfYear(calendar.getTime());
                endDate = DateUtil.endOfYear(calendar.getTime());
                timeMap.put("time" + i, String.valueOf(calendar.get(Calendar.YEAR)));
            }
            timeValue.put("time" + i, startDate);
            if(i==0){
                endDate=new Date();
            }
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
            sql =  sql + "IFNULL(sum(CASE  WHEN DATE_FORMAT(now_day, '%Y-%m-%d') =  '"+sdf.format(endDate) +"' THEN un_finished_count ELSE 0 END ), 0 ) as unFinishedCountTime" + i + ","+
                    "IFNULL(sum(CASE  WHEN  DATE_FORMAT(now_day, '%Y-%m-%d') =  '"+sdf.format(endDate) +"' THEN process_count ELSE 0 END ), 0 ) as  processCountTime" + i + ","+
                    "IFNULL(sum(CASE  WHEN  DATE_FORMAT(now_day, '%Y-%m-%d') =  '"+sdf.format(endDate) +"' THEN finished_count ELSE 0 END ), 0 ) as finishedCountTime" + i ;
            if(i<9){
                sql=sql+"," ;
            }
        }
        projectRiskLambdaQueryWrapperX.select(sql);
        projectRiskLambdaQueryWrapperX.eq(RiskStatusStatistics::getProjectId, projectRiskStatisticsDTO.getProjectId());
        projectRiskLambdaQueryWrapperX.eqIfPresent(RiskStatusStatistics::getTypeId, projectRiskStatisticsDTO.getRiskType());
        List<Map<String, Object>> list = riskStatusStatisticsService.listMaps(projectRiskLambdaQueryWrapperX);
        Map<String, Object> map = list.get(0);
        for (int i = 0; i < 10; i++) {
            ProjectRiskStatisticsVO projectRiskStatisticsVO = new ProjectRiskStatisticsVO();
            projectRiskStatisticsVO.setUnFinishedCount(Integer.parseInt(map.get("unFinishedCountTime" + i).toString()));
            projectRiskStatisticsVO.setProcessCount(Integer.parseInt(map.get("processCountTime" + i).toString()));
            projectRiskStatisticsVO.setFinishedCount(Integer.parseInt(map.get("finishedCountTime" + i).toString()));
            projectRiskStatisticsVO.setTimeValue(timeValue.get("time" + i));
            projectRiskStatisticsVO.setShowTime(timeMap.get("time" + i));
            projectRiskStatisticsVOs.add(projectRiskStatisticsVO);
        }
        Collections.reverse(projectRiskStatisticsVOs);
        return projectRiskStatisticsVOs;
    }

    @Override
    public List<ProjectRiskStatisticsVO> getProjectRiskCreateStatistics(ProjectRiskStatisticsDTO projectRiskStatisticsDTO) {
        List<ProjectRiskStatisticsVO> projectRiskStatisticsVOs = new ArrayList<>();
        LambdaQueryWrapperX<RiskManagement> projectRiskLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        String sql = "";
        Map<String, String> timeMap = new HashMap<>();
        Map<String, Date> timeValue = new HashMap<>();
        for (int i = 0; i < 10; i++) {
            Calendar calendar = Calendar.getInstance();
            Date startDate = new Date();
            Date endDate = new Date();
            if (projectRiskStatisticsDTO.getTimeType().equals("DAY")) {
                calendar.add(Calendar.DAY_OF_YEAR, -i);
                startDate = DateUtil.beginOfDay(calendar.getTime());
                endDate = DateUtil.endOfDay(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1) +"-"+ calendar.get(Calendar.DAY_OF_MONTH));
            }
            if (projectRiskStatisticsDTO.getTimeType().equals("MONTH")) {
                calendar.add(Calendar.MONTH, -i);
                startDate = DateUtil.beginOfMonth(calendar.getTime());
                endDate = DateUtil.endOfMonth(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1));
            }
            if (projectRiskStatisticsDTO.getTimeType().equals("QUARTER")) {
                calendar.add(Calendar.MONTH, -3 * i);
                startDate = DateUtil.beginOfQuarter(calendar.getTime());
                endDate = DateUtil.endOfQuarter(calendar.getTime());
                int quarter = (calendar.get(Calendar.MONTH)+1) < 3 ? 1 : (int)Math.ceil((double)(calendar.get(Calendar.MONTH)+1) / 3);
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "年第" + quarter + "季度");
            }
            if (projectRiskStatisticsDTO.getTimeType().equals("WEEK")) {
                calendar.add(Calendar.WEEK_OF_YEAR, -i);
                startDate = DateUtil.beginOfWeek(calendar.getTime());
                endDate = DateUtil.endOfWeek(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1) + "-第" + calendar.get(Calendar.WEEK_OF_MONTH) + "周");
            }
            if (projectRiskStatisticsDTO.getTimeType().equals("YEAR")) {
                calendar.add(Calendar.YEAR, -i);
                startDate = DateUtil.beginOfYear(calendar.getTime());
                endDate = DateUtil.endOfYear(calendar.getTime());
                timeMap.put("time" + i, String.valueOf(calendar.get(Calendar.YEAR)));
            }
            timeValue.put("time" + i, startDate);
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            sql = sql + "IFNULL(sum(CASE  WHEN `create_time`>= '" + sdf.format(startDate) + "'  and  `create_time` <= '" + sdf.format(endDate) +"' THEN 1 ELSE 0 END ), 0 ) as time" + i ;
            if(i<9){
                sql=sql+"," ;
            }
        }
        projectRiskLambdaQueryWrapperX.select(sql);
        projectRiskLambdaQueryWrapperX.eq(RiskManagement::getProjectId, projectRiskStatisticsDTO.getProjectId());
        projectRiskLambdaQueryWrapperX.eqIfPresent(RiskManagement::getRiskType, projectRiskStatisticsDTO.getRiskType());
        List<Map<String, Object>> list = riskManagementService.listMaps(projectRiskLambdaQueryWrapperX);
        Map<String, Object> map = list.get(0);
        for (int i = 0; i < 10; i++) {
            ProjectRiskStatisticsVO projectRiskStatisticsVO = new ProjectRiskStatisticsVO();
            projectRiskStatisticsVO.setUnFinishedCount(Integer.parseInt(map.get("time" + i).toString()));
            projectRiskStatisticsVO.setTimeValue(timeValue.get("time" + i));
            projectRiskStatisticsVO.setShowTime(timeMap.get("time" + i));
            projectRiskStatisticsVOs.add(projectRiskStatisticsVO);
        }
        Collections.reverse(projectRiskStatisticsVOs);
        return projectRiskStatisticsVOs;
    }

    @Override
    public Page<RiskManagementVO> getProjectRiskPages(Page<ProjectRiskStatisticsDTO> pageRequest) throws Exception {
        Page<RiskManagementVO>  riskManagementPage=pasFeignService.getRiskManagementStatisticPage(pageRequest).getResult();
        return riskManagementPage;
    }
}
