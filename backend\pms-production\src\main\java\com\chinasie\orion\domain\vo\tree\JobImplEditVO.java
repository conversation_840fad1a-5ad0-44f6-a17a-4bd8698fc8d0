package com.chinasie.orion.domain.vo.tree;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.chinasie.orion.domain.vo.job.BeforeAndAfterFourDay;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

@Data
public class JobImplEditVO   implements Serializable {
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "工单名称")
    private String  jobName;
    @ApiModelProperty(value = "工单id")
    private String  jobId;
    @ApiModelProperty(value = "工单号")
    private String  jobNumber;
    @ApiModelProperty(value = "大修伦次")
    private String  repairRound;
    @ApiModelProperty(value = "阶段")
    private String  phase;
    @ApiModelProperty(value = "责任人id")
    private String  rspUserId;
    @ApiModelProperty(value = "责任人名称")
    private String  rspUserName;
    @ApiModelProperty(value = "责任人工号")
    private String  rspUserCode;

    @ApiModelProperty(value = "实际开始时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date actualBeginTime;

    @ApiModelProperty(value = "实际结束时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date actualEndTime;
}
