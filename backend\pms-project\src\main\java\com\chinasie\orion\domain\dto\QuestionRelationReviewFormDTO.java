package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * QuestionRelationReviewForm DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-15 11:03:43
 */
@ApiModel(value = "QuestionRelationReviewFormDTO对象", description = "问题与评审单关系表")
@Data
@ExcelIgnoreUnannotated
public class QuestionRelationReviewFormDTO extends ObjectDTO implements Serializable{

/**
 * 问题ID
 */
@ApiModelProperty(value = "问题ID")
@ExcelProperty(value = "问题ID ", index = 0)
private String fromId;

/**
 * 问题类名
 */
@ApiModelProperty(value = "问题类名")
@ExcelProperty(value = "问题类名 ", index = 1)
private String fromClass;

/**
 * 评审单ID
 */
@ApiModelProperty(value = "评审单ID")
@ExcelProperty(value = "评审单ID ", index = 2)
private String toId;

/**
 * 评审单类名
 */
@ApiModelProperty(value = "评审单类名")
@ExcelProperty(value = "评审单类名 ", index = 3)
private String toClass;




}
