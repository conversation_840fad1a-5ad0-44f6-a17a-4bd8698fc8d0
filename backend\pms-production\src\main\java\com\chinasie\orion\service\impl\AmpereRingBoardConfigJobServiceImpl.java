package com.chinasie.orion.service.impl;

import ch.qos.logback.core.util.StringCollectionUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.chinasie.orion.domain.dto.AmpereRingBoardConfigJobDTO;
import com.chinasie.orion.domain.entity.AmpereRingBoardConfigJob;
import com.chinasie.orion.domain.vo.AmpereRingBoardConfigJobVO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.AmpereRingBoardConfigJobMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.AmpereRingBoardConfigJobService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 21 日
 **/
@Service
@Slf4j
public class AmpereRingBoardConfigJobServiceImpl extends OrionBaseServiceImpl<AmpereRingBoardConfigJobMapper, AmpereRingBoardConfigJob> implements AmpereRingBoardConfigJobService {
    /**
     * 分页查询
     *
     * @param pageRequest
     * @return
     */
    @Override
    public Page<AmpereRingBoardConfigJobVO> pages(Page<AmpereRingBoardConfigJobVO> pageRequest) {
        AmpereRingBoardConfigJobVO query = pageRequest.getQuery();
        LambdaQueryWrapperX<AmpereRingBoardConfigJob> queryWrapperX=new LambdaQueryWrapperX<>();
        if(Objects.nonNull(query) && StringUtils.hasText(query.getJobName())){
            queryWrapperX.like(AmpereRingBoardConfigJob::getJobName,query.getJobName());
        }
        queryWrapperX.select(AmpereRingBoardConfigJob::getJobName,AmpereRingBoardConfigJob::getId,AmpereRingBoardConfigJob::getIsShow);
        queryWrapperX.orderByAsc(AmpereRingBoardConfigJob::getJobName);
        Page<AmpereRingBoardConfigJob> mPage=new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        PageResult<AmpereRingBoardConfigJob> result = this.baseMapper.selectPage(mPage, queryWrapperX);
        List<AmpereRingBoardConfigJobVO> jobVOS = BeanCopyUtils.convertListTo(result.getContent(), AmpereRingBoardConfigJobVO::new);
        ArrayList list = jobVOS.stream().collect(Collectors.collectingAndThen(Collectors.toMap(AmpereRingBoardConfigJobVO::getJobName, Function.identity(), (k1, k2) -> k1), map ->
                new ArrayList(map.values())));
        Page<AmpereRingBoardConfigJobVO> resultPage=new Page<>(result.getPageNum(), result.getPageSize(),result.getTotalSize());
        resultPage.setContent(list);
        return resultPage;
    }

    /**
     * 列表查询
     *
     * @param jobVO
     * @return
     */
    @Override
    public List<AmpereRingBoardConfigJobVO> list(AmpereRingBoardConfigJobVO jobVO) {
        LambdaQueryWrapperX<AmpereRingBoardConfigJob> queryWrapperX=new LambdaQueryWrapperX<>();
        if(StringUtils.hasText(jobVO.getJobName())){
            queryWrapperX.like(AmpereRingBoardConfigJob::getJobName,jobVO.getJobName());
        }
        queryWrapperX.select(AmpereRingBoardConfigJob::getJobName,AmpereRingBoardConfigJob::getId,AmpereRingBoardConfigJob::getIsShow);
        queryWrapperX.orderByAsc(AmpereRingBoardConfigJob::getJobName);
        List<AmpereRingBoardConfigJob> result = this.baseMapper.selectList(queryWrapperX);
        ArrayList list = BeanCopyUtils.convertListTo(result,AmpereRingBoardConfigJobVO::new).stream().collect(Collectors.collectingAndThen(Collectors.toMap(AmpereRingBoardConfigJobVO::getJobName, Function.identity(), (k1, k2) -> k1), map ->
                new ArrayList(map.values())));
        return list;
    }

    /**
     * 是否在看板展示
     *
     * @param jobDTO
     * @return
     */
    @Override
    public Boolean isShow(AmpereRingBoardConfigJobDTO jobDTO) {
        if(StringUtils.hasText(jobDTO.getId())){
            LambdaQueryWrapperX<AmpereRingBoardConfigJob> queryWrapperX=new LambdaQueryWrapperX<>();
            queryWrapperX.in(AmpereRingBoardConfigJob::getId,jobDTO.getId());
            List<AmpereRingBoardConfigJob> ringBoardConfigJobs = this.baseMapper.selectList(queryWrapperX);
            if(CollectionUtil.isNotEmpty(ringBoardConfigJobs)){
                List<String> jobNameList = ringBoardConfigJobs.stream().map(AmpereRingBoardConfigJob::getJobName).distinct().collect(Collectors.toList());
                LambdaQueryWrapperX<AmpereRingBoardConfigJob> nameWrapperX=new LambdaQueryWrapperX<>();
                nameWrapperX.in(AmpereRingBoardConfigJob::getJobName,jobNameList);
                AmpereRingBoardConfigJob job=new AmpereRingBoardConfigJob();
                job.setIsShow(jobDTO.getIsShow());
                this.baseMapper.update(job,nameWrapperX);
            }

        }
        return true;
    }
}
