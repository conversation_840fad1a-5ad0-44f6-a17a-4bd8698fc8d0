package com.chinasie.orion.repository;

import com.chinasie.orion.domain.entity.DemandManagement;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import com.chinasie.orion.search.common.domain.IndexData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/18/14:59
 * @description:
 */
@Mapper
public interface DemandManagementRepository extends OrionBaseMapper<DemandManagement> {
    /**
     * 获取自上次索引依赖新增的可索引的EDM数据.
     *
     * @param lastIndexTime 上次索引时间
     * @param limitSize
     * @return
     */
    List<IndexData> fetchIndexData(@Param("lastIndexTime") Date lastIndexTime, @Param("limitSize") Integer limitSize);
}
