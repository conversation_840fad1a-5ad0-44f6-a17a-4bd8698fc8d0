// @ts-ignore
import { openDrawer, openSelectUserModal } from 'lyra-component-vue3';
import { h, ref, Ref } from 'vue';
import UserManageForm from './components/UserManageForm.vue';
import LeaveFactoryForm from '/@/views/pms/userManage/components/LeaveFactoryForm.vue';
import { message, Modal } from 'ant-design-vue';
import Api from '/@/api';

// 添加人员
export function openUserModal(params: {
    operationType?: string,
    baseName: string,
    basePlaceCode: string,
    trainCenterId?: string,
    trainId?: string,
    isChangDpt?: boolean,
    changeDptParams?: string[]
    trainNumber?: string,
    cb?: () => void,
}) {
  const treeDataApi = () => new Api('/pmi/organization/current/org/tree').fetch(
    params?.changeDptParams ?? [],
    '',
    'POST',
  );
  openSelectUserModal([], {
    okHandle(user: any[]) {
      if (params?.operationType === 'trainPerson') {
        return new Promise((resolve) => {
          new Api('/pms/train-person/add/batch').fetch({
            codeList: user.map((item) => item.code),
            trainCenterId: params?.trainCenterId,
            trainId: params?.trainId,
            trainNumber: params?.trainNumber,
          }, '', 'POST').then(async () => {
            message.success('添加成功');
            params?.cb?.();
            resolve('');
          }).catch(() => {
            resolve('');
          });
        });
      }
      Modal.confirm({
        title: '系统提示！',
        content: `是否将选中人员添加入${params.baseName}中?`,
        onOk() {
          return new Promise((res) => {
            new Api('/pms/person-mange/add/batch').fetch({
              basePlaceCode: params.basePlaceCode,
              codeList: user.map((item) => item.code),
            }, '', 'POST').then(() => {
              params?.cb?.();
              res(true);
            }).catch(() => {
              res('');
            });
          });
        },
      });

      return Promise.resolve();
    },
    ...(params.isChangDpt ? {
      treeDataApi,
    } : {}),
  });
}

// 新增、编辑人员
export function openUserManageForm(record: any, cb?: () => void) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: record?.id ? '编辑人员' : '新增人员',
    width: 1000,
    content() {
      return h(UserManageForm, {
        ref: drawerRef,
        record,
      });
    },
    async onOk() {
      await drawerRef.value.submit();
      cb?.();
    },
  });
}

// 离场
export function openLeaveFactoryForm(record: any, cb?: () => void) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: '人员离场',
    width: 1000,
    content() {
      return h(LeaveFactoryForm, {
        ref: drawerRef,
        record,
      });
    },
    async onOk() {
      await drawerRef.value.submit();
      cb?.();
    },
  });
}
