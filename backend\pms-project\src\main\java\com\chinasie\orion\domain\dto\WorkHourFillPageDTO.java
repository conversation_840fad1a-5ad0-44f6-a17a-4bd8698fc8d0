package com.chinasie.orion.domain.dto;

import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * WorkHourFillPageDTO Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-15 10:14:51
 */
@ApiModel(value = "WorkHourFillPageDTO对象", description = "工时填报pageDto")
@Data
public class WorkHourFillPageDTO extends ObjectDTO implements Serializable{

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 单据编号
     */
    @ApiModelProperty(value = "单据编号")
    private String number;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String type;

    /**
     * 成员id
     */
    @ApiModelProperty(value = "成员id")
    private String memberId;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 工时类型
     */
    @ApiModelProperty(value = "工时类型")
    private String workHourType;

    /**
     * 填报角色
     */
    @ApiModelProperty(value = "填报角色")
    private String fillRole;

}
