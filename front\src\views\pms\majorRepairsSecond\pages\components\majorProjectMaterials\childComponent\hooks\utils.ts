import {
  treeMap,
  openModal,
} from 'lyra-component-vue3';
import { Ref, ref, h } from 'vue';
import Api from '/@/api';

// 修改接口
export function updateEdit(record: Record<string, any>, url: string = 'relationOrgToMaterial/edit') {
  return new Api('/pms').fetch(record, url, 'PUT');
}

// 处理更新
export const handleUpdate = (dataList:any, record: any, data: any, field: string, value: string, resolve: (value: string) => void, url?: string, dicObject?: any, detailsData?: any, tableMethods?: any) => {
  updateEdit({
    ...data,
    [field]: value,
    name: record?.name,
    repairRound: detailsData?.repairRound,
    baseCode: detailsData?.baseCode,
  }, url).then((res) => {
    treeMap(dataList.value, {
      conversion(item) {
        item.businessDataList = item.businessDataList.map((b) =>
          (b?.data?.materialManageId === res.materialManageId
            ? {
              ...b,
              data: {
                ...b.data,
                status: field === 'actInDate' && value ? 1 : (field === 'actOutDate' && value ? 2 : b?.data?.status),
                ...res,
                outReasonName: field === 'outReason' ? dicObject?.label : b?.data?.outReasonName,
              },
            }
            : b));
      },
    });
    // if (res) {
    //   tableMethods.updateTable();
    // }
    resolve('');
  }).catch((error) => {
    resolve('');
  });
};

// 修改接口
export function updateEditMaterials(record: Record<string, any>, url: string = 'relationOrgToMaterial/edit') {
  return new Api('/pms').fetch(record, url, 'PUT');
}

// 物资下砖处理更新
export const handleUpdateMaterials = (record: any, params: any, field: string, value: string, resolve: (value: string) => void, url?: string, dicObject?: any, detailsData?: any, updateTable?: any) => {
  updateEditMaterials({
    ...params,
    [field]: value,
    repairOrgName: record?.name,
    name: record?.data?.userName,
    repairRound: detailsData?.repairRound,
    baseCode: detailsData?.baseCode,
  }, url).then((res) => {
    if (res) {
      updateTable();
    }
    resolve('');
  }).catch((error) => {
    resolve('');
  });
};

// 物资下转
export function openDataMaterialsForm(component: any, object: {
  record?: Record<string, any>,
  detailsData?: any,
  type?: any,
}): void {
  const drawerRef: Ref = ref();
  openModal({
    title: '物资信息',
    width: '80%',
    height: 650,
    content() {
      return h(component, {
        ref: drawerRef,
        record: object?.record,
        detailsData: object?.detailsData,
        materialDownEnum: object?.type,
      });
    },
    footer: null,
  });
}
