package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ReqDetail DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-28 17:18:32
 */
@ApiModel(value = "ReqDetailDTO对象", description = "富文本详情")
@Data
@ExcelIgnoreUnannotated
public class ReqDetailDTO extends  ObjectDTO   implements Serializable{

    /**
     * 需求ID
     */
    @ApiModelProperty(value = "需求ID")
    @ExcelProperty(value = "需求ID ", index = 0)
    private String reqId;

    /**
     * 富文本内容
     */
    @ApiModelProperty(value = "富文本内容")
    @ExcelProperty(value = "富文本内容 ", index = 1)
    private String context;




}
