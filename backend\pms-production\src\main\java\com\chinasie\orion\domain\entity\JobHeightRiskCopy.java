package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * JobHeightRiskCopy Entity对象
 *
 * <AUTHOR>
 * @since 2024-08-26 11:41:22
 */
@TableName(value = "pmsx_job_height_risk_copy")
@ApiModel(value = "JobHeightRiskCopyEntity对象", description = "作业高风险副本表")
@Data

public class JobHeightRiskCopy extends  ObjectEntity  implements Serializable{

    /**
     * 作业风险及判断标准
     */
    @ApiModelProperty(value = "作业风险及判断标准")
    @TableField(value = "operational_risks_json")
    private String operationalRisksJson;

    /**
     * 作业编号
     */
    @ApiModelProperty(value = "作业编号")
    @TableField(value = "job_number")
    private String jobNumber;

    /**
     * 工作主题
     */
    @ApiModelProperty(value = "工作主题")
    @TableField(value = "work_topics")
    private String workTopics;

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    @TableField(value = "work_order_no")
    private String workOrderNo;

    /**
     * 作业地点
     */
    @ApiModelProperty(value = "作业地点")
    @TableField(value = "job_address")
    private String jobAddress;

    /**
     * 作业地点名称
     */
    @ApiModelProperty(value = "作业地点名称")
    @TableField(value = "job_address_name")
    private String jobAddressName;

    /**
     * 计划开工时间
     */
    @ApiModelProperty(value = "计划开工时间")
    @TableField(value = "plan_commencement_date")
    private Date planCommencementDate;

    /**
     * 工作描述
     */
    @ApiModelProperty(value = "工作描述")
    @TableField(value = "job_content")
    private String jobContent;

    /**
     * 作业部门
     */
    @ApiModelProperty(value = "作业部门")
    @TableField(value = "operating_dept")
    private String operatingDept;

    /**
     * 项目负责人
     */
    @ApiModelProperty(value = "项目负责人")
    @TableField(value = "work_owner_name")
    private String workOwnerName;

    /**
     * 项目负责人电话
     */
    @ApiModelProperty(value = "项目负责人电话")
    @TableField(value = "work_owner_phone")
    private String workOwnerPhone;

    /**
     * 管理人
     */
    @ApiModelProperty(value = "管理人")
    @TableField(value = "manager_name")
    private String managerName;

    /**
     * 管理人id
     */
    @ApiModelProperty(value = "管理人id")
    @TableField(value = "manager_id")
    private String managerId;

    /**
     * 管理人电话
     */
    @ApiModelProperty(value = "管理人电话")
    @TableField(value = "manager_phone")
    private String managerPhone;

    /**
     * 作业过程状态
     */
    @ApiModelProperty(value = "作业过程状态")
    @TableField(value = "process_status")
    private String processStatus;

    /**
     * 当前环节
     */
    @ApiModelProperty(value = "当前环节")
    @TableField(value = "current_phase")
    private String currentPhase;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @TableField(value = "modify_time")
    private Date modifyTime;

    /**
     * 作业部门名称
     */
    @ApiModelProperty(value = "作业部门名称")
    @TableField(value = "operating_dept_name")
    private String operatingDeptName;

    /**
     * 监督人
     */
    @ApiModelProperty(value = "监督人")
    @TableField(value = "check_name")
    private String checkName;

    /**
     * 监督人id
     */
    @ApiModelProperty(value = "监督人id")
    @TableField(value = "check_id")
    private String checkId;

    /**
     * 监督人电话
     */
    @ApiModelProperty(value = "监督人电话")
    @TableField(value = "check_phone")
    private String checkPhone;

    /**
     * 转换的地址名称
     */
    @ApiModelProperty(value = "转换的地址名称")
    @TableField(exist = false)
    private String jobAddrName;
}
