package com.chinasie.orion.service;





import com.chinasie.orion.domain.dto.MajorPlanParamDTO;
import com.chinasie.orion.domain.dto.MajorRepairPlanMemberLevelDTO;
import com.chinasie.orion.domain.dto.MajorRepairPlanRoleDTO;
import com.chinasie.orion.domain.dto.permission.RoleMemberParamDTO;
import com.chinasie.orion.domain.entity.MajorRepairPlanMember;
import com.chinasie.orion.domain.dto.MajorRepairPlanMemberDTO;
import com.chinasie.orion.domain.vo.MajorRepairPlanMemberVO;
import java.lang.String;
import java.util.List;


import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import java.lang.String;
import java.util.Map;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * MajorRepairPlanMember 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30 19:20:50
 */
public interface MajorRepairPlanMemberService  extends  OrionBaseService<MajorRepairPlanMember>  {


        /**
         *  详情
         *
         * * @param id
         */
    MajorRepairPlanMemberVO detail(String id,String pageCode)throws Exception;

        /**
         *  新增
         *
         * * @param majorRepairPlanMemberDTO
         */
        String create(MajorRepairPlanMemberDTO majorRepairPlanMemberDTO)throws Exception;

        /**
         *  编辑
         *
         * * @param majorRepairPlanMemberDTO
         */
        Boolean edit(MajorRepairPlanMemberDTO majorRepairPlanMemberDTO)throws Exception;


        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;


        /**
         *  分页
         *
         * * @param pageRequest
         *
         */
        Page<MajorRepairPlanMemberVO> pages( Page<MajorRepairPlanMemberDTO> pageRequest)throws Exception;

    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<MajorRepairPlanMemberLevelDTO> pagesByLevel(Page<MajorRepairPlanRoleDTO> pageRequest)throws Exception;

        /**
         *  下载模板
         *
         * * @param response
         */
        void downloadExcelTpl(HttpServletResponse response)throws Exception;

        /**
         *  导入校验
         *
         * * @param file
         */
        ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


        /**
           *  确认导入
           *
           * * @param importId
           */
        Boolean importByExcel(String importId)throws Exception;

        /**
       *  取消导入
       *
       * * @param importId
       */
        Boolean importCancelByExcel(String importId)throws Exception;

        /**
            *  导出
            *
            * * @param searchConditions
            * * @param response
            */
        void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

        /**
            *  设置各种名称
            *
            * * @param searchConditions
            * * @param response
            */
        void  setEveryName(List<MajorRepairPlanMemberVO> vos)throws Exception;

    Boolean existByBusinessIds(List<String> ids);

    /**
     *  批量新增
     * @param roleMemberParamDTO
     * @return
     */
    Boolean batchAdd(RoleMemberParamDTO roleMemberParamDTO);

    /**
     *  获取 大修下 对应角色的用户ID列表
     * @param paramDTO
     * @return
     */
    List<String> getUserIdListByMajorParamDTO(MajorPlanParamDTO paramDTO);

    /**
     *  是否是指定角色的 管理 在大修中
     * @param mrcRoleCode
     * @param repRound
     * @return
     */
    Boolean isMange(String mrcRoleCode, String repRound);

    /**
     *  获取 指定角色列表下的用户列表
     * @param paramDTO
     * @return
     */
    Map<String,List<String>> getRoleCodeToUserIdListByMajorParamDTO(MajorPlanParamDTO paramDTO);

    /**
     *  是否是大修指挥部
     * @param majorRepairTurn
     * @return
     */
    Boolean isMajorRole(String majorRepairTurn);


    void syncPermission(String code, String rspUserId, String rspUserName, String repairRound,String oldRspUserId,String oldRspUserName);
}
