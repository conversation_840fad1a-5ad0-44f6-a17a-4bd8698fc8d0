import Api from '/@/api';

/**
 * 列表树
 * @param mainTableId 参数
 */
export const listTree = (mainTableId) => new Api(`/spm/taskDecomposition/list/tree/${mainTableId}`).fetch('', '', 'POST');
/**
 * 删除
 * @param params 参数
 */
export const remove = (params) => new Api('/spm/taskDecomposition/remove').fetch(params, '', 'DELETE');
/**
 * 删除
 * @param id 参数
 */
export const removeById = (id) => new Api(`/spm/taskDecomposition/${id}`).fetch('', '', 'DELETE');
/**
 * 编辑
 * @param params 参数
 */
export const edit = (params) => new Api('/spm/taskDecomposition/edit').fetch(params, '', 'PUT');
