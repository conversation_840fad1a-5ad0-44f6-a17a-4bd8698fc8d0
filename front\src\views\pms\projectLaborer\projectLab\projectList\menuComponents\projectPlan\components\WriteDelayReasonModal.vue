<template>
  <BasicModal
    :width="'600px'"
    title="计划超时原因"
    zIndex="999"
    @register="modalRegister"
    @ok="handleDistributeOk"
    @visible-change="handleVisibleChange"
  >
    <div class="dis-body">
      <div>
        <a-form
          ref="formRef"
          :model="form"
          layout="vertical"
        >
          <a-form-item
            label="计划超时情况说明"
            name="delayEndReason"
            required
          >
            <a-textarea
              v-model:value="form.delayEndReason"

              placeholder="请输入描述"
              :auto-size="{ minRows: 2, maxRows: 5 }"
            />
          </a-form-item>
        </a-form>
      </div>
      <!--      <div style="margin-top:24px">-->
      <!--        <BasicUpload-->
      <!--          :max-number="100"-->
      <!--          :isClassification="false"-->
      <!--          :isToolRequired="false"-->
      <!--          @uploadSuccessChange="uploadSuccessChange"-->
      <!--          @saveChange="saveChange"-->
      <!--        />-->
      <!--        <p class="m-b-t">-->
      <!--          支持的扩展名：.rar，.zip，.doc，.docx，.pdf，.jpg...-->
      <!--        </p>-->
      <!--      </div>-->
      <div class="field-list">
        <UploadList
          type="modal"
          :listData="fieldList"
          :onChange="onChange"
        />

        <!--        <template-->
        <!--          v-for="(item, index) in fieldList || []"-->
        <!--          :key="item.filePath"-->
        <!--        >-->
        <!--          <div class="field-item">-->
        <!--            <div class="field-item-name">-->
        <!--              {{ item.name + '.' + item.filePostfix }}-->
        <!--            </div>-->
        <!--            <i-->
        <!--              class="sie-icon-close"-->
        <!--              @click="deleteField(index)"-->
        <!--            />-->
        <!--          </div>-->
        <!--        </template>-->
      </div>
    </div>
    <SelectUserModal
      :on-ok="selectUserChange"
      @register="selectUserRegister"
    />
  </BasicModal>
</template>
<script lang="ts">
import { defineComponent, reactive, ref } from 'vue';
import {
  Form, FormItem, message, Textarea,
} from 'ant-design-vue';
import {
  BasicModal, SelectUserModal, UploadList, useModal, useModalInner,
} from 'lyra-component-vue3';
import Api from '/@/api';

export default defineComponent({
  name: 'PlanDone',
  components: {
    UploadList,
    SelectUserModal,
    BasicModal,
    ATextarea: Textarea,
    AForm: Form,
    AFormItem: FormItem,
  },
  emits: ['handleColse', 'updateForm'],
  setup(props, { emit }) {
    const notifyUserId = ref('');
    const fieldList = ref([]);
    const formRef = ref(null);
    const form = reactive({
      delayEndReason: '',
    });

    const projectSchemeId = ref([]);
    const [selectUserRegister, { openModal: selectUserOpenModal }] = useModal();
    // const remark = ref('');
    const [modalRegister, { closeModal }] = useModalInner(
      (rowData) => {
        // debugger
        projectSchemeId.value = rowData.id;
      },
    );
    const uploadSuccessChange = () => {
    };
    const saveChange = (val) => {
      fieldList.value = val.map((item) => item.responseData.result);
    };
    const deleteField = (index) => {
      fieldList.value.splice(index, 1);
    };
    function handleVisibleChange(visible: boolean) {
      fieldList.value = null;

      form.delayEndReason = '';
    }

    // 计划下发确认
    const handleDistributeOk = async () => {
      const res = await formRef.value.validate();
      if (res) {
        const params = {
          id: projectSchemeId.value,
          delayEndReason: form.delayEndReason,
          attachments: fieldList.value,
        };
        new Api('/pms')
          .fetch(params, 'projectScheme/writeDelayReason', 'POST')
          .then((res) => {
            if (res) {
              message.success('成功');
              formRef.value.resetFields();
              emit('updateForm');
              closeModal();
            }
          });
      }
    };
    function onChange(listData) {
      fieldList.value = listData;
    }
    return {
      modalRegister,
      handleDistributeOk,

      selectUserRegister,
      notifyUserId,
      saveChange,
      uploadSuccessChange,
      deleteField,
      fieldList,
      handleVisibleChange,
      form,
      formRef,
      onChange,
    };
  },
});
</script>
<style lang="less" scoped>
.dis-body {
  padding: 22px 22px 30px;

  .flex-box {
    display: flex;
    align-items: center;
    margin-top: 10px;

    > span {
      margin-right: 10px;
    }
  }
}
.field-list {
  .field-item {
    display: flex;
    height: 30px;
    line-height: 30px;
    margin-top: 10px;

    &:hover {
      .sie-icon-close {
        display: inline-block;
      }
    }

    .field-item-name {
      flex: 1;
    }

    .sie-icon-close {
      display: none;
      cursor: pointer;
    }
  }
}
</style>
