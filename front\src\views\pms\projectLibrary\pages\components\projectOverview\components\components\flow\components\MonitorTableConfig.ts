/**
 * 根据节点ID获取是否显示查看单据按钮
 */
export function getIsShowViewDocumentsByNodeId(nodeId: string) {
  if (!nodeId) {
    return false;
  }
  const showIds = [
    '0-1',
    '0-2',
    '0-3',
    '1-1',
    '1-2',
    '1-3',
    '1-4',
    '1-5',
    '1-6',
    '2-5',
    '3-1',
    '3-2',
    '3-2-1',
    '3-2-2',
    '3-2-3',
    '3-3',
    '3-4',
    '4-1',
  ];
  return showIds.includes(nodeId);
}

/**
 * 根据节点ID获取是否显示表格
 */
export function getIsShowTableByNodeId(nodeId: string) {
  if (!nodeId) {
    return false;
  }
  const showIds = [
    '2-1',
    '2-2',
    '2-3',
    '2-4',
    '2-6',
    '4-2',
    '4-3',
  ];
  return showIds.includes(nodeId);
}

/**
 * 根据节点ID获取是否显示基本信息
 */
export function getIsShowBaseInfoByNodeId(nodeId: string) {
  if (!nodeId) {
    return false;
  }
  const showIds = [
    '0-1',
    '0-2',
    '0-3',
    '1-1',
    '1-2',
    '1-3',
    '1-4',
    '1-5',
    '1-6',
    '3-2-3',
    '4-1',
  ];
  return showIds.includes(nodeId);
}
