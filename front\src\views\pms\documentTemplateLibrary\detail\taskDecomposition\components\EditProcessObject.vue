<script setup lang="ts">
import { nextTick, ref } from 'vue';
import { Select, Tag } from 'ant-design-vue';

const props = defineProps({
  processObject: {
    type: String,
    default: '',
  },
  processObjectName: {
    type: String,
    default: '',
  },
  options: {
    type: Array,
    default: () => ([]),
  },
});

const colors = {
  taskDecomposition_1: '#87d068', // 协同编制
  taskDecomposition_2: '#ff3299', //  收益策划
  taskDecomposition_3: '#108ee9', //  概算策划
  taskDecomposition_4: '#f50', //  风险策划
  taskDecomposition_5: '#722ed1', //  里程碑策划
};

const emit = defineEmits(['change']);
const isEdit = ref(false);
const processObject_ = ref('');
const processObjectName_ = ref('');
const refSelect = ref();
const handleBlur = () => {
  isEdit.value = false;
};

const handleMouseenter = async () => {
  isEdit.value = true;
  await nextTick();
  refSelect.value.focus();
  processObject_.value = props.processObject;
  processObjectName_.value = props.processObjectName;
};

const handleChange = (value, { label }) => {
  emit('change', {
    value,
    label,
  });
};

</script>

<template>
  <div
    v-if="!isEdit"
    class="flex-te"
    style="cursor: pointer;"
    :title="processObjectName"
  >
    <Tag
      :color="colors[processObject]"
      @mouseenter="handleMouseenter"
    >
      {{ processObjectName?processObjectName:'--' }}
    </Tag>
  </div>

  <Select
    v-else
    ref="refSelect"
    v-model:value="processObject_"
    style="width: 100%"
    :options="options"
    placeholder="请选择"
    @change="handleChange"
    @blur="handleBlur"
  />
</template>

<style scoped lang="less">

</style>
