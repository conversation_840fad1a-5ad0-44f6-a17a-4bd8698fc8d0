package com.chinasie.orion.controller.reporting;

import com.chinasie.orion.domain.dto.reporting.AuditParamDTO;
import com.chinasie.orion.domain.dto.reporting.ExportParamDTO;
import com.chinasie.orion.domain.dto.reporting.ProjectDailyStatementDTO;
import com.chinasie.orion.domain.request.reporting.ListDailyRequest;
import com.chinasie.orion.domain.vo.StatusVo;
import com.chinasie.orion.domain.vo.reporting.ProjectDailyCardVO;
import com.chinasie.orion.domain.vo.reporting.DailyStatementCardVO;
import com.chinasie.orion.domain.vo.reporting.ProjectDailyStatementVO;
import com.chinasie.orion.service.reporting.ProjectDailyStatementService;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.reporting.ProjectDailyStatementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <p>
 * ProjectDailyStatement 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-14 15:21:01
 */
@RestController
@RequestMapping("/projectDaily-statement")
@Api(tags = "项目日报-项目的接口已过时请查看通过管理相关接口")
public class ProjectDailyStatementController {

    @Resource
    private ProjectDailyStatementService projectDailyStatementService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Deprecated
    public ResponseDTO<ProjectDailyStatementVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        ProjectDailyStatementVO rsp = projectDailyStatementService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }



    /**
     * 详情
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "通过当前日期获取当前人是否有已创建的日报信息")
    @RequestMapping(value = "/today/info", method = RequestMethod.GET)
    @Deprecated
    public ResponseDTO<ProjectDailyStatementVO> todayInfo(@RequestParam("day") String day,
                                                          @RequestParam("projectId")String projectId) throws Exception {
        ProjectDailyStatementVO rsp = projectDailyStatementService.todayInfo(day, projectId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectDailyStatementDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @Deprecated
    public ResponseDTO<ProjectDailyStatementVO> create(@RequestBody ProjectDailyStatementDTO projectDailyStatementDTO) throws Exception {
        ProjectDailyStatementVO rsp =  projectDailyStatementService.create(projectDailyStatementDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectDailyStatementDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @Deprecated
    public ResponseDTO<Boolean> edit(@RequestBody  ProjectDailyStatementDTO projectDailyStatementDTO) throws Exception {
        Boolean rsp = projectDailyStatementService.edit(projectDailyStatementDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @Deprecated
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectDailyStatementService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页-列表")
    @RequestMapping(value = "/pages", method = RequestMethod.POST)
    @Deprecated
    public ResponseDTO<Page<ProjectDailyStatementVO>> pages(@RequestBody Page<ListDailyRequest> pageRequest) throws Exception {
        Page<ProjectDailyStatementVO> rsp =  projectDailyStatementService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "提交")
    @RequestMapping(value = "/submit/{id}", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @Deprecated
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = projectDailyStatementService.submitById(id);
        return new ResponseDTO(rsp);
    }


    @ApiOperation(value = "批量审核通过")
    @RequestMapping(value = "/audit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @Deprecated
    public ResponseDTO<Boolean> audit(@RequestBody AuditParamDTO auditParamDTO) throws Exception {
        Boolean rsp = projectDailyStatementService.audit(auditParamDTO);
        return new ResponseDTO(rsp);
    }


    @ApiOperation(value = "批量审核驳回")
    @RequestMapping(value = "/reject", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @Deprecated
    public ResponseDTO<Boolean> reject(@RequestBody List<String> idList) throws Exception {
        Boolean rsp = projectDailyStatementService.reject(idList);
        return new ResponseDTO(rsp);
    }

    @ApiOperation(value = "导出")
    @RequestMapping(value = "/export", method = RequestMethod.POST)
    @Deprecated
    public void exportData(@RequestBody ExportParamDTO exportParamDTO, HttpServletResponse response) throws Exception {
         projectDailyStatementService.exportData(exportParamDTO,response);
    }


    @ApiOperation(value = "卡片-列表")
    @RequestMapping(value = "/card/list", method = RequestMethod.POST)
    @Deprecated
    public ResponseDTO<List<DailyStatementCardVO>> cardList(@RequestBody ListDailyRequest pageRequest) throws Exception {
        List<DailyStatementCardVO> rsp =  projectDailyStatementService.cardList(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 审核卡片
     *
     * @param request
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "审核卡片")
    @RequestMapping(value = "/checkCard", method = RequestMethod.POST)
    @Deprecated
    public ResponseDTO<TreeMap<Date, ProjectDailyCardVO>> checkCard(@RequestBody ListDailyRequest request) throws Exception {
        TreeMap<Date, ProjectDailyCardVO> rsp =  projectDailyStatementService.checkCard(request);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 提醒
     *
     * @param id 日报id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "提醒")
    @RequestMapping(value = "/warn", method = RequestMethod.GET)
    @Deprecated
    public ResponseDTO<Boolean> checkCard(@RequestParam("id") String id, @RequestParam("url")String url) throws Exception {
        Boolean rsp =  projectDailyStatementService.warn(id,url);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 获取进度状态
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取进度状态")
    @RequestMapping(value = "/get-status", method = RequestMethod.GET)
    @Deprecated
    public ResponseDTO<List<StatusVo>> getStatus() {
        List<StatusVo> rsp =  projectDailyStatementService.getStatus();
        return new ResponseDTO<>(rsp);
    }
}
