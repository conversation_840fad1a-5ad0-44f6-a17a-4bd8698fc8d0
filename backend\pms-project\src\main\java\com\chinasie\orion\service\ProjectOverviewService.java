package com.chinasie.orion.service;

import com.chinasie.orion.domain.vo.MilestoneVo;
import com.chinasie.orion.domain.vo.projectOverview.*;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/07/9:19
 * @description:
 */
public interface ProjectOverviewService {

    /**
     * 获取 项目信息
     * @return
     */
    ProjectOverviewVo getOverviewProjectInfo(String projectId) throws Exception;

    /**
     *  项目工时统计
     * @param projectId
     * @return
     */
    ProjectPlanManHourVo getProjectManHourCount(String projectId) throws Exception;

    /**
     *  获取项目交付物统计
     * @param projectId
     * @return
     */
    ProjectDeliverCount getProjectDeliverCount(String projectId) throws Exception;

    /**
     *  获取未来两周的 工作量 按天计算
     * @param projectId
     * @return
     */
    List<ProjectDayCountVo> afterWorkCount(String projectId) throws Exception;

    /**
     *  获取计划类型统计
     * @param projectId
     * @return
     *  List<ProjectPlanTypeCountVo>
     */
   ProjectViewVo<ProjectPlanTypeCountVo> planTypeCount(String projectId) throws Exception;

    /**
     *  获取 项目下 里程碑列表
     * @param projectId
     * @return
     */
    ProjectViewVo<MilestoneVo> milestone(String projectId) throws Exception;
}
