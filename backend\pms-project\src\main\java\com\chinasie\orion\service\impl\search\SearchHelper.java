package com.chinasie.orion.service.impl.search;

import com.chinasie.orion.bo.IdAnalysisBo;
import com.chinasie.orion.search.client.service.SearchService;
import com.chinasie.orion.search.common.mq.DataChangeMessage;
import com.chinasie.orion.search.common.mq.FileChangeMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Map;

@Component
public class SearchHelper {
    /**
     * PMS 索引 library 标志.
     */
    public static final String PMS_LIBRARY = "PMS";

    /**
     * PMS 索引 module 标志.
     */
    public static final String PMS_MODULE = "PMS_DATA";


    @Autowired
    private SearchService searchService;

    @Resource
    private IdAnalysisBo idAnalysisBo;

    public DataChangeMessage pmsDataChangeMessage(String dataId, String module) {
        DataChangeMessage msg = new DataChangeMessage();
        msg.setLibrary(PMS_LIBRARY);
        msg.setModule(PMS_MODULE);
        if(StringUtils.hasText(module) && module.endsWith("_DATA") ){
            msg.setModule(module);
        }
        else if(StringUtils.hasText(module)){
            msg.setModule(module+"_DATA");
        }

        msg.setDataId(dataId);
        return msg;
    }


    public DataChangeMessage sendDataChangeMessage(String dataId){
        if(!StringUtils.hasText(dataId)){
            return null;
        }
        Map<String, String> idToClassNameMap = idAnalysisBo.getClassName(Arrays.asList(dataId));
        String module = idToClassNameMap.get(dataId);
        DataChangeMessage msg = new DataChangeMessage();
        msg.setLibrary(PMS_LIBRARY);
        msg.setModule(PMS_MODULE);
        if(StringUtils.hasText(module) && module.endsWith("_DATA") ){
            msg.setModule(module);
        }
        else if(StringUtils.hasText(module)){
            msg.setModule(module+"_DATA");
        }

        msg.setDataId(dataId);

        searchService.sendDataChangeMessage(msg);
        return msg;
    }


    public FileChangeMessage pmsFileChangeMessage(String fileId, String dataId, String module) {
        FileChangeMessage msg = new FileChangeMessage();
        msg.setLibrary(PMS_LIBRARY);
        msg.setModule(PMS_MODULE);
        if(StringUtils.hasText(module) && module.endsWith("_DATA") ){
            msg.setModule(module);
        }
        else if(StringUtils.hasText(module)){
            msg.setModule(module+"_DATA");
        }
        msg.setDataId(dataId);
        msg.setId(fileId);
        return msg;
    }
}
