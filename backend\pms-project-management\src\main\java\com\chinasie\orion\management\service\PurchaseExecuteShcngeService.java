package com.chinasie.orion.management.service;


import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.management.domain.dto.PurchaseExecuteShcngeDTO;
import com.chinasie.orion.management.domain.entity.PurchaseExecuteShcnge;
import com.chinasie.orion.management.domain.vo.PurchaseExecuteShcngeVO;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * PurchaseExecuteShcnge 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
public interface PurchaseExecuteShcngeService extends OrionBaseService<PurchaseExecuteShcnge> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    PurchaseExecuteShcngeVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param purchaseExecuteShcngeDTO
     */
    String create(PurchaseExecuteShcngeDTO purchaseExecuteShcngeDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param purchaseExecuteShcngeDTO
     */
    Boolean edit(PurchaseExecuteShcngeDTO purchaseExecuteShcngeDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     *
     * @param mainTableId
     */
    Page<PurchaseExecuteShcngeVO> pages(String mainTableId, Page<PurchaseExecuteShcngeDTO> pageRequest) throws Exception;

    /**
     * 根据合同编号查询采购执行变更信息
     * <p>
     * * @param code
     */
    Page<PurchaseExecuteShcngeVO> getByCode(Page<PurchaseExecuteShcngeDTO> pageRequest) throws Exception;


    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<PurchaseExecuteShcngeVO> vos) throws Exception;
}
