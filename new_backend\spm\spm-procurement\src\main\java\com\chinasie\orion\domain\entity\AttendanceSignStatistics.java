package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "AttendanceSignStatistics对象", description = "出勤签到统计")
@Data
public class AttendanceSignStatistics {

    /**
     * 季度
     */
    @ApiModelProperty(value = "季度")
    @TableField(value = "dataQuarter")
    private Integer dataQuarter;

    /**
     * 中心编号
     */
    @ApiModelProperty(value = "中心编号")
    @TableField(value = "org_code")
    private String orgCode;

    /**
     * 中心名称
     */
    @ApiModelProperty(value = "中心名称")
    @TableField(value = "org_name")
    private String orgName;


    /**
     * 岗级成本
     */
    @ApiModelProperty(value = "岗级成本")
    @TableField(value = "unit_price")
    private BigDecimal unitPrice;

    /**
     * 工作量 (人/月)
     */
    @ApiModelProperty(value = "工作量 (人/月)")
    @TableField(value = "attandance_rate")
    private BigDecimal attandanceRate;

}
