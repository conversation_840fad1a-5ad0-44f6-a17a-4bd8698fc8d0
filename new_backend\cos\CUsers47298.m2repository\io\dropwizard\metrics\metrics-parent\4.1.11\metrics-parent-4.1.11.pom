<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>io.dropwizard.metrics</groupId>
    <artifactId>metrics-parent</artifactId>
    <version>4.1.11</version>
    <packaging>pom</packaging>
    <name>Metrics Parent</name>
    <description>
        The Metrics library.
    </description>
    <url>https://metrics.dropwizard.io</url>

    <modules>
        <module>docs</module>
        <module>metrics-bom</module>
        <module>metrics-annotation</module>
        <module>metrics-benchmarks</module>
        <module>metrics-caffeine</module>
        <module>metrics-core</module>
        <module>metrics-collectd</module>
        <module>metrics-ehcache</module>
        <module>metrics-graphite</module>
        <module>metrics-healthchecks</module>
        <module>metrics-httpclient</module>
        <module>metrics-httpclient5</module>
        <module>metrics-httpasyncclient</module>
        <module>metrics-jcache</module>
        <module>metrics-jcstress</module>
        <module>metrics-jdbi</module>
        <module>metrics-jdbi3</module>
        <module>metrics-jersey2</module>
        <module>metrics-jetty9</module>
        <module>metrics-jmx</module>
        <module>metrics-json</module>
        <module>metrics-jvm</module>
        <module>metrics-log4j2</module>
        <module>metrics-logback</module>
        <module>metrics-servlet</module>
        <module>metrics-servlets</module>
    </modules>

    <properties>
        <project.build.outputTimestamp>2011-02-24T06:12:03Z</project.build.outputTimestamp>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <slf4j.version>1.7.30</slf4j.version>
        <assertj.version>3.16.1</assertj.version>
        <mockito.version>3.4.2</mockito.version>
        <junit.version>4.12</junit.version>
        <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
        <errorprone.javac.version>9+181-r4173-1</errorprone.javac.version>
    </properties>

    <developers>
        <developer>
            <name>Coda Hale</name>
            <email><EMAIL></email>
            <timezone>America/Los_Angeles</timezone>
            <roles>
                <role>architect</role>
            </roles>
        </developer>
        <developer>
            <name>Ryan Tenney</name>
            <email><EMAIL></email>
            <timezone>America/New_York</timezone>
            <roles>
                <role>committer</role>
            </roles>
        </developer>
        <developer>
            <name>Artem Prigoda</name>
            <email><EMAIL></email>
            <timezone>Europe/Berlin</timezone>
            <roles>
                <role>committer</role>
            </roles>
        </developer>
    </developers>

    <licenses>
        <license>
            <name>Apache License 2.0</name>
            <url>https://www.apache.org/licenses/LICENSE-2.0.html</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <scm>
        <connection>scm:git:git://github.com/dropwizard/metrics.git</connection>
        <developerConnection>scm:git:**************:dropwizard/metrics.git</developerConnection>
        <url>https://github.com/dropwizard/metrics/</url>
        <tag>v4.1.11</tag>
    </scm>

    <issueManagement>
        <system>github</system>
        <url>https://github.com/dropwizard/metrics/issues/</url>
    </issueManagement>

    <distributionManagement>
        <snapshotRepository>
            <id>sonatype-nexus-snapshots</id>
            <name>Sonatype Nexus Snapshots</name>
            <url>https://oss.sonatype.org/content/repositories/snapshots</url>
        </snapshotRepository>
        <repository>
            <id>sonatype-nexus-staging</id>
            <name>Nexus Release Repository</name>
            <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
        </repository>
    </distributionManagement>

    <dependencies>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>${slf4j.version}</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <version>${assertj.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-simple</artifactId>
            <version>${slf4j.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>jdk8</id>
            <activation>
                <jdk>1.8</jdk>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-compiler-plugin</artifactId>
                        <version>${maven-compiler-plugin.version}</version>
                        <configuration>
                            <fork>true</fork>
                            <compilerArgs combine.children="append">
                                <arg>-J-Xbootclasspath/p:${settings.localRepository}/com/google/errorprone/javac/${errorprone.javac.version}/javac-${errorprone.javac.version}.jar</arg>
                            </compilerArgs>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>release-sign-artifacts</id>
            <activation>
                <property>
                    <name>performRelease</name>
                    <value>true</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>1.6</version>
                        <configuration>
                            <gpgArguments>
                                <argument>--no-tty</argument>
                            </gpgArguments>
                        </configuration>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

    </profiles>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>2.8.2</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <showWarnings>true</showWarnings>
                    <compilerArgs>
                        <arg>-Xlint:all</arg>
                        <arg>-XDcompilePolicy=simple</arg>
                        <arg>-Xplugin:ErrorProne -XepExcludedPaths:.*/target/generated-sources/.*</arg>
                    </compilerArgs>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>com.google.errorprone</groupId>
                            <artifactId>error_prone_core</artifactId>
                            <version>2.4.0</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>5.1.1</version>
                <extensions>true</extensions>
                <configuration>
                    <instructions>
                        <Import-Package><![CDATA[
                            javax.servlet*;version="[2.5.0,4.0.0)",
                            org.slf4j*;version="[1.6.0,2.0.0)",
                            sun.misc.*;resolution:=optional,
                            com.sun.management.*;resolution:=optional,
                            *
                        ]]>
                        </Import-Package>
                        <!-- https://stackoverflow.com/a/29853163/49505 -->
                        <_removeheaders>Bnd-LastModified</_removeheaders>
                        <_reproducible>true</_reproducible>
                    </instructions>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <configuration>
                    <argLine>-Djava.net.preferIPv4Stack=true</argLine>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>1.4.1</version>
                <executions>
                    <execution>
                        <id>enforce</id>
                        <configuration>
                            <rules>
                                <DependencyConvergence />
                            </rules>
                        </configuration>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>3.1.2</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>3.2.0</version>
                <configuration>
                    <source>8</source>
                    <doclint>none</doclint>
                    <quiet>true</quiet>
                    <notimestamp>true</notimestamp>
                </configuration>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>2.5.3</version>
                <configuration>
                    <autoVersionSubmodules>true</autoVersionSubmodules>
                    <mavenExecutorId>forked-path</mavenExecutorId>
                    <tagNameFormat>v@{project.version}</tagNameFormat>
                    <preparationGoals>clean test</preparationGoals>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.2.0</version>
                <configuration>
                    <archive>
                        <manifest>
                            <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                        </manifest>
                        <manifestEntries>
                            <Automatic-Module-Name>${javaModuleName}</Automatic-Module-Name>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>3.1.1</version>
                <configuration>
                    <configLocation>checkstyle.xml</configLocation>
                    <includeTestSourceDirectory>true</includeTestSourceDirectory>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-site-plugin</artifactId>
                <version>3.9.1</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-project-info-reports-plugin</artifactId>
                <version>3.1.0</version>
            </plugin>
        </plugins>
    </build>
</project>
