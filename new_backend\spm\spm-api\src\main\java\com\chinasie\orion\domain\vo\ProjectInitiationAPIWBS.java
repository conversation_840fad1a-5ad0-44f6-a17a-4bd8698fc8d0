package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ProjectInitiationWBS Entity对象
 *
 * <AUTHOR>
 * @since 2024-07-16 14:52:10
 */
@ApiModel(value = "ProjectInitiationWBSEntity对象", description = "项目立项WBS预算")
@Data

public class ProjectInitiationAPIWBS extends ObjectEntity implements Serializable {

    /**
     * 项目层次等级
     */
    @ApiModelProperty(value = "项目层次等级")
    @TableField(value = "project_level")
    private String projectLevel;

    /**
     * 工作分解结构元素 (WBS 元素)
     */
    @ApiModelProperty(value = "工作分解结构元素 (WBS 元素)")
    @TableField(value = "wbs_element")
    private String wbsElement;

    /**
     * PS: 短描述 (第一行文本)
     */
    @ApiModelProperty(value = "PS: 短描述 (第一行文本)")
    @TableField(value = "description")
    private String description;

    /**
     * 功能范围
     */
    @ApiModelProperty(value = "功能范围")
    @TableField(value = "functional_scope")
    private String functionalScope;

    /**
     * I开头状态
     */
    @ApiModelProperty(value = "I开头状态")
    @TableField(value = "initial_status")
    private String initialStatus;

    /**
     * 利润中心编码
     */
    @ApiModelProperty(value = "利润中心编码")
    @TableField(value = "profit_center_code")
    private String profitCenterCode;

    /**
     * 利润中心名称
     */
    @ApiModelProperty(value = "利润中心名称")
    @TableField(value = "profit_center_name")
    private String profitCenterName;

    /**
     * 工厂
     */
    @ApiModelProperty(value = "工厂")
    @TableField(value = "company")
    private String company;

    /**
     * 负责人编号
     */
    @ApiModelProperty(value = "负责人编号")
    @TableField(value = "director_code")
    private String directorCode;

    /**
     * 负责人姓名（项目管理者）
     */
    @ApiModelProperty(value = "负责人姓名（项目管理者）")
    @TableField(value = "director_name")
    private String directorName;

    /**
     * 立项编号
     */
    @ApiModelProperty(value = "立项编号")
    @TableField(value = "project_number")
    private String projectNumber;

    /**
     * 业务分类
     */
    @ApiModelProperty(value = "业务分类")
    @TableField(value = "business")
    private String business;

    /**
     * 业务分类名称
     */
    @ApiModelProperty(value = "业务分类名称")
    @TableField(value = "business_name")
    private String businessName;

}

