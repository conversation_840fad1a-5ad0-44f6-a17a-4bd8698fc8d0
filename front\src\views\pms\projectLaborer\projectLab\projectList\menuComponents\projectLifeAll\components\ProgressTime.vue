<script lang="ts">
import { defineComponent } from 'vue';
import { CheckCircleFilled } from '@ant-design/icons-vue';

export default defineComponent({
  components: { CheckCircleFilled },
  props: {
    actions: {
      type: Array,
      default() {
        return [];
      },
    },
  },
});
</script>

<template>
  <div class="content-box">
    <div
      v-for="item in actions"
      :key="item.id"
      class="cont"
    >
      <div class="label-name">
        {{ item.name }}
      </div>
      <!--已完成-->
      <CheckCircleFilled
        v-if="item.isFinish"
        class="circle2"
      />
      <!--未完成-->
      <div
        v-else
        class="circle1"
      />
    </div>
  </div>
</template>

<style scoped lang="less">
.content-box{
  width: 250px;
  max-height: 200px;
  overflow: scroll;
  padding:0 10px;
  .cont{
    float: left;
    padding-left: 20px;
    position: relative;
    width: 100%;
    border-left: 2px solid #999;

    .label-name{
      padding: 5px 0;
    }
    .circle1{
      border:2px solid #1890ff;
      width: 20px;
      height: 20px;
      padding-top: 4px;
      background: #fff;
      border-radius: 50%;
      position: absolute;
      left: -11px;
      top: 8px;
    }
    .circle2{
      font-size: 20px;
      width: 20px;
      height: 20px;
      background: #fff;
      color: #1890ff;
      border-radius: 50%;
      position: absolute;
      left: -11px;
      top: 8px;
    }
  }
}

</style>
