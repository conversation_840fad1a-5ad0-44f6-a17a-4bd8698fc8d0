package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * OpenCost VO对象
 *
 * <AUTHOR>
 * @since 2024-10-28 15:17:25
 */
@ApiModel(value = "OpenCostVO对象", description = "开口项费用")
@Data
public class OpenCostVO extends  ObjectVO   implements Serializable{

    /**
     * 年
     */
    @ApiModelProperty(value = "年")
    private String dataYear;

    /**
     * 月
     */
    @ApiModelProperty(value = "月")
    private Integer dataMonth;

    /**
     * 季度
     */
    @ApiModelProperty(value = "季度")
    private Integer dataQuarter;

    /**
     * 中心编号
     */
    @ApiModelProperty(value = "中心编号")
    private String orgCode;


    /**
     * 中心名称
     */
    @ApiModelProperty(value = "中心名称")
    private String orgName;


    /**
     * 部门编号
     */
    @ApiModelProperty(value = "部门编号")
    private String deptCode;


    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String deptName;


    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNo;


    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;


    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    private String userCode;


    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String userName;


    /**
     * 供应商编号
     */
    @ApiModelProperty(value = "供应商编号")
    private String supplierNo;


    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;


    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    private Date payDate;


    /**
     * 费用类别编号
     */
    @ApiModelProperty(value = "费用类别编号")
    private String payTypeNo;


    /**
     * 费用类别名称
     */
    @ApiModelProperty(value = "费用类别名称")
    private String payTypeName;


    /**
     * 费用金额
     */
    @ApiModelProperty(value = "费用金额")
    private BigDecimal payAmt;




}
