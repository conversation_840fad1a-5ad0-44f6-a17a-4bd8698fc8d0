package com.chinasie.orion.controller;


import com.chinasie.orion.domain.dto.ProjectMaterialPreparationDTO;
import com.chinasie.orion.domain.vo.ProjectMaterialPreparationInfoImportVO;
import com.chinasie.orion.domain.vo.ProjectMaterialPreparationVO;
import com.chinasie.orion.service.ProjectMaterialPreparationInfoService;
import com.chinasie.orion.service.ProjectMaterialPreparationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;

/**
 * <p>
 * ProjectMaterialPreparation 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-25 15:41:46
 */
@RestController
@RequestMapping("/projectMaterialPreparation")
@Api(tags = "备料与加工申请")
public class  ProjectMaterialPreparationController  {

    @Autowired
    private ProjectMaterialPreparationService projectMaterialPreparationService;

    @Autowired
    private ProjectMaterialPreparationInfoService projectMaterialPreparationInfoService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据【{{#id}}】", type = "备料与加工申请", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectMaterialPreparationVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        ProjectMaterialPreparationVO rsp = projectMaterialPreparationService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectMaterialPreparationDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#projectMaterialPreparationDTO.name}}】", type = "备料与加工申请", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody @Validated ProjectMaterialPreparationDTO projectMaterialPreparationDTO, @RequestParam(value = "submit", required = false, defaultValue = "0") Boolean submit) throws Exception {
        String rsp =  projectMaterialPreparationService.create(projectMaterialPreparationDTO, submit);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectMaterialPreparationDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#projectMaterialPreparationDTO.name}}】", type = "备料与加工申请", subType = "编辑", bizNo = "{{#projectMaterialPreparationDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  @Validated ProjectMaterialPreparationDTO projectMaterialPreparationDTO, @RequestParam(value = "submit", required = false, defaultValue = "0") Boolean submit) throws Exception {
        Boolean rsp = projectMaterialPreparationService.edit(projectMaterialPreparationDTO, submit);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "备料与加工申请", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = projectMaterialPreparationService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "备料与加工申请", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectMaterialPreparationService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "备料与加工申请", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ProjectMaterialPreparationVO>> pages(@RequestBody Page<ProjectMaterialPreparationDTO> pageRequest) throws Exception {
        Page<ProjectMaterialPreparationVO> rsp =  projectMaterialPreparationService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 变更
     * @param projectMaterialPreparationDTO
     * @param submit
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "变更")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】变更了数据", type = "备料与加工申请", subType = "变更", bizNo = "")
    @RequestMapping(value = "/upgrade", method = RequestMethod.POST)
    public ResponseDTO<String> upgrade(@RequestBody ProjectMaterialPreparationDTO projectMaterialPreparationDTO, @RequestParam(value = "submit", required = false, defaultValue = "0" ) Boolean submit) throws Exception {
        String rsp =  projectMaterialPreparationService.upgrade(projectMaterialPreparationDTO, submit);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 获取版本记录
     * @param revKey
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取版本记录")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "备料与加工申请", subType = "版本记录", bizNo = "")
    @RequestMapping(value = "/allRev/list", method = RequestMethod.GET)
    public ResponseDTO<List<ProjectMaterialPreparationVO>> getAllRevList(@RequestParam("revKey") String revKey) throws Exception {
        List<ProjectMaterialPreparationVO> rsp =  projectMaterialPreparationService.getAllRevList( revKey);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("备料与加工申请信息导入校验（Excel）")
    @PostMapping(value = "/info/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "ProjectMaterialPreparationInfo", subType = "校验导入", bizNo = "")
    public ResponseDTO<ProjectMaterialPreparationInfoImportVO> importInfoCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ProjectMaterialPreparationInfoImportVO rsp = projectMaterialPreparationInfoService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("备料与加工申请信息导入下载模板(Excel)")
    @GetMapping(value = "/info/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "ProjectMaterialPreparationInfo", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        projectMaterialPreparationInfoService.downloadExcelTpl(response);
    }

}
