package com.chinasie.orion.domain.vo.tree;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/11/18/16:32
 * @description:
 */
@Data
public class RelationOrgJobWorkVO implements Serializable {
    @ApiModelProperty(value = "大修组织ID")
    private String repairOrgId;
    @ApiModelProperty(value = "是否重大项目")
    private Boolean isMajorProject;
    @ApiModelProperty(value = "工单名称")
    private String  jobName;
    @ApiModelProperty(value = "关系ID")
    private String  relationId;
    @ApiModelProperty(value = "工单id")
    private String  jobId;
    @ApiModelProperty(value = "工单号")
    private String  jobNumber;
    @ApiModelProperty(value = "大修伦次")
    private String  repairRound;
    @ApiModelProperty(value = "阶段")
    private String  phase;
    @ApiModelProperty(value = "责任人id")
    private String  rspUserId;
    @ApiModelProperty(value = "责任人名称")
    private String  rspUserName;
    @ApiModelProperty(value = "责任人工号")
    private String  rspUserCode;

    @ApiModelProperty(value = "实际开始时间")
    private Date actualBeginTime;
    @ApiModelProperty(value = "开工ID")
    private String workId;
    /**
     * 开工日期
     */
    @ApiModelProperty(value = "开工日期")
    private Date startWorkDate;
    @ApiModelProperty(value = "开工日期-字符")
    private String startWorkDateStr;

    /**
     * 上午开工状态
     */
    @ApiModelProperty(value = "上午开工状态")
    private Boolean morningStatus;


    /**
     * 下午开工状态
     */
    @ApiModelProperty(value = "下午开工状态")
    private Boolean afternoonStatus;


    /**
     * 夜间状态
     */
    @ApiModelProperty(value = "夜间状态")
    private Boolean nightStatus;

    @ApiModelProperty(value = "实际结束时间")
    private Date actualEndTime;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
