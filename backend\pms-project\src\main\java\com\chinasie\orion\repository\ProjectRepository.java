package com.chinasie.orion.repository;

import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.vo.ProjectBudgetPracticalViewVO;
import com.chinasie.orion.domain.vo.ProjectSimpleBasicVO;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import com.chinasie.orion.search.common.domain.IndexData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/06/10:38
 * @description:
 */
@Mapper
public interface ProjectRepository extends OrionBaseMapper<Project> {
    @Select({
            "<script>",
            "SELECT project_id projectId, COALESCE ( round( sum( year_expense ), 2 ), 0 ) AS budget,COALESCE ( round( sum( total_cost ), 2 ), 0 ) AS practical,CONCAT( ROUND ( COALESCE ( SUM( total_cost ) / SUM( year_expense ), 0 ) * 100, 2 ), '%' ) AS percent FROM pmsx_bud_project_budget WHERE year = #{annual} and logic_status != -1 and project_id in ",
            "<foreach collection='projectIdList' item='item' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "GROUP BY project_id",
            "</script>"
    })
    List<ProjectBudgetPracticalViewVO> findProjectBudgetStatisticList(@Param("annual") int annual, @Param("projectIdList") List<String> projectIdList);

    /**
     * 获取自上次索引依赖新增的可索引的EDM数据.
     *
     * @param lastIndexTime 上次索引时间
     * @param limitSize
     * @return
     */
    List<IndexData> fetchIndexData(@Param("lastIndexTime") Date lastIndexTime, @Param("limitSize") Integer limitSize);

    List<ProjectSimpleBasicVO> getProjectBasicInfoList();
}

