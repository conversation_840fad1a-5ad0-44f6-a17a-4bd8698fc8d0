package com.chinasie.orion.service;


import java.lang.String;
import java.util.List;


import com.chinasie.orion.domain.dto.AdjustmentVoucherDTO;
import com.chinasie.orion.domain.dto.HangingConnectDTO;
import com.chinasie.orion.domain.entity.AdjustmentVoucher;
import com.chinasie.orion.domain.vo.AdjustmentVoucherVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * AdjustmentVoucher 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-24 10:44:40
 */
public interface AdjustmentVoucherService extends OrionBaseService<AdjustmentVoucher> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    AdjustmentVoucherVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param adjustmentVoucherDTO
     */
    String create(AdjustmentVoucherDTO adjustmentVoucherDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param adjustmentVoucherDTO
     */
    Boolean edit(AdjustmentVoucherDTO adjustmentVoucherDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<AdjustmentVoucherVO> pages(Page<AdjustmentVoucherDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(AdjustmentVoucherDTO adjustmentVoucherDTO, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<AdjustmentVoucherVO> vos) throws Exception;

    Boolean hangingConnect(List<String> list);
}
