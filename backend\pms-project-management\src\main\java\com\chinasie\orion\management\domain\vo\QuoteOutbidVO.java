package com.chinasie.orion.management.domain.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@ApiModel(value = "QuoteOutbidVO对象", description = "经营看板-报价中标率")
@Data
public class QuoteOutbidVO implements Serializable {

    /**
     * 报价中标率
     */
    @ApiModelProperty(value = "报价中标率")
    private Double quoteOutbidNum;

    /**
     * 核能报价中标率
     */
    @ApiModelProperty(value = "核能报价中标率")
    private Double nuclearEnergyQuoteOutbidNum;
    /**
     * 非核能报价中标率
     */
    @ApiModelProperty(value = "非核能报价中标率")
    private Double otherQuoteOutbidNum;
    /**
     * 报价发出数量
     */
    @ApiModelProperty(value = "报价发出数量")
    private Integer sendQuoteNum;
    /**
     * 核能报价发出数量
     */
    @ApiModelProperty(value = "核能报价发出数量")
    private Integer nuclearEnergySendQuoteNum;
    /**
     * 非核能报价发出数量
     */
    @ApiModelProperty(value = "非核能报价发出数量")
    private Integer otherSendQuoteNum;


}

