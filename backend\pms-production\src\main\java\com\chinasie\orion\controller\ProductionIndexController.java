package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ProductionIndexDTO;
import com.chinasie.orion.domain.vo.ProductionIndexVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProductionIndexService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * ProductionIndex 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-18 17:49:18
 */
@RestController
@RequestMapping("/productionIndex")
@Api(tags = "生产看板指标维护")
public class ProductionIndexController {

    @Autowired
    private ProductionIndexService productionIndexService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看【生产看板指标维护】【{{#id}}】", type = "ProductionIndex", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProductionIndexVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        ProductionIndexVO rsp = productionIndexService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param productionIndexDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【生产看板指标维护】数据【{{#id}}】", type = "ProductionIndex", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody ProductionIndexDTO productionIndexDTO) throws Exception {
        String rsp = productionIndexService.create(productionIndexDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param productionIndexDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【生产看板指标维护】数据【{{#productionIndexDTO.id}}】", type = "ProductionIndex", subType = "编辑", bizNo = "{{#productionIndexDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody ProductionIndexDTO productionIndexDTO) throws Exception {
        Boolean rsp = productionIndexService.edit(productionIndexDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【生产看板指标维护】数据【{{#id}}】", type = "ProductionIndex", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = productionIndexService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【生产看板指标维护】数据", type = "ProductionIndex", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = productionIndexService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【生产看板指标维护】数据", type = "ProductionIndex", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ProductionIndexVO>> pages(@RequestBody Page<ProductionIndexDTO> pageRequest) throws Exception {
        Page<ProductionIndexVO> rsp = productionIndexService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 查询所有数据
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "查询所有数据")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【生产看板指标维护】数据", type = "ProductionIndex", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/getList", method = RequestMethod.POST)
    public ResponseDTO<List<ProductionIndexVO>> getList() throws Exception {
        List<ProductionIndexVO> rsp = productionIndexService.getList();
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("生产看板指标维护导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载【生产看板指标维护】导入模板", type = "ProductionIndex", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        productionIndexService.downloadExcelTpl(response);
    }

    @ApiOperation("生产看板指标维护导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验【生产看板指标维护】导入", type = "ProductionIndex", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = productionIndexService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("生产看板指标维护导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认【生产看板指标维护】导入", type = "ProductionIndex", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = productionIndexService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消生产看板指标维护导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消【生产看板指标维护】导入", type = "ProductionIndex", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = productionIndexService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("生产看板指标维护导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出【生产看板指标维护】数据", type = "ProductionIndex", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        productionIndexService.exportByExcel(searchConditions, response);
    }
}
