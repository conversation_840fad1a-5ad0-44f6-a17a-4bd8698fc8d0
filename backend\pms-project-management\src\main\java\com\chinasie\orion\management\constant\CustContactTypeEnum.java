package com.chinasie.orion.management.constant;

import lombok.Getter;

/**
 * <p>
 * 客户联系人-联系人类型-枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2024/9/6 下午5:09
 */
@Getter
public enum CustContactTypeEnum {

    BUSINESS("business", "商务联系人"),
    TECHNOLOGY("technology", "技术负责人"),
    HEAD("head", "总负责人"),
    ;

    private final String code;

    private final String desc;

    CustContactTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(String code) {
        for (CustContactTypeEnum value : CustContactTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return null;
    }
}
