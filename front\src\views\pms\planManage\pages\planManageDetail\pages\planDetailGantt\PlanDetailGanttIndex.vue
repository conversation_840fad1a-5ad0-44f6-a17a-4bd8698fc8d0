<template>
  <div
    v-loading="state.loadingStatus"
    class="plan-gantt-wrap"
  >
    <JQueryGantt
      v-if="state.taskData"
      :taskData="state.taskData"
      :getTaskDetailApi="getTaskDetailApi"
    />
  </div>
</template>

<script lang="ts" setup>
import { JQueryGantt } from 'lyra-component-vue3';
import { onMounted, reactive } from 'vue';
import { getPlanDetail, getPlanGanttData } from '/@/views/pms/api';

const props = defineProps<{
  planId: string
}>();

const state = reactive({
  loadingStatus: false,
  taskData: null,
});

onMounted(() => {
  init();
});

function init() {
  setTaskData();
}

async function setTaskData() {
  state.loadingStatus = true;
  try {
    const tasks = await getPlanGanttData(props.planId);
    if (tasks) {
      state.taskData = {
        tasks,
      };
    }
    state.loadingStatus = false;
  } catch (e) {
    state.loadingStatus = false;
  }
}

function getTaskDetailApi(task) {
  return getPlanDetail(task.planId);
}

function attributePopoverContainerRender() {
  return '';
}
</script>

<style scoped lang="less">
.plan-gantt-wrap {
  position: relative;
  height: 100%;
}
</style>
