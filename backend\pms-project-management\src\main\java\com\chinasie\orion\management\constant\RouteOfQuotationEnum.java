package com.chinasie.orion.management.constant;



public enum RouteOfQuotationEnum {

    ROUTE_OF_QUOTATION_INNER_EMAIL("route_of_quotation_inner_email","内部邮件"),
    ROUTE_OF_QUOTATION_OUT_EMAIL("route_of_quotation_out_email","外部邮件"),
    route_of_quotation_ecp("route_of_quotation_ECP","ECP投标"),
    ROUTE_OF_QUOTATION_OTHER_PLM("route_of_quotation_other_plm","外部平台投标");
    private String name;
    private String desc;

    RouteOfQuotationEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(String value){

        for (RouteOfQuotationEnum lt : RouteOfQuotationEnum.values()) {
            if(lt.name.equals( value)){
                return lt.desc;
            }
        }
        return null;
    }


}