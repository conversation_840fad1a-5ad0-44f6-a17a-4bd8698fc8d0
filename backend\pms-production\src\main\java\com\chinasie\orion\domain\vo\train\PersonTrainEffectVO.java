package com.chinasie.orion.domain.vo.train;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/14/19:29
 * @description:
 */
@Data
public class PersonTrainEffectVO implements Serializable {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "培训编号")
    private String trainNumber;
    @ApiModelProperty(value = "培训名称")
    private String trainName;


    @ApiModelProperty(value = "落地ID：在不影响数据情况下")
    private String trainCenterId;

    @ApiModelProperty(value = "培训基地编码")
    private String baseCode;

    /**
     * 培训基地名称
     */
    @ApiModelProperty(value = "培训基地名称")
    private String baseName;


    /**
     * 培训课时
     */
    @ApiModelProperty(value = "培训课时")
    private BigDecimal lessonHour;

    /**
     * 办结时间
     */
    @ApiModelProperty(value = "办结时间/完成时间")
    private Date endDate;
    /**
     * 办结时间
     */
    @ApiModelProperty(value = "到期时间时间")
    private Date expireTime;
}
