<script setup lang="ts">
import { BasicCard } from 'lyra-component-vue3';
import {
  inject, reactive,
} from 'vue';

const detailsData: Record<string, any> = inject('detailsData', reactive({}));
// 详情页面下的资审信息的字段名称定义
const baseInfoProps = reactive({
  list: [
    {
      label: '供应商名称',
      field: 'contractName',
    },
    {
      label: '申请编号',
      field: 'applicationNumber',
    },
    {
      label: '项目类别',
      field: 'projectCategory',
    },
    {
      label: '项目名称/采购任务名称',
      field: 'projectName',
    },
    {
      label: '采购包号',
      field: 'procureNumber',
    },
    {
      label: '申请类型',
      field: 'applicantType',
    },
    {
      label: '申请人',
      field: 'applicant',
    },
    {
      label: '申请公司',
      field: 'declaringCompany',
    },
    {
      label: '状态',
      field: 'state',
    },
    {
      label: '原因',
      field: 'reason',
    },
    {
      label: '流程环节',
      field: 'processStep',
    },
  ],
  column: 4,
  dataSource: detailsData,
});
</script>

<template>
  <BasicCard
    title="基本信息"
    :grid-content-props="baseInfoProps"
    :isBorder="false"
  />
</template>

<style scoped lang="less">

</style>