<script setup lang="ts">
import { BasicSteps, type IStepItem } from 'lyra-component-vue3';
import {
  computed, inject, onMounted, ref, watchEffect,
} from 'vue';
import Api from '/@/api';
import { usePriceUnit } from '/@/views/pms/purchaseManage/purchaseModule/utils';

const basicInfo = inject('projectApplicationItem');
const cgssNum = ref(0);
const cghtzxNum = ref(0);
const steps = computed(() => [
  {
    title: '采购申请',
    subTitle: `申请金额：${usePriceUnit(basicInfo.value.money)}`,
    description: '审批状态：已审批',
    disabled: true,
  },
  {
    title: '采购实施',
    subTitle: '合同状态：已立项未签订',
    description: `合同条数：${cgssNum.value}条`,
    disabled: true,
  },
  {
    title: '采购合同执行',
    subTitle: '合同状态：已签订执行中',
    description: `合同条数：${cghtzxNum.value}条`,
    disabled: true,
  },

  {
    title: 'SAP支付',
    subTitle: '单据状态：SAP已支付',
    // description: '合同条数：__条',
    disabled: true,
  },
]);
const current = ref(4);

const getApplicationProcess = async () => {
  try {
    const result = await new Api('/pms/ncfFormpurchaseRequest/getByCode').fetch({
      code: basicInfo.value.projectCode,
    }, '', 'POST');
    const { ssNum = 0, htNum = 0 } = result || {};
    cgssNum.value = ssNum;
    cghtzxNum.value = htNum;
  } catch (e) {}
};
watchEffect(async () => {
  await getApplicationProcess();
});
</script>

<template>
  <div id="ApplicationProcess">
    <BasicSteps
      v-model:current="current"
      :steps="steps"
    />
  </div>
</template>

<style scoped lang="less">
#ApplicationProcess{
  padding: 16px 100px 0 36px;
}
</style>