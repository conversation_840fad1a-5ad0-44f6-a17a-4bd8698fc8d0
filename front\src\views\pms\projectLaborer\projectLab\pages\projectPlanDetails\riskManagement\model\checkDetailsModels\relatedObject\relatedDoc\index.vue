<template>
  <div>
    <OrionTable
      ref="tableRef"
      :options="options"
    >
      <template #checkIn="{ record }">
        <span class="flex-te">{{ record.checkIn!==userId?'签入':'签出' }} </span>
      </template>
      <template #name="{ record }">
        <span class="action-btn">{{ record.name+'.'+record.filePostfix }}</span>
      </template>
    </OrionTable>
  </div>
</template>

<script lang="ts">
import {
  defineComponent, h, reactive, toRefs,
} from 'vue';
import {
  OrionTable, DataStatusTag,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
import Api from '/@/api';
import { useUserStore } from '/@/store/modules/user';
// import { Button } from 'ant-design-vue';
// import { PlusOutlined } from '@ant-design/icons-vue';
export default defineComponent({
  name: 'Index',
  components: { OrionTable },
  props: { id: {} },
  emits: [],
  setup(props) {
    const userStore = useUserStore();
    const { getUserInfo } = userStore;
    const state = reactive({
      userId: getUserInfo.id,
      tableRef: null,
      options: {
        deleteToolButton: 'add|delete|enable|disable',
        rowSelection: {},
        showIndexColumn: false,
        showSmallSearch: false,
        smallSearchField: ['name'],
        api: (P) => new Api('/res/manage/file/listByIds').fetch([props.id], '', 'POST'),
        columns: [
          {
            title: '名称',
            dataIndex: 'name',
            ellipsis: true,
            slots: { customRender: 'name' },
          },
          {
            title: '版本',
            dataIndex: 'revId',
          },
          {
            title: '状态',
            dataIndex: 'checkIn',
            slots: { customRender: 'checkIn' },
          },
          {
            title: '修改人',
            dataIndex: 'modifyName',
          },
          {
            title: '修改日期',
            dataIndex: 'modifyTime',
            ellipsis: true,
            customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : ''),
          },
        ],
      },
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang="less"></style>
