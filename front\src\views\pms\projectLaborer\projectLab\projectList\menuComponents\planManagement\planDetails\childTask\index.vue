<template>
  <div class="childTask">
    <div class="childTask_content">
      <OrionTable
        ref="tableRef"
        :options="tableOptions"
      >
        <template
          #toolbarLeft
        >
          <div
            class="_add"
          >
            <AButton
              class="mr10"
              type="primary"
              @click="addTablePlan"
            >
              <PlusOutlined />
              创建计划
            </AButton>
          </div>
        </template>
        <template #name="{text,record}">
          <div
            class="flex-te action-btn"
            :title="text"
          >
            <span
              @click.stop="checkClick(record)"
            >{{ text }}</span>
          </div>
        </template>
      </OrionTable>
    </div>
    <RightTool
      :btn-list="btnList"
      @clickType="clickType"
    />
    <AddPlanNode
      type="details"
      @register="register"
      @update="updateData"
    />
  </div>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, ref, inject,
} from 'vue';
import { OrionTable, RightTool, useDrawer } from 'lyra-component-vue3';
import dayjs from 'dayjs';
import Api from '/@/api';
import { Button, message, Modal } from 'ant-design-vue';
import {
  PlusOutlined,
} from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';
import AddPlanNode from '../../projectsPlan/components/AddPlanNode.vue';

export default defineComponent({
  name: 'ChildTask',
  components: {
    OrionTable,
    RightTool,
    AButton: Button,
    PlusOutlined,
    AddPlanNode,
  },
  setup() {
    const router = useRouter();
    const formData = inject('formData', {});
    const [register, { openDrawer }] = useDrawer();
    const state = reactive({
      btnList: ['edit', 'delete'],
    });
    const tableRef = ref(null);

    const tableOptions = ref({
      deleteToolButton: 'add|enable|disable|delete',
      rowSelection: {},
      showSmallSearch: false,
      showIndexColumn: false,
      api: (P) => new Api('/pms').fetch(P, `plan/tree/childrenlist/${formData?.value?.id}`, 'POST'),
      columns: [
        {
          title: '编号',
          dataIndex: 'number',
          width: 200,
          resizable: true,
        },
        {
          title: '名称',
          dataIndex: 'name',
          minWidth: 300,
          resizable: true,
          slots: { customRender: 'name' },
        },
        {
          title: '计划类型',
          dataIndex: 'planTypeName',
          minWidth: 100,
          resizable: true,
        },
        {
          title: '状态',
          dataIndex: 'status',
          align: 'left',
          width: 150,
          ellipsis: true,
          slots: { customRender: 'status' },
        },
        {
          title: '进度状态',
          dataIndex: 'speedStatusName',
          align: 'left',
          width: 150,
          ellipsis: true,
          slots: { customRender: 'speedStatusName' },
        },
        {
          title: '责任单位',
          dataIndex: 'resOrgName',
          minWidth: 100,
          resizable: true,
        },
        {
          title: '参与单位',
          dataIndex: 'joinOrgsName',
          minWidth: 100,
          resizable: true,
        },
        {
          title: '责任科室',
          dataIndex: 'resDeptName',
          minWidth: 100,
          resizable: true,
        },
        {
          title: '责任人',
          dataIndex: 'resUserName',
          width: 100,
          resizable: true,
        },
        {
          title: '参与科室',
          dataIndex: 'joinDeptsName',
          width: 100,
          resizable: true,
        },
        {
          title: '计划开始日期',
          dataIndex: 'planStartTime',
          width: 120,
          resizable: true,
          customRender({ text }) {
            return text ? dayjs(text).format('YYYY-MM-DD') : '';
          },
        },
        {
          title: '计划结束日期',
          dataIndex: 'planEndTime',
          width: 120,
          resizable: true,
          customRender({ text }) {
            return text ? dayjs(text).format('YYYY-MM-DD') : '';
          },
        },
      ],
    });
    const addTablePlan = () => {
      let selectRows = tableRef.value.getSelectRows();
      if (selectRows.length > 1) {
        message.warning('不能选择多条数据进行新增');
        return;
      }
      openDrawer(true, {
        type: 'add',
        data: { parentId: selectRows.length === 1 ? selectRows[0].id : formData?.value.id },
      });
    };
    function clickType(type) {
      let selectRows = tableRef.value.getSelectRows();
      if (selectRows.length === 0) {
        message.warning('请选择数据');
        return;
      }
      if (type === 'edit') {
        if (selectRows.length > 1) {
          message.warning('只能选择一条数据进行编辑');
          return;
        }
        new Api('/pms').fetch('', `plan/${selectRows[0].id}`, 'GET').then((res) => {
          openDrawer(true, {
            type: 'edit',
            data: res,
          });
        });
      }
      if (type === 'delete') {
        Modal.confirm({
          title: '确认提示',
          content: '请确认是否删除选中任务？',
          onOk() {
            let ids = selectRows.map((item) => item.id);
            new Api('/pms').fetch(ids, 'plan/batch', 'DELETE').then(() => {
              message.success('删除成功');
              tableRef.value.reload();
            });
          },
        });
      }
    }
    function updateData() {
      tableRef.value.reload();
    }
    function checkClick(record) {
      router.push({
        name: 'PlanDetails',
        query: {
          projectId: record.projectId,
          id: record.id,
        },
      });
    }
    return {
      ...toRefs(state),
      tableRef,
      addTablePlan,
      tableOptions,
      register,
      clickType,
      updateData,
      checkClick,
    };
  },
});
</script>
<style lang="less" scoped>
.childTask{
  display: flex;
  width: 100%;
  .childTask_content{
    width: calc(100% - 60px);
    flex: 1;
  }
}
</style>
