<script setup lang="ts">

import {
  BasicButton,
  BasicTableAction, downloadByData, IOrionTableActionItem, isPower, Layout, OrionTable,
} from 'lyra-component-vue3';
import Api from '/@/api';
import { Modal, RangePicker, Space } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import {
  computed,
  h,
  onMounted, reactive, ref, Ref, watchEffect,
} from 'vue';
import {
  cloneDeep, get as loadGet, isEmpty, lt,
} from 'lodash-es';
import MoneyRow from '../components/MoneyRow.vue';
import { openFormDrawer } from '../utils';
import OngoingForm from './components/OngoingForm.vue';
import CustomNumberRange
  from '/@/views/pms/purchaseManage/purchaseModule/projectOngoing/components/CustomNumberRange.vue';

const loadStatus: Ref<boolean> = ref(false);
const pageSearchConditions = ref(null);
const tableRef: Ref = ref();
const router = useRouter();
const rowMoney = reactive([
  {
    key: 'total',
    title: '合同数量',
    value: '',
    suffix: '个',
  },
  {
    key: 'allMoney',
    title: '合同总金额',
    value: '',
    suffix: '元',
  },
]);
const powerData = ref();
const showExportButton = computed(() => isPower('PMS_CGXMSS_container_01_button_04', powerData.value));
const tableOptions = {
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  smallSearchField: [
    'projectName',
    'purchReqDocCode',
    'processName',
  ],
  filterConfig: {
    fields: [
      {
        field: 'purchMethod',
        fieldName: '采购方式',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'purchReqDocCode',
        fieldName: '采购申请号',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'ecpPurchaseAppNo',
        fieldName: 'ECP采购申请号',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'projectName',
        fieldName: '项目名称',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'executionStatus',
        fieldName: '合同状态',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'bizRespons',
        fieldName: '商务人员',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'applicant',
        fieldName: '申请人',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'applyDepartment',
        fieldName: '申请部门',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'upmApprovalComplete',
        fieldName: 'UPM审批完成时间段',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterface: null,
        referenceInterfaceMethod: null,
        referenceInterfaceParams: null,
        component: 'Select',
        hidden: false,
        constValue: null,
        fieldNames: null,
        searchFieldName: null,
        optionsCustomRender({ filterItem, filterMethods, groupRelation }) {
          return h(RangePicker, {
            style: {
              width: '100%',
            },
            onChange(date: any) {
              filterMethods.setFieldValue(filterItem.field, date, groupRelation);
            },
            valueFormat: 'YYYY-MM-DD',
          });
        },
      },
      {
        field: 'approveTime',
        fieldName: '采购申请完成时间',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterface: null,
        referenceInterfaceMethod: null,
        referenceInterfaceParams: null,
        component: 'Select',
        hidden: false,
        constValue: null,
        fieldNames: null,
        searchFieldName: null,
        optionsCustomRender({ filterItem, filterMethods, groupRelation }) {
          return h(RangePicker, {
            style: {
              width: '100%',
            },
            onChange(date: any) {
              // console.log(date, 'date-date');
              filterMethods.setFieldValue(filterItem.field, date, groupRelation);
            },
            valueFormat: 'YYYY-MM-DD',
          });
        },
      },
      {
        field: 'usedTime',
        fieldName: '已经耗时',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterface: null,
        referenceInterfaceMethod: null,
        referenceInterfaceParams: null,
        component: 'Select',
        hidden: false,
        constValue: null,
        fieldNames: null,
        searchFieldName: null,
        optionsCustomRender({ filterItem, filterMethods, groupRelation }) {
          return h(CustomNumberRange, {
            onChange(arg: any) {
              // console.log(arg, 'arg-arg');
              filterMethods.setFieldValue(filterItem.field, arg, groupRelation);
            },
          });
        },
      },
    ],
  },
  columns: [
    {
      title: '操作',
      dataIndex: 'actions',
      width: 120,
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
    {
      title: '发起时间',
      dataIndex: 'initiationTime',
      width: 120,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '采购申请号',
      dataIndex: 'purchReqDocCode',
      width: 150,
    },
    {
      title: 'ECP采购申请号',
      dataIndex: 'ecpPurchaseAppNo',
      width: 150,
    },
    {
      title: '商务人员',
      dataIndex: 'bizRespons',
      width: 150,
    },
    {
      title: '需求部门',
      dataIndex: 'applyDepartment',
      width: 200,
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      width: 480,
      minWidth: 480,
    },
    {
      title: '合同类型',
      dataIndex: 'contractType',
      width: 100,
    },
    {
      title: '合同状态',
      dataIndex: 'executionStatus',
      width: 100,
    },
    {
      title: '采购类型',
      dataIndex: 'purchType',
      width: 100,
    },
    {
      title: '采购方式',
      dataIndex: 'purchMethod',
      width: 100,
    },
    {
      title: '关注事项',
      dataIndex: 'concerns',
      width: 220,
    },
    {
      title: '已经耗时',
      dataIndex: 'usedTime',
      width: 100,
    },
  ],
  api: (params:Record<string, any>) => {
    const newData = cloneDeep(params);
    const originSearchConditions = loadGet(cloneDeep(newData), 'searchConditions', []);
    const query = {};
    const searchConditions = originSearchConditions.reduce((prev, cur) => {
      for (let i = 0; i < cur.length; i++) {
        const single = cur[i];
        const filedProp = loadGet(single, 'field', '');
        if ([
          'upmApprovalComplete',
          'approveTime',
          'usedTime',
        ].includes(filedProp)) {
          const [first, second] = loadGet(single, 'values', []);
          if (filedProp === 'upmApprovalComplete') {
            Object.assign(query, {
              startDate: first,
              endDate: second,
            });
          }
          if (filedProp === 'approveTime') {
            Object.assign(query, {
              approveStartDate: first,
              approveEndDate: second,
            });
          }
          if (filedProp === 'usedTime' && first && second) {
            Object.assign(query, {
              startUsedTime: first,
              endUsedTime: second,
            });
          }
          cur.splice(i, 1, undefined);
        }
      }
      const lastCur = cur.filter(Boolean);
      if (lastCur.length) {
        return [...prev, lastCur];
      }
      return prev;
    }, []);
    const newSearchConditions = {
      searchConditions: searchConditions.length ? searchConditions : [],
      query: isEmpty(query) ? undefined : query,
    };
    pageSearchConditions.value = params.searchConditions ? newSearchConditions : null;
    return new Api('/pms/ncfPurchProjectImplementation/page').fetch({
      ...params,
      power: {
        pageCode: 'projectOngoing001',
        containerCode: 'PMS_CGXMSS_container_01',
      },
      ...newSearchConditions,
    }, '', 'POST');
  },
};
const actions: IOrionTableActionItem[] = [
  {
    text: '编辑',
    event: 'edit',
    isShow: (record) => isPower('PMS_CGXMSS_container_01_button_03', record.rdAuthList),
  },
  {
    text: '查看',
    event: 'view',
    isShow: (record) => isPower('PMS_CGXMSS_container_01_button_02', record.rdAuthList),
  },
];
function actionClick(actionItem: { event: string }, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'edit':
      openFormDrawer(OngoingForm, record, updateTable);
      break;
    case 'view':
      router.push({
        name: 'ProjectOngoingItem',
        params: {
          id: record.id,
        },
      });
      break;
    case 'delete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除这条数据吗？',
        onOk: () => deleteApi([record.id]),
      });
      break;
  }
}

function updateTable() {
  tableRef.value?.reload();
}
const getMoney = async () => {
  try {
    const result = await new Api('/pms/ncfPurchProjectImplementation/getNumMoney').fetch({
      ...(pageSearchConditions.value || {}),
    }, '', 'POST');
    rowMoney.forEach((item) => {
      item.value = result[item.key];
    });
  } catch (e) {}
};
const exportTable = () => {
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk: async () => {
      loadStatus.value = true;
      let res = await downloadByData('/pms/ncfPurchProjectImplementation/export/excel', {
        ...(pageSearchConditions.value || {}),
      }, '', 'POST', true, false, '导出处理完成，现在开始下载');
      loadStatus.value = false;
    },
  });
};

const getPowerDataHandle = (data) => {
  powerData.value = data;
};

watchEffect(async () => {
  await getMoney();
});

</script>

<template>
  <Layout
    v-get-power="{pageCode: 'projectOngoing001',getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      class="scroll-table"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <Space :size="12">
          <BasicButton
            v-if="showExportButton"
            icon="sie-icon-daochu"
            type="primary"
            @click="exportTable"
          >
            导出全部
          </BasicButton>
          <MoneyRow :data="rowMoney" />
        </Space>
      </template>
      <template #actions="{record}">
        <BasicTableAction
          :actions="actions"
          :record="record"
          @actionClick="actionClick($event,record)"
        />
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">
.scroll-table {
  ::-webkit-scrollbar {
    height: 15px !important;
    width: 15px !important;
  }
  ::-webkit-scrollbar-thumb {
    border-radius:0;
  }
}
</style>