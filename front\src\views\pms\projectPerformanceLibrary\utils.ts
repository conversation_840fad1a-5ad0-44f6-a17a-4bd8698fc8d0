import { openDrawer } from 'lyra-component-vue3';
import { h, ref, Ref } from 'vue';
import FormDrawer from './components/FormDrawer.vue';
// import ExecuteDrawer from './components/ExecuteDrawer.vue';
// import AddTestTaskModal from '/@/views/pas/testPlanManagement/testPlanManagementDetail/TestTask/AddTestTaskModal.vue';

export function openFormDrawer(testTaskId: string, record?: Record<string, any>, cb?: () => void, isView: boolean = false) {
  const formRef: Ref = ref();
  openDrawer({
    title: record?.id ? '编辑绩效' : '新建绩效',
    width: 1000,
    content() {
      return h(FormDrawer, {
        ref: formRef,
        id: record?.id,
        record,
      });
    },
    footer: {
      isOk: !isView,
    },
    async onOk(): Promise<void> {
      await formRef.value.onSubmit();
      cb?.();
    },
  });
}

// export function openExecuteDrawer(record?: Record<string, any>, cb?: () => any | void) {
//   const formRef: Ref = ref();
//   const drawer = openDrawer({
//     title: `执行用例-${record.number}`,
//     width: 1000,
//     footer: {
//       isContinue: true,
//       okText: '保存',
//     },
//     content() {
//       return h(ExecuteDrawer, { ref: formRef, record });
//     },
//     async onOk(): Promise<void> {
//       await formRef.value.onSubmit();
//       const data = await cb?.();
//       const nextRecord: Record<string, any> = data.filter((item: Record<string, any>) => item.status === 101)?.[0];
//       if (nextRecord) {
//         drawer.setProps({
//           title: `执行用例-${nextRecord?.number}`,
//         });
//         formRef.value?.updateDataId(nextRecord?.id);
//       }
//     },
//   });
// }
//
// // 编辑测试任务
// export function openTaskDrawer(record?: Record<string, any>, cb?: () => void) {
//   const formRef: Ref = ref();
//   openDrawer({
//     title: record?.id ? '编辑任务' : '新增任务',
//     width: 1000,
//     content() {
//       return h(AddTestTaskModal, { ref: formRef, record });
//     },
//     async onOk(): Promise<void> {
//       await formRef.value.onSubmit();
//       cb?.();
//     },
//   });
// }

export const DATA_STATUS_COLOR = [
  {
    label: '白色（默认）',
    key: 'white',
    value: '1',
    background: 'rgba(0, 0, 0, 0.04)',
    borderColor: 'rgba(0, 0, 0, 0.15)',
    color: 'rgba(0, 0, 0, 0.25)',
  },
  {
    label: '蓝色',
    key: 'blue',
    value: '2',
    background: 'rgba(230, 247, 255, 1)',
    borderColor: 'rgba(145, 213, 255, 1)',
    color: 'rgba(22, 119, 255, 1)',
  },
  {
    label: '绿色',
    key: 'green',
    value: '3',
    background: 'rgba(246, 255, 237, 1)',
    borderColor: 'rgba(217, 247, 190, 1)',
    color: 'rgba(82, 196, 26, 1)',
  },
  {
    label: '红色',
    key: 'red',
    value: '4',
    background: 'rgba(255, 241, 240, 1)',
    borderColor: 'rgba(255, 163, 158, 1)',
    color: 'rgba(255, 77, 79, 1)',
  },
  {
    label: '橙色',
    key: 'orange',
    value: '5',
    background: 'rgba(255, 251, 230, 1)',
    borderColor: 'rgba(255, 229, 143, 1)',
    color: 'rgba(250, 173, 20, 1)',
  },
  {
    label: '灰色',
    key: 'gray',
    value: '6',
    background: 'rgba(0, 0, 0, 0.04)',
    borderColor: 'rgba(0, 0, 0, 0.15)',
    color: 'rgba(0, 0, 0, 0.25)',
  },
];
