package com.chinasie.orion.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.concurrent.ConcurrentHashMap;

public class LogUtil {
    private static final ConcurrentHashMap<String, Logger> loggerCache = new ConcurrentHashMap<>();

    private LogUtil() {
        // 私有构造函数，防止实例化
    }

    public static void info(String message) {
        Logger logger = getLogger();
        if (logger != null) {
            logger.info(message);
        }
    }

    public static void info(String format, Object... arguments) {
        Logger logger = getLogger();
        if (logger != null) {
            logger.info(format, arguments);
        }
    }

    public static void debug(String message) {
        Logger logger = getLogger();
        if (logger != null) {
            logger.debug(message);
        }
    }

    public static void debug(String format, Object... arguments) {
        Logger logger = getLogger();
        if (logger != null) {
            logger.debug(format, arguments);
        }
    }

    public static void warn(String message) {
        Logger logger = getLogger();
        if (logger != null) {
            logger.warn(message);
        }
    }

    public static void warn(String format, Object... arguments) {
        Logger logger = getLogger();
        if (logger != null) {
            logger.warn(format, arguments);
        }
    }

    public static void error(String message) {
        Logger logger = getLogger();
        if (logger != null) {
            logger.error(message);
        }
    }

    public static void error(String format, Object... arguments) {
        Logger logger = getLogger();
        if (logger != null) {
            logger.error(format, arguments);
        }
    }

    public static void error(String message, Throwable throwable) {
        Logger logger = getLogger();
        if (logger != null) {
            logger.error(message, throwable);
        }
    }

    private static Logger getLogger() {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        String callerClassName = stackTrace[2].getClassName(); // 获取调用者的类名

        // 从缓存中获取Logger实例
        return loggerCache.computeIfAbsent(callerClassName, className -> {
            try {
                Class<?> clazz = Class.forName(className);
                Field loggerField = clazz.getDeclaredField("logger");
                if (Logger.class.isAssignableFrom(loggerField.getType())) {
                    loggerField.setAccessible(true);
                    return (Logger) loggerField.get(null);
                }
            } catch (ClassNotFoundException | NoSuchFieldException | IllegalAccessException e) {
                // 忽略这些异常
            }
            // 如果没有找到logger字段，则动态创建一个新的Logger实例
            return LoggerFactory.getLogger(className);
        });
    }
}
