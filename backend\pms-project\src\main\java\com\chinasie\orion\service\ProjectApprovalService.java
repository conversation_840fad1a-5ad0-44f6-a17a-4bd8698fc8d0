package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.ProjectApprovalDTO;

import com.chinasie.orion.domain.dto.ProjectDTO;
import com.chinasie.orion.domain.entity.ProjectApproval;
import com.chinasie.orion.domain.vo.ProjectApprovalVO;
import com.chinasie.orion.domain.vo.ProjectVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.lang.String;
import java.util.List;
/**
 * <p>
 * ProjectApproval 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-06 10:36:23
 */
public interface ProjectApprovalService  extends OrionBaseService<ProjectApproval> {
    /**
     *  详情
     *
     * * @param id
     */
    ProjectApprovalVO detail(String id)  throws Exception;

    /**
     *  新增
     *
     * * @param projectApprovalDTO
     */
    ProjectApprovalVO create(ProjectApprovalDTO projectApprovalDTO)  throws Exception;

    /**
     *  编辑
     *
     * * @param projectApprovalDTO
     */
    Boolean edit(ProjectApprovalDTO projectApprovalDTO) throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     */
    Page<ProjectApprovalVO> pages(Page<ProjectApprovalDTO> pageRequest) throws Exception;


    /**
     * 待发起申报项目分页
     * @return
     * @throws Exception
     */
    Page<ProjectVO> projectPages(Page<ProjectDTO> pageRequest) throws Exception;

    ProjectApprovalVO getProjectApproval(String id) throws Exception;

    ProjectApprovalVO completeProjectApproval(String id) throws Exception;

    ProjectVO getProjectById(String id) throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     */
    Page<ProjectApprovalVO> userPages(Page<ProjectApprovalDTO> pageRequest) throws Exception;

    /**
     * 通过项目id获取立项论证id
     * @param projectIdList
     * @return
     * @throws Exception
     */
    List<ProjectApproval> getProjectApprovalByProjectIds(List<String> projectIdList) throws Exception;
    /**
     *  关联项目
     *
     * * @param approvalId
     * * @param projectId
     */
    Boolean relevanceProject(String approvalId, String projectId) throws Exception;

    Page<ProjectApprovalVO> notRelevance(Page<ProjectApprovalDTO> pageRequest) throws Exception;
}
