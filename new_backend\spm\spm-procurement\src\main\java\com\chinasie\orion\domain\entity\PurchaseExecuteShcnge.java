package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * PurchaseExecuteShcnge Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@TableName(value = "ncf_form_purchase_execute_shcnge")
@ApiModel(value = "PurchaseExecuteShcngeEntity对象", description = "采购执行变更")
@Data

public class PurchaseExecuteShcnge extends ObjectEntity implements Serializable {

    /**
     * 变更人
     */
    @ApiModelProperty(value = "变更人")
    @TableField(value = "changer")
    private String changer;

    /**
     * 变更日期
     */
    @ApiModelProperty(value = "变更日期")
    @TableField(value = "change_date")
    private Date changeDate;

    /**
     * 变更内容
     */
    @ApiModelProperty(value = "变更内容")
    @TableField(value = "change_content")
    private String changeContent;

    /**
     * 变更前
     */
    @ApiModelProperty(value = "变更前")
    @TableField(value = "before_change")
    private String beforeChange;

    /**
     * 变更后
     */
    @ApiModelProperty(value = "变更后")
    @TableField(value = "after_change")
    private String afterChange;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @TableField(value = "main_table_id")
    private String mainTableId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_number")
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @TableField(value = "contract_name")
    private String contractName;
}
