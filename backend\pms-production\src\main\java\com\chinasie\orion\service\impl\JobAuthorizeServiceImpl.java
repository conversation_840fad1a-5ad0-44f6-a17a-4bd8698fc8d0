package com.chinasie.orion.service.impl;
import com.chinasie.orion.domain.dto.JobAuthorizeDTO;
import com.chinasie.orion.domain.entity.JobAuthorize;
import com.chinasie.orion.domain.vo.JobAuthorizeVO;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.JobAuthorizeMapper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.JobAuthorizeService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import java.lang.String;
import java.util.*;

import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * JobAuthorizationinformations 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05 10:41:49
 */
@Service
@Slf4j
public class JobAuthorizeServiceImpl extends  OrionBaseServiceImpl<JobAuthorizeMapper, JobAuthorize>   implements JobAuthorizeService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public JobAuthorizeVO detail(String id, String pageCode) throws Exception {
        JobAuthorize jobAuthorizationinformations =this.getById(id);
        JobAuthorizeVO result = BeanCopyUtils.convertTo(jobAuthorizationinformations,JobAuthorizeVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param jobAuthorizationinformationsDTO
     */
    @Override
    public  String create(JobAuthorizeDTO jobAuthorizationinformationsDTO) throws Exception {
        JobAuthorize jobAuthorizationinformations =BeanCopyUtils.convertTo(jobAuthorizationinformationsDTO,JobAuthorize::new);
        this.save(jobAuthorizationinformations);

        String rsp=jobAuthorizationinformations.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param jobAuthorizationinformationsDTO
     */
    @Override
    public Boolean edit(JobAuthorizeDTO jobAuthorizationinformationsDTO) throws Exception {
        JobAuthorize jobAuthorizationinformations =BeanCopyUtils.convertTo(jobAuthorizationinformationsDTO,JobAuthorize::new);

        this.updateById(jobAuthorizationinformations);

        String rsp=jobAuthorizationinformations.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<JobAuthorizeVO> pages(String mainTableId, Page<JobAuthorizeDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<JobAuthorize> condition = new LambdaQueryWrapperX<>( JobAuthorize. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(JobAuthorize::getCreateTime);

            condition.eq(JobAuthorize::getMainTableId, mainTableId);

        Page<JobAuthorize> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), JobAuthorize::new));

        PageResult<JobAuthorize> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<JobAuthorizeVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<JobAuthorizeVO> vos = BeanCopyUtils.convertListTo(page.getContent(), JobAuthorizeVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void  setEveryName(List<JobAuthorizeVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }





}
