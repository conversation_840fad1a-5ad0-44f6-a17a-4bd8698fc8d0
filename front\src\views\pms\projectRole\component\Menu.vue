<template>
  <BasicTree
    v-bind="$attrs"
    :tree-data-api="treeDataApi"
    :is-toolbar="false"
    :check-strictly="true"
    :default-expand-all="false"
    :replace-fields="{ children: 'children', title: 'description', key: 'id' }"
  />
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { BasicTree } from 'lyra-component-vue3';
import Api from '/@/api';

export default defineComponent({
  name: 'RolePermissionMenu',
  components: {
    BasicTree,
  },
  props: {
    roleId: {
      type: String,
      default: '',
    },
  },
  setup() {
    return {
      treeDataApi: () => new Api('/pmi/menu/treeListPage').fetch(
        {
          orders: [
            {
              asc: false,
              column: 'sort',
            },
          ],
          query: { name: '' },
          queryCondition: [
            {
              column: 'status',
              link: 'AND',
              type: 'ne',
              value: '-1',
            },
          ],
        },
        '',
        'POST',
      ),
    };
  },
});
</script>

<style scoped></style>
