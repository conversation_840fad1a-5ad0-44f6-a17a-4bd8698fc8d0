INSERT INTO `pmi_data_policy` (`id`, `class_name`, `name`, `label`, `remark`, `description`, `creator_id`, `modify_id`, `owner_id`, `create_time`, `modify_time`, `status`, `platform_id`, `unique_key`, `logic_status`) VALUES ('txf71854813058089185280', 'DataPolicy', '商城子订单状态策略', NULL, NULL, NULL, '314j1000000000000000000', '314j1000000000000000000', '314j1000000000000000000', '2024-11-08 17:07:48', '2024-11-08 17:09:42', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1);
INSERT INTO `pmi_data_status` (`id`, `class_name`, `parent_id`, `label`, `color`, `name`, `status_value`, `description`, `remark`, `creator_id`, `modify_id`, `modify_time`, `owner_id`, `create_time`, `status`, `sort`, `platform_id`, `unique_key`, `logic_status`, `is_initial_value`) VALUES ('qtks1854815470136946688', 'DataStatus', 'txf71854813058089185280', NULL, '6', '需求待分发', 121, NULL, NULL, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-11-08 17:23:35', 'user00000000000000000100000000000000', '2024-11-08 17:17:24', 1, 4954, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 1);
INSERT INTO `pmi_data_status` (`id`, `class_name`, `parent_id`, `label`, `color`, `name`, `status_value`, `description`, `remark`, `creator_id`, `modify_id`, `modify_time`, `owner_id`, `create_time`, `status`, `sort`, `platform_id`, `unique_key`, `logic_status`, `is_initial_value`) VALUES ('qtks1854815745920823296', 'DataStatus', 'txf71854813058089185280', NULL, '5', '需求待确认', 120, NULL, NULL, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-11-08 17:20:54', 'user00000000000000000100000000000000', '2024-11-08 17:18:29', 1, 4966, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 0);
INSERT INTO `pmi_data_status` (`id`, `class_name`, `parent_id`, `label`, `color`, `name`, `status_value`, `description`, `remark`, `creator_id`, `modify_id`, `modify_time`, `owner_id`, `create_time`, `status`, `sort`, `platform_id`, `unique_key`, `logic_status`, `is_initial_value`) VALUES ('qtks1854815941312475136', 'DataStatus', 'txf71854813058089185280', NULL, '2', '需求已确认', 1, NULL, NULL, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-11-08 17:25:23', 'user00000000000000000100000000000000', '2024-11-08 17:19:16', 1, 4978, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 0);
INSERT INTO `pmi_data_status` (`id`, `class_name`, `parent_id`, `label`, `color`, `name`, `status_value`, `description`, `remark`, `creator_id`, `modify_id`, `modify_time`, `owner_id`, `create_time`, `status`, `sort`, `platform_id`, `unique_key`, `logic_status`, `is_initial_value`) VALUES ('qtks1854816138113413120', 'DataStatus', 'txf71854813058089185280', NULL, '{\"color\":\"#31b95a\",\"background\":\"rgba(230, 247, 255, 1)\",\"borderColor\":\"rgba(145, 213, 255, 1)\"}', '订单已确认', 110, NULL, NULL, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-11-08 17:25:59', 'user00000000000000000100000000000000', '2024-11-08 17:20:03', 1, 4990, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 0);
INSERT INTO `pmi_data_status` (`id`, `class_name`, `parent_id`, `label`, `color`, `name`, `status_value`, `description`, `remark`, `creator_id`, `modify_id`, `modify_time`, `owner_id`, `create_time`, `status`, `sort`, `platform_id`, `unique_key`, `logic_status`, `is_initial_value`) VALUES ('qtks1854816318535593984', 'DataStatus', 'txf71854813058089185280', NULL, '{\"color\":\"#f3a012\",\"background\":\"rgba(255, 251, 230, 1)\",\"borderColor\":\"rgba(255, 229, 143, 1)\"}', '订单里程碑审批中', 180, NULL, NULL, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-11-08 17:26:28', 'user00000000000000000100000000000000', '2024-11-08 17:20:46', 1, 5002, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 0);
INSERT INTO `pmi_data_status` (`id`, `class_name`, `parent_id`, `label`, `color`, `name`, `status_value`, `description`, `remark`, `creator_id`, `modify_id`, `modify_time`, `owner_id`, `create_time`, `status`, `sort`, `platform_id`, `unique_key`, `logic_status`, `is_initial_value`) VALUES ('qtks1854816537604091904', 'DataStatus', 'txf71854813058089185280', NULL, '{\"color\":\"#e64cbd\",\"background\":\"rgba(255, 251, 230, 1)\",\"borderColor\":\"rgba(255, 229, 143, 1)\"}', '订单履行中', 170, NULL, NULL, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-11-08 17:26:43', 'user00000000000000000100000000000000', '2024-11-08 17:21:38', 1, 5014, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 0);
INSERT INTO `pmi_data_status` (`id`, `class_name`, `parent_id`, `label`, `color`, `name`, `status_value`, `description`, `remark`, `creator_id`, `modify_id`, `modify_time`, `owner_id`, `create_time`, `status`, `sort`, `platform_id`, `unique_key`, `logic_status`, `is_initial_value`) VALUES ('qtks1854816733142544384', 'DataStatus', 'txf71854813058089185280', NULL, '3', '订单完成', 160, NULL, NULL, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-11-08 17:24:10', 'user00000000000000000100000000000000', '2024-11-08 17:22:25', 1, 5026, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 0);


ALTER TABLE `pmsx_project_order` add column `customer` varchar(255) COMMENT '客户id';

INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01855158026322395136', '[标题]商城子订单分发', 'MARKET_ORDER_DISTRIBUTE_TITLE', '收到新的商城订单【$contractName$】，请到一体化平台进行处理。', '1', 1, '', '314j1000000000000000000', '2024-11-09 15:58:35', '314j1000000000000000000', '2024-11-09 15:58:39', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01855158137354010624', '商城子订单分发', 'MARKET_SUB_ORDER_DISTRIBUTE', '<p>收到新的商城订单【$contractName$】，请到一体化平台进行处理。</p>', '2', 1, '', '314j1000000000000000000', '2024-11-09 15:59:02', '314j1000000000000000000', '2024-11-09 15:59:05', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');


INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1855158529236221952', '商城子订单消息分发', 'MARKET_SUB_ORDER_DISTRIBUTE', 'vub01855158026322395136', 'vub01855158137354010624', 1, 'SYS,EMAIL', '1', '1', NULL, 'u0rn1820705362382827520', 1, NULL, '314j1000000000000000000', '2024-11-09 16:00:35', '314j1000000000000000000', '2024-11-09 16:00:38', NULL, NULL, NULL, 1, b'1', b'1');

INSERT INTO `msc_business_node_channel` (`id`, `class_name`, `creator_id`, `modify_time`, `owner_id`, `create_time`, `modify_id`, `remark`, `platform_id`, `org_id`, `status`, `logic_status`, `business_node_id`, `type`, `template_flag`, `custom`) VALUES ('35rj1855158529248804864', 'BusinessNodeChannel', '314j1000000000000000000', '2024-11-09 16:00:35', '314j1000000000000000000', '2024-11-09 16:00:35', '314j1000000000000000000', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', 1, 1, '0a0y1855158529236221952', 'SYS', b'1', 'vub01855158026322395136');
INSERT INTO `msc_business_node_channel` (`id`, `class_name`, `creator_id`, `modify_time`, `owner_id`, `create_time`, `modify_id`, `remark`, `platform_id`, `org_id`, `status`, `logic_status`, `business_node_id`, `type`, `template_flag`, `custom`) VALUES ('35rj1855158529252999168', 'BusinessNodeChannel', '314j1000000000000000000', '2024-11-09 16:00:35', '314j1000000000000000000', '2024-11-09 16:00:35', '314j1000000000000000000', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', 1, 1, '0a0y1855158529236221952', 'EMAIL', b'0', NULL);

INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub018558487***********', '商城子订单分发确认-标题', 'NODE_SUB_ORDER_CONFIRM_TITLE', '商城订单{$contractName$}，已被「用户」确认。请到商城系统处理。', '1', 1, '', '314j1000000000000000000', '2024-11-11 13:43:26', '314j1000000000000000000', '2024-11-11 13:46:55', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01855849002397417472', '商城子订单确认', 'NODE_SUB_ORDER_CONFIRM', '<p>商城订单{$contractName$}，已被「用户」确认。请到商城系统处理。</p>', '2', 1, '', '314j1000000000000000000', '2024-11-11 13:44:17', '314j1000000000000000000', '2024-11-11 13:44:28', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1855850005595242496', '商城子订单消息分发确认', 'NODE_SUB_ORDER_DISTRIBUTE', 'vub018558487***********', 'vub01855849002397417472', 1, 'SYS,EMAIL', '1', '1', NULL, 'u0rn1820705362382827520', 1, NULL, '314j1000000000000000000', '2024-11-11 13:48:16', '314j1000000000000000000', '2024-11-11 13:48:19', NULL, NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node_channel` (`id`, `class_name`, `creator_id`, `modify_time`, `owner_id`, `create_time`, `modify_id`, `remark`, `platform_id`, `org_id`, `status`, `logic_status`, `business_node_id`, `type`, `template_flag`, `custom`) VALUES ('35rj1855850005612019712', 'BusinessNodeChannel', '314j1000000000000000000', '2024-11-11 13:48:16', '314j1000000000000000000', '2024-11-11 13:48:16', '314j1000000000000000000', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', 1, 1, '0a0y1855850005595242496', 'SYS', b'1', 'vub018558487***********');
INSERT INTO `msc_business_node_channel` (`id`, `class_name`, `creator_id`, `modify_time`, `owner_id`, `create_time`, `modify_id`, `remark`, `platform_id`, `org_id`, `status`, `logic_status`, `business_node_id`, `type`, `template_flag`, `custom`) VALUES ('35rj1855850005620408320', 'BusinessNodeChannel', '314j1000000000000000000', '2024-11-11 13:48:16', '314j1000000000000000000', '2024-11-11 13:48:16', '314j1000000000000000000', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', 1, 1, '0a0y1855850005595242496', 'EMAIL', b'0', NULL);

INSERT INTO `dme_class` (`id`, `package_id`, `table_name`, `class_name`, `code`, `parent_id`, `prefix`, `label`, `type`, `icon`, `description`, `is_abstract`, `is_extend`, `class_route`, `table_route`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `sort`, `policy`, `revision`, `platform_id`, `unique_key`, `logic_status`, `owner_id`, `tree_flag`) VALUES ('gtjl1833419443304833024', 'eh6of839dd0486dd4e28a76134550b57834f', 'pmsx_project_order', 'ProjectOrder', '23uv', NULL, 'pmsx', 'projectorder', 'common', NULL, NULL, NULL, NULL, NULL, NULL, 1, '合同-商城子订单', '314j1000000000000000000', '2024-09-10 16:17:13', '314j1000000000000000000', '2024-11-11 10:20:44', 10, 'txf71854813058089185280', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, '314j1000000000000000000', 0);

INSERT INTO `dme_dict` (`id`, `keyword`, `label`, `parent_id`, `is_group`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `owner_id`, `remark`, `logic_status`, `type`) VALUES ('dict1856523039805140992', '框架下子订单类型', 'kuangjiaxiazidingdanleixing', '315f1d8308a74d48968f6005f2d1cb1b', 0, 4545, 'Market_Sub_Order_Type', 1, NULL, '314j1000000000000000000', '2024-11-13 10:22:40', '314j1000000000000000000', '2024-11-13 10:22:40', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'Dict', '314j1000000000000000000', NULL, 1, 1);

INSERT INTO `dme_dict_value` (`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1856524070702149632', 'dict1856523039805140992', '', 'frame', '', 0, 'frame', 1, '框架', '314j1000000000000000000', '2024-11-13 10:26:46', '314j1000000000000000000', '2024-11-13 10:27:31', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);
INSERT INTO `dme_dict_value` (`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1856524261199048704', 'dict1856523039805140992', '', 'totalPrice', '', 0, 'totalPrice', 1, '总价', '314j1000000000000000000', '2024-11-13 10:27:31', '314j1000000000000000000', '2024-11-13 10:27:31', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);

ALTER TABLE `pms_market_contract` add column `subOrder_type` varchar(255) COMMENT '框架子订单类型';

ALTER TABLE `pms_customer_info` ADD COLUMN `home_base` VARCHAR(64)  COMMENT '所属基地' ;
ALTER TABLE `pms_customer_info` ADD COLUMN `sales_class` VARCHARCREATE TABLE `pmsx_invoicing_revenue_accounting` (
`id` varchar(64) NOT NULL  COMMENT '主键',
`class_name` varchar(64)   COMMENT '创建人',
`creator_id` varchar(64) NOT NULL  COMMENT '创建人',
`modify_time` datetime NOT NULL  COMMENT '修改时间',
`owner_id` varchar(64)   COMMENT '拥有者',
`create_time` datetime NOT NULL  COMMENT '创建时间',
`modify_id` varchar(64) NOT NULL  COMMENT '修改人',
`remark` varchar(1024)   COMMENT '备注',
`platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
`org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
`status` int NOT NULL  COMMENT '状态',
`logic_status` int NOT NULL  COMMENT '逻辑删除字段',
`income_plan_num` varchar(255)   COMMENT '收入计划编号',
`mile_stone_name` varchar(255)   COMMENT '里程碑名称',
`income_verify_type` varchar(255)   COMMENT '收入确认类型',
`certificate_serial_number` varchar(255)   COMMENT '凭证编号',
`document_date` datetime   COMMENT '凭证日期',
`amt_no_tax` decimal   COMMENT '不含税金额',
`tax` decimal   COMMENT '税额',
`amt_tax` decimal   COMMENT '含税金额   【税额】+【收入净额】',
`project_number` varchar(255)   COMMENT '项目编号',
`contract_id` varchar(255)   COMMENT '关联合同id',
`text` varchar(255)   COMMENT '文本',
PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='开票收入核算信息表';


CREATE TABLE `pmsx_provisional_income_accounting` (
`id` varchar(64) NOT NULL  COMMENT '主键',
`class_name` varchar(64)   COMMENT '创建人',
`creator_id` varchar(64) NOT NULL  COMMENT '创建人',
`modify_time` datetime NOT NULL  COMMENT '修改时间',
`owner_id` varchar(64)   COMMENT '拥有者',
`create_time` datetime NOT NULL  COMMENT '创建时间',
`modify_id` varchar(64) NOT NULL  COMMENT '修改人',
`remark` varchar(1024)   COMMENT '备注',
`platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
`org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
`status` int NOT NULL  COMMENT '状态',
`logic_status` int NOT NULL  COMMENT '逻辑删除字段',
`income_plan_num` varchar(255)   COMMENT '收入计划编号',
`mile_stone_name` varchar(255)   COMMENT '里程碑名称',
`income_verify_type` varchar(255)   COMMENT '收入确认类型',
`certificate_serial_number` varchar(255)   COMMENT '凭证编号',
`document_date` datetime   COMMENT '凭证日期',
`amt_no_tax` decimal   COMMENT '不含税金额',
`tax` decimal   COMMENT '税额',
`amt_tax` decimal   COMMENT '含税金额',
`project_number` varchar(255)   COMMENT '项目编号',
`text` varchar(255)   COMMENT '文本',
`contract_id` varchar(255)   COMMENT '关联合同id',
PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='暂估收入核算信息';



CREATE TABLE `pmsx_advance_payment_invoiced` (
`id` varchar(64) NOT NULL  COMMENT '主键',
`class_name` varchar(64)   COMMENT '创建人',
`creator_id` varchar(64) NOT NULL  COMMENT '创建人',
`modify_time` datetime NOT NULL  COMMENT '修改时间',
`owner_id` varchar(64)   COMMENT '拥有者',
`create_time` datetime NOT NULL  COMMENT '创建时间',
`modify_id` varchar(64) NOT NULL  COMMENT '修改人',
`remark` varchar(1024)   COMMENT '备注',
`platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
`org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
`status` int NOT NULL  COMMENT '状态',
`logic_status` int NOT NULL  COMMENT '逻辑删除字段',
`income_plan_num` varchar(255)   COMMENT '收入计划编号',
`mile_stone_name` varchar(255)   COMMENT '里程碑名称',
`income_verify_type` varchar(255)   COMMENT '收入确认类型',
`certificate_serial_number` varchar(255)   COMMENT '凭证编号',
`document_date` datetime   COMMENT '凭证日期',
`amt_no_tax` decimal   COMMENT '不含税金额',
`tax` decimal   COMMENT '税额',
`amt_tax` decimal   COMMENT '含税金额',
`project_number` varchar(255)   COMMENT '项目编号',
`contract_id` varchar(255)   COMMENT '关联合同id',
`text` varchar(255)   COMMENT '文本',
PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='预收款开票挂账信息';

CREATE TABLE `pmsx_invoicing_revenue_accounting` (
`id` varchar(64) NOT NULL  COMMENT '主键',
`class_name` varchar(64)   COMMENT '创建人',
`creator_id` varchar(64) NOT NULL  COMMENT '创建人',
`modify_time` datetime NOT NULL  COMMENT '修改时间',
`owner_id` varchar(64)   COMMENT '拥有者',
`create_time` datetime NOT NULL  COMMENT '创建时间',
`modify_id` varchar(64) NOT NULL  COMMENT '修改人',
`remark` varchar(1024)   COMMENT '备注',
`platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
`org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
`status` int NOT NULL  COMMENT '状态',
`logic_status` int NOT NULL  COMMENT '逻辑删除字段',
`income_plan_num` varchar(255)   COMMENT '收入计划编号',
`mile_stone_name` varchar(255)   COMMENT '里程碑名称',
`income_verify_type` varchar(255)   COMMENT '收入确认类型',
`certificate_serial_number` varchar(255)   COMMENT '凭证编号',
`document_date` datetime   COMMENT '凭证日期',
`amt_no_tax` decimal   COMMENT '不含税金额',
`tax` decimal   COMMENT '税额',
`amt_tax` decimal   COMMENT '含税金额   【税额】+【收入净额】',
`project_number` varchar(255)   COMMENT '项目编号',
`contract_id` varchar(255)   COMMENT '关联合同id',
`text` varchar(255)   COMMENT '文本',
PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='开票收入核算信息表';


CREATE TABLE `pmsx_provisional_income_accounting` (
`id` varchar(64) NOT NULL  COMMENT '主键',
`class_name` varchar(64)   COMMENT '创建人',
`creator_id` varchar(64) NOT NULL  COMMENT '创建人',
`modify_time` datetime NOT NULL  COMMENT '修改时间',
`owner_id` varchar(64)   COMMENT '拥有者',
`create_time` datetime NOT NULL  COMMENT '创建时间',
`modify_id` varchar(64) NOT NULL  COMMENT '修改人',
`remark` varchar(1024)   COMMENT '备注',
`platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
`org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
`status` int NOT NULL  COMMENT '状态',
`logic_status` int NOT NULL  COMMENT '逻辑删除字段',
`income_plan_num` varchar(255)   COMMENT '收入计划编号',
`mile_stone_name` varchar(255)   COMMENT '里程碑名称',
`income_verify_type` varchar(255)   COMMENT '收入确认类型',
`certificate_serial_number` varchar(255)   COMMENT '凭证编号',
`document_date` datetime   COMMENT '凭证日期',
`amt_no_tax` decimal   COMMENT '不含税金额',
`tax` decimal   COMMENT '税额',
`amt_tax` decimal   COMMENT '含税金额',
`project_number` varchar(255)   COMMENT '项目编号',
`text` varchar(255)   COMMENT '文本',
`contract_id` varchar(255)   COMMENT '关联合同id',
PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='暂估收入核算信息';



CREATE TABLE `pmsx_advance_payment_invoiced` (
`id` varchar(64) NOT NULL  COMMENT '主键',
`class_name` varchar(64)   COMMENT '创建人',
`creator_id` varchar(64) NOT NULL  COMMENT '创建人',
`modify_time` datetime NOT NULL  COMMENT '修改时间',
`owner_id` varchar(64)   COMMENT '拥有者',
`create_time` datetime NOT NULL  COMMENT '创建时间',
`modify_id` varchar(64) NOT NULL  COMMENT '修改人',
`remark` varchar(1024)   COMMENT '备注',
`platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
`org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
`status` int NOT NULL  COMMENT '状态',
`logic_status` int NOT NULL  COMMENT '逻辑删除字段',
`income_plan_num` varchar(255)   COMMENT '收入计划编号',
`mile_stone_name` varchar(255)   COMMENT '里程碑名称',
`income_verify_type` varchar(255)   COMMENT '收入确认类型',
`certificate_serial_number` varchar(255)   COMMENT '凭证编号',
`document_date` datetime   COMMENT '凭证日期',
`amt_no_tax` decimal   COMMENT '不含税金额',
`tax` decimal   COMMENT '税额',
`amt_tax` decimal   COMMENT '含税金额',
`project_number` varchar(255)   COMMENT '项目编号',
`contract_id` varchar(255)   COMMENT '关联合同id',
`text` varchar(255)   COMMENT '文本',
PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='预收款开票挂账信息';

(64)  COMMENT '销售业务分类' ;

CREATE TABLE `pmsx_invoicing_revenue_accounting` (
`id` varchar(64) NOT NULL  COMMENT '主键',
`class_name` varchar(64)   COMMENT '创建人',
`creator_id` varchar(64) NOT NULL  COMMENT '创建人',
`modify_time` datetime NOT NULL  COMMENT '修改时间',
`owner_id` varchar(64)   COMMENT '拥有者',
`create_time` datetime NOT NULL  COMMENT '创建时间',
`modify_id` varchar(64) NOT NULL  COMMENT '修改人',
`remark` varchar(1024)   COMMENT '备注',
`platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
`org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
`status` int NOT NULL  COMMENT '状态',
`logic_status` int NOT NULL  COMMENT '逻辑删除字段',
`income_plan_num` varchar(255)   COMMENT '收入计划编号',
`mile_stone_name` varchar(255)   COMMENT '里程碑名称',
`income_verify_type` varchar(255)   COMMENT '收入确认类型',
`certificate_serial_number` varchar(255)   COMMENT '凭证编号',
`document_date` datetime   COMMENT '凭证日期',
`amt_no_tax` decimal   COMMENT '不含税金额',
`tax` decimal   COMMENT '税额',
`amt_tax` decimal   COMMENT '含税金额   【税额】+【收入净额】',
`project_number` varchar(255)   COMMENT '项目编号',
`contract_id` varchar(255)   COMMENT '关联合同id',
`text` varchar(255)   COMMENT '文本',
PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='开票收入核算信息表';


CREATE TABLE `pmsx_provisional_income_accounting` (
`id` varchar(64) NOT NULL  COMMENT '主键',
`class_name` varchar(64)   COMMENT '创建人',
`creator_id` varchar(64) NOT NULL  COMMENT '创建人',
`modify_time` datetime NOT NULL  COMMENT '修改时间',
`owner_id` varchar(64)   COMMENT '拥有者',
`create_time` datetime NOT NULL  COMMENT '创建时间',
`modify_id` varchar(64) NOT NULL  COMMENT '修改人',
`remark` varchar(1024)   COMMENT '备注',
`platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
`org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
`status` int NOT NULL  COMMENT '状态',
`logic_status` int NOT NULL  COMMENT '逻辑删除字段',
`income_plan_num` varchar(255)   COMMENT '收入计划编号',
`mile_stone_name` varchar(255)   COMMENT '里程碑名称',
`income_verify_type` varchar(255)   COMMENT '收入确认类型',
`certificate_serial_number` varchar(255)   COMMENT '凭证编号',
`document_date` datetime   COMMENT '凭证日期',
`amt_no_tax` decimal   COMMENT '不含税金额',
`tax` decimal   COMMENT '税额',
`amt_tax` decimal   COMMENT '含税金额',
`project_number` varchar(255)   COMMENT '项目编号',
`text` varchar(255)   COMMENT '文本',
`contract_id` varchar(255)   COMMENT '关联合同id',
PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='暂估收入核算信息';



CREATE TABLE `pmsx_advance_payment_invoiced` (
`id` varchar(64) NOT NULL  COMMENT '主键',
`class_name` varchar(64)   COMMENT '创建人',
`creator_id` varchar(64) NOT NULL  COMMENT '创建人',
`modify_time` datetime NOT NULL  COMMENT '修改时间',
`owner_id` varchar(64)   COMMENT '拥有者',
`create_time` datetime NOT NULL  COMMENT '创建时间',
`modify_id` varchar(64) NOT NULL  COMMENT '修改人',
`remark` varchar(1024)   COMMENT '备注',
`platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
`org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
`status` int NOT NULL  COMMENT '状态',
`logic_status` int NOT NULL  COMMENT '逻辑删除字段',
`income_plan_num` varchar(255)   COMMENT '收入计划编号',
`mile_stone_name` varchar(255)   COMMENT '里程碑名称',
`income_verify_type` varchar(255)   COMMENT '收入确认类型',
`certificate_serial_number` varchar(255)   COMMENT '凭证编号',
`document_date` datetime   COMMENT '凭证日期',
`amt_no_tax` decimal   COMMENT '不含税金额',
`tax` decimal   COMMENT '税额',
`amt_tax` decimal   COMMENT '含税金额',
`project_number` varchar(255)   COMMENT '项目编号',
`contract_id` varchar(255)   COMMENT '关联合同id',
`text` varchar(255)   COMMENT '文本',
PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='预收款开票挂账信息';

INSERT INTO `dme_dict` (`id`, `keyword`, `label`, `parent_id`, `is_group`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `owner_id`, `remark`, `logic_status`, `type`) VALUES ('dict1858782792044351488', '所属基地', 'suoshujidi', '315f1d8308a74d48968f6005f2d1cb1b', 0, 4555, 'HOME_BASE', 1, NULL, '314j1000000000000000000', '2024-11-19 16:02:07', '314j1000000000000000000', '2024-11-19 16:02:07', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'Dict', '314j1000000000000000000', NULL, 1, 1);


INSERT INTO `dme_dict_value` (`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1858786128684486656', 'dict1858782792044351488', '', 'zhaoyuan', '', 0, 'zhaoyuan', 1, '招远', '314j1000000000000000000', '2024-11-19 16:15:22', '314j1000000000000000000', '2024-11-19 16:15:22', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);
INSERT INTO `dme_dict_value` (`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1858786128692875264', 'dict1858782792044351488', '', 'yuping', '', 0, 'yuping', 1, '玉屏', '314j1000000000000000000', '2024-11-19 16:15:22', '314j1000000000000000000', '2024-11-19 16:15:22', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);
INSERT INTO `dme_dict_value` (`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1858786128692875265', 'dict1858782792044351488', '', 'gongchenggongsi', '', 0, 'gongchenggongsi', 1, '工程公司', '314j1000000000000000000', '2024-11-19 16:15:22', '314j1000000000000000000', '2024-11-19 16:15:22', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);
INSERT INTO `dme_dict_value` (`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1858786128697069568', 'dict1858782792044351488', '', 'shejiyuan', '', 0, 'shejiyuan', 1, '设计院', '314j1000000000000000000', '2024-11-19 16:15:22', '314j1000000000000000000', '2024-11-19 16:15:22', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);
INSERT INTO `dme_dict_value` (`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1858786128701263872', 'dict1858782792044351488', '', 'yanjiuyuan', '', 0, 'yanjiuyuan', 1, '研究院', '314j1000000000000000000', '2024-11-19 16:15:22', '314j1000000000000000000', '2024-11-19 16:15:22', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);
INSERT INTO `dme_dict_value` (`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1858786128701263873', 'dict1858782792044351488', '', 'CNOC', '', 0, 'CNOC', 1, 'CNOC', '314j1000000000000000000', '2024-11-19 16:15:22', '314j1000000000000000000', '2024-11-19 16:15:22', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);
INSERT INTO `dme_dict_value` (`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1858784724771246080', 'dict1858782792044351488', '', 'dayawan', '', 0, 'dayawan', 1, '大亚湾', '314j1000000000000000000', '2024-11-19 16:09:48', '314j1000000000000000000', '2024-11-19 16:15:19', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);
INSERT INTO `dme_dict_value` (`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1858784724783828992', 'dict1858782792044351488', '', 'hongyanhe', '', 0, 'hongyanhe', 1, '红沿河', '314j1000000000000000000', '2024-11-19 16:09:48', '314j1000000000000000000', '2024-11-19 16:15:20', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);
INSERT INTO `dme_dict_value` (`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1858784724788023296', 'dict1858782792044351488', '', 'ningde', '', 0, 'ningde', 1, '宁德', '314j1000000000000000000', '2024-11-19 16:09:48', '314j1000000000000000000', '2024-11-19 16:15:20', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);
INSERT INTO `dme_dict_value` (`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1858784724792217600', 'dict1858782792044351488', '', 'yangjiang', '', 0, 'yangjiang', 1, '阳江', '314j1000000000000000000', '2024-11-19 16:09:48', '314j1000000000000000000', '2024-11-19 16:15:21', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);
INSERT INTO `dme_dict_value` (`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1858784724800606208', 'dict1858782792044351488', '', 'fangchenggang', '', 0, 'fangchenggang', 1, '防城港', '314j1000000000000000000', '2024-11-19 16:09:48', '314j1000000000000000000', '2024-11-19 16:15:21', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);
INSERT INTO `dme_dict_value` (`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1858784724813189120', 'dict1858782792044351488', '', 'taishan', '', 0, 'taishan', 1, '台山', '314j1000000000000000000', '2024-11-19 16:09:48', '314j1000000000000000000', '2024-11-19 16:15:21', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);
INSERT INTO `dme_dict_value` (`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1858784724817383424', 'dict1858782792044351488', '', 'huizhou', '', 0, 'huizhou', 1, '惠州', '314j1000000000000000000', '2024-11-19 16:09:48', '314j1000000000000000000', '2024-11-19 16:15:22', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);
INSERT INTO `dme_dict_value` (`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1858784724834160640', 'dict1858782792044351488', '', 'cangnan', '', 0, 'cangnan', 1, '苍南', '314j1000000000000000000', '2024-11-19 16:09:48', '314j1000000000000000000', '2024-11-19 16:15:22', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);
INSERT INTO `dme_dict_value` (`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1858784724842549248', 'dict1858782792044351488', '', 'lufeng', '', 0, 'lufeng', 1, '陆丰', '314j1000000000000000000', '2024-11-19 16:09:48', '314j1000000000000000000', '2024-11-19 16:15:22', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);

ALTER TABLE pms_contract_milestone MODIFY COLUMN milestone_amt DECIMAL(20,2) COMMENT '合同约定验收金额';
ALTER TABLE pms_contract_milestone MODIFY COLUMN plan_accept_date DATETIME COMMENT '合同约定验收日期';

ALTER TABLE  pms_contract_milestone ADD COLUMN `ammount_type` VARCHAR(64) COMMENT '金额类型';

ALTER TABLE  pms_contract_milestone ADD COLUMN `date_type` VARCHAR(64) COMMENT '日期类型';

INSERT INTO `pmi_data_status` (`id`, `class_name`, `parent_id`, `label`, `color`, `name`, `status_value`, `description`, `remark`, `creator_id`, `modify_id`, `modify_time`, `owner_id`, `create_time`, `status`, `sort`, `platform_id`, `unique_key`, `logic_status`, `is_initial_value`) VALUES ('qtks1859546815866089472', 'DataStatus', 'txf71795823235359830016', NULL, '5', '审批中', 120, NULL, NULL, 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-11-21 18:38:04', 'user00000000000000000100000000000000', '2024-11-21 18:38:04', 1, 5038, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 0);
ALTER TABLE `pms_contract_milestone` ADD COLUMN `voucher_num` VARCHAR(255)  COMMENT '凭证号' ;
ALTER TABLE `pms_contract_milestone` ADD COLUMN `pass_account_date` DATETIME COMMENT '过帐日期' ;

ALTER TABLE `pms_contract_milestone` ADD  `confirm_income_provisional_estimate` DECIMAL(20,2)  COMMENT '确认收入金额-暂估收入';
ALTER TABLE `pms_contract_milestone` ADD  `confirm_income_invoicing` DECIMAL(20,2)  COMMENT '确认收入金额-开票收入';
ALTER TABLE `pms_contract_milestone` ADD  `confirm_income_sum` DECIMAL(20,2)  COMMENT '确认收入金额-合计值';

ALTER TABLE `pms_contract_milestone` add column `milestone_number` varchar(255) COMMENT '里程碑编号';

INSERT INTO `xxl_job_info` (`id`, `job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (1143, 5, '项目立项更新至里程碑', '2024-11-26 15:19:21', '2024-11-26 15:19:29', 'orion', '', 'CRON', '0 0 22 * * ? *', 'DO_NOTHING', 'FIRST', 'projectInitaionXxlJobHandler', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-11-26 15:19:21', '', 1, 1732629600000, 1732716000000);
ALTER TABLE `pms_contract_milestone` add column `is_track_confirm` varchar(255)  COMMENT '是否被跟踪确认';
INSERT INTO `xxl_job_info` (`id`, `job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (1140, 5, '商城子订单分发', '2024-11-11 09:12:23', '2024-11-11 10:12:40', 'orion', '', 'CRON', '0 30 22 * * ? *', 'DO_NOTHING', 'FIRST', 'marketOrderDistributeJob', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-11-11 09:12:23', '', 1, 1732717800000, 1732804200000);

INSERT INTO `dme_dict` (`id`, `keyword`, `label`, `parent_id`, `is_group`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `owner_id`, `remark`, `logic_status`, `type`) VALUES ('dict1862311952813064192', '日期类型', 'riqileixing', '315f1d8308a74d48968f6005f2d1cb1b', 0, 4595, 'date_type', 1, NULL, '314j1000000000000000000', '2024-11-29 09:45:44', '314j1000000000000000000', '2024-11-29 09:45:44', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'Dict', '314j1000000000000000000', NULL, 1, 1);
INSERT INTO `dme_dict` (`id`, `keyword`, `label`, `parent_id`, `is_group`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `owner_id`, `remark`, `logic_status`, `type`) VALUES ('dict1862310899522666496', '金融类型', 'jinrongleixing', '315f1d8308a74d48968f6005f2d1cb1b', 0, 4585, 'finance_type', 1, NULL, '314j1000000000000000000', '2024-11-29 09:41:33', '314j1000000000000000000', '2024-11-29 09:41:33', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'Dict', '314j1000000000000000000', NULL, 1, 1);

INSERT INTO `dme_dict_value` (`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1862312231742668800', 'dict1862311952813064192', '', 'plan_accept_date', '', 0, 'plan_accept_date', 1, '合同约定验收日期', '314j1000000000000000000', '2024-11-29 09:46:51', '314j1000000000000000000', '2024-11-29 09:46:51', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);
INSERT INTO `dme_dict_value` (`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1862312231755251712', 'dict1862311952813064192', '', 'expect_accept_date', '', 0, 'expect_accept_date', 1, '预计验收日期', '314j1000000000000000000', '2024-11-29 09:46:51', '314j1000000000000000000', '2024-11-29 09:46:51', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);

INSERT INTO `dme_dict_value` (`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1862311785552609280', 'dict1862310899522666496', '', 'milestone_amt', '', 0, 'milestone_amt', 1, '合同约定验收金额', '314j1000000000000000000', '2024-11-29 09:45:04', '314j1000000000000000000', '2024-11-29 09:45:04', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);
INSERT INTO `dme_dict_value` (`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1862311785565192192', 'dict1862310899522666496', '', 'except_acceptance_amt', '', 0, 'except_acceptance_amt', 1, '预计验收金额', '314j1000000000000000000', '2024-11-29 09:45:04', '314j1000000000000000000', '2024-11-29 09:45:04', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, '314j1000000000000000000', NULL);
ALTER TABLE pms_contract_milestone ADD `business_income_type` varchar(255) COMMENT '业务收入类型';

UPDATE `dme_dict_value` SET `dict_id` = 'dict1856523039805140992', `label` = '', `value` = 'frame', `sub_dict_id` = '', `sort` = 0, `number` = 'frame_type', `status` = 1, `description` = '框架', `creator_id` = '314j1000000000000000000', `create_time` = '2024-11-13 10:26:46', `modify_id` = '314j1000000000000000000', `modify_time` = '2024-11-13 10:27:31', `platform_id` = 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', `unique_key` = NULL, `class_name` = 'DictValue', `logic_status` = 1, `owner_id` = '314j1000000000000000000', `remark` = NULL WHERE `id` = 'v0ss1856524070702149632';
UPDATE `dme_dict_value` SET `dict_id` = 'dict1856523039805140992', `label` = '', `value` = 'totalPrice', `sub_dict_id` = '', `sort` = 0, `number` = 'totalPrice__type', `status` = 1, `description` = '总价', `creator_id` = '314j1000000000000000000', `create_time` = '2024-11-13 10:27:31', `modify_id` = '314j1000000000000000000', `modify_time` = '2024-11-13 10:27:31', `platform_id` = 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', `unique_key` = NULL, `class_name` = 'DictValue', `logic_status` = 1, `owner_id` = '314j1000000000000000000', `remark` = NULL WHERE `id` = 'v0ss1856524261199048704';
ALTER TABLE `pms_contract_our_signed_subject` add column `customer` varchar(255) COMMENT '客户id';