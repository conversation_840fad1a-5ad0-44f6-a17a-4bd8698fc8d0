package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/07/29/11:07
 * @description:
 */

@ApiModel(value = "TrainContactDTO对象", description = "培训联络人信息表")
@Data
@ExcelIgnoreUnannotated
public class TrainContactDTO extends ObjectDTO implements Serializable {

    @ApiModelProperty(value = "联络人类型")
    private String contactType;
    /**
     * 中心ID或部门ID
     */
    @ApiModelProperty(value = "中心ID或部门ID")
    @ExcelProperty(value = "中心ID或部门ID ", index = 0)
//    @NotEmpty(message = "所属中心或者部门不能为空")
    private String deptId;

    /**
     * 联系人id拼接
     */
    @ApiModelProperty(value = "联系人id拼接")
    @ExcelProperty(value = "联系人id拼接 ", index = 1)
    private String contactPersonIds;

    /**
     * 中心名称或者部门名称
     */
    @ApiModelProperty(value = "中心名称或者部门名称")
    @ExcelProperty(value = "中心名称或者部门名称 ", index = 2)
//    @NotEmpty(message = "部门名称不能为空")
    private String deptName;

    /**
     * 联系人名称拼接
     */
    @ApiModelProperty(value = "联系人名称拼接")
    @ExcelProperty(value = "联系人名称拼接 ", index = 3)
    private String contactPersonNames;


    @Size(min = 1,max = 5,message = "中心的联络人的最多不超过五个人")
    @ApiModelProperty(value = "联络人Id集合")
    private List<String> contactPersonIdList;

    @ApiModelProperty(value = "联络人名称集合")
    private List<String> contactPersonNameList;


    /**
     * 基地编码
     */
    @ApiModelProperty(value = "基地编码")
    private String baseCode;

    /**
     * 基地名称
     */
    @ApiModelProperty(value = "基地名称")
    private String baseName;

    /**
     * 管理属性
     */
    @ApiModelProperty(value = "管理属性")
    private String manageType;


}

