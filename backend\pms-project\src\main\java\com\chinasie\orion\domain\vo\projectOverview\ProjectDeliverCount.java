package com.chinasie.orion.domain.vo.projectOverview;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/07/14:01
 * @description:
 */
@Data
@ApiModel(value = "ProjectDeliverCount对象", description = "项目交付物详情")
public class ProjectDeliverCount implements Serializable {
    @ApiModelProperty(value = "提交数量")
    private Integer commitCount = 0;
    @ApiModelProperty(value = "未提交数量")
    private Integer unCommitCount= 0;
    @ApiModelProperty(value = "提交数量百分比")
    private String commitPercentage = "0%";
    @ApiModelProperty(value = "未提交数量百分比")
    private String unCommitPercentage= "0%";

    @ApiModelProperty(value = "总数")
    private Integer totalCount= 0;


}
