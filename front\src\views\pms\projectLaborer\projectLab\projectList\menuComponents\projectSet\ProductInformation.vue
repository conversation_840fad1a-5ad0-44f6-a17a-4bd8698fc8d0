<template>
  <div class="locations">
    <OrionTable
      ref="tableRef"
      :options="tableOptionsSource"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if=" isPower('PMS_XMXQ_container_12_01_product_head_button_01', headAuthList)"
          type="primary"
          icon="add"
          @click="applyProductCode"
        >
          申请PLM产品编码
        </BasicButton>
        <BasicButton
          v-if=" isPower('PMS_XMXQ_container_12_01_product_head_button_02', headAuthList)"
          type="primary"
          icon="add"
          @click="simultaneousProject"
        >
          同步立项&策划信息
        </BasicButton>
      </template>
    </OrionTable>
  </div>
</template>
<script setup lang="ts">
import {
  h, ref,
} from 'vue';
import {
  DataStatusTag, OrionTable, BasicButton, isPower,
} from 'lyra-component-vue3';
import { message, Modal } from 'ant-design-vue';
import { applyPLMById, syncApprovalById } from '/@/views/pms/api/project';
import { deleteById, listByProjectId } from '/@/views/pms/api/projectToProduct';
const props = defineProps<{
  projectId:string
}>();
const tableRef = ref();
const headAuthList = ref([]);
const tableOptionsSource = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  api: () => listByProjectId({
    query: {
      projectId: props.projectId,
    },
    power: {
      pageCode: 'PMS0004',
      headContainerCode: 'PMS_XMXQ_container_12_01_product_head',
      containerCode: 'PMS_XMXQ_container_12_01_product_list',
    },
  }).then((res) => {
    headAuthList.value = res.headAuthList;
    return res;
  }),
  columns: [
    {
      title: '商机产品编号',
      dataIndex: 'numberBOP',
    },
    {
      title: 'PLM产品编号',
      dataIndex: 'numberPLM',
    },
    {
      title: '产品名称',
      dataIndex: 'name',
    },
    {
      title: '状态',
      dataIndex: 'dataStatus',
      customRender: ({ record }) => h(DataStatusTag, { statusData: record.dataStatus }),
    },
    {
      title: '产品型号',
      dataIndex: 'productModelNumber',
    },
    {
      title: '产品组',
      dataIndex: 'productGroupName',
    },
    {
      title: '物料类别',
      dataIndex: 'materialTypeName',
    },
    {
      title: '产品分类',
      dataIndex: 'productClassifyName',
    },
    {
      title: '产品二级分类',
      dataIndex: 'productSecondClassifyName',
    },
    {
      title: 'J/民品分类',
      dataIndex: 'militaryCivilianName',
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 120,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '移除',
      ifShow: (record) => isPower('PMS_XMXQ_container_12_01_product_list_button_01', record.rdAuthList),
      modal: async (record: Record<string, any>) => {
        await deleteById(record.id);
        tableRef.value.reload();
      },
    },
  ],
};

const applyProductCode = () => {
  Modal.confirm({
    title: '请确认是否申请产品编码',
    content: '发起申请后，项目和产品编码将绑定，无法移除',
    async onOk() {
      await applyPLMById(props.projectId);
      message.success('操作成功');
      tableRef.value.reload();
    },
    onCancel() {
      Modal.destroyAll();
    },
  });
};
const simultaneousProject = () => {
  Modal.confirm({
    title: '请确认是否同步立项&策划信息',
    content: '确认后，将同步收益策划、风险策划等信息',
    async onOk() {
      await syncApprovalById(props.projectId);
      message.success('操作成功');
      tableRef.value.reload();
    },
    onCancel() {
      Modal.destroyAll();
    },
  });
};

</script>
<style lang="less" scoped>
.locations{
  height: 400px;
  overflow: hidden;
}
</style>
