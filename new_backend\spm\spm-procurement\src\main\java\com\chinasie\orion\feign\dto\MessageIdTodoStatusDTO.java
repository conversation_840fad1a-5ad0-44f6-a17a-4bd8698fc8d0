package com.chinasie.orion.feign.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @className MessageIdTodoStatusDTO
 * @description
 * @since 2024/5/8
 */
@Data
@ApiModel(value = "MessageIdTodoStatusDTO", description = "待办状态DTO（消息ID）")
public class MessageIdTodoStatusDTO {
    @ApiModelProperty("消息接收人ID")
    private String userId;

    @ApiModelProperty("消息ID列表")
    private List<String> messageIdList;
}
