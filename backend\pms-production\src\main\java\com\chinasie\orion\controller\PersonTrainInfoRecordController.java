package com.chinasie.orion.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;




import com.chinasie.orion.domain.entity.PersonTrainInfoRecord;
import com.chinasie.orion.domain.dto.PersonTrainInfoRecordDTO;
import com.chinasie.orion.domain.vo.PersonTrainInfoRecordVO;

import com.chinasie.orion.service.PersonTrainInfoRecordService;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * PersonTrainInfoRecord 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-22 16:21:02
 */
@RestController
@RequestMapping("/personTrainInfoRecord")
@Api(tags = "用户培训信息落地")
public class  PersonTrainInfoRecordController  {

    @Autowired
    private PersonTrainInfoRecordService personTrainInfoRecordService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看【用户培训信息落地】信息【{{#trainName}}】", type = "PersonTrainInfoRecord", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<PersonTrainInfoRecordVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        PersonTrainInfoRecordVO rsp = personTrainInfoRecordService.detail(id,pageCode);
        LogRecordContext.putVariable("trainName", rsp.getTrainName());
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param personTrainInfoRecordDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【用户培训信息落地】数据【{{#personTrainInfoRecordDTO.trainName}}】", type = "PersonTrainInfoRecord", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody PersonTrainInfoRecordDTO personTrainInfoRecordDTO) throws Exception {
        String rsp =  personTrainInfoRecordService.create(personTrainInfoRecordDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param personTrainInfoRecordDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【用户培训信息落地】数据【{{#personTrainInfoRecordDTO.trainName}}】", type = "PersonTrainInfoRecord", subType = "编辑", bizNo = "{{#personTrainInfoRecordDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  PersonTrainInfoRecordDTO personTrainInfoRecordDTO) throws Exception {
        Boolean rsp = personTrainInfoRecordService.edit(personTrainInfoRecordDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【用户培训信息落地】数据", type = "PersonTrainInfoRecord", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = personTrainInfoRecordService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【用户培训信息落地】数据", type = "PersonTrainInfoRecord", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = personTrainInfoRecordService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【用户培训信息落地】数据", type = "PersonTrainInfoRecord", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<PersonTrainInfoRecordVO>> pages(@RequestBody Page<PersonTrainInfoRecordDTO> pageRequest) throws Exception {
        Page<PersonTrainInfoRecordVO> rsp =  personTrainInfoRecordService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }
    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "列表")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【用户培训信息落地】数据", type = "PersonTrainInfoRecord", subType = "列表查询", bizNo = "")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public ResponseDTO<List<PersonTrainInfoRecordVO>> listByEntity(@RequestBody PersonTrainInfoRecordDTO pageRequest) throws Exception {
        List<PersonTrainInfoRecordVO> rsp =  personTrainInfoRecordService.listByEntity( pageRequest);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("用户培训信息落地导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载【用户培训信息落地】导入模板", type = "PersonTrainInfoRecord", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        personTrainInfoRecordService.downloadExcelTpl(response);
    }

    @ApiOperation("用户培训信息落地导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验【用户培训信息落地】导入", type = "PersonTrainInfoRecord", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = personTrainInfoRecordService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("用户培训信息落地导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认【用户培训信息落地】导入", type = "PersonTrainInfoRecord", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  personTrainInfoRecordService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消用户培训信息落地导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消【用户培训信息落地】导入", type = "PersonTrainInfoRecord", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  personTrainInfoRecordService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("用户培训信息落地导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出【用户培训信息落地】数据", type = "PersonTrainInfoRecord", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        personTrainInfoRecordService.exportByExcel(searchConditions, response);
    }
}
