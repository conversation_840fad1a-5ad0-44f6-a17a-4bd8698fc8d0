<template>
  <BasicDrawer
    v-bind="$attrs"
    title=""
    width="1000"
    :min-height="600"
    @register="registerModal"
    @visible-change="visibleChange"
  >
    <div class="add-plan-table-node-content">
      <BasicForm
        v-if="showForm"
        class="content-form"
        @register="registerForm"
      />
    </div>

    <template #footer>
      <div class="add-plan-footer">
        <div class="btn-style">
          <BasicButton
            class="canncel"
            @click="cancel"
          >
            取消
          </BasicButton>
          <BasicButton
            type="primary"
            :loading="loadingBtn"
            @click="confirm('save')"
          >
            保存
          </BasicButton>
          <BasicButton
            v-if="formType==='add'||formStatus===100"
            type="primary"
            :loading="loadingBtn"
            @click="confirm('startProcess')"
          >
            启动流程
          </BasicButton>
        </div>
      </div>
    </template>
  </BasicDrawer>
</template>
<script lang="ts">
import {
  defineComponent, onMounted, reactive, toRefs, computed, ref, nextTick,
} from 'vue';
import {
  useDrawerInner, BasicForm, useForm, BasicDrawer, BasicButton,
} from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import Api from '/@/api';
import {
  monthlyFeedbackForm, addMonthFeedbackList, editMonthFeedbackList, initMonthFeedbackDetails, getMonthFeedbackDetails,
} from '../index';
export default defineComponent({
  name: 'AddTableNode',
  components: {
    BasicForm,
    BasicDrawer,
    BasicButton,
  },
  emits: ['update'],
  setup(props, { emit }) {
    const state = reactive({
      formType: '',
      formId: '',
      yearId: '',
      showForm: false,
      monthNumber: 5,
      yearNumber: 0,
      formMethod: '' as any,
      loadingBtn: false,
      disabledReason: false,
      formStatus: 100,

    });
    const [registerModal, { closeDrawer, setDrawerProps }] = useDrawerInner(async (modalData) => {
      state.formType = modalData.type;
      setDrawerProps({ title: '编制月度投资计划反馈表' });
      if (state.formType === 'add') {
        state.yearId = modalData.yearId;
        initMonthFeedbackDetails(modalData.yearId).then((res) => {
          state.monthNumber = Number(res.month.split('月')[0]);
          state.yearNumber = Number(res.year);
          state.showForm = true;
          res.implementation = Number(res.mplanDo) === 0 ? '0.00%' : `${((Number(res.mpracticeDo) / Number(res.mplanDo)) * 100).toFixed(2)}%`;
          res.yearCompleteRate = Number(res.yinvestmentPlan) === 0 ? '0.00%' : `${((Number(res.mpracticeDo) / Number(res.yinvestmentPlan)) * 100).toFixed(2)}%`;
          // state.formMethod.setFieldsValue({ implementation: Number(mplanDo) === 0 ? '0.00%' : `${((val / Number(mplanDo)) * 100).toFixed(2)}%`, yearCompleteRate: Number(yinvestmentPlan) === 0 ? '0.00%' : `${((val / Number(yinvestmentPlan)) * 100).toFixed(2)}%` });
          nextTick(() => {
            // if()
            formMethod.setFieldsValue(res);
            formMethod.clearValidate();
          });
        });
      } else {
        state.formId = modalData.id;
        getMonthFeedbackDetails(state.formId).then((res) => {
          state.monthNumber = Number(res.month.split('月')[0]);
          state.yearNumber = Number(res.year);
          state.formStatus = res.status;
          res.implementation = `${((res.mpracticeDo / Number(res.mplanDo)) * 100).toFixed(2)}%`;
          res.yearCompleteRate = `${((res.mpracticeDo / Number(res.yinvestmentPlan)) * 100).toFixed(2)}%`;
          state.showForm = true;
          if (res.monthDoStatus === 1) {
            state.disabledReason = true;
          }
          nextTick(() => {
            formMethod.setFieldsValue(res);
            formMethod.clearValidate();
          });
        });
      }
    });
    // appendix 附件
    const [registerForm, formMethod] = useForm(monthlyFeedbackForm(state));
    function cancel() {
      closeDrawer();
    }
    const confirm = async (type = 'save') => {
      let formData: any = await formMethod.validateFields();
      let api = state.formType === 'add' ? addMonthFeedbackList : editMonthFeedbackList;
      let msg = state.formType === 'add' ? '新增成功' : '编辑成功';
      if (state.formType === 'add') {
        formData.yearInvestmentId = state.yearId;
      } else {
        formData.id = state.formId;
      }
      api(formData).then((res) => {
        message.success(msg);
        if (type === 'save') {
          emit('update', { type: 'save' });
        } else {
          emit('update', {
            type,
            record: res,
          });
        }
        closeDrawer();
        state.loadingBtn = false;
      }).catch((err) => {
        state.loadingBtn = false;
      });
    };
    function visibleChange(val) {
      if (!val) {
        state.showForm = false;
      }
    }
    onMounted(() => {
      state.formMethod = formMethod;
    });
    return {
      ...toRefs(state),
      registerModal,
      registerForm,
      cancel,
      confirm,
      visibleChange,
    };
  },

});
</script>
<style lang="less" scoped>
.add-plan-table-node-content{
  display: flex;
  height: 100%;
  flex-direction: column;
 :deep(.ant-form-item){
   display: block;
 }
}
.add-plan-footer{
  display: flex;
  justify-content: space-between;
  .next-check-box{
    line-height: 32px;
  }
  .btn-style{
    flex: 1;
    text-align: right;
    .canncel{
      margin-right: 10px;
    }
  }
}
</style>
<style lang="less">
</style>
