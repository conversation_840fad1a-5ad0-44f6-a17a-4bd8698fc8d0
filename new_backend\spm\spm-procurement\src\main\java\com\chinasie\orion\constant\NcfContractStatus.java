package com.chinasie.orion.constant;

import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class NcfContractStatus {

    private static List<String> allStatus = new ArrayList<>();
    private  static LinkedHashMap<String, String> statusMap = new LinkedHashMap<>();

    /**
     * 采购申请完成
     */
    static String PURCHASE_APPLY_COMPLETE = "采购立项完成";

    /**
     * 一级分发
     */
    static String ONE_LEVEL = "一级分发";

    /**
     * 二级分发
     */
    static String TWO_LEVEL = "二级分发";

    /**
     * 接受确认
     */
    static String CONFIRM = "接受确认";

    /**
     * 采购启动
     */
    static String PURCHASE_START = "采购启动";

    /**
     * 采购启动审批
     */
    static String PURCHASE_START_APPROVE = "采购启动审批";

    /**
     * 询价签发
     */
    static String ISSUE_ASK_PRICE = "询价签发";

    /**
     * 报价截至
     */
    static String QUOTE_END = "报价截至";

    /**
     * 开启报价
     */
    static String START_QUOTE = "开启报价";

    /**
     * 采购审批
     */
    static String PURCHASE_APPROVE = "采购审批";

    /**
     * UPM合同推荐
     */
    static String UPM_CONTRACT_RECOMMEND = "UPM合同推荐";

    /**
     * UPM审批完成
     */
    static String UPM_APPROVE_COMPLETE = "UPM审批完成";

    /**
     * 发送SAP
     */
    static String SEND_SAP = "发送SAP";

    /**
     * 已完成
     */
    static String COMPLETE = "已完成";


    public static List<String> getAllStatus() {
        if (CollectionUtils.isEmpty(allStatus)){
            allStatus.add(PURCHASE_APPLY_COMPLETE);
            allStatus.add(ONE_LEVEL);
            allStatus.add(TWO_LEVEL);
            allStatus.add(CONFIRM);
            allStatus.add(PURCHASE_START);
            allStatus.add(PURCHASE_START_APPROVE);
            allStatus.add(ISSUE_ASK_PRICE);
            allStatus.add(QUOTE_END);
            allStatus.add(START_QUOTE);
            allStatus.add(PURCHASE_APPROVE);
            allStatus.add(UPM_CONTRACT_RECOMMEND);
            allStatus.add(UPM_APPROVE_COMPLETE);
            allStatus.add(SEND_SAP);
            allStatus.add(COMPLETE);
        }
        return allStatus;
    }

    /**
     * 返回map结构，key对应当前节点，value对应当前节点的上一个节点
     */
    public static Map<String, String> buildStatusTransitionMap() {
        if (ObjectUtils.isEmpty(statusMap)){
            statusMap.put(ONE_LEVEL, PURCHASE_APPLY_COMPLETE);
            statusMap.put(TWO_LEVEL, ONE_LEVEL);
            statusMap.put(CONFIRM, TWO_LEVEL);
            statusMap.put(PURCHASE_START, CONFIRM);
            statusMap.put(PURCHASE_START_APPROVE, PURCHASE_START);
            statusMap.put(ISSUE_ASK_PRICE, PURCHASE_START_APPROVE);
            statusMap.put(QUOTE_END, ISSUE_ASK_PRICE);
            statusMap.put(START_QUOTE, QUOTE_END);
            statusMap.put(PURCHASE_APPROVE, START_QUOTE);
            statusMap.put(UPM_CONTRACT_RECOMMEND, PURCHASE_APPROVE);
            statusMap.put(UPM_APPROVE_COMPLETE, UPM_CONTRACT_RECOMMEND);
            statusMap.put(SEND_SAP, UPM_APPROVE_COMPLETE);
            statusMap.put(COMPLETE, SEND_SAP);
        }
        return statusMap;
    }


}
