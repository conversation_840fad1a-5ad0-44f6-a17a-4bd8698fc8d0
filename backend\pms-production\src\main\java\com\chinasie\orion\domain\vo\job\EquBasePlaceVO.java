package com.chinasie.orion.domain.vo.job;

import com.chinasie.orion.file.api.domain.vo.FileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/11/11:31
 * @description:
 */
@Data
public class EquBasePlaceVO implements Serializable {
    @ApiModelProperty(value = "等效ID")
    private String equId;

    @ApiModelProperty(value = "基地编码")
    private String baseCode;
    @ApiModelProperty(value = "基地名称")
    private String baseName;
    @ApiModelProperty("等效认定时间")
    private Date createTime;

    @ApiModelProperty("等效认定证书")
    private List<FileVO> fileVOList;
}
