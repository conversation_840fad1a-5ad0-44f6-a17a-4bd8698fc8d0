package com.chinasie.orion.constant;

/**
 * @author: yk
 * @date: 2023/10/25 20:40
 */
public enum ProjectContractPayNodeAuditStatusEnum {
    SAVE(101, "已保存"),
    AUDITING(110, "审核中"),
    AUDITED(130, "已审核"),
    REJECT(104, "已驳回"),
    ;


    private Integer status;

    private String desc;


    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    ProjectContractPayNodeAuditStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
