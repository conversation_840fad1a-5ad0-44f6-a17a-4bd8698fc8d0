<script setup lang="ts">

import { DataStatusTag, Layout, OrionTable } from 'lyra-component-vue3';
import { h } from 'vue';
import dayjs from 'dayjs';
import Api from '/src/api';

const tableOptions = {
  showToolButton: false,
  rowSelection: {},
  columns: [
    {
      title: '线索编号',
      dataIndex: 'number',
      width: 120,
      slots: { customRender: 'name' },
    },
    {
      title: '线索名称',
      dataIndex: 'name',
      minWidth: 120,
    },
    {
      title: '状态',
      dataIndex: 'busStatus',
      maxWidth: 120,
    },
    {
      title: '线索来源',
      dataIndex: 'sourceName',
      width: 120,
    },
    {
      title: '线索客户',
      dataIndex: 'custName',
      width: 100,
    },
    {
      title: '提出人',
      dataIndex: 'projectPerson',
      width: 100,
    },
    {
      title: '提出时间',
      dataIndex: 'proposeDate',
      width: 100,
    },
  ],
  api: (params) => new Api('/pms/projectInitiation/getClues').fetch(params, '', 'POST'),
};
</script>

<template>
  <Layout :options="{ body: { scroll: true } }">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      class="card-list-table"
    />
  </Layout>
</template>

<style scoped lang="less">

</style>