package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.domain.dto.DocumentDecompositionDTO;
import com.chinasie.orion.domain.entity.DocumentDecomposition;
import com.chinasie.orion.domain.entity.TaskDecomposition;
import com.chinasie.orion.domain.vo.DocumentDecompositionVO;
import com.chinasie.orion.domain.vo.SimpleVo;
import com.chinasie.orion.number.api.domain.GenerateNumberRequest;
import com.chinasie.orion.number.api.sdk.NumberApiService;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.DocumentDecompositionMapper;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.DocumentDecompositionService;
import com.chinasie.orion.service.TaskDecompositionService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.TreeUtils;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * DocumentDecomposition 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-01 17:00:34
 */
@Service
@Slf4j
public class DocumentDecompositionServiceImpl extends  OrionBaseServiceImpl<DocumentDecompositionMapper, DocumentDecomposition>   implements DocumentDecompositionService {

    @Autowired
    private NumberApiService numberApiService;

    @Autowired
    private TaskDecompositionService taskDecompositionService;

    @Autowired
    private ClassRedisHelper classRedisHelper;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public DocumentDecompositionVO detail(String id, String pageCode) throws Exception {
        DocumentDecomposition documentDecomposition = this.getById(id);
        DocumentDecompositionVO result = BeanCopyUtils.convertTo(documentDecomposition,DocumentDecompositionVO::new);

        setEveryName(documentDecomposition.getMainTableId(), Collections.singletonList(result));
//        result.setIsRelationTask(StrUtil.isNotBlank(documentDecomposition.getTaskId()) ? Boolean.TRUE : Boolean.FALSE);
        return result;
    }

    /**
     *  新增
     *
     * * @param documentDecompositionDTO
     */
    @Override
    public  String create(DocumentDecompositionDTO documentDecompositionDTO) throws Exception {
        DocumentDecomposition documentDecomposition =BeanCopyUtils.convertTo(documentDecompositionDTO,DocumentDecomposition::new);
        GenerateNumberRequest generateNumberRequest = new GenerateNumberRequest();
        generateNumberRequest.setClazzName(DocumentDecomposition.class.getSimpleName());
        generateNumberRequest.setEffectFlag(true);
        String generate = numberApiService.generate(generateNumberRequest);
        documentDecomposition.setNumber(generate);

        this.save(documentDecomposition);

        return documentDecomposition.getId();
    }

    /**
     *  编辑
     *
     * * @param documentDecompositionDTO
     */
    @Override
    public Boolean edit(DocumentDecompositionDTO documentDecompositionDTO) throws Exception {
        DocumentDecomposition documentDecomposition =BeanCopyUtils.convertTo(documentDecompositionDTO,DocumentDecomposition::new);

        this.updateById(documentDecomposition);

        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        taskDecompositionService.remove(new LambdaQueryWrapperX<>(TaskDecomposition.class)
                .select(TaskDecomposition::getId)
                .in(TaskDecomposition::getProcessInstances, ids));
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<DocumentDecompositionVO> pages(String mainTableId, Page<DocumentDecompositionDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<DocumentDecomposition> condition = new LambdaQueryWrapperX<>( DocumentDecomposition. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(DocumentDecomposition::getCreateTime);
            condition.eq(DocumentDecomposition::getMainTableId, mainTableId);

        Page<DocumentDecomposition> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), DocumentDecomposition::new));

        PageResult<DocumentDecomposition> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<DocumentDecompositionVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<DocumentDecompositionVO> vos = BeanCopyUtils.convertListTo(page.getContent(), DocumentDecompositionVO::new);
        setEveryName(mainTableId, vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public List<SimpleVo> list(String mainTableId) throws Exception {
        List<DocumentDecomposition> documentDecompositionVOS = getDocumentDecompositionVOS(mainTableId);
        if (CollectionUtil.isEmpty(documentDecompositionVOS)){
            return new ArrayList<>();
        }
        return documentDecompositionVOS.stream().map(m->{
            SimpleVo simpleVo = new SimpleVo();
            simpleVo.setId(m.getId());
            simpleVo.setName(m.getName());
            return simpleVo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<DocumentDecompositionVO> listTree(String mainTableId) throws Exception {
        List<DocumentDecomposition> documentDecompositionList = getDocumentDecompositionVOS(mainTableId);
        if (CollectionUtil.isEmpty(documentDecompositionList)){
            return new ArrayList<>();
        }
        List<DocumentDecompositionVO> documentDecompositionVOS = BeanCopyUtils.convertListTo(documentDecompositionList, DocumentDecompositionVO::new);
        setEveryName(mainTableId, documentDecompositionVOS);
        return TreeUtils.tree(documentDecompositionVOS);
    }

    @NotNull
    private List<DocumentDecomposition> getDocumentDecompositionVOS(String mainTableId) {
        LambdaQueryWrapperX<DocumentDecomposition> lambdaQueryWrapperX = new LambdaQueryWrapperX<>( DocumentDecomposition. class);
        lambdaQueryWrapperX.eq(DocumentDecomposition::getMainTableId, mainTableId);
        return this.list(lambdaQueryWrapperX);
    }

    @Override
    public Boolean generateTask(String mainTableId) throws Exception {
        List<DocumentDecomposition> documentDecompositionList = getDocumentDecompositionVOS(mainTableId);
        if (CollectionUtil.isEmpty(documentDecompositionList)){
            return Boolean.FALSE;
        }
        List<String> documentDecompositionIds = documentDecompositionList.stream().map(DocumentDecomposition::getId).collect(Collectors.toList());
        List<TaskDecomposition> list = taskDecompositionService.list(new LambdaQueryWrapperX<>(TaskDecomposition.class).in(TaskDecomposition::getDocumentDecompositionId, documentDecompositionIds));

        List<DocumentDecompositionVO> tree = TreeUtils.tree(BeanCopyUtils.convertListTo(documentDecompositionList, DocumentDecompositionVO::new));

        List<TaskDecomposition> taskDecompositionList = new ArrayList<>();
        taskDecompositionService.saveBatch(handleData(list,tree,"0", taskDecompositionList));

        return null;
    }


    private List<TaskDecomposition> handleData(List<TaskDecomposition> aldTaskDecompositionList, List<DocumentDecompositionVO> documentDecompositionList,
                                                                           String parentId, List<TaskDecomposition> taskDecompositionList) throws Exception {
        if (CollectionUtil.isNotEmpty(documentDecompositionList)) {

            Map<String, TaskDecomposition> oldTaskDecompositionMap = new HashMap<>();

            if (CollectionUtil.isNotEmpty(aldTaskDecompositionList)){
                oldTaskDecompositionMap = aldTaskDecompositionList.stream().filter(f->StrUtil.isNotBlank(f.getProcessInstances())).collect(Collectors.toMap(TaskDecomposition::getDocumentDecompositionId, Function.identity()));
            }

            for (DocumentDecompositionVO documentDecompositionVO : documentDecompositionList) {

                String id = documentDecompositionVO.getId();
                if (oldTaskDecompositionMap.containsKey(id)){
                    TaskDecomposition taskDecomposition = oldTaskDecompositionMap.get(id);
                    if (CollectionUtil.isNotEmpty(documentDecompositionVO.getChildren())){
                        handleData(aldTaskDecompositionList, documentDecompositionVO.getChildren(), taskDecomposition.getId(),taskDecompositionList);
                    }
                }else {
                    TaskDecomposition taskDecomposition = new TaskDecomposition();
                    taskDecomposition.setId(classRedisHelper.getUUID(TaskDecomposition.class.getSimpleName()));
                    taskDecomposition.setName(documentDecompositionVO.getName());
                    taskDecomposition.setWorkTime(1);
                    taskDecomposition.setParentId(parentId);
                    taskDecomposition.setMainTableId(documentDecompositionVO.getMainTableId());
                    taskDecomposition.setProcessInstances(documentDecompositionVO.getId());
                    taskDecomposition.setDocumentDecompositionId(documentDecompositionVO.getId());
                    taskDecompositionList.add(taskDecomposition);
                    handleData(aldTaskDecompositionList, documentDecompositionVO.getChildren(), taskDecomposition.getId(),taskDecompositionList);
                }
            }
        }
        return taskDecompositionList;
    }


    @Override
    public void  setEveryName(String mainTableId, List<DocumentDecompositionVO> vos)throws Exception {

        List<SimpleVo> list = taskDecompositionService.listForDecompositionId(mainTableId);
        Map<String, String> taskDecompositionMap = list.stream().collect(Collectors.toMap(SimpleVo::getId, SimpleVo::getName, (v1, v2) -> v2));

        Map<String, String> documentDecompositionMap = new HashMap<>();
        List<String> parentIds = vos.stream().map(DocumentDecompositionVO::getParentId).filter(parentId -> ObjectUtil.notEqual(0, parentId)).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(parentIds)){
            List<DocumentDecomposition> documentDecompositions = this.list(new LambdaQueryWrapperX<>(DocumentDecomposition.class)
                    .select(DocumentDecomposition::getId, DocumentDecomposition::getName)
                    .in(DocumentDecomposition::getId, parentIds));
            if (CollectionUtil.isNotEmpty(documentDecompositions)){
                documentDecompositionMap = documentDecompositions.stream().collect(Collectors.toMap(DocumentDecomposition::getId, DocumentDecomposition::getName));
            }
        }
        Map<String, String> finalDocumentDecompositionMap = documentDecompositionMap;
        vos.forEach(vo->{
            if (CollectionUtil.isNotEmpty(taskDecompositionMap)){
                vo.setTaskName(taskDecompositionMap.get(vo.getId()));
            }
            String parentId = vo.getParentId();
            if (StrUtil.isNotBlank(parentId)){
                vo.setParentName(finalDocumentDecompositionMap.get(parentId));
            }
        });

    }

}
