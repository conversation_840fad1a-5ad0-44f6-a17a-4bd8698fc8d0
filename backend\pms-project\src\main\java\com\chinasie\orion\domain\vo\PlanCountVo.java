package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/07/9:41
 * @description:
 */
@Data
public class PlanCountVo implements Serializable {

    @ApiModelProperty(value = "未开始数量")
    private Integer unFinishCount=0;
    @ApiModelProperty(value = "完成数量")
    private Integer finishCount=0;
    @ApiModelProperty(value = "进行中数量")
    private Integer runningCount=0;
}
