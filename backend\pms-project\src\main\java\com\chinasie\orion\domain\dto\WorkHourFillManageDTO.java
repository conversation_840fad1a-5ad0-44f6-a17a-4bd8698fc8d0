package com.chinasie.orion.domain.dto;

import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * WorkHourFillManageDTO Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-15 10:14:51
 */
@ApiModel(value = "WorkHourFillManageDTO对象", description = "工时填报(项目经理)")
@Data
public class WorkHourFillManageDTO extends ObjectDTO implements Serializable{

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @NotBlank(message = "项目id不能为空")
    private String projectId;

    /**
     * 成员id
     */
    @ApiModelProperty(value = "成员id")
    @NotBlank(message = "成员id不能为空")
    private String memberId;

    /**
     * 工时类型
     */
    @ApiModelProperty(value = "工时类型")
    private String workHourType;

    /**
     * 工时
     */
    @ApiModelProperty(value = "工时")
    @NotNull(message = "工时时长能为空")
    @Max(value = 168, message = "工时时长不能超过168小时")
    private Integer workHour;

    /**
     * 项目地点
     */
    @ApiModelProperty(value = "项目地点")
    private String projectPlace;

    /**
     * 关联对象
     */
    @ApiModelProperty(value = "关联对象")
    private String relateObject;


    /**
     * 每天明细
     */
    @ApiModelProperty(value = "每天明细")
    @NotNull(message = "工时填报不能为空")
    @Valid
    private List<WorkHourFillManageDayDTO> dayDetailList;

}
