package com.chinasie.orion.domain.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * ProjectSchemeExecutionLog
 *
 * @author: yangFy
 * @date: 2023/4/18
 * @description:
 * <p>
 * 项目计划执行记录 entity
 * </p>
 */
@Data
@TableName(value = "pms_project_scheme_execution_log")//, code = "exec", type = "common", useExtendsMode = false)
@ApiModel(value = "ProjectSchemeExecutionLog对象", description = "项目计划执行记录")
public class ProjectSchemeExecutionLog extends ObjectEntity implements Serializable {

  @ApiModelProperty(value = "优先级")
  @TableField(value = "sort")
  private String projectSchemeId;
  @ApiModelProperty(value = "优先级")
  @TableField(value = "sort")
  private String content;

}
