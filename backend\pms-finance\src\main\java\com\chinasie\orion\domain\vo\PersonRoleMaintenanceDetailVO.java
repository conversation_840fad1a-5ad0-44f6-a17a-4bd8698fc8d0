package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * PersonRoleMaintenanceDetail VO对象
 *
 * <AUTHOR>
 * @since 2024-10-09 20:19:13
 */
@ApiModel(value = "PersonRoleMaintenanceDetailVO对象", description = "人员角色维护表人员明细")
@Data
public class PersonRoleMaintenanceDetailVO extends  ObjectVO   implements Serializable{

    /**
     * 主表Id
     */
    @ApiModelProperty(value = "主表Id")
    private String mianTableId;


    /**
     * 人员类型
     */
    @ApiModelProperty(value = "人员类型")
    private String personType;


    /**
     * 人员Id
     */
    @ApiModelProperty(value = "人员Id")
    private String personId;




}
