package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.DocumentModelLibraryDirDTO;
import com.chinasie.orion.domain.entity.DocumentModelLibraryDir;
import com.chinasie.orion.domain.vo.DocumentModelLibraryDirVO;
import com.chinasie.orion.mybatis.service.OrionTreeNodeService;
import org.springframework.web.bind.annotation.*;

import io.swagger.annotations.Api;
import com.chinasie.orion.mybatis.controller.OrionTreeNodeController;


/**
 * <p>
 * DocumentModelLibraryDir 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-01 16:35:39
 */
@RestController
@RequestMapping("/documentModelLibraryDir")
@Api(tags = "文档模板库文件夹")
public class DocumentModelLibraryDirController extends OrionTreeNodeController<DocumentModelLibraryDir, DocumentModelLibraryDirDTO, DocumentModelLibraryDirVO> {
    public DocumentModelLibraryDirController(OrionTreeNodeService<DocumentModelLibraryDir, DocumentModelLibraryDirDTO, DocumentModelLibraryDirVO> orionTreeNodeService) {
        super(orionTreeNodeService);
    }

    //  public DocumentModelLibraryDirController(OrionTreeNodeService<DocumentModelLibraryDir, DocumentModelLibraryDirDTO, DocumentModelLibraryDirVO> orionTreeNodeService) {
//        super(orionTreeNodeService);
   // }
}
