<script setup lang="ts">
import {
  BasicButton, BasicForm, OrionTable, useForm,
  SelectUserModal,
  InputSelectUser, openSelectUserModal,
} from 'lyra-component-vue3';
import {
  reactive, Ref, ref, computed, onMounted,
} from 'vue';
import dayjs from 'dayjs';
import { schemeDTOSChangeTableColumns } from '../../tableColumns.js';
const props = withDefaults(defineProps<{
    schemeData:object
}>(), {
  schemeData: () => ({}),
});

const loading: Ref<boolean> = ref(false);

const schemeDTOSTableRef = ref();
const schemeDTOSTableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  showSmallSearch: false,
  pagination: false,
  showTableSetting: false,
  columns: schemeDTOSChangeTableColumns,
});
const projectSchemePrams = reactive({
  projectName: computed(() => props.schemeData.projectName),
  projectId: computed(() => props.schemeData.projectId),
  schemeDTOS: [],

});
onMounted(() => {
  if (props.schemeData.dataSource && Array.isArray(props.schemeData.dataSource)) {
    setTimeout(() => {
      schemeDTOSTableRef.value.setTableData(props.schemeData.dataSource);
    }, 300);
    // schemeDTOSTableRef.value.setTableData(props.schemeData.dataSource);
  }
});

// 生成一个排序算法

const [
  register,
  {
    validate, updateSchema, setFieldsValue, getFieldsValue,
  },
] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas: [
    {
      field: 'projectName',
      component: 'Input',
      label: '选择项目',
      defaultValue: props.schemeData.projectName,
      componentProps: {
        disabled: true,
      },
      rules: [{ required: true }],
    },
    {
      field: 'schemeDTOS',
      slot: 'schemeDTOS',
      component: 'Input',
      label: '项目计划',
      colProps: {
        span: 24,
      },
    },
  ],
});
// 定义一个映射对象，将不同的 props.type 映射到对应的 schemas

defineExpose({
  async getFormData() {
    const formValues = await validate();
    if (formValues) {
      const arr = schemeDTOSTableRef.value.getDataSource();

      const newArr = arr
        .map((obj) => ({
          schemeId: obj.id,
          projectId: obj.projectId,
          schemeName: obj.name,
          oldBeginTime: dayjs(obj.beginTime).format('YYYY-MM-DD'),
          oldEndTime: dayjs(obj.endTime).format('YYYY-MM-DD'),
          newBeginTime: dayjs(obj.newBeginTime).format('YYYY-MM-DD'),
          newEndTime: dayjs(obj.newEndTime).format('YYYY-MM-DD'),
          oldResUser: obj.rspUser,
          newResUser: obj.newRspUser,
          explain: obj.explain,
        }));

      projectSchemePrams.schemeDTOS = newArr;

      return projectSchemePrams;
    }

    return null;
  },
  async setFormData(record, detailData = null) {
    await setFieldsValue({ ...record });
    const arr = detailData.changeList.map((obj) => ({
      ...obj,
      name: obj.schemeName,
      beginTime: obj.oldBeginTime,
      endTime: obj.oldEndTime,
      newBeginTime: dayjs(obj.newBeginTime),
      newEndTime: dayjs(obj.newEndTime),
      rspUserName: obj.oldResUserName,
      rspUser: obj.oldResUser,
      newRspUserName: obj.newResUserName,
      newRspUser: obj.newResUser,
    }));

    schemeDTOSTableRef.value.setTableData(arr);
  },
});
const tableRef = ref();

</script>

<template>
  <div>
    <BasicForm
      @register="register"
    >
      <template #schemeDTOS>
        <div class="min-table">
          <OrionTable
            ref="schemeDTOSTableRef"
            class="min-table"
            :options="schemeDTOSTableOptions"
          />
        </div>
      </template>
    </BasicForm>
  </div>
</template>

<style scoped lang="less">
.min-table{
  overflow: hidden;
  height: 300px;
  :deep(.ant-basic-table){
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
}
</style>
