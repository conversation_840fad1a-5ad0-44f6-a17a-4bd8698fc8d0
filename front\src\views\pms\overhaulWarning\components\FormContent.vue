<script setup lang="ts">
import { BasicForm, FormSchema, useForm } from 'lyra-component-vue3';
import { computed, nextTick, watchEffect } from 'vue';

const props = defineProps<{
  fields: string[];
  roleOptions: any[];
  formValues: Record<string, any> | undefined;
  code: string
}>();

const schemas: FormSchema[] = [
  {
    label: 'code',
    field: 'code',
    component: 'Input',
    defaultValue: props.code,
    show: false,
  },
  {
    label: '设置关注天数',
    field: 'focusDays',
    component: 'InputNumber',
    required: true,
    componentProps: {
      min: 0,
    },
  },
  {
    label: '准备完成率（关注）',
    field: 'focusRate',
    component: 'InputNumber',
    required: true,
    componentProps: {
      min: 0,
      max: 100,
      formatter: (value) => `${value}%`,
      parser: (value) => value.replace('%', ''),
    },
  },
  {
    label: '设置预警天数',
    field: 'warningDays',
    component: 'InputNumber',
    required: true,
    componentProps: {
      min: 0,
    },
  },
  {
    label: '准备完成率（预警）',
    field: 'warningRate',
    component: 'InputNumber',
    required: true,
    componentProps: {
      min: 0,
      max: 100,
      formatter: (value) => `${value}%`,
      parser: (value) => value.replace('%', ''),
    },
  },
  {
    label: '隐患类别',
    field: 'cronList',
    component: 'SelectDictVal',
    rules: [
      {
        required: true,
        type: 'array',
      },
    ],
    componentProps: {
      mode: 'multiple',
      dictNumber: 'pms_pyramid_category',
    },
  },
  {
    label: '预警角色',
    field: 'warningRole',
    component: 'Select',
    rules: [
      {
        required: true,
        type: 'array',
      },
    ],
    componentProps: {
      mode: 'multiple',
      options: computed(() => props.roleOptions),
    },
  },
];

const schemasComp = computed(() => schemas.filter((item) => props.fields.some((field: any) => {
  if (item.field === 'code') return true;
  if (typeof field === 'string') {
    return field === item.field;
  }
  if (item.field === field.value) {
    item.label = field.label;
    return true;
  }
  return false;
})));

const [register, { validate, setFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 6,
  },
  schemas: schemasComp,
});

watchEffect(() => {
  if (props.formValues) {
    nextTick(() => {
      setFieldsValue({
        ...props.formValues,
        warningRole: props?.formValues?.warningRole?.split('、') || [],
        ...(props.code === 'potential_safety_hazard' ? {
          cronList: props?.formValues?.cronList?.filter((item) => item.status === 1).map((item) => item.code),
        } : {}),
      });
    });
  }
});

defineExpose({
  validate,
});
</script>

<template>
  <BasicForm @register="register" />
</template>

<style scoped lang="less">

</style>
