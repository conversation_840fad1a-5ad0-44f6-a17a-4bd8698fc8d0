package com.chinasie.orion.domain.dto;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * RankingVO
 *
 * <AUTHOR>
 * @since 2024-06-18 17:49:18
 */
@ApiModel(value = "OverhaulDetailVO", description = "多基地大修准备及实施状态")
@Data
public class OverhaulDetailDTO extends ObjectVO implements Serializable {

    /**
     * 所属基地名称
     */
    @ApiModelProperty(value = "所属基地名称")
    private String baseName;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private String startDate;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private String endDate;

}
