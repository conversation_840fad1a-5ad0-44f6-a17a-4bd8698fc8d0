package com.chinasie.orion.domain.entity.approval;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.lang.Boolean;
import java.lang.String;

/**
 * ProjectApprovalEstimateTemplateExpenseSubject Entity对象
 *
 * <AUTHOR>
 * @since 2024-04-28 17:21:24
 */
@TableName(value = "pms_project_approval_estimate_template_expense_subject")
@ApiModel(value = "ProjectApprovalEstimateTemplateExpenseSubjectEntity对象", description = "概算模板科目")
@Data
public class ProjectApprovalEstimateTemplateExpenseSubject extends ObjectEntity implements Serializable{

    /**
     * 概算模板id
     */
    @ApiModelProperty(value = "概算模板id")
    @TableField(value = "estimate_template_id")
    private String estimateTemplateId;

    /**
     * 科目id
     */
    @ApiModelProperty(value = "科目id")
    @TableField(value = "expense_subject_id")
    private String expenseSubjectId;

    /**
     * 是否必填
     */
    @ApiModelProperty(value = "是否必填")
    @TableField(value = "required")
    private Boolean required;

    /**
     * 父级id
     */
    @ApiModelProperty(value = "父级id")
    @TableField(value = "parent_id")
    private String parentId;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    @TableField(value = "number")
    private String number;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField(value = "name")
    private String name;

    @ApiModelProperty(value = "公式")
    @TableField(value = "formula")
    private String formula;

    /**
     * 公式名称
     */
    @ApiModelProperty(value = "公式名称")
    @TableField(value = "formula_name")
    private String formulaName;
}
