package com.chinasie.orion.constant;

public enum JobImplDownEnum {
    haveActBeginDateCount("haveActBeginDateCount"," "),
    haveActFinishDateCount("haveActFinishDateCount",""),
    jobCount("jobCount",""),
    finishedCount("finishedCount",""),
    yesterdayCount("yesterdayCount",""),
    todayCount("todayCount",""),
    tomorrowCount("tomorrowCount",""),
    dayAfterTomorrowCount("dayAfterTomorrowCount","");

    private String code;

    private String sqlDesc;

    private JobImplDownEnum(String code, String sqlDesc) {
        this.code = code;
        this.sqlDesc = sqlDesc;
    }

    public String getCode() {
        return code;
    }

    public String getSqlDesc() {
        return sqlDesc;
    }

    public static  Boolean getByCode(String code) {
        for (JobImplDownEnum jobImplDownEnum : JobImplDownEnum.values()) {
            if (jobImplDownEnum.getCode().equals(code)) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }


}
