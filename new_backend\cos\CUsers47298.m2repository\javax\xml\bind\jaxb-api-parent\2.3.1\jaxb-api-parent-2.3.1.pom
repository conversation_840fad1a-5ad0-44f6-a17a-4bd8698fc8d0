<?xml version="1.0"?>
<!--

    DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS HEADER.

    Copyright (c) 1997-2018 Oracle and/or its affiliates. All rights reserved.

    The contents of this file are subject to the terms of either the GNU
    General Public License Version 2 only ("GPL") or the Common Development
    and Distribution License("CDDL") (collectively, the "License").  You
    may not use this file except in compliance with the License.  You can
    obtain a copy of the License at
    https://oss.oracle.com/licenses/CDDL+GPL-1.1
    or LICENSE.txt.  See the License for the specific
    language governing permissions and limitations under the License.

    When distributing the software, include this License Header Notice in each
    file and include the License file at LICENSE.txt.

    GPL Classpath Exception:
    Oracle designates this particular file as subject to the "Classpath"
    exception as provided by Oracle in the GPL Version 2 section of the License
    file that accompanied this code.

    Modifications:
    If applicable, add the following below the License Header, with the fields
    enclosed by brackets [] replaced by your own identifying information:
    "Portions Copyright [year] [name of copyright owner]"

    Contributor(s):
    If you wish your version of this file to be governed by only the CDDL or
    only the GPL Version 2, indicate your decision by adding "[Contributor]
    elects to include this software in this distribution under the [CDDL or GPL
    Version 2] license."  If you don't indicate a single choice of license, a
    recipient has the option to distribute your version of this file under
    either the CDDL, the GPL Version 2 or to extend the choice of license to
    its licensees as provided above.  However, if you add GPL Version 2 code
    and therefore, elected the GPL Version 2 license, then the option applies
    only if the new code is made subject to such option by the copyright
    holder.

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <groupId>javax.xml.bind</groupId>
    <artifactId>jaxb-api-parent</artifactId>
    <version>2.3.1</version>

    <parent>
        <groupId>net.java</groupId>
        <artifactId>jvnet-parent</artifactId>
        <version>5</version>
    </parent>

    <modules>
        <module>jaxb-api</module>
        <module>jaxb-api-test</module>
    </modules>
    <packaging>pom</packaging>

    <name>Java Architecture for XML Binding</name>
    <description>JAXB (JSR 222) API</description>
    <url>https://github.com/javaee/jaxb-spec</url>

    <organization>
        <name>Oracle Corporation</name>
        <url>http://www.oracle.com/</url>
    </organization>

    <scm>
        <connection>scm:git:git://github.com/javaee/jaxb-spec.git</connection>
        <developerConnection>scm:git:**************:javaee/jaxb-spec.git</developerConnection>
        <url>https://github.com/javaee/jaxb-spec.git</url>
        <tag>HEAD</tag>
    </scm>

    <developers>
        <developer>
            <name>Roman Grigoriadi</name>
            <email><EMAIL></email>
            <organization>Oracle Corporation</organization>
        </developer>
        <developer>
            <name>Martin Grebac</name>
            <email><EMAIL></email>
            <organization>Oracle Corporation</organization>
        </developer>
        <developer>
            <name>Iaroslav Savytskyi</name>
            <email><EMAIL></email>
            <organization>Oracle Corporation</organization>
        </developer>
    </developers>

    <licenses>
        <license>
            <name>CDDL 1.1</name>
            <url>https://oss.oracle.com/licenses/CDDL+GPL-1.1</url>
            <distribution>repo</distribution>
        </license>
        <license>
            <name>GPL2 w/ CPE</name>
            <url>https://oss.oracle.com/licenses/CDDL+GPL-1.1</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <issueManagement>
        <system>jira</system>
        <url>https://github.com/javaee/jaxb-spec/issues</url>
    </issueManagement>

    <properties>
        <release.spec.feedback><EMAIL></release.spec.feedback>
        <release.spec.date>Jul 2017</release.spec.date>
        <findbugs.exclude>${project.basedir}/exclude.xml</findbugs.exclude>
        <findbugs.threshold>Low</findbugs.threshold>
        <mrjar.sourceDirectory>${project.basedir}/src/main/mr-jar</mrjar.sourceDirectory>
        <mrjar.build.outputDirectory>${project.build.directory}/classes-mrjar</mrjar.build.outputDirectory>
        <extension.name>javax.xml.bind</extension.name>
        <spec.version>2.3</spec.version>
        <impl.version>0</impl.version>
        <activation.version>1.2.0</activation.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>javax.activation</groupId>
                <artifactId>javax.activation-api</artifactId>
                <version>${activation.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.7.0</version>
                    <configuration>
                        <source>1.7</source>
                        <target>1.7</target>
                        <compilerArgs>
                            <arg>-Xlint:all</arg>
                        </compilerArgs>
                    </configuration>
                </plugin>
                <plugin>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>2.20</version>
                </plugin>
                <plugin>
                    <artifactId>maven-release-plugin</artifactId>
                    <version>2.5.3</version>
                </plugin>
                <plugin>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.0.1</version>
                </plugin>
                <plugin>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>3.0.0-M2</version>
                </plugin>
                <plugin>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.0.1</version>
                </plugin>
                <plugin>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>2.8.2</version>
                </plugin>
                <plugin>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>3.1.1</version>
                </plugin>
                <plugin>
                    <artifactId>maven-gpg-plugin</artifactId>
                    <version>1.6</version>
                </plugin>
            </plugins>
        </pluginManagement>

    </build>

   <profiles>
      <profile>
            <id>jvnet-release</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <configuration>
                            <additionalJOptions>
                                <additionalJOption>-Xdoclint:none</additionalJOption>
                            </additionalJOptions>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-enforcer-plugin</artifactId>
                   </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>
