<template>
  <Layout3
    v-if="tabsOption?.length>0"
    :defaultActionId="actionId"
    :menuData="tabsOption"
    :projectData="projectInfo"
    :type="2"
    @menuChange="contentTabsChange2"
  >
    <template #tabsRight>
      <RemoveBtn />
    </template>
    <!--  概述  -->
    <riskDetailsTabs
      v-if="actionId===77881 && isPower('FX_container_02', powerData)"
      :id="id"
    />
    <!-- 关联内容 -->
    <contactContent
      v-if="actionId===77882 && isPower('FX_container_03', powerData)"
      :id="id"
    />
    <!-- 流程 -->
    <ProcessTab
      v-if="actionId===77883 && isPower('FX_container_04', powerData)"
      :id="id"
    />
  </Layout3>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  provide,
  readonly,
  ref,
  inject,
  onMounted,
  getCurrentInstance,
  computed,
} from 'vue';
// import { Layout2 } from '/@/components/Layout2.0';
import {
  Layout3, isPower, useProjectPower,
} from 'lyra-component-vue3';
import riskDetailsTabs from './riskDetailsTabs/details/index.vue';
import contactContent from './riskDetailsTabs/contactContent/index.vue';
import ProcessTab from './riskDetailsTabs/processTab/index666.vue';
// import ProcessTab from './riskDetailsTabs/processTab/indexCopy.vue';
import { useRoute } from 'vue-router';
import useIndex from '/@/views/pms/projectLaborer/zkhooks/useLocalS.js';
import RemoveBtn from '/@/views/pms/projectLaborer/componentsList/returnBtn/index.vue';
import { riskDetailsApi } from '/@/views/pms/projectLaborer/api/riskManege';
import { setTitleByRootTabsKey } from '/@/utils';

export default defineComponent({
  components: {
    Layout3,
    riskDetailsTabs,
    contactContent,
    ProcessTab,
    RemoveBtn,
  },
  setup() {
    const route = useRoute();
    const state = reactive({
      tabsIndex: Number(route.query.type),
      id: route.query.id,
      projectInfo: {},
      actionId: 77881,
      projectId: route.query.projectId,
      className: '',
      // tabsOption: [
      //   { name: '概述' }, // 0
      //   { name: '关联内容' }, // 1
      //   // { name: '流程' }, // 2
      //   // { name: '日志' }, // 3
      // ],
      powerData: [],

    });
    const internalInstance = getCurrentInstance();
    // 获取权限
    async function getProjectPower() {
      return new Promise((resolve, reject) => {
        useProjectPower(
          { pageCode: 'PMS011' },
          (powerList) => {
            resolve(powerList || []);
          },
          internalInstance,
        );
      });
    }
    const state6 = reactive({
      tabsOption: [],
    });
    // const riskDetailsIndexLocal = useIndex('riskDetailsIndexLocal');
    // const riskContactLocal = useIndex('riskContactLocal');
    /* 获取详情 */
    const getDetail = async () => {
      const love = {
        id: state?.id,
        className: 'RiskManagement',
        moduleName: '项目管理-风险管理-实际风险',
        type: 'GET',
        remark: `打开了【${state?.id}】`,
      };
      await riskDetailsApi(state.id, love)
        .then((res) => {
          if (res) {
            state.projectInfo = { ...res };
            setTitleByRootTabsKey(route?.query?.rootTabsKey, res.name);
          }
        })
        .catch(() => {});
    };
    onMounted(async () => {
      state.powerData = await getProjectPower();
      isPower('FX_container_02', state.powerData) && state6.tabsOption.push({
        name: '概述',
        id: 77881,
      });
      isPower('FX_container_03', state.powerData) && state6.tabsOption.push({
        name: '关联内容',
        id: 77882,
      });
      isPower('FX_container_04', state.powerData) && state6.tabsOption.push({
        name: '流程',
        id: 77883,
      });
      // if (!riskContactLocal.value) {
      //   riskContactLocal.value = 0;
      // }
      //
      // if (riskDetailsIndexLocal.value !== 0) {
      //   state.actionId = riskDetailsIndexLocal.value;
      // }
      await getDetail();
    });
    function contentTabsChange2(index) {
      state.actionId = index.id;
      // riskContactLocal.value = 77881;
      // riskDetailsIndexLocal.value = index;
    }
    const riskItemId = ref(state.id);
    const projectId = ref(state.projectId);
    provide('riskItemId', readonly(riskItemId));
    provide('projectId', readonly(projectId));
    // 权限分发
    provide(
      'powerData',
      computed(() => state.powerData),
    );
    provide(
      'projectInfo',
      computed(() => state.projectInfo),
    );
    function getDetails() {
      return state.projectInfo;
    }
    provide('getForm', getDetail);
    provide('getDetails', getDetails);
    return {
      ...toRefs(state),
      ...toRefs(state6),
      isPower,
      contentTabsChange2,
    };
  },
});
</script>
