package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * MajorUserLike Entity对象
 *
 * <AUTHOR>
 * @since 2024-08-17 16:07:04
 */
@TableName(value = "pmsx_major_user_like")
@ApiModel(value = "MajorUserLikeEntity对象", description = "用户关注的大修")
@Data

public class MajorUserLike extends  ObjectEntity  implements Serializable{

    /**re
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    @TableField(value = "user_id")
    private String userId;

    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    @TableField(value = "repair_round")
    private String repairRound;
    @ApiModelProperty(value = "顺序")
    @TableField(value = "sort")
    private Integer sort;

}
