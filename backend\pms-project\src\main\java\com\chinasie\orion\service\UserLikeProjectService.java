package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.UserLikeProjectDTO;
import com.chinasie.orion.domain.entity.UserLikeProject;
import com.chinasie.orion.domain.vo.UserLikeProjectVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;


import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2023/04/09/16:53
 * @description:
 */
public interface UserLikeProjectService extends OrionBaseService<UserLikeProject> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    UserLikeProjectVO detail(String id) throws Exception;

    /**
     * 新增
     * <p>
     * * @param userLikeProjectDTO
     */
    UserLikeProjectVO create(UserLikeProjectDTO userLikeProjectDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param userLikeProjectDTO
     */
    Boolean edit(UserLikeProjectDTO userLikeProjectDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    Map<String, UserLikeProjectVO> queryListByProjectIdListAndUserId(List<String> projectIdList, String userId) throws Exception;

    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<UserLikeProjectVO> pages(Page<UserLikeProjectDTO> pageRequest) throws Exception;

}
