package com.chinasie.orion.controller;

import com.chinasie.orion.constant.MarketContractMilestoneStatusEnum;
import com.chinasie.orion.domain.dto.ContractMilestoneDTO;
import com.chinasie.orion.domain.dto.ContractMilestoneTreeDTO;
import com.chinasie.orion.domain.dto.MarketContractDTO;
import com.chinasie.orion.domain.entity.ContractMilestone;
import com.chinasie.orion.domain.entity.ContractOurSignedSubject;
import com.chinasie.orion.domain.tree.MileStoneTreeVo;
import com.chinasie.orion.domain.vo.ContractMilestoneTreeTotalVO;
import com.chinasie.orion.domain.vo.ContractMilestoneVO;
import com.chinasie.orion.domain.vo.NodeVO;
import com.chinasie.orion.domain.vo.TreeNodeVO;
import com.chinasie.orion.domain.vo.tree.RelationOrgJobImplVO;
import com.chinasie.orion.domain.vo.tree.SearchVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.management.domain.entity.CustomerInfo;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.org.SimpleDeptVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ContractMilestoneService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * ContractMilestone 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-29 15:01:52
 */
@RestController
@RequestMapping("/contractMilestone")
@Api(tags = "合同里程碑")
@RequiredArgsConstructor
public class ContractMilestoneController {

    private final ContractMilestoneService contractMilestoneService;


    /**
     * 详情
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据【{{#id}}】", type = "合同里程碑", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ContractMilestoneVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        ContractMilestoneVO rsp = contractMilestoneService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#contractMilestoneDTO.name}}】", type = "合同里程碑", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody @Validated ContractMilestoneDTO contractMilestoneDTO) {
        String rsp = contractMilestoneService.create(contractMilestoneDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "查询附件")
    @RequestMapping(value = "/file/{dataId}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询市场合同附件【{{#dataId}}】", type = "MarketContract", subType = "查询附件", bizNo = "{{#dataId}}")
    public ResponseDTO<List<Map<String, Object>>> queryFile(@PathVariable(value = "dataId") String dataId) throws Exception {
        List<Map<String, Object>> rsp = contractMilestoneService.queryFile(dataId);

        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据", type = "合同里程碑", subType = "编辑", bizNo = "")
    public ResponseDTO<Boolean> edit(@RequestBody ContractMilestoneDTO contractMilestoneDTO) throws Exception {
        Boolean rsp = contractMilestoneService.edit(contractMilestoneDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "合同里程碑", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<?> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = contractMilestoneService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 关闭里程碑
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "关闭里程碑")
    @RequestMapping(value = "close/{id}", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】关闭了里程碑", type = "合同里程碑", subType = "关闭", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> close(@PathVariable("id") String id) throws Exception {
        Boolean rsp = contractMilestoneService.close(id);
        return new ResponseDTO(rsp);
    }

    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "合同里程碑", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = contractMilestoneService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "合同里程碑", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ContractMilestoneVO>> pages(@RequestBody Page<ContractMilestoneDTO> pageRequest) throws Exception {
        Page<ContractMilestoneVO> rsp = contractMilestoneService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 关联里程碑分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "关联里程碑分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "合同里程碑", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/relation/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ContractMilestoneVO>> relationPages(@RequestBody Page<ContractMilestoneDTO> pageRequest) throws Exception {
        Page<ContractMilestoneVO> rsp = contractMilestoneService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "树形")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "合同里程碑", subType = "树形查询", bizNo = "")
    @RequestMapping(value = "/tree", method = RequestMethod.POST)
    public ResponseDTO<ContractMilestoneTreeTotalVO> tree(@RequestBody ContractMilestoneTreeDTO contractMilestoneTreeDTO) throws Exception {
        ContractMilestoneTreeTotalVO rsp = contractMilestoneService.tree(contractMilestoneTreeDTO);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("合同里程碑导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "合同里程碑", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        contractMilestoneService.downloadExcelTpl(response);
    }

    @ApiOperation("合同里程碑导入校验（Excel）")
    @PostMapping(value = "/import/excel/check/{contractId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "合同里程碑", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(
            @RequestPart("file") MultipartFile file, @PathVariable("contractId") String contractId) throws Exception {
        ImportExcelCheckResultVO rsp = contractMilestoneService.importCheckByExcel(file, contractId);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("合同里程碑导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "合同里程碑", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = contractMilestoneService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消合同里程碑导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "合同里程碑", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = contractMilestoneService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("合同里程碑导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "合同里程碑", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        contractMilestoneService.exportByExcel(searchConditions, response);
    }


    @ApiOperation(value = "合同内里程碑树形报表导出")
    @PostMapping(value = "/export/excelData",produces = "application/octet-stream")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出了合同内里程碑数据", type = "合同里程碑", subType = "合同里程碑数据导出", bizNo = "")
    public void exportExcelDatatree(@RequestBody ContractMilestoneTreeDTO contractMilestoneTreeDTO,HttpServletResponse response) throws Exception {
       contractMilestoneService.exportExcelDatatree(contractMilestoneTreeDTO,response);

    }

    @ApiOperation(value = "获取合同的里程碑树")
    @RequestMapping(value = "/impl/tree", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】获取了合同里程碑树", type = "合同里程碑", subType = "获取合同里程碑树", bizNo = "")
    public ResponseDTO<List<TreeNodeVO<NodeVO<MileStoneTreeVo>>>> implTree(@RequestBody MileStoneTreeVo searchVO) throws Exception {
        List<TreeNodeVO<NodeVO<MileStoneTreeVo>>> treeNodeVO = contractMilestoneService.queryTree(searchVO);
        return new ResponseDTO<>(treeNodeVO);
    }

    @ApiOperation(value = "预估里程碑审批")
    @RequestMapping(value = "/approval", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】预估里程碑审批", type = "合同里程碑", subType = "预估里程碑审批", bizNo = "")
    public ResponseDTO<String> approval(@RequestBody List<ContractMilestoneDTO> milestoneDTOS) throws Exception {
        String rsp = contractMilestoneService.approval(milestoneDTOS);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "获取子订单的里程碑树")
    @RequestMapping(value = "/order/tree", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】获取了子订单的里程碑树", type = "合同里程碑", subType = "获取子订单的里程碑树", bizNo = "")
    public ResponseDTO<List<TreeNodeVO<NodeVO<MileStoneTreeVo>>>> orderTree(@RequestBody MileStoneTreeVo searchVO) throws Exception {
        List<TreeNodeVO<NodeVO<MileStoneTreeVo>>> treeNodeVO = contractMilestoneService.queryOrderTree(searchVO);
        return new ResponseDTO<>(treeNodeVO);
    }

    @ApiOperation(value = "跟踪确认")
    @RequestMapping(value = "/trackConfirmation", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】跟踪确认", type = "合同里程碑", subType = "跟踪确认", bizNo = "")
    public ResponseDTO<String> trackConfirmation(@RequestBody ContractMilestoneDTO contractMilestoneDTO) throws Exception {
        String rsp = contractMilestoneService.trackConfirmation(contractMilestoneDTO);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "里程碑管理列表")
    @RequestMapping(value = "/milestoneManage", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】获取了里程碑管理列表", type = "合同里程碑", subType = "获取里程碑管理列表", bizNo = "")
    public ResponseDTO<List<TreeNodeVO<NodeVO<MileStoneTreeVo>>>> milestoneManage(@RequestBody MileStoneTreeVo searchVO) throws Exception {
        List<TreeNodeVO<NodeVO<MileStoneTreeVo>>> treeNodeVO = contractMilestoneService.milestoneManage(searchVO);
        return new ResponseDTO<>(treeNodeVO);
    }

    @ApiOperation(value = "获取合同下的甲方签约主体")
    @RequestMapping(value = "/getOurSignByContractId/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】获取了合同下的甲方签约主体", type = "合同里程碑", subType = "获取合同下的甲方签约主体", bizNo = "")
    public ResponseDTO<List<CustomerInfo>> getOurSignByContractId(@PathVariable(value = "id") String id) throws Exception {
        List<CustomerInfo> treeNodeVO = contractMilestoneService.getOurSignByContractId(id);
        return new ResponseDTO<>(treeNodeVO);
    }

    @ApiOperation(value = "新建一个空的里程碑")
    @RequestMapping(value = "/creatNew", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】新建了空的里程碑", type = "合同里程碑", subType = "新建空的里程碑", bizNo = "")
    public ResponseDTO<ContractMilestone> createNew(@RequestBody MileStoneTreeVo mileStoneTreeVo) throws Exception {
        ContractMilestone contractMilestone = contractMilestoneService.createNew(mileStoneTreeVo);
        return new ResponseDTO<>(contractMilestone);
    }

    @ApiOperation(value = "复制")
    @RequestMapping(value = "/copy", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】复制了里程碑", type = "合同里程碑", subType = "复制里程碑", bizNo = "")
    public ResponseDTO<ContractMilestone> copy(@RequestBody MileStoneTreeVo mileStoneTreeVo) throws Exception {
        ContractMilestone contractMilestone = contractMilestoneService.copy(mileStoneTreeVo);
        return new ResponseDTO<>(contractMilestone);
    }

    @ApiOperation(value = "获取当前合同的所有的所级")
    @RequestMapping(value = "/getOfficeDept", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】获取了当前合同的所有的所级", type = "合同里程碑", subType = "获取当前合同的所有的所级", bizNo = "")
    public ResponseDTO<List<SimpleDeptVO>> getOfficeDept(@RequestBody MileStoneTreeVo mileStoneTreeVo) throws Exception {
        List<SimpleDeptVO> rsp = contractMilestoneService.getOfficeDept(mileStoneTreeVo);
        return new ResponseDTO<>(rsp);

    }

    @ApiOperation(value = "商城子订单新建一个空的里程碑")
    @RequestMapping(value = "/creatNew/projectOrder", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】商城子订单新建了空的里程碑", type = "合同里程碑", subType = "商城子订单新建空的里程碑", bizNo = "")
    public ResponseDTO<ContractMilestone> createNewProjectOrder(@RequestBody MileStoneTreeVo mileStoneTreeVo) throws Exception {
        ContractMilestone contractMilestone = contractMilestoneService.createNewProjectOrder(mileStoneTreeVo);
        return new ResponseDTO<>(contractMilestone);
    }

    @ApiOperation(value = "框架子订单新建一个空的里程碑")
    @RequestMapping(value = "/creatNew/frame", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】框架子订单新建了空的里程碑", type = "合同里程碑", subType = "框架子订单新建空的里程碑", bizNo = "")
    public ResponseDTO<ContractMilestone> createNewFrame(@RequestBody MileStoneTreeVo mileStoneTreeVo) throws Exception {
        ContractMilestone contractMilestone = contractMilestoneService.createNewFrame(mileStoneTreeVo);
        return new ResponseDTO<>(contractMilestone);
    }

    @ApiOperation(value = "获取里程碑金额")
    @RequestMapping(value = "/getContractMilestoneMoney/{contractId}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】获取了里程碑金额", type = "合同里程碑", subType = "获取里程碑金额", bizNo = "")
    public ResponseDTO<Boolean> getContractMilestoneMoney(@PathVariable(value = "contractId") String contractId) throws Exception {
        Boolean rsp = contractMilestoneService.getContractMilestoneMoney(contractId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "校验里程碑金额是否等于对应子里程碑金额之和")
    @RequestMapping(value = "/checkMilestoneAmt/{contractId}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】校验了里程碑金额是否等于对应之子程碑金额之和", type = "合同里程碑", subType = "校验里程碑金额是否等于对应之子程碑金额之和", bizNo = "")
    public ResponseDTO<Boolean> checkMilestoneAmt(@PathVariable(value = "contractId") String contractId) throws Exception {
        Boolean rsp = contractMilestoneService.checkMilestoneAmt(contractId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "校验里程碑重名")
    @RequestMapping(value = "/checkRepeatName/{contractId}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】校验了里程碑重名", type = "合同里程碑", subType = "校验里程碑重名", bizNo = "")
    public ResponseDTO<Boolean> checkRepeatName(@PathVariable(value = "contractId") String contractId) throws Exception {
        Boolean rsp = contractMilestoneService.checkRepeatName(contractId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "根据里程碑id校验里程碑重名")
    @RequestMapping(value = "/checkRepeatNameByContractMilestoneId/{mileStoneId}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】根据里程碑id校验里程碑重名", type = "合同里程碑", subType = "根据里程碑id校验里程碑重名", bizNo = "")
    public ResponseDTO<Boolean> checkRepeatNameByContractMilestoneId(@PathVariable(value = "mileStoneId") String mileStoneId) throws Exception {
        Boolean rsp = contractMilestoneService.checkRepeatNameByContractMilestoneId(mileStoneId);
        return new ResponseDTO<>(rsp);
    }
}
