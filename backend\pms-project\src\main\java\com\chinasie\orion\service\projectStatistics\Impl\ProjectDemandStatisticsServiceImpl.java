package com.chinasie.orion.service.projectStatistics.Impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.constant.DictConstant;
import com.chinasie.orion.domain.dto.projectStatistics.ProjectDemandStatisticsDTO;
import com.chinasie.orion.domain.entity.DemandManagement;
import com.chinasie.orion.domain.entity.DemandManagement;
import com.chinasie.orion.domain.entity.projectStatistics.DemandStatusStatistics;
import com.chinasie.orion.domain.vo.DemandManagementVO;
import com.chinasie.orion.domain.vo.SimpleVo;
import com.chinasie.orion.domain.vo.projectStatistics.ProjectDemandStatisticsVO;
import com.chinasie.orion.feign.PasFeignService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.DemandManagementRepository;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.DemandManagementService;
import com.chinasie.orion.service.projectStatistics.DemandStatusStatisticsService;
import com.chinasie.orion.service.projectStatistics.ProjectDemandStatisticsService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collector;
import java.util.stream.Collectors;


@Service
public class ProjectDemandStatisticsServiceImpl implements ProjectDemandStatisticsService {

    @Autowired
    private DemandManagementService demandManagementService;

    @Autowired
    private DemandStatusStatisticsService demandStatusStatisticsService;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Resource
    private DemandManagementRepository demandManagementRepository;

    @Resource
    private DictBo dictBo;

    @Resource
    private PasFeignService pasFeignService;

    @Override
    public ProjectDemandStatisticsVO getProjectDemandStatusStatistics(ProjectDemandStatisticsDTO projectDemandStatisticsDTO) {
        ProjectDemandStatisticsVO projectDemandStatisticsVO = new ProjectDemandStatisticsVO();
        LambdaQueryWrapperX<DemandManagement> projectDemandLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        projectDemandLambdaQueryWrapperX.select("status,count(id) as count");
        projectDemandLambdaQueryWrapperX.eq(DemandManagement::getProjectId, projectDemandStatisticsDTO.getProjectId());
        projectDemandLambdaQueryWrapperX.eqIfPresent(DemandManagement::getType, projectDemandStatisticsDTO.getDemandType());
        projectDemandLambdaQueryWrapperX.groupBy(DemandManagement::getStatus);
        List<Map<String, Object>> list = demandManagementService.listMaps(projectDemandLambdaQueryWrapperX);
        for (Map<String, Object> map : list) {
            if ("101".equals(map.get("status").toString())) {
                projectDemandStatisticsVO.setNoStartCount(Integer.parseInt(map.get("count").toString()));
            }
            if ("110".equals(map.get("status").toString())) {
                projectDemandStatisticsVO.setUnderwayCount(Integer.parseInt(map.get("count").toString()));
            }
            if ("130".equals(map.get("status").toString())) {
                projectDemandStatisticsVO.setCompleteCount(Integer.parseInt(map.get("count").toString()));
            }
        }
        return projectDemandStatisticsVO;
    }

    @Override
    public List<ProjectDemandStatisticsVO> getProjectDemandRspUserStatistics(ProjectDemandStatisticsDTO projectDemandStatisticsDTO) {
        List<ProjectDemandStatisticsVO> projectDemandStatisticsVOs = new ArrayList<>();
        LambdaQueryWrapperX<DemandManagement> projectDemandLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        projectDemandLambdaQueryWrapperX.select("principal_id as rspUser,IFNULL( sum( CASE  WHEN `status`=101 THEN 1 ELSE 0 END ), 0 ) noStartCount ," +
                "IFNULL( sum( CASE  WHEN `status`=110 THEN 1 ELSE 0 END ), 0 ) as underwayCount," +
                "IFNULL( sum( CASE  WHEN `status`=130 THEN 1 ELSE 0 END ), 0 ) as completeCount");
        projectDemandLambdaQueryWrapperX.eq(DemandManagement::getProjectId, projectDemandStatisticsDTO.getProjectId());
        projectDemandLambdaQueryWrapperX.eqIfPresent(DemandManagement::getType, projectDemandStatisticsDTO.getDemandType());
        projectDemandLambdaQueryWrapperX.groupBy(DemandManagement::getPrincipalId);
        List<Map<String, Object>> list = demandManagementService.listMaps(projectDemandLambdaQueryWrapperX);
        for (Map<String, Object> map : list) {
            if (ObjectUtil.isEmpty(map.get("rspUser"))) {
                continue;
            }
            ProjectDemandStatisticsVO projectDemandStatisticsVO = new ProjectDemandStatisticsVO();
            projectDemandStatisticsVO.setRspuser(map.get("rspUser").toString());
            UserVO userVO = userRedisHelper.getUserById(map.get("rspUser").toString());
            if (ObjectUtil.isNotEmpty(userVO)) {
                projectDemandStatisticsVO.setRspuserName(userVO.getName());
            }
            projectDemandStatisticsVO.setNoStartCount(Integer.parseInt(map.get("noStartCount").toString()));
            projectDemandStatisticsVO.setCompleteCount(Integer.parseInt(map.get("completeCount").toString()));
            projectDemandStatisticsVO.setUnderwayCount(Integer.parseInt(map.get("underwayCount").toString()));
            projectDemandStatisticsVOs.add(projectDemandStatisticsVO);
        }
        return projectDemandStatisticsVOs;
    }


    @Override
    public List<ProjectDemandStatisticsVO> getProjectDemandChangeStatusStatistics(ProjectDemandStatisticsDTO projectDemandStatisticsDTO) {
        List<ProjectDemandStatisticsVO> projectDemandStatisticsVOs = new ArrayList<>();
        LambdaQueryWrapperX<DemandStatusStatistics> projectDemandLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        String sql = "";
        Map<String, String> timeMap = new HashMap<>();
        Map<String, Date> timeValue = new HashMap<>();
        for (int i = 0; i < 10; i++) {
            Calendar calendar = Calendar.getInstance();
            Date startDate = new Date();
            Date endDate = new Date();
            if (projectDemandStatisticsDTO.getTimeType().equals("DAY")) {
                calendar.add(Calendar.DAY_OF_YEAR, -i);
                startDate = DateUtil.beginOfDay(calendar.getTime());
                endDate = DateUtil.endOfDay(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1) +"-"+ calendar.get(Calendar.DAY_OF_MONTH));
            }
            if (projectDemandStatisticsDTO.getTimeType().equals("MONTH")) {
                calendar.add(Calendar.MONTH, -i);
                startDate = DateUtil.beginOfMonth(calendar.getTime());
                endDate = DateUtil.endOfMonth(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1));
            }
            if (projectDemandStatisticsDTO.getTimeType().equals("QUARTER")) {
                calendar.add(Calendar.MONTH, -3 * i);
                startDate = DateUtil.beginOfQuarter(calendar.getTime());
                endDate = DateUtil.endOfQuarter(calendar.getTime());
                int quarter = (calendar.get(Calendar.MONTH)+1) < 3 ? 1 : (int)Math.ceil((double)(calendar.get(Calendar.MONTH)+1) / 3);
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "年第" + quarter + "季度");
            }
            if (projectDemandStatisticsDTO.getTimeType().equals("WEEK")) {
                calendar.add(Calendar.WEEK_OF_YEAR, -i);
                startDate = DateUtil.beginOfWeek(calendar.getTime());
                endDate = DateUtil.endOfWeek(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1) + "-第" + calendar.get(Calendar.WEEK_OF_MONTH) + "周");
            }
            if (projectDemandStatisticsDTO.getTimeType().equals("YEAR")) {
                calendar.add(Calendar.YEAR, -i);
                startDate = DateUtil.beginOfYear(calendar.getTime());
                endDate = DateUtil.endOfYear(calendar.getTime());
                timeMap.put("time" + i, String.valueOf(calendar.get(Calendar.YEAR)));
            }
            timeValue.put("time" + i, startDate);
            if(i==0){
                endDate=new Date();
            }
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
            sql = sql + "IFNULL(sum(CASE  WHEN  DATE_FORMAT(now_day, '%Y-%m-%d') = '"+sdf.format(endDate) +"' THEN no_start_count ELSE 0 END ), 0 ) as noStartCountTime" + i + ","+
                    "IFNULL(sum(CASE  WHEN   DATE_FORMAT(now_day, '%Y-%m-%d') = '"+sdf.format(endDate) +"' THEN underway_count ELSE 0 END ), 0 ) as  underwayCountTime" + i + ","+
                    "IFNULL(sum(CASE  WHEN  DATE_FORMAT(now_day, '%Y-%m-%d') = '"+sdf.format(endDate) +"' THEN complete_count ELSE 0 END ), 0 ) as completeCountTime" + i ;
            if(i<9){
                sql=sql+"," ;
            }
        }
        projectDemandLambdaQueryWrapperX.select(sql);
        projectDemandLambdaQueryWrapperX.eq(DemandStatusStatistics::getProjectId, projectDemandStatisticsDTO.getProjectId());
        projectDemandLambdaQueryWrapperX.eqIfPresent(DemandStatusStatistics::getTypeId, projectDemandStatisticsDTO.getDemandType());
        List<Map<String, Object>> list = demandStatusStatisticsService.listMaps(projectDemandLambdaQueryWrapperX);
        Map<String, Object> map = list.get(0);
        for (int i = 0; i < 10; i++) {
            ProjectDemandStatisticsVO projectDemandStatisticsVO = new ProjectDemandStatisticsVO();
            projectDemandStatisticsVO.setNoStartCount(Integer.parseInt(map.get("noStartCountTime" + i).toString()));
            projectDemandStatisticsVO.setUnderwayCount(Integer.parseInt(map.get("underwayCountTime" + i).toString()));
            projectDemandStatisticsVO.setCompleteCount(Integer.parseInt(map.get("completeCountTime" + i).toString()));
            projectDemandStatisticsVO.setTimeValue(timeValue.get("time" + i));
            projectDemandStatisticsVO.setShowTime(timeMap.get("time" + i));
            projectDemandStatisticsVOs.add(projectDemandStatisticsVO);
        }
        Collections.reverse(projectDemandStatisticsVOs);
        return projectDemandStatisticsVOs;
    }

    @Override
    public List<ProjectDemandStatisticsVO> getProjectDemandCreateStatistics(ProjectDemandStatisticsDTO projectDemandStatisticsDTO) {
        List<ProjectDemandStatisticsVO> projectDemandStatisticsVOs = new ArrayList<>();
        LambdaQueryWrapperX<DemandManagement> projectDemandLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        String sql = "";
        Map<String, String> timeMap = new HashMap<>();
        Map<String, Date> timeValue = new HashMap<>();
        for (int i = 0; i < 10; i++) {
            Calendar calendar = Calendar.getInstance();
            Date startDate = new Date();
            Date endDate = new Date();
            if (projectDemandStatisticsDTO.getTimeType().equals("DAY")) {
                calendar.add(Calendar.DAY_OF_YEAR, -i);
                startDate = DateUtil.beginOfDay(calendar.getTime());
                endDate = DateUtil.endOfDay(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1) +"-"+ calendar.get(Calendar.DAY_OF_MONTH));
            }
            if (projectDemandStatisticsDTO.getTimeType().equals("MONTH")) {
                calendar.add(Calendar.MONTH, -i);
                startDate = DateUtil.beginOfMonth(calendar.getTime());
                endDate = DateUtil.endOfMonth(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1));
            }
            if (projectDemandStatisticsDTO.getTimeType().equals("QUARTER")) {
                calendar.add(Calendar.MONTH, -3 * i);
                startDate = DateUtil.beginOfQuarter(calendar.getTime());
                endDate = DateUtil.endOfQuarter(calendar.getTime());
                int quarter = (calendar.get(Calendar.MONTH)+1) < 3 ? 1 : (int)Math.ceil((double)(calendar.get(Calendar.MONTH)+1) / 3);
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "年第" + quarter + "季度");
            }
            if (projectDemandStatisticsDTO.getTimeType().equals("WEEK")) {
                calendar.add(Calendar.WEEK_OF_YEAR, -i);
                startDate = DateUtil.beginOfWeek(calendar.getTime());
                endDate = DateUtil.endOfWeek(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1) + "-第" + calendar.get(Calendar.WEEK_OF_MONTH) + "周");
            }
            if (projectDemandStatisticsDTO.getTimeType().equals("YEAR")) {
                calendar.add(Calendar.YEAR, -i);
                startDate = DateUtil.beginOfYear(calendar.getTime());
                endDate = DateUtil.endOfYear(calendar.getTime());
                timeMap.put("time" + i, String.valueOf(calendar.get(Calendar.YEAR)));
            }
            timeValue.put("time" + i, startDate);
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            sql = sql + "IFNULL(sum(CASE  WHEN `create_time`>= '" + sdf.format(startDate) + "'  and  `create_time` <= '" + sdf.format(endDate) + "' THEN 1 ELSE 0 END ), 0 ) as time" + i;
            if(i<9){
                sql=sql+"," ;
            }
        }
        projectDemandLambdaQueryWrapperX.select(sql);
        projectDemandLambdaQueryWrapperX.eq(DemandManagement::getProjectId, projectDemandStatisticsDTO.getProjectId());
        projectDemandLambdaQueryWrapperX.eqIfPresent(DemandManagement::getType, projectDemandStatisticsDTO.getDemandType());
        List<Map<String, Object>> list = demandManagementService.listMaps(projectDemandLambdaQueryWrapperX);
        Map<String, Object> map = list.get(0);
        for (int i = 0; i < 10; i++) {
            ProjectDemandStatisticsVO projectDemandStatisticsVO = new ProjectDemandStatisticsVO();
            projectDemandStatisticsVO.setNoStartCount(Integer.parseInt(map.get("time" + i).toString()));
            projectDemandStatisticsVO.setTimeValue(timeValue.get("time" + i));
            projectDemandStatisticsVO.setShowTime(timeMap.get("time" + i));
            projectDemandStatisticsVOs.add(projectDemandStatisticsVO);
        }
        Collections.reverse(projectDemandStatisticsVOs);
        return projectDemandStatisticsVOs;
    }

    @Override
    public Page<DemandManagementVO> getProjectDemandPages(Page<ProjectDemandStatisticsDTO> pageRequest) throws Exception {
        Page<DemandManagement> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), DemandManagement::new));
        LambdaQueryWrapperX<DemandManagement> objectLambdaQueryWrapperX = new LambdaQueryWrapperX<>(DemandManagement.class);
        if (ObjectUtil.isNotEmpty(pageRequest.getQuery())) {
            ProjectDemandStatisticsDTO projectDemandStatisticsDTO = pageRequest.getQuery();
            objectLambdaQueryWrapperX.eqIfPresent(DemandManagement::getStatus, projectDemandStatisticsDTO.getStatus());
            objectLambdaQueryWrapperX.eqIfPresent(DemandManagement::getType, projectDemandStatisticsDTO.getDemandType());
            objectLambdaQueryWrapperX.eq(DemandManagement::getProjectId, projectDemandStatisticsDTO.getProjectId());
            objectLambdaQueryWrapperX.eqIfPresent(DemandManagement::getPrincipalId, projectDemandStatisticsDTO.getRspUser());
            if (ObjectUtil.isNotEmpty(projectDemandStatisticsDTO.getCreateTime())) {
                if (projectDemandStatisticsDTO.getTimeType().equals("DAY")) {
                    objectLambdaQueryWrapperX.between(DemandManagement::getCreateTime,DateUtil.beginOfDay(projectDemandStatisticsDTO.getCreateTime()),DateUtil.endOfDay(projectDemandStatisticsDTO.getCreateTime()));
                }
                if (projectDemandStatisticsDTO.getTimeType().equals("WEEK")) {
                    objectLambdaQueryWrapperX.between(DemandManagement::getCreateTime,DateUtil.beginOfWeek(projectDemandStatisticsDTO.getCreateTime()),DateUtil.endOfWeek(projectDemandStatisticsDTO.getCreateTime()));
                }
                if (projectDemandStatisticsDTO.getTimeType().equals("QUARTER")) {
                    objectLambdaQueryWrapperX.between(DemandManagement::getCreateTime,DateUtil.beginOfQuarter(projectDemandStatisticsDTO.getCreateTime()),DateUtil.endOfQuarter(projectDemandStatisticsDTO.getCreateTime()));
                }
                if (projectDemandStatisticsDTO.getTimeType().equals("MONTH")) {
                    objectLambdaQueryWrapperX.between(DemandManagement::getCreateTime,DateUtil.beginOfMonth(projectDemandStatisticsDTO.getCreateTime()),DateUtil.endOfMonth(projectDemandStatisticsDTO.getCreateTime()));
                }
                if (projectDemandStatisticsDTO.getTimeType().equals("YEAR")) {
                    objectLambdaQueryWrapperX.between(DemandManagement::getCreateTime,DateUtil.beginOfYear(projectDemandStatisticsDTO.getCreateTime()),DateUtil.endOfYear(projectDemandStatisticsDTO.getCreateTime()));
                }
            }
        }
        PageResult<DemandManagement> page = demandManagementRepository.selectPage(realPageRequest, objectLambdaQueryWrapperX);
        Page<DemandManagementVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<DemandManagementVO> demandVOS = BeanCopyUtils.convertListTo(page.getContent(), DemandManagementVO::new);
        if (!CollectionUtil.isNotEmpty(demandVOS)) {
            return pageResult;
        }
        Map<String, String> priorityLevelValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.PRIORITY_LEVEL);

       List<String> demandIds=demandVOS.stream().map(DemandManagementVO::getType).collect(Collectors.toList());
        List<SimpleVo> simpleVos=pasFeignService.getDemandTypeByIds(demandIds).getResult();
        Map<String, String> simpleVoMap= simpleVos.stream().collect(Collectors.toMap(SimpleVo::getId, SimpleVo::getName));

        demandVOS.forEach(d -> {
            d.setPriorityLevelName(priorityLevelValueToDesMap.get(d.getPriorityLevel()));
            d.setScheduleName(ObjectUtil.isNull(d.getSchedule()) ? "0%" : d.getSchedule() + "%");
            d.setTypeName(simpleVoMap.get(d.getType()));
        });
        pageResult.setContent(demandVOS);
        return pageResult;
    }
}
