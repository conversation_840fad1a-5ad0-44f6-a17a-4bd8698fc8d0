package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "PlaneCostStatistics对象", description = "机票费用统计")
@Data
public class PlaneCostStatistics {

    /**
     * 季度
     */
    @ApiModelProperty(value = "季度")
    @TableField(value = "dataQuarter")
    private Integer dataQuarter;

    /**
     * 中心编号
     */
    @ApiModelProperty(value = "中心编号")
    @TableField(value = "org_code")
    private String orgCode;

    /**
     * 中心名称
     */
    @ApiModelProperty(value = "中心名称")
    @TableField(value = "org_name")
    private String orgName;


    /**
     * 折扣机票金额
     */
    @ApiModelProperty(value = "折扣机票金额")
    @TableField(value = "discount_price")
    private BigDecimal discountPrice;

}
