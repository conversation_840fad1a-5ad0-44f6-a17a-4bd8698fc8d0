package com.chinasie.orion.service.impl;


import com.chinasie.orion.domain.dto.ProjectPayPromiseDTO;
import com.chinasie.orion.domain.dto.ProjectPayShopPurchaseDTO;
import com.chinasie.orion.domain.entity.ProjectPayPromise;
import com.chinasie.orion.domain.entity.ProjectPayShopPurchase;
import com.chinasie.orion.domain.vo.ProjectPayShopPurchaseVO;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ProjectPayShopPurchaseMapper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectPayShopPurchaseService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * ProjectPayShopPurchase 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04 00:48:25
 */
@Service
@Slf4j
public class ProjectPayShopPurchaseServiceImpl extends  OrionBaseServiceImpl<ProjectPayShopPurchaseMapper, ProjectPayShopPurchase>   implements ProjectPayShopPurchaseService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProjectPayShopPurchaseVO detail(String id, String pageCode) throws Exception {
        ProjectPayShopPurchase projectPayShopPurchase =this.getById(id);
        ProjectPayShopPurchaseVO result = BeanCopyUtils.convertTo(projectPayShopPurchase,ProjectPayShopPurchaseVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param projectPayShopPurchaseDTO
     */
    @Override
    public  String create(ProjectPayShopPurchaseDTO projectPayShopPurchaseDTO) throws Exception {
        ProjectPayShopPurchase projectPayShopPurchase =BeanCopyUtils.convertTo(projectPayShopPurchaseDTO,ProjectPayShopPurchase::new);
        this.save(projectPayShopPurchase);

        String rsp=projectPayShopPurchase.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param projectPayShopPurchaseDTO
     */
    @Override
    public Boolean edit(ProjectPayShopPurchaseDTO projectPayShopPurchaseDTO) throws Exception {
        ProjectPayShopPurchase projectPayShopPurchase =BeanCopyUtils.convertTo(projectPayShopPurchaseDTO,ProjectPayShopPurchase::new);

        this.updateById(projectPayShopPurchase);

        String rsp=projectPayShopPurchase.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectPayShopPurchaseVO> pages( Page<ProjectPayShopPurchaseDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProjectPayShopPurchase> condition = new LambdaQueryWrapperX<>( ProjectPayShopPurchase. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ProjectPayShopPurchase::getCreateTime);
        ProjectPayShopPurchaseDTO projectPayActualDTO =  pageRequest.getQuery();
        if(!Objects.isNull(projectPayActualDTO)){
            if(StringUtils.hasText(projectPayActualDTO.getPspid())) {
                condition.eq(ProjectPayShopPurchase::getPspid,projectPayActualDTO.getPspid());
            }
            if(StringUtils.hasText(projectPayActualDTO.getKstar())){
                condition.eq(ProjectPayShopPurchase::getKstar,projectPayActualDTO.getKstar());
            }
        }

        Page<ProjectPayShopPurchase> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectPayShopPurchase::new));

        PageResult<ProjectPayShopPurchase> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectPayShopPurchaseVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectPayShopPurchaseVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectPayShopPurchaseVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "商城采购金额导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectPayShopPurchaseDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            ProjectPayShopPurchaseExcelListener excelReadListener = new ProjectPayShopPurchaseExcelListener();
        EasyExcel.read(inputStream,ProjectPayShopPurchaseDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ProjectPayShopPurchaseDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("商城采购金额导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ProjectPayShopPurchase> projectPayShopPurchasees =BeanCopyUtils.convertListTo(dtoS,ProjectPayShopPurchase::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ProjectPayShopPurchase-import::id", importId, projectPayShopPurchasees, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ProjectPayShopPurchase> projectPayShopPurchasees = (List<ProjectPayShopPurchase>) orionJ2CacheService.get("pmsx::ProjectPayShopPurchase-import::id", importId);
        log.info("商城采购金额导入的入库数据={}", JSONUtil.toJsonStr(projectPayShopPurchasees));

        this.saveBatch(projectPayShopPurchasees);
        orionJ2CacheService.delete("pmsx::ProjectPayShopPurchase-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ProjectPayShopPurchase-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ProjectPayShopPurchase> condition = new LambdaQueryWrapperX<>( ProjectPayShopPurchase. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ProjectPayShopPurchase::getCreateTime);
        List<ProjectPayShopPurchase> projectPayShopPurchasees =   this.list(condition);

        List<ProjectPayShopPurchaseDTO> dtos = BeanCopyUtils.convertListTo(projectPayShopPurchasees, ProjectPayShopPurchaseDTO::new);

        String fileName = "商城采购金额数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectPayShopPurchaseDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<ProjectPayShopPurchaseVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class ProjectPayShopPurchaseExcelListener extends AnalysisEventListener<ProjectPayShopPurchaseDTO> {

        private final List<ProjectPayShopPurchaseDTO> data = new ArrayList<>();

        @Override
        public void invoke(ProjectPayShopPurchaseDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ProjectPayShopPurchaseDTO> getData() {
            return data;
        }
    }


}
