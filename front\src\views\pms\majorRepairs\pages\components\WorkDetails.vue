<script setup lang="ts">
import {
  BasicTableAction, DataStatusTag, IOrionTableActionItem, Layout, OrionTable, isPower,
} from 'lyra-component-vue3';
import {
  Steps, Step, message, Popover,
} from 'ant-design-vue';
import {
  computed, ComputedRef, h, inject, onMounted, ref, Ref,
} from 'vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { openDailyWorkForm } from '/@/views/pms/dailyWork/utils';
import Api from '/@/api';
import MouseCellEdit from '/@/views/pms/trainManage/pages/components/MouseCellEdit.vue';

const router = useRouter();
const detailsData: Record<string, any> = inject('detailsData');
const powerData: Ref = inject('powerData');
const tableRef: Ref = ref();
const tableOptions = {
  showToolButton: false,
  showTableSetting: false,
  isSpacing: true,
  showSmallSearch: false,
  api: (params: any) => new Api('/pms/job-manage/page').fetch({
    ...params,
    query: {
      norO: 'O',
      repairRound: detailsData?.repairRound,
    },
    power: {
      containerCode: 'PMSMajorRepairsDetails',
      pageCode: 'PMS_DXGLXQ_container_02_01',
    },
  }, '', 'POST'),
  columns: [
    {
      title: '操作',
      dataIndex: 'actions',
      width: 100,
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
    {
      title: '工单号',
      dataIndex: 'number',
      width: 130,
    },
    {
      title: '作业名称',
      dataIndex: 'name',
      minWidth: 240,
      customRender({ text, record }) {
        if (isPower('PMS_DXGLXQ_container_02_01_button_02', record?.rdAuthList)) {
          return h('span', {
            class: 'flex-te action-btn',
            title: text,
            onClick: () => navDetails(record?.id),
          }, text);
        }
        return h('span', {
          class: 'flex-te',
          title: text,
        }, text);
      },
    },
    {
      title: '作业负责人',
      width: 130,
      dataIndex: 'rspUserName',
    },
    {
      title: '负责人所在中心',
      dataIndex: 'rspDeptName',
      width: 130,
    },
    {
      title: '是否重大项目',
      dataIndex: 'isMajorProject',
      width: 130,
      customRender({ text, record }) {
        if (isPower('PMS_DXGLXQ_container_02_01_button_03', record?.rdAuthList)) {
          return h(MouseCellEdit, {
            component: 'Select',
            record,
            text: text ? '是' : text === false ? '否' : '',
            componentValue: text,
            componentProps: {
              options: [
                {
                  label: '是',
                  value: true,
                },
                {
                  label: '否',
                  value: false,
                },
              ],
            },
            onSubmit(option: { label: string, value: boolean }, resolve: (value: any) => void) {
              new Api('/pms/job-manage/is/important/project').fetch({
                id: record?.id,
                isMajorProject: option.value,
              }, '', 'PUT').then(() => {
                resolve(true);
                message.success('操作成功');
                updateTable();
              });
            },
          });
        }
        return text ? '是' : text === false ? '否' : '';
      },
    },
    {
      title: '工作包审查状态',
      dataIndex: 'workPackageStatusName',
      width: 130,
    },
    {
      title: '是否重要作业',
      dataIndex: 'isImportant',
      width: 130,
      customRender({ text }) {
        return text ? '是' : text === false ? '否' : '';
      },
    },
    {
      title: '高风险',
      width: 130,
      dataIndex: 'heightRiskName',
    },
    {
      title: '首次执行',
      width: 130,
      dataIndex: 'firstExecuteName',
    },
    {
      title: '新人参与',
      width: 130,
      dataIndex: 'newParticipants',
      customRender({ text }) {
        return text ? '是' : text === false ? '否' : '';
      },
    },
    {
      title: '重要项目',
      dataIndex: 'importantProjectName',
      width: 130,
    },
    {
      title: '是否自带工器具',
      width: 130,
      dataIndex: 'isCarryTool',
      customRender({ text }) {
        return text ? '是' : text === false ? '否' : '';
      },
    },
    {
      title: '作业状态',
      dataIndex: 'busDataStatus',
      width: 130,
      customRender({ text, record }) {
        if (record?.phase && text) {
          return h(Popover, null, {
            content: () => record.phase,
            default: () => h(DataStatusTag, { statusData: text }),
          });
        }
        return text ? h(DataStatusTag, { statusData: text }) : '';
      },
    },
    {
      title: '计划开始日期',
      dataIndex: 'beginTime',
      width: 130,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '计划结束日期',
      dataIndex: 'endTime',
      width: 130,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '计划工期',
      dataIndex: 'workDuration',
      width: 130,
    },
    {
      title: '实际开工时间',
      dataIndex: 'actualBeginTime',
      width: 130,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '实际完成时间',
      dataIndex: 'actualEndTime',
      width: 130,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
  ],
};

const actions: IOrionTableActionItem[] = [
  {
    text: '编辑',
    event: 'edit',
    isShow: (record) => isPower('PMS_DXGLXQ_container_02_01_button_01', record?.rdAuthList),
  },
  {
    text: '查看',
    event: 'view',
    isShow: (record) => isPower('PMS_DXGLXQ_container_02_01_button_02', record?.rdAuthList),
  },
];

function actionClick(actionItem: any, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'edit':
      openDailyWorkForm(record, updateTable);
      break;
    case 'view':
      navDetails(record?.id);
      break;
  }
}

function navDetails(id: string) {
  router.push({
    name: 'OverhaulOperationDetails',
    params: {
      id,
    },
    query: {
      id: detailsData?.id,
    },
  });
}

function updateTable() {
  tableRef.value?.reload();
}

const steps: Ref<any[]> = ref([]);
const currentStep: ComputedRef<number> = computed(() => steps.value.findIndex((item: {
  status: number
}) => item.status === detailsData.status));

async function getStatusList() {
  const result = await new Api('/pms/major-repair-plan/status/list').fetch('', '', 'POST');
  steps.value = result || [];
}

onMounted(() => {
  getStatusList();
});
</script>

<template>
  <div class="flex-ver-wrap">
    <div class="steps-wrap">
      <Steps
        size="small"
        :current="currentStep"
      >
        <Step
          v-for="(item,index) in steps"
          :key="index"
          :title="item.desc"
        />
      </Steps>
    </div>

    <div class="table-wrap">
      <OrionTable
        ref="tableRef"
        :key="currentStep.toString()"
        :options="tableOptions"
      >
        <template #actions="{record}">
          <BasicTableAction
            :actions="actions"
            :record="record"
            @actionClick="actionClick($event,record)"
          />
        </template>
      </OrionTable>
    </div>
  </div>
</template>

<style scoped lang="less">
.flex-ver-wrap {
  display: flex;
  height: 100%;
  flex-direction: column;
}

.steps-wrap {
  width: 80%;
  flex-shrink: 0;
  margin: 0 auto;
  padding: ~`getPrefixVar('content-padding-top')` ~`getPrefixVar('content-padding-left')` 0;
}

.table-wrap {
  flex-grow: 1;
  height: 0;
  overflow: hidden;
}
</style>
