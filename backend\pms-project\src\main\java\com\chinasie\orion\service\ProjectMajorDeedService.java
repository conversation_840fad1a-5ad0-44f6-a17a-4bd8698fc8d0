package com.chinasie.orion.service;

import java.lang.String;
import java.util.List;


import com.chinasie.orion.domain.dto.ProjectMajorDeedDTO;
import com.chinasie.orion.domain.entity.ProjectMajorDeed;
import com.chinasie.orion.domain.vo.ProjectMajorDeedVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * ProjectMajorDeed 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-27 19:32:36
 */
public interface ProjectMajorDeedService  extends  OrionBaseService<ProjectMajorDeed>  {


        /**
         *  详情
         *
         * * @param id
         */
    ProjectMajorDeedVO detail(String id, String pageCode)throws Exception;

    /**
     * 新增编辑删除主要事迹
     * @param projectMajorDeedDTOList
     * @param projectId
     * @return
     * @throws Exception
     */
    Boolean saveOrRemove(List<ProjectMajorDeedDTO> projectMajorDeedDTOList, String projectId) throws Exception;

    /**
     * 获取主要事迹列表
     * @param projectId
     * @return
     * @throws Exception
     */
    List<ProjectMajorDeedVO> getList(String projectId) throws Exception;
}
