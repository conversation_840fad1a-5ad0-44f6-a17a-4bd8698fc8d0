package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ContractAssessmentStandardDTO;
import com.chinasie.orion.domain.vo.ContractAssessmentStandardVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ContractAssessmentStandardService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * ContractAssessmentStandard 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-17 09:29:14
 */
@RestController
@RequestMapping("/contractAssessmentStandard")
@Api(tags = "审核标准表")
public class  ContractAssessmentStandardController  {

    @Autowired
    private ContractAssessmentStandardService contractAssessmentStandardService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据【{{#id}}】", type = "审核标准表", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ContractAssessmentStandardVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        ContractAssessmentStandardVO rsp = contractAssessmentStandardService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param contractAssessmentStandardDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#contractAssessmentStandardDTO.name}}】", type = "审核标准表", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody ContractAssessmentStandardDTO contractAssessmentStandardDTO) throws Exception {
        String rsp =  contractAssessmentStandardService.create(contractAssessmentStandardDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param contractAssessmentStandardDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#contractAssessmentStandardDTO.name}}】", type = "审核标准表", subType = "编辑", bizNo = "{{#contractAssessmentStandardDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  ContractAssessmentStandardDTO contractAssessmentStandardDTO) throws Exception {
        Boolean rsp = contractAssessmentStandardService.edit(contractAssessmentStandardDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "审核标准表", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = contractAssessmentStandardService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "审核标准表", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = contractAssessmentStandardService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "审核标准表", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ContractAssessmentStandardVO>> pages(@RequestBody Page<ContractAssessmentStandardDTO> pageRequest) throws Exception {
        Page<ContractAssessmentStandardVO> rsp =  contractAssessmentStandardService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("审核标准表导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "审核标准表", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        contractAssessmentStandardService.downloadExcelTpl(response);
    }

    @ApiOperation("审核标准表导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "审核标准表", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = contractAssessmentStandardService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("审核标准表导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "审核标准表", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  contractAssessmentStandardService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消审核标准表导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "审核标准表", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  contractAssessmentStandardService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("审核标准表导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "审核标准表", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        contractAssessmentStandardService.exportByExcel(searchConditions, response);
    }

    @ApiOperation("通过合同编号获取审核标准")
    @PostMapping(value = "/list/{contractNumber}")
    @LogRecord(success = "【{USER{#logUserId}}】通过合同编号获取审核标准", type = "审核标准表", subType = "通过合同编号获取审核标准", bizNo = "")
    public ResponseDTO<List<ContractAssessmentStandardVO>> listByContractNumber(@PathVariable("contractNumber") String contractNumber){
        List<ContractAssessmentStandardVO> list =  contractAssessmentStandardService.listByNumber(contractNumber);
        return new ResponseDTO<>(list);
    }
}
