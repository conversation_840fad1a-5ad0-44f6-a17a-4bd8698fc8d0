package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * BasicUserInfoVO
 *
 * <AUTHOR>
 * @since 2024-06-22 14:03:35
 */
@ApiModel(value = "BasicUserInfoVO", description = "技术配置人员")
@Data
public class BasicUserInfoDTO implements Serializable {

    /**
     * 人员状态
     */
    @ApiModelProperty(value = "人员状态")
    @ExcelProperty(value = "人员状态 ", index = 0)
    private String userStatus;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @ExcelProperty(value = "姓名 ", index = 1)
    private String fullName;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    @ExcelProperty(value = "性别 ", index = 2)
    private String sex;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    @ExcelProperty(value = "身份证号", index = 3)
    private String idCard;

    /**
     * 员工号
     */
    @ApiModelProperty(value = "员工号")
    @ExcelProperty(value = "工号", index = 4)
    private String userCode;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期")
    @ExcelProperty(value = "出生日期", index = 5)
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date dateOfBirth;

    /**
     * 民族
     */
    @ApiModelProperty(value = "民族")
    @ExcelProperty(value = "民族", index = 6)
    private String nation;

    /**
     * 婚姻状况
     */
    @ApiModelProperty(value = "婚姻状况")
    @ExcelProperty(value = "婚姻状况", index = 7)
    private String maritalStatus;

    /**
     * 联系方式
     */
    @ApiModelProperty(value = "联系方式")
    @ExcelProperty(value = "联系方式", index = 8)
    private String contactInformation;

    @ApiModelProperty(value = "工作年限")
    @ExcelProperty(value = "工作年限", index = 9)
    private String workYear;

    /**
     * 最高学历
     */
    @ApiModelProperty(value = "最高学历")
    @ExcelProperty(value = "最高学历", index = 10)
    private String highestEducation;

    /**
     * 所学专业
     */
    @ApiModelProperty(value = "所学专业")
    @ExcelProperty(value = "所学专业", index = 11)
    private String major;

    /**
     * 职称
     */
    @ApiModelProperty(value = "职称")
    @ExcelProperty(value = "职称", index = 12)
    private String title;

    /**
     * 专业技术证书
     */
    @ApiModelProperty(value = "专业技术证书")
    @ExcelProperty(value = "专业技术证书", index = 13)
    private String professionalTechnicalCertificate;

    /**
     * 所属公司
     */
    @ApiModelProperty(value = "所属公司")
    @ExcelProperty(value = "所属公司", index = 14)
    private String companyName;

    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    @ExcelProperty(value = "所属部门", index = 15)
    private String deptName;

    /**
     * 所属研究所
     */
    @ApiModelProperty(value = "所属研究所")
    @ExcelProperty(value = "所属研究所", index = 16)
    private String instituteName;

    /**
     * 分管项目经理
     */
    @ApiModelProperty(value = "分管项目经理")
    @ExcelProperty(value = "分管项目经理", index = 17)
    private String departmentHeadProjectManager;

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @ExcelProperty(value = "供应商编码", index = 18)
    private String supplierCode;

    /**
     * 所属供应商
     */
    @ApiModelProperty(value = "所属供应商")
    @ExcelProperty(value = "所属供应商", index = 19)
    private String affiliatedSupplier;

    /**
     * 是否项目制人员
     */
    @ApiModelProperty(value = "是否项目制人员")
    @ExcelProperty(value = "是否项目制人员", index = 20)
    private String projectBasedStaff;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号", index = 21)
    private String contractNumber;

    /**
     * 合同级别
     */
    @ApiModelProperty(value = "合同级别")
    @ExcelProperty(value = "合同级别", index = 22)
    private String contractLevel;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称", index = 23)
    private String contractName;

    /**
     * 工作内容
     */
    @ApiModelProperty(value = "工作内容")
    @ExcelProperty(value = "工作内容", index = 24)
    private String jobContent;

    /**
     * 常驻服务地点
     */
    @ApiModelProperty(value = "常驻服务地点")
    @ExcelProperty(value = "常驻服务地点", index = 25)
    private String permanentServiceLocation;

    /**
     * 是否从事放射性工作
     */
    @ApiModelProperty(value = "是否从事放射性工作")
    @ExcelProperty(value = "是否从事放射性工作", index = 26)
    private String worksWithRadioactiveMaterials;

    /**
     * 是否已完成体检
     */
    @ApiModelProperty(value = "是否已完成体检")
    @ExcelProperty(value = "是否已完成体检", index = 27)
    private String completedPhysicalExamination;

    /**
     * 办卡或授权
     */
    @ApiModelProperty(value = "办卡或授权")
    @ExcelProperty(value = "办卡或授权", index = 28)
    private String cardOrAuthorizationChoice;

    /**
     * 是否有亲属在集团内
     */
    @ApiModelProperty(value = "是否有亲属在集团内")
    @ExcelProperty(value = "是否有亲属在集团内", index = 29)
    private String hasRelativeInGroup;

    /**
     * 亲属姓名
     */
    @ApiModelProperty(value = "亲属姓名")
    @ExcelProperty(value = "亲属姓名", index = 30)
    private String relativeName;

    /**
     * 亲属职务
     */
    @ApiModelProperty(value = "亲属职务")
    @ExcelProperty(value = "亲属职务", index = 31)
    private String relativePosition;


    /**
     * 亲属公司
     */
    @ApiModelProperty(value = "亲属公司")
    @ExcelProperty(value = "亲属公司", index = 32)
    private String relativeCompany;

    /**
     * 是否技术配置
     */
    @ApiModelProperty(value = "是否技术配置")
    @ExcelProperty(value = "是否技术配置", index = 33)
    private String technicalConfiguration;

    /**
     * 预计离岗时间
     */
    @ApiModelProperty(value = "预计离岗时间")
    @ExcelProperty(value = "预计离岗时间", index = 34)
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date expectedDepartureDate;

    /**
     * 是否违反相关安全规范
     */
    @ApiModelProperty(value = "是否违反相关安全规范")
    @ExcelProperty(value = "是否违反相关安全规范", index = 35)
    private String violatedSafetyRegulations;

    /**
     * 入场备注
     */
    @ApiModelProperty(value = "入场备注")
    @ExcelProperty(value = "入场备注", index = 36)
    private String entryRemarks;

    /**
     * 入场时间
     */
    @ApiModelProperty(value = "入场时间")
    @ExcelProperty(value = "入场时间 ", index = 37)
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date entryTime;

    /**
     * 离场时间
     */
    @ApiModelProperty(value = "离场时间")
    @ExcelProperty(value = "离场时间 ", index = 38)
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date departureTime;

    /**
     * 离职备注
     */
    @ApiModelProperty(value = "离职备注")
    @ExcelProperty(value = "离职备注 ", index = 39)
    private String departure;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    @ExcelProperty(value = "操作时间 ", index = 40)
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date operationTime;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    @ExcelProperty(value = "操作人 ", index = 41)
    private String operationName;

    /**
     * 锁定状态
     */
    @ApiModelProperty(value = "锁定状态")
    @ExcelProperty(value = "锁定状态 ", index = 42)
    private String lockedStatus;

}
