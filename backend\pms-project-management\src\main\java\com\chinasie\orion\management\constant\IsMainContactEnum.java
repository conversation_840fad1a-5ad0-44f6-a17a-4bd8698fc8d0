package com.chinasie.orion.management.constant;

/**
 * 客户级别字典
 */

public enum IsMainContactEnum {

    TRUE("true","是"),
    FALSE("false","否");

    private String name;
    private String desc;

    IsMainContactEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(String value){

        for (IsMainContactEnum lt : IsMainContactEnum.values()) {
            if(lt.name.equals( value)){
                return lt.desc;
            }
        }
        return null;
    }


}