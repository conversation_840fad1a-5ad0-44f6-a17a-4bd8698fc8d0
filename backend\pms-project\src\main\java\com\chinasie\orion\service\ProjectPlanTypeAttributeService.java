package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.ProjectPlanTypeAttributeDTO;
import com.chinasie.orion.domain.entity.ProjectPlanTypeAttribute;
import com.chinasie.orion.domain.vo.ProjectPlanTypeAttributeVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/10/9 17:37
 */
public interface ProjectPlanTypeAttributeService extends OrionBaseService<ProjectPlanTypeAttribute> {

    /**
     * 新增项目计划类型属性
     * @param typeAttributeDTO
     * @return
     * @throws Exception
     */
    String add(ProjectPlanTypeAttributeDTO typeAttributeDTO) throws Exception;

    /**
     * 编辑项目计划类型属性
     * @param typeAttributeDTO
     * @return
     * @throws Exception
     */
    Boolean edit(ProjectPlanTypeAttributeDTO typeAttributeDTO) throws Exception;

    /**
     * 获取风险属性分页
     * @param pageRequest
     * @return
     * @throws Exception
     */
    Page<ProjectPlanTypeAttributeVO> getPage(Page<ProjectPlanTypeAttribute> pageRequest) throws Exception;
}
