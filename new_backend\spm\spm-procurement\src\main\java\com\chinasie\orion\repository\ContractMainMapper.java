package com.chinasie.orion.repository;

import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.ContractCenterPlanVO;
import com.chinasie.orion.domain.vo.ContractInfoVO;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


/**
 * <p>
 * ContractMain Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-17 09:42:35
 */
@Mapper
public interface ContractMainMapper extends  OrionBaseMapper  <ContractMain> {

    /**
     * 获取编制中的合同计划主表id
     * @return 结果
     */
    @Select("select id from pmsx_contract_main where logic_status = 1 and status = 121")
    List<String> getUnIssuedIds();

    /**
     * 通过合同id 赋值相对应的中心用人计划
     * @param unIssuedIds 合同ids
     * @return 结果
     */
    List<ContractCenterPlan> copyCenterPlan(@Param("unIssuedIds") List<String> unIssuedIds);

    /**
     * 出勤签到统计
     * @param orgCodes
     * @param year
     * @return
     */
    List<AttendanceSignStatistics> selectAttendanceSign(@Param("orgCodes") List<String> orgCodes, @Param("year") int year, @Param("contractNumber") String contractNumber, @Param("quarter") Integer quarter);


    /**
     * 差旅费用统计
     * @param orgCodes
     * @param year
     * @return
     */
    List<TravelCostStatistics> selectTravelCost(@Param("orgCodes") List<String> orgCodes, @Param("year") int year, @Param("contractNumber") String contractNumber, @Param("quarter") Integer quarter);


    /**
     * 机票费用统计
     * @param orgCodes
     * @param year
     * @return
     */
    List<PlaneCostStatistics> selectPlaneCost(@Param("orgCodes") List<String> orgCodes, @Param("year") int year, @Param("contractNumber") String contractNumber, @Param("quarter") Integer quarter);


    /**
     * 开口项费用
     * @param orgCodes
     * @param year
     * @return
     */
    List<OpenCostStatistics> selectOpenCost(@Param("orgCodes") List<String> orgCodes, @Param("year") int year, @Param("contractNumber") String contractNumber, @Param("quarter") Integer quarter);



    /**
     * 获取合同信息
     * @param year 参数
     * @param contractNumber 参数
     * @return 结果
     */
    @Select("SELECT \n" +
            "\tpcm.contract_number as 'contractNumber',\n" +
            "\tpcm.contract_name as 'contractName',\n" +
            "\tci.estimated_start_time as 'estimatedStartTime',\n" +
            "\tci.estimated_end_time as 'estimatedEndTime',\n" +
            "\tci.status_name as 'contractStatusName',\n" +
            "\tci.status as 'contractStatus',\n" +
            "\tci.type as 'contractType',\n" +
            "\tYEAR(pcm.year) as 'year',\n" +
            "\tpcm.status \n" +
            "FROM\n" +
            "\tpmsx_contract_main pcm\n" +
            "\tINNER JOIN ncf_form_contract_info ci ON pcm.contract_number = ci.contract_number \n" +
            "WHERE\n" +
            "\tpcm.contract_number = #{contractNumber} \n" +
            "\tAND YEAR ( pcm.YEAR ) = #{year} and pcm.logic_status = 1")
    ContractInfoVO getContractInfo(@Param("year") Integer year,@Param("contractNumber") String contractNumber);


    @Select("SELECT\n" +
            "\tDISTINCT(pccp.id),\n" +
            "\tpcct.cost_type_name AS 'costTypeName',\n" +
            "\tpcct.cost_name AS 'costName',\n" +
            "\tpcct.unit_price AS 'unitPrice',\n" +
            "\tpccp.num,\n" +
            "\tpel.before_num as 'beforeNum',\n" +
            "\tpcct.unit \n" +
            "FROM\n" +
            "\tpmsx_contract_center_plan pccp\n" +
            "\tINNER JOIN pmsx_contract_cost_type pcct ON pccp.cost_type_id = pcct.id\n" +
            "\tLEFT JOIN pmsx_edit_log pel ON pccp.id = pel.plan_id \n" +
            "WHERE\n" +
            "\tpccp.contract_number = #{contractNumber}\n" +
            "\tAND YEAR ( pccp.YEAR ) = #{year}\n" +
            "\tAND pccp.center_code = #{code}\n")
    List<ContractCenterPlanVO> listByCode(@Param("code") String code,@Param("contractNumber") String contractNumber,@Param("year") Integer year);


    @Select("SELECT\n" +
            "\tpccp.id,\n" +
            "\tpcct.cost_type_name AS 'costTypeName',\n" +
            "\tpcct.cost_name AS 'costName',\n" +
            "\tpcct.unit_price AS 'unitPrice',\n" +
            "\tSUM( pccp.num ) AS 'num',\n" +
            "\tSUM( pel.before_num ) AS 'beforeNum',\n" +
            "\tpcct.unit \n" +
            "FROM\n" +
            "\tpmsx_contract_center_plan pccp\n" +
            "\tINNER JOIN pmsx_contract_cost_type pcct ON pccp.cost_type_number = pcct.cost_type_number\n" +
            "\tLEFT JOIN pmsx_edit_log pel ON pccp.id = pel.plan_id \n" +
            "WHERE\n" +
            "\tpccp.contract_number = #{contractNumber}\n" +
            "\t\n" +
            "\tAND YEAR ( pccp.YEAR ) = #{year}\n" +
            "\t\n" +
            "GROUP BY\n" +
            "\tpcct.cost_type_name,\n" +
            "\tpcct.unit_price,\n" +
            "\tpccp.id,\n" +
            "\tpcct.cost_name,\n" +
            "\tpcct.unit")
    List<ContractCenterPlanVO> listSum(@Param("contractNumber") String contractNumber,@Param("year") Integer year);
}

