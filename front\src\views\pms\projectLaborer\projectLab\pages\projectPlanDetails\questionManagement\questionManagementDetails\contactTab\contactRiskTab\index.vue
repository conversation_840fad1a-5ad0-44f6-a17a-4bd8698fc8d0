<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
  >
    <template #toolbarLeft>
      <BasicButton
        type="primary"
        icon="add"
        @click="clickType('add')"
      >
        新增风险
      </BasicButton>
      <BasicButton
        type="primary"
        icon="add"
        @click="openModalQuestion(true,{})"
      >
        关联风险
      </BasicButton>
      <BasicButton
        icon="delete"
        @click="clickType('delete')"
      >
        移除
      </BasicButton>
    </template>

    <template #createTime="{ text }">
      {{ text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '' }}
    </template>
    <template #schedule="{ text }">
      <Progress
        :percent="text"
        size="small"
        style="padding-right: 40px"
      />
    </template>

    <template #statusName="{ record }">
      <span
        :style="{
          color: record.statusName == '未完成' ? 'red' : ''
        }"
      >
        {{ record.statusName }}
      </span>
    </template>
    <template #action="{record}">
      <BasicTableAction :actions="actionsBtn(record)" />
    </template>
  </OrionTable>
  <ContactQuestionModal
    @register="registerQuestion"
    @check-project-callback="checkProjectCallback"
  />
  <AddTableNode
    isQuestion="true"
    @register="register"
    @update="addSuccess"
  />
</template>
<script lang="ts">
import {
  computed, defineComponent, h, inject, onMounted, reactive, ref, toRefs, unref,
} from 'vue';
import {
  BasicTableAction, DataStatusTag, isPower, ITableActionItem, OrionTable, useDrawer, useModal,
} from 'lyra-component-vue3';
import { message, Modal, Progress } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import useIndex from '/@/views/pms/projectLaborer/zkhooks/useLocalS.js';
import AddTableNode from '/@/views/pms/projectLaborer/projectLab/pages/projectPlanDetails/riskManagement/model/AddTableNode.vue';
import ContactQuestionModal from './modal/ContactQuestionModal.vue';
import Api from '/@/api';

const tableRef = ref(null);
export default defineComponent({
  name: 'ProductLibraryIndex',
  components: {
    Progress,
    OrionTable,
    AddTableNode,
    ContactQuestionModal,
    BasicTableAction,
  },
  setup() {
    const formData = inject('formData', {});
    const [registerQuestion, { openModal: openModalQuestion }] = useModal();
    const [register, { openDrawer }] = useDrawer();
    const state = reactive({
      editdataSource: {},
      addNodeModalData: {},
      message: '',
      nodeData: <any>[],
      searchData: {},
      params: {},
      queryCondition: [],
      dataSource: [],
      selectedRowKeys: [],
      selectedRows: [],
      searchStatus: '',
      hah: {},
      btnList: [
        {
          type: 'edit',
          powerCode: 'FXGL_container_button_03',
        },
        {
          type: 'delete',
          powerCode: 'FXGL_container_button_04',
        },
      ],
    });
    const powerData = inject('powerData');
    const state6 = reactive({
      btnObjectData: {
        check: { show: true },
        open: { show: true },
        // search: { show: true },
      },

    });
    const clickType = (type) => {
      switch (type) {
        case 'add':
          openDrawer(true, {
            type: 'add',
            data: { fromObjName: formData.value.projectName },
          });
          break;
        case 'delete':
          multiDelete();
          break;
      }
    };
    const multiLengthCheckHandle = () => {
      if (state.selectedRows.length === 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };
    /* 批量删除 */
    const multiDelete = () => {
      if (multiLengthCheckHandle()) return;
      Modal.confirm({
        title: '请确认是否对当前选中数据进行移除?',
        onOk() {
          return new Api(`/pas/questionRelationRisk/relationRisk/${qusetionItemId.value}`).fetch(state.selectedRowKeys, '', 'DELETE')
            .then((res) => {
              message.success('移除成功');
              tableRef.value.reload();
            });
        },
      });
    };

    async function searchEmit(data) {
      state.hah = data;
      await getTableAction().reload();
      setTimeout(() => {
        state.hah = {};
      });
    }
    function getTableAction() {
      const tableAction = unref(tableRef);
      if (!tableAction) {
        throw new Error('内部错误');
      }
      return tableAction;
    }

    const router = useRouter();
    const zkKeys = () => getTableAction().getSelectRowKeys();
    const zkRows = () => getTableAction().getSelectRows();
    let qusetionItemId: any = inject('qusetionItemId');
    let projectId: any = inject('projectId');
    const love = {
      id: qusetionItemId.value,
      className: 'QuestionManagement',
      moduleName: '项目管理-问题管理-关联风险',
      type: 'GET',
      remark: `获取/搜索了【${qusetionItemId.value}】关联风险列表`,
    };
    const getFormData = async (obj = {}) => {
      const res = await new Api(`/pas/question-management/relation/risk/${qusetionItemId.value}`).fetch('', '', 'POST');
      state.dataSource = res;
      // state.tablehttp.total = res.totalSize;
      state.selectedRowKeys = [];
      state.selectedRows = [];
    };
    const onSelectChange = (selectedRowKeys, selectedRows) => {
      state.selectedRowKeys = selectedRowKeys;
      state.selectedRows = selectedRows;
    };
    const checkData = () => {
      if (lengthCheckHandle()) return;
      state.nodeData = JSON.parse(JSON.stringify(zkRows()[0]));
    };
    const checkData2 = (data) => {
      state.nodeData = JSON.parse(JSON.stringify(data));
    };
    const lengthCheckHandle = () => {
      if (zkKeys().length > 1) {
        message.warning('请选择一条数据进行操作');
        return true;
      }
      if (zkKeys().length === 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };

    const riskDetailsIndexLocal = useIndex('riskDetailsIndexLocal');
    onMounted(() => {
      riskDetailsIndexLocal.value = 0;
      getFormData();
    });
    const openDetail = () => {
      if (lengthCheckHandle()) return;
      toDetails(zkRows()[0]);
    };
    const toDetails = (data) => {
      router.push({
        name: 'RiskManagementDetails',
        query: {
          itemId: data.id,
          projectId: projectId.value,
          type: 0,
        },
      });
    };
    function beforeFetch(T) {
      T = state.hah;
      return T;
    }

    const tableOptions = {
      showIndexColumn: false,
      pagination: true,
      bordered: false,
      deleteToolButton: 'add|delete|enable|disable',
      rowSelection: {
        selectedRowKeys: computed(() => state.selectedRowKeys),
        onChange: onSelectChange,
      },
      api(params) {
        return new Api('').fetch({
          ...params,
          query: {
            questionId: qusetionItemId.value,
          },
        }, 'pas/questionRelationRisk/relationRisk/page', 'POST').then((res) => res);
      },
      columns: [
        {
          title: '编号',
          dataIndex: 'number',
          align: 'left',
          key: 'number',

          width: '120px',
          // sorter: true,
          ellipsis: true,
        },
        {
          title: '名称',
          dataIndex: 'name',
          align: 'left',
          minWidth: 220,
          // sorter: true,
          ellipsis: true,
          customRender({ record, text }) {
            return h(
              'span',
              {
                class: 'action-btn',
                title: text,
                onClick(e) {
                  router.push({
                    name: 'RiskManagementDetails',
                    query: {
                      itemId: record.id,
                      projectId: projectId.value,
                      type: 0,
                    },
                  });
                },
              },
              text,
            );
          },
        },
        {
          title: '风险描述',
          dataIndex: 'remark',
          key: 'remark',
          width: '120px',
          align: 'left',
          ellipsis: true,
        },
        {
          title: '状态',
          dataIndex: 'dataStatus',
          key: 'dataStatus',
          width: '120px',
          align: 'left',
          // sorter: true,
          ellipsis: true,
          customRender: ({ record, text }) => h(DataStatusTag, { statusData: record.dataStatus }),
        },

        {
          title: '风险类型',
          dataIndex: 'riskTypeName',
          key: 'riskType',
          width: '80px',
          margin: '0 20px 0 0',
          align: 'left',
          slots: { customRender: 'riskTypeName' },
          ellipsis: true,
        },
        {
          title: '发生概率',
          dataIndex: 'riskProbabilityName',
          key: 'riskProbabilityName',
          width: '100px',
          align: 'left',
          slots: { customRender: 'riskProbabilityName' },
          ellipsis: true,
        },
        {
          title: '影响程度',
          dataIndex: 'riskInfluenceName',
          key: 'riskInfluenceName',

          width: '120px',
          align: 'left',
          slots: { customRender: 'riskInfluenceName' },
          ellipsis: true,
        },
        {
          title: '预估发生时间',
          dataIndex: 'predictStartTimeName',
          key: 'predictStartTimeName',
          width: '120px',
          align: 'left',
          // sorter: true,
          ellipsis: true,
        },

        {
          title: '负责人',
          dataIndex: 'principalName',
          key: 'principalName',
          width: '120px',
          align: 'left',
          // sorter: true,
          ellipsis: true,
          slots: { customRender: 'principalName' },
        },
        {
          title: '期望完成日期',
          dataIndex: 'predictEndTime',
          key: 'predictEndTime',
          width: '120px',
          align: 'left',
          // sorter: true,
          ellipsis: true,
          customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD') : ''),
        },

        {
          title: '应对策略',
          dataIndex: 'copingStrategyName',
          key: 'copingStrategyName',

          width: '120px',
          align: 'left',
          ellipsis: true,
          slots: { customRender: 'copingStrategyName' },
        },
        {
          title: '应对措施',
          dataIndex: 'solutions',
          key: 'solutions',

          width: '120px',
          align: 'left',
          ellipsis: true,
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 150,
          align: 'left',
          fixed: 'right',
          slots: { customRender: 'action' },
        },
      ],
    };
    const actionsBtn = (record) => {
      const actions:ITableActionItem[] = [
        {
          text: '编辑',
          isShow: record.isCurrentCreate && computed(() => state.btnList.some((item) => item.type === 'edit')),
          onClick() {
            openDrawer(true, {
              type: 'edit',
              data: { id: record.id },
            });
          },
        },
        {
          text: '删除',
          isShow: record.isCurrentCreate && computed(() => state.btnList.some((item) => item.type === 'delete')),
          modal() {
            return new Api(`/pas/questionRelationRisk/relationRisk/deleteRisk/${qusetionItemId.value}`).fetch({
              riskId: record.id,
            }, '', 'DELETE').then(() => {
              message.success('删除成功');
              tableRef.value.reload();
            });
          },
        },

        {
          text: '移除',
          modal() {
            return new Api(`/pas/questionRelationRisk/relationRisk/${qusetionItemId.value}`).fetch([record.id], '', 'DELETE')
              .then((res) => {
                message.success('移除成功');
                tableRef.value.reload();
              });
          },
        },

      ];
      return actions;
    };
    function addSuccess() {
      let pageSize = tableRef.value.getPaginationRef().pageSize;
      tableRef.value.setPagination({
        current: 1,
        pageSize,
      });
      tableRef.value.reload();
    }
    const checkProjectCallback = (data) => {
      new Api(`/pas/questionRelationRisk/relationRisk/${qusetionItemId.value}`).fetch(
        data.map((item) => item.id),
        '',
        'POST',
      )
        .then((res) => {
          message.success('关联成功');
          tableRef.value.reload();
        });
    };

    return {
      ...toRefs(state),
      ...toRefs(state6),
      clickType,
      confirm,
      dayjs,
      tableRef,
      searchEmit,
      beforeFetch,
      onSelectChange,
      tableOptions,
      powerData,

      registerQuestion,
      openModalQuestion,
      register,
      addSuccess,
      multiDelete,
      actionsBtn,
      checkProjectCallback,

    };
  },
});
</script>
<style lang="less" scoped>
</style>
