<template>
  <div>
    <div
      v-if="!multiple && isButton"
      class="flex flex-ac"
    >
      <AButtonGroup>
        <Upload
          :before-upload="beforeUpload"
          :showUploadList="false"
          :multiple="multiple"
          :accept="accept"
          :disabled="loadingStatus"
          class="upload-btn-wrap"
        >
          <AButton
            :type="buttonType"
            :loading="loadingStatus"
          >
            <Icon
              v-if="!loadingStatus"
              icon="fa-upload"
              :color="buttonType !== 'default' ? '#fff' : ''"
            />
            {{ buttonText }}
          </AButton>
          <div
            v-if="fileList.length && loadingStatus"
            class="load-progress"
            :style="{ width: (fileList[0]?.percent ? fileList[0]?.percent : 0) + '%' }"
          />
        </Upload>
        <AButton
          v-if="loadingStatus"
          type="primary"
          @click="allCancel"
        >
          取消
        </AButton>
      </AButtonGroup>
      <div class="info flex flex-ac">
        <Icon
          icon="fa-info-circle"
          color="#0960bd"
        />
        <span class="ml5">上传文件大小不超过 {{ maxSize }} M</span>
      </div>
    </div>
    <div v-else-if="isButton">
      <AButton
        :type="buttonType"
        @click="openModal"
      >
        <Icon
          v-if="!loadingStatus"
          icon="fa-upload"
          :color="buttonType !== 'default' ? '#fff' : ''"
        />{{ buttonText }}
      </AButton>
    </div>
    <BasicModal
      :title="buttonText"
      width="1000px"
      :height="400"
      :closeFunc="closeFunc"
      :showOkBtn="false"
      @register="registerModal"
      @visibleChange="visibleChange"
    >
      <div class="upload-wrap">
        <div class="top-wrap">
          <Upload
            :before-upload="beforeUpload"
            :showUploadList="false"
            :multiple="multiple"
            :accept="accept"
          >
            <AButton type="primary">
              <Icon
                v-if="!loadingStatus"
                icon="fa-upload"
                color="#fff"
              />选择文件
            </AButton>
          </Upload>
          <div class="flex-f1 flex">
            <div class="info flex flex-ac">
              <Icon
                icon="fa-info-circle"
                color="#0960bd"
              />
              <span class="ml5">单个文件不超过 {{ maxSize }} M{{
                maxNumber ? `，最多只能上传 ${maxNumber} 个文件` : ''
              }}</span>
            </div>
          </div>

          <div>
            <AButton
              v-if="!isUploadNow"
              type="primary"
              :disabled="!waitUploadFileStatus"
              @click="startUpload"
            >
              <Icon
                v-if="!loadingStatus"
                icon="fa-play-circle-o"
                color="#fff"
              />开始上传
            </AButton>
          </div>
          <div class="ml10">
            <AButton
              type="primary"
              @click="allCancel"
            >
              全部停止
            </AButton>
          </div>
        </div>
        <div class="list">
          <table width="100%">
            <thead>
              <tr>
                <td
                  width="70"
                  align="center"
                >
                  序号
                </td>
                <td>文件名称</td>
                <td width="100">
                  文件大小
                </td>
                <td
                  v-if="isClassification"
                  width="150"
                >
                  密级
                </td>
                <td
                  v-if="isClassification"
                  width="150"
                >
                  保密期限
                </td>
                <td width="100">
                  上传结果
                </td>
                <td width="100">
                  打开工具
                </td>
                <td width="70">
                  操作
                </td>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="(item, index) in fileList"
                :key="item.uid"
              >
                <td align="center">
                  <div>{{ index + 1 }}</div>
                </td>
                <td>
                  <div class="flex-te file-name">
                    {{ item.name }}
                  </div>
                </td>
                <td>
                  <div>{{ fileSize(item.size) }}M</div>
                </td>
                <td v-if="isClassification">
                  <div>
                    <ASelect
                      v-model:value="item.classification"
                      :options="classificationOptions"
                      class="select-wrap"
                      placeholder="请选择密级"
                      @change="classificationChange(item, $event, $event)"
                    />
                    <div
                      v-if="item?.errorClassification"
                      class="error-tool-message"
                    >
                      请选择密级
                    </div>
                  </div>
                </td>
                <td v-if="isClassification">
                  <div>
                    <ASelect
                      v-if="item?.isSecurityLimit"
                      v-model:value="item.securityLimit"
                      :options="securityLimitOptions"
                      class="select-wrap"
                      placeholder="请选择保密期限"
                      @change="securityLimitChange(item)"
                    /><span v-else>无期限</span>
                    <div
                      v-if="item?.errorSecurityLimit"
                      class="error-tool-message"
                    >
                      请选择保密期限
                    </div>
                  </div>
                </td>
                <td>
                  <div>
                    <UploadStatus
                      :percent="item?.percent"
                      :status="item.status"
                    />
                  </div>
                </td>
                <td>
                  <div>
                    <OpenToolSelect
                      :key="item.uid"
                      v-model:value="item.openTool"
                      :accept="item.accept"
                    />
                    <div
                      v-if="item?.errorTool"
                      class="error-tool-message"
                    >
                      请选择打开工具
                    </div>
                  </div>
                </td>
                <td>
                  <div>
                    <span
                      class="del-btn"
                      @click="del(item, index)"
                    >移除</span>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <template #centerFooter>
        <AButton
          type="primary"
          :loading="loadingStatus || saveStatus"
          :disabled="uploadSuccessStatus"
          @click="save"
        >
          保存
        </AButton>
      </template>
    </BasicModal>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  computed,
  ref,
  nextTick,
  watchEffect,
  watch,
} from 'vue';
import {
  Button, message, Upload, Modal, Select,
} from 'ant-design-vue';
import { defHttp } from '/@/utils/http/axios';
import UploadStatus from './UploadStatus.vue';
import { UploadResultStatus } from './enum';
import Axios from 'axios';
import OpenToolSelect from './OpenToolSelect.vue';
import { BasicModal, useModal } from '/@/components/Modal';
import Icon from '/@/components/Icon';
import Api from '/@/api';

export default defineComponent({
  name: 'BasicUpload',
  components: {
    AButton: Button,
    AButtonGroup: Button.Group,
    Upload,
    UploadStatus,
    OpenToolSelect,
    BasicModal,
    Icon,
    ASelect: Select,
  },
  props: {
    showUpModal: {
      type: Boolean,
      default: true,
    },
    buttonText: {
      type: String,
      default: '上传文件',
    },
    isButton: {
      type: Boolean,
      default: true,
    },
    buttonType: {
      type: String,
      default: 'primary',
    },
    multiple: {
      type: Boolean,
      default: true,
    },
    maxSize: {
      type: Number,
      default: 100,
    },
    maxNumber: {
      type: [Number, Boolean],
      default: false,
    },
    accept: {
      type: String,
      default: '',
    },
    isUploadNow: {
      type: Boolean,
      default: false,
    },
    isClassification: {
      type: Boolean,
      default: false,
    },
    isToolRequired: {
      type: Boolean,
      default: true,
    },
    secretLevel: {
      type: String,
      default: '',
    },
    secretLevelApi: {
      type: Function,
      default: null,
    },
  },
  emits: ['uploadSuccessChange', 'saveChange'],
  setup(props, { emit }) {
    const state: any = reactive({
      fileList: [],
      // 密级选项
      classificationOptions: [],
      // 保密期限选项
      securityLimitOptions: [],
    });
    const saveStatus = ref(false);
    nextTick(() => {
      if (props.isClassification && !state.classificationOptions.length) {
        watchEffect(() => {
          if (props.isClassification || props.secretLevel) {
            loadClassificationList();
            loadSecurityLimitList();
          }
        });
      }
    });
    watch(
      () => props.showUpModal,
      (newvalue, oldvalue) => {
        openModal();
      },
    );
    // 加载密级
    async function loadClassificationList() {
      if (props.secretLevelApi) {
        let data = await props.secretLevelApi();
        state.classificationOptions = data
          .map((item) => ({
            ...item,
            label: item.name,
            value: item.id,
            id: item.id,
            key: item.id,
          }))
          .sort((a, b) => b.sort - a.sort);
      } else if (props.secretLevel) {
        // 获取低于传入密级的密级选项
        new Api(`/pmi/data-classification/classification?id=${props.secretLevel}`)
          .fetch(
            {
              query: {
                status: 1,
              },
            },
            '',
            'GET',
          )
          .then((data) => {
            state.classificationOptions = data
              .map((item) => ({
                ...item,
                label: item.name,
                value: item.id,
                id: item.id,
                key: item.id,
              }))
              .sort((a, b) => a.sort - b.sort);
          });
      } else {
        // 获取所有密级选项
        new Api('/pmi/data-classification')
          .getList({
            status: 1,
          })
          .then((data) => {
            if (data) {
              state.classificationOptions = data
                .map((item) => ({
                  label: item.name,
                  value: item.id,
                  key: item.key,
                  id: item.id,
                  ...item,
                }))
                .sort((a, b) => a.sort - b.sort);
            }
          });
      }
    }

    // 加载保密期限
    function loadSecurityLimitList() {
      // securityLimitOptions
      new Api('/pmi/dict')
        .fetch({}, 'dictd85e24b99d23444f8ccf3ae59bcde2d3', 'GET')
        .then((data) => {
          if (data) {
            state.securityLimitOptions = data.map((item) => ({
              ...item,
              label: item.description,
              value: item.id,
              key: item.id,
            }));
          }
        });
    }

    // 弹窗相关
    const [registerModal, { openModal }] = useModal();
    // const [];

    // 上传状态
    const loadingStatus = computed(() => {
      for (let item of state.fileList) {
        if (item?.status === UploadResultStatus.UPLOADING) {
          // changeOkLoading(true);
          return true;
        }
      }
      // changeOkLoading(false);
      return false;
    });

    // 有文件上传成功
    const uploadSuccessStatus = computed(() => {
      for (let item of state.fileList) {
        if (item?.status === UploadResultStatus.SUCCESS) {
          return false;
        }
      }
      return true;
    });

    // 获取所有上传成功的文件
    const successAll = computed(() => state.fileList.filter((listItem) => listItem.status === UploadResultStatus.SUCCESS));

    // 有待上传文件
    const waitUploadFileStatus = computed(() => {
      const waitFiles = state.fileList.filter((item: any) => (
        item.status !== UploadResultStatus.SUCCESS
            && item.status !== UploadResultStatus.UPLOADING
      ));
      return waitFiles.length > 0;
    });

    // 上传api
    async function uploadApiByItem(item) {
      if (!item.openTool && props.isToolRequired) {
        item.errorTool = true;
        return;
      }
      item.errorTool = false;

      // 密级控制
      if (props.isClassification) {
        // 是否选择密级判断
        if (!item.classification) {
          item.errorClassification = true;
          return;
        }
        item.errorClassification = false;

        // 选择了密级未选择期限判断
        if (
          item.classification
            && item?.classificationItem?.isSecurityLimit
            && !item.securityLimit
        ) {
          item.errorSecurityLimit = true;
          return;
        }
        item.errorSecurityLimit = false;
      }

      try {
        let cancelToken = Axios.CancelToken;
        const source = cancelToken.source();
        return await defHttp
          .uploadFile(
            {
              url: '/file/upload',
              timeout: 1000 * 60 * 60,
              cancelToken: source.token,
              onUploadProgress(progressEvent: any) {
                item.percent = ((progressEvent.loaded / progressEvent.total) * 100).toFixed(2) - 0;
                item.status = UploadResultStatus.UPLOADING;
                item.source = source;
              },
            },
            { file: item.file },
          )
          .then((data) => {
            item.status = UploadResultStatus.SUCCESS;
            item.responseData = data.data;
            item.result = data.data.result;
            emit('uploadSuccessChange', {
              item,
              successAll: successAll.value,
            });
            if (!props.multiple) {
              save();
            }
            return data;
          });
      } catch (e) {
        console.log('捕获的错误', e);
        item.status = UploadResultStatus.ERROR;
        return e;
      }
    }

    // 开始上传
    async function startUpload() {
      const uploadFileList = state.fileList.filter((item: any) => (
        item.status !== UploadResultStatus.SUCCESS
              && item.status !== UploadResultStatus.UPLOADING
      )) || [];

      const loadStatus = await Promise.all(uploadFileList.map((item) => uploadApiByItem(item)));
    }

    // 全部取消
    function allCancel() {
      state.fileList.forEach((item) => {
        if (item.status === UploadResultStatus.UPLOADING && item.source) {
          item.source.cancel();
        }
      });
    }

    // 保存
    function save() {
      let close = true;
      emit('saveChange', successAll.value, async (api) => {
        if (api) {
          close = false;
        }
        saveStatus.value = true;
        return api
          .then(() => {
            openModal(false);
            saveStatus.value = false;
            clearFile();
            message.success('保存成功');
            return Promise.resolve();
          })
          .catch(() => {
            saveStatus.value = false;
          });
      });
      close && openModal(false);
    }

    // 转换文件大小
    const fileSize = (size) => (size / 1024 / 1024).toFixed(2) - 0;

    // 清除列表
    function clearFile() {
      state.fileList = [];
    }

    // 判断是否是相同的文件
    function isRepetitive(file1, file2) {
      // for (let key in file1) {
      //   if (file2[key] !== file1[key]) {
      //     return false;
      //   }
      // }
      if (file1.name === file2.name && file1.lastModified === file2.lastModified) {
        return true;
      }
      return false;
    }

    let confirmCloseOk: any = null;

    return {
      ...toRefs(state),
      loadingStatus,
      saveStatus,
      uploadSuccessStatus,
      waitUploadFileStatus,
      registerModal,
      openModal,
      // 获取文件
      beforeUpload(file) {
        // 不可重复添加文件
        for (const fileItem of state.fileList) {
          if (isRepetitive(fileItem.file, file)) {
            message.error(`列表中已存在文件：${file.name}`);
            return false;
          }
        }

        const { size, name, uid } = file;

        if (fileSize(size) > props.maxSize) {
          message.error(`${name}超过${props.maxSize}M`);
          return false;
        }
        if (props.maxNumber) {
          if (state.fileList.length === props.maxNumber) {
            message.error(`最多可上传${props.maxNumber}个文件`);
            return false;
          }
        }
        if (!props.multiple) {
          state.fileList = [];
        }
        const acceptArr = name.split('.');
        const accept = `.${acceptArr[acceptArr.length - 1]}`;

        const commonItem = {
          uid,
          size,
          name,
          accept,
          percent: 0,
          openTool: undefined,
          file,
          // 密级
          ...(props.isClassification
            ? {
              classification: undefined,
              securityLimit: undefined,
              isSecurityLimit: false,
            }
            : {}),
        };

        state.fileList = [...state.fileList, commonItem];

        // 单个文件马上上传
        if (!props.multiple) {
          startUpload();
        }
        return false;
      },
      // 计算文件大小
      fileSize,
      // 开始上传
      startUpload,
      // 移除文件
      del(item, index) {
        item?.source?.cancel();
        state.fileList.splice(index, 1);
      },
      // 保存文件
      save,
      allCancel,
      closeFunc: () => {
        if (loadingStatus.value) {
          if (!confirmCloseOk) {
            confirmCloseOk = Modal.confirm({
              content: '有正在上传的文件，关闭将停止上传，是否确认关闭？',
              onOk() {
                allCancel();
                openModal(false);
                confirmCloseOk = null;
              },
              onCancel() {
                confirmCloseOk = null;
              },
            });
          }

          return false;
        }
        return true;
      },
      clearFile,
      visibleChange(visible) {
        console.log('测试🚀🚀🚀🚀🚀🚀', visible);
        if (!visible) {
          clearFile();
        }
      },
      // 密级选择
      classificationChange(fileItem, e) {
        const classificationItem = state.classificationOptions.find((item) => item.id === e);
        fileItem.errorClassification = false;
        fileItem.classificationItem = {
          ...classificationItem,
          isSecurityLimit: classificationItem?.isSecurityLimit
            ? classificationItem.isSecurityLimit
            : false,
        };
        fileItem.isSecurityLimit = classificationItem?.isSecurityLimit;
        fileItem.securityLimit = null;
      },
      // 保密期限选择
      securityLimitChange(fileItem) {
        fileItem.errorSecurityLimit = false;
        //
      },
    };
  },
});
</script>

<style scoped lang="less">
  .top-wrap {
    display: flex;
    align-items: center;
  }

  .info {
    height: 32px;
    line-height: 30px;
    font-size: 13px;
    background: ~`getPrefixVar('primary-color-hover')` ;
    border: 1px solid ~`getPrefixVar('primary-color-active')` ;
    margin-left: 10px;
    padding: 0 10px;
  }

  .list {
    margin-top: 10px;

    thead {
      background: ~`getPrefixVar('primary-color-hover')` ;
      font-weight: 600;
    }

    tbody {
      tr {
        border-bottom: 1px solid #e9ecf2;
      }
    }

    td {
      padding: 10px 14px;
      vertical-align: baseline;
    }
  }

  .del-btn {
    color: ~`getPrefixVar('primary-color')`; ;
    cursor: pointer;
    transition: 0.3s;

    &:hover {
      color: ~`getPrefixVar('primary-color-hover')` ;
    }
  }

  .upload-btn-wrap {
    position: relative;

    .load-progress {
      position: absolute;
      height: 100%;
      left: 0;
      width: 20%;
      background-color: rgba(255, 255, 255, 0.2);
      top: 0;
    }
  }

  .error-tool-message {
    font-size: 12px;
    color: red;
  }

  .select-wrap {
    width: 130px;
  }

  .file-name {
    max-width: 150px;
  }
</style>
