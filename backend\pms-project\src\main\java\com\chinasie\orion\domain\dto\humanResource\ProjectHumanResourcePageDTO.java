package com.chinasie.orion.domain.dto.humanResource;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * ProjectHumanResource VO对象
 *
 * <AUTHOR>
 * @since 2024-04-15 17:57:05
 */
@ApiModel(value = "ProjectHumanResourceDTO对象", description = "人力资源统计统计分页")
@Data
public class ProjectHumanResourcePageDTO extends ObjectVO implements Serializable{

        private int pageNum;
        private int pageSize;

        @ApiModelProperty(value = "开始时间")
        private Date startTime;

        @ApiModelProperty(value = "结束时间")
        private Date endTime;

        @ApiModelProperty(value = "搜索条件")
        private String keyWord;


}
