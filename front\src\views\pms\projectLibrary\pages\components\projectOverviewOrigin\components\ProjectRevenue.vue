<script setup lang="ts">
import { BasicScrollbar } from 'lyra-component-vue3';
import { formatMoney } from '/@/views/pms/utils/utils';
import EmptyView from '/@/views/pms/components/EmptyView.vue';
import SpinView from '/@/views/pms/components/SpinView.vue';
import { Empty } from 'ant-design-vue';
import Api from '/@/api';
import {
  inject, onMounted, ref, Ref,
} from 'vue';

const projectId: string = inject('projectId');
const revenueInfo: Ref<Record<string, any>> = ref({});
const loading: Ref<boolean> = ref(false);

onMounted(() => {
  getRevenueInfo();
});

// 获取营收信息
async function getRevenueInfo() {
  loading.value = true;
  try {
    const result = await new Api('/pms/projectOverviewNew/projectRevenueTotal').fetch({
      projectId,
    }, '', 'GET');
    revenueInfo.value = result || {};
  } finally {
    loading.value = false;
  }
}

</script>

<template>
  <div class="total">
    <div class="item-cell">
      <span>合同总数：</span>
      <span>{{ revenueInfo.contractCount || 0 }}个</span>
    </div>
    <div class="item-cell">
      <span>营收目标总计：</span>
      <span>{{ formatMoney(revenueInfo.targetRevenue) }}元</span>
    </div>
    <div class="item-cell">
      <span>实际营收总计：</span>
      <span>{{ formatMoney(revenueInfo.actualRevenue) }}元</span>
    </div>
    <div class="item-cell">
      <span>待收金额总计：</span>
      <span>{{ formatMoney(revenueInfo.pendRevenue) }}元</span>
    </div>
  </div>
  <spin-view v-if="loading" />
  <BasicScrollbar
    v-else-if="revenueInfo.projectRevenueList?.length"
    style="flex-grow: 1;height: 0"
  >
    <div
      v-for="(item,index) in revenueInfo.projectRevenueList||[]"
      :key="index"
      class="item"
    >
      <div class="item-cell">
        <span>合同编号：</span>
        <span class="name">{{ item['contractNumber'] || '--' }}</span>
      </div>
      <div class="item-cell">
        <span>营收目标：</span>
        <span>{{ formatMoney(item['targetRevenue']) }}元</span>
      </div>
      <div class="item-cell">
        <span>实际营收：</span>
        <span>{{ formatMoney(item['actualRevenue']) }}元</span>
      </div>
      <div class="item-cell">
        <span>待收金额：</span>
        <span>{{ formatMoney(item['pendRevenue']) }}元</span>
      </div>
    </div>
  </BasicScrollbar>
  <empty-view v-else />
</template>

<style scoped lang="less">
.total {
  padding: 10px 15px;
  background-color: #eee;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
}

.item {
  position: relative;
  display: grid;
  grid-template-columns: repeat(4, minmax(0, 1fr));
  gap: 0 10px;
  padding: 10px 0;
  width: 100%;

  & + .item {
    border-top: 1px solid transparent;
  }

  & + .item::before {
    position: absolute;
    top: -2px;
    content: '';
    width: 100%;
    height: 1px;
    background: repeating-linear-gradient(to right,
    #efefe4 0,
    #efefe4 6px,
    transparent 6px,
    transparent 12px);
  }
}

.item-cell {
  display: flex;
  align-items: center;

  > :first-child {
    font-size: 12px;
  }

  > :last-child {
    font-size: 12px;
    font-weight: bold;
  }

  .name {
    font-size: 14px;
    font-weight: normal;
  }
}
</style>
