package com.chinasie.orion.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.api.code.SysCodeApi;
import com.chinasie.orion.bo.PmsMQProducer;
import com.chinasie.orion.constant.ProjectContractMessageNodeEnums;
import com.chinasie.orion.constant.ProjectContractNodePayTypeEnum;
import com.chinasie.orion.constant.ProjectContractPayNodeAuditStatusEnum;
import com.chinasie.orion.constant.ProjectContractPayNodeStatusEnum;
import com.chinasie.orion.domain.dto.ContractPayNodeConfirmAuditDTO;
import com.chinasie.orion.domain.dto.ContractPayNodeConfirmDTO;
import com.chinasie.orion.domain.dto.FileInfoDTO;
import com.chinasie.orion.domain.dto.MessageTodoStatusDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.MessageCenterApi;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ContractPayNodeConfirmNodeRepository;
import com.chinasie.orion.repository.ContractPayNodeConfirmRepository;
import com.chinasie.orion.repository.ContractPayNodeRepository;
import com.chinasie.orion.repository.ProjectContractRepository;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.sdk.util.IdUtils;
import com.chinasie.orion.service.ContractPayNodeConfirmAuditRecordService;
import com.chinasie.orion.service.ContractPayNodeConfirmService;
import com.chinasie.orion.service.DocumentService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.CollectionUtils;
import com.chinasie.orion.util.ResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * ContractPayNodeConfirm 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26 21:44:48
 */
@Service
public class ContractPayNodeConfirmServiceImpl extends OrionBaseServiceImpl<ContractPayNodeConfirmRepository, ContractPayNodeConfirm> implements ContractPayNodeConfirmService {

    @Autowired
    private ContractPayNodeConfirmRepository contractPayNodeConfirmRepository;

    @Autowired
    private ContractPayNodeRepository contractPayNodeRepository;

    @Autowired
    private ContractPayNodeConfirmNodeRepository contractPayNodeConfirmNodeRepository;

    @Autowired
    private SysCodeApi sysCodeApi;

    @Autowired
    private DocumentService documentService;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private ProjectContractRepository projectContractRepository;

    @Resource
    protected PmsMQProducer mqProducer;

    @Resource
    private MessageCenterApi messageCenterApi;

    @Autowired
    private ContractPayNodeConfirmAuditRecordService contractPayNodeConfirmAuditRecordService;

    @Autowired
    private ClassRedisHelper classRedisHelper;
    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public ContractPayNodeDetailVO detail(String id) throws Exception {
        ContractPayNodeDetailVO contractPayNodeDetailVO = new ContractPayNodeDetailVO();
        ContractPayNodeConfirm contractPayNodeConfirm = contractPayNodeConfirmRepository.selectById(id);
        if (contractPayNodeConfirm == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "节点确认信息未找到!");
        }
        ContractPayNodeConfirmVO contractPayNodeConfirmVO = BeanCopyUtils.convertTo(contractPayNodeConfirm, ContractPayNodeConfirmVO::new);
        String auditUserId = contractPayNodeConfirmVO.getAuditUserId();
        if (StringUtils.hasText(auditUserId)) {
            UserVO rspUser = userRedisHelper.getUserById(auditUserId);
            contractPayNodeConfirmVO.setAuditUserName(null == rspUser ? "" : rspUser.getName());
            contractPayNodeConfirmVO.setAuditUserCode(null == rspUser ? "" : rspUser.getCode());
        }

        String submitUserId = contractPayNodeConfirmVO.getSubmitUserId();
        if (StringUtils.hasText(submitUserId)) {
            UserVO rspUser = userRedisHelper.getUserById(submitUserId);
            contractPayNodeConfirmVO.setSubmitUserIdName(null == rspUser ? "" : rspUser.getName());
            contractPayNodeConfirmVO.setSubmitUserIdCode(null == rspUser ? "" : rspUser.getCode());
        }
        List<DocumentVO> documentVOList = documentService.getDocumentList(contractPayNodeConfirmVO.getSubmitId(), null);

        List<ContractPayNodeConfirmNode> contractPayNodeConfirmNodeList = contractPayNodeConfirmNodeRepository.selectList(ContractPayNodeConfirmNode::getConfirmId, id);
        List<ContractPayNode> contractPayNodeList = contractPayNodeRepository.selectBatchIds(contractPayNodeConfirmNodeList.stream().map(ContractPayNodeConfirmNode::getNodeId).collect(Collectors.toList()));
        List<ContractPayNodeVO> contractPayNodeVOList = BeanCopyUtils.convertListTo(contractPayNodeList, ContractPayNodeVO::new);

        contractPayNodeDetailVO.setContractPayNodeConfirmVO(contractPayNodeConfirmVO);
        contractPayNodeDetailVO.setContractPayNodeVOList(contractPayNodeVOList);
        contractPayNodeDetailVO.setDocumentVOList(documentVOList);

        if(StringUtils.hasText(contractPayNodeConfirmVO.getAuditId())){
            List<DocumentVO> auditDocumentVOList = documentService.getDocumentList(contractPayNodeConfirmVO.getAuditId(), null);
            contractPayNodeDetailVO.setAuditDocumentVOList(auditDocumentVOList);
        }


        return contractPayNodeDetailVO;
    }

    /**
     * 新增
     * <p>
     * * @param contractPayNodeConfirmDTO
     */
    @Override
    @Transactional
    public ContractPayNodeConfirmVO create(ContractPayNodeConfirmDTO contractPayNodeConfirmDTO) throws Exception {
        return create(contractPayNodeConfirmDTO, ProjectContractPayNodeAuditStatusEnum.SAVE.getStatus());
    }

    /**
     * 编辑
     * <p>
     * * @param contractPayNodeConfirmDTO
     */
    @Override
    @Transactional
    public Boolean edit(ContractPayNodeConfirmDTO contractPayNodeConfirmDTO) throws Exception {
        return edit(contractPayNodeConfirmDTO, ProjectContractPayNodeAuditStatusEnum.SAVE.getStatus());
    }

    private ContractPayNodeConfirmVO create(ContractPayNodeConfirmDTO contractPayNodeConfirmDTO, Integer status) throws Exception {
        List<String> nodeIdList = contractPayNodeConfirmDTO.getNodeIdList();
        if (CollectionUtils.isBlank(nodeIdList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "节点不能为空!");
        }
        nodeIdList = nodeIdList.stream().distinct().collect(Collectors.toList());
        List<ContractPayNode> contractPayNodeList = contractPayNodeRepository.selectBatchIds(nodeIdList);
        if (CollectionUtils.isBlank(contractPayNodeList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "节点id无效!");
        }

        String contractId = contractPayNodeConfirmDTO.getContractId();
        ProjectContract projectContract = projectContractRepository.selectById(contractId);
        if(projectContract == null){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "未找到项目合同!");
        }
        Map<String, ContractPayNode> contractPayNodeMap = contractPayNodeList.stream().collect(Collectors.toMap(ContractPayNode::getId, Function.identity()));
        nodeIdList.forEach(p -> {
            ContractPayNode contractPayNode = contractPayNodeMap.get(p);
            if (!contractId.equals(contractPayNode.getContractId())) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, contractPayNode.getPayType() + "支付节点和项目合同不对应!");
            }
            if (contractPayNode == null) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, p + "节点id未找到节点数据!");
            }
            if (ProjectContractPayNodeStatusEnum.COMPLETE.getStatus().equals(contractPayNode.getStatus())) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, contractPayNode.getPayType() + "节点支付已完成不能发起节点确认!");
            }
            if (ProjectContractNodePayTypeEnum.GUARANTEE.getCode().equals(contractPayNode.getPayType())) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, ProjectContractNodePayTypeEnum.GUARANTEE.getDescription() + "类型节点不能发起节点确认!");
            }

        });

        Map<String, ContractPayNodeConfirm> payNodeConfirmMap = new HashMap<>();
        List<ContractPayNodeConfirmNode> contractPayNodeConfirmNodeList = contractPayNodeConfirmNodeRepository.selectList(ContractPayNodeConfirmNode::getNodeId, nodeIdList);
        if (!CollectionUtils.isBlank(contractPayNodeConfirmNodeList)) {
            List<String> confirmIds = contractPayNodeConfirmNodeList.stream().map(ContractPayNodeConfirmNode::getConfirmId).collect(Collectors.toList());
            List<ContractPayNodeConfirm> contractPayNodeConfirmList = contractPayNodeConfirmRepository.selectBatchIds(confirmIds);
            if (!CollectionUtils.isBlank(contractPayNodeConfirmList)) {
                Map<String, ContractPayNodeConfirm> contractPayNodeConfirmMap = contractPayNodeConfirmList.stream().collect(Collectors.toMap(ContractPayNodeConfirm::getId, Function.identity()));
                contractPayNodeConfirmNodeList.forEach(p -> {
                    String nodeId = p.getNodeId();
                    String confirmId = p.getConfirmId();
                    payNodeConfirmMap.put(nodeId, contractPayNodeConfirmMap.get(confirmId));
                });
            }
        }

        nodeIdList.forEach(p -> {
            ContractPayNode contractPayNode = contractPayNodeMap.get(p);
            ContractPayNodeConfirm contractPayNodeConfirm = payNodeConfirmMap.get(p);
            if (contractPayNodeConfirm != null) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, contractPayNode.getPayType() + "已经发起过节点确认，不能再次发起节!");
            }
        });

        ResponseDTO<String> responseDTO = sysCodeApi.rulesAndSegmentCreate("ContractPayNodeConfirm", "number", false, "");
        if (ResponseUtils.fail(responseDTO)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, responseDTO.getMessage());
        }
        String number = responseDTO.getResult();
        if (!StringUtils.hasText(number)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "生成项目合同编号失败");
        }
        String submitId = classRedisHelper.getUUID(ContractPayNodeConfirm.class.getSimpleName());
//        String  submitId = IdUtils.orionUUID(ContractPayNodeConfirm.class);
        contractPayNodeConfirmDTO.setSubmitId(submitId);
        ContractPayNodeConfirm contractPayNodeConfirm = BeanCopyUtils.convertTo(contractPayNodeConfirmDTO, ContractPayNodeConfirm::new);
        contractPayNodeConfirm.setNumber(number);
        contractPayNodeConfirm.setSubmitUserId(CurrentUserHelper.getCurrentUserId());
        contractPayNodeConfirm.setSubmitDate(new Date());
        contractPayNodeConfirm.setStatus(status);
        int insert = contractPayNodeConfirmRepository.insert(contractPayNodeConfirm);
        List<ContractPayNodeConfirmNode> contractPayNodeConfirmNodes = new ArrayList<>();
        nodeIdList.forEach(p -> {
            ContractPayNodeConfirmNode contractPayNodeConfirmNode = new ContractPayNodeConfirmNode();
            contractPayNodeConfirmNode.setConfirmId(contractPayNodeConfirm.getId());
            contractPayNodeConfirmNode.setNodeId(p);
            contractPayNodeConfirmNodes.add(contractPayNodeConfirmNode);
        });
        contractPayNodeConfirmNodeRepository.insertBatch(contractPayNodeConfirmNodes);

        List<FileInfoDTO> fileInfoDTOList = contractPayNodeConfirmDTO.getFileInfoDTOList();
        if (!CollectionUtils.isBlank(fileInfoDTOList)) {
            fileInfoDTOList.forEach(p -> {
                p.setDataId(submitId);
            });
            List<FileInfoDTO> insertInfoDTOList = fileInfoDTOList.stream().filter(p -> !StringUtils.hasText(p.getId())).collect(Collectors.toList());

            List<FileInfoDTO> updateInfoDTOList = fileInfoDTOList.stream().filter(p -> StringUtils.hasText(p.getId())).collect(Collectors.toList());
            if(!CollectionUtils.isBlank(insertInfoDTOList)){
                List<String> fileDataIdList = documentService.saveBatchAdd(fileInfoDTOList);
            }

            if(!CollectionUtils.isBlank(updateInfoDTOList)){
                List<FileDTO> updateFileDtoList = BeanCopyUtils.convertListTo(updateInfoDTOList,FileDTO::new);
                documentService.updateBatchDocument(updateFileDtoList);
            }

        }
        ContractPayNodeConfirmVO rsp = BeanCopyUtils.convertTo(contractPayNodeConfirm, ContractPayNodeConfirmVO::new);


        String auditUserId = contractPayNodeConfirmDTO.getAuditUserId();
        if(StringUtils.hasText(auditUserId)){
            if(ProjectContractPayNodeAuditStatusEnum.AUDITING.getStatus().equals(status)){
                sendMessage(auditUserId, projectContract, contractPayNodeConfirm, ProjectContractMessageNodeEnums.PAY_NODE_CONFIRM_AUDITED);
            }
        }
        return rsp;
    }

    private Boolean edit(ContractPayNodeConfirmDTO contractPayNodeConfirmDTO, Integer status) throws Exception {
        List<String> nodeIdList = contractPayNodeConfirmDTO.getNodeIdList();
        if (CollectionUtils.isBlank(nodeIdList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "节点不能为空!");
        }

        ContractPayNodeConfirm nodeConfirm = contractPayNodeConfirmRepository.selectById(contractPayNodeConfirmDTO.getId());
        String oldSubmitId = nodeConfirm.getSubmitId();
        String newSubmitId = nodeConfirm.getSubmitId();
        if (!(ProjectContractPayNodeAuditStatusEnum.SAVE.getStatus().equals(nodeConfirm.getStatus()) || ProjectContractPayNodeAuditStatusEnum.REJECT.getStatus().equals(nodeConfirm.getStatus()))) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "当前节点确认记录审核状态不是（已保存、已驳回）不能修改!");
        }
        nodeIdList = nodeIdList.stream().distinct().collect(Collectors.toList());
        List<ContractPayNode> contractPayNodeList = contractPayNodeRepository.selectBatchIds(nodeIdList);
        if (CollectionUtils.isBlank(contractPayNodeList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "节点id无效!");
        }

        String contractId = contractPayNodeConfirmDTO.getContractId();
        ProjectContract projectContract = projectContractRepository.selectById(contractId);
        if(projectContract == null){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "未找到项目合同!");
        }
        Map<String, ContractPayNode> contractPayNodeMap = contractPayNodeList.stream().collect(Collectors.toMap(ContractPayNode::getId, Function.identity()));
        nodeIdList.forEach(p -> {
            ContractPayNode contractPayNode = contractPayNodeMap.get(p);
            if (!contractId.equals(contractPayNode.getContractId())) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, contractPayNode.getPayType() + "支付节点和项目合同不对应!");
            }
            if (contractPayNode == null) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, p + "节点id未找到节点数据!");
            }
            if (ProjectContractPayNodeStatusEnum.COMPLETE.getStatus().equals(contractPayNode.getStatus())) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, contractPayNode.getPayType() + "节点支付已完成不能发起节点确认!");
            }
            if (ProjectContractNodePayTypeEnum.GUARANTEE.getCode().equals(contractPayNode.getPayType())) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, ProjectContractNodePayTypeEnum.GUARANTEE.getDescription() + "类型节点不能发起节点确认!");
            }

        });

        Map<String, ContractPayNodeConfirm> payNodeConfirmMap = new HashMap<>();
        List<ContractPayNodeConfirmNode> contractPayNodeConfirmNodeList = contractPayNodeConfirmNodeRepository.selectList(ContractPayNodeConfirmNode::getNodeId, nodeIdList);
        if (!CollectionUtils.isBlank(contractPayNodeConfirmNodeList)) {
            List<String> confirmIds = contractPayNodeConfirmNodeList.stream().map(ContractPayNodeConfirmNode::getConfirmId).collect(Collectors.toList());
            List<ContractPayNodeConfirm> contractPayNodeConfirmList = contractPayNodeConfirmRepository.selectBatchIds(confirmIds);
            if (!CollectionUtils.isBlank(contractPayNodeConfirmList)) {
                Map<String, ContractPayNodeConfirm> contractPayNodeConfirmMap = contractPayNodeConfirmList.stream().collect(Collectors.toMap(ContractPayNodeConfirm::getId, Function.identity()));
                contractPayNodeConfirmNodeList.forEach(p -> {
                    String nodeId = p.getNodeId();
                    String confirmId = p.getConfirmId();
                    payNodeConfirmMap.put(nodeId, contractPayNodeConfirmMap.get(confirmId));
                });
            }
        }

        nodeIdList.forEach(p -> {
            ContractPayNode contractPayNode = contractPayNodeMap.get(p);
            ContractPayNodeConfirm contractPayNodeConfirm = payNodeConfirmMap.get(p);
            if (contractPayNodeConfirm != null && !contractPayNodeConfirmDTO.getId().equals(contractPayNodeConfirm.getId())) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, contractPayNode.getPayType() + "已经发起过节点确认，不能再次发起节!");
            }
        });

        if(ProjectContractPayNodeAuditStatusEnum.AUDITING.getStatus().intValue() == status.intValue()
                && ProjectContractPayNodeAuditStatusEnum.REJECT.getStatus().intValue() == nodeConfirm.getStatus().intValue()){
            newSubmitId = classRedisHelper.getUUID(ContractPayNodeConfirm.class.getSimpleName());
//            newSubmitId = IdUtils.orionUUID(ContractPayNodeConfirm.class);
        }
        contractPayNodeConfirmDTO.setSubmitId(newSubmitId);
        ContractPayNodeConfirm contractPayNodeConfirm = BeanCopyUtils.convertTo(contractPayNodeConfirmDTO, ContractPayNodeConfirm::new);
        List<ContractPayNodeConfirmNode> contractPayNodeConfirmNodes = new ArrayList<>();
        List<ContractPayNodeConfirmNode> confirmNodes = contractPayNodeConfirmNodeRepository.selectList(ContractPayNodeConfirmNode::getConfirmId, contractPayNodeConfirm.getId());
        List<String> oldNodeIds = confirmNodes.stream().map(ContractPayNodeConfirmNode::getNodeId).collect(Collectors.toList());
        nodeIdList.forEach(p -> {
            if (!oldNodeIds.contains(p)) {
                ContractPayNodeConfirmNode contractPayNodeConfirmNode = new ContractPayNodeConfirmNode();
                contractPayNodeConfirmNode.setConfirmId(contractPayNodeConfirm.getId());
                contractPayNodeConfirmNode.setNodeId(p);
                contractPayNodeConfirmNodes.add(contractPayNodeConfirmNode);
            }
        });
        final List<String> newNodeIds = nodeIdList;
        List<ContractPayNodeConfirmNode> removeConfirmNodes = confirmNodes.stream().filter(p -> !newNodeIds.contains(p.getNodeId())).collect(Collectors.toList());
        if (!CollectionUtils.isBlank(removeConfirmNodes)) {
            List<String> removeNodeIds = removeConfirmNodes.stream().map(ContractPayNodeConfirmNode::getId).collect(Collectors.toList());
            contractPayNodeConfirmNodeRepository.deleteBatchIds(removeNodeIds);
        }
        contractPayNodeConfirmNodeRepository.insertBatch(contractPayNodeConfirmNodes);
        contractPayNodeConfirm.setStatus(status);
        contractPayNodeConfirm.setSubmitId(newSubmitId);
        contractPayNodeConfirm.setAuditDate(null);
        contractPayNodeConfirm.setActualAuditUserId("");
        int update = contractPayNodeConfirmRepository.updateById(contractPayNodeConfirm);

        //节点确认材料上传
        List<FileInfoDTO> fileInfoDTOList = contractPayNodeConfirmDTO.getFileInfoDTOList();
        if(newSubmitId.equals(oldSubmitId)){
            documentService.deleteBatchFileByDataIds(List.of(oldSubmitId));
            if(!CollectionUtils.isBlank(fileInfoDTOList)){
                documentService.saveBatchAdd(fileInfoDTOList);
            }
        }
        else{
            final String newSubmitIdTemp = newSubmitId;
            fileInfoDTOList.forEach(p -> {
                p.setDataId(newSubmitIdTemp);
            });
            documentService.saveBatchAdd(fileInfoDTOList);
        }



        /*
        List<FileInfoDTO> fileInfoDTOList1 = documentService.getFileInfoList(oldSubmitId);
        if (!(CollectionUtils.isBlank(fileInfoDTOList) && CollectionUtils.isBlank(fileInfoDTOList1))) {
            if (CollectionUtils.isBlank(fileInfoDTOList)) {
                documentService.deleteBatchFile(fileInfoDTOList1.stream().map(FileInfoDTO::getId).collect(Collectors.toList()));
            } else if (CollectionUtils.isBlank(fileInfoDTOList1)) {
                fileInfoDTOList.forEach(p -> {
                    p.setDataId(newSubmitIdTemp);
                });
                documentService.saveBatchAdd(fileInfoDTOList);
            } else {
                List<FileInfoDTO> updateContractFile = fileInfoDTOList.stream().filter(p -> StringUtils.hasText(p.getId())).collect(Collectors.toList());
                List<FileInfoDTO> insertContractFile = fileInfoDTOList.stream().filter(p -> !StringUtils.hasText(p.getId())).collect(Collectors.toList());
                List<String> contractFileIdList = updateContractFile.stream().map(FileInfoDTO::getId).collect(Collectors.toList());
                List<String> deleteContractFileIds = fileInfoDTOList1.stream().filter(p -> !contractFileIdList.contains(p.getId())).map(FileInfoDTO::getId).collect(Collectors.toList());
                if (!CollectionUtils.isBlank(updateContractFile)) {
                    updateContractFile.forEach(p -> {
                        p.setDataId(contractPayNodeConfirm.getSubmitId());
                    });
                    List<FileDto> fileDtoList = BeanCopyUtils.convertListTo(updateContractFile, FileDto::new);
                    documentService.updateBatchDocument(fileDtoList);
                }

                if (!CollectionUtils.isBlank(deleteContractFileIds)) {
                    documentService.deleteBatchFile(deleteContractFileIds);
                }

                if (!CollectionUtils.isBlank(insertContractFile)) {
                    insertContractFile.forEach(p -> {
                        p.setDataId(contractPayNodeConfirm.getSubmitId());
                    });
                    documentService.saveBatchAdd(insertContractFile);
                }
            }

        }
        */
        String auditUserId = contractPayNodeConfirmDTO.getAuditUserId();
        if(StringUtils.hasText(auditUserId)){
            if(ProjectContractPayNodeAuditStatusEnum.AUDITING.getStatus().equals(status)){
                sendMessage(auditUserId, projectContract, contractPayNodeConfirm, ProjectContractMessageNodeEnums.PAY_NODE_CONFIRM_AUDITED);
            }
        }
        return SqlHelper.retBool(update);
    }

    /**
     * 提交
     * <p>
     * * @param contractPayNodeConfirmDTO
     */
    @Override
    public Boolean submit(ContractPayNodeConfirmDTO contractPayNodeConfirmDTO) throws Exception {
        String id = contractPayNodeConfirmDTO.getId();
        if (StringUtils.hasText(id)) {
            return edit(contractPayNodeConfirmDTO, ProjectContractPayNodeAuditStatusEnum.AUDITING.getStatus());
        } else {
            create(contractPayNodeConfirmDTO, ProjectContractPayNodeAuditStatusEnum.AUDITING.getStatus());
            return true;
        }

    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        if (CollectionUtils.isBlank(ids)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "节点id不能为空!");
        }
        List<ContractPayNodeConfirm> list = contractPayNodeConfirmRepository.selectBatchIds(ids);
        if (!CollectionUtils.isBlank(list)) {
            List<ContractPayNodeConfirm> noDeleteList = list.stream().filter(p -> !(p.getStatus().equals(ProjectContractPayNodeAuditStatusEnum.SAVE.getStatus()) || p.getStatus().equals(ProjectContractPayNodeAuditStatusEnum.REJECT.getStatus()))).collect(Collectors.toList());
            if (!CollectionUtils.isBlank(noDeleteList)) {
                throw new PMSException(PMSErrorCode.KMS_EFFECT_DATA, "节点状态不是已保存或已驳回不能删除!");
            }
        }
        int delete = contractPayNodeConfirmRepository.deleteBatchIds(ids);
        LambdaQueryWrapper<ContractPayNodeConfirmNode> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.in(ContractPayNodeConfirmNode::getConfirmId, ids);
        contractPayNodeConfirmNodeRepository.delete(lambdaQueryWrapper);
        return SqlHelper.retBool(delete);
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public Page<ContractPayNodeConfirmListVO> pages(Page<ContractPayNodeConfirmDTO> pageRequest) throws Exception {
        Page<ContractPayNodeConfirm> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ContractPayNodeConfirm::new));
        ContractPayNodeConfirm contractPayNodeConfirm = realPageRequest.getQuery();
        LambdaQueryWrapper<ContractPayNodeConfirm> confirmLambdaQueryWrapper = new LambdaQueryWrapper<>();

        if (contractPayNodeConfirm == null || !StringUtils.hasText(contractPayNodeConfirm.getContractId())) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "项目合同id不能为空!");
        }
        confirmLambdaQueryWrapper.eq(ContractPayNodeConfirm::getContractId, contractPayNodeConfirm.getContractId());
        PageResult<ContractPayNodeConfirm> page = contractPayNodeConfirmRepository.selectPage(realPageRequest, confirmLambdaQueryWrapper);

        Page<ContractPayNodeConfirmListVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ContractPayNodeConfirmListVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ContractPayNodeConfirmListVO::new);
        if (!CollectionUtils.isBlank(vos)) {
            List<String> confirmIds = vos.stream().map(ContractPayNodeConfirmListVO::getId).collect(Collectors.toList());
            List<ContractPayNodeConfirmNode> contractPayNodeConfirmNodeList = contractPayNodeConfirmNodeRepository.selectList(ContractPayNodeConfirmNode::getConfirmId, confirmIds);

            Map<String, List<ContractPayNodeConfirmNode>> nodeConfirmNodeMap = new HashMap<>();
            List<ContractPayNode> contractPayNodeList = new ArrayList<>();
            if (!CollectionUtils.isBlank(contractPayNodeConfirmNodeList)) {
                List<String> nodeIds = contractPayNodeConfirmNodeList.stream().map(ContractPayNodeConfirmNode::getNodeId).collect(Collectors.toList());
                contractPayNodeList = contractPayNodeRepository.selectList(ContractPayNode::getId, nodeIds);
                nodeConfirmNodeMap = contractPayNodeConfirmNodeList.stream().collect(Collectors.groupingBy(ContractPayNodeConfirmNode::getConfirmId));

            }
            List<String> auditUserIds = vos.stream().map(ContractPayNodeConfirmListVO::getAuditUserId).collect(Collectors.toList());
            List<String> submitUserIds = vos.stream().map(ContractPayNodeConfirmListVO::getSubmitUserId).collect(Collectors.toList());
            List<String> userIds = new ArrayList<>();
            userIds.addAll(auditUserIds);
            userIds.addAll(submitUserIds);
            Map<String, UserVO> userVOMap = new HashMap<>();
            if (!CollectionUtils.isBlank(userIds)) {
                userVOMap = userRedisHelper.getUserMapByUserIds(userIds);
            }
            for (ContractPayNodeConfirmListVO nodeConfirmVO : vos) {
                List<ContractPayNodeConfirmNode> confirmNodes = nodeConfirmNodeMap.get(nodeConfirmVO.getId());
                if (!CollectionUtils.isBlank(confirmNodes)) {
                    List<String> nodeIds = confirmNodes.stream().map(ContractPayNodeConfirmNode::getNodeId).collect(Collectors.toList());
                    nodeConfirmVO.setContractPayNodeList(contractPayNodeList.stream().filter(p -> nodeIds.contains(p.getId())).collect(Collectors.toList()));
                }
                String auditUserId = nodeConfirmVO.getAuditUserId();
                if (StringUtils.hasText(auditUserId)) {
                    UserVO rspUser = userVOMap.get(auditUserId);
                    nodeConfirmVO.setAuditUserName(null == rspUser ? "" : rspUser.getName());
                    nodeConfirmVO.setAuditUserCode(null == rspUser ? "" : rspUser.getCode());
                }

                String submitUserId = nodeConfirmVO.getSubmitUserId();
                if (StringUtils.hasText(submitUserId)) {
                    UserVO rspUser = userVOMap.get(submitUserId);
                    nodeConfirmVO.setSubmitUserIdName(null == rspUser ? "" : rspUser.getName());
                    nodeConfirmVO.setSubmitUserIdCode(null == rspUser ? "" : rspUser.getCode());
                }
            }

        }

        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public List<ContractPayNodeConfirmSelectVO> payNodeListByContractId(String contractId) throws Exception {
        List<ContractPayNode> contractPayNodeList = contractPayNodeRepository.selectList(ContractPayNode::getContractId, contractId);
        List<String> noSelectNodeIds = new ArrayList<>();
        if (CollectionUtils.isBlank(contractPayNodeList)) {
            return null;
        }
        List<String> nodeLists = contractPayNodeList.stream().map(ContractPayNode::getId).collect(Collectors.toList());
        List<ContractPayNodeConfirmNode> contractPayNodeConfirmNodeList = contractPayNodeConfirmNodeRepository.selectList(ContractPayNodeConfirmNode::getNodeId, nodeLists);
        if (!CollectionUtils.isBlank(contractPayNodeConfirmNodeList)) {
            List<String> confirmIds = contractPayNodeConfirmNodeList.stream().map(ContractPayNodeConfirmNode::getConfirmId).collect(Collectors.toList());
            List<ContractPayNodeConfirm> contractPayNodeConfirmList = contractPayNodeConfirmRepository.selectBatchIds(confirmIds);
            if (!CollectionUtils.isBlank(contractPayNodeConfirmNodeList)) {
                List<String> confirmIdList = contractPayNodeConfirmList.stream().map(ContractPayNodeConfirm::getId).collect(Collectors.toList());
                List<ContractPayNodeConfirmNode> contractPayNodeConfirmNodes = contractPayNodeConfirmNodeList.stream().filter(p -> confirmIdList.contains(p.getConfirmId())).collect(Collectors.toList());
                if (!CollectionUtils.isBlank(contractPayNodeConfirmNodes)) {
                    noSelectNodeIds.addAll(contractPayNodeConfirmNodes.stream().map(ContractPayNodeConfirmNode::getNodeId).collect(Collectors.toList()));
                }

            }
        }
        List<ContractPayNodeConfirmSelectVO> vos = BeanCopyUtils.convertListTo(contractPayNodeList, ContractPayNodeConfirmSelectVO::new);
        for (ContractPayNodeConfirmSelectVO confirmSelectVO : vos) {
            boolean isConfirm = true;
            if (ProjectContractPayNodeStatusEnum.COMPLETE.getStatus().equals(confirmSelectVO.getStatus())) {
                isConfirm = false;
            }
//            if (ProjectContractNodePayTypeEnum.GUARANTEE.getCode().equals(confirmSelectVO.getPayType())) {
//                isConfirm = false;
//            }
            if (noSelectNodeIds.contains(confirmSelectVO.getId())) {
                isConfirm = false;
            }
            confirmSelectVO.setIsConfirm(isConfirm);
        }
        return vos;
    }

    @Override
    public List<ContractPayNodeConfirmSelectVO> payNodeStatusConfirmListByContractId(String contractId) throws Exception {
        List<ContractPayNode> contractPayNodeList = contractPayNodeRepository.selectList(ContractPayNode::getContractId, contractId);
        List<String> noSelectNodeIds = new ArrayList<>();
        if (CollectionUtils.isBlank(contractPayNodeList)) {
            return null;
        }
        List<ContractPayNodeConfirmSelectVO> vos = BeanCopyUtils.convertListTo(contractPayNodeList, ContractPayNodeConfirmSelectVO::new);
        vos.forEach( p->{
            p.setIsConfirm(false);
        });

        LambdaQueryWrapper<ContractPayNodeConfirm> confirmLambdaQueryWrapper = new LambdaQueryWrapper<>();
        confirmLambdaQueryWrapper.eq(ContractPayNodeConfirm :: getContractId,contractId);
        confirmLambdaQueryWrapper.eq(ContractPayNodeConfirm :: getStatus,ProjectContractPayNodeAuditStatusEnum.AUDITED.getStatus());
        List<ContractPayNodeConfirm> contractPayNodeConfirmList = contractPayNodeConfirmRepository.selectList(confirmLambdaQueryWrapper);
        if (!CollectionUtils.isBlank(contractPayNodeConfirmList)) {
            List<String> confirmLists = contractPayNodeConfirmList.stream().map(ContractPayNodeConfirm :: getId).collect(Collectors.toList());
            List<ContractPayNodeConfirmNode> contractPayNodeConfirmNodeList = contractPayNodeConfirmNodeRepository.selectList(ContractPayNodeConfirmNode::getConfirmId, confirmLists);
            if (!CollectionUtils.isBlank(contractPayNodeConfirmNodeList)) {
               List<String> nodeLists =  contractPayNodeConfirmNodeList.stream().map(ContractPayNodeConfirmNode :: getNodeId).collect(Collectors.toList());
                vos.forEach( p->{
                    if(nodeLists.contains(p.getId())){
                        p.setIsConfirm(true);
                    }
                });
            }
        }
        return vos;
    }

    @Override
    public List<ContractPayNodeConfirmSelectVO> payNodeListById(String id) throws Exception {
        ContractPayNodeConfirm nodeConfirm = contractPayNodeConfirmRepository.selectById(id);
        List<ContractPayNodeConfirmNode> contractPayNodeConfirmNodeList = contractPayNodeConfirmNodeRepository.selectList(ContractPayNodeConfirmNode::getConfirmId, id);
        if (!CollectionUtils.isBlank(contractPayNodeConfirmNodeList)) {
            List<String> selectNodeIds = contractPayNodeConfirmNodeList.stream().map(ContractPayNodeConfirmNode::getNodeId).collect(Collectors.toList());
            List<ContractPayNode> contractPayNodes = contractPayNodeRepository.selectBatchIds(selectNodeIds);
            if (!CollectionUtils.isBlank(contractPayNodes)) {
                List<String> contractIds = contractPayNodes.stream().map(ContractPayNode::getContractId).distinct().collect(Collectors.toList());
                if (!CollectionUtils.isBlank(contractIds)) {
                    if (contractIds.size() > 1) {
                        throw new PMSException(PMSErrorCode.PMS_ERR, "一条支付节点确认记录关联多个合同!");
                    }
                    String contractId = contractIds.get(0);
                    List<ContractPayNode> contractPayNodeList = contractPayNodeRepository.selectList(ContractPayNode::getContractId, contractId);
                    List<String> noSelectNodeIds = new ArrayList<>();
                    if (CollectionUtils.isBlank(contractPayNodeList)) {
                        List<String> nodeLists = contractPayNodeList.stream().map(ContractPayNode::getId).collect(Collectors.toList());
                        List<ContractPayNodeConfirmNode> contractPayNodeConfirmNodes = contractPayNodeConfirmNodeRepository.selectList(ContractPayNodeConfirmNode::getNodeId, nodeLists);
                        if (!CollectionUtils.isBlank(contractPayNodeConfirmNodes)) {
                            contractPayNodeConfirmNodes = contractPayNodeConfirmNodes.stream().filter(p -> !p.getConfirmId().equals(id)).collect(Collectors.toList());
                            List<String> confirmIds = contractPayNodeConfirmNodes.stream().map(ContractPayNodeConfirmNode::getConfirmId).collect(Collectors.toList());
                            List<ContractPayNodeConfirm> contractPayNodeConfirmList = contractPayNodeConfirmRepository.selectBatchIds(confirmIds);
                            if (!CollectionUtils.isBlank(contractPayNodeConfirmList)) {
                                List<String> confirmIdList = contractPayNodeConfirmList.stream().map(ContractPayNodeConfirm::getId).collect(Collectors.toList());
                                List<ContractPayNodeConfirmNode> confirmNodes = contractPayNodeConfirmNodes.stream().filter(p -> confirmIdList.contains(p.getConfirmId())).collect(Collectors.toList());
                                if (!CollectionUtils.isBlank(confirmNodes)) {
                                    noSelectNodeIds.addAll(confirmNodes.stream().map(ContractPayNodeConfirmNode::getNodeId).collect(Collectors.toList()));
                                }

                            }
                        }
                        List<ContractPayNodeConfirmSelectVO> vos = BeanCopyUtils.convertListTo(contractPayNodeList, ContractPayNodeConfirmSelectVO::new);
                        for (ContractPayNodeConfirmSelectVO confirmSelectVO : vos) {
                            boolean isConfirm = true;
                            if (ProjectContractPayNodeStatusEnum.COMPLETE.getStatus().equals(confirmSelectVO.getStatus())) {
                                isConfirm = false;
                            }
//                            if (ProjectContractNodePayTypeEnum.GUARANTEE.getCode().equals(confirmSelectVO.getPayType())) {
//                                isConfirm = false;
//                            }
                            if (noSelectNodeIds.contains(confirmSelectVO.getId())) {
                                isConfirm = false;
                            }
                            confirmSelectVO.setIsConfirm(isConfirm);
                        }
                        return vos;
                    }
                }
            }
        }

        return null;
    }

    @Override
    public Boolean agree(ContractPayNodeConfirmAuditDTO confirmAuditDTO) throws Exception {
        String id = confirmAuditDTO.getId();
        ContractPayNodeConfirm nodeConfirm = contractPayNodeConfirmRepository.selectById(id);
        if (nodeConfirm == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "节点确认记录不存在!");
        }
        if (!ProjectContractPayNodeAuditStatusEnum.AUDITING.getStatus().equals((nodeConfirm.getStatus()))) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "节点确认记录状态不是审核中，不能同意操作!");
        }
        String auditUserId = nodeConfirm.getAuditUserId();
        if (StringUtils.hasText(auditUserId)) {
            if (!auditUserId.equals(CurrentUserHelper.getCurrentUserId())) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_NOT_ROLE, "当前用户不是审核人，不能进行审核操作!");
            }
        }
        String contractId = nodeConfirm.getContractId();
        ProjectContract projectContract = projectContractRepository.selectById(contractId);
        if(projectContract == null){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "未找到项目合同!");
        }
        nodeConfirm.setStatus(ProjectContractPayNodeAuditStatusEnum.AUDITED.getStatus());

        ContractPayNodeConfirmAuditRecord contractPayNodeConfirmAuditRecord = new ContractPayNodeConfirmAuditRecord();
        contractPayNodeConfirmAuditRecord.setAuditComment(confirmAuditDTO.getComment());
        contractPayNodeConfirmAuditRecord.setAuditDate(new Date());
        contractPayNodeConfirmAuditRecord.setAuditUserId(CurrentUserHelper.getCurrentUserId());
        contractPayNodeConfirmAuditRecord.setStatus(ProjectContractPayNodeAuditStatusEnum.AUDITED.getStatus());
        contractPayNodeConfirmAuditRecord.setConfirmId(id);
        contractPayNodeConfirmAuditRecordService.save(contractPayNodeConfirmAuditRecord);
        nodeConfirm.setAuditDate(new Date());
        nodeConfirm.setAuditDesc(confirmAuditDTO.getComment());
        nodeConfirm.setActualAuditUserId(CurrentUserHelper.getCurrentUserId());
        nodeConfirm.setAuditId(contractPayNodeConfirmAuditRecord.getId());
        contractPayNodeConfirmRepository.updateById(nodeConfirm);

        List<FileInfoDTO> fileInfoDTOList = confirmAuditDTO.getFileInfoDTOList();
        if (!CollectionUtils.isBlank(fileInfoDTOList)) {
            fileInfoDTOList.forEach(p -> {
                p.setDataId(contractPayNodeConfirmAuditRecord.getId());
            });
            List<String> fileDataIdList = documentService.saveBatchAdd(fileInfoDTOList);
        }


        if(StringUtils.hasText(nodeConfirm.getSubmitUserId())){
            sendMessage(nodeConfirm.getSubmitUserId(), projectContract, nodeConfirm, ProjectContractMessageNodeEnums.PAY_NODE_CONFIRM_COMMIT);
        }
        ResponseDTO<?> responseDTO = messageCenterApi.todoStatus(MessageTodoStatusDTO.builder()
                .userId(nodeConfirm.getAuditUserId())
                .businessId(nodeConfirm.getId())
                .build());
        return true;
    }

    @Override
    public Boolean reject(ContractPayNodeConfirmAuditDTO confirmAuditDTO) throws Exception {
        String id = confirmAuditDTO.getId();
        ContractPayNodeConfirm nodeConfirm = contractPayNodeConfirmRepository.selectById(id);
        if (nodeConfirm == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "节点确认记录不存在!");
        }
        if (!ProjectContractPayNodeAuditStatusEnum.AUDITING.getStatus().equals((nodeConfirm.getStatus()))) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "节点确认记录状态不是审核中，不能驳回操作!");
        }
        String auditUserId = nodeConfirm.getAuditUserId();
        if (StringUtils.hasText(auditUserId)) {
            if (!auditUserId.equals(CurrentUserHelper.getCurrentUserId())) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_NOT_ROLE, "当前用户不是审核人，不能进行审核操作!");
            }
        }

        String contractId = nodeConfirm.getContractId();
        ProjectContract projectContract = projectContractRepository.selectById(contractId);
        if(projectContract == null){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "未找到项目合同!");
        }

        nodeConfirm.setStatus(ProjectContractPayNodeAuditStatusEnum.REJECT.getStatus());

        ContractPayNodeConfirmAuditRecord contractPayNodeConfirmAuditRecord = new ContractPayNodeConfirmAuditRecord();
        contractPayNodeConfirmAuditRecord.setAuditComment(confirmAuditDTO.getComment());
        contractPayNodeConfirmAuditRecord.setAuditDate(new Date());
        contractPayNodeConfirmAuditRecord.setAuditUserId(CurrentUserHelper.getCurrentUserId());
        contractPayNodeConfirmAuditRecord.setStatus(ProjectContractPayNodeAuditStatusEnum.REJECT.getStatus());
        contractPayNodeConfirmAuditRecord.setConfirmId(id);
        contractPayNodeConfirmAuditRecordService.save(contractPayNodeConfirmAuditRecord);

        nodeConfirm.setAuditDate(new Date());
        nodeConfirm.setAuditDesc(confirmAuditDTO.getComment());
        nodeConfirm.setActualAuditUserId(CurrentUserHelper.getCurrentUserId());
        nodeConfirm.setAuditId(contractPayNodeConfirmAuditRecord.getId());
        contractPayNodeConfirmRepository.updateById(nodeConfirm);

        List<FileInfoDTO> fileInfoDTOList = confirmAuditDTO.getFileInfoDTOList();
        if (!CollectionUtils.isBlank(fileInfoDTOList)) {
            fileInfoDTOList.forEach(p -> {
                p.setDataId(contractPayNodeConfirmAuditRecord.getId());
            });
            List<String> fileDataIdList = documentService.saveBatchAdd(fileInfoDTOList);
        }


        if(StringUtils.hasText(nodeConfirm.getSubmitUserId())){
            sendMessage(nodeConfirm.getSubmitUserId(), projectContract, nodeConfirm, ProjectContractMessageNodeEnums.PAY_NODE_CONFIRM_REJECT);
        }

        ResponseDTO<?> responseDTO = messageCenterApi.todoStatus(MessageTodoStatusDTO.builder()
                .userId(nodeConfirm.getAuditUserId())
                .businessId(nodeConfirm.getId())
                .build());
        return true;
    }

    private void sendMessage(String auditUserId, ProjectContract projectContract,ContractPayNodeConfirm nodeConfirm, ProjectContractMessageNodeEnums  projectContractMessageNodeEnums){
        if(StringUtils.hasText(auditUserId)){
            Map<String, Object> messageMap = new HashMap<>();
            messageMap.put("$contractNumber$", projectContract.getNumber());
            messageMap.put("$confirmNumber$", nodeConfirm.getNumber());
            List<String> recipientIdList = new ArrayList<>();
            recipientIdList.add(auditUserId);

            Map<String, Object> businessDataMap = new HashMap<>();
            businessDataMap.put("bizCode", nodeConfirm.getId());
            businessDataMap.put("bizName", nodeConfirm.getNumber());
            businessDataMap.put("bizTypeCode", projectContract.getId());
            businessDataMap.put("bizTypeName", projectContract.getName());
            SendMessageDTO sendMsc = SendMessageDTO.builder()
                    .businessData(JSONObject.toJSONString(businessDataMap))
                    .businessId(nodeConfirm.getId())
                    .todoStatus(0)
                    .todoType(0)
                    .urgencyLevel(0)
                    .messageMap(messageMap)
                    .businessNodeCode(projectContractMessageNodeEnums.getCode())
                    .titleMap(messageMap)
                    //.messageUrl(String.format(JUMP_URL, project.getId()))
                    .messageUrl(projectContractMessageNodeEnums.getMessageUrl())
                    .messageUrlName(projectContractMessageNodeEnums.getMessageUrlName())
                    .recipientIdList(recipientIdList)
                    .senderId(CurrentUserHelper.getCurrentUserId())
                    .senderTime(new Date())
                    .build();
            mqProducer.sendPmsMessage(sendMsc);
        }
    }
}
