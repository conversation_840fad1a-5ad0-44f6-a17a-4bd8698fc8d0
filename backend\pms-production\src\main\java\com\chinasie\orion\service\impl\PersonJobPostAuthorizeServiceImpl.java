package com.chinasie.orion.service.impl;





import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.bo.LyraFileBO;
import com.chinasie.orion.constant.JobPostAuthorizeEnum;
import com.chinasie.orion.domain.entity.PersonJobPostAuthorize;
import com.chinasie.orion.domain.dto.PersonJobPostAuthorizeDTO;
import com.chinasie.orion.domain.entity.PersonJobPostEqu;
import com.chinasie.orion.domain.entity.PersonTrainInfoRecord;
import com.chinasie.orion.domain.vo.PersonJobPostAuthorizeVO;


import com.chinasie.orion.domain.vo.PersonJobPostEquVO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileTreeVO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.file.api.service.FileApiService;
import com.chinasie.orion.mybatis.domain.entity.LyraEntity;
import com.chinasie.orion.service.PersonJobPostAuthorizeService;
import com.chinasie.orion.repository.PersonJobPostAuthorizeMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.PersonJobPostEquService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * PersonJobPostAuthorize 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-22 16:30:22
 */
@Service
@Slf4j
public class PersonJobPostAuthorizeServiceImpl extends  OrionBaseServiceImpl<PersonJobPostAuthorizeMapper, PersonJobPostAuthorize>   implements PersonJobPostAuthorizeService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private PersonJobPostEquService postEquService;
    @Autowired
    private FileApiService fileApiService;

    @Autowired
    private PersonJobPostAuthorizeMapper personJobPostAuthorizeMapper;
    @Autowired
    private LyraFileBO fileApi;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  PersonJobPostAuthorizeVO detail(String id,String pageCode) throws Exception {
        PersonJobPostAuthorize personJobPostAuthorize =this.getById(id);
        PersonJobPostAuthorizeVO result = BeanCopyUtils.convertTo(personJobPostAuthorize,PersonJobPostAuthorizeVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param personJobPostAuthorizeDTO
     */
    @Override
    public  String create(PersonJobPostAuthorizeDTO personJobPostAuthorizeDTO) throws Exception {
        PersonJobPostAuthorize personJobPostAuthorize =BeanCopyUtils.convertTo(personJobPostAuthorizeDTO,PersonJobPostAuthorize::new);
        //状态默认授权
        personJobPostAuthorize.setAuthorizeStatus(JobPostAuthorizeEnum.FINISH.getStatus());
        personJobPostAuthorize.setAuthorizeStatusName(JobPostAuthorizeEnum.FINISH.getDesc());
        personJobPostAuthorize.setIsEquivalent(Boolean.FALSE);
        this.save(personJobPostAuthorize);
        //保存附件
        List<FileDTO> attachments = personJobPostAuthorizeDTO.getFileDTOList();
        if (!CollectionUtils.isEmpty(attachments)) {
            attachments.forEach(a -> {
                a.setDataId(personJobPostAuthorize.getId());
                a.setDataType("PersonJobPostAuthorize");
            });
            try {
                List<String> ids = fileApiService.batchSaveFile(attachments);
                log.info("上传文件={}", JSONUtil.toJsonStr(ids));
            } catch (Exception e) {
                log.info("上传文件错误", e);
            }
        }
        String rsp=personJobPostAuthorize.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param personJobPostAuthorizeDTO
     */
    @Override
    public Boolean edit(PersonJobPostAuthorizeDTO personJobPostAuthorizeDTO) throws Exception {
        PersonJobPostAuthorize personJobPostAuthorize =BeanCopyUtils.convertTo(personJobPostAuthorizeDTO,PersonJobPostAuthorize::new);

        this.updateById(personJobPostAuthorize);
        //编辑附件
        List<FileDTO> attachments = personJobPostAuthorizeDTO.getFileDTOList();
        List<FileTreeVO> getFilesByDataIdResponse = fileApi.getFilesTreeByDataId(personJobPostAuthorizeDTO.getId());
        if (!CollectionUtils.isEmpty(getFilesByDataIdResponse)) {
            List<String> filesIds = getFilesByDataIdResponse.stream().map(FileTreeVO::getId).collect(Collectors.toList());
            try {
                fileApi.deleteFileByIds(filesIds);
            } catch (Exception e) {
                log.error("删除文件错误", e);
            }
        }
        if (!CollectionUtils.isEmpty(attachments)) {
            attachments.forEach(a -> {
                a.setId(null);
                a.setDataId(personJobPostAuthorizeDTO.getId());
                a.setDataType("PersonJobPostAuthorize");
            });
            try {
                List<String> ids = fileApiService.batchSaveFile(attachments);
                log.info("上传文件={}", JSONUtil.toJsonStr(ids));
            } catch (Exception e) {
                log.error("上传文件错误", e);
            }
        }
        String rsp=personJobPostAuthorize.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<PersonJobPostAuthorizeVO> pages( Page<PersonJobPostAuthorizeDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<PersonJobPostAuthorize> condition = new LambdaQueryWrapperX<>( PersonJobPostAuthorize. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(PersonJobPostAuthorize::getCreateTime);

        PersonJobPostAuthorizeDTO postAuthorizeDTO= pageRequest.getQuery();
        if(null != postAuthorizeDTO && StringUtils.hasText(postAuthorizeDTO.getUserCode())){
            condition.eq(PersonJobPostAuthorize::getUserCode,postAuthorizeDTO.getUserCode());
        }

        Page<PersonJobPostAuthorize> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), PersonJobPostAuthorize::new));

        PageResult<PersonJobPostAuthorize> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<PersonJobPostAuthorizeVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<PersonJobPostAuthorizeVO> vos = BeanCopyUtils.convertListTo(page.getContent(), PersonJobPostAuthorizeVO::new);

        if(CollectionUtils.isEmpty(vos)){
            pageResult.setContent(vos);
            return pageResult;
        }
        setEveryName(vos);
        Map<String,List<PersonJobPostEquVO>> map = new HashMap<>();
        if(!Objects.isNull(postAuthorizeDTO)){
            List<PersonJobPostEquVO> equList=postEquService.listByUserCode(postAuthorizeDTO.getUserCode());
            if(!CollectionUtils.isEmpty(equList)){
                map=   equList.stream().collect(Collectors.groupingBy(item->String.format("%s_%s",item.getUserCode()
                        ,item.getFormRecordId())));
            }
        }
        Map<String, List<PersonJobPostEquVO>> finalMap = map;
        vos.forEach(item->{
            String key =String.format("%s_%s",item.getUserCode(),item.getId());
            List<PersonJobPostEquVO> personJobPostEquList = finalMap.getOrDefault(key, new ArrayList<>());
            item.setPersonJobPostEquList(personJobPostEquList);
        });
        pageResult.setContent(vos);
        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "人员岗位授权记录落地导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", PersonJobPostAuthorizeDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            PersonJobPostAuthorizeExcelListener excelReadListener = new PersonJobPostAuthorizeExcelListener();
        EasyExcel.read(inputStream,PersonJobPostAuthorizeDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<PersonJobPostAuthorizeDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("人员岗位授权记录落地导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<PersonJobPostAuthorize> personJobPostAuthorizees =BeanCopyUtils.convertListTo(dtoS,PersonJobPostAuthorize::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::PersonJobPostAuthorize-import::id", importId, personJobPostAuthorizees, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<PersonJobPostAuthorize> personJobPostAuthorizees = (List<PersonJobPostAuthorize>) orionJ2CacheService.get("pmsx::PersonJobPostAuthorize-import::id", importId);
        log.info("人员岗位授权记录落地导入的入库数据={}", JSONUtil.toJsonStr(personJobPostAuthorizees));

        this.saveBatch(personJobPostAuthorizees);
        orionJ2CacheService.delete("pmsx::PersonJobPostAuthorize-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::PersonJobPostAuthorize-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<PersonJobPostAuthorize> condition = new LambdaQueryWrapperX<>( PersonJobPostAuthorize. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(PersonJobPostAuthorize::getCreateTime);
        List<PersonJobPostAuthorize> personJobPostAuthorizees =   this.list(condition);

        List<PersonJobPostAuthorizeDTO> dtos = BeanCopyUtils.convertListTo(personJobPostAuthorizees, PersonJobPostAuthorizeDTO::new);

        String fileName = "人员岗位授权记录落地数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", PersonJobPostAuthorizeDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<PersonJobPostAuthorizeVO> vos)throws Exception {

        List<String> dataIdList= vos.stream().map(PersonJobPostAuthorizeVO::getId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<String,List<FileVO>>  map = new HashMap<>();
        if(!CollectionUtils.isEmpty(dataIdList)){
            List<FileVO> filesByDataIds = fileApiService.listMaxFileByDataIds(dataIdList);
            if(!CollectionUtils.isEmpty(filesByDataIds)){
                map = filesByDataIds.stream().collect(Collectors.groupingBy(FileVO::getDataId));
            }
        }
        Map<String, List<FileVO>> finalMap = map;
        vos.forEach(item->{
            item.setFileVOList(finalMap.getOrDefault(item.getId(),new ArrayList<>()));
        });
    }

    @Override
    public List<PersonJobPostAuthorize> listByUserCode(String userCode) {
        LambdaQueryWrapperX<PersonJobPostAuthorize> condition = new LambdaQueryWrapperX<>( PersonJobPostAuthorize. class);
        condition.eq(PersonJobPostAuthorize::getUserCode,userCode);
        return this.list(condition);
    }

    @Override
    public List<PersonJobPostAuthorize> listByUserCodeList(List<String> userCodeList,String baseCode) {
        LambdaQueryWrapperX<PersonJobPostAuthorize> condition = new LambdaQueryWrapperX<>( PersonJobPostAuthorize. class);
        if(StringUtils.hasText(baseCode)){
            condition.and(item->{
                item.eq(PersonJobPostAuthorize::getBaseCode,baseCode)
                        .or().eq(PersonJobPostAuthorize::getBaseCode,"SNPI");
            });
//            condition.eq(PersonJobPostAuthorize::getBaseCode,baseCode);
        }
        condition.in(PersonJobPostAuthorize::getUserCode,userCodeList);
        return this.list(condition);

    }

    @Override
    public void personJobPostAuthorizeVerifyEffect() {
        LambdaQueryWrapperX<PersonJobPostAuthorize> condition = new LambdaQueryWrapperX<>( PersonJobPostAuthorize. class);
        condition.select(PersonJobPostAuthorize::getId);
        condition.lt(PersonJobPostAuthorize::getEndDate,new Date());
        List<PersonJobPostAuthorize> personJobPostAuthorizeList =  this.list(condition);
        if(CollectionUtils.isEmpty(personJobPostAuthorizeList)){
            return;
        }
        LambdaUpdateWrapper<PersonJobPostAuthorize> wrapper = new LambdaUpdateWrapper<>(PersonJobPostAuthorize.class);
        wrapper.set(PersonJobPostAuthorize::getAuthorizeStatus,JobPostAuthorizeEnum.EXPIRED.getStatus());
        wrapper.set(PersonJobPostAuthorize::getAuthorizeStatusName,JobPostAuthorizeEnum.EXPIRED.getDesc());
        wrapper.in(PersonJobPostAuthorize::getId,personJobPostAuthorizeList.stream().map(LyraEntity::getId).distinct().collect(Collectors.toList()));
        this.update(wrapper);
    }

    @Override
    public List<PersonJobPostAuthorizeVO> listByEntity(PersonJobPostAuthorizeDTO postAuthorizeDTO) throws Exception {
        LambdaQueryWrapperX<PersonJobPostAuthorize> condition = new LambdaQueryWrapperX<>( PersonJobPostAuthorize. class);
        if(null != postAuthorizeDTO && StringUtils.hasText(postAuthorizeDTO.getUserCode())){
            condition.eq(PersonJobPostAuthorize::getUserCode,postAuthorizeDTO.getUserCode());
        }
        condition.select(PersonJobPostAuthorize::getId,PersonJobPostAuthorize::getUserCode,PersonJobPostAuthorize::getJobPostCode
                ,PersonJobPostAuthorize::getJobPostName,PersonJobPostAuthorize::getBaseCode,PersonJobPostAuthorize::getBaseName
                ,PersonJobPostAuthorize::getAuthorizeStatus,PersonJobPostAuthorize::getAuthorizeStatusName,PersonJobPostAuthorize::getEndDate
                ,PersonJobPostAuthorize::getIsEquivalent,PersonJobPostAuthorize::getRepairRound,PersonJobPostAuthorize::getSourceId);
        List<PersonJobPostAuthorize> personJobPostAuthorizes =this.list(condition);
        if(CollectionUtils.isEmpty(personJobPostAuthorizes)){
            return  new ArrayList<>();
        }
        List<PersonJobPostAuthorizeVO> vos=BeanCopyUtils.convertListTo(personJobPostAuthorizes, PersonJobPostAuthorizeVO::new);
        setEveryName(vos);
        Map<String,List<PersonJobPostEquVO>> map = new HashMap<>();
        if(!Objects.isNull(postAuthorizeDTO)){
            List<PersonJobPostEquVO> equList=postEquService.listByUserCode(postAuthorizeDTO.getUserCode());
            if(!CollectionUtils.isEmpty(equList)){
                map=   equList.stream().collect(Collectors.groupingBy(item->String.format("%s_%s",item.getUserCode()
                        ,item.getFormRecordId())));
            }
        }
        Map<String, List<PersonJobPostEquVO>> finalMap = map;
        vos.forEach(item->{
            String key =String.format("%s_%s",item.getUserCode(),item.getId());
            List<PersonJobPostEquVO> personJobPostEquList = finalMap.getOrDefault(key, new ArrayList<>());
            item.setPersonJobPostEquList(personJobPostEquList);
        });
        return vos;
    }


    public static class PersonJobPostAuthorizeExcelListener extends AnalysisEventListener<PersonJobPostAuthorizeDTO> {

        private final List<PersonJobPostAuthorizeDTO> data = new ArrayList<>();

        @Override
        public void invoke(PersonJobPostAuthorizeDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<PersonJobPostAuthorizeDTO> getData() {
            return data;
        }
    }


}
