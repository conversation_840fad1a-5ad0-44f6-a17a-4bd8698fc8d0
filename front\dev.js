const { spawn } = require('child_process');
const path = require('path');

// 记录当前目录
const currentDir = process.cwd();

// 运行任务1
const previewProcess = spawn('npm', ['run', 'preview'], {
  cwd: path.join(currentDir, 'node_modules/vue-main'),
  stdio: 'inherit',
  shell: true,
});

// 恢复到原来的目录并运行任务2
const serveProcess = spawn('npm', ['run', 'serve'], {
  cwd: currentDir,
  stdio: 'inherit',
  shell: true,
});
