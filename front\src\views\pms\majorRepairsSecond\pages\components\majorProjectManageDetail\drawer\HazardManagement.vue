<script setup lang="ts">
import {
  BasicButton,
  BasicCard,
  IOpenBasicSelectModalProps,
  openBasicSelectModal,
  openModal,
  OrionTable,
} from 'lyra-component-vue3';
import { ref } from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import { message } from 'ant-design-vue';
import router from '/@/router';

const props = defineProps<{
  projectId: string;
  record: {
    id: string,
    repairRound: string
  }
}>();

const tableRef = ref();
const columns = [
  {
    title: '隐患编号',
    dataIndex: 'hiddenDangerCode',
  },
  {
    title: '事件主题',
    dataIndex: 'eventTopic',
    width: 80,
  },
  {
    title: '事件等级',
    dataIndex: 'eventLevel',
    width: 80,
  },
  {
    title: '事件地点',
    dataIndex: 'eventLocation',
  },
  {
    title: '事件描述',
    dataIndex: 'eventDesc',
  },
  {
    title: '直接责任部门',
    width: 200,
    dataIndex: 'rspDeptName',
  },
  {
    title: '事发日期',
    dataIndex: 'occurrenceDate',
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD') : '';
    },
  },
  {
    title: '大修轮次',
    dataIndex: 'majorRepairTurn',
  },
  {
    title: '隐患/事件领域',
    dataIndex: 'hiddenEvent',
  },
  {
    title: '事件类型',
    dataIndex: 'eventType',
  },
];
const tableOptions = {
  showTableSetting: false,
  showToolButton: false,
  showSmallSearch: false,
  isSpacing: false,
  pagination: false,
  resizeHeightOffset: 80,
  api: () => new Api(`/pms/importantProject/relation/safetyQualityEnv/lists/${props.projectId}/${props.record.id}`).fetch({}, '', 'POST'),
  columns: [
    ...columns,
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 120,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '查看',
      onClick(record) {
        openModal.closeAll();
        router.push({
          name: 'PMSSQEManageDetails',
          params: {
            id: record.id,
          },
        });
      },
    },
    {
      text: '移除',
      modal(record) {
        return new Promise((resolve) => {
          new Api('/pms/importantProject/relation/safetyQualityEnv/remove').fetch({
            jobManageId: props.record.id,
            projectId: props.projectId,
            safetyQualityEnvId: [record.id],
          }, '', 'DELETE').then(() => {
            resolve(true);
          }).catch(() => {
            resolve('');
          });
        });
      },
    },
  ],
};

function handleAdd() {
  openBasicSelectModal({
    title: '隐患信息',
    smallSearchField: [
      'eventTopic',
      'hiddenDangerCode',
      'eventDesc',
      'rspDeptCode',
      'rspDeptName',
    ],
    async tableApi(params) {
      const result = await new Api('/pms/safety-quality-env/page').fetch({
        ...params,
        query: {
          majorRepairTurn: props.record.repairRound,
        },
      }, '', 'POST');
      return {
        ...result,
        content: result?.content?.map((item) => ({
          ...item,
          name: item.hiddenDangerCode,
        })),
      };
    },
    tableColumns: columns,
    async onOk(selectedData: any[]) {
      if (selectedData.length === 0) {
        message.error('请选择隐患信息');
        return Promise.reject();
      }
      return new Promise((resolve) => {
        new Api('/pms/importantProject/relation/safetyQualityEnv/add').fetch({
          jobManageId: props.record.id,
          projectId: props.projectId,
          safetyQualityEnvId: selectedData.map((item) => item.id),
        }, '', 'POST').then(() => {
          tableRef.value.reload();
        }).finally(() => {
          resolve();
        });
      });
    },
  } as IOpenBasicSelectModalProps);
}
</script>

<template>
  <div style="padding: 1px 0">
    <BasicCard
      title="隐患信息"
    >
      <OrionTable
        ref="tableRef"
        :options="tableOptions"
      >
        <template #toolbarLeft>
          <BasicButton
            type="primary"
            icon="sie-icon-tianjiaxinzeng"
            @click="handleAdd"
          >
            添加
          </BasicButton>
        </template>
      </OrionTable>
    </BasicCard>
  </div>
</template>

<style scoped lang="less">

</style>
