<script setup lang="ts">
import { Spin, Tag as ATag, Tooltip } from 'ant-design-vue';
import {
  Layout3, Layout3Content, BasicButton, isPower,
} from 'lyra-component-vue3';
import {
  computed, onMounted, provide, reactive, ref, Ref, unref,
} from 'vue';
import { useRoute } from 'vue-router';
import FrameBasicInfo from '../components/BasicInfo/FrameBasicInfo.vue';
import RequisitionForm from '../components/RequisitionForm.vue';
import ChildOrder from '../components/ChildOrder.vue';
import Api from '/@/api';
import { BasicInjectionsKey } from '../../tokens/basicKeys';
import { openFormDrawer } from '/@/views/spm/purchaseManage/purchaseModule/utils';
import PurchaseContractForm from '../components/PurchaseContractForm.vue';
import ContractRuntime from '../components/ContractRuntime/ContractRuntime.vue';
import PurchaseProjectApproval from '../components/PurchaseProjectApproval/PurchaseProjectApproval.vue';

interface MenuItem {
  id: string,
  name: string,
  show?: boolean,
  children?: MenuItem[]
}
const loading: Ref<boolean> = ref(false);
const layoutData = computed(() => ({}));
const defaultActionId: Ref<string> = ref('jBXX');
const route = useRoute();
const projectId: Ref<string> = ref(route.params.id as string);
const basicInfo = reactive({
  data: {},
});
const powerData = ref();

const menuData = computed(() => [
  {
    id: 'jBXX',
    name: '基本信息',
    code: 'PMS_KJHTLBXQ_container_02_HB_01',
  },
  {
    id: 'cGLX',
    name: '采购立项',
    code: 'PMS_KJHTLBXQ_container_02_HB_02',
  },
  {
    id: 'cGZX',
    name: '采购执行',
    code: 'PMS_KJHTLBXQ_container_02_HB_03',
  },
  {
    id: 'xQD',
    name: '需求单',
    code: 'PMS_KJHTLBXQ_container_02_HB_04',
  },
  {
    id: 'zDD',
    name: '子订单',
    code: 'PMS_KJHTLBXQ_container_02_HB_05',
  },
].filter((item) => isPower(item.code, powerData.value)));
const showEditButton = computed(() => isPower('PMS_KJHTLBXQ_container_02_HB_05_button_01', powerData.value));

provide(BasicInjectionsKey, basicInfo);

function menuChange(option: { id: string, index: number, item: MenuItem }): void {
  defaultActionId.value = option.id;
}
const getInfo = async () => {
  try {
    const result = await new Api('/spm/contractInfo').fetch('', unref(projectId), 'GET');
    basicInfo.data = result || {};
  } catch (e) {
  }
};
const handleUpdateForm = () => {
  openFormDrawer(PurchaseContractForm, basicInfo.data, () => {
    getInfo();
  });
};
const getPowerDataHandle = async (data: any) => {
  powerData.value = data;
};
onMounted(async () => {
  await getInfo();
});
</script>

<template>
  <Layout3
    v-get-power="{pageCode: 'frameworkContractInfoPMS001',getPowerDataHandle}"
    class="purchase-manage-layout"
    :projectData="layoutData"
    :menuData="menuData"
    :defaultActionId="defaultActionId"
    :type="2"
    :onMenuChange="menuChange"
  >
    <template #code>
      <h2
        class="custom-page-title"
        :title="basicInfo.data?.contractName"
      >
        {{ basicInfo.data?.contractName || basicInfo.data?.techRespons || "" }}
      </h2>
      <div class="page-subtitle">
        <span>合同编号：{{ basicInfo.data?.contractNumber }}</span>
        <template v-if="basicInfo.data?.purchaseApplicant">
          <Tooltip placement="top">
            <template #title>
              <span class="tooltip">{{ basicInfo.data?.purchaseApplicant }}</span>
            </template>
            <span>采购申请号：{{ basicInfo.data?.purchaseApplicant }}</span>
          </Tooltip>
        </template>
        <span v-else>采购申请号：-</span>
      </div>
    </template>
    <template #header-info>
      <div class="type-map">
        <a-tag
          style="margin-left: 20px;"
          color="purple"
        >
          已审核
        </a-tag>
        <a-tag color="green">
          履行中
        </a-tag>
      </div>
    </template>
    <template #header-right>
      <BasicButton
        v-if="showEditButton"
        type="primary"
        ghost
        icon="sie-icon-chakantuisong"
        @click="handleUpdateForm"
      >
        编辑
      </BasicButton>
    </template>
    <div
      v-if="loading"
      class="w-full h-full flex flex-pc flex-ac"
    >
      <Spin />
    </div>
    <Layout3Content v-else>
      <FrameBasicInfo v-if="defaultActionId==='jBXX'" />
      <PurchaseProjectApproval v-if="defaultActionId==='cGLX'" />
      <ContractRuntime v-if="defaultActionId==='cGZX'" />
      <RequisitionForm v-if="defaultActionId==='xQD'" />
      <ChildOrder v-if="defaultActionId==='zDD'" />
    </Layout3Content>
  </Layout3>
</template>

<style scoped lang="less">
:deep(.header-wrap) {
  min-height: 60px !important;
  height: auto !important;
  .header-main{
    min-height: 33px !important;
    height: auto !important;
  }
  .project-title {
    width: 400px !important;
    .flex-te{
      word-wrap: break-word;
      overflow-wrap: break-word;
      white-space: normal;
      line-height: 26px;
      padding-top: 10px;
    }
  }

  .layout-menu-warp {
    display: flex;
    align-items: center;

    .ant-menu {
      height: 60px !important;
    }
  }
}

.custom-page-title {
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
  line-height: 26px;
  padding-top: 10px;
  font-size: 18px;
  color: #000000D9;
}

.page-subtitle {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  font-size: 14px;
  color: rgb(150, 158, 180);
  font-weight: 40;
  padding-bottom: 6px;
  span {
    margin-right: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100%;
  }
}

.type-map {
  display: flex;
  align-items: center;
  .type-wrapper{
    display: flex;
    flex-direction: column;
  }

  .type-h2-enum {
    font-size: 14px;
    color: #000000D9;
    margin-bottom: 3px;
  }

  .type-p-enum {
    font-size: 12px;
    color: #000000D9;
    margin-bottom: 0;
  }
}
.purchase-manage-layout{
  :deep(.ant-menu-overflow){
    width: 390px;
  }
}
</style>
