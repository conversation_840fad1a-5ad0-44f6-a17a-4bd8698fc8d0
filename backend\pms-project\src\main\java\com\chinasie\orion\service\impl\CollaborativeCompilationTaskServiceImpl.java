package com.chinasie.orion.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.api.holidays.HolidaysApi;
import com.chinasie.orion.api.holidays.domain.dto.CalculationWorkdayNumDTO;
import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.bo.LyraFileBO;
import com.chinasie.orion.constant.ClassNameConstant;
import com.chinasie.orion.constant.FileConstant;
import com.chinasie.orion.conts.MsgBusinessTypeEnum;
import com.chinasie.orion.dict.ApprovalDict;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.dto.CollaborativeCompilationTaskDTO;
import com.chinasie.orion.domain.dto.collaborativecompilationtask.TaskFallBackDTO;
import com.chinasie.orion.domain.dto.collaborativecompilationtask.TaskIssueDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.entity.CollaborativeCompilationTask;
import com.chinasie.orion.domain.entity.ApprovalTaskPrePost;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.domain.vo.CollaborativeCompilationTaskVO;

import com.chinasie.orion.domain.vo.approval.ProjectApprovalEstimateExpenseSubjectVO;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalEstimateVO;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalIncomeVO;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalRiskPlanVO;
import com.chinasie.orion.manager.SendMessageManager;
import com.chinasie.orion.manager.TaskSendMessageManager;
import com.chinasie.orion.manager.WfInstanceTaskManage;
import com.chinasie.orion.number.api.domain.GenerateNumberRequest;
import com.chinasie.orion.number.api.sdk.NumberApiService;
import com.chinasie.orion.service.approval.ProjectApprovalEstimateExpenseSubjectService;
import com.chinasie.orion.service.approval.ProjectApprovalEstimateMaterialService;
import com.chinasie.orion.service.approval.ProjectApprovalIncomeService;
import com.chinasie.orion.service.approval.ProjectApprovalRiskPlanService;
import com.chinasie.orion.util.PlanAuthUtil;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import freemarker.template.Version;

import freemarker.template.TemplateException;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.manager.TaskStatusProcessor;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.CollaborativeCompilationTaskMapper;
import com.chinasie.orion.sdk.domain.vo.business.*;
import com.chinasie.orion.sdk.helper.*;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.io.IOException;
import java.io.StringWriter;
import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;

import java.io.InputStream;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;


/**
 * <p>
 * CollaborativeCompilationTask 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 17:04:08
 */
@Service
@Slf4j
public class CollaborativeCompilationTaskServiceImpl extends OrionBaseServiceImpl<CollaborativeCompilationTaskMapper, CollaborativeCompilationTask> implements CollaborativeCompilationTaskService {


    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private TaskStatusProcessor taskStatusProcessor;

    @Resource
    private ApprovalTaskPrePostService approvalTaskPrePostService;

    @Resource
    private ApprovalTaskPrePostService taskPrePostService;

    @Resource
    private ProjectApprovalService projectApprovalService;


    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Autowired
    private HolidaysApi holidaysApi;

    @Resource
    private LyraFileBO fileBo;

    private static final String ROOT_ID = "0";

    @Autowired
    private DictBo dictBo;

    @Autowired
    private CollaborativeCompilationDocumentService collaborativeCompilationDocumentService;

    @Autowired
    private DocumentDecompositionService documentDecompositionService;

    @Autowired
    private TaskDecompositionService taskDecompositionService;

    @Autowired
    private ClassRedisHelper classRedisHelper;

    @Autowired
    private StatusRedisHelper statusRedisHelper;

    @Autowired
    ProjectApprovalRiskPlanService projectApprovalRiskPlanService;

    @Autowired
    ProjectApprovalEstimateMaterialService projectApprovalEstimateMaterialService;

    @Autowired
    ProjectApprovalEstimateExpenseSubjectService projectApprovalEstimateExpenseSubjectService;

    @Autowired
    ProjectApprovalIncomeService projectApprovalIncomeService;

    @Autowired
    ProjectApprovalMilestoneService projectApprovalMilestoneService;

    @Autowired
    ProductPlanService productPlanService;

    @Autowired
    private NumberApiService numberApiService;

    @Resource
    private PlanAuthUtil planAuthUtil;

    @Autowired
    private WfInstanceTaskManage wfInstanceTaskManage;

    @Resource
    private TaskSendMessageManager messageManager;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public CollaborativeCompilationTaskVO detail(String id, String pageCode) throws Exception {

//        taskStatusProcessor.statusHandleById(CollUtil.toList(id));
        CollaborativeCompilationTask collaborativeCompilationTask = this.getById(id);
        CollaborativeCompilationTaskVO collaborativeCompilationTaskVO = BeanCopyUtils.convertTo(collaborativeCompilationTask, CollaborativeCompilationTaskVO::new);
        if (!ObjectUtil.equals("0", collaborativeCompilationTask.getParentId())) {
            String parentId = collaborativeCompilationTask.getParentId();
            CollaborativeCompilationTask parent = this.getById(parentId);
            collaborativeCompilationTaskVO.setParentName(parent.getName());
        }
        //设置下发人名称
        if (StringUtils.hasText(collaborativeCompilationTaskVO.getIssuedUser())) {
            UserVO user = userRedisHelper.getUser(CurrentUserHelper.getOrgId(), collaborativeCompilationTaskVO.getIssuedUser());
            collaborativeCompilationTaskVO.setIssuedUserName(user == null ? "" : user.getName());
        }

        //获取前置任务
        LambdaQueryWrapper<ApprovalTaskPrePost> prePostWrapper = new LambdaQueryWrapper<>(ApprovalTaskPrePost.class)
                .eq(ApprovalTaskPrePost::getApprovalId, collaborativeCompilationTask.getApprovalId())
                .eq(ApprovalTaskPrePost::getTaskId, id);
        List<ApprovalTaskPrePost> prePostList = taskPrePostService.list(prePostWrapper);
        if (CollectionUtil.isNotEmpty(prePostList)) {
            List<String> prePostIdList = new ArrayList<>();
            for (ApprovalTaskPrePost collaborativeCompilationTaskPrePost : prePostList) {
                if (Status.SCHEME_PRE.getCode().equals(collaborativeCompilationTaskPrePost.getType())) {
                    prePostIdList.add(collaborativeCompilationTaskPrePost.getPreTaskId());
                } else {
                    prePostIdList.add(collaborativeCompilationTaskPrePost.getPostTaskId());
                }
            }
            List<CollaborativeCompilationTaskVO> prePostSchemeVOS = BeanCopyUtils.convertListTo(this.listByIds(prePostIdList), CollaborativeCompilationTaskVO::new);

            Map<String, CollaborativeCompilationTaskVO> collaborativeCompilationTaskVOMap = prePostSchemeVOS.stream().collect(Collectors.toMap(CollaborativeCompilationTaskVO::getId, Function.identity()));

            List<ApprovalTaskPrePostVO> result = BeanCopyUtils.convertListTo(prePostList, ApprovalTaskPrePostVO::new);
            Map<Integer, List<ApprovalTaskPrePostVO>> prePostSchemeMap = result.stream().peek(prePost -> {
                CollaborativeCompilationTaskVO vo;
                if (Status.SCHEME_PRE.getCode().equals(prePost.getType())) {
                    vo = collaborativeCompilationTaskVOMap.get(prePost.getPreTaskId());
                } else {
                    vo = collaborativeCompilationTaskVOMap.get(prePost.getPostTaskId());
                }
                if (null == vo) {
                    return;
                }
                prePost.setPreTaskName(vo.getName());
                prePost.setTaskBeginTime(vo.getBeginTime());
                prePost.setTaskEndTime(vo.getEndTime());
                prePost.setRspDept(vo.getRspDept());
                prePost.setRspDeptName(vo.getRspDeptName());
                prePost.setRspUser(vo.getRspUser());
                prePost.setRspUserName(vo.getRspUserName());
                prePost.setTaskName(collaborativeCompilationTask.getName());
                prePost.setTypeName(Status.codeMapping(prePost.getType()));

            }).collect(Collectors.groupingBy(ApprovalTaskPrePostVO::getType, Collectors.toList()));
            collaborativeCompilationTaskVO.setTaskPreVOList(prePostSchemeMap.getOrDefault(Status.SCHEME_PRE.getCode(), new ArrayList<>()));
            collaborativeCompilationTaskVO.setTaskPostVOList(prePostSchemeMap.getOrDefault(Status.SCHEME_POST.getCode(), new ArrayList<>()));
            collaborativeCompilationTaskVO.setIsPrePost(prePostSchemeMap.containsKey(Status.SCHEME_PRE.getCode()));
            collaborativeCompilationTaskVO.setTaskPrePostVOS(prePostSchemeMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList()));
        }


        /**
         * 映射名称
         */
        //  commonManager.codeMapping(collaborativeCompilationTaskVO);
        setEveryName(Arrays.asList(collaborativeCompilationTaskVO));
        return collaborativeCompilationTaskVO;
    }

    /**
     * 新增
     * <p>
     * * @param collaborativeCompilationTaskDTO
     */
    @Override
    public String create(CollaborativeCompilationTaskDTO collaborativeCompilationTaskDTO) throws Exception {
        CollaborativeCompilationTask collaborativeCompilationTask = BeanCopyUtils.convertTo(collaborativeCompilationTaskDTO, CollaborativeCompilationTask::new);
        this.save(collaborativeCompilationTask);

        String rsp = collaborativeCompilationTask.getId();


        return rsp;
    }


    public static boolean isWithinRange(Date start1, Date end1, Date start2, Date end2) {
        // 如果 start1 或 end1 位于 start2 和 end2 之间（包括边界），则返回 true
        if ((start1.equals(start2) || start1.after(start2)) && (end1.equals(end2) || end1.before(end2))) {
            return true;
        }

        return false;
    }

    public static Date getnewDate(Date originalDate) {
        // 使用Calendar设置时分秒为00:00:00
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(originalDate);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        // 获取修改后的Date对象
        Date midnightDate = calendar.getTime();
        return midnightDate;
    }

    /**
     * 编辑
     * <p>
     * * @param collaborativeCompilationTaskDTO
     */
    @Override
    public Boolean edit(CollaborativeCompilationTaskDTO collaborativeCompilationTaskDTO) throws Exception {

        CollaborativeCompilationTask oldScheme = getById(collaborativeCompilationTaskDTO.getId());
        //查询父任务
        String parentId = oldScheme.getParentId();
        CollaborativeCompilationTask parentCollaborativeCompilationTask = this.getById(parentId);
        if (Objects.nonNull(parentCollaborativeCompilationTask)) {
            if (!isWithinRange(getnewDate(collaborativeCompilationTaskDTO.getBeginTime()),
                    getnewDate(collaborativeCompilationTaskDTO.getEndTime()),
                    getnewDate(parentCollaborativeCompilationTask.getBeginTime()),
                    getnewDate(parentCollaborativeCompilationTask.getEndTime()))) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_INVALID_ACTION, "子任务划时间范围不在父任务时间范围内");
            }
        }

        if (ObjectUtil.isNotNull(oldScheme.getBeginTime())) {
            List<ApprovalTaskPrePost> taskPrePosts = taskPrePostService.list(new LambdaQueryWrapper<>(ApprovalTaskPrePost.class)
                    .eq(ApprovalTaskPrePost::getTaskId, oldScheme.getId()));
            if (!CollectionUtils.isEmpty(taskPrePosts)) {
                //查询前置任务
                List<String> preSchemeIds = taskPrePosts.stream().filter(f -> Status.SCHEME_PRE.getCode().equals(f.getType())).map(ApprovalTaskPrePost::getPreTaskId).collect(Collectors.toList());
               if(CollUtil.isNotEmpty(preSchemeIds)) {
                   List<CollaborativeCompilationTask> preSchemes = this.listByIds(preSchemeIds);
                   preSchemes.forEach(pre -> {
                       //校验前置任务的结束时间是否在当前任务的前面
                       if (pre.getEndTime().after(collaborativeCompilationTaskDTO.getBeginTime())) {
                           throw new BaseException(PMSErrorCode.PMS_ERROR_PRJECT_SCHEME_PREPOST_TIME_ERROR, "您任务的开始时间在前置任务结束时间之前，请调整");
                       }
                   });
               }

                    //查询后置任务
                    List<String> postSchemeIds = taskPrePosts.stream().filter(f -> Status.SCHEME_POST.getCode().equals(f.getType())).map(ApprovalTaskPrePost::getPostTaskId).collect(Collectors.toList());
                if(CollUtil.isNotEmpty(preSchemeIds)) {
                   List<CollaborativeCompilationTask> postSchemes = this.listByIds(postSchemeIds);
                    postSchemes.forEach(post -> {
                        //校验前置任务的结束时间是否在当前任务的前面
                        if (collaborativeCompilationTaskDTO.getEndTime().after(post.getBeginTime())) {
                            throw new BaseException(PMSErrorCode.PMS_ERROR_PRJECT_SCHEME_PREPOST_TIME_ERROR, "您任务的开始时间在前置任务结束时间之前，请调整");
                        }
                    });
                }
            }
        }
        if(ObjectUtil.isNotEmpty(collaborativeCompilationTaskDTO.getContent())&&StrUtil.isNotBlank(oldScheme.getProcessInstances())){
            CollaborativeCompilationDocument  doc =    collaborativeCompilationDocumentService.getById(oldScheme.getProcessInstances());
            doc.setContent(collaborativeCompilationTaskDTO.getContent());
            collaborativeCompilationDocumentService.updateById(doc);
        }

        CollaborativeCompilationTask task = BeanCopyUtils.convertTo(collaborativeCompilationTaskDTO, CollaborativeCompilationTask::new);
        if (!Status.PENDING.getCode().equals(task.getStatus())) {
            task.setOwnerId(task.getRspUser());
        }

        CalculationWorkdayNumDTO calculationWorkdayNumDTO = new CalculationWorkdayNumDTO();
        calculationWorkdayNumDTO.setStartDate(task.getBeginTime());
        calculationWorkdayNumDTO.setEndDate(task.getEndTime());
        Long durationDays = holidaysApi.calculationIntervalDays(calculationWorkdayNumDTO).getResult();
        if (ObjectUtil.isNotEmpty(durationDays)) {
            task.setDurationDays(durationDays.intValue());
        }

        //TODO 消息处理
        if (this.updateById(task)) {
            //更新后置任务的任务开始时间和任务完成时间
//            updatePostSchemeTime(task, oldScheme);

            if (Status.PUBLISHED.getCode().equals(task.getStatus())) {
               // messageManager.sendMsg(MsgBusinessTypeEnum.EDIT, SchemeMsgDTO.builder().taskList(CollUtil.toList(oldScheme)).build());
                if (!oldScheme.getRspUser().equals(task.getRspUser())) {
                    //发送待办到新责任人
                    messageManager.sendMsg(MsgBusinessTypeEnum.TASK_SEND_DOWN, TaskMsgDTO.builder().collaborativeCompilationTasks(CollUtil.toList(task)).build());
                    //消除原有责任人待办
                    messageManager.clearToDo(MsgBusinessTypeEnum.TASK_SEND_DOWN, oldScheme.getId(), oldScheme.getRspUser());
                }
            }
        }


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        List<CollaborativeCompilationTask> childScheme = this.list(new LambdaQueryWrapper<>(CollaborativeCompilationTask.class).in(CollaborativeCompilationTask::getParentId, ids));
//        if (CollectionUtil.isNotEmpty(childScheme)) {
//            throw new BaseException(PMSErrorCode.PMS_ERROR_HAVE_CHILD);
//        }
        List<String> childIds = childScheme.stream().map(CollaborativeCompilationTask::getId).collect(Collectors.toList());
        childIds.addAll(ids);
        List<String> delIds = childIds.stream().distinct().collect(Collectors.toList());
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        List<CollaborativeCompilationTask> schemes = this.list(new LambdaQueryWrapper<>(CollaborativeCompilationTask.class)
                .in(CollaborativeCompilationTask::getId, childIds));
        if (CollUtil.isNotEmpty(schemes) && schemes.stream().map(CollaborativeCompilationTask::getCreatorId).anyMatch(item -> !item.equals(currentUserId))) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_INVALID_ACTION, "存在非本人创建的任务，不能删除");
        }
        LambdaQueryWrapper<CollaborativeCompilationTask> wrapper = new LambdaQueryWrapper<>(CollaborativeCompilationTask.class);
        wrapper.ne(CollaborativeCompilationTask::getStatus, Status.PENDING.getCode()).in(CollaborativeCompilationTask::getId, delIds);
        List<CollaborativeCompilationTask> nonPe = list(wrapper);
        if (CollUtil.isNotEmpty(nonPe)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_INVALID_ACTION, "存在已发布或已完成的任务，不能删除");
        }
        taskPrePostService.remove(new LambdaQueryWrapper<>(ApprovalTaskPrePost.class).and(sub -> sub.in(ApprovalTaskPrePost::getTaskId, delIds).or().in(ApprovalTaskPrePost::getTaskId, delIds)));

         List<CollaborativeCompilationTask> taskList = this.listByIds(delIds);

        List<String> docIds = taskList.stream().map(CollaborativeCompilationTask::getProcessInstances).collect(Collectors.toList());
//        List<CollaborativeCompilationDocument> documentList = collaborativeCompilationDocumentService.listByIds(docIds);
        if(CollUtil.isNotEmpty(docIds)) {
            LambdaUpdateWrapper<CollaborativeCompilationDocument> lambdaQueryWrapper = new LambdaUpdateWrapper<>(CollaborativeCompilationDocument.class);
            lambdaQueryWrapper.in(CollaborativeCompilationDocument::getId,docIds);
            lambdaQueryWrapper.set(CollaborativeCompilationDocument::getTaskId,null);
            collaborativeCompilationDocumentService.update(lambdaQueryWrapper);
        }

        return this.removeBatchByIds(delIds);
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<CollaborativeCompilationTaskVO> pages(Page<CollaborativeCompilationTaskDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<CollaborativeCompilationTask> condition = new LambdaQueryWrapperX<>(CollaborativeCompilationTask.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(CollaborativeCompilationTask::getCreateTime);


        Page<CollaborativeCompilationTask> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), CollaborativeCompilationTask::new));

        PageResult<CollaborativeCompilationTask> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<CollaborativeCompilationTaskVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<CollaborativeCompilationTaskVO> vos = BeanCopyUtils.convertListTo(page.getContent(), CollaborativeCompilationTaskVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public void setEveryName(List<CollaborativeCompilationTaskVO> vos) throws Exception {
         List<String> ids = vos.stream().map(CollaborativeCompilationTaskVO::getProcessInstances).collect(Collectors.toList());
         List<CollaborativeCompilationDocument> documentList = collaborativeCompilationDocumentService.listByIds(ids);
         Map<String,String> map = documentList.stream().collect(Collectors.toMap(CollaborativeCompilationDocument::getId,CollaborativeCompilationDocument::getName));
         vos.forEach(vo -> {
            vo.setCircumstanceName(Status.codeMapping(vo.getCircumstance()));
            vo.setRspUserName("");
            if (StrUtil.isNotBlank(vo.getRspDept())) {
                DeptVO dept = deptRedisHelper.getDeptById(vo.getRspDept());
                vo.setRspDeptName(null == dept ? "" : dept.getName());
            }
            if (StrUtil.isNotBlank(vo.getRspUser())) {
                UserVO rspUser = userRedisHelper.getUserById(vo.getRspUser());
                vo.setRspUserName(null == rspUser ? "" : rspUser.getName());
            }
            if (StrUtil.isNotBlank(vo.getCreatorId())) {
                UserVO rspUser = userRedisHelper.getUserById(vo.getCreatorId());
                vo.setCreatorName(null == rspUser ? "" : rspUser.getName());
            }
            if(StrUtil.isNotBlank(vo.getProcessInstances())){
                vo.setProcessInstancesName(map.get(vo.getProcessInstances()));
            }
        });
    }

    @Override
    public List<CollaborativeCompilationTaskVO> getList(CollaborativeCompilationTaskDTO collaborativeCompilationTaskDTO) throws Exception {

        String userId = CurrentUserHelper.getCurrentUserId();
        ProjectApproval projectApproval = projectApprovalService.getById(collaborativeCompilationTaskDTO.getApprovalId());
        if(ObjectUtil.isEmpty(projectApproval)){
            throw new PMSException(PMSErrorCode.PMS_ERR, "无立项数据");
        }
        List<CollaborativeCompilationTask> collaborativeCompilationTasks = this.list(new LambdaQueryWrapper<CollaborativeCompilationTask>().eq(CollaborativeCompilationTask::getApprovalId,collaborativeCompilationTaskDTO.getApprovalId()));
        taskStatusProcessor.statusHandle(collaborativeCompilationTasks);

        this.updateBatchById(collaborativeCompilationTasks);


        List<CollaborativeCompilationTaskVO> resultContent = CollUtil.toList();
        //过滤需要展示的项目任务
        LambdaQueryWrapperX<CollaborativeCompilationTask> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(CollaborativeCompilationTask.class);
        if(!projectApproval.getRspUser().equals(userId)){
            List<String> ids =  getUserTask(userId,projectApproval.getId());
             if(CollUtil.isNotEmpty(ids)){
                 lambdaQueryWrapperX.in(CollaborativeCompilationTask::getId,ids);
             }else{
                 return new ArrayList<>();
             }
        }
        lambdaQueryWrapperX.eq(CollaborativeCompilationTask::getApprovalId,collaborativeCompilationTaskDTO.getApprovalId());
        lambdaQueryWrapperX.eqIfPresent(CollaborativeCompilationTask::getStatus,collaborativeCompilationTaskDTO.getStatus());
        List<CollaborativeCompilationTask> viewList = this.list(lambdaQueryWrapperX);
        if (CollUtil.isEmpty(viewList)) {
            return resultContent;
        }
        List<CollaborativeCompilationTask> allList = this.list(new LambdaQueryWrapper<CollaborativeCompilationTask>().eq(CollaborativeCompilationTask::getApprovalId, collaborativeCompilationTaskDTO.getApprovalId()));
        if(!projectApproval.getRspUser().equals(userId)) {
            viewList = getTree(viewList, allList);
        }

        List<CollaborativeCompilationTaskVO> collaborativeCompilationTaskVOS = BeanCopyUtils.convertListTo(viewList, CollaborativeCompilationTaskVO::new);
        Map<String, String> idToName = new HashMap<>();
        List<String> userIdList = new ArrayList<>();
        collaborativeCompilationTaskVOS.forEach(o -> {
            userIdList.add(o.getCreatorId());
            UserVO creatorUserVO = userRedisHelper.getUserById(o.getCreatorId());
            if (Objects.nonNull(creatorUserVO)) {
                o.setCreatorName(creatorUserVO.getName());
            }
            String issuedUser = o.getIssuedUser();
            if (StringUtils.hasText(issuedUser)) {
                userIdList.add(issuedUser);
            }
        });

        Map<String, UserVO> idToEntity = userRedisHelper.getUserMapByUserIds(CurrentUserHelper.getOrgId(), userIdList);
        for (Map.Entry<String, UserVO> stringUserVOEntry : idToEntity.entrySet()) {
            idToName.put(stringUserVOEntry.getValue().getId(), stringUserVOEntry.getValue().getName());
        }
        collaborativeCompilationTaskVOS.forEach(o -> {
            o.setCreatorName(idToName.getOrDefault(o.getCreatorId(), ""));
            if (StringUtils.hasText(o.getIssuedUser())) {
                o.setIssuedUserName(idToName.getOrDefault(o.getIssuedUser(), ""));
            }
        });
        setEveryName(collaborativeCompilationTaskVOS);

        //权限设置
        Map<String, List<String>> dataRoleMap = getDataRoleMap(collaborativeCompilationTaskVOS);
        planAuthUtil.setRowAuths(CurrentUserHelper.getCurrentUserId(), collaborativeCompilationTaskDTO.getPower(), collaborativeCompilationTaskVOS, CollaborativeCompilationTaskVO::getId, CollaborativeCompilationTaskVO::getDataStatus, CollaborativeCompilationTaskVO::setRdAuthList,
                dataRoleMap);
        //设置展示属性
        setAttribute(collaborativeCompilationTaskVOS);
        //获取置顶项目任务
        List<CollaborativeCompilationTaskVO> topList = topTaskList(collaborativeCompilationTaskVOS);
        //任务排序
        resultContent = sortContent(collaborativeCompilationTaskVOS);
        //合并
        List<CollaborativeCompilationTaskVO> result = BeanCopyUtils.convertListTo(topList, CollaborativeCompilationTaskVO::new);
        result.addAll(resultContent);
        return result;

    }

    public List<String> getUserTask(String userId,String approvalId){
        LambdaQueryWrapperX<CollaborativeCompilationTask> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(CollaborativeCompilationTask.class);
        lambdaQueryWrapperX.eq(CollaborativeCompilationTask::getRspUser,userId);
        lambdaQueryWrapperX.eq(CollaborativeCompilationTask::getApprovalId,approvalId);
        List<CollaborativeCompilationTask> list = this.list(lambdaQueryWrapperX);
        List<String> ids = list.stream().map(CollaborativeCompilationTask::getId).collect(Collectors.toList());
        if(CollUtil.isEmpty(ids)){
           return new ArrayList<>();
        }
        List<ApprovalTaskPrePost> prePosts = approvalTaskPrePostService.list(new LambdaQueryWrapper<ApprovalTaskPrePost>()
                .eq(ApprovalTaskPrePost::getTaskId,ids));
        if(CollUtil.isNotEmpty(prePosts)) {
            List<String> preTaskIds = prePosts.stream().filter(f -> Status.SCHEME_PRE.getCode().equals(f.getType())).map(ApprovalTaskPrePost::getPreTaskId).collect(Collectors.toList());
            List<String> postTaskIds = prePosts.stream().filter(f -> Status.SCHEME_PRE.getCode().equals(f.getType())).map(ApprovalTaskPrePost::getPreTaskId).collect(Collectors.toList());
            ids.addAll(preTaskIds);
            ids.addAll(postTaskIds);
        }
        return  ids;
    }
    public Map<String, List<String>> getDataRoleMap(List<CollaborativeCompilationTaskVO> vos) throws Exception {
        Map<String, List<String>> dataRoleCodeMap = new HashMap<>();
        String currentUserId = CurrentUserHelper.getCurrentUserId();

        for (CollaborativeCompilationTaskVO v : vos) {
            List<String> roles = new ArrayList<>();
            //任务录入人 Planentryperson_jhlrr
            if (StrUtil.equals(v.getCreatorId(), currentUserId)) {
                roles.add("Planentryperson_jhlrr");
            }

            // 数据创建者
            if (Objects.equals(currentUserId, v.getCreatorId())) {
                roles.add("plan_sjcjz");
            }
            //数据参与者
            if (Objects.equals(currentUserId, v.getModifyId())) {
                roles.add("plan_sjscyz");
            }
            //数据拥有者
            if (Objects.equals(currentUserId, v.getOwnerId())) {
                roles.add("plan_sjsyz");
            }

            dataRoleCodeMap.put(v.getId(), roles);
        }
        return dataRoleCodeMap;
    }



    private void setAttribute(List<CollaborativeCompilationTaskVO> content) throws Exception {
        Map<String, List<ApprovalTaskPrePost>> preMap = initPreMap(content.stream()
                .map(CollaborativeCompilationTaskVO::getId)
                .collect(Collectors.toList()));
        Map<String, List<CollaborativeCompilationTaskVO>> childMap = initChildMap(content);
        content.forEach(item -> {
            item.setTaskPreVOList(getPreTask(item.getApprovalId(), item.getId()));
            item.setTaskPostVOList(getPostTask(item.getApprovalId(), item.getId()));
            item.setIsPrePost(!CollectionUtils.isEmpty(preMap.get(item.getId())));
            List<CollaborativeCompilationTaskVO> childList = childMap.getOrDefault(item.getId(), CollUtil.toList())
                    .stream()
                    .sorted(Comparator.comparing(CollaborativeCompilationTaskVO::getSort))
                    .collect(Collectors.toList());
            item.setChildren(childList);
        });
    }


    private Map<String, List<CollaborativeCompilationTaskVO>> initChildMap(List<CollaborativeCompilationTaskVO> collaborativeCompilationTaskVOs) {
        Map<String, List<CollaborativeCompilationTaskVO>> childMap = new HashMap<>();
        for (CollaborativeCompilationTaskVO item : collaborativeCompilationTaskVOs) {
//            commonManager.codeMapping(item);
            if (!childMap.containsKey(item.getParentId())) {
                childMap.put(item.getParentId(), CollUtil.toList());
            }
            childMap.get(item.getParentId()).add(item);
        }

        for (String s : childMap.keySet()) {
            List<CollaborativeCompilationTaskVO> childList = childMap.getOrDefault(s, CollUtil.toList());
            List<CollaborativeCompilationTaskVO> sortChildList = childList.stream().sorted(Comparator.comparing(CollaborativeCompilationTaskVO::getSort).reversed()
            ).collect(Collectors.toList());
            childMap.put(s, sortChildList);
        }
        collaborativeCompilationTaskVOs.forEach(item -> item.setChildren(childMap.getOrDefault(item.getParentId(), CollUtil.toList())));
        return childMap;
    }


    public Map<String, List<ApprovalTaskPrePost>> initPreMap(List<String> taskIds) throws Exception {
        LambdaQueryWrapper<ApprovalTaskPrePost> preWrapper = new LambdaQueryWrapper<>(ApprovalTaskPrePost.class).in(ApprovalTaskPrePost::getTaskId, taskIds);
        List<ApprovalTaskPrePost> list = Optional.ofNullable(approvalTaskPrePostService.list(preWrapper)).orElse(new ArrayList<>());
        return list.stream().collect(Collectors.groupingBy(ApprovalTaskPrePost::getTaskId, Collectors.toList()));
    }

    private List<CollaborativeCompilationTaskVO> topTaskList(List<CollaborativeCompilationTaskVO> tasks) {
        List<CollaborativeCompilationTaskVO> collect = tasks.stream().filter(item -> item.getTopSort() > 0)
                .sorted(Comparator.comparing(CollaborativeCompilationTaskVO::getTopSort).reversed()
                        .thenComparing(Comparator.comparing(CollaborativeCompilationTaskVO::getSort).reversed()))
                .collect(Collectors.toList());
        //深copy
        List<CollaborativeCompilationTaskVO> result = BeanUtil.copyToList(collect, CollaborativeCompilationTaskVO.class);
        result.forEach(item -> item.setChildren(CollUtil.toList()));
        return result;
    }

    private List<CollaborativeCompilationTaskVO> sortContent(List<CollaborativeCompilationTaskVO> collaborativeCompilationTaskVOs) {
        return collaborativeCompilationTaskVOs.stream()
                .filter(item -> item.getParentId().equals("0"))
                .sorted(Comparator.comparing(CollaborativeCompilationTaskVO::getSort))
                .collect(Collectors.toList());
    }


    public List<CollaborativeCompilationTask> getTree(List<CollaborativeCompilationTask> filterList, List<CollaborativeCompilationTask> allList) throws Exception {
        List<CollaborativeCompilationTask> viewList = CollUtil.toList();
        if (CollUtil.isNotEmpty(filterList)) {
            viewList.addAll(filterList);
            filterList.forEach(item -> addParentCollaborativeCompilationTask(viewList, item, allList));
        }
        return viewList.stream().distinct().collect(Collectors.toList());
    }


    private void addParentCollaborativeCompilationTask(List<CollaborativeCompilationTask> viewList, CollaborativeCompilationTask collaborativeCompilationTask, List<CollaborativeCompilationTask> sourceList) {
        List<CollaborativeCompilationTask> collect = sourceList.stream().filter(item -> collaborativeCompilationTask.getParentId().equals(item.getId())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            viewList.add(collect.get(0));
            addParentCollaborativeCompilationTask(viewList, collect.get(0), sourceList);
        }
    }


    /**
     * 上移
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public synchronized Boolean up(String id) throws Exception {
        CollaborativeCompilationTask task = this.getById(id);
        List<CollaborativeCompilationTask> upTaskList = this.list(new LambdaQueryWrapper<>(CollaborativeCompilationTask.class)
                .eq(CollaborativeCompilationTask::getApprovalId, task.getApprovalId())
                .eq(CollaborativeCompilationTask::getParentId, task.getParentId())
                .lt(CollaborativeCompilationTask::getSort, task.getSort())
                .orderByDesc(CollaborativeCompilationTask::getSort));
        if (CollectionUtil.isEmpty(upTaskList)) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_MAX_SORT);
        }
        CollaborativeCompilationTask upTask = upTaskList.get(0);
        Integer upSort = upTask.getSort();
        upTask.setSort(task.getSort());
        task.setSort(upSort);
        return this.updateBatchById(Arrays.asList(task, upTask));
    }

    /**
     * 下移
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean down(String id) throws Exception {
        CollaborativeCompilationTask task = this.getById(id);
        List<CollaborativeCompilationTask> downTaskList = this.list(new LambdaQueryWrapper<>(CollaborativeCompilationTask.class)
                .eq(CollaborativeCompilationTask::getApprovalId, task.getApprovalId())
                .eq(CollaborativeCompilationTask::getParentId, task.getParentId())
                .gt(CollaborativeCompilationTask::getSort, task.getSort())
                .orderByAsc(CollaborativeCompilationTask::getSort));
        if (CollectionUtil.isEmpty(downTaskList)) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_MIN_SORT);
        }
        CollaborativeCompilationTask downTask = downTaskList.get(0);
        Integer downSort = downTask.getSort();
        downTask.setSort(task.getSort());
        task.setSort(downSort);
        return this.updateBatchById(Arrays.asList(task, downTask));
    }

    /**
     * 置顶
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public synchronized Boolean top(String id) throws Exception {
        CollaborativeCompilationTask collaborativeCompilationTask = this.getById(id);
        /**
         * 0:取消置顶
         * topSort越大优先级越高
         */
        Integer maxTop = 0;
        List<CollaborativeCompilationTask> dbs = this.list(new LambdaQueryWrapper<>(CollaborativeCompilationTask.class).eq(CollaborativeCompilationTask::getApprovalId, collaborativeCompilationTask.getApprovalId()).orderByDesc(CollaborativeCompilationTask::getTopSort));
        if (CollectionUtils.isEmpty(dbs)) {
            maxTop = 0;
        } else {
            maxTop = dbs.get(0).getTopSort();
        }
        collaborativeCompilationTask.setTopSort(maxTop + 1);
        return this.updateById(collaborativeCompilationTask);
    }

    /**
     * 任务下发
     *
     * @param issueDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean issue(TaskIssueDTO issueDTO) throws Exception {

        CollaborativeCompilationTask task = this.getById(issueDTO.getTaskId());
        List<CollaborativeCompilationTask> collaborativeCompilationTasks = getCollaborativeCompilationTasksByApprovalId(issueDTO.getApprovalId());

        if (!task.getParentId().equals("0")) {
            List<CollaborativeCompilationTask> view = new ArrayList<>();
            getParentTask(view, task, collaborativeCompilationTasks);
            List<CollaborativeCompilationTask> collaborativeCompilationTaskList = view.stream().filter(item -> item.getStatus().equals(Status.PENDING.getCode())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(collaborativeCompilationTaskList)) {
                throw new PMSException(PMSErrorCode.PMS_ERR, "请先下发父级任务");
            }
        }
        List<CollaborativeCompilationTask> child = new ArrayList<>();
        //获取任务所有子项
        getTasksAllChild(child, task, collaborativeCompilationTasks);

        List<CollaborativeCompilationTask> pendingTask = child.stream().filter(item -> item.getStatus().equals(Status.PENDING.getCode())
                && !item.getCircumstance().equals(Status.ISSUE_APPROVAL.getCode())).collect(Collectors.toList());
        if (task.getStatus().equals(Status.PENDING.getCode())) {
            pendingTask.add(task);
        }
        if (CollUtil.isEmpty(pendingTask)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "无可下发任务");
        }
        //        boolean nonPending = pendingTask.stream().anyMatch(task -> !Status.PENDING.getCode().equals(task.getStatus()));
//        if (nonPending) {
//            throw new BaseException(PMSErrorCode.PMS_ERROR_EXIST_NON_PENDING);
//        }
        issue(issueDTO, pendingTask);
        return true;
    }

    private void issue(TaskIssueDTO issueDTO, List<CollaborativeCompilationTask> tasks) {
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        for (CollaborativeCompilationTask task : tasks) {
            task.setStatus(Status.PUBLISHED.getCode());
            task.setCircumstance(Status.CIRCUMSTANCE_WAIT.getCode());
            task.setIssueTime(new Date());
            task.setOwnerId(task.getRspUser());
            task.setIssuedUser(currentUserId);
        }
        this.updateBatchById(tasks);
        //TODO 消息处理
        messageManager.sendMsg(MsgBusinessTypeEnum.TASK_SEND_DOWN, TaskMsgDTO.builder().collaborativeCompilationTasks(tasks).recipientIds(issueDTO.getBeNotifiedPersons()).build());
    }


    /**
     * 任务完成确认
     *
     * @param collaborativeCompilationTaskDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean finish(CollaborativeCompilationTaskDTO collaborativeCompilationTaskDTO) throws Exception {
        //查询其前置任务是否已经点击完成
        CollaborativeCompilationTask oldTask = this.getById(collaborativeCompilationTaskDTO.getId());
        String approval = oldTask.getApprovalId();
//        ApprovalSetUpVO detail = projectSetUpService.detail(approval, ApprovalSetUpService.prePlanTimeRule);
//        if (Objects.nonNull(detail) && StrUtil.equals(detail.getValue(), "true")) {
        List<ApprovalTaskPrePost> preTasks = taskPrePostService.list(new LambdaQueryWrapperX<>(ApprovalTaskPrePost.class).eq(ApprovalTaskPrePost::getTaskId, collaborativeCompilationTaskDTO.getId()));
        if (!CollectionUtils.isEmpty(preTasks)) {
            List<String> preTaskIds = preTasks.stream().map(ApprovalTaskPrePost::getPreTaskId).collect(Collectors.toList());
            List<CollaborativeCompilationTask> collaborativeCompilationTasks = this.list(new LambdaQueryWrapperX<>(CollaborativeCompilationTask.class).in(CollaborativeCompilationTask::getId, preTaskIds).ne(CollaborativeCompilationTask::getStatus, Status.FINISHED.getCode()));
            if (!CollectionUtils.isEmpty(collaborativeCompilationTasks)) {
                throw new BaseException(PMSErrorCode.PMS_ERROR_EXIST_PRE_NON_FINISHED, "该任务存在未完成的前置任务");
            }
        }
        // }



        TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
        boolean result = transactionTemplate.execute(transactionStatus -> {
            try {
                if (!Status.EXECUTING.getCode().equals(oldTask.getStatus())) {
                    throw new BaseException(PMSErrorCode.PMS_ERROR_NON_PUBLISHED);
                }
                if (!Status.CIRCUMSTANCE_OVERD.getCode().equals(oldTask.getCircumstance())) {
                    collaborativeCompilationTaskDTO.setCircumstance(null);
                }
                CollaborativeCompilationTask task = BeanCopyUtils.convertTo(collaborativeCompilationTaskDTO, CollaborativeCompilationTask::new);
                if (StrUtil.isNotBlank(task.getParentId()) && !StrUtil.equals(ROOT_ID, task.getParentId())) {
                  if(StrUtil.isNotBlank(oldTask.getProcessObject())&&(oldTask.getProcessObject().equals("taskDecomposition_2")||oldTask.getProcessObject().equals("taskDecomposition_3"))) {
                      task.setStatus(Status.EXECUTING.getCode());
                      task.setCircumstance(Status.COMPLETE_APPROVAL.getCode());
                      wfInstanceTaskManage.process(task);
                  }else{
                      task.setStatus(Status.CONFIRMED.getCode());
                  }
                    return this.updateById(task);
                }
                LambdaQueryWrapper<CollaborativeCompilationTask> wrapper = new LambdaQueryWrapper<>(CollaborativeCompilationTask.class);
                wrapper.eq(CollaborativeCompilationTask::getApprovalId, task.getApprovalId())
                        .like(CollaborativeCompilationTask::getParentChain, task.getId())
                        .orderByDesc(CollaborativeCompilationTask::getTopSort)
                        .orderByAsc(CollaborativeCompilationTask::getSort);
                List<CollaborativeCompilationTask> list = Optional.ofNullable(this.list(wrapper)).orElse(new ArrayList<>());
                boolean havNonFinish = list.stream().anyMatch(childTask -> !Status.FINISHED.getCode().equals(childTask.getStatus()));
                if (havNonFinish) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_HAVE_CHILD_NON_PUBLISHED);
                }

                if(StrUtil.isNotBlank(oldTask.getProcessObject())&&(oldTask.getProcessObject().equals("taskDecomposition_2")||oldTask.getProcessObject().equals("taskDecomposition_3"))) {
                    task.setStatus(Status.EXECUTING.getCode());
                    task.setCircumstance(Status.COMPLETE_APPROVAL.getCode());
                    wfInstanceTaskManage.process(task);
                }else{
                    task.setStatus(Status.CONFIRMED.getCode());
                }

                Boolean ret = this.updateById(task);

//                if (ApprovalConsts.FROM_ACCEPTANCE_FORM.equals(collaborativeCompilationTaskDTO.getFrom())) {
//                    // 如果项目任务的执行完成是在验收单内发起，则同步附件信息到验收单.
//                    acceptanceFormService.saveRelatedFiles(collaborativeCompilationTaskDTO.getFromId(), attachments);
//                }
                return ret;
            } catch (BaseException e) {
                throw e;
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        ////TODO 消息处理
        if (result) {
           // messageManager.clearToDo(MsgBusinessTypeEnum.TASK_MODIFY_URGE, collaborativeCompilationTaskDTO.getId());
            messageManager.clearToDo(MsgBusinessTypeEnum.TASK_SEND_DOWN, collaborativeCompilationTaskDTO.getId());
//            messageManager.sendMsg(MsgBusinessTypeEnum.PRE_FINISH,
//                    TaskMsgDTO.builder().collaborativeCompilationTaskList(CollUtil.toList(BeanCopyUtils.convertTo(collaborativeCompilationTaskDTO, CollaborativeCompilationTask::new))).build());
        }
        return result;
    }

    @Override
    public Boolean urgePlan(UrgePlanRequestDTO urgePlanRequestDTO) {
        //TODO 消息处理
//        CollaborativeCompilationTask db = this.getById(urgePlanRequestDTO.getCollaborativeCompilationTaskId());
//        messageManager.sendMsg(MsgBusinessTypeEnum.MODIFY_URGE,
//                TaskMsgDTO.builder()
//                        .collaborativeCompilationTaskList(Collections.singletonList(db))
//                        .extParam(new HashMap<>() {{
//                            put("remark", urgePlanRequestDTO.getRemark());
//                            put("notifyUserId", JSONUtil.toJsonStr(urgePlanRequestDTO.getNotifyUserId()));
//                        }})
//                        .build());

        return true;
    }

    @Override
    public Boolean updateActualBeginTime(String id) {
        CollaborativeCompilationTask collaborativeCompilationTask = this.getById(id);
        if (!collaborativeCompilationTask.getStatus().equals(Status.PUBLISHED.getCode())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "非已下发任务无法开始执行");
        }
        collaborativeCompilationTask.setId(id);
        collaborativeCompilationTask.setActualBeginTime(new Date());
        collaborativeCompilationTask.setStatus(Status.EXECUTING.getCode());
        return this.updateById(collaborativeCompilationTask);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean taskFallback(TaskFallBackDTO fallBackDTO) {
        CollaborativeCompilationTask byId = this.getById(fallBackDTO.getId());
        if (!Status.PUBLISHED.getCode().equals(byId.getStatus())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "只有已下发状态才能退回任务");
        }
        byId.setStatus(Status.PENDING.getCode());
        byId.setCircumstance(Status.CIRCUMSTANCE_FALLBACK.getCode());
        this.updateById(byId);

        messageManager.clearToDo(MsgBusinessTypeEnum.TASK_SEND_DOWN, byId.getId());
        return true;
    }


    /**
     * 取消置顶
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean unTop(String id) throws Exception {
        CollaborativeCompilationTask collaborativeCompilationTask = this.getById(id);
        /**
         * 0:取消置顶
         * topSort越大优先级越高
         */
        collaborativeCompilationTask.setTopSort(0);
        return this.updateById(collaborativeCompilationTask);
    }

    @Override
    public String revocation(String id) {
        CollaborativeCompilationTask collaborativeCompilationTask = this.getById(id);

        if (!Status.PUBLISHED.getCode().equals(collaborativeCompilationTask.getStatus())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "只有已下发状态才能撤回任务");
        }
        List<CollaborativeCompilationTask> child = getTasksChild(collaborativeCompilationTask);

        List<CollaborativeCompilationTask> errCollaborativeCompilationTask = child.stream().filter(item -> !item.getStatus().equals(Status.PUBLISHED.getCode())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(errCollaborativeCompilationTask)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "当前已有子级任务被执行，无法撤回");
        }
        child.add(collaborativeCompilationTask);
        for (CollaborativeCompilationTask task : child) {
            task.setStatus(Status.PENDING.getCode());
            task.setCircumstance(Status.CIRCUMSTANCE_FALLBACK.getCode());
        }
        this.updateBatchById(child);
        //TODO 消息处理
        for (CollaborativeCompilationTask task : child) {
            messageManager.clearToDo(MsgBusinessTypeEnum.TASK_SEND_DOWN, task.getId());
//            sendMessage(task.getId(), task.getName(), Collections.singletonList(collaborativeCompilationTask.getCreatorId()),
//                    collaborativeCompilationTask.getCreatorId(), collaborativeCompilationTask.getOrgId(), collaborativeCompilationTask.getPlatformId());
        }

        String returnMsg = "已撤回" + child.size() + "条任务";
        return returnMsg;
    }


    //根据项目获取父级项目
    private List<CollaborativeCompilationTask> getParentTasks(CollaborativeCompilationTask collaborativeCompilationTask) {
        List<CollaborativeCompilationTask> viewList = new ArrayList<>();
        List<CollaborativeCompilationTask> projectTasks = getCollaborativeCompilationTasksByApprovalId(collaborativeCompilationTask.getApprovalId());
        getParentTask(viewList, collaborativeCompilationTask, projectTasks);
        return viewList;
    }

    private void getParentTask(List<CollaborativeCompilationTask> viewList, CollaborativeCompilationTask collaborativeCompilationTask, List<CollaborativeCompilationTask> sourceList) {
        List<CollaborativeCompilationTask> collect = sourceList.stream().filter(item -> collaborativeCompilationTask.getParentId().equals(item.getId())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            viewList.add(collect.get(0));
            getParentTask(viewList, collect.get(0), sourceList);
        }
    }

    private List<CollaborativeCompilationTask> getTasksChild(CollaborativeCompilationTask collaborativeCompilationTask) {
        List<CollaborativeCompilationTask> viewList = new ArrayList<>();
        List<CollaborativeCompilationTask> projectTasks = getCollaborativeCompilationTasksByApprovalId(collaborativeCompilationTask.getApprovalId());
        getTasksAllChild(viewList, collaborativeCompilationTask, projectTasks);
        return viewList;
    }


    private void getTasksAllChild(List<CollaborativeCompilationTask> viewList, CollaborativeCompilationTask collaborativeCompilationTask, List<CollaborativeCompilationTask> sourceList) {
        List<CollaborativeCompilationTask> collect = sourceList.stream().filter(item -> collaborativeCompilationTask.getId().equals(item.getParentId())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            viewList.addAll(collect);
            for (CollaborativeCompilationTask collaborativeCompilationTask1 : collect) {
                getTasksAllChild(viewList, collaborativeCompilationTask1, sourceList);
            }
        }
    }

    private List<CollaborativeCompilationTask> getCollaborativeCompilationTasksByApprovalId(String approval) {
        List<CollaborativeCompilationTask> collaborativeCompilationTasks = this.list(new LambdaQueryWrapper<>(CollaborativeCompilationTask.class).eq(CollaborativeCompilationTask::getApprovalId, approval));
        return collaborativeCompilationTasks;
    }

    public List<ApprovalTaskPrePostVO> getPreTask(String approvalId, String taskId) {
        List<CollaborativeCompilationTask> schemes = CollUtil.toList();
        try {
            LambdaQueryWrapperX<CollaborativeCompilationTask> objectLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            objectLambdaQueryWrapperX.selectAll(CollaborativeCompilationTask.class);

            schemes = this.list(
                    objectLambdaQueryWrapperX.leftJoin(ApprovalTaskPrePost.class, ApprovalTaskPrePost::getPreTaskId, CollaborativeCompilationTask::getId)
                            .eq(ApprovalTaskPrePost::getTaskId, taskId)
                            .eq(CollaborativeCompilationTask::getApprovalId, approvalId)
            );
        } catch (Exception e) {
            log.warn("sql error : ", e);
        }
        return convertPreVo(schemes);
    }


    public List<ApprovalTaskPrePostVO> getPostTask(String approvalId, String taskId) {
        try {
            LambdaQueryWrapperX<CollaborativeCompilationTask> objectLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            objectLambdaQueryWrapperX.selectAll(CollaborativeCompilationTask.class);
            List<CollaborativeCompilationTask> schemes = this.list(objectLambdaQueryWrapperX
                    .leftJoin(ApprovalTaskPrePost.class, ApprovalTaskPrePost::getTaskId, CollaborativeCompilationTask::getId)
                    .eq(ApprovalTaskPrePost::getPreTaskId, taskId)
                    .eq(CollaborativeCompilationTask::getApprovalId, approvalId)
            );
            return convertPreVo(schemes);
        } catch (Exception e) {
            log.warn("sql error : ", e);
        }
        return null;
    }
    private List<ApprovalTaskPrePostVO> convertPreVo(List<CollaborativeCompilationTask> collaborativeCompilationTasks) {
        List<ApprovalTaskPrePostVO> vos = CollUtil.toList();
        if (CollUtil.isEmpty(collaborativeCompilationTasks)) {
            return vos;
        }
        collaborativeCompilationTasks.forEach(item -> {
            ApprovalTaskPrePostVO vo = new ApprovalTaskPrePostVO();
            vo.setTaskName(item.getName());
            vo.setTaskId(item.getId());
            vos.add(vo);
        });
        return vos;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean completeConfirmation(CollaborativeCompilationTaskDTO collaborativeCompilationTaskDTO) throws Exception {

        CollaborativeCompilationTask collaborativeCompilationTask = this.getById(collaborativeCompilationTaskDTO.getId());
        if(!collaborativeCompilationTask.getStatus().equals(Status.CONFIRMED.getCode())){
            throw new PMSException(PMSErrorCode.PMS_ERR, "非待确认任务无法完成确认");
        }
        if(collaborativeCompilationTaskDTO.getConfirmResult().equals("agree")){
            collaborativeCompilationTask.setId(collaborativeCompilationTaskDTO.getId());
            collaborativeCompilationTask.setStatus(Status.FINISHED.getCode());
            if(collaborativeCompilationTask.getEndTime().compareTo(collaborativeCompilationTask.getActualEndTime())>=0){
                collaborativeCompilationTask.setCircumstance(Status.CIRCUMSTANCE_COMPLETE_NORMAL.getCode());
            }else{
                collaborativeCompilationTask.setCircumstance(Status.CIRCUMSTANCE_COMPLETE_OVERD.getCode());
            }
        }
        if(collaborativeCompilationTaskDTO.getConfirmResult().equals("reject")){
            collaborativeCompilationTask.setId(collaborativeCompilationTaskDTO.getId());
            collaborativeCompilationTask.setStatus(Status.EXECUTING.getCode());
        }
        return this.updateById(collaborativeCompilationTask);
    }

    @Override
    @Transactional
    public Boolean getDocument(String modelId,String approvalId) throws Exception {
        GenerateNumberRequest request = new GenerateNumberRequest();
        request.setClazzName(ClassNameConstant.COLLABORATIVE_TASK);

        GenerateNumberRequest requestDoc = new GenerateNumberRequest();
        requestDoc.setClazzName(ClassNameConstant.COLLABORATIVE_DOCUMNET);

        List<CollaborativeCompilationDocument> documentList = collaborativeCompilationDocumentService.list(new LambdaQueryWrapper<>(CollaborativeCompilationDocument.class)
                .eq(CollaborativeCompilationDocument::getApprovalId, approvalId));
        //获取最大分支
        Integer maxSort = documentList.stream().map(CollaborativeCompilationDocument::getSort).max(Comparator.comparing(Integer::longValue)).orElse(0);
        List<DocumentDecomposition>  documentDecompositions = documentDecompositionService.list(new LambdaQueryWrapper<DocumentDecomposition>()
               .eq(DocumentDecomposition::getMainTableId,modelId).orderByAsc(DocumentDecomposition::getCreateTime));
       List<TaskDecomposition> taskDecompositions = taskDecompositionService.list(new LambdaQueryWrapper<TaskDecomposition>()
               .eq(TaskDecomposition::getMainTableId,modelId).orderByAsc(TaskDecomposition::getCreateTime));

        Integer sort= maxSort+1;
        ClassVO taskClassVO = classRedisHelper.getClassByClassName(CollaborativeCompilationTask.class.getSimpleName());
        ClassVO docClassVO = classRedisHelper.getClassByClassName(CollaborativeCompilationDocument.class.getSimpleName());

        List<CollaborativeCompilationDocument> collaborativeCompilationDocuments = new ArrayList<>();
        List<CollaborativeCompilationTask> collaborativeCompilationTasks = new ArrayList<>();

        Map<String,String> modelDocToDocMap = new HashMap<>();

        Map<String,String> modelDocToTaskMap = new HashMap<>();

        //引入任务
       for(DocumentDecomposition d:documentDecompositions){
           String docId = String.format("%s%s", Objects.isNull(docClassVO) ? "" : docClassVO.getCode(), IdUtil.getSnowflakeNextIdStr());
           CollaborativeCompilationDocument collaborativeCompilationDocument =new CollaborativeCompilationDocument();
           collaborativeCompilationDocument.setApprovalId(approvalId);
           collaborativeCompilationDocument.setName(d.getName());
           collaborativeCompilationDocument.setContent(d.getContent());
           collaborativeCompilationDocument.setWriteRequire(d.getRemark());
           collaborativeCompilationDocument.setSort(sort);
           collaborativeCompilationDocument.setId(docId);
           if(d.getParentId().equals("0")){
               collaborativeCompilationDocument.setParentId("0");
           }
           String number =  numberApiService.generate(requestDoc);
           collaborativeCompilationDocument.setNumber(number);
           collaborativeCompilationDocument.setModelParentId(d.getParentId());
           collaborativeCompilationDocument.setModelId(d.getId());
           collaborativeCompilationDocuments.add(collaborativeCompilationDocument);
           modelDocToDocMap.put(d.getId(),docId);
           sort++;
       }
       for(CollaborativeCompilationDocument c:collaborativeCompilationDocuments){
           if(StrUtil.isBlank(c.getParentId())) {
               c.setParentId(modelDocToDocMap.get(c.getModelParentId()));
           }
       }
        Map<String,String> modelTaskToTaskMap = new HashMap<>();
        Map<String,String> modelTaskToDocMap = new HashMap<>();
        Integer taskSort= maxSort+1;
        //引入文档
        for(TaskDecomposition taskDecomposition:taskDecompositions){
            String taskId = String.format("%s%s", Objects.isNull(taskClassVO) ? "" : taskClassVO.getCode(), IdUtil.getSnowflakeNextIdStr());

            CollaborativeCompilationTask collaborativeCompilationTask = new CollaborativeCompilationTask();
            if(taskDecomposition.getParentId().equals("0")){
                collaborativeCompilationTask.setParentId("0");
            }
            collaborativeCompilationTask.setDurationDays(1);
            Calendar calendar = Calendar.getInstance();
            if(ObjectUtil.isEmpty(collaborativeCompilationTask.getBeginTime())){
                calendar.setTime(new Date()); // 设置Calendar实例的时间为当前日期
            }else{
                calendar.setTime(collaborativeCompilationTask.getBeginTime());
            }
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            collaborativeCompilationTask.setBeginTime(calendar.getTime());
            calendar.add(Calendar.DATE, 1); // 给日期加一天
            collaborativeCompilationTask.setEndTime(calendar.getTime());
            collaborativeCompilationTask.setModelParentId(taskDecomposition.getParentId());
            collaborativeCompilationTask.setRspUser(taskDecomposition.getRspUser());
            collaborativeCompilationTask.setRspDept(taskDecomposition.getRspDept());
            collaborativeCompilationTask.setName(taskDecomposition.getName());
            collaborativeCompilationTask.setId(taskId);
            String number =  numberApiService.generate(request);
            collaborativeCompilationTask.setNumber(number);
            collaborativeCompilationTask.setSort(taskSort);
            collaborativeCompilationTask.setApprovalId(approvalId);
            collaborativeCompilationTask.setProcessObject(taskDecomposition.getProcessObject());
            collaborativeCompilationTask.setTopSort(0);
            collaborativeCompilationTask.setCircumstance(0);
            collaborativeCompilationTask.setProcessInstances(taskDecomposition.getProcessInstances());
            collaborativeCompilationTask.setStatus(Status.PENDING.getCode());
            collaborativeCompilationTasks.add(collaborativeCompilationTask);
            modelTaskToTaskMap.put(taskDecomposition.getId(),taskId);
            if(StrUtil.isNotBlank(taskDecomposition.getProcessInstances())) {
                modelTaskToDocMap.put(taskDecomposition.getProcessInstances(), taskId);
            }
            if(StrUtil.isNotBlank(taskDecomposition.getProcessInstances())) {
                modelDocToTaskMap.put(taskDecomposition.getProcessInstances(),taskDecomposition.getId());
            }
            sort++;
        }
        for(CollaborativeCompilationTask c:collaborativeCompilationTasks){
            if(StrUtil.isBlank(c.getParentId())) {
                c.setParentId(modelTaskToTaskMap.get(c.getModelParentId()));
            }
            c.setProcessInstances(modelDocToDocMap.get(c.getProcessInstances()));
        }

        for(CollaborativeCompilationDocument c:collaborativeCompilationDocuments){
            String modelTaskId = modelDocToTaskMap.get(c.getModelId());
            c.setTaskId(modelTaskToTaskMap.get(modelTaskId));
        }
        collaborativeCompilationDocumentService.saveBatch(collaborativeCompilationDocuments);
        this.saveBatch(collaborativeCompilationTasks);
        return true;
    }

    @Override
    public Boolean transfer(CollaborativeCompilationTaskDTO collaborativeCompilationTaskDTO) {
        String id =  collaborativeCompilationTaskDTO.getId();
        CollaborativeCompilationTask collaborativeCompilationTask = this.getById(id);
        UserVO userVO = userRedisHelper.getUserById(collaborativeCompilationTaskDTO.getRspUser());
        collaborativeCompilationTask.setRspUser(userVO.getId());
        collaborativeCompilationTask.setRspDept(userVO.getOrganizations().get(0).getId());
        collaborativeCompilationTask.setReasonTransfer(collaborativeCompilationTaskDTO.getReasonTransfer());
        return this.updateById(collaborativeCompilationTask);
    }

//    private void setOrgInfo(CollaborativeCompilationTask p, List<DeptVO> orgs) {
//        if (CollUtil.isEmpty(orgs)) {
//            return;
//        }
//        DeptVO deptVO = orgs.get(0);
//        if ("20".equals(deptVO.getType())) {
//            p.setRspDept(deptVO.getId());
//        } else if ("30".equals(deptVO.getType())) {
//            DeptVO deptOrg = getParentOrgId(deptVO);
//            p.setRspDept(deptOrg.getId());
//        } else if ("40".equals(deptVO.getType())) {
//            DeptVO sectionOrg = getParentOrgId(deptVO);
//            DeptVO deptOrg = getParentOrgId(sectionOrg);
//            p.setRspDept(deptOrg.getId());
//        }
//
//    }
//    private DeptVO getParentOrgId(DeptVO org) {
//        String parentId = org.getParentId();
//        DeptVO organization = deptRedisHelper.getDeptById(parentId);
//        return Objects.nonNull(organization) ? organization : new DeptVO();
//    }


    @Override
    public List<UserVO> getUser() {
        List<UserVO> userVOs = userRedisHelper.getAllUser();
        return userVOs;
    }


    @Override
    public List<DataStatusVO> getStatus() {
        List<DataStatusVO> dataStatusVOS = statusRedisHelper.getStatusInfoListByClassName(ClassNameConstant.COLLABORATIVE_TASK);
        return dataStatusVOS;
    }


    public String  getContent(String id) throws Exception{
        CollaborativeCompilationTask collaborativeCompilationTask = this.getById(id);

        if (ObjectUtil.isNotEmpty(collaborativeCompilationTask)) {
            Configuration cfg = new Configuration(Configuration.VERSION_2_3_31);
            cfg.setClassForTemplateLoading(getClass(), "/");
            cfg.setDefaultEncoding("UTF-8");
            Map<String, Object> dataModel = new HashMap<>();
            if(StrUtil.isNotBlank(collaborativeCompilationTask.getProcessObject())) {
                if (collaborativeCompilationTask.getProcessObject().equals("taskDecomposition_4")) {
                    List<ProjectApprovalRiskPlanVO> data = projectApprovalRiskPlanService.getListPlan(collaborativeCompilationTask.getApprovalId());
                    if (CollUtil.isNotEmpty(data)) {
                        Template template = cfg.getTemplate("ftl/riskTable.ftl");
                        dataModel.put("list", data);
                        // 创建字符串写入器
                        StringWriter writer = new StringWriter();
                        // 合并模板和数据模型
                        template.process(dataModel, writer);
                        // System.out.println(writer.toString());
                        collaborativeCompilationTask.setContent(writer.toString());
                    }
                }

                if (collaborativeCompilationTask.getProcessObject().equals("taskDecomposition_3")) {
                    ProjectApprovalEstimateVO projectApprovalEstimateVO = projectApprovalEstimateMaterialService.getMaterialListByApprovalId(collaborativeCompilationTask.getApprovalId());
                    List<ProjectApprovalEstimateExpenseSubjectVO> projectApprovalEstimateExpenseSubjectVOS = projectApprovalEstimateExpenseSubjectService.getExpenseSubjectListByApprovalId(collaborativeCompilationTask.getApprovalId());
                    if (CollUtil.isNotEmpty(projectApprovalEstimateExpenseSubjectVOS) && ObjectUtil.isNotEmpty(projectApprovalEstimateVO)) {
                        Template template = cfg.getTemplate("ftl/estimateTable.ftl");
                        dataModel.put("ExpenseSubjectVOS", projectApprovalEstimateExpenseSubjectVOS);
                        dataModel.put("EstimateMaterialVOs", projectApprovalEstimateVO.getProjectApprovalEstimateMaterialVOList());
                        dataModel.put("materialFee", projectApprovalEstimateVO.getMaterialFee());
                        // 创建字符串写入器
                        StringWriter writer = new StringWriter();
                        // 合并模板和数据模型
                        template.process(dataModel, writer);
                        // 输出生成的HTML
                        collaborativeCompilationTask.setContent(writer.toString());
                    }
                }

                if (collaborativeCompilationTask.getProcessObject().equals("taskDecomposition_2")) {

                    List<ProjectApprovalIncomeVO> data = projectApprovalIncomeService.getList(collaborativeCompilationTask.getApprovalId());
                    if (CollUtil.isNotEmpty(data)) {
                        Template template = cfg.getTemplate("ftl/inComeTable.ftl");
                        dataModel.put("list", data);

                        // 创建字符串写入器
                        StringWriter writer = new StringWriter();
                        // 合并模板和数据模型
                        template.process(dataModel, writer);
                        // 输出生成的HTML
                        collaborativeCompilationTask.setContent(writer.toString());
                    }
                }

                if (collaborativeCompilationTask.getProcessObject().equals("taskDecomposition_5")) {
                    List<ProjectApprovalMilestoneVO> data = projectApprovalMilestoneService.getList(collaborativeCompilationTask.getApprovalId());
                    if (CollUtil.isNotEmpty(data)) {
                        Template template = cfg.getTemplate("ftl/milestoneTable.ftl");
                        dataModel.put("list", data);
                        // 创建字符串写入器
                        StringWriter writer = new StringWriter();
                        // 合并模板和数据模型
                        template.process(dataModel, writer);
                        // 输出生成的HTML
                        collaborativeCompilationTask.setContent(writer.toString());
                    }
                }


                if (collaborativeCompilationTask.getProcessObject().equals("taskDecomposition_6")) {
                    List<ProductPlanVO> data = productPlanService.getList(collaborativeCompilationTask.getApprovalId());
                    if (CollUtil.isNotEmpty(data)) {
                        Template template = cfg.getTemplate("ftl/productTable.ftl");
                        dataModel.put("list", data);
                        // 创建字符串写入器
                        StringWriter writer = new StringWriter();
                        // 合并模板和数据模型
                        template.process(dataModel, writer);
                        // 输出生成的HTML
                        collaborativeCompilationTask.setContent(writer.toString());
                    }
                }
                if (collaborativeCompilationTask.getProcessObject().equals("taskDecomposition_7")) {

                }
                if (StrUtil.isNotBlank(collaborativeCompilationTask.getProcessInstances())) {
                    CollaborativeCompilationDocument collaborativeCompilationDocument = collaborativeCompilationDocumentService.getById(collaborativeCompilationTask.getProcessInstances());
                    if (ObjectUtil.isNotEmpty(collaborativeCompilationDocument)) {
                        collaborativeCompilationDocument.setContent(collaborativeCompilationTask.getContent());
                        collaborativeCompilationDocumentService.updateById(collaborativeCompilationDocument);
                    }
                }
                this.updateById(collaborativeCompilationTask);
            }
        }
        return collaborativeCompilationTask.getContent();
    }

    @Override
    public Boolean editContent(CollaborativeCompilationTaskDTO collaborativeCompilationTaskDTO) throws Exception {
        CollaborativeCompilationTask collaborativeCompilationTask = BeanCopyUtils.convertTo(collaborativeCompilationTaskDTO, CollaborativeCompilationTask::new);
        this.updateById(collaborativeCompilationTask);
        return true;
    }
}
