package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * YearInvestmentScheme Entity对象
 *
 * <AUTHOR>
 * @since 2023-05-16 14:21:48
 */
@ApiModel(value = "YearInvestmentSchemeVO对象", description = "年度投资计划")
@Data
public class YearInvestmentSchemeVO extends ObjectVO implements Serializable {

    /**
     * 是否申报投资计划
     */
    @ApiModelProperty(value = "是否申报投资计划")
    private Boolean closeFlag;

    /**
     * 计划编号
     */
    @ApiModelProperty(value = "计划编号")
    private String number;


    /**
     * 计划名称
     */
    @ApiModelProperty(value = "计划名称")
    private String name;


    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    private String projectNumber;


    /**
     * 项目状态
     */
    @ApiModelProperty(value = "项目状态")
    private String projectStatusName;


    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;


    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 项目处室
     */
    @ApiModelProperty(value = "项目处室")
    private String rspDeptName;

    /**
     * 项目负责人
     */
    @ApiModelProperty(value = "项目负责人")
    private String rspUserName;

    /**
     * 概算
     */
    @ApiModelProperty(value = "概算")
    private String estimate;

    /**
     * 概算ID
     */
    @ApiModelProperty(value = "概算ID")
    private String estimateId;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    private String yearName;

    /**
     * 总体预算
     */
    @ApiModelProperty(value = "总体预算")
    private String overallBudget;


    /**
     * 总体实际
     */
    @ApiModelProperty(value = "总体实际")
    private String overallReality;


    /**
     * 立项金额
     */
    @ApiModelProperty(value = "立项金额")
    private String projectAmount;


    /**
     * 合同金额
     */
    @ApiModelProperty(value = "合同金额")
    private String contractAmount;

    /**
     * 累计Y-2年下达投资计划
     */
    @ApiModelProperty(value = "累计Y-2年下达投资计划")
    private String cutOffGiveY_2;

    /**
     * 累计Y-2年投资计划完成
     */
    @ApiModelProperty(value = "累计Y-2年投资计划完成")
    private String cutOffCompleteY_2;

    /**
     * 累计Y-1年下达投资计划
     */
    @ApiModelProperty(value = "累计Y-1年下达投资计划")
    private String cutOffGiveY_1;

    /**
     * 累计Y-1年投资计划完成
     */
    @ApiModelProperty(value = "累计Y-1年投资计划完成")
    private String cutOffCompleteY_1;


    /**
     * 累计Y年下达投资计划
     */
    @ApiModelProperty(value = "累计Y年下达投资计划")
    private String cutOffGiveY;

    /**
     * 累计Y年投资计划完成
     */
    @ApiModelProperty(value = "累计Y年投资计划完成")
    private String cutOffCompleteY;


    /**
     * Y-1年投资计划
     */
    @ApiModelProperty(value = "Y-1年投资计划")
    private String lastYear;


    /**
     * Y-1年投资计划预计完成
     */
    @ApiModelProperty(value = "Y-1年投资计划预计完成")
    private BigDecimal lastYearComplete=new BigDecimal("0");

    /**
     * Y-1年执行情况说明
     */
    @ApiModelProperty(value = "Y-1年执行情况说明")
    private String lastYearDoDesc;

    /**
     * 建筑工程
     */
    @ApiModelProperty(value = "建筑工程")
    private BigDecimal architecture=new BigDecimal("0");

    /**
     * 安装工程
     */
    @ApiModelProperty(value = "安装工程")
    private BigDecimal installation=new BigDecimal("0");

    /**
     * 设备投资
     */
    @ApiModelProperty(value = "设备投资")
    private BigDecimal device=new BigDecimal("0");

    /**
     * 其他费用
     */
    @ApiModelProperty(value = "其他费用")
    private BigDecimal other=new BigDecimal("0");


    /**
     * 分月计划
     */
    @ApiModelProperty(value = "分月计划")
    private List<MonthInvestmentSchemeVO> monthInvestmentSchemes;


    /**
     * Y年形象进度
     */
    @ApiModelProperty(value = "Y年形象进度")
    private String yearProcess;

    /**
     * Y+1年投资计划
     */
    @ApiModelProperty(value = "Y+1年投资计划")
    private BigDecimal nextOneYear=new BigDecimal("0");

    /**
     * Y+2年投资计划
     */
    @ApiModelProperty(value = "Y+2年投资计划")
    private BigDecimal nextTwoYear=new BigDecimal("0");


    /**
     * Y+3年投资计划
     */
    @ApiModelProperty(value = "Y+3年投资计划")
    private BigDecimal nextThreeYear=new BigDecimal("0");


    /**
     * Y+4年投资计划
     */
    @ApiModelProperty(value = "Y+4年投资计划")
    private BigDecimal nextFourYear=new BigDecimal("0");


    /**
     * Y+5年投资计划
     */
    @ApiModelProperty(value = "Y+5年投资计划")
    private BigDecimal nextFiveYear=new BigDecimal("0");


    /**
     * 投资计划Id
     */
    @ApiModelProperty(value = "投资计划Id")
    private String investmentId;


    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    private String projectId;


    /**
     * 年度投资计划
     */
    @ApiModelProperty(value = "年度投资计划")
    private String total;


    /**
     * 年度投资计划执行
     */
    @ApiModelProperty(value = "年度投资计划执行")
    private String totalDo;

    /**
     * 年度调整后投资计划
     */
    @ApiModelProperty(value = "年度调整后投资计划")
    private String totalChange;


    /**
     * 调整前Id
     */
    @ApiModelProperty(value = "调整前Id")
    private String oldId;

    /**
     * 总体进度执行情况
     */
    @ApiModelProperty(value = "总体进度执行情况")
    private String totalProcess;


    @ApiModelProperty(value = "本年预算")
    private String currentYearBudget;


    @ApiModelProperty(value = "本年预算执行")
    private String currentYearBudgetDo;
}
