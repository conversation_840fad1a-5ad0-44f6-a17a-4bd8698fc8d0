package com.chinasie.orion.service;

import com.chinasie.orion.domain.entity.DeliverGoalsToDeliverable;
import com.chinasie.orion.mybatis.service.OrionBaseService;

import java.util.List;

/**
 * <p>
 * DeliverGoalsToDeliverable 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29 17:50:25
 */
public interface DeliverGoalsToDeliverableService extends OrionBaseService<DeliverGoalsToDeliverable> {

    /**
     * ied关联交付物
     * @param deliverGoalsId
     * @param deliverIdList
     * @return
     * @throws Exception
     */
    Boolean saveRelation(String deliverGoalsId, List<String> deliverIdList) throws Exception;

    /**
     * ied获取关联的交付物
     * @param deliverGoalsIdList
     * @return
     * @throws Exception
     */
    List<DeliverGoalsToDeliverable> getListByDeliverGoals(List<String> deliverGoalsIdList) throws Exception;

    /**
     * 删除交付物关联关系
     * @param deliverGoalsId
     * @param deliverIdList
     * @return
     * @throws Exception
     */
    Boolean deleteRelation(String deliverGoalsId, List<String> deliverIdList) throws Exception;

}
