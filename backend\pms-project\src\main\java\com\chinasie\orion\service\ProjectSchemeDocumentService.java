package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.ProjectSchemeDocumentDTO;
import com.chinasie.orion.domain.entity.ProjectSchemeDocument;
import com.chinasie.orion.domain.vo.FileExtVO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

/**
 * <p>
 * ProjectSchemeDocument 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-26 13:33:08
 */
public interface ProjectSchemeDocumentService extends OrionBaseService<ProjectSchemeDocument> {

    /**
     *  新增
     *
     * * @param projectSchemeId
     */
    Boolean create(String projectSchemeId,List<FileDTO> files)  throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean deleteFiles(String projectSchemeId,List<String> fileIds)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     */
    Page<FileExtVO> pages(String projectSchemeId, Page<ProjectSchemeDocumentDTO> pageRequest) throws Exception;

}
