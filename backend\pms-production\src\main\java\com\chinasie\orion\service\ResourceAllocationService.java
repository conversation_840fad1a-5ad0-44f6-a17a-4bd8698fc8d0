package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.person.ObjJobDTO;
import com.chinasie.orion.domain.dto.source.SearchDTO;
import com.chinasie.orion.domain.vo.resource.MaterialSourceAllocationVO;
import com.chinasie.orion.domain.vo.resource.PersonSourceAllocationVO;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/09/05/14:13
 * @description:
 */
public interface ResourceAllocationService {
    /**
     *  人员资源统计
     * @param searchDTO
     * @return
     */
    PersonSourceAllocationVO personAllocationCount(SearchDTO searchDTO);

    /**
     *  保存人员作业时间
     * @param personJobDTOList
     * @return
     */
    Boolean savePersonJobDate(List<ObjJobDTO> personJobDTOList);

    /**
     *  保存物资作业时间
     * @param materialJobDTOList
     * @return
     */
    Boolean saveMaterialJobDate(List<ObjJobDTO> materialJobDTOList);

    /**
     *  物资资源统计
     * @param searchDTO
     * @return
     */
    MaterialSourceAllocationVO materialAllocationCount(SearchDTO searchDTO);

    PersonSourceAllocationVO personOverlapAllocationCount(SearchDTO searchDTO);

    /**
     *  物资重叠
     * @param searchDTO
     * @return
     */
    MaterialSourceAllocationVO materialOverlapAllocationCount(SearchDTO searchDTO);

    Integer materialOverlapDays(SearchDTO searchDTO);

    Integer personOverlapDays(SearchDTO searchDTO);
}
