<template>
  <Layout class="deliverablePage">
    <BasicTable
      ref="tableRef"
      :row-selection="pageType==='page'?{ type: 'checkbox' }:false"
      :columns="columns"
      :data-source="dataSource"
      :show-index-column="false"
      :pagination="false"
      row-key="id"
      :resize-height-offset="10"
      :loading="loading"
    />
  </Layout>
  <NewButtonModal
    v-if="pageType==='page'"
    :btn-object-data="btnConfig"
    @clickType="clickType"
  />
  <ViewDetails
    v-if="viewDetails.visible"
    :data="viewDetails"
  />
  <SearchAll
    v-if="searchAll.visible"
    :data="searchAll"
    @submit="submitSearch"
  />
  <Edit
    v-if="edit.visible"
    :data="edit"
    @submit="submitEdit"
  />
  <Deliver
    v-if="deliver.visible"
    :data="deliver"
  />
  <SearchModal
    v-if="pageType==='page'"
    @register="searchRegister"
    @searchEmit="searchEmit"
  />
</template>
<script>
import {
  Layout, BasicTable, isPower, useDrawer,
} from 'lyra-component-vue3';
import { message, Modal } from 'ant-design-vue';
import NewButtonModal from '/@/views/pms/projectLaborer/componentsList/buttonModal/newButtonModal.vue';
import {
  onMounted, reactive, toRefs, ref, inject, computed, h,
} from 'vue';
import Api from '/@/api';
import ViewDetails from './components/ViewDetails.vue';
import SearchAll from './components/SearchAll.vue';
import Edit from './components/Edit.vue';
import Deliver from '../../deliverDetails/index.vue';
import router from '/@/router';
import SearchModal from './SearchModal.vue';
export default {
  name: 'Index',
  components: {
    SearchAll,
    Deliver,
    Edit,
    NewButtonModal,
    Layout,
    BasicTable,
    ViewDetails,
    SearchModal,
  },
  props: {
    pageType: {
      type: String,
      default: 'page',
    },
    formId: {
      type: String,
      default: '',
    },
    projectId: {
      type: String,
      default: '',
    },
  },
  emits: ['checkDetails'],
  setup(props, { emit }) {
    const [searchRegister, { openDrawer: openSearchDrawer }] = useDrawer();
    const state = reactive({
      tableRef: ref(),
      edit: {},
      deliver: {},
      formDel: {
        idList: [],
        sourceId: props.formId,
        type: 1,
      },
      searchAll: {},
      viewDetails: {},
      columns: [
        {
          title: '编号',
          dataIndex: 'number',
          align: 'left',
          minWidth: 200,
        },
        {
          title: '交付物名称',
          dataIndex: 'name',
          align: 'left',
          minWidth: 200,
          customRender({ record, text }) {
            return h(
              'span',
              {
                class: computed(() => (isPower('RWX_container_button_09', state.powerData) && props.pageType === 'page' ? 'action-btn' : '')).value,
                title: text,
                onClick(e) {
                  if (isPower('RWX_container_button_09', state.powerData) && props.pageType === 'page') {
                    router.push({
                      name: 'DeliverDetails',
                      query: {
                        projectId: props.formId,
                        id: record.id,
                      },
                    });
                  }
                  e.stopPropagation();
                },
              },
              text,
            );
          },

        },
        {
          title: '计划交付物时间',
          dataIndex: 'predictDeliverTime',
          width: 150,
        },
        {
          title: '实际交付物时间',
          dataIndex: 'deliveryTime',
          width: 150,
        },
        {
          title: '所属任务',
          dataIndex: 'planName',
          width: 150,
        },
        {
          title: '状态',
          dataIndex: 'statusName',
          width: 100,
        },
        {
          title: '负责人',
          dataIndex: 'principalName',
          width: 150,
        },
        {
          title: '修改时间',
          dataIndex: 'modifyTime',
          width: 100,
        },
      ],
      dataSource: [],
      loading: false,

      powerData: [],
    });
    state.powerData = inject('powerData');
    const state6 = reactive({
      btnConfig: {
        check: { show: computed(() => isPower('RWX_container_button_08', state.powerData)) },
        open: { show: computed(() => isPower('RWX_container_button_09', state.powerData)) },
        edit: { show: computed(() => isPower('RWX_container_button_11', state.powerData)) },
        addNew: { show: computed(() => isPower('RWX_container_button_10', state.powerData)) },
        remove: { show: computed(() => isPower('RWX_container_button_12', state.powerData)) },
        search: { show: computed(() => isPower('RWX_container_button_13', state.powerData)) },
      },
    });
    function getPage(obj = {}) {
      state.loading = true;
      const love = {
        className: 'Plan',
        moduleName: '项目管理-计划管理-项目计划-交付物', // 模块名称
        type: 'GET', // 操作类型
        remark: '获取/搜索了【交付物列表】',
      };
      const url = `deliverable/list?planId=${props.formId}`;
      new Api('/pms', love)
        .fetch(obj, url, 'POST')
        .then((res) => {
          state.dataSource = res;
          state.loading = false;
          state.tableRef.clearSelectedRowKeys();
        })
        .catch(() => {
          state.loading = false;
        });
    }
    function searchEmit(data) {
      console.log('----- data -----', data);
      getPage(data);
    }
    function isSelectCheck(type) {
      const selectedRowKeys = state.tableRef.getSelectRowKeys();
      if (selectedRowKeys.length === 0) {
        message.warning('请选择数据进行操作');
        return false;
      }
      if (selectedRowKeys.length === 1) {
        return selectedRowKeys[0];
      }
      if (selectedRowKeys.length > 1) {
        if (type === 'remove') {
          return true;
        }
        message.warning('请选择一条数据进行操作');
        return false;
      }
    }
    function handleCheck(id) {
      emit('checkDetails', id);
      return;
      const love = {
        id,
        className: 'Plan',
        moduleName: '项目管理-计划管理-项目计划-交付物', // 模块名称
        type: 'GET', // 操作类型
        remark: `查看了【${id}】`,
      };
      new Api('/pms', love).fetch('', `deliverable/${id}`).then((res) => {
        state.viewDetails = {
          visible: true,
          title: '查看信息',
          form: res,
        };
      });
    }
    function clickType(type) {
      if (type === 'open') {
        const id = isSelectCheck();
        if (id) {
          // const projectId = props.projectId;
          // state.deliver = {
          //   visible: true,
          //   projectId,
          //   id,
          // };
          router.push({
            name: 'DeliverDetails',
            query: {
              projectId: props.formId,
              id,
            },
          });
        }
      }
      if (type === 'search') {
        openSearchDrawer(true);
        // state.searchAll = {
        //   visible: true,
        //   form: {
        //     keyWord: state.form.keyWord
        //   }
        // };
      }
      if (type === 'check') {
        const id = isSelectCheck();
        id && handleCheck(id);
      }
      if (type === 'remove') {
        const id = isSelectCheck(type);
        if (id) {
          Modal.confirm({
            title: '确认提示',
            content: '请确认是否移除此选中数据？',
            onOk() {
              isConfirm();
            },
          });
        }
      }
      if (type === 'addNew') {
        state.edit = {
          visible: true,
          title: '添加交付物',
          type,
          form: {
            planId: props.formId,
            projectId: props.projectId,
            name: undefined,
            principalId: undefined,
            principalName: undefined,
            predictDeliverTime: undefined,
            remark: undefined,
          },
        };
      }
      if (type === 'edit') {
        const id = isSelectCheck(type);
        if (id) {
          new Api('/pms').fetch('', `deliverable/${id}`).then((res) => {
            state.edit = {
              visible: true,
              title: '修改交付物',
              type,
              form: res,
            };
          });
        }
      }
    }
    function isConfirm() {
      const love = {
        className: 'Plan',
        moduleName: '项目管理-计划管理-项目计划-交付物',
        type: 'DELETE',
        remark: `删除了【${state.tableRef.getSelectRowKeys()}】`,
      };
      new Api('/pms', love)
        .fetch(state.tableRef.getSelectRowKeys(), 'deliverable/batch', 'DELETE')
        .then(() => {
          message.success('操作成功');
          getPage();
        });
    }
    function submitSearch(val) {
      // state.form.keyWord = val;
      // getPage();
    }

    function submitEdit(val) {
      state.edit.visible = false;
      if (val) {
        getPage();
      }
    }

    onMounted(() => {
      getPage();
    });
    return {
      ...toRefs(state),
      ...toRefs(state6),
      clickType,
      getPage,
      submitSearch,
      submitEdit,
      searchRegister,
      searchEmit,
    };
  },
};
</script>

<style scoped>
.deliverablePage {
  width: calc(100% - 60px);
  flex: 1;
  height: 500px;
}
</style>
