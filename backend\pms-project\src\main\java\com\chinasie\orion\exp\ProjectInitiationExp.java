package com.chinasie.orion.exp;

import com.chinasie.orion.permission.core.core.type.ValueExpType;
import com.chinasie.orion.permission.core.exp.IExp;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import org.apache.commons.math3.analysis.function.Exp;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/07/26/14:10
 * @description:
 */
@Component
public class ProjectInitiationExp implements IExp {
    @Override
    public ValueExpType group() {
        return ValueExpType.CUSTOM;
    }

    @Override
    public String expName() {
        return "项目立项权限-当前人";
    }

    @Override
    public List<String> exp(String s) {
        return Arrays.asList(CurrentUserHelper.getCurrentUserId());
    }

    @Override
    public Boolean apply() {
        return Boolean.TRUE;
    }
}
