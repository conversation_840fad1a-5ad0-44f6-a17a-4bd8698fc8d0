<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements. See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership. The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License. You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied. See the License for the
  specific language governing permissions and limitations
  under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-plugins</artifactId>
    <version>42</version>
    <relativePath />
  </parent>

  <artifactId>maven-jar-plugin</artifactId>
  <version>3.4.1</version>
  <packaging>maven-plugin</packaging>

  <name>Apache Maven JAR Plugin</name>
  <description>Builds a Java Archive (JAR) file from the compiled project classes and resources.</description>

  <contributors>
    <contributor>
      <name>Jerome Lacoste</name>
      <email><EMAIL></email>
      <organization>CoffeeBreaks</organization>
      <organizationUrl>http://www.coffeebreaks.org</organizationUrl>
      <roles>
        <role>Java Developer</role>
      </roles>
      <timezone>+1</timezone>
    </contributor>
  </contributors>

  <prerequisites>
    <maven>${mavenVersion}</maven>
  </prerequisites>

  <scm>
    <connection>scm:git:https://gitbox.apache.org/repos/asf/maven-jar-plugin.git</connection>
    <developerConnection>scm:git:https://gitbox.apache.org/repos/asf/maven-jar-plugin.git</developerConnection>
    <tag>maven-jar-plugin-3.4.1</tag>
    <url>https://github.com/apache/maven-jar-plugin/tree/${project.scm.tag}</url>
  </scm>
  <issueManagement>
    <system>JIRA</system>
    <url>https://issues.apache.org/jira/browse/MJAR</url>
  </issueManagement>
  <ciManagement>
    <system>Jenkins</system>
    <url>https://ci-maven.apache.org/job/Maven/job/maven-box/job/maven-jar-plugin/</url>
  </ciManagement>
  <distributionManagement>
    <site>
      <id>apache.website</id>
      <url>scm:svn:https://svn.apache.org/repos/asf/maven/website/components/${maven.site.path}</url>
    </site>
  </distributionManagement>

  <properties>
    <javaVersion>8</javaVersion>
    <mavenVersion>3.6.3</mavenVersion>
    <mavenArchiverVersion>3.6.2</mavenArchiverVersion>
    <project.build.outputTimestamp>2024-04-16T21:03:47Z</project.build.outputTimestamp>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>commons-io</groupId>
        <artifactId>commons-io</artifactId>
        <version>2.16.1</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <dependencies>
    <!-- Maven Core -->
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-plugin-api</artifactId>
      <version>${mavenVersion}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-core</artifactId>
      <version>${mavenVersion}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-artifact</artifactId>
      <version>${mavenVersion}</version>
      <scope>provided</scope>
    </dependency>

    <!-- plugins annotations -->
    <dependency>
      <groupId>org.apache.maven.plugin-tools</groupId>
      <artifactId>maven-plugin-annotations</artifactId>
      <scope>provided</scope>
    </dependency>

    <!-- Maven shared -->
    <dependency>
      <groupId>org.apache.maven.shared</groupId>
      <artifactId>file-management</artifactId>
      <version>3.1.0</version>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-archiver</artifactId>
      <version>${mavenArchiverVersion}</version>
    </dependency>

    <!-- plexus -->
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-utils</artifactId>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-archiver</artifactId>
      <version>4.9.2</version>
    </dependency>

    <!-- Other used dependencies -->
    <dependency>
      <groupId>javax.inject</groupId>
      <artifactId>javax.inject</artifactId>
      <version>1</version>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.7.36</version>
    </dependency>

    <!-- Test -->
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.plugin-testing</groupId>
      <artifactId>maven-plugin-testing-harness</artifactId>
      <version>4.0.0-alpha-2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>4.11.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-junit-jupiter</artifactId>
      <version>4.11.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-xml</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <resources>
      <resource>
        <filtering>true</filtering>
        <directory>src/main/filtered-resources</directory>
      </resource>
    </resources>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.rat</groupId>
          <artifactId>apache-rat-plugin</artifactId>
          <configuration>
            <excludes combine.children="append">
              <!--
                ! The manifest specification does not describe any method for writing comments into the manifest.
              -->
              <exclude>src/it/mjar-71-01/src/main/resources/META-INF/MANIFEST.MF</exclude>
              <exclude>src/it/mjar-71-02/src/main/resources/META-INF/MANIFEST.MF</exclude>
            </excludes>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>

    <plugins>
      <plugin>
        <groupId>org.eclipse.sisu</groupId>
        <artifactId>sisu-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>run-its</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-invoker-plugin</artifactId>
            <configuration>
              <goals>
                <goal>clean</goal>
                <goal>package</goal>
              </goals>
              <showErrors>true</showErrors>
            </configuration>
            <executions>
              <execution>
                <id>integration-test</id>
                <goals>
                  <goal>install</goal>
                  <goal>integration-test</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
