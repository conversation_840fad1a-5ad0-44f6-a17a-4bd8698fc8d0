package com.chinasie.orion.repository;
import com.chinasie.orion.domain.entity.FixedAssets;
import org.apache.ibatis.annotations.Mapper;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * NcfFormGVWdVZFnw Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 14:36:24
 */
@Mapper
public interface FixedAssetsMapper extends OrionBaseMapper<FixedAssets> {

    /**
     * 获取下次鉴定时间距离心在刚好两个月的数据
     * @return 结果
     */
    @Select("SELECT * FROM pmsx_fixed_assets WHERE DATE(next_verification_time) = DATE(DATE_ADD(CURRENT_DATE, INTERVAL 2 MONTH)) and is_need_verification = 1 and logic_status = 1")
    List<FixedAssets> selectTwoMonth();
}

