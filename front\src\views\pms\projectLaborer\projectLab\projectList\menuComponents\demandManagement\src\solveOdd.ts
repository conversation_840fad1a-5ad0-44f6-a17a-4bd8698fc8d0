import { roleListApi } from '/@/views/pms/projectLaborer/api/riskManege';
import { priorityLevelApi } from '/@/views/pms/projectLaborer/api/demandManagement';

import { simplePlanTreeListApi, isTypeApi, addOddPlanApi } from '/@/views/pms/projectLaborer/api/planList';
export const otherOddApi = {
  zkEdit: addOddPlanApi,
  zkAdd: addOddPlanApi,
  zkPeopel: roleListApi,
  addId: 'itemidandArr',
  //   zkItemDetails: itemDetailsApi
  //   zkForType: demandSimplePageApi
};
export const solveOddArr = [
  {
    type: 'treeSelect',
    label: '所属计划',
    field: 'parentId',
    optionsValue: [],
    getOptionFn: simplePlanTreeListApi,
    addId: 'arr',
    // rules: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
    apiConfig: {
      size: 'large',
      placeholder: '请选择所属计划',
    },
  },
  {
    type: 'input',
    label: '关联需求',
    field: 'xxxx',
    rules: [],
    fixed: true,
    apiConfig: {
      size: 'large',
      disabled: true,
    },
  },
  {
    type: 'input',
    label: '名称',
    field: 'name',
    rules: [
      {
        required: true,
        message: '请输入名称',
        trigger: 'blur',
      },
    ],
    apiConfig: {
      size: 'large',
      placeholder: '请输入名称',
    },
  },

  {
    type: 'select',
    label: '所属类型',
    field: 'planType',
    options: [],
    getOptionFn: isTypeApi,
    addId: 'projectid',
    apiConfig: {
      placeholder: '请选择所属类型',
      size: 'large',
    },
  },
  {
    type: 'selectSearchPeople',
    label: '负责人',
    field: 'principalId',
    radioOptions: [],
    addValue: 'ok',
    apiConfig: {
      placeholder: '请选择负责人',

      size: 'large',
    },
  },
  {
    type: 'select',
    label: '优先级',
    field: 'priorityLevel',
    getOptionFn: priorityLevelApi,
    options: [],

    apiConfig: {
      placeholder: '请选择优先级',

      size: 'large',
    },
  },
  //   {
  //     type: 'select',
  //     label: '优先级',
  //     field: 'type',

  //     getOptionFn: demandTypeApi,
  //     options: [],
  //     apiConfig: {
  //       size: 'large',
  //       placeholder: '请选择优先级'
  //     }
  //   },
  {
    type: 'dataPicker',
    label: '开始日期',
    field: 'planPredictStartTime',
    // format: true,
    format2: true,

    apiConfig: {
      placeholder: '请选择开始时间',

      size: 'large',
    },
  },
  {
    type: 'dataPicker',
    label: '结束日期',
    field: 'planPredictEndTime',

    // format: true,
    format2: true, // yyyy-MM-dd HH:mm:ss
    apiConfig: {
      placeholder: '请选择结束日期',

      size: 'large',
    },
  },
  //   {
  //     type: 'selectSearchPeople',
  //     label: '接收人',
  //     field: 'recipient',

  //     radioOptions: [],
  //     apiConfig: {
  //       placeholder: '请选择接收人',

  //       size: 'large'
  //     }
  //   },

  {
    type: 'inputIcon',
    label: '预估工时',
    field: 'manHour',

    apiConfig: {
      placeholder: '请输入工时',
      size: 'large',
      suffix: '小时',
    },
  },
  {
    type: 'textarea',
    label: '描述',
    field: 'remark',
    apiConfig: {
      placeholder: '请输入描述',
      maxlength: 255,
      rows: 4,
      size: 'large',
    },
  },
];
