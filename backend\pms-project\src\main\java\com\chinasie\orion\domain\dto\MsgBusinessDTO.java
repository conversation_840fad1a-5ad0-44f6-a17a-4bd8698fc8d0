package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * IcmDeliverBillDTO 传递单DTO对象
 *
 * <AUTHOR> sie
 * @since 2021-06-08
 */
@ApiModel(value = "MsgBusinessDTO对象", description = "业务变更")
public class MsgBusinessDTO implements Serializable {
    /**
     * businessId
     */
    @ApiModelProperty("业务id")
    private String businessId;
     /**
     * className
     */
    @ApiModelProperty("业务className")
    private String className;

    /**
     * 变更后状态
     */
    @ApiModelProperty("状态")
    private Integer status;

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
