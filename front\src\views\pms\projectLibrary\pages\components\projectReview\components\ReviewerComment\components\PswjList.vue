<script setup lang="ts">
import {
  OrionTable,
  BasicTableAction,
  IOrionTableActionItem,
  BasicButton,
  openSelectUserModal,
  BasicImport,
  useModal,
  downloadByData, isPower,
} from 'lyra-component-vue3';
import {
  computed, ComputedRef, ref, Ref, onMounted, inject, reactive, h,
} from 'vue';
import {
  Modal, InputSearch, Input, message,
} from 'ant-design-vue';
import { useRoute, useRouter } from 'vue-router';
import { formatActionsPowerAuth } from '../../../utils';
import {
  add, edit, listAppraisal, remove, importExcel, importExcelCancel, importExcelCheck,
} from '/@/views/pms/api/reviewOpinion';

const route = useRoute();
const router = useRouter();
const dataId = computed(() => route.params?.id);
const loading: Ref = ref(false);
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const detailsPowerData: any = inject('detailsPowerData', reactive([]));
const downloadFileObj = {
  url: '/pms/reviewOpinion/download/excel/tpl',
  method: 'GET',
};
const fileTypeList = [
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-excel.sheet.macroEnabled.12',
];
const [registerModal, { openModal }] = useModal();

const columns = [
  {
    title: '操作',
    dataIndex: 'actions',
    width: 160,
    fixed: 'right',
    slots: { customRender: 'actions' },
  },
  {
    title: '提出人',
    dataIndex: 'presentedUserName',
    minWidth: 200,
    slots: { customRender: 'presentedUserName' },
  },
  {
    title: '意见/建议',
    dataIndex: 'opinion',
    minWidth: 350,
    slots: { customRender: 'opinion' },
  },
  {
    title: '是否技术问题',
    dataIndex: 'isTechnicalIssues',
    customRender: ({ text }) => (text ? '是' : '否'),
  },
  {
    title: '采纳情况',
    dataIndex: 'adoptionSituation',
  },
  {
    title: '整改情况',
    dataIndex: 'overallDescription',
  },
  {
    title: '问题编号',
    dataIndex: 'questionNumber',
    customRender: ({ record, text }) => h('span', {
      class: 'action-btn',
      onClick: () => {
        router.push({
          name: 'PMSQuestionManagementDetails',
          params: {
            id: record.questionId,
          },
        });
      },
    }, text),
  },
];

const dataSource = ref([]);
const tableOptions = {
  rowSelection: {
    onChange(keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  showToolButton: false,
  isSpacing: true,
  pagination: false,
  showSmallSearch: false,
  showTableSetting: false,
  canResize: false,
  columns,
  dataSource: computed(() => dataSource.value),
};

const toolButtons: ComputedRef<Record<string, any>[]> = computed(() => [
  {
    event: 'add',
    text: '添加意见',
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
    code: 'PMS_XMPSXQ_BUTTON_REVIEW_ADD',
  },
  {
    event: 'import',
    text: '导入',
    icon: 'sie-icon-daoru',
    code: 'PMS_XMPSXQ_BUTTON_REVIEW_IMPORT',
  },
  {
    event: 'export',
    text: '导出',
    icon: 'sie-icon-daochu',
    code: 'PMS_XMPSXQ_BUTTON_REVIEW_EXPORT',
  },
  {
    event: 'batchDelete',
    text: '删除',
    icon: 'sie-icon-shanchu',
    code: 'PMS_XMPSXQ_BUTTON_REVIEW_DELETE',
  },
]);

async function toolClick(record: Record<string, any>) {
  const { event } = record;
  switch (event) {
    case 'add':
      try {
        loading.value = true;
        await add(1, dataId.value);
        updateTable();
      } finally {
        loading.value = false;
      }
      break;
    case 'import':
      handleImport();
      break;
    case 'export':
      handleExport();
      break;
    case 'batchDelete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除已勾选的数据吗？',
        onOk: () => deleteApi(selectedRows.value.map((item) => item.id)),
      });
      break;
  }
}

const actions: IOrionTableActionItem[] = formatActionsPowerAuth([
  {
    text: '删除',
    event: 'delete',
    code: 'PMS_XMPSXQ_BUTTON_REVIEW_ROW_DELETE',
  },
], detailsPowerData);

function actionClick(actionItem: { event: string }, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'delete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除这条数据吗？',
        onOk: () => deleteApi([record.id]),
      });
      break;
  }
}

async function updateTable() {
  try {
    selectedRows.value = [];
    loading.value = true;
    dataSource.value = await listAppraisal(dataId.value);
  } finally {
    loading.value = false;
  }
}

function deleteApi(ids: string[]) {
  return new Promise((resolve) => {
    remove(ids).then(() => {
      updateTable();
    }).finally(() => {
      resolve('');
    });
  });
}

function getButtonProps(item) {
  if (item.event === 'batchDelete') {
    item.disabled = !selectedRows.value.length;
  }
  return item;
}

function onClickInputSearch(record) {
  openSelectUserModal([], {
    selectType: 'radio',
    async okHandle(users) {
      record.presentedUser = users[0].id;
      record.presentedUserName = users[0]?.name;
      await edit(record);
      message.success('操作成功');
    },
  });
}

async function editFunc(record) {
  await edit(record);
  message.success('操作成功');
}

const handleImport = () => {
  openModal(true, {});
};
const handleExport = () => {
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk() {
      const url = `/pms/reviewOpinion/export/excel?mainTableId=${dataId.value}`;
      const msg = '导出处理完成，现在开始下载';
      downloadByData(url, [], '', 'POST', true, false, msg);
    },
  });
};
// 导入校验
const requestBasicImport = async (data) => {
  let formData = new FormData();
  formData.append('file', data[0]);
  return importExcelCheck(formData);
};
// 数据导入
const requestSuccessImport = (successKey) => importExcel(successKey, dataId.value);
// 取消导入
const changeImportModalFlag = async (data) => {
  if (!data.visible && data.successImportFlag) {
    // 导入成功刷新数据
    updateTable();
  } else if (!data.visible && !data.successImportFlag && data.succ) {
    await importExcelCancel(data.succ);
    message.success('取消导入成功');
  }
};
onMounted(() => {
  updateTable();
});
</script>

<template>
  <OrionTable
    v-loading="loading"
    :options="tableOptions"
  >
    <template #toolbarLeft>
      <template
        v-for="button in toolButtons"
        :key="button.event"
      >
        <BasicButton
          v-if="isPower(button.code, detailsPowerData)"
          v-bind="getButtonProps(button)"
          @click="toolClick(button)"
        >
          {{ button.text }}
        </BasicButton>
      </template>
    </template>
    <template #presentedUserName="{record}">
      <InputSearch
        v-model:value="record.presentedUserName"
        placeholder="请选择提出人"
        style="width: 100%;"
        :readonly="true"
        :disabled="!isPower('PMS_XMPSXQ_BUTTON_REVIEW_ROW_EDIT', detailsPowerData)"
        @click="onClickInputSearch(record)"
        @search="onClickInputSearch(record)"
      />
    </template>
    <template #opinion="{record}">
      <Input
        v-model:value="record.opinion"
        placeholder="请输入具体描述"
        style="width: 100%;"
        :disabled="!isPower('PMS_XMPSXQ_BUTTON_REVIEW_ROW_EDIT', detailsPowerData)"
        :maxlength="1000"
        @blur="editFunc(record)"
      />
    </template>
    <template #actions="{record}">
      <BasicTableAction
        :actions="actions"
        :record="record"
        @actionClick="actionClick($event,record)"
      />
    </template>
  </OrionTable>
  <!-- 导入 -->
  <BasicImport
    :requestBasicImport="requestBasicImport"
    :requestSuccessImport="requestSuccessImport"
    :downloadFileObj="downloadFileObj"
    :fileTypeList="fileTypeList"
    @register="registerModal"
    @change-import-modal-flag="changeImportModalFlag"
  />
</template>

<style scoped lang="less">

</style>
