<script setup lang="ts">
import {
  BasicGridView, DataStatusTag, isPower, OrionTable,
} from 'lyra-component-vue3';
import {
  CSSProperties, h, inject, reactive, ref, Ref,
} from 'vue';
import { useRouter } from 'vue-router';
import Api from '/@/api';

const router = useRouter();
const detailsData: Record<string, any> = inject('detailsData');
const powerData: Ref = inject('powerData');
const gridViewProps = reactive({
  gap: '10px 20px',
  list: [
    {
      label: '项目序列号',
      field: 'projectNumber',
    },
    {
      label: '项目名称',
      field: 'projectName',
      gridColumn: '2/4',
    },
    {
      label: '工单号',
      field: 'number',
    },
    {
      label: '作业名称',
      field: 'name',
      gridColumn: '1/3',
    },
    {
      label: '系统条件',
      field: 'norO',
    },
    {
      label: '大修轮次',
      field: 'repairRound',
    },
    {
      label: '工作中心',
      field: 'workCenter',
    },
    {
      label: '功能位置',
      field: 'functionalLocation',
    },
    {
      label: '是否自带工具',
      field: 'isCarryTool',
      isBoolean: true,
    },
    {
      label: '是否高风险',
      field: 'isHighRisk',
      isBoolean: true,
    },
    {
      label: '是否重大项目',
      field: 'isMajorProject',
      isBoolean: true,
    },
    {
      label: '防异物等级',
      field: 'antiForfeignLevelName',
    },
    {
      label: '首次执行',
      field: 'firstExecuteName',
    },
    {
      label: '新人参与',
      field: 'newParticipants',
      isBoolean: true,
    },
    {
      label: '作业负责人',
      field: 'rspUserName',
    },
    {
      label: '监督人员',
      field: 'supervisoryStaffName',
    },
    {
      label: '管理人员',
      field: 'managePersonName',
    },
    {
      label: '责任中心',
      field: 'rspDeptName',
    },
    {
      label: '作业部门',
      field: 'jobDeptName',
    },
    {
      label: '作业基地',
      field: 'jobBaseName',
    },
    {
      label: '工作地址',
      field: 'workPlace',
    },
    {
      label: '计划开工时间',
      field: 'beginTime',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '计划工期',
      field: 'workDuration',
    },
    {
      label: '实际开工时间',
      field: 'actualBeginTime',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '实际完成时间',
      field: 'actualEndTime',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '进展阶段',
      field: 'dataStatus',
      valueRender({ text, record }) {
        return text ? h(DataStatusTag, { statusData: text }) : '';
      },
    },
    {
      label: '作业状态',
      field: 'phase',
    },
    {
      label: '所属计划',
      field: 'planSchemeName',
      valueRender({ text, record }) {
        if (record.planSchemeId && isPower('PMS_DXZYXQNEW_container_01_button_03', powerData.value)) {
          return h('div', {
            class: 'flex-te action-btn',
            onClick() {
              router.push({
                name: 'ProPlanDetails',
                params: {
                  id: record.planSchemeId,
                },
              });
            },
          }, '查看');
        }
        return text || '--';
      },
    },
    {
      label: '风险级别及判断标准',
      field: 'riskLevel',
      slotName: 'riskLevel',
      gridColumn: '1/5',
      class: 'flex-ver',
    },
  ],
  dataSource: detailsData,
});

const tableWrapStyle: CSSProperties = {
  height: '400px',
  overflow: 'hidden',
};

const tableRef: Ref = ref();

const tableOptions = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  isSpacing: false,
  pagination: false,
  api: () => new Api('/pms/jobHeightRisk/list').fetch({
    query: {
      jobNumber: detailsData.number,
    },
  }, '', 'POST'),
  columns: [
    {
      title: '风险级别',
      dataIndex: 'riskLevel',
      width: '20%',
    },
    {
      title: '风险类型',
      dataIndex: 'judgmentStandards',
      width: '30%',
    },
    {
      title: '判断标准',
      dataIndex: 'jobContent',
    },
  ],
};
</script>

<template>
  <BasicGridView v-bind="gridViewProps">
    <template #riskLevel>
      <div :style="tableWrapStyle">
        <OrionTable
          ref="tableRef"
          style="position: relative"
          :options="tableOptions"
        />
      </div>
    </template>
  </BasicGridView>
</template>

<style scoped lang="less">
.details-grid {
  padding: 0;
}
</style>
