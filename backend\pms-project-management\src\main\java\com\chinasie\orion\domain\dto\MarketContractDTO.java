package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.domain.entity.MarketContractCustContact;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * MarketContract DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-28 21:47:43
 */
@ApiModel(value = "MarketContractDTO对象", description = "市场合同")
@Data
@ExcelIgnoreUnannotated
public class MarketContractDTO extends ObjectDTO implements Serializable {

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 0)
    @NotEmpty(message = "合同编号不能为空")
    private String number;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称 ", index = 1)
    @NotEmpty(message = "合同名称不能为空")
    private String name;

    /**
     * 关联框架合同id
     */
    @ApiModelProperty(value = "关联框架合同id")
    private String frameContractId;


    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型")
    @ExcelProperty(value = "合同类型 ", index = 2)
    @NotEmpty(message = "合同类型不能为空")
    private String contractType;

    /**
     * 报价单id
     */
    @ApiModelProperty(value = "报价单id")
    private String quoteId;

    /**
     * 报价编号
     */
    @ApiModelProperty(value = "报价单编号")
    @ExcelProperty(value = "报价单编号 ", index = 3)
    private String quoteNumber;


    /**
     * 技术负责人
     */
    @ApiModelProperty(value = "技术负责人")
    @ExcelProperty(value = "技术负责人 ", index = 5)
    @NotEmpty(message = "技术负责人不能为空")
    private String techRspUser;

    /**
     * 承担部门
     */
    @ApiModelProperty(value = "承担部门")
    @ExcelProperty(value = "承担部门 ", index = 6)
    private String techRspDept;

    /**
     * 合同金额
     */
    @ApiModelProperty(value = "合同金额")
    @ExcelProperty(value = "合同金额 ", index = 7)
    private BigDecimal contractAmt;

    /**
     * 框架合同金额
     */
    @ApiModelProperty(value = "框架合同金额")
    private BigDecimal frameContractAmt;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    @ExcelProperty(value = "币种 ", index = 8)
    @NotEmpty(message = "币种不能为空")
    private String currency;


    /**
     * 关联交易审批
     */
    @ApiModelProperty(value = "关联交易审批")
    @ExcelProperty(value = "关联交易审批 ", index = 11)
    private Boolean relTransAppr;

    /**
     * 交易审批单号
     */
    @ApiModelProperty(value = "交易审批单号")
    @ExcelProperty(value = "交易审批单号 ", index = 12)
    private String transApprNumber;

    /**
     * 交易审批id
     */
    @ApiModelProperty(value = "交易审批id")
    @ExcelProperty(value = "交易审批id ", index = 12)
    private String transApprId;

    /**
     * 主要内容
     */
    @ApiModelProperty(value = "主要内容")
    @ExcelProperty(value = "主要内容 ", index = 13)
    private String content;

    /**
     * 质保等级
     */
    @ApiModelProperty(value = "质保等级")
    @ExcelProperty(value = "质保等级 ", index = 18)
    private String qualityLevel;


    /**
     * 商务接口人
     */
    @ApiModelProperty(value = "商务接口人")
    @NotEmpty(message = "商务接口人不能为空")
    private String commerceRspUser;

    /**
     * 关闭日期
     */
    @ApiModelProperty(value = "关闭日期")
    private Date closeDate;

    /**
     * 关闭用户id
     */
    @ApiModelProperty(value = "关闭用户id")
    private String closeUserId;

    /**
     * 关闭用户id
     */
    @ApiModelProperty(value = "关闭用户名称")
    private String closeUserName;

    /**
     * 关闭方式
     */
    @ApiModelProperty(value = "关闭方式")
    private String closeType;

    /**
     * 是否需要采购
     */
    @ApiModelProperty(value = "是否需要采购")
    private Boolean isPurchase;

    /**
     * 需求Id
     */
    @ApiModelProperty(value = "需求Id")
    @ExcelProperty(value = "需求Id ", index = 30)
    private String requirementId;

    /**
     * 合同签署人id
     */
    @ApiModelProperty(value = "合同签署人id")
    private String contractSignUserId;

    /**
     * 合同签署人名称
     */
    @ApiModelProperty(value = "合同签署人名称")
    private String contractSignUserName;

    /**
     * 上传附件
     */
    @ApiModelProperty(value = "上传附件")
    @Valid
    @ExcelIgnore
    private List<FileDTO> fileList;

    /**
     * 乙方签约主体信息
     */
    @ApiModelProperty(value = "乙方签约主体信息")
    private List<ContractSupplierSignedSubjectDTO> supplierSignedSubjectList;

    /**
     * 甲方签约主体信息
     */
    @ApiModelProperty(value = "甲方签约主体信息")
    private List<ContractOurSignedSubjectDTO> ourSignedSubjectList;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private String custPersonId;

    @ApiModelProperty(value = "需求来源类型")
    @ExcelIgnore
    private String resSource;

    @ApiModelProperty(value = "关联合同名称")
    @ExcelIgnore
    private String frameContractName;


    @ApiModelProperty(value = "子订单名称")
    @ExcelIgnore
    private String sonContractName;

    @ApiModelProperty(value = "子订单编号")
    @ExcelIgnore
    private String sonContractNumber;

    @ApiModelProperty(value = "合同类型查询")
    @ExcelIgnore
    private List<String> contractTypesQ;

    @ApiModelProperty(value = "客户-客户关系。编码")
    private String custGroupInOut;

    @ApiModelProperty(value = "客户-业务收入类型。编码")
    private String custBusRevenueType;

    @ApiModelProperty(value = "业务类型")
    private String businessType;

    @ApiModelProperty(value = "客户-销售业务分类。客户关系 + 所属行业")
    private String custSaleBusType;

    @ApiModelProperty(value = "所级负责人")
    private String officeLeader;

    @ApiModelProperty(value = "客户-联系人")
    private List<MarketContractCustContact> custContacts;

    /**
    * 合同获取方式
    */
    @ApiModelProperty(value = "合同获取方式")
    private String contractMethod;
//
//    @ApiModelProperty(value = "业务类型")
//    private String businessType;

    /**
     * 优先级1低2中3高
     */
    @ApiModelProperty(value = "优先级1低2中3高")
    @ExcelProperty(value = "优先级1低2中3高 ", index = 31)
    private String priority;

    @ApiModelProperty(value = "优先级排序0升1降序")
    private String prioritySort;



    @ApiModelProperty(value = "框架下子订单类型")
    private String subOrderType;

    /**
     * 已回款金额
     */
    @ApiModelProperty(value = "已回款金额")
    private BigDecimal returnedMoney;

    /**
     * 不关联交易原因
     */
    @ApiModelProperty(value = "不关联交易原因")
    private String unrelatedReason;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String cusName;

}
