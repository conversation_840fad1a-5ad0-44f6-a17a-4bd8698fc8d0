<script setup lang="ts">
import {
  openDrawer, openFile, OrionTable, isPower,
} from 'lyra-component-vue3';
import {
  h, inject, ref, Ref,
} from 'vue';
import { Popover } from 'ant-design-vue';
import dayjs from 'dayjs';
import Api from '/@/api';
import TableDrawer from '/@/views/pms/technicalStaffAllocation/components/TableDrawer.vue';

const powerCodePrefix: string = inject('powerCodePrefix');
const powerData: Ref = inject('powerData');
const detailsData: Record<string, any> = inject('detailsData');
const tableRef: Ref = ref();
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const tableOptions = {
  rowSelection: {
    onChange(_keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  height: 300,
  showTableSetting: false,
  showSmallSearch: false,
  showToolButton: false,
  columns: [
    {
      title: '培训名称',
      dataIndex: 'trainName',
    },
    {
      title: '培训基地',
      dataIndex: 'baseName',
    },
    {
      title: '培训课时',
      dataIndex: 'lessonHour',
    },
    {
      title: '培训完成日期',
      dataIndex: 'endDate',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '培训讲师',
      dataIndex: 'trainLecturer',
    },
    {
      title: '到期日期',
      dataIndex: 'expireTime',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '是否等效',
      dataIndex: 'isEquivalent',
      customRender({ text }) {
        return text ? '是' : '否';
      },
    },
    {
      title: '等效认定基地',
      dataIndex: 'equRecordVOList',
      customRender({ text }) {
        if (isPower(`${powerCodePrefix}_container_03_01_button_01`, powerData.value)) {
          return h('div', {
            class: 'flex-te action-btn',
            onClick() {
              openDrawer({
                title: '培训等效认定',
                width: 1000,
                content() {
                  return h(TableDrawer, {
                    data: text || [],
                  });
                },
                footer: {
                  isOk: false,
                  canText: '返回',
                },
              });
            },
          }, '查看');
        }
        return '';
      },
    },
    {
      title: '培训内容',
      dataIndex: 'content',
    },
    {
      title: '培训记录',
      dataIndex: 'fileVOList',
      customRender({ text }) {
        if (isPower(`${powerCodePrefix}_container_03_01_button_02`, powerData.value)) {
          return h(Popover, { title: '附件' }, {
            default: () => h('div', { class: 'flex-te action-btn' }, '附件列表'),
            content: () => (text instanceof Array ? text : [])?.map((item: any) => h('p', {
              class: 'action-btn',
              onClick() {
                openFile(item);
              },
            }, item.name)),
          });
        }
        return '';
      },
    },
  ],
  api: (params: Record<string, any>) => new Api('/pms/personTrainInfoRecord').fetch({
    ...params,
    query: {
      userCode: detailsData?.userCode,
    },
    power: {
      containerCode: 'table-list-container-042a7f-TrainingInformation-ZnMsXieB',
      pageCode: 'list-container-042a7f-TrainingInformation',
    },
  }, 'page', 'POST'),
};
</script>

<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
  />
</template>

<style scoped lang="less">

</style>
