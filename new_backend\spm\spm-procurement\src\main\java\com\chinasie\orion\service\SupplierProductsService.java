package com.chinasie.orion.service;


import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.domain.dto.SupplierProductsDTO;
import com.chinasie.orion.domain.entity.SupplierProducts;
import com.chinasie.orion.domain.vo.SupplierProductsVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * SupplierProducts 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
public interface SupplierProductsService extends OrionBaseService<SupplierProducts> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    SupplierProductsVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param supplierProductsDTO
     */
    String create(SupplierProductsDTO supplierProductsDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param supplierProductsDTO
     */
    Boolean edit(SupplierProductsDTO supplierProductsDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     *
     * @param mainTableId
     */
    Page<SupplierProductsVO> pages(String mainTableId, Page<SupplierProductsDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<SupplierProductsVO> vos) throws Exception;

    /**
     * 根据供应商查询
     * <p>
     * * @param searchConditions
     * * @param response
     */
    List<SupplierProductsVO> getByCode(String code) throws Exception;
}
