package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * TrainEquivalentRelation Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-04 17:09:19
 */
@TableName(value = "pmsx_train_equivalent_relation")
@ApiModel(value = "TrainEquivalentRelationEntity对象", description = "员工培训等效关联表")
@Data
@Deprecated
public class TrainEquivalentRelation extends  ObjectEntity  implements Serializable{

    /**
     * 等效认证Id
     */
    @ApiModelProperty(value = "等效认证Id")
    @TableField(value = "equivalent_id")
    private String equivalentId;

    /**
     * 培训人员Id
     */
    @ApiModelProperty(value = "培训人员Id")
    @TableField(value = "train_person_id")
    private String trainPersonId;

}
