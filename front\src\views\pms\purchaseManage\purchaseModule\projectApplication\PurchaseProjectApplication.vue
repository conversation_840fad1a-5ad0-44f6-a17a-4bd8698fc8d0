<script setup lang="ts">
import { useRouter } from 'vue-router';
import {
  reactive, Ref, ref, h, watchEffect, computed,
} from 'vue';
import Api from '/@/api';
import {
  Modal, RangePicker, Space,
} from 'ant-design-vue';
import {
  Layout, OrionTable, BasicTableAction, IOrionTableActionItem, BasicButton, downloadByData, isPower,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
import {
  get as loadGet, isEmpty, isBoolean, cloneDeep,
} from 'lodash-es';
import { openFormDrawer, parseBooleanToRender, parsePriceByNumber } from '../utils';
import MoneyRow from '../components/MoneyRow.vue';
import PAForm from './components/PAForm.vue';

const router = useRouter();
const tableRef = ref();
const rowMoney = reactive([
  {
    key: 'total',
    title: '申请条数',
    value: '',
    suffix: '条',
  },
  {
    key: 'allMoney',
    title: '申请金额',
    value: '',
    suffix: '元',
  },
]);
const tableOptions = {
  showToolButton: false,
  filterConfigName: 'purchaseProjectApplication_filter001',
  isSpacing: true,
  pagination: {},
  rowSelection: {
  },
  smallSearchField: ['name'],
  filterConfig: {
    fields: [
      {
        field: 'purchasePlanCode',
        fieldName: '采购计划号',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'wbsId',
        fieldName: 'WBS编号',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'code',
        fieldName: '采购申请编号',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'applicantDept',
        fieldName: '申请部门',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'projectEndTime',
        fieldName: '采购立项完成时间段',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterface: null,
        referenceInterfaceMethod: null,
        referenceInterfaceParams: null,
        component: 'Select',
        hidden: false,
        constValue: null,
        fieldNames: null,
        searchFieldName: null,
        optionsCustomRender({ filterItem, filterMethods, groupRelation }) {
          return h(RangePicker, {
            style: {
              width: '100%',
            },
            onChange(date: any) {
              filterMethods.setFieldValue(filterItem.field, date, groupRelation);
            },
            valueFormat: 'YYYY-MM-DD',
          });
        },
      },
    ],
  },
  columns: [
    {
      title: '操作',
      dataIndex: 'actions',
      width: 80,
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
    {
      title: '采购申请单编码',
      dataIndex: 'code',
      width: 150,
    },
    {
      title: '申请单名称',
      dataIndex: 'name',
      width: 480,
      minWidth: 480,
    },
    {
      title: '采购立项完成时间',
      dataIndex: 'projectEndTime',
      width: 150,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '申请单状态',
      dataIndex: 'state',
      width: 100,
    },
    {
      title: '申请单类型',
      dataIndex: 'type',
      width: 170,
    },
    {
      title: '申请单来源',
      dataIndex: 'source',
      width: 100,
    },
    {
      title: '采购申请金额（元）',
      dataIndex: 'money',
      width: 150,
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '汇率',
      dataIndex: 'rate',
      width: 100,
    },
    {
      title: '预计开工时间',
      dataIndex: 'estimatedBeginTime',
      width: 130,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '质保等级',
      dataIndex: 'warrantyLevel',
      width: 80,
    },
    {
      title: '申请部门',
      dataIndex: 'applicantDept',
      width: 200,
    },
    {
      title: '申请人',
      dataIndex: 'applicantUser',
      width: 150,
    },
    {
      title: '币种',
      dataIndex: 'currency',
      width: 100,
    },
    {
      title: '与现场安全相关',
      dataIndex: 'withSafety',
      width: 150,
      customRender({ text }) {
        return parseBooleanToRender(text);
      },
    },
    {
      title: '采购计划号',
      dataIndex: 'purchasePlanCode',
      width: 180,
    },
    {
      title: '归口部门',
      dataIndex: 'bkDept',
      width: 150,
    },
    {
      title: '归口管理',
      dataIndex: 'bkManage',
      width: 150,
    },
    {
      title: '建议采购方式',
      dataIndex: 'suggestPurchaseWay',
      width: 130,
    },
    {
      title: '采购内容',
      dataIndex: 'purchaseContent',
      width: 220,
    },
    {
      title: '推荐供应商名单',
      dataIndex: 'recSupList',
      width: 220,
    },
    {
      title: '推荐潜在供应商名单',
      dataIndex: 'recPtSupList',
      width: 220,
    },
    {
      title: '是否有匹配的框架合同',
      dataIndex: 'isFrameContrac',
      width: 170,
      customRender({ text }) {
        return parseBooleanToRender(text);
      },
    },
  ],
  api: (params: Record<string, any>) => {
    const originSearchConditions = loadGet(cloneDeep(params), 'searchConditions', []);
    const query = {};
    const searchConditions = originSearchConditions.reduce((prev, cur) => {
      for (let i = 0; i < cur.length; i++) {
        const single = cur[i];
        const filedProp = loadGet(single, 'field', '');
        if (['wbsId', 'projectEndTime'].includes(filedProp)) {
          const [first, second] = loadGet(single, 'values', []);
          if (filedProp === 'wbsId') {
            Object.assign(query, {
              wbsId: first,
            });
          }
          if (filedProp === 'projectEndTime') {
            Object.assign(query, {
              startDate: first,
              endDate: second,
            });
          }
          cur.splice(i, 1, undefined);
        }
      }
      const lastCur = cur.filter(Boolean);
      if (lastCur.length) {
        return [...prev, lastCur];
      }
      return prev;
    }, []);
    const newSearchConditions = {
      searchConditions: searchConditions.length ? searchConditions : [],
      query: isEmpty(query) ? undefined : query,
    };
    pageSearchConditions.value = params.searchConditions ? newSearchConditions : null;
    return new Api('/pms/ncfFormpurchaseRequest/page').fetch({
      ...params,
      power: {
        pageCode: 'projectApplication001',
        containerCode: 'PMS_CGLXSQ_container_01',
      },
      ...newSearchConditions,
    }, '', 'POST');
  },
};
const pageSearchConditions = ref(null);
const loadStatus: Ref<boolean> = ref(false);
const powerData = ref();

const selectRows: Ref<any[]> = ref([]);
const selectKeys: Ref<string[]> = ref([]);

const showImportBtn = computed(() => isPower('PMS_CGLXSQ_container_01_button_04', powerData.value));

const actions: IOrionTableActionItem[] = [
  // {
  //   text: '编辑',
  //   event: 'edit',
  //   isShow: (record) => isPower('PMS_CGLXSQ_container_01_button_03', record.rdAuthList),
  // },
  {
    text: '查看',
    event: 'view',
    isShow: (record) => isPower('PMS_CGLXSQ_container_01_button_02', record.rdAuthList),
  },
  // {
  //   text: '删除',
  //   event: 'delete',
  //   isShow: (record) => isPower('PMS_CGLXSQ_container_01_button_01', record.rdAuthList),
  // },
];

function actionClick(actionItem: { event: string }, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'edit':
      openFormDrawer(PAForm, record, updateTable);
      break;
    case 'view':
      router.push({
        name: 'PurchaseProjectApplicationItem',
        params: {
          id: record.id,
        },
      });
      break;
    case 'delete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除这条数据吗？',
        onOk: () => deleteApi([record.id]),
      });
      break;
  }
}
// 表格多选回调
function selectionChange({ rows, keys }) {
  selectRows.value = rows; // 导出所有用
  selectKeys.value = keys; // 导出所选用
}
function updateTable() {
  tableRef.value?.reload();
}

function deleteApi(ids: string[]) {
  return new Promise((resolve) => {
    new Api('/pms/ncfFormpurchaseRequest').fetch(ids, 'remove', 'DELETE').then(() => {
      updateTable();
    }).finally(() => {
      resolve('');
    });
  });
}

const exportTable = () => {
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk: async () => {
      loadStatus.value = true;
      let res = await downloadByData('/pms/ncfFormpurchaseRequest/export/excel', {
        ...(selectKeys.value.length > 0
          ? {
            query: {
              ids: selectKeys.value,
            },
          } : (pageSearchConditions.value || {})),
      }, '', 'POST', true, false, '导出处理完成，现在开始下载');
      loadStatus.value = false;
    },
  });
};

const getMoney = async () => {
  try {
    const result = await new Api('/pms/ncfFormpurchaseRequest/getNumMoney').fetch({
      ...(pageSearchConditions.value || {}),
    }, '', 'POST');
    rowMoney.forEach((item) => {
      item.value = result[item.key];
    });
  } catch (e) {
  }
};
const getPowerDataHandle = (data) => {
  powerData.value = data;
};

watchEffect(() => {
  getMoney();
});
</script>

<template>
  <Layout
    v-get-power="{pageCode: 'projectApplication001',getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      class="scroll-table"
      :options="tableOptions"
      :onSelectionChange="selectionChange"
    >
      <template #toolbarLeft>
        <Space :size="12">
          <BasicButton
            v-if="showImportBtn"
            icon="sie-icon-daochu"
            type="primary"
            @click="exportTable"
          >
            导出全部
          </BasicButton>
          <MoneyRow :data="rowMoney" />
        </Space>
      </template>
      <template #actions="{record}">
        <BasicTableAction
          :actions="actions"
          :record="record"
          @actionClick="actionClick($event,record)"
        />
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">

:deep(.ant-input-group-wrapper) {
  display: flex;
  align-items: center;
}
.scroll-table {
  ::-webkit-scrollbar {
    height: 15px !important;
    width: 15px !important;
  }
  ::-webkit-scrollbar-thumb {
    border-radius:0;
  }
}
</style>
