package com.chinasie.orion.constant;

/**
 * @author: wyc
 * @date:
 * @description:
 */
public enum BillingCompanyEnum {

    SRG_SU("SRG_SU","[3302]苏州热工研究院有限公司"),
    SRG_NING("SRG_NING","苏州热工研究院有限公司宁德分公司"),
    GH_SHENG("GH_SHENG","[3310]中广核（深圳）运营技术与辐射监测有限公司"),
    GH_SU("GH_SU","[3304]中广核检测技术有限公司"),
    ;


    private String code;

    private String desc;


    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    BillingCompanyEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
