<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.management.repository.QuotationManagementMapper">

    <resultMap id="requirementVO" type="com.chinasie.orion.domain.vo.RequirementNoticeVO">
        <result column="id" property="id" />
        <result column="requirement_name" property="requirementName"/>
        <result column="business_person" property="businessPerson"/>
        <result column="tech_res" property="techRes"/>
        <result column="sign_deadln_time" property="signDeadlnTime"/>
        <result column="org_id" property="orgId"/>
        <result column="platform_id" property="platformId"/>
    </resultMap>


    <select id="queryData" resultType="com.chinasie.orion.domain.vo.RequirementNoticeVO">
        SELECT
            prm.id,
            prm.requirement_name,
            prm.business_person,
            prm.tech_res,
            prm.sign_deadln_time,
            prm.org_id,
            prm.platform_id
        FROM
            pms_requirement_mangement prm
                LEFT JOIN pmsx_quotation_management pqm ON prm.id = pqm.requirement_id
                LEFT JOIN pms_market_contract pmc ON pmc.requirement_id = prm.id
        WHERE
            pqm.logic_status = 1
          AND prm.project_status = 130
          AND prm.sign_deadln_time BETWEEN CURRENT_DATE
              AND CURRENT_DATE + INTERVAL 7 DAY
          AND pqm.STATUS IN ( 120, 110, 150 )
          AND pmc.id IS NULL
    </select>


    <select id="queryByPage" resultType="com.chinasie.orion.management.domain.vo.QuotationManagementVO">
        SELECT
        pqm.*,

        prm.business_person,
        prm.business_person_name,
        prm.tech_res,
        prm.tech_res_name,
        prm.res_source,
        prm.business_type,

        prm.cust_person,

        pci.cus_name cust_person_name,
        pci.group_in_out,
        pci.industry,
        pci.ywsrlx

        FROM

        pmsx_quotation_management pqm
        LEFT JOIN pms_requirement_mangement prm ON prm.id = pqm.requirement_id
        AND prm.logic_status = 1
        AND prm.org_id = pqm.org_id
        LEFT JOIN pms_customer_info pci ON pci.id = prm.cust_person
        AND pci.logic_status = 1
        AND pci.org_id = pqm.org_id

        <where>
            and pqm.logic_status = 1
            <if test="param.status != null">
                AND pqm.status = #{param.status}
            </if>
            <if test="param.quotationName != null and param.quotationName != ''">
                AND ( pqm.quotation_name like concat('%',#{param.quotationName},'%')
                or pqm.quotation_id like concat('%',#{param.quotationName},'%'))
            </if>
            <if test="param.userId != null and param.userId != '' and param.deptIds.size != 0">
                and (
                prm.req_ownership in
                <foreach collection="param.deptIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                or prm.tech_res = #{param.userId}
                or prm.business_person = #{param.userId}
                )
            </if>
        </where>

        order by pqm.create_time desc
    </select>

</mapper>