package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.PlanBaseLineInfoDTO;
import com.chinasie.orion.domain.dto.ProjectSimpleDto;
import com.chinasie.orion.domain.entity.PlanBaseLineInfo;
import com.chinasie.orion.domain.vo.PlanBaseLineInfoVo;
import com.chinasie.orion.domain.vo.PlanTreeVo;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.page.PageRequest;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.BaseLineInfoService;
import com.chinasie.orion.util.BeanCopyUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/16/15:56
 * @description:
 */
@RestController
@RequestMapping("/base-line-info")
@Api(tags = "基线信息表（是否还在使用）")
@Deprecated
public class PlanBaseLineInfoController {

    @Resource
    private BaseLineInfoService baseLineInfoService;

    @ApiOperation("新增基线")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectSimpleDto", dataType = "ProjectSimpleDto")
    })
    @PostMapping(value = "")
    public ResponseDTO<Boolean> savePlan(@RequestBody ProjectSimpleDto projectSimpleDto) throws Exception {
        return new ResponseDTO(baseLineInfoService.copyPlanRecursion(projectSimpleDto));
    }


    @ApiOperation("修改基线")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "baseLineInfoDTO", dataType = "BaseLineInfoDTO")
    })
    @PutMapping(value = "")
    public ResponseDTO<Boolean> updateBaseLine(@RequestBody PlanBaseLineInfoDTO baseLineInfoDTO) throws Exception {
        return new ResponseDTO(baseLineInfoService.updateById(BeanCopyUtils.convertTo(baseLineInfoDTO, PlanBaseLineInfo::new)));
    }

    @ApiOperation("获取基线信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", dataType = "String")
    })
    @GetMapping(value = "/{id}")
    public ResponseDTO<PlanBaseLineInfoVo> detailById(@PathVariable("id") String id) throws Exception {
        return new ResponseDTO(baseLineInfoService.getDetailById(id));
    }

    @ApiOperation("打开基线获取基线备份的计划列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", dataType = "String")
    })
    @GetMapping(value = "/detail/{id}")
    public ResponseDTO<List<PlanTreeVo>> getPlanTreeByBaseId(@PathVariable("id") String id) throws Exception {
        return new ResponseDTO(baseLineInfoService.getPlanTreeByBaseId(id));
    }

    @ApiOperation("获取基线列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", dataType = "String")
    })
    @GetMapping(value = "/list")
    public ResponseDTO<List<PlanBaseLineInfoVo>> list(@RequestParam("projectId") String projectId) throws Exception {
        return new ResponseDTO(baseLineInfoService.getListProjectId(projectId));
    }


    @ApiOperation("删除基线")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", dataType = "String")
    })
    @DeleteMapping(value = "/{id}")
    public ResponseDTO<Boolean> delById(@PathVariable("id") String id) throws Exception {
        return new ResponseDTO(baseLineInfoService.delById(id));
    }

    @ApiOperation("批量删除基线")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", dataType = "List")
    })
    @DeleteMapping(value = "/batch")
    public ResponseDTO<Boolean> delById(@RequestBody List<String> ids) throws Exception {
        return new ResponseDTO(baseLineInfoService.deleteByIdList(ids));
    }


    @ApiOperation("获取基线信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping(value = "/page")
    public ResponseDTO<PageResult<PlanBaseLineInfoVo>> getPlanTreeByBaseId(@RequestBody Page<PlanBaseLineInfoDTO> pageRequest) throws Exception {
        return new ResponseDTO(baseLineInfoService.pageList(pageRequest));
    }
}
