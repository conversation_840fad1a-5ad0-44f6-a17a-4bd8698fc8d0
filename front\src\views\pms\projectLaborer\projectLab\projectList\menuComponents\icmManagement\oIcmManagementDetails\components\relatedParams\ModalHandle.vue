<template>
  <BasicModal
    destroyOnClose
    showFooter
    :width="1200"
    min-height="650"
    :title="state.title"
    :show-continue="true"
    :useWrapper="false"
    :bodyStyle="{}"
    @register="modalRegister"
    @visible-change="visibleChange"
    @ok="confirm"
  >
    <TableComponent />
  </BasicModal>
</template>

<script setup lang="ts">
import {
  defineProps, reactive, defineExpose, defineEmits,
} from 'vue';
import {
  BasicModal, useModal, FormSchema, useForm,
} from 'lyra-component-vue3';
import Api from '/@/api';
import { message } from 'ant-design-vue';
import TableComponent from './TableComponent.vue';

const [modalRegister, modalMethods] = useModal();
const props = defineProps({});
const emit = defineEmits(['update']);

function initData() {
  return {
    action: 'add',
    title: '',
    originData: {},
  };
}

const state = reactive(initData());

function visibleChange(show) {
  !show && Object.assign(state, initData());
}

interface openModalTypes {
    action: String;// add  edit 等
    info?: any
}

function openDrawer(data: openModalTypes) {
  modalMethods.openModal(true);
  data && usualHandle(data);
  if (data.action === 'add') {
  }
  if (data.action === 'edit') {
  }
}

function usualHandle(data) {
  data?.action && (state.action = data?.action);
  data?.action === 'add' && (state.title = '新增参数');
  data?.action === 'edit' && (state.title = '编辑');
  if (data?.info) {
    state.originData = JSON.parse(JSON.stringify(data.info));
  }
}

async function confirm() {
  modalMethods.setModalProps({ confirmLoading: true });
  try {
    await goFetch().then(() => {
      message.success('操作成功');
      emit('update');
      if (!state?.isContinue) {
        modalMethods.openModal(false);
      }
    });
  } catch (_) {
  } finally {
    modalMethods.setModalProps({ confirmLoading: false });
  }
}

async function goFetch() {
  if (state.action === 'add') {
    return await new Api('').fetch('', '', 'POST');
  }
  if (state.action === 'edit') {
    return await new Api('').fetch('', '', 'POST');
  }
}

defineExpose({
  modalMethods,
  openDrawer,
});
</script>

<style scoped lang="less">

</style>
