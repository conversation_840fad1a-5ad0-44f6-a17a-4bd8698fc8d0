package com.chinasie.orion.domain.vo.job;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/09/13:56
 * @description:
 */
@Data
public class AuthorizeInfoVO  implements Serializable {

    @ApiModelProperty(value = "授权Id")
    private String id;
    @ApiModelProperty(value = "基地编码")
    private String baseCode;
    @ApiModelProperty(value = "基地名称")
    private String baseName;

    @ApiModelProperty(value = "岗位编号")
    private String jobCode;
    @ApiModelProperty(value = "岗位名称")
    private String jobName;

    /**
     * 授权到期日期
     */
    @ApiModelProperty(value = "授权到期日期")
    private Date endDate;
    /**
     * 授权状态（101-未授权，130-已授权）
     */
    @ApiModelProperty(value = "授权状态（101-未授权，130-已授权）")
    private Integer authorizeStatus;

    /**
     * 授权状态（100-未授权，111-已授权）
     */
    @ApiModelProperty(value = "授权状态（101-未授权，130-已授权）名称")
    private String authorizeStatusName;

}
