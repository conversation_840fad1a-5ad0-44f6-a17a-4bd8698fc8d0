<template>
  <OrionTable
    ref="tableRef"
    :options="TableOption"
  />
</template>
<script setup lang="ts">
import {
  ref, onMounted, computed, inject, h, watch, toRef,
} from 'vue';
import { useRouter } from 'vue-router';
import {
  DataStatusTag,
  isPower, Layout, OrionTable,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
import Api from '/@/api';
const props = defineProps({
  tableData: {
    type: Object,
    default: () => {},
  },
});
const powerData = inject('powerData');
const router = useRouter();
const tableRef = ref(null);
const dataSource = ref();
const tableQuery = toRef(props, 'tableData');
const TableOption = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  columns: [
    {
      title: '项目名称',
      dataIndex: 'name',
      minWidth: 200,
      customRender({ record, text }) {
        return h(
          'span',
          {
            class: computed(() => (isPower('XMX_list_button_03', powerData) ? 'action-btn' : '')).value,
            title: text,
            onClick(e) {
              if (isPower('XMX_list_button_03', powerData)) {
                handleToDetail(record);
              }
              e.stopPropagation();
            },
          },
          text,
        );
      },
    },
    {
      title: '层级',
      dataIndex: 'level',
      width: 100,
    },
    {
      title: '计划类型',
      dataIndex: 'nodeType',
      width: 120,
      customRender({ text }) {
        return text === 'milestone' ? '里程碑节点' : '计划';
      },
    },
    {
      title: '责任部门',
      dataIndex: 'rspSubDeptName',
      width: 120,
    },
    {
      title: '责任科室',
      dataIndex: 'rspSectionName',
      width: 120,
    },
    {
      title: '责任人',
      dataIndex: 'rspUserName',
      width: 120,
    },
    {
      title: '是否关联流程',
      dataIndex: 'processFlag',
      width: 120,
      customRender({ text }) {
        return text ? '是' : '否';
      },
    },
    {
      title: '计划状态',
      dataIndex: 'dataStatus',
      width: 120,
      customRender({ record }) {
        return record.dataStatus
          ? h(DataStatusTag, {
            statusData: record.dataStatus,
          })
          : '';
      },
    },
    // {
    //   title: '发起项目评价日期',
    //   dataIndex: 'evaluationTime',
    //   width: 150,
    //   customRender({ text }) {
    //     return text ? dayjs(text).format('YYYY-MM-DD') : '--';
    //   },
    // },
  ],
  api: (params: any) => new Api('/pms/projectSchemeStatistics/getProjectSchemePages').fetch({
    ...params,
    query: { ...dataSource.value },
  }, '', 'POST').then((res) => {
    // 清除空 children: [] 字段
    const cleanedContent = res.content.map((item: any) => {
      const { children, ...rest } = item;
      if (children.length > 0) {
        return item;
      }
      return rest;
    });

    return {
      ...res,
      content: cleanedContent,
    };
  }),
  immediate: true,
};

watch(tableQuery, (newValue, oldValue) => {
  dataSource.value = newValue;
  upTableDate();
}, {
  immediate: true,
  deep: true,
});
function upTableDate() {
  tableRef.value?.reload();
}
function handleToDetail(row) {
  router.push({
    name: 'ProPlanDetails',
    params: { id: row.id },
  });
}
</script>
