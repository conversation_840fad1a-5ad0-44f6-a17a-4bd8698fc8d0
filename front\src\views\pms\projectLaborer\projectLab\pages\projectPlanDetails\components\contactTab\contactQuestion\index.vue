<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
    @buttonClick="tableButtonClick"
  >
    <template #toolbarLeft>
      <BasicButton
        v-if="isPower('PMS_OJJHXQ_container_02_03_button_01',powerData)"
        type="primary"
        icon="add"
        @click="addSystemRoleHandle "
      >
        添加
      </BasicButton>
    </template>
    <template #modifyTime="{ text }">
      {{ text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '' }}
    </template>
    <template #proposedTime="{ text }">
      {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
    </template>
    <template #predictEndTime="{ text }">
      {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
    </template>
    <template #seriousLevelName="{ text }">
      <span :style="{ color: text === '严重' ? 'red' : '' }">{{ text }}</span>
    </template>
    <template #priorityLevelName="{ text }">
      <span :style="{ color: text === '最高' ? 'red' : '' }">{{ text }}</span>
    </template>
    <template #statusName="{ text }">
      <span
        :style="{ color: text === '未完成' ? '#ccc' : text == '已处理' ? 'green' : 'blue' }"
      >{{ text }}</span>
    </template>
  </OrionTable>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, computed, onMounted, inject, h, ref,
} from 'vue';
import {
  Layout, BasicTable, isPower, useDrawer, OrionTable, BasicButton, DataStatusTag, openModal,
} from 'lyra-component-vue3';
import {
  Dropdown, Menu, message, Progress, Modal,
} from 'ant-design-vue';
import {
  PlusCircleOutlined,
  InfoCircleOutlined,
  DeleteOutlined,
  ImportOutlined,
  ExportOutlined,
  PlusOutlined,

} from '@ant-design/icons-vue';
/* 格式化时间 */
import { formatterTime } from '/@/views/pms/projectLaborer/utils/time';
import router from '/@/router';
import dayjs from 'dayjs';
import AddSystemRole from './modal/addSystemRole.vue';
import Api from '/@/api';
import AssociationQuestionModal from './modal/AssociationQuestionModal.vue';
export default defineComponent({
  name: 'ProductLibraryIndex',
  components: {
    BasicButton,
    OrionTable,
  },
  props: {
    formId: {
      type: String,
      default: '',
    },
  },

  setup(props) {
    const powerData = inject('powerData');
    const tableOptions = {
      deleteToolButton: `'add|enable|disable'${isPower('PMS_OJJHXQ_container_02_03_button_02', powerData) ? '' : '|delete'}`,
      rowSelection: {},
      pagination: false,
      showSmallSearch: false,
      resizeHeightOffset: 60,
      api() {
        return new Api(`/pms/projectScheme/relation/question/lists/${props.formId}`).fetch('', '', 'POST');
      },
      columns: [
        {
          title: '编号',
          dataIndex: 'number',
          ellipsis: true,
          width: 200,
        },
        {
          title: '名称',
          dataIndex: 'name',
          minWidth: 240,
          align: 'left',
          ellipsis: true,
          customRender({ record, text }) {
            return h('span', {
              onClick: () => {
                openModal.closeAll();
                router.push({
                  name: 'PMSQuestionManagementDetails',
                  params: {
                    id: record.id,
                  },
                });
              },
              class: 'action-btn',
            }, text);
          },
        },
        {
          title: '问题来源',
          dataIndex: 'questionSourceName',
          key: 'questionSourceName',
          align: 'left',
          ellipsis: true,
        },
        {
          title: '问题类型',
          dataIndex: 'questionTypeName',
          key: 'questionTypeName',

          align: 'left',
          ellipsis: true,
        },
        {
          title: '严重程度',
          dataIndex: 'seriousLevelName',
          key: 'seriousLevelName',
          align: 'left',
          ellipsis: true,
        },
        {
          title: '问题内容',
          dataIndex: 'content',
          key: 'content',
          align: 'left',
          ellipsis: true,
        },
        {
          title: '状态',
          dataIndex: 'dataStatus',
          key: 'dataStatus',
          width: '80px',
          align: 'left',
          // sorter: true,
          ellipsis: true,
          customRender: ({ record, text }) => h(DataStatusTag, { statusData: record.dataStatus }),
        },
        {
          title: '提出人',
          dataIndex: 'exhibitorName',
          key: 'exhibitorName',
          width: '70px',
          align: 'left',
          // sorter: true,
          ellipsis: true,
        },
        {
          title: '提出时间',
          dataIndex: 'proposedTime',
          key: 'proposedTime',

          width: '100px',
          align: 'left',
          // sorter: true,
          ellipsis: true,
          slots: { customRender: 'proposedTime' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 150,
          align: 'left',
          fixed: 'right',
          slots: { customRender: 'action' },
        },
      ],
      actions: [
        {
          event: 'delete',
          text: '删除',
          isShow: isPower('PMS_OJJHXQ_container_02_03_button_02', powerData),
          modal(record) {
            const deleteParams = {
              toId: props.formId,
              fromIds: [record.id],
            };
            const love = {
            };
            return new Api('/pms').fetch(deleteParams, 'projectScheme/relation/question/remove', 'DELETE');
          },
        },
      ],
    };
    const tableRef = ref(null);
    let projectId: any = inject('projectId');
    const addSystemRoleHandle = () => {
      const quoteRiskPoolRef = ref();
      openModal({
        title: '关联问题',
        width: 1100,
        height: 700,
        content(h) {
          return h(AssociationQuestionModal, {
            ref: quoteRiskPoolRef,
            showLeftTree: true,
            formId: props.formId,
            projectId: projectId.value,
          });
        },
        async onOk() {
          await quoteRiskPoolRef.value.saveData();
          tableRef.value.reload();
        },
      });
    };
    onMounted(() => {
      //  getFormData();
    });
    /* 批量删除 */
    const multiDelete = (keys) => {
      Modal.confirm({
        title: '删除确认提示？',
        content: '请确认是否对当前选中数据进行删除？',
        onOk() {
          const deleteParams = {
            toId: props.formId,
            fromIds: keys,
          };
          return new Api('/pms').fetch(deleteParams, 'projectScheme/relation/question/remove', 'DELETE').then(() => {
            tableRef.value.reload();
          });
        },
        onCancel() {
          Modal.destroyAll();
        },
      });
    };
    function tableButtonClick(params) {
      if (params && params.type === 'delete') {
        multiDelete(params.selectColumns.keys);
      }
    }
    return {
      formatterTime,
      confirm,
      dayjs,
      addSystemRoleHandle,
      tableOptions,
      tableRef,
      tableButtonClick,
      powerData,
      isPower,
    };
  },

  // mounted() {}
});
</script>
<style scoped lang="less">
</style>
