package com.chinasie.orion.domain.vo.projectStatistics;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * DemandStatusStatistics VO对象
 *
 * <AUTHOR>
 * @since 2023-12-21 13:26:54
 */
@ApiModel(value = "DemandStatusStatisticsVO对象", description = "需求状态趋势统计表")
@Data
public class DemandStatusStatisticsVO  implements Serializable{


    @ApiModelProperty(value = "统计ID")
    private String id;
    /**
     * 统计时间
     */
    @ApiModelProperty(value = "统计时间")
    private Date nowDay;

    /**
     * 时间描述
     */
    @ApiModelProperty(value = "时间描述")
    private String dateStr;

    /**
     * 唯一值
     */
    @ApiModelProperty(value = "唯一值")
    private String uk;

    /**
     * 状态id
     */
    @ApiModelProperty(value = "状态id")
    private String typeId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 未开始数量
     */
    @ApiModelProperty(value = "未开始数量")
    private Integer noStartCount;

    /**
     * 进行中数量
     */
    @ApiModelProperty(value = "进行中数量")
    private Integer underwayCount;

    /**
     * 已完成数量
     */
    @ApiModelProperty(value = "已完成数量")
    private Integer completeCount;

}
