<script setup lang="ts">
import {
  BasicForm, DataStatusTag, getDict, OrionTable, useForm,
} from 'lyra-component-vue3';
import {
  computed, h, onMounted, ref, Ref, unref,
  inject,
} from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import { useRoute } from 'vue-router';
const route = useRoute();
const tableRef:Ref = ref();
const projectId: Ref<string> = ref(route.query.projectId as string);
const par = ref();
const paramsData = ref();
const tableOptions = {
  showToolButton: false,
  showTableSetting: false,
  rowSelection: {
    // type: 'radio',
  },
  api: (params) => new Api('/pms/projectPurchaseOrderInfo/page').fetch(setParams(params), '', 'POST'),
  columns: [
    {
      title: '订单编号',
      dataIndex: 'number',
      minWidth: 120,
    },
    {
      title: '订单名称',
      dataIndex: 'name',
      minWidth: 120,
    },
    {
      title: '订单状态',
      dataIndex: 'dataStatus',
      width: 100,
      customRender({ text }) {
        return text ? h(DataStatusTag, { statusData: text }) : '';
      },
    },
    {
      title: '订单行数',
      dataIndex: 'lineCount',
      width: 150,
    },
    {
      title: '采购类型',
      dataIndex: 'purchaseTypeName',
      minWidth: 150,
    },
    {
      title: '物资服务信息',
      dataIndex: 'descriptionList',
      minWidth: 150,
      customRender({ text }) {
        return text[0] || '--';
      },
    },
    {
      title: '供应商',
      dataIndex: 'supplierName',
      width: 150,
    },
    {
      title: '采购总数量',
      dataIndex: 'purchaseTotalAmount',
      minWidth: 150,
    },
    {
      title: '含税总金额',
      dataIndex: 'haveTaxTotalAmt',
      minWidth: 150,
    },
    {
      title: '采购员',
      dataIndex: 'resUserName',
      width: 140,
      customRender({ text }) {
        return text || '--';
      },
    },
  ],
};
const [register, { setFieldsValue, validate }] = useForm({
  schemas: [
    {
      field: 'purchaseType',
      component: 'ApiSelect',
      label: '采购类型',
      componentProps: {
        api: () => getDict('dict1717099480769298432'),
        labelField: 'description',
        onChange: (val) => {
          par.value = val;
          tableRef.value?.reload();
        },
      },
    },
  ],
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
});

function setParams(params) {
  if (par.value) {
    return {
      ...params,
      query: {
        projectId: projectId.value,
        purchaseType: par.value,
      },
    };
  }
  return {
    ...params,
    query: { projectId: projectId.value },
  };
}

defineExpose({
  getSelectRows: computed(() => tableRef.value.getSelectRows),
});
</script>

<template>
  <div style="height: 500px;overflow: hidden">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <BasicForm
          class="clue"
          @register="register"
        />
      </template>
    </OrionTable>
  </div>
</template>

<style scoped lang="less">
.clue{
  width: 560px;
}
:deep(.ant-basic-form){
  padding:0 !important;
}
</style>
