import { get as _get } from 'lodash-es';
import { batchIncludes } from '/@/views/pms/utils/utils';
export const useRouteParamFormUserHome = () => {
  const sessionKey = 'RouteParamFormUserHome';

  const getRouteParamFormUserHome = (prop?:string) => {
    const cache = sessionStorage.getItem(sessionKey);
    const cacheObj = JSON.parse(cache) || {};
    return prop ? _get(cacheObj, prop, undefined) : cacheObj;
  };
  const removeRouteParamFormUserHome = () => {
    sessionStorage.removeItem(sessionKey);
  };
  const isEnable = () => {
    const target = getRouteParamFormUserHome();
    return batchIncludes(Object.keys(target), [
      'tabTarget',
      'posiId',
      'sourceId',
    ]);
  };
  return {
    isEnable,
    getRouteParamFormUserHome,
    removeRouteParamFormUserHome,
  };
};