<script setup lang="ts">
import {
  BasicCard, BasicForm, FormSchema, useForm,
} from 'lyra-component-vue3';
import Api from '/@/api';
import {
  h, onMounted, ref, Ref, unref,
} from 'vue';

const props = defineProps<{
  record: any
}>();

const attendCenterCodeList: Ref<Array<{
  code: string,
  name: string
}>> = ref([]);
const schemas: FormSchema[] = [
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '培训信息',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  {
    field: 'isCheck',
    component: 'Select',
    label: '是否考核',
    rules: [
      {
        required: true,
        type: 'boolean',
      },
    ],
    componentProps: {
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
  },
  {
    field: 'type',
    component: 'SelectDictVal',
    label: '培训类型',
    required: true,
    ifShow() {
      return props.record?.pageType === 'special';
    },
    componentProps() {
      return {
        dictNumber: 'pms_train_type',
      };
    },
  },
  {
    field: 'baseCode',
    component: 'ApiSelect',
    label: '培训基地',
    required: true,
    componentProps() {
      return {
        api: () => new Api('/pms/base-place/list').fetch('', '', 'POST'),
        labelField: 'name',
        valueField: 'code',
        onChange(_value: string, option: { label: string }) {
          setFieldsValue({
            baseName: option?.label,
          });
        },
      };
    },
  },
  {
    field: 'baseName',
    component: 'Input',
    label: '培训基地',
    show: false,
  },
  {
    field: 'trainKey',
    component: 'SelectDictVal',
    label: '培训名称',
    required: true,
    ifShow() {
      return props.record?.pageType !== 'special';
    },
    componentProps() {
      return {
        dictNumber: 'pms_train_dict',
        onChange(_value: string, option: { description: string }) {
          setFieldsValue({
            name: option?.description,
          });
        },
      };
    },
  },
  {
    field: 'name',
    component: 'Input',
    label: '培训名称',
    required: true,
    ifShow() {
      return props.record?.pageType === 'special';
    },
    componentProps() {
      return {
        maxlength: 100,
      };
    },
  },
  {
    field: 'name',
    component: 'Input',
    label: '',
    show: false,
    ifShow() {
      return props.record?.pageType !== 'special';
    },
  },
  {
    field: 'trainLecturer',
    component: 'Input',
    label: '培训讲师',
    required: true,
    ifShow({ model }) {
      return model.isCheck;
    },
    componentProps() {
      return {
        maxlength: 200,
      };
    },
  },
  {
    field: 'completeDate',
    component: 'DatePicker',
    label: '拟完成时间',
    required: true,
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    field: 'endDate',
    component: 'DatePicker',
    label: '实际完成时间',
    ifShow({ model }) {
      return model.isCheck;
    },
    componentProps() {
      return {
        disabled: true,
        valueFormat: 'YYYY-MM-DD',
      };
    },
  },
  {
    field: 'expirationMonth',
    component: 'InputNumber',
    label: '培训有效期（月）',
    required: true,
    componentProps() {
      return {
        class: 'w-full',
        min: 0,
        max: 99999,
        precision: 0,
        addonAfter: '月',
      };
    },
  },
  {
    field: 'expireTime',
    component: 'DatePicker',
    label: '培训到期时间',
    ifShow({ model }) {
      return model.isCheck;
    },
    componentProps() {
      return {
        disabled: true,
        valueFormat: 'YYYY-MM-DD',
      };
    },
  },
  {
    field: 'lessonHour',
    component: 'InputNumber',
    label: '培训课时',
    required: true,
    componentProps() {
      return {
        class: 'w-full',
        min: 0,
        max: 99999,
        precision: 0,
      };
    },
  },
  {
    field: 'trainAddress',
    component: 'Input',
    label: '培训地点',
    required: true,
    ifShow({ model }) {
      return model.isCheck;
    },
    componentProps() {
      return {
        maxlength: 200,
      };
    },
  },
  {
    field: 'attendCenterCodeList',
    component: 'TreeSelectOrg',
    label: '参培中心',
    rules: [
      {
        required: true,
        type: 'array',
      },
    ],
    colProps: {
      span: 24,
    },
    ifShow({ model }) {
      return !model.isCheck;
    },
    componentProps() {
      return {
        multiple: true,
        fieldNames: {
          value: 'deptCode',
        },
        onChange(value: string[], label: string[]) {
          attendCenterCodeList.value = value.map((code, index) => ({
            name: label[index],
            code,
          }));
        },
      };
    },
  },
  {
    field: 'content',
    component: 'InputTextArea',
    label: '培训内容',
    required: true,
    colProps: {
      span: 24,
    },
    componentProps: {
      rows: 4,
      maxlength: 200,
      showCount: true,
    },
  },
];

const [register, { validate, setFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  props?.record?.id && getFormData();
});

const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/train-manage').fetch('', props?.record?.id, 'GET');
    await setFieldsValue({
      isCheck: result.isCheck,
    });
    await setFieldsValue({
      ...result,
      attendCenterCodeList: result?.attendCenterCodeList?.map((item: { code: string }) => item.code) || [],
    });
    attendCenterCodeList.value = result?.attendCenterCodeList || [];
  } finally {
    loading.value = false;
  }
}

defineExpose({
  async submit() {
    const formValues = await validate();
    const params = {
      ...formValues,
      id: props?.record?.id,
    };
    if (!formValues.isCheck) {
      params.attendCenterCodeList = unref(attendCenterCodeList);
    } else {
      delete params.attendCenterCodeList;
    }

    return new Promise((resolve, reject) => {
      new Api('/pms/train-manage').fetch(params, props?.record?.id ? 'edit' : 'add', props?.record?.id ? 'PUT' : 'POST').then(() => {
        resolve('');
      }).catch((e) => {
        reject(e);
      });
    });
  },
});

</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">

</style>
