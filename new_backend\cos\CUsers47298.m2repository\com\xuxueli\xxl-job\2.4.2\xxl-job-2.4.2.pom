<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.xuxueli</groupId>
	<artifactId>xxl-job</artifactId>
	<version>2.4.2</version>
	<packaging>pom</packaging>

	<name>${project.artifactId}</name>
	<description>A distributed task scheduling framework.</description>
	<url>https://www.xuxueli.com/</url>

	<modules>
		<module>xxl-job-core</module>
		<module>xxl-job-admin</module>
		<module>xxl-job-executor-samples</module>
    </modules>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<maven.compiler.encoding>UTF-8</maven.compiler.encoding>
		<maven.compiler.source>1.8</maven.compiler.source>
		<maven.compiler.target>1.8</maven.compiler.target>
		<maven.test.skip>true</maven.test.skip>

		<netty.version>4.1.115.Final</netty.version>
		<gson.version>2.11.0</gson.version>

		<spring.version>5.3.39</spring.version>
		<spring-boot.version>2.7.18</spring-boot.version>

		<mybatis-spring-boot-starter.version>2.3.2</mybatis-spring-boot-starter.version>
		<mysql-connector-j.version>9.1.0</mysql-connector-j.version>

		<slf4j-api.version>2.0.16</slf4j-api.version>
		<junit-jupiter.version>5.11.3</junit-jupiter.version>
		<javax.annotation-api.version>1.3.2</javax.annotation-api.version>

		<groovy.version>4.0.24</groovy.version>

		<maven-source-plugin.version>3.3.1</maven-source-plugin.version>
		<maven-javadoc-plugin.version>3.11.1</maven-javadoc-plugin.version>
		<maven-gpg-plugin.version>3.2.7</maven-gpg-plugin.version>
	</properties>

	<build>
		<plugins>
		</plugins>
	</build>


	<licenses>
		<license>
			<name>GNU General Public License version 3</name>
			<url>https://opensource.org/licenses/GPL-3.0</url>
		</license>
	</licenses>

	<scm>
		<tag>master</tag>
		<url>https://github.com/xuxueli/xxl-job.git</url>
		<connection>scm:git:https://github.com/xuxueli/xxl-job.git</connection>
		<developerConnection>scm:git:**************:xuxueli/xxl-job.git</developerConnection>
	</scm>
	<developers>
		<developer>
			<id>XXL</id>
			<name>xuxueli</name>
			<email><EMAIL></email>
			<url>https://github.com/xuxueli</url>
		</developer>
	</developers>

	<profiles>

		<profile>
			<id>release</id>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
			<modules>
				<module>xxl-job-core</module>
			</modules>
			<build>
				<plugins>
					<!-- Source -->
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-source-plugin</artifactId>
						<version>${maven-source-plugin.version}</version>
						<executions>
							<execution>
								<phase>package</phase>
								<goals>
									<goal>jar-no-fork</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
					<!-- Javadoc -->
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-javadoc-plugin</artifactId>
						<version>${maven-javadoc-plugin.version}</version>
						<executions>
							<execution>
								<phase>package</phase>
								<goals>
									<goal>jar</goal>
								</goals>
								<configuration>
									<doclint>none</doclint>
								</configuration>
							</execution>
						</executions>
					</plugin>
					<!-- GPG -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>${maven-gpg-plugin.version}</version>
						<configuration>
							<useAgent>false</useAgent>
						</configuration>
                        <executions>
                            <execution>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
				</plugins>
			</build>
			<distributionManagement>
				<snapshotRepository>
					<id>oss</id>
					<url>https://oss.sonatype.org/content/repositories/snapshots/</url>
				</snapshotRepository>
				<repository>
					<id>oss</id>
					<url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
				</repository>
			</distributionManagement>
		</profile>
	</profiles>

</project>