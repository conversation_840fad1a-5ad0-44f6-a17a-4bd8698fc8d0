package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


@Data
public class MarketContractMilestoneRescheduleAddMessageDTO   implements Serializable{

    /**
     * 里程碑id
     */
    @ApiModelProperty(value = "里程碑id")
    private String milestoneId;


    /**
     * 里程碑名称
     */
    @ApiModelProperty(value = "里程碑名称")
    private String milestoneName;

    /**
     * 里程碑类型
     */
    @ApiModelProperty(value = "里程碑类型")
    private String milestoneType;


    /**
     * 初始预估验收日期
     */
    @ApiModelProperty(value = "初始预估验收日期")
    @ExcelProperty(value = "初始预估验收日期 ", index = 4)
    private Date expectAcceptDate;

    /**
     * 预估验收金额
     */
    @ApiModelProperty(value = "预估验收金额")
    private BigDecimal acceptMoney;

    /**
     * 预计开票日期
     */
    @ApiModelProperty(value = "预计开票日期")
    private Date billingDate;

    /**
     * 预计验收日期
     */
    @ApiModelProperty(value = "原预计验收日期")
    private Date oldExpectAcceptDate;

    /**
     * 预估验收金额
     */
    @ApiModelProperty(value = "原预估验收金额")
    private BigDecimal oldAcceptMoney;

    /**
     * 预计开票日期
     */
    @ApiModelProperty(value = "原预计开票日期")
    private Date oldBillingDate;

    /**
     * 是否需要暂估
     */
    @ApiModelProperty(value = "是否需要暂估")
    private Boolean isProvisionalEstimate;

    /**
     * 计划暂估日期
     */
    @ApiModelProperty(value = "计划暂估日期")
    private Date plannedEstimatedDate;



}
