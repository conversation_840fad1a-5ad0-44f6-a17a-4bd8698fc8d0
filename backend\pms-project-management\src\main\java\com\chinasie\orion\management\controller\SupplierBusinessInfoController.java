package com.chinasie.orion.management.controller;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.dto.SupplierBusinessInfoDTO;
import com.chinasie.orion.management.domain.dto.SupplierInfoDTO;
import com.chinasie.orion.management.domain.vo.SupplierBusinessInfoVO;
import com.chinasie.orion.management.domain.vo.SupplierRestrictedRecordVO;
import com.chinasie.orion.management.service.SupplierBusinessInfoService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * SupplierBusinessInfo 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@RestController
@RequestMapping("/supplierBusinessInfo")
@Api(tags = "商务信息")
public class SupplierBusinessInfoController {

    @Autowired
    private SupplierBusinessInfoService supplierBusinessInfoService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "商务信息", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<SupplierBusinessInfoVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        SupplierBusinessInfoVO rsp = supplierBusinessInfoService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 根据采购编号查询商务信息
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "根据采购编号查询商务信息")
    @RequestMapping(value = "/getBusinessInfoByCode", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】根据采购编号查询商务信息", type = "商务信息", subType = "根据采购编号查询商务信息", bizNo = "{{#pageRequest.query.code}}")
    public ResponseDTO<Page<SupplierBusinessInfoVO>> getBusinessInfoByCode(@RequestBody Page<SupplierBusinessInfoDTO> pageRequest) throws Exception {
        Page<SupplierBusinessInfoVO> rsp = supplierBusinessInfoService.getByCode(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param supplierBusinessInfoDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#supplierBusinessInfoDTO.name}}】", type = "商务信息", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody SupplierBusinessInfoDTO supplierBusinessInfoDTO) throws Exception {
        String rsp = supplierBusinessInfoService.create(supplierBusinessInfoDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param supplierBusinessInfoDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#supplierBusinessInfoDTO.name}}】", type = "商务信息", subType = "编辑", bizNo = "{{#supplierBusinessInfoDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody SupplierBusinessInfoDTO supplierBusinessInfoDTO) throws Exception {
        Boolean rsp = supplierBusinessInfoService.edit(supplierBusinessInfoDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "商务信息", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = supplierBusinessInfoService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "商务信息", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = supplierBusinessInfoService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "商务信息", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page/{mainTableId}", method = RequestMethod.POST)
    public ResponseDTO<Page<SupplierBusinessInfoVO>> pages(@PathVariable("mainTableId") String mainTableId, @RequestBody Page<SupplierBusinessInfoDTO> pageRequest) throws Exception {
        Page<SupplierBusinessInfoVO> rsp = supplierBusinessInfoService.pages(mainTableId, pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("商务信息导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "商务信息", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        supplierBusinessInfoService.downloadExcelTpl(response);
    }

    @ApiOperation("商务信息导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "商务信息", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = supplierBusinessInfoService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("商务信息导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "商务信息", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = supplierBusinessInfoService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消商务信息导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "商务信息", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = supplierBusinessInfoService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("商务信息导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "商务信息", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        supplierBusinessInfoService.exportByExcel(searchConditions, response);
    }
}
