package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.base.api.domain.entity.*;
import com.chinasie.orion.base.api.repository.*;
import com.chinasie.orion.domain.dto.humanResource.ProjectHumanResourceSettingDTO;
import com.chinasie.orion.domain.dto.humanResource.ProjectHumanResourcePageDTO;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.entity.humanResource.ProjectHumanResourceSetting;
import com.chinasie.orion.domain.vo.humanResource.ProjectHumanResourcePageVO;
import com.chinasie.orion.domain.vo.humanResource.ProjectHumanResourceSettingVO;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.pas.api.domain.request.WorkHoursReportDetailForUserRequest;
import com.chinasie.orion.pas.api.service.WorkHoursReportDetailForUserService;
import com.chinasie.orion.permission.core.annotation.OrionDataPermission;
import com.chinasie.orion.repository.ProjectHumanResourceMapper;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.business.RoleVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.enums.TenantLineEnum;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectHumanResourceServiceSetting;
import com.chinasie.orion.service.ProjectSchemeService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;

import java.lang.String;
import java.text.DecimalFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;

import java.util.stream.Collectors;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

/**
 * <p>
 * ProjectHumanResource 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-15 17:57:05
 */
@Service
@Slf4j
public class ProjectHumanResourceServiceImplSetting extends OrionBaseServiceImpl<ProjectHumanResourceMapper, ProjectHumanResourceSetting> implements ProjectHumanResourceServiceSetting {

    @Autowired
    private ProjectSchemeService projectSchemeService;

    @Autowired
    private CurrentUserHelper currentUserHelper;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private UserDOMapper userDOMapper;

    @Autowired
    private WorkHoursReportDetailForUserService workHoursReportDetailForUserService;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProjectHumanResourceSettingVO detail(String id, String pageCode) throws Exception {
        ProjectHumanResourceSetting projectHumanResourceSetting = this.getById(id);
        ProjectHumanResourceSettingVO result = BeanCopyUtils.convertTo(projectHumanResourceSetting, ProjectHumanResourceSettingVO::new);
//        setEveryName(Collections.singletonList(result));
        return result;
    }

    /**
     * 新增
     * <p>
     * * @param projectHumanResourceDTO
     */
    @Override
    public String create(ProjectHumanResourceSettingDTO projectHumanResourceSettingDTO) throws Exception {

        ProjectHumanResourceSetting projectHumanResourceSetting = BeanCopyUtils.convertTo(projectHumanResourceSettingDTO, ProjectHumanResourceSetting::new);
        this.save(projectHumanResourceSetting);
        String rsp = projectHumanResourceSetting.getId();
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param projectHumanResourceDTO
     */
    @Override
    public Boolean edit(ProjectHumanResourceSettingDTO projectHumanResourceSettingDTO) throws Exception {
        ProjectHumanResourceSetting projectHumanResourceSetting = BeanCopyUtils.convertTo(projectHumanResourceSettingDTO, ProjectHumanResourceSetting::new);

        this.updateById(projectHumanResourceSetting);

        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectHumanResourceSettingVO> pages(Page<ProjectHumanResourceSettingDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProjectHumanResourceSetting> condition = new LambdaQueryWrapperX<>(ProjectHumanResourceSetting.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ProjectHumanResourceSetting::getCreateTime);


        Page<ProjectHumanResourceSetting> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectHumanResourceSetting::new));

        PageResult<ProjectHumanResourceSetting> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectHumanResourceSettingVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectHumanResourceSettingVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectHumanResourceSettingVO::new);
//        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    @OrionDataPermission(tenantLine = TenantLineEnum.CUSTOM) // todo 这里权限放开需等用户与部门关系字段改造后删除掉 orgId改为deptId
    public List<ProjectHumanResourcePageVO> humanResourcePages(ProjectHumanResourcePageDTO projectHumanResourcePageDTO) throws Exception {
        Date startTime = projectHumanResourcePageDTO.getStartTime();
        if (ObjectUtil.isNull(startTime)){
            return new ArrayList<>();
        }
        Date endTime = projectHumanResourcePageDTO.getEndTime();
        if (ObjectUtil.isNull(endTime)){
            return new ArrayList<>();
        }
        String keyWord = projectHumanResourcePageDTO.getKeyWord();
        int pageNum = projectHumanResourcePageDTO.getPageNum();
        int pageSize = projectHumanResourcePageDTO.getPageSize();
        LambdaQueryWrapperX<UserDO> queryWrapperX = getUserDOLambdaQueryWrapperX();

        if (StrUtil.isNotBlank(keyWord)){
            queryWrapperX
                    .like(UserDO::getName, keyWord).or()
                    .like(DeptDO::getName, keyWord).or()
                    .like(JobDO::getName, keyWord);
        }
        queryWrapperX.last(" limit " + (pageNum - 1) * pageSize + ","  + pageSize);

        List<UserDO> userList = getUserDOS(queryWrapperX);
        if (CollectionUtils.isEmpty(userList)){
            return new ArrayList<>();
        }
        DecimalFormat df = new DecimalFormat("#.##");
        Date startDate = startTime;

        LocalDate starLocalDate = convertToDate(startTime);
        LocalDate endDate = convertToDate(endTime);
        LocalDate currentDate = convertToDate(startDate);

        // 获取计划人天除去周末
        int workdays = 0;
        while (!currentDate.isAfter(endDate)) {
            if (currentDate.getDayOfWeek() != DayOfWeek.SATURDAY && currentDate.getDayOfWeek() != DayOfWeek.SUNDAY) {
                workdays++;
            }
            currentDate = currentDate.plusDays(1);
        }

        // 获取实际人天
        List<String> userIds = userList.stream().map(UserDO::getId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, Map<Date, Double>> map = getWorkHoursForUserMap(startTime, endTime, userIds);

        // 获取当前登录人实际人天查看权限
        long count = getCount(userIds);

        // 获取人员关联计划
        Map<String, List<ProjectScheme>> schemeByRspUsersForTime = projectSchemeService.getSchemeByRspUsersForTime(userIds, startTime, endTime);

        List<Date> daysList = new ArrayList<>();
        daysList.add(startDate);
        while (!starLocalDate.isAfter(endDate)) {
            starLocalDate = starLocalDate.plusDays(1);
            daysList.add(Date.from(starLocalDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
        }
        daysList.remove(daysList.size() - 1);
        List<Date> filterDays = daysList.stream().filter(date -> date.after(new Date())).collect(Collectors.toList());

        int finalWorkdays = workdays;
        return userList.stream().map(m->{
            ProjectHumanResourcePageVO projectHumanResourcePageVO = new ProjectHumanResourcePageVO();
            projectHumanResourcePageVO.setUserId(m.getId());
            projectHumanResourcePageVO.setUserName(m.getName());
            projectHumanResourcePageVO.setJobName(m.getUserType());
            projectHumanResourcePageVO.setDeptName(m.getRemark());
            projectHumanResourcePageVO.setPlanPersonDay(String.valueOf(finalWorkdays));
            Map<Date, Double> dateDoubleMap = map.get(m.getId());
            if (CollectionUtil.isNotEmpty(dateDoubleMap)){
                // 计算总工时并将其转换为实际工作天数，假设每天工作8小时
                Double workHours = dateDoubleMap.values().stream().collect(Collectors.summingDouble(f -> f)) / 8;
                // 设置实际工作天数
                projectHumanResourcePageVO.setActualPersonDay(df.format(workHours));
                // 比较计划工作天数和实际工作天数
                int compare = Double.compare(Double.parseDouble(projectHumanResourcePageVO.getPlanPersonDay()), Double.parseDouble(projectHumanResourcePageVO.getActualPersonDay()));
                // 如果实际工作天数少于计划工作天数,并且当前登录人没有查看权限将实际人天设置为计划人天
                if (compare < 0 && count == 0){
                    projectHumanResourcePageVO.setActualPersonDay(String.valueOf(finalWorkdays));
                }
                // 计算实际饱和度
                double actualPersonDay = Double.parseDouble(projectHumanResourcePageVO.getActualPersonDay());
                double planPersonDay = Double.parseDouble(projectHumanResourcePageVO.getPlanPersonDay());
                projectHumanResourcePageVO.setActualSaturationRate(df.format(actualPersonDay / planPersonDay) );
                // 筛选出当前时间之前人员填报数
                Map<Date, Double> collect = dateDoubleMap.entrySet().stream()
                        .filter(entry -> !entry.getKey().after(new Date()))
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
                projectHumanResourcePageVO.setBeforeTimePersonArray(collect);
            }
            // 筛选当前时间之后人员关联项目计划个数
            List<ProjectScheme> schemeList = schemeByRspUsersForTime.get(m.getId());
            if (CollectionUtil.isNotEmpty(schemeList)){
                Map<Date, Integer> daysMap = new HashMap<>();

                filterDays.stream().forEach(c->{
                    List<ProjectScheme> collect = schemeList.stream().filter(f -> !c.before(f.getBeginTime()) && !c.after(f.getEndTime())).collect(Collectors.toList());
                    daysMap.put(c, collect.size());
                });
                projectHumanResourcePageVO.setAfterTimePersonArray(daysMap);
            }
            return projectHumanResourcePageVO;
        }).collect(Collectors.toList());

    }
    private static LocalDate convertToDate(Date date) {
        return date.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
    }

    private List<UserDO> getUserDOS(LambdaQueryWrapperX<UserDO> queryWrapperX) {
        return userDOMapper.selectList(queryWrapperX);
    }

    private Map<String, Map<Date, Double>> getWorkHoursForUserMap(Date startTime, Date endTime, List<String> userIds) throws Exception {
        WorkHoursReportDetailForUserRequest workHoursReportDetailForUserRequest = new WorkHoursReportDetailForUserRequest();
        workHoursReportDetailForUserRequest.setUserIds(userIds);
        workHoursReportDetailForUserRequest.setStartTime(startTime);
        workHoursReportDetailForUserRequest.setEndTime(endTime);
        return workHoursReportDetailForUserService.userWorkHoursForTime(workHoursReportDetailForUserRequest);
    }

    private long getCount(List<String> userIds) {
        String userId = currentUserHelper.getUserId();
        UserVO userVo = userRedisHelper.getUserById(userId);
        List<Object> objects = Arrays.asList(userIds, userVo.getOrganizations().stream().map(DeptVO::getId).collect(Collectors.toList()), userVo.getRoles().stream().map(RoleVO::getId).collect(Collectors.toList())).stream().filter(Objects::nonNull).collect(Collectors.toList());
        LambdaQueryWrapperX<ProjectHumanResourceSetting> wrapperX = new LambdaQueryWrapperX<>(ProjectHumanResourceSetting.class);
        wrapperX.in(ProjectHumanResourceSetting::getDataId, objects);
        return this.count(wrapperX);
    }

    @NotNull
    private LambdaQueryWrapperX<UserDO> getUserDOLambdaQueryWrapperX() {
        LambdaQueryWrapperX<UserDO> queryWrapperX = new LambdaQueryWrapperX<>(UserDO.class);
        queryWrapperX.leftJoin(DeptUserDO.class,DeptUserDO::getUserId, UserDO::getId);
        queryWrapperX.leftJoin(DeptDO.class, DeptDO::getId, DeptUserDO::getDeptId);
        queryWrapperX.leftJoin(JobUserDO.class, JobUserDO::getUserId, UserDO::getId);
        queryWrapperX.leftJoin(JobDO.class, JobDO::getId, JobUserDO::getJobId);
        queryWrapperX.select(UserDO::getId, UserDO::getName, UserDO::getUserType);
        queryWrapperX.selectAs(DeptDO::getName, UserDO::getRemark);
        queryWrapperX.selectAs(JobDO::getName, UserDO::getUserType);
        return queryWrapperX;
    }


}
