<template>
  <layout :options="{ body: { scroll: true } }">
    <!-- <template #header>
      <div class="demo-header">
        <div class="mb10">项目列表</div>
        <span>项目管理 / 项目库 / 项目列表</span>
      </div>
    </template> -->
    <div class="productLibraryIndex1 layoutPage">
      <div class="productLibraryIndex_content layoutPage_content">
        <div class="productLibraryIndex_title">
          <div class="btnItem">
            <div
              class="addModel productLibraryIndex_btn"
              @click="addNode"
            >
              <!-- <PlusCircleOutlined /> -->
              <PlusOutlined />

              <span class="labelSpan">创建问题</span>
            </div>
            <div class="productLibraryIndex_btn blueFont">
              <ImportOutlined />
              <span class="labelSpan">导入数据</span>
            </div>
            <div class="productLibraryIndex_btn blueFont">
              <ExportOutlined />
              <span class="labelSpan">导出数据</span>
            </div>

            <!-- <div class="addModel productLibraryIndex_btn">
              <ImportOutlined />
              <span class="labelSpan">导入数据</span>
            </div>
            <div class="addModel productLibraryIndex_btn">
              <ExportOutlined />
              <span class="labelSpan">导出数据</span>
            </div> -->
            <!-- <div class="productLibraryIndex_btn" @click="multiDelete">
              <DeleteOutlined />
              <span class="labelSpan">批量删除</span>
            </div> -->
          </div>
          <div class="btnItem searchcenter">
            <a-input-search
              v-model:value="searchvlaue"
              placeholder="请输入名称或编号"
              style="width: 240px; margin-right: 8px"
              allow-clear
              @search="onSearch"
            />
          </div>
        </div>
        <div class="productLibraryIndex_table">
          <BasicTable
            class="pdmBasicTable"
            title=""
            title-help-message=""
            :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
            :columns="columns"
            :data-source="dataSource"
            :bordered="false"
            :can-resize="true"
            :show-index-column="false"
            :pagination="pagination"
            row-key="id"
            @register="registerTable"
            @change="handleChange"
            @rowClick="clickRow"
          >
            <template #proposedTime="{ text }">
              {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
            </template>
            <template #predictEndTime="{ text }">
              {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
            </template>
            <template #modifyTime="{ text }">
              {{ text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '' }}
            </template>
            <template #seriousLevelName="{ text }">
              <span
                v-if="text"
                :style="{ color: text == '严重' ? 'red' : '' }"
              >
                {{ text }}
              </span>
            </template>
            <template #schedule="{ text }">
              {{ text }}%
            </template>
            <template #seriousLevel="{ text }">
              <span
                v-if="text"
                :style="{ color: text == '最高' ? 'red' : '' }"
              >
                {{ text }}
              </span>
            </template>

            <template #statusName="{ text }">
              <span
                :style="{
                  color: text == '未开始' ? 'lightgray' : text == '处理中' ? 'green' : 'blue'
                }"
              >
                {{ text }}
              </span>
            </template>
          </BasicTable>
        </div>
      </div>

      <newButtonModal
        :btn-object-data="btnObjectData"
        @clickType="clickType"
      />
      <!-- 查看详情弹窗 -->
      <checkDetails :data="nodeData" />
      <!-- 简易弹窗提醒 -->
      <messageModal
        :title="'确认提示'"
        :show-visible="showVisible"
        @cancel="showVisible = false"
        @confirm="confirm"
      >
        <div class="messageVal">
          <InfoCircleOutlined />
          <span>{{ message }}</span>
        </div>
      </messageModal>
      <!-- 新建/编辑抽屉 -->
      <ZkAddModal
        :form-item-arr="formItemArr"
        :data="addNodeModalData"
        :list-data="editdataSource"
        :projectid="id"
        :other-api="otherApi"
        @success="successSave"
      />
      <!-- 高级搜索抽屉 -->
      <searchModal
        :data="searchData"
        @search="searchTable"
      />
    </div>
  </layout>
</template>
<script lang="ts">
import { formItemArr, otherApi } from './src/formItemArr';

import {
  defineComponent, reactive, toRefs, computed, onMounted,
} from 'vue';
import {
  useActionsRecord, Layout, OrionTable, BasicTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer,
} from 'lyra-component-vue3';
import {
  Dropdown, Menu, message, Progress,
} from 'ant-design-vue';
import {
  PlusCircleOutlined,
  InfoCircleOutlined,
  DeleteOutlined,
  ImportOutlined,
  ExportOutlined,
  PlusOutlined,
} from '@ant-design/icons-vue';
/* 格式化时间 */
//   import addProjectModal from './modal/addProjectModal.vue';
import ZkAddModal from '/@/views/pms/projectLaborer/componentsList/ZkAddModal/index'; // ZkAddModal
import checkDetails from './modal/checkmodal.vue';
import searchModal from './modal/searchModal.vue';
import messageModal from '/@/views/pms/projectLaborer/components/MessageModal/messageModal.vue';
import { useRouter } from 'vue-router';
import newButtonModal from '/@/views/pms/projectLaborer/componentsList/buttonModal/newButtonModal.vue';
//   import { BasicTable } from '/@/components/Table';
import dayjs from 'dayjs';
import { columns } from './src/table.config';
import { questionPageApi, deleteQuestionApi } from '/@/views/pms/projectLaborer/api/questionManage';
const [registerTable, { setLoading }] = useTable();
export default defineComponent({
  name: 'ProductLibraryIndex',
  components: {
    Layout,
    //   basicTitle,
    aDropdown: Dropdown,
    /* 表格 */
    BasicTable,
    aMenu: Menu,
    aMenuItem: Menu.Item,
    /* 添加图标 */
    PlusCircleOutlined,
    /* 删除图标 */
    DeleteOutlined,
    //   提示图标
    InfoCircleOutlined,
    //   addNodeModal,
    messageModal,
    checkDetails,
    newButtonModal,
    /* 新建项目抽屉 */
    //   addProjectModal,
    ZkAddModal,
    /* 进度条 */
    Progress,
    /* 高级搜索 */
    searchModal,
    ImportOutlined,
    ExportOutlined,
    PlusOutlined,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
  },

  setup(props) {
    //   const router = useRouter();
    //   const layoutModelStore = layoutModel();
    const state = reactive({
      /* 搜索框value */
      searchvlaue: '',
      /* 编辑send */
      editdataSource: {},
      /* 多选 */
      selectedRowKeys: [],
      /* 列 */
      dataSource: [],
      tablehttp: {
        orders: [
          {
            asc: false,
            column: '',
          },
        ],

        query: {
          projectId: '',
        },
        // 条数
        pageSize: 10,
        /* 页数 */
        pageNum: 1,
        /* 总数 */
        total: 0,
        queryCondition: [],
      },
      // 条数
      pageSize: 10,
      /* 页数 */
      current: 1,
      /* 总数 */
      total: 20,
      addNodeModalData: {},
      /* 选择行id */
      selectedRows: [],
      showVisible: false,
      /* 简易弹窗提醒消息 */
      message: '',
      nodeData: {},
      searchData: {},
      params: {},
      /* 高度 */
      tableHeight: 400,
      /* 右侧功能按钮 */
      btnObjectData: {
        check: { show: true },
        open: { show: true },
        add: { show: true },
        delete: { show: true },
        edit: { show: true },
        search: { show: true },
      },
    });
      /* 分页 */
    const pagination = computed(() => ({
      pageSize: state.tablehttp.pageSize,
      current: state.tablehttp.pageNum,
      total: state.tablehttp.total,
      // showQuickJumper: true,
      showSizeChanger: true,
      showTotal: (total) => `共${total}条`,
    }));
      /* 多选cb */
    const onSelectChange = (selectedRowKeys, selectedRows) => {
      state.selectedRowKeys = selectedRowKeys;
      state.selectedRows = selectedRows;
    };
      /* 页数变化cb */
    const handleChange = (pag, filters, sorter: any) => {
      // console.log('测试🚀 ~ file: index.vue ~ line 254 ~ pag', pag);
      // console.log('测试🚀 ~ file: index.vue ~ line 254 ~ sorter', sorter);
      // 如果是多选触发,则不更新页面
      if (typeof pag.current === 'undefined') return;
      state.tablehttp.pageNum = pag.current;
      state.tablehttp.pageSize = pag.pageSize;
      state.tablehttp.orders[0].asc = sorter.order == 'ascend';
      // state.tablehttp.orders[0].column = sorter.column?.key;
      state.tablehttp.orders[0].column = sorter.columnKey;
      getFormData();
    };
      /* 右按钮 */
    const clickType = (type) => {
      switch (type) {
        case 'edit':
          editNode();
          break;
        case 'check':
          checkData();
          break;
        case 'add':
          addNode();
          break;
        case 'open':
          openDetail();
          break;
        case 'delete':
          // deleteNode();
          multiDelete();
          break;
        case 'search':
          state.searchData = {};
          break;
      }
    };
    const router = useRouter();

    /* 编辑 */
    const editNode = () => {
      if (lengthCheckHandle()) return;
      // state.selectedRows = [];
      console.log('测试🚀🚀 ~~~ state.selectedRows ', state.selectedRows);

      state.addNodeModalData = { formType: 'edit' };
      state.editdataSource = {
        //   ...state.dataSource.filter((item) => {
        //     return item.id == state.selectedRowKeys[0];
        //   })
        ...state.selectedRows,
      };
    };
      /* 删除 */
    const deleteNode = () => {
      if (lengthCheckHandle()) return;
      // state.selectedRows = [];

      state.message = '请确认是否对当前选中数据进行删除？';
      state.showVisible = true;
    };
      /* 简易弹窗的确定cb */
    const confirm = () => {
      // 删除操作
      deletrow();
    };
    onMounted(() => {
      /* 高度变化 */
      // state.tableHeight = document.body.clientHeight - 365;
      state.tableHeight = document.body.clientHeight - 420;
      console.log('测试🚀🚀 ~~~ state.tableHeight', state.tableHeight);

      getFormData();
    });
    /* 删除操作 */
    const deletrow = () => {
      // new Api('/pms')
      //   .fetch(state.selectedRowKeys, `project/removeBatch/`, 'DELETE')
      deleteQuestionApi(state.selectedRowKeys)
        .then((res) => {
          message.success('删除成功');
          state.showVisible = false;
          getFormData();
        })
        .catch(() => {
          state.showVisible = false;
        });
    };
    const getFormData = async () => {
      setLoading(true);

      state.tablehttp.query.projectId = props.id;
      const res = await questionPageApi(state.tablehttp);
      state.dataSource = res.content;
      state.tablehttp.total = res.totalSize;
      state.selectedRowKeys = [];
      state.selectedRows = [];
      setLoading(false);
    };
      /* 查看详情 */
    const checkData = () => {
      if (lengthCheckHandle()) return;
      // state.selectedRows = [];

      state.nodeData = {
        ...state.dataSource.filter((item) => item.id == state.selectedRowKeys[0]),
      };
    };
      /* 检查选择条数fn */
    const lengthCheckHandle = () => {
      if (state.selectedRows.length > 1) {
        message.warning('请选择一条数据进行操作');
        return true;
      }
      if (state.selectedRows.length == 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };
      /* 批量检查选择条数fn */
    const multiLengthCheckHandle = () => {
      if (state.selectedRows.length == 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };
    const searchTable = (params) => {
      state.tablehttp.query = params.params;
      state.tablehttp.queryCondition = params.queryCondition;

      getFormData();
    };
      /* 打开按钮 */
    const openDetail = () => {
      if (lengthCheckHandle()) return;

      toDetails(state.selectedRows[0]);
    };
    const toDetails = (data) => {
      router.push({
        name: 'QuestionDetails',
        query: {
          id: data.id,
          projectId: props.id,
          type: 0,
        },
      });
    };
      /* 新建项目 */
    const addNode = () => {
      state.addNodeModalData = {
        formType: 'add',
      };
    };
      /* 批量删除 */
    const multiDelete = () => {
      if (multiLengthCheckHandle()) return;

      state.message = '请确认是否对当前选中数据进行删除？';
      state.showVisible = true;
    };
      /* 搜索右上 */
    const onSearch = () => {
      /* gettable */
      state.tablehttp.queryCondition = <any>[
        {
          column: 'name',
          type: 'like',
          link: 'or',
          value: state.searchvlaue,
        },
        {
          column: 'number',
          type: 'like',
          link: 'or',
          value: state.searchvlaue,
        },
      ];
      state.tablehttp.query = { projectId: '' };
      getFormData();
    };
      /* 新建项目成功回调 */
    const successSave = () => {
      state.tablehttp.pageNum = 1;
      state.selectedRowKeys = [];
      state.selectedRows = [];

      getFormData();
    };
    const clickRow = (record, index) => {
      const num = state.selectedRowKeys.findIndex((item) => item === record.id);
      num === -1 ? state.selectedRowKeys.push(record.id) : state.selectedRowKeys.splice(num, 1);
      const row = state.selectedRows.findIndex((item) => item.id === record.id);
      row === -1 ? state.selectedRows.push(record) : state.selectedRows.splice(row, 1);
    };
    return {
      ...toRefs(state),
      clickRow,
      clickType,
      /* 分页 */
      pagination,
      /* 行 */
      columns,
      /* 多选 */
      onSelectChange,
      /* 多选变化 */
      handleChange,
      /* 简易弹窗cb */
      confirm,
      /* 新增按钮 */
      addNode,
      dayjs,
      /* 批量删除 */
      multiDelete,
      /* 搜索右上角 */
      onSearch,
      successSave,
      searchTable,
      registerTable,
      setLoading,
      formItemArr,
      otherApi,
    };
  },

  mounted() {},
});
</script>
<style lang="less" scoped>
  @import url('/@/views/pms/projectLaborer/statics/style/page.less');
  @import url('/@/views/pms/projectLaborer/statics/style/margin.less');
</style>
