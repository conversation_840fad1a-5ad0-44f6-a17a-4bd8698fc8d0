package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.IncomePlanDataDTO;
import com.chinasie.orion.domain.entity.ContractMilestone;
import com.chinasie.orion.domain.entity.IncomePlanData;
import com.chinasie.orion.domain.vo.ContractMilestoneVO;
import com.chinasie.orion.domain.vo.IncomePlanDataTotalVO;
import com.chinasie.orion.domain.vo.IncomePlanDataVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.List;


/**
 * <p>
 * IncomePlanData 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29 19:19:47
 */
public interface IncomePlanDataService extends OrionBaseService<IncomePlanData> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    IncomePlanDataVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param incomePlanDataDTO
     */
    String create(IncomePlanDataDTO incomePlanDataDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param incomePlanDataDTO
     */
    Boolean edit(IncomePlanDataDTO incomePlanDataDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<IncomePlanDataVO> pages(Page<IncomePlanDataDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file,String incomePlanId) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(IncomePlanDataDTO incomePlanDataDTO, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<IncomePlanDataVO> vos) throws Exception;

    List<IncomePlanDataVO>  getList(IncomePlanDataDTO incomePlanDataDTO) throws Exception;

    List<IncomePlanDataVO>  getDifferentList(IncomePlanDataDTO incomePlanDataDTO) throws Exception;

    Boolean close(String id);

    List<IncomePlanData> saveIncomePlanData(List<IncomePlanDataDTO> incomePlanDataDTOS) throws Exception;

    IncomePlanDataTotalVO getTotal(IncomePlanDataDTO incomePlanDataDTO);


    Boolean report(String incomePlanId, String type);

    List<IncomePlanDataVO> getQuarterList(IncomePlanDataDTO incomePlanDataDTO,String incomePlanId) throws Exception;

    Boolean saveQuarterData(String mil, String incomePlanId) throws ParseException;

    public void saveLock(List<IncomePlanData> incomePlanDatas);

    IncomePlanDataVO getIncomePlanData(String milestoneId);

    List<DataStatusVO> listDataStatus();

    void exportDifferentList(IncomePlanDataDTO incomePlanDataDTO, HttpServletResponse response) throws Exception;

    void exportQuarterList(IncomePlanDataDTO incomePlanDataDTO, HttpServletResponse response) throws Exception;

    String getButtonPermission();
}
