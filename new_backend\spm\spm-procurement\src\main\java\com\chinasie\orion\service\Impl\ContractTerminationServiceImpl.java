package com.chinasie.orion.service.Impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.domain.dto.ContractTerminationDTO;
import com.chinasie.orion.domain.dto.NcfFormpurchaseRequestDTO;
import com.chinasie.orion.domain.entity.ContractTermination;
import com.chinasie.orion.domain.vo.ContractTerminationVO;
import com.chinasie.orion.repository.ContractTerminationMapper;
import com.chinasie.orion.service.ContractTerminationService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <p>
 * ContractTermination 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@Service
@Slf4j
public class ContractTerminationServiceImpl extends OrionBaseServiceImpl<ContractTerminationMapper, ContractTermination> implements ContractTerminationService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ContractTerminationVO detail(String id, String pageCode) throws Exception {
        ContractTermination contractTermination = this.getById(id);
        ContractTerminationVO result = BeanCopyUtils.convertTo(contractTermination, ContractTerminationVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param contractTerminationDTO
     */
    @Override
    public String create(ContractTerminationDTO contractTerminationDTO) throws Exception {
        ContractTermination contractTermination = BeanCopyUtils.convertTo(contractTerminationDTO, ContractTermination::new);
        this.save(contractTermination);

        String rsp = contractTermination.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param contractTerminationDTO
     */
    @Override
    public Boolean edit(ContractTerminationDTO contractTerminationDTO) throws Exception {
        ContractTermination contractTermination = BeanCopyUtils.convertTo(contractTerminationDTO, ContractTermination::new);

        this.updateById(contractTermination);

        String rsp = contractTermination.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ContractTerminationVO> pages(Page<ContractTerminationDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ContractTermination> condition = new LambdaQueryWrapperX<>(ContractTermination.class);
        condition.select(" t.*," +
                "CASE " +
                "    WHEN BIN(t.is_pre_sign_termination) like '%0001%' THEN '是'" +
                "    WHEN BIN(t.is_pre_sign_termination) like '%0000%' THEN '否'" +
                "    END AS 'isPreSignTerminationTurn'");
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }

        if (pageRequest.getQuery() != null) {
            //变更申请日期段
            if (pageRequest.getQuery().getStartDate() != null && pageRequest.getQuery().getEndDate() != null) {
                condition.between(ContractTermination::getTerminationRequestDate, pageRequest.getQuery().getStartDate(), pageRequest.getQuery().getEndDate());
            }
        }
        if (ObjectUtil.isNotEmpty(pageRequest.getQuery())&& pageRequest.getQuery().getIsPreSignTermination()!=null){
            String bitCondition;
            if (pageRequest.getQuery().getIsPreSignTermination()){
                bitCondition = "110001";
            }else {
                bitCondition = "110000";
            }
            condition.apply("BIN(is_pre_sign_termination) like '%"+bitCondition+"%'");
        }
        condition.orderByDesc(ContractTermination::getCreateTime);

        Page<ContractTermination> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ContractTermination::new));

        PageResult<ContractTermination> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ContractTerminationVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ContractTerminationVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ContractTerminationVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ContractTerminationVO> getByCode(Page<ContractTerminationDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<ContractTermination> condition = new LambdaQueryWrapperX<>(ContractTermination.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.eq(ContractTermination::getContractNumber, pageRequest.getQuery().getContractNumber());
        condition.orderByDesc(ContractTermination::getCreateTime);
        Page<ContractTermination> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ContractTermination::new));

        PageResult<ContractTermination> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ContractTerminationVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ContractTerminationVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ContractTerminationVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "合同终止信息表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractTerminationDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ContractTerminationExcelListener excelReadListener = new ContractTerminationExcelListener();
        EasyExcel.read(inputStream, ContractTerminationDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ContractTerminationDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("合同终止信息表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ContractTermination> contractTerminationes = BeanCopyUtils.convertListTo(dtoS, ContractTermination::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("ncf::ContractTermination-import::id", importId, contractTerminationes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ContractTermination> contractTerminationes = (List<ContractTermination>) orionJ2CacheService.get("ncf::ContractTermination-import::id", importId);
        log.info("合同终止信息表导入的入库数据={}", JSONUtil.toJsonStr(contractTerminationes));

        this.saveBatch(contractTerminationes);
        orionJ2CacheService.delete("ncf::ContractTermination-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("ncf::ContractTermination-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(Page<NcfFormpurchaseRequestDTO> pageRequest, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ContractTermination> condition = new LambdaQueryWrapperX<>(ContractTermination.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ContractTermination::getCreateTime);
        List<ContractTermination> contractTerminationes = this.list(condition);

        List<ContractTerminationDTO> dtos = BeanCopyUtils.convertListTo(contractTerminationes, ContractTerminationDTO::new);

        String fileName = "合同终止信息表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractTerminationDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<ContractTerminationVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }


    public static class ContractTerminationExcelListener extends AnalysisEventListener<ContractTerminationDTO> {

        private final List<ContractTerminationDTO> data = new ArrayList<>();

        @Override
        public void invoke(ContractTerminationDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ContractTerminationDTO> getData() {
            return data;
        }
    }


}
