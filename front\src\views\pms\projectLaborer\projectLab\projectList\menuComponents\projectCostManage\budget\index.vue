<template>
  <Layout
    :options="{ body: { scroll: true } }"
  >
    <!--    预算编制-->
    <Budgeting
      v-if="isBudgetType && isPower('PMS_XMXQ_container_04_01_01',powerData)"
      :id="props.id"
      @budgetType="budgetType"
    />
    <!-- 预算执行-->
    <BudgetExecute
      v-else-if="!isBudgetType && isPower('PMS_XMXQ_container_04_01_02',powerData)"
      :id="props.id"
      @budgetType="budgetType"
    />
  </layout>
</template>
<script setup lang="ts">
import { ref, inject } from 'vue';
import {
  isPower,
  Layout,
} from 'lyra-component-vue3';
import BudgetExecute from './components/budgetExecute/index.vue';
import Budgeting from './components/budgeting/index.vue';
const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});
const isBudgetType = ref(true);
const powerData = inject('powerData');
function budgetType(isTab) {
  isBudgetType.value = isTab;
}
</script>
<style scoped lang="less">

</style>
