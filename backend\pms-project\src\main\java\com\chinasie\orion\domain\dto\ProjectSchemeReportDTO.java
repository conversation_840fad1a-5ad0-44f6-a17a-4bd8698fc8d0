package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: lsy
 * @date: 2024/5/23
 * @description:
 */
@Data
public class ProjectSchemeReportDTO {

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "已完成计划")
    private Boolean completeTask = false;

    @ApiModelProperty(value = "逾期计划")
    private Boolean overdueTask = false;

    @ApiModelProperty(value = "变更计划")
    private Boolean changeTask = false;

    @ApiModelProperty(value = "反馈计划")
    private Boolean taskFeedback = false;

}
