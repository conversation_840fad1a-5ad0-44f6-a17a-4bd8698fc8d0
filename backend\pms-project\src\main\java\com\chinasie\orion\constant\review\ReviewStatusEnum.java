package com.chinasie.orion.constant.review;

import java.util.Objects;

public enum ReviewStatusEnum {
    CREATED(101, "待评审"),
    GOING(110, "评审中"),
    COMPLETE(130, "已评审"),
    ADD_LOTUS(150,"添加文审意见"),
    ADD_APPRAISAL(140,"添加评审意见"),
    ;

    private Integer code;
    private String name;


    ReviewStatusEnum(Integer code, String name) {
        this.name = name;
        this.code = code;
    }


    public String getName() {
        return name;
    }

    public Integer getCode() {
        return code;
    }


    private static ReviewStatusEnum getEnumByCode(Integer code) {
        for (ReviewStatusEnum performanceAppraiseStatusEnum : ReviewStatusEnum.values()) {
            if (Objects.equals(performanceAppraiseStatusEnum.getCode(), code)) {
                return performanceAppraiseStatusEnum;
            }
        }
        return null;
    }
}
