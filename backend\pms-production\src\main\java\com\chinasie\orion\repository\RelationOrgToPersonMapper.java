package com.chinasie.orion.repository;
import com.chinasie.orion.domain.dto.MajorRepairOrgJobSimpleDTO;
import com.chinasie.orion.domain.entity.PersonMange;
import com.chinasie.orion.domain.entity.RelationOrgToPerson;
import com.chinasie.orion.domain.vo.PersonTmpVO;
import org.apache.ibatis.annotations.Mapper;

import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <p>
 * RelationOrgToPerson Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18 09:40:03
 */
@Mapper
public interface RelationOrgToPersonMapper extends  OrionBaseMapper  <RelationOrgToPerson> {
    void updatePersonTime(@Param("date") MajorRepairOrgJobSimpleDTO date);

    void updatePersonTimeByList(@Param("list") List<String> list);



    List<PersonTmpVO> getPersonManageTreeDataStrategy(@Param("repairRound") String repairRound,@Param("orgIds") List<String> orgIds, @Param("sql") String sql, @Param("keyword")String keyword);


    List<PersonTmpVO> getPersonManageTreeData(@Param("repairRound") String repairRound,@Param("keyword") String keyword, @Param("ids") List<String> ids);


    List<PersonMange> getJobPostNames(@Param("baseCode") String baseCode, @Param("codeList")List<String> codeList);
}

