package com.chinasie.orion.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

import java.util.List;
/**
 * CostShare VO对象
 *
 * <AUTHOR>
 * @since 2024-11-18 09:29:00
 */
@ApiModel(value = "CostShareVO对象", description = "成本分摊")
@Data
public class CostShareVO extends  ObjectVO   implements Serializable{

            /**
         * 项目Id
         */
        @ApiModelProperty(value = "项目Id")
        private String projectId;


        /**
         * 项目编码
         */
        @ApiModelProperty(value = "项目编码")
        private String projectNumber;


        /**
         * 公司Id
         */
        @ApiModelProperty(value = "公司Id")
        private String companyId;

        @ApiModelProperty(value = "公司编码")
        private String companyNumber;

        @ApiModelProperty(value = "公司名称")
        private String companyName;



        @ApiModelProperty(value = "项目名称")
        private String projectName;

        @ApiModelProperty(value = "项目开始时间")
        @DateTimeFormat("yyyy/MM/dd")
        private Date projectBeginTime;

        @ApiModelProperty(value = "项目结束时间")
        @DateTimeFormat("yyyy/MM/dd")
        private Date projectEndTime;



        @ApiModelProperty(value = "项目经理名称")
        private String pmName;

        @ApiModelProperty(value = "委托方一代码")
        private String clientOneCode;

        @ApiModelProperty(value = "委托方一名称")
        private String clientOneName;

        @ApiModelProperty(value = "委托方二代码")
        private String clientTwoCode;

        @ApiModelProperty(value = "委托方二名称")
        private String clientTwoName;

        @ApiModelProperty(value = "委托方三代码")
        private String clientThreeCode;

        @ApiModelProperty(value = "委托方三名称")
        private String clientThreeName;


        /**
         * 集团内外
         */
        @ApiModelProperty(value = "集团内外")
        private String internalExternal;


        /**
         * 核电
         */
        @ApiModelProperty(value = "核电")
        private String nuclearPower;


        /**
         * 基地
         */
        @ApiModelProperty(value = "基地")
        private String base;


        /**
         * WBS对象
         */
        @ApiModelProperty(value = "WBS对象")
        private String wbsObject;


        /**
         * 年度
         */
        @ApiModelProperty(value = "年度")
        private Integer year;

        @ApiModelProperty(value = "WBS所属专业中心")
        private String wbsExpertiseCenter;

        @ApiModelProperty(value = "WBS所属专业中心名称")
        private String wbsExpertiseCenterName;

        /**
         * WBS所属利润中心
         */
        @ApiModelProperty(value = "WBS所属利润中心")
        private String wbsProfessionalCenter;


        /**
         * 业务分类
         */
        @ApiModelProperty(value = "业务分类")
        private String businessClassification;


        /**
         * 金额
         */
        @ApiModelProperty(value = "金额")
        private BigDecimal amount;


        /**
         * 分摊分类
         */
        @ApiModelProperty(value = "分摊分类")
        private String apportionmentClassification;

        @ApiModelProperty(value = "分摊分类名称")
        private String apportionmentClassificationName;


        /**
         * 成本类型
         */
        @ApiModelProperty(value = "成本类型")
        private String costType;

        @ApiModelProperty(value = "成本类型名称")
        private String costTypeName;


        /**
         * 成本元素大类
         */
        @ApiModelProperty(value = "成本元素大类")
        private String costElementCategorie;


        /**
         * 成本元素
         */
        @ApiModelProperty(value = "成本元素")
        private String costElement;


        /**
         * 发送部门
         */
        @ApiModelProperty(value = "发送部门")
        private String sendDeptId;

        @ApiModelProperty(value = "发送部门名称")
        private String sendDeptIdName;


        /**
         * 源发送部门
         */
        @ApiModelProperty(value = "源发送部门")
        private String sourceSendDept;

        @ApiModelProperty(value = "源发送部门名称")
        private String sourceSendDeptName;


        /**
         * 期间
         */
        @ApiModelProperty(value = "期间")
        private String period;


        /**
         * 凭证编码
         */
        @ApiModelProperty(value = "凭证编码")
        private String credentialCode;


        /**
         * 凭证日期
         */
        @ApiModelProperty(value = "凭证日期")
        private Date voucherDate;


        /**
         * 科目代码
         */
        @ApiModelProperty(value = "科目代码")
        private String subjectCode;


        /**
         * 科目名称
         */
        @ApiModelProperty(value = "科目名称")
        private String subejctName;


        /**
         * 凭证利润中心代码
         */
        @ApiModelProperty(value = "凭证利润中心代码")
        private String voucherProfitCenterCode;


        /**
         * 凭证利润中心名称
         */
        @ApiModelProperty(value = "凭证利润中心名称")
        private String voucherProfitCenterName;


        /**
         * 凭证成本中心代码
         */
        @ApiModelProperty(value = "凭证成本中心代码")
        private String voucherCostCenterCode;


        /**
         * 凭证成本中心名称
         */
        @ApiModelProperty(value = "凭证成本中心名称")
        private String voucherCostCenterName;

        @ApiModelProperty(value = "WBS对象名称号")
        private String wbsObjectName;



    

}
