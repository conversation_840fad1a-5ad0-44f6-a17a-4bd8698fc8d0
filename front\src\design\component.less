//===========-历史组件一些样式，兼容保留-===========
//===========-后期将会去除-======================
.layoutPage {
  display: flex;
  justify-content: space-between;
  height: calc(~'100% - 50px');
}
.ant-input-search {
  width: 100%;
}

.layoutPage_content {
  width: calc(~'100% - 60px');
  padding: 20px 20px;
  overflow: auto;
}
.messageContent {
  padding-left: 10px;
  padding-bottom: 20px;
  .messageContent_row {
    display: flex;
    padding: 10px 0px;
    span {
      color: #444b5e;
    }
    .messageContent_row_label {
      display: inline-block;
      width: 120px;
      vertical-align: middle;
    }
    .messageContent_row_value {
      display: inline-block;
      vertical-align: middle;
      width: calc(~'100% - 120px');
    }
  }
}

.messageVal {
  padding: 30px 20px;
  .anticon {
    color: #96b7f3;
    margin-right: 5px;
  }
  span {
    color: #444b5e;
  }
}

.layoutPage_content_title {
  height: 40px;
  background-color: #f0f2f5;
  line-height: 40px;
  color: #444b5e;
  padding-left: 16px;
  margin-bottom: 10px;
  .anticon-caret-down {
    color: #969eb4;
    cursor: pointer;
  }
  span {
    padding-left: 6px;
  }
}

.tableName {
  color: #5172dc;
  cursor: pointer;
}
//.vben-basic-table {
//  .vben-basic-arrow {
//    cursor: pointer;
//  }
//}
.pdmBasicTable {
  .vben-basic-arrow {
    cursor: pointer;
  }
  .ant-table-column-title {
    color: #444b5e;
  }
}

.treeList_content {
  .ant-tree-treenode-selected > .ant-tree-switcher,
  .ant-tree-node-selected {
    background: ~`getPrefixVar('primary-color-deprecated-f-12')`  !important;
    .ant-tree-title {
      color: ~`getPrefixVar('primary-color')`  !important;
    }
  }
  //.ant-tree-switcher,
  //.ant-tree-node-content-wrapper {
  //  height: 40px !important;
  //  line-height: 40px !important;
  //  color: #969eb4;
  //  .ant-tree-title {
  //    color: #444b5e;
  //  }
  //}
  .ant-tree-node-content-wrapper {
    padding-right: 20px !important;
  }
}
.fa-lg {
  font-size: 22px !important;
}
.layoutPage_btn {
  width: 60px;
  text-align: center;
  border-left: 1px #f0f2f5 solid;
  overflow: auto;
}
.basicTitle_content {
  .ant-image {
    width: 100%;
  }
}
.uploadImg {
  border-radius: 5px;
}
.pdmRightDrawer {
  .nodeImg {
    width: 60px;
    height: 70px;
    margin-right: 10px;
    .uploadImg {
      width: 100%;
    }
  }
  .nodeMessage {
    width: calc(~'100% - 70px');
  }
  .ant-drawer-header {
    background: ~`getPrefixVar('primary-color')` !important;
    position: fixed !important;
    width: 340px;
    z-index: 10000;
    .ant-drawer-title {
      color: #ffffff !important;
      font-weight: 400;
    }
    .ant-drawer-close {
      color: #ffffff !important;
    }
  }

  .ant-drawer-body {
    padding: 0px !important;
    padding-top: 60px !important;
  }
  .ant-select-tree-dropdown {
    max-height: 220px !important;
  }
  .ant-select,
  .ant-input {
    color: #444b5e;
  }
}
.pdmFormClass {
  .ant-form-item-label > label {
    color: #444b5e !important;
  }
  .ant-input,
  .ant-select-selection-search-input,
  .ant-select-selection-item,
  .ant-select {
    color: #444b5e !important;
  }

  .ant-form-item-control-input-content {
    .descriptionStyle {
      min-height: 115px;
      padding-top: 5px;
    }
  }
}
.tableName {
  color: #5172dc;
  cursor: pointer;
}
.iconSvg {
  color: #969eb4;
  cursor: pointer;
  padding: 10px 0px;
  .anticon {
    font-size: 22px;
  }
  .svgPath {
    text-align: center;
    svg {
      display: inline-block;
      font-size: 24px;
    }
  }
  .labalSpan {
    display: block;
    padding-top: 2px;
  }
  &:hover {
    color: ~`getPrefixVar('primary-color')`;//#5172dc;
    background: #f5f5f5;
  }
}

.bgDC {
  background: #5172dc !important;
}
.bgF3 {
  background: #96b7f3 !important;
}
.bgFa {
  background: #c9dafa !important;
}
