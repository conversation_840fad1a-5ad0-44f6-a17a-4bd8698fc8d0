package com.chinasie.orion.management.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ContractInfo VO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@ApiModel(value = "ContractInfoVO对象", description = "合同主表信息")
@Data
public class ContractInfoVO extends ObjectVO implements Serializable {

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;


    /**
     * 是否框架合同
     */
    @ApiModelProperty(value = "是否框架合同")
    private String isFream;


    /**
     * 采购申请号
     */
    @ApiModelProperty(value = "采购申请号")
    private String purchaseApplicant;


    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;


    /**
     * 合同执行状态
     */
    @ApiModelProperty(value = "合同执行状态")
    private String statusName;


    /**
     * 框架合同剩余金额
     */
    @ApiModelProperty(value = "框架合同剩余金额")
    private BigDecimal freamResidueAmount;


    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型")
    private String type;


    /**
     * 采购订单号
     */
    @ApiModelProperty(value = "采购订单号")
    private String procurementOrderNumber;


    /**
     * 采购立项号
     */
    @ApiModelProperty(value = "采购立项号")
    private String projectCode;


    /**
     * 采购立项金额
     */
    @ApiModelProperty(value = "采购立项金额")
    private BigDecimal procurementAmount;


    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商")
    private String supplierName;


    /**
     * 工厂
     */
    @ApiModelProperty(value = "工厂")
    private String factoryName;


    /**
     * 商务负责人
     */
    @ApiModelProperty(value = "商务负责人")
    private String businessRspUser;


    /**
     * 变更金额
     */
    @ApiModelProperty(value = "变更金额")
    private BigDecimal changeMoney;


    /**
     * 变更比例
     */
    @ApiModelProperty(value = "变更比例")
    private String changePercent;


    /**
     * 支付金额
     */
    @ApiModelProperty(value = "支付金额")
    private BigDecimal payMoney;


    /**
     * 是否合同终止
     */
    @ApiModelProperty(value = "是否合同终止")
    private String isContractTerminate;


    /**
     * 索赔金额
     */
    @ApiModelProperty(value = "索赔金额")
    private BigDecimal claimAmount;


    /**
     * 索赔比例
     */
    @ApiModelProperty(value = "索赔比例")
    private String claimPercent;


    /**
     * 终止金额
     */
    @ApiModelProperty(value = "终止金额")
    private BigDecimal terminateAmount;


    /**
     * 终止比例
     */
    @ApiModelProperty(value = "终止比例")
    private String terminatePercent;


    /**
     * 框架开始时间
     */
    @ApiModelProperty(value = "框架开始时间")
    private Date freamBeginTime;


    /**
     * 框架结束时间
     */
    @ApiModelProperty(value = "框架结束时间")
    private Date freamEndTime;


    /**
     * 框架合同已使用金额
     */
    @ApiModelProperty(value = "框架合同已使用金额")
    private BigDecimal freamUsedAmount;


    /**
     * 是否框架有效期内
     */
    @ApiModelProperty(value = "是否框架有效期内")
    private String isFreamPeriod;


    /**
     * 支付比例
     */
    @ApiModelProperty(value = "支付比例")
    private String payPercent;


    /**
     * 采购方式
     */
    @ApiModelProperty(value = "采购方式")
    private String procurementWay;


    /**
     * 采购周期
     */
    @ApiModelProperty(value = "采购周期")
    private String procurementCycle;


    /**
     * 节约金额
     */
    @ApiModelProperty(value = "节约金额")
    private BigDecimal amountSaved;


    /**
     * 商务活动类型
     */
    @ApiModelProperty(value = "商务活动类型")
    private String businessActivityType;


    /**
     * 最终采购方式
     */
    @ApiModelProperty(value = "最终采购方式")
    private String endProcurementWay;


    /**
     * 商务文件类型
     */
    @ApiModelProperty(value = "商务文件类型")
    private String businessFileType;


    /**
     * 预计合同开始日期
     */
    @ApiModelProperty(value = "预计合同开始日期")
    private Date estimatedStartTime;


    /**
     * 预计合同结束日期
     */
    @ApiModelProperty(value = "预计合同结束日期")
    private Date estimatedEndTime;


    /**
     * 付款方式
     */
    @ApiModelProperty(value = "付款方式")
    private String payWay;


    /**
     * 合同履约状态
     */
    @ApiModelProperty(value = "合同履约状态")
    private String executionStatusName;


    /**
     * 标的类别
     */
    @ApiModelProperty(value = "标的类别")
    private String objectType;


    /**
     * 类别占比（%）
     */
    @ApiModelProperty(value = "类别占比（%）")
    private String typePercent;


    /**
     * 审批价格（RMB）
     */
    @ApiModelProperty(value = "审批价格（RMB）")
    private BigDecimal approvedPrice;


    /**
     * 最终价格（原币）
     */
    @ApiModelProperty(value = "最终价格（原币）")
    private BigDecimal finalPrice;


    /**
     * 实际合同开始日期
     */
    @ApiModelProperty(value = "实际合同开始日期")
    private Date actualStartTime;


    /**
     * 实际合同结束日期
     */
    @ApiModelProperty(value = "实际合同结束日期")
    private Date actualEndTime;


    /**
     * 预计合同交付日期
     */
    @ApiModelProperty(value = "预计合同交付日期")
    private Date estimatedDeliveryTime;


    /**
     * 验收结果
     */
    @ApiModelProperty(value = "验收结果")
    private String acceptanceResults;


    /**
     * 实际验收日期
     */
    @ApiModelProperty(value = "实际验收日期")
    private Date actualAcceptanceTimes;


    /**
     * 是否发布启动公示
     */
    @ApiModelProperty(value = "是否发布启动公示")
    private String isPublicLaunch;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 是否办理履约保证金
     */
    @ApiModelProperty(value = "是否办理履约保证金")
    private String isPerformanceBond;


    /**
     * 保证金支付方式
     */
    @ApiModelProperty(value = "保证金支付方式")
    private String marginPaymentMethod;


    /**
     * 保证金
     */
    @ApiModelProperty(value = "保证金")
    private BigDecimal securityDeposit;


    /**
     * 是否参与计算
     */
    @ApiModelProperty(value = "是否参与计算	")
    private String isCalculation;


    /**
     * 采购立项审批完成时间
     */
    @ApiModelProperty(value = "采购立项审批完成时间")
    private Date projectEndTime;


    /**
     * 合同推荐审批完成时间
     */
    @ApiModelProperty(value = "合同推荐审批完成时间")
    private Date recommendEndTime;


    /**
     * 商务是否推荐供应商
     */
    @ApiModelProperty(value = "商务是否推荐供应商")
    private String isBizRecommend;

    /**
     * 价格模式
     */
    @ApiModelProperty(value = "价格模式")
    private String priceModel;

    /**
     * 所属部处
     */
    @ApiModelProperty(value = "所属部处")
    private String subdivision;

    /**
     * 是否填写一次验收合格
     */
    @ApiModelProperty(value = "是否填写一次验收合格")
    private String isFillOnetimeAcceptance;

    /**
     * 预警日期
     */
    @ApiModelProperty(value = "预警日期")
    private String warningDay;

    /**
     * 预警金额
     */
    @ApiModelProperty(value = "预警金额")
    private String warningMoney;


    /**
     * 技术负责人
     */
    @ApiModelProperty(value = "技术负责人")
    private String technicalRspUser;

    /**
     * 采购组
     */
    @ApiModelProperty(value = "采购组")
    private String procurementGroupName;

    /**
     * 采购申请号
     */
    @ApiModelProperty(value = "采购申请号所有拼接")
    private String purchaseApplicantTotal;


    @ApiModelProperty(value = "供应商来源")
    private String supplierFrom;


    /**
     * 是否非技术推荐供应商参与
     */
    @ApiModelProperty(value = "是否非技术推荐供应商参与")
    private String isTechSupplier;

    /**
     * 采购计划号
     */
    @ApiModelProperty(value = "采购计划号")
    private String purchasePlanCode;

    /**
     * 采购申请单编码
     */
    @ApiModelProperty(value = "采购申请单编码")
    private String purchaseRequestCode;

    @ApiModelProperty(value = "申请单名称")
    private String applicationName;

    @ApiModelProperty(value = "归口管理")
    private String bkManage;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "变更后合同承诺总价（总目标值）")
    private BigDecimal contactAmountAfterChange;

    @ApiModelProperty(value = "累计索赔金额（含本次）")
    private BigDecimal cumulativeClaimAmount;

    @ApiModelProperty(value = "总累计索赔占原合同价%")
    private String totalClaimPctOfOrigPrice;

    @ApiModelProperty(value = "发送Sap时间")
    private Date sendSapTime;

}
