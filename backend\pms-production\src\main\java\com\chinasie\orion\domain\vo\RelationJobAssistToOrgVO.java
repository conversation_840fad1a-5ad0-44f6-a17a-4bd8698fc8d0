package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.util.List;
/**
 * RelationJobAssistToOrg VO对象
 *
 * <AUTHOR>
 * @since 2024-11-20 19:05:01
 */
@ApiModel(value = "RelationJobAssistToOrgVO对象", description = "作业专业协助关系表")
@Data
public class RelationJobAssistToOrgVO extends  ObjectVO   implements Serializable{

            /**
         * 工单号
         */
        @ApiModelProperty(value = "工单号")
        private String jobNumber;


        /**
         * 大修组织ID：基本是执行班组
         */
        @ApiModelProperty(value = "大修组织ID：基本是执行班组")
        private String majorRepairOrgId;


    

}
