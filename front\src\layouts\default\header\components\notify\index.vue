<template>
  <div
    :class="prefixCls"
    @click="goMessageList"
  >
    <Badge
      :numberStyle="numberStyle"
    >
      <BellOutlined style="color: #fff" />
    </Badge>

    <!--    <Popover title="" trigger="click" :overlayClassName="`${prefixCls}__overlay`">-->
    <!--      <Badge :count="count" dot :numberStyle="numberStyle">-->
    <!--        <BellOutlined />-->
    <!--      </Badge>-->
    <!--      <template #content>-->
    <!--        <Tabs>-->
    <!--          <template v-for="item in tabListData" :key="item.key">-->
    <!--            <TabPane>-->
    <!--              <template #tab>-->
    <!--                {{ item.name }}-->
    <!--                <span v-if="item.list.length !== 0">({{ item.list.length }})</span>-->
    <!--              </template>-->
    <!--              <NoticeList :list="item.list" />-->
    <!--            </TabPane>-->
    <!--          </template>-->
    <!--        </Tabs>-->
    <!--      </template>-->
    <!--    </Popover>-->
  </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue';
import { Badge } from 'ant-design-vue';
import { BellOutlined } from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';
import { tabListData } from './data';
import { useDesign } from '/@/hooks/web/useDesign';

export default defineComponent({
  components: {
    BellOutlined,
    Badge,
  },
  setup() {
    const { prefixCls } = useDesign('header-notify');
    const router = useRouter();

    let count = 0;

    // console.log('useWebSocketStore', useWebSocket);

    for (let i = 0; i < tabListData.length; i++) {
      count += tabListData[i].list.length;
    }

    return {
      prefixCls,
      tabListData,
      count,
      numberStyle: {
        marginTop: '10px',
      },
      goMessageList() {
      },
    };
  },
});
</script>
<style lang="less">
  @prefix-cls: ~'@{namespace}-header-notify';

  .@{prefix-cls} {
    padding-top: 2px;

    &__overlay {
      max-width: 360px;
    }

    .ant-tabs-content {
      width: 300px;
    }

    .ant-badge {
      font-size: 18px;

      .ant-badge-multiple-words {
        padding: 0 4px;
      }

      svg {
        width: 0.9em;
      }
    }
  }
</style>
