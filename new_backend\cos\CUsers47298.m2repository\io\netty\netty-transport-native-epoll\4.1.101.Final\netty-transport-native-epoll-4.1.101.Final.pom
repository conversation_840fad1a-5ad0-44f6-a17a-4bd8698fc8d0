<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2014 The Netty Project
  ~
  ~ The Netty Project licenses this file to you under the Apache License,
  ~ version 2.0 (the "License"); you may not use this file except in compliance
  ~ with the License. You may obtain a copy of the License at:
  ~
  ~   https://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
  ~ WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
  ~ License for the specific language governing permissions and limitations
  ~ under the License.
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>io.netty</groupId>
    <artifactId>netty-parent</artifactId>
    <version>4.1.101.Final</version>
  </parent>
  <artifactId>netty-transport-native-epoll</artifactId>

  <name>Netty/Transport/Native/Epoll</name>
  <packaging>jar</packaging>

  <properties>
    <javaModuleNameClassifier>${os.detected.name}.${os.detected.arch}</javaModuleNameClassifier>
    <javaModuleName>io.netty.transport.epoll.${javaModuleNameClassifier}</javaModuleName>
    <!-- Needed as we use SelfSignedCertificate in our tests -->
    <argLine.java9.extras>--add-exports java.base/sun.security.x509=ALL-UNNAMED</argLine.java9.extras>
    <!-- Always check JNI during test run so we catch bugs that could cause crashes -->
    <argLine.jni>-Xcheck:jni</argLine.jni>
    <unix.common.lib.name>netty-unix-common</unix.common.lib.name>
    <unix.common.lib.dir>${project.build.directory}/unix-common-lib</unix.common.lib.dir>
    <unix.common.lib.unpacked.dir>${unix.common.lib.dir}/META-INF/native/lib</unix.common.lib.unpacked.dir>
    <unix.common.include.unpacked.dir>${unix.common.lib.dir}/META-INF/native/include</unix.common.include.unpacked.dir>
    <jni.compiler.args.cflags>CFLAGS=-O2 -pipe -Werror -fno-omit-frame-pointer -Wunused-variable -fvisibility=hidden -D_FORTIFY_SOURCE=2 -ffunction-sections -fdata-sections -I${unix.common.include.unpacked.dir}</jni.compiler.args.cflags>
    <jni.compiler.args.ldflags>LDFLAGS=-Wl,-z,relro -Wl,-z,now -Wl,--as-needed -Wl,--gc-sections -L${unix.common.lib.unpacked.dir}</jni.compiler.args.ldflags>
    <jni.compiler.args.libs>LIBS=-Wl,--whole-archive -l${unix.common.lib.name} -Wl,--no-whole-archive -ldl</jni.compiler.args.libs>
    <nativeSourceDirectory>${project.basedir}/src/main/c</nativeSourceDirectory>
    <skipTests>true</skipTests>
    <japicmp.skip>true</japicmp.skip>
  </properties>

  <profiles>
    <!--
      Netty must be released from RHEL 6.8 x86_64 or compatible so that:

        1) we ship x86_64 version of epoll transport officially, and
        2) we ensure the ABI compatibility with older GLIBC versions.

           The shared library built on a distribution with newer GLIBC
           will not run on older distributions.
    -->
    <profile>
      <id>restricted-release-epoll</id>
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <artifactId>maven-enforcer-plugin</artifactId>
              <version>3.0.0</version>
              <dependencies>
                <!-- Provides the 'requireFilesContent' enforcer rule. -->
                <dependency>
                  <groupId>com.ceilfors.maven.plugin</groupId>
                  <artifactId>enforcer-rules</artifactId>
                  <version>1.2.0</version>
                </dependency>
              </dependencies>
            </plugin>
          </plugins>
        </pluginManagement>
        <plugins>
          <plugin>
            <artifactId>maven-enforcer-plugin</artifactId>
            <executions>
              <execution>
                <id>enforce-release-environment</id>
                <goals>
                  <goal>enforce</goal>
                </goals>
                <configuration>
                  <rules>
                    <requireProperty>
                      <regexMessage>
                        Release process must be performed on linux-x86_64.
                      </regexMessage>
                      <property>os.detected.classifier</property>
                      <regex>^linux-x86_64$</regex>
                    </requireProperty>
                    <requireFilesContent>
                      <message>
                        Release process must be performed on RHEL 6.8 or its derivatives.
                      </message>
                      <files>
                        <file>/etc/redhat-release</file>
                      </files>
                      <content>release 6.9</content>
                    </requireFilesContent>
                  </rules>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>linux</id>
      <activation>
        <os>
          <family>linux</family>
        </os>
      </activation>
      <properties>
        <skipTests>false</skipTests>
      </properties>

      <build>
        <plugins>
          <plugin>
            <artifactId>maven-dependency-plugin</artifactId>
            <executions>
              <!-- unpack the unix-common static library and include files -->
              <execution>
                <id>unpack</id>
                <phase>generate-sources</phase>
                <goals>
                  <goal>unpack-dependencies</goal>
                </goals>
                <configuration>
                  <includeGroupIds>${project.groupId}</includeGroupIds>
                  <includeArtifactIds>netty-transport-native-unix-common</includeArtifactIds>
                  <classifier>${jni.classifier}</classifier>
                  <outputDirectory>${unix.common.lib.dir}</outputDirectory>
                  <includes>META-INF/native/**</includes>
                  <overWriteReleases>false</overWriteReleases>
                  <overWriteSnapshots>true</overWriteSnapshots>
                </configuration>
              </execution>
            </executions>
          </plugin>

          <plugin>
            <groupId>org.fusesource.hawtjni</groupId>
            <artifactId>hawtjni-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>build-native-lib</id>
                <configuration>
                  <name>netty_transport_native_epoll_${os.detected.arch}</name>
                  <nativeSourceDirectory>${nativeSourceDirectory}</nativeSourceDirectory>
                  <libDirectory>${project.build.outputDirectory}</libDirectory>
                  <!-- We use Maven's artifact classifier instead.
                       This hack will make the hawtjni plugin to put the native library
                       under 'META-INF/native' rather than 'META-INF/native/${platform}'. -->
                  <platform>.</platform>
                  <configureArgs>
                    <arg>${jni.compiler.args.ldflags}</arg>
                    <arg>${jni.compiler.args.libs}</arg>
                    <arg>${jni.compiler.args.cflags}</arg>
                    <configureArg>--libdir=${project.build.directory}/native-build/target/lib</configureArg>
                  </configureArgs>
                </configuration>
                <goals>
                  <goal>generate</goal>
                  <goal>build</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-jar-plugin</artifactId>
            <executions>
              <!-- Generate the JAR that contains the native library in it. -->
              <execution>
                <id>native-jar</id>
                <goals>
                  <goal>jar</goal>
                </goals>
                <configuration>
                  <archive>
                    <manifest>
                      <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                    </manifest>
                    <manifestEntries>
                      <Bundle-NativeCode>META-INF/native/libnetty_transport_native_epoll_${os.detected.arch}.so; osname=Linux; processor=${os.detected.arch},*</Bundle-NativeCode>
                      <Fragment-Host>io.netty.transport-classes-epoll</Fragment-Host>
                      <Automatic-Module-Name>${javaModuleName}</Automatic-Module-Name>
                    </manifestEntries>
                    <index>true</index>
                    <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                  </archive>
                  <classifier>${jni.classifier}</classifier>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
  
      <dependencies>
        <dependency>
          <groupId>io.netty</groupId>
          <artifactId>netty-transport-native-unix-common</artifactId>
          <version>${project.version}</version>
          <classifier>${jni.classifier}</classifier>
          <!--
            The unix-common with classifier dependency is optional because it is not a runtime dependency, but a build time
            dependency to get the static library which is built directly into the shared library generated by this project.
          -->
          <optional>true</optional>
        </dependency>
      </dependencies>
    </profile>
    <profile>
      <id>linux-aarch64</id>
      <properties>
        <!-- use aarch_64 as this is also what os.detected.arch will use on an aarch64 system -->
        <jni.classifier>${os.detected.name}-aarch_64</jni.classifier>
        <javaModuleNameClassifier>${os.detected.name}.aarch_64</javaModuleNameClassifier>
      </properties>
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <artifactId>maven-enforcer-plugin</artifactId>
              <version>3.0.0</version>
              <dependencies>
                <!-- Provides the 'requireFilesContent' enforcer rule. -->
                <dependency>
                  <groupId>com.ceilfors.maven.plugin</groupId>
                  <artifactId>enforcer-rules</artifactId>
                  <version>1.2.0</version>
                </dependency>
              </dependencies>
            </plugin>
          </plugins>
        </pluginManagement>
        <plugins>
          <plugin>
            <artifactId>maven-enforcer-plugin</artifactId>
            <executions>
              <execution>
                <id>enforce-release-environment</id>
                <goals>
                  <goal>enforce</goal>
                </goals>
                <configuration>
                  <rules>
                    <requireProperty>
                      <regexMessage>
                        Cross compile and Release process must be performed on linux-x86_64.
                      </regexMessage>
                      <property>os.detected.classifier</property>
                      <regex>^linux-x86_64.*</regex>
                    </requireProperty>
                    <requireFilesContent>
                      <message>
                        Cross compile and Release process must be performed on RHEL 7.6 or its derivatives.
                      </message>
                      <files>
                        <file>/etc/redhat-release</file>
                      </files>
                      <content>release 7.6</content>
                    </requireFilesContent>
                  </rules>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-dependency-plugin</artifactId>
            <executions>
              <!-- unpack the unix-common static library and include files -->
              <execution>
                <id>unpack</id>
                <phase>generate-sources</phase>
                <goals>
                  <goal>unpack-dependencies</goal>
                </goals>
                <configuration>
                  <includeGroupIds>${project.groupId}</includeGroupIds>
                  <includeArtifactIds>netty-transport-native-unix-common</includeArtifactIds>
                  <classifier>${jni.classifier}</classifier>
                  <outputDirectory>${unix.common.lib.dir}</outputDirectory>
                  <includes>META-INF/native/**</includes>
                  <overWriteReleases>false</overWriteReleases>
                  <overWriteSnapshots>true</overWriteSnapshots>
                </configuration>
              </execution>
            </executions>
          </plugin>

          <plugin>
            <groupId>org.fusesource.hawtjni</groupId>
            <artifactId>hawtjni-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>build-native-lib</id>
                <configuration>
                  <name>netty_transport_native_epoll_aarch_64</name>
                  <nativeSourceDirectory>${nativeSourceDirectory}</nativeSourceDirectory>
                  <libDirectory>${project.build.outputDirectory}</libDirectory>
                  <!-- We use Maven's artifact classifier instead.
                       This hack will make the hawtjni plugin to put the native library
                       under 'META-INF/native' rather than 'META-INF/native/${platform}'. -->
                  <platform>.</platform>
                  <configureArgs>
                    <arg>${jni.compiler.args.ldflags}</arg>
                    <arg>${jni.compiler.args.libs}</arg>
                    <arg>${jni.compiler.args.cflags}</arg>
                    <configureArg>--libdir=${project.build.directory}/native-build/target/lib</configureArg>
                    <configureArg>--host=aarch64-linux-gnu</configureArg>
                    <configureArg>CC=aarch64-none-linux-gnu-gcc</configureArg>
                  </configureArgs>
                </configuration>
                <goals>
                  <goal>generate</goal>
                  <goal>build</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-jar-plugin</artifactId>
            <executions>
              <!-- Generate the JAR that contains the native library in it. -->
              <execution>
                <id>native-jar</id>
                <goals>
                  <goal>jar</goal>
                </goals>
                <configuration>
                  <archive>
                    <manifest>
                      <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                    </manifest>
                    <manifestEntries>
                      <Bundle-NativeCode>META-INF/native/libnetty_transport_native_epoll_aarch_64.so; osname=Linux; processor=aarch64,*</Bundle-NativeCode>
                      <Fragment-Host>io.netty.transport-classes-epoll</Fragment-Host>
                      <Automatic-Module-Name>${javaModuleName}</Automatic-Module-Name>
                    </manifestEntries>
                    <index>true</index>
                    <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                  </archive>
                  <classifier>${jni.classifier}</classifier>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>

      <dependencies>
        <dependency>
          <groupId>io.netty</groupId>
          <artifactId>netty-transport-native-unix-common</artifactId>
          <version>${project.version}</version>
          <classifier>${jni.classifier}</classifier>
          <!--
            The unix-common with classifier dependency is optional because it is not a runtime dependency, but a build time
            dependency to get the static library which is built directly into the shared library generated by this project.
          -->
          <optional>true</optional>
        </dependency>
      </dependencies>
    </profile>
  </profiles>

  <dependencies>
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-common</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-buffer</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-transport</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-transport-native-unix-common</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-transport-classes-epoll</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-testsuite</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-transport-native-unix-common-tests</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>${tcnative.artifactId}</artifactId>
      <classifier>${tcnative.classifier}</classifier>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>io.github.artsok</groupId>
      <artifactId>rerunner-jupiter</artifactId>
      <scope>test</scope>
    </dependency>
    <!-- For SelfSignedCertificate usage on JDK20+ -->
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcpkix-jdk15on</artifactId>
      <optional>true</optional>
    </dependency>
  </dependencies>


  <build>
    <plugins>
      <!-- Also include c files in source jar -->
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>build-helper-maven-plugin</artifactId>
        <executions>
          <execution>
            <phase>generate-sources</phase>
            <goals>
              <goal>add-source</goal>
            </goals>
            <configuration>
              <sources>
                <source>${nativeSourceDirectory}</source>
              </sources>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <!-- Generate the fallback JAR that does not contain the native library. -->
          <execution>
            <id>default-jar</id>
            <configuration>
              <excludes>
                <exclude>META-INF/native/**</exclude>
              </excludes>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>

