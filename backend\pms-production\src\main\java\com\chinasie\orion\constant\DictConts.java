package com.chinasie.orion.constant;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/06/16:25
 * @description:
 */
public class DictConts {

    /**
     *  物资类型
     */
    public static  final String SUPPLIES_TYPE="pms_supplies_type";

    public static final  String TRAIN_TYPE="pms_train_type";
    /**
     *  要求类型
     */
    public static final  String REQUIREMENT_TYPE="pms_basic_requirement";
    /**
     *  培训类型
     */
    public static final String  TRAIN_TYPE_DICT="pms_train_type";

    public static final String  TRAIN_TYPE_INFO_DICT="pms_train_dict";


    /**
     * 金字塔类别
     */
    public static final String  PMS_PYRAMID_CATEGORY="pms_pyramid_category";
    /**
     * 考核级别
     */
    public static final String  PMS_ASSESSMENT_LEVEL="pms_assessment_Level";

    /** ************************************************************* 大修************************************ **/

    /**
     *  大修类型
     */
    public static final String MAJOR_REAPAIR_TYPE="pms_major_repair_type";


    /**
     *  首次执行
     */
    public static final String FIRST_EXECUTE="pms_first_execute";
    /**
     *  重要项目
     */
    public static final String IMPORTANT_PROJECT="pms_important_project";


    /**
     *  防疫级别
     */
    public static final String DUST_PROTECTION_LEVEL="pms_dust_protection_level";

    /**
     *  高风险
     */
    public static final String HEIGHT_LEVEL="pms_height_level";


    public static final String STATUS_CODE ="txf7180290fffgg34644992";


    /**
     *  优化领域
     */

    public static final String AREA_OF_OPT="pms_area_of_optimization";


    /**
     * 应用机组类型
     */
    public static final String APPLICATION_UNIT_TYPE ="pmx_application_unit_type";

    /**
     *  计量领域
     */
    public static final String METROLOGY_FIELD="pms_metrology_field";

    public static final String JOB_TYPE="pms_job_type";

    public static final String TECHNICAL_APPLICATION="pms_technical_application";


    public static final String STUDY_EXAMINE ="pms_study_examine";
    public static final String PMS_CERTIFICATE_TYPE ="pms_certificate_type" ;


    //

    public static  final  String PMS_SPECIALI_OP ="pms_speciali_op";

    public static  final String CERTIFICATE_LEVEL="pms_certificate_level";

    // 成本业务分类
    public static final String COS_BUSINESS_TYPE = "cos_business_type";

    /**
     *  离厂原因
     */
    public static  final  String PMS_OUT_FACTORY_REASON ="pms_out_factory_reason";

    /**
     * 物资离场原因
     */
    public static final String PMS_OUT_REASON = "pms_out_reason";

    public static  final String PMS_PREP_LIKE_TYPE ="pms_prep_like_type";

    /**
     * 基地管理
     */
    public static final String BASE_MANAGE = "base_manage";

    /**
     * 基地中心管理
     */
    public static final String BASE_CENTER_MANAGE = "base_center_manage";

    /**
     * 中心管理
     */
    public static final String CENTER_MANAGE = "center_manage";

    public static final String DIMENSION_DICT="pms_action_dimension";
}
