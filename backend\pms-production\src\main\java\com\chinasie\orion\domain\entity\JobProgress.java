package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;

/**
 * JobProgress Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-09 15:48:03
 */
@TableName(value = "pmsx_job_progress")
@ApiModel(value = "JobProgressEntity对象", description = "作业工作进展")
@Data

public class JobProgress extends  ObjectEntity  implements Serializable{

    /**
     * 作业id
     */
    @ApiModelProperty(value = "作业id")
    @TableField(value = "job_id")
    private String jobId;

    /**
     * 工作日期
     */
    @ApiModelProperty(value = "工作日期")
    @TableField(value = "work_date")
    private Date workDate;

    /**
     * 总体进展
     */
    @ApiModelProperty(value = "总体进展")
    @TableField(value = "progress_schedule")
    private BigDecimal progressSchedule;

    /**
     * 工作进展
     */
    @ApiModelProperty(value = "工作进展")
    @TableField(value = "progress_detail")
    private String progressDetail;

}
