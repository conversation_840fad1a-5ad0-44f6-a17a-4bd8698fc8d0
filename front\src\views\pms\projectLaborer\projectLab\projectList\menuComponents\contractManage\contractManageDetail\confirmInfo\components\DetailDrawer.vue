<template>
  <BasicDrawer
    :width="1000"
    v-bind="$attrs"
    title="审核详情"
    :showFooter="true"
    @register="register"
    @visible-change="visibleChange"
  >
    <template #footer>
      <div
        v-if="state.visibleStatus"
        class="flex flex-pe"
      >
        <BasicButton @click="()=>closeDrawer()">
          关闭
        </BasicButton>
        <BasicButton
          v-if="state.record?.status===104 && state.record?.operationType!=='history'&&useUserStore().getUserInfo?.id===state?.record?.['creatorId'] && isPower('HT_container_button_04',state.record?.rdAuthList??[])"
          type="primary"
          @click="okHandle"
        >
          重新提交
        </BasicButton>
      </div>
    </template>
    <SpinMain :loading="state.loading" />
    <DetailDrawerMain
      v-if="state.visibleStatus"
      :detail="state.detail"
    />
  </BasicDrawer>
</template>

<script setup lang="ts">
import {
  BasicDrawer, useDrawerInner, BasicButton, isPower,
} from 'lyra-component-vue3';
import { inject, reactive } from 'vue';
import DetailDrawerMain from './DetailDrawerMain.vue';
// import { getOrderNodeDetail } from '/@/views/pms/api';
import { childActionKey } from '../type';
import SpinMain from '/@/views/pms/projectLaborer/components/SpanMain/SpinMain.vue';
import { useUserStore } from '/@/store/modules/user';
import { getContractPayNodeConfirm } from '/@/views/pms/projectLaborer/projectLab/api';
import Api from '/@/api';

const state = reactive({
  visibleStatus: false,
  record: {
    operationType: '',
    status: undefined,
    rdAuthList: [],
  },
  detail: {},
  loading: false,
  childAction: inject(childActionKey),
});

const [register, { closeDrawer }] = useDrawerInner(async (record) => {
  state.record = record;
  if (state.record?.operationType === 'history') {
    await getHistoryDetail(record?.id);
  } else {
    await getDetail(record?.id);
  }
  state.visibleStatus = true;
});

function visibleChange(visible: boolean) {
  !visible && (state.visibleStatus = visible);
}

// 获取订单确认详情
async function getDetail(id) {
  if (!id) return;
  state.loading = true;
  try {
    state.detail = await getContractPayNodeConfirm(id);
  } finally {
    state.loading = false;
  }
}

async function getHistoryDetail(id) {
  if (!id) return;
  state.loading = true;
  try {
    state.detail = await new Api(`/pas/contractPayNodeConfirmAuditRecord/${id}`).fetch('', '', 'GET').finally(() => {
    });
  } finally {
    state.loading = false;
  }
}

function okHandle() {
  state.childAction('againCommit', state.record);
  closeDrawer();
}
</script>

<style scoped>

</style>
