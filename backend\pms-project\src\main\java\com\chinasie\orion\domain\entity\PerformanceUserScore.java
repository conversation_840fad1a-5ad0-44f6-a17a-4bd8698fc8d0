//package com.chinasie.orion.domain.entity;
//
//import com.baomidou.mybatisplus.annotation.TableName;
//
//import io.swagger.annotations.ApiModel;
//import lombok.Data;
//
//import java.io.Serializable;
///**
// * PerformanceIndicator Entity对象
// *
// * <AUTHOR>
// * @since 2024-03-25 16:41:31
// */
//@TableName(value = "pmsx_performance_user_score")
//@ApiModel(value = "PerformanceUserScore", description = "用户评分记录表")
//@Data
//public class PerformanceUserScore extends ObjectEntity implements Serializable {
//
//
//
//}
