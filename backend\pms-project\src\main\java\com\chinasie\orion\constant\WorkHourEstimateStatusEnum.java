package com.chinasie.orion.constant;

/**
 * @author: yk
 * @date: 2023/10/25 20:40
 * @description:
 */
public enum WorkHourEstimateStatusEnum {
    CREATED(101, "已创建"),
    AUDITED(130, "已生效"),
    ;


    private Integer status;

    private String desc;


    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    WorkHourEstimateStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
