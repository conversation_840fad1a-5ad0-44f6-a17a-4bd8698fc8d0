package com.chinasie.orion.feign;

import com.chinasie.orion.feign.dto.DownloadFilesParamDTO;
import com.chinasie.orion.sdk.core.conf.FeignConfig;
import feign.Logger;
import feign.Response;
import feign.codec.Decoder;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.cloud.openfeign.support.ResponseEntityDecoder;
import org.springframework.cloud.openfeign.support.SpringDecoder;
import org.springframework.context.annotation.Bean;
import org.springframework.http.MediaType;
import org.springframework.http.converter.ByteArrayHttpMessageConverter;
import org.springframework.http.converter.ResourceHttpMessageConverter;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
// , FeignStreamConfig.class
@FeignClient(name = "res",configuration = {FeignConfig.class,MyFeignConfig.class})
public interface FileResFeignService {

//
    @PostMapping(value = "/file/download/files/zip",produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    @ApiOperation(value = "下载多文件为zip--数据为字节数据")
    byte[] download(@RequestBody DownloadFilesParamDTO paramDTO) throws Exception;


//
//    @GetMapping(value = "/file/download/server/zip")
//    @ApiOperation(value = "下载多文件为zip--服务")
//    Response downloadServerZip(@SpringQueryMap DownloadFilesParamDTO paramDTO) throws Exception;


//    @GetMapping(value = "/download/server/files/zip")
//    @ApiOperation(value = "下载多附件为zip附件")
//    @LogRecord(success = "【{USER{#logUserId}}】下载多文件为zip--数据流数据", type = "基础服务-文件上传", subType = "下载多文件为zip--数据为字节数据", bizNo = "")
//    ResponseDTO<InputStream> downloadServerZip(@SpringQueryMap DownloadFilesParamDTO paramDTO) throws Exception;

//    @GetMapping(value = "/download/server/files/zip")
//    @ApiOperation(value = "下载多附件为zip附件")
//    ResponseEntity<Resource> downloadServerZip(@SpringQueryMap DownloadFilesParamDTO paramDTO) throws Exception;


}

class MyFeignConfig {
    @Bean
    public Logger.Level feignLoggerLevel() {
        return Logger.Level.FULL;
    }

    @Bean
    public Decoder feignDecoder() {
        return new ResponseEntityDecoder(new SpringDecoder(() -> new HttpMessageConverters(
                new ByteArrayHttpMessageConverter(),
                new ResourceHttpMessageConverter()
        )));
    }
}