package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.ProductionIndexDTO;
import com.chinasie.orion.domain.entity.ProductionIndex;
import com.chinasie.orion.domain.vo.PersonNumVO;
import com.chinasie.orion.domain.vo.ProductionIndexVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * ProductionIndex 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-18 17:49:18
 */
public interface ProductionIndexService extends OrionBaseService<ProductionIndex> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    ProductionIndexVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param productionIndexDTO
     */
    String create(ProductionIndexDTO productionIndexDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param productionIndexDTO
     */
    Boolean edit(ProductionIndexDTO productionIndexDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ProductionIndexVO> pages(Page<ProductionIndexDTO> pageRequest) throws Exception;

    /**
     * 查询所有数据
     *
     * @return
     * @throws Exception
     */
    List<ProductionIndexVO> getList();
    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<ProductionIndexVO> vos) throws Exception;
}
