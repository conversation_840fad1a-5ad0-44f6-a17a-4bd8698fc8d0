package com.chinasie.orion.domain.dto;


import lombok.Data;

import java.util.Date;

@Data
public class ComputeIntervalDayDTO {
    private String businessId;
    private Date endDate;
    private Date startDate;

    public ComputeIntervalDayDTO() {
    }

    public ComputeIntervalDayDTO(String businessId, Date startDate, Date endDate) {
        this.businessId = businessId;
        this.endDate = endDate;
        this.startDate = startDate;
    }
}
