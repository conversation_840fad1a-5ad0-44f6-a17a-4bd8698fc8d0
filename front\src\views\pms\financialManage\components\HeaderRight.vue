<template>
  <div class="customColors">
    <BasicButton
      v-if="isPower('PMS_SRJHTBXQ_container_03_button_01', powerData)"
      type="primary"
      icon="fa-edit"
      @click="handleOperation('fillPlan')"
    >
      月度计划填报
    </BasicButton>
    <BasicButton
      v-if="isPower('PMS_SRJHTBXQ_container_03_button_02', powerData)"
      type="primary"
      icon="fa-eye"
      @click="handleOperation('fillView')"
    >
      季度数据查看
    </BasicButton>
    <BasicButton
      v-if="isPower('PMS_SRJHTBXQ_container_03_button_03', powerData)"
      type="primary"
      icon="fa-share-square-o"
      @click="handleOperation('tracking')"
    >
      月度计划跟踪
    </BasicButton>
    <BasicButton
      v-if="isPower('PMS_SRJHTBXQ_container_03_button_04', powerData)"
      type="primary"
      icon="fa-eraser"
      @click="handleOperation('reconciliation')"
    >
      发起对账
    </BasicButton>
    <BasicButton
      v-if="isPower('PMS_SRJHTBXQ_container_03_button_05', powerData)"
      type="primary"
      icon="sie-icon-xiangmuyanshou"
      @click="handleOperation('invoice')"
    >
      发起开票
    </BasicButton>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';
import {
  BasicButton,
} from 'lyra-component-vue3';

export default defineComponent({
  components: {
    BasicButton,
  },
  props: {
    powerData: {
      type: Object as PropType<any>,
      required: true,
    },
  },
  emits: ['operation'],
  methods: {
    isPower(permission: string, powerData: any): boolean {
      return powerData;
    },
    handleOperation(operation: string) {
      this.$emit('operation', operation);
    },
  },
});
</script>

<style scoped>
.customColors {
  :deep(.ant-btn) {
    background-color: #ceedfc;
    color: #1890FF;
    border-color: #ceedfc;
  }
}
</style>
