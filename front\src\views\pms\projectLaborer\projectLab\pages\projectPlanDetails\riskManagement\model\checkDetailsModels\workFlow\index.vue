<template>
  <layout
    :options="{ body: { scroll: true } }"
  >
    <!--    <BpmnMain-->
    <!--      ref="flowModel"-->
    <!--      :userId="userId"-->
    <!--      :menu-instance-list-api="menuInstanceListApi"-->
    <!--      :template-list-api="templateListApi"-->
    <!--      :allTaskPageApi="allTaskPageApi"-->
    <!--      :addEditSavaApi="addEditSavaApi"-->
    <!--      :journalApi="journalApi"-->
    <!--      :taskBtnApi="taskBtnApi"-->
    <!--      :approvalListApi="approvalListApi"-->
    <!--      :nodeTableDataApi="nodeTableDataApi"-->
    <!--      :agreeApi="agreeApi"-->
    <!--      :type="2"-->
    <!--      @success="successChange"-->
    <!--      @openClick="openClick"-->
    <!--    />-->
    <WorkflowView
      ref="processViewRef"
      :workflow-props="workflowProps"
    />
  </layout>
</template>
<script lang="ts">
import {
  ComputedRef, Ref, defineComponent, onMounted, reactive, toRefs, computed, ref, inject, watch,
} from 'vue';
import { WorkflowView, WorkflowProps } from 'lyra-workflow-component-vue3';
import Api from '/@/api';
import {
  useActionsRecord, Layout, OrionTable, BasicTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer, BpmnMain,
} from 'lyra-component-vue3';
// import Layout from '/@/components/Layout';
// import { BpmnMain } from '/@/views/pms/projectLaborer/components/BpmnModules';
import { questionDetailsPageApi } from '/@/views/pms/projectLaborer/api/questionManage';
import { stampDate } from '/@/views/pms/projectLaborer/utils/dateUtil';
import { useUserStore } from '/@/store/modules/user';
export default defineComponent({
  components: {
    Layout,
    // BpmnMain,
    WorkflowView,
  },

  props: {
    id: {
      type: String,
      default: '',
    },
    details: {},
  },
  setup(props) {
    const userStore = useUserStore();
    const contentHeight = ref();
    const dataType = ref('');
    const procInstName = ref('');
    const projectId = ref('');
    const formData:any = inject('projectInfo', {});
    const processViewRef:Ref = ref();
    const state = reactive({
      templateList: [],
      flowModel: null,
      userId: userStore.getUserInfo.id,
      projectInfo: {} as any, // 传递给子组件的数据
    });
    const workflowProps = computed(() => ({
      Api,
      businessData: state.projectInfo,
      afterEvent: (type, props) => {
        processViewRef.value?.init();
      },
    }));
    onMounted(async () => {
      contentHeight.value = document.body.clientHeight - 220;

      // questionDetailsPageApi(props.id)
      await new Api(`/pas/risk-management/detail/${props.details.id}`).fetch('', '', 'GET')
        .then((res) => {
          if (res) {
            dataType.value = res.className;
            procInstName.value = res.name;
            projectId.value = res.projectId;

            state.projectInfo.className = res.className;
            state.projectInfo.name = res.name;
            state.projectInfo.id = res.projectId;
          }
        })
        .catch(() => {});
    });
    function menuInstanceListApi(data) {
      let params = {
        pageNum: 0,
        pageSize: 1000,
        query: {
          deliveries: [
            {
              deliveryId: formData?.value?.id,
            },
          ],
          // forUserId: 'string',
          // tenantId: 'string',
          userId: userStore.getUserInfo.id,
        },
      };
      return new Api('/workflow').fetch(params, 'process-instance/by_delivery/page', 'POST').then((res) => res);
    }
    function templateListApi() {
      return new Api('/res/data-type/list')
        .fetch(
          {
            dataType: formData?.value?.className,
          },
          '',
          'POST',
        )
        .then(async (data) => {
          if (data && data.length) {
            state.templateList = await new Api('/res/data-type-group/template/list')
              .fetch(
                {
                  dataTypeId: 'zt1ld947099ac3914e8b8336819c0cdb3ba8',
                  groupId: 'bzdj3b0a0a6e70704413b371d1bb738aa1f3',
                  templateType: 'FLOW-TEMPLATE',
                },
                '',
                'POST',
              )
              .then((data) => (
                (data
                          && data.map((item) => ({
                            ...item,
                            label: item.name,
                            key: item.procDefId,
                            value: item.procDefId,
                          })))
                      || []
              ));
            return state.templateList;
          }
          return [];
        });
      // return new Api('/res/data-type-group/template/list')
      //     .fetch(
      //         {
      //           dataTypeId: 'zt1ld947099ac3914e8b8336819c0cdb3ba8',
      //           groupId: 'bzdj3b0a0a6e70704413b371d1bb738aa1f3',
      //           templateType: 'FLOW-TEMPLATE',
      //         },
      //         '',
      //         'POST',
      //     )
      //     .then((data) => {
      //       state.templateList = data
      //       if (data?.length>0){
      //         data.map((item)=>{
      //           console.log("----- 999 -----", 999)
      //           return{
      //             ...item,
      //             label: item.name,
      //             value: item.procDefId,
      //             key: item.procDefId,
      //             // id: item.procDefId,
      //           }
      //         })
      //       }
      //       console.log("----- state.templateList -----", state.templateList)
      //     });
      // label: item.name,
      //     key: item.procDefId,
      //     value: item.procDefId,
      // return new Api(`/pms/doc-type-to-process/list/`).fetch('', '', 'GET').then((res) => {
      //   state.templateList = res;
      //   return res.map((item) => ({
      //     seleteNode: item,
      //     label: item.name,
      //     value: item.id,
      //     key: item.id,
      //     id: item.id,
      //   }));
      // });
    }
    function allTaskPageApi(data) {
      // return basicConfig.getAllTaskPage(data).then((res) => res);
      let url = `process-instance/task-definition/page?processDefinitionId=${data.procDefId}&userId=${userStore.getUserInfo.id}`;
      return new Api('/workflow').fetch('', url, 'POST');
    }
    function addEditSavaApi(data) {
      let templateItem = state.templateList.find((item) => item.procDefId === data.flowInfoId);
      data.deliveries.forEach((item) => {
        item.deliveryId = formData?.value?.id;
      });
      // let params = {
      //   // bizCatalogId: 'string',
      //   // bizCatalogName: 'string',
      //   bizId: data.bizId,
      //   // bizTypeName: 'string',
      //   // businessKey: 'string',
      //   deliveries: data.deliveries,
      //   flowInfoId: data.flowInfoId,
      //   // flowKey: 'string',
      //   // href: 'string',
      //   ownerId: userStore.getUserInfo.id,
      //   prearranges: data.prearranges,
      //   procDefId: templateItem.procDefId,
      //   procDefName: data.procDefName,
      //   procInstName: `${formData?.value?.name}实例${stampDate(Date.parse(new Date()), 'yyyy-MM-dd HH:mm:ss')}`,
      // };
      data.userId = userStore.getUserInfo.id;
      // data.bizId = data.bizId;
      data.bizId;
      data.ownerId = userStore.getUserInfo.id;
      data.procInstName = `${formData?.value?.name}实例 ${stampDate(Date.parse(Date()), 'yyyy-MM-dd HH:mm:ss')}`;
      return new Api('/workflow').fetch(data, 'process-instance', 'POST').then((res) => res);
    }
    function journalApi(id) {
      let params = {
        procInstId: id,
        userId: userStore.getUserInfo.id,
      };
      return new Api('/workflow').fetch(params, 'act-inst-detail/journal', 'GET').then((res) => res);
    }
    // 获取流程按钮
    function taskBtnApi(data) {
      if (!data.currentTasks) return;
      let currentTasksItem = data.currentTasks.find((item) => item.assignee === userStore.getUserInfo.id);
      let params = {
        procDefId: data.procDefId,
        userId: userStore.getUserInfo.id,
        taskId: currentTasksItem.id,
      };
      return new Api('/workflow').fetch(params, 'process-instance/task-action', 'GET').then((res) => res);
    }
    function approvalListApi(id) {
      return new Promise((resolve, reject) => {
        resolve([formData?.value]);
      });
    }
    function nodeTableDataApi(id) {
      let params = {
        procInstId: id,
        userId: userStore.getUserInfo.id,
      };
      return new Api('/workflow').fetch(params, 'act-inst-detail/journal', 'GET').then((res) => res);
    }
    // const successChange = (type) => {
    //   getFormData.value(formData?.value?.id);
    // };
    const reloadInfo = inject('getForm');
    const Details = inject('getDetails');
    async function successChange() {
      await reloadInfo();
      formData.value = Details();
      await state.flowModel.approvalObjectVm.tableRef.reload();
    }
    return {
      ...toRefs(state),
      contentHeight,
      dataType,
      procInstName,
      projectId,
      openClick(record) {
        // console.log('选择的审批物', record);
      },
      checkClick(record) {
        // console.log('要查看的审批物', record);
      },
      workflowProps,
      menuInstanceListApi,
      templateListApi,
      allTaskPageApi,
      addEditSavaApi,
      journalApi,
      taskBtnApi,
      approvalListApi,
      nodeTableDataApi,
      agreeApi(params) {
        return new Api('/workflow')
          .fetch(params, 'act-prearranged/prearranged/set-assignee', 'GET');
      },
      successChange,
    };
  },
});
</script>
<style lang="less" scoped>
  @import url('/@/views/pms/projectLaborer/statics/style/page.less');
  @import url('/@/views/pms/projectLaborer/statics/style/margin.less');
</style>
