package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.EcrExecuteDTO;
import com.chinasie.orion.sdk.core.conf.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lsy
 * @date: 2024/6/12
 * @description:
 */
@FeignClient(name = "pms", configuration = FeignConfig.class)
@Lazy
public interface ProjectEcrApiService {
    String API_PREFIX = "/api-pms/projectEcr";

    /**
     * 项目状态变更执行
     * @param ecrExecuteDTO
     * @return
     */
    @PostMapping(value = API_PREFIX + "/projectStatus/executing")
    Boolean projectStatusEcrExecuting(@RequestBody EcrExecuteDTO ecrExecuteDTO) throws Exception;
}
