package com.chinasie.orion.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.domain.dto.IncomeProvisionInformationDTO;
import com.chinasie.orion.domain.entity.IncomeProvisionInformation;
import com.chinasie.orion.domain.vo.IncomeProvisionInformationVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.IncomeProvisionInformationMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.IncomeProvisionInformationService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <p>
 * IncomeProvisionInformation 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29 11:32:18
 */
@Service
@Slf4j
public class IncomeProvisionInformationServiceImpl extends  OrionBaseServiceImpl<IncomeProvisionInformationMapper, IncomeProvisionInformation>   implements IncomeProvisionInformationService {

    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public IncomeProvisionInformationVO detail(String id, String pageCode) throws Exception {
        IncomeProvisionInformation incomeProvisionInformation =this.getById(id);
        IncomeProvisionInformationVO result = BeanCopyUtils.convertTo(incomeProvisionInformation,IncomeProvisionInformationVO::new);
        setEveryName(Collections.singletonList(result));
        return result;
    }

    /**
     *  新增
     *
     * * @param incomeProvisionInformationDTO
     */
    @Override
    public  String create(IncomeProvisionInformationDTO incomeProvisionInformationDTO) throws Exception {
        IncomeProvisionInformation incomeProvisionInformation =BeanCopyUtils.convertTo(incomeProvisionInformationDTO,IncomeProvisionInformation::new);
        this.save(incomeProvisionInformation);

        String rsp=incomeProvisionInformation.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param incomeProvisionInformationDTO
     */
    @Override
    public Boolean edit(IncomeProvisionInformationDTO incomeProvisionInformationDTO) throws Exception {
        IncomeProvisionInformation incomeProvisionInformation =BeanCopyUtils.convertTo(incomeProvisionInformationDTO,IncomeProvisionInformation::new);

        this.updateById(incomeProvisionInformation);

        String rsp=incomeProvisionInformation.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<IncomeProvisionInformationVO> pages( Page<IncomeProvisionInformationDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<IncomeProvisionInformation> condition = new LambdaQueryWrapperX<>( IncomeProvisionInformation. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(IncomeProvisionInformation::getCreateTime);


        Page<IncomeProvisionInformation> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), IncomeProvisionInformation::new));

        PageResult<IncomeProvisionInformation> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<IncomeProvisionInformationVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<IncomeProvisionInformationVO> vos = BeanCopyUtils.convertListTo(page.getContent(), IncomeProvisionInformationVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "收入计提信息表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", IncomeProvisionInformationDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            IncomeProvisionInformationExcelListener excelReadListener = new IncomeProvisionInformationExcelListener();
        EasyExcel.read(inputStream,IncomeProvisionInformationDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<IncomeProvisionInformationDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("收入计提信息表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<IncomeProvisionInformation> incomeProvisionInformationes =BeanCopyUtils.convertListTo(dtoS,IncomeProvisionInformation::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::IncomeProvisionInformation-import::id", importId, incomeProvisionInformationes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<IncomeProvisionInformation> incomeProvisionInformationes = (List<IncomeProvisionInformation>) orionJ2CacheService.get("pmsx::IncomeProvisionInformation-import::id", importId);
        log.info("收入计提信息表导入的入库数据={}", JSONUtil.toJsonStr(incomeProvisionInformationes));

        this.saveBatch(incomeProvisionInformationes);
        orionJ2CacheService.delete("pmsx::IncomeProvisionInformation-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::IncomeProvisionInformation-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<IncomeProvisionInformation> condition = new LambdaQueryWrapperX<>( IncomeProvisionInformation. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(IncomeProvisionInformation::getCreateTime);
        List<IncomeProvisionInformation> incomeProvisionInformationes =   this.list(condition);

        List<IncomeProvisionInformationDTO> dtos = BeanCopyUtils.convertListTo(incomeProvisionInformationes, IncomeProvisionInformationDTO::new);

        String fileName = "收入计提信息表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", IncomeProvisionInformationDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<IncomeProvisionInformationVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class IncomeProvisionInformationExcelListener extends AnalysisEventListener<IncomeProvisionInformationDTO> {

        private final List<IncomeProvisionInformationDTO> data = new ArrayList<>();

        @Override
        public void invoke(IncomeProvisionInformationDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<IncomeProvisionInformationDTO> getData() {
            return data;
        }
    }


}
