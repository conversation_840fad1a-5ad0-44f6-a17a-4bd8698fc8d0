//package com.chinasie.orion.domain.dto;
//
//import com.chinasie.orion.amqp.dict.TodoTypeDict;
//import com.chinasie.orion.domain.entity.ProjectSchemeApplyApproval;
//import com.chinasie.orion.sdk.helper.CurrentUserHelper;
//import lombok.Data;
//
//import java.io.Serializable;
//import java.util.Date;
//
///**
// * approveMessageDTO
// *
// * @author: yangFy
// * @date: 2023/5/5 17:06
// * @description:
// * <p>
// * 审批消息抄送DTO
// * </p>
// */
//@Data
//public class ApproveMessageDTO implements Serializable {
//
//    private String projectId;
//    /**
//     * 消息节点
//     */
//    private String businessNodeCode;
//
//    /**
//     * 项目名称
//     */
//    private String projectName;
//
//    /**
//     * 业务Id
//     */
//    private String businessId;
//
//    /**
//     * 计划名
//     */
//    private String schemeName;
//
//    /**
//     * 发送者
//     */
//    private String senderId;
//
//    /**
//     * 被通知人
//     */
//    private String beInformedId;
//
//    /**
//     * 平台Id
//     */
//    private String platformId;
//
//    /**
//     * 组织Id
//     */
//    private String orgId;
//
//    /**
//     * 待办/消息：0：消息，1：待办
//     */
//    private Integer todoType;
//
//    /**
//     * fa's
//     */
//    private Date senderTime;
//
//    private String url;
//
//    public ApproveMessageDTO() {
//
//    }
//
//
//    public ApproveMessageDTO(String businessNodeCode, ProjectSchemeDTO projectSchemeDTO) {
//        this.projectId = projectSchemeDTO.getProjectId();
//        this.businessNodeCode = businessNodeCode;
//        this.businessId = projectSchemeDTO.getId();
//        this.schemeName = projectSchemeDTO.getName();
//        this.beInformedId = projectSchemeDTO.getCreatorId();
//        this.platformId = projectSchemeDTO.getPlatformId();
//        this.orgId = projectSchemeDTO.getOrgId();
//        this.senderId = CurrentUserHelper.getCurrentUserId();
//        this.senderTime = new Date();
//        this.todoType = TodoTypeDict.TODO_TYPE_TASK;
//    }
//
//    public ApproveMessageDTO(String businessNodeCode, ProjectSchemeApplyApproval projectSchemeApplyApproval) {
//        this.projectId = projectSchemeApplyApproval.getProjectId();
//        this.businessNodeCode = businessNodeCode;
//        this.businessId = projectSchemeApplyApproval.getProjectSchemeId();
//        this.beInformedId = projectSchemeApplyApproval.getCreatorId();
//        this.platformId = projectSchemeApplyApproval.getPlatformId();
//        this.orgId = projectSchemeApplyApproval.getOrgId();
//        this.senderId = CurrentUserHelper.getCurrentUserId();
//        this.senderTime = new Date();
//    }
//}
