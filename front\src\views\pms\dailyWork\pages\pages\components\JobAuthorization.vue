<script setup lang="ts">
import {
  BasicCard, BasicForm, FormSchema, OrionTable, UploadList, useForm, BasicButton, isPower,
} from 'lyra-component-vue3';
import {
  h, inject, onMounted, ref, Ref,
} from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import { openPostTableSelect } from '/@/views/pms/dailyWork/pages/utils';
import { message } from 'ant-design-vue';
import { disabledEndDate, disabledStartDate } from '/@/views/pms/utils/utils';

const detailsData: Record<string, any> = inject('detailsData');
const powerData: Ref = inject('powerData');
const powerCodePrefix: Ref = inject('powerCodePrefix');
const tableOptions = {
  showSmallSearch: false,
  isSpacing: false,
  showTableSetting: false,
  showToolButton: false,
  pagination: false,
  maxHeight: 300,
  actions: [
    {
      isShow: () => isPower(`${powerCodePrefix.value}_container_04_02_button_01`, powerData.value),
      text: '移除',
      modalTitle: '移除提示！',
      modalContent: '确认移除当前数据？',
      modal() {
        setFieldsValue({
          jobPostEquDTOList: [],
        });
        return Promise.resolve(true);
      },
    },
  ],
};

const uploadPowerCode = {
  upload: `${powerCodePrefix.value}_container_04_03_button_01`,
  delete: `${powerCodePrefix.value}_container_04_03_button_03`,
  download: `${powerCodePrefix.value}_container_04_03_button_02`,
  preview: `${powerCodePrefix.value}_container_04_03_button_04`,
};

const schemas: FormSchema[] = [
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '岗位授权信息',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  {
    field: 'isApplyJobEqu',
    label: '是否申请岗位授权等效',
    component: 'Select',
    rules: [
      {
        required: true,
        type: 'boolean',
      },
    ],
    defaultValue: false,
    componentProps: {
      allowClear: false,
      onChange(flag: boolean) {
        setFormData(flag);
      },
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
  },
  {
    field: 'jobPostEquDTOList',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    ifShow({ model }) {
      return model.isApplyJobEqu;
    },
    renderColContent({ model, field }) {
      return h(BasicCard, {
        title: '岗位授权等效核定',
        isSpacing: false,
        isBorder: false,
      }, h('div', {
        style: 'height:260px;overflow:hidden',
      }, h(OrionTable, {
        options: tableOptions,
        key: field,
        dataSource: model[field],
        columns: [
          {
            title: '所属基地',
            dataIndex: 'baseName',
          },
          {
            title: '作业岗位',
            dataIndex: 'jobName',
          },
          {
            title: '授权到期日期',
            dataIndex: 'endDate',
            customRender({ text }) {
              const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
              return h('div', {
                class: 'flex-te',
                title: str,
              }, str);
            },
          },
          {
            title: '授权状态',
            dataIndex: 'authorizeStatusName',
          },
          {
            title: '操作',
            dataIndex: 'action',
            slots: { customRender: 'action' },
            width: 100,
            fixed: 'right',
          },
        ],
      }, isPower(`${powerCodePrefix.value}_container_04_01_button_01`, powerData.value) ? {
        toolbarLeft: () => h(BasicButton, {
          type: 'primary',
          icon: 'sie-icon-tianjiaxinzeng',
          onClick: () => {
            openPostTableSelect(detailsData?.userCode, [], (list: any[]) => {
              model[field] = list.map((item) => ({
                ...item,
                authorId: item.id,
                authorManageId: detailsData?.id,
                historyAuthorId: item.id,
                personCode: detailsData?.userCode,
                personId: detailsData?.personId,
              }));
            });
          },
        }, '选择岗位'),
      } : '')));
    },
  },
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    ifShow({ model }) {
      return !model.isApplyJobEqu;
    },
    renderColContent() {
      return h(BasicCard, {
        title: '岗位授权核定',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  {
    field: 'authorizationGuide',
    label: '岗位授权指引',
    component: 'InputTextArea',
    colProps: {
      span: 24,
    },
    ifShow({ model }) {
      return !model.isApplyJobEqu;
    },
    componentProps: {
      rows: 4,
      disabled: true,
    },
  },
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    ifShow({ model }) {
      return !model.isApplyJobEqu;
    },
    renderColContent() {
      return h(BasicCard, {
        title: '岗位授权信息',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  {
    field: 'baseName',
    label: '所属基地',
    component: 'Input',
    ifShow({ model }) {
      return !model.isApplyJobEqu;
    },
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'baseCode',
    label: '所属基地编码',
    component: 'Input',
    ifShow({ model }) {
      return !model.isApplyJobEqu;
    },
    show: false,
  },
  {
    field: 'name',
    label: '作业岗位',
    component: 'Input',
    ifShow({ model }) {
      return !model.isApplyJobEqu;
    },
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'authorizeStartDate',
    label: '授权起始日期',
    component: 'DatePicker',
    required: true,
    ifShow({ model }) {
      return !model.isApplyJobEqu;
    },
    componentProps(params) {
      return {
        valueFormat: 'YYYY-MM-DD',
        disabledDate: (startDate) => disabledStartDate(startDate, params.formModel?.endDate),
        onChange(e) {
          params.formModel.endDate = e ? dayjs(e).add((params.formModel?.authorizationTime ?? 0), 'M') : null;
          params.formActionType.clearValidate('endDate');
        },
      };
    },
  },
  {
    field: 'authorizationTime',
    label: '授权时间（月）',
    component: 'InputNumber',
    ifShow({ model }) {
      return !model.isApplyJobEqu;
    },
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'endDate',
    label: '授权到期日期',
    component: 'DatePicker',
    ifShow({ model }) {
      return !model.isApplyJobEqu;
    },
    componentProps(params) {
      return {
        valueFormat: 'YYYY-MM-DD',
        disabled: !params.formModel?.authorizeStartDate,
        disabledDate: (endDate) => disabledEndDate(endDate, params.formModel?.authorizeStartDate) || dayjs(endDate) > dayjs(params.formModel?.authorizeStartDate).add((params.formModel?.authorizationTime ?? 0), 'M'),
      };
    },
  },
  {
    field: 'dataStatus',
    label: '授权状态',
    component: 'Input',
    ifShow({ model }) {
      return !model.isApplyJobEqu;
    },
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'fileDTOList',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent({ model, field }) {
      return h(BasicCard, {
        title: model.isApplyJobEqu ? '岗位授权等效材料' : '岗位授权材料',
        isSpacing: false,
        isBorder: false,
      }, h(UploadList, {
        height: 300,
        isSpacing: false,
        type: 'modal',
        listData: model[field],
        edit: detailsData?.status !== 130,
        isFileDelete: detailsData?.status !== 130,
        powerData: powerData.value,
        powerCode: uploadPowerCode,
        onChange(fileDTOList: any[]) {
          setFieldsValue({
            fileDTOList,
          });
        },
      }));
    },
  },
];

const [register, { validate, setFieldsValue, getFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  detailsData?.id && getFormData();
});

const loading: Ref<boolean> = ref(false);
const authInfo: Ref = ref({});

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/job-post-authorize/manage/info').fetch('', detailsData?.id, 'GET');
    authInfo.value = result || {};
    await setFieldsValue({
      isApplyJobEqu: result?.isApplyJobEqu,
    });
    if (result?.isApplyJobEqu) {
      await getEquivalentData();
    }
    await setFormData(authInfo.value?.isApplyJobEqu);
  } finally {
    loading.value = false;
  }
}

// 为否设置表单回显
async function setFormData(flag: boolean) {
  loading.value = true;
  try {
    if (!flag) {
      await setFieldsValue({
        authorizationGuide: authInfo.value?.authorizationGuide,
        authorizeStartDate: authInfo.value?.authorizeStartDate,
        authorizationTime: authInfo.value?.jobPostLibrary?.authorizationTime,
        baseName: authInfo.value?.jobPostLibrary?.baseName,
        baseCode: authInfo.value?.jobPostLibrary?.baseCode,
        endDate: authInfo.value?.endDate,
        name: authInfo.value?.jobPostLibrary?.name,
        dataStatus: detailsData?.dataStatus?.name,
        fileDTOList: authInfo.value?.fileVOList || [],
      });
    } else {
      await setFieldsValue({
        fileDTOList: authInfo.value?.fileVOList || [],
        jobPostEquDTOList: equivalent.value || [],
      });
    }
  } finally {
    loading.value = false;
  }
}

// 获取岗位等效信息
const equivalent: Ref<any[]> = ref([]);

async function getEquivalentData() {
  const result = await new Api(`/pms/job-post-authorize/manage/equivalent/info/${detailsData?.id}`).fetch('', '', 'GET');
  equivalent.value = result || [];
}

defineExpose({
  async submit() {
    await validate();
    const formValues = getFieldsValue();
    return new Promise((resolve, reject) => {
      new Api('/pms/job-post-authorize/manage/save').fetch({
        ...formValues,
        id: detailsData?.id,
      }, '', 'POST').then(() => {
        message.success('保存成功');
        resolve('');
      }).catch((e) => {
        reject(e);
      });
    });
  },
});
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">

</style>
