package com.chinasie.orion.management.service;


import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.dto.ContractSupplierRecordDTO;
import com.chinasie.orion.management.domain.entity.ContractSupplierRecord;
import com.chinasie.orion.management.domain.vo.ContractSupplierRecordVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * ContractSupplierRecord 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
public interface ContractSupplierRecordService extends OrionBaseService<ContractSupplierRecord> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    ContractSupplierRecordVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param contractSupplierRecordDTO
     */
    String create(ContractSupplierRecordDTO contractSupplierRecordDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param contractSupplierRecordDTO
     */
    Boolean edit(ContractSupplierRecordDTO contractSupplierRecordDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     *
     * @param mainTableId
     */
    Page<ContractSupplierRecordVO> pages(String mainTableId, Page<ContractSupplierRecordDTO> pageRequest) throws Exception;

    /**
     * 根据合同编号查询合同行列表信息
     * <p>
     * * @param code
     */
    Page<ContractSupplierRecordVO> getByCode(Page<ContractSupplierRecordDTO> pageRequest) throws Exception;


    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<ContractSupplierRecordVO> vos) throws Exception;

    Page<ContractSupplierRecordVO> getChildByCode(Page<ContractSupplierRecordDTO> pageRequest)throws Exception;
}
