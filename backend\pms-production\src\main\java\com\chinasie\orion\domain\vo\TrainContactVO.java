package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/07/29/11:10
 * @description:
 */

@ApiModel(value = "TrainContactVO对象", description = "培训联络人信息表")
@Data
public class TrainContactVO extends ObjectVO implements Serializable {
    @ApiModelProperty(value = "联络人类型名称")
    private String contactTypeName;
    @ApiModelProperty(value = "联络人类型")
    private String contactType;

    /**
     * 中心ID或部门ID
     */
    @ApiModelProperty(value = "中心ID或部门ID")
    private String deptId;


    /**
     * 中心名称或者部门名称
     */
    @ApiModelProperty(value = "中心名称或者部门名称")
    private String deptName;


    /**
     * 联系人id拼接
     */
    @ApiModelProperty(value = "联系人id拼接")
    private String contactPersonIds;
    /**
     * 联系人名称拼接
     */
    @ApiModelProperty(value = "联系人名称拼接")
    private String contactPersonNames;
    /**
     * 基地编码
     */
    @ApiModelProperty(value = "基地编码")
    @TableField(value = "base_code")
    private String baseCode;

    /**
     * 基地名称
     */
    @ApiModelProperty(value = "基地名称")
    @TableField(value = "base_name")
    private String baseName;

    /**
     * 管理属性
     */
    @ApiModelProperty(value = "管理属性")
    private String manageType;

    /**
     * 管理属性
     */
    @ApiModelProperty(value = "管理属性")
    private String manageTypeName;


}
