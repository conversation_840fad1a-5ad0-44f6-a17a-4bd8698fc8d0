<script setup lang="ts">
import {
  computed, ComputedRef, inject, onMounted, ref, Ref, watch,
} from 'vue';
import SpinView from '/@/views/pms/components/SpinView.vue';
import { useChart } from './useChart';
import Api from '/@/api';

const projectId:string = inject('projectId');
const loading:Ref<boolean> = ref(false);
const riskInfo:Ref<Record<string, any>> = ref({});
const dataOptions = computed(() => [
  {
    name: '风险总数',
    value: riskInfo.value.total || 0,
  },
  {
    color: '#40C057',
    name: '已完成风险',
    value: riskInfo.value.closeCount || 0,
  },
  {
    color: '#FFD351',
    name: '剩余风险',
    value: riskInfo.value.noCloseCount || 0,
  },
]);
const legendOptions:ComputedRef<any[]> = computed(() => dataOptions.value.filter((item) => item.color));
const chartOption = computed(() => ({
  tooltip: {
    trigger: 'item',
  },
  legend: {
    show: false,
  },
  color: legendOptions.value.map((item) => item.color),
  series: [
    {
      name: '风险总数',
      type: 'pie',
      radius: [0, '90%'],
      center: ['50%', '50%'],
      label: {
        show: false,
      },
      data: legendOptions.value,
    },
  ],
}));

const chartRef: Ref = ref();
const chartInstance = useChart(chartRef, loading);

watch(() => chartOption.value, (value) => {
  chartInstance.value.setOption(value);
  chartInstance.value.hideLoading();
});

onMounted(() => {
  getRiskInfo();
});

async function getRiskInfo() {
  loading.value = true;
  try {
    const result = await new Api(`/pms/projectOverview/zgh/riskCount/${projectId}`).fetch({
    }, '', 'GET');
    riskInfo.value = result || {};
  } finally {
    loading.value = false;
  }
}
</script>

<template>
  <div class="container-risk">
    <div>
      <div
        v-for="(item,index) in dataOptions"
        :key="index"
        class="custom-legend-title"
      >
        <span>{{ item.name }}：</span>
        <span class="value">{{ item.value || 0 }}个</span>
      </div>
    </div>
    <spin-view
      v-if="loading"
      class="chart-risk"
    />
    <div
      v-show="!loading"
      ref="chartRef"
      class="chart-risk"
    />
    <div>
      <div
        v-for="(item,index) in legendOptions"
        :key="index"
        class="custom-legend-item"
      >
        <span :style="{backgroundColor:item.color}" />
        <span>{{ item.name }}<span />
        </span>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.container-risk {
  display: flex;
  align-items: center;
}

.custom-legend-title + .custom-legend-title {
  margin-top: 10px;
}

.chart-risk {
  width: 0;
  flex-grow: 1;
  height: 150px;
  margin: 0 12px;
}
</style>
