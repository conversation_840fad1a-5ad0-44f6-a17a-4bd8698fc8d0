<template>
  <div>
    <UploadList
      :key="getKey()"
      v-loading="loading"
      class="deliver-upload-list"
      :listApi="listApi"
      :saveApi="saveApi"
      :deleteApi="deleteApi"
      :batchDeleteApi="batchDeleteApi"
      :powerCode="powerCode"
      :powerData="powerData"
      :is-set-template="true"
      :isCreateBook="isCreateBook"
      :set-template-table-api="setTemplateTableApi"
      :set-template-tree-api="setTemplateTreeApi"
      :isSetTemplateOpenFile="true"
      @changeTemplate="changeTemplate"
      @clickAddTemplate="clickAddTemplate"
    />
  </div>
</template>

<script lang="ts">
import {
  inject, reactive, ref, toRefs,
} from 'vue';
import Api from '/@/api';
import {
  isPower, UploadList, useModal, openFile, randomString,
} from 'lyra-component-vue3';
import { message } from 'ant-design-vue';

export default {
  name: 'Index',
  components: {
    UploadList,
  },
  props: {
    projectId: {
      type: String,
      default: '',
    },
    id: {
      type: String,
      default: '',
    },
    pageType: {
      type: String,
      default: 'page',
    },
  },
  setup(props) {
    const state = reactive({
      tableRef: ref(),
      loading: false,
      dataSource: [],
      powerData: [],
      fileId: undefined,
    });
    const [registerModal, { openModal }] = useModal();
    const tableRef = ref();
    const isCreateBook = ref(true);
    state.powerData = inject('detailsPowerData');
    const powerCode = {
      download: 'PMS_XMPSXQ_BUTTON_FILE_DOWNLOAD',
      upload: 'PMS_XMPSXQ_BUTTON_FILE_UPDATE',
      delete: 'PMS_XMPSXQ_BUTTON_FILE_DELETE',
    };

    async function listApi() {
      return new Api('/pms/document/getList').fetch('', props.id, 'POST');
    }
    async function saveApi(files) {
      let fieldList = files.map((item) => {
        item.dataId = props?.id;
        item.projectId = props.projectId;
        return item;
      });
      return new Api('/pms/document/saveBatch').fetch(fieldList, '', 'POST');
    }

    async function deleteApi(deleteApi) {
      return new Api('/pms/document/removeBatch').fetch([deleteApi.id], '', 'DELETE');
    }

    async function batchDeleteApi({ keys, rows }) {
      if (keys.length === 0) {
        message.warning('请选择文件');
        return;
      }
      return new Api('/pms/document/removeBatch').fetch(rows.map((item) => item.id), '', 'DELETE');
    }
    const clickAddTemplateOption = ref();

    function changeTemplate(params) {
      clickAddTemplateOption.value = params;
      isCreateBook.value = false;
    }
    async function clickAddTemplate() {
      try {
        const templateid:string = clickAddTemplateOption.value.tableData[0].id;
        state.loading = true;
        //   请求创建模板接口
        await new Api(`/res/document-generate/generateDocument/type/deliver/templateid/${templateid}/dataid/${props?.id}`).fetch('', '', 'POST');
        isCreateBook.value = false;
        getKey();
      } finally {
        state.loading = false;
      }
    }
    function setTemplateTreeApi() {
      return new Api('/res/bookmark-Document-TemplateType/tree').fetch('', '', 'GET');
    }

    function setTemplateTableApi(params:any[]) {
      let name = params.searchConditions?.[0]?.[0]?.values?.[0] ?? '';

      return new Api(`/res/bookmark-Document-Template/pages/${params.node?.id}`).fetch({
        status: 'open',
        name,
      }, '', 'POST');
    }
    function clickTemplateFile(record) {
      debuffer;
      if (record.filePostfix === 'docx' || record.filePostfix === 'doc') {
        openModal(true, {
          type: 'add',
          fileId: record.id,
        });
      } else {
        openFile(record);
      }
    }
    function getKey() {
      return randomString(8);
    }
    return {
      ...toRefs(state),
      isPower,
      listApi,
      saveApi,
      deleteApi,
      batchDeleteApi,
      powerCode,
      isCreateBook,
      changeTemplate,
      setTemplateTreeApi,
      setTemplateTableApi,
      clickAddTemplate,
      tableRef,
      clickTemplateFile,
      registerModal,
      getKey,
    };
  },
};
</script>

<style scoped lang="less">
.deliver-upload-list{
  .file-name {
    padding-left: 4px;
    cursor:pointer
  }
}

</style>
