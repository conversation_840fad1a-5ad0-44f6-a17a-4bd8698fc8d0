package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.AdjustmentVoucherDTO;
import com.chinasie.orion.domain.dto.HangingConnectDTO;
import com.chinasie.orion.domain.vo.AdjustmentVoucherVO;
import com.chinasie.orion.service.AdjustmentVoucherService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;


import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * AdjustmentVoucher 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-24 10:44:40
 */
@RestController
@RequestMapping("/adjustmentVoucher")
@Api(tags = "调账凭证数据表")
public class  AdjustmentVoucherController  {

    @Autowired
    private AdjustmentVoucherService adjustmentVoucherService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "调账凭证数据表", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<AdjustmentVoucherVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        AdjustmentVoucherVO rsp = adjustmentVoucherService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param adjustmentVoucherDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#adjustmentVoucherDTO.name}}】", type = "调账凭证数据表", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody AdjustmentVoucherDTO adjustmentVoucherDTO) throws Exception {
        String rsp =  adjustmentVoucherService.create(adjustmentVoucherDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param adjustmentVoucherDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#adjustmentVoucherDTO.name}}】", type = "调账凭证数据表", subType = "编辑", bizNo = "{{#adjustmentVoucherDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody AdjustmentVoucherDTO adjustmentVoucherDTO) throws Exception {
        Boolean rsp = adjustmentVoucherService.edit(adjustmentVoucherDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "调账凭证数据表", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = adjustmentVoucherService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "调账凭证数据表", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = adjustmentVoucherService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "调账凭证数据表", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<AdjustmentVoucherVO>> pages(@RequestBody Page<AdjustmentVoucherDTO> pageRequest) throws Exception {
        Page<AdjustmentVoucherVO> rsp =  adjustmentVoucherService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("调账凭证数据表导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "调账凭证数据表", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        adjustmentVoucherService.downloadExcelTpl(response);
    }

    @ApiOperation("调账凭证数据表导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "调账凭证数据表", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = adjustmentVoucherService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("调账凭证数据表导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "调账凭证数据表", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  adjustmentVoucherService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消调账凭证数据表导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "调账凭证数据表", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  adjustmentVoucherService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("调账凭证数据表导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "AdjustmentVoucher", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody AdjustmentVoucherDTO adjustmentVoucherDTO, HttpServletResponse response) throws Exception {
        adjustmentVoucherService.exportByExcel(adjustmentVoucherDTO, response);
    }

    @ApiOperation(value = "挂接确认")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】挂接确认", type = "ConnectedMilestones", subType = "挂接确认", bizNo = "")
    @RequestMapping(value = "/hangingConnect", method = RequestMethod.POST)
    public ResponseDTO<Boolean> hangingConnect(@RequestBody List<String> ids) {
        Boolean rsp = adjustmentVoucherService.hangingConnect(ids);
        return new ResponseDTO<>(rsp);
    }
}
