<template>
  <OrionTable
    ref="tableRef"
    class="pdmBasicTable"
    :options="tableOptions"
  >
    <template #toolbarLeft>
      <BasicButton
        v-if="isPower('PAS_FXGLXQ_container_02_01_button_01',powerData)"
        type="primary"
        icon="add"
        @click="clickType('add')"
      >
        新增
      </BasicButton>
      <BasicButton
        v-if="isPower('PAS_FXGLXQ_container_02_01_button_02',powerData)"
        icon="delete"
        @click="clickType('delete')"
      >
        删除
      </BasicButton>
    </template>

    <template #planPredictStartTime="{ text }">
      {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
    </template>
    <template #planPredictEndTime="{ text }">
      {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
    </template>

    <template #action="{record}">
      <BasicTableAction :actions="actionsBtn(record)" />
    </template>
  </OrionTable>

  <!-- 查看详情弹窗 -->
  <checkDetails :data="nodeData" />
  <!-- 简易弹窗提醒 -->
  <messageModal
    :title="'确认提示'"
    :show-visible="showVisible"
    @cancel="showVisible = false"
    @confirm="confirm"
  >
    <div class="messageVal">
      <InfoCircleOutlined />
      <span>{{ message }}</span>
    </div>
  </messageModal>
  <!-- 新建/编辑抽屉 -->
  <addProjectModal
    :data="addNodeModalData"
    :list-data="editdataSource"
    :projectid="id"
    @success="successSave"
  />
  <!-- 从系统添加 -->
  <AddSystemRole
    :id="id"
    :data="addSystemModalData"
    @success="successSave"
  />

  <SearchModal
    @register="searchRegister"
    @searchEmit="searchEmit"
  />
</template>
<script lang="ts">
import {
  computed, defineComponent, h, inject, onMounted, reactive, Ref, toRefs,
} from 'vue';
import { message, Modal } from 'ant-design-vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
import { formatterTime } from '/@/views/pms/projectLaborer/utils/time';
import {
  BasicButton, BasicTableAction, DataStatusTag, isPower, ITableActionItem, OrionTable, useDrawer,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
import { useRouter } from 'vue-router';
import addProjectModal from './modal/addProjectModal.vue';
import AddSystemRole from './modal/addSystemRole.vue';
import checkDetails from './modal/checkmodal.vue';
import messageModal from '/@/views/pms/projectLaborer/components/MessageModal/messageModal.vue';
import Api from '/@/api';
import SearchModal from './SearchModal.vue';
import { useQiankun } from '/@/utils/qiankun/useQiankun';

export default defineComponent({
  name: 'ProductLibraryIndex',
  components: {
    BasicTableAction,
    BasicButton,
    //   提示图标
    InfoCircleOutlined,
    //   addNodeModal,
    messageModal,
    checkDetails,
    /* 新建项目抽屉 */
    addProjectModal,
    AddSystemRole,
    SearchModal,
    OrionTable,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
  },

  setup(props) {
    const [searchRegister, { openDrawer: openSearchDrawer }] = useDrawer();
    const router = useRouter();
    //   const layoutModelStore = layoutModel();
    const state = reactive({
      tableRef: null,
      /* 搜索框value */
      searchvlaue: '',
      /* 编辑send */
      editdataSource: {},
      /* 多选 */
      selectedRowKeys: [],
      /* 列 */
      dataSource: [],
      tablehttp: {
        orders: [
          {
            asc: false,
            column: '',
          },
        ],

        query: {
          projectId: '',
        },
        // 条数
        pageSize: 10,
        /* 页数 */
        pageNum: 1,
        /* 总数 */
        total: 0,
        queryCondition: [],
      },
      // 条数
      pageSize: 10,
      /* 页数 */
      current: 1,
      /* 总数 */
      total: 20,
      addNodeModalData: {},
      selectedRows: [],
      addSystemModalData: {},
      showVisible: false,
      message: '',
      nodeData: {},
      searchData: {},
      params: {},
      tableHeight: 400,
    });
    const powerData: Ref = inject('powerData');
    /* 分页 */
    const pagination = computed(() => ({
      pageSize: state.tablehttp.pageSize,
      current: state.tablehttp.pageNum,
      total: state.tablehttp.total,
      // showQuickJumper: true,
      showSizeChanger: true,
      showTotal: (total) => `共${total}条`,
    }));
    /* 多选cb */
    const onSelectChange = (selectedRowKeys, selectedRows) => {
      state.selectedRowKeys = selectedRowKeys;
      state.selectedRows = selectedRows;
    };
    /* 页数变化cb */
    const handleChange = (pag, filters, sorter: any) => {
      // 如果是多选触发,则不更新页面
      if (typeof pag.current === 'undefined') return;
      state.tablehttp.pageNum = pag.current;
      state.tablehttp.pageSize = pag.pageSize;
      state.tablehttp.orders[0].asc = sorter.order === 'ascend';
      state.tablehttp.orders[0].column = sorter.columnKey;

      state.tableRef.reload();
    };
    /* 右按钮 */
    const clickType = (type) => {
      switch (type) {
        case 'edit':
          // editNode();
          break;
        case 'add':
          addSystemRoleHandle();
          break;
        case 'open':
          // openDetail();
          // openPlan();
          break;
        case 'delete':
          // deleteNode();
          multiDelete();
          break;
        case 'search':
          // state.searchData = {};
          openSearchDrawer(true);
          break;
      }
    };
    let projectId: any = inject('projectId');

    const addSystemRoleHandle = () => {
      // console.log('从系统创建角色');
      state.addSystemModalData = { formType: 'add' };
    };
    /* 简易弹窗的确定cb */
    const confirm = () => {
      // 删除操作
      deletrow();
    };
    onMounted(() => {
      /* 高度变化 */
      state.tableHeight = document.body.clientHeight - 440;
    });
    let riskItemId: any = inject('riskItemId');

    /* 删除操作 */
    const deletrow = () => {
      const newArr = {
        id: riskItemId.value,
        planIds: state.selectedRowKeys,
      };
      const love = {
        className: 'RiskManagement',
        moduleName: '项目管理-风险管理-实际风险-关联计划',
        type: 'DELETE',
        remark: `删除了【${state.selectedRowKeys}】`,
      };
      // DeleteRiskContactPlanApi(newArr, love)
      new Api('/pas/risk-management/relation/plan/batch').fetch(newArr, '', 'DELETE')
        .then((res) => {
          message.success('删除成功');
          state.showVisible = false;
          state.tableRef.reload();
        })
        .catch(() => {
          state.showVisible = false;
        });
    };

    function searchEmit(data) {
      state.tableRef.reload();
    }

    /* 批量检查选择条数fn */
    const multiLengthCheckHandle = () => {
      if (state.selectedRows.length === 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };
    /* 批量删除 */
    const multiDelete = () => {
      if (multiLengthCheckHandle()) return;
      Modal.confirm({
        title: '请确认是否对当前选中数据进行删除?',
        onOk() {
          deletrow();
        },
      });
      //
      // state.message = '请确认是否对当前选中数据进行删除？';
      // state.showVisible = true;
    };

    /* 新建项目成功回调 */
    const successSave = () => {
      state.tablehttp.pageNum = 1;
      state.selectedRowKeys = [];
      state.selectedRows = [];

      state.tableRef.reload();
      state.searchvlaue = '';
      onSearch();
    };
    const clickRow = (record, index) => {
      const num = state.selectedRowKeys.findIndex((item) => item === record.id);
      num === -1 ? state.selectedRowKeys.push(record.id) : state.selectedRowKeys.splice(num, 1);
      const row = state.selectedRows.findIndex((item) => item.id === record.id);
      row === -1 ? state.selectedRows.push(record) : state.selectedRows.splice(row, 1);
    };
    const tableOptions = {
      showIndexColumn: false,
      pagination: false,
      bordered: false,
      deleteToolButton: 'add|delete|enable|disable',
      rowClick: clickRow,
      rowSelection: {
        selectedRowKeys: computed(() => state.selectedRowKeys),
        onChange: onSelectChange,
      },
      api(params) {
        return new Api(`/pas/risk-management/relation/plan/${riskItemId.value}`).fetch({
          ...params,
          power: {
            pageCode: 'PMSRiskManagementDetails',
            containerCode: 'PAS_FXGLXQ_container_02_01',
          },
        }, '', 'POST');
      },
      columns: [
        {
          title: '编号',
          dataIndex: 'number',
          align: 'left',
          key: 'number',

          width: '120px',
          // sorter: true,
          ellipsis: true,
        },
        {
          title: '任务项',
          dataIndex: 'name',
          key: 'name',
          customRender({ record, text }) {
            if (isPower('PAS_FXGLXQ_container_02_01_button_04', powerData.value)) {
              return h(
                'span',
                {
                  class: 'action-btn',
                  title: text,
                  onClick(e) {
                    router.push({
                      name: 'ProPlanDetails',
                      params: {
                        id: record.id,
                      },
                    });
                    // let routerName = record.planType !== 'milestone' ? 'ProPlanDetails' : 'ProPlanDetails';
                    // // let routerName = 'ProPlanDetails';
                    // if (record.planType === 'milestone') {
                    //   router.push({
                    //     name: routerName,
                    //     query: {
                    //       id: record.id,
                    //       projectId: record?.projectId,
                    //     },
                    //   });
                    // } else {
                    //   router.push({
                    //     name: routerName,
                    //     params: {
                    //       id: record.id,
                    //     },
                    //   });
                    // }
                  },
                },
                text,
              );
            }
            return text;
          },
          minWidth: 240,
        },
        {
          title: '计划类型',
          dataIndex: 'planTypeName',
          key: 'planType',
          width: '100px',
          margin: '0 20px 0 0',
          align: 'left',
          slots: { customRender: 'planTypeName' },
          // sorter: true,
          ellipsis: true,
        },
        {
          title: '负责人',
          dataIndex: 'principalName',
          key: 'principalId',

          width: '80px',
          align: 'left',
          // sorter: true,
          ellipsis: true,
          slots: { customRender: 'principalName' },
        },
        // {
        //   title: '计划进度',
        //   dataIndex: 'scheduleName',
        //   key: 'schedule',
        //   width: '90px',
        //   align: 'left',
        //   slots: { customRender: 'scheduleName' },
        //   // sorter: true,
        //   ellipsis: true,
        // },
        // {
        //   title: '优先级',
        //   dataIndex: 'priorityLevelName',
        //   key: 'priorityLevel',
        //   width: '80px',
        //   align: 'left',
        //   slots: { customRender: 'priorityLevelName' },
        //   // sorter: true,
        //   ellipsis: true,
        // },
        {
          title: '状态',
          dataIndex: 'dataStatus',
          customRender({ text }) {
            return text ? h(DataStatusTag, { statusData: text }) : '';
          },
          width: 120,
        },
        {
          title: '开始日期',
          dataIndex: 'planPredictStartTime',
          key: 'planPredictStartTime',
          width: '130px',
          align: 'left',
          slots: { customRender: 'planPredictStartTime' },
          sorter: true,
          ellipsis: true,
        },
        {
          title: '结束日期',
          dataIndex: 'planPredictEndTime',
          key: 'planPredictEndTime',
          width: '150px',
          align: 'left',
          sorter: true,
          ellipsis: true,
          slots: { customRender: 'planPredictEndTime' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 150,
          align: 'left',
          fixed: 'right',
          slots: { customRender: 'action' },
        },
      ],
    };

    const actionsBtn = (record) => {
      const actions: ITableActionItem<any>[] = [
        {
          text: '删除',
          isShow: (record: Record<string, any>) => isPower('PAS_FXGLXQ_container_02_01_button_03', record?.rdAuthList),
          modal() {
            const newArr = {
              id: riskItemId.value,
              planIds: [record.id],
            };
            const love = {
              className: 'RiskManagement',
              moduleName: '项目管理-风险管理-实际风险-关联计划',
              type: 'DELETE',
              remark: `删除了【${state.selectedRowKeys}】`,
            };
            return new Api('/pas/risk-management/relation/plan/batch').fetch(newArr, '', 'DELETE')
              .then(() => {
                message.success('删除成功');
                state.tableRef.reload();
              });
          },
        },
      ];
      return actions;
    };

    return {
      ...toRefs(state),
      clickRow,
      clickType,
      pagination,
      onSelectChange,
      handleChange,
      formatterTime,
      confirm,
      dayjs,
      multiDelete,
      successSave,
      addSystemRoleHandle,
      searchRegister,
      searchEmit,
      tableOptions,
      actionsBtn,
      isPower,
      powerData,
    };
  },
});
</script>
<style lang="less" scoped>
</style>
