<template>
  <div class="files-wrap">
    <UploadList
      ref="tableRef"
      class="pay-node-table"
      :listData="dataSource"
      :saveApi="saveApi"
      :batchDeleteApi="batchDeleteApi"
      :deleteApi="deleteApi"
    />
  </div>
</template>
<script setup lang="ts">
import { UploadList } from 'lyra-component-vue3';
import {
  computed, inject, onMounted, reactive, ref, Ref,
} from 'vue';
import Api from '/@/api';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';

const props = defineProps<{
  id: string
}>();
const state = reactive({
  allData: {},
});
const route = useRoute();
const dataSource = computed(() => state.allData?.documentVOList?.map((item) => ({
  ...item,
})) ?? []);
const tableRef = ref();
async function saveApi(filesRes) {
  const files = filesRes.map((item) => {
    const {
      filePath, filePostfix, fileSize, name,
    } = item;

    return {
      dataId: props.id,
      filePath,
      filePostfix,
      fileSize,
      name,
    };
  });
  return new Api('/pms/document/saveBatch').fetch(files, '', 'POST').then((res) => {
    getDetail();
  });
}

function batchDelete(ids:string[]) {
  return new Api('/pms/document/removeBatch').fetch(ids, '', 'DELETE').then((res) => {
    getDetail();
  });
}
onMounted(() => {
  getDetail();
});
async function batchDeleteApi({ keys, rows }) {
  if (keys.length === 0) {
    message.warning('请选择文件');
    return;
  }
  batchDelete(rows.map((item) => item.id)).then((res) => {
    tableRef.value.tableRef.clearSelectedRowKeys();
  });
}

async function deleteApi(deleteApi) {
  batchDelete([deleteApi.id]);
}

// 初始化数据
async function getDetail() {
  const allData = await new Api(`/pas/projectContract/all/${route.query.id}`).fetch('', '', 'GET').finally(() => {
  });

  state.allData = allData;
}

</script>

<style scoped lang="less">

.files-wrap {
  height: 100%;
  position: relative;
  overflow: hidden;
}
</style>
