<script setup lang="ts">
import {
  BasicButton, BasicTableAction, DataStatusTag, isPower, Layout, OrionTable,
} from 'lyra-component-vue3';
import { h, ref, Ref } from 'vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { Modal, Popover } from 'ant-design-vue';
import { deleteApi, openDailyWorkForm } from './utils';
import Api from '/@/api';
import { usePagePower } from '/@/views/pms/hooks';

const router = useRouter();
const { powerData, getPowerDataHandle } = usePagePower();
const tableRef: Ref = ref();
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const tableOptions = {
  rowSelection: {
    onChange(_keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  showToolButton: false,
  isSpacing: true,
  columns: [
    {
      title: '操作',
      dataIndex: 'actions',
      width: 160,
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
    {
      title: '工单号',
      dataIndex: 'number',
      width: 123,
    },
    {
      title: '作业名称',
      dataIndex: 'name',
      width: 145,
      customRender({ text, record }) {
        if (isPower('PMS_RCZY_container_01_button_01', record?.rdAuthList)) {
          return h('span', {
            class: 'flex-te action-btn',
            title: text,
            onClick: () => navDetails(record?.id),
          }, text);
        }
        return h('span', {
          class: 'flex-te',
          title: text,
        }, text);
      },
    },
    {
      title: '作业负责人',
      width: 95,
      dataIndex: 'rspUserName',
    },
    {
      title: '负责人所在中心',
      dataIndex: 'rspDeptName',
    },
    {
      title: '项目编号',
      width: 130,
      dataIndex: 'projectNumber',
    },
    {
      title: '项目名称',
      width: 165,
      dataIndex: 'projectName',
    },
    {
      title: '是否重要作业',
      dataIndex: 'isImportant',
      width: 112,
      customRender({ text }) {
        return text ? '是' : text === false ? '否' : '';
      },
    },
    {
      title: '高风险',
      width: 80,
      dataIndex: 'heightRiskName',
    },
    {
      title: '首次执行',
      width: 128,
      dataIndex: 'firstExecuteName',
    },
    {
      title: '新人参与',
      width: 90,
      dataIndex: 'newParticipants',
      customRender({ text }) {
        return text ? '是' : text === false ? '否' : '';
      },
    },
    {
      title: '重要项目',
      dataIndex: 'importantProjectName',
      width: 110,
    },
    {
      title: '是否自带工器具',
      width: 120,
      dataIndex: 'isCarryTool',
      customRender({ text }) {
        return text ? '是' : text === false ? '否' : '';
      },
    },
    {
      title: '作业状态',
      dataIndex: 'busDataStatus',
      customRender({ text, record }) {
        if (record?.phase && text) {
          return h(Popover, null, {
            content: () => record.phase,
            default: () => h(DataStatusTag, { statusData: text }),
          });
        }
        return text ? h(DataStatusTag, { statusData: text }) : '';
      },
    },
    {
      title: '计划开工时间',
      dataIndex: 'beginTime',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '计划结束时间',
      dataIndex: 'endTime',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '计划工期',
      dataIndex: 'workDuration',
      width: 90,
    },
    {
      title: '实际开工时间',
      dataIndex: 'actualBeginTime',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '实际完成时间',
      dataIndex: 'actualEndTime',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
  ],
  api: (params: Record<string, any>) => new Api('/pms/job-manage').fetch({
    ...params,
    query: {
      norO: 'N',
    },
    power: {
      containerCode: 'PMS_RCZY_container_01',
      pageCode: 'PMSDailyWork',
    },
  }, 'page', 'POST'),
};

const actions: any[] = [
  {
    text: '查看',
    event: 'view',
    isShow: (record) => isPower('PMS_RCZY_container_01_button_01', record?.rdAuthList),
  },
  {
    text: '编辑',
    event: 'edit',
    isShow: (record) => isPower('PMS_RCZY_container_01_button_02', record?.rdAuthList),
  },
  {
    text: '删除',
    event: 'delete',
  },
];

function actionClick(actionItem: any, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'edit':
      openDailyWorkForm(record, updateTable);
      break;
    case 'view':
      navDetails(record.id);
      break;
    case 'delete':
      Modal.confirm({
        title: '删除提示！',
        content: '确定删除当前数据吗？',
        onOk: () => deleteApi([record.id], updateTable),
      });
      break;
  }
}

function navDetails(id: string) {
  router.push({
    name: 'PMSDailyWorkDetails',
    params: {
      id,
    },
  });
}

function updateTable() {
  tableRef.value?.reload();
}

function handleDelete() {
  const ids = selectedRows.value.map((item) => item.id);
  Modal.confirm({
    title: '删除提示！',
    content: '确定删除已勾选数据吗？',
    onOk: () => deleteApi(ids, updateTable),
  });
}
</script>

<template>
  <Layout
    v-get-power="{pageCode: 'PMSDailyWork',getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <BasicButton
          type="primary"
          icon="sie-icon-tianjiaxinzeng"
          @click="openDailyWorkForm({},updateTable)"
        >
          新增
        </BasicButton>
        <BasicButton
          icon="sie-icon-shanchu"
          :disabled="selectedRows.length===0"
          @click="handleDelete"
        >
          删除
        </BasicButton>
      </template>
      <template #actions="{record}">
        <BasicTableAction
          :actions="actions"
          :record="record"
          @actionClick="actionClick($event,record)"
        />
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">

</style>
