package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * NcfFormZftvVEfTF Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-04 14:36:09
 */
// old table ncf_Form_zftv_V_ef_TF
@TableName(value = "pmsx_non_fixed_assets")
@ApiModel(value = "NonFixedAssets对象", description = "非固定资产标准库")
@Data
public class NonFixedAssets extends ObjectEntity implements Serializable {

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 资产条码
     */
    @ApiModelProperty(value = "资产条码")
    @TableField(value = "barcode")
    private String barcode;

    /**
     * 资产名称
     */
    @ApiModelProperty(value = "资产名称")
    @TableField(value = "name")
    private String name;

    /**
     * 规格型号
     */
    @ApiModelProperty(value = "规格型号")
    @TableField(value = "sp_model")
    private String spModel;

    /**
     * 是否需要检定
     */
    @ApiModelProperty(value = "是否需要检定")
    @TableField(value = "is_need_verification")
    private Boolean isNeedVerification;

}
