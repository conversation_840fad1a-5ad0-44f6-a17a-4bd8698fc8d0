package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/1/13 14:03
 * @description:
 */
@Data
@ApiModel(value = "ProjectTaskStatusVO对象", description = "项目状态")
public class ProjectTaskStatusVO extends ObjectVO {

    /**
     * 所属项目
     */
    @ApiModelProperty(value = "所属项目")
    private String projectId;

    /**
     * 启用禁用
     */
    @ApiModelProperty(value = "启用禁用")
    private Integer takeEffect;
    private String takeEffectName;

    /**
     * 所属类型
     */
    @ApiModelProperty(value = "所属类型")
    private String type;
    private String typeName;

}
