package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;

import java.io.Serializable;

import java.util.Date;
import java.lang.String;
import java.util.List;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * BasicUserExtendInfo DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-05 10:41:49
 */
@ApiModel(value = "BasicUserExtendInfoDTO对象", description = "人员拓展信息")
@Data
@ExcelIgnoreUnannotated
public class BasicUserExtendInfoDTO extends ObjectDTO implements Serializable {

    /**
     * 流程名称
     */
    @ApiModelProperty(value = "流程名称")
    @ExcelProperty(value = "流程名称 ", index = 0)
    private String processName;

    /**
     * 发起人
     */
    @ApiModelProperty(value = "发起人")
    @ExcelProperty(value = "发起人 ", index = 1)
    private String initiator;

    /**
     * 发起时间
     */
    @ApiModelProperty(value = "发起时间")
    @ExcelProperty(value = "发起时间 ", index = 2)
    private Date initiationTime;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    @ExcelProperty(value = "优先级 ", index = 3)
    private String priority;

    /**
     * 申请类型
     */
    @ApiModelProperty(value = "申请类型")
    @ExcelProperty(value = "申请类型 ", index = 4)
    private String applicationType;

    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
    @ExcelProperty(value = "申请人 ", index = 5)
    private String applicant;

    /**
     * 是否有亲属在集团内
     */
    @ApiModelProperty(value = "是否有亲属在集团内")
    @ExcelProperty(value = "是否有亲属在集团内 ", index = 6)
    private String hasRelativeInGroup;

    /**
     * 亲属姓名
     */
    @ApiModelProperty(value = "亲属姓名")
    @ExcelProperty(value = "亲属姓名 ", index = 7)
    private String relativeName;

    /**
     * 亲属职务
     */
    @ApiModelProperty(value = "亲属职务")
    @ExcelProperty(value = "亲属职务 ", index = 8)
    private String relativePosition;

    /**
     * 亲属公司
     */
    @ApiModelProperty(value = "亲属公司")
    @ExcelProperty(value = "亲属公司 ", index = 9)
    private String relativeCompany;

    /**
     * 最高学历
     */
    @ApiModelProperty(value = "最高学历")
    @ExcelProperty(value = "最高学历 ", index = 10)
    private String highestEducation;

    /**
     * 所学专业
     */
    @ApiModelProperty(value = "所学专业")
    @ExcelProperty(value = "所学专业 ", index = 11)
    private String major;

    /**
     * 职称
     */
    @ApiModelProperty(value = "职称")
    @ExcelProperty(value = "职称 ", index = 12)
    private String title;

    /**
     * 专业技术证书
     */
    @ApiModelProperty(value = "专业技术证书")
    @ExcelProperty(value = "专业技术证书 ", index = 13)
    private String professionalTechnicalCertificate;

    /**
     * 是否需要工号
     */
    @ApiModelProperty(value = "是否需要工号")
    @ExcelProperty(value = "是否需要工号 ", index = 14)
    private String needsEmployeeNumber;

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @ExcelProperty(value = "供应商编码 ", index = 15)
    private String supplierCode;

    /**
     * 所属供应商
     */
    @ApiModelProperty(value = "所属供应商")
    @ExcelProperty(value = "所属供应商 ", index = 16)
    private String affiliatedSupplier;

    /**
     * 是否从事放射性工作
     */
    @ApiModelProperty(value = "是否从事放射性工作")
    @ExcelProperty(value = "是否从事放射性工作 ", index = 17)
    private String worksWithRadioactiveMaterials;

    /**
     * 工作内容
     */
    @ApiModelProperty(value = "工作内容")
    @ExcelProperty(value = "工作内容 ", index = 18)
    private String jobContent;

    /**
     * 常驻服务地点
     */
    @ApiModelProperty(value = "常驻服务地点")
    @ExcelProperty(value = "常驻服务地点 ", index = 19)
    private String permanentServiceLocation;

    /**
     * 所属合同名称
     */
    @ApiModelProperty(value = "所属合同名称")
    @ExcelProperty(value = "所属合同名称 ", index = 20)
    private String contractName;

    /**
     * 合同级别
     */
    @ApiModelProperty(value = "合同级别")
    @ExcelProperty(value = "合同级别 ", index = 21)
    private String contractLevel;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 22)
    private String contractNumber;

    /**
     * 是否项目制人员
     */
    @ApiModelProperty(value = "是否项目制人员")
    @ExcelProperty(value = "是否项目制人员 ", index = 23)
    private String projectBasedStaff;

    /**
     * 入场时间
     */
    @ApiModelProperty(value = "入场时间")
    @ExcelProperty(value = "入场时间 ", index = 24)
    private Date entryTime;

    /**
     * 联系方式
     */
    @ApiModelProperty(value = "联系方式")
    @ExcelProperty(value = "联系方式 ", index = 25)
    private String contactInformation;

    /**
     * 是否已完成体检
     */
    @ApiModelProperty(value = "是否已完成体检")
    @ExcelProperty(value = "是否已完成体检 ", index = 26)
    private String completedPhysicalExamination;

    /**
     * 分管项目经理
     */
    @ApiModelProperty(value = "分管项目经理")
    @ExcelProperty(value = "分管项目经理 ", index = 27)
    private String departmentHeadProjectManager;

    /**
     * 办卡或授权选择
     */
    @ApiModelProperty(value = "办卡或授权选择")
    @ExcelProperty(value = "办卡或授权选择 ", index = 28)
    private String cardOrAuthorizationChoice;

    /**
     * 项目接口人
     */
    @ApiModelProperty(value = "项目接口人")
    @ExcelProperty(value = "项目接口人 ", index = 29)
    private String projectInterfacePerson;

    /**
     * 是否技术配置
     */
    @ApiModelProperty(value = "是否技术配置")
    @ExcelProperty(value = "是否技术配置 ", index = 30)
    private String technicalConfiguration;

    /**
     * 入场备注
     */
    @ApiModelProperty(value = "入场备注")
    @ExcelProperty(value = "入场备注 ", index = 31)
    private String entryRemarks;

    /**
     * 离场时间
     */
    @ApiModelProperty(value = "离场时间")
    @ExcelProperty(value = "离场时间 ", index = 32)
    private Date departureTime;

    /**
     * 预计离岗时间
     */
    @ApiModelProperty(value = "预计离岗时间")
    @ExcelProperty(value = "预计离岗时间 ", index = 33)
    private Date expectedDepartureDate;

    /**
     * 是否已取消授权
     */
    @ApiModelProperty(value = "是否已取消授权")
    @ExcelProperty(value = "是否已取消授权 ", index = 34)
    private String authorizationCancelled;

    /**
     * 是否违反相关安全规范
     */
    @ApiModelProperty(value = "是否违反相关安全规范")
    @ExcelProperty(value = "是否违反相关安全规范 ", index = 35)
    private String violatedSafetyRegulations;

    /**
     * 是否完成离职体检
     */
    @ApiModelProperty(value = "是否完成离职体检")
    @ExcelProperty(value = "是否完成离职体检 ", index = 36)
    private String completedDeparturePhysical;

    /**
     * 离职备注
     */
    @ApiModelProperty(value = "离职备注")
    @ExcelProperty(value = "离职备注 ", index = 37)
    private String departure;

    /**
     * 锁定状态
     */
    @ApiModelProperty(value = "锁定状态")
    @ExcelProperty(value = "锁定状态 ", index = 38)
    private String lockedStatus;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 39)
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @ExcelProperty(value = "主表ID ", index = 40)
    private String mainTableId;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    @ExcelProperty(value = "操作时间 ", index = 41)
    private Date operationTime;

    /**
     * 操作人id
     */
    @ApiModelProperty(value = "操作人id")
    @ExcelProperty(value = "操作人id ", index = 42)
    private String operationId;

    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人姓名")
    @ExcelProperty(value = "操作人姓名 ", index = 43)
    private String operationName;


}
