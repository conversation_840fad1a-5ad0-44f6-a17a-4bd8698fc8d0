<script setup lang="ts">
import {
  isPower, BasicCard, BasicButton, openModal,
} from 'lyra-component-vue3';
import { ref } from 'vue';
import ProjectPlan from './ProjectPlan.vue';
import ProjectPlanDetail from './ProjectPlanDetail.vue';
import { correlation } from '/@/views/pms/api/qualityItem';

const props = defineProps({
  projectId: {
    type: String,
    default: '',
  },
  id: {
    type: String,
    default: '',
  },
  status: {
    type: Number,
    default: 0,
  },
  dataSource: {
    type: Array,
    default: () => [],
  },
  powerData: {
    type: Array,
    default: () => [],
  },
});
const emit = defineEmits(['update']);
const powerCode = {
  mainRelevance: 'PMS_ZLGKX_DETAIL_container_02_button_01',
};
const handleAdd = () => {
  const refModal = ref();
  openModal({
    title: '关联项目计划',
    width: 1100,
    content(h) {
      return h(ProjectPlan, {
        ref: refModal,
        projectId: props.projectId,
      });
    },
    async onOk() {
      const { isSelectedAndGetData } = refModal.value;
      const values = await isSelectedAndGetData();
      await correlation(props.id, values);
      emit('update');
    },
  });
};
</script>

<template>
  <BasicCard title="关联项目计划">
    <BasicButton
      v-if="status === 130 && isPower(powerCode.mainRelevance, powerData)"
      type="primary"
      icon="add"
      @click="handleAdd"
    >
      关联项目计划
    </BasicButton>
    <ProjectPlanDetail :data-source="dataSource" />
  </BasicCard>
</template>

<style scoped lang="less">

</style>
