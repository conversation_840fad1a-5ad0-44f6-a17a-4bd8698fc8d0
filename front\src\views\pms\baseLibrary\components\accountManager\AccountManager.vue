<script setup lang="ts">
import {
  BasicCard, BasicButton, OrionTable,
  SelectUserModal, useModal, isPower,
} from 'lyra-component-vue3';

import Api from '/@/api';
import { message, Modal } from 'ant-design-vue';
import {
  computed, h, inject, ref,
} from 'vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { useRoute, useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { get, map } from 'lodash-es';

const detailsData = inject('detailsData');
const powerData = inject('powerData');
const selectKeys = ref([]);
const route = useRoute();
const tableRef = ref();
const [modalRegister, { openModal }] = useModal();

const selectUserData = ref([]);
const tableOptions = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  rowSelection: {
    onChange(val) {
      selectKeys.value = val;
    },
  },
  isSpacing: true,
  smallSearchField: undefined,
  columns: [
    {
      title: '姓名',
      dataIndex: 'name',
      fixed: 'left',
    },
    {
      title: '工号',
      dataIndex: 'code',
    },
    {
      title: '所属部门',
      dataIndex: 'deptName',
    },
    {
      title: '用户邮箱',
      dataIndex: 'email',
    },
    {
      title: '状态',
      dataIndex: 'status',
    },
    {
      title: '修改人',
      dataIndex: 'modifyName',
    },
    {
      title: '修改时间',
      dataIndex: 'modifyTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 90,
      fixed: 'right',
      slots: { customRender: 'action' },
    },
  ],
  actions: [
    {
      text: '移除',
      isShow: (record) => isPower('PMS_JDKXQ_container_02_button_03', record.rdAuthList),
      onClick(record: Record<string, any>) {
        Modal.confirm({
          title: '操作提示',
          content: '是否移除当前选择的数据?',
          async onOk() {
            const targetKeys = [record.id];
            return await deleteRequest(targetKeys);
          },
        });
      },
    },
  ],
  api: (params) => new Api('/pas/custManagerLink/page').fetch({
    ...params,
    power: {
      pageCode: 'PMSBaseLibraryDetails',
    },
    query: {
      basePlaceId: route.params.id,
    },
  }, '', 'POST').then((res) => {
    selectUserData.value = res.content;
    return res;
  }),
};

const showAddBtn = computed(() => isPower('PMS_JDKXQ_container_02_button_01', powerData.value));
const showBatchDeleteBtn = computed(() => isPower('PMS_JDKXQ_container_02_button_02', powerData.value));

function updateTable() {
  tableRef.value?.reload?.();
}
function handleAssociationRequirement() {
  openModal(true, {});
}
function handleRemove() {
  Modal.confirm({
    title: '删除警告',
    icon: h(ExclamationCircleOutlined),
    content: '确定删除选中的数据',
    async onOk() {
      try {
        const targetKeys = tableRef.value?.getSelectRowKeys();
        return await deleteRequest(targetKeys);
      } catch {
      }
    },
    onCancel() {},
  });
}
async function deleteRequest(idxs) {
  return new Promise((resolve) => {
    new Api('/pas/custManagerLink/remove')
      .fetch(idxs, '', 'DELETE')
      .then((res) => {
        updateTable();
        resolve({});
      });
  });
}
function handleUserChange(selectUserData) {
  return new Promise((resolve, reject) => {
    const bodyParams = map(selectUserData, (col) => ({
      userId: get(col, 'id'),
      deptId: get(col, 'simpleUser.orgId'),
      deptName: get(col, 'simpleUser.orgName'),
      basePlaceId: route.params.id,
      basePlaceCode: detailsData?.code,
    }));
    new Api('/pas/custManagerLink/add').fetch(bodyParams, '', 'POST')
      .then((res) => {
        message.success('添加成功');
        updateTable();
        resolve({});
      });
  });
}
</script>

<template>
  <BasicCard
    title="客户经理"
    :isBorder="false"
  >
    <div class="account-manager">
      <OrionTable
        ref="tableRef"
        :options="tableOptions"
      >
        <template #toolbarLeft>
          <BasicButton
            v-if="showAddBtn"
            class="init-ant"
            type="primary"
            icon="sie-icon-tianjiaxinzeng"
            @click="handleAssociationRequirement"
          >
            添加
          </BasicButton>
          <BasicButton
            v-if="showBatchDeleteBtn"
            :disabled="!selectKeys.length"
            icon="sie-icon-del"
            @click="handleRemove"
          >
            移除
          </BasicButton>
        </template>
      </OrionTable>
    </div>
    <SelectUserModal
      @ok="handleUserChange"
      @register="modalRegister"
    />
  </BasicCard>
</template>

<style scoped lang="less">
.account-manager{
  height: 500px;
  overflow: hidden;
  :deep(.ant-basic-table){
    padding: 0 !important;
  }
}

</style>