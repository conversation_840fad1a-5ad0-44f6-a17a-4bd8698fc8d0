<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.springframework.cloud</groupId>
    <artifactId>spring-cloud-dependencies-parent</artifactId>
    <version>3.1.5</version>
    <relativePath></relativePath>
  </parent>
  <groupId>com.alibaba.cloud</groupId>
  <artifactId>spring-cloud-alibaba-dependencies</artifactId>
  <version>2021.0.6.2</version>
  <packaging>pom</packaging>
  <name>Spring Cloud Alibaba Dependencies</name>
  <description>Spring Cloud Alibaba Dependencies</description>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
      <comments>Copyright 2014-2021 the original author or authors.

				Licensed under the Apache License, Version 2.0 (the "License");
				you may not use this file except in compliance with the License.
				You may obtain a copy of the License at

				https://www.apache.org/licenses/LICENSE-2.0

				Unless required by applicable law or agreed to in writing, software
				distributed under the License is distributed on an "AS IS" BASIS,
				WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
				implied.

				See the License for the specific language governing permissions and
				limitations under the License.</comments>
    </license>
  </licenses>
  <properties>
    <maven-javadoc-plugin.version>3.1.1</maven-javadoc-plugin.version>
    <sentinel.version>1.8.6</sentinel.version>
    <maven-gpg-plugin.version>3.0.1</maven-gpg-plugin.version>
    <maven-source-plugin.version>3.2.1</maven-source-plugin.version>
    <flatten-maven-plugin.version>1.2.7</flatten-maven-plugin.version>
    <schedulerx.worker.version>1.11.4</schedulerx.worker.version>
    <revision>2021.0.6.2</revision>
    <nacos.client.version>2.2.0</nacos.client.version>
    <seata.version>1.6.1</seata.version>
    <spring.context.support.version>1.0.11</spring.context.support.version>
    <shedlock.version>4.23.0</shedlock.version>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.alibaba.nacos</groupId>
        <artifactId>nacos-client</artifactId>
        <version>${nacos.client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-core</artifactId>
        <version>${sentinel.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-parameter-flow-control</artifactId>
        <version>${sentinel.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-datasource-extension</artifactId>
        <version>${sentinel.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-datasource-apollo</artifactId>
        <version>${sentinel.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-datasource-zookeeper</artifactId>
        <version>${sentinel.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-datasource-nacos</artifactId>
        <version>${sentinel.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-datasource-redis</artifactId>
        <version>${sentinel.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-datasource-consul</artifactId>
        <version>${sentinel.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-web-servlet</artifactId>
        <version>${sentinel.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-spring-cloud-gateway-adapter</artifactId>
        <version>${sentinel.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-transport-simple-http</artifactId>
        <version>${sentinel.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-annotation-aspectj</artifactId>
        <version>${sentinel.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-reactor-adapter</artifactId>
        <version>${sentinel.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-cluster-server-default</artifactId>
        <version>${sentinel.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-cluster-client-default</artifactId>
        <version>${sentinel.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-spring-webflux-adapter</artifactId>
        <version>${sentinel.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-api-gateway-adapter-common</artifactId>
        <version>${sentinel.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-spring-webmvc-adapter</artifactId>
        <version>${sentinel.version}</version>
      </dependency>
      <dependency>
        <groupId>io.seata</groupId>
        <artifactId>seata-spring-boot-starter</artifactId>
        <version>${seata.version}</version>
      </dependency>
      <dependency>
        <groupId>net.javacrumbs.shedlock</groupId>
        <artifactId>shedlock-spring</artifactId>
        <version>${shedlock.version}</version>
      </dependency>
      <dependency>
        <groupId>net.javacrumbs.shedlock</groupId>
        <artifactId>shedlock-provider-jdbc-template</artifactId>
        <version>${shedlock.version}</version>
      </dependency>
      <dependency>
        <groupId>com.aliyun.schedulerx</groupId>
        <artifactId>schedulerx2-worker</artifactId>
        <version>${schedulerx.worker.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-alibaba-sentinel-datasource</artifactId>
        <version>2021.0.6.2</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-alibaba-sentinel-gateway</artifactId>
        <version>2021.0.6.2</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        <version>2021.0.6.2</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-circuitbreaker-sentinel</artifactId>
        <version>2021.0.6.2</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-starter-alibaba-seata</artifactId>
        <version>2021.0.6.2</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        <version>2021.0.6.2</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        <version>2021.0.6.2</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-starter-stream-rocketmq</artifactId>
        <version>2021.0.6.2</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-starter-bus-rocketmq</artifactId>
        <version>2021.0.6.2</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-starter-alibaba-sidecar</artifactId>
        <version>2021.0.6.2</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-alibaba-commons</artifactId>
        <version>2021.0.6.2</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-starter-alibaba-schedulerx</artifactId>
        <version>2021.0.6.2</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.spring</groupId>
        <artifactId>spring-context-support</artifactId>
        <version>${spring.context.support.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <profiles>
    <profile>
      <id>release</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-source-plugin</artifactId>
            <version>${maven-source-plugin.version}</version>
            <executions>
              <execution>
                <phase>package</phase>
                <goals>
                  <goal>jar-no-fork</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>${maven-javadoc-plugin.version}</version>
            <executions>
              <execution>
                <phase>package</phase>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>${maven-gpg-plugin.version}</version>
            <executions>
              <execution>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>flatten-maven-plugin</artifactId>
            <version>${flatten-maven-plugin.version}</version>
            <executions>
              <execution>
                <id>flatten</id>
                <phase>process-resources</phase>
                <goals>
                  <goal>flatten</goal>
                </goals>
              </execution>
              <execution>
                <id>flatten.clean</id>
                <phase>clean</phase>
                <goals>
                  <goal>clean</goal>
                </goals>
              </execution>
            </executions>
            <configuration>
              <updatePomFile>true</updatePomFile>
              <flattenMode>resolveCiFriendliesOnly</flattenMode>
            </configuration>
          </plugin>
        </plugins>
      </build>
      <distributionManagement>
        <repository>
          <id>sonatype-nexus-staging</id>
          <name>Nexus Release Repository</name>
          <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
        </repository>
        <snapshotRepository>
          <id>sonatype-nexus-snapshots</id>
          <name>Sonatype Nexus Snapshots</name>
          <url>https://oss.sonatype.org/content/repositories/snapshots/</url>
        </snapshotRepository>
      </distributionManagement>
    </profile>
  </profiles>
</project>
