package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.Boolean;
import java.lang.String;

/**
 * PersonTrainInfoRecord Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-22 16:21:02
 */
@TableName(value = "pmsx_person_train_info_record")
@ApiModel(value = "PersonTrainInfoRecordEntity对象", description = "用户培训信息落地")
@Data

public class PersonTrainInfoRecord extends  ObjectEntity  implements Serializable{

    /**
     * 培训编码
     */
    @ApiModelProperty(value = "培训编码：等于 train_key")
    @TableField(value = "train_number")
    private String trainNumber;
//    /**
//     * 培训标识
//     */
//    @ApiModelProperty(value = "培训标识")
//    @TableField(value = "train_key")
//    private String trainKey;

    /**
     * 培训名称
     */
    @ApiModelProperty(value = "培训名称")
    @TableField(value = "train_name")
    private String trainName;

    /**
     * 培训基地名称
     */
    @ApiModelProperty(value = "培训基地名称")
    @TableField(value = "base_name")
    private String baseName;

    /**
     * 培训基地编码
     */
    @ApiModelProperty(value = "培训基地编码")
    @TableField(value = "base_code")
    private String baseCode;

    /**
     * 培训课时
     */
    @ApiModelProperty(value = "培训课时")
    @TableField(value = "lesson_hour")
    private BigDecimal lessonHour;

    /**
     * 完成时间
     */
    @ApiModelProperty(value = "完成时间")
    @TableField(value = "end_date")
    private Date endDate;

    /**
     * 是否等效
     */
    @ApiModelProperty(value = "是否等效")
    @TableField(value = "is_equivalent")
    private Boolean isEquivalent;

    /**
     * 到期时间时间
     */
    @ApiModelProperty(value = "到期时间时间")
    @TableField(value = "expire_time")
    private Date expireTime;

    /**
     * 培训讲师
     */
    @ApiModelProperty(value = "培训讲师")
    @TableField(value = "train_lecturer")
    private String trainLecturer;

    /**
     * 培训内容
     */
    @ApiModelProperty(value = "培训内容")
    @TableField(value = "content")
    private String content;

    /**
     * 培训内容
     */
    @ApiModelProperty(value = "用户编号")
    @TableField(value = "user_code")
    private String userCode;

    @ApiModelProperty(value = "培训ID - 培训ID")
    @TableField(value = "source_id")
    private String sourceId;

}
