package com.chinasie.orion.domain.vo;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;

import java.util.List;
/**
 * PersonJobPostEqu VO对象
 *
 * <AUTHOR>
 * @since 2024-06-22 16:20:42
 */
@ApiModel(value = "PersonJobPostEquVO对象", description = "人员岗位等效记录落地")
@Data
public class PersonJobPostEquVO extends  ObjectVO   implements Serializable{

            /**
         * 基地编码
         */
        @ApiModelProperty(value = "基地编码")
        private String baseCode;


        /**
         * 基地名称
         */
        @ApiModelProperty(value = "基地名称")
        private String baseName;


        /**
         * 等效认定时间
         */
        @ApiModelProperty(value = "等效认定时间")
        @DateTimeFormat(value = "yyyy-MM-dd")
        @JsonFormat(pattern="yyyy-MM-dd")
        private Date equivalentDate;


        /**
         * 用户编号
         */
        @ApiModelProperty(value = "用户编号")
        private String userCode;


        /**
         * 岗位编号
         */
        @ApiModelProperty(value = "岗位编号")
        private String jobPostCode;


        /**
         * 被等效基地编号
         */
        @ApiModelProperty(value = "被等效的落地ID")
        private String formRecordId;
        @ApiModelProperty(value = "附件列表")
        private List<FileVO> fileVOList;
        @ApiModelProperty(value = "来源Id")
        private String sourceId;

}
