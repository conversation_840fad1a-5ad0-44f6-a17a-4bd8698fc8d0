package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * CostCenter Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-16 13:42:22
 */
@ApiModel(value = "CostCenterVO对象", description = "成本中心类")
@Data
public class CostCenterVO extends ObjectVO implements Serializable {

    /**
     * 成本中心名称
     */
    @ApiModelProperty(value = "成本中心名称")
    private String name;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


}
