package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * LaborCostAcceptanceAssessmentStandard DTO对象
 *
 * <AUTHOR>
 * @since 2024-10-29 19:13:30
 */
@ApiModel(value = "LaborCostAcceptanceAssessmentStandardDTO对象", description = "验收人力成本与审核标准关联")
@Data
@ExcelIgnoreUnannotated
public class LaborCostAcceptanceAssessmentStandardDTO extends  ObjectDTO   implements Serializable{

    /**
     * 验收单id
     */
    @ApiModelProperty(value = "验收单id")
    @ExcelProperty(value = "验收单id ", index = 0)
    private String acceptanceId;

    /**
     * 审核标准Id
     */
    @ApiModelProperty(value = "审核标准Id")
    @ExcelProperty(value = "审核标准Id ", index = 1)
    private String assessmentAtandardId;




}
