package com.chinasie.orion.constant;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.*;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/12/20:48
 * @description:
 */
@ApiModel(value = "JobStatusEnum对象", description = "作业状态枚举")
public enum JobStatusEnum implements Serializable {
     NPLN("NPLN","待准备",1)
    ,ASGN("ASGN","已分配",2)
    ,INPL("INPL","准备中",3)
    ,PLND("PLND","已准备",4)
    ,RPLN("RPLN","重新准备",5)
    ,APPV("APPV","已批准",6)
    ,SCHD("SCHD","已计划",7)


    ,RTW("RTW","已下达",8)
    ,WIP("WIP","执行中",9)

    ,CSR("CSR","工作完成",10)
    ,CPL("CPL","关闭",11)
    ,REJ("REJ","拒绝",12)

    ,PRWD("PRWD","已校核",13)
    ,TOTAL("TOTAL","工单总数",14)
    ,RESRAT("RESRAT","准备率",15)
    ,COMRAT("COMRAT","完成率",16)
    ,JICRAT("JICRAT","实施率",17)
    ,JPC("JPC", "作业准备数", 18)
    ,JIC("JIC", "作业实施数", 19)
    ,JC("JC", "作业完成数", 20)
    ,PWC("PWC", "计划作业数", 21)
    ,AWC("AWC", "新增作业数", 22)
    ,TWC("TWC", "总作业数", 23)
    ,SUM("SUM","合计",24)
    ;

    JobStatusEnum(String key, String name, Integer order) {
        this.key = key;
        this.order = order;
        this.name = name;
    }

    private String key;

    private String name;

    private Integer order;

    public Integer getOrder() {
        return order;
    }
    public String getKey() {
        return key;
    }

    public String getName() {
        return name;
    }


    public static List<String> getColumnList(){
        List<String> columnList = new ArrayList<>();
        for (JobStatusEnum value : JobStatusEnum.values()) {
            if(value.getOrder()<=13){
                columnList.add(value.getKey());
            }
        }
        return columnList;
    }

    public static List<String> phaseList(String key){
        Integer currentOrder = keyToOrder.get(key);
        if(Objects.isNull(currentOrder)){
            return  new ArrayList<>();
        }
        List<String> phaseList = new ArrayList<>();
        for (Map.Entry<Integer, String> integerStringEntry : orderToKey.entrySet()) {
            Integer order = integerStringEntry.getKey();
            if(currentOrder.compareTo(order)>=0){
                phaseList.add(integerStringEntry.getValue());
            }
        }
        return  phaseList;
    }

    public static Map<String,Integer> keyToOrder= new HashMap<>();
    public static Map<Integer,String> orderToKey= new HashMap<>();
    static {
        for (JobStatusEnum value : JobStatusEnum.values()) {
            keyToOrder.put(value.getKey(),value.getOrder());
            orderToKey.put(value.getOrder(),value.getKey());
        }
    }

    public static Map<String,String> keyName(){
        Map<String,String> keyToName= new HashMap<>();
        JobStatusEnum[] values = JobStatusEnum.values();
        for (JobStatusEnum value : values) {
            if(value.getOrder()<=13){
                keyToName.put(value.getKey(),value.getName());
            }
        }
        return  keyToName;
    }

    public static List<String> workPrepareAndImplement(){
        List<String> allKey = workPrepare();
        allKey.add(APPV.getKey());
        allKey.add(SCHD.getKey());
        allKey.add(RTW.getKey());
        allKey.add(WIP.getKey());
        return allKey;
    }

    public static List<String> workPrepareAndImplementAndClose(){
        List<String> allKey = new ArrayList<>();
        allKey.add(PRWD.getKey());
        return allKey;
    }



    public static List<String> workPrepare(){
        List<String> allKey = new ArrayList<>();
        allKey.add(NPLN.getKey());
        allKey.add(ASGN.getKey());
        allKey.add(INPL.getKey());
        allKey.add(PLND.getKey());
        allKey.add(RPLN.getKey());
        return  allKey;
    }



    public static List<String> prepareFinish(){
        List<String> allKey = new ArrayList<>();
        allKey.add(APPV.getKey());
        allKey.add(SCHD.getKey());
        allKey.add(RTW.getKey());
        allKey.add(WIP.getKey());
        allKey.add(CSR.getKey());
        allKey.add(CPL.getKey());
        allKey.add(REJ.getKey());
        return  allKey;
    }


    public static List<String> workImplement(){
        List<String> allKey = new ArrayList<>();
        allKey.add(RTW.getKey());
        allKey.add(WIP.getKey());
        return  allKey;
    }

    public static List<String> allPhase(){
        List<String> allPhase= new ArrayList<>();
        JobStatusEnum[] values = JobStatusEnum.values();
        for (JobStatusEnum value : values) {
            if(value.getOrder()<=13){
                allPhase.add(value.getKey());
            }
        }
        return  allPhase;
    }
    public static List<String> workClose(){
        List<String> allKey = new ArrayList<>();
        allKey.add(CSR.getKey());
        allKey.add(CPL.getKey());
        allKey.add(PLND.getKey());
        return  allKey;
    }
}
