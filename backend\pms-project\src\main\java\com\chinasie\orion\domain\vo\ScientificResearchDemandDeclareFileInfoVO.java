package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;

/**
 * ScientificResearchDemandDeclareFileInfo VO对象
 *
 * <AUTHOR>
 * @since 2023-11-23 11:37:06
 */
@ApiModel(value = "ScientificResearchDemandDeclareFileInfoVO对象", description = "科研需求申报文件信息")
@Data
public class ScientificResearchDemandDeclareFileInfoVO extends ObjectVO implements Serializable{
    /**
     * 文件id
     */
    @ApiModelProperty(value = "文件id")
    @TableField(value = "file_data_id")
    private String fileDataId;

    /**
     * 科研需求申报id
     */
    @ApiModelProperty(value = "科研需求申报id")
    @TableField(value = "declare_id")
    private String declareId;


    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    @TableField(value = "type")
    private String type;
}

