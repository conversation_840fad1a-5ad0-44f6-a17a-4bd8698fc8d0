<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <groupId>org.sonatype.spice</groupId>
    <artifactId>spice-parent</artifactId>
    <version>12</version>
  </parent>
  
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.sonatype.plexus</groupId>
  <artifactId>plexus-sec-dispatcher</artifactId>
  <url>http://spice.sonatype.org/${project.artifactId}</url>
  
  <name>Plexus Security Dispatcher Component</name>
  <version>1.3</version>
  
  <distributionManagement>
    <site>
      <id>sonatype.org-sites</id>
      <url>${spiceSiteBaseUrl}/${project.artifactId}</url>
    </site>
  </distributionManagement>
  
  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-maven-plugin</artifactId>
        <version>1.3.5</version>
        <executions>
          <execution>
            <goals>
              <goal>descriptor</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin
        </artifactId>
        <configuration>
          <source>1.4</source>
          <target>1.4</target>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.modello</groupId>
        <artifactId>modello-maven-plugin</artifactId>
        <configuration>
          <version>1.0.0</version>
          <models>
            <model>src/main/mdo/settings-security.mdo</model>
          </models>
        </configuration>
        <executions>
          <execution>
            <id>standard</id>
            <goals>
              <goal>java</goal>
              <goal>xpp3-reader</goal>
              <goal>xpp3-writer</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  
  <dependencies>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-utils</artifactId>
    </dependency>
    <dependency>
      <groupId>org.sonatype.plexus</groupId>
      <artifactId>plexus-cipher</artifactId>
      <version>1.4</version>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-container-default</artifactId>
      <version>1.0-alpha-9-stable-1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>3.8.2</version>
    </dependency>
  </dependencies>

  <scm>
    <connection>scm:svn:http://svn.sonatype.org/spice/tags/plexus-sec-dispatcher-1.3</connection>
    <developerConnection>scm:svn:https://svn.sonatype.org/spice/tags/plexus-sec-dispatcher-1.3</developerConnection>
    <url>http://svn.sonatype.org/spice/tags/plexus-sec-dispatcher-1.3</url>
  </scm>
</project>
