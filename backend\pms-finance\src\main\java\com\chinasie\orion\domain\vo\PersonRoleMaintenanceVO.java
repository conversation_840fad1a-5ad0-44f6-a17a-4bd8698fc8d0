package com.chinasie.orion.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.domain.entity.PersonRoleMaintenanceLog;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * PersonRoleMaintenance VO对象
 *
 * <AUTHOR>
 * @since 2024-10-08 17:28:59
 */
@ApiModel(value = "PersonRoleMaintenanceVO对象", description = "人员角色维护表")
@Data
public class PersonRoleMaintenanceVO extends  ObjectVO   implements Serializable{

    /**
     * 人员id
     */
    @ApiModelProperty(value = "人员id")
    private String id;


    /**
     * 专业中心
     */
    @ApiModelProperty(value = "专业中心")
    private String expertiseCenter;

    /**
     * 专业中心名称
     */
    @ApiModelProperty(value = "专业中心名称")
    private String expertiseCenterTitle;

    /**
     * 变更人姓名
     */
    @ApiModelProperty(value = "变更人姓名")
    private String changePersonName;

    /**
     * 变更时间
     */
    @ApiModelProperty(value = "变更时间")
    private String changeTime;


    /**
     * 专业所
     */
    @ApiModelProperty(value = "专业所")
    private String expertiseStation;

    /**
     * 专业所名称
     */
    @ApiModelProperty(value = "专业所名称")
    private String expertiseStationTitle;


    /**
     * 变更原因
     */
    @ApiModelProperty(value = "变更原因")
    private String changeReason;

    /**
     * 专业所人员
     */
    @ApiModelProperty(value = "专业所审核人员编码")
    @ExcelProperty(value = "专业所审核人员编码 ", index = 3)
    private String expertiseStationCode;

    /**
     * 专业中心人员
     */
    @ApiModelProperty(value = "专业中心审核人员编码")
    @ExcelProperty(value = "专业中心审核人员编码 ", index = 4)
    private String expertiseCenterCode;

    /**
     * 财务人员
     */
    @ApiModelProperty(value = "财务人员编码")
    @ExcelProperty(value = "财务人员编码 ", index = 5)
    private String financialStaffCode;

    /**
     * 专业所人员
     */
    @ApiModelProperty(value = "专业所审核人员姓名")
    @ExcelProperty(value = "专业所审核人员姓名 ", index = 3)
    private String expertiseStationName;

    /**
     * 专业中心人员
     */
    @ApiModelProperty(value = "中心审核人员姓名")
    @ExcelProperty(value = "中心审核人员姓名", index = 4)
    private String expertiseCenterName;

    /**
     * 财务人员
     */
    @ApiModelProperty(value = "财务人员姓名")
    @ExcelProperty(value = "财务人员姓名", index = 5)
    private String financialStaffName;

    /**
     * 变更人员记录
     */
    private List<PersonRoleMaintenanceLog> personRoleMaintenanceLogList;
}
