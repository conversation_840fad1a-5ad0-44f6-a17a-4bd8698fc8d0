package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.util.Date;
import java.lang.String;
import java.lang.Integer;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ContractMain DTO对象
 *
 * <AUTHOR>
 * @since 2024-10-17 09:42:35
 */
@ApiModel(value = "ContractMainDTO对象", description = "合同计划主表")
@Data
@ExcelIgnoreUnannotated
public class ContractMainDTO extends  ObjectDTO   implements Serializable{

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 0)
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称 ", index = 1)
    private String contractName;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @ExcelProperty(value = "年份 ", index = 2)
    private Date year;

    /**
     * 合同状态
     */
    @ApiModelProperty(value = "合同状态")
    @ExcelProperty(value = "合同状态 ", index = 3)
    private Integer contractSatus;

    @ApiModelProperty(value = "是否开启了下年录入")
    private Integer hasNext;




}
