package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.excel.JobProgressParamDTO;
import com.chinasie.orion.domain.entity.JobProgress;
import com.chinasie.orion.domain.dto.JobProgressDTO;
import com.chinasie.orion.domain.vo.JobProgressVO;
import java.lang.String;
import java.util.Date;
import java.util.List;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;
import java.util.Map;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * JobProgress 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-09 15:48:03
 */
public interface JobProgressService extends OrionBaseService<JobProgress> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    JobProgressVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param jobProgressDTO
     */
    String create(JobProgressDTO jobProgressDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param jobProgressDTO
     */
    Boolean edit(JobProgressDTO jobProgressDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<JobProgressVO> pages(Page<JobProgressDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(JobProgressParamDTO progressParamDTO, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<JobProgressVO> vos) throws Exception;


    Map<String, JobProgress> listByJobIdsAndDate(List<String> jobIdList, Date workDate);
}
