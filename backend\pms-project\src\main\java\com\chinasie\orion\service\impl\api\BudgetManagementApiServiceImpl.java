package com.chinasie.orion.service.impl.api;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.chinasie.orion.domain.entity.BudgetManagement;
import com.chinasie.orion.domain.vo.BudgetManagementVO;
import com.chinasie.orion.service.BudgetManagementApiService;
import com.chinasie.orion.service.BudgetManagementService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: lsy
 * @date: 2024/6/6
 * @description:
 */
@RestController
public class BudgetManagementApiServiceImpl implements BudgetManagementApiService {

    @Autowired
    private BudgetManagementService budgetManagementService;

    @Override
    public List<BudgetManagementVO> getBudgetVOByIds(List<String> budgetIdList) throws Exception {
        List<BudgetManagement> budgetManagementList = budgetManagementService.listByIds(budgetIdList);
        if (CollectionUtil.isNotEmpty(budgetManagementList)) {
            List<BudgetManagementVO> budgetManagementVOList = BeanCopyUtils.convertListTo(budgetManagementList, BudgetManagementVO::new);
            budgetManagementService.setEveryName(budgetManagementVOList);
            return budgetManagementVOList;
        }
        return CollUtil.toList();
    }
}
