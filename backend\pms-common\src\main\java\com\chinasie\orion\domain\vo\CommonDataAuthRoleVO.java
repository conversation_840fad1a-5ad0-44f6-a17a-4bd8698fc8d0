package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;
/**
 * CommonDataAuthRole VO对象
 *
 * <AUTHOR>
 * @since 2024-11-23 15:09:06
 */
@ApiModel(value = "CommonDataAuthRoleVO对象", description = "通用数据权限")
@Data
public class CommonDataAuthRoleVO extends  ObjectVO   implements Serializable{


        /**
         * 数据类型
         */
        @ApiModelProperty(value = "数据类型")
        private String dataType;


        /**
         * 数据ID
         */
        @ApiModelProperty(value = "数据ID")
        private String dataId;


        /**
         * 授权对象：Role:User
         */
        @ApiModelProperty(value = "授权对象：Role:User")
        private String authObject;


        @ApiModelProperty(value = "权限code：read,write")
        private String permissionCode;

        /**
         * 对象值： 如果对象为 role那么为 角色编码，如果是User那么值为用户ID
         */
        @ApiModelProperty(value = "对象值： 如果对象为 role那么为 角色编码，如果是User那么值为用户ID")
        private String objectValue;

        private String objectName;

        private String objectCode;


    

}
