<script setup lang="ts">
import { BasicDrawer, useDrawerInner } from 'lyra-component-vue3';
import { ref, Ref } from 'vue';
import dayjs from 'dayjs';
import FormMain from './FormMain.vue';
import Api from '/@/api';

const emits = defineEmits<{
  (e:'updateTable'):void
}>();

const [drawerRegister, { closeDrawer, changeOkLoading }] = useDrawerInner(async (openProps:any) => {
  detail.value = openProps;
  await getDetail();
  visibleDrawer.value = true;
});

const visibleDrawer:Ref<boolean> = ref(false);
const detail:Ref = ref({});
const formRef:Ref = ref();

// 获取详情
async function getDetail() {
  const result = await new Api('/pms/warning-setting/detail').fetch('', detail.value.id, 'GET');
  detail.value = {
    ...result,
    roleList: result?.roleList ?? [],
    time: result?.time ? dayjs(result.time, 'HH:mm') : '',
  };
}

function visibleChange(visible: boolean) {
  // 窗口关闭时，设置状态值
  !visible && (visibleDrawer.value = visible);
}
async function onOk() {
  const formValues = await formRef.value.validate();
  const params = {
    ...formValues,
    id: detail.value?.id,
    time: dayjs(formValues.time).format('HH:mm'),
  };
  changeOkLoading(true);
  try {
    await new Api('/pms/warning-setting/edit').fetch(params, '', 'PUT');
    emits('updateTable');
    closeDrawer();
  } finally {
    changeOkLoading(false);
  }
}

</script>

<template>
  <BasicDrawer
    v-bind="$attrs"
    title="预警设置"
    width="600px"
    :show-footer="true"
    :onVisibleChange="visibleChange"
    @register="drawerRegister"
    @ok="onOk"
  >
    <FormMain
      v-if="visibleDrawer"
      ref="formRef"
      :detail="detail"
    />
  </BasicDrawer>
</template>

<style scoped lang="less">

</style>
