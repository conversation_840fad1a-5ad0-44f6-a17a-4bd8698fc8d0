package com.chinasie.orion.domain.dto.resourceAllocation;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ResourceAllocationDTO {

    @ApiModelProperty(value = "年份")
    private String yearNum;
    @ApiModelProperty(value = "关键字")
    private String keyword;
    @ApiModelProperty(value = "大修轮次")
    private String repairRound;
    @ApiModelProperty(value = "查询类型")
    private String queryType;
    @ApiModelProperty(value = "状态")
    private String status;

}
