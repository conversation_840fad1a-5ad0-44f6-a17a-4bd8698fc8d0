<template>
  <BasicDrawer
    v-bind="$attrs"
    width="800px"
    showFooter
    :showCancelBtn="false"
    :showOkBtn="false"
    @register="register"
    @close="cancelClick('close')"
  >
    <template #footer>
      <DrawerFooterButtons
        v-model:checked="checked"
        :isContinue="cloneData[0]==='add'"
        :loading="loading"
        @cancelClick="cancelClick('close')"
        @okClick="okClick"
      />
    </template>

    <div>
      <BasicForm
        :layout="'vertical'"
        @register="registerForm"
      />
      <!--      <span-->
      <!--          v-if="!addMore"-->
      <!--          class="action-btn"-->
      <!--          @click="addMoreForm"-->
      <!--      >添加更多信息</span>-->
    </div>

    <SelectUserModal
      selectType="radio"
      @register="selectUserRegister"
    />
  </BasicDrawer>
</template>
<script lang="ts">
import {
  computed, defineComponent, h, onMounted, reactive, toRefs,
} from 'vue';
import {
  BasicDrawer,
  BasicForm,
  FormSchema, getDict,
  SelectUserModal,
  useDrawerInner,
  useForm,
  useModal,
} from 'lyra-component-vue3';
import { Button, Checkbox, message } from 'ant-design-vue';
import DrawerFooterButtons from '/@/views/pms/components/DrawerFooterButtons.vue';
import Api from '/@/api';

export default defineComponent({
  name: '',
  components: {
    BasicDrawer,
    BasicForm,
    SelectUserModal,
    DrawerFooterButtons,
  },
  props: {
    selectChangeData: {},
  },
  emits: ['addSuccess'],
  setup(props, { emit }) {
    const [selectUserRegister, { openModal: selectUserOpenModal }] = useModal();
    const state: any = reactive({
      cloneData: [],
      checked: undefined,
      loading: false,
      discernPerson: '',
      principalId: '',
      recipientId: '',
      exhibitor: '',
      addMore: false,
      dirction: '',
      appendForm: [],
      typeTree: [],
    });
    function resetData() {
      state.cloneData = [];
      state.checked = undefined;
      state.loading = false;
      state.addMore = false;
      state.discernPerson = null;
      state.principalId = null;
      state.exhibitor = null;
      state.recipientId = null;
      state.dirction = '';
      state.appendForm = [];
      // state.typeTree = [];
    }
    const schemas: FormSchema[] = [
      {
        field: 'name',
        component: 'Input',
        colProps: {
          span: 11,
        },
        label: '标题:',
        rules: [
          {
            required: true,
            trigger: 'blur',
            type: 'string',
          },
        ],
        componentProps: {
        },
      },
      {
        field: 'number',
        component: 'Input',
        label: '编号:',
        colProps: {
          span: 11,
          offset: 2,
        },
        rules: [
          // { required: true, trigger: 'blur' },
        ],
        componentProps: {
          disabled: true,
          placeholder: '风险转问题完成时自动生成编号',
        },
        defaultValue: '风险转问题完成时自动生成编号',
      },
      {
        field: 'dirId',
        component: 'Input',
        label: '路径:',
        colProps: {
          span: 11,
        },
        rules: [
          // { required: true, trigger: 'blur' },
        ],
        componentProps: {
          disabled: true,
          placeholder: '风险新增完成时自动生成编号',
        },
        defaultValue: '风险新增完成时自动生成编号',
      },
      {
        field: 'riskId',
        component: 'Input',
        label: '关联风险:',
        colProps: {
          span: 11,
          offset: 2,
        },
        componentProps: {
          disabled: true,
        },
      },
      {
        field: 'principalName',
        component: 'Input',
        label: '负责人',
        colProps: {
          span: 11,
        },
        required: true,
        componentProps: {
          addonAfter: h(
            'span',
            {
              // class: 'boxs_zkw',
              onClick: () => {
                selectUserOpenModal(true, {
                  async onOk(data) {
                    await F1.setFieldsValue({ principalName: data[0].name });
                    state.principalId = data[0].id;
                  },
                });
              },
            },
            '请选择',
          ),
          async onChange(value) {
            message.info('请选择');
            await F1.setFieldsValue({ principalName: '' });
            state.principalId = '';
          },
        },
      },
      {
        field: 'questionType',
        component: 'TreeSelect',
        label: '问题类型',
        colProps: {
          span: 11,
          offset: 2,
        },
        // rules: [{ type: 'string', required: true }],
        componentProps: {
          // api: () => new Api('/pas/demand-type/tree?keyword=').fetch('', '', 'GET'),
          // labelField: 'name',
          // valueField: 'id',
          treeData: computed(() => state.typeTree),
          fieldNames: {
            children: 'children',
            key: 'id',
            value: 'id',
            label: 'name',
          },
          async onChange(value) {
            if (!value) {
              if (state.appendForm?.length > 0) {
                state.appendForm.forEach((item) => {
                  F1.removeSchemaByFiled(item.number);
                });
              }
              return;
            }
            const res = await new Api(`/pas/risk-type-to-risk-type-attribute/list/${value}`).fetch('', '', 'GET');
            if (state.appendForm?.length > 0) {
              state.appendForm.forEach((item) => {
                F1.removeSchemaByFiled(item.number);
              });
            }
            if (res?.length > 0) {
              res.reverse().forEach((item, n) => {
                F1.appendSchemaByField(
                  {
                    field: item.number,
                    label: item.name,
                    component: item.type === '1' ? 'Input' : 'Select',
                    // required:item.require==='1'?true:false,
                    rules: [
                      {
                        required: item.require === '1',
                        trigger: 'blur',
                        type: item.type === '1' ? 'string' : item.type === '2' ? 'string' : 'array',
                      },
                    ],
                    colProps: {
                      span: 11,
                      offset: res?.length % 2 === 0 ? n % 2 === 0 ? 2 : 0 : n % 2 === 0 ? 0 : 2,
                    },
                    componentProps: {
                      mode: item.type === 3 ? 'multiple' : '',
                      options: item.options && item.options.split(';').map((item) => ({
                        lable: item,
                        value: item,
                        key: item,
                      })),
                    },
                  },
                  'questionType',
                );
              });
            }
            state.appendForm = res;
          },
        },
      },
      {
        field: '',
        component: 'Divider',
        label: '更多字段',
      },
      {
        field: 'content',
        component: 'InputTextArea',
        label: '问题内容:',
        colProps: {
          span: 24,
        },
        componentProps: {
          rows: 4,
          showCount: true,
          maxlength: 100,
        },
      },
      {
        field: 'questionSource',
        component: 'ApiSelect',
        label: '问题来源:',
        colProps: {
          span: 11,
        },
        componentProps: {
          api: () => getDict('dicte78ee7b5cd4a4b00b505a4e29d5f9bae'),
          labelField: 'description',
          valueField: 'value',
        },
      },
      {
        field: 'seriousLevel',
        component: 'ApiSelect',
        label: '严重程度:',
        colProps: {
          span: 11,
          offset: 2,

        },
        componentProps: {
          api: () => getDict('dictd76685dd08004d5cb29c8547410bf399'),
          labelField: 'description',
          valueField: 'value',
        },
      },
      {
        field: 'exhibitorName',
        component: 'Input',
        label: '提出人:',
        colProps: {
          span: 11,

        },
        componentProps: {
          addonAfter: h(
            'span',
            {
              onClick: () => {
                selectUserOpenModal(true, {
                  async onOk(data) {
                    await F1.setFieldsValue({ exhibitorName: data[0].name });
                    state.exhibitor = data[0].id;
                  },
                });
              },
            },
            '请选择',
          ),
          async onChange(value) {
            message.info('请选择');
            await F1.setFieldsValue({ exhibitorName: '' });
            state.exhibitor = '';
          },
        },
      },

      {
        field: 'proposedTime',
        component: 'DatePicker',
        label: '提出日期:',
        colProps: {
          span: 11,
          offset: 2,

        },
        componentProps: {
          style: { width: '100%' },
        },
      },
      {
        field: 'recipientName',
        component: 'Input',
        label: '接收人:',
        colProps: {
          span: 11,

        },
        componentProps: {
          addonAfter: h(
            'span',
            {
              onClick: () => {
                selectUserOpenModal(true, {
                  async onOk(data) {
                    await F1.setFieldsValue({ recipientName: data[0].name });
                    state.recipientId = data[0].id;
                  },
                });
              },
            },
            '请选择',
          ),
          async onChange(value) {
            message.info('请选择');
            await F1.setFieldsValue({ recipientName: '' });
            state.recipientId = '';
          },
        },
      },
      {
        field: 'predictEndTime',
        component: 'DatePicker',
        label: '期望完成时间:',
        colProps: {
          span: 11,
          offset: 2,

        },
        componentProps: {
          style: { width: '100%' },
        },
      },
      {
        field: 'copingStrategy',
        component: 'ApiSelect',
        label: '优先级:',
        colProps: {
          span: 11,

        },
        componentProps: {
          api: () => getDict('dictc56421e19b264d9c91394c48e447e4cb'),
          labelField: 'description',
          valueField: 'value',
        },
      },
      {
        field: 'schedule',
        component: 'InputNumber',
        label: '进度:',
        colProps: {
          span: 11,
          offset: 2,
        },
        componentProps: {
          max: 100,
          min: 0,
          style: { width: '100%' },
          onChange(value) {
            if (value > 100 || value < 0) {
              message.error('状态值只能取0 ~ 100之间');
            }
          },
          addonAfter: h(
            'span',
            {
            },
            '%',
          ),
        },
      },
    ];
    const [registerForm, F1] = useForm({
      schemas,
      showSubmitButton: false,
      showResetButton: false,
      showActionButtonGroup: false,

    });
    const [register, DrawerM] = useDrawerInner((data) => {
      // DrawerM.setDrawerProps({
      //   showCancelBtn: false,
      //   showOkBtn:false
      // })
      state.cloneData = JSON.parse(JSON.stringify(data));
      F1.setFieldsValue({ dirId: data[1]?.dirction });
      F1.setFieldsValue({ riskId: data[1]?.data.name });
      if (state.cloneData[0] === 'edit') {
        // if (state.cloneData[1].fileToolList?.length > 0) {
        //   state.cloneData[1].fileToolList = state.cloneData[1].fileToolList.map((item) => item.id);
        // }
        // F1.setFieldsValue(state.cloneData[1]);
      }
    });

    /**
     * @description: 提交确定
     * */
    async function okClick() {
      await F1.validate();
      state.loading = true;
      const fd = F1.getFieldsValue();
      // console.log('----- fd -----', fd);
      // // 如果格式不对,此处可以对请求参数进行拷贝并处理
      const params:any = JSON.parse(JSON.stringify(fd));
      if (params.principalName) {
        params.principalId = state.principalId;
      }
      if (params.exhibitorName) {
        params.exhibitor = state.exhibitor;
      }
      if (params.recipientName) {
        params.recipientId = state.recipientId;
      }
      if (state.appendForm?.length > 0) {
        const typeAttrValueDTOList = [];
        state.appendForm.forEach((item) => {
          // console.log("----- item -----", item)
          for (const itemKey in params) {
            // console.log("----- item.number===itemKey -----", item.number,itemKey)
            if (item.number === itemKey) {
              if (item.type === 3) {
                typeAttrValueDTOList.push({
                  id: item.id,
                  name: item.name,
                  typeId: item.typeId,
                  value: params[itemKey].join(';'),
                  attributeId: item.number,
                });
              } else {
                typeAttrValueDTOList.push({
                  id: item.id,
                  name: item.name,
                  typeId: item.typeId,
                  value: params[itemKey],
                  attributeId: item.number,
                });
              }
            }
          }
          delete params[item.number];
        });
        params.typeAttrValueDTOList = typeAttrValueDTOList;
      }

      if (params.dirId) {
        params.dirId = props.selectChangeData.id;
      }
      // if(!params?.parentId){
      //   params.parentId=0
      // }else{
      //   params.parentId=state.cloneData[1].data.id
      // }
      if (params?.number === '风险转问题完成时自动生成编号') {
        delete params.number;
      }
      delete params.riskId;
      if (state.cloneData[0] === 'add') {
        new Api(`/pas/risk-management/riskChangeQuestion/${state.cloneData[1].data.id}`).fetch([params], '', 'POST').then(() => {
          message.success('操作成功');
          cancelClick('ok');
          F1.setFieldsValue({ questionType: params?.questionType });
          emit('addSuccess');
        }).catch(() => {
          state.loading = false;
        });
      } else {
        // 如果格式不对或需要添加某些id,此处可以对请求参数进行处理
        // params.id = state.cloneData[1].id;
        new Api('/pas/risk-management/save').fetch(fd, '', 'PUT').then(() => {
          message.success('操作成功');
          cancelClick('ok');
          F1.setFieldsValue({ questionType: params?.questionType });
          F1.setFieldsValue({ riskId: state.cloneData[1].data.name });
          emit('addSuccess');
        }).catch(() => {
          state.loading = false;
        });
      }
    }

    /**
     * @description: 取消
     * */
    function cancelClick(type) {
      state.loading = false;
      if (type === 'close') {
        resetData();
        F1.resetFields();
        DrawerM.closeDrawer();
      } else if (!state.checked) {
        F1.resetFields();
        DrawerM.closeDrawer();
      } else {
        F1.resetFields();
        setTimeout(() => {
          F1.setFieldsValue({ dirId: state.cloneData[1]?.dirction });
          F1.setFieldsValue({ riskId: state.cloneData[1].data.name });
        });
      }
    }
    function addMoreForm() {
      state.addMore = !state.addMore;
    }
    onMounted(async () => {
      const res2 = await new Api('/pas/question-type/tree?keyword=').fetch('', '', 'GET');
      state.typeTree = res2;
    });
    return {
      ...toRefs(state),
      register,
      registerForm,
      okClick,
      cancelClick,
      addMoreForm,
      selectUserRegister,
    };
  },
});
</script>
