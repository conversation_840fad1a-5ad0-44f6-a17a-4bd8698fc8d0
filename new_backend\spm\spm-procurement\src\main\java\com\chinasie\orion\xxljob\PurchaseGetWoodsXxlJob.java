package com.chinasie.orion.xxljob;

import com.chinasie.orion.constant.MsgHandlerConstant;
import com.chinasie.orion.domain.entity.NcfFormPurchOrder;
import com.chinasie.orion.repository.NcfFormPurchOrderMapper;
import com.chinasie.orion.msc.api.MscBuildHandlerManager;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class PurchaseGetWoodsXxlJob {

    @Resource
    NcfFormPurchOrderMapper ncfFormPurchOrderMapper;

    @Resource
    MscBuildHandlerManager mscBuildHandlerManager;

    @XxlJob("orderNotifyJob")
    public void orderNotifyJob() {
        List<NcfFormPurchOrder> ncfFormPurchOrders = ncfFormPurchOrderMapper.selectByOrderNumber();
        ncfFormPurchOrders.forEach(item->{
            mscBuildHandlerManager.send(item, MsgHandlerConstant.NODE_RECEVING_NOTIFY);
        });
    }

}
