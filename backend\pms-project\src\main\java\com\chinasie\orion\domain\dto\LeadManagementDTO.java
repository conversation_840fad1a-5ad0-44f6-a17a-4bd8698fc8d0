package com.chinasie.orion.domain.dto;

import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * LeadManagement Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-21 18:45:58
 */
@ApiModel(value = "LeadManagementDTO对象", description = "线索管理")
@Data
public class LeadManagementDTO extends ObjectDTO implements Serializable{

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    /**
     * 线索名称
     */
    @ApiModelProperty(value = "线索名称")
    private String name;

    /**
     * 线索来源
     */
    @ApiModelProperty(value = "线索来源")
    private String sourceId;

    /**
     * 目录id
     */
    @ApiModelProperty(value = "目录id")
    private String dirId;

    /**
     * 提出人
     */
    @ApiModelProperty(value = "提出人")
    private String proposeId;

    /**
     * 提出日期
     */
    @ApiModelProperty(value = "提出日期")
    private Date proposeDate;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private String priority;

    /**
     * 线索内容
     */
    @ApiModelProperty(value = "线索内容")
    private String content;

    /**
     * 是否子线索
     */
    @ApiModelProperty(value = "是否子线索")
    private Integer childFlag;

    /**
     * 父级线索
     */
    @ApiModelProperty(value = "父级线索")
    private String parentId;

    /**
     * 线索类型
     */
    @ApiModelProperty(value = "线索类型")
    private String leadModel;

    /**
     * 责任中心
     */
    @ApiModelProperty(value = "责任中心")
    private String rspCenter;

    /**
     * 客户经理
     */
    @ApiModelProperty(value = "客户经理")
    private String accountManagerId;

    /**
     * 第一次分配说明
     */
    @ApiModelProperty(value = "第一次分配说明")
    private String firstRemark;

    /**
     * 第一次分配日期
     */
    @ApiModelProperty(value = "第一次分配日期")
    private Date firstDate;

    /**
     * 配合中心
     */
    @ApiModelProperty(value = "配合中心")
    private String cooperateCenter;

    /**
     * 责任工程师
     */
    @ApiModelProperty(value = "责任工程师")
    private String rspEngineer;

    /**
     * 第二次分配说明
     */
    @ApiModelProperty(value = "第二次分配说明")
    private String secondRemark;

    /**
     * 第二次分配时间
     */
    @ApiModelProperty(value = "第二次分配时间")
    private Date secondDate;

    /**
     * 作废说明
     */
    @ApiModelProperty(value = "作废说明")
    private String invalidRemark;


    /**
     * 线索ids
     */
    @ApiModelProperty(value = "线索ids")
    private List<String> ids;

}
