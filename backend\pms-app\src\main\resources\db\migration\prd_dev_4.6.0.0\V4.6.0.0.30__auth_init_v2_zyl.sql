-- 项目申报
delete from pmi_container where id = 'exxr1858435099589701632';
INSERT INTO `pmi_container`(`id`, `name`, `page_id`, `parent_id`, `code`, `path`, `label`, `sort`, `role_permissions`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_id`) VALUES ('exxr1858435099589701632', '页面顶部', 'xaxe9881c76c1f644858b3c14d97fac8ef0b', NULL, 'PMS_XMSB_container_details_top', NULL, 'ye mian ding bu', NULL, NULL, 'Container', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-11-18 17:00:30', '314j1000000000000000000', '2024-11-18 17:00:30', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, NULL);

delete from pmi_button where id in( 'bqeo1858435162294546432' , 'bqeo1858435195333079040');
INSERT INTO `pmi_button`(`id`, `name`, `container_id`, `code`, `label`, `icon`, `sort`, `path`, `parent_id`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `row_button`) VALUES ('bqeo1858435162294546432', '编辑', 'exxr1858435099589701632', 'PMS_XMSB_container_details_top_edit', '1', 'fa-th-large', NULL, NULL, NULL, 'Button', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-11-18 17:00:45', '314j1000000000000000000', '2024-11-18 17:00:45', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, NULL);
INSERT INTO `pmi_button`(`id`, `name`, `container_id`, `code`, `label`, `icon`, `sort`, `path`, `parent_id`, `class_name`, `status`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `row_button`) VALUES ('bqeo1858435195333079040', '发起流程', 'exxr1858435099589701632', 'PMS_XMSB_container_details_top_process', '1', 'fa-th-large', NULL, NULL, NULL, 'Button', 1, '314j1000000000000000000', '314j1000000000000000000', '2024-11-18 17:00:53', '314j1000000000000000000', '2024-11-18 17:00:53', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, NULL);