package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * PmsJobPostRequirement Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-06 10:19:25
 */
@TableName(value = "pms_job_post_requirement")
@ApiModel(value = "PmsJobPostRequirementEntity对象", description = "岗位要求")
@Data

public class PmsJobPostRequirement extends  ObjectEntity  implements Serializable{

    /**
     * 岗位授权管理Id
     */
    @ApiModelProperty(value = "岗位Id")
    @TableField(value = "job_post_id")
    private String jobPostId;

    /**
     * 要求类型
     */
    @ApiModelProperty(value = "要求类型")
    @TableField(value = "type")
    private String type;

    /**
     * 要求名称
     */
    @ApiModelProperty(value = "要求名称")
    @TableField(value = "name")
    private String name;

    /**
     * 应取得证书
     */
    @ApiModelProperty(value = "应取得证书")
    @TableField(value = "certificate_number")
    private String certificateNumber;
    /**
     * 应取得证书
     */
    @ApiModelProperty(value = "应取得证书")
    @TableField(value = "certificate_id")
    private String certificateId;
    /**
     * 应通过培训
     */
    @ApiModelProperty(value = "应通过培训")
    @TableField(value = "train_number")
    private String trainNumber;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

}
