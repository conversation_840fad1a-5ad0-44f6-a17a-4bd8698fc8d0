<script setup lang="ts">
import {
  computed, h, inject, ref, onMounted, Ref, watchEffect,
} from 'vue';
import {
  openDrawer,
  BasicButton,
  openModal as openModalNew,
  DataStatusTag, isPower, Layout, OrionTable, BasicImport, useModal, downloadByData, openTreeSelectModal,
} from 'lyra-component-vue3';
import { message, Modal, Tag } from 'ant-design-vue';
import { useRouter, useRoute } from 'vue-router';
import ModalAdd from './components/ModalAdd.vue';
import ModalProductAdd from './components/ModalProductAdd.vue';
import DrawerAdd from './components/DrawerAdd.vue';
import dayjs from 'dayjs';
import {
  commit,
  page,
  edit,
  affirm,
  reject,
  add,
  remove,
  importExcel,
  importExcelCancel,
  importExcelCheck,
  qualityItemDelete,
  complete,
} from '/@/views/pms/api/qualityItem';
import { getExecuteItem } from '/@/views/pms/projectLibrary/pages/components/qualityControlItem/utill';
import Api from '/@/api';
import DocumentDrawer
  from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/docManagementFix/ManegeComponents/DocumentDrawer.vue';
import { roleTableColumns } from '/@/views/pms/components/associationAlter/tableColumns';

// const heardPower:Ref<string[]> = ref([]);
const projectId: string = inject('projectId');
const router = useRouter();
const route = useRoute();
const downloadFileObj = {
  url: '/pms/qualityItem/download/excel/tpl',
  method: 'GET',
};
const fileTypeList = [
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-excel.sheet.macroEnabled.12',
];
const qualityControlItemMessageId = route.query.qualityControlItemMessageId;
const [registerModal, { openModal }] = useModal();
const powerData = inject('powerData', []);

const searchConditions = ref([]);
const powerCode = {
  pageCode: 'PMS0004',
  headContainerCode: 'PMS_XMXQ_container_17_01',
  containerCode: 'PMS_XMXQ_container_17_02',

  headAdd: 'PMS_XMXQ_container_17_01_button_01', // 引用创建
  headSubmit: 'PMS_XMXQ_container_17_01_button_02', // 提交审批
  headConfirm: 'PMS_XMXQ_container_17_01_button_03', // 确认
  headReject: 'PMS_XMXQ_container_17_01_button_04', // 驳回
  headComplete: 'PMS_XMXQ_container_17_01_button_05', // 完成确认
  headExport: 'PMS_XMXQ_container_17_01_button_06', // 导出
  headDelete: 'PMS_XMXQ_container_17_01_button_07', // 删除
  headCreateDocument: 'PMS_XMXQ_container_17_01_button_08', // 创建文档
  headProductAdd: 'PMS_XMXQ_container_17_01_button_09', // 关联产品

  containerView: 'PMS_XMXQ_container_17_02_button_01', // 查看
  containerComplete: 'PMS_XMXQ_container_17_02_button_02', // 完成确认
  containerConfirm: 'PMS_XMXQ_container_17_02_button_03', // 确认
  containerReject: 'PMS_XMXQ_container_17_02_button_04', // 驳回
  containerEdit: 'PMS_XMXQ_container_17_02_button_05', // 编辑
  containerDelete: 'PMS_XMXQ_container_17_02_button_06', // 删除
  containerSubmit: 'PMS_XMXQ_container_17_02_button_07', // 提交
};
const tableRef = ref(null);

const columns = [
  {
    title: '质控项编码',
    dataIndex: 'number',
  },
  {
    title: '质控点',
    dataIndex: 'point',
    customRender({
      record, text,
    }) {
      if (isPower(powerCode.containerView, record.rdAuthList)) {
        return h('span', {
          class: 'action-btn',
          onClick() {
            router.push({
              name: 'QualityControlItemDetail',
              params: {
                id: record.id,
              },
            });
          },
        }, text);
      }
      return text;
    },
  },
  {
    title: '控制方案',
    dataIndex: 'scheme',
    minWidth: 260,
  },
  // {
  //   title: '关联产品编码',
  //   dataIndex: 'productNumber',
  //   minWidth: 260,
  // },
  {
    title: '状态',
    dataIndex: 'dataStatus',
    customRender: ({ record }) => h(DataStatusTag, { statusData: record.dataStatus }),
  },
  {
    title: '执行情况',
    dataIndex: 'execute',
    customRender: ({ text }) => {
      const { color, child } = getExecuteItem(text);
      return h(Tag, { color }, child);
    },
  },
  {
    title: '是否关联项目计划',
    dataIndex: 'relevanceScheme',
    customRender: ({ text }) => (text ? '关联' : '未关联'),
  },
  {
    title: '质控措施类型',
    dataIndex: 'typeName',
  },
  {
    title: '质控阶段',
    dataIndex: 'stage',
  },
  {
    title: '过程',
    dataIndex: 'processName',
  },
  {
    title: '担当人',
    dataIndex: 'responsiblePersonName',
  },
  {
    title: '必填/选填',
    dataIndex: 'requiredName',
  },
  {
    title: '质控活动',
    dataIndex: 'activityName',
  },
  {
    title: '负责人',
    dataIndex: 'resPersonName',
  },
  {
    title: '完成确认人',
    dataIndex: 'affirmName',
  },
  {
    title: '创建人',
    dataIndex: 'creatorName',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    type: 'dateTime',
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    slots: { customRender: 'action' },
  },
];
const actions = [
  {
    text: '完成确认',
    // 执行情况为已完成(2)且状态为已发布(130)
    isShow: (record) => record.execute === 2 && record.status === 130 && isPower(powerCode.containerComplete, record.rdAuthList),
    onClick(record) {
      funComplete([record.id]);
    },
  },
  {
    text: '确认',
    isShow: (record) => record.status === 110 && isPower(powerCode.containerConfirm, record.rdAuthList),
    onClick(record) {
      funConfirm([record.id]);
    },
  },
  {
    text: '驳回',
    isShow: (record) => record.status === 110 && isPower(powerCode.containerReject, record.rdAuthList),
    onClick(record) {
      funReject([record.id]);
    },
  },
  {
    text: '编辑',
    isShow: (record) => record.status === 120 && isPower(powerCode.containerEdit, record.rdAuthList),
    async onClick(record) {
      handleEdit(record.id);
    },
  },
  {
    text: '删除',
    isShow: (record) => record.status === 120 && isPower(powerCode.containerDelete, record.rdAuthList),
    async modal(record) {
      await qualityItemDelete(record.id);
      message.success('删除成功');
      tableRef.value.reload();
    },
  },
  {
    text: '提交',
    isShow: (record) => record.status === 120 && isPower(powerCode.containerSubmit, record.rdAuthList),
    onClick(record) {
      handleCommonSubmit([record.id]);
    },
  },
];
const selectedRowKeys = ref([]);
const selectedRow = ref([]);
// 只有状态为已发布执行情况为已完成完成确认按钮才不置灰
const disabledCompleteSure = computed(() => {
  let itemStatusRowData = selectedRow.value;
  let itemExecuteRowData = selectedRow.value;
  itemStatusRowData.map((item) => item?.status);
  itemExecuteRowData.map((item) => item?.execute);
  let hasStatus = itemStatusRowData.map((item) => item?.status).some((item) => item === 130);
  let hasExecute = itemExecuteRowData.map((item) => item?.execute).some((item) => item === 2);
  return !(hasStatus && hasExecute);
});

const baseTableOption = {
  rowKey: 'id',
  // 是否显示工具栏默认按钮
  showToolButton: true,
  settingCode: 'PMS_PROJECT_QUALITYCONTROLITEM',
  // 删除默认的部分按钮'String'用'|'隔开, 如 'add|delete|enable|disable'
  // 权限配置使用计算属性
  deleteToolButton: computed(() => {
    let str = 'add|enable|disable';
    // if (!heardPower.value.includes(powerCode.headDelete)) str += '|delete';
    return str;
  }),
  rowSelection: {
    selectedRowKeys,
    selectedRow,
    onChange(keys: string[], rows: Record<string, any>[]) {
      selectedRowKeys.value = keys;
      selectedRow.value = rows;
    },
  },
  columns,
  isFilter2: true,
  filterConfig: {
    fields: [
      {
        field: 'stage',
        fieldName: '质控阶段',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterface: null,
        referenceInterfaceMethod: null,
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        fieldNames: null,
        searchFieldName: null,
      },
      {
        field: 'type',
        fieldName: '质控措施类型',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterface: '/pmi/dict-value/v2/quality_type',
        referenceInterfaceMethod: 'GET',
        referenceInterfaceParams: null,
        component: 'Select',
        hidden: false,
        constValue: null,
        fieldNames: '{"name":"description","value":"number"} ',
        searchFieldName: null,
      },
      {
        field: 'process',
        fieldName: '过程',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterface: '/pmi/dict-value/v2/quality_process',
        referenceInterfaceMethod: 'GET',
        referenceInterfaceParams: null,
        component: 'Select',
        hidden: false,
        constValue: null,
        fieldNames: '{"name":"description","value":"number"} ',
        searchFieldName: null,
      },
      {
        field: 'activity',
        fieldName: '质控活动',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterface: '/pmi/dict-value/v2/quality_activity',
        referenceInterfaceMethod: 'GET',
        referenceInterfaceParams: null,
        component: 'Select',
        hidden: false,
        constValue: null,
        fieldNames: '{"name":"description","value":"number"} ',
        searchFieldName: null,
      },
      {
        field: 'responsiblePerson',
        fieldName: '担当人',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterface: '/pmi/role/list/module/PMS',
        referenceInterfaceMethod: 'GET',
        referenceInterfaceParams: null,
        component: 'Select',
        hidden: false,
        constValue: null,
        fieldNames: '{"name":"name","value":"id"} ',
        searchFieldName: null,
      },
      {
        field: 'required',
        fieldName: '必填/选填',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterface: '/pmi/dict-value/v2/quality_required',
        referenceInterfaceMethod: 'GET',
        referenceInterfaceParams: null,
        component: 'Select',
        hidden: false,
        constValue: null,
        fieldNames: '{"name":"description","value":"number"} ',
        searchFieldName: null,
      },
    ],
  },
  filterConfigName: 'PAS_QUALITY_CONTROL_PROGRAM_LIBRARY',
  // 是否显示工具栏上的搜索
  showSmallSearch: true,
  // 工具栏搜索字段配置，string | string[] 默认 'name' , 传数组字段值则查询多个子字段
  smallSearchField: ['point', 'number'],
  api: (params) => {
    params.power = {
      pageCode: powerCode.pageCode,
      headContainerCode: powerCode.headContainerCode,
      containerCode: powerCode.containerCode,
    };
    params.query = {
      projectId,
      messageId: qualityControlItemMessageId,
    };
    searchConditions.value = params?.searchConditions || [];
    return page(params).then((res) => res);
  },
  actions,
  // 批量删除自定义api，有特殊情况才使用, data={ids:'选中的id组','选中的原始数据'}
  batchDeleteApi: async ({ ids }) => {
    await remove(ids);
  },

};

const handleAdd = () => {
  const refDrawer = ref();
  openModalNew({
    title: '引入质量管控项',
    width: 1100,
    height: 700,
    content(h) {
      return h(ModalAdd, {
        ref: refDrawer,
        projectId,
      });
    },
    async onOk() {
      const { isSelectedAndGetData } = refDrawer.value;
      const values = await isSelectedAndGetData();
      await add(projectId, values);
      tableRef.value.reload();
    },
  });
};
// const handleProductAdd = () => {
//   const refModalProductAdd = ref();
//   openModalNew({
//     title: '关联产品',
//     width: 400,
//     height: 300,
//     content(h) {
//       return h(ModalProductAdd, {
//         ref: refModalProductAdd,
//         projectId,
//         // productNumberArr: tableRef.value.getSelectRows()[0].productNumber?.split(','),
//         productNumberArr: [],
//         qualityItemIds: [...tableRef.value.getSelectRowKeys()],
//       });
//     },
//     async onOk() {
//       await refModalProductAdd.value.submit();
//       tableRef.value.reload();
//     },
//   });
// };
const handleEdit = (id) => {
  const refDrawer = ref();
  openDrawer({
    title: '编辑质控措施',
    width: 900,
    content(h) {
      return h(DrawerAdd, {
        ref: refDrawer,
        id,
      });
    },
    async onOk() {
      const { formMethods: { validate }, getDataDetail } = refDrawer.value;
      const values = await validate();
      const dataDetail = getDataDetail();

      await edit({
        ...dataDetail,
        ...values,
      });
      tableRef.value.reload();
    },
  });
};

const handleExport = () => {
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk() {
      const url = `/pms/qualityItem/export/excel?projectId=${projectId}`;
      const msg = '导出处理完成，现在开始下载';
      downloadByData(url, searchConditions.value, '', 'POST', true, false, msg);
    },
  });
};

// 导入校验
const requestBasicImport = async (data) => {
  let formData = new FormData();
  formData.append('file', data[0]);
  return importExcelCheck(formData);
};
// 数据导入
const requestSuccessImport = (successKey) => importExcel(successKey);
// 取消导入
const changeImportModalFlag = async (data) => {
  if (!data.visible && data.successImportFlag) {
    // 导入成功刷新数据
    tableRef.value.reload();
  } else if (!data.visible && !data.successImportFlag && data.succ) {
    await importExcelCancel(data.succ);
    message.success('取消导入成功');
  }
};
// 检查是否选中数据
const isSelectedAndGetData = () => new Promise((resolve, reject) => {
  const keys = tableRef.value.getSelectRowKeys();
  if (keys.length > 0) {
    resolve(keys);
  } else {
    message.warning('请选择数据');
    reject();
  }
});

// 公共提交方法
const handleCommonSubmit = (ids) => {
  Modal.confirm({
    title: '提交确认',
    content: '请确认是否提交勾选数据',
    async onOk() {
      await commit(ids);
      tableRef.value.reload();
    },
  });
};
// 提交审批
const handleSubmit = async () => {
  const keys = await isSelectedAndGetData();
  handleCommonSubmit(keys);
};
const funConfirm = (ids) => {
  Modal.confirm({
    title: '确认审核',
    content: '请确定是否确认勾选数据',
    async onOk() {
      await affirm(ids);
      tableRef.value.reload();
    },
  });
};
// 确认
const handleConfirm = async () => {
  const keys = await isSelectedAndGetData();
  funConfirm(keys);
};
const funReject = async (keys) => {
  Modal.confirm({
    title: '驳回确认',
    content: '请确定是否驳回勾选数据',
    async onOk() {
      await reject(keys);
      tableRef.value.reload();
    },
  });
};
// 驳回
const handleReject = async () => {
  const keys = await isSelectedAndGetData();
  await funReject(keys);
};
const funComplete = (ids) => {
  Modal.confirm({
    title: '完成确认',
    content: '请确定勾选数据是否完成',
    async onOk() {
      const data = {
        ids,
        completionStatement: null,
      };
      await complete(data);
      tableRef.value.reload();
    },
  });
};
// 完成确认
const handleComplete = async () => {
  const keys = await isSelectedAndGetData();
  funComplete(keys);
};
onMounted(() => {
  // getHeardPower();
});
// function getHeardPower() {
//   let params = {
//     pageNum: 1,
//     pageSize: 20,
//     power: {
//       pageCode: 'PMS0004',
//       headContainerCode: 'PMS_XMXQ_container_17_01',
//       containerCode: 'PMS_XMXQ_container_17_02',
//     },
//     query: { projectId },
//   };
//   new Api('/pms').fetch(params, 'qualityItem/pagesAuth', 'post').then((res) => {
//     heardPower.value = res;
//   });
// }
function createDocument() {
  openTreeSelectModal({
    title: '选择模板',
    selectType: 'radio',
    treeApi() {
      return new Api('/res/bookmark-Document-TemplateType/tree').fetch('', '', 'GET');
    },
    tableApi(params: Record<string, any>) {
      let newParams = {
        searchConditions: params.searchConditions || [],
        status: 'open',
        pageNum: params.pageNum,
        pageSize: params.pageSize,
      };
      return new Api(`/res/bookmark-Document-Template/pages/${params.node?.id}`).fetch(newParams, '', 'POST');
    },
    columns: [
      {
        title: '模板编号',
        dataIndex: 'number',
        width: 180,
      },
      {
        title: '模板名称',
        dataIndex: 'name',
        minWidth: 220,
        slots: { customRender: 'name' },
      },
      {
        title: '类型',
        dataIndex: 'typeName',
        width: '150px',
      },
      {
        title: '模板标识',
        dataIndex: 'mark',
        width: '170px',
      },
      {
        title: '版本',
        dataIndex: 'revId',
        width: '170px',
      },
      {
        title: '状态',
        dataIndex: 'dataStatus',
        width: '100px',
        customRender({ record }) {
          return record.dataStatus ? h(DataStatusTag, {
            statusData: record.dataStatus,
          }) : '';
        },
      },
      {
        title: '创建人',
        dataIndex: 'creatorName',
        width: '100px',
      },
      {
        title: '发布时间',
        align: 'left',
        dataIndex: 'createTime',
        width: 180,
        customRender({ text }) {
          return h('div', {
            title: text ? dayjs(text)
              .format('YYYY-MM-DD HH:mm:ss') : '',
          }, text ? dayjs(text)
            .format('YYYY-MM-DD HH:mm:ss') : '');
        },
      },
      {
        title: '修改时间',
        align: 'left',
        dataIndex: 'modifyTime',
        width: 180,
        customRender({ text }) {
          return h('div', {
            title: text ? dayjs(text)
              .format('YYYY-MM-DD HH:mm:ss') : '',
          }, text ? dayjs(text)
            .format('YYYY-MM-DD HH:mm:ss') : '');
        },
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 180,
        align: 'left',
        fixed: 'right',
        slots: { customRender: 'action' },
      },
    ],
    async onOk(option: Record<string, any>) {
      const params = {
        projectId,
        templateId: option.tableData[0].id,
      };
      // const url = `/pms/qualityItem/export/getDocument?projectId=${projectId}&templateId=${option.tableData[0].id}`;
      // const msg = '导出处理完成，现在开始下载';
      // downloadByData(url, params, '', 'POST', true, false, msg);
      const res = await new Api(`/pms/qualityItem/export/getDocument?projectId=${projectId}&templateId=${option.tableData[0].id}`).fetch(params, '', 'POST');
      if (res) {
        message.success('新增成功');
        tableRef.value.reload();
      }
    },
  });
}
</script>

<template>
  <Layout :options="{ body: { scroll: true } }">
    <OrionTable
      ref="tableRef"
      :options="baseTableOption"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="isPower(powerCode.headAdd,powerData)"
          type="primary"
          icon="add"
          @click="handleAdd"
        >
          添加质控项
        </BasicButton>
        <!--        <BasicButton-->
        <!--          v-if="isPower(powerCode.headProductAdd,powerData)"-->
        <!--          icon="add"-->
        <!--          :disabled="selectedRowKeys.length===0"-->
        <!--          @click="handleProductAdd"-->
        <!--        >-->
        <!--          关联产品-->
        <!--        </BasicButton>-->
        <BasicButton
          v-if="isPower(powerCode.headSubmit,powerData)"
          icon="fa-check-square-o"
          @click="handleSubmit"
        >
          提交审批
        </BasicButton>
        <BasicButton
          v-if="isPower(powerCode.headConfirm,powerData)"
          icon="sie-icon-qiyong"
          @click="handleConfirm"
        >
          确认
        </BasicButton>
        <BasicButton
          v-if="isPower(powerCode.headReject,powerData)"
          icon="fa-mail-reply"
          @click="handleReject"
        >
          驳回
        </BasicButton>
        <BasicButton
          v-if="isPower(powerCode.headComplete,powerData)"
          :disabled="disabledCompleteSure"
          icon="fa-check-square-o"
          @click="handleComplete"
        >
          完成确认
        </BasicButton>
        <BasicButton
          v-if="isPower(powerCode.headExport,powerData)"
          icon="sie-icon-daochu"
          @click="handleExport"
        >
          导出
        </BasicButton>
        <BasicButton
          v-if="isPower(powerCode.headCreateDocument,powerData)"
          @click="createDocument"
        >
          创建文档
        </BasicButton>
      </template>
    </OrionTable>
    <!-- 导入 -->
    <BasicImport
      :requestBasicImport="requestBasicImport"
      :requestSuccessImport="requestSuccessImport"
      :downloadFileObj="downloadFileObj"
      :fileTypeList="fileTypeList"
      @register="registerModal"
      @change-import-modal-flag="changeImportModalFlag"
    />
  </Layout>
</template>

<style scoped lang="less">

</style>
