package com.chinasie.orion.management.service.impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.management.domain.dto.PurchaseAppWbsWbsDTO;
import com.chinasie.orion.management.domain.entity.PurchaseAppWbsWbs;
import com.chinasie.orion.management.domain.vo.PurchaseAppWbsWbsVO;
import com.chinasie.orion.management.repository.PurchaseAppWbsWbsMapper;
import com.chinasie.orion.management.service.PurchaseAppWbsWbsService;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <p>
 * PurchaseAppWbsWbs 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@Service
@Slf4j
public class PurchaseAppWbsWbsServiceImpl extends OrionBaseServiceImpl<PurchaseAppWbsWbsMapper, PurchaseAppWbsWbs> implements PurchaseAppWbsWbsService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public PurchaseAppWbsWbsVO detail(String id, String pageCode) throws Exception {
        PurchaseAppWbsWbs purchaseAppWbsWbs = this.getById(id);
        PurchaseAppWbsWbsVO result = BeanCopyUtils.convertTo(purchaseAppWbsWbs, PurchaseAppWbsWbsVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param purchaseAppWbsWbsDTO
     */
    @Override
    public String create(PurchaseAppWbsWbsDTO purchaseAppWbsWbsDTO) throws Exception {
        PurchaseAppWbsWbs purchaseAppWbsWbs = BeanCopyUtils.convertTo(purchaseAppWbsWbsDTO, PurchaseAppWbsWbs::new);
        this.save(purchaseAppWbsWbs);

        String rsp = purchaseAppWbsWbs.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param purchaseAppWbsWbsDTO
     */
    @Override
    public Boolean edit(PurchaseAppWbsWbsDTO purchaseAppWbsWbsDTO) throws Exception {
        PurchaseAppWbsWbs purchaseAppWbsWbs = BeanCopyUtils.convertTo(purchaseAppWbsWbsDTO, PurchaseAppWbsWbs::new);

        this.updateById(purchaseAppWbsWbs);

        String rsp = purchaseAppWbsWbs.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<PurchaseAppWbsWbsVO> pages(String mainTableId, Page<PurchaseAppWbsWbsDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<PurchaseAppWbsWbs> condition = new LambdaQueryWrapperX<>(PurchaseAppWbsWbs.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(PurchaseAppWbsWbs::getCreateTime);

        condition.eq(PurchaseAppWbsWbs::getMainTableId, mainTableId);

        Page<PurchaseAppWbsWbs> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), PurchaseAppWbsWbs::new));

        PageResult<PurchaseAppWbsWbs> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<PurchaseAppWbsWbsVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<PurchaseAppWbsWbsVO> vos = BeanCopyUtils.convertListTo(page.getContent(), PurchaseAppWbsWbsVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<PurchaseAppWbsWbsVO> getByCode(Page<PurchaseAppWbsWbsDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<PurchaseAppWbsWbs> condition = new LambdaQueryWrapperX<>(PurchaseAppWbsWbs.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(PurchaseAppWbsWbs::getCreateTime);
        Page<PurchaseAppWbsWbs> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), PurchaseAppWbsWbs::new));

        PageResult<PurchaseAppWbsWbs> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<PurchaseAppWbsWbsVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<PurchaseAppWbsWbsVO> vos = BeanCopyUtils.convertListTo(page.getContent(), PurchaseAppWbsWbsVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "采购立项WBS信息导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", PurchaseAppWbsWbsDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        PurchaseAppWbsWbsExcelListener excelReadListener = new PurchaseAppWbsWbsExcelListener();
        EasyExcel.read(inputStream, PurchaseAppWbsWbsDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<PurchaseAppWbsWbsDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("采购立项WBS信息导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<PurchaseAppWbsWbs> purchaseAppWbsWbses = BeanCopyUtils.convertListTo(dtoS, PurchaseAppWbsWbs::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("ncf::PurchaseAppWbsWbs-import::id", importId, purchaseAppWbsWbses, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<PurchaseAppWbsWbs> purchaseAppWbsWbses = (List<PurchaseAppWbsWbs>) orionJ2CacheService.get("ncf::PurchaseAppWbsWbs-import::id", importId);
        log.info("采购立项WBS信息导入的入库数据={}", JSONUtil.toJsonStr(purchaseAppWbsWbses));

        this.saveBatch(purchaseAppWbsWbses);
        orionJ2CacheService.delete("ncf::PurchaseAppWbsWbs-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("ncf::PurchaseAppWbsWbs-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<PurchaseAppWbsWbs> condition = new LambdaQueryWrapperX<>(PurchaseAppWbsWbs.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(PurchaseAppWbsWbs::getCreateTime);
        List<PurchaseAppWbsWbs> purchaseAppWbsWbses = this.list(condition);

        List<PurchaseAppWbsWbsDTO> dtos = BeanCopyUtils.convertListTo(purchaseAppWbsWbses, PurchaseAppWbsWbsDTO::new);

        String fileName = "采购立项WBS信息数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", PurchaseAppWbsWbsDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<PurchaseAppWbsWbsVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }


    public static class PurchaseAppWbsWbsExcelListener extends AnalysisEventListener<PurchaseAppWbsWbsDTO> {

        private final List<PurchaseAppWbsWbsDTO> data = new ArrayList<>();

        @Override
        public void invoke(PurchaseAppWbsWbsDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<PurchaseAppWbsWbsDTO> getData() {
            return data;
        }
    }


}
