package com.chinasie.orion.controller;

import com.chinasie.orion.domain.vo.BasePlaceVO;
import com.chinasie.orion.domain.vo.detailvo.TrainManageDetailVO;
import com.chinasie.orion.domain.vo.train.SimpleTrainVO;
import com.chinasie.orion.sdk.domain.vo.SimpleVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.domain.entity.TrainManage;
import com.chinasie.orion.domain.dto.TrainManageDTO;
import com.chinasie.orion.domain.vo.TrainManageVO;
import com.chinasie.orion.service.TrainManageService;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * TrainManage 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 17:09:01
 */
@RestController
@RequestMapping("/train-manage")
@Api(tags = "培训管理")
public class  TrainManageController  {

    @Autowired
    private TrainManageService trainManageService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看【培训管理】的信息", type = "TrainManage", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<TrainManageVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        TrainManageVO rsp = trainManageService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param trainManageDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【培训管理】数据【{{#trainManageDTO.name}}】", type = "TrainManage", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody TrainManageDTO trainManageDTO) throws Exception {
        String rsp =  trainManageService.create(trainManageDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param trainManageDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【培训管理】数据【{{#trainManageDTO.name}}】", type = "TrainManage", subType = "编辑", bizNo = "{{#trainManageDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  TrainManageDTO trainManageDTO) throws Exception {
        Boolean rsp = trainManageService.edit(trainManageDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【培训管理】数据", type = "TrainManage", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = trainManageService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【培训管理】数据", type = "TrainManage", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = trainManageService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【培训管理】数据", type = "TrainManage", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<TrainManageVO>> pages(@RequestBody Page<TrainManageDTO> pageRequest) throws Exception {
        Page<TrainManageVO> rsp =  trainManageService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("培训管理导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载【培训管理】导入模板", type = "TrainManage", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        trainManageService.downloadExcelTpl(response);
    }

    @ApiOperation("培训管理导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验【培训管理】导入", type = "TrainManage", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = trainManageService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("培训管理导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认【培训管理】导入", type = "TrainManage", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  trainManageService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消培训管理导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消【培训管理】导入", type = "TrainManage", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  trainManageService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("培训管理导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出【培训管理】数据", type = "TrainManage", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        trainManageService.exportByExcel(searchConditions, response);
    }




    /**
     *
     * @param
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取培训列表-简化")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【培训管理】简化列表", type = "TrainManage", subType = "查询列表-简化", bizNo = "")
    @RequestMapping(value = "/simple/list", method = RequestMethod.POST)
    public ResponseDTO<List<SimpleTrainVO>> simpleList() throws Exception {
        List<SimpleTrainVO> rsp =  trainManageService.simpleList();
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "获取当前人能够查询的基地列表")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】获取【培训管理】当前人能够查询的基地列表", type = "TrainManage", subType = "查询列表", bizNo = "")
    @RequestMapping(value = "/base-place/list", method = RequestMethod.POST)
    public ResponseDTO<List<BasePlaceVO>> basePlaceList() throws Exception {
        List<BasePlaceVO> rsp =  trainManageService.allBasePlaceList();
        return new ResponseDTO<>(rsp);
    }
}
