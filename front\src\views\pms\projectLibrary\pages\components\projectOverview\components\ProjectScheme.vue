<script setup lang="ts">
import {
  inject, onMounted, provide, readonly, ref, Ref,
} from 'vue';
import SchemeStatus from '../charts/SchemeStatus.vue';
import SchemeExecute from '../charts/SchemeExecute.vue';
import SchemeEvolve from '../charts/SchemeEvolve.vue';
import Api from '/@/api';
const projectId = inject('projectId');
const basicInfo: Ref<{
  [propName: string]: any
}> = ref({});
const loading:Ref<boolean> = ref(false);
provide('loading', readonly(loading));
const schemeInfoNew:Ref<Record<string, any>> = ref({});
provide('schemeInfoNew', schemeInfoNew);

onMounted(() => {
  getSchemeInfo();
});
async function getSchemeInfo() {
  loading.value = true;
  try {
    const result = await new Api(`/pms/projectOverview/zgh/projectPlanCount/${projectId}`).fetch({
    }, '', 'GET');
    schemeInfoNew.value = result || {};
    // eslint-disable-next-line no-console
  } finally {
    loading.value = false;
  }
}
</script>

<template>
  <div class="project-scheme">
    <div class="charts-wrap">
      <div class="title">
        计划状态
      </div>
      <SchemeStatus />
    </div>
    <div class="charts-wrap">
      <div class="title">
        计划执行异常
      </div>
      <SchemeExecute />
    </div>
    <div class="charts-wrap">
      <div class="title">
        计划数量
      </div>
      <SchemeEvolve />
    </div>
  </div>
</template>

<style scoped lang="less">
.project-scheme {
  display: grid;
  grid-template-columns: minmax(0, 1fr) minmax(0, 1fr) minmax(0, 2.5fr);

  .charts-wrap {
    position: relative;

    & + .charts-wrap::before {
      position: absolute;
      top: 38px;
      content: '';
      width: 1px;
      height: calc(100% - 38px);
      background-color: #f4f4f4;
    }

    .title {
      font-weight: bold;
      margin-bottom: ~`getPrefixVar('content-margin-top')`;
    }
  }
}
</style>
