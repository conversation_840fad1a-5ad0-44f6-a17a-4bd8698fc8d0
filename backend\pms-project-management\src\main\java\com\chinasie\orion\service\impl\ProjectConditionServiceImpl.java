package com.chinasie.orion.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.domain.dto.ProjectConditionDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.ProjectConditionVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.management.domain.entity.ContractInfo;
import com.chinasie.orion.management.domain.entity.CustomerInfo;
import com.chinasie.orion.management.domain.entity.ProjectInitiation;
import com.chinasie.orion.management.domain.entity.RequirementMangement;
import com.chinasie.orion.management.service.CustomerInfoService;
import com.chinasie.orion.management.service.ProjectInitiationService;
import com.chinasie.orion.management.service.RequirementMangementService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.ProjectConditionMapper;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ProjectConditionServiceImpl extends OrionBaseServiceImpl<ProjectConditionMapper, ProjectCondition> implements ProjectConditionService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private ProjectInitiationService projectInitiationService;
    @Autowired
    private MarketContractService marketContractService;
    @Autowired
    private ContractMilestoneService contractMilestoneService;
    @Autowired
    private ProjectPayService projectPayService;
    @Autowired
    private RequirementMangementService requirementMangementService;
    @Autowired
    private CustomerInfoService customerInfoService;
    @Autowired
    private PlatformTransactionManager transactionManager;
    @Autowired
    private DeptRedisHelper deptRedisHelper;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProjectConditionVO detail(String id, String pageCode) throws Exception {
        ProjectCondition projectCondition = this.getById(id);
        ProjectConditionVO result = BeanCopyUtils.convertTo(projectCondition, ProjectConditionVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param projectConditionDTO
     */
    @Override
    public String create(ProjectConditionDTO projectConditionDTO) throws Exception {
        ProjectCondition projectCondition = BeanCopyUtils.convertTo(projectConditionDTO, ProjectCondition::new);
        this.save(projectCondition);

        String rsp = projectCondition.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param projectConditionDTO
     */
    @Override
    public Boolean edit(ProjectConditionDTO projectConditionDTO) throws Exception {
        ProjectCondition projectCondition = BeanCopyUtils.convertTo(projectConditionDTO, ProjectCondition::new);

        this.updateById(projectCondition);

        String rsp = projectCondition.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectConditionVO> pages(Page<ProjectConditionDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProjectCondition> condition = new LambdaQueryWrapperX<>(ProjectCondition.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ProjectCondition::getCreateTime);


        Page<ProjectCondition> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectCondition::new));

        PageResult<ProjectCondition> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectConditionVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectConditionVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectConditionVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "项目状态导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectConditionDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ProjectConditionExcelListener excelReadListener = new ProjectConditionExcelListener();
        EasyExcel.read(inputStream, ProjectConditionDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ProjectConditionDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("项目状态导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ProjectCondition> projectConditiones = BeanCopyUtils.convertListTo(dtoS, ProjectCondition::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ProjectCondition-import::id", importId, projectConditiones, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ProjectCondition> projectConditiones = (List<ProjectCondition>) orionJ2CacheService.get("pmsx::ProjectCondition-import::id", importId);
        log.info("项目状态导入的入库数据={}", JSONUtil.toJsonStr(projectConditiones));

        this.saveBatch(projectConditiones);
        orionJ2CacheService.delete("pmsx::ProjectCondition-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ProjectCondition-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ProjectCondition> condition = new LambdaQueryWrapperX<>(ProjectCondition.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ProjectCondition::getCreateTime);
        List<ProjectCondition> projectConditiones = this.list(condition);

        List<ProjectConditionDTO> dtos = BeanCopyUtils.convertListTo(projectConditiones, ProjectConditionDTO::new);

        String fileName = "项目状态数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectConditionDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<ProjectConditionVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }

    @Override
    @Async
    public void insertProjectConditionData() throws Exception {
        List<ProjectCondition> projectConditions = this.list();
        //项目数据
        List<Project> projectList = projectService.list(new LambdaQueryWrapperX<>(Project.class));
        //项目编码集合
        List<String> projectNumbers = projectList.stream().filter(project -> project.getNumber() != null).map(Project::getNumber).collect(Collectors.toList());
        //项目id集合
        List<String> projectIds = projectList.stream().filter(project -> project.getId() != null).map(Project::getId).collect(Collectors.toList());
        //承接中心
        List<String> centerList = projectList.stream().filter(project -> project.getResDept() != null).map(Project::getResDept).distinct().collect(Collectors.toList());
        List<DeptVO> deptVos;
        if (!centerList.isEmpty()) {
            deptVos = deptRedisHelper.getDeptByIds(centerList);
        } else {
            deptVos = new ArrayList<>();
        }

        List<ProjectPay> payList;
        if (!projectIds.isEmpty()) {
            //支出
            payList = projectPayService.list(new LambdaQueryWrapperX<>(ProjectPay.class).in(ProjectPay::getProjectId, projectIds));
        } else {
            payList = new ArrayList<>();
        }



        //项目立项-根据项目编码关联
        List<ProjectInitiation> projectInitiationList;
        if (!projectNumbers.isEmpty()) {
            projectInitiationList = projectInitiationService.list(new LambdaQueryWrapperX<>(ProjectInitiation.class).in(ProjectInitiation::getProjectNumber, projectNumbers));
        } else {
            projectInitiationList = new ArrayList<>();
        }

        List<String> contractNumbers = projectInitiationList.stream().filter(o -> StrUtil.isNotBlank(o.getContractNumbers())).map(o -> Arrays.asList(o.getContractNumbers().split(","))).flatMap(Collection::stream).distinct().collect(Collectors.toList());

        //根据项目立项关联合同查询合同表
        List<MarketContract> contractList;
        if (!contractNumbers.isEmpty()) {
            contractList = marketContractService.list(new LambdaQueryWrapperX<>(MarketContract.class).in(MarketContract::getNumber, contractNumbers));
        } else {
            contractList = new ArrayList<>();
        }

        //需求Id
        List<String> requirementIds = contractList.stream().map(MarketContract::getRequirementId).collect(Collectors.toList());

        //需求管理
        List<RequirementMangement> requirementMangements;
        if (!requirementIds.isEmpty()) {
            requirementMangements = requirementMangementService.list(new LambdaQueryWrapperX<>(RequirementMangement.class).in(RequirementMangement::getId, requirementIds));
        } else {
            requirementMangements = new ArrayList<>();
        }
        //客户编码
        List<String> customerCodes = requirementMangements.stream().map(RequirementMangement::getCustPerson).collect(Collectors.toList());
        //客户
        List<CustomerInfo> customerInfos;
        if (!customerCodes.isEmpty()) {
            customerInfos = customerInfoService.list(new LambdaQueryWrapperX<>(CustomerInfo.class).in(CustomerInfo::getId, customerCodes));
        } else {
            customerInfos = new ArrayList<>();
        }

        List<String> marketContractsIds = contractList.stream().map(MarketContract::getId).collect(Collectors.toList());
        //收入--里程碑actual_milestone_amt合计表示收入
        List<ContractMilestone> contractMilestones;
        if (!marketContractsIds.isEmpty()) {
            contractMilestones = contractMilestoneService.list(new LambdaQueryWrapperX<>(ContractMilestone.class).in(ContractMilestone::getContractId, marketContractsIds));
        } else {
            contractMilestones = new ArrayList<>();
        }

        projectList.forEach(project -> {
            ProjectCondition projectCondition = projectConditions.stream().filter(o -> o.getProjectNumber().equals(project.getNumber())).collect(Collectors.toList()).stream().findFirst().orElse(new ProjectCondition());

            projectCondition.setProjectNumber(project.getNumber());
            projectCondition.setProjectName(project.getName());
            DeptVO deptVO = deptVos.stream().filter(o -> o.getId().equals(project.getResDept())).collect(Collectors.toList()).stream().findFirst().orElse(new DeptVO());
            projectCondition.setUndertakingCenter(deptVO.getName());
            //对应项目立项
            List<ProjectInitiation> projectInitiations = projectInitiationList.stream().filter(o -> o.getProjectNumber().equals(project.getNumber())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(projectInitiations)) {
                ProjectInitiation projectInitiation = projectInitiations.get(0);
                //对应合同
                String[] contractNumberList = projectInitiation.getContractNumbers().split(",");
                if (!CollectionUtils.isEmpty(Arrays.asList(contractNumberList))) {
                    //对应合同
                    List<MarketContract> marketContracts = contractList.stream().filter(o -> o.getNumber().equals(contractNumberList[0])).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(marketContracts)) {
                        MarketContract marketContract = marketContracts.get(0);
                        //需求
                        List<RequirementMangement> ments = requirementMangements.stream().filter(o -> o.getId().equals(marketContract.getRequirementId())).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(ments)) {
                            RequirementMangement requirementMangement = ments.get(0);
                            List<CustomerInfo> customers = customerInfos.stream().filter(o -> o.getId().equals(requirementMangement.getCustPerson())).collect(Collectors.toList());
                            if (!CollectionUtils.isEmpty(customers)) {
                                //客户
                                projectCondition.setCustCompany(customers.get(0).getCusName());
                            }
                        }
                        //收入
                        double income = contractMilestones.stream().filter(o -> o.getContractId().equals(marketContract.getId()) && o.getActualMilestoneAmt() != null).mapToDouble(o -> o.getActualMilestoneAmt().doubleValue()).sum();
                        projectCondition.setProjectRevenue(BigDecimal.valueOf(income));
                    }
                }
            }
            //支出
            double pay = payList.stream().filter(o -> o.getProjectId().equals(project.getId()) && o.getActualAmount() != null).mapToDouble(o -> o.getActualAmount().doubleValue()).sum();
            projectCondition.setProjectCost(BigDecimal.valueOf(pay));
            projectConditions.add(projectCondition);
        });
        //开启事务
        TransactionStatus transactionStatus = transactionManager.getTransaction(new DefaultTransactionDefinition());
        int size = projectConditions.size();
        int batchSize = 1000;
        try {
            for (int i = 0; i < size; i += batchSize) {
                List<ProjectCondition> subList = projectConditions.subList(i, Math.min(i + batchSize, size));
                this.saveOrUpdateBatch(subList);
            }
            // 提交事务
            transactionManager.commit(transactionStatus);
        } catch (Exception e) {
            // 回滚事务
            transactionManager.rollback(transactionStatus);
            throw e;
        }
    }


    public static class ProjectConditionExcelListener extends AnalysisEventListener<ProjectConditionDTO> {

        private final List<ProjectConditionDTO> data = new ArrayList<>();

        @Override
        public void invoke(ProjectConditionDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ProjectConditionDTO> getData() {
            return data;
        }
    }


}


