<script setup lang="ts">
import { BasicForm, useForm } from 'lyra-component-vue3';
import { onMounted, Ref, ref } from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';
const props = defineProps<{
  formId: string | undefined
}>();

const schemas = [
  {
    field: 'indexNumber',
    label: '指标编码',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入指标编码',
      maxlength: 200,
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'indexType',
    label: '考核类型',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入考核类型',
      maxlength: 200,
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'indexTarget',
    label: '目标值',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入目标值',
      maxlength: 200,
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'indexActual',
    label: '实际值',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入实际值',
      maxlength: 200,
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  // 状态灯 下拉框 红绿黄
  {
    field: 'indexStatus',
    label: '状态灯',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请选择状态灯',
      options: [
        {
          label: '红',
          value: 'red',
        },
        {
          label: '绿',
          value: 'green',
        },
        {
          label: '黄',
          value: 'yellow',
        },
      ],
    },
    rules: [{ required: true }],
    component: 'Select',
  },

];

const [register, { validate, setFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  props.formId && getFormData();
});

const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/productionIndex').fetch('', props.formId, 'GET');
    setFieldsValue(result);
  } finally {
    loading.value = false;
  }
}

false;

defineExpose({
  async onSubmit() {
    const formValues = await validate();
    formValues.cusCeateTime = formValues.cusCeateTime ? dayjs(formValues.cusCeateTime).format('YYYY-MM-DD HH:mm:ss') : '';
    const params = {
      id: props?.formId,
      ...formValues,
      mainTableId: undefined,
    };

    return new Promise((resolve, reject) => {
      new Api('/pms/productionIndex').fetch(params, props.formId ? 'edit' : 'add', props.formId ? 'PUT' : 'POST').then(() => {
        resolve('');
      }).catch((err) => {
        reject(err);
      });
    });
  },
});
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less"></style>
