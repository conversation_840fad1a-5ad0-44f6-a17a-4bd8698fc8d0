package com.chinasie.orion.management.service.impl;

import com.chinasie.orion.management.constant.IsMainContactEnum;
import com.chinasie.orion.management.domain.dto.CustomerContactDTO;
import com.chinasie.orion.management.domain.dto.ExportCustomerContactDTO;
import com.chinasie.orion.management.domain.entity.CustomerContact;
import com.chinasie.orion.management.domain.vo.CustomerContactVO;
import com.chinasie.orion.management.repository.CustomerContactMapper;
import com.chinasie.orion.management.service.CustomerContactService;
import com.chinasie.orion.page.PageResult;

import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;

import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;




/**
 * <p>
 * customerContact 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-30 16:25:24
 */
@Service
@Slf4j
public class CustomerContactServiceImpl extends OrionBaseServiceImpl<CustomerContactMapper, CustomerContact> implements CustomerContactService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public CustomerContactVO detail(String id, String pageCode) throws Exception {
        CustomerContact customerContact =this.getById(id);
        CustomerContactVO result = BeanCopyUtils.convertTo(customerContact, CustomerContactVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param customerContactDTO
     */
    @Override
    public  String create(CustomerContactDTO customerContactDTO) throws Exception {
        CustomerContact customerContact =BeanCopyUtils.convertTo(customerContactDTO, CustomerContact::new);
        if (customerContact.getIsMain().equals("true")) {
            LambdaQueryWrapperX<CustomerContact> queryWrapperX = new LambdaQueryWrapperX<>();
            queryWrapperX.eq(CustomerContact::getMainTableId, customerContactDTO.getMainTableId());
            List<CustomerContact> customerContacts = this.baseMapper.selectList(queryWrapperX);
            if (!CollectionUtils.isEmpty(customerContacts)) {
                customerContacts.forEach(c -> {
                    c.setIsMain("false");
                });
                this.baseMapper.updateBatch(customerContacts,customerContacts.size());
            }

        }
        this.save(customerContact);

        String rsp= customerContact.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param customerContactDTO
     */
    @Override
    public Boolean edit(CustomerContactDTO customerContactDTO) throws Exception {
        CustomerContact customerContact =BeanCopyUtils.convertTo(customerContactDTO, CustomerContact::new);
        if (customerContact.getIsMain().equals("true")) {
            LambdaQueryWrapperX<CustomerContact> queryWrapperX = new LambdaQueryWrapperX<>();
            queryWrapperX.eq(CustomerContact::getMainTableId, customerContactDTO.getMainTableId());
            List<CustomerContact> customerContacts = this.baseMapper.selectList(queryWrapperX);
            if (!CollectionUtils.isEmpty(customerContacts)) {
                customerContacts.forEach(c -> {
                    c.setIsMain("false");
                });
                this.baseMapper.updateBatch(customerContacts,customerContacts.size());
            }

        }
        this.updateById(customerContact);

        String rsp= customerContact.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        for (String id : ids) {




        }
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<CustomerContactVO> pages(String mainTableId, Page<CustomerContactDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<CustomerContact> condition = new LambdaQueryWrapperX<>( CustomerContact. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(CustomerContact::getCreateTime);

            condition.eq(CustomerContact::getMainTableId, mainTableId);

        Page<CustomerContact> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), CustomerContact::new));

        PageResult<CustomerContact> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<CustomerContactVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<CustomerContactVO> vos = BeanCopyUtils.convertListTo(page.getContent(), CustomerContactVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "客户管理详情导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", CustomerContactDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            customerContactExcelListener excelReadListener = new customerContactExcelListener();
        EasyExcel.read(inputStream, CustomerContactDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<CustomerContactDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("客户管理详情导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<CustomerContact> customerContactes =BeanCopyUtils.convertListTo(dtoS, CustomerContact::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pms::customerContact-import::id", importId, customerContactes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<CustomerContact> customerContactes = (List<CustomerContact>) orionJ2CacheService.get("pms::customerContact-import::id", importId);
        log.info("客户管理详情导入的入库数据={}", JSONUtil.toJsonStr(customerContactes));

        this.saveBatch(customerContactes);
        orionJ2CacheService.delete("pms::customerContact-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pms::customerContact-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<CustomerContact> condition = new LambdaQueryWrapperX<>( CustomerContact. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(CustomerContact::getCreateTime);
        List<CustomerContact> customerContactes =   this.list(condition);

        List<CustomerContactDTO> dtos = BeanCopyUtils.convertListTo(customerContactes, CustomerContactDTO::new);

        String fileName = "客户管理详情数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", CustomerContactDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<CustomerContactVO> vos)throws Exception {


        vos.forEach(vo->{
            vo.setIsMainName(IsMainContactEnum.getDesc(vo.getIsMain()));
        });


    }

    @Override
    public void exportByContactExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<CustomerContact> condition = new LambdaQueryWrapperX<>( CustomerContact. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(CustomerContact::getCreateTime);
        List<CustomerContact> customerContactes =   this.list(condition);

        List<ExportCustomerContactDTO> dtos = BeanCopyUtils.convertListTo(customerContactes, ExportCustomerContactDTO::new);

        String fileName = "客户管理详情数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ExportCustomerContactDTO.class,dtos );

    }


    public static class customerContactExcelListener extends AnalysisEventListener<CustomerContactDTO> {

        private final List<CustomerContactDTO> data = new ArrayList<>();

        @Override
        public void invoke(CustomerContactDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<CustomerContactDTO> getData() {
            return data;
        }
    }


}
