<template>
  <BasicModal
    :width="'600px'"
    title="计划下发"
    zIndex="999"
    @register="modalRegister"
    @ok="handleDistributeOk"
  >
    <div class="dis-body">
      <p>计划将通过代办通知下发给责任人，计划下发后无法返回，请悉知</p>
      <a-radio-group v-model:value="distributesState.needNote">
        <a-radio :value="false">
          不需要抄送
        </a-radio>
        <a-radio :value="true">
          需要抄送
        </a-radio>
      </a-radio-group>
      <div
        v-if="distributesState.needNote"
        class="flex-box"
      >
        <span>请选择抄送人：</span>
        <a-select
          v-model:value="distributesState.beNotifiedPersons"
          placeholder="请选择抄送人"
          style="width: 200px"
          mode="multiple"
          :options="userList"
        >
          <a-select-option value="private">
            Private
          </a-select-option>
          <a-select-option value="public">
            Public
          </a-select-option>
        </a-select>
      </div>
    </div>
  </BasicModal>
</template>
<script lang="ts">
import { defineComponent, ref, reactive } from 'vue';
import {
  message,
  RadioGroup,
  Select,
  SelectOption,
  Radio,
} from 'ant-design-vue';
import { BasicModal, useModalInner } from 'lyra-component-vue3';
import Api from '/@/api';

export default defineComponent({
  name: 'PlanDone',
  components: {
    BasicModal,
    ARadioGroup: RadioGroup,
    ASelect: Select,
    ASelectOption: SelectOption,
    ARadio: Radio,
  },
  emits: ['handleColse', 'updateForm'],
  setup(props, { emit }) {
    const data = reactive({
      schemeIds: [],
      projectName: '',
      informUserId: '',
      projectId: '',
      from: '',
      fromId: '',
    });

    const userList = ref([]);
    const distributesState = reactive({
      needNote: false,
      beNotifiedPersons: [],
    });

    const [modalRegister, { closeModal, setModalProps }] = useModalInner(
      (rowData) => {
        data.schemeIds = rowData.schemeIds;
        data.projectName = rowData.projectName;
        data.projectId = rowData.projectId;
        data.informUserId = rowData.informUserId;
        distributesState.needNote = false;
        distributesState.beNotifiedPersons = [];
        data.from = rowData.from;
        data.fromId = rowData.fromId;
        getUserList();
      },
    );

    const getUserList = async () => {
      const res = await new Api('/pms').fetch(
        '',
        `project-role-user/${data.projectId}/allUser`,
        'POST',
      );
      userList.value = res.map((item) => ({
        value: item.id,
        label: item.name,
      }));
    };

    // 计划下发确认
    const handleDistributeOk = () => {
      const params = {
        ...data,
        ...distributesState,
      };
      new Api('/pms')
        .fetch(params, 'projectScheme/issue', 'PUT')
        .then((res) => {
          message.success('下发成功');
          emit('updateForm');
          closeModal();
          // emit('handleColse');
        });
    };

    return {
      distributesState,
      modalRegister,
      handleDistributeOk,
      userList,
    };
  },
});
</script>
<style lang="less" scoped>
.dis-body {
  padding: 22px 22px 30px;

  .flex-box {
    display: flex;
    align-items: center;
    margin-top: 10px;

    > span {
      margin-right: 10px;
    }
  }
}
</style>
