import {
  BasicButton, openDrawer, openModal, randomString,
} from 'lyra-component-vue3';
import { h, ref, Ref } from 'vue';
import ApplyDrawer from './components/ApplyDrawer.vue';
import Api from '/@/api';

export function useLaborCost() {
  const tableData: Ref<any[]> = ref([]);
  const fetching: Ref<boolean> = ref(false);
  const tableKey: Ref<string> = ref('');

  async function getApi(params: {
        contractNumber: string,
        year: string
    }) {
    fetching.value = true;
    try {
      const result = await new Api('/spm/contractMain/laborCost/statistics').fetch(params, '', 'GET');
      tableData.value = result ? [result] : [];
      tableKey.value = randomString();
    } finally {
      fetching.value = false;
    }
  }

  const loading: Ref<boolean> = ref(false);
  const footerActions = [
    {
      text: '取消',
      event: 'cancel',
    },
    {
      text: '保存',
      event: 'save',
    },
    {
      text: '发起流程',
      event: 'initiate',
      props: {
        type: 'primary',
      },
    },
  ];

  function openCheckLaborCostDrawer(record: Record<string, any>, cb: Function) {
    const contentRef: Ref = ref();
    const fetching: Ref<boolean> = ref(true);
    openDrawer({
      title: '发起合同验收单',
      width: 1000,
      content() {
        return h(ApplyDrawer, {
          ref: contentRef,
          fetching: fetching.value,
          onUpdateFetching(value: boolean) {
            fetching.value = value;
          },
          record: record || {},
        });
      },
      visibleChange() {
        loading.value = false;
      },
      footer: () => h('div', {
        style: {
          marginLeft: 'auto',
        },
      }, {
        default: () => footerActions.map((item: any) => h(BasicButton, {
          ...(item.props || {}),
          loading: item.event !== 'cancel' && loading.value,
          onClick: () => handleFooter(item.event, contentRef, cb),
        }, {
          default: () => item.text,
        })),
      }),
    });
  }

  async function handleFooter(type: string, contentRef: Ref, cb: Function) {
    switch (type) {
      case 'cancel':
        (openModal as any).closeAll();
        break;
      case 'save':
      case 'initiate':
        const result = await contentRef.value.save(loading);
        if (type === 'initiate') {
          await contentRef.value.initiate(result, loading);
        }
        cb?.();
        (openModal as any).closeAll();
        break;
    }
  }

  return {
    tableKey,
    tableData,
    fetching,
    getApi,
    openCheckLaborCostDrawer,
  };
}
