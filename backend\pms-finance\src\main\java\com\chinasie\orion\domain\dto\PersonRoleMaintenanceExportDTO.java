package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * PersonRoleMaintenance DTO对象
 *
 * <AUTHOR>
 * @since 2024-10-08 17:28:59
 */
@ApiModel(value = "PersonRoleMaintenanceDTO对象", description = "人员角色维护表")
@Data
@ExcelIgnoreUnannotated
public class PersonRoleMaintenanceExportDTO extends  ObjectDTO   implements Serializable {



    /**
     * 专业所编码
     */
    @ApiModelProperty(value = "专业所编码")
    @ExcelProperty(value = "专业所编码 ", index = 0)
    private String expertiseStation;

    /**
     * 专业所名称
     */
    @ApiModelProperty(value = "专业所名称")
    @ExcelProperty(value = "专业所名称 ", index = 1)
    private String expertiseStationTitle;

    /**
     * 专业所审核人员姓名
     */
    @ApiModelProperty(value = "专业所审核人员姓名")
    @ExcelProperty(value = "专业所审核人员姓名 ", index = 2)
    private String expertiseStationName;

    /**
     * 专业所审核人员编码
     */
    @ApiModelProperty(value = "专业所审核人员编码")
    @ExcelProperty(value = "专业所审核人员编码 ", index = 3)
    private String expertiseStationCode;

    /**
     * 专业中心
     */
    @ApiModelProperty(value = "专业中心")
    @ExcelProperty(value = "专业中心 ", index = 4)
    private String expertiseCenter;

    /**
     * 专业中心名称
     */
    @ApiModelProperty(value = "专业中心名称")
    @ExcelProperty(value = "专业中心名称 ", index = 5)
    private String expertiseCenterTitle;

    /**
     * 专业中心审核人员编码
     */
    @ApiModelProperty(value = "专业中心审核人员编码")
    @ExcelProperty(value = "专业中心审核人员编码 ", index = 6)
    private String expertiseCenterCode;

    /**
     * 专业中心审核人员姓名
     */
    @ApiModelProperty(value = "专业中心审核人员姓名")
    @ExcelProperty(value = "专业中心审核人员姓名 ", index = 7)
    private String expertiseCenterName;

    /**
     * 财务人员编码
     */
    @ApiModelProperty(value = "财务人员编码")
    @ExcelProperty(value = "财务人员编码 ", index = 8)
    private String financialStaffCode;


    /**
     * 财务人员姓名
     */
    @ApiModelProperty(value = "财务人员姓名")
    @ExcelProperty(value = "财务人员姓名 ", index = 9)
    private String financialStaffName;


    /**
     * 变更时间
     */
    @ApiModelProperty(value = "变更时间")
    @ExcelProperty(value = "变更时间 ", index = 10)
    private String changeTime;


    /**
     * 变更人编码
     */
    @ApiModelProperty(value = "变更人编码")
    @ExcelProperty(value = "变更人编码 ", index = 11)
    private String changePerson;

    /**
     * 变更人姓名
     */
    @ApiModelProperty(value = "变更人姓名")
    @ExcelProperty(value = "变更人姓名 ", index = 12)
    private String changePersonName;

    /**
     * 变更原因
     */
    @ApiModelProperty(value = "变更原因")
    private String changeReason;

    /**
     * 更新人
     */
    /*@ApiModelProperty("更新人")
    private String modifyName;*/


    /**
     * 最后一次更新时间
     */
    /*@ApiModelProperty("最后一次更新时间")
    private Date modifyTime;
*/
}
