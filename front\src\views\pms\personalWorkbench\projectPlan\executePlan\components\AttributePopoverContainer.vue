<template>
  <div class="attribute-popover-container-wrap-pms">
    <Spin :spinning="state.loadingStatus">
      <div v-if="state.taskDetail">
        <div class="title">
          {{ state.taskDetail?.name }}
        </div>
        <div class="content">
          <!--          <div class="flex">-->
          <!--            <div class="s-title">-->
          <!--              编号:-->
          <!--            </div>-->
          <!--            <div class="flex-f1">-->
          <!--              {{ state.taskDetail?.number }}-->
          <!--            </div>-->
          <!--          </div>-->
          <div class="flex">
            <div class="s-title">
              责任处室:
            </div>
            <div class="flex-f1">
              {{ state.taskDetail?.leftText.split("：")[0]||'' }}
            </div>
          </div>
          <div class="flex">
            <div class="s-title">
              负责人:
            </div>
            <div class="flex-f1">
              {{ state.taskDetail?.leftText.split("：")[1]||'' }}
            </div>
          </div>
          <div class="flex">
            <div class="s-title">
              计划完成时间:
            </div>
            <div class="flex-f1">
              {{ state.taskDetail?.end ? formatDate(state.taskDetail?.end):'' }}
            </div>
          </div>
        </div>
      </div>
      <div
        v-else
        class="empty"
      />
    </Spin>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive } from 'vue';
import { Spin } from 'ant-design-vue';
import dayjs from 'dayjs';

const props = defineProps<{
  taskItem?: any,
  getTaskDetailApi?:(task)=> Promise<any>
}>();

const state = reactive({
  loadingStatus: false,
  taskDetail: null,
});

onMounted(() => {
  init();
});

function init() {
  getTaskDetail();
}

async function getTaskDetail() {
  state.taskDetail = props.taskItem;
  if (!props.getTaskDetailApi) return;
  state.loadingStatus = true;
  state.taskDetail = await props.getTaskDetailApi(props.taskItem);
  state.loadingStatus = false;
}

function formatDate(t) {
  return t ? dayjs(t).format('YYYY-MM-DD') : '';
}

</script>

<style  lang="less">
.attribute-popover-container-wrap-pms {
  width: 220px;
  height: 185px;
  padding: 15px;
  .empty {
    height: 130px
  }
  .title {
    font-size: 14px;
    font-weight: bold;

  }
  .content {
    margin-top: 5px;
    color: ~`getPrefixVar('text-color-second')`;

    >div {
      padding: 2px 0;
    }

    .s-title {
      padding-right: 4px;
    }
  }
}
</style>
