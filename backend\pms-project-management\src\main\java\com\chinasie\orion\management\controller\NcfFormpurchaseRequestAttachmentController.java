package com.chinasie.orion.management.controller;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.dto.NcfFormpurchaseRequestAttachmentDTO;
import com.chinasie.orion.management.domain.dto.ProjectOrderDTO;
import com.chinasie.orion.management.domain.vo.NcfFormpurchaseRequestAttachmentVO;
import com.chinasie.orion.management.service.NcfFormpurchaseRequestAttachmentService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * NcfFormpurchaseRequestAttachment 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-11 10:49:58
 */
@RestController
@RequestMapping("/ncfFormpurchaseRequestAttachment")
@Api(tags = "采购申请附件")
public class NcfFormpurchaseRequestAttachmentController {

    @Autowired
    private NcfFormpurchaseRequestAttachmentService ncfFormpurchaseRequestAttachmentService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "采购申请附件", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<NcfFormpurchaseRequestAttachmentVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        NcfFormpurchaseRequestAttachmentVO rsp = ncfFormpurchaseRequestAttachmentService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param ncfFormpurchaseRequestAttachmentDTOs
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#ncfFormpurchaseRequestAttachmentDTO.name}}】", type = "采购申请附件", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> create(@RequestBody List<NcfFormpurchaseRequestAttachmentDTO> ncfFormpurchaseRequestAttachmentDTOs) throws Exception {
        boolean rsp = ncfFormpurchaseRequestAttachmentService.create(ncfFormpurchaseRequestAttachmentDTOs);

        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param ncfFormpurchaseRequestAttachmentDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#ncfFormpurchaseRequestAttachmentDTO.name}}】", type = "采购申请附件", subType = "编辑", bizNo = "{{#ncfFormpurchaseRequestAttachmentDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody NcfFormpurchaseRequestAttachmentDTO ncfFormpurchaseRequestAttachmentDTO) throws Exception {
        Boolean rsp = ncfFormpurchaseRequestAttachmentService.edit(ncfFormpurchaseRequestAttachmentDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "采购申请附件", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = ncfFormpurchaseRequestAttachmentService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "采购申请附件", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = ncfFormpurchaseRequestAttachmentService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "采购申请附件", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<NcfFormpurchaseRequestAttachmentVO>> pages(@RequestBody Page<NcfFormpurchaseRequestAttachmentDTO> pageRequest) throws Exception {
        Page<NcfFormpurchaseRequestAttachmentVO> rsp = ncfFormpurchaseRequestAttachmentService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 根据编号查询附件
     *
     * @param ncfFormpurchaseRequestAttachmentDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "根据编号查询附件")
    @RequestMapping(value = "/getAttachmentsByCode", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】根据编号查询附件", type = "采购申请附件", subType = "根据编号查询附件", bizNo = "{{#ncfFormpurchaseRequestAttachmentDTO.code}}")
    public ResponseDTO<List<NcfFormpurchaseRequestAttachmentVO>> getAttachmentsByCode(@RequestBody NcfFormpurchaseRequestAttachmentDTO ncfFormpurchaseRequestAttachmentDTO) throws Exception {

        List<NcfFormpurchaseRequestAttachmentVO> rsp = ncfFormpurchaseRequestAttachmentService.getAttachmentsByCode(ncfFormpurchaseRequestAttachmentDTO.getCode());
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("采购申请附件导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "采购申请附件", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        ncfFormpurchaseRequestAttachmentService.downloadExcelTpl(response);
    }

    @ApiOperation("采购申请附件导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "采购申请附件", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = ncfFormpurchaseRequestAttachmentService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("采购申请附件导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "采购申请附件", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = ncfFormpurchaseRequestAttachmentService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消采购申请附件导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "采购申请附件", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = ncfFormpurchaseRequestAttachmentService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("采购申请附件导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "采购申请附件", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        ncfFormpurchaseRequestAttachmentService.exportByExcel(searchConditions, response);
    }
}
