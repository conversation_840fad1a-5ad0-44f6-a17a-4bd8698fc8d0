<?xml version='1.0' encoding='UTF-8'?>

<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-plugins</artifactId>
    <version>35</version>
    <relativePath />
  </parent>

  <artifactId>maven-clean-plugin</artifactId>
  <version>3.2.0</version>
  <packaging>maven-plugin</packaging>

  <name>Apache Maven Clean Plugin</name>
  <description>
    The Maven Clean Plugin is a plugin that removes files generated at build-time in a project's directory.
  </description>
  <inceptionYear>2001</inceptionYear>

  <prerequisites>
    <maven>${mavenVersion}</maven>
  </prerequisites>

  <scm>
    <connection>scm:git:https://gitbox.apache.org/repos/asf/maven-clean-plugin.git</connection>
    <developerConnection>scm:git:https://gitbox.apache.org/repos/asf/maven-clean-plugin.git</developerConnection>
    <url>https://github.com/apache/maven-clean-plugin/tree/${project.scm.tag}</url>
    <tag>maven-clean-plugin-3.2.0</tag>
  </scm>
  <issueManagement>
    <system>JIRA</system>
    <url>https://issues.apache.org/jira/browse/MCLEAN</url>
  </issueManagement>
  <ciManagement>
    <system>Jenkins</system>
    <url>https://ci-builds.apache.org/job/Maven/job/maven-box/job/maven-clean-plugin/</url>
  </ciManagement>
  <distributionManagement>
    <site>
      <id>apache.website</id>
      <url>scm:svn:https://svn.apache.org/repos/asf/maven/website/components/${maven.site.path}</url>
    </site>
  </distributionManagement>

  <properties>
    <mavenVersion>3.2.5</mavenVersion>
    <javaVersion>8</javaVersion>
    <surefire.version>2.22.2</surefire.version>
    <mavenPluginToolsVersion>3.6.4</mavenPluginToolsVersion>
    <project.build.outputTimestamp>2022-04-01T21:20:29Z</project.build.outputTimestamp>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-plugin-api</artifactId>
      <version>${mavenVersion}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.shared</groupId>
      <artifactId>maven-shared-utils</artifactId>
      <version>3.3.4</version>
    </dependency>

    <!-- dependencies to annotations -->
    <dependency>
      <groupId>org.apache.maven.plugin-tools</groupId>
      <artifactId>maven-plugin-annotations</artifactId>
      <scope>provided</scope>
    </dependency>

    <!-- Test -->
    <dependency>
      <groupId>org.apache.maven.plugin-testing</groupId>
      <artifactId>maven-plugin-testing-harness</artifactId>
      <version>3.3.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-compat</artifactId>
      <version>${mavenVersion}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-core</artifactId>
      <version>${mavenVersion}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.13.2</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <profiles>
    <profile>
      <id>run-its</id>
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-invoker-plugin</artifactId>
              <configuration>
                <debug>true</debug>
                <addTestClassPath>true</addTestClassPath>
                <projectsDirectory>src/it</projectsDirectory>
                <cloneProjectsTo>${project.build.directory}/it</cloneProjectsTo>
                <pomIncludes>
                  <pomInclude>*/pom.xml</pomInclude>
                </pomIncludes>
                <preBuildHookScript>setup</preBuildHookScript>
                <postBuildHookScript>verify</postBuildHookScript>
                <localRepositoryPath>${project.build.directory}/local-repo</localRepositoryPath>
                <settingsFile>src/it/settings.xml</settingsFile>
                <goals>
                  <goal>clean</goal>
                </goals>
              </configuration>
            </plugin>
          </plugins>
        </pluginManagement>
      </build>
    </profile>
  </profiles>
</project>
