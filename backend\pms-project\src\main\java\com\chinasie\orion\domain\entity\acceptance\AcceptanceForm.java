package com.chinasie.orion.domain.entity.acceptance;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;

import java.io.Serializable;
import java.util.Date;

/**
 * 验收单.
 */
@TableName(value = "pms_acceptance_form")
public class AcceptanceForm extends ObjectEntity implements Serializable {

    // 项目Id
    @TableField("project_id")
    private String projectId;

    // 验收单编号
    @TableField("number")
    private String number;

    // 验收单类型
    @TableField("type")
    private String type;

    // 验收完成日期
    @TableField("complete_time")
    private Date completeTime;

    // 验收完成执行人Id
    @TableField("complete_user_id")
    private String completeUserId;


    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Date getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }

    public String getCompleteUserId() {
        return completeUserId;
    }

    public void setCompleteUserId(String completeUserId) {
        this.completeUserId = completeUserId;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }
}
