package com.chinasie.orion.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.lock.LockInfo;
import com.baomidou.lock.LockTemplate;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.bo.CommonRoleBo;
import com.chinasie.orion.bo.PersonCountUtils;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.DictConstant;
import com.chinasie.orion.constant.MajorRepairOrgEnum;
import com.chinasie.orion.constant.PersonManageLedgerTypeEnum;
import com.chinasie.orion.constant.PersonPermanentConstant;
import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.dto.excel.PersonMangeEntryExcelTpl;
import com.chinasie.orion.domain.dto.excel.PersonPlanVO;
import com.chinasie.orion.domain.dto.job.EditContactUserDTO;
import com.chinasie.orion.domain.dto.job.EditNewcomerDTO;
import com.chinasie.orion.domain.dto.material.InAndOutDTO;
import com.chinasie.orion.domain.dto.person.AddParamDTO;
import com.chinasie.orion.domain.dto.person.LeaveDTO;
import com.chinasie.orion.domain.dto.person.PersonDownDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.domain.vo.major.BasePersonCountVO;
import com.chinasie.orion.domain.vo.relationOrgToMaterial.MaterialManageImplementTreeVO;
import com.chinasie.orion.enums.StatusEnum;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.export.ImportExcelErrorNoteVO;
import com.chinasie.orion.exception.BaseErrorCode;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.MyExceptionCode;
import com.chinasie.orion.handler.status.write.ExcelCellSelectWriterHandler;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.PersonManageLedgerMapper;
import com.chinasie.orion.repository.PersonMangeMapper;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.domain.vo.user.UserBaseCacheVO;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.*;
import com.chinasie.orion.strategy.config.StrategyConfig;
import com.chinasie.orion.strategy.constant.PersonDeepEnum;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.TooUtils;
import com.chinasie.orion.util.TreeInfoProcessor;
import com.mzt.logapi.context.LogRecordContext;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.chinasie.orion.constant.DictConts.*;


/**
 * <p>
 * PersonMange 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-03 22:00:57
 */
@Service
@Slf4j
public class PersonMangeServiceImpl extends OrionBaseServiceImpl<PersonMangeMapper, PersonMange> implements PersonMangeService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private BasicUserService basicUserService;

    @Autowired
    private BasePlaceService basePlaceService;


    @Autowired
    private PersonManageLedgerService personManageLedgerService;
    @Autowired
    private PersonManageLedgerMapper personManageLedgerMapper;
    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private DictRedisHelper dictRedisHelper;

    @Autowired
    private MajorRepairPlanService majorRepairPlanService;

    private JobPostAuthorizeService jobPostAuthorizeService;

    @Autowired
    private PersonMangeMapper personMangeMapper;

    @Autowired
    private ClassRedisHelper classRedisHelper;

    @Autowired
    private JobNodeStatusService jobNodeStatusService;

    @Autowired
    private JobPersonRecordService jobPersonRecordService;

    @Autowired
    private RelationOrgToPersonService repairOrgPersonService;

    @Autowired
    private MajorRepairOrgService majorRepairOrgService;

    @Autowired
    private CommonRoleBo commonRoleBo;

    @Autowired
    private StrategyConfig strategyConfig;

    @Autowired
    private LockTemplate lockTemplate;
    private static final String importExcelKey = "pmsx::PersonMangeEntryExcelTpl-import::id";
    private static final String EDIT_KEY = "pmsx::JobManage-edit::locked::id";

    @Autowired
    public void setJobPostAuthorizeService(JobPostAuthorizeService jobPostAuthorizeService) {
        this.jobPostAuthorizeService = jobPostAuthorizeService;
    }

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public PersonMangeVO detail(String id, String pageCode) throws Exception {
        PersonMange personMange =  personMangeMapper.getPersonManage(id);
//        PersonMange personMange = this.getById(id);
        PersonMangeVO result = BeanCopyUtils.convertTo(personMange, PersonMangeVO::new);
        setEveryName(Collections.singletonList(result));
        return result;
    }

    /**
     * 新增
     * <p>
     * * @param personMangeDTO
     */
    @Override
    public String create(PersonMangeDTO personMangeDTO) throws Exception {
        PersonMange personMange = BeanCopyUtils.convertTo(personMangeDTO, PersonMange::new);
        this.save(personMange);

        String rsp = personMange.getId();


        return rsp;
    }


    @Override
    public ObjectTreeInfoVO<TreeNodeVO<NodeVO<PersonManagePrepareTreeVO>>> getPrepareTree(TreeSelectDTO param) throws Exception {
        long allBefore = System.currentTimeMillis();
        long before = System.currentTimeMillis();
        List<MajorRepairOrg> list = majorRepairOrgService.getList(param.getRepairRound(), param.getRepairOrgId());
        long after = System.currentTimeMillis();
        log.info("one get RepairOrg:{}ms",after-before);
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        List<String> ids = list.stream().map(MajorRepairOrg::getId).collect(Collectors.toList());

        before = System.currentTimeMillis();
        PersonMangeMapper baseMapper = this.getBaseMapper();
        List<PersonTmpVO> personsTmp = baseMapper.getPersonManageTreeData(param.getKeyword(),ids);
        List<PersonMange> persons = BeanCopyUtils.convertListTo(personsTmp, PersonMange::new);
        //大修组织编号映射人员列表

        List<String> codeList = persons.stream().map(PersonMange::getNumber).collect(Collectors.toList());

        //为人员信息封装授权岗位信息
        if (!CollectionUtils.isEmpty(persons)){
            List<PersonMange> numberAndPostName = baseMapper.getJobPostNames(persons.get(0).getBaseCode(),codeList);
            Map<String, List<PersonMange>> numberToEntity = numberAndPostName.stream().collect(Collectors.groupingBy(PersonMange::getNumber));
            for (PersonMange person : persons) {
                List<PersonMange> entityList = numberToEntity.getOrDefault(person.getNumber(),null);
                if (!CollectionUtils.isEmpty(entityList)){
                    person.setJobPostName(entityList.stream().map(PersonMange::getJobPostName).collect(Collectors.joining(",")));
                }
            }
        }

        after = System.currentTimeMillis();
        log.info("two get persons:{}ms",after-before);

        //通过班组节点id获取人员信息
        LambdaQueryWrapperX<RelationOrgToPerson> relationWrapper = new LambdaQueryWrapperX<>(RelationOrgToPerson.class);
        relationWrapper.in(RelationOrgToPerson::getRepairOrgId, ids);

        before = System.currentTimeMillis();
        List<RelationOrgToPerson> relations = repairOrgPersonService.list(relationWrapper);
        after = System.currentTimeMillis();
        log.info("three get relations:{}ms",after-before);

        Map<String, List<RelationOrgToPerson>> orgIdToPersonIds = relations.stream()
                .collect(Collectors.groupingBy(RelationOrgToPerson::getRepairOrgId));

        before = System.currentTimeMillis();
        List<UserBaseCacheVO> userBaseCacheByCode = userRedisHelper.getUserBaseCacheByCode(CurrentUserHelper.getOrgId(), codeList);
        after = System.currentTimeMillis();
        log.info("four get userBaseCacheByCode:{}ms",after-before);

        Map<String, String> codeToName = new HashMap<>();
        if (!CollectionUtils.isEmpty(userBaseCacheByCode)){
            codeToName = userBaseCacheByCode.stream().collect(Collectors.toMap(UserBaseCacheVO::getCode, UserBaseCacheVO::getName));
        }

        Map<String, PersonMange> idToPerson = persons.stream().collect(Collectors.toMap(PersonMange::getId, Function.identity(), (v1, v2) -> v2));
        List<NodeVO<PersonManagePrepareTreeVO>> nodeList = new ArrayList<>();

        List<String> dataIds = new ArrayList<>();
        before = System.currentTimeMillis();
        List<String> userIds = list.stream().map(MajorRepairOrg::getRspUserId).collect(Collectors.toList());
        List<UserVO> users = userRedisHelper.getUserByIds(CurrentUserHelper.getOrgId(), userIds);
        Map<String, String> idToName = users.stream().filter(item -> StringUtils.hasText(item.getId())).collect(Collectors.toMap(UserVO::getId, UserVO::getName));
        after = System.currentTimeMillis();
        log.info("new redis to get info:{}",after-before);

        before = System.currentTimeMillis();
        for (MajorRepairOrg majorRepairOrg : list) {
                NodeVO<PersonManagePrepareTreeVO> tmp = new NodeVO<>();
                PersonManagePrepareTreeVO personManagePrepareTreeVO = new PersonManagePrepareTreeVO();
                tmp.setId(majorRepairOrg.getId());
                tmp.setRspUserId(majorRepairOrg.getRspUserId());
                tmp.setRspUserName(idToName.getOrDefault(majorRepairOrg.getRspUserId(), ""));
                tmp.setParentId(majorRepairOrg.getParentId());
                tmp.setNodeType(majorRepairOrg.getLevelType());
                tmp.setName(majorRepairOrg.getName());
                tmp.setCode(majorRepairOrg.getCode());
                tmp.setSort(majorRepairOrg.getSort());

                tmp.setData(personManagePrepareTreeVO);
                nodeList.add(tmp);

                dataIds.add(majorRepairOrg.getId());
        }
        after = System.currentTimeMillis();
        log.info("five  for the list:{}ms",after-before);

        List<NodeVO<PersonManagePrepareTreeVO>> dataNode = new ArrayList<>();
        Map<String, String> personIdToRelationId = relations.stream().collect(Collectors.toMap(RelationOrgToPerson::getPersonId, RelationOrgToPerson::getId, (k1, k2) -> k2));

        //数据排序
        orgIdToPersonIds.forEach((orgId, personsList) -> {
            personsList.sort(Comparator.comparing(RelationOrgToPerson::getCreateTime).reversed());
        });

        before = System.currentTimeMillis();

        Date date  =new Date();

        for (MajorRepairOrg majorRepairOrg : list) {
            List<RelationOrgToPerson> personsList = orgIdToPersonIds.getOrDefault(majorRepairOrg.getId(), null);
            if (!CollectionUtils.isEmpty(personsList)){
                List<String> personIds = personsList.stream().map(RelationOrgToPerson::getPersonId).collect(Collectors.toList());
                for (String personId : personIds) {
                    NodeVO<PersonManagePrepareTreeVO> tmp = new NodeVO<>();
                    PersonMange personMange = idToPerson.getOrDefault(personId, null);
                    if (personMange == null) {
                        continue;
                    }

                    String relationId = personIdToRelationId.getOrDefault(personId, "");

                    tmp.setId(relationId);
                    tmp.setParentId(majorRepairOrg.getId());
                    tmp.setName(majorRepairOrg.getName());
                    tmp.setRspUserId(majorRepairOrg.getRspUserId());
                    tmp.setRspUserName(majorRepairOrg.getRspUserName());
                    tmp.setCode(majorRepairOrg.getCode());
                    tmp.setChainPath(majorRepairOrg.getChainPath());
                    tmp.setNodeType(MajorRepairOrgEnum.LEVEL_TYPE_GROUP_BUSINESS_DATA.getCode());
                    tmp.setDataId(personMange.getId());
                    PersonManagePrepareTreeVO personManagePrepareTreeVO;
                    personManagePrepareTreeVO = BeanCopyUtils.convertTo(personMange, PersonManagePrepareTreeVO::new);
                    personManagePrepareTreeVO.setSex(personMange.getSex());
                    personManagePrepareTreeVO.setPersonId(personMange.getId());
                    personManagePrepareTreeVO.setBasicUserId(personMange.getBasicUserId());
                    personManagePrepareTreeVO.setPlanInDate(personMange.getInDate());

                    //通过计划入场时间计算入场倒计时 如果没有计划入场时间则为null
                    if (Objects.nonNull(personMange.getInDate())){
                        long diff = Math.abs(personMange.getInDate().getTime() - new Date().getTime());
                        //转换为天数
                        long diffInDays = diff / (24 * 60 * 60 * 1000);
                        if (personMange.getInDate().getTime()>new Date().getTime()){
                            personManagePrepareTreeVO.setInDays(diffInDays);
                        }else{
                            personManagePrepareTreeVO.setInDays(-diffInDays);
                        }
                    }else {
                        personManagePrepareTreeVO.setInDays(null);
                    }
                    personManagePrepareTreeVO.setPlanOutDate(personMange.getOutDate());


                    personManagePrepareTreeVO.setPersonCount(1);
                    personManagePrepareTreeVO.setNewPersonCount(personMange.getNewcomer()!=null&&personMange.getNewcomer() ? 1 : 0);
                    personManagePrepareTreeVO.setNoPlanIn(personMange.getInDate() == null ? 1 : 0);
                    personManagePrepareTreeVO.setUserName(codeToName.getOrDefault(personMange.getNumber(),""));
                    personManagePrepareTreeVO.setCode(personMange.getNumber());
                    personManagePrepareTreeVO.setSupposedPlanCount((personMange.getInDate()!=null&&personMange.getInDate().before(new Date()))?1:0);
                    personManagePrepareTreeVO.setSupposedActCount(personMange.getActInDate()!=null?1:0);
                    personManagePrepareTreeVO.setActInDate(personMange.getActInDate());
                    personManagePrepareTreeVO.setJobPostName(personMange.getJobPostName());
                    tmp.setData(personManagePrepareTreeVO);
                    dataNode.add(tmp);

                    dataIds.add(personMange.getId());
                }

            }
        }
        after = System.currentTimeMillis();
        log.info("six:{}ms",after-before);

        //封装树结构
        Map<String, List<NodeVO<PersonManagePrepareTreeVO>>> idToNode = dataNode.stream()
                            .filter(nodeVO -> StringUtils.hasText(nodeVO.getParentId()))
                            .collect(Collectors.groupingBy(NodeVO::getParentId));

        before = System.currentTimeMillis();

        Map<String, Set<String>> currentPermissions = commonRoleBo.currentUserRoles(dataIds, list);
        after = System.currentTimeMillis();
        log.info("seven:{}ms",after-before);

        //去重统计
        //过滤父级节点为空的数据且节点不为顶级节点
        List<NodeVO<PersonManagePrepareTreeVO>> voList = nodeList.stream().filter(x -> StrUtil.equals("0", x.getId()) || StrUtil.isNotBlank(x.getParentId())).collect(Collectors.toList());
        nodeList = PersonCountUtils.countPeoplePrepare(voList, dataNode, persons);

        before = System.currentTimeMillis();
        TreeInfoProcessor<NodeVO<PersonManagePrepareTreeVO>> processor = new TreeInfoProcessor<>(
                nodeList,
                NodeVO::getId,
                NodeVO::getParentId,
                NodeVO::getRspUserId,
                CurrentUserHelper.getCurrentUserId(),
                NodeVO::getSort,
                currentPermissions,
                false,
                false,
                idToNode
        );
        after = System.currentTimeMillis();
        log.info("seven:{}ms",after-before);

        ObjectTreeInfoVO<TreeNodeVO<NodeVO<PersonManagePrepareTreeVO>>> res = new ObjectTreeInfoVO<>();
        res.setTreeNodeVOList(processor.getRootList());
        res.setParenIdList(processor.getParenIdList());
        res.setOneOrTwoIdList(processor.getOneOrTwoIdList());
        long allAfter = System.currentTimeMillis();
        log.info("all:{}ms",allAfter-allBefore);
        return res;
    }

    @Override
    public ObjectTreeInfoVO<TreeNodeVO<NodeVO<PersonManageExecuteTreeVO>>> getExecuteTree(TreeSelectDTO param) throws Exception {
        List<MajorRepairOrg> list = majorRepairOrgService.getList(param.getRepairRound(), param.getRepairOrgId());
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        List<String> ids = list.stream().map(MajorRepairOrg::getId).collect(Collectors.toList());
        PersonMangeMapper baseMapper = this.getBaseMapper();
        List<PersonTmpVO> personsTmp = baseMapper.getPersonManageTreeData(param.getKeyword(),ids);
        List<PersonMange> persons = BeanCopyUtils.convertListTo(personsTmp, PersonMange::new);

        //通过班组节点id获取人员信息
        LambdaQueryWrapperX<RelationOrgToPerson> relationWrapper = new LambdaQueryWrapperX<>(RelationOrgToPerson.class);
        relationWrapper.in(RelationOrgToPerson::getRepairOrgId, ids);
        List<RelationOrgToPerson> relations = repairOrgPersonService.list(relationWrapper);

        Map<String, List<RelationOrgToPerson>> orgIdToPersonIds = relations.stream()
                .collect(Collectors.groupingBy(RelationOrgToPerson::getRepairOrgId));

        List<String> codeList = persons.stream().map(PersonMange::getNumber).collect(Collectors.toList());

        List<UserBaseCacheVO> userByIds = userRedisHelper.getUserBaseCacheByCode(CurrentUserHelper.getOrgId(),codeList);
        Map<String, String> codeToName = new HashMap<>();
        if (!CollectionUtils.isEmpty(userByIds)){
            codeToName = userByIds.stream().collect(Collectors.toMap(UserBaseCacheVO::getCode, UserBaseCacheVO::getName));
        }

        Map<String, PersonMange> idToPerson = persons.stream().collect(Collectors.toMap(PersonMange::getId, Function.identity(),(v1,v2)->v2));
        List<NodeVO<PersonManageExecuteTreeVO>> nodeList = new ArrayList<>();

        List<String> dataIds = new ArrayList<>();
        List<String> userIds = list.stream().map(MajorRepairOrg::getRspUserId).collect(Collectors.toList());
        List<UserVO> users = userRedisHelper.getUserByIds(CurrentUserHelper.getOrgId(), userIds);
        Map<String, String> idToName = users.stream().filter(item -> StringUtils.hasText(item.getId())).collect(Collectors.toMap(UserVO::getId, UserVO::getName));

        //节点数据
        for (MajorRepairOrg majorRepairOrg : list) {

            NodeVO<PersonManageExecuteTreeVO> tmp = new NodeVO<>();
            PersonManageExecuteTreeVO personManageExecuteTreeVO = new PersonManageExecuteTreeVO();
            tmp.setId(majorRepairOrg.getId());
            tmp.setRspUserId(majorRepairOrg.getRspUserId());
            tmp.setRspUserName(idToName.getOrDefault(majorRepairOrg.getRspUserId(), ""));
            tmp.setParentId(majorRepairOrg.getParentId());
            tmp.setName(majorRepairOrg.getName());
            tmp.setNodeType(majorRepairOrg.getLevelType());
            tmp.setCode(majorRepairOrg.getCode());
            tmp.setSort(majorRepairOrg.getSort());

            tmp.setData(personManageExecuteTreeVO);
            nodeList.add(tmp);

            dataIds.add(majorRepairOrg.getId());
        }

        List<NodeVO<PersonManageExecuteTreeVO>> dataNode = new ArrayList<>();
        Map<String, String> personIdToRelationId = relations.stream().collect(Collectors.toMap(RelationOrgToPerson::getPersonId, RelationOrgToPerson::getId, (k1, k2) -> k1));

        //数据排序
        orgIdToPersonIds.forEach((orgId, personsList) -> {
            personsList.sort(Comparator.comparing(RelationOrgToPerson::getCreateTime).reversed());
        });

        //离场原因字典获取
        List<DictValueVO> byDictNumber = dictRedisHelper.getByDictNumber(DictConstant.PMS_OUT_FACTORY_REASON, CurrentUserHelper.getOrgId());
        Map<String, String> numberToDescription = byDictNumber.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));

        //业务数据
        for (MajorRepairOrg majorRepairOrg : list) {
            List<RelationOrgToPerson> personsList = orgIdToPersonIds.getOrDefault(majorRepairOrg.getId(), null);
            if (!CollectionUtils.isEmpty(personsList)){
                List<String> personIds = personsList.stream().map(RelationOrgToPerson::getPersonId).collect(Collectors.toList());
                for (String personId : personIds) {
                    NodeVO<PersonManageExecuteTreeVO> tmp = new NodeVO<>();
                    PersonMange personMange = idToPerson.get(personId);
                    if (personMange == null) {
                        continue;
                    }

                    String relationId = personIdToRelationId.getOrDefault(personId, "");

                    tmp.setId(relationId);
                    tmp.setParentId(majorRepairOrg.getId());
                    tmp.setName(majorRepairOrg.getName());
                    tmp.setRspUserId(majorRepairOrg.getRspUserId());
                    tmp.setChainPath(majorRepairOrg.getChainPath());
                    tmp.setDataId(personMange.getId());
                    tmp.setRspUserName(majorRepairOrg.getRspUserName());
                    tmp.setNodeType(MajorRepairOrgEnum.LEVEL_TYPE_GROUP_BUSINESS_DATA.getCode());

                    PersonManageExecuteTreeVO personManageExecuteTreeVO;
                    personManageExecuteTreeVO = BeanCopyUtils.convertTo(personMange, PersonManageExecuteTreeVO::new);
                    personManageExecuteTreeVO.setBasicUserId(personMange.getBasicUserId());
                    personManageExecuteTreeVO.setPersonId(personMange.getId());
                    personManageExecuteTreeVO.setSex(personMange.getSex());
                    personManageExecuteTreeVO.setNoActIn(personMange.getActInDate() == null ? 1 : 0);
                    personManageExecuteTreeVO.setActInCount(personMange.getStatus()==1? 1:0);
                    personManageExecuteTreeVO.setActOutCount(personMange.getStatus()==2? 1:0);


                    personManageExecuteTreeVO.setActOutNotReportCount((personMange.getStatus()==2&&personMange.getActOutDate()==null) ? 1:0);
                    personManageExecuteTreeVO.setUserName(codeToName.getOrDefault(personMange.getNumber(),""));
                    personManageExecuteTreeVO.setCode(personMange.getNumber());
                    if (StringUtils.hasText(personMange.getLeaveReason())){
                        personManageExecuteTreeVO.setLeaveReasonName(numberToDescription.getOrDefault(personMange.getLeaveReason(), ""));
                    }

                    tmp.setData(personManageExecuteTreeVO);
                    dataNode.add(tmp);

                    dataIds.add(personId);
                }

            }
        }
        //封装树结构
        Map<String, List<NodeVO<PersonManageExecuteTreeVO>>> idToNode = dataNode.stream().collect(Collectors.groupingBy(NodeVO::getParentId));

        Map<String, Set<String>> currentPermissions = commonRoleBo.currentUserRoles(dataIds, list);
        //去重统计
        //过滤父级节点为空的数据且节点不为顶级节点
        List<NodeVO<PersonManageExecuteTreeVO>> voList = nodeList.stream().filter(x -> StrUtil.equals("0", x.getId()) || StrUtil.isNotBlank(x.getParentId())).collect(Collectors.toList());
        nodeList = PersonCountUtils.countPeopleExecute(voList, dataNode, persons, majorRepairOrgService);

        TreeInfoProcessor<NodeVO<PersonManageExecuteTreeVO>> processor = new TreeInfoProcessor<>(
                nodeList,
                NodeVO::getId,
                NodeVO::getParentId,
                NodeVO::getRspUserId,
                CurrentUserHelper.getCurrentUserId(),
                NodeVO::getSort,
                currentPermissions,
                false,
                false,
                idToNode
        );
        ObjectTreeInfoVO<TreeNodeVO<NodeVO<PersonManageExecuteTreeVO>>> res = new ObjectTreeInfoVO<>();
        res.setTreeNodeVOList(processor.getRootList());
        res.setParenIdList(processor.getParenIdList());
        res.setOneOrTwoIdList(processor.getOneOrTwoIdList());
        return res;

    }


    @Override
    public Boolean existData(List<String> nodeIdList) {
        LambdaQueryWrapperX<RelationOrgToPerson> wrapperX = new LambdaQueryWrapperX<>(RelationOrgToPerson.class);
        wrapperX.in(RelationOrgToPerson::getRepairOrgId, nodeIdList);
        Long count = wrapperX.count();

        return count > 0;
    }

    @Override
    public void removeNew(List<PersonRemoveDTO> param) {
         if (!CollectionUtils.isEmpty(param)){
             PersonMangeMapper baseMapper = this.getBaseMapper();
             List<String> allPersonIds = param.stream().map(PersonRemoveDTO::getPersonId).distinct().collect(Collectors.toList());
             List<PersonMange> personMangeList = personMangeMapper.queryListByIdList(allPersonIds);

             baseMapper.removeNew(param);
             //删除无关系人员数据
             List<String> personIds = param.stream().map(PersonRemoveDTO::getPersonId).collect(Collectors.toList());
             LogRecordContext.putVariable("userCodes", personMangeList.stream().map(PersonMange::getNumber).collect(Collectors.joining(",")));
             baseMapper.removeNoRelation(personIds);
             String orgId =  param.get(0).getRepairOrgId();
             MajorRepairOrg majorRepairOrg = majorRepairOrgService.getById(orgId);
             LogRecordContext.putVariable("repairRound", majorRepairOrg.getRepairRound());
             LogRecordContext.putVariable("orgName", majorRepairOrg.getName());
         }
    }

    @Override
    public Boolean editNew(PersonMangeDTO personMangeDTO) throws Exception {
        String id = personMangeDTO.getId();
        LockInfo lock = lockTemplate.lock(EDIT_KEY + id, 5000, 3000);
        if (Objects.isNull(lock)){
            throw new BaseException(MyExceptionCode.ERROR_NOT_PERMISSION.getErrorCode(),"当前数据正在被修改，请稍后修改");
        }
        try {
            // 获取当前数据
            PersonMange currentData = personMangeMapper.getPersonManage(id);
            if(Objects.isNull(currentData)){
                throw new BaseException(HttpStatus.BAD_REQUEST.value(),"数据不存在");
            }
            // 如果当前数据被删除或者已离场 被重新入场  // todo 如果加人的时候 基地绝对不能为空
//        boolean isOutData = false; //是否是离厂后的数据
            if(!Objects.equals(currentData.getStatus(),StatusEnum.ENABLE.getIndex())){
                currentData.setLogicStatus(StatusEnum.ENABLE.getIndex());
                currentData.setActOutDate(null);
                currentData.setLeaveReason(null);
                currentData.setIsFinishOutHandover(null);
                currentData.setIsAgainIn(null);
                currentData.setLeaveRemark(null);
            }
            //正常入场
            currentData.setContactUser(personMangeDTO.getContactUser());
            currentData.setContactUserName(personMangeDTO.getContactUserName());
            currentData.setIsBasePermanent(personMangeDTO.getIsBasePermanent());
            currentData.setLeaveReason(personMangeDTO.getLeaveReason());
            currentData.setIsAgainIn(personMangeDTO.getIsAgainIn());
            currentData.setLeaveRemark(personMangeDTO.getLeaveRemark());
            currentData.setIsFinishOutHandover(personMangeDTO.getIsFinishOutHandover());
            currentData.setNewcomer(personMangeDTO.getNewcomer());
            currentData.setStatus(StatusEnum.ENABLE.getIndex());
            currentData.setIsBasePermanent(personMangeDTO.getIsBasePermanent());
            currentData.setInDays(this.getInDate(currentData.getInDate()));
            this.updateById(currentData);
            // 插入台账
            String uniqueId = String.format("%s_%s_%s", currentData.getId(),currentData.getBaseCode(), DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN));
//        PersonManageLedger personManageLedger = personManageLedgerService.getById(id);
            PersonMangeVO result = BeanCopyUtils.convertTo(currentData, PersonMangeVO::new);
            setEveryName(Collections.singletonList(result));
            PersonManageLedger personManageLedger = BeanCopyUtils.convertTo(result, PersonManageLedger::new);
            personManageLedger.setId(null);
            personManageLedger.setCreateTime(null);
            personManageLedger.setCreatorId(null);
            personManageLedger.setModifyTime(null);
            personManageLedger.setModifyId(null);
            personManageLedger.setUniqueId(uniqueId);
            personManageLedger.setClassName(null);
            personManageLedger.setType(PersonManageLedgerTypeEnum.INPUT.getKey());
            personManageLedger.setPersonManageId(id);
            personManageLedgerService.save(personManageLedger);
            String personLedgerId = personManageLedger.getId();

            // 需要将台账数据插入 作业信息
            jobPostAuthorizeService.updatePersonLedgerId(null,personLedgerId, id,currentData.getNumber(),id);
            // 修改生命周期状态
            List<String> jobIdList =jobPostAuthorizeService.listByPersonId(id);
            if(!CollectionUtils.isEmpty(jobIdList)){
                jobNodeStatusService.setNodeStatusByIdList(jobIdList,"personJoin");
            }
            LogRecordContext.putVariable("baseCode",personManageLedger.getBaseCode());
        }catch (Exception e){
            throw new BaseException(HttpStatus.INTERNAL_SERVER_ERROR.value(),"数据保存失败");
        }finally {
            lockTemplate.releaseLock(lock);
        }
        return Boolean.TRUE;
    }


    @Override
    public PersonPrepareVO editPrepare(PersonPrepareDTO param) throws Exception {
        String personId = param.getPersonId();
        PersonMange person = getById(personId);
        if (Objects.isNull(person)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "数据不存在，请刷新后重试");
        }
        Date actInDate = param.getActInDate();
        if (actInDate !=null&&actInDate.after(new Date())){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(),"入场时间不能晚于当前时间");
        }

        boolean editDate = false;

        PersonPrepareVO res = BeanCopyUtils.convertTo(param, PersonPrepareVO::new);
        if(Objects.nonNull(param.getNewcomer())){
            person.setNewcomer(param.getNewcomer());
        }
        //修改入场日期
        if (actInDate !=null){
            person.setActInDate(actInDate);
            person.setStatus(StatusEnum.ENABLE.getIndex());
            person.setInDays(TooUtils.getInDate(actInDate));
            editDate = true;

            res.setStatus(StatusEnum.ENABLE.getIndex());
        }else{
            res.setStatus(StatusEnum.DISABLE.getIndex());
        }
        //清空离场信息
        person.setActOutDate(null);
        person.setIsFinishOutHandover(false);
        person.setLeaveReason(null);
        person.setLeaveRemark(null);
        boolean sameDay = TooUtils.isSameDay(actInDate);
        //如果是常驻人员直接清空实际入场时间
        person.setIsBasePermanent(param.getIsBasePermanent());
        if (Objects.nonNull(person.getIsBasePermanent())){
            if(person.getIsBasePermanent()){
                person.setStatus(StatusEnum.ENABLE.getIndex());
                res.setStatus(StatusEnum.ENABLE.getIndex());
            }
        }
        if(Objects.nonNull(person.getNewcomer())&&!person.getNewcomer()){
            person.setNewcomerMatchPerson(null);
            res.setNewcomerMatchPerson(null);
        }else {
            if(Objects.isNull(person.getNewcomer())){
                person.setNewcomer(Boolean.FALSE);
            }
            person.setNewcomerMatchPerson(param.getNewcomerMatchPerson());
        }
        person.setNewcomerMatchPersonCode(param.getNewcomerMatchPersonCode());
        person.setInDate(param.getPlanInDate());
        person.setOutDate(param.getPlanOutDate());
        person.setActInDate(param.getActInDate());
        boolean b = updateById(person);
        if (editDate){
            //插入或者更新台账
            saveLedger(person,person.getActInDate());
        }
        LogRecordContext.putVariable("userNumber",person.getNumber());
        LogRecordContext.putVariable("repairOrgName","");
        LogRecordContext.putVariable("repairRound","");
        return res;
    }


    @Override
    public PersonExecuteVO editExecute(PersonExecuteDTO param) throws Exception {
        String personId = param.getPersonId();
        PersonMange person = getById(personId);
        if (Objects.isNull(person)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "数据不存在，请刷新后重试");
        }

        if(!person.getStatus().equals(StatusEnum.ENABLE.getIndex())){
            throw new BaseException(MyExceptionCode.ERROR_NOT_PERMISSION.getErrorCode(),"人员未入场或已离场，无法修改");
        }

        Date actOutDate = param.getActOutDate();
        if (actOutDate !=null&&actOutDate.after(new Date())){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(),"离场时间不能晚于当前时间");
        }

        boolean editDate = false;
        PersonExecuteVO res = BeanCopyUtils.convertTo(param, PersonExecuteVO::new);
        if (actOutDate !=null){
            person.setActOutDate(actOutDate);
            person.setStatus(StatusEnum.DRAFT.getIndex());
            editDate = true;
            res.setStatus(StatusEnum.DRAFT.getIndex());
        }else{
            res.setStatus(StatusEnum.ENABLE.getIndex());
        }
        person.setIsBasePermanent(param.getIsBasePermanent());
        person.setIsAgainIn(param.getIsAgainIn());
        person.setLeaveReason(param.getLeaveReason());
        person.setActOutDate(param.getActOutDate());
        person.setIsFinishOutHandover(param.getIsFinishOutHandover());

        boolean b = updateById(person);

        if (editDate){
            //插入或者更新台账
            saveLedger(person,person.getActOutDate());
        }else {
            saveLedger(person,new Date());
        }
        LogRecordContext.putVariable("userNumber",person.getNumber());
        LogRecordContext.putVariable("repairOrgName","");
        LogRecordContext.putVariable("repairRound","");
        return res;
    }


    @Override
    public List<PersonTmpVO> personDown(PersonDownDTO personDownDTO) throws Exception {
        //获取大修组织下的子组织
        String majorRepairOrg = personDownDTO.getMajorRepairOrg();
        List<MajorRepairOrg> orgList = majorRepairOrgService.getList(personDownDTO.getRepairRound(), null);
        List<String> orgIds = orgList.stream().filter(org-> org.getChainPath().contains(majorRepairOrg)).map(MajorRepairOrg::getId).collect(Collectors.toList());

        //获取业务数据
        List<PersonTmpVO> data = strategyConfig.getStrategy(personDownDTO.getPersonDeepEnum().getStatisticFieldName()).strategy(orgIds, personDownDTO.getKeyword());
        if(Objects.equals(personDownDTO.personDeepEnum.getStatisticFieldName(), PersonDeepEnum.requiredPrepImplCount.getStatisticFieldName())){// 如果是  大修准备，大修实施 必填未填统计处理 那么
            Date date = new Date();
            data = data.stream().filter(item -> {
                Boolean isBasePermanent = item.getIsBasePermanent();
                if (Objects.equals(isBasePermanent, Boolean.TRUE)) {
                    return Boolean.TRUE;
                } else {
                    Date inDate = item.getPlanInDate();
                    return Objects.isNull(isBasePermanent) || Objects.isNull(item.getNewcomer()) || Objects.isNull(inDate)
                            || Objects.isNull(item.getPlanOutDate()) || !TooUtils.isBeforeDay(inDate, date);
                }
            }).collect(Collectors.toList());
        }
        //赋权
        if (!CollectionUtils.isEmpty(data)){
            Map<String, Set<String>> personIdToRoleMap = commonRoleBo.currentUserRolesList(data, orgList);
            for (PersonTmpVO re : data) {
                re.setRoleList(personIdToRoleMap.getOrDefault(re.getId(), new HashSet<>()));
            }
        }

        //人员去重 有权限的优先取有权限的
        Map<String, PersonTmpVO> personIdToEntity = data.stream()
                .collect(Collectors.toMap(PersonTmpVO::getPersonId, Function.identity(), (v1, v2) -> CollectionUtils.isEmpty(v1.getRoleList()) ? v2 : v1));

        return new ArrayList<>(personIdToEntity.values());
    }

    /**
     * 编辑
     * <p>
     * * @param personMangeDTO
     */
    @Override
    public Boolean edit(PersonMangeDTO personMangeDTO) throws Exception {
        String id = personMangeDTO.getId();
        // 获取当前数据
        PersonMange currentData = personMangeMapper.getPersonManage(id);
        if(Objects.isNull(currentData)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(),"数据不存在");
        }
        // 如果当前数据被删除或者已离场 被重新入场  // todo 如果加人的时候 基地绝对不能为空
//        boolean isOutData = false; //是否是离厂后的数据
        if(!Objects.equals(currentData.getStatus(),StatusEnum.ENABLE.getIndex())){
            currentData.setLogicStatus(StatusEnum.ENABLE.getIndex());
            currentData.setActOutDate(null);
            currentData.setLeaveReason(null);
            currentData.setIsFinishOutHandover(null);
            currentData.setIsAgainIn(null);
            currentData.setLeaveRemark(null);
        }
        //正常入场
        currentData.setActOutDate(null);
        currentData.setHeightStr(personMangeDTO.getHeightStr());
        currentData.setWeightStr(personMangeDTO.getWeightStr());
        currentData.setContactUserName(personMangeDTO.getContactUserName());
        currentData.setActInDate(personMangeDTO.getActInDate());
        currentData.setNewcomer(personMangeDTO.getNewcomer());
        currentData.setStatus(StatusEnum.ENABLE.getIndex());
        currentData.setChemicalToxinUseJob(personMangeDTO.getChemicalToxinUseJob());
        currentData.setIsBasePermanent(personMangeDTO.getIsBasePermanent());
        currentData.setIsJoinYearMajorRepair(personMangeDTO.getIsJoinYearMajorRepair());
        currentData.setIsHeightMeasurePerson(personMangeDTO.getIsHeightMeasurePerson());
        currentData.setInDays(this.getInDate(currentData.getInDate()));
        this.updateById(currentData);
        saveLedger(currentData,currentData.getActInDate());
//        // 修改生命周期状态
//        List<String> jobIdList =jobPostAuthorizeService.listByPersonId(id);
//        if(!CollectionUtils.isEmpty(jobIdList)){
//            jobNodeStatusService.setNodeStatusByIdList(jobIdList,"personJoin");
//        }
//        LogRecordContext.putVariable("baseCode",personManageLedger.getBaseCode());
        return Boolean.TRUE;
    }

    private Boolean saveLedger(PersonMange currentData,Date inOrOutDate) throws Exception {



        String id = currentData.getId();
        // 插入台账
        String uniqueId = String.format("%s_%s_%s", currentData.getId(),currentData.getBaseCode(), DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN));
//        PersonManageLedger personManageLedger = personManageLedgerService.getById(id);
        PersonMangeVO result = BeanCopyUtils.convertTo(currentData, PersonMangeVO::new);
        setEveryName(Collections.singletonList(result));
        PersonManageLedger personManageLedger = BeanCopyUtils.convertTo(result, PersonManageLedger::new);
        personManageLedger.setId(null);
        personManageLedger.setCreateTime(null);
        personManageLedger.setCreatorId(null);
        personManageLedger.setModifyTime(null);
        personManageLedger.setModifyId(null);
        personManageLedger.setUniqueId(uniqueId);
        personManageLedger.setClassName(null);
        personManageLedger.setType(PersonManageLedgerTypeEnum.INPUT.getKey());
        personManageLedger.setPersonManageId(id);
        personManageLedger.setId(currentData.getId()+TooUtils.getCurrentDate(TooUtils.yyyyMMdd,inOrOutDate));

        PersonManageLedger byId = personManageLedgerService.getById(personManageLedger.getId());

        if (Objects.isNull(byId)){
            personManageLedgerService.save(personManageLedger);
        }else {
            personManageLedgerService.updateById(personManageLedger);
        }

        String personLedgerId = personManageLedger.getId();

        // 需要将台账数据插入 作业信息
        jobPostAuthorizeService.updatePersonLedgerId(null,personLedgerId, id,currentData.getNumber(),id);
        LogRecordContext.putVariable("name",byId.getName());
        return Boolean.TRUE;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<PersonMangeVO> pages(Page<PersonMangeDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<PersonMange> condition = new LambdaQueryWrapperX<>(PersonMange.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(PersonMange::getCreateTime);
        condition.in(PersonMange::getStatus,Arrays.asList(StatusEnum.ENABLE.getIndex(),StatusEnum.DISABLE.getIndex()));
        Page<PersonMange> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), PersonMange::new));
        PersonMangeDTO personMange = pageRequest.getQuery();
        if (!Objects.isNull(personMange)) {
            if (StringUtils.hasText(personMange.getBaseCode())) {
                condition.eq(PersonMange::getBaseCode, personMange.getBaseCode());
            }
        }
        PageResult<PersonMange> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<PersonMangeVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<PersonMangeVO> vos = BeanCopyUtils.convertListTo(page.getContent(), PersonMangeVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void setEveryName(List<PersonMangeVO> vos) throws Exception {
        if (CollectionUtils.isEmpty(vos)) {
            return;
        }
        List<String> codeList = new ArrayList<>();
        //  临时处理 单独构建 用户ID列表 查询数据，处理历史数据查询回显
        List<String> userIdList = new ArrayList<>();
        List<String> relationMajorIdList = new ArrayList<>();
        for (PersonMangeVO vo : vos) {
            codeList.add(vo.getNumber());
            if (StringUtils.hasText(vo.getNewcomerMatchPerson())) {
                userIdList.add(vo.getNewcomerMatchPerson());
            }
            if (StringUtils.hasText(vo.getParticipateONot())) {
                relationMajorIdList.add(vo.getParticipateONot());
            }
        }
        List<String> disTinctCodeList = codeList.stream().distinct().collect(Collectors.toList());
        // 临时处理，后续会将 基础用户basic_user 加入缓存
        Map<String, BasicUser> basicUserMap = basicUserService.getMapByNumberList(disTinctCodeList);
        List<UserVO> userVOList = userRedisHelper.getUserByIds(userIdList);
        Map<String, String> idToName = new HashMap<>();
        if (!CollectionUtils.isEmpty(relationMajorIdList)) {
            idToName = majorRepairPlanService.getSipMapList(relationMajorIdList);
        }

        Map<String, String> userIdToEntity = new HashMap<>();
        if (!CollectionUtils.isEmpty(userVOList)) {
            for (UserVO simpleUser : userVOList) {
                userIdToEntity.put(simpleUser.getId(), simpleUser.getName());
            }
        }

        List<DictValueVO> outFactoryList= dictRedisHelper.getDictListByCode(PMS_OUT_FACTORY_REASON);
        Map<String,String> numberToReson = new HashMap<>();
        if(!CollectionUtils.isEmpty(outFactoryList)){
            for (DictValueVO dictValueVO : outFactoryList) {
                numberToReson.put(dictValueVO.getNumber(),dictValueVO.getDescription());
            }
        }


        List<DictValueVO> dictValueVOList = dictRedisHelper.getDictListByCode(PMS_SPECIALI_OP);
        Map<String, String> numberToDesc = dictValueVOList.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (k1, k2) -> k1));

        List<DictValueVO> applicationDict = dictRedisHelper.getDictListByCode(TECHNICAL_APPLICATION);
        Map<String, String> applicationKeyToDesc = applicationDict.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (k1, k2) -> k1));

        Map<String, String> finalIdToName = idToName;
        vos.forEach(vo -> {

            if (StrUtil.isEmpty(vo.getDeptName()) || StrUtil.isEmpty(vo.getNowPosition())) {
                BasicUser basicUser = basicUserMap.get(vo.getNumber());
                if (Objects.nonNull(basicUser)) {
                    vo.setName(basicUser.getFullName());
                    vo.setSex(basicUser.getSex());
                    vo.setIdCard(basicUser.getIdCard());
                    vo.setPoliticalAffiliation(basicUser.getPoliticalAffiliation());
                    vo.setNature(basicUser.getPersonnelNature());
                    vo.setCompanyName(basicUser.getCompanyName());
                    vo.setDeptName(basicUser.getDeptName());
                    vo.setInstituteName(basicUser.getInstituteName());
                    vo.setNowPosition(basicUser.getNowPosition());

                    vo.setPersonnelNature(basicUser.getPersonnelNature());
                    vo.setNation(basicUser.getNation());
                    vo.setHomeTown(basicUser.getHomeTown());
                    vo.setBirthPlace(basicUser.getBirthPlace());
                    vo.setNewPosition(basicUser.getNowPosition());
                    vo.setJobTitle(basicUser.getJobTitle());
                    vo.setJoinWorkTime(basicUser.getJoinWorkTime());
                    vo.setAddUnitTime(basicUser.getAddUnitTime());
                    vo.setAddZghTime(basicUser.getAddZghTime());
                }
            }
            String code = vo.getNewcomerMatchPerson();
            if (StringUtils.hasText(code)) {
                BasicUser basicUser1 = basicUserMap.get(code);
                vo.setNewcomerMatchPersonCodeName(null == basicUser1 ? "" : basicUser1.getFullName());
            }
            if (StringUtils.hasText(vo.getWorkType())) {
                vo.setWorkTypeName(applicationKeyToDesc.getOrDefault(vo.getWorkType(), ""));
            }
            if (StringUtils.hasText(vo.getSpeTaskCertSit())) {
                vo.setSpeTaskCertSitName(numberToDesc.getOrDefault(vo.getSpeTaskCertSit(), ""));
            }
            String person = vo.getNewcomerMatchPerson();
            if (StringUtils.hasText(person)) {
                vo.setNewcomerMatchPersonCodeName(userIdToEntity.getOrDefault(person, ""));
            }
            if (StringUtils.hasText(vo.getParticipateONot())) {
                vo.setParticipateONotName(finalIdToName.getOrDefault(vo.getParticipateONot(), ""));
            }
            if(StringUtils.hasText(vo.getLeaveReason())){
                vo.setLeaveReasonName(numberToReson.getOrDefault(vo.getLeaveReason(),""));
            }
        });


    }

    @Override
    public Map<String,PersonMange> addBatchByCodeList(AddParamDTO addParamDTO) {
        List<String> codeList = addParamDTO.getCodeList();
        if (CollectionUtils.isEmpty(codeList)) {
            return new HashMap();
        }
        String baseCode = addParamDTO.getBaseCode();

        List<PersonMange> personMangeList = this.getPersonMangeList(baseCode, codeList);
        Map<String,PersonMange> numberToEnMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(personMangeList)) {
            numberToEnMap = personMangeList.stream().collect(Collectors.toMap(PersonMange::getNumber, Function.identity(), (k1, k2) -> k2));
            numberToEnMap.forEach((k, v) -> {
                codeList.remove(k);
            });
            if(CollectionUtils.isEmpty(codeList)){
                return numberToEnMap;
            }
        }

        String basePlaceCode = addParamDTO.getBaseCode();
        if(StrUtil.isEmpty(basePlaceCode)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(),"基地数据不存在");
        }
        String basePlaceName= basePlaceService.getNameByCode(basePlaceCode);
        Map<String, BasicUser> numberToEntity = basicUserService.getMapByNumberList(codeList);
        List<PersonMange> personManges = new ArrayList<>();
        for (Map.Entry<String, BasicUser> stringBasicUserEntry : numberToEntity.entrySet()) {
            BasicUser basicUser = stringBasicUserEntry.getValue();
            PersonMange personMange = new PersonMange();
            personMange.setId(classRedisHelper.getUUID(PersonMange.class.getSimpleName()));
            personMange.setBaseCode(basePlaceCode);
            personMange.setBaseName(basePlaceName);
            personMange.setNumber(basicUser.getUserCode());
            personMange.setStatus(StatusEnum.DISABLE.getIndex());
            personManges.add(personMange);
            numberToEnMap.put(personMange.getNumber(),personMange);
        }
        if (CollectionUtils.isEmpty(personManges)) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "所选用户未在库中查询到，请刷新缓存或更新基础库");
        }
        boolean save = this.saveBatch(personManges);
        return numberToEnMap;
    }


    List<String> checkPersonRelation(String repairOrgId,List<String> personCodes){
        LambdaQueryWrapperX<RelationOrgToPerson> wrapperX = new LambdaQueryWrapperX<>(RelationOrgToPerson.class);
        wrapperX.innerJoin(PersonMange.class, PersonMange::getId, RelationOrgToPerson::getPersonId);
        wrapperX.eq(RelationOrgToPerson::getRepairOrgId,repairOrgId);
        wrapperX.in(PersonMange::getNumber, personCodes);
        wrapperX.selectAs(PersonMange::getNumber, RelationOrgToPerson::getNumber);
        List<RelationOrgToPerson> list = repairOrgPersonService.list(wrapperX);
        if (CollectionUtils.isEmpty(list)){
            return personCodes;
        }
        List<String> existNumbers = list.stream().map(RelationOrgToPerson::getNumber).collect(Collectors.toList());
        personCodes.removeAll(existNumbers);
        return personCodes;

    }

    @Override
    public Map<String, PersonMange> addBatchByCodeListNew(AddParamDTO addParamDTO) {
        List<String> personCodeList = addParamDTO.getCodeList();
        String repairOrgId = addParamDTO.getRepairOrgId();
        String baseCode = addParamDTO.getBaseCode();
        if (CollectionUtils.isEmpty(personCodeList)){
            return null;
        }
        if (!StringUtils.hasText(repairOrgId)){
            throw new BaseException(MyExceptionCode.ERROR_PARAM.getErrorCode(), "大修轮次异常");
        }
        String baseName = basePlaceService.getNameByCode(baseCode);
        if (StrUtil.isEmpty(baseName)) {
            return new HashMap<>();
        }
        // 获取人员在所处基地是否存在数据
        List<PersonMange> personMangeList = getPersonMangeList(baseCode,personCodeList);
        Map<String, PersonMange> numberToEnMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(personMangeList)) {
            numberToEnMap = personMangeList.stream().collect(Collectors.toMap(PersonMange::getNumber, Function.identity(), (k1, k2) -> k2));
            //校验哪些没有和大修组织有关系 并移除已存在的 同组织
            personCodeList = checkPersonRelation(repairOrgId, personCodeList);
            if(CollectionUtils.isEmpty(personCodeList)){
                return numberToEnMap;
            }
        }

        //获取所有没有相应关系的人员的name
        Map<String, BasicUser> numberToEntity = basicUserService.getMapByNumberList(personCodeList);
        List<String> nameList = new ArrayList<>();
        numberToEntity.forEach((k,v)->{
            nameList.add(v.getFullName());
        });
        //添加日志参数
        LogRecordContext.putVariable("nameList", nameList);
        List<String> personCodes = personMangeList.stream().map(PersonMange::getNumber).collect(Collectors.toList());
        Set<String> finalSet = new HashSet<>();
        LambdaQueryWrapperX<PersonMange> noRelationWrapper = new LambdaQueryWrapperX<>(PersonMange.class);
        noRelationWrapper.in(PersonMange::getNumber, personCodeList);
        noRelationWrapper.eq(PersonMange::getBaseCode, baseCode);
//        noRelationWrapper.ne(PersonMange::getStatus, StatusEnum.DRAFT.getIndex());
        noRelationWrapper.select(PersonMange::getId,PersonMange::getNumber);
        List<PersonMange> persons = list(noRelationWrapper);
        for (PersonMange person : persons) {
            finalSet.add(person.getId());
        }
        //移除已存在personManage中的人员
        if(!CollectionUtils.isEmpty(personCodes)){
            personCodeList.removeAll(personCodes);
        }
        //如果移除后还存在code
        List<PersonMange> personManges = new ArrayList<>();
        MajorRepairOrg byId = majorRepairOrgService.getById(repairOrgId);
        LogRecordContext.putVariable("repairOrgName",byId.getName());
        LogRecordContext.putVariable("repairRound",byId.getRepairRound());
        if (!CollectionUtils.isEmpty(personCodeList)) {
            for (Map.Entry<String, BasicUser> stringBasicUserEntry : numberToEntity.entrySet()) {
                BasicUser basicUser = stringBasicUserEntry.getValue();
                PersonMange personMange = new PersonMange();
                //设置是否常驻 如果是常驻则人员出入场时间修改
//                personMange.setIsBasePermanent(false);
                if (!ObjectUtil.isNull(basicUser.getPermanent()) && basicUser.getPermanent().equals(PersonPermanentConstant.PERMANENT_STATUS_YES)) {
                    if (StringUtils.hasText(basicUser.getPermanentBasicCode()) && basicUser.getPermanentBasicCode().equals(baseCode)) {
                        personMange.setIsBasePermanent(true);
                        personMange.setActInDate(byId.getActualBeginTime());
                        personMange.setActOutDate(byId.getActualEndTime());
                    }
                }
                personMange.setId(classRedisHelper.getUUID(PersonMange.class.getSimpleName()));
                personMange.setBaseCode(baseCode);
                personMange.setBaseName(baseName);
                personMange.setNumber(basicUser.getUserCode());
                personMange.setStatus(StatusEnum.DISABLE.getIndex());
                personMange.setCode(personMange.getNumber());
                personMange.setNewcomer(Boolean.FALSE);
                personMange.setName(basicUser.getFullName());
                personMange.setSex(basicUser.getSex());
                personMange.setBasicUserId(basicUser.getId());
                personManges.add(personMange);
                numberToEnMap.put(personMange.getNumber(), personMange);
            }
            if (CollectionUtils.isEmpty(personManges)) {
                throw new BaseException(HttpStatus.BAD_REQUEST.value(), "所选用户未在库中查询到，请刷新缓存或更新基础库");
            }
            boolean save = this.saveBatch(personManges);
        }
        personManges.forEach(item-> finalSet.add(item.getId()));
        List<RelationOrgToPerson> repairOrgPersonList = new ArrayList<>();
        for (String id : finalSet) {
            RelationOrgToPerson repairOrgPerson = new RelationOrgToPerson();
            repairOrgPerson.setPersonId(id);
            repairOrgPerson.setRepairOrgId(repairOrgId);
            repairOrgPersonList.add(repairOrgPerson);
        }
        if (!CollectionUtils.isEmpty(repairOrgPersonList)){
            repairOrgPersonService.saveBatch(repairOrgPersonList);
        }

        return numberToEnMap;
    }

    public List<PersonMange> getPersonMangeList(String baseCode, List<String> codeList) {
        LambdaQueryWrapperX<PersonMange> personMangeLambdaQueryWrapperX = new LambdaQueryWrapperX<>(PersonMange.class);
        personMangeLambdaQueryWrapperX.in(PersonMange::getNumber, codeList);
        if (StringUtils.hasText(baseCode)){
            personMangeLambdaQueryWrapperX.eq(PersonMange::getBaseCode, baseCode);
        }
        personMangeLambdaQueryWrapperX.select(PersonMange::getId,PersonMange::getOutDate,PersonMange::getInDate,PersonMange::getActInDate,PersonMange::getActOutDate
                , PersonMange::getBaseCode, PersonMange::getNumber, PersonMange::getId,PersonMange::getStatus);
        return this.list(personMangeLambdaQueryWrapperX);
    }

    public boolean insertPersonLedger(PersonMange personMange, BasicUser baseUser, SimpleUser simpleUser) {
//        if(null != personMange.getActualLeaveDate() ){
//            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "该人员已经离厂，不能重复离厂");
//        }
        String uniqueId = String.format("%s_%s", personMange.getId(), DateUtil.format(new Date(), DatePattern.SIMPLE_MONTH_FORMAT));
        PersonManageLedger personManageLedger1 = personManageLedgerService.singleByUniqueId(uniqueId);
        if (Objects.isNull(baseUser)) {
            baseUser = new BasicUser();
            baseUser.setNumber(simpleUser.getCode());
            baseUser.setFullName(simpleUser.getName());
            baseUser.setSex(simpleUser.getSex());
            baseUser.setIdCard(simpleUser.getCardNo());
            baseUser.setUserCode(simpleUser.getCode());
            baseUser.setDeptCode(simpleUser.getDeptCode());
            baseUser.setDeptName(simpleUser.getDeptName());
        }
        PersonManageLedger personManageLedger = BeanCopyUtils.convertTo(baseUser, PersonManageLedger::new);
        personManageLedger.setType(PersonManageLedgerTypeEnum.INPUT.getKey());
        personManageLedger.setName(baseUser.getFullName());
        personManageLedger.setSex(baseUser.getSex());
        personManageLedger.setIdCard(baseUser.getIdCard());
        personManageLedger.setPoliticalAffiliation(baseUser.getPoliticalAffiliation());
        personManageLedger.setNature(baseUser.getPersonnelNature());
        personManageLedger.setCompanyName(baseUser.getCompanyName());
        personManageLedger.setDeptName(baseUser.getDeptName());
        personManageLedger.setInstituteName(baseUser.getInstituteName());
        personManageLedger.setNowPosition(baseUser.getNowPosition());
        personManageLedger.setBaseCode(personMange.getBaseCode());
        personManageLedger.setBaseName(personMange.getBaseName());
        personManageLedger.setUniqueId(uniqueId);

        // 记录台账
        personManageLedger.setId(personManageLedger1 == null ? null : personManageLedger1.getId());
        return personManageLedgerService.saveOrUpdate(personManageLedger);
    }

    @Override
    public Boolean leave(LeaveDTO leaveDTO) throws Exception {
        String id = leaveDTO.getId();

        PersonMange entity1 = this.getById(id);
        if (null == entity1) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "数据不存在，请刷新后重试");
        }
        PersonMangeVO entity = BeanCopyUtils.convertTo(entity1, PersonMangeVO::new);
        entity.setLeaveReason(leaveDTO.getLeaveReason());
        entity.setLeaveRemark(leaveDTO.getLeaveRemark());

        String number = entity.getNumber();
        BasicUserVO baseUser = basicUserService.detailUserCode(number);

        PersonManageLedger personManageLedger = BeanCopyUtils.convertTo(entity, PersonManageLedger::new);
        if (null != baseUser) {
            personManageLedger.setName(baseUser.getFullName());
            personManageLedger.setSex(baseUser.getSex());
            personManageLedger.setIdCard(baseUser.getIdCard());
            personManageLedger.setPoliticalAffiliation(baseUser.getPoliticalAffiliation());
            personManageLedger.setInstituteName(baseUser.getInstituteName());
            personManageLedger.setNowPosition(baseUser.getNowPosition());
            personManageLedger.setNature(baseUser.getPersonnelNature());
            personManageLedger.setCompanyName(baseUser.getCompanyName());
            personManageLedger.setDeptName(baseUser.getDeptName());
        }
        personManageLedger.setType(PersonManageLedgerTypeEnum.OUT.getKey());
        personManageLedger.setActOutDate(leaveDTO.getActOutDate());
        if (StringUtils.hasText(entity.getWorkType())) {
            DictValueVO dictValueVO = dictRedisHelper.getDictValueInfoByCode(entity.getWorkType());
            personManageLedger.setWorkTypeName(null == dictValueVO ? "" : dictValueVO.getDescription());
        }
        setEveryName(Collections.singletonList(entity));
        // 记录台账
        Date date = new Date();
        personManageLedger.setCreateTime(date);
        personManageLedger.setModifyTime(date);
        personManageLedger.setPersonManageId(id);
        personManageLedger.setId(null);
        personManageLedger.setIsAgainIn(leaveDTO.getIsAgainIn());
        Boolean isAgainIn = leaveDTO.getIsAgainIn();
        personManageLedger.setLeaveReason(leaveDTO.getLeaveReason());
        if (Objects.nonNull(isAgainIn) && isAgainIn) {
            List<Date> dateList=  leaveDTO.getInAndOutDateList();
            if(CollectionUtils.isEmpty(dateList)){
                throw new BaseException(HttpStatus.BAD_REQUEST.value(), "入场离场时间不能为空");
            }
            // 修改状态为 待入场
            BeanCopyUtils.copyProperties(entity, entity1);
            entity1.setStatus(StatusEnum.DISABLE.getIndex());
            entity1.setInDate(dateList.get(0));
            entity1.setOutDate(dateList.get(1));
//            entity1.setActInDate(null);
//            entity1.setActOutDate(null);
            entity1.setIsAgainIn(true);
            entity1.setIsFinishOutHandover(leaveDTO.getIsFinishOutHandover());
            this.updateById(entity1);
        }else{
            BeanCopyUtils.copyProperties(entity, entity1);
//            entity1.setInDate(null);
//            entity1.setOutDate(null);
//            entity1.setActOutDate(null);
//            entity1.setActInDate(null);
            entity1.setActOutDate(leaveDTO.getActOutDate());
            entity1.setIsAgainIn(isAgainIn);
            entity1.setStatus(StatusEnum.DRAFT.getIndex());
            entity1.setIsFinishOutHandover(leaveDTO.getIsFinishOutHandover());
            this.updateById(entity1);
        }
        personManageLedger.setIsFinishOutHandover(leaveDTO.getIsFinishOutHandover());
        personManageLedgerService.save(personManageLedger);
        // 修改生命周期状态
        List<String> jobIdList =jobPostAuthorizeService.listByPersonId(id);
        if (!CollectionUtils.isEmpty(jobIdList)){
            this.setPersonManageLedger(jobIdList,id,personManageLedger.getId());
            jobNodeStatusService.setNodeStatusByIdList(jobIdList,"personOut");
        }
        LogRecordContext.putVariable("name",entity.getName());
        LogRecordContext.putVariable("number",entity.getNumber());
        LogRecordContext.putVariable("baseCode",entity.getBaseCode());
        return Boolean.TRUE;
    }

    private void setPersonManageLedger(List<String> jobIdList,String personManegeId, String id) {
        if(CollectionUtils.isEmpty(jobIdList)){
            return;
        }
        LambdaUpdateWrapper<JobPostAuthorize> updateWrapper = new LambdaUpdateWrapper<>(JobPostAuthorize.class);
        updateWrapper.in(JobPostAuthorize::getJobId,jobIdList);
//        updateWrapper.eq(JobPostAuthorize::getPersonId,personManegeId);
        updateWrapper.eq(JobPostAuthorize::getPersonManageId,personManegeId);
        updateWrapper.set(JobPostAuthorize::getPersonLedgerId,id);
        jobPostAuthorizeService.update(updateWrapper);
    }
    @Override
    public List<PersonMangeVO> listByPersonCodeList(List<String> userCodeList, String baseCode) {
        if (CollectionUtils.isEmpty(userCodeList)) {
            return new ArrayList<>();
        }
        List<PersonMange> personMangeList = personMangeMapper.queryListByParam(userCodeList, baseCode);
        if (CollectionUtils.isEmpty(personMangeList)) {
            return new ArrayList<>();
        }

        List<PersonMangeVO> personMangeVOList=  BeanCopyUtils.convertListTo(personMangeList, PersonMangeVO::new);
        personMangeVOList.forEach(item->{
            // getOutDate(item.getOutDate())
            item.setOutDays(0);
            List<Date> allDate = new ArrayList<>();
            if(Objects.isNull(item.getInDate()) && Objects.isNull(item.getOutDate()) ){

            }else{
                allDate.add(item.getInDate());
                allDate.add(item.getOutDate());
            }
            if(Objects.equals(item.getStatus(),StatusEnum.DISABLE.getIndex())){
                item.setInDays(getInDate(item.getInDate()));
            }
            item.setInAndOutDateList(allDate);
        });



        return personMangeVOList;
    }

    @Override
    public PersonMangeVO lastInfoByPersonManagetId(String personManageId) {
        LambdaQueryWrapperX<PersonManageLedger> wrapperX = new LambdaQueryWrapperX<>(PersonManageLedger.class);
        wrapperX.eq(PersonManageLedger::getPersonManageId, personManageId);
        wrapperX.orderByDesc(PersonManageLedger::getCreateTime);
        List<PersonManageLedger> personManageLedgerList =personManageLedgerService.list(wrapperX);
        if(CollectionUtils.isEmpty(personManageLedgerList)){
            return null;
        }
        PersonManageLedger personManageLedger = personManageLedgerList.get(0);
        PersonMangeVO personMangeVO =BeanCopyUtils.convertTo(personManageLedger,PersonMangeVO::new);
        return personMangeVO;
    }
    public long getInDate(Date inDate) {
        if(!Objects.isNull(inDate)){
            Calendar startCalendar = Calendar.getInstance();
            Calendar endCalendar = Calendar.getInstance();

            startCalendar.setTime(DateUtil.beginOfDay(inDate));
            endCalendar.setTime(DateUtil.beginOfDay(new Date()));
            if(startCalendar.getTimeInMillis()>endCalendar.getTimeInMillis()){
                return ((startCalendar.getTimeInMillis() - endCalendar.getTimeInMillis()) / (24 * 60 * 60 * 1000));

            }else{
                return ( startCalendar.getTimeInMillis() -endCalendar.getTimeInMillis() ) / (24 * 60 * 60 * 1000);
            }
        }
        return 0;
    }

    public long getOutDate(Date outDate) {
        if(!Objects.isNull(outDate)){
            Calendar startCalendar = Calendar.getInstance();
            Calendar endCalendar = Calendar.getInstance();

            startCalendar.setTime(DateUtil.beginOfDay(new Date()));
            endCalendar.setTime(DateUtil.beginOfDay(outDate));
            if(startCalendar.getTimeInMillis()>endCalendar.getTimeInMillis()){
                return 0;
            }else{
                return (endCalendar.getTimeInMillis() - startCalendar.getTimeInMillis()) / (24 * 60 * 60 * 1000);
            }
        }
        return 0;
    }
    @Override
    public Boolean editDate(InAndOutDTO inAndOutDTO) {
        String personManageId = inAndOutDTO.getId();
        LambdaUpdateWrapper<PersonMange> wrapperX = new LambdaUpdateWrapper<>(PersonMange.class);
        wrapperX.eq(PersonMange::getId, personManageId);
        List<Date>  inAndOutDateList =  inAndOutDTO.getInAndOutDateList();
        wrapperX.set(PersonMange::getInDate, inAndOutDateList.get(0));
        wrapperX.set(PersonMange::getOutDate, inAndOutDateList.get(1));
        wrapperX.set(PersonMange::getModifyTime, new Date());
        wrapperX.set(PersonMange::getModifyId, CurrentUserHelper.getCurrentUserId());
        return this.update(wrapperX);
    }

    @Override
    public Boolean editNewcomer(EditNewcomerDTO editNewcomerDTO) {
        LambdaUpdateWrapper<PersonMange> wrapperX = new LambdaUpdateWrapper<>(PersonMange.class);
        wrapperX.eq(PersonMange::getId, editNewcomerDTO.getId());
        wrapperX.set(PersonMange::getNewcomer, editNewcomerDTO.getIsNewcomer());
        jobPostAuthorizeService.updateJobNewcomer( editNewcomerDTO.getId(),editNewcomerDTO.getIsNewcomer());
        boolean update =this.update(wrapperX);
        return update;
    }

    @Override
    public Boolean editContact(EditContactUserDTO editNewcomerDTO) {
        LambdaUpdateWrapper<PersonMange> wrapperX = new LambdaUpdateWrapper<>(PersonMange.class);
        wrapperX.eq(PersonMange::getId, editNewcomerDTO.getId());
        wrapperX.set(PersonMange::getContactUserName, editNewcomerDTO.getContactUserName());
//        wrapperX.set(PersonMange::getContactUserCode, editNewcomerDTO.getContactUserCode());
        return this.update(wrapperX);
    }

    @Override
    public List<PersonMangeVO> listByPersonIdList(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }
        List<PersonMange> personMangeList = personMangeMapper.queryListByIdList(idList);
        if (CollectionUtils.isEmpty(personMangeList)) {
            return new ArrayList<>();
        }
        List<PersonMangeVO> personMangeVOList=  BeanCopyUtils.convertListTo(personMangeList, PersonMangeVO::new);
        personMangeVOList.forEach(item->{
            // getOutDate(item.getOutDate())
            item.setOutDays(0);
            List<Date> allDate = new ArrayList<>();
            if(Objects.isNull(item.getInDate()) && Objects.isNull(item.getOutDate()) ){

            }else{
                allDate.add(item.getInDate());
                allDate.add(item.getOutDate());
            }
            if(Objects.equals(item.getStatus(),StatusEnum.DISABLE.getIndex())){
                item.setInDays(getInDate(item.getInDate()));
            }
            item.setInAndOutDateList(allDate);
        });
        return personMangeVOList;
    }

    @Override
    public BasePersonCountVO listByBaseCode(String baseCode) {
        LambdaQueryWrapperX<PersonMange>  wrapperX = new LambdaQueryWrapperX<>(PersonMange.class);
        wrapperX.eq(PersonMange::getBaseCode,baseCode);
        wrapperX.in(PersonMange::getStatus,StatusEnum.ENABLE.getIndex());
        wrapperX.isNotNull(PersonMange::getActInDate);
        wrapperX.select(PersonMange::getNumber,PersonMange::getActInDate,PersonMange::getId);
        List<PersonMange> personMangeList =  this.list(wrapperX);
        BasePersonCountVO basePersonCountVO =new BasePersonCountVO();
        if(CollectionUtils.isEmpty(personMangeList)){
           return  basePersonCountVO;
        }
        basePersonCountVO.setPersonTotal(personMangeList.size());
        Date nowDate = new Date();
        long inTotal= personMangeList.stream().filter(item-> Objects.nonNull(item.getActInDate())&&this.isSameDay(item.getActInDate(),nowDate)).count();
        basePersonCountVO.setTodayInTotal(Integer.valueOf(String.valueOf(inTotal)));

        // 人员台账
        basePersonCountVO.setTodayOutTotal(personManageLedgerMapper.outNumberToDayByBaseCode(baseCode));
        /**
         *   基础数据列表
         */
        List<BasicUser> basicUserList= basicUserService.listByNumberList(personMangeList.stream().map(PersonMange::getNumber).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        if(CollectionUtils.isEmpty(basicUserList)){
            basePersonCountVO.setAgeCompareFifthTotal(0);
        }else{
            //
            long fifth =basicUserList.stream().filter(item-> this.getAge(item.getDateOfBirth()) > 50).count();
            basePersonCountVO.setAgeCompareFifthTotal(Integer.valueOf(String.valueOf(fifth)));
        }
        return basePersonCountVO;
    }

    @Override
    public void updateByList(List<PersonPlanVO> voList, List<String> personIdList) {
        List<PersonMange> personMangeList= this.listByIds(personIdList);
        if(CollectionUtils.isEmpty(personMangeList)){
            return ;
        }
        Map<String, PersonPlanVO> idToEntity= voList.stream().collect(Collectors.toMap(PersonPlanVO::getPersonId, Function.identity(),(k1,k2)->k1));
        personMangeList.forEach(item->{
            PersonPlanVO personPlanVO =  idToEntity.get(item.getId());
            item.setOutDate(personPlanVO.getOutDate());
            item.setInDate(personPlanVO.getInDate());
            item.setNewcomer(Objects.equals(personPlanVO.getIsNewcomer(),"是")?true:false);
            item.setContactUserName(personPlanVO.getContactUserName());
        });
        this.updateBatchById(personMangeList);
    }

    /**
     * 大修管理 - 人员入场导入 - excel模板
     *
     * @param query    大修轮次查询
     * @param response 下载到浏览器
     */
    @Override
    public void downloadEntryExcelTpl(SchemeToPersonDTO query, HttpServletResponse response) {
        query.setStateList(List.of(0, 2));
        final List<PersonMangeEntryExcelTpl> list = this.queryPersonMangeEntry(query);

        String fileName = "人员入场导入模板.xlsx";
        response.reset();
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        try {
            EasyExcel.write(response.getOutputStream(), PersonMangeEntryExcelTpl.class)
                    .registerWriteHandler(new ExcelCellSelectWriterHandler(1,
                            CollectionUtils.isEmpty(list) ? 1 : list.size(), 5, 8, new String[]{"是", "否"}))
                    .sheet("人员入场导入模板").doWrite(list);
        } catch (Exception e) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERR.getErrorCode(), e.getMessage());
        }
    }

    /**
     * 大修管理 - 人员入场导入 excel 导入
     *
     * @param excel       excel文件
     * @param repairRound 和大修主表的关联字段
     * @return ImportExcelCheckResultVO
     */
    @Override
    public ImportExcelCheckResultVO importCheckByEntryExcel(MultipartFile excel, String repairRound) {
        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();

        InputStream inputStream = null;
        try {
            inputStream = excel.getInputStream();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        final PersonMangeEntryExcelTplListener excelReadListener = new PersonMangeEntryExcelTplListener();
        EasyExcel.read(inputStream, PersonMangeEntryExcelTpl.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<PersonMangeEntryExcelTpl> dtoS = excelReadListener.getData();

        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (!StringUtils.hasText(repairRound)) {
            result.setCode(400);
            result.setOom("参数错误，请传入大修轮次，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }

        final List<String> codeQuery = Lists.newArrayList();

        final List<ImportExcelErrorNoteVO> err = Lists.newArrayList();
        ImportExcelErrorNoteVO en;
        for (int i = 0; i < dtoS.size(); i++) {
            String order = String.valueOf(i + 2);
            PersonMangeEntryExcelTpl e = dtoS.get(i);
            List<String> notes = Lists.newArrayList();

            if (!StringUtils.hasText(e.getUserCode())) {
                notes.add("第" + order + "员工号不能为空");
            }

            if (!StringUtils.hasText(e.getUserName())) {
                notes.add("第" + order + "用户名称不能为空");
            }

            if (ObjectUtil.isNull(e.getActInDate())) {
                notes.add("第" + order + "实际入场日期不能为空");
            }

            if (!StringUtils.hasText(e.getHeightStr())) {
                notes.add("第" + order + "身高不能为空");
            } else if (!e.getHeightStr().matches("-?\\d+")) {
                notes.add("第" + order + "身高请输入整数如：180");
            }

            if (!StringUtils.hasText(e.getWeightStr())) {
                notes.add("第" + order + "体重不能为空");
            }

            if (ObjectUtil.isNull(e.getChemicalToxinUseJob())) {
                notes.add("第" + order + "化学品/毒品使用接触作业不能为空");
            }

            if (ObjectUtil.isNull(e.getIsJoinYearMajorRepair())) {
                notes.add("第" + order + "一年内参与过集团大修不能为空");
            }

            if (ObjectUtil.isNull(e.getIsHeightMeasurePerson())) {
                notes.add("第" + order + "高剂量人员不能为空");
            }


            if (ObjectUtil.isNull(e.getIsBasePermanent())) {
                notes.add("第" + order + "是否基地常驻不能为空");
            }

            if(!CollectionUtils.isEmpty(notes)){
                en = new ImportExcelErrorNoteVO();
                en.setOrder(order);
                en.setErrorNotes(notes);
                err.add(en);
            }

            codeQuery.add(e.getUserCode());
        }

        // 单号不可重复
        if (codeQuery.stream().distinct().count() != codeQuery.size()) {
            en = new ImportExcelErrorNoteVO();
            en.setOrder("1");
            en.setErrorNotes(List.of("员工号不能重复，请您检查"));
            err.add(en);
        }

        SchemeToPersonDTO query = new SchemeToPersonDTO();
        query.setRepairRound(repairRound);
        query.setUserCodes(codeQuery);
        final List<PersonMangeEntryExcelTpl> list = this.queryPersonMangeEntry(query);

        final Map<String, PersonMangeEntryExcelTpl> manageMap = list.stream().collect(
                Collectors.toMap(PersonMangeEntryExcelTpl::getUserCode, Function.identity(), (k1, k2) -> k1));
        String key;
        for (int i = 0; i < dtoS.size(); i++) {
            String order = String.valueOf(i + 2);
            PersonMangeEntryExcelTpl e = dtoS.get(i);
            key = e.getUserCode();
            if (!manageMap.containsKey(key)) {
                en = new ImportExcelErrorNoteVO();
                en.setOrder(order);
                en.setErrorNotes(List.of("第" + order + "，此大修轮次中，按员工号未找到用户信息"));
                err.add(en);
            } else {
                e.setId(manageMap.get(key).getId());

                final PersonMangeEntryExcelTpl item = manageMap.get(key);

                if (!ObjectUtil.equal(e.getUserName(), item.getUserName())) {
                    en = new ImportExcelErrorNoteVO();
                    en.setOrder(order);
                    en.setErrorNotes(List.of("第" + order + "，按员工号和姓名不匹配"));
                    err.add(en);
                }else if (ObjectUtil.isNull(item.getInDate()) || ObjectUtil.isNull(item.getInDate())) {
                    en = new ImportExcelErrorNoteVO();
                    en.setOrder(order);
                    en.setErrorNotes(List.of("第" + order + "，请完成计划入场、离场时间补充"));
                    err.add(en);
                }
            }
        }
        if (!CollectionUtils.isEmpty(err)) {
            result.setCode(200);
            result.setOom("数据校验失败");
            result.setErr(err);
            return result;
        }

        final String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set(importExcelKey, importId, dtoS, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }

    /**
     * 大修管理 - 人员入场导入 excel 确认导入
     *
     * @param importId 导入校验后，生成的流水号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean importByEntryExcel(String importId) {
        final List<PersonMangeEntryExcelTpl> list = (List<PersonMangeEntryExcelTpl>)
                orionJ2CacheService.get(importExcelKey, importId);

        final Map<String, PersonMangeEntryExcelTpl> pmMap = list.stream().collect(
                Collectors.toMap(PersonMangeEntryExcelTpl::getId, Function.identity(), (k1, k2) -> k1));

        final LambdaQueryWrapperX<PersonMange> pmQuery = new LambdaQueryWrapperX<>(PersonMange.class);
        pmQuery.in(PersonMange::getId, list.stream().map(PersonMangeEntryExcelTpl::getId).collect(Collectors.toList()));
        final List<PersonMange> pmList = this.list(pmQuery);

        for (PersonMange e : pmList) {
            PersonMangeEntryExcelTpl item = pmMap.get(e.getId());
            e.setActInDate(item.getActInDate());
            e.setHeightStr(item.getHeightStr());
            e.setWeightStr(item.getWeightStr());
            e.setChemicalToxinUseJob(item.getChemicalToxinUseJob());
            e.setIsJoinYearMajorRepair(item.getIsJoinYearMajorRepair());
            e.setIsHeightMeasurePerson(item.getIsHeightMeasurePerson());
            e.setIsBasePermanent(item.getIsBasePermanent());
            e.setActOutDate(null);
            e.setLeaveRemark(null);
            e.setLeaveReason(null);
            e.setIsAgainIn(null);
            e.setIsFinishOutHandover(null);
            e.setStatus(1);
        }

        log.info("大修管理 - 人员入场导入 excel 导入的入库数据={}", JSONUtil.toJsonStr(list));
        this.updateBatchById(pmList);

        List<PersonMangeVO> vos = BeanCopyUtils.convertListTo(pmList, PersonMangeVO::new);
        try {
            setEveryName(vos);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        // 插入台账
        this.saveLedgerData(vos);
        return true;
    }

    @Getter
    public static class PersonMangeEntryExcelTplListener extends AnalysisEventListener<PersonMangeEntryExcelTpl> {
        private final List<PersonMangeEntryExcelTpl> data = new ArrayList<>();

        @Override
        public void invoke(PersonMangeEntryExcelTpl dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        }
    }

    /**
     * 大修管理 - 查询 人员入场信息
     *
     * @param query 大修轮次查询
     */
    protected List<PersonMangeEntryExcelTpl> queryPersonMangeEntry(SchemeToPersonDTO query) {
        final LambdaQueryWrapperX<PersonMange> condition = new LambdaQueryWrapperX<>(PersonMange.class);
        condition.select(PersonMange::getId, PersonMange::getActInDate, PersonMange::getHeightStr,
                PersonMange::getWeightStr, PersonMange::getChemicalToxinUseJob, PersonMange::getIsJoinYearMajorRepair,
                PersonMange::getIsHeightMeasurePerson, PersonMange::getIsBasePermanent, PersonMange::getInDate,
                PersonMange::getOutDate);

        condition.rightJoin(SchemeToPerson.class, "sp", on -> on.eq(PersonMange::getId, SchemeToPerson::getPersonId))
                .select(SchemeToPerson::getUserId, SchemeToPerson::getPersonId, SchemeToPerson::getIsHaveProject,
                        SchemeToPerson::getUserCode, SchemeToPerson::getUserName);

        condition.eq(SchemeToPerson::getRepairRound, query.getRepairRound());
        if (!CollectionUtils.isEmpty(query.getUserCodes())) {
            condition.in(SchemeToPerson::getUserCode, query.getUserCodes());
        }
        if (!CollectionUtils.isEmpty(query.getStateList())) {
            condition.in(PersonMange::getStatus, query.getStateList());
        }

        List<PersonMangeEntryExcelTpl> list = this.selectJoinList(PersonMangeEntryExcelTpl.class, condition);

        // 去重逻辑，和page保持统一
        return new ArrayList<>(list.stream()
                .collect(Collectors.toMap(
                        u -> new SchemeToPersonServiceImpl.CompositeKey(u.getUserId(), u.getUserCode(), u.getUserName(), u.getPersonId(), u.getIsHaveProject()),
                        u -> u,
                        (u1, u2) -> u1
                )).values());
    }

    /**
     * 台账信息保存,保存相关表数据
     *
     * @param pmList 人员信息
     */
    protected void saveLedgerData(List<PersonMangeVO> pmList) {
        final List<String> ids = pmList.stream().map(PersonMangeVO::getId).collect(Collectors.toList());

        final List<PersonManageLedger> pmlList = Lists.newArrayList();
        String uniqueId;
        PersonManageLedger pmlItem;

        for (PersonMangeVO e : pmList) {
            uniqueId = String.format("%s_%s_%s", e.getId(), e.getBaseCode(),
                    DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN));
            pmlItem = BeanCopyUtils.convertTo(e, PersonManageLedger::new);
            pmlItem.setId(null);
            pmlItem.setCreateTime(null);
            pmlItem.setCreatorId(null);
            pmlItem.setModifyTime(null);
            pmlItem.setModifyId(null);
            pmlItem.setUniqueId(uniqueId);
            pmlItem.setClassName(null);
            pmlItem.setType(PersonManageLedgerTypeEnum.INPUT.getKey());
            pmlItem.setPersonManageId(e.getId());

            pmlList.add(pmlItem);
        }

        // 插入台账数据
        personManageLedgerService.saveBatch(pmlList);
        final Map<String, PersonManageLedger> pmlMap = pmlList.stream().collect(
                Collectors.toMap(PersonManageLedger::getPersonManageId, Function.identity(), (k1, k2) -> k1));

        if(CollectionUtils.isEmpty(ids)){
            return;
        }
        // 更新作业授权信息，先根据人员信息查找授权表，再更新授权表上的台账id等

        final LambdaQueryWrapperX<JobPostAuthorize> jobPostQuery = new LambdaQueryWrapperX<>(JobPostAuthorize.class);
        jobPostQuery.in(JobPostAuthorize::getPersonManageId, ids);
        final List<JobPostAuthorize> jobPostList = jobPostAuthorizeService.list(jobPostQuery);
        final List<JobPostAuthorize> jobPostUpdate = Lists.newArrayList();
        jobPostList.forEach(e -> {
            PersonManageLedger pml = pmlMap.get(e.getPersonManageId());
            JobPostAuthorize itm = new JobPostAuthorize();
            itm.setId(e.getId());
            itm.setPersonLedgerId(pml.getId());

            jobPostUpdate.add(itm);
        });
        jobPostAuthorizeService.updateBatchById(jobPostUpdate);
        // 同时还需要更新关系表
        final LambdaQueryWrapperX<JobPersonRecord> jobPersonQuery = new LambdaQueryWrapperX<>(JobPersonRecord.class);
        jobPersonQuery.in(JobPersonRecord::getPersonManageId, ids);
        final List<JobPersonRecord> jobPersonList = jobPersonRecordService.list(jobPersonQuery);
        final List<JobPersonRecord> jobPersonInsert = Lists.newArrayList();
        jobPersonList.forEach(e -> {
            PersonManageLedger ledger = pmlMap.get(e.getPersonManageId());
            if (!ObjectUtil.equal(ledger.getNumber(), e.getUserCode())) {
                JobPersonRecord itm = new JobPersonRecord();
                itm.setId(e.getId());
                itm.setPersonManageId(ledger.getPersonManageId());
                itm.setUserCode(ledger.getNumber());

                jobPersonInsert.add(itm);
            }
        });
        if (!CollectionUtils.isEmpty(jobPersonInsert)) {
            jobPersonRecordService.saveBatch(jobPersonInsert);
        }

        // 同时修改生命周期状态
        final List<String> jobIds = jobPostList.stream().map(JobPostAuthorize::getJobId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(jobIds)) {
            final LambdaQueryWrapperX<JobNodeStatus> jobNodeQuery = new LambdaQueryWrapperX<>(JobNodeStatus.class);
            jobNodeQuery.in(JobNodeStatus::getJobId, jobIds);
            jobNodeQuery.select(JobNodeStatus::getJobId, JobNodeStatus::getId, JobNodeStatus::getNodeKeyJson);
            List<JobNodeStatus> jobNodeList = jobNodeStatusService.list(jobNodeQuery);

            List<String> nodeKeyList = List.of("personJoin");

            final List<JobNodeStatus> nodeUpdateList = new ArrayList<>();
            final List<JobNodeStatus> nodeInsertList = new ArrayList<>();
            jobNodeList.forEach(e -> {
                // 移除掉 已经存在的
                jobIds.remove(e.getJobId());
                // 节点状态JSON
                String nodeKeyJson = e.getNodeKeyJson();
                if (StrUtil.isNotBlank(nodeKeyJson)) {
                    List<String> keys = JSONUtil.toList(nodeKeyJson, String.class);
                    if (CollectionUtils.isEmpty(keys)) {
                        keys = new ArrayList<>();
                    }
                    AtomicBoolean b = new AtomicBoolean(false);
                    if (!keys.contains(nodeKeyList.get(0))) {
                        keys.add(nodeKeyList.get(0));
                        b.set(true);
                    }
                    if (b.get()) {
                        e.setNodeKeyJson(JSONUtil.toJsonStr(keys));
                        nodeUpdateList.add(e);
                    }
                }
            });
            for (String jobId : jobIds) {
                JobNodeStatus jobNodeStatus = new JobNodeStatus();
                jobNodeStatus.setJobId(jobId);
                jobNodeStatus.setNodeKeyJson(JSONUtil.toJsonStr(nodeKeyList));
                nodeInsertList.add(jobNodeStatus);
            }

            // 将需要更新的进行更新
            if (!CollectionUtils.isEmpty(nodeUpdateList)) {
                jobNodeStatusService.updateBatchById(nodeUpdateList);
            }
            if (!CollectionUtils.isEmpty(nodeInsertList)) {
                jobNodeStatusService.saveBatch(nodeInsertList);
            }
        }
    }

    private boolean isSameDay(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            throw new IllegalArgumentException("The dates must not be null");
        }
        java.util.Calendar cal1 = java.util.Calendar.getInstance();
        java.util.Calendar cal2 = java.util.Calendar.getInstance();
        cal1.setTime(date1);
        cal2.setTime(date2);
        return cal1.get(java.util.Calendar.YEAR) == cal2.get(java.util.Calendar.YEAR) &&
                cal1.get(java.util.Calendar.DAY_OF_YEAR) == cal2.get(java.util.Calendar.DAY_OF_YEAR);
    }

    public  int getAge(Date birthDate) {
        if(Objects.isNull(birthDate)){
            return 0;
        }

        LocalDate dateOfBirth = birthDate.toInstant() // 转换为Instant
                .atZone(ZoneId.systemDefault())// 转换为ZonedDateTime
                .toLocalDate(); // 获取LocalDate

        // 定义日期格式
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 将字符串转换为LocalDate对象
//        LocalDate dateOfBirth = LocalDate.pa(birthDate, formatter);
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();

        // 计算年龄
        Period period = Period.between(dateOfBirth, currentDate);
        // 返回年龄
        return period.getYears();
    }
}
