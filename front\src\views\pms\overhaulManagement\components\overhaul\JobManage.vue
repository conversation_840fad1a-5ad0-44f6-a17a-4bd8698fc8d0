<script setup lang="ts">
import { Progress } from 'ant-design-vue';
import {
  computed, h, onMounted, onUnmounted, ref, Ref,
} from 'vue';
import { addResizeListener, removeResizeListener } from '/@/utils/event';

const props = defineProps<{
  data: Record<string, any>
}>();

const progressWidth: Ref<number> = ref(120);
const containerRef: Ref = ref();
onMounted(() => {
  addResizeListener(containerRef.value, updateProgressWidth);
});

onUnmounted(() => {
  removeResizeListener(containerRef.value, updateProgressWidth);
});

function updateProgressWidth() {
  if (!containerRef.value) return;
  const domWidth: number = Math.ceil(containerRef.value.offsetWidth);
  if (domWidth < 115) {
    progressWidth.value = 115;
  } else if (domWidth > 150) {
    progressWidth.value = 150;
  } else {
    progressWidth.value = domWidth;
  }
}

function format() {
  const total: number = props.data?.jobRiskCountVO?.jobNum || 0;
  return h('div', { class: 'progress-content' }, {
    default: () => [h('span', { class: 'value' }, total), h('span', { class: 'label' }, '作业总数')],
  });
}

const progressData = computed(() => {
  const total = props.data?.jobRiskCountVO?.jobNum || 0;
  return [
    {
      label: '高风险一级',
      percent: 0,
      value: 0,
      trailColor: '#FFF2F0',
      strokeColor: '#F67C66',
      field: 'heightOneNum',
      width: () => progressWidth.value,
      format: () => '',
    },
    {
      label: '高风险二级',
      percent: 0,
      value: 0,
      trailColor: '#FFF9ED',
      strokeColor: '#FABA4A',
      field: 'heightTwoNum',
      width: () => progressWidth.value * 0.8,
      format: () => '',
    },
    {
      label: '高风险三级',
      percent: 0,
      value: 0,
      trailColor: '#F6F8FF',
      strokeColor: '#A6BAFF',
      field: 'heightThreeNum',
      width: () => progressWidth.value * 0.6,
      format,
    },
  ].map((item) => {
    let percent = 0;
    const value = props.data?.jobRiskCountVO?.[item.field] || 0;
    if (total !== 0) {
      percent = Math.ceil(((props.data?.jobRiskCountVO?.[item.field] || 0) / total) * 100);
    }
    return {
      ...item,
      value,
      percent,
    };
  });
});

const projectData = computed(() => {
  const total = props.data?.importantJobCountVO?.jobNum || 0;
  return [
    {
      label: '未完成',
      field: 'finishedNum',
      percent: Math.ceil((props.data?.importantJobCountVO?.prepRate || 0) * 100),
      trailColor: '#EAEED5',
      strokeColor: '#84BE1F',
      width: () => progressWidth.value,
      format: () => projectFormat(total),
    },
  ];
});

const projectLegend = computed(() => [
  {
    label: '已完成',
    total: 0,
    color: '#84BE1F',
    field: 'finishedNum',
  },
  {
    label: '未完成',
    total: 0,
    color: '#EAEED5',
    field: 'noFinishNum',
  },
].map((item) => ({
  ...item,
  total: props.data?.importantJobCountVO?.[item.field] || 0,
})));

function projectFormat(value: number) {
  return h('div', { class: 'progress-content' }, {
    default: () => [h('span', { class: 'value' }, value), h('span', { class: 'label' }, '重大项目总数')],
  });
}
</script>

<template>
  <div class="job-manage">
    <div class="job">
      <div class="title">
        普通作业 <span>（单位:个）</span>
      </div>
      <div class="content">
        <div
          ref="containerRef"
          class="progress-wrap"
        >
          <Progress
            v-for="(item,index) in progressData"
            :key="index"
            type="circle"
            :percent="item.percent"
            :width="item.width()"
            :strokeColor="item.strokeColor"
            :trailColor="item.trailColor"
            :format="item.format"
          />
        </div>
        <div class="legend-wrap">
          <span>准备完成率：{{ Math.ceil((data?.jobRiskCountVO?.prepRate || 0) * 100) }}%</span>
          <div
            v-for="(item,index) in progressData"
            :key="index"
          >
            <span
              :style="{backgroundColor:item.strokeColor}"
              class="block"
            />
            <span>{{ item.label }} : {{ item.value }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="project pl5">
      <div class="title">
        重大项目 <span>（单位:个）</span>
      </div>
      <div class="content">
        <div
          ref="containerRef"
          class="progress-wrap"
        >
          <Progress
            v-for="(item,index) in projectData"
            :key="index"
            type="circle"
            :percent="item.percent"
            :width="item.width()"
            :strokeColor="item.strokeColor"
            :trailColor="item.trailColor"
            :format="item.format"
          />
        </div>
        <div class="legend-wrap">
          <span>准备完成率：{{ Math.ceil((data?.importantJobCountVO?.prepRate || 0) * 100) }}%</span>
          <div
            v-for="(item,index) in projectLegend"
            :key="index"
          >
            <span
              :style="{backgroundColor:item.color}"
              class="block"
            />
            <span>{{ item.label }} : {{ item.total }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.job-manage {
  box-shadow: 0 0 5px 0 #eee;
  height: 300px;
  padding: 15px 10px;
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));

  > div {
    display: flex;
    flex-direction: column;

    .title {
      font-size: 16px;
      line-height: 1;

      > span {
        font-size: 14px;
        color: #aaa;
      }
    }

    .content {
      display: flex;
      align-items: center;
      margin-top: 20px;
      flex-grow: 1;
      height: 0;

      .progress-wrap {
        position: relative;
        flex-grow: 1;
        width: 0;

        .ant-progress {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }

      .legend-wrap {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        margin-left: 15px;

        > div {
          display: flex;
          align-items: center;
          color: #999;
          margin-top: 10px;
          font-size: 12px;

          .block {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 5px;
          }
        }
      }
    }
  }
}

:deep(.progress-content) {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-items: center;

  .value {
    font-weight: bold;
  }

  .label {
    font-size: 12px;
    margin-top: 6px;
  }
}
</style>
