package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.MaterialOutManageDTO;
import com.chinasie.orion.domain.vo.MaterialOutManageVO;
import com.chinasie.orion.service.MaterialOutManageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * MaterialOutManage 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 17:08:55
 */
@RestController
@RequestMapping("/material-in-out-manage")
@Api(tags = "物资台账管理")
public class  MaterialOutManageController  {

    @Autowired
    private MaterialOutManageService materialOutManageService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看【物资台账管理】详情【{{#materialNumber}}】", type = "MaterialOutManage", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<MaterialOutManageVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        MaterialOutManageVO rsp = materialOutManageService.detail(id,pageCode);
        LogRecordContext.putVariable("materialNumber", rsp.getId());
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param materialOutManageDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【物资台账管理】数据【{{#materialOutManageDTO.number}}】", type = "MaterialOutManage", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody MaterialOutManageDTO materialOutManageDTO) throws Exception {
        String rsp =  materialOutManageService.create(materialOutManageDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param materialOutManageDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【物资台账管理】数据【{{#materialOutManageDTO.number}}】", type = "MaterialOutManage", subType = "编辑", bizNo = "{{#materialOutManageDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  MaterialOutManageDTO materialOutManageDTO) throws Exception {
        Boolean rsp = materialOutManageService.edit(materialOutManageDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【物资台账管理】数据", type = "MaterialOutManage", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = materialOutManageService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【物资台账管理】数据", type = "MaterialOutManage", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = materialOutManageService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【物资台账管理】分页数据", type = "MaterialOutManage", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<MaterialOutManageVO>> pages(@RequestBody Page<MaterialOutManageDTO> pageRequest) throws Exception {
        Page<MaterialOutManageVO> rsp =  materialOutManageService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("物质出库管理导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载【物资台账管理】导入模板", type = "MaterialOutManage", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        materialOutManageService.downloadExcelTpl(response);
    }

    @ApiOperation("物质出库管理导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验【物资台账管理】导入", type = "MaterialOutManage", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = materialOutManageService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("物质出库管理导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认【物资台账管理】导入", type = "MaterialOutManage", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  materialOutManageService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消物质出库管理导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消【物资台账管理】导入", type = "MaterialOutManage", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  materialOutManageService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("物质出库管理导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出【物资台账管理】数据", type = "MaterialOutManage", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        materialOutManageService.exportByExcel(searchConditions, response);
    }
}
