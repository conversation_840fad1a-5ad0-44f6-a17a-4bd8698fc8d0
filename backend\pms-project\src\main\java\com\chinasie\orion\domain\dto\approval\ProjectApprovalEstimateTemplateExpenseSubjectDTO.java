package com.chinasie.orion.domain.dto.approval;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;

import java.lang.Boolean;
import java.lang.String;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProjectApprovalEstimateTemplateExpenseSubject DTO对象
 *
 * <AUTHOR>
 * @since 2024-04-28 17:21:24
 */
@ApiModel(value = "ProjectApprovalEstimateTemplateExpenseSubjectDTO对象", description = "概算模板科目")
@Data
@ExcelIgnoreUnannotated
public class ProjectApprovalEstimateTemplateExpenseSubjectDTO extends ObjectDTO implements Serializable{

    /**
     * 概算模板id
     */
    @ApiModelProperty(value = "概算模板id")
    @ExcelProperty(value = "概算模板id ", index = 0)
    private String estimateTemplateId;

    /**
     * 科目id
     */
    @ApiModelProperty(value = "科目id")
    @ExcelProperty(value = "科目id ", index = 1)
    private String expenseSubjectId;

    /**
     * 是否必填
     */
    @ApiModelProperty(value = "是否必填")
    @ExcelProperty(value = "是否必填 ", index = 2)
    private Boolean required;

    /**
     * 父级id
     */
    @ApiModelProperty(value = "父级id")
    @ExcelProperty(value = "父级id ", index = 3)
    private String parentId;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    @ExcelProperty(value = "编号 ", index = 4)
    private String number;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 公式
     */
    @ApiModelProperty(value = "公式")
    @ExcelProperty(value = "公式 ", index = 5)
    private String formula;

    /**
     * 公式名称
     */
    @ApiModelProperty(value = "公式名称")
    @ExcelProperty(value = "公式名称 ", index = 6)
    private String formulaName;

}
